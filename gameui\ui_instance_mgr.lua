require("game/fubenpanel/fuben_start_down")
UiInstanceMgr = UiInstanceMgr or BaseClass()

SCENE_NOR_CD_TYPE = {
	M_I = 1,				--怪物入侵刷新倒计时
	KF_h = 2,				--跨服修罗塔倒计时
	PK_H = 3,				--跑酷倒计时
	YEZHANWANGCHENG = 4,	--切换阵营倒计时
}

function UiInstanceMgr:__init()
	if UiInstanceMgr.Instance then
		ErrorLog("[UiInstanceMgr] Attempt to create singleton twice!")
		return
	end
	UiInstanceMgr.Instance = self

	self.guai_ji_ui = nil
	self.guai_ji_visible = false

	self.layout_xunlu = nil

	self.leave_scene_countdown_ui = nil

	self.scene_normal_update_ui = nil
	self.scene_normal_update_visible = false
	self.scene_normal_update_type = SCENE_NOR_CD_TYPE.M_I

	self.guild_chat_ui = nil
	self.is_show_guild_ui = false
	-- self.guild_chat_ui_max_btn_x = HandleRenderUnit:GetWidth() + IPHONE_X_SIZE * 2 - 20
	-- self.guild_chat_ui_min_btn_x = HandleRenderUnit:GetWidth() + IPHONE_X_SIZE * 2 - GuildDataConst.GUILD_CHAT_WIDTH - 20

	self.answer_scene_countdown_ui = nil

	-- self.gc_remind_ui = nil
	-- self.gc_remind_num_ui = nil

	self.is_in_play_image_effect = false 			-- 是否播放场景诗句效果
	self.last_image_effect_play_time = 0
	self.image_effect_start_speed = 20

	self.count_down_total_time = 0
	-- Runner.Instance:AddRunObj(self, 4)
	-- GlobalEventSystem:Bind(SceneEventType.SCENE_CHANGE_COMPLETE, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self.fuben_start_down = nil
end

-----------------------------------------------------------------start
function UiInstanceMgr:ShowFBStartDown(seconds,callback)
	if self.fuben_start_down == nil then 
		self.fuben_start_down = FBStartDown.New() 
	end

	self.fuben_start_down:Show(seconds,function()
		if callback then
			callback()
		end
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectZhanChangKaiShi, nil, true))
	end)
end

function UiInstanceMgr:ShowFBStartDown2(seconds,callback)
	if self.fuben_start_down == nil then 
		self.fuben_start_down = FBStartDown.New() 
	end

	self.fuben_start_down:Show2(seconds,function()
		if callback then
			callback()
		end
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectZhanChangKaiShi, nil, true))
	end)
end	

function UiInstanceMgr:ShowFBStartDown3(type,seconds,callback)
	if self.fuben_start_down == nil then 
		self.fuben_start_down = FBStartDown.New() 
	end

	self.fuben_start_down:Show3(type,seconds,function()
		if callback then
			callback()
		end
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectZhanChangKaiShi, nil, true))
	end)
end

-- 幻梦秘境倒计时
function UiInstanceMgr:ShowFBStartDown4(is_boss, seconds, callback)
	if self.fuben_start_down == nil then 
		self.fuben_start_down = FBStartDown.New() 
	end

	self.fuben_start_down:Show4(is_boss, seconds, function()
		if callback then
			callback()
		end
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectZhanChangKaiShi, nil, true))
	end)
end	
------------------------------------------------------------------end

----------------------------New--------------------------------------
function UiInstanceMgr:DoFBStartDown(end_time_stamp, callback, is_check, fix_time, show_type)
	if is_check and end_time_stamp ~= nil and end_time_stamp - TimeWGCtrl.Instance:GetServerTime() <= 0 then
		if callback ~= nil then
			callback()
		end
		return
	end

	if self.fuben_start_down == nil then 
		self.fuben_start_down = FBStartDown.New() 
	end

	self.fuben_start_down:ChangeToStartFightTimer(end_time_stamp, callback, fix_time, show_type)
end
---------------------------------------------------------------------

function UiInstanceMgr:ColseFBStartDown()
	if self.fuben_start_down == nil then 
		return 
	end

	self.fuben_start_down:Close()
	self.fuben_start_down:DeleteMe()
	self.fuben_start_down = nil
end

function UiInstanceMgr:__delete()
	-- Runner.Instance:RemoveRunObj(self)
	-- if nil ~= self.gc_remind_num_ui then
	-- 	self.gc_remind_num_ui:removeFromParent()
	-- 	self.gc_remind_num_ui = nil
	-- end

	-- if nil ~= self.gc_remind_ui then
	-- 	self.gc_remind_ui:removeFromParent()
	-- 	self.gc_remind_ui = nil
	-- end

	if nil ~= self.guild_chat_ui then
		self.guild_chat_ui:removeFromParent()
		self.guild_chat_ui = nil
	end

	if nil ~= self.scene_normal_update_ui then
		self.scene_normal_update_ui:removeFromParent()
		self.scene_normal_update_ui = nil
	end

	if nil ~= self.leave_scene_countdown_ui then
		self.leave_scene_countdown_ui:removeFromParent()
		self.leave_scene_countdown_ui = nil
	end

	-- if nil ~= self.guild_show_window then
	-- 	self.guild_show_window:removeFromParent()
	-- 	self.guild_show_window = nil
	-- end

	if self.guiji_efficiency then
		self.guiji_efficiency:DeleteMe()
		self.guiji_efficiency = nil
	end

	self.guiji_efficiency_bg = nil

	if nil ~= self.guai_ji_ui then
		self.guai_ji_ui:removeFromParent()
		self.guai_ji_ui = nil
	end
	
	if nil ~= self.layout_xunlu then
		self.layout_xunlu:removeFromParent()
		self.layout_xunlu = nil
	end

	if nil ~= self.scene_linshi_show_img then
		self.scene_linshi_show_img:removeFromParent()
		self.scene_linshi_show_img = nil
	end

	if nil ~= self.mammonbless_ui then
		self.mammonbless_ui:removeFromParent()
		self.mammonbless_ui = nil
	end

	if nil ~= self.answer_scene_countdown_ui then
		self.answer_scene_countdown_ui:removeFromParent()
		self.answer_scene_countdown_ui = nil
	end

	if nil ~= self.scene_image_effect then
		self.scene_image_effect:removeFromParent()
		self.scene_image_effect = nil
	end

	if CountDownManager.Instance:HasCountDown("mammonbless_scene_countdown") then
		CountDownManager.Instance:RemoveCountDown("mammonbless_scene_countdown")
	end

	if self.custom_menu then
		self.custom_menu:DeleteMe()
		self.custom_menu = nil
	end

	if self.long_custom_menu then
		self.long_custom_menu:DeleteMe()
		self.long_custom_menu = nil
	end
	self:ColseFBStartDown()
	GlobalTimerQuest:CancelQuest(self.show_tips_delay)
	UiInstanceMgr.Instance = nil
end

function UiInstanceMgr:ShowChongZhiView()
	VipWGCtrl.Instance:OpenTipNoGold()
end

function UiInstanceMgr:OpenTeamMenu(menulabels, point, callback_func, callback_param, red_point_list)
	if nil == self.custom_menu then
		self.custom_menu = CustomMenu.New()
	end
	local menu = menulabels
	-- if not self.custom_menu:IsOpen() then
	-- end
	self.custom_menu:Open()
	self.custom_menu:SetMenuData(menu)
	self.custom_menu:SetRedPointList(red_point_list)
	self.custom_menu:SetPosition(point)
	self.custom_menu:BindCallBack(callback_func, callback_param)
	self.custom_menu:BindCloseCallBack(close_callback)
	self.custom_menu:Flush()
end
function UiInstanceMgr:OpenCustomMenu(menulabels, point, callback_func, callback_param, is_online, off_line_menulabels, close_callback,player_info, alpha_type)
	if nil == self.custom_menu then
		self.custom_menu = CustomMenu.New()
	end
	local menu = {}
	if is_online and off_line_menulabels and is_online == 0 then	
		for i = 1, table.getn(off_line_menulabels) do	
			local is_null = true
			for k, v in pairs(menulabels) do			
				if v == off_line_menulabels[i] then
					menu[k] = v
					is_null = false
				end
			end
			if is_null then 
				menu[#menulabels + 1] = off_line_menulabels[i]  --如果有
			end
		end
	else
		menu = menulabels
	end
	-- if not self.custom_menu:IsOpen() then
	-- end
	self.custom_menu:Open()
	self.custom_menu:SetMaskAlpha(alpha_type)
	self.custom_menu:SetMenuData(menu,player_info)
	self.custom_menu:SetPosition(point)
	self.custom_menu:BindCallBack(callback_func, callback_param)
	self.custom_menu:BindCloseCallBack(close_callback)
	self.custom_menu:Flush()
end

function UiInstanceMgr:OpenMannegeMenu(menulabels, point, callback_func, callback_param, guild_post, close_callback, is_show)
	if nil == self.custom_menu then
		self.custom_menu = CustomMenu.New()
	end
	local menu = {}
	if guild_post then	
		local is_mengzhu = guild_post == 4
		if guild_post == 4 then
			is_mengzhu = true
		end
		local has_power = false
		local fu_mengzhu = false
		if guild_post == 2 or guild_post == 3 or is_mengzhu then
			has_power =  true
		end
		if guild_post == 3 or is_mengzhu then
			fu_mengzhu = true
		end
		for k,v in pairs(menulabels) do
			if v == Language.Guild.ShenQingSheZhi 						--是盟主，显示"申请设置" "解散仙盟"选项
				or v == Language.Guild.JieSanXianMeng or v == Language.Guild.GuildMerge then
				if is_mengzhu then  									
					menu[k] = v
				end
			elseif v == Language.Guild.TuiChuGuild						 --是不是盟主，显示"退出仙盟" "弹劾盟主"选项
				or v == Language.Guild.TanHeMengZhu then                
				if not is_mengzhu then
					menu[k] = v
				end
			elseif v == Language.Guild.ChengYuanZhaoMu 					--是盟主，副盟主，长老
				or v == Language.Guild.QunFaYouJian then
				if has_power then
					menu[k] = v
				end
			elseif v == Language.Guild.ApplyforList then
				if has_power then
					if is_show then
						menu[k] = v .. "{.-}"
					else
						menu[k] = v
					end
				end
			elseif v == Language.Guild.ZhaoLanXianShi then              --是盟主，副盟主
				if fu_mengzhu then
					menu[k] = v
				end
			else
				menu[k] = v
			end
		end
	else
		for k,v in pairs(menulabels) do
			if v == Language.Guild.TuiChuGuild then
				menu[k] = v
			end
		end
	end
	self.custom_menu:ClearFlushParam()
	self.custom_menu:Open()
	self.custom_menu:SetPosition(point)
	self.custom_menu:BindCallBack(callback_func, callback_param)
	self.custom_menu:BindCloseCallBack(close_callback)
	self.custom_menu:Flush(0, nil, menu)
end

function UiInstanceMgr:CloseCustomMenu()
	if self.custom_menu and self.custom_menu:IsOpen() then
		self.custom_menu:Close()
	end
end