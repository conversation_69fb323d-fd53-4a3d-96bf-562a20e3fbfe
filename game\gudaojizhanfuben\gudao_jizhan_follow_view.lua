GuDaoSceneFollowView = GuDaoSceneFollowView or BaseClass(SafeBaseView)


function GuDaoSceneFollowView:__init()
	self:AddViewResource(0, "uis/view/gudao_jizhan_prefab", "layout_gudao_follow_view")
	
	self.view_layer = UiLayer.MainUILow
	self.open_tween = nil
	self.close_tween = nil

end

function GuDaoSceneFollowView:__delete()

end

function GuDaoSceneFollowView:LoadCallBack()
	self.ranklist = AsyncListView.New(GuDaoRankListCell, self.node_list.ranklist)
	self.node_list.btn_states.button:AddClickListener(BindTool.Bind(self.OnClickNodes,self))
	self.obj = self.node_list.panle_bg.gameObject
	self.out_type = nil
end

-- 切换标签调用
function GuDaoSceneFollowView:ShowIndexCallBack()
	self.obj:SetActive(true)
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
	end
end

function GuDaoSceneFollowView:InitCallBack()
	MainuiWGCtrl.Instance:GetTaskMaskRootNode(function ()
		local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
		self.obj.transform:SetParent(parent.gameObject.transform, false)
		MainuiWGCtrl.Instance:SetBtnLevel(true)
	end)
	self:Flush()
end

function GuDaoSceneFollowView:ReleaseCallBack()
	if self.ranklist then
		self.ranklist:DeleteMe()
		self.ranklist = nil
	end
	
	if self.obj then
		ResMgr:Destroy(self.obj)
		self.obj = nil
	end

	if self.activity_countdown then
		GlobalTimerQuest:CancelQuest(self.activity_countdown) 
		self.activity_countdown = nil
	end

	if self.flush_countdown then
		GlobalTimerQuest:CancelQuest(self.flush_countdown) 
		self.flush_countdown = nil
	end

	if self.fb_out_countdown then
		GlobalTimerQuest:CancelQuest(self.fb_out_countdown) 
		self.fb_out_countdown = nil
	end
end



function GuDaoSceneFollowView:OnFlush()
	local next_boss_fresh_time,now_boss_index,damage_rank_list = GuDaoFuBenWGData.Instance:GetBattleHrutRankInfo()
	self.next_boss_fresh_time = next_boss_fresh_time
	self.now_boss_index = now_boss_index + 1
	self.ranklist:SetDataList(damage_rank_list)
	if self.now_boss_index > 5 then 
		self.now_boss_index = 5 
	end

	local my_damage,rank_num = GuDaoFuBenWGData.Instance:GetMyRank()
	if rank_num == -1 then
		self.node_list["my_rank_text"].text.text = Language.GuDaoJiZhan.MyRank2
		self.node_list["my_damage"].text.text = 0
	else
		self.node_list["my_rank_text"].text.text = Language.GuDaoJiZhan.MyRank..rank_num
		self.node_list["my_damage"].text.text = CommonDataManager.ConverExpByThousand(my_damage.hurt_value)
	end
	if self.now_boss_index == -1 then
		if not self.out_type then
			self.out_type = 2 
			self:ShowFBOut(self.next_boss_fresh_time)
		else

		end
	elseif now_boss_index == -3 then
		return
	else
		self:ShowNextWave(self.next_boss_fresh_time,self.now_boss_index)
		self.node_list["cur_wave"].text.text = string.format(Language.GuDaoJiZhan.WaveDesc,self.now_boss_index)
	end
	

	if nil == self.activity_countdown then
		self.node_list["left_time"]:SetActive(true)
		self.node_list["fb_left_time"]:SetActive(false)
		self.activity_countdown = GlobalTimerQuest:AddRunQuest(function ()
			self:CaCularActivityTime()
		end,1)
		self:CaCularActivityTime()
	end

	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	if cur_time < next_boss_fresh_time and self.now_boss_index ~= 1 then
		local all_gather_id = GuDaoFuBenWGData.Instance:GetAllGatherIDList()
		local now_gather_num = GuDaoFuBenWGData.Instance:GetGatherNum()
		if now_gather_num < 3 then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
			local target_obj = Scene.Instance:SelectNearGather(200)
			local scene_logiuc = Scene.Instance:GetSceneLogic()
			GuajiWGCtrl.Instance:ClearAllOperate()
       		MoveCache.SetEndType(MoveEndType.Gather)
       		GuajiWGCtrl.Instance:MoveToObj(target_obj, scene_logiuc:MoveToGatherRange())
 		end
	else
		--GuDaoFuBenWGData.Instance:ClearGatherNum()
		-- GuajiWGCtrl.Instance:ResetMoveCache()
		-- GuajiWGCtrl.Instance:ClearAllOperate()
		-- local main_role = Scene.Instance:GetMainRole()
 	-- 	if main_role then
		-- 	main_role:StopMove()	
		-- end
		--GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function GuDaoSceneFollowView:CaCularActivityTime()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN)
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	if activity_status.next_time - cur_time > 60 then

	elseif activity_status.next_time - cur_time > 0 and not self.out_type or self.out_type == 1 then
		self.out_type = 1
		self:ShowActivityOut(activity_status.next_time - cur_time)
	else
		if self.activity_countdown then
			GlobalTimerQuest:CancelQuest(self.activity_countdown) 
			self.activity_countdown = nil
		end

	end
	self.node_list["left_time"].text.text = Language.GuDaoJiZhan.ActivityCuntDown..TimeUtil.FormatSecond(activity_status.next_time - cur_time, 2)
end

function GuDaoSceneFollowView:ShowNextWave(time,index)
	if not self.out_type then
		self.node_list["bg"]:SetActive(true)
		self.node_list["next_boss_group"]:SetActive(true)
		self.node_list["activity_out_group"]:SetActive(false)
		self.node_list["fb_out_group"]:SetActive(false)
		self.node_list["next_turn_text"].text.text = index

		if self.flush_countdown then
			GlobalTimerQuest:CancelQuest(self.flush_countdown) 
			self.flush_countdown = nil
		end
		self.flush_countdown = GlobalTimerQuest:AddRunQuest(function ()
			self:CacularNextFlushTime(time)
		end,1)
		self:CacularNextFlushTime(time)
	end
end

function GuDaoSceneFollowView:CacularNextFlushTime(time)
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local left_time = time - cur_time
	if left_time < 0 then
		self:ClearAll()
		if self.flush_countdown then
			GlobalTimerQuest:CancelQuest(self.flush_countdown) 
			self.flush_countdown = nil
		end
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self.node_list["next_turn_time"].text.text = math.floor(left_time)
	end
end

function GuDaoSceneFollowView:ShowActivityOut(time) --活动结束
	self.node_list["bg"]:SetActive(true)
	self.node_list["next_boss_group"]:SetActive(false)
	self.node_list["activity_out_group"]:SetActive(true)
	self.node_list["fb_out_group"]:SetActive(false)
	self.node_list["activity_out_text"].text.text = math.floor(time)
end

function GuDaoSceneFollowView:ShowFBOut(time)	--副本结束
	self.node_list["bg"]:SetActive(true)
	self.node_list["next_boss_group"]:SetActive(false)
	self.node_list["activity_out_group"]:SetActive(false)
	self.node_list["fb_out_group"]:SetActive(true)
	self.node_list["fb_out_time"].text.text = time
	self.node_list["fb_left_time"]:SetActive(true)
	self.node_list["left_time"]:SetActive(false)

	if self.fb_out_countdown then
			GlobalTimerQuest:CancelQuest(self.fb_out_countdown) 
			self.fb_out_countdown = nil
		end
	self.fb_out_countdown = GlobalTimerQuest:AddRunQuest(function ()
		self:CacularFBOutTime(time)
	end,1)

	self:CacularFBOutTime(time)
end

function GuDaoSceneFollowView:CacularFBOutTime(time)
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local left_time = time - cur_time
	if left_time <= 0 then
		self:ClearAll()
		if self.fb_out_countdown then
			GlobalTimerQuest:CancelQuest(self.fb_out_countdown) 
			self.fb_out_countdown = nil
		end
		self.node_list["fb_out_time"].text.text = 0
		self.node_list["fb_left_time"].text.text = Language.GuDaoJiZhan.ActivityCuntDown..TimeUtil.FormatSecond(left_time, 2)

		
	else
		self.node_list["fb_out_time"].text.text = math.floor(left_time)
		self.node_list["fb_left_time"].text.text = Language.GuDaoJiZhan.ActivityCuntDown..TimeUtil.FormatSecond(left_time, 2)
	end
end


function GuDaoSceneFollowView:ClearAll()
	if not self.out_type then
		self.node_list["bg"]:SetActive(false)
	end
end

-- 关闭前调用
function GuDaoSceneFollowView:CloseCallBack()
	if self.obj then
		self.obj:SetActive(false)
	end
	self.out_type = nil
end

function GuDaoSceneFollowView:OnClickNodes()
	local role_tip = RuleTip.Instance
	role_tip:SetContent(Language.GuDaoJiZhan.RuleDesc, Language.GuDaoJiZhan.RuleTitle)
end


GuDaoRankListCell = GuDaoRankListCell or BaseClass(BaseRender)

function GuDaoRankListCell:__init()
	
end

function GuDaoRankListCell:OnFlush()
	if not IsEmptyTable(self.data) then
		if self.data.index <=3 then
			self.node_list["rank_img"]:SetActive(true)
			local bundle, asset = ResPath.GetF2CommonIcon("icon_kf_paiming"..self.data.index)
 			self.node_list["rank_img"].image:LoadSpriteAsync(bundle, asset,function ()
				self.node_list["rank_img"].image:SetNativeSize()
			end)
			self.node_list["rank_num"].text.text = ""
		else
			self.node_list["rank_img"]:SetActive(false)
			self.node_list["rank_num"].text.text = self.data.index
		end

		self.node_list["name"].text.text = self.data.role_name	
		self.node_list["damage_num"].text.text = CommonDataManager.ConverExpByThousand(self.data.hurt_value)

	end
end