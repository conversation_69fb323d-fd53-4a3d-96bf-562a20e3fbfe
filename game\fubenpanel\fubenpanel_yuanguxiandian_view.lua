FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

FuBenPanelView.ANGLE_DELTA = 20
FuBenPanelView.DRAG_COUNT = 5

function FuBenPanelView:InitHighTeamEquip()
    self.node_list["btn_add_hte_times"].button:AddClickListener(BindTool.Bind1(self.OnClickAddHteTimes, self))
    self.node_list["layout_combine_equip_mark"].button:AddClickListener(BindTool.Bind1(self.OnClickEquipCombine,self))
    self.node_list["btn_hte_baoming_enter"].button:AddClickListener(BindTool.Bind1(self.OnClickHteBaoMingEnter,self))
    self.hte_reward_list = AsyncListView.New(HteRewardCell, self.node_list["equipfb_reward_list"])
    TeamEquipFbWGData.Instance:RecordHasGetItemList()

    if not self.equipfb_boss_list then
        self.equipfb_boss_list = AsyncListView.New(YGXDFBBossItemCellRender, self.node_list.equipfb_boss_list)
        self.equipfb_boss_list:SetSelectCallBack(BindTool.Bind(self.HTEBossSelectCallBack, self))
        self.equipfb_boss_list:SetEndScrolledCallBack(BindTool.Bind(self.HTEListEndScrolledCallBack, self))
    end

    self.temp_first_open_layer = -1
    self.cur_data = {}
    self.cur_page = 0

    local other_cfg = TeamEquipFbWGData.Instance:GetHighTeamEquipFbOther()
    if other_cfg.fb_des then
        self.node_list.desc_tfdb.text.text = other_cfg.fb_des
    end

    self.node_list.desc_ygxd_ads.text.text = Language.FuBen.YuanGuXianDianAds

    -- self.is_equip_des_expand = false

    -- XUI.AddClickEventListener(self.node_list["btn_equip_show_desc"], BindTool.Bind(self.OnClickEquipShowDescBtn, self))  --描述文本展开
    --self.node_list["btn_hte_pingtai"].button:AddClickListener(BindTool.Bind1(self.OnClickHtePingTai,self))

    -- self.hte_boss_list = AsyncListView.New(HteFbCell, self.node_list["ph_list_equip"])
    -- self.hte_boss_list:SetSelectCallBack(BindTool.Bind1(self.HTEBossSelectCallBack,self))
    -- self.hte_boss_list:SetEndScrolledCallBack(BindTool.Bind(self.HTEListEndScrolledCallBack, self))

    -- self.team_equip_cambered_list = nil
    -- self.boss_index = 1
	-- self.boss_drag_select_index = 1
    -- self:InitCreateCamberedList_TeamEquip()
end


function FuBenPanelView:DeleteTeamEquipView2()
	-- if self.hte_boss_list then
	-- 	self.hte_boss_list:DeleteMe()
	-- 	self.hte_boss_list = nil
	-- end
	if self.hte_reward_list then
		self.hte_reward_list:DeleteMe()
		self.hte_reward_list = nil
	end
	-- if self.alert_window2 then
    --     self.alert_window2:DeleteMe()
    --     self.alert_window2 = nil
    -- end
    if self.hte_cancel_match_alert then
		self.hte_cancel_match_alert:DeleteMe()
		self.hte_cancel_match_alert = nil
	end

    -- if self.team_equip_cambered_list then
	-- 	self.team_equip_cambered_list:DeleteMe()
	-- 	self.team_equip_cambered_list = nil
	-- end

    if self.equipfb_boss_list then
        self.equipfb_boss_list:DeleteMe()
        self.equipfb_boss_list = nil
    end

    self.cur_load_hlj_bg = nil
    self.first_open_equip_fb = nil
    self.first_open_huanlinjing = nil
    self.temp_first_open_layer = nil
    self.cur_data = nil
    self.cur_page = nil
end

-- function FuBenPanelView:InitCreateCamberedList_TeamEquip()
-- 	local cambered_list_data = {
-- 		item_render = HteFbCell,
-- 		asset_bundle = "uis/view/team_equip_fb_prefab",
-- 		asset_name = "ph_render_equip1",

-- 		scroll_list = self.node_list.cambered_list,
-- 		center_x = 820,
-- 		center_y = -230,
-- 		radius_x = 820,
-- 		radius_y = 800,
-- 		angle_delta = Mathf.PI / FuBenPanelView.ANGLE_DELTA,
-- 		origin_rotation = Mathf.PI * 0.402,
-- 		is_drag_horizontal = false,
-- 		is_clockwise_list = false,
-- 		speed = 1,
-- 		arg_speed = 0.2,
-- 		viewport_count = FuBenPanelView.DRAG_COUNT,

-- 		click_item_cb = BindTool.Bind(self.OnClickBossBtn, self),
-- 		drag_to_next_cb = BindTool.Bind(self.OnDragBossToNextCallBack, self),
-- 		drag_to_last_cb = BindTool.Bind(self.OnDragBossToLastCallBack, self),
-- 		on_drag_end_cb = BindTool.Bind(self.OnDragBossLEndCallBack, self),
-- 	}

-- 	self.team_equip_cambered_list = CamberedList.New(cambered_list_data)
-- end

-- function FuBenPanelView:SetBossListSelectCellIndex_TeamEquip(cell_index)
-- 	if self.boss_index == cell_index then
-- 		return
-- 	end

-- 	self.boss_index = cell_index
-- 	self.boss_drag_select_index = cell_index

-- 	local btn_item_list = self.team_equip_cambered_list:GetRenderList()
-- 	for k, item_cell in ipairs(btn_item_list) do
-- 		if cell_index == item_cell:GetIndex() then
-- 			self:OnBossSelectedBtnChange(function()
-- 				self:HTEBossSelectCallBack(item_cell, nil, true)
-- 			end, true)
-- 		end
-- 	end
-- end

-- function FuBenPanelView:OnDragBossToNextCallBack()
-- 	local max_index = self.data_list and #self.data_list or 6
-- 	self.boss_drag_select_index = self.boss_drag_select_index + 1
-- 	self.boss_drag_select_index = self.boss_drag_select_index > max_index and max_index or self.boss_drag_select_index
-- end

-- function FuBenPanelView:OnDragBossToLastCallBack()
-- 	self.boss_drag_select_index = self.boss_drag_select_index - 1
-- 	self.boss_drag_select_index = self.boss_drag_select_index < 1 and 1 or self.boss_drag_select_index
-- end

-- function FuBenPanelView:OnDragBossLEndCallBack()
-- 	self:OnBossSelectedBtnChange(nil, false, self.boss_drag_select_index)
-- end

-- function FuBenPanelView:OnBossSelectedBtnChange(callback, is_click, drag_index)
-- 	if self.team_equip_cambered_list == nil then
-- 		return
-- 	end

-- 	local to_index = drag_index ~= nil and drag_index or self.boss_index or 1
-- 	self.team_equip_cambered_list:ScrollToIndex(to_index, callback, is_click)

-- 	if is_click then
-- 		local item_list = self.team_equip_cambered_list:GetRenderList()
-- 		for k, item_cell in ipairs(item_list) do
-- 			item_cell:SetSelectedHL(to_index)
-- 		end
-- 	end
-- end

-- function FuBenPanelView:OnClickBossBtn(item_cell, force_jump)
-- 	if item_cell == nil then
-- 		return
-- 	end

-- 	local select_index = item_cell:GetIndex()
-- 	local select_data = item_cell:GetData()
-- 	if select_data == nil then
-- 		return
-- 	end

-- 	if (not force_jump and self.boss_index == select_index) then
-- 		return
-- 	end

-- 	self.boss_index = select_index
-- 	self.boss_drag_select_index = select_index

-- 	self:OnBossSelectedBtnChange(function()
-- 		self:HTEBossSelectCallBack(item_cell, nil, true)
-- 	end, true)
-- end

-- function FuBenPanelView:refreshBossList(need_select)
--     if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.fubenpanel_equip_high) then
--         return
--     end

-- 	local default_index = self.boss_index or 1

-- 	local data_list = TeamEquipFbWGData.Instance:GetEquipFbListLimitLevel()
--     self.data_list = data_list

-- 	self.boss_index = default_index
-- 	self.team_equip_cambered_list:CreateCellList(#data_list)
-- 	local btn_item_list = self.team_equip_cambered_list:GetRenderList()
-- 	for k, item_cell in ipairs(btn_item_list) do
-- 		local item_data = self.data_list[k]
-- 		item_cell:SetData(item_data)

-- 		if self.boss_index == item_cell:GetIndex() then
-- 			self:OnBossSelectedBtnChange(function()
-- 				self:HTEBossSelectCallBack(item_cell, nil, true)
-- 			end, true)
-- 		end
-- 	end
-- end

-- function FuBenPanelView:DoTeamEquipFBTweenStart()
--     -- UITween.CleanAllTween(GuideModuleName.FuBenPanel)
--     -- UITween.FakeHideShow(self.node_list.ph_list_equip)
--     -- self.do_team_equip_fb_tween = true
--     -- if not self.first_open_huanlinjing then
--     --     self.first_open_huanlinjing = true
--     -- else
--     --     local data_list = TeamEquipFbWGData.Instance:GetEquipFbListLimitLevel()
--     --     local max_layer = #data_list
--     --     local cur_layer = TeamEquipFbWGData.Instance:GetCurFbIndex()
--     --     local jump_layer = MathClamp(cur_layer, 1, max_layer)
--     -- end
-- end

-- function FuBenPanelView:DoTeamEquipFBListTween()
--     if not self.do_team_equip_fb_tween then
--         return
--     end
--     local tween_info = UITween_CONSTS.FuBen
--     ReDelayCall(self, function()
--         local list = self.hte_boss_list:GetAllItems()
--         local sort_list = {}

--         for i, v in pairs(list) do
--             local data = {}
--             data.index = v:GetIndex()
--             data.item = v
--             sort_list[#sort_list + 1] = data
--         end

--         table.sort(sort_list, SortTools.KeyLowerSorter("index"))

--         local count = 0
--         local cur_index = 0
--         for k,v in ipairs(sort_list) do
--             if 0 ~= v.index then
--                 count = count + 1
--             end
--             v.item:PalyItemAnimator(count)
--         end

--         UITween.FakeToShow(self.node_list.ph_list_equip)
--     end, tween_info.DelayAlphaShowTimeQuick, "team_equip_fb_list_tween")

--     self.do_team_equip_fb_tween = false
-- end

function FuBenPanelView:OnFlushHighTeamEquipView(key, value)
    if "combine_key" == key then
        self:FlushHasEnterCount()
    else
        self:_InternalFlushEquipBossList()
        -- self:refreshBossList()
        self:FlushHasEnterCount()
        self:FlushYuanGuEffect()
        self:FlushYuanGuBtn()
        -- self:FlushYuanGuDesc()
        self:ShowYuanGuShuoMing()
    end
end

function FuBenPanelView:ShowYuanGuShuoMing()
    -- for i = 1, 9 do
    --     if Language.FuBenPanel.HuanLingJingZShuoMing[i] then
    --         self.node_list["equip_info"..i]:SetActive(true)
    --         self.node_list["equip_info"..i].text.text = Language.FuBenPanel.HuanLingJingZShuoMing[i]
    --     else
    --         self.node_list["equip_info"..i]:SetActive(false)
    --     end
    -- end
end

-- function FuBenPanelView:FlushYuanGuDesc()
--     local other_cfg = TeamEquipFbWGData.Instance:GetHighTeamEquipFbOther()
--     if other_cfg.fb_des then
--         self.node_list["team_equip_des"].text.text = other_cfg.fb_des
--     end
-- end

function FuBenPanelView:FlushYuanGuBtn()
    --按钮文本
	local is_match = NewTeamWGData.Instance:GetIsMatching()
    local team_type, fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
    self.node_list.btn_hte_baoming_enter_text.text.text = (is_match and team_type == GoalTeamType.YuanGuXianDian) and Language.NewTeam.IsMatching or Language.NewTeam.AutoMatch
   -- self.node_list.hte_pingtai_effect:SetActive(is_match and team_type == GoalTeamType.YuanGuXianDian)
end

function FuBenPanelView:_InternalFlushEquipBossList()
	if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.fubenpanel_equip_high) then
        return
    end

	local data_list = TeamEquipFbWGData.Instance:GetEquipFbListLimitLevel()
    self.data_list = data_list
    self.equipfb_boss_list:SetDataList(data_list)

    -- self.hte_boss_list:SetDataList(data_list)
    local max_layer = #data_list
    local cur_layer = TeamEquipFbWGData.Instance:GetCurFbIndex()
    local jump_layer = MathClamp(cur_layer, 1, max_layer)

    --从我的队伍进入
    if SocietyWGData.Instance:GetIsInTeam() == 1 then
        local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        if team_type == GoalTeamType.YuanGuXianDian then
            jump_layer = MathClamp(teamfb_mode + 1, 1, max_layer)
        end
    end

    --从平台进入
    local mode = NewTeamWGData.Instance:GetPingTaiJumpYuanGuIndex()
    if mode then
        jump_layer = MathClamp(mode + 1, 1, max_layer)
        NewTeamWGData.Instance:SetPingTaiJumpYuanGuIndex(nil)
    end

    if FunctionGuide.Instance:GetIsGuide() then
        jump_layer = 1
    end

    if not self.first_open_equip_fb then
        self.temp_first_open_layer = jump_layer
        -- self.hte_boss_list:SelectIndex(self.temp_first_open_layer)
        self.equipfb_boss_list:JumpToIndex(self.temp_first_open_layer)
        self.first_open_equip_fb = true
    else
        self.temp_first_open_layer = jump_layer
    end

    -- self:DoTeamEquipFBListTween()
end

function FuBenPanelView:HTEListEndScrolledCallBack()
    if self.temp_first_open_layer and self.temp_first_open_layer ~= -1 then
        -- self.hte_boss_list:SelectIndex(self.temp_first_open_layer)
        self.equipfb_boss_list:JumpToIndex(self.temp_first_open_layer)
        -- local cell = {}
        -- cell.data = {}
        -- local data_list = TeamEquipFbWGData.Instance:GetEquipFbListLimitLevel()
        -- for k , v in pairs(data_list) do
        --     if v.layer == self.temp_first_open_layer -1 then
        --         cell.data = v
        --     end
        -- end
        -- self:HTEBossSelectCallBack(cell)
        self.temp_first_open_layer = -1
    end
end

function FuBenPanelView:HTEBossSelectCallBack(cell, cell_index, is_click)
    if nil == cell then
        return
    end

    self.cur_data = cell.data
    self.cur_page = cell.data.layer
    self:FlushRewardList()
    self:FlushYGCombineMask()

	local role_level = RoleWGData.Instance:GetAttr('level')
    local color = role_level >= self.cur_data.need_role_level and COLOR3B.C8 or COLOR3B.C10
    self.node_list["equip_info1"].text.text = string.format(Language.FuBenPanel.HuanLingJingShuoMing2, color, self.cur_data.need_role_level)

    -- local max_layer = #self.data_list 
    -- local jump_index = cell.data.layer +1
    -- local cell_hight = 300
    -- local space = 7 --间隔
    -- local roll_x = space + cell_hight

    -- local list_width = self.node_list.ph_list_equip.rect.rect.width

    -- local pos_x = -1
    -- if jump_index >= (max_layer - 1) then
    --     pos_x = (max_layer * cell_hight + (max_layer -1) * space - list_width + 5) --listView宽度
    -- elseif jump_index <= 2 then
    --     pos_x = 0
    -- elseif jump_index > 2 then
    --     local max_x = max_layer * roll_x - space
    --     pos_x = roll_x * (jump_index - 2)
    -- end
    -- if max_layer > 2 then
    --     if pos_x ~= -1 and pos_x >= 0 then
    --         GlobalTimerQuest:AddDelayTimer(function ()
    --         local rect_transform = self.node_list["ph_list_equip"].scroll_rect.content:GetComponent(typeof(UnityEngine.RectTransform))
    --         local list_animal = rect_transform:DOAnchorPosX(-pos_x, 0.3)
    --         list_animal:SetEase(DG.Tweening.Ease.OutCubic)
    --         end, 0)
    --     end
    -- end
end

function FuBenPanelView:FlushRewardList()
	local data_list = TeamEquipFbWGData.Instance:GetRewardList(self.cur_page or 0)
	self.hte_reward_list:SetDataList(data_list, 3)
    -- local layer_cfg = TeamEquipFbWGData.Instance:GetBossByLayer(self.cur_page or 0)
    -- if self.node_list["fb_info_text_1"] and layer_cfg then
        
    --     local level_str =  RoleWGData.Instance:TransToDianFengLevelStr(layer_cfg.need_role_level)
    --     self.node_list["fb_info_text_1"].text.text = string.format(Language.FuBenPanel.HuanLingJingShuoMing2, level_str)
    -- end
end

function FuBenPanelView:FlushYGCombineMask()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local pre_show_level = combine_cfg[FB_COMBINE_TYPE.YUANGU + 1].pre_show_level
    self.node_list.team_combine_mark:SetActive(role_level >= pre_show_level)
end

function FuBenPanelView:FlushHasEnterCount()
    if self.node_list["lbl_htefb_enter_times"] then
        local remain_times, total_times = TeamEquipFbWGData.Instance:GetHTEFbTimes()
        remain_times = remain_times < 0 and 0 or remain_times
        self.node_list["lbl_htefb_enter_times"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, remain_times, total_times)
        --if remain_times > 0 then
        --    self.node_list["lbl_htefb_enter_times"].text.text = string.format(Language.FuBenPanel.HTEFbEnterTimes2, remain_times, total_times)
        --else
        --    self.node_list["lbl_htefb_enter_times"].text.text = string.format(Language.FuBenPanel.HTEFbEnterTimesNotEnough2, remain_times, total_times)
        --end
    end

    local hook_type = FuBenWGData.Instance:GetCombineStatus(FB_COMBINE_TYPE.YUANGU)
    self.node_list.layout_combine_equip_mark_hook:SetActive(hook_type == 1)
end

function FuBenPanelView:FlushYuanGuEffect()
    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local vip_buy_cfg = TeamEquipFbWGData.Instance:GetHTEVipCfg()
    local buy_times = TeamEquipFbWGData.Instance:GetHTEFbVipBuyTimes()
    local have_count = vip_buy_cfg["param_" .. role_vip] - buy_times
    local remain_times, total_times = TeamEquipFbWGData.Instance:GetHTEFbTimes()
    self.node_list.yuangu_effect:SetActive(have_count > 0 and remain_times == 0 and FuBenPanelWGData.Instance:CheckVipCondition())
end

function FuBenPanelView:SendFBUseCombineEquip()
    local vas = self.node_list.layout_combine_equip_mark_hook:GetActive()
    if not vas then return end
    --local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
    FuBenWGCtrl.Instance:SendFBUseCombine(1,FB_COMBINE_TYPE.YUANGU)
end

function FuBenPanelView:OnClickAddHteTimes()
    --FuBenPanelWGCtrl.Instance:OpenPetBuy(false, FUBEN_TYPE.HIGH_TEAM_EQUIP)
    --FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.HIGH_TEAM_EQUIP)
end

function FuBenPanelView:OnClickEquipCombine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.YUANGU + 1].level_limit
    if need_level > role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips,level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end


    local remain_times, total_times = TeamEquipFbWGData.Instance:GetHTEFbTimes()
    if remain_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
    local vas = self.node_list.layout_combine_equip_mark_hook:GetActive()

    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine,FB_COMBINE_TYPE.YUANGU)
         --self.node_list.layout_combine_equip_mark_hook:SetActive(not vas)
    else
        local callback_func = function()
            --self.node_list.layout_combine_equip_mark_hook:SetActive(true)
        end
        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.YUANGU,callback_func)
    end
end

--报名进入
function FuBenPanelView:OnClickHteBaoMingEnter()
    if not self.cur_data then
        return
    end

    if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end

    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_active = role_level >= self.cur_data.need_role_level
    if not is_active then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBen.HuanLingJingLevelLimite,self.cur_data.name))
        return
    end
	local team_type = GoalTeamType.YuanGuXianDian
    local data_list = TeamEquipFbWGData.Instance:GetEquipFbListLimitLevel()
	local cur_cfg = self.cur_data--data_list[self.cur_page]
	local fb_mode = cur_cfg.layer
    local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
    local remain_count = total_count - cur_enter_count
    if remain_count == 0 then
       -- SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
       -- self:OnClickAddHteTimes()
        return
    end

    NewTeamWGCtrl.Instance:F2SendTeamFuBenEnter(team_type, fb_mode, 5)

	-- local info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	-- if not info then
	-- 	return
	-- end

	-- local is_match = NewTeamWGData.Instance:GetIsMatching()
	-- local operate = is_match and 1 or 0
	-- --如果没在匹配中，才进行一些操作
	-- if not is_match then
 --         --如果选中的是全部队伍，自动创建无目标
 --        if info.team_type == -1 and info.fb_mode == -1 then
 --            info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(0, 1)
 --        end
 --        local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
 --        local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(info.team_type)
 --        local remain_count = total_count - cur_enter_count

 --        if is_not_in_team then
 --            NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, info.role_max_level)
 --            SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode) --创建完，立即请求队伍列表

 --            if remain_count == 0 then
 --                SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
 --                self:OnClickAddHteTimes()
 --                return
 --            end
 --        end
	-- 	NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
	-- 	NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, info.role_max_level)
	-- 	NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, info.role_max_level)


 --        if remain_count == 0 and 1 == SocietyWGData.Instance:GetIsTeamLeader()  then
 --            SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
 --            self:OnClickAddHteTimes()
 --            return
 --        end
 --        NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
	-- else
	-- 	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
 --        if now_team_type == GoalTeamType.YuanGuXianDian then
 --            --NewTeamWGCtrl.Instance:OpenBaoMingEnterView()
 --            ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
 --        else
 --            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
 --        end
	-- end
end

--组队平台
function FuBenPanelView:OnClickHtePingTai()
    if NewTeamWGData.Instance:GetIsInRoomScene() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.InFbStateTip)
		return
	end
    local data_list = TeamEquipFbWGData.Instance:GetEquipFbList()
	local cur_cfg = self.cur_data--data_list[self.cur_page]
	local fb_mode = cur_cfg.layer
    local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
    local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0
	if is_match and now_team_type == GoalTeamType.YuanGuXianDian then
        if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
            if not self.hte_cancel_match_alert then
                self.hte_cancel_match_alert = Alert.New()
            end
            self.hte_cancel_match_alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
            self.hte_cancel_match_alert:SetOkFunc(function()
                    NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.YuanGuXianDian, 0)
            end)
            self.hte_cancel_match_alert:Open()
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanDo)
        end
	else
        NewTeamWGData.Instance:SetTeamTypeAndMode(GoalTeamType.YuanGuXianDian, fb_mode)
		ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
	end
end

-- function FuBenPanelView:OnClickEquipShowDescBtn()
-- 	local is_expand = not self.is_equip_des_expand
-- 	local height = self.node_list.team_equip_des.rect.sizeDelta.y + 8
-- 	self.node_list.equip_desc_bg.rect.sizeDelta = is_expand and u3dpool.vec2(570, height) or u3dpool.vec2(570, 28)
-- 	self.node_list.img_btn_arrow_equip.rect.rotation = is_expand and Quaternion.Euler(0, 0, 180) or Quaternion.identity
-- 	self.is_equip_des_expand = is_expand
-- end




---------------------HteRewardCell--------------------------------
HteRewardCell = HteRewardCell or BaseClass(BaseRender)
function HteRewardCell:__init()
	self.base_cell = ItemCell.New(self.node_list["pos"])
    self.base_cell:SetIsShowTips(true)
end
function HteRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end
function HteRewardCell:OnFlush()
	self.base_cell:SetData(self.data)
    self.node_list.three_flag:SetActive(self.data.is_three_must_drop and self.data.is_three_must_drop == true)
    -- local new_flag = TeamEquipFbWGData.Instance:GetIsNew(self.data.item_id)
    -- self.node_list.new_flag:SetActive(new_flag == true)
end



---------------------HteFbCell--------------------------------
HteFbCell = HteFbCell or BaseClass(BaseRender)

function HteFbCell:__init(instance,parent)
    self.is_paly_tween = false
end

function HteFbCell:ReleaseCallBack()

end

function HteFbCell:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function HteFbCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_active = role_level >= self.data.need_role_level
	self.node_list["layer_name_yuangu"].text.text = self.data.name

	local level_str = RoleWGData.GetLevelString(self.data.need_role_level)
    local level_color = is_active and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
    level_str = ToColorStr(level_str, level_color)
	local show_level_str = string.format(Language.FuBenPanel.LevelEnough, level_str)
	self.node_list["lbl_level"].text.text = show_level_str


	--灰色背景
    -- XUI.SetGraphicGrey(self.node_list["RawImage_yuangu"], not is_active)
	self.node_list["limit_kuang"]:SetActive(not is_active)
    local data_list = TeamEquipFbWGData.Instance:GetMonsterCfg()
    local head_icon = 0
    for k,v in pairs(data_list) do
		if self.data.layer == v.layer then
            head_icon = v.boss_icon
        end
	end
    local asset_name,bundle_name = ResPath.GetBossIcon("wrod_boss_".. head_icon)
    self.node_list.RawImage_yuangu.image:LoadSprite(asset_name,bundle_name,function ()
        -- self.node_list.RawImage_yuangu.image:SetNativeSize()
    end)
end

function HteFbCell:SetClickCallback(event)
    self.event = event
end

function HteFbCell:OnClick()
	if IsEmptyTable(self.data) then
		return
	end

    if self.event then
        self.event(self)
    end
end

function HteFbCell:OnSelectChange(is_select)
    self.node_list.select_kuang:SetActive(is_select)   --高亮
end

function HteFbCell:SetSelectedHL(index)
	local is_select = self.index == index

	self.node_list.select_kuang:SetActive(is_select)
end

function HteFbCell:PalyItemAnimator(item_index)
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.FuBen.TeamEquipCellTween

	UITween.FakeHideShow(self.node_list["tween_root"])

	ReDelayCall(self, function()
		if self.node_list and self.node_list["tween_root"] then
			UITween.RotateAlphaShow(GuideModuleName.FuBenPanel,self.node_list["tween_root"], tween_info)
		end

	end, tween_info.NextDoDelay * wait_index, "HteFbCell_" .. wait_index)
end

--------------------------------------------YGXDFBBossItemCellRender-----------------------------------------------------------
YGXDFBBossItemCellRender = YGXDFBBossItemCellRender or BaseClass(BaseRender)

function YGXDFBBossItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

    self.node_list.layer_name.text.text = self.data.name
    self.node_list.layer_name_hl.text.text = self.data.name

    local data_list = TeamEquipFbWGData.Instance:GetMonsterCfg()
    local head_icon = 0
    for k,v in pairs(data_list) do
		if self.data.layer == v.layer then
            head_icon = v.boss_icon
        end
	end
    local asset_name,bundle_name = ResPath.GetBossIcon("wrod_boss_".. head_icon)
    self.node_list.icon.image:LoadSprite(asset_name,bundle_name,function ()
        -- self.node_list.icon.image:SetNativeSize()
    end)

    self.node_list.level.text.text = self.data.need_role_level

    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_active = role_level >= self.data.need_role_level
    self.node_list.limit:SetActive(not is_active)
end

function YGXDFBBossItemCellRender:OnSelectChange(is_select)
    self.node_list.bg:CustomSetActive(not is_select)
    self.node_list.bg_select:CustomSetActive(is_select)
end