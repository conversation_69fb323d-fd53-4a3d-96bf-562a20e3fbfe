-- [Deprecated] 已废弃未执行
function TianShenView:RSInitView()
    if not self.rs_bag_grid then
        self.rs_bag_grid = LingHeResloveGrid.New()
        self.rs_bag_grid:SetStartZeroIndex(false)
        self.rs_bag_grid:SetIsMultiSelect(true)
        self.rs_bag_grid:CreateCells({
            col = 4,
            cell_count = 16,
            list_view = self.node_list["rs_bag_grid"],
            itemRender = LingHeResloveBagItem,
            assetBundle = "uis/view/tianshen_linghe_ui_prefab",
            assetName = "linghe_reslove_bag_cell",
            change_cells_num = 1,
        })
        self.rs_bag_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectCB, self))
    end

    if not self.rs_get_list_view then
        self.rs_get_list_view = AsyncListView.New(LingHeResloveGetItem, self.node_list["rs_get_list_view"])
    end

    self.rs_color_select_list = {}
    XUI.AddClickEventListener(self.node_list["rs_quality_toggle_2"], BindTool.Bind(self.OnRSSelectColor, self, {1, 2}))
    XUI.AddClickEventListener(self.node_list["rs_quality_toggle_3"], BindTool.Bind(self.OnRSSelectColor, self, 3))
    XUI.AddClickEventListener(self.node_list["rs_quality_toggle_4"], BindTool.Bind(self.OnRSSelectColor, self, 4))
    XUI.AddClickEventListener(self.node_list["rs_btn_resolve"], BindTool.Bind(self.OnClickReslove, self))

    self.is_rs_first_load = true
    self.node_list["rs_quality_toggle_2"].toggle.isOn = true
end

function TianShenView:RSReleaseCallBack()
    self:CleanResloveTimer()

    self.rs_color_select_list = {}
    self.is_rs_first_load = nil
    self.rs_send_oprate_flag = nil

    if self.rs_bag_grid then
        self.rs_bag_grid:DeleteMe()
        self.rs_bag_grid = nil
    end

    if self.rs_get_list_view then
        self.rs_get_list_view:DeleteMe()
        self.rs_get_list_view = nil
    end
end

function TianShenView:RSShowIndexCallBack()

end

function TianShenView:OnBagSelectCB(cell)
    self:FlushGetItemView()
end

function TianShenView:FlushResloveView()
    -- print_error("---FlushResloveView--")
    local reslove_list = TianShenLingHeWGData.Instance:GetResloveBagList()
    self.node_list["rs_no_item"]:SetActive(#reslove_list == 0)
    self.rs_bag_grid:SetDataList(reslove_list)
    self.rs_bag_grid:SetColorSelcet(self.rs_color_select_list)
    self:FlushGetItemView()
end

function TianShenView:OnRSSelectColor(color, is_on)
    -- print_error("----OnRSSelectColor-----")
    if type(color) == "number" then
        self.rs_color_select_list[color] = is_on
    elseif type(color) == "table" then
        for k,v in pairs(color) do
            self.rs_color_select_list[v] = is_on
        end
    end

    if self.is_rs_first_load then
        self.is_rs_first_load = nil
        return
    end

    if self.rs_bag_grid then
        self.rs_bag_grid:SetColorSelcet(self.rs_color_select_list)
    end

    self:FlushGetItemView()
end

function TianShenView:FlushGetItemView()
    -- print_error("---FlushGetItemView--")
    self.rs_select_list = self.rs_bag_grid:GetAllSelectCell()
    local get_item_list = {}
    local tslh_data = TianShenLingHeWGData.Instance

    local get_id, get_num
    for k,v in pairs(self.rs_select_list) do
        local cfg = tslh_data:GetLingGHeCfgByItemId(v.item_id)
        get_id = cfg and cfg.get_id or 0
        get_num = cfg and cfg.get_num or 0
        if get_id > 0 and get_num > 0 then
            get_item_list[get_id] = get_item_list[get_id] or 0
            get_item_list[get_id] = get_item_list[get_id] + get_num
        end
    end

    local show_list = {}
    for k,v in pairs(get_item_list) do
        table.insert(show_list, {item_id = k, get_num = v})
    end

    if not IsEmptyTable(show_list) then
        SortTools.SortAsc(show_list, "item_id")
    end

    self.node_list["rs_no_get_tips"]:SetActive(#show_list == 0)
    self.rs_get_list_view:SetDataList(show_list)
end

function TianShenView:CleanResloveTimer()
    if self.reslove_cd_timer then
        CountDown.Instance:RemoveCountDown(self.reslove_cd_timer)
        self.reslove_cd_timer = nil
    end
end

-- 分解
function TianShenView:OnClickReslove()
    if IsEmptyTable(self.rs_select_list) then
        return
    end

    if self.reslove_cd_timer or self.rs_send_oprate_flag then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenLingHe.ResloveDoing)
        return
    end

    self.rs_send_oprate_flag = true
    self.node_list["rs_resolve_effect"]:SetActive(true)
    self.reslove_cd_timer = CountDown.Instance:AddCountDown(1.8, 0.2,
        function (elapse_time, total_time)
            if elapse_time > 1.4 and self.rs_send_oprate_flag then
                TianShenLingHeWGCtrl.Instance:SendResloveReq(self.rs_select_list)
                self.rs_send_oprate_flag = nil
            end
        end,
        function()
            if self.node_list["rs_resolve_effect"] then
                self.node_list["rs_resolve_effect"]:SetActive(false)
            end
            self:CleanResloveTimer()
        end
    )
end

---------------------------------------------------------
-- LingHeResloveBagItem
---------------------------------------------------------
LingHeResloveBagItem = LingHeResloveBagItem or BaseClass(BaseRender)
function LingHeResloveBagItem:__init()

end

function LingHeResloveBagItem:LoadCallBack()
    self.lh_item = BaseLingHeCell.New(nil, self.node_list["linghe_node"])
    self.lh_item:SetUseButton(false)
end

function LingHeResloveBagItem:__delete()
    if self.lh_item then
        self.lh_item:DeleteMe()
        self.lh_item = nil
    end
end

function LingHeResloveBagItem:OnFlush()
    if self.data == nil then
        return
    end

    self.lh_item:SetData(self.data)
    self.node_list["name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
    self.node_list["attr_desc"].text.text = ItemShowWGData.Instance:OnlyGetAttrDesc(self.data.item_id, COLOR3B.DEFAULT, COLOR3B.DEFAULT_NUM)
end

function LingHeResloveBagItem:OnSelectChange(is_select)
    self.node_list["normal_bg"]:SetActive(not is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end



---------------------------------------------------------
-- LingHeResloveGetItem
---------------------------------------------------------
LingHeResloveGetItem = LingHeResloveGetItem or BaseClass(BaseRender)
function LingHeResloveGetItem:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["can_get_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
    self.node_list["can_get_num"].text.text = self.data.get_num
end


