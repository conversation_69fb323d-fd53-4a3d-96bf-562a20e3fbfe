-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local BundleCache = require "lib/resmanager/bundle_cache"
local ResUtil = require "lib/resmanager/res_util"
local DownLoadConfig = require "lib/resmanager/download_config"

DownLoadConfig.Init()

-- 是否自动限制下载
AutoControlDownload = false

local UnityAssetBundle = UnityEngine.AssetBundle
local UnityWebRequest = UnityEngine.Networking.UnityWebRequest
local ThreadPriorityHigh = UnityEngine.ThreadPriority.High
local ThreadPriorityLow = UnityEngine.ThreadPriority.Low

local BASE_MAX_DOWNLOADING_BUNDLE_COUNT = 4
local BASE_MAX_HANDLE_STEAL_BUNDLE_COUNT = 2  		-- 同时处理的偷偷下载数量
local MAX_DOWNLOAD_RETRY_COUNT = 10
local MAX_SYNC_WRITING_BUNDLE_COUNT = 5 			-- 一帧使用同步接口写入的文件个数
local MAX_ASYNE_WRITE_BUNDLE_COUNT = 3				-- 使用异步接口写入文件的最大线程数
local BASE_MAX_LOADING_BUNDLE_COUNT = 15
local BASE_MAX_LOADING_ASSET_COUNT = 4
local MAX_SYNC_WRITE_BUNDLE_SIZE = 1024 * 200 		-- 使用同步接口写入文件的大小 200k

local http_get_method = UnityWebRequest.kHttpVerbGET
local zeroInt64 = int64.new(0, 0)
local zeroUInt64 = uint64.zero
local fhInt64 = int64.new(0, 400)
local SysFile = System.IO.File
local _sformat = string.format
local _tinsert = table.insert

local M = ResUtil.create_class()
local DownloadState = {
	state_downloading = 0,
	state_fail = 1,
	state_succ = 2,
}

function M:_init()
	self.v_game_is_stop = false
	self.v_loading_session = 0
	UnityEngine.Application.backgroundLoadingPriority = ThreadPriorityLow
	self.v_background_loading_priority = ThreadPriorityLow
	self.download_delay = nil
	self.load_delay = nil
	self.next_delay_update_download_time = 0
	self.next_delay_update_bundle_load_time = 0
	self.next_delay_update_asset_load_time = 0

	-- 加载优先级列表
	self.load_priority_type_list = {
		ResLoadPriority.sync,
		ResLoadPriority.faster_async_high,
		ResLoadPriority.faster_async_mid,
		ResLoadPriority.faster_async_low,
		ResLoadPriority.high,
		ResLoadPriority.mid,
		ResLoadPriority.low,
		ResLoadPriority.steal
	}

	-- 在剩余的可分配数量里能够分配到的比率，为1时则能分配到全部，只要大于0都会分配到至少1个，为0时则在没有任何加载时会配到1个
	self.load_priority_count_list = {1, 1, 1, 1, 0.9, 0.7, 0.1, 0.1}

	self.v_downloading_bundle_count = 0
	self.v_steal_handle_bundle_count = 0
	self.v_async_writing_bundle_count = 0
	self.v_loading_bundle_count = 0
	self.v_loading_asset_count = 0
	self.v_loading_faster_asset_count = 0

	self.v_need_priority_load_bundles = {}
	self.v_need_load_bundle_infos = {}
	self.v_need_check_bundle_loads = {}
	self.v_download_bundle_records = {}

	-- 重试加载的数量，不恢复
	self.v_download_retry_counts = {}
	self.v_need_priority_sync_write_bundles = {}
	self.v_need_priority_async_write_bundles = {}

	self.v_need_priority_load_assets = {}
	self.v_need_load_asset_infos = {}
	self.v_need_check_asset_loads = {}
	self.v_load_bundle_records = {}

	self.v_need_priority_download_bundles = {}
	self.v_need_download_bundle_infos = {}
	self.v_need_check_download_bundles = {}
	self.v_load_asset_recoreds = {}
	self.v_async_write_handle_list = {}
	self.v_download_handle_list = {}
	self.v_crc_checked_list = {}
	self.bundle_redownload_counts = {}
	self.bundle_damaged_list = {}

	self.v_unloading = false
	self.v_unload_timer_count = 0
	self.v_unload_invalid_time = 0
	-- 是否使用边下载边写方式
	self.v_is_download_write_intime = false

	self.v_last_predownload_timestamp = 0
	self.v_last_predownload_filesize = 0

	self:_ReadBlackList()

	-- 初始化不同优先级下的加载列表
	for i,v in ipairs(self.load_priority_type_list) do
		self.v_need_priority_download_bundles[v] = {}
		self.v_need_priority_load_bundles[v] = {}
		self.v_need_priority_load_assets[v] = {}
		self.v_need_priority_sync_write_bundles[v] = {}
		self.v_need_priority_async_write_bundles[v] = {}
	end

	if self.v_is_download_write_intime then
		DownloadBufferMgr.MinWriteSize = 1 * 1024 * 1024
		DownloadBufferMgr.WriteCD = 0.5
	end
end

function M:Update()
	if self.v_unloading then
		self:_UpdateUnloadState()
		return
	end

	self:_UpdateCheckDownloadingStatus()
	self:_UpdateDownloadBundles()
	self:_UpdatePreDownloadBundles()
	self:_UpdateWriteBundles()

	self:_UpdateCheckBundleLoadStatus()
	self:_UpdateBundleLoads()

	self:_UpdateCheckAssetLoadStatus()
	self:_UpdateAssetLoads()
end

-- 设置下载延迟
function M:SetMaxDownloadDelay(download_delay)
	self.download_delay = download_delay
end

-- 设置加载延迟
function M:SetMaxLoadDelay(load_delay)
	self.load_delay = load_delay
end

function M:ReqHighLoad()
	if self.v_background_loading_priority ~= ThreadPriorityHigh then
		self.v_background_loading_priority = ThreadPriorityHigh
		UnityEngine.Application.backgroundLoadingPriority = ThreadPriorityHigh
	end
end

function M:ReqLowLoad()
	if self.v_background_loading_priority ~= ThreadPriorityLow then
		self.v_background_loading_priority = ThreadPriorityLow
		UnityEngine.Application.backgroundLoadingPriority = ThreadPriorityLow
	end
end

function M:IsLowLoad()
	return self.v_background_loading_priority == ThreadPriorityLow
end

-- 每帧检查正在下载的AB状态
-- 下载指定的AB时,如果有同样的AB正在下载中，则把此次请求加入检查列表。这里对检查列表进行检查
function M:_UpdateCheckDownloadingStatus()
	local succ_num = 0
	local succ_list = ResUtil.GetTable()
	local fail_num = 0
	local fail_list = ResUtil.GetTable()

	for i = #self.v_need_check_download_bundles, 1, -1 do
		local t = self.v_need_check_download_bundles[i]
		local record = self.v_download_bundle_records[t.cache_path]
		if record == DownloadState.state_succ then
			table.remove(self.v_need_check_download_bundles, i)
			succ_num = succ_num + 1
			succ_list[succ_num] = t.session

		elseif record == DownloadState.state_fail then
			table.remove(self.v_need_check_download_bundles, i)
			fail_num = fail_num + 1
			fail_list[fail_num] = t.session
		end
	end

	for k, v in pairs(self.v_download_bundle_records) do
		if DownloadState.state_succ == v or DownloadState.state_fail == v then
			self.v_download_bundle_records[k] = nil
		end
	end

	for i = succ_num, 1, -1 do
		self:_OnDownloadBundleSucc(succ_list[i])
		succ_list[i] = nil
	end

	for i = 1, fail_num do
		self:_OnDownloadBundleFail(fail_list[i])
		fail_list[i] = nil
	end

	ResUtil.ReleaseTable(succ_list)
	ResUtil.ReleaseTable(fail_list)
end

-- 每帧检查开启下载
function M:_UpdateDownloadBundles()
	if nil ~= self.download_delay then
		if GlobalUnityTime < self.next_delay_update_download_time then
			return
		else
			local delay = math.random(0, self.download_delay * 1000) / 1000
			self.next_delay_update_download_time = GlobalUnityTime + delay
		end
	end

	local max_downloading_count = BASE_MAX_DOWNLOADING_BUNDLE_COUNT
	-- 是否开启限速
	if AutoControlDownload then
		max_downloading_count = DownLoadConfig.GetMaxDownloadCount()
	end

	local remain_can_download_count = max_downloading_count - self.v_downloading_bundle_count
	for i, v in ipairs(self.load_priority_type_list) do
		if remain_can_download_count > 0 then
			local allocate_can_download_count = math.ceil(remain_can_download_count * self.load_priority_count_list[i])
			if 0 == allocate_can_download_count and 0 == self.v_downloading_bundle_count then
				allocate_can_download_count = 1
			end

			-- 限制偷下载的数量
			if v == ResLoadPriority.steal then
				local remain_can_steal_download_count = BASE_MAX_HANDLE_STEAL_BUNDLE_COUNT - self.v_steal_handle_bundle_count
				if remain_can_steal_download_count <= 0 then
					remain_can_steal_download_count = 0
				end
				allocate_can_download_count = math.min(allocate_can_download_count, remain_can_steal_download_count)
			end
			remain_can_download_count = remain_can_download_count - self:_UpdateDownloadBundlesInPriority(v, allocate_can_download_count)
		end
	end
end

-- 加载指定优先级下的资源
function M:_UpdateDownloadBundlesInPriority(load_priority, load_count)
	if load_count < 0 then
		print_error("[AssetBundleManager] _UpdateDownloadBundlesInPriority big bug, load_count less 0", load_count)
		load_count = 0
	end

	local queue = self.v_need_priority_download_bundles[load_priority]
	load_count = math.min(load_count, #queue)
	for i=1, load_count do
		local download_t = table.remove(queue, 1)
		self:_InternalDownloadBundle(download_t)
	end

	return load_count
end

local empty_func = function ()

end

-- 每帧检查预下载
function M:_UpdatePreDownloadBundles()
	if nil == PreDownload or nil == PreDownload.Instance then
		return
	end

	if PreDownload.Instance:IsEmpty() then
		return
	end

	-- 只有空闲的时候才开启下载
	if self.v_downloading_bundle_count <= 0 then
		-- 是否开启限速
		if AutoControlDownload then
			local max_download_speed = DownLoadConfig.GetMaxDownloadSpeed()
			local passed_time = math.max(0, GlobalUnityTime - self.v_last_predownload_timestamp)
			if passed_time * max_download_speed > self.v_last_predownload_filesize then
				local bundles = PreDownload.Instance:GetNextBundles(2)
				if nil ~= bundles then
					self:DownLoadBundles(bundles, empty_func, ResLoadPriority.steal)
					self.v_last_predownload_timestamp = GlobalUnityTime
					self.v_last_predownload_filesize = self:GetTotalFileSize(bundles)
				end
			end
		else
			local bundles = PreDownload.Instance:GetNextBundles(2)
			if nil ~= bundles then
				self:DownLoadBundles(bundles, empty_func, ResLoadPriority.steal)
			end
		end
	end
end

-- 每帧检查正在加载的AB包的状态
-- 加载指定的AB时,如果有同样的AB正在加载中，则把此次请求加入检查列表。这里对检查列表进行检查
function M:_UpdateCheckBundleLoadStatus()
	local succ_num = 0
	local succ_list = ResUtil.GetTable()
	local fail_num = 0
	local fail_list = ResUtil.GetTable()

	for i = #self.v_need_check_bundle_loads, 1, -1 do
		local t = self.v_need_check_bundle_loads[i]
		local record = self.v_load_bundle_records[t.bundle_name]
		if true == record then
			table.remove(self.v_need_check_bundle_loads, i)
			if nil ~= BundleCache:GetCacheRes(t.bundle_name) then
				succ_num = succ_num + 1
				succ_list[succ_num] = t.session
			else
				fail_num = fail_num + 1
				fail_list[fail_num] = t.session
				print_error("[AssetBundleManager] _UpdateCheckBundleLoadStatus big bug", t.bundle_name)
			end

		elseif false == record then
			fail_num = fail_num + 1
			fail_list[fail_num] = t.session
			table.remove(self.v_need_check_bundle_loads, i)
		end
	end

	for k,v in pairs(self.v_load_bundle_records) do
		if true == v or false == v then
			self.v_load_bundle_records[k] = nil
		end
	end

	for i = succ_num, 1, -1 do
		self:_OnBundleLoadSucc(succ_list[i])
		succ_list[i] = nil
	end

	for i = 1, fail_num do
		self:_OnBundleLoadFail(fail_list[i])
		fail_list[i] = nil
	end

	ResUtil.ReleaseTable(succ_list)
	ResUtil.ReleaseTable(fail_list)
end

function M:_UpdateWriteBundles()
	local remain_can_sync_write_count = MAX_SYNC_WRITING_BUNDLE_COUNT
	local remain_can_async_write_count = MAX_ASYNE_WRITE_BUNDLE_COUNT - self.v_async_writing_bundle_count

	for i, v in ipairs(self.load_priority_type_list) do
		if remain_can_sync_write_count > 0 then
			local allocate_can_sync_write_count = math.ceil(remain_can_sync_write_count * self.load_priority_count_list[i])
			if allocate_can_sync_write_count == 0 and 0 == self.v_async_writing_bundle_count then
				allocate_can_sync_write_count = 1
			end
			remain_can_sync_write_count = remain_can_sync_write_count - self:_UpdateWriteBundlesSyncInPriority(v, allocate_can_sync_write_count)
		end

		if remain_can_async_write_count > 0 then
			local allocate_can_async_write_count = math.ceil(remain_can_async_write_count * self.load_priority_count_list[i])
			if allocate_can_async_write_count == 0 then
				allocate_can_async_write_count = 1
			end
			remain_can_async_write_count = remain_can_async_write_count - self:_UpdateWriteBundlesAsyncInPriority(v, allocate_can_async_write_count)
		end
	end
end

-- 阻塞式写指定优先级下的文件
function M:_UpdateWriteBundlesSyncInPriority(load_priority, write_count)
	if write_count < 0 then
		print_error("[AssetBundleManager] _UpdateWriteBundlesSyncInPriority big bug, write_count less 0", write_count)
		write_count = 0
	end

	local queue = self.v_need_priority_sync_write_bundles[load_priority]
	write_count = math.min(write_count, #queue)
	for i=1,write_count do
		local download_t = table.remove(queue, 1)
		self:_InternalWriteBundleSync(download_t)
	end

	return write_count
end

-- 流式写指定优先级下的文件
function M:_UpdateWriteBundlesAsyncInPriority(load_priority, write_count)
	if write_count < 0 then
		print_error("[AssetBundleManager] _UpdateWriteBundlesAsyncInPriority big bug, write_count less 0", write_count)
		write_count = 0
	end

	local queue = self.v_need_priority_async_write_bundles[load_priority]
	write_count = math.min(write_count, #queue)
	for i=1,write_count do
		local download_t = table.remove(queue, 1)
		self:_InternalWriteBundleAsync(download_t)
	end

	return write_count
end

-- 每帧检查开启加载
function M:_UpdateBundleLoads()
	if nil ~= self.load_delay then
		if GlobalUnityTime < self.next_delay_update_bundle_load_time then
			return
		else
			local delay = math.random(0, self.load_delay * 1000) / 1000
			self.next_delay_update_bundle_load_time = GlobalUnityTime + delay
		end
	end

	local max_loading_count = BASE_MAX_LOADING_BUNDLE_COUNT
	local remain_can_load_count =  max_loading_count - self.v_loading_bundle_count
	for i, v in ipairs(self.load_priority_type_list) do
		if remain_can_load_count > 0 then
			local allocate_can_load_count = math.ceil(remain_can_load_count * self.load_priority_count_list[i])
			if allocate_can_load_count == 0 and 0 == self.v_loading_bundle_count then
				allocate_can_load_count = 1
			end
			remain_can_load_count = remain_can_load_count - self:_UpdateBundleLoadsInPriority(v, allocate_can_load_count)
		end
	end
end

-- 加载指定优先级下的资源
function M:_UpdateBundleLoadsInPriority(load_priority, load_count)
	if load_count < 0 then
		print_error("[AssetBundleManager] _UpdateBundleLoadsInPriority big bug, load_count less 0", load_count)
		load_count = 0
	end

	local queue = self.v_need_priority_load_bundles[load_priority]
	load_count = math.min(load_count, #queue)
	for i=1,load_count do
		local load_t = table.remove(queue, 1)
		self:_InternalLoadBundleAsync(load_t.bundle_file_path, load_t.bundle_name, load_t.bundle_hash, load_t.session)
		self:_ReleaseLoadTable(load_t)
	end

	return load_count
end

-- 每帧检查正在加载的Asset状态
-- 加载指定的Asset时,如果有同样的Asset正在加载中，则把此次请求加入检查列表。这里对检查列表进行检查
function M:_UpdateCheckAssetLoadStatus()
	local succ_num = 0
	local succ_list = ResUtil.GetTable()
	local fail_num = 0
	local fail_list = ResUtil.GetTable()

	for i = #self.v_need_check_asset_loads, 1, -1 do
		local t = self.v_need_check_asset_loads[i]
		local record = self.v_load_asset_recoreds[t.asset_full_path]
		if true == record then
			local asset = ResPoolMgr:ScanRes(t.bundle_name, t.asset_name)
			table.remove(self.v_need_check_asset_loads, i)
			if nil ~= asset then
				succ_num = succ_num + 1
				succ_list[succ_num] = {t.session, asset}
			else
				print_error("[AssetBundleManager] _UpdateCheckAssetLoadStatus big bug", t.bundle_name, t.asset_name)
				fail_num = fail_num + 1
				fail_list[fail_num] = t.session
			end
		elseif false == record then
			fail_num = fail_num + 1
			fail_list[fail_num] = t.session
			table.remove(self.v_need_check_asset_loads, i)
		end
	end

	for k,v in pairs(self.v_load_asset_recoreds) do
		if true == v or false == v then
			self.v_load_asset_recoreds[k] = nil
		end
	end

	for i = succ_num, 1, -1 do
		local t = succ_list[i]
		self:_OnAssetLoaded(t[1], t[2])
		succ_list[i] = nil
	end

	for i = 1, fail_num do
		self:_OnAssetLoaded(fail_list[i], nil)
		fail_list[i] = nil
	end

	ResUtil.ReleaseTable(succ_list)
	ResUtil.ReleaseTable(fail_list)
end

-- 每帧检查开启从AB包加载Asset，控制加载总量和允许慢点加载的数量
function M:_UpdateAssetLoads()
	if nil ~= self.load_delay then
		if GlobalUnityTime < self.next_delay_update_asset_load_time then
			return
		else
			local delay = math.random(0, self.load_delay * 1000) / 1000
			self.next_delay_update_asset_load_time = GlobalUnityTime + delay
		end
	end

	-- faster加载最大数另外控制
	local max_loading_count = self:IsLowLoad() and BASE_MAX_LOADING_ASSET_COUNT or BASE_MAX_LOADING_ASSET_COUNT * 3
	local remain_can_load_count = max_loading_count - self.v_loading_faster_asset_count
	for i, v in ipairs(self.load_priority_type_list) do
		if v >= ResLoadPriority.faster_async_low and v <= ResLoadPriority.faster_async_high and remain_can_load_count > 0 then
			local allocate_can_load_count = math.ceil(remain_can_load_count * self.load_priority_count_list[i])
			if allocate_can_load_count == 0 and 0 == self.v_loading_faster_asset_count then
				allocate_can_load_count = 1
			end
			remain_can_load_count = remain_can_load_count - self:_UpdateAssetLoadsInPriority(v, allocate_can_load_count)
		end
	end

	local max_loading_count2 = self:IsLowLoad() and BASE_MAX_LOADING_ASSET_COUNT or BASE_MAX_LOADING_ASSET_COUNT * 2
	local remain_can_load_count2 = max_loading_count2 - self.v_loading_asset_count
	for i, v in ipairs(self.load_priority_type_list) do
		if remain_can_load_count2 > 0 then
			local allocate_can_load_count = math.ceil(remain_can_load_count2 * self.load_priority_count_list[i])
			if allocate_can_load_count == 0 and 0 == self.v_loading_asset_count then
				allocate_can_load_count = 1
			end
			remain_can_load_count2 = remain_can_load_count2 - self:_UpdateAssetLoadsInPriority(v, allocate_can_load_count)
		end
	end
end

-- 加载指定优先级下的asset
function M:_UpdateAssetLoadsInPriority(load_priority, load_count)
	if load_count < 0 then
		print_error("[AssetBundleManager] _UpdateAssetLoadsInPriority big bug, load_count less 0", load_count)
		load_count = 0
	end

	local queue = self.v_need_priority_load_assets[load_priority]
	load_count = math.min(load_count, #queue)
	for i=1,load_count do
		local load_t = table.remove(queue, 1)
		self:_InternalLoadAssetAsync(load_t.bundle_name, load_t.asset_name, load_t.session, load_t.asset_type, load_priority)
	end

	return load_count
end

local download_t_list = {}
local download_t_index = 0
local function DownloadWriteInlineCallBack(succ, p_download_t_index)
	local download_t = download_t_list[p_download_t_index]
	download_t_list[p_download_t_index] = nil
	if not download_t then
		return
	end

	local self = download_t.self

	self.v_downloading_bundle_count = self.v_downloading_bundle_count - 1
	if download_t.load_priority == ResLoadPriority.steal then
		self.v_steal_handle_bundle_count = self.v_steal_handle_bundle_count - 1
	end

	if succ then
		self.v_download_retry_counts[download_t.cache_path] = nil
		self.v_download_bundle_records[download_t.cache_path] = DownloadState.state_succ

		ResUtil.black_list[download_t.bundle_name] = nil
		self:_WriteBlackList()

		self:_OnDownloadBundleSucc(download_t.session)
		self:_ReleaseLoadTable(download_t)

		if self.v_downloading_bundle_count <= 0 then
			self:_TryDestoryBuffers()
		end
	else
		self:_OnDownloadError(download_t)
	end
end

local function DownloadBundleCallBack(download_t)
	if ResUtil.log_debug then
		ResUtil.Log("[AssetBundleManager] start download ", download_t.remote_path)
	end

	local www = download_t.downloading_www:SendWebRequest()
	coroutine.www(www)

	if ResUtil.log_debug then
		ResUtil.Log("[AssetBundleManager] download complete ", download_t.remote_path)
	end

	local self = download_t.self
	local downloading_www = download_t.downloading_www

	if self.v_game_is_stop then
		self:_DestroyDownload(download_t)
		return
	end


	if not self.v_is_download_write_intime then
		self.v_downloading_bundle_count = self.v_downloading_bundle_count - 1
	end

	local error_msg
	local net_error = downloading_www.result == UnityWebRequest.Result.ConnectionError or downloading_www.result == UnityWebRequest.Result.DataProcessingError
	local http_error = downloading_www.result == UnityWebRequest.Result.ProtocolError
	if net_error then
		error_msg = _sformat("[AssetBundleManager]download load fail, network error: %s %s", download_t.remote_path, downloading_www.error)
	elseif http_error then
		error_msg = _sformat("[AssetBundleManager]download load fail, http error: %s", download_t.remote_path)
	elseif downloading_www.responseCode < zeroInt64 or downloading_www.responseCode >= fhInt64 then
		error_msg = _sformat("[AssetBundleManager]download load fail, code error: %s %s", download_t.remote_path, downloading_www.responseCode)
	elseif downloading_www.downloadedBytes <= zeroUInt64 then
		error_msg = _sformat("[AssetBundleManager]download load fail, bytes error: %s %s", download_t.remote_path, downloading_www.downloadedBytes)
	end

	if error_msg then
		-- 大于一定次数才上报，以免buggly上太多没用日志
		if self.v_download_retry_counts[download_t.cache_path] and self.v_download_retry_counts[download_t.cache_path] >= MAX_DOWNLOAD_RETRY_COUNT / 2 then
			print_error("[AssetBundleManager] download fail ", download_t.remote_path, self.v_download_retry_counts[download_t.cache_path], error_msg)
		end
		self:_DestroyDownload(download_t)

		-- 如果是边下边写，在 DownloadWriteInlineCallBack 里面处理失败的情况
		if not self.v_is_download_write_intime then
			self:_OnDownloadError(download_t)
		end

	elseif self.v_is_download_write_intime then
		-- 如果是边下边写，这里只需要清理掉download_t, DownloadWriteInlineCallBack 才是写入成功的回调
		self:_DestroyDownload(download_t)
	else
		if download_t.load_priority >= ResLoadPriority.faster_async_low and download_t.load_priority <= ResLoadPriority.sync then
			self:_InternalWriteBundleSync(download_t)
		else
			local size = ResMgr:GetBundleSize(download_t.bundle_name) or 0
			if size < MAX_SYNC_WRITE_BUNDLE_SIZE then
				_tinsert(self.v_need_priority_sync_write_bundles[download_t.load_priority], download_t)
			else
				_tinsert(self.v_need_priority_async_write_bundles[download_t.load_priority], download_t)
			end
		end
	end
end

-- 下载指定的AB,如果有同样的AB正在下载中，则把此次请求加入检查列表。每帧检查该AB的下载状态。
function M:_InternalDownloadBundle(download_t)
	local bundle_name = download_t.bundle_name
	local bundle_hash = ResMgr:GetBundleHash(bundle_name)

	if ResUtil.IsFileExist(bundle_name, bundle_hash) then
		self:_OnDownloadBundleSucc(download_t.session)
		self:_ReleaseLoadTable(download_t)
		return
	end

	if nil ~= self.v_download_bundle_records[download_t.cache_path] then
		local t = {cache_path = download_t.cache_path, session = download_t.session}
		_tinsert(self.v_need_check_download_bundles, t)
		self:_ReleaseLoadTable(download_t)
		return
	end

	local redownload_count = self.bundle_redownload_counts[bundle_name] or 0
	if nil == self.bundle_redownload_counts[bundle_name] then
		self.bundle_redownload_counts[bundle_name] = 1
	else
		self.bundle_redownload_counts[bundle_name] = redownload_count + 1
	end

	self.v_download_bundle_records[download_t.cache_path] = DownloadState.state_downloading
	download_t.self = self
	download_t.downloading_www = nil
	download_t.download_handler = nil

	-- 如果bundle是因为损坏而重新下载的，直接切换到url2
	local is_damaged_bundle = self.bundle_damaged_list[bundle_name]
	if is_damaged_bundle then
		self.bundle_damaged_list[bundle_name] = nil
	end

	download_t.remote_path = ResMgr:GetRemotePath(bundle_name, bundle_hash, redownload_count, is_damaged_bundle)

	if self.v_is_download_write_intime then
		ResUtil.black_list[bundle_name] = true
		self:_WriteBlackList()
		download_t_index = download_t_index + 1
		download_t_list[download_t_index] = download_t
		-- 注意！！！
		-- DownloadWriteInlineCallBack 才是真正的写入完成的回调
		-- DownloadBundleCallBack 是下载完成的回调
		-- 正常情况下, DownloadWriteInlineCallBack 会晚于 DownloadBundleCallBack(但是有些异常情况可能会导致时序改变)
		download_t.download_handler = DownloadHandlerBundle.New(download_t.cache_path, ResMgr:GetBundleSize(bundle_name),
			DownloadWriteInlineCallBack, download_t_index, 8192)

		download_t.downloading_www = UnityWebRequest.New(download_t.remote_path, http_get_method, download_t.download_handler, nil)
		self.v_download_handle_list[download_t.download_handler] = download_t.download_handler

		if download_t.load_priority >= ResLoadPriority.sync then
			download_t.download_handler:SetPriority(0)
		elseif download_t.load_priority >= ResLoadPriority.faster_async_mid then
			download_t.download_handler:SetPriority(3)
		elseif download_t.load_priority >= ResLoadPriority.high then
			download_t.download_handler:SetPriority(2)
		end
	else
		download_t.downloading_www = UnityWebRequest.Get(download_t.remote_path)
	end

	self.v_downloading_bundle_count = self.v_downloading_bundle_count + 1
	if download_t.load_priority == ResLoadPriority.steal then
		self.v_steal_handle_bundle_count = self.v_steal_handle_bundle_count + 1
	end
	coroutine.start(DownloadBundleCallBack, download_t)
end

function M:_OnDownloadError(download_t)
	local cache_path = download_t.cache_path
	self.v_download_retry_counts[cache_path] = self.v_download_retry_counts[cache_path] or 0
	if self.v_download_retry_counts[cache_path] <= MAX_DOWNLOAD_RETRY_COUNT or ResMgr:GetIsIgnoreHashCheck() then
		self.v_download_bundle_records[cache_path] = nil
		self.v_download_retry_counts[cache_path] = self.v_download_retry_counts[cache_path] + 1
		_tinsert(self.v_need_priority_download_bundles[download_t.load_priority], download_t)
	else
		self.v_download_retry_counts[cache_path] = nil
		self.v_download_bundle_records[cache_path] = DownloadState.state_fail
		self:_OnDownloadBundleFail(download_t.session)
		self:_ReleaseLoadTable(download_t)
	end
end

-- 把下载好的AssetBundle写文件到本地(同步)
function M:_InternalWriteBundleSync(download_t)
	local cache_path = download_t.cache_path
	local session = download_t.session

	local is_succ = RuntimeAssetHelper.TryWriteWebRequestData(cache_path, download_t.downloading_www)
	self:_DestroyDownload(download_t)

	if download_t.load_priority == ResLoadPriority.steal then
		self.v_steal_handle_bundle_count = self.v_steal_handle_bundle_count - 1
	end
	self.v_download_retry_counts[cache_path] = nil
	self.v_download_bundle_records[cache_path] = DownloadState.state_succ

	if is_succ then
		self:_OnDownloadBundleSucc(session)
		self:_ReleaseLoadTable(download_t)
	else
		-- 如果写文件异常则忽略本地错当作下载失败处理
		ResUtil.RemoveFile(download_t.bundle_name, cache_path)
		self:_OnDownloadError(download_t)
	end
end

-- 把下载好的AssetBundle写文件到本地(异步)
-- 异步写入文件可能存在写入一半的时候进程被关闭，所以在写入文件之前需要把这个文件加入black_list
-- black_list里面的文件意味着是不完整的文件，这个时候禁止使用
function M:_InternalWriteBundleAsync(download_t)
	local cache_path = download_t.cache_path
	local session = download_t.session
	self.v_async_writing_bundle_count = self.v_async_writing_bundle_count + 1
	-- 加入black_list
	ResUtil.black_list[download_t.bundle_name] = true
	self:_WriteBlackList()

	local handle = nil
	local is_need_cache_async_handle = true

	handle = RuntimeAssetHelper.TryWriteWebRequestDataByStream(cache_path, download_t.downloading_www, function (is_succ)
		is_need_cache_async_handle = false
		if nil ~= handle then
			self.v_async_write_handle_list[handle] = nil
		end

		self:_DestroyDownload(download_t)
		if download_t.load_priority == ResLoadPriority.steal then
			self.v_steal_handle_bundle_count = self.v_steal_handle_bundle_count - 1
		end
		self.v_download_retry_counts[cache_path] = nil
		self.v_download_bundle_records[cache_path] = DownloadState.state_succ
		self.v_async_writing_bundle_count = self.v_async_writing_bundle_count - 1

		-- 移除black_list
		ResUtil.black_list[download_t.bundle_name] = nil

		if is_succ then
			self:_WriteBlackList()
			self:_OnDownloadBundleSucc(session)
			self:_ReleaseLoadTable(download_t)
		else
			-- 如果写文件异常则忽略本地错当作下载失败处理
			ResUtil.RemoveFile(download_t.bundle_name, cache_path)
			self:_WriteBlackList()
			self:_OnDownloadError(download_t)
		end
	end, MAX_SYNC_WRITE_BUNDLE_SIZE)

	if is_need_cache_async_handle and nil ~= handle then
		self.v_async_write_handle_list[handle] = true
	end
end

-- 把BlackList写入到本地
function M:_WriteBlackList()
	local file_path, error_info = ResUtil.GetCachePath("assetbundle_black_list.txt")
	local file = io.open(file_path, "w")
	if nil == file then
		print_error("[AssetBundleManager] BigBug! io error, file is nil!!!", file_path, error_info or "error")
		return
	end

	local tbl = {}
	for k,v in pairs(ResUtil.black_list) do
		table.insert(tbl, k)
		table.insert(tbl, "\n")
	end
	local content = table.concat(tbl)
	file:write(content)
	file:close()
end

-- 读取BlackList
function M:_ReadBlackList()
	local file_path = ResUtil.GetCachePath("assetbundle_black_list.txt")
	local file = io.open(file_path, "r")
	if file then
		for bundle_name in file:lines() do
			-- 这些文件可能是损坏的，需要删除
			ResUtil.DelCacheBundle(bundle_name)
		end
		file:close()
		os.remove(file_path)
	end
end

local function LoadBundleAsyncCallBack(data)
	local self = data.self
	local loading_bundle = data.loading_bundle
	local bundle_file_path = data.bundle_file_path
	local bundle_name = data.bundle_name
	local session = data.session
	self:_ReleaseLoadTable(data)

	coroutine.www(loading_bundle)
	self.v_loading_bundle_count = self.v_loading_bundle_count - 1

	if self.v_game_is_stop then
		if nil ~= loading_bundle.assetBundle then
			loading_bundle.assetBundle:Unload(true)
		end

		if self.v_loading_bundle_count <= 0 and nil ~= self.wait_assetbundle_unload_callback then
			self.wait_assetbundle_unload_callback()
			self.wait_assetbundle_unload_callback = nil
		end
		return
	end

	if nil == loading_bundle.assetBundle then
		local cal_md5 = 0
		if SysFile.Exists(bundle_file_path) then
			cal_md5 = MD5.GetMD5FromFile(bundle_file_path)
		end

		print_error("[AssetBundleManager] async load bundle fail, bundle is nil ", bundle_file_path, bundle_name, cal_md5)

		-- 来到这里说明bundle本地文件已损坏，重新启动下载
		ResUtil.RemoveFile(bundle_name, bundle_file_path)
		self.bundle_damaged_list[bundle_name] = true

		self:DownLoadBundles({bundle_name}, function (is_succ)
			print_error("[AssetBundleManager] bigbug! load assetbundle fail, restart download callback", bundle_name, is_succ)
		end, ResLoadPriority.low)

		self.v_load_bundle_records[bundle_name] = false
		self:_OnBundleLoadFail(session)
		return
	end

	self.v_load_bundle_records[bundle_name] = true
	BundleCache:CacheRes(bundle_name, loading_bundle.assetBundle)
	self:_OnBundleLoadSucc(session)
end

-- 加载指定的AB,如果有同样的AB正在加载中，则把此次请求加入检查列表。每帧检查该AB的加载状态。
function M:_InternalLoadBundleAsync(bundle_file_path, bundle_name, bundle_hash, session)
	if self.v_game_is_stop then
		return
	end

	if nil ~= BundleCache:GetCacheRes(bundle_name) then
		self:_OnBundleLoadSucc(session)
		return
	end

	if nil ~= self.v_load_bundle_records[bundle_name] then
		_tinsert(self.v_need_check_bundle_loads, {bundle_name = bundle_name, session = session})
		return
	end

	self.v_load_bundle_records[bundle_name] = 1

	if ResUtil.black_list[bundle_name] then
		print_error("[AssetBundleManager] assetbundle file maybe damaged!!", bundle_name, bundle_hash)
		self.v_load_bundle_records[bundle_name] = false
		self:_OnBundleLoadFail(session)
		return
	end

	-- 加密资源预处理(针对审核)
	-- if ResUtil.is_ios_encrypt_asset or ResUtil.is_android_encrypt_asset then
	-- 	local cache_file_path = ResUtil.GetCachePath(bundle_name, bundle_hash)
	-- 	if EncryptMgr.DecryptAssetBundle(bundle_file_path, cache_file_path) then
	-- 		bundle_file_path = cache_file_path
	-- 	end
	-- end

	local loading_bundle = nil
	if not self.v_crc_checked_list[bundle_file_path]
		and ResMgr:GetIsCanCheckCRC()
		and ResUtil.IsCachePath(bundle_file_path) 
	then
		if ResUtil.is_android_encrypt_asset or ResUtil.is_ios_encrypt_asset then
			loading_bundle = UnityAssetBundle.LoadFromFileAsync(bundle_file_path, ResMgr:GetBundleCRC(bundle_name), EncryptMgr.GetEncryptKeyLength(bundle_name))
		else
			loading_bundle = UnityAssetBundle.LoadFromFileAsync(bundle_file_path, ResMgr:GetBundleCRC(bundle_name))
		end
		
		if IS_LOCLA_WINDOWS_DEBUG_EXE then -- windows测试包即使失败也只处理一次
			self.v_crc_checked_list[bundle_file_path] = true
		end
	else
		if ResUtil.is_android_encrypt_asset or ResUtil.is_ios_encrypt_asset then
			loading_bundle = UnityAssetBundle.LoadFromFileAsync(bundle_file_path, 0, EncryptMgr.GetEncryptKeyLength(bundle_name))
		else
			loading_bundle = UnityAssetBundle.LoadFromFileAsync(bundle_file_path)
		end
		
	end

	if nil == loading_bundle then
		print_error("[AssetBundleManager] async load bundle fail, not exist bundle", bundle_file_path, bundle_name)
		self.v_load_bundle_records[bundle_name] = false
		self:_OnBundleLoadFail(session)
		return
	end

	self.v_crc_checked_list[bundle_file_path] = true
	self.v_loading_bundle_count = self.v_loading_bundle_count + 1
	local data = self:_GetLoadTable()
	data.self = self
	data.loading_bundle = loading_bundle
	data.bundle_file_path = bundle_file_path
	data.bundle_name = bundle_name
	data.session = session
	coroutine.start(LoadBundleAsyncCallBack, data)
end

local function LoadAssetAsyncCallBack(data)
	local self = data.self
	local request = data.request
	local asset_full_path = data.asset_full_path
	local bundle_name = data.bundle_name
	local asset_name = data.asset_name
	local session = data.session
	local load_priority = data.load_priority
	self:_ReleaseLoadTable(data)

	coroutine.www(request)

	if self.v_game_is_stop then
		if nil ~= request.asset then
			UnityEngine.Resources.UnloadAsset(request.asset)
		end
		return
	end

	self.v_loading_asset_count = self.v_loading_asset_count - 1
	if load_priority >= ResLoadPriority.faster_async_low and load_priority <= ResLoadPriority.faster_async_high then
		self.v_loading_faster_asset_count = self.v_loading_faster_asset_count - 1
	end

	if M.log_debug then
		M.Log("[AssetBundleManager] aysnc load asset from bundle complete", bundle_name, asset_name)
	end

	local asset = request.asset
	if nil == asset then
		print_error("[AssetBundleManager] async load asset from bundle fail", bundle_name, asset_name)
		self.v_load_asset_recoreds[asset_full_path] = false
		self:_OnAssetLoaded(session, nil)
		return
	end

	self.v_load_asset_recoreds[asset_full_path] = true
	self:_OnAssetLoaded(session, asset)
end

-- 从AB包中加载指定的Asset,如果有同样的Asset正在加载，则把此次请求加入检查列表。每帧检查该Asset的加载状态。
function M:_InternalLoadAssetAsync(bundle_name, asset_name, session, asset_type, load_priority)
	local asset = ResPoolMgr:ScanRes(bundle_name, asset_name)
	if nil ~= asset then
		self:_OnAssetLoaded(session, asset)
		return
	end

	local bundle = BundleCache:GetCacheRes(bundle_name)
	if nil == bundle then
		print_error("[AssetBundleManager] not exist bundle in asset cache", bundle_name)
		self:_OnAssetLoaded(session, nil)
		return
	end

	if M.log_debug then
		M.Log("[AssetBundleManager] start aysnc load asset from bundle complete", bundle_name, asset_name)
	end

	local asset_full_path = ResUtil.GetAssetFullPath(bundle_name, asset_name)
	if nil ~= self.v_load_asset_recoreds[asset_full_path] then
		local t = {bundle_name = bundle_name, asset_name = asset_name, asset_full_path = asset_full_path, session = session}
		_tinsert(self.v_need_check_asset_loads, t)
		return
	end

	self.v_load_asset_recoreds[asset_full_path] = 1

	local request = nil
	if nil ~= asset_type then
		request = bundle:LoadAssetAsync(asset_name, asset_type)
	else
		request = bundle:LoadAssetAsync(asset_name)
	end

	if nil == request then
		print_error("[AssetBundleManager] asset not exist from bundle", bundle_name, asset_name)
		self.v_load_asset_recoreds[asset_full_path] = false
		self:_OnAssetLoaded(session, nil)
	else
		self.v_loading_asset_count = self.v_loading_asset_count + 1
		if load_priority >= ResLoadPriority.faster_async_low and load_priority <= ResLoadPriority.faster_async_high then
			self.v_loading_faster_asset_count = self.v_loading_faster_asset_count + 1
		end

		local data = self:_GetLoadTable()
		data.self = self
		data.request = request
		data.asset_full_path = asset_full_path
		data.bundle_name = bundle_name
		data.asset_name = asset_name
		data.session = session
		data.load_priority = load_priority
		coroutine.start(LoadAssetAsyncCallBack, data)
	end
end

-- 下载多个assetbundle
function M:DownLoadBundles(bundle_infos, finish_callback, load_priority, cbdata)
	if nil == load_priority or load_priority <= ResLoadPriority.min or load_priority >= ResLoadPriority.max then
		print_error("[AssetBundleManager] DownLoadBundles, load_priority is invalid")
		finish_callback(false, cbdata)
		return
	end

	if #bundle_infos <= 0 then
		finish_callback(true, cbdata)
		return
	end

	if self.v_game_is_stop then
		return
	end

	local session = self:_NewLoadingSession()
	for _, bundle_name in ipairs(bundle_infos) do
		local bundle_hash = ResMgr:GetBundleHash(bundle_name)
		local cache_path = ResUtil.GetCachePath(bundle_name, bundle_hash)
		local download_t = self:_GetLoadTable()
		download_t.cache_path = cache_path
		download_t.bundle_name = bundle_name
		download_t.session = session
		download_t.load_priority = load_priority
		_tinsert(self.v_need_priority_download_bundles[load_priority], download_t)
	end

	self.v_need_download_bundle_infos[session] = {
		finish_callback = finish_callback,
		need_download_count = #bundle_infos,
		is_failed = false,
		cbdata = cbdata,
	}
end

-- 加载多个AB，先考虑缓存里是否有，无则加入加载队列
local need_load_bundles = {}
function M:LoadMultiBundlesAsync(bundle_infos, finish_callback, load_priority, cbdata)
	if nil == load_priority or load_priority <= ResLoadPriority.min or load_priority >= ResLoadPriority.max then
		print_error("[AssetBundleManager] LoadMultiBundlesAsync, load_priority is invalid")
		finish_callback(false, cbdata)
	end

	if self.v_game_is_stop then
		return
	end

	local session = self:_NewLoadingSession()
	local is_failed = false
	local count = 0

	for _, bundle_name in ipairs(bundle_infos) do
		if nil == BundleCache:GetCacheRes(bundle_name) then
			local bundle_hash = ResMgr:GetBundleHash(bundle_name)
			local bundle_file_path = ResUtil.GetBundleFilePath(bundle_name, bundle_hash)
			if nil == bundle_file_path then
				is_failed = true
				print_error("[AssetBundleManager] async load bundle fail, not exit bundle", bundle_name, bundle_hash)
			else
				local t = self:_GetLoadTable()
				t.bundle_file_path = bundle_file_path
				t.bundle_name = bundle_name
				t.bundle_hash = bundle_hash
				t.load_priority = load_priority
				t.session = session

				count = count + 1
				need_load_bundles[count] = t
			end
		end
	end

	if is_failed then
		finish_callback(false, cbdata)
		return
	end

	if count <= 0 then
		finish_callback(true, cbdata)
		return
	end

	for i = 1, count do
		local v = need_load_bundles[i]
		_tinsert(self.v_need_priority_load_bundles[v.load_priority], v)
	end

	self.v_need_load_bundle_infos[session] = {
		finish_callback = finish_callback,
		need_load_count = count,
		is_failed = false,
		cbdata = cbdata,
	}
end

-- 同步加载多个AB包，先考虑缓存里是否有，无则同步加载
-- 注:如果有资源已经正在异步加载中，则不再进行同步加载，否则会触发unity重复加载AssetBundle的接口
-- 注:为了方便处理这种情况。这种情况很少发生，且这种同步改为异步的方式不会对逻辑层有任何影响，只是减慢了些加载速度
function M:LoadMultiBundlesSync(bundle_infos, finish_callback, load_priority, cbdata)
	if self.v_game_is_stop then
		return
	end

	local is_in_async_loading = false
	for _, bundle_name in ipairs(bundle_infos) do
		if 1 == self.v_load_bundle_records[bundle_name] then
			is_in_async_loading = true
			break
		end
	end

	if is_in_async_loading or self.v_unloading then
		self:LoadMultiBundlesAsync(bundle_infos, finish_callback, ResLoadPriority.high, cbdata)
		return
	end

	local is_succ = true
	for _, bundle_name in ipairs(bundle_infos) do
		if nil == BundleCache:GetCacheRes(bundle_name) then
			local bundle_hash = ResMgr:GetBundleHash(bundle_name)
			local bundle_file_path = ResUtil.GetBundleFilePath(bundle_name, bundle_hash)
			if nil == bundle_file_path then
				print_error("[AssetBundleManager] not exit assetbundle file", bundle_name, bundle_hash)
				is_succ = false
				break
			elseif ResUtil.black_list[bundle_name] then
				print_error("[AssetBundleManager] assetbundle file maybe damaged!!", bundle_name, bundle_hash)
				is_succ = false
				break
			else
				-- 加密资源预处理(针对审核)
				-- if ResUtil.is_ios_encrypt_asset or ResUtil.is_android_encrypt_asset then
				-- 	local is_decrypt_ab = EncryptMgr.DecryptAssetBundle(bundle_file_path, cache_file_path)
				-- 	print_error("is_decrypt_ab =", is_decrypt_ab)
				-- 	if is_decrypt_ab then
				-- 		bundle_file_path = cache_file_path
				-- 	end
				-- end

				local bundle = nil
				if not self.v_crc_checked_list[bundle_file_path]
					and ResMgr:GetIsCanCheckCRC()
					and ResUtil.IsCachePath(bundle_file_path) 
				then
					if ResUtil.is_android_encrypt_asset or ResUtil.is_ios_encrypt_asset then
						bundle = UnityAssetBundle.LoadFromFile(bundle_file_path, ResMgr:GetBundleCRC(bundle_name), EncryptMgr.GetEncryptKeyLength(bundle_name))
					else
						bundle = UnityAssetBundle.LoadFromFile(bundle_file_path, ResMgr:GetBundleCRC(bundle_name))
					end
						
					if IS_LOCLA_WINDOWS_DEBUG_EXE then -- windows测试包即使失败也只处理一次
						self.v_crc_checked_list[bundle_file_path] = true
					end
				else
					if ResUtil.is_android_encrypt_asset or ResUtil.is_ios_encrypt_asset then
						bundle = UnityAssetBundle.LoadFromFile(bundle_file_path, 0, EncryptMgr.GetEncryptKeyLength(bundle_name))
					else
						bundle = UnityAssetBundle.LoadFromFile(bundle_file_path)
					end
				end

				if nil ~= bundle then
					self.v_crc_checked_list[bundle_file_path] = true
					BundleCache:CacheRes(bundle_name, bundle)
				else
					-- 来到这里说明bundle本地文件已损坏，重新启动下载
					local cal_md5 = 0
					if SysFile.Exists(bundle_file_path) then
						cal_md5 = MD5.GetMD5FromFile(bundle_file_path)
					end

					print_error("[AssetBundleManager] async load bundle fail, bundle is nil ", bundle_file_path, bundle_name, cal_md5)
					ResUtil.RemoveFile(bundle_name, bundle_file_path)
					self.bundle_damaged_list[bundle_name] = true
					self:DownLoadBundles({bundle_name}, function (is_succ)
						print_error("[AssetBundleManager] bigbug! load assetbundle fail, restart download callback", bundle_name, is_succ)
					end, ResLoadPriority.high)

					is_succ = false
					break
				end
			end
		end
	end
	finish_callback(is_succ, cbdata)
end

-- 从指定的AB包里异步加载资源，调用此方法前确保BundleCache里存在此AB
function M:LoadAssetAsync(bundle_name, asset_name, finish_callback, asset_type, load_priority, cbdata)
	if nil == load_priority or load_priority <= ResLoadPriority.min or load_priority >= ResLoadPriority.max then
		print_error("[AssetBundleManager] LoadMultiBundlesAsync, load_priority is invalid")
		finish_callback(nil, cbdata)
		return
	end

	if self.v_game_is_stop then
		return
	end

	local asset = ResPoolMgr:ScanRes(bundle_name, asset_name)
	if nil ~= asset then
		finish_callback(asset, cbdata)
		return
	end

	local bundle = BundleCache:GetCacheRes(bundle_name)
	if nil == bundle then
		print_error("[AssetBundleManager] async load asset fail, because not exist bundle", bundle_name, asset_name)
		finish_callback(nil, cbdata)
		return
	end

	local session = self:_NewLoadingSession()
	local t = {bundle_name = bundle_name, asset_name = asset_name, session = session, asset_type = asset_type, load_priority = load_priority}
	_tinsert(self.v_need_priority_load_assets[load_priority], t)
	self.v_need_load_asset_infos[session] = {
		finish_callback = finish_callback,
		cbdata = cbdata,
	}
end

-- 从指定的AB包里同步加载资源，调用此方法前确保BundleCache里存在此AB
-- 注:如果该资源已经正在异步加载中，则不再进行同步加载，否则可能会触发未知问题（加载资源一个在同步，一个在异步）
-- 注:为了方便处理这种情况。这种情况很少发生，且这种同步改为异步的方式不会对逻辑层有任何影响，只是减慢了些加载速度
function M:LoadAssetSync(bundle_name, asset_name, finish_callback, asset_type, load_priority, cbdata)
	local asset = ResPoolMgr:ScanRes(bundle_name, asset_name)
	if nil ~= asset then
		finish_callback(asset, cbdata)
		return
	end

	if self.v_game_is_stop then
		return
	end

	local asset_full_path = ResUtil.GetAssetFullPath(bundle_name, asset_name)
	if 1 == self.v_load_asset_recoreds[asset_full_path] or self.v_unloading then
		self:LoadAssetAsync(bundle_name, asset_name, finish_callback, asset_type, ResLoadPriority.high, cbdata)
		return
	end

	local bundle = BundleCache:GetCacheRes(bundle_name)
	if nil == bundle then
		print_error("[AssetBundleManager] sync load asset fail, because not exist bundle", bundle_name, asset_name)
		finish_callback(nil, cbdata)
		return
	end

	if nil ~= asset_type then
		asset = bundle:LoadAsset(asset_name, asset_type)
	else
		asset = bundle:LoadAsset(asset_name)
	end

	if nil == asset then
		print_error("[AssetBundleManager] sync load asset from bundle fail", bundle_name, asset_name)
		finish_callback(nil, cbdata)
		return
	end

	finish_callback(asset, cbdata)
end

function M:_OnDownloadBundleSucc(session)
	local info = self.v_need_download_bundle_infos[session]
	info.need_download_count = info.need_download_count - 1

	if info.need_download_count == 0 then
		self.v_need_download_bundle_infos[session] = nil
		info.finish_callback(not info.is_failed, info.cbdata)
	elseif info.need_download_count < 0 then
		print_error("[AssetBundleManager] _OnDownloadBundleSucc big bug, download count less 0")
	end
end

function M:_OnDownloadBundleFail(session)
	local info = self.v_need_download_bundle_infos[session]
	info.is_failed = true
	info.need_download_count = info.need_download_count - 1

	if info.need_download_count == 0 then
		self.v_need_download_bundle_infos[session] = nil
		info.finish_callback(false, info.cbdata)
	elseif info.need_download_count < 0 then
		print_error("[AssetBundleManager] _OnDownloadBundleFail big bug, download count less 0")
	end
end

function M:_OnBundleLoadSucc(session)
	local info = self.v_need_load_bundle_infos[session]
	info.need_load_count = info.need_load_count - 1

	if info.need_load_count == 0 then
		self.v_need_load_bundle_infos[session] = nil
		info.finish_callback(not info.is_failed, info.cbdata)
	elseif info.need_load_count < 0 then
		print_error("[AssetBundleManager] _OnBundleLoadSucc big bug, load count less 0")
	end
end

function M:_OnBundleLoadFail(session)
	local info = self.v_need_load_bundle_infos[session]
	info.need_load_count = info.need_load_count - 1
	info.is_failed = true

	if info.need_load_count == 0 then
		self.v_need_load_bundle_infos[session] = nil
		info.finish_callback(false, info.cbdata)
	elseif info.need_load_count < 0 then
		print_error("[AssetBundleManager] _OnBundleLoadFail big bug, load count less 0")
	end
end

function M:_OnAssetLoaded(session, asset)
	local info = self.v_need_load_asset_infos[session]
	if nil == info then
		print_error("[AssetBundleManager] _OnAssetLoaded big bug, load count less 0", session, asset)
		return
	end

	self.v_need_load_asset_infos[session] = nil
	info.finish_callback(asset, info.cbdata)
end

function M:_OnSceneLoaded()
	local t = self.v_load_scene_t
	self.v_load_scene_t = nil
	t.finish_callback(true)
end

function M:_NewLoadingSession()
	self.v_loading_session = self.v_loading_session + 1
	return self.v_loading_session
end

function M:OnGameStop()
	self.v_game_is_stop = true

	for k, queue in pairs(self.v_need_priority_sync_write_bundles) do
		for _, v in ipairs(queue) do
			v.downloading_www:Dispose()
		end
		self.v_need_priority_sync_write_bundles[k] = {}
	end

	for k, queue in pairs(self.v_need_priority_async_write_bundles) do
		for _, v in ipairs(queue) do
			v.downloading_www:Dispose()
		end
		self.v_need_priority_async_write_bundles[k] = {}
	end

	for k,v in pairs(self.v_async_write_handle_list) do
		k:Stop()
	end
	self.v_async_write_handle_list = {}

	for _,v in pairs(self.v_download_handle_list) do
		v:OnDestroy()
	end
	self.v_download_handle_list = {}

	LoadTableList = {}
	DownLoadConfig.Delete()
end

function M:_UpdateUnloadState()
	-- 防止失效。超过一定时间，直接返回，不处理
	if self.v_unload_invalid_time > 0 and GlobalUnityTime >= self.v_unload_invalid_time then
		self.v_unload_invalid_time = 0
		if nil ~= self.v_unloading_finish_callback then
			local callback = self.v_unloading_finish_callback
			self.v_unloading_finish_callback = nil
			callback()
		end
		return
	end

	local can_unload = true
	for i,v in ipairs(self.v_load_bundle_records) do
		if v == 1 then
			can_unload = false
		end
	end

	for i,v in ipairs(self.v_load_asset_recoreds) do
		if v == 1 then
			can_unload = false
		end
	end

	if can_unload then
		self.v_unload_timer_count = self.v_unload_timer_count + 1
		if self.v_unload_timer_count == 2 then
			UnityEngine.Resources.UnloadUnusedAssets()
		elseif self.v_unload_timer_count == 4 then
			self.v_unload_invalid_time = 0
			self.v_unload_timer_count = 0
			self.v_unloading = false
			if nil ~= self.v_unloading_finish_callback then
				local callback = self.v_unloading_finish_callback
				self.v_unloading_finish_callback = nil
				callback()
			end
		end
	end
end

function M:UnloadUnusedAssets(finish_callback)
	self.v_unload_invalid_time = GlobalUnityTime + 2
	self.v_unloading = true
	self.v_unload_timer_count = 0
	self.v_unloading_finish_callback = finish_callback
end

function M:NeedWaitAssetBundleUnLoad()
	return self.v_loading_bundle_count > 0
end

function M:WaitAssetBundleUnLoad(callback)
	if self.v_loading_bundle_count > 0 then
		self.wait_assetbundle_unload_callback = callback
	else
		callback()
	end
end

function M:GetTotalFileSize(bundles)
	local total_size = 0
	for k,v in ipairs(bundles) do
		local file_size = ResMgr:GetBundleSize(v) or 0
		total_size = total_size + file_size
	end
	return total_size
end

function M:IsAssetBundleDownloading(cache_path)
	return nil ~= self.v_download_bundle_records[cache_path]
end

-- 等待AB下载完成（要求这个AB已经在下载中）
function M:WaitAssetBundleDownloaded(cache_path, bundle_name, callback)
	if nil == self.v_download_bundle_records[cache_path] then
		callback(false)
		return
	end

	-- 因为这个AB已经在下载中了，DownLoadBundles接口会自动把这次下载插入到检查列表
	self:DownLoadBundles({bundle_name}, callback, ResLoadPriority.high)
end

-- 创建缓冲器(由逻辑层控制)
local has_created_buffers = false
local need_destory_buffers = false
function M:CreateBuffers()
	if self.v_is_download_write_intime and not has_created_buffers then
		has_created_buffers = true
		for i = 1, 2 do
			DownloadBufferMgr.CreateBuffer(0.5 * 1024 * 1024)
		end

		for i = 1, 2 do
			DownloadBufferMgr.CreateBuffer(1 * 1024 * 1024)
		end

		for i = 1, 2 do
			DownloadBufferMgr.CreateBuffer(2 * 1024 * 1024)
		end

		for i = 1, 1 do
			DownloadBufferMgr.CreateBuffer(4 * 1024 * 1024)
		end

		for i = 1, 1 do
			DownloadBufferMgr.CreateBuffer(8 * 1024 * 1024)
		end
	end
end

-- 删除缓冲器(由逻辑层控制)
function M:DestoryBuffers()
	need_destory_buffers = true
end

function M:_TryDestoryBuffers()
	if self.v_is_download_write_intime and need_destory_buffers then
		need_destory_buffers = false
		DownloadBufferMgr.DestoryAllBuffer()
	end
end

function M:_DestroyDownload(download_t)
	if nil ~= download_t then
		local download_handler = download_t.download_handler
		local downloading_www = download_t.downloading_www

		if nil ~= download_handler then
			self.v_download_handle_list[download_handler] = nil
			-- OnDestroy()可能会触发 DownloadWriteInlineCallBack()回调，导致 download_t 被clear了
			download_handler:OnDestroy()
			ResMgr:DestroySysObj(download_handler)
			download_t.download_handler = nil
		end

		if nil ~= downloading_www then
			downloading_www:Dispose()
			ResMgr:DestroySysObj(downloading_www)
			download_t.downloading_www = nil
		end
	end
end

local LoadTableList = {}
function M:_GetLoadTable()
    local t = table.remove(LoadTableList)
    if nil == t then
        t = {}
    end

    return t
end

function M:_ReleaseLoadTable(t)
    t.downloading_www = nil
    t.loading_bundle = nil
    t.download_handler = nil
    t.request = nil
    table.insert(LoadTableList, t)
end

return M