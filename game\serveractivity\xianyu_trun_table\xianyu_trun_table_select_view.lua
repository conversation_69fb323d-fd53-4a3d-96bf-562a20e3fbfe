
XianyuTrunTableSelectView = XianyuTrunTableSelectView or BaseClass(SafeBaseView)
function XianyuTrunTableSelectView:__init()
	self:SetMaskBg(true,true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/xianyu_trun_table_prefab", "layout_select_rewared_view")
end

function XianyuTrunTableSelectView:__delete()

end

function XianyuTrunTableSelectView:ReleaseCallBack()

	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end

end

function XianyuTrunTableSelectView:OpenCallBack()
end

function XianyuTrunTableSelectView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["bg_size"])
	self.node_list.title_view_name.text.text = Language.XianyuTrunTable.SelectViewTitle
	self.change_select_list = AsyncListView.New(XianyuTrunTableSelectBigCell, self.node_list.xianling_list)
	self.node_list["confirm"].button:AddClickListener(BindTool.Bind(self.OnClickConfirm,self))
end

function XianyuTrunTableSelectView:OnClickConfirm()
	local all_select_flag = XianyuTrunTableWGData.Instance:GetTempSelectList()
	for i = 1, 4 do
		local have_num,need_num = XianyuTrunTableWGData.Instance:GetHaoManyWeSelect(i)
		if have_num ~= need_num then
			TipWGCtrl.Instance:ShowSystemMsg(Language.XianyuTrunTable.PleaseSelect)
			return
		end
	end

	local default_select = {}
	for i = 1 , 4 do
		default_select[i] = {}
		for q = 1, 32 do
			default_select[i][33-q] = all_select_flag[i][q] and 1 or 0
		end
	end

	XianyuTrunTableWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_CHOOSE_REWARD,bit:b2d(default_select[1]),bit:b2d(default_select[2]),bit:b2d(default_select[3]))
	self:Close()
end

function XianyuTrunTableSelectView:OnFlush()
	local all_data = XianyuTrunTableWGData.Instance:GetAllItemList()
	self.change_select_list:SetDataList(all_data)
end


XianyuTrunTableSelectBigCell = XianyuTrunTableSelectBigCell or BaseClass(BaseRender)

function XianyuTrunTableSelectBigCell:__init()
	self.change_select_list = AsyncListView.New(XianyuTrunTableSelectSmallCell, self.node_list.item_group)
end

function XianyuTrunTableSelectBigCell:__delete()
	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end
end

function XianyuTrunTableSelectBigCell:OnFlush()
	if not IsEmptyTable(self.data) then
		self.level = self.data[1].index1
		for k,v in pairs(self.data) do
			v.click_call_back = function ()
				self:FlushSelectNum()
			end
		end
		self.change_select_list:SetDataList(self.data)
		local bundle, asset = ResPath.GetZhouYiYunChengImg("zyyc_t"..self.level)
 		self.node_list["img"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["img"].image:SetNativeSize()
		end)

		self:FlushSelectNum()
	else

	end
end


function XianyuTrunTableSelectBigCell:FlushSelectNum()
	local have_num,limite_num = XianyuTrunTableWGData.Instance:GetHaoManyWeSelect(self.level)
	local color = have_num == limite_num and COLOR3B.GREEN or COLOR3B.RED
	local str = "("..ToColorStr(have_num,color).."/"..limite_num..")"
	self.node_list["select_num"].text.text = str
end

XianyuTrunTableSelectSmallCell = XianyuTrunTableSelectSmallCell or BaseClass(BaseRender)

function XianyuTrunTableSelectSmallCell:__init()
	self.item_cell = ItemCell.New(self.node_list["item_pos"])
	--self.item_cell:UseNewSelectEffect(true)
	self.node_list["click_area"].button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
	-- self.node_list["select_area"].button:AddClickListener(BindTool.Bind(self.OnClickSelect, self))
end

function XianyuTrunTableSelectSmallCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function XianyuTrunTableSelectSmallCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local cell_data = {}
		cell_data.item_id = tonumber(self.data.item_id)
		cell_data.num = tonumber(self.data.num)
		cell_data.is_bind = tonumber(self.data.is_bind)
		self.item_cell:SetData(cell_data)
	else

	end
	self:FlushSelect()
end

function XianyuTrunTableSelectSmallCell:OnClickSelect()
	local index1 = self.data.index1
	local index2 = self.data.index2
	local value = XianyuTrunTableWGData.Instance:TryToSelectOrUnSelectThis(index1, index2)
	if value then
		self:FlushSelect()
		if self.data.click_call_back then
			self.data.click_call_back()
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.XianyuTrunTable.IsSelectEnough)
	end
end

function XianyuTrunTableSelectSmallCell:FlushSelect()
	local index1 = self.data.index1
	local index2 = self.data.index2
	local is_select = XianyuTrunTableWGData.Instance:GetItemIsBeenSelect(index1, index2)
	self.node_list["select_flag"]:SetActive(is_select)
	--self.item_cell:SetSelectEffect(is_select)
end

function XianyuTrunTableSelectSmallCell:OnClickItem()
	local cell_data = {}
	cell_data.item_id = tonumber(self.data.item_id)
	cell_data.num = tonumber(self.data.num)
	cell_data.is_bind = tonumber(self.data.is_bind)
	TipWGCtrl.Instance:OpenItem(cell_data)
end