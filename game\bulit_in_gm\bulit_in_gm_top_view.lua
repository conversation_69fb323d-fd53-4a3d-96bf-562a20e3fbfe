BulitInTopGMView = BulitInTopGMView or BaseClass(SafeBaseView)
function BulitInTopGMView:__init()
    self.view_layer = UiLayer.GM
    self.view_name = "BulitInTopGMView"
    self.self_control_rendring = true
    self.active_close = false
	self:AddViewResource(0, "uis/view/bulit_in_gm_prefab", "layout_top_gm")
end

function BulitInTopGMView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_open_gm, BindTool.Bind(self.OnClickOpenGM, self))
end

function BulitInTopGMView:OnClickOpenGM()
    if not BulitInGMCtrl.Instance.view:IsOpen() then
        BulitInGMCtrl.Instance.view:Open()
    else
        BulitInGMCtrl.Instance.view:Close()
    end
end
