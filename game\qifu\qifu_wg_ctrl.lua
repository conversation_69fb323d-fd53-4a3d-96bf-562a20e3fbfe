require("game/qifu/qifu_wg_data")
require("game/qifu/qifu_view")
require("game/qifu/qifu_wish_view")
require("game/qifu/qifu_get_energy_view")
require("game/qifu/qifu_hongmeng_view")
require("game/qifu/qifu_yunshi_view")
require("game/welfare/qifu_yunshi/qifu_yunshi_wg_data")
require("game/welfare/qifu_yunshi/qifu_yunshi_result_view")
require("game/welfare/qifu_yunshi/qifu_yunshi_first_view")

QiFuWGCtrl = QiFuWGCtrl or BaseClass(BaseWGCtrl)
function QiFuWGCtrl:__init()
	if QiFuWGCtrl.Instance then
		error("[QiFuWGCtrl]:Attempt to create singleton twice!")
	end
	QiFuWGCtrl.Instance = self

	self.data = QiFuWGData.New()
	self.qifu_yunshi_data = QifuYunShiWGData.New()
	self.view = QiFuView.New(GuideModuleName.QIFU)
	--self.qifu_yunshi_result_view = QifuYunShiResultView.New(GuideModuleName.QifuYunShiResultView)
	self.qifu_yunshi_first_view = QifuYunShiFirstView.New(GuideModuleName.QifuYunShiFirstView)
	self:RegisterAllProtocols()
	self.qifucheckbox = true
	self.qifucheckbox1 = true
	self.ganwucheckbox = true
	self.need_open_yunshi_flag = false

	self.fun_open_event = BindTool.Bind(self.FunOpenEvent, self)
	FunOpen.Instance:NotifyFunOpen(self.fun_open_event)

	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind1(self.OnFlyDownEnd, self))
	self.open_yunshi_event = BindTool.Bind(self.OpenYunShiAnim,self)

	self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChange, self))
end

function QiFuWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.qifu_yunshi_data then
		self.qifu_yunshi_data:DeleteMe()
		self.qifu_yunshi_data = nil
	end

	-- if self.qifu_yunshi_result_view then
	-- 	self.qifu_yunshi_result_view:DeleteMe()
	-- 	self.qifu_yunshi_result_view = nil
	-- end

	if self.qifu_yunshi_first_view then
		self.qifu_yunshi_first_view:DeleteMe()
		self.qifu_yunshi_first_view = nil
	end

	FunOpen.Instance:UnNotifyFunOpen(self.fun_open_event)

	self.qifucheckbox = true
	self.qifucheckbox1 = true
	self.ganwucheckbox = true
	QiFuWGCtrl.Instance = nil

	GlobalEventSystem:UnBind(self.fly_down_end_event)
	self.fly_down_end_event = nil

	GlobalEventSystem:UnBind(self.pass_day_event)
	self.pass_day_event = nil

end

function QiFuWGCtrl:RegisterAllProtocols()
	---------------------鸿蒙感悟--------------------------------
	self:RegisterProtocol(SCExpBuyInfo, "OnSCExpBuyInfo")
	self:RegisterProtocol(CSExpBuyReqBuy)

	--------------------运势-----------------------
	self:RegisterProtocol(CSFortuneOperate)								-- 运势操作请求
	self:RegisterProtocol(SCFortuneInfo,"OnSCFortuneInfo")				-- 运势返回
end

--鸿蒙感悟
function QiFuWGCtrl:SendExpBuyReqBuy()
	self.data:SetBtnEffect(false)
	local protocol = ProtocolPool.Instance:GetProtocol(CSExpBuyReqBuy)
	protocol:EncodeAndSend()
	
end 

function QiFuWGCtrl:OnSCExpBuyInfo(protocol)
	self.data:SetExpBuyInfo(protocol)
	if self.view:IsOpen() then
		self.data:SetBtnEffect(true)
		RankWGCtrl.Instance:SendRankReq(RankKind.Person,PersonRankType.Level)
		RemindManager.Instance:Fire(RemindName.HongMengWuDao)
		self.view:Flush(TabIndex.qifu_hmwd)
	end
end

function QiFuWGCtrl:Open(tab_index, param_t)
	self.view:Open(tab_index, param_t)
end

function QiFuWGCtrl:OnVipInfo(protocol)
	if protocol.obj_id == GameVoManager.Instance:GetMainRoleVo().obj_id then
		self.data:SetWelfareQiFuData(protocol.gold_buycoin_times, protocol.gold_buyyuanli_times,
		 protocol.last_free_buycoin_timestamp,protocol.qifu_reward_mutiple)
		self:FlushQiFuView(TabIndex.qifu_qf)
		-- if WelfareWGCtrl.Instance.view:IsOpen() then
		-- 	WelfareWGCtrl.Instance:Flush(WELFARE_TYPE.QIFU)
		-- end
		-- if self.view:IsOpen() then
		-- 	self.view:OnFlushEffect()
		-- end		
	end
	
	
end
function QiFuWGCtrl:CheckWelfareRemind(remind_id)
	return QiFuWGData.Instance:RemindTip()
end
function QiFuWGCtrl:FlushQiFuView(index)
	if self.view:IsOpen() then
		self.view:Flush(index)
	end
end
function QiFuWGCtrl:GetPanelShowIndex()
	if self.view:IsOpen() then
		local index = self.view:GetShowIndex()
		return index == TabIndex.qifu_qf
	end
end

function QiFuWGCtrl:GetWelfareQiFuCheckBox()
	return self.qifucheckbox
end

function QiFuWGCtrl:GetWelfareGanWuCheckBox()
	return self.ganwucheckbox
end

function QiFuWGCtrl:SetWelfareGanWuCheckBox(is_cun)
	self.ganwucheckbox = is_cun
end

function QiFuWGCtrl:SetWelfareQiFuCheckBox(is_cun)
	self.qifucheckbox = is_cun
end

function QiFuWGCtrl:GetWelfareQiFuCheckBox1()
	return self.qifucheckbox1
end

function QiFuWGCtrl:SetWelfareQiFuCheckBox1(is_cun)
	self.qifucheckbox1 = is_cun
end

function QiFuWGCtrl:SendCSFortuneOperate(operate_type,param1,param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSFortuneOperate)
	send_protocol.operate_type = operate_type or 0
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end

function QiFuWGCtrl:OnSCFortuneInfo(protocol)
	--local send_chouqian_falg = QifuYunShiWGData.Instance:GetSendChouQianFlag()
	local ignoire_anim_flag = QifuYunShiWGData.Instance:GetIgnoireAnimFlag()
	self.qifu_yunshi_data:SetSCFortuneInfo(protocol)
	RemindManager.Instance:Fire(RemindName.QiFuYunShi)
	QiFuWGCtrl.Instance:FlushQiFuView(TabIndex.qifu_yunshi)
	--local first_send_chouqian_falg = QifuYunShiWGData.Instance:GetFirstSendChouQianFlag()

	if self.view:IsOpen() and self.view:IsLoadedIndex(TabIndex.qifu_yunshi) then
		-- self.view:PlayYunShiAnimEffect()
	end	

	if self.qifu_yunshi_first_view and self.qifu_yunshi_first_view:IsOpen() and self.qifu_yunshi_first_view:IsLoaded() then
		self.qifu_yunshi_first_view:Flush()

		if protocol.fortune_type > 0 and not ignoire_anim_flag then
		 	self.qifu_yunshi_first_view:YunShiAnim()
			QifuYunShiWGData.Instance:SetSendChouQianFlag(false)
		end
	else
		if protocol.fortune_type > 0 and  not ignoire_anim_flag then
			if self.view:IsOpen() and self.view:IsLoadedIndex(TabIndex.qifu_yunshi) then
				self.view:PlayYunShiAnim()
			end		
			QifuYunShiWGData.Instance:SetSendChouQianFlag(false)
		end
	end
	
	MainuiWGCtrl.Instance:AddInitCallBack(nil,function ()
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.Common then
			self:OpenYunShiAnim()
		else
			self.need_open_yunshi_flag = true
		end
	end)
	
	GlobalEventSystem:Fire(MainUIEventType.EXP_EFFICIENCY_INFO)
end

function QiFuWGCtrl:FunOpenEvent(fun_name)
	if fun_name == "qifu_yunshi" then
		MainuiWGCtrl.Instance:AddInitCallBack(nil,function ()
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.Common then
				MainuiWGCtrl.Instance:AddInitCallBack(nil,self.open_yunshi_event)
			else
				self.need_open_yunshi_flag = true
			end
		end)
		
		FunOpen.Instance:UnNotifyFunOpen(self.fun_open_event)
	end
end

function QiFuWGCtrl:OpenYunShiAnim()
	local info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local fortune_type = info.fortune_type
	local has_info = info.has_info
	local is_open = FunOpen.Instance:GetFunIsOpened("qifu_yunshi")
	if has_info and fortune_type and fortune_type == 0 and not self.first_login_game and is_open then
		self.first_login_game = true
		-- ViewManager.Instance:Open(GuideModuleName.QifuYunShiFirstView)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.QIFU_YUNSHI_TIP, 1,function ()
			-- ViewManager.Instance:Open(GuideModuleName.QifuYunShiFirstView)
			QiFuWGCtrl.Instance:Open(TabIndex.qifu_yunshi)
			-- MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.QIFU_YUNSHI_TIP)
			return true
		end)
		self.need_open_yunshi_flag = false
	else
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.QIFU_YUNSHI_TIP)
	end
end

function QiFuWGCtrl:OnFlyDownEnd()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Common and self.need_open_yunshi_flag then
		MainuiWGCtrl.Instance:AddInitCallBack(nil,self.open_yunshi_event)
	end
end

function QiFuWGCtrl:DayChange()
	self.first_login_game = false
end

function QiFuWGCtrl:FlushQiFuGetEnergyView()
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.qifu_getengrgy)
	end
end

function QiFuWGCtrl:OpenQiFuView(tab_index)
	self.view:Open(tab_index)
end