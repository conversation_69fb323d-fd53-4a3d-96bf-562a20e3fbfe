RectTransform = {}

local vector2 = Vector2(0, 0)
local vector3 = Vector3(0, 0, 0)
function RectTransform.GetSizeDelta(rect_transform, size_delta)
	if IsNil(rect_transform) then return size_delta end

	size_delta = rect_transform.sizeDelta
	return size_delta
end

function RectTransform.GetAnchoredPosition(rect_transform, anchored_position)
	if IsNil(rect_transform) then return anchored_position end

	anchored_position = rect_transform.anchoredPosition
	return anchored_position
end

function RectTransform.GetAnchoredPositionXY(rect_transform)
	if IsNil(rect_transform) then return 0, 0 end

	vector2 = rect_transform.anchoredPosition
	return vector2.x, vector2.y
end

function RectTransform.SetSizeDeltaXY(rect_transform, x, y)
	if IsNil(rect_transform) then
		return
	 end

	vector2.x = x
	vector2.y = y
	rect_transform.sizeDelta = vector2
end

--[[ 精度有问题,暂时屏蔽
function RectTransform.GetLocalScaleXYZ(rect_transform)
	if IsNil(rect_transform) then return 0, 0, 0 end

	Vector3.SetTemporary(vector3)
	vector3 = rect_transform.localScale
	return vector3.x, vector3.y, vector3.z
end
]]

function RectTransform.SetLocalScale(rect_transform, scale)
	if IsNil(rect_transform) then
		return
	 end

	 vector3.x = scale
	 vector3.y = scale
	 vector3.z = scale
	 rect_transform.localScale = vector3
end

function RectTransform.SetLocalScaleXYZ(rect_transform, x, y, z)
	if IsNil(rect_transform) then
		return
	 end
	 vector3.x = x
	 vector3.y = y
	 vector3.z = z
	 rect_transform.localScale = vector3
end

function RectTransform.SetAnchoredPositionXY(rect_transform, x, y)
	if IsNil(rect_transform) then
		return
	end

	vector2.x = x
	vector2.y = y
	rect_transform.anchoredPosition = vector2
end

function RectTransform.SetAnchoredPosition3DXYZ(rect_transform, x, y, z)
	if IsNil(rect_transform) then
		return
	end

	vector3.x = x
	vector3.y = y
	vector3.z = z
	rect_transform.anchoredPosition3D = vector3
end

function RectTransform.SetAnchorMinXY(rect_transform, x, y)
	if IsNil(rect_transform) then
		return
	end

	vector2.x = x
	vector2.y = y
	rect_transform.anchorMin = vector2
end

function RectTransform.SetAnchorMaxXY(rect_transform, x, y)
	if IsNil(rect_transform) then
		return
	end

	vector2.x = x
	vector2.y = y
	rect_transform.anchorMax = vector2
end

function RectTransform.SetPivotXY(rect_transform, x, y)
	if IsNil(rect_transform) then
		return
	end

	vector2.x = x
	vector2.y = y
	rect_transform.pivot = vector2
end

function RectTransform.SetAnchorAllign(rect_transform, allign)
	local min_x, min_y = 0.5, 0.5
	local max_x, max_y = 0.5, 0.5
	if allign == AnchorPresets.TopLeft then
		min_x, min_y = 0, 1
		max_x, max_y = 0, 1

	elseif allign == AnchorPresets.TopCenter then
		min_x, min_y = 0.5, 1
		max_x, max_y = 0.5, 1

	elseif allign == AnchorPresets.TopRight then
		min_x, min_y = 1, 1
		max_x, max_y = 1, 1

	elseif allign == AnchorPresets.MiddleLeft then
		min_x, min_y = 0, 0.5
		max_x, max_y = 0, 0.5

	elseif allign == AnchorPresets.MiddleCenter then
		min_x, min_y = 0.5, 0.5
		max_x, max_y = 0.5, 0.5

	elseif allign == AnchorPresets.MiddleRight then
		min_x, min_y = 1, 0.5
		max_x, max_y = 1, 0.5

	elseif allign == AnchorPresets.BottomLeft then
		min_x, min_y = 0, 0
		max_x, max_y = 0, 0

	elseif allign == AnchorPresets.BottonCenter then
		min_x, min_y = 0.5, 0
		max_x, max_y = 0.5, 0

	elseif allign == AnchorPresets.BottomRight then
		min_x, min_y = 1, 0
		max_x, max_y = 1, 0

	elseif allign == AnchorPresets.HorStretchTop then
		min_x, min_y = 0, 1
		max_x, max_y = 1, 1

	elseif allign == AnchorPresets.HorStretchMiddle then
		min_x, min_y = 0, 0.5
		max_x, max_y = 1, 0.5

	elseif allign == AnchorPresets.HorStretchBottom then
		min_x, min_y = 0, 0
		max_x, max_y = 1, 0

	elseif allign == AnchorPresets.VertStretchLeft then
		min_x, min_y = 0, 0
		max_x, max_y = 0, 1

	elseif allign == AnchorPresets.VertStretchCenter then
		min_x, min_y = 0.5, 0
		max_x, max_y = 0.5, 1

	elseif allign == AnchorPresets.VertStretchRight then
		min_x, min_y = 1, 0
		max_x, max_y = 1, 1

	elseif allign == AnchorPresets.StretchAll then
		min_x, min_y = 0, 0
		max_x, max_y = 1, 1
	end

	RectTransform.SetAnchorMinXY(rect_transform, min_x, min_y)
	RectTransform.SetAnchorMaxXY(rect_transform, max_x, max_y)
end

function RectTransform.SetPivotAllign(rect_transform, allign)
	local x, y = 0.5, 0.5
	if allign == PivotPresets.TopLeft then
		x, y = 0, 1

	elseif allign == PivotPresets.TopCenter then
		x, y = 0.5, 1

	elseif allign == PivotPresets.TopRight then
		x, y = 1, 1

	elseif allign == PivotPresets.MiddleLeft then
		x, y = 0, 0.5

	elseif allign == PivotPresets.MiddleCenter then
		x, y = 0.5, 0.5

	elseif allign == PivotPresets.MiddleRight then
		x, y = 1, 0.5

	elseif allign == PivotPresets.BottomLeft then
		x, y = 0, 0

	elseif allign == PivotPresets.BottomCenter then
		x, y = 0.5, 0

	elseif allign == PivotPresets.BottomRight then
		x, y = 1, 0
	end

	RectTransform.SetPivotXY(rect_transform, x, y)
end

local mt = {}
mt.__index = function (tbl, key)
	return UnityEngine.RectTransform[key]
end

setmetatable(RectTransform, mt)
