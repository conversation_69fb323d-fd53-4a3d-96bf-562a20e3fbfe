BeKilledView = BeKilledView or BaseClass(SafeBaseView)
local FuHuoType = {
    None = 0,
	Common = 1,
	Here = 2,
}
local stone_id = COMMON_CONSTS.RESURGENCE_STONE --复活石Id
function BeKilledView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false, false)

	self.is_modal = true
	self.is_any_click_close = false
	self.killer_name = ""
	self.killer_level = ""
	self.active_close = false
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_bekilled")
    self.fuhuo_type = FuHuoType.None
    self.is_world_boss_tired = false

   	self.is_show_add_enemy = false
	self.enemy_plat_type = nil
	self.enemy_server_id = nil
	self.enemy_uid = nil
end

function BeKilledView:__delete()
	self.fuhuo_common_callback = nil
	self.fuhuo_here_callback = nil
	self.killer_name = nil
	self.is_role = nil
	self.killer_level = nil
end

function BeKilledView:ReleaseCallBack()
	self.fuhuo_type = FuHuoType.None

	if CountDownManager.Instance:HasCountDown("fuhuo_boss_map") then
		CountDownManager.Instance:RemoveCountDown("fuhuo_boss_map")
	end

	self.enemy_plat_type = nil
	self.enemy_server_id = nil
	self.enemy_uid = nil
end

function BeKilledView:CloseCallBack()
	self.fuhuo_type = FuHuoType.None
	self.killer_name = ""
    self.killer_level = ""
    self.is_world_boss_tired = false
	CountDownManager.Instance:RemoveCountDown("fuhuo_boss_map")

	if self.fuhuo_delay then
		GlobalTimerQuest:CancelQuest(self.fuhuo_delay)
		self.fuhuo_delay = nil
	end
end

function BeKilledView:OpenCallBack()
	self.fuhuo_type = FuHuoType.None
end

function BeKilledView:LoadCallBack()
	self.is_free_fuhuo = false
    --self:SetSecondView(Vector2(740, 400))
    --self.node_list["btn_close_window"]:SetActive(false)
	--self.node_list.title_view_name.tmp.text = Language.Fuhuo.HeadTips
	--self.node_list["btn_equip_bg"].button:AddClickListener(BindTool.Bind(self.OnCLickEquip, self))
	--self.node_list["btn_ride_bg"].button:AddClickListener(BindTool.Bind(self.OnCLickRide, self))
	self.node_list["btn_fuhuo_common"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoCommon, self))
	self.node_list["btn_fuhuo_free_time"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
	self.node_list["btn_stone_stone"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
	self.node_list["btn_fuhuo_yu"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
	--self.node_list["btn_canel"].button:AddClickListener(BindTool.Bind(self.OnCancel, self))
	self.node_list["text_fuhuo_times"]:SetActive(not BeKilledView.IsTire())
	self.node_list["fuhuo_time_slider"]:SetActive(not BeKilledView.IsTire())

	XUI.AddClickEventListener(self.node_list.btn_add_enemy, BindTool.Bind1(self.OnClickAddEnemy, self))

	self.node_list["btn_open_chat"].button:AddClickListener(function()
		ChatWGCtrl.Instance:OpenChatWindow()
	end)

	local btn_show = FuBenWGData.Instance:GetFuhuoBtn()
    for i = 1, 3 do
		self.node_list["btn_get_" .. i].button:AddClickListener(BindTool.Bind(self.OnclickHandler, self, btn_show[i]))
		--self.node_list["btn_get_" .. i].image:LoadSprite(ResPath.GetF2MainUIImage(btn_show[i].img_name))
		--self.node_list["txt_get_"..i].image:LoadSprite(ResPath.GetF2MainUIImage(btn_show[i].view_name2))
    end
    local item_config = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.RESURGENCE_STONE)
    if item_config then
        self.node_list["stone_icon"].image:LoadSprite(ResPath.GetItem(item_config.icon_id))
        self.node_list["text_stone_name"].tmp.text = item_config.name
    end
end

function BeKilledView:OnCancel()
	-- self.node_list["layout_common"]:SetActive(true)
end

function BeKilledView:OnFlush(param_t)
	self:RefreshView(param_t)
	self:FlushFuHuoTire()
	self:FlushFreeFuHuoCount()
end

function BeKilledView:RefreshView(param_t)
	local num = ItemWGData.Instance:GetItemNumInBagById(stone_id)
	self.node_list.rich_tips:SetActive(true)
	self.node_list.tips_1.tmp.text = string.format(Language.Fuhuo.FuHuoTips_1, ToColorStr(self.killer_name, COLOR3B.RED))
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_1.rect)
	self.node_list.tips_2.tmp.text = Language.Fuhuo.FuHuoTips_2 
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_2.rect)

    self.is_world_boss_tired = BossWGData.Instance:IsWorldBossTired() and Scene.Instance:GetSceneType() == SceneType.WorldBoss
	for k, v in pairs(param_t) do
		if k == "all" then
		elseif k == "daojishi" then
			self:DaoJiShi(v.time or 1)
		end
	end

	self.node_list["text_money"].tmp.text = FuhuoWGCtrl.Instance:GetFuhuoGold()
	self.node_list.text_stone.tmp.text = string.format(Language.Fuhuo.Stone, num <= 999 and num or 999)
	-- self.node_list.btn_fuhuo_free_time:SetActive(num <= 0)
	-- self.node_list.btn_stone_stone:SetActive(num > 0)
	self.node_list.btn_add_enemy:SetActive(self.is_show_add_enemy)
end

function BeKilledView:FlushFuHuoTire()
	self.node_list["text_fuhuo_times"]:SetActive(true)
	self.node_list["fuhuo_time_slider"]:SetActive(true)
	-- self.node_list["layout_common"]:SetActive(true)
end

function BeKilledView:FlushFreeFuHuoCount()
	local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	local free_count = RechargeWGData.Instance:GetFreeFuHuoCount()
	local num = ItemWGData.Instance:GetItemNumInBagById(stone_id)
	local is_free_fuhuo = is_active and free_count > 0
	if is_free_fuhuo then
		self.node_list.free_fuhuo_count.tmp.text = string.format(Language.Fuhuo.FreeCountDesc, free_count)
	end
	self.is_free_fuhuo = is_free_fuhuo
	self.node_list.btn_fuhuo_free_time:SetActive(is_free_fuhuo)
	self.node_list.btn_stone_stone:SetActive(not is_free_fuhuo and num > 0)
	self.node_list.btn_fuhuo_yu:SetActive(not is_free_fuhuo and num <= 0)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.btn_operate_root.rect)
end

function BeKilledView:DaoJiShi(time)
	local relive_time = TimeWGCtrl.Instance:GetServerTime() + time
	local str = string.format(Language.Boss.FuHuoTime, time)
	self.node_list["text_fuhuo_times"].tmp.text = time
	self.node_list["fuhuo_time_slider"].image.fillAmount = 1

	XUI.SetButtonEnabled(self.node_list["btn_fuhuo_common"], self.is_world_boss_tired or false)

	CountDownManager.Instance:RemoveCountDown("fuhuo_boss_map")
	CountDownManager.Instance:AddCountDown("fuhuo_boss_map",
		BindTool.Bind1(self.UpdateCountDownTime, self),
		BindTool.Bind(self.CompleteCountDownTime, self, true),
		relive_time, nil, 0.5)
end

-- 倒计时每次循环执行的函数
function BeKilledView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["text_fuhuo_times"] then
		return
	end

    local last_time = math.floor(total_time - elapse_time)
    self.last_time = last_time
	local str = string.format(Language.Boss.FuHuoTime, last_time)
	self.node_list["text_fuhuo_times"].tmp.text = last_time
	self.node_list["fuhuo_common_txt"].tmp.text = string.format(Language.Boss.AutoHuoTime, last_time)
	

	if self.node_list["fuhuo_time_slider"] then
		self.node_list["fuhuo_time_slider"].image.fillAmount = last_time / total_time
	end
end

function BeKilledView:CompleteCountDownTime(is_auto_fuhuo)
	if not self.node_list["btn_fuhuo_common"] then
		return
	end
    self.last_time = 0
	XUI.SetButtonEnabled(self.node_list["btn_fuhuo_common"], true)

	self.node_list["text_fuhuo_times"].tmp.text = ""

	if self.node_list["fuhuo_time_slider"] then
		self.node_list["fuhuo_time_slider"].image.fillAmount = 0
	end

    FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
end

function BeKilledView:SetKillerName(killer_name,killer_level,is_role, plat_type, server_id, uid)
	self.killer_name = killer_name or ''
	self.is_role = is_role or false
	self.killer_level = killer_level or 0

	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()
	self.is_show_add_enemy = false
	self.enemy_plat_type = nil
	self.enemy_server_id = nil
	self.enemy_uid = nil

	if killer_name ~= nil and killer_name ~= "" and not SocietyWGData.Instance:GetIsLimitAddEnemyScene(scene_type) and is_role and plat_type ~= nil and server_id ~= nil and uid ~= nil then
		local my_server_id = RoleWGData.Instance:GetOriginServerId()
		local my_plat_type = RoleWGData.Instance:GetPlatType()
		-- if my_server_id == server_id and my_plat_type == plat_type then
			self.is_show_add_enemy = true
			self.enemy_plat_type = plat_type
			self.enemy_server_id = server_id
			self.enemy_uid = uid
		-- end		
	end

	self:Flush()
end

function BeKilledView:FuhuoCallback()
	if self.fuhuo_common_callback and self.fuhuo_type == FuHuoType.Common then
		self.fuhuo_common_callback()
	elseif self.fuhuo_here_callback and self.fuhuo_type == FuHuoType.Here then
		self.fuhuo_here_callback()
	end
	
	self:Close()
end

function BeKilledView:SetFuhuoCallback(common_callback,here_callback)
	self.fuhuo_common_callback = common_callback
	self.fuhuo_here_callback = here_callback
end

function BeKilledView.OnCLickEquip()
	ViewManager.Instance:Open(GuideModuleName.Equipment,TabIndex.equipment_strength)
end

function BeKilledView.OnCLickRide()
	ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_mount)
end

local TireList = {
	[SceneType.WorldBoss] = true,
	[SceneType.KF_BOSS] = true,
	[SceneType.SHENGYU_BOSS] = true,
	[SceneType.MJ_BOSS] = true,
}

function BeKilledView.IsTire()
	local scene_type = Scene.Instance:GetSceneType()
	local relive_times, tire_end_time = BossWGData.Instance:GetBossReliveTire()
	local other_cfg = BossWGData.Instance:GetOtherCfg()
	if relive_times >= other_cfg.relive_tire_max_level and tire_end_time > TimeWGCtrl.Instance:GetServerTime() and TireList[scene_type] then
		return true
	else
		return false
	end
end

function BeKilledView:CommonFuHuo()
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function BeKilledView:OnClickFuhuoCommon()
	local scene_type = Scene.Instance:GetSceneType()
	local relive_times, tire_end_time = BossWGData.Instance:GetBossReliveTire()
	-- if scene_type == SceneType.KF_BOSS
    -- local other = ConfigManager.Instance:GetAutoConfig("cross_boss_auto").other[1]
    if scene_type == SceneType.WorldBoss then
        if self.is_world_boss_tired and self.last_time > 0 then
            local data = {}
            data.time = self.last_time or 60
            BossWGCtrl.Instance:OpenSpecialFuhuoView(data)
            return
        end
    end
	local other_cfg = BossWGData.Instance:GetOtherCfg()
	if relive_times >= other_cfg.relive_tire_max_level and tire_end_time > TimeWGCtrl.Instance:GetServerTime() and TireList[scene_type] then
		self:OnCancel()
	else
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
		self.fuhuo_type = FuHuoType.Common
	end

end

function BeKilledView:OnClickFuhuoHere()
	if self.is_free_fuhuo then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.TOZI_FREE)
	else
		local gold = FuhuoWGCtrl.Instance:GetFuhuoGold()
		local num = ItemWGData.Instance:GetItemNumInBagById(stone_id)
		if num > 0 then
			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_STUFF, 0, -1)
		else
			if not RoleWGData.Instance:GetIsEnoughAllGold(gold) then
				VipWGCtrl.Instance:OpenTipNoGold()
			end
			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_ICON, 0, -1)
		end
	end
	
	self.fuhuo_type = FuHuoType.Here

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.WorldBoss or scene_type == SceneType.XianJie_Boss or scene_type == SceneType.KF_BOSS or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
		return
	end

	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

	-- 原地复活后需要默认挂机状态。增加攻击机制，优先级顺序如下
	-- 当前我攻击的玩家
	-- 当前攻击我的玩家（最近）
	-- 最近可攻击的玩家
	-- 攻击boss（怪物）

	local select_obj = nil
	if GuajiCache.target_obj and GuajiCache.target_obj:IsRole() then
		select_obj = GuajiCache.target_obj
	end
	GuajiCache.target_obj = nil
	MoveCache.target_obj = nil

	if not select_obj then
		select_obj = ReviveWGData.Instance:GetBeHitData()
	end

	if select_obj and select_obj:IsRole() and Scene.Instance:IsEnemy(select_obj) then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
	end
end

function BeKilledView:OnclickHandler(param_t)
	ViewManager.Instance:Open(param_t.view_name, param_t.tab_index)
end

function BeKilledView:OnClickAddEnemy()
	if not self.is_show_add_enemy then
		return
	end

	if self.enemy_plat_type == nil or self.enemy_server_id == nil or self.enemy_uid == nil then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()
	local is_enemy_ed = SocietyWGData.Instance:CheckisRoleEnemy(self.enemy_plat_type, self.enemy_uid)
	if is_enemy_ed then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.AddEnemyError)
		return
	end

	if not SocietyWGData.Instance:GetIsLimitAddEnemyScene(scene_type) and self.is_role then
		local my_server_id = RoleWGData.Instance:GetOriginServerId()
		local my_plat_type = RoleWGData.Instance:GetPlatType()
		SocietyWGCtrl.Instance:SendAddEnemy(self.enemy_uid, self.enemy_plat_type)	
	end
end

function BeKilledView:GetUseFuHuoType()
	return self.fuhuo_type
end