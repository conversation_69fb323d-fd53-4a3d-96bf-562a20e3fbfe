FortunecatTargetView = FortunecatTargetView or BaseClass(SafeBaseView)

function FortunecatTargetView:__init()
	self.view_name = "FortunecatTargetView"
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/fortunecat_ui_prefab", "layout_fortune_cat_target")
end

function FortunecatTargetView:ReleaseCallBack()
	if self.target_list then
		self.target_list:DeleteMe()
		self.target_list = nil
	end

end

function FortunecatTargetView:LoadCallBack()
	self.target_list = AsyncListView.New(FortuneCatTargetCell, self.node_list.target_ist)

    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))

end

function FortunecatTargetView:OnFlush()
	self:FlushTargetList()
end

function FortunecatTargetView:FlushTargetList()
	local target_data_list = FortuneCatWGData.Instance:GetTargetDataList()

	local temp_list = {}
	for i, v in ipairs(target_data_list) do
		temp_list[i] = v
	end
	table.sort(temp_list, function (a,b)
		local recharge_value = FortuneCatWGData.Instance:GetFortuneCatRechargeValue()
		local a_weight = a.min_count
		local b_weight = b.min_count
		if recharge_value >= a.total_target then
			a_weight = a_weight + 10000
		end
		if recharge_value >= b.total_target then
			b_weight = b_weight + 10000
		end
		return a_weight < b_weight
	end)

	self.target_list:SetDataList(temp_list)
end

---------------------------------FortuneCatTargetCell---------------------------------
FortuneCatTargetCell = FortuneCatTargetCell or BaseClass(BaseRender)

function FortuneCatTargetCell:__init()
	self.cell_item = ItemCell.New(self.node_list.cell_node)
end

function FortuneCatTargetCell:__delete()
	if self.cell_item then

		self.cell_item:DeleteMe()
		self.cell_item = nil
	end
end

function FortuneCatTargetCell:OnFlush()
	if not self.data then
		return
	end

	local recharge_value = FortuneCatWGData.Instance:GetFortuneCatRechargeValue()

	local color =  recharge_value >= self.data.total_target  and COLOR3B.L_GREEN or COLOR3B.L_RED
	local min_value = math.min(recharge_value,self.data.total_target)
	local target = ToColorStr(string.format("%s/%s",min_value,self.data.total_target),color)
	self.node_list.text_target.text.text = string.format(Language.FortuneCat.TargetStr,target)
	self.node_list.text_desc.text.text = Language.FortuneCat.TargetReward

	self.node_list.img_reciver:SetActive(recharge_value >= self.data.total_target)
	local item_id = FortuneCatWGData.Instance:GeOtherCfgByKey("show_item")
	self.cell_item:SetData({item_id = item_id})
end
