TweenManager = TweenManager or BaseClass()
require("gameui/common/tween_wg_data")

local TypeTween = 
{
	[TweenType.Alpha] = typeof(UGUITweenAlpha),
	[TweenType.Height] = typeof(UGUITweenHeight),
	[TweenType.Position] = typeof(UGUITweenPosition),
	[TweenType.Rotation] = typeof(UGUITweenRotation),
	[TweenType.Scale] = typeof(UGUITweenScale),
	[TweenType.Width] = typeof(UGUITweenWidth),
	[TweenType.SizeDelta] = typeof(UGUITweenSizeDelta),
}

local TweenPlayMethod =
{
	[TweenLuaMethod.Linear] = TweenMethod.Linear,
	[TweenLuaMethod.EaseIn] = TweenMethod.EaseIn,
	[TweenLuaMethod.EaseOut] = TweenMethod.EaseOut,
	[TweenLuaMethod.EaseInOut] = TweenMethod.EaseInOut,
	[TweenLuaMethod.BounceIn] = TweenMethod.BounceIn,
	[TweenLuaMethod.BounceOut] = TweenMethod.BounceOut,
}

function TweenManager:__init()
	if nil ~= TweenManager.Instance then
		print_error("[TweenManager]:Attempt to create singleton twice!")
	end
	TweenManager.Instance = self

	self.tween_data = TweenWGData.New()

	self.is_open_tween = true
end

function TweenManager:__delete()
	TweenManager.Instance = nil
	self.tween_data:DeleteMe()
	self.tween_data = nil
end

-- 设置界面动画是否开启(有的界面切换为了不突兀需要临时禁用)
function TweenManager:SetIsOpenTween(is_open)
	self.is_open_tween = is_open
end

-- 执行添加动画
function TweenManager:ExecuteViewTween(view_name, sub_index, node_list)
	if not self.is_open_tween then
		return
	end
	if not node_list then
		return
	end

	local node_cfg = self.tween_data:GetModuleNodeCfg(view_name, sub_index)
	if (not node_cfg) or (not node_cfg.node_tween) or type(node_cfg.node_tween) ~= "string" then
		return
	end

	local node_tween_list = Split(node_cfg.node_tween, "|")
	for _, node_tween_str in ipairs(node_tween_list) do
		self:AddTween(node_tween_str, node_list)
	end
end

-- 添加动画
function TweenManager:AddTween(node_tween_str, node_list)
	if (not node_tween_str) or type(node_tween_str) ~= "string" then
		return 
	end

	local tween_list = Split(node_tween_str, ",")
	if #tween_list < 2 then
		return
	end

	local node_name = tween_list[1]
	if not node_list[node_name] then
		return
	end

	for i = 2, #tween_list do
		local tween_id = tonumber(tween_list[i]) or 0 
		local tween_cfg = self.tween_data:GetTweenCfg(tween_id, node_list[node_name])
		if tween_cfg then
			self:CheckAddTween(node_list[node_name], tween_cfg)
		end
	end
end

-- 添加对应动画组件
function TweenManager:CheckAddTween(node, tween_cfg)
	local type_tween = TypeTween[tween_cfg.tween_type]
	local tween_cam = node:GetComponent(type_tween)

	if not IsNil(tween_cam) then
		tween_cam:ResetToBeginning()
		return
	end

	tween_cam = node.gameObject:AddComponent(type_tween)
	if type_tween == typeof(UGUITweenPosition) then
		tween_cam.anchor = true
	end

	tween_cam.from = tween_cfg.form
	tween_cam.to = tween_cfg.to
	tween_cam.duration = tween_cfg.tween_time
	tween_cam.disable_finish = false
	tween_cam.enabled_play = false

	tween_cam.delay = tween_cfg.initinte_time
	tween_cam.method = TweenPlayMethod[tween_cfg.tween_method]

	-- 设置初始位置
	self:SetNodeInitParams(node, tween_cam, type_tween)
end

-- 设置初始位置
function TweenManager:SetNodeInitParams(node, tween_cam, type_tween)
	if type_tween == typeof(UGUITweenAlpha) then
		node.canvas_group.alpha = tween_cam.from
	elseif type_tween == typeof(UGUITweenHeight) then
		node.rect.sizeDelta = Vector2(node.canvas_group.rect.sizeDelta.x, tween_cam.from)
	elseif type_tween == typeof(UGUITweenPosition) then
		node.transform.anchoredPosition = tween_cam.from
	elseif type_tween == typeof(UGUITweenRotation) then
		node.rect.rotation = Quaternion.Euler(tween_cam.from.x, tween_cam.from.y, tween_cam.from.z)
	elseif type_tween == typeof(UGUITweenScale) then
		node.transform.localScale = tween_cam.from
	elseif type_tween == typeof(UGUITweenWidth) then
		node.rect.sizeDelta = Vector2(tween_cam.from, node.canvas_group.rect.sizeDelta.y)
	elseif type_tween == typeof(UGUITweenSizeDelta) then
		node.rect.sizeDelta = Vector2(tween_cam.from.x, tween_cam.from.y)
	end
end