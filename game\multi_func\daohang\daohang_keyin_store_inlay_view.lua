DaoHangKeYinInlayStoreView = DaoHangKeYinInlayStoreView or BaseClass(SafeBaseView)

function DaoHangKeYinInlayStoreView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -10), sizeDelta = Vector2(600, 604)})
	self:AddViewResource(0, "uis/view/multi_function_ui/daohang_prefab", "layout_daohang_baishi_select")
end

function DaoHangKeYinInlayStoreView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Charm.DaoHangKeYinStoreTitle

	if not self.stone_list_view then
		self.stone_list_view = AsyncListView.New(DaoHangSelectBaoShiItemRender, self.node_list["ph_select_baoshi_list_view"])
		self.stone_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectItemHand<PERSON>, self))
	end
end

function DaoHangKeYinInlayStoreView:ReleaseCallBack()
	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end

	self.change_data_list = nil
	self.select_slot = nil
end

function DaoHangKeYinInlayStoreView:SetData(slot ,data_list)
	self.select_slot = slot
	self.change_data_list = data_list
end

function DaoHangKeYinInlayStoreView:OnFlush()
	if self.stone_list_view and self.change_data_list then
		self.stone_list_view:SetDataList(self.change_data_list)
		self.stone_list_view:CancelSelect()
	end
end

function DaoHangKeYinInlayStoreView:OnSelectItemHandler(item, cell_index, is_default, is_click)
	if not is_click or nil == item or nil == item.data then
		return
	end

	MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.USE_STONE, self.select_slot, item.data.index)
	self:Close()
end

------------------------------------------DaoHangSelectBaoShiItemRender-----------------------------------------
DaoHangSelectBaoShiItemRender = DaoHangSelectBaoShiItemRender or BaseClass(BaseRender)

function DaoHangSelectBaoShiItemRender:LoadCallBack()
	if not self.arrow_tweener then
		local tween_arrow = self.node_list["img_remind"]
		RectTransform.SetAnchoredPositionXY(tween_arrow.rect, -8, -5)
		self.arrow_tweener = tween_arrow.gameObject.transform:DOAnchorPosY(0, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end

	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["ph_item"])
	end
end

function DaoHangSelectBaoShiItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function DaoHangSelectBaoShiItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetData(self.data)
	local store_cfg = MultiFunctionWGData.Instance:GetDaoHangKeLingStoreCfg(self.data.item_id)

	if not IsEmptyTable(store_cfg) then
		local attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(store_cfg, "attr_id", "attr_value")

		if not IsEmptyTable(attr_data) then
			for i = 1, 4 do
				local data = attr_data[i]
			    local has_data = not IsEmptyTable(data)

				if has_data then
					self.node_list["lbl_attr" .. i].text.text = data.attr_name .. "  " .. ToColorStr(data.value_str, COLOR3B.GREEN)
				end
				
				self.node_list["lbl_attr" .. i]:CustomSetActive(has_data)
			end
		end
	end
end