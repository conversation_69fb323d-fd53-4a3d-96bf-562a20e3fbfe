---------------------------------------
-- 升级引导
-----------------------------------------
ExpGuideWGView = ExpGuideWGView or BaseClass(SafeBaseView)

function ExpGuideWGView:__init()
    self.view_style = ViewStyle.Half
    self:SetMaskBg(true)

    self:AddViewResource(0, "uis/view/exp_guide_ui_prefab", "exp_guide_view")

    self.cur_select_index = 1
    self.show_fun_list = {}
    self.icon_ins_list = {}
    self.show_otem_cell = nil
end

function ExpGuideWGView:LoadCallBack()
    self.node_list.goto_btn.button:AddClickListener(BindTool.Bind(self.ClickGotoBtn, self))
    local num = self.node_list.icons.transform.childCount
    for index = 1, num do
        local obj = self.node_list.icons:FindObj(string.format("icon_%s", index))
        local cell = IconCell.New(obj)
        cell:SetClickCallBack(BindTool.Bind2(self.OnClickCellCallback, self, index))
        table.insert(self.icon_ins_list, cell)
    end
end

function ExpGuideWGView:ReleaseCallBack()
    if self.icon_ins_list then
        for _, value in pairs(self.icon_ins_list) do
            value:DeleteMe()
        end
        self.icon_ins_list = {}
    end

    if self.show_otem_cell then
        self.show_otem_cell:DeleteMe()
        self.show_otem_cell = nil
    end
end

function ExpGuideWGView:ClickGotoBtn()
    local data = self.show_fun_list[self.cur_select_index]
    if not data then
        return
    end

    if data.id == 188 then
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, true)
        TaskWGData.Instance:DoShangJinTask()
    elseif data.id == 2021 then
        if NewTeamWGData.Instance:GetIsMatching() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
            return
        end
        if YunbiaoWGData.Instance:GetIsHuShong() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
            return
        end
        TaskGuide.Instance:CanAutoAllTask(false)
        ActIvityHallWGCtrl.Instance:DoHuSong()
    else
        local way_cfg = ExpGuideWGData.Instance:GetWay(data.id)
        if not way_cfg then
            return
        end
        ViewManager.Instance:OpenByCfg(way_cfg.open_panel)
    end
    ExpGuideWGCtrl.Instance:CloseExpGuideView()
end

function ExpGuideWGView:OnClickCellCallback(index)
    self.cur_select_index = index
    local data = self.show_fun_list[self.cur_select_index]
    if not data then
        return
    end

    if data.id == 188 then
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, true)
        TaskWGData.Instance:DoShangJinTask()
    elseif data.id == 2021 then
        if NewTeamWGData.Instance:GetIsMatching() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
            return
        end
        if YunbiaoWGData.Instance:GetIsHuShong() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
            return
        end
        TaskGuide.Instance:CanAutoAllTask(false)
        ActIvityHallWGCtrl.Instance:DoHuSong()
    else
        local way_cfg = ExpGuideWGData.Instance:GetWay(data.id)
        if not way_cfg then
            return
        end
        ViewManager.Instance:OpenByCfg(way_cfg.open_panel)
    end

    self:OnFlush()
    ExpGuideWGCtrl.Instance:CloseExpGuideView()
end

function ExpGuideWGView:OnFlush()
    self.show_fun_list = ExpGuideWGData.Instance:GetShowFunList()
    
    for index, cell in ipairs(self.icon_ins_list) do
        local data = self.show_fun_list[index]
        cell:SetVisible(data and true or false)
        if data then
            cell:SetData(data)
            cell:SelectStatus(self.cur_select_index == index)
        end
    end

    self:FlushExpAndLev()
end

function ExpGuideWGView:FlushExpAndLev()
    local data = self.show_fun_list[self.cur_select_index]
    if not data then
        return
    end

    -- 道具
    if not self.show_otem_cell then
        self.show_otem_cell = ItemCell.New(self.node_list.item)
    end
    if data.item_id then
        self.node_list.item:SetActive(true)
        self.show_otem_cell:SetData({item_id = data.item_id})
    else
        self.node_list.item:SetActive(false)
    end
    
    -- 经验
    local total_num = data.count == 0 and 1 or data.count
    local exp = ExpGuideWGData.Instance:GetExp(data.id)
    self.node_list.exp_text.tmp.text = string.format(Language.ExpGuide.ExpTip, CommonDataManager.ConverExp(exp))
    -- 预计升级
    local role_lev = GameVoManager.Instance:GetMainRoleVo().level
    local role_exp_cfg = RoleWGData.Instance.GetRoleExpCfgByLv(role_lev)
    local cur_need_exp = role_exp_cfg and role_exp_cfg.exp or 0
    local up_lev = 0
    if cur_need_exp > 0 then
        up_lev = exp/cur_need_exp
    end
    self.node_list.lev_text.tmp.text = string.format(Language.ExpGuide.LevTip, up_lev)
end


--------------------------------------------------------------------------------------------------------------------
-- 功能图标
IconCell = IconCell or BaseClass(BaseRender)


function IconCell:OnFlush()
    local model = ExpGuideWGData.Instance
    local data = self:GetData()
    local way_cfg = model:GetWay(data.id)
    if not way_cfg then
        return
    end

    if data.icon then
        self.node_list.icon.image:LoadSprite(ResPath.GetExpGuideImg("a3_sjyd_icom_"..data.icon))
    end
    self.node_list.name_text.tmp.text = way_cfg.discription
    -- 次数
    local total_num = data.count or 1
    local is_show_num = total_num > 1
    self.node_list.num_node:SetActive(is_show_num)
    if is_show_num then
        local use_num = model:GetNum(data)
        local total_num2 = model:GetAllNum(data)
        local real_num = total_num2 > 0 and total_num2 or total_num
        self.node_list.num.tmp.text = string.format("%s/%s", real_num - use_num, real_num)
    end

end
function IconCell:SelectStatus(status)
    self.node_list.selected:SetActive(status)
end