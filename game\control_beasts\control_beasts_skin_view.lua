ControlBeastsSkinView = ControlBeastsSkinView or BaseClass(SafeBaseView)

function ControlBeastsSkinView:__init()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	local common_bundle = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/control_beasts_ui_prefab"
	self:AddViewResource(0, bundle_name, "layout_beasts_skin")
	self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")

	self:SetTabShowUIScene(0, { type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BEASTS_BASE_INFO })
end

function ControlBeastsSkinView:__delete()

end

function ControlBeastsSkinView:LoadCallBack()
	-- 模块名称
	self.node_list["title_view_name"].text.text = Language.ContralBeasts.TitleName14
	-- 创建货币栏
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.beasts_skin_list then
		self.beasts_skin_list = AsyncFancyAnimView.New(BeastsSkinItem, self.node_list.beasts_skin_list)
		self.beasts_skin_list:SetSelectCallBack(BindTool.Bind(self.OnSkinItemSelectCB, self))
	end

	if self.skin_attr_list == nil then
		self.skin_attr_list = {}
		local node_num = self.node_list["attr_content"].transform.childCount
		for i = 1, node_num do
			self.skin_attr_list[i] = CommonAddAttrRender.New(self.node_list["attr_content"]:FindObj("attr_" .. i))
			self.skin_attr_list[i]:SetAttrNameNeedSpace(true)
			self.skin_attr_list[i]:SetAttrArrowTween(false)
		end
	end

	if not self.beast_model then
		self.beast_model = RoleModel.New()
		self.beast_model:SetUISceneModel(self.node_list["display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.beast_model)
	end

	if not self.star_list then
		self.star_list = {}
		for i = 1, 5 do
			self.star_list[i] = self.node_list["star" .. i]
		end
	end

	if not self.consume_item then
		self.consume_item = ItemCell.New(self.node_list["consume_item_root"])
	end

	XUI.AddClickEventListener(self.node_list["btn_skin_active"], BindTool.Bind(self.OnClickSkinActiveBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_wear_skin"], BindTool.Bind(self.OnClickSkinUse, self))
end

function ControlBeastsSkinView:ReleaseCallBack()
	-- 销毁 MoneyBar
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.beasts_skin_list then
		self.beasts_skin_list:DeleteMe()
		self.beasts_skin_list = nil
	end

	if self.skin_attr_list then
		for k, v in pairs(self.skin_attr_list) do
			v:DeleteMe()
		end
		self.skin_attr_list = nil
	end

	if self.beast_model then
		self.beast_model:DeleteMe()
		self.beast_model = nil
	end

	if self.consume_item then
		self.consume_item:DeleteMe()
		self.consume_item = nil
	end

	self.star_list = nil
end

function ControlBeastsSkinView:SetDataAndOpen(beast_data)
	self.cur_beast_data = beast_data
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function ControlBeastsSkinView:OpenCallBack()

end

function ControlBeastsSkinView:CloseCallBack()
	self.beast_model_res_id = nil
	self.cur_beast_data = nil
	self.cur_sel_index = nil
	self.cur_sel_skin_data = nil
	self.cur_beast_type = nil
end

function ControlBeastsSkinView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.item_id then -- 外部跳转
				local skin_cfg = ControlBeastsWGData.Instance:GetBeastsSkinCfgByItemId(v.item_id)
				self.cur_beast_type = (skin_cfg or {}).beast_type
				self.jump_skin_seq = (skin_cfg or {}).skin_seq
				self.cur_beast_data = ControlBeastsWGData.Instance:FindTargetBeastData(self.cur_beast_type)
			elseif self.cur_beast_data then
				self.cur_beast_type = ControlBeastsWGData.Instance:GetBeastTypeByBeastId(self.cur_beast_data.item_id)
			end
			self:FlushCultureBeastMessage()
			self:FlushSkinList()
		end
	end
end

function ControlBeastsSkinView:FlushSkinList()
	if not self.cur_beast_type then
		return
	end

	local skin_cfg_list = ControlBeastsWGData.Instance:GetSkinListByBeastsType(self.cur_beast_type)
	local skin_list = {}

	local default_is_using = true
	if self.cur_beast_data then
		default_is_using = self.cur_beast_data.server_data.use_skin == -1
	end
	skin_list[1] = {
		is_default = true,
		is_using = default_is_using,
		beast_type = self.cur_beast_type,
	}

	for i, v in ipairs(skin_cfg_list) do
		local data = {}
		data.is_default = false
		data.beast_type = self.cur_beast_type
		data.cfg = v
		data.is_using = false
		if self.cur_beast_data then
			data.is_using = self.cur_beast_data.server_data.use_skin == v.skin_seq
		end
		table.insert(skin_list, data)
	end

	self.beasts_skin_list:SetDataList(skin_list)
	-- 跳转
	if self.jump_skin_seq then
		for i, v in ipairs(skin_list) do
			if v.cfg and self.jump_skin_seq == v.cfg.skin_seq then
				self.beasts_skin_list:SelectCell(i)
				break
			end
		end
		self.jump_skin_seq = nil
	else
		-- 红点->穿戴中->当前默认
		local jump_index
		for i, v in ipairs(skin_cfg_list) do
			local remind = ControlBeastsWGData.Instance:GetSingleBeastSkinRed(v.skin_seq)
			if remind then
				jump_index = i + 1
				break
			end
		end
		if not self.cur_sel_index and not jump_index and self.cur_beast_data then
			for i, v in ipairs(skin_cfg_list) do
				if self.cur_beast_data.server_data.use_skin == v.skin_seq then
					jump_index = i + 1
					break
				end
			end
		end
		if not jump_index then
			jump_index = self.cur_sel_index or 1
		end
		self.beasts_skin_list:SelectCell(jump_index)
	end
end

-- 刷新中间的模型星级和等级
function ControlBeastsSkinView:FlushCultureBeastMessage()
	if not self.cur_beast_type then
		return
	end
	local beast_cfg, server_data
	if self.cur_beast_data then
		server_data = self.cur_beast_data.server_data
		beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.cur_beast_data.item_id)
	else
		beast_cfg = ControlBeastsWGData.Instance:GetBeatsPreviewCfgByBeastType(self.cur_beast_type)
	end

	if beast_cfg then
		local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
		self.node_list["active_jingshen_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["active_jingshen_img"].image:SetNativeSize()
		end)

		if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
			bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
			self.node_list["active_jingshen_img"]:ChangeAsset(bundle, asset)
		end

		local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
		local no_have_star = "a3_ty_xx_zc0"
		for k,v in pairs(self.star_list) do
			v:CustomSetActive(star_res_list[k] ~= no_have_star)
			v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
		end

		bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
		self.node_list["beasts_element_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["beasts_element_img"].image:SetNativeSize()
		end)
		self.node_list["beasts_name"].text.text = beast_cfg.beast_name
		self.node_list["beasts_level"].text.text = server_data and server_data.beast_level or 1
	end
end

function ControlBeastsSkinView:FlushSkinInfoPart()
	if not self.cur_sel_skin_data then
		return
	end

	local server_data = self.cur_beast_data and self.cur_beast_data.server_data or nil
	if self.cur_sel_skin_data.is_default then -- 默认皮肤
		self.node_list["attr_panel"]:SetActive(false)
		self.node_list["no_attr_text"]:SetActive(true)
		self.node_list["no_active_part"]:SetActive(false)
		self.node_list["active_part"]:SetActive(true)
		local is_using = true
		if server_data then
			is_using = server_data.use_skin == -1
		end
		self.node_list["skin_wearing_flag"]:SetActive(is_using)
		self.node_list["btn_wear_skin"]:SetActive(not is_using)
	else
		
		local skin_seq = self.cur_sel_skin_data.cfg.skin_seq
		local skin_level = ControlBeastsWGData.Instance:GetBeastsSkinData(skin_seq)
		
		-- 属性部分
		local attr_list = ControlBeastsWGData.Instance:GetSkinAttrList(skin_seq, skin_level)
		local no_attr = IsEmptyTable(attr_list)
		self.node_list["attr_panel"]:SetActive(not no_attr)
		for k, v in pairs(self.skin_attr_list) do
			v:SetData(attr_list[k])
		end
		self.node_list["no_attr_text"]:SetActive(no_attr)

		-- 激活部分
		local is_using = false
		if server_data then
			is_using = server_data.use_skin == self.cur_sel_skin_data.cfg.skin_seq
		end
		local is_actice = skin_level > 0
		self.node_list["no_active_part"]:SetActive(not is_actice)
		self.node_list["active_part"]:SetActive(is_actice)
		if is_actice then
			self.node_list["skin_wearing_flag"]:SetActive(is_using)
			self.node_list["btn_wear_skin"]:SetActive(not is_using)
		else
			local skin_level_cfg = ControlBeastsWGData.Instance:GetSkinLevelCfg(skin_seq, 1) -- 1级为激活
			self.consume_item:SetData({ item_id = skin_level_cfg.cost_item_id })
			local had_num = ItemWGData.Instance:GetItemNumInBagById(skin_level_cfg.cost_item_id)
			local need_num = skin_level_cfg.cost_item_num
			local color = had_num >= need_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
			self.node_list["txt_consume_num"].tmp.text = string.format(Language.ContralBeasts.SkinActiveNum, color, had_num, need_num) 
			self.node_list["remind_skin_active"]:SetActive(had_num >= need_num)
		end
	end
end

-- 刷新模型
function ControlBeastsSkinView:FlushBeastSkinModel()
	if not self.cur_sel_skin_data or not self.cur_beast_type then
		return
	end

	local res_id
	if self.cur_sel_skin_data.is_default then -- 默认皮肤
		if self.cur_beast_data then
			res_id = ControlBeastsWGData.Instance:GetBeastModelResId(self.cur_beast_data.item_id)
		else
			local beast_cfg = ControlBeastsWGData.Instance:GetBeatsPreviewCfgByBeastType(self.cur_beast_type)
			if beast_cfg then
				res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beast_cfg.beast_id)
			end
		end
	else
		res_id = ControlBeastsWGData.Instance:GetBeastModelSkinResId(self.cur_sel_skin_data.cfg.skin_seq)
	end

	if not res_id then return end
	if self.beast_model_res_id ~= res_id then
		self.beast_model_res_id = res_id
		local bundle, asset = ResPath.GetBeastsModel(res_id)
		self.beast_model:SetMainAsset(bundle, asset)
		self.beast_model:SetUSAdjustmentNodeLocalPosition(-0.6, 0, 0)
		self.beast_model:PlayRoleAction(SceneObjAnimator.Rest)
	end
end

function ControlBeastsSkinView:OnSkinItemSelectCB(cell, index)
	self.cur_sel_index = index
	self.cur_sel_skin_data = cell:GetData()
	self:FlushSkinInfoPart()
	self:FlushBeastSkinModel()
end

-- 激活按钮点击
function ControlBeastsSkinView:OnClickSkinActiveBtn()
	if not self.cur_sel_skin_data then
		return
	end

	local skin_seq = self.cur_sel_skin_data.cfg.skin_seq
	local skin_level = ControlBeastsWGData.Instance:GetBeastsSkinData(skin_seq)
	if skin_level > 1 then
		return
	end

	local skin_level_cfg = ControlBeastsWGData.Instance:GetSkinLevelCfg(skin_seq, 1) -- 1级为激活
	local had_num = ItemWGData.Instance:GetItemNumInBagById(skin_level_cfg.cost_item_id)
	local need_num = skin_level_cfg.cost_item_num
	if had_num >= need_num then
		ControlBeastsWGCtrl.Instance:SendOperateTypeBeastSkinUpLevel(skin_seq)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = skin_level_cfg.cost_item_id})
	end
end

-- 使用按钮点击
function ControlBeastsSkinView:OnClickSkinUse()
	if not self.cur_sel_skin_data then
		return
	end

	if not self.cur_beast_data then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ContralBeasts.HaveNotBeasts)
	else
		local skin_seq = -1
		if not self.cur_sel_skin_data.is_default then
			skin_seq = self.cur_sel_skin_data.cfg.skin_seq
		end
		local server_bag_id = self.cur_beast_data.bag_id - 1
		ControlBeastsWGCtrl.Instance:SendOperateTypeBeastSkinUse(server_bag_id, skin_seq)
	end
end

-- 升级成功或突破成功特效
function ControlBeastsSkinView:SkinOperateShowEffect(ui_effect_type, is_center)
	local node_root = self.node_list["operate_effect_root"]
	if is_center then
		node_root = self.node_list["layout_a3_common_top_panel"]
	end
	TipWGCtrl.Instance:ShowEffect({ effect_type = ui_effect_type, is_success = true, pos = Vector2(0, 0), parent_node = node_root })
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

----------------------------------
-- 皮肤Item
----------------------------------
BeastsSkinItem = BeastsSkinItem or BaseClass(BaseRender)
function BeastsSkinItem:LoadCallBack()

end

function BeastsSkinItem:ReleaseCallBack()

end

function BeastsSkinItem:OnFlush()
	if not self.data then
		return
	end
	
	local skin_image, skin_color
	if self.data.is_default then
		self.node_list["skin_name"].tmp.text = Language.ContralBeasts.DefaultSkin
		self.node_list["img_locked"]:SetActive(false)
		self.node_list["img_red"]:SetActive(false)
		skin_image = "a3_hs_default_skin_" ..  self.data.beast_type
		skin_color = 1
	else
		local cfg = self.data.cfg
		self.node_list["skin_name"].tmp.text = cfg.skin_name
		local skin_server_data = ControlBeastsWGData.Instance:GetBeastsSkinData(cfg.skin_seq)
		self.node_list["img_locked"]:SetActive(skin_server_data == 0)
		local skin_red = ControlBeastsWGData.Instance:GetSingleBeastSkinRed(cfg.skin_seq)
		self.node_list["img_red"]:SetActive(skin_red)
		skin_image = "a3_hs_skin_" .. cfg.skin_seq
		skin_color = cfg.skin_color
	end
	local bundle, asset = ResPath.GetNoPackPNG(skin_image)
	self.node_list["img_skin_icon"].image:LoadSprite(bundle, asset, function()
		self.node_list["img_skin_icon"].image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetControlBeastsImg("a3_hszb_pz" .. skin_color)
	self.node_list["img_quality_flag"].image:LoadSprite(bundle, asset)
	self.node_list["txt_quality"].tmp.text = Language.ContralBeasts.SkinQuality[skin_color]
	self.node_list["wearing_flag"]:SetActive(self.data.is_using)
end

-- 选择状态改变
function BeastsSkinItem:OnSelectChange(is_select)
	self.node_list["img_selected"]:SetActive(is_select)
end
