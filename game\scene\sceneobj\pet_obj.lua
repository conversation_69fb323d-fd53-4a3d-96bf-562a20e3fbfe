Pet = Pet or BaseClass(Character)

function Pet:__init(vo)
	self.obj_type = SceneObjType.Pet
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.Pet
	self.followui_hide_when_self_hide = true
	self.shadow_hide_when_self_hide = true
	self.owner_obj_id = vo.owner_objid
	self.res_id = 20113001 --TODO
	self.head_id = 0
	self.general_resid = 0
	self.is_skill_reading = false
	self.magic_skill_id = nil
	self.magic_time_record = nil
	self.bundle_name = ""
	self.asset_name = ""

	if nil ~= PetWGData.Instance then
		local res_id = PetWGData.Instance:GetPetResByPetId(self.vo.pet_id)
		if res_id and res_id ~= "" then
			self.res_id = res_id
		end
	end

	self.totem_name = self.vo.name or ""
	self.tower_attack_range = nil
	self.deputy_attack_target = nil

    self.record_time = self.now_time or 0
    self.is_talk = false
    self.can_bubble = false

	self.fight_cache = nil
end

function Pet:__delete()
	if self.time_coundown then
		GlobalTimerQuest:CancelQuest(self.time_coundown)
		self.time_coundown = nil
	end
	if self.tower_attack_range then
		self.tower_attack_range:DeleteMe()
		 self.tower_attack_range = nil
	end

    if self.bobble_timer_quest then
        GlobalTimerQuest:CancelQuest(self.bobble_timer_quest)
        self.bobble_timer_quest = nil
    end

	if nil ~= self.deputy_attack_target then
		self.deputy_attack_target = nil
	end

	self:ClearFightCache()

	if nil ~= self.owner_obj and self.owner_obj:IsRole() then
		self.owner_obj:RemovePet(self)
	end
	self.owner_obj = nil

	ReuseableHandleManager.Instance:ReleaseShieldHandle(self.effect_handle)
	self.effect_handle = nil
end

local start_playdead = nil
local DecayMounstCount = 0
function Pet:DeleteDrawObj()
	if not self:IsRealDead() or DecayMounstCount > 10 then
		Character.DeleteDrawObj(self)
		return
	end

	if nil ~= self.draw_obj then
		local draw_obj = self.draw_obj
		local id = self:GetObjId()
		self.draw_obj = nil
		if self.res_id ~= 3030001 then
			DecayMounstCount = DecayMounstCount + 1
			start_playdead = Status.NowTime
			draw_obj:PlayDead(self.dietype, function()
				DecayMounstCount = DecayMounstCount - 1
				draw_obj:DeleteMe()
			end,nil,id)
		else
			draw_obj:DeleteMe()
		end
	end
end

function Pet:InitInfo()
	Character.InitInfo(self)
	self:GetFollowUi()
	self:ReloadUIName()
	self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("pet", self.res_id))

	self.effect_handle = ReuseableHandleManager.Instance:GetShieldHandle(RolePartEffectShieldHandle, self, ShieldObjType.PetEffect, SceneObjPart.Main)
    self.effect_handle:CreateShieldHandle()
    self:UpdateEffectVisible()
end

-- 重写CheckModleScale方法 避免放大缩小给限制了
function Pet:CheckModleScale()

end

function Pet:InitAppearance()
	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId() or 0
	self.load_priority = 3
	if self.obj_scale ~= nil then
		local transform = self.draw_obj:GetRoot().transform
		transform.localScale = Vector3(self.obj_scale, self.obj_scale, self.obj_scale)
	end

	local bundle, asset = "", ""
	bundle, asset = ResPath.GetPetModel(self.res_id)
	self:InitModel(bundle, asset)

    self.exist_time = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].exist_time
    self.interval = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].interval
    self.record_time = self.record_time + self.interval
end

function Pet:InitModel(bundle, asset)
	self.bundle_name = bundle
	self.asset_name = asset
	self:ChangeModel(SceneObjPart.Main, bundle, asset)
	self:UpdateQualityLevel()
end

function Pet:InitEnd()
	Character.InitEnd(self)
end

function Pet:SetDirectionByXY(x, y)
	Character.SetDirectionByXY(self, x, y)
end

function Pet:OnEnterScene()
	Character.OnEnterScene(self)
	-- self:CreateShadow()

    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    self.can_bubble = fb_scene_cfg.is_bubble and fb_scene_cfg.is_bubble == 1 or false
end

function Pet:CancelSelect()
	Character.CancelSelect(self)
end

function Pet:EnterStateDead()
	Character.EnterStateDead(self)
end

function Pet:IsPet()
	return true
end

--是否是自己的宠物
function Pet:IsOnwerPet()
	return self.owner_obj_id == GameVoManager.Instance:GetMainRoleVo().obj_id
end

--设置是否副战宠
function Pet:SetIsDeputyPet(bo)
	self.is_deputy_pet = bo
end

--返回是否副战宠
function Pet:IsDeputyPet()
	return self.is_deputy_pet
end

function Pet:GetPetId()
	return self.vo.pet_id
end

function Pet:GetPetHead()
	return self.head_id
end

function Pet:IsSkillReading()
	return self.is_skill_reading
end

function Pet:Update(now_time, elapse_time)
	Character.Update(self, now_time, elapse_time)

    if not self.can_bubble or not self:GetVisiable() then
        return
    end
    if nil ~= self.follow_ui and self:IsOnwerPet() then
        if now_time > self.record_time and self.is_talk == false then
            self.is_talk = true
            self:UpdataBubble()
            self.follow_ui:ForceSetVisible(true)
            self.follow_ui:ShowBubble()
            self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
                self.follow_ui:CancelForceSetVisible()
                self.follow_ui:HideBubble()
                self.is_talk = false
                self.record_time = now_time + self.interval
            end,self.exist_time)
        end
    end
end

function Pet:ClearMagicData()
	-- body
	self.magic_skill_id = nil
	self.is_skill_reading = false
	self.magic_time_record = nil
end

function Pet:EnterStateAttack()
	local anim_name = SceneObjAnimator.Atk1
	local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
	if nil ~= skill_cfg then
		anim_name = "attack" .. skill_cfg.action
	end
	Character.EnterStateAttack(self, anim_name)
end

function Pet:ShowName()
	self:GetFollowUi():ShowName()
end

function Pet:HideName()
	self:GetFollowUi():HideName()
end

function Pet:OnDie()
	Character.OnDie(self)

	local part_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()

	if self.is_skill_reading then
		self:ClearMagicData()
		if nil ~= part_obj and nil ~= part_obj.actor_ctrl then
			part_obj.actor_ctrl:StopEffects()
		end
	end
end

-- 受击
function Pet:OnBeHit(real_blood, deliverer, skill_id)
	Character.OnBeHit(self, real_blood, deliverer, skill_id)

	if SkillWGData.GetSkillBigType(skill_id) == SKILL_BIG_TYPE.NORMAL then
		self:DoHurt()
	end
end

function Pet:SetBubble(text)
	if nil ~= self.follow_ui and text then
		self.follow_ui:ChangeBubble(text)
	end
	if text then
		self.follow_ui:ShowBubble()
	else
		self.follow_ui:HideBubble()
	end
end

function Pet:ReloadUIName()
	if self.follow_ui ~= nil then
		if not self:IsDeputyPet() then
			local cfg = PetWGData.Instance:GetPetBaseCfg(self.vo.pet_id)
			self.follow_ui:SetName(cfg.name, self)
		end
	end
end

function Pet:AttackActionEndHandle()
	-- print_error("Pet AttackActionEndHandle.......")
	self:ChangeToCommonState()
end

function Pet:GetActionTimeRecord(anim_name)
	local atr = PetActionConfig[self.res_id]
    if atr ~= nil then
        return atr[anim_name]
    end

    return PetActionConfig[0][anim_name]
end

function Pet:PlayAddHpEffect()
	GlobalEventSystem:Fire(ObjectEventType.HP_RECOVER,self)
end

function Pet:OnModelLoaded(part, obj)
	if self:IsDeleted() then
		return
	end

	Character.OnModelLoaded(self, part, obj)
		local scale = PetWGData.Instance:GetPetSceneScale(self.vo.pet_id)
		self.draw_obj:GetRoot().transform.localScale = Vector3(scale,scale,scale)
		if part == SceneObjPart.Main then
			if obj then
			if self:IsDeputyPet() then
				local fight_cache = self:GetFightCache()
				-- if not fight_cache then return end
				local target_obj = self:GetDeputyAttackTarget()
				local pos_x, pos_y = target_obj:GetLogicPos()
				-- self:SetDirectionByXY(pos_x, pos_y)
				-- local part = self.draw_obj:GetPart(SceneObjPart.Main)
		-- 			part:CrossFade(SceneObjAnimator.Atk1,false)
					self:DoAttack(fight_cache.skill_id, pos_x, pos_y, fight_cache.target_obj_id)

					Scene.Instance:DeleteObj(self.vo.obj_id, self:GetDeputyPetShowTime(fight_cache.skill_id))
			end
		end
	end
end

--获取副宠显示时间，默认2s 有些技能id 有分段伤害特殊处理，增加显示时间
function Pet:GetDeputyPetShowTime(skill_id)
	if skill_id == 420 or skill_id == 426 or skill_id == 439 then
		return 7
	end

	return 2
end

function Pet:SetDeputyAttackTarget(target_obj)
	self.deputy_attack_target = target_obj
end

function Pet:GetDeputyAttackTarget()
	return self.deputy_attack_target
end

function Pet:SetFightCache(data)
	self.fight_cache = data
end

function Pet:GetFightCache()
	return self.fight_cache
end

function Pet:ClearFightCache()
    if nil ~= self.fight_cache then
    	self.fight_cache = nil
    end
end

function Pet:GetRandBubbletext()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    if #bubble_cfg > 0 then
        math.randomseed(os.time())
        local bubble_text_index = math.random(1, #bubble_cfg)
        return bubble_cfg[bubble_text_index].pet_talk
    else
        return ""
    end
end

function Pet:GetFirstBubbleText()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    return bubble_cfg[1].pet_talk
end

function Pet:UpdataBubble()
    if nil ~= self.follow_ui then
        local text = self:GetRandBubbletext()
        self.follow_ui:ChangeBubble(text)
    end
end

function Pet:CreateFollowUi()
    self.follow_ui = PetFollow.New(self.vo)
    self.follow_ui:SetOwnerObj(self)
    self.follow_ui:OnEnterScene(self.is_enter_scene)
    self.follow_ui:Create(SceneObjType.Pet)
    self:UpdateFollowUIRule()
    if self.draw_obj then
        self.follow_ui:SetFollowTarget(self.draw_obj.root.transform, self.draw_obj:GetName())
    end
end

function Pet:GetOwnerObjID()
	return self.owner_obj_id
end

function Pet:SetOwnerObj(owner_obj)
	self.owner_obj = owner_obj
end

function Pet:GetOwnerObj()
	if nil == self.owner_obj or self.owner_obj:IsDeleted() then
		self.owner_obj = Scene.Instance:GetObjectByObjId(self.owner_obj_id)
		if nil ~= self.owner_obj and self.owner_obj:IsRole() then
			self.owner_obj:SetPet(self)
		end
	end
	return self.owner_obj
end

function Pet:PartEffectVisibleChanged(part, visible)
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

function Pet:SetEffectQualityLevelOffset(offset)
	if self.effect_handle then
		self.effect_handle:SetQualityLevelOffset(offset)
	end
end

function Pet:UpdateQualityLevel()
	local base_offset = -1
	local owner_obj = self:GetOwnerObj()
	if owner_obj and owner_obj.IsMainRole and owner_obj:IsMainRole() then
		base_offset = 1
	end

	local model_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     model_offset = model_offset + QualityWGData.Instance:GetModelQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetQualityLevelOffset(model_offset)

	local effect_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     effect_offset = effect_offset + QualityWGData.Instance:GetModelEffectQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetEffectQualityLevelOffset(effect_offset)
end

function Pet:UpdateEffectVisible()
	local owner_obj = self:GetOwnerObj()
	if self.effect_handle and owner_obj and not owner_obj:IsDeleted() and owner_obj.GetRoleEffectVisible then
		local visible = owner_obj:GetRoleEffectVisible()
		if visible then
		    self.effect_handle:CancelForceSetVisible()
		else
		    self.effect_handle:ForceSetVisible(false)
		end
	end
end