﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using System;
using System.Linq;


[CreateAssetMenu(menuName = "MySubMenue/Create YYDayNightCycleProfile ")]
public partial class YYDayNightCycleProfile : ScriptableObject
{

    [Header("Day/night cycle")]
    [Range(-100000, 100000.0f)]
    public float Speed = 10.0f;

    [Range(-100000, 100000.0f)]
    public float NightSpeed = 10.0f;

    [Range(0.0f, 10.0f)]
    public float UpdateInterval = 0.03f;

    private float accumulatedTime = 10.0f;

    public const float SecondsPerDay = 86400.0f;

    [Range(0.0f, SecondsPerDay)]
    [Tooltip("The current time of day in seconds (local time).")]
    public float TimeOfDay = SecondsPerDay * 0.5f; // high noon default time of day

#if UNITY_EDITOR

#pragma warning disable 0414

    //[ReadOnlyLabel]
    [SerializeField]
    internal string TimeOfDayLabel = string.Empty;

#pragma warning restore 0414

#endif


    //public int someVariable;
    //public int someVariable11;
    [Header("Date")]
    [Tooltip("The year for simulating the sun and moon position - this can change during runtime. " +
           "The calculation is only correct for dates in the range March 1 1900 to February 28 2100.")]
    [Range(1900, 2100)]
    public int Year = 2000;

    [Tooltip("The month for simulating the sun and moon position - this can change during runtime.")]
    [Range(1, 12)]
    public int Month = 9;

    [Tooltip("The day for simulating the sun and moon position - this can change during runtime.")]
    [Range(1, 31)]
    public int Day = 21;

    public bool AdjustDateWhenDayEnds = true;

    /// <summary>Offset for the time zone of the lat / lon in seconds. Set to -1111 to auto-calculate (just tab out of the text field after you type -1111). Note about -1111: during editor mode, a web service is used. During play mode, longitude is used for fast calculation.</summary>
    [Tooltip("Offset for the time zone of the lat / lon in seconds. Set to -1111 to auto-calculate (just tab out of the text field after you type -1111). Note about -1111: during editor mode, a web service is used. During play mode, longitude is used for fast calculation.")]
    public int TimeZoneOffsetSeconds = -21600;


    [Header("Location")]
    [Range(-90.0f, 90.0f)]
    [Tooltip("The latitude in degrees on the planet that the camera is at - 90 (north pole) to -90 (south pole)")]
    public double Latitude = 40.7608; // salt lake city latitude

    [Range(-180.0f, 180.0f)]
    [Tooltip("The longitude in degrees on the planet that the camera is at. -180 to 180.")]
    public double Longitude = -111.8910; // salt lake city longitude

    [Range(0.0f, 360.0f)]
    [Tooltip("The amount of degrees your planet is tilted - Earth is about 23.439f")]
    public float AxisTilt = 23.439f;





    public void UpdateFromProfile(bool updateTimeOfDay)
    {
        if (YYWeather.Instance == null)
        {
            return;
        }

        Camera mainCamera = Camera.main;//YYWeather.Instance.MainCamera;
        YYCelestialObject sun = YYLightManager.Instance.SunPerspective;// YYLightManager.SunForCamera(mainCamera);
        if (sun == null)
        {
            Debug.Log("UpdateFromProfile fail");
            return;
        }

        accumulatedTime += Time.deltaTime;
        if (accumulatedTime > UpdateInterval)
        {
            CameraMode mode = YYWeather.ResolveCameraMode();
            IList<YYCelestialObject> suns = YYLightManager.Instance.Suns;

            //float l = sun.GetGradientLookup();
            //UpdateAmbientColors(l);
            UpdateTimeOfDay(updateTimeOfDay);
            UpdateSuns(mode, suns);
            //UpdateDayMultipliers(mode, sun, l);
            //UpdateMoons(mode, sun, moons);
            accumulatedTime = 0.0f;
        }


    }

    ///  ///////////////////////////////////


    ///  //////////////////////////////////\
    
   


    public readonly SunInfo SunData = new SunInfo();
    //public int TimeZoneOffsetSeconds = -21600;
   
   // public float TimeOfDay = SecondsPerDay * 0.5f; // high noon default time of day
    //public int Year = 2000;
    //public int Month = 9;
    //public int Day = 21;

    //public double Latitude = 40.7608; // salt lake city latitude
    //public double Longitude = -111.8910; // salt lake city longitude
    //public float AxisTilt = 23.439f;

    private void UpdateSuns(CameraMode mode, IList<YYCelestialObject> suns)
    {
        foreach (YYCelestialObject sun in suns)
        {
            if(sun == null)
            {
                Debug.Log("UpdateSuns erro");
                return;
            }

            ////
            //TimeOfDay = SecondsPerDay * 0.5f;
            ////

            double offsetSeconds = TimeZoneOffsetSeconds;
            TimeSpan t = TimeSpan.FromSeconds(TimeOfDay - offsetSeconds);

            SunData.DateTime = new DateTime(Year, Month, Day, 0, 0, 0, DateTimeKind.Utc) + t;
            SunData.Latitude = Latitude;
            SunData.Longitude = Longitude;
            SunData.AxisTilt = AxisTilt;

            if (sun.OrbitType == WeatherOrbitType.FromEarth)
            {

                //Debug.Log("sun.RotateYDegrees is " + sun.RotateYDegrees);
                //Debug.Log(" SunData.DateTime  is " + SunData.DateTime.ToString());

                YYCalculateSunPosition.CalculateSunPosition(SunData, sun.RotateYDegrees);

                //Debug.Log(" SunData.UnitVectorDown  is " + SunData.UnitVectorDown.ToString());
            }

            SetCelestialObjectPosition(sun, SunData.UnitVectorDown);

            //// calculate sun intensity and shadow strengths
            //float l = sun.GetGradientLookup();
            //float sunIntensityLookup = SunIntensityGradient.Evaluate(l).grayscale;
            //sunIntensityLookup *= sunIntensityLookup;
            //sun.Light.color = sun.LightColor * SunTintColorGradient.Evaluate(l);
            //sun.Light.shadowStrength = sun.LightBaseShadowStrength;
            //if (sun.LightMode != WeatherMakerCelestialObjectLightMode.None)
            //{
            //    sun.Light.intensity = sun.LightBaseIntensity * sunIntensityLookup;
            //}
        }
    }

    private void SetCelestialObjectPosition(YYCelestialObject obj, Vector3 transformForward)
    {
        if (obj.OrbitType == WeatherOrbitType.CustomOrthographic || obj.OrbitType == WeatherOrbitType.CustomPerspective)
        {
            Debug.Log("位置没有更新");
            return;
        }
        obj.transform.forward = (transformForward == Vector3.zero ? obj.transform.forward : transformForward);
    }

  
   



}


public class SunInfo
{
    /// <summary>
    /// Calculation parameter, the date/time on the observer planet
    /// </summary>
    public DateTime DateTime;

    /// <summary>
    /// Calculation parameter, latitude of observer planet in degrees
    /// </summary>
    public double Latitude;

    /// <summary>
    /// Calculation parameter, longitude of observer planet in degrees
    /// </summary>
    public double Longitude;

    /// <summary>
    /// Calculation parameter, axis tilt of observer planet in degrees
    /// </summary>
    public double AxisTilt;

    /// <summary>
    /// Position (unit vector) of the sun in the sky from origin
    /// </summary>
    public Vector3 UnitVectorUp;

    /// <summary>
    /// Normal (unit vector) of the sun in the sky pointing to origin (negation of Position)
    /// </summary>
    public Vector3 UnitVectorDown;

    /// <summary>
    /// Time of dawn
    /// </summary>
    public TimeSpan Dawn;

    /// <summary>
    /// Time of sunrise
    /// </summary>
    public TimeSpan SunRise;

    /// <summary>
    /// Time of sunset
    /// </summary>
    public TimeSpan SunSet;

    /// <summary>
    /// Time of dusk
    /// </summary>
    public TimeSpan Dusk;

    /// <summary>
    /// JulianDays
    /// </summary>
    public double JulianDays;

    /// <summary>
    /// Declination
    /// </summary>
    public double Declination;

    /// <summary>
    /// RightAscension
    /// </summary>
    public double RightAscension;

    /// <summary>
    /// Azimuth
    /// </summary>
    public double Azimuth;

    /// <summary>
    /// Altitude
    /// </summary>
    public double Altitude;

    /// <summary>
    /// SolarMeanAnomaly
    /// </summary>
    public double SolarMeanAnomaly;

    /// <summary>
    /// EclipticLongitude
    /// </summary>
    public double EclipticLongitude;

    /// <summary>
    /// SiderealTime
    /// </summary>
    public double SiderealTime;
}
