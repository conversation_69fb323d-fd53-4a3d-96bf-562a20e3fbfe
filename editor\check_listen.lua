-- 在close, release，delete中检查监听是否被释放
local CheckListen = {}
local obj_bind_func = {}

local IgnoreCheckKeys = {
    ["__userdata__"] = true,
    ["__raw_image_loader"] = true,
    ["__attach_gameobj_loader"] = true,
    ["__loader_owner"] = true,
   	["view_loader"] = true,
   	["view_effect"] = true,
   	["view_render"] = true,
}

function CheckListen:OnBindFun(obj, func, new_func)
	if nil == obj or nil == new_func then return end
	
	if nil == obj_bind_func[obj] then
		obj_bind_func[obj] = {}
	end

	obj_bind_func[obj][new_func] = {traceback = debug.traceback(), un_trigger = ""}
end

function CheckListen:OnCreateObj(obj)
	local list = obj_bind_func[obj]
	if nil == list then return end

	-- 在执行完New()后，检查所有该对象下的相关绑定是否在监听列表里，是则标记移除监听的触发原因为OnDeleteObj
	for k,v in pairs(list) do
		if self:IsListenExists(k) then
			v.un_trigger = "OnDeleteObj"
		end
	end
end

function CheckListen:OnReleaseView(view)
	if IsGameStop then return end

	local list = {view}
	self:GetNeedChekChildObj(view, list)

	IsStopCheckForCall = true
	for i,v in ipairs(list) do
		self:CheckListenRefrence(v, "OnReleaseView", "请考虑在ReleaseCallBack中移除")
	end
	IsStopCheckForCall = false
end

function CheckListen:OnCloseView(view)
	if IsGameStop then return end
	
	if view.view_cache_time == ViewCacheTime.MOST or view.view_cache_time == ViewCacheTime.NORMAL then
		local list = {view}
		self:GetNeedChekChildObj(view, list)
		for i,v in ipairs(list) do
			self:CheckListenRefrence(v, "OnCloseView", "请考虑在CloseCallBack中移除")
		end
	end
end

function CheckListen:OnDeleteObj(obj)
	if IsGameStop then return end

	IsStopCheckForCall = true
	local list = {obj}
	self:GetNeedChekChildObj(obj, list)
	for i,v in ipairs(list) do
		self:CheckListenRefrence(v, "OnDeleteObj", "请考虑在DeleteMe中移除")
	end

	obj_bind_func[obj] = nil
	IsStopCheckForCall = false
end

-- 检查对象是否应该被忽略
function CheckListen:ShouldIgnoreObject(obj, key)
	return "table" ~= type(obj) or
		   obj.is_component or
		   IgnoreCheckKeys[key] or
		   self:IsCfgObj(obj)
end

-- 检查是否为配置对象
function CheckListen:IsCfgObj(obj)
	local metable = getmetatable(obj)
	return nil ~= metable and nil ~= rawget(metable, "__pairs")
end

-- 添加对象到检查列表
function CheckListen:AddObjectToList(obj, list)
	if nil ~= obj.DeleteMe then
		table.insert(list, obj)
	end
end

-- 检查数组类型的table，返回是否包含DeleteMe对象
function CheckListen:CheckArrayObjects(array_table, list)
	local has_deleteme_objects = false

	for key, obj in pairs(array_table) do
		if not self:ShouldIgnoreObject(obj, key) then
			if nil ~= obj.DeleteMe then
				has_deleteme_objects = true
				self:AddObjectToList(obj, list)
			end
		end
	end

	return has_deleteme_objects
end

-- 检查obj下的所有继承baseclass的子节点（深度遍历得到）
function CheckListen:GetNeedChekChildObj(obj, list, visited_stack)
	-- 早期返回：跳过无效或应忽略的对象
	if nil == obj or self:ShouldIgnoreObject(obj, nil) or nil ~= obj.Instance then
		return
	end

	-- 防止循环引用导致的无限递归
	visited_stack = visited_stack or {}
	if visited_stack[obj] then
		return
	end
	visited_stack[obj] = true

	for key, child in pairs(obj) do
		-- 只处理不应忽略的子对象
		if not self:ShouldIgnoreObject(child, key) then
			-- 检查单个对象是否有DeleteMe方法
			if nil ~= child.DeleteMe then
				self:AddObjectToList(child, list)
			else
				-- 检查是否为包含DeleteMe对象的数组
				local has_deleteme_objects = self:CheckArrayObjects(child, list)

				-- 如果不是DeleteMe对象数组，则递归检查
				if not has_deleteme_objects then
					self:GetNeedChekChildObj(child, list, visited_stack)
				end
			end
		end
	end
end

-- 监听检查器配置表
local LISTEN_CHECKERS = {
	{
		instance = function() return ItemWGData.Instance end,
		name = "物品监听",
		check = function(instance, func) return instance:IsExistsListen(func) end
	},
	{
		instance = function() return RoleWGData.Instance end,
		name = "人物属性监听",
		check = function(instance, func) return instance:IsExistsListen(func) end
	},
	{
		instance = function() return RemindManager.Instance end,
		name = "红点监听",
		check = function(instance, func) return instance:IsExistsListen(func) end
	},
	{
		instance = function() return FunctionGuide.Instance end,
		name = "引导监听",
		check = function(instance, func) return instance:IsExistsListen(func) end
	},
	{
		instance = function() return GlobalTimerQuest end,
		name = "TimerQuest监听",
		check = function(instance, func) return instance:IsExistsListen(func) end
	},
	{
		instance = function() return CountDown.Instance end,
		name = "CountDown监听",
		check = function(instance, func) return instance:IsExistsListen(func) end
	},
	{
		instance = function() return CountDownManager.Instance end,
		name = "CountDown监听",
		check = function(instance, func) return instance:IsExistsListen(func) end
	}
}

-- 检查单个监听函数
function CheckListen:CheckSingleListen(func, desc, traceback, obj)
	-- 检查全局事件系统（特殊处理，因为返回event_id）
	if GlobalEventSystem then
		local event_id = GlobalEventSystem:IsExistsListen(func)
		if event_id then
			print_error("[CheckListen]全局事件监听没有移除！！！ EventID:", event_id, obj.view_name, desc, traceback)
		end
	end

	-- 检查其他监听系统
	for _, checker in ipairs(LISTEN_CHECKERS) do
		local instance = checker.instance()
		if instance and checker.check(instance, func) then
			print_error(string.format("[CheckListen]%s没有移除！！！", checker.name), desc, traceback)
		end
	end
end

-- 检查对象的监听引用
function CheckListen:CheckListenRefrence(obj, un_trigger, desc)
	local list = obj_bind_func[obj]
	if nil == list or IsGameStop then
		return
	end

	for func, info in pairs(list) do
		if info.un_trigger == "" or info.un_trigger == un_trigger then
			self:CheckSingleListen(func, desc, info.traceback, obj)
		end
	end
end

function CheckListen:IsListenExists(fun)
	return (nil ~= ItemWGData.Instance and ItemWGData.Instance:IsExistsListen(fun))
		or (nil ~= RoleWGData.Instance and RoleWGData.Instance:IsExistsListen(fun))
		or (nil ~= RemindManager.Instance and RemindManager.Instance:IsExistsListen(fun))
		or (nil ~= FunctionGuide.Instance and FunctionGuide.Instance:IsExistsListen(fun))
		or (nil ~= GlobalEventSystem and GlobalEventSystem:IsExistsListen(fun) ~= nil)
		or (nil ~= GlobalTimerQuest and GlobalTimerQuest:IsExistsListen(fun))
		or (nil ~= CountDown.Instance and CountDown.Instance:IsExistsListen(fun))
		or (nil ~= CountDownManager.Instance and CountDownManager.Instance:IsExistsListen(fun))
end

return CheckListen
