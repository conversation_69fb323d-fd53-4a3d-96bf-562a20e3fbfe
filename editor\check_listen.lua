-- 在close, release，delete中检查监听是否被释放
local CheckListen = {}
local obj_bind_func = {}

local IgnoreCheckKeys = {
    ["__userdata__"] = true,
    ["__raw_image_loader"] = true,
    ["__attach_gameobj_loader"] = true,
    ["__loader_owner"] = true,
   	["view_loader"] = true,
   	["view_effect"] = true,
   	["view_render"] = true,
}

function CheckListen:OnBindFun(obj, func, new_func)
	if nil == obj or nil == new_func then return end
	
	if nil == obj_bind_func[obj] then
		obj_bind_func[obj] = {}
	end

	obj_bind_func[obj][new_func] = {traceback = debug.traceback(), un_trigger = ""}
end

function CheckListen:OnCreateObj(obj)
	local list = obj_bind_func[obj]
	if nil == list then return end

	-- 在执行完New()后，检查所有该对象下的相关绑定是否在监听列表里，是则标记移除监听的触发原因为OnDeleteObj
	for k,v in pairs(list) do
		if self:IsListenExists(k) then
			v.un_trigger = "OnDeleteObj"
		end
	end
end

function CheckListen:OnReleaseView(view)
	if IsGameStop then return end

	local list = {view}
	self:GetNeedChekChildObj(view, list)

	IsStopCheckForCall = true
	for i,v in ipairs(list) do
		self:CheckListenRefrence(v, "OnReleaseView", "请考虑在ReleaseCallBack中移除")
	end
	IsStopCheckForCall = false
end

function CheckListen:OnCloseView(view)
	if IsGameStop then return end
	
	if view.view_cache_time == ViewCacheTime.MOST or view.view_cache_time == ViewCacheTime.NORMAL then
		local list = {view}
		self:GetNeedChekChildObj(view, list)
		for i,v in ipairs(list) do
			self:CheckListenRefrence(v, "OnCloseView", "请考虑在CloseCallBack中移除")
		end
	end
end

function CheckListen:OnDeleteObj(obj)
	if IsGameStop then return end

	IsStopCheckForCall = true
	local list = {obj}
	self:GetNeedChekChildObj(obj, list)
	for i,v in ipairs(list) do
		self:CheckListenRefrence(v, "OnDeleteObj", "请考虑在DeleteMe中移除")
	end

	obj_bind_func[obj] = nil
	IsStopCheckForCall = false
end

function CheckListen:IsCfgObj(obj)
	local metable = getmetatable(obj)
	return nil ~= metable and nil ~= rawget(metable, "__pairs")
end

-- 检查obj下的所有继承baseclass的子节点（深度遍历得到）
function CheckListen:GetNeedChekChildObj(obj, list, visited_stack)
	if nil == obj or "table" ~= type(obj) or obj.is_component or self:IsCfgObj(obj) or nil ~= obj.Instance then
		return
	end

	visited_stack = visited_stack or {}
	if visited_stack[obj] then
		return
	end

	visited_stack[obj] = true

	for k1, v1 in pairs(obj) do
		if "table" == type(v1) and not v1.is_component and not IgnoreCheckKeys[k1] and not self:IsCfgObj(v1) then
			if nil ~= v1.DeleteMe then
				table.insert(list, v1)
			else
				-- 检查数组中是否包含有DeleteMe方法的对象
				local has_deleteme_objects = false
				for k2, v2 in pairs(v1) do
					if "table" == type(v2) and not v2.is_component and not IgnoreCheckKeys[k2] and not self:IsCfgObj(v2) and nil ~= v2.DeleteMe then
						has_deleteme_objects = true
						table.insert(list, v2)
					end
				end

				-- 如果不是包含DeleteMe对象的数组，则继续递归检查
				if not has_deleteme_objects then
					for k2, v2 in pairs(v1) do
						if not IgnoreCheckKeys[k2] then
							self:GetNeedChekChildObj(v2, list, visited_stack)
						end
					end
				end
			end
		end
	end
end

function CheckListen:CheckListenRefrence(obj, un_trigger, desc)
	local list = obj_bind_func[obj]
	if nil == list or IsGameStop then return end

	for k,v in pairs(list) do
		if v.un_trigger == "" or v.un_trigger == un_trigger then
			if ItemWGData.Instance and ItemWGData.Instance:IsExistsListen(k) then
				print_error("[CheckListen]物品监听没有移除！！！", desc, v.traceback)
			end
			if RoleWGData.Instance and RoleWGData.Instance:IsExistsListen(k) then
				print_error("[CheckListen]人物属性监听没有移除！！！", desc, v.traceback)
			end
			if RemindManager.Instance and RemindManager.Instance:IsExistsListen(k) then
				print_error("[CheckListen]红点监听没有移除！！！", desc, v.traceback)
			end
			if FunctionGuide.Instance and FunctionGuide.Instance:IsExistsListen(k) then
				print_error("[CheckListen]引导监听没有移除！！！", desc, v.traceback)
			end
			local event_id = GlobalEventSystem and GlobalEventSystem:IsExistsListen(k)
			if event_id then
				print_error("[CheckListen]全局事件监听没有移除！！！ EventID:", event_id, obj.view_name, desc, v.traceback)
			end
			if GlobalTimerQuest and GlobalTimerQuest:IsExistsListen(k) then
				print_error("[CheckListen]TimerQuest监听没有移除！！！", desc, v.traceback)
			end
			if (CountDown.Instance and CountDown.Instance:IsExistsListen(k))
				or (CountDownManager.Instance and CountDownManager.Instance:IsExistsListen(k)) then
				print_error("[CheckListen]CountDown监听没有移除！！！", desc, v.traceback)
			end
		end
	end
end

function CheckListen:IsListenExists(fun)
	return (nil ~= ItemWGData.Instance and ItemWGData.Instance:IsExistsListen(fun))
		or (nil ~= RoleWGData.Instance and RoleWGData.Instance:IsExistsListen(fun))
		or (nil ~= RemindManager.Instance and RemindManager.Instance:IsExistsListen(fun))
		or (nil ~= FunctionGuide.Instance and FunctionGuide.Instance:IsExistsListen(fun))
		or (nil ~= GlobalEventSystem and GlobalEventSystem:IsExistsListen(fun) ~= nil)
		or (nil ~= GlobalTimerQuest and GlobalTimerQuest:IsExistsListen(fun))
		or (nil ~= CountDown.Instance and CountDown.Instance:IsExistsListen(fun))
		or (nil ~= CountDownManager.Instance and CountDownManager.Instance:IsExistsListen(fun))
end

return CheckListen
