MengLingUpStroeView = MengLingUpStroeView or BaseClass(SafeBaseView)

function MengLingUpStroeView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -20), sizeDelta = Vector2(800, 580)})
	self:AddViewResource(0, "uis/view/function_collections_ui/mengling_prefab", "layout_mengling_baoshi_upgrade")
end

function MengLingUpStroeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Charm.DaoHangKeYinUpStoreTitle

	if not self.left_cell then
		self.left_cell = ItemCell.New(self.node_list.ph_cell1)
	end

	if not self.right_cell then
		self.right_cell = ItemCell.New(self.node_list.ph_cell2)
	end

	if not self.cost_item then
		self.cost_item = ItemCell.New(self.node_list.cost_item)
	end

	-- if not self.stone_list_view then
	-- 	self.stone_list_view = AsyncListView.New(ItemCell, self.node_list["have_stone_list"])
	-- end

	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnUpgradeClick,self))
end

function MengLingUpStroeView:ReleaseCallBack()
	self.data_info = nil

	if self.left_cell then
		self.left_cell:DeleteMe()
		self.left_cell = nil
	end

	if self.right_cell then
		self.right_cell:DeleteMe()
		self.right_cell = nil
	end

	if self.cost_item then
		self.cost_item:DeleteMe()
		self.cost_item = nil
	end

	-- if self.stone_list_view then
	-- 	self.stone_list_view:DeleteMe()
	-- 	self.stone_list_view = nil
	-- end
end

function MengLingUpStroeView:SetData(data_info)
	self.data_info = data_info
end

function MengLingUpStroeView:OnFlush()
	if IsEmptyTable(self.data_info) then
		return
	end

	local data = self.data_info
	local hole = data.hole
	local item_id = data.item_id
	local level = data.level

	local cur_level_cfg = MengLingWGData.Instance:GetMengLingInLayHoleLevelCfg(hole, level)
	
	if not IsEmptyTable(cur_level_cfg) then
		local next_level_cfg = MengLingWGData.Instance:GetMengLingInLayHoleLevelCfg(hole, level + 1)
		local left_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cur_level_cfg, "attr_id", "attr_value")
		local right_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_level_cfg, "attr_id", "attr_value")

		self.left_cell:SetData({item_id = item_id})
		self.node_list.lbl_name1.text.text = string.format(Language.Common.Level1, level)

		self.right_cell:SetData({item_id = item_id})
		self.node_list.lbl_name2.text.text = string.format(Language.Common.Level1, level + 1)

		for i = 1, 4 do
			local left_attr = left_attr_data[i]
			local right_attr = right_attr_data[i]
			local has_left_attr = not IsEmptyTable(left_attr)
			local has_right_attr = not IsEmptyTable(right_attr)

			if has_left_attr then
				self.node_list["lbl_attr1" .. "_" .. i].text.text = left_attr.attr_name .. "  " .. ToColorStr(left_attr.value_str, COLOR3B.GREEN)
			end

			if has_right_attr then
				self.node_list["lbl_attr2" .. "_" .. i].text.text = right_attr.attr_name .. "  " .. ToColorStr(right_attr.value_str, COLOR3B.GREEN)
			end

			self.node_list["lbl_attr1" .. "_" .. i]:CustomSetActive(has_left_attr)
			self.node_list["lbl_attr2" .. "_" .. i]:CustomSetActive(has_right_attr)
		end

		local cost_item_id = cur_level_cfg.cost_item_id
        local cost_item_num = cur_level_cfg.cost_item_num
        local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
		local enough = has_num >= cost_item_num

		self.cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(has_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.cost_item:SetRightBottomColorText(right_text)
            self.cost_item:SetRightBottomTextVisible(true)
        end)
        self.cost_item:SetData({item_id = cost_item_id})
	end
end

function MengLingUpStroeView:OnUpgradeClick()
	if IsEmptyTable(self.data_info) then
		return
	end

	local data = self.data_info
	local hole = data.hole
	local level = data.level
	local cur_level_cfg = MengLingWGData.Instance:GetMengLingInLayHoleLevelCfg(hole, level)

	if not IsEmptyTable(cur_level_cfg) then
		local cost_item_id = cur_level_cfg.cost_item_id
        local cost_item_num = cur_level_cfg.cost_item_num
        local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
		local enough = has_num >= cost_item_num

		if enough then
			MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_HOLE_UP_LEVEL, data.seq, data.slot, hole)
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0)})
		else
			TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
		end

		self:Close()
	end
end