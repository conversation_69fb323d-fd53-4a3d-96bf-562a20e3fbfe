require("game/serveractivity/zhouyi_yuncheng/zhouyi_yuncheng_wg_data")
require("game/serveractivity/zhouyi_yuncheng/zhouyi_yuncheng_view")
require("game/serveractivity/zhouyi_yuncheng/zhouyi_yuncheng_select_view")
ZhouYiYunChengWGCtrl = ZhouYiYunChengWGCtrl or BaseClass(BaseWGCtrl)

function ZhouYiYunChengWGCtrl:__init()
	if ZhouYiYunChengWGCtrl.Instance then
        error("[ZhouYiYunChengWGCtrl]:Attempt to create singleton twice!")
	end
	ZhouYiYunChengWGCtrl.Instance = self

	self.data = ZhouYiYunChengWGData.New()
	self.view = ZhouYiYunChengView.New(GuideModuleName.ZhouYiYunCheng)
	self.select_view = ZhuoYiYunChengSelectView.New()
	self:RegisterAllProtocals()

end

function ZhouYiYunChengWGCtrl:__delete()
	self.view:DeleteMe()
	self.data:DeleteMe()
	self.select_view:DeleteMe()

	ZhouYiYunChengWGCtrl.Instance = nil
end

function ZhouYiYunChengWGCtrl:RegisterAllProtocals()
	-- -- 注册协议
	self:RegisterProtocol(SCRAHappyMondayInfo, "OnSCRAHappyMondayInfo")
	self:RegisterProtocol(SCRAHappyMondayDrawResult, "OnSCRAHappyMondayDrawResult")
	self:RegisterProtocol(SCRAHappyMondayServerRecord, "OnSCRAHappyMondayServerRecord")
	self:RegisterProtocol(SCRAHappyMondayPersonRecord, "OnSCRAHappyMondayPersonRecord")
end

function ZhouYiYunChengWGCtrl:OnSCRAHappyMondayInfo(protocol)
	self.data:OnSCRAHappyMondayInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.ZhouYi_YunCheng)
end

function ZhouYiYunChengWGCtrl:OnSCRAHappyMondayDrawResult(protocol)
	self.data:OnSCRAHappyMondayDrawResult(protocol)
	if self.view:IsOpen() then
		self.view:PlayRollAnimal()
	end
end

function ZhouYiYunChengWGCtrl:OnSCRAHappyMondayServerRecord(protocol)
	self.data:OnSCRAHappyMondayServerRecord(protocol)
	if self.view:IsOpen() then
		self.view:FlushWorld()
	end
end

function ZhouYiYunChengWGCtrl:OnSCRAHappyMondayPersonRecord(protocol)
	self.data:OnSCRAHappyMondayPersonRecord(protocol)
	if self.view:IsOpen() then
		self.view:FlushPersonal()
	end
end

-- RA_HAPPY_MONDAY_OP = { 						--周一运程操作类型
-- 	RA_HAPPY_MONDAY_OP_REQ_INFO = 1,					-- 请求信息
-- 	RA_HAPPY_MONDAY_OP_FETCH_DRAW_TIMES = 2,			-- 领取抽奖次数，参数1是任务类型
-- 	RA_HAPPY_MONDAY_OP_CHOOSE_REWARD = 3,				-- 选择奖励，param1-4 分别代码四个档位，其比特位为1则表示选择其中的奖励
-- 	RA_HAPPY_MONDAY_OP_DRAW = 4,						-- 进行一次抽奖
-- }

function ZhouYiYunChengWGCtrl:SendYunChengRollReq(opera_type,param_1,param_2,param_3,param_4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
  	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG
  	protocol.opera_type = opera_type or 0
  	protocol.param_1 = param_1 or 0
  	protocol.param_2 = param_2 or 0
  	protocol.param_3 = param_3 or 0
  	protocol.param_4 = param_4 or 0
  	protocol:EncodeAndSend()
end

function ZhouYiYunChengWGCtrl:OpenRewardSelectView()
	if self.select_view then
		self.data:CreateTempSelectList()
		self.select_view:Open()
	end
end