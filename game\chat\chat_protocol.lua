require("game/society/society_wg_ctrl")

ChatWGCtrl = ChatWGCtrl or BaseClass(BaseWGCtrl)
ChatWGCtrl.ignoreLevelLimit = 0

local cache_msg_info = {} --临时缓存一条消息

function ChatWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCChannelChatAck, "OnChannelChat")
	self:RegisterProtocol(SCChannelChatOnLogin, "OnSCChannelChatOnLogin")
	self:RegisterProtocol(SCSingleChatAck, "OnSingleChat")
	self:RegisterProtocol(SCFakePrivateChat, "OnFakePrivateChat")
	self:RegisterProtocol(SCSpeaker, "OnSpeaker")
	self:RegisterProtocol(SCSystemMsg, "OnSystemMsg")
	self:RegisterProtocol(SCSingleChatUserNotExist, "OnSingleChatUserNotExist")
	self:RegisterProtocol(SCChatLevelLimit, "OnChatLevelLimit")
	self:RegisterProtocol(SCBlacklistsACK, "OnBlacklistsACK")
	self:RegisterProtocol(SCChangeBlacklist, "OnChangeBlacklist")

	self:RegisterProtocol(CSChannelChatReq)
	self:RegisterProtocol(CSSingleChatReq)
	self:RegisterProtocol(CSSpeaker)
	self:RegisterProtocol(CSAddBlackReq)
	self:RegisterProtocol(CSDeleteBlackReq)
	self:RegisterProtocol(CSSetGuaJiInfo)

	self:RegisterProtocol(CSSaveRoleCustomSign)
	self:RegisterProtocol(SCSaveRoleCustomSign, "OnSCSaveRoleCustomSign")

	self:RegisterProtocol(CSGetCityName)
	self:RegisterProtocol(SCCityName, "OnSCCityName")

	self:RegisterProtocol(CSTeamInviteChannelChat)
	self:RegisterProtocol(CSBlackGetList)

	self:RegisterProtocol(CSInteractionReq)
	self:RegisterProtocol(CSInteractionContentReq)
	self:RegisterProtocol(SCInteractionRet, "OnSCInteractionRet")
	self:RegisterProtocol(SCInteractionSetRet, "OnSCInteractionSetRet")

	self:RegisterProtocol(CSChatCallOper)
	self:RegisterProtocol(SCChatCalledList, "OnSCChatCalledList")
	self:RegisterProtocol(SCSingleChatCallInfo, "OnSCSingleChatCallInfo")

	self:RegisterProtocol(SCChatUserInfo, "OnSCChatUserInfo")

	--玩家封禁,静默信息
	self:RegisterProtocol(SCForbidChatInfo, "OnSCForbidChatInfo")

	-- 聊天口令
	self:RegisterProtocol(CSChatKeyOperateReq)
	self:RegisterProtocol(SCChatKeyInfo, "OnSCChatKeyInfo")
end


-- 频道消息处理
function ChatWGCtrl:OnChannelChat(protocol)
	local is_robot = protocol.is_robot_talk == 1

	-- if protocol.channel_type == CHANNEL_TYPE.SCENE and Scene.Instance:GetSceneType() == SceneType.KF_DUCK_RACE then
	-- 	protocol.channel_type = CHANNEL_TYPE.WORLD
	-- end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.next_send_world_time < server_time then
		self.next_send_world_time = server_time + self.interval
	end

	if protocol.channel_type == CHANNEL_TYPE.CHUAN_WEN then
		local shield_hearsay = SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_HEARSAY)
		if shield_hearsay then
			return
		end
	end

	local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	if self.data:IsPingBiChannel(protocol.channel_type) and main_role_id ~= protocol.from_uid then
		return
	end

	if self.data:InBlacklist(protocol.from_uid) then
		return
	end
	if protocol.channel_type == CHANNEL_TYPE.GUILD and protocol.msg_reason == CHAT_MSG_RESSON.NORMAL then
		local i, j = string.find(protocol.content, ChatWGData.BossXiezhuMaching)
		local is_boss_assist = (i ~= nil and j ~= nil)
		if is_boss_assist then
			if not (protocol.from_uid == GlobalLocalRoleId) then
				local decomose_str = Split(protocol.content,";")
				local scene_id = decomose_str[2]
				if not BossWGData.Instance:CheckCanShowGuildHelpInfo(scene_id) then
					return
				end
			end
		end
	end


	local chat_content_show_type = CHAT_CONTENT_SHOW_TYPE.CHAT

	local i, j = string.find(protocol.content, ChatWGData.RedPacketMaching)
	local isPacket = (i ~= nil and j ~= nil)

	local a1, b1 = string.find(protocol.content, ChatWGData.BillionDEZGMaching)
	local billion_dezg = (a1 and b1)

	local a2, b2 = string.find(protocol.content, ChatWGData.BillionDRPTMaching)
		local billion_drpt = (a2 and b2)

	if isPacket then
		chat_content_show_type = CHAT_CONTENT_SHOW_TYPE.RED_PACKET
	elseif billion_dezg then
		chat_content_show_type = CHAT_CONTENT_SHOW_TYPE.BILLION_DEZG
	elseif billion_drpt then
		chat_content_show_type = CHAT_CONTENT_SHOW_TYPE.BILLION_DRPT
	end

	local is_redpacket = Split(protocol.content, "||")

	local msg_info = ChatWGData.CreateMsgInfo()
	local from_uid = is_robot and (-10000 - protocol.from_uid) or protocol.from_uid
	local from_cross_uuid = is_robot and from_uid or protocol.from_cross_uuid
	msg_info.from_uid = from_uid
	msg_info.from_cross_uuid = from_cross_uuid
	msg_info.username = protocol.username
	msg_info.sex = protocol.sex
	msg_info.camp = protocol.camp
	msg_info.prof = protocol.prof
	msg_info.authority_type = protocol.authority_type
	msg_info.content_type = protocol.content_type
	msg_info.tuhaojin_color = protocol.tuhaojin_color
	msg_info.bigchatface_status = protocol.bigchatface_status
	msg_info.channel_window_bubble_type = is_redpacket[2] == "red" and 998 or protocol.personalize_window_bubble_type
	msg_info.rank = protocol.rank
	msg_info.msg_reason = protocol.msg_reason
	msg_info.is_answer_true = protocol.is_answer_true
	msg_info.level = protocol.level
	msg_info.vip_level = protocol.vip_level
	msg_info.merge_server_id = protocol.merge_server_id
	msg_info.channel_type = protocol.channel_type
	msg_info.city_name = protocol.city_name
	msg_info.fashion_bubble = protocol.fashion_bubble
	msg_info.fashion_photoframe = protocol.fashion_photoframe
	msg_info.content = isPacket and string.sub(protocol.content, 0, i - 1) or protocol.content
	msg_info.msg_timestamp = protocol.msg_timestamp or TimeWGCtrl.Instance:GetServerTime() --os.date("*t", protocol.msg_timestamp)
	msg_info.send_time_str = protocol.msg_timestamp
	msg_info.chat_content_show_type = chat_content_show_type
	msg_info.origin_plat_type = protocol.origin_plat_type

	msg_info.scene_id = protocol.scene_id
	msg_info.scene_key = protocol.scene_key

	msg_info.cur_plat_type = protocol.cur_plat_type
    msg_info.cur_server_id = protocol.cur_server_id

    msg_info.monster_id = protocol.monster_id
    msg_info.is_monster = protocol.is_monster
    msg_info.shield_vip_flag = protocol.shield_vip_flag

	--print_error("protocol.msg_reason", protocol.msg_reason== CHAT_MSG_RESSON.TEAM_TIPS)
	--队伍提示消息特殊处理
	if protocol.msg_reason == CHAT_MSG_RESSON.TEAM_TIPS then
		local add_flag = string.find(protocol.content, Language.Common.QuitTeam)
		if add_flag ~= nil then
			msg_info.is_add_team = false
		else
			msg_info.is_add_team = true
		end
	end
	AvatarManager.Instance:SetAvatarFrameKey(protocol.from_uid, protocol.fashion_photoframe)
	AvatarManager.Instance:SetAvatarKey(protocol.from_uid, protocol.avatar_key_big, protocol.avatar_key_small)
	AvatarManager.Instance:SetAvatarBubbleKey(protocol.from_uid, protocol.fashion_bubble)

	if not ChatWGData.FiltrationMsg(msg_info.content) then
		return
	end

	-- 缓存世界聊天
	if msg_info.channel_type == CHANNEL_TYPE.WORLD then
		local temp_world_list = self.data:GetTempWorldList()
		if next(temp_world_list) or (self.next_send_world_time - server_time > 0 and self.next_send_world_time - server_time < self.interval) then
			self.data:AddTempWorldList(msg_info)
			if self.world_time_quest then
				return
			end
			self.world_time_quest = GlobalTimerQuest:AddRunQuest(function()
				local new_server_time = TimeWGCtrl.Instance:GetServerTime()
				if self.next_send_world_time > new_server_time then
					return
				end
				if not next(temp_world_list) then
					self:ClearWorldTimeQuest()
					return
				end
				local new_msg_info = temp_world_list[1]
				self.data:AddChannelMsg(new_msg_info)
				self:TryCalcH(new_msg_info)
				self:CheckToPlayVoice(new_msg_info)
				self.chat_window:RefreshChannel(CHANNEL_TYPE.WORLD)

				GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, new_msg_info)
				--移除表头
				self.data:RemoveTempWorldList(1)
				--重新记录下次发送时间
				self.next_send_world_time = new_server_time + self.interval
			end, 0.1)
			return
		end
	end

	local show_duck_barrage_channel = msg_info.channel_type == CHANNEL_TYPE.SCENE
	if show_duck_barrage_channel and Scene.Instance:GetSceneType() == SceneType.KF_DUCK_RACE and protocol.is_robot_talk ~= 1 then
		DuckRaceWGCtrl.Instance:OpenBarrageView(msg_info.content, true)
	end

	self.data:AddChannelMsg(msg_info)
	self:TryCalcH(msg_info)
	self.chat_window:RefreshChannel()
	GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, msg_info)
	self:HandleShowInvityTip(msg_info.channel_type, msg_info, isPacket)
	self:CheckToPlayVoice(msg_info)

	if protocol.channel_type == CHANNEL_TYPE.GUILD and protocol.msg_reason == CHAT_MSG_RESSON.NORMAL then
		local send_guild_answer_flag = GuildAnswerWGData.Instance:GetSendGuildAnswerFlag()
		if send_guild_answer_flag then
			GuildAnswerWGData.Instance:SendGuildAnswerChuangWen()
		end
	end
end

function ChatWGCtrl:ClearWorldTimeQuest()
	if self.world_time_quest then
		GlobalTimerQuest:CancelQuest(self.world_time_quest)
		self.world_time_quest = nil
	end
end
function ChatWGCtrl:ClearChuanWenTimeQuest()
	if self.chuanwen_time_quest then
		GlobalTimerQuest:CancelQuest(self.chuanwen_time_quest)
		self.chuanwen_time_quest = nil
	end
end

function ChatWGCtrl:ClearGuildSystemTimeQuest()
	if self.guild_system_time_quest then
		GlobalTimerQuest:CancelQuest(self.guild_system_time_quest)
		self.guild_system_time_quest = nil
	end
end

function ChatWGCtrl:HandleShowInvityTip(channel_type, msg_info, is_red_packit)
	if self.chat_window:IsOpen() == false or self.chat_window:GetShowIndex() ~= ChatTabIndex[channel_type] then
		local time_stamp = self.data:GetChannelTimeStamp()
		if time_stamp[channel_type] and time_stamp[channel_type] >= msg_info.msg_timestamp then return end
		self.data:AddChannelUnreadMsg(msg_info,channel_type)

		-- 只显示仙盟的聊天提示
		if TabIndexUnReadInviteTip[ChatTabIndex[channel_type]] ~= MAINUI_TIP_TYPE.CHAT_GUILD or is_red_packit then return end
		local unchat_list = self.data:GetChannelUnreaList(channel_type)
		if unchat_list and #unchat_list > 0 then
			MainuiWGCtrl.Instance:InvateTip(TabIndexUnReadInviteTip[ChatTabIndex[channel_type]], #unchat_list, function ()
	  			self:OpenChatWindow(ChatTabIndex[channel_type],true)
	  			MainuiWGCtrl.Instance.is_can_show_guildeffect = false
				MainuiWGCtrl.Instance:FlushGuildIcon()
				-- MainuiWGCtrl.Instance:InvateTip(TabIndexUnReadInviteTip[ChatTabIndex[channel_type]], 0)
				return true
			end)
		end
	end
end

-- 私聊消息处理
function ChatWGCtrl:OnSingleChat(protocol)
	-- if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.STRANGER_CHAT)
	-- 	and nil == SocietyWGData.Instance:FindFriend(protocol.from_uid) then		-- 拒绝陌生私聊
	-- 	return
	-- end
	if not ViewManager.Instance:IsOpen(GuideModuleName.Society) then
		ChatWGData.Instance:CheckFirst(protocol.from_uid)
	end
	if nil == SocietyWGData.Instance:FindFriend(protocol.from_uid) then		-- 强制拒绝陌生私聊(暂时处理成只要是陌生人都不接收私聊)
		return
	end

	if self.data:InBlacklist(protocol.from_uid) then
		return
	end
	local msg_info = ChatWGData.CreateMsgInfo()
	msg_info.from_uid = protocol.from_uid
	msg_info.from_cross_uuid = protocol.from_uid  --
	msg_info.username = protocol.username
	msg_info.sex = protocol.sex
	msg_info.camp = protocol.camp
	msg_info.prof = protocol.prof
	msg_info.authority_type = protocol.authority_type
	msg_info.content_type = protocol.content_type
	msg_info.rank = protocol.rank
	msg_info.level = protocol.level
	msg_info.add_type = protocol.add_type
	msg_info.vip_level = protocol.vip_level
	msg_info.tuhaojin_color = protocol.tuhaojin_color
	msg_info.bigchatface_status = protocol.bigchatface_status
	msg_info.channel_window_bubble_type = protocol.personalize_window_bubble_type
	msg_info.channel_type = CHANNEL_TYPE.PRIVATE
	msg_info.content = protocol.content
	msg_info.city_name = protocol.city_name
	msg_info.msg_timestamp = protocol.msg_timestamp or 0
	msg_info.fashion_bubble = protocol.fashion_bubble
	msg_info.fashion_photoframe = protocol.fashion_photoframe
	msg_info.shield_vip_flag = protocol.shield_vip_flag
	-- local time = msg_info.msg_timestamp > 0 and msg_info.msg_timestamp or TimeWGCtrl.Instance:GetServerTime()
	msg_info.send_time_str = msg_info.msg_timestamp

	AvatarManager.Instance:SetAvatarFrameKey(protocol.from_uid, protocol.fashion_photoframe)
	AvatarManager.Instance:SetAvatarKey(protocol.from_uid, protocol.avatar_key_big, protocol.avatar_key_small)
	AvatarManager.Instance:SetAvatarBubbleKey(protocol.from_uid, protocol.fashion_bubble)

	--self.chat_window:RefreshChannel()
	ChatWGData.Instance:AddPrivateMsg(protocol.from_uid, msg_info)
	SocietyWGData.Instance:SetPlayerprefsinfo(protocol.from_uid, true)
	--self.society_view:SetAchievementData()
	if SocietyWGCtrl.Instance.society_view.private_role_id == protocol.from_uid then
		-- self.view:UpdatePrivateView(true)
		--self.chat_window:UpdatePrivateView(true)
		-- SocietyWGCtrl.Instance.society_view:SetAchievementData()
	else
		self.data:AddPrivateUnreadMsg(msg_info)
		MainuiWGCtrl.Instance:SetFriendRemind()
		MainuiWGCtrl.Instance:SetChatPrivateUnreadMsgHead()
		RemindManager.Instance:Fire(RemindName.SocietyFriends)
		RemindManager.Instance:Fire(RemindName.SocietyFriends2)

		if not SocietyWGCtrl.Instance.society_view:IsOpen() then
			local unchat_list = ChatWGData.Instance:GetPrivateUnreadList()
			if unchat_list and #unchat_list > 0 then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND_CHAT, #unchat_list, function ()
				SocietyWGCtrl.Instance:OpenFriendChatView()
				 --MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.MAIL)
				  end)
			end
		end
		-- MainuiWGCtrl.Instance:UpdatePrivateTip() --TODO
	end

	--如果新发的私聊信息中 vip等级和好友列表的vip等级不一致，请求刷新好友列表
	if SocietyWGData.Instance:GetIsMyFriend(msg_info.from_uid) then
		local friend = SocietyWGData.Instance:FindFriend(msg_info.from_uid)
		local friend_level = friend and friend.vip_level or 0
		if msg_info.vip_level ~= friend_level then
			SocietyWGCtrl.Instance:SendFriendInfoReq()
		end
	end
	SocietyWGCtrl.Instance:Flush("friend_list")
	self:CheckToPlayVoice(msg_info)
end

function ChatWGCtrl:OnSingleChatUserNotExist(protocol)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NotOnline)
end


function ChatWGCtrl:OnChatLevelLimit(protocol)
	local is_limit = RechargeWGData.Instance:GetHistoryRecharge() < protocol.ignoreLevelLimit

	-- 先直接设置，由客户端人员自行修改
	COMMON_CONSTS.CHAT_LEVEL_LIMIT = is_limit and protocol.open_level_list[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_WORLD] or 0
	COMMON_CONSTS.PRIVATE_CHAT_LEVEL_LIMIT = is_limit and protocol.open_level_list[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_SINGLE] or 0
	COMMON_CONSTS.SEND_MAIL_LEVEL = is_limit and protocol.open_level_list[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_SEND_MAIL] or 0
	COMMON_CONSTS.GUILD_CHAT_LEVEL_LIMIT = is_limit and protocol.open_level_list[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_GUILD] or 0
	COMMON_CONSTS.CHAT_CROSS_LEVEL_LIMIT = is_limit and protocol.open_level_list[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_CROSS_CHAT] or 0
	COMMON_CONSTS.CREATE_TEAM_LIMIT = is_limit and protocol.open_level_list[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_CREATE_TEAM] or 0
	ChatWGData.IS_PB_AUDIO_CHAT = protocol.is_forbid_audio_chat == 1

	ChatWGData.Instance:SetChatAllInfo(protocol)
	GlobalEventSystem:Fire(OtherEventType.CHAT_PB_CHANGE)
end

-- 喇叭
function ChatWGCtrl:OnSpeaker(protocol)
	local msg_info = ChatWGData.CreateMsgInfo()

	msg_info.from_uid = protocol.from_uid
	msg_info.from_cross_uuid = protocol.from_cross_uuid
	msg_info.username = protocol.username
	msg_info.sex = protocol.sex
	msg_info.camp = protocol.camp
	msg_info.prof = protocol.prof
	msg_info.authority_type = protocol.authority_type
	msg_info.content_type = protocol.content_type
	msg_info.level = protocol.level
	msg_info.vip_level = protocol.vip_level
	msg_info.plat_name = protocol.plat_name
	msg_info.server_id =  protocol.server_id
	msg_info.merge_server_id = protocol.server_id
	msg_info.speaker_type =  protocol.speaker_type
	msg_info.tuhaojin_color = protocol.tuhaojin_color
	msg_info.bigchatface_status = protocol.bigchatface_status
	msg_info.personalize_window_type = protocol.personalize_window_type
	msg_info.channel_window_bubble_type = protocol.personalize_window_bubble_type
	msg_info.channel_type = CHANNEL_TYPE.WORLD -- CHANNEL_TYPE.CROSS--
	msg_info.city_name = protocol.city_name
	msg_info.shield_vip_flag = protocol.shield_vip_flag -- 隐藏VIP信息

	if msg_info.speaker_type == SPEAKER_TYPE.SPEAKER_TYPE_CROSS then
		msg_info.channel_type = CHANNEL_TYPE.CROSS
	end

	msg_info.content = protocol.speaker_msg
	-- msg_info.send_time_str = TimeUtil.FormatTable2HMS(TimeWGCtrl.Instance:GetServerTimeFormat())
	msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
	AvatarManager.Instance:SetAvatarKey(protocol.from_uid, protocol.avatar_key_big, protocol.avatar_key_small)

	self.data:AddChannelMsg(msg_info)
	self.chat_window:RefreshChannel(msg_info.channel_type)
	self.data:AddTransmitInfo(msg_info)
	self.chat_window:UpdateRollTransmit()
	TipWGCtrl.Instance:ShowSpeakerNotive(msg_info)
	GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, msg_info)
end

-- 系统消息
function ChatWGCtrl:OnSystemMsg(protocol)
	-- print_error("-----系统消息----", protocol.msg_type1, protocol.content)
	--【新】的传闻类型根据配置显示
	if protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_ROLL_LAMP then 		-- 跑马灯
		TipWGCtrl.Instance:ShowSystemScroll(protocol.content)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHAT_WORLD_AND_ROLL_BEAST_EQUIP then 		-- 幻兽装备跑马灯
		TipWGCtrl.Instance:ShowBeastEquipSystemScroll(protocol.content)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_PUBLIC_NOTICE then -- 公告
		TipWGCtrl.Instance:ShowSystemNotice(protocol.content)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_GUILD then 		-- 仙盟频道
		local is_have_guild = GuildWGData.Instance:IsHaveGuild() --不在仙盟不做提醒
		if is_have_guild == ShowRedPoint.SHOW_RED_POINT then
			return
		end

		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.msg_reason = CHAT_MSG_RESSON.GUILD_TIPS
		msg_info.vip_level = 0
		msg_info.channel_type = CHANNEL_TYPE.GUILD
		msg_info.content =protocol.content
		msg_info.send_time_str = protocol.send_time--TimeUtil.FormatTable2HMS(os.date("*t", protocol.send_time))

		self.data:AddChannelMsg(msg_info)
		self:TryCalcH(msg_info)
		GuildWGCtrl.Instance:IRefreshGuildChat()
		ChatWGCtrl.Instance:RefreshChannel()
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, msg_info)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_WORLD then 		-- 世界频道
		self:AddRumorMsg(protocol.content, protocol.send_time, CHANNEL_TYPE.WORLD)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_SYSTEM_MSG then 	-- 传闻频道
		self:AddRumorMsg(protocol.content, protocol.send_time, CHANNEL_TYPE.CHUAN_WEN)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_SYSTEM then 		-- 系统频道
		self:AddRumorMsg(protocol.content, protocol.send_time, CHANNEL_TYPE.SYSTEM)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_SCENE then 		-- 附近频道
		self:AddRumorMsg(protocol.content, protocol.send_time, CHANNEL_TYPE.SCENE)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_TEAM then 			-- 队伍频道
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.vip_level = 0
		msg_info.msg_reason = CHAT_MSG_RESSON.TEAM_TIPS
		msg_info.channel_type = CHANNEL_TYPE.TEAM
		msg_info.content = protocol.content
		msg_info.send_time_str = protocol.send_time--TimeUtil.FormatTable2HMS(os.date("*t", protocol.send_time))
		local add_flag = string.find(protocol.content, Language.Common.QuitTeam)
		if add_flag ~= nil then
			msg_info.is_add_team = false
		else
			msg_info.is_add_team = true
		end

		self:AddChannelMsg(msg_info, true)
		self:TryCalcH(msg_info)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_CROSS then 		-- 跨服频道

	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_ZHANDUI_3V3 then 	-- 3V3战队频道

	end


	--【旧】消息类型，请勿使用，留下来兼容之前的代码
	if protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_ONLY_CHAT_GUILD then	--服务端不好做。5代只在仙盟聊天里显示系统消息。前台自己构建
		self:AddGuildSystemMsg(protocol.content, protocol.send_time)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_PERSONAL then
		SysMsgWGCtrl.Instance:ErrorRemind(protocol.content)

	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_PUBLIC_NOTICE then		--系统公告滚动
		-- (内容)
		TipWGCtrl.Instance:ShowSystemScroll(protocol.content)
		self:AddRumorMsg(protocol.content, protocol.send_time, nil, true)
		-- SysMsgWGCtrl.Instance:RollingEffect(protocol.content, GUNDONGYOUXIAN.SYSTEM_TYPE)

	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_ROLL then				-- 传闻公告滚动
		-- SysMsgWGCtrl.Instance:RollingEffect(protocol.content, GUNDONGYOUXIAN.HEARSAY_TYPE)
		TipWGCtrl.Instance:ShowSystemScroll(protocol.content)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CENTER_AND_ROLL then	--传闻中央显示和滚动同时显示
		-- SysMsgWGCtrl.Instance:RollingEffect(protocol.content, GUNDONGYOUXIAN.HEARSAY_TYPE)
		TipWGCtrl.Instance:ShowSystemScroll(protocol.content)
		self:AddRumorMsg(protocol.content, protocol.send_time)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CROSS_TO_SCENE then --目前六界争霸需要添加  跨服频道
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.channel_type = CHANNEL_TYPE.CROSS
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.msg_reason =  CHAT_MSG_RESSON.GUILD_TIPS
		msg_info.vip_level = 0
		--msg_info.channel_type = CHANNEL_TYPE.GUILD
		msg_info.content =protocol.content
		msg_info.send_time_str = protocol.send_time--TimeUtil.FormatTable2HMS(os.date("*t", protocol.send_time))

		self:AddChannelMsg(msg_info, true)
		self:TryCalcH(msg_info)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_SPECIAL_SCENE_CENTER_AND_ROLL then	--战场专用滚动和聊天框传闻
		--TipWGCtrl.Instance:ShowSystemNotice(protocol.content) --左右 滚动
		if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN then
			--print_error(">>>>>>>>>>>>>>>>>>>>>>", protocol)
            self:AddRumorMsg(protocol.content, protocol.send_time)
		else
			TipWGCtrl.Instance:ShowZCRuneMsg(protocol.content) --右下角 上漂.
		end
		--TipWGCtrl.Instance:ShowZCRuneMsg(protocol.content) --右下角 上漂
		-- SysMsgWGCtrl.Instance:RollingEffect(protocol.content, GUNDONGYOUXIAN.HEARSAY_TYPE, protocol.msg_type)
		--self:AddRumorMsg(protocol.content, protocol.send_time)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_ONLY_CHAT_TEAM then	--只添加到聊天队伍频道
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.vip_level = 0
		msg_info.msg_reason = CHAT_MSG_RESSON.TEAM_TIPS
		msg_info.channel_type = CHANNEL_TYPE.TEAM
		msg_info.content =protocol.content
		msg_info.send_time_str = protocol.send_time--TimeUtil.FormatTable2HMS(os.date("*t", protocol.send_time))
		local add_flag = string.find(protocol.content, Language.Common.QuitTeam)
		if add_flag ~= nil then
			msg_info.is_add_team = false
		else
			msg_info.is_add_team = true
		end

		self:AddChannelMsg(msg_info, true)
		self:TryCalcH(msg_info)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CENTER_PERSONAL_NOTICE then	--战场专用滚动和聊天框传闻
		SysMsgWGCtrl.Instance:ErrorRemind(protocol.content)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CENTER then	--中间传闻
		TipWGCtrl.Instance:ShowSystemNotice(protocol.content)
		self:AddRumorMsg(protocol.content, protocol.send_time)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_SEND_TO_SCENE then
		-- SysMsgWGCtrl.Instance:RollingEffect(protocol.content, GUNDONGYOUXIAN.HEARSAY_TYPE)
		TipWGCtrl.Instance:ShowSystemNotice(protocol.content)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_ROLE_ADD_GUILD then			--角色加入帮派
		local msg_info = ChatWGData.CreateMsgInfo()
		--msg_info.channel_type = CHANNEL_TYPE.SYSTEM
		msg_info.from_uid = GuildDataConst.GUILDVO.tuanzhang_uid--110
		msg_info.username = GuildDataConst.GUILDVO.tuanzhang_name
		msg_info.sex = 0
		msg_info.camp =  GuildDataConst.GUILDVO.camp
		msg_info.prof = 1
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.vip_level = GuildDataConst.GUILDVO.vip_level
		msg_info.channel_type = CHANNEL_TYPE.GUILD
		--msg_info.content =protocol.content
		msg_info.msg_reason = CHAT_MSG_RESSON.NORMAL
		msg_info.send_time_str = protocol.send_time--TimeUtil.FormatTable2HMS(os.date("*t", protocol.send_time))

		local list = Split(protocol.content,"|")  --文本内容 + 幫主ID + 战力排行名次
		local temp_user_id = tonumber(list[2])
		msg_info.content = list[1]
		msg_info.from_uid = temp_user_id
		msg_info.rank = tonumber(list[3])
		cache_msg_info[temp_user_id] = msg_info
		-- local list1 = Split(protocol.content,";") --获取新加入的角色ID
		-- local join_id = tonumber(list1[2])

		-- --如果当前角色ID是自己，则需要发送请求 帮主信息
		-- if Scene.Instance:GetMainRole().vo.role_id == join_id then
			BrowseWGCtrl.Instance:BrowRoelInfo(temp_user_id, function(param_protocol)
				local pro_use_id = param_protocol.role_id
				if cache_msg_info[pro_use_id] then
					cache_msg_info[pro_use_id].username = param_protocol.role_name
					cache_msg_info[pro_use_id].sex = param_protocol.sex
					cache_msg_info[pro_use_id].prof = param_protocol.prof
					cache_msg_info[pro_use_id].vip_level = param_protocol.vip_level
					cache_msg_info[pro_use_id].fashion_bubble = 0
					self:SendGetCityName(pro_use_id)
				end

					-- if not self.chat_window:IsOpen() and not SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.GuildChatBubble) then --TODO 屏蔽判断 因为没有这个方法
				-- if not self.chat_window:IsOpen() then
				-- 	--UiInstanceMgr.Instance:ShowGuildNewestChatMsg(msg_info)	-- 主界面显示仙盟聊天信息
				-- 	ChatWGCtrl.Instance:OpenChatWindow(CHANNEL_TYPE.GUILD)
				-- end
			end)
		-- else
		-- 	msg_info.from_uid = GuildDataConst.GUILDVO.tuanzhang_uid--110
		-- 	msg_info.username = GuildDataConst.GUILDVO.tuanzhang_name
		-- 	msg_info.sex = param_protocol.sex
		-- 	msg_info.prof = param_protocol.prof
		-- 	self.data:AddChannelMsg(msg_info)
		-- 	GuildWGCtrl.Instance:IRefreshGuildChat()
		-- 	ChatWGCtrl.Instance:RefreshChannel()
		-- 	GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, msg_info)
		-- 		-- if not self.chat_window:IsOpen() and not SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.GuildChatBubble) then --TODO 屏蔽判断 因为没有这个方法
		-- 	if not self.chat_window:IsOpen() then
		-- 		--UiInstanceMgr.Instance:ShowGuildNewestChatMsg(msg_info)	-- 主界面显示仙盟聊天信息
		-- 		ChatWGCtrl.Instance:OpenChatWindow(CHANNEL_TYPE.GUILD)
		-- 	end
		-- end

	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_SEND_TO_SCENE_PIAOZI_AND_CHUANWEN then
		TipWGCtrl.Instance:ShowSystemNotice(protocol.content)
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.vip_level = 0
		msg_info.msg_reason = CHAT_MSG_RESSON.NORMAL
		msg_info.channel_type = CHANNEL_TYPE.CHUAN_WEN
		msg_info.content = protocol.content
		msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
		msg_info.is_add_team = false
		msg_info.title_text = Language.ChannelColor2[10]

		ChatWGCtrl.Instance:AddChannelMsg(msg_info, true)
		self:TryCalcH(msg_info)
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_ROLL_AND_WORLD then
		TipWGCtrl.Instance:ShowSystemScroll(protocol.content)
		self:AddRumorMsg(protocol.content, protocol.send_time, nil, true)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_SPECIAL_SCENE_ROLL_AND_SYSTEM_MSG then
		TipWGCtrl.Instance:ShowZCRuneMsg(protocol.content) --右下角 上漂.
		self:AddRumorMsg(protocol.content, protocol.send_time, nil, true)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_SPEAK_ROLL then
		local msg_info = {}
		msg_info.content = protocol.content
		TipWGCtrl.Instance:SetLowPriorityNotice(msg_info)

		self:AddRumorMsg(protocol.content, protocol.send_time, CHANNEL_TYPE.CHUAN_WEN,true)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_NO_LIMIT_SYSTEM_MSG then  -- 传闻频道(不拦截)
		self:AddRumorMsg(protocol.content, protocol.send_time, nil, true)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHANNEL_SYSTEM_MSG then 	-- 传闻频道
		--占位，防止出现两条
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CROSS_AND_GUILD then      -- 跨服、仙盟、千里传音
		local msg_info = {}
		msg_info.content = protocol.content
		TipWGCtrl.Instance:SetLowPriorityNotice(msg_info)

		local msg_info1 = ChatWGData.CreateMsgInfo()
		msg_info1.channel_type = CHANNEL_TYPE.CROSS
		msg_info1.from_uid = 0
		msg_info1.username = ""
		msg_info1.sex = 0
		msg_info1.camp = 0
		msg_info1.prof = 0
		msg_info1.authority_type = 0
		msg_info1.tuhaojin_color = 0
		msg_info1.level = 0
		msg_info1.msg_reason =  CHAT_MSG_RESSON.GUILD_TIPS
		msg_info1.vip_level = 0
		msg_info1.content =protocol.content
		msg_info1.send_time_str = protocol.send_time--TimeUtil.FormatTable2HMS(os.date("*t", protocol.send_time))
		self:AddChannelMsg(msg_info1, true)
		self:TryCalcH(msg_info1)

		local msg_info2 = ChatWGData.CreateMsgInfo()
		msg_info2.channel_type = CHANNEL_TYPE.GUILD
		msg_info2.from_uid = 0
		msg_info2.username = ""
		msg_info2.sex = 0
		msg_info2.camp = 0
		msg_info2.prof = 0
		msg_info2.authority_type = 0
		msg_info2.tuhaojin_color = 0
		msg_info2.level = 0
		msg_info2.msg_reason =  CHAT_MSG_RESSON.GUILD_TIPS
		msg_info2.vip_level = 0
		msg_info2.content =protocol.content
		msg_info2.send_time_str = protocol.send_time--TimeUtil.FormatTable2HMS(os.date("*t", protocol.send_time))
		self:AddChannelMsg(msg_info2, true)
		self:TryCalcH(msg_info2)
	elseif protocol.msg_type1 == SYS_MSG_TYPE.SYS_MSG_CHAT_WORLD_AND_ROLL_REAL then      -- 世界频道+跑马灯 (圣兽特殊处理)
		TipWGCtrl.Instance:ShowSystemScroll(protocol.content)
		self:AddRumorMsg(protocol.content, protocol.send_time, CHANNEL_TYPE.WORLD, true, true)
	else
		self:AddRumorMsg(protocol.content, protocol.send_time)
	end
end

function ChatWGCtrl:SendTeamInviteChannelChat()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamInviteChannelChat)
	protocol:EncodeAndSend()
end

function ChatWGCtrl:SendBlackGetList()
	local protocol = ProtocolPool.Instance:GetProtocol(CSBlackGetList)
	protocol:EncodeAndSend()
end

function ChatWGCtrl:SendChannelChat(type1, content, content_type, msg_reason, is_other_operate, not_report, show_tip, need_php_vip_limit)
	-- 发送聊天口令领奖励
	local chat_reward_word_seq
	if type1 == CHANNEL_TYPE.WORLD and content_type ~= CHAT_CONTENT_TYPE.AUDIO and content_type ~= CHAT_CONTENT_TYPE.FEES_AUDIO then
		local word_list = GLOBAL_CONFIG.chat_reward_word_list
		if word_list then
			for k,v in ipairs(word_list) do
				if v.word == content then
					chat_reward_word_seq = v.seq
					break
				end
			end
		end
	end

	-- 聊天口令不受聊天限制影响
	if (chat_reward_word_seq == nil and need_php_vip_limit) and ChatWGData.Instance:IsPHPVipLimitChat(type1) then
		return
	end

	show_tip = show_tip == nil and true or show_tip
	if "" == content then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end

	if type1 == CHANNEL_TYPE.GUILD and RoleWGData.Instance.role_vo.guild_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoGuild)
		return
	end

	if ChatWGData.Instance:CheckChatDailyLivenessLimit(type1, show_tip) then
		return
	end

	if msg_reason ~= CHAT_MSG_RESSON.TEAM_TIPS then
		--组队聊天是判断是否有队伍
		if type1 == CHANNEL_TYPE.TEAM and 0 == SocietyWGData.Instance:GetIsInTeam() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoTeam)
			return false
		end
	end

	if ChatWGData.ExamineChannelRule(type1, is_other_operate) then
		-- if SceneType.KF_DUCK_RACE == Scene.Instance:GetSceneType() then
		-- 	if type1 == CHANNEL_TYPE.WORLD then
		-- 		type1 = CHANNEL_TYPE.SCENE
		-- 	end
		-- end

		local content_str = content
		if content_type ~= CHAT_CONTENT_TYPE.AUDIO and content_type ~= CHAT_CONTENT_TYPE.FEES_AUDIO then
			content_str = ChatFilter.Instance:Filter(content)

			-- 领取聊天口令奖励
			if chat_reward_word_seq then
				local reward_cfg = ChatWGData.Instance:GetChatwordRewardCfg(chat_reward_word_seq)
				if reward_cfg then
					local is_get = ChatWGData.Instance:GetChatWordRewardIsGet(chat_reward_word_seq)
					if not is_get then
						self:SendGetChatWordReward(1, chat_reward_word_seq)
					end
				end
			end
		end

		-- 检测聊天内容是否包含艾特玩家信息
		content_str = ChatWGData.Instance:ConvertToAiTeMsg(content_str)
		local protocol = ProtocolPool.Instance:GetProtocol(CSChannelChatReq)
		protocol.content_type = content_type or 0
		protocol.msg_reason = msg_reason or 0
		protocol.channel_type = type1
		protocol.content = content_str
		protocol.is_other_operate = is_other_operate or 0
		protocol:EncodeAndSend()

		-- 用来过滤一些不需要上报的客户端自己发的消息
		if not_report then
			return
		end

		if content_type ~= CHAT_CONTENT_TYPE.AUDIO and content_type ~= CHAT_CONTENT_TYPE.FEES_AUDIO then
			ReportManager:ReportChat(type1, content)
		end
	end
end

function ChatWGCtrl:SendSingleChat(to_uid, content, content_type, not_report)
	local content_str = content
	local protocol = ProtocolPool.Instance:GetProtocol(CSSingleChatReq)
	protocol.to_uid = to_uid
	protocol.content = content_str
	--protocol.msg_reason = msg_reason or 0
	protocol.content_type = content_type or 0
	protocol:EncodeAndSend()

	if not_report then
		return
	end

	if content_type ~= CHAT_CONTENT_TYPE.AUDIO and content_type ~= CHAT_CONTENT_TYPE.FEES_AUDIO then
		
		local chat_target_data
		local data = ChatWGData.Instance:GetPrivateObjByRoleId(to_uid)
		if data then
			chat_target_data = {
				role_id = to_uid,
				role_name = data.username or to_uid,
			}
		end

		ReportManager:ReportChat(CHANNEL_TYPE.PRIVATE, content, chat_target_data)
	end
end

function ChatWGCtrl:SendCurrentTransmit(is_auto_buy, speaker_msg, content_type, speaker_type)
	if ChatWGData.Instance:IsPHPVipLimitChat() then
		return
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSSpeaker)
	protocol.is_auto_buy = is_auto_buy
	protocol.content_type = content_type or 0
	protocol.speaker_msg = ChatFilter.Instance:Filter(speaker_msg)
	protocol.speaker_type = speaker_type or 0
	protocol:EncodeAndSend()
end

--私聊消息处理
function ChatWGCtrl:OnFakePrivateChat(protocol)
	-- if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.STRANGER_CHAT)
	-- 	and nil == SocietyWGData.Instance:FindFriend(protocol.from_uid) then		-- 拒绝陌生私聊
	-- 	return
	-- end
	if nil == SocietyWGData.Instance:FindFriend(protocol.from_uid) then			-- 强制拒绝陌生私聊(暂时处理成只要是陌生人都不接收私聊)
		return
	end

	local msg_info = ChatWGData.CreateMsgInfo()
	msg_info.from_uid = protocol.from_uid
	msg_info.username = protocol.username
	msg_info.sex = protocol.sex
	msg_info.camp = protocol.camp
	msg_info.prof = protocol.prof
	msg_info.authority_type = 0
	msg_info.content_type = 0
	msg_info.tuhaojin_color = 0
	msg_info.level = protocol.level
	msg_info.vip_level = protocol.vip_level
	msg_info.channel_type = CHANNEL_TYPE.PRIVATE
	msg_info.content = self:GetRandomContent()
	msg_info.city_name = protocol.city_name
	msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
	AvatarManager.Instance:SetAvatarKey(protocol.from_uid, protocol.avatar_key_big, protocol.avatar_key_small)

	ChatWGData.Instance:AddPrivateMsg(protocol.from_uid, msg_info)
	if self:IsPrivateOpen() then
		-- self.view:UpdatePrivateView(true)
		self.chat_window:UpdatePrivateView(true)
	else
		self.data:AddPrivateUnreadMsg(msg_info)
		SocietyWGCtrl.Instance:Flush("friend_list")
		MainuiWGCtrl.Instance:SetChatPrivateUnreadMsgHead()
		MainuiWGCtrl.Instance:SetFriendRemind()
		RemindManager.Instance:Fire(RemindName.SocietyFriends)
		RemindManager.Instance:Fire(RemindName.SocietyFriends2)
		-- MainuiWGCtrl.Instance:UpdatePrivateTip() --方法没有还在调用
	end
end

-- 黑名单信息
function ChatWGCtrl:OnBlacklistsACK(protocol)
	self.data:SetBlacklist(protocol.blacklist)
	--self.blacklist_view:Flush()
	SocietyWGCtrl.Instance:Flush()
end

-- 黑名单信息改变
function ChatWGCtrl:OnChangeBlacklist(protocol)

	local blacklist = self.data:GetBlacklist()
	local index = -1
	if protocol.changstate == 0 then
		local vo = {
				user_id = protocol.user_id,
				gamename = protocol.gamename,
				sex = protocol.sex,
				prof = protocol.prof,
				--vip_level = protocol.vip_level,
				level = protocol.level,
				is_online = protocol.is_online,
				fashion_photoframe = protocol.fashion_photoframe,
				--capapility = protocol.capapility,
				}
		for k,v in pairs(blacklist) do
			if v.user_id == protocol.user_id then
				index = k
				blacklist[k] = vo
				break
			end
		end
		if index == -1 then
			table.insert(blacklist, vo)
		end
	elseif protocol.changstate == 1 then
		for k,v in pairs(blacklist) do
			if v.user_id == protocol.user_id then
				index = k
				break
			end
		end
		if index > -1 then
			table.remove(blacklist, index)
		end
	end
	--self.blacklist_view:Flush()
	--SocietyWGCtrl.Instance.society_view:FlushBlackView()
	ViewManager.Instance:FlushView(GuideModuleName.Rank)
	SocietyWGCtrl.Instance:Flush("friend_list")
end

-- 请求添加黑名单
function ChatWGCtrl:SendAddBlackReq(uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSAddBlackReq)
	protocol.user_id = uid or 0
	protocol:EncodeAndSend()
end

-- 请求删除黑名单
function ChatWGCtrl:SendDeleteBlackReq(uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDeleteBlackReq)
	protocol.user_id = uid or 0
	protocol:EncodeAndSend()
end

function ChatWGCtrl:OnSCChannelChatOnLogin(protocol)
	for i,v in ipairs(protocol.info_list) do
		local is_robot = v.is_robot_talk == 1
		if not is_robot then
			local from_uid = is_robot and (-10000 - v.from_uid) or v.from_uid
			local from_cross_uuid = is_robot and from_uid or v.from_cross_uuid
			v.from_uid = from_uid
			v.from_cross_uuid = from_cross_uuid
			AvatarManager.Instance:SetAvatarFrameKey(v.from_uid, v.fashion_photoframe)
			AvatarManager.Instance:SetAvatarKey(v.from_uid, v.avatar_key_big, v.avatar_key_small)
			AvatarManager.Instance:SetAvatarBubbleKey(v.from_uid, v.fashion_bubble)
			self.data:AddChannelMsg(v)
			self:HandleShowInvityTip(v.channel_type, v)
		end
	end

	self:TryInitChatMsgH()
	self.chat_window:RefreshChannel()
	self.is_receive_complete = true

	--print_error("接收聊天消息is_receive_complete ******************************************")
	--战队日志消息处理
	if #self.zhandui_log_msg > 0 then
		for i, v in ipairs(self.zhandui_log_msg) do
			self.data:AddChannelMsg(v)
		end
		ChatWGCtrl.Instance:RefreshChannel()
	end
	GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)

	self:ReqAiTeInfoList()
end

function ChatWGCtrl:SetChannelInfo(str)
	if string.len(str) > 124 then
        print_error('save string len is too long ')
        return
    end
    local protocol = ProtocolPool.Instance:GetProtocol(CSSetGuaJiInfo)
    protocol.str = str
    protocol:EncodeAndSend()
end

function ChatWGCtrl:SaveChannelInfo()
	local str = ""
	local data = self.data:GetChannelTimeStamp()
	for i,v in pairs(data) do
		str = str .. i
		str = str .. ","
		str = str .. v .. "|"
	end
	str = string.sub(str, 1, -2)
	self:SetChannelInfo(str)
end

function ChatWGCtrl:SendPingBiInfo(channel_enum, is_pinbi)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSaveRoleCustomSign)
    protocol.index = channel_enum
    protocol.sign = is_pinbi or 0
    protocol:EncodeAndSend()
end

function ChatWGCtrl:OnSCSaveRoleCustomSign(protocol)
	-- if IS_ON_CROSSSERVER then return end
	self.data:SetPingBiList(protocol.pingbi_list)
	if self.setting_view:IsOpen() then
		self.setting_view:Flush()
	end
end

function ChatWGCtrl:SendGetCityName(user_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetCityName)
	protocol.target_uid = user_id
	protocol:EncodeAndSend()
end

function ChatWGCtrl:OnSCCityName(protocol)
	local role_id = protocol.uuid.temp_low
	if role_id > 0 and cache_msg_info[role_id] then
		cache_msg_info[role_id].city_name = protocol.city_name
		--发送帮主欢迎消息
		self.data:AddChannelMsg(cache_msg_info[role_id])
		-- GuildWGCtrl.Instance:IRefreshGuildChat()
		ChatWGCtrl.Instance:RefreshChannel()
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, cache_msg_info[role_id])
		cache_msg_info[role_id] = nil
	end
end

--=====================================拍一拍 start===================
--请求拍一拍
function ChatWGCtrl:SendToPaiYiPai(data)
	local protocol = ProtocolPool.Instance:GetProtocol(CSInteractionReq)
	protocol.target_role_id = data.target_role_id or 0  			-- 目标对象ID
	protocol.target_role = data.target_role or "" 					-- 目标对象名字
	protocol.target_server_id = data.target_server_id or 0  		-- 目标对象服务器ID
	protocol.target_plat_type = data.target_plat_type or 0 			-- 目标对象平台ID
	protocol.channel_type = data.channel_type or 0 					-- 频道类型
	protocol.interaction_type = data.interaction_type or HUDONG_TYPE.PYP  -- 互动类型

	protocol:EncodeAndSend()
end

--自定义拍一拍文本
function ChatWGCtrl:SendSetCustomInfo(interaction_type, index, is_set_flag, msg_buff)
	local protocol = ProtocolPool.Instance:GetProtocol(CSInteractionContentReq)
	-- print_error("FFF==== 自定义拍一拍文本", index, is_set_flag, msg_buff)
	protocol.interaction_type = interaction_type or HUDONG_TYPE.PYP
	protocol.index = index or 0
	protocol.is_set_flag = is_set_flag
	protocol.msg_buff = msg_buff or 0
	protocol:EncodeAndSend()
end

--拍一拍信息返回
function ChatWGCtrl:OnSCInteractionRet(protocol)
	--拍一拍勾选屏蔽
	if ChatWGData.Instance:GetPYPIsPingbi() then
		return
	end

	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	--黑名单判断:屏蔽黑名单人员拍我信息
	if protocol.target_role_id == my_uid and ChatWGData.Instance:InBlacklist(protocol.from_role_id) then
		return
	end

	local msg_info = ChatWGData.CreateMsgInfo()
	msg_info.from_uid = protocol.from_role_id						-- 发送者id
	msg_info.username = protocol.from_role							-- 发送者名字
	msg_info.target_role_id = protocol.target_role_id				-- 目标对象ID
	msg_info.target_role = protocol.target_role						-- 目标对象名字
	msg_info.from_role_server_id = protocol.from_role_server_id		-- 发送者服务器ID
	msg_info.target_role_server_id = protocol.target_role_server_id	-- 目标对象服务器ID
	msg_info.pyp_index = protocol.param1							-- 参数(拍一拍显示类型下标)
	msg_info.interaction_type = protocol.interaction_type			-- 互动类型
	msg_info.is_set_flag = protocol.is_set_flag						-- 是否设置自定义内容标记
	msg_info.msg_buff = protocol.is_set_flag ~= 0 and protocol.msg_buff or ""	-- 自定义显示内容
	msg_info.channel_type = protocol.channel_type					-- 频道类型
	msg_info.msg_reason = CHAT_MSG_RESSON.HUDONG_PYP	--互动--拍一拍

	msg_info.from_role_plat_type = protocol.from_role_plat_type
	msg_info.scene_id = protocol.scene_id
	msg_info.scene_key = protocol.scene_key

	msg_info.cur_plat_type = protocol.cur_plat_type
	msg_info.cur_server_id = protocol.cur_server_id

	--拍一拍引导信息
	--0: None 不引导 1: First 引导第一次 2: PingBi 引导屏蔽
	local is_first = ChatWGData.Instance:GetIsFirstPYP() and my_uid == msg_info.from_uid--首次标记 以及 我拍别人
	local is_pingbi = false
	local is_i_touch = my_uid == msg_info.from_uid--我拍别人
	local is_other_touch_me = my_uid == msg_info.target_role_id--别人拍我

	if not is_first then
		is_pingbi = ChatWGData.Instance:CheckPYPPingBiGuide(msg_info.channel_type)
	end

	local not_shield_pyp = not ChatWGData.Instance:GetIsShieldPYP()
	local is_add_guide = (is_first or is_pingbi) and not_shield_pyp
	msg_info.pyp_guide_type = PYP_MSG_GUIDE_TYPE.None
	if msg_info.channel_type == CHANNEL_TYPE.PRIVATE then--私聊
		local temp_from_uid = is_i_touch and msg_info.target_role_id or msg_info.from_uid
		ChatWGData.Instance:AddPrivateMsg(temp_from_uid, msg_info)

		--增加 私聊引导信息
		if is_add_guide then
			local pyp_guide_type = is_first and PYP_MSG_GUIDE_TYPE.First or PYP_MSG_GUIDE_TYPE.PingBi
			local guide_msg_info = ChatWGData.Instance:AddPYPGuideMsgInfo(msg_info, pyp_guide_type)
			if not IsEmptyTable(guide_msg_info) then
				ChatWGData.Instance:AddPrivateMsg(temp_from_uid, guide_msg_info)
			end
		end

		SocietyWGCtrl.Instance:TryToFlushSocietyPYP()
	else
		msg_info.fix_show_main = is_i_touch or is_other_touch_me
		if is_i_touch or is_other_touch_me then
			GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
		end
		--存储本地
		ChatWGData.Instance:AddChannelMsg(msg_info)

		--增加 引导信息
		if is_add_guide then
			local pyp_guide_type = is_first and PYP_MSG_GUIDE_TYPE.First or PYP_MSG_GUIDE_TYPE.PingBi
			local guide_msg_info = ChatWGData.Instance:AddPYPGuideMsgInfo(msg_info, pyp_guide_type)
			if not IsEmptyTable(guide_msg_info) then
				guide_msg_info.fix_show_main = false
				ChatWGData.Instance:AddChannelMsg(guide_msg_info)
			end
		end

		ChatWGCtrl.Instance:RefreshChannel()
	end
end

-- 玩家设置互动内容返回
function ChatWGCtrl:OnSCInteractionSetRet(protocol)
	self.data:OnCachePYPRoleSettingInfo(protocol)
end

--=====================================拍一拍 end===================

function ChatWGCtrl:OnSCChatUserInfo(protocol)
	self.data:OnSCChatUserInfo(protocol)
end

--=====================================@ 艾特操作 start
--请求查询 @ 信息
function ChatWGCtrl:ReqAiTeInfoList()
	--print_error("FFF===== 请求查询 @ 信息")
	local protocol = ProtocolPool.Instance:GetProtocol(CSChatCallOper)
	protocol.oper_type = AITE_MSG_TYPE.ReqInfo
	protocol.channel_type = 0
	protocol.timestamp = 0
	protocol.server_id = 0
	protocol.plat_type = 0
	protocol.role_uid = 0
	protocol:EncodeAndSend()
end

--请求清除频道内所有 @ 信息
function ChatWGCtrl:ClearAiTeInfoListByChannel(channel_type)
	-- print_error("FFF===== 请求清除频道内所有 @ 信息")
	local protocol = ProtocolPool.Instance:GetProtocol(CSChatCallOper)
	protocol.oper_type = AITE_MSG_TYPE.ClearInfo
	protocol.channel_type = channel_type or 0
	protocol.timestamp = 0
	protocol.server_id = 0
	protocol.plat_type = 0
	protocol.role_uid = 0
	protocol:EncodeAndSend()
end

function ChatWGCtrl:ChatAiTeSomeone(data)
	-- print_error("FFF===== 请求@ data", data)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChatCallOper)
	protocol.oper_type = data.oper_type or 0
	protocol.channel_type = data.channel_type or 0
	protocol.timestamp = data.timestamp or 0
	protocol.server_id = data.server_id or 0
	protocol.plat_type = data.plat_type or 0
	protocol.role_uid = data.role_uid or 0

	protocol:EncodeAndSend()
end

function ChatWGCtrl:OnSCChatCalledList(protocol)
	--print_error("FFFF===== @信息返回", protocol.call_list)
	self.data:OnSCChatCalledList(protocol)
end

function ChatWGCtrl:OnSCSingleChatCallInfo(protocol)
	--print_error("FFFF===== @单条 信息返回", protocol)
	local info = protocol.call_info

	if self.data:InBlacklist(info.uid) then
		return
	end
	self.data:OnSCSingleChatCallInfo(protocol)
end
--=====================================@ 艾特操作 end

-- boss发假信息
function ChatWGCtrl:FakeBossChatMsg(monster_id, content, channel_type)
    local fake_protocol = {}
	fake_protocol.from_uid = -1
    fake_protocol.from_cross_uuid = 0
    local cfg = BossWGData.Instance:GetMonsterCfgByid(monster_id)
	fake_protocol.username = cfg.name
	fake_protocol.sex = 0
	fake_protocol.camp = 0
	fake_protocol.prof = 0
	fake_protocol.authority_type = 0
	fake_protocol.content_type = 0
	fake_protocol.tuhaojin_color = 0
	fake_protocol.bigchatface_status = 0
	fake_protocol.personalize_window_bubble_type = 0		-- 气泡框，-1 表示未激活
	fake_protocol.rank = 0					-- 战力前十标记
	fake_protocol.msg_reason = 0
	fake_protocol.is_answer_true = 0
	fake_protocol.avatar_key_big = 0
	fake_protocol.avatar_key_small = 0
	fake_protocol.level = 0
	fake_protocol.vip_level = 0
	fake_protocol.channel_type = channel_type
	fake_protocol.city_name = ""

	fake_protocol.fashion_bubble = 0
	fake_protocol.fashion_photoframe = 0
	fake_protocol.origin_plat_type = 0

	fake_protocol.msg_timestamp = TimeWGCtrl.Instance:GetServerTime()
	fake_protocol.is_robot_talk = 0
	fake_protocol.merge_server_id = RoleWGData.Instance:GetMergeServerId()
	fake_protocol.scene_id = Scene.Instance:GetSceneId()
	fake_protocol.scene_key = RoleWGData.Instance:GetAttr("scene_key")
	fake_protocol.cur_plat_type = RoleWGData.Instance:GetCurPlatType()
	fake_protocol.cur_server_id = RoleWGData.Instance:GetCurServerId()
	fake_protocol.msg_length = string.len(content)
    fake_protocol.content = content

    fake_protocol.monster_id = monster_id
    fake_protocol.is_monster = true
    if cfg then
        self:OnChannelChat(fake_protocol)
    end
end

function ChatWGCtrl:SendFakePro()
    --ChatWGCtrl.Instance:SendFakePro()
    self:FakeBossChatMsg(54012, "llllllll啦啦啦阿拉来了", CHANNEL_TYPE.SCENE)
end

function ChatWGCtrl:OnSCForbidChatInfo(protocol)
	local forbid_uid_list = protocol.forbid_uid_list
	self:ClearForbidChatInfo(forbid_uid_list)
end

function ChatWGCtrl:ClearForbidChatInfo(forbid_uid_list)
	if IsEmptyTable(forbid_uid_list) then
		return
	end

	local channel_list = self.data:GetForbidChatChanel()
	if IsEmptyTable(channel_list) then
		return
	end

	--剔除玩家消息
	local channel_info, info_list, need_flush
	for _, channel in pairs(channel_list) do
		channel_info = self.data:GetChannel(channel)
		info_list = channel_info and channel_info.msg_list
		need_flush = false
		if not IsEmptyTable(info_list) then
			for i = #info_list, 1, -1 do
				if info_list[i].from_uid and forbid_uid_list[info_list[i].from_uid] then
					-- print_error("remove remove remove channel", channel, info_list[i].content)
					table.remove(info_list, i)
					if not need_flush then
						need_flush = true
					end
				end
			end

			if need_flush and channel ~= CHANNEL_TYPE.MAINUI then
				ChatWGCtrl.Instance:RefreshChannel(channel, nil, true)
			end
		end
	end
	
	--刷新聊天
	-- ChatWGCtrl.Instance:RefreshChannel(nil, nil, true)
	GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
end

function ChatWGCtrl:OnSCChatKeyInfo(protocol)
	self.data:SetChatWordRewardInfo(protocol)
end

function ChatWGCtrl:SendGetChatWordReward(operate_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChatKeyOperateReq)
	protocol.operate_type = operate_type or 0
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end