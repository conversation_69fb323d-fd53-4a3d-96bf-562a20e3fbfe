require("game/new_festival_activity/new_festival_prayer/new_festival_prayer_view")
require("game/new_festival_activity/new_festival_prayer/new_festival_prayer_wg_data")
require("game/new_festival_activity/new_festival_prayer/new_festival_prayer_items")
require("game/new_festival_activity/new_festival_prayer/new_festival_prayer_buy_view")
require("game/new_festival_activity/new_festival_prayer/new_festival_prayer_advanced_view")
require("game/new_festival_activity/new_festival_prayer/new_festival_prayer_result_view")

NewFestivalPrayerWGCtrl = NewFestivalPrayerWGCtrl or BaseClass(BaseWGCtrl)
function NewFestivalPrayerWGCtrl:__init()
	if NewFestivalPrayerWGCtrl.Instance then
		ErrorLog("[NewFestivalPrayerWGCtrl] Attemp to create a singleton twice !")
	end
	NewFestivalPrayerWGCtrl.Instance = self

	self.data = NewFestivalPrayerWGData.New()
	self.prayer_buy_view = NewFestivalPrayerBuyView.New()
	self.advanced_view = NewFestivalPrayerAdvancedView.New()
	self.result_view = NewFestivalPrayerResultView.New()

	self:RegisterAllProtocols()
end

function NewFestivalPrayerWGCtrl:__delete()
	NewFestivalPrayerWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

	if self.prayer_buy_view then
        self.prayer_buy_view:DeleteMe()
        self.prayer_buy_view = nil
    end

	if self.advanced_view then
        self.advanced_view:DeleteMe()
        self.advanced_view = nil
    end

	if self.result_view then
        self.result_view:DeleteMe()
        self.result_view = nil
    end
end

function NewFestivalPrayerWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCNewFestivalActPrayerAllInfo,"OnSCNewFestivalActPrayerAllInfo")
	self:RegisterProtocol(SCNewFestivalActPrayerInfo,"OnSCNewFestivalActPrayerInfo")
	self:RegisterProtocol(SCNewFestivalActTaskUpdate,"OnSCNewFestivalActTaskUpdate")
end

function NewFestivalPrayerWGCtrl:ReqNewFestivalPrayerInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.NEW_FESTIVAL_ACT_PRAYER
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function NewFestivalPrayerWGCtrl:OnSCNewFestivalActPrayerAllInfo(protocol)
	--print_error("========所有信息========",protocol)
	self.data:SetAllPrayerAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2347)
	RemindManager.Instance:Fire(RemindName.NewFestivalPrayer)
end

function NewFestivalPrayerWGCtrl:OnSCNewFestivalActPrayerInfo(protocol)
	--print_error("========祈愿信息改变========",protocol)
	local old_reward_state = self.data:GetAllRewardFlag()
	self.data:SetAllPrayerInfoUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2347)
	if self.prayer_buy_view:IsOpen() then
		self.prayer_buy_view:Flush()
	end

	if self.advanced_view:IsOpen() then
		self.advanced_view:Flush()
	end
	
	self:CheckNeedPopResultView(old_reward_state)
	RemindManager.Instance:Fire(RemindName.NewFestivalPrayer)
end

function NewFestivalPrayerWGCtrl:OnSCNewFestivalActTaskUpdate(protocol)
	--print_error("========任务数据改变========",protocol)
	self.data:SetPrayerTaskUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2347)
	RemindManager.Instance:Fire(RemindName.NewFestivalPrayer)
end

function NewFestivalPrayerWGCtrl:OpenPrayeBuyView()
	self.prayer_buy_view:Open()
end

function NewFestivalPrayerWGCtrl:OpenAdvancedView()
	self.advanced_view:Open()
end

function NewFestivalPrayerWGCtrl:CheckNeedPopResultView(old_reward_state)
	local reward_list = self.data:GetRewardShowInfo()
	local is_buy_advanced = self.data:GetIsBuyAdvancedFlag()
	local prayer_count = self.data:GetPrayerCount()
	local show_reward_list = {}
	local spe_show_reward_list = {}
	for k, v in ipairs(reward_list) do
		if old_reward_state[v.seq] ~= v.reward_flag then
			if old_reward_state[v.seq] == NEWYEAR_PRAYER_REWARD_FLAG.NOT_GET then
				table.insert(show_reward_list, v.cfg.normal_reward[0])
			end

			if v.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.GET_ADVANCED then
				for index, info in pairs(v.cfg.advanced_reward) do
					table.insert(show_reward_list, info)
				end
			end
		end

		if not is_buy_advanced then
			if prayer_count >= v.cfg.prayer_count then
				for index, info in pairs(v.cfg.advanced_reward) do
					table.insert(spe_show_reward_list, info)
				end
			end
		end
	end

	if IsEmptyTable(show_reward_list) then
		return 
	end

	self.result_view:SetDataAndOpen(show_reward_list, spe_show_reward_list)
end