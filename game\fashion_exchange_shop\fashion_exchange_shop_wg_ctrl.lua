require("game/fashion_exchange_shop/fashion_exchange_shop_wg_data")
require("game/fashion_exchange_shop/fashion_exchange_shop_view")
require("game/fashion_exchange_shop/fashion_exchange_shop_item_render")

-- 时装兑换商城
FashionExchangeShopWGCtrl = FashionExchangeShopWGCtrl or BaseClass(BaseWGCtrl)

function FashionExchangeShopWGCtrl:__init()
	if FashionExchangeShopWGCtrl.Instance then
		error("[FashionExchangeShopWGCtrl]:Attempt to create singleton twice!")
	end
	FashionExchangeShopWGCtrl.Instance = self

	self.data = FashionExchangeShopWGData.New()
	self.view = FashionExchangeShopView.New(GuideModuleName.FashionExchangeShopView)
	self:RegisterAllProtocals()
end

function FashionExchangeShopWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	FashionExchangeShopWGCtrl.Instance = nil
end

function FashionExchangeShopWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSShapeShopOperate)
	self:RegisterProtocol(SCShapeShopInfo, "OnShopItemInfo")
	self:RegisterProtocol(SCShapeShopUpdateInfo, "OnShopBuy")
end

function FashionExchangeShopWGCtrl:SendShapeShopOperate(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShapeShopOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function FashionExchangeShopWGCtrl:OnShopItemInfo(protocol)
	-- print_error("OnShopItemInfo:::", protocol)
	self.data:AllShopInfo(protocol)

	if ViewManager.Instance:IsOpen(GuideModuleName.FashionExchangeShopView) then
		ViewManager.Instance:FlushView(GuideModuleName.FashionExchangeShopView, nil, "OnShopItemInfo")
	end
end

function FashionExchangeShopWGCtrl:OnShopBuy(protocol)
	-- print_error("OnShopBuy:::", protocol)
	self.data:ChangeShopInfo(protocol)

	if ViewManager.Instance:IsOpen(GuideModuleName.FashionExchangeShopView) then
		ViewManager.Instance:FlushView(GuideModuleName.FashionExchangeShopView, nil, "OnShopBuy")
	end
end
