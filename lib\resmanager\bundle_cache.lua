-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local UnityGameObject = UnityEngine.GameObject
local UnityDestroy = UnityGameObject.Destroy
local CustomAssetBundle = require("lib/resmanager/assetbundle")

local M = {}

function M:Init()
	self.last_sweep_time = 0
	self.v_override_cache_times = {}
	self.v_caches = {}
	self.v_refs = {}
	self.v_max_refs = {}
	self.v_last_del_times = {}
	self.v_last_use_times = {}
	self.v_ref_bundle_detail_dic = {}
	self.bundle_lock_num = {}
	self.v_ab_refers_map = {}
end

function M:Clear()
	local del_list = {}
	for k, v in pairs(self.v_caches) do
		if nil == self.bundle_lock_num[k] and (nil == self.v_refs[k] or self.v_refs[k] <= 0) then
			local bundle = self.v_caches[k]
			self.v_refs[k] = nil
			self.v_max_refs[k] = nil
			self.v_last_del_times[k] = nil
			self.v_last_use_times[k] = nil
			self.v_override_cache_times[k] = nil
			bundle:Unload(true)
			table.insert(del_list, k)
		end
	end

	for k,v in pairs(del_list) do
		self.v_caches[v] = nil
	end
end

function M:Update(time, delta_time)
	if time - self.last_sweep_time < 0.1 then
		return
	end

	local now_time = time
	self.last_sweep_time = now_time

	for k, v in pairs(self.v_caches) do
		if nil == self.bundle_lock_num[k]
			and (nil == self.v_refs[k] or self.v_refs[k] <= 0)
			and (nil == self.v_last_del_times[k] or time - self.v_last_del_times[k] >= self:GetCacheTime(k))
			and self:IsRefersAllBeUnload(k) then
			if UNITY_EDITOR then
				print_log("[BundleCache]Unload", k, self:GetCacheTime(k))
			end
			local bundle = self.v_caches[k]
			self.v_caches[k] = nil
			self.v_refs[k] = nil
			self.v_max_refs[k] = nil
			self.v_last_del_times[k] = nil
			self.v_last_use_times[k] = nil
			self.v_override_cache_times[k] = nil
			bundle:Unload(true)
			break
		end
	end
end

-- 外部可通过该接口设置资源释放的基础时间(释放完会清掉)
function M:SetOverrideCacheTime(bundle_name, cache_time)
	if nil == bundle_name or nil == cache_time then
		print_error("[BundleCache] SetOverrideCacheTime big bug, param is invalid", bundle_name, cache_time)
		return
	end

	self.v_override_cache_times[bundle_name] = cache_time
	local deps = ResMgr:GetBundleDeps(bundle_name)
	if nil ~= deps then
		for k,v in pairs(deps) do
			self.v_override_cache_times[v] = cache_time
		end
	end
end

-- 获得缓存的时间
function M:GetCacheTime(bundle_name)
	local cache_time = 5
	if nil ~= self.v_override_cache_times[bundle_name] then
		cache_time = self.v_override_cache_times[bundle_name]
	elseif nil ~= self.v_max_refs[bundle_name] then
		cache_time = cache_time + self.v_max_refs[bundle_name] / 20 * 60
		if cache_time > 60 then cache_time = 60 end
	end

	return cache_time
end

-- 是否所有引用者都已经释放
function M:IsRefersAllBeUnload(bundle_name)
	local refers = self.v_ab_refers_map[bundle_name]
	if nil ~= refers then
		for _, v in pairs(refers) do
			if nil ~= self.v_caches[v] then
				return false
			end
		end
	end
	return true
end

function M:IsBundlRefing(bundle_name)
	if self.v_caches[bundle_name]
		and nil ~= self.v_refs[bundle_name]
		and self.v_refs[bundle_name] > 0 then
		return true
	end

	return false
end

function M:GetCacheRes(bundle_name)
	if nil == self.bundle_lock_num[bundle_name] then
		print_error("[BundleCache] GetCacheRes big bug, the bundle is not lock", bundle_name)
	end
	return self.v_caches[bundle_name]
end

function M:CacheRes(bundle_name, bundle)
	if nil == bundle then
		print_error("[BundleCache] cache bundle fail!", bundle_name)
		return
	end

	if nil ~= self.v_caches[bundle_name] then
		print_error("[BundleCache] cache bundle repeat!", bundle_name)
		return
	end

	self.v_caches[bundle_name] = CustomAssetBundle:new(bundle)
end

-- 使用AssetBundle时，所有依赖包+1
local is_refer_loop = false
function M:OnUseBundle(bundle_name)
	self:AddRef(bundle_name)
	local deps = ResMgr:GetBundleDeps(bundle_name)
	if nil ~= deps then
		local cache = {depth = 0}
		for _, dep in ipairs(deps) do
			-- 记录AB被哪个AB引用，释放AB时一定要引用者先释放
			is_refer_loop = false
			self:AddRefer(bundle_name, dep, 0, cache)
			cache.depth = 0
			self:AddRef(dep)

			if (is_debug or IS_LOCLA_WINDOWS_DEBUG_EXE) and is_refer_loop then
				self:DebugLogBundleLoopDepend(bundle_name, dep)
			end
		end
	end
end

function M:AddRefer(parent_bundle, depend_bundle, depth, cache)
	depth = depth + 1
	if cache ~= nil then
		cache.depth = cache.depth + 1
		if cache.depth >= 500 then
			is_refer_loop = true
			print_error("[BundleCache]该预制体存在巨大BUG！！！！！互相耦合！！！！！马上处理！！！！！", parent_bundle, depend_bundle)
			return
		end
	end

	-- if depth > 20 then
	-- 	print_error("[bundle_cache] AddRefer depth过大", depth, parent_bundle, depend_bundle)
	-- end

	if depth > 30 then
		is_refer_loop = true
		return
	end

	self.v_ab_refers_map[depend_bundle] = self.v_ab_refers_map[depend_bundle] or {}
	self.v_ab_refers_map[depend_bundle][parent_bundle] = parent_bundle

	local deps = ResMgr:GetBundleDeps(depend_bundle)
	if nil ~= deps then
		for _, dep in ipairs(deps) do
			self:AddRefer(depend_bundle, dep, depth, cache)
		end
	end
end

local debug_looped_bundle_map = {}
function M:DebugLogBundleLoopDepend(parent_bundle, child_bundle)
	local stack = {}
	local map = { [parent_bundle] = true }
	local looped_bundle

	local function search(depend_bundle, depth)
		depth = depth + 1
		if depth > 30 then
			return
		end

		table.insert(stack, depend_bundle)
		if map[depend_bundle] then
			looped_bundle = depend_bundle
			return true
		end

		map[depend_bundle] = true

		local deps = ResMgr:GetBundleDeps(depend_bundle)
		if nil ~= deps then
			for _, dep in ipairs(deps) do
				if search(dep, depth) then
					return true
				end
			end
		end

		table.remove(stack)
		map[depend_bundle] = false
	end

	search(child_bundle, 0)

	if not debug_looped_bundle_map[looped_bundle] then
		debug_looped_bundle_map[looped_bundle] = true

		local flag = false
		local str = ""
		for _, v in ipairs(stack) do
			if flag then
				str = str .. " -> " .. v
			end

			if v == looped_bundle and not flag then
				flag = true
				str = str .. looped_bundle
			end
		end

		print_error("【严重错误】AB包出现环形引用！" .. str)
	end
end

function M:OnUnUseBundle(bundle_name)
	self:DelRef(bundle_name)
	local deps = ResMgr:GetBundleDeps(bundle_name)
	if nil ~= deps then
		for _, dep in ipairs(deps) do
			self:DelRef(dep)
		end
	end
end

function M:AddRef(bundle_name)
    if self.v_refs[bundle_name] == nil then
        self.v_refs[bundle_name] = 0
    end

	self.v_refs[bundle_name] = self.v_refs[bundle_name] + 1
	self.v_max_refs[bundle_name] = math.max(self.v_max_refs[bundle_name] or 0, self.v_refs[bundle_name] )
	self.v_last_del_times[bundle_name] = nil
	self.v_last_use_times[bundle_name] = GlobalUnityTime
end

function M:DelRef(bundle_name)
    local ref = self.v_refs[bundle_name]
    if nil == ref then
        return
    end

    if ref <= 0 then
        print_error("[BundleCache] DelRef big bug!!!!, ref < 0", bundle_name, ref)
    end

	self.v_refs[bundle_name] =  self.v_refs[bundle_name] - 1
	self.v_last_del_times[bundle_name] = GlobalUnityTime
end

function M:LockBundles(bundle_list)
    for _, v in ipairs(bundle_list) do
    	self.bundle_lock_num[v] = (self.bundle_lock_num[v] or 0) + 1
    end
end

function M:UnLockBundles(bundle_list)
    for _, v in ipairs(bundle_list) do
        self.bundle_lock_num[v] = (self.bundle_lock_num[v] or 0) - 1
        if self.bundle_lock_num[v] == 0 then
        	self.bundle_lock_num[v] = nil

        elseif self.bundle_lock_num[v] < 0 then
			print_error("[BundleCache] UnLockBundles big bug!!!!, lock num < 0", v, self.bundle_lock_num[v])
        end
    end
end

-- debug
function M:CheckAsetBundleLeak()
	local content = {}
	for k, v in pairs(self.v_last_use_times) do
		if self.v_refs[k] > 0 then
			local ref = self.v_refs[k]
			local timer = math.floor(GlobalUnityTime - v)
			table.insert(content, {text=string.format("ref=%s	last_use=%s	%s %s\n", ref, timer, k, self.v_caches[k] ~= nil), timer=timer, ref=ref})
		end
	end

	SortTools.SortDesc(content, "timer", "ref")
	local out_content = ""
	local tbl = {}
	for k,v in pairs(content) do
		table.insert(tbl, v.text)
	end

	out_content = table.concat(tbl)
	return out_content
end

-- debug
function M:CheckAsetBundleDetailLeak()
	local content = ""
	local tbl = {}

	for k, v in pairs(self.v_last_use_times) do
		if self.v_refs[k] > 0 then
			local s_builder = {}
			local lookup = {}
			self:GetAssetBundleRefInfo(k, s_builder, 0, lookup)
			if #s_builder > 0 then
				for _, s in ipairs(s_builder) do
					table.insert(tbl, s)
					table.insert(tbl, "\n")
				end
				table.insert(tbl, "\n")
			end
		end
	end

	content = table.concat(tbl)
	return content
end

-- deubg
function M:CacheBundleRefDetail(bundle_name, refer)
	if bundle_name == refer then
		return
	end

	local refers = self.v_ref_bundle_detail_dic[bundle_name]
	if nil == refers then
		refers = {}
		self.v_ref_bundle_detail_dic[bundle_name] = refers
	end

	refers[refer] = true
end

-- deubg
function M:GetAssetBundleRefInfo(bundle_name, s_builder, depth, lookup)
	local indent = ""
	for i = 0, depth do
		indent = indent .. "	"
	end

	local begin, _ = string.find(bundle_name, "Asset")
	if nil ~= begin then
		table.insert(s_builder, indent .. bundle_name)
		return
	end

	local refers = self.v_ref_bundle_detail_dic[bundle_name]
	if nil == refers then
		return
	end

	local ref_count = self.v_refs[bundle_name] or 0
	lookup[bundle_name] = true
	local elapse_time = math.floor(GlobalUnityTime - (self.v_last_use_times[bundle_name] or GlobalUnityTime))
	local show_bundle_name = bundle_name
	if 0 == depth then
		show_bundle_name = "[AB]" .. bundle_name
	end

	table.insert(s_builder, string.format("%s%s, ref=%s, last_use_time=%ss", indent, show_bundle_name, ref_count, elapse_time))

	for k,v in pairs(refers) do
		if nil ~= lookup[k] then
			print_error(string.format("出现了包之间的互引用 %s => %s", bundle_name, k))
		else
			self:GetAssetBundleRefInfo(k, s_builder, depth + 1, lookup)
		end
	end

	lookup[bundle_name] = nil
end

function M:GetBundleCount(t)
	t.bundle_count = 0
	for k,v in pairs(self.v_caches) do
		t.bundle_count = t.bundle_count + 1
	end
end

function M:GetBundleRef(bundle_name)
	local ref_count = self.v_refs[bundle_name] or 0
	local detail_content = ""
	if ref_count > 1 then
		local refers = self.v_ab_refers_map[bundle_name]
		if nil ~= refers then
			local s_builder = {}
			for k,_ in pairs(refers) do
				if nil ~= self.v_caches[k] then
					table.insert(s_builder, string.format("\t%s", k))
				end
			end
			detail_content = table.concat(s_builder)
		end
	end

	return ref_count, detail_content
end

function M:OnGameStop()
	for k, v in pairs(self.v_caches) do
		v:Unload(true)
	end
	self.v_caches = {}
end

return M