return {
	actor<PERSON>ontroller = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff2100_rest",
				effectAsset = {
					BundleName = "model/wings/2100_prefab",
					AssetName = "eff2100_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root/eff2100_rest(Clone)/A3_beishi_35@skin/root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "restchu",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "",
						effectAsset = {},

					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}