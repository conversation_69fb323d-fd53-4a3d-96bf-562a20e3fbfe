﻿#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using LuaInterface;
using Nirvana;

public static class CheckController {
    static bool hasChecked = false;
    private static List<CheckObject> list = new List<CheckObject>();
    // 把继承于StaticCheck的脚本在这里实例化
    private static StaticCheck[] staticCheckers = {
        new AssetRecorderChecker(),
    };

    // 把继承于DynamicChecker的脚本在这里实例化
    private static DynamicChecker[] dynamicCheckers = {
        new UIViewDynamicChecker(),
        new ActorDynamicChecker(),
        //new EffectDynamicChecker(),
    };

    public static void Start(LuaState luaState)
    {
        if (hasChecked)
        {
            return;
        }
        hasChecked = true;
        EditorResourceMgr.loadAction += LoadAction;
        StartCheck(0);
    }

	public static void CheckUIView(string bundleName, string assetName)
	{
		dynamicCheckers[0].CheckBundle(bundleName, assetName);
	}

	public static void ClearCheckUIViewList()
	{
		dynamicCheckers[0].ClearShowWindowList();
	}

	public static void Stop()
    {
        EditorResourceMgr.loadAction -= LoadAction;
    }

    private static void StartCheck(int index)
    {
        if (index < staticCheckers.Length)
        {
            var checker = staticCheckers[index];
            checker.StartCheck(() => StartCheck(index + 1));
        }
    }

    private static void LoadAction(string bundleName, string assetName)
    {
        for (int i = 0; i <dynamicCheckers.Length; ++i)
        {
            var checker = dynamicCheckers[i];
            checker.CheckBundle(bundleName, assetName);
        }
    }

    public static void AddLuaWarning(string note, string level)
    {
        if (!hasChecked)
            return;

        WarningLevel warningLevel = WarningLevel.Low;
        if (level == "Normal")
            warningLevel = WarningLevel.Normal;
        else if (level == "High")
            warningLevel = WarningLevel.High;

        CheckObject checkObj = new CheckObject(null, note, warningLevel);
        list.Add(checkObj);
        ShowWindow();
    }

    private static void ShowWindow()
    {
        var window = GetWindow();
        window.Init();
        window.List = list;
        window.Show();
        window.position = new Rect(new Rect(1200, 600, 800, 600));
        window.Focus();
        window.onClose = () =>
        {
            Scheduler.Delay(ShowWindow, 1);
        };
    }

    private static LuaWarningWindow GetWindow()
    {
        return UnityEditor.EditorWindow.GetWindowWithRect<LuaWarningWindow>(new Rect(1200, 600, 800, 600));
    }
}
#endif