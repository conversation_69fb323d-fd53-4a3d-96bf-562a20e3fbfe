-- 新节日活动_活动商店
local tehui_shop_timer_key = "tehui_shop_timer"

function NewFestivalActivityView:LoadIndexCallBackTehuiShop()
    NewFestivalTehuiShopWGCtrl.Instance:SendTehuiShopReq(OA_NEW_FESTIVAL_TEHUI_SHOP_INFO.INFO)
	local bundle = "uis/view/new_festival_activity_ui_prefab"
	local asset = "xianyu_shop_cell"
	if self.xianyu_shop_grid_list == nil then
		self.xianyu_shop_grid_list = AsyncBaseGrid.New()
		self.xianyu_shop_grid_list:CreateCells({col = 4,
								change_cells_num = 1,
								list_view = self.node_list["xianyu_shop_list"],
								assetBundle = bundle,
								assetName = asset,
								itemRender = XianyuShopItemRender})
		self.xianyu_shop_grid_list:SetStartZeroIndex(false)
	end

    if not self.purchase_shop_list then
        self.purchase_shop_list = AsyncListView.New(PurchaseShopItemRender, self.node_list["purchase_shop_list"])
        self.purchase_shop_list:SetStartZeroIndex(false)
    end

    for i = 1, 2 do
		self.node_list["tehui_shop_toggle_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickShopTypeSelect, self, i))
	end
    self:SetTehuiShopImg()
    self:CreateTehuiShopCountDown()
end

function NewFestivalActivityView:ReleaseCallBackTehuiShop()
    if self.xianyu_shop_grid_list then
        self.xianyu_shop_grid_list:DeleteMe()
        self.xianyu_shop_grid_list = nil
    end

    if self.purchase_shop_list then
        self.purchase_shop_list:DeleteMe()
        self.purchase_shop_list = nil
    end

    if CountDownManager.Instance:HasCountDown(tehui_shop_timer_key) then
        CountDownManager.Instance:RemoveCountDown(tehui_shop_timer_key)
    end
end

function NewFestivalActivityView:OnClickShopTypeSelect(type)
	if self.select_type == type then
		return
	end

	self.select_type = type
    self.node_list["tehui_shop_toggle_" .. type].toggle.isOn = true
end

function NewFestivalActivityView:SetTehuiShopImg()
    local tehui_shop_title_bg_bundle, tehui_shop_title_bg_asset = ResPath.GetNewFestivalRawImages("thsd_title")
    self.node_list["tehui_shop_title_bg"].raw_image:LoadSprite(tehui_shop_title_bg_bundle, tehui_shop_title_bg_asset, function ()
        self.node_list["tehui_shop_title_bg"].raw_image:SetNativeSize()
     end)

    local toggle_1_normal_bundle, toggle_1_normal_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1")
    self.node_list["tehui_shop_nor_img1"].image:LoadSprite(toggle_1_normal_bundle, toggle_1_normal_asset)
    self.node_list["tehui_shop_nor_img2"].image:LoadSprite(toggle_1_normal_bundle, toggle_1_normal_asset)

    local toggle_1_hl_bundle, toggle_1_hl_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1_hl")
    self.node_list["tehui_shop_hl_img1"].image:LoadSprite(toggle_1_hl_bundle, toggle_1_hl_asset)
    self.node_list["tehui_shop_hl_img2"].image:LoadSprite(toggle_1_hl_bundle, toggle_1_hl_asset)

    local timer_bg_bundle, timer_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dl_di2")
    self.node_list.tehui_shop_act_timer_bg.image:LoadSprite(timer_bg_bundle, timer_bg_asset, function ()
        self.node_list.tehui_shop_act_timer_bg.image:SetNativeSize()
    end)

    local shop_text_cfg = NewFestivalActivityWGData.Instance:GetTehuiShopTextCfg()
    self.node_list["tehui_shop_toggle_txt1"].text.text = shop_text_cfg.btn_text_1
    self.node_list["tehui_shop_toggle_txt_hl1"].text.text = shop_text_cfg.btn_text_1
    self.node_list["tehui_shop_toggle_txt2"].text.text = shop_text_cfg.btn_text_2
    self.node_list["tehui_shop_toggle_txt_hl2"].text.text = shop_text_cfg.btn_text_2

    self.node_list["tehui_shop_toggle_txt1"].text.color = Str2C3b(shop_text_cfg.btn_text_color_1)
    self.node_list["tehui_shop_toggle_txt2"].text.color = Str2C3b(shop_text_cfg.btn_text_color_1)
    self.node_list["tehui_shop_toggle_txt_hl1"].text.color = Str2C3b(shop_text_cfg.btn_text_color_2)
    self.node_list["tehui_shop_toggle_txt_hl2"].text.color = Str2C3b(shop_text_cfg.btn_text_color_2)
    self.node_list["tehui_shop_act_times"].text.color = Str2C3b(shop_text_cfg.timer_color)
end

function NewFestivalActivityView:OnFlushTehuiShop()
    local purchase_shop_cfg = NewFestivalTehuiShopWGData.Instance:GetPurchaseShopDataList()
    local xianyu_shop_cfg = NewFestivalTehuiShopWGData.Instance:GetXianyuShopDataList()
    self.purchase_shop_list:SetDataList(purchase_shop_cfg)
    self.xianyu_shop_grid_list:SetDataList(xianyu_shop_cfg)
end

function NewFestivalActivityView:CreateTehuiShopCountDown()
    if CountDownManager.Instance:HasCountDown(tehui_shop_timer_key) then
        return
    end

    local time, total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_FESTIVAL_ACT_TEHUI_SHOP)
    if time > 0 then
        self:TehuiShopUpdateCountDown(total_time - time, total_time)
        CountDownManager.Instance:AddCountDown(tehui_shop_timer_key, BindTool.Bind1(self.TehuiShopUpdateCountDown, self), BindTool.Bind1(self.TehuiShopCompleteCallBack, self), nil, time, 1)
    else
        self:TehuiShopCompleteCallBack()
    end
end

function NewFestivalActivityView:TehuiShopUpdateCountDown(elapse_time, total_time)
    if self.node_list and self.node_list.tehui_shop_act_times then
        local shop_text_cfg = NewFestivalActivityWGData.Instance:GetTehuiShopTextCfg()
        local time_part_color = shop_text_cfg and shop_text_cfg.time_part_color or COLOR3B.D_GREEN
        self.node_list.tehui_shop_act_times.text.text = string.format(Language.NewFestivalActivity.ActTime, time_part_color, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
    end
end

function NewFestivalActivityView:TehuiShopCompleteCallBack()
    if self.node_list and self.node_list.tehui_shop_act_times then
        self.node_list.tehui_shop_act_times.text.text = Language.Common.ActivityIsEnd
    end
end








----------------------PurchaseShopItemRender----------------------
PurchaseShopItemRender = PurchaseShopItemRender or BaseClass(BaseRender)
function PurchaseShopItemRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.purchase_shop_special_cell)

    if not self.reward_grid then
        self.reward_grid = AsyncBaseGrid.New()
		local t = {}
		t.col = 3
		t.change_cells_num = 1
		t.itemRender = ItemCell
		t.list_view = self.node_list["purchase_shop_item_list"]
		self.reward_grid:CreateCells(t)
        self.reward_grid:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.purchase_shop_buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))
end

function PurchaseShopItemRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

    if self.reward_grid then
		self.reward_grid:DeleteMe()
		self.reward_grid = nil
	end
end

function PurchaseShopItemRender:OnFlush()
    if not self.data then
        return
    end

    local cost_times = NewFestivalTehuiShopWGData.Instance:GetPurchaseShopTimesBySeq(self.data.seq)
    local shop_text_cfg = NewFestivalActivityWGData.Instance:GetTehuiShopTextCfg()
    if self.data.limit_type == 1 then
        local purchase_shop_cell_bg_bundle, purchase_shop_cell_bg_asset = ResPath.GetNewFestivalRawImages("thsd_bg4")
        self.node_list["purchase_shop_cell_bg"].raw_image:LoadSprite(purchase_shop_cell_bg_bundle, purchase_shop_cell_bg_asset, function ()
            self.node_list["purchase_shop_cell_bg"].raw_image:SetNativeSize()
         end)
        self.node_list.purchase_shop_limit_txt.text.text = string.format(Language.NewFestivalActivity.PurchaseShopLimitBuyTimes, self.data.limit_count - cost_times, self.data.limit_count)
    elseif self.data.limit_type == 2 then
        local purchase_shop_cell_bg_bundle, purchase_shop_cell_bg_asset = ResPath.GetNewFestivalRawImages("thsd_bg3")
        self.node_list["purchase_shop_cell_bg"].raw_image:LoadSprite(purchase_shop_cell_bg_bundle, purchase_shop_cell_bg_asset, function ()
            self.node_list["purchase_shop_cell_bg"].raw_image:SetNativeSize()
         end)
        self.node_list.purchase_shop_limit_txt.text.text = string.format(Language.NewFestivalActivity.PurchaseShopTodayLimitBuyTimes, self.data.limit_count - cost_times, self.data.limit_count)
    end

    self.node_list["purchase_shop_discount_txt"].text.color = Str2C3b(shop_text_cfg.purchase_discount_text_color)
    self.node_list["purchase_shop_limit_txt"].text.color = Str2C3b(shop_text_cfg.purchase_limit_text_color)
    self.node_list["purchase_shop_buy_btn_txt"].text.color = Str2C3b(shop_text_cfg.purchase_shop_buy_btn_txt_color)
    local purchase_shop_discount_bundle, purchase_shop_discount_asset = ResPath.GetNewFestivalActImages("a3_jrhd_bq_zk")
    self.node_list["purchase_shop_discount_img"].image:LoadSprite(purchase_shop_discount_bundle, purchase_shop_discount_asset)

    local purchase_shop_btn_bundle, purchase_shop_btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn1")
    self.node_list["purchase_shop_buy_btn"].image:LoadSprite(purchase_shop_btn_bundle, purchase_shop_btn_asset)
    self.node_list.purchase_shop_discount_txt.text.text = string.format(Language.NewFestivalActivity.Discount, self.data.discount)
    local price = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list.purchase_shop_buy_btn_txt.text.text = price

    XUI.SetButtonEnabled(self.node_list.purchase_shop_buy_btn, not (self.data.limit_count - cost_times <= 0))

    if self.data.limit_count - cost_times <= 0 then
        self.node_list.purchase_shop_buy_btn_txt.text.text = Language.NewFestivalActivity.AllShopBuy
    end
    self.item_cell:SetData({item_id = self.data.reward_item[0].item_id})
    self.reward_grid:SetDataList(self.data.reward_item)
end

function PurchaseShopItemRender:OnClickBuyBtn()
    RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
end









----------------------XianyuShopItemRender----------------------
XianyuShopItemRender = XianyuShopItemRender or BaseClass(BaseRender)
function XianyuShopItemRender:LoadCallBack()
    self.xianyu_shop_item_cell = ItemCell.New(self.node_list.xianyu_shop_item)
    XUI.AddClickEventListener(self.node_list.xianyu_shop_buy_btn, BindTool.Bind1(self.OnClickBuyItemBtn, self))
    XUI.AddClickEventListener(self.node_list.xianyu_shop_item_icon, BindTool.Bind1(self.OnClickOpenIconTipsBtn, self))
end

function XianyuShopItemRender:ReleaseCallBack()
	if self.xianyu_shop_item_cell then
		self.xianyu_shop_item_cell:DeleteMe()
		self.xianyu_shop_item_cell = nil
	end

    if self.xianyu_buy_alert then
        self.xianyu_buy_alert:DeleteMe()
        self.xianyu_buy_alert = nil
    end
end

function XianyuShopItemRender:OnFlush()
    local data = self:GetData()

    if not data then
        return
    end

    local shop_text_cfg = NewFestivalActivityWGData.Instance:GetTehuiShopTextCfg()
    local cost_times = NewFestivalTehuiShopWGData.Instance:GetXianYuShopTimesBySeq(self.data.seq)
    if self.data.limit_type == 1 then
        self.node_list["xianyu_shop_text1"].text.color = Str2C3b(shop_text_cfg.xianyu_limit_text_color_1)
        self.node_list["xianyu_shop_item_num"].text.color = Str2C3b(shop_text_cfg.xianyu_limit_text_color_1)
        local xianyu_shop_cell_bg_bundle, xianyu_shop_cell_bg_asset = ResPath.GetNewFestivalRawImages("thsd_bg2")
        self.node_list["xianyu_shop_cell_bg"].raw_image:LoadSprite(xianyu_shop_cell_bg_bundle, xianyu_shop_cell_bg_asset, function ()
            self.node_list["xianyu_shop_cell_bg"].raw_image:SetNativeSize()
         end)
        self.node_list.xianyu_shop_limit_txt.text.text = string.format(Language.NewFestivalActivity.XianyuShopLimitBuyTimes, self.data.limit_count - cost_times, self.data.limit_count)
    elseif self.data.limit_type == 2 then
        self.node_list["xianyu_shop_text1"].text.color = Str2C3b(shop_text_cfg.xianyu_limit_text_color_2)
        self.node_list["xianyu_shop_item_num"].text.color = Str2C3b(shop_text_cfg.xianyu_limit_text_color_2)
        local xianyu_shop_cell_bg_bundle, xianyu_shop_cell_bg_asset = ResPath.GetNewFestivalRawImages("thsd_bg1")
        self.node_list["xianyu_shop_cell_bg"].raw_image:LoadSprite(xianyu_shop_cell_bg_bundle, xianyu_shop_cell_bg_asset, function ()
            self.node_list["xianyu_shop_cell_bg"].raw_image:SetNativeSize()
         end)
         self.node_list.xianyu_shop_limit_txt.text.text = string.format(Language.NewFestivalActivity.XianyuShopTodayLimitBuyTimes, self.data.limit_count - cost_times, self.data.limit_count)
    end

    self.node_list["xianyu_shop_limit_txt"].text.color = Str2C3b(shop_text_cfg.xianyu_limit_text_color)
    self.node_list["xianyu_shop_discount_txt"].text.color = Str2C3b(shop_text_cfg.xianyu_discount_text_color)
    self.node_list["xianyu_shop_item_price"].text.color = Str2C3b(shop_text_cfg.xianyu_btn_text_color)
    local xianyu_shop_discount_bundle, xianyu_shop_discount_asset = ResPath.GetNewFestivalActImages("a3_jrhd_bq_zk")
    self.node_list["xianyu_shop_discount_img"].image:LoadSprite(xianyu_shop_discount_bundle, xianyu_shop_discount_asset)

    local xianyu_shop_btn_bundle, xianyu_shop_btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn1")
    self.node_list["xianyu_shop_buy_btn"].image:LoadSprite(xianyu_shop_btn_bundle, xianyu_shop_btn_asset)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item[0].item_id)
    local cost_item_cfg = ItemWGData.Instance:GetItemConfig(self.data.free_item[0].item_id)
    if item_cfg == nil or cost_item_cfg == nil then
        return
    end
    XUI.SetButtonEnabled(self.node_list.xianyu_shop_buy_btn, not (self.data.limit_count - cost_times <= 0))
    self.node_list.xianyu_shop_item_name.text.text = item_cfg.name
    self.node_list.xianyu_shop_item_icon.image:LoadSprite(ResPath.GetItem(cost_item_cfg.icon_id))
    self.node_list.xianyu_shop_discount_txt.text.text = string.format(Language.NewFestivalActivity.Discount, self.data.discount)
    self.node_list.xianyu_shop_item_num.text.text = self.data.free_item[0].num
    self.node_list.xianyu_shop_item_price.text.text = self.data.xianyu_price
    self.node_list.is_buy_sold:SetActive(self.data.limit_count - cost_times <= 0)
    self.xianyu_shop_item_cell:SetData({item_id = self.data.reward_item[0].item_id})
    self.xianyu_shop_item_cell:MakeGray(self.data.limit_count - cost_times <= 0)
end

function XianyuShopItemRender:OnClickBuyItemBtn()
	if nil == self.xianyu_buy_alert then
		self.xianyu_buy_alert = Alert.New(nil, nil, nil, nil, true)
		self.xianyu_buy_alert:SetCheckBoxDefaultSelect(false)
	end

    self.xianyu_buy_alert:SetLableString(string.format(Language.NewFestivalActivity.CostAlertFormat2, self.data.xianyu_price))
    self.xianyu_buy_alert:SetOkFunc(function()
        NewFestivalTehuiShopWGCtrl.Instance:SendTehuiShopReq(OA_NEW_FESTIVAL_TEHUI_SHOP_INFO.XIANYU_BUY, self.data.seq)
    end)
	self.xianyu_buy_alert:Open()
end

function XianyuShopItemRender:OnClickOpenIconTipsBtn()
    local cost_item_cfg = ItemWGData.Instance:GetItemConfig(self.data.free_item[0].item_id)
    if cost_item_cfg == nil then
        return
    end
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cost_item_cfg.icon_id})
end

