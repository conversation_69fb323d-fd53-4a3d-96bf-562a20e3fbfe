using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using Unity.VisualScripting;
using UnityEditor;
using UnityEngine;

namespace ProceduralLOD
{
    
    [CustomEditor(typeof(TreeLODGenerateHelper))]
    public class TreeLODGenerateHelperEditor : LODGenerateHelperEditor
    {
        protected override void ReductionStrengthItemGUI(int i, float reductionStrength)
        {
            if (i == this.m_Component.reductionStrengths.Length - 1)
            {
                EditorGUILayout.LabelField($"LOD {i + 1}  BillBoard");
            }
            else
            {
                base.ReductionStrengthItemGUI(i, reductionStrength);
            }
        }
    }
}