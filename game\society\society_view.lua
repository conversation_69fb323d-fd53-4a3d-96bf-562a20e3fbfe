SocietyView = SocietyView or BaseClass(SafeBaseView)


SocietyView.Tab_M = TabIndex.society_mail     --10
SocietyView.Tab_F = TabIndex.society_friend   --20
SocietyView.Tab_E = TabIndex.society_enemy    --30  --仇人
SocietyView.Tab_BL = TabIndex.society_buyu    --50  --黑名单

SocietyView.INFO = TabIndex.master_info       --41  --师徒1
SocietyView.GF = TabIndex.master_worship      --42  --师徒2

SocietyView.WXSJ = TabIndex.master_wuxingshenjiang
SocietyView.FB = TabIndex.master_fb
SocietyView.BOSS = TabIndex.master_boss
SocietyView.EQ = TabIndex.master_equip
SocietyView.WASH = TabIndex.master_wash
SocietyView.RL = TabIndex.master_recycle
SocietyView.Tab_B = TabIndex.society_tab_b  --捕鱼

function SocietyView:__init()
	self.view_name = GuideModuleName.Society
	self.name = "societyView"
	self.is_visable = true
	self.is_align_right = true						-- 是否向右对齐
	self.is_safe_area_adapter = true
	--self.is_write_mail = false
	self:SetMaskBg()

	local bundle = "uis/view/society_ui_prefab"
	self:AddViewResource(0, bundle, "layout_panel")
	self:AddViewResource(0, bundle, "VerticalTabbar")
	self:AddViewResource(SocietyView.Tab_M, "uis/view/society_ui_prefab", "layout_mail_base")
	self:AddViewResource(SocietyView.Tab_F, "uis/view/society_ui_prefab", "layout_friend")
	self.is_modal = true
	-- print_error(SocietyView.Tab_M)
	-- if SocietyWGCtrl.Instance.is_main_mail_icon_open then
	--  	self.default_index = SocietyView.Tab_M
	--  	SocietyWGCtrl.Instance.is_main_mail_icon_open = false
	-- else
	-- 	self.default_index = SocietyView.Tab_F
	-- end
	self.auto_jsyq = true --自动接受邀请



	--发送邮件物品数据
	--self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)

	self.select_bag_cell = nil
	self.m_wupin_list = {}
	self.m_send_item_list = {}
	self.lifeskill_type = 0 	--当前生活技能类型
	self.cur_equip_index = 0

	self.layout_friend_base = nil
	self.layout_mail_base = nil

	self.ignore_def_select_friend = false
	self.is_open_from_lyl = false
	self.from_lyl_role_id = nil
	-- self:__InitFishPondView()
end

function SocietyView:IsMailViewOpen()
	return self.layout_mail_base ~= nil
end

function SocietyView:IsFriendViewOpen()
	return self.layout_friend_base ~= nil
end

function SocietyView:__delete()
	if self.face_view then
		self.face_view:DeleteMe()
		self.face_view = nil
	end
end

function SocietyView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:DeleteMailView()
	-- self:DeleteTeamView()
	self:DeleteFriendView()
	self:DeleteEnemyView()
	self:DeleteBlackView()

	-- self:DeleteSpaceView()
	-- self:DeleteLifeSkillView()
	-- self:DeleteHuoliExchangeView()
	-- self:DeleteZhufuView()

	if self.task_btn_effect then
		self.task_btn_effect:DeleteMe()
		self.task_btn_effect = nil
	end
	if self.task_btn_effect2 then
		self.task_btn_effect2:DeleteMe()
		self.task_btn_effect2 = nil
	end

	-- self:DeleteMaterView()
	RemindManager.Instance:UnBind(self.remind_call_back)

	 self:DeleteFishPondView()
	 self:ShopViewDelete()

	 if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.hu_moing then
		GlobalTimerQuest:CancelQuest(self.hu_moing)
		self.hu_moing = nil
	end
end

function SocietyView:LoadCallBack()
	if not self.tabbar then
		local remind_tab = {
		    {RemindName.SocietyFriendsInfo},
		    {RemindName.ScoietyMail},
		}
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:SetVerTabbarIconStr("society") --设置图标
		self.tabbar:Init(Language.Society.TabGrop, nil, "uis/view/society_ui_prefab", nil, remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Society, self.tabbar)

	-- self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.Society]
	self:SetAccordionData()

	-- if not self.money_bar then
	-- 	self.money_bar = MoneyBar.New()
	-- 	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	-- 	local show_params = {
    --     show_gold = true, show_bind_gold = true,
    --     show_coin = true, show_silver_ticket = true,
    --     }
    --     self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	-- 	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	-- end

	self.remind_call_back = BindTool.Bind(self.RemindChange, self)
	RemindManager.Instance:Bind(self.remind_call_back, RemindName.AntiFraudCartoonRemind)
end


function SocietyView:LoadIndexCallBack(index)
	if index == SocietyView.Tab_M then
		self:InitMailView()
	elseif index == SocietyView.Tab_F then
		self:InitFriendView()
	elseif index == SocietyView.INFO then
		--self:InitInfoView()
	elseif index == SocietyView.GF then
		--self:InitWorshipView()
	elseif index == SocietyView.Tab_E then
		--self:InitEnemyView()
	elseif index == SocietyView.Tab_BL then
		--self:InitBlackView()
	elseif index == SocietyView.FB then
		--self:InitFbView()
	elseif index == SocietyView.Tab_B then

		-- self:InitFishPondView()
	elseif index == SocietyView.WXSJ then
		--self:InitWxsjView()
	elseif index == SocietyView.BOSS then
		--self:InitBossView()
	elseif index == SocietyView.EQ then
		-- self:InitEquipView()
		-- self:CreateRoleInfoWidget()
	elseif index == SocietyView.WASH then
		--self:InitWashView()
		-- self:CreateRoleInfoWidget()
	elseif index == SocietyView.RL then
		--self:InitReycleView()
	end
end


function SocietyView:OpenCallBack()
	-- print_error("--------------------OpenCallBack")
	self.is_get_mail_list = true
	--self:LoadMailList()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	--ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	--self:Flush(SocietyView.Tab_M, "mail_list")
	-- MasterWGCtrl.Instance:SendShituInfo()
	-- MasterWGCtrl.Instance:SendShituPataFbInfo()
	-- MasterWGCtrl.Instance:SendShituEquipInfo()
	-- MasterWGCtrl.Instance:SendShituWashInfo()
	-- MasterWGCtrl.Instance:SendShituWashInfo()
	MasterWGCtrl.Instance:SendWuXingShenJiangOperate()
	-- MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.MAIL)
	RankWGCtrl.Instance:SendGetPersonRankListReq(PersonRankType.ZhanLi, function(rank_type, list,protocol)
		self.my_chat_zhanli_rank = protocol.self_rank
	end)

	SocietyWGCtrl.Instance:SendCrossEnemyReq(CS_CROSS_FRIEND_REQ_TYPE.CS_CROSS_FRIEND_REQ_ENEMY_INFO)
end

--刷新相应界面
function SocietyView:OnFlush(param_t, index)
	-- if SocietyWGCtrl.Instance.is_main_mail_icon_open == true then
	-- self:ChangeToIndex(SocietyView.Tab_M)
	-- SocietyWGCtrl.Instance.is_main_mail_icon_open = false
	-- 	return
	-- end

	--
--	print_error("1323123213123213112321",param_t, index)
	if not self:IsLoadedIndex(index) then return end
	for k,v in pairs(param_t) do
		--print_error("OnFlush",param_t, index,k,v)
		if index == MASTER_TAB_TYPE.INFO then
			--self:FlushInfoView()
		-- elseif index == MASTER_TAB_TYPE.GF then
			--self:FlushWorshipView()
		-- elseif index == MASTER_TAB_TYPE.FB then
			---self:FlushFbView()
		-- elseif index == MASTER_TAB_TYPE.WXSJ then
			--self:FlushWxsjView()
		-- elseif index == MASTER_TAB_TYPE.BOSS then
			--self:FlushBossView()
		-- elseif index == MASTER_TAB_TYPE.EQ then
			-- self:FlushEquipView()
		-- elseif index == MASTER_TAB_TYPE.WASH then
			--self:FlushWashView()
		-- elseif index == MASTER_TAB_TYPE.RL then
			--self:FlushReycleView()
		elseif index == SocietyView.Tab_F then
			if "friend_list" == k then
				
			elseif k == "update_msg" then
				self:UpdateMsg()
			end
			--print_error("SocietyView:OnFlush", k, v[1])
			if k =="delete_friend" then
				self:FriendDeleteHandle(v.uid)
			end
			if "find_role_id" == k then
				self:OpenViewFormRoleId(v[1])
			end
			if "find_role_id_form_add_friend" == k then
				self:OpenViewFormAddFriend(v[1])
			end
			if "liao_yi_liao" == k then
				self:OpenViewFromLiaoYiLiao(v[1], v[2])
			end
			self:FlushFriendView()
		elseif index == SocietyView.Tab_M then
			if self:IsMailViewOpen() then
				self:FlushMailView()
			end
		-- elseif "enemy_list" == k and index == SocietyView.Tab_E then
			--self:FlushEnemyView()
		-- elseif "black_list" == k and index == SocietyView.Tab_BL then
			--print_error("mmmmmmmmmm")
			--self:FlushBlackView()
		-- elseif "open_write_mail" == k then
			-- SocietyWGData.Instance:SetIsWriteMailBool(true)
			-- self:IOpenWriteMail(v.name)
			-- SocietyWGData.Instance:SetIsWriteMailBool()
			--print_error("写邮件++++++++++++")
		-- elseif index == SocietyView.Tab_B then
			--self:FlushFishPondView(param_t)

		-- elseif "out_of_team" == k then
			--self:OutOfTeam(v.is_clear, v.protocol)
		-- elseif k == "master_equip_change" then
			-- self.role_info_widget:UpdateEquipGrids()
			-- if index == MASTER_TAB_TYPE.WASH then
			-- 	self.role_info_widget:SelectCellByIndex(self.cur_equip_index)
			-- end
		-- elseif k =="team_change" then
			-- self:UpdateTeam()
		-- elseif k == "team_out" then
			-- self:TeammateLeaveTeam()
		-- elseif k == "item_tips_callback" then
			-- self:HandleItemTipCallBack(v.data, v.handle_type, v.handle_param_t)
		-- elseif k == "boss_num" then
			-- self:RefreshCurLayerInfo()
		-- elseif k == "suc" then
			-- self:MasterUpLevelSuc()
		end
	end

end


--btn单击事件绑定
function SocietyView:BtnBindEven(btn_name, OnClickHandler)
	if nil == OnClickHandler then
		Log("绑定单击事件为空")
		return
	end
	if nil ~= btn_name and nil ~= self.node_t_list[btn_name] then
		self.node_t_list[btn_name].node:addClickEventListener(BindTool.Bind1(OnClickHandler, self))
	end
end

--设置ui是否可见
function SocietyView:SetUiVisible(ui_name,flag)
	-- if nil ~= ui_name and nil ~= self.node_t_list[ui_name] then
	-- 	self.node_t_list[ui_name].node:setVisible(flag)
	-- end
end

function SocietyView:ShowIndexCallBack(index)
	if index == SocietyView.Tab_F then
		self:OpenViewFromMainIcon(true)
	end

	self:Flush(index)
	self.old_index = index
end

function SocietyView:SetIgnoreDefSelectFriend(value, role_id)
	self.ignore_def_select_friend = value
	self.from_lyl_role_id = role_id
end

function SocietyView:Close()
	self.ignore_def_select_friend = false
	self.is_open_from_lyl = false
	self.from_lyl_role_id = nil

	SocietyWGCtrl.Instance.is_friend_chat_panel = false
	SocietyWGData.Instance:RemoveAllSelectMail()
	if self.node_list.chckmark_all_icon then
		self.node_list.chckmark_all_icon:SetActive(false)
	end
	SocietyWGData.Instance:SetIsDanXuan(false)
	SocietyWGCtrl.Instance:SetMailMainUIViewIcon()
	SocietyWGCtrl.Instance:SetFriendChatMainUIViewIcon()
	SafeBaseView.Close(self)
end

function SocietyView:CloseCallBack(is_all)
	PlayerPrefsUtil.Save()
end

-- 选择打开指定选项卡
function SocietyView:SelectIndex(index)
	self:ChangeToIndex(index)
end

--设置选中第几个,从1开始
function SocietyView:SelectTabCallback(index)
	self:ChangeToIndex(index)
end

function SocietyView:GetGuideUiCallBack(ui_name, ui_param)

end


function SocietyView:TaskBtnEffect(btn_node, effect_id)

end

function SocietyView:TaskBtnEffect2(btn_node, effect_id)

end

function SocietyView:GetAlertWindow()
	if nil == self.alert_window then
		self.alert_window = Alert.New()
	end
	return self.alert_window
end

function SocietyView:DestoryAlertWindow()
	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end


function SocietyView:ShowHuMoAnimator()
	local bundle_name2, asset_name2 = ResPath.GetEffectUi(Ui_Effect.UI_xbcg)
	EffectManager.Instance:PlayAtTransform(bundle_name2, asset_name2, self.node_list.humo_effect.transform, 2, nil, nil, nil)
end
