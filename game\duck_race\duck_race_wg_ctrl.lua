require("game/duck_race/duck_race_wg_data")
require("game/duck_race/duck_race_view")
require("game/duck_race/duck_race_bet_view")
require("game/duck_race/duck_race_results_view")
require("game/duck_race/duck_race_fight_view")
require("game/duck_race/duck_select_tips")
require("game/duck_race/duck_details_tips")
require("game/duck_race/barrage_view")
require("game/duck_race/duck_race_dialog_view")
require("game/duck_race/duck_race_results_vote_view")

-- 小鸭疾走
DuckRaceWGCtrl = DuckRaceWGCtrl or BaseClass(BaseWGCtrl)

function DuckRaceWGCtrl:__init()
	if DuckRaceWGCtrl.Instance ~= nil then
		ErrorLog("[DuckRaceWGCtrl] attempt to create singleton twice!")
		return
	end

	DuckRaceWGCtrl.Instance = self
	self.data = DuckRaceWGData.New()
	self.view = DuckRaceView.New(GuideModuleName.DuckRace) 									-- 小鸭疾走主view
	self.fight_view = DuckRaceFightView.New(GuideModuleName.DuckRaceFight) 					-- 小鸭疾走场景面板
	self.duck_select_tips = DuckSelectTips.New() 											-- 鸭子选择tips
	self.duck_details_tips = DuckDetailsTips.New() 											-- 鸭子详情Tips
	self.barrage_view = BarrageView.New(GuideModuleName.BarrageView) 						-- 弹幕面板
	self.duck_race_dialog_view = DuckRaceDialogView.New(GuideModuleName.DuckDialog) 		-- 小鸭对话
	self.duck_race_results_vote_view = DuckRaceResultsVoteView.New(GuideModuleName.DuckRaceResultsVoteView) 		-- 结算投票

	self:RegisterAllProtocols()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.OnMainUIOpen, self))
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function DuckRaceWGCtrl:__delete()
	ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
	self.activity_change_callback = nil

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.fight_view:DeleteMe()
	self.fight_view = nil

	self.duck_select_tips:DeleteMe()
	self.duck_select_tips = nil

	self.duck_details_tips:DeleteMe()
	self.duck_details_tips = nil

	self.duck_race_dialog_view:DeleteMe()
	self.duck_race_dialog_view = nil

	self.duck_race_results_vote_view:DeleteMe()
	self.duck_race_results_vote_view = nil

	DuckRaceWGCtrl.Instance = nil
end

function DuckRaceWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossRacingOpera) 													-- 操作

	self:RegisterProtocol(SCCrossRacingRoundInfo, "OnSCCrossRacingRoundInfo")  					-- 轮次信息
	self:RegisterProtocol(SCCrossRacingPlayerInfo, "OnSCCrossRacingPlayerInfo")  				-- 玩家个人信息
	self:RegisterProtocol(SCCrossRacingDuckInfo, "OnSCCrossRacingDuckInfo")  					-- 所有鸭子信息
	self:RegisterProtocol(SCCrossRacingResultInfo, "OnSCCrossRacingResultInfo") 				-- 结算信息（记录着每轮获胜的鸭子信息）
	self:RegisterProtocol(SCCrossRacingRacingLiveInfo, "OnSCCrossRacingRacingLiveInfo") 		-- 比赛实况信息（左侧面板）
	self:RegisterProtocol(SCCrossRacingDuckOperaResult, "OnSCCrossRacingDuckOperaResult") 		-- 鸭子操作结果返回（干扰、鼓励等）
	self:RegisterProtocol(SCCrossRacingDuckEvent, "OnSCCrossRacingDuckEvent") 					-- 鸭子随机事件通知
end

-- 操作（对应枚举CROSS_RACING_OPERA）
function DuckRaceWGCtrl:SendCSCrossRacingOpera(op_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossRacingOpera)
	protocol.opera_type = op_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-- 请求获取所有信息
function DuckRaceWGCtrl:SendGetAllInfo()
	self:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_QUERY_INFO)
end

-- 请求领取应援币
function DuckRaceWGCtrl:SendFetchPlayCoin()
	self:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_FETCH_PLAY_COIN)
end

-- 请求鼓舞鸭子
function DuckRaceWGCtrl:SendInspireDuck(duck_index)
	self:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_INSPIRE_DUCK, duck_index)
end

-- 请求干扰鸭子
function DuckRaceWGCtrl:SendDisturbDuck(duck_index)
	self:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_DISTURB_DUCK, duck_index)
end

-- 下注一只鸭子
function DuckRaceWGCtrl:SendBetOneDuck(duck_index)
	self:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_BET_ONE_DUCK, duck_index)
end

-- 设置跟随速度
local last_send_speed = 0
local last_send_time = 0
function DuckRaceWGCtrl:SendMainRoleFollowSpeed(move_speed)
	if math.abs(move_speed - last_send_speed) > 50 and UnityEngine.Time.unscaledTime - last_send_time > 1 then
		last_send_speed = move_speed
		last_send_time = UnityEngine.Time.unscaledTime
		self:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_CHANGE_MOVE_SPEED, move_speed)
	end
end

-- 请求进入小鸭疾走场景
function DuckRaceWGCtrl:SendEnterScene()
	--print_error("请求进入小鸭疾走场景")
	if SceneType.KF_HotSpring == Scene.Instance:GetSceneType() then
		self:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_ENTER_FROM_HOTSPRING)
	else
		CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.KF_DUCK_RACE)
	end
end

-- 轮次信息
function DuckRaceWGCtrl:OnSCCrossRacingRoundInfo(protocol)
	-- 该变量禁止除CheckRoundState以外的地方调用，去Data层取
	self._round_protocol_info = protocol
	self:CheckRoundState()
	if protocol.racing_begin_open_view_flag == 1 then
		-- 表示刚进入比赛阶段
		self:OnRoundStateChange(DUCK_RACE_ROUND_STATE.RACING)
	end
end

-- 轮次信息更变
function DuckRaceWGCtrl:OnRoundInfoChange(protocol)
	local old_state = self.data:GetCurRoundState()
	-- 如果三轮都结束了
	if protocol.round == 0 then
		ViewManager.Instance:Close(GuideModuleName.DuckRace)
	end
	self.data:SetRoundInfo(protocol)

	if self.data:GetCurRoundState() == DUCK_RACE_ROUND_STATE.BET then
		if old_state == DUCK_RACE_ROUND_STATE.RESULT then 				-- 旧状态是结算状态
			self:OnRoundStateChange(DUCK_RACE_ROUND_STATE.BET)
		end
	end

	ViewManager.Instance:FlushView(GuideModuleName.DuckRaceFight)
	ViewManager.Instance:FlushView(GuideModuleName.DuckRace, TabIndex.duck_race_bet)
	RemindManager.Instance:Fire(RemindName.DuckRaceFetchRemind)
end

-- 个人信息
function DuckRaceWGCtrl:OnSCCrossRacingPlayerInfo(protocol)
	self.data:SetPlayerInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DuckRace)
	RemindManager.Instance:Fire(RemindName.DuckRaceFetchRemind)
end

-- 所有鸭子信息
function DuckRaceWGCtrl:OnSCCrossRacingDuckInfo(protocol)
	self.data:SetAllDuckInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DuckRaceFight)
	ViewManager.Instance:FlushView(GuideModuleName.DuckRace, TabIndex.duck_race_bet)
end

-- 结算信息
function DuckRaceWGCtrl:OnSCCrossRacingResultInfo(protocol)
	self.data:SetReusltsInfo(protocol)
	self:CheckRoundState()
	if protocol.open_view_flag == 1 then
		self:OnRoundStateChange(DUCK_RACE_ROUND_STATE.RESULT)
	end
	ViewManager.Instance:FlushView(GuideModuleName.DuckRace, TabIndex.duck_race_results)
end

-- 检查是否需要切换到结算状态
function DuckRaceWGCtrl:CheckRoundState()
	-- 如果当前轮次状态是比赛阶段，检测到当前轮次是否已经产生了冠军，如果产生了则进入到结算阶段（服务端不好做，客户端来切状态）
	local cur_round_winner_info = self.data:GetRoundDuckWinner(self.data:GetCurRound())
	-- 如果有冠军了
	if cur_round_winner_info and cur_round_winner_info.index >= 0 then
		-- 如果处于比赛阶段
		if self.data:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RACING then
			-- 切到结算阶段
			self._round_protocol_info.round_state = DUCK_RACE_ROUND_STATE.RESULT
			self:OnRoundInfoChange(self._round_protocol_info)
			return
		end
	end
	self:OnRoundInfoChange(self._round_protocol_info)
end

-- 比赛实况信息
function DuckRaceWGCtrl:OnSCCrossRacingRacingLiveInfo(protocol)
	self.data:SetRaceLiveInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DuckRaceFight)
end

-- 干扰或鼓励鸭子结果
function DuckRaceWGCtrl:OnSCCrossRacingDuckOperaResult(protocol)
	-- 如果是主角的操作结果，则弹出对话面板
	if protocol.op_user_obj_id == GameVoManager.Instance:GetMainRoleVo().obj_id then
		local dialog_content = DuckRaceWGData.Instance:GetDuckDialog(protocol.opera_type, protocol.opera_res, protocol.duck_index)
		self:OpenDuckDialogView(protocol.duck_index, dialog_content)
		-- 主角弹幕
		self:OpenBarrageView(dialog_content)
	else
		-- 其他人弹幕
		local desc = self.data:GetBarrageDesc(protocol.opera_type, protocol.opera_res, protocol.op_user_name, protocol.duck_index)
		self:OpenBarrageView(desc)
	end

	-- 鸭子气泡
	local duck_info = self.data:GetDuckInfoByIndex(protocol.duck_index)
	if duck_info then
		local bubble_text = self.data:GetDuckBubble(protocol.opera_type, protocol.opera_res, protocol.duck_index)
		if bubble_text ~= "" then
			self:ShowDuckBubble(duck_info.duck_objid, bubble_text)
		end
	end

end

-- 小鸭遭遇的随机事件通知
function DuckRaceWGCtrl:OnSCCrossRacingDuckEvent(protocol)
	local event_cfg = nil
	if protocol.event_type ==  CROSS_RACING_EVENT_TYPE.CROSS_RACING_EVENT_TYPE_RAND then
		event_cfg = self.data:GetEventCfgById(protocol.event_id)
	elseif protocol.event_type ==  CROSS_RACING_EVENT_TYPE.CROSS_RACING_EVENT_TYPE_SHORTCUT then
		event_cfg = self.data:GetShortcutEventCfgById(protocol.event_id)
	end
	if event_cfg and event_cfg.duck_qipao1 ~= "" then
		self:ShowDuckBubble(protocol.duck_objid, event_cfg.duck_qipao1)
	end
end

-- 显示鸭子气泡
function DuckRaceWGCtrl:ShowDuckBubble(duck_objid, str)
	local monster_obj = Scene.Instance:GetObj(duck_objid)
	if monster_obj then
		monster_obj:ShowTextToBubble(str, 4)
	end
end

-- 跟随自己下注最多的鸭子
function DuckRaceWGCtrl:FollowMyMaxBetCountDuck()
	local max_bet_duck_index = DuckRaceWGData.Instance:GetSelfMaxBetDuckIndex()
	DuckRaceWGData.Instance:SetFollowDuckIndex(max_bet_duck_index)
end

-- 打开鸭子选择Tips
function DuckRaceWGCtrl:OpenDuckSelectTips(select_type, select_callback, world_pos, child_alignment, special_pos)
	self.duck_select_tips:SetData(select_type, select_callback, world_pos, child_alignment, special_pos)
	self.duck_select_tips:Open()
end

-- 关闭鸭子选择Tips
function DuckRaceWGCtrl:CloseSelectTips()
	self.duck_select_tips:Close()
end

-- 打开鸭子详情Tips
function DuckRaceWGCtrl:OpenDuckDetailsTips(duck_info)
	self.duck_details_tips:SetData(duck_info)
	self.duck_details_tips:Open()
end

-- 关闭鸭子详情Tips
function DuckRaceWGCtrl:CloseDuckDetailsTips()
	self.duck_details_tips:Close()
end

-- 打开弹幕面板(其他系统也有用到)
function DuckRaceWGCtrl:OpenBarrageView(str, is_emoji_text, is_self_barrage)
	is_emoji_text = is_emoji_text or false
	self.barrage_view:AddBarrageText({text = str, is_emoji_text = is_emoji_text, is_self_barrage = is_self_barrage})
end

-- 打开询问玩家是否跟随的tips
function DuckRaceWGCtrl:OpenFollowTips()
	if self.data:GetCurRoundState() ~= DUCK_RACE_ROUND_STATE.RACING then 
		return
	end

	local _, max_value = DuckRaceWGData.Instance:GetSelfMaxBetDuckIndex()
	if max_value > 0 then
		TipWGCtrl.Instance:OpenAlertTips(Language.DuckRace.NeedFollowTips, function()
			if self.data:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RACING then
				self:FollowMyMaxBetCountDuck()
				ViewManager.Instance:Close(GuideModuleName.DuckRace)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.DuckRace.CurRoundRacingEnd)
			end
		end)
	end
end

-- 打开鸭子对话面板
function DuckRaceWGCtrl:OpenDuckDialogView(duck_index, content)
	if not self.duck_race_dialog_view:IsOpen() then
		self.duck_race_dialog_view:SetData(duck_index, content)
		self.duck_race_dialog_view:Open()
	end
end

-- 主界面加载完毕
function DuckRaceWGCtrl:OnMainUIOpen()

end

-- 活动信息改变
function DuckRaceWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	-- 跨服温泉与小鸭疾走同时开启的时候优先显示小鸭疾走
	if activity_type == ACTIVITY_TYPE.KF_DUCK_RACE and ACTIVITY_STATUS.OPEN == status then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.KF_HOTSPRING, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 0) 					-- 关闭跨服温泉活动
	elseif activity_type == ACTIVITY_TYPE.KF_HOTSPRING and ACTIVITY_STATUS.CLOSE ~= status then
		if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_DUCK_RACE) then
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.KF_HOTSPRING, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 0) 			
		end	
	end
end

-- 弹通知和面板用
function DuckRaceWGCtrl:OnRoundStateChange(new_state)
	-- 如果进入比赛阶段
	if new_state == DUCK_RACE_ROUND_STATE.RACING then
		ViewManager.Instance:FlushView(GuideModuleName.DuckRaceFight, nil, "show_race_start") 	-- 比赛开始Tips
		ViewManager.Instance:Close(GuideModuleName.DuckRace)
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ChuiShao)) 	-- 播放音频
		if self.open_follow_tips_timer then
			GlobalTimerQuest:CancelQuest(self.open_follow_tips_timer)
			self.open_follow_tips_timer = nil
		end

		self.open_follow_tips_timer = GlobalTimerQuest:AddDelayTimer(function ()
			if not self.data:GetFollowDuckIndex() then
				self:OpenFollowTips() 															-- 前往跟随弹窗
				self.open_follow_tips_timer = nil
			end
		end, 1.7)
	-- 如果进入结算状态
	elseif new_state == DUCK_RACE_ROUND_STATE.RESULT then
		ViewManager.Instance:FlushView(GuideModuleName.DuckRaceFight, nil, "show_race_end") 	-- 比赛结束Tips

		if self.open_results_timer then
			GlobalTimerQuest:CancelQuest(self.open_results_timer)
			self.open_results_timer = nil
		end

		self.open_results_timer = GlobalTimerQuest:AddDelayTimer(function ()
			if self.data:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RESULT then
				ViewManager.Instance:Open(GuideModuleName.DuckRace, TabIndex.duck_race_results)  -- 结算面板
				ViewManager.Instance:Open(GuideModuleName.DuckRaceResultsVoteView)  -- 结算面板
				self.open_results_timer = nil
			end
		end, 3)

		-- 停止跟随
		self.data:SetFollowDuckIndex(nil)
	elseif new_state == DUCK_RACE_ROUND_STATE.BET then
		ViewManager.Instance:Open(GuideModuleName.DuckRace, TabIndex.duck_race_bet) 			 -- 打开下注面板
	end
end