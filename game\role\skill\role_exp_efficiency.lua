ExpEfficiencyView = ExpEfficiencyView or BaseClass(SafeBaseView)

function ExpEfficiencyView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/role_ui_prefab", "exp_efficiency")
end
function ExpEfficiencyView:__delete()
end

function ExpEfficiencyView:ReleaseCallBack()

end

function ExpEfficiencyView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Skill.Title
	XUI.AddClickEventListener(self.node_list.btn_ck_sure,BindTool.Bind(self.Close,self))
	-- self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(812,539)
	SkillWGCtrl.Instance:SendExpEfficiencyReq()

end

function ExpEfficiencyView:OpenCallBack()
end

function ExpEfficiencyView:FlushView(protocol)
	if not self:IsOpen() then return end
	local totle_add = 0
	for k,v in pairs(protocol) do
		if type(v) == "number" and k ~= 'exp_efficiency'and k ~= 'msg_type' then
			totle_add = totle_add + v
		end
	end
	local totle_add_1 = 0

	self.node_list.add_value.text.text = string.format(Language.Skill.Time,CommonDataManager.ConverExp(protocol.exp_efficiency)) .. string.format(Language.Skill.Time_1,totle_add*100)


	self.node_list.world_extra_percent.text.text  = string.format(Language.Skill.Time_2,protocol.world_extra_percent*100)
	totle_add_1 = totle_add_1 + protocol.world_extra_percent
	self.node_list.vip_extra_percent.text.text  = string.format(Language.Skill.Time_2,protocol.vip_extra_percent*100)
	totle_add_1 = totle_add_1 + protocol.vip_extra_percent
	self.node_list.intimacy_extra.text.text  = string.format(Language.Skill.Time_2,protocol.intimacy_extra*100)
	totle_add_1 = totle_add_1 + protocol.intimacy_extra
	self.node_list.team_extra_exp_per.text.text  = string.format(Language.Skill.Time_2,protocol.team_extra_exp_per*100)
	totle_add_1 = totle_add_1 + protocol.team_extra_exp_per
	self.node_list.rune_extra.text.text  = string.format(Language.Skill.Time_2,protocol.rune_extra*100)
	totle_add_1 = totle_add_1 + protocol.rune_extra
	self.node_list.imp_guard_add_per.text.text  = string.format(Language.Skill.Time_2,protocol.imp_guard_add_per*100)
	totle_add_1 = totle_add_1 + protocol.imp_guard_add_per
	self.node_list.rand_activity_exp_double.text.text  = string.format(Language.Skill.Time_2,protocol.rand_activity_exp_double*100)
	totle_add_1 = totle_add_1 + protocol.rand_activity_exp_double
	self.node_list.base_attr_exp_per.text.text  = string.format(Language.Skill.Time_2,protocol.base_attr_exp_per*100)
	totle_add_1 = totle_add_1 + protocol.base_attr_exp_per
	self.node_list.other_add.text.text  = string.format(Language.Skill.Time_2,(totle_add- totle_add_1)*100)

	local num = SkillWGData.GetExpEfficiency(RoleWGData.Instance.role_vo.level)
	if num then
	self.node_list.base_value.text.text = string.format(Language.Skill.Time,CommonDataManager.ConverExp(num)) .. string.format(Language.Skill.Ji,RoleWGData.Instance.role_vo.level)
	end

end



----------------------------------世界经验弹窗-------------------------------------------------
ExpEfficiencyWorldView = ExpEfficiencyWorldView or BaseClass(SafeBaseView)

function ExpEfficiencyWorldView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/role_ui_prefab", "exp_efficiency_world")
	-- self.isexp_efficiency_world = true
end
function ExpEfficiencyWorldView:__delete()
end

function ExpEfficiencyWorldView:ReleaseCallBack()

end
function ExpEfficiencyWorldView:OpenCallBack()
	SkillWGCtrl.Instance:SendExpEfficiencyReq()--经验效率请求
	-- body
end
function ExpEfficiencyWorldView:LoadCallBack()
end

function ExpEfficiencyWorldView:ShowIndexCallBack()
	self:FlushView()
end

function ExpEfficiencyWorldView:FlushView()
	self.node_list.des_text.text.text = Language.Rank.WorldLevelDesc3
	--显示世界等级
	if self.node_list.des_level then
		local level = RankWGData.Instance:GetWordLevel()
		local is_df,lv = RoleWGData.Instance:GetDianFengLevel(level)
		self.node_list.des_level.text.text = lv or 0
		self.node_list.huo:SetActive(is_df)
	end
	--显示自身等级
	local role_vo = RoleWGData.Instance.role_vo
	local zhuanzhi_level = ConfigManager.Instance:GetAutoConfig('zhuanzhicfg_auto').other[1].role_level_to_prof_level4
	if zhuanzhi_level and role_vo.level > zhuanzhi_level then
		self.node_list.my_level.text.text = string.format(Language.Role.FeiXianDesc,role_vo.level - zhuanzhi_level )
		self.node_list.huo2:SetActive(true)
	else
		self.node_list.my_level.text.text = role_vo.level .. Language.Common.Ji
		self.node_list.huo2:SetActive(false)
	end
	--显示属性加成
	local value = SkillWGData.Instance:GetExpEfficiencyInfo()
	value = value and value.world_extra_percent
	if value and self.node_list.des_value then
		value = value*100
		value = math.ceil(value)
		self.node_list.des_value.text.text = value .. "%"
	end
end
