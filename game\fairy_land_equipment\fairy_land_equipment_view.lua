
-- /jy_gm xianjieequipgmreq:3 index 0 0 --一键凝聚元神
FairyLandEquipmentView = FairyLandEquipmentView or BaseClass(SafeBaseView)
function FairyLandEquipmentView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.is_safe_area_adapter = true
	self.default_index = 0

	local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	--self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
	--self:AddViewResource(TabIndex.fairy_land_eq_god_book, bundle, "layout_god_book_view")
	self:AddViewResource(0, bundle, "layout_fairy_land_eq_common")
	self:AddViewResource(TabIndex.fairy_land_eq_god_body, bundle, "layout_god_body_view")
	self:AddViewResource({ TabIndex.fairy_land_eq_holy_equip,
		TabIndex.fl_eq_forge_strengthen,
		TabIndex.fl_eq_forge_evolve,
		TabIndex.fl_eq_forge_upquality,
	}, bundle, "layout_equip_list_panel")
	self:AddViewResource(TabIndex.fairy_land_eq_holy_equip, bundle, "layout_holy_equip_view")
	self:AddViewResource(TabIndex.fl_eq_forge_strengthen, bundle, "layout_fle_strengthen_view")
	self:AddViewResource(TabIndex.fl_eq_forge_evolve, bundle, "layout_evolve_view")
	self:AddViewResource(TabIndex.fl_eq_forge_upquality, bundle, "layout_upquality_view")
	self:AddViewResource(0, bundle, "VerticalTabbar")
	self:AddViewResource(0, bundle, "HorizontalTabbar")
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")
	--self:AddViewResource(0, bundle, "layout_fairy_land_eq_base")
end

function FairyLandEquipmentView:__delete()

end

function FairyLandEquipmentView:OpenCallBack()
	self.tabbar_jump_remind = true

	-- if self.model_display then
	-- 	self.model_display:PlayLastAction()
	-- end
end

function FairyLandEquipmentView:CloseCallBack()
	self.tabbar_jump_remind = nil
	self.wait_jump_flush = nil
end

function FairyLandEquipmentView:LoadCallBack()
	self.is_jump_index = false
	self.jump_to_page = 0
	if not self.flem_tabbar then
		self.select_slot_index = -1
		self.old_show_index = nil
		self.select_equip_part = -1
		local hor_tab = { Language.FairyLandEquipment.HorTabGrop }
		self.flem_tabbar = FairyLandEquipmentTabbar.New(self.node_list)
		self.flem_tabbar:SetSelectCallback(BindTool.Bind(self.ToSelectTabIndex, self))
		self.flem_tabbar:SetGBodySelectCallback(BindTool.Bind(self.OnSelectGodBodyCB, self))
		self.flem_tabbar:InitList(Language.FairyLandEquipment.VerTabGrop1, hor_tab)
		-- FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.FairyLandEquipmentView, self.flem_tabbar)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
		}

		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	-- if nil == self.model_display then
	-- 	self.model_display = RoleModel.New()
	-- 	local display_data = {
	-- 		parent_node = self.node_list["display_root"],
	-- 		camera_type = MODEL_CAMERA_TYPE.BASE,
	-- 		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
	-- 		rt_scale_type = ModelRTSCaleType.L,
	-- 		can_drag = true,
	-- 	}
		
	-- 	self.model_display:SetRenderTexUI3DModel(display_data)

	-- 	-- self.model_display:SetUI3DModel(self.node_list["display_root"].transform,
	-- 	-- 	self.node_list["display_root"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
	-- 	self:AddUiRoleModel(self.model_display)
	-- end

	if not self.display_model then
        self.display_model = OperationActRender.New(self.node_list["display_root"])
        self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	self.node_list.title_view_name.text.text = Language.FairyLandEquipment.ViewTitle
	--XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OpenPlayTips, self))

	-- local bundle, asset = ResPath.GetRawImagesJPG("a3_ys_bgd")
	-- self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- end)

	local data = {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.LINGYU}
	self:SetTabShowUIScene(0, data)

	XUI.AddClickEventListener(self.node_list.btn_open_plane_view, BindTool.Bind(self.OnClickOpenPlaneViewBtn, self))
end

function FairyLandEquipmentView:OnClickOpenPlaneViewBtn()
    FairyLandEquipmentWGCtrl.Instance:OpenPlaneView()
	self:Close()
end

function FairyLandEquipmentView:ReleaseCallBack()
	if self.flem_tabbar then
		self.flem_tabbar:DeleteMe()
		self.flem_tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	-- if self.model_display then
	-- 	self.model_display:DeleteMe()
	-- 	self.model_display = nil
	-- end

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	if self.he_wear_list ~= nil then
		for k, v in pairs(self.he_wear_list) do
			v:DeleteMe()
		end
		self.he_wear_list = nil
	end

	self:DeleteGodBookView()
	self:DeleteGodBodyView()
	self:DeleteHolyEquipView()
	self:StrengthenReleaseCallBack()
	self:EvolveReleaseCallBack()
	self:UpqualityReleaseCallBack()

	self.select_equip_part = nil
	self.wait_click_tab_oprate = nil
	self.tabbar_jump_remind = nil
	self.wait_jump_flush = nil
	self.select_slot_index = nil
	self.old_show_index = nil
	self.jump_slot_index = nil
	self.jump_to_page = nil
	self.old_list_show_index = nil
end

function FairyLandEquipmentView:LoadIndexCallBack(index)
	-- 装备列表
	if index == TabIndex.fairy_land_eq_holy_equip or
		index == TabIndex.fl_eq_forge_strengthen or
		index == TabIndex.fl_eq_forge_evolve or
		index == TabIndex.fl_eq_forge_upquality then
		if not self.he_wear_list then
			self.he_wear_list = {}
			for part = 0, XIANJIE_EQUIP_TYPE.XIANYIN do
				self.he_wear_list[part] = HolyEquipEquipCell.New(self.node_list["equip_" .. part])
				self.he_wear_list[part]:SetIndex(part)
				self.he_wear_list[part]:SetClickCallBack(BindTool.Bind(self.OnClickHECallBack, self))
			end
		end
	end

	-- if index == TabIndex.fairy_land_eq_god_book then
	-- 	self:InitGodBookView()
	if index == TabIndex.fairy_land_eq_god_body then
		self:InitGodBodyView()
	elseif index == TabIndex.fairy_land_eq_holy_equip then
		self:InitHolyEquipView()
	elseif index == TabIndex.fl_eq_forge_strengthen then
		self:StrengthenLoadCallBack()
	elseif index == TabIndex.fl_eq_forge_evolve then
		self:EvolveLoadIndexCallBack()
	elseif index == TabIndex.fl_eq_forge_upquality then
		self:UpqualityLoadIndexCallBack()
	end
end

function FairyLandEquipmentView:ShowIndexCallBack(index)
	self.is_jump_index = true
	self:ShowIndexHandle(index)
end

function FairyLandEquipmentView:ShowIndexHandle(index)
	if self.he_wear_list then
		local is_show_common_equip = index ~= TabIndex.fl_eq_forge_upquality
		self.node_list["equip_list_left"]:CustomSetActive(is_show_common_equip)
		self.node_list["equip_list_right"]:CustomSetActive(is_show_common_equip)
	end

	if index == TabIndex.fairy_land_eq_holy_equip then
		self.select_equip_part = nil
		self:FlushEquipListHL()
	elseif index == TabIndex.fl_eq_forge_evolve then
		self:EvolveShowIndexCallBack()
	end

	-- if self.show_index >= 20 then
	-- 	RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, -100, -50)
	-- else
	-- 	RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, -50, -50)
	-- end
end

function FairyLandEquipmentView:ToSelectTabIndex(tab_index)
	if self.show_index == tab_index and self:IsLoadedIndex(tab_index) then
		self:ShowIndexHandle(tab_index)
		-- print_error("---try merge----", tab_index)
		self:__MergeFlushParam(tab_index, "all", { "all" }, false)
	end
	self:ChangeToIndex(tab_index)
end

-- 神体选择回调
function FairyLandEquipmentView:OnSelectGodBodyCB(slot)
	if self.select_slot_index ~= slot then
		-- 刷新模型
		local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
		if slot_cfg then
			--local tianshen_index = TianShenWGData.Instance:GetWeaponIndexByAppeImageId(slot_cfg.show_model)
			--self.model_display:SetTianShenModel(slot_cfg.show_model, tianshen_index, true, nil, SceneObjAnimator.Rest)

			local display_data = {}
			display_data.should_ani = true
			if slot_cfg.model_show_itemid and slot_cfg.model_show_itemid ~= 0 and slot_cfg.model_show_itemid ~= "" then
				local split_list = string.split(slot_cfg.model_show_itemid, "|")
				if #split_list > 1 then
					local list = {}
					for k, v in pairs(split_list) do
						list[tonumber(v)] = true
					end
					display_data.model_item_id_list = list
				else
					display_data.item_id = slot_cfg.model_show_itemid
				end

				-- display_data.model_click_func = function ()
				-- 	TipWGCtrl.Instance:OpenItem({item_id = slot_cfg["model_show_itemid"]})
				-- end
			end

			display_data.bundle_name = slot_cfg["model_bundle_name"]
			display_data.asset_name = slot_cfg["model_asset_name"]
			local model_show_type = tonumber(slot_cfg["model_show_type"]) or 1
			display_data.render_type = model_show_type - 1
			display_data.model_rt_type = ModelRTSCaleType.L

			local scale = slot_cfg["display_scale"]
			if scale and scale ~= "" then
				display_data.model_adjust_root_local_scale = scale
			end

			if slot_cfg.rotation and slot_cfg.rotation ~= "" then
				local rotation_tab = string.split(slot_cfg.rotation,"|")
				display_data.model_adjust_root_local_rotation = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
			end

			self.display_model:SetData(display_data)

			local pos_x, pos_y = 0, 0
			if slot_cfg.display_pos and slot_cfg.display_pos ~= "" then
				local pos_list = string.split(slot_cfg.display_pos, "|")
				pos_x = tonumber(pos_list[1]) or pos_x
				pos_y = tonumber(pos_list[2]) or pos_y
			end

			RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, pos_x, pos_y)
		end

		self.is_god_body_new_slot = true
	end

	-- print_error("-----OnSelectGodBodyCB-----", slot)
	self.select_slot_index = slot

	self.wait_click_tab_oprate = true
end

-- 当前选择神体下标
function FairyLandEquipmentView:GetCurSelectSlot()
	return self.select_slot_index
end

-- 当前选择神体数据
function FairyLandEquipmentView:GetCurSelectSlotData()
	return FairyLandEquipmentWGData.Instance:GetGodBodyData(self.select_slot_index)
end

-- 点击装备格子回调
function FairyLandEquipmentView:OnClickHECallBack(cell)
	if cell == nil or cell.data == nil then
		return
	end

	local fle_data = FairyLandEquipmentWGData.Instance
	local data = cell.data
	if data.item_id == nil or data.item_id <= 0 then
		local show_cfg = fle_data:GetGBEquipVirtualCfg(self:GetCurSelectSlot(), cell.index)
		if show_cfg then
			-- local get_way_list = TipWGData.Instance:GetGetWayList(show_cfg.item_id)
			-- if not IsEmptyTable(get_way_list) then
			-- 	FightSoulWGCtrl.Instance:OpenFightSoulGetWayTips(get_way_list)
			-- end
			local show_data = { item_id = show_cfg.item_id }
			TipWGCtrl.Instance:OpenItem(show_data)
		end

		return
	end

	-- print_error("点击装备格子回调", self.show_index)
	local old_select_equip_part = self.select_equip_part
	local btn_callback_event = {}
	if self.show_index == TabIndex.fairy_land_eq_holy_equip then
		btn_callback_event[1] = {
			btn_text = Language.Tip.ButtonLabel[13],
			callback = function()
				ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView,
					TabIndex.fl_eq_forge_strengthen, nil, { strengthen_select = true, slot = data.slot, part = data.part })
			end
		}

		btn_callback_event[2] = {
			btn_text = Language.Tip.ButtonLabel[7],
			callback = function()
				FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.TAKE_OFF, data.slot, data.part)
			end
		}
	elseif self.show_index == TabIndex.fl_eq_forge_strengthen then
		self.select_equip_part = data.part
		self:StrengthenFlushRightPanel()
	elseif self.show_index == TabIndex.fl_eq_forge_evolve then
		self.select_equip_part = data.part
		FairyLandEquipmentWGData.Instance:ClearEvolveSelectBagParam()
		self:EvolveFlushContent()
	elseif self.show_index == TabIndex.fl_eq_forge_upquality then
		self.select_equip_part = data.part
		self:UpqualityFlushContent()
	end

	if old_select_equip_part == self.select_equip_part then
		TipWGCtrl.Instance:OpenItem(data, ItemTip.FROM_HOLY_EQUIP_WEAR, nil, nil, btn_callback_event)
	else
		self:FlushEquipListHL()
	end
end

function FairyLandEquipmentView:FlushEquipListHL()
	if self.he_wear_list then
		for k, v in pairs(self.he_wear_list) do
			v:OnSelectChange(k == self.select_equip_part)
		end
	end
end

-- {to_ui_name = slot, to_ui_param = page}
function FairyLandEquipmentView:OnFlush(param_t, index)
	-- print_error("---OnFlush---", index, param_t)
	-- 点击神体标签 wait功能标签选择刷新
	if self.wait_click_tab_oprate then
		self.wait_click_tab_oprate = nil
	end

	local book_remind = FairyLandEquipmentWGData.Instance:GetGodBookRemind()
	self.node_list.btn_open_plane_view_red:SetActive(book_remind == 1)

	for k, v in pairs(param_t) do
		if k == "all" then
			local select_slot, hor_bar_index
			if v.to_ui_name then -- 跳书页
				select_slot = tonumber(v.to_ui_name)
				hor_bar_index = index ~= 0 and index
			elseif v.strengthen_select then -- 跳强化
				if v.slot and v.part then
					select_slot = tonumber(v.slot)
					hor_bar_index = TabIndex.fl_eq_forge_strengthen
					self.cache_jump_equip_part = v.part
				end
			elseif self.tabbar_jump_remind then
				-- 跳红点
				select_slot, hor_bar_index = FairyLandEquipmentWGData.Instance:GetOpenViewJump()
				self.tabbar_jump_remind = nil
			end

			if select_slot and self.is_jump_index then
				self.wait_jump_flush = true
				self.is_jump_index = false
				self.flem_tabbar:TryJumpToTabbar(select_slot, hor_bar_index)
				return
			end

			self:FlushViewByIndex()
		elseif k == "jump_book" then
			-- local slot, page = FairyLandEquipmentWGData.Instance:GetGodBookJump()
			-- self.jump_to_page = page
			-- self.flem_tabbar:TryJumpToTabbar(slot, TabIndex.fairy_land_eq_god_book)
			--================================【神体】=====================================--
		elseif k == "flush_gb" then
			self:FlushViewByIndex()

			--================================【圣装】=====================================--
		elseif k == "flush_holy_equip" then
			self:FlushViewByIndex()

			--==============================【锻造 进化】==================================--
		elseif k == "evolve_select_bag" then
			self:EvolveFlushContent()
		end
	end
end

function FairyLandEquipmentView:FlushViewByIndex()
	if self.select_slot_index == nil or self.select_slot_index == -1 then
		return
	end

	if not self:IsLoadedIndex(self.show_index) then
		return
	end

	local refresh_ver, refresh_hor = true, true
	if self.wait_jump_flush then
		self.wait_jump_flush = nil
		refresh_ver = false
		refresh_hor = false
	else
		-- 减少点击标签刷新标签红点逻辑
		if self.old_show_index and self.old_show_index ~= self.show_index then
			refresh_ver = false
			refresh_hor = false
		end
	end

	local need_restart_select_part = self.old_show_index ~= self.show_index
	self.old_show_index = self.show_index
	self.flem_tabbar:RefreshAllCellData(refresh_ver, refresh_hor)
	local fle_data = FairyLandEquipmentWGData.Instance

	-- if self.show_index == TabIndex.fairy_land_eq_god_book then
	-- 	local jump_page = self.jump_to_page
	-- 	if jump_page then
	-- 		local page_state = fle_data:GetPageActState(self.select_slot_index, jump_page)
	-- 		if page_state == GOODS_STATE_TYPE.UNACT then
	-- 			local page_gather_num = fle_data:GetPageGatherNum(self.select_slot_index) - 1
	-- 			page_gather_num = page_gather_num > 0 and page_gather_num or 0
	-- 			jump_page = jump_page > page_gather_num and page_gather_num or jump_page
	-- 		end
	-- 	else
	-- 		jump_page = fle_data:GetSlotMaxJumpPage(self.select_slot_index)
	-- 	end

	-- 	self:JumpToPage(jump_page)
	-- 	self:FlushGodBookView()
	-- 	self.jump_to_page = nil
	if self.show_index == TabIndex.fairy_land_eq_god_body then
		self:FlushGodBodyView()
	elseif self.show_index == TabIndex.fairy_land_eq_holy_equip then
		self:FlushHolyEquipWearPanel()
		self:FlushHolyEquipBagPanel()
	elseif self.show_index == TabIndex.fl_eq_forge_strengthen then
		if self.cache_jump_equip_part == nil and need_restart_select_part then
			self.cache_jump_equip_part = fle_data:GetFLEStrengthenJumpIndex(self.select_slot_index)
		end

		self:FlushHolyEquipWearPanel()
	elseif self.show_index == TabIndex.fl_eq_forge_evolve then
		if self.cache_jump_equip_part == nil and need_restart_select_part then
			self.cache_jump_equip_part = fle_data:GetEvolveJumpPart(self.select_slot_index)
		end

		self:FlushHolyEquipWearPanel()
	elseif self.show_index == TabIndex.fl_eq_forge_upquality then
		if self.cache_jump_equip_part == nil and need_restart_select_part then
			self.cache_jump_equip_part = fle_data:GetUpqualityJumpPart(self.select_slot_index)
		end

		self:FlushHolyEquipWearPanel()
	end
end

-- 刷新穿戴
function FairyLandEquipmentView:FlushHolyEquipWearPanel()
	if self.he_wear_list == nil then
		self.node_list.he_cap_value.text.text = 0
		return
	end

	local slot = self:GetCurSelectSlot()
	local fle_data = FairyLandEquipmentWGData.Instance
	self.node_list.he_cap_value.text.text = fle_data:GetSlotEquipCapability(slot)

	-- 装备列表刷新
	local wear_list = fle_data:GetHolyEquipWearList(slot)
	local start_index = self.show_index == TabIndex.fl_eq_forge_upquality and XIANJIE_EQUIP_TYPE.TEJIE or
	XIANJIE_EQUIP_TYPE.WUQI
	for i = start_index, XIANJIE_EQUIP_TYPE.XIANYIN do
		local cell = self.he_wear_list[i]
		if cell then
			cell:SetData(wear_list[i])
		end
	end

	if self.cache_jump_equip_part and self.select_equip_part ~= self.cache_jump_equip_part then
		local cell = self.he_wear_list[self.cache_jump_equip_part]
		self.cache_jump_equip_part = nil
		if cell then
			cell:OnClick()
		end
	else
		self.cache_jump_equip_part = nil
		if self.show_index == TabIndex.fl_eq_forge_strengthen then
			self:StrengthenFlushRightPanel()
		elseif self.show_index == TabIndex.fl_eq_forge_evolve then
			self:EvolveFlushContent()
		elseif self.show_index == TabIndex.fl_eq_forge_upquality then
			self:UpqualityFlushContent()
		end
	end
end

function FairyLandEquipmentView:GetWearCellList()
	return self.he_wear_list
end

function FairyLandEquipmentView:OpenPlayTips()
	local rule_tip = RuleTip.Instance
	local fle_str = Language.FairyLandEquipment
	local title = fle_str.PlayTipsTitle[self.show_index]
	local content = fle_str.PlayTipsContent[self.show_index]

	rule_tip:SetTitle(title)
	rule_tip:SetContent(content, nil, nil, nil, true)
end

-- 展示特效
function FairyLandEquipmentView:ShowEffect(effect_type, is_success, operate_type)
	local effect_node = self.node_list.effect_node
	if self.show_index == TabIndex.fairy_land_eq_god_body then
		-- if is_success then
		-- 	local bundle_name, asset_name = ResPath.GetUIEffect("UI_guangshu")
		-- 	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name,
		-- 		self.node_list.god_body_effect_node.transform,
		-- 		nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
		-- end
	elseif self.show_index == TabIndex.fl_eq_forge_strengthen then
		--self:StrengthenPlayStengthEffect()
	elseif is_success and operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_UPGRADE and self.show_index == TabIndex.fl_eq_forge_evolve then
		--self:ShowEvolveSuccess()
	elseif operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_UPCOLOR and self.show_index == TabIndex.fl_eq_forge_upquality then
		--self:ShowUpQuaalitySuccess()
	end

	if effect_node then
		TipWGCtrl.Instance:ShowEffect({
			effect_type = effect_type,
			is_success = is_success,
			pos = Vector2(0, 0),
			parent_node = effect_node
		})
	end
end
