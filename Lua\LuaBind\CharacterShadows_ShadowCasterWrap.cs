﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class CharacterShadows_ShadowCasterWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(CharacterShadows.ShadowCaster), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>un<PERSON>("OnBoundsChanged", OnBoundsChanged);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Index", get_Index, null);
		<PERSON><PERSON>("Projection", get_Projection, null);
		<PERSON><PERSON>("View", get_View, null);
		<PERSON><PERSON>("Projector", get_Projector, null);
		<PERSON><PERSON>("DecalMode", get_DecalMode, set_DecalMode);
		<PERSON><PERSON>("AvailableBias", get_AvailableBias, set_AvailableBias);
		<PERSON><PERSON>("BlobBias", get_BlobB<PERSON>, set_BlobBias);
		<PERSON><PERSON>("Shadow", get_Shadow, null);
		<PERSON>.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBoundsChanged(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)ToLua.CheckObject<CharacterShadows.ShadowCaster>(L, 1);
			obj.OnBoundsChanged();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Index(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			int ret = obj.Index;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Index on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Projection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			UnityEngine.Matrix4x4 ret = obj.Projection;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Projection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_View(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			UnityEngine.Matrix4x4 ret = obj.View;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index View on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Projector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			UnityEngine.Rendering.Universal.DecalProjector ret = obj.Projector;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Projector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DecalMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			CharacterShadows.ShadowDecalMode ret = obj.DecalMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DecalMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AvailableBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			UnityEngine.Vector2 ret = obj.AvailableBias;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AvailableBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BlobBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			UnityEngine.Vector2 ret = obj.BlobBias;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BlobBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Shadow(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			CharacterShadow ret = obj.Shadow;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Shadow on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DecalMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			CharacterShadows.ShadowDecalMode arg0 = (CharacterShadows.ShadowDecalMode)ToLua.CheckObject(L, 2, typeof(CharacterShadows.ShadowDecalMode));
			obj.DecalMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DecalMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AvailableBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.AvailableBias = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AvailableBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_BlobBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.ShadowCaster obj = (CharacterShadows.ShadowCaster)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.BlobBias = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BlobBias on a nil value");
		}
	}
}

