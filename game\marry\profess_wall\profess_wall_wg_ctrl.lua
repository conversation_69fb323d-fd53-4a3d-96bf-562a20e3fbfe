require("game/marry/profess_wall/profess_wall_wg_data")
require("game/marry/profess_wall/choose_profess_view")
require("game/marry/profess_wall/profess_view")
require("game/marry/profess_wall/profess_wall_view")
require("game/marry/profess_wall/profess_rank_male_view")
require("game/marry/profess_wall/profess_select_friend_view")
require("game/marry/profess_wall/profess_remind_view")
require("game/marry/profess_wall/profess_all_confession_view")
require("game/marry/profess_wall/profess_my_confession_view")
require("game/marry/profess_wall/profess_tomy_confession_view")
require("game/marry/profess_wall/profess_wall_male_rank_view")
require("game/marry/profess_wall/profess_wall_female_rank_view")
require("game/marry/marry_yuyue_view/marry_xunyuan")
require("game/marry/marry_yuyue_view/marry_xunyuan_tip")

ProfessWallWGCtrl = ProfessWallWGCtrl or BaseClass(BaseWGCtrl)

function ProfessWallWGCtrl:__init()
	if ProfessWallWGCtrl.Instance ~= nil then
		print_error("[ProfessWallWGCtrl] attempt to create singleton twice!")
		return
	end

	ProfessWallWGCtrl.Instance = self
	self:RegisterAllProtocols()

	self.data = ProfessWallWGData.New()
	self.view = ProfessView.New(GuideModuleName.ProfessWallView) 	--表白墙界面
	self.choose_view = ChooseProfessView.New(GuideModuleName.ChooseProfessView) 	--表白界面
	self.select_friend_view = ProfessSelectFriendView.New(GuideModuleName.ProfessSelectFriendView) --选择表白对象
	self.profess_remind_view = ProfessRemindView.New(GuideModuleName.ProfessRemindView)
	self.xunyuan_tip_view = MarryXunYuanTipView.New()

	self.effect_count = 0
	self.handle_list = {} 
	self.effect_list = {}

	self:BindGlobalEvent(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind(self.SendProfessWallReq, self))
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.SetXunYuanTipState, self))
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.HasViewOpen, self))
end

function ProfessWallWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if self.choose_view then
		self.choose_view:DeleteMe()
		self.choose_view = nil
	end

	if self.select_friend_view then
		self.select_friend_view:DeleteMe()
		self.select_friend_view = nil
	end

	if self.profess_remind_view then
		self.profess_remind_view:DeleteMe()
		self.profess_remind_view = nil
	end

	if self.xunyuan_tip_view then
		self.xunyuan_tip_view:DeleteMe()
		self.xunyuan_tip_view = nil
	end

	self.effect_count = 0
	self.effect_list = {}
	self.handle_list = {}
	ProfessWallWGCtrl.Instance = nil
	self.not_show_effect = false
end

function ProfessWallWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSProfessToReq)
	self:RegisterProtocol(SCGlobalProfessWallInfo, "OnGlobalProfessWallInfo")
	self:RegisterProtocol(SCPersonProfessWallInfo, "OnPersonProfessWallInfo")
	self:RegisterProtocol(SCProfessWallEffect, "OnProfessWallEffect")
	self:RegisterProtocol(SCRAProfessRankInfo, "OnRAProfessRankInfo")                     --我的排行榜信息
	self:RegisterProtocol(SCQingYuanZhengHunNotice, "OnSCQingYuanZhengHunNotice")
end

--表白请求
function ProfessWallWGCtrl:SendProfessToReq(target_role_id, gift_type, is_auto_buy, content)
	self.send_profess_req = true
	local protocol          = ProtocolPool.Instance:GetProtocol(CSProfessToReq)
	protocol.target_role_id = target_role_id or 0
	protocol.gift_type      = gift_type  or 0
	protocol.is_auto_buy    = is_auto_buy or 0
	protocol.content        = content or ""
	protocol:EncodeAndSend()
end

function ProfessWallWGCtrl:OnGlobalProfessWallInfo(protocol)
	self.data:SetGlobalProfessWallInfo(protocol)
	if self.view and self.view:IsOpen() then
		self.view:Flush(TabIndex.profess_wall_all)
	end
end

function ProfessWallWGCtrl:GetViewIsOpen()
	return self.view and self.view:IsOpen()		
end

function ProfessWallWGCtrl:SendProfessWallReqInfo()
    if self.view and self.view:IsOpen()	then
        -- self.view:SendReqInfo()
		ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
		ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_PASSIVE)
		ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE)
    end
end

function ProfessWallWGCtrl:OnPersonProfessWallInfo(protocol)
	self.data:SetPersonProfessWallInfo(protocol)
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end

function ProfessWallWGCtrl:OnProfessWallEffect(protocol)
	if self.view:IsOpen() and not self.send_profess_req then
		local index = self.view:GetShowIndex()
		MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_INFO, PROFESS_OPERATOR[index], 0)
	end
	self.send_profess_req = false
	if self.not_show_effect then
		return
	end
	-- if self.effect_count > 0 then
	-- 	return
	-- end

	self.is_play_effect = true
	self.effect_count = 1
	if protocol.effect_type == 2 then
    	self:PlayerEffect("ui_shehuojian", 3,protocol)
    elseif protocol.effect_type == 0 then
    	self:PlayerEffect("ui_kaixiangbin", 2.5,protocol)
    elseif protocol.effect_type == 1 then
    	self:PlayerEffect("ui_shuayoulun", 4,protocol)
    end
end

--自己的排行榜信息
function ProfessWallWGCtrl:OnRAProfessRankInfo(protocol)
	self.data:SetSpecialProfessRankInfo(protocol)
	-- print_error("自己的排行榜信息")
	if self.view and self.view:IsOpen() then
		self.view:Flush(TabIndex.profess_wall_rank_male)
	end
end

function ProfessWallWGCtrl:OnSCQingYuanZhengHunNotice(protocol)
	self.data:SetQingYuanZhengHunNotice(protocol)
	self:OpenXunYuanTip()
end

function ProfessWallWGCtrl:SendProfessWallReq()
	MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_INFO, 0, 0)
	MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_INFO, 1, 0)
	MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_INFO, 2, 0)
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK) then
		self:SendGetKaifuActivityInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
		RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_MALE)
		RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_FEMALE)		
	end
end

function ProfessWallWGCtrl:SendGetKaifuActivityInfo( rand_activity_type, opera_type, param_1, param_2 )
	if IS_ON_CROSSSERVER then
		return
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = rand_activity_type or 0
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

function ProfessWallWGCtrl:PlayerEffect(objname, time,protocol)
	--print_error("000000000000000000000000000000")
	self.profess_remind_view:SetEffect(objname, time,protocol)
    --ViewManager.Instance:Open(GuideModuleName.ProfessRemindView)

	-- GlobalTimerQuest:AddDelayTimer(function()
 --    	ViewManager.Instance:Close(GuideModuleName.ProfessRemindView)
	-- 	self.effect_count = 0
	-- end, time)
end

function ProfessWallWGCtrl:RemoveEventListen(handle)
    if nil ~= handle then
        self.handle_list[handle] = nil
    end
end

function ProfessWallWGCtrl:FlushView(param_t)
	if self.view and self.view:IsOpen() then
		self.view:Flush(param_t)
	end
end

function ProfessWallWGCtrl:AddStopEventListen(event)
    local handle = {func = event}
    self.handle_list[handle] = handle
    return handle
end

function ProfessWallWGCtrl:StopEffect()
    for k,v in pairs(self.handle_list) do
        v.func()
    end
    self.handle_list = {}
end

function ProfessWallWGCtrl:OnDeleteProfessInfoResult(protocol)
	self.data:SetDeleteProfessInfoResult(protocol)
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end

function ProfessWallWGCtrl:HasViewOpen(view)
	print_error("HasViewOpen")
	if view.full_screen and self.is_play_effect then
		for k,v in pairs(self.effect_list) do
			if v and not IsNil(v) then
		        self.effect_count = self.effect_count - 1
		        ResMgr:Destroy(v)
		    end
		end
		self.effect_list = {}
		
		ViewManager.Instance:Close(GuideModuleName.ProfessRemindView)
	end
end

function ProfessWallWGCtrl:SetSelectFriendCallBack(callback)
	if self.select_friend_view then
		self.select_friend_view:SetCallBack(callback)
	end
end

function ProfessWallWGCtrl:NotShowEffect()
	self.not_show_effect = true
end

--表白界面默认选中信息
function ProfessWallWGCtrl:SetDefaultSelectInfo(uid)

end

function ProfessWallWGCtrl:GetMyFriendList()
	local friend_list = SocietyWGData.Instance:GetFriendList()
	local temp_list = {}
	for k,v in pairs(friend_list) do
		if v.is_online == 1 or v.is_online == 3 then
			if RoleWGData.Instance.role_vo.lover_uid == v.user_id then
				v.is_love_id = 1
			else
				v.is_love_id = 0
			end
			table.insert(temp_list,v)
		end
	end
	table.sort(temp_list, SortTools.KeyUpperSorters("is_love_id", "intimacy", "level"))
	return temp_list
end

function ProfessWallWGCtrl:FlushChooseViewByInfoChange()
	if self.choose_view then
		self.choose_view:Flush(0, "info_change")
	end
end

function ProfessWallWGCtrl:OpenXunYuanTip()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local flag = PlayerPrefsUtil.GetInt("MarryXunYuanTipView" .. main_role_id)

	if flag ~= 1 then
		local data = self.data:GetQingYuanZhengHunNotice()
		self.xunyuan_tip_view:SetData(data)
		if self.xunyuan_tip_view:IsOpen() then
			self.xunyuan_tip_view:Flush()
		else
			self.xunyuan_tip_view:Open()
		end
	end
end

function ProfessWallWGCtrl:SetXunYuanTipState()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt("MarryXunYuanTipView" .. main_role_id, 0)
end