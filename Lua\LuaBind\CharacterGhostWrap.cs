﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CharacterGhostWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CharacterGhost), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SetMaxGhostTypeNum", SetMaxGhostTypeNum);
		<PERSON><PERSON>ction("SetSkinnedMeshRenderer", SetSkinnedMeshRenderer);
		<PERSON><PERSON>Function("SetSpeedFactor", SetSpeedFactor);
		L.RegFunction("ShowGhost", ShowGhost);
		<PERSON><PERSON>RegFunction("Stop", Stop);
		<PERSON><PERSON>RegFunction("Bind", Bind);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON>.<PERSON>ar("Material", get_Material, set_Material);
		<PERSON>.Reg<PERSON>ar("Root", get_Root, set_Root);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMaxGhostTypeNum(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CharacterGhost obj = (CharacterGhost)ToLua.CheckObject<CharacterGhost>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetMaxGhostTypeNum(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSkinnedMeshRenderer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CharacterGhost obj = (CharacterGhost)ToLua.CheckObject<CharacterGhost>(L, 1);
			UnityEngine.SkinnedMeshRenderer arg0 = (UnityEngine.SkinnedMeshRenderer)ToLua.CheckObject<UnityEngine.SkinnedMeshRenderer>(L, 2);
			obj.SetSkinnedMeshRenderer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSpeedFactor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CharacterGhost obj = (CharacterGhost)ToLua.CheckObject<CharacterGhost>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetSpeedFactor(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowGhost(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			CharacterGhost obj = (CharacterGhost)ToLua.CheckObject<CharacterGhost>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
			obj.ShowGhost(arg0, arg1, arg2, arg3);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CharacterGhost obj = (CharacterGhost)ToLua.CheckObject<CharacterGhost>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Stop(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Bind(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			CharacterGhost o = CharacterGhost.Bind(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Material(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterGhost obj = (CharacterGhost)o;
			UnityEngine.Material ret = obj.Material;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Material on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Root(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterGhost obj = (CharacterGhost)o;
			UnityEngine.Transform ret = obj.Root;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Root on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Material(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterGhost obj = (CharacterGhost)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.Material = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Material on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Root(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterGhost obj = (CharacterGhost)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.Root = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Root on a nil value");
		}
	}
}

