
-------------------------------------- 每日首充------------------------------------------
function QuanMinBeiZhanView:InitShouChongView()
	self.init_shouchong = true
	if not self.shouchong_reward_list then
		self.shouchong_reward_list = AsyncListView.New(BZShouChongRender, self.node_list["first_reward_list"])
	end

	self:InitShouChongList()
	self:SCTimeCountDown()

	XUI.AddClickEventListener(self.node_list.first_btn_receive, BindTool.Bind(self.OnShouChongBtnReceiveClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.first_btn_tip, BindTool.Bind(self.OnShouChongBtnTip<PERSON>lick<PERSON><PERSON><PERSON>,self))

	local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_shouchong)
	if theme_cfg ~= nil then
		self.node_list.first_tip_label.text.text = theme_cfg.rule_desc
	end
end

function QuanMinBeiZhanView:ReleaseShouChongView()
	if self.shouchong_reward_list then
		self.shouchong_reward_list:DeleteMe()
		self.shouchong_reward_list = nil
	end

	if self.sc_cell_list then
		for k,v in pairs(self.sc_cell_list) do
			v:DeleteMe()
		end
	end
	self.sc_cell_list = nil

	if self.shouchong_count_down and CountDownManager.Instance:HasCountDown(self.shouchong_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.shouchong_count_down)
	end

	if self.leichong_count_down and CountDownManager.Instance:HasCountDown(self.leichong_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.leichong_count_down)
	end

	self.init_shouchong = nil
end

function QuanMinBeiZhanView:ShowShouChongView()
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_MEIRISHOUCHONG, TIANSHEN_THEME_SHOUCHONG_OP_TYPE.TYPE_INFO)
end

function QuanMinBeiZhanView:FlushShouChongView()
	if self.init_shouchong ~= nil then
		local gift_state = QuanMinBeiZhanWGData.Instance:GetShouChongGiftState()
		local btn_desc = Language.QuanMinBeiZhan.ReChargeStr1
		self.node_list.first_btn_redpoint:SetActive(false)
		self.node_list.first_btn_ylq:SetActive(false)
		self.node_list.first_btn_receive:SetActive(true)

		if gift_state == ActivityRewardState.KLQ then
			XUI.SetGraphicGrey(self.node_list.first_btn_receive, false)
			self.node_list.first_btn_redpoint:SetActive(true)
		elseif gift_state == ActivityRewardState.YLQ then
			local is_active_last_day = QuanMinBeiZhanWGData.Instance:IsActivityLastDay(TabIndex.quanmin_beizhan_shouchong)
			if is_active_last_day then
				self.node_list.first_btn_ylq:SetActive(true)
				self.node_list.first_btn_receive:SetActive(false)
			else
				btn_desc = Language.TianShenRoad.ReChargeStr2
				XUI.SetGraphicGrey(self.node_list.first_btn_receive, true)
			end
		end
		self.node_list.first_btn_label.text.text = btn_desc
	end
end

function QuanMinBeiZhanView:InitShouChongList()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_MEIRISHOUCHONG)
	local day_index = open_day - activity_open_day + 1
	
	local cfg = QuanMinBeiZhanWGData.Instance:GetShouChongRewardCfgByDay(day_index)
	if not cfg then
		return
	end

	local chaozhi_state = string.split(cfg.chaozhi, ":")
	local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(cfg.reward_item)
	for k,v in pairs(reward_list) do
		v.chaozhi_state = chaozhi_state[k+1] and tonumber(chaozhi_state[k+1]) or 0
	end
	self.shouchong_reward_list:SetDataList(reward_list)
end

function QuanMinBeiZhanView:OnShouChongBtnReceiveClickHnadler()
	local gift_state = QuanMinBeiZhanWGData.Instance:GetShouChongGiftState()
	if gift_state == ActivityRewardState.KLQ then
		QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_MEIRISHOUCHONG, TIANSHEN_THEME_SHOUCHONG_OP_TYPE.TYPE_DRAW)
	elseif gift_state == ActivityRewardState.YLQ then
		TipWGCtrl.Instance:ShowSystemMsg(Language.QuanMinBeiZhan.ReChargeStr3)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
	end
end

function QuanMinBeiZhanView:OnShouChongBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = QuanMinBeiZhanWGData.Instance:GetActivityTip(TabIndex.quanmin_beizhan_shouchong)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

--有效时间倒计时
function QuanMinBeiZhanView:SCTimeCountDown()
	self.shouchong_count_down = "shouchong_count_down"
	local invalid_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_shouchong)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.first_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.shouchong_count_down, BindTool.Bind1(self.UpdateSCCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function QuanMinBeiZhanView:UpdateSCCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.first_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

----------------------------------- 累计充值 --------------------------------------------
function QuanMinBeiZhanView:InitLeiChongView()
	if not self.leichong_reward_list then
		self.leichong_reward_list = AsyncListView.New(BZLeiChongRender, self.node_list["total_reward_list"])
	end

	if not self.leichong_slider_list then
		self.leichong_slider_list = {}
		for k = 1, 5 do
			self.leichong_slider_list[k] = BZJFLeiChongSliderRender.New(self.node_list["total_reward_" .. k])
			self.leichong_slider_list[k]:SetIndex(k)
		end
	end

	self:LCTimeCountDown()
	XUI.AddClickEventListener(self.node_list.total_btn_tip, BindTool.Bind(self.OnLeiChongBtnTipClickHnadler,self))

	local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_leichong)
	if theme_cfg ~= nil then
	end
end

function QuanMinBeiZhanView:ReleaseRechargeView()
	if self.leichong_reward_list then
		self.leichong_reward_list:DeleteMe()
		self.leichong_reward_list = nil
	end

	if nil ~= self.leichong_slider_list then
		for k,v in ipairs(self.leichong_slider_list)do
			v:DeleteMe()
		end
		self.leichong_slider_list = nil
	end
end

function QuanMinBeiZhanView:ShowLeiChongView()
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LEICHONGHAOLI, TIANSHEN_THEME_LEICHONG_OP_TYPE.TYPE_INFO)
end

function QuanMinBeiZhanView:FlushLeiChongView()
	local cur_xianyu = QuanMinBeiZhanWGData.Instance:GetOwnXianYu()
	local need_value, cur_stage_value, pre_stage_value = QuanMinBeiZhanWGData.Instance:GetNeedRechargeXianYU()
	local slide_percent = 0

	self.node_list.leichong_cout.text.text = cur_xianyu
	local slider_value = QuanMinBeiZhanWGData.Instance:GetLeiChongProgress(cur_xianyu)
	self.node_list.slider_bg.slider.value = slider_value
	
	local list = QuanMinBeiZhanWGData.Instance:GetLeiChongRewardList()
	if self.leichong_reward_list then
		self.leichong_reward_list:SetDataList(list)
	end
	if self.leichong_slider_list then
		local slider_cfg = QuanMinBeiZhanWGData.Instance:GetLeiChongRewardCfg()
		for k,v in ipairs(self.leichong_slider_list)do
			v:SetData(slider_cfg[k])
		end
	end

end

function QuanMinBeiZhanView:OnLeiChongBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = QuanMinBeiZhanWGData.Instance:GetActivityTip(TabIndex.quanmin_beizhan_leichong)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

--有效时间倒计时
function QuanMinBeiZhanView:LCTimeCountDown()
	self.leichong_count_down = "leichong_count_down"
	local invalid_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_leichong)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.total_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.leichong_count_down, BindTool.Bind1(self.UpdateLCCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function QuanMinBeiZhanView:UpdateLCCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.total_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

-----------------------------------------------------------------------------------------------

BZLeiChongRender = BZLeiChongRender or BaseClass(BaseRender)

function BZLeiChongRender:LoadCallBack()
	self.btn_lingqu = self.node_list["btn_lingqu"]
	self.btn_recharge = self.node_list["btn_recharge"]
	self.btn_lingqu.button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	self.btn_recharge.button:AddClickListener(BindTool.Bind1(self.OnClickRechargeHnadler, self))
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.reward_list:SetIsDelayFlush(false)
end

function BZLeiChongRender:ReleaseCallBack()
	self.btn_lingqu = nil
	self.btn_recharge = nil
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function BZLeiChongRender:OnFlush()
	if not self.data then
		return
	end 

	local cur_xianyu = QuanMinBeiZhanWGData.Instance:GetOwnXianYu()
	local color =  cur_xianyu >= self.data.cfg.stage_value and COLOR3B.GREEN or COLOR3B.RED
	local value_str = string.format("%d/%d",cur_xianyu,self.data.cfg.stage_value)
	self.node_list["recharge_value"].text.text = ToColorStr(value_str, color)

	self.node_list["btn_lingqu"]:SetActive(cur_xianyu >= self.data.cfg.stage_value and self.data.receive_state == 1)
	self.node_list["btn_recharge"]:SetActive(cur_xianyu < self.data.cfg.stage_value and self.data.receive_state == 0)
	self.node_list["btn_yilingqu"]:SetActive(self.data.receive_state == 2)
	local list = OperationActivityWGData.Instance:SortDataByItemColor(self.data.cfg.reward_item)
	-- for k,v in pairs(self.data.cfg.reward_item) do
	-- 	table.insert(list, v)
	-- end
	if not IsEmptyTable(list) then
		self.reward_list:SetDataList(list)
	end
end

function BZLeiChongRender:OnClickRewardHnadler(sender)
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LEICHONGHAOLI, TIANSHEN_THEME_LEICHONG_OP_TYPE.TYPE_DRAW, self.data.cfg.ID)
end

function BZLeiChongRender:OnClickRechargeHnadler(sender)
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end

---------------------------------------------------------------------------------------

BZShouChongRender = BZShouChongRender or BaseClass(BaseRender)

function BZShouChongRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BZShouChongRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["cell"])
end

function BZShouChongRender:OnFlush()
	self.item_cell:SetData(self.data)
end

---------------------------------------------------------
BZJFLeiChongSliderRender = BZJFLeiChongSliderRender or BaseClass(BaseRender)

function BZJFLeiChongSliderRender:__init()
	self.item_cell = ItemCell.New(self.node_list.item_pos)
	XUI.AddClickEventListener(self.node_list["get_btn"], BindTool.Bind1(self.OnClickGetReward, self))
end

function BZJFLeiChongSliderRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BZJFLeiChongSliderRender:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end
	self.item_cell:SetData(self.data.show_reward_item[0])
	local receive_state = QuanMinBeiZhanWGData.Instance:GetLeiChongReceiveState(self.data.ID)
	self.node_list.get_btn:SetActive(receive_state == 1)
	self.node_list.lingqu_bg:SetActive(receive_state == 2)
	self.node_list.num_cout.text.text = self.data.stage_value
end

function BZJFLeiChongSliderRender:OnClickGetReward()
	local receive_state = QuanMinBeiZhanWGData.Instance:GetLeiChongReceiveState(self.data.ID)
	if receive_state == 1 then
		QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LEICHONGHAOLI, TIANSHEN_THEME_LEICHONG_OP_TYPE.TYPE_DRAW, self.data.ID)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
	end
end