-- T-天下第一.xls
local item_table={
[1]={item_id=26380,num=1,is_bind=1},
[2]={item_id=26369,num=1,is_bind=1},
[3]={item_id=50299,num=5,is_bind=1},
[4]={item_id=26379,num=1,is_bind=1},
[5]={item_id=26378,num=1,is_bind=1},
[6]={item_id=26368,num=1,is_bind=1},
[7]={item_id=47114,num=18,is_bind=1},
[8]={item_id=36423,num=6300,is_bind=1},
[9]={item_id=36421,num=45,is_bind=1},
[10]={item_id=36417,num=970000,is_bind=1},
[11]={item_id=47114,num=16,is_bind=1},
[12]={item_id=36423,num=6000,is_bind=1},
[13]={item_id=36421,num=41,is_bind=1},
[14]={item_id=36417,num=941000,is_bind=1},
[15]={item_id=50299,num=4,is_bind=1},
[16]={item_id=47114,num=14,is_bind=1},
[17]={item_id=36423,num=5700,is_bind=1},
[18]={item_id=36421,num=37,is_bind=1},
[19]={item_id=36417,num=913000,is_bind=1},
[20]={item_id=47114,num=12,is_bind=1},
[21]={item_id=36423,num=5400,is_bind=1},
[22]={item_id=36421,num=33,is_bind=1},
[23]={item_id=36417,num=886000,is_bind=1},
[24]={item_id=47114,num=10,is_bind=1},
[25]={item_id=36423,num=5100,is_bind=1},
[26]={item_id=36421,num=30,is_bind=1},
[27]={item_id=36417,num=859000,is_bind=1},
[28]={item_id=36423,num=4800,is_bind=1},
[29]={item_id=36421,num=27,is_bind=1},
[30]={item_id=36417,num=833000,is_bind=1},
[31]={item_id=50299,num=3,is_bind=1},
[32]={item_id=36423,num=4600,is_bind=1},
[33]={item_id=36421,num=24,is_bind=1},
[34]={item_id=36417,num=808000,is_bind=1},
[35]={item_id=36423,num=4400,is_bind=1},
[36]={item_id=36421,num=22,is_bind=1},
[37]={item_id=36417,num=784000,is_bind=1},
[38]={item_id=36423,num=4200,is_bind=1},
[39]={item_id=36421,num=20,is_bind=1},
[40]={item_id=36417,num=760000,is_bind=1},
[41]={item_id=48411,num=1,is_bind=1},
[42]={item_id=47117,num=1,is_bind=1},
[43]={item_id=50088,num=1,is_bind=1},
[44]={item_id=46048,num=9,is_bind=1},
[45]={item_id=27909,num=18,is_bind=1},
[46]={item_id=36417,num=2800000,is_bind=1},
[47]={item_id=48412,num=1,is_bind=1},
[48]={item_id=46048,num=8,is_bind=1},
[49]={item_id=27909,num=16,is_bind=1},
[50]={item_id=36417,num=2600000,is_bind=1},
[51]={item_id=50087,num=3,is_bind=1},
[52]={item_id=46048,num=7,is_bind=1},
[53]={item_id=27909,num=14,is_bind=1},
[54]={item_id=36417,num=2400000,is_bind=1},
[55]={item_id=50087,num=2,is_bind=1},
[56]={item_id=46048,num=6,is_bind=1},
[57]={item_id=27909,num=12,is_bind=1},
[58]={item_id=36417,num=2200000,is_bind=1},
[59]={item_id=50087,num=1,is_bind=1},
[60]={item_id=46048,num=5,is_bind=1},
[61]={item_id=27909,num=10,is_bind=1},
[62]={item_id=27908,num=10,is_bind=1},
[63]={item_id=36417,num=2000000,is_bind=1},
[64]={item_id=50086,num=3,is_bind=1},
[65]={item_id=27909,num=8,is_bind=1},
[66]={item_id=27908,num=8,is_bind=1},
[67]={item_id=36417,num=1800000,is_bind=1},
[68]={item_id=50086,num=2,is_bind=1},
[69]={item_id=27909,num=6,is_bind=1},
[70]={item_id=27908,num=5,is_bind=1},
[71]={item_id=36417,num=1600000,is_bind=1},
[72]={item_id=36419,num=50,is_bind=1},
[73]={item_id=47114,num=4,is_bind=1},
[74]={item_id=36419,num=100,is_bind=1},
[75]={item_id=47114,num=6,is_bind=1},
[76]={item_id=47114,num=20,is_bind=1},
[77]={item_id=36423,num=6600,is_bind=1},
[78]={item_id=36421,num=50,is_bind=1},
[79]={item_id=36417,num=1000000,is_bind=1},
[80]={item_id=38925,num=1,is_bind=1},
[81]={item_id=48410,num=1,is_bind=1},
[82]={item_id=46048,num=10,is_bind=1},
[83]={item_id=27909,num=20,is_bind=1},
[84]={item_id=36417,num=3000000,is_bind=1},
[85]={item_id=36419,num=20,is_bind=1},
[86]={item_id=47114,num=2,is_bind=1},
}

return {
activity_time={
{}
},

activity_time_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
round={
{},
{subround=2,sub_round_name="第二场",subround_show_time="20:39-20:44",},
{subround=3,sub_round_name="第三场",subround_show_time="20:46-20:51",},
{round=2,round_name="海选赛第二轮",},
{round=2,round_name="海选赛第二轮",},
{round=2,round_name="海选赛第二轮",},
{round=3,round_name="海选赛第三轮",},
{round=3,round_name="海选赛第三轮",},
{round=3,round_name="海选赛第三轮",},
{round=4,relive_count=6,type=2,round_name="淘汰赛",sub_round_name="100进50",subround_show_time="20:33-20:38",btn_name="100进50",desc="淘汰赛排名前<color=#95d12b>50</color>可以进入下一轮",},
{subround=2,sub_round_name="50进10",subround_show_time="20:41-20:46",btn_name="50进10",desc="淘汰赛排名前<color=#95d12b>10</color>可以进入决赛",},
{subround=3,sub_round_name="决赛",subround_show_time="20:49-20:54",btn_name="决赛",desc="最终获胜玩家获得冠军",}
},

round_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[8]=5,	-- depth:2
[9]=6,	-- depth:2
[11]=10,	-- depth:1
[12]=10,	-- depth:1
},
continue_kill_score={
{},
{kill_count=3,kill_desc="<color=#ffb400>%s</color>已完成三杀！",},
{kill_count=4,kill_desc="<color=#ffb400>%s</color>已完成四杀！",},
{kill_count=5,kill_desc="<color=#ffb400>%s</color>已完成五杀！",},
{kill_count=6,kill_desc="<color=#ffb400>%s</color>已完成六杀！",},
{kill_count=7,kill_desc="<color=#ffb400>%s</color>已完成七连绝世！",},
{kill_count=8,kill_desc="<color=#ffb400>%s</color>已完成八杀贯日！",},
{kill_count=9,kill_desc="<color=#ffb400>%s</color>已完成九杀逆天！",},
{kill_count=10,kill_desc="<color=#ffb400>%s</color>已完成以一敌十！",}
},

continue_kill_score_meta_table_map={
},
pk_rank_reward={
{},
{rank_min=2,rank_max=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8],[8]=item_table[9],[9]=item_table[10]},},
{rank_min=3,rank_max=3,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[11],[7]=item_table[12],[8]=item_table[13],[9]=item_table[14]},},
{rank_min=4,rank_max=4,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[15],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[16],[7]=item_table[17],[8]=item_table[18],[9]=item_table[19]},},
{rank_min=5,rank_max=5,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[15],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[20],[7]=item_table[21],[8]=item_table[22],[9]=item_table[23]},},
{rank_min=6,rank_max=6,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[15],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[24],[7]=item_table[25],[8]=item_table[26],[9]=item_table[27]},},
{rank_min=7,rank_max=7,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[15],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[24],[7]=item_table[28],[8]=item_table[29],[9]=item_table[30]},},
{rank_min=8,rank_max=8,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[31],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[24],[7]=item_table[32],[8]=item_table[33],[9]=item_table[34]},},
{rank_min=9,rank_max=9,reward_item={[0]=item_table[1],[1]=item_table[31],[2]=item_table[5],[3]=item_table[4],[4]=item_table[24],[5]=item_table[35],[6]=item_table[36],[7]=item_table[37]},},
{rank_min=10,rank_max=10,reward_item={[0]=item_table[1],[1]=item_table[31],[2]=item_table[5],[3]=item_table[24],[4]=item_table[38],[5]=item_table[39],[6]=item_table[40]},}
},

pk_rank_reward_meta_table_map={
},
season_rank_reward={
{},
{rank_min=2,rank_max=3,reward_item={[0]=item_table[41],[1]=item_table[42],[2]=item_table[43],[3]=item_table[44],[4]=item_table[45],[5]=item_table[46]},},
{rank_min=5,rank_max=8,reward_item={[0]=item_table[47],[1]=item_table[42],[2]=item_table[43],[3]=item_table[48],[4]=item_table[49],[5]=item_table[50]},},
{rank_min=9,rank_max=15,reward_item={[0]=item_table[42],[1]=item_table[51],[2]=item_table[52],[3]=item_table[53],[4]=item_table[54]},},
{rank_min=16,rank_max=30,reward_item={[0]=item_table[42],[1]=item_table[55],[2]=item_table[56],[3]=item_table[57],[4]=item_table[58]},},
{rank_min=31,rank_max=50,reward_item={[0]=item_table[42],[1]=item_table[59],[2]=item_table[60],[3]=item_table[61],[4]=item_table[62],[5]=item_table[63]},},
{rank_min=51,rank_max=100,reward_item={[0]=item_table[42],[1]=item_table[64],[2]=item_table[60],[3]=item_table[65],[4]=item_table[66],[5]=item_table[67]},},
{rank_min=101,rank_max=999,reward_item={[0]=item_table[68],[1]=item_table[60],[2]=item_table[69],[3]=item_table[70],[4]=item_table[71]},}
},

season_rank_reward_meta_table_map={
},
konckout={
[1]={index=1,},
[2]={index=2,},
[3]={index=3,}
},

konckout_meta_table_map={
},
knockout_end_extra_score={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

knockout_end_extra_score_meta_table_map={
},
guess_cfg={
{},
{index=2,guess_count=10,cost_count=25,reward_item_1={[0]=item_table[72]},reward_item_2={[0]=item_table[73]},},
{index=3,guess_count=1,cost_count=50,reward_item_1={[0]=item_table[74]},reward_item_2={[0]=item_table[75]},}
},

guess_cfg_meta_table_map={
},
buff_cfg={
[0]={index=0,buff_icon="tszc_zjm_jian",buff_skill_icon_id=4,},
[1]={index=1,item_id=91313,buff_desc="防御提高：<color=#ebff7c>+50%</color>",buff_skill_icon_id=5,},
[2]={index=2,item_id=91311,buff_desc="血量回复<color=#ebff7c>+20%</color>",show_in_buff_list=0,},
[3]={index=3,item_id=91314,buff_icon="tszc_zjm_feixie",buff_desc="移速提高：<color=#ebff7c>+50%</color>",buff_skill_icon_id=21,},
[4]={index=4,buff_continue_time=5000,item_id=91316,buff_desc="无敌状态<color=#ebff7c>5秒</color>",buff_skill_icon_id=9,},
[5]={index=5,item_id=91315,buff_icon="tszc_zjm_jian",buff_desc="增加积分<color=#ebff7c>200</color>",}
},

buff_cfg_meta_table_map={
[5]=2,	-- depth:1
},
buff_refresh={
{},
{},
{},
{},
{},
{}
},

buff_refresh_meta_table_map={
},
alive_score={
{},
{},
{},
{}
},

alive_score_meta_table_map={
},
relive_pos_list={
[1]={index=1,camera_y=38,},
[2]={index=2,camera_y=-2,},
[3]={index=3,camera_y=-140,},
[4]={index=4,camera_y=39,},
[5]={index=5,camera_y=91,},
[6]={index=6,camera_y=-88,},
[7]={index=7,camera_y=41,},
[8]={index=8,},
[9]={index=9,camera_y=176,},
[10]={index=10,}
},

relive_pos_list_meta_table_map={
},
activity_time_default_table={},

other_default_table={open_day=0,open_level=400,standby_scene_id=7101,pk_scene_id=7102,scene_view_left_audition_rule="系统自动分配每场战斗，每个\n场景<color=#95d12b>10</color>人，直至剩下最后一人\n战斗过程中每人拥有<color=#95d12b>4</color>次复活\n剩余生命越高排名积分越高！",scene_view_left_knockout_rule="系统自动分配每场战斗，每个\n场景<color=#95d12b>10</color>人，直至剩下最后一人\n战斗过程中每人拥有<color=#95d12b>6</color>次复活\n剩余生命越高排名积分越高！",relive_time=7,pk_standy_time=5,},

round_default_table={round=1,subround=1,relive_count=4,type=1,round_name="海选赛第一轮",sub_round_name="第一场",subround_show_time="20:32-20:37",btn_name="",desc="海选赛排名前<color=#95d12b>100</color>可进入淘汰赛",},

continue_kill_score_default_table={type=2,kill_count=2,kill_desc="<color=#ffb400>%s</color>已完成双杀！",},

pk_rank_reward_default_table={rank_min=1,rank_max=1,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[76],[7]=item_table[77],[8]=item_table[78],[9]=item_table[79]},},

season_rank_reward_default_table={rank_min=1,rank_max=1,reward_item={[0]=item_table[80],[1]=item_table[81],[2]=item_table[42],[3]=item_table[43],[4]=item_table[82],[5]=item_table[83],[6]=item_table[84]},},

konckout_default_table={index=1,},

knockout_end_extra_score_default_table={},

guess_cfg_default_table={index=1,guess_count=20,cost_type=1,cost_count=10,reward_item_1={[0]=item_table[85]},reward_item_2={[0]=item_table[86]},},

buff_cfg_default_table={index=0,buff_continue_time=10000,item_id=91312,buff_icon="tszc_zjm_dunpai",buff_desc="伤害提高：<color=#ebff7c>+50%</color>",show_in_buff_list=1,buff_skill_icon_id="",},

buff_refresh_default_table={},

alive_score_default_table={},

relive_pos_list_default_table={index=1,camera_x=20,camera_y=-136,}

}

