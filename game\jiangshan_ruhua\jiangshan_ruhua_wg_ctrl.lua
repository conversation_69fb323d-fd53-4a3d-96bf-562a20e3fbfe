require("game/jiangshan_ruhua/jiangshan_ruhua_view")
require("game/jiangshan_ruhua/jiangshan_ruhua_wg_data")
require("game/jiangshan_ruhua/jiangshan_ruhua_gailv")
require("game/jiangshan_ruhua/jiangshan_ruhua_draw_times_tips")

JiangShanRuHuaWGCtrl = JiangShanRuHuaWGCtrl or BaseClass(BaseWGCtrl)
function JiangShanRuHuaWGCtrl:__init()
    if JiangShanRuHuaWGCtrl.Instance then
        ErrorLog("[JiangShanRuHuaWGCtrl] Attemp to create a singleton twice !")
    end
    JiangShanRuHuaWGCtrl.Instance = self
    self.view = JiangShanRuHuaView.New(GuideModuleName.JiangShanRuHuaView)
    self.data = JiangShanRuHuaWGData.New()
    self.probability_view = JiangShanRuHuaProbabilityView.New()
    self.select_times_tip_view = JSRHDrawTimesTips.New()
    self:RegisterAllProtocols()
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
    self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.OnDayPass, self))

    if not self.item_data_change then
        self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
    end
end

function JiangShanRuHuaWGCtrl:__delete()
    JiangShanRuHuaWGCtrl.Instance = nil

    self.view:DeleteMe()
    self.view = nil

    self.data:DeleteMe()
    self.data = nil

    self.probability_view:DeleteMe()
    self.probability_view = nil

    if self.select_times_tip_view then
        self.select_times_tip_view:DeleteMe()
        self.select_times_tip_view = nil
    end

    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function JiangShanRuHuaWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCRAJiangShanRuHuaInfo,'OnSCRAJiangShanRuHuaInfo')
    self:RegisterProtocol(SCRAJiangShanRuHuaRecordListInfo,'OnSCRAJiangShanRuHuaRecordListInfo')
    self:RegisterProtocol(SCRAJiangShanRuHuaDrawRewardInfo,'OnSCRAJiangShanRuHuaDrawRewardInfo')
    self:RegisterProtocol(SCRAJiangShanRuHuaBaoDiRewardDrawInfo,'OnSCRAJiangShanRuHuaBaoDiRewardDrawInfo')
end

function JiangShanRuHuaWGCtrl:OnDayPass()
    if self.view:IsOpen() then
        self.view:Flush(0, "flush_model")
    end
end

--请求
function JiangShanRuHuaWGCtrl:SendReq(opera_type, param1, param2)
    local param_t = {
        rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA,
        opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
    }
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function JiangShanRuHuaWGCtrl:MainuiOpenCreate()
    self.main_ui_load = true
    if self.data:GetActIsOpen() then
        self:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.INFO)
    end
end


function JiangShanRuHuaWGCtrl:OnSCRAJiangShanRuHuaInfo(protocol)
    self.data:SetInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    if FunOpen.Instance:GetFunIsOpened(GuideModuleName.JiangShanRuHuaView) then
        local btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA)
        local act_cfg = self.data:GetGradeCfg()
        if btn and act_cfg then
            btn:Flush("SetSprite", {act_cfg.res_name})
        end
    end
    RemindManager.Instance:Fire(RemindName.JiangShanRuHua)
end

function JiangShanRuHuaWGCtrl:OnSCRAJiangShanRuHuaRecordListInfo(protocol)
    self.data:SetRecord(protocol.record_list)
    if self.view:IsOpen() then
        self.view:Flush()
    end
end

function JiangShanRuHuaWGCtrl:OnSCRAJiangShanRuHuaDrawRewardInfo(protocol)
    local draw_times = self.data:GetDrawTimes()
    local consume_cfg = self.data:GetConsumeCfg()
    local consume_num = draw_times
    local str = string.format(Language.JiangShanRuHua.BtnStr, consume_num)
    local ok_func = function ()
        if self.view:IsOpen() then
            self.view:Flush()
        end

        JiangShanRuHuaWGCtrl.Instance:ClickUse(consume_num, function()
            JiangShanRuHuaWGCtrl.Instance:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.BUY, consume_num, 1)
        end)
    end

    local other_info = {}
    other_info.stuff_id = consume_cfg.cost_item_id
    other_info.times = consume_num
    other_info.spend = consume_cfg.consume_count
    other_info.again_text = str
    other_info.total_price_desc1 = Language.JiangShanRuHua.ValueDesc
    local data_list = self.data:CalDrawRewardList(protocol)

    --大奖展示.
    local item_ids = {}
    local reward_id_list = {}
    for i, v in ipairs(data_list) do
        if v.reward_item then
            if v.reward_type == JiangShanRuHuaWGData.REWARD_TYPE.BIG then
                table.insert(item_ids, v.reward_item.item_id)
            else
                table.insert(reward_id_list, v.reward_item)
            end
        end
    end

    local best_data = {}
    if IsEmptyTable(item_ids) then
        best_data = nil
    else
        best_data.item_ids = item_ids
    end
    other_info.best_data = best_data

	TipWGCtrl.Instance:ShowGetValueReward(reward_id_list, ok_func, other_info, false)

    -- 神品大奖弹窗
    local data_cfg_list = {}
    for index, value in ipairs(item_ids) do
        local cfg = TipWGData.Instance:GetBigRewardCfgByItemId(value)
        if not IsEmptyTable(cfg) then
            table.insert(data_cfg_list, cfg)
        end
    end
    if not IsEmptyTable(data_cfg_list) then
        TipWGCtrl.Instance:OpenBigRewardView(data_cfg_list)
    end
end

function JiangShanRuHuaWGCtrl:OnSCRAJiangShanRuHuaBaoDiRewardDrawInfo(protocol)
    self.data:SaveDrawInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.JiangShanRuHua)
end

function JiangShanRuHuaWGCtrl:OpenLingHeGailvView()
    self.probability_view:Open()
end

function JiangShanRuHuaWGCtrl:ClickUse(cost_num, func)
    --数量检测
   local cfg = self.data:GetConsumeCfg()
   local num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)

   local tips_data = {}
   tips_data.item_id = cfg.cost_item_id
   tips_data.price = cfg.consume_count
   tips_data.draw_count = cost_num
   tips_data.has_checkbox = true
   tips_data.checkbox_str = string.format("xuyuan_fresh_pool_%d", cost_num)
   TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, func, nil)
end

---comment
---@param data table { ok_func, cancel_func }
function JiangShanRuHuaWGCtrl:OpenSelectTimesView(data)
    self.select_times_tip_view:Open()
    self.select_times_tip_view:SetData(data)
end

function JiangShanRuHuaWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        local check_list = self.data:GetItemDataChangeList()
        if self.view and self.view:IsOpen() then
            for i, v in pairs(check_list) do
                if v == change_item_id then
                    self.view:FlushItemInfo()
                end
            end
        end
    end
end