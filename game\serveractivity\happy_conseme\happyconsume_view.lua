-- 消费欢乐颂系统

HappyConsumeView = HappyConsumeView or BaseClass(SafeBaseView)

function HappyConsumeView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/happy_consume_ui_prefab", "layout_happy_consume2")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
	self.item_cell = {}
end

function HappyConsumeView:__delete()
	if nil ~= self.item_cell then
		for k,v in ipairs(self.item_cell) do 
			v:DeleteMe()
		end
		self.item_cell = nil
	end
end

function HappyConsumeView:ReleaseCallBack()
	if nil ~= self.consume_grid then
		self.consume_grid:DeleteMe()
		self.consume_grid = nil
	end
	if CountDownManager.Instance:HasCountDown("act_happyconsume") then
		CountDownManager.Instance:RemoveCountDown("act_happyconsume")
	end
end

function HappyConsumeView:LoadCallBack()
	self.node_list["img_title"].image:LoadSprite(ResPath.GetActivityTitle("HuanLeSong"))
	self:CreateItemSlot()
end

function HappyConsumeView:FlushAllItem()
	local data = HappyConsumeWGData.Instance:GetConsumeData()
	 for i = 0 ,#data  do
	 	
	 	local item_data = data[i]
	 	self:FlushItem(item_data,i)
	 end
end

function HappyConsumeView:FlushItem(data,intdex)
	if nil == data then
		return
	end
	self.item_cell[intdex]:SetData(data.reward_item)
	local happy_recharge_exchange_flag = HappyConsumeWGData.Instance:GetComsumeExchangeFlag()
	if next(happy_recharge_exchange_flag) == nil then return end
	local recharge_cfg = HappyConsumeWGData.Instance:GetConsumeConfg(intdex)
	if recharge_cfg == nil then return end
	local cur_exchange_times = happy_recharge_exchange_flag[intdex]
	local can_exchange_times = recharge_cfg.can_exchange_times
	local left_exchange_times = can_exchange_times - cur_exchange_times

	local happy_recharge_count = HappyConsumeWGData.Instance:GetTotalJiFen()
	local cur_need_jifen = recharge_cfg.consume_point + cur_exchange_times * recharge_cfg.add_consume_point_once
	local can_lingyu = happy_recharge_count >= cur_need_jifen and left_exchange_times > 0
	local rich_color = can_lingyu and "007f18" or "f11536"
	local cur_need_str = left_exchange_times > 0 and tostring(cur_need_jifen) or "--"
	local str = string.format(Language.HappyConsume.NeedJiFen, cur_need_str)
	self.node_list["lbl_lq_times"..intdex].text.text = left_exchange_times .. "/" .. can_exchange_times
	self.node_list["rich_recharge_num"..intdex].text.text =  str
	XUI.SetButtonEnabled(self.node_list["btn_lingqu"..intdex], can_lingyu)
end

 function HappyConsumeView:CreateItemSlot()
	for i = 0, 5 do
		self.item_cell[i] = ItemCell.New()
		self.item_cell[i]:SetInstanceParent(self.node_list["ph_reward_"..i])
	end
	for i = 0, 5 do
		self.node_list["btn_lingqu"..i].button:AddClickListener(BindTool.Bind(self.OnClickLingQu,self,i))
	end	
end

function HappyConsumeView:OnClickLingQu(index)
	local data = HappyConsumeWGData.Instance:GetConsumeData()
	local item_data = data[index]
	if nil == item_data then return end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_HAPPY_CUNSUME,
		opera_type = RA_HAPPY_CONSUME_REQ_TYPE.RA_HAPPY_CONSUME_REQ_TYPE_EXCHANGE_ITEM,
		param_1 = item_data.seq
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function HappyConsumeView:OpenCallBack()
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_HAPPY_CUNSUME,
		opera_type = RA_HAPPY_CONSUME_REQ_TYPE.RA_HAPPY_CONSUME_REQ_TYPE_REQ_INFO
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function HappyConsumeView:CloseCallBack()

end

function HappyConsumeView:ShowIndexCallBack(index)
	self:Flush()
end

function HappyConsumeView:OnFlush(param_t, index)
	self:UpdateRemainTimeCD()
	self:FlushAllItem()
	local jifen = HappyConsumeWGData.Instance:GetTotalJiFen()
	self.node_list.lbl_consume_jifen.text.text = jifen
end

function HappyConsumeView:UpdateRemainTimeCD()
	local act_statu =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_HAPPY_CUNSUME)
	if CountDownManager.Instance:HasCountDown("act_happyconsume") then
		CountDownManager.Instance:RemoveCountDown("act_happyconsume")
	end
	if act_statu and ACTIVITY_STATUS.OPEN == act_statu.status then
		local next_time = act_statu.next_time or 0
		self:UpdataRollerTime(TimeWGCtrl.Instance:GetServerTime(), next_time)
		CountDownManager.Instance:AddCountDown("act_happyconsume", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)	
	else
		self:CompleteRollerTime()
	end
end

function HappyConsumeView:UpdataRollerTime(elapse_time, total_time)
	self.node_list.lbl_remain_time.text.text =  TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

function HappyConsumeView:CompleteRollerTime()
	self.node_list.lbl_remain_time.text.text = ""
end