require("game/capability_up_gift/capability_up_gift_wg_data")
require("game/capability_up_gift/capability_up_gift_view")

CapabilityUpGiftWGCtrl = CapabilityUpGiftWGCtrl or BaseClass(BaseWGCtrl)

function CapabilityUpGiftWGCtrl:__init()
	if CapabilityUpGiftWGCtrl.Instance then
		error("[CapabilityUpGiftWGCtrl]:Attempt to create singleton twice!")
	end

    CapabilityUpGiftWGCtrl.Instance = self

    self.data = CapabilityUpGiftWGData.New()
    self.view = CapabilityUpGiftView.New(GuideModuleName.CapabilityUpGiftView)

    self:RegisterAllProtocols()
end

function CapabilityUpGiftWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

    CapabilityUpGiftWGCtrl.Instance = nil
end


function CapabilityUpGiftWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCapabilityUpGiftInfo, "OnSCCapabilityUpGiftInfo")
end

function CapabilityUpGiftWGCtrl:OnSCCapabilityUpGiftInfo(protocol)
	self.data:SetCapUpGiftInfo(protocol)
	self:CheckNeedChangeActState()
end

function CapabilityUpGiftWGCtrl:CheckNeedChangeActState()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.CAP_UP_GIFT)
	if not act_cfg then
		return
	end

	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local end_time = self.data:GetCurGiftEndTime()
	local cur_is_buy = self.data:GetGiftCurIsBuyDone()
	local act_state = self.data:GetActState()
	local total_time = math.floor(end_time - cur_time)

	if act_state == ACTIVITY_STATUS.CLOSE and total_time > 0 and not cur_is_buy then
		self.data:SetActState(ACTIVITY_STATUS.OPEN)
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.CAP_UP_GIFT, end_time, act_cfg)
	elseif act_state == ACTIVITY_STATUS.OPEN and (total_time <= 0 or cur_is_buy) then

		if self.view:IsOpen() then
			self.view:Close()
		end

		self.data:SetActState(ACTIVITY_STATUS.CLOSE)
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.CAP_UP_GIFT, false)
	end
end