require("game/hidden_weapon/hidden_weapon_wg_data")
require("game/hidden_weapon/hidden_weapon_view")
require("game/hidden_weapon/utils/hidden_weapon_request")
require("game/hidden_weapon/build/hidden_weapon_equip_select_view")
require("game/hidden_weapon/detail/hw_detail_skill_tip")
require("game/hidden_weapon/detail/hw_equip_skill_tip")
require("game/hidden_weapon/detail/hidden_weapon_detail_view")
require("game/hidden_weapon/hidden_weapon_strengthen_view")
require("game/hidden_weapon/build/hidden_weapon_build_view")
require("game/hidden_weapon/detail/hw_detail_item_render")
require("game/hidden_weapon/hw_awaken_item_render")
require("game/hidden_weapon/strengthen/hidden_weapon_keming_handler")
require("game/hidden_weapon/hw_build_item_render")
require("game/hidden_weapon/build/hidden_weapon_material_render")
require("game/hidden_weapon/build/hidden_weapon_build_skill_group_render")
require("game/hidden_weapon/detail/hw_detail_skill_render")
require("game/hidden_weapon/build/hidden_weapon_quick_rl_view")

HiddenWeaponWGCtrl = HiddenWeaponWGCtrl or BaseClass(BaseWGCtrl)

function HiddenWeaponWGCtrl:__init()
    if HiddenWeaponWGCtrl.Instance then
        ErrorLog("[HiddenWeaponWGCtrl]:Attempt to create singleton twice!")
    end
    HiddenWeaponWGCtrl.Instance = self
    self.equip_bag_change_listeners = {}
    self.equip_grid_change_listeners = {}

    self.data = HiddenWeaponWGData.New()
    self.view = HiddenWeaponView.New(GuideModuleName.HiddenWeaponView)
    self.equip_choose_view = HiddenWeaponEquipSelectView.New(GuideModuleName.HiddenWeaponEquipSelectView)
    self.hidden_weapon_quick_rl = HiddenWeaponViewQuickRL.New()
    -- 技能tip
    self.detail_skill_view = HWDetailSkillTip.New(GuideModuleName.HWDetailSkillTip)
    self.equip_skill_view = HWEquipSkillTip.New(GuideModuleName.HWEquipSkillTip)

    self:RegisterAllProtocls()
end

function HiddenWeaponWGCtrl:__delete()
    self.equip_bag_change_listeners = nil
    self.equip_grid_change_listeners = nil
    self.delay_get_list = nil
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.hidden_weapon_quick_rl then
        self.hidden_weapon_quick_rl:DeleteMe()
        self.hidden_weapon_quick_rl = nil
    end

    if self.detail_skill_view then
        self.detail_skill_view:DeleteMe()
        self.detail_skill_view = nil
    end

    if self.equip_skill_view then
        self.equip_skill_view:DeleteMe()
        self.equip_skill_view = nil
    end

    if self.equip_choose_view then
        self.equip_choose_view:DeleteMe()
        self.equip_choose_view = nil
    end

    if self.hw_alert then
        self.hw_alert:DeleteMe()
        self.hw_alert = nil
    end

    HiddenWeaponWGCtrl.Instance = nil
end

-- tabindex: 1/2/3  weapont_type: 1/2
function HiddenWeaponWGCtrl:OpenWnd(tabindex, weapon_type)
    local param = nil
    if weapon_type then
        param = {weapon_type = weapon_type}
    end
    FunOpen.Instance:OpenViewByName(GuideModuleName.HiddenWeaponView, tabindex, param)
end

function HiddenWeaponWGCtrl:OpenEquipSelect(call_back, target, selected)
    -- 打开面板
    if self.equip_choose_view then
        self.equip_choose_view:SetConfirmCallback(call_back, target, selected)
    end
    ViewManager.Instance:Open(GuideModuleName.HiddenWeaponEquipSelectView)
end

function HiddenWeaponWGCtrl:OpenHiddenWeaponRL()
    -- 打开面板
    if not self.hidden_weapon_quick_rl:IsOpen() then
        self.hidden_weapon_quick_rl:Open()
    else
        self.hidden_weapon_quick_rl:Flush()
    end
end

function HiddenWeaponWGCtrl:RegisterAllProtocls()
    self:RegisterProtocol(CSShenJiEquipOpera)
    self:RegisterProtocol(CSShenJiQuickUpgrade)
    self:RegisterProtocol(SCShenJiEquipGrid, "OnSCShenJiEquipGrid")
    self:RegisterProtocol(SCShenJiEquipBag, "OnSCShenJiEquipBag")
    self:RegisterProtocol(SCShenJiReleaseSkillInfo, "OnSCShenJiReleaseSkillInfo")
end

-- enum GRID_SEND_REASON_TYPE
-- {
--     LOGIN = 0,                                      // 下发原因-登录
--     OPERA 1,                                          // 下发原因-主动请求
--     PUT_ON 2,                                         // 下发原因-穿戴
--     UPLEVEL_EXP 3,                                    // 下发原因-升级-只改变经验条
--     UPLEVEL_SUCC, 4                                  // 下发原因-升级-等级和经验条都改变
--     USE_EXP,         5                               // 下发原因-消耗经验
--     UPGRADE,        6                                // 下发原因-进阶
--     RAND_ATTR_START,     7                           // 下发原因-刻铭-开始
--     RAND_ATTR_BREAK,        8                        // 下发原因-刻铭突破
--     SPECIAL_EFFECT_UPLEVEL, 9                        // 下发原因-专属效果升级
--     UP_COLOR_STAR,            10                      // 下发原因-升品升星
--     ADD_EXP = 11,                                        // 下发原因-获得经验
--     ADD_EXP_DECOMPOSE,                              // 下发原因-分解获得经验
--     RAND_ATTR_REMOVE,                               // 下发原因-刻铭-放弃
--     RAND_ATTR_SAVE,                                 // 下发原因-刻铭-保存
-- };

function HiddenWeaponWGCtrl:CheckEquipCompose(protocol)
    -- 对比前后协议数据，判断装备分解
    if protocol.send_reason and protocol.send_reason == 12 then
        local item_list = {}
        if self.data.anqi_info and self.data.anqi_info.exp_value then
            local change = protocol.anqi_info.exp_value - self.data.anqi_info.exp_value
            if change > 0 then
                table.insert(item_list, {item_id = HiddenWeaponWGData.Instance:GetAnQiExpItem(), num = change})
            end
        end
        if self.data.ruanjia_info and self.data.ruanjia_info.exp_value then
            local change = protocol.ruanjia_info.exp_value - self.data.ruanjia_info.exp_value
            if change > 0 then
                table.insert(item_list, {item_id = HiddenWeaponWGData.Instance:GetRuanJiaExpItem(), num = change})
            end
        end
        if #item_list > 0 then
            TipWGCtrl.Instance:ShowGetReward(nil, item_list, false)
        end

    elseif protocol.send_reason == 2 then --穿戴
        local old_anqi_info = self.data.anqi_info
        local old_ruanjia_info = self.data.ruanjia_info
        local anqi_info = protocol.anqi_info
        local ruanjia_info = protocol.ruanjia_info
        if (old_anqi_info.item_id <= 0 and anqi_info.item_id > 0) or 
            (old_ruanjia_info.item_id <= 0 and ruanjia_info.item_id > 0) then
            return
        end
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiEquip.PUT_ON_TIP)

    elseif protocol.send_reason == 4 then --注灵
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_zhulin, is_success = true, pos = Vector2(-112, 80)})
    elseif protocol.send_reason == 6 then --升阶
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jinjie, is_success = true, pos = Vector2(-112, 80)})
    elseif protocol.send_reason == 14 then --刻铭替换
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_keming, is_success = true, pos = Vector2(-112, 80)})
    elseif protocol.send_reason == 8 then --突破
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_tupo, is_success = true, pos = Vector2(-112, 80)})
    elseif protocol.send_reason == 9 then --觉醒
        TipWGCtrl.Instance:ShowEffect(
            {effect_type = UIEffectName.s_juexing, is_success = true, pos = Vector2(-112, 80)}
        )
    elseif protocol.send_reason == 10 then
        -- 打造，对比前后变化
        local func_check = function(equip_info, new_equip_info)
            if equip_info and equip_info.item_id > 0 and new_equip_info and new_equip_info.item_id == equip_info.item_id then
                local new_color = new_equip_info.color
                local new_star = new_equip_info.star
                local pre_color = equip_info.color
                local pre_star = equip_info.star
                if new_color > pre_color or new_star > pre_star then
                    local data = HiddenWeaponWGData.Instance:GetCommonEquipVo(new_equip_info)
                    -- TipWGCtrl.Instance:ShowEffect(
                    --     {effect_type = UIEffectName.s_duanzao, is_success = true, pos = Vector2(10, 240)}
                    -- )
                    TipWGCtrl.Instance:ShowGetReward(nil, {data}, false)
                    return true
                end
            end
        end

        local check1 = func_check(self.data.anqi_info, protocol.anqi_info)
        if not check1 then
            func_check(self.data.ruanjia_info, protocol.ruanjia_info)
        end
    elseif protocol.send_reason == 11 then --加经验
        local list = self.data:GetSCShenJiEquipGrid()
        local old_anqi_val = list[1].protocol_equip_item.exp_value
        local old_ruanjia_val = list[2].protocol_equip_item.exp_value

        local new_anqi_val = protocol.anqi_info.exp_value
        local new_ruanjia_val = protocol.ruanjia_info.exp_value

        local add_anqi_val = new_anqi_val - old_anqi_val
        local add_ruanjia_val = new_ruanjia_val - old_ruanjia_val

        if add_anqi_val > 0 then
            local item_name = ToColorStr(Language.ShenJiEquip.ZL_EXP_NAME[1], ITEM_COLOR[2])
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, add_anqi_val))
        end

        if add_ruanjia_val > 0 then
            local item_name = ToColorStr(Language.ShenJiEquip.ZL_EXP_NAME[2], ITEM_COLOR[2])
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, add_ruanjia_val))
        end
    end
end

function HiddenWeaponWGCtrl:CheckEquipedBuild(protocol)
    -- 检查本次协议身上装备是否打造
end

-- 神机装备格子信息下发
function HiddenWeaponWGCtrl:OnSCShenJiEquipGrid(protocol)
    self:CheckEquipCompose(protocol)

    self.data:SetSCShenJiEquipGrid(protocol)
    for key, callback in pairs(self.equip_grid_change_listeners) do
        if callback then
            callback()
        end
    end

    HiddenWeaponWGData.Instance.remind_manager:FireAllRemind()
end

-- 神机装备背包下发
function HiddenWeaponWGCtrl:OnSCShenJiEquipBag(protocol)
    self:CheckBagCompose(protocol)
    self.data:SetSCShenJiEquipBag(protocol)
    for key, callback in pairs(self.equip_bag_change_listeners) do
        if callback then
            callback()
        end
    end

    if self.hidden_weapon_quick_rl:IsOpen() then
        self.hidden_weapon_quick_rl:Flush()
    end
    HiddenWeaponWGData.Instance.remind_manager:FireAllRemind()
end

function HiddenWeaponWGCtrl:CheckBagCompose(protocol)
    -- 打造成功，需要播放恭喜获得界面，并播放“打造成功”特效
    if protocol.send_reason == 2 and not IsEmptyTable(protocol.sj_bag_list) then
        local item_list = {}
        for k,v in pairs(protocol.sj_bag_list) do
            local data = HiddenWeaponWGData.Instance:GetCommonEquipVo(v)
            table.insert(item_list, data)
        end
        
        if not IsEmptyTable(item_list) then
            TipWGCtrl.Instance:ShowGetReward(nil, item_list, false)
        end
        --[[if data and data.equip then
            -- TipWGCtrl.Instance:ShowEffect(
            --     {effect_type = UIEffectName.s_duanzao, is_success = true, pos = Vector2(10, 240)}
            -- )
            local item_list = {data}
            
        end--]]


    elseif protocol.send_reason == 3 then--获得装备
        for k,v in pairs(protocol.sj_bag_list) do
            local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
            local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[v.color])
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.Add, item_name))
        end
    elseif protocol.send_reason == 4 then--神机百炼抽奖获得
         for k,v in pairs(protocol.sj_bag_list) do
             local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
             local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[v.color])
             self:InsertDelayList(item_name)
        end
    end
end

function HiddenWeaponWGCtrl:InsertDelayList(item_name)
    if not self.delay_get_list then
        self.delay_get_list = {}
    end
    table.insert(self.delay_get_list, function()
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.Add, item_name))
    end)
end

function HiddenWeaponWGCtrl:DoDelayList()
    for i, v in ipairs(self.delay_get_list or {}) do
        v()
    end
    self.delay_get_list = {}
end

-- 暗器技能
function HiddenWeaponWGCtrl:OnSCShenJiReleaseSkillInfo(protocol)
    --F1的技能特效  先屏蔽
    -- local hidden_weapon_vo = GameVoManager.Instance:CreateVo(HiddenWeaponVo)
    -- hidden_weapon_vo.obj_id = -1 -- 使用客戶端生成的obj_id
    -- hidden_weapon_vo.weapon_id = protocol.weapon_id
    -- hidden_weapon_vo.deliverer_id = protocol.deliverer_id
    -- hidden_weapon_vo.target_obj_id = protocol.target_obj_id
    -- hidden_weapon_vo.skill_id = protocol.skill_id
    -- hidden_weapon_vo.name = string.format("HiddenWeapon%d", protocol.weapon_id)
    -- Scene.Instance:CreateHiddenWeapon(hidden_weapon_vo)
end

-- 监听 神机装备背包下发
function HiddenWeaponWGCtrl:AddEquipBagChangeListener(callback)
    self.equip_bag_change_listeners = self.equip_bag_change_listeners or {}
    self.equip_bag_change_listeners[callback] = callback
end

--移除绑定回调
function HiddenWeaponWGCtrl:RemoveEquipBagChangeListener(callback)
    if self.equip_bag_change_listeners and callback then
        self.equip_bag_change_listeners[callback] = nil
    end
end

-- 监听 神机装备信息下发
function HiddenWeaponWGCtrl:AddEquipGridChangeListener(callback)
    self.equip_grid_change_listeners = self.equip_grid_change_listeners or {}
    self.equip_grid_change_listeners[callback] = callback
end

--移除绑定回调
function HiddenWeaponWGCtrl:RemoveEquipGridChangeListener(callback)
    if callback and self.equip_grid_change_listeners then
        self.equip_grid_change_listeners[callback] = nil
    end
end

function HiddenWeaponWGCtrl:LoadStartUI(view, area_name)
    area_name = area_name or "star_area"
    local star_area = view.node_list[area_name]
    local arr_star_ui
    if star_area then
        arr_star_ui = {}

        for i = 1, HW_CONST_PARAM.HW_ONCE_LAYER_STAR_NUM do
            local ui = star_area:FindObj(string.format("jin_group/jin_star (%s)", i))
            table.insert(arr_star_ui, ui)
        end

        for i = 1, HW_CONST_PARAM.HW_ONCE_LAYER_STAR_NUM do
            local ui = star_area:FindObj(string.format("red_group/yin_star (%s)", i))
            table.insert(arr_star_ui, ui)
        end
    end

    return arr_star_ui
end

--刷新暗器星数
function HiddenWeaponWGCtrl:RefreshShowStar(show_num, view, arr_name, area_name)
    arr_name = arr_name or "arr_star_ui"
    if not view[arr_name] then
        view[arr_name] = self:LoadStartUI(view, area_name)
    end

    -- show_num = 8
    local arr_ui = view[arr_name]
    for i, v in ipairs(arr_ui) do
        v:SetActive(false)
    end

    if show_num <= 0 then
        return
    end

    local star_num = 1
    local end_num = show_num

    for i = star_num, end_num do
        arr_ui[i]:SetActive(true)
    end
end

function HiddenWeaponWGCtrl:ShowDetailSkillView(data)
    if not data then
        return
    end

    self.detail_skill_view:SetData(data)
end

function HiddenWeaponWGCtrl:ShowEquipSkillView(data)
    if not data then
        return
    end

    self.equip_skill_view:SetData(data)
end

function HiddenWeaponWGCtrl:OpnHWAlertView(ok_func)
    if not self.hw_alert then
        self.hw_alert = Alert.New()
    end

    self.hw_alert:SetShowCheckBox(true, "hw_keming_is_ignore_alert")
    self.hw_alert:SetOkFunc(ok_func)
    self.hw_alert:SetLableString(Language.ShenJiEquip.AlertTxt)
    self.hw_alert:SetCheckBoxDefaultSelect(false)
    self.hw_alert:SetLableRectWidth(500)
    self.hw_alert:SetCheckBoxText(Language.ShenJiEquip.NoRemind)
    self.hw_alert:Open()
end