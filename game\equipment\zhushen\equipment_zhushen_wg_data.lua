function EquipmentWGData:InitZhuShenData()
    self.equip_zhushen_cfg = ConfigManager.Instance:GetAutoConfig("cast_soul_cfg_auto")
	self.equip_zhushen_level_cfg = ListToMap(self.equip_zhushen_cfg.level, "equip_index", "level") --铸神等级配置
	self.equip_zhushen_skill_level_cfg = ListToMap(self.equip_zhushen_cfg.suit, "seq", "suit_level") --铸神技能等级配置
	self.equip_zhushen_skill_seq_cfg = ListToMap(self.equip_zhushen_cfg.suit, "need_level")
	self.equip_zhushen_suit_cfg = self.equip_zhushen_cfg.suit

	self.equip_zhushentai_hole_cfg = self.equip_zhushen_cfg.god
	self.equip_zhushentai_hole_seq = ListToMap(self.equip_zhushen_cfg.god, "seq")
	self.equip_zhushentai_hole_level_cfg = ListToMap(self.equip_zhushen_cfg.god_level, "seq", "level")
    self.god_level_list = {}
    self.part_level_list = {}
	self.suit_level_list = {}

	self.equip_zhushen_stuff_id_list = {}
    for k, v in pairs(self.equip_zhushen_cfg.level) do
        self.equip_zhushen_stuff_id_list[v.cost_item_id] = true
    end

	self.equip_zhushentai_stuff_id_list = {}
    for k, v in pairs(self.equip_zhushen_cfg.god_level) do
        self.equip_zhushentai_stuff_id_list[v.cost_item_id] = true
    end

	RemindManager.Instance:Register(RemindName.Equipment_ZhuShen, BindTool.Bind(self.GetEquipZhuShiRed, self))
	RemindManager.Instance:Register(RemindName.Equipment_ZhuShenTai, BindTool.Bind(self.GetEquipZhuShiTaiRed, self))
end


function EquipmentWGData:DeleteZhuShenCfg()
	RemindManager.Instance:UnRegister(RemindName.Equipment_ZhuShen)
	RemindManager.Instance:UnRegister(RemindName.Equipment_ZhuShenTai)
end

function EquipmentWGData:GetEquipZhuShiRed()
	local data_list = self:GetEquipZhuShenList()
	if IsEmptyTable(data_list) then
		return 0
	end

	if self:GetEquipZhuShenSuitRedmind() or self:GetEquipZhuShenAllSkillRedmind() then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHUHUN_EQUIPMENT, 1, function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_zhushen)
            return true
        end)
        return 1
    end

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHUHUN_EQUIPMENT, 0)
	return 0
end

function EquipmentWGData:GetEquipZhuShiTaiRed()
	local data_list = self:GetEquipZhuShenList()
	if IsEmptyTable(data_list) then
		return 0
	end

	if self:GetZhuShenTaiAllHoleRedmind() then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHUSHENTAI_EQUIPMENT, 1, function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_zhushentai)
            return true
        end)
        return 1
    end

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHUSHENTAI_EQUIPMENT, 0)
	return 0
end

function EquipmentWGData:IsZhuShenChangeItem(change_item_id)
    return self.equip_zhushen_stuff_id_list[change_item_id] ~= nil
end

function EquipmentWGData:IsZhuShenTaiChangeItem(change_item_id)
    return self.equip_zhushentai_stuff_id_list[change_item_id] ~= nil
end

function EquipmentWGData:SetZhuShenLevelList(protocol)
	self.suit_level_list = protocol.suit_level_list
    self.part_level_list = protocol.part_level_list
    self.god_level_list = protocol.god_level_list
end

function EquipmentWGData:GetZhuShenTaiHoleCfg(seq)
	return self.equip_zhushentai_hole_seq[seq] or {}
end

--铸神台等级
function EquipmentWGData:GetZhuShenTaiPartInfo(seq)
	return self.god_level_list[seq] or 0
end

--技能等级信息
function EquipmentWGData:GetZhuShenEquipSkillLevel(seq)
	return self.suit_level_list[seq] or 0
end

--铸神技能索引
function EquipmentWGData:GetZhuShenSkillSeq(need_level)
	local data = self.equip_zhushen_skill_seq_cfg[need_level]
	return data and data.seq or 0
end

function EquipmentWGData:GetZhuShenSkillSuitLevelCfg(need_level)
	local data = self.equip_zhushen_skill_seq_cfg[need_level]
	return data and data.suit_level or 0
end

--下一级显示 铸神技能是否解锁
function EquipmentWGData:GetZhuShenSkillIsAct()
	local suit_level = self:GetZhuShenAllEquipNextLevel()
    local skill_seq = self:GetZhuShenSkillSeq(suit_level)
	local skill_level = self:GetZhuShenEquipSkillLevel(skill_seq)

	return skill_level > 0
end

--全身装备等级 下一阶段进度
function EquipmentWGData:GetZhuShenAllEquipNextLevel()
	local need_level_cfg = {}
	local cur_level = self:GetZhuShenAllEquipLevel()
	local index = 1
	for k, v in pairs(self.equip_zhushen_suit_cfg) do
		table.insert(need_level_cfg, v.need_level)
	end
	
	SortTools.SortAsc(need_level_cfg)

	for k, v in pairs(need_level_cfg) do
		if cur_level >= v then
			index = k + 1
		end
	end
	
	if index >= #need_level_cfg then
		index = #need_level_cfg
	end

	local next_level = need_level_cfg[index]
	return next_level
end

--当前全身装备已达到的进度等级
function EquipmentWGData:GetZhuShenAllEquipLevel()
	local min_level
	for equip_index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		local level = self:GetZhuShenEquipPartInfo(equip_index)
		if min_level == nil then
			min_level = level
		end

		if min_level >= level then
			min_level = level
		end
	end

	return min_level
end

--获取达到要求的装备数量
function EquipmentWGData:GetZhuShenOneEquipLevel()
	local min_level = 0
	for equip_index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		local level = self:GetZhuShenEquipPartInfo(equip_index)
		local next_level = self:GetZhuShenAllEquipNextLevel()
		if  level >= next_level then
			min_level = min_level + 1
		end
	end
	return min_level
end

--铸神台 特殊孔位 是否能升级
function EquipmentWGData:GetZhuShenTaiAllHoleLevel(sp_hole_level, min_level_limit)
	local min_level
	for hole = 0, 4 do
		local level = self:GetZhuShenTaiPartInfo(hole)
		if min_level == nil then
			min_level = level
		end

		if min_level >= level then
			min_level = level
		end
	end

	if sp_hole_level < 1 then
		return min_level >= min_level_limit
	end

	return min_level > sp_hole_level
end

--铸神台 普通孔位 是否全部激活
function EquipmentWGData:GetZhuShenTaiNorHoleIsAllAct()
	for i = 0, 4 do
		local nor_act = EquipmentWGData.Instance:GetZhuShenTaiHoleIsAct(i)
		if not nor_act then
			return false
		end
	end

	return true
end

--铸神技能配置
function EquipmentWGData:GetZhuShenSkillCfg()
	local skill_list = {}
	for k, v in pairs(self.equip_zhushen_cfg.god_skill) do
		local skill_level = self:GetZhuShenEquipSkillLevel(v.skill_id)
		local data = {
			skill_id = v.skill_id,
			skill_icon = v.skill_icon,
			skill_name = v.skill_name,
			skill_level = skill_level,
		}

		table.insert(skill_list, data)
	end

	return skill_list
end

--铸神技能等级配置
function EquipmentWGData:GetZhuShenSkillLevelCfg(seq, skill_level)
	local skill_level_cfg = self.equip_zhushen_skill_level_cfg[seq]
	if skill_level_cfg == nil then
		return nil
	end

	local max_skill_level = skill_level_cfg[#skill_level_cfg].suit_level
	if skill_level >= max_skill_level then
		skill_level = max_skill_level
	end

	return skill_level_cfg[skill_level]
end

--单技能最大等级
function EquipmentWGData:GetZhuShenSkillMaxLevelCfg(seq)
	local skill_level_cfg = self.equip_zhushen_skill_level_cfg[seq]
	if skill_level_cfg == nil then
		return nil
	end

	return skill_level_cfg[#skill_level_cfg].suit_level
end

--铸神等级配置
function EquipmentWGData:GetZhuShenEquipLevelCfg(equip_index, level)
	local level_cfg = self.equip_zhushen_level_cfg[equip_index]
	if level_cfg == nil then
		return nil
	end

	local max_level = level_cfg[#level_cfg].level
	if level >= max_level then
		level = max_level
	end
	
	return level_cfg[level]
end

--铸神台孔位等级配置
function EquipmentWGData:GetZhuShenTaiHoleLevelCfg(seq, level)
	local hole_level_cfg = self.equip_zhushentai_hole_level_cfg[seq]
	if hole_level_cfg == nil then
		return nil
	end

	local max_hole_level = hole_level_cfg[#hole_level_cfg].level
	if level >= max_hole_level then
		level = max_hole_level
	end

	return hole_level_cfg[level]
end

--铸神台孔位最大等级
function EquipmentWGData:GetZhuShenTaiHoleMaxLevel(seq)
	local level_cfg = self.equip_zhushentai_hole_level_cfg[seq] or {}
	local data = level_cfg[#level_cfg]
	return data and data.level or 0
end

--装备部位等级
function EquipmentWGData:GetZhuShenEquipPartInfo(equip_index)
	return self.part_level_list[equip_index] or 0
end

--部位最大等级
function EquipmentWGData:GetZhuShenEquipMaxLevelCfg(equip_index)
	local level_cfg = self.equip_zhushen_level_cfg[equip_index] or {}
	local data = level_cfg[#level_cfg]
	return data and data.level or 0
end

--铸神装备属性
function EquipmentWGData:GetZhuShenEquipAttrList(equip_index, level)
	local attr_list = {}
	local cfg = self:GetZhuShenEquipLevelCfg(equip_index, level)
	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 2
	if cfg == nil then
        return attr_list
    end

	for i = 1, max_attr_num do
		attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = em_data:GetAttrStrByAttrId(attr_id)
			local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
				add_value = 0,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }

			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_list
end

--铸神台孔位属性
function EquipmentWGData:GetZhuShenTaiHoleAttrList(seq, level)
	local attr_list = {}
	local cfg = self:GetZhuShenTaiHoleLevelCfg(seq, level)
	local next_level_cfg = self:GetZhuShenTaiHoleLevelCfg(seq, level + 1)
	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 4
	if cfg == nil then
        return attr_list
    end

	for i = 1, max_attr_num do
		attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value >= 0 then
			local attr_str = em_data:GetAttrStrByAttrId(attr_id)
			local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
				add_value = 0,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }

			if next_level_cfg ~= nil and level >= 0 then
				data.add_value = next_level_cfg["attr_value" .. i] - data.attr_value
			end

			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_list
end

--铸神台孔位数据
function EquipmentWGData:GetZhuShenTaiHoleList()
	local data_list = {}
	for k, v in pairs(self.equip_zhushentai_hole_cfg) do
		local hole_level = self:GetZhuShenTaiPartInfo(v.seq)
		local data = {
			seq = v.seq,
			min_level_limit = v.min_level_limit,
			name = v.name,
			level = hole_level,
		}
		data_list[k] = data
	end
	
	return data_list
end

--铸神台 孔位是否激活
function EquipmentWGData:GetZhuShenTaiHoleIsAct(seq)
	local hole_level = self:GetZhuShenTaiPartInfo(seq)
	return hole_level and hole_level > 0 or false
end

--铸神台 特殊孔位标记
function EquipmentWGData:GetZhuShenTaiIsSpecialHole(seq)
	local hole_cfg = self.equip_zhushentai_hole_cfg
	return seq == hole_cfg[#hole_cfg].seq
end

--装备列表
function EquipmentWGData:GetEquipZhuShenList()
	local data_list = EquipWGData.Instance:GetDataList()

	local equip_data = {}
	local seq = 1
	for k, v in pairs(data_list) do
		if v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
			equip_data[seq] = v
			seq = seq + 1
		end
	end

	local target_data_list = EquipmentWGData.SortEquipTabForAwake(equip_data)
	self.show_equip_data_list = target_data_list

	for k, v in pairs(target_data_list) do
		self.show_equip_list_id[v.index] = k
	end
	
	return target_data_list
end

--装备部位红点
function EquipmentWGData:GetEquipZhuShenPartRedmind(equip_index)
	local cur_equip_level = self:GetZhuShenEquipPartInfo(equip_index)
    local equip_level_cfg = self:GetZhuShenEquipLevelCfg(equip_index, cur_equip_level)
	if equip_level_cfg == nil then
        return false
    end

	local max_level = self:GetZhuShenEquipMaxLevelCfg(equip_index)
	if cur_equip_level >= max_level then
		return false
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(equip_level_cfg.cost_item_id)
	return has_num >= equip_level_cfg.cost_item_num
end

--铸神技能红点
function EquipmentWGData:GetEquipZhuShenSkillRedmind(skill_seq)
	local skill_level = self:GetZhuShenEquipSkillLevel(skill_seq)
	local skill_cfg = self:GetZhuShenSkillLevelCfg(skill_seq, skill_level + 1)
	if skill_cfg == nil then
		return false
	end

	local max_level = self:GetZhuShenSkillMaxLevelCfg(skill_seq)
	if skill_level >= max_level then
		return false
	end

	local cur_suit_level = self:GetZhuShenAllEquipLevel()
	return cur_suit_level >= skill_cfg.need_level
end

--铸神套装红点
function EquipmentWGData:GetEquipZhuShenSuitRedmind()
	if self.show_equip_data_list == nil then
		return false
	end
	
	for k, v in pairs(self.show_equip_data_list) do
		if self:GetEquipZhuShenPartRedmind(v.index) then
			return true
		end
	end

	return false
end

--铸神技能红点
function EquipmentWGData:GetEquipZhuShenAllSkillRedmind()
	local skill_cfg = self:GetZhuShenSkillCfg()
	for k, v in pairs(skill_cfg) do
		if self:GetEquipZhuShenSkillRedmind(v.skill_id) then
			return true
		end
	end

	return false
end

--铸神台孔位红点
function EquipmentWGData:GetZhuShenTaiHoleRedmind(seq)
	local hole_level = self:GetZhuShenTaiPartInfo(seq)
	local hole_cfg = self:GetZhuShenTaiHoleLevelCfg(seq, hole_level)
    if hole_cfg == nil then
        return false
    end

	local is_special_hole = self:GetZhuShenTaiIsSpecialHole(seq) --是否为特殊孔位
	local hole_other_cfg = self:GetZhuShenTaiHoleCfg(seq)
	local hole_all_act = self:GetZhuShenTaiNorHoleIsAllAct() --普通孔位是否全部激活
	local has_num = ItemWGData.Instance:GetItemNumInBagById(hole_cfg.cost_item_id)

	local is_up = self:GetZhuShenTaiAllHoleLevel(hole_level, hole_other_cfg.min_level_limit)
	if is_special_hole then
		if hole_all_act and is_up then --普通孔位全部激活  特殊孔位满足升级条件
			return has_num >= hole_cfg.cost_item_num
		else
			return false
		end
	end

	return has_num >= hole_cfg.cost_item_num
end

--铸神台所有红点
function EquipmentWGData:GetZhuShenTaiAllHoleRedmind()
    local hole_list = self:GetZhuShenTaiHoleList()
	if hole_list == nil then
		return false
	end

	for k, v in pairs(hole_list) do
		if self:GetZhuShenTaiHoleRedmind(v.seq) then
			return true
		end
	end

    return false
end

--铸神台跳转孔位
function EquipmentWGData:GetZhuShenTaiJumpHole()
    local hole_list = self:GetZhuShenTaiHoleList()
	if hole_list == nil then
		return 1
	end

    local jump_hole
	
	for k, v in pairs(hole_list) do
		if self:GetZhuShenTaiHoleRedmind(v.seq) then
			return v.seq
		end

		if jump_hole == nil then
			jump_hole = v.seq
		end
	end

    return jump_hole
end

function EquipmentWGData:GetZhuShenEquipJumpHole()
	local jump_hole
	if self.show_equip_data_list == nil then
		return 1
	end
	
	for k, v in pairs(self.show_equip_data_list) do
		if self:GetEquipZhuShenPartRedmind(v.index) then
			return k
		end

		if jump_hole == nil then
			jump_hole = k
		end
	end

	return jump_hole
end