---------------
--首充团购(新)
---------------
local ZheKouType = {
	CheckType = 0,
	BuyType = 1,
}
OpenZheKou = OpenZheKou or BaseClass(SafeBaseView)

function OpenZheKou:__init(act_id,zodaer)
	self.act_id = act_id
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_zhekoulibao")
	self:AddViewResource(0, "uis/view/act_operate_ui_prefab", "flower")
end

function OpenZheKou:__delete()

end

function OpenZheKou:ReleaseCallBack()
	if nil ~= self.title_list_view then
		self.title_list_view:DeleteMe()
		self.title_list_view = nil
	end
	if self.flush_event then 
		GlobalEventSystem:UnBind(self.flush_event)
		self.flush_event = nil
	end
	self.has_load = nil
	self.need_refresh = nil
end

function OpenZheKou:OpenCallBack()
	
end

function OpenZheKou:LoadCallBack()
	self.title_list_view = AsyncListView.New(OpenZheKouItem, self.node_list.ph_title_list)
	-- local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()   -- 开服天数
	-- self.data_list = ServerActivityWGData.Instance:GetZheKouReward(open_day)
	ServerActivityWGCtrl.Instance:AdiscountGiftInfoReq(ZheKouType.CheckType,-1)  -- 请求信息
	self.flush_event = GlobalEventSystem:Bind(ActEventType.FLUSH_ADISCOUNTGIFT,BindTool.Bind(self.RefreshView,self))
	self.has_load = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

-- 刷新当前视图
function OpenZheKou:RefreshView(param_list)
	if not self:IsOpen() then return end
	if not self.has_load then
		self.need_refresh = true
		return
	end
	if param_list == "daychange" then
		ServerActivityWGCtrl.Instance:AdiscountGiftInfoReq(ZheKouType.CheckType,-1)  -- 请求信息
		return
	end
	self:RefreshTopDesc()

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()   -- 开服天数
	self.data_list = ServerActivityWGData.Instance:GetZheKouReward(open_day)
	if self.title_list_view and next(self.data_list) then
		self.title_list_view:SetDataList(self.data_list,3)
	end
end

function OpenZheKou:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()   -- 开服天数

	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			local day = 60*60*24
			local star_str = (math.floor(act_info.start_time/day)+(open_day-1))*day - 60*60*8
			local end_str = star_str + day - 1
			star_str = TimeUtil.FormatSecond2MYHM(star_str)
			end_str = TimeUtil.FormatSecond2MYHM(end_str)
			self.node_list.version_act_time.text.text = star_str .. "————" .. end_str
		end
	end
	local data = ServerActivityWGData.Instance:GetZheKouDescInfo(open_day)
	if data then
		self.node_list.version_act_des.text.text = Language.OpenServer.ActShuoMing_1 .. data.activity_explain
	end
end


--------------------------------OpenZheKouItem   -------------------------   list列表
OpenZheKouItem = OpenZheKouItem or BaseClass(BaseRender)
function OpenZheKouItem:__init()
	self:CreateChild()
end

function OpenZheKouItem:__delete()
	if self.item_cells then
		for k,v in pairs(self.item_cells) do
			v:DeleteMe()
		end
	end
end

function OpenZheKouItem:CreateChild()
	self.item_cells = {}
	local param
	for i = 1, 2 do
		param = self.node_list["ph_cell_" .. i]
		self.item_cells[i] = ItemCell.New(param)
		self.item_cells[i]:SetIsShowTips(true)
	end

	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind1(self.OnClickLingQuCallBack, self))
end

function OpenZheKouItem:OnFlush()
	if not self.data then return end

	for i = 1, 2 do
		local data = self.data.goods_item[i-1]
		if data then
			self.item_cells[i]:SetVisible(true)
			self.item_cells[i]:SetData({item_id = data.item_id, num = data.num, is_bind = data.is_bind})
		else
			self.item_cells[i]:SetVisible(false)
		end
	end
	self.node_list.old_price.text.text = self.data.show_gold
	self.node_list.cur_price.text.text = self.data.need_gold

	local zhekou_num =  self.data.discount               --tonumber(string.format("%.2f",self.data.need_gold/self.data.show_gold))*100  
	self.node_list.zhekou_num.text.text = string.format(Language.OpenServer.EveryDayShopZhe,zhekou_num) 
	local count = ServerActivityWGData.Instance:GetZheKouInfo(self.data.seq)
	if not count then return end

	local can_buy_num = self.data.count_limit - count
	self.node_list.can_buy_num.text.text = can_buy_num
	
	XUI.SetButtonEnabled(self.node_list.btn_buy,can_buy_num >0)
	self.node_list.btn_text.text.text = can_buy_num >0 and Language.OpenServer.Buy or Language.OpenServer.SellAll

end

function OpenZheKouItem:OnClickLingQuCallBack()
	ServerActivityWGCtrl.Instance:AdiscountGiftInfoReq(ZheKouType.BuyType, self.data.seq)
end
