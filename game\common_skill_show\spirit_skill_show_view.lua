SpiritSkillShowView = SpiritSkillShowView or BaseClass(SafeBaseView)

function SpiritSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_spirit_skill_show_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function SpiritSkillShowView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShen.SkillPreview   
    -- 普攻
    XUI.AddClickEventListener(self.node_list.normal_attack_btn, BindTool.Bind(self.OnClickNormalAttackBtn, self))

    -- 技能
    if self.node_list.skill_list and self.skill_btn_list == nil then
        self.skill_btn_list = {}
        local node_num = self.node_list.skill_list.transform.childCount
        for i = 1, node_num do
            self.skill_btn_list[i] = SkillShowSkillRender.New(self.node_list.skill_list:FindObj("skill" .. i))
            self.skill_btn_list[i]:SetIndex(i)
            self.skill_btn_list[i]:SetNeedChangeSkillBtnPos(false)
            self.skill_btn_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self))
        end
    end

    if not self.skill_pre_view then
        self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage, true)
    end

    self.skill_play_timestemp = 0
    self.normal_attack_play_timestemp = 0
end

function SpiritSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()

    if self.skill_btn_list then
        for k, v in pairs(self.skill_btn_list) do
            v:DeleteMe()
        end
        self.skill_btn_list = nil
    end

    if self.skill_pre_view then
        self.skill_pre_view:DeleteMe()
        self.skill_pre_view = nil
    end

    self.nuqi_type = nil
    self.general_attack_list = nil
    self.skill_play_timestemp = nil
    self.normal_attack_play_timestemp = nil
end

function SpiritSkillShowView:SetShowDataAndOpen(nuqi_type)
	self.nuqi_type = nuqi_type
	self:Open()
end

function SpiritSkillShowView:OnFlush()
    if not self.nuqi_type then
        return
    end

    self.general_attack_list = CultivationWGData.Instance:GetActiveNormalSkillListByType(self.nuqi_type)
    local star_index = 1
    -- 刷新技能格子
    local skill_list = CultivationWGData.Instance:GetActiveSkillListByType(self.nuqi_type)
    for k, v in pairs(self.skill_btn_list) do
        local skill_index = star_index + k

        v:SetData({skill_id = skill_list[skill_index], skill_level = 1})
    end

    self:OnClickNormalAttackBtn(true)
end

-- 点击普攻
function SpiritSkillShowView:OnClickNormalAttackBtn(is_change_list)
    if IsEmptyTable(self.general_attack_list) then
        return
    end

    if self:IsLimitClick() then
		return
	end

    if not self.general_attack_index then
        self.general_attack_index = 1
    else
        self.general_attack_index = self.general_attack_index + 1
        if self.general_attack_index > #self.general_attack_list then
            self.general_attack_index = 1
        end
    end
    
    self:SimulateTSGeneralAttackOpera(is_change_list)
end

-- 模拟普攻
function SpiritSkillShowView:SimulateTSGeneralAttackOpera(is_change_list)
	if IsEmptyTable(self.general_attack_list) then
		return
	end

	local skill_id = self.general_attack_list[self.general_attack_index]
	if not skill_id then
		return
	end

	local do_next_action_time = SkillPreviewData.Instance:GetRoleSkillTime(skill_id)
    self.normal_attack_play_timestemp = Status.NowTime + do_next_action_time
    self:CleanSkillBtnCDTimer()
    self:SetAllSkillBtnCD(string.format("%.1f", do_next_action_time), do_next_action_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(do_next_action_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), do_next_action_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, do_next_action_time)
        end
    )

    if self.skill_pre_view:GetPreviewIsLoaded() then
        self.skill_pre_view:PlaySkill(skill_id, is_change_list)
    else
        self.skill_pre_view:SetPreviewLoadCb(function()
            self.skill_pre_view:PlaySkill(skill_id, is_change_list)
        end)
    end
end

-- 点击技能
function SpiritSkillShowView:OnClickSkillBtn(item)
    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self:CleanSkillBtnCDTimer()

    -- 0.5s 等带特效后小半截播完
    local skill_show_time = SkillPreviewData.Instance:GetRoleSkillTime(data.skill_id)
    self.skill_play_timestemp = Status.NowTime + skill_show_time
    self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, skill_show_time)
        end
    )

    if self.skill_pre_view:GetPreviewIsLoaded() then
        self.skill_pre_view:PlaySkill(data.skill_id)
    else
        self.skill_pre_view:SetPreviewLoadCb(function()
            self.skill_pre_view:PlaySkill(data.skill_id)
        end)
    end
end

function SpiritSkillShowView:SetAllSkillBtnCD(time, total_time)
    if self.skill_btn_list then
        for k,v in pairs(self.skill_btn_list) do
            v:SetSkillBtnCD(time, total_time)
        end
    end
end

-- 清除倒计时器1
function SpiritSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end

function SpiritSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp or Status.NowTime < self.normal_attack_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end
