require("game/festival_activity/festival_login_youli/festival_login_youli_wg_data")
require("game/festival_activity/festival_login_youli/festival_login_youli_item")
--运营活动-登录有礼
FestivalLoginYouLiWGCtrl = FestivalLoginYouLiWGCtrl or BaseClass(BaseWGCtrl)

function FestivalLoginYouLiWGCtrl:__init()
	if FestivalLoginYouLiWGCtrl.Instance then
		ErrorLog("[FestivalLoginYouLiWGCtrl] Attemp to create a singleton twice !")
	end
	FestivalLoginYouLiWGCtrl.Instance = self

	self:RegisterAllProtocols()
    self.data = FestivalLoginYouLiWGData.New()
end

function FestivalLoginYouLiWGCtrl:__delete()
	FestivalLoginYouLiWGCtrl.Instance = nil

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

end

function FestivalLoginYouLiWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSCFestivalActLoginGift)
    self:RegisterProtocol(SCCFestivalActLoginGiftInfo,'OnSCCFestivalActLoginGiftInfo')
end

--登录奖励信息
function FestivalLoginYouLiWGCtrl:OnSCCFestivalActLoginGiftInfo(protocol)
	self.data:SetLoginRewardInfo(protocol)
	FestivalActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LOGINGIFT)
    FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2262, "RefreshLogin")
end

function FestivalLoginYouLiWGCtrl:SendActivityRewardOp(opera_type,param1)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSCFestivalActLoginGift)
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol:EncodeAndSend()
end