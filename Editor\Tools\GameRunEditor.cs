﻿using Nirvana;
using System;
using System.Collections.Generic;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;

public class GameRunEditor : OdinEditorWindow
{
	enum ClassPrintType
	{
		None = 0,
		Type_1 = 1,
		Type_2 = 2,
		Type_3 = 3,
		Type_4 = 4,
	}

	[ShowInInspector]
	[TabGroup("调试开关")]
	[Space(10)]
	[Title("分类打印")]
	[LabelText("启动分类打印")]
	[OnValueChanged("OnClassPrintChanged")]
	private bool mClassPrintSwitch;

	[ShowInInspector]
	[TabGroup("调试开关")]
	[LabelText("当前分类打印类型")]
	[OnValueChanged("OnClassPrintTypeChanged")]
	private ClassPrintType mClassPrintType;

	[TabGroup("游戏控制")]
	[Title("游戏模式")]
	[Button("进入快速模式", ButtonSizes.Medium)]
	private void EnterFastMode()
	{
		GameLuaOpera("xinShou");
		Time.timeScale = 2f;
	}

	[TabGroup("游戏控制")]
	[Button("恢复正常模式", ButtonSizes.Medium)]
	private void RestoreNormalMode()
	{
		GameLuaOpera("resetRoleAttr");
		Time.timeScale = 1f;
	}

	[TabGroup("游戏控制")]
	[LabelText("游戏时间速率")]
	[Range(0.1f, 5f)]
	[OnValueChanged("OnTimeScaleChanged")]
	private float TimeScale = 1;

	[TabGroup("游戏控制")]
	[Button("清空本地Unity数据缓存", ButtonSizes.Large)]
	[GUIColor(1f, 0.5f, 0.5f)]
	private void ClearPlayerPrefs()
	{
		PlayerPrefs.DeleteAll();
		LoadSettings();
	}

    [ShowInInspector]
    [PropertyOrder(10)]
    [TabGroup("预制体编辑")]
    [Title("文本查找")]
    [LabelText("查找文本")]
    private string findText = "";

    [PropertyOrder(10)]
    [TabGroup("预制体编辑")]
    [Button("查找文本", ButtonSizes.Medium)]
	private void SearchTextButton()
	{
        SearchText();
	}

    [ShowInInspector]
    [PropertyOrder(11)]
    [TabGroup("预制体编辑")]
    [Title("重命名")]
    [LabelText("重命名")]
	private string rename;

    [PropertyOrder(11)]
    [TabGroup("预制体编辑")]
    [Button("重命名", ButtonSizes.Medium)]
	private void ChangeNameButton()
	{
		ChangeName();
	}

	[TabGroup("预制体编辑")]
	[Title("组件操作")]
	[Button("拷贝组件", ButtonSizes.Medium)]
	private void CopyComponentsButton()
	{
		GetAllChilds(Selection.activeGameObject, pri_my_list);
	}

	[TabGroup("预制体编辑")]
	[Button("应用拷贝", ButtonSizes.Medium)]
	private void PasteComponentsButton()
	{
		GameObject[] tmpGameObj = Selection.gameObjects;
		foreach (var item in tmpGameObj)
		{
			PasteChildComponent(item, pri_my_list);
		}
	}

	[TabGroup("预制体编辑")]
	[Button("移除无用的 Renderer", ButtonSizes.Medium)]
	private void RemoveCanvasRendererButton()
	{
		RemoveCanvasRenderer();
	}

	public class MyComponentList
	{
		public MyComponentList()
		{
		}

		public List<Component> gameObjList;
		public List<MyComponentList> nextList;
	}
	MyComponentList pri_my_list = new MyComponentList();

	[MenuItem("Tools/游戏运行编辑器")]
	public static GameRunEditor Init()
	{
		GameRunEditor window = GetWindow<GameRunEditor>();
		window.titleContent = new GUIContent("游戏运行编辑器");
		window.Show();
		return window;
	}
	const string SendProtocolDebugKey = "send_protocol_debug_switch";
	const string RecvProtocolDebugKey = "recv_protocol_debug_switch";
	const string ItemIdDebugKey = "show_item_id_switch";
	const string AutoRunTaskDebugKey = "auto_run_task_switch";
	const string MainTaskPopDebugKey = "main_task_pop_switch";
    const string SystemTipsDbugKey = "system_tips_switch";

    protected override void OnEnable()
	{
		base.OnEnable();
		LoadSettings();
    }

	private void LoadSettings()
	{
		mClassPrintType = (ClassPrintType)PlayerPrefs.GetInt("MyClassPrintType");
		mClassPrintSwitch = PlayerPrefs.GetInt("ClassPrintSwitch") == 1;
		TimeScale = Time.timeScale;
    }

	private void OnClassPrintChanged()
	{
		PlayerPrefs.SetInt("ClassPrintSwitch", mClassPrintSwitch ? 1 : 0);
		GameLuaOpera("classPrint");
	}

	private void OnClassPrintTypeChanged()
	{
		PlayerPrefs.SetInt("MyClassPrintType", (int)mClassPrintType);
		if (GameRoot.Instance)
		{
			GameLuaOpera("changePrintType", (int)mClassPrintType);
		}
	}

	private void OnTimeScaleChanged()
	{
		Time.timeScale = TimeScale;
	}

    private void GameLuaOpera(string peraKey)
    {
        if (GameRoot.Instance)
        {
            var operaStr = string.Format("/jy_cmd InsertLua GameDebugSwitch(\"{0}\")", peraKey);
            GameRoot.Instance.ExecuteGm(operaStr);
        }
    }

    private void GameLuaOpera(string peraKey, int param2)
    {
        if (GameRoot.Instance)
        {
			var operaStr = string.Format("/jy_cmd InsertLua GameDebugSwitch(\"{0}\",{1})", peraKey, param2);
			GameRoot.Instance.ExecuteGm(operaStr);
		}
    }

	private void SearchText()
	{
		List<UnityEngine.Object> objects = new List<UnityEngine.Object>();
		Text[] uiText = GameObject.FindObjectsOfType<Text>();
		foreach (var item in uiText)
		{
			if (item.text.Contains(findText))
			{
				EditorGUIUtility.PingObject(item.gameObject);
				objects.Add(item.gameObject);
			}
		}
        TMP_Text[] uiTMP = GameObject.FindObjectsOfType<TMP_Text>();
        foreach (var item in uiTMP)
        {
            if (item.text.Contains(findText))
            {
                EditorGUIUtility.PingObject(item.gameObject);
                objects.Add(item.gameObject);
            }
        }
        Selection.objects = objects.ToArray();
	}

	private void ChangeName()
	{
		GameObject[] gameObject = Selection.gameObjects;
		foreach (var item in gameObject)
		{
			item.name = this.rename + (item.transform.GetSiblingIndex() + 1);
		}
	}

	private static void GetAllChilds(GameObject transformForSearch, MyComponentList next)
	{
		List<Component> childsOfGameobject = new List<Component>();
		next.gameObjList = childsOfGameobject;
		next.nextList = new List<MyComponentList>();

		foreach (var item in transformForSearch.GetComponents<Component>())
		{
			childsOfGameobject.Add(item);
		}
		foreach (Transform item in transformForSearch.transform)
		{
			MyComponentList tmpnext = new MyComponentList();
			GetAllChilds(item.gameObject, tmpnext);
			next.nextList.Add(tmpnext);
		}
		return;
	}

	private static bool SpecialComponent(System.Type type, Component component)
	{
		if (type == typeof(Text))
		{
			Text text = (Text)component;
			string s = text.text;
			UnityEditorInternal.ComponentUtility.PasteComponentValues(component);
			text.text = s;
			return true;
		}
        if (type == typeof(TMP_Text))
        {
            TMP_Text text = (TMP_Text)component;
            string s = text.text;
            UnityEditorInternal.ComponentUtility.PasteComponentValues(component);
            text.text = s;
            return true;
        }

        return false;
	}

	private static bool ContinueComponent(System.Type type)
	{

		if (type == typeof(UINameTable) || type == typeof(UIEventTable) || type == typeof(UIVariable) || type == typeof(ScrollRect) || type == typeof(Button) || type == typeof(Toggle) || type == typeof(ToggleActivator))
		{
			return true;
		}
		return false;
	}

	private static void PasteChildComponent(GameObject gameObj, MyComponentList next)
	{
		if (next.gameObjList != null)
		{
			foreach (var copiedComponent in next.gameObjList)
			{
				gameObj.SetActive(copiedComponent.gameObject.activeSelf);
				if (!copiedComponent) continue;

				if (ContinueComponent(copiedComponent.GetType())) continue;

				UnityEditorInternal.ComponentUtility.CopyComponent(copiedComponent);

				if (gameObj.GetComponent(copiedComponent.GetType()) == null)
				{
					UnityEditorInternal.ComponentUtility.PasteComponentAsNew(gameObj);
				}
				else if (!SpecialComponent(copiedComponent.GetType(), gameObj.GetComponent(copiedComponent.GetType())))
				{
					UnityEditorInternal.ComponentUtility.PasteComponentValues(gameObj.GetComponent(copiedComponent.GetType()));
				}
			}
		}

		if (next.nextList != null)
		{
			List<Transform> TmpListTrans = new List<Transform>();
			foreach (Transform item in gameObj.transform)
			{
				TmpListTrans.Add(item);
			}
			int i = 0;
			foreach (var item in next.nextList)
			{
				if (i < TmpListTrans.Count)
				{
					PasteChildComponent(TmpListTrans[i].gameObject, item);
				}
				i++;
			}
		}
	}

	private static void RemoveCanvasRenderer()
	{
		GameObject gameObject = Selection.activeGameObject;
		CanvasRenderer[] canvasRenderers = gameObject.GetComponentsInChildren<CanvasRenderer>(true);
		for (int i = 0; i < canvasRenderers.Length; ++i)
		{
			var canvasRender = canvasRenderers[i];
			if (null == canvasRender.GetComponent<Graphic>())
			{
				GameObject.DestroyImmediate(canvasRender);
			}
		}
	}
}


