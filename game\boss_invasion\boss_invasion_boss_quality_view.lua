BOSSInvasionBossQualityView = BOSSInvasionBossQualityView or BaseClass(SafeBaseView)

function BOSSInvasionBossQualityView:__init()
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_boss_quality_view")
end

function BOSSInvasionBossQualityView:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_display"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		
		self:FlushModel()
	end

	if not self.boss_quality_list then
		self.boss_quality_list = {}
		
		local boss_color_cfg = BOSSInvasionWGData.Instance:GetCurBossColorListCfg()
		for i = 1, 4 do
			self.boss_quality_list[i] = BIBossQualityItemCellRender.New(self.node_list["color_item" .. i])
			self.boss_quality_list[i]:SetData(boss_color_cfg[i])
		end
	end

	self.node_list.desc_title.text.text = Language.BOSSInvasion.BossQualityTitle
	self.node_list.desc_title_tip.text.text = Language.BOSSInvasion.BossQualityTip
	self.node_list.desc_add_pro_tip.text.text = Language.BOSSInvasion.BossQualityAddProTip

	XUI.AddClickEventListener(self.node_list["btn_add_pro"], BindTool.Bind(self.OnClickAddProBtn, self))
end

function BOSSInvasionBossQualityView:ReleaseCallBack()
	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.boss_quality_list then
		for k, v in pairs(self.boss_quality_list) do
			v:DeleteMe()
		end

		self.boss_quality_list = nil
	end
end

function BOSSInvasionBossQualityView:OnFlush()
	local cur_boss_num = BOSSInvasionWGData.Instance:GetCurBossNum()
	self.node_list.desc_cur_value.text.text = cur_boss_num

	local cur_boss_quality_cfg = BOSSInvasionWGData.Instance:GetCurBossColorCfg()
	
	if cur_boss_quality_cfg then 
		self.node_list.desc_name.text.text = cur_boss_quality_cfg.color_name
		
		local bundle, asset = ResPath.GetBossInvasionImg(cur_boss_quality_cfg.name_bg)
		self.node_list.name_bg.image:LoadSprite(bundle, asset, function ()
			self.node_list.name_bg.image:SetNativeSize()
		end)

		self.node_list.name_bg:CustomSetActive(true)
	else
		self.node_list.name_bg:CustomSetActive(false)
	end

	local slider_value = BOSSInvasionWGData.Instance:CalBossQualitySLidervalue()
	self.node_list.boss_num_slider.slider.value = slider_value

	for k, v in pairs(self.boss_quality_list) do
		v:Flush()
	end
end

function BOSSInvasionBossQualityView:FlushModel()
	local model_data = BOSSInvasionWGData.Instance:GetOtherCfg()
	local display_data = {}
	display_data.should_ani = true

	if model_data.model_show_itemid ~= 0 and model_data.model_show_itemid ~= "" then
		local split_list = string.split(model_data.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_data.model_show_itemid
		end
	end

	display_data.bundle_name = model_data.model_bundle_name
    display_data.asset_name = model_data.model_asset_name
	local model_show_type = tonumber(model_data["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
	display_data.model_rt_type = ModelRTSCaleType.XL
	display_data.can_drag = false

	if model_data.model_pos and model_data.model_pos ~= "" then
		local pos_list = string.split(model_data.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if model_data.model_rot and model_data.model_rot ~= "" then
		local rot_list = string.split(model_data.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_data.model_scale and model_data.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_data.model_scale
	end

	self.model_display:SetData(display_data)
end

function BOSSInvasionBossQualityView:OnClickAddProBtn()
	-- local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)
	-- if act_is_open then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.ACTIsOpenCanNotUpQuality)
	-- 	return
	-- end

	local status = BOSSInvasionWGData.Instance:GetCurActStatus()

	if status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS or status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.ACTIsOpenCanNotUpQuality)
		return
	end

	-- 最高品质不可再增加
	local is_max_quality = BOSSInvasionWGData.Instance:IsBossMaxQuality()

	if is_max_quality then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.BossQualityMAxColor)
	else
		BOSSInvasionWGCtrl.Instance:OpenQualityAddTip()
	end
end

-----------------------------BIBossQualityItemCellRender----------------------------
BIBossQualityItemCellRender = BIBossQualityItemCellRender or BaseClass(BaseRender)

function BIBossQualityItemCellRender:LoadCallBack()
	if not self.data_list then
		self.data_list = AsyncListView.New(ItemCell, self.node_list.data_list)
		self.data_list:SetStartZeroIndex(true)
	end
end

function BIBossQualityItemCellRender:__delete()
	if self.data_list then
		self.data_list:DeleteMe()
		self.data_list = nil
	end
end

function BIBossQualityItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.data_list:SetDataList(self.data.show_reward_item)

	local bundle, asset = ResPath.GetBossInvasionImg(self.data.icon)
	self.node_list.icon.image:LoadSprite(bundle, asset, function()
		self.node_list.icon.image:SetNativeSize()
	end)

	self.node_list.num.text.text = self.data.need_num

	local cur_boss_num = BOSSInvasionWGData.Instance:GetCurBossNum()
	self.node_list.flag_enough:CustomSetActive(cur_boss_num >= self.data.need_num)
end