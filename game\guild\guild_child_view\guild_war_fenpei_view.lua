GuildWarFeiPeiView = GuildWarFeiPeiView or BaseClass(SafeBaseView)

function GuildWarFeiPeiView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_war_fenpei")
	self:SetMaskBg()
end

function GuildWarFeiPeiView:ReleaseCallBack()
    if self.apply_list then
        self.apply_list:DeleteMe()
        self.apply_list = nil
    end

    if self.cell_list then
    	for k,v in pairs(self.cell_list) do
    		v:DeleteMe()
    	end
    	self.cell_list = {}
    end
    self.cur_select_cell = nil
end

function GuildWarFeiPeiView:OpenCallBack()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_post = role_vo.guild_post
	local is_mengzhu = guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.JiaMengZhu
	if is_mengzhu then
		local type_oper = GUILD_BATTLE_REWARD_APPLY_OR_ALLOC.GUILD_BATTLE_REWARD_INFO_SEND
		GuildWGCtrl.Instance:SendCSGuildBattleRewardApply(type_oper)
	end
end

function GuildWarFeiPeiView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Guild.GuildWarFeiPeiTitle
    self:SetSecondView(nil, self.node_list["size"])
    -- self.list_view = AsyncListView.New(WarFenPeiItem, self.node_list["list_view"])
    XUI.AddClickEventListener(self.node_list["tips_btn"],BindTool.Bind(self.ClickTipsBtn,self))
    XUI.AddClickEventListener(self.node_list["close_apply_panel"],BindTool.Bind(self.CloseApplyPanel,self))

    self.cell_list = {}
	self.data_list = GuildBattleRankedWGData.Instance:GetFenPeiItemInfo()
    local list_delegate = self.node_list["list_view"].list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self)

	self.apply_list = AsyncListView.New(FeiPeiNameItem, self.node_list["apply_list"])
    self.apply_list:SetSelectCallBack(BindTool.Bind(self.OnSelectName, self))
    self.node_list["apply_panel"]:SetActive(false)
end

function GuildWarFeiPeiView:GetListViewNumbers()
	return #self.data_list
end


function GuildWarFeiPeiView:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	cell_index = cell_index + 1
	if not item_cell then
		item_cell = WarFenPeiItem.New(cell.gameObject)
		self.cell_list[cell] = item_cell
		item_cell:SetClickSelectApply(BindTool.Bind(self.ItemClickSelectApply,self))
	end
	local item_data = self.data_list[cell_index]
	item_cell:SetIndex(cell_index)
	item_cell:SetData(item_data)
end

function GuildWarFeiPeiView:OnFlush()
    self.data_list = GuildBattleRankedWGData.Instance:GetFenPeiItemInfo()
    local max_apply_num = GUILD_BATTLE_FENPEI_REWARD_LIMIT_NUM
    local get_num = GuildBattleRankedWGData.Instance:GetFenPeiGetNum() 
    local count_num = max_apply_num - get_num
	local color = get_num < max_apply_num and COLOR3B.GREEN or COLOR3B.RED
    self.node_list["max_text"].text.text = string.format(Language.Guild.FenPeiReawrdMax,color,count_num,max_apply_num)

    self.node_list["list_view"].scroller:RefreshAndReloadActiveCellViews(true)
end

function GuildWarFeiPeiView:ClickTipsBtn()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.FenPeiRuleTitle)
		role_tip:SetContent(Language.Guild.FenPeiRuleContent, nil, nil, nil, true)
	end
end

local RectTransUtility = UnityEngine.RectTransformUtility
function GuildWarFeiPeiView:ItemClickSelectApply(cell)
	if cell then
		local name_data = GuildBattleRankedWGData.Instance:GetApplyRoleList(cell.data.item_id)
		if IsEmptyTable(name_data) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FenPeiNotApply)
			return
		end
		self.cur_select_cell = cell
		local perfab = cell:GetApplyPanel()
		if perfab then
			local screen_pos_tbl = RectTransUtility.WorldToScreenPoint(UICamera, perfab.rect.position)
			local rect = self.node_list["list_view_panel"].rect
			local _, local_position_tbl = RectTransUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos_tbl, UICamera, Vector2(0, 0))
			local pos_y = local_position_tbl.y
			local pos_x = self.node_list["apply_panel"].rect.anchoredPosition.x
			self.node_list["apply_panel"].rect.anchoredPosition = Vector2(pos_x,pos_y)
			self.node_list["apply_panel"]:SetActive(true)
		end
		
		self.apply_list:SetDataList(name_data)
	end
end

function GuildWarFeiPeiView:OnSelectName(item, cell_index, is_default, is_click)
	if is_default then return end
	local data = item:GetData()
	if not data then return end
	if self.cur_select_cell then
		self.cur_select_cell:SetSelectName(data.role_name)
		self.cur_select_cell:SetSelectApplyRoleId(data.role_id)
	end
	self:CloseApplyPanel()
end

function GuildWarFeiPeiView:CloseApplyPanel()
	self.node_list["apply_panel"]:SetActive(false)
	if self.cur_select_cell then
		self.cur_select_cell:CloseApplyPanel()
	end
end

--------------------------------------------------------------------------------------
WarFenPeiItem = WarFenPeiItem or BaseClass(BaseRender)

function WarFenPeiItem:__init()
    self.item_cell = ItemCell.New(self.node_list["item_cell"])
    XUI.AddClickEventListener(self.node_list["select_apply_btn"],BindTool.Bind(self.ClickSelectApply,self))
    XUI.AddClickEventListener(self.node_list["opert_btn"],BindTool.Bind(self.ClickOpertBtn,self))
    self.node_list["apply_panel"]:SetActive(false)
    local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_post = role_vo.guild_post
	self.is_mengzhu = guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.JiaMengZhu
	self.select_apply_role_id = nil
end

function WarFenPeiItem:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    if self.apply_list then
        self.apply_list:DeleteMe()
        self.apply_list = nil
    end
    self.select_apply_role_id = nil
end

function WarFenPeiItem:OnFlush()
	if not self.data then return end
	local reward_item = {item_id = self.data.item_id,num = self.data.num,is_bind = self.data.is_bind}
	self.item_cell:SetData(reward_item)
	local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id or 0)
	self.node_list["item_name"].text.text = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
	local count_num = self.data.num
	count_num = count_num > 0 and count_num or 0 
	self.node_list["count_num"].text.text = string.format(Language.Guild.ShengYuStr,count_num)
	
	self.node_list["select_apply_btn"]:SetActive(self.is_mengzhu)
	if self.is_mengzhu then
		self.node_list["opert_btn_text"].text.text = Language.Guild.ApplyBtnStr2
		self.node_list["jianto_down"]:SetActive(false)
		self.node_list["jiantou_up"]:SetActive(true)
		local name_data = GuildBattleRankedWGData.Instance:GetApplyRoleList(self.data.item_id)
		if IsEmptyTable(name_data) then
			self.node_list["select_apply_text"].text.text = Language.Guild.FenPeiNotApply
		else
			self.node_list["select_apply_text"].text.text = name_data[1] and name_data[1].role_name or Language.Guild.FenPeiNotApply
			self.select_apply_role_id = name_data[1].role_id
		end
	else
		self.node_list["opert_btn_text"].text.text = Language.Guild.ApplyBtnStr1
		self.node_list["opert_btn"]:SetActive(self.data.is_apply ~= 1)
		self.node_list["has_apply"]:SetActive(self.data.is_apply == 1)
	end
end


function WarFenPeiItem:SetSelectName(name)
	self.node_list["select_apply_text"].text.text = name
end

function WarFenPeiItem:SetSelectApplyRoleId(role_id)
	self.select_apply_role_id = role_id
end

function WarFenPeiItem:SetClickSelectApply(call_back)
	self.select_apply_callback = call_back
end

function WarFenPeiItem:ClickSelectApply()
	local name_data = GuildBattleRankedWGData.Instance:GetApplyRoleList(self.data.item_id)
	if IsEmptyTable(name_data) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FenPeiNotApply)
		return
	end
	if self.select_apply_callback then
		self.select_apply_callback(self)
	end
	self.node_list["jianto_down"]:SetActive(true)
	self.node_list["jiantou_up"]:SetActive(false)
end

function WarFenPeiItem:GetApplyPanel()
	return self.node_list["apply_panel"]
end

function WarFenPeiItem:CloseApplyPanel()
	self.node_list["jianto_down"]:SetActive(false)
	self.node_list["jiantou_up"]:SetActive(true)
end

function WarFenPeiItem:ClickOpertBtn()
	local item_id = self.data.item_id
	if self.is_mengzhu then
		--分配奖励
		if not self.select_apply_role_id then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FenPeiNotApply)
			return
		end
		local type_oper = GUILD_BATTLE_REWARD_APPLY_OR_ALLOC.GUILD_BATTLE_REWARD_ALLOC
		local role_id = self.select_apply_role_id
		if role_id then
			GuildWGCtrl.Instance:SendCSGuildBattleRewardApply(type_oper,role_id,item_id)
		end
	else
		--申请奖励
		local type_oper = GUILD_BATTLE_REWARD_APPLY_OR_ALLOC.GUILD_BATTLE_REWARD_APPLY
		local get_num = GuildBattleRankedWGData.Instance:GetFenPeiGetNum()
		if get_num >= GUILD_BATTLE_FENPEI_REWARD_LIMIT_NUM then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FenPeiInviteMax)
		else
			local role_id = RoleWGData.Instance:GetRoleVo().role_id
			GuildWGCtrl.Instance:SendCSGuildBattleRewardApply(type_oper,role_id,item_id)
		end
	end
end

--------------FeiPeiNameItem-----------
FeiPeiNameItem = FeiPeiNameItem or BaseClass(BaseRender)

function FeiPeiNameItem:__init()
end

function FeiPeiNameItem:__delete()
end

function FeiPeiNameItem:OnFlush()
	if not self.data then return end
	self.node_list["name"].text.text = self.data.role_name
end

function FeiPeiNameItem:OnSelectChange(is_select)
	self.node_list["select_bg"]:SetActive(is_select)
end