-- 数字键盘
-- max_value不为空时，max_length无效

SpecialKeypad = SpecialKeypad or BaseClass(SafeBaseView)
SpecialKeypad.MaxVaule = 99999999999999

local ORI_POS = Vector2(-67,118)

function SpecialKeypad:__init(view_name,max_length, max_value)
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/cangbaoge_ui_prefab", "layout_num")
	self.ui_config = {"uis/view/cangbaoge_ui_prefab","SpecialKeyPad"}
	self.ok_callback = nil							-- 确定回调
	self.max_value = 999							-- 可输入的最大值
	self.min_value = 0
	self.input_num = 0								-- 输入的数字
	self.input_str = ""
	self.click_btn_func = nil
	if nil ~= max_length then
		local temp_max = math.pow(10, max_length)
		if nil == max_value or max_value > temp_max then
			max_value = temp_max
		end
	end
	if nil ~= max_value then
		self:SetMaxValue(max_value)
	end
end

function SpecialKeypad:__delete()
	
end

function SpecialKeypad:ReleaseCallBack()
	if self.text_component ~= nil then
		self.text_component = nil
	end
	self.is_load_complete = false
	self.click_btn_func = nil
	self.ok_callback = nil
end
function SpecialKeypad:CloseCallBack()
	-- self.click_btn_func = nil
	-- self.ok_callback = nil
	self:Reset()
end
-- 设置确定按钮回调事件
function SpecialKeypad:SetOkCallBack(func)
	if "function" == type(func) then
		self.ok_callback = func
	else
		ErrorLog("[SpecialKeypad] set ok callback is not func")
	end
end
function SpecialKeypad:SetBtnClickCallBack(func)
	if "function" == type(func) then
		self.click_btn_func = func
	else
		ErrorLog("[SpecialKeypad] click_btn_func is not func")
	end
end
-- 设置输入最大值
function SpecialKeypad:SetMaxValue(max_value, lock)
	self.is_lock = lock or false
	if nil ~= max_value and max_value >= 0 then
		self.max_value = max_value > 99999999999 and 99999999999 or max_value
	end
end

function SpecialKeypad:SetMinValue(min_value)
	if nil ~= min_value and min_value >= 0 then
		self.min_value = min_value
		if self.input_num < self.min_value then
			self.input_num = self.min_value
		end
	end
end

function SpecialKeypad:LoadCallBack()	
	self:RegisterAllEvents()

	self.is_load_complete = true
	-- if self.is_should_setnum then
	-- 	self.is_should_setnum = false
	-- 	self:SetNumFunc()
	-- end

	-- if self.is_should_setborder == true then
	-- 	self.is_should_setborder = false
	-- 	self:SetBorderActive(self.set_border_value)
	-- 	self.set_border_value = nil
	-- end

	if self.is_should_setposition == true then
		self.is_should_setposition = false
		self:SetPosition(self.setposition_param[1],self.setposition_param[2])
		self.setposition_param = nil
	end
	if self.is_should_setposition_1 == true then
		self.is_should_setposition_1 = false
		self:SetPosition1(self.setposition_container)
		self.setposition_container = nil
	end
end

function SpecialKeypad:RegisterAllEvents()
	for i = 0, 9 do
		XUI.AddClickEventListener(self.node_list["btn_num_" .. i], BindTool.Bind2(self.OnClickBtn, self, i))
		-- self.node_list["btn_num_" .. i]:setTouchEnabled(true)
	end
	XUI.AddClickEventListener(self.node_list["btn_num_del"], BindTool.Bind1(self.OnClickDel, self))
	XUI.AddClickEventListener(self.node_list["btn_num_ok"], BindTool.Bind1(self.OnClickOK, self))
	XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind1(self.OnClickOK, self))
end

function SpecialKeypad:OpenCallBack()
	-- if not self.is_load_complete then
	-- 	self.is_should_setnum = true
	-- 	return
	-- end
	self:SetNumFunc()		
end

function SpecialKeypad:SetNumFunc()
	if self.text_component ~= nil then
		self:SetNum(tonumber(self.text_component.text.text))
	end
end

function SpecialKeypad:ShowIndexCallBack()
	-- self:SetPosition(ORI_POS)
	self.is_first_click = true
end

function SpecialKeypad:Reset()
	self.input_num = 0
	self.text_component = nil
	self.max_value = 999
	self.min_value = 0
	self.ok_callback = nil
	self.click_btn_func = nil 
	self.is_up = false
	self.is_first_click = true
end

function SpecialKeypad:OnFlush(param_t)
	-- self.special_keypad:SetNum(self.item_num)
	-- self.special_keypad:SetNode(self.node_list["label_trade_up"],max)
	-- self.special_keypad:SetBorderActive(self.show_border)
	-- if not param_t then return end
	for k,v in pairs(param_t) do
		if k == "keypad" then
			local data = param_t["keypad"]
			if data.item_num then
				self:SetNum(data.item_num)
			end
			if data.node and data.max then
				self:SetNode(data.node,data.max)
				self:SetMaxValue(data.max)
			end
			self:SetBorderActive(data.is_border_active or false)
			if data.ok_func then
				self:SetOkCallBack(data.ok_func)
			end
			if data.btn_func then
				self:SetBtnClickCallBack(data.btn_func)
			end
			if data.is_up == nil then data.is_up = false end
			if data.pos_table then
				-- local node = data.is_up then self.node_list.triangleup or self.node_list.triangle
				-- node.rect.anchoredPosition = Vector2(data.pos_table.x,data.pos_table.y)
				-- print_error("change pos =========",self.node_list.triangleup.rect.anchoredPosition,data.pos_table,false == nil)
				self:SetPosition(data.pos_table, data.is_up)
			end
			if data.min_value then
				self:SetMinValue(data.min_value)
			end
		elseif k == "send_flower" then
			local data = param_t["send_flower"]
			if data.item_num then
				self:SetNum(data.item_num)
			end
			if data.node and data.max then
				self:SetNode(data.node,data.max)
				self:SetMaxValue(data.max)
			end
			self:SetBorderActive(data.is_border_active or false)
			if data.ok_func then
				self:SetOkCallBack(data.ok_func)
			end
			if data.btn_func then
				self:SetBtnClickCallBack(data.btn_func)
			end
			if data.is_up == nil then data.is_up = false end
			if data.pos_table then
				self:SetPosition(data.pos_table, data.is_up)
			end
			if data.pos_table_1 then
				self:SetPosition1(data.pos_table_1)
			else
				self.is_should_setposition_1 = false
			end
		end
	end

	-- local data = param_t["all"]
	-- print_error("SpecialKeypad=====",data)
	-- if key == "all" then
		
	-- end

	if self.input_str ~= "" then
		self.node_list["lbl_pop_num"].text.text = self.input_str
		if self.text_component then
			self.text_component.text.text = self.input_str
		end
	else
		self.node_list["lbl_pop_num"].text.text = tostring(self.input_num)
		if self.text_component then
			self.text_component.text.text = tostring(self.input_num)
		end
	end
end

function SpecialKeypad:SetNode(text_component,max_value)
	if self.text_component == nil then
		self.text_component = text_component
	end
	if nil ~= max_value then
		self:SetMaxValue(max_value)
	end	
	-- self:Open()
end

function SpecialKeypad:OnClickBtn(num)
	if self.is_first_click then
		self.input_num = num
		self.is_first_click = nil
	else
		self.input_num = self.input_num * 10 + num
	end

	if self.is_lock then
		if self.input_num > self.max_value then
			self.input_num = math.floor(self.input_num / 10)
		end
	else
		if self.input_num > self.max_value then
			self.input_num = self.max_value
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
		end
	end

	if self.input_num < self.min_value then
		self.input_num = self.min_value
	end

	if self.click_btn_func ~= nil then
		self.click_btn_func(self.input_num)
	end
	self:SetNum(self.input_num)
	self:SetPopString("")
end

function SpecialKeypad:OnClickDel()
	self:SetNum(math.floor(self.input_num / 10))
	if self.click_btn_func ~= nil then
		self.click_btn_func(math.floor(self.input_num / 10))
	end
end

-- 点击确定按钮
function SpecialKeypad:OnClickOK()
	if nil ~= self.ok_callback then
		self.ok_callback(self:GetNum())
	end

	self:Close()
end

function SpecialKeypad:GetText()
	return tostring(self:GetNum())
end

function SpecialKeypad:SetText(text)
	self:SetNum(tonumber(text))
end

function SpecialKeypad:GetNum()
	return self.input_num
end

function SpecialKeypad:SetNum(num)
	if self.min_value and num < self.min_value then
		num = self.min_value
	end
	self.input_num = num
	self:Flush()
end

function SpecialKeypad:SetPopString(str)
	self.input_str = str
	self:Flush()
end

function SpecialKeypad:SetBorderActive(is_active)
	-- if not self.is_load_complete then
	-- 	self.is_should_setborder = true
	-- 	self.set_border_value = is_active
	-- 	return
	-- end
	self.node_list["border"]:SetActive(is_active)
end

function SpecialKeypad:SetPosition( vec2 ,direction)
	if not self.is_load_complete then
		self.is_should_setposition = true
		self.setposition_param = {}
		self.setposition_param[1] = vec2
		self.setposition_param[2] = direction
		return
	end
	
	if direction then
		self.node_list.triangle:SetActive(false)
		self.node_list.triangleup:SetActive(true)
		self.node_list.all_container.transform:SetParent(self.node_list.triangleup.transform)
		if self.node_list.triangleup then
			self.node_list.triangleup.rect.anchoredPosition = vec2
		end
	else
		self.node_list.triangle:SetActive(true)
		self.node_list.triangleup:SetActive(false)
		self.node_list.all_container.transform:SetParent(self.node_list.triangle.transform)
		if self.node_list.triangle then
			self.node_list.triangle.rect.anchoredPosition = vec2
		end
	end
end

function SpecialKeypad:SetPosition1( vec2)
	if not self.is_load_complete then
		self.is_should_setposition_1 = true
		self.setposition_container= vec2
		return
	end
	self.node_list.all_container.rect.anchoredPosition = vec2

end