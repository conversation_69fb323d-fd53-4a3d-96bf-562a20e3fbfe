using System;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

[Serializable, VolumeComponentMenu("Addition-Post-processing/New Motion Blur")]
public class NewMotionBlur : VolumeComponent, IPostProcessComponent
{
    public ClampedFloatParameter SampleDist = new ClampedFloatParameter(0f, 0, 20);
    public ClampedFloatParameter SampleStrength = new ClampedFloatParameter(0f, 0, 20);
    public bool IsActive()
    {
        return active && SampleDist.value != 0;
    }

    public bool IsTileCompatible()
    {
        return false;
    }
}
