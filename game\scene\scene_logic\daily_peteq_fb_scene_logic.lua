DailyPeteqFbSceneLogic = DailyPeteqFbSceneLogic or BaseClass(CommonFbLogic)

function DailyPeteqFbSceneLogic:__init()

end

function DailyPeteqFbSceneLogic:__delete()
	
end

function DailyPeteqFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	DailyWGCtrl.Instance:Close()

	FuBenWGCtrl.Instance:OpenTaskFollow()
	FuBenWGCtrl.Instance:UpdataTaskFollow()

	-- XuiBaseView.CloseAllView()
end

function DailyPeteqFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end


function DailyPeteqFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	if RoleWGData.Instance.role_vo.level > 130 then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Daily)
	end
end