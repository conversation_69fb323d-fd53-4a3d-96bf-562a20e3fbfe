function BossNewPrivilegeView:ZBYCOpenCallBack()
	BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.INFO)
end

function BossNewPrivilegeView:ZBYCLoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_activate"], BindTool.Bind1(self.OnClickActivate, self)) --激活特权
	XUI.AddClickEventListener(self.node_list["btn_open"], BindTool.Bind1(self.OnClickSetOpenState, self)) --开启/关闭特权
end

function BossNewPrivilegeView:ZBYCOnFlush()
	self:FlushBtnState()
	self:FlushPrivilegePanel()
	local fb_data_list = BossPrivilegeWGData.Instance:GetPrivilegeBossTimes()
	local cur_level, max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
	local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive()

	local index = 1
	for k, v in pairs(fb_data_list) do
		local type_cfg = BossPrivilegeWGData.Instance:GetSceneTypeInfoCfgByType(v.boss_type)
		local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesListCfgByLevel(cur_level + 1)
		if not IsEmptyTable(type_cfg) then
			local active = type_cfg.is_lock == 0
			self.node_list["boss_info_item" .. index]:CustomSetActive(active)

			if active then
				local str_color = v.times > 0 and "#99ffbb" or "#ff9292"
				-- local outline_color = v.times > 0 and "#294E39FF" or "#7A3030FF"
				local times_str = ToColorStr(v.times, str_color)
				self.node_list["boss_fb_name" .. index].text.text = type_cfg.scene_name
				self.node_list["boss_fb_times" .. index].text.text = string.format(Language.BossPrivilege.ZBCountText,
					times_str)
				if not is_active or cur_level >= max_level then
					self.node_list["boss_fb_next_times" .. index]:CustomSetActive(false)
				else
					self.node_list["boss_fb_next_times" .. index]:CustomSetActive(true)

					local next_times = next_times_list[index].times
					local str_color_next = next_times > 0 and "#99ffbb" or "#ff9292"
					local next_times_str = ToColorStr(next_times, str_color_next)
					self.node_list["boss_fb_next_times" .. index].text.text = string.format(
						Language.BossPrivilege.ZBCountText, next_times_str)
				end

				index = index + 1
			else
				self.node_list["boss_fb_next_times" .. index]:CustomSetActive(false)
			end
		end
	end

	self.node_list.text_attr:SetActive(not is_active)
	if not is_active then
		self.node_list.text_attr.text.text = Language.BossPrivilege.ActiveStr
	end

	self.node_list.zb_desc.text.text = Language.BossPrivilege.ZBDescInfoNotAct
end

--按钮状态
function BossNewPrivilegeView:FlushBtnState()
	local is_open = BossPrivilegeWGData.Instance:GetActivateState()
	local is_times = BossPrivilegeWGData.Instance:GetIsPrivilegeTimes()
	local open_str = ""
	if is_open > 0 and is_times > 0 then
		open_str = Language.BossPrivilege.BtnOpenTxt
	elseif is_open < 1 or is_times < 1 then
		open_str = Language.BossPrivilege.BtnCloseTxt
	end
	local Zan_Ting = is_open > 0 and is_times > 0
	local Kai_QI = is_open < 1 or is_times < 1

	if Zan_Ting then
		local zt_bundle, zt_asset = ResPath.GetBossUI("a3_boss_btn_zt")
		self.node_list["zb_btn_open_icon"].image:LoadSprite(zt_bundle, zt_asset, function()
			self.node_list["zb_btn_open_icon"].image:SetNativeSize()
		end)
	elseif Kai_QI then
		local bf_bundle, bf_asset = ResPath.GetBossUI("a3_boss_btn_bf1")
		self.node_list["zb_btn_open_icon"].image:LoadSprite(bf_bundle, bf_asset, function()
			self.node_list["zb_btn_open_icon"].image:SetNativeSize()
		end)
	end
	-- self.node_list["zt_anniu"]:SetActive(Zan_Ting)
	-- self.node_list["kq_anniu"]:SetActive(Kai_QI)
	self.node_list["open_state_txt"].text.text = open_str
	-- self.node_list["open_effect"]:SetActive(is_open > 0 and is_times > 0) --开启特效
end

function BossNewPrivilegeView:FlushPrivilegePanel()
	local cur_level, max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
	-- self.node_list["max_level"]:SetActive(cur_level >= max_level)
	local up_text = cur_level >= max_level and Language.BossPrivilege.BtnUpgradeTxt4 or Language.BossPrivilege.BtnUpgradeTxt3
	self.node_list.privilege_up_lv_text.text.text = up_text
end

function BossNewPrivilegeView:OnClickActivate()
	BossPrivilegeWGCtrl.Instance:OpenBossPrivilegeUpgradeView()
end

--激活/升级特权
function BossNewPrivilegeView:OnClickLevelUp()
	local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive() --是否激活过
	if not is_active then                                                  --激活
		local times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(1)

		local ok_func = function()
			local enough = RoleWGData.Instance:GetIsEnoughUseGold(times_list.condition_value)
			if enough then
				BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.LEVEL_UP) --升级特权请求(0为激活)
				--首次激活自动开启特权
				BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.SET_STATUS,
					BOSS_DROP_PRIVILEGE_OPEN_TYPE.OPEN)
			else
				VipWGCtrl.Instance:OpenTipNoGold()
			end
		end

		local str = string.format(Language.BossPrivilege.DiscountBuyTips, times_list.condition_value)
		local check_string = "boss_privilege"
		TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string, nil)
	else
		local cur_level, max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
		local next_level = cur_level >= max_level and max_level or cur_level + 1
		local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(next_level)
		if not next_times_list or cur_level >= max_level then
			return
		end

		local text_dec = ""
		local is_enough_money
		if next_times_list.condition_type == 1 then --灵玉类型
			text_dec = string.format(Language.BossPrivilege.ArrayBuyTips1, next_times_list.condition_value)
			is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(next_times_list.condition_value)
		elseif next_times_list.condition_type == 2 then --直购类型
			local price = RoleWGData.GetPayMoneyStr(next_times_list.condition_value, next_times_list.rmb_type,
				next_times_list.rmb_seq)
			text_dec = string.format(Language.BossPrivilege.ArrayBuyTips2, price)
		end

		local ok_func = function()
			if next_times_list.condition_type == 1 then
				if is_enough_money then
					BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.LEVEL_UP)
				else
					VipWGCtrl.Instance:OpenTipNoGold()
				end
			elseif next_times_list.condition_type == 2 then
				RechargeWGCtrl.Instance:Recharge(next_times_list.condition_value, next_times_list.rmb_type,
					next_times_list.rmb_seq)
			end
		end

		TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
	end
end

--开启/关闭 特权
function BossNewPrivilegeView:OnClickSetOpenState()
	local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive() --是否激活过
	if not is_active then                                                  --激活
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossPrivilege.GodWarError)
		self:OnClickActivate()
		return
	end

	local is_times = BossPrivilegeWGData.Instance:GetIsPrivilegeTimes() --是否还有次数
	if is_times < 1 then
		BossPrivilegeWGCtrl.Instance:OpenBossPrivilegeUpgradeView()
		return
	end

	local cur_state = BossPrivilegeWGData.Instance:GetActivateState() --当前开启状态
	if cur_state > 0 then                                          --开启中
		BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.SET_STATUS,
			BOSS_DROP_PRIVILEGE_OPEN_TYPE.CLOSE)                   --关闭特权
	else
		BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.SET_STATUS,
			BOSS_DROP_PRIVILEGE_OPEN_TYPE.OPEN) --开启特权
	end

	BossPrivilegeWGCtrl.Instance:OutBossPrivilegeSceneCallBack()
end
