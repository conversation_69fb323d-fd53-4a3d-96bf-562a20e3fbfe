local MountAttrStoreItemEffectPos = {
    [1] = {x = -2, y = 164},
    [2] = {x = -2, y = 158},
    [3] = {x = -2, y = 162},
}

NewAppearanceAttrStoreView = NewAppearanceAttrStoreView or BaseClass(SafeBaseView)

function NewAppearanceAttrStoreView:__init()
    self:SetMaskBg(true)
    -- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -16), sizeDelta = Vector2(1202, 572)})
    self:AddViewResource(0, "uis/view/new_appearance_ui_prefab", "layout_mount_attr_store")
end

function NewAppearanceAttrStoreView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.NewAppearance.AttrStoneTitle

    if not self.mount_attr_store_list then
        self.mount_attr_store_list = {}
        for i = 0, 5 do
            self.mount_attr_store_list[i] = MountAttrStoreItemRender.New(self.node_list["mount_attr_store_list_item" .. i])
        end
    end

    if not self.mount_store_list then
        self.mount_store_list = AsyncListView.New(MountAttrStoreItemRender, self.node_list.mount_store_list)
        self.mount_store_list:SetStartZeroIndex(false)
        self.mount_store_list:SetSelectCallBack((BindTool.Bind(self.OnClickCellCallBack, self)))
    end

    self.select_index = -1
end

function NewAppearanceAttrStoreView:ReleaseCallBack()
    if self.mount_attr_store_list then
        for k, v in pairs(self.mount_attr_store_list) do
            v:DeleteMe()
        end

        self.mount_attr_store_list = nil
    end

    if self.mount_store_list then
        self.mount_store_list:DeleteMe()
        self.mount_store_list = nil
    end

    self.advanced_type = nil
    self.qc_type = nil
    self.select_index = nil
end

function NewAppearanceAttrStoreView:SetData(advanced_type, qc_type)
    self.advanced_type = advanced_type
    self.qc_type = qc_type
end

function NewAppearanceAttrStoreView:OnFlush()
    if not self.advanced_type or not self.qc_type then
        return
    end

    local sxd_data_list = {}
    if self.advanced_type >= 0 then
        sxd_data_list = NewAppearanceWGData.Instance:GetAdvancedSXDExtendlist(self.advanced_type)
    else
        sxd_data_list = NewAppearanceWGData.Instance:GetQiChongSXDExtendlist(self.qc_type)
    end

    local use_list = #sxd_data_list > 6
    if use_list then
        self.mount_store_list:SetDataList(sxd_data_list)
    else
        for i = 0, 5 do
            local data = sxd_data_list[i + 1] or {}
            local has_data = not IsEmptyTable(data)
            
            if has_data then
                self.mount_attr_store_list[i]:SetData(data)
            end
    
            self.node_list["mount_attr_store_list_item" .. i]:CustomSetActive(has_data)
        end
    end

    self.node_list.mount_attr_store_list:CustomSetActive(not use_list)
    self.node_list.mount_store_list:CustomSetActive(use_list)
end

function NewAppearanceAttrStoreView:OnClickCellCallBack(item)
    if nil == item or nil == item:GetData() then
        return
    end

    local index = item:GetIndex()

    if self.select_index < 0 then
        self.select_index = index
        return
    end

    self.select_index = index
    local data = item:GetData()
    NewAppearanceWGCtrl.Instance:OnClickAttributeStoneItem(data)
end

---------------------------------MountAttrStoreItemRender-------------------------------
MountAttrStoreItemRender = MountAttrStoreItemRender or BaseClass(BaseRender)

function MountAttrStoreItemRender:LoadCallBack()
    if not self.attr_list then
        self.attr_list = {}

        for i = 1, 6 do
            self.attr_list[i] = MountAttrStoreItemAttrRender.New(self.node_list["attr_" .. i])
        end
    end

	XUI.AddClickEventListener(self.view, BindTool.Bind1(self.OnClick, self))
end

function MountAttrStoreItemRender:__delete()
    if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end

        self.attr_list = nil
    end

    self:CancleTewwn()
end

function MountAttrStoreItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetRawImagesPNG("a3_sz_di_" .. self.data.cfg.bg)
    self.node_list.bg.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list.bg.raw_image:SetNativeSize()
    end)

    local img_res = self.data.cfg.show_img
    local item_bundle, item_asset = ResPath.GetNewAppearanceImage(img_res)
    self.node_list["icon"].image:LoadSprite(item_bundle, item_asset, function ()
        self.node_list["icon"].image:SetNativeSize()
    end)

    self.node_list["num"].text.text = self.data.used_num

    local is_max_level = self.data.used_num >= self.data.cfg.use_limit_num
    self.node_list.bom_content:CustomSetActive(self.data.is_remind and not is_max_level)
    self.node_list.store_red:CustomSetActive(self.data.is_remind and not is_max_level)
    self.node_list.max_level_flag:CustomSetActive(is_max_level)

    if self.data.is_remind and not is_max_level then
        self.node_list["add_num"].text.text = "+" .. self.data.had_num
    end

    local attr_data = self:GetAttrDataList()

    for i = 1, 6 do
        local data = attr_data[i] or {}
        local has_data = not IsEmptyTable(data)

        if has_data then
            self.attr_list[i]:SetData(data)
        end

        self.node_list["attr_" .. i]:CustomSetActive(has_data)
    end

    -- if self.data.cfg.effect then
    --     local effect_bundle, effect_asset = ResPath.GetEffectUi(self.data.cfg.effect)
    --     self.node_list.effect:ChangeAsset(effect_bundle, effect_asset)

    --     local effect_pos = MountAttrStoreItemEffectPos[self.data.cfg.bg]
    --     if not IsEmptyTable(effect_pos) then
    --         RectTransform.SetAnchoredPositionXY(self.node_list.effect.rect, effect_pos.x, effect_pos.y)
    --     end
    -- end

    self:CancleTewwn()
    self.arrow_tweener = self.node_list["icon"].gameObject.transform:DOAnchorPosY(166, 1)
    self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
    self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
end

function MountAttrStoreItemRender:GetAttrDataList()
    local attr_info = self.data.cfg
    local use_num = self.data.used_num
    if use_num <= 0 then
        use_num = 1 -- 未激活改成 默认显示一级属性 
    end
	local allow_value_zero = true
	local attr_list = {}

	for i = 1, 5 do
		local attr_id = attr_info["attr_id" .. i]
		local attr_value = attr_info["attr_value" .. i] * use_num

		if attr_id and attr_id > 0 and attr_value then
			if allow_value_zero or (not allow_value_zero and attr_value > 0) then
				local data = {}
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
				data["attr_name"] = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
				data["value_str"] = AttributeMgr.PerAttrValue(attr_str, attr_value)
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
				table.insert(attr_list, data)
			end
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    local special_name = self.data.cfg.attr_per_name
    local special_value = self.data.cfg.attr_per
    local special_add_value = self.data.cfg.attr_per * use_num
    local special_value_str = string.format("%d%%", special_add_value / 100)
    if special_name ~= "" and special_value ~= 0 then
        table.insert(attr_list, {attr_name = special_name, value_str =  special_value_str})
    end

    return attr_list
end

function MountAttrStoreItemRender:OnClick(is_on)
    NewAppearanceWGCtrl.Instance:OnClickAttributeStoneItem(self.data)
end

function MountAttrStoreItemRender:CancleTewwn()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end
end

------------------------------MountAttrStoreItemAttrRender----------------------------
MountAttrStoreItemAttrRender = MountAttrStoreItemAttrRender or BaseClass(BaseRender)

function MountAttrStoreItemAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text = self.data.value_str or ""
end