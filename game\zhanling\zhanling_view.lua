ZhanLingView = ZhanLingView or BaseClass(SafeBaseView)

function ZhanLingView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self:SetMaskBg(false)
	self.is_safe_area_adapter = true
	self.default_index = TabIndex.zl_main_reward

	self.remind_tab = {{RemindName.ZhanLing_Reward}, {RemindName.ZhanLing_Task}, {RemindName.ZhanLing_ExChange}}
	self.tab_sub = {}

	local bundle = "uis/view/zhanling_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(TabIndex.zl_main_reward, bundle, "layout_zhanling_main")
	self:AddViewResource(TabIndex.zl_task_panel, bundle, "layout_zhanling_task")
	self:AddViewResource(TabIndex.zl_shop_panel, bundle, "layout_zhanling_shop")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function ZhanLingView:__delete()
end

function ZhanLingView:OpenCallBack()
	ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.ExchangeInfo)
	ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.BuyExpInfo)
end

function ZhanLingView:CloseCallBack()
end

function ZhanLingView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_zl_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self.node_list.title_view_name.text.text = Language.ZhanLing.TitleName
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		-- local ver_path = "uis/view/zhanling_ui_prefab"
		-- local img_path = ResPath.GetZhanLingImg()
		-- local res_name = "zhanl_tab"

		-- self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.FlushTabVisible, self))
		-- self.tabbar:SetVerTabbarNameImgRes(img_path, res_name)
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:Init(Language.ZhanLing.NameTable, nil, ResPath.CommonBundleName, nil, self.remind_tab)
		-- FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.ZhanLingView, self.tabbar)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.old_round_day = 0

	local is_new_round = ZhanLingWGData.Instance:GetIsNewRound()
	if is_new_round then
		self:OpenNewRoundTips()
	end
end

function ZhanLingView:OpenNewRoundTips()
	if nil == self.new_round_alert then
		self.new_round_alert = Alert.New()
		self.new_round_alert:SetCheckBoxDefaultSelect(false)
	end

	self.new_round_alert:UseOne()
	self.new_round_alert:SetLableString(Language.ZhanLing.NewRoundTips)
	self.new_round_alert:SetOkFunc(function()
		ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.ReadNewRound)
	end)

	self.new_round_alert:SetCloseBeforeFunc(function()
		ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.ReadNewRound)
	end)

	self.new_round_alert:Open()
end

function ZhanLingView:ReleaseCallBack()
	self:CleanZhanLingCDTime()

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.new_round_alert then
		self.new_round_alert:DeleteMe()
		self.new_round_alert = nil
	end

	self.old_round_day = nil

	self:ReleaseMianRewardPanel()
	self:ReleaseShopPanel()
	self:ReleaseTaskPanel()
end

function ZhanLingView:LoadIndexCallBack(index)
	if index == TabIndex.zl_main_reward then
		self:InitMianRewardPanel()
	elseif index == TabIndex.zl_shop_panel then
		self:InitShopPanel()
	elseif index == TabIndex.zl_task_panel then
		self:InitTaskPanel()
	end
end

function ZhanLingView:ShowIndexCallBack(index)
	if index == TabIndex.zl_main_reward then
		self:SetJumpRewardflag(true)
	elseif index == TabIndex.zl_task_panel then
		self:DefaultSelectTaskToggle()
	elseif index == TabIndex.zl_shop_panel then
		ZhanLingWGData.Instance:SetExChangeOpenFlag()
	end
end

function ZhanLingView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.zl_main_reward then
				self:FlushMianRewardPanel()
			elseif index == TabIndex.zl_shop_panel then
				self:FlushShopPanel()
			elseif index == TabIndex.zl_task_panel then
				self:FlushTaskPanel()
			end
		elseif k == "flush_anim" then
			self:TaskFinishEffect()
		end
	end

	self:FlushZhanLingRestTime()
end

function ZhanLingView:OnClickBuyExpBtn()
	ZhanLingWGCtrl.Instance:OpenBuyLevelView()
end

function ZhanLingView:CleanZhanLingCDTime()
	if CountDownManager.Instance:HasCountDown("zhanling_round_time") then
		CountDownManager.Instance:RemoveCountDown("zhanling_round_time")
	end
end

function ZhanLingView:FlushZhanLingRestTime()
	local cur_round_day = ZhanLingWGData.Instance:GetZhanLingCurRoundDay()
	if self.old_round_day == cur_round_day then
		return
	end

	self.old_round_day = cur_round_day
	self:CleanZhanLingCDTime()

	local res_time = ZhanLingWGData.Instance:GetCurRoundRestTime()
	self:PanelFlushTime(TimeUtil.FormatSecondDHM2(res_time))
	if res_time > 0 then
		CountDownManager.Instance:AddCountDown("zhanling_round_time", BindTool.Bind(self.UpdateZhanLingRestTime, self), nil, nil, res_time, 1)
	end
end

function ZhanLingView:UpdateZhanLingRestTime(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self:PanelFlushTime(TimeUtil.FormatSecondDHM2(valid_time))
	else
		self:PanelFlushTime("00:00")
		self:CleanZhanLingCDTime()
	end
end

function ZhanLingView:PanelFlushTime(time_str)
	if self.node_list.m_rest_time then
		self.node_list.m_rest_time.text.text = time_str
	end

	-- if self.node_list.t_rest_time then
	-- 	self.node_list.t_rest_time.text.text = ToColorStr(time_str, COLOR3B.GREEN)
	-- 	--self.node_list.t_rest_time.text.text = string.format(Language.ZhanLing.ResidueTime, time_str)
	-- end

	if self.node_list.shop_rest_time then
		self.node_list.shop_rest_time.text.text = time_str
	end
end
