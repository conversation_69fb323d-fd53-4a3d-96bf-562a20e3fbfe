AuctionTipsWGData = AuctionTipsWGData or BaseClass()

function AuctionTipsWGData:__init()
	if AuctionTipsWGData.Instance ~= nil then
		ErrorLog("[AuctionTipsWGData] attempt to create singleton twice!")
		return
	end

	AuctionTipsWGData.Instance = self

	self:InitCfg()

end

function AuctionTipsWGData:__delete()
	AuctionTipsWGData.Instance = nil

end

function AuctionTipsWGData:GetIsFun(fun_name)
    return fun_name == FunName.market_country_auction
end

function AuctionTipsWGData:InitCfg()
	--拍卖配置
    self.new_auction_cfg = ConfigManager.Instance:GetAutoConfig("new_auction_auto")
    self.new_auction_item_cfg = ListToMapList(self.new_auction_cfg.auction_item, "type", "round","group")

    -- 这时候没有服务器时间 拿不了 只能第一次获取的时候拿
    -- self:SetPeriodTime()
end

function AuctionTipsWGData:SetCurRound(round)
    self.round = round
end

function AuctionTipsWGData:GetCurRound(round)
    return self.round or 0
end


-- 判断当前处于哪个轮次中
-- return 0,时间戳|非0，时间戳table
function AuctionTipsWGData:CheckInPeriodBuType(type)
    local is_open = FunOpen.Instance:GetFunIsOpened(FunName.market_country_auction)
    if not is_open then
        return 0, nil
    end
    if IsEmptyTable(self.period_time) or IsEmptyTable(self.period_time[type]) then
        self:SetPeriodTime()
    end
    local period_list = self.period_time[type]
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    local next_start_timestemp = 0
    local public_time = MarketWGData.Instance:GetPublickTime(AUCTION_TYPE.Country)
    for key, value in ipairs(period_list) do
        if server_time>= value.start_timestemp - public_time and server_time<=value.end_timestemp then
            return value.round, value
        end
        if server_time < value.start_timestemp - public_time and next_start_timestemp == 0 then
            next_start_timestemp = value.start_timestemp - public_time
        end
    end
    -- 返回下一场的开始时间
    return 0, next_start_timestemp
end

-- 设置轮次时间
function AuctionTipsWGData:SetPeriodTime()
    self.period_time = {}
    -- 每个类型会有好几轮，需要
    for type, round_data_list in pairs(self.new_auction_item_cfg) do
        self.period_time[type] = {}
        for key, round_cfg in pairs(round_data_list) do
            -- 把每一轮的时间取出来
            local period_timestemp = self:GetAuctionPeriodTimestemp(round_cfg)
            table.insert(self.period_time[type],period_timestemp)
        end
    end
end

-- 获取某一轮的开始结束时间戳
-- 同一轮中 第一场的开始时间和最后一场的结束时间
function AuctionTipsWGData:GetAuctionPeriodTimestemp(round_cfg)
    local data = {}
    data.start_timestemp = 0
    data.end_timestemp = 0
    -- 当前轮次的场次时间数据
    data.timestemp_list = {}
    for key, group_cfg in pairs(round_cfg) do
        local time_data = self:GetAuctionGroupTimestemp(group_cfg)
        if not IsEmptyTable(time_data) then
            data.start_timestemp = data.start_timestemp == 0 and time_data.start_timestemp or math.min(time_data.start_timestemp, data.start_timestemp)
            data.end_timestemp = data.end_timestemp == 0 and time_data.end_timestemp or math.max(time_data.end_timestemp, data.end_timestemp)
            data.round = time_data.round
            table.insert(data.timestemp_list,time_data)
        end
    end
    
    return data
end

-- 获取某一场次的开始结束时间戳 
-- 同场道具中 第一个拍卖的为开始时间 最后一个拍卖的结束时间
function AuctionTipsWGData:GetAuctionGroupTimestemp(group_cfg)
    local data = {}
    data.start_timestemp = 0
    data.end_timestemp = 0
    for key, value in pairs(group_cfg) do
        local time_data = self:GetAuctionCfgTimestemp(value)
        if not IsEmptyTable(time_data) then
            data.start_timestemp = data.start_timestemp == 0 and time_data.start_timestemp or math.min(time_data.start_timestemp, data.start_timestemp)
            data.end_timestemp = data.end_timestemp == 0 and time_data.end_timestemp or math.max(time_data.end_timestemp, data.end_timestemp)
        end
    end
    data.group = group_cfg[1].group
    data.round = group_cfg[1].round
    return data
end

-- 获取某一物品的开始结束时间戳
function AuctionTipsWGData:GetAuctionCfgTimestemp(cfg)
    local data = {}
    if cfg.start_time and cfg.end_time then
        data.start_timestemp = TimeUtil.FormatCfgTimestamp(cfg.start_time)
        data.end_timestemp = TimeUtil.FormatCfgTimestamp(cfg.end_time)

        if data.end_timestemp < data.start_timestemp then
            data.end_timestemp = data.end_timestemp + 86400
        end
    end
    return data
end

-- 获取所有轮次
function AuctionTipsWGData:GetRoundCfg(type, round)
    if self.new_auction_item_cfg[type] and self.new_auction_item_cfg[type][round] then
        return self.new_auction_item_cfg[type][round]
    end
    return {}
end

-- 获取某一场次的数据
function AuctionTipsWGData:GetGroupCfg(type, round, group)
    if self.new_auction_item_cfg[type] and self.new_auction_item_cfg[type][round] and self.new_auction_item_cfg[type][round][group] then
        return self.new_auction_item_cfg[type][round][group]
    end
    return {}
end



