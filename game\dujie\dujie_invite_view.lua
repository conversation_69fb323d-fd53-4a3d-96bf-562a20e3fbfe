DujieInviteView = DujieInviteView or BaseClass(SafeBaseView)
function DujieInviteView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(556, 366)})
    self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_invite_view")
end

function DujieInviteView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind(self.OnClickBtnCancel, self))
	XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind(self.OnClickBtnOK, self))
	XUI.AddClickEventListener(self.node_list["btn_input_min"], BindTool.Bind(self.OnClickSetAmount, self, true))
	XUI.AddClickEventListener(self.node_list["btn_input_max"], BindTool.Bind(self.OnClickSetAmount, self, false))

    self.node_list.title_view_name.text.text = Language.Dujie.IviteTitle
end

function DujieInviteView:ShowIndexCallBack()
    self.node_list.checkbox_all.toggle.isOn = true
    self.node_list.checkbox_friend.toggle.isOn = true
    self.node_list.checkbox_guild.toggle.isOn = true
end

function DujieInviteView:ReleaseCallBack()

end

function DujieInviteView:CloseCallBack()
    self:ClearTimer()
end

function DujieInviteView:OnFlush()
    self.node_list.input_field_min_level.input_field.text = 100
    self.node_list.input_field_max_level.input_field.text = 9999

    local add_strength = DujieWGData.Instance:GetSinglePlayerAddStrength()
    self.node_list.text_dujie_tips.text.text = string.format(Language.Dujie.AddStrengthTips, add_strength)

    self:ClearTimer()
    -- Language.Common.BtnOK

    local can_invite, cd_time = self:GetInviteCD()
    if can_invite then
        self:SetBtnOKText()
    else
        XUI.SetGraphicGrey(self.node_list["btn_ok"], true)
        local cd_text = string.format("%s(%s)",Language.Common.BtnOK,cd_time)
        self.node_list.btn_text_ok.text.text = ToColorStr(cd_text, COLOR3B.GRAY)
        self.node_list.btn_text_ok.text.characterSpacing = 0

        self.timer_inview = CountDown.Instance:AddCountDown(cd_time, 1,
            -- 回调方法
            function(elapse_time, total_time)
                local can_invite, cd_time = self:GetInviteCD()
                if cd_time == 0 then
                    self:SetBtnOKText()
                else
                    local cd_text = string.format("%s(%s)",Language.Common.BtnOK,cd_time)
                    self.node_list.btn_text_ok.text.text = ToColorStr(cd_text, COLOR3B.GRAY)
                end

            end,
            -- 倒计时完成回调方法
            function()
                self:SetBtnOKText()
            end
        )
    end
end

function DujieInviteView:SetBtnOKText()
    XUI.SetGraphicGrey(self.node_list["btn_ok"], false)
    self.node_list.btn_text_ok.text.text = Language.Common.BtnOK
    self.node_list.btn_text_ok.text.characterSpacing = 60
end

-- 清除倒计时器1
function DujieInviteView:ClearTimer()
    if self.timer_inview and CountDown.Instance:HasCountDown(self.timer_inview) then
        CountDown.Instance:RemoveCountDown(self.timer_inview)
        self.timer_inview = nil
    end
end


function DujieInviteView:OnClickBtnCancel()
    self:Close()
end

function DujieInviteView:GetInviteCD()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local last_invite_time = DujieWGData.Instance:GetLastInviteTime()
    local invate_cd = DujieWGData.Instance:GetOtherCfg("invite_cd")
    if last_invite_time ~= 0 and last_invite_time + invate_cd > server_time then
        return false, math.ceil(last_invite_time + invate_cd - server_time)
    end
    return true, 0
end

function DujieInviteView:OnClickBtnOK()
    local can_invite, cd_time = self:GetInviteCD()
    if not can_invite then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Dujie.InviteCd, cd_time))
        return 
    end

    local invite_flags = {}
    invite_flags[DUJIE_INVITE_TYPE.INVITE_TYPE_ALL] = self.node_list.checkbox_all.toggle.isOn and 1 or 0
    invite_flags[DUJIE_INVITE_TYPE.INVITE_TYPE_FRIEND] = self.node_list.checkbox_friend.toggle.isOn and 1 or 0
    invite_flags[DUJIE_INVITE_TYPE.INVITE_TYPE_GUILD] = self.node_list.checkbox_guild.toggle.isOn and 1 or 0

    local new_flag = bit:b2d_end(invite_flags)
    --print_error("new_flag:",new_flag)
    -- 渡劫基础信息


    DujieWGCtrl.Instance:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_VIEW_INVITE, new_flag, self.node_list.input_field_min_level.input_field.text, self.node_list.input_field_max_level.input_field.text)
    SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.InviteSuccess)

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    DujieWGData.Instance:SetLastInviteTime(server_time)

    self:Close()
end

-- 点击设置数量
function DujieInviteView:OnClickSetAmount(is_min)

	local function callback(input_num)
        if is_min then
            self.node_list.input_field_min_level.input_field.text = input_num
        else
            self.node_list.input_field_max_level.input_field.text = input_num

        end

	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
    if is_min then
        num_keypad:SetNum(100)
    else
        num_keypad:SetNum(9999)
    end
	

    num_keypad:SetMaxValue(9999)
    num_keypad:SetMinValue(40)

	num_keypad:SetOkCallBack(callback)
end

