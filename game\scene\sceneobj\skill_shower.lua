SkillShower = SkillShower or BaseClass(Role)

SkillShower.ShowerType = {
	TianShen = 1,
	Monster = 2
}

function SkillShower:__init(vo)
	self.obj_type = SceneObjType.SkillShower
	self.draw_obj:SetObjType(self.obj_type)
    self.shield_obj_type = ShieldObjType.SkillShower
    self.draw_obj:SetCurDetailLevel(SceneObjDetailLevel.High)

    self.action_is_playing = false
    
    if SceneObjLODManager.Instance then
        SceneObjLODManager.Instance:Remove(self)
    end

    self:InitSummonInfo()
    self:InitInfo()
	Runner.Instance:AddRunObj(self, 8)
end

function SkillShower:__delete()
    self.jb_ts_attack_end_callback = nil
    self.ts_attack_hit_callback = nil
    self.beast_create_callback = nil
    self.shuangsheng_create_callback = nil
    self.action_is_playing = false
    self:DeleteAllBeast()
    self:RemoveShuangSheng()
	Runner.Instance:RemoveRunObj(self)
end

function SkillShower:InitSummonInfo()
    self.is_monster_obj = false
    self.monster_res_id = 0
    self.monster_res_type = 0
    if self.vo.monster_id and self.vo.monster_id > 0 then
        self.is_monster_obj = true
        local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.monster_id]
        if nil ~= cfg then
            self.monster_res_id = cfg.resid
            self.monster_res_type = cfg.boss_type2

            local transform = self.draw_obj:GetRoot().transform
            transform.localScale = Vector3(cfg.scale, cfg.scale, cfg.scale)
        end
    end
end

function SkillShower:SetIsJbTs(is_jb_ts)
    self.is_jb_ts = is_jb_ts
end

function SkillShower:InitInfo()--effect_rot, effect_scale, target_effect_scale)
	self.actor_trigger = ActorTrigger.New(self.obj_type)
	-- self.actor_trigger:SetEffectTriggerCustomScale(Vector3(effect_scale, effect_scale, effect_scale))
    self.actor_trigger:SetTargetEffectTriggerCustomScale(Vector3(0.1, 0.1, 0.1))
	-- self.actor_trigger:SetEffectTriggerCustomRotation(Quaternion.Euler(effect_rot.x, effect_rot.y, effect_rot.z))
    self.actor_trigger:DeleteEffectHandle()
    self.actor_trigger:DeleteCameraShakeHandle()
	self.actor_ctrl = ActorWGCtrl.New(self.actor_trigger)

    if self:IsTianShenAppearance() then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Tianshen", self.special_res_id))

    elseif self:IsGundam() then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Gundam", self.gundam_weapon_id))

    elseif self:IsRidingFightMount() then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("zuoqi", self.vo.mount_appeid))
    elseif self.is_monster_obj then
        if self:IsTianShenMonsterRes() then
            self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Tianshen", self.monster_res_id))
        else
            self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Monster", self.monster_res_id))
        end
    else
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetRoleAutoPrefabConfig(self.vo.sex, self:GetProf()))
    end
end

function SkillShower:SetTargetEffectTriggerScale(target_effect_scale)
    if self.actor_trigger then
        self.actor_trigger:SetTargetEffectTriggerCustomScale(Vector3(target_effect_scale, target_effect_scale, target_effect_scale))
    end
end

function SkillShower:SetEffectTriggerScale(effect_scale)
    if self.actor_trigger then
        self.actor_trigger:SetEffectTriggerCustomScale(Vector3(effect_scale, effect_scale, effect_scale))
    end
end

function SkillShower:SetEffectTriggerRotation(effect_rot)
    if self.actor_trigger then
        self.actor_trigger:SetEffectTriggerCustomRotation(Quaternion.Euler(effect_rot.x, effect_rot.y, effect_rot.z))
    end
end

function SkillShower:GetQualityOffsetLevel()
    return 1
end

function SkillShower:InitPartQualityRules()
end

function SkillShower:UpdateAppearance()
    self:InitSummonInfo()
    if self:IsTianShenAppearance() then         -- 天神
        self.special_res_id = self.vo.appearance_param
        self:UpdateTianShenShowerModel()
    
    elseif self:IsGundam() then
        -- special_res_id 索引从0开始，兼容旧逻辑
        local cfg = MechaWGData.Instance:GetPartCfgBySeq(self.vo.appearance_param_extend[MECHA_PART_TYPE.BODY])
        if cfg then
            local gundam_seq = cfg.mechan_seq + 1
            self.special_res_id = gundam_seq * 100000 + cfg.res_id
        end

        cfg = MechaWGData.Instance:GetPartCfgBySeq(self.vo.appearance_param_extend[MECHA_PART_TYPE.WEAPON])
        if cfg then
            local gundam_seq = cfg.mechan_seq + 1
            self.gundam_weapon_id = gundam_seq * 100000 + cfg.res_id + MECHA_PART_TYPE.WEAPON * 1000
        end

        self:UpdateGundamModel()
    elseif self.is_monster_obj then
        if self:IsTianShenMonsterRes() then
            self:UpdateTianShenShowerModel()
        else
            self:UpdateMonsterModel()
        end
    else    -- 玩家角色
        self:UpdateRoleShowerModel()
    end
end

function SkillShower:LoadRideBack()
    if self:IsRiding() then
        self:CrossAction(SceneObjPart.Main, self:SetRidingActionIdelParam())
    end
end

function SkillShower:UpdateRoleShowerModel()
    local body_res, face_res, hair_res = self:GetModelPartRes()
    local role_bundle, role_name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.vo.sex, self:GetProf()))
    self:ClearTianShenSpecialEff()
    local extra_model_data = {
        role_body_res = body_res,
        role_face_res = face_res,
        role_hair_res = hair_res,
    }

    self:ChangeMainPartModel(role_bundle, role_name, function()
    end, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE, DRAW_MODEL_TYPE.ROLE, extra_model_data)
    self:EquipDataChangeListen()

    self:ChangeFaZhen()

    self:UpdateSkillHaloResId()
    self:ChangeSkillHalo()

    -- self:UpdateBaoJu()
    self:UpdateWing()
    self:UpdateJianZhen()
    self:UpdateHalo()

    if self.beast_create_callback then
        self:ChangeBeast()
    end

    local load_ride_back = BindTool.Bind(self.LoadRideBack, self)
    self:UpdateMount(load_ride_back)
    if self.wuhun_create_callback then
        self:ChangeWuHun()
    end

    if self.shuangsheng_create_callback then
        self:ChangeShuangSheng()
    end
end

-- 法阵
function SkillShower:ChangeFaZhen()
    local res_id = self.vo.appearance and self.vo.appearance.fazhen_id
    self.fazhen_res_id = res_id
    if res_id and res_id > -1 then
        self:ChangeModel(SceneObjPart.FaZhen, ResPath.GetSkillFaZhenModel(self.fazhen_res_id))
    else
        self:RemoveModel(SceneObjPart.FaZhen)
    end
end

-- 技能光环
function SkillShower:ChangeSkillHalo()
    local is_show = not self.is_fighting
    if self.skill_halo_res_id ~= nil and self.skill_halo_res_id ~= 0 and is_show then
        self:ChangeModel(SceneObjPart.SkillHalo, ResPath.GetSkillHaloModel(self.skill_halo_res_id))
    else
        self:RemoveModel(SceneObjPart.SkillHalo)
    end
end

-- 翅膀
function SkillShower:UpdateWing()
    if not self.vo then return end
    if 0 ~= self.vo.wing_appeid then
        self:ChangeModel(SceneObjPart.Wing, ResPath.GetWingModel(self.vo.wing_appeid))
    else
        self:RemoveModel(SceneObjPart.Wing)
    end
end

-- 剑阵
function SkillShower:UpdateJianZhen()
    if not self.vo then return end
    if 0 ~= self.vo.jianzhen_appeid then
        self:ChangeModel(SceneObjPart.Jianling, ResPath.GetJianZhenModel(self.vo.jianzhen_appeid))
    else
        self:RemoveModel(SceneObjPart.Jianling)
    end
end

-- 光环
function SkillShower:UpdateHalo()
    if not self.vo then return end
    if self.vo.appearance ~= nil and self.vo.appearance.fashion_guanghuan > 0 then
        self:ChangeModel(SceneObjPart.Halo, ResPath.GetHaloModel(self.vo.appearance.fashion_guanghuan))
    else
        self:RemoveModel(SceneObjPart.Halo)
    end
end

-- 法宝
function SkillShower:UpdateBaoJu()
    if not self.vo then return end
    if self.vo.fabao_appeid > 0 then
        self:ChangeModel(SceneObjPart.BaoJu, ResPath.GetFaBaoModel(self.vo.fabao_appeid))
    else
        self:RemoveModel(SceneObjPart.BaoJu)
    end
end


-- 天神
function SkillShower:UpdateTianShenShowerModel()
    local res_id = self.special_res_id
    if self.is_monster_obj and self:IsTianShenMonsterRes() then
        res_id = self.monster_res_id
    end

	local bundle, name = nil, nil
    bundle, name = ResPath.GetBianShenModel(res_id)
    self:ChangeMainPartModel(bundle, name, nil, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN)
    self:EquipDataChangeListen()
end

function SkillShower:InitAppearance()

end

function SkillShower:UpdateMonsterModel()
	local bundle, asset = self:GetMonsterBundleAsset()
    local sync_anim_type = self:IsTianShenMonsterRes() and SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN or nil
    self:ChangeObjWaitSyncAnimType(sync_anim_type)
	self:ChangeModel(SceneObjPart.Main, bundle, asset)
end

-- 更新武器模型
function SkillShower:EquipDataChangeListen()
    local vo = self.vo
    if self:IsRiding() then
        return
    end

    local can_use_weapon_attach = false
    self.draw_obj:SetIsCanWeaponPointAttach(can_use_weapon_attach)
    local weapon_res_id = 0
    local res_func = ResPath.GetWeaponModelRes

    -- if need_weapon then
        if self:IsTianShenAppearance() then -- 天神变身 武器
            res_func = ResPath.GetTianShenShenQiPath
            if 0 == vo.tianshenshenqi_appeid then
                local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.vo.appearance_param, false)
                local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
                local cur_huanhua_id = TianShenWGData.Instance:GetWaiGuanHuanHua(shenqi_cfg and shenqi_cfg.index)
                if cur_huanhua_id == -1 then
                    weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
                else
                    weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id1 or weapon_res_id
                end
                weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
            elseif -1 == vo.tianshenshenqi_appeid then
                local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.vo.appearance_param, false)
                local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
                weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
            else
                local waiguan_cfg = TianShenWGData.Instance:GetWaiGuanCfg(vo.tianshenshenqi_appeid)
                weapon_res_id = waiguan_cfg and waiguan_cfg.facade_res_id or weapon_res_id
            end
        elseif self:IsTianShenMonsterRes() then
            res_func = ResPath.GetTianShenShenQiPath
            local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.monster_res_id, false)
            local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
            local cur_huanhua_id = TianShenWGData.Instance:GetWaiGuanHuanHua(shenqi_cfg and shenqi_cfg.index)
            if cur_huanhua_id == -1 then
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
            else
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id1 or weapon_res_id
            end
            weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
        elseif 0 ~= vo.appearance.fashion_wuqi or 0 ~= vo.shenwu_appeid then
            weapon_res_id = RoleWGData.GetFashionWeaponId(vo.sex, vo.prof, 0 ~= vo.appearance.fashion_wuqi and vo.appearance.fashion_wuqi or vo.shenwu_appeid)
        else
            weapon_res_id = RoleWGData.GetJobWeaponId(vo.sex, vo.prof)
        end
    -- end

    self.weapon_res_id = weapon_res_id
    if 0 ~= weapon_res_id then
        local bundle, asset = res_func(weapon_res_id)
        self:ChangeModel(SceneObjPart.Weapon, bundle, asset)
    else
        self:RemoveModel(SceneObjPart.Weapon)
    end
end

function SkillShower:EnterStateStand()
    if self.draw_obj == nil then
        return
    end

    -- local part = self.draw_obj:GetPart(SceneObjPart.Main)
    -- part:SetBool("fight", false)
    -- if self:IsWeaponOwnAnim() then
    --     local weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)
    --     weapon_part:SetBool("fight", false)
    -- end

    Role.EnterStateStand(self)

    if self.shaungsheng_tianshen_obj ~= nil then
        Role.EnterStateStand(self.shaungsheng_tianshen_obj)
    end
end

function SkillShower:EnterStateAttack(anim_name)
    -- print_error("---SkillShower EnterStateAttack---", anim_name)
    self:ClearActionData()

    -- 秘笈技能只在技能展示时有动作
    local esoterica_skill_cfg = CultivationWGData.Instance:GetEsotericaCfgBySkillId(self.attack_skill_id)
    if esoterica_skill_cfg then
        anim_name = esoterica_skill_cfg.skill_show_anim
    else
        anim_name = SkillWGData.GetSkillActionStr(self.obj_type, self.attack_skill_id, self.attack_index)
    end

    if anim_name and anim_name ~= "" then
        self.action_time_record = self:GetActionTimeRecord(anim_name)
    end

    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    local part_obj = part:GetObj()
    if part_obj == nil or IsNil(part_obj.gameObject) then
        return
    end

    -- if not anim_name then
    --     anim_name = "combo1_1"
    --     self.action_time_record = self:GetActionTimeRecord(anim_name)
    -- end

    if not anim_name or anim_name == SceneObjAnimator.Atk0 then
        anim_name = SceneObjAnimator.Idle
    end

    self.anim_name = anim_name
    if self:IsRidingFightMount() then
        self:CrossAction(SceneObjPart.Mount, anim_name, false)
    else
        self:CrossAction(SceneObjPart.Main, anim_name, false)
    end

    -- 无技能动作，需要播放音效
    local sound_bundle, sound_asset
    local is_tianshen_heji_skill = SkillWGData.Instance:GetIsTianShenHeJiSkill(self.attack_skill_id)
    if is_tianshen_heji_skill then
        local cfg = TianShenWGData.Instance:GetHejiCfgBySkillId(self.attack_skill_id)
        if cfg then
            sound_bundle = cfg.sound_bundle
            sound_asset = cfg.sound_asset
        end
    end

    if sound_bundle and sound_asset then
        local shower_draw_obj = self:GetDrawObj()
        if shower_draw_obj ~= nil and not IsNil(shower_draw_obj:GetTransfrom()) then
            local play_pos = shower_draw_obj:GetTransfrom().position
            AudioManager.PlayAndForget(sound_bundle, sound_asset, nil, play_pos.transform)
        end
    end

    self:OnAnimatorBegin()
end

function SkillShower:AttackActionEndHandle()
	if self.action_time_record ~= nil and self.action_time_record.has_back then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        local part_obj = part:GetObj()
        if part_obj == nil or IsNil(part_obj.gameObject) then
            return
        end
        
        self.play_action_back_fun = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnPlayActionBackEnd, self), self.action_time_record.back_time)
        if self:IsRidingFightMount() then
            self:CrossAction(SceneObjPart.Mount, self.anim_name.."_back")
        else
            self:CrossAction(SceneObjPart.Main, self.anim_name.."_back")

            if self.shaungsheng_tianshen_obj ~= nil then
                self.shaungsheng_tianshen_obj:CrossAction(SceneObjPart.Main, self.anim_name.."_back")
            end
        end
    else
        self:ChangeToCommonState()
    end

    if self.jb_ts_attack_end_callback then
        self.jb_ts_attack_end_callback()
    end
end

function SkillShower:CurIdleAni()
    return SceneObjAnimator.Idle
end

function SkillShower:EnterFightState()
    if self.draw_obj == nil then return end

    -- if self.draw_obj then
    --     local part = self.draw_obj:GetPart(SceneObjPart.Main)
    --     part:SetBool("fight", true)
    --     if self:IsWeaponOwnAnim() then
    --         local weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)
    --         weapon_part:SetBool("fight", true)
    --     end
    -- end

    self.is_fighting = true
    self:ChangeSkillHalo()
end

function SkillShower:OnModelLoaded(part, obj, obj_class)
	Role.OnModelLoaded(self, part, obj, obj_class)
	if part == SceneObjPart.Main then
        if self.load_callback ~= nil then
            self.load_callback()
        end
	end
end

function SkillShower:DoAttackForNoTarget(...)
    self:ResetActionPlayingFlag()
	Role.DoAttackForNoTarget(self, ...)
end

function SkillShower:SetModelLoadCallback(load_callback)
	self.load_callback = load_callback
end

function SkillShower:SetJBTianShenAttackEndCallback(callback)
    self.jb_ts_attack_end_callback = callback
end

function SkillShower:SetAttackHitCallback(callback)
    self.ts_attack_hit_callback = callback
end

function SkillShower:SetEnemyPos(enemy_target)
	self.enemy_target = enemy_target
end

function SkillShower:CharacterAnimatorEvent(param, state_info, anim_name, is_not_skill_shower)
    local actor_trigger = self:GetActorTrigger()
    if actor_trigger ~= nil then
        local source = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
        local target = self.enemy_target

        if self.attack_skill_id ~= nil then
            local is_hit = string.find(anim_name, "/hit")
            local cfg = SkillWGData.Instance:GetBuffLayerCfg(self.attack_skill_id)
            if cfg ~= nil and cfg.effect_buff_type == SKILL_BUFF_SHOW.SHAKE then
                state_info = state_info or {}
                state_info.is_ignore_shake = true
            end
            
            self:PlayAfterSkillEfffect(anim_name)
        end

        local pos = nil
        if self.attack_target_pos_x ~= nil and self.attack_target_pos_x ~= 0 and self.attack_target_pos_y ~= nil and self.attack_target_pos_y ~= 0 then
        	if self.attack_target_pos_x ~= self.logic_pos.x and self.attack_target_pos_y ~= self.logic_pos.x then
        		local t_x, t_y = GameMapHelper.LogicToWorld(self.attack_target_pos_x, self.attack_target_pos_y)
        		pos = Vector3(t_x, 0, t_y)
        	end
        end

        if pos ~= nil then
        	if state_info ~= nil then
        		if state_info.dir_pos == nil then
        			state_info.dir_pos = pos
        		end
        	else
        		state_info = {dir_pos = pos}
        	end
        end

        state_info = state_info or {}
        state_info.is_skill_shower = not is_not_skill_shower
        -- 怪物蓄力多范围技能
        local begin = string.find(anim_name, "magic[1-4]_3/begin")
        if anim_name ~= nil and begin ~= nil and self.attack_skill_id ~= nil and self.attack_skill_id ~= nil and self:IsMonster() and self.attack_sub_zone ~= nil and next(self.attack_sub_zone) ~= nil then
            local skill_cfg = SkillWGData.GetMonsterSkillConfig(self.attack_skill_id)
            if skill_cfg ~= nil and skill_cfg.RandZoneCount ~= "" and skill_cfg.RandZoneCount > 0 then
                local pos = self:GetLuaPosition()
                local pos_table = {}
                local info = {pos_table = pos_table}
                for k,v in pairs(self.attack_sub_zone) do
                    local real_x, real_y = GameMapHelper.LogicToWorld(v.pos_x, v.pos_y)
                    local start_pos = Vector3(real_x, pos.y + 0.1, real_y)
                    table.insert(pos_table, start_pos)
                end

                actor_trigger:OnAnimatorEvent(param, info, source, nil, anim_name)
            else
                actor_trigger:OnAnimatorEvent(param, state_info, source, target, anim_name)
            end
        else
            actor_trigger:OnAnimatorEvent(param, state_info, source, target, anim_name)
        end
    end
end

function SkillShower:ClearActionTimeRecord()
	self.action_time_record = nil
end

function SkillShower:IsSkillShower()
    return true
end

function SkillShower:OnEnterScene()
    Character.OnEnterScene(self)
    Scene.Instance:SetSkyBoxActive(false)
end

function SkillShower:OnAnimatorBegin()
    Role.OnAnimatorBegin(self)
    self.action_is_playing = true
end

function SkillShower:OnAnimatorHit()
    Role.OnAnimatorHit(self)
    if self.ts_attack_hit_callback then
        self.ts_attack_hit_callback(self.attack_skill_id, self.is_jb_ts)
    end
end

function SkillShower:OnPlayActionBackEnd()
    Role.OnPlayActionBackEnd(self)
    self.action_is_playing = false
end

function SkillShower:IsActionPlaying()
    return self.action_is_playing
end

function SkillShower:SetActionPlaying(action_is_playing)
    self.action_is_playing = action_is_playing
end

function SkillShower:ResetActionPlayingFlag()
    self.action_is_playing = false
end

function SkillShower:CheckInvisible(reason)
    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    local weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)
    if reason == BUFF_FLUSH_REASON.ADD then
        if part ~= nil then
            part:PlayFade(true, 0.25)
        end

        if weapon_part ~= nil then
            weapon_part:PlayFade(true, 0.25)
        end
    else
        if part ~= nil then
            part:PlayFade(false)
        end

        if weapon_part ~= nil then
            weapon_part:PlayFade(false)
        end
    end
end



-- [[武魂
function SkillShower:SetWuHunCreateCallBack(callback)
    self.wuhun_create_callback = callback
    self:ChangeWuHun()
end

function SkillShower:CreateWuHunObj()
    local wuhun_obj = nil
    local role_vo = self:GetVo()
    local wuhun_vo = GameVoManager.Instance:CreateVo(WuHunObjVo)
    wuhun_vo.role_vo = role_vo
    wuhun_vo.wuhun_id = role_vo.wuhun_id
    wuhun_vo.wuhun_lv = role_vo.wuhun_lv
    wuhun_vo.owner_obj = self
    wuhun_vo.hp = 100
    wuhun_vo.max_hp = 100
    wuhun_obj = WuHunObj.New(wuhun_vo)

    return wuhun_obj
end

function SkillShower:ChangeWuHun()
    local is_mount = self:IsRiding()
    if self.vo and self.vo.wuhun_id and self.vo.wuhun_id > 0 and (not is_mount) then
        if not self.wuhun_obj then
            self.wuhun_obj = self:CreateWuHunObj()
            if self.wuhun_create_callback then
                self.wuhun_create_callback()
            end
        else
            self.wuhun_obj:ChangeWuHunImageId(self.vo.wuhun_id, self.vo.wuhun_lv)
            self.wuhun_obj:AttachToRoleMainPart()
        end
    else
        self:RemoveWuHun()
    end
end

--武魂
function SkillShower:RemoveWuHun()
    if self.wuhun_obj then
        self.wuhun_obj:DeleteMe()
        self.wuhun_obj = nil
    end
end

--武魂 end]]

-- [[驭兽
-- 灵兽变更
function SkillShower:SetBeastCreateCallBack(callback)
    self.beast_create_callback = callback
    self:ChangeBeast()
end

function SkillShower:ChangeBeast()
    if self.vo.beast_ids and #self.vo.beast_ids > 0 then
        local beast_obj = self:GetBeastObjList()
        if beast_obj then
            if not beast_obj or beast_obj:IsDeleted() then
                self:SetBeast(beast_obj, 1)
                if self.beast_create_callback then
                    self.beast_create_callback(beast_obj, 1)
                end
            else
                self:SetBeast(beast_obj, 1)
                beast_obj:TryFlushAppearance(self.vo.beast_ids, 1)
            end
        end
    else
        self:DeleteAllBeast()
    end
end

function SkillShower:CreateBeastObjByRole(role, battle_index, beast_id)
    local beast_obj = nil
    local role_vo = role:GetVo()
    local beast_vo = GameVoManager.Instance:CreateVo(BeastObjVo)
    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.beast_id)
    beast_vo.role_vo = role_vo
    beast_vo.obj_name = aim_cfg and aim_cfg.beast_name or ""
    beast_vo.battle_index = battle_index
    beast_vo.beast_id = beast_id
    beast_vo.beast_skin = role_vo.beast_skin
    beast_vo.pos_x, beast_vo.pos_y = role:GetLogicPos()
    beast_vo.pos_x = beast_vo.pos_x + 2
    beast_vo.owner_objid = role_vo.obj_id
    beast_vo.owner_obj_name = role_vo.name
    beast_vo.move_speed = role_vo.move_speed
    beast_vo.hp = 100
    beast_vo.max_hp = 100
    beast_vo.owner_obj = role
    beast_obj = self:CreateBeast(beast_vo)
    return beast_obj
end

function SkillShower:CreateBeast(vo)
    local obj = BeastObj.New(vo)
    return obj
end

function SkillShower:DeleteAllBeast()
    local beast_obj  = self:GetBeastObjList()
    if beast_obj and beast_obj:GetVo() then
        beast_obj:DeleteMe()
        beast_obj = nil
    end
    
    self.beast_obj = nil
end
--驭兽 end]]


-- [[双生天神
function SkillShower:SetShuangShengCreateCallBack(callback)
    self.shuangsheng_create_callback = callback
    self:ChangeShuangSheng()
end

function SkillShower:CreateShuangShengObj()
    local shaungsheng_tianshen_obj = nil
    local role_vo = self:GetVo()
    local shuangsheng_vo = GameVoManager.Instance:CreateVo(ShuangShengTianShenObjVo)
    shuangsheng_vo.role_vo = role_vo
    shuangsheng_vo.shaungsheng_tianshen_aura_id = role_vo.shaungsheng_tianshen_aura_id
    shuangsheng_vo.owner_obj = self
    shuangsheng_vo.hp = 100
    shuangsheng_vo.max_hp = 100
    shaungsheng_tianshen_obj = ShuangShengTianShenObj.New(shuangsheng_vo)

    return shaungsheng_tianshen_obj
end

function SkillShower:ChangeShuangSheng()
    if self.vo and self.vo.shaungsheng_tianshen_aura_id and self.vo.shaungsheng_tianshen_aura_id > -1 then
        if not self.shaungsheng_tianshen_obj then
            self.shaungsheng_tianshen_obj = self:CreateShuangShengObj()
            if self.shuangsheng_create_callback then
                self.shuangsheng_create_callback(self.shaungsheng_tianshen_obj)
            end
        else
            self.shaungsheng_tianshen_obj:ChangeShuangShengImageId(self.vo.shaungsheng_tianshen_aura_id)
            self.shaungsheng_tianshen_obj:AttachToRoleMainPart()
        end
    else
        self:RemoveShuangSheng()
    end
end

--双生天神
function SkillShower:RemoveShuangSheng()
    if self.shaungsheng_tianshen_obj then
        self.shaungsheng_tianshen_obj:DeleteMe()
        self.shaungsheng_tianshen_obj = nil
    end
end

--双生天神 end]]


function SkillShower:GetMonsterBundleAsset()
	local bundle, asset
	if self:IsTianShenMonsterRes() then
		bundle, asset = ResPath.GetBianShenModel(self.monster_res_id)
	else
		bundle, asset = ResPath.GetMonsterModel(self.monster_res_id)
	end

	return bundle, asset
end

function SkillShower:IsTianShenMonsterRes()
    return self.monster_res_type == ClientBossType.TSBoss or self.monster_res_type == ClientBossType.WeaponAniTSBoss
end

-- 获取动作时间
function SkillShower:GetActionTimeRecord(anim_name)
    if self:IsTianShenAppearance() then
        return (TianShenBossActionConfig[self.special_res_id] or {})[anim_name]

    elseif self:IsGundam() then
        return GundamActionConfig[self.special_res_id][anim_name]

    elseif self:IsRidingFightMount() then
        local appid = self:GetCurRidingResId()
        return (ZuoqiActionConfig[appid] or {})[anim_name]

    elseif self.is_monster_obj then
        if self:IsTianShenMonsterRes() then
            return (TianShenBossActionConfig[self.monster_res_id] or {})[anim_name]
        else
            return (MonsterActionConfig[self.monster_res_id] or {})[anim_name]
        end
    end

    return ((RoleActionConfig[self.vo.sex] or {})[self:GetProf()] or {})[anim_name]
end

function SkillShower:IsWeaponOwnAnim()
    if self:IsDeleted() then
        return false
    end

    if self.is_monster_obj and not self:IsTianShenMonsterRes() then
        return false
    end

    return true
end