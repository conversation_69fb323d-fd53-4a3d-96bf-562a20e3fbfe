------------------------------------------------------
--采集进度条
------------------------------------------------------
GatherBar = GatherBar or BaseClass(SafeBaseView)

local VECTOR3_ZERO = Vector3(0, 0, 0)
local VECTOR3_360 = Vector3(0, 0, -360)
local NORMAL_POS = Vector3(0, 200, 0)
local HIGH_POS = Vector3(0, 400, 0)
local LATELY_POS = Vector3(180, 420, 0)

function GatherBar:__init()
	if GatherBar.Instance ~= nil then
		ErrorLog("[FunctionGuide] attempt to create singleton twice!")
		return
	end
	self.view_layer = UiLayer.MainUILow
	

	self:AddViewResource(0, "uis/view/main_ui_prefab", "GatherBar")
	GatherBar.Instance = self
	self.gather_id = 0
	self.open_tween = nil
	self.close_tween = nil
	self.is_wabao = false
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD        -- 分辨率

	self.cache_state = nil
	self.special_type = nil
	self.main_view_lately_show = false
	self.is_set_other_describe = nil
	self.default_describe = Language.Common.Gathering
end

function GatherBar:__delete()
	GatherBar.Instance = nil
end

function GatherBar:ReleaseCallBack()
	if self.progressbar then
		self.progressbar:DeleteMe()
		self.progressbar =nil
	end

	if self.tip_txt then
		self.tip_txt = nil
    end
    
	if CountDownManager.Instance:HasCountDown("prepare_time233") then
		CountDownManager.Instance:RemoveCountDown("prepare_time233")
	end
	if self.tween1 then
		self.tween1:Kill()
		self.tween1 = nil
	end

	self.is_wabao = false
	self.is_load_finish = nil
	self.special_type = nil
	self.main_view_lately_show = false
	self.is_set_other_describe = nil
end

function GatherBar:SetGatherId(gather_id,left_gather_time)
	self.gather_id = gather_id
	self.temp_gather_id = gather_id
	self.left_gather_time = left_gather_time or 0
	self:Flush()
end

function GatherBar:LoadCallBack()
    self.tip_txt = self.node_list["tip_txt"]
    self.is_load_finish = true
end

function GatherBar:ShowIndexCallBack()
	if self.need_init_view then
		self:SetGatherView()
		self.need_init_view = false
	end
end

function GatherBar:OnFlush()
	local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[self.gather_id]
	local describe = ""
	if gather_config then
		describe = gather_config.describe
		local name = gather_config.show_name
		if describe == "" then
			describe = name ~= "" and Language.Common.IsGather .. name or Language.Common.Gathering
		end
	end
	
	if self.is_wabao then
		describe = Language.Common.WaBaoing
	end

	if self.special_type ~= nil then
		describe = Language.Common.GatherBarStr[self.special_type] or describe
	end
		
	if self.main_view_lately_show then
		describe = ""
	end

	if not self.is_set_other_describe then
		self.default_describe = describe
	end

	self.tip_txt.text.text = self.default_describe
	self.node_list.tip_txt_part:SetActive(self.default_describe ~= "")
end

function GatherBar:OpenCallBack()
	self.cache_state = MainuiWGCtrl.Instance:GetRoleAutoXunluState()
	GlobalEventSystem:Fire(OtherEventType.GUAJI_TYPE_CHANGE, GuajiType.None)
end

function GatherBar:CloseCallBack()
	self.need_show_count_down = false
	self.gather_name = ""
	self.special_type = nil
	self.gather_id = 0
	self.main_view_lately_show = false
	self.is_set_other_describe = nil

	if self.tween1 then
		self.tween1:Pause()
	end

	local state = self.cache_state
	self.cache_state = nil

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
		return
	end
	
	if Scene.Instance then
		Scene.Instance:CheckWaitGatherList()
	end
	
	if state ~= nil then
		if state == XunLuStatus.XunLu then
			if main_role:IsStand() then
				return
			end
		elseif state == XunLuStatus.AutoFight then
			if GuajiCache.guaji_type == GuajiType.None then
				return
			end
		elseif state == XunLuStatus.AutoWaBao then
			return
		end
	end

	GlobalEventSystem:Fire(OtherEventType.GUAJI_TYPE_CHANGE, state)
end

function GatherBar:SetGatherTime(time, is_wabao, special_type)
	self.is_wabao = is_wabao
	self.total_time = time
	self.special_type = special_type

	if not self.is_load_finish then
		self.need_init_view = true
		return
	end
	self:SetGatherView()
end

function GatherBar:SetGatherView()
	-- self.main_view_lately_show = MainuiWGCtrl.Instance:GetLatelyGatherBtnState()
	MainuiWGCtrl.Instance:SetClickFindLatelyGatherBtnShow(false)
	
	if self.tween1 then
		self.tween1:Kill()
		self.tween1 = nil
	end

	-- if self.main_view_lately_show then
	-- 	self.node_list["Gather_Bar"].transform.anchoredPosition = LATELY_POS
	-- else
	--     if Scene.Instance:GetSceneType() == SceneType.HunYanFb then
	--         self.node_list["Gather_Bar"].transform.anchoredPosition = HIGH_POS
	--     else
	--         self.node_list["Gather_Bar"].transform.anchoredPosition = NORMAL_POS
	--     end
	-- end

	local enter_time = TimeWGCtrl.Instance:GetServerTime() + self.total_time
	CountDownManager.Instance:AddCountDown("prepare_time233", BindTool.Bind1(self.UpdatePrepareTime, self), BindTool.Bind1(self.CompleteLoading, self), enter_time, nil, 0.018)
	self.node_list["hight_light"].transform.rotation = VECTOR3_ZERO(0, 0, 0)
	self.tween1 = self.node_list["hight_light"].transform:DORotate(
			VECTOR3_360(0, 0, -360),
			self.total_time,
			DG.Tweening.RotateMode.FastBeyond360)
	self.tween1:SetEase(DG.Tweening.Ease.Linear)
end

function GatherBar:UpdatePrepareTime(elapse_time, total_time)
	if nil == elapse_time then return end
	self.node_list["progress"].slider.value =  elapse_time / ( self.total_time *0.96 )

	local text = self.default_describe
	-- 检查是否设置了采集物名称
	if self.gather_name and self.gather_name ~= "" then
		text = string.format(Language.Common.Gathering2, self.gather_name)
	end

	-- 检查是否需要显示倒计时秒数
	if self.need_show_count_down then
		text = string.format(text .. Language.Common.GatheringTime, math.ceil(self.total_time - elapse_time))
	end

	if self.main_view_lately_show then
		text = ""
	end

	if self.special_type == nil then
		self.tip_txt.text.text = text
	end

	self.node_list.tip_txt_part:SetActive(text ~= "")
end

function GatherBar:CompleteLoading()
	if Scene.Instance:GetSceneType() == SceneType.KF_BOSS then
       GuildWGCtrl.Instance:OpenKFBossGatherShareTip(self.temp_gather_id,self.left_gather_time)
       self.temp_gather_id = 0
    end
	self:Close()
end

-- 设置是否需要显示倒计时秒数
function GatherBar:SetNeedShowCountDown(is_show)
	self.need_show_count_down = is_show
end

-- 设置采集物名称
function GatherBar:SetGatherName(name)
	self.gather_name = name
end

-- 设置采集物名称
function GatherBar:SetOtherDefaultDescribe(var_default_describe)
	self.is_set_other_describe = true
	self.default_describe = var_default_describe
end