HonorhallsView = HonorhallsView or BaseClass(SafeBaseView)

function HonorhallsView:__init(view_name)
	self.active_close = false
    self:AddViewResource(0, "uis/view/kuafu_honorhalls_ui_prefab", "layout_honorhalls_view")
end

function HonorhallsView:ReleaseCallBack()
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end

	if self.my_tween then
		self.my_tween:Kill()
		self.my_tween = nil
	end

	if nil ~= self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end

	if self.be_select_event then
		GlobalEventSystem:UnBind(self.be_select_event)
		self.be_select_event = nil
	end

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	-- if nil ~= self.main_menu_icon_change then
	-- 	GlobalEventSystem:UnBind(self.main_menu_icon_change)
	-- 	self.main_menu_icon_change = nil
	-- end

	GlobalTimerQuest:CancelQuest(self.shop_timer)
	CountDownManager.Instance:RemoveCountDown("honorhalls_end_count_down")
	ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_state_change)
end

function HonorhallsView:CloseCallBack()
    if self.obj then
        self.obj:SetActive(false)
    end
end

function HonorhallsView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
	-- self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))

	if not self.rank_list then
		self.rank_list = AsyncListView.New(KFHonorhallRankItemRender, self.node_list["rank_list"])
	end

	self.node_list.fp_message_node:CustomSetActive(true)
	self.node_list.fp_history_message_node:CustomSetActive(false)
	self:InitParam()
	self:InitListener()

	XUI.AddClickEventListener(self.node_list.history_btn, BindTool.Bind2(self.BestHistoryClick, self))                 	-- 最速详情
    XUI.AddClickEventListener(self.node_list.message_btn, BindTool.Bind2(self.LevelMessageClick, self))                 -- 关卡详情
end

-- 主界面加载完成，把任务面板加到主界面的节点上去
function HonorhallsView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["fp_move_left_node"] then
		self.obj = self.node_list["fp_move_left_node"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0,0,0)
		self.obj.transform.localScale = Vector3.one
	end

	if self.obj ~= nil then
        self.obj:SetActive(true)
    end
end

-- function HonorhallsView:MainMenuIconChangeEvent(isOn)
-- 	local to_alpha = isOn and 0 or 1
-- 	local now_alpha = isOn and 1 or 0
-- 	self.node_list.left_root.canvas_group:DoAlpha(now_alpha, to_alpha, 0.4)
-- end

-- 初始化参数
function HonorhallsView:InitParam()
	self.reward_item_list = {}
	self.now_layer = nil
	self.max_lay = KuafuHonorhallWGData.Instance:GetMaxLayer()
	self.open_shop_layer = self.max_lay
	self.is_end_time = false
	self.shop_timer = nil
	self.show_timer = nil
	self.end_timestamp = 0
	self.save_act_status = nil
end

-- 初始化监听
function HonorhallsView:InitListener()
	self.be_select_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjCallBack, self))
	self.act_state_change = BindTool.Bind(self.ActStatusChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_state_change)
end


function HonorhallsView:OnSelectObjCallBack(target_obj, select_type)
	if target_obj ~= nil and target_obj:GetType() == SceneObjType.Npc then
		self:OnClickGotoShopBtn()
	end
end

function HonorhallsView:ActStatusChange()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if not IsEmptyTable(activity_info) and self.save_act_status ~= activity_info.status then
		self.save_act_status = activity_info.status
		self:FlushGuajiState()
	end
end

function HonorhallsView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:RefreshView()
			self:FlushRankView()
			self:FlushBestPassView()
			self:FlushLayerPlayerView()
		elseif k == "role_info" then
			self:RefreshView()
		elseif k == "pass_info" then
			self:RefreshRewardList()
		elseif k == "enter_scene" then
			self:FlushGuajiState()
			self:ShowNextLayer()
			self:ShowGoShopBtn()
		elseif k == "rank" then
			self:FlushRankView()
		elseif k == "best_pass_info" then
			self:FlushBestPassView()	
		elseif k == "layerplayer_info" then
			self:FlushLayerPlayerView()	
		end
	end
end

function HonorhallsView:RefreshView()
	local role_info = KuafuHonorhallWGData.Instance:GetRoleInfo()
	local cur_layer = role_info.cur_layer
	local cur_layer_kill_count = role_info.cur_layer_kill_count
	---[[后端在准备阶段时没清除上一次活动的数据
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		cur_layer = 0
		cur_layer_kill_count = 0
	end
	--]]

	self.node_list.kill_count_label.text.text = Language.Honorhalls.KillCountDesc
	local need_kill = KuafuHonorhallWGData.Instance:GetUpLayerKills(cur_layer + 1)
	if cur_layer < self.max_lay then
		self.node_list.arrived_next_layer_tips.text.text = string.format(Language.Honorhalls.ArrivedNextLayerDesc, cur_layer_kill_count, need_kill)
		self.node_list.next_layer_spine_tips:CustomSetActive(self.now_layer ~= nil and self.now_layer ~= cur_layer)
	else
		self.node_list.arrived_next_layer_tips.text.text = Language.Honorhalls.MaxLayer
	end

	if self.now_layer ~= cur_layer then
		self.now_layer = cur_layer
		self:RefreshLeftInfoPanle()
		self:RefreshRewardList()
		self:ShowGoShopBtn()
	end
end

-- 刷新提示信息
function HonorhallsView:RefreshLeftInfoPanle()
	local cur_layer = self.now_layer
	self.node_list.cur_layer_num.text.text = string.format(Language.FuBenPanel.ZhuShenTaFuCurLevel, cur_layer + 1)

	-- if cur_layer >= self.open_shop_layer then
	-- 	self.node_list.tips_label.text.text = Language.Honorhalls.OpenShopTips
	-- else
	-- 	self.node_list.tips_label.text.text = string.format(Language.Honorhalls.LayerTips, self.open_shop_layer + 1)
	-- end
end

-- 刷新奖励信息
function HonorhallsView:RefreshRewardList()
	if not KuafuHonorhallWGData.Instance then return end
	
	local reward_data = nil
	if self.now_layer == self.max_lay then
		reward_data = KuafuHonorhallWGData.Instance:GetAllRewardItem()
		self.node_list.layer_reward_desc.text.text = Language.Honorhalls.LayerMaxDesc
	else
		reward_data = KuafuHonorhallWGData.Instance:GetRewardItem(self.now_layer)
		self.node_list.layer_reward_desc.text.text = Language.Honorhalls.LayerDesc
	end
	if not reward_data then
		return
	end

	reward_data = SortTableKey(reward_data)
	local item_list = self.reward_item_list
	if #item_list < #reward_data then
		local parent = self.node_list.reward_list
		for i = #item_list + 1, #reward_data do
			item_list[i] = ItemCell.New(parent)
		end
		self.reward_item_list = item_list
	end

	for i=1,#item_list do
		if reward_data[i] then
			item_list[i]:SetData(reward_data[i])
			item_list[i]:SetActive(true)
		else
			item_list[i]:SetActive(false)
		end
	end
end

-- 刷新排行榜
function HonorhallsView:FlushRankView()
    local data_list = KuafuHonorhallWGData.Instance:GetThereRankInfo()
    self.rank_list:SetDataList(data_list)

    local my_info = KuafuHonorhallWGData.Instance:GetRoleInfo()
    local rank_num = KuafuHonorhallWGData.Instance:GetRankNum()
    self.node_list["my_rank"].text.text = rank_num > 0 and string.format(Language.Honorhalls.MyRank, rank_num) or Language.Honorhalls.NoRank
    local cur_layer = my_info and my_info.cur_layer + 1 or 0
    cur_layer = cur_layer >= 10 and 10 or cur_layer
    self.node_list["my_pass_str"].text.text = cur_layer > 0 and string.format(Language.Honorhalls.Layer,cur_layer) or ""
end

-- 刷新最速记录
function HonorhallsView:FlushBestPassView()
	local fast_pass_info = KuafuHonorhallWGData.Instance:GetFastPassInfo()
	
	if fast_pass_info then
		if fast_pass_info.fast_pass_time > 0 then
			local fast_time_str = ToColorStr(TimeUtil.FormatSecondDHM8(fast_pass_info.fast_pass_time), COLOR3B.DEFAULT_NUM)
			local fast_user_name = ToColorStr(fast_pass_info.fast_pass_user_name, COLOR3B.D_RED)
			self.node_list.best_history_txt.text.text = string.format("%s\n%s", fast_user_name, fast_time_str)
		end

		if fast_pass_info.my_fast_pass_time > 0 then
			self.node_list.my_history_txt.text.text = ToColorStr(TimeUtil.FormatSecondDHM8(fast_pass_info.my_fast_pass_time), COLOR3B.DEFAULT_NUM) 
		end
	end
end

-- 刷新当前层人数
function HonorhallsView:FlushLayerPlayerView()
	local my_info = KuafuHonorhallWGData.Instance:GetRoleInfo()
	self.node_list.curr_player_num.text.text = string.format(Language.Honorhalls.CurrLayerPlayerNum, my_info and my_info.cur_layer_role_num or 0)
end



function HonorhallsView:BestHistoryClick()
	self.node_list.fp_message_node:CustomSetActive(false)
	self.node_list.fp_history_message_node:CustomSetActive(true)
end

function HonorhallsView:LevelMessageClick()
	self.node_list.fp_message_node:CustomSetActive(true)
	self.node_list.fp_history_message_node:CustomSetActive(false)
end

function HonorhallsView:OnClickShowRewardBtn()
	KuafuHonorhallWGCtrl.Instance:OpenRewardView()
end

function HonorhallsView:OnClickGotoShopBtn()
	local scene_id = Scene.Instance:GetSceneId()
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if scene_cfg then
		local npc = scene_cfg.npcs[1] -- 顶层就一个NPC
		if npc then
			MoveCache.SetEndType(MoveEndType.DoNothing)
			MoveCache.param1 = npc.id
			local range = TaskWGData.Instance:GetNPCRange(npc.id)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, npc.x, npc.y, range, nil, nil, nil, function ()
				KuafuHonorhallWGCtrl.Instance:OpenBuyView()
			end)
		else
			print_error("-----【Why npc cfg no data】------", scene_id)
		end
	end
end

function HonorhallsView:ShowGoShopBtn()
	local scene_id = Scene.Instance:GetSceneId()
	local layer_cfg = KuafuHonorhallWGData.Instance:GetLayerCfgBySceneId(scene_id)
	if layer_cfg and layer_cfg.layer == self.open_shop_layer  then
		MainuiWGCtrl.Instance:SetToHonorhallShopBtnState(true)
	else
		MainuiWGCtrl.Instance:SetToHonorhallShopBtnState(false)
	end
end

function HonorhallsView:FlushGuajiState()
	local scene_id = Scene.Instance:GetSceneId()
	local layer_cfg = KuafuHonorhallWGData.Instance:GetLayerCfgBySceneId(scene_id)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if not layer_cfg or not activity_info then
		return
	end
	if activity_info.status == ACTIVITY_STATUS.STANDY then

	elseif activity_info.status == ACTIVITY_STATUS.CLOSE or layer_cfg.layer >= self.max_lay then
		GuajiWGCtrl.Instance:StopGuaji()
		MoveCache.SetEndType(MoveEndType.Node)
	else
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)  --挂机
	end
end

------------------------------------------------------------------------------

function HonorhallsView:ShowNextLayer()
	local scene_id = Scene.Instance:GetSceneId()
	local layer_cfg = KuafuHonorhallWGData.Instance:GetLayerCfgBySceneId(scene_id)
	if not layer_cfg then
		return
	end

	GlobalTimerQuest:CancelQuest(self.shop_timer)

	self:FlushEndCountDown()

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if activity_info and activity_info.status ~= ACTIVITY_STATUS.STANDY then
		if activity_info.next_time - TimeWGCtrl.Instance:GetServerTime() <= 60 then
			return
		end
	end
	
	if layer_cfg.layer == self.max_lay then
		self:ShowShopTips()
		self.shop_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:ShowMaxLayerTips()
		end, 2.5)
		self:OnClickGotoShopBtn()
	else
		self:ShowLayerTips(layer_cfg.layer + 1)
	end
end

function HonorhallsView:ShowOrHideTopTips(is_show)
	if self.my_tween then
		self.my_tween:Kill()
		self.my_tween = nil
	end
	if is_show then
		self.node_list.top_center_root:SetActive(true)
		self.node_list.top_center_root.canvas_group.alpha = 1
	else
		local tween = self.node_list.top_center_root.canvas_group:DoAlpha(1, 0, 0.5)
		tween:OnComplete(function ()
			self.my_tween = nil
			self.node_list.layer_desc:SetActive(false)
			self.node_list.max_layer_desc:SetActive(false)
			self.node_list.act_end_count_down:SetActive(false)
			self.node_list.finish_desc:SetActive(false)
			-- self:ShowCountDown()
		end)
		self.my_tween = tween
	end
end

-- 2s后渐变隐藏
function HonorhallsView:DelayTimeHide()
	GlobalTimerQuest:CancelQuest(self.show_timer)
	self.show_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:ShowOrHideTopTips(false)
	end, 2)
end

-- 当前层数：XX层
function HonorhallsView:ShowLayerTips(layer)
	self.node_list.layer_desc:SetActive(true)
	self.node_list.cur_layer_label.text.text = layer
	self:DelayTimeHide()
	self:ShowOrHideTopTips(true)
end

-- 到达顶层，神秘商人已出现
function HonorhallsView:ShowShopTips()
	self.node_list.max_layer_desc:SetActive(true)
	self:DelayTimeHide()
	self:ShowOrHideTopTips(true)
end

-- 您已冲顶成功，可随时离开副本
function HonorhallsView:ShowMaxLayerTips()
	self.node_list.finish_desc:SetActive(true)
	self.node_list.layer_desc:SetActive(false)
	self.node_list.max_layer_desc:SetActive(false)
	self:DelayTimeHide()
	self:ShowOrHideTopTips(true)
end

---[[ 活动倒计时
function HonorhallsView:ShowCountDown()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if not activity_info then
		return
	end
	if activity_info.status == ACTIVITY_STATUS.STANDY then
		FuBenPanelWGCtrl.Instance:SetCountDowmType(nil, GameEnum.FU_BEN_ZHUNBEI)
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time, true, GameEnum.FU_BEN_ZHUNBEI)
	else
		if activity_info.next_time - TimeWGCtrl.Instance:GetServerTime() > 60 then
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time, false, GameEnum.FU_BEN_OUT_SCENE)
		else
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(-1)
		end
	end
end

function HonorhallsView:FlushEndCountDown()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if not activity_info or activity_info.status == ACTIVITY_STATUS.STANDY then
		return
	end
	self.is_end_time = true
	self.end_timestamp = activity_info.next_time
	CountDownManager.Instance:RemoveCountDown("honorhalls_end_count_down")
	if self.end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
		CountDownManager.Instance:AddCountDown(
			"honorhalls_end_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.EndCountDown, self),
			activity_info.next_time,
			nil,
			1
		)
	end
end

-- 跨服存在刚进入场景时候时间不同步的情况(所以一直拿两个时间戳相减)
local count_down = 0
function HonorhallsView:UpdateCountDown(elapse_time, total_time)
	count_down = self.end_timestamp - TimeWGCtrl.Instance:GetServerTime()
	if count_down <= 60 then
		if self.is_end_time then
			self.node_list.act_end_count_down:SetActive(true)
			self:ShowOrHideTopTips(true)
			self.is_end_time = false
		end
		self.node_list.count_down_label.text.text = math.floor(count_down)
	end
end

function HonorhallsView:EndCountDown()
	self:ShowOrHideTopTips(false)
end
--]]


----------------------------KFHonorhallRankItemRender---------------------------

KFHonorhallRankItemRender = KFHonorhallRankItemRender or BaseClass(BaseRender)
function KFHonorhallRankItemRender:OnFlush()
    if not self.data then
        return
    end

	self.node_list.rank_text.text.text = self.index >= 4 and self.index or ""
	local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")
	if self.index < 4 then    
		bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["rank_img"]:SetActive(true)
		self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
	else
		self.node_list["rank_img"]:SetActive(false)
    end

	self.node_list["bg"].image:LoadSprite(bg_bundle, bg_asset)
    local my_uuid = RoleWGData.Instance:GetUUid()
    local name_color = COLOR3B.WHITE
    if my_uuid == self.data.uuid then
        name_color = COLOR3B.DEFAULT_NUM
    end

    self.node_list.role_name.text.text = ToColorStr(self.data.user_name, name_color)
	local cur_layer = self.data.max_layer + 1
    cur_layer = cur_layer >= 10 and 10 or cur_layer
    if cur_layer > 0 then
        self.node_list["pass_str"].text.text = string.format(Language.Honorhalls.Layer,cur_layer)
    else
        self.node_list["pass_str"].text.text = ""
    end

    -- local flag = self.index <= 3
    -- self.node_list.rank_img:SetActive(flag)
    -- self.node_list.rank_text:SetActive(not flag)
    -- if flag then
    --     local b,a = ResPath.GetCommonIcon("a3_tb_jp" .. self.index)
	-- 	self.node_list.rank_img.image:LoadSprite(b, a, function()
    --         XUI.ImageSetNativeSize(self.node_list.rank_img)
	-- 	end)
    -- else
    --     self.node_list.rank_text.text.text = self.index
    -- end
    -- local my_uuid = RoleWGData.Instance:GetUUid()
    -- local name_color = COLOR3B.WHITE
    -- if my_uuid == self.data.uuid then
    --     name_color = COLOR3B.DEFAULT_NUM
    -- end

    -- self.node_list.role_name.text.text = ToColorStr(self.data.user_name, name_color)
    -- local cur_layer = self.data.max_layer + 1
    -- cur_layer = cur_layer >= 10 and 10 or cur_layer
    -- if cur_layer > 0 then
    --     self.node_list["pass_str"].text.text = string.format(Language.Honorhalls.Layer,cur_layer)
    -- else
    --     self.node_list["pass_str"].text.text = ""
    -- end
end