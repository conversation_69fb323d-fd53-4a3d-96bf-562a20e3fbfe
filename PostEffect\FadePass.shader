﻿Shader "Game/PostEffect/FadePass"
{
	Properties
	{
		_MainTex("Texture", 2D) = "white" {}
		_Process("Process", float) = 1
	}
	
	CGINCLUDE

	#include "UnityCG.cginc"

	struct v2f
	{
		half4 pos : SV_POSITION;
		half2 uv : TEXCOORD0;
	};

	sampler2D _MainTex;
	float _Process;

	v2f vert(appdata_img v)
	{
		v2f o;
		o.pos = UnityObjectToClipPos(v.vertex);
		o.uv.xy = v.texcoord.xy;
		return o;
	}

	half4 frag (v2f o) : SV_Target
	{
		fixed4 mianColor = tex2D(_MainTex, o.uv);
		fixed4 shadowColor = fixed4(1, 1, 1, 1) * _Process;
		return mianColor * shadowColor;
	}

	ENDCG
	
	Subshader
	{
		Cull Off
		ZTest Off
		ZWrite Off
 
		Pass
		{     
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			ENDCG
		} 
	}
	
	Fallback off
}
