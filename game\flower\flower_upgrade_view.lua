local SHOW_REWARD_MAX = 5
local SHOW_REWARD_MUST_NUM = 2

function FlowerView:InitFlowerUpgradeView()
    self.is_auto_up_grade = false

    XUI.AddClickEventListener(self.node_list.meili_upgrade_btn, BindTool.Bind(self.OnUpGradeBtn, self))

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    if not self.reward_item_list then
        self.reward_item_list = {}
        for i = 1, SHOW_REWARD_MAX do
            self.reward_item_list[i] = FlowerRewardItemRender.New(self.node_list["flower_reward_item_" .. i])
            self.reward_item_list[i]:SetIndex(i)
        end
    end
end

function FlowerView:DeleteFlowerUpgradeView()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.reward_item_list then
        for k, v in pairs(self.reward_item_list) do
            v:DeleteMe()
            v = nil
        end
        self.reward_item_list = nil
    end
end

function FlowerView:FlushFlowersView(flush_key)
    local meili_level = MarryWGData.Instance:GetFlowerScoreUpgradeLevel()
    local flower_num = MarryWGData.Instance:GetSendFlowerNum()
    local flower_cfg = MarryWGData.Instance:GetSendFlowersUpgradeByLevel(meili_level)

    self.node_list.txt_tips.text.text = Language.Flower.FlowerUpgradeDes
    self.node_list.meili_level_text.text.text = string.format(Language.Flower.Level, meili_level)

    if flower_cfg then
        local next_flower_cfg = MarryWGData.Instance:GetSendFlowersUpgradeByLevel(meili_level + 1)
        local need_num = next_flower_cfg and next_flower_cfg.need_num or flower_cfg.need_num

        if next_flower_cfg then
            self.reward_list:SetDataList(next_flower_cfg.reward_item or {})
    
            for i = 1, SHOW_REWARD_MAX do
                --前2个获取固定奖励数据，其他则获取随机奖励数据.
                if i <= SHOW_REWARD_MUST_NUM then
                    if next_flower_cfg.reward_item and next_flower_cfg.reward_item[i - 1] then
                        self.reward_item_list[i]:SetData(next_flower_cfg.reward_item[i - 1])
                    end
                else
                    if next_flower_cfg.reward_item_2 and next_flower_cfg.reward_item_2[i - 1 - SHOW_REWARD_MUST_NUM] then
                        self.reward_item_list[i]:SetData(next_flower_cfg.reward_item_2[i - 1 - SHOW_REWARD_MUST_NUM])
                    end
                end
            end
        end

        local slider_value = flower_num / need_num
        slider_value = slider_value > 1 and 1 or slider_value
        local is_max_level = meili_level == MarryWGData.Instance:GetSendFlowersUpgradeMaxLevel()

        self.node_list.meili_num.text.text = string.format(Language.Flower.Expend, flower_num, need_num)
        self.node_list.upgrade_flag:SetActive(flower_num >= need_num and not is_max_level)
        self.node_list.hl_flower_group:SetActive(flower_num >= need_num and not is_max_level)
        self.node_list.nor_flower_group:SetActive(flower_num < need_num)
        self.node_list.flower_slider:SetActive(flower_num < need_num)
        self.node_list.max_level_flag:SetActive(is_max_level)
        self.node_list.meili_upgrade_btn.button.interactable = not is_max_level

        if "send_flower_level_change" == flush_key then
            self.node_list.meili_slider.image:DOFillAmount(0, 0.5):OnComplete(function()
                if self.node_list.meili_slider then
                    self.node_list.meili_slider.image:DOFillAmount(slider_value, 0.8):OnComplete(function()
                        self:AutoUpGrade()
                    end)
                end
            end)
        elseif "send_flower_num_change" == flush_key then
            self.node_list.meili_slider.image:DOFillAmount(slider_value, 1)
        else
            self.node_list.meili_slider.image.fillAmount = slider_value
        end
    end
end

function FlowerView:CloseAutoUpGrade()
    self.is_auto_up_grade = false
end

function FlowerView:OnUpGradeBtn()
    self.is_auto_up_grade = not self.is_auto_up_grade
    self:AutoUpGrade()
end

--升级成功
function FlowerView:UpGradeSuccess()
	if self.node_list["effect_pos"] then --不为空判断
		--播放升级成功特效
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_tupo,
					is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["effect_pos"]})
	end

    if self.node_list.up_grade_success_effect then
        self.node_list.up_grade_success_effect:SetActive(false)
        self.node_list.up_grade_success_effect:SetActive(true)
    end
end

function FlowerView:AutoUpGrade()
    if self.is_auto_up_grade then
        local flower_num = MarryWGData.Instance:GetSendFlowerNum()
        local flower_cfg = MarryWGData.Instance:GetSendFlowersUpgradeByLevel(MarryWGData.Instance:GetFlowerScoreUpgradeLevel() + 1)

        if flower_cfg and flower_num >= flower_cfg.need_num then
            MarryWGCtrl.Instance:SendCSUpgradeFlowerScoreLevel()
        else
            self:CloseAutoUpGrade()
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.SendFlowerRemind)
        end
    end
end

function FlowerView:OnClickGotoBtn()
    FlowerWGCtrl.Instance:Open()
end

------------------------------FlowerRewardItemRender------------------------------
FlowerRewardItemRender = FlowerRewardItemRender or BaseClass(BaseRender)

function FlowerRewardItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.OnClickItemBtn, self))
end

function FlowerRewardItemRender:ReleaseCallBack()

end

function FlowerRewardItemRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.name.text.text = self.index > SHOW_REWARD_MUST_NUM and Language.Flower.GetText or Language.Flower.GetMustText
    self.node_list.num.text.text = string.format(Language.Flower.ItemNumText, self.data.num)

    local bundle, asset = ResPath.GetItem(self.data.item_id)
    XUI.SetNodeImage(self.node_list.icon, bundle, asset)
end

function FlowerRewardItemRender:OnClickItemBtn()
    if not self.data then
        return
    end

    TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_NORMAL)
end
