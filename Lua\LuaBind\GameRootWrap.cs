﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class GameRootWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(GameRoot), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("IsUseAndroid64", IsUseAndroid64);
		<PERSON><PERSON>ction("Restart", Restart);
		<PERSON><PERSON>unction("PruneLuaBundles", PruneLuaBundles);
		<PERSON><PERSON>unction("SetBuglyUserID", SetBuglyUserID);
		<PERSON><PERSON>RegFunction("SetBuglySceneID", SetBuglySceneID);
		<PERSON><PERSON>RegFunction("UpdateLogEnable", UpdateLogEnable);
		<PERSON><PERSON>unction("LimitScreenResolution", LimitScreenResolution);
		L.RegFunction("GetFixQueryUrl", GetFixQueryUrl);
		<PERSON><PERSON>RegFunction("DecodeJsonData", DecodeJsonData);
		<PERSON><PERSON>RegFunction("IsLuaFileExist", IsLuaFileExist);
		<PERSON><PERSON>("GetAliasResPath", GetAliasResPath);
		<PERSON><PERSON>ction("AddLuaWarning", AddLuaWarning);
		L.RegFunction("CollectMemoryInfo", CollectMemoryInfo);
		L.RegFunction("SetIsCollectGraphicMem", SetIsCollectGraphicMem);
		L.RegFunction("SetIsLowMemColllect", SetIsLowMemColllect);
		L.RegFunction("SetIsChangeSceneColllect", SetIsChangeSceneColllect);
		L.RegFunction("PrintSceneMemoryLog", PrintSceneMemoryLog);
		L.RegFunction("GetFPSSampler", GetFPSSampler);
		L.RegFunction("GetEditorTimeStamp", GetEditorTimeStamp);
		L.RegFunction("GetDebugTrackback", GetDebugTrackback);
		L.RegFunction("OverrideLuaBundle", OverrideLuaBundle);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("GLOBAL_URL_SIGN_KEY", get_GLOBAL_URL_SIGN_KEY, null);
		L.RegVar("QueryURLBase64DecodeKey", get_QueryURLBase64DecodeKey, set_QueryURLBase64DecodeKey);
		L.RegVar("GetQueryUrl", get_GetQueryUrl, null);
		L.RegVar("Instance", get_Instance, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsUseAndroid64(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = GameRoot.IsUseAndroid64();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Restart(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			obj.Restart();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PruneLuaBundles(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			obj.PruneLuaBundles();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetBuglyUserID(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			string arg0 = ToLua.CheckString(L, 2);
			obj.SetBuglyUserID(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetBuglySceneID(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetBuglySceneID(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateLogEnable(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
				obj.UpdateLogEnable();
				return 0;
			}
			else if (count == 2)
			{
				GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.UpdateLogEnable(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GameRoot.UpdateLogEnable");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LimitScreenResolution(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.LimitScreenResolution(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFixQueryUrl(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = GameRoot.GetFixQueryUrl();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DecodeJsonData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string o = GameRoot.DecodeJsonData(arg0, arg1);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsLuaFileExist(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			bool o = GameRoot.IsLuaFileExist(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAliasResPath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = GameRoot.GetAliasResPath(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddLuaWarning(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				GameRoot.AddLuaWarning(arg0);
				return 0;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				GameRoot.AddLuaWarning(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GameRoot.AddLuaWarning");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CollectMemoryInfo(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
			GameRoot.CollectMemoryInfo(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsCollectGraphicMem(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			GameRoot.SetIsCollectGraphicMem(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsLowMemColllect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			GameRoot.SetIsLowMemColllect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsChangeSceneColllect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			GameRoot.SetIsChangeSceneColllect(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PrintSceneMemoryLog(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
			GameRoot.PrintSceneMemoryLog(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFPSSampler(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			FPSSampler o = obj.GetFPSSampler();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEditorTimeStamp(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			double o = GameRoot.GetEditorTimeStamp();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDebugTrackback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			string o = obj.GetDebugTrackback();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverrideLuaBundle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GameRoot obj = (GameRoot)ToLua.CheckObject(L, 1, typeof(GameRoot));
			string arg0 = ToLua.CheckString(L, 2);
			string arg1 = ToLua.CheckString(L, 3);
			obj.OverrideLuaBundle(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_GLOBAL_URL_SIGN_KEY(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, GameRoot.GLOBAL_URL_SIGN_KEY);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_QueryURLBase64DecodeKey(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, GameRoot.QueryURLBase64DecodeKey);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_GetQueryUrl(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, GameRoot.GetQueryUrl);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.PushSealed(L, GameRoot.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_QueryURLBase64DecodeKey(IntPtr L)
	{
		try
		{
			string arg0 = ToLua.CheckString(L, 2);
			GameRoot.QueryURLBase64DecodeKey = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

