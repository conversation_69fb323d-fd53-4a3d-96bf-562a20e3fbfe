RechargeVipAllPrivilegeTips = RechargeVipAllPrivilegeTips or BaseClass(SafeBaseView)

local PAGE_MAX = 2					--最大页数.
local VIP_LEVEL_MAX = 15			--最大VIP等级.

function RechargeVipAllPrivilegeTips:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel",
		{ vector2 = Vector2(0, -17), sizeDelta = Vector2(1080, 612) })
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_recharge_vip_all_privilege_tips")
end

function RechargeVipAllPrivilegeTips:LoadCallBack()
	self.cur_page_idx = 1

	if not self.add_item_list then
		self.add_item_list = AsyncListView.New(RechargeVipAllPrivilegeTipsItem, self.node_list.content)
	end

	if not self.title_item then
		self.title_item = RechargeVipAllPrivilegeTipsItem.New(self.node_list.vip_all_privilege_item)
	end

	XUI.AddClickEventListener(self.node_list.left_btn, BindTool.Bind(self.ChangePage, self, -1))
	XUI.AddClickEventListener(self.node_list.right_btn, BindTool.Bind(self.ChangePage, self, 1))

	self.node_list.title_view_name.text.text = Language.Recharge.VipAllPrivilegeTitle
end

function RechargeVipAllPrivilegeTips:ReleaseCallBack()
	if self.add_item_list then
		self.add_item_list:DeleteMe()
		self.add_item_list = nil
	end

	if self.title_item then
		self.title_item:DeleteMe()
		self.title_item = nil
	end
end

function RechargeVipAllPrivilegeTips:OnFlush()
	local data_list = VipWGData.Instance:GetCanShowVipSpecPermissions()
	if not IsEmptyTable(data_list) then
		self.add_item_list:SetDataList(data_list)
	end

	self.title_item:SetData({ is_title = true })

	self.node_list.page_text.text.text = string.format(Language.KingVip.page_text, self.cur_page_idx, PAGE_MAX)

	local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local jump_page_idx = self.cur_page_idx
	for i = 1, PAGE_MAX do
		local page_info = VipWGData.Instance:GetVipAllPrivilegeTipsPageInfo(i)
		if page_info then
			if page_info.start_idx <= cur_vip_level and cur_vip_level <= page_info.end_idx then
				jump_page_idx = i
				break
			end
		end
	end

	self:ChangePage(0, jump_page_idx)
end

function RechargeVipAllPrivilegeTips:ChangePage(value, jump_page_idx)
	self.cur_page_idx = jump_page_idx and jump_page_idx or self.cur_page_idx + value

	if self.cur_page_idx > PAGE_MAX then
		self.cur_page_idx = PAGE_MAX
		return
	elseif self.cur_page_idx < 1 then
		self.cur_page_idx = 1
		return
	end

	self.node_list.left_btn_image1:SetActive(self.cur_page_idx <= 1)
	self.node_list.left_btn_image2:SetActive(self.cur_page_idx > 1)
	self.node_list.right_btn_image1:SetActive(self.cur_page_idx >= PAGE_MAX)
	self.node_list.right_btn_image2:SetActive(self.cur_page_idx < PAGE_MAX)

	self.node_list.page_text.text.text = string.format("%s/%s", self.cur_page_idx, PAGE_MAX)

	if self.add_item_list then
		local data_list = VipWGData.Instance:GetCanShowVipSpecPermissions()
		if not IsEmptyTable(data_list) then
			self.add_item_list:SetDataList(data_list)
		end
	end

	self.title_item:SetData({ is_title = true })
end

function RechargeVipAllPrivilegeTips:GetPageIdx()
	return self.cur_page_idx
end

---------------------------------RechargeVipAllPrivilegeTipsItem-----------------------------
RechargeVipAllPrivilegeTipsItem = RechargeVipAllPrivilegeTipsItem or BaseClass(BaseRender)

function RechargeVipAllPrivilegeTipsItem:ReleaseCallBack()

end

function RechargeVipAllPrivilegeTipsItem:LoadCallBack()
	self.cur_page_idx = 1
	self:InitValueNodeList()
end

function RechargeVipAllPrivilegeTipsItem:InitValueNodeList()
	self.value_node_list = {}
	for i = 0, 10 do
		local node_parent = self.node_list.value_group:FindObj("value_" .. i)
		self.value_node_list[i] = {}
		self.value_node_list[i].value_bg = node_parent:FindObj("value_bg")
		self.value_node_list[i].value_text = node_parent:FindObj("value_text")
	end
end

function RechargeVipAllPrivilegeTipsItem:OnFlush()
	if not self.data then
		return
	end

	self.cur_page_idx = RechargeWGCtrl.Instance:GetAllPrivilegeTipsPageIdx()
	local page_info = VipWGData.Instance:GetVipAllPrivilegeTipsPageInfo(self.cur_page_idx)
	if IsEmptyTable(page_info) then
		return
	end

	self.node_list.add_name_text.text.text = self.data.is_title and
		Language.Recharge.VipAllPrivilegeInfo.title or self.data.power_desc

	local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local page_start = page_info.start_idx
	local page_end = page_info.end_idx
	local vip_level

	--i为预制体后缀.
	for i = 0, 10 do
		local text = ""
		local is_show = false
		vip_level = page_start + i
		if vip_level <= VIP_LEVEL_MAX then
			if self.data.is_title then
				if vip_level <= page_end then
					text = Language.Recharge.VipAllPrivilegeInfo["vip_" .. vip_level]
				end
			else
				if vip_level <= page_end then
					is_show = cur_vip_level == vip_level
					local add_value = self.data["param_" .. vip_level]
					if self.data.show_mode == 0 then --是否拥有特权.
						text = add_value == 1 and Language.Recharge.VipAllPrivilegeHave or
						Language.Recharge.VipAllPrivilegeNoHave
					elseif self.data.show_mode == 1 then --特权数值.
						text = add_value
					elseif self.data.show_mode == 2 then --万分比.
						text = add_value / 100 .. "%"
					end
				end
			end
		end

		--有数据则显示实际数据，无数据的位置显示为 空.
		self.value_node_list[i].value_text.text.text = text
		self.value_node_list[i].value_bg:SetActive(is_show)
	end
end
