LandWarFbPersonWGData = LandWarFbPersonWGData or BaseClass()

LandWarFbPersonWGData.STAGE = {
	ZREO = 0,
	ONE = 1,
	TWO = 2,
	THREE = 3,
}

function LandWarFbPersonWGData:__init()
	if LandWarFbPersonWGData.Instance ~= nil then
		<PERSON>rrorLog("[LandWarFbPersonWGData] attempt to create singleton twice!")
		return
	end
	LandWarFbPersonWGData.Instance = self

	self:InitCfg()

	self.stage = 0
	self.monster_die_flag = {}
	self.pass_reward_flag  = {}

	RemindManager.Instance:Register(RemindName.LandWarFbPerson, BindTool.Bind(self.GetLandWarFbPersonRemind, self))
end

function LandWarFbPersonWGData:__delete()
	LandWarFbPersonWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.LandWarFbPerson)
end

function LandWarFbPersonWGData:InitCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("land_war_fb_cfg_auto")
	self.land_cfg = ListToMap(cfg.land, "stage")
	self.monster_cfg = ListToMap(cfg.monster, "seq")
	self.camp_cfg = ListToMap(cfg.camp, "camp")
	self.city_land_cfg = ListToMap(cfg.city_land, "seq")
	self.other_cfg = cfg.other[1]
	self.city_land_reward_cfg = ListToMap(cfg.city_land_reward, "seq")
end


function LandWarFbPersonWGData:SetBaseInfo(protocol)
	self.stage = protocol.stage
	self.monster_die_flag = protocol.monster_die_flag
	self.pass_reward_flag  = protocol.pass_reward_flag
end

function LandWarFbPersonWGData:GetCurStage()
	return self.stage
end

function LandWarFbPersonWGData:GetBossActive(boss_seq)   -- boss只存活于争夺时间段内   -- 0 存活   1死亡
	return self.monster_die_flag[boss_seq] == 0
end

function LandWarFbPersonWGData:GetCurStageMonsterListData(stage)
	local monster_list = {}
	local land_cfg = self.land_cfg[stage]
	if not land_cfg then
		return monster_list
	end
	
	local scene_id = land_cfg.scene_id

	for k, v in pairs(self.monster_cfg) do
		if v.scene_id == scene_id then
			table.insert(monster_list, v)
		end
	end

	return monster_list
end

function LandWarFbPersonWGData:GetCurStageLandCfg(stage)
	return self.land_cfg[stage]
end

function LandWarFbPersonWGData:GetAllStageLandCfg()
	return self.land_cfg
end

function LandWarFbPersonWGData:GetCityCfgDataInfo()
	return self.camp_cfg, self.city_land_cfg
end

function LandWarFbPersonWGData:GetOtherCfg()
	return self.other_cfg
end

function LandWarFbPersonWGData:GetPassRewardIsGet()
	return self.pass_reward_flag == 1
end

function LandWarFbPersonWGData:GetCityLandRewardCfg()
	return self.city_land_reward_cfg
end

function LandWarFbPersonWGData:IsGetAllPassReward()
	for k, v in pairs(self.city_land_reward_cfg) do
		if not self:GetPassRewardIsGetBySeq(v.seq) then
			return false
		end
	end
	
	return true
end

function LandWarFbPersonWGData:GetPassRewardIsGetBySeq(seq)
	return 1 == self.pass_reward_flag[seq]
end

function LandWarFbPersonWGData:GetLandWarFbPersonRemind()
	if self.stage == 3 then
		for k, v in pairs(self.city_land_reward_cfg) do
			if not self:GetPassRewardIsGetBySeq(v.seq) then
				return 1
			end
		end
	end

	-- if self.stage == 3 and self.pass_reward_flag == 0 then
	-- 	return 1
	-- end

	return 0
end