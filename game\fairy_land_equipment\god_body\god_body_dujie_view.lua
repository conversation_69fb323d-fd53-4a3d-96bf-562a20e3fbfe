GodBodyDuJieView = GodBodyDuJieView or BaseClass(SafeBaseView)

local jj_co
function GodBodyDuJieView:__init()
    -- self:SetMaskBg(true, true)
    local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	self:AddViewResource(0, bundle, "god_body_dujie_view")
end

function GodBodyDuJieView:ReleaseCallBack()
	self.audio_play = nil
	self.cur_hp = nil
	self.cur_slot = nil

	if CountDownManager.Instance:HasCountDown("dujie_auto_close") then
		CountDownManager.Instance:RemoveCountDown("dujie_auto_close")
	end

	if CountDownManager.Instance:HasCountDown("jj_thunder") then
		CountDownManager.Instance:RemoveCountDown("jj_thunder")
	end

	self:DeleteEffectList(true)
end

function GodBodyDuJieView:DeleteEffectList(is_delete)
	if not is_delete then
		if not IsEmptyTable(self.effect_list) then
			for i, v in pairs(self.effect_list) do
				v:SetActive(false)
			end
		end
	else
		self.effect_list = nil
	end

	self.uplevel_effect = nil
end

function GodBodyDuJieView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["close_panel_btn"], BindTool.Bind(self.Close,self))
end

function GodBodyDuJieView:ShowIndexCallBack()
	self:ReadyDuJie()
end

function GodBodyDuJieView:OnFlush()

end

function GodBodyDuJieView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("dujie_auto_close") then
		CountDownManager.Instance:RemoveCountDown("dujie_auto_close")
	end
end

-- {slot}
function GodBodyDuJieView:SetData(data)
	self.cur_slot = data.slot
	self.cur_result = data.result
end

function GodBodyDuJieView:SwitchBreakResult(status)
	-- self:SetContinueBtnState(false)
	if status then
		local result = self.cur_result or 0
		local str
		local effect_type
        local img_name
		local is_success = true
		if result == 1 then
			effect_type = UIEffectName.s_dujie
            img_name = "a1_s_dujie"
			is_success = true
		elseif result == 0 then
			effect_type = UIEffectName.f_dujie
            img_name = "a1_f_dujie"
			is_success = false
		end

		if effect_type then
			TipWGCtrl.Instance:ShowEffect({effect_type = effect_type, is_success = is_success,
				pos = Vector2(0, 0), parent_node = self.node_list.effct_break_result})
		end
		if result == 1 then
			-- self:PlaySuccTween()
			MainuiWGCtrl.Instance:DelayShowCachePower(0)
		else
			-- self:SetContinueBtnState(true)
		end

		if img_name then
			local bundle, asset = ResPath.GetNoPackPNG(img_name)
			self.node_list["img_break_result"].image:LoadSprite(bundle, asset, function()
				self.node_list["img_break_result"].image:SetNativeSize()
				self.node_list["img_break_result"]:SetActive(true)
				self.node_list["btn_close_window"]:SetActive(true)
				self.node_list["img_break_result"].rect.localScale = Vector3(0,0,0)
				self.node_list["img_break_result"].rect:DOScale(Vector3(1,1,1), 0.7)
			end)
		end
			
		if CountDownManager.Instance:HasCountDown("dujie_auto_close") then
			CountDownManager.Instance:RemoveCountDown("dujie_auto_close")
		end

		CountDownManager.Instance:AddCountDown("dujie_auto_close", BindTool.Bind(self.DuJieAutoCloseUpdate, self),
			BindTool.Bind(self.DuJieAutoCloseComplete, self), nil, 10, 1)

		self.node_list["close_panel_btn"]:SetActive(true)
	else
		self.node_list["auto_close_text"].text.text = ""
		self.node_list["img_break_result"]:SetActive(false)
		self.node_list["btn_close_window"]:SetActive(false)
		self.node_list["close_panel_btn"]:SetActive(false)
	end

end

function GodBodyDuJieView:DuJieAutoCloseUpdate(now_time, elapse_time)
	local time = math.ceil(elapse_time - now_time)
	self.node_list["auto_close_text"].text.text = string.format(Language.Common.AutoCloseTimerTxt,time)
end

function GodBodyDuJieView:DuJieAutoCloseComplete()
	self.node_list["auto_close_text"].text.text = ""
	self:Close()
end

function GodBodyDuJieView:ReadyDuJie()
	if not self:IsOpen() then
		return
	end
	self:DeleteEffectList()
	self:SwitchBreakResult(false)
	-- local result = JingJieWGData.Instance:GetBreakResult()
	local result = self.cur_result
	if result == nil then
		return
	end
	local is_succ = result == 1

	self.node_list["txt_hp_per"].text.text = "100%"
	self.cur_hp = 100
	self.hp_bar = 1
	self.node_list["dujie_slider_hp"].image.fillAmount = 1


	local slot = self.cur_slot or 0
	local gb_data = FairyLandEquipmentWGData.Instance:GetGodBodyData(slot)
	local grade = gb_data:GetGrade()
	if is_succ then
		grade = grade - 1
	end
	local cfg = FairyLandEquipmentWGData.Instance:GetGBUpGradeCfg(slot,grade)

	local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
    local buy_seq = slot_cfg and slot_cfg.buy_seq or -1
    local is_buy_upgrade_right = FairyLandEquipmentWGData.Instance:GetGodBodyUpgradeRightIsBuy(buy_seq)
    local right_cfg = FairyLandEquipmentWGData.Instance:GetGBUpgradeRightCfg(buy_seq)
	local succ_per = cfg and cfg.succ_per or 10000
	local tequan_add_per = (right_cfg and is_buy_upgrade_right) and right_cfg.dujie_add_per or 0
	succ_per = succ_per * 0.01
	tequan_add_per = tequan_add_per * 0.01
	if is_buy_upgrade_right then
		local temp_str = string.format(Language.FairyLandEquipment.DuJieTeQuanAdd, tequan_add_per)
		local str = string.format("%s%s", string.format(Language.FairyLandEquipment.DuJieSuccRate, succ_per), temp_str)
		self.node_list["txt_succ_per"].text.text = str
	else
		self.node_list["txt_succ_per"].text.text = string.format(Language.FairyLandEquipment.DuJieSuccRate, succ_per)
	end

	local delay_time = 1
	-- self:AsyncBreakEffect("effects2/prefab/ui/ui_dujie_leiyun_prefab","UI_dujie_leiyun")

	-- self:PlayLeiYunBGM()

	GlobalTimerQuest:AddDelayTimer(function()
		self:StartDuJie(is_succ)
	end,delay_time)
end


function GodBodyDuJieView:StartDuJie(success)
	local min_hp = success and FairyLandEquipmentWGData.Instance:GetDuJieMinHp() or 0
	local hp_list = FairyLandEquipmentWGData.Instance:GetDuJieHpList(min_hp)
	local thunder_time = FairyLandEquipmentWGData.Instance:GetDuJieTime()

	local thunder_num = GOD_BODY_DUJIE_NUM
	jj_co = coroutine.create(function()
		for i,v in ipairs(thunder_time) do
			local CD = CountDownManager.Instance
			if CD:HasCountDown("jj_thunder") then
				CD:RemoveCountDown("jj_thunder")
			end
			if self:IsOpen() then
				local delay_time = self:PlayThunder(i, thunder_num)
				if self.dujie_effect_delay then
					GlobalTimerQuest:CancelQuest(self.dujie_effect_delay)
					self.dujie_effect_delay = nil
				end

				self.dujie_effect_delay = GlobalTimerQuest:AddDelayTimer(function()
					-- self:AsyncBreakEffect("effects2/prefab/ui_x_prefab","UI_huoqiu_dujie01", false, 0, 0)
					self:AsyncBreakEffect("effects2/prefab/ui/ui_dujie_shouji_prefab","UI_dujie_shouji", false, 0, -175)
					self:PlayHpCount(hp_list[i], v)
					if i == thunder_num then
						local result = self.cur_result or 0
						local str
						if result == 1 then
							str = "effect_result1"
							--self:PlayJingJieBgm("ui_dujie_yanwu")
							-- AudioManager.PlayAndForget(ResPath.UiseRes("ui_dujie_yanwu"))
						else
							str = "effect_result0"
						end
						-- self.node_list[str]:SetActive(true)
					end
				end, delay_time)
			end
			coroutine.yield()
		end
		self:SwitchBreakResult(true)
	end)
	coroutine.resume(jj_co)
end

function GodBodyDuJieView:PlayLeiYunBGM()
	local bundle, asset = ResPath.UiseRes("ui_dujie_leiyun")
	AudioManager.Play(bundle, asset, nil, nil, function(player)
		self.jj_leiyun_bgm = player
	end, nil, true)
end

function GodBodyDuJieView:PlayHpCount(hp_value, cur_thunder_time)
	CountDownManager.Instance:AddCountDown("jj_thunder", BindTool.Bind(self.UpdateHpBar, self, hp_value, self.hp_bar),
			BindTool.Bind(self.CompleteHpBar, self), nil, cur_thunder_time, 0.02)
	if self.node_list["dujie_slider_hp"] then
		-- local num = self.node_list["dujie_slider_hp"].slider.value
		-- self.node_list["dujie_slider_hp"].slider.value = num - hp_value/100
		self.cur_hp = self.cur_hp - hp_value
		local cur_hp_value = self.cur_hp > 0 and self.cur_hp or 0
		self.node_list["txt_hp_per"].text.text = cur_hp_value .. "%"
	end
end

function GodBodyDuJieView:UpdateHpBar(hp, hp_bar, el_time, to_time)
	if self.node_list["dujie_slider_hp"] then
		local val = el_time/to_time * hp / 100
		hp_bar = hp_bar or 1
		self.node_list["dujie_slider_hp"].image.fillAmount = hp_bar - val
	end
end

function GodBodyDuJieView:CompleteHpBar()
	GlobalTimerQuest:AddDelayTimer(function()
		if self.node_list and self.node_list.dujie_slider_hp then
			-- self.node_list["dujie_slider_hp"].slider.value = self.cur_hp/100 - 0.01
			self.hp_bar = self.cur_hp/100
			coroutine.resume(jj_co)
		end
	end,0)
end

function GodBodyDuJieView:PlayThunder(i, thunder_num)
	local delay_time = 0.3
	if i ~= thunder_num then
		-- local thunder_effect_type = GameMath.Rand(1,4)
		-- delay_time = JingJieWGData.Instance:GetThunderHitTime(thunder_effect_type)
		local thunder_effect_type = 1
		self:AsyncBreakEffect("effects2/prefab/ui/ui_dujie_shandian0" .. thunder_effect_type .. "_prefab","UI_dujie_shandian0" .. thunder_effect_type,true)
		-- self:PlayJingJieBgm("ui_dujie_shandian")
		-- self:AsyncBreakEffect("effects2/prefab/ui_x_prefab","UI_huoqiu_dujie",true)
	else
		-- delay_time = JingJieWGData.Instance:GetThunderHitTime(5)
		self:AsyncBreakEffect("effects2/prefab/ui/ui_dujie_shandian_zise_prefab","UI_dujie_shandian_zise", true)
		-- self:AsyncBreakEffect("effects2/prefab/ui_x_prefab","UI_huoqiu_dujie",true)
		-- self:PlayJingJieBgm("ui_dujie_shandian_zise")
	end
	return delay_time
end

function GodBodyDuJieView:AsyncBreakEffect(bundle, asset, is_thunder, x, y)
	if not self.effect_list then
		self.effect_list = {}
	end
	if self.effect_list[asset] then
		if is_thunder then
			local rot_z = GameMath.Rand(-50,50)
			-- if rot_z > 0 then
			-- 	self.node_list["effct_lei"].transform.localScale = Vector3(-1,1,1)
			-- else
			-- 	self.node_list["effct_lei"].transform.localScale = Vector3(1,1,1)
			-- end
			self.node_list["effct_lei"].transform.localRotation = Quaternion.Euler(0, 0, rot_z)
		end
		self.effect_list[asset]:SetActive(false)
		self.effect_list[asset]:SetActive(true)
		return
	end

	--容错判空
	if not self.node_list["effect_node"] then
		return
	end

	local loader = AllocAsyncLoader(self, asset)
	loader:SetParent(self.node_list["effect_node"].transform)
	loader:SetIsUseObjPool(true)
	loader:SetLoadPriority(ResLoadPriority.mid)
	loader:Load(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end
		local node = is_thunder and self.node_list["effct_lei"] or self.node_list["effct_yun"]
		obj.transform:SetParent(node.transform, false)
		if x ~= nil and y ~= nil then
			obj.transform.anchoredPosition = Vector3(x,y,0)
		end
		self.effect_list[asset] = obj
	end)
end

function GodBodyDuJieView:PlayJingJieBgm(str)
	if self.audio_play then
		AudioManager.StopAudio(self.audio_play)
	end

	local bundle, asset = ResPath.UiseRes(str)
	AudioManager.Play(bundle, asset, nil, nil, function(player)
		self.audio_play = player
	end)
end
