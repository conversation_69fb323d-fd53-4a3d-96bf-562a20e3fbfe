-- K-跨服刺探.xls
local item_table={
[1]={item_id=29476,num=7,is_bind=1},
[2]={item_id=26148,num=2,is_bind=1},
[3]={item_id=26149,num=2,is_bind=1},
[4]={item_id=29476,num=8,is_bind=1},
[5]={item_id=26148,num=3,is_bind=1},
[6]={item_id=29476,num=9,is_bind=1},
[7]={item_id=26149,num=3,is_bind=1},
[8]={item_id=29477,num=1,is_bind=1},
[9]={item_id=26422,num=2,is_bind=1},
[10]={item_id=26423,num=2,is_bind=1},
[11]={item_id=29476,num=4,is_bind=1},
[12]={item_id=56316,num=8,is_bind=1},
[13]={item_id=56317,num=2,is_bind=1},
[14]={item_id=56316,num=12,is_bind=1},
[15]={item_id=56316,num=16,is_bind=1},
[16]={item_id=57837,num=2,is_bind=1},
[17]={item_id=56317,num=4,is_bind=1},
[18]={item_id=56316,num=20,is_bind=1},
[19]={item_id=57837,num=3,is_bind=1},
[20]={item_id=56317,num=6,is_bind=1},
[21]={item_id=56316,num=24,is_bind=1},
[22]={item_id=29476,num=10,is_bind=1},
[23]={item_id=56317,num=3,is_bind=1},
[24]={item_id=26148,num=5,is_bind=1},
[25]={item_id=26149,num=5,is_bind=1},
[26]={item_id=29476,num=6,is_bind=1},
[27]={item_id=29476,num=5,is_bind=1},
[28]={item_id=27741,num=40,is_bind=1},
[29]={item_id=27741,num=30,is_bind=1},
[30]={item_id=27741,num=20,is_bind=1},
[31]={item_id=27741,num=10,is_bind=1},
[32]={item_id=29476,num=3,is_bind=1},
[33]={item_id=26421,num=1,is_bind=1},
[34]={item_id=26425,num=1,is_bind=1},
[35]={item_id=29476,num=2,is_bind=1},
[36]={item_id=56316,num=4,is_bind=1},
[37]={item_id=56317,num=1,is_bind=1},
[38]={item_id=56316,num=6,is_bind=1},
[39]={item_id=57837,num=1,is_bind=1},
[40]={item_id=56316,num=10,is_bind=1},
[41]={item_id=27741,num=5,is_bind=1},
[42]={item_id=26148,num=4,is_bind=1},
[43]={item_id=26149,num=4,is_bind=1},
[44]={item_id=56316,num=2,is_bind=1},
}

return {
enter_other_gs={
{}
},

enter_other_gs_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
open_time={
{}
},

open_time_meta_table_map={
},
npc_info={
{show_map_name="铜弥",information_type="1|2",information_pro="6|4",gather_time=5000,npc_talk="铜弥龙穴，可获得<color=#3be0f9>低品龙蛋</color>、<color=#b759fa>普通龙蛋</color>（采集并带回高级、史诗龙蛋可以在破坏阶段召唤帮手啊）",},
{npc_id=10377,show_map_name="翡翠",npc_name="蓝紫橙龙蛋点",pos_x=177,pos_y=266,information_type="1|2|3",information_pro="400|267|333",npc_talk="翡翠龙穴，可获得<color=#3be0f9>低品龙蛋</color>、<color=#b759fa>普通龙蛋</color>、<color=#ff6000>高级龙蛋</color>（采集并带回高级、史诗龙蛋可以在破坏阶段召唤帮手啊）",},
{npc_id=10378,npc_name="紫橙红龙蛋点1",pos_x=136,pos_y=318,npc_talk="金骸龙穴，可获得<color=#b759fa>普通龙蛋</color>、<color=#ff6000>高级龙蛋</color>、<color=#ec0228>史诗龙蛋</color>（采集并带回高级、史诗龙蛋可以在破坏阶段召唤帮手啊）",},
{npc_id=10379,npc_name="紫橙红龙蛋点2",pos_x=134,pos_y=259,gather_time=15000,information_num=2,},
{npc_id=10380,npc_name="紫橙红龙蛋点3",pos_x=125,pos_y=150,gather_time=15000,information_num=3,}
},

npc_info_meta_table_map={
},
information_reward={
{},
{information_type=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},kill_add_score=300,realive_add_score=300,information_name="普通龙蛋",num=2,attacker_add_score=450,image_id=5,},
{information_type=3,reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[3]},kill_add_score=400,realive_add_score=400,information_name="高级龙蛋",num=4,attacker_add_score=600,image_id=6,},
{information_type=4,information_num=3,reward_item={[0]=item_table[6],[1]=item_table[5],[2]=item_table[7],[3]=item_table[8]},kill_add_score=600,realive_add_score=600,information_name="史诗龙蛋",num=8,attacker_add_score=1000,image_id=9,}
},

information_reward_meta_table_map={
},
defense_reward={
{},
{index=2,need_score=500,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12]},},
{index=3,need_score=700,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[13],[4]=item_table[14]},},
{index=4,need_score=900,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[13],[4]=item_table[15]},},
{index=5,need_score=1100,reward_item={[0]=item_table[16],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[17],[5]=item_table[18]},},
{index=6,need_score=1500,reward_item={[0]=item_table[19],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[20],[5]=item_table[21]},}
},

defense_reward_meta_table_map={
},
disguise={
{},
{disguise_type=2,consume_item_id=46052,npc_name="炎黄卫兵",},
{disguise_type=3,consume_item_id=46053,npc_name="木箱",}
},

disguise_meta_table_map={
},
information_color={
{},
{index=2,npc_id=10377,information_type=3,},
{index=3,npc_id=10378,information_type=4,}
},

information_color_meta_table_map={
},
attack_rank_reward={
{reward_item={[0]=item_table[22],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},},
{rank_index=2,reward_item={[0]=item_table[6],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},},
{rank_index=3,reward_item={[0]=item_table[4],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},},
{rank_index=4,reward_item={[0]=item_table[1],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},},
{rank_index=5,reward_item={[0]=item_table[26],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},},
{rank_index=6,reward_item={[0]=item_table[27],[1]=item_table[13],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},},
{rank_index=7,reward_item={[0]=item_table[11],[1]=item_table[13],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},},
{rank_index=8,},
{rank_index=9,},
{rank_index=10,}
},

attack_rank_reward_meta_table_map={
},
defense_rank_reward={
{reward_item={[0]=item_table[19],[1]=item_table[28],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25],[6]=item_table[8]},},
{rank_index=2,reward_item={[0]=item_table[19],[1]=item_table[29],[2]=item_table[4],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25],[6]=item_table[8]},},
{rank_index=3,reward_item={[0]=item_table[19],[1]=item_table[30],[2]=item_table[1],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25],[6]=item_table[8]},},
{rank_index=4,reward_item={[0]=item_table[16],[1]=item_table[31],[2]=item_table[26],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25],[6]=item_table[8]},},
{rank_index=5,},
{rank_index=6,},
{rank_index=7,},
{rank_index=8,},
{rank_index=9,reward_item={[0]=item_table[16],[1]=item_table[31],[2]=item_table[32],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25],[6]=item_table[8]},},
{rank_index=10,},
{rank_index=11,},
{rank_index=12,},
{rank_index=13,},
{rank_index=14,},
{rank_index=15,},
{rank_index=16,},
{rank_index=17,},
{rank_index=18,},
{rank_index=19,},
{rank_index=20,}
},

defense_rank_reward_meta_table_map={
[8]=9,	-- depth:1
[7]=8,	-- depth:2
[6]=7,	-- depth:3
[5]=6,	-- depth:4
[10]=5,	-- depth:5
},
accept_call_reward={
{}
},

accept_call_reward_meta_table_map={
},
spy_boss={
{}
},

spy_boss_meta_table_map={
},
spy_boss_attack={
{},
{id=2,attack_boss_id=5502,attack_boss_info="166,274",},
{id=3,attack_boss_id=5503,attack_boss_info="170,280",},
{id=4,attack_boss_id=5504,attack_boss_info="170,274",},
{need_number=10,attack_boss_info="152,290",},
{need_number=10,attack_boss_info="147,288",},
{need_number=10,attack_boss_info="153,295",},
{need_number=10,attack_boss_info="148,293",},
{need_number=20,attack_boss_info="151,263",},
{need_number=20,attack_boss_info="146,266",},
{need_number=20,attack_boss_info="144,260",},
{need_number=20,attack_boss_info="149,257",}
},

spy_boss_attack_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[10]=2,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:1
},
attack_reward={
{},
{index=2,need_score=1000,reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[35],[3]=item_table[36]},},
{index=3,need_score=1500,reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[35],[3]=item_table[37],[4]=item_table[38]},},
{index=4,need_score=2000,reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[35],[3]=item_table[37],[4]=item_table[12]},},
{index=5,need_score=3000,reward_item={[0]=item_table[39],[1]=item_table[33],[2]=item_table[34],[3]=item_table[35],[4]=item_table[13],[5]=item_table[40]},},
{index=6,need_score=5000,reward_item={[0]=item_table[39],[1]=item_table[33],[2]=item_table[34],[3]=item_table[35],[4]=item_table[23],[5]=item_table[14]},}
},

attack_reward_meta_table_map={
},
enter_other_gs_default_table={enter_level=200,enter_scene_id=1003,task_scene_id=1003,enter_pos_x=43,enter_pos_y=156,national_relive_pos="91,239",non_national_relive_pos="104,407",protect_buff_id=1005,back_scene_id=1003,back_pos_x=43,back_pos_y=156,cross_bgm=1102,ui_level=200,},

other_default_table={spy_task_id=51000,task_count=3,add_task_npc=10375,portal_pos_x=43,portal_pos_y=156,call_cd_time=60,reward_item={[0]=item_table[27]},npc_talk="前往敌国主城刺探龙蛋，将龙蛋带回我这里，<color=#ec0228>品质越高的龙蛋奖励越好</color>，刺探时间为每日的<color=#95d12b>10:00~24:00</color>",kill_score=100,},

open_time_default_table={is_open=1,start_time=1600,end_time=1630,server_count=4,spy_boss_start_time=1615,spy_boss_end_time=1630,},

npc_info_default_table={npc_id=10376,show_map_name="金骸",npc_name="蓝紫龙蛋点",pos_x=163,pos_y=221,information_type="2|3|4",information_pro="25|25|50",gather_time=10000,information_num=1,scene_id=1003,npc_talk="前往第一个金骸龙穴获得<color=#ec0228>史诗龙蛋</color>碎片，再来找我吧！",},

information_reward_default_table={information_type=1,information_num=1,reward_item={[0]=item_table[26],[1]=item_table[2],[2]=item_table[3]},kill_add_score=200,realive_add_score=200,information_name="低品龙蛋",num=1,attacker_add_score=300,image_id=7,},

defense_reward_default_table={index=1,need_score=300,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[36]},},

disguise_default_table={disguise_type=1,consume_item_id=46051,consume_item_num=1,res_id=2010001,res_type=1,npc_name="猫咪",},

information_color_default_table={index=1,npc_id=10376,information_type=2,},

attack_rank_reward_default_table={rank_index=1,reward_item={[0]=item_table[32],[1]=item_table[13],[2]=item_table[24],[3]=item_table[25],[4]=item_table[8]},title_id=0,},

defense_rank_reward_default_table={rank_index=1,reward_item={[0]=item_table[39],[1]=item_table[41],[2]=item_table[32],[3]=item_table[37],[4]=item_table[24],[5]=item_table[25],[6]=item_table[8]},title_id=0,},

accept_call_reward_default_table={accept_call_max_count=1,reward_item={[0]=item_table[35]},},

spy_boss_default_table={boss_id=5500,boss_pos="155,278",attack_skill_id="500|501",defend_skill_id="502|503",relive_buff_id=220,gather_buff_id=221,boss_hp_check_time=3,boss_hp_box="30:44317:10:12823|40:44317:10:12823|50:44317:10:12823|60:44317:10:12823|70:44317:10:12823|80:44317:10:12823|90:44317:10:12823",box_reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[3]},boss_hp_box_pos="141,291|150,288|156,290|141,283|148,286|157,287|141,279|139,273|138,268|144,265|151,262|164,270|170,271|174,270|179,273|176,280|165,283|179,283|160,289|137,260|151,258|154,264|173,261|144,260|142,253|148,254|139,259|143,250",boss_hp_score="30:10000|50:30000|70:50000|90:80000",box_time=3,box_duration_time=5,get_box_max_count=10,attack_boss_add_score=2,area_add_score_time=3,area_add_score=10,area_add_score_pos="136,289|136,266|176,266|176,289",success_reward_item1={[0]=item_table[19],[1]=item_table[22],[2]=item_table[23],[3]=item_table[24],[4]=item_table[25],[5]=item_table[8]},success_reward_item2={[0]=item_table[16],[1]=item_table[4],[2]=item_table[23],[3]=item_table[24],[4]=item_table[25],[5]=item_table[8]},success_reward_item3={[0]=item_table[16],[1]=item_table[1],[2]=item_table[13],[3]=item_table[42],[4]=item_table[43],[5]=item_table[8]},fail_reward_item={[0]=item_table[16],[1]=item_table[27],[2]=item_table[13],[3]=item_table[5],[4]=item_table[7],[5]=item_table[8]},boss_kill_reward_item={[0]=item_table[32],[1]=item_table[37],[2]=item_table[5],[3]=item_table[7],[4]=item_table[8]},skill_use_item="500,26580,1,1|501,26581,1,1|502,26582,1,1|503,26583,1,1",boss_scene_id=1003,},

spy_boss_attack_default_table={need_number=5,id=1,attack_boss_id=5501,attack_boss_info="166,280",defend_add_score=2000,},

attack_reward_default_table={index=1,need_score=600,reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[35],[3]=item_table[44]},}

}

