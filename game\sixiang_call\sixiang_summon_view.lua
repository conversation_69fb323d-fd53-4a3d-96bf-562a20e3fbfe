-------------------------------
-- 四象召唤
-------------------------------
function SiXiangCallView:InitSummonView()
    self:InitSummonParam()
    self:InitSummonListener()
    self:InitSummonItemList()
    self:InitOneSummonConst()
    self:InitTenSummonConst()
    self:InitSummonDanmu()
    self:InitSummonChangeItem()
end

function SiXiangCallView:ReleaseSummonView()
    if self.sixiang_item_list then
        for k,v in pairs(self.sixiang_item_list) do
            v:DeleteMe()
        end
        self.sixiang_item_list = nil
    end

    if self.summon_tedian_item_list then
        for k,v in pairs(self.summon_tedian_item_list) do
            v:DeleteMe()
        end
        self.summon_tedian_item_list = nil
    end

    if self.summon_danmu_item_list then
        local item_list = self.summon_danmu_item_list
        for i=1,#item_list do
            for k,v in pairs(item_list[i]) do
                v:DeleteMe()
            end
        end
        self.summon_danmu_item_list = nil
    end

    if self.summon_danmu_timer then
        GlobalTimerQuest:CancelQuest(self.summon_danmu_timer)
        self.summon_danmu_timer = nil
    end

    self.summon_change_item_list = nil

    self:InitSummonParam()
    CountDownManager.Instance:RemoveCountDown(self.summon_count_down_key)
end

function SiXiangCallView:InitSummonParam()
    self.summon_single_item = nil
    self.summon_multi_item = nil
    self.summon_single_const_id_list = {}
    self.summon_multi_const_id_list = {}
    self.summon_change_item_list = {}
    self.summon_count_down_key = "sixiang_summon_count_down"
    self.summon_tedian_item_list = {}
    self.summon_tedian_is_open = false
    self.summon_danmu_index = 1
    self.summon_type = -1
    self.summon_is_draw_cd = false
    self.summon_change_mark = true
end

function SiXiangCallView:InitSummonListener()
    XUI.AddClickEventListener(self.node_list.sx_tip_btn, BindTool.Bind1(self.OnClickSummonTipBtn, self))
    XUI.AddClickEventListener(self.node_list.sx_record_btn, BindTool.Bind1(self.OnClickSummonRecordBtn, self))
    XUI.AddClickEventListener(self.node_list.sx_sixiang_btn, BindTool.Bind1(self.OnClickSummonSiXiangBtn, self))
    XUI.AddClickEventListener(self.node_list.sx_reward_pond_btn, BindTool.Bind1(self.OnClickSummonRewardPondBtn, self))
    XUI.AddClickEventListener(self.node_list.sx_one_call_btn, BindTool.Bind(self.OnClickSummonBtn, self, OP_YSZH_DRAW_TYPE.SINGLE))
    XUI.AddClickEventListener(self.node_list.sx_ten_call_btn, BindTool.Bind(self.OnClickSummonBtn, self, OP_YSZH_DRAW_TYPE.MULTI))
    XUI.AddClickEventListener(self.node_list.one_call_const_img, BindTool.Bind(self.OnClickSummonConstImg, self, OP_YSZH_DRAW_TYPE.SINGLE))
    XUI.AddClickEventListener(self.node_list.ten_call_const_img, BindTool.Bind(self.OnClickSummonConstImg, self, OP_YSZH_DRAW_TYPE.MULTI))
    XUI.AddClickEventListener(self.node_list.exchange_btn, BindTool.Bind1(self.OnClickSummonExchangeBtn, self))
    XUI.AddClickEventListener(self.node_list.danmu_toggle, BindTool.Bind(self.OnClickSummonDanmuToggle, self))
	XUI.AddClickEventListener(self.node_list.probability_show_btn, BindTool.Bind(self.OpenGaiLvView, self))

    self.node_list.sixiang_btn_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.FightSoul_Bone) > 0)
end

function SiXiangCallView:InitSummonItemList()
    local res_async_loader = AllocResAsyncLoader(self, "sixiang_item_list")
    res_async_loader:Load("uis/view/sixiang_call_prefab", "sixiang_call_render", nil,
        function(new_obj)
            local item_list = {}
            for i = 1, 6 do
                local root = self.node_list["sx_item_root_" .. i]
                if root then
                    local obj = ResMgr:Instantiate(new_obj)
                    obj.transform:SetParent(root.transform, false)
                    item_list[i] = SiXiangSummonItem.New(obj)
                    item_list[i]:SetIndex(i)
                    item_list[i]:SetIsShowTips(true)
                end
            end
            self.sixiang_item_list = item_list
            self:FlushSummonItemList()
        end)
end

function SiXiangCallView:InitSummonChangeItem()
    local change_item_list = {}
    for i = 1, 4 do
        change_item_list[i] = self.node_list["sx_change_item_" .. i]
        change_item_list[i].toggle:AddClickListener(BindTool.Bind(self.OnClickSummonTypeChangeToggle, self, i))
    end
    self.summon_change_item_list = change_item_list
    self:FlushSummonChangeList()
end

function SiXiangCallView:FlushSummonItemList()
    if not self.sixiang_item_list then
        return
    end
    local show_list = SiXiangCallWGData.Instance:GetSiXiangSummonItemList()
    local item_list = self.sixiang_item_list
    for i=1,#item_list do
        item_list[i]:SetData(show_list[i])
    end
end

function SiXiangCallView:ShowSummonView()
    if self.summon_type > 0 then
        local summon_type = SiXiangCallWGData.Instance:GetSiXiangSummonType()
        if self.summon_type ~= summon_type then
            self:Flush(TabIndex.sixiang_call_sx, "summon_type_change")
        end
    end
    -- self:FlushSummonDanmu()
end

function SiXiangCallView:FlushSummonView(param_t)
    ---[[ 获取途径跳转类型
    local to_ui_param = CheckList(param_t, "all", "to_ui_param")
    if to_ui_param then
        local summon_type = tonumber(to_ui_param)
        if summon_type and summon_type > 0 and summon_type <= 4 then
            SiXiangCallWGData.Instance:SetSiXiangSummonType(summon_type)
        end
    end
    --]]
    for k,v in pairs(param_t) do
        if k == "all" and not to_ui_param then
            self:FlushOneSummonConst()
            self:FlushTenSummonConst()
            self:FlushSummonTeDianPanel()
        elseif k == "summon_type_change" or to_ui_param then
            self:InitOneSummonConst()
            self:InitTenSummonConst()
            self:FlushOneSummonConst()
            self:FlushTenSummonConst()
            self:FlushSummonItemList()
            self:FlushSummonTeDianPanel()
            self:FlushSummonChangeList()
        elseif k == "sever_log" then
            -- self:FlushSummonDanmu()
        end
    end
end

function SiXiangCallView:OnClickSummonTipBtn()
    RuleTip.Instance:SetContent(Language.SiXiangCall.TipContent, Language.SiXiangCall.TipTitle)
end

function SiXiangCallView:OnClickSummonRecordBtn()
    ViewManager.Instance:Open(GuideModuleName.SiXiangCallRecord)
end

function SiXiangCallView:OnClickSummonSiXiangBtn()
    ViewManager.Instance:Open(GuideModuleName.FightSoulView, TabIndex.fight_soul_bone, nil, {select_type = self.summon_type})
end

function SiXiangCallView:OnClickSummonExchangeBtn()
    ViewManager.Instance:Open(GuideModuleName.SiXiangExchange)
end

function SiXiangCallView:OnClickSummonRewardPondBtn()
    ViewManager.Instance:Open(GuideModuleName.SiXiangRewardPond)
end

function SiXiangCallView:OnClickSummonDanmuToggle(is_on)
    self:FlushSummonDanmu()
end

function SiXiangCallView:OnClickSummonTypeChangeToggle(index)
    local summon_type = SiXiangCallWGData.Instance:GetSiXiangSummonType()
    if index ~= summon_type then
        SiXiangCallWGData.Instance:SetSiXiangSummonType(index)
    end

    self.summon_type = summon_type
    self:Flush(TabIndex.sixiang_call_sx, "summon_type_change")
end

---[[ 抽奖相关
function SiXiangCallView:OnClickSummonBtn(_type)
    if self.summon_is_draw_cd then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.DrawCdTips)
        return
    end
    self.summon_is_draw_cd = true
    ReDelayCall(self, function()
        self.summon_is_draw_cd = false
    end, 1, "OnClickSummonBtn")

    if _type == OP_YSZH_DRAW_TYPE.SINGLE then
        local summon_single_item = self.summon_single_item
        if summon_single_item then
            SiXiangCallWGCtrl.Instance:SiXiangSummon(summon_single_item.item_id, summon_single_item.num, summon_single_item.draw_type)
        end
    elseif _type == OP_YSZH_DRAW_TYPE.MULTI then
        local summon_multi_item = self.summon_multi_item
        if summon_multi_item then
            SiXiangCallWGCtrl.Instance:SiXiangSummon(summon_multi_item.item_id, summon_multi_item.num, summon_multi_item.draw_type)
        end
    end
end

function SiXiangCallView:OnClickSummonConstImg(_type)
    local item_id = 0
    if _type == OP_YSZH_DRAW_TYPE.SINGLE then
        item_id = self.summon_single_item and self.summon_single_item.item_id
    elseif _type == OP_YSZH_DRAW_TYPE.MULTI then
        item_id = self.summon_multi_item and self.summon_multi_item.item_id
    end
    if item_id > 0 then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
    end
end

function SiXiangCallView:GetSummonSkipToggleState()
    if self.node_list.sx_skip_btn then
        return self.node_list.sx_skip_btn.toggle.isOn
    end
end
--]]

---[[ 消耗道具显示相关
function SiXiangCallView:InitOneSummonConst()
    local summon_single_item = SiXiangCallWGData.Instance:GetSiXiangSummonConst(OP_YSZH_DRAW_TYPE.SINGLE)

    ---[[ 通用的单抽
    local nor_single_item = SiXiangCallWGData.Instance:GetSiXiangSummonConst(OP_YSZH_DRAW_TYPE.NOR_SINGLE)
    if nor_single_item then
        self.summon_single_const_id_list[nor_single_item.item_id] = true
        local single_has_num = ItemWGData.Instance:GetItemNumInBagById(summon_single_item and summon_single_item.item_id)
        local nor_has_num = ItemWGData.Instance:GetItemNumInBagById(nor_single_item.item_id)
        if single_has_num <= 0 and nor_has_num > 0 then
            summon_single_item = nor_single_item
        end
    end
    --]]

    if summon_single_item then
        local item_cfg = ItemWGData.Instance:GetItemConfig(summon_single_item.item_id)
        local bundle, asset = ResPath.GetItem(item_cfg and item_cfg.icon_id)
        self.node_list.one_call_const_img.image:LoadSprite(bundle, asset)
    end

    self.summon_single_item = summon_single_item
end

function SiXiangCallView:InitTenSummonConst()
    ---[[ 10抽普通消耗
    local summon_multi_item = SiXiangCallWGData.Instance:GetSiXiangSummonConst(OP_YSZH_DRAW_TYPE.MULTI)
    -- local multi_has_num = ItemWGData.Instance:GetItemNumInBagById(summon_multi_item.item_id)
    --]]

    ---[[ 通用10抽普通消耗
    local nor_multi_item = SiXiangCallWGData.Instance:GetSiXiangSummonConst(OP_YSZH_DRAW_TYPE.NOR_MULTI)
    if nor_multi_item then
        self.summon_multi_const_id_list[nor_multi_item.item_id] = true
        local nor_has_num = ItemWGData.Instance:GetItemNumInBagById(nor_multi_item.item_id)
        if nor_has_num >= nor_multi_item.num then
            summon_multi_item = nor_multi_item
        end
    end
    --]]

    ---[[ 通用10抽特殊消耗
    local nor_special_item = SiXiangCallWGData.Instance:GetSiXiangSummonConst(OP_YSZH_DRAW_TYPE.NOR_USE_MULTI_ITEM)
    if nor_special_item then
        self.summon_multi_const_id_list[nor_special_item.item_id] = true
        local nor_special_num = ItemWGData.Instance:GetItemNumInBagById(nor_special_item.item_id)
        if nor_special_num >= nor_special_item.num then
            summon_multi_item = nor_special_item
        end
    end
    --]]

    ---[[ 特殊的十连召唤符
    local special_item = SiXiangCallWGData.Instance:GetSiXiangSummonConst(OP_YSZH_DRAW_TYPE.USE_MULTI_ITEM)
    if special_item then
        self.summon_multi_const_id_list[special_item.item_id] = true
        local special_num = ItemWGData.Instance:GetItemNumInBagById(special_item.item_id)
        if special_num >= special_item.num then
            summon_multi_item = special_item
        end
    end
    --]]

    if summon_multi_item then
        local item_cfg = ItemWGData.Instance:GetItemConfig(summon_multi_item.item_id)
        local bundle, asset = ResPath.GetItem(item_cfg and item_cfg.icon_id)
        self.node_list.ten_call_const_img.image:LoadSprite(bundle, asset)
    end

    ---[[ 折扣显示
    if summon_multi_item and summon_multi_item.num > 1 then
        local multi_lotto = SiXiangCallWGData.Instance:GetSiXiangSummonOtherCfg("multi_lotto")
        if multi_lotto then
            local discount = math.floor(summon_multi_item.num / multi_lotto * 10)
            self.node_list.ten_discount_bg:SetActive(discount < 10)
            self.node_list.ten_call_dis_lbl.text.text = string.format(Language.SiXiangCall.DiscountText, discount)
        end
    else
        self.node_list.ten_discount_bg:SetActive(false)
    end
    --]]

    self.summon_multi_item = summon_multi_item
end

function SiXiangCallView:FlushSummonConst(change_item_id)
    if self.summon_multi_const_id_list[change_item_id] then
        self:InitTenSummonConst()
        self:FlushTenSummonConst()
    elseif self.summon_multi_item and change_item_id == self.summon_multi_item.item_id then
         self:FlushTenSummonConst()
    end
    if self.summon_single_const_id_list[change_item_id] then
        self:InitOneSummonConst()
        self:FlushOneSummonConst()
    elseif self.summon_single_item and change_item_id == self.summon_single_item.item_id then
        self:FlushOneSummonConst()
    end
end

function SiXiangCallView:FlushOneSummonConst()
    if self.summon_single_item then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(self.summon_single_item.item_id)
        local need_num = self.summon_single_item.num or 0
        local str = string.format("%d/%d", has_num, need_num)
        local str_color = has_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list.one_call_const_lbl.text.text = ToColorStr(str, str_color)
    end
end

function SiXiangCallView:FlushTenSummonConst()
    if self.summon_multi_item then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(self.summon_multi_item.item_id)
        local need_num = self.summon_multi_item.num or 0
        local str = string.format("%d/%d", has_num, need_num)
        local str_color = has_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list.ten_call_const_lbl.text.text = ToColorStr(str, str_color)
        self.node_list.sx_ten_call_remind:SetActive(has_num >= need_num)
    end
end
--]]

---[[ 四象特典
function SiXiangCallView:FlushSummonTeDianPanel()
    local is_open_tedain = SiXiangTeDianWGData.Instance:GetIsOpenTeDian()
    self.summon_tedian_is_open = is_open_tedain
    self.node_list.tedian_summon_content:SetActive(is_open_tedain)
    if is_open_tedain then
        self:FlushSummonTeDianOpen()
    else
        self:FlushSummonTeDianClose()
    end
    self:FlushSummonTeDianTime()
end

function SiXiangCallView:FlushSummonTeDianOpen()
    local act_list = SiXiangTeDianWGData.Instance:GetShowActIdList()
    if IsEmptyTable(act_list) then
        return
    end

    self:CreateSummonTedianItem(#act_list)

    local item_list = self.summon_tedian_item_list
    for i=1,#item_list do
        item_list[i]:SetData({act_id = act_list[i]})
    end
end

function SiXiangCallView:FlushSummonTeDianClose()

end

function SiXiangCallView:CreateSummonTedianItem(need_item_num)
    if need_item_num > #self.summon_tedian_item_list then
        local item_list = self.summon_tedian_item_list
        local parent = self.node_list.tedian_summon_content
        for i = #item_list + 1, need_item_num do
            item_list[i] = SiXiangSummonTeDianItem.New()
            item_list[i]:DoLoad(parent)
        end
    end
end

function SiXiangCallView:FlushSummonTeDianTime()
    CountDownManager.Instance:RemoveCountDown(self.summon_count_down_key)
    local end_time = SiXiangTeDianWGData.Instance:GetTeDianEndTimeStamp()
    local sever_time = TimeWGCtrl.Instance:GetServerTime()
    if end_time <= sever_time then
        self.node_list.tedian_summon:SetActive(false)
        return
    end
    self.node_list.tedian_summon:SetActive(true)

    self:SummonTeDianUpdateCountDown(sever_time, end_time)

    CountDownManager.Instance:AddCountDown(
        self.summon_count_down_key,
        BindTool.Bind(self.SummonTeDianUpdateCountDown, self),
        BindTool.Bind(self.FlushSummonTeDianTime, self),
        end_time,
        nil,
        1
    )
end

function SiXiangCallView:SummonTeDianUpdateCountDown(elapse_time, total_time)
    local time_str = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
    time_str = ToColorStr(time_str, COLOR3B.DEFAULT_NUM)
    if self.summon_tedian_is_open then
        self.node_list.tedian_summon_time.text.text = string.format(Language.SiXiangCall.CloseTimeDesc, time_str)
    else
        self.node_list.tedian_summon_time.text.text = string.format(Language.SiXiangCall.OpenTimeDesc, time_str)
    end
end

function SiXiangCallView:SummonTeDianCompleteCountDown()
    SiXiangCallWGCtrl.Instance:SendSiXiangRequest(OP_YUAN_SHEN_ZHAO_HUAN_TYPE.INFO)
end
--]]

---[[ 弹幕相关
function SiXiangCallView:InitSummonDanmu()
    -- local is_ignore = RoleWGData.GetRolePlayerPrefsInt("yuanshen_danmu")
    self.node_list.danmu_toggle.toggle.isOn = false--not (is_ignore == 1)

    local parent = self.node_list.sx_danmu_root
    -- local with = self.node_list.sx_danmu_root.rect.sizeDelta.x
    local with = self.node_list.sx_danmu_root.rect.rect.width
    local item_list = {}
    local index = 1

    for i=1,4 do
        item_list[i] = {}
        for j=1,2 do
            local item = SiXiangDanMuItem.New()
            item:SetIndex(index)
            index = index + 1
            item:SetRectanglePos(0, -40 * (i - 1))
            item:SetRectangleWith(with)
            item:DoLoad(parent)
            item_list[i][j] = item
        end
    end

    self.summon_danmu_item_list = item_list
end

function SiXiangCallView:FlushSummonDanmu()
    local is_open_danmu = self.node_list.danmu_toggle.toggle.isOn
    if is_open_danmu then
        self:ShowSummonDanmu()
    else
        self:HideSummonDanmu()
    end
    self.node_list.sx_danmu_root:SetActive(is_open_danmu)
    local is_ignore = not is_open_danmu
    RoleWGData.SetRolePlayerPrefsInt("yuanshen_danmu", is_ignore and 1 or 0)
end

function SiXiangCallView:ShowSummonDanmu()
    if self.summon_danmu_timer ~= nil or IsEmptyTable(self.summon_danmu_item_list) then
        return
    end
    self:AddSummonDanmu()
end

function SiXiangCallView:HideSummonDanmu()
    if self.summon_danmu_timer then
        GlobalTimerQuest:CancelQuest(self.summon_danmu_timer)
        self.summon_danmu_timer = nil
    end
end

function SiXiangCallView:AddSummonDanmu()
    if self:GetShowIndex() ~= TabIndex.sixiang_call_sx then
        return
    end

    local danmu = SiXiangCallWGData.Instance:GetSiXiangDanmu()
    if not danmu then
        return
    end

    local item_list = self.summon_danmu_item_list
    local index = self.summon_danmu_index
    if not item_list[index] then
        index = 1
    end
    self.summon_danmu_index = index + 1

    if item_list[index] then
        for _,v in ipairs(item_list[index]) do
            if v:CanShowDanMu() then
                v:SetData(danmu)
                break
            end
        end
    end

    local delay_time = GameMath.Rand(50, 100) * 0.04
    self.summon_danmu_timer = GlobalTimerQuest:AddDelayTimer(function()
        self:AddSummonDanmu()
    end, delay_time)
end
--]]

function SiXiangCallView:FlushSummonChangeList()
    local summon_type = SiXiangCallWGData.Instance:GetSiXiangSummonType()
    local item_list = self.summon_change_item_list
    for i=1,#item_list do
        if i == summon_type and not item_list[i].toggle.isOn then
            item_list[i].toggle.isOn = true
        end
    end
end

function SiXiangCallView:OpenGaiLvView()
    local sixiang_type = SiXiangCallWGData.Instance:GetSiXiangSummonType()
    local info = SiXiangCallWGData.Instance:GetGaiLvInfo(sixiang_type)
    TipWGCtrl.Instance:OpenGaiLvShowView(info)
end

----------------------- 召唤特典 ----------------------------------

SiXiangSummonTeDianItem = SiXiangSummonTeDianItem or BaseClass(BaseRender)

function SiXiangSummonTeDianItem:__delete()
    self.act_name_list = nil
end

function SiXiangSummonTeDianItem:DoLoad(parent)
    self:LoadAsset("uis/view/sixiang_call_prefab", "sixiang_tedian_item", parent.transform)
end

function SiXiangSummonTeDianItem:LoadCallBack()
    local act_name_list = {}
    local act_name = ""
    for i=1,4 do
        act_name = "act_name_" .. i
        act_name_list[act_name] = self.node_list[act_name]
        act_name_list[act_name]:SetActive(false)
    end
    self.act_name_list = act_name_list

    XUI.AddClickEventListener(self.node_list.root_bg, BindTool.Bind1(self.OnClickTeDianItem, self))
end

function SiXiangSummonTeDianItem:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        self:SetVisible(false)
        return
    end
    self:SetVisible(true)

    local cfg_data = SiXiangTeDianWGData.Instance:GetClientShowCfg(data.act_id)
    if cfg_data then
        local asset, bundle = ResPath.GetSiXiangCallImg(cfg_data.item_bg)
        self.node_list.root_bg.image:LoadSprite(asset, bundle)
        self:FlushItemName(cfg_data.name, data.act_id)
    end
end

function SiXiangSummonTeDianItem:FlushItemName(name, act_id)
    for k,v in pairs(self.act_name_list) do
        v:SetActive(k == name)
    end

    -- if act_id == YuanShenZhaoHuanSubActId.ShiLian1 then
    --     self.node_list.shilain_label.text.text = Language.SiXiangCall.ShiLianThreeDiscount
    -- elseif act_id == YuanShenZhaoHuanSubActId.ShiLian2 then
    --     self.node_list.shilain_label.text.text = Language.SiXiangCall.ShiLianFiveDiscount
    -- end
end

function SiXiangSummonTeDianItem:OnClickTeDianItem()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    local cfg_data = SiXiangTeDianWGData.Instance:GetClientShowCfg(data.act_id)
    if cfg_data then
        ViewManager.Instance:Open(cfg_data.open_param)
    end
end

----------------------- 召唤弹幕 ----------------------------------

SiXiangDanMuItem = SiXiangDanMuItem or BaseClass(BaseRender)

function SiXiangDanMuItem:__init()
    self.rectangle_with = 0
    self.rectangle_pos_x = 0
    self.rectangle_pos_y = 0
    self.danmu_run_tween = nil
end

function SiXiangDanMuItem:__delete()
    if self.danmu_run_tween then
        self.danmu_run_tween:Kill()
        self.danmu_run_tween = nil
    end
end

function SiXiangDanMuItem:SetRectangleWith(With)
    self.rectangle_with = With
end

function SiXiangDanMuItem:SetRectanglePos(x, y)
    self.rectangle_pos_x = x
    self.rectangle_pos_y = y
end

function SiXiangDanMuItem:DoLoad(parent)
    self:LoadAsset("uis/view/sixiang_call_prefab", "sixiang_danmu_item", parent.transform)
end

function SiXiangDanMuItem:LoadCallBack()
    self:SetAnchoredPosition(self.rectangle_pos_x, self.rectangle_pos_y)
end

function SiXiangDanMuItem:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    local str = ""
    if item_cfg then
        str = string.format(Language.SiXiangCall.DamMuInfo, data.role_name, ITEM_COLOR[item_cfg.color], item_cfg.name)
    end
    self.node_list.danmu_lbl.text.text = str

    self:PlayRunTween()
end

function SiXiangDanMuItem:CanShowDanMu()
    return self.danmu_run_tween == nil
end

function SiXiangDanMuItem:PlayRunTween()
    if self.danmu_run_tween then
        return
    end
    local danmu_lbl_with = 700
    RectTransform.SetAnchoredPositionXY(self.node_list.danmu_lbl.rect, 0, 0)

    local tween_move = self.node_list.danmu_lbl.rect:DOAnchorPosX(-danmu_lbl_with - self.rectangle_with, 20)
    self.danmu_run_tween = tween_move
    tween_move:SetEase(DG.Tweening.Ease.Linear)
    tween_move:OnComplete(function ()
        self.danmu_run_tween = nil
    end)
end

---------------------------------------------------------------