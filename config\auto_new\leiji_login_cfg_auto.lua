-- L-累计登录.xls
local item_table={
[1]={item_id=26191,num=1,is_bind=1},
[2]={item_id=44073,num=1,is_bind=1},
[3]={item_id=26130,num=25,is_bind=1},
[4]={item_id=30447,num=10,is_bind=1},
[5]={item_id=26129,num=30,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
reward={
{},
{day=2,},
{day=3,},
{day=4,reward_item={[0]=item_table[1]},},
{day=5,reward_item={[0]=item_table[2]},},
{day=6,},
{day=7,}
},

reward_meta_table_map={
[7]=4,	-- depth:1
},
day_reward={
{},
{seq=1,need_day=7,reward_item={[0]=item_table[3]},}
},

day_reward_meta_table_map={
},
other_default_table={open_level=100,},

reward_default_table={day=1,reward_item={[0]=item_table[4]},},

day_reward_default_table={seq=0,need_day=3,reward_item={[0]=item_table[5]},}

}

