function MainUIView:TargetLoadCallBack(index, loaded_times)
	-- 找到要控制的变量
	self.show_target = self.node_list["TargetInfoPanel"]["TargetInfoPanel"]
	self.show_target:SetActive(false)
	self.target_level = self.node_list["Level"]
	self.target_name = self.node_list["Name"]
	self.boss_hp_count = self.node_list["TextBossHPCount"]
	self.hp_bar = self.node_list["HpBar"]
	self.hp_slider_value = self.node_list["RoleHPValue"]
	-- self.hp_slider_top = self.node_list["RoleHPTop"]
	self.boss_hp_bar = self.node_list["BossHpBar"]
	self.boss_hp_value = self.node_list["BossHPValue"]
	self.boss_hp_top_value = self.node_list["BossHPTop"]
	self.boss_hp_bottom_value = self.node_list["BossHPBottom"]
	self.img_back_ground = self.node_list["ImgBackGround"]
	self.img_while = self.node_list["ImgWhile"]
	-- self.img_bottom_fill = self.node_list["ImgBottomFill"]
	self.img_top_fill = self.node_list["ImgTopFill"]
	self.boss_angry = self.node_list["BossAngry"]
	self.boss_score = self.node_list["BossScore"]
	self.angry_value = self.node_list["AngryVaule"]
	self.score_value = self.node_list["ScoreValue"]
	self.portrait_label = self.node_list["PortraitLabel"]
	self.portrait = self.node_list["portrait"]
	local lp = self.node_list["portrait"].transform.localPosition
	local ls = self.node_list["portrait"].transform.localScale
	self.portrait_original_pos = Vector3(lp.x, lp.y, lp.z)
	self.portrait_original_scale = Vector3(ls.x, ls.y, ls.z)
	self.role_head_icon = self.node_list["role_head_icon"]
	self.custom_portrait = self.node_list["CustomIconImage"]
	self.team_type = self.node_list["teamtype"]
	self.tian_shen_break_shield = self.node_list["tian_shen_break_shield"]
	self.break_shield_value = self.node_list["BreakShieldValue"]
	self.boss_spe_tired = self.node_list["BossSpeTired"]
	-- self.left_sign = self.node_list["left_sign"]
	self.ts_bs_hurt_value = 0 
	self.ts_bs_hurt_clear_t = 0
	self.ts_bs_panel_effect_show = false
	self.is_show = false
	self.is_boss = false
	self.boss_hp_grey_effect_play_flag = false

	-- 监听系统事件
	self.show_ascription_event = GlobalEventSystem:Bind(MainUIEventType.SHOW_ASCRIPTION, BindTool.Bind(self.SetAscriptionText, self))  --归属掉落需要贯穿整个游戏生命周期
	self.be_select_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjHead, self))
	self.obj_delete_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DELETE, BindTool.Bind(self.OnObjDeleteHead, self))
	self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDeleteHead, self))
	self.target_hp_change_event = GlobalEventSystem:Bind(ObjectEventType.TARGET_HP_CHANGE, BindTool.Bind(self.OnTargetHpChangeHead, self))
	self.change_attack_mode_event = GlobalEventSystem:Bind(SettingEventType.Change_Attack_Mode, BindTool.Bind(self.OnChangeAttackMode, self))
	self.obj_hp_recover = GlobalEventSystem:Bind(ObjectEventType.HP_RECOVER, BindTool.Bind(self.OnTargetHpReCover, self))
	self.top_hp_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind(self.TopPanelAni, self))
	self.target_change_buff_list = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.FlushTargetBuffer, self))
	self.hudun_event = GlobalEventSystem:Bind(ObjectEventType.HuDun_Change, BindTool.Bind(self.OnHuDunEvent, self))
	self.remove_buff_event = GlobalEventSystem:Bind(ObjectEventType.REMOVE_BUFF, BindTool.Bind(self.OnRemoveBuffEvent, self))
	self.special_hudun_event = GlobalEventSystem:Bind(ObjectEventType.SPECIAL_SHIELD_CHANGE, BindTool.Bind(self.OnSpecialHuDunEvent, self))
	self.obj_die_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind(self.MainRoleDie,self))

	XUI.AddClickEventListener(self.portrait, BindTool.Bind(self.OnClickHead, self))
	XUI.AddClickEventListener(self.role_head_icon, BindTool.Bind(self.OnClickHead, self))
	XUI.AddClickEventListener(self.custom_portrait, BindTool.Bind(self.OnClickHead, self))
	-- XUI.AddClickEventListener(self.left_sign, BindTool.Bind(self.OnClickHead, self))
    XUI.AddClickEventListener(self.node_list["boss_head"], BindTool.Bind(self.OnClickBossHead, self))
    XUI.AddClickEventListener(self.node_list["boss_xianli"], BindTool.Bind(self.OnClickBossXianli, self))
    XUI.AddClickEventListener(self.node_list["boss_reward_btn"], BindTool.Bind(self.OnClickBossReward, self))
	XUI.AddClickEventListener(self.node_list["btn_show_hp_info"], BindTool.Bind(self.OnClickShowHpInfo, self))
	self:CreateTargerBuffList()

	self:OnSelectObjHead(SceneObj.select_obj, SceneTargetSelectType.SELECT)
	self.target_info_shake_root_original_y = self.node_list["TargetInfoShakeRoot"].rect.anchoredPosition.y
	self.emeny_role_head_cell = BaseHeadCell.New(self.node_list["role_head_icon"])
end

function MainUIView:Target__ReleaseCallBack()
	self.show_target = nil
	self.target_level = nil
	self.target_name = nil
	self.boss_hp_count = nil
	self.hp_bar = nil
	self.hp_slider_value = nil
	-- self.hp_slider_top = nil
	self.boss_hp_bar = nil
	self.boss_hp_value = nil
	self.boss_hp_top_value = nil
	self.boss_hp_bottom_value = nil
	self.img_back_ground = nil
	self.img_while = nil
	-- self.img_bottom_fill = nil
	self.img_top_fill = nil
	self.portrait_label = nil
	self.portrait = nil
	self.role_head_icon = nil
	self.boss_portrait_label = nil
	self.boss_angry = nil
	self.boss_score = nil
	self.angry_value = nil
	self.score_value = nil
	self.custom_portrait = nil
	self.team_type = nil
	self.tian_shen_break_shield = nil
	self.break_shield_value = nil
	self.boss_spe_tired = nil
	self.ts_bs_hurt_value = 0 
	self.ts_bs_hurt_clear_t = 0
	self.ts_bs_panel_effect_show = false

	if self.be_select_event then
		GlobalEventSystem:UnBind(self.be_select_event)
		self.be_select_event = nil
	end

	if self.obj_delete_event then
		GlobalEventSystem:UnBind(self.obj_delete_event)
		self.obj_delete_event = nil
	end

	if self.obj_dead_event then
		GlobalEventSystem:UnBind(self.obj_dead_event)
		self.obj_dead_event = nil
	end

	if self.top_hp_event then
		GlobalEventSystem:UnBind(self.top_hp_event)
		self.top_hp_event = nil
	end

	if self.target_change_buff_list then
		GlobalEventSystem:UnBind(self.target_change_buff_list)
		self.target_change_buff_list = nil
	end

	if self.target_hp_change_event then
		GlobalEventSystem:UnBind(self.target_hp_change_event)
		self.target_hp_change_event = nil
	end

	if self.show_ascription_event then
		GlobalEventSystem:UnBind(self.show_ascription_event)
		self.show_ascription_event = nil
	end

	if self.change_attack_mode_event then
		GlobalEventSystem:UnBind(self.change_attack_mode_event)
		self.change_attack_mode_event = nil
	end

	if self.obj_hp_recover then
		GlobalEventSystem:UnBind(self.obj_hp_recover)
		self.obj_hp_recover = nil
	end

	if self.do_hit_event then
		GlobalEventSystem:UnBind(self.do_hit_event)
		self.do_hit_event = nil
	end

	if self.hudun_event then
		GlobalEventSystem:UnBind(self.hudun_event)
		self.hudun_event = nil
	end

	if self.special_hudun_event then
		GlobalEventSystem:UnBind(self.special_hudun_event)
		self.special_hudun_event = nil
	end

	if self.obj_die_event then
		GlobalEventSystem:UnBind(self.obj_die_event)
		self.obj_die_event = nil
	end

	if self.remove_buff_event then
		GlobalEventSystem:UnBind(self.remove_buff_event)
		self.remove_buff_event = nil
	end

	if self.hudun_hp_tween then
		self.hudun_hp_tween:Kill()
		self.hudun_hp_tween = nil
	end

	if self.target_role_head_cell then
		self.target_role_head_cell:DeleteMe()
		self.target_role_head_cell = nil
	end

	if self.emeny_role_head_cell then
		self.emeny_role_head_cell:DeleteMe()
		self.emeny_role_head_cell = nil
	end

	if self.target_buff_list then
		for k,v in pairs(self.target_buff_list) do
			v:DeleteMe()
		end
		self.target_buff_list = nil
	end

	if CountDownManager.Instance:HasCountDown("mianui_target_bleed_show_countdown") then
		CountDownManager.Instance:RemoveCountDown("mianui_target_bleed_show_countdown")
	end
end

--目标是否为玩家 true == 显示玩家血条 隐藏boss血条
function MainUIView:SetTargetBar(enable)
	self.target_is_player = enable
	self.hp_bar:SetActive(enable)
	self.boss_hp_bar:SetActive(not enable)
end

-- 设置归属
function MainUIView:SetAscriptionText(text, title, is_boss)
	 local scene_type = Scene.Instance:GetSceneType()
	 if scene_type == SceneType.Kf_PVP or scene_type == SceneType.Kf_OneVOne
	 	or scene_type == SceneType.HIGH_TEAM_EQUIP_FB
	 	or MainuiWGData.IsGuideFbScene(scene_type) or scene_type == SceneType.Guild_Invite
	 	or scene_type == SceneType.XianMengzhan or scene_type == SceneType.ETERNAL_NIGHT
		or scene_type == SceneType.ETERNAL_NIGHT_FINAL or scene_type == SceneType.FBCT_NEWPLAYERFB 
		or scene_type == SceneType.SCENE_TYPE_DRAGON_TRIALT_FB then
     	self.node_list.BossFirstHurt:SetActive(false)
     	return
	 end

	 title = title or Language.Common.AscriptionTitle
	 if scene_type == SceneType.Shenyuan_boss then
	 	title = Language.Common.AscriptionTitle2
	 end

	 if (text and text ~= "" and self.is_boss) or is_boss then
	 	self.node_list.BossFirstHurt:SetActive(true)
		 self.node_list.boss_first_hurt_title.text.text = title
	 	self.node_list.AscriptionText.text.text = text
	 else
	 	self.node_list.AscriptionText.text.text = ""
	 	self.node_list.BossFirstHurt:SetActive(false)
	 end
end

-- 创建BUFF列表
function MainUIView:CreateTargerBuffList()
	local res_async_loader = AllocResAsyncLoader(self, "BuffSmlItem")
	res_async_loader:Load("uis/view/main_ui_prefab", "BuffSmlItem", nil,
		function(new_obj)
			self.target_buff_list = {}
			for i = 1, 7 do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(self.node_list.TargetBuffPanel.transform, false)
				local item_render = TargetBuffItemRender.New(obj)
				item_render:SetIndex(i)
				self.target_buff_list[i] = item_render
				if i == 7 then
					self:FlushTargetBuffer()
				end
			end
		end)
end

function MainUIView:OnChangeAttackMode()
	-- if not  self.is_show or not self.target_obj then return end
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if role_vo["attack_mode"] == ATTACK_MODE.ALL then
		return
	end

	if not Scene.Instance:IsEnemy(self.target_obj) then
		self:SetMianUITargetState(false)
	else
		if self.target_obj:IsMonster() then
			local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.target_obj:GetVo().monster_id]
			if cfg == nil or MONSTER_TYPE.MONSTER == cfg.type
				or MONSTER_TYPE.NPC == cfg.type then
				self:SetMianUITargetState(false)
				return
			end
		end
		
		self:SetMianUITargetState(true)
	end
end

-- 选择对象显示头像
function MainUIView:OnSelectObjHead(target_obj, select_type)
	if nil == target_obj or target_obj:IsDeleted() or target_obj:GetType() == SceneObjType.MainRole then
		self.target_obj = nil
		self.is_show = false
		self:SetMianUITargetState(false)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Kf_OneVOne
	or scene_type == SceneType.Field1v1
	or scene_type == SceneType.YEZHANWANGCHENGFUBEN then
		self.target_obj = nil
		self.is_show = false
		self:SetMianUITargetState(false)
		return
	end
	
	if self.target_obj ~= nil and target_obj:GetObjId() ~= self.target_obj:GetObjId() then
		self:ShowSpecialHurtPanelEffect()--切换目标
	end

	self.target_obj = target_obj
	self.portrait_label:SetActive(true)
	self.boss_angry:SetActive(false)
	self.boss_score:SetActive(false)
	self.role_head_icon:SetActive(false)
	self.node_list.teamtype:SetActive(false)
    self.node_list["boss_xianli"]:SetActive(false)
	self.boss_spe_tired:SetActive(false)
	if target_obj:IsMonster() then
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[target_obj:GetVo().monster_id]
		if cfg == nil
			or MONSTER_TYPE.MONSTER == cfg.type  									-- 小怪
			or MONSTER_TYPE.NPC == cfg.type then											-- 护送NPC不要血条
			self.is_show = false
			self:SetMianUITargetState(false)
			return
		else
			GuildWGCtrl.Instance:SetAscriptionText(target_obj) 								-- 猎鲲归属
		end
	elseif self.target_obj:IsRole() then

	else
		self.is_show = false
		self:SetMianUITargetState(self.is_show)
		return
	end

	self.is_show = true
	self:FlushTargetBuffer()
	self:SetMianUITargetState(self.is_show)

	self.node_list.DianFeng_Img:SetActive(false)
	local vo = target_obj:GetVo()

	if target_obj:IsRole() then
		FightWGCtrl.SendGetEffectListReq(vo.obj_id)
		local is_demons = vo.is_demons and vo.is_demons == EnumDemonsTpye.IsDemons
		if is_demons then
			local _, monster_id = FuBenPanelWGData.Instance:GetXinMoMonNum()
			local mon_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]
			local color_name = ""
			if mon_cfg then
				color_name = ToColorStr(mon_cfg.name, COLOR3B.RED)
				self:SetTargetName(color_name)
				self:DemonFBTargetInfoLogic(mon_cfg)
				return
			end
		else
			self:SetTargetBar(true)
			local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
		    local name = target_obj:GetName()
		    if not is_cross_server_stage then
				local name_list = Split(name, "_")
				if not IsEmptyTable(name_list) then
		        	name = name_list[1]
				end
		    end

			self:SetTargetName(name)
		end

		self.node_list.boss_head:SetActive(false)
		local role_id = vo.role_id
		if IS_ON_CROSSSERVER and vo.origin_uid > 0 then
			role_id = vo.origin_uid
		end

		self.portrait.transform.localPosition = self.portrait_original_pos
		self.portrait.transform.localScale = self.portrait_original_scale
		if (scene_type == SceneType.YEZHANWANGCHENGFUBEN) and
			KuafuYeZhanWangChengWGData.Instance:CheckActIsStart() then
			local bundle, asset = RoleWGData.Instance:GetHeadIconResByIdnex(0, vo.sex, vo.prof, true)
			self.portrait.image:LoadSprite(bundle, asset, function()
				self.portrait.image:SetNativeSize()
				self.portrait:SetActive(true)
			end)
		elseif scene_type == SceneType.TianShen3v3 then
            local tianshen_cfg = TianShenWGData.Instance:GetImageModelByAppeId(vo.appearance_param, true)

            if tianshen_cfg then
	            local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
				self.portrait.image:LoadSprite(bundle, asset, function()
					self.portrait.image:SetNativeSize()
					self.portrait:SetActive(true)
					self.portrait.transform.localPosition = Vector3(0, 20, 0)
					self.portrait.transform.localScale = Vector3(1, 1, 1)
				end)
			end
		else
			self.portrait:SetActive(false)
			self.role_head_icon:SetActive(true)
			local fashion_photoframe = 0
			local cur_fashion_photoframe = AvatarManager.Instance:GetAvatarFrameKey(role_id)
			if nil ~= cur_fashion_photoframe and cur_fashion_photoframe >= 0 then
				fashion_photoframe = cur_fashion_photoframe
			else
				fashion_photoframe = vo.appearance and vo.appearance.fashion_photoframe or 0
			end

			-- XUI.UpdateRoleHead(self.portrait, self.custom_portrait, role_id, vo.sex , vo.prof, false, nil, true)
			if self.emeny_role_head_cell then
				self.emeny_role_head_cell:SetData({role_id = role_id, sex = vo.sex, prof = vo.prof, fashion_photoframe = fashion_photoframe})
			end
		end

		self:SetTargetRoleLevel(target_obj:GetAttr("level"))
		self:SetHpPercent(target_obj:GetAttr("hp") / target_obj:GetAttr("max_hp"))
		self.img_back_ground:SetActive(false)
		self.boss_hp_count:SetActive(false)
		self.boss_hp_value:SetActive(false)
		self.is_boss =false
		self.node_list.BossFirstHurt:SetActive(false)
	elseif target_obj:IsMonster() then
		self:StopAutoRevenge()
		local mosnter_type = nil
		if target_obj ~= nil and target_obj:GetType() == SceneObjType.Monster then
			mosnter_type = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[target_obj:GetVo().monster_id].type
		end

		if mosnter_type == MONSTER_TYPE.BOSS or mosnter_type == MONSTER_TYPE.TISNSHEN_CALL then
			FightWGCtrl.SendGetEffectListReq(vo.obj_id)
			self.boss_hp_count:SetActive(true)
			self.img_back_ground:SetActive(true)
			self.boss_hp_value:SetActive(true)
			self.is_boss = true
			self.node_list.BossFirstHurt:SetActive(true)
			--加载头像
			self:SetTargetBossHead(target_obj:GetMonsterHead())
		elseif mosnter_type == MONSTER_TYPE.MONSTER then
			self.boss_hp_count:SetActive(false)
			self.img_back_ground:SetActive(false)
			self.boss_hp_value:SetActive(false)
			self.is_boss = false
		end
		
		self:SetShieldValuePercent()
		self:SetTargetBar(false)
		self:SetMianUITargetState(target_obj:IsCanShowHead())

		--仙盟战boss阵容队伍刷新
        if scene_type == SceneType.XianMengzhan then
			self:FlushTeamType(target_obj)
		end

		--仙盟争霸定级赛 boss血条客户端做假显示  不走通用逻辑
		local show_common_hp = true
        if scene_type == SceneType.Guild_Invite then
            local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE)
            local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
            local monster_id = other_cfg and other_cfg.boss_id or 0
            if act_is_open and monster_id == vo.monster_id then
                 show_common_hp = false
            end
        end

        if show_common_hp then
			self:SetHpPercent(target_obj:GetAttr("hp") / target_obj:GetAttr("max_hp"))
        end

		local monster_id = vo.monster_id
		local config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
		if config and config[monster_id] then
			local level = config[monster_id].level or 0
			local name = target_obj:GetName() or ""
			self:SetTargetName(name)
			if scene_type == SceneType.CROSS_LIEKUN or scene_type == SceneType.FengShenBang
				or scene_type == SceneType.Wujinjitan or scene_type == SceneType.LingHunGuangChang or scene_type == SceneType.COPPER_FB 
				or scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
				self:SetTargetLevel(Language.Mainui.Level3 .. vo.level)
			elseif scene_type == SceneType.PERSON_MABI_BOSS_DISPLAY then
				local fb_cfg = PersonMaBiBossWGData.Instance:GetPersonFBCfg()
				local boss_level = fb_cfg and fb_cfg.boss_level or level
				self:SetTargetLevel(Language.Mainui.Level3 .. boss_level)
			else
				self:SetTargetLevel(Language.Mainui.Level3 .. level)
			end
		end

		self.monste_obj = target_obj.vo
		self.select_monster_id = target_obj.vo.monster_id
		self.select_target_obj = target_obj:IsMonster()

		if scene_type == SceneType.DABAO_BOSS then
			local bundle, asset = ResPath.GetBossAngryIcon()
			self.boss_angry.image:LoadSprite(bundle, asset, function()
				self.boss_angry.image:SetNativeSize()
			end)
			self.boss_angry:SetActive(true)
			local angry = BossWGData.Instance:GetBossAngry(target_obj.vo.monster_id)
			self.angry_value.text.text = string.format(Language.Common.AngryValue, angry)
		end

		if scene_type == SceneType.FBCT_NEWPLAYERFB or scene_type == SceneType.BOSS_INVASION or scene_type == SceneType.PERSON_BOSS then
			self.node_list.BossFirstHurt:SetActive(false)
		end

		if scene_type == SceneType.CROSS_DIVINE_DOMAIN then
			local angry, score = HolyHeavenlyDomainWGData.Instance:GetBossAngryAndScore(target_obj.vo)

			if angry > 0 then
				local bundle, asset = ResPath.GetBossAngryIcon()
				self.boss_angry.image:LoadSprite(bundle, asset, function()
					self.boss_angry.image:SetNativeSize()
				end)

				self.angry_value.text.text = string.format(Language.HolyHeavenlyDomain.PowerValue, angry)
			end

			self.boss_angry:SetActive(angry > 0)

			if score > 0 then
				local score_bundle, score_asset = ResPath.GetBossScoreIcon()
				self.boss_score.image:LoadSprite(score_bundle, score_asset, function()
					self.boss_score.image:SetNativeSize()
				end)

				self.score_value.text.text = string.format(Language.HolyHeavenlyDomain.KillScore, score)
			end

			self.boss_score:SetActive(score > 0)
		end

		if scene_type == SceneType.XianJie_Boss then
			local bundle, asset = ResPath.GetBossXianjieTiredIcon()
			self.boss_angry.image:LoadSprite(bundle, asset, function()
				self.boss_angry.image:SetNativeSize()
			end)
			self.boss_angry:SetActive(true)
			local angry = XianJieBossWGData.Instance:GetBossAngry(target_obj.vo.monster_id)
			self.angry_value.text.text = string.format(Language.Common.AngryValue, angry)
		end

		if scene_type == SceneType.VIP_BOSS then
			local bundle, asset = ResPath.GetF2MainUIImage("a3_zjm_ztbli")
			self.node_list["boss_xianli"].image:LoadSprite(bundle, asset, function()
				self.node_list["boss_xianli"].image:SetNativeSize()
			end)
			local is_show_lingli = BossWGData.Instance:GetIsShowLingLi()
			self.node_list["boss_xianli"]:SetActive(is_show_lingli > 0)
			local boss_info = BossWGData.Instance:GetBossInfoByBossId(target_obj.vo.monster_id)
			local kill_reduce_xianli = boss_info and boss_info.kill_reduce_xianli or 0
			self.node_list["xianli_cost_txt"].text.text = kill_reduce_xianli
		end

		if scene_type == SceneType.MJ_BOSS then
			local bundle, asset = ResPath.GetBossUI("boss_mijing_power")
			self.boss_angry.image:LoadSprite(bundle, asset, function()
				self.boss_angry.image:SetNativeSize()
			end)
			self.boss_angry:SetActive(true)
			local info = BossWGData.Instance:GetSecretBossInfoByBossId(target_obj.vo.monster_id)
			-- self.angry_value.text.text = info.kill_consume_physical
			self.angry_value.text.text = string.format(Language.Common.AngryValue, info.kill_consume_physical)
		end

		if scene_type == SceneType.CROSS_LAND_WAR then
			local boss_seq =  (vo.special_param % 100000) % 1000
			local land_seq =  math.floor((vo.special_param % 100000) / 1000) - 1
			local monster_group = math.floor(vo.special_param / 100000) - 1
			local boss_cfg = PositionalWarfareWGData.Instance:GetCurMonsterCfgData(monster_group, land_seq, boss_seq)

			if boss_cfg and boss_cfg.kill_tired then
				self.node_list["spe_tired_value"].text.text = boss_cfg.kill_tired
				self.boss_spe_tired:SetActive(true)
			end
		end
	else
		self:SetMianUITargetState(false)
		self:SetHpPercent(1)
		self.is_boss = false
    end

    -- if self.is_boss then
    --     self.node_list["boss_reward_btn"]:SetActive(self:GetIsShowRewardBtn(target_obj))
    -- else
    --     self.node_list["boss_reward_btn"]:SetActive(false)
    -- end

	local show_reward = self.is_boss and self:GetIsShowRewardBtn(target_obj) 
	self.node_list["boss_reward_btn"]:SetActive(show_reward)
	-- self.left_sign:SetActive(not show_reward) 
end

function MainUIView:GetIsShowRewardBtn(target_obj)
	local vo = target_obj.vo

	-- -- 2024/8/29 策划xzp 让玩家显示+号 所有怪物都显示宝箱
	-- if target_obj:IsMonster() then
	-- 	return true	
	-- end

	if target_obj:IsMonster() and vo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_CALL then
		return false
	end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.PERSON_BOSS or scene_type == SceneType.WorldBoss
    or scene_type == SceneType.DABAO_BOSS or scene_type == SceneType.VIP_BOSS
    or scene_type == SceneType.KF_BOSS
    or scene_type == SceneType.Shenyuan_boss
    or scene_type == SceneType.XianJie_Boss
    or scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS
	or scene_type == SceneType.CROSS_EVERYDAY_RECHARGE_BOSS 
	or scene_type == SceneType.CROSS_LAND_WAR then
        return true
    end

    return false
end

--心魔副本特殊处理, 由于心魔副本的怪物是角色影子role new出来的，需要显示成BOSS一样的信息
function MainUIView:DemonFBTargetInfoLogic(mon_cfg)
	self:SetTargetBar(false)
	self.boss_hp_count:SetActive(true)
	self.img_back_ground:SetActive(true)
	self.boss_hp_value:SetActive(true)
	self.is_boss = true
	self.node_list.BossFirstHurt:SetActive(true)

	--加载头像
	self:SetTargetBossHead(mon_cfg.small_icon)
	self:SetTargetLevel(ToColorStr(Language.Mainui.Level3 .. (mon_cfg.level or 0),  COLOR3B.RED))
end

function MainUIView:SetTargetBossHead(small_icon)
	if not small_icon or small_icon == "" then
		small_icon = 9999
	end

	if small_icon then
		self.portrait:SetActive(false)
		self.custom_portrait:SetActive(false)
		self.node_list.boss_head:SetActive(true)
		local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. small_icon)
		self.node_list.boss_head.image:LoadSprite(bundle, asset, function()
			self.node_list.boss_head.image:SetNativeSize()
		end)
	end
end

function MainUIView:SetTargetName(name)
	self.target_name.text.text = name or ""
end

function MainUIView:SetTargetLevel(str)
	self.target_level.text.text = str or ""
end

--设置主角等级
function MainUIView:SetTargetRoleLevel(value)
	if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN then
		if KuafuYeZhanWangChengWGData.Instance:CheckActIsStart() then
			self.node_list.DianFeng_Img:SetActive(false)
			self.node_list["Level"].text.text = ""
			return
		end
	end

	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(value)
	self.node_list.DianFeng_Img:SetActive(is_vis)
	local str = level
	if not is_vis then
		str = string.format("Lv.%s", level)
	end

	self.node_list["Level"].text.text = str
end


-- 取消
function MainUIView:OnObjDeleteHead(obj)
	if self.target_obj == obj then
		self.target_obj = nil
		-- local scene_id = Scene.Instance:GetSceneId()
        local scene_type = Scene.Instance and Scene.Instance:GetSceneType()
		-- if not IS_ON_CROSSSERVER and scene_type == SceneType.Common and MainuiWGCtrl.Instance and scene_id ~= BootyBayWGData.Instance:GetBootyBaySceneId() then
		-- 	MainuiWGCtrl.Instance:PlayTopButtonTween(true)
        -- end

        if scene_type == SceneType.XianJie_Boss and BossWGCtrl.Instance then
            BossWGCtrl.Instance:JudgeNeedPickObj(obj)
        end

        self:ShowSpecialHurtPanelEffect()--目标死亡/丢失目标
		self:SetMianUITargetState(false)
	end
end

-- 目标血量改变
function MainUIView:OnTargetHpChangeHead(target_obj, no_change)
	if self.target_obj ~= nil and not self.target_obj:IsDeleted() and target_obj ~= nil and not target_obj:IsDeleted() then
		local show_id = self.target_obj:GetObjId()
		local t_id = target_obj:GetObjId()
		if show_id ~= nil and t_id ~= nil and show_id == t_id then
			self:SetHpPercent(target_obj:GetAttr("hp") / target_obj:GetAttr("max_hp"))
		end
	end
end

function MainUIView:OnHuDunEvent(protocol)
	if not self.show_target or not self.show_target.gameObject.activeSelf then
		return
	end

	if not self.target_obj or not self.target_obj:GetVo() or self.target_obj:GetObjId() ~= protocol.obj_id then
		return
	end

    local percent = (protocol.real_hurt + protocol.left_hp) / protocol.max_hp
	self.node_list["hudun_hp"].slider.value = percent
	percent = protocol.left_hp / protocol.max_hp
	self.hudun_hp_tween = self.node_list["hudun_hp"].slider:DOValue(percent, 0.5, false)
end

function MainUIView:MainRoleDie()
	self:ShowSpecialHurtPanelEffect()--玩家死亡
end

function MainUIView:OnRemoveBuffEvent(protocol)
	if not self.show_target or not self.show_target.gameObject.activeSelf then
		return
	end

	if not self.target_obj or not self.target_obj:GetVo() or self.target_obj:GetObjId() ~= protocol.obj_id then
		return
	end

	if protocol.buff_type ~= BUFF_TYPE.EBT_HPSTORE then
		return
	end

	if self.hudun_hp_tween then
		self.hudun_hp_tween:Kill()
		self.hudun_hp_tween = nil
	end

	self.node_list["hudun_hp"].slider.value = 0
	self:FlushBleedShow()
end

function MainUIView:OnTargetHpReCover(obj)
	local target_obj_vo = self.target_obj and self.target_obj:GetVo()
	local cur_obj_vo = obj and obj:GetVo()
	if target_obj_vo and cur_obj_vo then
		if target_obj_vo.monster_id == cur_obj_vo.monster_id then
			self:SetHpPercent(obj:GetAttr("hp") / obj:GetAttr("max_hp"))
		end
	end
end

-- 设置目标血条
local old_index = 0
local old_percent = 1
local old_per = 1
function MainUIView:SetHpPercent(percent)
	self.hp_slider_value.slider.value = (percent)
	self.boss_hp_top_value.slider.value = (percent)
	if self.is_boss == true then
		local cur_monsterid = self.target_obj and self.target_obj:GetVo() and self.target_obj:GetVo().monster_id
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[cur_monsterid]
		local max_index = cfg and cfg.blood_num or 100
		
		-- 天神召唤物5条血
		if self.target_obj ~= nil and not self.target_obj:IsDeleted() and self.target_obj:IsMonster() and self.target_obj:IsTianShenCallMonster() then
			max_index = 5
		end

		local index = math.floor(percent * max_index)
		local per = percent * max_index - index  				--当前血条的百分比

		local res_index = index % 5
		if per == 0 and percent ~= 0 then
			per = 1
		end

		if percent == 1 then
			res_index = 1
			res_index = 4
		else
			index = index + 1
		end

		self.img_top_fill.image:LoadSprite(ResPath.GetBossHp(5 - res_index))
		GlobalTimerQuest:AddDelayTimer(function()
				if nil == self.boss_hp_bar then return end
				if not self.target_is_player then
			 		self.boss_hp_bar:SetActive(index > 0)
			 	else
			 		self:SetTargetBar(true)
			 	end
			 	end, 0.5)
		if index > 1 then
			self.img_back_ground.image:LoadSprite(ResPath.GetBossHp((6 - res_index) > 5 and 1 or (6 - res_index)))
			-- self.img_bottom_fill.image:LoadSprite(ResPath.GetBossHp(6 - res_index > 5 and 1 or 6 - res_index))
			self.img_back_ground:SetActive(true)
		else
			self.img_back_ground:SetActive(false)
        end

		self.boss_hp_count.text.text = (index == 0 and "" or ("x" .. index))
		self.boss_hp_value.slider.value = (per)
		self.boss_hp_top_value.slider.value = (per)
		if old_index ~= index then
			old_per = per
			old_percent = per
			self.boss_hp_bottom_value.slider.value = 1
			self:FlushHpGreyEffect(index < old_index)
			self.boss_hp_bottom_value.slider:DOValue(old_per, 0.5, false):OnComplete(function ()
				self.boss_hp_grey_effect_play_flag = false
				self:FlushHpGreyEffect(false)
			end)

			self.boss_hp_count.transform:SetLocalScale(1.4, 1.4, 1.4)
			self.boss_hp_count.transform:DOScale(1, 0.2)
		else
			self:FlushHpGreyEffect(per < old_percent)
			self.boss_hp_bottom_value.slider:DOValue(per, 0.5, false):OnComplete(function ()
				self.boss_hp_grey_effect_play_flag = false
				self:FlushHpGreyEffect(false)
			end)

			old_percent = per
		end

		old_index = index
	else
		self:FlushHpGreyEffect(percent < old_percent)
		self.boss_hp_bottom_value.slider:DOValue(percent, 0.5, false):OnComplete(function ()
			self.boss_hp_grey_effect_play_flag = false
			self:FlushHpGreyEffect(false)
		end)

		old_percent = percent
	end
end

local target_state = false
function MainUIView:SetMianUITargetState(state)
	if state == nil then
		state = target_state
	end

	target_state = state
	if self.target_obj == nil then
		target_state = false
	end

	if self.show_target then
		local cur_is_show = target_state and not Scene.Instance:GetMainRole():IsQingGong() and not Scene.Instance:GetMainRole():IsMitsurugi()
		local last_state = self.show_target.gameObject.activeSelf
		self.show_target:SetActive(cur_is_show)

		if cur_is_show then
			self:SetShrinkButtonIsOn(false)
		end

		if not cur_is_show and BossWGCtrl.Instance and BossWGCtrl.Instance.CloseBossRewardView then
			BossWGCtrl.Instance:CloseBossRewardView()
		end

		if cur_is_show ~= last_state and FightWGCtrl.Instance ~= nil then
			if not cur_is_show then
				FightWGCtrl.Instance:SendCurAttentionObj(COMMON_CONSTS.SERVER_INVAILD_OBJ_ID)
			else
				if nil == self.target_obj or self.target_obj:IsDeleted() or self.target_obj:GetType() == SceneObjType.MainRole then
					FightWGCtrl.Instance:SendCurAttentionObj(COMMON_CONSTS.SERVER_INVAILD_OBJ_ID)
				else
					FightWGCtrl.Instance:SendCurAttentionObj(self.target_obj:GetObjId())
				end
			end
		end
		
		if not last_state then
			self.node_list["hudun_hp"].slider.value = 0
		end
	end
end

function MainUIView:SetMianUITargetPos(pos_x, pos_y)
	if self.node_list["TargetInfoPanel"] then
		local position = self.node_list["TargetInfoPanel"]["TargetInfoPanel"].rect.anchoredPosition
		pos_x = pos_x or position.x
		pos_y = pos_y or position.y
		self.node_list["TargetInfoPanel"]["TargetInfoPanel"].rect.anchoredPosition = Vector2(pos_x, pos_y)
	end
end

function MainUIView:SetWhileFillAmount(value)
	if self.img_while then
		self.img_while.image.fillAmount = value
	end
end

function MainUIView:TopPanelAni(ison)
	--print_error(ison)
	local is_have_aim = self.target_obj and Scene.Instance:IsEnemy(self.target_obj)
	self:SetMianUITargetState(is_have_aim and not ison and self.is_show)
	local max_move, min_move = self:GetTopPanelAniPosY()
	local move_vaule = ison == true and max_move or min_move
	if nil == self.node_list.MoveAniPos then
		return
	end

	if target_state and not Scene.Instance:GetMainRole():IsQingGong() and not Scene.Instance:GetMainRole():IsMitsurugi() then
		local tween = self.node_list.MoveAniPos.rect:DOAnchorPosY(move_vaule, 0)
		tween:SetEase(DG.Tweening.Ease.Linear)
	end
end

-- 刷新目标buff列表
function MainUIView:FlushTargetBuffer(is_main_role)
	if self.target_buff_list and self.target_obj and not self.target_obj:IsDeleted() and not is_main_role then
		local eff_list = FightWGData.Instance:GetOtherRoleShowEffect(self.target_obj:GetObjId())
		local cur_show_type = MainuiWGData.Instance:GetCurShowBuffType()
		local need_close_buff_tip = true
		if self.target_obj:IsMonster() then
			self:CheckIsNeedPressBuff(eff_list)
		end

		for k,v in pairs(self.target_buff_list) do
			v:SetData(eff_list[k])
			if eff_list[k] ~= nil and eff_list[k].info.client_effect_type == cur_show_type then
				need_close_buff_tip = false -- 当前显示tip的buff还在有效期内，不关闭提示框
			end
		end

		if need_close_buff_tip then
			MainuiWGCtrl.Instance:CloseTargetBuffTip()
		end

		self:FlushBleedShow()
	end
end

--检查是否需要添加boss压制buff
function MainUIView:CheckIsNeedPressBuff(eff_list)
	local vo = self.target_obj:GetVo()
	if vo ~= nil then
		local press_type = MainuiWGData.Instance:IsBossRePress(vo.monster_id, vo.level)
		MainuiWGData.Instance:AddBossPressBuff(eff_list, press_type)
	end
end

function MainUIView:GetTopPanelAniPosY()
	return 0, 0
end

function MainUIView:OnClickHead()
	if self.target_obj and self.target_obj:IsRole() and self.target_obj:GetVo() and self.target_obj:GetVo().is_shadow ~= 1 then
		if self.target_role_head_cell == nil then
		 	self.target_role_head_cell = RoleHeadCell.New(true)
		 	local menu = Language.Menu
		 	self.target_role_head_cell:AddCustomMenu(menu.ShowInfo
		 											, menu.AddFriend
		 											, menu.Profess
		 											, menu.InviteTeam
		 											, menu.GiveFlowerNew
		 											, menu.Blacklist
		 											, menu.ApplyTeam
		 											, menu.ApplyToBeTeamLeader
		 											, Language.Menu.AddEnemy
													, Language.Menu.DeleteEnemy)
		end

        local vo = self.target_obj:GetVo()
		local role_info = {
			role_name = vo.name,
			prof = vo.prof,
			sex = vo.sex,
			is_online = true,
			plat_type = vo.origin_plat_type,
			plat_name = vo.plat_name,
			server_id = vo.merge_server_id,
		}
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            CrossTeamWGCtrl.Instance:QueryCrossTeamInfo(vo.uuid, function(protocol)
				role_info.role_id = vo.uuid.temp_low
				role_info.team_index = protocol.team_index
				role_info.area_index = vo.area_index
                self.target_role_head_cell:SetRoleInfo(role_info)
                self.target_role_head_cell:OpenMenu()
            end)
        else
            NewTeamWGCtrl.Instance:QueryTeamInfo(vo.origin_uid, function(protocol)
                local is_same_server = not RoleWGData.Instance:IsSameServer(vo)
				role_info.team_index = protocol.team_index
                if IS_ON_CROSSSERVER or is_same_server then
					role_info.role_id = vo.origin_uid
                    self.target_role_head_cell:SetRoleInfo(role_info)
                else
					role_info.role_id = vo.role_id
                    self.target_role_head_cell:SetRoleInfo(role_info)
                end

                if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN and
                    KuafuYeZhanWangChengWGData.Instance:CheckActIsStart() then
                    return
                end

                self.target_role_head_cell:OpenMenu()
            end)
        end
	end
end

function MainUIView:OnClickBossReward()
    if self.target_obj and self.target_obj:IsMonster() then
        local boss_id = self.target_obj:GetVo().monster_id
		local cfg = {}

		if Scene.Instance:GetSceneType() == SceneType.CROSS_LAND_WAR then
			local vo = self.target_obj:GetVo()
			local boss_id = vo.monster_id or -1
			
			if boss_id > 0 and vo.special_param > 0 then
				local boss_seq =  (vo.special_param % 100000) % 1000
				local land_seq =  math.floor((vo.special_param % 100000) / 1000) - 1
				local monster_group = math.floor(vo.special_param / 100000) - 1
	
				cfg = PositionalWarfareWGData.Instance:GetCurMonsterCfgData(monster_group, land_seq, boss_seq)
			end
		else
			cfg = BossWGData.Instance:GetRewardListById(boss_id)
		end

        if not IsEmptyTable(cfg) then
            BossWGCtrl.Instance:OpenBossRewardView(cfg)
        else
            print_error("cfg is nil, monster_id = ", boss_id)
        end
    end
end

function MainUIView:OnClickBossXianli()
    if self.target_obj and self.target_obj:IsBoss() then
        local boss_id = self.target_obj:GetVo().monster_id
        local boss_info = BossWGData.Instance:GetBossInfoByBossId(boss_id)
        local kill_reduce_xianli = boss_info and boss_info.kill_reduce_xianli
        if kill_reduce_xianli then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.VipCostXianli, kill_reduce_xianli))
        end
	end
end

function MainUIView:OnClickBossHead()
	if self.target_obj and self.target_obj:IsMonster() then
		GuajiWGCtrl.Instance:StopGuaji()
        MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		MoveCache.param1 = (self.target_obj:GetVo() or {}).monster_id or 0
		GuajiWGCtrl.Instance:MoveToObj(self.target_obj, COMMON_CONSTS.GUAJI_MAX_RANGE)
	end
end

function MainUIView:SetTargetViewShakeRootPositionX(x)
	self.node_list["TargetInfoShakeRoot"].rect.anchoredPosition = Vector2(x, self.node_list["TargetInfoShakeRoot"].rect.anchoredPosition.y)
end

function MainUIView:SetTargetViewShakeRootPositionY(y)
	self.node_list["TargetInfoShakeRoot"].rect.anchoredPosition = Vector2(self.node_list["TargetInfoShakeRoot"].rect.anchoredPosition.x, y)
end

function MainUIView:ResetTargetViewShakeRootPosition()
	self.node_list["TargetInfoShakeRoot"].rect.anchoredPosition = Vector2(self.node_list["TargetInfoShakeRoot"].rect.anchoredPosition.x, self.target_info_shake_root_original_y)
end

function MainUIView:FlushBleedShow()
	local has_buff = false
	local buff_layer = 0
	local time = 0
	if self.target_obj ~= nil and not self.target_obj:IsDeleted() then
		local obj_id = self.target_obj:GetObjId()
		local buff_list = FightWGData.Instance:GetOtherRoleEffectList(obj_id)
		if buff_list ~= nil then
			for i = 1, #buff_list do
				if buff_list[i].buff_type == BUFF_TYPE.EBT_SHANGHENG then
					has_buff = true
					buff_layer = buff_list[i].merge_layer
					time = buff_list[i].cd_time - Status.NowTime
					break
				end
			end
		end
	end

	if self.node_list.root_bleed ~= nil then
		self.node_list.root_bleed:SetActive(has_buff)
	end

	if has_buff then
		if self.node_list.text_bleed_layer ~= nil then
			self.node_list.text_bleed_layer.text.text = "X" .. buff_layer
		end

		self:FlushBleedTimer(time)
	else
		if CountDownManager.Instance:HasCountDown("mianui_target_bleed_show_countdown") then
			CountDownManager.Instance:RemoveCountDown("mianui_target_bleed_show_countdown")
		end
	end
end

-- 刷新倒计时   end_time 下次可用时间戳   bianshen_end_time  变身结束时间戳
function MainUIView:FlushBleedTimer(total_time)
	if CountDownManager.Instance:HasCountDown("mianui_target_bleed_show_countdown") then
		CountDownManager.Instance:RemoveCountDown("mianui_target_bleed_show_countdown")
	end

	self:FlushBleedTime(0, total_time)
	if total_time > 0 then
		local timer_sign = "mianui_target_bleed_show_countdown"
		CountDownManager.Instance:AddCountDown(timer_sign, BindTool.Bind(self.FlushBleedTime, self), BindTool.Bind(self.FlushBleedTimeCom, self), nil, total_time, 0.1)
	else
		if self.node_list.root_bleed ~= nil then
			self.node_list.root_bleed:SetActive(false)
		end
	end
end

function MainUIView:FlushBleedTime(elapse_time, total_time)
	if total_time > 0 then
		local value = elapse_time / total_time
		if self.node_list.img_bleed_pro ~= nil then
			self.node_list.img_bleed_pro:SetActive(true)
			self.node_list.img_bleed_pro.image.fillAmount = 1 - value
		end
	else
		if self.node_list.img_bleed_pro ~= nil then
			self.node_list.img_bleed_pro:SetActive(false)
		end
	end
end

function MainUIView:FlushBleedTimeCom()
	if self.node_list.img_bleed_pro then
		self.node_list.img_bleed_pro:SetActive(false)
	end
end

function MainUIView:CancelTargetObj(obj_id)
	if obj_id ~= nil and self.target_obj ~= nil then
		if not self.target_obj:IsDeleted() then
			if self.target_obj:GetObjId() == obj_id then
				self:SetMianUITargetState(false)
			end
		end
	end
end

function MainUIView:FlushTeamType(target_obj)
	local monster_id = target_obj:GetVo().monster_id
	local monster_side =  GuildBattleRankedWGData.Instance:GetGuildBossSide(monster_id)--0 蓝方  1红方
	local is_friend = GuildBattleRankedWGData.Instance:IsTheSameBatterArrayMonster(monster_id)
	self.node_list.teamtype:SetActive(true)
	self.node_list.redtype:SetActive(monster_side == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.REDTEAM)
	self.node_list.bluetype:SetActive(monster_side == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM)
	self.node_list.teamtype_text.text.text = is_friend and Language.GuildBattleRanked.GuildBatterBossExplain[0] or Language.GuildBattleRanked.GuildBatterBossExplain[1]
end

function MainUIView:SetShieldValuePercent()
	self.break_shield_value:SetActive(false)
	if self.target_obj ~= nil and not self.target_obj:IsDeleted() and self.target_obj:IsMonster() and self.target_obj:GetType() == SceneObjType.Monster then
		if self.target_obj:GetAttr("shield_value") then
			if self.target_obj:GetAttr("shield_value") > 0 and self.target_obj:GetAttr("max_shield_value") > 0 then
				self.break_shield_value:SetActive(true)
				self.break_shield_value.slider.value = self.target_obj:GetAttr("shield_value") / self.target_obj:GetAttr("max_shield_value")
			end
		end
	end
end

--播放全屏特效一次
function MainUIView:ShowBreakShieldBuffEffect()
	if self.node_list["breakshield_screen_effect"] then
		self.node_list["breakshield_screen_effect"]:SetActive(true)
	end
end

--检测选中的BOSS的魔气盾变化-显示护盾条变化
function MainUIView:OnSpecialHuDunEvent(info)
	if not self.show_target or not self.show_target.gameObject.activeSelf then
		return
	end

	if not self.target_obj or self.target_obj:GetType() ~= SceneObjType.Monster or not self.target_obj:GetVo() or self.target_obj:GetObjId() ~= info.obj_id then
		return
	end

	--播放全屏特效
	if info.shield_value == 0 then
		self:ShowBreakShieldBuffEffect()
	end

	self.node_list.effect_break_shield:CustomSetActive(info.shield_value <= 0)
	self.node_list.desc_break_shield:CustomSetActive(info.shield_value <= 0)
	self.node_list.zero_hudun_effect:CustomSetActive(info.shield_value <= 0)

	if info.shield_value <= 0 then
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.target_obj:GetVo().monster_id]
		
		if cfg and cfg.shield_index then
			local shield_cfg = BossWGData.Instance:GetBossShieldData(cfg.shield_index)
			self.node_list.desc_break_shield.text.text = shield_cfg.broken_txt or ""
		end
	end

	self.break_shield_value:SetActive(true)
	local percent = (info.shield_value - info.change_shield_value) / info.max_shield_value
	self.break_shield_value.slider.value = percent
	percent = info.shield_value / info.max_shield_value
	self.break_shield_value.slider:DOValue(percent, 0.5, false)
end

--显示伤害界面并实时计算伤害
function MainUIView:SetTianShenBreakShieldHurt(info)
	if self.ts_bs_panel_effect_show or not self.target_obj or self.target_obj:IsDeleted() or self.target_obj:GetType() == SceneObjType.MainRole then
		return
	end

	local is_has = MainuiWGData.Instance:CheckIsHaveBreakShieldBuff(self.target_obj:GetObjId())
	--没有护盾
	if not is_has then
		return
	end

	local deliverer = Scene.Instance:GetObj(info.deliverer)
	--检测玩家是否对选中的BOSS造成伤害
	if info.blood == 0 or info.obj_id ~= self.target_obj:GetObjId() or (deliverer and not deliverer:IsMainRole()) then
		return
	end

	local time = BossWGData.Instance:GetBossShieldDelayTime()
	local hurt_value = -info.blood
	if not self.tian_shen_break_shield.gameObject.activeSelf then
		self.tian_shen_break_shield:SetActive(true)
		self.node_list.ts_bs_effect:SetActive(false)
		TweenManager.Instance:ExecuteViewTween(GuideModuleName.MainUIView, 0, self.node_list)

		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			main_role:TryCameraShake("break_effect")
		end
	end

	self.ts_bs_hurt_clear_t = time + Status.NowTime--刷新时间间隔
	local complete_fun = function()
		
    end

	self.node_list.ts_bs_hurt.text:DoNumberTo(self.ts_bs_hurt_value, self.ts_bs_hurt_value > 0 and self.ts_bs_hurt_value + hurt_value or hurt_value, 1, complete_fun)
	self.ts_bs_hurt_value = self.ts_bs_hurt_value + hurt_value
end

--监听是否长时间未造成伤害
function MainUIView:CheckSpecialHurtPanel()
	if not self.tian_shen_break_shield or not self.tian_shen_break_shield.gameObject.activeSelf 
		or not self.target_obj or self.target_obj:IsDeleted() or self.target_obj:GetType() == SceneObjType.MainRole then
		return
	end

	if self.ts_bs_hurt_clear_t > 0 and Status.NowTime >= self.ts_bs_hurt_clear_t then
		self.ts_bs_hurt_value = 0 
		self.ts_bs_hurt_clear_t = 0
		self.node_list.ts_bs_hurt.text.text = 0
	end

	if not self.ts_bs_panel_effect_show then
		local is_has = MainuiWGData.Instance:CheckIsHaveBreakShieldBuff(self.target_obj:GetObjId())
		if not is_has then
			self:ShowSpecialHurtPanelEffect()--buff消失
		end
	end
end

--破盾buff消失/切换目标/boss死亡/目标消失/玩家死亡
function MainUIView:ShowSpecialHurtPanelEffect()
	if self.ts_bs_panel_effect_show or not self.tian_shen_break_shield or not self.tian_shen_break_shield.gameObject.activeSelf then
		return
	end

	self.ts_bs_panel_effect_show = true          
	self.ts_bs_hurt_value = 0 
	self.ts_bs_hurt_clear_t = 0
	self.node_list.ts_bs_effect:SetActive(true)
	ReDelayCall(self, function()
		self.ts_bs_panel_effect_show = false
		self.node_list.ts_bs_effect:SetActive(false)
		self.tian_shen_break_shield:SetActive(false)
	end, 0.5, "close_special_hurt_panel")
end

-- 刷新掉血特效
function MainUIView:FlushHpGreyEffect(is_show)
	if self.boss_hp_grey_effect_play_flag then
		return
	end

	if is_show then
		self.boss_hp_grey_effect_play_flag = true
	end

	if self.node_list.boss_hp_grey_effect then
		self.node_list.boss_hp_grey_effect:CustomSetActive(is_show)
	end
end

function MainUIView:OnClickShowHpInfo()
	if self.is_boss then
		RuleTip.Instance:SetContent(Language.Mainui.DescHPHuDunContent, Language.Mainui.DescHPHuDunTitle)
	end
end

--------------------------------------------------------------------------------------------------
TargetBuffItemRender = TargetBuffItemRender or BaseClass(BaseRender)
function TargetBuffItemRender:__init()
end

function TargetBuffItemRender:__delete()
	if CountDownManager.Instance:HasCountDown("mianui_target_buff_countdown" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("mianui_target_buff_countdown" .. self.index)
	end
end

function TargetBuffItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickItem, self))
end

function TargetBuffItemRender:OnClickItem()
	if nil == self.data then
		return
	end
	
	MainuiWGCtrl.Instance:OpenOpenTargetBuffTip(self.view, self.data)
	MainuiWGData.Instance:SetCurShowBuffType(self.data.info.client_effect_type)
end

function TargetBuffItemRender:OnFlush()
	if CountDownManager.Instance:HasCountDown("mianui_target_buff_countdown" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("mianui_target_buff_countdown" .. self.index)
	end

	self:SetVisible(self.data ~= nil)
	if nil == self.data then
		return
	end

	self.node_list["icon"].image:LoadSprite(ResPath.GetBuff(self.data.info.client_effect_type))
	self.node_list["num"].text.text = 1 < self.data.info.merge_layer and self.data.info.merge_layer or ""
	self:FlushSkillBuffTimer(self.index, self.data.info.cd_time or 0)
end

-- 刷新倒计时   end_time 下次可用时间戳   bianshen_end_time  变身结束时间戳
function TargetBuffItemRender:FlushSkillBuffTimer(index, end_time)
	if CountDownManager.Instance:HasCountDown("mianui_target_buff_countdown" .. index) then
		CountDownManager.Instance:RemoveCountDown("mianui_target_buff_countdown" .. index)
	end

	end_time = end_time - Status.NowTime
	self:FlushBuffEffTime(0, end_time)

	if end_time > 0 then
		if self.node_list.mask then
			self.node_list.mask:SetActive(true)
		end
		local timer_sign = "mianui_target_buff_countdown" .. index
		CountDownManager.Instance:AddCountDown(timer_sign, BindTool.Bind(self.FlushBuffEffTime, self), BindTool.Bind(self.FlushBuffEffTimeCom, self), nil, end_time, 0.1)
	else
		if self.node_list.mask then
			self.node_list.mask:SetActive(false)
			self.node_list.mask.image.fillAmount = 0
		end
	end
end

function TargetBuffItemRender:FlushBuffEffTime(elapse_time, total_time)
	if total_time > 0 then
		local value = elapse_time / total_time
		if self.node_list.mask then
			self.node_list.mask:SetActive(true)
			self.node_list.mask.image.fillAmount = value
		end
	else
		if self.node_list.mask then
			self.node_list.mask:SetActive(false)
			self.node_list.mask.image.fillAmount = 0
		end
	end
end

function TargetBuffItemRender:FlushBuffEffTimeCom(index)
	if self.node_list.mask then
		self.node_list.mask:SetActive(false)
		self.node_list.mask.image.fillAmount = 0
	end
end