TransFerDemonView = TransFerDemonView or BaseClass(SafeBaseView)

function TransFerDemonView:__init()
	self.view_layer = UiLayer.MainUI
	
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "TransferTargetContent")
end

function TransFerDemonView:__delete()

end

function TransFerDemonView:ReleaseCallBack()

	if self.left_solt_list then
		self.left_solt_list:DeleteMe()
		self.left_solt_list = nil
	end

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
	end

	self.monster_id = nil
end

function TransFerDemonView:LoadCallBack()
    self.item_cell_list = {}
    for i=1,3 do
    	self.item_cell_list[i] = ItemCell.New(self.node_list["item_cell"..i])
    	self.item_cell_list[i]:SetActive(false)
    	self.node_list["item_cell"..i]:SetActive(false)
    end
	-- XUI.AddClickEventListener(self.node_list["bg_skill_btn"], BindTool.Bind(self.ClickSkillBtn,self))

	self.left_solt_list = AsyncListView.New(TransFerSlotItem,self.node_list["left_solt_list"])

end

function TransFerDemonView:OpenCallBack()
end

function TransFerDemonView:ShowIndexCallBack()
	self.node_list["TargetContent_root"]:SetActive(false)
    MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
    	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
    	parent:SetActive(true)
    	self.node_list["TargetContent_root"]:SetActive(true)
    	self.node_list["TargetContent_root"].transform:SetParent(parent.transform, false)
    end)
    self.obj = self.node_list["TargetContent_root"].gameObject
	self:Flush()
end

function TransFerDemonView:OnFlush()
	self:InitMonsterId()

	local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	local fb_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
	self.node_list.des.text.text = fb_cfg.fb_desc
	local des_tab = Language.FuBenPanelDes.TopDes['transfer']
	--self.node_list['rich_text_1'].text.text = des_tab.Title1
	self.node_list['rich_text_2'].text.text = des_tab.Label0

	local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	prof_zhuan = prof_zhuan + 1


	-- 奖励显示
	local face_cfg = TransFerWGData.Instance:GetFaceCfgByZhuanNum(prof_zhuan)
	self.left_solt_list:SetDataList(face_cfg)

	local reward_item_list = TransFerWGData.Instance:GetTransFerSingleRewardList(prof_zhuan)
	if reward_item_list then
		for k,v in pairs(self.item_cell_list) do
			if reward_item_list[k - 1] and reward_item_list[k - 1].item_id and reward_item_list[k - 1].item_id > 0 then
				v:SetData(reward_item_list[k - 1])
				v:SetActive(true)
				self.node_list["item_cell"..k]:SetActive(true)
			end
		end
	end

	if self.monster_id and self.monster_id > 0 then
		local monster_cfg = BossWGData.Instance:GetMonsterInfo(self.monster_id)
		if monster_cfg and not IsEmptyTable(fb_info) then
			if fb_info.is_finish < fb_info.total_boss_num then
				self.node_list['rich_text_2'].text.text =  string.format(des_tab.Label2, monster_cfg.name, fb_info.is_finish, fb_info.total_boss_num)
			else
				self.node_list['rich_text_2'].text.text =  string.format(des_tab.Label1, monster_cfg.name, fb_info.is_finish, fb_info.total_boss_num)
			end
		end
	end

end

function TransFerDemonView:InitMonsterId()
	if not self.monster_id then
		local monster_list = Scene.Instance:GetMonsterList()
		if IsEmptyTable(monster_list) then
			return
		end

		for k,v in pairs(monster_list) do
			self.monster_id = v:GetVo().monster_id
			break
		end
	end
end

function TransFerDemonView:CloseCallBack()
	if self.node_list["TargetContent_root"] then
	    self.node_list["TargetContent_root"]:SetActive(false)
	    self.node_list["TargetContent_root"].transform:SetParent(self.root_node_transform.transform, false)
	end
end

