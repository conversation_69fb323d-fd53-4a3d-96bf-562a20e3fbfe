--铭文item类
MingWenItemRender = MingWenItemRender or BaseClass(BaseRender)

local SHOW_TEXT_MODE1 = 6    --两个字的模式
local SHOW_TEXT_MODE2 = 9    --3个字的模式
local SHOW_TEXT_MODE3 = 12   --四个字的模式
local gradient_color_list = {[1] = {"#b9f6ff", "#ffffff"}, [2] = {"#b9f6ff", "#ffffff"}, [3] = {"#c6bbff", "#ffffff"},
 [4] = {"#fffbb9", "#ffffff"}, [5] = {"#ffcbb6", "#ffffff"}, [6] = {"#fffbb9", "#ffffff"}, [7] = {"#fffbb9", "#ffffff"},
 [8] = {"#b1fff5", "#ffffff"}, [9] = {"#fffbb9", "#ffffff"}, [10] = {"#b9fff3", "#ffffff"}}

 local outline_color_list = {[1] = "2348A4", [2] = "2348A4", [3] = "5D23A4",
 [4] = "95491B", [5] = "741F31", [6] = "951B5D", [7] = "7F4F15", [8] = "394998",
 [9] = "88681C", [10] = "157B8D"}

function MingWenItemRender:__init(instance)
	if nil == self.root_node then
		local bundle, asset = ResPath.GetMingWenPrefab("mingwen_item_cell")
        local u3dobj = U3DObject(ResPoolMgr:TryGetGameObject(bundle, asset))
		self:SetInstance(u3dobj)
		self.is_use_objpool = true
	end

	if instance then
		self:SetInstanceParent(instance)
	end

    self.root_node = self.node_list["root_cell"]
	self.btn_block = self.root_node:GetComponent("UIBlock")
	self.be_select = false  --是否被为选中状态
	self.is_only_show = true
	self.is_show_item_tip = true
	self.is_not_can_equip = false
	self.active_cell_type = nil

	self.up_level_time_key = nil
	self.inlay_time_key = nil

	self.is_run = 0

	XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind1(self.OnClickBtnGo, self))
	XUI.AddClickEventListener(self.node_list.btn_show_item_tip, BindTool.Bind1(self.OnClickItemCallBack, self))

    if nil == self.xq_attr_list then
        self.xq_attr_list = {}
        local attr_num = self.node_list.xq_attr_list.transform.childCount
        for i = 1, attr_num do
            local cell = MingWenItemAttrRender.New(self.node_list.xq_attr_list:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.xq_attr_list[i] = cell
        end
    end
end

function MingWenItemRender:LoadCallBack()
	--self.rune_item_cell = ItemCell.New(self.node_list["cell_pos"])
	--气泡上下浮动动画
	-- if not self.arrow_tweener then
	-- 	self.arrow_tweener = self.node_list["unlock_bg"].rect:DOAnchorPosY(self.node_list.unlock_bg.rect.anchoredPosition.y + 5, 0.8)
	-- 	self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
	-- 	self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	-- end

	--按钮绑定
	--self.node_list["click_button"].button:AddClickListener(BindTool.Bind(self.OnClickRuneCell,self))
end

--释放掉
function MingWenItemRender:__delete()
	self.root_node = nil
	--if self.rune_item_cell then
	--	self.rune_item_cell:DeleteMe()
	--	self.rune_item_cell = nil
	--end
	self.be_select = nil
	self.active_cell_type = nil
	self.active_cell_item = nil

	self:CleanInlayTimer()
	self:CleanUpLevelTimer()
	self.inlay_time_key = nil
	self.up_level_time_key = nil
	-- if self.arrow_tweener then
	-- 	self.arrow_tweener:Kill()
	-- 	self.arrow_tweener = nil
	-- end
    if self.xq_attr_list then
        for k,v in pairs(self.xq_attr_list) do
            v:DeleteMe()
        end
        self.xq_attr_list = nil
    end
end

function MingWenItemRender:OnFlush()
    if not self.data then
        return
    end
 	local data = {}
	local item_data = {}
	local type = -1
	local is_special = false
    local tianxiange_fb_pass_level = 0
    local img_name = ""
	local is_have_add_value = false

	if self.is_only_show then
		item_data = MingWenWGData.Instance:GetMingWenItemCellDataByItemId(self.data.item_id)
		type = item_data.type
	else
		self.up_level_time_key = "RuneCellUpLevel" .. self.data.slot_index
		self.inlay_time_key = "RuneCellInlay" .. self.data.slot_index
		local slot_cfg = MingWenWGData.Instance:GetPosySlotCfgBySlotIndex(self.data.slot_index)
		type = slot_cfg.put_type
		data = MingWenWGData.Instance:GetEquipPosyDataBySlot(self.data.slot_index)  --根据铭文下标 得到数据
		self.is_open, tianxiange_fb_pass_level = MingWenWGData.Instance:GetSlotIndexIsOpen(self.data.slot_index)  --得到铭文槽是否开启

		local is_red = MingWenWGData.Instance:GetIsSlotRedByIndex(self.data.slot_index)
		self.node_list["img_remind"]:SetActive(is_red)

		if is_red then
			self.is_run = MingWenWGData.Instance:GetSlotIsHasBetterByIndex(self.data.slot_index)
		else
			self.is_run = 0
		end

    	self.node_list.text_replace:SetActive(self.is_run > 0)
	end

	self.node_list.cap_bg:SetActive(type ~= 1)	--狗粮经验卡不显示战力

	is_special = MingWenWGData.Instance:GetIsSpecialByType(type) == 1 --特殊铭文
    local limit_cfg = MingWenWGData.Instance:GetPosyLimitCfgByType(type)
    local desc_node_name = ""
	self.is_not_can_equip = false

    if is_special then
        self.node_list["select_special"]:SetActive(true)
        self.node_list["select_normal"]:SetActive(false)
        img_name = "a3_wgf_ts_k_"
    else
        self.node_list["select_special"]:SetActive(false)
        self.node_list["select_normal"]:SetActive(true)
        img_name = "a3_wgf_pt_k_"
    end

    if (not IsEmptyTable(data) and data.posy.item_id > 0) or self.is_only_show then  --不为空判断
        self.node_list["have_data_state"]:SetActive(true)
        self.node_list["no_data_state"]:SetActive(false)

        self.node_list["no_data_pic"]:SetActive(false)
        self.node_list["mingwen_pic"]:SetActive(true)

        local bundle, asset = ResPath.GetRawImagesPNG("a3_wgf_pic_" .. limit_cfg.picture)
        self.node_list["mingwen_pic"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["mingwen_pic"].raw_image:SetNativeSize()
        end)

		local quality
		local attr_list

		if self.is_only_show then
			quality = item_data.quality
			if self.data.level and self.data.level > 0 then
				attr_list = MingWenWGData.Instance:GetEquipPosyShowAttrByItemId(item_data.item_id, self.data.level)
			else
				attr_list = MingWenWGData.Instance:GetEquipPosyBaseAttrByItemId(item_data.item_id)
			end

			self.node_list["level_bg"]:SetActive(false)
			is_have_add_value = false
		else
			self.node_list["level_bg"]:SetActive(true)
			self.node_list["level_text"].text.text = data.posy.level
			if nil == self.active_cell_type then
				self.active_cell_type = data.posy.type
				self.active_cell_item = data.posy.item_id
			else
				if self.active_cell_type ~= data.posy.type then
					self.active_cell_type = data.posy.type
					--self:PlayInlayEffect()
				end

				if self.active_cell_item ~= data.posy.item_id then
					self.active_cell_item = data.posy.item_id
					--self:PlayInlayEffect()
				end
			end

			quality = data.posy.quality
			attr_list = MingWenWGData.Instance:GetEquipPosyShowAttrBySlot(self.data.slot_index, data.posy.level)
			local max_level = MingWenWGData.Instance:GetMingWenMaxLevel()
			is_have_add_value = max_level == data.posy.level and false or true
		end

        img_name = img_name .. quality
        desc_node_name = "type_desc_panel"
        self:ChangeToQualityText(self.node_list["type_desc_panel1"]:FindObj("type_desc"), quality)
        self:ChangeToQualityText(self.node_list["type_desc_panel2"]:FindObj("type_desc1"), quality)
        self:ChangeToQualityText(self.node_list["type_desc_panel2"]:FindObj("type_desc2"), quality)
		self:ChangeToQualityText(self.node_list["type_desc_panel3"]:FindObj("type_desc1"), quality)
        self:ChangeToQualityText(self.node_list["type_desc_panel3"]:FindObj("type_desc2"), quality)

		for k, v in ipairs(self.xq_attr_list) do
			v:SetData(attr_list[k])
		end

		if type == 1 and self.is_only_show then
			self.node_list["exp_type_desc"]:SetActive(true)
			local level_cfg = MingWenWGData.Instance:GetMingWenLvCfgByLv(1)
			local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(self.data.item_id)
			self.node_list["exp_type_desc"].text.text = string.format(Language.MingWenView.ExpTypeDesc, level_cfg["get_jingyan_" .. base_cfg.cost_type])
		else
			self.node_list["exp_type_desc"]:SetActive(false)
		end

		if is_have_add_value then
			RectTransform.SetAnchoredPositionXY(self.node_list.xq_attr_list.rect, 0, self.node_list.xq_attr_list.rect.anchoredPosition.y)
		else
			RectTransform.SetAnchoredPositionXY(self.node_list.xq_attr_list.rect, 26, self.node_list.xq_attr_list.rect.anchoredPosition.y)
		end

        local attr_data_list = self:GetAttrDataList(attr_list)
        local capability = AttributeMgr.GetCapability(attr_data_list)
        self.node_list["cap_value"].text.text = capability
    else

		self.node_list.text_replace:SetActive(false)

        self.node_list["have_data_state"]:SetActive(false)
        self.node_list["no_data_state"]:SetActive(true)
        self.node_list["no_data_pic"]:SetActive(true)
        self.node_list["mingwen_pic"]:SetActive(false)

        if not self.is_open then
            self.node_list["lock_state"]:SetActive(true)
            self.node_list["active_state"]:SetActive(false)
            self.node_list["lock_desc"].text.text = string.format(Language.MingWenView.PassLevelNotEnough2, tianxiange_fb_pass_level)
        else
            self.node_list["lock_state"]:SetActive(false)
            self.node_list["active_state"]:SetActive(true)
			--self.node_list["can_equip_text"]:SetActive(self.is_run == 1)
			self.node_list["can_equip_img"]:SetActive(self.is_run == 1)
			self.node_list["can_not_equip_text"]:SetActive(self.is_run ~= 1)
			self.is_not_can_equip = self.is_run ~= 1
        end
        img_name = img_name .. 0
        desc_node_name = "type_desc_no_data_panel"
    end

    self.node_list.name.text.text = limit_cfg.name
    local bundle, asset = ResPath.GetRawImagesPNG(img_name)
	self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["bg"].raw_image:SetNativeSize()
	end)

    local text1, text2
    local type_name = limit_cfg.type_name

    if #type_name == SHOW_TEXT_MODE1 then
        text1 = type_name:sub(1, 3)
        text2 = type_name:sub(4, 6)

        self.node_list[desc_node_name .. 1]:SetActive(true)
        self.node_list[desc_node_name .. 2]:SetActive(false)
		self.node_list[desc_node_name .. 3]:SetActive(false)
        self.node_list[desc_node_name .. 1]:FindObj("type_desc").text.text = string.format(Language.MingWenView.type_desc, text1, text2)
    elseif #type_name == SHOW_TEXT_MODE2 then
		text1 = type_name:sub(1, 3)
		text2 = type_name:sub(4, 9)

        self.node_list[desc_node_name .. 2]:SetActive(true)
        self.node_list[desc_node_name .. 1]:SetActive(false)
		self.node_list[desc_node_name .. 3]:SetActive(false)
        self.node_list[desc_node_name .. 2]:FindObj("type_desc1").text.text = text1
        self.node_list[desc_node_name .. 2]:FindObj("type_desc2").text.text = text2
	elseif #type_name == SHOW_TEXT_MODE3 then
		text1 = type_name:sub(1, 6)
		text2 = type_name:sub(7, 12)

        self.node_list[desc_node_name .. 3]:SetActive(true)
        self.node_list[desc_node_name .. 1]:SetActive(false)
		self.node_list[desc_node_name .. 2]:SetActive(false)
        self.node_list[desc_node_name .. 3]:FindObj("type_desc1").text.text = text1
        self.node_list[desc_node_name .. 3]:FindObj("type_desc2").text.text = text2
    end

	self.node_list.btn_show_item_tip:SetActive(self.is_show_item_tip)
end

function MingWenItemRender:GetAttrDataList(attr_list)
	local attr_data_list = AttributePool.AllocAttribute()
	local attr_str

	for k, v in pairs(attr_list) do
		-- if v.attr_str == GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_WUQI_JC_PER then
		-- 	if v.attr_value > 0 then
		-- 		local weapon_base_attr = EquipWGData.Instance:GetWeaponBaseAttrTab()
		-- 		v.attr_value = weapon_base_attr.gongji * v.attr_value *  0.0001
		-- 	end

		-- 	attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(GameEnum.BASE_CHARINTATTR_TYPE_GONGJI)
		-- elseif v.attr_str == GameEnum.BASE_CHARINTATTR_TYPE_POJIA_WUQI_JC_PER then
		-- 	if v.attr_value > 0 then
		-- 		local weapon_base_attr = EquipWGData.Instance:GetWeaponBaseAttrTab()
		-- 		v.attr_value = weapon_base_attr.pojia * v.attr_value *  0.0001
		-- 	end

		-- 	attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(GameEnum.BASE_CHARINTATTR_TYPE_POJIA)
		-- elseif v.attr_str == GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_SM_JC_PER then
		-- 	if v.attr_value > 0 then
		-- 		local equip_base_attr = EquipWGData.Instance:GetEquipBaseAttrTab()
		-- 		v.attr_value = equip_base_attr.maxhp * v.attr_value *  0.0001
		-- 	end

		-- 	attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(GameEnum.BASE_CHARINTATTR_TYPE_MAXHP)
		-- elseif v.attr_str == GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_GJ_JC_PER then
		-- 	if v.attr_value > 0 then
		-- 		local equip_base_attr = EquipWGData.Instance:GetEquipBaseAttrTab()
		-- 		v.attr_value = equip_base_attr.gongji * v.attr_value *  0.0001
		-- 	end

		-- 	attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(GameEnum.BASE_CHARINTATTR_TYPE_GONGJI)
		-- elseif v.attr_str == GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_FY_JC_PER then
		-- 	if v.attr_value > 0 then
		-- 		local equip_base_attr = EquipWGData.Instance:GetEquipBaseAttrTab()
		-- 		v.attr_value = equip_base_attr.fangyu * v.attr_value *  0.0001
		-- 	end

		-- 	attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(GameEnum.BASE_CHARINTATTR_TYPE_FANGYU)
		-- elseif v.attr_str == GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_PJ_JC_PER then
		-- 	if v.attr_value > 0 then
		-- 		local equip_base_attr = EquipWGData.Instance:GetEquipBaseAttrTab()
		-- 		v.attr_value = equip_base_attr.pojia * v.attr_value *  0.0001
		-- 	end

		-- 	attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(GameEnum.BASE_CHARINTATTR_TYPE_POJIA)
		-- else
		-- 	attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_str)
		-- end
		attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_str)
		attr_data_list[attr_str] = attr_data_list[attr_str] + v.attr_value
	end

	return attr_data_list
end

function MingWenItemRender:ChangeToQualityText(text, quality)
	-- local gradient = text:GetOrAddComponent(typeof(UIGradient))
	-- gradient.Color1 = Str2C3b(gradient_color_list[quality][1])
	-- gradient.Color2 = Str2C3b(gradient_color_list[quality][2])

	text.text.colorGradient = TMPro.VertexGradient(
		Str2C3b(gradient_color_list[quality][1]),
		Str2C3b(gradient_color_list[quality][1]),
		Str2C3b(gradient_color_list[quality][2]),
		Str2C3b(gradient_color_list[quality][2])
	)

    -- local out_line = text:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
    -- out_line.effectColor = Str2C3b(outline_color_list[quality])

	TMPUtil.UpdateMat(text.text, "TMP_Font_SDF_O1_" .. outline_color_list[quality])
end

function MingWenItemRender:SetIsOnlyShow(bool)
    self.is_only_show = bool
end

function MingWenItemRender:SetIsShowItemTip(bool)
    self.is_show_item_tip = bool
end

function MingWenItemRender:OnClickItemCallBack()
	if self.is_show_item_tip then
		local cell_data = {}
		cell_data.item_id = self.data.item_id
		TipWGCtrl.Instance:OpenItem(cell_data)
	end
end

function MingWenItemRender:SetButtonBlockEnable(bool)
    self.btn_block.enabled = bool
end

--铭文item选中刷新
function MingWenItemRender:FlushHL(index)
    if not self.data then
        return
    end

	self.node_list["select_state"]:SetActive(self.index == index) --传进的index如果和自己的index一样 就激活选中特效
	self.be_select = self.index == index  --true
end

function MingWenItemRender:OnClickRuneCell()
	if self.is_open then  --该铭文槽位  是否开启
		self.select_call_back(self.data.slot_index)
	else  --没有开启点击的话  就弹出提示文字
		local is_open,tianxiange_fb_pass_level = MingWenWGData.Instance:GetSlotIndexIsOpen(self.data.slot_index)
		--提示文字：  去通关修真路 xx层
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.MingWenView.PassLevelNotEnough,tianxiange_fb_pass_level))
	end
end

function MingWenItemRender:OnClickBtnGo()
	ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_welkin)
end

--------------------- 播放铭文升级特效 start ---------------------
function MingWenItemRender:PlayUpLevelEffect()
	self.node_list.effect_chenggong_cell:SetActive(false)
	self.node_list.effect_chenggong_cell:SetActive(true)
	self:DoUpLevelEffectHide()
end

function MingWenItemRender:DoUpLevelEffectHide()
	self:CleanUpLevelTimer()
	CountDownManager.Instance:AddCountDown(self.up_level_time_key, nil,
	function()
		self.node_list.effect_chenggong_cell:SetActive(false)
		CountDownManager.Instance:RemoveCountDown(self.up_level_time_key)
	end,
	nil, 1, nil)
end

function MingWenItemRender:CleanUpLevelTimer()
	if CountDownManager.Instance:HasCountDown(self.up_level_time_key) then
		CountDownManager.Instance:RemoveCountDown(self.up_level_time_key)
	end
end
--------------------- 播放铭文升级特效  end  ---------------------

--------------------- 播放铭文镶嵌特效 start ---------------------
function MingWenItemRender:PlayInlayEffect()
	self.node_list.effect_active_cell:SetActive(false)
	self.node_list.effect_active_cell:SetActive(true)
	self:DoInlayEffectHide()
end

function MingWenItemRender:DoInlayEffectHide()
	self:CleanInlayTimer()
	CountDownManager.Instance:AddCountDown(self.inlay_time_key, nil,
	function()
		self.node_list.effect_active_cell:SetActive(false)
		CountDownManager.Instance:RemoveCountDown(self.inlay_time_key)
	end,
	nil, 1, nil)
end

function MingWenItemRender:CleanInlayTimer()
	if CountDownManager.Instance:HasCountDown(self.inlay_time_key) then
		CountDownManager.Instance:RemoveCountDown(self.inlay_time_key)
	end
end
--------------------- 播放铭文镶嵌特效  end  ---------------------

MingWenItemAttrRender = MingWenItemAttrRender or BaseClass(BaseRender)

function MingWenItemAttrRender:__init()

end

function MingWenItemAttrRender:__delete()

end

function MingWenItemAttrRender:LoadCallBack()

end

function MingWenItemAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	self.view:SetActive(true)

    local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)

    local per_desc = is_per and "%" or ""
    local value_str = (is_per and self.data.attr_value / 100 or self.data.attr_value) or 0
    self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, true, false) .. ":"
    self.node_list.attr_value.text.text = value_str .. per_desc

    if self.data.add_value and self.data.add_value > 0 then
        value_str = is_per and self.data.add_value / 100 or self.data.add_value
        self.node_list.add_value.text.text = "+ " ..  value_str .. per_desc
		self.node_list.add_value:SetActive(true)
	else
		self.node_list.add_value:SetActive(false)
	end

	-- self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, true, false) .. ":"
	-- self.node_list.attr_value.text.text = self.data.attr_value

	-- if(self.data.add_value > 0) then
	-- 	self.node_list.add_value.text.text ="+ " .. self.data.add_value
	-- 	self.node_list.add_value:SetActive(true)
	-- else
	-- 	self.node_list.add_value:SetActive(false)
	-- end
end
