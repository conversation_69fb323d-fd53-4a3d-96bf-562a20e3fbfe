﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System;

public class ShrinkButton : MonoBehaviour
{
    [SerializeField]
    private int normalWidth;

    [SerializeField]
    private int spaceing;

    [SerializeField]
    private float changeTime = 0.5f;

    [SerializeField]
    private GameObject redPoint;

    [SerializeField]
    private ShrinkTransList[] transList;

    private List<Tween> tweenList = new List<Tween>();

    public void ClearTween()
    {
        int count = tweenList.Count;
        for (int i = 0; i < count; i++)
        {
            var tween = tweenList[i];
            if (tween != null)
            {
                tween.Kill();
            }
        }
        tweenList.Clear();
    }

    void Start()
    {
        var toggle = this.transform.GetComponent<Toggle>();
        if (null != toggle)
        {
            ShrinkOrUnShrink(toggle.isOn);
            toggle.onValueChanged.AddListener((bool value) => ToggleIsOnChange(value));
        }
    }

    void ToggleIsOnChange(bool value)
    {
        ClearTween();
        ShrinkOrUnShrink(value);
        if (value)
        {
            CheckRedPoint();
        }
    }

    public void CheckRedPoint()
    {
        int flag = GetRedPoint();
        if (redPoint != null)
        {
            redPoint.SetActive(flag == 1);
        }
    }

    public int GetRedPoint()
    {
        int flag = 0;
        int count = transList.Length;

        for (int i = 0; i < count; i++)
        {
            var trans = transList[i];
            var parent_obj = trans.GetGameObj();
            var red_point_obj = trans.GetRedPointObj();
            if (red_point_obj != null && red_point_obj.activeSelf && parent_obj.activeInHierarchy)
            {
                flag = 1;
                break;
            }
        }
        return flag;
    }

    //开始播放动画
    void ShrinkOrUnShrink(bool value)
    {
        int change_width = -spaceing;
        int move_pos_x = 0;
        if (value == true)
        {
            change_width = normalWidth;
            move_pos_x = -(normalWidth / 2);
        }
        int count = transList.Length;
        for (int i = 0; i < count; i++)
        {
            var obj = transList[i];
            if (obj.GetGameObj() == null)
            {
                continue;
            }
            var move = obj.GetMoveObj();
            if (move == null)
            {
                continue;
            }
            var move_rect = move.GetComponent<RectTransform>();
            if (move_rect == null)
            {
                continue;
            }

            var rect = obj.GetGameObj().GetComponent<RectTransform>();
            var canvas_group = rect.GetComponent<CanvasGroup>();

            var my_tween = rect.DOSizeDelta(new Vector2(change_width, rect.sizeDelta.y), changeTime);
            my_tween.SetEase(Ease.InOutQuad);

            var ch_tween = move_rect.DOAnchorPosX(move_pos_x, changeTime);
            if (value)
            {
                move_rect.gameObject.SetActive(true);
            }
            else
            {
                ch_tween.OnComplete(() =>
                {
                    move_rect.gameObject.SetActive(false);
                });
            }

            my_tween.OnUpdate(() =>
            {
                var bili = (Mathf.Abs(move_rect.anchoredPosition.x)) / (normalWidth / 2);
                if (canvas_group != null)
                {
                    canvas_group.alpha = bili;
                }
            });

            my_tween.SetUpdate(true);
            ch_tween.SetUpdate(true);
            tweenList.Add(my_tween);
            tweenList.Add(ch_tween);
        }
    }
}