--废弃的
MiJingBossView = MiJingBossView or BaseClass(SafeBaseView)

function MiJingBossView:__init()
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_mj_boss")
	self.cur_boss_id = 0
	self.boss_cell_list = {}
	self.boss_data = {}
	self.view_cache_time = 0
end

function MiJingBossView:__delete()
	-- body
end

function MiJingBossView:LoadCallBack()
	local function callback(show_node)
		ResMgr:LoadGameobjSync("uis/view/boss_ui_prefab", "BossList",
				function (obj)
					if self:IsOpen() then
						obj.transform:SetParent(show_node.transform,false)
						obj = U3DObject(obj)
						self.data_node = FuBenTFTaskDataList.New(obj)
						self:CreateBossList()
						MainuiWGCtrl.Instance:SetTaskPanel(false)
						self:Flush()
					else
						self:Close()
					end
				end)
	end
	MainuiWGCtrl.Instance:GetTaskMaskRootNode(callback)
	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text8)
end

function MiJingBossView:OpenCallBack()
	MainuiWGCtrl.Instance:SetTaskPanel(false)
end

function MiJingBossView:ReleaseCallBack()
	if self.boss_list then
		self.boss_list:DeleteMe()
		self.boss_list = nil
	end

	for _, v in pairs(self.boss_cell_list) do
        if v then
            v:DeleteMe()
        end
    end
    self.boss_cell_list = {}

	if self.data_node then
		local obj = self.data_node.node_list.boss_root_node.gameObject
		self.data_node:DeleteMe()
		ResMgr:Destroy(obj)
		self.data_node = nil
	end
end

function MiJingBossView:CloseCallBack()
	MainuiWGCtrl.Instance:ResetTaskPanel()
end

function MiJingBossView:ShowIndexCallBack()

end

function MiJingBossView:OnFlush()
	self.boss_data = BossWGData.Instance:GetCurBossListBySceneId(Scene.Instance:GetSceneId())
	if self.boss_data == nil then return end
	if self.data_node == nil then return end
	self.data_node.node_list.BossList.scroller:RefreshAndReloadActiveCellViews(true)
	local physical = BossWGData.Instance:GetNowOwnPower()
	local _,max_physical = BossWGData.Instance:GetCanGainPowerNum()
	self.data_node.node_list.Text.text.text = string.format(Language.Boss.Physical,physical,max_physical)
end

function MiJingBossView:CreateBossList()
    local list_view_delegate = self.data_node.node_list.BossList.list_simple_delegate
    list_view_delegate.NumberOfCellsDel = function()
        return #self.boss_data
    end
    list_view_delegate.CellRefreshDel = BindTool.Bind(self.RefresBossListView, self)
end

-- 刷新Boss列表
function MiJingBossView:RefresBossListView(cell, data_index, cell_index)
    data_index = data_index + 1
    local icon_cell = self.boss_cell_list[cell]
    if icon_cell == nil then
        icon_cell = MJBossFollowRender.New(cell.gameObject, self)
        self.boss_cell_list[cell] = icon_cell
    end
    local data = self.boss_data[data_index]
    icon_cell:SetIndex(data_index)
    icon_cell:SetData(data)
    icon_cell:FlushHl()
end

function MiJingBossView:GetCurIndex()
    return self.cur_boss_id
end

function MiJingBossView:SetCurIndex(boss_id)
    self.cur_boss_id = boss_id
end

function MiJingBossView:FlushAllHl()
    for k,v in pairs(self.boss_cell_list) do
        v:FlushHl()
    end
end

--------------------------------------------------------------------------------
MJBossFollowRender = MJBossFollowRender or BaseClass(BaseRender)

function MJBossFollowRender:__init(instance, parent)
	XUI.AddClickEventListener(self.node_list.BtnSelf,BindTool.Bind(self.OnClickBossRender, self))
	self.boss_text = nil
	self.refresh_event = nil
	self.parent = parent
end

function MJBossFollowRender:__delete()
	self.boss_text = nil
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
	self.parent = nil
end

function MJBossFollowRender:OnClickBossRender()
    local boss_data = self.data
    if nil ~= boss_data then
        Scene.Instance:ClearAllOperate()

        local sence_id = Scene.Instance:GetSceneId()
		local role = Scene.Instance:GetMainRole()
		local role_x,role_y = role:GetLogicPos()

		BossWGData.Instance:SetCurSelectBossID(0 , 0, boss_data.boss_id)

        if  role_x == boss_data.x_pos and role_y == boss_data.y_pos then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        else
        	GuajiWGCtrl.Instance:StopGuaji()
        	MoveCache.SetEndType(MoveEndType.FightByMonsterId)
			GuajiCache.monster_id = boss_data.boss_id
			MoveCache.param1 = boss_data.boss_id
			local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)

			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
				-- 切换挂机模式，以优先选择玩家 --
				GuajiWGCtrl.Instance:StopGuaji()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)


			GuajiWGCtrl.Instance:MoveToPos(sence_id, boss_data.x_pos, boss_data.y_pos, range)
        end
    self.parent:SetCurIndex(self.data.boss_id)
    self.parent:FlushAllHl()
    end

end

function MJBossFollowRender:OnFlush()
	if next(self.data) == nil and not self.data then return end
	local str = ""
    str = self.data.boss_name.." Lv.".. self.data.boss_level

	self.boss_text = str
	self:RefreshRemainTime()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
	self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),1)

	local boss_data = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_id)
	if boss_data ~= nil then
		local role_level = GameVoManager.Instance:GetMainRoleVo().level
	end
end

function MJBossFollowRender:RefreshRemainTime()
	if self.data then
		local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
		if boss_info == nil then return end
		local time_txt
		if boss_info.next_refresh_time -  TimeWGCtrl.Instance:GetServerTime() > 1 then
			time_txt =  ToColorStr(TimeUtil.FormatSecond(boss_info.next_refresh_time - TimeWGCtrl.Instance:GetServerTime(), 3),COLOR3B.RED)
		else
			time_txt =  Language.Boss.BossRefresh--ToColorStr(Language.Boss.BossRefresh,COLOR3B.DEFAULT)
		end
		if self.node_list.TextDesc.text then
			self.node_list.TextDesc.text.text = self.boss_text
		end
		if self.node_list.TimeDesc.text then
			self.node_list.TimeDesc.text.text = time_txt
		end
	end
end

function MJBossFollowRender:FlushHl()
	if self.node_list["SelectLigth"] then
		self.node_list["SelectLigth"]:SetActive(self.parent:GetCurIndex() == self.data.boss_id)
	end
end
