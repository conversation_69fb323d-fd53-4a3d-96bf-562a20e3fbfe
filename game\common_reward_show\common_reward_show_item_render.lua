RewardShowItemRender = RewardShowItemRender or BaseClass(BaseRender)

function RewardShowItemRender:__init(instance, top_res_name)
	self.parent = instance

	self.item_render_tab = {}
	self.asset_bundle = nil
	self.asset_name = nil

	local top_bundle_name, top_asset_name = ResPath.GetRewardShowTipPrefab(top_res_name)
	self:LoadAsset(top_bundle_name, top_asset_name, instance.transform)
end

function RewardShowItemRender:__delete()
	self.parent = nil

	for k, v in pairs(self.item_render_tab) do
		v:DeleteMe()
	end
	self.item_render_tab = {}
end

function RewardShowItemRender:SetItemRender(item_render)
	self.item_render = item_render
end

function RewardShowItemRender:SetAssetBundle(asset_bundle, asset_name)
	self.asset_bundle = asset_bundle
	self.asset_name = asset_name
end

function RewardShowItemRender:SetData(data)
	self.data = data
	local reward_item_list = data.reward_item_list
	local render_length = #self.item_render_tab
	local data_length = #reward_item_list
	local length = data_length > render_length and data_length or render_length
	for i = 1, length do
		local item_render = self.item_render_tab[i]
		if nil == item_render then
			if self.asset_bundle and self.asset_name then
				item_render = self.item_render.New(self.parent)
				self.item_render_tab[i] = item_render
				item_render:LoadAsset(self.asset_bundle, self.asset_name, self.parent.transform)
			else
				item_render = self.item_render.New(self.parent)
				self.item_render_tab[i] = item_render
			end
		end

		if reward_item_list[i] then
			item_render:SetIndex(i)
			item_render:SetData(reward_item_list[i])
			item_render:SetActive(true)
		else
			item_render:SetActive(false)
		end
	end

	if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function RewardShowItemRender:OnFlush()
	self.node_list.title.text.text = self.data.title_text
end

------------------------------------RewardShowProbabilityItemRender--------------------------------------
RewardShowProbabilityItemRender = RewardShowProbabilityItemRender or BaseClass(BaseRender)

function RewardShowProbabilityItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function RewardShowProbabilityItemRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function RewardShowProbabilityItemRender:OnFlush()
	self.item_cell:SetData(self.data.item)
	self.node_list.probability_text.text.text = self.data.probability_text
end