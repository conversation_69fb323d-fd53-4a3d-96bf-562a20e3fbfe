-- 解锁战令界面
PositionalWarfareUnlockZhanLingView = PositionalWarfareUnlockZhanLingView or BaseClass(SafeBaseView)

function PositionalWarfareUnlockZhanLingView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_unlock_zhanling_view")
end

function PositionalWarfareUnlockZhanLingView:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncBaseGrid.New()
        self.reward_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.reward_list})
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_buy_zhanling, BindTool.Bind(self.OnClickBugZhanLingBtn, self))
end

function PositionalWarfareUnlockZhanLingView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function PositionalWarfareUnlockZhanLingView:OnFlush()
    local reward_cfg = PositionalWarfareWGData.Instance:GetCurGroupCfg()
    if IsEmptyTable(reward_cfg) then
        return
    end

    self.reward_list:SetDataList(reward_cfg.order_preview)
    local get_higer_order = PositionalWarfareWGData.Instance:GetHigerOrderRewardFlag()
    self.node_list.flag_is_get:CustomSetActive(get_higer_order)
    self.node_list.btn_buy_zhanling:CustomSetActive(not get_higer_order)

    if not get_higer_order then
        local price = RoleWGData.GetPayMoneyStr(reward_cfg.order_price, reward_cfg.rmb_type, reward_cfg.rmb_seq)
        self.node_list.buy_text.text.text = string.format(Language.PositionalWarfare.ZhanLingBuyLevelBtn, price)
    end

    if reward_cfg.order_return and reward_cfg.order_return > 0 then
        self.node_list.desc_lingyu.text.text = string.format(Language.PositionalWarfare.ZhanLingBackLingYu, reward_cfg.order_return)
    else
        self.node_list.desc_lingyu.text.text = ""
    end
end

function PositionalWarfareUnlockZhanLingView:OnClickBugZhanLingBtn()
    local reward_cfg = PositionalWarfareWGData.Instance:GetCurGroupCfg()

    if IsEmptyTable(reward_cfg) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(reward_cfg.order_price, reward_cfg.rmb_type, reward_cfg.rmb_seq)
    self:Close()
end