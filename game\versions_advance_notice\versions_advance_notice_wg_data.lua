VersionsAdvanceNoticeWGData = VersionsAdvanceNoticeWGData or BaseClass()

function VersionsAdvanceNoticeWGData:__init()
	if VersionsAdvanceNoticeWGData.Instance then
		error("[VersionsAdvanceNoticeWGData] Attempt to create singleton twice!")
		return
	end

	VersionsAdvanceNoticeWGData.Instance = self

	local face_book_cfg = ConfigManager.Instance:GetAutoConfig("face_book_auto")
    self.notice_cfg = face_book_cfg.advance_notice
    self.notice_map_cfg = ListToMap(self.notice_cfg, "type")
	self.other_cfg = face_book_cfg.other[1]
	self.activity_notice_cfg = face_book_cfg.activity_notice
	self:InitCheckList()
end

function VersionsAdvanceNoticeWGData:__delete()
	VersionsAdvanceNoticeWGData.Instance = nil
end

function VersionsAdvanceNoticeWGData:GetActivityNoticeCfg()
	return self.activity_notice_cfg
end

-- 初始化需要检测开启的功能 活动
function VersionsAdvanceNoticeWGData:InitCheckList()
	self.fun_check_list = {}
	self.act_check_list = {}
	self.entrance_check_list = {}

	local max_day = 0
	for k,v in pairs(self.notice_cfg) do
		if v.fun_name ~= "" then
			self.fun_check_list[v.fun_name] = true
		end

		if v.fun_name == "" and v.act_id ~= "" and v.act_id > 0 then
			self.act_check_list[v.act_id] = true
		end

		if v.show_open_server_day > max_day then
			max_day = v.show_open_server_day
		end
	end

	for k,v in pairs(self.notice_cfg) do
		if v.show_open_server_day == max_day then
			table.insert(self.entrance_check_list, v)
		end
	end
end

function VersionsAdvanceNoticeWGData:GetEntranceShowLevel()
	return self.other_cfg.van_level
end

function VersionsAdvanceNoticeWGData:GetEntranceCheckList()
	return self.entrance_check_list
end

-- 检测入口是否需要显示
function VersionsAdvanceNoticeWGData:CheckEntranceIsShow()
	local role_level = RoleWGData.Instance.role_vo.level
	local need_level = self:GetEntranceShowLevel()
	if role_level < need_level then
		return false
	end

	local cur_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k, v in ipairs(self.notice_cfg) do
		if cur_server_day >= v.show_open_server_day then
			local is_open = FunOpen.Instance:GetFunIsOpened(v.fun_name)
			if not is_open then
				return true
			end
		end
	end

	return false
end

function VersionsAdvanceNoticeWGData:GetIsFun(fun_name)
	return self.fun_check_list[fun_name]
end

function VersionsAdvanceNoticeWGData:GetIsAct(act_id)
	return self.act_check_list[act_id]
end

function VersionsAdvanceNoticeWGData:GetNoticeCfgByType(type)
	return self.notice_map_cfg[type]
end

function VersionsAdvanceNoticeWGData:GetShowList()
    local show_list = {}
    local item_list_length
	local cur_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

    for k, v in ipairs(self.notice_cfg) do
		local is_open = FunOpen.Instance:GetFunIsOpened(v.fun_name)
		if cur_server_day >= v.show_open_server_day and not is_open then
	        local data = {sort = v.sort, type = v.type, view_res = v.view_res, name = v.name,
	                    model_show_type = v.model_show_type, model_show_itemid = v.model_show_itemid,
	                    item_show_type = v.item_show_type, jump_view = v.jump_view,
	                    model_bundle_name = v.model_bundle_name, model_asset_name = v.model_asset_name,
	                    show_item_attr = v.show_item_attr, show_item_cap = v.show_item_cap,
						 cs_act_view_type = v. cs_act_view_type,}

	        local item_list = {}
	        if v.item_show_type ~= VAN_ITEM_LIST_TYPE.NONE then
	            item_list_length = #v.item_show_list
	            for i = 0, item_list_length do
	                table.insert(item_list, v.item_show_list[i])
	            end
	        end

	        data.item_list = item_list
	        data.state_table = self:GetFunAndActStateTable(v)
	        table.insert(show_list, data)
		end
    end

	if not IsEmptyTable(show_list) then
		SortTools.SortAsc(show_list, "sort")
	end

    return show_list
end

function VersionsAdvanceNoticeWGData:GetCurNotice()
	local flag = 1
	local cur_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k, v in ipairs(self.notice_cfg) do
		local is_open = FunOpen.Instance:GetFunIsOpened(v.fun_name)
		if cur_server_day >= v.show_open_server_day and not is_open then
			if self:CanAdvanceNotic(v) then
				return v.type
			end

			flag = v.type
		end
	end

	return flag
end

function VersionsAdvanceNoticeWGData:CanAdvanceNotic(cfg)
	if IsEmptyTable(cfg) then
		return false
	end

	local cur_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance.role_vo.level

	local fun_is_open, tips
	if cfg.fun_name ~= "" then
		fun_is_open, tips = FunOpen.Instance:GetFunIsOpened(cfg.fun_name, true)
	end

	-- 【各种花里胡哨的状态显示】
	if cfg.type == VAN_PANEL_TYPE.GUILD_WAR then      						-- 【仙盟争霸】
		local cross_time_cfg = BiZuoWGData.Instance:GetCrossActivityOpenTimeCfg(cfg.act_id)
		if cross_time_cfg then
			if cur_server_day + 1 == cross_time_cfg.certain_open_openserver_day then	-- 明日开启
				return true
			elseif not fun_is_open then
				return true
			end
		end
	elseif cfg.type == VAN_PANEL_TYPE.FENG_SHEN_BANG then -- 【封神榜】
		local other_info = FengShenBangWGData.Instance:GetActCfgList("other")
		if cur_server_day + 1 == other_info.open_server_day then				-- 明日开启
			return true
		else
			local now_time = TimeWGCtrl.Instance:GetServerTime()
			local start_time, clear_time, close_time = FengShenBangWGData.Instance:GetActTime()
			if now_time >= start_time and now_time <= clear_time then
				if role_level < other_info.role_level then						-- 功能开启当天等级未达到
					return true
				end
			elseif now_time < start_time then									-- 活动状态判断
				return true
			end
		end
	elseif cfg.type == VAN_PANEL_TYPE.NIGHT_WAR then      						-- 【夜战皇城】
		if not fun_is_open then
			return true
		end
	end
end

function VersionsAdvanceNoticeWGData:GetFunAndActStateTable(cfg)
	local state_table = {
		state = VAN_NOTICE_STATE.NO_NOTICE,
		desc = "",
		cd_timestamp = 0,
		btn_limit_desc = "",
		desc_color = COLOR3B.D_GREEN,
		btn_limit_callback = nil,
	}

	if IsEmptyTable(cfg) then
		return state_table
	end

	local cur_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance.role_vo.level

	local fun_is_open, tips
	if cfg.fun_name ~= "" then
		fun_is_open, tips = FunOpen.Instance:GetFunIsOpened(cfg.fun_name, true)
	end

	-- 【各种花里胡哨的状态显示】
	if cfg.type == VAN_PANEL_TYPE.GUILD_WAR then      -- 【仙盟争霸】
		local cross_time_cfg = BiZuoWGData.Instance:GetCrossActivityOpenTimeCfg(cfg.act_id)
		if cross_time_cfg then
			if cur_server_day + 1 == cross_time_cfg.certain_open_openserver_day then	-- 明日开启
				local today_rest = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
				local min = math.floor(cross_time_cfg.activity_start_time % 100)
				local hour = math.floor(cross_time_cfg.activity_start_time / 100)
				local hour_time_desc = hour < 10 and "0" .. hour or hour
				local min_time_desc = min < 10 and "0" .. min or min
				state_table.cd_timestamp = today_rest + hour * 3600 + min * 60
				state_table.desc = Language.VersionsAdvanceNotice.TomorrowOpen
				state_table.btn_limit_desc = string.format(Language.VersionsAdvanceNotice.TomorrowActOpenDesc,
															hour_time_desc .. ":" .. min_time_desc)
				state_table.state = VAN_NOTICE_STATE.ADVANCE_CD
				state_table.desc_color = COLOR3B.GREEN
			else
				if not fun_is_open then
					if RoleWGData.Instance.role_vo.guild_id == 0 then
						state_table.desc = ToColorStr(Language.Society.CheckNoGuild, COLOR3B.GREEN)
						state_table.btn_limit_callback = function()
							FunOpen.Instance:OpenViewByName(GuideModuleName.Guild)
						end
						state_table.btn_limit_desc = Language.Society.CheckNoGuild
					else
						local open_fun_cfg = FunOpen.Instance:GetFunByName(cfg.fun_name)
						if open_fun_cfg then
							local limit_desc = string.format(Language.VersionsAdvanceNotice.LevelLimit, open_fun_cfg.trigger_param) -- 功能开启当天等级未达到
							state_table.btn_limit_desc = limit_desc
							state_table.desc = ToColorStr(limit_desc, COLOR3B.GREEN)
						end
					end
					state_table.state = VAN_NOTICE_STATE.ADVANCE
				else
					local zhou_str, start_time, end_time = BiZuoWGData.Instance:GetActOpenTimeStr(cfg.act_id)
					state_table.desc = string.format(Language.VersionsAdvanceNotice.ActTime, zhou_str, start_time, end_time)
					state_table.desc = ToColorStr(state_table.desc, COLOR3B.GREEN)
				end
			end
		end
	elseif cfg.type == VAN_PANEL_TYPE.FENG_SHEN_BANG then -- 【封神榜】
		local other_info = FengShenBangWGData.Instance:GetActCfgList("other")
		if cur_server_day + 1 == other_info.open_server_day then				-- 明日开启
			local start_time = FengShenBangWGData.Instance:GetActTime()
			state_table.state = VAN_NOTICE_STATE.ADVANCE_CD
			state_table.desc = Language.VersionsAdvanceNotice.TomorrowOpen
			state_table.cd_timestamp = start_time - TimeWGCtrl.Instance:GetServerTime()
			state_table.btn_limit_desc = string.format(Language.VersionsAdvanceNotice.TomorrowActOpenDesc,
														TimeUtil.FormatHM(start_time))
		else
			local now_time = TimeWGCtrl.Instance:GetServerTime()
			local start_time, clear_time, close_time = FengShenBangWGData.Instance:GetActTime()
			if now_time >= start_time and now_time <= clear_time then
				if role_level < other_info.role_level then							-- 功能开启当天等级未达到
					local limit_desc = string.format(Language.VersionsAdvanceNotice.LevelLimit, other_info.role_level)
					state_table.btn_limit_desc = limit_desc
					state_table.desc = ToColorStr(limit_desc, COLOR3B.D_GREEN)
					state_table.state = VAN_NOTICE_STATE.ADVANCE
				else
					local start_time, clear_time, close_time = FengShenBangWGData.Instance:GetActTime()
					local end_time_str = TimeUtil.FormatSecond2MYHM(clear_time)
					local start_time_str = TimeUtil.FormatSecond2MYHM(start_time)
					state_table.desc = string.format("%s — %s", start_time_str, end_time_str)
					state_table.desc = string.format(Language.VersionsAdvanceNotice.DescType[1], ToColorStr(state_table.desc, COLOR3B.D_GREEN))
				end
			else																	-- 活动状态判断
				if now_time > clear_time then
					state_table.btn_limit_desc = Language.FengShenBang.ActClose
					state_table.desc = string.format(Language.VersionsAdvanceNotice.DescType[1], ToColorStr(Language.Common.ActivityIsEnd, COLOR3B.D_GREEN))
				elseif now_time < start_time then
					state_table.btn_limit_desc = Language.FengShenBang.ActStandy
					state_table.state = VAN_NOTICE_STATE.ADVANCE
					local end_time_str = TimeUtil.FormatSecond2MYHM(clear_time)
					local start_time_str = TimeUtil.FormatSecond2MYHM(start_time)
					state_table.desc = string.format("%s — %s", start_time_str, end_time_str)
					state_table.desc = string.format(Language.VersionsAdvanceNotice.DescType[1], ToColorStr(state_table.desc, COLOR3B.D_GREEN))
				end
			end
		end
	elseif cfg.type == VAN_PANEL_TYPE.NIGHT_WAR then      -- 【夜战皇城】
		if fun_is_open then
			local zhou_str, start_time, end_time = BiZuoWGData.Instance:GetActOpenTimeStr(cfg.act_id)
			state_table.desc = string.format(Language.VersionsAdvanceNotice.ActTime, zhou_str, start_time, end_time)
			state_table.desc = string.format(Language.VersionsAdvanceNotice.DescType[1], ToColorStr(state_table.desc, COLOR3B.D_GREEN))
		else
			local open_fun_cfg = FunOpen.Instance:GetFunByName(cfg.fun_name)
			if open_fun_cfg then
				local level_str = RoleWGData.GetLevelString(open_fun_cfg.trigger_param)
				local limit_desc = string.format(Language.VersionsAdvanceNotice.CrossLevelLimit, open_fun_cfg.cross_gs_count, level_str)
				state_table.desc = ToColorStr(limit_desc, COLOR3B.D_GREEN)
				state_table.btn_limit_desc = limit_desc
			end
			state_table.state = VAN_NOTICE_STATE.ADVANCE
		end
	elseif cfg.type == VAN_PANEL_TYPE.NO_1 then      -- 天下第一
		local open_fun_cfg = FunOpen.Instance:GetFunByName(cfg.fun_name)
		if open_fun_cfg then
			local level_str = RoleWGData.GetLevelString(open_fun_cfg.trigger_param)
			local limit_desc = string.format(Language.VersionsAdvanceNotice.CrossLevelLimit, open_fun_cfg.cross_gs_count, level_str)
			state_table.desc = ToColorStr(limit_desc, COLOR3B.D_GREEN)
			state_table.btn_limit_desc = limit_desc
		end
		state_table.state = VAN_NOTICE_STATE.ADVANCE
	end

	return state_table
end
