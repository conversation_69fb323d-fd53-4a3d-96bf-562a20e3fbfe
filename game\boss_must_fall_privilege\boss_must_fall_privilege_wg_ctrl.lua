require("game/boss_must_fall_privilege/boss_must_fall_privilege_view")
require("game/boss_must_fall_privilege/boss_must_fall_privilege_wg_data")

BossMustFallPrivilegeWGCtrl = BossMustFallPrivilegeWGCtrl or BaseClass(BaseWGCtrl)

function BossMustFallPrivilegeWGCtrl:__init()
    if BossMustFallPrivilegeWGCtrl.Instance ~= nil then
		print_error("[BossMustFallPrivilegeWGCtrl] attempt to create singleton twice!")
		return
	end

	BossMustFallPrivilegeWGCtrl.Instance = self
    self.view = BossMustFallPrivilegeView.New(GuideModuleName.BossMustFallPrivilegeView)
    self.data = BossMustFallPrivilegeWGData.New()
    
    self:RegisterProtocol(SCVipBossExtraRewardsInfo, "OnSCVipBossExtraRewardsInfo")
end

function BossMustFallPrivilegeWGCtrl:__delete()
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    BossMustFallPrivilegeWGCtrl.Instance = nil
end

function BossMustFallPrivilegeWGCtrl:OnSCVipBossExtraRewardsInfo(protocol)
    self.data:SetVipBossExtraRewardsInfo(protocol)

    if self.view and self.view:IsOpen() then
        self.view:Flush()
    end

	MainuiWGCtrl.Instance:FlushView(0, "boss_must_full_privrlege_info")
end

function BossMustFallPrivilegeWGCtrl:SendOperateReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSVipBossEnterReq)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function BossMustFallPrivilegeWGCtrl:OpenMustFallPrivilegeView()
    if self.view and not self.view:IsOpen() then
        self.view:Open()
    end
end