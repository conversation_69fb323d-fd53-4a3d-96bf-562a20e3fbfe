function SwornView:InitSwornApplyView()
	if not self.apply_team_list then
        self.apply_team_list = AsyncListView.New(ApplyTeamListItemRender, self.node_list.apply_team_list)
    end

    if not self.apply_team_menber_list then
        self.apply_team_menber_list = AsyncListView.New(ApplyTeamMenberListItemRender, self.node_list.apply_team_menber_list)
    end
end

function SwornView:ShowSwornApplyView()
    local sworn_state = SwornWGData.Instance:GetMySwornState()
    if sworn_state ~= SwornWGData.SWORN_TYPE.HAS_SWORN then
        SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_LIST)
    end
end

function SwornView:SwornApplyViewReleaseCallBack()
    if self.apply_team_list then
        self.apply_team_list:DeleteMe()
        self.apply_team_list = nil
    end

    if self.apply_team_menber_list then
        self.apply_team_menber_list:DeleteMe()
        self.apply_team_menber_list = nil
    end
end

function SwornView:FlushSwornApplyView()
    local sworn_state = SwornWGData.Instance:GetMySwornState()
    local team_list_state = sworn_state == SwornWGData.SWORN_TYPE.NOSWORN or sworn_state == SwornWGData.SWORN_TYPE.SWORNING
    self.node_list.team_list:SetActive(team_list_state)
    self.node_list.my_team_info:SetActive(not team_list_state)

    if team_list_state then
        local team_num, team_list_data = SwornWGData.Instance:GetJieYiTeamList()
        local no_data = IsEmptyTable(team_list_data)
        self.node_list.team_info_nodata:SetActive(no_data)
        self.apply_team_list:SetDataList(team_list_data)
    else
        local menber_num, team_menber_list_data = SwornWGData.Instance:GetJieYiTeamJoinReq()
        local no_data =  IsEmptyTable(team_menber_list_data)
        self.node_list.my_team_info_nodata:SetActive(no_data)
        self.apply_team_menber_list:SetDataList(team_menber_list_data)
    end  
end

---------------------------------------------队伍列表----------------------------------
ApplyTeamListItemRender = ApplyTeamListItemRender or BaseClass(BaseRender)
function ApplyTeamListItemRender:__init()
    if not self.ph_menber_list then
        self.ph_menber_list = AsyncListView.New(ApplyMenberListItemRender, self.node_list.ph_menber_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_apply, BindTool.Bind(self.OnClickApplyBtn, self))
end

function ApplyTeamListItemRender:__delete()
    if self.ph_menber_list then
        self.ph_menber_list:DeleteMe()
        self.ph_menber_list = nil
    end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function ApplyTeamListItemRender:OnFlush()
    if IsEmptyTable(self:GetData()) then
       return  
    end

    local data = self:GetData()
    local apply_flag = data.apply_flag
    local cache_flag = SwornWGData.Instance:IsSwornApplyCacheTeam(data.jieyi_id)

    self.node_list.btn_apply:SetActive(apply_flag == 0 and not cache_flag)
    self.node_list.btn_yishenqing:SetActive(apply_flag == 1 or cache_flag)
    table.sort(data.member_list, function (a, b)
            return a.uid > b.uid
	end)

    self.ph_menber_list:SetDataList(data.member_list)
end

function ApplyTeamListItemRender:OnClickApplyBtn()
    local cannot_jieyi, cd_time = SwornWGData.Instance:GetJieYiCdTime()

    if cannot_jieyi then
        TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Sworn.JieYiCdTimeTip, TimeUtil.FormatSecondDHM2(cd_time)))
        return
    end

    local sworn_state = SwornWGData.Instance:GetMySwornState()
    if sworn_state == SwornWGData.SWORN_TYPE.NOSWORN then
        if not self.alert then
            self.alert = Alert.New()
            self.alert:SetLableString(Language.Sworn.ApplyNotSwornDesc)
			self.alert:SetOkFunc(function ()
                ViewManager.Instance:Open(GuideModuleName.SwornView, TabIndex.sworn_start)
			end)
        end

        self.alert:Open()
    elseif sworn_state == SwornWGData.SWORN_TYPE.SWORNING then
        local data = self:GetData()
        SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_REQ, data.jieyi_id)
        SwornWGData.Instance:AddSwornApplyTeamCache(data.jieyi_id)      
    end
end

-----------------------------------------------队伍中的队友-------------------------------------
ApplyMenberListItemRender = ApplyMenberListItemRender or BaseClass(BaseRender)
function ApplyMenberListItemRender:__init()
    if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end
end

function ApplyMenberListItemRender:__delete()
    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function ApplyMenberListItemRender:OnFlush()
    if IsEmptyTable(self:GetData()) then
        return  
     end
 
    local data = self:GetData()
    local active = data.uid > 0
    self.node_list.name.text.text = active and data.name or ""
    self.node_list.add:SetActive(not active)
    self.node_list.head_pos:SetActive(active)

    if active then
        self.node_list.name.text.text = data.name

        local head_data = {fashion_photoframe = data.shizhuang_photoframe}
        head_data.role_id = data.uid
        head_data.prof = data.prof
        head_data.sex = data.sex
        head_data.is_show_main = true

        self.role_head_cell:SetImgBg(data.shizhuang_photoframe > 0)
        self.role_head_cell:SetData(head_data)     
    end
end



---------------------------------------------申请入队----------------------------------
ApplyTeamMenberListItemRender = ApplyTeamMenberListItemRender or BaseClass(BaseRender)
function ApplyTeamMenberListItemRender:__init()
    if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end

    XUI.AddClickEventListener(self.node_list.btn_cancle, BindTool.Bind(self.OnClickCancleBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_agree, BindTool.Bind(self.OnClickAgreeBtn, self))
end

function ApplyTeamMenberListItemRender:__delete()
    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function ApplyTeamMenberListItemRender:OnFlush()
    if IsEmptyTable(self:GetData()) then
        return  
     end
 
    local data = self:GetData()
    self.node_list.name.text.text = data.name
    self.node_list.cap.text.text = data.capability

    local is_vis, level = RoleWGData.Instance:GetDianFengLevel(data.level)
    self.node_list.dianfeng_img:SetActive(is_vis)
    self.node_list.level_txt.text.text = level

    local head_data = {fashion_photoframe = data.shizhuang_photoframe}
    head_data.role_id = data.uid
    head_data.prof = data.prof
    head_data.sex = data.sex
    head_data.is_show_main = true

    self.role_head_cell:SetImgBg(data.shizhuang_photoframe > 0)
    self.role_head_cell:SetData(head_data)  
end

function ApplyTeamMenberListItemRender:OnClickCancleBtn()
    if IsEmptyTable(self:GetData()) then
        return  
     end

    local data = self:GetData()
    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_REQ_RET, data.uid, 0)
end

function ApplyTeamMenberListItemRender:OnClickAgreeBtn()
    if IsEmptyTable(self:GetData()) then
        return  
     end

    local data = self:GetData()
    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_REQ_RET, data.uid, 1)
end