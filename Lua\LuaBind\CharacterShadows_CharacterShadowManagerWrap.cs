﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CharacterShadows_CharacterShadowManagerWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(CharacterShadows.CharacterShadowManager), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("Setup", Setup);
		<PERSON><PERSON>unction("OnFeatureSetup", OnFeatureSetup);
		<PERSON><PERSON>Function("RegisterShadow", RegisterShadow);
		L<PERSON>RegFunction("UnregisterShadow", UnregisterShadow);
		<PERSON>.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("SHADOW_LIMIT", 25);
		<PERSON><PERSON>("onLightChanged", get_onLightChanged, set_onLightChanged);
		<PERSON>.<PERSON>ar("Instance", get_Instance, null);
		<PERSON><PERSON>("Feature", get_Feature, null);
		<PERSON><PERSON>("Light", get_Light, set_Light);
		<PERSON><PERSON>un<PERSON>("OnLightChanged", CharacterShadows_CharacterShadowManager_OnLightChanged);
		<PERSON><PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Setup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CharacterShadows.CharacterShadowManager obj = (CharacterShadows.CharacterShadowManager)ToLua.CheckObject<CharacterShadows.CharacterShadowManager>(L, 1);
			CharacterShadows.CharacterShadowFeature arg0 = (CharacterShadows.CharacterShadowFeature)ToLua.CheckObject<CharacterShadows.CharacterShadowFeature>(L, 2);
			obj.Setup(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnFeatureSetup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CharacterShadows.CharacterShadowManager obj = (CharacterShadows.CharacterShadowManager)ToLua.CheckObject<CharacterShadows.CharacterShadowManager>(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer arg0 = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 2);
			UnityEngine.Rendering.Universal.RenderingData arg1 = StackTraits<UnityEngine.Rendering.Universal.RenderingData>.Check(L, 3);
			obj.OnFeatureSetup(arg0, ref arg1);
			ToLua.PushValue(L, arg1);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RegisterShadow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CharacterShadow arg0 = (CharacterShadow)ToLua.CheckObject<CharacterShadow>(L, 1);
			CharacterShadows.CharacterShadowManager.RegisterShadow(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnregisterShadow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CharacterShadow arg0 = (CharacterShadow)ToLua.CheckObject<CharacterShadow>(L, 1);
			CharacterShadows.CharacterShadowManager.UnregisterShadow(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onLightChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.CharacterShadowManager obj = (CharacterShadows.CharacterShadowManager)o;
			CharacterShadows.CharacterShadowManager.OnLightChanged ret = obj.onLightChanged;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onLightChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.Push(L, CharacterShadows.CharacterShadowManager.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Feature(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.CharacterShadowManager obj = (CharacterShadows.CharacterShadowManager)o;
			CharacterShadows.CharacterShadowFeature ret = obj.Feature;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Feature on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Light(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.CharacterShadowManager obj = (CharacterShadows.CharacterShadowManager)o;
			UnityEngine.Transform ret = obj.Light;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Light on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onLightChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.CharacterShadowManager obj = (CharacterShadows.CharacterShadowManager)o;
			CharacterShadows.CharacterShadowManager.OnLightChanged arg0 = (CharacterShadows.CharacterShadowManager.OnLightChanged)ToLua.CheckDelegate<CharacterShadows.CharacterShadowManager.OnLightChanged>(L, 2);
			obj.onLightChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onLightChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Light(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadows.CharacterShadowManager obj = (CharacterShadows.CharacterShadowManager)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.Light = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Light on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CharacterShadows_CharacterShadowManager_OnLightChanged(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<CharacterShadows.CharacterShadowManager.OnLightChanged>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<CharacterShadows.CharacterShadowManager.OnLightChanged>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

