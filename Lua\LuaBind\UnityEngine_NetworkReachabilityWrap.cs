﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_NetworkReachabilityWrap
{
	public static void Register(LuaState L)
	{
		L.Begin<PERSON>num(typeof(UnityEngine.NetworkReachability));
		<PERSON><PERSON>("NotReachable", get_NotReachable, null);
		<PERSON><PERSON>("ReachableViaCarrierDataNetwork", get_ReachableViaCarrierDataNetwork, null);
		<PERSON><PERSON>("ReachableViaLocalAreaNetwork", get_ReachableViaLocalAreaNetwork, null);
		<PERSON><PERSON>RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.NetworkReachability>.Check = CheckType;
		StackTraits<UnityEngine.NetworkReachability>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.NetworkReachability arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.NetworkReachability), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NotReachable(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.NetworkReachability.NotReachable);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ReachableViaCarrierDataNetwork(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.NetworkReachability.ReachableViaCarrierDataNetwork);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ReachableViaLocalAreaNetwork(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.NetworkReachability.ReachableViaLocalAreaNetwork);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.NetworkReachability o = (UnityEngine.NetworkReachability)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

