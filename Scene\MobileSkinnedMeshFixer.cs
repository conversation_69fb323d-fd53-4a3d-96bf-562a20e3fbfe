using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;
using System.Linq;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// 移动端SkinnedMeshRenderer兼容性修复器
/// 解决 "Mesh has been changed to one which is not compatible with the expected mesh data size and vertex stride" 错误
/// </summary>
public class MobileSkinnedMeshFixer : MonoBehaviour
{
    [Header("移动端优化设置")]
    [SerializeField] private bool enableAutoFix = true;
    [SerializeField] private bool enableLogging = true;
    [SerializeField] private int maxBonesPerVertex = 4;
    [SerializeField] private int maxTotalBones = 32;
    
    [Header("Mesh验证设置")]
    [SerializeField] private bool validateMeshCompatibility = true;
    [SerializeField] private bool forceCPUSkinning = false;
    
    private static Dictionary<Mesh, MeshCompatibilityInfo> meshCache = new Dictionary<Mesh, MeshCompatibilityInfo>();
    
    private struct MeshCompatibilityInfo
    {
        public int vertexCount;
        public int vertexStride;
        public VertexAttributeDescriptor[] vertexAttributes;
        public bool isCompatibleWithMobile;
    }
    
    void Start()
    {
        if (enableAutoFix)
        {
            FixSkinnedMeshRenderersInScene();
        }
    }
    
    /// <summary>
    /// 修复场景中所有的SkinnedMeshRenderer
    /// </summary>
    public void FixSkinnedMeshRenderersInScene()
    {
        SkinnedMeshRenderer[] renderers = FindObjectsOfType<SkinnedMeshRenderer>();
        
        foreach (var renderer in renderers)
        {
            FixSkinnedMeshRenderer(renderer);
        }
        
        if (enableLogging)
        {
            Debug.Log($"[MobileSkinnedMeshFixer] 已处理 {renderers.Length} 个SkinnedMeshRenderer");
        }
    }
    
    /// <summary>
    /// 修复单个SkinnedMeshRenderer
    /// </summary>
    public bool FixSkinnedMeshRenderer(SkinnedMeshRenderer renderer)
    {
        if (renderer == null || renderer.sharedMesh == null)
            return false;
            
        bool hasIssues = false;
        
        // 1. 验证Mesh兼容性
        if (validateMeshCompatibility && !ValidateMeshCompatibility(renderer.sharedMesh))
        {
            hasIssues = true;
            if (enableLogging)
            {
                Debug.LogWarning($"[MobileSkinnedMeshFixer] Mesh '{renderer.sharedMesh.name}' 在移动端可能不兼容", renderer);
            }
        }
        
        // 2. 检查骨骼数量
        if (renderer.bones != null && renderer.bones.Length > maxTotalBones)
        {
            hasIssues = true;
            if (enableLogging)
            {
                Debug.LogWarning($"[MobileSkinnedMeshFixer] SkinnedMeshRenderer '{renderer.name}' 骨骼数量 ({renderer.bones.Length}) 超过移动端推荐值 ({maxTotalBones})", renderer);
            }
        }
        
        // 3. 应用移动端优化
        ApplyMobileOptimizations(renderer);
        
        // 4. 强制CPU Skinning（如果需要）
        if (forceCPUSkinning || hasIssues)
        {
            ForceCPUSkinning(renderer);
        }
        
        return !hasIssues;
    }
    
    /// <summary>
    /// 验证Mesh在移动端的兼容性
    /// </summary>
    private bool ValidateMeshCompatibility(Mesh mesh)
    {
        if (mesh == null) return false;
        
        // 检查缓存
        if (meshCache.TryGetValue(mesh, out MeshCompatibilityInfo cachedInfo))
        {
            return cachedInfo.isCompatibleWithMobile;
        }
        
        MeshCompatibilityInfo info = new MeshCompatibilityInfo();
        info.vertexCount = mesh.vertexCount;
        info.vertexAttributes = mesh.GetVertexAttributes();
        
        // 计算vertex stride
        info.vertexStride = 0;
        foreach (var attr in info.vertexAttributes)
        {
            info.vertexStride += GetAttributeSize(attr.format);
        }
        
        // 检查移动端兼容性
        info.isCompatibleWithMobile = CheckMobileCompatibility(info);
        
        // 缓存结果
        meshCache[mesh] = info;
        
        return info.isCompatibleWithMobile;
    }
    
    /// <summary>
    /// 检查mesh在移动端的兼容性
    /// </summary>
    private bool CheckMobileCompatibility(MeshCompatibilityInfo info)
    {
        // 检查顶点数量限制
        if (info.vertexCount > 65535)
        {
            if (enableLogging)
                Debug.LogWarning("[MobileSkinnedMeshFixer] Mesh顶点数量超过移动端限制 (65535)");
            return false;
        }
        
        // 检查vertex stride是否合理
        if (info.vertexStride > 64) // 移动端通常不支持过大的vertex stride
        {
            if (enableLogging)
                Debug.LogWarning($"[MobileSkinnedMeshFixer] Vertex stride ({info.vertexStride}) 可能对移动端过大");
            return false;
        }
        
        // 检查是否有不支持的vertex attributes
        foreach (var attr in info.vertexAttributes)
        {
            if (!IsMobileSupportedAttribute(attr))
            {
                if (enableLogging)
                    Debug.LogWarning($"[MobileSkinnedMeshFixer] 不支持的vertex attribute: {attr.attribute}");
                return false;
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// 检查vertex attribute是否被移动端支持
    /// </summary>
    private bool IsMobileSupportedAttribute(VertexAttributeDescriptor attr)
    {
        // 移动端通常支持的基本attributes
        switch (attr.attribute)
        {
            case VertexAttribute.Position:
            case VertexAttribute.Normal:
            case VertexAttribute.Tangent:
            case VertexAttribute.Color:
            case VertexAttribute.TexCoord0:
            case VertexAttribute.TexCoord1:
            case VertexAttribute.BlendWeight:
            case VertexAttribute.BlendIndices:
                return true;
            default:
                return false;
        }
    }
    
    /// <summary>
    /// 获取vertex attribute的字节大小
    /// </summary>
    private int GetAttributeSize(VertexAttributeFormat format)
    {
        switch (format)
        {
            case VertexAttributeFormat.Float32: return 4;
            case VertexAttributeFormat.Float16: return 2;
            case VertexAttributeFormat.UNorm8: return 1;
            case VertexAttributeFormat.SNorm8: return 1;
            case VertexAttributeFormat.UNorm16: return 2;
            case VertexAttributeFormat.SNorm16: return 2;
            case VertexAttributeFormat.UInt8: return 1;
            case VertexAttributeFormat.SInt8: return 1;
            case VertexAttributeFormat.UInt16: return 2;
            case VertexAttributeFormat.SInt16: return 2;
            case VertexAttributeFormat.UInt32: return 4;
            case VertexAttributeFormat.SInt32: return 4;
            default: return 4;
        }
    }
    
    /// <summary>
    /// 应用移动端优化设置
    /// </summary>
    private void ApplyMobileOptimizations(SkinnedMeshRenderer renderer)
    {
        // 禁用不必要的功能以提高性能
        renderer.skinnedMotionVectors = false;
        
        // 调整质量设置
        if (QualitySettings.skinWeights == SkinWeights.Unlimited)
        {
            QualitySettings.skinWeights = SkinWeights.FourBones;
        }
        
        // 确保使用合适的更新频率
        if (renderer.updateWhenOffscreen)
        {
            renderer.updateWhenOffscreen = false;
        }
    }
    
    /// <summary>
    /// 强制使用CPU Skinning
    /// </summary>
    private void ForceCPUSkinning(SkinnedMeshRenderer renderer)
    {
        // 通过设置material的特定关键字来强制CPU skinning
        Material[] materials = renderer.materials;
        for (int i = 0; i < materials.Length; i++)
        {
            if (materials[i] != null)
            {
                // 禁用GPU skinning相关的shader keywords
                materials[i].DisableKeyword("_ENABLE_GPU_SKINNING");
                materials[i].DisableKeyword("UNITY_USE_DITHER_MASK_FOR_ALPHA");
            }
        }
        
        if (enableLogging)
        {
            Debug.Log($"[MobileSkinnedMeshFixer] 为 '{renderer.name}' 启用CPU Skinning", renderer);
        }
    }
    
    /// <summary>
    /// 获取系统信息以进行移动端检测
    /// </summary>
    public static bool IsMobilePlatform()
    {
        return Application.isMobilePlatform || 
               SystemInfo.graphicsDeviceType == GraphicsDeviceType.OpenGLES2 ||
               SystemInfo.graphicsDeviceType == GraphicsDeviceType.OpenGLES3;
    }
    
    /// <summary>
    /// 运行时诊断工具
    /// </summary>
    [System.Diagnostics.Conditional("UNITY_EDITOR")]
    public void RunDiagnostics()
    {
        Debug.Log("=== 移动端SkinnedMeshRenderer诊断报告 ===");
        Debug.Log($"平台: {Application.platform}");
        Debug.Log($"图形API: {SystemInfo.graphicsDeviceType}");
        Debug.Log($"GPU: {SystemInfo.graphicsDeviceName}");
        Debug.Log($"GPU内存: {SystemInfo.graphicsMemorySize}MB");
        Debug.Log($"Quality Level: {QualitySettings.names[QualitySettings.GetQualityLevel()]}");
        Debug.Log($"Skin Weights: {QualitySettings.skinWeights}");
        
        // 检查URP设置
        var urpAsset = GraphicsSettings.renderPipelineAsset as UniversalRenderPipelineAsset;
        if (urpAsset != null)
        {
            Debug.Log("URP Asset找到，建议检查移动端配置");
        }
        else
        {
            Debug.LogWarning("未找到URP Asset!");
        }
    }
}

#if UNITY_EDITOR
/// <summary>
/// Editor工具用于批量修复
/// </summary>
[CustomEditor(typeof(MobileSkinnedMeshFixer))]
public class MobileSkinnedMeshFixerEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        
        MobileSkinnedMeshFixer fixer = (MobileSkinnedMeshFixer)target;
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("诊断工具", EditorStyles.boldLabel);
        
        if (GUILayout.Button("运行诊断"))
        {
            fixer.RunDiagnostics();
        }
        
        if (GUILayout.Button("修复场景中的SkinnedMeshRenderer"))
        {
            fixer.FixSkinnedMeshRenderersInScene();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("此工具用于修复移动端SkinnedMeshRenderer兼容性问题。" +
                               "建议在移动端构建前运行。", MessageType.Info);
    }
}
#endif 