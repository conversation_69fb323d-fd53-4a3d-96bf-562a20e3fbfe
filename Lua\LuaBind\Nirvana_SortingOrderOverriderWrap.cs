﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_SortingOrderOverriderWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.SortingOrderOverrider), typeof(UnityEngine.MonoBehaviour));
		L<PERSON>Function("__eq", op_Equality);
		L<PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("SortingOrder", get_SortingOrder, set_SortingOrder);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SortingOrder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.SortingOrderOverrider obj = (Nirvana.SortingOrderOverrider)o;
			int ret = obj.SortingOrder;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SortingOrder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SortingOrder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.SortingOrderOverrider obj = (Nirvana.SortingOrderOverrider)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SortingOrder = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SortingOrder on a nil value");
		}
	}
}

