require("game/reiki_seed/reiki_seed_view")
require("game/reiki_seed/reiki_seed_wg_data")
require("game/reiki_seed/reiki_seed_reward_view")

ReikiSeedWGCtrl = ReikiSeedWGCtrl or BaseClass(BaseWGCtrl)
function ReikiSeedWGCtrl:__init()
	if ReikiSeedWGCtrl.Instance then
		error("[ReikiSeedWGCtrl]:Attempt to create singleton twice!")
	end

    ReikiSeedWGCtrl.Instance = self

    self.data = ReikiSeedWGData.New()
    self.view = ReikiSeedView.New(GuideModuleName.ReikiSeedView)
    self.reward_view = ReikiSeedRewardView.New()

    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)

    self:RegisterAllProtocols()
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function ReikiSeedWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    self.reward_view:DeleteMe()
	self.reward_view = nil

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
    end

    ReikiSeedWGCtrl.Instance = nil
end

function ReikiSeedWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOAGloryCrystalInfo3, "OnSCOAGloryCrystalInfo")
    self:RegisterProtocol(SCOAGloryCrystalDrawResult3, "OnSCOAGloryCrystalDrawResult")
end

function ReikiSeedWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local other_cfg = self.data:GetOtherCfg()
    if (change_item_id == other_cfg.cost_item_id or self.data:GetIsStuffItem(change_item_id)) and old_num < new_num then
        if self.view:IsOpen() then
            self.view:OnItemDataChange()
        end

        RemindManager.Instance:Fire(RemindName.ReikiSeed)
    end
end

function ReikiSeedWGCtrl:ReqReikiSeedInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_ReikiSeed
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function ReikiSeedWGCtrl:OnSCOAGloryCrystalInfo(protocol)
    local old_reward_state = self.data:GetAllRewardState()
	local reward_list = {}
	local reward_cfg = self.data:GetAllTimesRewardCfg()
	if not IsEmptyTable(old_reward_state) then
		for k, v in pairs(protocol.times_reward_flag) do
			if old_reward_state[k] ~= v and old_reward_state[k] == 0 and reward_cfg[k] then
				table.insert(reward_list, reward_cfg[k].item)
			end
		end
	end

	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end

	self.data:SetAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ReikiSeedView)
	RemindManager.Instance:Fire(RemindName.ReikiSeed)
end

function ReikiSeedWGCtrl:OnSCOAGloryCrystalDrawResult(protocol)
	self.data:SetResultData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ReikiSeedView, nil, "play_ani")
	local delay_time = self.data:GetDelayTime()
    GlobalTimerQuest:AddDelayTimer(function ()
    	self.reward_view:SetDataType(protocol.mode, protocol.count, false)
	    if not self.reward_view:IsOpen() then
	        self.reward_view:Open()
	    else
	        self.reward_view:Flush()
	    end
    end, delay_time)
end

function ReikiSeedWGCtrl:MainuiOpenCreate()
	self:ReqReikiSeedInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.INFO)
end

--使用道具并弹窗
function ReikiSeedWGCtrl:ClickUse(mode_type, form_reward_view)
    --数量检测
   local other_cfg = self.data:GetOtherCfg()
   local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
   local mode_cfg = self.data:GetModeCfgByMode(mode_type)

   if IsEmptyTable(item_cfg) or not mode_cfg then
       return
   end

   local item_count = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
   if item_count >= mode_cfg.cost_item_num then
       self:ReqReikiSeedInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.DRAW, mode_type)
       self.data:CacheOrGetDrawIndex(mode_type)
       if form_reward_view and not self.data:GetJumpAni() then
           if self.reward_view:IsOpen() then
               self.reward_view:Close()
           end
       end
   else
       if not self.alert then
           self.alert = Alert.New()
       end

       self.alert:ClearCheckHook()
       self.alert:SetShowCheckBox(true, "reiki_seed")
       self.alert:SetCheckBoxDefaultSelect(false)
       local func = function()
           local consume = other_cfg.cost_gold * (mode_cfg.cost_item_num - item_count)
           --检查仙玉
           local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
           --足够购买，不足弹窗
           if enough then
               self:ReqReikiSeedInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.DRAW, mode_type)
               self.data:CacheOrGetDrawIndex(mode_type)
           else
               VipWGCtrl.Instance:OpenTipNoGold()
           end

           if form_reward_view and not self.data:GetJumpAni() then
               if self.reward_view:IsOpen() then
                   self.reward_view:Close()
               end
           end
       end

       local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
       local cost = other_cfg.cost_gold * (mode_cfg.cost_item_num - item_count)
       local str = string.format(Language.GloryCrystal.CostStr, name, cost)

       self.alert:SetLableString(str)
       self.alert:SetOkFunc(func)
       self.alert:Open()
   end
end