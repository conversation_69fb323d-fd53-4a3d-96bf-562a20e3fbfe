require("game/phantom_dreamland/phantom_dreamland_wg_data")
require("game/phantom_dreamland/phantom_dreamland_view")
require("game/phantom_dreamland/phantom_dreamland_fb_scene_view")
require("game/phantom_dreamland/phantom_dreamland_fb_rank_view")
require("game/phantom_dreamland/phantom_dreamland_fb_card_tips_view")
require("game/phantom_dreamland/phantom_dreamland_fb_add_times_view")

-- 历练副本
PhantomDreamlandWGCtrl = PhantomDreamlandWGCtrl or BaseClass(BaseWGCtrl)
function PhantomDreamlandWGCtrl:__init()
	if PhantomDreamlandWGCtrl.Instance ~= nil then
		ErrorLog("[PhantomDreamlandWGCtrl] Attemp to create a singleton twice !")
	end
	PhantomDreamlandWGCtrl.Instance = self

	self.view = PhantomDreamlandView.New(GuideModuleName.PhantomDreamlandView)
	self.dreamland_fb_scene_view = PhantomDreamlandFBSceneView.New()
	self.dreamland_fb_rank_view = PhantomDreamlandFBRankView.New()
    self.dreamland_fb_card_tips_view = PhantomDreamlandFBCardTipsView.New()
	self.dreamland_fb_add_times_view = PhantomDreamlandFBAddTimesView.New()
	self.data = PhantomDreamlandWGData.New()

	self:RegisterAllProtocols()
end

function PhantomDreamlandWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	if self.dreamland_fb_scene_view then
		self.dreamland_fb_scene_view:DeleteMe()
		self.dreamland_fb_scene_view = nil
	end

	if self.dreamland_fb_rank_view then
		self.dreamland_fb_rank_view:DeleteMe()
		self.dreamland_fb_rank_view = nil
	end

	if self.dreamland_fb_card_tips_view then
		self.dreamland_fb_card_tips_view:DeleteMe()
		self.dreamland_fb_card_tips_view = nil
	end

	if self.dreamland_fb_add_times_view then
		self.dreamland_fb_add_times_view:DeleteMe()
		self.dreamland_fb_add_times_view = nil
	end

	PhantomDreamlandWGCtrl.Instance = nil
end

function PhantomDreamlandWGCtrl:RegisterAllProtocols()
	--self:RegisterProtocol(CSExpWestOperate)												-- 	副本相关请求
	self:RegisterProtocol(SCOADreamSecretBaseInfo, "OnSCOADreamSecretBaseInfo")				--	副本信息
	self:RegisterProtocol(SCOADreamSecretSceneInfo, "OnSCOADreamSecretSceneInfo")						-- 	副本场景信息
	self:RegisterProtocol(SCOADreamSecretRankInfo, "OnSCOADreamSecretRankInfo")							--	副本当前等级的排行信息
	self:RegisterProtocol(SCOADreamSecretCardInfo, "OnSCOADreamSecretCardInfo")							--	副本当前的选卡信息
	self:RegisterProtocol(SCOADreamSecretSkillInfo, "OnSCOADreamSecretSkillInfo")						--	副本当前的技能信息
end

--请求
function PhantomDreamlandWGCtrl:SendReq(opera_type, param1, param2)
    local param_t = {
        rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DREAM_SECRET,
        opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
    }
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

-- 请求副本基础信息
function PhantomDreamlandWGCtrl:RequestPhantomDreamlandBaseInfo()
	self:SendReq(OA_DREAM_SECRET_OPERATE_TYPE.OA_DREAM_SECRET_OPERATE_TYPE_BASE_INFO)
end


-- 请求挑战关卡
function PhantomDreamlandWGCtrl:RequestPhantomDreamlandDare()
	self:SendReq(OA_DREAM_SECRET_OPERATE_TYPE.OA_DREAM_SECRET_OPERATE_TYPE_DARE)
end

--请求排行信息
function PhantomDreamlandWGCtrl:RequestPhantomDreamlandRankInfo()
	self:SendReq(OA_DREAM_SECRET_OPERATE_TYPE.OA_DREAM_SECRET_OPERATE_TYPE_RANK_INFO)
end

-- 领取波次奖励
function PhantomDreamlandWGCtrl:RequestPhantomDreamlandGetReward(wave)
	self:SendReq(OA_DREAM_SECRET_OPERATE_TYPE.OA_DREAM_SECRET_OPERATE_TYPE_FETCH_WAVE_REWARD, wave)
end

--[[
-- 请求一键通关
function PhantomDreamlandWGCtrl:RequestExpWestAutoDare()
	self:SendReq(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_AUTO_DARE)
end

]]

--请求选卡
function PhantomDreamlandWGCtrl:RequestDreamlandChooseCard(wave, index)
	self:SendReq(OA_DREAM_SECRET_OPERATE_TYPE.OA_DREAM_SECRET_OPERATE_TYPE_CHOOSE_CARD, wave, index)
end


--请求使用技能
function PhantomDreamlandWGCtrl:RequestDreamlandUseSkill()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local main_role_x, main_role_y = main_role:GetLogicPos()
		self:SendReq(OA_DREAM_SECRET_OPERATE_TYPE.OA_DREAM_SECRET_OPERATE_TYPE_USE_SKILL, 0, main_role_x, main_role_y)
	end
end

---请求购买时长
---@param buy_type number 0:购买一次 1:一键购买
function PhantomDreamlandWGCtrl:RequestDreamlandBuyTimes(buy_type)
	self:SendReq(OA_DREAM_SECRET_OPERATE_TYPE.OA_DREAM_SECRET_OPERATE_TYPE_BUY_TIME, buy_type)
end

-- 副本基础信息
function PhantomDreamlandWGCtrl:OnSCOADreamSecretBaseInfo(protocol)
	self.data:SetDreamSecretBaseInfo(protocol)
	self.view:Flush()
	RemindManager.Instance:Fire(RemindName.PhantomDreamland)
end

-- 场景信息
function PhantomDreamlandWGCtrl:OnSCOADreamSecretSceneInfo(protocol)
	-- print_error("场景信息", protocol)
	self.data:SetDreamlandSceneInfo(protocol)
	if protocol.is_end == 1 then
		if GuajiCache.guaji_type == GuajiType.Auto then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		end

		if protocol.is_pass == 1 then
			-- 挑战成功
			local pass_vo = FuBenWGData.CreateCommonPassVo()
			pass_vo.scene_type = SceneType.PHANTOM_DREAMLAND_FB
			pass_vo.is_pass = protocol.is_pass
			pass_vo.reward_list = {}
			pass_vo.tip1 = string.format(Language.PhantomDreamland.CurHarmValue, protocol.harm_value)
			local best_value = 0
			if protocol.is_new_record == 1 then
				pass_vo.is_show_new_record = true
				best_value = protocol.harm_value
			else
				best_value = self.data:GetBestHarmValue()
			end
			pass_vo.tip2 = string.format(Language.PhantomDreamland.BestHarmValue, best_value)
			FuBenWGCtrl.Instance:OpenFuBenNextView(pass_vo)
		else
			-- 挑战失败
			FuBenWGCtrl.Instance:OpenFuBenLoseView()
		end
	else
		if GuajiCache.guaji_type ~= GuajiType.Auto then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end
	
	self:FlushDreamlandFbSceneView(0, "flush_wave")
end

-- 排行信息
function PhantomDreamlandWGCtrl:OnSCOADreamSecretRankInfo(protocol)
	-- print_error("排行信息", protocol)
	self.data:SetDreamSecretRankInfo(protocol)
end

-- 选卡信息
function PhantomDreamlandWGCtrl:OnSCOADreamSecretCardInfo(protocol)
	-- print_error("选卡信息", protocol)
	self.data:SetDreamlandCardInfo(protocol)
	self:FlushDreamlandFbSceneView(0, "flush_card")
end

-- 技能信息
function PhantomDreamlandWGCtrl:OnSCOADreamSecretSkillInfo(protocol)
	-- print_error("技能信息", protocol)
	self.data:SetDreamlandSkillInfo(protocol)
	self:FlushDreamlandFbSceneView(0, "flush_skill")
end

-- 打开界面
function PhantomDreamlandWGCtrl:OpenPhantomDreamlandFbView()
	if self.view:IsOpen() then
		self.view:Flush()
	else
		self.view:Open()
	end
end

-- 打开场景界面
function PhantomDreamlandWGCtrl:OpenDreamlandFbSceneView()
	if self.dreamland_fb_scene_view:IsOpen() then
		self.dreamland_fb_scene_view:Flush()
	else
		self.dreamland_fb_scene_view:Open()
	end
end

function PhantomDreamlandWGCtrl:FlushDreamlandFbSceneView(index, key, param_t)
	if self.dreamland_fb_scene_view:IsOpen() then
		self.dreamland_fb_scene_view:Flush(index, key, param_t)
	end
end

function PhantomDreamlandWGCtrl:CloseDreamlandFbSceneView()
	self.dreamland_fb_scene_view:Close()
end

function PhantomDreamlandWGCtrl:GetDreamlandFbSceneView()
	return self.dreamland_fb_scene_view
end

-- 打开场景界面
function PhantomDreamlandWGCtrl:OpenDreamlandFbRankView()
	if self.dreamland_fb_rank_view:IsOpen() then
		self.dreamland_fb_rank_view:Flush()
	else
		self.dreamland_fb_rank_view:Open()
	end
end

-- 打开总属性展示
function PhantomDreamlandWGCtrl:OpenDreamlandFbCardTipsView()
	if self.dreamland_fb_card_tips_view:IsOpen() then
		self.dreamland_fb_card_tips_view:Flush()
	else
		self.dreamland_fb_card_tips_view:Open()
	end
end

-- 打开购买时长界面
function PhantomDreamlandWGCtrl:OpenDreamlandFbAddTimesView()
	if self.dreamland_fb_add_times_view:IsOpen() then
		self.dreamland_fb_add_times_view:Flush()
	else
		self.dreamland_fb_add_times_view:Open()
	end
end