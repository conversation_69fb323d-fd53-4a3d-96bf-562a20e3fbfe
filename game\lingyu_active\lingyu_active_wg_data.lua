LingYuActiveWGData = LingYuActiveWGData or BaseClass()
--初始化
function LingYuActiveWGData:__init()
	if LingYuActiveWGData.Instance then
		error("[LingYuActiveWGData] Attempt to create singleton twice!")
		return
	end
	-- 单例
	LingYuActiveWGData.Instance = self

	self.totalGoldValue = 0
	self.playerRechargeValue = 0
	self.activeEndTime = 0
end

function LingYuActiveWGData:__delete()
	LingYuActiveWGData.Instance = nil
end

-- 存储协议下发的数据
function LingYuActiveWGData:SetActiveInfo(protocol)
	self.totalGoldValue = protocol.totalGoldValue
	self.playerRechargeValue = protocol.playerRechargeValue
	local activeData = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_LINGYUPOOL_ACTIVE)
	self.activeEndTime = activeData.end_time
end

--得到活动总共充值的金额
function LingYuActiveWGData:GetTotalGoldValue()
	return self.totalGoldValue
end

--得到活动个人充值的金额
function LingYuActiveWGData:GetPlayerRechargeValue()
	return self.playerRechargeValue
end

--得到活动的结束时间
function LingYuActiveWGData:GetActiveEndTime()
	return self.activeEndTime
end
