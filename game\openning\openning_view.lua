OpenningView = OpenningView or BaseClass(SafeBaseView)

-- 开启仙途
function OpenningView:__init()
	self.view_style = ViewStyle.Window
	self.is_need_depth = true
	self.view_layer = UiLayer.PopTop
	
	self.view_name = GuideModuleName.OpenningView
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/openning_prefab", "layout_openning")

end

function OpenningView:__delete()

end

function OpenningView:OpenCallBack()
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.HuanYinYe, nil, true))
end

function OpenningView:CloseCallBack()
	TaskGuide.Instance:CanAutoAllTask(true)
end

function OpenningView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_openning"], BindTool.Bind1(self.Close, self))
	if CountDownManager.Instance:HasCountDown("openning_view_countdown") then
		CountDownManager.Instance:RemoveCountDown("openning_view_countdown")
	end
	
	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	-- 界面关闭时间与新手检查自动做任务时间一致
	self:UpdateCallBack(0, other_cfg.novice_check_time)
	CountDownManager.Instance:AddCountDown("openning_view_countdown", BindTool.Bind1(self.UpdateCallBack, self), BindTool.Bind1(self.Close, self), nil, other_cfg.novice_check_time, 1)
end

function OpenningView:UpdateCallBack(elapse_time, total_time)
	self.node_list["countdown"].text.text = string.format(Language.OpenningView.CountDown, total_time - elapse_time)
end

function OpenningView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("openning_view_countdown") then
		CountDownManager.Instance:RemoveCountDown("openning_view_countdown")
	end
end

function OpenningView:LoadIndexCallBack(index)

end

function OpenningView:ShowIndexCallBack(index)

end

function OpenningView:OnFlush(param_list, index)

end
