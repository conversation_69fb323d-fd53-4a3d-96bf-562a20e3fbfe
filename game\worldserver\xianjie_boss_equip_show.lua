local Max_Index = Xian<PERSON>ieBossWGData.MaxChipIndex

local State_Tb = {
    Shrink = 0,
    Expand = 1
}
XianJieBossEquipShow = XianJieBossEquipShow or BaseClass(SafeBaseView)
function XianJieBossEquipShow:__init()
    self.is_safe_area_adapter = true
    self.view_name = "XianJieBossEquipShow"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_xianjie_book_chips")
    
    self.view_layer = UiLayer.MainUIHigh
end

function XianJieBossEquipShow:CloseCallBack()
    if CountDownManager.Instance:HasCountDown("xianjie_boss_equip_hide") then
        CountDownManager.Instance:RemoveCountDown("xianjie_boss_equip_hide")
    end
    self:CancelTween()
end

function XianJieBossEquipShow:LoadCallBack()
    self.obj_die_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind(self.MainRoleDie, self))

    XUI.AddClickEventListener(self.node_list["shrink_btn"], BindTool.Bind(self.PlayTween, self, false))
    XUI.AddClickEventListener(self.node_list["open_btn"], BindTool.Bind(self.OnClickBookBtn, self))
    XUI.AddClickEventListener(self.node_list["total_btn"], BindTool.Bind(self.OnClickGoToEquip, self))

    self.chips_item_list = {}
    local list_node = self.node_list["chip_list"]
    for i = 0, Max_Index do
        self.chips_item_list[i] = XianJieBookEquipChipRender.New(list_node:FindObj("chip_" .. i))
        self.chips_item_list[i]:SetIndex(i)
    end

    self.state = State_Tb.Expand
    self:PlayTween(true)

    if self.load_callback then
		self.load_callback()
        self.load_callback = nil
    else
        self:SetAutoHide(6)
    end

    MainuiWGCtrl.Instance:AddObjToMainUI(self.node_list["open_btn"], "tianshu_mainui_root")
end

function XianJieBossEquipShow:ShowIndexCallBack()

end

function XianJieBossEquipShow:OnClickGoToEquip()
    local slot, page = XianJieBossWGData.Instance:GetCurSlotAndPage()
    ViewManager.Instance:Open(GuideModuleName.FairyLandRuleView, 0, "open_rule", {to_slot = slot, to_ui_param = page})
end

function XianJieBossEquipShow:MainRoleDie()
	self:Flush()
end

function XianJieBossEquipShow:GetCurSceneSlotPage()
    local cfg = XianJieBossWGData.Instance:GetBossLayerCfgBySceneId(Scene.Instance:GetSceneId())
    if cfg then
        return cfg.slot_limit, cfg.slot_page_limit
    else
        --print_error("找不到场景的对应层配置，scend_id", Scene.Instance:GetSceneId())
        return 0, 0
    end
end

function XianJieBossEquipShow:FlushData(show_func)
    if self:IsOpen() and self:IsLoaded() then
        -- if self.enter_play_tween then
        --     self:CancelTween()
        -- end
        if self.state == State_Tb.Shrink then
            self:OnClickBookBtn()
        end
        show_func()
    else
        self:SetLoadCallBack(show_func)
        self:Open()
    end
end

function XianJieBossEquipShow:UpdataTime(elapse_time, total_time)
	
end

function XianJieBossEquipShow:TimeCompleteCallBack()
    if self.state == State_Tb.Expand and not self.enter_play_tween then
        self:PlayTween(false)
    end
end

function XianJieBossEquipShow:SetAutoHide(time)
    if CountDownManager.Instance:HasCountDown("xianjie_boss_equip_hide") then
        CountDownManager.Instance:RemoveCountDown("xianjie_boss_equip_hide")
    end

    time = time or 4
    CountDownManager.Instance:AddCountDown("xianjie_boss_equip_hide", BindTool.Bind(self.UpdataTime, self), BindTool.Bind(self.TimeCompleteCallBack, self), nil, time, 1)
end

function XianJieBossEquipShow:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end

function XianJieBossEquipShow:GetState()
    return self.state
end

function XianJieBossEquipShow:OnClickBookBtn()
    self:PlayTween(self.state ~= State_Tb.Expand)       -- 合起天书
end

function XianJieBossEquipShow:PlayTween(is_enter)
    self.state = is_enter and State_Tb.Expand or State_Tb.Shrink
    if is_enter then
        if CountDownManager.Instance:HasCountDown("xianjie_boss_equip_hide") then
            CountDownManager.Instance:RemoveCountDown("xianjie_boss_equip_hide")
        end
    end

    self:CancelTween()
    self.node_list["open_btn"]:SetActive(true)
    if not is_enter then
        self.node_list["shrink_btn"]:SetActive(is_enter)
    end

    self.enter_play_tween = DG.Tweening.DOTween.Sequence()
    local from = is_enter and 0 or 1
    local to = is_enter and 1 or 0
    self.node_list.chip_list.canvas_group.alpha = from
    self.node_list.book_content.transform.localScale = Vector3(from, from, from)
    local tween_alpha = self.node_list.chip_list.canvas_group:DoAlpha(from, to, 0.3)
    local tween_scale = self.node_list.book_content.rect:DOScale(Vector3(to, to, to), 0.3)
    if is_enter then
        self.enter_play_tween:Append(tween_scale)
        self.enter_play_tween:Append(tween_alpha)
    else
        self.enter_play_tween:Append(tween_alpha)
        self.enter_play_tween:Append(tween_scale)
    end

    if not is_enter then --收缩
        self.enter_play_tween:OnComplete(function()
            self.node_list["shrink_btn"]:SetActive(is_enter)
            BossWGCtrl.Instance:MoveXianJieRevengeHorizontal(is_enter)
            self.enter_play_tween = nil
        end)
    else --展开
        self.enter_play_tween:OnComplete(function()
            self.node_list["shrink_btn"]:SetActive(true)
            self.enter_play_tween = nil
        end)
    end

    self.enter_play_tween:Play()
end


function XianJieBossEquipShow:SetLoadCallBack(callback)
	self.load_callback = callback
end

function XianJieBossEquipShow:ReleaseCallBack()
    if self.node_list and self.node_list["open_btn"] then
        self.node_list["open_btn"].transform:SetParent(self.root_node_transform, false)
    end

    if not IsEmptyTable(self.chips_item_list) then
        for k, v in pairs(self.chips_item_list) do
            v:DeleteMe()
        end
        self.chips_item_list = nil
    end

    if self.move_tip_tween then
        self.move_tip_tween:Kill()
        self.move_tip_tween = nil
    end

    if CountDownManager.Instance:HasCountDown("xianjie_boss_equip_hide") then
        CountDownManager.Instance:RemoveCountDown("xianjie_boss_equip_hide")
    end

    if self.obj_die_event then
		GlobalEventSystem:UnBind(self.obj_die_event)
		self.obj_die_event = nil
	end

    self:CancelTween()
    self.load_callback = nil
end

function XianJieBossEquipShow:OnFlush()
    local slot, page = self:GetCurSceneSlotPage()
    for k,v in pairs(self.chips_item_list) do
        v:SetData({slot = slot, page = page})
    end

    local num = FairyLandEquipmentWGData.Instance:GetPageChipGatherNum(slot, page)
    local max = FairyLandEquipmentWGData.Instance:GetActPageNeedNum()
    self.node_list["gb_collect_text"].text.text = string.format("%d/%d", num, max)

    local data = FairyLandEquipmentWGData.Instance:GetGodBookCfg(page)
    local effect_name = data.effect
    local bundle, asset = ResPath.GetEffectUi(effect_name)
	self.node_list.effect:ChangeAsset(bundle, asset)
end

function XianJieBossEquipShow:GetEquipBtnNode(item_id)
	local cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByItemId(item_id)
	if cfg then
        if self:IsOpen() and self:IsLoaded() then
            local slot, page = self:GetCurSceneSlotPage()
            local node = self.node_list["chip_list_"..page]
            local cell_node = node and node.transform:Find("chip_".. cfg.part)-- self.node_list["chip_".. cfg.part]
            if cell_node then
                local cell = U3DObject(cell_node.gameObject, cell_node, self)
                return cell
            end
		end
    end

    return self.node_list.chip_list
end


XianJieBookEquipChipRender = XianJieBookEquipChipRender or BaseClass(BaseRender)
function XianJieBookEquipChipRender:__init()
end

function XianJieBookEquipChipRender:LoadCallBack()
    self.node_list["page_self"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
end

function XianJieBookEquipChipRender:__delete()
end

function XianJieBookEquipChipRender:OnFlush()
    if self.data == nil then
        return
    end

    local slot = self.data.slot
    local page = self.data.page
    local part = self.index
    local is_act = FairyLandEquipmentWGData.Instance:GetPagePartAct(slot, page, part)
    local bundle, asset
    if is_act then
        local chip_cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByData(slot, page, part)
        if chip_cfg then
            local item_cfg = ItemWGData.Instance:GetItemConfig(chip_cfg.item_id)
            if item_cfg then
                bundle, asset = ResPath.GetItem(item_cfg.icon_id)
            end
        end
    else
        bundle, asset = ResPath.GetBossUI("a3_ys_ihui" .. part)
    end
    self.node_list.icon.image:LoadSprite(bundle, asset)
end

function XianJieBookEquipChipRender:OnClickCell()
    if self.data == nil then
        return
    end

    local slot = self.data.slot
    local page = self.data.page
    local part = self.index

    local chip_cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByData(slot, page, part)
    if chip_cfg then
        TipWGCtrl.Instance:OpenItem({item_id = chip_cfg.item_id})
    end
end