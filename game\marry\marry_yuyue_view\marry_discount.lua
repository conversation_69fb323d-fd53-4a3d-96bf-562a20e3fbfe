MarryDiscountView = MarryDiscountView or BaseClass(SafeBaseView)

function MarryDiscountView:__init()
    self:SetMaskBg(true)
    self.view_name = GuideModuleName.MarryDiscountView
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_discount_view")
end

function MarryDiscountView:__delete()

end

function MarryDiscountView:LoadCallBack()
    self.node_list["btn_window_close"].button:AddClickListener(BindTool.Bind1(self.Close, self))
    self.node_list["btn_open_tiqin"].button:AddClickListener(BindTool.Bind1(self.OpenMarryTiqinView, self))

    self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

    self:LoginTimeCountDown()
end

function MarryDiscountView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("marry_discount_count_down") then
		CountDownManager.Instance:RemoveCountDown("marry_discount_count_down")
	end

    if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
end

--活动状态监听
function MarryDiscountView:ActivityChangeCallBack(activity_type, status)
	if activity_type == ACTIVITY_TYPE.MARRY_DISCOUNT and status == ACTIVITY_STATUS.CLOSE then
        self:Close()
    end
end

function MarryDiscountView:OpenMarryTiqinView()
    ViewManager.Instance:Open(GuideModuleName.MarryTiQin)
end

--活动时间倒计时
function MarryDiscountView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.MARRY_DISCOUNT)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list.time_text.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("marry_discount_count_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end

function MarryDiscountView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list.time_text.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function MarryDiscountView:OnComplete()
	self.node_list.time_text.text.text = "活动已结束"
end