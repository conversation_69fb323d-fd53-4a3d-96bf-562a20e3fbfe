SystemCapRankTipsView = SystemCapRankTipsView or BaseClass(SafeBaseView)
function SystemCapRankTipsView:__init()
    self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(970, 576)})
	local assetbundle = "uis/view/system_cap_rank_ui_prefab"
	self:AddViewResource(0, assetbundle, "system_cap_rank_tips")
end

function SystemCapRankTipsView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.SystemCapRank.SystemCapRankTipsTiele
	self.rank_log_list = AsyncListView.New(SysCapRankLogItemRender, self.node_list["ph_item_list"])

	self:FlushView()
end

function SystemCapRankTipsView:ReleaseCallBack()
	if self.rank_log_list then
		self.rank_log_list:DeleteMe()
		self.rank_log_list = nil
	end
end

function SystemCapRankTipsView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end
end

function SystemCapRankTipsView:FlushView()
	local data_list = SystemCapRankWGData.Instance:GetRankInfo()
	--print_error(data_list)
	if data_list then
        self.rank_log_list:SetDataList(data_list)
	end

	local my_total_zhanli = SystemCapRankWGData.Instance:GetMyRankValue()
    local rank_data = SystemCapRankWGData.Instance:GetMyRankInfo()
	if rank_data ~= nil then
		self.node_list["my_rank"].text.text = string.format(Language.OperationActivity.RechargeRankTitle4, rank_data.rank_index)
	else
		self.node_list["my_rank"].text.text = Language.OperationActivity.RechargeNoRank
	end
    
	if my_total_zhanli then
		self.node_list["total_value"].text.text = string.format(Language.SystemCapRank.SystemCapRankDesc, my_total_zhanli)
	end
end


SysCapRankLogItemRender = SysCapRankLogItemRender or BaseClass(BaseRender)
function SysCapRankLogItemRender:OnFlush()
	if not self.data then
		return
	end

    local is_top_3
    local player_rank
    if self.data.no_true_rank then  --未上榜
        self.node_list.need_cap_value.text.text = Language.OperationActivity.BaoMi
		self.node_list.need_cap_value2.text.text = Language.OperationActivity.BaoMi

		self.node_list.player_name.text.text = ""
        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
	else
		self.node_list.need_cap_value.text.text = self.data.rank_data.rank_value
		self.node_list.need_cap_value2.text.text = self.data.rank_data.rank_value
        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
	end

    local user_name = self.data.rank_data.user_name
	if self.data.no_true_rank then
		user_name = Language.OperationActivity.XuWeiYiDai
	end

    self.node_list.player_name.text.text = user_name
	self.node_list.player_name2.text.text = user_name

	self.node_list.player_name.text.enabled = not is_top_3
	self.node_list.player_name2.text.enabled = is_top_3

    self.node_list.need_cap_value.text.enabled = not is_top_3
	self.node_list.need_cap_value2.text.enabled = is_top_3

    self.node_list.img_rank.image.enabled = is_top_3
 
    if not is_top_3 then
			self.node_list.img_bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_bt_2"))
		self.node_list.rank.text.text = player_rank
	else
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. player_rank))
		self.node_list.img_bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_" .. player_rank))
		self.node_list.rank.text.text = ""
	end

end