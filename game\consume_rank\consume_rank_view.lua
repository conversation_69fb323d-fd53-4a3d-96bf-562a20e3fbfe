-------------------------
-- 本服消费榜
-------------------------
ConsumeRankView = ConsumeRankView or BaseClass(SafeBaseView)

function ConsumeRankView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Window
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)

	self:AddViewResource(0, "uis/view/recharge_consume_rank_ui_prefab", "layout_consume_rank")
end

function ConsumeRankView:LoadCallBack()
	self.consume_rank_list = AsyncListView.New(ConsumeRankItem, self.node_list["rank_list"])
	
	XUI.AddClickEventListener(self.node_list["btn_tips"], BindTool.Bind(self.OnClickTipsBtn, self))
	XUI.AddClickEventListener(self.node_list["everyday_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_recommend_gift"], BindTool.Bind(self.SetRecommendPartShow, self, true))
	XUI.AddClickEventListener(self.node_list["btn_close_recommend"], BindTool.Bind(self.SetRecommendPartShow, self, false))

	self.node_list["mianui_show_rank"].toggle.isOn = ConsumeRankWGData.Instance:GetMainUiIsShowConsumeRank()
	self.node_list["mianui_show_rank"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMainUiShowConsumeRankToggle, self))

	self.node_list["txt_rank_tips"].text.text = Language.ConsumeRank.ConsumeRankTips

	self.display_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["model_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	self.display_model:SetRenderTexUI3DModel(display_data)
	self:AddUiRoleModel(self.display_model)
end

function ConsumeRankView:ReleaseCallBack()
	if self.consume_rank_list then
		self.consume_rank_list:DeleteMe()
		self.consume_rank_list = nil
	end

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	if CountDownManager.Instance:HasCountDown("consume_rank_count_down") then
		CountDownManager.Instance:RemoveCountDown("consume_rank_count_down")
	end
end

function ConsumeRankView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.ConsumeRankView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK)
end


function ConsumeRankView:OpenCallBack()
	ConsumeRankWGCtrl.Instance:SendConsumeRankReq(OA_CONSUME_GOLD_RANK_OPERATE_TYPE.OA_CONSUME_GOLD_RANK_OPERATE_TYPE_BASE_INFO)
end

function ConsumeRankView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:StartTimeCountDown()
			self:FlushRankInfo()
			self:FlushRankModel()
			self:FlushDailyGift()
			self:FlushJumpBtns()
		elseif k == "BaseInfo" then
			self:FlushRankInfo()
			self:FlushDailyGift()
		end
	end
end

function ConsumeRankView:FlushRankInfo()
	local data_list = ConsumeRankWGData.Instance:GetRankShowList()
	if IsEmptyTable(data_list) then
		return
	end
	self.consume_rank_list:SetDataList(data_list)

	local my_rank_info = ConsumeRankWGData.Instance:GetSelfRankInfo()
	local my_rank_str = my_rank_info.self_rank == 0 and Language.ConsumeRank.NoRank or string.format(Language.ConsumeRank.RankText, my_rank_info.self_rank)
	self.node_list["my_rank_state"].text.text = my_rank_str
	local self_consume_num = ConsumeRankWGData.Instance:GetSelfConsumeNum()
	self.node_list["my_rank_value"].text.text = string.format(Language.ConsumeRank.ConsumeAmount, self_consume_num)
end

function ConsumeRankView:FlushRankModel()
	local first_rank = ConsumeRankWGData.Instance:GetRankInfoByRank(1) or {}
	local role_id = first_rank.user_id
	if role_id and role_id > 0 then
		self.node_list["champion_name"].text.text = first_rank.user_name
		self.node_list["img_no_champion"]:SetActive(false)
		self.node_list["model_root"]:SetActive(true)

		local plat_type = RoleWGData.Instance.role_vo.plat_type
		BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (data_info)
			local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}
			if self.display_model then
				self.display_model:SetModelResInfo(data_info, special_status_table)
			end
		end, plat_type)
	else
		-- 无上榜
		self.node_list["champion_name"].text.text = Language.ConsumeRank.NoInfoName
		self.node_list["img_no_champion"]:SetActive(true)
		self.node_list["model_root"]:SetActive(false)
	end
end

function ConsumeRankView:FlushDailyGift()
	local reward_flag = ConsumeRankWGData.Instance:GetDailyGiftRewardFlag()
	self.node_list.gift_is_get:SetActive(reward_flag == 1)
	self.node_list.gift_remind:SetActive(reward_flag ~= 1)
end

function ConsumeRankView:FlushJumpBtns()
	local jump_cfg_list = ConsumeRankWGData.Instance:GetCurJumpCfg()
	local child_count = self.node_list["recommend_part"].transform.childCount
	local open_num = 0
	for i = 1, child_count do
		local btn_node = self.node_list["btn_entrance_" .. i]
		if jump_cfg_list[i] then
			local is_fun_open = FunOpen.Instance:GetCfgPathIsOpen(jump_cfg_list[i].jump_path)
			local role_level = RoleWGData.Instance:GetRoleLevel()
			local show_btn = is_fun_open and role_level >= jump_cfg_list[i].show_level
			btn_node:SetActive(show_btn)
			if show_btn then
				open_num = open_num + 1
				local bundle, asset = ResPath.GetMainUIIcon(jump_cfg_list[i].jump_icon)
				btn_node.image:LoadSprite(bundle, asset)
				XUI.AddClickEventListener(btn_node, BindTool.Bind(self.OnClickJumpBtn, self, i))
			end
		else
			btn_node:SetActive(false)
		end
	end
	self.node_list["btn_recommend_gift"]:SetActive(open_num > 0)
end

function ConsumeRankView:OnClickTipsBtn()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.ConsumeRank.Tips)
	rule_tip:SetContent(Language.ConsumeRank.TipsContent, nil, nil, nil, true)
end


function ConsumeRankView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_free = ConsumeRankWGData.Instance:GetDailyGiftRewardFlag()
		if is_buy_free == 1 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllFreeShopBuy)
			return
		end

		ConsumeRankWGCtrl.Instance:SendConsumeRankReq(OA_CONSUME_GOLD_RANK_OPERATE_TYPE.OA_CONSUME_GOLD_RANK_OPERATE_TYPE_DAILY_REWARD)
	end
end

function ConsumeRankView:OnClickMainUiShowConsumeRankToggle(is_on)
	ConsumeRankWGData.Instance:SetMainUiIsShowConsumeRank(is_on and 1 or 0)
end

function ConsumeRankView:OnClickJumpBtn(index)
	local jump_cfg_list = ConsumeRankWGData.Instance:GetCurJumpCfg()
	if jump_cfg_list and jump_cfg_list[index] then
		FunOpen.Instance:OpenViewNameByCfg(jump_cfg_list[index].jump_path)
	end
	self:SetRecommendPartShow(false)
end

function ConsumeRankView:SetRecommendPartShow(is_show)
	self.node_list["btn_close_recommend"]:SetActive(is_show)
	self.node_list["recommend_part"]:SetActive(is_show)
end

------------------------------------活动时间倒计时
function ConsumeRankView:StartTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            local time_str = string.format(Language.ConsumeRank.ActivityTime, TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime()))
            self.node_list["act_time"].text.text = time_str
            CountDownManager.Instance:AddCountDown("consume_rank_count_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end
   
function ConsumeRankView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        local time_str = string.format(Language.ConsumeRank.ActivityTime, TimeUtil.FormatSecondDHM8(valid_time))
        self.node_list["act_time"].text.text = time_str
    end
end

function ConsumeRankView:OnComplete()
    self.node_list["act_time"].text.text = ""
    self:Close()
end

-------------------------------------------
-- 排行Render ConsumeRankItem
-------------------------------------------
ConsumeRankItem = ConsumeRankItem or BaseClass(BaseRender)
function ConsumeRankItem:__init()
end

function ConsumeRankItem:LoadCallBack()
	self.reward_rank_list = AsyncListView.New(ItemCell, self.node_list["item_list"])
	self.head_cell = BaseHeadCell.New(self.node_list["role_head_sculpture"])
	XUI.AddClickEventListener(self.node_list["role_head_sculpture"], BindTool.Bind1(self.RoleInfoList, self))
	-- self.head_cell:SetBgActive(false)
	self.reward_rank_list:SetStartZeroIndex(true)
end

function ConsumeRankItem:__delete()
    if self.reward_rank_list then
		self.reward_rank_list:DeleteMe()
		self.reward_rank_list = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function ConsumeRankItem:RoleInfoList()
	self.head_cell:OpenMenu(nil, ConsumeRankWGCtrl.Instance:GetCacularPos(self.node_list["role_head_sculpture"]), nil, MASK_BG_ALPHA_TYPE.Normal)
end

function ConsumeRankItem:OnFlush()
	if not self.data then
		return
	end
	self.reward_rank_list:SetDataList(self.data.reward_item)
	self.node_list["txt_rank"].text.text = self.index
	self.node_list["rank_bg"]:SetActive(self.index < 4)
	self.node_list["role_rank_img"]:SetActive(self.index < 4)
	if self.index < 4 then
		local bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. self.index)
		self.node_list["rank_bg"].image:LoadSprite(bundle, asset)
		bundle, asset = ResPath.GetCommonImages("a3_ty_panking_" .. self.index)
		self.node_list["role_rank_img"].image:LoadSprite(bundle, asset)
	end

	local rank_data = ConsumeRankWGData.Instance:GetRankInfoByRank(self.index)
	local had_data = rank_data ~= nil and rank_data.user_id and rank_data.user_id > 0
	
	self.node_list["txt_rank_target"]:SetActive(not had_data)
	self.node_list["had_data_root"]:SetActive(had_data)
	self.node_list["txt_rank_target"].text.text = string.format(Language.ConsumeRank.RankCondition, self.data.reach_value)
	self.node_list["role_head_sculpture"]:SetActive(had_data)
	if had_data then
		self.node_list["role_name"].text.text = rank_data.user_name
		self.node_list["txt_rank_value"].text.text = string.format(Language.ConsumeRank.RankValue, rank_data.rank_value)
		local head_data = {
			role_id = rank_data.user_id, sex = rank_data.sex, 
			prof = rank_data.prof, fashion_photoframe = rank_data.fashion_photoframe
		}
		self.head_cell:SetData(head_data)
	end
end
