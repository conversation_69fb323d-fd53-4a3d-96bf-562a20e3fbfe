CrossTreasureWGData = CrossTreasureWGData or BaseClass()

function CrossTreasureWGData:__init()
	if CrossTreasureWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[CrossTreasureWGData] attempt to create singleton twice!")
		return
	end
    CrossTreasureWGData.Instance = self

    self:InitCfg()
	self:InitInfo()
end

function CrossTreasureWGData:__delete()
    CrossTreasureWGData.Instance = nil

    self:DeleteCfg()
	self:DeleteInfo()
end

-- 初始化配置表
function CrossTreasureWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_treasure_auto")
	if cfg then
        self.base_cfg = cfg.other[1]
        self.treasure_seq_cfg = cfg.treasure
        self.treasure_seq_cfg2 = ListToMap(cfg.treasure, "seq")
        self.treasure_item_cfg = ListToMapList(cfg.treasure, "item_id")
        self.treasure_cfg = ListToMapList(cfg.treasure, "type")
        self.treasure_level_cfg = ListToMap(cfg.level, "seq", "level")
        self.beast_pool_cfg = ListToMap(cfg.beast_pool, "id", "seq")
    end
end

-- 清理垃圾
function CrossTreasureWGData:DeleteCfg()
    self.base_cfg = nil
    self.treasure_seq_cfg = nil
    self.treasure_cfg = nil
    self.treasure_level_cfg = nil
    self.beast_pool_cfg = nil
    self.treasure_item_cfg = nil
end

-- 初始化数据
function CrossTreasureWGData:InitInfo()
	RemindManager.Instance:Register(RemindName.CrossTreasureLinzu, BindTool.Bind(self.GetCrossTreasureLinzuRemind, self))
	RemindManager.Instance:Register(RemindName.CrossTreasureBeast, BindTool.Bind(self.GetCrossTreasureBeastRemind, self))
end
-- 清理垃圾
function CrossTreasureWGData:DeleteInfo()
    RemindManager.Instance:UnRegister(RemindName.CrossTreasureLinzu)
	RemindManager.Instance:UnRegister(RemindName.CrossTreasureBeast)
end

-- 获取灵珠红点信息
function CrossTreasureWGData:GetCrossTreasureLinzuRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.CrossTreasureView)
	if not is_open then
		return 0
	end

    if self:GetHasCrossTreasureOrFinish() then
        return 1
    end

    return 0
end

-- 获取掠夺信息红点
function CrossTreasureWGData:GetCrossTreasureBeastRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.CrossTreasureView)
	if not is_open then
		return 0
	end

    if self:GetHasCrossTreasureStealTimes() then
        return 1
    end

    return 0
end

-- 获取基础配置
function CrossTreasureWGData:GetBaseCfg()
	return self.base_cfg
end

-- 获取所有藏宝图信息
function CrossTreasureWGData:GetAllTreasureCfg()
	return self.treasure_seq_cfg
end

-- 获取所有藏宝图信息
function CrossTreasureWGData:GetTreasureCfgBySeq(seq)
	local empty = {}
	return (self.treasure_seq_cfg2 or empty)[seq] or nil
end

-- 获取藏宝图信息
function CrossTreasureWGData:GetTreasureDetailCfgByType(type)
	local empty = {}
	return (self.treasure_cfg or empty)[type] or nil
end

-- 获取所有藏宝图等级信息列表
function CrossTreasureWGData:GetAllTreasureLevelCfgBySeq(seq)
	local empty = {}
	return (self.treasure_level_cfg or empty)[seq] or nil
end

-- 获取所有藏宝图等级信息
function CrossTreasureWGData:GetAllTreasureLevelCfgBySeqLevel(seq, level)
	local empty = {}
	return ((self.treasure_level_cfg or empty)[seq] or empty)[level]
end

-- 获取所有藏宝图等级信息
function CrossTreasureWGData:GetBeastPoolCfgByIdSeq(id, seq)
	local empty = {}
	return ((self.beast_pool_cfg or empty)[id] or empty)[seq]
end
------------------------------protocol-----------------------
-- 跨服藏宝 灵珠信息
function CrossTreasureWGData:SetTreasureInfo(protocol)
    if not self.treasure_server_list then
        self.treasure_server_list = {}
    end

    if not self.treasure_server_list[protocol.server_id] then
        self.treasure_server_list[protocol.server_id] = {}
    end

    local data = {}
    data.seq = protocol.seq
    data.server_id = protocol.server_id
    data.level_count_list = protocol.level_count_list

    self.treasure_server_list[protocol.server_id][protocol.seq] = data
end

-- 获取当前服务器的数据
function CrossTreasureWGData:GetTreasureInfoByServerId(server_id, seq)
    local empty = {}
	return ((self.treasure_server_list or empty)[server_id] or empty)[seq]
end

-- 设置跨服藏宝个人信息
function CrossTreasureWGData:SetTreasureRoleInfo(protocol)
    self.treasure_role_info = protocol
end

-- 设置跨服藏宝基础信息
function CrossTreasureWGData:SetTreasureBaseInfo(protocol)
    self.treasure_base_info = protocol
end

-- 设置跨服藏宝幻兽信息
function CrossTreasureWGData:SetTreasureBeastInfo(protocol)
    if not self.beast_server_list then
        self.beast_server_list = {}
    end

    local data = {}
    data.grade = protocol.grade
    data.server_id = protocol.server_id
    data.special_beast_num = protocol.special_beast_num
    data.beast_num = protocol.beast_num
    self.beast_server_list[protocol.server_id] = data
end

-- 获取每日已经掠夺的灵珠次数
function CrossTreasureWGData:GetDailyStealedNum()
    local empty = {}
	return (self.treasure_base_info or empty).loot_times or 0
end

-- 获取服务器的幻兽信息
function CrossTreasureWGData:GetServerTreasureBeastByServerId(server_id)
    local empty = {}
	return (self.beast_server_list or empty)[server_id]
end

-- 获取幻兽捕捉次数
function CrossTreasureWGData:GetServerTreasureBeastGatherTimes()
    local empty = {}
	return (self.treasure_base_info or empty).beast_gather_times or 0
end

-- 获取幻兽捕捉购买次数
function CrossTreasureWGData:GetServerTreasureBeastGatherBuyTimes()
    local empty = {}
	return (self.treasure_base_info or empty).beast_gather_buy_times or 0
end

-----------------------------------------------------------------------
-- 获取当前放置的灵珠数量
function CrossTreasureWGData:GetNowSelfTreasureList()
    local empty = {}
    local list = (self.treasure_role_info or empty).treasure_item_list
    local act_list = {}

    if not list then
        return act_list, 0
    end

    for i, v in ipairs(list) do
        if v.level ~= 0 then
            table.insert(act_list, v)
        end
    end

    return act_list, #act_list
end

-- 获取当前的
function CrossTreasureWGData:GetIsTreasureItemChange(item_id)
    if self.treasure_item_cfg[item_id] ~= nil then
        return true
    end

    return false
end

-- 获取当前自身的所有的灵珠信息
-- 给与长度，长度不够则取占位数据
function CrossTreasureWGData:GetRoleAllTreasureList(full_list_count)
    local list = self:GetNowSelfTreasureList()
    local is_used = #list
    local empty = {reserve_seat = true}

    local complement_fun = function()
        for i = 1, full_list_count do
            if i > #list then
                table.insert(list, empty)
            end
        end
    end

    if full_list_count ~= nil then
        complement_fun()
    end

    return list, is_used
end

-- 获取灵珠列表信息(传入服务器数据)(返回字符串)
function CrossTreasureWGData:GetServerTreasureCountList(server_data)
    if (not server_data) or (not server_data.level_count_list) then
        return "", nil
    end

    local str = ""
    local max_lv = 0

    for level = #server_data.level_count_list, 1, -1 do
        local data = server_data.level_count_list[level]
        local cfg = self:GetAllTreasureLevelCfgBySeqLevel(server_data.seq, level)
        if cfg then
            if data > 0 and level > max_lv then
                max_lv = level
            end

            local color = data > 0 and COLOR3B.GREEN or COLOR3B.RED
            local desc_str = string.format("%s：%s", ToColorStr(cfg.name, ITEM_COLOR[cfg.color]), ToColorStr(data, color)) 
            if str == "" then
                str = desc_str
            else
                str = string.format("%s %s", str, desc_str)
            end
        end
    end

    return str, max_lv
end

-- 获取灵珠列表信息(传入服务器数据)（返回列表）
function CrossTreasureWGData:GetServerTreasureCountList2(server_data)
    if (not server_data) or (not server_data.level_count_list) then
        return {}, nil
    end

    local return_list = {}
    for level = #server_data.level_count_list, 1, -1 do
        local data = server_data.level_count_list[level]
        local cfg = self:GetAllTreasureLevelCfgBySeqLevel(server_data.seq, level)
        if cfg then
            local color = data > 0 and COLOR3B.GREEN or COLOR3B.RED
            local desc_str = string.format("%s：%s", ToColorStr(cfg.name, ITEM_COLOR[cfg.color]), ToColorStr(data, color)) 
            table.insert(return_list, desc_str)
        end
    end

    return return_list
end

-- 获取是否石活动期间的boss
function CrossTreasureWGData:IsServerTreasureBeastStatusBoss()
    local is_catch_beast = false
    local scene_type = Scene.Instance:GetSceneType()
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_BEAST)
    local act_status = CrossAirWarWGData.Instance:GetAirWarActivityStatus()
    if act_info and act_info.status == ACTIVITY_STATUS.OPEN then
        is_catch_beast = true
    end

    return scene_type == SceneType.Common and is_catch_beast
end

-- 获取是否有掠夺次数
function CrossTreasureWGData:GetHasCrossTreasureStealTimes()
    local max_gather_times = self.base_cfg and self.base_cfg.gather_times or 0
	local steal_num = self:GetDailyStealedNum()
	local last_num = max_gather_times - steal_num
    return last_num >= 1, last_num
end

-- 获取当前的自己的灵珠是否可以采集
function CrossTreasureWGData:GetHasCrossTreasureOrFinish()
    local has_num, list, lingzhu_data = self:GetHasCrossTreasureUseItemNum()

    if has_num then
        return true
    end

    if list then
        for i, v in ipairs(list) do
            if self:GetHasOneCrossTreasureFinish(v, lingzhu_data) then
                return true
            end
        end
    end

    return false
end

-- 获取当前自己的灵珠是否可以放置
function CrossTreasureWGData:GetHasCrossTreasureUseItemNum()
    local max_treasure = self.base_cfg and self.base_cfg.max_treasure or 0
    local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
    local list = CrossTreasureWGData.Instance:GetNowSelfTreasureList()
    local lingzhu_data = lingzhu_list and lingzhu_list[1]
	if not lingzhu_data then
		return false, list, lingzhu_data
	end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(lingzhu_data.item_id)
	local need_num = 1

    -- 有物品且可以放置
    if #list < max_treasure and item_num >= need_num then
        return true, list, lingzhu_data
    end

    return false, list, lingzhu_data
end

-- 获取当前自己的灵珠是否可以采集
function CrossTreasureWGData:GetHasOneCrossTreasureFinish(data, lingzhu_data)
    local type_cfg = self:GetAllTreasureLevelCfgBySeqLevel(data.seq, data.level)
	if not type_cfg then
		return false, nil
	end

    local mature_time = lingzhu_data.mature_times or 1
	local start_time = data.time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local remain_time = server_time - start_time

	if remain_time < mature_time then
        return false, remain_time
    else
        return true, remain_time
    end
end

-- 获取当前自己的灵珠是否可以种植
function CrossTreasureWGData:GetUserPosIsCanGather(pos_x, pos_y)
    local is_block = AStarFindWay:IsBlock(pos_x, pos_y)
    local is_safe_area = AStarFindWay:IsInSafeArea(pos_x, pos_y)

    if is_block or is_safe_area then
        return false
    end

    return true
end

-- 判断当前位置是否可以种植
function CrossTreasureWGData:GetUserPosIsNearForOther()
    local min_range = self.base_cfg and self.base_cfg.min_range or 2
    local has_num, list, lingzhu_data = self:GetHasCrossTreasureUseItemNum()

    if not list then
        return false
    end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role then
        return false
    end

    for i, v in ipairs(list) do
        local logic = u3d.vec2(v.pos_x, v.pos_y)
        local target_dis = main_role:GetLogicDistance(logic, false)

        if target_dis <= min_range then
            return true
        end
    end

    return false
end


