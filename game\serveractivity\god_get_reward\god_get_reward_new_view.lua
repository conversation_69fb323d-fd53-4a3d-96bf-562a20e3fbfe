GodGetRewardNewView = GodGetRewardNewView or BaseClass(SafeBaseView)
function GodGetRewardNewView:__init(view_name)
	self.view_style = ViewStyle.Full
	self.view_name = "GodGetRewardNewView"
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/ts_duobao_ui_prefab", "layout_ts_duobao")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
	self:SetMaskBg(true, true)
end

function GodGetRewardNewView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitMoneyBar()
	self:InitListener()
end

function GodGetRewardNewView:ReleaseCallBack()
	self.cur_draw_info = nil
	CountDownManager.Instance:RemoveCountDown("godget_reward_count_down")

	if self.bubble_reward_item_list then
		for _,v in pairs(self.bubble_reward_item_list) do
			v:DeleteMe()
		end
		self.bubble_reward_item_list = nil
	end

	-- if self.const_buy_alert then
	-- 	self.const_buy_alert:DeleteMe()
	-- 	self.const_buy_alert = nil
	-- end

	if self.next_round_alert then
		self.next_round_alert:DeleteMe()
		self.next_round_alert = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	if self.cambered_list then
		self.cambered_list:DeleteMe()
		self.cambered_list = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function GodGetRewardNewView:ShowIndexCallBack(index)
	self.only_play_onec_anim_flag = nil
	self:PlayChangeIndexPanelAnim()
	self:SelectPageIndex()
end

function GodGetRewardNewView:PlayChangeIndexPanelAnim()
	local tween_info = UITween_CONSTS.GodGetReward

	UITween.CleanAllTween(GuideModuleName.GodGetReward)
	UITween.FakeHideShow(self.node_list["tween_mask"])
	UITween.FakeHideShow(self.node_list["bottom_info_root"])
	RectTransform.SetAnchoredPositionXY(self.node_list["bottom_panel"].rect, 0, -150)
	--self.node_list["reward_group"]:SetActive(false)

	if not self.only_play_onec_anim_flag then--界面首次打开播放的动画
		self.only_play_onec_anim_flag = true

		RectTransform.SetAnchoredPositionXY(self.node_list["left_toggle_panel"].rect, -200, 0)
		self.node_list["left_toggle_panel"].rect:DOAnchorPos(Vector2(0, 0), tween_info.MoveTime)
	end

	UITween.AlphaShow(GuideModuleName.GodGetReward, self.node_list["tween_mask"], 0, tween_info.ToAlpha, tween_info.MoveTime, tween_info.AlphaShowType)
	self.node_list["bottom_panel"].rect:DOAnchorPos(Vector2(0, 0), tween_info.MoveTime)
	ReDelayCall(self, function()
		UITween.AlphaShow(GuideModuleName.GodGetReward, self.node_list["bottom_info_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime)
		--self.node_list["reward_group"]:SetActive(true)
		--self:PlayBubbleScaleAnim()
	end, tween_info.AlphaDelay, "god_get_reward_tween")

	--self:PlayBirdFlyOutTween()
end

--奖励气泡缩放动画
function GodGetRewardNewView:PlayBubbleScaleAnim()
	local tween_info = UITween_CONSTS.GodGetReward
	if not IsEmptyTable(self.bubble_reward_item_list)then
		local end_scale = Vector3(tween_info.BubbleEndScale, tween_info.BubbleEndScale, tween_info.BubbleEndScale)
		local scale_1 = tween_info.BubbleScale_1
		local scale_2 = tween_info.BubbleScale_2
		
		for k, v in pairs(self.bubble_reward_item_list) do
			local bubble_root_node = v:GetBubbleRootNode()
			if bubble_root_node then
				if k <= 3 then
					Transform.SetLocalScaleXYZ(bubble_root_node.transform, scale_1, scale_1, scale_1)
				else
					Transform.SetLocalScaleXYZ(bubble_root_node.transform, scale_2, scale_2, scale_2)
				end
				bubble_root_node.rect:DOScale(end_scale, tween_info.BubbleEndScaleTime)
			end
		end
	end
end

function GodGetRewardNewView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" or k == "day_change" then
			self:FlushToggleActive()
			if v.open_param then
				self:SelectPageIndex(v.open_param)
			end
			self:FlushToggleRemind(true)
			self:FlushExchangeBtnRemind()
		elseif k == "layer_info" then
			self:FlushToggleRemind(true)
			self:FlushExchangeBtnRemind()
		elseif k == "reward" then
			self:PopupGetRewardPanel()
			self:FlushConstItem(true)
			self:FlushRoundTime()
			self:FlushToggleRemind()
			self:FlushExchangeBtnRemind()
			if self.has_exchange_btn then
				GodGetRewardWGCtrl.Instance:SendOpera(TS_XUNBAO_OPERA_TYPE.EXCHANGE_INFO, self.cur_layer) -- 兑换积分刷新，后端不主动推
			end
		elseif k == "bubble_tween_complete" then
			self:PopupGetRewardPanel()
		elseif k == "exchange" then
			--self:FlushExchangeScore()
		end
	end
end

function GodGetRewardNewView:InitParam()
	self.toggle_num = 4
	self.page_index = 1
	self.cur_layer = 0
	self.cur_draw_info = nil
	self.has_exchange_btn = false
	self.has_exchange_remind = false
	self.is_last_bubble = false
	self.is_pass_time = false
	self.bubble_reward_item_list = {}
end

function GodGetRewardNewView:InitMoneyBar()
	self.money_bar = MoneyBar.New()
	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
end

function GodGetRewardNewView:InitPanel()
	local cambered_list_data = {
		item_render = GodGetRewardBubble,
		asset_bundle = "uis/view/ts_duobao_ui_prefab",
		asset_name = "duobao_reward_item",
		scroll_list = self.node_list.reward_group,
		center_x = 0, center_y = 0,
		radius_x = 315, radius_y = 150,-- x 椭圆半长轴,y 椭圆半短轴
		angle_delta = Mathf.PI / 3.5,
		origin_rotation = Mathf.PI,
		is_drag_horizontal = true,
		scale_min = 0.6,			-- 最小缩放比例
		need_change_scale = true,
		is_assist = true,
		behind = self.node_list.behind,
		front = self.node_list.front,
	}

	self.cambered_list = CamberedList.New(cambered_list_data)
	self.cambered_list:CreateCellList(7)
	self.cambered_list:AutoMove()

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.display)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	local res_async_loader = AllocResAsyncLoader(self, "duobao_reward_item")
	res_async_loader:Load("uis/view/ts_duobao_ui_prefab", "duobao_reward_item", nil,
		function(new_obj)
			if not new_obj then
				return
			end

			local item_root = self.node_list.reward_group
			local child_count = item_root.transform.childCount
			local item_list = {}
			for i=1,child_count do 
				local obj_root = item_root:FindObj("reward_item_" .. i)
				if obj_root then
					local obj = ResMgr:Instantiate(new_obj)
					obj.transform:SetParent(obj_root.transform, false)
					item_list[i] = GodGetRewardBubble.New(obj)
					item_list[i]:SetIndex(i)
					--item_list[i]:SetTotalPos(obj_root.rect.anchoredPosition)
				end
			end

			self.bubble_reward_item_list = item_list
			self:FlushBubbleRewardList()
		end)

	self.item_data_event = BindTool.Bind1(self.OnItemDataChange, self)
	self.node_list.title_view_name.text.text = Language.TSXunBao.Title
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function GodGetRewardNewView:InitListener()
	XUI.AddClickEventListener(self.node_list.tip_btn, BindTool.Bind(self.OnClickTipBtn, self))
	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.record_btn, BindTool.Bind(self.OnClickRecordBtn, self))
	XUI.AddClickEventListener(self.node_list.exchange_btn, BindTool.Bind(self.OnClickExchangeBtn, self))
	XUI.AddClickEventListener(self.node_list.const_click_img, BindTool.Bind(self.OnClickConstItemBtn, self))

	for i = 1,self.toggle_num do
		local layer_cfg = GodGetRewardWGData.Instance:GetLayerCfgByLayer(i - 1)
		self.node_list["toggle_item_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickPageToggle, self, i))
		if layer_cfg then
			self.node_list["name_pt_" .. i].text.text = layer_cfg.name
			self.node_list["name_hl_" .. i].text.text = layer_cfg.name
		end
	end
end

function GodGetRewardNewView:FlushModel(reward_id, totla_num)
	local reward_info = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(reward_id)
	if not reward_info then
		return
	end

	local state = GodGetRewardWGData.Instance:GetRewardIsShowByLayerAndId(self.cur_layer, totla_num)
	self.node_list.display:SetActive(state)
	self.node_list.none_bg:SetActive(not state)
	local item_data = reward_info.reward_item
	if not item_data then
		return
	end

	-- 形象展示
	local display_data = {}
	display_data.should_ani = true
	display_data.item_id = item_data.item_id
	display_data.render_type = 0
	display_data.need_wp_tween = true
	display_data.hide_model_block = false
	display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL)
    end
	self.model_display:SetData(display_data)
end

function GodGetRewardNewView:OnClickRecordBtn()
    GodGetRewardWGCtrl.Instance:SendOpera(TS_XUNBAO_OPERA_TYPE.DRAW_RECORD)
end

function GodGetRewardNewView:OnClickTipBtn()
	RuleTip.Instance:SetContent(Language.TSXunBao.TipContent, Language.TSXunBao.TipTitle)
end

function GodGetRewardNewView:OnClickExchangeBtn()
	GodGetRewardWGCtrl.Instance:OpenExchangeView()
	if self.has_exchange_remind then
		GodGetRewardWGData.Instance:SetExchangeTodayRemind(self.cur_layer)
		self.node_list.exchange_btn_remind:SetActive(false)
		self:FlushToggleRemind()
		RemindManager.Instance:Fire(RemindName.GodGetReward)
	end
end


function GodGetRewardNewView:OnClickConstItemBtn()
	if self.cur_draw_info then
		TipWGCtrl.Instance:OpenItem({item_id = self.cur_draw_info.draw_consume_item_id}, ItemTip.FROM_NORMAL)
	end
end

function GodGetRewardNewView:OnItemDataChange(item_id, index, reason, put_reason, old_num, new_num)
	if self.cur_draw_info and item_id == self.cur_draw_info.draw_consume_item_id then
		self:FlushConstItem()
		self:FlushToggleRemind()
	end
end

function GodGetRewardNewView:OnClickPageToggle(index)
	self.page_index = index
	self.cur_layer = index - 1
	GodGetRewardWGData.Instance:SetSelectLayer(self.cur_layer)
	self:RefreshView()
	self:FlushConstItem(true)
	self:FlushRoundTime()
	self:FlushBubbleRewardList()
	self:FlushExchangeBtnRemind()
	self:PlayChangeIndexPanelAnim()
end

function GodGetRewardNewView:SelectPageIndex(page_index)
	local index = 1
	if page_index then
		index = page_index
	else
		for i = 1, self.toggle_num do
			if GodGetRewardWGData.Instance:IsShowBtnDrawRedByLayer(i - 1) then
				index = i
				break
			end
		end
	end

	local toggle_item = self.node_list["toggle_item_" .. index]
	if toggle_item and toggle_item:GetActive() then
		if toggle_item.toggle.isOn == true then
			self:OnClickPageToggle(index)
		else
			toggle_item.toggle.isOn = true
		end
	end
end

function GodGetRewardNewView:RefreshView()
	local index = self.page_index
	local path_index = 1
	-- self.node_list.tip_desc.text.text = Language.TSXunBao.QuesTip[index]
	--策划需求 龙凤底图和龙凤图  换着变。。
	if index == 1 then --神龙寻宝
		path_index = 1
	elseif index == 2 then --天龙寻宝
		path_index = 1
	else					--剩下的两个寻宝
		path_index = 3
	end

	self.node_list.title_bg.image:LoadSprite(ResPath.GetTsDuoBaoIcon("a2_ycwx_djmc_" .. path_index))
	self.node_list.duobao_sun.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a2_ycwx_yp_" .. path_index))
	self.has_exchange_btn = GodGetRewardWGData.Instance:HasExchangeByLayer(self.cur_layer)
	self.node_list.exchange_btn:SetActive(self.has_exchange_btn)
end

function GodGetRewardNewView:FlushToggleActive()
	for i = 1, self.toggle_num do
		self.node_list["toggle_item_" .. i]:SetActive(GodGetRewardWGData.Instance:CheckLayerIsOpenByPageIndex(i))
	end
end

function GodGetRewardNewView:FlushToggleRemind(is_all)
	if is_all then
		for i=1,self.toggle_num do
			self.node_list["toggle_remind_" .. i]:SetActive(GodGetRewardWGData.Instance:IsShowBtnDrawRedByLayer(i - 1))
		end
	else
		self.node_list["toggle_remind_" .. self.page_index]:SetActive(GodGetRewardWGData.Instance:IsShowBtnDrawRedByLayer(self.cur_layer))
	end
end

function GodGetRewardNewView:FlushExchangeBtnRemind()
	if self.has_exchange_btn then
		self.has_exchange_remind = GodGetRewardWGData.Instance:GetExchangeTodayRemind(self.cur_layer)
		self.node_list.exchange_btn_remind:SetActive(self.has_exchange_remind)
	end
end

-- function GodGetRewardNewView:FlushExchangeScore()
-- 	if self.has_exchange_btn then
-- 		self.node_list.exchange_label.text.text = GodGetRewardWGData.Instance:GetExchangeScore(self.cur_layer)
-- 	end
-- end

function GodGetRewardNewView:FlushConstItem(need_flush)
	if not self.cur_draw_info or need_flush then
		self.cur_draw_info = GodGetRewardWGData.Instance:GetCurDrawInfoByLayer(self.cur_layer)
	end
	local cur_draw_info = self.cur_draw_info
	if not cur_draw_info then
		self.node_list.buy_btn_remind:SetActive(false)
		self.node_list.const_icon:SetActive(false)
		return
	end
	self.node_list.const_icon:SetActive(true)

	if need_flush then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cur_draw_info.draw_consume_item_id)
		if item_cfg then
			local bundle,asset = ResPath.GetItem(item_cfg.icon_id)
			self.node_list.const_icon.image:LoadSprite(bundle,asset)
		end
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local can_buy = item_num >= cur_draw_info.draw_consume_item_count
	local color = can_buy and COLOR3B.D_GREEN or COLOR3B.D_RED
	local is_empty = GodGetRewardWGData.Instance:GetIsDrawEmptyByLayer(self.cur_layer)
	self.node_list.const_label.text.text = ToColorStr(item_num .. "/" .. cur_draw_info.draw_consume_item_count, color)
	self.node_list.buy_btn_remind:SetActive(can_buy and not is_empty)
end

function GodGetRewardNewView:FlushBubbleRewardList()
	local cur_layer = self.cur_layer
	local big_reward_list, cambered_list_data, other_list_data = GodGetRewardWGData.Instance:GetRewardListByLayer(cur_layer)--GodGetRewardWGData.Instance:GetCurRewardListByLayer(cur_layer)
	local item_list = self.bubble_reward_item_list
	local totla_num = #big_reward_list + #cambered_list_data + #other_list_data
	if IsEmptyTable(big_reward_list) or #item_list == 0 or IsEmptyTable(cambered_list_data) or IsEmptyTable(other_list_data)then
		local btn_item_list = self.cambered_list:GetRenderList()
	    for k, item_cell in ipairs(btn_item_list) do
	        item_cell:SetData({state = false, layer = cur_layer, is_special = true})
	    end

	    for i = 1, #item_list do
			item_list[i]:SetData({state = false, layer = cur_layer, is_special = false})
		end

		self.node_list.none_bg:SetActive(true)
		self.node_list.display:SetActive(false)
		return
	end

	self.node_list.center_panel:SetActive(true)
	local result_num = GodGetRewardWGData.Instance:GetLayerRewardNumByLayer(cur_layer)
	self.is_last_bubble = totla_num - result_num <= 1
	local index = 1
	local sub_num = totla_num + 1
	--9-16
	for i = 1, #item_list do
		local state = GodGetRewardWGData.Instance:GetRewardIsShowByLayerAndId(cur_layer, sub_num - (i + 8))
		item_list[i]:SetData({reward_id = other_list_data[i], state = state, layer = cur_layer, is_special = false})
	end
	--2-8
    local btn_item_list = self.cambered_list:GetRenderList()
    for k, item_cell in ipairs(btn_item_list) do
    	local state = GodGetRewardWGData.Instance:GetRewardIsShowByLayerAndId(cur_layer, sub_num - (k + 1))
        item_cell:SetData({reward_id = cambered_list_data[k], state = state, layer = cur_layer, is_special = true})
    end

    self:FlushModel(big_reward_list[1], totla_num)
end

---[[ 计时间
function GodGetRewardNewView:FlushRoundTime()
	local next_time, cur_round_count, max_round_count = GodGetRewardWGData.Instance:GetActRoundTimeByLayer(self.cur_layer)
	local is_empty = GodGetRewardWGData.Instance:GetIsDrawEmptyByLayer(self.cur_layer)
	if is_empty then
		self.node_list.act_count_down_desc.text.text = Language.TSXunBao.special_desc1
		next_time = 0
	elseif self.cur_layer == 0 then
		self.node_list.act_count_down_desc.text.text = Language.TSXunBao.special_desc4
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOD_XUNBAO)
		local now_time = TimeWGCtrl.Instance:GetServerTime()
		next_time = act_info and act_info.next_time or 0
		next_time = next_time - now_time
	elseif next_time > 0 and cur_round_count >= max_round_count then
		self.node_list.act_count_down_desc.text.text = Language.TSXunBao.special_desc3
	else
		self.node_list.act_count_down_desc.text.text = Language.TSXunBao.special_desc2
	end

	if cur_round_count and max_round_count then
		self.node_list.tip_desc.text.text = string.format(Language.TSXunBao.RoundDesc, cur_round_count, max_round_count)
	end

	self.is_pass_time = next_time <= 0
	self:FlushActCountDown(next_time)
end

function GodGetRewardNewView:FlushActCountDown(count_down_time)
	CountDownManager.Instance:RemoveCountDown("godget_reward_count_down")
	if count_down_time > 0 then
		self.node_list.act_count_down_lbl.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
		CountDownManager.Instance:AddCountDown(
			"godget_reward_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.FlushRoundTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.act_count_down_lbl.text.text = Language.TSXunBao.YiJieShu
	end
end

function GodGetRewardNewView:UpdateCountDown(elapse_time, total_time)
	self.node_list.act_count_down_lbl.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end
--]]

---[[ 放小鸟
function GodGetRewardNewView:OnClickBuyBtn()
	-- if self.bird_fly_tween then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.TSXunBao.BirdCDTips)
	-- 	return
	-- end

	if self.is_pass_time then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivityIsEnd)
		return
	end

	local cur_draw_info = GodGetRewardWGData.Instance:GetCurDrawInfoByLayer(self.cur_layer)
	if cur_draw_info == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TSXunBao.TxtTime)
		return
	end

	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(cur_draw_info.shop_seq or 0)
	local price = shop_cfg and shop_cfg.price or 0

	local tips_data = {}
	tips_data.item_id = cur_draw_info.draw_consume_item_id
	tips_data.price = price
	tips_data.draw_count = cur_draw_info.draw_consume_item_count
	tips_data.has_checkbox = true
	tips_data.checkbox_str = string.format("ts_xunbao_%d", self.cur_layer)
	TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind(self.SendBuy, self, true), nil)
end

function GodGetRewardNewView:SendBuy(is_auto_buy)
	GodGetRewardWGCtrl.Instance:SendOpera(TS_XUNBAO_OPERA_TYPE.DRAW, self.cur_layer, is_auto_buy and 1 or 0)
end

-- function GodGetRewardNewView:GetBuyAlert()
-- 	if not self.const_buy_alert then
-- 		self.const_buy_alert = Alert.New()
-- 		self.const_buy_alert:SetShowCheckBox(true, "ts_xunbao")
-- 		self.const_buy_alert:SetCheckBoxDefaultSelect(false)
-- 		self.const_buy_alert:SetCheckBoxText(Language.TSXunBao.AlertTip)
-- 	end
-- 	return self.const_buy_alert
-- end

function GodGetRewardNewView:PopupGetRewardPanel()
	local result = GodGetRewardWGData.Instance:GetResultInfo()
    local reward_cfg = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(result.reward_id)
	local cur_draw_info = GodGetRewardWGData.Instance:GetCurDrawInfoByLayer(self.cur_layer)

    if not IsEmptyTable(reward_cfg) and cur_draw_info then
		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(cur_draw_info.shop_seq or 0)
		local price = shop_cfg and shop_cfg.price or 0

		local other_info = {}
		other_info.stuff_id = cur_draw_info.draw_consume_item_id
		other_info.times = cur_draw_info.draw_consume_item_count
		other_info.spend = price
		other_info.again_text = Language.TreasureHunt.BtnText[1]
		TipWGCtrl.Instance:ShowGetValueReward({reward_cfg.reward_item}, BindTool.Bind(self.OnClickBuyBtn, self), other_info, false, BindTool.Bind(self.ResultCloseCallBack, self))
    end
end

function GodGetRewardNewView:ResultCloseCallBack()
	if self.is_last_bubble then
		local is_empty = GodGetRewardWGData.Instance:GetIsDrawEmptyByLayer(self.cur_layer)
		if not is_empty then
			self:OpenNextRoundAlert()
		else
			self:FlushBubbleRewardList()
		end
	else
		self:FlushBubbleRewardList()
	end
end

function GodGetRewardNewView:OpenNextRoundAlert()
	if not self.next_round_alert then
		local func = BindTool.Bind(self.FlushBubbleRewardList, self)
		local temp_alert = Alert.New()
		temp_alert:SetLableString(Language.TSXunBao.NextLayerTips)
		temp_alert:SetOkFunc(func)
		temp_alert:SetCloseFunc(func)
		temp_alert:SetUseOneSign(true)
		self.next_round_alert = temp_alert
	end
	self.next_round_alert:Open()
end

------------------------------------------------------------------------------------------------

GodGetRewardBubble = GodGetRewardBubble or BaseClass(BaseRender)

function GodGetRewardBubble:__init()
	self.item_data = nil
	self.save_total_pos = nil
end

function GodGetRewardBubble:__delete()
	self.item_data = nil
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GodGetRewardBubble:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.cell_root)
end

function GodGetRewardBubble:SetTotalPos(total_pos)
	self.save_total_pos = total_pos
end

function GodGetRewardBubble:GetTotalPos()
	return self.save_total_pos
end

function GodGetRewardBubble:OnFlush()
	local data = self:GetData()
	self.node_list.special_bg:SetActive(data.is_special)
	local bundle, asset = ResPath.GetTsDuoBaoIcon("a2_ycwx_qp_" .. data.layer + 1)
	self.node_list.color_qp.image:LoadSprite(bundle, asset)
	bundle, asset = ResPath.GetTsDuoBaoIcon("a2_ycwx_xdztx_" .. data.layer + 1)
	self.node_list.color_bg.image:LoadSprite(bundle, asset)

	if not data.reward_id or not data.state then
		self.node_list.cell_root:SetActive(false)
		--self:SetVisible(false)
		return
	end

	self:RestItem()
	self.node_list.cell_root:SetActive(true)
	--self:SetVisible(true)
	local reward_info = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(data.reward_id)
	if not reward_info then
		return
	end

	self.item_data = reward_info.reward_item
	if not self.item_data then
		return
	end

	self.item_cell:SetData(self.item_data)
end

function GodGetRewardBubble:OnClickPopupItemTip()
	if self.item_data then
		TipWGCtrl.Instance:OpenItem(self.item_data, ItemTip.FROM_NORMAL)
	end
end

function GodGetRewardBubble:RestItem()
	local canvas_group = self.node_list.cell_root.canvas_group
	if canvas_group then
		canvas_group.alpha = 1
	end
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, 0, 0)
	--self.node_list.bubble_img:SetActive(true)
end

function GodGetRewardBubble:GetBubbleRootNode()
	if self.node_list["duobao_reward_item_root"] then
		return self.node_list["duobao_reward_item_root"]
	end
	return nil
end