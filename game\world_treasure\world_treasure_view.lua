WorldTreasureView = WorldTreasureView or BaseClass(SafeBaseView)

local TAB_ICON_RES = {
    ["tcdb_login_gift"] = "a2_tcdb_icon_1",
    ["tcdb_first_recharge"] = "a2_tcdb_icon_2",
    ["tcdb_total_recharge"] = "a2_tcdb_icon_1",
    ["tcdb_jigsaw"] = "a2_tcdb_icon_3",
	["tcdb_flash_sale1"] = "a2_tcdb_icon_4",
}

local TAB_INDEX = {
	["tcdb_first_page"] = TabIndex.tcdb_first_page,					-- 通天降临-首页*
	["tcdb_login_gift"] = TabIndex.tcdb_login_gift,					-- 天财地宝-登录有礼
	["tcdb_first_recharge"] = TabIndex.tcdb_first_recharge,			-- 天财地宝-首充送礼
	["tcdb_flowing"] = TabIndex.tcdb_flowing,						-- 通天降临-川流不息*
	["tcdb_raise_star_gift"] = TabIndex.tcdb_raise_star_gift,		-- 天财地宝-升星赠礼*
	["tcdb_together"] = TabIndex.tcdb_together,						-- 通天降临-携手同行*
	["tcdb_jigsaw"] = TabIndex.tcdb_jigsaw,							-- 天财地宝-拼图领奖
	["tcdb_flash_sale"] = TabIndex.tcdb_flash_sale,					-- 天财地宝-限时抢购
	["tcdb_flash_sale1"] = TabIndex.tcdb_flash_sale1,				-- 天财地宝-幻兽召唤
	["tcdb_flash_sale2"] = TabIndex.tcdb_flash_sale2,				-- 天财地宝-材料商店
	["tcdb_total_recharge"] = TabIndex.tcdb_total_recharge,			-- 天财地宝-累计充值*
	["tcdb_jianglin"] = TabIndex.tcdb_jianglin,						-- 天财地宝-试炼副本
	["tcdb_mowang"] = TabIndex.tcdb_mowang,				    		-- 天财地宝-幻境降魔(掉落)
	["tcdb_pursuit"] = TabIndex.tcdb_pursuit,						-- 通天降临-迷影寻踪*
	["tcdb_scheme"] = TabIndex.tcdb_scheme,							-- 通天降临-奕者谋定*
	["tcdb_premium_shop"] = TabIndex.tcdb_premium_shop,				-- 通天降临-专享商店*
}

local COMMON_EFFECTS = {
	"effect_liuguang_no_delay",
	"effect_liuguang",
	"effect_ty_lizi",
}

local TAB_EFFECT = {
	[TabIndex.tcdb_flowing] = "effect_ty_lizi",						-- 通天降临-川流不息*
	[TabIndex.tcdb_raise_star_gift] = "effect_ty_lizi",				-- 天财地宝-升星赠礼*
	[TabIndex.tcdb_together] = "effect_ty_lizi",					-- 通天降临-携手同行*
	[TabIndex.tcdb_total_recharge] = "effect_ty_lizi",				-- 天财地宝-累计充值*
	[TabIndex.tcdb_pursuit] = "effect_liuguang_no_delay",			-- 通天降临-迷影寻踪*
	[TabIndex.tcdb_scheme] = "effect_ty_lizi",						-- 通天降临-奕者谋定*
	[TabIndex.tcdb_premium_shop] = "effect_liuguang_no_delay",		-- 通天降临-专享商店*
}

function WorldTreasureView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self:SetMaskBg()
	--self.default_index = TabIndex.tcdb_login_gift
	self.view_name = GuideModuleName.WorldTreasureView

	local bond_name = "uis/view/world_treasure_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(TabIndex.tcdb_first_page, bond_name, "layout_world_treasure_first_page")
	self:AddViewResource(TabIndex.tcdb_login_gift, bond_name, "layout_world_treasure_login")
	self:AddViewResource(TabIndex.tcdb_first_recharge, bond_name, "layout_world_treasure_first_recharge")
	self:AddViewResource(TabIndex.tcdb_raise_star_gift, bond_name, "layout_world_treasure_raise_star_gift")
	self:AddViewResource(TabIndex.tcdb_jigsaw, bond_name, "layout_world_treasure_jigsaw")
	--self:AddViewResource(TabIndex.tcdb_flash_sale, bond_name, "layout_world_treasure_limit_buy")
	self:AddViewResource(TabIndex.tcdb_flash_sale1, bond_name, "layout_world_treasure_limit_buy")
	self:AddViewResource(TabIndex.tcdb_flash_sale2, bond_name, "layout_world_treasure_shop")
	self:AddViewResource(TabIndex.tcdb_total_recharge, bond_name, "layout_world_treasure_total_recharge")
	self:AddViewResource(TabIndex.tcdb_jianglin, bond_name, "layout_world_treasure_jianlin")
	self:AddViewResource(TabIndex.tcdb_mowang, bond_name, "layout_world_treasure_mowan")
	self:AddViewResource(TabIndex.tcdb_together, bond_name, "layout_world_treasure_together")
	self:AddViewResource(TabIndex.tcdb_scheme, bond_name, "layout_world_treasure_scheme")
	self:AddViewResource(TabIndex.tcdb_premium_shop, bond_name, "layout_premium_store")
	self:AddViewResource(TabIndex.tcdb_flowing, bond_name, "layout_world_treasure_flowing")
	self:AddViewResource(TabIndex.tcdb_pursuit, bond_name, "layout_world_treasure_pursuit")

	self:AddViewResource(0, bond_name, "layout_world_treasure_common")
	self:AddViewResource(0, bond_name, "HorizontalTabbar")
	self:AddViewResource(0, bond_name, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	self:AddViewResource(0, bond_name, "layout_world_treasure_common_effect_view")
	--self:AddViewResource(0, bond_name, "world_treasure_list_part")

	self.tab_sub = { nil, nil, nil, nil, nil, nil, nil, Language.WorldTreasure.SubGroup5, nil, nil, nil, nil, nil, nil }
	self.remind_tab = { 
		nil, 
		{RemindName.WorldTreasure_Login},
		{RemindName.WorldTreasure_FirstRecharge}, 
		{RemindName.WorldTreasure_Flowing},
		{RemindName.WorldTreasure_RaiseStarGift},
		{RemindName.WorldTreasure_Together}, 
		{RemindName.WorldTreasure_ReceiveAward},
		{RemindName.WorldTreasure_LimitBuy, nil},
		{RemindName.WorldTreasure_TotalReCharge}, 
		{RemindName.WorldTreasure_JiangLin},
		{RemindName.WorldTreasure_MoWang},
		{RemindName.WorldTreasure_Pursuit},
		{RemindName.WorldTreasure_Scheme},
		{RemindName.WorldTreasure_PremiumShop}}
end

function WorldTreasureView:__delete()

end

function WorldTreasureView:CalcShowIndex()
	local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
	self.default_index = grade == 1 and TabIndex.tcdb_first_page or TabIndex.tcdb_login_gift
	return SafeBaseView.CalcShowIndex(self)
end

function WorldTreasureView:LoadCallBack()
	--self.world_treasure_grade_change = true
	self.node_tab_toggle = {}
	self.red_data = {}
	self.select_index = 0
    --self:InitCamberedList()

	-- if not self.tab_bar_list_view then
    --     self.tab_bar_list_view = AsyncListView.New(WorldTreasureListRender, self.node_list.tab_bar_list)
	-- 	self.tab_bar_list_view:SetSelectCallBack(BindTool.Bind(self.OnClickEsotericaSlotBtn, self))
	-- end

	XUI.AddClickEventListener(self.node_list.btn_close_txt, BindTool.Bind(self.OnClickBtnCloseImg,self))

	self:InitTabbar()

	local is_show = WorldTreasureWGData.Instance:GetOpenEffectIsShow()
	if not is_show then
		self.node_list["open_effect"]:SetActive(true)
		ReDelayCall(self, function()
			self.node_list["open_effect"]:SetActive(false)
		end, 3, "world_treasure_open_effect")
	end
	WorldTreasureWGData.Instance:SetOpenEffectIsShow(true)
end

--[[
function WorldTreasureView:LoadBigBg()
	local bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end
]]

function WorldTreasureView:InitTabbar()
	local toggle_name_list = Language.WorldTreasure.NameGroup
	local common_path = "uis/view/common_panel_prefab"
	local bond_path = "uis/view/world_treasure_ui_prefab"
	self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
	self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabVisible, self))
	self.tabbar:Init(toggle_name_list, self.tab_sub, common_path,
		bond_path, self.remind_tab)
	-- self.tabbar:JumpToVerPrecent(1)
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.WorldTreasureView, self.tabbar)
end

function WorldTreasureView:SetTabVisible()
	if nil == self.tabbar then
		return
	end

	local is_open = WorldTreasureWGData.Instance:IsUpstarGiftOpen()
	self.tabbar:SetToggleVisible(TabIndex.tcdb_raise_star_gift, is_open)

	for k, v in pairs(TAB_INDEX) do
		if v ~= TabIndex.tcdb_raise_star_gift then
			local is_tab_show = WorldTreasureWGData.Instance:GetGradeIndexIsOpen(v)
			if not is_tab_show then
				self.tabbar:SetToggleVisible(v, false)
			end
		end
	end
end

function WorldTreasureView:ReleaseCallBack()
	self.node_tab_toggle = nil
	self.red_data = nil
    self.select_index = nil
	
    -- if self.cambered_list then
    --     self.cambered_list:DeleteMe()
    --     self.cambered_list = nil
    -- end
	
	-- if self.tab_bar_list_view then
    --     self.tab_bar_list_view:DeleteMe()
	-- 	self.tab_bar_list_view = nil
	-- end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:FirstPageReleaseCallBack()
	self:ReleaseCallBack_Login()
	self:ReleaseCallBack_FirstRecharge()
	self:ReleaseCallBack_TotalRecharge()
	self:ReleaseCallBack_PinTu()
	self:ReleaseCallBack_Shop()
	self:ReleaseCallBack_LimitBuy()
	self:ReleaseJiangLinView()
	self:ReleaseMWView()
	self:ReleaseCallBack_RaiseStarGift()
	self:TogetherReleaseCallBack()
	self:PremiumStoreReleaseCallBack()
	self:SchemeReleaseCallBack()
	self:FlowingReleaseCallBack()
	self:PursuitReleaseCallBack()
	CountDownManager.Instance:RemoveCountDown("world_treasure_count_down")
end

function WorldTreasureView:LoadIndexCallBack(index)
	if index == TabIndex.tcdb_first_page then
		self:FirstPageLoadCallBack()
	elseif index == TabIndex.tcdb_login_gift then
		self:LoadIndexCallBack_Login()
	elseif index == TabIndex.tcdb_first_recharge then
		self:LoadIndexCallBack_FirstRecharge()
	elseif index == TabIndex.tcdb_raise_star_gift then
		self:LoadIndexCallBack_RaiseStarGift()
	elseif index == TabIndex.tcdb_jigsaw then
        self:LoadIndexCallBack_PinTu()
	elseif index == TabIndex.tcdb_flash_sale1 then
		self:LoadIndexCallBack_LimitBuy()
	elseif index == TabIndex.tcdb_flash_sale2 then
		self:LoadIndexCallBack_Shop()
	elseif index == TabIndex.tcdb_total_recharge then
		self:LoadIndexCallBack_TotalRecharge()
	elseif index == TabIndex.tcdb_jianglin then
		self:InitJiangLinView()
	elseif index == TabIndex.tcdb_mowang then
		self:InitMWView()
	elseif index == TabIndex.tcdb_together then
		self:TogetherLoadCallBack()
	elseif index == TabIndex.tcdb_premium_shop then
		self:PremiumStoreLoadCallBack()
	elseif index == TabIndex.tcdb_scheme then
		self:SchemeLoadCallBack()
	elseif index == TabIndex.tcdb_flowing then
		self:FlowingLoadCallBack()
	elseif index == TabIndex.tcdb_pursuit then
		self:PursuitLoadCallBack()
	end

	-- 临时处理档位2
	local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
	self.node_list["common_effect_root"]:SetActive(grade == 1)
end

-- function WorldTreasureView:CalcShowIndex()
-- 	local list_data = self:GetListData()
-- 	local init_index = 0
-- 	for k_1, v_1 in ipairs(list_data) do
-- 		local red_num = WorldTreasureWGData.Instance:GetRedShow(v_1)
-- 		if init_index == 0 and red_num > 0 then
-- 			init_index = k_1
-- 		end
-- 	end

-- 	init_index = init_index == 0 and 1 or init_index
-- 	return init_index * 10
-- end

-- local SHOW_INDEX_NAME =
-- {
-- 	[1] = "tcdb_login_gift",
-- 	[2] = "tcdb_first_recharge",
-- 	[3] = "tcdb_total_recharge",
-- 	[4] = "tcdb_jigsaw",
-- 	[5] = "tcdb_flash_sale1",
-- }

-- function WorldTreasureView:GetListData()
-- 	local list_data = {}
-- 	for k_i, k_v in ipairs(SHOW_INDEX_NAME) do
-- 		local open_flag = FunOpen.Instance:GetFunIsOpened(FunName[k_v])
-- 		if open_flag then
-- 			table.insert(list_data, k_v)
-- 		end
-- 	end

-- 	return list_data
-- end

function WorldTreasureView:OpenCallBack()
	self.select_index = 0
end

function WorldTreasureView:CloseCallBack()
end

function WorldTreasureView:ShowIndexCallBack(index)
	local bg_name = "a3_yushou_jsrh_bg_1"
	if index == TabIndex.tcdb_first_page then
		bg_name = "a3_ttjl_db"
		self:FirstPageShowIndexCallBack()
	elseif index == TabIndex.tcdb_login_gift then
		self:ShowIndexCallBack_Login()
	elseif index == TabIndex.tcdb_first_recharge then
		self:ShowIndexCallBack_FirstRecharge()
	elseif index == TabIndex.tcdb_raise_star_gift then
		local upstar_grade_cfg = WorldTreasureWGData.Instance:GetUpstarGiftGradeCfg()
		if upstar_grade_cfg and upstar_grade_cfg.bg_res_name and upstar_grade_cfg.bg_res_name ~= "" then
			bg_name = upstar_grade_cfg.bg_res_name
		end
		self:ShowIndexCallBack_RaiseStarGift()
	elseif index == TabIndex.tcdb_jigsaw then
        self:ShowIndexCallBack_PinTu()
	elseif index == TabIndex.tcdb_flash_sale1 then
		self:ShowIndexCallBack_LimitBuy()
	elseif index == TabIndex.tcdb_flash_sale2 then
		self:ShowIndexCallBack_Shop()
	elseif index == TabIndex.tcdb_total_recharge then
		bg_name = WorldTreasureWGData.Instance:GetLeiChongBGRes()
		self:ShowIndexCallBack_TotalRecharge()
	elseif index == TabIndex.tcdb_jianglin then     -- 天神主题-天神降临
		self:TRJLShowIndexCallBack()
	elseif index == TabIndex.tcdb_mowang then -- 天神主题-魔王有礼
		self:ShowMWView()
	elseif index == TabIndex.tcdb_together then
		bg_name = "a3_ttjl_xstx_bg"
		self:TogetherShowIndexCallBack()
	elseif index == TabIndex.tcdb_premium_shop then
		local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade() or 1
		bg_name = "a3_ttjl_bj1_" .. grade
		self:PremiumStoreShowIndexCallBack()
	elseif index == TabIndex.tcdb_scheme then
		bg_name = "a3_yzmtx_bg"
		self:SchemeShowIndexCallBack()
	elseif index == TabIndex.tcdb_flowing then
		local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade() or 1
		bg_name = "a3_ttjl_bj_" .. grade
		self:FlowingShowIndexCallBack()
	elseif index == TabIndex.tcdb_pursuit then
		bg_name = "a3_ttjl_myxz_bg"
		self:PursuitShowIndexCallBack()
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local str = "world_treasure_desc"..main_role_id..index
	local day = PlayerPrefsUtil.GetInt(str)

	local desc_cfg = WorldTreasureWGData.Instance:GetClientDesc(index)
	if  day ~= open_day and not IsEmptyTable(desc_cfg) and desc_cfg.txt ~= "" then
		self.node_list.img_txt:CustomSetActive(true)
		self.node_list.text_txt.text.text = desc_cfg.txt
	else
		self.node_list.img_txt:CustomSetActive(false)
	end

	-- 背景
	local bundle, asset = ResPath.GetRawImagesPNG(bg_name)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	-- 特效
	for k, v in pairs(COMMON_EFFECTS) do
		self.node_list[v]:SetActive(v == TAB_EFFECT[index])
	end
end

function WorldTreasureView:OnClickBtnCloseImg()
	self.node_list.img_txt:CustomSetActive(false)

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local str = "world_treasure_desc"..main_role_id..self.show_index
	PlayerPrefsUtil.SetInt(str, open_day)
	
end

function WorldTreasureView:OnFlush(param_t, index)
	-- if self.world_treasure_grade_change then
    --     self.world_treasure_grade_change = false
    --     self:LoadBigBg()
    -- end

	if index == TabIndex.tcdb_first_page then
		self:FirstPageOnFlush(param_t)
	elseif index == TabIndex.tcdb_login_gift then
		self:OnFlush_Login(param_t, index)
	elseif index == TabIndex.tcdb_first_recharge then
		self:OnFlush_FirstRecharge(param_t, index)
	elseif index == TabIndex.tcdb_raise_star_gift then
		self:OnFlush_RaiseStarGift(param_t, index)
	elseif index == TabIndex.tcdb_jigsaw then
		self:OnFlush_PinTu(param_t, index)
	elseif index == TabIndex.tcdb_flash_sale1 then
		self:OnFlush_LimitBuy(param_t, index)
	elseif index == TabIndex.tcdb_flash_sale2 then
		self:OnFlush_Shop(param_t, index)
	elseif index == TabIndex.tcdb_total_recharge then
		self:OnFlush_TotalRecharge(param_t, index)
	elseif index == TabIndex.tcdb_jianglin then
		self:FlushJiangLinView(param_t, index)
	elseif index == TabIndex.tcdb_mowang then
		self:FlushMWView(param_t, index)
	elseif index == TabIndex.tcdb_together then
		self:TogetherOnFlush(param_t)
	elseif index == TabIndex.tcdb_premium_shop then
		self:PremiumStoreOnFlush(param_t)
	elseif index == TabIndex.tcdb_scheme then
		self:SchemeOnFlush(param_t)
	elseif index == TabIndex.tcdb_flowing then
		self:FlowingOnFlush(param_t)
	elseif index == TabIndex.tcdb_pursuit then
		self:PursuitOnFlush(param_t)
	end

	-- self.list_data = self:GetListData()

	--[[
	弧形列表
	self.cambered_list:CreateCellList(#self.list_data)
	local btn_item_list = self.cambered_list:GetRenderList()
	local item_data = nil
	local init_index = 0
	for k, item_cell in ipairs(btn_item_list) do
		item_data = self.list_data[k]
		item_cell:SetIndex(k)
		item_cell:SetData(item_data)

		local red_num = WorldTreasureWGData.Instance:GetRedShow(item_data)
		if init_index == 0 and red_num > 0 then
			init_index = k
		end
	end

	if self.select_index == 0 then
		init_index = init_index == 0 and 1 or init_index
		local item_cell = btn_item_list[init_index]
		self:OnClickEsotericaSlotBtn(item_cell, true)
	end
	]]

	-- 列表
	-- local init_index = 0
	-- for i, value in ipairs(self.list_data) do
	-- 	local red_num = WorldTreasureWGData.Instance:GetRedShow(value)
	-- 	if init_index == 0 and red_num > 0 then
	-- 		init_index = i
	-- 	end
	-- end
    -- if self.tab_bar_list_view then
    --     self.tab_bar_list_view:SetDataList(self.list_data)
	-- 	if self.select_index == 0 then
	-- 		init_index = init_index == 0 and 1 or init_index
	-- 		self.tab_bar_list_view:JumpToIndex(init_index)
	-- 	end
    -- end
    -- end

	self:DBTimeCountDown()
	self:FlushTitleName()
end

--有效时间倒计时
function WorldTreasureView:DBTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("world_treasure_count_down")
	local invalid_time = WorldTreasureWGData.Instance:GetCurGradeEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list["txt_cur_grade_end_time"].text.text = string.format(Language.WorldTreasure.LimitRefreshTime, TimeUtil.FormatSecondDHM9(invalid_time - server_time))
		CountDownManager.Instance:AddCountDown("world_treasure_count_down", BindTool.Bind1(self.UpdateGradeCountDown, self), BindTool.Bind1(self.DBTimeCountDown, self), invalid_time, nil, 1)
	end
end

function WorldTreasureView:UpdateGradeCountDown(elapse_time, total_time)
	self.node_list["txt_cur_grade_end_time"].text.text = string.format(Language.WorldTreasure.LimitRefreshTime, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
end

function WorldTreasureView:FlushTitleName()
	local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	if not IsEmptyTable(grade_cfg) then
		self.node_list["title_view_name"].text.text = grade_cfg.entrance_title
	end
end

---	设置Image
---@param image_node string 节点名
---@param res_name string 图片名
function WorldTreasureView:SetImageRes(image_node, res_name)
	local bundle, asset = ResPath.GetNoPackPNG(res_name)
	self.node_list[image_node].image:LoadSprite(bundle, asset, function()
		self.node_list[image_node].image:SetNativeSize()
	end)
end

function WorldTreasureView:FlushEsotericaView()
	if self.select_index * 10 ~= self.show_index then
		self:Open(self.select_index * 10)
	end
end

--[[ 旧版弧形列表
function WorldTreasureView:InitCamberedList()
	local cambered_list_data = {
		item_render = WorldTreasureListRender,
		asset_bundle = "uis/view/world_treasure_ui_prefab",
		asset_name = "node_list_item_render",

		scroll_list = self.node_list.node_list_part,
		center_x = 230, center_y = 30,
		radius_x = 300, radius_y = 300,
		angle_delta = Mathf.PI / 7.5,
		origin_rotation = Mathf.PI * 0.25,
		is_drag_horizontal = false,
		speed = 1,
		arg_speed = 0.2,
        is_clockwise_list = false,

		click_item_cb = BindTool.Bind(self.OnClickEsotericaSlotBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragEsotericaSlotToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragEsotericaSlotToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragEsotericaSlotEndCallBack, self),
	}

	self.cambered_list = CamberedList.New(cambered_list_data)
end
]]

function WorldTreasureView:OnClickEsotericaSlotBtn(item_cell)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if self.select_index == select_index then
		return
	end

	self.select_index = select_index
	self:FlushEsotericaView()

	--[[ 旧
	local btn_item_list = self.cambered_list:GetRenderList()
	for k, cell in ipairs(btn_item_list) do
		cell:SetSelectedHL(select_index)
	end
	]]
end

--[[ 旧
-- 拖拽到下一个
function WorldTreasureView:OnDragEsotericaSlotToNextCallBack()
end

function WorldTreasureView:OnDragEsotericaSlotToLastCallBack()
end

function WorldTreasureView:OnDragEsotericaSlotEndCallBack()
	self:OnDragEsotericaSlotSelectedBtnChange(nil, nil, 1)
end

function WorldTreasureView:OnDragEsotericaSlotSelectedBtnChange(callback, is_click, drag_index)
	if self.cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.select_index or 1
	self.cambered_list:ScrollToIndex(to_index, callback, is_click)

	if callback then
		callback()
	end
end
]]

WorldTreasureListRender = WorldTreasureListRender or BaseClass(BaseRender)

function WorldTreasureListRender:LoadCallBack()
	-- XUI.AddClickEventListener(self.node_list.btn_normal, BindTool.Bind(self.OnNormalClick, self))
	-- XUI.AddClickEventListener(self.node_list.btn_select, BindTool.Bind(self.OnSelectClick, self))
end

function WorldTreasureListRender:OnFlush(param, index)
	local red_num = WorldTreasureWGData.Instance:GetRedShow(self.data)
	self.node_list.RedPoint:SetActive(red_num > 0)

	local name = Language.WorldTreasure[self.data] or ""
	self.node_list.text_normal.text.text = name
    self.node_list.text_select.text.text = name

	local bundle, asset = ResPath.GetWorldTreasureImg(TAB_ICON_RES[self.data])
    self.node_list.img_icon_normal.image:LoadSprite(bundle, asset)
	self.node_list.img_icon_select.image:LoadSprite(bundle, asset)
end

--[[ 旧
function WorldTreasureListRender:SetSelectedHL(index)
	self.node_list.btn_select:SetActive(index == self.index)
end
]]

function WorldTreasureListRender:OnSelectChange(is_select)
	self.node_list.btn_select:SetActive(is_select)
end

function WorldTreasureListRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function WorldTreasureListRender:OnNormalClick()
	if self.click_callback then
		self.click_callback()
	end
end

function WorldTreasureListRender:OnSelectClick()
	--if self.click_callback then
		--self.click_callback()
	--end
end