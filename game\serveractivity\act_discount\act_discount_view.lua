ActDisCountView = ActDisCountView or BaseClass(SafeBaseView)

function ActDisCountView:__init()
	self:SetMaskBg()
	
	self:AddViewResource(0, "uis/view/act_subview_ui_prefab", "layout_act_discount")
end

function ActDisCountView:__delete()
	
end

function ActDisCountView:ReleaseCallBack()
	if self.bar_list then
		self.bar_list:DeleteMe()
		self.bar_list = nil
	end

	if CountDownManager.Instance:HasCountDown("act_discount_count") then
		CountDownManager.Instance:RemoveCountDown("act_discount_count")
	end

	if self.content_list then
		self.content_list:DeleteMe()
		self.content_list = nil
	end

	if self.close_timer_quest then
		GlobalTimerQuest:CancelQuest(self.close_timer_quest)
		self.close_timer_quest = nil
	end

	self.model_cache = nil
	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	self.select_bar_phase_cache = nil
	self.first_start = nil
end

function ActDisCountView:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendDiscountBuyGetInfo()
end

function ActDisCountView:LoadCallBack()
	if not self.bar_list then
		self.bar_list = AsyncListView.New(ActDisCountBar, self.node_list["left_bar_list"])
		self.bar_list:SetSelectCallBack(BindTool.Bind(self.OnClickBar, self))
	end

	if not self.content_list then
		self.content_list = AsyncListView.New(ActDiscountContent, self.node_list["right_list"])
	end

	self.model_display = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}

	self.model_display:SetRenderTexUI3DModel(display_data)
end

function ActDisCountView:ShowIndexCallBack()
	self.first_start = true
end

function ActDisCountView:FlushSelectIndex()
	if self.first_start then
		self.first_start = nil
		local index,length = ActDiscountData.Instance:GetPhaseIndex()
		local per = 0
		if length ~= nil then
			if length - index < 8 then
				per = 1
			elseif index > 7 then
				per = index/length
			end
		end
		self.node_list["left_bar_list"].scroller:ReloadData(per)
		self:SelectBarCell(index,10)
	end
end

function ActDisCountView:SelectBarCell(index,count)
	if count < 1 then return end
	count = count - 1
	if self.bar_list:GetItemAt(index) then
		if self.close_timer_quest then
			GlobalTimerQuest:CancelQuest(self.close_timer_quest)
			self.close_timer_quest = nil
		end
		self.bar_list:SelectIndex(index)
	else
		if self.close_timer_quest then
			GlobalTimerQuest:CancelQuest(self.close_timer_quest)
			self.close_timer_quest = nil
		end
		self.close_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SelectBarCell,self,index,count),0.04)
	end
end

function ActDisCountView:OnFlush()
	self:FlushLeftList()
	self:FlushRightContent()
	self:FlushModel()
end

function ActDisCountView:FlushLeftList(is_complete)
	local left_bar = ActDiscountData.Instance:GetLeftBarList()
	if #left_bar == 0 then
		self:Close()
		return
	end
	if is_complete then
		self.bar_list:SetDataList(left_bar,3)
	else
		self.bar_list:SetDataList(left_bar)
	end
	self:FlushSelectIndex()
end

function ActDisCountView:FlushRightContent()
	if not self.select_bar then return end
	local content = ActDiscountData.Instance:GetRightContentList(self.select_bar.phase)
	if self.select_bar_phase_cache ~= self.select_bar.phase then
		self.content_list:SetDataList(content,0)
		self.select_bar_phase_cache = self.select_bar.phase
	else
		self.content_list:SetDataList(content,3)
	end
end

function ActDisCountView:FlushModel()
	if not self.select_bar then return end
	if self.model_cache ~= self.select_bar.model_show then
		self.model_cache = self.select_bar.model_show
		local res = Split(self.select_bar.model_show,",")
		local bundle,asset = res[1],res[2]
		self.model_display:SetMainAsset(bundle,asset)
		local v3p = ActDiscountData.Instance:GetVector3(self.select_bar.position)
		local v3r = ActDiscountData.Instance:GetVector3(self.select_bar.rotation)
		self.model_display:CustomDisplayPositionAndRotation(v3p,Quaternion.Euler(v3r.x,v3r.y,v3r.z))
	end
end

function ActDisCountView:FlushCountDown()
	if not self.select_bar then return end
	local info = ActDiscountData.Instance:GetRightContentInfo(self.select_bar.phase)
	local time = info.close_timestamp-TimeWGCtrl.Instance:GetServerTime()
	if time < 0 then
		if CountDownManager.Instance:HasCountDown("act_discount_count") then
			CountDownManager.Instance:RemoveCountDown("act_discount_count")
		end
		self:CompleteCountDown()
		return
	end
	local dhms = TimeUtil.Format2TableDHMS(time)
	local str
	if dhms.day > 0 then
		str = string.format(Language.Common.TimeStr4,dhms.day,dhms.hour)
	elseif dhms.hour > 0 then
		str = string.format(Language.Common.TimeStr1,dhms.hour,dhms.min)
	else
		str = string.format(Language.Common.TimeStr5,dhms.min,dhms.s)
	end
	self.node_list["act_time"].text.text = Language.Common.ActTime .. ToColorStr(str,COLOR3B.GREEN)
end

function ActDisCountView:CompleteCountDown()
	self:FlushLeftList(true)
end

function ActDisCountView:OnClickBar(cell)
	if not cell then return end
	self.select_bar = cell.data
	self:FlushModel()
	self:FlushRightContent()
	self:FlushCountDown()
	if CountDownManager.Instance:HasCountDown("act_discount_count") then
		CountDownManager.Instance:RemoveCountDown("act_discount_count")
	end
	local info = ActDiscountData.Instance:GetRightContentInfo(self.select_bar.phase)
	local time = info.close_timestamp-TimeWGCtrl.Instance:GetServerTime()
	CountDownManager.Instance:AddCountDown("act_discount_count", BindTool.Bind(self.FlushCountDown, self),
		BindTool.Bind(self.CompleteCountDown,self),nil,time,0.4)
	-- local info = ActDiscountData.Instance:GetRightContentInfo(self.select_bar.phase)
	-- local time = info.close_timestamp-TimeWGCtrl.Instance:GetServerTime()
	-- local str = TimeUtil.FormatSecondDHM(time)
	-- self.node_list["act_time"].text.text = Language.Common.ActTime .. ToColorStr(str,COLOR3B.GREEN)
end

--------------------------------------------------------------------------------
ActDisCountBar = ActDisCountBar or BaseClass(BaseRender)
function ActDisCountBar:__init()

end

function ActDisCountBar:__delete()
	
end

function ActDisCountBar:OnFlush()
	if not self.data then
		return
	end
	local bundle,asset = ResPath.GetActDiscountIcon(self.data.tab_act_icon,false)
	self.node_list["icon_bg"].image:LoadSprite(bundle,asset,function()
		self.node_list["icon_bg"].image:SetNativeSize()
	end)
	bundle,asset = ResPath.GetActDiscountIcon(self.data.tab_act_icon,true)
	self.node_list["icon_hl"].image:LoadSprite(bundle,asset,function()
		self.node_list["icon_hl"].image:SetNativeSize()
	end)
	self.node_list["Text_bg"].text.text = self.data.phase_desc
	self.node_list["Text_hl"].text.text = self.data.phase_desc
	self.node_list["red"]:SetActive(ActDiscountData.Instance:ShowPhaseRed(self.data.phase))
end

function ActDisCountBar:OnSelectChange( is_select )
	self.node_list["hl"]:SetActive(is_select)
end

--------------------------------------------------------------------------------
ActDiscountContent = ActDiscountContent or BaseClass(BaseRender)
function ActDiscountContent:__init()

end

function ActDiscountContent:__delete()
	if self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end
end

function ActDiscountContent:LoadCallBack()
	self.node_list["Button"].button:AddClickListener(BindTool.Bind(self.OnClickBuy, self))
end

function ActDiscountContent:OnFlush()
	if not self.data then
		return
	end
	if not self.reward_cell then
		self.reward_cell = ItemCell.New(self.node_list.cell)
	end
	self.reward_cell:SetData(self.data.reward_item)
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
	if cfg == nil then
		print_error("cfg 为空，item_id =", self.data.reward_item.item_id)
		return
	end
	local state = self.data.price == 0
	self.node_list["free_flag"]:SetActive(state)
	self.node_list["name"].text.text = cfg.name
	self.node_list["old_price"].text.text = self.data.show_price
	self.node_list["now_price"].text.text = state and Language.Common.Free or self.data.price
	self.node_list["red"]:SetActive(state)
	local can_buy = ActDiscountData.Instance:GetCanBuyIt(self.data.phase, self.data.item_seq)
	XUI.SetButtonEnabled(self.node_list["Button"], can_buy)
	if can_buy == 1 then
		self.node_list["btn_text"].text.text = Language.Common.SellOut
	else
		self.node_list["btn_text"].text.text = state and Language.OpenServer.LingQu or Language.Common.GouMai
	end
end

function ActDiscountContent:OnClickBuy()
	local gold = GameVoManager.Instance:GetMainRoleVo().gold
	if gold >= self.data.price then
		ServerActivityWGCtrl.Instance:DiscountBuyReq(self.data)
	else
		ViewManager.Instance:Open(GuideModuleName.VipTip)
	end
end