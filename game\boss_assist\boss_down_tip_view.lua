--boss体力不足提示
BossDownTipView = BossDownTipView or BaseClass(SafeBaseView)

function BossDownTipView:__init()
	self.active_close = false
	self.view_layer = UiLayer.MainUIHigh
	self.view_name = "BossDownTipView"
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "boss_down_tip_panel")
	self.view_cache_time = 0
	self.open_tween = nil
	self.close_tween = nil
end

function BossDownTipView:ReleaseCallBack()
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end
    if self.delay_hide_tween then
        self.delay_hide_tween:Kill()
        self.delay_hide_tween = nil
    end
end

function BossDownTipView:LoadCallBack()

end

function BossDownTipView:OnFlush()
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end
    if self.delay_hide_tween then
        self.delay_hide_tween:Kill()
        self.delay_hide_tween = nil
    end
    local boss_id = self.cur_boss_id
    local boss_info = BossWGData.Instance:GetBossInfoByBossId(boss_id)
    local kill_reduce_xianli = boss_info and boss_info.kill_reduce_xianli
    if kill_reduce_xianli then
        local total_xianli = BossAssistWGData.Instance:GetBossXianli()
        self.is_enough = kill_reduce_xianli <= total_xianli
    end
    self.node_list.img_not_enough:SetActive(not self.is_enough)
    self.node_list.img_enough:SetActive(self.is_enough)
    self.node_list.parent_canvas.canvas_group.alpha = 1
    self.delay_timer = GlobalTimerQuest:AddDelayTimer(function()
        self.delay_timer = nil
        self.delay_hide_tween = self.node_list.parent_canvas.canvas_group:DoAlpha(1, 0, 1.5)
        self.delay_hide_tween:OnComplete(function()
            self:Close()
        end)
    end, 4.5)
end

function BossDownTipView:SetCurBoss(boss_id)
   self.cur_boss_id = boss_id
end

function BossDownTipView:GetCurBossAndState()
    return self.cur_boss_id, self.is_enough
end

function BossDownTipView:ClearBossId()
    self.cur_boss_id = nil
end