function RebateActivityView:InitDayView()
    self.box_render_list = {}
    for i = 1, COMMON_CONSTS.DayRebateNum do
        self.box_render_list[i] = BoxRebateRender.New(self.node_list["box"..i])
        self.box_render_list[i]:SetIndex(i)
    end
end

function RebateActivityView:ShowIndexDayView()
    local content = RebateDayActivityWGData.Instance:GetTipsContent()
    if content == nil then
        return
    end

    self:SetRuleInfo(content, Language.RebateDay.Title)
end

function RebateActivityView:FlushDayView()
    local data_list = RebateDayActivityWGData.Instance:GetBoxInfo()
    for k, v in pairs(self.box_render_list) do
        local data = data_list[k] or {}
        v:SetData(data)
    end
    local cur_open_day, gold_num = RebateDayActivityWGData.Instance:GetCommonInfo()
    self.node_list.cur_activity_day.text.text = string.format(Language.RebateDay.CurDayDes, cur_open_day)
    self.node_list.cost_num.text.text = string.format(Language.RebateDay.XianyuDes, gold_num)
end

function RebateActivityView:PlayXianyuEffect()
    local index = RebateDayActivityWGData.Instance:GetLastChangeIndex()
    local node = index and self.node_list["box"..index] or self.node_list.rebate_day_center_pos
	if node and self:GetShowIndex() == TabIndex.rebate_activity_2250 then
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_zhakai)
		local bundle, asset = ResPath.UiseRes("effect_xianyuhuode")
        AudioManager.PlayAndForget(bundle, asset)
        EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, node.transform, 1.6, nil,nil,nil,
        BindTool.Bind(self.PlayEffectXianyuSecond, self, node))
	end
end

function RebateActivityView:PlayEffectXianyuSecond(node)
    TipWGCtrl.Instance:DestroyFlyEffectByViewName("PlayEffectRebateDayView")
	if self.money_bar then
		local end_obj = self.money_bar:GetSlicketNode(GameEnum.NEW_MONEY_BAR.XIANYU)
        if end_obj and node then	
            local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_new)						
            TipWGCtrl.Instance:ShowFlyEffectManager("PlayEffectRebateDayView", bundle_name,
            asset_name, node, end_obj, DG.Tweening.Ease.OutCubic, 0.5,
            function ()
                local data = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.VIRTUAL_ITEM_GOLD)
                local gold = RebateDayActivityWGData.Instance:GetGoldNum()
                    if self.money_bar then
                        self.money_bar:Flush()
                    end
                    if gold > 0 then
                        local str = string.format(Language.Bag.GetItemTxt, data.name, gold)
                        SysMsgWGCtrl.Instance:ErrorRemind(str)
                    end
            end, nil, 20, 200)
		end
	end
end

function RebateActivityView:ReleaseDayView()
    if self.box_render_list then
        for k, v in pairs(self.box_render_list) do
            v:DeleteMe()
        end
        self.box_render_list = nil
    end
end

BoxRebateRender = BoxRebateRender or BaseClass(BaseRender)
function BoxRebateRender:__init()

end

function BoxRebateRender:__delete()

end

function BoxRebateRender:LoadCallBack()
    self.node_list.icon.button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
end

function BoxRebateRender:OnClickGet()
    if self.data and self.data.is_get == 1 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RebateDay.RemindDes5)
        return
    end
    if self.data and self.data.can_get == 1 then
        ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FANLIBAOXIAN, RebateDayActivityWGData.OPERA_TYPE.GET_REWARD, self.index, 0, 0) 
    else
        local cur_day = RebateDayActivityWGData.Instance:GetCommonInfo()
        if cur_day < self.index then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.RebateDay.RemindDes1, self.index))
        elseif cur_day == self.index then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.RebateDay.RemindDes2, self.index + COMMON_CONSTS.DayRebateNum))
        else
            if cur_day < self.index + COMMON_CONSTS.DayRebateNum then
                if self.data and self.data.gold_num > 0 then
                    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.RebateDay.RemindDes3, self.index + COMMON_CONSTS.DayRebateNum))
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.RebateDay.RemindDes4)
                end
            else
                if self.data and self.data.gold_num == 0 then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.RebateDay.RemindDes4)
                end
            end
        end
    end
end

local img_state = {
    [0] = "flbx_baoxing",
    [1] = "flbx_full",
    [2] = "flbx_open",
}

function BoxRebateRender:OnFlush()
    if not self.data then
        return
    end

    local cur_day = RebateDayActivityWGData.Instance:GetCommonInfo()
    self.node_list.day_txt.text.text = string.format(Language.RebateDay.DayCount, NumberToChinaNumber(self.index))
    self.node_list.canget_effect:SetActive(false)
    self.node_list.effect_full:SetActive(false)
    self.node_list.effect_circle:SetActive(false)
    self.node_list.icon.image:LoadSprite(ResPath.GetRebateActImage(img_state[0]))
    if self.data.gold_num and self.data.gold_num > 0 then --有存储
        self.node_list.wait_storage.text.text = ""
        if self.data.is_get == 0 then
            if self.data.can_get == 1 then --可领取
                self.node_list.canget_effect:SetActive(true)
                self.node_list.icon.image:LoadSprite(ResPath.GetRebateActImage(img_state[1]))
                self.node_list.state_txt.text.text = string.format(Language.RebateDay.CanGetDes, self.data.gold_num)
            else --
                self.node_list.effect_full:SetActive(true)
                self.node_list.state_txt.text.text = string.format(Language.RebateDay.WaitGetDes, self.data.gold_num)
            end
        else --已领取
            self.node_list.icon.image:LoadSprite(ResPath.GetRebateActImage(img_state[2]))
            self.node_list.state_txt.text.text = Language.RebateDay.HadGet
        end
    else
        if cur_day <= self.index then --待存
            self.node_list.state_txt.text.text = ""
            self.node_list.wait_storage.text.text = Language.RebateDay.WaitStorage
        else --无存储
            self.node_list.state_txt.text.text = ""
            self.node_list.wait_storage.text.text = Language.RebateDay.NoStorage
        end
    end
    if cur_day > COMMON_CONSTS.DayRebateNum then
        self.node_list.bg_hight:SetActive(self.index == (cur_day - COMMON_CONSTS.DayRebateNum))
    else
        self.node_list.effect_circle:SetActive(self.index == cur_day)
        self.node_list.bg_hight:SetActive(self.index == cur_day)
    end

end