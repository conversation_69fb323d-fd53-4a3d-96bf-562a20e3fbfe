--动态运营活动 每日首充
function OperationActivityView:InitDayShouChongView()
	self.shouchong_reward_list = nil
end

function OperationActivityView:LoadIndexCallBackDayShouChongView()
	-- self:DTYYOnFlushShouChongView()
	self:DTYYLoadShouChongView()
	XUI.AddClickEventListener(self.node_list.first_btn_receive, BindTool.Bind(self.OnShouChongBtnReceiveClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.first_btn_recharge, BindTool.Bind(self.OnShouChongBtnRechargeClickHnadler,self))
end

function OperationActivityView:OpenIndexCallBackShouChongView()
	OperationFirstRechargeWGCtrl.Instance:SendDayShouChongReq(OPERATION_ACTIVITY_SHOUCHONG_TYPE.TYPE_INFO)
end

function OperationActivityView:DTYYReleaseShouChongView()
	if self.shouchong_reward_list then
		self.shouchong_reward_list:DeleteMe()
		self.shouchong_reward_list = nil
	end

	if self.box_tween then
		self.box_tween:Kill()
		self.box_tween = nil
	end

	if self.sc_cell_list then
		for k,v in pairs(self.sc_cell_list) do
			v:DeleteMe()
		end
		self.sc_cell_list = nil
	end
end

function OperationActivityView:DTYYLoadShouChongView()
	-- local view_cfg = OperationFirstRechargeWGData.Instance:GetShouChongViewCfgByInterface()
	-- if view_cfg == nil then
	-- 	return
	-- end
	
	local xiangzi_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_5")
	local bundle, asset = ResPath.GetRawImagesPNG(xiangzi_name)
	self.node_list["box_bg"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["box_bg"].raw_image:SetNativeSize()
    end)

	local recharge_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_4")
	local bundle2, asset2 = ResPath.GetOperationActivityImagePath(recharge_name)
	self.node_list["first_reward_bg"].image:LoadSprite(bundle2, asset2, function()
        self.node_list["first_reward_bg"].image:SetNativeSize()
    end)

	local recharge_title_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_txt_3")
	local bundle3, asset3 = ResPath.GetRawImagesPNG(recharge_title_name)
	self.node_list["rechage_title_img"].raw_image:LoadSprite(bundle3, asset3, function()
        self.node_list["rechage_title_img"].raw_image:SetNativeSize()
    end)

	local title_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_txt_2")
	local bundle4, asset4 = ResPath.GetRawImagesPNG(title_name)
	self.node_list["title_txt"].raw_image:LoadSprite(bundle4, asset4, function()
        self.node_list["title_txt"].raw_image:SetNativeSize()
    end)

	-- local title_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a2_zx_ggdt")
	-- local bundle5, asset5 = ResPath.GetRawImagesPNG(title_bg_name)
	-- self.node_list["title_box"].raw_image:LoadSprite(bundle5, asset5, function()
    --     self.node_list["title_box"].raw_image:SetNativeSize()
    -- end)

	-- if self.box_tween == nil then
	-- 	local tween_root = self.node_list["dayfirst_box"].rect
	-- 	self.box_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 25, 1)
	-- 	self.box_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	-- end

end

function OperationActivityView:DTYYOnFlushShouChongView()
	local view_cfg = OperationFirstRechargeWGData.Instance:GetShouChongViewCfgByInterface()
	if view_cfg == nil then
		return
	end
	local is_active_last_day = OperationActivityWGData.Instance:IsActivityLastDay(TabIndex.operation_act_first_recharge)
	local gift_state = OperationFirstRechargeWGData.Instance:GetFirstRechargeState()
	--local btn_desc
	-- self.node_list.first_btn_redpoint:SetActive(false)
	-- self.node_list.first_btn_receive:SetActive(true)
	--self.node_list.btn_effect:SetActive(false)
	--可领取
	-- if gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.KLQ then
	-- 	--不置灰
	-- 	self.node_list.first_btn_receive:SetActive(true)
	-- 	self.node_list.first_btn_recharge:SetActive(false)
	-- 	--XUI.SetGraphicGrey(self.node_list.first_btn_receive, false)
	-- 	self.node_list.first_btn_redpoint:SetActive(true)
	-- --	self.node_list.btn_effect:SetActive(true)
	-- 	--btn_desc = view_cfg.btn_1_2
	-- --已领取状态
	-- elseif gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.YLQ then
	-- 	--最后一天显示已领取（领取按钮隐藏）
	-- 	if is_active_last_day then
	-- 		self.node_list.first_btn_receive:SetActive(false)
	-- 		--btn_desc = view_cfg.btn_1_4
	-- 	else
	-- 	--明日可领取（领取按钮置灰）
	-- 		--btn_desc = view_cfg.btn_1_3
	-- 		XUI.SetGraphicGrey(self.node_list.first_btn_receive, true)
	-- 	end
	-- --不可领取状态显示特效	
	-- else
	-- --	self.node_list.btn_effect:SetActive(true)
	-- 	XUI.SetGraphicGrey(self.node_list.first_btn_receive, false)
	-- 	--btn_desc = view_cfg.btn_1_1
	-- end
	self:FlushRulerTipAndTipsInfo()
	self.node_list.first_btn_receive:SetActive(gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.KLQ)
	self.node_list.first_btn_redpoint:SetActive(gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.KLQ)
	self.node_list.first_btn_recharge:SetActive(gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.WDC)
	self.node_list.first_btn_ylq:SetActive(gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.YLQ)
	--self.node_list.first_btn_label.text.text = btn_desc
	self:DTYYInitShouChongList()
	
end

--奖励物品格子
function OperationActivityView:DTYYInitShouChongList()
	local open_day = OperationFirstRechargeWGData.Instance:GetFirstDayRechargeOpenDay()
	local cfg = OperationFirstRechargeWGData.Instance:GetShouChongRewardCfgByDay(open_day)
	if cfg ~= nil then
		local chaozhi_state = string.split(cfg.chaozhi, ":")
		local list = {}

		for i=0,#cfg.reward_item do
			if chaozhi_state[i+1] ~= nil then
				cfg.reward_item[i].chaozhi_state = tonumber(chaozhi_state[i+1])
			else
				cfg.reward_item[i].chaozhi_state = 0
			end
			table.insert(list,cfg.reward_item[i])
		end

		local len = #list
		list = OperationActivityWGData.Instance:SortDataByItemColor(list)
		--小于6就都按顺序排列显示，大于6就用list
		-- if len <= 6 then
		-- 	if self.sc_cell_list == nil then
		-- 		self.sc_cell_list = {}
		-- 	end

		-- 	for i=1,len do
		-- 		if not self.sc_cell_list[i] then
		-- 			self.sc_cell_list[i] = DTYYRechargeShouChongRender.New(self.node_list["first_item"..i].gameObject)
		-- 		end
		-- 		self.sc_cell_list[i]:SetItemData(list[i])
		-- 		self.sc_cell_list[i]:SetActive(true)
		-- 	end
		-- else
		if not self.shouchong_reward_list then
			self.shouchong_reward_list = AsyncListView.New(DTYYRechargeShouChongRender, self.node_list["first_reward_list"])
		end
		self.shouchong_reward_list:SetDataList(list)
		--end
	end
end

--点击领奖按钮
function OperationActivityView:OnShouChongBtnReceiveClickHnadler()
    local gift_state = OperationFirstRechargeWGData.Instance:GetFirstRechargeState()
    if gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.KLQ then
        local empty_num = ItemWGData.Instance:GetEmptyNum()
        local stuff_empty_num = ItemWGData.Instance:GetStuffBagEmptyNum()
        if empty_num == 0 or stuff_empty_num == 0 then
            RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)  --背包已满
            return
        end
		--可领取发领取协议（活动号，领取）
		OperationFirstRechargeWGCtrl.Instance:SendDayShouChongReq(OPERATION_ACTIVITY_SHOUCHONG_TYPE.TYPE_DRAW, 0)
	elseif gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.YLQ then
		--已领取飘窗
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenRoad.ReChargeStr3)
	end
end

--点击前往充值按钮
function OperationActivityView:OnShouChongBtnRechargeClickHnadler()
	--打开充值界面
	FunOpen.Instance:OpenViewByName(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_rapidly)
end

--RuleTip 和 tips
function OperationActivityView:FlushRulerTipAndTipsInfo()
	local view_cfg = OperationFirstRechargeWGData.Instance:GetShouChongViewCfgByInterface()
	if view_cfg == nil then
		return
	end
	local title = OperationActivityWGData.Instance:GetActivityNameByActivityNum(ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE)
	local desc = view_cfg.rule_2
	local tips = view_cfg.rule_1
	self.node_list.dayfirst_title_desc.text.text = desc
	self:SetRuleInfo(desc, title)
	self:SetOutsideRuleTips(tips)
end

-------------------------------------DTYYRechargeShouChongRender----------------------------
DTYYRechargeShouChongRender = DTYYRechargeShouChongRender or BaseClass(BaseRender)
function DTYYRechargeShouChongRender:__init()
	
end

function DTYYRechargeShouChongRender:LoadCallBack()

end

function DTYYRechargeShouChongRender:ReleaseCallBack()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function DTYYRechargeShouChongRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function DTYYRechargeShouChongRender:OnFlush()
	if not self.data then
		return
	end 

	self.node_list.chaozhi:SetActive(self.data.chaozhi_state > 0)
	if not self.cell then
		self.cell = ItemCell.New(self.node_list["cell"])
	end

	local gift_state = OperationFirstRechargeWGData.Instance:GetFirstRechargeState()
	self.cell:SetData(self.data)
	self.cell:SetRedPointEff(gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.KLQ)
	self.cell:SetLingQuVisible(gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.YLQ)
end