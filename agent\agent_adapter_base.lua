AgentAdapterBase = AgentAdapterBase or BaseClass()

function AgentAdapterBase:__init()
	-- Create the channel user info data.
	self.user_info = ChannelUserInfo.New()

	-- Bind the channel agent.
	self.event_init = BindTool.Bind(self.OnInit, self)
	ChannelAgent.InitializedEvent = ChannelAgent.InitializedEvent + self.event_init

	self.event_login = BindTool.Bind(self.OnLogin, self)
	ChannelAgent.LoginEvent = ChannelAgent.LoginEvent + self.event_login

	self.event_logout = BindTool.Bind(self.OnLogout, self)
	ChannelAgent.LogoutEvent = ChannelAgent.LogoutEvent + self.event_logout

	self.event_exit = BindTool.Bind(self.OnExit, self)
	ChannelAgent.ExitEvent = ChannelAgent.ExitEvent + self.event_exit

	self.event_reserve = BindTool.Bind(self.OnReserveEvent, self)
	ChannelAgent.ReserveEvent = ChannelAgent.ReserveEvent + self.event_reserve

	-- Bind the game event for report.
	self.event_handler_list = {}
	table.insert(self.event_handler_list, GlobalEventSystem:Bind(
											LoginEventType.CREATE_ROLE,
											BindTool.Bind(self.ReportCreateRole, self)))

	table.insert(self.event_handler_list, GlobalEventSystem:Bind(
											LoginEventType.GAME_SERVER_CONNECTED,
											BindTool.Bind(self.ReportEnterZone, self)))

	table.insert(self.event_handler_list, GlobalEventSystem:Bind(
												OtherEventType.ROLE_LEVEL_UP,
												BindTool.Bind(self.ReportLevelUp, self)))

	table.insert(self.event_handler_list, GlobalEventSystem:Bind(
												LoginEventType.RECV_MAIN_ROLE_INFO,
												BindTool.Bind(self.ReportLoginRole, self)))

	table.insert(self.event_handler_list, GlobalEventSystem:Bind(
												LoginEventType.LOGOUT,
												BindTool.Bind(self.Logout, self)))

	-- Set data
	self.login_user = {}

	-- Initialize the channel.
	print_log("ChannelAgent.Initialize.")
	ChannelAgent.Initialize()
end

function AgentAdapterBase:__delete()
	for _, v in pairs(self.event_handler_list) do
		GlobalEventSystem:UnBind(v)
	end
	self.event_handler_list = {}

	if nil ~= self.event_init then
		ChannelAgent.InitializedEvent = ChannelAgent.InitializedEvent - self.event_init
		self.event_init = nil
	end

	if nil ~= self.event_login then
		ChannelAgent.LoginEvent = ChannelAgent.LoginEvent - self.event_login
		self.event_login = nil
	end

	if nil ~= self.event_logout then
		ChannelAgent.LogoutEvent = ChannelAgent.LogoutEvent - self.event_logout
		self.event_logout = nil
	end

	if nil ~= self.event_exit then
		ChannelAgent.ExitEvent = ChannelAgent.ExitEvent - self.event_exit
		self.event_exit = nil
	end

	if nil ~= self.event_reserve then
		ChannelAgent.ReserveEvent = ChannelAgent.ReserveEvent - self.event_reserve
		self.event_reserve = nil
	end

end

function AgentAdapterBase:ShowLogin(callback)
	self.login_callback = callback
	if nil ~= self.http_login_call_back  then
		HttpClient:CancelRequest(self.http_login_call_back)
		self.http_login_call_back = nil
	end
	local user_info = self:GetUserInfo()
	print_log("ChannelAgent.Login: ", user_info)
	ChannelAgent.Login(user_info)
end

function AgentAdapterBase:Logout()
	local user_info = self:GetUserInfo()
	print_log("ChannelAgent.Logout: ", user_info)
	ChannelAgent.Logout(user_info)
end

--[[
	product_id:产品ID
	discounted_price:折扣后的商品金额
	desc:商品描述
	recharge_type:商品类型
	gold_flag:商品索引
	discount_ticket_index：服务器的券包列表索引
	custom_param:自定义参数（目前是买一得三）
]]
function AgentAdapterBase:Pay(product_id, discounted_price, desc, recharge_type, gold_flag, discount_ticket_index, custom_param)
	local extension = (custom_param and custom_param > 0) and custom_param or nil
	local orginal_price = discounted_price

	-- 次数满减额度
	local count_discount_threshold = 0
	if recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TB_DEZG_RMB_BUY then
		local reduce_value = BillionSubsidyWGData.Instance:GetDEZGShopCurReduceValue()
		count_discount_threshold = reduce_value
	end

	local coupon
	if discount_ticket_index and discount_ticket_index >= 0 then
		local discount_ticket_data = BillionSubsidyWGData.Instance:GetSingleDiscountTicketDataBySeq(discount_ticket_index)
		if discount_ticket_data then
			orginal_price = discounted_price + discount_ticket_data.reduce_quota + count_discount_threshold
			-- 优惠券信息
			coupon = {
				id = discount_ticket_index,
				type = 1,
				threshold = discount_ticket_data.quota_limit * COMMON_CONSTS.CENT_100,
				discount = discount_ticket_data.reduce_quota * COMMON_CONSTS.CENT_100,
			}
		end
	end

	local cent_discounted_price = discounted_price * COMMON_CONSTS.CENT_100
	local cent_orginal_price = orginal_price * COMMON_CONSTS.CENT_100

	local user_info = self:GetUserInfo()
	user_info.ProductName = desc
	user_info.ProductDesc = desc
	user_info.Ratio = tostring(RECHARGE_BILI)					--兑换比例

	-- 获取订单号
	local user_vo = GameVoManager.Instance:GetUserVo()
	local plat_id = CHANNEL_AGENT_ID
	local order_post_data = {
		access_token = user_vo.access_token,
		plat_id = plat_id,
		money = cent_discounted_price,					-- 优惠后的实付价  金额：分
		server_id = user_info.ZoneID,
		role_id = user_info.RoleID,
		role_name = user_info.RoleName,
		role_level = user_info.RoleLevel,
		product_id = product_id,
		product_price = cent_orginal_price,
		product_name = user_info.ProductName,
		product_desc = user_info.ProductDesc,
		product_type = recharge_type,
		product_flag = gold_flag,
		extension = extension,
		coupon = coupon,
	}

	local order_url = GLOBAL_CONFIG.api_urls.pay.order
	PhpHandle.HandlePhpJsonPostRequest(order_url, order_post_data, order_post_data, function(cbData)
			local order_str = cbData.data.trade_no
			if GLOBAL_CONFIG.param_list.switch_list.gamewp then
				local notify_url = string.format("%s/v1/pay/notify/%s", GlobalUrl, plat_id)
				local notify_post_data = {
					order_id = order_str,
					cp_order_id = order_str,
					money = cbData.data.money,		-- 返回：分
				}
				
				PhpHandle.HandlePhpJsonPostRequest(notify_url, notify_post_data, notify_post_data, function (notifyCBData)
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.BuySucc)
				end, function (notifyCBData)
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.BuyFail)
				end)
			else
				ChannelAgent.Pay(user_info, order_str, product_id, cent_discounted_price)
			end
		end,
		function (cbData)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OrderFail)
		end
	)
end

function AgentAdapterBase:OnInit(result)
end

function AgentAdapterBase:GetLoginVerifyData()
	return self.base64_data or ""
end

function AgentAdapterBase:OnLogin(data)
	local login_url = GLOBAL_CONFIG.api_urls.client.login

	if nil == login_url or "" == login_url then
		return
	end

	if nil ~= self.http_login_call_back then
		HttpClient:CancelPostJsonRequest(self.http_login_call_back)
		self.http_login_call_back = nil
	end

	local loginData = {
		plat_id = CHANNEL_AGENT_ID,
		partner_login_data = data,
	}

	self.http_login_call_back = BindTool.Bind(AgentAdapter.OnVerifyLogin, self)
	PhpHandle.HandlePhpJsonPostRequest(login_url, loginData, loginData, self.http_login_call_back,
		function (cb_data)
			if self.login_callback then
				self.login_callback(false)
			end
		end
	)
end

function AgentAdapterBase:OnVerifyLogin(cb_data)
	local data = cb_data.data
	self.login_user = data
	PlayerPrefsUtil.SetString("login_user", self.login_user.uid)
	local uservo = GameVoManager.Instance:GetUserVo()
	uservo.account_user_id = data.uid
	uservo.login_time = data.login_time
	uservo.access_token = data.access_token
	uservo.is_white = data.is_white
	uservo.plat_session_key = data.sign

	local plat_id = CHANNEL_AGENT_ID
	PlayerPrefsUtil.SetString("cahce_account_user_id", data.uid)
	PlayerPrefsUtil.SetString("account_token_" .. (data.uid or ""), data.access_token)
	PlayerPrefsUtil.SetString("plat_session_key_" .. (data.uid or ""), data.sign)
	PlayerPrefsUtil.SetString("is_white_" .. (data.uid or ""), data.is_white)

	if LoginWGData.Instance then
		LoginWGData.Instance:SetPHPAccountRoleList(data.role_list)
	end

	GameRoot.Instance:SetBuglyUserID(string.format("%s_%s", plat_id, uservo.account_user_id))
	local callback = self.login_callback
	self.login_callback = nil
	if nil ~= callback then
		callback(true)
	end
end

function AgentAdapterBase:OnLogout()
	LoginWGCtrl.Instance:OnLoginOut()

	if Scene.Instance:IsEnterScene() then	--登出检测是否在场景，去掉不影响，不去掉服务器宕机会导致回不了登录
		GameRestart()
	end
end

function AgentAdapterBase:OnExit()
	if TipWGCtrl and TipWGCtrl.Instance and Language then
		-- local yes_func = function() DeviceTool.Quit() end
		-- local describe = Language.Common.QuitGame
		-- TipWGCtrl.Instance:OpenConfirmAlertTips(describe, yes_func)
		TipWGCtrl.Instance:OpenExitGameTip()
	end
end

function AgentAdapterBase:GetUserInfo()
	local user_info = self.user_info

	local user_id = ""
	if nil == self.login_user or nil == self.login_user.uid or "" == tostring(self.login_user.uid) then
		user_id = PlayerPrefsUtil.GetString("login_user")
	else
		user_id = tostring(self.login_user.uid)
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	user_info.ZoneID = user_vo.plat_server_id or 0
	user_info.ZoneName = user_vo.plat_server_name or ""
	user_info.RoleID = user_vo:GetOriginalRoleID() or 0
	user_info.RoleName = main_role_vo.role_name or ""
	user_info.RoleLevel = main_role_vo.level or 0
	user_info.Currency = main_role_vo.coin or 0
	user_info.Diamond = main_role_vo.gold or 0
	user_info.VIP = main_role_vo.vip_level or 0
	user_info.GuildName = main_role_vo.guild_name or ""
	user_info.CreateTime = main_role_vo.create_time or 0
	user_info.UserID = user_id
	user_info.ProductName = Language.Common.ZuanShi
	user_info.ProductDesc = Language.Common.MiaoShu
	user_info.Ratio = tostring(RECHARGE_BILI)
	user_info.RoleCapability = main_role_vo.capability

	return user_info
end

function AgentAdapterBase:ReportEnterZone(is_succ)
	if IS_ON_CROSSSERVER then
		return
	end

	if is_succ then
		local user_info = self:GetUserInfo()
		print_log("[AgentAdapter.ReportEnterZone]is_succ, user_info ", is_succ, user_info)
		ChannelAgent.ReportEnterZone(user_info)
	end
end

function AgentAdapterBase:ReportCreateRole()
	if IS_ON_CROSSSERVER then
		return
	end

	local user_info = self:GetUserInfo()
	print_log("[AgentAdapter.ReportCreateRole]user_info = ", user_info)
	ChannelAgent.ReportCreateRole(user_info)
end

function AgentAdapterBase:ReportLevelUp()
	if IS_ON_CROSSSERVER then
		return
	end

	local user_info = self:GetUserInfo()
	print_log("[AgentAdapter.ReportLevelUp]user_info = ", user_info)
	ChannelAgent.ReportLevelUp(user_info)
end

function AgentAdapterBase:ReportLoginRole()
	if IS_ON_CROSSSERVER then
		return
	end

	local user_info = self:GetUserInfo()
	print_log("[AgentAdapter.ReportLoginRole]user_info = ", user_info)
	ChannelAgent.ReportLoginRole(user_info)
end

function AgentAdapterBase:ReportLogoutRole()
	if IS_ON_CROSSSERVER then
		return
	end

	local user_info = self:GetUserInfo()
	print_log("[AgentAdapter.ReportLogoutRole]user_info = ", user_info)
	ChannelAgent.ReportLogoutRole(user_info)
end

-- 请求用户充值数据
function AgentAdapterBase:QuaryGetUserMoney()
	if UNITY_EDITOR or IS_AUDIT_VERSION then
		return
	end

	-- 渠道id
	local user_vo = GameVoManager.Instance:GetUserVo()
	local spid = CHANNEL_AGENT_ID
	local time = os.time()
	local user_id = user_vo.account_user_id
	local sign = spid .. user_id .. time .. "bd1e3dbcf76412dec9b8a80e05e24757"
	sign = MD52.GetMD5(sign)

	local get_user_money_url = GLOBAL_CONFIG.param_list.get_user_money_url
	if not get_user_money_url or get_user_money_url == '' then
		return 
	end

	local real_url = string.format("%s?spid=%s&user_id=%s&time=%s&sign=%s",
		get_user_money_url,
		spid,
		user_id,
		time,
		sign)

	if real_url ~= nil then
		HttpClient:Request(real_url, function(...)
			self:OnQuaryGetUserMoney(...)
		end)
	end
end

function AgentAdapterBase:OnQuaryGetUserMoney(url, is_succ, data)
	-- SysMsgWGCtrl.Instance:ErrorRemind('OnQuaryGetUserMoney--' .. tostring(is_succ))
	LoginWGData.Instance:SetUserMoney(0)
	if not is_succ then
		return
	end

	local info = cjson.decode(data)
	if info == nil then
		print_log("请求后台充值数据有误, 数据为空", data)
		return
	end

	if info.ret ~= 0 and info.ret == -2 then
		local user_vo = GameVoManager.Instance:GetUserVo()
		local user_id = user_vo.account_user_id
		print_log("sign 验证失败",user_id, url)
	elseif info.ret ~= 0 and info.ret == -5 then
		local user_vo = GameVoManager.Instance:GetUserVo()
		local user_id = user_vo.account_user_id
		print_log("用户id异常",user_id)
	elseif info.ret ~= 0 and info.ret == -3 then
		print_log("渠道异常")
	else
		print_log("请求后台充值数据有误", info.ret)
		return
	end

	info = info.data
	-- SysMsgWGCtrl.Instance:ErrorRemind('OnQuaryGetUserMoney--status:' .. tostring(info.status))

	if not info then
		print_log('OnQuaryGetUserMoney 数据返回错误 url:',url)
		return
	end

	LoginWGData.Instance:SetUserMoney(tonumber(info.pay_money))
end

function AgentAdapterBase:RequestPhoneBindStatus()
	local data = {
		y_type = "y_isBindPhone"
	}
	local json_data = cjson.encode(data)
	print_log("[AgentAdapter.RequestPhoneBindStatus]json_data = ", json_data)
	ChannelAgent.Reserve(json_data)
end

function AgentAdapterBase:ShowPhoneBindView()
	local data = {
		y_type = "y_onBindPhone"
	}
	local json_data = cjson.encode(data)
	print_log("[AgentAdapter.ShowPhoneBindView]json_data = ", json_data)
	ChannelAgent.Reserve(json_data)
end

function AgentAdapterBase:OnReserveEvent(data)
	local reserve_data = cjson.decode(data)
	if not reserve_data then
		return
	end

	local func_type = reserve_data.y_type
	local b_status = reserve_data.y_status

	-- if func_type == "y_isBindPhone" or func_type == "y_onBindPhone" then
	-- 	WelfareWGCtrl.Instance:SetIsPhoneBinded(b_status)
	-- end
	
	if reserve_data.y_type == "y_isBindPhone" then
		GlobalEventSystem:Fire(AuditEvent.IS_BIND_PHONE, reserve_data)
	elseif reserve_data.y_type == "y_onBindPhone" then
		GlobalEventSystem:Fire(AuditEvent.IS_BIND_PHONE_SUCCESS, reserve_data)
	end

end