﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_SkinWeightsWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(UnityEngine.SkinWeights));
		<PERSON><PERSON>("OneBone", get_OneBone, null);
		<PERSON><PERSON>("TwoBones", get_TwoBones, null);
		<PERSON><PERSON>("FourBones", get_FourBones, null);
		<PERSON><PERSON>("Unlimited", get_Unlimited, null);
		<PERSON><PERSON>unction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.SkinWeights>.Check = CheckType;
		StackTraits<UnityEngine.SkinWeights>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.SkinWeights arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.SkinWeights), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OneBone(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.SkinWeights.OneBone);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TwoBones(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.SkinWeights.TwoBones);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FourBones(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.SkinWeights.FourBones);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Unlimited(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.SkinWeights.Unlimited);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.SkinWeights o = (UnityEngine.SkinWeights)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

