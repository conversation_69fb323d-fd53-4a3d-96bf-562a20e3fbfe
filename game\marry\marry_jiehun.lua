local JIE_HUN = 1 	--结婚
local LI_HUN = 2 	--离婚
local UIOverrideOrder = typeof(UIOverrideOrder)
local MOVE_TIME = 0.6

local POS =
{
    [1] = Vector2(-594, 147+384),
    [2] = Vector2(-490, -89+384),
    [3] = Vector2(-310, -165+384),
    [4] = Vector2(298, -112+384),
    [5] = Vector2(555, -31+384),
    [6] = Vector2(422, 185+384),
}
local Tween_time = 1

function MarryView:InitJieHunView()
	self.need_init_jiehun = false
	if RoleWGData.Instance.role_vo.lover_uid > 0 then
		self:NeedInitJieHun()
	end

	XUI.AddClickEventListener(self.node_list["btn_marry_title"], BindTool.Bind(self.OnClickOpenTitle, self))
	XUI.AddClickEventListener(self.node_list["btn_help"], BindTool.Bind1(self.OpenMarryTips, self))
	XUI.AddClickEventListener(self.node_list["btn_qiuhun"], BindTool.Bind(self.OpenSelectLoverView, self, JIE_HUN)) 	--结婚
	XUI.AddClickEventListener(self.node_list["btn_qiuhun_new"], BindTool.Bind(self.OpenSelectLoverView, self, JIE_HUN)) --结婚
	XUI.AddClickEventListener(self.node_list["btn_lihun"], BindTool.Bind(self.OpenSelectLoverView, self, LI_HUN)) 		--离婚
    XUI.AddClickEventListener(self.node_list["btn_open_nearby_single"], BindTool.Bind1(self.OnClickOpenNearbySingle, self))
    XUI.AddClickEventListener(self.node_list["btn_jiehun_head_view"], BindTool.Bind1(self.OnClickHeadView, self))
	XUI.AddClickEventListener(self.node_list["btn_baoxia"], BindTool.Bind(self.OnClickBaoXia, self))

    XUI.AddClickEventListener(self.node_list["baby_img_bg"], BindTool.Bind1(self.OnClickOpenGetBabyView, self))

    self.rember_baby_id = -1 --记录当前对象的仙娃id
    --self.node_list.lbl_goto_get:SetActive(false)
end

function MarryView:JieHunOpenCallBack()
    MarryWGCtrl.Instance:ReqChangeLYLRole(true)
end

function MarryView:JieHunCloseCallBack()
    self.is_first_enter_jiehun = false

    if self.open_zhiyin_tween then
        self.open_zhiyin_tween:Kill()
        self.open_zhiyin_tween = nil
        self.is_tweening = false
    end
    if self.close_zhiyin_tween then
        self.close_zhiyin_tween:Kill()
        self.close_zhiyin_tween = nil
    end
    if self.font_tween then
        self.font_tween:Kill()
        self.font_tween = nil
    end
    if self.font_tween1 then
        self.font_tween1:Kill()
        self.font_tween1 = nil
    end
    self.is_tweening = false
end

function MarryView:OnClickZhiYinBtn()
    local index = self.cur_zhiyin_index
    if index == 1 then --我要脱单
        self:OnClickOpenNearbySingle()
    elseif index == 2 then --我要结婚
        self:OpenSelectLoverView(JIE_HUN)
    end
end

function MarryView:OnClickLiaoYiLiao()
    local lyl_role_info = MarryWGData.Instance:GetLiaoYiLiaoObjInfo()
    if IsEmptyTable(lyl_role_info) or lyl_role_info.role_id <= 0 then
        return
    end

    if SocietyWGData.Instance:GetIsMyFriend(lyl_role_info.role_id) then
        local content = SocietyWGData.Instance:GetRandomContent()
        ChatWGCtrl.Instance:SendPrivateChatMsg(lyl_role_info.role_id, content[1].content, CHAT_CONTENT_TYPE.TEXT)
    --     SocietyWGCtrl.Instance:SetIgnodeDefSelectFriendValue(true)
    --     ViewManager.Instance:FlushView(GuideModuleName.Society, SocietyView.Tab_F, "liao_yi_liao",{lyl_role_info.role_id, 1})
    --     ViewManager.Instance:Open(GuideModuleName.Society, SocietyView.Tab_F)
    -- else
    --     MarryWGCtrl.Instance:ReqLYLRole(lyl_role_info.role_id)
    end

    MarryWGCtrl.Instance:ReqLYLRole(lyl_role_info.role_id)
end

function MarryView:OnClickChangeLiao()
    if not MarryWGData.Instance:GetIsLYLChangeRoleInCD() then
        MarryWGCtrl.Instance:ReqChangeLYLRole()
        MarryWGData.Instance:UpdateLYLChangeRoleCD()
    else
        TipsSystemManager.Instance:ShowSystemTips(string.format(Language.Marry.LYLChangeRoleCD, math.ceil(MarryWGData.Instance:GetLYLChangeRoleRemainCD())))
    end
end

function MarryView:ShowJieHunIndexCallBack()
	if self.need_show_lovel_model then
		self.need_show_lovel_model = false
		self:ShowJieHunLoverModel()
	end
	if RoleWGData.Instance.role_vo.lover_uid > 0 then
		self:NeedInitJieHun()
	end
end

function MarryView:DeleteJieHunView()
	self.effect = nil
	self.need_init_jiehun = false
	self.rember_baby_id = nil
    self.lover_display_resid = nil
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
    end

    if self.close_zhiyin_tween then
        self.close_zhiyin_tween:Kill()
        self.close_zhiyin_tween = nil
    end

    if self.font_tween then
        self.font_tween:Kill()
        self.font_tween = nil
    end

    if self.font_tween1 then
        self.font_tween1:Kill()
        self.font_tween1 = nil
    end

    if self.open_zhiyin_tween then
        self.open_zhiyin_tween:Kill()
        self.open_zhiyin_tween = nil
    end

	if self.lover_display then
		self.lover_display:DeleteMe()
		self.lover_display = nil
	end

	if self.role_baby_model then
		self.role_baby_model:DeleteMe()
		self.role_baby_model = nil
	end

	if self.lover_baby_display then
		self.lover_baby_display:DeleteMe()
		self.lover_baby_display = nil
	end

    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
        self.role_head_cell = nil
    end

    if self.lyl_role_avatar then
        self.lyl_role_avatar:DeleteMe()
        self.lyl_role_avatar = nil
    end

    self:CleanLYLChangeCDTime()

	if CountDownManager.Instance:HasCountDown("getbaby_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("getbaby_end_countdown")
	end

    if self.liaoyiliao_tweener then
        self.liaoyiliao_tweener:Kill()
        self.liaoyiliao_tweener = nil
    end
end

function MarryView:NeedInitJieHun()
	self.node_list["marry_view"]:SetActive(true)
	self.node_list["no_marry_view"]:SetActive(false)
	self:CreateRole()
	if self.need_init_jiehun then
		self.need_init_jiehun = false
		self:FlushJieHunView()
	end
end

function MarryView:FirstShowZhiyin()
    --self:SetZhiyinActive(true)
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt("first_enter_jiehun" .. main_role_id, 1)
end

function MarryView:GetIsFirstEnterJiehun()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	return  PlayerPrefsUtil.GetInt("first_enter_jiehun" .. main_role_id) == 0
end

--没有结婚/结婚后立马离婚 的逻辑
function MarryView:ClearCurView()
    if not self.is_first_enter_jiehun and self:GetIsFirstEnterJiehun() then
        self:FirstShowZhiyin()
        self.is_first_enter_jiehun = true
    end

	self.need_init_jiehun = true   --下一帧他们结婚时需要初始化操作
	self.node_list["marry_view"]:SetActive(false)
	self.node_list["no_marry_view"]:SetActive(true)

    if self.lover_display then
        self.lover_display:RemoveAllModel()
    end

	if self.lover_baby_display then
		self.lover_baby_display:RemoveMain()
	end
end

function MarryView:FlushJieHunView()
	--领取宝宝提示相关设置
	self:GetBabyHintSet()
    local role_sex = RoleWGData.Instance.role_vo.sex or 0  --角色性别
    local trans = role_sex == GameEnum.MALE and self.node_list["no_display_left"] or self.node_list["no_display_right"]
    local pos_x = role_sex == GameEnum.MALE and 3.6 or -1.5
    if nil == self.role_model then
        -- 模型展示
		self.role_model = RoleModel.New()
		self.role_model:SetUISceneModel(trans.event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, TabIndex.marry_jiehun)
    else
        self.role_model:PlayLastAction()
    end

    local resource = MarryWGData.Instance:GetMarryOtherCfg().wedding_dress_show
    local res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(resource)

    local vo = GameVoManager.Instance:GetMainRoleVo()
    if self.role_model then
        -- local ignore_table = {ignore_wing = true, ignore_halo= true, ignore_weapon= true,
        -- ignore_fazhen= true, ignore_mantle = true, ignore_waist = true, ignore_mask = true,
        -- ignore_shouhuan = true, ignore_tail= true, ignore_jianzhen= true}
        -- self.role_model:SetModelResInfo(vo, ignore_table)

        local extra_role_model_data = {
            prof = vo.prof,
            sex = vo.sex,
        }
        self.role_model:SetRoleResid(res_id, nil, extra_role_model_data)
        self.role_model:SetWeaponModelFakeRemove()
        self.role_model:SetUSAdjustmentNodeLocalPosition(pos_x, 0, 0)
    end

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
	if lover_id <= 0 then  --没有结婚
        self.node_list["man"]:SetActive(not (role_sex == GameEnum.MALE))
        self.node_list["woman"]:SetActive(role_sex == GameEnum.MALE)
        self:ClearCurView()

        --self:InitLylPanel()
		return
	end
 
    if self.lover_display then
		self.lover_display:PlayLastAction()
	end

	if self.need_init_jiehun then
		self:NeedInitJieHun()
        --self:InitLylPanel()
		return
	end

	-- if lover_id > 0 then
	-- 	self.node_list["btn_open_nearby_single"]:SetActive(false)
	-- else
	-- 	self.node_list["btn_open_nearby_single"]:SetActive(true)
	-- end

	local flag = MarryWGData.Instance:GetMarryTitleRemindShow()
    self.node_list["img_remind"]:SetActive(flag == 1)
    self.node_list["jiehun_yuyue_remind"]:SetActive(false)

    local baoxia_remind = MarryWGData.Instance:CanBaoXiaRemind()
    self.node_list.baoxia_remind:SetActive(baoxia_remind == 1)
end

function MarryView:CleanLYLChangeCDTime()
	if CountDownManager.Instance:HasCountDown("lyl_change_cd") then
		CountDownManager.Instance:RemoveCountDown("lyl_change_cd")
	end
end


function MarryView:InitLylPanel()
    local lyl_role_info = MarryWGData.Instance:GetLiaoYiLiaoObjInfo()
    if lyl_role_info and lyl_role_info.role_id ~= 0 then
        --self.node_list["btn_liaoyiliao"].button.interactable = true
        self.node_list["liaoyiliao_content"]:SetActive(true)
        self.node_list["liaoyiliao_empty"]:SetActive(false)
        --print_error("InitJieHunView lyl_role_info =", lyl_role_info)
        BrowseWGCtrl.Instance:SendQueryRoleInfoReq(lyl_role_info.role_id, function (role_info)
            -- print_error("role_info =", role_info)
            local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(role_info.level)
            self.node_list.jh_dianfeng_icon:SetActive(is_vis)
            local level_str = role_level
            if not is_vis then
                level_str = "Lv." .. level_str
            end
            local city_name = ""
            if lyl_role_info.city_name and lyl_role_info.city_name ~= "" then
                city_name = string.format("[%s]",lyl_role_info.city_name)
            end
            --self.node_list["role_name"].text.text = string.format("%s %s %s", level_str, role_info.role_name,city_name)
            self.node_list["xindong_num"].text.text = lyl_role_info.rand_num .. "%"

            if not self.role_head_cell then
                self.role_head_cell = BaseHeadCell.New(self.node_list["role_head"])
            end

            if self.role_head_cell then
                self.role_head_cell:SetData(role_info)
            end

            if not self.lyl_role_avatar then
                self.lyl_role_avatar = RoleHeadCell.New(false)
            end

            if self.lyl_role_avatar then
                local role_data = {
                    role_id = role_info.role_id,
                    role_name = role_info.role_name,
                    prof = role_info.prof,
                    sex = role_info.sex,
                    is_online = role_info.is_online,
                    team_index = role_info.team_index,
                    team_type = TEAM_INVITE_TYPE.FRIEND,
                    plat_type = role_info.plat_type,
                    server_id = role_info.server_id,
                }
                self.lyl_role_avatar:SetRoleInfo(role_data)
                self.lyl_role_avatar:RemoveItems(Language.Menu.Blacklist)
            end
        end)

        local lyl_is_cd = MarryWGData.Instance:GetIsLYLChangeRoleInCD()

        local res_time = MarryWGData.Instance:GetLYLChangeRoleRemainCD()
        self:UpdataLYLChangeBtnText(res_time)
        if res_time > 0 then
            self:CleanLYLChangeCDTime()
            CountDownManager.Instance:AddCountDown("lyl_change_cd",
            BindTool.Bind(self.UpdateLYLChangeBtnShow, self),
            function()
                self:UpdataLYLChangeBtnText(0)
            end,
            nil, res_time, 0.5)
        end
    else
        self.node_list["liaoyiliao_content"]:SetActive(false)
        self.node_list["liaoyiliao_empty"]:SetActive(true)
    end
end

function MarryView:SetJieHunPanel()

end

--人物自己的信息和孩子
function MarryView:CreateRole()
    local role_sex = RoleWGData.Instance:GetRoleSex()
    local trans = {}
    trans[1] = role_sex == GameEnum.MALE and self.node_list["ph_display_left"] or self.node_list["ph_display_right"]
    trans[2] = role_sex == GameEnum.MALE and self.node_list["ph_display_baby_left"] or self.node_list["ph_display_baby_right"]
    --trans[1] = self.node_list["ph_display_left"]
    --trans[2] = self.node_list["ph_display_baby_left"]

    local pos_x = role_sex == GameEnum.MALE and 3.6 or -1.5
    local baby_pos_x = role_sex == GameEnum.MALE and 4.5 or -0.6
    local role_text = role_sex == GameEnum.MALE and self.node_list["label_left_name"] or self.node_list["label_right_name"]
    local lover_text = role_sex == GameEnum.MALE and self.node_list["label_right_name"] or self.node_list["label_left_name"]
    -- trans[1] = self.node_list["ph_display_left"]
    -- trans[2] = self.node_list["ph_display_baby_left"]

    local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
    --local role_text = self.node_list["label_left_name"]
    role_text.text.text = RoleWGData.Instance.role_vo.name
    --local lover_text = self.node_list["label_right_name"]
	if lover_id > 0 then
        lover_text.text.text = RoleWGData.Instance.role_vo.lover_name
		BrowseWGCtrl.Instance:BrowRoelInfo(lover_id, BindTool.Bind(self.JieHunVoAck, self))
	else
		lover_text.text.text = Language.Marry.NoLover
	end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.label_left_name.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.label_right_name.rect)

    self:AdjustPos()

    if nil == self.role_model then
        -- 模型展示
		self.role_model = RoleModel.New()
		self.role_model:SetUISceneModel(trans[1].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, TabIndex.marry_jiehun)
	end

    local resource = MarryWGData.Instance:GetMarryOtherCfg().wedding_dress_show
    local res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(resource)
    local vo = GameVoManager.Instance:GetMainRoleVo()
    if self.role_model and self.role_model.role_res_id ~= res_id then
        -- local ignore_table = {ignore_wing = true, ignore_halo= true, ignore_weapon= true,
        -- ignore_fazhen= true, ignore_mantle = true, ignore_waist = true, ignore_mask = true,
        -- ignore_shouhuan = true, ignore_tail= true, ignore_jianzhen= true}
        -- self.role_model:SetModelResInfo(vo, ignore_table)
        local extra_role_model_data = {
            prof = vo.prof,
            sex = vo.sex,
        }
        self.role_model:SetRoleResid(res_id, nil, extra_role_model_data)
        self.role_model:SetUSAdjustmentNodeLocalPosition(pos_x, 0, 0)
        self.role_model:SetWeaponModelFakeRemove()
    end

    --孩子
    if nil == self.role_baby_model then
        -- 模型展示
		self.role_baby_model = RoleModel.New()
		self.role_baby_model:SetUISceneModel(trans[2].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_baby_model, TabIndex.marry_jiehun)
    end

    if self.role_baby_model then
        local bundle, asset = ResPath.GetHaiZiModel(vo.use_baby_id)
        if bundle == nil and asset == nil then
            return
        end

        self.role_baby_model:SetMainAsset(bundle, asset)
        self.role_baby_model:SetUSAdjustmentNodeLocalPosition(baby_pos_x, -1, -1)
        self.role_baby_model:SetUSAdjustmentNodeLocalScale(0.6)
    end
end

--求婚按钮 -- 跑到月老那边去
function MarryView:OpenSelectLoverView(btn_index)
	MarryWGCtrl.Instance:OpenSelectLoverView(btn_index)
end

function MarryView:JieHunVoAck(protocol)
	if not protocol then
		print_error("protocol error！！")
		return
	end
	MarryWGData.Instance:SetLoverInfo2(protocol)
	--请求查询玩家信息时切换了其他界面，不显示模型
	if self.show_index ~= TabIndex.marry_jiehun then
		self.need_show_lovel_model = true
		return
	end
	self:ShowJieHunLoverModel()
end

--加载对象的信息
function MarryView:ShowJieHunLoverModel()
    self:AdjustPos()
    local role_base_info_ack = MarryWGData.Instance:GetLoverInfo2()
    if not role_base_info_ack then
        return
    end

    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    local role_sex = role_base_info_ack.sex
    local d_face_res = role_base_info_ack.appearance.default_face_res_id
    local d_hair_res = role_base_info_ack.appearance.default_hair_res_id
    local d_body_res = role_base_info_ack.appearance.default_body_res_id
    local trans = {}

    trans[1] = main_role_vo.sex == GameEnum.MALE and self.node_list["ph_display_right"] or self.node_list["ph_display_left"]
    trans[2] = main_role_vo.sex == GameEnum.MALE and self.node_list["ph_display_baby_right"] or self.node_list["ph_display_baby_left"]

    local pos_x = main_role_vo.sex == GameEnum.MALE and -1.5 or 3.6
    local baby_pos_x = main_role_vo.sex == GameEnum.MALE and -0.6 or 4.5
    --trans[1] = self.node_list["ph_display_right"]
    --trans[2] = self.node_list["ph_display_baby_right"]
    --local pos_x = -1.5
    --local baby_pos_x = -0.6

	if nil == self.lover_display then
        -- 模型展示
		self.lover_display = RoleModel.New()
		self.lover_display:SetUISceneModel(trans[1].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.lover_display, TabIndex.marry_jiehun)
	end

    local resource = MarryWGData.Instance:GetMarryOtherCfg().wedding_dress_show
    local res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(resource)
    if self.lover_display then
        local extra_role_model_data = {
            prof = role_base_info_ack.prof,
            sex = role_sex,
            d_face_res = d_face_res,
            d_hair_res = d_hair_res,
            d_body_res = d_body_res,
        }
        self.lover_display:SetRoleResid(res_id, nil, extra_role_model_data)
        self.lover_display_resid = res_id
        self.lover_display:SetWeaponModelFakeRemove()
        self.lover_display:SetUSAdjustmentNodeLocalPosition(pos_x, 0, 0)
    end

	--孩子
	if nil == self.lover_baby_display then
        -- 模型展示
		self.lover_baby_display = RoleModel.New()
		self.lover_baby_display:SetUISceneModel(trans[2].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.lover_baby_display, TabIndex.marry_jiehun)
	end
    if self.lover_baby_display then
		local bundle, asset = ResPath.GetHaiZiModel(role_base_info_ack.lover_use_baby_id)
		if bundle == nil and asset == nil then
			return
		end
		if self.rember_baby_id and self.rember_baby_id == role_base_info_ack.lover_use_baby_id then
			return
		end
		self.rember_baby_id = role_base_info_ack.lover_use_baby_id
		self.lover_baby_display:SetMainAsset(bundle, asset)
        self.lover_baby_display:SetUSAdjustmentNodeLocalPosition(baby_pos_x, -1, -1)
        self.lover_baby_display:SetUSAdjustmentNodeLocalScale(0.6)
    end

end

function MarryView:AdjustPos()
    local baby_num = 0
    local lover_info =  MarryWGData.Instance:GetLoverInfo2()
    if GameVoManager.Instance:GetMainRoleVo().use_baby_id > 0 then
        baby_num = baby_num + 1
    end
    if lover_info and lover_info.lover_use_baby_id > 0 then
        baby_num = baby_num + 1
    end

    if baby_num == 2 then
        self.node_list["ph_display_baby_left"].rect.localPosition = Vector2(-134, -59)
        self.node_list["ph_display_baby_right"].rect.localPosition = Vector2(122, -59)
        self.node_list["ph_display_left"].rect.localPosition = Vector2(42, -22)
        self.node_list["ph_display_right"].rect.localPosition = Vector2(-76, -22)
    elseif baby_num == 1 then
        self.node_list["ph_display_baby_left"].rect.localPosition = Vector2(234, -59)
        self.node_list["ph_display_baby_right"].rect.localPosition = Vector2(-220, -59)
        self.node_list["ph_display_left"].rect.localPosition = Vector2(-26, -22)
        self.node_list["ph_display_right"].rect.localPosition = Vector2(-19, -22)
    else
        self.node_list["ph_display_baby_left"].rect.localPosition = Vector2(-220, -59)
        self.node_list["ph_display_baby_right"].rect.localPosition = Vector2(-220, -59)
        self.node_list["ph_display_left"].rect.localPosition = Vector2(-26, -22)
        self.node_list["ph_display_right"].rect.localPosition = Vector2(-19, -22)
    end

    local role_base_info_ack = MarryWGData.Instance:GetLoverInfo2()
    if role_base_info_ack then
        local role_sex = RoleWGData.Instance:GetRoleSex()
        if role_sex == GameEnum.MALE and role_base_info_ack.sex == GameEnum.MALE then
            self.node_list["my_title"].text.text = Language.Marry.BaoZe
            self.node_list["ta_title"].text.text = Language.Marry.BaoZe
        elseif role_sex == GameEnum.FEMALE and role_base_info_ack.sex == GameEnum.FEMALE then
            self.node_list["my_title"].text.text = Language.Marry.JinLan
            self.node_list["ta_title"].text.text = Language.Marry.JinLan
        else
            local title_text = role_sex == GameEnum.MALE and self.node_list["my_title"] or self.node_list["ta_title"]
            local love_title_text = role_sex == GameEnum.MALE and self.node_list["ta_title"] or self.node_list["my_title"]
            title_text.text.text = role_sex == GameEnum.MALE and Language.Marry.FuJun or Language.Marry.NiangZi
            love_title_text.text.text = role_base_info_ack.sex == GameEnum.MALE and Language.Marry.FuJun or Language.Marry.NiangZi
        end
    end
end


--我要征婚
function MarryView:GetMarryBoard()
	if MarryWGData.Instance:GetIsCanSeeking() then
		MarryWGCtrl.Instance:SendMarrySeekingReq()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryZhengHun)
	end
end

--离婚按钮
function MarryView:OpenClickDivorceView()

end

--帮助按钮点击
function MarryView:OpenMarryTips()
	local role_tip = RuleTip.Instance
	local title = Language.Marry.MarryTipsTitle
	local content = ""
	if RoleWGData.Instance.role_vo.lover_uid > 0 then
		content = Language.Marry.MarryAfterTips
	else
  		content = Language.Marry.MarryTips
  	end
  	MarryView.OpenTips(content,title)
end

--装备格子点击
function MarryView:ClickCellCallBack()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	if lover_id == 0  then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.LockRing)
		return
	end
end

--查看仙侣称号
function MarryView:OnClickOpenTitle()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_LOVER_TITLE_INFO)
	MarryWGCtrl.Instance:OpenMarryTitleView()
end

--我要脱单
function MarryView:OnClickOpenNearbySingle()
    MarryWGCtrl.Instance:SendMarryReq(MARRY_REQ_TYPE.MARRY_REQ_SINGLE)
	MarryWGCtrl.Instance:OpenNearbySingleView()
end

function MarryView:OnClickBaoXia()
    ViewManager.Instance:Open(GuideModuleName.MarryBaoXiaView)
end

function MarryView:OnClickOpenGetBabyView()
	local get_baby_timestamp, get_baby_flag = MarryWGData.Instance:GetBabyLingQuInfo()
	if get_baby_timestamp > 0 and get_baby_timestamp <= TimeWGCtrl.Instance:GetServerTime() and get_baby_flag == 0 then
        local data = MarryWGData.Instance:GetBabyActiveCardCfg()[1]
        if data and data.type then
            MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_FETCH_BABY, data.type, data.id)
        end
    elseif get_baby_timestamp > TimeWGCtrl.Instance:GetServerTime() then
        MarryWGCtrl.Instance:OpenGetNewView()
        --local time = get_baby_timestamp - TimeWGCtrl.Instance:GetServerTime()
        --local format_time = TimeUtil.FormatSecondDHM4(time)
        --SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.GetBabyTimeHintDes1, format_time))
	end
end

function MarryView:GetBabyHintSet()
    local get_baby_timestamp, get_baby_flag = MarryWGData.Instance:GetBabyLingQuInfo()
	if get_baby_timestamp <= 0 then
		self.node_list["lingqu_baby"]:SetActive(false)
		self:SetEffectVisable(false)
		return
	elseif get_baby_timestamp > 0 and get_baby_flag == 0 then
		self.node_list["lingqu_baby"]:SetActive(true)
	elseif get_baby_timestamp > 0 and get_baby_flag == 1 and RoleWGData.Instance.role_vo.lover_uid > 0 then
		self.node_list["img_get_hint"]:SetActive(false)
        self.node_list["get_baby_hint"].text.text = ""
		self.node_list["lingqu_baby"]:SetActive(false)
		self:SetEffectVisable(false)
		return
	else
		self.node_list["lingqu_baby"]:SetActive(false)
	end
	if get_baby_timestamp - TimeWGCtrl.Instance:GetServerTime() > 0 and get_baby_flag == 0 then
		self.node_list["img_get_hint"]:SetActive(true)
		self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), get_baby_timestamp)
		if CountDownManager.Instance:HasCountDown("getbaby_end_countdown") then
			CountDownManager.Instance:RemoveCountDown("getbaby_end_countdown")
		end
		self:SetEffectVisable(false)
		CountDownManager.Instance:AddCountDown("getbaby_end_countdown", BindTool.Bind1(self.UpdataEndTime, self), BindTool.Bind1(self.EndTimeCallBack, self), get_baby_timestamp, nil, 1)
	elseif get_baby_timestamp > 0 and get_baby_flag == 0 then --时间到未领取
		self.node_list["img_get_hint"]:SetActive(false)
        self.node_list["get_baby_hint"].text.text = "" -- Language.Marry.GetBabyTimeHint3
		self:SetEffectVisable(true)
	elseif get_baby_timestamp > 0 and get_baby_flag == 1 then --时间到已领取
		self.node_list["img_get_hint"]:SetActive(false)
        self.node_list["get_baby_hint"].text.text = ""
		self:SetEffectVisable(false)
	else --未结婚尚未开启此功能
		self.node_list["img_get_hint"]:SetActive(false)
        self.node_list["get_baby_hint"].text.text = ""
		self:SetEffectVisable(false)
	end
	--设置展示的坐标
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
	--local pos = lover_id <= 0 and Vector2(9.3,191.9) or Vector2(9.3,162.7)
	--self.node_list["lingqu_baby"].rect.anchoredPosition = pos
end

function MarryView:UpdataEndTime(elapse_time, total_time)
	local time = total_time - elapse_time
    local format_time = TimeUtil.FormatSecondDHM4(time)
    self:SetEffectVisable(false)
    self.node_list["get_baby_hint"].text.text = string.format(Language.Marry.GetBabyTimeHintDes, format_time)
end

function MarryView:EndTimeCallBack()
	local get_baby_timestamp, get_baby_flag = MarryWGData.Instance:GetBabyLingQuInfo()
	if get_baby_timestamp > 0 and get_baby_flag == 0 then --时间到未领取
		self.node_list["img_get_hint"]:SetActive(false)
        --self.node_list["get_baby_hint"].text.text = Language.Marry.GetBabyTimeHint3
        self.node_list["get_baby_hint"].text.text = ""
		self:SetEffectVisable(true)
	else
        self.node_list["img_get_hint"]:SetActive(false)
		self.node_list["get_baby_hint"].text.text = ""
		self:SetEffectVisable(false)
	end
end
--设置特效显隐
function MarryView:SetEffectVisable(enable)
    self.node_list.baby_remind:SetActive(enable)
	--self.node_list["baby_lingqu_effect"]:SetActive(enable)
end

function MarryView:ClickLYLRoleHead()
    self.lyl_role_avatar:OpenMenu(nil,SocietyWGCtrl.Instance:GetCacularPos(self.node_list["role_head"]))--Vector2(-141, -82))
end

function MarryView:OnClickHeadView()
    -- LoverPkWGCtrl.Instance:OpenLoverPKHeadView()
end