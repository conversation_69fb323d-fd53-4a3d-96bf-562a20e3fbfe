function YiBenWanLiView:InitDailyGift(index,loaded_times)
	if not self.list_view then
		self.list_view = AsyncListView.New(DailyGiftCell,self.node_list.list_view)
	end
	-- self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnClickDailyGiftCloseBtn,self))
	self.node_list.onekey_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickOneClickBuyBtn,self))
	self.node_list["list_view"].scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnValueChanged, self))
end

function YiBenWanLiView:OnValueChanged()
  	local percent = self.node_list["list_view"].scroll_rect.horizontalNormalizedPosition
  	-- self.node_list.left_shadow:SetActive(percent ~= 0)
  	-- self.node_list.right_shadow:SetActive(percent ~= 1)
end

function YiBenWanLiView:DeleteDailyGift()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
end

function YiBenWanLiView:FlushDailyGift()
	local data_list = YiBenWanLiWGData.Instance:GetDailyGiftListViewData()
	if not data_list then return end
	self.list_view:SetDataList(data_list,2)
end

function YiBenWanLiView:OnClickDailyGiftCloseBtn()
	self:Close()
end

function YiBenWanLiView:OnClickOneClickBuyBtn()
	local data_list = YiBenWanLiWGData.Instance:GetDailyGiftListViewData()
	if not data_list then
		return
	end

	local str_list= {}

	local money = 0

	local flag_list = {}
	for i=1,32 do
		flag_list[i] = 0
		str_list[i] = ""
	end

	for i=1,#flag_list do
		if nil ~= data_list[i] then
			if data_list[i].state == 0 then
				money = money + data_list[i].cfg.buy_cost
				flag_list[33 - data_list[i].seq] = 1
				str_list[i] = data_list[i].cfg.gift_name .. "、"
			end
		end
	end
	if money <= 0 then
		return
	end
	local str = string.format(Language.YiBneWanLi.OneKeyBuyDesc, str_list[1], str_list[2], str_list[3], str_list[4], str_list[5], str_list[6])
	-- 直接拼接会多个顿号，只能特殊处理
	str = string.sub(str, 0, -4) .. ")"
	local flag = bit:b2d(flag_list)
	RechargeWGCtrl.Instance:Recharge(money, GET_GOLD_REASON.GET_GOLD_REASON_YIBENWANLI_DAILY_GIFT, flag)
end

-------------------------------------------------------------------------------DailyGiftCell-------------------------------------------------------------------------------

DailyGiftCell = DailyGiftCell or BaseClass(BaseRender)

function DailyGiftCell:__init()
	self.item_list = {}
	for i=1,3 do
		self.item_list[i] = ItemCell.New(self.node_list["item_cell_"..i])
	end
	self.node_list.fetch_btn.button:AddClickListener(BindTool.Bind(self.OnClicFetchBtn,self))
end

function DailyGiftCell:__delete()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
	end
end

function DailyGiftCell:OnFlush()
	if not self.data then return end

	for i=1,3 do
		if self.data.cfg.reward_show[i - 1] then
			self.node_list["item_cell_"..i]:SetActive(true)
			self.item_list[i]:SetData(self.data.cfg.reward_show[i - 1])
		else
			self.node_list["item_cell_"..i]:SetActive(false)
		end
	end

	self.node_list.gift_name.text.text = self.data.cfg.gift_name
	self.node_list.gift_values.text.text = self.data.cfg.gift_value
	--0 不可领取，1 可领取， 2 已领取
	-- XUI.SetButtonEnabled(self.node_list.fetch_btn,true)
	self.node_list.fetch_btn:SetActive(true)
	self.node_list.fetch_flag:SetActive(false)
	self.node_list.remind:SetActive(false)
	if self.data.state == 0 then
		self.node_list.fetch_text.text.text = string.format(Language.YiBneWanLi.RMBText,self.data.cfg.buy_cost)
	elseif self.data.state == 1 then
		self.node_list.remind:SetActive(true)
		self.node_list.fetch_text.text.text = Language.YiBneWanLi.LiJiLingQu
	else
		-- XUI.SetButtonEnabled(self.node_list.fetch_btn,false)
		self.node_list.fetch_btn:SetActive(false)
		self.node_list.fetch_flag:SetActive(true)
		self.node_list.fetch_text.text.text = Language.Common.YiLingQu
	end
end

function DailyGiftCell:OnClicFetchBtn()
	if not self.data then return end
	if self.data.state == 0 then
		local flag_list = {}
		for i=1,32 do
			flag_list[i] = 0
		end

		flag_list[33 - self.data.seq] = 1
		local flag = bit:b2d(flag_list)
		--充钱
		RechargeWGCtrl.Instance:Recharge(self.data.cfg.buy_cost, GET_GOLD_REASON.GET_GOLD_REASON_YIBENWANLI_DAILY_GIFT, flag)
	elseif self.data.state == 1 then
		--领取
		YiBenWanLiWGCtrl.Instance:SendYiBenWanLiWGCtrl(YIBENWANLI_OPERA_TYPE.YIBENWANLI_OPERA_DAILY_FETCH,self.data.cfg.seq)
	end

end