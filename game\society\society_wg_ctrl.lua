require("game/society/society_view")
require("game/society/master/master_info_view")
require("game/society/master/master_worship_view")

require("game/society/society_add_req")
require("game/society/society_friend_list")
require("game/society/society_send_goods_view")
-- require("game/society/society_near_list")
-- require("game/society/society_nearteam_list")
-- require("game/society/society_team_req")
require("game/society/society_team_invite_req")
require("game/society/society_wg_data")
require("game/society/society_friend")
require("game/society/society_paiyipai")
require("game/society/society_send_num_view")
require("game/society/society_mail")
require("game/society/society_black")
-- require("game/society/society_team")
require("game/society/society_enemy")
require("game/society/society_items")
require("game/society/society_auto_add")
require("game/society/society_friend_remove")
require("game/society/society_add_friend")
require("game/chat/add_blacklist_view")
-- require("game/society/society_friends_blessing_view")
-- require("game/society/society_blessing_receive_view")
-- require("game/society/society_blessing_view")
-- require("game/society/society_blessing_tip")
require("game/society/society_bekilled_view")

require("game/serveractivity/fishpond/fishpond_view")
require("game/serveractivity/fishpond/fishpond_friend_view")
require("game/serveractivity/fishpond/fishpond_all_info_view")
-- require("game/serveractivity/fishpond/fishpond_fish_info_view")
require("game/serveractivity/fishpond/fishpond_shop_view")
require("game/serveractivity/fishpond/fish")
require("game/society/humo_rember_view")
require("game/society/humo_betouch_view")
-- require("utils/calcstep")
require("game/society/society_enemy_record_view")


OPERA_TYPE = {
	FRIEND_BLESS_OPERA_TYPE_INVALID = 0,				-- 无效请求
	FRIEND_BLESS_OPERA_TYPE_QUERY = 1,					-- 好友祝福信息请求
	FRIEND_BLESS_OPERA_TYPE_BLESS = 2,					-- 祝福好友请求
	FRIEND_BLESS_OPERA_TYPE_INVITE_BLESS = 3			-- 邀请好友祝福我
}

FRIEND_BLESS_NOTIFY_REASON = {
		FRIEND_BLESS_NOTIFY_REASON_DEFAULT = 0,
		FRIEND_BLESS_NOTIFY_REASON_REMOVE_STATUS = 1,		-- 删除祝福状态
		FRIEND_BLESS_NOTIFY_REASON_UPDATE_STATUS = 2,		-- 更新祝福状态
		FRIEND_BLESS_NOTIFY_REASON_UPDATE_REWARD_TIMES = 3,	-- 更新领奖数量
		FRIEND_BLESS_NOTIFY_REASON_MAX = 3,
	}

-- 社交
SocietyWGCtrl = SocietyWGCtrl or BaseClass(BaseWGCtrl)

function SocietyWGCtrl:__init()
	if SocietyWGCtrl.Instance then
		ErrorLog("[SocietyWGCtrl] attempt to create singleton twice!")
		return
	end
	SocietyWGCtrl.Instance = self

	self.data_mgr = SocietyWGData.New()
	self.society_view = SocietyView.New(GuideModuleName.Society)
	self.add_req_view = SocietyAddReqView.New(GuideModuleName.SocietyAddReqView)
	self.auto_addfriend_view = SocietyAutoAddView.New(GuideModuleName.OpenKeyAddFriend)
	self.friend_list_view = SocietyFriendListView.New()
	self.add_blacklist_view = AddBlacklistView.New()
	-- self.team_near_list_view = SocietyNearTeam.New()
	-- self.team_req_list_view = SocietyTeamReq.New()
	self.team_invite_list_view = SocietyTeamInviteReq.New(GuideModuleName.SocietyTeamInviteReq)
	self.society_remove_view = SocietyRemoveView.New()
	self.society_add_friend_view = AddFriendView.New()
	-- self.near_list_view = SocietyNearListView.New()
	-- self.friends_blessing_view = SocietyFriendsBlessingView.New()
	-- self.friends_receive_view = SocietyBlessingReceiveView.New()
	-- self.blessing_view = SocietyBlessingView.New()
	-- self.blessingTip = SocietyBlessingTip.New()
	self.bekilled_view = SocietyBeKilledView.New()
	self.society_send_goods_view = SocietySendGoodsView.New(GuideModuleName.SocietySendGoods)  --赠送面板
	self.society_send_num = SocietySendNumPanel.New()  --赠送数量面板
	self.society_send_record_view = SocietySendRecordPanel.New()  --赠送记录面板

	self.add_friend_tips_panel = AddFriendTipsPanel.New()
	self.humo_betouch_view = HuMoBeTouchHintView.New() --虎摸提示
	self.humo_rember_view = HuMoRemberInfoView.New()

	self.enemy_record_view = SocietyEnemyRecordView.New(GuideModuleName.SocietyEnemyRecordView)

	self.callback_list = {}
	self.m_friend_list_callback_flag = ""
	self.m_near_list_callback_flag = ""
	self.gamename_invite = ""									--邀请我祝福的好友的名字
	self.blessing_icon = nil
	self.is_flower_panen_open = false
	self.del_mail_count = 0
	self.del_mail_temp_count = 0
	self.open_friend_from_main_icon = false
	self.set_cell_data_list = {}

    self.invite_callback_list = {}
	--self.temp_all_obj_chatlist = {}   ----用来接收聊天消息  用来做未读的对比

	self.friend_view = FishpondFriendView.New()
	self.all_info_view = FishpondAllInfoView.New()
	-- self.fish_info_view = FishpondFishInfoView.New()
	self.is_friend_chat_panel = false
	self.select_send_friend_data = {}

	self:RegisterAllProtocals()

	self:Bind(OtherEventType.ROLE_ONLINE_CHANGE, BindTool.Bind1(self.OnOtherRoleOnlineChange, self))
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.ToGetBlessAllInfo, self))
	self:BindGlobalEvent(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind1(self.OnRecvMainRoleInfo, self))

end

function SocietyWGCtrl:__delete()
	if self.add_blacklist_view then
		self.add_blacklist_view:DeleteMe()
		self.add_blacklist_view = nil
	end
	if self.society_send_goods_view then
		self.society_send_goods_view:DeleteMe()
		self.society_send_goods_view = nil
	end
	if self.society_send_num then
		self.society_send_num:DeleteMe()
		self.society_send_num = nil
	end
	if self.society_send_record_view then
		self.society_send_record_view:DeleteMe()
		self.society_send_record_view = nil
	end
	if nil ~= self.data_mgr then
		self.data_mgr:DeleteMe()
		self.data_mgr = nil
	end
	self.del_mail_count = nil
	self.del_mail_temp_count = nil
	if nil ~= self.society_view then
		self.society_view:DeleteMe()
		self.society_view = nil
	end

	if self.add_friend_tips_panel then
		self.add_friend_tips_panel:DeleteMe()
		self.add_friend_tips_panel = nil
	end
	if nil ~= self.add_req_view then
		self.add_req_view:DeleteMe()
		self.add_req_view = nil
	end

	if nil ~= self.auto_addfriend_view then
		self.auto_addfriend_view:DeleteMe()
		self.auto_addfriend_view = nil
	end

	if nil ~= self.friend_list_view then
		self.friend_list_view:DeleteMe()
		self.friend_list_view = nil
	end

	if nil ~= self.near_list_view then
		self.near_list_view:DeleteMe()
		self.near_list_view = nil
	end

	if nil ~= self.team_near_list_view then
		self.team_near_list_view:DeleteMe()
		self.team_near_list_view = nil
	end

	if nil ~= self.team_req_list_view then
		self.team_req_list_view:DeleteMe()
		self.team_req_list_view = nil
	end

	if nil ~= self.team_invite_list_view then
		self.team_invite_list_view:DeleteMe()
		self.team_invite_list_view = nil
	end

	if self.society_remove_view then
		self.society_remove_view:DeleteMe()
		self.society_remove_view = nil
	end


	if self.society_add_friend_view then
		self.society_add_friend_view:DeleteMe()
		self.society_add_friend_view = nil
	end

	self.bekilled_view:DeleteMe()
	self.bekilled_view = nil

	if self.friends_blessing_view then
		self.friends_blessing_view:DeleteMe()
		self.friends_blessing_view = nil
	end

	if self.friends_receive_view then
		self.friends_receive_view:DeleteMe()
		self.friends_receive_view = nil
	end
	if self.blessing_view then
		self.blessing_view:DeleteMe()
		self.blessing_view = nil
	end
	if self.blessingTip then
		self.blessingTip:DeleteMe()
		self.blessingTip = nil
	end

	if nil ~= self.pop_blessing_alert then
		self.pop_blessing_alert:DeleteMe()
		self.pop_blessing_alert = nil
	end

	if self.kf_pvp_team_change_alert then
		self.kf_pvp_team_change_alert:DeleteMe()
		self.kf_pvp_team_change_alert = nil
	end
	if self.bind_day_pass then
		GlobalEventSystem:UnBind(self.bind_day_pass)
		self.bind_day_pass = nil
	end

	if self.enemy_record_view ~= nil then
		self.enemy_record_view:DeleteMe()
		self.enemy_record_view = nil
	end

	self.blessing_icon = nil
	SocietyWGCtrl.Instance = nil


--	捕鱼
if self.friend_view ~= nil then
	--print_error("卸载捕鱼 ctrl")
	self.friend_view:DeleteMe()
	self.friend_view = nil
end
if self.all_info_view then
	self.all_info_view:DeleteMe()
	self.all_info_view = nil
end

	if self.humo_betouch_view then
		self.humo_betouch_view:DeleteMe()
		self.humo_betouch_view = nil
	end
	if self.humo_rember_view then
		self.humo_rember_view:DeleteMe()
		self.humo_rember_view = nil
	end
-- if self.fish_info_view then
	-- self.fish_info_view:DeleteMe()
	-- self.fish_info_view = nil
-- end
end

function SocietyWGCtrl:RegisterAllProtocals()
	--好友协议注册
	self:RegisterProtocol(CSFriendInfoReq)
	self:RegisterProtocol(CSAddFriendReq)
	self:RegisterProtocol(CSAddFriendRet)
	self:RegisterProtocol(CSDeleteFriend)
	self:RegisterProtocol(CSFindRoleByName)
	self:RegisterProtocol(CSGetRandomRoleList)

	self:RegisterProtocol(SCRandomRoleListRet, "OnGetAutoFriendList")
	self:RegisterProtocol(SCFriendInfoAck, "OnGetFriendList")
	self:RegisterProtocol(SCAddFriendRoute, "OnAddFriendRoute")
	self:RegisterProtocol(SCFriendApplicantListInfo, "OnSCFriendApplicantListInfo")

	self:RegisterProtocol(SCChangeFriend, "OnChangeFriend")
	self:RegisterProtocol(SCFindRoleByNameRet, "OnFindRoleByNameRet")

	--邮件协议注册
	self:RegisterProtocol(CSMailGetList)								-- 获取邮件列表
	self:RegisterProtocol(CSMailSend)									-- 发送邮件
	self:RegisterProtocol(CSMailDelete)									-- 删除邮件
	self:RegisterProtocol(CSMailOneKeyDelete)                           -- 一键批量删除
	self:RegisterProtocol(CSMailRead)									-- 读取邮件
	self:RegisterProtocol(CSMailFetchAttachment)						-- 获取附件
	self:RegisterProtocol(CSMailOneKeyFetchAttachment)					-- 一键获取附件
	self:RegisterProtocol(CSMailOneKeyFetchAttachmentArr)               -- 一键获取多个附件  新加

	self:RegisterProtocol(SCMailListAck, "OnGetMailList")				-- 同步邮件列表
	self:RegisterProtocol(SCRecvNewMail, "OnRecvNewMail")				-- 收到新邮件
	self:RegisterProtocol(SCMailSendAck, "OnMailSendAck")				-- 发送邮件结果返回
	self:RegisterProtocol(SCMailDeleteAck, "OnMailDeleteAck")			-- 删除邮件返回
	self:RegisterProtocol(SCMailOneKeyDeleteAck,"MailOneKeyDeleteAck")  -- 批量删除已读邮件返回
	self:RegisterProtocol(SCMailDetailAck, "OnMailDetailAck")			-- 邮件详细信息
	self:RegisterProtocol(SCFetchAttachmentAck, "OnFetchAttachmentAck")	-- 提取邮件附件返回SCMailOneKeyFetchAttachmentArr
	self:RegisterProtocol(SCMailOneKeyFetchAttachmentArr, "MailOneKeyFetchAttachmentArr")	-- 提取邮件附件返回
	self:RegisterProtocol(SCHasUnReadMail, "OnHasUnReadMail")			-- 上线时是否有未读邮件通知

	--队伍协议注册
	-- self:RegisterProtocol(CSCreateTeam)
	self:RegisterProtocol(CSInviteUser)
	self:RegisterProtocol(CSInviteUserTransmitRet)
	self:RegisterProtocol(CSReqJoinTeam)
	self:RegisterProtocol(CSDismissTeam)   		--解散队伍
	self:RegisterProtocol(CSKickOutOfTeam)
	self:RegisterProtocol(CSChangeTeamLeader)
	self:RegisterProtocol(CSChangeMustCheck)
	self:RegisterProtocol(CSExitTeam)
	self:RegisterProtocol(CSTeamListReq)
	self:RegisterProtocol(CSReqJoinTeamRet)
	-- self:RegisterProtocol(CSAutoHaveTeam)
	self:RegisterProtocol(CSAutoApplyJoinTeam)
	self:RegisterProtocol(CSChangeAssignMode)
	self:RegisterProtocol(CSTeamReadyStateChangeReq)
	self:RegisterProtocol(CSReqMemChangeTeamLeader)
	self:RegisterProtocol(CSAckMemChangeTeamLeader)
	self:RegisterProtocol(CSTeamCallTogether)
	self:RegisterProtocol(CSTeamMemberShareItem)

	self:RegisterProtocol(SCOutOfTeam, "OnOutOfTeam")
	self:RegisterProtocol(SCTeamInfo, "OnTeamInfo")
	self:RegisterProtocol(SCTeamCallTogether, "OnSCTeamCallTogether")
	self:RegisterProtocol(SCTeamListAck, "OnTeamListAck")
	self:RegisterProtocol(SCReqJoinTeamTransmit, "OnReqJoinTeamTransmit") -- 通知队长有人申请加入队伍 9103
	self:RegisterProtocol(SCInviteUserTransmit, "OnInviteUserTransmit")   -- 通知被邀请 9102
	self:RegisterProtocol(SCJoinTeam, "OnJoinTeam")
	self:RegisterProtocol(SCRoleTeamInfo, "OnRoleTeamInfo")
    self:RegisterProtocol(SCReqChangeTeamLeader, "OnSCReqChangeTeamLeader")

    self:RegisterProtocol(SCNoticeRename, "OnSCNoticeRename") --名字修改 - 通知好友,仇人,情侣 1497
	self:RegisterProtocol(SCTeamMemberFollowLeaderChangeScene, "OnSCTeamMemberFollowLeaderChangeScene")
	self:RegisterProtocol(SCTeamShareItemInfo, "OnSCTeamShareItemInfo")
	self:RegisterProtocol(SCTeamShareItemUpdate, "OnSCTeamShareItemUpdate")
	self:RegisterProtocol(SCTeamMemberShareItemUse, "OnSCTeamMemberShareItemUse")

	--最近联系人列表返回
--	self:RegisterProtocol(SCConnectorInfoACK, "ConnectorInfoACK")


	--仇人协议注册
	self:RegisterProtocol(CSEnemyDelete)

	self:RegisterProtocol(SCEnemyListACK, "OnEnemyListACK")
	self:RegisterProtocol(SCChangeEnemy, "OnChangeEnemy")
	-- 添加仇人
	self:RegisterProtocol(CSAddEnemyReq)

	--祝福协议注册
	self:RegisterProtocol(CSFriendBlessOperaReq)

	self:RegisterProtocol(SCFriendBlessAllInfo, "OnFriendBlessAllInfo")
	self:RegisterProtocol(SCFriendBlessChangeInfo, "OnFriendBlessChangeInfo")
	self:RegisterProtocol(SCFriendBlessInviteBless, "OnFriendBlessInviteBless")



	--好友祝福(升级祝福)
	--self:RegisterProtocol(CSFriendblessOperate)
	--self:RegisterProtocol(SCFriendblessNotice, "OnFriendblessNotice")

	--被击杀
	self:RegisterProtocol(SCNotifyBeKillInfo, "OnSCNotifyBeKillInfo")

	--捕鱼
	self:RegisterProtocol(CSFishPoolRaiseReq)
	self:RegisterProtocol(CSFishPoolBuyBulletReq)
	self:RegisterProtocol(CSFishPoolQueryReq)
	self:RegisterProtocol(CSFishPoolStealFish)
	self:RegisterProtocol(CSFishPoolHarvest)
	self:RegisterProtocol(CSFishPoolExtendCapacity)
	self:RegisterProtocol(CSBuyFishBoot)

	self:RegisterProtocol(SCFishPoolAllInfo, "OnFishPoolAllInfo")
	self:RegisterProtocol(SCFishPoolAllRaiseInfo, "OnFishPoolAllRaiseInfo")
	self:RegisterProtocol(SCFishPoolCommonInfo, "OnFishPoolCommonInfo")
	self:RegisterProtocol(SCFishPoolRaiseInfoChange, "OnFishPoolRaiseInfoChange")
	self:RegisterProtocol(SCFishPoolFriendsGeneralInfo, "OnFishPoolFriendsGeneralInfo")
	self:RegisterProtocol(SCFishPoolGuildMemberGeneralInfo, "OnFishPoolGuildMemberGeneralInfo")
	self:RegisterProtocol(SCFishPoolStolenMemberGeneralInfo, "OnFishPoolStolenMemberGeneralInfo")
	self:RegisterProtocol(SCFishPoolEachOtherMemberGeneralInfo, "OnFishPoolEachOtherMemberGeneralInfo")
	--师徒上下线提醒

	self:RegisterProtocol(SCShituOnlineInfoBack, "SCShituOnlineInfoBack")

	--赠送界面
	self:RegisterProtocol(CSGivePresent)  --赠送
	self:RegisterProtocol(CSGivePresentHistory) --请求赠送记录
	self:RegisterProtocol(SCGivePresentHistory,"GivePresentHistory") --赠送记录返回

	--虎摸信息
	self:RegisterProtocol(CSTouchOperReq) --虎摸请求
	self:RegisterProtocol(SCFriendTouchBaseInfo,"OnSCFriendTouchBaseInfo") --虎摸基础信息
	self:RegisterProtocol(SCTouchRecordInfo,"OnSCTouchRecordInfo") --虎摸记录信息
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	--日期改变监听
	self.bind_day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.DayChange, self))

	--移除好友列表
	self:RegisterProtocol(SCFriendApplicantListRemove, "OnSCFriendApplicantListRemove")

	--今日不在接受该玩家请求
	self:RegisterProtocol(CSNotReceiveApplicationFlag)
	self:RegisterProtocol(SCNotReceiveApplicationFlagRet, "OnSCNotReceiveApplicationFlagRet")



	--------------------------新仇人协议-------------------------------------
	self:RegisterProtocol(CSCrossFriendReq) -- 仇人操作请求
	self:RegisterProtocol(SCCrossEnemyList,"OnSCCrossEnemyList") --新的仇人列表
	self:RegisterProtocol(SCCrossEnemyChange,"OnSCCrossEnemyChange") --新的仇人信息变更
	self:RegisterProtocol(SCCrossKillMeInfo,"OnSCCrossKillMeInfo") --杀我的人列表
end

function SocietyWGCtrl:SCShituOnlineInfoBack(protocol)
--	print_error("返回",protocol)
	if self.society_view:IsOpen() then
		--print_error("请求",protocol)
		MasterWGCtrl.Instance:SendShituInfo()
	end
end

--最近联系人列表返回
function SocietyWGCtrl:ConnectorInfoACK(protocol)
	self.data_mgr:SetConnectorInfo(protocol)
end

function SocietyWGCtrl:OpenAddBlacklistView()
	self.add_blacklist_view:Open()
end

function SocietyWGCtrl:OnRecvMainRoleInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailGetList)
	send_protocol:EncodeAndSend()
end

--好友相关的协议事件
function SocietyWGCtrl:OnGetFriendList(protocol)
	-- print_error("OnGetFriendList  获得好友列表------------",protocol)
	local friend_list = {}

	for i = 1, protocol.count do
		friend_list[i] = protocol.friend_list[i]
		AvatarManager.Instance:SetAvatarKey(friend_list[i].user_id, friend_list[i].avatar_key_big, friend_list[i].avatar_key_small)
	end
	self.data_mgr:SetFriendList(friend_list, protocol.count)
	self:Flush("friend_list")
	RemindManager.Instance:Fire(RemindName.SocietyFriends)
	RemindManager.Instance:Fire(RemindName.SocietyFriends2)
	MainuiWGCtrl.Instance:SetFriendRemind()

	ViewManager.Instance:FlushView(GuideModuleName.NewTeamInviteView)
end

-- 随机在线玩家列表
function SocietyWGCtrl:OnGetAutoFriendList(protocol)
	local random_role_list = {}
	for i = 1, protocol.count do
		random_role_list[i] = protocol.auto_addfriend_list[i]
		AvatarManager.Instance:SetAvatarKey(random_role_list[i].user_id, random_role_list[i].avatar_key_big, random_role_list[i].avatar_key_small)
	end
	self.data_mgr:SetAutoaddfriendList(random_role_list)
	self:Flush("auto_addfriend")

	self:OpenAutoAddFriendView()
end

--收到添加好友请求
function SocietyWGCtrl:OnAddFriendRoute(protocol)
	if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.FRIEND_REQUEST) then	-- 拒绝好友邀请
		local user_info = {}
		user_info.user_id = protocol.req_user_id
		user_info.req_gamename = protocol.req_gamename
		user_info.req_sex = protocol.req_sex
		user_info.req_prof = protocol.req_prof
		user_info.req_role_vip_level = protocol.req_role_vip_level
		self:SendAddFriendRet(user_info, 0)
		return
	end

	local req_info = {}
	req_info.user_id = protocol.req_user_id
	req_info.req_gamename = protocol.req_gamename
	req_info.req_avatar = protocol.req_avatar
	req_info.req_sex = protocol.req_sex
	req_info.req_prof = protocol.req_prof
	req_info.req_camp = protocol.req_camp
	req_info.req_level = protocol.req_level
	req_info.req_role_vip_level = protocol.req_role_vip_level

	AvatarManager.Instance:SetAvatarKey(protocol.req_user_id, protocol.avatar_key_big, protocol.avatar_key_small)

	self.data_mgr:AddFriendToReqtList(req_info)
	self:Flush("friend_req_list")
	--self:OpenReqView()
	-- local icon = MainuiWGCtrl.Instance:GetTipIcon(MAINUI_TIP_TYPE.FRIEND)
	-- if nil == icon or not icon:IsVisible() or self.data_mgr:GetReqFriendListSize() == 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND, self.data_mgr:GetReqFriendListSize(), function ()
			self:OpenReqView()
			return true
		--MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.FRIEND)
		end)
	-- end
	MainuiWGCtrl.Instance:SetFriendRemind()
	RemindManager.Instance:Fire(RemindName.SocietyFriends)
	RemindManager.Instance:Fire(RemindName.SocietyFriends2)
end

--收到添加好友请求列表
function SocietyWGCtrl:OnSCFriendApplicantListInfo(protocol)
	self.data_mgr:AddFriendToReqtList(protocol.applicant_count_list)
	self.data_mgr:AddLYLFriendToReqtList(protocol.lyl_applicant_list)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND, self.data_mgr:GetReqFriendListSize(), function ()
		self:OpenReqView()
		return true
	end)

	self:InvateFriendLiaoYiLiaoTip(protocol)

	self.add_req_view:Flush()
	self:Flush("friend_list")
	MainuiWGCtrl.Instance:SetFriendRemind()
	RemindManager.Instance:Fire(RemindName.SocietyFriends)
	RemindManager.Instance:Fire(RemindName.SocietyFriends2)
end

function SocietyWGCtrl:InvateFriendLiaoYiLiaoTip(protocol)
	if self.society_view:IsOpen() then
		--MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO)
		SocietyWGData.Instance:ClearLYLReqFriendList()
		return
	end
	
	-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO, self.data_mgr:GetLYLReqFriendListSize(), function()
	-- 	-- SocietyWGCtrl.Instance:OpenFriendChatView()
	-- 	local liaoyiliao_req_list = SocietyWGData.Instance:GetLYLReqFriendList()
	-- 	local info = liaoyiliao_req_list[1]
	-- 	local lyl_role_id = info.user_id
	-- 	local new_lyl_role_id = SocietyWGData.Instance:GetNewLiaoYiLiaoUserId()
	-- 	if new_lyl_role_id ~= 0 then
	-- 		for k,v in pairs(liaoyiliao_req_list) do
	-- 			if new_lyl_role_id == v.user_id then
	-- 				lyl_role_id = v.user_id
	-- 			end
	-- 		end
	-- 	end
	-- 	SocietyWGCtrl.Instance:OpenFriendViewFromLiaoYiLiao(lyl_role_id,true)
	-- 	--MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO)
	-- 	SocietyWGData.Instance:ClearLYLReqFriendList()
	-- end)
end

--收到好友改变
function SocietyWGCtrl:OnChangeFriend(protocol)
	local friend_info = __TableCopy(protocol.friend_info)
	if 1 == protocol.changestate then
		self.data_mgr:RemoveFdFormFdList(friend_info.user_id)
		ChatWGData.Instance:RemPrivateUnreadMsg(friend_info.user_id)
		MainuiWGCtrl.Instance:SetChatPrivateUnreadMsgHead()
		self:SetFriendChatMainUIViewIcon()
		self:FlushSocietyView(SocietyView.Tab_F, "delete_friend", {uid = friend_info.user_id})
    else
        local old_info = self.data_mgr:FindFriend(friend_info.user_id)
        if not IsEmptyTable(old_info) then
            if old_info.intimacy < friend_info.intimacy then
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.IntimacyAdd, friend_info.intimacy - old_info.intimacy ))
            end
        end
        --当熊抱可领取时 重新设置缓存时间
        if old_info and old_info.touch_me_total_count < friend_info.touch_me_total_count then
        	SocietyWGData.Instance:SetPlayerprefsinfo(friend_info.user_id,true)
        end
		self.data_mgr:AddFriendToFdList(friend_info)
		AvatarManager.Instance:SetAvatarKey(friend_info.user_id, friend_info.avatar_key_big, friend_info.avatar_key_small)
	end
	self:Flush("friend_list")

	if MarryWGCtrl.Instance.friend_view:IsOpen() then
		MarryWGCtrl.Instance.friend_view:Flush()
	end

	MarryWGCtrl.Instance:FlushFriendQinMi()
	self:FlushSendGoodsView()
	RemindManager.Instance:Fire(RemindName.SocietyFriends)
	RemindManager.Instance:Fire(RemindName.SocietyFriends2)
	MainuiWGCtrl.Instance:SetFriendRemind()

	if FlowerWGCtrl.Instance.send_flower_view:IsOpen() then
		FlowerWGCtrl.Instance.send_flower_view:Flush()
	end
end

--通过名字查找信息
function SocietyWGCtrl:OnFindRoleByNameRet(protocol)
	if protocol.role_id == 0 then
		if nil ~= self.callback_list[protocol.msg_identify] then
			self.callback_list[protocol.msg_identify](0)
		end
		return
	end
	if nil ~= self.callback_list[protocol.msg_identify] then
		self.callback_list[protocol.msg_identify](1, protocol)
		AvatarManager.Instance:SetAvatarKey(protocol.role_id, protocol.avatar_key_big, protocol.avatar_key_small)
	end
end

--邮件相关协议收

--获取邮件列表
function SocietyWGCtrl:OnGetMailList(protocol)
	for i = 1, protocol.count do
		local mail = protocol.mails[i]
		--print_error(mail)
		if nil == mail then
			ErrorLog("邮件列表数量不符合")
		end

		local mail_info = mail
		if nil ~= mail_info then
			self.data_mgr:AddMail(mail_info)
		end
	end

	self:Flush("mail_list")
	-- print_error("OnGetMailList获取邮件列表------------- ",self.data_mgr:GetMailRemind(),protocol.count)

	self:SetMailMainUIViewIcon()
	self:SetMailMarketAuctionIcon()
end

function SocietyWGCtrl:SetMailMainUIViewIcon()
	RemindManager.Instance:Fire(RemindName.ScoietyMail)
	if self.data_mgr:GetMailRemind() == 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, 0)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, self.data_mgr:GetMailRemind(), function ()  return self:OpenMailView() end)
	end
	--self:SetFriendChatMainUIViewIcon()
end

function SocietyWGCtrl:SetMailMarketAuctionIcon()
	local data = SocietyWGData.Instance:GetMailList()
	local need_tip = false
	local jump_index = 1
	for k, v in pairs(data) do
		if v.mail_status.kind == 5 and v.mail_status.is_read == 0 then
			need_tip = true
			jump_index = k
			break
		end
	end

	if need_tip then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MARKET_AUCTOIN_SUS, 1, function ()
			self.society_view:Open(SocietyView.Tab_M)
			self:SelectMailListIndexByItem(jump_index)
			end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MARKET_AUCTOIN_SUS, 0)
	end
end

function SocietyWGCtrl:SetFriendChatMainUIViewIcon()
	local chat_list = ChatWGData.Instance:GetPrivateUnreadList()
	if #chat_list > 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND_CHAT, #chat_list, function ()
				SocietyWGCtrl.Instance:OpenFriendChatView() end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND_CHAT, 0)
	end
end

--新邮件
function SocietyWGCtrl:OnRecvNewMail(protocol)
	-- print_error("OnRecvNewMail新邮件----------".. #protocol.mail_brief,protocol.mail_brief)
	local mail_info = protocol.mail_brief
	SocietyWGData.Instance:GetNewMail(mail_info)
	-- local icon = MainuiWGCtrl.Instance:GetTipIcon(MAINUI_TIP_TYPE.MAIL)
	-- if nil == icon or (not icon:IsVisible()) then
	-- 	if mail_info.mail_status.is_read == 0  then
	-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, 1, function () self:OpenMailView() end)
	-- 	end
	-- end
	-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, 1, function ()
	--  self:OpenMailView()
	--  --MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.MAIL)
	--   end).
	--table.insert(self.data_mgr:GetMailList(), mail_info)
	self:Flush("mail_list")
	self:SetMailMainUIViewIcon()
	self:SetMailMarketAuctionIcon()
end

--发送邮件返回
function SocietyWGCtrl:OnMailSendAck(protocol)
	-- print_error("OnMailSendAck发送邮件返回----- "..protocol.ret)
	if 0 == protocol.ret then
		if nil ~= Language.Society["MailSendSuccess"] then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society["MailSendSuccess"])
		end
	end
end

function SocietyWGCtrl:SetDelMailCount(count)
	self.del_mail_count = count
	self.del_mail_temp_count = 0
end


--删除邮件返回
function SocietyWGCtrl:OnMailDeleteAck(protocol)
	--print_error("删除邮件返回     ",protocol)
	--self.del_mail_temp_count = self.del_mail_temp_count + 1
	if 0 == protocol.ret then
		--print_error("删除邮件返回     "..protocol.mail_index)
		self.data_mgr:RemoveMail(protocol.mail_index)
		if self.data_mgr:GetMailRemind() == 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, 0)
		end

	end
	--if self.del_mail_count == self.del_mail_temp_count then
		self:Flush("mail_list")
	--end
	RemindManager.Instance:Fire(RemindName.ScoietyMail)
end

function SocietyWGCtrl:MailOneKeyDeleteAck(protocol)
	if protocol.ret == -1 then
		-- print_error("批量删除邮件失败 protocol.ret",protocol.ret,protocol)
		return
	end

	for k,v in pairs(protocol.mail_index_array) do
		self.data_mgr:RemoveMail(protocol.mail_index_array[k])
	end

	if self.society_view:IsMailViewOpen() then
		self.society_view:FlushMailView(-1)
	end
    SocietyWGData.Instance:SetHightmailItem (nil)
    RemindManager.Instance:Fire(RemindName.ScoietyMail)
end

--邮件详细信息
function SocietyWGCtrl:OnMailDetailAck(protocol)
	-- print_error("OnMailDetailAck-------右边查看邮件", protocol)
	self.society_view:SetMailDetailMsg(protocol)
	self:SetMailMarketAuctionIcon()
end

--提取邮件附件返回
function SocietyWGCtrl:OnFetchAttachmentAck(protocol)
	--print_error("提取邮件附件返回1    "..protocol.mail_index)
	self.data_mgr:SetMailNotHavdFujian(protocol.mail_index)
	if self.data_mgr:GetMailRemind() == 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, 0)
	end
	self:Flush("mail_list")
	RemindManager.Instance:Fire(RemindName.ScoietyMail)
end

function SocietyWGCtrl:MailOneKeyFetchAttachmentArr(protocol)
	--print_error("提取邮件附件返回2", protocol.ret)
	if protocol.mail_index_array then
		for k,v in pairs( protocol.mail_index_array) do
			self.data_mgr:SetMailNotHavdFujian(v)
		end
	end

	if self.data_mgr:GetMailRemind() == 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, 0)
	end
	RemindManager.Instance:Fire(RemindName.ScoietyMail)

	if protocol.ret == -1 then
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(protocol.bag_type)  --背包已满
		--SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoSelect1)
		return
	elseif protocol.ret == -2 then
		return
	elseif protocol.ret == 0 then
		SocietyWGData.Instance:SetAllSelectMailNoFuJian(protocol.mail_index_array)
	else
		SocietyWGData.Instance:SetAllSelectMailNoFuJian(protocol.mail_index_array)
		if protocol.ret ~= -3 then
			RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(protocol.bag_type) --背包已满
		end
	end

	if self.society_view:IsMailViewOpen() then
		self.society_view:FlushMailView(2)
	end
end

-- 上线时未读邮件通知
function SocietyWGCtrl:OnHasUnReadMail(protocol)
	self.data_mgr:SetHasNoReadMail(true, protocol.unread_num)
	RemindManager.Instance:Fire(RemindName.ScoietyMail)
end

--获取附近队伍信息
function SocietyWGCtrl:SendTeamListReq(team_type, fb_mode)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSTeamListReq)
	send_protocol.team_type = team_type
	send_protocol.teamfb_mode = fb_mode
	send_protocol:EncodeAndSend()
end

--请求加入队伍
function SocietyWGCtrl:SendReqJoinTeam(team_index, is_fenshen)
		--护送拦截
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
    end
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_JOIN, team_index)
    else
        local send_protocol = ProtocolPool.Instance:GetProtocol(CSReqJoinTeam)
	    send_protocol.team_index = team_index
	    send_protocol:EncodeAndSend()
    end
end

--队伍协议接收事件

--队员离开队伍
function SocietyWGCtrl:OnOutOfTeam(protocol)
	local origin_role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local who = ""
	if (origin_role_id == protocol.user_id and
		protocol.reason == OUT_OF_TEAM_REASON.OFT_KICK_OFF) or
		(origin_role_id == protocol.user_id and
		protocol.reason == OUT_OF_TEAM_REASON.OFT_SECEDE) or
		protocol.reason == OUT_OF_TEAM_REASON.OFT_DISMISS then
		self.data_mgr:ClearTeamData()
		self.data_mgr:TeamJoinReqClear()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)

		NewTeamWGData.Instance:ClearHeBing()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)

		who = Language.Society.You
		NewTeamWGCtrl.Instance:CloseBaoMingEnterView()
		NewTeamWGCtrl.Instance:CloseInviteView()
		local role = Scene.Instance:GetMainRole()
		role:SetAttr("is_team_leader", 0)

		if SocietyWGData.Instance:GetIsInTeam() == 0 then
	        local scene_logic = Scene.Instance:GetSceneLogic()
	        if scene_logic ~= nil then
	            scene_logic:OnlyResetTeamTrackRoleInfo()
	        end
		end

		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil and not main_role:IsDeleted() then
			main_role:CheckFollowState(false)
		end
	else
		who = protocol.user_name
	end

	self:UpadateMemberHpBar(protocol.origin_role_id)
	--print_error("SocietyWGCtrl:OnOutOfTeam-->>队员离开队伍" , who, GameVoManager.Instance:GetMainRoleVo().role_id,  protocol.user_id, protocol.reason, protocol)
	local text_format = ""
	if OUT_OF_TEAM_REASON.OFT_DISMISS == protocol.reason then
		text_format = Language.Society.JieSanTeam
	elseif OUT_OF_TEAM_REASON.OFT_KICK_OFF ==  protocol.reason then
		text_format = Language.Society.TiChuTeam
	else
		text_format = Language.Society.LeaveTeam
	end

	if OUT_OF_TEAM_REASON.OFT_SECEDE == protocol.reason and protocol.new_leader_uid > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.LeaveTeamNewLeader, who, protocol.new_leader_name))
	else
		--合并不弹提示，服务器弹
		if OUT_OF_TEAM_REASON.OFT_HEBING ~= protocol.reason then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(text_format, who))
		end

	end
	--
	-- self.society_view:Flush(SocietyView.Tab_T, "out_of_team", {is_clear = false, protocol = protocol})

	-- 离队的时候(仙侣)刷新情缘的永恒之路队伍信息。
	if protocol.user_id == RoleWGData.Instance.role_vo.lover_uid then
		MarryWGCtrl.Instance:LeaveTeam()
	end
	if MasterWGData.Instance:IsMaster(protocol.user_id) or MasterWGData.Instance:IsApprentice(protocol.user_id) then
		MasterWGCtrl.Instance:LeaveTeam()
	end
	-- GlobalEventSystem:Fire(TeamInfoQuery.TEAM_INFO_BACK)
	GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)

	--退出队伍的时候, 如果正在平台页面，请求一下当前列表
	if NewTeamWGCtrl.Instance:CheckIsOpenPingTaiIndex() then
		local team_type, team_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
		SocietyWGCtrl.Instance:SendTeamListReq(team_type, team_mode)
	end
	if NewTeamWGCtrl.Instance:CheckIsOpenNearIndex() then
		SocietyWGCtrl.Instance:SendTeamListReq(-1, -1)
	end

	-- if NewTeamWGData.Instance:GetIsMatching() then
		--退出队伍的时候， 设置匹配状态
		--local operate = NewTeamWGData.Instance:GetIsMatching() and 1 or 0
		--print_error("请求设置匹配状态 >>>>>>>>> ", operate, team_type, team_mode )
		--NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, team_type, team_mode )
		--NewTeamWGCtrl.Instance:SendSCTeamMatchStateReq()
	-- end
	if protocol.user_id == RoleWGData.Instance:GetMainRoleId() then
		NewTeamWGCtrl.Instance:SwitchView(TabIndex.team_pingtai)
	else
		NewTeamWGCtrl.Instance:Flush(TabIndex.team_my_team)
	end
end

function SocietyWGCtrl:ClearTeamInfo()
	self.data_mgr:ClearTeamData()
	self:FlushSocietyView(SocietyView.Tab_T, "out_of_team", {is_clear = true})
	NewTeamWGData.Instance:ClearTeamInfo()
	GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)
end

function SocietyWGCtrl:OpenSocietyViewMore(index)
	if self.society_view:IsOpen() then
		self.society_view:OperateActive(index)
	end
end

--收到队伍信息
function SocietyWGCtrl:OnTeamInfo(protocol)
	-- print_error("收到队伍信息 >>>>>> ", protocol.team_member_list)
	-- Log("SocietyWGCtrl:OnTeamInfo--=======================================>>收到队伍信息")
	local team_tag = SocietyWGData.Instance:GetIsInTeam() --记录上一次是否有组队
	--如果有队伍，判断上一次的数量与现在的数量的差 是否大于 0
	--如果大于0，说明有人加入了队伍
	local old_member_list = SocietyWGData.Instance:GetTeamMemberList()
	self.data_mgr:SetTeamData(protocol)
	local new_member_list = SocietyWGData.Instance:GetTeamMemberList()

	if SocietyWGData.Instance:GetIsInTeam() == 0 then
		--BootyBayWGData.Instance:SetIsNeedCheckTeamPos(false)
		--TeamFollowData.Instance:ResetTeamPosList()
	end

	local check_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	if check_role_id ~= nil then
		local check_flag = false
		for k,v in pairs(old_member_list) do
			if v.role_id == check_role_id then
				check_flag = true
				break
			end
		end

		if not check_flag then
			for k,v in pairs(new_member_list) do
				if v.role_id == check_role_id then
					MainuiWGCtrl.Instance:StopAutoRevenge(true, Language.Fight.RevengeObjAlly, v.role_id)
					break
				end
			end
		end
	end

	ViewManager.Instance:FlushView(GuideModuleName.KF3V3MatchView)

	local new_member_list = SocietyWGData.Instance:GetTeamMemberList()
	local match_info = KuafuPVPWGData.Instance:GetMatchStateInfo()
	local is_matching = match_info.matching_state >= 0 and match_info.matching_state ~= 3
	local leader_is_change = false

    local old_leader_uid
    for k,v in pairs(old_member_list) do
        if v.is_leader == 1 then
            old_leader_uid = v.role_id
            break
        end
    end

	if team_tag == 1 then
		local role_vo = RoleWGData.Instance:GetRoleVo()
		for k,v in pairs(new_member_list) do
			if v.is_leader == 1 then
				if v.role_id ~= old_leader_uid and v.role_id == role_vo.role_id then
					local content
					local name = v.name
					local level = v.level
					content = string.format(Language.NewTeam.LeaderChange2, name, RoleWGData.GetLevelString(level))
					local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
					--在组队做特殊处理 避免频繁刷 聊天功能90级开放 提示
					if main_role_vo.level >= COMMON_CONSTS.CHAT_LEVEL_LIMIT then
		      			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1, nil, true)
					end
	      			leader_is_change = true
	      		else
	      			if v.role_id ~= old_leader_uid then
	      				leader_is_change = true
	      			end
				end
			end
		end
	end

	if team_tag == 1 and (#new_member_list-#old_member_list) >0 then
		local role_vo = RoleWGData.Instance:GetRoleVo()
		for k,v in pairs(new_member_list) do
			if role_vo.role_id == v.role_id then
				if new_member_list[#new_member_list] then
			        local name = new_member_list[#new_member_list].name
			        local level = new_member_list[#new_member_list].level

					-- 不发协议走聊天，直接插入数据
					local msg_info = ChatWGData.CreateMsgInfo()
					msg_info.channel_type = CHANNEL_TYPE.TEAM
					msg_info.content = string.format(Language.NewTeam.JoinTeamMsg, name, RoleWGData.GetLevelString(level))
					msg_info.msg_reason = CHAT_MSG_RESSON.TEAM_TIPS
					msg_info.is_add_team = true
					msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
					ChatWGCtrl.Instance:AddChannelMsg(msg_info, true)
				end
			end
		end

	elseif team_tag == 0 then
		if is_matching then
			if SocietyWGData.Instance:GetIsTeamLeader() == 0 then
				KuafuPVPWGCtrl.Instance:SendCrossMultiuerChallengeCancelMatching()
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.CancelMatchForTeam)
			end
		end
	end
	--if is_matching and (#new_member_list - #old_member_list) > 0 then
	--	if team_tag ~= 0 and #new_member_list ~= 1 then
	--		if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
	--			if not self.kf_pvp_team_change_alert then
	--				self.kf_pvp_team_change_alert = Alert.New()
	--				self.kf_pvp_team_change_alert:SetLableString(Language.Field1v1.ReMatchForTeam)
	--				self.kf_pvp_team_change_alert:SetOkFunc(function()
	--					KuafuPVPWGCtrl.Instance:SendCrossMultiuerChallengeCancelMatching()
	--					KuafuPVPWGCtrl.Instance:AddTeamMatchCallBack(function()
	--						KuafuPVPWGCtrl.Instance:SendCrossMultiuserChallengeMatchgingReq()
	--					end)
	--				end)
	--			end
	--			self.kf_pvp_team_change_alert:Open()
	--		end
	--	end
	--end

	--print_error("收到队伍信息",new_member_list)
	for k, v in pairs(protocol.team_member_list) do
		AvatarManager.Instance:SetAvatarKey(v.role_id, v.avatar_key_big, v.avatar_key_small)
		-- 组队是仙伴(仙侣)刷新情缘的永恒之路队伍信息。
		if v.role_id == RoleWGData.Instance.role_vo.lover_uid then
			MarryWGCtrl.Instance:LoverJoinTeam()
		end
		-- if MasterWGData.Instance:IsMaster(v.role_id) or MasterWGData.Instance:IsApprentice(v.role_id) then
		-- 	MasterWGCtrl.Instance:ShituJoinTeam()
		-- end
	end

	--新创建队伍逻辑
	if self.data_mgr:GetIsInTeam() and self.data_mgr:GetIsTeamLeader() and self.data_mgr:GetTeamMemberCount() == 1 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
	end

	self:Flush("team_list")
	NewTeamWGData.Instance:SetData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team)
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "PingTaiFlushList", { PingTaiFlushList = true})
--	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_near, "NearFlushList", {NearFlushList = true})
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamBaoMingEnterView)
	ViewManager.Instance:FlushView(GuideModuleName.BootyBayFBReadyView)
	NewTeamWGCtrl.Instance:ForceFlushInviteView()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ZHUSHENTA_FB then
		SocietyWGCtrl.Instance:SendTeamReadyStateChange(1)
	else
		local state = MainuiWGCtrl.Instance:GetRoleAutoXunluState()
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, state)
	end

	if team_tag == 0 and TEAM_TYPE.MIGONGXIANFU == protocol.team_type then
		FunOpen.Instance:OpenViewByName(GuideModuleName.TransferProfTeam)
	end
	-- DailyWGData.UpdataTeamFbTipIcon()
	GlobalEventSystem:Fire(TeamInfoQuery.TEAM_INFO_BACK)
	GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)

	MainuiWGCtrl.Instance:FlushMatchStateInfo()

	self:UpadateMemberHpBar()

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and not main_role:IsDeleted() then
		main_role:CheckFollowState(leader_is_change)
	end

	NewTeamWGCtrl.Instance:ClearQuickInviteTeamList()
	MainuiWGCtrl.Instance:FlushXunLuStates()

	NewTeamWGData.Instance:UpdataTeamShareItemInfo()
	MainuiWGCtrl.Instance:RefreshBossItemCellsData()
end

function SocietyWGCtrl:UpadateMemberHpBar(user_id)
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		if not v:IsDead() and not v:IsDeleted() and not v:IsMainRole() then
			local vo = v:GetVo()
			local is_friend = SocietyWGData.Instance:GetTargetIsTeamMember(vo.origin_uid)
			if is_friend or vo.origin_uid ~= user_id then
				local follow_ui = v:GetFollowUi()
				follow_ui:GetHpBar():UpdateProgress()
			end
		end
	end
end

--收附近队伍信息
function SocietyWGCtrl:OnTeamListAck(protocol)
	self.data_mgr:SetNearTeamData(protocol)
	--for k, v in pairs(protocol.team_list) do
	--	AvatarManager.Instance:SetAvatarKey(v.member_uid_list[1], v.avatar_key_big, v.avatar_key_small)
	--end
	self:Flush("team_near_list")

	NewTeamWGData.Instance:SetNearTeamList(protocol)
	NewTeamWGCtrl.Instance.quick_view:Flush()
	--print_error("SocietyWGCtrl 刷新 >>>>>>> 平台", protocol)
	NewTeamWGCtrl.Instance.m_view:Flush(TabIndex.team_pingtai, "PingTaiFlushList", { PingTaiFlushList = true})
--	NewTeamWGCtrl.Instance.m_view:Flush(TabIndex.team_near, "NearFlushList", {NearFlushList = true})
end

--收到请求加入队伍
function SocietyWGCtrl:OnReqJoinTeamTransmit(protocol)
	self.data_mgr:TeamAddJoinReq(protocol)
	AvatarManager.Instance:SetAvatarKey(protocol.req_role_id, protocol.avatar_key_big, protocol.avatar_key_small)

	self:Flush("team_req_list")
	local num = self.data_mgr:GetReqTeamListSize() > 0 and 1 or 0
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, num, function ()
		--NewTeamWGCtrl.Instance.apply_view:Open()
		--ViewManager.Instance:Open(GuideModuleName.NewTeamView)
		NewTeamWGCtrl.Instance:OpenApplyView()
		return true
	end)
	MainuiWGCtrl.Instance:ReloadTeamList()
	NewTeamWGCtrl.Instance.apply_view:Flush()
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView)
end

--收到邀请加入队伍
function SocietyWGCtrl:OnInviteUserTransmit(protocol)
	-- 2022.5.27 拒绝所有人组队邀请（原 拒绝陌生人）
	if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.REFUSE_STRANGER_TEAM) then
		-- local is_friend = SocietyWGData.Instance:CheckIsFriend(protocol.inviter)
		-- if not is_friend then
			return
		-- end
	end

	self.data_mgr:TeamAddInviteReq(protocol)
	AvatarManager.Instance:SetAvatarKey(protocol.inviter, protocol.avatar_key_big, protocol.avatar_key_small)

	self:Flush("team_invite_list")
	-- local num = self.data_mgr:GetInviteListSize() > 0 and 1 or 0
	--print_error("我收到了邀请加入队伍")
	--MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, num, function ()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, self.data_mgr:GetInviteListSize(), function ()
		--护送拦截
		if YunbiaoWGData.Instance:GetIsHuShong() then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
			return true
		end

		self.team_invite_list_view:Open()
		return true
	end)

	--快速组队邀请
	NewTeamWGData.Instance:GetQuickInviteFromUserTransmit(protocol)
end

function SocietyWGCtrl:DeleteReq(role_id)
	if self.team_invite_list_view:IsOpen() then
		self.team_invite_list_view:DeleteReq(role_id)
	end
end

function SocietyWGCtrl:GetTodayCheckActive()
	return self.team_invite_list_view:GetTodayCheckActive()
end

function SocietyWGCtrl:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
	self.team_invite_list_view:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
end

-- 通知玩家加入了队伍
function SocietyWGCtrl:OnJoinTeam(protocol)
	--self.society_view:JoinTeamMsg(protocol)
	self:JoinTeamMsg(protocol)
	-- print_error("QueryRoleInfo")
	-- NewTeamWGCtrl.Instance:QueryRoleInfo(protocol.user_id)
	GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)
end
function SocietyWGCtrl:JoinTeamMsg(protocol)
	if GameVoManager.Instance:GetMainRoleVo().role_id == protocol.user_id then
		--合并加入的队伍不弹提示
		if protocol.reason ~= NotifyJoinInfoReason.HeBingJoinTeam then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.MyselfJoinTeamTis)
		end

		--跨服邀请进队，服务器拉进队伍的，不打开界面
		if protocol.notify_reason and protocol.notify_reason ~= NotifyJoinInfoReason.CrossInviteAutoAddMember then
			--关闭当前界面
			--ViewManager.Instance:Close(GuideModuleName.NewTeamQuickView)
			ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
	else
		if protocol.reason ~= NotifyJoinInfoReason.HeBingJoinTeam then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.JoinTeam, protocol.user_name))
		end

		if NewTeamWGCtrl.Instance:CheckIsOpenPingTaiIndex() then
			local team_type, team_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
			SocietyWGCtrl.Instance:SendTeamListReq(team_type, team_mode)
		end
		if NewTeamWGCtrl.Instance:CheckIsOpenNearIndex() then
			SocietyWGCtrl.Instance:SendTeamListReq(-1, -1)
		end
	end
end

-- 玩家自己的队伍相关信息
function SocietyWGCtrl:OnRoleTeamInfo(protocol)
	self.data_mgr:SetAutoJoinTeam(protocol.is_auto_apply_join_team)  --是否答应自动加入队伍
	self:Flush("team_list", {auto_join = true})
	--do something
end

function SocietyWGCtrl:OnSCNoticeRename(protocol)

    local list = self.data_mgr:GetFriendList()
    for k, v in pairs(list) do
        if v.user_id == protocol.role_id then
            v.gamename = protocol.new_name
        end
    end

    local list2 = self.data_mgr:GetEnemyList()
    for k, v in pairs(list2) do
        if v.user_id == protocol.role_id then
            v.gamename = protocol.new_name
        end
    end
    self:Flush("friend_list")
end

function SocietyWGCtrl:OnSCReqChangeTeamLeader(protocol)
	local desc = string.format(Language.Society.ApplyToBeTeamLeader, protocol.role_name)
	local ok_func = function ()
		self:SendAckMemChangeTeamLeader(protocol.role_id, 1)
	end
	local cancel_func = function ()
		self:SendAckMemChangeTeamLeader(protocol.role_id, 0)
	end
	TipWGCtrl.Instance:OpenAlertTips(desc, ok_func, cancel_func, nil, cancel_func, nil, nil, "同意", "拒绝")
end

-- -- 删除仇人
-- function SocietyWGCtrl:DeleteEnemy(role_id)
-- 	local send_protocol = ProtocolPool.Instance:GetProtocol(CSEnemyDelete)
-- 	send_protocol.user_id = role_id
-- 	send_protocol:EncodeAndSend()
-- end

-- -- 添加仇人
-- function SocietyWGCtrl:SendAddEnemy(role_id)
-- 	local send_protocol = ProtocolPool.Instance:GetProtocol(CSAddEnemyReq)
-- 	send_protocol.uid = role_id
-- 	send_protocol:EncodeAndSend()
-- end

--获取仇人列表
function SocietyWGCtrl:OnEnemyListACK(protocol)
	-- self.data_mgr:SetEnemyData(protocol)
	-- for k, v in pairs(protocol.enemy_list) do
	-- 	AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
	-- end
	-- self:Flush("enemy_list")
end

--仇人信息变化
function SocietyWGCtrl:OnChangeEnemy(protocol)
	-- Log("仇人信息变化")
	-- self.data_mgr:UpdateEnemyData(protocol)
	-- local enemy_info = protocol.enemy_info
	-- AvatarManager.Instance:SetAvatarKey(enemy_info.user_id, enemy_info.avatar_key_big, enemy_info.avatar_key_small)

	-- self:Flush("friend_list")
	--self:Flush("enemy_list")
end

--刷新不同的view
function SocietyWGCtrl:Flush(type, value)
	if nil == type then
		return
	end

	if "friend_list" == type then
		self:FlushSocietyView(SocietyView.Tab_F, "friend_list", value)
		self.friend_list_view:Flush()
		return
	end

	if "find_role_id" == type then
		if not self.society_view:IsOpen() then
			self.society_view:Open(SocietyView.Tab_F)
		end
		self.society_view:Flush(SocietyView.Tab_F, "find_role_id", value)
		return
	end

	if "friend_req_list" == type then
		self.add_req_view:Flush()
		self:FlushSocietyView("friend_list")
		return
	end

	if "auto_addfriend" == type then
		self.auto_addfriend_view:Flush()
		return
	end

	if "mail_list" == type then
		if self.society_view:IsMailViewOpen() then
			self.society_view:FlushMailView(0)
		end
		return
	end

	-- if "team_list" == type then
	-- 	self:FlushSocietyView(SocietyView.Tab_T, "team_list", value)
	-- 	return
	-- end

	if "enemy_list" == type then
		self:FlushSocietyView(SocietyView.Tab_E, "enemy_list", value)
		return
	end

	-- if "team_near_list" == type then
	-- 	self.team_near_list_view:Flush()
	-- 	return
	-- end

	-- if "team_req_list" == type then
	-- 	self.team_req_list_view:Flush()
	-- 	return
	-- end

	if "team_invite_list" == type then
		self.team_invite_list_view:Flush()
	end
end

--对外接口
--通过名字查询用户信息
function SocietyWGCtrl:GetUserInfoByName(flag, user_name, call_back_fucn)
	if nil == user_name or nil == call_back_fucn or nil == flag then
		return
	end

	--print_error("SocietyWGCtrl:GetUserInfoByName-------------"..flag.."   "..user_name)
	if "number" ~= type(flag) then
		return
	end
	self.callback_list[flag] = call_back_fucn

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSFindRoleByName)
	send_protocol.gamename = user_name
	send_protocol.msg_identify = flag
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:Open(tab_index, param_t)
	if param_t ~= nil then
		if param_t.sub_view_name == SubViewName.QuickTeam then
			self.society_view:OnFindNearTeam()
			return
		end
	end
	self.society_view:Open(tab_index)

	if nil ~= param_t then
		if param_t.sub_view_name == SubViewName.OneKeyAddFriend then
			self.auto_addfriend_view:Open()
			return
		end
	end
end

function SocietyWGCtrl:Close()
	self.society_view:Close()
end

function SocietyWGCtrl:IsOpen()
	return self.society_view:IsOpen()
end

function SocietyWGCtrl:FlushSocietyView(index, key, param_t)
	if self.society_view:IsOpen() then
		self.society_view:Flush(index, key, param_t)
	end
end

function SocietyWGCtrl:OpenReqView()
	self.add_req_view:Open()
end
-- 打开一键添加好友面板
function SocietyWGCtrl:OpenAutoAddFriendView()
	if self.auto_addfriend_view and not self.auto_addfriend_view:IsOpen() then
		self.auto_addfriend_view:Open()
	end
end

function SocietyWGCtrl:CloseAutoAddFreindView()
	if self.auto_addfriend_view then
		self.auto_addfriend_view:Close()
	end
end

function SocietyWGCtrl:GetFriendListCallBack()
	if nil ~= self.m_friend_list_callback_flag then
		return self.callback_list[self.m_friend_list_callback_flag]
	end
end


function SocietyWGCtrl:GetNearListCallBack()
	if nil ~= self.m_near_list_callback_flag then
		return self.callback_list[self.m_near_list_callback_flag]
	end
end

--加入队伍
function SocietyWGCtrl:JoinTeam(team_index)
	if team_index then
		--self.society_view:JoinTeam(team_index)
		self:JoinTeam1(team_index)
	end
end

function SocietyWGCtrl:JoinTeam1(team_index)
	if team_index then
		SocietyWGCtrl.Instance:SendReqJoinTeam(team_index)
	end
end
--打开好友列表面板
function SocietyWGCtrl:OpenFriendListView(flag, call_back_fucn)
	if nil == flag or nil == call_back_fucn then
		ErrorLog("打开面板参数有误")
		return
	end
	self.m_friend_list_callback_flag = "friend_list"..flag
	self.callback_list[self.m_friend_list_callback_flag] = call_back_fucn
	self.friend_list_view:Open()
end

--打开附近玩家列表面板
function SocietyWGCtrl:OpenNearListView(flag, call_back_fucn)
	if nil == flag or nil == call_back_fucn then
		ErrorLog("打开面板参数有误")
		return
	end
	self.m_near_list_callback_flag = "near_list"..flag
	self.callback_list[self.m_near_list_callback_flag] = call_back_fucn
	self.near_list_view:Open()
end

--添加好友接口
function SocietyWGCtrl:IAddFriend(user_id)
	if user_id then
		self:AddFriend(user_id,0)
	end
end

--组队邀请接口  --type 对应 TEAM_INVITE_TYPE  
function SocietyWGCtrl:ITeamInvite(user_id, type, team_type, fb_mode)
	local function callback()
		if user_id then
			--self.society_view:ITeamInvite(user_id)
			self:ITeamInvite1(user_id, type, team_type, fb_mode)
		end
	end
	OperateFrequency.Operate(callback, "teaminvite")
end

--创建队伍返回1成功 0失败
function SocietyWGCtrl:DoOpearte(result)
    if result == 0 then
        local count = #self.invite_callback_list
        for i = count, 1, -1 do
			table.remove(self.invite_callback_list, i)
		end
    elseif result == 1 then
        local count = #self.invite_callback_list
        local info = nil
        for i = count, 1, -1 do
			info = self.invite_callback_list[i]
			info.callback(info.role_id, info.type)
			table.remove(self.invite_callback_list, i)
		end
    end
end

--type1 对应 TEAM_INVITE_TYPE
function SocietyWGCtrl:ITeamInvite1(role_id, type1, team_type, fb_mode)
	if 1 == SocietyWGData.Instance:GetIsInTeam() then
		if 0 == SocietyWGData.Instance:GetIsTeamLeader() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.DontInviety)
			return
		end
    else
		local team_type = team_type or 0
	    local fb_mode = fb_mode or 1
	    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
	    local min_level, max_level = COMMON_CONSTS.NoalGoalLimitMinLevel, top_user_level
        NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
        SocietyWGCtrl.Instance:SendTeamListReq(team_type, fb_mode)

        local callback = function(role_id, type1)
            NewTeamWGCtrl.Instance:SendInviteUser(role_id, type1, 0)
        end
        local info = {["role_id"] = role_id,["type"] = type1, ["callback"] = callback,}
        if IsEmptyTable(self.invite_callback_list) then
            self.invite_callback_list[1] = info
        else
            for k, v in pairs(self.invite_callback_list) do
                if v.role_id ~= role_id or v.type ~= type1 then

                    table.insert(self.invite_callback_list, info)
                end
            end
        end

	    -- ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
	    return
	end
	NewTeamWGCtrl.Instance:SendInviteUser(role_id, type1, 1)
end


--发送邮件接口
function SocietyWGCtrl:IOpenSendMail(name)
	if name then

		self.society_view:Open(SocietyView.Tab_M)
		self.society_view:Flush(SocietyView.Tab_M, "open_write_mail", {name = name})
	end
end

function SocietyWGCtrl:OpenMailView()
	-- if IS_ON_CROSSSERVER then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.CrossAddFriend)
	-- 	return true
	-- end
	self.society_view:Open(SocietyView.Tab_M)
	return true
	--self.is_main_mail_icon_open = true
	--ViewManager.Instance:Open(GuideModuleName.Society,SocietyView.Tab_M)SocietyView.Tab_F
end
function SocietyWGCtrl:OpenFriendChatView()
	self.open_friend_from_main_icon = true
	self.society_view:Open(SocietyView.Tab_F)
end

-- 打开队伍面板
function SocietyWGCtrl:IOpenTeamView()
	self:Open(SocietyView.Tab_T)
end

function SocietyWGCtrl:ResetSocietyFriendViewActerLYLLahei()
	self.society_view:OnClickExpandHandler(1, false)
end

function SocietyWGCtrl:OnOtherRoleOnlineChange(role_id, is_online)
	local friend_list = self.data_mgr:GetFriendList()

	for k, v in pairs(friend_list) do
		if v.user_id == role_id then
			if is_online == 0 or is_online == 1 then
				local str = is_online == 0 and Language.Society.FriendUnLine or Language.Society.FriendOnLine
				str = string.format(str,v.gamename)
				if v.is_online ~= 3 then
					SysMsgWGCtrl.Instance:ErrorRemind(str)
				end
			end
			v.is_online = is_online
			self:Flush("friend_list")
			self:FlushSendGoodsView()

			-- if role_id == RoleWGData.Instance:GetLoverRoleId() then
			-- 	if is_online ~= 0 then
			-- 		local has_gift, has_flower, _, flower_item_id = ItemWGData.Instance:CheckHasScoietyGiftItem()
			-- 		if has_gift or has_flower then
			-- 			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, 1, function()
			-- 				if has_gift then
			-- 					--ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
			-- 				else
			-- 					local t = flower_item_id ~= 0 and {item_id = flower_item_id} or nil
			-- 					FlowerWGCtrl.Instance:Open(t)
			-- 				end
			-- 				return true
			-- 			end)
			-- 		elseif MarryWGData.Instance:GetIsSocietyGiftOpened() then
			-- 			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, 0)
			-- 		end
			-- 	elseif MarryWGData.Instance:GetIsSocietyGiftOpened() then
			-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, 0)
			-- 	end
			-- end
			break
		end
	end

	local enemy_list = self.data_mgr:GetEnemyList()
	for k, v in pairs(enemy_list) do
		if v.user_id == role_id then
			if is_online == 0 or is_online == 1 then
				local str = is_online == 0 and Language.Society.EnemyUnLine or Language.Society.EnemyOnLine
				str = string.format(str,v.gamename)
				if v.is_online ~= 3 then
					SysMsgWGCtrl.Instance:ErrorRemind(str)
				end
			end
			v.is_online = is_online
			self:Flush("enemy_list")
			break
		end
	end

	if 1 == is_online then
		ChatWGData.Instance:ReadMessage(role_id, true)
		ChatWGData.Instance:RemoveCache(role_id)
	end
end
function SocietyWGCtrl:FlushSendGoodsView()
	if self.society_send_goods_view:IsOpen() then
		self.society_send_goods_view:FlushFriendList(2)
	end
end
-- 发送随机获取附近玩家请求
function SocietyWGCtrl:SendGetRandomRoleList()
	-- print_error("SocietyWGCtrl:SendGetRandomRoleList------------------------------")
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetRandomRoleList)
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendAddFriendRet(user_info, flag, add_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSAddFriendRet)
	send_protocol.req_user_id = user_info.user_id
	send_protocol.req_gamename = user_info.req_gamename
	send_protocol.is_accept = flag
	send_protocol.req_sex = user_info.req_sex
	send_protocol.req_prof = user_info.req_prof
	send_protocol.add_type = add_type and ADD_FRIEND_REQ_TYPE.LIAOYILIAO or ADD_FRIEND_REQ_TYPE.COMMON
	
	send_protocol.is_not_receive_in_today = self.data_mgr:GetFriendNotReceiveFlag() --1：表示今日不在接受该玩家请求
	send_protocol:EncodeAndSend()
end
-- 请求好友信息
function SocietyWGCtrl:SendFriendInfoReq()
	--if self.get_friend_time and self.get_friend_time > TimeWGCtrl.Instance:GetServerTime() - 5 then return end
	self.get_friend_time = TimeWGCtrl.Instance:GetServerTime()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSFriendInfoReq)
	send_protocol:EncodeAndSend()
end
-- 添加好友
function SocietyWGCtrl:AddFriend(user_id, yi_jian)
	if user_id then
		-- print_error("AddFriend-----------"..user_id.."    "..yi_jian)
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSAddFriendReq)
		send_protocol.friend_user_id = user_id
		send_protocol.is_yi_jian = yi_jian
		send_protocol:EncodeAndSend()
	end
end

function SocietyWGCtrl:SendAutoHaveTeam()
	-- local send_protocol = ProtocolPool.Instance:GetProtocol(CSAutoHaveTeam)
	-- send_protocol:EncodeAndSend()
end

-- 自动加入答应加入队伍
-- is_auto_join_team 1为是 0为否
function SocietyWGCtrl:SendAutoApplyJoinTeam(is_auto_join_team)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSAutoApplyJoinTeam)
	send_protocol.is_auto_join_team = is_auto_join_team

	send_protocol:EncodeAndSend()
end

--改变拾取模式
function SocietyWGCtrl:SendChangeAssignMode(assign_mode)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChangeAssignMode)
	send_protocol.assign_mode = assign_mode
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendTeamReadyStateChange(is_ready)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSTeamReadyStateChangeReq)
	send_protocol.is_ready = is_ready
	send_protocol:EncodeAndSend()
end

--解散队伍
function SocietyWGCtrl:SendDismissTeam()
	local protocol = ProtocolPool.Instance:GetProtocol(CSDismissTeam)
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendChangeTeamLeader(role_id)
	if role_id == nil then
		return
	end
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChangeTeamLeader)
	send_protocol.role_id = role_id
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendExitTeam()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSExitTeam)
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendTeamMemberFollow(agree)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSTeamMemberFollow)
	send_protocol.agree = agree
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendKickOutOfTeam(role_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSKickOutOfTeam)
	send_protocol.role_id = role_id
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendInviteUserTransmitRet(role_id, result, is_record_refuse_flag, is_fenshen)
	is_record_refuse_flag = is_record_refuse_flag or 0
	if nil == role_id or nil == result then return end
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSInviteUserTransmitRet)
	send_protocol.inviter = role_id
    send_protocol.result = result
    send_protocol.is_record_refuse_flag = is_record_refuse_flag
	--send_protocol.is_fenshen = is_fenshen and is_fenshen or 0
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendReqMemChangeTeamLeader()
	local protocol = ProtocolPool.Instance:GetProtocol(CSReqMemChangeTeamLeader)
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendAckMemChangeTeamLeader(role_id, is_accept)
	local protocol = ProtocolPool.Instance:GetProtocol(CSAckMemChangeTeamLeader)
	protocol.role_id = role_id or 0
	protocol.is_accept = is_accept or false
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:SendReqTeamCallTogether(role_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamCallTogether)
	protocol.role_id = role_id
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:OnSCTeamCallTogether(protocol)
	local call_data = {}
	call_data.scene_id = protocol.scene_id
	call_data.scene_key = protocol.scene_key
	call_data.pos_x = protocol.pos_x
	call_data.pos_y = protocol.pos_y
	NewTeamWGCtrl.Instance:OpenTeamCallTogether(call_data)
end

function SocietyWGCtrl:OnSCTeamMemberFollowLeaderChangeScene(protocol)
	local info = {}
	info.scene_id = protocol.scene_id
	info.scene_key = protocol.scene_key
	info.pos_x = protocol.pos_x
	info.pos_y = protocol.pos_y
	info.is_follow_flag = protocol.is_follow_flag
	if info.is_follow_flag == 1 then
		self:TeamCallLeaderChange(info)
	else
		local recovery_uuid = nil
		local recovery_follow_type = nil
		local recovery_cache = GuajiWGCtrl.Instance:GetRecoveryFollowCache()

		if recovery_cache == nil then
			return
		end
	
		recovery_uuid = recovery_cache.uuid
		recovery_follow_type = recovery_cache.follow_type
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
		GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
	
		if recovery_uuid ~= nil and recovery_follow_type ~= nil and recovery_follow_type == OBJ_FOLLOW_TYPE.TEAM then
			self:TeamCallLeaderChange(info)
		end
	end
end

function SocietyWGCtrl:TeamCallLeaderChange(info)
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
    end

    local main_role = Scene.Instance:GetMainRole()
    local scene_logic = Scene.Instance:GetSceneLogic()
    if main_role == nil or main_role:IsDeleted() then
    	return
    end

    if main_role:IsInXunYou() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
        return
    end

    local member_list = SocietyWGData.Instance:GetTeamMemberList()
    local leader_role_id
    for k, v in pairs(member_list) do
        if v.is_leader == 1 then
            leader_role_id = v.role_id
            break
        end
    end

    local sence_id = Scene.Instance:GetSceneId()
	if sence_id == info.scene_id then
        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
        GuajiWGCtrl.Instance:MoveToPos(sence_id, info.pos_x, info.pos_y, 1, nil, nil, nil, function ()
        	-- 隊長在視野範圍内
        	-- local leader_obj = Scene.Instance:GetRoleByOrginalId(leader_role_id)
        	-- if leader_obj then
        	-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        	-- end
			MainuiWGCtrl.Instance:OnClickFollowBtn()
        end, nil, CLIENT_MOVE_REASON.FOLLOW)
	else
		local map_name = Config_scenelist[info.scene_id] and Config_scenelist[info.scene_id].name or ""
    	TeamHandleClickPoint.CheckSceneIdOperate(info.scene_id, {map_name, info.scene_id, info.pos_x,
    									info.pos_y, function ()
											MainuiWGCtrl.Instance:OnClickFollowBtn()
										end})
	end
end

function SocietyWGCtrl:SendFollowLeader(protocol)
    local member_list = SocietyWGData.Instance:GetTeamMemberList()
    local leader_role_id = 0
    for k, v in pairs(member_list) do
        if v.is_leader == 1 then
            leader_role_id = v.role_id
            break
        end
    end

	local info = {}
	for i = 1, #protocol.team_member_list do
		if leader_role_id == protocol.team_member_list[i].role_id then
			info.scene_id = protocol.team_member_list[i].scene_id
			info.scene_key = protocol.team_member_list[i].scene_key
			info.pos_x = protocol.team_member_list[i].pos_x
			info.pos_y = protocol.team_member_list[i].pos_y
			break
		end
	end

	if not IsEmptyTable(info) and info.scene_id > 0 then
		self:TeamCallLeaderChange(info)
	elseif not IsEmptyTable(info) and info.scene_id == 0 then
		--延迟一档时间在请求一次（服务端还没处理完）
		GlobalTimerQuest:AddDelayTimer(function()
			Scene.SendReqTeamMemberPos(EnumReqTeamMemberPosReason.FollowTeam)
		end, 2)
	end
end

--发送获得祝福所有信息的请求
function SocietyWGCtrl:SendChangeBlessInfoReq(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFriendBlessOperaReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end


--打开面板时所有的祝福信息读取
function SocietyWGCtrl:OnFriendBlessAllInfo(protocol)
	 self.data_mgr:FriendBlessAllInfo(protocol)
end

--更改时获得更改的祝福信息（只返回个别）
function SocietyWGCtrl:OnFriendBlessChangeInfo(protocol)
	self.data_mgr:FriendBlessChangeInfo(protocol)
	self:Flush("friend_list")
end

--检测是否有好友邀请我祝福他
function SocietyWGCtrl:OnFriendBlessInviteBless(protocol)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SOCIETY_WISH, 1, function ()
			if nil == self.pop_blessing_alert then
				self.pop_blessing_alert = Alert.New()
			end
			self.pop_blessing_alert:Open()
			self.pop_blessing_alert:SetOkFunc(function ()
	  			self:ToSendBless(protocol.uid)
	  			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SOCIETY_WISH, 0)
	  		end)
			self.pop_blessing_alert:SetLableString(string.format(Language.Society.BlessingAlert, protocol.gamename))
			self.pop_blessing_alert:SetCancelFunc(function ()
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SOCIETY_WISH, 0)
			end)
			self.pop_blessing_alert:SetCloseFunc(function ()
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SOCIETY_WISH, 0)
			end)
		end)
end

--发送第一次打开界面的时候的祝福信息请求
function SocietyWGCtrl:ToGetBlessAllInfo()
	SocietyWGCtrl.Instance:ToGetBless()
end



--发送单次的祝福请求
function SocietyWGCtrl:ToSendBless(target_uid)
	if target_uid == nil then return end
	self:SendChangeBlessInfoReq(OPERA_TYPE.FRIEND_BLESS_OPERA_TYPE_BLESS,target_uid)
end

--发送祝福请求
function SocietyWGCtrl:ToGetBless()
	self:SendChangeBlessInfoReq(OPERA_TYPE.FRIEND_BLESS_OPERA_TYPE_QUERY,0)
end

--发送邀请别人祝福请求
function SocietyWGCtrl:ToSendBlessInvite(target_uid)
	self:SendChangeBlessInfoReq(OPERA_TYPE.FRIEND_BLESS_OPERA_TYPE_INVITE_BLESS,target_uid)
end

--打开好友列表面板
function SocietyWGCtrl:OpenFriendPanel()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Society, "society_friend")
end

function SocietyWGCtrl:OpenAddFrinedView(name)
	self.society_add_friend_view:SetFriendName(name)
	self.society_add_friend_view:Open()
end

--===============批量删除好友======================
function SocietyWGCtrl:OpenRemovePanel()
	self.society_remove_view:Open()
end

--删除好友
function SocietyWGCtrl:DeleteFriend(user_id)
	-- print_error("DeleteFriend------------   "..user_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSDeleteFriend)
	send_protocol.user_id = user_id
	send_protocol:EncodeAndSend()
end


--好友祝福(升级祝福)----------------------------------------------------------

function SocietyWGCtrl:OpenFriendblessPanel()
	--self.friends_blessing_view:Open()
end

function SocietyWGCtrl:OpenBlessingTip()
	self.blessingTip:Open()
end

function SocietyWGCtrl:CloseFriendblessPanel()
	--self.friends_blessing_view:Close()
end

function SocietyWGCtrl:OpenFriendReceivePanel()
	self.friends_receive_view:Open()
end

function SocietyWGCtrl:CloseFriendReceivePanel()
	self.friends_receive_view:Close()
end

function SocietyWGCtrl:OpenBlessingView()
	self.blessing_view:Open()
end

-- 好友祝福通知信息
function SocietyWGCtrl:OnFriendblessNotice(protocol)
	self.data_mgr:SetFriendblessNotice(protocol)

	if protocol.notice_type == FRIENDBLESS_NOTICE.BEBLESSED then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REBATE, 1, function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.FriendsReturn)
		end)
		if self.friends_receive_view then
			self.friends_receive_view:Flush()
		end
	else
		--self.friends_blessing_view:Flush()
		if self.friends_receive_view then
			self.friends_receive_view:Flush()
		end
	end
end

-- 好友祝福操作(升级祝福)
function SocietyWGCtrl:SendFriendblessOperate(operate, param1, param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSFriendblessOperate)
	send_protocol.operate = operate or 0
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:FlushMasterView(index, key, param_t)
	--print_error("执行了结束关系后的刷新",index, key, param_t)
	self:FlushSocietyView(index, key, param_t)
end

--被击杀 屏蔽被击杀弹窗
function SocietyWGCtrl:OnSCNotifyBeKillInfo(protocol)
	FuhuoWGCtrl.Instance:SetKillerName(protocol.killer_name,protocol.killer_level,protocol.m_prof ~= -1, protocol.plat_type, protocol.server_id, protocol.uid, protocol.type, protocol.param)

	-- 被别人渡劫的雷劈死不添加仇人
	if protocol.type == FUHUO_TYPE.TYPE_ORDEAL then
		return
	end

	-- 自动复活添加仇人
	if protocol.m_prof == -1 then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()

	if self.data_mgr:GetIsLimitAddEnemyScene(scene_type) then
		return
	end

	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if 0 == fb_scene_cfg.is_auto_revive then return end
    if 0 == fb_scene_cfg.is_resurgence then return end

	local shield_hearsay = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_REVIVE)
	if not shield_hearsay then return end
	local my_server_id = RoleWGData.Instance:GetOriginServerId()
	local my_plat_type = RoleWGData.Instance:GetPlatType()
	if my_server_id ~= protocol.server_id or my_plat_type ~= protocol.plat_type then
		return
	end	

    local num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.RESURGENCE_STONE) -- 有复活石使用复活石复活，没有仙玉复活
    if num > 0 then
    	SocietyWGCtrl.Instance:SendAddEnemy(protocol.uid, protocol.plat_type)
    else
        local main_role_vo = RoleWGData.Instance:GetRoleInfo()
        local cost = FuhuoWGCtrl.Instance:GetFuhuoGold()
        if cost > main_role_vo.bind_gold + main_role_vo.gold then return end
        SocietyWGCtrl.Instance:SendAddEnemy(protocol.uid, protocol.plat_type)
	end
end


---------------------------------------------------------------------
--					捕鱼
----------------------------------------------------------------------

--鱼塘系统


-- 打开灵池面板
-- function SocietyWGCtrl:Open(param_t)
-- 	local role_id = RoleWGData.Instance.role_vo.role_id
-- 	self:ChangeShowPondUid(role_id)
-- 	self.society_view:Open(param_t)
-- end

-- 打开好友面板
function SocietyWGCtrl:OpenFriendView(value)
	self.friend_view:Open()
	self.friend_view:ChangeData(SocietyWGData.GetInfoType.ServerAllInfo)
end

-- 关闭所有相关面板（除了主面板）
function SocietyWGCtrl:CloseAllView()
	if self.friend_view:IsOpen() then
		self.friend_view:Close()
	end
	if self.all_info_view:IsOpen() then
		self.all_info_view:Close()
	end
	-- if self.fish_info_view:IsOpen() then
	-- 	self.fish_info_view:Close()
	-- end
end

-- 打开详细信息面板
function SocietyWGCtrl:OpenAllInfoView()
	local uid = self.data_mgr:GetShowPond()
	if nil == uid or 0 >= uid then return end
	self.all_info_view:Open()
	local all_data = self.data_mgr:GetFishpondAllInfo(self.data_mgr:GetShowPond())
	self.all_info_view:Flush()
	if all_data then
		self.all_info_view:Flush()
	else
		self:SendFishPoolQueryReq(SocietyWGData.GetInfoType.AllInfo, uid)
	end
end

-- 打开某条鱼信息面板
function SocietyWGCtrl:OpenFishInfoView(info)
	-- self.fish_info_view:Open()
	-- self.fish_info_view:Setdata(info)
end

-- 鱼池所有信息
function SocietyWGCtrl:OnFishPoolAllInfo(protocol)
	--print_error("111",protocol)
	self.data_mgr:SetFishpondAllInfo(protocol)
	if self.data_mgr:IsShowPond(protocol.normal_info.owner_uid) then
		self:FlushSocietyView(SocietyView.Tab_B, "allfish")
	end
end

-- 鱼塘鱼儿信息
function SocietyWGCtrl:OnFishPoolAllRaiseInfo(protocol)
	--print_error("55555555555555",protocol)
	self.data_mgr:SetFishpondFishInfo(protocol)
	if self.data_mgr:IsShowPond(protocol.owner_uid) then
		-- self:FlushSocietyView(SocietyView.Tab_B)
		self.all_info_view:Flush()
	end
end

-- 普通信息
function SocietyWGCtrl:OnFishPoolCommonInfo(protocol)
	--print_error("普通信息",protocol)
	self.data_mgr:SetFishpondNormalInfo(protocol)
	if self.data_mgr:IsShowPond(protocol.normal_info.owner_uid) then
		self:FlushSocietyView(SocietyView.Tab_B)
		self.all_info_view:Flush()
	elseif self.society_view:IsOpen() then
		self.society_view:UpdataBulletNum()
	end
end

-- 鱼塘放养信息变化
function SocietyWGCtrl:OnFishPoolRaiseInfoChange(protocol)
	local fish_info = self.data_mgr:GetFishpondFishInfo(protocol.owner_uid)
	if fish_info then
		local del = false
		if 0 == protocol.fish_info.fish_type then
			for i, v in pairs(fish_info) do
				if v and v.fish_objid == protocol.fish_info.fish_objid then
					table.remove(fish_info, i)
				end
			end
			del = true
			-- self.fish_info_view:DelFishCheck(fish_info)
		else
			table.insert(fish_info, protocol.fish_info)
			--print_error(fish_info)
			del = false
		end
		if self.data_mgr:IsShowPond(protocol.owner_uid) then
			self.society_view:FishsChange(protocol.fish_info, del)
		end
	end
	local day_raise_count = self.data_mgr:GetFishpondRecordInfo(protocol.owner_uid)
	if day_raise_count then
		local has_type = false
		for i, v in pairs(day_raise_count) do
			if v.fish_type == protocol.fish_recoed.fish_type then
				v.day_raise_count = protocol.fish_recoed.day_raise_count
				has_type = true
			end
		end
		if not has_type then
			table.insert(day_raise_count, protocol.fish_recoed)
		end
		--print_error("开始刷新商店")
		self.society_view:FlushFishShop()
	end
end

-- 好友鱼池简要信息
function SocietyWGCtrl:OnFishPoolFriendsGeneralInfo(protocol)
	self.data_mgr:SetFishPoolFriendList(protocol.info_list)
	for k, v in pairs(protocol.info_list) do
		AvatarManager.Instance:SetAvatarKey(v.friend_uid, v.avatar_key_big, v.avatar_key_small)
	end
end

-- 盟友鱼池简要信息
function SocietyWGCtrl:OnFishPoolGuildMemberGeneralInfo(protocol)
	self.data_mgr:SetGuildFriendList(protocol.info_list)
	for k, v in pairs(protocol.info_list) do
		AvatarManager.Instance:SetAvatarKey(v.friend_uid, v.avatar_key_big, v.avatar_key_small)
	end
end

--返回处理
function SocietyWGCtrl:BackHandler()
	local role_id = RoleWGData.Instance.role_vo.role_id
	if nil == role_id then return end
	self:ChangeShowPondUid(role_id)
	--print_error(role_id)
end

--改变显示鱼塘的主人id
function SocietyWGCtrl:ChangeShowPondUid(uid)
	local cur_uid = self.data_mgr:GetShowPond()
	--print_error(cur_uid,uid)
	if nil == uid or cur_uid == uid then return end
	self.data_mgr:ChangeShowPond(uid)
	local info = self.data_mgr:GetFishpondAllInfo(uid) or {}
	if #info == 0 then
		self:SendFishPoolQueryReq(SocietyWGData.GetInfoType.AllInfo, uid)
	else
		self:FlushSocietyView(SocietyView.Tab_B, "allfish")
	end
end

-- 请求放养鱼儿
function SocietyWGCtrl:SendFishPoolRaiseReq(fish_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFishPoolRaiseReq)
	protocol.fish_type = fish_type
	protocol:EncodeAndSend()
end

-- 购买子弹请求
function SocietyWGCtrl:SendFishPoolBuyBulletReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSFishPoolBuyBulletReq)
	protocol:EncodeAndSend()
end

-- 查询信息请求 query_type 0:所有信息 1:放养信息 2:好友简要信息 3：盟友简要信息 4:全服鱼塘 5：偷鱼记录
function SocietyWGCtrl:SendFishPoolQueryReq(query_type, query_uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFishPoolQueryReq)
	protocol.query_type = query_type or 0
	protocol.query_uid = query_uid or 0
	protocol:EncodeAndSend()
end

-- 偷鱼请求
function SocietyWGCtrl:SendFishPoolStealFish(target_uid, fish_objid, raise_timestamp)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFishPoolStealFish)
	protocol.target_uid = target_uid
	protocol.fish_objid = fish_objid
	protocol.raise_timestamp = raise_timestamp
	protocol:EncodeAndSend()
end

-- 收获请求
function SocietyWGCtrl:SendFishPoolHarvest(fish_objid, is_force_max_level)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFishPoolHarvest)
	protocol.fish_objid = fish_objid

	protocol.is_force_max_level = is_force_max_level or 0
	protocol:EncodeAndSend()
end

-- 拓展背包请求
function SocietyWGCtrl:SendFishPoolExtendCapacity(extend_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFishPoolExtendCapacity)
	protocol.extend_type = extend_type
	protocol:EncodeAndSend()
end

--鱼池扩展成功
function SocietyWGCtrl:OnPoolExtendSuccess(result)
	if result == 1 and self.all_info_view:IsOpen() then
		self.all_info_view:PlayPoolExtendResultEffect(3006)
	end
end

--购买保护
function SocietyWGCtrl:SendBuyFishBoot(fish_objid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBuyFishBoot)
	protocol.fish_objid = fish_objid
	protocol:EncodeAndSend()
end
--检查捕鱼提醒
function SocietyWGCtrl:CheckFishRemind(remind)
	if not FunOpen.Instance:GetFunIsOpened(FunName.Fishpond) then return 0 end
	local flag = 0
	if self.data_mgr:GetAdultFishRemind() == 1 then
		flag = 1
	elseif self.data_mgr:GetFramFishRemind() == 1 then
		flag = 1
	elseif self.data_mgr:GetBulletNum() > 0 and self.data_mgr:GetPoolFriendListCanSteal() then
		flag = 1
	elseif self.data_mgr:GetFishExtendRemind() == 1 then
		flag = 1
	end
	return flag
 end

-- 游戏开始自动拉取自己鱼塘数据和全服数据
function SocietyWGCtrl:MainuiOpenCreate()
	-- local role_id = RoleWGData.Instance.role_vo.role_id
	-- self:SendFishPoolQueryReq(SocietyWGData.GetInfoType.AllInfo, role_id)
	-- self:SendFishPoolQueryReq(SocietyWGData.GetInfoType.ServerAllInfo, 0)
	self:SendTouchOperReq(HUMO_TYPE.SEND_REQ)
end
--全服鱼池简要信息
function SocietyWGCtrl:OnFishPoolStolenMemberGeneralInfo(protocol)
	self.data_mgr:SetAllPeopleInfoList(protocol.info_list)
	for k, v in pairs(protocol.info_list) do
		AvatarManager.Instance:SetAvatarKey(v.friend_uid, v.avatar_key_big, v.avatar_key_small)
	end
	self.friend_view:Flush()
end
-- 能显示的被偷与偷人的最大数量
function SocietyWGCtrl:OnFishPoolEachOtherMemberGeneralInfo(protocol)
	self.data_mgr:SetStolenInfoList(protocol.info_list)
	for k, v in pairs(protocol.info_list) do
		AvatarManager.Instance:SetAvatarKey(v.friend_uid, v.avatar_key_big, v.avatar_key_small)
	end
end


function SocietyWGCtrl:SetTempChatList(msg_info)
	--self.temp_all_obj_chatlist
end

function SocietyWGCtrl:GetViewSelectRole()
	local role_id = 0
	if self.society_view then
		role_id = self.society_view.private_role_id or 0
	end
	return role_id
end


function SocietyWGCtrl:SelectMailListIndexByItem( index )
	self.society_view:SelectMailListIndexByItem(index)
end
--打开社交赠送道具界面
function SocietyWGCtrl:SendGoodsOpen(tag,data)
	if tag then
		--print_error(tag,data)
		self.society_send_goods_view:SetDataByTipOpen(data)
	else
		self.society_send_goods_view:Open()
	end
end
--打开社交赠送道具数量界面
function SocietyWGCtrl:OpenSendNumView(item_id, item_num,index)
	local num  = item_num > 999 and 999 or item_num
	self.society_send_num:SetData(item_id, num,index)
end
--确定选择数量后
function SocietyWGCtrl:ClickNumOK(item_id,num,index,is_change)
	--

	local data = {}
	data.item_id = item_id
	data.num = num
	data.index = index
	-- local bag_list = ItemWGData.Instance:GetBagItemDataList()
	-- for k,v in pairs(bag_list) do
	-- 	if v.item_id == item_id then
	-- 		data.index = v.index
	-- 	end
	-- end


	if is_change then
		self.set_cell_data_list = {}
	else
		if IsEmptyTable(self.set_cell_data_list) then
			table.insert(self.set_cell_data_list,data)
		else
			local is_hae_select = false
			for k,v in pairs(self.set_cell_data_list) do
				if v.item_id == item_id and v.index == index then
					v.num = v.num + num
					if v.num <= 0 then
					table.remove(self.set_cell_data_list,k)
					end
					is_hae_select = true
				end
			end
			if #self.set_cell_data_list >= GameEnum.GIVE_PRESENT_MAX_NUM and not is_hae_select then
				is_hae_select = false
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.SendPresentNumMax)
				return
			end
			if not is_hae_select then
				table.insert(self.set_cell_data_list,data)
			end
		end
	end
	self.society_send_goods_view:HasSelectItemCellSetData(self.set_cell_data_list)
end
function SocietyWGCtrl:GetSelectItemCellSetData()
	return self.set_cell_data_list
end
function SocietyWGCtrl:SetSendFriendData(data)
	self.select_send_friend_data = data
end
function SocietyWGCtrl:SendSendSpecialMaterial(present_type) --赠送礼物特殊处理  present_type = 3
	if IsEmptyTable(self.set_cell_data_list) or IsEmptyTable(self.select_send_friend_data) then return end
	local bag_list = {}
	for k,v in pairs(ItemWGData.Instance:GetBagItemDataList()) do
		local t = bag_list[v.item_id] or {}
		table.insert(t, v)
		bag_list[v.item_id] = t
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSGivePresent)
	local send_list = {}
	local num = 0
	for k,v in pairs(self.set_cell_data_list) do
		local t = bag_list[v.item_id]
		num = v.num or 0
		if t then
			table.sort(t, SortTools.KeyLowerSorter("num"))
			for k1,v1 in ipairs(t) do
				if v.item_id == v1.item_id then
					send_list[v.item_id] = send_list[v.item_id] or {}
					if num > 0 then
						num = num - v1.num
						table.insert(send_list, {item_id = v1.item_id, index = v1.index, num = num > 0 and v1.num or num + v1.num})
						if #send_list == 5 then
							self:SendSendMaterialSeq(present_type, send_list)
							send_list = {}
						end
					else
						break
					end
				end
			end
		end
	end
	if next(send_list) then
		self:SendSendMaterialSeq(present_type, send_list)
	end


end
--赠送协议发送
function SocietyWGCtrl:SendSendMaterial(present_type)
	self:SendSendMaterialSeq(present_type, self.set_cell_data_list)
end

function SocietyWGCtrl:SendSendMaterialSeq(present_type, list)
	if IsEmptyTable(list) or IsEmptyTable(self.select_send_friend_data) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.SendFault)
		return
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSGivePresent)
	protocol.present_type = present_type
	protocol.target_uid = self.select_send_friend_data.user_id
	protocol.present_count = #list
	protocol.present_list = list
	protocol:EncodeAndSend()
	self:SendSendMaterialAfter()
end

function SocietyWGCtrl:SendSendMaterialAfter()
	self.set_cell_data_list = {}
	self.society_send_goods_view:HasSelectItemCellSetData(self.set_cell_data_list ,true)
end
function SocietyWGCtrl:PresentHistoryReq(type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGivePresentHistory)
	protocol.history_type = type
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:GivePresentHistory(protocol)
	SocietyWGData.Instance:SetReceiveGiftRecord(protocol)
	if self.society_send_record_view:IsOpen() then
		self.society_send_record_view:Flush()
	end
end
function SocietyWGCtrl:OpenSendRecordView()
	if self.society_send_record_view then
		self.society_send_record_view:Open()
	end
end
function SocietyWGCtrl:GetChatRank()
	return self.society_view.my_chat_zhanli_rank or -1
end

function SocietyWGCtrl:RefuseInviteTeam(role_id)
	SocietyWGCtrl.Instance:SendInviteUserTransmitRet(role_id, 1)
	GlobalTimerQuest:AddDelayTimer(function ()
		SocietyWGCtrl.Instance:DeleteReq(role_id)
	end, 0)
end
--
function SocietyWGCtrl:OpenAddTipsPanel(info)
	self.add_friend_tips_panel:SetDataInfo(info)
end


--虎摸相关
function SocietyWGCtrl:SendTouchOperReq(oper_type,param1)
	if oper_type == HUMO_TYPE.HM_FRIEND then
		self.sned_humo_req_flag = true
		-- self:ShowHuMoAnimator()
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSTouchOperReq)
	protocol.oper_type = oper_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:ShowHuMoAnimator()
	if self.society_view:IsOpen() then
		self.society_view:ShowHuMoAnimator()
	end
end

function SocietyWGCtrl:OnSCFriendTouchBaseInfo(protocol)
	local old_info = SocietyWGData.Instance:GetFriendTouchBaseInfo()
	local old_touch_num = old_info and old_info.today_touch_count or 0
	if self.sned_humo_req_flag and old_touch_num < protocol.today_touch_count then
		self:ShowHuMoAnimator()
		self.sned_humo_req_flag = false
	end
	self.data_mgr:SetFriendTouchBaseInfo(protocol)
	-- if protocol.info_type == 1 then
	-- 	if self.humo_betouch_view and not self.humo_betouch_view:IsOpen() then
	-- 		self:OpenBeTouchView()
	-- 		return
	-- 	end
	-- 	self:FlushBeTouchView()
	-- end
end

function SocietyWGCtrl:OnSCTouchRecordInfo(protocol)
	self.data_mgr:SetTouchRecordInfo(protocol)
	self:FlushHuMoRmberView()
	self:Flush("friend_list")
	MainuiWGCtrl.Instance:SetFriendRemind()
	RemindManager.Instance:Fire(RemindName.SocietyFriends)
	RemindManager.Instance:Fire(RemindName.SocietyFriends2)
end

function SocietyWGCtrl:OpenBeTouchView(data)
	if self.humo_betouch_view then
		self.humo_betouch_view:SetFriendInfo(data)
		self.humo_betouch_view:Open()
	end
end

function SocietyWGCtrl:FlushBeTouchView()
	if self.humo_betouch_view and self.humo_betouch_view:IsOpen() then
		self.humo_betouch_view:Flush()
	end
end

function SocietyWGCtrl:OpenHuMoRmberView()
	if self.humo_rember_view then
		self.humo_rember_view:Open()
	end
end

function SocietyWGCtrl:FlushHuMoRmberView()
	if self.humo_rember_view and self.humo_rember_view:IsOpen() then
		self.humo_rember_view:Flush()
	end
end

--日期改变的时候刷新一下红点
function SocietyWGCtrl:DayChange()
	self:SendTouchOperReq(HUMO_TYPE.SEND_REQ)
end

--已读邮件详细信息
function SocietyWGCtrl:CSMailRead(select_mail_index)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailRead)
	send_protocol.mail_index = select_mail_index
	send_protocol:EncodeAndSend()
end

--一键领取/领取
function SocietyWGCtrl:CSMailOneKeyFetchAttachmentArr(temp_has_fu_jian)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailOneKeyFetchAttachmentArr)
	send_protocol.mail_num = #temp_has_fu_jian
	send_protocol.mail_index_list = temp_has_fu_jian
	send_protocol:EncodeAndSend()
end

--一键删除/删除
function SocietyWGCtrl:CSMailOneKeyDelete(all_read_mail)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailOneKeyDelete)
	send_protocol.mail_num = #all_read_mail
	send_protocol.mail_index_list = all_read_mail
	send_protocol:EncodeAndSend()
end

--移除好友列表数据
function SocietyWGCtrl:OnSCFriendApplicantListRemove(protocol)
	local cur_list_num = self.data_mgr:RemoveFriendListInfo(protocol.remove_uid)

	if cur_list_num > 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND, 1, function ()
			self:OpenReqView()
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND, 0)
	end
	RemindManager.Instance:Fire(RemindName.SocietyFriends)
	RemindManager.Instance:Fire(RemindName.SocietyFriends2)
	MainuiWGCtrl.Instance:SetFriendRemind()
	self.add_req_view:Flush()
	self:Flush("friend_list")
end

function SocietyWGCtrl:GetCacularPos(node)
	if nil == node then return nil end
	local society_view = self.society_view
	if nil == society_view or not society_view:IsOpen() then
		return
	end
	local parent_rect= society_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	--local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
	local y = parent_rect.sizeDelta.y / 2
	local screen_pos_tbl
	if node.gameObject then
		screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	else
		screen_pos_tbl = node
	end
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	if local_position_tbl then
		--[[x = local_position_tbl.x + 155 + (node.rect and node.rect.sizeDelta.x or 0)
		x = math.max(x, -parent_rect.sizeDelta.x / 2)
		x = math.min(x, parent_rect.sizeDelta.x / 2 - 133)--]]
		y = local_position_tbl.y -110
		y = math.max(y, -parent_rect.sizeDelta.y / 2 +280)
		y = math.min(y, parent_rect.sizeDelta.y / 2 + 50)
		return Vector2(-60, y) --Vector2(x, y)
	end
end

function SocietyWGCtrl:TryToFlushSocietyPYP()
	if self.society_view:IsOpen() then
		self.society_view:Flush(TabIndex.society_friend, "update_msg")
	end
end

--今日不在接受该玩家请求
function SocietyWGCtrl:SendFriendNotReceive(flag)
	-- print_error("FFFF 111==== 今日不在接受该玩家请求", flag)
	local protocol = ProtocolPool.Instance:GetProtocol(CSNotReceiveApplicationFlag)
	protocol.flag = flag or 0
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:OnSCNotReceiveApplicationFlagRet(protocol)
	-- print_error("FFFF 222==== 今日不在接受该玩家返回", protocol.flag)
	self.data_mgr:SetFriendNotReceiveFlag(protocol.flag or 0)
	--界面刷新
	self:FlushNotReceiveBtnState()
end

function SocietyWGCtrl:FlushNotReceiveBtnState()
	if self.add_req_view and self.add_req_view:IsOpen() then
		self.add_req_view:FlushNotReceiveBtnState()
	end
end

function SocietyWGCtrl:SendAddEnemy(role_id, plat_type)
	if role_id == nil or plat_type == nil then
		return
	end

	self:SendCrossEnemyReq(CS_CROSS_FRIEND_REQ_TYPE.CS_CROSS_FRIEND_REQ_ADD_ENEMY, role_id, plat_type)
end

function SocietyWGCtrl:DeleteEnemy(role_id, plat_type)
	if role_id == nil or plat_type == nil then
		return
	end

	self:SendCrossEnemyReq(CS_CROSS_FRIEND_REQ_TYPE.CS_CROSS_FRIEND_REQ_REMOVE_ENEMY, role_id, plat_type)
end

function SocietyWGCtrl:IgnoreOneEnemyRecord(role_id, plat_type, index)
	if role_id == nil or plat_type == nil or index == nil then
		return
	end

	self:SendCrossEnemyReq(CS_CROSS_FRIEND_REQ_TYPE.CS_CROSS_FRIEND_REQ_REMOVE_KILL_RECORD, role_id, plat_type, index)
end

function SocietyWGCtrl:ClearEnemyRecordInfo()
	self:SendCrossEnemyReq(CS_CROSS_FRIEND_REQ_TYPE.CS_CROSS_FRIEND_REQ_REMOVE_ALL_KILL_RECORD)
end

function SocietyWGCtrl:SendCrossEnemyReq(opera_type, param_1, param_2, param_3, param_4)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossFriendReq)
	send_protocol.opera_type = opera_type
	send_protocol.param_1 = param_1 or 0
	send_protocol.param_2 = param_2 or 0
	send_protocol.param_3 = param_3 or 0
	send_protocol.param_4 = param_4 or 0
	send_protocol:EncodeAndSend()
end

function SocietyWGCtrl:OnSCCrossEnemyList(protocol)
	self.data_mgr:SetEnemyData(protocol)
	for k, v in pairs(protocol.enemy_list) do
		AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
	end
	self:Flush("enemy_list")

	if self.enemy_record_view:IsOpen() then
		self.enemy_record_view:Flush()
	end
end

function SocietyWGCtrl:OnSCCrossEnemyChange(protocol)
	if protocol.reason_type == CS_CROSS_FRIEND_CHANGE_TYPE.CHANGE_TYPE_INFO or protocol.reason_type == CS_CROSS_FRIEND_CHANGE_TYPE.CHANGE_TYPE_ADD then
		self.data_mgr:UpdateEnemyData(protocol)
	else
		self.data_mgr:RemoveEnemy(protocol.enemyinfo.uuid)
	end

	AvatarManager.Instance:SetAvatarKey(protocol.enemyinfo.uuid, protocol.enemyinfo.avatar_key_big, protocol.enemyinfo.avatar_key_small)
	self:Flush("friend_list")

	if self.enemy_record_view:IsOpen() then
		self.enemy_record_view:Flush()
	end
end

function SocietyWGCtrl:OnSCCrossKillMeInfo(protocol)
	local count = self.data_mgr:GetEnemyRecordInfo()
	if count ~= nil and count < protocol.count then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ENEMY_RECORD, 1, function() 
			ViewManager.Instance:Open(GuideModuleName.SocietyEnemyRecordView)
		end)
	else
		if protocol.count == 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ENEMY_RECORD, 0, function() 
				ViewManager.Instance:Open(GuideModuleName.SocietyEnemyRecordView)
			end)

			self.data_mgr:SynEnemyRecordRealNum()
		end
	end

	self.data_mgr:SetEnemyRecordData(protocol)

	if self.enemy_record_view:IsOpen() then
		self.enemy_record_view:Flush()
	end
end

function SocietyWGCtrl:OpenFriendViewFromLiaoYiLiao(user_id, open_view)
	if open_view then
		local t = {}
		t[1] = user_id
		t[2] = 1
		self.society_view:Flush(SocietyView.Tab_F, "liao_yi_liao", t)

		if not self.society_view:IsOpen() then
			self:SetIgnodeDefSelectFriendValue(true, user_id)
			self.society_view:Open(SocietyView.Tab_F)
		end
	else
		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.Society, SocietyView.Tab_F) then
			self.society_view:Flush(SocietyView.Tab_F, "liao_yi_liao", {user_id})
		end
	end
end

function SocietyWGCtrl:OpenFriendViewFromAddFriend(user_id)
	-- print_error("OpenFriendViewFromAddFriend", user_id, ViewManager.Instance:IsOpenByIndex(GuideModuleName.Society, SocietyView.Tab_F))
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.Society, SocietyView.Tab_F) then
		self.society_view:Flush(SocietyView.Tab_F, "find_role_id_form_add_friend", {user_id})
	end
end

-- 要保证一定会打开界面
function SocietyWGCtrl:SetIgnodeDefSelectFriendValue(value, role_id)
	self.society_view:SetIgnoreDefSelectFriend(value, role_id)
end

function SocietyWGCtrl:GetSocietyViewPrivateRoleId()
	if self.society_view then
		return self.society_view:GetPrivateRoleId()
	end
end

function SocietyWGCtrl:SendReqCSTeamMemberShareItem(agree)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamMemberShareItem)
	protocol.agree = agree or 0
	protocol:EncodeAndSend()
end

function SocietyWGCtrl:OnSCTeamShareItemInfo(protocol)
	--print_error("OnSCTeamShareItemInfo====",protocol)
	NewTeamWGData.Instance:SetTeamShareItemInfo(protocol)
	NewTeamWGData.Instance:UpdataTeamShareItemInfo()
	MainuiWGCtrl.Instance:RefreshBossItemCellsData()
end

function SocietyWGCtrl:OnSCTeamShareItemUpdate(protocol)
	--print_error("update====",protocol)
	NewTeamWGData.Instance:SetSingleShareItemInfo(protocol)
	NewTeamWGData.Instance:UpdataTeamShareItemInfo()
	MainuiWGCtrl.Instance:RefreshBossItemCellsData()
end

function SocietyWGCtrl:OnSCTeamMemberShareItemUse(protocol)
	local uid  = protocol.uid
	local name = protocol.name
	local share_item_id = protocol.share_item_id

	local item_name = ItemWGData.Instance:GetItemName(share_item_id) or ""

	local error_str = string.format(Language.FollowState.ShareCardStr, name, item_name)
	SysMsgWGCtrl.Instance:ErrorRemind(error_str)
end