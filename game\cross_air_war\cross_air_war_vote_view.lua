CrossAirWarVoteView = CrossAirWarVoteView or BaseClass(SafeBaseView)
local vote_bg_length = 290					-- 0.1进度长度
local unit_pro = 0.2						-- 总长度
local min_unit_pro = 0.4					-- 最小进度
local BossType = {
    God = 0,								-- 神类型
    Demon = 1								-- 魔类型
}

function CrossAirWarVoteView:__init()
	self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_vote")
   	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.HALF)
end

function CrossAirWarVoteView:LoadCallBack()
	if not self.part_left then
		self.part_left = AirWarPartRender.New(self.node_list.part_left)
	end

	if not self.part_right then
		self.part_right = AirWarPartRender.New(self.node_list.part_right)
	end

	XUI.AddClickEventListener(self.node_list.god_vote,BindTool.Bind(self.OnClickVoteBossType, self, BossType.God))
	XUI.AddClickEventListener(self.node_list.demon_vote,BindTool.Bind(self.OnClickVoteBossType, self, BossType.Demon))
end

function CrossAirWarVoteView:ReleaseCallBack()
	if self.part_left then
		self.part_left:DeleteMe()
		self.part_left = nil
	end

	if self.part_right then
		self.part_right:DeleteMe()
		self.part_right = nil
	end

	if self.tween_move then
		self.tween_move:Kill()
		self.tween_move = nil
	end

	if self.time_txt_tween then
		self.time_txt_tween:Kill()
		self.time_txt_tween = nil
	end

	self:RemoveWarBossRefreshCd()
	self:KillBgViewTween()
	self:KillBttleSquenceTween()
	self:CancelAirVoteTween()
	self.save_monster_seq = nil
	self.vote_type = nil
end


function CrossAirWarVoteView:OnFlush(param)
	local cur_monster_seq = CrossAirWarWGData.Instance:GetWarCurVieMonsterSeq()
	if cur_monster_seq == 0 then
		return
	end 

	if (not self.save_monster_seq) or self.save_monster_seq ~= cur_monster_seq then
		self.save_monster_seq = cur_monster_seq
		self:UpdateGodDemonPart()
	end

	self.node_list.title_txt.text.text = Language.CrossAirWar.VoteSelectTitle1
	self:UpdateGodDemonVal()
	self:RefreshCDTimer()
end

function CrossAirWarVoteView:UpdateGodDemonVal()
	local god_val = CrossAirWarWGData.Instance:GetWarCurVoteGodScore()
	local demon_val = CrossAirWarWGData.Instance:GetWarCurVoteDemonScore()
	-- self.node_list.god_val.text.text = string.format(Language.CrossAirWar.GodValTxt, god_val)
	-- self.node_list.demon_val.text.text = string.format(Language.CrossAirWar.DemonValTxt, demon_val)
	self:KillBttleSquenceTween()
	self:VoteNodeSliderAnimi(self.node_list.bg_root.transform, self.battle_sequence)

	if god_val == 0 and demon_val == 0 then
		self.node_list.prog_l_val.slider.value = 0.5
		self.node_list.prog_r_val.slider.value = 0.5
		return 
	end

	local total_num = god_val + demon_val
	local left_pro = god_val / total_num * unit_pro
	local bg_length = ((left_pro - 0.1) * 10) * vote_bg_length

	self:KillBgViewTween()
	self.bg_view_tween = self.node_list.view_bg.transform:DOAnchorPosX(bg_length, 0.4)
	self.bg_view_tween:SetEase(DG.Tweening.Ease.OutCubic)
	self.node_list.prog_l_val.slider:DOValue(min_unit_pro + left_pro, 0.4)
	self.node_list.prog_r_val.slider:DOValue(min_unit_pro + demon_val / total_num * unit_pro, 0.4)
end

function CrossAirWarVoteView:KillBgViewTween()
	if self.bg_view_tween then
		self.bg_view_tween:Kill()
		self.bg_view_tween = nil
	end
end

function CrossAirWarVoteView:KillBttleSquenceTween()
	if self.battle_sequence then
		self.battle_sequence:Kill()
		self.battle_sequence = nil
	end
end

function CrossAirWarVoteView:UpdateGodDemonPart()
	local left_data, right_data = CrossAirWarWGData.Instance:GetMonsterCfg2VoteViewBySeq(self.save_monster_seq)
	self.part_left:SetData(left_data)
	self.part_right:SetData(right_data)
end


function CrossAirWarVoteView:RefreshCDTimer()
	self:RemoveWarBossRefreshCd()
	local status_time = CrossAirWarWGData.Instance:GetWarVieMosterEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local remain_time = status_time - server_time

	if remain_time > 0 then
		self.node_list["time_txt"].text.text = TimeUtil.FormatSecondDHM2(remain_time)
		CountDownManager.Instance:AddCountDown("air_war_boss_refresh_cd", 
			BindTool.Bind1(self.UpdateVoteCD, self),
			BindTool.Bind1(self.OnVoteCDComplete, self), 
			nil, remain_time, 1)
	else
		self:OnVoteCDComplete()
	end
end

function CrossAirWarVoteView:RemoveWarBossRefreshCd()
	if CountDownManager.Instance:HasCountDown("air_war_boss_refresh_cd") then
		CountDownManager.Instance:RemoveCountDown("air_war_boss_refresh_cd")
	end
end

function CrossAirWarVoteView:UpdateVoteCD(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM2(time)
	self.node_list["time_txt"].text.text = time_str
	self.node_list["time_txt"].transform.localScale = u3dpool.vec3(2, 2, 2)
	self.time_txt_tween = self.node_list["time_txt"].transform:DOScale(1, 0.8)
	self.time_txt_tween:SetEase(DG.Tweening.Ease.OutCubic)
end

function CrossAirWarVoteView:OnVoteCDComplete()
	self:RemoveWarBossRefreshCd()
	self:KillBttleSquenceTween()
	-- 刷新胜出者
	local god_val = CrossAirWarWGData.Instance:GetWarCurVoteGodScore()
	local demon_val = CrossAirWarWGData.Instance:GetWarCurVoteDemonScore()
	local winner = self.node_list.prog_l_val
	local loser = self.node_list.prog_r_val
	local winner_root = self.node_list.part_left
	local loser_root = self.node_list.part_right
	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(self.save_monster_seq)

	if not moster_cfg then
		return
	end

	-- 设置阶段
	self.node_list.title_txt.text.text = string.format(Language.CrossAirWar.VoteSelectTitle2, moster_cfg.stage_name) 

	if demon_val > god_val then
		winner = self.node_list.prog_r_val
		loser = self.node_list.prog_l_val
		winner_root = self.node_list.part_right
		loser_root = self.node_list.part_left
	end

	winner.slider:DOValue(0.8, 0.4)
	loser.slider:DOValue(0, 0.4)
	self.node_list.view_bg:SetActive(false)
	self.node_list.time_bg:SetActive(false)
	loser_root:SetActive(false)
	self.tween_move = winner_root.transform:DOAnchorPos(Vector3.zero, 0.4)
	self.tween_move:SetEase(DG.Tweening.Ease.OutCubic)

	TryDelayCall(self, function()
		self:Close()
	end, 2, "DelayCloseAirWarVoteView")
end

-- 进度条
function CrossAirWarVoteView:VoteNodeSliderAnimi(trans, sequence)
	self:CancelAirVoteTween()
	sequence = sequence or DG.Tweening.DOTween.Sequence()
	sequence:Append(trans:DOAnchorPosX(0, 0.1)) 		--恢复0
	sequence:Append(trans:DOAnchorPosX(10, 0.1)) 	--右10
	sequence:Append(trans:DOAnchorPosX(0, 0.1)) 		--恢复0
	sequence:Append(trans:DOAnchorPosX(-8, 0.2))		--左8
	sequence:Append(trans:DOAnchorPosX(10, 0.2))		
	sequence:Append(trans:DOAnchorPosX(0, 0.1))		
	sequence:Append(trans:DOAnchorPosX(-10, 0.2))	
	sequence:Append(trans:DOAnchorPosX(0, 0.1))		
	sequence:Append(trans:DOAnchorPosX(8, 0.2))		
	sequence:Append(trans:DOAnchorPosX(0, 0.2))		
    sequence:SetEase(DG.Tweening.Ease.Linear)
	sequence:SetLoops(-1)

	self.air_war_vote_tween = sequence
end

function CrossAirWarVoteView:CancelAirVoteTween()
    if self.air_war_vote_tween then
        self.air_war_vote_tween:Kill()
        self.air_war_vote_tween = nil
    end
end

-- vote_boss_type(0神、1魔)
function CrossAirWarVoteView:OnClickVoteBossType(vote_boss_type)
	vote_boss_type = vote_boss_type or 0
	if not self.vote_type then
		self.vote_type = vote_boss_type
		CrossAirWarWGCtrl.Instance:SendCSCrossAirWarOperate(CROSS_AIR_WAR_OPERATE_TYPE.MONSTER_VIE, vote_boss_type)
		local t_node = self.vote_type == BossType.God and self.node_list["demon_vote"] or self.node_list["god_vote"]
		XUI.SetButtonEnabled(t_node, false)
	else
		if self.vote_type == vote_boss_type then
			CrossAirWarWGCtrl.Instance:SendCSCrossAirWarOperate(CROSS_AIR_WAR_OPERATE_TYPE.MONSTER_VIE, vote_boss_type)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossAirWar.SelectRemind)
		end
	end
end

-------------------------- AirWarPartRender --------------------------
AirWarPartRender = AirWarPartRender or BaseClass(BaseRender)
function AirWarPartRender:__init()
end

function AirWarPartRender:LoadCallBack()
	if not self.reward_list_list then
		self.reward_list_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list_list:SetStartZeroIndex(true)
	end
end

function AirWarPartRender:ReleaseCallBack()
	if self.reward_list_list then
		self.reward_list_list:DeleteMe()
		self.reward_list_list = nil
	end
end

function AirWarPartRender:OnFlush()
	if not self.data then return end

	local monster_name = ''
	local monster_data = BossWGData.Instance:GetMonsterCfgByid(self.data.monster_id)
	if not IsEmptyTable(monster_data) then
		monster_name = monster_data.name
	end
	self.node_list["name_txt"].text.text = monster_name
	self.reward_list_list:SetDataList(self.data.reward_list)

	local bundle, asset = ResPath.GetBossUI(self.data.monster_head)
	if bundle and asset then
		self.node_list["boss_bg"].image:LoadSprite(bundle, asset, function()
			self.node_list["boss_bg"].image:SetNativeSize()
		end)
	end
end
