
KfOneVOnePrapareSceneLogic = KfOneVOnePrapareSceneLogic or BaseClass(CrossServerSceneLogic)

function KfOneVOnePrapareSceneLogic:__init()

end

function KfOneVOnePrapareSceneLogic:__delete()

end
function KfOneVOnePrapareSceneLogic:ReleaseCallBack()

end

-- 进入场景
function KfOneVOnePrapareSceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	if old_scene_type == SceneType.Common then --防止刚进场景就匹配到  把匹配界面关掉的情况
		ViewManager.Instance:CloseAll()
	end

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetBtnLevel(true)
	end)

	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))

	-- if MainuiWGCtrl.Instance.view then
	-- 	MainuiWGCtrl.Instance.view:SetAttackMode(ATTACK_MODE.PEACE)
	-- end
	local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_PERSON_INFO
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type)
  
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_MATCH_INFO)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_KNOCKOUT_MATCH_INFO)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_SCORE_RANK)

	Field1v1WGCtrl.Instance:OpenOneVOneTaskView()
--	MainuiWGCtrl.Instance:FlushKfPKRenPoint()
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN 
		and activity_info.next_time and activity_info.next_time > server_time then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time)
	end
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Kuafu1V1.ConfirmLevelFB)
	local flag = Field1v1WGCtrl.Instance:GetEnterPrematchMaxRound()
	if flag then
		KFOneVOneWGData.Instance:SetCanPlay16QiangAnim(true)
	end
    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
	if old_scene_type == SceneType.Kf_OneVOne then
		self:OnFlyDownEnd()
	end

	local open_onevone_result = Field1v1WGCtrl.Instance:GetOpenOneVOneResult()
	if old_scene_type == SceneType.Kf_OneVOne and new_scene_type == SceneType.Kf_OneVOne_Prepare and not open_onevone_result then
		local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
		local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
		if match_state == KFOneVOneWGData.MatchState.TaoTai and knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
			ViewManager.Instance:Open(GuideModuleName.KFOneVOneResultView)
			Field1v1WGCtrl.Instance:SetOpenOneVOneResult(true)
		end
	end
	ViewManager.Instance:AddMainUIFuPingChangeList(Field1v1WGCtrl.Instance:GetOnevOneTaskView())
end

function KfOneVOnePrapareSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self,old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		KF3V3WGCtrl.Instance:CheckOpenActEndView(new_scene_type, {act_type = ACTIVITY_TYPE.KF_ONEVONE})
	end

	MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelFB)
	MainuiWGCtrl.Instance:ShowMainuiMenu(true)
	MainuiWGCtrl.Instance:SetBtnLevel(false)
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	Field1v1WGCtrl.Instance:CloseOneVOneTaskView()
	GlobalEventSystem:UnBind(self.fly_down_end_event)
	self.fly_down_end_event = nil
	KFOneVOneWGData.Instance:SetMainRoleFlyDownFalg(false)
	-- if MainuiWGCtrl.Instance.view then
	-- 	local before_act_mode = GameVoManager.Instance:GetMainRoleVo().attack_mode or 0
	-- 	MainuiWGCtrl.Instance.view:SetAttackMode(before_act_mode)
	-- end
	Field1v1WGCtrl.Instance:CloseOneVOneMatchingView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(Field1v1WGCtrl.Instance:GetOnevOneTaskView())
end

function KfOneVOnePrapareSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end

	local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local enter_scene_knockout = KFOneVOneWGData.Instance:GetEnterSceneKnockout()
	KFOneVOneWGData.Instance:SetMainRoleFlyDownFalg(true)

	local can_paly_16_qiang_anim = KFOneVOneWGData.Instance:GetCanPlay16QiangAnim()
	local score_rank_paly_16qiang_anim = Field1v1WGCtrl.Instance:GetScoreRankPlay16QiangAnim()
	if score_rank_paly_16qiang_anim and can_paly_16_qiang_anim then
		Field1v1WGCtrl.Instance:PlayJingJiTips(KFOneVOneWGData.MatchState.YuXuan,KFOneVOneWGData.MatchState.YuXuanEnd)
	end

	if match_state == KFOneVOneWGData.MatchState.TaoTai and enter_scene_knockout then
		Field1v1WGCtrl.Instance:PlayJingJiTips(match_state,match_state,enter_scene_knockout)
	end

end

function KfOneVOnePrapareSceneLogic:IsEnemy()
	return false
end
