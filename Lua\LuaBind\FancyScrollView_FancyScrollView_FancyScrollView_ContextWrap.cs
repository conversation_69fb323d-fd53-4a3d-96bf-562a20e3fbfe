﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FancyScrollView_FancyScrollView_FancyScrollView_ContextWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FancyScrollView.FancyScrollView<FancyScrollView.Context>), typeof(UnityEngine.MonoBehaviour), "FancyScrollView_FancyScrollView_Context");
		<PERSON><PERSON>unction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("CellRefreshDel", get_CellRefreshDel, set_CellRefreshDel);
		<PERSON><PERSON>("CellSelectDel", get_CellSelectDel, set_CellSelectDel);
		<PERSON>.RegFunction("CellSelectDelegate", FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate);
		<PERSON><PERSON>un<PERSON>("CellRefreshDelegate", FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CellRefreshDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyScrollView<FancyScrollView.Context> obj = (FancyScrollView.FancyScrollView<FancyScrollView.Context>)o;
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate ret = obj.CellRefreshDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRefreshDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CellSelectDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyScrollView<FancyScrollView.Context> obj = (FancyScrollView.FancyScrollView<FancyScrollView.Context>)o;
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate ret = obj.CellSelectDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellSelectDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CellRefreshDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyScrollView<FancyScrollView.Context> obj = (FancyScrollView.FancyScrollView<FancyScrollView.Context>)o;
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate arg0 = (FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate)ToLua.CheckDelegate<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate>(L, 2);
			obj.CellRefreshDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRefreshDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CellSelectDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyScrollView<FancyScrollView.Context> obj = (FancyScrollView.FancyScrollView<FancyScrollView.Context>)o;
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate arg0 = (FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate)ToLua.CheckDelegate<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate>(L, 2);
			obj.CellSelectDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellSelectDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

