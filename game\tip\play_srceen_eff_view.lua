PlaySrceenEffView = PlaySrceenEffView or BaseClass(SafeBaseView)
function PlaySrceenEffView:__init()
    self:AddViewResource(0, "uis/view/common_effect_prefab", "layout_srceen_eff_view")
    self.view_layer = UiLayer.PopTop
    self.view_name = "PlaySrceenEffView"

    self.effect_type = 0
    self.dealy_close_time = 2
end

function PlaySrceenEffView:ReleaseCallBack()
    if self.close_timer then
        GlobalTimerQuest:CancelQuest(self.close_timer)
        self.close_timer = nil
    end
end

function PlaySrceenEffView:LoadCallBack()

end

function PlaySrceenEffView:CloseCallBack()
end

function PlaySrceenEffView:SetData(effect_type, dealy_close_time)
    if effect_type == nil then
        return
    end

    local old_type = self.effect_type
    self.effect_type = effect_type
    self.dealy_close_time = dealy_close_time or 2
    if not self:IsOpen() or not self:IsLoaded() then
        self:Open()
    else
        if old_type ~= nil and old_type == effect_type then
            return
        end

        self:PlayEffect()
    end
end

function PlaySrceenEffView:ShowIndexCallBack()
    self:PlayEffect()
end

function PlaySrceenEffView:PlayEffect()
    if self.close_timer then
        GlobalTimerQuest:CancelQuest(self.close_timer)
        self.close_timer = nil
    end

    local dealy_close_time = self.dealy_close_time
    local bundle = nil
    local asset = nil
    local cfg = SkillWGData.Instance:GetSkillScreenEffect(self.effect_type)
    if cfg then
        bundle, asset = cfg.bundle, cfg.asset
    end

    if bundle == nil or asset == nil then
        self:Close()
        return
    end

    EffectManager.Instance:PlaySingleAtTransform(bundle, asset, self.node_list.root_obj.transform, dealy_close_time - 0.5,
        Vector3(0, 0, 0), nil, nil)

    self.close_timer = GlobalTimerQuest:AddRunQuest(function()
        self:Close()
    end, dealy_close_time)
end

