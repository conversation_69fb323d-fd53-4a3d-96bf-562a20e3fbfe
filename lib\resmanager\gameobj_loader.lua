-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local GameObjLoader = GameObjLoader or BaseClass()
local LoaderLayer = GameObject.Find("GameRoot/LoaderLayer").transform
local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()
local type_rect_transform = typeof(UnityEngine.RectTransform)

local empty_tbl = {}
local update_loader_map = {}
local add_list = {}
local add_index = 0
GameObjLoader.loader_map = {}
function GameObjLoader._Update(now_time, elapse_time)
	if add_index > 0 then
		for i = 1, add_index do
			GameObjLoader.loader_map[add_list[i]] = true
			add_list[i] = empty_tbl
		end
		add_index = 0
	end

	for k,_ in pairs(GameObjLoader.loader_map) do
		if k:Update(now_time, elapse_time) then
			GameObjLoader.loader_map[k] = nil
		end
	end
end

function GameObjLoader.AddTimer(loader)
	add_index = add_index + 1
	add_list[add_index] = loader
end

function GameObjLoader:__init(parent_transform)
	self.name = "GameObjLoader"
	self.parent_transform = parent_transform or LoaderLayer

	self.is_active = true
	self.local_pos_v3 = nil
	self.local_scale_v3 = nil
	self.local_rotation_euler = nil
	self.is_wangqi_visable = nil

	self.is_use_objpool = nil
	self.release_policy = ResPoolReleasePolicy.Defualt
	self.is_optimize_effect = true
    self.is_async = true
    self.load_priority = ResLoadPriority.high
    self.is_reset_parent_transform = false
    self.cur_t = {}
    self.loading_t = {}
    self.wait_t = {}

    self.obj_alive_time = nil
    self.obj_alive_time_stamp = 0
end

function GameObjLoader:__delete()
	if not self.__is_had_del_in_cache then
		self.__is_had_del_in_cache = true
		if nil ~= self.__loader_key and nil ~= self.__loader_owner and nil ~= self.__loader_owner.__gameobj_loaders then
			self.__loader_owner.__gameobj_loaders[self.__loader_key] = nil
		end
	end

	self:Destroy()
	self.parent_transform = nil
	self.cur_t.bundle_name = nil
	self.wait_t.bundle_name = nil
	self.loading_t.bundle_name = nil
	self.is_wangqi_visable = nil

	if self.check_wangqi_status_fun then
		GlobalEventSystem:UnBind(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.check_wangqi_status_fun)
		self.check_wangqi_status_fun = nil
	end
end

-- 一个loader只加载1个对象，如果要删除该对象需调用loader的Destroy接口安全移除
-- 如果该对象已经被其他地方非法删除了，以下有记录instance_id对对象池进行清理操作
-- 如果该对象还在队列中，则从队列中取消
-- 如果有正加等待加载的对象，则该等待对象将不再加载
-- 强制清除parent_transform的引用
function GameObjLoader:Destroy(is_reset_parent_transform)
	self.loading_t.bundle_name = nil
	if nil ~= self.loading_t.queue_seesion then
		ResPoolMgr:__CancleGetInQueue(self.loading_t.queue_seesion)
		self.loading_t.queue_seesion = nil
	end

	self.wait_t.bundle_name = nil
	self.is_wangqi_visable = nil

	if nil ~= self.cur_t.bundle_name then
		local gameobj = self.cur_t.gameobj
		local instance_id = self.cur_t.instance_id

		self.cur_t.bundle_name = nil
		self.cur_t.prefab_name = nil
		self.cur_t.gameobj = nil
		self.cur_t.instance_id = 0

		if IsNil(gameobj) then
			if self.is_use_objpool then
				ResPoolMgr:ReleaseInObjId(instance_id)
			else
				ResMgr:ReleaseInObjId(instance_id)
			end
		else
			self:_DestoryObj(gameobj)
		end
	end

	if is_reset_parent_transform then
		self.parent_transform = LoaderLayer
	end
end

function GameObjLoader:_DestoryObj(obj)
	if IsNil(obj) then
		return
	end

	if self.is_use_objpool then
        ResPoolMgr:Release(obj, self.release_policy)
	else
        ResMgr:__Destroy(obj)
	end
end

function GameObjLoader:GetGameObj()
	return self.cur_t.gameobj
end

function GameObjLoader:Update(now_time, elapse_time)
	if now_time >= self.obj_alive_time_stamp then
		self:Destroy()
		return true
	end

	return false
end

function GameObjLoader:_DelayDelObj()
	if nil ~= self.obj_alive_time then
		self.obj_alive_time_stamp = self.obj_alive_time + Status.NowTime
		GameObjLoader.AddTimer(self)
	end
end

function GameObjLoader:SetObjAliveTime(obj_alive_time)
	self.obj_alive_time = obj_alive_time
	self:_DelayDelObj()
end

function GameObjLoader:SetIsASyncLoad(is_async)
	self.is_async = is_async
end

function GameObjLoader:SetLoadPriority(load_priority)
	if nil ~= load_priority then
		self.load_priority = load_priority
	end
end

-- 纯为了兼容老包，新包其实可以不再调用
function GameObjLoader:SetIsOptimizeEffect(is_optimize_effect)
	self.is_optimize_effect = is_optimize_effect
end

function GameObjLoader:SetIsUseObjPool(is_use_objpool, release_policy)
	if nil ~= self.is_use_objpool and self.is_use_objpool ~= is_use_objpool then
		print_error("[GameObjLoader] SetIsUseObjPool该接口只允许设置1次，以免引起不必要Bug")
		return
	end

	self.is_use_objpool = is_use_objpool
	self.release_policy = release_policy or ResPoolReleasePolicy.Defualt
end

function GameObjLoader:SetReleasePolicy(release_policy)
	self.release_policy = release_policy
end

function GameObjLoader:SetIsInQueueLoad(is_in_queue)
	if is_in_queue then
		self.load_priority = ResLoadPriority.low
	end
end

function GameObjLoader:SetParent(parent_transform)
	if not IsNil(parent_transform) then
		if nil == parent_transform.SetParent then -- 可能会传入一个非Transform的参数,故意报错出来
			print_error("[严重bug]SetParent传入了一个非Transform的参数！")
		end
	end

	if self.parent_transform ~= parent_transform then
		self.is_reset_parent_transform = true
	end

	self.parent_transform = parent_transform
end

function GameObjLoader:SetLocalPosition(local_pos_v3)
	self.local_pos_v3 = local_pos_v3

	if self.cur_t ~= nil and not IsNil(self.cur_t.gameobj) then
		 self.cur_t.gameobj.transform.localPosition = self.local_pos_v3
	end
end

function GameObjLoader:SetLocalScale(local_scale_v3)
	self.local_scale_v3 = local_scale_v3

	if self.cur_t ~= nil and not IsNil(self.cur_t.gameobj) then
		self.cur_t.gameobj.transform.localScale = local_scale_v3
	end
end

function GameObjLoader:SetLocalRotation(local_rotation_euler)
	self.local_rotation_euler = local_rotation_euler

	if self.cur_t ~= nil and not IsNil(self.cur_t.gameobj) then
		self.cur_t.gameobj.transform.localRotation = local_rotation_euler
	end
end

function GameObjLoader:SetActive(active)
	if self.is_active ~= active then
		self.is_active = active
		if self.cur_t ~= nil and not IsNil(self.cur_t.gameobj) then
			self.cur_t.gameobj:SetActive(active)
		end
	end
end

function GameObjLoader:ReLoad(bundle_name, prefab_name, load_callback, cbdata)
	self:Destroy()
	self:Load(bundle_name, prefab_name, load_callback, cbdata)
end

function GameObjLoader:Load(bundle_name, prefab_name, load_callback, cbdata)
	if nil == bundle_name or "" == bundle_name
        or nil == prefab_name or "" == prefab_name then
        return
    end

    -- 若资源已存在则直接回调处理
	if nil ~= self.cur_t
		and self.cur_t.bundle_name == bundle_name
		and self.cur_t.prefab_name == prefab_name
		and not IsNil(self.cur_t.gameobj) then

		self:OnLoadSucc(self.cur_t.gameobj)
		if nil ~= load_callback then
			load_callback(self.cur_t.gameobj, cbdata)
		end
		return
	end

	-- 若请求的资源正在加载中则不处理
	if self.loading_t.bundle_name == bundle_name and self.loading_t.prefab_name == prefab_name then
		-- 这个时候正在加载的资源已经是最新的了，要清除等待列表
		self.wait_t.bundle_name = nil
		self.wait_t.prefab_name = nil
		self.wait_t.load_callback = nil
		self.wait_t.cbdata = nil
		return
	end

	if is_develop then
		if not EditorResourceMgr.IsExistsAsset(bundle_name, prefab_name) then
	        print_error("加载不存在资源，马上检查!!!", bundle_name, prefab_name)
	        return
		end
	end

    -- 如果正在加载则等待
    if nil ~= self.loading_t.bundle_name then
    	self.wait_t.bundle_name = bundle_name
    	self.wait_t.prefab_name = prefab_name
    	self.wait_t.load_callback = load_callback
    	self.wait_t.cbdata = cbdata
    else
        self:Destroy()
        self.loading_t.bundle_name = bundle_name
        self.loading_t.prefab_name = prefab_name
        self.loading_t.load_callback = load_callback
        self.loading_t.cbdata = cbdata
        self:DoLoad()
    end
end

function GameObjLoader:DoLoad()
	if nil == self.parent_transform or self.parent_transform == LoaderLayer then
		if is_develop then
	        develop_mode:OnLuaCall("call_invalid_param", "在加载前必须有具有parent_transform,请在Load之前通过SetParent指定父节点。提高效率的同时更因为写逻辑的人经常因为parent被提前移除而没释放导致内存泄漏，防不胜防!!!。马上修改!!!!")
	    end
	end

	-- 监测加载
	if is_develop then
		develop_mode:OnLuaCall("gameobj_loader_load", self, self.loading_t.bundle_name, self.loading_t.prefab_name)
	end

	self.is_reset_parent_transform = false
	local cbdata = GameObjLoader.GetCBData()
	cbdata[1] = self

    if self.is_use_objpool then
    	if self.is_async then
			self.loading_t.queue_seesion = ResPoolMgr:__GetDynamicObjAsync(
		            self.loading_t.bundle_name,
		            self.loading_t.prefab_name,
		            GameObjLoader.LoadComplete,
		            self.parent_transform,
		            self.load_priority,
		            cbdata)
    	else
    		ResPoolMgr:__GetDynamicObjSync(
	            self.loading_t.bundle_name,
	            self.loading_t.prefab_name,
	            GameObjLoader.LoadComplete,
			    self.parent_transform,
			    cbdata)
    	end
    else
    	if self.is_async then
    		ResMgr:LoadGameobjAsync(
	            self.loading_t.bundle_name,
	            self.loading_t.prefab_name,
	            GameObjLoader.LoadComplete,
			    self.parent_transform,
			    cbdata,
	            self.load_priority)
    	else
			ResMgr:LoadGameobjSync(
	            self.loading_t.bundle_name,
	            self.loading_t.prefab_name,
	            GameObjLoader.LoadComplete,
			    self.parent_transform,
			    cbdata)
    	end
    end
end

 -- 当加载完后检查此次加载返回上请求的是不是同一个。
 -- 如果不是（即外部已发起新的请求或者加载器已删除），则释放掉当前加载的，再进行新的加载
function GameObjLoader.LoadComplete(gameobj, cbdata)
	local self = cbdata[1]
	GameObjLoader.ReleaseCBData(cbdata)

	local bundle_name = self.loading_t.bundle_name
	local prefab_name = self.loading_t.prefab_name
	local load_callback = self.loading_t.load_callback
	local up_cbdata = self.loading_t.cbdata

	self.loading_t.bundle_name = nil
	self.loading_t.prefab_name = nil
	self.loading_t.load_callback = nil
	self.loading_t.cbdata = nil
	self.loading_t.queue_seesion = nil

	 -- 如果是有等待加载的资源则释放当前加载的,并执行新的加载
	if nil ~= self.wait_t.bundle_name then
		self:_DestoryObj(gameobj)
		self.loading_t.bundle_name = self.wait_t.bundle_name
		self.loading_t.prefab_name = self.wait_t.prefab_name
		self.loading_t.load_callback = self.wait_t.load_callback
		self.loading_t.cbdata = self.wait_t.cbdata
		self.wait_t.bundle_name = nil
		self.wait_t.prefab_name = nil
		self.wait_t.load_callback = nil
		self.wait_t.cbdata = nil

        self:DoLoad()
        return
	end

	-- 如果是空对象则，这里不敢再调callback了？？
	if IsNil(gameobj) then
		return
	end

	-- 如果父节点已释放或者被调了DeleteMe后则直接删除obj
	if IsNil(self.parent_transform) or nil == bundle_name then
		self:_DestoryObj(gameobj)
		return
	end

	self.cur_t.bundle_name = bundle_name
	self.cur_t.prefab_name = prefab_name
	self.cur_t.gameobj = gameobj
	self.cur_t.instance_id = gameobj:GetInstanceID()

	self:OnLoadSucc(gameobj)
	self:TryOptimizeEffect(gameobj)

	if nil ~= load_callback then
		load_callback(gameobj, up_cbdata)
	end
end

function GameObjLoader:OnLoadSucc(gameobj)
	if self.is_reset_parent_transform and not IsNil(self.parent_transform) then
		self.is_reset_parent_transform = false
		gameobj.transform:SetParent(self.parent_transform, false)
	end

	-- 监测加载完成
	if is_develop then
		develop_mode:OnLuaCall("gameobj_loader_load_complete", self, self.cur_t.bundle_name, self.cur_t.prefab_name, self.cur_t.gameobj)
	end

	if nil ~= self.local_pos_v3 then
		gameobj.transform.localPosition = self.local_pos_v3
	end

	if nil ~= self.local_scale_v3 then
		gameobj.transform.localScale = self.local_scale_v3
	end

	if nil ~= self.local_rotation_euler then
		gameobj.transform.localRotation = self.local_rotation_euler
	end

	gameobj:SetActive(self.is_active)

	self:_DelayDelObj()
end

-- 粒子特效都会使用对象池加载(有检查机制)，而层级调整针对的也是粒子特效。这里取巧用is_use_objpool减少计算
function GameObjLoader:TryOptimizeEffect(gameobj)
	if self.is_use_objpool and self.is_optimize_effect then
		if nil == gameobj:GetComponentInParent(type_rect_transform) then
			EffectOrderGroup.RefreshRenderOrder(gameobj)
		end
	end
end

GameObjLoader.cbdata_list = {}
function GameObjLoader.GetCBData()
    local cbdata = table.remove(GameObjLoader.cbdata_list)
    if nil == cbdata then
        cbdata = {true}
    end

    return cbdata
end

function GameObjLoader.ReleaseCBData(cbdata)
    cbdata[1] = true
    table.insert(GameObjLoader.cbdata_list, cbdata)
end

function GameObjLoader:SetIsWangQiVisable(is_wangqi_visable)
	self.is_wangqi_visable = is_wangqi_visable

	if self.is_wangqi_visable then
		self.check_wangqi_status_fun = BindTool.Bind(self.CheckSceneWangqiStatus, self)
		GlobalEventSystem:Bind(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.check_wangqi_status_fun)
		self:CheckSceneWangqiStatus()
	end
end

-- 望气展示效果
function GameObjLoader:CheckSceneWangqiStatus(is_wangqi)
	if not self.is_wangqi_visable then
		return
	end

	local is_now_wangqi = is_wangqi or Scene.Instance:IsEnterWangQiStatus()
	--是否望气显示
	self:SetActive(is_now_wangqi)
end

return GameObjLoader