MainUIViewExp = MainUIViewExp or BaseClass(BaseRender)

function MainUIViewExp:__init()

end

function MainUIViewExp:LoadCallBack()
	-- 找到要控制的变量
	-- self.exp = self:FindVariable("Exp")
	-- self.text_exp = self:FindVariable("TextExp")
	-- self.effect = self:FindVariable("Effect")

	-- 属性事件处理
	self.attr_handlers = {
		exp = BindTool.Bind1(self.OnExpChanged, self),
		-- max_exp = BindTool.Bind1(self.OnExpChanged, self),
	}

	-- 监听系统事件
	self.player_data_change_callback = BindTool.Bind1(self.PlayerDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.player_data_change_callback, {"exp"})

	-- 首次刷新数据
	self:OnExpInitialized()
end

function MainUIViewExp:__delete()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
	
	self.exp_effect = nil
end

function MainUIViewExp:ReleaseCallBack()
	RoleWGData.Instance:UnNotifyAttrChange(self.player_data_change_callback)

	if self.text_exp then
		self.text_exp = nil
	end

	self.old_level = nil
	self.old_exp_value = nil
	self.old_max_exp_value = nil
end

function MainUIViewExp:PlayerDataChangeCallback(attr_name, value, old_value)
	local handler = self.attr_handlers[attr_name]
	if handler ~= nil then
		handler()
	end
end

function MainUIViewExp:SetExpInfoTextNode( node )
	self.text_exp = node
	self:OnExpInitialized()
end

function MainUIViewExp:SetExpProgressVal( val )
	self.view.slider.value = val
end

function MainUIViewExp:SetExpTextVal( str )
	if self.text_exp then
		self.text_exp.text.text = str
	end
end

function MainUIViewExp:OnExpInitialized(is_not_show_effect)
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local max_exp = RoleWGData.Instance.GetRoleExpCfgByLv(vo.level).exp
	local now_exp = vo.exp
	local now_lv = vo.level
	local slider = self.view.slider
	if self.old_exp_value == nil or self.old_level == nil then
		self:SetFinalExpChange(now_lv, now_exp, max_exp)
	else
		if self.old_level == now_lv and self.old_exp_value == now_exp then
			return
		end

		if self.old_level > now_lv then
			self:SetFinalExpChange(now_lv, now_exp, max_exp)
			return
		end

		if self.old_level < now_lv then
			self:ShowExpEffectNode(true)
			slider:DOValue(1, 0.5):OnComplete(function()
				slider.value = 0
				self:ShowExpEffectNode(false)
				self:SetFinalExpChange(now_lv, now_exp, max_exp)--self.old_level + 1
				self:OnExpInitialized(true)	-- 重新检测
			end)
		else
			if not is_not_show_effect then
				self:ShowExpEffectNode(true)
			end

			slider:DOValue(now_exp / max_exp, 0.5):OnComplete(function()
				self:ShowExpEffectNode(false)
				self:SetFinalExpChange(now_lv, now_exp, max_exp)
			end)
		end
	end
end

-- 设置最终值
function MainUIViewExp:SetFinalExpChange(now_lv, now_exp, max_exp)
	self:SetExpProgressVal(now_exp / max_exp)
	--不需要显示具体数值
	-- self:SetExpTextVal(CommonDataManager.ConverExp(BigNumFormat(vo.exp)) .. " / " .. CommonDataManager.ConverExp(BigNumFormat(max_exp)))
	self.old_level = now_lv
	self.old_exp_value = now_exp
	self.old_max_exp_value = max_exp
	-- 赏金任务经验特效播放
	TaskWGCtrl.Instance:PlayEffect1()
end

function MainUIViewExp:OnExpChanged()
	-- self:SetAutoTalkTime()
	self:OnExpInitialized()
end

function MainUIViewExp:SetAutoTalkTime()
	if self.count_down then return end
	self.count_down = CountDown.Instance:AddCountDown(0.6, 0.1, BindTool.Bind(self.CountDown, self))
end

function MainUIViewExp:CountDown(elapse_time, total_time)
	if total_time - elapse_time <= 0 then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function MainUIViewExp:SetEffectNode(exp_effect)
	self.exp_effect = exp_effect
end

function MainUIViewExp:ShowExpEffectNode(is_show)
	if self.exp_effect then
		self.exp_effect:CustomSetActive(is_show)
	end
end

