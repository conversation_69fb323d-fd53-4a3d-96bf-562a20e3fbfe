HolyHeavenlyDomainMoBaiSceneLogic = HolyHeavenlyDomainMoBaiSceneLogic or BaseClass(CommonFbLogic)

function HolyHeavenlyDomainMoBaiSceneLogic:__init()
	self.update_elaspe_time = 0
end

function HolyHeavenlyDomainMoBaiSceneLogic:__delete()
	self.is_gather = nil
	self.update_elaspe_time = nil

	if self.main_role_pos_change then
        GlobalEventSystem:UnBind(self.main_role_pos_change)
        self.main_role_pos_change = nil
    end

	if self.scene_loading_state_quit then
        GlobalEventSystem:UnBind(self.scene_loading_state_quit)
        self.scene_loading_state_quit = nil
    end
end

function HolyHeavenlyDomainMoBaiSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.HolyHeavenlyDomain.HHDMoBaiFBName)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		HolyHeavenlyDomainWGCtrl.Instance:OpenMobaiTaskView()
	end)

	self.is_gather = false
	self.update_elaspe_time = 0

	self.main_role_pos_change = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRolePosChange,self))
	self.scene_loading_state_quit = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeComplete, self))
end

function HolyHeavenlyDomainMoBaiSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	HolyHeavenlyDomainWGCtrl.Instance:CloseMobaiTaskView()

	if self.gather_complete_event then
		GlobalEventSystem:UnBind(self.gather_complete_event)
		self.gather_complete_event = nil
	end

	self.scene_enter_complete = false
	self:ClearAllHolyBeastCallWorshipStatue()

	if self.main_role_pos_change then
        GlobalEventSystem:UnBind(self.main_role_pos_change)
        self.main_role_pos_change = nil
    end

	if self.scene_loading_state_quit then
        GlobalEventSystem:UnBind(self.scene_loading_state_quit)
        self.scene_loading_state_quit = nil
    end
end

function HolyHeavenlyDomainMoBaiSceneLogic:OnSceneChangeComplete(old_scene_type, new_scene_type)
	if new_scene_type == SceneType.CROSS_DIVINE_DOMAIN_MOBAI then
		self:CreateHolyBeastCallWorshipStatue()
	end
end

function HolyHeavenlyDomainMoBaiSceneLogic:OnMainRolePosChange(role_x, role_y)
	self:CreateHolyBeastCallWorshipStatue()

	self:BreakMoBai()
end

function HolyHeavenlyDomainMoBaiSceneLogic:CreateHolyBeastCallWorshipStatue()
	if not self.holy_beastcall_worship then
		self.holy_beastcall_worship = {}
	end

	local scene_type = Scene.Instance:GetSceneType()

    if scene_type == SceneType.CROSS_DIVINE_DOMAIN_MOBAI then
        local clear_worship_obj = function(index)
            local worship = self.holy_beastcall_worship[index]

            if nil ~= worship then
                if not worship:IsDeleted()  then
                    Scene.Instance:DeleteObj(worship:GetObjId(), 0)
                    self.holy_beastcall_worship[index] = nil
                end
            end
        end

        for i = 1, 5 do
            local info = HolyHeavenlyDomainWGData.Instance:GetWorshipVoByIndex(i)
			local worship_pos_cfg = HolyHeavenlyDomainWGData.Instance:GetWorShipCfgByIndex(i)

            if not IsEmptyTable(info) then
				if nil == self.holy_beastcall_worship[i] then
					local vo = GameVoManager.Instance:CreateVo(CityOwnerStatueVo)
					vo.pos_x = worship_pos_cfg.pos_x
					vo.pos_y = worship_pos_cfg.pos_y
					vo.worship = i
					vo.statue_type = StatueType.HolyBeastCallWorship
					self.holy_beastcall_worship[i] = Scene.Instance:CreateObj(vo, SceneObjType.CityOwnerStatue)
				end
            else
                clear_worship_obj(i)
            end
        end
    else
		self:ClearAllHolyBeastCallWorshipStatue()
    end
end

function HolyHeavenlyDomainMoBaiSceneLogic:ClearAllHolyBeastCallWorshipStatue()
	if self.holy_beastcall_worship then
		for i = 1, 5 do
			local worship = self.holy_beastcall_worship[i]

			if nil ~= worship then
				if not worship:IsDeleted() then
					Scene.Instance:DeleteObj(worship:GetObjId(), 0)
					self.holy_beastcall_worship[i] = nil
				end
			end
		end
	end

	self.holy_beastcall_worship = nil
end

-- 圣天神域雕像
function HolyHeavenlyDomainMoBaiSceneLogic:UpdateHolyBeastCallWorshipStatue()
    for i = 1, 5 do
        if self.holy_beastcall_worship[i] then
            self.holy_beastcall_worship[i]:UpdateStatue()
        end
    end
end

--移动到挖宝的地点
function HolyHeavenlyDomainMoBaiSceneLogic:GoToWorshipPos()
	local worship_data = HolyHeavenlyDomainWGData.Instance:GetCurWorShipCfg()
	
	if IsEmptyTable(worship_data) then
		return
	end

	local main_vo = Scene.Instance:GetMainRole()
	if main_vo and main_vo:GetIsGatherState() then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	local pox_x, pos_y = worship_data.cfg.gather_pos_x,  worship_data.cfg.gather_pos_y

	local gather_time = 2.5
	local move_to_pos_call_back = function ()
		GatherBar.Instance:SetGatherTime(gather_time, true) 								--界面采集动画时长
		GatherBar.Instance:Open()
		GlobalEventSystem:Fire(ObjectEventType.START_GATHER) 								--播放界面采集动画
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)		--更改寻路状态
		
		if main_vo then
			main_vo:SetIsGatherState(true, 0) --播放模型采集动画
		end

		self.gather_timer = GlobalTimerQuest:AddDelayTimer(function ()
			if not main_vo:IsDeleted() then
				main_vo:SetIsGatherState(false, 0) --播放模型采集动画
				HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.WORSHIP, worship_data.info.rank, worship_data.info.uid)
			end
		end, gather_time)
	end

	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(move_to_pos_call_back )
	GuajiWGCtrl.Instance:MoveToPos(scene_id, pox_x, pos_y, 1)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function HolyHeavenlyDomainMoBaiSceneLogic:BreakMoBai()
	local main_vo = Scene.Instance:GetMainRole()
	if main_vo and main_vo:GetIsGatherState() then
		main_vo:SetIsGatherState(false, 0) --播放模型采集动画
		GlobalEventSystem:Fire(ObjectEventType.STOP_GATHER)
		GatherBar.Instance:Close()

		if self.gather_timer ~= nil then
			GlobalTimerQuest:CancelQuest(self.gather_timer)
			self.gather_timer = nil
		end
	end
end