FunctionChooseView = FunctionChooseView or BaseClass(SafeBaseView)
local RENDER_WIDTH = 150 --一个子物体的宽度
local RENDER_HIGHT = 236 --高度

function FunctionChooseView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
	self.ten_list_view = nil
	self.bind_func = BindTool.Bind1(self.ItemChangeCallBack, self)
end

function FunctionChooseView:__delete()

end

function FunctionChooseView:ReleaseCallBack()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
	if nil ~= self.ten_list_view then
		self.ten_list_view:DeleteMe()
		self.ten_list_view = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.bind_func)
end

function FunctionChooseView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/choose_gift_prefab", "choose_gift_view")
	self.cfg = ConfigManager.Instance:GetAutoConfig("equipment_star_auto")
end

function FunctionChooseView:LoadCallBack()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.bind_func)
	self.ten_list_view = AsyncListView.New(ChoosegiftRender, self.node_list["listview"])

	if self.node_list.top_title_bg then
		self.node_list.top_title_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_gxhd_djhd_ryxz"))
	end
end

function FunctionChooseView:ItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self:Flush(0)
end

function FunctionChooseView:ShowIndexCallBack()
	self:Flush(0)
end

function FunctionChooseView:OnFlush()
	local num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
	self.second_gift_cfg = self.cfg.gift_equipment_star[self.data.id]
	local data = self.data
	if self.data.id then
		data = self.data.item_data
	end

	local count = #data
	--超过8个格子读8个格子的宽度
	if count < 8 and count > 0 then
		local width = RENDER_WIDTH * count
		self.node_list["listview"]:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta = Vector2(width, RENDER_HIGHT)
	else
		local width = RENDER_WIDTH * 7.5
		self.node_list["listview"]:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta = Vector2(width, RENDER_HIGHT)
	end
	if self.is_hide_btn then
		self.node_list["choose_text"].text.text = Language.ChooseGift.ChooseText2
	else
		self.node_list["choose_text"].text.text = Language.ChooseGift.ChooseText1
	end
	self.ten_list_view:SetDataList(data)
	if not self.is_hide_btn and num <= 0 then
		self:Close()
	end
end

function FunctionChooseView:SetData(index, num, data, item_id, is_hide_btn)
	self.data = data
	self.gift_index = index
	self.select_num = num
	self.item_id = item_id
	self.is_hide_btn = is_hide_btn
	self:Open()
end

ChoosegiftRender = ChoosegiftRender or BaseClass(BaseRender)
function ChoosegiftRender:__init()

end

function ChoosegiftRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function ChoosegiftRender:LoadCallBack()
	self.cell = ItemCell.New(self.node_list["cell_parent"])
	self.cell:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
	XUI.AddClickEventListener(self.node_list["btn_choose"], BindTool.Bind1(self.OnClickFuWenTips, self))
	XUI.AddClickEventListener(self.node_list["shenshou_btn"], BindTool.Bind1(self.OnClickShenShouBtn, self))
end

function ChoosegiftRender:OnFlush()
	if not self.data then return end
	local item_data = self.data.item_data
	local view = FunctionChooseWGCtrl.Instance.view
	if view and view.is_hide_btn then
		self.node_list["btn_choose"]:SetActive(false)
	else
		self.node_list["btn_choose"]:SetActive(true)
	end
	self.node_list.cell_hava_text.text.text = ""
	if item_data then
		self.cell:SetData({item_id = item_data.id, num = item_data.num,grade_level = 1, is_bind = item_data.is_bind, param = item_data.param})
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.id)
		local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		self.node_list.cell_name.text.text = name
	else
		local temp_data = __TableCopy(self.data)
		temp_data.grade_level = 1
		temp_data.is_bind = temp_data.isbind
		self.cell:SetData(temp_data)
		local temp_cfg = ItemWGData.Instance:GetItemConfig(temp_data.item_id)
		local name = ToColorStr(temp_cfg.name, ITEM_COLOR[temp_cfg.color])
		self.node_list.cell_name.text.text = name
		if ShenShouWGData.Instance:GetIsShenShouEquip(self.data.item_id) then
			self.cell:SetLeftTopImg(self.data.star_count or 0)
			self.cell:SetLeftTopTextVisible(false)
		end
		-- 图鉴礼包显示处理
		if not temp_data.item_id then
			return
		end

		local cfg = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(temp_data.item_id)
		local longzhu_flag = LongZhuWGData.Instance:IsLongZhuStuff(temp_data.item_id)
		local mingwen_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(temp_data.item_id)
		if cfg then
			local server_info = ShanHaiJingWGData.Instance:GetTJAllServerInfo()[cfg.seq]
			local num = ShanHaiJingWGData.Instance:GetItemNumInBagById(cfg.active_item_id)
			if (num > 0) or (server_info and server_info.level > 0) then
				self.node_list.cell_hava_text.text.text = Language.ShanHaiJing.State_Str1
			else
				self.node_list.cell_hava_text.text.text = Language.ShanHaiJing.State_Str2
			end
		elseif longzhu_flag then
			local longzhu_activate = LongZhuWGData.Instance:IsActiveByItemId(temp_data.item_id)
			local longzhu_num = ItemWGData.Instance:GetItemNumInBagById(temp_data.item_id)
			if longzhu_num > 0 and not longzhu_activate then
				self.node_list.cell_hava_text.text.text = string.format(Language.LongZhu.TipsHave, longzhu_num)
			end
			
			if longzhu_activate then
				local longzhu_level = LongZhuWGData.Instance:GetLongZhuLevelByItemId(temp_data.item_id)
				self.node_list.cell_hava_text.text.text = string.format(Language.LongZhu.TipsLevel, longzhu_level)
			end

			if not longzhu_activate and longzhu_num <= 0 then
				self.node_list.cell_hava_text.text.text = Language.ShanHaiJing.State_Str2
			end
		elseif mingwen_cfg then
			
			local mingwen_active = MingWenWGData.Instance:IsActive(temp_data.item_id)
			local mingwen_num =  MingWenWGData.Instance:GetMingWenBagItemNum(temp_data.item_id)
			if mingwen_active then
				self.node_list.cell_hava_text.text.text = Language.MingWenView.InsertStr
			elseif mingwen_num > 0  then
				self.node_list.cell_hava_text.text.text = string.format(Language.MingWenView.TipsHave, mingwen_num)
			else
				self.node_list.cell_hava_text.text.text = Language.MingWenView.NoInsert
			end
		else	
			--(外观， 骑宠， 宝宝)
			local check_item_id = temp_data.item_id 
			local part_type = NewAppearanceWGData.Instance:GetFashionTypeByItemId(check_item_id)	
			local fashion_level = NewAppearanceWGData.Instance:GetFashionLevelByItemId(check_item_id)
			local desc = ""
			local item_num = ItemWGData.Instance:GetItemNumInBagById(check_item_id)
			if part_type and part_type >= 0 then
				if fashion_level > 0 then
					if fashion_level - 1 == 0 then
						desc = Language.Tip.FashionState2[1] --"状态：已激活"
					else
						desc = string.format(Language.Tip.FashionState2[3], fashion_level - 1) -- 多少星
					end
				else
					desc = item_num > 0 and string.format(Language.Tip.FashionState2[2], item_num) or Language.Tip.FashionState2[4]--没激活拥有多少数量
				end
			elseif part_type == -1 then
				local is_active = false
				if NewAppearanceWGData.Instance:GetQiChongTypeByItemId(check_item_id) > -1 then
					local qc_type, image_id = NewAppearanceWGData.Instance:GetQiChongTypeByItemId(check_item_id)
					is_active = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(check_item_id)
					if is_active then
						local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(qc_type, image_id)
						local qc_star_level = info and (info.star_level or info.star) or 0
						if qc_star_level <= 0  then
							desc = Language.Tip.FashionState2[1] --"状态：已激活"
						else
							desc = string.format(Language.Tip.FashionState2[3], qc_star_level) -- 多少星
						end
					else
						desc =  item_num > 0 and string.format(Language.Tip.FashionState2[2], item_num) or Language.Tip.FashionState2[4]--没激活拥有多少数量
					end
				elseif MarryWGData.Instance:IsBabyActiveCard(check_item_id) then
					is_active = MarryWGData.Instance:GetBabyActiveByItemId(check_item_id)
					if is_active then
						desc = Language.Tip.FashionState2[1] --"状态：已激活"
					else
						desc =  item_num > 0 and string.format(Language.Tip.FashionState2[2], item_num) or Language.Tip.FashionState2[4]--没激活拥有多少数量
					end
				end
			end

			self.node_list.cell_hava_text.text.text = desc
		end
	end

	self.node_list.shenshou_btn:SetActive(ShenShouWGData.Instance:GetIsShenShouEquip(self.data.item_id))
end

function ChoosegiftRender:OnClickFuWenTips()
	self.select_item_index = {}
	for i = 0, GameEnum.SELECT_ITEM_MAX_NUM - 1 do
		self.select_item_index[i] = 0
	end

	local view = FunctionChooseWGCtrl.Instance.view
	local data_list = view.second_gift_cfg
	if data_list and self.data.param then
		for i=1, GameEnum.SELECT_ITEM_MAX_NUM do
			if self.data.item_id == data_list["item_" .. i .. "_id"] then
				self.select_item_index[i - 1] = 1
				break
			end
		end
	else
		self.select_item_index[self.index - 1] = 1
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(view.item_id)
	if has_num > 1 then
		local send_fun = function (cur_num)
			BagWGCtrl.Instance:SendUseItem(view.gift_index, cur_num, 0, 0, self.select_item_index)
			ViewManager.Instance:FlushView(GuideModuleName.ChooseGiftView)
		end
		local item_data = self.data.item_data
		local item_id = 0
		if item_data then
			item_id = item_data.id
		else
			item_id = self.data.item_id
		end
		
		local gift_id = view.item_id
		local select_gift_data = ItemWGData.Instance:GetItemListInGift(gift_id)
		local data_currect = false
		for k,v in pairs(select_gift_data) do
			if v.item_id == item_id then
				data_currect = true
				break
			end
		end
		if data_currect then

		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.BagDataChange)
			ViewManager.Instance:Close(GuideModuleName.ChooseGiftView)
			return
		end

		FunctionChooseWGCtrl.Instance:OpenBatchUseChooseGift(view.item_id, item_id, 1, 1, send_fun, view.gift_index)
	else
		BagWGCtrl.Instance:SendUseItem(view.gift_index, 1, 0, 0, self.select_item_index)
		ViewManager.Instance:FlushView(GuideModuleName.ChooseGiftView)
	end
end

function ChoosegiftRender:OnClickShenShouBtn()
	ShenShouWGCtrl.Instance:OpenShenShouEquipTip(self.data, ShenShouEquipTip.FROM_EQUIMENT_HECHENG)
end
