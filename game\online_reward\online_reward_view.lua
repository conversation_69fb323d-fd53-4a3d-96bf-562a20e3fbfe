OnlineRewardView = OnlineRewardView  or BaseClass(SafeBaseView)
function OnlineRewardView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/online_reward_ui_prefab", "layout_online_reward")
end

function OnlineRewardView:__delete()

end

function OnlineRewardView:LoadCallBack()
    self.reward_list = AsyncListView.New(OnlineRewardRender, self.node_list["reward_list"])
    self.reward_list:SetStartZeroIndex(true)

    local remain_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ONLINE_REWARD)
    if remain_time > 0 then
        self.act_timer = CountDown.Instance:AddCountDown(remain_time, 0.5,
        function(elapse_time, total_time)
            local time = math.floor(total_time - elapse_time)
            self:ShowActTime(time)
        end,
        function()
            self:ShowActTime(0)
        end)
    else
        self:ShowActTime(0)
    end
end

function OnlineRewardView:ReleaseCallBack()
    if self.act_timer and CountDown.Instance:HasCountDown(self.act_timer) then
        CountDown.Instance:RemoveCountDown(self.act_timer)
        self.act_timer = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function OnlineRewardView:OnFlush()
    local cfg_list = OnlineRewardWGData.Instance:GetRewardList()
    self.reward_list:SetDataList(cfg_list)

    local cfg = OnlineRewardWGData.Instance:GetRewardCfg()
    if cfg then
        local cur_seq = OnlineRewardWGData.Instance:GetCurSeq()
        self.reward_list:JumpToIndex(cur_seq, 3)
    end
end

function OnlineRewardView:ShowActTime(time)
	local time_str = TimeUtil.FormatSecondDHM9(time)
	if self.node_list["remain_time_desc"] then
		self.node_list["remain_time_desc"].text.text = time_str
	end
end




OnlineRewardRender = OnlineRewardRender or BaseClass(BaseRender)
function OnlineRewardRender:LoadCallBack()
    self.reward_item = ItemCell.New(self.node_list["item"])
    XUI.AddClickEventListener(self.node_list["btn_get"], BindTool.Bind(self.OnClickGetReward, self))
end

function OnlineRewardRender:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function OnlineRewardRender:OnFlush()
    local cur_seq = OnlineRewardWGData.Instance:GetCurSeq()
    local reward_status = OnlineRewardWGData.Instance:GetRewardSatus(self.data.seq)
    self.reward_item:SetData(self.data.item)
    self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item.item_id)

    local is_un_done = reward_status == REWARD_STATE_TYPE.UNDONE
    self.node_list["btn_get"]:CustomSetActive(not is_un_done)
    self.node_list["no_enough"]:CustomSetActive(is_un_done)

    local is_can_fetch = reward_status == REWARD_STATE_TYPE.CAN_FETCH
    if not is_un_done then
        local str_seq = is_can_fetch and 1 or 2
        self.node_list["btn_get_text"].text.text = Language.Boss.GetStateStr[str_seq]
        self.node_list["btn_get_remind"]:CustomSetActive(is_can_fetch)
        XUI.SetButtonEnabled(self.node_list["btn_get"], is_can_fetch)
    end

    local cfg_min = self.data.online_time / 60
    local cur_min = 0
    if reward_status == REWARD_STATE_TYPE.FINISH or is_can_fetch then
        cur_min = cfg_min
    elseif reward_status == REWARD_STATE_TYPE.UNDONE and cur_seq == self.data.seq then
        local online_time = OnlineRewardWGData.Instance:GetOnlineTime()
        cur_min = math.floor(online_time / 60)
    end

    local str = string.format("%s/%s", cur_min, cfg_min)
    local str_color = cur_min >= cfg_min and COLOR3B.GREEN or COLOR3B.RED
    self.node_list["time_desc"].text.text = string.format(Language.Activity.OnlineRewardStr, ToColorStr(str, str_color))
end

function OnlineRewardRender:OnClickGetReward()
    local reward_status = OnlineRewardWGData.Instance:GetRewardSatus(self.data.seq)
    if reward_status == REWARD_STATE_TYPE.CAN_FETCH then
        ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ONLINE_REWARD, 2)
    end
end