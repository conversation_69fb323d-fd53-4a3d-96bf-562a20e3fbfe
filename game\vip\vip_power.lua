------------------------------------------------------
--vip权限管理
--<AUTHOR>
------------------------------------------------------
VipPowerId =
{
	scene_fly 						= 0,			-- 传送
	key_dialy_task 					= 1,			-- 一键日常
	husong_buy_times 				= 2,			-- 购买护送次数
	vip_level_reward				= 3,			-- vip等级礼包
	fight_monster_exp				= 4,			-- 打怪经验加成
	coin_fb_buy_times 				= 5, 			-- 购买铜币本次数

	arena_buy_times				    = 6,			-- 购买竞技场挑战次数
	equip_fb_buy_times				= 7,			-- 装备本VIP购买次数
	hot_spring_exp				    = 8,			-- 温泉经验加成
	is_can_set_password			    = 9,			-- 是否可以设置拍卖密码
	pray_coin_times					= 10, 			-- 祈福铜币额外次数
	pray_exp_times			    	= 11, 			-- 祈福经验额外次数
	exp_fb_buy_times 				= 12,			-- 经验本购买次数
	tafang_fb_buy_times 			= 13,			-- 塔防本可购买次数
	lingchong_devour_exp 			= 14,			-- 装备吞噬经验加成
	vat_person_boss_extra_buy_times = 16,			-- 个人购买boss次数
	team_equip_buy_times 			= 17, 			-- 远古仙殿购买次数（目前的海底废墟）
	pet_fb_buy_times 		    	= 15,			-- 新宠物本vip购买次数
	linghunguangchang_buy_times 	= 21,			-- 灵魂广场购买次数
	boss_home_buy_times			 	= 24,			-- boss之家购买次数
	reward_shengwang_day_max = 25,              	-- 每日获得声望上限
	reward_tianshen_fb_buy_times  =26, 				-- 天神副本购买次数
	reward_baguamizhen_fb_buy_times  =27, 			-- 八卦迷阵副本购买次数
	reward_manhuanggudian_fb_buy_times = 28, 		-- 蛮荒古殿副本购买次数
	daily_task_double 				= 33,			-- 日常任务双倍
    vat_person_boss_times = 34,			            -- 个人boss次数

    vat_shenyuanboss_buy_times = 40,				-- 深渊BOSS参与奖励次数
	-- tili 							= 1,			--体力
	-- yao_money 						= 2,			--摇钱
	-- yao_jingyuan 					= 3,			--摇精元
	-- yao_xianhun						= 4,			--摇仙魂
	-- exp_fbsd_free_times 			= 6,			--经验本扫荡免费次数
	-- buy_enter_suoyaota	 			= 7,			--购买锁妖塔次数
	-- buy_enter_yaoshouplaza 			= 8,			--购买妖兽广场次数
	-- -- auto_dialy_task 				= 10,			--自动日常
	-- buy_act_reward 					= 12,			--购买活动奖励
	-- key_answer 						= 13,			--一键答题
	-- xunbao_150_times 				= 14,			--150次寻宝
	-- weapon_fbsd_free_auto_times    	= 16,			--过图免费扫荡次数
	-- tower_fb_buy_times    			= 17,			--塔防购买次数
	-- tower_fbsd_free_auto_times    	= 18,			--塔防免费扫荡次数
	-- quality_fb_buy_times 			= 19,			--挑战本重置次数
	-- quality_fbsd_free_auto_times 	= 20,			--挑战本免费扫荡次数
	-- shop_discount 					= 21,			--市场手续费折扣
	-- act_qibing 						= 22,			--激活骑兵
	-- daily_fuli						= 23,			--福利副本
	-- exp_fb_buy_play_times 			= 24,			--经验副本购买次数
	-- eq_fb_buy_play_times 			= 25,			--装备副本购买次数
	-- hot_spring_exp					= 26,			--温泉经验加成

	-- welfart_cion_num 				= 10, 			--祈福铜币额外次数
	-- welfart_exp_num					= 11,			--祈福经验额外次数

	-- lingchong_devour_exp			= 35,			--灵童吞噬装备exp加成
}

VipPower = VipPower or BaseClass()

function VipPower:__init()
	if VipPower.Instance ~= nil then
		ErrorLog("[VipPower] Attemp to create a singleton twice !")
	end
	VipPower.Instance = self
end

function VipPower:__delete()
	VipPower.Instance = nil
end

--是否有权限
--@target_param 目标次数
--@used_param 使用过的次数
function VipPower:GetHasPower(power_id, is_alert, target_param, used_param)
	local vip_cfg = self:GetPowerCfg(power_id)
	if vip_cfg == nil then
		Log("配置里没有该权限:" .. power_id)
		return false
	end
	local vip_level = RoleWGData.Instance.role_vo.vip_level
	if not VipWGData.Instance:IsVip() then
		vip_level = 0
	end

	local need_vip_level, vip_param = self:GetMinVipLevelLimit(power_id, target_param)
	local has_power = vip_level >= need_vip_level and need_vip_level >= 0

	if not has_power and is_alert then
		if need_vip_level > 0 then
			local tip_remain_times = nil
			if used_param then
				tip_remain_times = vip_param - used_param  --剩余次数
				if tip_remain_times < 0 then tip_remain_times = 0 end
			end
			VipWGCtrl.Instance:OpenVipTipsView("set_need_vip", {need_vip = need_vip_level, power_id = power_id, tip_remain_times = tip_remain_times})
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.HasNoCount)
		end
	end

	if power_id == VipPowerId.scene_fly then
		has_power = has_power and VipWGData.Instance:IsVipTy()
	end

	return has_power
end

function VipPower:GetPowerCfg(power_id)
	local vip_power_cfg_list = ConfigManager.Instance:GetAutoConfig("vip_auto").level

	return vip_power_cfg_list[power_id]
end

--获得当前vip权限下的参数
function VipPower:GetParam(power_id, level)
	local vip_cfg = self:GetPowerCfg(power_id)
	if vip_cfg == nil then
		return 0
	end

	local vip_level = level or RoleWGData.Instance.role_vo.vip_level

	local power_value = vip_cfg["param_" .. vip_level] or 0
	return power_value
end

--获得下一个更大的值。使用时注意判断0的情况
function VipPower:GetNextBigParam(power_id, cur_param)
	local power_cfg = self:GetPowerCfg(power_id)
	local max_level = VipWGData.Instance:GetMaxVIPLevel()
	if max_level == RoleWGData.Instance.role_vo.vip_level then
		return max_level, cur_param
	end
	if power_cfg ~= nil then
		for i = 0, max_level do
			local vip_param = power_cfg["param_".. i] or 0
			if vip_param > cur_param then
				return i, vip_param
			end
		end
	end
	return RoleWGData.Instance.role_vo.vip_level, self:GetParam(power_id, RoleWGData.Instance.role_vo.vip_level)
end

--获得vip等级限制
--@返回该权限的最低vip等级要求
--@param默认为1，如果次数请传具体值
function VipPower:GetMinVipLevelLimit(power_id, param)
	local max_level = VipWGData.Instance:GetMaxVIPLevel()
	local vip_cfg = self:GetPowerCfg(power_id)
	if vip_cfg == nil then
		return 0
	end
	param = param or 1

	for i = 0, max_level do
		local vip_param = vip_cfg["param_".. i] or 0
		if vip_param >= param then
			return i, vip_param
		end
	end
	return -1, param
end