function ShenShiQiangHuaView:TaoZhuangReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	if self.equip_cells then
		for k, v in pairs(self.equip_cells) do
			v:DeleteMe()
		end
	end
	self.equip_cells = {}

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end
	self.suit_list = nil
end


function ShenShiQiangHuaView:TaoZhuangLoadCallBack()
	self.suit_list = nil
	self.role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["shenshi_taozhuang_display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	
	self.role_model:SetRenderTexUI3DModel(display_data)
    -- self.role_model:SetUI3DModel(self.node_list["shenshi_taozhuang_display"].transform, self.node_list["shenshi_taozhuang_block"].event_trigger_listener, 2, true, MODEL_CAMERA_TYPE.BASE)
    self:AddUiRoleModel(self.role_model)

	local alpha_color = Color.New(0,0,0,0)
	self.equip_cells = {}
	for i=1, 4 do
		self.equip_cells[i] = ItemCell.New(self.node_list["go_taozhuang_pos" .. i])
		self.equip_cells[i].root_node.image.color = alpha_color
	end

	self.list_view = AsyncListView.New(TianShenTaoZhuangItemRender, self.node_list["ph_listview"])
end

--刷新模型展示
function ShenShiQiangHuaView:FlushShenShiModelDisPlay()
	local data = self.select_tainshen_cfg
	if data then
		self.node_list["text_tianshen_name"].text.text = data.bianshen_name
		local bundle, asset = ResPath.GetBianShenModel(data.appe_image_id)

		if bundle and asset then
			self.role_model:SetMainAsset(bundle, asset)
			self.role_model:PlayMonsterAction()
		end

		bundle, asset = TianShenWGData.Instance:GetTianShenWeapon(data.index)
		self.role_model:SetWeaponModel(bundle, asset)
	end
end

function ShenShiQiangHuaView:FlushEquipLists()
	if not self.equip_cells then return end

	local tianshen_data = TianShenWGData.Instance:GetTianShenInfoByIndex(self.select_tainshen_cfg.index)
	if not tianshen_data then return end

	local equip_part_list = tianshen_data.equip_part
	for i=1,#self.equip_cells do
		local v = equip_part_list[i - 1]
		self.equip_cells[i]:SetData(v)
		--self.equip_cells[i]:SetPartName("")
		self.equip_cells[i]:SetButtonComp(true)
		if  v.item_id == 0 then
			--self.equip_cells[i]:SetPartName(Language.TianShen.EquipNameList[i - 1])
			self.equip_cells[i]:SetItemIcon(ResPath.GetEquipIcon(i+50))
		end
	end
end

function ShenShiQiangHuaView:GetTaoZhuangSelectData()
	return self.select_tainshen_cfg
end

function ShenShiQiangHuaView:TaoZhuangOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "tz_succes" then 
			self:TaoZhuangEffect()
		end
	end
	self:FlushTaoZhuangView()
end

function ShenShiQiangHuaView:TaoZhuangEffect()
    -- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tianshen_shengji)
    -- EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.tz_effect.transform, 4)
end

function ShenShiQiangHuaView:FlushTaoZhuangView()
	self.suit_list = TianShenWGData.Instance:GetShenShiSuitList(self.select_tainshen_cfg.index)
	self.list_view:SetDataList(self.suit_list, 3)
	self:FlushEquipLists()
	self:FlushShenShiModelDisPlay()
end

-------------------------------------------------------------------------
TianShenTaoZhuangItemRender = TianShenTaoZhuangItemRender or BaseClass(BaseRender)

function TianShenTaoZhuangItemRender:__init()
	XUI.AddClickEventListener(self.node_list["btn_active"], BindTool.Bind(self.OnClickActive, self))
end

function TianShenTaoZhuangItemRender:__delete()

end

function TianShenTaoZhuangItemRender:OnClickActive()
	-- body
	local view = ViewManager.Instance:GetView(GuideModuleName.ShenShiQiangHuaView)
	local select_data = view:GetTaoZhuangSelectData()
	if not select_data then return end
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type16, select_data.index, self.data.suit_code)
end

function TianShenTaoZhuangItemRender:OnFlush()
	if not self.data then return end
	local view = ViewManager.Instance:GetView(GuideModuleName.ShenShiQiangHuaView)
	local select_data = view:GetTaoZhuangSelectData()
	if not select_data then return end
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(select_data.index)
	local quip_num = TianShenWGData.Instance:GetShenShiSuitNum(select_data.index, self.data.colour, self.data.part_star)
	local pinzhi_str = Language.Common.ColorName3[self.data.colour]
	local yitao_str = Language.TianShen.TaoZhuangTips1
	local num_str = ToColorStr(self.data.part_star, ITEM_COLOR[self.data.colour])
	local and_str = string.format(Language.TianShen.TaoZhuangTips2, quip_num >= TIANSHEN_MAX_EQUIP and COLOR3B.GREEN or COLOR3B.RED, quip_num)
	local str = yitao_str .. pinzhi_str .. num_str .. and_str
	self.node_list["text_des"].text.text = str
	self.node_list["text_name"].text.text = self.data.name
	local list = TianShenWGData.Instance:GetShenShiSuitAttr(select_data.index, self.data.colour, self.data.part_star)
	for i=1,4 do
		self.node_list["text_attr" .. i]:SetActive(nil ~= list[i])
		if 1 == tianshen_info.activity_suit_sign[self.data.suit_code] and list[i] then
			self.node_list["text_attr" .. i].text.text = list[i].attr_name .. "<color=#006a25>+" .. list[i].attr_value .. "</color>"
		else
			if list[i] then
				self.node_list["text_attr" .. i].text.text = "<color=#453550>" .. list[i].attr_name .. "</color><color=#006a25>+" .. list[i].attr_value .. "</color>"
			end
		end
	end

	local url = ""
	local is_weidacheng
	local text

	self.node_list["btn_active"]:SetActive(false)
	self.node_list["img_flag"]:SetActive(false)
	self.node_list["taozhuang_lock_tips"]:SetActive(false)
	if 1 == tianshen_info.activity_suit_sign[self.data.suit_code] then
		--已经达成
		self.node_list["img_flag"]:SetActive(true)
	else
		is_weidacheng = quip_num < TIANSHEN_MAX_EQUIP
		if is_weidacheng then
			self.node_list["taozhuang_lock_tips"]:SetActive(true)
		else
			self.node_list["btn_active"]:SetActive(true)
		end
		self.node_list["img_remind"]:SetActive(quip_num >= TIANSHEN_MAX_EQUIP)
	end
end
