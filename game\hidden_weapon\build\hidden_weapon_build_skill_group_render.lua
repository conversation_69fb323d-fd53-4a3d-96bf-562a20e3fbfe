-- 暗器打造-技能变化组（包含两个技能框）
HWBuildSkillGroupRender = HWBuildSkillGroupRender or BaseClass(BaseRender)

function HWBuildSkillGroupRender:__init()
end

function HWBuildSkillGroupRender:__delete()
    if self.skill_render1 then
        self.skill_render1:DeleteMe()
        self.skill_render1 = nil
    end
    if self.skill_render2 then
        self.skill_render2:DeleteMe()
        self.skill_render2 = nil
    end
end

function HWBuildSkillGroupRender:LoadCallBack(a)
    -- 两个技能
    self.skill_render1 = HWDetailSkillRender.New(self.node_list["hw_build_skill1"])
    self.skill_render2 = HWDetailSkillRender.New(self.node_list["hw_build_skill2"])
end

function HWBuildSkillGroupRender:OnFlush()
    if not self.data then
        return
    end
    local data = self.data
    if data.skill_data1 then
        self.skill_render1:SetData(data.skill_data1)
    end
    if data.skill_data2 then
        self.skill_render2:SetData(data.skill_data2)
    end
end
