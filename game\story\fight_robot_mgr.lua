FightRobertManager = FightRobertManager or BaseClass()
function FightRobertManager:__init()
	if FightRobertManager.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[FightRobertManager] attempt to create singleton twice!")
		return
	end
	FightRobertManager.Instance = self

	self.robots_fight_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("story_auto")["robots_fight"], "scene")
	self.has_robots = false
	self.atk_cacha_t = {}
	self.performer_count = 0
	self.target_t = {}
end

function FightRobertManager:__delete()
	FightRobertManager.Instance = nil
end

local scene_id = 0
local scene_instance = nil
local robot_mgr_instance = nil
function FightRobertManager:CheckRobots()
	scene_instance = Scene.Instance
	robot_mgr_instance = RobertManager.Instance
	if scene_instance == nil or robot_mgr_instance == nil then return end

	scene_id = Scene.Instance:GetSceneId()
	local robert_cfg = nil
	if nil ~= self.robots_fight_cfg[scene_id] then
        for k, v in pairs(self.robots_fight_cfg[scene_id]) do
        	robert_cfg = robot_mgr_instance:GetRobertCfg(v.id)
            if robert_cfg and math.abs(robert_cfg.born_x - scene_instance.main_role_pos_x) <= 60 and math.abs(robert_cfg.born_y - scene_instance.main_role_pos_y) <= 60 then
            	if not robot_mgr_instance:GetRobertByRobertId(v.id) then
	            	robot_mgr_instance:CreateRobert(v.id, true)
	            	self.performer_count = self.performer_count + 1
	            	if robot_mgr_instance:GetRobertByRobertId(v.target_id) then
	            		robot_mgr_instance:RobertAtkTarget(v.id, v.target_id)
	            		self.target_t[v.target_id] = self.target_t[v.target_id] or {}
	            		self.target_t[v.target_id][v.id] = true
	            	else
	            		self.atk_cacha_t[v.target_id] = v.id
	            	end
	            	if self.atk_cacha_t[v.id] then
	            		robot_mgr_instance:RobertAtkTarget(self.atk_cacha_t[v.id], v.id)
	            		self.target_t[v.id] = self.target_t[v.id] or {}
	            		self.target_t[v.id][self.atk_cacha_t[v.id]] = true
	            		self.atk_cacha_t[v.id] = nil
	            	end
	            end
	            if not robot_mgr_instance:IsFighting() then
	            	robot_mgr_instance:StartFight({})
	            end
            else
            	if robot_mgr_instance:GetRobertByRobertId(v.id) then
            		robot_mgr_instance:DelRobertByRobertId(v.id)
            		for k1,v1 in pairs(self.target_t[v.id] or {}) do
            			if robot_mgr_instance:GetRobertByRobertId(k1) then
	            			self.atk_cacha_t[v.id] = k1
	            		end
            		end

	                self.performer_count = math.max(self.performer_count - 1, 0)
	                if robot_mgr_instance:IsFighting() and self.performer_count == 0 then
		            	robot_mgr_instance:StopFight()
		            end
		        end
            end
        end
    end
end