--匹配 /jy_gm crossdivinedomainoperate:999 0 0 0 
-- /jy_gm crossdivinedomainoperate:998 0 500 0   seq  积分  0   设置积分
-- /jy_gm crossdivinedomainoperate:997 seq 0 0   --设置城池归属
--/jy_gm crossdivinedomainoperate:996 数值 0 0    加power
--/jy_gm crossdivinedomainoperate:995 0 0 0    真个跨服进膜拜
--/jy_gm crossdivinedomainoperate:994 0 0 0    切回正常打boss场景

-- /jy_gm crossdivinedomainoperate:999 0 0 0	匹配初始化
-- /jy_gm crossdivinedomainoperate:998 0 100 0	给玩家加100积分
-- /jy_gm crossdivinedomainoperate:997 1 0 0	把seq1变成我占领
-- /jy_gm crossdivinedomainoperate:996 0 0 0	加灵力
-- /jy_gm crossdivinedomainoperate:995 0 0 0	进膜拜场景
-- /jy_gm crossdivinedomainoperate:994 0 0 0	进boss场景
-- /jy_gm crossdivinedomainoperate:993 0 0 0	赛季结算
-- /jy_gm crossdivinedomainoperate:992 0 0 0	每日结算

require("game/holy_heavenly_domain/holy_heavenly_domain_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_details_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_hof_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_tujie_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_war_situation")
require("game/holy_heavenly_domain/holy_heavenly_domain_camp_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_camp_details_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_map_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_privilege_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_shop_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_rank_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_war_reward")
require("game/holy_heavenly_domain/holy_heavenly_domain_country_tip")
require("game/holy_heavenly_domain/holy_heavenly_domain_fb_task_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_mobai_task_view")
require("game/holy_heavenly_domain/holy_heavenly_domain_wg_data")
require("game/holy_heavenly_domain/holy_heavenly_domain_set_target_view")

HolyHeavenlyDomainWGCtrl = HolyHeavenlyDomainWGCtrl or BaseClass(BaseWGCtrl)

function HolyHeavenlyDomainWGCtrl:__init()
	if HolyHeavenlyDomainWGCtrl.Instance then
		ErrorLog("[HolyHeavenlyDomainWGCtrl] attempt to create singleton twice!")
		return
	end

	HolyHeavenlyDomainWGCtrl.Instance = self

	if not self.data then
		self.data = HolyHeavenlyDomainWGData.New()
	end

	self:InitView()
	self:RegisterAllProtocols()
	self.need_open_war_situation = false

	self.item_change_callback = BindTool.Bind(self.OnItemChangeCallBack, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)

	self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
end

function HolyHeavenlyDomainWGCtrl:__delete()
	HolyHeavenlyDomainWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	
	if self.tujie_view then
		self.tujie_view:DeleteMe()
		self.tujie_view = nil
	end

	if self.war_situation_view then
		self.war_situation_view:DeleteMe()
		self.war_situation_view = nil
	end

	if self.camp_view then
		self.camp_view:DeleteMe()
		self.camp_view = nil
	end

	if self.camp_details_view then
		self.camp_details_view:DeleteMe()
		self.camp_details_view = nil
	end

	if self.map_view then
		self.map_view:DeleteMe()
		self.map_view = nil
	end

	if self.privilege_view then
		self.privilege_view:DeleteMe()
		self.privilege_view = nil
	end

	if self.shop_view then
		self.shop_view:DeleteMe()
		self.shop_view = nil
	end

	if self.rank_view then
		self.rank_view:DeleteMe()
		self.rank_view = nil
	end

	if self.war_reward_view then
		self.war_reward_view:DeleteMe()
		self.war_reward_view = nil
	end

	if self.country_tip then
		self.country_tip:DeleteMe()
		self.country_tip = nil
	end

	if self.fb_task_view then
		self.fb_task_view:DeleteMe()
		self.fb_task_view = nil
	end

	if self.mobai_task_view then
		self.mobai_task_view:DeleteMe()
		self.mobai_task_view = nil
	end

	if self.set_target_view then
		self.set_target_view:DeleteMe()
		self.set_target_view = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)

	if nil ~= self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end
end

--------------------------------protocol--------------------------------
function HolyHeavenlyDomainWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossDivineDomainOperate)
	self:RegisterProtocol(SCCrossDivineDomainBaseInfo, "OnSCCrossDivineDomainBaseInfo")
	self:RegisterProtocol(SCCrossDivineDomainRoomInfo, "OnSCCrossDivineDomainRoomInfo")
	self:RegisterProtocol(SCCrossDivineDomainCityInfo, "OnSCCrossDivineDomainCityInfo")
	self:RegisterProtocol(SCCrossDivineDomainPlayerScoreRank, "OnSCCrossDivineDomainPlayerScoreRank")
	self:RegisterProtocol(SCCrossDivineDomainMonsterInfo, "OnSCCrossDivineDomainMonsterInfo")
	self:RegisterProtocol(SCCrossDivineDomainMonsterUpdate, "OnSCCrossDivineDomainMonsterUpdate")
	self:RegisterProtocol(SCCrossDivineDomainSceneInfo, "OnSCCrossDivineDomainSceneInfo")
	self:RegisterProtocol(SCCrossDivineDomainServerScoreRank, "OnSCCrossDivineDomainServerScoreRank")
	self:RegisterProtocol(SCCrossDivineDomainBaseOtherInfo, "OnSCCrossDivineDomainBaseOtherInfo")
	self:RegisterProtocol(SCCrossDivineDomainCampInfo, "OnSCCrossDivineDomainCampInfo")
end

function HolyHeavenlyDomainWGCtrl:OnDivineDomainOperate(operate_type, param1, param2, param3)
	-- print_error("圣天神域请求", operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossDivineDomainOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function HolyHeavenlyDomainWGCtrl:OnDivineDomainGetCitiInfoOperate(city_table)
	local city_flag = bit:b2d_end(city_table)
	self:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.CITY_INFO, city_flag)
end

-- 基础信息
function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainBaseInfo(protocol)
	-- print_error("基础信息 OnSCCrossDivineDomainBaseInfo", protocol)
	self.data:SetBaseInfo(protocol)
	self.data:CalConvertRemind()
	RemindManager.Instance:Fire(RemindName.HolyHeavenlyDomainShop)

	self.data:CalBattlefieldRemindCache()
	RemindManager.Instance:Fire(RemindName.HolyHeavenlyDomainBattlefield)

	-- 战场红点
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.shop_view and self.shop_view:IsOpen() then
		self.shop_view:Flush()
	end

	if self.privilege_view and self.privilege_view:IsOpen() then
		self.privilege_view:Flush()
	end

	if self.map_view and self.map_view:IsOpen() then
		self.map_view:Flush()
	end

	if self.country_tip and self.country_tip:IsOpen() then
		self.country_tip:Flush()
	end

	if self.fb_task_view and self.fb_task_view:IsOpen() then
		self.fb_task_view:Flush()
	end
end

-- 房间信息
function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainRoomInfo(protocol)
	-- print_error("房间信息 SCCrossDivineDomainRoomInfo", protocol)
	self.data:SetRoomInfo(protocol)

	if self.map_view and self.map_view:IsOpen() then
		self.map_view:Flush()
	end

	if self.camp_details_view and self.camp_details_view:IsOpen() then
		self.camp_details_view:Flush()
	end

	if self.war_situation_view and self.war_situation_view:IsOpen() then
		self.war_situation_view:Flush()
	end
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainCityInfo(protocol)
	-- print_error("城市信息 OnSCCrossDivineDomainCityInfo", protocol)
	self.data:SetCityInfo(protocol)
	self.data:CalBattlefieldRemindCache()
	RemindManager.Instance:Fire(RemindName.HolyHeavenlyDomainBattlefield)

	local need_open_war_situation = self.data:GetBattlefieldRemind() == 1
	if need_open_war_situation then
		if self.view and self.view:IsOpen() then
			if self.need_open_war_situation then
				self.need_open_war_situation = false
				self:OpenWarSituationView()
			end
		end
	end

	if self.map_view and self.map_view:IsOpen() then
		self.map_view:Flush()
	end

	-- 战场红点
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
	
	if self.war_situation_view and self.war_situation_view:IsOpen() then
		self.war_situation_view:Flush()
	end
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainPlayerScoreRank(protocol)
	-- print_error("设置任务积分排行信息 OnSCCrossDivineDomainPlayerScoreRank", protocol)
	self.data:SetPlayerScoreRankInfo(protocol)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.rank_view and self.rank_view:IsOpen() then
		self.rank_view:Flush()
	end

	if protocol.type == CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK then
		if self.mobai_task_view and self.mobai_task_view:IsOpen() then
			self.mobai_task_view:Flush()
		end

		local scene_type = Scene.Instance:GetSceneType()

		if scene_type == SceneType.CROSS_DIVINE_DOMAIN_MOBAI then
			local scene_logic = Scene.Instance:GetSceneLogic()
			if scene_logic ~= nil and scene_logic.UpdateHolyBeastCallWorshipStatue then
				scene_logic:UpdateHolyBeastCallWorshipStatue()
			end
		end
	end
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainMonsterInfo(protocol)
	-- print_error("设置怪物信息 OnSCCrossDivineDomainMonsterInfo", protocol)
	self.data:SetMonsterInfo(protocol)

	if self.country_tip and self.country_tip:IsOpen() then
		self.country_tip:Flush()
	end
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainMonsterUpdate(protocol)
	-- print_error("更新怪物信息 OnSCCrossDivineDomainMonsterUpdate", protocol)
	self.data:UpdateMonsterInfo(protocol)

	if self.country_tip and self.country_tip:IsOpen() then
		self.country_tip:Flush()
	end

	if self.fb_task_view and self.fb_task_view:IsOpen() then
		self.fb_task_view:Flush()
	end
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainSceneInfo(protocol)
	-- print_error("OnSCCrossDivineDomainSceneInfo", protocol)
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainServerScoreRank(protocol)
	-- print_error("服积分信息 OnSCCrossDivineDomainServerScoreRank", protocol)
	self.data:SetServerScoreRank(protocol)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.rank_view and self.rank_view:IsOpen() then
		self.rank_view:Flush()
	end
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainBaseOtherInfo(protocol)
	-- print_error(" 积分奖励 占城奖励 目标城市 OnSCCrossDivineDomainBaseOtherInfo", protocol)
	self.data:SetBaseOtherInfo(protocol)
	self.data:CalScoreRewardRemind()
	RemindManager.Instance:Fire(RemindName.HolyHeavenlyDomainScoreReward)

	self.data:CalBattlefieldRemindCache()
	RemindManager.Instance:Fire(RemindName.HolyHeavenlyDomainBattlefield)

	if self.map_view and self.map_view:IsOpen() then
		self.map_view:Flush()
	end

	if self.country_tip and self.country_tip:IsOpen() then
		self.country_tip:Flush()
	end

	if self.war_reward_view and self.war_reward_view:IsOpen() then
		self.war_reward_view:Flush()
	end

	-- 战场红点
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.mobai_task_view and self.mobai_task_view:IsOpen() then
		self.mobai_task_view:Flush()
	end

	local scene_type = Scene.Instance:GetSceneType()

	if scene_type == SceneType.CROSS_DIVINE_DOMAIN_MOBAI then
		local scene_logic = Scene.Instance:GetSceneLogic()

		if scene_logic ~= nil and scene_logic.GoToWorshipPos then
			scene_logic:GoToWorshipPos()
		end
	end
end

function HolyHeavenlyDomainWGCtrl:OnSCCrossDivineDomainCampInfo(protocol)
	-- print_error("阵营前三排名信息 OnSCCrossDivineDomainCampInfo", protocol)
	self.data:SetCampInfo(protocol)

	if self.camp_details_view and self.camp_details_view:IsOpen() then
		self.camp_details_view:Flush()
	end
end

function HolyHeavenlyDomainWGCtrl:OnItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:IsConvertItem(change_item_id) then
			self.data:CalConvertRemind()
			RemindManager.Instance:Fire(RemindName.HolyHeavenlyDomainShop)

			if self.view and self.view:IsOpen() then
				self.view:Flush(TabIndex.holy_heavenly_domain_details)
			end

			if self.shop_view and self.shop_view:IsOpen() then
				self.shop_view:Flush()
			end

			if self.map_view and self.map_view:IsOpen() then
				self.map_view:Flush()
			end
		end
	end
end

--------------------------------VIEW_OPERA--------------------------------
function HolyHeavenlyDomainWGCtrl:InitView()
	if not self.view then
		self.view = HolyHeavenlyDomainView.New(GuideModuleName.HolyHeavenlyDomainView)
	end

	if not self.tujie_view then
		self.tujie_view = HolyHeavenlyDomainTuJieView.New()
	end

	if not self.war_situation_view then
		self.war_situation_view = HolyHeavenlyDomainWarSituationView.New()
	end

	if not self.camp_view then
		self.camp_view = HolyHeavenlyDomainCampView.New(GuideModuleName.HolyHeavenlyDomainCampView)
	end

	if not self.camp_details_view then
		self.camp_details_view = HolyHeavenlyDomainCampDetailsView.New()
	end

	if not self.map_view then
		self.map_view = HolyHeavenlyDomainMapView.New(GuideModuleName.HolyHeavenlyDomainMapView)
	end

	if not self.privilege_view then
		self.privilege_view = HolyHeavenlyDomainPrivilegeView.New(GuideModuleName.HolyHeavenlyDomainPrivilegeView)
	end

	if not self.shop_view then
		self.shop_view = HolyHeavenlyDomainShopView.New(GuideModuleName.HolyHeavenlyDomainShopView)
	end

	if not self.rank_view then
		self.rank_view = HolyHeavenlyDomainRankView.New()
	end

	if not self.war_reward_view then
		self.war_reward_view = HolyHeavenlyDomainWarRewardView.New()
	end

	if not self.country_tip then
		self.country_tip = HolyHeavenlyDomainCountryTip.New()
	end

	if not self.fb_task_view then
		self.fb_task_view = HolyHeavenlyDomainFBTaskView.New()
	end

	if not self.mobai_task_view then
		self.mobai_task_view = HolyHeavenlyDomainMoBaiTaskView.New()
	end

	if not self.set_target_view then
		self.set_target_view = HolyHeavenlyDomainSetTargetView.New()
	end
end

function HolyHeavenlyDomainWGCtrl:OpenTuJieView()
	if self.tujie_view then
		self.tujie_view:Open()
	end
end

function HolyHeavenlyDomainWGCtrl:OpenWarSituationView()
	if self.war_situation_view then
		self.war_situation_view:Open()
	end
end

function HolyHeavenlyDomainWGCtrl:OpenCampDetailsView(data)
	if data == nil then
		return
	end

	if self.camp_details_view then
		self.camp_details_view:SetData(data)
	end
end

function HolyHeavenlyDomainWGCtrl:OpenRankView(select_person_rank)
	local is_select_person_rank = select_person_rank or false

	if self.rank_view then
		self.rank_view:SetSelectRankType(is_select_person_rank)
		
		if not self.rank_view:IsOpen() then
			self.rank_view:Open()
		else
			self.rank_view:Flush()
		end
	end
end

function HolyHeavenlyDomainWGCtrl:OpenWarRewardView()
	if self.war_reward_view then
		self.war_reward_view:Open()
	end
end

function HolyHeavenlyDomainWGCtrl:OpenCountryTip(data)
	if data == nil then
		return
	end

	self.country_tip:SetData(data)
end

function HolyHeavenlyDomainWGCtrl:OpenMobaiTaskView()
	if self.mobai_task_view then
		self.mobai_task_view:Open()
	end
end

function HolyHeavenlyDomainWGCtrl:CloseMobaiTaskView()
	if self.mobai_task_view then
		self.mobai_task_view:Close()
	end
end

function HolyHeavenlyDomainWGCtrl:OpenFBTaskView()
	if self.fb_task_view then
		self.fb_task_view:Open()
	end
end

function HolyHeavenlyDomainWGCtrl:CloseFBTaskView()
	if self.fb_task_view then
		self.fb_task_view:Close()
	end
end

function HolyHeavenlyDomainWGCtrl:OnHolyHeavenlyDomainFBBossHurtInfo(state)
	if self.fb_task_view and self.fb_task_view:IsOpen() then
		self.fb_task_view:UpdateRankList(state)
	end
end

function HolyHeavenlyDomainWGCtrl:OpenSetTargetView()
	if self.set_target_view then
		self.set_target_view:Open()
	end
end

function HolyHeavenlyDomainWGCtrl:CloseSetTargetView()
	if self.set_target_view then
		self.set_target_view:Close()
	end
end

function HolyHeavenlyDomainWGCtrl:SetAutoOpenWarSituationState(state)
	self.need_open_war_situation = state
end

-- 服务端优化，要求的跨天补个请求
function HolyHeavenlyDomainWGCtrl:OnDayPass()
	HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.BASE_OTHER_INFO)
end