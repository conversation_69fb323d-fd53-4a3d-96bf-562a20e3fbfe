GoldStoneView = GoldStoneView or BaseClass(SafeBaseView)

function GoldStoneView:__init()
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/goldstone_ui_prefab", "layout_goldstone_buy")
end

function GoldStoneView:OpenCallBack()
	GoldStoneWGCtrl.Instance:SendAllGetReward(GOLDEN_TALK_OPERATE_TYPE.INFO, 0)
end

function GoldStoneView:LoadCallBack()
	-- self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	-- self.reward_list:SetStartZeroIndex(true)
	XUI.AddClickEventListener(self.node_list["free_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_sell"], BindTool.Bind(self.OnBtnSellBtn, self))
	XUI.AddClickEventListener(self.node_list.skill_btn, BindTool.Bind(self.OnClickBenedictionBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_show_cg, BindTool.Bind(self.OnClickShowCgBtn, self))
	--self:FlushTimeCount()

	if not self.reward_item_list then
		self.reward_item_list = {}

		for i = 0, 5 do
			local item = ItemCell.New(self.node_list["reward_item" .. i])
			item:SetShowCualityBg(false)
			item:SetCellBgEnabled(false)
			item:NeedDefaultEff(false)
			-- item:SetIsShowTips(false)
			self.reward_item_list[i] = item
		end
	end
end

function GoldStoneView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("gold_stone_time") then
        CountDownManager.Instance:RemoveCountDown("gold_stone_time")
    end

	-- if self.reward_list then
	-- 	self.reward_list:DeleteMe()
	-- 	self.reward_list = nil
	-- end

	if self.gift_box_tween then
		self.gift_box_tween:Kill()
		self.gift_box_tween = nil
	end

	if self.reward_item_list then
		for k, v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end

		self.reward_item_list = nil
	end
end

function GoldStoneView:OnFlush()
	GoldStoneWGData.Instance:ChangeShopID()
	local cur_shop_cfg = GoldStoneWGData.Instance:GetCurShopCfg()
	if IsEmptyTable(cur_shop_cfg) then
		return
	end

	local price = RoleWGData.GetPayMoneyStr(cur_shop_cfg.price, cur_shop_cfg.rmb_type, cur_shop_cfg.rmb_seq)
	local is_buy_rmb, is_buy_free = GoldStoneWGData.Instance:GetShopIsBuyFlag()

	for i = 0, 5 do
		local data = ((cur_shop_cfg or {}).reward_item or {})[i] or {}

		if not IsEmptyTable(data) then
			self.node_list["item_" .. i]:CustomSetActive(true)
			self.reward_item_list[i]:SetData(data)
		else
			self.node_list["item_" .. i]:CustomSetActive(false)
		end
	end

	-- self.reward_list:SetDataList(cur_shop_cfg.reward_item)
	self.node_list.desc_2.text.text = cur_shop_cfg.desc_2
	local str = price
	if is_buy_rmb then
		str = Language.GoldStoneBuy.GoldStoneAllOut
	end

	self.node_list.btn_text.text.text = str
	self.node_list.original_price:SetActive(not is_buy_rmb)
	self.node_list.desc_1.text.text = cur_shop_cfg.desc_1
	self.node_list.btn_red:SetActive(not is_buy_free)
	self.node_list["free_gift"]:SetActive(not is_buy_free)

	-- 技能图标
	local cfg = GoldStoneWGData.Instance:GetFreeShop()
	-- local skill_bundle, skill_asset = cfg.model_bundle_name2, cfg.model_asset_name2
	--[[local skill_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	local skill_id = LongZhuWGData.Instance:GetOtherCfg("skill_id")
	local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level > 0 and skill_level or 1)--]]
	-- if cfg.model_bundle_name2 and cfg.model_asset_name2 then
	-- 	self.node_list.skill_icon.image:LoadSprite(skill_bundle, skill_asset)
	-- end

	if not is_buy_free then
		if self.gift_box_tween then
			self.gift_box_tween:Restart()
		else
			if self.gift_box_tween then
				self.gift_box_tween:Kill()
				self.gift_box_tween = nil
			end

			self.gift_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.free_gift.transform, self.gift_box_tween)
		end
	elseif self.gift_box_tween then
		self.gift_box_tween:Pause()
		self.node_list.free_gift.transform.localRotation = Quaternion.identity
	end
end

function GoldStoneView:FlushTimeCount()
	if CountDownManager.Instance:HasCountDown("gold_stone_time") then
		CountDownManager.Instance:RemoveCountDown("gold_stone_time")
	end

	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.GOLD_STONE_VIEW)
    if time > 0 then
		self.node_list.time_bg:CustomSetActive(true)

        CountDownManager.Instance:AddCountDown("gold_stone_time",
            BindTool.Bind(self.FinalUpdateTimeCallBack, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function GoldStoneView:FinalUpdateTimeCallBack(now_time, total_time)
	if self.node_list["time_str"] then
		local time = math.ceil(total_time - now_time)
		local time_str = TimeUtil.FormatSecondDHM9(time)
		self.node_list["time_str"].text.text = string.format(Language.GoldStoneBuy.ActivityTime, time_str)
	end
end

function GoldStoneView:OnComplete()
	if self.node_list.time_str then
		self.node_list.time_bg:CustomSetActive(false)
		self.node_list.time_str.text.text = string.format(Language.GoldStoneBuy.ActivityTime, "00:00")
	end
end

function GoldStoneView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOLD_STONE_VIEW)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local _, is_buy_free = GoldStoneWGData.Instance:GetShopIsBuyFlag()
		if is_buy_free then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllFreeShopBuy)
			return
		end

		GoldStoneWGCtrl.Instance:SendAllGetReward(GOLDEN_TALK_OPERATE_TYPE.BUY_FREE, GoldStoneWGData.Instance:GetCurFreeShopID())
	end
end

function GoldStoneView:OnBtnSellBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOLD_STONE_VIEW)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_rmb = GoldStoneWGData.Instance:GetShopIsBuyFlag()
		if is_buy_rmb then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllShopBuy)
			return
		end

		local cur_shop_cfg = GoldStoneWGData.Instance:GetCurShopCfg()
		if not IsEmptyTable(cur_shop_cfg) then
			RechargeWGCtrl.Instance:Recharge(cur_shop_cfg.price, cur_shop_cfg.rmb_type, cur_shop_cfg.rmb_seq)
			return
		end
	end
end

-- 灵珠祝福
function GoldStoneView:OnClickBenedictionBtn()
	local cfg = GoldStoneWGData.Instance:GetFreeShop()
	if cfg.open_panel ~= nil then
        FunOpen.Instance:OpenViewNameByCfg(cfg.open_panel)
    end
	--LongZhuWGCtrl.Instance:OpenLongZhuTip()
end

function GoldStoneView:OnClickShowCgBtn()
	local cg_bundle = "cg/a3_cg_skill_prefab"
	local cg_asset = "A3_CG_Skill"

	CgManager.Instance:Play(UICg.New(cg_bundle, cg_asset))
end