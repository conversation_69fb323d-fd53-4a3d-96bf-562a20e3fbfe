FuBenMiJingView = FuBenMiJingView or BaseClass(SafeBaseView)

function FuBenMiJingView:__init()
    --self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_common")
    -- self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layou_guildfb_fb_info")
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layou_guildfb_rank_info")
    self.view_cache_time = 1
    self.active_close = false
    self.is_safe_area_adapter = true
end

function FuBenMiJingView:__delete()

end

function FuBenMiJingView:ReleaseCallBack()
    if self.reward_cell then
        self.reward_cell:DeleteMe()
        self.reward_cell = nil
    end

    if self.reward_cell_2 then
        self.reward_cell_2:DeleteMe()
        self.reward_cell_2 = nil
    end
    --self.hp_slider_value0 = nil
    self.old_percent = nil
    self.is_fisrt = nil
    if self.boss_close_time then
        GlobalTimerQuest:CancelQuest(self.boss_close_time)
        self.boss_close_time = nil
    end
    self.rankdata_list = nil
    self.is_show_one_list = nil
    self.is_show_two_list = nil
    self.die_flag_list = nil
    if self.be_hit_timequest then
        GlobalTimerQuest:CancelQuest(self.be_hit_timequest)
        self.be_hit_timequest = nil
    end
    self.cur_wave = nil
    self.hurt_data_list = nil
    self.beauty_show_one = nil
    self.beauty_show_two = nil
    --self.is_destroy_bg_list = nil

    if self.my_rank_item then
        self.my_rank_item:DeleteMe()
        self.my_rank_item = nil
    end

    if self.reward_cell_list then
        self.reward_cell_list:DeleteMe()
        self.reward_cell_list = nil
    end

    if self.main_top_arrow_click then
        GlobalEventSystem:UnBind(self.main_top_arrow_click)
        self.main_top_arrow_click = nil
    end

    self.is_fisrt_set_item_list = nil
    self.fb_info_obj = nil
    self.rank_info_obj = nil
    self.m_is_close = nil
    self.old_exp_num = nil

    if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
end

function FuBenMiJingView:LoadCallBack()
    self.cur_wave = -1
    self.hurt_data_list = {}
    self.is_show_one_list = {}
    self.is_show_two_list = {}
    self.old_percent = 1
    self.is_fisrt = true
    for i = 1, 3 do
        self.is_show_one_list[i] = false
    end
    for i = 1, 3 do
        self.is_show_two_list[i] = false
    end
    self.rankdata_list = AsyncListView.New(RankItemRender, self.node_list.rank_list)
    self.my_rank_item = RankItemRender.New(self.node_list["my_rank_item"])
    if not self.reward_cell_list then
        self.reward_cell_list = AsyncListView.New(ItemCell, self.node_list["reward_cell_container"])
    end

    self.show_info = true
    self.node_list.gd_info_panel:CustomSetActive(true)
    self.node_list.gd_rank_panel:CustomSetActive(false)
    self.node_list.no_data:CustomSetActive(false)

    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        self:InitCallBack()
    end)

    XUI.AddClickEventListener(self.node_list["btn_shenshou"], BindTool.Bind(self.OnClickDragon))
    XUI.AddClickEventListener(self.node_list["btn_exchange"], BindTool.Bind(self.OnClickGDExchange, self))
end

function FuBenMiJingView:InitCallBack()
    local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["task_root"] then
		self.obj = self.node_list["task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function FuBenMiJingView:CloseCallBack()
    self.m_is_close = true
    self.is_out_fb = true
    
    if self.obj then
        self.obj:SetActive(false)
    end
end

function FuBenMiJingView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function FuBenMiJingView:ShowHurtRankChuanWen(rank_data)
    if Scene.Instance:GetSceneType() ~= SceneType.GuildMiJingFB then return end
    local length = #rank_data
    if length == 1 then
        TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.HurtRankOne, rank_data[1].user_name))
    elseif length == 2 then
        TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.HurtRankOne, rank_data[1].user_name))
        if rank_data[2].roleid > 0 then
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.HurtRankTwo, rank_data[2].user_name))
        end
    elseif length >= 3 then
        TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.HurtRankOne, rank_data[1].user_name))
        if rank_data[2].roleid > 0 then
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.HurtRankTwo, rank_data[2].user_name))
        end
        if rank_data[3].roleid > 0 then
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.HurtRankThree, rank_data[3].user_name))
        end
    end
end

function FuBenMiJingView:OnFlush()
    local data_list = GuildWGData.Instance:GetGuildLogicViewItemList()
    self.reward_cell_list:SetDataList(data_list)

    local fuben_info = GuildWGData.Instance:GetGuildFbData()
    local cur_num = GuildWGData.Instance:GetNowMonsterLun()
    local text_list = fuben_info[1].text_t
    self.node_list["value_1"].text.text = "["..text_list[1].str.."]"
    if text_list[3].str <= 0 then
        self.node_list["value_3"].text.text = 0
    else
        self.node_list["value_3"].text.text = text_list[3].str
    end

    local new_exp_num = math.ceil(fuben_info[1].mijing_exp)
    if nil == self.old_exp_num then
        self.old_exp_num = 0
    end
    self.save_exp_num = new_exp_num
    self:PlayExpAni()

    local other_cfg = GuildWGData.Instance:GetGuildSHOtherCfg()
    local beaty_info = BossWGData.Instance:GetMonsterInfo(other_cfg.xiannv_id)
    self:SetBeautyHp(text_list[2].str, beaty_info.name)

    --设置神兽血量
    self.node_list.hp_slider.slider.value = text_list[2].str / 100
    self.node_list.slider_value.text.text = text_list[2].str .. "%"

    local data = fuben_info[1].rankList
    if self.hurt_data_list and self.hurt_data_list[1] ~=nil and self.hurt_data_list[1].user_name ~= data[1].user_name then
        if Scene.Instance:GetSceneType() == SceneType.GuildMiJingFB then
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.BangShouYiZhu, data[1].user_name))
        end
    end

    if #data > 0 then
        --只显示前十名
        local top_ten_data = {}
        for i,v in ipairs(data) do
            if i <= 10 then
                table.insert(top_ten_data, data[i])
            end
        end
        self.rankdata_list:SetDataList(top_ten_data)

        self.node_list.no_data:CustomSetActive(IsEmptyTable(top_ten_data))
        self.hurt_data_list = data
    end
    if cur_num ~= self.cur_wave and self.cur_wave ~= -1 and cur_num > 1 then
        self:ShowHurtRankChuanWen(data)
    end

    self.cur_wave = cur_num

    --策划配置移除了2,3雕像，保留了中间1雕像只做fuben_info[1].doorHp[1]处理
    local num = fuben_info[1].doorHp[1] / fuben_info[1].doorMaxHp[1]
    if fuben_info[1].doorHp[1] <= 0 then
        self:DoorIsBeAtk(1,0)
        self:HandleDie(1)
    elseif fuben_info[1].doorHp[1] < fuben_info[1].doorMaxHp[1] then
        self:HandleHp(1,num * 100)
        self:DoorIsBeAtk(1,num)
    end

    --self:ShowGuide(cur_num,text_list[3].str)
    if self.my_rank_item then
        local my_data, my_rank = GuildWGData.Instance:GetMainRoleRankData()
        if self.node_list["my_rank_item"] then
            self.node_list["my_rank_item"]:CustomSetActive(my_data ~= nil)
        end
        self.my_rank_item:SetData(my_data)
        self.my_rank_item:SetIndex(my_rank)
    end

    -- self:_InternalFlushRewardCell()
end

function FuBenMiJingView:PlayExpAni()
    if self.is_doing_ani then
        return
    end
    if self.old_exp_num >= self.save_exp_num then return end

    self.is_doing_ani = true

    local text_obj = self.node_list["value_4"].text
    local next_exp_num = self.save_exp_num
    local complete_fun = function()
        self.old_exp_num = next_exp_num
        self.is_doing_ani = false
        self:PlayExpAni()
    end

    local update_fun = function(num)
        local value, postfix_name = CommonDataManager.ConverExpFBNum(num)
        if postfix_name == "" then
            text_obj.text = (string.format("%.0f", value)) .. postfix_name
        else
            text_obj.text = (value) .. postfix_name
        end
    end
    UITween.DONumberTo(text_obj, self.old_exp_num, next_exp_num, 0.5, update_fun, complete_fun)
    self.node_list["value_4"].transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 0.5)

end

function FuBenMiJingView:HandleDie(index)
    if not self.die_flag_list then
        self.die_flag_list = {}
        for i = 1, 3 do
            self.die_flag_list[i] = false
        end
    end
    if not self.die_flag_list[index] then
        local door_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").door_cfg
        if Scene.Instance:GetSceneType() == SceneType.GuildMiJingFB and door_cfg[index] then
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.YiShiShou, door_cfg[index].name))
            self.die_flag_list[index] = true
        end
    end
end

function FuBenMiJingView:HandleHp(index, percent)
    if not self.is_show_one_list then
        self.is_show_one_list = {}
        for i = 1, 3 do
            self.is_show_one_list[i] = false
        end
    end
    if not self.is_show_two_list then
        self.is_show_two_list = {}
        for i = 1, 3 do
            self.is_show_two_list[i] = false
        end
    end
    if Scene.Instance:GetSceneType() == SceneType.GuildMiJingFB then
        local door_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").door_cfg
        if nil == door_cfg[index]then
            return
        end
        if percent < 60 and percent > 30 and not self.is_show_one_list[index] then
            self.is_show_one_list[index] = true
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu60, door_cfg[index].name))
        elseif percent < 30 and percent > 0 and not self.is_show_two_list[index] then
            self.is_show_two_list[index] = true
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu30, door_cfg[index].name))
        end
    end
end

function FuBenMiJingView:OnClickDragon()
    local pos_x, pos_y = GuildWGData.Instance:GetBeautyPos()
    local scene_id = Scene.Instance:GetSceneId()
    --GuildWGData.Instance:GetMiJingDianDoor(4)
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
    GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end)
    GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y)
end

function FuBenMiJingView:OnClickGDExchange()
    self.show_info = not self.show_info
    self.node_list.gd_info_panel:CustomSetActive(self.show_info)
    self.node_list.gd_rank_panel:CustomSetActive(not self.show_info)
end

function FuBenMiJingView:StartGuaJi()
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function FuBenMiJingView:SetBeautyHp(hp_value,name)
    --self.node_list["title_value"].text.text = hp_value .. "%"
    local percent = hp_value / 100

    if hp_value < 60 and hp_value > 30 and not self.beauty_show_one then
        TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu60, name))
        self.beauty_show_one = true
    elseif hp_value < 30 and hp_value > 0 and not self.beauty_show_two then
        self.beauty_show_two = true
        TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu30, name))
    end

    local is_be_hit = self.old_percent ~= percent and percent > 0
    if is_be_hit then
        --处理受击传闻
        if self.be_hit_timequest == nil then
            local other_cfg = GuildWGData.Instance:GetGuildSHOtherCfg()
            local beaty_info = BossWGData.Instance:GetMonsterInfo(other_cfg.xiannv_id)
            TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.IsBeHit, beaty_info.name))
            self.be_hit_timequest = GlobalTimerQuest:AddDelayTimer(function ()
                self.be_hit_timequest = nil
            end,10)
        end
    end
    if self.is_fisrt then self.is_fisrt = false return end
    self.old_percent = percent
end

--判断其余三个门是否被攻击
function FuBenMiJingView:DoorIsBeAtk(doornum,hp_value)
    if self["old_door_hp_"..doornum] == nil then
        self["old_door_hp_"..doornum] = hp_value
        return
    end
    local is_be_hit = self["old_door_hp_"..doornum] ~= hp_value and hp_value > 0
    --self.node_list["HurtWaring"..doornum]:SetActive(is_be_hit)
    self["old_door_hp_"..doornum] = hp_value

    if is_be_hit then
        --处理受击传闻
        if self.be_hit_timequest ~= nil then return end
        local door_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").door_cfg
        if door_cfg[doornum] then
            TipWGCtrl.Instance:ShowZCRuneMsg( string.format(Language.ZCRuneText.IsBeHit, door_cfg[doornum].name))
            self.be_hit_timequest = GlobalTimerQuest:AddDelayTimer(function ()
                self.be_hit_timequest = nil
            end,10)
        end
    end
end

function FuBenMiJingView:SetBossComingTips()
    local fuben_info = GuildWGData.Instance:GetGuildFbData()
    local process_info = fuben_info[1].process_flag
    if process_info.boss_flag == 1 then
        if self.boss_close_time then
            GlobalTimerQuest:CancelQuest(self.boss_close_time)
            self.boss_close_time = nil
        end
        self.boss_close_time = GlobalTimerQuest:AddDelayTimer(function()
        end, 3)
    end
end

function FuBenMiJingView:SetNextWaveTime(data)
    --暂时注销掉
    -- if data ~= nil then
    --     -- self.node_list.txt_time.text.text = data
    --      self.node_list.time_1.text.text = data
    --      self.node_list.time_2.text.text = data
    --      self.node_list.time_3.text.text = data
    -- end
end


----------------------------------RankItemRender-----------------------------------------------------
RankItemRender = RankItemRender or BaseClass(BaseRender)
function RankItemRender:__init()
end

function RankItemRender:OnFlush()
    if not self.data then
        return
    end
    
    if self.data.user_name == nil or self.data.user_name == "" then
        self.node_list["content"]:SetActive(false)
    else
        self.node_list["content"]:SetActive(true)
    end

    self.node_list.rank_name.text.text = self.data.user_name
    if self.index then
        self.node_list.rank_num:SetActive(self.index > 3)
        if self.node_list.rank_img then
            self.node_list.rank_img:SetActive(self.index <= 3)
        else
            self.node_list.rank_num:SetActive(true)
        end
        if self.index > 3 then
             self.node_list.rank_num.text.text = self.index
        else
            if self.node_list.rank_img and self.index > 0 then
                --self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonImages("icon_paiming"..self.index))
                local bundel, asset = ResPath.GetCommonIcon("a3_tb_jp"..self.index)
                self.node_list.rank_img.image:LoadSprite(bundel, asset, function ()
                    self.node_list.rank_img.image:SetNativeSize()
                end)
            else
                self.node_list.rank_num.text.text = self.index
            end
        end
    else
        self.node_list.rank_num.text.text = Language.Guild.SHNotRank
    end

   local hurt_num = CommonDataManager.NotConverExpExtend(self.data.hurt_val) --BigNumFormat(self.data.hurt_val)
    self.node_list.rank_exp.text.text = hurt_num
end
