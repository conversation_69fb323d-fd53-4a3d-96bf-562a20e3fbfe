TianShenView = TianShenView or BaseClass(SafeBaseView)

TianShenView.TabIndex = {
	--TSInfo = TabIndex.tianshen_Info,
	Activation = TabIndex.tianshen_activation,     -- 天神-详情
	--HeJi = TabIndex.tianshen_heji,
	--Battle = TabIndex.tianshen_battle,
	ShenShi = TabIndex.tianshen_shenshi,           -- 诸神-道痕
	LingHeUplevel = TabIndex.tianshen_linghe_uplevel, -- 诸神-神饰
	ShenQi = TabIndex.tianshen_shenQi,             -- 诸神-器灵
	--LingHeReslove = TabIndex.tianshen_linghe_reslove,
	--LingHeCompose = TabIndex.tianshen_linghe_compose,
	BaGua = TabIndex.tianshen_bagua,               -- 诸神-天珠
}

local show_bg_index =
{
	TabIndex.tianshen_bagua,
	TabIndex.tianshen_temple,
}

function TianShenView:__init()
	self.is_upgrade_view_load = false
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self.default_index = TianShenView.TabIndex.Activation
	local bundle_name = "uis/view/tianshen_prefab"
	local view_bundle = "uis/view/tianshen_linghe_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(show_bg_index, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(TianShenView.TabIndex.BaGua, bundle_name, "layout_tianshen_bagua")
	self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
	--self:AddViewResource(TianShenView.TabIndex.TSInfo, bundle_name, "layout_tianshen_info_show")
	self:AddViewResource(TianShenView.TabIndex.Activation, bundle_name, "layout_activation")
	self:AddViewResource({TianShenView.TabIndex.Activation, TianShenView.TabIndex.ShenShi}, bundle_name, "layout_special_list_bar")
	--self:AddViewResource(TianShenView.TabIndex.HeJi, bundle_name, "layout_ts_heji")
	--self:AddViewResource(TianShenView.TabIndex.Battle, bundle_name, "layout_battle")
	self:AddViewResource(TianShenView.TabIndex.ShenShi, bundle_name, "layout_shenshi")
	self:AddViewResource(TianShenView.TabIndex.ShenQi, bundle_name, "layout_tianshen_shengqi")
	self:AddViewResource(TianShenView.TabIndex.LingHeUplevel, view_bundle, "layout_tslh_uplevel")
	self:AddViewResource(TabIndex.tianshen_temple, bundle_name, "layout_tianshen_shendian")

	--self:AddViewResource(TianShenView.TabIndex.LingHeReslove, view_bundle, "layout_tslh_resolve")
	--self:AddViewResource(TianShenView.TabIndex.LingHeCompose, view_bundle, "layout_linghe_compose")
	self:AddViewResource(0, common_bundle_name, "HorizontalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

	--self.tab_sub = { nil, nil, Language.TianShenLingHe.TabGrop, nil, nil}
	self.remind_tab = {
		{ RemindName.TianShen_Activation_Tab },
		--{ RemindName.TianShenHeJi },
		{ RemindName.TianShen_ShenShi },
		{ RemindName.TianShenLingHeUpLevel }, --RemindName.TianShenLingHeReslove, RemindName.TianShenLingHeCompose 
		{ RemindName.TianShen_ShenQi },
		{ RemindName.TianShen_BaGua },
		{ RemindName.TianShen_Temple},
		--{ RemindName.TianShen_Battle },
	}

	self.tianshen_active_audio_play_t = {}
	self.tianshen_shenshi_audio_play_t = {}

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	self.jump_open = false

	self.cur_select_tianshen_series = 0
	self.cur_select_shenshi_tianshen_series = 0
	self.cur_select_shenqi_tianshen_series = 0
	self.ts_quality_type_vo_list = {}
	self.shenqi_type_vo_list = {}

	self:SetTabShowUIScene(TabIndex.tianshen_activation, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.TIANSHEN_INFO})
	self:SetTabShowUIScene(TabIndex.tianshen_shenshi, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.TIANSHEN_INFO})
	self:SetTabShowUIScene(TabIndex.tianshen_linghe_uplevel, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.TIANSHEN_INFO})
	self:SetTabShowUIScene(TabIndex.tianshen_shenQi, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.TIANSHEN_INFO})

	self:ShenShiInit()
	self:BaGuaInit()
end

function TianShenView:__delete()
	self.tianshen_active_audio_play_t = {}
	self.tianshen_shenshi_audio_play_t = {}
	self.ts_quality_type_vo_list = {}
	self.shenqi_type_vo_list = {}

	self.is_upgrade_view_load = false
end

function TianShenView:ReleaseCallBack()
	self.bg_bundle = nil
	self.bg_asset = nil

	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	self.jump_open = false
	if self.special_remind_event then
		for k, v in pairs(self.special_remind_list) do
			RemindManager.Instance:UnBind(self.special_remind_event)
		end
		self.special_remind_event = nil
	end

	self.is_open = false

	if MainuiWGCtrl.Instance then
		MainuiWGCtrl.Instance:CacheTableClearData()
	end
	self:ReleaseTianShenActivationCallBack()
	--self:BattleReleaseCallBack()
	self:ShenShiReleaseCallBack()
	self:ShengQiReleaseCallBack()
	self:BaGuaReleseCallBack()
	self:ReleaseActivationAccordion()
	self:ULReleaseCallBack()
	self:ReleaseTianshenTempleCallback()
	--self:RSReleaseCallBack()
	--self:CPReleaseCallBack()
	--self:HeJiReleseCallBack()

	if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
	self.item_data_change_callback = nil
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TianShenView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function TianShenView:LoadCallBack()
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TianShenView, self.get_guide_ui_event)

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		--self.tabbar:SetVerTabbarIconStr("ts_view")
		--self.tabbar:SetVerTabbarCellName("TianShen_VerticalTabbarCell")
		self.tabbar:Init(Language.TianShen.TabGrop, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.TianShenView, self.tabbar)
	end
	TianShenWGData.Instance:UpdateTianShenInfoList(false)

	if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end
end

function TianShenView:LoadIndexCallBack(index)
	if index == TianShenView.TabIndex.Activation then
		self:LoadTianShenActivationCallBack()
	--[[elseif index == TianShenView.TabIndex.Battle then
		self:BattleLoadCallBack()]]
	elseif index == TianShenView.TabIndex.ShenShi then
		self:ShenShiLoadCallBack()
	elseif index == TianShenView.TabIndex.ShenQi then
		self:ShenQiLoadCallBack()
	elseif index == TianShenView.TabIndex.BaGua then
		self:BaGuaLoadCallBack()
	elseif index == TianShenView.TabIndex.LingHeUplevel then
		self:ULInitView()
	-- elseif index == TianShenView.TabIndex.LingHeReslove then
	-- 	self:RSInitView()
	-- elseif index == TianShenView.TabIndex.LingHeCompose then
	-- 	self:CPInitView()
	--elseif index == TianShenView.TabIndex.HeJi then
		--self:HeJiLoadCallBack()
	elseif index == TabIndex.tianshen_temple then
		self:LoadTianshenTempleCallback()
	end
end

function TianShenView:OpenCallBack()
	self:OpenTianShenActivationCallBack()
	self:ShenShiOpenCallBack()
	self:OpenShenQiCallBack()
	if nil ~= self.tabbar then
		self.tabbar:AddAllListen()
	end

	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function TianShenView:CloseCallBack()
	self:CloseTianShenActivationCallBack()
	self:ShenShiCloseCallBack()
	self:CloseShenQiCallBack()
	if nil ~= self.tabbar then
		self.tabbar:UnAllListen()
	end
end

function TianShenView:ShowIndexCallBack(index)
	self.is_oneKey = false
	local bundle, asset = ResPath.GetRawImagesJPG("a3_zs_bj")
	local title = Language.TianShen.TitleName
	if index == TianShenView.TabIndex.Activation then
		self:ShowTianShenActivationIndexCallBack()
	--[[elseif index == TianShenView.TabIndex.Battle then -- 天神-出战
		bundle, assert = ResPath.GetRawImagesPNG("a2_pz_bj")
		self:BattleShowIndexCallBack()]]
	elseif index == TianShenView.TabIndex.ShenShi then
		self:ShenShiShowIndexCallback()
		title = Language.TianShen.TitleName1
	elseif index == TianShenView.TabIndex.ShenQi then
		self:ShenQiShowIndexCallback()
		title = Language.TianShen.TitleName2
	elseif index == TianShenView.TabIndex.BaGua then
		self:BaGuaShowIndexCallBack()
		title = Language.TianShen.TitleName4
		bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.NOT_HAVE_ROLEMODEL)
	elseif index == TianShenView.TabIndex.LingHeUplevel then
		self:ULShowIndexCallBack()
		title = Language.TianShen.TitleName3
	-- elseif index == TianShenView.TabIndex.LingHeReslove then
	-- 	bundle, assert = ResPath.GetRawImagesPNG("a2_ty_bg")
	-- 	self:RSShowIndexCallBack()
	-- 	title = Language.TianShen.TitleName3
	-- elseif index == TianShenView.TabIndex.LingHeCompose then
	-- 	bundle, assert = ResPath.GetRawImagesPNG("a2_ty_bg")
	-- 	self:CPShowIndexCallBack()
	-- 	title = Language.TianShen.TitleName3
	--[[elseif index == TianShenView.TabIndex.HeJi then
		self:HeJiShowIndexCallBack()
		title = Language.TianShen.TitleName6
		bundle, assert = ResPath.GetRawImagesJPG("a2_ts_hj_bg")]]
	elseif index == TabIndex.tianshen_temple then
		bundle, asset = ResPath.GetRawImagesJPG("a3_sld_di")
		self:ShowTianshenTempleCallback()
		title = Language.TianShen.TitleName7
	end

	self.node_list.title_view_name.text.text = title
	if self.bg_bundle ~= bundle and self.bg_asset ~= asset then
		self.bg_bundle = bundle
		self.bg_asset = asset
		if self.node_list and self.node_list.RawImage_tongyong then
			self.node_list["RawImage_tongyong"].raw_image:LoadSprite(self.bg_bundle, self.bg_asset, function()
				self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
			end)
		end
	end
end

function TianShenView:SpecialAppearanceRemindChange(name, num)

end

function TianShenView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if index == TianShenView.TabIndex.Activation then
			self:ActivationOnFlush(param_t)
		--[[elseif index == TianShenView.TabIndex.Battle then -- 天神-出战
			self:BattleOnFlush()]]
		elseif index == TianShenView.TabIndex.ShenShi then
			self:ShenShiOnFlush(param_t)
		elseif index == TianShenView.TabIndex.ShenQi then
			self:FlushShenQiSelect(param_t)
			--self:OnShengQiFlush()
			self:PlayShenQiEffect()
			self:FlushShenQiAllRemind()
		elseif index == TianShenView.TabIndex.BaGua then
			self:FlushBaGuaJump(param_t)
		elseif index == TianShenView.TabIndex.LingHeUplevel then
			self:ShenShiULOnFlush(v.force_jump_ts_index)
		-- elseif index == TianShenView.TabIndex.LingHeReslove then
		-- 	self:FlushResloveView()
		-- elseif index == TianShenView.TabIndex.LingHeCompose then
		-- 	if k == "all" then
		-- 		self.cp_flush_wait_flag = true
		-- 		self:CPSelectToggle(v.force_big_type, v.force_small_type, false)
		-- 	elseif k == "protocol_change" then
		-- 		self.cp_flush_wait_flag = true
		-- 		self:FlushCPToggleAllData()
		-- 		self:CPSelectToggle(nil, nil, false)
		-- 	end
		--[[elseif index == TianShenView.TabIndex.HeJi then
			self:HeJiInfoViewOnFlush()]]
		elseif index == TabIndex.tianshen_temple then
			self:FlushTianshenTempleCallback(param_t)
		end

		if k == "InsertAnim" then
			if self.show_index == TianShenView.TabIndex.BaGua then
				self:CheckPlayeBaGuaEquipAnim(v)
			end
		elseif k == "FlushShopRed" then
			if self.show_index == TianShenView.TabIndex.BaGua then
				self:FLushBaGuaShopRed()
			end
		elseif k == "Flushlist" then
			if self.show_index == TianShenView.TabIndex.ShenQi then
				self:FlushShenQiAllRemind()
				self:UpdateCellList()
			end
		end

		if v.sub_view_name == "baguashop" then
			TianShenWGCtrl.Instance:OpenBaGuaShopView()
		end
	end
end

function TianShenView:IsShowActivityImg()
	if ActivityWGCtrl.Instance:IsOpenServerOpen() then
		if ServerActivityWGData.Instance:IsOpenSprintAct(ServerActClientId.MOUNT_JINJIE) then
			return TianShenView.TabIndex.Activation
		elseif ServerActivityWGData.Instance:IsOpenSprintAct(ServerActClientId.LINGCHONG_JINJIE) then
			return TianShenView.TabIndex.ShenShi
		end
	end
	return -1
end

--物品变化
function TianShenView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if not self:IsLoadedIndex(self.show_index) then
		return
	end

	if self.show_index == TianShenView.TabIndex.Activation then
		self:FlushConsume()
		self:FlushActivationImageRender()
		self:FlushTsIconList()
		--self:FlushActivationHuaMo()
	elseif self.show_index == TianShenView.TabIndex.ShenShi then
		self:ShenShiItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	elseif self.show_index == TianShenView.TabIndex.ShenQi then
		self:ShenQiItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	elseif self.show_index == TianShenView.TabIndex.BaGua then
		self:BaGuaItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	elseif self.show_index == TabIndex.tianshen_temple then
		self:TianshenTempleItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	end
end

function TianShenView:IsShowingShenShiPanel()
	return self.show_index == TianShenView.TabIndex.ShenShi
end

function TianShenView:GetCurSelectListData(select_data)
	if nil == select_data then
		return self.select_data
	end
	self.select_data = select_data
end

function TianShenView:OnClickBtnWuXingTip()
	ViewManager.Instance:Open(GuideModuleName.WuXingTip)
end

-- [废弃]
function TianShenView:GetTianShenQualityListVo()
	if IsEmptyTable(self.ts_quality_type_vo_list) then
		for i = 0, 5 do
			local vo = {}
			vo.name = Language.TianShen.TianShenSeriesColorStr[i]
			vo.series = i + 2
			self.ts_quality_type_vo_list[i] = vo
		end
	end
	return self.ts_quality_type_vo_list
end

function TianShenView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	return self.node_list[ui_name]
end

---------------------------------------- TianShenItem ----------------------------------------
TianShenItem = TianShenItem or BaseClass(BaseRender)

function TianShenItem:__init()
end

function TianShenItem:__delete()
	if self.show_cell then
        self.show_cell:DeleteMe()
        self.show_cell = nil
    end
end

function TianShenItem:LoadCallBack()
    self.show_cell = ItemCell.New(self.node_list["item_icon"])
    self.show_cell:SetIsShowTips(false)
end

function TianShenItem:OnFlush()
	if self.data == nil then return end
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.index)
	local tianshen_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.data.index)
	if tianshen_item_cfg then
		self.show_cell:SetData({item_id = tianshen_item_cfg.act_item_id})
	end

	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.index)
	if not tianshen_info then
		return
	end

	if self.data.active_status == 1 then
		local golden_star_num = math.ceil(tianshen_info.star / GameEnum.ITEM_MAX_STAR)
		local silver_star_num = tianshen_info.star % GameEnum.ITEM_MAX_STAR
		if tianshen_info.star > 0 and silver_star_num == 0 then
			silver_star_num = GameEnum.ITEM_MAX_STAR
		end

		local star_res_list = GetStarImgResByStar(tianshen_info.star)
		for i = 1, GameEnum.ITEM_MAX_STAR do
			self.node_list["star" .. i]:SetActive(true)
			self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
		end
	end

	self.node_list.level_bg:SetActive(self.data.active_status == 1)
	self.node_list.star:SetActive(self.data.active_status == 1)
	self.node_list.no_get_sign:SetActive(self.data.active_status == 0)
	self.node_list.lbl_ml_item_name.text.text = tianshen_cfg.bianshen_name
	self.node_list.level.text.text = tianshen_info.level
	local view = ViewManager.Instance:GetView(GuideModuleName.TianShenView)
	local ts_select_index = TianShenWGData.Instance:ActivationTsSelectIndex()
	local flag = false
	local flag_text = ""
	local tianshen_data = TianShenWGData.Instance
	local is_remind = false
	if view:GetShowIndex() == TianShenView.TabIndex.Activation then
		if tianshen_data:SpecialTianshenCanActive(self.data.index) then
			is_remind = true
		elseif tianshen_data:SpecialTianshenCanUpStar(self.data.index) then
			is_remind = true
		end

		local active = tianshen_info and tianshen_info.zhan_index >= 0 or false
		self.node_list.img_battle:SetActive(active)
		self.node_list.remind:SetActive(tianshen_data:SpecialTianshenCanUpGrade(self.data.index) or is_remind)
	elseif view:GetShowIndex() == TianShenView.TabIndex.ShenShi then
		ts_select_index = TianShenWGData.Instance:ShenShiTsSelectIndex()
		if tianshen_data:SpecialTianshenCanActive(self.data.index) then
			flag = true
			flag_text = Language.TianShen.FlagTipTxt.CanActive
		else
			local active = tianshen_info and tianshen_info.zhan_index >= 0 or false
			self.node_list.img_battle:SetActive(active)
			flag = false
		end
		local has_red, compose_red = TianShenWGData.Instance:CheckEquipShenShiRed(self.data.index)

		self.node_list.remind:SetActive(has_red > 0 or compose_red == 1)
	elseif view:GetShowIndex() == TianShenView.TabIndex.LingHeUplevel then
		self.node_list.remind:SetActive(self.data.is_remind)
	end

	self:SetTsSelect(ts_select_index == self.data.index)
	self.node_list.flag_tip:SetActive(flag)
	self.node_list.flag_tip_text.text.text = flag_text
end

function TianShenItem:SetTsSelect(is_select)
	self.node_list.img_hl:SetActive(is_select)
end


---------------------------------------- TianShenQualityTypeItem ----------------------------------------
TianShenQualityTypeItem = TianShenQualityTypeItem or BaseClass(BaseRender)

function TianShenQualityTypeItem:__init()

end

function TianShenQualityTypeItem:__delete()

end

function TianShenQualityTypeItem:OnFlush()
	if self.data == nil then return end
	self.node_list.quality_name.text.text = self.data.name
end

function TianShenQualityTypeItem:SetSelect(is_select)
	self.node_list.img_hl:SetActive(is_select)
end
