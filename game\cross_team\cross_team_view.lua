--跨服组队面板 --策划说跨服组队和原服组队没有关系，因此新加面板
CrossTeamView = CrossTeamView or BaseClass(SafeBaseView)
local MAX_TEAM_MEMBER_NUMS = 3
function CrossTeamView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/new_team_ui_prefab", "cross_team_panel")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self.team_cell_flush = GlobalEventSystem:Bind(TeamInfoQuery.TEAM_INFO_BACK, BindTool.Bind(self.TeamCellFlush, self))
end

function CrossTeamView:ReleaseCallBack()

    if self.all_team_list then
        self.all_team_list:DeleteMe()
        self.all_team_list = nil
    end

    if self.right_reward_list then
        self.right_reward_list:DeleteMe()
        self.right_reward_list = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
    self:ReleaseMyTeam()
    self.select_left_idx = nil
    if CrossTeamWGData.Instance then
        CrossTeamWGData.Instance:SetPtTeamLimitLevel(nil, nil)
    end

    if self.world_talk_timecount then
        GlobalEventSystem:UnBind(self.world_talk_timecount)
        self.world_talk_timecount = nil
    end

    if self.word_talk_cd_over then
        GlobalEventSystem:UnBind(self.word_talk_cd_over)
        self.word_talk_cd_over = nil
    end
end

function CrossTeamView:OpenCallBack()
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_INFO)
end

function CrossTeamView:LoadCallBack()
    local bundle, asset = ResPath.GetRawImagesJPG("a3_zd_bg")
    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

    XUI.AddClickEventListener(self.node_list["pt_btn_flush"], BindTool.Bind(self.TeamOnClickFlush, self))                --刷新列表
    XUI.AddClickEventListener(self.node_list["pt_btn_create_team"], BindTool.Bind1(self.TeamOnClickCrateTeam, self))     --创建队伍
    XUI.AddClickEventListener(self.node_list["pt_btn_create_team2"], BindTool.Bind1(self.TeamOnClickCrateTeam, self))
    XUI.AddClickEventListener(self.node_list["pt_btn_auto_req"], BindTool.Bind1(self.TeamAutoRequest, self))             --未组队：一键申请， 已组队：退出队伍
    XUI.AddClickEventListener(self.node_list["btn_quick_team"], BindTool.Bind1(self.OnClickQuitTeam, self))
    XUI.AddClickEventListener(self.node_list["btn_all_team_target"],
        BindTool.Bind(self.TeamOnClickChangeGoal, self, true))                                                           --改变平台等级筛选
    XUI.AddClickEventListener(self.node_list["pt_btn_apply"], BindTool.Bind1(self.TeamOnClickApplyList, self))           --申请列表
    XUI.AddClickEventListener(self.node_list["pt_btn_speak"], BindTool.Bind1(self.TeamOnClickWorldTalk, self))           --喊话（世界， 跨服）

    self.node_list.title_view_name.text.text = Language.CrossTeam.ViewName

    self.node_list["btn_team_common1"].button:AddClickListener(BindTool.Bind2(self.OnClickLeftBtn, self, 1)) --全部队伍
    self.node_list["btn_team_common2"].button:AddClickListener(BindTool.Bind2(self.OnClickLeftBtn, self, 2)) --我的队伍

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = false,
            show_bind_gold = false,
            show_coin = false,
            show_silver_ticket = false,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    self:InitMyTeam()
    self.default_select_index = 1
    self:OnClickLeftBtn(1)
    self.world_talk_timecount = GlobalEventSystem:Bind(TeamWorldTalk.NEW_TEAM_WORLD_TALK,
        BindTool.Bind(self.UpdateTeamWordTalkBtn, self))
    self.word_talk_cd_over = GlobalEventSystem:Bind(TeamWorldTalk.COMPLETE_CALL_BACK,
        BindTool.Bind(self.ComleteTeamWoldTalkCD, self))
end

function CrossTeamView:UpdateTeamWordTalkBtn(time)
    if self.node_list.pt_btn_speak_text then
        self.node_list.pt_btn_speak_text.text.text = string.format(Language.NewTeam.WorldTalk5_New, time)
    end
    if not self.pt_has_change_btn_gray then
        self.pt_has_change_btn_gray = true
        XUI.SetGraphicGrey(self.node_list["pt_btn_speak"], true)
    end
end

function CrossTeamView:ComleteTeamWoldTalkCD()
    if CountDownManager.Instance:HasCountDown("team_world_talk") then
        CountDownManager.Instance:RemoveCountDown("team_world_talk")
    end

    if self.node_list.pt_btn_speak_text then
        self.node_list.pt_btn_speak_text.text.text = Language.NewTeam.WorldTalk7
    end

    self.pt_has_change_btn_gray = nil
    XUI.SetGraphicGrey(self.node_list["pt_btn_speak"], false)
end

function CrossTeamView:OnClickLeftBtn(index)
    if self.select_left_idx == index then
        return
    end

    self.node_list["all_team_panel"]:CustomSetActive(index == 1)
    self.node_list["my_team_panel"]:CustomSetActive(index == 2)

    self.select_left_idx = index
    if self.select_left_idx == 1 then
        self:TeamOnClickFlush()
    else
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_INFO)
    end
    self:OnFlushRight()
end

function CrossTeamView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if "all" == k then
            self:OnFlushRight()
        elseif "jump_my_team" == k then
            if self.select_left_idx ~= 2 then
                self:OnClickLeftBtn(2)
            else
                self:OnFlushRight()
            end
        elseif "jump_all_team" == k then
            if self.select_left_idx ~= 1 then
                self:OnClickLeftBtn(1)
            else
                self:OnFlushRight()
            end
        end
    end
end

function CrossTeamView:ResetSelectDefIndex()
    if CrossTeamWGData.Instance:GetIsInTeam() == 1 then
        self.default_select_index = 2
    else
        self.default_select_index = 1
    end
end

function CrossTeamView:OnFlushRight()
    self:ResetSelectDefIndex()
    self.select_left_idx = self.select_left_idx or self.default_select_index
    if self.select_left_idx == 1 then
        self:FlushAllTeam()
    else
        self:FlushMyTeam()
    end

    local is_in_team = CrossTeamWGData.Instance:GetIsInTeam() == 1
    -- if is_in_team then                                                                   --如果有队伍s
    --     self.node_list.pt_btn_auto_req_text.text.text = Language.NewTeam.QuitTeamBtnText --退出队伍
    -- else
    --     self.node_list.pt_btn_auto_req_text.text.text = Language.NewTeam.AutoReqBtnText  --一键申请
    -- end
    self.node_list.btn_quick_team:SetActive(is_in_team)
    self.node_list["pt_btn_auto_req"]:SetActive(not is_in_team and not IsEmptyTable(data_list))
    
    self.node_list["pt_btn_create_team"]:SetActive(not is_in_team)
    self:ApplyRemindState()
end

function CrossTeamView:ApplyRemindState()
    local apply_list = CrossTeamWGData.Instance:GetReqTeamList()
    if #apply_list > 0 then
        self.node_list["PTRemind"]:SetActive(true)
    else
        self.node_list["PTRemind"]:SetActive(false)
    end
end

function CrossTeamView:FlushAllTeam()
    if IsEmptyTable(self.all_team_list) then
        self:CreateAllList()
    end
    local data_list = CrossTeamWGData.Instance:GetTeamListForView()
    local is_in_team = CrossTeamWGData.Instance:GetIsInTeam() == 1
    local my_uid = RoleWGData.Instance:InCrossGetOriginUid()

    if is_in_team then                              --如果有队伍
        local remove_pos = 0
        for k, v in pairs(data_list) do
            for i, v1 in ipairs(v.member_list) do
                if v1.uuid.temp_low == my_uid then
                    remove_pos = k
                    break
                end
            end
        end

        if remove_pos > 0 then
            table.remove(data_list, remove_pos)
        end

        --self.node_list.pt_btn_auto_req_text.text.text = Language.NewTeam.QuitTeamBtnText --退出队伍
    else
        --self.node_list.pt_btn_auto_req_text.text.text = Language.NewTeam.AutoReqBtnText  --一键申请
        self.node_list["pt_btn_speak"]:CustomSetActive(false)
    end

    self.node_list["btn_team_common2"]:CustomSetActive(is_in_team)
    self.node_list["pt_btn_create_team2"]:CustomSetActive(not is_in_team)
    self.all_team_list:SetDataList(data_list)

    
    -- if IsEmptyTable(data_list) and not is_in_team then
    --     self.node_list["pt_btn_auto_req"]:SetActive(false)
    -- else
    --     self.node_list["pt_btn_auto_req"]:SetActive(true)
    -- end
    self.node_list["pt_btn_apply"]:SetActive(is_in_team and 1 == CrossTeamWGData.Instance:GetIsTeamLeader())
    self.node_list.not_team_container:SetActive(IsEmptyTable(data_list))
    self.node_list.quick_list_bg:SetActive(not IsEmptyTable(data_list))

    local min_level, max_level = CrossTeamWGData.Instance:GetPtTeamLimitLevel()
    local str = string.format(Language.NewTeam.PTLevelLimitTop, min_level, max_level)
    EmojiTextUtil.ParseRichText(self.node_list["pt_level_limit_text"].emoji_text, str, 24, COLOR3B.BLUE_TITLE)
end

function CrossTeamView:CreateAllList()
    if IsEmptyTable(self.all_team_list) then
        self.all_team_list = AsyncListView.New(CrossAllTeamListRender, self.node_list.pt_ph_quick_list)
    end
end

--是否在全部队伍页签
function CrossTeamView:IsOpenTeamList()
    if self:IsOpen() and self:IsLoaded() and self.select_left_idx == 1 then
        return true
    end
    return false
end

function CrossTeamView:TeamOnClickFlush()
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_LIST)
end

-- 未组队：创建队伍
function CrossTeamView:TeamOnClickCrateTeam()
    CrossTeamWGCtrl.Instance:SendCreateTeamReq()
end

--未组队：一键申请， 已组队：退出队伍
function CrossTeamView:TeamAutoRequest()
    --一键申请
    local team_list = CrossTeamWGData.Instance:GetTeamList()
    for i, v in pairs(team_list) do
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_JOIN, v.index)
    end
    -- if CrossTeamWGData.Instance:GetIsInTeam() == 0 then
    --     --一键申请
    --     local team_list = CrossTeamWGData.Instance:GetTeamList()
    --     for i, v in pairs(team_list) do
    --         CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_JOIN, v.index)
    --     end
    -- else
    --     CrossTeamWGCtrl.Instance:ExitTeam()
    -- end
end

function CrossTeamView:OnClickQuitTeam()
    CrossTeamWGCtrl.Instance:ExitTeam()
end

function CrossTeamView:TeamOnClickChangeGoal(is_pingtai)
    --if NewTeamWGData.Instance:GetIsMatching() then
    --	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchTip)
    --	return
    --end
    if 1 == CrossTeamWGData.Instance:GetIsInTeam() and 0 == CrossTeamWGData.Instance:GetIsTeamLeader() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
        return
    end
    CrossTeamWGCtrl.Instance:OpenChangeGoalView(is_pingtai)
end

function CrossTeamView:TeamOnClickApplyList()
    NewTeamWGCtrl.Instance:OpenApplyView()
end

function CrossTeamView:TeamOnClickWorldTalk()
    local name = RoleWGData.Instance:GetRoleVo().name
    CrossTeamWGCtrl.Instance:ShowTalkView(name)
end

------------------itemRender-----------------
CrossAllTeamListRender = CrossAllTeamListRender or BaseClass(BaseRender)

function CrossAllTeamListRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_apply"], BindTool.Bind1(self.OnClickApply, self))
    XUI.AddClickEventListener(self.node_list["btn_invate"], BindTool.Bind1(self.OnClickInvite, self))
end

function CrossAllTeamListRender:__delete()

end

function CrossAllTeamListRender:OnFlush()
    --找到队长
    local leader = nil
    --是否是自己的队伍
    local is_my_team = false
    local my_uid = RoleWGData.Instance:GetUUid()
    local leader_index = self.data.leader_index
    local count = 0
    for i, v in pairs(self.data.member_list) do
        if v.uuid.temp_low > 0 then
            count = count + 1
        end
        if v.is_leader == 1 then
            leader = v
        end
        if v.uuid == my_uid then
            is_my_team = true
        end
        --头像信息相关
        if not self.member_info_list then
            self.member_info_list = {}
        end
        if not self.member_info_list[i] then
            self.member_info_list[i] = CrossMemberHeadInfoRender.New(self.node_list["info" .. i])
        end
        self.member_info_list[i]:SetData(v)
    end
    --目标名（副本名）
    self.node_list["fb_name"].text.text = CrossTeamWGData.Instance:GetSceneName()
    if leader then
        --队长等级
        local level_str = string.format(Language.NewTeam.PTLevel2, leader.level)
        EmojiTextUtil.ParseRichText(self.node_list["lbl_leader_level"].emoji_text, level_str)
        --队长名字
        self.node_list["team_name"].text.text = leader.name
    end

    --我的队伍标记
    self.node_list["my_team_flag"]:CustomSetActive(false)
    self.node_list["match_text"]:CustomSetActive(false)
    --申请按钮
    self.node_list["btn_apply"]:CustomSetActive(not is_my_team)

    --等级限制
    local str = string.format(Language.NewTeam.PTLevelLimit, self.data.min_level_limit, self.data.max_level_limit)
    EmojiTextUtil.ParseRichText(self.node_list["level_limit"].emoji_text, str, 20, COLOR3B.DEFAULT_NUM)

    self.node_list["btn_apply"]:CustomSetActive(not is_my_team and (count < MAX_TEAM_MEMBER_NUMS))

    --local is_maxmember = count < MAX_TEAM_MEMBER_NUMS
    --self.node_list["btn_invate"]:CustomSetActive(is_my_team and is_maxmember)

    local is_in_team = CrossTeamWGData.Instance:GetIsInTeam() == 1
    XUI.SetGraphicGrey(self.node_list["btn_apply"], is_in_team)
end

function CrossAllTeamListRender:OnClickApply()
    if SocietyWGData.Instance:GetIsInTeam() == 1 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.AlreadyInTeam)
        return
    end
    local role_level = RoleWGData.Instance.role_vo.level
    if role_level < self.data.min_level_limit or role_level > self.data.max_level_limit then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.ApplyTeamError)
        return
    end
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_JOIN, self.data.index)
end

function CrossAllTeamListRender:OnClickInvite()
    NewTeamWGCtrl.Instance:OpenInviteView()
end

---------------------------------CrossMemberHeadInfoRender----------------------------------------
---单条队伍列表单个头像信息
CrossMemberHeadInfoRender = CrossMemberHeadInfoRender or BaseClass(BaseRender)
function CrossMemberHeadInfoRender:__init()
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function CrossMemberHeadInfoRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function CrossMemberHeadInfoRender:OnFlush()
    if not self.data then return end
    ----队长标记 --策划说不显示了
    self.node_list.leader_img:CustomSetActive(self.data.is_leader == 1)
    --没有队员信息，隐藏相关信息
    self.node_list.no_data_hide:CustomSetActive(self.data.uuid.temp_low > 0)
    self.node_list.lbl_camp:CustomSetActive(false) --self.data.uuid > 0)
    self.node_list.label_camp:CustomSetActive(false) -- self.data.uuid > 0)
    self.node_list.lbl_no_people:CustomSetActive(self.data.uuid.temp_low <= 0)
    self.node_list.head_cell:CustomSetActive(self.data.uuid.temp_low > 0)
    if self.data.uuid.temp_low > 0 then
        --Vip等级
        --self.node_list.vip_level:SetActive(self.data.vip_level > 0)
        --self.node_list.vip_level.text.text = string.format(Language.NewTeam.VipLevel,  self.data.vip_level)
        --       local vip_res_name = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag) and "vip_hide" or "vip".. self.data.vip_level
        -- local bundle, asset = ResPath.GetVipIcon(vip_res_name)
        -- self.node_list.vip_image:SetActive(self.data.vip_level > 0)
        -- self.node_list.vip_image.image:LoadSpriteAsync(bundle, asset, function ()  		
        -- 	self.node_list.vip_image.image:SetNativeSize()
        -- end)
        
        --人物等级
        local str = string.format(Language.NewTeam.PTLevel2, self.data.level)
        EmojiTextUtil.ParseRichText(self.node_list["lbl_level"].emoji_text, str, 21, COLOR3B.WHITE)
        self.node_list.team_name.text.text = self.data.name -- 名称
        --self.node_list.lbl_camp.text.text = self.data.capability --战力
        ----头像
        local data = {}
        data.role_id = self.data.uuid.temp_low
        data.prof = self.data.prof
        data.sex = self.data.sex
        local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME,
            self.data.shizhuang_photoframe)
        data.fashion_photoframe = image_cfg and image_cfg.resouce or 0
        self.head_cell:SetData(data)
    end
end
