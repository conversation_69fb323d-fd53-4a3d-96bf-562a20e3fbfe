EverythingUnderTheSunView = EverythingUnderTheSunView or BaseClass(SafeBaseView)

local TOGGLE_MAX = 12
function EverythingUnderTheSunView:__init()
	self.view_style = ViewStyle.Half
    self.is_safe_area_adapter = true
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/everything_under_the_sun_ui_prefab", "layout_everything_under_the_sun")
end

function EverythingUnderTheSunView:LoadCallBack()
    self.first_index = 0
    self.old_select_act = -2
    self.cur_select_act = -1
    self.cur_select_small_type = -1
    self.act_cell_list = {}

    if not self.model_list then
        self.model_list = {}
        for i = 1, 3 do
            local model_render = OperationActRender.New(self.node_list["model_pos" .. i])
            model_render:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
            self.model_list[i] = model_render
        end
    end

    self:CreateActAccordionList()
    XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGoToBtn, self))
end

function EverythingUnderTheSunView:ReleaseCallBack()
    if self.act_accordion_list then
        for k, v in pairs(self.act_accordion_list) do
            v:DeleteMe()
        end

        self.act_accordion_list = nil
    end

    if self.act_cell_list then
        for k, v in pairs(self.act_cell_list) do
            for k1, cell in pairs(v) do
                cell:DeleteMe()
            end

            v = nil
        end

        self.act_cell_list = nil
    end

    if self.model_list then
        for k, v in pairs(self.model_list) do
            v:DeleteMe()
        end

        self.model_list = nil
    end
end

function EverythingUnderTheSunView:OnFlush()
    self:FlushTitleImg()
    self:FlushModelList()
end

function EverythingUnderTheSunView:CreateActAccordionList()
    if self.act_accordion_list then return end
    if self.act_cell_list == nil then return end

    local act_accordion_list = EverythingUnderTheSunWGData.Instance:GetShowAccordionList()
    self.act_accordion_list = {}

    self.first_index = #act_accordion_list > 0 and 1 or 0
    for i = 1, TOGGLE_MAX do
        local item_render = EverythingUnderTheSunBigRender.New(self.node_list.Content:FindObj("SelectBtn" .. i))
        local act_type = act_accordion_list[i] and act_accordion_list[i].act_type or 0
        local accordion_data = act_accordion_list[i] and act_accordion_list[i].info
        local is_next = act_accordion_list[i] and act_accordion_list[i].is_next
        item_render:SetToggleValueChangeCallBack(BindTool.Bind(self.OnClickHandler, self, i))
        item_render:SetData(accordion_data)
        self.act_accordion_list[i] = item_render
        self:LoadAccordionCell(i, act_type, is_next)
    end
end

function EverythingUnderTheSunView:LoadAccordionCell(index, act_type, is_next)
    local accor_data = EverythingUnderTheSunWGData.Instance:GetBigTypeList(act_type, is_next)
	if IsEmptyTable(accor_data) then
		return
	end

	local res_async_loader = AllocResAsyncLoader(self, "slwx_accordion_item" .. index)
	res_async_loader:Load("uis/view/everything_under_the_sun_ui_prefab", "type_toggle", nil,
		function(new_obj)
            if IsNil(new_obj) then
                return
            end

            table.sort(accor_data, SortTools.KeyLowerSorter("small_type"))
			local item_vo = {}
			for i = 1, #accor_data do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
                local parent_list = self.act_accordion_list[index]:GetSmallTypeList()
				obj_transform:SetParent(parent_list.transform, false)
				obj:GetComponent("Toggle").group = parent_list.toggle_group
				local item_render = EverythingUnderTheSunRender.New(obj)
                local small_type = accor_data[i] and accor_data[i].small_type or 0
                local cell_data = EverythingUnderTheSunWGData.Instance:GetSmallTypeCfgByType(small_type, is_next)
                item_render:SetData(cell_data) --里层按钮赋值信息
				item_render.flush_list_cb = BindTool.Bind(self.FlushAllListSelectState, self, cell_data)
				item_vo[i] = item_render
			end

			self.act_cell_list[index] = item_vo
            if self.first_index > 0 and self.first_index == index then
                self:FirstSelect()
            end
		end)
end

function EverythingUnderTheSunView:FirstSelect()
    self.act_accordion_list[1]:SetToggleValue(true)
end

function EverythingUnderTheSunView:FlushTitleImg()
    local accordion_data = EverythingUnderTheSunWGData.Instance:GetBigTypeCfgByType(self.cur_select_act)
    if IsEmptyTable(accordion_data) or self.old_select_act == self.cur_select_act then
        return
    end

    self.old_select_act = self.cur_select_act
    local bundle, asset = ResPath.GetF2RawImagesPNG(accordion_data.title_img)
    self.node_list.title_img.raw_image:LoadSprite(bundle, asset)
end

function EverythingUnderTheSunView:FlushModelList()
    local is_next_day = EverythingUnderTheSunWGData.Instance:GetIsNextDayActivity(self.cur_select_act)
    local model_data_list = EverythingUnderTheSunWGData.Instance:GetModelDataBySmallType(self.cur_select_small_type, is_next_day)
    if IsEmptyTable(model_data_list) then
        return
    end

    for k, v in pairs(self.model_list) do
        local model_obj = self.node_list["model_pos" .. k]
        if model_data_list[k] then
            local display_data = model_data_list[k].display_data
            model_obj:SetActive(display_data.render_type ~= -1)
            if display_data.render_type == -1 then
                return
            end

            local transform_info = model_data_list[k].transform_info
            if not display_data.model_click_func then
                display_data.model_click_func = function ()
                    if display_data.itemid == nil or type(display_data.itemid) == "string" or display_data.itemid <= 0 then
                        return
                    end

                    TipWGCtrl.Instance:OpenItem({item_id = display_data.itemid})
                end
            end

            v:SetData(display_data)
            RectTransform.SetAnchoredPositionXY(model_obj.rect, transform_info.pos_x, transform_info.pos_y)
            model_obj.rect.rotation = Quaternion.Euler(transform_info.rot_x, transform_info.rot_y, transform_info.rot_z)
            Transform.SetLocalScaleXYZ(model_obj.transform, transform_info.scale, transform_info.scale, transform_info.scale)
        else
            model_obj:SetActive(false)
        end
    end
end

function EverythingUnderTheSunView:FlushBtnState()
    local is_next_day_act = EverythingUnderTheSunWGData.Instance:GetIsNextDayActivity(self.cur_select_act)
    XUI.SetButtonEnabled(self.node_list.goto_btn, not is_next_day_act)

    local btn_str = is_next_day_act and Language.EverythingUnderTheSun.NextDayOpen or Language.EverythingUnderTheSun.Goto
    self.node_list.btn_txt.text.text = btn_str
end

function EverythingUnderTheSunView:SetShowPanelActive(is_show)
    self.node_list.title_img:SetActive(is_show)
    self.node_list.model_list:SetActive(is_show)
end

function EverythingUnderTheSunView:OnClickGoToBtn()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(self.cur_select_act)
    if not is_open then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return
    end

    local accordion_data = EverythingUnderTheSunWGData.Instance:GetBigTypeCfgByType(self.cur_select_act)
    if IsEmptyTable(accordion_data) then
        return
    end

    FunOpen.Instance:OpenViewByName(accordion_data.open_panel)
end

function EverythingUnderTheSunView:OnClickHandler(index)
    if not self.act_accordion_list or not self.act_accordion_list[index]  then
        return
    end

    local act_type = self.act_accordion_list[index]:GetActivityType()
    if self.cur_select_act == act_type  then
        return
    end

    self.cur_select_act = act_type
    self:FlushBtnState()

    if #self.act_cell_list > 0 and self.act_cell_list[index] and self.act_accordion_list[index]:GetAccordionElementIsOn() then
        if not self.node_list.title_img:GetActive() or not self.node_list.model_list:GetActive() then
            self:SetShowPanelActive(true)
        end

        self.act_cell_list[index][1]:OnClickItem(true)
    else
        self:SetShowPanelActive(false)
	end
end

function EverythingUnderTheSunView:OnClickSmallCell(cell_data)
    if not cell_data then
        return
    end

    self.cur_select_small_type = cell_data.small_type
    self:Flush()
end

function EverythingUnderTheSunView:FlushAllListSelectState(select_data)
    for k, v in pairs(self.act_cell_list) do
        for k1, v1 in pairs(v) do
            v1:OnSelectChange(false)
        end
    end

    self:OnClickSmallCell(select_data)
end

------------- EverythingUnderTheSunBigRender外层按钮 -------------
EverythingUnderTheSunBigRender = EverythingUnderTheSunBigRender or BaseClass(BaseRender)

function EverythingUnderTheSunBigRender:OnFlush()
    local is_empty_data = not self.data
    self.view:SetActive(not is_empty_data)
    self.node_list.line:SetActive(not is_empty_data)
    if is_empty_data then
        return
    end

    self.node_list.nor_name.text.text = self.data.tab_name
    self.node_list.hl_name.text.text = self.data.tab_name
end

function EverythingUnderTheSunBigRender:GetActivityType()
    local act_type = self.data and self.data.act_type or 0
    return act_type
end

function EverythingUnderTheSunBigRender:GetSmallTypeList()
    return self.node_list.List
end

function EverythingUnderTheSunBigRender:GetAccordionElementIsOn()
	return self.view.accordion_element.isOn
end

function EverythingUnderTheSunBigRender:SetToggleValueChangeCallBack(call_back)
	self.view.toggle:AddValueChangedListener(call_back)
end

function EverythingUnderTheSunBigRender:SetToggleValue(is_on)
	self.view.toggle.isOn = is_on
end




------------- EverythingUnderTheSunRender内层按钮 -------------
EverythingUnderTheSunRender = EverythingUnderTheSunRender or BaseClass(BaseRender)

function EverythingUnderTheSunRender:LoadCallBack()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function EverythingUnderTheSunRender:ReleaseCallBack()
end

function EverythingUnderTheSunRender:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetItem(self.data.icon_id)
    self.node_list.icon.image:LoadSprite(bundle, asset)
    self.node_list.nor_name.text.text = self.data.show_name
    self.node_list.hl_name.text.text = self.data.show_name
end

function EverythingUnderTheSunRender:OnClickItem(is_on)
	if nil == is_on then return end
	if true == is_on then
        if self.flush_list_cb then
            self.flush_list_cb()
        end

		self:OnSelectChange(true)
	else
		self:OnSelectChange(false)
	end
end

function EverythingUnderTheSunRender:OnSelectChange(is_select)
    self.node_list.nor_img:SetActive(not is_select)
    self.node_list.hl_img:SetActive(is_select)
end