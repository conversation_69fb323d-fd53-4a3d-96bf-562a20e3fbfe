﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class DropdownEx : Dropdown
{
    [Tooltip("下拉列表时需要显示的游戏物体")]
    public GameObject[] activeGameObject;
    [Tooltip("下拉列表时需要隐藏的游戏物体")]
    public GameObject[] inactiveGameObject;

    public System.Action<bool> OnShowStatusChange;

    private bool listShowStatus = false;
    public bool ListShowStatus
    {
        get
        {
            return listShowStatus;
        }

        set
        {
            if (value != listShowStatus)
            {
                if (OnShowStatusChange != null)
                {
                    OnShowStatusChange(value);
                }
                ShowGameObject(value);
                listShowStatus = value;
            }
        }
    }


    
    protected override GameObject CreateDropdownList(GameObject template)
    {
        var result = base.CreateDropdownList(template);
        ListShowStatus = true;
        return result;
    }
    protected override void DestroyDropdownList(GameObject template)
    {
        ListShowStatus = false;
        base.DestroyDropdownList(template);
    }

    public void ShowGameObject(bool value)
    {
        if (activeGameObject != null)
        {
            foreach (var item in activeGameObject)
            {
                item.SetActive(value);
            }
        }
        if (inactiveGameObject != null)
        {
            foreach (var item in inactiveGameObject)
            {
                item.SetActive(!value);
            }
        }
    }

    public void AddListChangeListener(System.Action<bool> func)
    {
        OnShowStatusChange += func;
    }
    public void RemoveListChangeListener(System.Action<bool> func)
    {
        OnShowStatusChange -= func;
    }
}