ProfessRemindView = ProfessRemindView or BaseClass(SafeBaseView)

function ProfessRemindView:__init()
    self.view_name = "ProfessRemindView"
	self.view_layer = UiLayer.PopTop
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_profess_remind_view")
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.HALF)
	self:SetMaskBg(true)
end

function ProfessRemindView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnClickCloseEffect,self))
	self:Flush()
end

function ProfessRemindView:ShowIndexCallBack()

end
function ProfessRemindView:OnFlush()
	if self.objname then
		if self.addely_timer then
			GlobalTimerQuest:CancelQuest(self.addely_timer)
			self.addely_timer = nil
		end

		local bundle, asset = ResPath.GetA2Effect(self.objname)
		EffectManager.Instance:PlaySingleAtTransform(bundle, asset, self.node_list["effect"].transform, self.time)

		if nil == self.addely_timer then
			self.addely_timer = GlobalTimerQuest:AddDelayTimer(function()
				self:Close()

				end, self.time)
		end

		local uid = RoleWGData.Instance:InCrossGetOriginUid()
		--print_error(uid,self.protocol.to_role_id,self.protocol.from_role_id)
		self.node_list.left_panel:SetActive(uid == self.protocol.to_role_id)
		self.node_list.title.text.text = string.format(Language.Activity.PerfertEffectTitle, self.protocol.to_role_name)
		self.node_list.content.text.text = self.protocol.content
		self.node_list.title_down.text.text = self.protocol.from_role_name
		local time_num = TimeWGCtrl.Instance:GetServerTime()
		local time_tab = os.date("*t", time_num)
		self.node_list.time_text.text.text = time_tab.year.."."..time_tab.month.."."..time_tab.day
		local base_cfg = ActivePerfertQingrenWGData.Instance:GetBiaoBaiEndTimeCfg()
		local icon_id 

		if self.protocol.effect_type then
			if self.protocol.effect_type == 0 then
				icon_id = base_cfg.champagne_icon --����
				--AudioManager.PlayAndForget(ResPath.UiseRes("effect_xiangbin"))
			elseif self.protocol.effect_type == 1 then
				icon_id = base_cfg.cruise_icon --��ͧ
				--AudioManager.PlayAndForget(ResPath.UiseRes("effect_youting"))
			else
				icon_id = base_cfg.rocket_icon
				--AudioManager.PlayAndForget(ResPath.UiseRes("effect_huojian"))
			end
		end

		if icon_id and icon_id > 0 then
			local b,a = ResPath.GetItem(icon_id)
			self.node_list.type_imafe.image:LoadSprite(b, a, function ()
			  	self.node_list.type_imafe.image:SetNativeSize()
	   		end)  
		end
		
	end
end

function ProfessRemindView:CloseCallBack()
	if self.node_list and self.node_list.show_effect_tog and self.node_list.show_effect_tog.toggle.isOn then
		ProfessWallWGCtrl.Instance:NotShowEffect()
	end

	self.objname = nil
	self.time = nil
	self.protocol = nil

end

function ProfessRemindView:OnClickCloseEffect()
	-- if self.addely_timer then
	-- 	GlobalTimerQuest:CancelQuest(self.addely_timer)
	-- 	self.addely_timer = nil
	-- end
	if self.node_list.show_effect_tog.toggle.isOn then
		ProfessWallWGCtrl.Instance:NotShowEffect()
	end
	--print_error(self.time,"关闭000")
	self:Close()
end
function ProfessRemindView:ReleaseCallBack()
	if self.addely_timer then
		--print_error(self.addely_timer)
		GlobalTimerQuest:CancelQuest(self.addely_timer)
		self.addely_timer = nil
	end
end

function ProfessRemindView:SetEffect(objname, time,protocol)
	self.objname = objname
	self.time = time
	self.protocol = protocol
	--print_error(self.time,self:IsOpen())
	-- if ViewManager.Instance:IsOpen(GuideModuleName.ProfessRemindView) then
	-- 	self:Flush()
	-- else
		ViewManager.Instance:Open(GuideModuleName.ProfessRemindView)
		--self:Open()
		self:Flush()
	--end
end