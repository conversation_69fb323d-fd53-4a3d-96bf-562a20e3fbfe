TianShenRoadCBRankView = TianShenRoadCBRankView or BaseClass(SafeBaseView)

function TianShenRoadCBRankView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "TianShenRoadUi"
	self:AddViewResource(0, "uis/view/merge_activity_ui/sp_rank_prefab", "layout_second_panel", {sizeDelta = Vector2(786, 540)})
	self:AddViewResource(0, "uis/view/tianshenroad_ui_prefab", "layout_tsrank_reward")
end

function TianShenRoadCBRankView:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function TianShenRoadCBRankView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.TianShenRoad.TianShenZhanLi
	local end_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_chongbang)
	self.node_list["rank_time"].text.text = string.format(Language.TianShenRoad.RankStr3, TimeUtil.FormatMDHMS(end_time))
	self.reward_list_view = AsyncListView.New(TianShenRoadRankItemRender, self.node_list["ph_item_list"])

	self:FlushView()
end

function TianShenRoadCBRankView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end
end

function TianShenRoadCBRankView:FlushView()
	local data_list = TianshenRoadWGData.Instance:GetTSRankInfo()
	if data_list ~= nil then
		self.reward_list_view:SetDataList(data_list)
	end

	local rank, cap = TianshenRoadWGData.Instance:GetMyRankAndCap()

	if rank == 0 then
		self.node_list.my_rank.text.text = Language.TianShenRoad.RankStr2
	else
		self.node_list.my_rank.text.text = string.format(Language.TianShenRoad.RankStr4, rank)
	end
	
	self.node_list.tscao_value.text.text = string.format(Language.TianShenRoad.TianShenRankDesc, cap)
end

----------------------------------------------------------------------------------------------------

TianShenRoadRankItemRender = TianShenRoadRankItemRender or BaseClass(BaseRender)

function TianShenRoadRankItemRender:OnFlush()
	if not self.data then
		return
	end
	if self.data.no_true_rank then  --未上榜
		local rank_cfg = TianshenRoadWGData.Instance:GetCBRankRewardCfg()
		for _,v in ipairs(rank_cfg) do
			if self.data.rank_data.rank_id <= v.rank then
				self.node_list.need_cap_value.text.text = v.min_zhanli
				self.node_list.need_cap_value2.text.text = v.min_zhanli
				break
			end
		end

		self.node_list.time.text.text = ""
		self.node_list.time2.text.text = ""
		self.node_list.player_name.text.text = ""
	else
		
		self.node_list.need_cap_value.text.text = self.data.rank_data.zhanli
		self.node_list.need_cap_value2.text.text = self.data.rank_data.zhanli
		local date = TimeUtil.FormatUnixTime2Date(self.data.rank_data.reach_time)
		self.node_list.time.text.text = string.format("%02d-%02d  %02d:%02d", date.month, date.day, date.hour, date.minute)
		self.node_list.time2.text.text = string.format("%02d-%02d  %02d:%02d", date.month, date.day, date.hour, date.minute)
	end

	local user_name = self.data.rank_data.player_name
	if self.data.no_true_rank then
		user_name = Language.OpenServer.XuWeiYiDai
	end

	local is_top_3 = self.data.rank_data.rank_id <= 3
	self.node_list.player_name.text.text = user_name
	self.node_list.player_name2.text.text = user_name

	self.node_list.player_name.text.enabled = not is_top_3
	self.node_list.player_name2.text.enabled = is_top_3

	self.node_list.need_cap_value.text.enabled = not is_top_3
	self.node_list.need_cap_value2.text.enabled = is_top_3

	self.node_list.time.text.enabled = not is_top_3
	self.node_list.time2.text.enabled = is_top_3

	self.node_list.img_rank.image.enabled = is_top_3

	self.node_list.img_bg:SetActive(is_top_3)
	if not is_top_3 then
		self.node_list.rank.text.text = self.data.rank_data.rank_id
	else
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank_data.rank_id))
		self.node_list.img_bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_" .. self.data.rank_data.rank_id))
		self.node_list.rank.text.text = ""
	end
end