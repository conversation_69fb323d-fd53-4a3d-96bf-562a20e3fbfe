-- X-新节日活动-新春盛典.xls
local item_table={
[1]={item_id=38456,num=1,is_bind=1},
[2]={item_id=37426,num=1,is_bind=1},
[3]={item_id=37672,num=1,is_bind=1},
[4]={item_id=37856,num=1,is_bind=1},
[5]={item_id=38787,num=1,is_bind=1},
[6]={item_id=38159,num=1,is_bind=1},
[7]={item_id=26569,num=1,is_bind=1},
[8]={item_id=44185,num=1,is_bind=1},
[9]={item_id=44184,num=1,is_bind=1},
[10]={item_id=44183,num=1,is_bind=1},
[11]={item_id=44182,num=1,is_bind=1},
[12]={item_id=44180,num=1,is_bind=1},
[13]={item_id=56317,num=1,is_bind=1},
[14]={item_id=56316,num=1,is_bind=1},
[15]={item_id=28448,num=1,is_bind=1},
[16]={item_id=28447,num=1,is_bind=1},
[17]={item_id=28446,num=1,is_bind=1},
[18]={item_id=48559,num=1,is_bind=1},
[19]={item_id=48560,num=1,is_bind=1},
[20]={item_id=48561,num=1,is_bind=1},
[21]={item_id=28665,num=1,is_bind=1},
[22]={item_id=28666,num=1,is_bind=1},
[23]={item_id=54806,num=1,is_bind=1},
[24]={item_id=54807,num=1,is_bind=1},
[25]={item_id=22076,num=8000,is_bind=1},
[26]={item_id=22076,num=10000,is_bind=1},
[27]={item_id=22076,num=15000,is_bind=1},
[28]={item_id=22076,num=18000,is_bind=1},
[29]={item_id=22076,num=20000,is_bind=1},
[30]={item_id=22076,num=25000,is_bind=1},
[31]={item_id=22076,num=30000,is_bind=1},
[32]={item_id=22076,num=35000,is_bind=1},
[33]={item_id=22076,num=40000,is_bind=1},
[34]={item_id=22076,num=50000,is_bind=1},
[35]={item_id=22076,num=80000,is_bind=1},
[36]={item_id=22076,num=100000,is_bind=1},
[37]={item_id=37223,num=1,is_bind=1},
[38]={item_id=54805,num=1,is_bind=1},
[39]={item_id=22076,num=5000,is_bind=1},
}

return {
open_day={
{}
},

open_day_meta_table_map={
},
reward_pool={
{weight=0,need_display=1,},
{seq=1,reward_item={[0]=item_table[1]},},
{seq=2,reward_item={[0]=item_table[2]},},
{seq=3,reward_item={[0]=item_table[3]},need_display=1,},
{seq=4,reward_item={[0]=item_table[4]},},
{seq=5,reward_item={[0]=item_table[5]},},
{seq=6,reward_item={[0]=item_table[6]},weight=1,need_display=1,},
{seq=7,reward_item={[0]=item_table[7]},weight=10,need_display=1,},
{seq=8,reward_item={[0]=item_table[8]},weight=150,},
{seq=9,reward_item={[0]=item_table[9]},weight=300,},
{seq=10,reward_item={[0]=item_table[10]},weight=800,},
{seq=11,reward_item={[0]=item_table[11]},weight=1200,},
{seq=12,reward_item={[0]=item_table[12]},},
{seq=13,reward_item={[0]=item_table[13]},},
{seq=14,reward_item={[0]=item_table[14]},},
{seq=15,reward_item={[0]=item_table[15]},},
{seq=16,reward_item={[0]=item_table[16]},},
{seq=17,reward_item={[0]=item_table[17]},},
{seq=18,reward_item={[0]=item_table[18]},},
{seq=19,reward_item={[0]=item_table[19]},},
{seq=20,reward_item={[0]=item_table[20]},weight=100,},
{seq=21,reward_item={[0]=item_table[21]},weight=1100,},
{seq=22,reward_item={[0]=item_table[22]},weight=600,},
{draw_button=2,},
{seq=1,reward_item={[0]=item_table[1]},},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=2,},
{draw_button=3,weight=0,},
{draw_button=3,},
{draw_button=3,seq=2,reward_item={[0]=item_table[2]},},
{seq=3,reward_item={[0]=item_table[3]},},
{seq=4,reward_item={[0]=item_table[4]},},
{seq=5,reward_item={[0]=item_table[5]},},
{draw_button=3,seq=6,reward_item={[0]=item_table[6]},weight=1,},
{draw_button=3,seq=7,reward_item={[0]=item_table[7]},weight=10,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,},
{draw_button=3,}
},

reward_pool_meta_table_map={
[24]=47,	-- depth:1
[50]=49,	-- depth:1
[51]=49,	-- depth:1
[52]=49,	-- depth:1
[29]=52,	-- depth:2
[28]=51,	-- depth:2
[27]=50,	-- depth:2
[3]=4,	-- depth:1
[25]=27,	-- depth:3
[2]=4,	-- depth:1
[20]=21,	-- depth:1
[26]=49,	-- depth:1
[18]=22,	-- depth:1
[5]=4,	-- depth:1
[6]=4,	-- depth:1
[19]=21,	-- depth:1
[48]=25,	-- depth:4
[13]=11,	-- depth:1
[14]=23,	-- depth:1
[17]=22,	-- depth:1
[16]=21,	-- depth:1
[15]=22,	-- depth:1
[67]=21,	-- depth:1
[66]=20,	-- depth:2
[55]=9,	-- depth:1
[57]=11,	-- depth:1
[58]=12,	-- depth:1
[59]=13,	-- depth:2
[60]=14,	-- depth:2
[62]=16,	-- depth:2
[63]=17,	-- depth:2
[64]=18,	-- depth:2
[65]=19,	-- depth:2
[56]=10,	-- depth:1
[61]=15,	-- depth:2
[35]=58,	-- depth:2
[45]=22,	-- depth:1
[30]=53,	-- depth:1
[31]=54,	-- depth:1
[32]=55,	-- depth:2
[33]=56,	-- depth:2
[34]=57,	-- depth:2
[68]=45,	-- depth:2
[36]=59,	-- depth:3
[37]=60,	-- depth:3
[38]=61,	-- depth:3
[39]=62,	-- depth:3
[40]=63,	-- depth:3
[41]=64,	-- depth:3
[42]=65,	-- depth:3
[43]=66,	-- depth:3
[44]=67,	-- depth:2
[46]=23,	-- depth:1
[69]=46,	-- depth:2
},
draw_type={
{},
{draw_button=2,draw_item=item_table[23],consume_lingyu_num=5000,add_points=10,draw_num=10,},
{draw_button=3,draw_item=item_table[24],consume_lingyu_num=25000,add_points=50,draw_num=50,}
},

draw_type_meta_table_map={
},
baodi_reward={
{}
},

baodi_reward_meta_table_map={
},
leiji_points_reward={
{},
{seq=1,need_points=1000,reward_item={[0]=item_table[25]},},
{seq=2,need_points=2000,reward_item={[0]=item_table[26]},},
{seq=3,need_points=5000,reward_item={[0]=item_table[27]},},
{seq=4,need_points=8000,reward_item={[0]=item_table[28]},},
{seq=5,need_points=10000,reward_item={[0]=item_table[29]},},
{seq=6,need_points=20000,reward_item={[0]=item_table[30]},},
{seq=7,need_points=30000,reward_item={[0]=item_table[31]},},
{seq=8,need_points=50000,reward_item={[0]=item_table[32]},},
{seq=9,need_points=80000,reward_item={[0]=item_table[33]},},
{seq=10,need_points=100000,reward_item={[0]=item_table[34]},},
{seq=11,need_points=120000,reward_item={[0]=item_table[35]},},
{seq=12,need_points=150000,reward_item={[0]=item_table[36]},}
},

leiji_points_reward_meta_table_map={
},
model_display={
{}
},

model_display_meta_table_map={
},
item_random_desc={
{random_count=0.001,},
{number=2,item_name="钢铁加鲁鲁",item_id=38456,},
{number=3,item_name="钢铁圣龙芯",item_id=37426,},
{number=4,item_name="钢铁玫瑰武",item_id=37672,},
{number=5,item_name="钢铁玫瑰衣",item_id=37856,},
{number=6,item_name="美人鱼·娜美",item_id=38787,},
{number=7,item_name="阿根廷宝贝",item_id=38159,},
{number=8,item_name="君子贵人丹",item_id=26569,random_count=0.02,},
{number=9,item_name="4级丹药礼包",item_id=44185,random_count=0.05,},
{number=10,item_name="3级丹药礼包",item_id=44184,random_count=0.06,},
{number=11,item_name="2级丹药礼包",item_id=44183,random_count=0.07,},
{number=12,item_name="1级丹药礼包",item_id=44182,random_count=0.1,},
{number=13,item_name="属性丹随机礼包",item_id=44180,random_count=0.07,},
{number=14,item_name="龙力金丹",item_id=56317,random_count=0.06,},
{number=15,item_name="龙力丹",item_id=56316,random_count=0.1,},
{number=16,item_name="天罚神丹",item_id=28448,random_count=0.04,},
{number=17,item_name="天罚仙丹",item_id=28447,random_count=0.1,},
{number=18,item_name="天罚灵丹",item_id=28446,random_count=0.1,},
{number=19,item_name="外观属性丹自选包",item_id=48559,random_count=0.03,},
{number=20,item_name="武魂属性丹自选包",item_id=48560,random_count=0.04,},
{number=21,item_name="养龙属性丹自选包",item_id=48561,random_count=0.04,},
{number=22,item_name="武魂石·凡",item_id=28665,random_count=0.1,},
{number=23,item_name="武魂石·卓",item_id=28666,random_count=0.04,}
},

item_random_desc_meta_table_map={
},
open_day_default_table={start_day=1,end_day=9999,grade=1,},

reward_pool_default_table={grade=1,draw_button=1,seq=0,reward_item={[0]=item_table[37]},weight=2,need_display=0,},

draw_type_default_table={draw_button=1,draw_item=item_table[38],consume_lingyu_num=500,add_points=1,draw_num=1,},

baodi_reward_default_table={grade=1,need_points=5000,reward_item={[0]=item_table[37]},},

leiji_points_reward_default_table={grade=1,seq=0,need_points=500,reward_item={[0]=item_table[39]},},

model_display_default_table={grade=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=38456,model_name="泼墨山河鲲",display_pos="150|0",display_scale=1,display_rotation="0|0|0",},

item_random_desc_default_table={grade=1,number=1,item_name="时之翼",item_id=37223,random_count=0.01,}

}

