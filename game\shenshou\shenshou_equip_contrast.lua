------------------------------------------------------------
--物品对比tips
------------------------------------------------------------
ShenShouContrastTip = ShenShouContrastTip or BaseClass(SafeBaseView)

function ShenShouContrastTip:__init()
	self.view_layer = UiLayer.Pop
	self.view_name = "ShenshouUi"
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_itemtip")

	self.equip_data_1 = nil
	self.equip_data_2 = nil
end

function ShenShouContrastTip:__delete()

end

function ShenShouContrastTip:ReleaseCallBack()
	if self.shenshou_equip_tip_1 then
        self.shenshou_equip_tip_1:Delete<PERSON>e()
     	self.shenshou_equip_tip_1 = nil
   	end

   	if self.shenshou_equip_tip_2 then
        self.shenshou_equip_tip_2:DeleteMe()
     	self.shenshou_equip_tip_2 = nil
   	end
	self.equip_data_list = nil
end

function ShenShouContrastTip:LoadCallBack()
	self.shenshou_equip_tip_1 = ShenShouEquipTip.New(self.node_list["base_tip_root_1"])
	self.shenshou_equip_tip_2 = ShenShouEquipTip.New(self.node_list["base_tip_root_2"])
	self.shenshou_equip_tip_1:SetIgnoreBtn(true)
	self.shenshou_equip_tip_1:SetIndex(1)
	self.shenshou_equip_tip_2:SetIndex(2)
end

function ShenShouContrastTip:OnFlush()
	if self.equip_data_list[1] and self.shenshou_equip_tip_1 then
		self.node_list["base_tip_root_1"]:SetActive(true)
		local data_list = self.equip_data_list[1]
		self.shenshou_equip_tip_1:SetData(data_list.data, data_list.fromView, data_list.param_t)
		self.shenshou_equip_tip_1:SetTopLeftIcon()
	else
		self.node_list["base_tip_root_1"]:SetActive(false)
	end
	if self.equip_data_list[2] and self.shenshou_equip_tip_2 then
		self.node_list["base_tip_root_2"]:SetActive(true)
		local data_list = self.equip_data_list[2]
		self.shenshou_equip_tip_2:SetData(data_list.data, data_list.fromView, data_list.param_t)
	else
		self.node_list["base_tip_root_2"]:SetActive(false)
	end
end

function ShenShouContrastTip:SetData(data_list)
	self.equip_data_list = data_list
	self:Open()
end