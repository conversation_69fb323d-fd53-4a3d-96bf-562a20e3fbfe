TianShenLiLianView = TianShenLiLianView or BaseClass(SafeBaseView)

local BuildingAnchoredPos = {
    [0] = Vector3(-30, -70, 0),
    [1] = Vector3(0, 18, 0),
}

local StageAnchoredPos = {
    [0] = Vector3(44, 48, 0),
    [1] = Vector3(55, 116, 0),
}

local RT_MODEL_SCALE = 0.6
local RT_MODEL_POS = Vector3(0, -1, 0)
local RT_MODEL_ROT = Vector3(0, -60, 0)

local ROLE_MODEL_POS_1 = {Vector2(0, 0),Vector2(0, 0),Vector2(0, 0)}
local ROLE_MODEL_POS_2 = {Vector2(140, 0),Vector2(-140, 0),Vector2(0, 0)}
local ROLE_MODEL_POS_3 = {Vector2(240, 0),Vector2(50, 0),Vector2(-280, 0)}

function TianShenLiLianView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg(false)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_tianshen_lilian")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_light_common_top_panel")
end

function TianShenLiLianView:OpenCallBack()
 
end

function TianShenLiLianView:CloseCallBack()
    self.need_flush_role_model = false
end


function TianShenLiLianView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesJPG("a3_ll_bj")
	self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.RawImage_tongyong.raw_image:SetNativeSize()
	end)

    self.node_list.title_view_name.text.text = Language.TianShen.TitleText

    -- 出战天神
    if not self.battle_slot then
        self.battle_slot = LiLianChuZhanRender.New(self.node_list.battle_slot)
    end

    -- 角色模型＋天神模型
    if not self.cur_role_model then
        self.cur_role_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["role_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.cur_role_model:SetRenderTexUI3DModel(display_data)
        self.cur_role_model:SetRTAdjustmentRootLocalScale(RT_MODEL_SCALE)
        self.cur_role_model:SetRTAdjustmentRootLocalRotation(RT_MODEL_ROT.x, RT_MODEL_ROT.y, RT_MODEL_ROT.z)
        self.cur_role_model:SetRTAdjustmentRootLocalPosition(RT_MODEL_POS.x, RT_MODEL_POS.y, RT_MODEL_POS.z)
        -- self.cur_role_model:SetUI3DModel(self.node_list.role_display.transform, nil, nil, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.cur_role_model)
    end

    if not self.cur_tianshen_model then
        self.cur_tianshen_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["tianshen_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.cur_tianshen_model:SetRenderTexUI3DModel(display_data)
        self.cur_tianshen_model:SetRTAdjustmentRootLocalScale(RT_MODEL_SCALE)
        self.cur_tianshen_model:SetRTAdjustmentRootLocalRotation(RT_MODEL_ROT.x, RT_MODEL_ROT.y, RT_MODEL_ROT.z)
        self.cur_tianshen_model:SetRTAdjustmentRootLocalPosition(RT_MODEL_POS.x, RT_MODEL_POS.y, RT_MODEL_POS.z)
        -- self.cur_tianshen_model:SetUI3DModel(self.node_list.tianshen_display.transform, nil, nil, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.cur_tianshen_model)
    end

    if not self.cur_ground_model then
        self.cur_ground_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ground_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            rt_scale_type = ModelRTSCaleType.PS_XS,
            can_drag = false,
        }
        
        self.cur_ground_model:SetRenderTexUI3DModel(display_data)
        self.cur_ground_model:SetRTAdjustmentRootLocalScale(5)
        self.cur_ground_model:SetRTAdjustmentRootLocalPosition(-2.8, -57, 0)
        -- self.cur_tianshen_model:SetUI3DModel(self.node_list.tianshen_display.transform, nil, nil, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.cur_ground_model)
    end

    if not self.cur_ground_front_model then
        self.cur_ground_front_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ground_front_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            rt_scale_type = ModelRTSCaleType.PS_XS,
            can_drag = false,
        }
        
        self.cur_ground_front_model:SetRenderTexUI3DModel(display_data)
        self.cur_ground_front_model:SetRTAdjustmentRootLocalScale(5)
        self.cur_ground_front_model:SetRTAdjustmentRootLocalPosition(-2.8, -57, 0)
        -- self.cur_tianshen_model:SetUI3DModel(self.node_list.tianshen_display.transform, nil, nil, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.cur_ground_front_model)
    end
    
    if not self.level_render then
        self.level_render = LilianDetailedRender.New(self.node_list.level_render)
    end

    if not self.fb_render then
        self.fb_render = LilianDetailedRender.New(self.node_list.fb_render)
    end

    if not self.xiuwei_render then
        self.xiuwei_render = LilianDetailedRender.New(self.node_list.xiuwei_render)
    end

    if not self.tianshen_render then
        self.tianshen_render = LilianDetailedRender.New(self.node_list.tianshen_render)
    end

    if not self.total_render then
        self.total_render = LilianDetailedRender.New(self.node_list.total_render)
    end

    -- 副本挂机奖励
	if not self.fb_reward_show then
		self.fb_reward_show = AsyncListView.New(ItemCell, self.node_list.fb_reward_show)
	end

    -- 副本
    if not self.mid_fb_list then
        self.mid_fb_list = {}

        for i = 1, 5 do
            local fb_item_obj = self.node_list.mid_fb_list:FindObj(string.format("fb_item_%d", i))
            if fb_item_obj then
                local cell = LiLianFbRender.New(fb_item_obj)
                cell:SetIndex(i)
                self.mid_fb_list[i] = cell
            end
        end
    end

    self.need_flush_role_model = true
    XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClickSitBox, self))
    XUI.AddClickEventListener(self.node_list.challenge_fb_btn, BindTool.Bind(self.OnClickChallengeFB, self))
    XUI.AddClickEventListener(self.node_list.goto_assign_btn, BindTool.Bind(self.OnGoAssignClick, self))
    XUI.AddClickEventListener(self.node_list.add_exp_per_btn, BindTool.Bind(self.OnClickExpPerBtn, self))
    
    local tween_time = 0.8
    local node = self.node_list.can_get_reward
    if node then
        self:KillArrowTween()
        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, 78)
        self.arrow_tweener = node.rect:DOAnchorPosY(86, tween_time)
        self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
    end

    -- 监听系统事件
	self.main_ui_exp_view = MainUIViewExp.New(self.node_list.exp_info)
	self.main_ui_exp_view:SetEffectNode(self.node_list["ExpInfo_effect"])
    self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TianShenLiLianView, self.get_guide_ui_event)
end

function TianShenLiLianView:ReleaseCallBack()

    self.model_res_id = nil
    
    if self.battle_slot then
        self.battle_slot:DeleteMe()
        self.battle_slot = nil
    end

    if self.cur_role_model then
        self.cur_role_model:DeleteMe()
        self.cur_role_model = nil
    end
    
  
    if self.cur_tianshen_model then
        self.cur_tianshen_model:DeleteMe()
        self.cur_tianshen_model = nil
    end

    if self.cur_ground_model then
        self.cur_ground_model:DeleteMe()
        self.cur_ground_model = nil
    end

    if self.cur_ground_front_model then
        self.cur_ground_front_model:DeleteMe()
        self.cur_ground_front_model = nil
    end

    if self.level_render then
        self.level_render:DeleteMe()
        self.level_render = nil
    end

    if self.fb_render then
        self.fb_render:DeleteMe()
        self.fb_render = nil
    end

    if self.xiuwei_render then
        self.xiuwei_render:DeleteMe()
        self.xiuwei_render = nil
    end

    if self.tianshen_render then
        self.tianshen_render:DeleteMe()
        self.tianshen_render = nil
    end

    if self.total_render then
        self.total_render:DeleteMe()
        self.total_render = nil
    end

    if self.fb_reward_show then
        self.fb_reward_show:DeleteMe()
        self.fb_reward_show = nil
    end

    if self.mid_fb_list and #self.mid_fb_list > 0 then
		for _, render_cell in ipairs(self.mid_fb_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.mid_fb_list = nil
	end

    self.need_flush_role_model = nil
    self.old_model_res = nil
    self.is_init_ground_model = nil

    if self.main_ui_exp_view then
		self.main_ui_exp_view:DeleteMe()
		self.main_ui_exp_view = nil
	end

    self:CleanOfflineTimer()
    self:KillArrowTween()

    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TianShenLiLianView, self.get_guide_ui_event)
    self.get_guide_ui_event = nil
end


-- function TianShenLiLianView:ShowIndexCallBack()
-- 	self.need_flush_role_model = true
-- end

function TianShenLiLianView:KillArrowTween()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end
end

function TianShenLiLianView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == "goto_assign_btn" then
        return self.node_list[ui_name], BindTool.Bind(self.OnGoAssignClick, self)
    end
end

function TianShenLiLianView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if k == "all" then
            self:FlushChuZhanPart()
            self:FlushExpAndBoxPart()
            self:FlushExpFbPart()
            self:FlushLilianNumAndRemind()
        elseif k == "flush_exp" then
            self:FlushExpAndBoxPart()
        elseif k == "flush_exp_fb" then
            self:FlushExpFbPart()
            self:FlushLilianNum()
        elseif k == "flush_chuzhan" then
            self:FlushChuZhanPart()
        elseif k == "flush_lilian_num" then
            self:FlushLilianNum()
        elseif k == "flush_rent" then
            self:FlushLilianNumAndRemind()
        end
    end
end

function TianShenLiLianView:FlushChuZhanPart()
    local chuzhan_list = TianShenWGData.Instance:GetFightTisnShen(true)
    local tianshen_data = chuzhan_list[0]
    -- local is_boss_func_open = FunOpen.Instance:GetFunIsOpened(FunName.TianShenView)		
    -- self.node_list.tianshen_lilian_left:CustomSetActive(is_boss_func_open)

    self.battle_slot:SetData(tianshen_data)
    self:FlushModel(tianshen_data)
end

function TianShenLiLianView:FlushModel(tianshen_data)
    if self.is_init_ground_model == nil then
        self.is_init_ground_model = true

        -- 1号其他模型
        local bundle, asset = ResPath.GetNormalOtherModel(1, "1_1")
        self.cur_ground_model:SetMainAsset(bundle, asset)

        bundle, asset = ResPath.GetNormalOtherModel(1, "1_2")
        self.cur_ground_front_model:SetMainAsset(bundle, asset)
    end

    local need_show_data = {}
    -- 模型展示
    local obj_animator = SceneObjAnimator.Move
    if self.need_flush_role_model then

        
        -- 主上阵幻兽模型
        local main_list_data = ControlBeastsWGData.Instance:GetHoleMainData()
        for i, v in ipairs(main_list_data) do
            if v.beasts_bag_id~=-1 then
                table.insert(need_show_data,v)
                break
                -- 不要上阵三个了
                -- if #need_show_data < 3 then
                    -- table.insert(need_show_data,v)
                -- end
            end
        end
        -- 副上阵幻兽模型
        if #need_show_data == 0 then
            local assist_list_data = ControlBeastsWGData.Instance:GetHoleAssistData()
            for i, v in ipairs(assist_list_data) do
                if v.beasts_bag_id~=-1 then
                    table.insert(need_show_data,v)
                    break
                end
            end
        end
        -- 幻兽模型
        if #need_show_data == 0 then
            local beasts_list_data = ControlBeastsWGData.Instance:GetBeastsList()
            for i, v in ipairs(beasts_list_data) do
                if v.beasts_bag_id~=-1 then
                    table.insert(need_show_data,v)
                    break
                end
            end
        end
        if #need_show_data > 0 then
            local pos_list = nil

            if #need_show_data == 1 then
                pos_list = ROLE_MODEL_POS_1
            elseif #need_show_data == 2 then
                pos_list = ROLE_MODEL_POS_2
            elseif #need_show_data == 3 then
                pos_list = ROLE_MODEL_POS_3
            end
            for k, v in pairs(need_show_data) do
                local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(v.beasts_bag_id)
                if not beast_data then
                    beast_data = v
                end
                if beast_data and beast_data.server_data then
                    -- 展示模型
                    local server_data = beast_data.server_data
                    local beast_id = server_data.beast_id
                    local res_id = 0
                    if server_data then
                        res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beast_id, server_data.use_skin)
                    else    
                        res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beast_id)
                    end

                    local cur_role_modle = nil
                    if k == 1 then
                        cur_role_modle = self.cur_role_model
                        cur_role_modle.u3d_obj.rect.anchoredPosition = pos_list[1]
                    end
                    if self.model_res_id ~= res_id and cur_role_modle ~= nil then
                        self.model_res_id = res_id
                        local bundle, asset = ResPath.GetBeastsModel(res_id)
                        
                        cur_role_modle:SetMainAsset(bundle, asset)
                        cur_role_modle:SetUSAdjustmentNodeLocalScale(0.7)
                        cur_role_modle:PlayRoleAction(obj_animator)
                        cur_role_modle:SetInteger("status", 1)
                    end
                end
            end

        else
            -- 人物模型
            self.cur_role_model:SetModelScale(Vector3(0.55, 0.55, 0.55))
            local role_vo = GameVoManager.Instance:GetMainRoleVo()
            self.cur_role_model:SetModelResInfo(role_vo, {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_jianzhen = true})
            self.cur_role_model:PlayRoleAction(obj_animator)
            self.cur_role_model:SetRotation(u3dpool.vec3(0, 90, 0))
        end
        self.need_flush_role_model = false
    else
        self.cur_role_model:PlayRoleAction(obj_animator)
        self.cur_role_model:SetInteger("status", 1)
    end

    if tianshen_data then
        obj_animator = SceneObjAnimator.Move
        if self.old_model_res ~= tianshen_data.appe_image_id then
            -- 添加化魔展示
            self.cur_tianshen_model:SetModelScale(Vector3(0.62, 0.62, 0.62))
            local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(tianshen_data.index) or tianshen_data.appe_image_id
            self.cur_tianshen_model:SetTianShenModel(appe_image_id, tianshen_data.index, true, nil, obj_animator)
            self.cur_tianshen_model:SetRotation(u3dpool.vec3(0, 90, 0))
            self.old_model_res = tianshen_data.appe_image_id
        else
            self.cur_tianshen_model:PlayLastAction()
        end
    else
        self.cur_tianshen_model:RemoveAllModel()
    end
end

function TianShenLiLianView:FlushExpAndBoxPart()
    local assemble_exp_per = function(now_text, now_text_per, sp_img)
        local info = {}
        info.now_text = now_text
        info.now_text_per = now_text_per
        info.sp_img = sp_img

        return info
    end

    local total_xiaolv = 0
    local off_data = OfflineRestWGData.Instance
    -- 经验效率
    local role_level = RoleWGData.Instance:GetAttr("level")
    ---local exp_xiaolv = off_data:GetLiLianExpXiaoLv()
    local exp_xiaolv = ExpAdditionWGData.Instance:GetOffLineStdExp()

    total_xiaolv = total_xiaolv + exp_xiaolv
    local conver_str = CommonDataManager.NotConverExpExtend(exp_xiaolv)
    local now_text_per = string.format(Language.Skill.NumThree, conver_str)
    local is_vis, level = RoleWGData.Instance:GetDianFengLevel(role_level)
    local now_text = string.format(Language.Common.LevelNormal, level)
    local level_render_data = assemble_exp_per(now_text, now_text_per, is_vis)

    -- 副本进度
	local fb_level = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
    local wave = ExperienceFbWGData.Instance:GetCurrExpWestWave()
	local pass_wave = wave - 1

	if pass_wave <= 0 and fb_level > 1 then
		local list = ExperienceFbWGData.Instance:GetWaveListByLevel(fb_level)
		pass_wave = #list
		fb_level = fb_level - 1
	end
	
    local fb_level_cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(fb_level)
    local wave_cfg = ExperienceFbWGData.Instance:GetLevelWaveCfgByLevelWave(fb_level, pass_wave)

    exp_xiaolv = wave_cfg and wave_cfg.std_exp or 0
    total_xiaolv = total_xiaolv + exp_xiaolv
    conver_str = CommonDataManager.NotConverExpExtend(exp_xiaolv)
    now_text_per = string.format(Language.Skill.NumThree, conver_str)
    now_text = string.format(Language.OfflineRest.ExperienceFbTitle, fb_level_cfg.stage_name, wave)
    local fb_render_data = assemble_exp_per(now_text, now_text_per)

    -- 修为经验
	local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
    exp_xiaolv = cur_stage_cfg and cur_stage_cfg.reset_add_exp or 0
    total_xiaolv = total_xiaolv + exp_xiaolv
    conver_str = CommonDataManager.NotConverExpExtend(exp_xiaolv)
    now_text_per = string.format(Language.Skill.NumThree, conver_str)
    now_text = cur_stage_cfg and cur_stage_cfg.stage_title or ""
    local xiuwei_render_data = assemble_exp_per(now_text, now_text_per)

    -- 天神加成
    local chuzhan_list = TianShenWGData.Instance:GetFightTisnShen(true)
    local tianshen_data = chuzhan_list[0]
    local cur_vip_lv = VipWGData.Instance:GetRoleVipLevel()
    local data = RechargeWGData.Instance:GetKingVipAddition(cur_vip_lv)
    local add_xs = 0
    local exp_add = math.ceil((data and data.add_11 or 0) / 100)

    if tianshen_data then
        add_xs = add_xs + tianshen_data.offline_drop_coe
    end

    -- 经验加成
    local exp_add_per = add_xs * 100 + exp_add
    now_text_per = string.format("x%d%%", exp_add_per)
    now_text = tianshen_data and tianshen_data.bianshen_name or ""
    local tianshen_render_data = assemble_exp_per(now_text, now_text_per)
    self.node_list.tianshen_render:CustomSetActive(exp_add_per > 0)

    -- 总效率
    local cur_add_exp_per = off_data.total_cur_add_exp_per
	total_xiaolv = math.floor(total_xiaolv * (1 + (cur_add_exp_per * 0.0001)))
	conver_str = CommonDataManager.NotConverExpExtend(total_xiaolv)
    now_text_per = string.format(Language.Skill.NumThree, conver_str)
    local total_render_data = assemble_exp_per("", now_text_per)

    self.level_render:SetData(level_render_data)
    self.fb_render:SetData(fb_render_data)
    self.xiuwei_render:SetData(xiuwei_render_data)
    self.tianshen_render:SetData(tianshen_render_data)
    self.total_render:SetData(total_render_data)

    -- 设置当前的挂机奖励
    local list, equip_order = OfflineRestWGData.Instance:GLiLianShowReward(fb_level)
    self.fb_reward_show:SetDataList(list)
    -- local str = string.format(Language.OfflineRest.ExperienceEquipTips, fb_level_cfg.stage_name, NumberToChinaNumber(equip_order))
    self.node_list.fb_tips_text.text.text = Language.OfflineRest.ExperienceEquipTips

    -- 设置挂机时间
    local other_cfg = OfflineRestWGData.Instance:GetOtherCfg()
    local hang_max_offlie_time = other_cfg.hang_max_offlie_time or 0
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local pass_offline_time = off_data:GetPassOfflineRestTime()
    local last_calc_online_reward_time = off_data.last_calc_online_reward_time
    local leiji_offline_time = server_time > last_calc_online_reward_time and (server_time - last_calc_online_reward_time) or 0
    self:CleanOfflineTimer()

    self:FlushOfflineTimeStr(leiji_offline_time)

    local last_offline_time = hang_max_offlie_time - leiji_offline_time
    if last_offline_time > 0 then
        CountDownManager.Instance:AddCountDown("lilian_offline",
            BindTool.Bind(self.FlushOfflineTime, self),
            function()
                self:FlushOfflineTimeStr(hang_max_offlie_time)
            end, nil, last_offline_time, 1)
    end

    -- 刷新宝箱状态
    local progress_value = leiji_offline_time / hang_max_offlie_time
    local has_red = progress_value >= 0.1
    local box_str = "a3_ll_bx_1"
    if progress_value > 0.35 then
        box_str = "a3_ll_bx_2"
    elseif progress_value > 0.65 then
        box_str = "a3_ll_bx_3"
    end

    self.node_list.get_reward_box.image:LoadSprite(ResPath.GetOfflinerestImg(box_str))
    self.node_list.can_get_reward:CustomSetActive(has_red)

    local cur_add_exp_per = off_data.total_cur_add_exp_per
    self.node_list.cur_add_exp_per.text.text = string.format("+%s%%",  cur_add_exp_per * 0.01)
end

-- 清除挂机定时器
function TianShenLiLianView:CleanOfflineTimer()
    if CountDownManager.Instance:HasCountDown("lilian_offline") then
        CountDownManager.Instance:RemoveCountDown("lilian_offline")
    end
end

-- 刷新挂机时间
function TianShenLiLianView:FlushOfflineTime(elapse_time, total_time)
    local off_data = OfflineRestWGData.Instance
    local other_cfg = OfflineRestWGData.Instance:GetOtherCfg()
    local hang_max_offlie_time = other_cfg.hang_max_offlie_time or 0
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local last_calc_online_reward_time = off_data.last_calc_online_reward_time
    local leiji_offline_time = server_time > last_calc_online_reward_time and (server_time - last_calc_online_reward_time) or 0
    self:FlushOfflineTimeStr(leiji_offline_time)
end

--刷新挂机时间状态
function TianShenLiLianView:FlushOfflineTimeStr(rest_time)
    local other_cfg = OfflineRestWGData.Instance:GetOtherCfg()
    local hang_max_offlie_time = other_cfg.hang_max_offlie_time or 0

    if self.node_list.guaiji_time_slider then
        self.node_list.guaiji_time_slider.slider.value = rest_time / hang_max_offlie_time
    end

    if self.node_list.guaiji_time_str then
        self.node_list.guaiji_time_str.text.text = TimeUtil.FormatSecondDHM4(rest_time)
    end
end

-- 刷新历练值
function TianShenLiLianView:FlushLilianNum()
    local exp = AssignmentWGData.Instance:GetTaskExp()
    local max_exp = AssignmentWGData.Instance:GetMaxTaskExp()
    self.node_list.now_lilian_text.text.text = string.format(Language.Assignment.LilianSpliderStr, exp, max_exp)
end

--刷新历练红点
function TianShenLiLianView:FlushLilianNumAndRemind()
    local exp = AssignmentWGData.Instance:GetTaskExp()
    local max_exp = AssignmentWGData.Instance:GetMaxTaskExp()
    self.node_list.now_lilian_text.text.text = string.format(Language.Assignment.LilianSpliderStr, exp, max_exp)

    local remind = AssignmentWGData.Instance:GetAssignRemind()
    self.node_list.go_assign_remind:CustomSetActive(remind == 1)
end

-- 刷新副本信息
function TianShenLiLianView:FlushExpFbPart()
    local list = ExperienceFbWGData.Instance:LiLianShowStageList()
    for i, render_cell in ipairs(self.mid_fb_list) do
        render_cell:SetVisible(list[i] ~= nil)

        if list[i] ~= nil then
            render_cell:SetData(list[i])
        end
    end
end

-----------------------------------------------------------------------------------------------------
-- 领取离线挂机经验
function TianShenLiLianView:OnClickSitBox()
    OfflineRestWGCtrl.Instance:SendGetSitRewardInfo(true)
end

-- 打开挑战副本
function TianShenLiLianView:OnClickChallengeFB()
    ExperienceFbWgCtrl.Instance:OpenExperienceFbView()
end

-- 打开委托
function TianShenLiLianView:OnGoAssignClick()
    ViewManager.Instance:Open(GuideModuleName.AssignmentView)
end

function TianShenLiLianView:OnClickExpPerBtn()
    ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end
----------------------------出战位置-----------------------------------------
LiLianChuZhanRender = LiLianChuZhanRender or BaseClass(BaseRender)
function LiLianChuZhanRender:LoadCallBack()
    self.slot_is_open = false
    XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSlot, self))
end

function LiLianChuZhanRender:OnFlush()
    self.node_list.add_icon:CustomSetActive(self.data == nil)
    self.node_list.tianshen_icon:CustomSetActive(self.data ~= nil)

    if not self.data then
        return
    end

    self.node_list.tianshen_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.bianshen_icon))
    self.node_list.tianshen_icon.image:SetNativeSize()
end

function LiLianChuZhanRender:OnClickSlot()
    if not self.data then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.NoTianShenTips)
        return
    end

    local has_list = TianShenWGData.Instance:GetFightTisnShen(false)
    if #has_list > 0 then
        ViewManager.Instance:Open(GuideModuleName.TianShenSelectView, nil, nil,
            { index = self.index - 1, data = self.data or false })
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.NoTianShenTips_1)
    end
end

----------------------------副本-----------------------------------------
-- 经验加成信息
LilianDetailedRender = LilianDetailedRender or BaseClass(BaseRender)
function LilianDetailedRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.now_text.text.text = self.data.now_text
    self.node_list.now_text_per.text.text = self.data.now_text_per

    if self.node_list.sp_img then
        local width_size = 134
        if not self.data.sp_img then
            width_size = 158
        end

        self.node_list.sp_img:CustomSetActive(self.data.sp_img)
        self.node_list.now_text_per.rect.sizeDelta = Vector2(width_size, 30)
    end
end

-- 副本信息
LiLianFbRender = LiLianFbRender or BaseClass(BaseRender)
function LiLianFbRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.fb_level.text.text = string.format("%d-%d", self.data.level, self.data.wave) 
    self.node_list.fb_name_txt.text.text = string.format(Language.OfflineRest.ExperienceFbGoToTips, self.data.stage_name)
    local is_pass = self.data.level <= self.data.now_level
    XUI.SetGraphicGrey(self.node_list.line_root, not is_pass)
    self.node_list.fb_name_root:CustomSetActive(self.data.level == self.data.now_level)
    self:OnSelectChange(self.data.level == self.data.now_level)
    local is_pass_ed = self.data.level < self.data.now_level
	self.node_list.pass_ed:CustomSetActive(is_pass_ed)

	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.monster_id]
    if monster_cfg then
        local asset_name,bundle_name = ResPath.GetBossIcon("wrod_boss_"..monster_cfg.small_icon)
        self.node_list.fb_icon.image:LoadSprite(asset_name,bundle_name,function ()
            self.node_list.fb_icon.image:SetNativeSize()
        end)
    end
end

function LiLianFbRender:OnSelectChange(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
    self.node_list.select:CustomSetActive(is_select)
end
