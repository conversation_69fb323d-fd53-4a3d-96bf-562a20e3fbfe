require("game/hidden_weapon/utils/hidden_weapon_remind_manager")
HiddenWeaponWGData = HiddenWeaponWGData or BaseClass()

HIDDEN_WEAPON_TYPE = {
    MAIN_WEAPON = "MAIN_WEAPON", --暗器
    SOFT_ARMOR = "XIANQI" --软甲
}

-- 身上装备的暗器软甲信息，由装备信息协议返回，不存在于服务端返回背包列表，无特定index
-- 详情展示时要由客户端生成对象添加，按以下索引来区分已装备未装备
-- 合成时发现特定索引则改用 HiddenWeaponRequest:ReqCurrEquipCompose
HW_ANQI_EQUIP_INDEX = -998
HW_RUANJIA_EQUIP_INDEX = -997

HW_BASE_ATTRS = {
    "gongji",
    "pojia",
    "yuansu_sh",
    "shanghai_zs",
    "shengming_max",
    "fangyu",
    "yuansu_hj",
    "hanghai_zs",
    "shengming_qq",
    "fangyu_zs",
    "fangtan"
}

-- 暗器技能特效类型
HIDDEN_WEAPON_EFFECT_TYPE = {
    INVALID = 0, -- 非法类型
    TARGET_CIRCLE = 1, -- 目标处 圆形
    SELF_SECTOR = 2, -- 释放者处 扇形
    TARGET = 3 -- 目标处 单体
}

function HiddenWeaponWGData:__init()
    if HiddenWeaponWGData.Instance then
        ErrorLog("[HiddenWeaponWGData]:Attempt to create singleton twice!")
    end
    HiddenWeaponWGData.Instance = self

    self.sj_bag_list = {}
    self.sj_equip_vo_bag_list = {}
    self.anqi_info = {}
    self.ruanjia_info = {}
    self:SetupComposeConfig()

    self.remind_manager = HiddenWeaponRemindManager.New(self)
end

function HiddenWeaponWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.HiddenWeapon)
    self.retry_get_shenji_equip = nil
    self.retry_get_shenji_bag = nil

    self.sj_bag_list = nil
    self.sj_equip_list = nil
    self.sj_equip_vo_bag_list = nil

    HiddenWeaponWGData.Instance = nil
    if self.remind_manager then
        self.remind_manager:DeleteMe()
        self.remind_manager = nil
    end
end

function HiddenWeaponWGData:SetupComposeConfig()
    self.cfg_shenji_equip = ConfigManager.Instance:GetAutoConfig("shenji_equip_auto")
    -- 装备信息（卡牌）
    self.map_equip = ListToMap(self.cfg_shenji_equip.equip, "item_id")
    -- 装备基础属性（大类+小类+品质+星级相同，则属性相同）
    self.map_equip_base_attr =
        self:ListToMapWithMultiKey(
        self.cfg_shenji_equip.base_attr,
        "big_type",
        "node_type",
        "color",
        "star",
        "special_flag"
    )
    -- 装备星级属性（大类+品质+星级相同，则属性相同）
    self.map_equip_color_attr =
        self:ListToMapWithMultiKey(self.cfg_shenji_equip.color_attr, "big_type", "color", "star", "special_flag")
    -- 打造配方（以当前品质星级为key，找寻下一级）
    self.map_equip_compose =
        self:ListToMapWithMultiKey(self.cfg_shenji_equip.breakthrough, "special_flag", "color", "star")

    self.awaken_cfg = ListToMapList(self.cfg_shenji_equip.special_effect, "big_type")
    --装备部位名字表
    self.name_cfg = ListToMap(self.cfg_shenji_equip.name, "big_type", "node_type")
    -- 刻铭突破表
    self.map_rand_attr_break = ListToMap(self.cfg_shenji_equip.rand_attr_break, "rand_index")
    --注灵等级表
    self.uplevel_cfg = ListToMap(self.cfg_shenji_equip.uplevel, "big_type", "node_type", "level")
    --注灵升阶表
    self.upgrade_cfg = ListToMap(self.cfg_shenji_equip.upgrade, "big_type", "node_type", "grade")
    --星级属性表
    self.color_attr_cfg = ListToMap(self.cfg_shenji_equip.color_attr, "big_type", "color", "star", "special_flag")
    --专属表
    self.special_skill_cfg = ListToMap(self.cfg_shenji_equip.special_skill, "skill_id", "skill_level")
    --主动技能
    self.active_skill_cfg_ics = ListToMap(self.cfg_shenji_equip.active_skill, "skill_id", "need_color", "need_star")
    --主动技能
    self.active_skill_cfg = ListToMap(self.cfg_shenji_equip.active_skill, "skill_id", "skill_level")
    --被动技能
    self.passive_skill_cfg_ics = ListToMap(self.cfg_shenji_equip.passive_skill, "skill_id", "need_color", "need_star")
    self.passive_skill_cfg = ListToMap(self.cfg_shenji_equip.passive_skill, "skill_id", "skill_level")
    --专属技能
    self.special_skill_cfg_ics = ListToMap(self.cfg_shenji_equip.special_skill, "skill_id", "need_color", "need_star")
    --星级属性顺序表
    self.map_color_attr_index = ListToMapList(self.cfg_shenji_equip.color_attr_index, "big_type")

    --当前打造配方
    self.map_now_cfg = ListToMap(self.cfg_shenji_equip.breakthrough, "special_flag", "color", "star")
    --目标打造配方
    self.map_target_cfg = ListToMap(self.cfg_shenji_equip.breakthrough, "special_flag", "target_color", "target_star")

    self.model_cfg = ListToMap(self.cfg_shenji_equip.model_icon_change, "item_id", "color")
end

function HiddenWeaponWGData:GetAwakenCfgData(big_type)
    return self.awaken_cfg[big_type]
end

function HiddenWeaponWGData:GetMaxAwakenCfgData(big_type)
    local data_cfg = self:GetAwakenCfgData(big_type)
    local max_lv = 0
    local cfg
    for k, v in pairs(data_cfg) do
        if max_lv < v.effect_level then
            cfg = v
        end
    end
    return cfg
end

-- 通用 根据服务端返回装备数据获取完整结构（装备可能升品升星过）
-- {item_id,color,star}
function HiddenWeaponWGData:GetCommonEquipVo(protocol_equip_item)
    local result = {}
    if protocol_equip_item == nil then
        return result
    end
    local item_id = tonumber(protocol_equip_item.item_id)
    result.protocol_equip_item = protocol_equip_item
    result.index = protocol_equip_item.index
    result.item_id = item_id
    result.equip = self:GetEquipCfgById(item_id)
    if result.equip then
        -- 获取属性前，给装备设置正式的品质和星级
        result.equip.base_color = tonumber(protocol_equip_item.color) or result.equip.base_color
        result.equip.base_star = tonumber(protocol_equip_item.star) or result.equip.base_star
        result.base_attr = self:GetEquipBaseAttr(result.equip)
        result.color_attr = self:GetEquipColorAttr(result.equip)
        result.equip.up_model = self:GetEquipModelAsset(item_id, result.equip.base_color)
        result.equip.special_skill_icon = self:GetEquipSkillIcon(item_id, result.equip.base_color)
        result.equip.sj_icon = self:GetEquipIcon(item_id, result.equip.base_color)
    else
        result.base_attr = {}
        result.color_attr = {}
    end

    result.capability = protocol_equip_item.capability or 0
    return result
end

function HiddenWeaponWGData:GetEquipModelCfg(item_id, color)
    if not self.model_cfg[item_id] then
        return nil
    end

    local cfg = self.model_cfg[item_id][color]

    if not cfg then
        return nil
    end

    return cfg
end

function HiddenWeaponWGData:GetEquipModelAsset(item_id, color)
    local cfg = self:GetEquipModelCfg(item_id, color)

    if not cfg then
        return nil
    end

    return cfg.model
end

function HiddenWeaponWGData:GetEquipSkillIcon(item_id, color)
    local cfg = self:GetEquipModelCfg(item_id, color)

    if not cfg then
        return nil
    end

    return cfg.skill_icon
end

function HiddenWeaponWGData:GetEquipIcon(item_id, color)
    local cfg = self:GetEquipModelCfg(item_id, color)

    if not cfg then
        return nil
    end

    return cfg.icon
end

-- 通用 获取一件装备的基础结构
function HiddenWeaponWGData:GetBaseEquipVo(item_id)
    local item_id = tonumber(item_id)
    local equip = self:GetEquipCfgById(item_id)
    if equip then
        local result = {}
        result.item_id = item_id
        result.equip = equip
        result.base_attr = self:GetEquipBaseAttr(result.equip)
        result.color_attr = self:GetEquipColorAttr(result.equip)

        result.param0 = 4
        return result
    end
    return nil
end

function HiddenWeaponWGData:GetEquipCfgById(item_id)
    local item_id = tonumber(item_id)
    if item_id == nil or item_id <= 0 then
        return nil
    end
    local equip = self.map_equip[item_id]
    if equip then
        return self:GenVoWrapperFromData(equip)
    end
    return nil
end

-- 根据item_id获得暗器模型id
function HiddenWeaponWGData:GetWeaponModelId(item_id)
    local equip_cfg = self:GetEquipCfgById(item_id)
    if equip_cfg then
        local data = self:GetSCShenJiEquipGridByType(equip_cfg.big_type)
        if data and data.equip then
            return data.equip.up_model or 0
        end
    end

    return 0
end

-- 获得暗器技能特效数据
function HiddenWeaponWGData:GetWeaponSkillEffectData(skill_id)
    if not self.active_skill_cfg[skill_id] then
        return HIDDEN_WEAPON_EFFECT_TYPE.INVALID, ""
    end

    local skill_cfg = self.active_skill_cfg[skill_id][1]
    if not skill_cfg then
        return HIDDEN_WEAPON_EFFECT_TYPE.INVALID, ""
    end

    local effect_type = skill_cfg.effect_type or HIDDEN_WEAPON_EFFECT_TYPE.INVALID
    local effect = skill_cfg.effect or ""

    return effect_type, effect
end

--物品item_id和是否进阶返回模型ab
function HiddenWeaponWGData:GetEquipModel(item_id, is_up)
    local cfg = self:GetEquipCfgById(item_id)
    local model = is_up and cfg.up_model or cfg.base_model
    return ResPath.GetShenJiModel(cfg.base_model)
end

function HiddenWeaponWGData:GetEquipBaseAttr(equip)
    if not equip then
        return {}
    end
    local key =
        "" ..
        (equip.big_type or 0) ..
            (equip.node_type or 0) .. (equip.base_color or 0) .. (equip.base_star) .. (equip.special_flag or 0)
    local base_attr = self.map_equip_base_attr[key]
    if base_attr then
        return self:GenVoWrapperFromData(base_attr)
    end
    return {}
end

function HiddenWeaponWGData:GetEquipColorAttr(equip)
    if not equip then
        return {}
    end
    local key = "" .. (equip.big_type or 0) .. (equip.base_color or 0) .. (equip.base_star) .. (equip.special_flag or 0)
    local color_attr = self.map_equip_color_attr[key]
    if color_attr then
        return self:GenVoWrapperFromData(color_attr)
    end
    return {}
end

-- 获取打造下一级的装备基本结构
function HiddenWeaponWGData:GetComposeTargetEquip(data)
    if data == nil or data.equip == nil then
        return nil
    end
    -- 当前装备品质星级
    local special_flag = data.equip.special_flag
    local curr_color = data.equip.base_color
    local curr_star = data.equip.base_star
    local key = "" .. special_flag .. curr_color .. curr_star
    -- 获取下一级的品质星级
    local target = self.map_equip_compose[key]
    if target == nil then
        return nil
    end
    local target_color = target.target_color
    local target_star = target.target_star
    -- 判断是否超出上限
    local base_equip = self:GetEquipCfgById(data.item_id)
    if target_color > base_equip.limit_color or target_star > base_equip.limit_star then
        return nil
    end
    -- 获取完整装备结构
    local protocol_item = {}
    protocol_item.item_id = data.item_id
    protocol_item.color = target.target_color
    protocol_item.star = target.target_star

    return self:GetCommonEquipVo(protocol_item), target
end

-- 只读对象
function HiddenWeaponWGData:GenVoWrapperFromData(origin_data)
    local obj = {
        origin_data = origin_data,
        is_wrap = true
    }
    setmetatable(obj, {__index = obj.origin_data})
    return obj
end

-- list 转 map，同时使用多个属性合并为主键
function HiddenWeaponWGData:ListToMapWithMultiKey(list, ...)
    local map = {}
    local key_list = {...}
    for index, cfg in ipairs(list) do
        local key_values = {}
        for index = 1, #key_list do
            local key_name = key_list[index]
            key_values[index] = cfg[key_name]
        end
        local key = table.concat(key_values)
        map[key] = cfg
    end
    return map
end

-- 当前穿戴的神机装备信息
function HiddenWeaponWGData:SetSCShenJiEquipGrid(protocol)
    self.anqi_info = protocol.anqi_info
    self.ruanjia_info = protocol.ruanjia_info
end

-- 获取已装备神机协议返回数据
function HiddenWeaponWGData:GetSCShenJiEquipGrid()
    local equip_list = {}
    equip_list[1] = self:GetCommonEquipVo(self.anqi_info)
    if equip_list[1] then
        equip_list[1].index = HW_ANQI_EQUIP_INDEX
    end
    equip_list[2] = self:GetCommonEquipVo(self.ruanjia_info)
    if equip_list[2] then
        equip_list[2].index = HW_RUANJIA_EQUIP_INDEX
    end

    -- 获取装备信息为空时，尝试请求一次。可改为次数上限
    local is_empty =
        (equip_list[1] == nil or equip_list[1].equip == nil) and (equip_list[2] == nil or equip_list[2].equip == nil)
    if not self.retry_get_shenji_equip and is_empty then
        self.retry_get_shenji_equip = true
        HiddenWeaponRequest:ReqGridInfo()
    end
    return equip_list
end

function HiddenWeaponWGData:GetSCShenJiEquipGridByType(type)
    local equip_list = self:GetSCShenJiEquipGrid()
    return equip_list[type]
end

-- 获取神机背包协议返回列表
function HiddenWeaponWGData:GetSCShenJiEquipBag()
    local is_empty = self.sj_bag_list == nil or #self.sj_bag_list <= 0
    if not self.retry_get_shenji_bag and is_empty then
        -- 背包为空时，尝试请求一次。可改为次数上限
        self.retry_get_shenji_bag = true
        HiddenWeaponRequest:ReqBagList()
    end
    return self.sj_bag_list or {}
end

--协议下发时 先使用GetCommonEquipVo 获取装备具体数据 先用GetEquipCommonScore计算好评分
function HiddenWeaponWGData:GetSJEquipVoBagList()
    local is_empty = self.sj_equip_vo_bag_list == nil or #self.sj_equip_vo_bag_list <= 0
    if not self.retry_get_shenji_bag and is_empty then
        -- 背包为空时，尝试请求一次。可改为次数上限
        self.retry_get_shenji_bag = true
        HiddenWeaponRequest:ReqBagList()
    end
    return self.sj_equip_vo_bag_list or {}
end

-- 神机装备列表
function HiddenWeaponWGData:SetSCShenJiEquipBag(protocol)
    if protocol.sj_bag_list == nil then
        return
    end
    if protocol.send_reason == 0 then
        -- 下发原因-全部
        self.sj_bag_list = {}
        self.sj_equip_vo_bag_list = {}
        for i, value in pairs(protocol.sj_bag_list) do
            self.sj_bag_list[value.index or 0] = value
            local equip_vo = HiddenWeaponWGData.Instance:GetCommonEquipVo(value)
            -- local base_score = self:GetEquipCommonScore(equip_vo)
            -- equip_vo.base_score = base_score
            self.sj_equip_vo_bag_list[value.index or 0] = equip_vo
        end
    else
        for index, value in pairs(protocol.sj_bag_list) do
            self.sj_bag_list[value.index or 0] = value
            local equip_vo = HiddenWeaponWGData.Instance:GetCommonEquipVo(value)
            -- local base_score = self:GetEquipCommonScore(equip_vo)
            -- equip_vo.base_score = base_score
            self.sj_equip_vo_bag_list[value.index or 0] = equip_vo
        end
    end
    if protocol.send_reason == 1 then
        local bag_list = protocol.sj_bag_list
        if #bag_list == 1 and bag_list[1] and bag_list[1].item_id == 0 then
            self.last_change_itemindex = bag_list[1].index
        end
    end
end

function HiddenWeaponWGData:GetDataByIndex(item_index)
    -- 根据服务端返回的神机背包信息，生成具体装备数据
    local equip_vo_bag_list = self:GetSJEquipVoBagList()
    local equip_vo = equip_vo_bag_list[item_index]
    if equip_vo and equip_vo.equip then
        return equip_vo
    end
    return nil
end

-- 搜索背包数据
function HiddenWeaponWGData:GetDataListByCondition(weapon_type, item_index, item_id, color, star, node_type)
    local ignore_equiped = true
    local data_list = self:GetDataList(weapon_type, -1, ignore_equiped)
    local search_result = {}
    for index, data in ipairs(data_list) do
        if data.index ~= item_index 
        and (not item_id or data.item_id == item_id)
        and (not node_type or data.equip.node_type == node_type)
        and (not color or data.equip.base_color == color)
        and (not star or data.equip.base_star == star) then
            table.insert(search_result, data)
        end
    end

    return search_result
end

function HiddenWeaponWGData:GetEquipCommonScore(data)
    local base_attr_obj = AttributeMgr.GetAttributteByClass(data.base_attr)
    local score1 = TipWGData.Instance:GetCommonPingFenByAttrList(base_attr_obj) or 0
    local pro = (data.color_attr.base_attr_per or 0) / 10000
    score1 = score1 * (1 + pro)

    local color_attr_obj = AttributeMgr.GetAttributteByClass(data.color_attr)
    local score2 = TipWGData.Instance:GetCommonPingFenByAttrList(color_attr_obj) or 0

    return score1 + score2
end

-- 获取实际数据列表，当不用区分武器类型获得所有装备数据时不传weapon_type
function HiddenWeaponWGData:GetDataList(weapon_type, filter_tag, ignore_equiped)
    local map_origin_datas = {}
    local map_equiped = {} --当前装备中的
    local curr_datas = {}
    -- 先添加已经装备的
    if ignore_equiped == nil or ignore_equiped == false then
        local equip_list = self:GetSCShenJiEquipGrid()
        for index, equip_vo in ipairs(equip_list) do
            if equip_vo and equip_vo.equip then
                if weapon_type then
                    local bid_type = equip_vo.equip.big_type
                    map_origin_datas[bid_type] = map_origin_datas[bid_type] or {}
                    table.insert(map_origin_datas[bid_type], equip_vo)
                else
                    table.insert(map_origin_datas, equip_vo)
                end

                equip_vo.base_score = self:GetEquipCommonScore(equip_vo)
                equip_vo.is_dress = (equip_vo.index == HW_ANQI_EQUIP_INDEX or equip_vo.index == HW_RUANJIA_EQUIP_INDEX)
                map_equiped[equip_vo.equip.big_type] = equip_vo
            end
        end
    end

    -- 根据服务端返回的神机背包信息，生成具体装备数据
    local equip_vo_bag_list = self:GetSJEquipVoBagList()
    local result = {}
    for index, equip_vo in pairs(equip_vo_bag_list) do
        if equip_vo and equip_vo.equip then
            if weapon_type then
                local bid_type = equip_vo.equip.big_type
                map_origin_datas[bid_type] = map_origin_datas[bid_type] or {}
                table.insert(map_origin_datas[bid_type], equip_vo)
            else
                table.insert(map_origin_datas, equip_vo)
            end
        end
    end

    -- 筛选
    local bid_type_datas
    if weapon_type then
        bid_type_datas = map_origin_datas[weapon_type] or {}
    else
        bid_type_datas = map_origin_datas or {}
    end

    local filter_result = {}
    if filter_tag and filter_tag > 0 then
        for index, data in ipairs(bid_type_datas) do
            if weapon_type then
                if data.equip and data.equip.node_type == filter_tag then
                    table.insert(filter_result, data)
                end
            else
                local filter_param1 = filter_tag > 3 and 2 or 1 --区分武器类型
                local filter_param2 = filter_tag > 3 and filter_tag - 3 or filter_tag --区分子类型
                if data.equip and data.equip.node_type == filter_param2 and data.equip.big_type == filter_param1 then
                    table.insert(filter_result, data)
                end
            end
        end
    else
        filter_result = bid_type_datas
    end
    curr_datas = filter_result

    -- 排序
    local index1 = 1
    local index2 = 2
    local func_sort = function(a, b)
        -- 1.已装备暗器
        if (a.index == HW_ANQI_EQUIP_INDEX) then
            return true
        end
        if (b.index == HW_ANQI_EQUIP_INDEX) then
            return false
        end
        -- 2.已装备软甲
        if (a.index == HW_RUANJIA_EQUIP_INDEX) then
            return true
        end
        if (b.index == HW_RUANJIA_EQUIP_INDEX) then
            return false
        end
        -- 3.品质高＞品质低
        if (a.equip.base_color == b.equip.base_color) then
            if (a.equip.origin_data.base_color ~= b.equip.origin_data.base_color) then
                return a.equip.origin_data.base_color > b.equip.origin_data.base_color
            end
        else
            return a.equip.base_color > b.equip.base_color
        end

        -- 3.星级高＞星级低
        if (a.equip.base_star > b.equip.base_star) then
            return true
        end
        if (a.equip.base_star < b.equip.base_star) then
            return false
        end
        -- 4.基础评分高＞基础评分低
        -- 珍稀标志
        if (a.equip.special_flag > b.equip.special_flag) then
            return true
        end
        if (a.equip.special_flag < b.equip.special_flag) then
            return false
        end
        -- 5.子类型1234
        if (a.equip.node_type == b.equip.node_type) then
            return a.index < b.index
        else
            return a.equip.node_type < b.equip.node_type
        end
    end
    table.sort(curr_datas, func_sort)
    -- 战力对比检查
    if not ignore_equiped then
        self:HandleEquipCompare(curr_datas, map_equiped)
        -- 可合成检查
        self:HandleComposeCheck(bid_type_datas, curr_datas)
    end

    return curr_datas
end

function HiddenWeaponWGData:HandleEquipCompare(curr_datas, map_equiped)
    for index, value in ipairs(curr_datas) do
        -- HWDetailItemRender 其他地方用到 base_score 评分
        value.is_better = false
        -- value.base_score = self:GetEquipCommonScore(value)
        local equiped = map_equiped[value.equip.big_type]
        local equip = (equiped or {}).equip
        local curr_equip = value.equip
        if curr_equip then
            local cur_special_flag = curr_equip.special_flag or 0
            local equip_special_flag = equip and equip.special_flag or 0
            local origin_base_color = curr_equip.origin_data.base_color or 0
            local origin_equip_color = equip and equip.origin_data.base_color or 0
            local origin_base_star = curr_equip.origin_data.base_star or 0
            local origin_equip_star = equip and equip.origin_data.base_star or 0
            -- 1.是否有已穿戴的装备
            if equiped == nil or equip == nil then
                value.is_better = true
            -- 2.已穿戴装备，对比珍稀标志
            elseif cur_special_flag ~= equip_special_flag then
                value.is_better = cur_special_flag > equip_special_flag
            -- 3.珍稀标志相同，对比原始品质
            elseif origin_base_color ~= origin_equip_color then
                value.is_better = origin_base_color > origin_equip_color
            -- -- 4.原始品质相同，对比当前品质（评分）
            -- elseif value.base_score > (equiped.base_score or 0) then
            --     value.is_better = true
            -- 4.品质相同 对比星级
            elseif origin_base_star ~= origin_equip_star then
                 value.is_better = origin_base_star > origin_equip_star
            end
        end
    end
end

--与当前身上的装备做比较
function HiddenWeaponWGData:CheckEquipCompare(check_equip)
    local check_cfg = self:GetOnlyEquipCfgById(check_equip.item_id)
    local is_up = false
    if check_cfg then
        local self_equip = self:GetSCShenJiEquipGridByType(check_cfg.big_type)            -- 大类型 身上的装备
        if IsEmptyTable(self_equip) then
            is_up = true
            return is_up
        end

        local curr_equip = self_equip.equip
        if curr_equip == nil then
            is_up = true
            return is_up
        end

         -- 2.已穿戴装备，对比珍稀标志
        local cur_special_flag = (curr_equip.special_flag or 0)
        local equip_special_flag = (check_cfg.special_flag or 0)
        if cur_special_flag ~= equip_special_flag then
            is_up = cur_special_flag < equip_special_flag
        end
        -- 3.珍稀标志相同，对比原始品质
        local origin_base_color = (curr_equip.origin_data.base_color or 0)
        local origin_equip_color = (check_cfg.base_color or 0)
        if origin_base_color ~= origin_equip_color then
            is_up = origin_base_color < origin_equip_color
        end
        -- 4.原始品质相同，对比当前品质（评分）
        local check_equip_vo = self:GetBaseEquipVo(check_equip.item_id)
        local check_base_score = self:GetEquipCommonScore(check_equip_vo)
        local self_base_score = self:GetEquipCommonScore(self_equip)
        if check_base_score > self_base_score then
            is_up = true
        end
    end
    return is_up
end

function HiddenWeaponWGData:HandleComposeCheck(bid_type_datas, curr_datas)
    -- 可打造判断
    -- 将装备转成数量表，再判断合成所需材料数量
    local map_equip_count = {} --装备数量
    local hight_equip_vo = bid_type_datas[1]       --大类型 最高品质的装备
    local hight_equip_index = 0
    -- 取大类型种 最高品质的装备
    if not IsEmptyTable(hight_equip_vo) and not hight_equip_vo.is_dress then
        local self_equip = self:GetSCShenJiEquipGridByType(hight_equip_vo.equip.big_type)            -- 大类型 身上的装备
        if self_equip and self_equip.equip then
            -- 品质高＞品质低
            if (hight_equip_vo.equip.base_color == self_equip.equip.base_color) then
                if (hight_equip_vo.equip.origin_data.base_color ~= self_equip.equip.origin_data.base_color) then
                    hight_equip_vo = hight_equip_vo.equip.origin_data.base_color > self_equip.equip.origin_data.base_color and hight_equip_vo or self_equip
                else
                    -- 星级高＞星级低
                    if (hight_equip_vo.equip.base_star > self_equip.equip.base_star) then
                        hight_equip_vo = hight_equip_vo
                    end
                    if (hight_equip_vo.equip.base_star < self_equip.equip.base_star) then
                        hight_equip_vo = self_equip
                    end
                end
            else
                hight_equip_vo = hight_equip_vo.equip.base_color > self_equip.equip.base_color and hight_equip_vo or self_equip
            end
        end
    end

    for index, data in ipairs(bid_type_datas) do
        -- 已装备的不算材料
        if data.index >= 0 and data.equip then
            --同名
            local key_list = {data.item_id, data.equip.big_type, data.equip.base_color, data.equip.base_star}
            local key = table.concat(key_list, "-")
            if map_equip_count[key] == nil then
                map_equip_count[key] = {}
            end
            table.insert(map_equip_count[key],data.index)
            -- map_equip_count[key] = (map_equip_count[key] or 0) + 1

            --同子类
            local key_list1 = {data.equip.node_type, data.equip.big_type, data.equip.base_color, data.equip.base_star}
            local key1 = table.concat(key_list1, "-")
            if map_equip_count[key1] == nil then
                map_equip_count[key1] = {}
            end
            table.insert(map_equip_count[key1],data.index)
            -- map_equip_count[key1] = (map_equip_count[key1] or 0) + 1

            --同大类
            local key_list2 = {data.equip.big_type, data.equip.base_color, data.equip.base_star}
            local key2 = table.concat(key_list2, "-")
            if map_equip_count[key2] == nil then
                map_equip_count[key2] = {}
            end
            table.insert(map_equip_count[key2],data.index)
            -- map_equip_count[key2] = (map_equip_count[key2] or 0) + 1
        end
    end

--取大类型 最高品质的装备 所需要的打造配置   至少 橙 6 以上
    --合成红点新加判断逻辑 
    --如果当前大类型中品质最高的装备中所需合成的材料中 最低所需的品质和星级
    --其他装备合成的红点必须要 小于 最高品质的装备中需要合成所需的品质和星级
    local hight_need_color = 0
    local hight_need_star = 0
    if hight_equip_vo then
        hight_equip_index = hight_equip_vo.index
        local hight_equip = hight_equip_vo.equip
        local higth_break_through = self:GetBreakThrough(hight_equip.special_flag, hight_equip.base_color, hight_equip.base_star, false)
        if higth_break_through ~= nil then
            local hight_id_color = higth_break_through.same_id_color >= 4 and higth_break_through.same_id_color or 9999
            local hight_big_type_color = higth_break_through.same_big_type_color >= 4 and higth_break_through.same_big_type_color or 9999
            local hight_node_type_color = higth_break_through.same_node_type_color >= 4 and higth_break_through.same_node_type_color or 9999
            local hight_id_star = higth_break_through.same_id_star >= 6 and higth_break_through.same_id_star or 9999
            local hight_big_type_star = higth_break_through.same_big_type_star >= 6 and higth_break_through.same_big_type_star or 9999
            local hight_node_type_star = higth_break_through.same_node_type_star >= 6 and higth_break_through.same_node_type_star or 9999
            hight_need_color = math.min(hight_id_color,hight_big_type_color,hight_node_type_color)
            hight_need_star = math.min(hight_id_star,hight_big_type_star,hight_node_type_star)
        end
    end

    -- 等级限制
    local level = RoleWGData.Instance:GetRoleLevel()
    for index, data in ipairs(curr_datas) do
        local equip = data.equip

        data.can_compose = false
        local break_through = self:GetBreakThrough(equip.special_flag, equip.base_color, equip.base_star, false)
        local base_equip = self:GetEquipCfgById(data.item_id)
        if not (equip.base_star >= hight_need_star and equip.base_color >= hight_need_color and data.index ~= hight_equip_index)
        -- 非等级不足
        and not (break_through == nil or level < (break_through.role_level_limit or 0))
        -- 非超出打造上限
        and not (break_through.target_color > base_equip.limit_color or break_through.target_star > base_equip.limit_star) then

            -- map_equip_count 里是包含自身的，所以判断数量要+1，但已装备的不在材料中，所以不+1
            local remove_self = (data.index ~= HW_ANQI_EQUIP_INDEX and HW_RUANJIA_EQUIP_INDEX ~= data.index)
            local key_self_0 =
                table.concat({data.item_id, data.equip.big_type, data.equip.base_color, data.equip.base_star}, "-")
            local key_self_1 =
                table.concat({data.equip.node_type,data.equip.big_type, data.equip.base_color, data.equip.base_star}, "-")
            local key_self_2 = table.concat({data.equip.big_type, data.equip.base_color, data.equip.base_star}, "-")
            -- --同名卡
            local key_require_0 =
                table.concat(
                {
                    data.item_id,
                    data.equip.big_type,
                    break_through.same_id_color,
                    break_through.same_id_star
                },
                "-"
            )
            --同子类卡
            local key_require_1 =
                table.concat(
                {
                    data.equip.node_type,
                    data.equip.big_type,
                    break_through.same_node_type_color,
                    break_through.same_node_type_star
                },
                "-"
            )

            --同类卡
            local key_require_2 =
                table.concat(
                {data.equip.big_type, break_through.same_big_type_color, break_through.same_big_type_star},
                "-"
            )
            local require_same_count = break_through.same_id_num
            local require_node_type_count = break_through.same_node_type_num
            local require_type_count = break_through.same_big_type_num
            -- 有A张同名 B张同子类，C张同类  A可能包括BC B可能包括C
            local key0_equip_count = (map_equip_count[key_require_0] and #map_equip_count[key_require_0] or 0)  --同名卡
            local key1_equip_count = (map_equip_count[key_require_1] and #map_equip_count[key_require_1] or 0)  --同子类卡
            local key2_equip_count = (map_equip_count[key_require_2] and #map_equip_count[key_require_2] or 0)  --同类卡

            local data_color = data.equip.origin_data.base_color
            local data_special_flag = data.color_attr.special_flag

            if key_self_0 == key_require_0 and remove_self then
                -- 颜色品质相同，排除自身
                key0_equip_count = key0_equip_count - 1
            end

            if key_self_1 == key_require_1 then
                -- 颜色品质相同，排除自身
                if remove_self then
                    key1_equip_count = key1_equip_count - 1
                end
            end

            if require_node_type_count > 0 then
                --打造配置中所消耗的装备星级和品质比 所选的装备要高的话 数量-1
                local count_list = map_equip_count[key_require_1] or {}
                for k,v in pairs(count_list) do
                    local equip_vo = self:GetDataByIndex(v)
                    if equip_vo then
                        local select_color = equip_vo.equip.origin_data.base_color
                        local select_special_flag = equip_vo.color_attr.special_flag
                        if select_color > data_color or (select_special_flag == 1 and data_special_flag == 0) then
                            key1_equip_count = key1_equip_count - 1
                        end
                    end
                end
            end

            if key_self_2 == key_require_2 then
                -- 颜色品质相同，排除自身
                if remove_self then
                    key2_equip_count = key2_equip_count - 1
                end
            end

            if require_type_count > 0 then
                --打造配置中所消耗的装备星级和品质比 所选的装备要高的话 数量-1
                local count_list = map_equip_count[key_require_2] or {}
                for k,v in pairs(count_list) do
                    local equip_vo = self:GetDataByIndex(v)
                    if equip_vo then
                        local select_color = equip_vo.equip.origin_data.base_color
                        local select_special_flag = equip_vo.color_attr.special_flag
                        if select_color > data_color or (select_special_flag == 1 and data_special_flag == 0) then
                            key2_equip_count = key2_equip_count - 1
                        end
                    end
                end
            end

            local key0_flag = false
            local key1_flag = false
            local key2_flag = false
            if key0_equip_count >= require_same_count then
                key0_flag = true
                if break_through.same_id_color == break_through.same_node_type_color 
                    and break_through.same_id_star == break_through.same_node_type_star then
                    key1_equip_count = key1_equip_count - require_same_count
                end
                if break_through.same_id_color == break_through.same_big_type_color 
                    and break_through.same_id_star == break_through.same_big_type_star then
                    key2_equip_count = key2_equip_count - require_same_count
                end
            end

            if key1_equip_count >= require_node_type_count then
                key1_flag = true
                if break_through.same_node_type_color == break_through.same_big_type_color 
                    and break_through.same_node_type_star == break_through.same_big_type_star then
                    key2_equip_count = key2_equip_count - require_node_type_count
                end
            end

            if key2_equip_count >= require_type_count then
                key2_flag = true
            end

            if key0_flag and key1_flag and key2_flag then
                data.can_compose = true
            end
        end
    end
end

function HiddenWeaponWGData:GetBagLimit()
    return (self.cfg_shenji_equip.other[1] or {}).bag_limit or 250
end

--刻铭突破时，达到上限星级属性的数量要求
function HiddenWeaponWGData:GetRandBreakNum()
    return (self.cfg_shenji_equip.other[1] or {}).rand_attr_break_num or 10
end

--当前索引，最大刻铭突破
function HiddenWeaponWGData:GetMaxRandBreak(index)
    return (self.map_rand_attr_break[index] or {}).break_limit or 999999
end

function HiddenWeaponWGData:GetRandBreakCfg(index)
    return self.map_rand_attr_break[index]
end

--通过sub_type判断是否暗器软件
function HiddenWeaponWGData.IsHiddenWeapon(sub_type)
    return sub_type == GameEnum.E_TYPE_SHENJI
end

--仅仅获得装备配置
function HiddenWeaponWGData:GetOnlyEquipCfgById(item_id)
    return self.map_equip[item_id]
end

--获取装备部位名称,
function HiddenWeaponWGData:GetEquipPartStr(item_id)
    local cfg = self.map_equip[item_id]

    if not self.name_cfg[cfg.big_type] then
        return ""
    end

    if not self.name_cfg[cfg.big_type][cfg.node_type] then
        return ""
    end

    return self.name_cfg[cfg.big_type][cfg.node_type].name or ""
end

--升级配置
function HiddenWeaponWGData:GetUpLevelCfg(big_type, node_type, level)
    if not self.uplevel_cfg[big_type] then
        return nil
    end

    if not self.uplevel_cfg[big_type][node_type] then
        return nil
    end

    return self.uplevel_cfg[big_type][node_type][level]
end

--升阶配置
function HiddenWeaponWGData:GetUpgradeCfg(big_type, node_type, grade)
    if not self.upgrade_cfg[big_type] then
        return nil
    end

    if not self.upgrade_cfg[big_type][node_type] then
        return nil
    end

    return self.upgrade_cfg[big_type][node_type][grade]
end

--星级配置
function HiddenWeaponWGData:GetEquipStarAttr(big_type, color, star, flag)
    if not self.color_attr_cfg[big_type] then
        return nil
    end

    if not self.color_attr_cfg[big_type][color] then
        return nil
    end

    if not self.color_attr_cfg[big_type][color][star] then
        return nil
    end

    return self.color_attr_cfg[big_type][color][star][flag]
end

--技能配置
function HiddenWeaponWGData:GetSpecialSkill(skill_id, skill_level)
    if not self.special_skill_cfg[skill_id] then
        return nil
    end

    return self.special_skill_cfg[skill_id][skill_level]
end

function HiddenWeaponWGData:GetSkillInfoByLevel(skill_id, skill_type, skill_level)
    local skill_cfg
    if skill_type == HW_CONST_PARAM.ACTIVE_SKILL_TYPE then
        skill_cfg = self.active_skill_cfg[skill_id] and self.active_skill_cfg[skill_id][skill_level]
    elseif skill_type == HW_CONST_PARAM.SPECIAL_SKILL_TYPE then
        skill_cfg = self.special_skill_cfg[skill_id] and self.special_skill_cfg[skill_id][skill_level]
    elseif skill_type == HW_CONST_PARAM.PASSIVE_SKILL_TYPE then
        skill_cfg = self.passive_skill_cfg[skill_id] and self.passive_skill_cfg[skill_id][skill_level]
    end

    return skill_cfg
end

--根据skill_id和装备的color，star找到对应的配置
function HiddenWeaponWGData:GetSkillInfo(skill_id, color, star, type)
    local t = {}
    if type == 1 then
        t.skill_cfg = self.active_skill_cfg[skill_id]
        t.skill_type = HW_CONST_PARAM.ACTIVE_SKILL_TYPE
    elseif type == 2 then
        t.skill_cfg = self.special_skill_cfg[skill_id]
        t.skill_type = HW_CONST_PARAM.SPECIAL_SKILL_TYPE
    elseif type == 3 then
        t.skill_cfg = self.passive_skill_cfg[skill_id]
        t.skill_type = HW_CONST_PARAM.PASSIVE_SKILL_TYPE
    end

    t.max_cfg = t.skill_cfg[#t.skill_cfg]

    t.is_active = false
    t.cfg = t.skill_cfg[1]
    for i, v in ipairs(t.skill_cfg or {}) do
        if color >= v.need_color and star >= v.need_star then
            t.cfg = v
            t.is_active = true
        else
            break
        end
    end
    return t
end

--获取属性索引
function HiddenWeaponWGData:GetColorAttrIndexByTip(big_type)
    return self.map_color_attr_index[big_type]
end

--获得刻铭属性数量
function HiddenWeaponWGData:GetColorAttrCount(big_type)
    local cfg_list = self:GetColorAttrIndexByTip(big_type)
    local sum = 0
    for k, v in pairs(cfg_list) do
        sum = sum + 1
    end

    return sum
end

function HiddenWeaponWGData:GetTipMakeWay(item_id, special_flag, color, star)
    --print_error(item_id, special_flag, color, star)
    local list = {}
    local base_cfg = self:GetOnlyEquipCfgById(item_id)
    local now_cfg = self:GetBreakThrough(special_flag, color, star, false)
    local old_cfg = now_cfg
    local is_best = false
    if now_cfg == nil then--最顶级的装备只有个打造目标，没有 当前颜色星级的对应配置
        old_cfg = self:GetBreakThrough(special_flag, color, star, true)
        is_best = true
    end

    local cfg = now_cfg or old_cfg
    if cfg == nil then
        return list
    end

    table.insert(list,
        {is_cur = true, equip = {item_id = item_id, special_flag = special_flag, base_color = color, base_star = star},item_id = item_id}
    )
    -- table.insert(list, {})

    local last_cfg
    local equip
    for i = 1, 3 do
        --print_error(cfg.target_color, base_cfg.limit_color, cfg.target_star, base_cfg.limit_star)
        if not is_best and cfg and cfg.target_color <= base_cfg.limit_color and cfg.target_star <= base_cfg.limit_star then
            last_cfg = cfg
            cfg = self:GetBreakThrough(special_flag, cfg.target_color, cfg.target_star, false)--用target去寻找下一个配置
            if cfg then
                equip = self:GetTipMakeCfg(cfg, item_id, special_flag, false)
                table.insert(list, { equip = equip,item_id = item_id })
                -- table.insert(list, {})
            else
                local best_cfg = self:GetBreakThrough(special_flag, last_cfg.target_color, last_cfg.target_star, true)--上一个配置如果是最后一个配置，用target去形成顶级配置
                if best_cfg then
                    equip = self:GetTipMakeCfg(best_cfg, item_id, special_flag, true)
                    table.insert(list, { equip = equip,item_id = item_id })
                    -- table.insert(list, {})
                    cfg = nil--最好配置，停止往下寻找
                end
            end
        else
            old_cfg = self:GetBreakThrough(special_flag, old_cfg.color, old_cfg.star, not is_best)--最好配置要用次一级的配置去查看
            if old_cfg and old_cfg.color >= base_cfg.base_color and old_cfg.star >= base_cfg.base_star then
                equip = self:GetTipMakeCfg(old_cfg, item_id, special_flag, false)
                table.insert(list, 1, { equip = equip,item_id = item_id })
                if is_best then--最好的装备的配置没有单独一列，所以要特殊处理下
                    is_best = false
                    cfg = nil
                end
            else
                break
            end
        end
    end
    return list
end

function HiddenWeaponWGData:GetTipMakeCfg(cfg, item_id, special_flag, is_target)
    return {
        item_id = item_id,
        special_flag = special_flag,
        base_color = is_target and cfg.target_color or cfg.color,
        base_star = is_target and cfg.target_star or cfg.star
    }
end

--打造配置
function HiddenWeaponWGData:GetBreakThrough(special_flag, color, star, is_target)
    local cfg = is_target and self.map_target_cfg or self.map_now_cfg
    if not cfg[special_flag] then
        return nil
    end

    if not cfg[special_flag][color] then
        return nil
    end

    return cfg[special_flag][color][star]
end

function HiddenWeaponWGData:GetZLInfo(big_type)
    local equip_list = self:GetSCShenJiEquipGrid()
    local data = equip_list[big_type]
    if data.equip then
        return data.protocol_equip_item
    else
        return nil
    end
end

function HiddenWeaponWGData:GetZLCfgByBW(big_type, node_type)
    local big_cfg = self.uplevel_cfg[big_type]

    if not big_cfg then
        print_error("不存在该配置", big_type)
        return
    end

    local all_cfg = big_cfg[node_type]

    return all_cfg
end

function HiddenWeaponWGData:GetZLCfg(big_type, node_type, lv)
    local all_cfg = self:GetZLCfgByBW(big_type, node_type)
    return all_cfg[lv]
end

function HiddenWeaponWGData:GetZLAttrCfg(big_type, node_type, lv)
    local all_cfg = self:GetZLCfgByBW(big_type, node_type)

    local attr_data = {}

    local cfg

    if lv == 0 then
        cfg = all_cfg[1]
    else
        cfg = all_cfg[lv]
    end

    return AttributeMgr.GetAttributteByClass(cfg)
end

function HiddenWeaponWGData:GetZLAttrMaxLv(big_type, node_type)
    local all_cfg = self:GetZLCfgByBW(big_type, node_type)
    return #all_cfg
end

function HiddenWeaponWGData:GetCurZLLastProgress(big_type, node_type)
end

function HiddenWeaponWGData:GetJJAttrCfg(big_type, node_type, cur_grade)
    if cur_grade == 0 then
        cur_grade = 1
    end
    local cfg = self:GetUpgradeCfg(big_type, node_type, cur_grade)
    return AttributeMgr.GetAttributteByClass(cfg)
end

function HiddenWeaponWGData:IsCanUpGrade(big_type, cur_lv)
    local data = self:GetSCShenJiEquipGridByType(big_type)
    local equip = data.equip
    if equip == nil then
        return false
    end
    local node_type = equip.node_type
    local protocol_equip_item = data.protocol_equip_item
    cur_lv = cur_lv or protocol_equip_item.level
    local cur_grade = protocol_equip_item.grade
    local max_lv = self:GetZLAttrMaxLv(big_type, node_type)
    local is_max = (cur_lv >= max_lv)

    local next_cfg
    if is_max then
        next_cfg = self:GetZLCfg(big_type, node_type, cur_lv)
    else
        next_cfg = self:GetZLCfg(big_type, node_type, cur_lv + 1)
    end
    if next_cfg == nil then
        return false
    end
    local next_grade = next_cfg.need_upgrade_level

    return next_grade ~= cur_grade
end

--获取当前的阶级
function HiddenWeaponWGData:GetCurTypeGrade(big_type)
    local data = self:GetSCShenJiEquipGridByType(big_type)
    local protocol_equip_item = data.protocol_equip_item
    local cur_grade = protocol_equip_item.grade
    return cur_grade
end

-- 星级属性顺位
function HiddenWeaponWGData:GetColorAttrIndex(weapon_type)
    local index_objs = self.map_color_attr_index[weapon_type] or {}
    local indexs = {}
    for index, value in ipairs(index_objs) do
        table.insert(indexs, value.attr)
    end
    return indexs
end

function HiddenWeaponWGData:CalSkillCfg(cfg_list, skill_id, color, star)
    if not cfg_list then
        return nil
    end

    local skill_cfg = cfg_list[skill_id]
    if not skill_cfg then
        print_error("不存在该技能配置")
        return nil
    end

    local max_color = 0
    local max_star = 0
    for i = color, 1, -1 do
        local color_cfg = skill_cfg[i]
        if color_cfg and max_color == 0 then
            max_color = i
        end

        if color_cfg then
            for k, v in pairs(color_cfg) do
                if k <= star and max_star < k then
                    max_star = k
                elseif color > max_color and max_star < k then
                    max_star = k
                end
            end
        end

        if max_star == 0 then --没有颜色 对应的星数
            max_color = 0
        end
    end

    -- print_error("===11======", skill_id, color, star)
    -- print_error("===222======", skill_id, max_color, max_star)
    if not skill_cfg[max_color] or not skill_cfg[max_color][max_star] then
        local min_color = 99999
        local min_star = 9999

        for c, _ in pairs(skill_cfg) do
            if min_color > c then
                min_color = c
            end
        end

        local color_cfg = skill_cfg[min_color]
        for s, _ in pairs(color_cfg) do
            if min_star > s then
                min_star = s
            end
        end

        return color_cfg[min_star], false
    end

    return skill_cfg[max_color][max_star], true
end

function HiddenWeaponWGData:GetSkillCfg(skill_type, skill_id, color, star)
    if not skill_id then
        return nil
    end

    local cfg_list = nil
    if skill_type == 1 then
        cfg_list = self.active_skill_cfg_ics
    elseif skill_type == 2 then
        cfg_list = self.passive_skill_cfg_ics
    elseif skill_type == 3 then
        cfg_list = self.special_skill_cfg_ics
    end

    local cfg, is_active = self:CalSkillCfg(cfg_list, skill_id, color, star)
    local max_cfg = self:CalMaxSkillCfg(cfg_list, skill_id, color, star)
    -- print_log("===is_active======", skill_id, is_active)
    return cfg, is_active, max_cfg
end

function HiddenWeaponWGData:CalMaxSkillCfg(cfg_list, skill_id, color, star)
    if not cfg_list then
        return nil
    end

    local skill_cfg = cfg_list[skill_id]
    if not skill_cfg then
        print_error("不存在该技能配置")
        return nil
    end

    local max_lv = 0
    local cfg = nil
    for _, color_cfg in pairs(skill_cfg) do
        for _, v in pairs(color_cfg) do
            if v.skill_level > max_lv then
                max_lv = v.skill_level
                cfg = v
            end
        end
    end

    return cfg
end

function HiddenWeaponWGData:GetSkillMaxLvCfg(skill_type, skill_id, color, star)
    if not skill_id then
        return nil
    end

    local cfg_list = nil
    if skill_type == 1 then
        cfg_list = self.active_skill_cfg_ics
    elseif skill_type == 2 then
        cfg_list = self.passive_skill_cfg_ics
    elseif skill_type == 3 then
        cfg_list = self.special_skill_cfg_ics
    end
    return self:CalMaxSkillCfg(cfg_list, skill_id, color, star)
end

-- 是否有可分解道具（感觉会做红点）
function HiddenWeaponWGData:IsDataContainsDecompose()
    local protocol_list = HiddenWeaponWGData.Instance:GetSCShenJiEquipBag()
    local has_anqi_info = self.anqi_info.item_id > 0
    local has_ruanjia_info = self.ruanjia_info.item_id > 0
    if not has_anqi_info or not has_ruanjia_info then
        return false
    end
    local has_decompose = false
    for index, value in pairs(protocol_list) do
        -- local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(value.item_id)
        -- if value.item_id > 0 and value.color <= GameEnum.ITEM_COLOR_GREEN then
        --    has_decompose = true
        -- end

        -- if has_anqi_info and has_decompose and cfg and cfg.big_type == 1 then
        --     return true
        -- end

        -- if has_ruanjia_info and has_decompose and cfg and cfg.big_type == 2 then
        --     return true
        -- end
        if value.item_id > 0 and value.color <= GameEnum.ITEM_COLOR_GREEN then
            return true
        end
    end
    return false
end

-- 刻铭道具消耗
function HiddenWeaponWGData:GetKemingConsumeCfg(weapon_type)
    for index, cfg in ipairs(self.cfg_shenji_equip.rand_attr_consume) do
        if weapon_type == cfg.big_type then
            return cfg
        end
    end
end

function HiddenWeaponWGData:GetAnQiExpItem()
    return (self.cfg_shenji_equip.other[1] or {}).anqi_exp_item
end

function HiddenWeaponWGData:GetRuanJiaExpItem()
    return (self.cfg_shenji_equip.other[1] or {}).lingjia_exp_item
end

-- 获取注灵属性加成
function HiddenWeaponWGData:GetZLAdd(weapon_type)
    local data = self:GetSCShenJiEquipGridByType(weapon_type)
    local equip = data.equip
    local protocol_equip_item = data.protocol_equip_item
    if equip == nil or protocol_equip_item == nil then
        return {}
    end
    local all_cfg = self:GetZLCfgByBW(equip.big_type, equip.node_type)
    if protocol_equip_item.level == 0 then
        return {}
    else
        return all_cfg[protocol_equip_item.level]
    end
end

-- 获取当前刻铭百分比
function HiddenWeaponWGData:GetRandPercent(weapon_type)
    if weapon_type == 2 then
        return (self.ruanjia_info or {}).rand_attr_list or {}
    else
        return (self.anqi_info or {}).rand_attr_list or {}
    end
end

function HiddenWeaponWGData:GetRemindManager()
    return self.remind_manager
end

function HiddenWeaponWGData:isNeedTupo(protocol_item)
    local break_num = HiddenWeaponWGData.Instance:GetRandBreakNum()
    local max_break = HiddenWeaponWGData.Instance:GetMaxRandBreak(protocol_item.max_rand_index)
    local break_count = 0
    for index, value in ipairs(protocol_item.rand_attr_list) do
        if value >= max_break then
            break_count = break_count + 1
        end
    end
    if break_count >= break_num then
        return true
    else
        return false
    end
end

-- 判断装备是否能够刻铭
function HiddenWeaponWGData:CanEquipKm(data)
    if data == nil or data.equip == nil then
        return false
    end
    -- 刻铭的最低品质要求
    local limit = (self.cfg_shenji_equip.other[1] or {}).rand_attr_color_limit or 1
    return (data.equip.base_color or 0) >= limit
end

-- 刻铭是否达到上限
function HiddenWeaponWGData:isKmMax(protocol_item)
    if protocol_item and protocol_item.max_rand_index then
        local max_break_cfg = self.map_rand_attr_break[#self.map_rand_attr_break] or {}
        local max_break = max_break_cfg.break_limit or 999999
        local rand_max_num = 0
        local is_max_index = protocol_item.max_rand_index >= max_break_cfg.rand_index
        for index, value in ipairs(protocol_item.rand_attr_list) do
            local not_limit = value < max_break
            rand_max_num = not_limit and rand_max_num or rand_max_num + 1
            if not is_max_index and not_limit then
                return false
            end
        end

        if protocol_item.max_rand_index == max_break_cfg.rand_index then
            return rand_max_num >= self:GetRandBreakNum()
        else
            return true
        end

    end

    return false
end

-- 返回强化相关的材料id
function HiddenWeaponWGData:GetMaterialMap()
    local map = {}
    map[29850] = true
    map[29851] = true
    map[29852] = true
    map[29853] = true
    map[29854] = true
    map[29855] = true
    return map
end

function HiddenWeaponWGData:GetZLAddAttr(data)
    local add_attr = {}
    if IsEmptyTable(data) then return add_attr end
    local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(data.item_id)
    --注灵升级
    local level = data.protocol_equip_item and data.protocol_equip_item.level
    local zl_attr_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(cfg.big_type, cfg.node_type, level)
    --注灵进阶
    local grade = data.protocol_equip_item and data.protocol_equip_item.grade
    local jj_attr_cfg = HiddenWeaponWGData.Instance:GetUpgradeCfg(cfg.big_type, cfg.node_type, grade)
    add_attr = AttributeMgr.AddAttributeAttr(AttributeMgr.GetAttributteByClass(jj_attr_cfg), AttributeMgr.GetAttributteByClass(zl_attr_cfg))
    return add_attr
end

function HiddenWeaponWGData:GetShowEquipStarAttr(data)
    local star_sttr = {}
    if IsEmptyTable(data) then return star_sttr end
    local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(data.item_id)
    local equip = {
        big_type = cfg.big_type,
        node_type = cfg.node_type,
        base_star = data.equip and data.equip.base_star or cfg.base_star,
        base_color = data.equip and data.equip.base_color or cfg.base_color,
        special_flag = data.equip and data.equip.special_flag or cfg.special_flag
    }
    star_sttr = HiddenWeaponWGData.Instance:GetEquipStarAttr(equip.big_type, equip.base_color, equip.base_star, equip.special_flag)
    return star_sttr
end

function HiddenWeaponWGData:GetShowEquipBaseAttr(data)
    local base_attr = {}
    if IsEmptyTable(data) then return base_attr end
    local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(data.item_id)
    local equip = {
        big_type = cfg.big_type,
        node_type = cfg.node_type,
        base_star = data.equip and data.equip.base_star or cfg.base_star,
        base_color = data.equip and data.equip.base_color or cfg.base_color,
        special_flag = data.equip and data.equip.special_flag or cfg.special_flag
    }
    local base_cfg = HiddenWeaponWGData.Instance:GetEquipBaseAttr(equip)
    base_attr = AttributeMgr.GetAttributteByClass(base_cfg)
    return base_attr
end

function HiddenWeaponWGData:GetShenJiSkillData(data)
    local data_list = {}
    local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(data.item_id)
    local cfg_list = {"active_skill_list", "special_skill_list", "passive_skill_list"}
    local id_list, list_str
    local list_data
    local equip = data.equip or {}
    local color = equip.base_color or cfg.base_color
    local star = equip.base_star or cfg.base_star

    data_list.title = Language.ShenJiEquip.SkillTitle
    data_list.skill_list_1 = {}
    data_list.skill_list_2 = {}
    data_list.skill_list_3 = {}
    local skill_group = 1
    local title_img_list = {"a3_sl_bq_sx", "a3_sl_bq_bd"}
    for i, v in ipairs(cfg_list) do
        if cfg[v] ~= "" then
            skill_group = i == 3 and 2 or 1
            id_list = Split(cfg[v], "|")
            list_data = {}
            for m, n in pairs(id_list) do
                list_data = {}
                local skill_id = tonumber(n)
                local skill_table = HiddenWeaponWGData.Instance:GetSkillInfo(skill_id, color, star,i)
                local skill_icon = skill_table and skill_table.cfg and skill_table.cfg.skill_icon or 213
                local bundle, asset = ResPath.GetSkillIconById(skill_icon)
                list_data.bundle = bundle
                list_data.asset = asset
                list_data.color = color
                list_data.click_func = BindTool.Bind(self.ClickSkillFunc, self, skill_table)
                table.insert(data_list["skill_list_" .. skill_group], list_data)
            end

            data_list["skill_list_" .. skill_group].title_img = title_img_list[skill_group]
        end
    end

    -- if data.is_virtual then
    --     data_list.title = Language.HiddenWeapon.TypeName[cfg.big_type] .. Language.Tip.HiddenSkill .. Language.Tip.HiddenTitle1
    -- else
    --     data_list.title = Language.HiddenWeapon.TypeName[cfg.big_type] .. Language.Tip.HiddenSkill
    -- end
    return data_list
end

function HiddenWeaponWGData:ClickSkillFunc(skill_table)
    if IsEmptyTable(skill_table) then
        return
    end
    local data = {
        cfg = skill_table.cfg,
        is_active = skill_table.is_active,
        skill_type = skill_table.skill_type,
        max_cfg = skill_table.max_cfg,
    }
    HiddenWeaponWGCtrl.Instance:ShowEquipSkillView(data)
end

function HiddenWeaponWGData:GetCurItemCap(equip_vo)
    local base_attr_obj = AttributeMgr.GetAttributteByClass(equip_vo.base_attr.origin_data)
    local pro = (equip_vo.color_attr.base_attr_per or 0) / 10000


    -- local color_attr_obj = AttributeMgr.GetAttributteByClass(equip_vo.color_attr.origin_data)
    -- ClassPrint("D2", "D2>>>>>>>>color_attr_obj",color_attr_obj)
    local zy_attrs = HiddenWeaponWGData.Instance:GetZLAdd(equip_vo.equip.big_type) or {}
    local zl_attr_obj = AttributeMgr.GetAttributteByClass(zy_attrs)
    -- zl_attr_obj = AttributeMgr.MulAttribute(zl_attr_obj, (1 + pro))

    local rand_percent = HiddenWeaponWGData.Instance:GetRandPercent(equip_vo.equip.big_type)
    local attrs_index = HiddenWeaponWGData.Instance:GetColorAttrIndex(equip_vo.equip.big_type)
    local star_attrs = equip_vo.color_attr
    local star_attr_tab = {}
    for index, attr_name in ipairs(attrs_index) do
        local percent = (rand_percent[index] or 0) / 10000
        if attr_name == "base_attr_per" then
            local per = math.ceil(star_attrs[attr_name] / 10000 * percent)
            pro = pro + per
        else
            -- star_attr_tab[attr_name] = math.ceil(star_attrs[attr_name] * (1 + pro)) or 0
            -- star_attr_tab[attr_name] = star_attr_tab[attr_name] + math.ceil(star_attrs[attr_name] * (1 + percent))
            star_attr_tab[attr_name] = math.ceil(star_attrs[attr_name] * (1 + percent))
        end
    end

    base_attr_obj = AttributeMgr.MulAttribute(base_attr_obj, (1 + pro))
    local star_attrs_obj = AttributeMgr.GetAttributteByClass(star_attr_tab)
    local add_attr = AttributeMgr.AddAttributeAttr(base_attr_obj, star_attrs_obj)
    add_attr = AttributeMgr.AddAttributeAttr(add_attr, zl_attr_obj)

    local awaken_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(equip_vo.equip.big_type)

    local protocol_equip_item = equip_vo.protocol_equip_item


    local equip = equip_vo.equip
    local color = equip.base_color
    local star = equip.base_star

    local active_skill_list = string.split(equip.active_skill_list, "|")

    local passive_skill_list = string.split(equip.passive_skill_list, "|")

    local special_skill_list = string.split(equip.special_skill_list, "|")

    local cap = AttributeMgr.GetCapability(add_attr)

    local cur_awaken_cfg = awaken_cfg[protocol_equip_item.special_effect_level]

    if awaken_cfg ~= nil then
        for k,v in pairs(awaken_cfg) do
            if v.effect_level == protocol_equip_item.special_effect_level then
                cur_awaken_cfg = v
                break
            end
        end
    end

    local awaken_attr = AttributeMgr.GetAttributteByClass(cur_awaken_cfg)
    local awaken_skill_cap = AttributeMgr.GetCapability(awaken_attr, cur_awaken_cfg)
    cap = cap + awaken_skill_cap

    for k, v in pairs(active_skill_list) do
        local skill_id = tonumber(active_skill_list[k])
        local cfg, is_active, max_cfg = HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.ACTIVE_SKILL_TYPE, skill_id, color, star)
        if is_active then
            local skill_attr = AttributeMgr.GetAttributteByClass(cfg)
            local skill_cap = AttributeMgr.GetCapability(skill_attr, cfg)
            cap = cap + skill_cap
        end
    end

    for k, v in pairs(passive_skill_list) do
        local skill_id = tonumber(passive_skill_list[k])
        local cfg, is_active, max_cfg = HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.PASSIVE_SKILL_TYPE, skill_id, color, star)
        if is_active then
            local skill_attr = AttributeMgr.GetAttributteByClass(cfg)
            local skill_cap = AttributeMgr.GetCapability(skill_attr, cfg)
            cap = cap + skill_cap
        end
    end

    for k, v in pairs(special_skill_list) do
        local skill_id = tonumber(special_skill_list[k])
        local cfg, is_active, max_cfg = HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.SPECIAL_SKILL_TYPE, skill_id, color, star)
        if is_active then
             local skill_attr = AttributeMgr.GetAttributteByClass(cfg)
            local skill_cap = AttributeMgr.GetCapability(skill_attr, cfg)
            cap = cap + skill_cap
        end
    end
end

function HiddenWeaponWGData:GetPrintLog()
    local tab_str = "HiddenWeaponWGData:GetPrintLog\n"
    local is_open1 = FunOpen.Instance:GetFunIsOpened("sixiang_call_sx")
    --local is_open2 = FunOpen.Instance:GetFunIsOpened("sixiang_call_xqzl")
    local is_open3 = FunOpen.Instance:GetFunIsOpened("SiXiangCallView")
    tab_str = tab_str .. "sixiang_call_sx:" .. tostring(is_open1) .. "\n"
    --tab_str = tab_str .. "sixiang_call_xqzl:" .. tostring(is_open2) .. "\n"
    tab_str = tab_str .. "SiXiangCallView:" .. tostring(is_open3) .. "\n"

    return tab_str
end


function HiddenWeaponWGData:SaveMutiSelect(index, is_on)
    if not self.select_muti_list then
        self.select_muti_list = {}
    end
    self.select_muti_list[index] = is_on
end

function HiddenWeaponWGData:ResetMutiSelect()
    self.select_muti_list = {}
end

function HiddenWeaponWGData:GetMutiSelect()
    if not self.select_muti_list then
        self.select_muti_list = {}
    end
    return self.select_muti_list
end

function HiddenWeaponWGData:GetMutiSelectIndex(index)
    if not self.select_muti_list then
        self.select_muti_list = {}
    end
    return self.select_muti_list[index]
end

-- 获取仙器战力
function HiddenWeaponWGData:GetItemCapability(item_id)
    local capability = 0
    local total_cfg = self:GetEquipCfgById(item_id)
    if IsEmptyTable(total_cfg) then
        return capability
    end

    local base_attribute = AttributePool.AllocAttribute()
    local base_attr = self:GetEquipBaseAttr(total_cfg)
    local color_attr = self:GetEquipColorAttr(total_cfg)
    local base_attr_per = (color_attr and color_attr.origin_data) and color_attr.origin_data.base_attr_per * 0.0001 or 0

    -- 基础
    if not IsEmptyTable(base_attr) then
        for k,v in pairs(base_attr.origin_data) do
            local attr_str = AttributeMgr.GetAttributteKey(k)
            if base_attribute[attr_str] ~= nil and v > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + v * (1 + base_attr_per)
            end
        end
    end

    -- 星级
    if not IsEmptyTable(color_attr) then
        for k,v in pairs(color_attr.origin_data) do
            local attr_str = AttributeMgr.GetAttributteKey(k)
            if base_attribute[attr_str] ~= nil and v > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + v
            end
        end
    end

    local color = total_cfg.base_color
    local star = total_cfg.base_star
    local active_skill_list = string.split(total_cfg.active_skill_list, "|")
    local passive_skill_list = string.split(total_cfg.passive_skill_list, "|")
    local special_skill_list = string.split(total_cfg.special_skill_list, "|")

    local skill_id = 0
    local cfg, is_active, max_cfg
    local temp_skill_cfg = {attack_power = 0, defence_power = 0, capability_inc = 0}
    local function calc_skill_cfg(skill_cfg)
        if IsEmptyTable(skill_cfg) then
            return
        end

        for k,v in pairs(temp_skill_cfg) do
            if skill_cfg[k] then
                temp_skill_cfg[k] = v + skill_cfg[k]
            end
        end
    end

    -- 主动
    for k, v in pairs(active_skill_list) do
        skill_id = tonumber(active_skill_list[k])
        cfg, is_active, max_cfg = self:GetSkillCfg(HW_CONST_PARAM.ACTIVE_SKILL_TYPE, skill_id, color, star)
        if is_active then
            calc_skill_cfg(cfg)
        end
    end

    -- 被动
    for k, v in pairs(passive_skill_list) do
        skill_id = tonumber(passive_skill_list[k])
        cfg, is_active, max_cfg = self:GetSkillCfg(HW_CONST_PARAM.PASSIVE_SKILL_TYPE, skill_id, color, star)
        if is_active then
            calc_skill_cfg(cfg)
        end
    end

    -- 专属
    for k, v in pairs(special_skill_list) do
        skill_id = tonumber(special_skill_list[k])
        cfg, is_active, max_cfg = self:GetSkillCfg(HW_CONST_PARAM.SPECIAL_SKILL_TYPE, skill_id, color, star)
        if is_active then
            calc_skill_cfg(cfg)
        end
    end

    -- 觉醒
    local awaken_cfg = self:GetAwakenCfgData(total_cfg.big_type)
    if awaken_cfg and awaken_cfg[1] then
        calc_skill_cfg(cfg)
    end

    capability = AttributeMgr.GetCapability(base_attribute, temp_skill_cfg)
    return capability
end

function HiddenWeaponWGData:GetRLBagList()
    local bag_list = self:GetDataList()
    local rl_bag_list = {}
    local bag_index_list = {}
    for k,v in pairs(bag_list) do
        if v.color_attr.origin_data.color > 1 then
            local list = {}
            list.cfg = v
            list.index = v.index
            bag_index_list[v.index] = false
            rl_bag_list[#rl_bag_list + 1] = list
        end
    end

    local rl_list = {}
    for k,v in pairs(rl_bag_list) do
        local item_list, index_list = self:GetRLMaterialList(v, bag_index_list)
        if not IsEmptyTable(item_list) then
            local data = {}
            data.material_item_list = item_list
            data.material_index_list = index_list
            data.cur_index = v.index
            data.cur_item = v
            rl_list[#rl_list + 1] = data
        end
    end

    return rl_list
end

function HiddenWeaponWGData:GetRLMaterialList(data, bag_index_list)
    local bag_index = data.index
    local target_vo, condition = self:GetComposeTargetEquip(data.cfg)
    if condition == nil or bag_index_list[bag_index] then
        return
    end

    local limit = condition.role_level_limit or 0
    local level = RoleWGData.Instance:GetRoleLevel()
    if level < limit then
        return
    end
    local index_list = {}
    for i=1, 4 do
        table.insert(index_list, -1)
    end

    local item_list = {}
    local num = 0
    local same_node_type_num = 0
    local same_big_type_num = 0
    local same_id_num = 0
    local all_num = condition.same_id_num + condition.same_node_type_num + condition.same_big_type_num
    local same_id_data_list = {}
    local same_node_data_list = {}
    local same_big_data_list = {}
    local pairs = pairs
    if condition.same_id_num > 0 then
        same_id_data_list =
            self:GetDataListByCondition(
            target_vo.equip.big_type,
            bag_index,
            target_vo.item_id,
            condition.same_id_color,
            condition.same_id_star,
            target_vo.equip.node_type
        )
    end
    for k,v in pairs(same_id_data_list) do
        if not bag_index_list[v.index] and condition.same_id_num > same_id_num then
            bag_index_list[v.index] = true
            table.insert(item_list, v)
            num = num + 1
            same_id_num = same_id_num + 1
            index_list[num] = v.index
        end
    end

    if condition.same_node_type_num > 0 then
        same_node_data_list =
            self:GetDataListByCondition(
            target_vo.equip.big_type,
            bag_index,
            nil,
            condition.same_node_type_color,
            condition.same_node_type_star,
            target_vo.equip.node_type
        )
    end
   
    for k,v in pairs(same_node_data_list) do
        if not bag_index_list[v.index] and condition.same_node_type_num > same_node_type_num then
            bag_index_list[v.index] = true
            table.insert(item_list, v)
            num = num + 1
            same_node_type_num = same_node_type_num + 1
            index_list[num] = v.index
        end
    end

    if condition.same_big_type_num > 0 then
        same_big_data_list =
            self:GetDataListByCondition(
            target_vo.equip.big_type,
            bag_index,
            nil,
            condition.same_big_type_color,
            condition.same_big_type_star,
            nil
        )
    end
   
    for k,v in pairs(same_big_data_list) do
        if not bag_index_list[v.index] and condition.same_big_type_num > same_big_type_num then
            bag_index_list[v.index] = true
            table.insert(item_list, v)
            num = num + 1
            same_big_type_num = same_big_type_num + 1
            index_list[num] = v.index
        end
    end

    if num < all_num then
        for k,v in pairs(item_list) do
            bag_index_list[v.index] = false
        end

        return {}, {}
    else
        bag_index_list[bag_index] = true
    end

    return item_list, index_list
end