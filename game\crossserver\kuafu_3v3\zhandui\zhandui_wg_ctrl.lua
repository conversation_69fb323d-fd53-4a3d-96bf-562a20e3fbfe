require("game/crossserver/kuafu_3v3/zhandui/zhandui_wg_data")
require("game/crossserver/kuafu_3v3/kf_3v3_zhandui_rank_view")
require("game/crossserver/kuafu_3v3/kf_3v3_season_reward_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_be_invite_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_create_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_join_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_zhanling_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_apply_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_invite_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_handle_apply_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_change_name_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_change_notice_view")
require("game/crossserver/kuafu_3v3/zhandui/zhandui_talk_view")

--3v3战队
ZhanDuiWGCtrl = ZhanDuiWGCtrl or BaseClass(BaseWGCtrl)

function ZhanDuiWGCtrl:__init()
	if nil ~= ZhanDuiWGCtrl.Instance then
		ErrorLog("[ZhanDuiWGCtrl]:Attempt to create singleton twice!")
	end
	ZhanDuiWGCtrl.Instance = self

	self.data = ZhanDuiWGData.New()
	self.zhandui_view = KF3V3ZhanDuiView.New(GuideModuleName.KF3V3ZhanDuiView)
	self.zhandui_sw_view = KF3V3SeasonRewardView.New(GuideModuleName.KF3V3SeasonRewardView)
	self.zhandui_rank_view = KF3V3ZhanDuiRankView.New(GuideModuleName.KF3V3ZhanDuiRankView)
	self.zhandui_be_invite_view = ZhanDuiBeInviteView.New(GuideModuleName.ZhanDuiBeInviteView)          --自己被邀请加入战队界面
	self.zhandui_create_view = ZhanDuiCreateView.New(GuideModuleName.ZhanDuiCreateView)                 --创建战队界面
	self.zhandui_zhanling_view = ZhanDuiZhanLingView.New(GuideModuleName.ZhanDuiZhanLingView)           --战队战令界面
	self.zhandui_join_view = ZhanDuiJoinView.New(GuideModuleName.ZhanDuiJoinView)                       --加入战队界面
	self.zhandui_apply_view = ZhanDuiApplyView.New(GuideModuleName.ZhanDuiApplyView)                    --别人申请加入战队界面
	self.zhandui_invite_view = ZhanDuiInviteView.New(GuideModuleName.ZhanDuiInviteView)                 --邀请别人加入战队界面
	self.zhandui_change_notice_view = ZhanDuiChangeNoticeView.New(GuideModuleName.ZhanDuiChangeNoticeView) --修改公告界面
	self.zhandui_change_name_view = ZhanDuiChangeNameView.New(GuideModuleName.ZhanDuiChangeNameView)    --修改名字界面
	self.zhandui_talk_view = ZhanDuiTalkView.New()                                                      --战队喊话界面
	self:RegisterAllProtocols()

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
	self:Bind(OtherEventType.ROLE_ONLINE_CHANGE, BindTool.Bind1(self.OnOtherRoleOnlineChange, self))
end

function ZhanDuiWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.zhandui_view then
		self.zhandui_view:DeleteMe()
		self.zhandui_view = nil
	end

	if self.zhandui_sw_view then
		self.zhandui_sw_view:DeleteMe()
		self.zhandui_sw_view = nil
	end

	if self.zhandui_rank_view then
		self.zhandui_rank_view:DeleteMe()
		self.zhandui_rank_view = nil
	end

	if self.zhandui_be_invite_view then
		self.zhandui_be_invite_view:DeleteMe()
		self.zhandui_be_invite_view = nil
	end
	if self.zhandui_create_view then
		self.zhandui_create_view:DeleteMe()
		self.zhandui_create_view = nil
	end
	if self.zhandui_join_view then
		self.zhandui_join_view:DeleteMe()
		self.zhandui_join_view = nil
	end
	if self.zhandui_zhanling_view then
		self.zhandui_zhanling_view:DeleteMe()
		self.zhandui_zhanling_view = nil
	end
	if self.zhandui_apply_view then
		self.zhandui_apply_view:DeleteMe()
		self.zhandui_apply_view = nil
	end
	if self.zhandui_invite_view then
		self.zhandui_invite_view:DeleteMe()
		self.zhandui_invite_view = nil
	end
	if self.zhandui_change_notice_view then
		self.zhandui_change_notice_view:DeleteMe()
		self.zhandui_change_notice_view = nil
	end
	if self.zhandui_change_name_view then
		self.zhandui_change_name_view:DeleteMe()
		self.zhandui_change_name_view = nil
	end
	if self.zhandui_talk_view then
		self.zhandui_talk_view:DeleteMe()
		self.zhandui_talk_view = nil
	end

	ZhanDuiWGCtrl.Instance = nil
end

function ZhanDuiWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSGroupOpera)                                            -- 6910
	self:RegisterProtocol(CSGroupModifyNotice)                                     -- 修改团队公告 6911
	self:RegisterProtocol(CSGroupModifyName)                                       -- 修改团队名字 6912
	self:RegisterProtocol(CSZhandui3v3Opera)                                       -- 6924
	self:RegisterProtocol(CSZhandui3v3OneKeyAppliedJoin)                           -- 一键申请加入战队 6930

	self:RegisterProtocol(SCZhanduiRoleInfo, "OnSCZhanduiRoleInfo")                -- 战队中自己的信息 6931
	self:RegisterProtocol(SCZhandui3V3Info, "OnSCZhandui3V3Info")                  -- 3V3战队信息 6921
	self:RegisterProtocol(SCZhandui3V3InfoList, "OnSCZhandui3V3InfoList")          -- 3V3战队信息列表 6920
	self:RegisterProtocol(SCZhandui3v3AppliedZhandui, "OnSCZhandui3v3AppliedZhandui") -- 已申请的战队列表 6929

	self:RegisterProtocol(SCZhandui3V3ApplyJoinInfo, "OnSCZhandui3V3ApplyJoinInfo") -- 3v3战队申请信息 6923
	self:RegisterProtocol(SCZhandui3V3ApplyJoinList, "OnSCZhandui3V3ApplyJoinList") -- 3v3战队申请信息列表 6922

	self:RegisterProtocol(SCZhandui3v3NewLog, "OnSCZhandui3v3NewLog")              -- 3v3战队新日志 6925
	self:RegisterProtocol(SCZhandui3v3AllLog, "OnSCZhandui3v3AllLog")              -- 3v3战队全部日志 6926

	self:RegisterProtocol(SZZhandui3v3NewInvite, "OnSZZhandui3v3NewInvite")        -- 3v3战队新邀请(被邀请) 6928
	self:RegisterProtocol(SCZhandui3v3InviteList, "OnSCZhandui3v3InviteList")      -- 3v3战队邀请列表(被邀请) 6927

	self:RegisterProtocol(SCZhanduiInfoChangeNotify, "OnSCZhanduiInfoChangeNotify") -- 3v3战队邀请列表(被邀请) 6927
	self:RegisterProtocol(SCZhanduiNewZhanDuiLingPai, "OnSCZhanduiNewZhanDuiLingPai") -- 3v3战队战令 6932
end

function ZhanDuiWGCtrl:CloseZhanDuiView()
	if self.zhandui_view:IsOpen() then
		self.zhandui_view:Close()
	end
end

function ZhanDuiWGCtrl:OpenZhanDuiRankView()
	self.zhandui_rank_view:Open()
end

--打开赛季奖励界面
function ZhanDuiWGCtrl:OpenSeasonReward()
	self.zhandui_sw_view:Open()
end

function ZhanDuiWGCtrl:GetSeasonRewardTabType()
	if self.zhandui_sw_view:IsOpen() then
		return self.zhandui_sw_view:GetSeasonRewardTabType()
	end

	return 1
end

--协议操作 start

--战队中自己的信息
function ZhanDuiWGCtrl:OnSCZhanduiRoleInfo(protocol)
	self.data:SetRoleInfo(protocol)
	GlobalEventSystem:Fire(OtherEventType.KF3V3ZhanDuiRoleInfoChnage)
	RemindManager.Instance:Fire(RemindName.ActJjcArena)
	RemindManager.Instance:Fire(RemindName.ActPVPbArena)
	RemindManager.Instance:Fire(RemindName.KF3V3)
end

--战队信息返回
function ZhanDuiWGCtrl:OnSCZhandui3V3Info(protocol)
	--print_error("战队信息返回>>>>>>>>>>", protocol)
	self.data:SetZhanDuiInfo(protocol.zhandui_info, protocol.member_list)

	--这里只处理关闭界面
	if protocol.notify_reason == NotifyZhanduiInfoReason.JoinZhandui then -- 玩家自己加入战队
		--关闭加入界面、创建界面，打开战队信息界面
		self:CloseJoinView()
		self:CloseCreateView()
		self:CloseBeInviteView()
		-- 播放段位动画
		local data = {}
		data.data_type = EnumDuanweiChangeType.Cur
		data.new_score = protocol.zhandui_info.score
		KF3V3WGCtrl.Instance:OpenDuanWeiChangeView(data)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_BE_INVITE, 0)

		----创建新战队修改默认战令名字
		--if self.data:GetIsZhanDuiCaptain() and self.data:GetZhanDuiMemberCount() == 1 then
		--	self:ModifyZhanDuiZhanLingText(Language.ZhanDui.DefaultZhanLingName)
		--end
	elseif protocol.notify_reason == NotifyZhanduiInfoReason.ModifyName then     -- 修改名字
		self:CloseChangeNameView()
	elseif protocol.notify_reason == NotifyZhanduiInfoReason.ModifyNotice then   -- 修改公告
		self:CloseChangeNoticeView()
	elseif protocol.notify_reason == NotifyZhanduiInfoReason.ChangeLingpaiName then -- 修改战令名字
		self:CloseZhanLingView()
	elseif protocol.notify_reason == NotifyZhanduiInfoReason.LeaveZhandui
		or protocol.notify_reason == NotifyZhanduiInfoReason.BeKicked then -- 主动离开战队、获取被踢出
		self.data:ClearApply()
		self.data:ClearLog()
		self.data:ClearZhanDuiData()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE_START_CROSS_3V3, 0)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_NEW_APPLT, 0)
		self:CloseZhanDuiView()
	elseif protocol.notify_reason == NotifyZhanduiInfoReason.AgreeJoinZhandui then -- 同意加入战队（申请，然后被同意，这种情况不弹出面板）
		self:CloseBeInviteView()
		self:CloseJoinView()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_BE_INVITE, 0)
	end
	self.data:AddUnlockZhanLingFlag(protocol.zhandui_info.actived_zhandui_lingpai_id_flag)
	GlobalEventSystem:Fire(OtherEventType.ZhanDui_Info_Change, protocol.notify_reason)
	KF3V3WGCtrl.Instance:FlushInfoView()
end

--请求战队列表会先下发6929， 然后下发6920
--已申请的战队列表
function ZhanDuiWGCtrl:OnSCZhandui3v3AppliedZhandui(protocol)
	self.data:SetAppliedZhanDuiInfoList(protocol)
	GlobalEventSystem:Fire(OtherEventType.ZhanDui_Applied_List_Receive)
end

--战队信息列表返回
function ZhanDuiWGCtrl:OnSCZhandui3V3InfoList(protocol)
	self.data:SetZhanDuiInfoList(protocol)
	GlobalEventSystem:Fire(OtherEventType.ZhanDui_List_Receive)
end

--3v3战队申请信息
function ZhanDuiWGCtrl:OnSCZhandui3V3ApplyJoinInfo(protocol)
	self.data:AddApplyInfo(protocol.apply_item)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_NEW_APPLT, self.data:GetApplyCount(), function()
		self:OpenApplyView()
		return true
	end)
	RemindManager.Instance:Fire(RemindName.ActPVPbArena)
	RemindManager.Instance:Fire(RemindName.ZhanDui)
	GlobalEventSystem:Fire(OtherEventType.ZhanDui_New_Apply)
	KF3V3WGCtrl.Instance:FlushInfoView()
end

--3v3战队申请信息列表
function ZhanDuiWGCtrl:OnSCZhandui3V3ApplyJoinList(protocol)
	self.data:SetApplyInfoList(protocol)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_NEW_APPLT, self.data:GetApplyCount(), function()
		self:OpenApplyView()
		return true
	end)
	GlobalEventSystem:Fire(OtherEventType.ZhanDui_All_Apply)
	RemindManager.Instance:Fire(RemindName.ActPVPbArena)
	RemindManager.Instance:Fire(RemindName.ZhanDui)
	KF3V3WGCtrl.Instance:FlushInfoView()
end

-- 3v3战队新日志
function ZhanDuiWGCtrl:OnSCZhandui3v3NewLog(protocol)
	self.data:AddLog(protocol)
	GlobalEventSystem:Fire(OtherEventType.ZhanDui_Add_Log, protocol.content, protocol.timestamp)
end

-- 3v3战队全部日志(登陆下发)
function ZhanDuiWGCtrl:OnSCZhandui3v3AllLog(protocol)
	self.data:SetAllLogList(protocol)
end

-- 3v3战队新邀请(被邀请) 6928
function ZhanDuiWGCtrl:OnSZZhandui3v3NewInvite(protocol)
	--功能未开启
	local is_open_fun = FunOpen.Instance:GetFunIsOpened(FunName.KFPVP)
	if not is_open_fun then
		return
	end

	self.data:AddBeInviteInfo(protocol.be_invite_zhandui_info)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_BE_INVITE, self.data:GetBeInviteCount(), function()
		self:OpenBeInviteView()
		return true
	end)
	GlobalEventSystem:Fire(OtherEventType.ZhanDui_Add_BeInvite)
end

-- 3v3战队邀请列表(被邀请) 6929
function ZhanDuiWGCtrl:OnSCZhandui3v3InviteList(protocol)
	--功能未开启
	local is_open_fun = FunOpen.Instance:GetFunIsOpened(FunName.KFPVP)
	if not is_open_fun then
		return
	end
	self.data:SetBeInviteInfoList(protocol)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_BE_INVITE, self.data:GetBeInviteCount(), function()
		self:OpenBeInviteView()
		return true
	end)
	self.zhandui_be_invite_view:Flush()
end

--战队新战令的解锁 6932
function ZhanDuiWGCtrl:OnSCZhanduiNewZhanDuiLingPai(protocol)
	self.data:AddNewZhanLingFlag(protocol.lingpai_id)
	self:FlushZhanLingView()
end

-- 头顶战队信息变更广播 6933
function ZhanDuiWGCtrl:OnSCZhanduiInfoChangeNotify(protocol)
	local role = Scene.Instance:GetObj(protocol.obj_id)
	if role then
		if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
			local follow_ui = role:GetFollowUi()
			follow_ui:SetZhanDuiName(role:GetVo().zhandui3v3_name)
			follow_ui:SetZhanDuiZhanLingId(role:GetVo().zhandui3v3_lingpai_id)
			follow_ui:SetZhanDuiZhanLingText(role:GetVo().zhandui3v3_lingpai_name)
			follow_ui:SetGuildName()
		end
	end
end

--3V3战队操作(处理)
function ZhanDuiWGCtrl:SendZhandui3v3Opera(opera_type, param1, param2, name_flexible)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSZhandui3v3Opera)
	send_protocol.opera_type = opera_type
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol.name_flexible = name_flexible or ""
	send_protocol:EncodeAndSend()
end

--3V3战队操作(修改战队公告)
function ZhanDuiWGCtrl:SendGroupModifyNotice(group_id, notice)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGroupModifyNotice)
	send_protocol.group_type = GROUP_TYPE.GROUP_TYPE_ZHANDUI3V3
	send_protocol.group_id = group_id
	send_protocol.notice = notice or ""
	send_protocol:EncodeAndSend()
end

--修改战队名
function ZhanDuiWGCtrl:SendModifyZhanDuiName(group_id, name)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGroupModifyName)
	send_protocol.group_type = GROUP_TYPE.GROUP_TYPE_ZHANDUI3V3
	send_protocol.group_id = group_id
	send_protocol.name = name or ""
	send_protocol:EncodeAndSend()
end

--一键申请加入战队
function ZhanDuiWGCtrl:SendAutoApplyZhanDui(zhandui_id_list)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSZhandui3v3OneKeyAppliedJoin)
	send_protocol.zhandui_id_list = zhandui_id_list
	send_protocol:EncodeAndSend()
end

--3V3战队操作(团队操作)
function ZhanDuiWGCtrl:SendZhanDuiOperate(opera_type, group_id, group_name, param1, param2, param3)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGroupOpera)
	send_protocol.opera_type = opera_type
	send_protocol.group_type = GROUP_TYPE.GROUP_TYPE_ZHANDUI3V3
	send_protocol.group_id = group_id or MsgAdapter.InitUUID()
	send_protocol.group_name = group_name or ""
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol.param3 = param3 or 0
	send_protocol:EncodeAndSend()
end

--请求创建3V3战队
function ZhanDuiWGCtrl:ReqCreateZhanDui(group_name)
	self:SendZhanDuiOperate(GROUP_OPERA_TYPE.GROUP_OPERA_TYPE_CREATE, nil, group_name)
end

--申请加入3V3战队
function ZhanDuiWGCtrl:SendApplyToJoinZhanDui(group_id)
	self:SendZhanDuiOperate(GROUP_OPERA_TYPE.GROUP_OPERA_TYPE_APPLY_JOIN, group_id)
end

--审批加入3V3战队 param1是申请人uid， param2：是否今日拒绝 1:今日拒绝,0:不是
function ZhanDuiWGCtrl:SendApprovalApplyToJoinZhanDui(param1, param2)
	local group_id = ZhanDuiWGData.Instance:GetZhanDuiInfo().zhandui_id
	self:SendZhanDuiOperate(GROUP_OPERA_TYPE.GROUP_OPERA_TYPE_APPROVAL_APPLY_JOIN, group_id, nil, param1, param2)
end

--邀请加入3V3战队 param1是邀请人uid
function ZhanDuiWGCtrl:SendInviteZhanDui(param1)
	local group_id = ZhanDuiWGData.Instance:GetZhanDuiInfo().zhandui_id
	self:SendZhanDuiOperate(GROUP_OPERA_TYPE.GROUP_OPERA_TYPE_INVITE, group_id, nil, param1)
end

--回应邀请加入3V3战队 param1：是否同意 1:同意,0:拒绝， param2：是否今日拒绝 1:今日拒绝,0:不是
function ZhanDuiWGCtrl:SendBeInviteAckZhanDui(zhandui_id, param1, param2)
	self:SendZhanDuiOperate(GROUP_OPERA_TYPE.GROUP_OPERA_TYPE_INVITE_ACK, zhandui_id, nil, param1, param2)
end

--离开3V3战队
function ZhanDuiWGCtrl:SendLeaveZhanDui()
	local group_id = ZhanDuiWGData.Instance:GetZhanDuiInfo().zhandui_id
	self:SendZhanDuiOperate(GROUP_OPERA_TYPE.GROUP_OPERA_TYPE_LEAVE, group_id)
end

--踢出3V3战队 param1是要踢的玩家uid
function ZhanDuiWGCtrl:SendKickMember(param1)
	local group_id = ZhanDuiWGData.Instance:GetZhanDuiInfo().zhandui_id
	self:SendZhanDuiOperate(GROUP_OPERA_TYPE.GROUP_OPERA_TYPE_KICK_MEMBER, group_id, nil, param1)
end

--请求战队信息
function ZhanDuiWGCtrl:ReqZhanDuiInfo()
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.QueryMyZhanduiInfo)
end

--请求3V3战队信息列表
function ZhanDuiWGCtrl:ReqZhanDuiInfoList()
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.QueryAllZhanduiList)
end

--请求战队日志
function ZhanDuiWGCtrl:ReqZhanDuiLogList()
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.QueryAllLog)
end

--请求申请列表
function ZhanDuiWGCtrl:ReqZhanDuiApplyList()
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.QueryAppliedZhandui)
end

--请求已申请本战队的玩家列表
function ZhanDuiWGCtrl:ReqZhanDuiApplyPlayerList()
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.QueryAppliedRole)
end

--移交队长
function ZhanDuiWGCtrl:SendZhanDuiChangeCaptain(param1)
	local group_id = ZhanDuiWGData.Instance:GetZhanDuiInfo().zhandui_id
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.ChangeCaptain, param1)
end

--今日拒绝玩家申请加入 param1是目标玩家uid
function ZhanDuiWGCtrl:SendTodayRejectApplyJoin(param1)
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.RejectApplyJoin, param1)
end

--请求排名 param1是是否上个赛季， 1是上个赛季
function ZhanDuiWGCtrl:SendZhanDuiRankReq(param1)
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.QueryCross3V3Rank, param1)
end

--领取阶段奖励，param1发阶段奖励的配置索引
function ZhanDuiWGCtrl:SendZhanDuiFetchJieDuanReward(param1)
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.FetchMatchTimesReward, param1)
end

--选择战队令牌，param1是战队令牌配置id
function ZhanDuiWGCtrl:SendChooseZhanduiLingpai(param1)
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.ChooseZhanduiLingpai, param1)
end

--修改战队令牌 字
function ZhanDuiWGCtrl:ModifyZhanDuiZhanLingText(str)
	local is_in_zhandui = self.data:GetIsInZhanDui()
	local is_zhandui_captain = self.data:GetIsZhanDuiCaptain()
	if not is_in_zhandui then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotInZhandui)
		return
	end
	if not is_zhandui_captain then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotIsZhanduiCaptain)
		return
	end
	--local zhandui_info = self.data:GetZhanDuiInfo()
	self:SendZhandui3v3Opera(ZHANDUI3V3_OPERA.ChangeZhanduiLingpaiName, nil, nil, str)
end

--修改公告操作
function ZhanDuiWGCtrl:ModifyZhanDuiNotice(notice)
	local is_in_zhandui = self.data:GetIsInZhanDui()
	local is_zhandui_captain = self.data:GetIsZhanDuiCaptain()
	if not is_in_zhandui then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotInZhandui)
		return
	end
	if not is_zhandui_captain then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotIsZhanduiCaptain)
		return
	end
	local zhandui_info = self.data:GetZhanDuiInfo()
	self:SendGroupModifyNotice(zhandui_info.zhandui_id, notice)
end

--修改名字操作
function ZhanDuiWGCtrl:ModifyZhanDuiName(name)
	local is_in_zhandui = self.data:GetIsInZhanDui()
	local is_zhandui_captain = self.data:GetIsZhanDuiCaptain()
	if not is_in_zhandui then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotInZhandui)
		return
	end
	if not is_zhandui_captain then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotIsZhanduiCaptain)
		return
	end
	local zhandui_info = self.data:GetZhanDuiInfo()
	self:SendModifyZhanDuiName(zhandui_info.zhandui_id, name)
end

--协议操作 end








------------界面操作 start

--打开加入战队界面
function ZhanDuiWGCtrl:OpenJoinView()
	if not self.zhandui_join_view:IsOpen() then
		self.zhandui_join_view:Open()
	end
end

--关闭加入战队界面
function ZhanDuiWGCtrl:CloseJoinView()
	if self.zhandui_join_view:IsOpen() then
		self.zhandui_join_view:Close()
	end
end

--打开创建战队界面
function ZhanDuiWGCtrl:OpenCreateView()
	if not self.zhandui_create_view:IsOpen() then
		self.zhandui_create_view:Open()
	end
end

--关闭创建战队界面
function ZhanDuiWGCtrl:CloseCreateView()
	if self.zhandui_create_view:IsOpen() then
		self.zhandui_create_view:Close()
	end
end

--打开修改公告界面
function ZhanDuiWGCtrl:OpenChangeNoticeView()
	if not self.zhandui_change_notice_view:IsOpen() then
		self.zhandui_change_notice_view:Open()
	end
end

--关闭修改公告界面
function ZhanDuiWGCtrl:CloseChangeNoticeView()
	if self.zhandui_change_notice_view:IsOpen() then
		self.zhandui_change_notice_view:Close()
	end
end

--打开修改名字界面
function ZhanDuiWGCtrl:OpenChangeNameView()
	if not self.zhandui_change_name_view:IsOpen() then
		self.zhandui_change_name_view:Open()
	end
end

--关闭修改名字界面
function ZhanDuiWGCtrl:CloseChangeNameView()
	if self.zhandui_change_name_view:IsOpen() then
		self.zhandui_change_name_view:Close()
	end
end

--打开修改战令界面
function ZhanDuiWGCtrl:OpenZhanLingView()
	if not self.zhandui_zhanling_view:IsOpen() then
		self.zhandui_zhanling_view:Open()
	end
end

--关闭修改战令界面
function ZhanDuiWGCtrl:CloseZhanLingView()
	if self.zhandui_zhanling_view:IsOpen() then
		self.zhandui_zhanling_view:Close()
	end
end

--刷新战令界面
function ZhanDuiWGCtrl:FlushZhanLingView()
	self.zhandui_zhanling_view:Flush()
end

--打开被邀请界面
function ZhanDuiWGCtrl:OpenBeInviteView()
	if not self.zhandui_be_invite_view:IsOpen() then
		self.zhandui_be_invite_view:Open()
	end
end

--关闭被邀请界面
function ZhanDuiWGCtrl:CloseBeInviteView()
	if self.zhandui_be_invite_view:IsOpen() then
		self.zhandui_be_invite_view:Close()
	end
end

--打开邀请別人界面
function ZhanDuiWGCtrl:OpenInviteView()
	if not self.zhandui_invite_view:IsOpen() then
		self.zhandui_invite_view:Open()
	end
end

--关闭邀请別人界面
function ZhanDuiWGCtrl:CloseInviteView()
	if self.zhandui_invite_view:IsOpen() then
		self.zhandui_invite_view:Close()
	end
end

--打开申请界面
function ZhanDuiWGCtrl:OpenApplyView()
	if not self.zhandui_apply_view:IsOpen() then
		self.zhandui_apply_view:Open()
	end
end

--关闭申请界面
function ZhanDuiWGCtrl:CloseApplyView()
	if self.zhandui_apply_view:IsOpen() then
		self.zhandui_apply_view:Close()
	end
end

--刷新申请界面
function ZhanDuiWGCtrl:FlushApplyView()
	if self.zhandui_apply_view:IsOpen() then
		self.zhandui_apply_view:Flush()
	end
end

--打開喊话界面
function ZhanDuiWGCtrl:OpenTalkView()
	if CountDownManager.Instance:HasCountDown("zhandui_world_talk") then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CDWorldTalk)
		return
	end
	self.zhandui_talk_view:Open()
end

function ZhanDuiWGCtrl:CloseTalkView()
	self.zhandui_talk_view:Close()
end

--申请界面是否勾选今日拒绝
function ZhanDuiWGCtrl:GetApplyTodayCheckActive()
	return self.zhandui_apply_view:GetApplyTodayCheckActive()
end

--被邀请入队界面是否勾选今日拒绝
function ZhanDuiWGCtrl:GetBeInviteTodayCheckActive()
	return self.zhandui_be_invite_view:GetBeInviteTodayCheckActive()
end

------------界面操作 end


--跨天，直接关闭申请按钮
function ZhanDuiWGCtrl:OnPassDay()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_NEW_APPLT, 0)
	ZhanDuiWGData.Instance:ClearApply()
	KF3V3WGCtrl.Instance:FlushInfoView()
end

--好友状态改变刷新邀請界面
function ZhanDuiWGCtrl:OnOtherRoleOnlineChange()
	if self.zhandui_invite_view:IsOpen() then
		self.zhandui_invite_view:Flush()
	end
end

--UI赋值操作
--设置战令图片
function ZhanDuiWGCtrl.SetZhanLingImg(node, zhandui_lingpai_id)
	if not node or not node.image then
		return
	end
	if not zhandui_lingpai_id then
		return
	end
	local cfg = KF3V3WGData.Instance:GetZhanLingCfg(zhandui_lingpai_id)
	if not cfg then
		return
	end
	node.image:LoadSprite(ZhanDuiWGData.Instance:GetZhanLingResPath(cfg.icon))
end

local ZhanLingColorText = {
	[ZHAN_LING_TEXT_COLOR_TYPE.ONE] = {
		[1] = Color.New(232 / 255, 248 / 255, 249 / 255, 1),
		[2] = Color.New(160 / 255, 185 / 255, 194 / 255, 1),
		[3] = Color.New(72 / 255, 16 / 255, 16 / 255, 1) },
	[ZHAN_LING_TEXT_COLOR_TYPE.TWO] = {
		[1] = Color.New(1, 1, 233 / 255, 1),
		[2] = Color.New(249 / 255, 207 / 255, 153 / 255, 1),
		[3] = Color.New(72 / 255, 16 / 255, 16 / 255, 1) },
}
function ZhanDuiWGCtrl.ChangeZhanLingTextColor(text, color_type)
	if IsNil(text) then
		return
	end
	if not ZhanLingColorText[color_type] then
		return
	end
	local gradient = text:GetOrAddComponent(typeof(UIGradient))
	gradient.Color1 = ZhanLingColorText[color_type][1]
	gradient.Color2 = ZhanLingColorText[color_type][2]

	local shadow = text:GetOrAddComponent(typeof(UnityEngine.UI.Shadow))
	shadow.effectColor = ZhanLingColorText[color_type][3]
	shadow.effectDistance = Vector2(1, -1)
end

--设置段位名字
function ZhanDuiWGCtrl.SetZhanDuiDuanWeiText(node, grade_cfg, not_show_out_line)
	if not node or not node.text then
		return
	end
	if not grade_cfg then
		return
	end
	node.text.text = grade_cfg.name
	--ChangeToQualityText(node.text, RankGradeEnum[grade_cfg.rank_id], not_show_out_line)
end

--设置段位图片
function ZhanDuiWGCtrl.SetZhanDuiDuanWeiImage(node, grade_cfg)
	if not node or not node.image then
		return
	end
	if not grade_cfg then
		return
	end
	local bundel, asset = ResPath.GetCommon("a3_jjc_hz" .. grade_cfg.grade)
	node.image:LoadSprite(bundel, asset, function()
		node.image:SetNativeSize()
	end)
end

--发送喊话
function ZhanDuiWGCtrl:SendWordTalk(str)
	CountDownManager.Instance:AddCountDown("zhandui_world_talk", BindTool.Bind(self.UpdateWordTalkBtn, self),
		BindTool.Bind(self.ComleteWoldTalkCD, self), nil, 10, 0.5)

	local zhandui_info = self.data:GetZhanDuiInfo()
	local find_str = Language.ZhanDui.ZhanDui .. zhandui_info.name
	local i, j = string.find(str, find_str)
	if i and j then
		local replace_str = string.format(Language.ZhanDui.ZhanDuiTalkTextPrefix, zhandui_info.name)
		str = string.gsub(str, find_str, replace_str)
	end

	local zhandui_id = zhandui_info.zhandui_id
	local content = string.format(Language.ZhanDui.AddZhanDuiLink, str, zhandui_id.temp_low, zhandui_id.temp_high)
	ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, content, CHAT_CONTENT_TYPE.TEXT, CHAT_MSG_RESSON.NORMAL)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.SendWordTalkSuccess)

	self:CloseTalkView()
end

function ZhanDuiWGCtrl:UpdateWordTalkBtn(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	GlobalEventSystem:Fire(ZhanDuiWorldTalk.UPDATE_WORLD_TALK, time)
end

function ZhanDuiWGCtrl:ComleteWoldTalkCD()
	GlobalEventSystem:Fire(ZhanDuiWorldTalk.COMPLETE_WORLD_TALK)
end

function ZhanDuiWGCtrl:FlushTextInvite()
	if self.zhandui_invite_view:IsOpen() and self.zhandui_invite_view:IsLoaded() then
		self.zhandui_invite_view:FlushTextInvite()
	end
end
