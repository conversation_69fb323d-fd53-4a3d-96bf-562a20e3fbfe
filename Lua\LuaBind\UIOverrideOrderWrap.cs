﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UIOverrideOrderWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(UIOverrideOrder), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("GetTarget", GetTarget);
		<PERSON><PERSON>unction("SetOverrideOrder", SetOverrideOrder);
		<PERSON><PERSON>unction("ResetRootCanvas", ResetRootCanvas);
		<PERSON><PERSON>RegFunction("SetClipSoftness", SetClipSoftness);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		L<PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIOverrideOrder obj = (UIOverrideOrder)ToLua.CheckObject<UIOverrideOrder>(L, 1);
			UnityEngine.GameObject o = obj.GetTarget();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOverrideOrder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			UIOverrideOrder obj = (UIOverrideOrder)ToLua.CheckObject<UIOverrideOrder>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			int arg3;
			obj.SetOverrideOrder(arg0, arg1, arg2, out arg3);
			LuaDLL.lua_pushinteger(L, arg3);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetRootCanvas(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIOverrideOrder obj = (UIOverrideOrder)ToLua.CheckObject<UIOverrideOrder>(L, 1);
			obj.ResetRootCanvas();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetClipSoftness(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIOverrideOrder obj = (UIOverrideOrder)ToLua.CheckObject<UIOverrideOrder>(L, 1);
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.SetClipSoftness(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

