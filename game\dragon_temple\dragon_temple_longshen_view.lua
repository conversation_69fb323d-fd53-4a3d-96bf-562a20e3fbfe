function DragonTempleView:LoadIndexCallBackLongShen()
	XUI.AddClickEventListener(self.node_list["longshen_purchase_up_btn"], BindTool.Bind(self.OnClickOpenLongShenBuy, self))
	XUI.AddClickEventListener(self.node_list["longshen_up_level_btn"], BindTool.Bind(self.OnClickLongShenUpLevel, self))
	XUI.AddClickEventListener(self.node_list.combat_mount_btn, BindTool.Bind(self.OnClickCombatMountBtn, self))
	XUI.AddClickEventListener(self.node_list.longshen_wild_btn, BindTool.Bind(self.OnClickOpenWildBuyView, self))
	if not self.longshen_cell then
		self.longshen_cell = ItemCell.New(self.node_list["longshen_cell_pos"])
	end

	if not self.longshen_next_cell then
		self.longshen_next_cell = ItemCell.New(self.node_list["longhun_next_cell"])
	end

	if nil == self.dragon_model_display then
        self.dragon_model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["dragon_model_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.dragon_model_display:SetRenderTexUI3DModel(display_data)
        -- self.dragon_model_display:SetUI3DModel(self.node_list["dragon_model_display"].transform, self.node_list["dragon_model_display"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.dragon_model_display)
    end

	self.node_list["stuff_icon"].button:AddClickListener(BindTool.Bind(self.ShowLongHunItemTips, self))
	self.node_list["longshen_box_img"].button:AddClickListener(BindTool.Bind(self.GetLongShenDailyReward, self))

	self.longhun_remind_change = BindTool.Bind(self.ComBatMountCallBack, self)
	RemindManager.Instance:Bind(self.longhun_remind_change, RemindName.NewFightMount)
end


function DragonTempleView:ShowIndexCallBackLongShen()
	self:DoLongShenViewAnim()

	self.node_list.combat_mount_btn:CustomSetActive(FunOpen.Instance:GetFunIsOpened(FunName.NewFightMountView))
end

function DragonTempleView:ReleaseLongShen()
	if self.longshen_cell then
		self.longshen_cell:DeleteMe()
		self.longshen_cell = nil
	end

	if self.longshen_next_cell then
		self.longshen_next_cell:DeleteMe()
		self.longshen_next_cell = nil
	end

	if self.dragon_model_display then
        self.dragon_model_display:DeleteMe()
        self.dragon_model_display = nil
    end

	if self.longhun_remind_change then
        RemindManager.Instance:UnBind(self.longhun_remind_change)
        self.longhun_remind_change = nil
    end
end

function DragonTempleView:OnFlushLongShen(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushLongShenLeftInfo()
			self:FlushLongShenRightInfo()
		end
	end

	self.node_list.longshen_wild_btn:CustomSetActive(DragonTempleWGData.Instance:ShowWildFunBtn())
end

function DragonTempleView:FlushLongShenLeftInfo()
	local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
	local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
	local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
	local cap = DragonTempleWGData.Instance:GetLongShenLevelCapability(longshen_level)
	self.node_list.longshen_desc_1.text.text = string.format(Language.DragonTemple.CapStr, cap)
	self.node_list.cap_value.text.text = cap
	if next_level_cfg and cur_level_cfg then
		self.node_list.longshen_desc_2.text.text = string.format(Language.DragonTemple.LongHunNum, NumberToChinaNumber(cur_level_cfg.daily_max_longhun))
		self.node_list.longshen_desc_3.text.text = next_level_cfg.box_desc
	end

	self.node_list.longshen_desc_img_1:SetActive(false)          --屏蔽
	self.node_list.longshen_desc_img_2:SetActive(not IsEmptyTable(next_level_cfg))
	self.node_list.longshen_desc_img_3:SetActive(not IsEmptyTable(next_level_cfg) and next_level_cfg.box_desc ~= "")
	self.node_list.longshen_purchase_up_btn:SetActive(not IsEmptyTable(next_level_cfg))

	local state = DragonTempleWGData.Instance:GetDailyRewardSate()
	XUI.SetButtonEnabled(self.node_list.longshen_box_img, state == 0)
	self.node_list.longshen_red:SetActive(state == 0)

    if self.dragon_model_display and cur_level_cfg then
        local data = {}
        data.item_id = cur_level_cfg.show_model

        local bundle,asset = ResPath.GetMountModel(data.item_id)
        self.dragon_model_display:SetMainAsset(bundle, asset)
    end
end

function DragonTempleView:FlushLongShenRightInfo()
	local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
	local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
	local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
	local other_cfg = DragonTempleWGData.Instance:GetOtherCfg()
	local longhun_value = DragonTempleWGData.Instance:GetLongHunValue()
	if cur_level_cfg then
		self.longshen_cell:SetData({item_id = cur_level_cfg.item_id})
		local item_cfg = ItemWGData.Instance:GetItemConfig(cur_level_cfg.item_id)
		self.node_list.longshen_cell_name.text.text = item_cfg and item_cfg.name or ""
		local asset, bundle = ResPath.GetCommon("a2_ty_wpk_bk_" .. cur_level_cfg.show_icon_ground)
	    self.node_list["longshen_cell_bg"].image:LoadSprite(asset, bundle, function ()
	        self.node_list["longshen_cell_bg"].image:SetNativeSize()
	    end)

	    self.node_list.longshen_cur_level_desc.text.text = cur_level_cfg.level_desc
	end

	self.node_list.longshen_next_info:SetActive(not IsEmptyTable(next_level_cfg))
	self.node_list.longshen_max_level:SetActive(IsEmptyTable(next_level_cfg))
	if cur_level_cfg and next_level_cfg then
		local item_cfg = ItemWGData.Instance:GetItemConfig(next_level_cfg.item_id)
		self.node_list.need_longhun_cell_name.text.text = string.format(Language.DragonTemple.UpLongShenName, item_cfg and item_cfg.name or "")
		self.longshen_next_cell:SetData({item_id = next_level_cfg.item_id})
		local color = longhun_value >= cur_level_cfg.need_longhun and COLOR3B.D_GREEN or COLOR3B.D_PINK
		local value_str = longhun_value .. "/" .. cur_level_cfg.need_longhun
		self.node_list["longhun_need_num"].text.text = ToColorStr(value_str, color)

		local asset1, bundle1 = ResPath.GetCommon("a2_ty_wpk_bk_" .. next_level_cfg.show_icon_ground)
	    self.node_list["longshen_next_cell_bg"].image:LoadSprite(asset1, bundle1, function ()
	        self.node_list["longshen_next_cell_bg"].image:SetNativeSize()
	    end)
	end

	local y_value = IsEmptyTable(next_level_cfg) and 280 or 110
	self.node_list.longshen_desc_bg.rect.sizeDelta = Vector2(282, y_value)

	local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.longhun_item)
	self.node_list["stuff_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))

	self.node_list.longshen_up_level_red:SetActive(not IsEmptyTable(next_level_cfg) and (longhun_value >= cur_level_cfg.need_longhun))
end

function DragonTempleView:OnClickOpenLongShenBuy()
	DragonTempleWGCtrl.Instance:OpenDragonTempleLevelBuyView()
end

function DragonTempleView:OnClickLongShenUpLevel()
	local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
	local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
	local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
	local other_cfg = DragonTempleWGData.Instance:GetOtherCfg()
	if IsEmptyTable(next_level_cfg) or IsEmptyTable(cur_level_cfg) then
		return
	end

	local longhun_value = DragonTempleWGData.Instance:GetLongHunValue()
	if longhun_value >= cur_level_cfg.need_longhun then
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.LEVEL_UP)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.LongHunNotEnough)
		TipWGCtrl.Instance:OpenItem({item_id = other_cfg.longhun_item})
	end
end

function DragonTempleView:ShowLongHunItemTips()
    local other_cfg = DragonTempleWGData.Instance:GetOtherCfg()
	TipWGCtrl.Instance:OpenItem({item_id = other_cfg.longhun_item})
end

function DragonTempleView:GetLongShenDailyReward()
	local state = DragonTempleWGData.Instance:GetDailyRewardSate()
	if state == 1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.YiLingQu)
	else
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.DAILY_REWARD)
	end
end

function DragonTempleView:DoLongShenViewAnim()
	local tween_info = UITween_CONSTS.DragonTemple
	RectTransform.SetAnchoredPositionXY(self.node_list.longshen_right_root.rect, 600, 0)
	for i = 1, 3 do
		local obj_transform = self.node_list["longshen_desc_img_" .. i].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
		obj_transform.alpha = 0
	end

	self.node_list.longshen_right_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
	for i = 1, 3 do
		self.node_list["longshen_desc_img_" .. i].canvas_group:DoAlpha(0, 1, tween_info.desc_time)
	end
end

function DragonTempleView:PlayLongShenSucEffcet()
	if self.node_list["longhun_suc_effct"] then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_effect_longshendian7")
			EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["longhun_suc_effct"].transform, 3)
	end
end

function DragonTempleView:OnClickCombatMountBtn()
	ViewManager.Instance:Open(GuideModuleName.NewFightMountView)
end

function DragonTempleView:ComBatMountCallBack(remind_name, num)
	self.node_list["combat_mount_red"]:SetActive(num > 0)
end

function DragonTempleView:OnClickOpenWildBuyView()
	DragonTempleWGCtrl.Instance:OpenDragonWildBuyView()
end