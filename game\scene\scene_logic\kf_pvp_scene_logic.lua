
KfPVPSceneLogic = KfPVPSceneLogic or BaseClass(CrossServerSceneLogic)

function KfPVPSceneLogic:__init()
	self.last_show_be_kill_time = 0
	self.next_get_all_move_obj_time = 0					-- 下次获取移动对象的时间
end

function KfPVPSceneLogic:__delete()
	self.next_get_all_move_obj_time = 0
end

-- 进入场景
function KfPVPSceneLogic:Enter(old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	self.next_get_all_move_obj_time = 0
	KF3V3WGCtrl.Instance:OpenLogicView()

	MainuiWGCtrl.Instance:SetTaskRootNodeActive(false)
	-- GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

    self.cross_3v3_side_change = GlobalEventSystem:Bind(OtherEventType.MAIN_ROLE_3V3_SIDE_CHANGE, BindTool.Bind1(self.Cross3V3SideChange, self)) 	-- 主角阵营更变
	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))
	self.main_role_enter_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.OnMainRoleEnter, self))
	self.guaji_change_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.BindGuaJiFireEvent,self))

	local role_list = Scene.Instance:GetRoleList()
	for i, v in pairs(role_list) do
		if not v:IsMainRole() then
			local follow_ui = v:GetFollowUi()
			follow_ui:SetZhanDuiName(v:GetVo().zhandui3v3_name)
			follow_ui:SetZhanDuiZhanLingId(v:GetVo().zhandui3v3_lingpai_id)
			follow_ui:SetZhanDuiZhanLingText(v:GetVo().zhandui3v3_lingpai_name)
			follow_ui:SetGuildName()
			v:SetIsPerformer(v:IsGhost())
		end
	end

	local main_role = Scene.Instance:GetMainRole()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local follow_ui = main_role:GetFollowUi()
	follow_ui:SetZhanDuiName(main_role_vo.zhandui3v3_name)
	follow_ui:SetZhanDuiZhanLingId(main_role_vo.zhandui3v3_lingpai_id)
	follow_ui:SetZhanDuiZhanLingText(main_role_vo.zhandui3v3_lingpai_name)
	follow_ui:SetGuildName() 

	local other_cfg = KF3V3WGData.Instance:GetNewOtherCfg()
	if other_cfg then
		local target_pos = Split(other_cfg.target_pos, "|")
		local normal_pos_x, normal_pos_y = 0,0

		if target_pos then
			normal_pos_x = target_pos[1] or normal_pos_x
			normal_pos_y = target_pos[2] or normal_pos_y

			for i, v in pairs(role_list) do
				v:SetDirectionByXY(normal_pos_x, normal_pos_y)
			end
		end
	end

	--下移对方玩家头像血条
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, -100)
	self:TryChangeCameraAngle()

	local logic_view = KF3V3WGCtrl.Instance:GetLogicView()
	ViewManager.Instance:AddMainUIFuPingChangeList(logic_view)
	ViewManager.Instance:AddMainUIRightTopChangeList(logic_view)
end

function KfPVPSceneLogic:Update(now_time, elapse_time)
	if self.last_show_be_kill_time and self.last_show_be_kill_time > Status.NowTime then
		return
	end

	local queue = KF3V3WGData.Instance:GetKillInfoQueue()
	if #queue <= 0 then
		return
	end

	self.last_show_be_kill_time = Status.NowTime + 1  --一秒弹一次
	local kill_info = table.remove(queue, 1)
	KF3V3WGCtrl.Instance:OpenKillInfoView(kill_info)
end

function KfPVPSceneLogic:Out(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Out(self)
	if old_scene_type ~= new_scene_type then
		KF3V3WGCtrl.Instance:CancelChangeGhost()
		KF3V3WGCtrl.Instance:CloseLogicView()
		MainuiWGCtrl.Instance:SetTaskRootNodeActive(true)
		KF3V3WGData.Instance:ClearKillInfoQueue() --清空击杀信息
		MainuiWGCtrl.Instance:SetTransparentMaskActive(false)
		MainuiWGCtrl.Instance:UpdateMainRoleHp()

		KF3V3WGCtrl.Instance:CheckOpenActEndView(new_scene_type, {act_type = ACTIVITY_TYPE.KF_PVP})
	end

	self.last_show_be_kill_time = 0
	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end

    if self.main_role_enter_event then
        GlobalEventSystem:UnBind(self.main_role_enter_event)
        self.main_role_enter_event = nil
    end

    if self.guaji_change_event then
        GlobalEventSystem:UnBind(self.guaji_change_event)
        self.guaji_change_event = nil
    end

    if self.cross_3v3_side_change then
        GlobalEventSystem:UnBind(self.cross_3v3_side_change)
        self.cross_3v3_side_change = nil
    end

    --复原对方玩家头像血条
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)

	local view = KF3V3WGCtrl.Instance:GetLogicView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
end

function KfPVPSceneLogic:GetRoleNameBoardText(role_vo)
	local col_t = COLOR3B.WHITE
	if Scene.Instance:GetMainRole() then
		local main_role_vo = Scene.Instance:GetMainRole():GetVo()
		local main_role_side = main_role_vo.cross3v3_side
		local role_side = role_vo.is_shadow and role_vo.special_param or role_vo.cross3v3_side
		if role_vo.obj_id == main_role_vo.obj_id then
			col_t = COLOR3B.WHITE
		elseif main_role_side == role_side then
			col_t = COLOR3B.YELLOW
		else
			col_t = COLOR3B.RED
		end
	end

	local t = {}
	t.color = col_t
	t.text = role_vo.name
	return t
end

-- 获取采集物特殊名字显示
function KfPVPSceneLogic:GetGatherSpecialText(gather_vo)
	local t = {}
	local gather_name, gather_color = KuafuPVPWGData.Instance:GetGatherNameByObjid(gather_vo.obj_id)
	if gather_name then
		t[1] = {}
		t[1].color = gather_color
		t[1].text = "【" .. gather_name .. "】"
		return t
	end

	return t
end

-- 获取怪物名
function KfPVPSceneLogic:GetMonsterName(monster)
	local color_name = ""
	local stronghold_info = KuafuPVPWGData.Instance:GetStrongHoldInfo()
	local side = stronghold_info.flag_info
	if side.flag_side and side.flag_side == 0 then
		color_name = "<color=#ff0000ff>红方占领</color>"
	elseif side.flag_side and side.flag_side == 1 then
		color_name = "<color=#2365aaff>蓝方占领</color>"
	else
		color_name = "暂无归属"
	end
	return color_name
end

-- 获取采集物特殊形象
function KfPVPSceneLogic:GetGatherSpecialRes(gather_vo)
	return KuafuPVPWGData.Instance:GetGatherResByObjid(gather_vo.obj_id)
end

-- 角色是否是敌人
function KfPVPSceneLogic:IsRoleEnemy(target_obj, main_role)
	local main_role_side = KF3V3WGData.Instance:GetMainRoleSide()
	if target_obj == nil or main_role_side == nil then
		return false, Language.Fight.Side
	end

	local target_vo = target_obj:GetVo()
	local target_side = target_vo.is_shadow and target_vo.special_param or target_vo.cross3v3_side
	if main_role_side == target_side then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end

function KfPVPSceneLogic:OnClickHeadHandler(is_show)
	CrossServerSceneLogic.OnClickHeadHandler(self, is_show)
end

function KfPVPSceneLogic:SpecialSelectEnemy()
	return false
end

function KfPVPSceneLogic:GetGuajiCharacter()
	local target_obj = self:GetRole()
	if target_obj ~= nil then
		GuajiCache.target_obj = target_obj
		return target_obj, COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE, false
	end
end

function KfPVPSceneLogic:GetRole( )
	local main_role = Scene.Instance:GetMainRole()
	local role_list = Scene.Instance:GetRoleList()
	local limit_distance = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local target_obj = nil
	local main_x,main_y = main_role:GetLogicPos()
	for k,v in pairs(role_list) do
		if not v:IsMainRole() and self:IsRoleEnemy(v, main_role) and v:IsDead() == false then
			local x,y = v:GetLogicPos()
			local distance = GameMath.GetDistance(main_x, main_y, x, y, false)
			if distance < limit_distance then
				target_obj = v
				limit_distance = distance
			end
		end
	end

	if target_obj ~= nil then
		return target_obj
	end
end

function KfPVPSceneLogic:IsHideBOSSNameState()
	return false
end

function KfPVPSceneLogic:CanGetMoveObj()
	return true
end

function KfPVPSceneLogic:OnRoleEnter(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	local follow_ui = role:GetFollowUi()
	follow_ui:SetZhanDuiName(role:GetVo().zhandui3v3_name)
	follow_ui:SetZhanDuiZhanLingId(role:GetVo().zhandui3v3_lingpai_id)
	follow_ui:SetZhanDuiZhanLingText(role:GetVo().zhandui3v3_lingpai_name)
	follow_ui:SetGuildName()
	role:ReloadUIName()

	local main_role = Scene.Instance:GetMainRole()
	if main_role and main_role:IsGhost() then
		role:SetIsPerformer(true)
	end

	if GuajiCache.target_obj == nil and self:IsRoleEnemy(role, main_role) then
		GuajiCache.target_obj = role
		GuajiWGCtrl.Instance:MoveToObj(role, 4)
	end
end

function KfPVPSceneLogic:OnMainRoleEnter(scene_id)
	self:TryChangeCameraAngle()
end

function KfPVPSceneLogic:TryChangeCameraAngle()	
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then

	end
end

function KfPVPSceneLogic:GetGuajiSelectObjDistance()
	return 2000
end

function KfPVPSceneLogic:GetSceneLogicMoveState()
    local other_info = KF3V3WGData.Instance:GetSceneOtherInfo()
    if other_info and other_info.pk_state == EnumKF3V3PKStateType.Fight then
        return true
    end
	return false
end

function KfPVPSceneLogic:CanOpenMail()
	return false
end

function KfPVPSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function KfPVPSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

function KfPVPSceneLogic:BindGuaJiFireEvent(guaji_type)
	if guaji_type == GuajiType.Auto then
		if Status.NowTime >= self.next_get_all_move_obj_time then
			self.next_get_all_move_obj_time = Status.NowTime + 10
			Scene.SendGetAllObjMoveInfoReq()
		end
	end
end

function KfPVPSceneLogic:GetGuajiPos()
	--默认挂机寻路点.
	local normal_pos_x, normal_pos_y = 585, 539

	local other_cfg = KF3V3WGData.Instance:GetNewOtherCfg()
	if other_cfg then
		local target_pos = Split(other_cfg.target_pos, "|")
		if target_pos then
			normal_pos_x = target_pos[1] or normal_pos_x
			normal_pos_y = target_pos[2] or normal_pos_y
		end
	end

	-- local main_role_vo = Scene.Instance:GetMainRole():GetVo()
	-- local main_role_side = main_role_vo.cross3v3_side
	-- local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
	-- if not IsEmptyTable(obj_move_info_list) then
	-- 	local scene_info = KF3V3WGData.Instance:GetSceneInfo()
	-- 	local enemy_role_list = not IsEmptyTable(scene_info) and scene_info[main_role_side == 0 and 2 or 1] or {}
	-- 	if not IsEmptyTable(enemy_role_list) then
	-- 		for k,v in pairs(obj_move_info_list) do
	-- 			for k2,v2 in pairs(enemy_role_list) do
	-- 				if v.vo.role_id == v2.uid then
	-- 					return v.vo.pos_x, v.vo.pos_y
	-- 				end
	-- 			end
	-- 		end
	-- 	end
	-- end

	return normal_pos_x, normal_pos_y
end

-- 主角阵营更变
function KfPVPSceneLogic:Cross3V3SideChange()
	local role_list = Scene.Instance:GetRoleList()
	for i, v in ipairs(role_list) do
		v:ReloadUINameColor()
	end
end
