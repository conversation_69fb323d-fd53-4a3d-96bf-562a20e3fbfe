--------------------------------------------------
-- 养龙寺boss刷新提醒
--------------------------------------------------
YangLongSiBossNoticeView = YangLongSiBossNoticeView or BaseClass(SafeBaseView)

function YangLongSiBossNoticeView:__init()
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self.view_name = "YangLongSiBossNoticeView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/country_map_ui/yanglonsi_ui_prefab", "layout_yanglongsi_bossnotice")
end

function YangLongSiBossNoticeView:Load<PERSON><PERSON><PERSON>ack()
	self.node_list["btn_go_kill"].button:AddClickListener(BindTool.Bind1(<PERSON>.<PERSON><PERSON><PERSON>, self))
end

function YangLongSiBossNoticeView:OnFlush()
	if self.data then
		self.node_list["img_boss_head"].text.text = self.data.name
		self.node_list["boss_name_txt"].text.text = self.data.desc
		local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
		local bundle, asset

		if monster_cfg.small_icon ~= "" then
			bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. monster_cfg.small_icon)
			self.node_list.icon.image:LoadSprite(bundle, asset, function()
				self.node_list.icon.image:SetNativeSize()
			end)
		end
	end
end

function YangLongSiBossNoticeView:SetYangLongSiBossNoticeData(data)
	if not data then
		return
	end

	self.data = data
end


function YangLongSiBossNoticeView:ClickHandler()
	if self.data and self.data.click_call_back then
		self.data.click_call_back()
		self:Close()
	else
		self:Close()
	end
end

function YangLongSiBossNoticeView:OnClickCloseWindow()
	self:Close()
end