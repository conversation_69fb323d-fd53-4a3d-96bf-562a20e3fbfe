LoverPKPreliminaryEndView = LoverPKPreliminaryEndView or BaseClass(SafeBaseView)

function LoverPKPreliminaryEndView:__init()
	self.view_style = ViewStyle.Half
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_loverpk_preliminary_end")
end

function LoverPKPreliminaryEndView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_out_end"], BindTool.Bind1(self.OnClickBuyOut, self))

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end

	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetServerTime() + 5)

	if CountDownManager.Instance:HasCountDown("LoverPKPreliminaryEndView") then
		CountDownManager.Instance:RemoveCountDown("LoverPKPreliminaryEndView")
	end
	
	CountDownManager.Instance:AddCountDown("LoverPKPreliminaryEndView", 
		BindTool.Bind1(self.UpdateTime, self), 
		function ()
			local scene_type = Scene.Instance:GetSceneType()

			if scene_type == SceneType.CROSS_PK_LOVER then
				LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.RETURN_STANDBY)
			end

			self:Close()
		end,
	TimeWGCtrl.Instance:GetServerTime() + 5 , nil, 1)
end

function LoverPKPreliminaryEndView:SetDataInfoAndOpen(data)
	self.is_win = tonumber(data.is_win)
	self.is_couple = tonumber(data.is_couple)
	
	if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function LoverPKPreliminaryEndView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if CountDownManager.Instance:HasCountDown("LoverPKPreliminaryEndView") then
		CountDownManager.Instance:RemoveCountDown("LoverPKPreliminaryEndView")
	end
end

function LoverPKPreliminaryEndView:OnFlush()
	if nil == self.is_win or nil == self.is_couple then
		return
	end

	local is_win = self.is_win == 1
	self.node_list.victory:CustomSetActive(is_win)
	self.node_list.lose:CustomSetActive(not is_win)

	local my_score, lover_score = LoverPkWGData.Instance:GetTotalCoupleScore()
	-- local total_score = my_score + lover_score
	local total_score = my_score
	local reward_cfg = LoverPkWGData.Instance:GetMatchRewardCfgByIsCouple(self.is_couple)

	if not IsEmptyTable(reward_cfg) then
		local reward_data_list = is_win and reward_cfg.win_reward_item or reward_cfg.lose_reward_item
		local get_score_limit = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("get_score_limit")
		local match_count = LoverPkWGData.Instance:GetMatchCount()
		local add_score = (match_count <= get_score_limit) and (is_win and reward_cfg.win_score or reward_cfg.lose_score) or 0
		self.node_list.rich_jifen.text.text = string.format(Language.LoverPK.MatchGetScore, total_score, add_score)
		self.reward_list:SetDataList(reward_data_list)
	end
end

function LoverPKPreliminaryEndView:UpdateTime(elapse_time, total_time)
	if self.node_list.lbl_close then
		local temp_seconds = GameMath.Round(total_time - elapse_time)
		self.node_list.lbl_close.text.text = string.format(Language.LoverPK.EndTimeCountDown, temp_seconds)
	end
end

function LoverPKPreliminaryEndView:OnClickBuyOut()
	local scene_type = Scene.Instance:GetSceneType()
	
	if scene_type == SceneType.CROSS_PK_LOVER then
		LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.RETURN_STANDBY)
	end

	self:Close()
end