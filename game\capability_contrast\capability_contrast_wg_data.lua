CapabilityContrastWGData = CapabilityContrastWGData or BaseClass()

function CapabilityContrastWGData:__init()
	if CapabilityContrastWGData.Instance then
		error("[CapabilityContrastWGData] Attempt to create singleton twice!")
		return
	end

	CapabilityContrastWGData.Instance = self

	self.look_role_info = {}
	self.my_cap_list = {}
	self.other_cap_list = {}

	local cfg = ConfigManager.Instance:GetAutoConfig("capcomparison_auto")
	self.sub_sys_map_cfg = ListToMap(cfg.subsystem, "main_index", "child_index")
	self:InitSysCapList()
end

function CapabilityContrastWGData:__delete()
	CapabilityContrastWGData.Instance = nil
end

-- 初始化系统战力关联列表
function CapabilityContrastWGData:InitSysCapList()
	self.sys_cap_list = {}
	local cfg = ConfigManager.Instance:GetAutoConfig("capcomparison_auto").subsystem
	for k,v in ipairs(cfg) do
		self.sys_cap_list[v.main_index] = self.sys_cap_list[v.main_index] or {}
		local list = {}
		local type_list = Split(v.zhanli_index, "|")
		for i, type in ipairs(type_list) do
			list[tonumber(type)] = true
		end
		self.sys_cap_list[v.main_index][v.child_index] = list
	end
end

-- 获取系统战力关联列表
function CapabilityContrastWGData:GetSysCapListByIndex(main_index, child_index)
	local empty_table = {}
	return (self.sys_cap_list[main_index] or empty_table)[child_index] or empty_table
end

function CapabilityContrastWGData:SetCapabilityInfo(protocol)
	self.look_role_info = protocol.look_role_info
	self.my_cap_list = protocol.my_cap_list
	self.other_cap_list = protocol.other_cap_list
end

function CapabilityContrastWGData:GetLookRoleInfo()
	return self.look_role_info
end

function CapabilityContrastWGData:GetMyCapByType(type)
	return self.my_cap_list[type] or 0
end

function CapabilityContrastWGData:GetOtherCapByType(type)
	return self.other_cap_list[type] or 0
end

-- 总战力 - 自己
function CapabilityContrastWGData:GetMyTotalCap()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	return role_vo and role_vo.capability or 0
end

-- 总战力 - 他人
function CapabilityContrastWGData:GetOtherTotalCap()
	return self.look_role_info.capability or 0
end

function CapabilityContrastWGData:GetDefCapData()
	return {index = 0, my_cap = 0, other_cap = 0, sys_name = "",
			btn_text = "", jump_view = "", sub_list = {},}
end

-- 显示列表
function CapabilityContrastWGData:GetCapShowList()
	local show_list = {}
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	local function get_data(cfg)
		local data = CapabilityContrastWGData:GetDefCapData()
		if IsEmptyTable(cfg) then
			return data
		end

		data.index = cfg.child_index > 0 and cfg.child_index or cfg.main_index
		data.sys_name = cfg.child_name
		data.btn_text = cfg.jump_btn_desc
		data.jump_view = cfg.jump_view
		data.main_index = cfg.main_index
		data.child_index = cfg.child_index
		local my_cap, other_cap = 0, 0
		local list = self:GetSysCapListByIndex(cfg.main_index, cfg.child_index)
		for k,v in pairs(list) do
			my_cap = self:GetMyCapByType(k) + my_cap
			other_cap = self:GetOtherCapByType(k) + other_cap
		end

		data.my_cap = my_cap
		data.other_cap = other_cap
		return data
	end

	for main_index, sub_list in pairs(self.sub_sys_map_cfg) do
		local list = {}
		for child_index, sub in pairs(sub_list) do
			if open_day >= sub.show_open_day then
				if sub.child_index == 0 then
					show_list[main_index] = get_data(sub)
				else
					list[sub.child_index] = get_data(sub)
				end
			end
		end
		if show_list[main_index] then
			show_list[main_index].sub_list = list
		end
	end

	return show_list
end
