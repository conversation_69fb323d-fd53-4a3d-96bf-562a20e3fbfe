HolyDarkSealOnceShopView = HolyDarkSealOnceShopView or BaseClass(SafeBaseView)
function HolyDarkSealOnceShopView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "layout_holy_dark_once_shop_view")
end

function HolyDarkSealOnceShopView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["onekey_buy_btn"], BindTool.Bind(self.ClickOneKeyBuy, self))

    if not self.shop_cell_list then
        self.shop_cell_list = {}
        for i = 0, 3 do
            self.shop_cell_list[i] = HolyDarkShopCell.New(self.node_list["seal_cell_" .. i])
            self.shop_cell_list[i]:SetIndex(i)
        end
    end
end

function HolyDarkSealOnceShopView:ReleaseCallBack()
	self.seal_data = nil

    if self.shop_cell_list then
        for k, v in pairs(self.shop_cell_list) do
            v:DeleteMe()
        end
        self.shop_cell_list = nil
    end
end

function HolyDarkSealOnceShopView:SetDataAndOpen(seal_data)
	self.seal_data = seal_data
    self:Open()
end

function HolyDarkSealOnceShopView:OnFlush()
	if not self.seal_data then
		return
	end

    self:FlushPanelView()
end

function HolyDarkSealOnceShopView:FlushPanelView()
    self.node_list.skill_name.text.text = string.format("【%s】",self.seal_data.seal_skill_name)
    self.node_list.skill_desc.text.text = self.seal_data.seal_skill_desc
    self.node_list.zhe_text.text.text = self.seal_data.zhekou_word

    local all_price = RoleWGData.GetPayMoneyStr(self.seal_data.seal_total_price, self.seal_data.rmb_type, self.seal_data.rmb_seq)
    self.node_list.onekey_buy_price.text.text = string.format(Language.HolyDarkWeapon.TotalPriceTxt, all_price)

    local seal_data = HolyDarkWeaponWGData.Instance:GetSealCfgBySeq(self.seal_data.seq)
    if not IsEmptyTable(seal_data) then
        for k, v in pairs(self.shop_cell_list) do
            v:SetData(seal_data[k] or {})
        end
    end

    local seal_cap = HolyDarkWeaponWGData.Instance:GetRelicSealCapabilityBySeq(self.seal_data.seq, true)
    self.node_list.cap_value.text.text = seal_cap

    local have_seal = HolyDarkWeaponWGData.Instance:GetHaveSealActBySeq(self.seal_data.seq)
    XUI.SetButtonEnabled(self.node_list["onekey_buy_btn"], not have_seal)
end

function HolyDarkSealOnceShopView:ClickOneKeyBuy()
    if not IsEmptyTable(self.seal_data) then
        RechargeWGCtrl.Instance:Recharge(self.seal_data.seal_total_price, self.seal_data.rmb_type, self.seal_data.rmb_seq)
    end
end

---------------------
HolyDarkShopCell = HolyDarkShopCell or BaseClass(BaseRender)
function HolyDarkShopCell:LoadCallBack()

end

function HolyDarkShopCell:__delete()

end

function HolyDarkShopCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetRawImagesPNG("a2_cbg_daoju_" .. self.data.seal_index)
    self.node_list.seal_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list.seal_img.raw_image:SetNativeSize()
    end)

    self.node_list.desc.text.text = self.data.desc1

    local attr_list = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.data, "attr_id", "attr_value", 1, 1)
    self.node_list["attr"].text.text = string.format(Language.HolyDarkWeapon.HolyDarkAttr, attr_list[1].attr_name, attr_list[1].value_str)
end