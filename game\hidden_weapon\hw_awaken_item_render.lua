


HWAwakenItemRender = HWAwakenItemRender or BaseClass(BaseRender)

function HWAwakenItemRender:__init(a)

	self.icon_star_arr = {}
    local star_arae = self.node_list["star_area"]
    for i = 1, 5 do
        local ui = star_arae:FindObj('star' .. i)
        table.insert(self.icon_star_arr, ui)
    end


    local node = self.node_list["node"]
    self.attr_ui_arr = {}
    for i = 1, 6 do
    	local name_txt = node:FindObj("attr_name_"..i)
    	local val_txt = name_txt:FindObj("attr_val")
    	self.attr_ui_arr[i] = {
    		name_txt = name_txt,
    		val_txt = val_txt,
    	}
    end


    self.no_active_attr_ui_arr = {}
    for i = 1, 6 do
        local name_txt = node:FindObj("no_actoive_attr_name_"..i)
        local val_txt = name_txt:FindObj("attr_val")
        self.no_active_attr_ui_arr[i] = {
            name_txt = name_txt,
            val_txt = val_txt,
        }
    end
end

function HWAwakenItemRender:LoadCallBack(a)
    --print_log(a)
end

function HWAwakenItemRender:__delete()
end


function HWAwakenItemRender:UpdateAttr(is_active)
	for k,v in pairs(self.attr_ui_arr) do
		v.name_txt:SetActive(false)
	end

    for k,v in pairs(self.no_active_attr_ui_arr) do
        v.name_txt:SetActive(not is_active)
    end

    local attr_list = AttributeMgr.GetAttributteByClass(self.data.cfg)
    local pre_list = AttributeMgr.GetAttributteByClass(self.data.pre_cfg or {})
    local attr_sort = {}
    local sort_list = AttributeMgr.SortAttribute()
    local attr_name = Language.Common.AttrName


    for k,v in pairs(sort_list) do
        local val = attr_list[v]
        if val and val > 0 then
            local pre_val = pre_list[v] or 0
            val = val - pre_val
            if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
                val = (val / 100) .. '%'
            end

            table.insert(attr_sort, {name = attr_name[v], attr_type = v, attr_val = val})
        end
    end


    for k,v in ipairs(attr_sort) do
        local ui_data = self.attr_ui_arr[k]
        ui_data.name_txt:SetActive(is_active)

        ui_data.name_txt.text.text = v.name
        ui_data.val_txt.text.text = v.attr_val


        local no_active = self.no_active_attr_ui_arr[k]
        no_active.name_txt.text.text = v.name
        no_active.val_txt.text.text = v.attr_val
    end

end


function HWAwakenItemRender:UpdateStar()
    local lv = self.data.cfg.effect_level
	for k,v in ipairs(self.icon_star_arr) do
		v:SetActive(k <= lv)
	end
end

function HWAwakenItemRender:OnFlush()
    if not self.data then
        return
    end

    local cfg = self.data.cfg

    local weapon_type = self.data.weapon_type
    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)

    local is_active = (data.protocol_equip_item.special_effect_level or 0) >= cfg.effect_level


    self:UpdateAttr(is_active)
    self:UpdateStar()


    self.node_list["icon"].image:LoadSprite(ResPath.GetSkillIconById(cfg.skill_icon))
    XUI.SetGraphicGrey(self.node_list["icon"], not is_active)


    self.node_list["effect_desc"]:SetActive(is_active)
    self.node_list["no_active_effect_desc"]:SetActive(not is_active)

    self.node_list["title_effect_bg"]:SetActive(is_active)
    self.node_list["no_active_title_effect_bg"]:SetActive(not is_active)

    self.node_list["title_attr_bg"]:SetActive(is_active)
    self.node_list["no_active_title_attr_bg"]:SetActive(not is_active)

    self.node_list["effect_desc"].text.text = cfg.skill_des
    self.node_list["no_active_effect_desc"].text.text = cfg.skill_des_dark



    if is_active then
        self.node_list["state_txt"].text.text = string.format(Language.HiddenWeapon.HiddenWeaponActive, "#bef857")
        local color = Color.New(32 / 255, 92 / 255, 14 / 255, 128 / 255)
        -- self.node_list["state_txt"].out_line.effectColor = Str2C3b("#205c0e")
        self.node_list["state_txt"].out_line.effectColor = color
        local num = self.index > 3 and self.index % 3 or self.index
        self.node_list["item_bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("xianqidiban_"..num))
    else
        self.node_list["state_txt"].text.text = string.format(Language.HiddenWeapon.HiddenWeaponNoActive, "#ff0000")
        local color = Color.New(0,0,0,0)
        self.node_list["state_txt"].out_line.effectColor = color
        self.node_list["item_bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("xqi_diban"))
    end


    self.node_list["eff"]:SetActive(is_active)
end

