---------------
--首充团购(新)
---------------
OpenFirstGroupBuying = OpenFirstGroupBuying or BaseClass(SafeBaseView)

function OpenFirstGroupBuying:__init(act_id,zodaer)
	self.act_id = act_id
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_first_group_buying")
	self:AddViewResource(0, "uis/view/act_operate_ui_prefab", "flower")
end

function OpenFirstGroupBuying:__delete()

end

function OpenFirstGroupBuying:ReleaseCallBack()
	if nil ~= self.title_list_view then
		self.title_list_view:DeleteMe()
		self.title_list_view = nil
	end

	if nil ~= self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
	self.tab_list = nil
	self.has_load = nil
	self.need_refresh = nil
end

function OpenFirstGroupBuying:OpenCallBack()
	
end

function OpenFirstGroupBuying:LoadCallBack()
	self.title_list_view = PerfectLoversListView.New(ActGroupBuyingRender, self.node_list.ph_list)
	self.title_list_view:SetSelectCallBack(BindTool.Bind1(self.GroupTitleSelectCallBack,self))
	self.title_list_view:SetDefaultSelectIndex(1)

	self.reward_list_view = AsyncListView.New(ActGroupBuyingListRender, self.node_list.ph_title_list)

	self.data_list = ServerActivityWGData.Instance:GetGroupBuyReward(self.act_id)
	if self.data_list == nil then return end
	self.tab_list = {}
	-- local role_num = {}
	local cur_value = -1
	for i,v in ipairs(self.data_list) do
		if cur_value ~= v.groupbuy_active_need_person then
			cur_value = v.groupbuy_active_need_person
			table.insert(self.tab_list, cur_value)
			-- table.insert(role_num, cur_value)
		end
	end

	if self.title_list_view then
		self.title_list_view:SetDataList(self.tab_list,3)
		-- self.title_list_view:SelectIndex(1)
	end
	-- self:RefreshView()
	self.has_load = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function OpenFirstGroupBuying:GroupTitleSelectCallBack(item)
	if not item and not item.data then return end
	local old_type = self.cur_type
	self.cur_type = self.tab_list[item:GetIndex()]
	-- self:RefreshView()
	if self.reward_list_view then
		local reward_data_list = ServerActivityWGData.Instance:GetCurrentActivityDataList(self.act_id, self.data_list, self.cur_type)  -- 重點
		if self.cur_type ~= old_type then
			self.reward_list_view:SetDataList(reward_data_list, 2)
		else
			self.reward_list_view:SetDataList(reward_data_list, 2)
		end
	end
end

-- 刷新当前视图
function OpenFirstGroupBuying:RefreshView(param_list)
	if not self:IsOpen() then return end
	if not self.has_load then
		self.need_refresh = true
		return
	end
	-- if param_list ~= nil and param_list.flush_param == "change_index" then
		self:RefreshTopDesc()
	-- end

	if self.title_list_view and next(self.tab_list) then
		self.title_list_view:SetDataList(self.tab_list, 2)
	end
	if self.reward_list_view then
		local reward_data_list = ServerActivityWGData.Instance:GetCurrentActivityDataList(self.act_id, self.data_list, self.cur_type)  -- 重點
		self.reward_list_view:SetDataList(reward_data_list, 2)
	end
end

function OpenFirstGroupBuying:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)

	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 60)
		end
		self.node_list.version_act_time.text.text = star_str .. "————" .. end_str
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text =(open_act_cfg.top_desc)
	end
end

--------------------------------ActGroupBuyingRender
ActGroupBuyingRender = ActGroupBuyingRender or BaseClass(BaseRender)
function ActGroupBuyingRender:__init()
	self.role_num = 0
end 

function ActGroupBuyingRender:CreateChild()
	BaseRender.CreateChild(self)

	if self:IsSelect() then
		self:OnSelectChange(true)
	end
end

function ActGroupBuyingRender:OnFlush()
	if not self.data then return end
	if self.role_num ~= 0 then
		self.node_list.num.text.text = self.role_num
	end

	local is_get = ServerActivityWGData.Instance:GetGroupBuyingTitleFlag(self.data)
	-- self.node_list.img_buy_remind:SetVisible(is_get)
	self:SetRoleNum(self.data)
	self:OnSelectChange(self:IsSelectIndex())
end

function ActGroupBuyingRender:SetRoleNum(role_num)
	if self.node_list.num ~= nil then
		self.node_list.num.text.text = role_num
	end
	self.role_num = role_num
end

function ActGroupBuyingRender:OnSelectChange(is_select)
	if is_select ~= nil then 
		self.node_list.img_high.image.enabled = is_select
	end 
end

function ActGroupBuyingRender:CreateSelectEffect()
end


--------------------------------ActGroupBuyingListRender   -------------------------   list列表
ActGroupBuyingListRender = ActGroupBuyingListRender or BaseClass(BaseRender)
function ActGroupBuyingListRender:__init()
	self:CreateChild()
end

function ActGroupBuyingRender:__delete()
	if self.item_cells then
		for k,v in pairs(self.item_cells) do
			v:DeleteMe()
		end
	end

	self.btn_effect = nil
end

function ActGroupBuyingListRender:CreateChild()
	self.item_cells = {}
	local param
	for i = 0, 2 do
		param = self.node_list["ph_cell_" .. i]
		self.item_cells[i] = ItemCell.New(param)
		self.item_cells[i]:SetIsShowTips(true)
	end
	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind1(self.OnClickLingQuCallBack, self))
end

function ActGroupBuyingListRender:OnFlush()
	if not self.data then return end

	for i = 0, 2 do
		local data = self.data.reward_item[i]
		if data then
			self.item_cells[i]:SetVisible(true)
			self.item_cells[i]:SetData({item_id = data.item_id, num = data.num, is_bind = 0})
		else
			self.item_cells[i]:SetVisible(false)
		end
	end

	self.node_list.rich_desc.text.text = self.data.des or ""     --, 20, COLOR3B.L_ORANGE)
	self.node_list.num_desc.text.text = self.data.num or ""
	if self.data.img_flag == 1 then
		self.node_list.img_desc.image:LoadSprite(ResPath.GetOpenServerName('first_group_buying_text_1'))
		self.node_list.spe_img_desc:SetActive(false)
	elseif self.data.img_flag == 2 then
		self.node_list.img_desc.image:LoadSprite(ResPath.GetOpenServerName('first_group_buying_text_2'))
		self.node_list.spe_img_desc:SetActive(false)

	elseif self.data.img_flag == 3 then
		self.node_list.img_desc.image:LoadSprite(ResPath.GetOpenServerName('first_group_buying_text_3'))
		self.node_list.spe_img_desc:SetActive(true)
		self.node_list.num.text.text = self.data.img_num
	end
	-- self.btn_effect:SetVisible(self.data.effect)
	XUI.SetButtonEnabled(self.node_list.btn_lingqu, self.data.effect)   -- 0 true 表示可領取

	local flag = ServerActivityWGData.Instance:GetBuyRewardFlag()
	self.node_list.btn_lingqu:SetActive(flag[32 - self.data.seq] == 0)
	self.node_list.img_lingqu_flag:SetActive(flag[32 - self.data.seq] == 1)

end

function ActGroupBuyingListRender:OnClickLingQuCallBack()
	ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_FIRST_CHONGZHI_GROUP_BUY, self.data.seq)
end

function ActGroupBuyingListRender:CreateSelectEffect()
end