NewFestivalRaffleWGData = NewFestivalRaffleWGData or BaseClass()
NewFestivalRaffleWGData.REWARD_NUM = 8

function NewFestivalRaffleWGData:__init()
	if NewFestivalRaffleWGData.Instance then
		ErrorLog("[NewFestivalRaffleWGData] Attemp to create a singleton twice !")
	end

	NewFestivalRaffleWGData.Instance = self
	self:InitCfg()

	self.grade = 0
	self.fetch_flag = 0
	self.leiji_point = 0
	self.cur_point = 0

	RemindManager.Instance:Register(RemindName.NewFestivalRaffle, BindTool.Bind(self.ShowRed, self))
end

function NewFestivalRaffleWGData:__delete()
	NewFestivalRaffleWGData.Instance = nil

	self.grade = nil
	self.fetch_flag = nil
	self.leiji_point = nil
	self.cur_point = nil

	RemindManager.Instance:UnRegister(RemindName.NewFestivalRaffle)
end

function NewFestivalRaffleWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("new_festival_activity_shengdian_config_auto")
	self.open_cfg = cfg.open_day
	self.reward_pool_cfg = ListToMapList(cfg.reward_pool, "grade")-- 奖池
	self.draw_type_cfg = ListToMap(cfg.draw_type, "draw_button")
	self.reward_cfg = ListToMap(cfg.reward_pool, "grade", "draw_button")
	self.rebate_cfg = ListToMap(cfg.leiji_points_reward, "grade", "seq")
	self.model_cfg = ListToMap(cfg.model_display, "grade")
	self.baodi_cfg = ListToMap(cfg.baodi_reward, "grade")
	self.probability_cfg = ListToMapList(cfg.item_random_desc, "grade")
end

function NewFestivalRaffleWGData:GetRewardPoolCfg()
	local grade = self:GetCurGrade()
	return self.reward_pool_cfg[grade]
end

function NewFestivalRaffleWGData:GetDrawTypeCfg()
	return self.draw_type_cfg
end

function NewFestivalRaffleWGData:GetProbabilityInfo()
	local grade = self:GetCurGrade()
	return self.probability_cfg[grade]
end

function NewFestivalRaffleWGData:GetRewardById(grade, draw_button)
	return (self.reward_cfg[grade] or {})[draw_button]
end

function NewFestivalRaffleWGData:GetModelCfg()
	local grade = self:GetCurGrade()
	return self.model_cfg[grade]
end

function NewFestivalRaffleWGData:GetBaoDiCfg()
	local grade = self:GetCurGrade()
	return self.baodi_cfg[grade]
end

function NewFestivalRaffleWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.draw_btn_index = btn_index
	end

	return self.draw_btn_index
end

function NewFestivalRaffleWGData:GetDisPlayCfg()
	local pool_cfg = self:GetRewardPoolCfg()
	local list_data = {}
	if not pool_cfg then
		return list_data
	end

	for k,v in pairs(pool_cfg) do
		if v.need_display == 1 then
			table.insert(list_data, v)
		end
	end

	return list_data
end

function NewFestivalRaffleWGData:CalDrawRewardList(protocol, draw_button)
	local data_list = {}
	if not protocol or not protocol.count or protocol.count <= 0 then
		return data_list
	end

	local grade = self:GetCurGrade()
	local cfg = self:GetRewardById(grade, draw_button)
	for i, v in ipairs(protocol.reward_list) do
		if cfg then
			if v.item_id == cfg.reward_item[0].item_id then
				local temp = cfg.reward_item[0]
				table.insert(data_list, temp)
			end
		else
			print_error("错误数据 请检查新节日盛典配置 grade, draw_button", grade, draw_button)
		end
	end

	return data_list
end

function NewFestivalRaffleWGData:GetPointRewardCfg()
	local grade = self:GetCurGrade()
	local rebate_cfg = self.rebate_cfg[grade]
	local have_points = self.leiji_point
	local list = {}
	for i = 0, #rebate_cfg do
		local temp = {}
		if have_points >= rebate_cfg[i].need_points then
			if not self:GetFanliHasGet(rebate_cfg[i].seq) then
				temp.seq = rebate_cfg[i].seq
			else
				temp.seq = 1000 + rebate_cfg[i].seq
			end
		else
			temp.seq = 100 + rebate_cfg[i].seq
		end

		temp.data = rebate_cfg[i]
		temp.need_points = rebate_cfg[i].need_points
		temp.has_get = self:GetFanliHasGet(rebate_cfg[i].seq) and 1 or 0
		table.insert(list, temp)
	end

	if not IsEmptyTable(list) then
		table.sort(list, SortTools.KeyLowerSorter("seq"))
	end

	return list
end


function NewFestivalRaffleWGData:SetAllInfo(protocol)
	self.grade = protocol.grade														-- 档位
	self.is_skip_comic = protocol.is_skip_comic										-- 跳过动画？
	self.fetch_flag = protocol.leiji_reward_fetch_flag								-- 累计奖励领取标记
	self.leiji_point = protocol.leiji_point											-- 累计积分值
	self.cur_point = protocol.cur_point												-- 当前积分值
end

function NewFestivalRaffleWGData:GetFanliHasGet(index)
	return bit:_and(self.fetch_flag or 0, bit:_lshift(1, index)) ~= 0
end

function NewFestivalRaffleWGData:GetCurGrade()
	return self.grade
end

function NewFestivalRaffleWGData:GetLeiJiPoint()
	return self.leiji_point
end

function NewFestivalRaffleWGData:GetCurPoint()
	return self.cur_point
end

function NewFestivalRaffleWGData:GetItemDataChangeList()
	if not self.item_data_change_list then
		self.item_data_change_list = {}
		local cfg = self:GetDrawTypeCfg()
		for i, v in pairs(cfg) do
			table.insert(self.item_data_change_list, v.draw_item.item_id)
		end
	end

	return self.item_data_change_list
end

function NewFestivalRaffleWGData:ShowRed()
	if self:GetEnoughItem() then
		return 1
	end

	if self:GetFanliRed() then
		return 1
	end

	return 0
end

function NewFestivalRaffleWGData:GetEnoughItem()
	local draw_type = self:GetDrawTypeCfg()
	for i, v in pairs(draw_type) do
		if ItemWGData.Instance:GetItemNumInBagById(v.draw_item.item_id) > 0 then
			return true
		end
	end

	return false
end

function NewFestivalRaffleWGData:GetFanliRed()
	local cur_grade = self:GetCurGrade()
	local leiji_point = self:GetLeiJiPoint()
	if not self.rebate_cfg[cur_grade] then
		return false
	end

	for i, v in ipairs(self.rebate_cfg[cur_grade]) do
		if leiji_point >= v.need_points then
			if not self:GetFanliHasGet(v.seq) then
				return true
			end
		end
	end

	return false
end