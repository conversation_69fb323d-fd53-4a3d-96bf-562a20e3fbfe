local SCORE_REWARD_TYPE = {
    SCORE_RANK = 1,
    PERSON_SCORE = 2,
}

function UltimateBattlefieldRewardView:LoadIndexCallBackScoreReward()
    if not self.score_type_list then
        self.score_type_list = AsyncListView.New(UlScoreTypeRender, self.node_list.score_type_list)
        self.score_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectTypeItemCB, self))
    end

    if not self.score_reward_list then
        self.score_reward_list = AsyncListView.New(UlScoreRankRewardRender, self.node_list.score_reward_list)
    end
end

---类型点击
function UltimateBattlefieldRewardView:OnSelectTypeItemCB(item)
	if nil == item or nil == item.data then
		return
	end

    local index = item.index
    if self.cur_select_type == index then
        return
    end

    self.cur_select_type = index
    self:FlushRewardList()
end


function UltimateBattlefieldRewardView:ShowIndexCallBackScoreReward()

end

function UltimateBattlefieldRewardView:ReleaseScoreReward()
	if self.score_reward_list then
		self.score_reward_list:DeleteMe()
		self.score_reward_list = nil
	end

    if self.score_type_list then
		self.score_type_list:DeleteMe()
		self.score_type_list = nil
	end

    self.cur_select_type = nil
end

function UltimateBattlefieldRewardView:OnFlushScoreReward()
    self.score_type_list:SetDataList(Language.UltimateBattlefield.TabGropRewardType)
    if not self.cur_select_type then
        self.score_type_list:JumpToIndex(1, 5)
    end
end

function UltimateBattlefieldRewardView:FlushRewardList()
    local rank_list = nil

    if self.cur_select_type == SCORE_REWARD_TYPE.SCORE_RANK then
        rank_list = UltimateBattlefieldWGData.Instance:GetAllScoreRankReward()
    else
        rank_list = UltimateBattlefieldWGData.Instance:GetAllScoreReward()
    end

    if rank_list then
        self.score_reward_list:SetDataList(rank_list)
    end
end

---------UlScoreTypeRender------------------
UlScoreTypeRender = UlScoreTypeRender or BaseClass(BaseRender)
function UlScoreTypeRender:OnFlush()
    if not self.data then
        return 
    end

    local str = self.data

    if self.data.is_stage then
        local interval_str = NumberToChinaNumber(self.index)
        str = string.format(Language.UltimateBattlefield.RoundStr, interval_str) 
    end

    self.node_list.text_btn.text.text = str
    self.node_list.text_high_btn.text.text = str
end


function UlScoreTypeRender:OnSelectChange(is_select)
    self.node_list.noraml:SetActive(not is_select)
    self.node_list.Image_hl:SetActive(is_select)
end

----------UlScoreRankRewardRender-------------
UlScoreRankRewardRender = UlScoreRankRewardRender or BaseClass(BaseRender)
function UlScoreRankRewardRender:__init()
    if not self.item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    end
end

function UlScoreRankRewardRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end


function UlScoreRankRewardRender:OnFlush()
    if not self.data then
        return 
    end

    local real_reward_item = {}
    for _, reward_data in pairs(self.data.reward_item) do
        if reward_data and reward_data.item_id then
            table.insert(real_reward_item, reward_data)
        end
    end

    self.item_list:SetDataList(real_reward_item)
    if self.data.max_rank and self.data.min_rank then
        if self.data.max_rank > self.data.min_rank then
            self.node_list.score_text.text.text = string.format(Language.UltimateBattlefield.SocreRanksStr[2], self.data.min_rank, self.data.max_rank)
        else
            self.node_list.score_text.text.text = string.format(Language.UltimateBattlefield.SocreRanksStr[1], self.data.min_rank)
        end
    else
        self.node_list.score_text.text.text = string.format(Language.UltimateBattlefield.ScoreStr, self.data.need_score)
    end
end
