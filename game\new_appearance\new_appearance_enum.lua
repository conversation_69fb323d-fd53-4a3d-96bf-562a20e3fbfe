-- 角色形象类型（前后端一致）
ROLE_APPE_TYPE = {
	LINGCHONG = 0,							-- 灵宠
	MOUNT = 1,								-- 幻化坐骑
	WING = 2,								-- 羽翼
	FABAO = 3,								-- 法宝
	SHENBING = 4,							-- 武器
	LINGGONG = 5,							-- 灵弓
	BABY = 6,								-- 宝宝
	IMP = 7, 								-- 小鬼
	JINGJIE = 8,							-- 境界等级
	-- 9 战斗坐骑（废弃）
	LINGQI = 10,							-- 灵骑
	LINGYI = 11,							-- 灵翼
    FASHION = 12,							-- 时装
    FOOT = 13,								-- 足迹
    HALO = 14,								-- 光环
    LINGCHONG_NORMAL = 15,					-- 灵宠2 正常进阶的灵宠
    WING_NORMAL = 16,						-- 角色面板中的4个模型
	-- 17 战斗坐骑2（废弃）
	MOUNT_NORMAL = 18,						-- 进阶坐骑
	BIANSHEN = 19,							-- 变身特殊形象 + 变身初始形象
	SHENGQI = 20,							-- 圣器
    DIVINEWEAPON = 21,						-- 天兵
    FEICHONG = 22,							-- 飞宠
    FACE = 23,								-- 脸
    YAO = 24,								-- 腰
    WEI = 25,								-- 尾
    HAND = 26,								-- 手
    JIANZHEN = 27,							-- 剑阵
    TIANSHENSHENQI = 28,					-- 天神神器
    TIANSHENSHENQIHUANHUA = 29,				-- 天神神器幻化
    SHENQI_IMAGE = 30,						-- 天神神器当前形象
    PHOTO = 31,						        -- 相框
    BUBBLE = 32,							-- 气泡
    HUAKUN = 33,							-- 化鲲
    FAME = 34,				                -- 国家版图名望
    XIANJIE_EQUIP_TIANSHU = 35,				-- 仙界装备天书变化
    TITLE = 36,				                -- 称号
	GOD_OR_DEMON = 37,		                -- 一念神魔
	WU_HUN_ZHEN_SHEN = 38,					-- 武魂真身
	BEAST = 40,								-- 灵兽
	SHUANGSHENG_TIANSHEN = 41,				-- 双生天神 
	STAGELEVEL = 42,                        -- 境界等级
	WUHUNHUNZHEN = 43,						-- 武魂魂阵
	LONGZHU_SKILL_LEVEL = 44,               -- 龙珠（星陨怒兵）技能等级
	-- 自定义
	
    RELIC_TYPE = 900,						-- 圣器
	ZHIZUN = 901,				            -- 至尊领域
	CANGMING = 902,                         -- 五行沧溟
	NEW_FIGHT_MOUNT = 903,                  -- 新战斗坐骑
	BIANSHEN_XIUWEI = 904,					-- 变身特殊形象-修为变身
	THUNDER_MANA = 905,                     -- 雷法
	ESOTERICA = 906,                        -- 仙法 秘籍
	GOD_BODY = 1000,						-- 神体
	BEAST_SKIN = 1100,						-- 幻兽皮肤
}

-- 进阶类型
-- 此定义匹配【进阶配置】定义的类型 type
ADVANCED_DATA_TYPE = {
	WING = 0,							-- 羽翼
	FABAO = 1,							-- 法宝
	SHENWU = 2,							-- 神兵
	LINGGONG  = 3,						-- 灵弓
	LINGQI = 5,							-- 灵骑
	LINGYI = 6,							-- 灵翼
	JIANZHEN = 7,						-- 剑阵
}

-- 时装类型
-- 此定义匹配【时装配置】定义的部位类型 part_type
SHIZHUANG_TYPE = {
	WUQI = 0,							--废弃
	BODY = 1,							--时装
	FOOT = 2,							--足迹
	HALO = 3,							--光环
	WING = 4,							--羽翼
	FABAO = 5,							--法宝
	SHENBING = 6,						--（武器/神兵）（时装配置字段shifoushengb 0/1 区分）
	LINGGONG = 7,						--灵弓
	PHOTOFRAME = 8,						--相框
	BUBBLE = 9, 						--气泡
	MASK = 10,							--脸饰
	BELT = 11,							--腰饰
	WEIBA = 12,							--尾巴
	SHOUHUAN = 13,						--手环
	JIANZHEN = 14,						--剑阵（背饰）
	MAX = 15,

	TitleUpgrade = 100,					--客户端定义（疑似废弃）
	Mount = 101,						--坐骑（自定义）
}

-- 角色形象类型 映射 时装类型
ROLE_APPE_2_SHIZHUANG_TYPE = {
	[ROLE_APPE_TYPE.FASHION] = SHIZHUANG_TYPE.BODY,							--时装
	[ROLE_APPE_TYPE.FOOT] = SHIZHUANG_TYPE.FOOT,							--足迹
	[ROLE_APPE_TYPE.HALO] = SHIZHUANG_TYPE.HALO,							--光环
	[ROLE_APPE_TYPE.WING] = SHIZHUANG_TYPE.WING,							--羽翼
	[ROLE_APPE_TYPE.FABAO] = SHIZHUANG_TYPE.FABAO,							--法宝
	[ROLE_APPE_TYPE.SHENBING] = SHIZHUANG_TYPE.SHENBING,					--（武器/神兵）（时装配置字段shifoushengb 0/1 区分）
	[ROLE_APPE_TYPE.LINGGONG] = SHIZHUANG_TYPE.LINGGONG,					--灵弓
	[ROLE_APPE_TYPE.PHOTO] = SHIZHUANG_TYPE.PHOTOFRAME,						--相框
	[ROLE_APPE_TYPE.BUBBLE] = SHIZHUANG_TYPE.BUBBLE, 						--气泡
	[ROLE_APPE_TYPE.FACE] = SHIZHUANG_TYPE.MASK,							--脸饰
	[ROLE_APPE_TYPE.YAO] = SHIZHUANG_TYPE.BELT,								--腰饰
	[ROLE_APPE_TYPE.WEI] = SHIZHUANG_TYPE.WEIBA,							--尾巴
	[ROLE_APPE_TYPE.HAND] = SHIZHUANG_TYPE.SHOUHUAN,						--手环
	[ROLE_APPE_TYPE.JIANZHEN] = SHIZHUANG_TYPE.JIANZHEN,					--剑阵（背饰）
}

-- 时装类型 映射 进阶类型
SHIZHUANG_2_ADVANCED_TYPE = {
	[SHIZHUANG_TYPE.WING] = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING,
	[SHIZHUANG_TYPE.FABAO] = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO,
	[SHIZHUANG_TYPE.SHENBING] = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING,
	[SHIZHUANG_TYPE.JIANZHEN] = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN,
}

ADVANCED_TYPE_2_TABINDEX = {
	[ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING] = TabIndex.new_appearance_upgrade_wing,
	[ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO] = TabIndex.new_appearance_upgrade_fabao,
	[ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING] = TabIndex.new_appearance_upgrade_shenbing,
	[ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN] = TabIndex.new_appearance_upgrade_jianzhen,
}

-- 定义追溯到上古时期
-- 珍稀骑宠、鲲
MOUNT_LINGCHONG_APPE_TYPE = {
	MOUNT = 0,
	LINGCHONG = 1,
	KUN = 2,
}

-- 骑宠功能类型
QICHONG_FUNC_TYPE = {
	UPSTAR = 1,
	UPLEVEL = 2,
	HUALING = 3,
}

-- 新形象标签 - 装扮
NEW_APPEARANCE_ZHUANGBAN_TAB_INDEX = {
    TabIndex.new_appearance_zhuangban_shouhuan,
    TabIndex.new_appearance_zhuangban_foot,
    TabIndex.new_appearance_zhuangban_photoframe,
    TabIndex.new_appearance_zhuangban_bubble,
}

NEW_WARDROBE_FASHION_TYPE = {
	WARDROBE_FASHION_BODY = 1001,
	WARDROBE_FASHION_FOOT = 1002,
	WARDROBE_FASHION_HALO = 1003,
	WARDROBE_FASHION_WING = 1004,
	WARDROBE_FASHION_FABAO = 1005,
	WARDROBE_FASHION_SHENBING = 1006,
	WARDROBE_FASHION_LINGGONG = 1007,
	WARDROBE_FASHION_PHOTOFRAME = 1008,
	WARDROBE_FASHION_BUBBLE = 1009,
	WARDROBE_FASHION_MASK = 1010,
	WARDROBE_FASHION_BELT = 1011,
	WARDROBE_FASHION_WEIBA = 1012,
	WARDROBE_FASHION_SHOUHUAN = 1013,
	WARDROBE_FASHION_JIANZHEN = 1014,
}

-- 新形象页签数据
NEW_APPEARANCE_TAB_DATA = {
	----[[进阶
	[TabIndex.new_appearance_upgrade_wing] = {
		ad_type = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING,
		fashion_type = SHIZHUANG_TYPE.WING,
		remind_name = RemindName.NewAppearance_Upgrade_Wing,
		strengthen_type = MAINUI_TIP_TYPE.ADVANCED_WING,
		lingzhi_type = LINGZHI_SKILL_TYPE.WING,
		lz_remind_name = RemindName.LingZhi_Wing,
		lz_act_remind_name = RemindName.LingZhi_Wing_Act,
		fun_name = FunName.new_appearance_upgrade_wing,
	},

	[TabIndex.new_appearance_upgrade_fabao] = {
		ad_type = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO,
		fashion_type = SHIZHUANG_TYPE.FABAO,
		strengthen_type = MAINUI_TIP_TYPE.ADVANCED_FABAO,
		remind_name = RemindName.NewAppearance_Upgrade_FaBao,
		lingzhi_type = LINGZHI_SKILL_TYPE.FABAO,
		lz_remind_name = RemindName.LingZhi_FaBao,
		lz_act_remind_name = RemindName.LingZhi_FaBao_Act,
		fun_name = FunName.new_appearance_upgrade_fabao,
	},

	[TabIndex.new_appearance_upgrade_shenbing] = {
		ad_type = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING,
		fashion_type = SHIZHUANG_TYPE.SHENBING,
		strengthen_type = MAINUI_TIP_TYPE.ADVANCED_SHENBING,
		remind_name = RemindName.NewAppearance_Upgrade_ShenBing,
		lingzhi_type = LINGZHI_SKILL_TYPE.SHENBING,
		lz_remind_name = RemindName.LingZhi_ShenBing,
		lz_act_remind_name = RemindName.LingZhi_JianZhen_Act,
	},

	[TabIndex.new_appearance_upgrade_jianzhen] = {
		ad_type = ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN,
		fashion_type = SHIZHUANG_TYPE.JIANZHEN,
		strengthen_type = MAINUI_TIP_TYPE.ADVANCED_JIANZHEN,
		remind_name = RemindName.NewAppearance_Upgrade_JianZhen,
		lingzhi_type = LINGZHI_SKILL_TYPE.JIANZHEN,
		lz_remind_name = RemindName.LingZhi_JianZhen,
		lz_act_remind_name = RemindName.LingZhi_ShenBing_Act,
	},
	--]]

	----[[进阶骑宠
	[TabIndex.new_appearance_upgrade_lingchong] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG,
		strengthen_type = MAINUI_TIP_TYPE.ROLE_LINGCHONG,
		remind_name = RemindName.NewAppearance_Upgrade_LingChong,
	},

	[TabIndex.new_appearance_upgrade_mount] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
		strengthen_type = MAINUI_TIP_TYPE.ROLE_ZUOQI,
		remind_name = RemindName.NewAppearance_Upgrade_Mount,
		fun_name = FunName.new_appearance_upgrade_mount,
	},
	--]]

	----[[珍稀骑宠、鲲
	[TabIndex.new_appearance_mount_upstar] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
		strengthen_type = MAINUI_TIP_TYPE.MOUNT_UPSTAR,
		remind_name = RemindName.NewAppearance_Mount_Upstar,
	},

	[TabIndex.new_appearance_mount_upgrade] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
		strengthen_type = MAINUI_TIP_TYPE.MOUNT_UPLEVEL,
		remind_name = RemindName.NewAppearance_Mount_Upgrade,
	},

	[TabIndex.new_appearance_mount_hualing] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
		strengthen_type = MAINUI_TIP_TYPE.MOUNT_HuaLing,
		remind_name = RemindName.NewAppearance_Mount_HuaLing,
	},

	[TabIndex.new_appearance_lingchong_upstar] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG,
		strengthen_type = MAINUI_TIP_TYPE.LINGCHONG_UPSTAR,
		remind_name = RemindName.NewAppearance_LingChong_Upstar,
	},

	[TabIndex.new_appearance_lingchong_upgrade] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG,
		strengthen_type = MAINUI_TIP_TYPE.LINGCHONG_UPLEVEL,
		remind_name = RemindName.NewAppearance_LingChong_Upgrade,
	},

	[TabIndex.new_appearance_lingchong_hualing] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG,
		strengthen_type = MAINUI_TIP_TYPE.LINGCHONG_HuaLing,
		remind_name = RemindName.NewAppearance_LingChong_HuaLing,
	},

	[TabIndex.new_appearance_kun_upstar] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.KUN,
		strengthen_type = MAINUI_TIP_TYPE.KUN_UPSTAR,
		remind_name = RemindName.NewAppearance_Kun_Upstar,
	},

	[TabIndex.new_appearance_kun_upgrade] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.KUN,
		strengthen_type = MAINUI_TIP_TYPE.KUN_UPLEVEL,
		remind_name = RemindName.NewAppearance_Kun_Upgrade,
	},

	[TabIndex.new_appearance_kun_hualing] = {
		qc_type = MOUNT_LINGCHONG_APPE_TYPE.KUN,
		strengthen_type = MAINUI_TIP_TYPE.KUN_HuaLing,
		remind_name = RemindName.NewAppearance_Kun_HuaLing,
	},
	--]]

	----[[时装
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_BODY] = {
		fashion_type = SHIZHUANG_TYPE.BODY,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_BODY,
		remind_name = RemindName.NewAppearance_WaiGuan_Body,
		guide_module_name = GuideModuleName.WardrobeView,
		jump_index = TabIndex.wardrobe_fashion,
		open_param = 1,
	},
	
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_SHENBING] = {
		fashion_type = SHIZHUANG_TYPE.SHENBING,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_WUQI,
		remind_name = RemindName.NewAppearance_WaiGuan_Weapon,
		guide_module_name = GuideModuleName.WardrobeView,
		jump_index = TabIndex.wardrobe_fashion,
		open_param = 2,
	},
	
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_HALO] = {
		fashion_type = SHIZHUANG_TYPE.HALO,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_GUANGHUAN,
		remind_name = RemindName.NewAppearance_WaiGuan_Halo,
		guide_module_name = GuideModuleName.NewAppearanceHaloWGView,
		jump_index = 0,
	},
	
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_WING] = {
		fashion_type = SHIZHUANG_TYPE.WING,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_WING,
		remind_name = RemindName.NewAppearance_WaiGuan_Wing,
		guide_module_name = GuideModuleName.WardrobeView,
		jump_index = TabIndex.wardrobe_jewelry,
		open_param = 3,
	},
	
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_FABAO] = {
		fashion_type = SHIZHUANG_TYPE.FABAO,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_FABAO,
		remind_name = RemindName.NewAppearance_WaiGuan_FaBao,
		guide_module_name = GuideModuleName.WardrobeView,
		jump_index = TabIndex.wardrobe_jewelry,
		open_param = 4,
	},
	
	-- [NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_SHENBING] = {
	-- 	fashion_type = SHIZHUANG_TYPE.SHENBING,
	-- 	is_shenbing = true,
	-- 	strengthen_type = MAINUI_TIP_TYPE.FASHION_SHENBING,
	-- 	remind_name = RemindName.NewAppearance_WaiGuan_ShenBing,
	-- },
	
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_JIANZHEN] = {
		fashion_type = SHIZHUANG_TYPE.JIANZHEN,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_JIANZHEN,
		remind_name = RemindName.NewAppearance_WaiGuan_JianZhen,
		guide_module_name = GuideModuleName.WardrobeView,
		jump_index = TabIndex.wardrobe_jewelry,
		open_param = 3,
	},
	
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_MASK] = {
		fashion_type = SHIZHUANG_TYPE.MASK,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_MASK,
		remind_name = RemindName.NewAppearance_ZhuangBan_Mask,
		guide_module_name = GuideModuleName.WardrobeView,
		jump_index = TabIndex.wardrobe_jewelry,
		open_param = 1,
	},
	
	[NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_BELT] = {
		fashion_type = SHIZHUANG_TYPE.BELT,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_BELT,
		remind_name = RemindName.NewAppearance_ZhuangBan_Belt,
		guide_module_name = GuideModuleName.WardrobeView,
		jump_index = TabIndex.wardrobe_jewelry,
		open_param = 2,
	},
	
	-- [TabIndex.new_appearance_zhuangban_weiba] = {
	-- 	fashion_type = SHIZHUANG_TYPE.WEIBA,
	-- 	strengthen_type = MAINUI_TIP_TYPE.FASHION_WEIBA,
	-- 	remind_name = RemindName.NewAppearance_ZhuangBan_WeiBa,
	-- },
	
	[TabIndex.new_appearance_zhuangban_shouhuan] = {
		fashion_type = SHIZHUANG_TYPE.SHOUHUAN,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_SHOUHUAN,
		remind_name = RemindName.NewAppearance_ZhuangBan_ShouHuan,
	},
	
	[TabIndex.new_appearance_zhuangban_foot] = {
		fashion_type = SHIZHUANG_TYPE.FOOT,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_FOOT,
		remind_name = RemindName.NewAppearance_ZhuangBan_Foot,
	},
	
	[TabIndex.new_appearance_zhuangban_photoframe] = {
		fashion_type = SHIZHUANG_TYPE.PHOTOFRAME,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_PHOTOFRAME,
		remind_name = RemindName.NewAppearance_ZhuangBan_PhotoFrame,
	},
	
	[TabIndex.new_appearance_zhuangban_bubble] = {
		fashion_type = SHIZHUANG_TYPE.BUBBLE,
		strengthen_type = MAINUI_TIP_TYPE.FASHION_BUBBLE,
		remind_name = RemindName.NewAppearance_ZhuangBan_Bubble,
	},
	--]]
}

-- 进阶 - 操作类型
UPGRADE_REQ_TYPE = {
	UPLEVEL = 0,					-- 进阶升级 param1:type  param3:item_id
	USE_SHUXINGDAN = 1,				-- 使用属性丹 param1:type param2:属性丹类型 param2 数量
	USE_IMAGE = 2,					-- 使用形象 param1:type param2 image_id param3(进阶系的传0 / 时装系需传时装index)
	UPSKILL = 3,					-- 请求技能升级 param1:type param2 skill_id
	APPEARANCE_HIDE = 4, 			-- 隐藏场景显示的外观
}

-- 时装 - 操作类型
FASHION_OPERATE = {
	REQ_INFO = 0,					-- 时装请求信息(part)
	USE = 1,						-- 时装使用(part, index)
	UPLEVEL = 2,					-- 时装升级(part, index)
	SKILL_USE = 3,					-- 时装技能使用
	SKILL_RESET = 4,				-- 时装技能重置
	RESLOVE = 5,					-- 回收信息
	SHIZHUANG_BUY = 6,				--// 购买时装 param1: part_type param2: index
	SHIZHUANG_USE_PROJECT = 7,		--// 时装方案使用 param1: project_id
}

-- 坐骑 - 操作类型
MOUNT_OPERA_TYPE = {
	ALL_INFO = 0,
	UPSTAR = 1,						-- param1 升星物品id
	USE_SHUXINGDAN = 2,				-- param1 格子索引 param2 数量
	UPGRADE_SPECIAL_IMG = 3,		-- param1 形象索引 param2 升阶物品id
	USE_IMAGE = 4,					-- param1 形象id param2(进阶系的传-1 / 幻化系传image_id)
	UNUSE_IMAGE = 5,				-- param1 形象id
	ZHUAN_SHENG = 6,				-- 转生
	USE_PUT_ON = 7,					-- param1 形象id
	TASK_OFF = 8,					-- param1 形象id
	COMPOSE = 9,					-- 转生
	FUHUN = 10,						-- 附魂 param1 装备槽位index
	PUTON = 11,						-- 穿戴 param1 背包index
	UP_SKILL = 12,					-- 坐骑技能升级
	SPECIAL_IMAGE_UP_STAR = 13,		-- 坐骑升星
	MOUNT_OPERA_TYPE_BUY = 14,		-- 购买 param1: image_id param2: num
}

-- 灵宠 - 操作类型
LINGCHONG_OPERA_TYPE = {
	ALL_INFO = 0,
	UPSTAR = 1,						-- param1 升星物品id
	USE_SHUXINGDAN = 2,				-- param1 格子索引 param2 数量
	UPGRADE_SPECIAL_IMG = 3,		-- param1 形象索引 param2 升阶物品id
	USE_IMAGE = 4,					-- param1 形象id
	UNUSE_IMAGE = 5,				-- param1 形象id
	ZHUAN_SHENG = 6,				-- 转生
	USE_PUT_ON = 7,					-- param1 形象id
	TASK_OFF = 8,					-- param1 形象id
	COMPOSE = 9,					-- 转生
	FUHUN = 10,						-- 附魂 param1 装备槽位index
	PUTON = 11,						-- 穿戴 param1 背包index
	SKILL_UP_LEVEL = 12,			-- 灵宠技能升级
	SPECIAL_IMAGE_UP_STAR = 13,		-- 特殊灵宠升星
}

-- 鲲 - 操作类型
KUN_OPERA_TYPE = {
	ACTIVE = 0,						--激活  param1:鲲id
	UP_LV = 1,						--升阶  param1:鲲id
	UP_STAR = 2,					--升星  param1:鲲id
	UP_XL = 3,						--洗练	param1:鲲id param2:是否使用仙玉 【0，1】param3:int整数，个十百千万，从个位开始分别用来标记第【1，5】条属性是否锁定，1表示锁定，0表示未锁定
	USE_KUN = 4,					--使用  param1:鲲id
	ACTIVE_SKILL = 5,				--技能激活 param1:鲲id param2:技能index
	KUN_OPERA_TYPE_BUY = 6,			--购买		param1:鲲id param2:num
}

ROLE_APPE_TYPE_2_IDX = {
	[ROLE_APPE_TYPE.FASHION] 	= SHIZHUANG_TYPE.BODY,
	[ROLE_APPE_TYPE.SHENBING] 	= SHIZHUANG_TYPE.SHENBING,
	[ROLE_APPE_TYPE.HALO]  		= SHIZHUANG_TYPE.HALO,
	[ROLE_APPE_TYPE.WING] 		= SHIZHUANG_TYPE.WING,
	[ROLE_APPE_TYPE.FABAO] 		= SHIZHUANG_TYPE.FABAO,
	[ROLE_APPE_TYPE.JIANZHEN]  	= SHIZHUANG_TYPE.JIANZHEN,
	[ROLE_APPE_TYPE.FACE] 		= SHIZHUANG_TYPE.MASK,
	[ROLE_APPE_TYPE.YAO] 		= SHIZHUANG_TYPE.BELT,
	[ROLE_APPE_TYPE.HAND] 		= SHIZHUANG_TYPE.SHOUHUAN,
	[ROLE_APPE_TYPE.FOOT] 		= SHIZHUANG_TYPE.FOOT,
	[ROLE_APPE_TYPE.PHOTO] 		= SHIZHUANG_TYPE.PHOTOFRAME,
	[ROLE_APPE_TYPE.BUBBLE] 	= SHIZHUANG_TYPE.BUBBLE,
	[ROLE_APPE_TYPE.LINGCHONG]	= MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG,
	[ROLE_APPE_TYPE.HUAKUN]		= MOUNT_LINGCHONG_APPE_TYPE.KUN,
}
