LongHunUpGradeView = LongHunUpGradeView or BaseClass(SafeBaseView)

function LongHunUpGradeView:__init()

	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
	self.slot_index = -1
end

function LongHunUpGradeView:LoadConfig()

	local bundle_name = "uis/view/long_hun_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_commmon_second_panel", {sizeDelta = Vector2(782, 488)})
	self:AddViewResource(0,bundle_name,"layout_upgrade_view")

end

function LongHunUpGradeView:LoadCallBack(index, loaded_times)
	self.node_list.title_view_name.text.text = Language.LongHunView.UpGradeName
	self.node_list["btn_upgrade"].button:AddClickListener(BindTool.Bind(self.OnClickUpGrade,self))
	--self.item_cell = ItemCell.New(self.node_list["item_pos"])

end


function LongHunUpGradeView:__delete()
	self.slot_index = -1
end

function LongHunUpGradeView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DelteMe()
		self.item_cell = nil
	end
end
function LongHunUpGradeView:SetData(slot_index)
	if not slot_index then 
		self:Close()
		return
	end
	self.slot_index = slot_index
		
end

function LongHunUpGradeView:OnFlush()
	self.can_upgrade = false
	if not self.slot_index or self.slot_index == -1 then
		return
	end

	local data = LongHunWGData.Instance:GetEquipPosyDataBySlot(self.slot_index)
	local base_cfg = LongHunWGData.Instance:GetMingWenBaseCfgByID(data.lifesoul.item_id)
	local cfg = ItemWGData.Instance:GetItemConfig(data.lifesoul.item_id)
	local attr_type,attr_value,add_value = LongHunWGData.Instance:GetEquipPosyAttrBySlot(data.lifesoul)
	local name_list,is_pre = LongHunWGData.Instance:GetAttributeNameListByType(attr_type, true)
	local can_upgrade,next_need,is_max = LongHunWGData.Instance:LongHunCanUpGrade(self.slot_index)
	self.can_upgrade = can_upgrade

	--self.item_cell:SetData(data.lifesoul)
	if cfg and cfg.icon_id then
 		local bundle, asset = ResPath.GetItem(cfg.icon_id)
 		self.node_list["item_pos"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["item_pos"].image:SetNativeSize()
		end)
		self.node_list["next_item_pos"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["next_item_pos"].image:SetNativeSize()
		end)
	end

	self.node_list["buttom_group"]:SetActive(is_max == 0)
	self.node_list["upgrade_red"]:SetActive(false)--can_upgrade == 1)
	self.node_list["btn_upgrade"]:SetActive(is_max == 0)
	self.node_list["ismax"]:SetActive(is_max == 1)
	self.node_list["ismax2"]:SetActive(is_max == 1)

	local color = can_upgrade == 1 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
	local have_exp = LongHunWGData.Instance:GetLongHunMoney()
	self.node_list["cost_text"].text.text = ToColorStr(have_exp,color).."/".. next_need
	self.node_list["name"].text.text = ToColorStr(base_cfg.name .. " Lv"..data.lifesoul.level,ITEM_COLOR[base_cfg.quality])
	local next_level = is_max == 1 and data.lifesoul.level or data.lifesoul.level + 1
	self.node_list["next_name"].text.text = ToColorStr(base_cfg.name .. " Lv".. next_level,ITEM_COLOR[base_cfg.quality])



	for i=1,3 do
		if name_list[i] then
			self.node_list["now_attr"..i]:SetActive(true)
			self.node_list["naxt_attr"..i]:SetActive(true)

			if is_pre[i] then
				self.node_list["now_attr"..i].text.text = name_list[i]
				self.node_list["now_value"..i].text.text = "+" .. (tonumber(attr_value[i])/100) .. "%"
			else
				self.node_list["now_attr"..i].text.text = name_list[i]
				self.node_list["now_value"..i].text.text = "+" .. attr_value[i]
			end

			if add_value[i] then
				if is_pre[i] then
					self.node_list["naxt_attr"..i].text.text = name_list[i]
					self.node_list["next_value"..i].text.text = "+" .. (tonumber(add_value[i])/100) .. "%"
				else
					self.node_list["naxt_attr"..i].text.text = name_list[i]
					self.node_list["next_value"..i].text.text = "+" .. add_value[i]
				end
			end
		else
			self.node_list["now_attr"..i]:SetActive(false)
			self.node_list["naxt_attr"..i]:SetActive(false)
		end

		if is_max == 1 then
			self.node_list["naxt_attr"..i]:SetActive(false)
		end
	end
end

function LongHunUpGradeView:OnClickUpGrade()
	if self.slot_index and self.slot_index ~= -1 then
		if self.can_upgrade == 0 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.CanNotUpGrade)
			return
		end
		local index = 1
		local is_special = self.slot_index > LongHunWGData.Instance:GetNormalSlotNum() and 1 or 0
		if is_special == 1 then
			index = self.slot_index - LongHunWGData.Instance:GetNormalSlotNum() - 1
		else
			index = self.slot_index - 1
		end
		LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_UP_LEVEL,index,is_special)
	end
end