local tween_hatch_yuan_rotatepos = Vector3(0, 0, -360)

function DragonTempleView:LoadIndexCallBackHatch()
	if not self.hatch_level_order_list then
		self.hatch_level_order_list = AsyncListView.New(HatchLevelOrderListItemRender, self.node_list.hatch_level_order_list)
		self.hatch_level_order_list:SetStartZeroIndex(false)
	end

	if self.hatch_attr_list == nil then
        self.hatch_attr_list = {}
        local node_num = self.node_list["hatch_attr_list"].transform.childCount
        for i = 1, node_num do
            self.hatch_attr_list[i] = CommonAddAttrRender.New(self.node_list["hatch_attr_list"]:FindObj("attr_" .. i))
        end
    end

	if self.hatch_model == nil then
		self.hatch_model = OperationActRender.New(self.node_list["hatch_model"])
	end

	if not self.hatch_stuff_item1 then
		self.hatch_stuff_item1 = ItemCell.New(self.node_list.hatch_stuff_item1)
	end

	if not self.hatch_stuff_item2 then
		self.hatch_stuff_item2 = ItemCell.New(self.node_list.hatch_stuff_item2)
	end

	if self.hatch_tupo_attr_list == nil then
        self.hatch_tupo_attr_list = {}
        local node_num = self.node_list["hatch_tupo_attr_list"].transform.childCount
        for i = 1, node_num do
            self.hatch_tupo_attr_list[i] = CommonAddAttrRender.New(self.node_list["hatch_tupo_attr_list"]:FindObj("attr_" .. i))
        end
    end

	if self.hatch_tupo_star_list == nil then
		self.hatch_tupo_star_list = {}
		local star_num = self.node_list.hatch_tupo_stars_list.transform.childCount
		for i = 1, star_num do
			self.hatch_tupo_star_list[i] = self.node_list["hatch_tupo_star_" .. i]
		end
	end

	if not self.hatch_tupo_cost_cell then
		self.hatch_tupo_cost_cell = ItemCell.New(self.node_list["hatch_tupo_cost"])
	end

	local dragon_temple_data = DragonTempleWGData.Instance

	for i = 0, 4 do
		local name_cfg = dragon_temple_data:GetHatchNameCfgBySeq(i)
		self.node_list["hatch_item_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickHatchToggle, self, i))

		if not IsEmptyTable(name_cfg) then
			self.node_list["hatch_item_name" .. i].text.text = name_cfg.name
			self.node_list["hatch_item_name_hl" .. i].text.text = name_cfg.name
		end

		XUI.AddClickEventListener(self.node_list["hatch_item_lock_" .. i], BindTool.Bind1(self.OnClickHatchOpenLongShenBuy, self))
		XUI.AddClickEventListener(self.node_list["hatch_skill_" .. (i + 1)], BindTool.Bind2(self.OnClickSkillItem, self, (i + 1)))
	end

	if not self.hatch_dan_list then
        self.hatch_dan_list = {}
        for i = 0, 2 do
            local cell = HatchDanRender.New(self.node_list["hatch_dan_" .. i])
            cell:SetIndex(i)
            self.hatch_dan_list[i] = cell
        end
    end

	self.hatch_select_tog_index = -1
	self.is_hatch_auto_uplevel = false
	self.node_list.hatch_up_desc.text.text = Language.DragonTemple.HatchUpDesc
	XUI.AddClickEventListener(self.node_list.hatch_btn, BindTool.Bind1(self.OnClickHatchBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_hatch_uplevel, BindTool.Bind1(self.OnClickHatchUpLevelBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_hatch_privilege, BindTool.Bind1(self.OnClickHatchPrivilegeBtn, self))

	XUI.AddClickEventListener(self.node_list["hatch_ug_btn_left"], BindTool.Bind(self.OnClickHatchUGModelShow, self, -1))
    XUI.AddClickEventListener(self.node_list["hatch_ug_btn_right"], BindTool.Bind(self.OnClickHatchUGModelShow, self, 1))
	-- XUI.AddClickEventListener(self.node_list.btn_hatch_show_auto_panel, BindTool.Bind1(self.OnClickBackAutoBtn, self))
	XUI.AddClickEventListener(self.node_list.hatch_tupo_btn, BindTool.Bind1(self.OnClickHatchTupo, self))	
	XUI.AddClickEventListener(self.node_list.hatch_mount_btn_use, BindTool.Bind(self.OnClickHatchMountUsed, self, true))		
	XUI.AddClickEventListener(self.node_list.hatch_mount_btn_reset, BindTool.Bind(self.OnClickHatchMountUsed, self, false))
	XUI.AddClickEventListener(self.node_list.hatch_fight_mount_btn, BindTool.Bind(self.OnClickHatchOpenFightMount, self))
	XUI.AddClickEventListener(self.node_list["king_vip_btn"], BindTool.Bind(self.OnClickKingVipBtn, self))

	self.hatch_remind_change = BindTool.Bind(self.HatchRemindCallBack, self)
	RemindManager.Instance:Bind(self.hatch_remind_change, RemindName.NewFightMount)
end

function DragonTempleView:ShowIndexCallBackHatch()
	self:SetHatchAutoUpLevelPanel(true)
	self:ClearHatchSliderTween()

	if self.tween_hatch_yuan then
		self.tween_hatch_yuan:Restart()
	else
		self.tween_hatch_yuan = self.node_list.lsd_yuan2.transform:DORotate(tween_hatch_yuan_rotatepos, 8, DG.Tweening.RotateMode.FastBeyond360)
		self.tween_hatch_yuan:SetEase(DG.Tweening.Ease.Linear)
		self.tween_hatch_yuan:SetLoops(-1)
	end

	self:DokHatchViewAnim()

    self.node_list.hatch_fight_mount_btn:CustomSetActive(FunOpen.Instance:GetFunIsOpened(FunName.NewFightMountView))
end

function DragonTempleView:ReleaseHatch()
	if self.hatch_level_order_list then
		self.hatch_level_order_list:DeleteMe()
		self.hatch_level_order_list = nil
	end

	if self.hatch_attr_list then
        for k, v in pairs(self.hatch_attr_list) do
            v:DeleteMe()
        end
        self.hatch_attr_list = nil
    end

	if self.hatch_model then
		self.hatch_model:DeleteMe()
		self.hatch_model = nil
	end

	if self.hatch_stuff_item1 then
		self.hatch_stuff_item1:DeleteMe()
		self.hatch_stuff_item1 = nil
	end

	if self.hatch_stuff_item2 then
		self.hatch_stuff_item2:DeleteMe()
		self.hatch_stuff_item2 = nil
	end

	if self.hatch_tupo_attr_list then
        for k, v in pairs(self.hatch_tupo_attr_list) do
            v:DeleteMe()
        end
        self.hatch_tupo_attr_list = nil
    end

	self.hatch_tupo_star_list = nil

	if self.hatch_tupo_cost_cell then
		self.hatch_tupo_cost_cell:DeleteMe()
		self.hatch_tupo_cost_cell = nil
	end

    if self.hatch_dan_list then
        for k,v in pairs(self.hatch_dan_list) do
            v:DeleteMe()
        end
        self.hatch_dan_list = nil
    end

	self:ClearHatchSliderTween()

	if self.tween_hatch_yuan then
		self.tween_hatch_yuan:Kill()
		self.tween_hatch_yuan = nil
	end

	if self.hatch_remind_change then
        RemindManager.Instance:UnBind(self.hatch_remind_change)
        self.hatch_remind_change = nil
    end

	self.hatch_cur_show_model_type = nil
	self.hatch_record_select_type = nil
	self.hatch_record_model_type = nil
	self:RemoveHatchAddExpCountDown()
end

function DragonTempleView:FlushHatchToggle(item_id)
	for i = 0, 4 do
		local active = DragonTempleWGData.Instance:CanShowHatchItem(i)
		local last_seq = (i - 1) < 0 and 0 or (i - 1)
		local last_item_act = DragonTempleWGData.Instance:CanShowHatchItem(last_seq)
		self.node_list["hatch_item_" .. i]:SetActive(active or last_item_act)
		self.node_list["hatch_item_lock_" .. i]:SetActive(last_item_act and (not active))
		local hatch_name_cfg = DragonTempleWGData.Instance:GetHatchNameCfgBySeq(i)
		self.node_list["hatch_item_lock_desc_" .. i].text.text = string.format(Language.DragonTemple.HatchLockDesc, hatch_name_cfg and hatch_name_cfg.show_level)
	end

	local show_index = self.hatch_select_tog_index or 0
	if item_id and item_id ~= 0 then
		local cfg = NewFightMountWGData.Instance:GetUpgradeCostCfg(item_id)
		show_index = cfg and cfg.mount_seq or show_index
	elseif self.hatch_select_tog_index == nil or self.hatch_select_tog_index < 0 then
		show_index = DragonTempleWGData.Instance:GetHatchTogSelect()
	end

	if show_index >= 0 then
		local active = DragonTempleWGData.Instance:CanShowHatchItem(show_index)
		show_index = active and show_index or 0
		self.node_list["hatch_item_" .. show_index].toggle.isOn = true
		self.hatch_select_tog_index = show_index
	end

	self:SetKingVipBtnInfo()
end

function DragonTempleView:OnClickHatchToggle(index, is_on)
	if is_on then
		if self.hatch_select_tog_index == index then
			return
		end

        self:StopHatchAutoUpLevel()
		self:ClearHatchSliderTween()
		self.hatch_select_tog_index = index
		self:FlushHatchMid()
		self:FlushHatchRight(true)
		self:FlushHatchTupoPanel()
		self:StartAddExpCountDown()
		self:SetKingVipBtnInfo()

		local def_select_panel = 1
		if DragonTempleWGData.Instance:GetHatchLevelRemind(self.hatch_select_tog_index) then
			def_select_panel = 1
		elseif  DragonTempleWGData.Instance:HatchTupoRemind(self.hatch_select_tog_index)  then
			def_select_panel = 2
		end
		
		self.node_list.hatch_level_tog.toggle.isOn = def_select_panel == 1
		self.node_list.hatch_tupo_tog.toggle.isOn = def_select_panel == 2
	end
end

function DragonTempleView:FlushHatchLeftTogRemind()
	for i = 0, 4 do
		self.node_list["hatch_item_remind" .. i]:SetActive(DragonTempleWGData.Instance:GetHatchItemRemind(i))
	end
end

function DragonTempleView:OnFlushHatch(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushHatchToggle(v.item_id)
		end
	end

	if self.hatch_select_tog_index < 0 then
		return
	end

	self:FlushHatchLeftTogRemind()
	self:FlushHatchRightCostItem()

	local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)
	if not IsEmptyTable(hatch_item_info) then
		local current_level_info = DragonTempleWGData.Instance:GetHatchLevelCfg(self.hatch_select_tog_index, hatch_item_info.level)

		if not IsEmptyTable(current_level_info) then
			local is_max_level = DragonTempleWGData.Instance:IsHatchMaxLevel(self.hatch_select_tog_index)
			if is_max_level then
				if self.is_hatch_auto_uplevel then
					self:PlayHatchSliderTween(0, hatch_item_info.exp, current_level_info.need_exp)
				else
					self.node_list.hatch_slider.slider.value = 1
				end
	
				self.node_list.hatch_pro_text.text.text = "-/-"
			else
				if self.is_hatch_auto_uplevel then
					local add_level = hatch_item_info.level - self.hatch_old_level
			   		self:PlayHatchSliderTween(add_level, hatch_item_info.exp, current_level_info.need_exp)
				else
					local need_exp = current_level_info.need_exp
					local current_exp = hatch_item_info.exp
					local slider_value = current_exp / need_exp
					self.node_list.hatch_pro_text.text.text = current_exp .. "/" .. need_exp
					self.node_list.hatch_slider.slider.value = slider_value
					self.node_list["hatch_btn_text"].text.text = Language.DragonTemple.UpHatchBtnDesc[1]
				end
			end
	
			if not self.is_hatch_auto_uplevel then
				self:HatchLevelChangeFlush()
			end

			self:StartAddExpCountDown()
		end
	end
end

function DragonTempleView:FlushHatchRight(need_recover_panel)
	local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)
	local attr_list = DragonTempleWGData.Instance:GethatchlevelAttrList(self.hatch_select_tog_index)
	local cap = DragonTempleWGData.Instance:GethatchCapBySeq(self.hatch_select_tog_index)
	self.node_list.hatch_cap_value.text.text = cap

	local dragon_level = (hatch_item_info or {}).level or 0
	local need_show_attr_up_effect = false

	if nil ~= self.hatch_up_select_tog_cache and nil ~= self.hatch_up_select_tog_level_cache then
		if self.hatch_up_select_tog_cache == self.hatch_select_tog_index and (dragon_level - self.hatch_up_select_tog_level_cache == 1) then
			need_show_attr_up_effect = true
		end
	end

	for i = 1, #self.hatch_attr_list do
		self.hatch_attr_list[i]:SetData(attr_list[i])

		if need_show_attr_up_effect then
			self.hatch_attr_list[i]:PlayAttrValueUpEffect()
		end
	end

	self.hatch_up_select_tog_cache = self.hatch_select_tog_index
	self.hatch_up_select_tog_level_cache = dragon_level

	-- 属性丹
	local dan_list = DragonTempleWGData.Instance:GetHatchDanlist(self.hatch_select_tog_index)
	for k, v in pairs(self.hatch_dan_list) do
		v:SetData(dan_list[k])
	end

	if IsEmptyTable(hatch_item_info) then
		return
	end

	local level = hatch_item_info.level
	local show_level_index = 0

	if level > 0 then
		show_level_index = (level % 10 == 0) and 10 or (level % 10)
	end

	for i = 1, 10 do
		self.node_list["hatch_level_icon_nl" .. i]:SetActive(i > show_level_index)
		self.node_list["hatch_level_icon_hl" .. i]:SetActive(i <= show_level_index)
	end

	local current_level_info = DragonTempleWGData.Instance:GetHatchLevelCfg(self.hatch_select_tog_index, hatch_item_info.level)
	local level_str = string.format(Language.DragonTemple.HatchLevel, hatch_item_info.level)
	self.node_list.hatch_level.text.text = level_str
	if IsEmptyTable(current_level_info) then
		return
	end

	local is_max_level = DragonTempleWGData.Instance:IsHatchMaxLevel(self.hatch_select_tog_index)
	local is_breach = current_level_info.need_exp <= 0 and current_level_info.cost_gold > 0
	local can_up_level = DragonTempleWGData.Instance:IsHatchCanUpLevel(self.hatch_select_tog_index)

	if not is_max_level then
		if is_breach then
			self.node_list.uplevel_breath_part_cost.text.text = current_level_info.cost_gold
			-- self.node_list.btn_hatch_uplevel_text.text.text = is_hatch and Language.DragonTemple.OneKeyHatch or Language.DragonTemple.HatchBreath
			self.node_list.btn_hatch_uplevel_text.text.text = Language.DragonTemple.HatchBreath
		else
			local need_exp = current_level_info.need_exp
			local current_exp = hatch_item_info.exp
			local slider_value = current_exp / need_exp
			self.node_list.hatch_pro_text.text.text = current_exp .. "/" .. need_exp
			self.node_list.hatch_slider.slider.value = slider_value
			self:FlushHatchRightCostItem()

			local str = can_up_level and Language.DragonTemple.HatchOneKeyUP or Language.DragonTemple.FeedPills
			self.node_list.btn_hatch_uplevel_text.text.text = str
		end
	end

	if is_max_level or can_up_level or is_breach then
		self.node_list.btn_hatch_uplevel_slider.image.fillAmount = 1
	end

	self.node_list.text_uplevel_cd:CustomSetActive(not is_max_level and not can_up_level and not is_breach)
	self.node_list.btn_hatch_uplevel:CustomSetActive(not is_max_level)
	self.node_list.uplevel_breath_part:CustomSetActive(is_breach and not is_max_level)
	self.node_list.btn_hatch_uplevel_remind:SetActive(DragonTempleWGData.Instance:GetHatchOneLevelRemind(self.hatch_select_tog_index))

	if not self.is_hatch_auto_uplevel and (is_max_level or is_breach or need_recover_panel or can_up_level) then
		self:SetHatchAutoUpLevelPanel(true)
		self.node_list.btn_hatch_is_maxlevel:CustomSetActive(is_max_level)
	end

	self.node_list.hatch_level_toggle_remind:SetActive(DragonTempleWGData.Instance:GetHatchLevelRemind(self.hatch_select_tog_index))
end

function DragonTempleView:OnHatchItemChangeFlush()
	self:FlushHatchLeftTogRemind()
	self:FlushHatchRightCostItem()
	self:FlushHatchTupoPanel()
end

function DragonTempleView:OnHatchGoldChangeFlush()
	self:FlushHatchLeftTogRemind()
end

function DragonTempleView:HatchLevelChangeFlush()
	self:FlushHatchMid()
	self:FlushHatchRight()
	self:FlushHatchTupoPanel()
end

function DragonTempleView:SetHatchAutoUpLevelPanel(show_auto_uplevel_panel)
	self.node_list.hatch_auto_level_panel:CustomSetActive(show_auto_uplevel_panel)
	self.node_list.stuff_cost_panel:CustomSetActive(not show_auto_uplevel_panel)
end

function DragonTempleView:StartAddExpCountDown()
	local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)
	if not IsEmptyTable(hatch_item_info) then
		local level = hatch_item_info.level > 0 and hatch_item_info.level or 1
		local current_level_info = DragonTempleWGData.Instance:GetHatchLevelCfg(self.hatch_select_tog_index, level)

		if not IsEmptyTable(current_level_info) then
			local is_max_level = DragonTempleWGData.Instance:IsHatchMaxLevel(self.hatch_select_tog_index)
			local is_breach = current_level_info.need_exp <= 0 and current_level_info.cost_gold > 0
			local can_up_level = DragonTempleWGData.Instance:IsHatchCanUpLevel(self.hatch_select_tog_index)

			if not is_max_level and not can_up_level and not is_breach then
				self:AddHatchAddExpCountDown()
			else
				self:RemoveHatchAddExpCountDown()
			end
		end
	end
end

function DragonTempleView:AddHatchAddExpCountDown()
	local hatch_auto_uplevel_info = DragonTempleWGData.Instance:GetHatchAutoUplevelInfo(self.hatch_select_tog_index)

	if hatch_auto_uplevel_info and hatch_auto_uplevel_info.is_need_check then
		self:RemoveHatchAddExpCountDown()
		local time = TimeWGCtrl.Instance:GetServerTime()
		local rest_time = hatch_auto_uplevel_info.up_level_cd_time - time
		-- local up_level_total_time = hatch_auto_uplevel_info.up_level_total_time
		-- local up_level_elapse_time = up_level_total_time - rest_time
		-- self.node_list.btn_hatch_uplevel_slider.image.fillAmount = up_level_elapse_time / up_level_total_time

		local add_exp_total_time = hatch_auto_uplevel_info.add_exp_total_time
		self.node_list.btn_hatch_uplevel_slider.image.fillAmount = (add_exp_total_time - rest_time) / add_exp_total_time

		self.node_list.text_uplevel_cd.text.text = string.format(Language.DragonTemple.HatchCultivationTime, TimeUtil.FormatSecondDHM9(rest_time))
		self.uplevel_countdown = CountDown.Instance:AddCountDown(rest_time, 1,
									function(elapse_time, total_time)
										if self.node_list.text_uplevel_cd then
											self.node_list.text_uplevel_cd.text.text = string.format(Language.DragonTemple.HatchCultivationTime, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
											-- self.node_list.btn_hatch_uplevel_slider.image.fillAmount = elapse_time / total_time
											-- self.node_list.btn_hatch_uplevel_slider.image.fillAmount = (up_level_elapse_time + elapse_time) / up_level_total_time
											self.node_list.btn_hatch_uplevel_slider.image.fillAmount = (add_exp_total_time - total_time + elapse_time)/ add_exp_total_time
										end
									end,
									function()
									end)
	end
end

function DragonTempleView:RemoveHatchAddExpCountDown()
	if CountDown.Instance:HasCountDown(self.uplevel_countdown) then
        CountDown.Instance:RemoveCountDown(self.uplevel_countdown)
        self.uplevel_countdown = nil
    end
end

function DragonTempleView:FlushHatchRightCostItem()
	local hatch_stuff_data = DragonTempleWGData.Instance:GetHatchStuffCfg()
	if not IsEmptyTable(hatch_stuff_data) then
		local item_id1 = hatch_stuff_data[1].item_id
		local num1 = ItemWGData.Instance:GetItemNumInBagById(item_id1)
		self.hatch_stuff_item1:SetFlushCallBack(function ()
			self.hatch_stuff_item1:SetRightBottomColorText(num1)
			self.hatch_stuff_item1:SetRightBottomTextVisible(true)
		end)
		
		self.hatch_stuff_item1:SetData({item_id = item_id1, num = num1, is_bind = 0})
		
		local item_id2 = hatch_stuff_data[2].item_id
		local num2 = ItemWGData.Instance:GetItemNumInBagById(item_id2)
		self.hatch_stuff_item2:SetFlushCallBack(function ()
			self.hatch_stuff_item2:SetRightBottomColorText(num2)
			self.hatch_stuff_item2:SetRightBottomTextVisible(true)
		end)

		self.hatch_stuff_item2:SetData({item_id = item_id2, num = num2, is_bind = 0})
		self.node_list.btn_hatch_uplevel_remind:SetActive(DragonTempleWGData.Instance:GetHatchOneLevelRemind(self.hatch_select_tog_index))
		self.node_list.hatch_level_toggle_remind:SetActive(DragonTempleWGData.Instance:GetHatchLevelRemind(self.hatch_select_tog_index))
	end

	-- 属性丹
	local dan_list = DragonTempleWGData.Instance:GetHatchDanlist(self.hatch_select_tog_index)
	for k, v in pairs(self.hatch_dan_list) do
		v:SetData(dan_list[k])
	end
end

function DragonTempleView:FlushHatchMid()
	local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)
	if IsEmptyTable(hatch_item_info) then
		return
	end

	local is_hatch = hatch_item_info.model_change or false

	if is_hatch then
		self:FlushHatchMidSkillInfo(hatch_item_info)
	else
		self:FlushHatchMidJieShuInfo(hatch_item_info)		
	end

	self.node_list["hatch_panel"]:SetActive(not is_hatch)
	self.node_list["lsd_yuan"]:SetActive(not is_hatch)
	self.node_list["hatch_level_icon_list"]:SetActive(not is_hatch)
	self.node_list["hatch_level_order_list"]:SetActive(not is_hatch)
	self.node_list["larva_panel"]:SetActive(is_hatch)

	local mode_type_cfg = DragonTempleWGData.Instance:GetHatchModelTypeCfgByLevel(self.hatch_select_tog_index, hatch_item_info.level)
	if IsEmptyTable(mode_type_cfg) then
		return
	end

	self.hatch_cur_show_model_type = mode_type_cfg.model_type
	self:FlushHatchModel()
end

function DragonTempleView:FlushHatchMidJieShuInfo(hatch_item_info)
	local data_list = DragonTempleWGData.Instance:GetHatchOrderDataList(self.hatch_select_tog_index)
	local level = hatch_item_info.level
	local show_level_index = 0
	self.hatch_level_order_list:SetDataList(data_list)

	if level > 0 then
		show_level_index = (level % 10 == 0) and 10 or (level % 10)
	end

	for i = 1, 10 do
		self.node_list["hatch_level_icon" .. i]:SetActive(i <= show_level_index)
	end
end

function DragonTempleView:FlushHatchMidSkillInfo(hatch_item_info)
	local skill_data = DragonTempleWGData.Instance:GetHatchSkillAttrInfo(self.hatch_select_tog_index)
	local skill_cfg = DragonTempleWGData.Instance:GetHatchSkillInfo(self.hatch_select_tog_index)

	if not IsEmptyTable(skill_data) and not IsEmptyTable(skill_cfg) then
		for i = 1, 5 do
			local data = skill_cfg[i]
			local has_data = not IsEmptyTable(data)
			if has_data then
				local active = skill_data[i] or false
				XUI.SetGraphicGrey(self.node_list["hatch_skill_" .. i], not active)
	
				local skill_icon = data.skill_icon
				local bundel, asset = ResPath.GetSkillIconById(skill_icon)
				self.node_list["hatch_skill_icon" .. i].image:LoadSprite(bundel, asset, function()
					self.node_list["hatch_skill_icon" .. i].image:SetNativeSize()
				end)
			end

			self.node_list["hatch_skill_" .. i]:SetActive(has_data)
		end
	end
end

-- 方向键点击
function DragonTempleView:OnClickHatchUGModelShow(dir)
    if not self.hatch_cur_show_model_type then
        return
    end

    local max_model_type = DragonTempleWGData.Instance:GetHatchMaxModelByType(self.hatch_select_tog_index)
    local model_type = self.hatch_cur_show_model_type + dir
    model_type = math.min(model_type, max_model_type)
    model_type = math.max(1, model_type)
	self.hatch_cur_show_model_type = model_type
	self:FlushHatchModel()
end


function DragonTempleView:FlushHatchModel()
	if not self.hatch_cur_show_model_type then
		return
	end

	local mode_type_cfg = DragonTempleWGData.Instance:GetHatchModelTypeCfgByType(self.hatch_select_tog_index, self.hatch_cur_show_model_type)
	if IsEmptyTable(mode_type_cfg) then
		return
	end

	local flush_model = not (self.hatch_record_select_type == self.hatch_select_tog_index and self.hatch_record_model_type == self.hatch_cur_show_model_type)
	if flush_model then
		self.hatch_record_model_type = self.hatch_cur_show_model_type
		self.hatch_record_select_type = self.hatch_select_tog_index
		local display_data = {}
		display_data.should_ani = true
		if mode_type_cfg.model_show_itemid ~= 0 and mode_type_cfg.model_show_itemid ~= "" then
			local split_list = string.split(mode_type_cfg.model_show_itemid, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				display_data.model_item_id_list = list
			else
				display_data.item_id = mode_type_cfg.model_show_itemid
			end
		end
		
		display_data.hide_model_block = true
		display_data.bundle_name = mode_type_cfg["model_bundle_name"]
		display_data.asset_name = mode_type_cfg["model_asset_name"]
		local model_show_type = tonumber(mode_type_cfg["model_show_type"]) or 1
		display_data.render_type = model_show_type - 1

		self.hatch_model:SetData(display_data)
		local scale = mode_type_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(self.node_list["hatch_model"].transform, scale, scale, scale)
		local pos_x, pos_y = 0, 0
		if mode_type_cfg.display_pos and mode_type_cfg.display_pos ~= "" then
			local pos_list = string.split(mode_type_cfg.display_pos, "|")
			pos_x = tonumber(pos_list[1]) or pos_x
			pos_y = tonumber(pos_list[2]) or pos_y
		end

		RectTransform.SetAnchoredPositionXY(self.node_list.hatch_model.rect, pos_x, pos_y)

		if mode_type_cfg.rotation and mode_type_cfg.rotation ~= "" then
			local rotation_tab = string.split(mode_type_cfg.rotation,"|")
			self.node_list["hatch_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		end
	end

	local max_model_type = DragonTempleWGData.Instance:GetHatchMaxModelByType(self.hatch_select_tog_index)
	self.node_list["hatch_ug_btn_left"]:CustomSetActive(self.hatch_cur_show_model_type > 1)
   	self.node_list["hatch_ug_btn_right"]:CustomSetActive(self.hatch_cur_show_model_type < mode_type_cfg.model_type + 1 and self.hatch_cur_show_model_type < max_model_type)

	local use_mount_id = NewFightMountWGData.Instance:GetUseMountId()
	local mount_level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(self.hatch_select_tog_index)
	self.node_list.hatch_mount_btn_reset:SetActive(use_mount_id == self.hatch_select_tog_index and self.hatch_cur_show_model_type == 3)
	self.node_list.hatch_mount_btn_use:SetActive(use_mount_id ~= self.hatch_select_tog_index and mount_level > 0 and self.hatch_cur_show_model_type == 3)

	local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)
	local not_act_flag = true
	if not IsEmptyTable(hatch_item_info) then
		if hatch_item_info.level >= mode_type_cfg.min_level then
			if mode_type_cfg.need_model_act == 1 and mount_level > 0 then
				not_act_flag = false
			elseif mode_type_cfg.need_model_act == 0 then
				not_act_flag = false
			end
		end
	end

	self.node_list.hatch_no_act_flag:SetActive(not_act_flag)
end

function DragonTempleView:OnClickSkillItem(skill_index)
	local skill_info = DragonTempleWGData.Instance:GetHatchSkillDataByIndex(self.hatch_select_tog_index, skill_index)

	if not IsEmptyTable(skill_info) then
		NewAppearanceWGCtrl.Instance:DisplayBoxOpen(skill_info)
	end
end

function DragonTempleView:OnClickHatchBtn()
	if not self.is_hatch_auto_uplevel then
		self:StartHatchAutoUpLevel()
	else
		self:StopHatchAutoUpLevel()
	end
end

function DragonTempleView:StartHatchAutoUpLevel(no_tips)
	local hatch_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)
	local is_max_level = DragonTempleWGData.Instance:IsHatchMaxLevel(self.hatch_select_tog_index)

	if IsEmptyTable(hatch_info) or is_max_level then
		self:StopHatchAutoUpLevel()
		return
	end

	local has_material = DragonTempleWGData.Instance:IsCanHatchUpLevel()
	if not has_material then
		if not no_tips then
			local stuff_cfg = DragonTempleWGData.Instance:GetHatchStuffCfg()
			local item_id = (stuff_cfg[1] or {}).item_id
			
			if item_id then
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
			end
		end

		self:StopHatchAutoUpLevel()
		return 
	end

	local current_level_info = DragonTempleWGData.Instance:GetHatchLevelCfg(self.hatch_select_tog_index, hatch_info.level)
	self.hatch_old_level = hatch_info.level

	if not IsEmptyTable(current_level_info) then
		local is_breach = current_level_info.need_exp <= 0 and current_level_info.cost_gold >= 0
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.HANTCH_LEVEL_UP, self.hatch_select_tog_index)

		if is_breach then
			self:StopHatchAutoUpLevel()
			return
		else
			self.is_hatch_auto_uplevel = true
			self.node_list["hatch_btn_text"].text.text = Language.DragonTemple.UpHatchBtnDesc[2]
		end
	end
end

function DragonTempleView:StopHatchAutoUpLevel()
    self.is_hatch_auto_uplevel = false
    self.node_list["hatch_btn_text"].text.text = Language.DragonTemple.UpHatchBtnDesc[1]
end

function DragonTempleView:PlayHatchSliderTween(add_level, exp_val, need_exp_val)
	if need_exp_val <= 0 then
		self:ClearHatchSliderTween()
		self:StopHatchAutoUpLevel()
		return
	end

	if add_level == 0 and exp_val == need_exp_val then
        self:ClearHatchSliderTween()

        local slider = self.node_list.hatch_slider.slider
        local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.5))
        self.hatch_slider_tween = slider:DOValue(1, time)
        self.hatch_slider_tween:OnComplete(function ()
			MainuiWGCtrl.Instance:DelayShowCachePower(0)
            self:PlayUseEffect(UIEffectName.s_shengji)
		end)

        self:StopHatchAutoUpLevel()
        return
    end

	local hatch_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)
    if IsEmptyTable(hatch_info) then
        return
    end

	local real_level = hatch_info.level

    self.hatch_slider_tween_func = function (progress)
        self:ClearHatchSliderTween()
		local before_uplevel_cfg = DragonTempleWGData.Instance:GetHatchLevelCfg(self.hatch_select_tog_index, self.hatch_old_level)

        if not before_uplevel_cfg then
            return
        end
        
        if progress <= 0 then
            if self.is_hatch_auto_uplevel then
                if self.hatch_old_level ~= real_level then
                    self.hatch_old_level = real_level
                    self:HatchLevelChangeFlush()
                end

                self:StartHatchAutoUpLevel(true)
            end

            return
        end

        local is_up_one_level = false
        local slider = self.node_list.hatch_slider.slider

		if progress > 1 then
			local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.5))
			self.hatch_slider_tween = slider:DOValue(1, time)
            is_up_one_level = true
            self.node_list.hatch_pro_text.text.text = before_uplevel_cfg.need_exp .. "/" .. before_uplevel_cfg.need_exp
		else
			local time = tonumber(string.format("%.2f", (progress - slider.value) * 0.5))
			self.hatch_slider_tween = slider:DOValue(progress, time)
            self.node_list.hatch_pro_text.text.text = hatch_info.exp .. "/" .. before_uplevel_cfg.need_exp
		end

        progress = progress - 1
        self.hatch_slider_tween:OnComplete(function ()
			if progress >= 0 then
				slider.value = 0

                if is_up_one_level then
                    self.hatch_old_level = self.hatch_old_level + 1
                    self:HatchLevelChangeFlush()
					self:PlayUseEffect(UIEffectName.s_shengji)
                end
			end
			
			if progress < 1 then
				MainuiWGCtrl.Instance:DelayShowCachePower(0)
			end

			self.hatch_slider_tween_func(progress)
		end)
    end

    local total_progress = add_level + exp_val / need_exp_val
	self.hatch_slider_tween_func(total_progress)
end

function DragonTempleView:ClearHatchSliderTween()
    if self.hatch_slider_tween then
        self.hatch_slider_tween:Kill()
        self.hatch_slider_tween = nil
    end
end

function DragonTempleView:OnClickHatchUpLevelBtn()
	local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.hatch_select_tog_index)

	if not IsEmptyTable(hatch_item_info) then
		local current_level_info = DragonTempleWGData.Instance:GetHatchLevelCfg(self.hatch_select_tog_index, hatch_item_info.level)

		if not IsEmptyTable(current_level_info) then
			local is_breach = current_level_info.need_exp <= 0 and current_level_info.cost_gold > 0
	
			if is_breach then
				local role_gold = RoleWGData.Instance.role_info.gold
		
				if role_gold < current_level_info.cost_gold then
					VipWGCtrl.Instance:OpenTipNoGold()
				else
					DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.HANTCH_LEVEL_UP, self.hatch_select_tog_index)
				end
			else
				local can_up_level = DragonTempleWGData.Instance:IsHatchCanUpLevel(self.hatch_select_tog_index)
		
				if can_up_level then
					DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.HANTCH_LEVEL_UP, self.hatch_select_tog_index)
				else
					self:SetHatchAutoUpLevelPanel(false)
				end
			end
		end
	end
end

function DragonTempleView:IsHatchAutoUpLevelNow()
	return self.is_hatch_auto_uplevel
end

function DragonTempleView:OnClickHatchPrivilegeBtn()
	DragonTempleWGCtrl.Instance:OpenHatchPrivilegeView()
end

function DragonTempleView:DokHatchViewAnim()
    local tween_info = UITween_CONSTS.DragonTemple
    RectTransform.SetAnchoredPositionXY(self.node_list.hatch_right_root.rect, 600, 0)

    self.node_list.hatch_right_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
end

function DragonTempleView:PlayHatchEffect()
	-- self:PlayUseEffect(UIEffectName.s_juanxian)
	local bundle_name, asset_name = ResPath.GetEffectUi("UI_effect_longshendian5")
	EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["breath_effect"].transform, 3)
end

function DragonTempleView:OnClickBackAutoBtn()
	self:SetHatchAutoUpLevelPanel(true)
end

function DragonTempleView:OnClickHatchOpenLongShenBuy()
	DragonTempleWGCtrl.Instance:OpenDragonTempleLevelBuyView()
end

-------------突破
function DragonTempleView:FlushHatchTupoPanel()
	if self.hatch_select_tog_index == nil or self.hatch_select_tog_index < 0 then
		return
	end

	local level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(self.hatch_select_tog_index)
	local cur_level_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(self.hatch_select_tog_index, level)
	local next_level_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(self.hatch_select_tog_index, level + 1)
	if not cur_level_cfg then
		return
	end

	local is_max_level = next_level_cfg == nil
	local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 5)

	local need_show_attr_up_effect = false
	if nil ~= self.hatch_tupo_tog_cache and nil ~= self.hatch_tupo_level_cache then
		if self.hatch_tupo_tog_cache == self.hatch_select_tog_index and (level - self.hatch_tupo_level_cache == 1) then
			need_show_attr_up_effect = true
		end
	end

	for i = 1, #self.hatch_tupo_attr_list do
		self.hatch_tupo_attr_list[i]:SetData(attr_list[i])

		if need_show_attr_up_effect then
			self.hatch_tupo_attr_list[i]:PlayAttrValueUpEffect()
		end
	end

	self.hatch_tupo_tog_cache = self.hatch_select_tog_index
	self.hatch_tupo_level_cache = level

	self.hatch_tupo_cost_cell:SetData({item_id = cur_level_cfg.cost_item_id})
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
	local color = item_num >= cur_level_cfg.need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
	local up_level_str = item_num .. "/" .. cur_level_cfg.need_num
	self.node_list.hatch_tupo_cost_num.text.text = ToColorStr(up_level_str, color)

	local star_res_list = GetStarImgResByStar(level)
	for k,v in pairs(self.hatch_tupo_star_list) do
		v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
	end

	local is_red = DragonTempleWGData.Instance:HatchTupoRemind(self.hatch_select_tog_index)
	self.node_list.hatch_tupo_toggle_remind:SetActive(is_red)
	self.node_list.hatch_tupo_remind:SetActive(is_red)
	self.node_list.hatch_tupo_btn:SetActive(not is_max_level)
	self.node_list.hatch_tupo_max:SetActive(is_max_level)
	self.node_list.hatch_tupo_cost_num:SetActive(not is_max_level)
end

function DragonTempleView:OnClickHatchTupo()
	if self.hatch_select_tog_index == nil or self.hatch_select_tog_index < 0 then
		return
	end

	local level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(self.hatch_select_tog_index)
	local cur_level_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(self.hatch_select_tog_index, level)
	local next_level_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(self.hatch_select_tog_index, level + 1)
	if cur_level_cfg and next_level_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		if item_num >= cur_level_cfg.need_num then
			NewFightMountWGCtrl.Instance:SendCSNewFightMountOperateRequest(FIGHT_MOUNT2_OPERATE_TYPE.UPGRADE, self.hatch_select_tog_index)
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_level_cfg.cost_item_id})
		end
	end
end

-- 使用 / 重置
function DragonTempleView:OnClickHatchMountUsed(use_this)
	if self.hatch_select_tog_index == nil or self.hatch_select_tog_index < 0 then
		return
	end

	-- 重置
    if not use_this then
		local appe_id = 0
		local grade = NewAppearanceWGData.Instance:GetBaseQiChongGrade(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
		local base_cfg = NewAppearanceWGData.Instance:GetQiChongBaseCfgByGrade(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, grade)
		if base_cfg then
            appe_id = base_cfg.appe_image_id
        end

		NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_id, -1)
	else
		NewFightMountWGCtrl.Instance:SendCSNewFightMountOperateRequest(FIGHT_MOUNT2_OPERATE_TYPE.GOON, self.hatch_select_tog_index)
		self:PlayHatchMountUseEffect()
	end
end

function DragonTempleView:OnClickHatchOpenFightMount()
	ViewManager.Instance:Open(GuideModuleName.NewFightMountView)
end

function DragonTempleView:HatchRemindCallBack(remind_name, num)
	self.node_list["hatch_fight_mount_red"]:SetActive(num > 0)
end
-- 使用特效
function DragonTempleView:PlayHatchMountUseEffect()
	local bundle_name, asset_name = ResPath.GetEffect(Ui_Effect.UI_huanhua)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["hatch_mount_use_effect"].transform, nil, Quaternion.Euler(-12, 0, 0))
end

--设置王者特权按钮信息.
function DragonTempleView:SetKingVipBtnInfo()
	local data = RechargeWGData.Instance:GetCurKingVipAddition()
	if not IsEmptyTable(data) then
		local attr_add = data["add_" .. self.hatch_select_tog_index + 6]
		attr_add = attr_add / 100

		self.node_list.king_vip_text.text.text = string.format(Language.DragonTemple.king_vip_text, attr_add)

		local is_active = RechargeWGCtrl.Instance:GetKingVipIsActive()

		self.node_list.king_vip_btn.button.interactable = not is_active

		local king_vip_go_text = ""

		if is_active then
			king_vip_go_text = Language.DragonTemple.king_vip_go_text2
		else
			king_vip_go_text = Language.DragonTemple.king_vip_go_text
		end
		self.node_list.king_vip_go_text.text.text = king_vip_go_text
	end

	self.node_list.king_vip_btn:SetActive(not IsEmptyTable(data))
end

--王者特权前往激活.
function DragonTempleView:OnClickKingVipBtn()
	RechargeWGCtrl.Instance:Open(TabIndex.recharge_king_vip)
end

------HatchLevelOrderListItemRender------
HatchLevelOrderListItemRender = HatchLevelOrderListItemRender or BaseClass(BaseRender)
function HatchLevelOrderListItemRender:OnFlush()
	if IsEmptyTable( self.data) then
		return
	end

	XUI.SetGraphicGrey(self.node_list.hatch_level_order_item_icon, self.data.is_lock)
end

--------HatchDanRender--------------
HatchDanRender = HatchDanRender or BaseClass(BaseRender)
function HatchDanRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickDan, self))
end

function HatchDanRender:OnFlush()
    if IsEmptyTable(self.data) or (not self.data.is_open) then
        self.view:SetActive(false)
        return
    end

	self.view:SetActive(true)
	local item_id = self.data.cfg.use_item_id
	local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(item_id)
    self.node_list["icon"].image:LoadSprite(bundle, asset)

	self.node_list["used_num"].text.text = self.data.used_num
	local remain_num = self.data.cfg.use_limit_num - self.data.used_num
	local show_num = self.data.had_num > remain_num and remain_num or self.data.had_num
	if self.data.is_remind then
        self.node_list["can_add_num"].text.text = "+" .. show_num
    else
        self.node_list["can_add_num"].text.text = ""
    end
    self.node_list["arrow_bg"]:SetActive(self.data.is_remind)
end

function HatchDanRender:OnClickDan()
    if IsEmptyTable(self.data) then
        return
    end

	local cfg = self.data.cfg
	local remain_num = self.data.cfg.use_limit_num - self.data.used_num
	local show_num = self.data.had_num > remain_num and remain_num or self.data.had_num
	if self.data.is_remind then
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.HANTCH_DAN_UP, cfg.type, cfg.index, show_num)
        local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_shuxingdan)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["icon"].transform)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.use_item_id})
	end
end