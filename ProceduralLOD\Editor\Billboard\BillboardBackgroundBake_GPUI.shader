﻿Shader "Hidden/GPUInstancer/Billboard/BackgroundBake"
{
	Properties
	{
		_BaseMap ("Texture", 2D) = "white" {}
		_Color("Color", Color) = (1,1,1,1)
		_IsLinearSpace("Add Gama Correction", Float) = 0.0
	}
	SubShader
	{
		Cull Off
        
		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			
			#include "UnityCG.cginc"
			#include "../Include/GPUIBillboardInclude.cginc" 

			sampler2D _BaseMap;
			float4 _Color;
			float _IsLinearSpace;
						
			struct v2f
			{
				float2 uv : TEXCOORD0;
				float4 vertex : SV_POSITION;
			};

			v2f vert (float4 vertex : POSITION, float2 uv : TEXCOORD0)
			{


				v2f o;
				o.vertex = UnityObjectToClipPos(vertex);
				o.uv = uv.xy;
				return o;
			}
			
			float4 frag (v2f i) : SV_Target
			{
				float4 c = tex2Dlod(_BaseMap, half4(i.uv, 0, 3));

				c.a = 1; // set the non-discarded pixels back to full alpha
				
				// Account fot gamma correction if necessary
				return c * (1 - _IsLinearSpace) + (_IsLinearSpace * float4(LinearToGamma(c.rgb), 1));
			}
			ENDCG
		}
	}
}
