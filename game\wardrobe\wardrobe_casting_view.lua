WardrobeCastingView = WardrobeCastingView or BaseClass(SafeBaseView)
function WardrobeCastingView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

    local common_bundle = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/wardrobe_new_ui_prefab"
	self:AddViewResource(0, bundle_name, "layout_wardrobe_casting")
	self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.WARDROBE})
end

function WardrobeCastingView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Wardrobe.ViewCastingName

	-- 初始化6辅战
	if not self.wardrobe_casting_list then
		self.wardrobe_casting_list = {}

		for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
			local index = j + 1
			local cell_obj = self.node_list.wardrobe_casting_list:FindObj(string.format("wardrobe_casting_item_0%d", index))
			if cell_obj then
				local cell = WardrobeCastingItemRender.New(cell_obj)
				cell:SetClickCallBack(BindTool.Bind1(self.OnSelectCastingItemCB, self))
				cell:SetIndex(j)
				self.wardrobe_casting_list[j] = cell
			end
		end
	end

	if nil == self.role_model then
		self.role_model = CommonUserModelRender.New(self.node_list.ph_display)
		self.role_model:AddUiRoleModel(self)
	end

	-- 星数
	if not self.casting_star_list then
		self.casting_star_list = {}
		for i = 1, 10 do
			self.casting_star_list[i] = self.node_list["casting_star_" .. i]
		end
	end

	if not self.casting_spend_item then
		self.casting_spend_item = ItemCell.New(self.node_list.casting_spend_item)
	end

	if not self.casting_spend_up_item_cell then
		self.casting_spend_up_item_cell = ItemCell.New(self.node_list.casting_spend_up_item_cell)
	end

    -- 基础属性
    if self.casting_attr_list == nil then
        self.casting_attr_list = {}
        for i = 1, 10 do
            local attr_obj = self.node_list.casting_attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.casting_attr_list[i] = cell
            end
        end
    end

	XUI.AddClickEventListener(self.node_list.btn_casting_up, BindTool.Bind(self.OnClickCastingUp, self))               				-- 操作按钮，升星或是打造
	XUI.AddClickEventListener(self.node_list.btn_wardrobe_casting_skill, BindTool.Bind(self.OnClickCastingSkill, self))           	-- 技能界面
	XUI.AddClickEventListener(self.node_list.casting_spend_up_btn, BindTool.Bind(self.OnClickCastingSpendUp, self))           		-- 成功率提升
end

function WardrobeCastingView:ReleaseCallBack()
	if self.wardrobe_casting_list and #self.wardrobe_casting_list > 0 then
		for i, v in ipairs(self.wardrobe_casting_list) do
			v:DeleteMe()
			v = nil
		end

		self.wardrobe_casting_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.casting_spend_item then
		self.casting_spend_item:DeleteMe()
		self.casting_spend_item = nil
	end

	if self.casting_spend_up_item_cell then
		self.casting_spend_up_item_cell:DeleteMe()
		self.casting_spend_up_item_cell = nil
	end

	if self.casting_attr_list and #self.casting_attr_list > 0 then
		for i, v in ipairs(self.casting_attr_list) do
			v:DeleteMe()
			v = nil
		end

		self.casting_attr_list = nil
	end

	self.casting_star_list = nil
	self.is_click_per_up = nil
	self.show_data = nil
end

-- 关闭前调用
function WardrobeCastingView:CloseCallBack()
	self.is_init_role_model = false
	self.cache_upgrade = nil
	self.cache_star = nil
	self.is_operate_send = nil
	WardrobeWGCtrl.Instance:UpdateUISceneShowState()
end

-- 设置当前预览的缓存数据
function WardrobeCastingView:SetNowCacheData(cache_data)
	self.show_data = cache_data
end

-- 方案切换
function WardrobeCastingView:OnSelectCastingItemCB(cell)
	if cell == nil or cell.data == nil then
		return
	end

	if not self.show_data then
		return
	end

	local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.show_data.fashion_part_type, self.show_data.fashion_index)
	if not is_act then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Wardrobe.CastingUpgradeTips5)
		return
	end

	local forge_data = WardrobeWGData.Instance:GetShizhuangForgeInfoByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local grade_level = forge_data and forge_data.grade_level or 0
	local grade_cfg = WardrobeWGData.Instance:GetForgeGradeCfgByLevel(grade_level)
	local stone_quality = grade_cfg and grade_cfg.stone_quality or 0
	local client_seq = WardrobeWGData.Instance:GetShizhuangForgeIndexByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local server_seq = client_seq - 1
	local now_stone_cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(stone_quality, cell.index)

	if cell.data == -1 then
		local aim_stone_cfg = nil
		local aim_stone_item = nil
		local is_can_put = false
		local now_stone_cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(stone_quality, cell.index)
	
		for i = stone_quality, 1, -1 do
			aim_stone_cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(i, cell.index)
			aim_stone_item = aim_stone_cfg and aim_stone_cfg.stone_item_id or 0
	
			if aim_stone_item ~= 0 then
				local item_num = ItemWGData.Instance:GetItemNumInBagById(aim_stone_item)
	
				if item_num >= 1 then
					is_can_put = true
					break
				end
			end
		end

		if is_can_put and aim_stone_cfg then
			if aim_stone_item == now_stone_cfg.stone_item_id then
				WardrobeWGCtrl.Instance:SendShiZhuangForgePutStone(server_seq, aim_stone_cfg.seq)
			else
				WardrobeWGCtrl.Instance:OpenCastingUpgradeView({
					now_seq = aim_stone_cfg.seq,
					next_seq = now_stone_cfg.seq,
					stone_part = cell.index,
					server_seq = server_seq,
				})
			end
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = now_stone_cfg.stone_item_id })
		end
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = now_stone_cfg.stone_item_id })
	end
end

-- 刷新
function WardrobeCastingView:OnFlush(param_t)
	if not self.show_data then
		return
	end

	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushForgeMessage()
			self:FlushRightShowMessage()
			self:FlushSelectPerUp()
			self:FlushRoleModel()
        elseif k == "item_change" then
			self:FlushForgeMessage()
			self:FlushRightShowMessage(true)
        end
    end
end

-- 刷新天石信息
function WardrobeCastingView:FlushForgeMessage()
	local forge_data = WardrobeWGData.Instance:GetShizhuangForgeInfoByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local list = forge_data and forge_data.stone_part_list or {}
	local grade_level = forge_data and forge_data.grade_level or 0
	local grade_cfg = WardrobeWGData.Instance:GetForgeGradeCfgByLevel(grade_level)
	local stone_quality = grade_cfg and grade_cfg.stone_quality or 0

	for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
		if self.wardrobe_casting_list[j] then
			self.wardrobe_casting_list[j]:SetSlotQuality(stone_quality)
			self.wardrobe_casting_list[j]:SetData(list[j] or -1)
		end
	end

	-- 刷新天石技能
	local list = WardrobeWGData.Instance:GetForgeEffectCfgByPartIndex(self.show_data.fashion_part_type, self.show_data.fashion_index)
	if list and list[1] then
		self.node_list.wardrobe_casting_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(list[1].skill_icon))
	end
end

-- 刷新方案信息
function WardrobeCastingView:FlushRightShowMessage(is_not_check)
	if not self.show_data then
		return
	end

	local forge_data = WardrobeWGData.Instance:GetShizhuangForgeInfoByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local grade_level = forge_data and forge_data.grade_level or 0
	local star = forge_data and forge_data.star or 0
	local name = fashion_cfg and fashion_cfg.name or ""
	self.node_list.upgrade_level.text.text = grade_level
	self.node_list.casting_slot_name.tmp.text = name
	local star_grade = math.floor(star / 10)
	local star_show = star % 10
	local real_shar_show = star_show
	local is_need_upgrade = false

	if not is_not_check then
		if self:CheckOperateUpgradeResult(grade_level) then
			self.cache_star = star
		else
			self:CheckOperateJingLianResult(star)
		end
	end

	if star_show == 0 and star_grade ~= 0 then
		if star_grade ~= grade_level then	-- 未突破
			real_shar_show = 10	
			is_need_upgrade = true
		else	-- 已突破
			real_shar_show = star_show
		end
	end

	for i, v in ipairs(self.casting_star_list) do
		local str = i <= real_shar_show and "a3_ty_xx_zc" or "a3_ty_xx_zc0"
		v.image:LoadSprite(ResPath.GetCommonImages(str))
	end

	self.node_list.casting_spend_up_item_root:CustomSetActive(not is_need_upgrade)
	if not is_need_upgrade then
		local forge_star_protect_id = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("forge_star_protect_id")
		local forge_star_protect_consume_num = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("forge_star_protect_consume_num")
		local item_num = ItemWGData.Instance:GetItemNumInBagById(forge_star_protect_id)
		local color = item_num >= forge_star_protect_consume_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.casting_spend_up_item_cell:SetData({ item_id = forge_star_protect_id })
		self.casting_spend_up_item_cell:SetRightBottomTextVisible(true)
		self.casting_spend_up_item_cell:SetRightBottomColorText(item_num .. '/' .. forge_star_protect_consume_num, color)
	end

	local str = is_need_upgrade and Language.Wardrobe.CastingBtnName2 or Language.Wardrobe.CastingBtnName1
	self.node_list.btn_casting_up_txt.text.text = str

	local spend_cfg =  nil
	if is_need_upgrade then
		spend_cfg = WardrobeWGData.Instance:GetForgeGradeCfgByLevel(grade_level)
	else
		spend_cfg = WardrobeWGData.Instance:GetForgeStarCfgByStar(star)
	end

	self.node_list.casting_suc_txt:CustomSetActive(not is_need_upgrade and spend_cfg ~= nil)
	self.node_list.casting_suc_tips_txt:CustomSetActive(not is_need_upgrade and spend_cfg ~= nil)
	self.node_list.casting_upgrade_suc_txt:CustomSetActive(is_need_upgrade)
	self.node_list.btn_casting_up:CustomSetActive(spend_cfg ~= nil)
	self.node_list.casting_up_flag:CustomSetActive(spend_cfg == nil)
	local is_remind = false

	if spend_cfg then
		local star_up_item_id = spend_cfg.consume_id
		local star_up_item_num = spend_cfg.consume_num
		local item_num = ItemWGData.Instance:GetItemNumInBagById(star_up_item_id)
		local color = item_num >= star_up_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		is_remind = item_num >= star_up_item_num
		self.casting_spend_item:SetData({ item_id = star_up_item_id })
		self.casting_spend_item:SetRightBottomTextVisible(true)
		self.casting_spend_item:SetRightBottomColorText(item_num .. '/' .. star_up_item_num, color)
	end

	if is_need_upgrade then
		local list = forge_data and forge_data.stone_part_list or {}
		local now_solt_stone = 0
		local grade_level = forge_data and forge_data.grade_level or 0
		local grade_cfg = WardrobeWGData.Instance:GetForgeGradeCfgByLevel(grade_level)
		local stone_quality = grade_cfg and grade_cfg.stone_quality or 1
		stone_quality = stone_quality + 1	-- 缘晶默认大一个品阶
		local need_stone_num = WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT + 1

		for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
			if list[j] ~= nil and list[j] ~= -1 then
				now_solt_stone = now_solt_stone + 1
			end
		end

		local color = now_solt_stone >= need_stone_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		local str1 = ToColorStr(string.format("(%d/%d)", now_solt_stone, need_stone_num), color)
		local str2 = ToColorStr(string.format(Language.Wardrobe.CastingUpgradeTips7, Language.Common.ColorName4[stone_quality]), ITEM_COLOR[stone_quality])
		local str3 = string.format(Language.Wardrobe.CastingUpgradeTips6, need_stone_num, str2, str1)
		self.node_list.casting_upgrade_suc_txt.tmp.text = str3
		is_remind = is_remind and now_solt_stone >= need_stone_num
	end

	self.node_list.btn_casting_up_remind:CustomSetActive(is_remind)
	local attr_list = WardrobeWGData.Instance:GetShiZhuangForgeAttrList(self.show_data.fashion_part_type, self.show_data.fashion_index)
	for i, attr_cell in ipairs(self.casting_attr_list) do
		attr_cell:SetVisible(attr_list[i] ~= nil)
		if attr_list[i] ~= nil then
			attr_cell:SetData(attr_list[i])
		end
	end

	-- 计算战斗力
	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

    for index, attr_cell in ipairs(attr_list) do
		if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
			add_tab(attr_str, attr_cell.attr_value)
		end
    end

	local cap = AttributeMgr.GetCapability(attribute)
    self.node_list.wardrobe_casting_cap_value.text.text = cap

	if not is_need_upgrade then
		self:FlushNowCastingPer()
	end
end

-- 刷新打造成功率
function WardrobeCastingView:FlushNowCastingPer()
	if not self.show_data then
		return
	end
	local forge_data = WardrobeWGData.Instance:GetShizhuangForgeInfoByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local star = forge_data and forge_data.star or 0
	local per = WardrobeWGData.Instance:GetForgeSucPerByStar(star)
	local per_value = math.floor(per / 10)
	self.node_list.casting_suc_txt.tmp.text = string.format(Language.Common.SuccessRate, per_value) 
	local str = self.is_click_per_up and Language.Wardrobe.CastingUpgradeTips or Language.Wardrobe.CastingUpgradeTips2
	self.node_list.casting_suc_tips_txt.tmp.text = str
end

-- 刷新保底物品选中
function WardrobeCastingView:FlushSelectPerUp(is_click)
	local forge_star_protect_id = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("forge_star_protect_id")
	local forge_star_protect_consume_num = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("forge_star_protect_consume_num")
	local item_num = ItemWGData.Instance:GetItemNumInBagById(forge_star_protect_id)

	if item_num < forge_star_protect_consume_num then
		self.is_click_per_up = false

		if is_click then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
		end
	end

	self.node_list.casting_spend_up_check:CustomSetActive(self.is_click_per_up == true)
end

-- 刷新模型
function WardrobeCastingView:FlushRoleModel()
	if self.is_init_role_model then
		return
	end	

	if not self.show_data then
		return
	end

	self.is_init_role_model = true
	local user_fashion_model_data = {}
	local role_res_id = AppearanceWGData.Instance:GetRoleResId()
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local res_id = fashion_cfg and fashion_cfg.resouce or 0

	if res_id == 0 then
		res_id = role_res_id
	end
	user_fashion_model_data.body_res_id = res_id
	user_fashion_model_data.cur_anim = SceneObjAnimator.Rest
	user_fashion_model_data.model_rt_type = ModelRTSCaleType.M
	user_fashion_model_data.is_ui_scene = true
	user_fashion_model_data.foot_effect_id = 0
	self.role_model:SetData(user_fashion_model_data)
	self.role_model:SetUSAdjustmentNodeLocalScale(1)
	self.role_model:SetUSAdjustmentNodeLocalPosition(Vector3(-0.8, 0, 0))
end

-- 精炼结果
function WardrobeCastingView:CheckOperateJingLianResult(star)
	if not self.cache_star then
		self.cache_star =  star
		return
	end

	if not self.is_operate_send then
		self.cache_star =  star
		return false
	end

	local success = star > self.cache_star
	local effect_type = success and UIEffectName.s_jinglian or UIEffectName.f_jinglian
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
						is_success = success, pos = Vector2(0, 0), parent_node = self.node_list["wardrobe_casting_effect"]})
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))

	self.cache_star =  star
	self.is_operate_send = false
end

-- 升阶结果
function WardrobeCastingView:CheckOperateUpgradeResult(upgrade)
	if not self.cache_upgrade then
		self.cache_upgrade =  upgrade
		return false
	end

	local success = upgrade > self.cache_upgrade
	if not success then
		self.cache_upgrade =  upgrade
		return false
	end

	if not self.is_operate_send then
		self.cache_upgrade =  upgrade
		return false
	end

	local effect_type = UIEffectName.ty_shengji
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
						is_success = success, pos = Vector2(0, 0), parent_node = self.node_list["wardrobe_casting_effect"]})
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	self.cache_upgrade =  upgrade
	self.is_operate_send = false

	if self.role_model then
		self.role_model:ChangeRoleAction(SceneObjAnimator.Rest)
	end

	return true
end

------------------------------------------------------------------------------------------
-- 天赏操作    
function WardrobeCastingView:OnClickCastingUp()
	if not self.show_data then
		return
	end

	local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.show_data.fashion_part_type, self.show_data.fashion_index)
	if not is_act then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Wardrobe.CastingUpgradeTips5)
		return
	end

	local forge_data = WardrobeWGData.Instance:GetShizhuangForgeInfoByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local grade_level = forge_data and forge_data.grade_level or 0
	local star = forge_data and forge_data.star or 0
	local star_grade = math.floor(star / 10)
	local star_show = star % 10
	local real_shar_show = 0
	local is_need_upgrade = false
	local grade_cfg = WardrobeWGData.Instance:GetForgeGradeCfgByLevel(grade_level)
	local grade_quality = grade_cfg and grade_cfg.stone_quality or 0
	local client_seq = WardrobeWGData.Instance:GetShizhuangForgeIndexByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local server_seq = client_seq - 1

	if star_show == 0 and star_grade ~= 0 then
		if star_grade ~= grade_level then	-- 未突破
			is_need_upgrade = true
		end
	end

	local spend_cfg =  nil
	if is_need_upgrade then
		spend_cfg = WardrobeWGData.Instance:GetForgeGradeCfgByLevel(grade_level)
	else
		spend_cfg = WardrobeWGData.Instance:GetForgeStarCfgByStar(star)
	end

	if spend_cfg then
		local aim_item_id = spend_cfg.consume_id
		local aim_item_num = spend_cfg.consume_num

		local item_num = ItemWGData.Instance:GetItemNumInBagById(aim_item_id)
		if item_num >= aim_item_num then
			if is_need_upgrade then
				local list = forge_data and forge_data.stone_part_list or {}
				local is_can_upgrade = true
			
				for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
					if list[j] == -1 then
						is_can_upgrade = false
						break
					end

					local stone_cfg = WardrobeWGData.Instance:GetStoneCfgBySeq(list[j])
					local stone_quality = stone_cfg and stone_cfg.stone_quality or -1
					if stone_quality ~= grade_quality then
						is_can_upgrade = false
						break
					end
				end

				if is_can_upgrade then
					self.is_operate_send = true
					WardrobeWGCtrl.Instance:SendShiZhuangForgeUpGrade(server_seq)
				else
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Wardrobe.CastingUpgradeTips3)
				end
			else
				self.is_operate_send = true
				WardrobeWGCtrl.Instance:SendShiZhuangForgeUpStar(server_seq, self.is_click_per_up and 1 or 0)
			end
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = aim_item_id })
		end
	end
end 

-- 天赏技能 		
function WardrobeCastingView:OnClickCastingSkill()
	local list = WardrobeWGData.Instance:GetForgeEffectCfgByPartIndex(self.show_data.fashion_part_type, self.show_data.fashion_index)
	if not list then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Wardrobe.CastingUpgradeTips4)
		return
	end

	WardrobeWGCtrl.Instance:OpenCastingSkillView(self.show_data)
end

-- 点击使用提升概率物品
function WardrobeCastingView:OnClickCastingSpendUp()
	self.is_click_per_up = not self.is_click_per_up
	self:FlushSelectPerUp(true)
	self:FlushNowCastingPer()
end

----------------------------------方案item-----------------------
WardrobeCastingItemRender = WardrobeCastingItemRender or BaseClass(BaseRender)
function WardrobeCastingItemRender:__delete()
	self.now_quality = nil
end

function WardrobeCastingItemRender:SetSlotQuality(quality)
	self.now_quality = quality
end

function WardrobeCastingItemRender:OnFlush()
    if not self.data then
        return
    end
	
	local show_quality = self.now_quality ~= nil and self.now_quality or 1
	self.node_list.normal_bg.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_tsdz_yq_%d", show_quality)))
	local stone_cfg = WardrobeWGData.Instance:GetStoneCfgBySeq(self.data)
	local is_have_stone = stone_cfg ~= nil

	self.node_list.normal_icon:CustomSetActive(is_have_stone)
	self.node_list.name_bg:CustomSetActive(is_have_stone)
	self.node_list.name_txt:CustomSetActive(is_have_stone)

	if stone_cfg then
		self.node_list.name_txt.tmp.text = stone_cfg.stone_name
		self.node_list.normal_icon.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_tsdz_icon_%d", stone_cfg.stone_icon)))
		self.node_list.normal_bg.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_tsdz_yq_%d", stone_cfg.stone_quality)))
	end

	local is_remind = false
	if self.data == -1 then
		local now_stone_cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(show_quality, self.index)
		local aim_stone_item = now_stone_cfg and now_stone_cfg.stone_item_id or 0
		local item_num = ItemWGData.Instance:GetItemNumInBagById(aim_stone_item)
		if item_num >= 1 then
			is_remind = true
		end
	end

	if self.node_list.remind then
		self.node_list.remind:CustomSetActive(is_remind)
	end
end
