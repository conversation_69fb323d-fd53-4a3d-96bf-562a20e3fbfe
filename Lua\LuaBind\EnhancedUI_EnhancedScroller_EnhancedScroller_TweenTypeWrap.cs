﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class EnhancedUI_EnhancedScroller_EnhancedScroller_TweenTypeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>(typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
		<PERSON><PERSON>("immediate", get_immediate, null);
		<PERSON><PERSON>("linear", get_linear, null);
		<PERSON><PERSON>("spring", get_spring, null);
		<PERSON><PERSON>("easeInQuad", get_easeInQuad, null);
		<PERSON><PERSON>("easeOutQuad", get_easeOutQuad, null);
		<PERSON><PERSON>("easeInOutQuad", get_easeInOutQuad, null);
		<PERSON><PERSON>("easeInCubic", get_easeInCubic, null);
		<PERSON><PERSON>("easeOutCubic", get_easeOutCubic, null);
		<PERSON><PERSON>("easeInOutCubic", get_easeInOutCubic, null);
		<PERSON><PERSON>("easeInQuart", get_easeInQuart, null);
		<PERSON><PERSON>("easeOutQuart", get_easeOutQuart, null);
		<PERSON><PERSON>Var("easeInOutQuart", get_easeInOutQuart, null);
		L.RegVar("easeInQuint", get_easeInQuint, null);
		L.RegVar("easeOutQuint", get_easeOutQuint, null);
		L.RegVar("easeInOutQuint", get_easeInOutQuint, null);
		L.RegVar("easeInSine", get_easeInSine, null);
		L.RegVar("easeOutSine", get_easeOutSine, null);
		L.RegVar("easeInOutSine", get_easeInOutSine, null);
		L.RegVar("easeInExpo", get_easeInExpo, null);
		L.RegVar("easeOutExpo", get_easeOutExpo, null);
		L.RegVar("easeInOutExpo", get_easeInOutExpo, null);
		L.RegVar("easeInCirc", get_easeInCirc, null);
		L.RegVar("easeOutCirc", get_easeOutCirc, null);
		L.RegVar("easeInOutCirc", get_easeInOutCirc, null);
		L.RegVar("easeInBounce", get_easeInBounce, null);
		L.RegVar("easeOutBounce", get_easeOutBounce, null);
		L.RegVar("easeInOutBounce", get_easeInOutBounce, null);
		L.RegVar("easeInBack", get_easeInBack, null);
		L.RegVar("easeOutBack", get_easeOutBack, null);
		L.RegVar("easeInOutBack", get_easeInOutBack, null);
		L.RegVar("easeInElastic", get_easeInElastic, null);
		L.RegVar("easeOutElastic", get_easeOutElastic, null);
		L.RegVar("easeInOutElastic", get_easeInOutElastic, null);
		L.RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType>.Check = CheckType;
		StackTraits<EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType>.Push = Push;
	}

	static void Push(IntPtr L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_immediate(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.immediate);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_linear(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.linear);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spring(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.spring);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInQuad(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInQuad);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutQuad(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutQuad);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutQuad(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutQuad);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInCubic(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInCubic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutCubic(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutCubic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutCubic(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutCubic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInQuart(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInQuart);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutQuart(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutQuart);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutQuart(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutQuart);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInQuint(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInQuint);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutQuint(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutQuint);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutQuint(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutQuint);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInSine(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInSine);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutSine(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutSine);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutSine(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutSine);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInExpo(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInExpo);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutExpo(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutExpo);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutExpo(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutExpo);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInCirc(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInCirc);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutCirc(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutCirc);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutCirc(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutCirc);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInBounce(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInBounce);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutBounce(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutBounce);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutBounce(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutBounce);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInBack(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInBack);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutBack(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutBack);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutBack(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutBack);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInElastic(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInElastic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeOutElastic(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeOutElastic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_easeInOutElastic(IntPtr L)
	{
		ToLua.Push(L, EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType.easeInOutElastic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType o = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

