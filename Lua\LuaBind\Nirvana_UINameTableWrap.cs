﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_UINameTableWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Nirvana.UINameTable), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>un<PERSON>("Find", Find);
		<PERSON><PERSON>ction("Add", Add);
		<PERSON><PERSON>unction("GetCount", GetCount);
		L.RegFunction("GetBindPair", GetBindPair);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("binds", get_binds, set_binds);
		<PERSON><PERSON>("Lookup", get_Lookup, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Find(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UINameTable obj = (Nirvana.UINameTable)ToLua.CheckObject(L, 1, typeof(Nirvana.UINameTable));
			string arg0 = ToLua.CheckString(L, 2);
			UnityEngine.GameObject o = obj.Find(arg0);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Add(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			Nirvana.UINameTable obj = (Nirvana.UINameTable)ToLua.CheckObject(L, 1, typeof(Nirvana.UINameTable));
			string arg0 = ToLua.CheckString(L, 2);
			UnityEngine.GameObject arg1 = (UnityEngine.GameObject)ToLua.CheckObject(L, 3, typeof(UnityEngine.GameObject));
			bool o = obj.Add(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UINameTable obj = (Nirvana.UINameTable)ToLua.CheckObject(L, 1, typeof(Nirvana.UINameTable));
			int o = obj.GetCount();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetBindPair(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UINameTable obj = (Nirvana.UINameTable)ToLua.CheckObject(L, 1, typeof(Nirvana.UINameTable));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			Nirvana.UINameTable.BindPair o = obj.GetBindPair(arg0);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_binds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UINameTable obj = (Nirvana.UINameTable)o;
			System.Collections.Generic.List<Nirvana.UINameTable.BindPair> ret = obj.binds;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index binds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Lookup(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UINameTable obj = (Nirvana.UINameTable)o;
			System.Collections.Generic.Dictionary<string,UnityEngine.GameObject> ret = obj.Lookup;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Lookup on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_binds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UINameTable obj = (Nirvana.UINameTable)o;
			System.Collections.Generic.List<Nirvana.UINameTable.BindPair> arg0 = (System.Collections.Generic.List<Nirvana.UINameTable.BindPair>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<Nirvana.UINameTable.BindPair>));
			obj.binds = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index binds on a nil value");
		}
	}
}

