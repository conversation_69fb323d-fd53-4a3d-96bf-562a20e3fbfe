ReikiSeedWGData = ReikiSeedWGData or BaseClass()
function ReikiSeedWGData:__init()
	if ReikiSeedWGData.Instance then
		error("[ReikiSeedWGData] Attempt to create singleton twice!")
		return
	end

    ReikiSeedWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.ReikiSeed, BindTool.Bind(self.ShowReikiSeedRemind, self))
end

function ReikiSeedWGData:__delete()
    self.grade = nil
	self.draw_times = nil
	self.lucky = nil
	self.times_reward_flag = nil
	self.convert_times_list = nil
	self.cache_draw_btn_index = nil
	self.result_reward_list = nil
    self.baodi_item = nil
    ReikiSeedWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.ReikiSeed)
end

function ReikiSeedWGData:InitParam()
    self.grade = 1 -- 档次
	self.draw_times = 0 --次数
	self.lucky = 0 -- 幸运值
	self.times_reward_flag = {} -- 次数奖励领取状态
	self.convert_times_list = {} -- 兑换次数
	self.cache_draw_btn_index = 1
	self.result_reward_list = {}
    self.baodi_item = {}
end

function ReikiSeedWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_glory_crystal3_auto")
	self.times_reward_cfg = ListToMap(cfg.times_reward, "grade", "seq")
	self.reward_pool_cfg = ListToMap(cfg.reward_pool, "grade", "seq")
	self.mode_cfg = ListToMap(cfg.mode, "mode")
	self.baodi_cfg = ListToMap(cfg.baodi, "grade")
	self.convert_cfg = ListToMap(cfg.convert, "grade", "seq")
	self.stuff_cfg = ListToMap(cfg.convert, "stuff_id_1")
    self.item_random_desc = ListToMapList(cfg.item_random_desc, "grade")
	self.other_cfg = cfg.other[1]
end

function ReikiSeedWGData:SetAllInfo(protocol)
    self.grade = protocol.grade
    self.draw_times = protocol.draw_times
    self.lucky = protocol.lucky
    self.times_reward_flag = protocol.times_reward_flag
    self.convert_times_list = protocol.convert_times_list
end

function ReikiSeedWGData:SetResultData(protocol)
    self.result_reward_list = protocol.result_item_list
    self.baodi_item = protocol.baodi_item
end

--档次
function ReikiSeedWGData:GetCurGrade()
	return self.grade
end

--次数奖励领取状态
function ReikiSeedWGData:GetTimesRewardState(seq)
	return self.times_reward_flag[seq] or 0
end

function ReikiSeedWGData:GetAllRewardState()
	return self.times_reward_flag
end

--抽奖次数
function ReikiSeedWGData:GetDrawTimes()
	return self.draw_times
end

--获取幸运值
function ReikiSeedWGData:GetLuckyValue()
	return self.lucky
end

--兑换次数
function ReikiSeedWGData:GetConvertTimesBySeq(seq)
	return self.convert_times_list[seq] or 0
end

--获取保底item
function ReikiSeedWGData:GetBaodItem()
	return self.baodi_item
end

--获取抽奖奖励
function ReikiSeedWGData:GetResultRewardList()
	return self.result_reward_list
end

function ReikiSeedWGData:GetOtherCfg()
	return self.other_cfg
end

function ReikiSeedWGData:GetModeCfgByMode(mode)
	return self.mode_cfg[mode]
end

function ReikiSeedWGData:GetBaoDiCfgByGrade(grade)
	return self.baodi_cfg[grade]
end

function ReikiSeedWGData:GetAllTimesRewardCfg()
	return self.times_reward_cfg[self.grade] or {}
end

function ReikiSeedWGData:GetAllTimesRewardInfo()
	local sort_list = {}
	local cur_grade_times_reward_cfg = self.times_reward_cfg[self.grade]
	local index = 0
	if not IsEmptyTable(cur_grade_times_reward_cfg) then
		for k, v in pairs(cur_grade_times_reward_cfg) do
			index = index + 1
	 		sort_list[index] = {}
			sort_list[index].grade = v.grade
			sort_list[index].seq = v.seq
			sort_list[index].need_draw_times = v.need_draw_times
			sort_list[index].item = v.item
			sort_list[index].sort_index = 0
			local state = self:GetTimesRewardState(v.seq)
			if state == 1 then -- 已经领取奖励
				sort_list[index].sort_index = 1000
				sort_list[index].state = REWARD_STATE_TYPE.FINISH
			elseif state == 0 and self.draw_times >= v.need_draw_times then --可领取
				sort_list[index].sort_index = 10
				sort_list[index].state = REWARD_STATE_TYPE.CAN_FETCH
			else --不可领取
				sort_list[index].sort_index = 100
				sort_list[index].state = REWARD_STATE_TYPE.UNDONE
			end
		end

		table.sort(sort_list, SortTools.KeyLowerSorters("sort_index", "seq"))
	end

	return sort_list
end

function ReikiSeedWGData:GetShowRewardPool()
	local show_list = {}
	local cur_grade_reward_pool_cfg = self.reward_pool_cfg[self.grade]
	if not IsEmptyTable(cur_grade_reward_pool_cfg) then
		for k, v in pairs(cur_grade_reward_pool_cfg) do
			if v.show_item == 1 then
				table.insert(show_list, v)
			end
		end
	end

	return show_list
end

function ReikiSeedWGData:GetShowConvertData()
	local sort_list = {}
	local cur_grade_convert_cfg = self.convert_cfg[self.grade] or {}
	local index = 0
	if not IsEmptyTable(cur_grade_convert_cfg) then
		for k, v in pairs(cur_grade_convert_cfg) do
			index = index + 1
	 		sort_list[index] = {}
			sort_list[index].grade = v.grade
			sort_list[index].seq = v.seq
			sort_list[index].time_limit = v.time_limit
			sort_list[index].item = v.item
			sort_list[index].stuff_id_1 = v.stuff_id_1  --策划说只用一种材料
			sort_list[index].stuff_num_1 = v.stuff_num_1
			sort_list[index].sort_index = 0
			local cur_convert_times = self:GetConvertTimesBySeq(v.seq)
			sort_list[index].cur_convert_times = cur_convert_times
			if cur_convert_times >= v.time_limit then --兑换次数达到上限
				sort_list[index].sort_index = 100
			else
				sort_list[index].sort_index = 10
			end
		end

		table.sort(sort_list, SortTools.KeyLowerSorters("sort_index", "seq"))
	end

	return sort_list
end

--兑换材料
function ReikiSeedWGData:GetIsStuffItem(item_id)
    return self.stuff_cfg[item_id] ~= nil
end

--获取抽奖的选项
function ReikiSeedWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end


-- enable_anim为false表示跳过动画(奖励界面)
function ReikiSeedWGData:SetRewardAnimToggleData(enable_anim)
	self.reward_anim_toggle_enable = enable_anim
end

-- 返回false表示跳过动画(奖励界面)
function ReikiSeedWGData:GetRewardAnimToggleData()
	return self.reward_anim_toggle_enable or false
end

--抽獎界面动画（true 为跳过动画）
function ReikiSeedWGData:GetJumpAni()
	return self.jump_ani or false
end

function ReikiSeedWGData:SetJumpAni()
	self.jump_ani = not self.jump_ani
end

--是否跳过动画对应的延时
function ReikiSeedWGData:GetDelayTime()
    --是否跳过动画
    if self.jump_ani then
        return 0
    else
        return 2
    end
end

function ReikiSeedWGData:ShowReikiSeedRemind()
	--累计抽奖次数奖励红点
	local info = self:GetAllTimesRewardInfo()
	for i, v in pairs(info) do
		if v.state == REWARD_STATE_TYPE.CAN_FETCH then
			return 1
		end
	end

    local cost_item_id = ReikiSeedWGData.Instance:GetOtherCfg().cost_item_id
    local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    if item_num > 0 then
        return 1
    end

	return 0
end

function ReikiSeedWGData:GetGaiLvInfo()
	return self.item_random_desc[self.grade] or {}
end