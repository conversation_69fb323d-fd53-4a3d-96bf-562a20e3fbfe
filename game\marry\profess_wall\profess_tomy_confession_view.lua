function ProfessView:LoadToMyConfessionViewCallBack()
    if not self.pw_tomy_list then
        self.pw_tomy_list = AsyncBaseGrid.New()
        local bundle = "uis/view/marry_ui_prefab"
		local asset = "profess_note_tomy_item"
        self.pw_tomy_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["pw_tomy_list"],
            assetBundle = bundle, assetName = asset, itemRender = ProfessWallInfoToMyCellRender})
        self.pw_tomy_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_pw_tomy_qiuhun, BindTool.Bind(self.OpenSelectPWToMyProfess, self))
end

function ProfessView:ShowToMyConfessionViewCallBack()

end

function ProfessView:ReleaseToMyConfessionViewCallBack()
    if self.pw_tomy_list then
        self.pw_tomy_list:DeleteMe()
        self.pw_tomy_list = nil
    end
end

function ProfessView:OnFlushToMyConfessionViewCallBack()
    local _, temp_data = ActivePerfertQingrenWGData.Instance:GetPersonalInfo()
    local no_data = IsEmptyTable(temp_data)
    self.pw_tomy_list:SetDataList(temp_data)
    self.node_list.pw_tomy_no_data:CustomSetActive(no_data)
    self.node_list.pw_tomy_list:CustomSetActive(not no_data)
end

function ProfessView:OpenSelectPWToMyProfess()
	ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
end

-----------------------------ProfessWallInfoToMyCellRender------------------------------
ProfessWallInfoToMyCellRender = ProfessWallInfoToMyCellRender or BaseClass(ProfessWallInfoAllCellRender)

function ProfessWallInfoToMyCellRender:SetHeadCell()
    local flush_fun = function (protocol)
        local data = {role_id = protocol.role_id, prof = protocol.prof, sex = protocol.sex, fashion_photoframe = 0}
        self.head_cell:SetData(data)

        local bundle = "uis/view/marry_ui/images_atlas"
        local asset = "a3_qy_tq_yd"
        self.head_cell:ChangeBg(bundle, asset, true)
    end

    BrowseWGCtrl.Instance:BrowRoelInfo(self.data.other_role_id, flush_fun, RoleWGData.Instance.role_vo.plat_type)
end