
BossAssistView = BossAssistView or BaseClass(SafeBaseView)

BossAssistView.MAX_DAY_ASSIST_SHENGWANG = 1000

function BossAssistView:__init()
	self.default_index = 12
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(9, 0), sizeDelta = Vector2(1010, 690)})
	-- self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_assist_common")
	self:AddViewResource(TabIndex.boss_xiezhu, "uis/view/boss_assist_ui_prefab", "layout_assist_view")
	self:AddViewResource(TabIndex.kajia_xiezhu, "uis/view/boss_assist_ui_prefab", "layout_kajia_assist_view")
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "HorizontalTabbar", {vector2 = Vector2(158, 281)})
end

function BossAssistView:ReleaseCallBack()
	if self.assist_list then
		self.assist_list:DeleteMe()
		self.assist_list = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	self:KanJiaReleaseCallBack()
end

function BossAssistView:LoadCallBack()
	-- local remind_tab = {
	-- 	{RemindName.BossXieZhu, RemindName.KanJiaXieZhu},
	-- }

	-- self.tabbar = Tabbar.New(self.node_list)
	-- self.tabbar:Init(nil, Language.BossAssist.TabGrop, nil, nil, remind_tab)
	-- self.tabbar:SetSelectCallback(BindTool.Bind1(self.TabbarClick, self))
	self.node_list.title_view_name.text.text = Language.Boss.AssistTitle

	-- XUI.AddClickEventListener(self.node_list.btn_help, BindTool.Bind1(self.OnclickHelp, self))
end

function BossAssistView:LoadIndexCallBack(index)
	if index == TabIndex.boss_xiezhu then
		self.assist_list = AsyncListView.New(BossAssistItemRender, self.node_list.assist_list)
	elseif index == TabIndex.kajia_xiezhu then
		self:KanJiaInit()
	end
end

function BossAssistView:ShowIndexCallBack(index)
	self:Flush(index)
end

function BossAssistView:OnclickHelp()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetContent(Language.BossAssist.BossAssistTips2, Language.BossAssist.BossAssistTipsTitle2)
	end
end

function BossAssistView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.boss_xiezhu then
				self.assist_list:JumpToTop()
			elseif index == TabIndex.kajia_xiezhu then
				self:KanJiaFlush()
            end
            -- self:FlushXieZhuNum()
		end
	end
end

function BossAssistView:FlushXieZhuNum()
	self.node_list.richange_text.text.text = string.format("<color=#006a25>%s</color><color=#432612>/%s</color>", RoleWGData.Instance.role_vo.day_reward_shengwang, VipPower.Instance:GetParam(VipPowerId.reward_shengwang_day_max))
	self.node_list.assist_text.text.text = string.format("<color=#006a25>%s</color><color=#432612>/%s</color>", RoleWGData.Instance.role_vo.day_assist_shengwang, BossAssistView.MAX_DAY_ASSIST_SHENGWANG)
end

function BossAssistView:TabbarClick(index)
	self:ChangeToIndex(index)
end
----------------------------------------------------------------------------
-- BossAssistItemRender
----------------------------------------------------------------------------
BossAssistItemRender = BossAssistItemRender or BaseClass(BaseRender)
function BossAssistItemRender:__init()

end

function BossAssistItemRender:__delete()
	if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function BossAssistItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.assist_btn, BindTool.Bind(self.GotoAssist, self))
	self.head_cell = BaseHeadCell.New(self.node_list["img_cell"])
end

function BossAssistItemRender:OnFlush()
	self.node_list.lbl_camp_username.text.text = self.data.role_name

	local is_df, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.label_level.text.text = "Lv" .. level
	self.node_list["img_huo"]:SetActive(is_df)
	--self.node_list.label_level.text.text = RoleWGData.GetLevelString(self.data.level) .. Language.Common.Ji

	if self.data.vip_level then   -- 设置VIP等级
		self.node_list.vip_img:SetActive(self.data.vip_level >= 0)
		self.node_list.vip_img.image:LoadSprite(ResPath.GetFloatingImage(string.format("vip%d", self.data.vip_level)))
	else
		self.node_list.vip_img:SetActive(false)
	end

	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.target_boss_id]
	if monster_cfg then
		self.node_list.boss_info.text.text = string.format(Language.BossAssist.KillBossText, monster_cfg.name .. "Lv." .. monster_cfg.level)
	end
	local scene_config = ConfigManager.Instance:GetSceneConfig(self.data.target_boss_scene_id)
    if scene_config then
        self.node_list.scene_name.text.text = scene_config.name
    end

    local need_level = BossWGData.Instance:GetBossLevelLimitBySceneId(self.data.target_boss_scene_id)
    local role_level = RoleWGData.Instance:GetRoleLevel()

    self.node_list.assist_btn_text.text.text = role_level >= need_level and Language.BossAssist.AssistBtnTxt[1] or ToColorStr(Language.BossAssist.AssistBtnTxt[2], COLOR3B.RED)

	self.head_cell:SetData(self.data)
end

function BossAssistItemRender:GotoAssist()
	local need_level = BossWGData.Instance:GetBossLevelLimitBySceneId(self.data.target_boss_scene_id)
    local role_level = RoleWGData.Instance:GetRoleLevel()
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	local str = ""

	if role_level < need_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossAssist.AssistTips6)
		--ViewManager.Instance:Close(GuideModuleName.BossAssist)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId()
	if SceneType.Common ~= scene_type and scene_id ~= self.data.target_boss_scene_id then
		TipWGCtrl.Instance:ShowSystemMsg(Language.BossAssist.AssistTips5)
		return
	end

	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		str = Language.BossAssist.AssistTips3
	elseif assist_info.target_boss_id > 0 then
		if assist_info.assist_role_id > 0 then
			if assist_info.assist_role_id == self.data.role_id then
				TipWGCtrl.Instance:ShowSystemMsg(Language.BossAssist.AssistTips4)
				return
			else
				str = Language.BossAssist.AssistTips2
			end
		else
			str = Language.BossAssist.AssistTips1
		end
	else
		self:SendAssist()
		return
	end
	if not self.alert_tips then
		self.alert_tips = Alert.New()
		self.alert_tips:SetOkFunc(BindTool.Bind(self.SendAssist, self))
	end
	self.alert_tips:SetLableString(string.format(str, self.data.role_name))
	self.alert_tips:Open()

end

function BossAssistItemRender:SendAssist()
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		SocietyWGCtrl.Instance:SendExitTeam()
	end

	BossAssistWGCtrl.SendGuildAssistGoToHelp(self.data.role_id, self.data.target_boss_scene_id, self.data.target_boss_id)
	ViewManager.Instance:Close(GuideModuleName.BossAssist)
end