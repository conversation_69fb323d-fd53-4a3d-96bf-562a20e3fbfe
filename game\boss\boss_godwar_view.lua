function BossNewPrivilegeView:BossGodwarLoadCallBack()
	if not self.cur_godwar_item_list then
		self.cur_godwar_item_list = AsyncListView.New(ItemCell, self.node_list.cur_godwar_item_list)
		self.cur_godwar_item_list:SetStartZeroIndex(true)
	end

	if not self.next_godwar_item_list then
		self.next_godwar_item_list = AsyncListView.New(ItemCell, self.node_list.next_godwar_item_list)
		self.next_godwar_item_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list["godwar_btn_opening"], BindTool.Bind1(self.OnClickSetCloseState, self))    --关闭特权
	XUI.AddClickEventListener(self.node_list["godwar_btn_open"], BindTool.Bind1(self.OnClickBossGodwarSetOpenState, self)) --开启特权
	XUI.AddClickEventListener(self.node_list["godwar_btn_buy"], BindTool.Bind(self.OnClickBossGodwarLevelUp, self))
	XUI.AddClickEventListener(self.node_list["right_get_reward_button"],
		BindTool.Bind(self.OnClickBossGodwarGetReward, self))
end

function BossNewPrivilegeView:BossGodwarReleaseCallBack()
	if self.cur_godwar_item_list then
		self.cur_godwar_item_list:DeleteMe()
		self.cur_godwar_item_list = nil
	end

	if self.next_godwar_item_list then
		self.next_godwar_item_list:DeleteMe()
		self.next_godwar_item_list = nil
	end
end

function BossNewPrivilegeView:BossGodwarOnFlush()
	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	local cur_cfg, next_cfg = BossGodWarWGData.Instance:GetPrivilegeCfgByLevel(god_war_data.level)
	local is_act = cur_cfg ~= nil

	--设置每日奖励
	local cur_items = cur_cfg and cur_cfg.item
	local next_items = next_cfg and next_cfg.item

	if cur_items then
		self.cur_godwar_item_list:SetDataList(cur_items)
	end
	if next_items then
		self.next_godwar_item_list:SetDataList(next_items)
	end

	local str = Language.BossPrivilege.GodWarTxt
	local next_str = Language.BossPrivilege.GodWarTxt2

	if cur_cfg then
		str = string.format(Language.BossPrivilege.GodWarTimesTxt2, ToColorStr(cur_cfg.add_count, COLOR3B.C2))
	end

	if next_cfg then
		if is_act then
			next_str = string.format(Language.BossPrivilege.GodWarTimesTxt3,
				ToColorStr(next_cfg.add_count, COLOR3B.C2))
		else
			next_str = string.format(Language.BossPrivilege.GodWarTimesTxt, ToColorStr(next_cfg.add_count, COLOR3B.C2))
		end
	end

	self.node_list.curr_desc.text.text = str
	self.node_list.next_desc.text.text = next_str

	-- 设置购买价格
	if next_cfg then
		local price = RoleWGData.GetPayMoneyStr(next_cfg.rmb_price, next_cfg.rmb_type, next_cfg.rmb_seq)
		self.node_list.btn_buy_txt.text.text = string.format(Language.BossPrivilege.GodWarBuyBtnText, price)
	end

	-- 今日剩余次数
	self.node_list.godwar_rule_times.text.text = string.format(Language.BossPrivilege.GodWarBtnText,
		god_war_data and god_war_data.total_count or 0)
	self.node_list.godwar_btn_opening:SetActive(god_war_data and god_war_data.open_status == 1)
	self.node_list.godwar_btn_open:SetActive(god_war_data and god_war_data.open_status == 0)
	self.node_list.godwar_btn_buy:SetActive(next_cfg ~= nil)
	self.node_list.buy_full:SetActive(next_cfg == nil)

	local is_fetch_daily_rewards = god_war_data.is_fetch_daily_rewards or 0
	self.node_list["right_get_reward_red"]:SetActive(god_war_data.level ~= -1 and is_fetch_daily_rewards == 0)
	self.node_list["right_item_list_get_ed"]:SetActive(god_war_data.level ~= -1 and is_fetch_daily_rewards == 1)
	self.node_list["right_get_reward_button"]:SetActive(god_war_data.level ~= -1 and is_fetch_daily_rewards == 0)

	self.node_list.cur_godwar_panel:SetActive(is_act)
	self.node_list.next_godwar_arrow:SetActive(next_cfg ~= nil)
	self.node_list.next_godwar_panel:SetActive(next_cfg ~= nil)

	self.node_list.boss_godwar_desc.text.text = Language.BossPrivilege.GodWarDescInfoAct
end

-- 关闭特权
function BossNewPrivilegeView:OnClickSetCloseState()
	BossPrivilegeWGCtrl.Instance:SendBossGodwarPrivilegeReq(ZHANSHENPRIVILEGE_OPERA_TYPE.SET_STATUS, 0)
end

--开启特权
function BossNewPrivilegeView:OnClickBossGodwarSetOpenState()
	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	if god_war_data.level == -1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossPrivilege.GodWarError)
		return
	end

	if god_war_data.total_count < 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossPrivilege.GodWarError2)
		return
	end

	BossPrivilegeWGCtrl.Instance:SendBossGodwarPrivilegeReq(ZHANSHENPRIVILEGE_OPERA_TYPE.SET_STATUS, 1)
end

--激活/升级特权
function BossNewPrivilegeView:OnClickBossGodwarLevelUp()
	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	local cur_cfg, next_cfg = BossGodWarWGData.Instance:GetPrivilegeCfgByLevel(god_war_data.level)

	if next_cfg == nil then
		return
	end

	if god_war_data.level == -1 then
		local ok_func = function()
			RechargeWGCtrl.Instance:Recharge(next_cfg.rmb_price, next_cfg.rmb_type, next_cfg.rmb_seq)
		end

		local price = RoleWGData.GetPayMoneyStr(next_cfg.rmb_price, next_cfg.rmb_type, next_cfg.rmb_seq)
		local str = string.format(Language.BossPrivilege.GodWarBuyTips, price)
		local check_string = "boss_god_war_privilege"
		TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string, nil)
	else
		local text_dec = ""
		local price = RoleWGData.GetPayMoneyStr(next_cfg.rmb_price, next_cfg.rmb_type, next_cfg.rmb_seq)
		text_dec = string.format(Language.BossPrivilege.ArrayBuyTips2, price)

		local ok_func = function()
			RechargeWGCtrl.Instance:Recharge(next_cfg.rmb_price, next_cfg.rmb_type, next_cfg.rmb_seq)
		end

		TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
	end
end

function BossNewPrivilegeView:OnClickBossGodwarGetReward()
	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()

	if god_war_data.level == -1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossPrivilege.GodWarError)
		return
	end

	if god_war_data.is_fetch_daily_rewards == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossPrivilege.GodWarRewardEd)
		return
	end

	BossPrivilegeWGCtrl.Instance:SendBossGodwarPrivilegeReq(ZHANSHENPRIVILEGE_OPERA_TYPE.FETCH_DAILY_REWARDS)
end
