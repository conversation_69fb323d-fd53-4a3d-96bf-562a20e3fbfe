local MAX_DOWNLOAD_COUNT = 2 		-- 最大下载数
local MAX_UPLOAD_COUNT = 2			-- 最大上传数
local MAX_REQUEST_COUNT = 2			-- 最大上报数
local MAX_REQUEST_POST_COUNT = 2	-- 最大上报数

HttpClient = HttpClient or {
	download_list = {},
	upload_list = {},
	request_list = {},
	download_queue = {},
	downloading_count = 0,
	upload_queue = {},
	uploading_count = 0,
	request_queue = {},
	requesting_count = 0,
	request_postdata_list = {},
	request_postdata_queue = {},
	request_postdata_count = 0,
	request_postdata_json_list = {},
	request_postdata_json_queue = {},
	request_postdata_json_count = 0,
}

-- 下载
function HttpClient:Download(url, path, callback)
	local t = self.download_list[url]
	if nil == t then
		self.download_list[url] = {callback}
		self.downloading_count = self.downloading_count + 1
		if self.downloading_count > MAX_DOWNLOAD_COUNT then
			table.insert(self.download_queue, {url = url, path = path})
		else
			UtilU3d.Download(url, path, function(is_succ, data) HttpClient:DownloadCallback(url, path, is_succ, data) end)
		end
	else
		table.insert(t, callback)
	end

	return true
end

-- 取消下载
function HttpClient:CancelDownload(url, callback)
	local t = self.download_list[url]
	if nil ~= t then
		for k, v in pairs(t) do
			if v == callback then
				table.remove(t, k)
				break
			end
		end

		if #t == 0 then
			self.download_list[url] = nil
		end
	end
end

-- 下载完成
function HttpClient:DownloadCallback(url, path, is_succ, data)
	self.downloading_count = self.downloading_count - 1
	if self.downloading_count < 0 then
		print_error("[HttpClient] DownloadCallback BigBug! downloading_count less 0", self.downloading_count)
		self.downloading_count = 0
	end

	if nil ~= self.download_list[url] then
		for k, v in pairs(self.download_list[url]) do
			v(url, path, is_succ, data)
		end
		self.download_list[url] = nil
	end

	local loop_count = 0
	while #self.download_queue > 0 do
		loop_count = loop_count + 1
		if loop_count > 1000 then
			break
		end

		local quest = table.remove(self.download_queue, 1)
		local url = quest.url
		local path = quest.path
		if nil ~= self.download_list[url] then
			UtilU3d.Download(url, path, function(is_succ, data)
				HttpClient:DownloadCallback(url, path, is_succ, data)
			end)
			break
		else
			self.downloading_count = self.downloading_count - 1
		end
	end
end

-- 上传
function HttpClient:Upload(url, path, callback)
	local t = self.upload_list[url]
	if nil == t then
		self.uploading_count = self.uploading_count + 1
		if self.uploading_count > MAX_UPLOAD_COUNT then
			table.insert(self.upload_queue, {url = url, path = path})
		else
			if UtilU3d.Upload(url, path, function(is_succ, data) HttpClient:UploadCallback(url, path, is_succ, data) end) then
				self.upload_list[url] = {callback}
				return true
			end
			return false
		end
	else
		table.insert(t, callback)
	end

	return true
end

-- 取消上传
function HttpClient:CancelUpload(url, callback)
	local callback_list = self.upload_list[url]
	if nil ~= callback_list then
		for k, v in pairs(callback_list) do
			if v == callback then
				table.remove(callback_list, k)
				break
			end
		end

		if #callback_list == 0 then
			self.upload_list[url] = nil
		end
	end
end

-- 上传完成
function HttpClient:UploadCallback(url, path, is_succ, data)
	self.uploading_count = self.uploading_count - 1
	if self.uploading_count < 0 then
		print_error("[HttpClient] UploadCallback BigBug! uploading_count less 0", self.uploading_count)
		self.uploading_count = 0
	end

	if nil ~= self.upload_list[url] then
		for k, v in pairs(self.upload_list[url]) do
			v(url, path, is_succ, data)
		end
		self.upload_list[url] = nil
	end

	local loop_count = 0
	while #self.upload_queue > 0 do
		loop_count = loop_count + 1
		if loop_count > 1000 then
			break
		end

		local quest = table.remove(self.upload_queue, 1)
		local url = quest.url
		local path = quest.path
		if nil ~= self.upload_list[url] then
			local callback = function(is_succ, data)
				HttpClient:UploadCallback(url, path, is_succ, data)
			end

			if not UtilU3d.Upload(url, path, callback) then
				self:UploadCallback(url, path, false, nil)
			end

			break
		else
			self.uploading_count = self.uploading_count - 1
		end
	end
end

-- get 请求
function HttpClient:Request(url, callback)
	if nil ~= callback then
		self.request_list[callback] = callback
	end

	self.requesting_count = self.requesting_count + 1

	if self.requesting_count > 20 and not IS_DEBUG_BUILD and not IS_LOCLA_WINDOWS_DEBUG_EXE then
		print_error("[HttpClient] Request requesting_count is greater than 20", self.requesting_count, url)
	end

	if self.requesting_count > MAX_REQUEST_COUNT then
		table.insert(self.request_queue, {url = url, callback = callback})
	else
		UtilU3d.RequestGet(url, function(is_succ, data)
			self:RequestCallback(url, callback, is_succ, data)
		end)
	end

	return true
end

-- 取消请求
function HttpClient:CancelRequest(callback)
	self.request_list[callback] = nil
end

function HttpClient:RequestCallback(url, callback, is_succ, data)
	self.requesting_count = self.requesting_count - 1
	if self.requesting_count < 0 then
		print_error("[HttpClient] RequestCallback BigBug! requesting_count less 0", self.requesting_count)
		self.requesting_count = 0
	end

	if nil ~= self.request_list[callback] then
		self.request_list[callback] = nil
		if nil ~= callback then
			callback(url, is_succ, data)
		end
	end

	local loop_count = 0
	while #self.request_queue > 0 do
		loop_count = loop_count + 1
		if loop_count > 1000 then
			break
		end

		local quest = table.remove(self.request_queue, 1)
		local url = quest.url
		local callback = quest.callback
		if nil ~= self.request_list[callback] then
			UtilU3d.RequestGet(url, function(is_succ, data)
				self:RequestCallback(url, callback, is_succ, data)
			end)

			break
		else
			self.requesting_count = self.requesting_count - 1
		end
	end
end

-- 取消post请求
function HttpClient:CancelPostRequest(callback)
	self.request_postdata_list[callback] = nil
end

-- post请求
function HttpClient:RequestPost(url, post_data, req_callback)
	if nil ~= req_callback then
		self.request_postdata_list[req_callback] = req_callback
	end

	self.request_postdata_count = self.request_postdata_count + 1

	if self.request_postdata_count > 20 and not IS_DEBUG_BUILD and not IS_LOCLA_WINDOWS_DEBUG_EXE then
		print_error("[HttpClient] RequestPost request_postdata_count is greater than 20", self.request_postdata_count)
	end

	if self.request_postdata_count > MAX_REQUEST_POST_COUNT then
		table.insert(self.request_postdata_queue, {url = url, post_data = post_data, call_back = req_callback})
	else
		UtilU3d.RequestPost(url, post_data, function (is_succ, data)
			self:RequestPostDataCallback(is_succ, data, req_callback)
		end)
	end
end

function HttpClient:RequestPostDataCallback(is_succ, data, req_callback)
	self.request_postdata_count = self.request_postdata_count - 1
	if self.request_postdata_count < 0 then
		print_error("[HttpClient] RequestPostDataCallback BigBug! request_postdata_count less 0", self.request_postdata_count)
		self.request_postdata_count = 0
	end

	if nil ~= self.request_postdata_list[req_callback] then
		self.request_postdata_list[req_callback] = nil
		if nil ~= req_callback then
			req_callback(is_succ, data)
		end
	end

	local loop_count = 0
	while #self.request_postdata_queue > 0 do
		loop_count = loop_count + 1
		if loop_count > 1000 then
			break
		end

		local quest = table.remove(self.request_postdata_queue, 1)

		local callback = quest.call_back
		if nil ~= self.request_postdata_list[callback] then
			local url = quest.url
			local post_data = quest.post_data
			UtilU3d.RequestPost(url, post_data, function (is_succ, data)
				self:RequestPostDataCallback(is_succ, data, callback)
			end)

			break
		else
			self.request_postdata_count = self.request_postdata_count - 1
		end
	end
end

function HttpClient:UrlEncode(str)
	if (str) then
		str = string.gsub(str, "\n", "\r\n")
		str = string.gsub(str, "([^%w %-%_%.%~])",
			function (c) return string.format ("%%%02X", string.byte(c)) end)
		str = string.gsub(str, " ", "+")
	end
	return str
end

-- 取消post请求
function HttpClient:CancelPostJsonRequest(callback)
	self.request_postdata_json_list[callback] = nil
end

-- post 请求。post_data 为json数据
function HttpClient:RequestJsonPost(url, post_data, req_callback)
	if nil ~= req_callback then
		self.request_postdata_json_list[req_callback] = req_callback
	end

	self.request_postdata_json_count = self.request_postdata_json_count + 1
	if self.request_postdata_json_count > 20 and not IS_DEBUG_BUILD and not IS_LOCLA_WINDOWS_DEBUG_EXE then
		print_error("[HttpClient] RequestPost request_postdata_json_count is greater than 20", self.request_postdata_json_count)
	end

	-- 支付的请求不能被滞后
	local force_req = false
	if string.find(url, "pay/") then
        force_req = true
    end

	if not force_req and self.request_postdata_json_count > MAX_REQUEST_POST_COUNT then
		table.insert(self.request_postdata_json_queue, {url = url, post_data = post_data, call_back = req_callback})
	else
		UtilU3d.RequestJsonPost(url, post_data, function (is_succ, data)
			self:RequestJsonPostDataCallback(is_succ, data, req_callback)
		end)
	end
end

function HttpClient:RequestJsonPostDataCallback(is_succ, data, req_callback)
	self.request_postdata_json_count = self.request_postdata_json_count - 1
	if self.request_postdata_json_count < 0 then
		print_error("[HttpClient] RequestJsonPostDataCallback BigBug! RequestJsonPost less 0", self.request_postdata_json_count)
		self.request_postdata_json_count = 0
	end

	if nil ~= self.request_postdata_json_list[req_callback] then
		self.request_postdata_json_list[req_callback] = nil
		if nil ~= req_callback then
			req_callback(is_succ, data)
		end
	end

	local loop_count = 0
	while #self.request_postdata_json_queue > 0 do
		loop_count = loop_count + 1
		if loop_count > 1000 then
			break
		end

		local quest = table.remove(self.request_postdata_json_queue, 1)

		local callback = quest.call_back
		if nil ~= self.request_postdata_json_list[callback] then
			local url = quest.url
			local post_data = quest.post_data
			UtilU3d.RequestJsonPost(url, post_data, function (is_succ, data)
				self:RequestJsonPostDataCallback(is_succ, data, callback)
			end)

			break
		else
			self.request_postdata_json_count = self.request_postdata_json_count - 1
		end
	end
end