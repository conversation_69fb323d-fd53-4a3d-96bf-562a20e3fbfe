﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;


public enum WeatherCameraType
{
    /// <summary>
    /// Normal
    /// </summary>
    Normal,

    /// <summary>
    /// Reflection (water, mirror, etc.)
    /// </summary>
    Reflection,

    /// <summary>
    /// Cube map (reflection probe, etc.)
    /// </summary>
    CubeMap,

    /// <summary>
    /// Pre-render or other camera, internal use, should generally be ignored
    /// </summary>
    Other
}

public class WeatherCommandBuffer
{
    public CommandBuffer CommandBuffer;


    public Camera Camera;

    public WeatherCameraType CameraType { get; set; }


    //
    public Material Material;
    public YYTemporalReprojectionState ReprojectionState;
    public CameraEvent RenderQueue;
}

[ExecuteInEditMode]
public partial class YYCommandBufferManager : MonoBehaviour {


    private readonly List<KeyValuePair<System.Action<Camera>, MonoBehaviour>> preCullEvents = new List<KeyValuePair<System.Action<Camera>, MonoBehaviour>>();
    private readonly List<KeyValuePair<System.Action<Camera>, MonoBehaviour>> preRenderEvents = new List<KeyValuePair<System.Action<Camera>, MonoBehaviour>>();


    private readonly List<Camera> cameraStack = new List<Camera>();
    public static Camera BaseCamera { get { return (Instance == null || Instance.cameraStack.Count == 0 ? null : Instance.cameraStack[0]); } }

    internal readonly Matrix4x4[] view = new Matrix4x4[2];
    internal readonly Matrix4x4[] inverseView = new Matrix4x4[2];
    internal readonly Matrix4x4[] inverseProj = new Matrix4x4[2];


    //
    public RenderTexture HalfDepthBuffer { get; private set; }
    //

    [Tooltip("Material to downsample the depth buffer")]
    public Material DownsampleDepthMaterial;

    private float commandBufferCheckTimer;

    private static YYCommandBufferManager instance;
    public static YYCommandBufferManager Instance
    {
        get { return YYWeather.FindOrCreateInstance<YYCommandBufferManager>(ref instance, false); }
        //get { return instance; }
    }

    private void Awake()
    {
       

        YYWeather.RemoveNull<YYCommandBufferManager>();
    }


    

    private readonly List<WeatherCommandBuffer> commandBuffers = new List<WeatherCommandBuffer>();

    //
    //Camera mainCamera;
    public static bool cameraEnable = false;
    //

   
    public void AddCommandBuffer(WeatherCommandBuffer cmdBuffer)
    {

#if !UNITY_SERVER

        if (!commandBuffers.Contains(cmdBuffer))
        {
            commandBuffers.Add(cmdBuffer);
        }

#endif

    }




    public void RegisterPreCull(System.Action<Camera> action, MonoBehaviour script)
    {
        if (script != null && !ListHasScript(preCullEvents, script))
        {
            preCullEvents.Add(new KeyValuePair<System.Action<Camera>, MonoBehaviour>(action, script));
        }
    }

    public void RegisterPreRender(System.Action<Camera> action, MonoBehaviour script, bool highPriority = false)
    {

#if !UNITY_SERVER

        if (script != null && !ListHasScript(preRenderEvents, script))
        {
            if (highPriority)
            {
                preRenderEvents.Add(new KeyValuePair<System.Action<Camera>, MonoBehaviour>(action, script));
            }
            else
            {
                preRenderEvents.Insert(0, new KeyValuePair<System.Action<Camera>, MonoBehaviour>(action, script));
            }
        }

#endif

    }

    public void CameraPreRender(Camera camera)
    {
        if (camera == null)
        {
            return;
        }
        else
        {
            if (camera.depthTextureMode == DepthTextureMode.None)
            {
                camera.depthTextureMode = DepthTextureMode.Depth;
            }

            SetupCommandBufferForCamera(camera);
            AttachDepthCommandBuffer(camera);

            InvokeEvents(camera, preRenderEvents);
        }


    }

    private YYWeatherCamera yYWeatherCamera = null;
    private void Update()
    {
        //
        if (yYWeatherCamera == null && Camera.main != null)
        {
            yYWeatherCamera = Camera.main.GetComponent<YYWeatherCamera>();
            if ( yYWeatherCamera == null)
            {
                yYWeatherCamera =  Camera.main.gameObject.AddComponent<YYWeatherCamera>();
            }
        }

        //  解除yypost 的干扰  
        if(Camera.main != null && Camera.main.depthTextureMode != DepthTextureMode.Depth)
        {
            Camera.main.depthTextureMode = DepthTextureMode.Depth;
        }
        //

        //
        UpdateDepthDownsampler();
    }

   

   

    private void SetupCommandBufferForCamera(Camera camera)
    {
        if (camera == null)
        {
            return;
        }

        Camera baseCamera = YYCommandBufferManager.BaseCamera;
        CalculateMatrixes(camera, baseCamera);

        Shader.SetGlobalMatrixArray(WMS._WeatherMakerInverseView, inverseView);
        Shader.SetGlobalMatrixArray(WMS._WeatherMakerInverseProj, inverseProj);

    }

    private void CalculateMatrixes(Camera camera, Camera baseCamera)
    {
        
        if (camera == null || baseCamera == null)
        {
            return;
        }

        view[0] = view[1] = camera.worldToCameraMatrix;

        inverseView[0] = inverseView[1] = view[0].inverse;

        // only use base camera projection
        inverseProj[0] = inverseProj[1] = baseCamera.projectionMatrix.inverse;
    }

    public void CameraPreCull(Camera camera)
    {
        if (camera == null || cameraStack.Contains(camera))// || YYWeather.ShouldIgnoreCamera(this, camera, false))
        {
            return;
        }

        //Debug.Log("CameraPreCull");

        RemoveEmptyCommandBuffers(camera);
        cameraStack.Add(camera);
        InvokeEvents(camera, preCullEvents);
    }

    private void OnEnable()
    {
        //mainCamer

        //Camera.onPreCull += CameraPreCull;
        //Camera.onPreRender += CameraPreRender;
        //Camera.onPostRender += CameraPostRender;
        if (Camera.main != null)
        {
            Camera.main.depthTextureMode = DepthTextureMode.Depth;
        }
        cameraEnable = true;
        
    }
    private void OnDisable()
    {
        //Camera.onPreCull -= CameraPreCull;
        //Camera.onPreRender -= CameraPreRender;
        //Camera.onPostRender -= CameraPostRender;

        if (Camera.main != null)
        {
            Camera.main.depthTextureMode = DepthTextureMode.None;
        }
        cameraEnable = false;
        
    }

    public void RemoveCommandBuffer(WeatherCommandBuffer cmdBuffer)
    {

#if !UNITY_SERVER

        commandBuffers.Remove(cmdBuffer);

#endif

    }


    private void RemoveEmptyCommandBuffers(Camera camera)
    {
        if ((commandBufferCheckTimer += Time.deltaTime) >= 1.0f)
        {
            commandBufferCheckTimer = 0.0f;
            foreach (CameraEvent evt in System.Enum.GetValues(typeof(CameraEvent)))
            {
                CommandBuffer[] cmdBuffers = camera.GetCommandBuffers(evt);
                foreach (CommandBuffer cmdBuffer in cmdBuffers)
                {
                    if (cmdBuffer.sizeInBytes == 0)
                    {
                        camera.RemoveCommandBuffer(evt, cmdBuffer);
                    }
                }
            }
        }
    }

    public void CameraPostRender(Camera camera)
    {
        if (camera != null && cameraStack.Contains(camera))
        {
            cameraStack.Remove(camera);
            //InvokeEvents(camera, postRenderEvents);
        }
    }

    //private void RemoveEmptyCommandBuffers(Camera camera)
    //{
    //    if ((commandBufferCheckTimer += Time.deltaTime) >= 1.0f)
    //    {
    //        commandBufferCheckTimer = 0.0f;
    //        foreach (CameraEvent evt in System.Enum.GetValues(typeof(CameraEvent)))
    //        {
    //            CommandBuffer[] cmdBuffers = camera.GetCommandBuffers(evt);
    //            foreach (CommandBuffer cmdBuffer in cmdBuffers)
    //            {
    //                if (cmdBuffer.sizeInBytes == 0)
    //                {
    //                    camera.RemoveCommandBuffer(evt, cmdBuffer);
    //                }
    //            }
    //        }
    //    }
    //}

    public void UnregisterPreCull(MonoBehaviour script)
    {

#if !UNITY_SERVER

        if (script != null)
        {
            for (int i = preCullEvents.Count - 1; i >= 0; i--)
            {
                if (preCullEvents[i].Value == script)
                {
                    preCullEvents.RemoveAt(i);
                }
            }
        }

#endif

    }


    private void InvokeEvents(Camera camera, List<KeyValuePair<System.Action<Camera>, MonoBehaviour>> list)
    {
        for (int i = list.Count - 1; i >= 0; i--)
        {
            if (list[i].Value == null)
            {
                list.RemoveAt(i);
            }
            else if (list[i].Value.enabled)
            {
                list[i].Key(camera);
            }
        }
    }

    private bool ListHasScript(List<KeyValuePair<System.Action<Camera>, MonoBehaviour>> list, MonoBehaviour script)
    {
        foreach (KeyValuePair<System.Action<Camera>, MonoBehaviour> item in list)
        {
            if (item.Value == script)
            {
                return true;
            }
        }
        return false;
    }


}
