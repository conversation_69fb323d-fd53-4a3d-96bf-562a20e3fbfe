local COLOR_LEV = {
	[1] = "#1EAA2BFF",
	[2] = "#3899C3FF",
	[3] = "#B669FFFF",
	[4] = "#E59E28FF",
	[5] = "#FF5859FF",
	[6] = "#FF9797FF",
	[7] = "#7E7D1AFF",
	[8] = "#DF4FD0FF"
}

local ATTR_PANEL_POSITION = {
	[0] = {cur = Vector3(-436, 11, 0), next = Vector3(331, 79, 0)},
	[1] = {cur = Vector3(331, -134, 0), next = Vector3(-326, 86, 0)},
}

local DESC_YAZHI_POSITION = {
	[0] = Vector3(-428, -85, 0),
	[1] = Vector3(339, -230, 0),
}

function RoleView:JingJieInit()
	self.jj_item_cell = nil
	self.cur_lev_attr_list = nil
	self.next_lev_attr_list = nil
end

function RoleView:JingJieLoadCallBack()
	if not self.jj_item_cell then
		self.jj_item_cell = ItemCell.New(self.node_list["item_cell"])
		self.jj_item_cell:SetShowCualityBg(false)
	end

	if not self.cur_lev_attr_list then
		self.cur_lev_attr_list = AsyncListView.New(JingJieNewItem, self.node_list["cur_attr_list"])
	end

	if not self.next_lev_attr_list then
		self.next_lev_attr_list = AsyncListView.New(JingJieNewItem, self.node_list["next_attr_list"])
	end

	self.node_list["btn_up"].button:AddClickListener(BindTool.Bind(self.OnClickUp, self))

	-- 境界名称
	-- local max_level_cfg = JingJieWGData.Instance:GetJingJieMaxLevelCfg()

	-- if max_level_cfg then
	-- 	self.node_list["jingjie_name"].text.text = max_level_cfg.name

	-- 	local bundle, asset = ResPath.GetJingJieImg("a3_jingjie_" .. max_level_cfg.jingjie_level)
	-- 	self.node_list.jingjie_icon.image:LoadSprite(bundle, asset, function()
	-- 		self.node_list.jingjie_icon.image:SetNativeSize()
	-- 	end)	
	-- end
end

function RoleView:JingJieShowIndexCallBack()
	self.node_list.title_view_name.text.text = Language.JingJie.Text_view_name
	self:InitUpGradeAnim()
end

function RoleView:JingJieReleaseCallBack()

	if self.jj_item_cell then
		self.jj_item_cell:DeleteMe()
		self.jj_item_cell = nil
	end

	if self.cur_lev_attr_list then
		self.cur_lev_attr_list:DeleteMe()
		self.cur_lev_attr_list = nil
	end

	if self.next_lev_attr_list then
		self.next_lev_attr_list:DeleteMe()
		self.next_lev_attr_list = nil
	end
end

function RoleView:JingJieOnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.upgrade_succeed then
				self:PlayUpGradeAnim()
			end
		end
	end

	self:FlushJingJieCurLevPanel()
end

function RoleView:FlushJingJieCurLevPanel()
	local select_level = JingJieWGData.Instance:GetJingJieLevel()
	local max_level = JingJieWGData.Instance:GetJingJieMaxLevel()
	local is_first_lev = select_level == 0
	local is_max_lev = select_level >= max_level
	local cur_level_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(select_level)
	local nex_lev = is_max_lev and max_level or select_level + 1
	local nex_level_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(nex_lev)
	local cur_cap = RoleWGData.Instance:GetMainRoleCap()
	local need_cap = (nex_level_cfg or {}).cap_limit or 0
	local flag_cap_enough = (not is_max_lev) and (need_cap > 0) and (cur_cap >= need_cap)

	self.node_list.flag_cap_enough:CustomSetActive(flag_cap_enough)
	self.node_list.need_cap_Info:CustomSetActive(not is_max_lev)
	self.node_list.need_cap_max_flag:CustomSetActive(is_max_lev)
	self.node_list.need_stuff_info:CustomSetActive(not is_max_lev)
	self.node_list.need_stuff_max_flag:CustomSetActive(is_max_lev)

	local stuff_num = (nex_level_cfg or {}).stuff_num or 0
	local stuff_id = (nex_level_cfg or {}).stuff_id or 0

	if not is_max_lev then
		if need_cap > 0 then
			local color = cur_cap < need_cap and COLOR3B.D_RED or COLOR3B.GREEN
			local str = CommonDataManager.ConverExpByThousand(cur_cap) .. "/" .. CommonDataManager.ConverExpByThousand(need_cap)
			self.node_list["need_cap"].text.text = ToColorStr(str, color)
		else
			self.node_list["need_cap"].text.text = "-/-"
		end

		if stuff_id > 0 or stuff_num > 0 then
			self.jj_item_cell:SetData({item_id = stuff_id})
			self.jj_item_cell:SetCellBgEnabled(false)
			self.jj_item_cell:SetQualityIconVisible(false)
			self.jj_item_cell:SetEffectRootEnable(false)
		end
	end

	local capability = JingJieWGData.Instance:GetCapabilityByID(cur_level_cfg.jingjie_level)
	self.node_list.cap_value.text.text = capability

	--境界压制描述
	-- if cur_level_cfg.jingjie_jc_per > 0 then
	-- 	self.node_list["yazhi_desc"].text.text = string.format(Language.JingJie.YazhiDesc, cur_level_cfg.name, cur_level_cfg.jingjie_jc_per / 100)
	-- 	self.node_list["yazhi_desc"]:SetActive(true)
	-- else
	-- 	self.node_list["yazhi_desc"]:SetActive(false)
	-- end
	
	-- 红点
	self.node_list["remind"]:SetActive(JingJieWGData.Instance:GetJingJieRemind() > 0)

	-- 无状态
	if is_first_lev then
		--当前属性  无  属性展示一行字
		self.node_list.cur_attr_name.text.text = Language.JingJie.NoJinJieLevel
		self.node_list["next_attr_name"].text.text = string.format(Language.JingJie.JingJieName, nex_level_cfg.name)

		local has_amount = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
		local color = has_amount < stuff_num and COLOR3B.D_RED or COLOR3B.GREEN
		local str = has_amount .. "/" .. stuff_num
		self.node_list["item_cell_amount"].text.text = ToColorStr(str, color)
		if self.next_lev_attr_list then
			local attr_t, attr_t_cap = JingJieWGData.Instance:GetJingJieAttrListCfgBylevel(nex_lev, true)
			self.next_lev_attr_list:SetDataList(attr_t)
			self.node_list["next_attr_cap_value"].text.text = ToColorStr(AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(attr_t_cap)), COLOR3B.GREEN)
		end

		self.node_list.cur_attr_panel_info:CustomSetActive(false)
		self.node_list.cur_attr_panel_tip:CustomSetActive(true)
		self.node_list.next_attr_panel_tip:CustomSetActive(false)
		self.node_list.next_attr_panel_info:CustomSetActive(true)
		self.node_list.cur_attr_panel_tip.text.text = Language.JingJie.NoJingJieDesc
		self.node_list["btn_up"]:CustomSetActive(flag_cap_enough and (has_amount >= stuff_num))
		self.node_list.flag_stuff_enough:CustomSetActive(has_amount >= stuff_num)
	elseif is_max_lev then
		--满状态
		-- XUI.SetButtonEnabled(self.node_list["btn_up"], false)

		self.node_list["btn_up"]:CustomSetActive(false)
		self.node_list.flag_stuff_enough:CustomSetActive(false)

		--当前属性展示 下一阶级 已满及  属性展示一行文本
		self.node_list.cur_attr_name.text.text = string.format(Language.JingJie.JingJieName, nex_level_cfg.name)
		self.node_list["next_attr_name"].text.text = Language.JingJie.MaxJinJie
		self.node_list["cur_attr_name"].text.text = string.format(Language.JingJie.JingJieName, cur_level_cfg.name)

		if self.cur_lev_attr_list then
			local attr_t, attr_t_cap = JingJieWGData.Instance:GetJingJieAttrListCfgBylevel(nex_lev, false)
			self.cur_lev_attr_list:SetDataList(attr_t)
			self.node_list["cur_attr_cap_value"].text.text = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(attr_t_cap))
		end

		self.node_list.cur_attr_panel_info:CustomSetActive(true)
		self.node_list.cur_attr_panel_tip:CustomSetActive(false)
		self.node_list.next_attr_panel_tip:CustomSetActive(true)
		self.node_list.next_attr_panel_info:CustomSetActive(false)
		self.node_list.next_attr_panel_tip.text.text = Language.JingJie.MaxJingjieTip
	else
		--有状态
		XUI.SetButtonEnabled(self.node_list["btn_up"], true)

		local has_amount = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
		local color = has_amount < stuff_num and COLOR3B.D_RED or COLOR3B.GREEN
		local str = has_amount .. "/" .. stuff_num
		self.node_list["item_cell_amount"].text.text = ToColorStr(str, color)

		self.node_list["cur_attr_name"].text.text = string.format(Language.JingJie.JingJieName, cur_level_cfg.name)

		self.node_list["next_attr_name"].text.text = string.format(Language.JingJie.JingJieName, nex_level_cfg.name)

		-- 都展示属性
		if self.cur_lev_attr_list then
			local attr_t, attr_t_cap = JingJieWGData.Instance:GetJingJieAttrListCfgBylevel(select_level, false)
			self.cur_lev_attr_list:SetDataList(attr_t)
			self.node_list["cur_attr_cap_value"].text.text = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(attr_t_cap))
		end
		if self.next_lev_attr_list then
			local attr_t, attr_t_cap = JingJieWGData.Instance:GetJingJieAttrListCfgBylevel(nex_lev, true)
			self.next_lev_attr_list:SetDataList(attr_t)
			self.node_list["next_attr_cap_value"].text.text = ToColorStr(AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(attr_t_cap)), COLOR3B.GREEN)
		end

		self.node_list.cur_attr_panel_info:CustomSetActive(true)
		self.node_list.cur_attr_panel_tip:CustomSetActive(false)
		self.node_list.next_attr_panel_tip:CustomSetActive(false)
		self.node_list.next_attr_panel_info:CustomSetActive(true)
		self.node_list["btn_up"]:CustomSetActive(flag_cap_enough and (has_amount >= stuff_num))
		self.node_list.flag_stuff_enough:CustomSetActive(has_amount >= stuff_num)
	end
end

-- 突破成功特效
-- function RoleView:PlayEffect()
-- 	-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_TPCG_F2)
-- 	-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_container"].transform)
-- end

-- 点击升级
function RoleView:OnClickUp()
	local cur_level = JingJieWGData.Instance:GetJingJieLevel()
	local max_levle = JingJieWGData.Instance:GetJingJieMaxLevel()
	local is_max_lev = cur_level >= max_levle

	local nex_lev = is_max_lev and max_levle or cur_level + 1
	local nex_level_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(nex_lev)

	if IsEmptyTable(nex_level_cfg) then
		return
	end

	local item_id = nex_level_cfg.stuff_id
	local need_num = nex_level_cfg.stuff_num
	local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)

	if not is_max_lev and has_num < need_num then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
	else
		RoleWGCtrl.Instance:SendJingJieUp(CS_ROLE_JINGJIE_OPERA.PROMOTE_LEVEL, 0)
	end
end

-- function RoleView:FlushJingJieMidModel(cur_level_cfg)
-- 	if IsEmptyTable(cur_level_cfg) then
-- 		self.node_list.display_root:CustomSetActive(false)
-- 		return
-- 	end

-- 	self.node_list.display_root:CustomSetActive(true)
-- 	local display_data = {}
-- 	if cur_level_cfg.model_show_itemid1 ~= 0 and cur_level_cfg.model_show_itemid1 ~= "" then
-- 		local split_list = string.split(cur_level_cfg.model_show_itemid1, "|")

-- 		if #split_list > 1 then
-- 			local list = {}
-- 			for k, v in pairs(split_list) do
-- 				list[tonumber(v)] = true
-- 			end

-- 			display_data.model_item_id_list = list
-- 		else
-- 			display_data.item_id = cur_level_cfg.model_show_itemid1
-- 		end
-- 	end

-- 	display_data.should_ani = true
-- 	display_data.bundle_name = cur_level_cfg.model_bundle_name1
-- 	display_data.asset_name = cur_level_cfg.model_asset_name1
-- 	local model_show_type = tonumber(cur_level_cfg.model_show_type1) or 1
-- 	display_data.render_type = model_show_type - 1

-- 	-- display_data.model_click_func = function ()
-- 	-- 	TipWGCtrl.Instance:OpenItem({item_id = data.model_show_itemid})
-- 	-- end
-- 	-- self.node_list.display_click:SetActive(model_show_type ~= 1)

-- 	self.model_display:SetData(display_data)
	
-- 	local scale = cur_level_cfg.display_scale1
-- 	Transform.SetLocalScaleXYZ(self.node_list.display_root.transform, scale, scale, scale)

-- 	if cur_level_cfg.display_pos1 and cur_level_cfg.display_pos1 ~= "" then
-- 		local pos_x, pos_y = 0, 0
-- 		local pos_list = string.split(cur_level_cfg.display_pos1, "|")
-- 		pos_x = tonumber(pos_list[1]) or pos_x
-- 		pos_y = tonumber(pos_list[2]) or pos_y
-- 		RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, pos_x, pos_y)
-- 	end
-- end

function RoleView:InitUpGradeAnim()
	local jingjie_level = JingJieWGData.Instance:GetJingJieLevel()
	local show_left = jingjie_level == 0 or jingjie_level % 2 == 0

	-- 位置复位
	self.node_list.jingjie_left_root.transform.localPosition = Vector3(0, -500, 0)
	self.node_list.jingjie_right_root.transform.localPosition = Vector3(0, -500, 0)

	local attr_pos_cfg = show_left and ATTR_PANEL_POSITION[1] or ATTR_PANEL_POSITION[0]
	self.node_list.cur_attr_panel.transform.localPosition = attr_pos_cfg.cur
	self.node_list.next_attr_panel.transform.localPosition = attr_pos_cfg.next
	-- self.node_list.cur_attr_line.transform.localPosition = show_left and Vector3(-18, 69, 0) or Vector3(18, 69, 0)
	-- self.node_list.next_attr_line.transform.localPosition = show_left and Vector3(18, 69, 0) or Vector3(-18, 69, 0)
	self.node_list.cur_attr_line.transform.localScale = show_left and Vector3(1, 1, 1) or Vector3(-1, 1, 1)
	self.node_list.next_attr_line.transform.localScale = show_left and Vector3(-1, 1, 1) or Vector3(1, 1, 1)
	self.node_list.cur_attr_panel.transform:DOScale(Vector3.one, 0.3)
	self.node_list.next_attr_panel.transform:DOScale(Vector3.one, 0.3)

	local desc_yazhi_pos = show_left and DESC_YAZHI_POSITION[1] or DESC_YAZHI_POSITION[0]
	self.node_list.yazhi_desc.transform.localPosition = desc_yazhi_pos

	-- 确认显示左右节点
	self.node_list.jingjie_left_root:CustomSetActive(show_left)
	self.node_list.jingjie_right_root:CustomSetActive(not show_left)

	local cur_level_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(jingjie_level)
	-- 境界压制描述
	if cur_level_cfg.jingjie_jc_per > 0 then
		self.node_list["yazhi_desc"].text.text = string.format(Language.JingJie.YazhiDesc, cur_level_cfg.name, cur_level_cfg.jingjie_jc_per / 100)
		self.node_list["yazhi_desc"]:CustomSetActive(true)
	else
		self.node_list["yazhi_desc"]:CustomSetActive(false)
	end
	self.node_list.yazhi_desc:CustomSetActive(true)

	--设置节点名称
	for i = 0, 1 do
		local jingjie_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(jingjie_level + i)
		local name_str = (jingjie_cfg or {}).name or ""

		self.node_list["left_item_name" .. (i + 2)].text.text = name_str
		self.node_list["right_item_name" .. (i + 2)].text.text = name_str
	end
end

function RoleView:PlayUpGradeAnim()
	-- 隐藏属性展示
	self.node_list.cur_attr_panel.transform:DOScale(Vector3.zero, 0.3)
	self.node_list.next_attr_panel.transform:DOScale(Vector3.zero, 0.3)
	self.node_list.yazhi_desc:CustomSetActive(false)

	local jingjie_level = JingJieWGData.Instance:GetJingJieLevel()
	local is_right = (jingjie_level == 0 or jingjie_level % 2 == 0)
	local node_head = is_right and "right_" or "left_"

	local function line_move()
		local node = is_right and self.node_list.jingjie_right_root or self.node_list.jingjie_left_root
		self.node_list[node_head .. "xian_jihuo_effect"]:CustomSetActive(true)

		ReDelayCall(self, function ()
			self.node_list[node_head .. "jihuo_effect"]:CustomSetActive(true)
		end, 0.4, "jingjie_jihuo_effect")

		node.transform:DOLocalMove(Vector3(0, -1028, -2992), 0.7):SetEase(DG.Tweening.Ease.InCubic):OnComplete(function ()
			ReDelayCall(self, function ()	
				self:InitUpGradeAnim()
				RoleWGCtrl.Instance:OpenJingjieUpTipView()

				self.node_list[node_head .. "xuli_effect"]:CustomSetActive(false)
				self.node_list[node_head .. "xian_jihuo_effect"]:CustomSetActive(false)
				self.node_list[node_head .. "jihuo_effect"]:CustomSetActive(false)

			end, 1, "jingjie_up_grade_open_tip")
		end)
	end

	self.node_list[node_head .. "xuli_effect"]:CustomSetActive(true)
	ReDelayCall(self, function ()	
		line_move()
	end, 0.7, "jingjie_xuli_effect")
end

---------------------境界加成属性item------------------
JingJieNewItem = JingJieNewItem or BaseClass(BaseRender)

function JingJieNewItem:OnFlush()
	if not self.data then
		return
	end

	-- local color = self.data.is_show_up and COLOR3B.D_GREEN or COLOR3B.WHITE
	self.node_list["attr_name"].text.text = AttributeMgr.DisposeAttrName(Language.Common.AttrName[AttributeMgr.GetAttributteKey(self.data.attr_name)], false, true)
	self.node_list["attr_value"].text.text =  AttributeMgr.PerAttrValue(self.data.attr_name, self.data.attr_value) -- ToColorStr(self.data.attr_value, color)
	self.node_list["sign"]:SetActive(self.data.is_show_up)
end