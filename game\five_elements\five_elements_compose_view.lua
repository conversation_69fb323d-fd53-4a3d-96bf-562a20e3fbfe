FiveElementsComposeView = FiveElementsComposeView or BaseClass(SafeBaseView)

function FiveElementsComposeView:__init()
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vertor2 = Vector2(0, -4), sizeDelta = Vector2(1100, 590)})
	self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_compose")
end
 
function FiveElementsComposeView:ReleaseCallBack()
    if self.cell_list ~= nil then
        for k, v in pairs(self.cell_list) do
            for i, j in pairs(v) do
				j:DeleteMe()
			end
        end
        self.cell_list = nil
    end

	if self.compose_item_list ~= nil then
        for k, v in pairs(self.compose_item_list) do
            v:DeleteMe()
        end
        self.compose_item_list = nil
    end

	if self.target_item then
		self.target_item:DeleteMe()
		self.target_item = nil
	end

	self.load_bar_cell_complete = nil
	self.big_type = nil
	self.small_type = nil
	self.can_compose = false
end

function FiveElementsComposeView:LoadCallBack()
	if not self.compose_item_list then
        self.compose_item_list = {}
		
        for i = 1, 4 do
            local cell = ItemCell.New(self.node_list["item_pos" .. i])
            if i > 1 then
                cell:ClearData()
				cell:SetCellBgEnabled(false)
                --cell:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
                cell:SetItemIcon(ResPath.GetCommonImages("a2_ty_suo"))
            end
            self.compose_item_list[i] = cell
        end
    end

	if not self.target_item then
		self.target_item = ItemCell.New(self.node_list["target_item_pos"])
	end

	self.big_type = 1
	self.small_type = 1

	self.cell_list = {}
	self.load_bar_cell_complete = false
	self.node_list.title_view_name.text.text = Language.FiveElements.Compose_Title

	self:LoadNavBar()
	XUI.AddClickEventListener(self.node_list["btn_compose"], BindTool.Bind(BindTool.Bind(self.OnClickCompose, self)))
end

function FiveElementsComposeView:LoadNavBar()
	local data_list = FiveElementsWGData.Instance:GetComposeBigItemListInfo()

	for i = 1, #data_list do
		if not IsEmptyTable(data_list[i]) then
			if self.node_list['text_' .. i] then
				self.node_list['text_' .. i].text.text = data_list[i][1].name
			end
			
			if self.node_list['text_hl_' .. i] then
				self.node_list['text_hl_' .. i].text.text = data_list[i][1].name
			end

			self.node_list["SelectBtnTj" .. i]:SetActive(true)
			self.node_list["SelectBtnTj" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickComposeBarHandler, self, i))
			self:LoadNavBarItemCell(i, i == #data_list)
		else
			self.node_list["SelectBtnTj" .. i]:SetActive(false)
		end
	end
end

function FiveElementsComposeView:LoadNavBarItemCell(index, load_end)
	local res_async_loader = AllocResAsyncLoader(self, "fiveelements_compose_list_cell" .. index)
	local data_list = FiveElementsWGData.Instance:GetComposeToggleListInfo(index)
	local item_count = #data_list

	res_async_loader:Load("uis/view/five_elements_ui_prefab", "fiveelements_compose_list_cell", nil,
		function(new_obj)
			local item_vo = {}

			for i = 1, item_count do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["ListTj"..index].transform, false)
				obj:GetComponent("Toggle").group = self.node_list["ListTj" .. index].toggle_group
				local item_render = ComposeSmallTypeRender.New(obj)
				item_render.parent_view = self
				item_render:SetIndex(i)
				item_render:SetValueChangedCallBack(BindTool.Bind(self.OnClickAccordionChild, self))
				item_vo[i] = item_render

				if i == item_count and load_end then
					self.load_bar_cell_complete = true
				end
				obj:SetActive(true)
			end
			self.cell_list[index] = item_vo

			if self.load_bar_cell_complete then
				-- self:FlushToggleAllData()
				-- self:SelectToggle()
				self:Flush()
			end
	end)
end

-- function FiveElementsComposeView:FlushToggleAllData()
--     if IsEmptyTable(self.cell_list) then
--         return
--     end

-- 	local big_type_data_list = FiveElementsWGData.Instance:GetComposeBigItemListInfo()
-- 	local remind_list = FiveElementsWGData.Instance:GetComposeRemind()
-- 	for i = 1, #big_type_data_list do
-- 		for k, v in pairs(self.cell_list[i]) do
-- 			v:FlushRemind()
-- 		end
-- 		self.node_list["root_remind" .. i]:SetActive(remind_list[i].remind) 
-- 	end
-- end

function FiveElementsComposeView:OnClickComposeBarHandler(index, is_on)
	if nil == index or not is_on or self.big_type == index then
		return
	end

	self.big_type = index
	local remind_list = FiveElementsWGData.Instance:GetComposeRemind()
	local data = remind_list[index]
	local small_type = 1

	for k, v in pairs(data) do
		if type(v) == "table" and v.remind == true then
			small_type = k
			break
		end
	end

	self.small_type = small_type
	self.cell_list[self.big_type][self.small_type]:OnSelectChange(true)

	self:FlushMidComposePanel()
end

function FiveElementsComposeView:OnClickAccordionChild(item)
	if nil == item or nil == item.data then
		return 
	end

	local data = item.data
	local index = item.index
	self.small_type = index
	self:FlushMidComposePanel()
end

function FiveElementsComposeView:OnClickCompose()
	local data = FiveElementsWGData.Instance:GetComposeData(self.big_type, self.small_type)

	if self.can_compose then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng,
	    is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["compose_effect_pos"]})
		local part = -1
		local hold = -1

		--策划要求先拿镶嵌上去的
		local wear_data = FiveElementsWGData.Instance:GetComposeWearCacheByItemId(data.stuff_id)
		local bag_compose_data = (wear_data or {})[1] or {}

		if not IsEmptyTable(bag_compose_data) then
			part = bag_compose_data.part_id
			hold = bag_compose_data.hole
		end

		FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_COMPOS_STONE, data.item_id, part, hold)
	else
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
		TipWGCtrl.Instance:OpenItem({item_id = data.stuff_id})
	end
end


function FiveElementsComposeView:OnFlush()
	if not self.load_bar_cell_complete then
		return
	end

	local big_type_data_list = FiveElementsWGData.Instance:GetComposeBigItemListInfo()
	local remind_list = FiveElementsWGData.Instance:GetComposeRemind()

	for i = 1, #big_type_data_list do
		local data_list = FiveElementsWGData.Instance:GetComposeToggleListInfo(i)
		for k, v in pairs(self.cell_list[i]) do
			v:SetData(data_list[k])
		end

		self.node_list["root_remind" .. i]:SetActive(remind_list[i].remind) 
	end

	self:SelectToggle()
	self:FlushMidComposePanel()
end

function FiveElementsComposeView:SelectToggle()
	local defaule_compose_select_bigtype, defaule_compose_select_smalltype = FiveElementsWGData.Instance:GetComposeDefaultSelect(self.big_type, self.small_type)
	self.node_list["SelectBtnTj" .. defaule_compose_select_bigtype].accordion_element.isOn = true
	self.cell_list[defaule_compose_select_bigtype][defaule_compose_select_smalltype]:OnSelectChange(true)
	self.big_type = defaule_compose_select_bigtype
	self.small_type = defaule_compose_select_smalltype
end

function FiveElementsComposeView:FlushMidComposePanel()
	local data = FiveElementsWGData.Instance:GetComposeData(self.big_type, self.small_type)
	self.target_item:SetData({item_id = data.item_id})

	if data.stuff_num ~= nil then
		local item_num = FiveElementsWGData.Instance:GetCanComposeNum(data.stuff_id)
		self.compose_item_list[1]:SetFlushCallBack(function ()
			local enough = item_num >= data.stuff_num
			local right_text = ToColorStr(item_num .. "/" .. data.stuff_num, enough and COLOR3B.GREEN or COLOR3B.PINK)
			self.compose_item_list[1]:SetRightBottomColorText(right_text)
			self.node_list.item_text.text.text = right_text
			self.compose_item_list[1]:SetRightBottomTextVisible(false)
		end)
	
		self.can_compose = item_num >= data.stuff_num
		self.node_list.btn_compose_remind:SetActive(self.can_compose)
		self.compose_item_list[1]:SetData({item_id = data.stuff_id, num = data.stuff_num, is_bind = 0})
		self.compose_item_list[1]:SetCellBgEnabled(false)
		self.compose_item_list[1]:SetQualityIconVisible(false)
		self.compose_item_list[1]:SetEffectRootEnable(false)
	end

	-- 刷新属性
	local attr_data = FiveElementsWGData.Instance:GetStoreInfoByItemId(data.item_id)
	local attr_list = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_data, "attr_id", "attr_value")
	
	for i = 1, 5 do
		if not IsEmptyTable(attr_list[i]) then
			self.node_list["attr_item_cell" .. i]:SetActive(true)
			self.node_list["attr_item_name" .. i].text.text = attr_list[i].attr_name
			self.node_list["attr_item_value" .. i].text.text = attr_list[i].value_str
		else
			self.node_list["attr_item_cell" .. i]:SetActive(false)
		end
	end
end

-------------------------------------------------------------------------------------------------
ComposeSmallTypeRender = ComposeSmallTypeRender or BaseClass(BaseRender)
function ComposeSmallTypeRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function ComposeSmallTypeRender:__delete()
end

function ComposeSmallTypeRender:OnFlush()
	local data = self:GetData()
	if nil == data then
		return
	end

	self.node_list.normal_text.text.text = data.name
	self.node_list.select_text.text.text = data.name
	self:FlushRemind()
end

function ComposeSmallTypeRender:SetValueChangedCallBack(call_back)
	self.value_change_call_back = call_back
end

function ComposeSmallTypeRender:OnClickItem(is_on)
	if is_on then
		self.value_change_call_back(self)
	end	
end

function ComposeSmallTypeRender:FlushRemind()
	if not self.data or not self.index then
		return
	end

	local remind_data = FiveElementsWGData.Instance:GetComposeRemind()
	local flag = remind_data[self.data.big_type][self.data.small_type].remind
	self.node_list.remind:SetActive(flag)
end

function ComposeSmallTypeRender:OnSelectChange(is_select)
		self.node_list.select:SetActive(is_select)
		self.node_list.normal:SetActive(not is_select)
		self.view.toggle.isOn = is_select
end