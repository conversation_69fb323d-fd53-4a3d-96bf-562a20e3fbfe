function FuBenPanelView:InitRuneTowerView()
	if not self.rune_tower_reward_list then
		self.rune_tower_reward_list = AsyncListView.New(FuBenRuneTowerRewardCell, self.node_list["rune_tower_reward_list"])
		self.rune_tower_reward_list:SetStartZeroIndex(true)
	end

    self.node_list["layout_rune_tower_combine_mark"].button:AddClickListener(BindTool.Bind1(self.OnClickRuneTowerCombine, self))
	self.node_list["btn_rune_tower_enter"].button:AddClickListener(BindTool.Bind1(self.OnClickRuneTowerBaoMingEnter,self))
end

function FuBenPanelView:DeleteRuneTowerView()
	if self.rune_tower_reward_list then
		self.rune_tower_reward_list:DeleteMe()
		self.rune_tower_reward_list = nil
	end
end

function FuBenPanelView:FlushRuneTowerView()
    local cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenTypeModelCfg(GoalTeamType.FbRuneTowerType, 0)
	if not cfg then
		return 
	end

    self.node_list.desc_rune_tower_ads.text.text = cfg.title_desc
    self.node_list.rune_tower_fb_desc.text.text = cfg.fb_desc

	self.node_list["rune_tower_fb_info_sign1"]:SetActive(true)

	local suggest_cfg = FuBenTeamCommonTowerWGData.Instance:GetSuggestCfgByType(GoalTeamType.FbRuneTowerType)
	local suggest_cap = 0
	local kf_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
	if suggest_cfg then
		for i, v in ipairs(suggest_cfg) do
			if v.min_world_level <= kf_world_level and kf_world_level <= v.max_world_level then
				suggest_cap = v.cap
			end
		end
	end
	self.node_list["rune_tower_fb_info1"].text.text = string.format(Language.FuBenPanel.RuneTowerCap, suggest_cap)

    for i = 2, 5 do
		if Language.FuBenPanel.RuneTowerFuBenShuoming[i - 1] then
			self.node_list["rune_tower_fb_info_sign"..i]:SetActive(true)
			self.node_list["rune_tower_fb_info"..i].text.text = Language.FuBenPanel.RuneTowerFuBenShuoming[i - 1]
		else
			self.node_list["rune_tower_fb_info_sign"..i]:SetActive(false)
		end
	end

    self.rune_tower_reward_list:SetDataList(cfg.show_reward_item)
    self:FlushRuneTowerEnterCount()
end

function FuBenPanelView:FlushRuneTowerEnterCount()
    if self.node_list["rune_tower_enter_count"] then
		local team_type_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbRuneTowerType)
		local enter_times = FuBenTeamCommonTowerWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbRuneTowerType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.node_list["rune_tower_enter_count"].text.color = Str2C3b(remain_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.L_RED)
        self.node_list["rune_tower_enter_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, remain_times, max_count)
    end

    local hook_type = FuBenWGData.Instance:GetCombineStatus(FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1)
    self.node_list.layout_rune_tower_combine_hook:SetActive(hook_type == 1)
end

function FuBenPanelView:OnClickRuneTowerCombine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1 + 1].level_limit

    if need_level >= role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips, level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

	local team_type_cfg =  FuBenTeamCommonTowerWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbRuneTowerType)
	local enter_times = FuBenTeamCommonTowerWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbRuneTowerType)
	local max_count = team_type_cfg.max_times or 0
	local remain_times = max_count - enter_times

    if remain_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
	
    local vas = self.node_list.layout_rune_tower_combine_hook:GetActive()

    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine, FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1)
    else
        local callback_func = function()
        end

        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1, callback_func)
    end
end

function FuBenPanelView:OnClickRuneTowerBaoMingEnter()
	local team_type = GoalTeamType.FbRuneTowerType
	local fb_mode = 0

	local not_day_limit, open_day_tab = NewTeamWGData.Instance:CheckIsOpenDayLimit(GoalTeamType.FbRuneTowerType)
	if not not_day_limit then
		local num_str = ""
		for k, v in pairs(open_day_tab) do
			if num_str == "" then
				num_str = NumberToChinaNumber(k)
			else
				num_str = num_str .. "." ..  NumberToChinaNumber(k)
			end
		end

		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBen.OpenDayLimitError, num_str))
		return 
	end

	local team_type_cfg =  FuBenTeamCommonTowerWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbRuneTowerType)
	local enter_times = FuBenTeamCommonTowerWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbRuneTowerType)
	local max_count = team_type_cfg.max_times or 0
	local remain_count = max_count - enter_times
    if remain_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
        return
    end

    NewTeamWGCtrl.Instance:F2SendTeamFuBenEnter(team_type, fb_mode, 5)
end

---------------------FuBenRuneTowerRewardCell--------------------------------
FuBenRuneTowerRewardCell = FuBenRuneTowerRewardCell or BaseClass(BaseRender)
function FuBenRuneTowerRewardCell:__init()
	if not self.base_cell then
		self.base_cell = ItemCell.New(self.node_list["pos"])
		self.base_cell:SetIsShowTips(true)
	end
end

function FuBenRuneTowerRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function FuBenRuneTowerRewardCell:OnFlush()
	if not self.data then
		return
	end

	self.base_cell:SetData(self.data)
    self.node_list.three_flag:SetActive(false)
end