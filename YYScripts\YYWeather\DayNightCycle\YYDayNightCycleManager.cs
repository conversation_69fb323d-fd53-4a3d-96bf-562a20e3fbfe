﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[ExecuteInEditMode]
public class YYDayNightCycleManager : MonoBehaviour {

    public YYDayNightCycleProfile DayNightProfile;
    public float TimeOfDay { get { return DayNightProfile.TimeOfDay; } set { DayNightProfile.TimeOfDay = value; } }

    public bool Auto = true;

    [Range(0.0f,24.0f)]
    public float hour = 12;


    private void Start()
    {
        //EnsureProfile();
        //DayNightProfile.UpdateFromProfile(WeatherMakerScript.Instance != null && WeatherMakerScript.Instance.NetworkConnection.IsServer);

        if (Auto == true)
        {
            DayNightProfile.UpdateFromProfile(true);
        }
        
    }

    private void Update()
    {
        if (Auto == true)
        {
            TimeOfDay = hour * 60 * 60;
            DayNightProfile.UpdateFromProfile(true);
        }
        

    }

    private static YYDayNightCycleManager instance;
    /// <summary>
    /// Shared instance of day/night cycle manager script
    /// </summary>
    public static YYDayNightCycleManager Instance
    {
        get { return YYWeather.FindOrCreateInstance(ref instance, true); }
    }

}
