-- 鸭子选择tips
DuckSelectTips = DuckSelectTips or BaseClass(SafeBaseView)

DuckSelectTips.SELECT_TYPE = {
	FOLLOW = 1, 				-- 跟随 	
	INSPIRE = 2, 				-- 鼓舞
	DISTURB = 3,  				-- 干扰
}

function DuckSelectTips:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "duck_select_layout")
end

function DuckSelectTips:__delete()

end

function DuckSelectTips:OpenCallBack()
end

function DuckSelectTips:CloseCallBack()

end

-- select_type:选择类型（对应枚举DuckSelectTips.SELECT_TYPE）, select_callback:选中后回调
function DuckSelectTips:SetData(select_type, select_callback, pos, child_alignment, special_children_pos)
	self.select_type = select_type
	self.select_callback = select_callback
	self.pos = pos
	self.child_alignment = child_alignment or UnityEngine.TextAnchor.MiddleCenter
	self.special_children_pos = special_children_pos
end

function DuckSelectTips:LoadCallBack()
	self.node_list["duck_head_prefab"]:SetActive(false)
	self.duck_head_list = {}
end

function DuckSelectTips:ReleaseCallBack()
	if self.duck_head_list then
		for k,v in pairs(self.duck_head_list) do
			v:DeleteMe()
		end
	end
	self.duck_head_list = nil
end


function DuckSelectTips:OnFlush()
	self.node_list["content"].transform.position = self.pos
	local hor_layout = self.node_list["head_group"]:GetComponent(typeof(UnityEngine.UI.HorizontalLayoutGroup))
	hor_layout.childAlignment = self.child_alignment

	local duck_index_list = {}
	for i = 0, DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT - 1 do 			-- 全部鸭子都能跟随
		table.insert(duck_index_list, i)
	end
	--[[if self.select_type == DuckSelectTips.SELECT_TYPE.FOLLOW then 				-- 跟随 
		duck_index_list = {}
		for i = 0, DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT - 1 do 			-- 全部鸭子都能跟随
			table.insert(duck_index_list, i)
		end
	elseif self.select_type == DuckSelectTips.SELECT_TYPE.DISTURB then 			-- 干扰
		duck_index_list = DuckRaceWGData.Instance:GetCanDisturbDuckIndexList()
	end--]]

	for k,v in pairs(self.duck_head_list) do
		v:SetActive(false)
	end

	if not IsEmptyTable(duck_index_list) then
		for i, duck_index in ipairs(duck_index_list) do
			if self.duck_head_list[i] == nil then
				local go = ResMgr:Instantiate(self.node_list["duck_head_prefab"].gameObject)
				go:SetActive(true)
				self.duck_head_list[i] = DuckHeadItem.New(go)
			end
			if self.special_children_pos then
				self.duck_head_list[i].view.transform:SetParent(self.node_list[self.special_children_pos .. "_special_head_" .. duck_index].transform, false)
				self.duck_head_list[i].view.transform.localPosition = Vector3.zero
			else
				self.duck_head_list[i].view.transform:SetParent(self.node_list["head_group"].transform, false)
			end
			self.duck_head_list[i]:SetActive(true)
			self.duck_head_list[i]:SetIndex(duck_index)
			self.duck_head_list[i]:SetSelectType(self.select_type)
			self.duck_head_list[i]:SetClickCallback(function(index)
				if self.select_callback then
					self.select_callback(index)
					self:Close()
				end
			end)
			self.duck_head_list[i]:Flush()
		end
	else
		self:Close()
	end
end

------------------------------------------------------------------
-- 鸭子头像
DuckHeadItem = DuckHeadItem or BaseClass(BaseGridRender)
function DuckHeadItem:__init()
	XUI.AddClickEventListener(self.node_list["head_icon"], BindTool.Bind(self.OnClickHead, self))
end

function DuckHeadItem:__delete()

end

function DuckHeadItem:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function DuckHeadItem:OnFlush()
	local duck_info = DuckRaceWGData.Instance:GetDuckInfoByIndex(self.index)
	if duck_info then 
		local duck_cfg = DuckRaceWGData.Instance:GetDuckCfg(duck_info.duck_id) 									-- 鸭子配置
		self.node_list["head_icon"].image:LoadSprite(ResPath.GetDuckRaceImg("a2_xzsl_" .. self.index)) 	-- 鸭子头像
	end
	--self.node_list["duck_index"].image:LoadSprite(ResPath.GetDuckRaceImg("duck_index_" .. self.index)) 			-- 鸭子下标
	self.node_list["bet_label"]:SetActive(DuckRaceWGData.Instance:GetIsMyBetDuckIndex(self.index)) 				-- 已下注标签
end

function DuckHeadItem:OnClickHead()
	if self.select_type == DuckSelectTips.SELECT_TYPE.INSPIRE then
		local can_op = DuckRaceWGData.Instance:GetIsMyBetDuckIndex(self.index)
		if not can_op then
			TipWGCtrl.Instance:ShowSystemMsg(Language.DuckRace.Reason4)
			return
		end
	end

	if self.click_callback then
		self.click_callback(self.index)
	end
end

function DuckHeadItem:SetSelectType(select_type)
	self.select_type = select_type
end