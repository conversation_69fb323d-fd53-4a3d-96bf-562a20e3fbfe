local show_image_pos = {
    [1] = {x = -62, y = 24},
    [2] = {x = -70, y = 22},
    [3] = {x = -28, y = 70},
    [4] = {x = -76, y = 54},
}

function FightSoulView:InitTrainView()
    self.t_uplevel_attr_list = {}
    local attr_num = self.node_list.base_attr_part.transform.childCount
    for i = 1, attr_num do
        local cell = FightSoulAttrRender.New(self.node_list.base_attr_part:FindObj("attr_" .. i))
        cell:SetIndex(i)
        self.t_uplevel_attr_list[i] = cell
    end

    self.t_break_attr_list = {}
    attr_num = self.node_list.break_attr_part.transform.childCount
    for i = 1, attr_num do
        local cell = FightSoulAttrRender.New(self.node_list.break_attr_part:FindObj("attr_" .. i))
        cell:SetIndex(i)
        self.t_break_attr_list[i] = cell
    end

    self.train_star_list = {}
    local star_num = self.node_list.t_star_part.transform.childCount
    for i = 1, star_num do
        self.train_star_list[i] = self.node_list["t_star_" .. i]
    end

    self.break_stuff_item = ItemCell.New(self.node_list.break_stuff_item)
    self.base_stuff_item = ItemCell.New(self.node_list.base_stuff_icon)

    self:InitExpItemIcon()

    XUI.AddClickEventListener(self.node_list.t_skill, BindTool.Bind(self.ClickTrainSkill, self))
    --XUI.AddClickEventListener(self.node_list.base_stuff_icon, BindTool.Bind(self.ClickUpLevelStuff, self))
    XUI.AddClickEventListener(self.node_list.btn_t_wear, BindTool.Bind(self.ClickTrainWear, self))
    XUI.AddClickEventListener(self.node_list.btn_exp_pool, BindTool.Bind(self.ClickExpPool, self))
    XUI.AddClickEventListener(self.node_list.btn_uplevel, BindTool.Bind(self.ClickTrainUpLevel, self))
    XUI.AddClickEventListener(self.node_list.btn_break, BindTool.Bind(self.ClickTrainBreak, self))
    XUI.AddClickEventListener(self.node_list["btn_out_fight"], BindTool.Bind(self.OnClickOutFight, self))
    XUI.AddClickEventListener(self.node_list["icon_fighting"], BindTool.Bind(self.OnClickOutFight, self))

    local str
    for index = 1, 2 do
        str = string.format("t_toggle_%d", index)
        XUI.AddClickEventListener(self.node_list[str], BindTool.Bind(self.OnToggleChanged, self, index))
        if index == 1 then
            self.node_list[str].toggle.isOn = true
            self:OnToggleChanged(index, true)
        end
    end

    -- self:InitTrainTweenPos()
end

function FightSoulView:DeleteTrainView()
    if self.break_stuff_item then
        self.break_stuff_item:DeleteMe()
        self.break_stuff_item = nil
    end

    if self.base_stuff_item then
        self.base_stuff_item:DeleteMe()
        self.base_stuff_item = nil
    end

    if self.t_uplevel_attr_list then
        for k,v in pairs(self.t_uplevel_attr_list) do
            v:DeleteMe()
        end
        self.t_uplevel_attr_list = nil
    end

    if self.t_break_attr_list then
        for k,v in pairs(self.t_break_attr_list) do
            v:DeleteMe()
        end
        self.t_break_attr_list = nil
    end

    self:CleanTrainCDTime()
    self.train_star_list = nil
    self.cache_t_fs_type = nil
end

function FightSoulView:InitExpItemIcon()
    local exp_item_id = FightSoulWGData.Instance:GetFightSoulExpItemId()
    self.base_stuff_item:SetData({item_id = exp_item_id})
end

function FightSoulView:OnToggleChanged(index, is_on)
    if not is_on then
		return
	end

    self.select_toggle_index = index
    self:FlushTrainToggle()
end

function FightSoulView:FlushTrainToggle(flush_remind)
    if self.select_toggle_index == 1 then
        self:FlushUpLevelPart()
    else
        self:FlushBreakPart()
    end

    if flush_remind then
        self.node_list.t_toggle_remind_1.image.enabled = FightSoulWGData.Instance:GetSlotSingleLevelRemind(self.select_slot_index)
        self.node_list.t_toggle_remind_2.image.enabled = FightSoulWGData.Instance:GetSlotSingleBreakRemind(self.select_slot_index)
    end
end

function FightSoulView:FlushTrainView(no_flush_all)
    -- self:DoTrainTween()
    self:FlushTrainToggle(true)
    self:FlushTrainModelPart(no_flush_all)
end

function FightSoulView:InitTrainTweenPos()
    RectTransform.SetAnchoredPositionXY(self.node_list.t_right_part.rect, 320, 0)
end

function FightSoulView:DoTrainTween()
    if not self.do_train_tween_flag then
        return
    end
    self.do_train_tween_flag = false


    UITween.CleanAllTween(GuideModuleName.FightSoulView)

    UITween.FakeHideShow(self.node_list.t_right_tween_node)
    -- self:InitTrainTweenPos()
    -- self:InitSlotListTween()
    -- self:DoTrainTweenMove()
end

function FightSoulView:CleanTrainCDTime()
    if self.train_tween_quest ~= nil then
        GlobalTimerQuest:CancelQuest(self.train_tween_quest)
        self.train_tween_quest = nil
    end
end

function FightSoulView:DoTrainTweenMove()
    if not self.list_load_over then
        if self.train_tween_quest == nil then
            self.train_tween_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.DoTrainTweenMove, self), 0.1)
        end
        return
    else
        self:CleanTrainCDTime()
    end

    local tween_info = UITween_CONSTS.SiXiangSys
    self:MoveSlotListTween()
    self.node_list.t_right_part.rect:DOAnchorPos(Vector2(-10, 0), tween_info.MoveTime)
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.FightSoulView, self.node_list.t_right_tween_node, 0, 1, tween_info.AlphaTime)
    end, tween_info.MoveTime, "fs_train_tween")
end

------------------------------养成公共  部分-------------------------------------
function FightSoulView:TrainNoWearShow()
    XUI.SetGraphicGrey(self.node_list.add_img, true)
    self.node_list.t_no_wear_part:SetActive(true)
    self.node_list.t_skill_part:SetActive(false)
    self.node_list.t_star_part:SetActive(false)
    self.node_list.train_spine_pos:SetActive(false)
end

function FightSoulView:TrainHadWearShow()
    self.node_list.t_no_wear_part:SetActive(false)
    self.node_list.t_skill_part:SetActive(true)
    self.node_list.t_star_part:SetActive(true)
    self.node_list.train_spine_pos:SetActive(true)
end

-- 模型面板
function FightSoulView:FlushTrainModelPart(no_flush_all)
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
        return
    end

    if fs_data:GetIsWear() then
        self:TrainHadWearShow()
        self:SetShowStar(fs_data:GetStar())

        -- 技能
        local skill_id, skill_level = fs_data:GetSkillIdAndLevel()
        self.node_list.t_skill_level.text.text = string.format(Language.Common.LevelNormal, skill_level)

        local s_bundle, s_asset = FightSoulWGData.Instance:GetSkillIconResByItemId(fs_data:GetItemId())
        self.node_list.t_skill_icon:SetActive(false)
        self.node_list.t_skill_icon.image:LoadSprite(s_bundle, s_asset, function()
            self.node_list.t_skill_icon:SetActive(true)
        end)

        local fs_type = fs_data:GetType()
        if self.show_fs_model_ani or self.cache_t_fs_type ~= fs_type then
            local bundle, asset = ResPath.GetFightSoulShowUI(fs_type)
            self.node_list.train_spine_pos:ChangeAsset(bundle, asset, false)
            RectTransform.SetAnchoredPositionXY(self.node_list.train_spine_pos.rect, show_image_pos[fs_type].x, show_image_pos[fs_type].y)

            self.cache_t_fs_type = fs_type
        end
    else
        self.node_list.t_remind_inlay:SetActive(FightSoulWGData.Instance:GetFightSoulCanWearByType(fs_data:GetSlotIndex()))
        self:TrainNoWearShow()
    end

    if not no_flush_all then
        self.node_list.t_cap_value.text.text = FightSoulWGData.Instance:GetNoEquipCapability(fs_data:GetItemData())
    end
end

function FightSoulView:SetShowStar(num)
    num = num or 0
    local star_res_list = GetStarImgResByStar(num)
    local bundle, asset = "", ""
    for i = 1, GameEnum.ITEM_MAX_STAR do
        if self.train_star_list[i] then
            if i <= num then
                self.train_star_list[i]:SetActive(true)
                bundle, asset = ResPath.GetCommonImages(star_res_list[i])
                self.train_star_list[i].image:LoadSprite(bundle, asset)
            else
                self.train_star_list[i]:SetActive(false)
            end
        end
    end
end

-- 点击技能
function FightSoulView:ClickTrainSkill()
    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        return
    end

    FightSoulWGCtrl.Instance:OpenSkillTipsByItemId(data:GetItemId(), data:GetSlotIndex())
end

-- 点击exp stuff
function FightSoulView:ClickUpLevelStuff()
    local exp_item_id = FightSoulWGData.Instance:GetFightSoulExpItemId()
    TipWGCtrl.Instance:OpenItem({item_id = exp_item_id})
end

-- 点击上阵
function FightSoulView:ClickTrainWear()
    FightSoulWGCtrl.Instance:OpenSelectViewBySlot(self.select_slot_index)
end

function FightSoulView:ClickExpPool()
    FightSoulWGCtrl.Instance:OpenExpPoolView()
end

------------------------------升级面板  部分-------------------------------------
-- 升级面板
function FightSoulView:FlushUpLevelPart()
    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        return
    end

    local fs_data = FightSoulWGData.Instance
    local level = data:GetLevel()
    local is_wear = data:GetIsWear()
    local is_max = data:GetIsMaxLevel()
    local is_grade_max_level = data:GetIsCurGradeMaxLevel()
    local show_limit = not is_wear or is_max or is_grade_max_level

    -- 展示
    self.node_list.base_no_fight_desc:SetActive(not is_wear)
    self.node_list.base_level_part:SetActive(is_wear)
    local btn_text = Language.FightSoul.UpLevelBtnTxt[1]
    local limit_desc = ""
    if not is_wear then
        self.node_list.base_no_fight_desc.text.text = Language.FightSoul.UpLevelLimitDesc1
    elseif is_max then
        limit_desc = Language.FightSoul.UpLevelLimitDesc2
        self.node_list.base_level_arrow:SetActive(false)
        self.node_list.base_next_level:SetActive(false)
        self.node_list.base_next_img:SetActive(false)
        btn_text = Language.FightSoul.UpLevelBtnTxt[3]
    elseif is_grade_max_level then
        limit_desc = Language.FightSoul.UpLevelLimitDesc3
        self.node_list.base_level_arrow:SetActive(true)
        self.node_list.base_next_level.text.text = string.format(Language.Common.LevelNormal, level + 1)
        self.node_list.base_next_level:SetActive(true)
        self.node_list.base_next_img:SetActive(true)
        btn_text = Language.FightSoul.UpLevelBtnTxt[2]
    else
        self.node_list.base_level_arrow:SetActive(true)
        self.node_list.base_next_level.text.text = string.format(Language.Common.LevelNormal, level + 1)
        self.node_list.base_next_level:SetActive(true)
        self.node_list.base_next_img:SetActive(true)
    end
    self.node_list.base_cur_level.text.text = string.format(Language.Common.LevelNormal, level)
    self.node_list.base_limit_desc.text.text = limit_desc

    -- 属性
    local attr_lsit = fs_data:GetUpLevelShowAttr(level, not is_wear)
    for k,v in pairs(self.t_uplevel_attr_list) do
        v:SetData(attr_lsit[k])
    end

    -- 经验池
    local exp = fs_data:GetExp()
    local max_exp = fs_data:GetMaxExp()
    local exp_pool_level = fs_data:GetExpPoolLevel()
    local pool_cfg = fs_data:GetExpPoolCfg(exp_pool_level)
    local percent = exp / max_exp
    self.node_list.exp_pro_text.text.text = string.format("%d%%", math.floor(percent * 100))
    percent = percent > 1 and 1 or percent
    self.node_list.exp_progress.image.fillAmount = percent

    local next_level_cfg = fs_data:GetLevelCfgByLevel(level + 1)
    local need_exp = next_level_cfg and next_level_cfg.need_exp or 0
    local enough_exp = exp >= need_exp
    local exp_txt_color = enough_exp and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
    local exp_str = string.format("<color=%s>%s</color>/%s", exp_txt_color,
                                    CommonDataManager.ConverNumber(exp),
                                    CommonDataManager.ConverNumber(need_exp))
    self.node_list.base_stuff_text.text.text = is_wear and not is_max and exp_str or ""

    -- 按钮
    local is_max_pool = fs_data:IsMaxExpPoolLevel()
    self.node_list.btn_uplevel_remind:SetActive(not show_limit and enough_exp)
    self.node_list.btn_uplevel_text.text.text = btn_text
    self.node_list.btn_uplevel_part:SetActive(not is_max)
    self.node_list.uplevel_ismax:SetActive(is_max)
end

function FightSoulView:ClickTrainUpLevel()
    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        return
    end

    local level = data:GetLevel()
    local is_wear = data:GetIsWear()
    local is_max = data:GetIsMaxLevel()
    local is_grade_max_level = data:GetIsCurGradeMaxLevel()
    if not is_wear then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.UpLevelLimitTips1)
        return
    end

    if is_max then
        return
    end

    -- 前往突破
    if is_grade_max_level then
        self.node_list.t_toggle_2.toggle.isOn = true
        return
    end

    local exp = FightSoulWGData.Instance:GetExp()
    local next_level_cfg = FightSoulWGData.Instance:GetLevelCfgByLevel(level + 1)
    local need_exp = next_level_cfg and next_level_cfg.need_exp or 0
    local no_enough_exp = exp < need_exp
    if no_enough_exp then
        local item_id = FightSoulWGData.Instance:GetFightSoulExpItemId()
        local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
        if item_cfg then
            local item_name = ToColorStr(Language.FightSoul.FightSoulExpName, ITEM_COLOR[item_cfg.color])
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.NoEnoughTips, item_name))
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
            return
        end
    end

    FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.FIGHT_SOUL_UPLEVEL, self.select_slot_index)
end



------------------------------突破面板  部分-------------------------------------
-- 突破面板
function FightSoulView:FlushBreakPart()
    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        return
    end

    local grade = data:GetGrade()
    local is_wear = data:GetIsWear()
    local is_max = data:GetIsMaxGrade()
    local is_break_limit = data:GetIsBreakLimit()
    local show_limit = not is_wear or is_break_limit
    local grade_cfg = FightSoulWGData.Instance:GetBreakCfgByGrade(grade)

    -- 展示
    self.node_list.break_no_fight_desc:SetActive(not is_wear)
    self.node_list.break_chong_part:SetActive(is_wear or is_max)
    if is_max then
        self.node_list["base_cur_quality"].image:LoadSprite(ResPath.GetCommon("a2_quality_text_" .. grade_cfg.show_quality))
        self.node_list.base_cur_jie.text.text = string.format(Language.FightSoul.GradeJie, NumberToChinaNumber(grade_cfg.show_grade))
        self.node_list.base_next_quality:SetActive(false)
        self.node_list.base_chong_arrow:SetActive(false)
        self.node_list.break_next_chong:SetActive(false)
        self.node_list.base_next_jie:SetActive(false)
    else

        local next_grade_cfg = FightSoulWGData.Instance:GetBreakCfgByGrade(grade + 1)
        self.node_list["base_cur_quality"].image:LoadSprite(ResPath.GetCommon("a2_quality_text_" .. grade_cfg.show_quality))
        self.node_list["base_next_quality"].image:LoadSprite(ResPath.GetCommon("a2_quality_text_" .. next_grade_cfg.show_quality))
        self.node_list.base_cur_jie.text.text = string.format(Language.FightSoul.GradeJie, NumberToChinaNumber(grade_cfg.show_grade))
        self.node_list.base_next_jie.text.text = string.format(Language.FightSoul.GradeJie, NumberToChinaNumber(next_grade_cfg.show_grade))

        self.node_list.base_next_quality:SetActive(true)
        self.node_list.base_chong_arrow:SetActive(true)
        self.node_list.break_next_chong:SetActive(true)
        self.node_list.base_next_jie:SetActive(true)
    end

    local limit_desc = ""
    if is_max then
        limit_desc = Language.FightSoul.BreakLimitDesc1
    elseif is_break_limit and grade_cfg then
        limit_desc = string.format(Language.FightSoul.BreakLimitDesc2, grade_cfg.up_max_level)
    elseif grade_cfg then
        local old_level, max_level = FightSoulWGData.Instance:GetCurGradeLevelRange(grade + 1)
        limit_desc = string.format(Language.FightSoul.BreakLimitDesc4, max_level)
    end
    self.node_list.break_limit_desc.text.text = limit_desc

    -- 属性
    local attr_lsit = FightSoulWGData.Instance:GetBreakShowAttr(grade, not is_wear)
    for k,v in pairs(self.t_break_attr_list) do
        v:SetData(attr_lsit[k])
    end

    -- 材料
    local is_have_stuff = false
    if is_wear and grade_cfg then
        local item_str = ""
        local str_color
        local consume_item = FightSoulWGData.Instance:GetBreakStuffIdByType(data:GetType())
        self.break_stuff_item:SetData({item_id = consume_item})
        if not is_max and grade_cfg.consume_num > 0 then
            local num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
            is_have_stuff = num >= grade_cfg.consume_num
            str_color = is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
            item_str = num .. "/" .. grade_cfg.consume_num
        end
        self.node_list.base_stuff_num.text.text = item_str
        self.node_list.base_stuff_num:SetActive(true)
        --self.break_stuff_item:SetRightBottomColorText(item_str, str_color)
        --self.break_stuff_item:SetRightBottomTextVisible(true)
    else
        self.node_list.base_stuff_num:SetActive(false)
        self.break_stuff_item:ClearData()
        self.break_stuff_item:SetItemIcon(ResPath.GetCommonImages("a2_ty_suo"))
    end

    -- 按钮
    self.node_list.btn_break_remind:SetActive(not show_limit and is_have_stuff)
    self.node_list.btn_break:SetActive(not is_max)
    self.node_list.break_ismax:SetActive(is_max)

    local btn_text = Language.FightSoul.BreakBtnTxt[1]
    if is_max then
        btn_text = Language.FightSoul.BreakBtnTxt[3]
    elseif is_break_limit then
        local can_level = FightSoulWGData.Instance:GetSlotSingleLevelRemind(self.select_slot_index)
        if can_level then
            btn_text = Language.FightSoul.BreakBtnTxt[2]
        end
    end
    self.node_list.btn_break_text.text.text = btn_text
end

function FightSoulView:ClickTrainBreak()
    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        return
    end

    local grade = data:GetGrade()
    local is_wear = data:GetIsWear()
    local is_max = data:GetIsMaxGrade()
    local is_break_limit = data:GetIsBreakLimit()
    if not is_wear then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.BreakLimitTips1)
        return
    end

    if is_max then
        return
    end

    local can_level = FightSoulWGData.Instance:GetSlotSingleLevelRemind(self.select_slot_index)
    if can_level then
        self.node_list.t_toggle_1.toggle.isOn = true
        return
    end

    if is_break_limit then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.BreakLimitDesc3)
        return
    end

    local grade_cfg = FightSoulWGData.Instance:GetBreakCfgByGrade(grade)
    local consume_item = FightSoulWGData.Instance:GetBreakStuffIdByType(data:GetType())
    if grade_cfg and grade_cfg.consume_num > 0 then
        local num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
        if num < grade_cfg.consume_num then
            local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(consume_item)
            if item_cfg then
                local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.NoEnoughTips, item_name))
                TipWGCtrl.Instance:OpenItemTipGetWay({item_id = consume_item}, grade_cfg.consume_num - num)
                return
            end
        end
    end

    FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.FIGHT_SOUL_BREAK, self.select_slot_index)
end

function FightSoulView:OnClickOutFight()
	local data = self:GetCurFightSoulSlot()
	if IsEmptyTable(data) then
		return
	end

	local send_index = data:GetOutFightFlag() and -1 or self.select_slot_index
	FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.SIXIANG_CALL, send_index)
end

-- 刷新出战按钮
function FightSoulView:FlushOutFightPart()
    if not self:IsLoadedIndex(TabIndex.fight_soul_train) then
        return
    end

    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        self.node_list.out_fight_part:SetActive(false)
        return
    else
		local is_wear = data:GetIsWear()
        self.node_list.out_fight_part:SetActive(is_wear)
		if is_wear then
	        local is_out_fight = data:GetOutFightFlag()
	        self.node_list.btn_out_fight:SetActive(not is_out_fight)
	        self.node_list.icon_fighting:SetActive(is_out_fight)
			self.node_list.out_fight_remind:SetActive(data:GetOutFightRemind())
		else
			self.node_list.out_fight_remind:SetActive(false)
		end
    end
end