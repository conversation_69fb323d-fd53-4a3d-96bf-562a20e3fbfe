EquipmentWGData = EquipmentWGData or BaseClass()

EquipmentWGData.COMPOSE_EQUIP_TYPE = {
	FEMALE = 0,		-- 女性装备
	MALE = 1,		-- 男性装备
	JEWELRY = 2,	-- 首饰
}

function EquipmentWGData:InitHechengData()
	self.cache_meet_condition = {}
	self.equipforge_cfg = ConfigManager.Instance:GetAutoConfig("equipforge_auto") --锻造表
	self.shenshou_equip_cfg = ConfigManager.Instance:GetAutoConfig("shenshou_cfg_new_auto") --神兽装备表
	self.mount_equip_cfg = ConfigManager.Instance:GetAutoConfig("mount_auto") --坐骑表
	self.compose_zhuzhan_cfg =  ConfigManager.Instance:GetAutoConfig("compose_auto")
	self.tianshen_cfg =  ConfigManager.Instance:GetAutoConfig("tianshen_cfg_auto")

	self.zhizun_equip_cfg = ConfigManager.Instance:GetAutoConfig("zhizunequip_auto")--至尊装备表

	self.equip_compose_to_map = ListToMap(self.equipforge_cfg.equip_compose, "compose_equip_id", "compose_equip_best_attr_num")
	self.equip_show_compose_to_map = ListToMapList(self.equipforge_cfg.equip_exchange, "title_index")
	self.equip_show_compose_to_map2 = ListToMap(self.equipforge_cfg.equip_exchange,
									"title_index", "compose_equip_best_attr_num", "type", "order")
	 self:InitComposeConsumeList()

	self.equip_sshecheng_make = self:InitEquipSSHeChengMakeData()
	self.equip_ts_hecheng_make = self:InitEquipTSHeChengMakeData()
	self.equip_lingchong_make = self:InitEquipLingChongHeChengMakeData()
	self.equip_mount_make = self:InitEquipMountHeChengMakeData()

	self.nan_jianke_data = self:InitEquipComposeShowData(EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE)
	self.nv_jianke_data = self:InitEquipComposeShowData(EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE)
	self.compose_jewelry_data = self:InitEquipComposeShowData(EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY)

	self.equip_success_probability = {
		[3] = 70,
		[4] = 85,
		[5] = 100,
	}
end

function EquipmentWGData:InitComposeConsumeList()
	local consume_list = {}
	local cfg = self.equipforge_cfg.equip_compose
	if IsEmptyTable(cfg) then
		self.equip_compose_consume_list = consume_list
		return
	end

	for k, v in pairs(cfg) do
		local equip_id = v.compose_equip_id
		local star = v.compose_equip_best_attr_num
		if not consume_list[equip_id] then
			consume_list[equip_id] = {}
		end

		if not consume_list[equip_id][star] then
			consume_list[equip_id][star] = {}
		end

		local data = {}
		local need_sex = GameEnum.MALE
		if v.title_index == EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE then
			need_sex = GameEnum.FEMALE
		elseif v.title_index == EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY then
			need_sex = GameEnum.SEX_NOLIMIT
		end

		data.need_sex = need_sex
		data.need_sex_limit = v.sex_limit > 0
		data.need_part_limit = v.part_limit > 0
		data.part_limit_list = EquipmentWGData.GetECPartLimitList(v.part_limit_range)
		data.need_order = v.need_order
		data.equip_consume_list = self:GetComposeConsumeList(v)
		local codition_num = #data.equip_consume_list
		local remind_equip_num = codition_num > 0 and (v.max_consume_num / codition_num) or 0
		data.remind_equip_num = remind_equip_num

		data.stuff_cost_list = {}
		for i = 1, 2 do
			local id_str = string.format("stuff%s_id", i)
			local num_str = string.format("stuff%s_num", i)
			local stuff_id = v[id_str] or 0
			local stuff_num = v[num_str] or 0
			if stuff_id > 0 and stuff_num > 0 then
				local stuff_data = {stuff_id = stuff_id, stuff_num = stuff_num}
				table.insert(data.stuff_cost_list, stuff_data)
			end
		end
		consume_list[equip_id][star] = data
	end

	self.equip_compose_consume_list = consume_list
end

function EquipmentWGData:GetComposeConsumeData(item_id, star)
	local empty_table = {}
	return ((self.equip_compose_consume_list or empty_table)[item_id] or empty_table)[star]
end

function EquipmentWGData:InitEquipComposeShowData(title_index)
	local data_list = {}
	local show_list = self.equip_show_compose_to_map[title_index]
	if IsEmptyTable(show_list) then
		return data_list
	end

	for k, v in ipairs(show_list) do
		local key = v.type * 100 + v.compose_equip_best_attr_num
		if data_list[key] == nil then
			data_list[key] = {}
		end

		table.insert(data_list[key], v)
	end

	for k,v in pairs(data_list) do
		table.sort(v, SortTools.KeyLowerSorter("order"))
	end

	return data_list
end

--根据等级获取装备合成显示的最大最小阶数
function EquipmentWGData:GetEquinHeChengShowOrder()
	local cfg = self.equipforge_cfg.equip_exchange_order_show
	local max_order, min_order = 0, 0
	local role_level = RoleWGData.Instance:GetRoleLevel()

	for k = #cfg, 1, -1 do
		if role_level >= cfg[k].level then
			max_order = cfg[k].max_order
			min_order = cfg[k].min_order
			break
		end
	end

	return max_order, min_order
end

function EquipmentWGData:GetEquipComposeOrderRange()
	if not self.equip_compose_order_min or not self.equip_compose_order_max then
		local cfg = self.equipforge_cfg.equip_exchange_order_show
		if IsEmptyTable(cfg) then
			self.equip_compose_order_min = 0
			self.equip_compose_order_max = 0
		else
			for k, v in ipairs(cfg) do
				if not self.equip_compose_order_min or self.equip_compose_order_min > v.min_order then
					self.equip_compose_order_min = v.min_order
				end

				if not self.equip_compose_order_max or self.equip_compose_order_max < v.max_order then
					self.equip_compose_order_max = v.max_order
				end
			end
		end
	end

	return self.equip_compose_order_min, self.equip_compose_order_max
end

function EquipmentWGData:GetEquipIsCanCompose(data)
	local tab_index = "other_compose_eq_hecheng_one"
	if data == nil then
		return false, tab_index, {}
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id or data.id)
	if not item_cfg or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return false, tab_index, {}
	end

	local legend_num = data.param and data.param.star_level or 0
	local max_order, min_order = self:GetEquinHeChengShowOrder()
	if min_order > item_cfg.order or item_cfg.order > max_order or legend_num < 2 then
		return false, tab_index, {}
	end

	local part_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	-- if part_index == GameEnum.EQUIP_INDEX_XIANJIE or part_index == GameEnum.EQUIP_INDEX_XIANZHUO
	-- or part_index > GameEnum.EQUIP_INDEX_XIANJIE then
	-- 	return false, tab_index, {}
	-- end

	local cur_type = EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(part_index)
	if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
		tab_index = "other_compose_eq_hecheng_three"
		cur_type = EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY
	elseif role_sex == GameEnum.FEMALE then
		tab_index = "other_compose_eq_hecheng_two"
		cur_type = EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE
	end

	local show_list = self.equip_show_compose_to_map[cur_type]
	if IsEmptyTable(show_list) then
		return false, tab_index, {}
	end

	for k, v in ipairs(show_list) do
		if item_cfg.order == v.order then
			local tmp_itemid = cur_type == EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY and v.cao12 or v.cao1
			local compose_cfg = self:GetEquipComposeCfgByID(tmp_itemid, v.compose_equip_best_attr_num)
			if not IsEmptyTable(compose_cfg) then
                local consume_condition = self:GetComposeConsumeList(compose_cfg)
				for i = #consume_condition, 1, -1 do
                    local temp_data = consume_condition[i]
					for temp_k, temp_v in ipairs(temp_data) do
						if item_cfg.color == temp_v.need_color and legend_num == temp_v.need_star then
							return true, tab_index, v
						end
					end
				end
			end
		end
	end

	return false, tab_index, {}
end

function EquipmentWGData:GetEquipComposeJumpParam(title_index, type, star_level, order)
	local open_param, to_ui_param
	local acc_list = EquipmentWGData.Instance:GetEquinHeChengAccordionDataList(title_index)
	if not IsEmptyTable(acc_list) then
		for acc_k, acc_v in ipairs(acc_list) do
			if acc_v.name_type == type and acc_v.star_level == star_level then
				for child_k, child_v in ipairs(acc_v.child) do
					if child_v.order == order then
						open_param = acc_k
						to_ui_param = child_k
						return open_param, to_ui_param
					end
				end
			end
		end
	end

	return open_param, to_ui_param
end

--根据目标物品获取装备合成界面跳转参数
function EquipmentWGData:GetEquipComposeCfgByProductId(product_id, cur_type, star_lv)
    local show_list = self.equip_show_compose_to_map[cur_type]
	if IsEmptyTable(show_list) then
		return false, 0, 0
    end
    local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(product_id)
    if not item_cfg or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return false, 0, 0
    end
    for k, v in ipairs(show_list) do
		if item_cfg.order == v.order then
            local compose_cfg = self:GetEquipComposeCfgByID(product_id, star_lv)
            if not IsEmptyTable(compose_cfg) then
                local open_param, to_ui_param
                local acc_list = self:GetEquinHeChengAccordionDataList(compose_cfg.title_index)
                if not IsEmptyTable(acc_list) then
                    for acc_k, acc_v in ipairs(acc_list) do
                        if acc_v.name_type == (item_cfg.color - 2) and acc_v.star_level == star_lv then --type 好像就是color-2
                            open_param = acc_k
                            for child_k, child_v in ipairs(acc_v.child) do
                                if child_v.order == item_cfg.order then
                                    to_ui_param = child_k
                                    return true, open_param, to_ui_param
                                end
                            end
                        end
                    end
                end
			end
		end
    end
    return false, 0, 0
end

--获取装备合成可折叠的数据列表
function EquipmentWGData:GetEquinHeChengAccordionDataList(param)
	local acc_data_list = {}
	local max_order, min_order = self:GetEquinHeChengShowOrder()
	-- local role_level = RoleWGData.Instance:GetRoleLevel()
	local cur_data = {}
	if param == EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE then
		cur_data = self.nan_jianke_data
	elseif param == EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE then
		cur_data = self.nv_jianke_data
	elseif param == EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY then
		cur_data = self.compose_jewelry_data
	end

	for k, child_list in pairs(cur_data)do
		local item_data = {}
		for k, v in ipairs(child_list) do
			-- if v.zhizun ~= 1 then
				if min_order <= v.order and v.order <= max_order then
					table.insert(item_data, v)
				end
			-- else
			-- 	if v.show_zhizun <= role_level then
			-- 		table.insert(item_data, v)
			-- 	end
			-- end
		end

		if not IsEmptyTable(item_data) then
			local sort_index = item_data[1].type * 199 + item_data[1].compose_equip_best_attr_num
			local temp_data = {child = item_data,
								title_index = item_data[1].title_index,
								star_level = item_data[1].compose_equip_best_attr_num,
								name_type = item_data[1].type,
								sort_index = sort_index}
			table.insert(acc_data_list, temp_data)
		end
	end

	table.sort(acc_data_list, SortTools.KeyLowerSorter("sort_index"))
	return acc_data_list
end

-- 根据大分页 获取红点
function EquipmentWGData:GetComposeEquipRemindByBigType(title_index)
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local show_list = self.equip_show_compose_to_map[title_index]

	if not title_index or IsEmptyTable(show_list) then
		return 0, nil, nil, nil
	end

	local max_order, min_order = self:GetEquinHeChengShowOrder()

	for k, v in pairs(show_list) do
		if v.order >= min_order and v.order <= max_order then
			for i = 1, 15 do
				if v["cao" .. i] and v["cao" .. i] > 0 then
					local remind = self:GetCESingleRemindByData(v["cao" .. i], v.compose_equip_best_attr_num)
					if remind then
						return 1, v.type, v.compose_equip_best_attr_num, v.order
					end
				end
			end
		end
	end

	return 0, nil, nil, nil
end

-- 根据中分页 获取红点
function EquipmentWGData:GetComposeEquipRemindByMidType(title_index, star, type)
	local empty_table = {}
	local cfg = ((self.equip_show_compose_to_map2[title_index] or empty_table)[star] or empty_table)[type]

	if IsEmptyTable(cfg) then
		return false
	end

	for order, v in pairs(cfg) do
		for i = 1, 15 do
			if v["cao" .. i] and v["cao" .. i] > 0 then
				local remind = self:GetCESingleRemindByData(v["cao" .. i], v.compose_equip_best_attr_num)
				if remind then
					return true
				end
			end
		end
	end

	return false
end

-- 根据小分页 获取红点
function EquipmentWGData:GetComposeEquipRemindBySmallType(title_index, star, type, order)
	local empty_table = {}
	local cfg = (((self.equip_show_compose_to_map2[title_index] or empty_table)[star] or empty_table)[type] or empty_table)[order]

	if IsEmptyTable(cfg) then
		return false
	end

	for i = 1, 15 do
		if cfg["cao" .. i] and cfg["cao" .. i] > 0 then
			local remind = self:GetCESingleRemindByData(cfg["cao" .. i], cfg.compose_equip_best_attr_num)
			if remind then
				return true
			end
		end
	end

	return false
end

-- 普通装备 和 饰品区分开
-- 1、10阶以下装备：如果当前身上装备最低都是8阶金色，就不提示8阶及8阶以下的粉装、金装合成
-- 2、10阶以上装备（能合炫彩）：如果当前身上装备最低都是10阶最低是金色，就不提示9阶及以下的粉装、金装合成
-- 3、10阶以上装备（能合炫彩）：如果当前身上装备最低都是10阶最低是炫彩，就不提示10阶及以下的粉装、金装合成
function EquipmentWGData:GetEquipComposeNeedSpecialLimit(equip_type, order, color)
	local min_wear_data = EquipWGData.Instance:GetComposeRermindNeedData(equip_type)
	if IsEmptyTable(min_wear_data) then
		return false
	end

	local pink = GameEnum.ITEM_COLOR_PINK
	local glod = GameEnum.ITEM_COLOR_GLOD
	local color_ful = GameEnum.ITEM_COLOR_XUAN_QING
	local min_color = min_wear_data.color
	local min_order = min_wear_data.order
	if min_color < glod then
		return false
	end

	if min_order < 10 then
		if order <= min_order and (color == pink or color == glod) then
			return true
		end
	else
		if min_color < color_ful then
			if order < min_order and (color == pink or color == glod) then
				return true
			end
		else
			if order <= min_order and (color == pink or color == glod) then
				return true
			end
		end
	end

	return false
end

-- 6阶以下粉/金装红点加多个判断条件，如果身上已经穿了 就不显示红点了（6阶以上可以合幻彩，不用特殊判断）
function EquipmentWGData:CheckFenEquipSpecialLimit(equip_body_index, order, color)
	if order < 6 and (color == GameEnum.ITEM_COLOR_PINK or color == color == GameEnum.ITEM_COLOR_GLOD) then
		local wear_data = EquipWGData.Instance:GetGridData(equip_body_index)

		if wear_data and wear_data.item_id > 0 then
			local _, item_color = ItemWGData.Instance:GetItemColor(wear_data.item_id)

			if item_color >= GameEnum.ITEM_COLOR_PINK then
				return true
			end
		end
	end

	return false
end

function EquipmentWGData:GetCESingleRemindByData(item_id, star)
	item_id = tonumber(item_id)
	local consume_cfg = self:GetComposeConsumeData(item_id, star)

	if IsEmptyTable(consume_cfg) then
		return false
	end

	local role_sex = RoleWGData.Instance:GetRoleSex()
	if (consume_cfg.need_sex ~= GameEnum.SEX_NOLIMIT and role_sex ~= consume_cfg.need_sex) then
		return false
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < item_cfg.limit_level then
		return false
	end

	local role_prof = RoleWGData.Instance:GetRoleProf()
	local prof_meet = item_cfg.limit_prof == role_prof or item_cfg.limit_prof == GameEnum.ROLE_PROF_NOLIMIT
	if not prof_meet then
		return false
	end

	-- 转职限制
	-- if item_cfg.is_limit_zhuanzhi_prof and item_cfg.is_limit_zhuanzhi_prof ~= 1 then
	-- 	local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	-- 	local mix_limit_prof = EquipWGData.GetEquipProfLimit(equip_index, item_cfg.order)
	-- 	local zhuanzhi_num = RoleWGData.Instance:GetZhuanZhiNumber()
	-- 	if mix_limit_prof > 0 and zhuanzhi_num < mix_limit_prof then
	-- 		return false
	-- 	end
	-- end

	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
	-- 需求：普通装备粉5阶不提醒
	-- if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL and item_cfg.color == GameEnum.ITEM_COLOR_PINK and item_cfg.order == 5 then
	-- 	return false
	-- end

	
	local fen_equip_special_limit = self:CheckFenEquipSpecialLimit(equip_body_index, item_cfg.order, item_cfg.color)
	if fen_equip_special_limit then
		return false
	end

	-- 新增提醒限制需求
	local is_special_limit = self:GetEquipComposeNeedSpecialLimit(equip_type, item_cfg.order, item_cfg.color)
	if is_special_limit then
		return false
	end

	local can_wear = EquipWGData.Instance:GetEquipCanWear(item_id)
	local part_limit_list = consume_cfg.part_limit_list
	local consume_condition = consume_cfg.equip_consume_list
	local must_meet_type_num = #consume_condition
	local must_meet_type_table = {}
	for i = 1, must_meet_type_num do
		local temp_data = consume_condition[i]
		local meet_num = 0
		for temp_k, temp_v in pairs(temp_data) do
			local data_list = ItemWGData.Instance:GetComposeEquipListByData(consume_cfg.need_order, temp_v.need_color, temp_v.need_star)
			if not IsEmptyTable(data_list) then
				if consume_cfg.need_sex_limit or consume_cfg.need_part_limit then
					for k, v in pairs(data_list) do
						if (not consume_cfg.need_sex_limit or (consume_cfg.need_sex_limit and item_cfg.limit_sex == v.limit_sex))
						and (not consume_cfg.need_part_limit or (consume_cfg.need_part_limit and part_limit_list[v.sub_type])) then
							meet_num = meet_num + 1
						end
					end
				else
					meet_num = meet_num + #data_list
				end
			end

			-- 目标装备能穿戴， 计算身上穿戴满足的
			if can_wear then
				local wear_meet = self:GetCEWearEquipCanUseByData(equip_body_index, consume_cfg.need_sex_limit,
						item_cfg.limit_sex, consume_cfg.need_order, temp_v.need_color, temp_v.need_star)
				if wear_meet then
					meet_num = meet_num + 1
				end
			end
		end
		must_meet_type_table[i] = meet_num
	end

	-- print_error("------must_meet_type_table------", must_meet_type_table)
	for i = 1, must_meet_type_num do
		if must_meet_type_table[i] < consume_cfg.remind_equip_num then
			return false
		end
	end

	for k, v in pairs(consume_cfg.stuff_cost_list) do
		local have_num = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id)
		if have_num < v.stuff_num then
			return false
		end
	end

	return true
end

-- 计算可用材料
function EquipmentWGData:GetCEEquipCanUseNumByData(item_id, star)
	item_id = tonumber(item_id)
	local consume_cfg = self:GetComposeConsumeData(item_id, star)
	if IsEmptyTable(consume_cfg) then
		return 0
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return 0
	end

	local can_wear = EquipWGData.Instance:GetEquipCanWear(item_id)
	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local part_limit_list = consume_cfg.part_limit_list
	local consume_condition = consume_cfg.equip_consume_list
	local must_meet_type_num = #consume_condition
	local must_meet_type_table = {}

	for i = 1, must_meet_type_num do
		local temp_data = consume_condition[i]
		local meet_num = 0
		for temp_k, temp_v in pairs(temp_data) do
			-- 背包满足的
			local data_list = ItemWGData.Instance:GetComposeEquipListByData(consume_cfg.need_order, temp_v.need_color, temp_v.need_star)
			if not IsEmptyTable(data_list) then
				if consume_cfg.need_sex_limit or consume_cfg.need_part_limit then
					for k, v in pairs(data_list) do
						if ((not consume_cfg.need_sex_limit) or (consume_cfg.need_sex_limit and item_cfg.limit_sex == v.limit_sex))
						and ((not consume_cfg.need_part_limit) or (consume_cfg.need_part_limit and part_limit_list[v.sub_type])) then
							meet_num = meet_num + 1
						end
					end
				else
					meet_num = meet_num + #data_list
				end
			end

			-- 目标装备能穿戴， 计算身上穿戴满足的
			if can_wear then
				local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
				local wear_meet = self:GetCEWearEquipCanUseByData(equip_body_index, consume_cfg.need_sex_limit,
						item_cfg.limit_sex, consume_cfg.need_order, temp_v.need_color, temp_v.need_star)
				if wear_meet then
					meet_num = meet_num + 1
				end
			end
		end
		must_meet_type_table[i] = meet_num
	end
	-------------------------------------------------------------------------
	--		每种材料需要件数: 1			  |			每种材料需要件数: 2
	-- A材料		B材料		显示可用材料数|	A材料			B材料		显示可用材料数
	-- 	1			0			1		|		1			0			1
	-- 	2			0			1		|		2			0			2
	-- 	2			1			3		|		2			2			4
	-- 	2			2			4		|		3			4			7
	-- 	7			2			5		|		3			7			7
	-------------------------------------------------------------------------
	local total_num = 0
	local remind_equip_num = consume_cfg.remind_equip_num	-- 每种材料需要件数
	if remind_equip_num > 0 then
		local min_can_compose_num = -1
		for i = 1, must_meet_type_num do
			local tmp_can_num = math.floor(must_meet_type_table[i] / remind_equip_num)
			if (min_can_compose_num < 0) or (tmp_can_num < min_can_compose_num) then
				min_can_compose_num = tmp_can_num
			end
		end

		for i = 1, must_meet_type_num do
			local need_num = min_can_compose_num * remind_equip_num
			local excess_num = must_meet_type_table[i] - need_num
			excess_num = excess_num < 0 and 0 or excess_num
			excess_num = excess_num > remind_equip_num and remind_equip_num or excess_num
			total_num = total_num + excess_num + need_num
		end
	end

	return total_num
end

function EquipmentWGData:GetCEWearEquipCanUseByData(equip_body_index, need_sex_limit, need_sex, need_order, need_color, need_star)
	local wear_data = EquipWGData.Instance:GetGridData(equip_body_index)
	if IsEmptyTable(wear_data) then
		return false
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(wear_data.item_id)
	if IsEmptyTable(item_cfg) then
		return false
	end

	-- 性别限制
	if need_sex_limit and item_cfg.limit_sex ~= need_sex then
		return false
	end

	local star = wear_data.param and wear_data.param.star_level or 0
	if need_order ~= item_cfg.order or need_color ~= item_cfg.color or star ~= need_star then
		return false
	end

	return true
end

function EquipmentWGData:GetEquinHechengItemData(compose_equip_best_attr_num, item_id)
	return self:GetEquipComposeCfgByID(item_id, compose_equip_best_attr_num)
end

--获取装备成功率
function EquipmentWGData:GetEquineSuccessProbability(item_id, legend_num, item_num)
	local compose_cfg = self:GetEquipComposeCfgByID(item_id, legend_num)

	local rate = 0
	if compose_cfg then
		local min_num = compose_cfg.min_consume_num
		local max_num = compose_cfg.max_consume_num
		item_num = item_num >= max_num and max_num or item_num
		if item_num < min_num then
			return 0
		end

		local can_add_num = item_num - min_num + 1
		local rate_list = Split(compose_cfg.succ_rate, ":")
		for k, v in ipairs(rate_list) do
			if k == can_add_num then
				rate = tonumber(v)
			end
		end
	end

	return rate
end

--获取灵宠粉色装备成功率
function EquipmentWGData:GetLingChongSuccessProbability(item_num)
	return item_num >= 1 and self.equip_success_probability[item_num + 2] or 0
end

--获取灵宠非粉色装备成功率
function EquipmentWGData:GetLingChongSuccessProbability2(item_num)
	return item_num >= 3 and self.equip_success_probability[item_num] or 0
end


function EquipmentWGData:GetEquineSuccessItemNum(item_id, attr_num)
	local compose_cfg = self:GetEquipComposeCfgByID(item_id, attr_num)

	local need_num = 5
	if compose_cfg then
		need_num = compose_cfg.max_consume_num or need_num
	end

	return need_num
end

--神兽写死三个
function EquipmentWGData:GetShenShouEquineSuccessItemNum()
	return 3
end

--天神写死三个
function EquipmentWGData:GetTSEquipSuccessItemNum()
	return 3
end

--灵宠写死三个(粉色3个其他5个)
function EquipmentWGData:GetLingChongEquineSuccessItemNum(is_fense)
	return is_fense and 3 or 5
end

--坐骑写死三个
function EquipmentWGData:GetMountEquineSuccessItemNum()
	return 5
end

--  "|" 划分单独需要满足要求， ":" 物品满足其一要求即可， "," 条件的分割
function EquipmentWGData:GetComposeConsumeList(compose_data)
	if IsEmptyTable(compose_data) then
		return {}
	end

	local item_id = compose_data.compose_equip_id
	local star = compose_data.compose_equip_best_attr_num
	local cache_meet_condition = self.cache_meet_condition[item_id] and self.cache_meet_condition[item_id][star]

	if not cache_meet_condition then
		cache_meet_condition = {}
		local need_list = Split(compose_data.consume_condition, "|")
		for k, meet in ipairs(need_list) do
			cache_meet_condition[k] = {}
			local need_child_list = Split(meet, ":")
			for i, child in ipairs(need_child_list) do
				local condi = Split(child, ",")
				cache_meet_condition[k][i] = {}
				cache_meet_condition[k][i].need_color = tonumber(condi[1])
				cache_meet_condition[k][i].need_star = tonumber(condi[2])
			end
		end

		self.cache_meet_condition[item_id] = self.cache_meet_condition[item_id] or {}
		self.cache_meet_condition[item_id][star] = cache_meet_condition
	end

	return cache_meet_condition
end

function EquipmentWGData.GetECPartLimitList(list_str)
	local list = {}
	if list_str == nil then
		return list
	end

	local str_list = Split(list_str, ",")
	for i, child in pairs(str_list) do
		local key = tonumber(child) or -1
		list[key] = true
	end

	return list
end

--获取可以合成的装备
--@is_one_key 一键添加/自动添加
function EquipmentWGData:GetHechengEquipmentItemList(demand_data, from_select_view, is_one_key)
	if not demand_data then
		return
	end

	local equip_id_cfg = ItemWGData.Instance:GetItemConfig(demand_data.compose_equip_id)
	if equip_id_cfg == nil then
		return
	end

	local can_wear = EquipWGData.Instance:GetEquipCanWear(demand_data.compose_equip_id)
	local equip_index = EquipWGData.Instance:GetEquipIndexByType(equip_id_cfg.sub_type) --获取装备部位

	local need_order = demand_data.need_order
	local need_sex_limit = demand_data.sex_limit > 0
	local need_part_limit = demand_data.part_limit > 0
	local max_need_num = demand_data.max_consume_num

	local can_hecheng_item = {}
	local bag_item_data = ItemWGData.Instance:GetBagItemDataList()
	local already_select_index = ComposeWGCtrl.Instance.view:GetEquipHeChengSacrificeList()

	--print_error("----配置条件----", demand_data.consume_condition)
	--  "|" 划分单独需要满足要求， ":" 物品满足其一要求即可， "," 条件的分割
	local temp_meet_condition = self:GetComposeConsumeList(demand_data)
	local cache_meet_condition = __TableCopy(temp_meet_condition)
	local part_limit_list = EquipmentWGData.GetECPartLimitList(demand_data.part_limit_range)

	-- print_error("----整理后----", cache_meet_condition)

	-- 剩余需满足
	local one_key_rest = {}
	if #cache_meet_condition >= 1 then
		local temp_flag_tab = {}	-- 使用标记
		for i = #cache_meet_condition, 1, -1 do
			local temp_data = cache_meet_condition[i]
			one_key_rest[i] = max_need_num / #temp_meet_condition
			for k, v in ipairs(temp_data) do
				for add_i, add_data in pairs(already_select_index) do
					if not temp_flag_tab[add_i] and add_data.color == v.need_color
						and add_data.star == v.need_star then
						temp_flag_tab[add_i] = true
						one_key_rest[i] = one_key_rest[i] - 1

						if one_key_rest[i] <= 0 then
							break
						end
					end
				end

				if one_key_rest[i] <= 0 then
					table.remove(one_key_rest, i)
					table.remove(cache_meet_condition, i)
					break
				end
			end
		end
	end
	-- print_error("----剔除后----", one_key_rest, cache_meet_condition)

	-- 目标装备能穿戴，获取玩家身上穿戴的
	if can_wear then
		local wear_meet = false
		local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(equip_id_cfg)

		if not IsEmptyTable(cache_meet_condition) then
			for list_idx, condi_list in ipairs(cache_meet_condition) do
				for condi_idx, condi in ipairs(condi_list) do

					wear_meet = self:GetCEWearEquipCanUseByData(equip_body_index, need_sex_limit,
							equip_id_cfg.limit_sex, need_order, condi.need_color, condi.need_star)
					if wear_meet then
						break
					end
				end

				if wear_meet then
					break
				end
			end
		end

		if wear_meet then
			can_hecheng_item[#can_hecheng_item + 1] = EquipWGData.Instance:GetGridData(equip_body_index)
		end
	end

	-- 背包中的
	for k, v in pairs(bag_item_data) do
		local item_config, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		local can_add_equip_list = true		--能否加入列表
		if item_config and item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			-- 印记剔除
			if v.param and v.param.impression > 0 then
				can_add_equip_list = false
			end

			-- 阶数限制
			if can_add_equip_list then
				can_add_equip_list = item_config.order == need_order
			end

			-- 性别限制
			if can_add_equip_list and need_sex_limit then
				can_add_equip_list = item_config.limit_sex == equip_id_cfg.limit_sex
			end

			-- 部位限制
			if can_add_equip_list and need_part_limit then
				can_add_equip_list = part_limit_list[item_config.sub_type]
			end

			-- 极品属性限制 和 颜色限制
			if can_add_equip_list then
				can_add_equip_list = false
				if not IsEmptyTable(cache_meet_condition) then
					local item_color = item_config.color
					local item_star = v.param and v.param.star_level or 0
					for list_idx, condi_list in ipairs(cache_meet_condition) do
						for condi_idx, condi in ipairs(condi_list) do
							if condi.need_color == item_color and condi.need_star == item_star then
								can_add_equip_list = true
								break
							end
						end
						if can_add_equip_list then
							break
						end
					end
				end
			end

			if can_add_equip_list then
				can_hecheng_item[#can_hecheng_item + 1] = v
			end
		end
	end

	-- 已添加移除
	local is_wear
	for i = #can_hecheng_item, 1, -1 do
		for _, v in ipairs(already_select_index) do
			local data = can_hecheng_item[i]
			is_wear = data.frombody and 1 or 0
			if is_wear == v.is_wear and data.index == v.index then
				table.remove(can_hecheng_item, i)
				break
			end
		end
	end

	-- 一键操作
	if is_one_key then
		local rest_need_num = max_need_num - #already_select_index
		local one_key_list = {}

		if rest_need_num > 0 then
			for k, v in ipairs(can_hecheng_item) do
				local can_add_equip_list = false
				local item_config, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
				local item_color = item_config.color
				local item_star = v.param and v.param.star_level or 0

				for i = #cache_meet_condition, 1, -1 do
					if one_key_rest[i] and one_key_rest[i] > 0 then
						local temp_data = cache_meet_condition[i]
						for condi_idx, condi in ipairs(temp_data) do
							if condi.need_color == item_color and condi.need_star == item_star then
								one_key_rest[i] = one_key_rest[i] - 1
								can_add_equip_list = true
								break
							end
						end

						if can_add_equip_list then
							break
						end
					end
				end

				if can_add_equip_list then
					rest_need_num = rest_need_num - 1
					table.insert(one_key_list, v)
				end

				if rest_need_num <= 0 then
					break
				end
			end
		end

		return one_key_list
	end

	return can_hecheng_item
end

function EquipmentWGData:GetHechengSpecialCfg(item_id, legend_num)
	for k,v in ipairs(self.equipforge_cfg.jz_sz_compose)do
		if v.equip_id == item_id and v.new_star_num == legend_num then
			return v
		end
	end
end

function EquipmentWGData:GetHechengSpeCfg(item_id, legend_num)
	for k,v in ipairs(self.equipforge_cfg.jz_sz_compose)do
		if v.equip_id == item_id and v.new_star_num == legend_num then
			return v
		end
	end
end

function EquipmentWGData:GetSSEquipHeChengAccordionDataList()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local data_list = {}
	for k, v in ipairs(self.shenshou_equip_cfg.equip_exchange) do
		if role_level >= v.level then
			if data_list[v.type] == nil then
				data_list[v.type] = {}
			end
			table.insert(data_list[v.type], v)
		end
	end

	local acc_data_list = {}
	for k, child_list in pairs(data_list) do
		local item_data = {}
		for k,v in ipairs(child_list) do
			table.insert(item_data, v)
		end

		table.sort(item_data, SortTools.KeyLowerSorters("compose_equip_best_attr_num"))
		table.insert(acc_data_list, {child = item_data, star_level = item_data[1].compose_equip_best_attr_num, name_type = item_data[1].type})
	end

	table.sort(acc_data_list, SortTools.KeyLowerSorters("name_type"))
	return acc_data_list
end

-- 天神
function EquipmentWGData:GetTSEquipHeChengAccordionDataList()
	local data_list = {}
	local cfg = {}

	for k, v in pairs(self.tianshen_cfg.shenshi_com) do
		cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if data_list[cfg.color -1] == nil then
			data_list[cfg.color -1] = {}
		end
		local compose_info = {}
		compose_info.product_id = v.item_id
		compose_info.stuff_id_1 = v.cost_item_id
		compose_info.stuff_count_1 = v.cost
		compose_info.color = cfg.color
		compose_info.is_shenshi = true
		table.insert(data_list[cfg.color -1], compose_info)
	end


	local acc_data_list ={}
	for k, child_list in pairs(data_list) do
		table.sort(child_list, SortTools.KeyLowerSorter("product_id"))
		table.insert(acc_data_list, {child = child_list, star_level = child_list[1].color, name_type = child_list[1].color})
	end
	table.sort(acc_data_list, SortTools.KeyLowerSorters("name_type"))
	return acc_data_list
end


--剑客
function EquipmentWGData:InitLingChongHeChengShowData()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local data_list = {}
	for k, v in ipairs(self.compose_zhuzhan_cfg.compose_zhuzhanjiemian) do
		if role_level >= v.level then								--根据角色来 只保留自己角色的配表
			if data_list[v.type * 100 + v.compose_equip_best_attr_num] == nil then
				data_list[v.type * 100 + v.compose_equip_best_attr_num] = {}
			end
			table.insert(data_list[v.type * 100 + v.compose_equip_best_attr_num], v)
		end
	end
	for k,v in pairs(data_list) do
		table.sort(v, SortTools.KeyUpperSorters("color")) 			--坐骑在上面   KeyLowerSorter
	end
	return data_list
end

--获取装备合成可折叠的数据列表
function EquipmentWGData:GetLingChongEquipHeChengAccordionDataList()
	local acc_data_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cur_data = self:InitLingChongHeChengShowData()

	for k, child_list in pairs(cur_data) do
		local item_data = {}
		for k,v in ipairs(child_list) do
			table.insert(item_data, v)
		end
		if next(item_data) ~= nil then
			table.insert(acc_data_list, {child = item_data, star_level = item_data[1].compose_equip_best_attr_num,
						name_type = item_data[1].type,pet_flag = item_data[1].pet_flag,color = item_data[1].color})
		end
	end

	table.sort(acc_data_list, SortTools.KeyLowerSorters("star_level", "name_type","color"))
	--print_error("acc_data_list",acc_data_list)
	return acc_data_list
end

function EquipmentWGData:GetMountEquipHeChengAccordionDataList()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local data_list = {}
	for k, v in ipairs(self.mount_equip_cfg.equip_exchange) do
		if role_level >= v.level then
			local key = v.type * 100 + v.compose_equip_best_attr_num
			if data_list[key] == nil then
				data_list[key] = {}
			end
			table.insert(data_list[key], v)
		end
	end

	local acc_data_list = {}
	for k, child_list in pairs(data_list)do
		local item_data = {}
		for k,v in ipairs(child_list)do
			table.insert(item_data, v)
		end
		table.insert(acc_data_list, {child = item_data, star_level = item_data[1].compose_equip_best_attr_num, name_type = item_data[1].type})
	end

	return acc_data_list
end

function EquipmentWGData:InitEquipSSHeChengMakeData()
	local data_list = {}
	for k, v in ipairs(self.shenshou_equip_cfg.compose_cfg) do
		data_list[v.give_start_num * 10000 + v.v_item_id] = v
	end
	return data_list
end

function EquipmentWGData:InitEquipTSHeChengMakeData()
	local data_list = {}
	for k, v in ipairs(self.tianshen_cfg.shenshi_com) do
		data_list[10000 + v.item_id] = v
	end
	return data_list
end
function EquipmentWGData:InitEquipLingChongHeChengMakeData()
	local data_list = {}
	for k, v in ipairs(self.compose_zhuzhan_cfg.compose_zhuzhan) do
		data_list[v.give_start_num * 10000 + v.v_item_id] = v
	end
	return data_list
end
function EquipmentWGData:InitEquipMountHeChengMakeData()
	local data_list = {}
	for k, v in ipairs(self.mount_equip_cfg.compose_cfg) do
		data_list[v.give_start_num * 10000 + v.v_item_id] = v
	end
	return data_list
end

function EquipmentWGData:GetComposeByVItemId(v_item_id)
	local compose_cfg = self.shenshou_equip_cfg.compose_cfg
	for k, v in ipairs(compose_cfg) do
		if v_item_id == v.v_item_id then
			return v
		end
	end
	return nil
end

function EquipmentWGData:GetComposeByVItemIdAndStar(v_item_id,star)
	local compose_cfg = self.shenshou_equip_cfg.compose_cfg
	for k, v in ipairs(compose_cfg) do
		if v_item_id == v.v_item_id and v.give_start_num == star then
			return v
		end
	end
	return nil
end

-- 获取天神装备合成配置
function EquipmentWGData:GetTSComposeByItemId(item_id)
	for k,v in pairs(self.tianshen_cfg.equip_com) do
		if item_id == v.item_id then
			return v
		end
	end
	return nil
end

function EquipmentWGData:GetLingChongComposeByVItemId(v_item_id)
	local compose_cfg = self.compose_zhuzhan_cfg.compose_zhuzhan
	for k, v in ipairs(compose_cfg) do
		if v_item_id == v.v_item_id then
			return v
		end
	end
	return nil
end
function EquipmentWGData:GetMountComposeByVItemId(v_item_id)
	local compose_cfg = self.mount_equip_cfg.compose_cfg
	for k, v in ipairs(compose_cfg) do
		if v_item_id == v.v_item_id then
			return v
		end
	end
	return nil
end

function EquipmentWGData:GetSSEquinHechengItemData(give_start_num, item_id)
	-- print_error("self.equip_sshecheng_make:::",self.equip_sshecheng_make)
	return self.equip_sshecheng_make[give_start_num * 10000 + item_id]
end
function EquipmentWGData:GetTSEquinHechengItemData(give_start_num, item_id)
	return self.equip_ts_hecheng_make[give_start_num * 10000 + item_id]
end
function EquipmentWGData:GetLingChongEquinHechengItemData(give_start_num, item_id)
	-- print_error("self.equip_sshecheng_make:::",self.equip_sshecheng_make)
	return self.equip_lingchong_make[give_start_num * 10000 + item_id]
end
function EquipmentWGData:GetMountEquinHechengItemData(give_start_num, item_id)
	return self.equip_mount_make[give_start_num * 10000 + item_id]
end

function EquipmentWGData:GetSSHechengEquipmentItemList(demand_data)
	local need_start_num = demand_data.need_start_num
	local need_qualit = demand_data.need_qualit  --神兽装备 需要-1
	--print_error("*****************1",need_start_num,need_qualit)
	local can_hecheng_item = ShenShouWGData.Instance:FilterShenShouEq(need_qualit, need_start_num)
	--print_error("*****************2",can_hecheng_item)
	local already_select_index = ComposeWGCtrl.Instance.view:GetEquipHeChengSacrificeList()

	for i = #can_hecheng_item, 1, -1 do
		for _, v in ipairs(already_select_index) do
			if can_hecheng_item[i].index == v.index then
				table.remove(can_hecheng_item, i)
				break
			end
		end
	end
	return can_hecheng_item
end

function EquipmentWGData:GetTSHechengEquipmentItemList(demand_data)
	local need_start_num = demand_data.need_star
	local need_qualit = demand_data.need_color  --神兽装备 需要-1
	-- print_error("*****************1",need_start_num,need_qualit)

	local can_hecheng_item = TianShenWGData.Instance:FilterTSEq(need_qualit, need_start_num)
	-- print_error("*****************2",can_hecheng_item)
	local already_select_index = ComposeWGCtrl.Instance.view:GetEquipHeChengSacrificeList()

	for i = #can_hecheng_item, 1, -1 do
		for _, v in ipairs(already_select_index) do
			if can_hecheng_item[i].index == v.index then
				table.remove(can_hecheng_item, i)
				break
			end
		end
	end
	return can_hecheng_item
end

function EquipmentWGData:GetMountHechengEquipmentItemList(demand_data)
	local need_start_num = demand_data.need_start_num
	local need_qualit = demand_data.need_qualit

	local can_hecheng_item = ShenShouWGData.Instance:FilterShenShouEq(need_qualit, need_start_num)

	local already_select_index = ComposeWGCtrl.Instance.view:GetEquipHeChengSacrificeList()

	for i = #can_hecheng_item, 1, -1 do
		for _, v in ipairs(already_select_index) do
			if can_hecheng_item[i].index == v.index then
				table.remove(can_hecheng_item, i)
				break
			end
		end
	end
	return can_hecheng_item
end
-----------------------至尊装备--------------------------------------------------
--获取至尊装备配置
function EquipmentWGData:GetZhiZunEquipCfg(item_id)
	for i ,v in pairs(self.equipforge_cfg.equip_zhizun) do
		if item_id == v.compose_equip_id then
			return v
		end
	end
	return nil
end
--两数组相加
function EquipmentWGData:TableAdd(tab1,tab2)

	local list = {}
	for i,v in pairs(tab1) do
		table.insert(list,v)
	end
	for i,v in pairs(tab2) do
		v.is_role_equip = 10000 --装备在角色身上
	end
	for i,v in pairs(tab2) do
		table.insert(list,v)
	end
	return list
end

--获取可以合成的装备(至尊装备)
function EquipmentWGData:GetHechengZhiZunEquipmentItemList(demand_data)
	local bag_item_data = ItemWGData.Instance:GetBagItemDataList() --获取背包所有物品
	local waer_equip_list = __TableCopy(EquipWGData.Instance:GetDataList()) --获取角色身上的装备
	local item_list = __TableCopy(self:TableAdd(bag_item_data, waer_equip_list))
	local equip_list = {}
	local select_index = 1 --记录选择的第几件装备
	for i=1,#item_list do
		if item_list[i].item_id == demand_data.equip1_id or item_list[i].item_id == demand_data.equip2_id then
			if item_list[i].param.star_level == demand_data.compound_number then
				item_list[i].select_index = select_index
				table.insert(equip_list, item_list[i])
				select_index = select_index + 1
			end
		end
	end
	--local hecheng_equip_id = EquipmentWGCtrl.Instance.view:GetHeChengEquipId()
	local hecheng_equip_id = ComposeWGCtrl.Instance.view:GetHeChengEquipId()
	for i, v1 in pairs(equip_list) do
		for _, v2 in ipairs(hecheng_equip_id) do
			if v1.select_index == v2 then
				table.remove(equip_list, v2)
				break
			end
		end
	end
	return equip_list
end

--获取至尊套装部位
function EquipmentWGData:GetZhiZunEquipNum(item_id,item_order)
	local bag_item_data = ItemWGData.Instance:GetBagItemDataList() --获取背包所有物品
	local waer_equip_list = __TableCopy(EquipWGData.Instance:GetDataList()) --获取角色身上的装备
	local item_list = __TableCopy(self:TableAdd(bag_item_data, waer_equip_list))
	local equip_num = 0 --记录至尊装备不同部位开启数量
	local wuqi_count = 0 --记录数量只进一次
	local xianfu_count = 0 --记录数量只进一次
	-- print_error("item_list====/",bag_item_data,waer_equip_list)
	for i,v in pairs(item_list) do
		-- print_error(v.item_id)
		if v and v.item_id >0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg.is_zhizun == 1 then
				if item_cfg.sub_type == GameEnum.EQUIP_TYPE_WUQI and item_cfg.order == item_order then
					if wuqi_count == 0 then
						equip_num = equip_num + 1
						wuqi_count = wuqi_count + 1
					end
				elseif item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIANFU and item_cfg.order == item_order then
					if xianfu_count == 0 then
						equip_num = equip_num + 1
						xianfu_count = xianfu_count + 1
					end
				end
			end
		end

	end
	return equip_num
end

--获取绝版属性
function EquipmentWGData:GetJueBanAttr(equip_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(equip_id)
	if item_cfg.is_zhizun == 1 then
		local list_1 = self.zhizun_equip_cfg.equip_active
		local list_2 = self.zhizun_equip_cfg.other
		local role_level = RoleWGData.Instance.role_vo.level
		for i,v in pairs(list_1) do
			if item_cfg.id == v.equip_id then
				if role_level > list_2[1].max_level then
					role_level = list_2[1].max_level
				end
				if v.param1 ~= 0 then
					return role_level * v.param1
				else
					return role_level * v.param2
				end
			end
		end
	else
		return nil
	end
end
--获取至尊装备属性值
function EquipmentWGData:GetZhiZunEquipAtrrValue(item_order)
	local list = self.zhizun_equip_cfg.suit_effect
	for i,v in pairs(list) do
		if v.equip_grade == item_order then
			return v.pofang_per
		end
	end
	return 0
end
--获取至尊装备绝版属性的最大值
function EquipmentWGData:GetJueBanAttrMaxValue(equip_id)
	local list_1 = self.zhizun_equip_cfg.equip_active
	local max_level = self.zhizun_equip_cfg.other[1].max_level
	for i,v in pairs(list_1) do
		if equip_id == v.equip_id then
			if v.param1 ~= 0 then
				return 	max_level * v.param1
			else
				return 	max_level * v.param2
			end
		end
 	end
end

function EquipmentWGData:GetEquipComposeCfgByID(item_id, best_attr_num)
	item_id = tonumber(item_id)
	best_attr_num = tonumber(best_attr_num)

	return self.equip_compose_to_map[item_id] and self.equip_compose_to_map[item_id][best_attr_num]
end

function EquipmentWGData:GetEquipDeComposeDescByItemid(item_id, star_level, is_come_form_equip_tihuan)
	local desc = ""
	local decompose_cfg = self:GetEquipDecomposeByID(item_id, star_level)
	if not decompose_cfg then
		return desc
	end

	local split_str
	local is_fixed = false
	local return_star = 0
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local fixed_equip_id = decompose_cfg.fixed_equip_id
	local put_equip_id = decompose_cfg["put_equip_id_" .. role_prof]

	if fixed_equip_id ~= 0 then
		is_fixed = true
		return_star = decompose_cfg.fixed_best_attr_count
		split_str = string.split(fixed_equip_id, "|")
	elseif put_equip_id and put_equip_id ~= 0 then
		return_star = decompose_cfg.put_best_attr_count
		split_str = string.split(put_equip_id, "|")
	end

	if IsEmptyTable(split_str) then
		return desc
	end

	local tmp_split = is_fixed and split_str or {split_str[1]}
	local function get_desc(tmp_id, star, is_fixed)
		local tmp_desc = ""
		local equip_cfg = ItemWGData.Instance:GetItemConfig(tmp_id)
		if not equip_cfg then
			return tmp_desc
		end
		star = star or 0
		local euip_color = equip_cfg.color
		local euip_order = equip_cfg.order
		local euip_name = equip_cfg.name
		local desc_color = ITEM_COLOR[euip_color] or ITEM_COLOR[0]
		local daxie_color = Language.Card.ColorName[euip_color] or Language.Card.ColorName[0]
		if is_fixed then
			tmp_desc = string.format(Language.F2Tip.EquipStarDesc, desc_color, star, euip_order, euip_name) .. " * 1"
		else
			local get_num_str = " * " .. decompose_cfg.put_equip_num
			tmp_desc = string.format(Language.F2Tip.EquipStarDesc2, desc_color, daxie_color, star, euip_order, euip_name)
			tmp_desc = tmp_desc .. get_num_str
		end

		return tmp_desc
	end

	local return_equip_str = ""
	for k, v in ipairs(tmp_split) do
		local tmp_id = tonumber(v)
		local douhao_str = k == #tmp_split and "" or ",\n"
		local temp_desc = get_desc(tmp_id, return_star, is_fixed)
		return_equip_str = return_equip_str .. temp_desc .. douhao_str
	end

	local return_stuff_str = ""
	for i = 1, 2 do
		local stuff_cfg = ItemWGData.Instance:GetItemConfig(decompose_cfg["stuff".. i .. "_id"])
		local stuff_num = decompose_cfg["stuff".. i .. "_num"]
		if stuff_cfg and stuff_num > 0 then
			local stuff_color = ITEM_COLOR[stuff_cfg.color] or ITEM_COLOR[0]
			local stuff_name = ToColorStr(stuff_cfg.name, stuff_color)
			local get_num_str = " * " .. stuff_num
			return_stuff_str = return_stuff_str .. ",\n" .. stuff_name .. get_num_str
		end
	end

	if is_come_form_equip_tihuan then
		local item_cfg, _ = ItemWGData.Instance:GetItemConfig(item_id)
		desc = string.format(Language.Common.EquipDecompose1, ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]), return_equip_str, return_stuff_str)
	else
		desc = string.format(Language.Common.EquipDecompose2, return_equip_str, return_stuff_str)
	end

	return desc
end

function EquipmentWGData:GetEquipTipsShowComposeData(order, color, star)
	if not self.equip_tips_compose_show_cfg then
		local cfg = self.equipforge_cfg.tips_compose_show
		if not IsEmptyTable(cfg) then
			for k, v in ipairs(cfg) do
				local temp_list = {}
				local equip_list = {}
				for i = 1, 3 do
					local equip_str = v["equip_" .. i] or ""
					local show_list = Split(equip_str, ",")
					local color = tonumber(show_list[1])
					local star = tonumber(show_list[2])
					if color and star then
						equip_list[#equip_list + 1] = {color = color, star = star}
					end
				end

				temp_list.show_add = v.show_add == 1
				temp_list.equip_list = equip_list
				self.equip_tips_compose_show_cfg = self.equip_tips_compose_show_cfg or {}
				self.equip_tips_compose_show_cfg[v.order] = self.equip_tips_compose_show_cfg[v.order] or {}
				self.equip_tips_compose_show_cfg[v.order][v.color] = self.equip_tips_compose_show_cfg[v.order][v.color] or {}
				self.equip_tips_compose_show_cfg[v.order][v.color][v.star] = temp_list
			end
		else
			self.equip_tips_compose_show_cfg = {}
		end
	end

	local empty_table = {}
	return (((self.equip_tips_compose_show_cfg or empty_table)[order] or empty_table)[color] or empty_table)[star]
end

-- 需求：当已穿戴装备有可合成提升时，加入变强列表
function EquipmentWGData:GetWearEquipComposeRemind(cur_type)
	local wear_remind, type, star_level, order = false, 0, 0, 0
	local wear_list = EquipWGData.Instance:GetDataList()
	if IsEmptyTable(wear_list) then
		return wear_remind, type, star_level, order
	end

	local is_jewelry = cur_type == EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY
	for i = 0, #wear_list do
		local data = wear_list[i]
		if data and data.item_id > 0 then
			if is_jewelry then
				if data.index == GameEnum.EQUIP_INDEX_XIANLIAN or data.index == GameEnum.EQUIP_INDEX_XIANZHUI then
					wear_remind, type, star_level, order = self:GetSingleWearEquipComposeRemind(cur_type, data)
					if wear_remind then
						return wear_remind, type, star_level, order
					end
				end
			else
				local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(data.index)
				if equip_type ~= GameEnum.EQUIP_BIG_TYPE_XIANQI then
					wear_remind, type, star_level, order = self:GetSingleWearEquipComposeRemind(cur_type, data)
					if wear_remind then
						return wear_remind, type, star_level, order
					end
				end
			end
		end
	end

	return wear_remind, type, star_level, order
end

-- 【职业改动修改】
-- 合成展示界面无规律对照表
-- 装备部位对应槽位索引， 武器根据职业获取对应槽位索引
local EQUIP_COMPOSE_SHOW_CAO_LIST = {
	[0] = 6, [1] = 7, [2] = 8,
	[3] = 13, [4] = 9, [6] = 12,
	[7] = 5,
	[15] = 1, [25] = 3, [35] = 2, [45] = 4,
}
function EquipmentWGData:GetSingleWearEquipComposeRemind(cur_type, item_data)
	local remind, type, star_level, order = false, 0, 0, 0
	if item_data == nil then
		return remind, type, star_level, order
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if not item_cfg or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return remind, type, star_level, order
	end

	local cur_order = item_cfg.order
	local cur_color = item_cfg.color
	local legend_num = item_data.param and item_data.param.star_level or 0
	local max_order, min_order = self:GetEquinHeChengShowOrder()
	if min_order > cur_order or cur_order > max_order or legend_num < 2 then
		return remind, type, star_level, order
	end

	local part_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	if part_index == GameEnum.EQUIP_INDEX_XIANJIE or part_index == GameEnum.EQUIP_INDEX_XIANZHUO
	or part_index > GameEnum.EQUIP_INDEX_XIANJIE then
		return remind, type, star_level, order
	end

	local role_sex, role_prof = RoleWGData.Instance:GetRoleSexProf()
	local show_list = self.equip_show_compose_to_map[cur_type]
	if IsEmptyTable(show_list) then
		return remind, type, star_level, order
	end

	-- 找到身上穿戴的能合成什么，并且合成目标是否材料足够
	local cao_index = EQUIP_COMPOSE_SHOW_CAO_LIST[part_index] or 0
	if part_index == GameEnum.EQUIP_INDEX_WUQI then
		local key = role_prof * 10 + part_index
		cao_index = EQUIP_COMPOSE_SHOW_CAO_LIST[key] or 0
	end

	local cao_key = string.format("cao%s", cao_index)
	local find_flag = false

	for k, v in ipairs(show_list) do
		if cur_order == v.order and cur_color <= v.type + 2 and legend_num <= v.compose_equip_best_attr_num then
			local compose_cfg = self:GetEquipComposeCfgByID(v[cao_key], v.compose_equip_best_attr_num)
            local consume_condition = self:GetComposeConsumeList(compose_cfg)
			for i = #consume_condition, 1, -1 do
                local temp_data = consume_condition[i]
				for temp_k, temp_v in ipairs(temp_data) do
					if cur_color == temp_v.need_color and legend_num == temp_v.need_star then
						remind = self:GetCESingleRemindByData(v[cao_key], v.compose_equip_best_attr_num)
						if remind then
							return true, v.type, v.compose_equip_best_attr_num, v.order
						end

						find_flag = true
						break
					end
				end
			end
		end

		if find_flag then
			break
		end
	end

	return remind, type, star_level, order
end

function EquipmentWGData:GetShenShouAllComposeCfg()
	return self.shenshou_equip_cfg.compose_cfg or {}
end
