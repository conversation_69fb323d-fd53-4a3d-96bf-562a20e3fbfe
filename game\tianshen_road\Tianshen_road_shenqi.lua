
local temp_pos = Vector2(0, 0)

function TianshenRoadView:InitShenQiView()
	self.sq_const_item_id = 0

	XUI.AddClickEventListener(self.node_list.btn_pre_show, BindTool.Bind(self.PreShow,self, false))
	XUI.AddClickEventListener(self.node_list.btn_hide, BindTool.Bind(self.PreShow,self, true))
	XUI.AddClickEventListener(self.node_list.btn_show_task, BindTool.Bind(self.ShowTask,self))

	self.select_jigsaw = nil
	-- 当前被拖拽
	self.dragged_item = nil
	-- 当前拖拽temp
	self.drag_temp_item = TianShenLoadBXItem.New(self.node_list.drag_laygout)
	self.drag_temp_item:SetVisible(false)
	self.is_can_drag = false
	self.drag_final_pos = nil
	self.is_pre_show = false

	self:InitAwardList()
	self:InitShenQiBoxList()
	self:SQTimeCountDown()
	self:FlushSQView()
end

function TianshenRoadView:ReleaseShenQiView()
	self.dragged_item = nil
	if self.drag_temp_item then
		self.drag_temp_item:DeleteMe()
		self.drag_temp_item = nil
	end

	if self.award_list then
		for k,v in pairs(self.award_list) do
			v:DeleteMe()
		end
		self.award_list = nil
	end

	if self.bx_list then
		for k,v in pairs(self.bx_list) do
			v:DeleteMe()
		end
		self.bx_list = nil
	end

	self.select_jigsaw = nil

	if self.ts_sq_tween_yoyo then
		self.ts_sq_tween_yoyo:Kill()
		self.ts_sq_tween_yoyo = nil
    end

    if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	CountDownManager.Instance:RemoveCountDown("tianshenroad_shenqi_count_down")
end

function TianshenRoadView:InitAwardList()
	local award_list = {}
	local gift_list = TianshenRoadWGData.Instance:GetSQRewardCfg()
	local bx_root = self.node_list.award_list_view
	if gift_list then
		for i=1,#gift_list do
			local item = TianShenLoadJigsawAwardItem.New(bx_root)
			item:SetData(gift_list[i])
			item:SetIndex(i)
			award_list[i] = item
		end
	end
	self.award_list = award_list
end

function TianshenRoadView:InitShenQiBoxList()
	-- 预览
	self.pre_show_list = {}
	local bx_list = {}
	local jigsaw_list = TianshenRoadWGData.Instance:GetJigsawList()
	local bx_root = self.node_list.sq_score_layout
	if jigsaw_list then
		for i=1,#jigsaw_list do
			local item = TianShenLoadBXItem.New(bx_root)

			local evt_trigger_listener = item.node_list["drag_evt"].event_trigger_listener
			evt_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnClickDown, self, i))
			evt_trigger_listener:AddBeginDragListener(BindTool.Bind(self.OnBeginDrag, self, i))
			evt_trigger_listener:AddDragListener(BindTool.Bind(self.OnDrag, self, i))
			evt_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnEndDrag, self, i))
			evt_trigger_listener:AddCancelListener(BindTool.Bind(self.OnEndDrag, self, i))
			
			item:SetData(jigsaw_list[i])
			item:SetIndex(i)
			bx_list[i] = item

			self.pre_show_list[i] = {id = i - 1}
		end
	end
	self.bx_list = bx_list
end

function TianshenRoadView:TRSQShowIndexCallBack()
	self:DoTRSQAnim()
end

--更新界面数据
function TianshenRoadView:FlushSQView()
	self:FlushScore()
	self:FlushJigsaw()

	self.node_list.btn_show_task_red:CustomSetActive(TianshenRoadWGData.Instance:IsShenQiTaskRed() == 1)
end

function TianshenRoadView:FlushScore()
	local score = TianshenRoadWGData.Instance:GetDangweiScore()
	self.node_list.sorce_txt.text.text = score
	
	local cur_progress = TianshenRoadWGData.Instance:CalcIrregularProgress(score)
	if cur_progress > 0 then
		self.node_list.exp_slider.slider.value = cur_progress
	else
		self.node_list.exp_slider.slider.value = 0
	end

	if self.award_list then
		for k,v in pairs(self.award_list) do
			v:Flush()
		end
	end
end

function TianshenRoadView:FlushJigsaw(list)
	if self.bx_list then
		local jigsaw_list = list or TianshenRoadWGData.Instance:GetJigsawList()
		for k,v in pairs(self.bx_list) do
			local data = jigsaw_list[k]
			if data then
				v:SetData(data)
			end
		end
	end
end

function TianshenRoadView:OnSQBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = TianshenRoadWGData.Instance:GetActivityTip(TabIndex.tianshenroad_shenqi)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end   

--有效时间倒计时
function TianshenRoadView:SQTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_shenqi_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_shenqi)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.sq_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("tianshenroad_shenqi_count_down", BindTool.Bind1(self.UpdateSQCountDown, self), BindTool.Bind1(self.SQTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.sq_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.sq_time_label.text.color = Str2C3b(COLOR3B.RED)
	end
end

function TianshenRoadView:UpdateSQCountDown(elapse_time, total_time)
	self.node_list.sq_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

function TianshenRoadView:DoTRSQAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["tssq_tween_root"])

    RectTransform.SetAnchoredPositionXY(self.node_list["tssq_bottom"].rect, 0, -500)
   
    self.node_list["tssq_bottom"].rect:DOAnchorPos(Vector2(0, -265), tween_info.MoveTime)
    UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["tssq_tween_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

-- 按下
function TianshenRoadView:OnClickDown(index)
	if self.is_pre_show then -- 预览不给操作
		return
	end
	self.drag_final_pos = nil
	-- self.is_can_drag = true
	self.is_can_drag = TianshenRoadWGData.Instance:CanDrag(index)
	if not self.is_can_drag then
		if TianshenRoadWGData.Instance:IsRightJigsaw(index) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenRoad.RightJigsaw)
		end
		return
	end
	self.dragged_item = self.bx_list and self.bx_list[index]
end

-- 开始拖动，开始显示拖动icon
function TianshenRoadView:OnBeginDrag(index)
	if not self.is_can_drag or not self.dragged_item then
		return
	end
	self.drag_temp_item:SetVisible(true)
	self.dragged_item:CustomSetActive(false)
	self.drag_temp_item:SetData(self.dragged_item:GetData())
end

-- 拖拽中
function TianshenRoadView:OnDrag(index, eventData)
	if not self.is_can_drag then
		return
	end

	local _, pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list.sq_score_layout.rect, eventData.position, UICamera, temp_pos)
	
	self.drag_temp_item.node_list.drag_evt.transform.localPosition = pos
	self.drag_final_pos = pos
end

-- 抬起/取消
function TianshenRoadView:OnEndDrag(index)
	if not self.is_can_drag or not self.dragged_item then
		return
	end
	local taget_index = index -- 点击
	if self.drag_final_pos then
		taget_index = self:CheckPos(self.drag_final_pos)
	end
	local taget = self.bx_list[taget_index]
	local is_right = TianshenRoadWGData.Instance:IsRightJigsaw(taget_index)
	if not is_right and taget then
		self:SetSelectJigsaw(false)

		local drag_data = not self.drag_temp_item:IsEmpty() and self.drag_temp_item:GetData()
		taget_index = taget_index - 1
		if drag_data and taget_index ~= drag_data.index then
			local temp_data = taget:GetData()
			temp_data.index = drag_data.index
			drag_data.index = taget_index
			taget:SetData(drag_data)
			self.dragged_item:SetData(temp_data)
			QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_WANT_SHENQI, WOYAOSHENQI_OP_TYPE.WOYAOSHENQI_CHANGE_JIGSAW, drag_data.index, temp_data.index)
		
			if drag_data.index == drag_data.id then
				taget:ShowEffect()
				self:ShowGuide(false)
			end

			if temp_data.index == temp_data.id then
				self.dragged_item:ShowEffect()
			end
		end

		self.select_jigsaw = taget
		self:SetSelectJigsaw(true)
	end

	self.drag_temp_item:SetVisible(false)
	self.drag_temp_item:SetEmpty()
	self.dragged_item:CustomSetActive(true)
	self.dragged_item = nil
end

function TianshenRoadView:CheckPos(pos)
	if self.bx_list then
		local range_x = 72
		local range_y = 63
		for i = 1, #self.bx_list do
			local taget = self.bx_list[i]
			local taget_pos = taget.node_list.drag_evt.transform.localPosition
			if math.abs(taget_pos.x - pos.x) < range_x and math.abs(taget_pos.y - pos.y) < range_y then
				return i
			end
		end
	end

	return -1
end

function TianshenRoadView:SetSelectJigsaw(status)
	if self.select_jigsaw then
		self.select_jigsaw:SetSelect(status)
	end
end

function TianshenRoadView:PreShow(status)
	self.is_pre_show = status
	self.node_list.btn_pre_show:CustomSetActive(status)
	self.node_list.btn_hide:CustomSetActive(not status)
	if status then
		self:SetSelectJigsaw(false)
		self:FlushJigsaw(self.pre_show_list)
	else
		self:FlushJigsaw()
		self:SetSelectJigsaw(true)
	end
end

function TianshenRoadView:ShowTask()
	ViewManager.Instance:Open(GuideModuleName.TianshenRoadJigsawTask)
end

-- 引导特殊处理
function TianshenRoadView:ShowGuide(status)
	if self.node_list.guide_node then
		self.node_list.guide_node:CustomSetActive(status)
	end
end