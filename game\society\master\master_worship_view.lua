----------------------------------  师徒--供奉 -----------------------
function SocietyView:InitWorshipView()
	self.rewardlist = {}
	MasterWGCtrl.Instance:SendShituInfo()

	self.huoyue_progressbar = self.node_list["prog_huoyue_jingyan"]
	self.huoyue_progressbar.slider.value = 0
	self.def_select_index = 1
	XUI.AddClickEventListener(self.node_list["yaoqing_team"], BindTool.Bind1(self.InvitationMasterTeam, self))



	self:CreateTudiList()
	self:CreatRewardList()   --活跃奖励list

	self.max_segree = MasterWGData.GetActiveMax()
	XUI.SetButtonEnabled(self.node_list.yaoqing_team, false)
end

function SocietyView:OnClickGFSetting(isOn)
	self.is_show_gf_flag = isOn
	self:Flush(MASTER_TAB_TYPE.GF)
end


function SocietyView:InvitationMasterTeam()
	local team_list = SocietyWGData.Instance:GetTeamMemberList()
	local shitu_info = MasterWGData.Instance:GetShituInfo()
	if self.team_user_is_online and self.team_user_is_online == 1 then
		for k,v in pairs(team_list) do
			if v.role_id == shitu_info.shifu_id then
				NewTeamWGCtrl.Instance:Open()
				return
			end
		end
		if self.team_user_name_id and self.team_user_name_id > 0 then
			----print_error(self.team_user_name_id )
			MasterWGCtrl.Instance:OpenInvitationTeamPanel(self.team_user_name_id)
		end
	elseif self.team_user_is_online == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Master.FbFriendsOnline)
	else
		--print_error("self.team_user_is_online = nil !!")
	end

end
function SocietyView:DeleteWorshipView()
	if self.rewardlist then
		for k,v in pairs(self.rewardlist) do
			v:DeleteMe()
		end
		self.rewardlist = nil
	end
	-- if nil ~= self.huoyue_progressbar then
	-- 	self.huoyue_progressbar:DeleteMe()
	-- 	self.huoyue_progressbar = nil
	-- end
	if nil ~= self.tudi_active_list then
		self.tudi_active_list:DeleteMe()
		self.tudi_active_list = nil
	end
	if nil ~= self.ph_tudi_task_list then
		self.ph_tudi_task_list:DeleteMe()
		self.ph_tudi_task_list = nil
	end
	self:DestoryAlertWindow()
	self.huoyue_progressbar = nil
	--self.img_gongfeng_hook = nil
end

function SocietyView:CreateTudiList()
	self.tudi_active_list = AsyncListView.New(TudiActivieItemRender, self.node_list["ph_tudi_list"])
	self.tudi_active_list:SetSelectCallBack(BindTool.Bind1(self.TuDiTaskInfoFlush,self))
	self.ph_tudi_task_list = AsyncListView.New(ShituTaskInfoItemRender, self.node_list["ph_tudi_task_list"])

end

function SocietyView:TuDiTaskInfoFlush(item)
	--print_error(item.data)
	self.team_user_name_id = item.data.role_id
	self.team_user_is_online = item.data.tudi_is_online
	if item.data.tudi_is_online == 1 then
		XUI.SetButtonEnabled(self.node_list.yaoqing_team, true)
	else
		XUI.SetButtonEnabled(self.node_list.yaoqing_team, false)
	end
	self.def_select_index = self.tudi_active_list:GetSelectIndex()
	local daily_work_auto = __TableCopy(ConfigManager.Instance:GetAutoConfig("dailywork_auto").work)
	local temp_have_close_task = {} --关闭任务的索引
	local temp_have_over_this_task = {}
	for k,v in pairs(daily_work_auto) do
		v.have_compelete_num = item.data.daily_task_num_list[v.type - 1]
		if v.close_level > 0 and v.close_level <= item.data.level  then
			table.insert(temp_have_close_task,k)
		end
		if item.data.daily_task_num_list[v.type - 1] >= (v.complete_max_times - v.buy_times) then
			table.insert(temp_have_over_this_task,k)
		end

	end
	local new_shitu_info_list = __TableCopy(daily_work_auto)
	--print_error(#temp_have_over_this_task)
	for i = #temp_have_over_this_task ,1 ,-1 do
		--print_error(temp_have_over_this_task[i])
		table.remove(daily_work_auto,temp_have_over_this_task[i])
	end
	for i=1,#temp_have_over_this_task do
		--print_error(temp_have_over_this_task[i])
		table.insert(daily_work_auto,new_shitu_info_list[temp_have_over_this_task[i]])
	end





	-- for k,v in pairs(temp_have_over_this_task) do
	-- 	--print_error("已完成先移除索引",v)
	-- 	table.remove(daily_work_auto,v)
	-- end
	-- for k,v in pairs(temp_have_over_this_task) do
	-- 	--print_error("已完成添加末尾索引",v)
	-- 	table.insert(daily_work_auto,v)
	-- end
	for k,v in pairs(temp_have_close_task) do
		--print_error("关闭任务的索引",v)
		table.remove(daily_work_auto,v)
	end

    --print_error(daily_work_auto)
	self.ph_tudi_task_list:SetDataList(daily_work_auto)
	----print_error(,item.data)
end
function SocietyView:CreatRewardList()
	local reward_list = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").degree
	for k,v in pairs(reward_list) do

		local reward_item = ShituRewardItemRender.New(self.node_list["ph_reward_item"..k])
		reward_item:SetIndex(k)
		self.rewardlist[k] = reward_item
		reward_item:SetClickCallBack(BindTool.Bind2(self.OnClickRewardItem, self, k))
	end

end

function SocietyView:OnClickRewardItem(index)
	MasterWGCtrl.Instance:SendApprenticeActReward(index - 1)
end


function SocietyView:FlushWorshipView()
	local shitu_info = {}
	shitu_info = __TableCopy(MasterWGData.Instance:GetShituInfo())
	----print_error(#shitu_info.tudi_list)
	--shitu_info.gf_chibang_flag = self.is_show_gf_flag
	if #shitu_info.tudi_list == 2 then
		--print_error(shitu_info,shitu_info.tudi_list)
		table.sort(shitu_info.tudi_list,function(a,b) return a.tudi_is_online > b.tudi_is_online end)
	end
	if #shitu_info.tudi_list <= 1 then
	local data = {}
		data.is_add_shoutu = true
		table.insert(shitu_info.tudi_list,data)
	end
	self.tudi_active_list:SetDataList(shitu_info.tudi_list)
	--self.tudi_active_list:SelectIndex(self.def_select_index)
	if #shitu_info.tudi_list == 3 then
		self.tudi_active_list:SelectIndex(self.def_select_index)
	elseif #shitu_info.tudi_list == 2 then
		self.tudi_active_list:SelectIndex(1)
	end

	--self.node_list["lbl_huoyue"].text.text = shitu_info.total_degree  --总经验
	self:UpdataBar(shitu_info.total_degree)
	--print_error("shitu_info",shitu_info,shitu_info.total_degree)
	self:UpdataRewardList()
end

function SocietyView:UpdataBar(curcondition)
	if curcondition > self.max_segree then
		curcondition = self.max_segree
	end

	self.huoyue_progressbar.slider.value = curcondition
end

function SocietyView:GetPercent(k)
	local pingfen_list = {}
	local reward_list = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").degree
	for k,v in pairs(reward_list) do
		pingfen_list[k] = v.degree_limit / self.max_segree
	end
	for i,v in ipairs(pingfen_list) do
		if k == i then
			return v
		end
	end
	return nil
end

function SocietyView:GetBarPercent(times)
	local cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").degree
	local percent = 0
	for i=1,#cfg - 1 do
		if times <= cfg[1].degree_limit then
			percent = times / cfg[i].degree_limit
		elseif times > cfg[i].degree_limit and times < cfg[i + 1].degree_limit then
			local p1 = self:GetPercent(i)
			local p2 = 0.1 * (times - cfg[i].degree_limit)/ (cfg[i + 1].degree_limit - cfg[i].degree_limit)
			percent = p1 + p2 - 0.05
		elseif times == cfg[i].degree_limit and times < cfg[i + 1].degree_limit then
			local p1 = self:GetPercent(i)
			percent = p1 - 0.1
		elseif times >= cfg[#cfg].degree_limit then
			percent = 1
		end
	end
	return percent
end

function SocietyView:UpdataRewardList()
	local item_data_list = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").degree
	if nil == item_data_list then
		ErrorLog("item_data_list")
		return
	end
	for k, v in pairs(item_data_list) do
		local reward_list = self.rewardlist[k]
		if reward_list then
			reward_list:SetData(v)
		end
	end
end

----------------------------------------------------
-- TudiActivieItemRender    左边徒弟列表
----------------------------------------------------
TudiActivieItemRender = TudiActivieItemRender or BaseClass(BaseRender)
function TudiActivieItemRender:__init()

end

function TudiActivieItemRender:__delete()
	if self.role_display then
		self.role_display:DeleteMe()
		self.role_display = nil
	end
end

-- function TudiActivieItemRender:CreateChild()
-- 	BaseRender.CreateChild(self)
-- end
function TudiActivieItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["cancel_relationship_btn"], BindTool.Bind1(self.CancelRelationship, self))
	XUI.AddClickEventListener(self.node_list["Button_shoutu"], BindTool.Bind1(self.InvitationMaster, self))
end
function TudiActivieItemRender:InvitationMaster()
	local shoutu_level = MasterWGData.Instance:GetShouTuLevel()
	if shoutu_level > RoleWGData.Instance.role_vo.level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Master.ShouTuLevelLimit, shoutu_level))
		return
	end
	----print_error(string.format(Language.Master.FindApprenticeTxt, RoleWGData.Instance.role_vo.role_id, RoleWGData.Instance.role_vo.name, RoleWGData.Instance.role_vo.level), CHAT_CONTENT_TYPE.TEXT)
	ChatWGCtrl.Instance:SendChannelChat(0, string.format(Language.Master.FindApprenticeTxt, RoleWGData.Instance.role_vo.role_id, RoleWGData.Instance.role_vo.name, RoleWGData.Instance.role_vo.level), CHAT_CONTENT_TYPE.TEXT)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Master.ShoutuSendSuc)
end
function TudiActivieItemRender:CancelRelationship()
	local alert_window = self:GetAlertWindow()
	alert_window:SetLableString(string.format(Language.Master.RemoveRelationTips, self.data.role_name))
	alert_window:SetOkFunc(function ()
		MasterWGCtrl.Instance:SendRemoveRelation(self.data.role_id)
		SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.INFO)
	end)
	alert_window:Open()
end

function TudiActivieItemRender:OnFlush()
	if nil == self.data then return end
	-- if self.data.role_id > 0 then
	-- 	BrowseWGCtrl.Instance:BrowRoelInfo(self.data.role_id, BindTool.Bind(self.DrawRole, self))
	-- end
	if self.data.is_add_shoutu then
		self.node_list.Button_shoutu:SetActive(true)
		self.node_list.normal:SetActive(false)
		return
	else
		self.node_list.Button_shoutu:SetActive(false)
		self.node_list.normal:SetActive(true)
	end
	local bundle,asset = ResPath.GetRoleHeadIconSociety (self.data.prof)
	self.node_list["tudi_had_icon"].image:LoadSprite(bundle,asset)


	--self.node_list.lbl_cap.text.text = self.data.cap_num
	if self.data.tudi_is_online == 0 then
		self.node_list.lbl_online.text.text = Language.Master.MasterNotOnLine
		self.node_list.lbl_cap.text.text =  string.format(Language.Master.MasterNoONlineCapGray,self.data.cap_num)
		self.node_list.lbl_name.text.text = string.format(Language.Master.MasterNotOnLineGray,self.data.role_name)

	else
		self.node_list.lbl_online.text.text = Language.Master.MasterOnLine
		self.node_list.lbl_cap.text.text = string.format(Language.Master.MasterCapGray,self.data.cap_num)
		if self.data.sex == GameEnum.FEMALE then
			self.node_list.lbl_name.text.text = string.format(Language.Field1v1.FemalenNameColor,self.data.role_name)
		else
			self.node_list.lbl_name.text.text = self.data.role_name
		end
	end


	--self.node_list.lbl_cur_act.text.text = self.data.degree
end
function TudiActivieItemRender:OnSelectChange(is_select)
	if self.data.is_add_shoutu then
		return
	end
	self.node_list.img_choose:SetActive(is_select)
end

function TudiActivieItemRender:DrawRole(role_info)
-- --print_error(role_info.prof)
-- if role_info
-- self.node_list.lbl_online.text.text =
	-- if nil == self.role_display then
	-- 	self.role_display = RoleDisplay.New(self.view, 10, false, true, false, true)
	-- 	local size = self.view:getContentSize()
	-- 	local ph = self.ph_list.ph_role
	-- 	self.role_display:SetPosition(ph.x, ph.y)
	-- 	self.role_display:SetScale(0.7)
	-- end
	-- if nil ~= role_info then
	-- 	self.role_display:SetRoleVo(role_info)
	-- 	local flag = MasterWGData.Instance:GetShituInfo().gf_chibang_flag
	-- 	self.role_display:SetChiBangIsShow(flag)
	-- end
end

function TudiActivieItemRender:CreateSelectEffect()

end



---------------------------------------------
--------ShituRewardItemRender
---------------------------------------------
ShituRewardItemRender = ShituRewardItemRender or BaseClass(BaseRender)
function ShituRewardItemRender:__init()

end

function ShituRewardItemRender:LoadCallBack()
	self.is_show_tips = false
	XUI.AddClickEventListener(self.node_list["img_jyd"], BindTool.Bind1(self.OnClick, self))
end

function ShituRewardItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ShituRewardItemRender:OnClick()
	--print_error("ShituRewardItemRender -- self.index--------  "..self.index)
	MasterWGCtrl.Instance:SendApprenticeActReward(self.index - 1)
end

function ShituRewardItemRender:OnFlush()
	if nil == self.data then
		return
	end
	local data = self.data

	self.node_list["reward_item_num"].text.text = data.item.num
	local shitu_info = MasterWGData.Instance:GetShituInfo()

	self.node_list["lbl_huoyue_num"].text.text = data.degree_limit .. Language.Master.DayExp
	local cur_huoyue = shitu_info.total_degree
	local is_lingqu = MasterWGData.Instance:GetRewardFlagByType(self.index)
	-- self.cell_effect:setVisible(is_lingqu == 0 and cur_huoyue >= data.degree_limit)
	self.is_show_tips = (is_lingqu == 0 and cur_huoyue >= data.degree_limit)

	local master_info = MasterWGData.Instance:GetShituInfo()
	local flag = 1 == master_info.degree_fetch_flag[32 - (self.index - 1)] and true or false

	self.node_list["img_fetch"]:SetActive(flag)
end

function ShituRewardItemRender:ItemcellClickCallBack()
	if not self.is_show_tips then
		return
	end
	if self.click_callback then
		self.click_callback(self)
	end
end

function ShituRewardItemRender:CreateSelectEffect()

end





ShituTaskInfoItemRender = ShituTaskInfoItemRender or BaseClass(BaseRender)
function ShituTaskInfoItemRender:__init()

end

function ShituTaskInfoItemRender:LoadCallBack()

end

function ShituTaskInfoItemRender:__delete()

end
function ShituTaskInfoItemRender:OnFlush()
	if nil == self.data then return end
	----print_error(self.data)
	self.node_list.lbl_task_type.text.text = self.data.name
	local grade_num = self.data.have_compelete_num * self.data.exp_per_times
	self.node_list.lbl_reward.text.text = string.format(Language.Master.PerStudyGreadAdd,grade_num)
	self.node_list.lbl_over_case.text.text = self.data.have_compelete_num .."/"..(self.data.complete_max_times - self.data.buy_times)




end
function ShituTaskInfoItemRender:OnSelectChange(is_select)
	--self.node_list.img_choose:SetActive(is_select)
end
