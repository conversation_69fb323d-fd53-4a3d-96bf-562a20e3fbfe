﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;

public class YYFullScreenEffect :System.IDisposable
{

    
    // 初始化用  /////////////////////////////////
    public CameraEvent RenderQueue = CameraEvent.BeforeForwardAlpha;
    public string CommandBufferName { get; set; }


    /// ////

    public Material Material;
    protected Material clonedMaterial;

    //[Tooltip("Source blend mode")]
    public BlendMode SourceBlendMode = BlendMode.One;
    public BlendMode DestBlendMode = BlendMode.OneMinusSrcAlpha;

    // /////

    protected bool Enabled = false;

    // ///////////////////////
    //public WeatherDownsampleScale DownsampleScale = WeatherDownsampleScale.FullResolution;
    //public WeatherDownsampleScale DownsampleScale = WeatherDownsampleScale.HalfResolution;
    public WeatherDownsampleScale DownsampleScale = WeatherDownsampleScale.SixteenthResolution;



    [Tooltip("做径向模糊的缩放比例，比DownsampleScale  的缩放比例大时，才起作用")]
    public WeatherDownsampleScale DownsampleScalePostProcess = WeatherDownsampleScale.HalfResolution;

    /// </summary>

    public System.Action<WeatherCommandBuffer> UpdateMaterialProperties { get; set; }
    private System.Action<WeatherCommandBuffer> preSetupCommandBuffer;


    [Tooltip("Blur shader type")]
    public BlurShaderType BlurShaderType = BlurShaderType.None;


    [Tooltip("Material for blurring")]
    public Material BlurMaterial;

    /// <summary>Material to render the final pass if needed, not all setups will need this but it should be set anyway</summary>
    [Tooltip("Material to render the final pass if needed, not all setups will need this but it should be set anyway")]
    public Material BlitMaterial;

    public void SetupEffect(
        Material material,
        Material blurMaterial,
        System.Action<WeatherCommandBuffer>  updateMaterialProperties,
        bool enabled,
        System.Action<WeatherCommandBuffer> preSetupCommandBuffer)
    {
        clonedMaterial = material;

        BlurMaterial = blurMaterial;

        Enabled = enabled;

        UpdateMaterialProperties = updateMaterialProperties;

        this.preSetupCommandBuffer = preSetupCommandBuffer;
        

    }

    public void PreCullCamera(Camera camera, bool integratedTemporalReprojection = true)
    {
        YYTemporalReprojectionState reprojState = null;
        WeatherDownsampleScale downsampleScale = DownsampleScale;

        WeatherCommandBuffer cmdBuffer = CreateCommandBuffer(camera, reprojState, downsampleScale, preSetupCommandBuffer);
    }

    public void PreRenderCamera(Camera camera)
    {
        foreach (KeyValuePair<Camera, WeatherCommandBuffer> cmd in weatherMakerCommandBuffers)
        {
            if (cmd.Key == camera && cmd.Value != null && cmd.Value.Camera == camera && cmd.Value.Camera != null && cmd.Value.Material != null && cmd.Value.CommandBuffer != null)
            {
                UpdateMaterialProperties(cmd.Value);
                break;
            }
        }
    }

    private readonly List<KeyValuePair<Camera, WeatherCommandBuffer>> weatherMakerCommandBuffers = new List<KeyValuePair<Camera, WeatherCommandBuffer>>();


    protected WeatherCommandBuffer GetOrCreateWeatherMakerCommandBuffer(Camera camera)
    {
        foreach (KeyValuePair<Camera, WeatherCommandBuffer> kv in weatherMakerCommandBuffers)
        {
            if (kv.Key == camera)
            {
                return kv.Value;
            }
        }
        WeatherCommandBuffer cmd = new WeatherCommandBuffer
        {
            Camera = camera,
            CameraType = WeatherCameraType.Normal, //YYWeather.GetCameraType(camera),
            CommandBuffer = new CommandBuffer { name = CommandBufferName }
        };
        weatherMakerCommandBuffers.Add(new KeyValuePair<Camera, WeatherCommandBuffer>(camera, cmd));
        return cmd;
    }

    protected virtual WeatherCommandBuffer CreateCommandBuffer(Camera camera, YYTemporalReprojectionState reprojState, WeatherDownsampleScale downsampleScale,
          System.Action<WeatherCommandBuffer> preSetupCommandBuffer)
    {
        return null;
    }

    protected void AttachPostProcessing(CommandBuffer commandBuffer, Material material, int w, int h, RenderTextureFormat defaultFormat,
           RenderTargetIdentifier renderedImageId, YYTemporalReprojectionState reprojState, Camera camera,
           ref int postSourceId, ref RenderTargetIdentifier postSource)
    {
        if (material.passCount > 2)
        {
            if (renderedImageId == CameraTargetIdentifier())
            {
                Debug.Log("Weather Maker command buffer post processing cannot blit directly to camera target");
            }
            else
            {
                int downsampleMain = WMS._MainTex2;
                float postScale = (float)Mathf.Max((int)DownsampleScale, (int)DownsampleScalePostProcess);
                commandBuffer.SetGlobalFloat(WMS._WeatherMakerDownsampleScale, postScale);
                postSourceId = WMS._MainTex4;
                postSource = new RenderTargetIdentifier(WMS._MainTex4);
                commandBuffer.GetTemporaryRT(postSourceId, YYFullScreenEffectUtil.GetRenderTextureDescriptor((int)postScale, 0, 1, defaultFormat, 0, camera), FilterMode.Bilinear);

                if (reprojState == null)
                {
                    if ((int)DownsampleScale < (int)DownsampleScalePostProcess)
                    {
                        commandBuffer.GetTemporaryRT(downsampleMain, YYFullScreenEffectUtil.GetRenderTextureDescriptor((int)postScale, 0, 1, defaultFormat, 0, camera), FilterMode.Bilinear);
                        commandBuffer.GetTemporaryRT(postSourceId, YYFullScreenEffectUtil.GetRenderTextureDescriptor((int)postScale, 0, 1, defaultFormat, 0, camera), FilterMode.Bilinear);
                        commandBuffer.SetGlobalTexture(WMS._MainTex2, downsampleMain);
                        commandBuffer.Blit(renderedImageId, downsampleMain);
                        commandBuffer.Blit(downsampleMain, postSourceId, material, 2);
                        commandBuffer.ReleaseTemporaryRT(downsampleMain);
                    }
                    else
                    {
                        commandBuffer.SetGlobalTexture(WMS._MainTex2, renderedImageId);
                        commandBuffer.Blit(renderedImageId, postSourceId, material, 2);
                    }
                }
            }
            commandBuffer.SetGlobalFloat(WMS._WeatherMakerDownsampleScale, (float)DownsampleScale);
            commandBuffer.SetGlobalTexture(WMS._MainTex4, postSourceId);
            commandBuffer.Blit(postSourceId, renderedImageId, material, 3); // pass index 3 is post process blit pass

            //commandBuffer.Blit(postSourceId, renderedImageId);
            commandBuffer.ReleaseTemporaryRT(postSourceId);
        }
    }

   



    public static RenderTargetIdentifier CameraTargetIdentifier()
    {

        

#if UNITY_URP

            return WMS._CameraColorTexture;

#else

        return BuiltinRenderTextureType.CameraTarget;

#endif

    }


    public void Dispose()
    {
        if (Application.isPlaying && clonedMaterial != null && clonedMaterial.name.IndexOf("(clone)", System.StringComparison.OrdinalIgnoreCase) >= 0)
        {
            GameObject.DestroyImmediate(clonedMaterial);
            clonedMaterial = null;
        }
        if (YYCommandBufferManager.Instance != null)
        {
            foreach (KeyValuePair<Camera, WeatherCommandBuffer> kv in weatherMakerCommandBuffers)
            {
                YYCommandBufferManager.Instance.RemoveCommandBuffer(kv.Value);
            }
        }

        foreach (KeyValuePair<Camera, WeatherCommandBuffer> kv in weatherMakerCommandBuffers)
        {
            if (kv.Value != null && kv.Value.CommandBuffer != null)
            {
                if (kv.Key != null)
                {
                    kv.Key.RemoveCommandBuffer(RenderQueue, kv.Value.CommandBuffer);
                }
                kv.Value.CommandBuffer.Clear();
                kv.Value.CommandBuffer.Release();
            }
        }
        weatherMakerCommandBuffers.Clear();
        //foreach (WeatherMakerTemporalReprojectionState state in temporalStates)
        //{
        //    state.Dispose();
        //}
        //temporalStates.Clear();
        Enabled = false;
    }


  

}

public enum WeatherDownsampleScale
{
    /// <summary>
    /// No downsampling
    /// </summary>
    Disabled = 0,

    /// <summary>
    /// Use full resolution
    /// </summary>
    FullResolution = 1,

    /// <summary>
    /// Use half resolution
    /// </summary>
    HalfResolution = 2,

    /// <summary>
    /// Use quarter resolution
    /// </summary>
    QuarterResolution = 4,

    /// <summary>
    /// Use eighth resolution
    /// </summary>
    EighthResolution = 8,

    /// <summary>
    /// Use sixteenth resolution
    /// </summary>
    SixteenthResolution = 16
}
