require("game/multi_func/multi_function_wg_data")
require("game/multi_func/multi_function_view")
-- require("game/multi_func/charm/charm_holy_seal_view")
-- require("game/multi_func/charm/charm_lingzhu_view")
-- require("game/multi_func/charm/charm_compose_view")
-- require("game/multi_func/charm/charm_suit_overview_view")
-- require("game/multi_func/charm/charm_attr_view")
-- require("game/multi_func/charm/charm_lingzhu_tunshi_view")
-- require("game/multi_func/charm/charm_bag_cell")
-- require("game/multi_func/charm/charm_wg_data")
-- require("game/multi_func/charm/charm_wg_ctrl")
require("game/multi_func/daohang/daohang_wg_ctrl")
require("game/multi_func/daohang/daohang_wg_data")

require("game/multi_func/daohang/daohang_holyseal_view")
require("game/multi_func/daohang/daohang_decompose_view")
require("game/multi_func/daohang/daohang_wg_data")
require("game/multi_func/daohang/daohang_jinjie_view")
require("game/multi_func/daohang/daohang_kaiguang_view")
require("game/multi_func/daohang/daohang_keling_view")
require("game/multi_func/daohang/daohang_keyin_store_up_view")
require("game/multi_func/daohang/daohang_keyin_store_inlay_view")
require("game/multi_func/daohang/daohang_keyin_view")
require("game/multi_func/daohang/daohang_qianghua_view")
require("game/multi_func/daohang/daohang_resonance_view")
require("game/multi_func/daohang/daohang_suit_attr_view")
require("game/multi_func/daohang/daohang_suit_view")
require("game/multi_func/daohang/daohang_compose_view")
require("game/multi_func/daohang/daohang_dati_view")
require("game/multi_func/daohang/pixiu_wg_data")
require("game/multi_func/daohang/daohang_suit_fashion_preview_view")

MultiFunctionWGCtrl = MultiFunctionWGCtrl or BaseClass(BaseWGCtrl)
function MultiFunctionWGCtrl:__init()
	if MultiFunctionWGCtrl.Instance then
		print_error("[MultiFunctionWGCtrl] Attemp to create a singleton twice !")
	end
	MultiFunctionWGCtrl.Instance = self

	self.view = MultiFunctionView.New(GuideModuleName.MultiFunctionView)
	self.data = MultiFunctionWGData.New()
	--self:InitCharmCtrl()
	self:InitDaoHangCtrl()

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
	self.bag_init_end = false

	self.role_data_change_callback = BindTool.Bind1(self.OnRoleDataChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"gold", "coin"})
end

function MultiFunctionWGCtrl:__delete()
	MultiFunctionWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	--self:DeleteCharmCtrl()
	self:DeleteDaoHangCtrl()

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
end

------------------------------------------protocol_end----------------------------------------------
function MultiFunctionWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		--self:OnCharmItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
		self:OnDaoHangItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	end
end

-- function MultiFunctionWGCtrl:OnCharmItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
-- 	if self.data:IsCharmComposeStuff(change_item_id) then
-- 		self.data:CalculationCharmComposeRemind()
-- 		RemindManager.Instance:Fire(RemindName.Charm_Holy_Seal)

-- 		if self.charm_compose_view and self.charm_compose_view:IsOpen() then
-- 			self.charm_compose_view:Flush()
-- 		end	
-- 	 end
-- end

function MultiFunctionWGCtrl:OnDaoHangItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.data:IsDaoHangKeLingStore(change_item_id) then
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_keling)
	end

	if self.data:IsDaoHangQiangHuaItem(change_item_id) then
		self.data:CalculationDaoHangQiangHuaRemind()
		RemindManager.Instance:Fire(RemindName.DaoHang_QiangHua)
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_qianghua)
	end

	if self.data:IsDaoHangJinJieItem(change_item_id) then
		self.data:CalculationDaoHangJinJieRemind()
		RemindManager.Instance:Fire(RemindName.DaoHang_JinJie)
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_jinjie)
	end

	if self.data:IsDaoHangKeYinItem(change_item_id) then
		self.data:CalculationDaoHangKeYinRemind()
		RemindManager.Instance:Fire(RemindName.DaoHang_KeYin)
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_keyin)
	end

	if self.data:IsDaoHangKaiGuangItem(change_item_id) then
		self.data:CalculationDaoHangKaiGuangRemind()
		RemindManager.Instance:Fire(RemindName.DaoHang_KaiGuang)
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_kaiguang)
	end

	if self.data:IsDaoHangComposeItem(change_item_id) then
		self.data:CalculationDaoHangComposeRemind()
		RemindManager.Instance:Fire(RemindName.DaoHang_Suit)

		if self.daohang_compose_view and self.daohang_compose_view:IsOpen() then
			self.daohang_compose_view:Flush()
		end

		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_suit)
	end

	if self.data:IsDaoHangHolySealItem(change_item_id) then
		RemindManager.Instance:Fire(RemindName.DaoHang_Suit)
		self:FlushHolySealView()
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_suit)
	end
	
end

function MultiFunctionWGCtrl:OnRoleDataChange(key, value, old_value)
	if self.data:IsDaoHangNoWearEquip() then
		return
	end

	if key == "gold" then
		self.data:CalculationDaoHangJinJieRemind()
		RemindManager.Instance:Fire(RemindName.DaoHang_JinJie)
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_jinjie)
	end

	if key == "coin" then
		self.data:CalculationDaoHangQiangHuaRemind()
		RemindManager.Instance:Fire(RemindName.DaoHang_QiangHua)
		ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_qianghua)
	end
end