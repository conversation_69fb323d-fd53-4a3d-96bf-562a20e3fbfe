TaskConsumeView = TaskConsumeView or BaseClass(SafeBaseView) 

function TaskConsumeView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/activity_ui_prefab", "layout_guildtask_list")
	
	self.view_layer = UiLayer.MainUI
end

function TaskConsumeView:__delete()

end

function TaskConsumeView:ReleaseCallBack()
	if self.consume_alert ~= nil then
		self.consume_alert:DeleteMe() 
		self.consume_alert = nil
	end
	if self.list_view ~= nil then
		self.list_view:DeleteMe() 
		self.list_view = nil
	end
end

function TaskConsumeView:OpenCallBack()
	-- TaskGuide.Instance:SetIsOpenAutoMengTask(false)
end

function TaskConsumeView:LoadCallBack()
	self.mask_bg.image.color = Color.New(0,0,0,0)
	XUI.AddClickEventListener(self.node_list.btn_item_1, BindTool.Bind1(self.OnClickOpenHaiDiFeiXu, self))
	XUI.AddClickEventListener(self.node_list.btn_item_2, BindTool.Bind1(self.OnClickOpenBangPaiCangKu, self))
	XUI.AddClickEventListener(self.node_list.btn_item_3, BindTool.Bind1(self.OnClickOpenScale, self))
	XUI.AddClickEventListener(self.node_list.btn_item_4, BindTool.Bind1(self.OnClickOpenConsumePay, self))
	XUI.AddClickEventListener(self.node_list.btn_select, BindTool.Bind1(self.OnClickConsumeEquipment, self))
end

function TaskConsumeView:OnClickOpenHaiDiFeiXu()
	ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_equip_high)
end

function TaskConsumeView:OnClickOpenBangPaiCangKu()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, TabIndex.guild_cangku)
end

function TaskConsumeView:OnClickOpenScale()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Market, TabIndex.market_goulai)
end

function TaskConsumeView:ShowIndexCallBack(index)
end

function TaskConsumeView:OnFlush(param_t, index)
	for key, v in pairs(param_t) do
		if key == "task_data" then
			self.task_id = v.task_id
			self.pay_monery = v.c_param1
			-- 获取可提交物品的列表
			local list_data = TaskWGData.Instance:GetConsumeEquipment(v.c_param4, v.c_param3)
			if nil ~= next(list_data) then
				if not self.list_view then
					self.list_view = PerfectLoversListView.New(TaskConsumeEquipment,self.node_list.select_item_list)
					self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnFlushConsumeEquipmentBtn, self))
					self.list_view.select_index = 1

				end
				self.list_view:SetDataList(list_data,0)
				self.node_list.caozuo_btn_list:SetActive(false)
				self.node_list.select_item_view:SetActive(true)
			else
				self.node_list.caozuo_btn_list:SetActive(true)
				self.node_list.select_item_view:SetActive(false)
			end
		end
	end
end

-- 花费元宝提交任务
function TaskConsumeView:OnClickOpenConsumePay()
	local ok_callback = function ()
		if nil ~= self.task_id then
			TaskWGCtrl.Instance:CompleteTaskItemCommit(self.task_id, -1)
			self:Close()
		end
	end
	if self.consume_alert == nil and self.pay_monery ~= nil then
		local alert_text = string.format(Language.Task.TaskConsumeTip, self.pay_monery)
		self.consume_alert = Alert.New(alert_text, ok_callback, nil, nil, false)
		self.consume_alert:SetOkFunc(ok_callback)
	end
	self.consume_alert:Open()
end

-- 选择物品提交任务
function TaskConsumeView:OnClickConsumeEquipment()
	local select_index = self.list_view:GetSelectIndex()
	if nil ~= select_index and nil ~= self.task_id then
		local item_index = self.list_view:GetDataList()[select_index].index
		TaskWGCtrl.Instance:CompleteTaskItemCommit(self.task_id, item_index)
		self:Close()
	end
end

function TaskConsumeView:OnFlushConsumeEquipmentBtn(select_item, index)
	XUI.SetButtonEnabled(self.node_list.btn_select, nil ~= index)
end

--================================================================
TaskConsumeEquipment = TaskConsumeEquipment or BaseClass(BaseRender)
function TaskConsumeEquipment:__init()
end

function TaskConsumeEquipment:__delete()
	if nil ~= self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function TaskConsumeEquipment:LoadCallBack()
	local cell_pos = self.node_list.cell_pos
	self.cell = ItemCell.New(cell_pos)
end

function TaskConsumeEquipment:OnFlush()
	local data = self:GetData()
	if nil == data then return end
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	self.node_list.name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

	-- local color = ItemWGData.Instance:GetItemColor(data.item_id)
	-- self.node_list.lbl_item_name:setColor(color)

	self.cell:SetData(data)
	self:SelectChange(self:IsSelectIndex())
end

function TaskConsumeEquipment:SelectChange(is_select)
	-- if nil == self.node_list.img9_item_bg then
	-- 	return
	-- end

	-- local img_path = is_select and ResPath.GetCommon("img9_123") or ResPath.GetCommon("img9_122")
	-- self.node_list.img9_item_bg:loadTexture(img_path)
	if is_select ~= nil then
		self.node_list.high:SetActive(is_select)
	end 
end