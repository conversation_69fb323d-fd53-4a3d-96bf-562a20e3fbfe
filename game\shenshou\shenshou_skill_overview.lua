ShenShouSkillOverview = ShenShouSkillOverview or BaseClass(SafeBaseView)

function ShenShouSkillOverview:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_soul_ring_skill_tip_view")
end

function ShenShouSkillOverview:ReleaseCallBack()
    self.show_data = nil
    self.soul_ring_seq = nil
end

function ShenShouSkillOverview:SetDataAndOpen(soul_ring_seq, data)
    if IsEmptyTable(data) then
        return
    end
    
    self.show_data = data
    self.soul_ring_seq = soul_ring_seq

    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function ShenShouSkillOverview:OnFlush()
    self.node_list.skill_level_text.text.text = self.show_data.level

    self.node_list.cur_desc:SetActive(self.show_data.level ~= 0)

    local cur_skill_cfg = ShenShouWGData.Instance:GetShenShouSKillCfg(self.show_data.skill_type, self.show_data.level)

    if not IsEmptyTable(cur_skill_cfg) then
        self.node_list.skill_name.text.text = cur_skill_cfg.name
        XUI.SetSkillIcon(self.node_list.ph_ml_skill_bg, self.node_list.ph_ml_skill_item, cur_skill_cfg.icon_id)
        
    end

    local skill_desc_list_height = 0
    local skill_data_list = ShenShouWGData.Instance:GetShenShouSKillListByType(self.show_data.skill_type)
    for i = 1, 10 do
        local skill_data = skill_data_list[i]
        local has_skill_data = not IsEmptyTable(skill_data)
        self.node_list["skill_desc_" .. i]:CustomSetActive(has_skill_data)

        if has_skill_data then
            local condition_cfg = ShenShouWGData.Instance:GetSoulRingSkillUnlockConditionCfg(self.soul_ring_seq, self.show_data.skill_type, skill_data.level)
            local condition_cfg_str = condition_cfg and condition_cfg.unlock_condition and  "  (".. condition_cfg.unlock_condition ..")" or ""
            self.node_list["skill_desc_title_" .. i].text.text = string.format(Language.ShenShou.SoulRingSkillNameDesc, skill_data.level) .. condition_cfg_str -- , skill_data.name)

            local color = self.show_data.level == skill_data.level and COLOR3B.GREEN or COLOR3B.C6
            self.node_list["skill_desc_content_" .. i].text.text = ToColorStr(skill_data.description, color)

            if self.show_data.level == i then
                self.node_list.skill_dsc.text.text = ToColorStr(skill_data.description, COLOR3B.GREEN)
            end
        end

        if self.show_data.level > i then
            -- 加上Spacing
            skill_desc_list_height = skill_desc_list_height + 2
            skill_desc_list_height = skill_desc_list_height + self.node_list["skill_desc_content_" .. i].text.preferredHeight + 30
        end


    end
    	-- 加上Top
	if skill_desc_list_height > 0 then
		skill_desc_list_height = skill_desc_list_height + 4
	end

    self:ChangePanelHeight(skill_desc_list_height)
end

-- 设置高度
function ShenShouSkillOverview:ChangePanelHeight(skill_desc_list_height)
	local scroll_view = self.node_list["scroll_view"]
	local scroll_content = self.node_list["layout_skill_tip"]
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(scroll_content.rect)

	local max_center_height = 424
	local scroll_content_height = scroll_content.rect.sizeDelta.y
    local cur_desc_height = self.node_list.skill_dsc.text.preferredHeight + 46
    if self.show_data.level ~= 0 then
        max_center_height = max_center_height - cur_desc_height
    end
    

    local scroll_view_height = 0
	if scroll_content_height > max_center_height then
		scroll_view.layout_element.preferredHeight = max_center_height
		scroll_view.scroll_rect.vertical = true
        scroll_view_height = max_center_height
	else
		scroll_view.layout_element.preferredHeight = scroll_content_height
		scroll_view.scroll_rect.vertical = false
        scroll_view_height = scroll_content_height

	end

    local percent = 0
	local scroll_max = scroll_content_height - scroll_view_height
	if skill_desc_list_height > scroll_max then
		percent = 1
	else
		percent = skill_desc_list_height / scroll_max 
	end
	scroll_view.scroll_rect.verticalNormalizedPosition = 1 - percent
end