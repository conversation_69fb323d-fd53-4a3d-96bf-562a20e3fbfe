RoleBagView = RoleBagView or BaseClass(SafeBaseView)


function RoleBagView:InitBagView(index)
	self.arrange_time = false

	if nil == self.role_info_widget then
		self:CreateRoleInfoWidget()
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
		show_cangjin_score = true,
		show_cash_point = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.open_num = COMMON_CONSTS.MAX_BAG_COUNT

	if not self.bag_grid then
		self.bag_grid = AsyncBaseGrid.New()
		self.bag_grid:SetIsShowTips(false)
		self.bag_grid:CreateCells( { col = 4, cell_count = self.open_num, list_view = self.node_list["ph_bag_grid"], itemRender = RoleBagCell } )
		self.bag_grid:SetSelectCallBack(BindTool.Bind1(self.SelectBagCellCallBack, self))
	end

	self.select_equip_body_type = 0
	self.select_equip_body_seq = -1
	self.need_equip_body_tween = true
	self.change_need_equip_body_tween = nil
	self.jump_to_equip_body_seq_cache = -1

	-- FunOpen.Instance:RegisterFunUi(FunName.LongZhuView, self.node_list["long_zhu_btn"])
	FunOpen.Instance:RegisterFunUi(FunName.EquipmentMark, self.node_list["btn_equipment_mark"])

	-- XUI.AddClickEventListener(self.node_list["long_zhu_btn"], BindTool.Bind1(self.OnClickLongZhuBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_bag_clearup"], BindTool.Bind1(self.OnBagCleanupHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_auto_sell"], BindTool.Bind(self.OpenAutoSell, self))
	self.node_list["btn_star_attr"].button:AddClickListener(BindTool.Bind(self.OnClickRoleEquipAttr, self, 2))
	XUI.AddClickEventListener(self.node_list["btn_equipment_mark"],BindTool.Bind(self.OnClickEquipmentMarkBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_auto_melting"],BindTool.Bind(self.OnClickAutoMelting, self))
	XUI.AddClickEventListener(self.node_list["btn_super_dragon_seal"],BindTool.Bind(self.OnClickSuperDragonSeal, self))
	XUI.AddClickEventListener(self.node_list["btn_dragon_king_token"],BindTool.Bind(self.OnClickDragonKingToken, self))

	self.get_yuanbao_event = GlobalEventSystem:Bind(OtherEventType.GET_BAG_YUANBAO, BindTool.Bind(self.BagGetYuanBaoEffect, self))

	self.load_equip_cell_complete = false
	self:InitBagLeftTogList()
end

function RoleBagView:BagGetYuanBaoEffect()
	if not self:IsOpen() then
		return
	end
	self:PlayEffectByRecharge()
end

function RoleBagView:PlayEffectByRecharge(money_type)
	money_type = GameEnum.MONEY_BAR.SILVER_TICKET
	if money_type == GameEnum.MONEY_BAR.SILVER_TICKET and  self.node_list.bag_effect_pos then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanbao_zhakai_new")
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.bag_effect_pos.transform,2)
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectXianYuHuoDe, false, true))
		GlobalTimerQuest:AddDelayTimer(function ()
            self:PlayEffectByRechargeSecond(GameEnum.NEW_MONEY_BAR.SILVER_TICKET)
        end, 0.5)
	end
end

function RoleBagView:PlayEffectByRechargeSecond(money_type)
	TipWGCtrl.Instance:DestroyFlyEffectByViewName(GuideModuleName.Bag)
    if self.money_bar then
    	local end_obj = self.money_bar:GetSlicketNode()
        TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Bag, "effects2/prefab/ui/ui_yuanbao_new_prefab",
        "UI_yuanbao_new", self.node_list.bag_effect_pos, end_obj, DG.Tweening.Ease.OutCubic, 0.5, nil, nil, 18, 150,nil,false,true)
    end
end

function RoleBagView:DeleteBagView()
	if self.get_yuanbao_event then
		GlobalEventSystem:UnBind(self.get_yuanbao_event)
	end

	if nil ~= self.bag_grid then
		self.bag_grid:DeleteMe()
		self.bag_grid = nil
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	-- if self.change_model then
	-- 	GlobalEventSystem:UnBind(self.change_model)
	-- 	self.change_model = nil
    -- end

	if self.role_bag_model then
		self.role_bag_model:DeleteMe()
		self.role_bag_model = nil
	end

	-- if self.equip_body_list then
	-- 	self.equip_body_list:DeleteMe()
	-- 	self.equip_body_list = nil
	-- end

	self.arrange_time = nil
	self.need_equip_body_tween = nil
	self.change_need_equip_body_tween = nil

	if self.equip_body_tog_cell_list then
		for k, v in pairs(self.equip_body_tog_cell_list) do
			for i, u in pairs(v) do
				u:DeleteMe()
			end
		end

		self.equip_body_tog_cell_list = nil
	end

	self.load_equip_cell_complete = nil
	self.load_equip_cell_flush = nil

	if CountDownManager.Instance:HasCountDown('BagCleanup') then
		CountDownManager.Instance:RemoveCountDown('BagCleanup')
	end
end

function RoleBagView:FlushStrengthRemind()
	if self.node_list and self.node_list["star_remind"] then
		local remind_0 = EquipWGData.Instance:GetStaActiveLevelRemind() or 0
		self.node_list["star_remind"]:SetActive(remind_0 > 0)
	end
end

function RoleBagView:FlushQuickUseRemind()
	if self.node_list and self.node_list.bag_clearup_remind then
		local is_remind = ItemWGData.Instance:GetIsHasQuickUseItem()
		self.node_list.bag_clearup_remind:SetActive(is_remind)
	end
end

function RoleBagView:FlushModel(appe_type)
	local show_index = self.show_index
	if show_index ~= TabIndex.rolebag_bag_all and show_index ~= TabIndex.rolebag_stuff then
		return
	end

	if appe_type ~= nil and not RoleWGData.IsNeedFlushModelAppeType(appe_type) then
		return
	end

	local is_need_flush = false
	if nil == self.role_bag_model and self.node_list["ModelEvent"] then
		self.role_bag_model = RoleModel.New()
		self.role_bag_model:SetUISceneModel(self.node_list["ModelEvent"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_bag_model, {TabIndex.rolebag_stuff, TabIndex.rolebag_bag_all})
		is_need_flush = true
	end

	if self.role_bag_model then
		if is_need_flush then
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			local special_status_table = {ignore_halo = true}
			self.role_bag_model:SetModelResInfo(role_vo, special_status_table)

			if role_vo.fabao_appeid ~= nil and role_vo.fabao_appeid > 0 then
				self.role_bag_model:SetBaoJuResid(role_vo.fabao_appeid)
			end

			self.role_bag_model:FixToOrthographicOnUIScene(self.root_node_transform)
		else
			self.role_bag_model:PlayRoleAction(SceneObjAnimator.UiIdle, nil, true)
		end
	end
end

function RoleBagView:FlushModelAction()
	if self.role_bag_model then
		self.role_bag_model:PlayLastAction()
	end
end

function RoleBagView:OnClickRoleEquipAttr(index)
	--原本是宝石的提示 如果灵石开启了，同时显示宝石和灵石的属性信息
	if index == 1 then
		local is_open = FunOpen.Instance:GetFunIsOpened("equipment_lingyu")
		index = is_open and 5 or 1
	end
	RoleWGCtrl.Instance:SetEquipTextData(index, self.select_equip_body_seq)
	RoleWGCtrl.Instance:OpenEquipAttr()
end

function RoleBagView:FlushBagView(param)
	if param and param.open_param == "up_star_tip" then
		RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.UP_STAR_TIP, self.select_equip_body_seq)
		RoleWGCtrl.Instance:OpenEquipAttr()
	end

	if self.bag_grid then
		self.bag_grid:CancleAllSelectCell()
		self.bag_grid:SetDataList(ItemWGData.Instance:GetBagItemDataList(), 2)
	end

    self:FlushStrengthRemind()
    self:FlushQuickUseRemind()
    self:FlushBagLongZhu()
	self:FlushLongXiBtnState()
	self:FlushBagCapacity()
	self:FlushEquipBodyList(param)
end

function RoleBagView:FlushBagCapacity()
	self.node_list.text_bag_capacity.text.text = ItemWGData.Instance.hold_knapsack_num .. "/" .. ItemWGData.Instance.max_knapsack_valid_num
end

function RoleBagView:SelectBagCallback(index)
	self:FlushBagView()
end

function RoleBagView:SelectBagCellCallBack(cell)
	if nil == cell then
		return
	end

	if self:TryOpenCell(cell) then
		return
	end

	local cell_data = cell:GetData()
	if nil == cell_data or not next(cell_data) then
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(cell_data.item_id)
	if ItemWGData.GetIsXiaogGui(cell_data.item_id) then
		local time = cell_data.invalid_time - TimeWGCtrl.Instance:GetServerTime()
		local is_overdue = time <= 0 and true or false --配置配为0表示永久
		if is_overdue and item_cfg.time_length >0 then
			TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_GUOQI)
		else
			TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_NO_GUOQI)
		end
	else
		local btn_callback_event
		local is_market = MarketWGData.Instance:CheckIsCanMarket(cell_data)
		local is_open = FunOpen.Instance:GetFunIsOpened(FunName.OtherMarket)
		if item_cfg and 0 == item_cfg.isbind and is_market and is_open then
			btn_callback_event = {}
			if cell_data.is_bind == 0 then
				btn_callback_event[1] = {btn_text = Language.Common.ShangJia, callback = function()
					cell_data.knapsack_type = KNAPSACK_TYPE.NORMAL
					ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_sell)
					MarketWGCtrl.Instance:OpenMarketTipItemView(cell_data)
				end}
			end
		end
		TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_BAG, nil, nil, btn_callback_event)				--打开tip,提示使用
	end
end

--物品变化
function RoleBagView:OnBagItemDataChange(item_data)
	if not IsEmptyTable(item_data) then
		self:FlushBagView({item_change_data = item_data})
	else
		self:FlushBagView()
	end
end

function RoleBagView:OnItemCDChange(item_id, index, cd_end_time, client_colddown)

end

-- 整理背包
function RoleBagView:OnBagCleanupHandler()
	-- 快速使用
	if ItemWGData.Instance:GetIsHasQuickUseItem() then
		BagWGCtrl.Instance:OpenQuickUseView()
	else
		if self.arrange_time then return end

		CountDownManager.Instance:AddCountDown('BagCleanup',function (time,total_time)
			time = math.modf(time)
			self.node_list.cleanup_txt.text.text = total_time - time
		end,
		function ()
			self.node_list.cleanup_txt.text.text = Language.Bag.CleanUp_1
			self.arrange_time = false
			XUI.SetButtonEnabled(self.node_list.btn_bag_clearup,true)
			CountDownManager.Instance:RemoveCountDown('BagCleanup')
		end,
		nil,5,1
		)
		self.node_list.cleanup_txt.text.text = 5
		XUI.SetButtonEnabled(self.node_list.btn_bag_clearup,false)
		self.arrange_time = true
		BagWGCtrl.Instance:SendKnapsackStoragePutInOrder(GameEnum.STORAGER_TYPE_BAG, 0)
	end
end

function RoleBagView:OnOpenBagGrid(value1, alertdata)
	-- BagWGCtrl.Instance:SendKnapsackStorageExtendGridNum(GameEnum.STORAGER_TYPE_BAG, alertdata)
end

function RoleBagView:OnOpenStorageGrid(value1, alertdata)
	-- BagWGCtrl.Instance:SendKnapsackStorageExtendGridNum(GameEnum.STORAGER_TYPE_STORAGER, alertdata)
end



function RoleBagView:OnClickCallBuyGold()
	RechargeWGCtrl.Instance:Open(TabIndex.recharge_cz)
end

--一键出售
function RoleBagView:OpenAutoSell()
	local auto_sell_list , num = RoleBagWGData.Instance:GetAutoSellGrid()
	if num > 0 then
		if next(auto_sell_list) ~= nil then
			RoleBagWGCtrl.Instance:OpenAutoSellView()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoSellItem)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoSellItem)
	end
end

--一键熔炼
function RoleBagView:OnClickAutoMelting()
	local _,num = RoleBagWGData.Instance:GetBagEquipMeltList()
	local other = RoleBagWGData.Instance:EquipMeltCfg().other
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local cfg = FunOpen.Instance:GetFunByName("rolebag_view_metling_view")
	local open_level = cfg and cfg.trigger_param or 0
	if other and other[1] and level >= open_level then
		-- RoleBagWGCtrl.Instance:OpenMeltView()
		ViewManager.Instance:Open(GuideModuleName.RoleBagViewMeltingView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Bag.MeltTip, open_level))
	end

	if num <= 0 then
		if FunctionGuide.Instance:GetIsGuide() then
			FunctionGuide.Instance:EndGuide()
		end
	end
end

--打开装备印记界面
function RoleBagView:OnClickEquipmentMarkBtn()
	local data_list = EquipWGData.Instance:GetDataList()
	if IsEmptyTable(data_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.role_bag_tips.ZhangXingTips)
		return
	end
	ViewManager.Instance:Open(GuideModuleName.EquipmentMarkView)
end

-- 打开龙珠界面
function RoleBagView:OnClickLongZhuBtn()
	ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu)
end

function RoleBagView:FlushBagLongZhu()
	local is_red = LongZhuWGData.Instance:IsShowLongZhuRedPoint()
	self.node_list["long_zhu_red"]:SetActive(is_red)
end

function RoleBagView:OnClickSuperDragonSeal()
	ViewManager.Instance:Open(GuideModuleName.LongXiView, TabIndex.super_dragon_seal)
end

function RoleBagView:OnClickDragonKingToken()
	ViewManager.Instance:Open(GuideModuleName.LongXiView, TabIndex.dragon_king_token)
end

function RoleBagView:FlushLongXiBtnState()
	local super_dragon_seal_data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
	if not IsEmptyTable(super_dragon_seal_data) then
		if self.node_list and self.node_list.super_gragon_seal_bg then
			local active = super_dragon_seal_data.is_active ~= 0
			local asset_name = active and "a3_bb_icon_di_1" or "a3_bb_icon_di_2"
			self.node_list.super_gragon_seal_bg.image:LoadSprite(ResPath.GetRoleBagImg(asset_name))
			local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.SuperDragonSeal)
			local icon_asset_name = fun_open and "a3_bb_icon_jtzl_1" or "a3_bb_icon_jtzl_2"
			self.node_list.super_dragon_seal_icon.image:LoadSprite(ResPath.GetRoleBagImg(icon_asset_name))
			
			if not fun_open or not active then
				if not fun_open then
					local fun_cfg = FunOpen.Instance:GetFunByName(FunName.SuperDragonSeal)
					if fun_cfg.trigger_type == 16 or fun_cfg.trigger_type == 26 then
						local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

						local day = fun_cfg.trigger_param - open_day
						if day > 0 then
							day = day < 1 and 1 or math.floor(day)
							self.node_list.super_gragon_seal_no_open_text.text.text = string.format(Language.LongXi.DayTimeOpen, day)
						end
					end
				end
			end

			self.node_list.super_gragon_seal_no_open:SetActive(not fun_open)
			self.node_list.super_gragon_seal_no_active:SetActive(not active and fun_open)
			self.node_list.super_gragon_seal_effect:SetActive(active)
		end
	end

	local dragon_king_token_data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	if not IsEmptyTable(dragon_king_token_data) then
		if self.node_list and self.node_list.dragon_king_token_bg then
			local active = dragon_king_token_data.is_active ~= 0
			local asset_name = active and "a3_bb_icon_di_1" or "a3_bb_icon_di_2"
			self.node_list.dragon_king_token_bg.image:LoadSprite(ResPath.GetRoleBagImg(asset_name))
			local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.DragonKingToken)
			local icon_asset_name = fun_open and "a3_bb_icon_lszl_1" or "a3_bb_icon_lszl_2"
			self.node_list.dragon_king_token_icon.image:LoadSprite(ResPath.GetRoleBagImg(icon_asset_name))

			if not fun_open or not active then
				if not fun_open then
					local fun_cfg = FunOpen.Instance:GetFunByName(FunName.DragonKingToken)
					if fun_cfg.trigger_type == 16 or fun_cfg.trigger_type == 26 then
						local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

						local day = fun_cfg.trigger_param - open_day
						if day > 0 then
							day = day < 1 and 1 or math.floor(day)
							self.node_list.dragon_king_token_no_open_text.text.text = string.format(Language.LongXi.DayTimeOpen, day)
						end
					end
				end
			end

			self.node_list.dragon_king_token_no_open:SetActive(not fun_open)
			self.node_list.dragon_king_token_no_active:SetActive(not active and fun_open)
			self.node_list.dragon_king_token_effect:SetActive(active)
		end
	end
end

----------------------------------------------装备肉身---------------------------------------------------
function RoleBagView:FlushEquipBodyList(item_data)
	if not self.load_equip_cell_complete then
		self.load_equip_cell_flush = function()
			self:FlushEquipBodyList(item_data)
		end
		return
	end

	-- 设置大类型显示
	local equip_body_type_cfg_list = EquipBodyWGData.Instance:GetEquipBodyTypeDataList()
	for k, v in pairs(equip_body_type_cfg_list) do
		-- 肉身列表
		local equip_body_list = EquipBodyWGData.Instance:GetEquipBodyDataListByType(v.equip_body_type)
		local has_data = not IsEmptyTable(equip_body_list)

		self.node_list["eb_select_btn" .. (v.equip_body_type)]:CustomSetActive(has_data)

		if has_data then 
			-- 设置肉身数据
			local cell_list = self.equip_body_tog_cell_list[v.equip_body_type]

			if not IsEmptyTable(cell_list) then
				for i, u in pairs(cell_list) do
					local data = equip_body_list[i]
					if not IsEmptyTable(data) then
						u:SetData(equip_body_list[i])
						u:SetSelfActive(true)
					else
						u:SetSelfActive(false)
					end
				end
			end
		else
			self.node_list["eb_list" .. (v.equip_body_type)]:CustomSetActive(false)
		end
	end

	-- 设置选中
	local target_seq = -1
	if not IsEmptyTable(item_data) then
		local item_change_data = item_data and item_data.item_change_data

		if not IsEmptyTable(item_change_data) then
			for k, v in pairs(item_change_data) do
				if v.reason ~= GameEnum.DATALIST_CHANGE_REASON_UPDATE and target_seq < 0 then
					local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

					if item_cfg and item_cfg.order then
						local cal_seq = EquipBodyWGData.Instance:GetEquipOrderToEquipBodySeq(item_cfg.order)

						if cal_seq and EquipBodyWGData.Instance:IsEquipBodyUnLock(cal_seq) then
							target_seq = cal_seq or -1
						end
					end
				end
			end
		end

		local target_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(target_seq)
	
		if not IsEmptyTable(target_cfg) then
			self.select_equip_body_type = target_cfg.equip_body_type
		end
	end

	self.select_equip_body_type = self.select_equip_body_type >= 0 and self.select_equip_body_type or 0

	-- 选中大类型
	if self.node_list["eb_select_btn" .. self.select_equip_body_type].accordion_element.isOn then
		self.jump_to_equip_body_seq_cache = -1
		self:OnClickEquipBodyTypeHandler(self.select_equip_body_type, target_seq, true)
	else
		self.jump_to_equip_body_seq_cache = target_seq
		self.node_list["eb_select_btn" .. self.select_equip_body_type].accordion_element.isOn = true
	end

	-- local target_seq = -1
	-- -- 选中凡/灵
	-- if not IsEmptyTable(item_data) then
	-- 	local item_change_data = item_data and item_data.item_change_data

	-- 	if not IsEmptyTable(item_change_data) then
	-- 		for k, v in pairs(item_change_data) do
	-- 			if target_seq < 0 then
	-- 				local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

	-- 				if item_cfg and item_cfg.order then
	-- 					local cal_seq = EquipBodyWGData.Instance:GetEquipOrderToEquipBodySeq(item_cfg.order)
	-- 					target_seq = cal_seq or -1
	-- 				end
	-- 			end
	-- 		end
	-- 	end

	-- 	local target_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(target_seq)
	
	-- 	if not IsEmptyTable(target_cfg) then
	-- 		self.select_equip_body_type = target_cfg.type
	-- 	end
	-- end

	-- for i = 0, 1 do
	-- 	self.node_list["btn_equip_body_icon" .. i]:CustomSetActive(i == self.select_equip_body_type)
	-- end

	-- self.node_list.desc_btn_equip_body.text.text = Language.RoleEquipBody.EquipBodySuitName[self.select_equip_body_type]
	-- local equip_body_data_list = EquipBodyWGData.Instance:GetEquipBodyDataList(self.select_equip_body_type)
	-- self.equip_body_list:SetDataList(equip_body_data_list)
	-- self.equip_body_list:JumpToIndex(self:GetEquipBodySelect(equip_body_data_list, target_seq))
end

-- 
function RoleBagView:GetEquipBodySelect(data_list, target_seq)
	if target_seq < 0 then
		if self.select_equip_body_seq >= 0 then
			target_seq = self.select_equip_body_seq
		else
			for k, v in pairs(data_list) do
				if EquipBodyWGData.Instance:IsEquipBodyWearEquip(v.seq) and target_seq < v.seq then
					target_seq = v.seq
				end
			end
		end
	end

	if target_seq >= 0 then
		for k, v in pairs(data_list) do
			if v.seq == target_seq then
				return k
			end
		end
	end

	return 1
end

-- function RoleBagView:OnClickEquipBodyBtn()
-- 	-- 切换
-- 	if self.select_equip_body_type == EQUIP_BODY_TYPE.NORMAL then
-- 		self.select_equip_body_type = EQUIP_BODY_TYPE.SPECIAL
-- 	else
-- 		self.select_equip_body_type = EQUIP_BODY_TYPE.NORMAL
-- 	end

-- 	if nil == self.change_need_equip_body_tween then
-- 		self.change_need_equip_body_tween = true
-- 	end

-- 	self:FlushEquipBodyList()
-- end

-- function RoleBagView:OnSelectEquipBodyHandler(item, cell_index, is_default, is_click)
-- 	if nil == item or IsEmptyTable(item.data) then
-- 		return
-- 	end

-- 	local data = item.data

-- 	if self.select_equip_body_seq ~= data.seq and data.type == 1 then
-- 		self:ChangeToSpecialEquipBody(data)
-- 	end

-- 	self.select_equip_body_seq = data.seq

-- 	self.node_list["btn_star_attr"]:CustomSetActive(data.show_star_bonus == 1)

-- 	if self.role_info_widget then
-- 		self.role_info_widget:SetSelectRoleEquipBodySeq(data.seq)
-- 		self.role_info_widget:SetRoleData()
-- 	end

-- 	-- local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(data.seq)
-- 	-- if not unlock and is_click then
-- 	-- 	EquipBodyWGCtrl.Instance:SetDataAndOpen(data)
-- 	-- end
-- end

function RoleBagView:ChangeToSpecialEquipBody(data)
	local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(data.seq)
	
	if not is_wear_equip then
		local equipA_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(data.seq)
		SysMsgWGCtrl.Instance:ErrorRemind(equipA_body_cfg.no_equip_hint)
	end

	local node_num = self.node_list.equip_root.transform.childCount
	for i = 1, node_num do
		local obj = self.node_list.equip_root.transform:GetChild(i - 1)
		if obj then
			local pos_tween_cam = obj:GetComponent(typeof(UGUITweenPosition))
			if pos_tween_cam then
				pos_tween_cam:ResetToBeginning()
			end

			local alpha_tween_can = obj:GetComponent(typeof(UGUITweenAlpha))
			if alpha_tween_can then
				alpha_tween_can:ResetToBeginning()
			end
		end
	end

	-- TweenManager.Instance:ExecuteViewTween(GuideModuleName.Bag, TabIndex.rolebag_bag_all, self.node_list)
end

-- function RoleBagView:EquipBodyListSetEndScrollCallBack(scroll_obj, start_idx, end_idx)
-- 	if self.need_equip_body_tween or self.change_need_equip_body_tween then
-- 		if self.need_equip_body_tween then
-- 			self.need_equip_body_tween = false
-- 		end

-- 		if self.change_need_equip_body_tween then
-- 			self.change_need_equip_body_tween = false
-- 		end

-- 		local tween_info = UITween_CONSTS.EquipBody

-- 		local cell_list = self.equip_body_list:GetAllItems()
-- 		for i = 1, #cell_list do
-- 			cell_list[i]:PlayItemTween()
-- 		end
-- 	end
-- end

function RoleBagView:InitBagLeftTogList()
	local equip_body_type_cfg_list = EquipBodyWGData.Instance:GetEquipBodyTypeDataList()
	self.equip_body_tog_cell_list = {}

	for i = 0, 9 do
		local data = equip_body_type_cfg_list[i]

		if not IsEmptyTable(data) then
			self.node_list["eb_select_nor_text" .. i].text.text = data.name or ""
			self.node_list["eb_select_sel_text" .. i].text.text = data.name or ""

			if data.show_icon then
				local bundle, asset = ResPath.GetEquipBodyIcon(data.show_icon)
				self.node_list["eb_select_icon" .. i].image:LoadSprite(bundle, asset, function ()
					self.node_list["eb_select_icon" .. i].image:SetNativeSize()
				end)
			end

			self:LoadLeftEquipBodyListCell(i, IsEmptyTable(equip_body_type_cfg_list[i + 1]))
			self.node_list["eb_select_btn" .. i]:CustomSetActive(true)
			self.node_list["eb_select_btn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickEquipBodyTypeHandler, self, i, -1))
		else
			self.node_list["eb_select_btn" .. i]:CustomSetActive(false)
			self.node_list["eb_list" .. i]:CustomSetActive(false)
		end
	end
end

function RoleBagView:LoadLeftEquipBodyListCell(equip_body_type, is_end)
	local res_async_loader = AllocResAsyncLoader(self, "equip_body_type_cell_list" .. equip_body_type)
	res_async_loader:Load("uis/view/rolebag_ui_prefab", "equip_body_tog_list_cell", nil,
		function(new_obj)
			local equip_body_list = EquipBodyWGData.Instance:GetEquipBodyTypeDataListCacheBuType(equip_body_type)

			if not IsEmptyTable(equip_body_list) then
				local item_vo = {}

				for i = 1, #equip_body_list do
					local obj = ResMgr:Instantiate(new_obj)
					local obj_transform = obj.transform
					obj_transform:SetParent(self.node_list["eb_list" .. equip_body_type].transform, false)
					obj:GetComponent("Toggle").group = self.node_list["eb_list" .. equip_body_type].toggle_group
					local item_render = RoleBagEquipBodyListItemCellRender.New(obj)
					-- item_render.parent_view = self
					item_render:SetValueChangedCallBack(BindTool.Bind(self.OnClickEquipBodyCellHandler, self, equip_body_type))
					item_render:SetData(equip_body_list[i])
					item_vo[i] = item_render
					-- obj:SetActive(false)
				end
	
				self.equip_body_tog_cell_list[equip_body_type] = item_vo
			end

			if is_end then
				self.load_equip_cell_complete = true
				if self.load_equip_cell_flush then
					self.load_equip_cell_flush()
					self.load_equip_cell_flush = nil
				end
			end
		end)
end

function RoleBagView:OnClickEquipBodyTypeHandler(equip_body_type, target_seq, isOn)
	if isOn then
		-- 肉身列表
		local equip_body_list = EquipBodyWGData.Instance:GetEquipBodyDataListByType(equip_body_type)

		if not IsEmptyTable(equip_body_list) then
			target_seq = target_seq >= 0 and target_seq or self.jump_to_equip_body_seq_cache
			local target_index = self:GetEquipBodySelect(equip_body_list, target_seq)

			local cell = (self.equip_body_tog_cell_list[equip_body_type] or {})[target_index]

			if cell then
				if cell.view.toggle.isOn then
					self:OnClickEquipBodyCellHandler(equip_body_type, cell)
				else
					cell.view.toggle.isOn = true
				end
			end
		end
	end
end

function RoleBagView:OnClickEquipBodyCellHandler(equip_body_type, item)
	if nil == item or nil == item.data then
		return 
	end

	local cell_list = self.equip_body_tog_cell_list[equip_body_type]
	if not IsEmptyTable(cell_list) then
		for k, v in pairs(cell_list) do
			v:SetSelect(v == item)
		end
	end

	local data = item.data

	if self.select_equip_body_seq ~= data.seq and data.type == 1 then
		self:ChangeToSpecialEquipBody(data)
	end

	self.select_equip_body_seq = data.seq

	self.node_list["btn_star_attr"]:CustomSetActive(data.show_star_bonus == 1)

	if self.role_info_widget then
		self.role_info_widget:SetSelectRoleEquipBodySeq(data.seq)
		self.role_info_widget:SetRoleData()
	end
end

-- -------------------------------RoleBagEquipBodyItemCellRender--------------------------------
-- RoleBagEquipBodyItemCellRender = RoleBagEquipBodyItemCellRender or BaseClass(BaseRender)

-- function RoleBagEquipBodyItemCellRender:LoadCallBack()
-- 	XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickEquipBodyTipBtn, self))
-- end

-- function RoleBagEquipBodyItemCellRender:OnFlush()
-- 	if IsEmptyTable(self.data) then
-- 		return
-- 	end

-- 	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
-- 	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
-- 		self.node_list.icon.image:SetNativeSize()
-- 	end)

-- 	if self.data.show_hl_icon and "" ~= self.data.show_hl_icon then
-- 		local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
-- 		self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
-- 			self.node_list.icon_hl.image:SetNativeSize()
-- 		end)
-- 	end

-- 	if self.data.select_bg and "" ~= self.data.select_bg then
-- 		local select_bundle, select_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
-- 		self.node_list.special_select.image:LoadSprite(select_bundle, select_asset, function ()
-- 			self.node_list.special_select.image:SetNativeSize()
-- 		end)
-- 	end

-- 	self.node_list.name.text.text = self.data.name
-- 	self.node_list.name_hl.text.text = self.data.name
-- 	self.node_list.specall_name.text.text = self.data.name

-- 	local is_unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
-- 	self.node_list.flag_lock:CustomSetActive(not is_unlock)

-- 	self.node_list.btn_tip:CustomSetActive(not is_unlock)

-- 	local remind = EquipBodyWGData.Instance:IsCanEquipBodyUnLock(self.data.seq)
-- 	self.node_list.remind:CustomSetActive(remind)
-- end

-- function RoleBagEquipBodyItemCellRender:OnSelectChange(is_select)
-- 	local is_special = self.data.select_bg and "" ~= self.data.select_bg
-- 	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
-- 	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
-- 	self.node_list.special_select:CustomSetActive(is_special and is_select)
-- 	self.node_list.name:CustomSetActive(not is_special and not is_select)
-- 	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
-- 	self.node_list.specall_name:CustomSetActive(is_special)
-- end

-- function RoleBagEquipBodyItemCellRender:OnClickEquipBodyTipBtn()
-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.UnLockTip)
-- end

-- function RoleBagEquipBodyItemCellRender:PlayItemTween()
-- 	UITween.FakeHideShow(self.node_list.root)
-- 	local index = self:GetIndex()
-- 	local tween_info = UITween_CONSTS.EquipBody.ListCellTween
-- 	self.cell_delay_key = "RoleBagEquipBodyItemCellRender" .. index
-- 	ReDelayCall(self, function()
-- 		if self.node_list and self.node_list.root then
-- 			UITween.FakeToShow(self.node_list.root)
-- 		end
-- 	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
-- end

----------------------------------------RoleBagEquipBodyListItemCellRender---------------------------------------------
RoleBagEquipBodyListItemCellRender = RoleBagEquipBodyListItemCellRender or BaseClass(BaseRender)

function RoleBagEquipBodyListItemCellRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
	XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickEquipBodyTipBtn, self))
end

function RoleBagEquipBodyListItemCellRender:__delete()
	self.value_change_call_back = nil
end

function RoleBagEquipBodyListItemCellRender:OnFlush()
	if nil == self.data then
		return
	end

	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	if self.data.show_hl_icon and "" ~= self.data.show_hl_icon then
		local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
		self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
			self.node_list.icon_hl.image:SetNativeSize()
		end)
	end

	if self.data.select_bg and "" ~= self.data.select_bg then
		local select_bundle, select_asset = ResPath.GetEquipBodyIcon(self.data.select_bg)
		self.node_list.special_select.image:LoadSprite(select_bundle, select_asset, function ()
			self.node_list.special_select.image:SetNativeSize()
		end)
	end

	self.node_list.name.text.text = self.data.name
	self.node_list.name_hl.text.text = self.data.name
	self.node_list.specall_name.text.text = self.data.name
	self.node_list.specall_name_hl.text.text = self.data.name

	local is_unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
	self.node_list.flag_lock:CustomSetActive(not is_unlock)

	self.node_list.btn_tip:CustomSetActive(not is_unlock)

	local remind = EquipBodyWGData.Instance:IsCanEquipBodyUnLock(self.data.seq)
	self.node_list.remind:CustomSetActive(remind)
end

function RoleBagEquipBodyListItemCellRender:SetValueChangedCallBack(call_back)
	self.value_change_call_back = call_back
end

function RoleBagEquipBodyListItemCellRender:OnClickItem(is_on)
	if is_on then
		self.value_change_call_back(self)
	end	
end

function RoleBagEquipBodyListItemCellRender:OnSelectChange(is_select)
	local is_special = self.data.select_bg and "" ~= self.data.select_bg
	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
	self.node_list.special_select:CustomSetActive(is_special and is_select)
	self.node_list.name:CustomSetActive(not is_special and not is_select)
	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
	self.node_list.specall_name:CustomSetActive(is_special and not is_select)
	self.node_list.specall_name_hl:CustomSetActive(is_special and is_select)
end

function RoleBagEquipBodyListItemCellRender:SetSelfActive(active)
	self.view:SetActive(active)
end

function RoleBagEquipBodyListItemCellRender:OnClickEquipBodyTipBtn()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.UnLockTip)
end