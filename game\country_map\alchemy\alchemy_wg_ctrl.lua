require("game/country_map/alchemy/alchemy_wg_data")
require("game/country_map/alchemy/alchemy_view")
require("game/country_map/alchemy/alchemy_compose_view")
require("game/country_map/alchemy/alchemy_normal_array_view")
require("game/country_map/alchemy/alchemy_furnace_view")
require("game/country_map/alchemy/alchemy_speed_up_view")

AlchemyWGCtrl = AlchemyWGCtrl or BaseClass(BaseWGCtrl)
function AlchemyWGCtrl:__init()
	if AlchemyWGCtrl.Instance then
		error("[AlchemyWGCtrl]:Attempt to create singleton twice!")
	end

	AlchemyWGCtrl.Instance = self

	self.data = AlchemyWGData.New()
	self.alchemy_view = AlchemyView.New(GuideModuleName.AlchemyView)
	self.alchemy_compose_view = AlchemyComposeView.New(GuideModuleName.AlchemyComposeView) 			-- 炼丹合成面板
	self.alchemy_normal_array_view = AlchemyNormalArrayView.New(GuideModuleName.AlchemyNormalArrayView) 	-- 法阵升级
	self.alchemy_furnace_view = AlchemyFurnaceView.New(GuideModuleName.AlchemyFurnaceView) 			-- 丹炉升级
	self.alchemy_speed_up_view = AlchemySpeedUpView.New(GuideModuleName.AlchemySpeedUpView)											-- 炼丹加速

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
	self:RegisterAllProtocols()
end

function AlchemyWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.alchemy_compose_view:DeleteMe()
	self.alchemy_compose_view = nil

	self.alchemy_normal_array_view:DeleteMe()
	self.alchemy_normal_array_view = nil

	self.alchemy_furnace_view:DeleteMe()
	self.alchemy_furnace_view = nil

	if self.alchemy_speed_up_view then
		self.alchemy_speed_up_view:DeleteMe()
		self.alchemy_speed_up_view = nil
	end

	if CountDownManager.Instance:HasCountDown("get_redhint") then
        CountDownManager.Instance:RemoveCountDown("get_redhint")
    end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)

	AlchemyWGCtrl.Instance = nil
end

function AlchemyWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSAlchemyOperate)

	self:RegisterProtocol(SCAlchemyInfo,"OnSCAlchemyInfo")
	self:RegisterProtocol(SCAlchemyPelletUpdate,"OnSCAlchemyPelletUpdate")
	self:RegisterProtocol(SCAlchemyFurnaceUpdate,"OnSCAlchemyFurnaceUpdate")
	self:RegisterProtocol(SCAlchemyNormalArrayUpdate,"OnSCAlchemyNormalArrayUpdate")
	self:RegisterProtocol(SCAlchemyDanzaoUpdate,"OnSCAlchemyDanzaoUpdate")
	self:RegisterProtocol(SCAlchemyComposResult,"OnSCAlchemyComposResult")
end


-- 炼丹请求
function AlchemyWGCtrl:SenSAlchemyReq(operate_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSAlchemyOperate)
    protocol.operate_type = operate_type or 0
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
    protocol.param_3 = param_3 or 0
    protocol:EncodeAndSend()
end

function AlchemyWGCtrl:OnSCAlchemyInfo(protocol)
	--("========炼丹信息========",protocol)
	self.data:SetAllAlchemyInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.AlchemyView)
	RemindManager.Instance:Fire(RemindName.CountryMapActAlchemy)
	HomesWGCtrl.Instance:FlushHomesView()
	self:GetIsCanLiqnqu()

	RemindManager.Instance:Fire(RemindName.Role_LianDan)
end

function AlchemyWGCtrl:OnSCAlchemyPelletUpdate(protocol)
	--print_error("========丹药单个改变信息========",protocol)
	self.data:SetSinglePelletInfo(protocol)
	RoleWGCtrl.Instance:FlushView(TabIndex.role_refining)
	RemindManager.Instance:Fire(RemindName.Role_LianDan)
end

function AlchemyWGCtrl:OnSCAlchemyFurnaceUpdate(protocol)
	--print_error("========炼丹炉单个变化========",protocol)
	self.data:SetSingleFurnaceInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.AlchemyView)
	ViewManager.Instance:FlushView(GuideModuleName.AlchemyFurnaceView)
	RemindManager.Instance:Fire(RemindName.CountryMapActAlchemy)
	HomesWGCtrl.Instance:FlushHomesView()
	self:GetIsCanLiqnqu()
end

function AlchemyWGCtrl:OnSCAlchemyNormalArrayUpdate(protocol)
	--print_error("========法阵单个变化========",protocol)
	self.data:SetSingleNormalArrayInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.AlchemyView, 0, "flush_alchemy_normal")
	ViewManager.Instance:FlushView(GuideModuleName.AlchemyNormalArrayView)
end

function AlchemyWGCtrl:OnSCAlchemyDanzaoUpdate(protocol)
	--print_error("========丹灶单个变化========",protocol)
	self.data:SetSingleDanzaoInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.AlchemyView)
	RemindManager.Instance:Fire(RemindName.CountryMapActAlchemy)
	HomesWGCtrl.Instance:FlushHomesView()
	self:GetIsCanLiqnqu()
end

function AlchemyWGCtrl:OnSCAlchemyComposResult(protocol)
	--print_error("========合成结果========",protocol)
	local reward_list = {}
	local compos_cfg = AlchemyWGData.Instance:GetComposeCfgSeq(protocol.compos_seq)
	local num = protocol.is_double == 1 and 2 or 1 --翻倍
	for i = 1, num do
		local data = {}
		data.item_id =  compos_cfg.product_item.item_id
		data.is_bind =  compos_cfg.product_item.is_bind
		data.num =  compos_cfg.product_item.num
		data.is_extra = i ~= 1
		table.insert(reward_list, data)
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.added_item_id)
	if item_cfg then
		table.insert(reward_list, {item_id = protocol.added_item_id, is_bind = 1, num = 1, is_extra = true})
	end

	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end
end

function AlchemyWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if AlchemyWGData.Instance:GetStuffCfg(change_item_id) then
		ViewManager.Instance:FlushView(GuideModuleName.AlchemyComposeView)
	end

	if AlchemyWGData.Instance:GetExpediteItemCfg(change_item_id) then
		ViewManager.Instance:FlushView(GuideModuleName.AlchemySpeedUpView)
	end

	if AlchemyWGData.Instance:GetEIItemIdCanAct(change_item_id) then
		RemindManager.Instance:Fire(RemindName.Role_LianDan)
	end
end

--监听是否可以领取
function AlchemyWGCtrl:GetIsCanLiqnqu()
    local hint_time
    local danzao_info = self.data:GetDanzaoAllInfo()
    for k, v in pairs(danzao_info) do
        if v.is_unlock ~= 0 and v.compos_seq >= 0 then
            local grow_time = self.data:GetGrowTimeComposeBySeq(v.compos_seq, v.start_compos_time)
            if grow_time > 0 then
                hint_time = hint_time and (grow_time < hint_time and grow_time or hint_time) or grow_time
            end
        end
    end

    if hint_time then
        if CountDownManager.Instance:HasCountDown("get_redhint") then
            CountDownManager.Instance:RemoveCountDown("get_redhint")
        end
        
        CountDownManager.Instance:AddCountDown("get_redhint", nil, BindTool.Bind1(self.EndTimeCallBack, self), nil, hint_time + 1, 1)
    end
end

function AlchemyWGCtrl:EndTimeCallBack()
    RemindManager.Instance:Fire(RemindName.CountryMapActAlchemy) 
    HomesWGCtrl.Instance:FlushHomesView()
end

function AlchemyWGCtrl:AlchemyExpeditePlayEffect(seq)
	if self.alchemy_view:IsOpen() then
		self.alchemy_view:PlayExpediteEffect(seq)
	end
end

