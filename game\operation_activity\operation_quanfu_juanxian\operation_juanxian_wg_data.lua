OperationJuanXianWGData = OperationJuanXianWGData or BaseClass()

function OperationJuanXianWGData:__init()
	if OperationJuanXianWGData.Instance ~= nil then
		ErrorLog("[OperationJuanXianWGData] attempt to create singleton twice!")
		return
	end
	OperationJuanXianWGData.Instance = self

	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_QUANFU_JUANXIAN, {[1] = OPERATION_EVENT_TYPE.LEVEL},
		BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowJuanXianRedPoint, self))

	self:UpdataConfig()

	--物品监听
	-- self.jx_item_data_change_callback = BindTool.Bind1(self.JxOnItemDataChange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.jx_item_data_change_callback, false)

	RemindManager.Instance:Register(RemindName.OperationQuanFuJuanXian, BindTool.Bind(self.IsShowJuanXianRedPoint, self))
end

function OperationJuanXianWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.OperationQuanFuJuanXian)
	OperationJuanXianWGData.Instance = nil
	--ItemWGData.Instance:UnNotifyDataChangeCallBack(self.jx_item_data_change_callback)
end

function OperationJuanXianWGData:GetQfjxDayRewardCfg()
	local oa_qfjx_server_day_reward = self.operation_juanxian_cfg.oa_qfjx_server_day_reward
	local interface = self:GetInterfaceByServerDay()
	local reawrd_cfg = {}

	for k,v in pairs(oa_qfjx_server_day_reward) do
		if interface == v.grade then
			table.insert(reawrd_cfg, v)
		end
	end

	return reawrd_cfg
end

function OperationJuanXianWGData:GetQFRoleRewardCfg()
	local oa_qfjx_role_lj_reward = self.operation_juanxian_cfg.oa_qfjx_role_lj_reward
	local interface = self:GetInterfaceByServerDay()
	local reawrd_cfg = {}

	for k,v in pairs(oa_qfjx_role_lj_reward) do
		if interface == v.grade then
			table.insert(reawrd_cfg, v)
		end
	end

	return reawrd_cfg
end

function OperationJuanXianWGData:GetQfjxDayRewardCfgMaxCount()
	local cfg = self:GetQfjxDayRewardCfg()
	if cfg ~= nil then
		return #cfg
	end
	return 0
end

function OperationJuanXianWGData:SetQuanFuJuanXianInfo(protocol)
	self.juanxiandata = {}

	self.juanxiandata.quanfu_juanxian_num = protocol.quanfu_juanxian_num        --全服捐献次数
	self.juanxiandata.role_juanxian_num = protocol.role_juanxian_num			--个人捐献次数
	self.juanxiandata.server_reward_state_list = protocol.server_reward_state_list
	self.juanxiandata.role_reward_state_list = protocol.role_reward_state_list
	self.juanxiandata.server_reward_time_list = protocol.server_reward_time_list
end

--获取全服礼包奖励状态
function OperationJuanXianWGData:GetGiftState(index)
	if self.juanxiandata ~= nil and self.juanxiandata.server_reward_state_list ~= nil then
		return self.juanxiandata.server_reward_state_list[index]
	end
	return 0
end

--获取有多少个激活
function OperationJuanXianWGData:GetActiveNum()
	local max_count = #self:GetQfjxDayRewardCfg()

	if max_count > 0 then
		local active_num = 0
		if self.juanxiandata ~= nil and self.juanxiandata.server_reward_state_list ~= nil then
			for i,v in ipairs(self.juanxiandata.server_reward_state_list) do
				if v > 0 then
					active_num = active_num + 1
				end
			end
		end
		return active_num, max_count
	end
	
	return 0, 1
end

--获取全服奖励生效时间
function OperationJuanXianWGData:GetRewardTime(index)
	if self.juanxiandata ~= nil and self.juanxiandata.server_reward_time_list ~= nil then
		return self.juanxiandata.server_reward_time_list[index]
	end
	return 0
end

--个人奖励状态
function OperationJuanXianWGData:GetRoleRewardState(index)
	if self.juanxiandata ~= nil and self.juanxiandata.role_reward_state_list ~= nil then
		return self.juanxiandata.role_reward_state_list[index]
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function OperationJuanXianWGData:GetServerJuanXianCount()
	if self.juanxiandata ~= nil then
		return self.juanxiandata.quanfu_juanxian_num
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function OperationJuanXianWGData:GetRoleJuanXianCount()
	if self.juanxiandata ~= nil then
		return self.juanxiandata.role_juanxian_num
	end
	return 0
end

--获取当前节点对应的百分比
function OperationJuanXianWGData:IsActiveServerJXReward(index)
	local cfg = self:GetQfjxDayRewardCfg()
	local server_count = self:GetServerJuanXianCount()
	local need_count = 1
	local cur_count = 0

	if index == 1 then
		need_count = cfg[index].server_day_juanxian_num
		cur_count = server_count
	else
		need_count = cfg[index].server_day_juanxian_num - cfg[index-1].server_day_juanxian_num
		cur_count = server_count - cfg[index-1].server_day_juanxian_num
	end

	need_count = need_count > 0 and need_count or 1
	if cur_count > 0 then
		local rate = cur_count / need_count
		return rate > 1 and 1 or rate
	else
		return 0
	end
end

--根据服务器开服天数获得界面配置
function OperationJuanXianWGData:GetInterfaceByServerDay()
	local open_day =  OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_QUANFU_JUANXIAN)
	for k,v in pairs(self.operation_juanxian_param_cfg) do
		if v.start_server_day <= open_day and open_day <= v.end_server_day then
			return v.grade, v.open_level
		end
	end
	
	return 0, 9999
end

--根据界面配置获取界面信息
function OperationJuanXianWGData:GetViewCfgByInterface()
	local interface = self:GetInterfaceByServerDay()
	return self.operation_juanxian_interface_cfg[interface]
end

--根据界面配置获取寻玉录按钮是否开启
function OperationJuanXianWGData:GetXunYuLuBtnOpen()
	local interface = self:GetInterfaceByServerDay()
	if self.operation_juanxian_interface_cfg[interface] then
		return 1 == self.operation_juanxian_interface_cfg[interface].if_open
	end
	return false
end

--获取捐献的道具
function OperationJuanXianWGData:GetJuanXianOtherNeedId()
	return self.operation_juanxian_other_cfg[1].juanxian_stuff_item_id or 0
end

--物品变化
function OperationJuanXianWGData:JxOnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
	if juanxian_stuff_item_id == change_item_id then
		OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_quanfu_juanxian, "RefreshJuanXian", {"RefreshJuanXian"})
		RemindManager.Instance:Fire(RemindName.OperationQuanFuJuanXian)
	end
end

--是否开启
function OperationJuanXianWGData:GetActCanOpen()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local _, open_level = self:GetInterfaceByServerDay()
	if vo.level >= open_level then
		return true
	end
	return false
end

--全民捐献红点
function OperationJuanXianWGData:IsShowJuanXianRedPoint()
	local is_open = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_QUANFU_JUANXIAN)
	if not is_open then
		return 0
	end

	if self.juanxiandata ~= nil then
		local cfg = self:GetQfjxDayRewardCfg()

		if self.juanxiandata.server_reward_state_list ~= nil then
			for k,v in ipairs(self.juanxiandata.server_reward_state_list) do
				if cfg[k] ~= nil and cfg[k].reward_type == OPERATION_REAWAD_TYPE.TWO then  --只有礼包需要领取
					if v == OPERATION_REAWAD_STATE.KLQ then
						return 1
					end
				end
			end
		end

		if self.juanxiandata.role_reward_state_list ~= nil then
			for k,v in ipairs(self.juanxiandata.role_reward_state_list) do
				if v == OPERATION_REAWAD_STATE.KLQ then
					return 1
				end
			end
		end

		local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
		local item_num = ItemWGData.Instance:GetItemNumInBagById(juanxian_stuff_item_id)
		if item_num > 0 then
			return 1
		end

		--寻玉录
		local is_open_xunyulu = self:GetXunYuLuBtnOpen()
		if is_open_xunyulu and ServerActivityWGData.Instance:IsXunYuLuRedPoint() then
			return 1
		end

	end
	return 0
end

--热更读表
function OperationJuanXianWGData:UpdataConfig()
	self.operation_juanxian_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_quanfu_juanxian_auto")
	self.operation_juanxian_param_cfg = self.operation_juanxian_cfg.config_param
	self.operation_juanxian_interface_cfg = ListToMap(self.operation_juanxian_cfg.interface, "interface")
	self.operation_juanxian_other_cfg = self.operation_juanxian_cfg.other
end

function OperationJuanXianWGData:CreatMainExpBtn()
	local buff_cfg = nil
	local buff_state = ACTIVITY_STATUS.CLOSE

	if buff_cfg then
		buff_state = ACTIVITY_STATUS.OPEN
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERATIONACTIVITY_JUANXINA_BUFF)
	if activity_info == nil then
		if buff_state == ACTIVITY_STATUS.OPEN then
			local nex_time = buff_cfg.cd_time + TimeWGCtrl.Instance:GetServerTime()
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY_JUANXINA_BUFF, ACTIVITY_STATUS.OPEN, nex_time, 0, 0, 1)
		else
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY_JUANXINA_BUFF, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
		end
	else
		if buff_state == ACTIVITY_STATUS.OPEN then
			local nex_time = buff_cfg.cd_time + TimeWGCtrl.Instance:GetServerTime()
			if nex_time ~= activity_info.next_time then
				ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY_JUANXINA_BUFF, ACTIVITY_STATUS.OPEN, nex_time, 0, 0, 1)
			end
		else
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY_JUANXINA_BUFF, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
		end
	end
end


