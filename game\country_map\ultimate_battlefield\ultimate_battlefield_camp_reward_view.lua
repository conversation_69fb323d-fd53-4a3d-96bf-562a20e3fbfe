local SCORE_REWARD_TYPE = {
    ATTACK_MODE = 1,
    DEFENSE_MODE = 2,
}

function UltimateBattlefieldRewardView:LoadIndexCallBackCamp()
    if not self.team_type_list then
        self.team_type_list = AsyncListView.New(UlScoreTypeRender, self.node_list.team_type_list)
        self.team_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectTeamTypeItemCB, self))
    end

    if not self.round_type_list then
        self.round_type_list = AsyncListView.New(UlScoreTypeRender, self.node_list.round_type_list)
        self.round_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectRoundTypeItemCB, self))
    end

    if not self.attack_reward then
        self.attack_reward = UlCampRewardRender.New(self.node_list.attack_reward)
    end

    if not self.defense_reward then
        self.defense_reward = UlCampRewardRender.New(self.node_list.defense_reward)
    end
end

---第一队列点击
function UltimateBattlefieldRewardView:OnSelectTeamTypeItemCB(item)
	if nil == item or nil == item.data then
		return
	end

    local index = item.index
    if self.cur_camp_select_type == index then
        return
    end

    self.cur_camp_select_type = index
    self.round_type = nil
    self.round_data = nil
    self:FlushCampRewardList()
end

---回合点击
function UltimateBattlefieldRewardView:OnSelectRoundTypeItemCB(item)
	if nil == item or nil == item.data then
		return
	end

    local index = item.index
    if self.round_type == index then
        return
    end

    self.round_type = index
    self.round_data = item.data
    self:FlushCampMessage()
end


function UltimateBattlefieldRewardView:ShowIndexCallBackCamp()

end

function UltimateBattlefieldRewardView:ReleaseCampReward()
	if self.team_type_list then
		self.team_type_list:DeleteMe()
		self.team_type_list = nil
	end

    if self.round_type_list then
		self.round_type_list:DeleteMe()
		self.round_type_list = nil
	end

    if self.attack_reward then
		self.attack_reward:DeleteMe()
		self.attack_reward = nil
	end

    if self.defense_reward then
		self.defense_reward:DeleteMe()
		self.defense_reward = nil
	end

    self.cur_camp_select_type = nil
    self.round_data = nil
    self.round_type = nil
end

function UltimateBattlefieldRewardView:OnFlushCamp()
    self.team_type_list:SetDataList(Language.UltimateBattlefield.TabGropRewardType2)
    if not self.cur_camp_select_type then
        self.team_type_list:JumpToIndex(1, 5)
    end
end

function UltimateBattlefieldRewardView:FlushCampRewardList()
    local reward_list = UltimateBattlefieldWGData.Instance:GetAllStageCfg()

    if reward_list then
        self.round_type_list:SetDataList(reward_list)
        if not self.round_type then
            self.round_type_list:JumpToIndex(1, 5)
        end
    end
end

function UltimateBattlefieldRewardView:FlushCampMessage()
    if (not self.round_data) or (not self.cur_camp_select_type) or (not self.round_data.stage_data) then
        return
    end

    local stage_data = self.round_data.stage_data
    local win_data = stage_data.camp0_win_reward_item
    local fail_data = stage_data.camp0_fail_reward_item

    if self.cur_camp_select_type == SCORE_REWARD_TYPE.DEFENSE_MODE then
        win_data = stage_data.camp1_win_reward_item
        fail_data = stage_data.camp1_fail_reward_item
    end

    self.attack_reward:SetData(win_data)
    self.defense_reward:SetData(fail_data)
end

---------UlCampRewardRender------------------
UlCampRewardRender = UlCampRewardRender or BaseClass(BaseRender)
function UlCampRewardRender:__delete()
    if self.item_reward_list then
        for k, v in pairs(self.item_reward_list) do
            v:DeleteMe()
        end
        self.item_reward_list = nil
    end

	self.item_reward_list_root = nil
end

function UlCampRewardRender:LoadCallBack()
    if self.item_reward_list == nil then
        self.item_reward_list = {}
		self.item_reward_list_root = {}

        for i = 1, 6 do
			local cell_obj = self.node_list.reward_list:FindObj(string.format("reward_pos_%d", i))
			if cell_obj then
				local cell = ItemCell.New(cell_obj)
				cell:SetIndex(i)
				self.item_reward_list[i] = cell
				self.item_reward_list_root[i] = cell_obj
			end
        end
    end
end

function UlCampRewardRender:OnFlush()
    if not self.data then return end
    
    local item_list = self.data
    if item_list and self.item_reward_list then
        for i, item_reward_cell in ipairs(self.item_reward_list) do
            if item_reward_cell then
				if item_list[i - 1] then    -- 这里减个1，方便表生成默认为0
					self:SetRewardRootVisible(true, i)
					item_reward_cell:SetData(item_list[i - 1])
				else
					self:SetRewardRootVisible(false, i)
				end
            end
        end
    end
end

function UlCampRewardRender:SetRewardRootVisible(Visible, index)
	if self.item_reward_list_root and self.item_reward_list_root[index] then
		self.item_reward_list_root[index]:CustomSetActive(Visible)
	end
end