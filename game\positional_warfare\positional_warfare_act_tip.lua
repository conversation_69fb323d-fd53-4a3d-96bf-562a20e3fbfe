PositionalWarfareActTip = PositionalWarfareActTip or BaseClass(SafeBaseView)

function PositionalWarfareActTip:__init()
	self.is_modal = true
	self.view_layer = UiLayer.MainUIHigh
	self.view_name = "PositionalWarfareActTip"
	self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "positional_warfare_act_tips")
end

function PositionalWarfareActTip:LoadCallBack()
	self.node_list["btn_center_act"].button:AddClickListener(BindTool.Bind1(self.ClickActEnterBtn, self))
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind1(self.Close, self))
end

function PositionalWarfareActTip:OnFlush()
	self.node_list.act_name.text.text = Language.PositionalWarfare.PWFBName
    self.node_list.act_time_quest.text.text = Language.PositionalWarfare.PWACTTipDesc
end

function PositionalWarfareActTip:ClickActEnterBtn()
    ViewManager.Instance:Open(GuideModuleName.PositionalWarfareView)
end