MiJingKFBossSceneLogic = MiJingKFBossSceneLogic or BaseClass(CrossServerSceneLogic)

MiJingKFBossSceneLogic.TREASURECRYTAL = {125, 126, 127}
function MiJingKFBossSceneLogic:__init()

end

function MiJingKFBossSceneLogic:__delete()

end

-- 进入场景
function MiJingKFBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	BossWGCtrl.Instance:Close()
	BossWGCtrl.Instance:OpenMJBossList()
	self:InitGoToPos()
end

-- 获取挂机打怪的位置
function MiJingKFBossSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = nil
    local target_y = nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

function MiJingKFBossSceneLogic:InitGoToPos()
	local sence_id = Scene.Instance:GetSceneId()
	local type1,layer, boss_id = BossWGData.Instance:GetCurSelectBossID()
	local list_data = BossWGData.Instance:GetCrossMJBossByLayer(layer)
	if list_data == nil then return end
	local data = {}
	for k,v in pairs(list_data) do
		if v.boss_id == boss_id then
			data = v
			break
		end
	end

	if IsEmptyTable(data) then return end
    GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end)

	MoveCache.SetEndType(MoveEndType.FightByMonsterId)
	GuajiCache.monster_id = boss_id
	MoveCache.param1 = boss_id
	local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
	GuajiWGCtrl.Instance:MoveToPos(sence_id,data.x_pos, data.y_pos, range)
end

function MiJingKFBossSceneLogic:Out()

	CrossServerSceneLogic.Out(self)
	BossWGCtrl.Instance:CloseMJBossList()
end

-- 此场景优先保证单位数量
function MiJingKFBossSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function MiJingKFBossSceneLogic:IsEnemyVisiblePriortiy()
	return true
end
