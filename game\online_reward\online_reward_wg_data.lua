OnlineRewardWGData = OnlineRewardWGData or BaseClass()
function OnlineRewardWGData:__init()
	if OnlineRewardWGData.Instance then
		error("[OnlineRewardWGData] Attempt to create singleton twice!")
		return
	end
	OnlineRewardWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_online_reward_auto")
    self.task_map_cfg = ListToMap(cfg.reward, "grade", "seq")

    self.grade = 0
    self.seq = -1
    self.online_time = 0
end

function OnlineRewardWGData:__delete()
    OnlineRewardWGData.Instance = nil
end

function OnlineRewardWGData:SetInfo(protocol)
    self.grade = protocol.grade
    self.seq = protocol.seq
    self.online_time = protocol.online_time
end

function OnlineRewardWGData:GetCurSeq()
    return self.seq
end

function OnlineRewardWGData:GetOnlineTime()
    return self.online_time
end

function OnlineRewardWGData:GetRewardList(grade)
    grade = grade or self.grade
    return self.task_map_cfg[grade] or {}
end

function OnlineRewardWGData:GetRewardCfg(grade, seq)
    grade = grade or self.grade
    seq = seq or self.seq
    return (self.task_map_cfg[grade] or {})[seq]
end

function OnlineRewardWGData:GetRewardSatus(seq)
    seq = seq or self.seq
    if self.seq > seq then
        return REWARD_STATE_TYPE.FINISH
    elseif self.seq < seq then
        return REWARD_STATE_TYPE.UNDONE
    end

    local cfg = self:GetRewardCfg()
    if not cfg then
        return REWARD_STATE_TYPE.FINISH
    end

    local remain_time = cfg.online_time - self.online_time
    return remain_time > 0 and REWARD_STATE_TYPE.UNDONE or REWARD_STATE_TYPE.CAN_FETCH
end

function OnlineRewardWGData:GetRemainTime()
    local remain_time = 0
    local cfg = self:GetRewardCfg()
    if not cfg then
        return remain_time
    end

    remain_time = cfg.online_time - self.online_time
    return remain_time > 0 and remain_time or 0
end