MainCrossLingZhuTaskPanel = MainCrossLingZhuTaskPanel or BaseClass(BaseRender)
local LingZhuTaskBtnType = {
	MyLingzhuBtn = 1,
	StealLingzhuBtn = 2,
}
local LINGZHU_PLACE_TIME = 1.5

function MainCrossLingZhuTaskPanel:LoadCallBack()
	if not self.btn_type_list then
		self.btn_type_list = {}
		for i = 1, 2 do
			local obj_root = self.node_list.layout_btn_lingzhu_root:FindObj(string.format("btn_lingzhu_type_%d", i))
			if obj_root then
				local cell = TreasureBtnTypeRender.New(obj_root)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.OnSelectTreasureType, self))
				self.btn_type_list[i] = cell
			end
		end
	end

	if not self.steal_lingzhu_list then
		self.steal_lingzhu_list = AsyncListView.New(LingZhuStealCell, self.node_list.steal_lingzhu_list)
		self.steal_lingzhu_list:SetSelectCallBack(BindTool.Bind(self.BtnStealLingZhu, self))
	end

	if not self.layout_lingzhu_list then
		self.layout_lingzhu_list = AsyncListView.New(LingZhuCell, self.node_list.layout_lingzhu_list)
		self.layout_lingzhu_list:SetSelectCallBack(BindTool.Bind(self.BtnGetSelfLingZhu, self))
	end

    XUI.AddClickEventListener(self.node_list.steal_lingzhu_empty_btn, BindTool.Bind(self.BtnStealLingZhu, self))
    XUI.AddClickEventListener(self.node_list.btn_lingzhu_times_rule, BindTool.Bind2(self.BtnLingZhuRule, self))
    XUI.AddClickEventListener(self.node_list.btn_lingzhu_use, BindTool.Bind2(self.BtnLingZhuUse, self))
	XUI.AddClickEventListener(self.node_list.btn_lingzhu_bomb, BindTool.Bind2(self.BtnLingZhuBomb, self))
	self.move_event = self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_MOVE_START, BindTool.Bind1(self.OnMainRoleMoveStart, self))
end

function MainCrossLingZhuTaskPanel:ReleaseCallBack()
	if self.layout_lingzhu_list then
		self.layout_lingzhu_list:DeleteMe()
		self.layout_lingzhu_list = nil	
	end

	if self.steal_lingzhu_list then
		self.steal_lingzhu_list:DeleteMe()
		self.steal_lingzhu_list = nil	
	end

	if self.btn_type_list and #self.btn_type_list > 0 then
		for _, btn_type_cell in ipairs(self.btn_type_list) do
			btn_type_cell:DeleteMe()
			btn_type_cell = nil
		end

		self.btn_type_list = nil
	end

	if nil ~= self.move_event then
		GlobalEventSystem:UnBind(self.move_event)
		self.move_event = nil
	end

	self:RemoveLingZhuDelayTimer()
	self.cur_select_type_index = nil
	self.is_lingzhu_place_ing = nil
end

function MainCrossLingZhuTaskPanel:OnMainRoleMoveStart()
	self:RemoveLingZhuDelayTimer()

	if not self.is_lingzhu_place_ing then
		return
	end

	self.is_lingzhu_place_ing = false
	CrossTreasureWGCtrl.Instance:CloseLingZhuPlace()
	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	if main_role then
		main_role:SetIsGatherState(false, 0)
	end
end

function MainCrossLingZhuTaskPanel:OnSelectTreasureType(cell)
	if not cell then
		return
	end

	self.cur_select_type_index = cell.index
	self:FlushCurrSelectView()
end

function MainCrossLingZhuTaskPanel:OnFlush(param_t)
	self.cur_select_type_index = LingZhuTaskBtnType.MyLingzhuBtn
	local is_jump_refresh = true

	for k,v in pairs(param_t) do
		if k == "is_force" then
			if v ~= nil then	-- 不判断点跳转，直接刷新当前界面
				is_jump_refresh = false
			end
		end
	end

	if is_jump_refresh then
		if IS_ON_CROSSSERVER then
			self.cur_select_type_index = LingZhuTaskBtnType.StealLingzhuBtn
		else
			local is_gather_self_red = CrossTreasureWGData.Instance:GetCrossTreasureLinzuRemind() == 1			-- 自己的灵珠红点信息
			local is_gather_other_red = CrossTreasureWGData.Instance:GetCrossTreasureBeastRemind() == 1			-- 可掠夺的灵珠的红点信息
			if is_gather_other_red and (not is_gather_other_red) then
				self.cur_select_type_index = LingZhuTaskBtnType.StealLingzhuBtn
			end
		end
	end

	self:FlushCurrSelectView()
end

function MainCrossLingZhuTaskPanel:FlushCurrSelectView()
	for i, btn_type_cell in ipairs(self.btn_type_list) do
		btn_type_cell:SetData(self.cur_select_type_index == i)
		btn_type_cell:OnSelectChange(self.cur_select_type_index == i)
	end

	self.node_list.layout_my_lingzhu_root:CustomSetActive(self.cur_select_type_index == LingZhuTaskBtnType.MyLingzhuBtn)
	self.node_list.layout_steal_lingzhu_root:CustomSetActive(self.cur_select_type_index == LingZhuTaskBtnType.StealLingzhuBtn)

	if self.cur_select_type_index == LingZhuTaskBtnType.MyLingzhuBtn then
		self:FulshMyLingZhuMessage()
	else
		self:FlushStealMessage()
	end

	self:FlushUseOpeaMessage()
end

-- 刷新列表信息
function MainCrossLingZhuTaskPanel:FulshMyLingZhuMessage()
	local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()
	if not base_cfg then
		return
	end

	local max_treasure = base_cfg.max_treasure
	local list = CrossTreasureWGData.Instance:GetNowSelfTreasureList()
	self.now_used_num = list and #list or 0 
	self.node_list.my_lingzhu_empty:CustomSetActive(self.now_used_num <= 0)
	self.node_list.layout_lingzhu_list:CustomSetActive(self.now_used_num > 0)
	local str = string.format(Language.CrossTreasure.CanUseTimesTips, self.now_used_num, max_treasure)
	self.node_list.last_lingzhu_times.text.text = str
	self.layout_lingzhu_list:SetDataList(list)
end

--刷新掠夺信息
function MainCrossLingZhuTaskPanel:FlushStealMessage(base_cfg)
	local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()
	if not base_cfg then
		return
	end

	local max_gather_times = base_cfg.gather_times
	local steal_num = CrossTreasureWGData.Instance:GetDailyStealedNum()
	local last_num = max_gather_times - steal_num
	local color = last_num >= 1 and COLOR3B.GREEN or COLOR3B.RED
	local str = string.format(Language.CrossTreasure.StealTimesTips, ToColorStr(last_num, color), max_gather_times)
	self.node_list.last_lingzhu_times.text.text = str
	self.node_list.steal_lingzhu_empty:CustomSetActive(not IS_ON_CROSSSERVER)
	self.node_list.steal_lingzhu_list:CustomSetActive(IS_ON_CROSSSERVER)

	if IS_ON_CROSSSERVER then
		self.node_list.steal_lingzhu_tips.text.text = string.format(Language.CrossTreasure.LingzhuStealTips, ToColorStr(Language.Common.WangQi, COLOR3B.GREEN))
		local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
        local lingzhu_data = lingzhu_list and lingzhu_list[1]
		local curr_server_id = RoleWGData.Instance:GetCurServerId()
        local treasure_data = CrossTreasureWGData.Instance:GetTreasureInfoByServerId(curr_server_id, lingzhu_data.seq)
        local list = CrossTreasureWGData.Instance:GetServerTreasureCountList2(treasure_data)
		self.steal_lingzhu_list:SetDataList(list)
	else
		self.node_list.steal_lingzhu_tips.text.text = Language.CrossTreasure.LingzhuGatherErrorTips
	end
end

--刷新采集信息
function MainCrossLingZhuTaskPanel:FlushUseOpeaMessage()
	local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
	local lingzhu_data = lingzhu_list and lingzhu_list[1]
	local need_num = 1

	if lingzhu_data then
		local item_lingzhu_num = ItemWGData.Instance:GetItemNumInBagById(lingzhu_data.item_id) 
		local item_lingzhu_cfg = ItemWGData.Instance:GetItemConfig(lingzhu_data.item_id)
		local lingzhu_color = item_lingzhu_num >= need_num and COLOR3B.GREEN or COLOR3B.RED
		self.node_list.lingzhu_can_use.text.text = string.format("%s:%s", item_lingzhu_cfg.name, ToColorStr(item_lingzhu_num, lingzhu_color))
	end

	local bomb_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_BOMB)
	local bomb_data = bomb_list and bomb_list[1]
	
	if bomb_data then
		local item_bomb_num = ItemWGData.Instance:GetItemNumInBagById(bomb_data.item_id)
		local item_bomb_cfg = ItemWGData.Instance:GetItemConfig(bomb_data.item_id)
		local bomb_color = item_bomb_num >= need_num and COLOR3B.GREEN or COLOR3B.RED
		self.node_list.lingzhu_bomb_num.text.text = string.format("%s:%s", item_bomb_cfg.name, ToColorStr(item_bomb_num, bomb_color))
	end

	local has_num = CrossTreasureWGData.Instance:GetHasCrossTreasureUseItemNum()
	self.node_list.btn_lingzhu_use_red:CustomSetActive(has_num)
end

--移除回调
function MainCrossLingZhuTaskPanel:RemoveLingZhuDelayTimer()
    if self.show_place_timer then
        GlobalTimerQuest:CancelQuest(self.show_place_timer)
        self.show_place_timer = nil
    end
end
-----------------------------------------------------
-- 掠夺按钮
function MainCrossLingZhuTaskPanel:BtnStealLingZhu(item, cell_index, is_default, is_click)
	if is_default then
		return
	end
	CrossTreasureWGCtrl.Instance:OpenTreasureView()
end

-- 采收自己的灵珠
function MainCrossLingZhuTaskPanel:BtnGetSelfLingZhu(item, cell_index, is_default, is_click)
	if is_default then
		return
	end

	if (not item) or (not item.data) then
		return
	end

	local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
	local lingzhu_data = lingzhu_list and lingzhu_list[1]
	if not lingzhu_data then
		return
	end

	local is_finish, remain_time = CrossTreasureWGData.Instance:GetHasOneCrossTreasureFinish(item.data, lingzhu_data)
	if is_finish then
		if not RoleWGData.Instance:CheckCurServerIsOrigin() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips3)
		else
			GuajiWGCtrl.Instance:MoveToPos(MapWGData.WORLDCFG[3], item.data.pos_x, item.data.pos_y, 2)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips9)
	end
end

-- 灵珠规则
function MainCrossLingZhuTaskPanel:BtnLingZhuRule()
	local rule_tip = RuleTip.Instance
	local rule_title = Language.CrossTreasure.LingzhuRuleTitle
	local rule_content = Language.CrossTreasure.LingzhuRuleContent
	
	rule_tip:SetTitle(rule_title)
	rule_tip:SetContent(rule_content, nil, nil, nil, true)
end

-- 使用灵珠
function MainCrossLingZhuTaskPanel:BtnLingZhuUse()
	local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
	local lingzhu_data = lingzhu_list and lingzhu_list[1]
	local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()

	if (not base_cfg) or (not lingzhu_data) or (lingzhu_data.item_id == nil) or (lingzhu_data.item_id == 0) then
		return
	end

	local max_treasure = base_cfg.max_treasure
	if self.now_used_num >= max_treasure then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingZhuUseError)
		return
	end

	if IS_ON_CROSSSERVER then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips3)
		return
	end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(lingzhu_data.item_id)
	local need_num = 1

	if item_num >= need_num then
		local main_role = Scene.Instance:GetMainRole()
		if not main_role then
			return
		end
	
		local pos_x, pos_y = main_role:GetLogicPos()
		if not CrossTreasureWGData.Instance:GetUserPosIsCanGather(pos_x, pos_y) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips7)
			return
		end 

		if CrossTreasureWGData.Instance:GetUserPosIsNearForOther() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips11)
			return
		end

		self:StopRoleCurrStatus()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		main_role:SetIsGatherState(true, 0)
		self:RemoveLingZhuDelayTimer()
		self.is_lingzhu_place_ing = true
		CrossTreasureWGCtrl.Instance:OperateLingZhuPlace(LINGZHU_PLACE_TIME, Language.CrossTreasure.LingZhuPlaceTips)

		self.show_place_timer = GlobalTimerQuest:AddDelayTimer(function ()
			if main_role then
				main_role:SetIsGatherState(false, 0)
			end

			self.is_lingzhu_place_ing = false
			CrossTreasureWGCtrl.Instance:SendTreasureOperaTypeUse(lingzhu_data.seq)
		end, LINGZHU_PLACE_TIME)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = lingzhu_data.item_id})
	end
end

-- 使用炸弹
function MainCrossLingZhuTaskPanel:BtnLingZhuBomb()
	local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()
	local bomb_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_BOMB)
	local bomb_data = bomb_list and bomb_list[1]

	if (not base_cfg) or (not bomb_data) or (bomb_data.item_id == nil) or (bomb_data.item_id == 0) then
		return
	end

	if IS_ON_CROSSSERVER then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips3)
		return
	end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(bomb_data.item_id)
	local need_num = 1
	if item_num >= need_num then
		local main_role = Scene.Instance:GetMainRole()
		if not main_role then
			return
		end

		local pos_x, pos_y = main_role:GetLogicPos()
		if not CrossTreasureWGData.Instance:GetUserPosIsCanGather(pos_x, pos_y) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips7)
			return
		end 

		if CrossTreasureWGData.Instance:GetUserPosIsNearForOther() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips11)
			return
		end
		
		self:StopRoleCurrStatus()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		main_role:SetIsGatherState(true, 0)
		self:RemoveLingZhuDelayTimer()
		self.is_lingzhu_place_ing = true
		CrossTreasureWGCtrl.Instance:OperateLingZhuPlace(LINGZHU_PLACE_TIME, Language.CrossTreasure.LingZhuPlaceTips2)

		self.show_place_timer = GlobalTimerQuest:AddDelayTimer(function ()
			if main_role then
				main_role:SetIsGatherState(false, 0)
			end

			self.is_lingzhu_place_ing = false
			CrossTreasureWGCtrl.Instance:SendTreasureOperaTypeUse(bomb_data.seq)
		end, LINGZHU_PLACE_TIME)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = bomb_data.item_id})
	end
end

-- 中断当前的状态
function MainCrossLingZhuTaskPanel:StopRoleCurrStatus()
	GuajiWGCtrl.Instance:ResetPickCache()
	GuajiWGCtrl.Instance:ResetMoveCache()

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end
	main_role:SetArriveFunc()
	main_role:ChangeToCommonState()
end
----------奖励-------------
TreasureBtnTypeRender = TreasureBtnTypeRender or BaseClass(BaseRender)
function TreasureBtnTypeRender:OnFlush()
	local red = false
	if self.index == 1 then
		red = CrossTreasureWGData.Instance:GetCrossTreasureLinzuRemind() == 1
	else
		red =CrossTreasureWGData.Instance:GetCrossTreasureBeastRemind() == 1
	end

	self.node_list.RedPoint:CustomSetActive(red)
end

-- 是否选中
function TreasureBtnTypeRender:OnSelectChange(is_select)
	self.node_list.HLImage:CustomSetActive(is_select)
end

----------奖励-------------
LingZhuCell = LingZhuCell or BaseClass(BaseRender)
function LingZhuCell:ReleaseCallBack()
	self:RemoveStatusCountDown()
end

function LingZhuCell:OnFlush()
    if not self.data then
        return 
    end

	local type_cfg = CrossTreasureWGData.Instance:GetAllTreasureLevelCfgBySeqLevel(self.data.seq, self.data.level)
	local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
	local lingzhu_data = lingzhu_list and lingzhu_list[1]
	if (not type_cfg) or (not lingzhu_data) then
		return
	end

	local is_finish, remain_time = CrossTreasureWGData.Instance:GetHasOneCrossTreasureFinish(self.data, lingzhu_data)
	local name = type_cfg.name or ""
	local color = type_cfg.color or 1
	local mature_time = lingzhu_data.mature_times or 1
	self.node_list.lingzhu_name.text.text = ToColorStr(name, ITEM_COLOR[color])

	if not is_finish then
		self:RemoveStatusCountDown()
		local last_time = mature_time - remain_time
		self.node_list.lingzhu_time.text.text = TimeUtil.FormatSecondDHM9(last_time)

		local function timer_func(elapse_time, total_time)
			local show_time = total_time - elapse_time
			local time_str = TimeUtil.FormatSecondDHM9(show_time)
			self.node_list.lingzhu_time.text.text = time_str
		end

		local complete_fun  = function()
			self:RemoveStatusCountDown()
			-- -- 请求一下信息
			CrossTreasureWGCtrl.Instance:SendTreasureRoleInfo()
		end
		
		self.count_down = CountDown.Instance:AddCountDown(last_time, 1, timer_func, complete_fun)
	else
		self.node_list.lingzhu_time.text.text = ToColorStr(Language.CrossTreasure.LingzhuMature, COLOR3B.C8)
	end
end

function LingZhuCell:RemoveStatusCountDown()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

----------奖励-------------
LingZhuStealCell = LingZhuStealCell or BaseClass(BaseRender)
function LingZhuStealCell:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.lingzhu_name.text.text = self.data
end