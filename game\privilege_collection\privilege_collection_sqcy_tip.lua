PrivilegeCollectionSQCYTip = PrivilegeCollectionSQCYTip or BaseClass(SafeBaseView)

function PrivilegeCollectionSQCYTip:__init()
    self:SetMaskBg(true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_sqcy_tip")
end

function PrivilegeCollectionSQCYTip:SetDataAndOpen(data)
    if IsEmptyTable(data) then
        return
    end

    self.data_info = data

    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function PrivilegeCollectionSQCYTip:LoadCallBack()
    if not self.attr_list then
        self.attr_list = {}

        for i = 1, 2 do
            self.attr_list[i] = CommonAttrRender.New(self.node_list["attr" .. i])
            self.attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end
end

function PrivilegeCollectionSQCYTip:ReleaseCallBack()
    self.data_info = nil

    if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end

        self.attr_list = nil
    end
end

function PrivilegeCollectionSQCYTip:OnFlush()
    if IsEmptyTable(self.data_info) then
        return
    end

    -- 背景切换
    local view_cfg = PrivilegeCollectionWGData.Instance:GetHolyWeaponViewShowCfg(self.data_info.cfg.id)
    if view_cfg then
        local bundle, asset = ResPath.GetPrivilegeCollectionImg(view_cfg.holy_weapon_attr_bg)
        self.node_list.bg_root.image:LoadSprite(bundle, asset, function ()
            self.node_list.bg_root.image:SetNativeSize()
        end)
    end

    -- 0级无属性 拿下一级
    local info = PrivilegeCollectionWGData.Instance:GetHolyWeaponInfoById(self.data_info.cfg.id)
    local level = info and info.level or -1

    if level >= 0 then
        level = level > 0 and level or 1

        local target_cfg = PrivilegeCollectionWGData.Instance:GetHolyWeaponCfg(self.data_info.cfg.id, level)
        if target_cfg then
            local attr_data_list = PrivilegeCollectionWGData.Instance:GetHolyWeaponSkillMaxAttrValueList(target_cfg)

            for i = 1, 2 do
                self.attr_list[i]:SetData(attr_data_list[i])
            end
        end
    end
end