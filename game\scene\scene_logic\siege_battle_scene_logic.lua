SiegeBattleSceneLogic = SiegeBattleSceneLogic or BaseClass(CommonActivityLogic)

function SiegeBattleSceneLogic:__init()
	self.block_decoration = nil
	self.score_decoration = nil
end

function SiegeBattleSceneLogic:__delete()
	if self.block_decoration then
		self.block_decoration:DeleteMe()
		self.block_decoration = nil
	end
	if self.score_decoration then
		self.score_decoration:DeleteMe()
		self.score_decoration = nil
	end

	self.btn_tran_old_vis = nil
end

function SiegeBattleSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonActivityLogic.Enter(self, old_scene_type, new_scene_type)
	self:OpenActivitySceneCd(ACTIVITY_TYPE.GONGCHENGZHAN)

	-- XuiBaseView.CloseAllView()

	-- 所有下坐骑(普通和战斗坐骑)
	MountWGCtrl.Instance:AllDownMount()

	-- FuBenWGCtrl.Instance:OpenTaskFollow()
	-- FuBenWGCtrl.Instance:UpdataTaskFollow()
	-- GongChengWGCtrl.Instance:OpenGCZFollowView()
	-- GongChengWGCtrl.Instance:OpenGCZBtnRenderInMainuiChat()
	-- -- GongChengWGCtrl.Instance:SendGongchengzhanOperate(0)
	self:SetBtnTransformVisible(false)

	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.GUILD)
end

function SiegeBattleSceneLogic:Update(now_time, elapse_time)
	CommonActivityLogic.Update(self, now_time, elapse_time)
end

function SiegeBattleSceneLogic:Out()
	CommonActivityLogic.Out(self)
	-- FuBenWGCtrl.Instance:CloseTaskFollow()
	-- GongChengWGCtrl.Instance:CloseGCZFollowView()
	-- GongChengWGCtrl.Instance:CloseGCZFollowBtnRender()
	self:SetBtnTransformVisible(true)
end

function SiegeBattleSceneLogic:SetBtnTransformVisible(is_visible)
	local btn = MainuiWGCtrl.Instance:GetBtnTransform()
	if not btn then return end
	if is_visible then
		if nil == self.btn_tran_old_vis then
			self.btn_tran_old_vis = btn:isVisible()
		else
			btn:setVisible(self.btn_tran_old_vis)
		end
	else
		self.btn_tran_old_vis = btn:isVisible()
		btn:setVisible(is_visible)
	end
end

-- --无论和平还是PK
-- function GongChengZhanSceneLogic:IsRoleEnemy(target_obj, main_role)
-- 	if main_role:GetVo().camp == target_obj:GetVo().camp then			-- 同一边
-- 		return false, Language.Fight.Side
-- 	end
-- 	return true
-- end

-- -- 怪物是否是敌人
-- function GongChengZhanSceneLogic:IsMonsterEnemy(target_obj, main_role)
-- 	if target_obj:GetVo().monster_id == 1102 then
-- 		return false
-- 	end
-- 	return true
-- end

-- 无论和平还是PK
function SiegeBattleSceneLogic:IsRoleEnemy(target_obj, main_role)
	-- local gongcheng_role_info = GongChengData.Instance:GetGCZGlobalInfo()
	-- local shoufang_camp = 0
	-- if gongcheng_role_info then
	-- 	shoufang_camp = gongcheng_role_info.shoufang_camp
	-- end

	-- if main_role:GetVo().guild_id == target_obj:GetVo().guild_id then			-- 同帮派
	-- 	return false
	-- elseif main_role:GetVo().guild_id == shoufang_camp then 		-- 自己是守方
	-- 	return true
	-- elseif target_obj:GetVo().guild_id == shoufang_camp then		-- 目标是守方
	-- 	return true
	-- else 				-- 都是攻方
	-- 	return false
	-- end
	return true
end

-- 怪物是否是敌人
function SiegeBattleSceneLogic:IsMonsterEnemy(target_obj, main_role)
	-- local gongcheng_role_info = GongChengData.Instance:GetGCZGlobalInfo()
	-- if nil ~= gongcheng_role_info then
	-- 	local shoufang_camp = 0
	-- 	if gongcheng_role_info then
	-- 		shoufang_camp = gongcheng_role_info.shoufang_camp
	-- 	end
	-- 	local monster_id = target_obj:GetVo().monster_id
	-- 	if monster_id then
	-- 		local id_list
	-- 		if main_role:GetVo().guild_id == shoufang_camp then
	-- 			id_list = GongChengData.GetEnemyIdList(true)
	-- 		else
	-- 			id_list = GongChengData.GetEnemyIdList(false)
	-- 		end

	-- 		if id_list then
	-- 			for k,v in pairs(id_list) do
	-- 				if monster_id == v then return true end
	-- 			end
	-- 		end
	-- 	end
	-- end
	return false
end

-- 获取挂机打怪的敌人
function SiegeBattleSceneLogic:GetGuiJiMonsterEnemy()
	-- local gongcheng_role_info = GongChengData.Instance:GetGCZGlobalInfo()
	-- local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	-- local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	-- local shoufang_camp = 0
	-- if gongcheng_role_info then
	-- 	shoufang_camp = gongcheng_role_info.shoufang_camp
	-- end
	-- local role = Scene.Instance.main_role
	-- if role:GetVo().guild_id == shoufang_camp then
	-- 	return Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
	-- else
	-- 	local obj, dis = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
	-- 	if obj then
	-- 		return obj, dis
	-- 	else
	-- 		return Scene.Instance:SelectObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
	-- 	end
	-- end
	-- local obj, dis = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
	-- if obj then
	-- 	return obj, dis
	-- else
	-- 	return Scene.Instance:SelectObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
	-- end
end

function SiegeBattleSceneLogic:UpdateSceneLogic()
	-- local gong_cheng_globle_info = GongChengData.Instance:GetGCZGlobalInfo()
	-- if nil == gong_cheng_globle_info then
	-- 	return
	-- end

	-- self:SetGongChengBlockZone(gong_cheng_globle_info.is_pochen < 1)
	-- self:SetGongChengBlockEffect(gong_cheng_globle_info.is_pochen < 1)
end


-- --阻碍区域
-- function SiegeBattleSceneLogic:SetGongChengBlockZone(is_block)
-- 	local other_cfg = GongChengData.Instance:GetGongChengOtherConfig()
-- 	local pos_list = {}
-- 	local pos_str_list = Split(other_cfg.block_area, "@")
-- 	for i, v in ipairs(pos_str_list) do
-- 		local pos = {}
-- 		local pos_str = Split(v, ":")
-- 		pos.x, pos.y = tonumber(pos_str[1]), tonumber(pos_str[2])
-- 		pos_list[i] = pos
-- 	end

-- 	local cal_line_posy_func = function(x, point_1, point_2)
-- 		return (point_2.y - point_1.y) * (x - point_1.x) / (point_2.x - point_1.x) + point_1.y
-- 	end

-- 	local syt_map = Config_scenelist[Scene.Instance:GetSceneId()]
-- 	for j = pos_list[1].x, pos_list[3].x do
-- 		local posy_list = {}
-- 		posy_list[1] = cal_line_posy_func(j, pos_list[1], pos_list[2])
-- 		posy_list[2] = cal_line_posy_func(j, pos_list[2], pos_list[3])
-- 		posy_list[3] = cal_line_posy_func(j, pos_list[3], pos_list[4])
-- 		posy_list[4] = cal_line_posy_func(j, pos_list[4], pos_list[1])
-- 		table.sort(posy_list)
-- 		local y_min, y_max = math.floor(posy_list[2]), math.floor(posy_list[3])
-- 		for k = y_min, y_max do
-- 			if is_block then
-- 				HandleGameMapHandler:GetGameMap():setZoneInfo(j + syt_map.res_x, k + syt_map.res_y, ZONE_TYPE_BLOCK)
-- 			else
-- 				HandleGameMapHandler:GetGameMap():resetZoneInfo(j + syt_map.res_x, k + syt_map.res_y)
-- 			end
-- 		end
-- 	end
-- end

-- --阻碍区域特效
-- function SiegeBattleSceneLogic:SetGongChengBlockEffect(is_visible)
-- 	local other_cfg = GongChengData.Instance:GetGongChengOtherConfig()
-- 	local pos_str = Split(other_cfg.effect_pos, ":")
-- 	local pos = {x = tonumber(pos_str[1]), y = tonumber(pos_str[2])}
-- 	if is_visible then
-- 		if nil == self.block_decoration then
-- 			local decoration_vo = GameVoManager.Instance:CreateVo(DecorationVo)
-- 			decoration_vo.obj_id = Scene.Instance:GetClientObjId()
-- 			decoration_vo.scene_index = Scene.CalcSceneIndex(SceneObjType.Decoration, 111)
-- 			decoration_vo.decoration_id = other_cfg.effect_id
-- 			decoration_vo.pos_x = pos.x
-- 			decoration_vo.pos_y = pos.y
-- 			local decoration = Scene.Instance:CreateDecoration(decoration_vo)
-- 			decoration:SetScale(2)
-- 			self.block_decoration = decoration
-- 		end
-- 	else
-- 		if self.block_decoration then
-- 			Scene.Instance:DeleteClientObj(self.block_decoration:GetObjId())
-- 		end
-- 	end
-- end
