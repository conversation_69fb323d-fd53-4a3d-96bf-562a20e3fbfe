NewFestivalActivityWGData = NewFestivalActivityWGData or BaseClass()

function NewFestivalActivityWGData:__init()
	if NewFestivalActivityWGData.Instance then
		ErrorLog("[NewFestivalActivityWGData] Attemp to create a singleton twice !")
	end

	NewFestivalActivityWGData.Instance = self

	self.btn_one_remind = {}--按钮1的红点状态
    self:InitCfg()
	self:SetTabIndex()

	RemindManager.Instance:Register(RemindName.NewFestivalActivity, BindTool.Bind(self.IsShowMainViewRedPoint, self))

	self.one_remind_change = BindTool.Bind(self.OneRemindChangeCallBack, self)
	self:SetRemindEventList()
end

function NewFestivalActivityWGData:__delete()
	NewFestivalActivityWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.NewFestivalActivity)
	RemindManager.Instance:UnBind(self.one_remind_change)
end

function NewFestivalActivityWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("new_festival_activity_config_auto")
	self.new_activity_info_cfg = cfg.new_activity_info
	self.new_festival_act_cfg = ListToMap(cfg.new_activity_info, "activity_type")
	self.other_cfg = cfg.other[1]
	self.prayer_info_cfg = cfg.prayer_info[1]

	self.denglu_info_cfg = cfg.denglu_info[1]
	self.tehui_shop_text_cfg = cfg.tehui_shop[1]
	self.consume_rebate_text_cfg = cfg.consume_rebate[1]
	self.collect_item_cfg = cfg.collect_item[1]
	self.collect_card_cfg = cfg.collect_card[1]
	self.boss_drop_cfg = cfg.boss_drop[1]
	self.rank_info_cfg = cfg.rank_info[1]
	self.sd_info_cfg = cfg.sd_info[1]
	self.recharge_info_cfg = cfg.recharge_info[1]--活动累充配置表
end

--设置界面信息
function NewFestivalActivityWGData:SetTabIndex()
	self.tab_index_name = {}
	self.sub_tab_name_list = {}
	self.remind_tab = {}
	self.all_remind_tab = {}
	if IsEmptyTable(self.new_activity_info_cfg) then
		return
	end

	table.sort(self.new_activity_info_cfg, SortTools.KeyLowerSorters("big_type", "small_type"))
	for k,v in ipairs(self.new_activity_info_cfg) do
		local tab_index = v.big_type * 10
		local sub_index = v.small_type

		TabIndex["new_festival_activity_" .. v.activity_type] = tab_index + sub_index

		if not self.tab_index_name[v.big_type] then
			table.insert(self.tab_index_name, Language.NewFestivalActivity.BigTypeName[v.big_type])
		end
		
		if not self.sub_tab_name_list[v.big_type] then
			self.sub_tab_name_list[v.big_type] = {}
		end

		table.insert(self.sub_tab_name_list[v.big_type], v.active_name)

		if not self.remind_tab[v.big_type] then
			self.remind_tab[v.big_type] = {}
		end

		local red_name = v.remind_name or ""
		table.insert(self.remind_tab[v.big_type], red_name)
		if red_name ~= "" then
			table.insert(self.all_remind_tab, red_name)
		end
	end
end

function NewFestivalActivityWGData:GetViewShowInfo()
	return self.tab_index_name or {}, self.sub_tab_name_list or {}, self.remind_tab or {}
end

--获取活动配置
function NewFestivalActivityWGData:GetFestivalActivityCfg(activity_type)
	return self.new_festival_act_cfg[activity_type]
end

function NewFestivalActivityWGData:GetOtherCfg()
	return self.other_cfg
end

function NewFestivalActivityWGData:GetPrayerInfoCfg()
	return self.prayer_info_cfg
end

--特惠商店文字配置
function NewFestivalActivityWGData:GetTehuiShopTextCfg()
	return self.tehui_shop_text_cfg
end

--消费返利文字配置
function NewFestivalActivityWGData:GetConsumeRebateTextCfg()
	return self.consume_rebate_text_cfg
end

--获取所有活动
function NewFestivalActivityWGData:GetAllFestivalActivityCfg()
	return self.new_festival_act_cfg
end

function NewFestivalActivityWGData:GetNewFesActDLCfg()
	return self.denglu_info_cfg
end

function NewFestivalActivityWGData:GetNewFesActDRCfg()
	return self.rank_info_cfg
end

function NewFestivalActivityWGData:GetNewFesActSDCfg()
	return self.sd_info_cfg
end

--获取活动资源路径
function NewFestivalActivityWGData:GetResPath()
	return self.other_cfg.res_path or ""
end

--获取活动是否开启
function NewFestivalActivityWGData:GetActivityState(activity_type)
	return ActivityWGData.Instance:GetActivityIsOpen(activity_type)
end

--获取第一个开启的功能(如果有红点红点优先)
function NewFestivalActivityWGData:GetOneOpenTabIndex()
	local activity_cfg = self.new_activity_info_cfg
	table.sort(activity_cfg, SortTools.KeyLowerSorters("big_type", "small_type"))

	local first_index = 0
	for k, v in ipairs(activity_cfg) do
		if self:GetActivityState(v.activity_type) then
			if 0 == first_index then
				first_index = v.big_type * 10 + v.small_type
			end

			local red_name = ((self.remind_tab[v.big_type] or {})[v.small_type] or {}) or ""
			if red_name and red_name ~= "" then
				local remind_num = RemindManager.Instance:GetRemind(red_name)
				if remind_num > 0 then
					return v.big_type * 10 + v.small_type
				end
			end
		end
	end

	return first_index
end

function NewFestivalActivityWGData:GetCollectItemOtherCfg()
	return self.collect_item_cfg
end

function NewFestivalActivityWGData:GetCollectCardOtherCfg()
	return self.collect_card_cfg
end

function NewFestivalActivityWGData:GetBossDropOtherCfg()
	return self.boss_drop_cfg
end

function NewFestivalActivityWGData:GetRechargeOtherCfg()
	return self.recharge_info_cfg
end
------------------------- 活动红点数据-------------------------
--对监听的红点进行监听
function NewFestivalActivityWGData:SetRemindEventList()
	if IsEmptyTable(self.all_remind_tab) then
		return
	end

	for k, v in pairs(self.all_remind_tab) do
		RemindManager.Instance:Bind(self.one_remind_change, v)
	end
end

function NewFestivalActivityWGData:OneRemindChangeCallBack(remind_name, num)
	RemindManager.Instance:Fire(RemindName.NewFestivalActivity)
end

--主界面红点提示按钮
function NewFestivalActivityWGData:IsShowMainViewRedPoint()
	if IsEmptyTable(self.all_remind_tab) then
		return 0
	end

	for k, v in pairs(self.all_remind_tab) do
		if v ~= "" then
			local remind_num = RemindManager.Instance:GetRemind(v)
			if remind_num > 0 then
				return 1
			end 
		end
	end

	return 0
end