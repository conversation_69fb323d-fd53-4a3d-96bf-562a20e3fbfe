--终极战场
function CountryMapMapView:LoadIndexCallBackUltimateBattlefield()
    if self.display_model == nil then
		local display_node = self.node_list.display_root
		self.display_model = RoleModel.New()
		local display_data = {
			parent_node = display_node,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.display_model:SetRenderTexUI3DModel(display_data)
		-- self.display_model:SetUI3DModel(display_node.transform, display_node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.display_model)
	end


	XUI.AddClickEventListener(self.node_list.ult_battle_record_btn, BindTool.Bind1(self.OnClickUltBattleRecord, self))
	XUI.AddClickEventListener(self.node_list.ult_talent_btn, BindTool.Bind1(self.OnClickUltTalent, self))
	XUI.AddClickEventListener(self.node_list.ult_reward_btn, BindTool.Bind1(self.OnClickUltReward, self))
	XUI.AddClickEventListener(self.node_list.ult_shop_btn, BindTool.Bind1(self.OnClickUltShop, self))
	XUI.AddClickEventListener(self.node_list.ult_act_btn, BindTool.Bind1(self.OnClickUltToAct, self))
	XUI.AddClickEventListener(self.node_list.ult_show_explain_btn, BindTool.Bind1(self.OnClickUltShowExplain, self))
	XUI.AddClickEventListener(self.node_list.wor_ship_left_btn, BindTool.Bind2(self.ChangeWorShipShow, self, -1))
	XUI.AddClickEventListener(self.node_list.wor_ship_right_btn, BindTool.Bind2(self.ChangeWorShipShow, self, 1))
	XUI.AddClickEventListener(self.node_list.wor_ship_btn, BindTool.Bind1(self.OnClickUlWorShipBtn, self))
end


function CountryMapMapView:ShowIndexCallBackUltimateBattlefield()

end

function CountryMapMapView:ReleaseUltimateBattlefield()
	self.worship_index = nil
	self.worship_infos = nil
	self.worship_max_length = nil
	self.cur_model_uid = nil

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end
end

function CountryMapMapView:OnFlushUltimateBattlefield(param_t, index)
	for k, v in pairs(param_t) do
		self:FlushUIWorShip()
	end
end

-- 刷新膜拜
function CountryMapMapView:FlushUIWorShip()
	self.worship_infos = UltimateBattlefieldWGData.Instance:GetWorshipInfoList()
	local is_have_worship = self.worship_infos ~= nil and #self.worship_infos > 0
	self.node_list.over_root:CustomSetActive(is_have_worship)
	self.node_list.display_root:CustomSetActive(is_have_worship)
	self.node_list.xuwei:CustomSetActive(not is_have_worship)

	if not is_have_worship then
		return
	end

	self.worship_max_length = #self.worship_infos

	if self.worship_index == nil then
		self.worship_index = 1
	end

	self:FlushWorshipShow()
end

-- 刷新膜拜展示
function CountryMapMapView:FlushWorshipShow()
	self.node_list.xuwei:CustomSetActive(false)
	self.node_list.wor_ship_btn_root:CustomSetActive(false)
	self.node_list.wor_ship_left_btn:CustomSetActive(false)
	self.node_list.wor_ship_right_btn:CustomSetActive(false)

	if (not self.worship_index) or (not self.worship_infos) or #self.worship_infos < 1 then
		self.node_list.xuwei:CustomSetActive(true)
		return
	end

	self.node_list.wor_ship_btn_root:CustomSetActive(true)
	local curr_show = self.worship_infos[self.worship_index]
	


	if curr_show then
		-- 存在上阵，展示
		if self.display_model and self.cur_model_uid ~= curr_show.uid then
			local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true, ignore_fazhen = true}
			self.display_model:SetModelResInfo(curr_show, ignore_table)
			self.cur_model_uid = curr_show.uid
		end

		if self.display_model then
			self.display_model:PlayRoleAction(SceneObjAnimator.UiIdle)
		end
		
		self.node_list.wor_ship_name_txt.text.text = curr_show.name
		local self_uuid = RoleWGData.Instance:GetUUid().temp_low

		if curr_show.uid == self_uuid then
			self.node_list.wor_ship_btn_txt.text.text = Language.UltimateBattlefield.ShipTips02
		else
			self.node_list.wor_ship_btn_txt.text.text = Language.UltimateBattlefield.ShipTips01
		end
		
		local is_left_red = UltimateBattlefieldWGData.Instance:GetWorshipFlagRed(self.worship_index, true)
		local is_right_red = UltimateBattlefieldWGData.Instance:GetWorshipFlagRed(self.worship_index, false)
		local is_this_red = UltimateBattlefieldWGData.Instance:GetWorshipFlagRed(self.worship_index, false, true)
	
		self.node_list.wor_ship_left_btn:CustomSetActive(self.worship_index ~= 1)
		self.node_list.wor_ship_right_btn:CustomSetActive(self.worship_index ~= #self.worship_infos)
		self.node_list.wor_ship_left_remind:CustomSetActive(is_left_red > 0)
		self.node_list.wor_ship_right_remind:CustomSetActive(is_right_red > 0)
		self.node_list.wor_ship_remind:CustomSetActive(is_this_red > 0)
		XUI.SetGraphicGrey(self.node_list.wor_ship_btn_root, is_this_red == 0)
	end
end

-- 膜拜左右切换
function CountryMapMapView:ChangeWorShipShow(offset)
	if (not self.worship_index) or (not self.worship_infos) or #self.worship_infos < 1 or self.worship_max_length == nil then
		return
	end
	
	self.worship_index = self.worship_index + offset
	if self.worship_index < 1 then
		self.worship_index = 1
	elseif self.worship_index > self.worship_max_length then
		self.worship_index = self.worship_max_length
	end

	self:FlushWorshipShow()
end

function CountryMapMapView:OnClickUlWorShipBtn()
	if (not self.worship_index) or (not self.worship_infos) or #self.worship_infos < 1 or self.worship_max_length == nil then
		return
	end

	local curr_show = self.worship_infos[self.worship_index]
	local self_uuid = RoleWGData.Instance:GetUUid().temp_low
	local is_self = curr_show and curr_show.uid == self_uuid

	local is_this_red = UltimateBattlefieldWGData.Instance:GetWorshipFlagRed(self.worship_index, false, true)
	if is_this_red > 0 then
		-- 和服务器index 相差1
		if is_self then
			UltimateBattlefieldWGCtrl.Instance:RequestWorShipReward()
		else
			UltimateBattlefieldWGCtrl.Instance:RequestWorShip(self.worship_index - 1)
		end
	else
		if is_self then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.CanNotWorShipReward)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.CanNotWorShip)
		end
	end
end


function CountryMapMapView:OnClickUltBattleRecord()
    local my_rank_info = UltimateBattlefieldWGData.Instance:GetMyRankInfo()
    if not my_rank_info then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.NoHaveGuessHistory)
		return
    end

	UltimateBattlefieldWGCtrl.Instance:OpenGuessHistoryView()
end

function CountryMapMapView:OnClickUltTalent()
	UltimateBattlefieldWGCtrl.Instance:OpenTalentPreviewView()
end

function CountryMapMapView:OnClickUltReward()
	ViewManager.Instance:Open(GuideModuleName.UltimateBattlefieldReward)
end

function CountryMapMapView:OnClickUltShop()
	UltimateBattlefieldWGCtrl.Instance:OpenShopView()
end

-- 进入活动按钮
function CountryMapMapView:OnClickUltToAct()
	local is_open_act = UltimateBattlefieldWGData.Instance:CheckActIsOpen()
	if is_open_act then
		-- 进入活动
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
	end
end

-- 图解
function CountryMapMapView:OnClickUltShowExplain()
	UltimateBattlefieldWGCtrl.Instance:OpenExplainView()
end