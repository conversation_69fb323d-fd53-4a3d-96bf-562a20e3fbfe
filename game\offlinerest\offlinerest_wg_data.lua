OfflineRestWGData = OfflineRestWGData or BaseClass()

OfflineRestWGData.OfflineTotalTime = 30 * 3600 * 24
OfflineRestWGData.OfflineTotalTimeHour = 20
local TOGGLE_MAX = 21 --少于21时显示空格子

function OfflineRestWGData:__init()
	if OfflineRestWGData.Instance ~= nil then
		ErrorLog("[OfflineRestWGData] Attemp to create a singleton twice !")
	end
	OfflineRestWGData.Instance = self

	self.notify_reson = 0
	self.get_exp_per_minute = 0

	self.remain_offline_rest_time = 0
	self.pass_offline_rest_time = 0

	self.old_level = 0
	self.new_level = 0

	self.add_exp = 0
	self.add_coin = 0
	self.total_cur_add_exp_per = 0
	self.add_fight_soul_exp = 0
	self.online_last_add_exp = 0
	self.online_demitation_pass_time = 0
	self.last_calc_online_reward_time = 0

	self.add_equip_info = {}
	self.eat_equip_info = {}
	self.offline_zhaohui_info = {}
	self.offline_zhaohui_has_opened = false

	self.start_guaji_time = 0
	self.update_guaji_data_time = 0

	self.is_alearday_hint = false

	--离线挂机卡表只是为了判断是否是离线挂机卡用的
	self.offline_card_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("rest_auto").offline_guaji_card, "is_hang_up_card")
	self.other_cfg = ConfigManager.Instance:GetAutoConfig("rest_auto").other[1]
	self.meditation_pos_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("rest_auto").meditation_pos, "scene_id")
	self:InitSitInfo()

	self:GetOffLineCardTime()

	RemindManager.Instance:Register(RemindName.TianShenLiLian, BindTool.Bind(self.GetLiLianRemind, self))
end

function OfflineRestWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.TianShenLiLian)

	OfflineRestWGData.Instance = nil
	self.is_alearday_hint = nil
end

function OfflineRestWGData:GetOtherCfg()
	return self.other_cfg
end

--获取离线挂机卡的时间
function OfflineRestWGData:GetOffLineCardTime()
	self.offline_id = {}
	self.offline_idlist = {}
	local card_cfg = ConfigManager.Instance:GetAutoConfig("rest_auto").offline_guaji_card
	if nil == card_cfg then
		return
	end

	for k,v in pairs(card_cfg) do
		self.offline_id[v.is_hang_up_card] = v.time * 3600
		self.offline_idlist[k] = v.is_hang_up_card
	end
end

--获取离线挂机卡的ID
function OfflineRestWGData:GetOffLineCardItemId()
	return self.offline_idlist
end

function OfflineRestWGData:GetOfflineRestLimitLevel()
	return ConfigManager.Instance:GetAutoConfig("rest_auto").other[1].hang_limit_level
end

function OfflineRestWGData:SetOfflineRestData(protocol)
	-- if self.update_guaji_data_time > 0 then
	-- 	local  diff_time = math.floor((protocol.remain_offline_rest_time - self.remain_offline_rest_time)/3600)
	-- 	if diff_time > 0 then
	-- 		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.OfflineRest.AddOfflineTime, diff_time))
	-- 	end
	-- end
	self.update_guaji_data_time = Status.NowTime

	self.notify_reson = protocol.notify_reson
	self.get_exp_per_minute = protocol.get_exp_per_minute
	self.get_exp_per_minute_pank_value = protocol.get_exp_per_minute_pank_value
	self.remain_offline_rest_time = protocol.remain_offline_rest_time
	self.pass_offline_rest_time = protocol.pass_offline_rest_time
	self.total_offline_time = protocol.total_offline_time
	self.last_calc_online_reward_time = protocol.last_calc_online_reward_time
	self.online_last_add_exp = protocol.online_last_add_exp
	self.online_demitation_pass_time = protocol.online_demitation_pass_time
	self.old_level = protocol.old_level
	self.new_level = protocol.new_level

	self.add_exp = protocol.add_exp
	self.add_coin = protocol.add_coin
	self.total_cur_add_exp_per = protocol.total_cur_add_exp_per
	self.add_fight_soul_exp = protocol.add_fight_soul_exp

	self.add_equip_info = protocol.add_equip_info
	self.eat_equip_info = protocol.eat_equip_info
	self.put_index_list = protocol.put_index_list

	--排序操作
	self:ItemSortList()

	if self.pass_offline_rest_time > 1800 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.GUAJI_LILIAN, 1, function ()
			ViewManager.Instance:Open(GuideModuleName.TianShenLiLianView)
			ViewManager.Instance:Open(GuideModuleName.OfflineRestView)
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.GUAJI_LILIAN, 0)
	end
end

--对物品做一个排序
function OfflineRestWGData:ItemSortList()
	-- 魂灵经验获得展示
	if self.put_index_list ~= nil and self.add_fight_soul_exp > 0 then
		local fs_exp_itemid = FightSoulWGData.Instance:GetFightSoulExpItemId()
		local item_data = {item_id = fs_exp_itemid, num = self.add_fight_soul_exp, param = {star_level = 0}}
		table.insert(self.put_index_list, item_data)
	end

	local role_prof = RoleWGData.Instance:GetRoleProf()
	if role_prof and self.put_index_list and not IsEmptyTable(self.put_index_list) then
		for k,v in pairs(self.put_index_list) do
			v.sort_num = self:GetItemSortNum(v.item_id, v.param.star_level)
		end
		table.sort(self.put_index_list, SortTools.KeyUpperSorter("sort_num"))
	end
end

function OfflineRestWGData:GetItemSortNum(item_id, star_level)
	local sort_num = 0
	local cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if cfg == nil then
		print_error(cfg, item_id)
		return 0
	end

	local fs_exp_itemid = FightSoulWGData.Instance:GetFightSoulExpItemId()

	if cfg.sort_special ~= nil and cfg.sort_special ~= 0 then
		sort_num = sort_num + cfg.sort_special + (1e+11)
	elseif item_id == fs_exp_itemid then
		sort_num = sort_num + cfg.color + (1e+12)
	else
		if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			sort_num = sort_num + 4 * (1e+10)
			--[[if cfg.limit_prof == role_prof then
				sort_num = sort_num + (1e+9)
			end--]]
			sort_num = sort_num + cfg.color * (1e+9)
			sort_num = sort_num + cfg.order * (1e+7)
			sort_num = sort_num + star_level * (1e+6)
			sort_num = sort_num + (TAOBAO_BAG_SUBTYPE[cfg.sub_type] or 0) * (1e+4)
			sort_num = sort_num + item_id
		else
			if item_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
				sort_num = sort_num + 3 * (1e+10)
			elseif item_type == GameEnum.ITEM_BIGTYPE_GIF then
				sort_num = sort_num + 2 * (1e+10)
			elseif item_type == GameEnum.ITEM_BIGTYPE_OTHER then
				sort_num = sort_num + (1e+10)
			end
			sort_num = sort_num + cfg.color * (1e+8)
			sort_num = sort_num + (1e+6) - item_id
		end
	end
	return sort_num
end

function OfflineRestWGData:GetOfflineGuajiEquip()
	--if self.put_index_list and not IsEmptyTable(self.put_index_list)
		--and #self.put_index_list < TOGGLE_MAX then
		--for i=1, (TOGGLE_MAX - #self.put_index_list) do
			--table.insert(self.put_index_list, {})
		--end
	--end
	return self.put_index_list or {}
end

function OfflineRestWGData:GetRemainOfflineRestTime()
	return self.remain_offline_rest_time
end

-- 获取当前的离线挂机时间
function OfflineRestWGData:GetPassOfflineRestTime()
	return self.pass_offline_rest_time
end

function OfflineRestWGData:IsOfflineItem(item_id)
	return self.offline_id[item_id]
end

function OfflineRestWGData:GetExpPerMinute()
	return self.get_exp_per_minute
end

function OfflineRestWGData:GetExpPerMinutePankValue()
	return self.get_exp_per_minute_pank_value
end

function OfflineRestWGData:GetTimeStr(time)
	local hour = math.floor(time / 3600)
	local minute = math.floor((time / 60) % 60)
	local second = math.floor(time % 60)
	local time_str = ""
	if hour > 0 or "" ~= time_str then
		time_str = time_str .. hour .. Language.Common.TimeList.h
	end
	if minute > 0 or "" ~= time_str then
		time_str = time_str .. minute .. Language.Common.TimeList.min
	end
	if second >= 0 or "" ~= time_str then
		time_str = time_str .. second .. Language.Common.TimeList.s
	end

	return time_str
end

function OfflineRestWGData:GetNoSecondTimeStr(time)
	local hour = math.floor(time / 3600)
	local minute = math.ceil((time / 60) % 60)
	local time_str = ""
	if hour > 0 or "" ~= time_str then
		time_str = time_str .. hour .. Language.Common.TimeList.h
	end
	if minute >= 0 or "" ~= time_str then
		time_str = time_str .. minute .. Language.Common.TimeList.min
	end

	return time_str
end

function OfflineRestWGData:IsOverstep(item_id, item_num)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	--73 是挂机卡的使用类型
	if item_cfg and item_cfg.use_type == 73 then
		local remain_offline_rest_time = OfflineRestWGData.Instance:GetRemainOfflineRestTime() or 0
		local can_add_time = 20*3600 - remain_offline_rest_time
		local cur_item_add_time = tonumber(item_cfg.param1)
		local can_add_num = math.floor(can_add_time/cur_item_add_time)
		return can_add_num < item_num
	end
	return false
end

function OfflineRestWGData:NeedUserNum(item_id)
	for k,v in pairs(self.offline_id) do
		if k == item_id then
			return math.floor((OfflineRestWGData.OfflineTotalTime - self.remain_offline_rest_time) / v)
		end
	end
	return 1
end

function OfflineRestWGData:CanShowGuaJiEfficiency()
	local scene_type = Scene.Instance:GetSceneType()
	return Status.NowTime > self.start_guaji_time + 60 and self.get_exp_per_minute > 0 and
		Status.NowTime < self.update_guaji_data_time + 60 and scene_type ~= SceneType.Kf_Honorhalls
end

function OfflineRestWGData:SetStartGuajiTime(start_guaji_time)
	self.start_guaji_time = start_guaji_time
end

-- 暂时注释掉 判断离线挂机时间是否大于20个小时，是否要做快速使用的弹出
-- function OfflineRestWGData:IsOpenQuickUseView()
-- 	local shenyu_time = self:GetRemainOfflineRestTime() or 0
-- 	local hour = math.floor(shenyu_time / 3600)
-- 	if OfflineRestWGData.OfflineTotalTimeHour > hour then
-- 		return true
-- 	end

-- 	return false

-- end

--是否是离线挂机卡
function OfflineRestWGData:IsOfflineGuaJiCard(item_id)
	if nil == item_id then
		return false
	end

	if self.offline_card_cfg and self.offline_card_cfg[item_id] then
		return true
	end

	return false
end


------------------------打坐---------------------------------
function OfflineRestWGData:InitSitInfo()
	local area_str = self.other_cfg.sit_area
	self.sit_area_info = nil

	if area_str ~= nil and area_str ~= "" and area_str ~= 0 then
		local t = Split(area_str, "#")
		if t ~= nil and #t == 5 then
			self.sit_area_info = {}
			self.sit_area_info.scene_id = tonumber(t[1])
			for i = 2, 5 do
				local s = t[i]
				local pos_t = Split(s, ",")
				if pos_t ~= nil and #pos_t == 2 then
					self.sit_area_info["pos_" .. i - 1] = {x = tonumber(pos_t[1]), y = tonumber(pos_t[2])}
				end
			end
		end
	end

	self.sit_area_random_list = {}
end

function OfflineRestWGData:GetSitAreaInfo()
	return self.sit_area_info
end

function OfflineRestWGData:SetSitRewardInfo(protocol)
	self.lilian_item_count = protocol.item_count
	self.lilian_exp_xiaolv = protocol.exp_xiaolv
end

function OfflineRestWGData:GetLiLianItemCount()
	return self.lilian_item_count or 0
end

function OfflineRestWGData:GetLiLianExpXiaoLv()
	return self.lilian_exp_xiaolv or 0
end

function OfflineRestWGData:GetSitLingLiXiaoLv()
	local add_s = self.other_cfg.second_add_xiuwei_lingli or 0
	if self.other_cfg.xiuwei_interval > 0 then
		add_s = add_s / self.other_cfg.xiuwei_interval
	else
		add_s = 0
	end

	return add_s * 60
end

function OfflineRestWGData:GetRewardNotifyReason()
	return self.notify_reson or 0
end

function OfflineRestWGData:TryGetCacheInfo(scene_id, x, y, r)
	local is_cache = true
	if self.sit_area_random_list[scene_id] == nil then
		self.sit_area_random_list[scene_id] = {}
		is_cache = false
	end

	if self.sit_area_random_list[scene_id][x] == nil then
		self.sit_area_random_list[scene_id][x] = {}
		is_cache = false
	end

	if self.sit_area_random_list[scene_id][x][y] == nil then
		self.sit_area_random_list[scene_id][x][y] = {}
		is_cache = false
	end

	if not is_cache then
		for i = 1, r * 2 do
			for j = 1, r * 2 do
				local t = {x = x, y = y}
				if i < r then
					t.x = t.x - i
				elseif i > r then
					t.x = t.x + (i - r)
				end

				if j < r then
					t.y = t.y - j
				elseif j > r then
					t.y = t.y + (j - r)
				end

				if not AStarFindWay:IsBlock(t.x, t.y) then
					table.insert(self.sit_area_random_list[scene_id][x][y], t)
				end
			end
		end
	end

	local t = self.sit_area_random_list[scene_id][x][y]
	local index = math.random(1, #t)
	return t[index]
end

function OfflineRestWGData:GetSitPosInfo(index)
	local info = nil
	if self.sit_area_info == nil then
		return info
	end

	local cur_scene_id = Scene.Instance:GetSceneId()
	local t = self.meditation_pos_cfg[self.sit_area_info.scene_id]
	if t ~= nil and #t > 0 then
		local index = index or math.random(1, #t)
		local r_pos = t[index]
		if cur_scene_id ~= self.sit_area_info.scene_id then
			info = {scene_id = self.sit_area_info.scene_id, x = r_pos.pos_x, y = r_pos.pos_y, is_need_move = true, index = index}
		else
			local pos = self:TryGetCacheInfo(r_pos.scene_id, r_pos.pos_x, r_pos.pos_y, r_pos.radius)
			if pos == nil then
				print_error("取随机打坐点失败", r_pos)
				return
			end

			info = {scene_id = r_pos.scene_id, x = pos.x, y = pos.y, is_need_move = false}
		end
	end

	return info
end

function OfflineRestWGData:GetOfflineZhaoHuiCostItem()
	return self.other_cfg and self.other_cfg.exp_item
end

function OfflineRestWGData:OnOfflineHangExpFindInfo(protocol)
	local info = {}
	info.find_hour = protocol.find_hour
	info.find_exp = protocol.find_exp
	self.offline_zhaohui_info = info
end

function OfflineRestWGData:GetOfflineZhaoHuiInfo()
	return self.offline_zhaohui_info or {}
end

--记录本次登陆是否被打开过
function OfflineRestWGData:SetOfflineZhaoHuiHasOpened(has_open)
	self.offline_zhaohui_has_opened = has_open
end

function OfflineRestWGData:GetOfflineZhaoHuiHasOpened()
	return self.offline_zhaohui_has_opened
end

--数据是否可找回
function OfflineRestWGData:GetOfflineZhaoHuiCanFind()
	if IsEmptyTable(self.offline_zhaohui_info) then
		return false
	end
	local find_hour = self.offline_zhaohui_info.find_hour or 0
	return find_hour > 0
end

--离线经验找回道具消耗是否足够
function OfflineRestWGData:CheckFindCostEnough()
	if IsEmptyTable(self.offline_zhaohui_info) then
		return false
	end
	local find_hour = self.offline_zhaohui_info.find_hour or 0
	local cost_item_id = self:GetOfflineZhaoHuiCostItem() or 0
	if find_hour > 0 and cost_item_id > 0 then
		return ItemWGData.Instance:GetItemNumInBagById(cost_item_id) >= find_hour
	end

	return false
end

function OfflineRestWGData:GLiLianShowReward()
	local role_level = RoleWGData.Instance:GetAttr("level")
	local cfg = ConfigManager.Instance:GetAutoConfig("rest_auto").lilian_show
	local drop_id
	for k,v in ipairs(cfg) do
		if role_level >= v.min_level and role_level <= v.max_level then
			drop_id = v.dropid1
			break
		end
	end

	return ItemWGData.Instance:GetDropAllShowItemList(drop_id)
end

function OfflineRestWGData:GLiLianShowRewardByFb(fb_level)
	local cfg = ConfigManager.Instance:GetAutoConfig("rest_auto").lilian_show
	local drop_id, equip_order
	for k,v in pairs(cfg) do
		if fb_level >= v.min_exp_west_level and fb_level <= v.max_exp_west_level then
			drop_id = v.dropid1
			equip_order = v.now_level_order
			break
		end
	end

	return ItemWGData.Instance:GetDropAllShowItemList(drop_id), equip_order
end

function OfflineRestWGData:GetLiLianRemind()
	local lilian_is_open = FunOpen.Instance:GetFunIsOpened(FunName.TianShenLiLianView)
	if not lilian_is_open then
		return 0
	end

	-- local count = self:GetLiLianItemCount()
	-- if count > 0 then
	-- 	return 1
	-- end

	-- 设置挂机时间
	local other_cfg = self:GetOtherCfg()
	local hang_max_offlie_time = other_cfg.hang_max_offlie_time or 0
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local pass_offline_time = self:GetPassOfflineRestTime()
	local last_calc_online_reward_time = self.last_calc_online_reward_time
	local leiji_offline_time = server_time > last_calc_online_reward_time and (server_time - last_calc_online_reward_time) or 0
	-- 刷新宝箱状态
	local progress_value = leiji_offline_time / hang_max_offlie_time
	local has_red = progress_value >= 0.1

	if has_red then
			return 1
	end

	return 0
end