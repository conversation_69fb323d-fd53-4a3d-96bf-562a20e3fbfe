﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.SceneManagement;

public class RuntimeGUIResMgr : Nirvana.Singleton<RuntimeGUIResMgr>
{
    private Dictionary<string, AssetBundle> bundleDic = new Dictionary<string, AssetBundle>();
    private AssetBundleManifest manifest;
    private string updateUrl;
    private Dictionary<string, string> md5Dic = new Dictionary<string, string>();

    public void SetUpdateUrl(string updateUrl)
    {
        this.updateUrl = updateUrl;
    }

    public void Startup()
    {
        if (string.IsNullOrEmpty(this.updateUrl))
        {
            Debug.LogError("[RuntimeGUIResMgr] startup 失败，没有指定UpdateUrl");
            return;
        }
        this.RequestManifest();
        this.RequestMd5();
    }

    public void LoadScene(string sceneName)
    {
        string bundleName = string.Format("scenes/map/{0}", sceneName.ToLower());
        this.LoadAssetBundle(bundleName);
        SceneManager.LoadScene(sceneName, LoadSceneMode.Single);
        Scene scene = SceneManager.GetSceneByName(sceneName);
        if (null == scene || !scene.IsValid())
        {
            Debug.LogFormat("加载场景失败, {0}", sceneName);
            return;
        }

        Scheduler.Delay(() =>
        {
            GameObject[] gameObjects = scene.GetRootGameObjects();
            Debug.LogFormat("加载场景成功 {0} {1}", scene.name, gameObjects.Length);
            GameObject mainRoot = GameObject.Find("Main");
            mainRoot.GetOrAddComponent<RuntimeSceneDriver>();
        }, 0.5f);
    }

    public GameObject LoadPrefab(string bundleName, string assetName)
    {
        AssetBundle assetBundle = this.LoadAssetBundle(bundleName);
        if (null == assetBundle)
        {
            Debug.LogErrorFormat("[RuntimeGUIResMgr] LoadGameObject 失败，不存在Bundle, {0}, {1}", bundleName, assetName);
            return null;
        }

        var prefab = assetBundle.LoadAsset<GameObject>(assetName);
        if (null == prefab)
        {
            Debug.LogErrorFormat("[RuntimeGUIResMgr] LoadGameObject 失败，不存在资源, {0}, {1}", bundleName, assetName);
            return null;
        }

        return prefab;
    }

    private void RequestManifest()
    {
        string url = Path.Combine(updateUrl, "AssetBundle");
        Debug.LogFormat("[RuntimeGUIResMgr] RequestManifest, {0}", url);

        var www = UnityWebRequest.Get(url);
        www.SendWebRequest();
        while (!www.isDone) { };

        string cacheFilePath = Path.Combine(Application.persistentDataPath, "BundleCache/AssetBundle");
        RuntimeAssetHelper.WriteWebRequestData(cacheFilePath, www);
        www.Dispose();

        AssetBundle assetBundle = AssetBundle.LoadFromFile(cacheFilePath);
        manifest = assetBundle.LoadAsset<AssetBundleManifest>("AssetBundleManifest");
    }

    private void RequestMd5()
    {
        string url = Path.Combine(updateUrl, "md5_list.txt");
        Debug.LogFormat("[RuntimeGUIResMgr] RequestMd5, {0}", url);
        var www = UnityWebRequest.Get(url);
        www.SendWebRequest();
        while (!www.isDone) { };
        string cacheFilePath = string.Format("{0}/BundleCache/md5_list.txt", Application.persistentDataPath);
        RuntimeAssetHelper.WriteWebRequestData(cacheFilePath, www);
        www.Dispose();

        string[] md5List = File.ReadAllLines(cacheFilePath);
        for (int i = 0; i < md5List.Length; i++)
        {
            string[] ary = md5List[i].Split(' ');
            string bundlePath = ary[0];
            string md5 = ary[1];
            md5Dic.Add(bundlePath, md5);
        }
    }

    private string GetAssetVersion(string bundleName)
    {
        if (!md5Dic.ContainsKey(bundleName)) return string.Empty;

        return md5Dic[bundleName];
    }

    private void TryDownloadBundle(string bundleName)
    { 
        string assetPath = GetAssetPath(bundleName);
        if (string.IsNullOrEmpty(assetPath)) return;

        if (File.Exists(assetPath))
        {
            return;
        }

        string url = string.Format("{0}/{1}", updateUrl, bundleName);
        Debug.LogFormat("[RuntimeGUIResMgr] TryDownloadBundle, {0}", url);
        var www = UnityWebRequest.Get(url);
        www.SendWebRequest();
        while (!www.isDone) { };
        RuntimeAssetHelper.WriteWebRequestData(assetPath, www);
        www.Dispose();
    }

    private string GetCachePath(string bundleName, string version)
    {
        string cachePath = string.Format("{0}/BundleCache/{1}-{2}", Application.persistentDataPath, bundleName, version);
        return cachePath;
    }

    private string GetStreamPath(string bundleName, string version)
    {
        string cachePath = string.Format("{0}/AssetBundle/{1}-{2}", Application.streamingAssetsPath, bundleName, version);
        return cachePath;
    }

    private string GetAssetPath(string bundleName)
    {
        string version = this.GetAssetVersion(bundleName);
        if (string.IsNullOrEmpty(version))
        {
            Debug.LogErrorFormat("[RuntimeResMgr]不存在资源的版本号, {0}", bundleName);
            return string.Empty;
        }

        string path = this.GetStreamPath(bundleName, version);
        if (File.Exists(path))
        {
            return path;
        }

        return this.GetCachePath(bundleName, version);
    }

    private AssetBundle LoadAssetBundle(string bundleName)
    {
        string bundlePath = GetAssetPath(bundleName);
        if (string.IsNullOrEmpty(bundlePath))
        {
            Debug.LogErrorFormat("[RuntimeResMgr] LoadAssetBundle 不存在资源, {0}", bundleName);
            return null;
        }

        string[] deps = manifest.GetAllDependencies(bundleName);
        for (int i = 0; i < deps.Length; i++)
        {
            if (!bundleDic.ContainsKey(deps[i]))
            {
                string depPath = GetAssetPath(deps[i]);
                if (string.IsNullOrEmpty(depPath))
                {
                    continue;
                }

                this.TryDownloadBundle(deps[i]);
                var depAb = AssetBundle.LoadFromFile(depPath);
                bundleDic.Add(deps[i], depAb);
            }
        }

        AssetBundle assetBundle;
        if (!bundleDic.TryGetValue (bundleName, out assetBundle))
        {
            this.TryDownloadBundle(bundleName);
            assetBundle = AssetBundle.LoadFromFile(bundlePath);
            bundleDic.Add(bundleName, assetBundle);
        }

        return assetBundle;
    }
}
