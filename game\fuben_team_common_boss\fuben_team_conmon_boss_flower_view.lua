FuBenTeamCommonBossFlowerView = FuBenTeamCommonBossFlowerView or BaseClass(SafeBaseView)

function FuBenTeamCommonBossFlowerView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/sendflower_ui_prefab", "layout_spe_fb_flowerback")
	--设置是否自动提醒
	self.noremind = false
end

function FuBenTeamCommonBossFlowerView:__delete()

end

function FuBenTeamCommonBossFlowerView:SetDataAndOpen(flower_info)
	self.flower_info = flower_info

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function FuBenTeamCommonBossFlowerView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.daxie_btn, BindTool.Bind1(self.ClickDaxie, self))
	XUI.AddClickEventListener(self.node_list.sendback_btn, BindTool.Bind1(self.ClickSendBack, self))
    XUI.AddClickEventListener(self.node_list.add_friend_btn, BindTool.Bind1(self.OnClickAddFriend, self))
end 

function FuBenTeamCommonBossFlowerView:ReleaseCallBack()	
end

function FuBenTeamCommonBossFlowerView:OnFlush()
    if not self.flower_info then
        return
    end

	self.node_list.rich_tip_text.text.text = string.format(Language.FuBenTeamCommonBoss.ReceiveFlowerTxt, self.flower_info.player_name)

    local role_id = 0
    if self.flower_info.player_is_robot == 1 then
        role_id = 0
    else
        role_id = self.flower_info.player_uuid.temp_low
    end

    local is_friend = role_id ~= 0 and SocietyWGData.Instance:CheckIsFriend(role_id)
    self.node_list.add_friend_btn:SetActive(not is_friend)
    self.node_list.sendback_btn:SetActive(is_friend)
    self.node_list.daxie_btn:SetActive(is_friend)
end

function FuBenTeamCommonBossFlowerView:ClickDaxie()
    if not self.flower_info then
        return
    end

    local role_id = 0
    if self.flower_info.player_is_robot == 1 then
        role_id = 0
    else
        role_id = self.flower_info.player_uuid.temp_low
    end

    if role_id > 0 then
        ChatWGCtrl.Instance:SendSingleChat(role_id, Language.FuBenTeamCommonBoss.ThankForFlower, CHAT_CONTENT_TYPE.TEXT, true)
	    SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenTeamCommonBoss.ReceiveSuc)
    end

	self:Close()
end

function FuBenTeamCommonBossFlowerView:ClickSendBack()
    if not self.flower_info then
        return
    end

    local role_id = 0
    if self.flower_info.player_is_robot == 1 then
        role_id = 0
    else
        role_id = self.flower_info.player_uuid.temp_low
    end

    if role_id > 0 then
        FlowerWGCtrl.Instance:OpenSendFlowerView(role_id, self.flower_info.from_nameplayer_name, self.flower_info.sex, self.flower_info.prof)
    end
	
	self:Close()
end

function FuBenTeamCommonBossFlowerView:OnClickAddFriend()
    if not self.flower_info then
        return
    end


    local role_id = 0
    if self.flower_info.player_is_robot == 1 then
        role_id = 0
    else
        role_id = self.flower_info.player_uuid.temp_low
    end

	if role_id > 0 and role_id ~= RoleWGData.Instance:GetUUid().temp_low and (not SocietyWGData.Instance:CheckIsFriend(role_id)) then
		SocietyWGCtrl.Instance:IAddFriend(role_id)
	end

    SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.ApplyFriendSuccess)
    self:Close()
end