HolyHeavenlyDomainSetTargetView = HolyHeavenlyDomainSetTargetView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainSetTargetView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_set_target")
end

function HolyHeavenlyDomainSetTargetView:LoadCallBack()
    if not self.target_list then
        self.target_list = AsyncListView.New(HHDSetTargetItemRender, self.node_list.target_list)
    end
end

function HolyHeavenlyDomainSetTargetView:ReleaseCallBack()
    if self.target_list then
        self.target_list:DeleteMe()
        self.target_list = nil
    end
end

function HolyHeavenlyDomainSetTargetView:OnFlush()
    local data_list = HolyHeavenlyDomainWGData.Instance:GetSetTargetDataList()
    self.target_list:SetDataList(data_list)
end

HHDSetTargetItemRender = HHDSetTargetItemRender or BaseClass(BaseRender)

function HHDSetTargetItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_zhaoji, BindTool.Bind(self.OnClickZhaoJi, self))
    XUI.AddClickEventListener(self.node_list.btn_set_target, BindTool.Bind(self.OnClickSetTarget, self))
end

function HHDSetTargetItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(self.data.seq)

    if IsEmptyTable(city_info) then
        return
    end

    self.node_list.desc_city_name.text.text = self.data.city_name

    local owenr_usid = city_info.owner_usid
    local desc_city_owner = Language.HolyHeavenlyDomain.Neutral
    local city_owner_bundle, city_owner_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_zhanyyy16")
    
    local camp_id = HolyHeavenlyDomainWGData.Instance:GetCountryIdByServerUsid(owenr_usid)
    local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(camp_id)

    desc_city_owner = camp_cfg and camp_cfg.camp_name or desc_city_owner

    if not IsEmptyTable(camp_cfg) then
        city_owner_bundle, city_owner_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_zhanyyy" .. camp_cfg.camp_icon)
    end

    self.node_list.img_city_owner.image:LoadSprite(city_owner_bundle, city_owner_asset, function ()
        self.node_list.img_city_owner.image:SetNativeSize()
    end)

    self.node_list.desc_city_owner.text.text = desc_city_owner

    local atk_value = 0
    local dfen_value = 0
    local total_value = 0
    local siege_info_list = city_info.server_siege_list

	if not IsEmptyTable(siege_info_list) then
	    for k, v in pairs(siege_info_list) do
			-- 获取攻击方得服ID
			local usid = HolyHeavenlyDomainWGData.Instance:GetCampServerIdByCampSeq(k)

            if nil ~= usid then
                if owenr_usid == usid then
                    dfen_value = dfen_value + v
                else
                    atk_value = atk_value + v
                end
            end

            total_value = total_value + v
        end
    end

    local atk_value_str = (total_value > 0 and atk_value > 0) and (atk_value / total_value * 100) or 0
    local dfen_value_str = (total_value > 0 and dfen_value > 0) and (dfen_value / total_value * 100) or 0

    local slider_value = 0.5
    if atk_value ~= dfen_value and total_value > 0 then
        slider_value = (dfen_value > 0) and (dfen_value / total_value) or 0
    end

    self.node_list.slider_pro.slider.value = slider_value
    self.node_list.desc_den_value.text.text = string.format(Language.HolyHeavenlyDomain.defensive, dfen_value_str)
    self.node_list.desc_atk_value.text.text = string.format(Language.HolyHeavenlyDomain.Offensive, atk_value_str)
end

function HHDSetTargetItemRender:OnClickZhaoJi()
    if IsEmptyTable(self.data) then
        return
    end

    local city_seq =self.data.seq
	local is_zhaoji_target = HolyHeavenlyDomainWGData.Instance:GetAssembleCityFlagBySeq(city_seq)

	if is_zhaoji_target then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.IsAssembleCityNow)
	else
		local id_cd_time, _, time = HolyHeavenlyDomainWGData.Instance:IsAssembleCityCD()

		if not id_cd_time then
			HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.ASSEMBLE_CITY, city_seq)
            HolyHeavenlyDomainWGCtrl.Instance:CloseSetTargetView()
		else
			local time_str = TimeUtil.FormatTimeLanguage2(time)
			local reason = string.format(Language.HolyHeavenlyDomain.AssembleCityCd, time_str)
			SysMsgWGCtrl.Instance:ErrorRemind(reason)
		end
	end
end

function HHDSetTargetItemRender:OnClickSetTarget()
    if IsEmptyTable(self.data) then
        return
    end

    local city_seq =self.data.seq
	local can_set_target, reason = HolyHeavenlyDomainWGData.Instance:IsCityCanSetTarget(city_seq)
	if can_set_target then
		local is_convene = HolyHeavenlyDomainWGData.Instance:IsConveneCity(city_seq)
		if is_convene then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.IsConveneCityNow)
		else
			HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.CONVENE_CITY, city_seq)
            HolyHeavenlyDomainWGCtrl.Instance:CloseSetTargetView()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(reason)
	end
end