﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class System_GCWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("GC");
		<PERSON><PERSON>RegFunction("GetAllocatedBytesForCurrentThread", GetAllocatedBytesForCurrentThread);
		<PERSON><PERSON>unction("AddMemoryPressure", AddMemoryPressure);
		<PERSON><PERSON>unction("RemoveMemoryPressure", RemoveMemoryPressure);
		L.RegFunction("GetGeneration", GetGeneration);
		<PERSON><PERSON>RegFunction("Collect", Collect);
		<PERSON><PERSON>RegFunction("CollectionCount", CollectionCount);
		<PERSON><PERSON>RegFunction("KeepAlive", KeepAlive);
		<PERSON><PERSON>unction("WaitForPendingFinalizers", WaitForPendingFinalizers);
		<PERSON>.RegFunction("SuppressFinalize", SuppressFinalize);
		<PERSON><PERSON>RegFunction("ReRegisterForFinalize", ReRegisterForFinalize);
		<PERSON><PERSON>RegFunction("GetTotalMemory", GetTotalMemory);
		<PERSON><PERSON>RegFunction("RegisterForFullGCNotification", RegisterForFullGCNotification);
		L.RegFunction("CancelFullGCNotification", CancelFullGCNotification);
		L.RegFunction("WaitForFullGCApproach", WaitForFullGCApproach);
		L.RegFunction("WaitForFullGCComplete", WaitForFullGCComplete);
		L.RegFunction("TryStartNoGCRegion", TryStartNoGCRegion);
		L.RegFunction("EndNoGCRegion", EndNoGCRegion);
		L.RegVar("MaxGeneration", get_MaxGeneration, null);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAllocatedBytesForCurrentThread(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			long o = System.GC.GetAllocatedBytesForCurrentThread();
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddMemoryPressure(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			long arg0 = LuaDLL.tolua_checkint64(L, 1);
			System.GC.AddMemoryPressure(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveMemoryPressure(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			long arg0 = LuaDLL.tolua_checkint64(L, 1);
			System.GC.RemoveMemoryPressure(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGeneration(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes<System.WeakReference>(L, 1))
			{
				System.WeakReference arg0 = (System.WeakReference)ToLua.ToObject(L, 1);
				int o = System.GC.GetGeneration(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<object>(L, 1))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = System.GC.GetGeneration(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.GC.GetGeneration");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Collect(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				System.GC.Collect();
				return 0;
			}
			else if (count == 1)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				System.GC.Collect(arg0);
				return 0;
			}
			else if (count == 2)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				System.GCCollectionMode arg1 = (System.GCCollectionMode)ToLua.CheckObject(L, 2, typeof(System.GCCollectionMode));
				System.GC.Collect(arg0, arg1);
				return 0;
			}
			else if (count == 3)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				System.GCCollectionMode arg1 = (System.GCCollectionMode)ToLua.CheckObject(L, 2, typeof(System.GCCollectionMode));
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				System.GC.Collect(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 4)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				System.GCCollectionMode arg1 = (System.GCCollectionMode)ToLua.CheckObject(L, 2, typeof(System.GCCollectionMode));
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 4);
				System.GC.Collect(arg0, arg1, arg2, arg3);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.GC.Collect");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CollectionCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			int o = System.GC.CollectionCount(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int KeepAlive(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			object arg0 = ToLua.ToVarObject(L, 1);
			System.GC.KeepAlive(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WaitForPendingFinalizers(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			System.GC.WaitForPendingFinalizers();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SuppressFinalize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			object arg0 = ToLua.ToVarObject(L, 1);
			System.GC.SuppressFinalize(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReRegisterForFinalize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			object arg0 = ToLua.ToVarObject(L, 1);
			System.GC.ReRegisterForFinalize(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTotalMemory(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			long o = System.GC.GetTotalMemory(arg0);
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RegisterForFullGCNotification(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			System.GC.RegisterForFullGCNotification(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CancelFullGCNotification(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			System.GC.CancelFullGCNotification();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WaitForFullGCApproach(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				System.GCNotificationStatus o = System.GC.WaitForFullGCApproach();
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 1)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				System.GCNotificationStatus o = System.GC.WaitForFullGCApproach(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.GC.WaitForFullGCApproach");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WaitForFullGCComplete(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				System.GCNotificationStatus o = System.GC.WaitForFullGCComplete();
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 1)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				System.GCNotificationStatus o = System.GC.WaitForFullGCComplete(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.GC.WaitForFullGCComplete");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryStartNoGCRegion(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				long arg0 = LuaDLL.tolua_checkint64(L, 1);
				bool o = System.GC.TryStartNoGCRegion(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<bool>(L, 2))
			{
				long arg0 = LuaDLL.tolua_checkint64(L, 1);
				bool arg1 = LuaDLL.lua_toboolean(L, 2);
				bool o = System.GC.TryStartNoGCRegion(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<long>(L, 2))
			{
				long arg0 = LuaDLL.tolua_checkint64(L, 1);
				long arg1 = LuaDLL.tolua_toint64(L, 2);
				bool o = System.GC.TryStartNoGCRegion(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3)
			{
				long arg0 = LuaDLL.tolua_checkint64(L, 1);
				long arg1 = LuaDLL.tolua_checkint64(L, 2);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				bool o = System.GC.TryStartNoGCRegion(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.GC.TryStartNoGCRegion");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EndNoGCRegion(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			System.GC.EndNoGCRegion();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxGeneration(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, System.GC.MaxGeneration);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

