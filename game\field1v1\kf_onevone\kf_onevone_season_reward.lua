KFOneVOneSeasonReward = KFOneVOneSeasonReward or BaseClass(SafeBaseView)

function KFOneVOneSeasonReward:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_season_reward")
	self:SetMaskBg(true)
end

function KFOneVOneSeasonReward:LoadCallBack()
    self:SetSecondView(nil, self.node_list["size"])
  
	self.node_list["title_view_name"].text.text = Language.Kuafu1V1.RankRewardTitle
	self.rank_reward_list = AsyncListView.New(KF1v1RankRewardItemRender, self.node_list.rank_reward_list)

end

function KFOneVOneSeasonReward:ReleaseCallBack()
	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end
end

function KFOneVOneSeasonReward:OnFlush( param )
	local ranking_data = KFOneVOneWGData.Instance:GetRankRewardCfg()
	self.node_list.rank_reward_list:SetActive(true)
	self.rank_reward_list:SetDataList(ranking_data)

	-- local server_time = TimeWGCtrl.Instance:GetServerTime()
	-- local time_table = os.date("*t", server_time)
	-- local month_day_count = os.date("%d",os.time({year=time_table.year,month=time_table.month+1,day=0}))
	-- local count_shengyu = month_day_count - time_table.day + 1
	-- self.node_list.join_count.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc2,time_table.month,month_day_count,count_shengyu)
end


--赛季奖励
KF1v1RankRewardItemRender = KF1v1RankRewardItemRender or BaseClass(BaseRender)
function KF1v1RankRewardItemRender:__init()
	self.item_list = AsyncListView.New(KF1V1RankRewardItemCell, self.node_list.item_list)
	XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.ClickRewardBtn,self))
end
function KF1v1RankRewardItemRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end
function KF1v1RankRewardItemRender:OnFlush()
	if not self.data then return end
	--奖励物品
	local data_list = {}
	for i=0,#self.data.reward_item do
		table.insert(data_list, self.data.reward_item[i])
	end

	local is_red = KFOneVOneWGData.Instance:GeRankRewardIsGet(self.data.seq)
	self.node_list["red"]:SetActive(is_red)
	local is_get = KFOneVOneWGData.Instance:GetRankRewardFetchFlag(self.data.seq)
	self.node_list["reward_btn"]:SetActive(is_red)
	self.node_list["ylq_img"]:SetActive(is_get)
	-- self.node_list["wdc_img"]:SetActive(not is_get and not is_red)

	self.item_list:SetDataList(data_list, 0)
	self.node_list.bg:SetActive(self.index % 2 == 1)
	self.node_list.rank_text.text.text = self.data.title_des
end

function KF1v1RankRewardItemRender:ClickRewardBtn()
	if not self.data then return end 
	local seq = self.data.seq
	local is_red = KFOneVOneWGData.Instance:GeRankRewardIsGet(self.data.seq)
	if is_red then
		local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_FETCH_RANK_REWARD
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type,seq)
	end
end

---------------------------

KF1V1RankRewardItemCell = KF1V1RankRewardItemCell or BaseClass(BaseRender)
function KF1V1RankRewardItemCell:__init()
	self.cell = ItemCell.New(self.node_list.pos)
end
function KF1V1RankRewardItemCell:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end
function KF1V1RankRewardItemCell:OnFlush()
	self.cell:SetData(self.data)
end