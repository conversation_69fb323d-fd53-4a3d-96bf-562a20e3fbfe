HolyBeastCallView = HolyBeastCallView or BaseClass(SafeBaseView)

local DRAW_TYPE = {
    ONE = 1,
    MORE = 2,
}

local tween_circle1_rotatepos = Vector3(0, 0, 360)
local tween_circle2_rotatepos = Vector3(0, 0, -360)

function HolyBeastCallView:__init()
    self.view_style = ViewStyle.Full
	self:SetMaskBg()
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/holy_beast_call_ui_prefab", "layout_holy_beast_call_view")
end

function HolyBeastCallView:LoadCallBack()
    if not self.reward_item_list then
        self.reward_item_list = {}

        for i = 1, 3 do
            self.reward_item_list[i] = ItemCell.New(self.node_list["reward_item_root_" .. i])
        end
    end

    if not self.item_show_list then
        self.item_show_list = {}

        for i = 1, 6 do
            local render = ItemCell.New(self.node_list["item_root_" .. i])
            render:SetShowCualityBg(false)
            render:SetCellBgEnabled(false)
            render:NeedDefaultEff(false)
            self.item_show_list[i] = render
        end
    end

    if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model_display)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    self.grade_cache = -1

    XUI.AddClickEventListener(self.node_list.btn_range, BindTool.Bind1(self.OnClickRange, self))
    XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind1(self.OnClickTips, self))
    XUI.AddClickEventListener(self.node_list.btn_daily_reward, BindTool.Bind1(self.OnClickDailyReward, self))
    XUI.AddClickEventListener(self.node_list.btn_draw_1, BindTool.Bind(self.OnClickDraw, self, DRAW_TYPE.ONE))
    XUI.AddClickEventListener(self.node_list.btn_draw_2, BindTool.Bind(self.OnClickDraw, self, DRAW_TYPE.MORE))
end

function HolyBeastCallView:ReleaseCallBack()
    if self.item_show_list then
        for k, v in pairs(self.item_show_list) do
            v:DeleteMe()
        end

        self.item_show_list = nil
    end

    if self.reward_item_list then
        for k, v in pairs(self.reward_item_list) do
            v:DeleteMe()
        end

        self.reward_item_list = nil
    end

    if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

    if CountDownManager.Instance:HasCountDown("holy_beast_call_time_count_dowm") then
		CountDownManager.Instance:RemoveCountDown("holy_beast_call_time_count_dowm")
	end

    if self.tween_circle1 then
        self.tween_circle1:Kill()
        self.tween_circle1 = nil
    end

    if self.tween_circle2 then
        self.tween_circle2:Kill()
        self.tween_circle2 = nil
    end

    self.grade_cache = nil
end

function HolyBeastCallView:CloseCallBack()
    RuleTip.Instance:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
end

function HolyBeastCallView:ShowIndexCallBack()
    self:TimeCountDown()
    self:InitTween()
end

function HolyBeastCallView:TimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HOLY_BEAST_CALL)
    self.node_list.act_time:CustomSetActive(false)

    if not IsEmptyTable(activity_data) then
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local invalid_time = 0

        if activity_data.status == ACTIVITY_STATUS.OPEN then
            invalid_time = activity_data.end_time
        end

        if invalid_time > server_time then
            if CountDownManager.Instance:HasCountDown("holy_beast_call_time_count_dowm") then
                CountDownManager.Instance:RemoveCountDown("holy_beast_call_time_count_dowm")
            end

            self.node_list.act_time.text.text = string.format(Language.HolyBeastCall.ActTimeCountDown, TimeUtil.FormatSecondDHM8(invalid_time - server_time))
            self.node_list.act_time:CustomSetActive(true)
            CountDownManager.Instance:AddCountDown("holy_beast_call_time_count_dowm",
            function (elapse_time, total_time)
                local valid_time = total_time - elapse_time
                if self.node_list.act_time then
                    -- self.login_act_date = TimeUtil.FormatUnixTime2Date()
                    local time_str = string.format(Language.HolyBeastCall.ActTimeCountDown, TimeUtil.FormatSecondDHM8(valid_time))
                    self.node_list.act_time.text.text = time_str
                end
            end,
            function ()
                if self.node_list.act_time then
                    self.node_list.act_time.text.text = ""
                    self.node_list.act_time:CustomSetActive(false)
                end
            end,
            invalid_time,
            nil, 1)
        end
    end
end

function HolyBeastCallView:InitTween()
    if self.tween_circle1 then
        self.tween_circle1:Restart()
    else
        self.tween_circle1 = self.node_list.circle1.transform:DORotate(tween_circle1_rotatepos, 10, DG.Tweening.RotateMode.FastBeyond360)
        self.tween_circle1:SetEase(DG.Tweening.Ease.Linear)
        self.tween_circle1:SetLoops(-1)
    end

    if self.tween_circle2 then
        self.tween_circle2:Restart()
    else
        self.tween_circle2 = self.node_list.circle2.transform:DORotate(tween_circle2_rotatepos, 10, DG.Tweening.RotateMode.FastBeyond360)
        self.tween_circle2:SetEase(DG.Tweening.Ease.Linear)
        self.tween_circle2:SetLoops(-1)
    end
end

function HolyBeastCallView:OnFlush()
    local draw_time, draw_limit_time = HolyBeastCallWGData.Instance:GetDrawTimeAndLimitTime()
    local color = draw_time >= draw_limit_time and COLOR3B.D_RED or COLOR3B.D_GREEN
    local limit_str = draw_limit_time >= 999999 and Language.HolyBeastCall.DrawTimeNoLimit or draw_limit_time
    local rmb_num = HolyBeastCallWGData.Instance:GetShengShowRmbNum()
    self.node_list.act_desc.text.text = string.format(Language.HolyBeastCall.ActDesc, COLOR3B.D_GREEN, rmb_num, color, draw_time, limit_str)
    local is_get_flag = HolyBeastCallWGData.Instance:IsGetDailyRewardFlag()
    self.node_list.can_get_daily_reward:CustomSetActive(not is_get_flag)
    self.node_list.cannot_get_daily_reward:CustomSetActive(is_get_flag)
    self.node_list.text_daily_reward.text.text = is_get_flag and Language.HolyBeastCall.DailyRewardTip or Language.HolyBeastCall.DailyARewardCanGetTip

    self:FlushDrawBtnState()

    local cur_grade = HolyBeastCallWGData.Instance:GetCurGrade()
    if cur_grade ~= self.grade_cache then
        self:FlushRewardItemData()
        self:FlushModel()
        self.grade_cache = cur_grade
    end
end

function HolyBeastCallView:FlushDrawBtnState()
    local item_cost_cfg = HolyBeastCallWGData.Instance:GetItemCostCfg()
    if not IsEmptyTable(item_cost_cfg) then
        for i = 1, 2 do
            local data = item_cost_cfg[i]
            if not IsEmptyTable(data) then
                local cost_item_id = data.cost_item_id
                local cost_item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
                local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
                local need_num = data.cost_item_num
                self.node_list["txt_buy_" .. i].text.text = string.format(Language.HolyBeastCall.DrawTimeDesc, data.draw_times)
                local can_draw = need_num <= item_num
                local draw_limit = HolyBeastCallWGData.Instance:IsHolyBeastCallDrawLimit()
                self.node_list["btn_red_" .. i]:CustomSetActive(can_draw and not draw_limit)
                local color = can_draw and COLOR3B.D_GREEN or COLOR3B.D_RED
                self.node_list["btn_red_num_" .. i].text.text = ToColorStr(item_num .. "/" .. need_num, color)

                if cost_item_cfg then
                    self.node_list["btn_icon_" .. i].image:LoadSprite(ResPath.GetItem(cost_item_cfg.icon_id))
                    self.node_list["btn_icon_" .. i].button:AddClickListener(BindTool.Bind(function ()
                        TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
                    end,self))
                end
            end
        end
    end
end

function HolyBeastCallView:FlushRewardItemData()
    local rate_list, reward_list = HolyBeastCallWGData.Instance:GetShowRewardItemList()

    if not IsEmptyTable(rate_list) then
        for i = 1, 3 do
            local data = (rate_list[i] or {}).item or {}

            if not IsEmptyTable(data) then
                self.reward_item_list[i]:SetData(data)
            end
        end
    end

    if not IsEmptyTable(reward_list) then
        for i = 1, 6 do
            local data = (reward_list[i] or {}).item or {}

            if not IsEmptyTable(data) then
                self.item_show_list[i]:SetData(data) 
            end
        end 
    end
end

function HolyBeastCallView:FlushModel()
    local model_cfg = HolyBeastCallWGData.Instance:GetCurModelCfg()
	if IsEmptyTable(model_cfg) then
		return
	end

    local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1

	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
	display_data.model_click_func = function ()
		TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	end

	self.model_display:SetData(display_data)

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.model_display.rect, pos_x, pos_y)

	if model_cfg["display_scale"] then
		local scale = model_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(self.node_list.model_display.transform, scale, scale, scale)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		self.node_list.model_display.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function HolyBeastCallView:OnClickRange()
    HolyBeastCallWGCtrl.Instance:OpenProbabilityView()
end

function HolyBeastCallView:OnClickTips()
    RuleTip.Instance:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	RuleTip.Instance:SetTitle(Language.HolyBeastCall.TipsTitle)
	RuleTip.Instance:SetContent(Language.HolyBeastCall.TipsContent, nil, nil, nil, true)
end

function HolyBeastCallView:OnClickDailyReward()
    local is_get_flag = HolyBeastCallWGData.Instance:IsGetDailyRewardFlag()

    if is_get_flag then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyBeastCall.DailyRewardTip)
    else
        HolyBeastCallWGCtrl.Instance:SendHolyBeastCallReq(OA_SHENGSHOU_DRAW_OPERATE_TYPE.GET_FREE_DRAW)
    end
end

function HolyBeastCallView:OnClickDraw(draw_type)
    HolyBeastCallWGCtrl.Instance:SetLastDrawType(draw_type)
    local item_cost_cfg = HolyBeastCallWGData.Instance:GetItemCostCfg()
    local cur_data = item_cost_cfg[draw_type] or {}

    if not IsEmptyTable(cur_data) then
        local cost_item_id = cur_data.cost_item_id
        local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
        local draw_time = cur_data.draw_times
        local cost_item_num = cur_data.cost_item_num

        local function draw_func()
            HolyBeastCallWGCtrl.Instance:SendHolyBeastCallReq(OA_SHENGSHOU_DRAW_OPERATE_TYPE.DRAW, draw_time)
        end

        if cost_item_num <= item_num then
            draw_func()
        else
            local once_cost_gold = cur_data.cost_gold_num / draw_time
            local once_cost_item = cost_item_num / draw_time
            local need_num = cost_item_num - item_num
            local need_gole = (need_num / once_cost_item) * once_cost_gold
            local item_name = item_cfg and item_cfg.name or ""
            local utem_color = ITEM_COLOR[item_cfg and item_cfg.color or 0] or GameEnum.ITEM_COLOR_WHITE
            local desc = string.format(Language.HolyBeastCall.DrawAlertDesc, utem_color, item_name, COLOR3B.GREEN, need_gole, COLOR3B.GREEN, need_num, COLOR3B.GREEN, draw_time)
            TipWGCtrl.Instance:OpenCheckTodayAlertTips(desc, function ()
                draw_func()
            end, "HolyBeastCallDraw", Language.Chat.DontTipToday)
        end
    end
end