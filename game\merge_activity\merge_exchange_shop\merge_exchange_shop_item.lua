-----------------------MergeExchangeShopItem-----------------------------------
MergeExchangeShopItem = MergeExchangeShopItem or BaseClass(BaseRender)

function MergeExchangeShopItem:__init()
	-- XUI.AddClickEventListener(self.node_list.click_cell, BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_exchange, BindTool.Bind(self.ClickExchange, self))
	XUI.AddClickEventListener(self.node_list.item_icon, BindTool.Bind(self.ClickStuff, self))
	
	
	self.item_cell = MergeExchangeShopItemCell.New(self.node_list.item)
end

function MergeExchangeShopItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function MergeExchangeShopItem:OnFlush()
	if self.data == nil then
		self.view:SetActive(false)
		return
	end
	self.view:SetActive(true)
	-- local exchange_data = ExchangeShopWGData.Instance:GetData()
	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id)
	self.node_list.name.text.text = self.data.name
	self.node_list.cost.text.text = GetRightColor(num .. "/" .. self.data.cost, num >= self.data.cost, COLOR3B.L_GREEN, COLOR3B.L_RED)
	local color = self.data.can_exchange_times > 0 and COLOR3B.C2 or COLOR3B.C3
	self.node_list.exchange_num.text.text = string.format(Language.MergeExchange.CanExchangeText, ToColorStr(self.data.can_exchange_times, color), ToColorStr(self.data.max_exchange_times, color))

	self.node_list.remind:SetActive(self.data.can_exchange_times > 0 and num >= self.data.cost)
	local config = ItemWGData.Instance:GetItemConfig(self.data.stuff_id)

	if config then
		local bundle, asset = ResPath.GetItem(config.icon_id)
		self.node_list.item_icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.item_icon.image:SetNativeSize()
		end)
	else
		print_error("[MergeExchangeShopItem] item_config is invaild, item_id is:", self.data.stuff_id)
	end


	self.node_list.btn_exchange:SetActive(self.data.can_exchange_times ~= 0)
	self.node_list.word_sold_out:SetActive(self.data.can_exchange_times == 0)

	self.item_cell:SetData(self.data.item)
end

function MergeExchangeShopItem:SetClickCallBack(event)
	self.event = event
end

function MergeExchangeShopItem:OnClick()
	if self.event then
		self.event(self)
	end
end

function MergeExchangeShopItem:ClickExchange()
	local num = self.data.item.num
	local empty = ItemWGData.Instance:GetEmptyNum()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)

	if empty < num and item_cfg and item_cfg.not_put_flag == 0 then
		RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
		return
	end

	local buy_func = function(num)
		TipWGCtrl.Instance:ShowGetItem(self.data.item)
		local buy_num = num or 1
		MergeExchangeShopWGCtrl.Instance:SendOperExchange(RAND_ACTIVITY_TYPE_CSA_CONVERT_OPERA_TYPE.CONVERT, self.data.seq, buy_num)
	end

	local tips_data = {}
	tips_data.title_view_name = Language.Common.ExchangeItemTipsTitle
	tips_data.item_id = self.data.item.item_id
	tips_data.expend_item_id = self.data.stuff_id
	tips_data.expend_item_num = self.data.cost
	tips_data.max_buy_count = self.data.can_exchange_times
	tips_data.is_show_limit = true
	TipWGCtrl.Instance:OpenCustomBuyItemTipsView(tips_data, buy_func)
end

function MergeExchangeShopItem:ClickStuff()
	TipWGCtrl.Instance:OpenItem({item_id = self.data.stuff_id}, ItemTip.FROM_NORMAL, nil)
end

MergeExchangeShopItemCell = MergeExchangeShopItemCell or BaseClass(ItemCell)

function MergeExchangeShopItemCell:OnFlush()
	ItemCell.OnFlush(self)
	if not self.data then return end
	--self:ShowItemActive()
end

