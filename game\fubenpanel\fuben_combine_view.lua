---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by 123.
--- DateTime: 2019/10/16 15:47
---
--副本合并
FuBenCombine = FuBenCombine or BaseClass(SafeBaseView)

function FuBenCombine:__init()
	self:SetMaskBg(true)
	self.view_name = "FuBenCombine"
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(600, 420)})
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_combine")
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function FuBenCombine:ReleaseCallBack()
	self.fb_type = nil
	if self.callback_func then
		self.callback_func = nil
	end
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)

	if self.day_count_change_event then
		GlobalEventSystem:UnBind(self.day_count_change_event)
		self.day_count_change_event = nil
	end
end

function FuBenCombine:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FuBenPanel.CommbineTitle
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkCombine, self))
	self.node_list["Add_Button"].button:AddClickListener(BindTool.Bind(self.AddCount, self))
	self.node_list["Button_reduce"].button:AddClickListener(BindTool.Bind(self.ReduceCount, self))
	self.node_list["Add_Button_tips"].button:AddClickListener(BindTool.Bind(self.Showtips, self))
	self.node_list["free_combine_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFreeCombine, self))
	self.node_list["free_combine_count"].button:AddClickListener(BindTool.Bind(self.OnClickFreeCombine, self))
	
	self.item = ItemCell.New(self.node_list.comsume_item)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DayCounterChange, self))
end

function FuBenCombine:ItemDataChangeCallback()
	self:Flush()
end

function FuBenCombine:ShowIndexCallBack()
	self:Flush()
end

function FuBenCombine:DayCounterChange(day_counter_id)
	self:Flush(0, "DayCounterChange", {DayCounterChange = true})
end

function FuBenCombine:OnFlush(param_t, index)
	self:RefreshView(param_t)
	self:FlushComsumeInfo()
end

function FuBenCombine:RefreshView(param_t)
	if nil == self.fb_type or nil == FuBenPanelWGCtrl.Instance.view  then return end
		if nil == self.fb_type or nil == FuBenPanelWGCtrl.Instance.view  then return end
	self.have_count = 0
	self.all_count = 0
	if self.fb_type == FB_COMBINE_TYPE.WUJINJITAN or self.fb_type == FB_COMBINE_TYPE.LINGHUN_GUANGCHANG then
		local other_cfg = WuJinJiTanWGData.GetOtherCfg()
		local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
		local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
		local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
		self.have_count = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
		self.all_count = other_cfg.everyday_times + yet_buy_time + user_ticket_time
	elseif self.fb_type == FB_COMBINE_TYPE.YUANGU then
		local remain_times, total_times = TeamEquipFbWGData.Instance:GetHTEFbTimes()
		self.have_count = remain_times
		self.all_count = total_times
	elseif self.fb_type == FB_COMBINE_TYPE.KILL_GOD_TOWER then
		local cur_count ,total_count = FuBenPanelWGData.Instance:GetZhuShenTaFBCount()
		self.have_count = cur_count
		self.all_count = total_count
	elseif self.fb_type == FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN then
		self.have_count = ManHuangGuDianWGData.Instance:GetRemainTimes()
		self.all_count = ManHuangGuDianWGData.Instance:GetTotalTimes()
	elseif self.fb_type == FB_COMBINE_TYPE.COPPER_FB then
		local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
		if not copper_info then
			return
		end
		local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
		self.have_count = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
		self.all_count = other_cfg.fb_day_free_times + copper_info.day_buy_times
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1 then
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbControlBeastsType)
		local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbControlBeastsType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.have_count = remain_times
		self.all_count = max_count
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2 then
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbBeautyType)
		local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbBeautyType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.have_count = remain_times
		self.all_count = max_count
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3 then
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbWuHunType)
		local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbWuHunType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.have_count = remain_times
		self.all_count = max_count
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1 then
		local team_type_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbRuneTowerType)
		local enter_times = FuBenTeamCommonTowerWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbRuneTowerType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.have_count = remain_times
		self.all_count = max_count
	end

	local cfg = FuBenWGData.Instance:GetCombineCfg()
	-- local consume_combine = cfg[self.fb_type + 1].combine_once_gold
	self.node_list.shengyu_count.text.text = string.format(Language.FuBenPanel.CommbineHaveCount,self.have_count,self.all_count)

	--购买完次数刷新处理
	for k, v in pairs(param_t) do
		if k == "DayCounterChange" then
			self.need_combine_count = self.have_count
		end
	end

    self.need_combine_count = MathClamp(self.need_combine_count, 2, self.all_count)

	self.node_list.num_show_txt.text.text = self.need_combine_count
	local data = __TableCopy(cfg[self.fb_type + 1].item)
	self.item:SetFlushCallBack(function ()
		local has_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
		local need_num = data.num * (self.need_combine_count - 1)
		local color = has_num >= need_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        self.item:SetRightBottomColorText(ToColorStr(has_num .. "/" .. need_num, color))
        local count = need_num - has_num > 1 and need_num - has_num or 1
        self.item:SetDefShowBuyCount(count)
		self.item:SetRightBottomTextVisible(true)
	end)
	self.item:SetData(data)
	
	self.node_list.rich_buy_desc.text.text = Language.FuBenPanel.ShowCombineDsc1
	if self.fb_type == FB_COMBINE_TYPE.YUANGU then
		self.node_list.rich_comsume_tip.text.text = Language.FuBenPanel.CombineComsumeTip2
	else
		self.node_list.rich_comsume_tip.text.text = Language.FuBenPanel.CombineComsumeTip1
	end
end

function FuBenCombine:FlushComsumeInfo()
	self.node_list.free_left_day.text.text = Language.FuBenPanel.CombineFreeCount2

	local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.MonthCard)
	self.node_list.combine_root:SetActive(not is_active)
	self.node_list.free_combine_count:SetActive(is_active)
end

function FuBenCombine:OnClinkCombine()
	local is_free_combine = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.MonthCard) -- 月卡免费合并
	local cfg = FuBenWGData.Instance:GetCombineCfg()
	local item = cfg[self.fb_type + 1].item
	local need_num = item.num * (self.need_combine_count - 1)
	local item_num = ItemWGData.Instance:GetItemNumInBagById(item.item_id)

    if need_num > item_num and not is_free_combine then
        TipWGData.Instance:SetDefShowBuyCount(need_num - item_num)
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item.item_id})
	else
		if self.callback_func then
			self.callback_func()
			self.callback_func = nil
		end

		FuBenWGData.Instance:SetEnterFbCombineCount(self.fb_type, self.need_combine_count)
		--print_error(self.fb_type,self.need_combine_count)
		FuBenWGCtrl.Instance:SendFBUseCombine(1, self.fb_type)
		self:Close()
	end
end

function FuBenCombine:AddCount()
	if self.need_combine_count < self.have_count then
		self.need_combine_count = self.need_combine_count + 1
		self:Flush()
		return
	end
	if self.fb_type == FB_COMBINE_TYPE.WUJINJITAN or self.fb_type == FB_COMBINE_TYPE.LINGHUN_GUANGCHANG then
		FuBenPanelWGCtrl.Instance.view:OnClickBuyCount()
	elseif self.fb_type == FB_COMBINE_TYPE.YUANGU then
		--FuBenPanelWGCtrl.Instance:OpenPetBuy(false, FUBEN_TYPE.HIGH_TEAM_EQUIP)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMaxNum)
		
	elseif self.fb_type == FB_COMBINE_TYPE.KILL_GOD_TOWER then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMaxNum)
	elseif self.fb_type == FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN then
		--SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMaxNum)
		FuBenPanelWGCtrl.Instance:OpenManHuangGuDianBuy()
	elseif self.fb_type == FB_COMBINE_TYPE.COPPER_FB then
		FuBenPanelWGCtrl.Instance:OpenCopperBuy()
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMaxNum)
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMaxNum)
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMaxNum)
	elseif self.fb_type == FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMaxNum)
	end
end

function FuBenCombine:ReduceCount()
	if self.need_combine_count <= 2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMinNum)
		return
	end
	self.need_combine_count = self.need_combine_count - 1
	self:Flush()
end

function FuBenCombine:Showtips()
	local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
	local need_level = combine_cfg[self.fb_type + 1].level_limit
	local level_des = RoleWGData.GetLevelString(need_level)
	if self.fb_type == FB_COMBINE_TYPE.YUANGU then
		local str = Language.FuBenPanel.CombineTipsContent2
		RuleTip.Instance:SetContent(str, Language.FuBenPanel.CombineTipsTitle)
	else
		local str = Language.FuBenPanel.CombineTipsContent1
		RuleTip.Instance:SetContent(str, Language.FuBenPanel.CombineTipsTitle)
	end
end

function FuBenCombine:SetBasData(fb_type,callback_func)
	self.fb_type = fb_type
	self.callback_func = callback_func
	if nil == self.fb_type or nil == FuBenPanelWGCtrl.Instance.view  then return end
	self.have_count = 0
	self.all_count = 0
	if self.fb_type == FB_COMBINE_TYPE.WUJINJITAN or self.fb_type == FB_COMBINE_TYPE.LINGHUN_GUANGCHANG then
		local other_cfg = WuJinJiTanWGData.GetOtherCfg()
		local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
		local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
		local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
		self.have_count = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
		self.all_count = other_cfg.everyday_times + yet_buy_time + user_ticket_time
	elseif self.fb_type == FB_COMBINE_TYPE.YUANGU then
		local remain_times, total_times = TeamEquipFbWGData.Instance:GetHTEFbTimes()
		self.have_count = remain_times
		self.all_count = total_times
	elseif self.fb_type == FB_COMBINE_TYPE.KILL_GOD_TOWER then
		local cur_count ,total_count = FuBenPanelWGData.Instance:GetZhuShenTaFBCount()
		self.have_count = cur_count
		self.all_count = total_count
	elseif self.fb_type == FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN then
		self.have_count = ManHuangGuDianWGData.Instance:GetRemainTimes()
		self.all_count = ManHuangGuDianWGData.Instance:GetTotalTimes()
	elseif self.fb_type == FB_COMBINE_TYPE.COPPER_FB then
		local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
		if not copper_info then
			return
		end
		local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
		self.have_count = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
		self.all_count = other_cfg.fb_day_free_times + copper_info.day_buy_times
	end
	self.need_combine_count = self.have_count
	self:Open()
end

function FuBenCombine:OnClickFreeCombine()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_month_card)
	self:Close()
end