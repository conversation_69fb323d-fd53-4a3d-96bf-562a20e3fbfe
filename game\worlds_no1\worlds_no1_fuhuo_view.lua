WorldsNO1FuhuoView = WorldsNO1FuhuoView or BaseClass(SafeBaseView)
local OBSERVATION_TIME = 10 	-- 观战按钮倒计时

-- 天下第一复活面板
function WorldsNO1FuhuoView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false,false)

	self.is_nolonger = false
	self.is_modal = true
	self.killer_name = ""
	self:LoadConfig()

	self.killer_objid = 0
	self.record_cut_time = 0
	self.active_close = false
end

function WorldsNO1FuhuoView:SetOpenParam(death_count_in_day,killer_objid)
	self.death_count_in_day = death_count_in_day
	self.killer_objid = killer_objid
end

function WorldsNO1FuhuoView:__delete()

end

function WorldsNO1FuhuoView:ReleaseCallBack()
	CountDownManager.Instance:RemoveCountDown("WorldsNO1FuhuoView:FlushObservationTime")
	CountDownManager.Instance:RemoveCountDown("WorldsNO1FuhuoView:FlushReliveTime")
end

-- 加载配置
function WorldsNO1FuhuoView:LoadConfig()
	local bundle_name = "uis/view/fuhuo_ui_prefab"
	local common_bundle_name =  "uis/view/common_panel_prefab"
	self:AddViewResource(0, bundle_name, "layout_worlds_no1_fuhuo")
end

function WorldsNO1FuhuoView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_relive"], BindTool.Bind(self.OnClickRelive, self)) 				-- 复活
	XUI.AddClickEventListener(self.node_list["btn_observation"], BindTool.Bind(self.OnClickObservation, self)) 		-- 观战
	XUI.AddClickEventListener(self.node_list["btn_leave"], BindTool.Bind(self.OnClickLeave, self)) 					-- 退出

	self.root_view = self.node_list.root_view

	self.gongneng_sort = FuBenWGData.Instance:GetFuhuoBtn()
	self:CreateGongNengHead()
    self:RegisterEvents()
end

function WorldsNO1FuhuoView:RegisterEvents()

end

function WorldsNO1FuhuoView:ShowIndexCallBack()
end

function WorldsNO1FuhuoView:OpenCallBack()
	self.open_view_time = TimeWGCtrl.Instance:GetServerTime()
end

function WorldsNO1FuhuoView:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown("WorldsNO1FuhuoView:FlushObservationTime")
	CountDownManager.Instance:RemoveCountDown("WorldsNO1FuhuoView:FlushReliveTime")
	self.open_view_time = 0
end

function WorldsNO1FuhuoView:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid)
	self.killer_name = killer_name or ''
    local killer_name_list = Split(killer_name, "_")
    if not IsEmptyTable(killer_name_list) then
        if killer_name_list[2] then
            self.killer_name = string.format(Language.BiZuo.ServerName_2, killer_name_list[2], killer_name_list[1])
        else
            self.killer_name = killer_name_list[1]
        end
    end

	self.is_role = is_role or false
	self.killer_level = killer_level or 0

	self:Flush()
end

function WorldsNO1FuhuoView:OnFlush(param_t)
	if self.killer_name == "" or self.killer_name == nil then
		return
	end
	self.node_list.tips_1.text.text = string.format(Language.Fuhuo.FuHuoTips_1, self.killer_name)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_1.rect)
	self.node_list.tips_2.text.text = string.format(Language.Fuhuo.FuHuoTips_2, self.killer_level)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_2.rect)

	local relive_amount = WorldsNO1WGData.Instance:GetReliveAmount()
	self.node_list["btn_relive"]:SetActive(relive_amount > 0)
	self.node_list["relive_amount"]:SetActive(relive_amount <= 0)
	self.node_list["btn_observation"]:SetActive(relive_amount <= 0)
	self.node_list["btn_leave"]:SetActive(relive_amount <= 0)

	-- 剩余复活次数
	local format_str = relive_amount > 0 and Language.WorldsNO1.ReliveStr1 or Language.WorldsNO1.ReliveStr2
	self.node_list["relive_amount"].text.text = string.format(format_str, relive_amount)
	self.node_list["btn_relive_amount"].text.text = string.format(format_str, relive_amount)

	self:FlushObservationTime()
	self:FlushReliveTime()
end

-- 刷新观战按钮倒计时
function WorldsNO1FuhuoView:FlushObservationTime()
	self.node_list["observation_time"].text.text = ""
	local relive_amount = WorldsNO1WGData.Instance:GetReliveAmount()
	if relive_amount > 0 then
		return
	end
	local total_time = self.open_view_time + OBSERVATION_TIME - TimeWGCtrl.Instance:GetServerTime() 
	self:UpdateObservationTime(0, total_time)
	CountDownManager.Instance:RemoveCountDown("WorldsNO1FuhuoView:FlushObservationTime")
	CountDownManager.Instance:AddCountDown("WorldsNO1FuhuoView:FlushObservationTime", BindTool.Bind1(self.UpdateObservationTime, self), BindTool.Bind1(self.CompleteObservationTime, self), nil, total_time, 1)
end

function WorldsNO1FuhuoView:UpdateObservationTime(elapse_time, total_time)
	if not self.node_list["observation_time"] then
		return
    end
    local last_time = math.floor(total_time - elapse_time)  
	self.node_list["observation_time"].text.text = string.format(Language.WorldsNO1.Seconds, last_time)
end

function WorldsNO1FuhuoView:CompleteObservationTime()
	self:OnClickObservation()
	self:Close()
end

-- 刷新复活按钮倒计时
function WorldsNO1FuhuoView:FlushReliveTime()
	self.node_list["relive_time"].text.text = ""
	local relive_amount = WorldsNO1WGData.Instance:GetReliveAmount()
	if relive_amount <= 0 then
		return
	end
	self.node_list["btn_relive"].button.interactable = false
	local relive_time = WorldsNO1WGData.Instance:GetOtherCfg().relive_time
	local total_time = relive_time
	self:UpdateReliveTime(0, total_time)
	CountDownManager.Instance:RemoveCountDown("WorldsNO1FuhuoView:FlushReliveTime")
	CountDownManager.Instance:AddCountDown("WorldsNO1FuhuoView:FlushReliveTime", BindTool.Bind1(self.UpdateReliveTime, self), BindTool.Bind1(self.CompleteReliveTime, self), nil, total_time, 1)
end

function WorldsNO1FuhuoView:UpdateReliveTime(elapse_time, total_time)
	if not self.node_list["relive_time"] then
		return
    end
    local time = math.floor(total_time - elapse_time)  
	self.node_list["relive_time"].text.text = string.format(Language.WorldsNO1.ReliveSeconds, time)
end

function WorldsNO1FuhuoView:CompleteReliveTime()
	if self.node_list["btn_relive"] then
		self.node_list["btn_relive"].button.interactable = true
	end
	self:OnClickRelive()
	self:Close()
end


-- 创建功能头像
function WorldsNO1FuhuoView:CreateGongNengHead()
	if nil ~= self.gongneng_sort then
		for i = 1, 3 do
			self.node_list["btn_getway"..i]:SetActive(false)
		end
		local timecount = 0
		for i, v in ipairs(self.gongneng_sort) do
			if nil ~= v.img_name then
				timecount = timecount + 1
				self.node_list["btn_getway" .. i]:SetActive(true)
				self.node_list["getway_text" .. i].image:LoadSprite(ResPath.GetF2MainUIImage(v.view_name2))
				self.node_list["getway_img" .. i].image:LoadSprite(ResPath.GetF2MainUIImage(v.img_name))
				XUI.AddClickEventListener(self.node_list["btn_getway" .. i], BindTool.Bind(self.ClickFunIconHandler, self, v))
			end
		end
	end
end

function WorldsNO1FuhuoView:ClickFunIconHandler(param_t)
	ViewManager.Instance:Open(param_t.view_name, param_t.tab_index)
end

-- 点击复活
function WorldsNO1FuhuoView:OnClickRelive()
	-- 自动复活不主动请求
	-- FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
	-- self:Close()
end

-- 点击观战
function WorldsNO1FuhuoView:OnClickObservation()
	WorldsNO1WGCtrl.Instance:SendObservate()
	self:Close()
end

-- 点击退出
function WorldsNO1FuhuoView:OnClickLeave()
	Field1v1WGCtrl.Instance:LeaveZhanChangScene()
	self:Close()
end

function WorldsNO1FuhuoView:GetUseFuHuoType()
	return FuHuoType.Common
end

