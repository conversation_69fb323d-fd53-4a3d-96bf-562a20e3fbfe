UltimateBattlefieldShopView = UltimateBattlefieldShopView or BaseClass(SafeBaseView)

function UltimateBattlefieldShopView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_battlefield_shop")
end

function UltimateBattlefieldShopView:LoadCallBack()
    if not self.grid_list then
        self.grid_list = AsyncBaseGrid.New()
        self.grid_list:CreateCells({col = 5, change_cells_num = 1, list_view = self.node_list.ph_grid_list,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_shop_cell", 
                itemRender = UltimateBattlefieldShopCell})
        --self.grid_list:SetStartZeroIndex(false)
    end
end

function UltimateBattlefieldShopView:ReleaseCallBack()
    if self.grid_list then
        self.grid_list:DeleteMe()
        self.grid_list = nil
    end
end

function UltimateBattlefieldShopView:OnFlush()
    local info = UltimateBattlefieldWGData.Instance:GetShopShowInfo()
    if not IsEmptyTable(info) then
        self.grid_list:SetDataList(info)
    end
end


---------------UltimateBattlefieldShopCell-------------
UltimateBattlefieldShopCell = UltimateBattlefieldShopCell or BaseClass(BaseRender)

function UltimateBattlefieldShopCell:LoadCallBack()
    -- XUI.AddClickEventListener(self.node_list["cell_icon"], BindTool.Bind1(self.OnClickItemTipsShow, self))
    if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.cell_icon)
	end
    XUI.AddClickEventListener(self.node_list["bg"], BindTool.Bind1(self.OnClickItemConvert, self))
end

function UltimateBattlefieldShopCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function UltimateBattlefieldShopCell:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.item.item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg then
        -- self.node_list["cell_bg"].image:LoadSpriteAsync(ResPath.GetCommon("a2_sc_btn_bg_" .. item_cfg.color))
        -- self.node_list["cell_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        self.node_list["cell_name"].text.text = item_cfg.name
        self.item_cell:SetData({item_id = item_id})
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id_1)
    local color = item_num >= self.data.stuff_num_1 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.stuff_count.text.text = ToColorStr(item_num .. "/" .. self.data.stuff_num_1, color)
    local stuff_cfg = ItemWGData.Instance:GetItemConfig(self.data.stuff_id_1)
    if stuff_cfg then
        self.node_list["stuff_icon"].image:LoadSprite(ResPath.GetItem(stuff_cfg.icon_id))
    end

    local base_data = UltimateBattlefieldWGData.Instance:GetBaseInfo()
    -- 这里加1是服务器下标从0开始的
    local buy_num = (base_data and base_data.convert_times_list) and base_data.convert_times_list[self.data.seq + 1] or 0
    local is_limit = buy_num >= self.data.times_limit 
    self.node_list.limit_text.text.text = string.format(Language.ChaoticPurchase.BuyTimesStr, self.data.times_limit - buy_num, self.data.times_limit)
    self.node_list.buy_status.text.text = is_limit and Language.Shop.EmptyDes or Language.Common.CanPurchase
end

-- 展示物品
function UltimateBattlefieldShopCell:OnClickItemTipsShow()
    if not self.data then
		return
	end

    local item_id = self.data.item.item_id
    TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

-- 购买
function UltimateBattlefieldShopCell:OnClickItemConvert()
    if not self.data then
        return
    end

    local base_data = UltimateBattlefieldWGData.Instance:GetBaseInfo()
    -- 这里加1是服务器下标从0开始的
    local buy_num = (base_data and base_data.convert_times_list) and base_data.convert_times_list[self.data.seq + 1] or 0
    local is_limit =  buy_num >= self.data.times_limit 
    if not is_limit then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id_1)
        if item_num >= self.data.stuff_num_1 then
            UltimateBattlefieldWGCtrl.Instance:RequestConvert(self.data.seq)
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.stuff_id_1})
        end
    end
end
