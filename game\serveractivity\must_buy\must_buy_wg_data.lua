MustBuyWGData = MustBuyWGData or BaseClass()

function MustBuyWGData:__init()
	if MustBuyWGData.Instance then
		error("[MustBuyWGData] Attempt to create singleton twice!")
		return
	end
	MustBuyWGData.Instance = self
	RemindManager.Instance:Register(RemindName.MustBuyRemind,BindTool.Bind(self.GetMustBuyRemind,self))
	self.rand_act_zhuanfu_type = ServerActivityWGData.Instance:GetRandActZhuanFuType()
	self.must_buy_cfg = ListToMap(ServerActivityWGData.Instance:GetCurrentRandActivityConfig().worth_buy, "seq")
	self.has_open_must_buy_flag = false
end

function MustBuyWGData:__delete()
	MustBuyWGData.Instance = nil
	self.has_open_must_buy_flag = false
end

--策划： 绑玉足够的时候 有绑玉道具购买时 打开界面后红点消失 这里加个红点提示
function MustBuyWGData:GetMustBuyRemind()
	if self.has_open_must_buy_flag then
		return 0
	end
	local flag = self:HasMustBuyRemind()
	return flag and 1 or 0
end

function MustBuyWGData:HasMustBuyRemind()
	local bind_gold = RoleWGData.Instance:GetAttr("bind_gold")
	local item_list = self:GetItemList()
	for k,v in pairs(item_list) do
		local info = MustBuyWGData.Instance:GetItemCfgBySeq(v.seq)
		if info and info.money_type == 2 and bind_gold >= info.buy_cost and v.buy_times < info.buy_limit then
			return true
		end
	end
	return false
end

function MustBuyWGData:SetHasOpenMustBuyFlag(flag)
	self.has_open_must_buy_flag = flag
end

function MustBuyWGData:SetActInfo(protocol)
	self.item_list = protocol.item_list
end

function MustBuyWGData:GetMustBuyCfg()
	if self.rand_act_zhuanfu_type ~= ServerActivityWGData.Instance:GetRandActZhuanFuType() then
		self.must_buy_cfg = ListToMap(ServerActivityWGData.Instance:GetCurrentRandActivityConfig().worth_buy, "seq")
		self.rand_act_zhuanfu_type = ServerActivityWGData.Instance:GetRandActZhuanFuType()
	end
	return self.must_buy_cfg
end

function MustBuyWGData:GetItemCfgBySeq(seq)
	return self:GetMustBuyCfg()[seq]
end

function MustBuyWGData:GetItemList()
	if not self.item_list then
		return {}
	end
	if IsEmptyTable(self.item_list) then
		return self.item_list
    end
    local ser_time = TimeWGCtrl.Instance:GetServerTime()
    for i = #self.item_list, 1, -1 do --策划让屏蔽已下架
        if self.item_list[i].off_time ~= 0 and self.item_list[i].off_time - ser_time <= 0 then
            table.remove(self.item_list, i) 
        end
     end
	table.sort(self.item_list, function(a,b)
		local info_a = self:GetItemCfgBySeq(a.seq)
        local info_b = self:GetItemCfgBySeq(b.seq)
		local order_a = 0
		local order_b = 0
        if info_a and info_b then
            if info_a.money_type == 2 then --策划需求把消耗绑元的放前面
                order_a = order_a + 10000
            end
            if info_b.money_type == 2 then
                order_b = order_b + 10000
            end
			if a.off_time ~= 0 then
				order_a = order_a + 1000
			end
			if b.off_time ~= 0 then
				order_b = order_b + 1000
			end

			if a.off_time < b.off_time then
				order_a = order_a + 100
			end
			if b.off_time < a.off_time then
				order_b = order_b + 100
			end

			if info_a.rank_seq < info_b.rank_seq then
				order_a = order_a + 10
			end
			if info_b.rank_seq < info_a.rank_seq then
				order_b = order_b + 10
			end

			return order_a > order_b
		end
    end)
    
	return self.item_list
end

function MustBuyWGData:HasNewItem()
	if not self.item_list then
		return
	end
	self.new_flag = false
	for k,v in ipairs(self.item_list) do
		if v.is_new ~= 0 then
			self.new_flag = true
			-- local info = self:GetItemCfgBySeq(v.seq)
			-- if info.is_new then
			-- 	return info
			-- end
			break
		end
	end
end

function MustBuyWGData:ShowButtonNextTime()
	if not self.item_list then
		return
	end

	local time = 9999999999
	for k,v in pairs(self.item_list) do
		if v.off_time ~= 0 and v.off_time < time then
			time = v.off_time
		end
	end
	return time
end

function MustBuyWGData:GetNewFlag()
	self:HasNewItem()
	return self.new_flag
end

function MustBuyWGData:GetShowBubbleTime()
	local cfg = self:GetOtherCfg()
	return cfg.show_bubble_time or 3600
end

function MustBuyWGData:GetMinTimeItem()
	if not self.item_list then
		return
	end

	local t = {}
	t.off_time = 9999999999
	t.rank_seq = 10
	local t_data = {}
	for k,v in pairs(self.item_list) do
		if v.off_time ~= 0 then
			local info = self:GetItemCfgBySeq(v.seq)
			if info.rank_seq <= t.rank_seq then
				if v.off_time < t.off_time then
					t = v
					t.rank_seq = info.rank_seq
					t_data = info
				end
			end
		end
	end

	local cfg
	if t_data.buy_item then
		cfg = ItemWGData.Instance:GetItemConfig(t_data.buy_item.item_id)
	end

	if cfg then
		return cfg.name, cfg.color
	end
end

function MustBuyWGData:GetMutexView()
	if not self.mutex_view then
		local cfg = self:GetOtherCfg()
		local view_list = cfg.mutex_view or "SelectCameraModeView|AppearanceGetNew|GuradInvalidTimeView|VipTip|TipsShowChapterView|RechargeVipTyView"
		self.mutex_view = Split(view_list, "|")
	end
	return self.mutex_view
end

function MustBuyWGData:GetMaxOffTime()
	if not self.item_list then
		return 0
	end
	local off_time = 0
	for k,v in pairs(self.item_list) do
		if off_time < v.off_time then
			off_time = v.off_time
		end
	end
	return off_time
end

function MustBuyWGData:GetShowTipTime()
	local cfg = self:GetOtherCfg()
	return cfg.worth_buy_tip_time or 5
end

function MustBuyWGData:GetShowCountTime()
	local cfg = self:GetOtherCfg()
	return cfg.worth_buy_countdown or 3600
end

function MustBuyWGData:GetOtherCfg()
	return ServerActivityWGData.Instance:GetCurrentRandActivityConfig().other[1]
end

function MustBuyWGData:CheckProtocolConfig(protocol)
	if protocol.item_count > 0 then
		local new_item_count = 0
		local new_item_list = {}
		for i=1,protocol.item_count do
			if protocol.item_list[i] then
				if self:GetItemCfgBySeq(protocol.item_list[i].seq) then
					new_item_count = new_item_count + 1
					new_item_list[new_item_count] = protocol.item_list[i]
				end
			end
		end
		protocol.item_count = new_item_count
		protocol.item_list = new_item_list
		return protocol
	else
		return protocol
	end
end