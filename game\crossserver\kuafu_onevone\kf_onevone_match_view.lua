KFOneVOneMatchView = KFOneVOneMatchView or BaseClass(SafeBaseView)

local COUNT_TIME1 = 120
local COUNT_TIME2 = 60
local START_TIME = 3
local ZERO_POS = 384
IS_ON_MATCHING = false

function KFOneVOneMatchView:__init()
	self:SetMaskBg(false, false)
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_matching")
	-- self.view_layer = UiLayer.Normal
	self.active_close = false
end

function KFOneVOneMatchView:ReleaseCallBack()
	IS_ON_MATCHING = nil
	self.old_guaqi_time = nil
	self.flag = nil
	self.start_fight = nil
	self.has_load_callback = nil
	self.need_flush = nil
	self.count_second = nil

	if CountDownManager.Instance:HasCountDown("Kf1v1match") then
		CountDownManager.Instance:RemoveCountDown("Kf1v1match")
	end

	if CountDownManager.Instance:HasCountDown("Kf1v1match_start") then
		CountDownManager.Instance:RemoveCountDown("Kf1v1match_start")
	end

	if self.match_info_change then
		GlobalEventSystem:UnBind(self.match_info_change)
		self.match_info_change = nil
	end

	if self.match_info_start then
		GlobalEventSystem:UnBind(self.match_info_start)
		self.match_info_start = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	self.center_mask_bg = nil
	-- if self.center_mask_bg and self.center_mask_bg.gameObject then
	-- 	ResMgr:Destroy(self.center_mask_bg.gameObject)
	-- 	self.center_mask_bg = nil
	-- end

	if self.head_list then
		for k,v in pairs(self.head_list) do
			v:DeleteMe()
		end
		self.head_list = {}
	end
end

function KFOneVOneMatchView:CloseCallBack()
	IS_ON_MATCHING = false

	if self.tween3 then
		self.tween3:Kill()
		self.tween3 = nil
 	end
end

function KFOneVOneMatchView:LoadCallBack()
	self.node_list["btn_match"].button:AddClickListener(BindTool.Bind(self.OnClickBtnMatch, self))
	self.node_list["small_content_btn"].button:AddClickListener(BindTool.Bind(self.MatchBack, self))
	self.match_info_change = GlobalEventSystem:Bind(KFONEVONE1v1Type.KF_INFO_CHANGE, BindTool.Bind(self.StopMatch, self))
	self.match_info_start = GlobalEventSystem:Bind(KFONEVONE1v1Type.START_PVP, BindTool.Bind(self.StartFight, self))
	--local bundle, asset = ResPath.GetKf1V1("kf_txt_matching")
	self.node_list.img_matching.text.text = Language.NewTeam.IsMatching
	-- self.node_list["img_matching"].image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["img_matching"].image:SetNativeSize()
	-- end)

	self.has_load_callback = true
	if self.need_flush then
		self:Flush()
	end
	--self:CreateCenterBg()
	self.center_mask_bg = self.node_list.kf_zc_bg

	self.head_list = {}
	for i = 1, 6 do
		self.head_list[i] = BaseHeadCell.New(self.node_list["head_icon_" .. i])
	end

	local role_vo = RoleWGData.Instance.role_vo
	self.node_list.role_name_1.text.text = role_vo.name
	self.node_list.role_level_1.text.text = Language.Rank.RankValueName[2] .. role_vo.level
	self.node_list.role_power_1.text.text = Language.Rank.RankValueName[1] .. role_vo.capability
	local self_score = KuafuOnevoneWGData.Instance:Get1V1InfoJiFen()
  	local rank_cfg = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self_score)
	local a, b = ResPath.GetF2Field1v1Rank(rank_cfg.rank_id)
	self.node_list.rank_icon_1.image:LoadSprite(a, b)

	local enemy_info = KuafuOnevoneWGData.Instance:GetMatchingEnemySex()
	if not IsEmptyTable(enemy_info) then
		self:StartFight()
	end

	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind(self.ClickCancel, self))

end
function KFOneVOneMatchView:PKMatchingAni()
	self.node_list.matching_time:SetActive(false)
 	self.node_list.img_matching:SetActive(false)
 	-- self.node_list.btn_match:SetActive(false)
 	self.node_list.bottom:SetActive(false)
 	self.node_list.rotate_image:SetActive(false)
 	self.node_list.rotate_image_1:SetActive(false)
	self.node_list.left_red_image.rect.anchoredPosition = Vector2(-1600,53.1)
	self.node_list.right_blue_image.rect.anchoredPosition = Vector2(1600,43.1)
	 local tween1 = self.node_list.left_red_image.rect:DOAnchorPosX(-210.8, 0.5)
	 local tween2 = self.node_list.right_blue_image.rect:DOAnchorPosX(178.1, 0.5)
	 tween1:SetEase(DG.Tweening.Ease.Linear)
	 tween2:SetEase(DG.Tweening.Ease.Linear)
	 tween1:OnComplete(function ()
	 	self.node_list.matching_time:SetActive(true)
	 	self.node_list.img_matching:SetActive(true)
	 	-- self.node_list.btn_match:SetActive(true)
	 	self.node_list.bottom:SetActive(true)
	 	self.node_list.rotate_image_1:SetActive(true)
	 end)
end

function KFOneVOneMatchView:CreateCenterBg()
	if nil ~= self.center_mask_bg then
		ResMgr:Destroy(self.center_mask_bg.gameObject)
	end
	self.center_mask_bg = U3DObject(GameObject.New("CenterMaskBg"), nil, self)
	local center_mask_bg_transform = self.center_mask_bg.transform
	center_mask_bg_transform:SetParent(self.root_node.transform, false)
	center_mask_bg_transform:SetSiblingIndex(1)
	self.center_mask_bg.gameObject:AddComponent(typeof(UnityEngine.UI.Image))
	local bundle, asset = ResPath.GetKf1V1("center_mask_bg")
	self.center_mask_bg.image:LoadSprite(bundle, asset)
	self.center_mask_bg.image.raycastTarget = false
	local rect = self.center_mask_bg.rect
	rect.anchorMin = Vector2(0, 0.5)
	rect.anchorMax = Vector2(1, 0.5)
	rect.localPosition = Vector2(0, 0)
	rect.sizeDelta = Vector2(0, 486)
end

function KFOneVOneMatchView:OnFlush()
	if not self.has_load_callback then
		self.need_flush = true
	end
	self:FLushAllHead()
	self:FLushRoleNum()
end

--刷新头像
function KFOneVOneMatchView:FLushAllHead()
	local role_vo = RoleWGData.Instance.role_vo

	-- 第三个位置代表自己\
	self.head_list[3]:SetActive(true)
	self.head_list[3]:SetData({role_id = RoleWGData.Instance:InCrossGetOriginUid(), prof = role_vo.prof, sex = role_vo.sex, fashion_photoframe = CheckList(role_vo, "appearance", "fashion_photoframe") or 0})
	self.node_list.role_name_2.text.text = ""
	self.node_list.role_level_2.text.text = ""
	self.node_list.role_power_2.text.text = ""
	self.flag = Field1v1WGData.Instance:GetCurMatchFlag()
	self.count_second = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg().macth_time
	if self.flag == KFPVP_TYPE.ONE then
		self.node_list["head_bg1"]:SetActive(false)
		self.node_list["head_bg2"]:SetActive(false)
		self.node_list["head_bg5"]:SetActive(false)
		self.node_list["head_bg6"]:SetActive(false)
		self:ResetHeadIcon(4)
		self.node_list.fake_head:SetActive(true)
	elseif self.flag == KFPVP_TYPE.MORE then
		self.node_list["head_bg1"]:SetActive(true)
		self.node_list["head_bg2"]:SetActive(true)
		self.node_list["head_bg5"]:SetActive(true)
		self.node_list["head_bg6"]:SetActive(true)
		for i=1,6 do
			if i ~= 3 then--第三个位置是自己
				self:ResetHeadIcon(i)
			end
		end
		local mate_list = KuafuPVPWGData.Instance:GetMatesInfo()
		local uid = GameVoManager.Instance:GetMainRoleVo().role_id
		local count = 2
		for i=1,#mate_list do
			if mate_list[i] then
				if uid ~= mate_list[i].uid then
					self:FlushRoleHeadIcon(count, mate_list[i].role_id, mate_list[i].sex,mate_list[i].prof)

					self:FLushRoleHeadFrame(count, mate_list[i].appearance.fashion_photoframe)
					count = count - 1
				end
			end
		end
	end
end

--重置头像
function KFOneVOneMatchView:ResetHeadIcon( id, state)
	-- local bundle, asset = ResPath.GetKf1V1("kf_matching_head")
	-- self.node_list["img_head" .. id].image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["img_head" .. id].image:SetNativeSize()
	-- end)

	if nil == id or nil == self.head_list[id] then
		print_error("匹配出现错误！！id,state",id,state)
	end
	self.head_list[id]:SetData({})
	self.head_list[id]:SetActive(false)
	self.node_list.rank_icon_2:SetActive(false)

	self.node_list["img_question" .. id]:SetActive(state == nil)
	if id == 4 then
		self.node_list["default_head_icon_bg_4"]:SetActive(state == nil)
	end

end

--刷新匹配人数，开始倒计时
function KFOneVOneMatchView:FLushRoleNum()
	if self.flag == KFPVP_TYPE.ONE then
		local match_info = KuafuOnevoneWGData.Instance:Get1V1MacthInfo()

		if match_info.result == 0 then
			self:Close()
		else
			self:StartCountDown()
		end

	elseif self.flag == KFPVP_TYPE.MORE then
		-- local mate_list = KuafuPVPWGData.Instance:GetMatesInfo()
		local info = KuafuPVPWGData.Instance:GetMatchStateInfo()
		self:StartCountDown()
	end
end

--刷新头像
function KFOneVOneMatchView:FlushRoleHeadIcon( node_index, role_id, sex,prof, fashion_photoframe)
	self:ResetHeadIcon(node_index, true)

	if self.head_list[node_index] then
		self.head_list[node_index]:SetActive(true)
		self.node_list.rank_icon_2:SetActive(true)
		self.node_list.fake_head:SetActive(false)
		self.head_list[node_index]:SetData({role_id = role_id,prof = prof, sex = sex, fashion_photoframe = fashion_photoframe})
	end
end

--刷新头像框
function KFOneVOneMatchView:FLushRoleHeadFrame( node_index, index )
	if self.head_list[node_index] then
		self.head_list[node_index]:Flush()
	end
end

--开始倒计时，不传时间为60s总时间，传了为total_time倒计时
function KFOneVOneMatchView:StartCountDown( total_time )
	if CountDownManager.Instance:HasCountDown("Kf1v1match") then
		CountDownManager.Instance:RemoveCountDown("Kf1v1match")
	end
	if total_time == nil then
		local activity_info = self.flag == KFPVP_TYPE.ONE
			and ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
			or ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
		local time = activity_info and activity_info.next_time or nil
		CountDownManager.Instance:AddCountDown("Kf1v1match", BindTool.Bind(self.UpdateCountDown,self),
			BindTool.Bind(self.CompleteCountDown, self), time, nil , 0.2)
		return
	end
	CountDownManager.Instance:AddCountDown("Kf1v1match_start", BindTool.Bind(self.UpdateCountDown,self),
		BindTool.Bind(self.CompleteStartCountDown, self), nil, total_time , 0.2)
end

--复位
function KFOneVOneMatchView:ShowIndexCallBack()
	self.node_list.big_bg.image.raycastTarget = false
	self.node_list["match_content"].canvas_group.blocksRaycasts = true
	self.node_list["match_content"].canvas_group.alpha = 1
	self.node_list["match_content"].rect.anchoredPosition = Vector2(0,0)
	self.node_list["small_content"].rect.anchoredPosition = Vector2(self.node_list["small_content"].rect.anchoredPosition.x, 120)
	self.center_mask_bg.image.color = Color.New(0, 0, 0, 1)
	if nil == self.center_mask_bg then return end
	local rect = self.center_mask_bg.rect
	rect.anchorMin = Vector2(0, 0.5)
	rect.anchorMax = Vector2(1, 0.5)
	rect.localPosition = Vector2(0, 0)
	rect.sizeDelta = Vector2(0, 486)
	self:PKMatchingAni()
end

-- 点击挂起
function KFOneVOneMatchView:OnClickBtnMatch()
	-- KuafuOnevoneWGCtrl.Instance:SendCross1v1MatchQuery(CROSS_1V1_MATCH_REQ_TYPE.CROSS_1V1_MATCH_REQ_CANCEL)
	-- if not IS_ON_MATCHING then
	-- if self.tween3 then
	-- 	self.tween3:Kill()
	-- 	self.tween3 = nil
 -- 	end

 	local tween1 = self.node_list["small_content"].rect:DOAnchorPosY(0, 0.8)
	tween1:SetEase(DG.Tweening.Ease.InBack)

	local tween = self.node_list["match_content"].rect:DOAnchorPosY(ZERO_POS, 0.8)
	tween:SetEase(DG.Tweening.Ease.InBack)
	tween:OnUpdate(BindTool.Bind(self.ChangeAlpha, self))
	local pos_max = 2 * ZERO_POS
	local tween2 = self.center_mask_bg.rect:DOAnchorPosY(pos_max, 0.8)
	tween2:SetEase(DG.Tweening.Ease.InBack)

	self.node_list["match_content"].canvas_group.blocksRaycasts = false
	self.node_list.big_bg.image.raycastTarget = false
	self:SetMaskBgAlpha(0)
	-- end
	self:ChangeGuaQiState(true)
end

--dotween改变alpha
function KFOneVOneMatchView:ChangeAlpha()
	local dis_y = ZERO_POS - self.node_list["match_content"].rect.anchoredPosition.y
	self.node_list.match_content.canvas_group.alpha = dis_y / ZERO_POS
	self.node_list.small_content.canvas_group.alpha = 1 - dis_y / ZERO_POS
	self.center_mask_bg.image.color = Color.New(0, 0, 0, dis_y / ZERO_POS)
end

--改变 匹配中 状态
function KFOneVOneMatchView:ChangeGuaQiState(status)
	IS_ON_MATCHING = true
end

--完成3秒倒计时
function KFOneVOneMatchView:CompleteStartCountDown()
	if self.start_fight then
		if self.flag == KFPVP_TYPE.ONE then
			local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossEnterActivtyReq)
			send_protocol.cross_activity_type = ACTIVITY_TYPE.KF_ONEVONE
			send_protocol:EncodeAndSend()
			--CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_ONEVONE)
		else
			local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossEnterActivtyReq)
			send_protocol.cross_activity_type = ACTIVITY_TYPE.KF_PVP
			send_protocol:EncodeAndSend()
			--CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_PVP)
		end
		ViewManager.Instance:CloseAll()
		if self.role_data_change then
			RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
			self.role_data_change = nil
		end
		self:Close()
		self.start_fight = false
	end
end

--倒计时处理
function KFOneVOneMatchView:UpdateCountDown(elapse_time, total_time)
	if self.start_fight then
		local time = START_TIME - elapse_time
		time = time > 0 and time or 0
		self.node_list["matching_time"].text.text = math.ceil(time)
		self.node_list["small_matching_time"].text.text = ""
	else
		local cfg = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg()
		local cur_total_time = self.flag == KFPVP_TYPE.ONE and self.count_second or COUNT_TIME2
		local time = cur_total_time - elapse_time
		while time < 0 do
			time = time + cur_total_time
		end
		self.node_list["matching_time"].text.text = math.floor(time)
		self.node_list["small_matching_time"].text.text = math.floor(time)
	end
end

--完成总倒计时
function KFOneVOneMatchView:CompleteCountDown()
	self:Close()
	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

--停止匹配
function KFOneVOneMatchView:StopMatch()
	if CountDownManager.Instance:HasCountDown("Kf1v1match") then
		CountDownManager.Instance:RemoveCountDown("Kf1v1match")
	end
	-- self:Close()
end

--匹配到人，然后3秒倒计时
function KFOneVOneMatchView:StartFight()
	CgManager.Instance:Stop()
	self.start_fight = true
	self:StartCountDown(START_TIME)
	self.node_list["match_content"].canvas_group.blocksRaycasts = true
	if self.flag == KFPVP_TYPE.ONE then
		local enemy = KuafuOnevoneWGData.Instance:GetMatchingEnemySex()
		if not IsEmptyTable(enemy) then
			self.node_list.role_name_2.text.text = enemy.name
			self.node_list.role_level_2.text.text = Language.Rank.EmenyLevel .. enemy.level
			self.node_list.role_power_2.text.text = Language.Rank.EmenyCap .. enemy.capability
			self:FlushRoleHeadIcon(4, enemy.role_id, enemy.sex,enemy.prof, enemy.photo_frame)
			self:FLushRoleHeadFrame(4, enemy.photo_frame)
			local rank_cfg = KuafuOnevoneWGData.Instance:GetRewardBaseCell(enemy.oppo_score)
			local a, b = ResPath.GetF2Field1v1Rank(rank_cfg.rank_id)
			self.node_list.rank_icon_2.image:LoadSprite(a, b)
		end
	else
		local enemy_list = KuafuPVPWGData.Instance:GetEnemyList()
		for i=1,3 do
			--print_error(enemy_list[i],"***********************************",enemy_list)
			if enemy_list[i] then
				self:FlushRoleHeadIcon(i + 3, enemy_list[i].role_id, enemy_list[i].sex,enemy_list[i].prof)
				self:FLushRoleHeadFrame(i + 3, enemy_list[i].appearance.fashion_photoframe)
			end
		end
	end

	if IS_ON_MATCHING then
		self:MatchBack()
	end
	self.node_list["img_matching"].text.text = Language.NewTeam.IsMatchingEnter
	-- self.node_list["btn_match"]:SetActive(false)
	self.node_list.bottom:SetActive(false)
	self.node_list["rotate_image"]:SetActive(true)
	self.node_list.matching_pa.rect.anchoredPosition = Vector2(11, -72)

	-- local bundle, asset = ResPath.GetKf1V1("kf_txt_enter")
	-- self.node_list["img_matching"].image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["img_matching"].image:SetNativeSize()
	-- end)

	--角色死亡复活
	local role = Scene.Instance:GetMainRole()
	if role and role:IsRealDead() then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
	end
	if not self.role_data_change then
		self.role_data_change = BindTool.Bind(self.RoleDataChangeCallback, self)
	end
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"hp"})
end

--监听匹配到人的死亡状态，然后复活
function KFOneVOneMatchView:RoleDataChangeCallback( atr_name, new_value, old_value )
	if atr_name == "hp" then
		if old_value > 0 and new_value <= 0 then
			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
		end
	end
end

function KFOneVOneMatchView:MatchBack()
	local tween = self.node_list["match_content"].rect:DOAnchorPosY(0, 0.5)
	tween:SetEase(DG.Tweening.Ease.OutBack)
	tween:OnUpdate(BindTool.Bind(self.ChangeAlpha, self))

	local tween1 = self.node_list["small_content"].rect:DOAnchorPosY(120, 0.5)
	tween1:SetEase(DG.Tweening.Ease.OutBack)

	local tween2 = self.center_mask_bg.rect:DOAnchorPosY(0, 0.5)
	tween2:SetEase(DG.Tweening.Ease.OutBack)

	self.node_list["match_content"].canvas_group.blocksRaycasts = true
	self.node_list.big_bg.image.raycastTarget = false
	self:ChangeGuaQiState(false)
	self:SetMaskBgAlpha(128/255)
end

function KFOneVOneMatchView:ClickCancel()
	ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
	KuafuOnevoneWGCtrl.Instance:SendCross1v1MatchQuery(CROSS_1V1_MATCH_REQ_TYPE.CROSS_1V1_MATCH_REQ_CANCEL)
end
