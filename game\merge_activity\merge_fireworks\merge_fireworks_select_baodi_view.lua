-------------------------------------
-- 陨落星辰-自选保底
-------------------------------------
MergeFireworksSelectBaoDiView = MergeFireworksSelectBaoDiView or BaseClass(SafeBaseView)

local MAX_SELECT_NUM = 3

function MergeFireworksSelectBaoDiView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal
    self.view_cache_time = 0

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", { sizeDelta = Vector2(602, 426) })
	self:AddViewResource(0, "uis/view/merge_activity_ui/fireworks_ui_prefab", "layout_fireworks_select_baodi_view")
end

function MergeFireworksSelectBaoDiView:LoadCallBack()
    if not self.select_grid then
        self.select_grid = MFWSelectBaoDiGrid.New()
        self.select_grid:SetStartZeroIndex(false)
        self.select_grid:SetIsMultiSelect(true)
        self.select_grid:CreateCells({
            col = 4,
            assetName = "reward_select_item",
            assetBundle = "uis/view/merge_activity_ui/fireworks_ui_prefab",
            list_view = self.node_list["select_item_grid"],
            itemRender = MWFSelectItemRender,
            change_cells_num = 1,
        })
        self.select_grid:SetSelectCallBack(BindTool.Bind(self.OnItemSelectCB, self))
    end

    self.node_list["title_view_name"].text.text = Language.MergeFireworks.SelectBaoDiViewName

    XUI.AddClickEventListener(self.node_list["btn_confirm"], BindTool.Bind(self.OnClickConfirm, self))
    XUI.AddClickEventListener(self.node_list["btn_reset_select"], BindTool.Bind(self.OnClickReset, self))
end

function MergeFireworksSelectBaoDiView:ReleaseCallBack()
    if self.select_grid then
        self.select_grid:DeleteMe()
        self.select_grid = nil
    end

    self.select_data_list = nil
end

function MergeFireworksSelectBaoDiView:OnFlush()
    local baodi_list = MergeFireworksWGData.Instance:GetSelectBaoDiList()
    self.select_grid:SetDataList(baodi_list)

    local cur_selected_id_list = MergeFireworksWGData.Instance:GetSelectedRewardList()
    self.select_grid:SetSelectList(cur_selected_id_list)
    self:FlushSelectItemView()
end

function MergeFireworksSelectBaoDiView:OnItemSelectCB(cell)
    self:FlushSelectItemView()
end

function MergeFireworksSelectBaoDiView:FlushSelectItemView()
    self.select_data_list = self.select_grid:GetAllSelectCell()
    
    local str = string.format(Language.MergeFireworks.ConfirmSelect, #self.select_data_list, MAX_SELECT_NUM)
    self.node_list["btn_select_text"].text.text = str
end

-- 确认
function MergeFireworksSelectBaoDiView:OnClickConfirm()
    if not self.select_data_list or #self.select_data_list ~= 3 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.MergeFireworks.SelectNotEnough)
        return
    end

    local reward_id_list = {}
    for i, v in ipairs(self.select_data_list) do
        reward_id_list[i] = v.reward_id
    end
    MergeFireworksWGCtrl.Instance:SendRewrdId(reward_id_list)
    self:Close()
end

-- 重置
function MergeFireworksSelectBaoDiView:OnClickReset()
    self.select_grid:SetSelectList({})
    self:FlushSelectItemView()
end

---------------------------------------------------------
-- MFWSelectBaoDiGrid
---------------------------------------------------------
MFWSelectBaoDiGrid = MFWSelectBaoDiGrid or BaseClass(AsyncBaseGrid)
function MFWSelectBaoDiGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= MAX_SELECT_NUM then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeFireworks.SelectLimit)
            return true
        end
    end

    return false
end

function MFWSelectBaoDiGrid:SetSelectList(select_seq_list)
    if not select_seq_list then
        return
    end

    self.select_tab[1] = {}
    self.cur_multi_select_num = 0
    local data = self.cell_data_list
    for i = 1, self.has_data_max_index do
        for j, v in ipairs(select_seq_list) do
            if data[i] and data[i].reward_id == v then
                if self.cur_multi_select_num < MAX_SELECT_NUM then
                    self.cur_multi_select_num = self.cur_multi_select_num + 1
                    self.select_tab[1][i] = true
                else
                    break
                end
            end
        end
    end

    self:__DoRefreshSelectState()
end


-------------------------------------------
-- 物品选择格子
-------------------------------------------
MWFSelectItemRender = MWFSelectItemRender or BaseClass(BaseRender)
function MWFSelectItemRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_cell_pos"])
        --self.item_cell:SetIsShowTips(false)
        self.item_cell:SetUseButton(false)
        self.item_cell:IsCanDJ(false)
        self.item_cell:UseNewSelectEffect(true)
    end
end

function MWFSelectItemRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function MWFSelectItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    self.item_cell:SetData(self.data.reward_item)
    local item_name = ItemWGData.Instance:GetItemName(self.data.reward_item.item_id)
    self.node_list["txt_item_name"].text.text = item_name
end

function MWFSelectItemRender:SetSelect(is_select)
    self.item_cell:SetSelectEffect(is_select)
end