﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

[CreateAssetMenu(
        fileName = "UIBundleDependChecker",
        menuName = "Checker/UIBundleDependChecker")]
public class UIBundleDependChecker : ScriptableObject {
    public List<UIDependItem> list;

    [Serializable]
    public struct UIDependItem
    {
        public UnityEngine.Object prefabFloder;
        public UnityEngine.Object atlasFloder;
    }
}
