TianShenDaLianView = TianShenDaLianView or BaseClass(SafeBaseView)

function TianShenDaLianView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/tianshenroad_ui_prefab", "layout_tian_dalian")
end

function TianShenDaLianView:ReleaseCallBack()
	self.ske_graphic_tianshen = nil
	self.ske_graphic_xiaotianquan = nil

	if self.delay_play_anim then
		GlobalTimerQuest:CancelQuest(self.delay_play_anim)
		self.delay_play_anim = nil
	end

	if self.delay_play_ts_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_ts_idle)
		self.delay_play_ts_idle = nil
	end

	if self.delay_paly_xtq_idle then
		GlobalTimerQuest:CancelQuest(self.delay_paly_xtq_idle)
		self.delay_paly_xtq_idle = nil
	end

	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if self.ts_boss_model then
		self.ts_boss_model:DeleteMe()
		self.ts_boss_model = nil
	end

	CountDownManager.Instance:RemoveCountDown("dalian_down_time")
end

function TianShenDaLianView:LoadCallBack()
	-- self.delay_play_anim = GlobalTimerQuest:AddDelayTimer(function()
	--     	self:PlaySpineAnim()
	--     end, 0.2)
	XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGotoBtn, self))
	-- XUI.AddClickEventListener(self.node_list.not_tip_toggle, BindTool.Bind(self.OnClickNotTipToggle, self))
	self.item_list = AsyncListView.New(TianShenDaLianItem, self.node_list.item_list)
	self:InitModel()
end

function TianShenDaLianView:ShowIndexCallBack()
	self:AutoCloseView()
	if self.ts_boss_model then
		self.ts_boss_model:PlayMonsterAction()
	end
end

function TianShenDaLianView:OnClickGotoBtn()
	self:Close()
	TianshenRoadWGCtrl.Instance:Open()
end

function TianShenDaLianView:CloseCallBack()
	if self.node_list.not_tip_toggle then
		local is_not_tip = self.node_list.not_tip_toggle.toggle.isOn
		if is_not_tip then
			local uuid = RoleWGData.Instance:GetUUid()
			local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "tianshen_dalian_flag")
			PlayerPrefsUtil.SetInt(key, 1)
		end
	end
end

function TianShenDaLianView:OnFlush(param_t)
	self:RefreshView()
end

function TianShenDaLianView:RefreshView()
	local cfg_data = TianshenRoadWGData.Instance:GetTianShenDaLianCfg()
	if not cfg_data then
		return
	end

	local reward_list = SortTableKey(cfg_data.reward_item)
	self.item_list:SetDataList(reward_list)

	local end_time, start_time = TianshenRoadWGData.Instance:GetActivityInValidTime()
	local end_time_tab = os.date("*t", end_time)
	local start_time_tab = os.date("*t", start_time)
	local start_str = string.format(Language.TianShenRoad.DaLianStr, start_time_tab.month, start_time_tab.day,
		start_time_tab.hour)
	local end_str = string.format(Language.TianShenRoad.DaLianStr, end_time_tab.month, end_time_tab.day,
		end_time_tab.hour)
	self.node_list.activity_time.text.text = string.format(Language.TianShenRoad.DaLianStr2, start_str, end_str)
end

function TianShenDaLianView:InitModel()
	self.ts_boss_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["ph_bianshen_display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}
	
	self.ts_boss_model:SetRenderTexUI3DModel(display_data)
	-- self.ts_boss_model:SetUI3DModel(self.node_list.ph_bianshen_display.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
	self:AddUiRoleModel(self.ts_boss_model)

	local tianshen_index = 9
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(tianshen_index)
	self.ts_boss_model:SetTianShenModel(tianshen_cfg.appe_image_id, tianshen_index, false)
end

function TianShenDaLianView:AutoCloseView()
	CountDownManager.Instance:RemoveCountDown("dalian_down_time")
	self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3, 30)
	CountDownManager.Instance:AddCountDown("dalian_down_time",
		function(elapse_time, total_time)
			self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3,
				math.ceil(total_time - elapse_time))
		end,
		function()
			self:Close()
		end, nil, 30, 1)
end

function TianShenDaLianView:PlaySpineAnim()
	if not self.ske_graphic_tianshen then
		self.ske_graphic_tianshen = self.node_list["tianshen"].gameObject:GetComponent("SkeletonGraphic")
	end

	if not self.ske_graphic_xiaotianquan then
		self.ske_graphic_xiaotianquan = self.node_list["xiaotianquan"].gameObject:GetComponent("SkeletonGraphic")
	end

	if self.ske_graphic_tianshen then
		self.ske_graphic_tianshen.AnimationState:SetAnimation(0, "into", false)
		self.delay_play_ts_idle = GlobalTimerQuest:AddDelayTimer(function()
			self.ske_graphic_tianshen.AnimationState:SetAnimation(0, "idle", true)
		end, 1)
	end

	if self.ske_graphic_xiaotianquan then
		self.ske_graphic_xiaotianquan.AnimationState:SetAnimation(0, "into", false)
		self.delay_paly_xtq_idle = GlobalTimerQuest:AddDelayTimer(function()
			self.ske_graphic_xiaotianquan.AnimationState:SetAnimation(0, "idle", true)
		end, 1)
	end
end

----------------------------------------------------------------------------

TianShenDaLianItem = TianShenDaLianItem or BaseClass(BaseRender)

function TianShenDaLianItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.root_bg, BindTool.Bind1(self.OnClickItem, self))
end

function TianShenDaLianItem:OnFlush()
	local data = self:GetData()
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
	self.node_list.item_icon.image:LoadSpriteAsync(bundle, asset)
end

function TianShenDaLianItem:OnClickItem()
	TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_NORMAL, nil)
end
