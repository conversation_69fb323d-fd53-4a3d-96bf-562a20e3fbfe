require("game/half_purchase/half_purchase_view")
require("game/half_purchase/half_purchase_wg_data")

HalfPurchaseWGCtrl = HalfPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function HalfPurchaseWGCtrl:__init()
	if HalfPurchaseWGCtrl.Instance then
		Error("[HalfPurchaseWGCtrl]:Attempt to create singleton twice!")
	end

	HalfPurchaseWGCtrl.Instance = self
	self.data = HalfPurchaseWGData.New()
	self.view = HalfPurchaseView.New(GuideModuleName.HalfPurchaseView)
end

function HalfPurchaseWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	HalfPurchaseWGCtrl.Instance = nil
end