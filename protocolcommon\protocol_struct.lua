ProtocolStruct = ProtocolStruct or {}

-- 默认角色外观数据
function ProtocolStruct.RoleAppearance()
	return {
		wuqi_id = 0,
		wing_jinhua_grade = 0,
		xianjian_id = 0,
		wing_special_imageid = 0,
		footprint_effect_id = 0,
		wing_use_grade = 0,
		fashion_wuqi = 0,
		fashion_body = 0,
		fashion_foot = 0,
		fashion_bubble = 0,
		fashion_photoframe = 0,
		fashion_guanghuan = 0,
		body_color = 0,
		waist = 0,
		mask = 0,
		tail = 0,
		shouhuan = 0,
		qilinbi = -1,
		jianzhen = 0,
		clothes_id = 0,
		fazhen_id = -1,
		skill_halo_id = 0,
		part_color = {},
	}
end

-- 默认角色外观数据
function ProtocolStruct.RoleDiyAppearance()
	return {
		--头发
		hair_color = {},

		--眼睛
		eye_size = 0,
		eye_position = 0,
		eye_shadow_color = {},

		--瞳孔左
		left_pupil_type = 0,
		left_pupil_size = 0,
		left_pupil_color = {},

		--瞳孔右
		right_pupil_type = 0,
		right_pupil_size = 0,
		right_pupil_color = {},

		--嘴巴
		mouth_size = 0,
		mouth_position = 0,
		mouth_color = {},

		--脸部贴花
		face_decal_id = 0,

		--预设索引
		preset_seq = 1,
	}
end

-- 读取角色外观数据
function ProtocolStruct.ReadRoleAppearance()
	local appearance = ProtocolStruct.RoleAppearance()
	appearance.wuqi_id = MsgAdapter.ReadUShort()
	appearance.clothes_id = MsgAdapter.ReadUShort()
	appearance.wing_appeid = MsgAdapter.ReadShort()
	appearance.fabao_appeid = MsgAdapter.ReadShort()
	appearance.shenwu_appeid = MsgAdapter.ReadShort()
	appearance.linggong_appeid = MsgAdapter.ReadShort()
	appearance.jianzhen_appeid = MsgAdapter.ReadShort()
	appearance.shizhuang_wuqi = MsgAdapter.ReadShort()
	appearance.shizhuang_body = MsgAdapter.ReadShort()
	appearance.shizhuang_foot = MsgAdapter.ReadShort()
	appearance.shizhuang_guanghuan = MsgAdapter.ReadShort()
	appearance.shizhuang_photoframe = MsgAdapter.ReadShort()
	appearance.shizhuang_bubble = MsgAdapter.ReadShort()
	appearance.shizhuang_mask = MsgAdapter.ReadShort()
	appearance.shizhuang_belt = MsgAdapter.ReadShort()
	appearance.shizhuang_tail = MsgAdapter.ReadShort()
    appearance.shizhuang_bracelet = MsgAdapter.ReadShort()
    appearance.yuyi_skillindex = MsgAdapter.ReadShort()			-- 羽翼当前装备的技能 --判断是否飞行
	appearance.tianshenbagua_waiguan = MsgAdapter.ReadShort()   -- 八卦外观(0:没有 1-对应第一套八卦)
	appearance.anqi_id = MsgAdapter.ReadUShort()				-- 暗器id
	appearance.ruanjia_id = MsgAdapter.ReadUShort()				-- 软甲id
	appearance.default_face_res_id = MsgAdapter.ReadShort()		-- 创角时选择的脸型资源id
	appearance.default_hair_res_id = MsgAdapter.ReadShort()		-- 创角时选择的发型资源id
	appearance.default_body_res_id = MsgAdapter.ReadShort()		-- 创角时选择的衣服资源id
	appearance.fazhen_id = MsgAdapter.ReadShort()
	appearance.skill_halo_id = MsgAdapter.ReadShort()
	-- 时装染色
	local color_info = {}
	-- for m = 1, 3 do
	-- 	color_info[m] = {}
	-- 	color_info[m].r = MsgAdapter.ReadUChar()
	-- 	color_info[m].g = MsgAdapter.ReadUChar()
	-- 	color_info[m].b = MsgAdapter.ReadUChar()
	-- 	color_info[m].a = MsgAdapter.ReadUChar()
	-- end

	for m = 1, SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PART_COUNT do
		if m <= SHIZHUANG_DYE_ENUM.MSG_CUR_DYEING_PART_COUNT then	-- 目前只取4个，服务器留了6个
			color_info[m] = {}
			color_info[m].r = MsgAdapter.ReadUChar()
			color_info[m].g = MsgAdapter.ReadUChar()
			color_info[m].b = MsgAdapter.ReadUChar()
			color_info[m].a = MsgAdapter.ReadUChar()
		else
			MsgAdapter.ReadUInt()
		end
	end

	appearance.part_color = color_info
	appearance.shizhuang_project_id = MsgAdapter.ReadInt()

	MsgAdapter.ReadStrN(12)

	-- 以下字段只是为了容错，实际上已经废弃
	appearance.wing_jinhua_grade = 0
	appearance.xianjian_id = -1
	appearance.wing_special_imageid = 0
	appearance.footprint_effect_id = -1
	appearance.divineweapon_puton_seq = -1
	appearance.wing_use_grade = 0
	appearance.qilinbi = -1

	local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.SHENBING, appearance.shizhuang_wuqi)
	appearance.fashion_wuqi = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BODY, appearance.shizhuang_body)
	appearance.fashion_body = image_cfg and image_cfg.resouce or 0
	appearance.body_color = image_cfg and image_cfg.color or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.FOOT, appearance.shizhuang_foot)
	appearance.fashion_foot = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BUBBLE, appearance.shizhuang_bubble)
	appearance.fashion_bubble = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME, appearance.shizhuang_photoframe)
	appearance.fashion_photoframe = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.HALO, appearance.shizhuang_guanghuan)
	appearance.fashion_guanghuan = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.MASK, appearance.shizhuang_mask)
	appearance.mask = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BELT, appearance.shizhuang_belt)
	appearance.waist = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.WEIBA, appearance.shizhuang_tail)
	appearance.tail = image_cfg and image_cfg.resouce or 0

	image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.SHOUHUAN, appearance.shizhuang_bracelet)
	appearance.shouhuan = image_cfg and image_cfg.resouce or 0

	return appearance
end

-- 读取定制角色外观数据（服务端10个LL）
function ProtocolStruct.ReadRoleDiyAppearance()
	local role_diy_appearance = ProtocolStruct.RoleDiyAppearance()
	--头发
	local hair_color_info = {}
	hair_color_info.r = MsgAdapter.ReadUChar()
	hair_color_info.g = MsgAdapter.ReadUChar()
	hair_color_info.b = MsgAdapter.ReadUChar()
	hair_color_info.a = MsgAdapter.ReadUChar()
	role_diy_appearance.hair_color = hair_color_info
	--眼睛
	role_diy_appearance.eye_size = MsgAdapter.ReadShort()
	role_diy_appearance.eye_position = MsgAdapter.ReadShort()

	local eye_shadow_color_info = {}
	eye_shadow_color_info.r = MsgAdapter.ReadUChar()
	eye_shadow_color_info.g = MsgAdapter.ReadUChar()
	eye_shadow_color_info.b = MsgAdapter.ReadUChar()
	eye_shadow_color_info.a = MsgAdapter.ReadUChar()
	role_diy_appearance.eye_shadow_color = eye_shadow_color_info

	--瞳孔 左
	role_diy_appearance.left_pupil_type = MsgAdapter.ReadUShort()
	role_diy_appearance.left_pupil_size = MsgAdapter.ReadShort()

	local left_pupil_color_info = {}
	left_pupil_color_info.r = MsgAdapter.ReadUChar()
	left_pupil_color_info.g = MsgAdapter.ReadUChar()
	left_pupil_color_info.b = MsgAdapter.ReadUChar()
	left_pupil_color_info.a = MsgAdapter.ReadUChar()
	role_diy_appearance.left_pupil_color = left_pupil_color_info

	--瞳孔 右
	role_diy_appearance.right_pupil_type = MsgAdapter.ReadUShort()
	role_diy_appearance.right_pupil_size = MsgAdapter.ReadShort()

	local right_pupil_color_info = {}
	right_pupil_color_info.r = MsgAdapter.ReadUChar()
	right_pupil_color_info.g = MsgAdapter.ReadUChar()
	right_pupil_color_info.b = MsgAdapter.ReadUChar()
	right_pupil_color_info.a = MsgAdapter.ReadUChar()
	role_diy_appearance.right_pupil_color = right_pupil_color_info

	--嘴巴
	role_diy_appearance.mouth_size = MsgAdapter.ReadShort()
	role_diy_appearance.mouth_position = MsgAdapter.ReadShort()

	local mouth_color_info = {}
	mouth_color_info.r = MsgAdapter.ReadUChar()
	mouth_color_info.g = MsgAdapter.ReadUChar()
	mouth_color_info.b = MsgAdapter.ReadUChar()
	mouth_color_info.a = MsgAdapter.ReadUChar()
	role_diy_appearance.mouth_color = mouth_color_info

	--脸部贴花
	role_diy_appearance.face_decal_id = MsgAdapter.ReadUShort()
	role_diy_appearance.preset_seq = MsgAdapter.ReadUShort()
	MsgAdapter.ReadStrN(40)
	return role_diy_appearance
end

-- 背包里的物品数据
function ProtocolStruct.ReadKnapsackInfo()
	local t = {}
	t.item_id = MsgAdapter.ReadUShort()
	t.index = MsgAdapter.ReadShort()
	t.num = MsgAdapter.ReadShort()
	t.is_bind = MsgAdapter.ReadChar()
	t.has_param = MsgAdapter.ReadChar()	  --有为1，无为0
	t.invalid_time = MsgAdapter.ReadUInt()
	t.knapsack_type = KNAPSACK_TYPE.NORMAL
	return t
 end

-- 读取物品参数数据
function ProtocolStruct.ReadItemParamData()
	local t = CommonStruct.ItemParamData()
	t.strengthen_level = MsgAdapter.ReadUChar()			--强化等级
	t.shen_level = MsgAdapter.ReadUChar() 				--随机属性类型1
	t.fuling_level = MsgAdapter.ReadUChar()				--随机属性类型2
	t.star_level = MsgAdapter.ReadUChar()				--星星等级

	t.has_lucky = MsgAdapter.ReadUShort()		-- (改为强化幸运值)	 --随机属性值2
	t.fumo_id = MsgAdapter.ReadUShort()			-- 附魔id			-- 随机属性值3

	--仙品属性
	t.xianpin_type_list = {}
	for i=1,COMMON_CONSTS.XIANPIN_MAX_NUM do
		local xianpin_type = MsgAdapter.ReadInt()
		if xianpin_type > 0 then
			table.insert(t.xianpin_type_list, xianpin_type)
		end
	end
	t.param1 = MsgAdapter.ReadInt()        -- 改为附魔失效时间

	t.random_arrt_val = MsgAdapter.ReadUShort()      -- 随机属性值1
	t.random_attr_type = MsgAdapter.ReadUChar()      -- 随机属性类型3
	t.impression = MsgAdapter.ReadChar()         -- 装备印记

	t.has_rand_xianpin = MsgAdapter.ReadChar()
	MsgAdapter.ReadUChar()
	MsgAdapter.ReadShort()

	MsgAdapter.ReadChar()
	t.is_refine = MsgAdapter.ReadChar()					--是否真炼
	t.get_impression_grade = MsgAdapter.ReadUChar()		--获取印记的阶数
	MsgAdapter.ReadChar()

	return t
end

-- 读取物品数据
function ProtocolStruct.ReadItemDataWrapper()
	local t = CommonStruct.ItemDataWrapper()
	t.item_id = MsgAdapter.ReadUShort()
	MsgAdapter.ReadShort()
	t.num = MsgAdapter.ReadInt()
	t.is_bind = MsgAdapter.ReadShort()
	t.has_param = MsgAdapter.ReadShort()
	t.invalid_time = MsgAdapter.ReadUInt()
	t.gold_price = MsgAdapter.ReadInt()
	t.param = ProtocolStruct.ReadItemParamData()
	return t
end

--读取人物技能数据
function ProtocolStruct.ReadRoleSkillInfoItem()
	local t = {}
	t.index = MsgAdapter.ReadShort()
	t.skill_id = MsgAdapter.ReadUShort()
	t.level = MsgAdapter.ReadInt()
	t.last_perform = MsgAdapter.ReadLL()
	t.skill_exp = MsgAdapter.ReadInt()
	t.awake_level = MsgAdapter.ReadInt()
	t.cd_past_time = MsgAdapter.ReadLL()
	return t
end

--读取任务单条数据
function ProtocolStruct.ReadTaskInfo()
	local t = {}
	t.task_id = MsgAdapter.ReadUShort()
	t.task_ver = MsgAdapter.ReadChar()
	t.task_condition = MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
	t.is_complete = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	t.accept_time = MsgAdapter.ReadUInt()
	t.progress_num = MsgAdapter.ReadInt()
	return t
end

-- 读取仙盟信息列表数据
function ProtocolStruct.ReadAllGuildInfo()
	local item = {}
	item.guild_id = MsgAdapter.ReadInt()
	item.guild_name = MsgAdapter.ReadStrN(32)
	item.tuanzhang_uid = MsgAdapter.ReadInt()
	item.mengzhu_name = MsgAdapter.ReadStrN(32)
	item.create_time = MsgAdapter.ReadUInt()
	item.guild_level = MsgAdapter.ReadInt()
	item.cur_member_num = MsgAdapter.ReadInt()
	item.max_member_num = MsgAdapter.ReadInt()
	item.camp = MsgAdapter.ReadChar()
	item.vip_type = MsgAdapter.ReadChar()
	item.applyfor_setup = MsgAdapter.ReadShort()
	item.jingjie_level = MsgAdapter.ReadInt()         --盟主境界等级
	item.union_guild_id = MsgAdapter.ReadInt()
	item.applyfor_need_capability = MsgAdapter.ReadLL()
	item.applyfor_need_level = MsgAdapter.ReadInt()
	item.active_degree = MsgAdapter.ReadInt()
	item.guild_all_capability = MsgAdapter.ReadLL()
	item.zone = MsgAdapter.ReadInt() 				  --仙盟评级
	item.guild_money = MsgAdapter.ReadInt() 		  --仙盟资金
	item.has_applying = MsgAdapter.ReadInt() 		  --是否申请中
	item.last_day_active_count = MsgAdapter.ReadInt() --活跃人数
	item.flag_id = MsgAdapter.ReadShort()			  -- 旗帜id
	item.flag_color = MsgAdapter.ReadShort()		  -- 旗帜颜色
    item.flag_name = MsgAdapter.ReadStrN(32)		  -- 旗号
    item.mengzhu_fame = MsgAdapter.ReadInt()		  -- 名望
    item.mengzhu_level = MsgAdapter.ReadInt()		  -- 等级

	item.shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    item.reserved1 = MsgAdapter.ReadChar()      --预留1
    item.reserved2 = MsgAdapter.ReadShort()     --预留2    

	return item
end

-- 读取仙盟事件列表数据
function ProtocolStruct.ReadGuildEventItem()
	local item = {}
	item.event_type = MsgAdapter.ReadShort()
	item.event_owner_post = MsgAdapter.ReadShort()
	item.event_owner = MsgAdapter.ReadStrN(32)
	item.event_time = MsgAdapter.ReadUInt()

	item.big_event = MsgAdapter.ReadShort()
	item.cannot_remove = MsgAdapter.ReadShort()

	item.param0 = MsgAdapter.ReadInt()
	item.param1 = MsgAdapter.ReadInt()
	item.param2 = MsgAdapter.ReadInt()
	item.param3 = MsgAdapter.ReadInt()
	item.sparam0 = MsgAdapter.ReadStrN(32)
	item.ep_sting = MsgAdapter.ReadStrN(128)
	item.item_data = {}
	item.item_data.strengthen_level=MsgAdapter.ReadUChar()                      -- 强化等级
    item.item_data.rand_attr_type_1=MsgAdapter.ReadUChar()                 		-- 随机属性1
    item.item_data.rand_attr_type_2=MsgAdapter.ReadUChar()                      --随机属性1
    item.item_data.star_level=MsgAdapter.ReadUChar()                        	--星星等级

    item.item_data.rand_attr_val_2 =MsgAdapter.ReadShort()                      -- 随机属性2
    item.item_data.rand_attr_val_3=MsgAdapter.ReadShort()                       -- 随机属性3
    item.item_data.star_exp = MsgAdapter.ReadInt()
    item.item_data.fumo_invalid_time= MsgAdapter.ReadInt()
    item.item_data.rand_attr_val_1= MsgAdapter.ReadShort()
    item.item_data.rand_attr_type_3=MsgAdapter.ReadUChar()
    item.item_data.param2=MsgAdapter.ReadUChar()
    item.item_data.xianpin_type_list = {}
     for i=1,6 do
     	item.item_data.xianpin_type_list[i] = MsgAdapter.ReadInt()
     end

      item.item_data.has_rand_xianpin=MsgAdapter.ReadUChar()                       --是否随机生成过仙品属性
      item.item_data.reserve_ch=MsgAdapter.ReadUChar()
      item.item_data.reserve_sh= MsgAdapter.ReadShort()

	return item
end

-- 读取仙盟成员列表数据
function ProtocolStruct.ReadGuildMemberItem()
	local item = {}
	item.uid = MsgAdapter.ReadInt()
	item.role_name = MsgAdapter.ReadStrN(32)
	item.level = MsgAdapter.ReadInt()
	item.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	item.post = MsgAdapter.ReadChar()
	item.vip_type = MsgAdapter.ReadChar()
	local photframe = MsgAdapter.ReadShort()
	-- item.reserve_ch = MsgAdapter.ReadChar()
	item.fenpei_reward_count = MsgAdapter.ReadShort()  --用作六界争霸分配活动奖励次数
	item.vip_level = MsgAdapter.ReadShort()
	item.is_online = MsgAdapter.ReadShort()
	item.join_time = MsgAdapter.ReadUInt()
	item.last_login_time = MsgAdapter.ReadUInt()
	item.gongxian = MsgAdapter.ReadInt()
	item.total_gongxian = MsgAdapter.ReadInt()
	item.capability = MsgAdapter.ReadLL()
	item.avatar_key_big = MsgAdapter.ReadUInt()
	item.avatar_key_small = MsgAdapter.ReadUInt()
	-- item.avatar_timetamp = MsgAdapter.ReadLL()
	item.team_index = MsgAdapter.ReadInt()
	item.guild_battle_allocate_time = MsgAdapter.ReadInt()		--仙盟爭霸分配獎勵次數
	item.zhandui_id = MsgAdapter.ReadUUID()
	item.chongzhi_num = MsgAdapter.ReadInt()
	item.relation_flag = MsgAdapter.ReadUInt()
	-- local shizhuang_bubble = MsgAdapter.ReadShort()    -- 泡泡形象
	-- image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BUBBLE, shizhuang_bubble)
	-- item.fashion_bubble = image_cfg and image_cfg.resouce or 0

	item.shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    item.reserved1 = MsgAdapter.ReadChar()      --预留1
	item.gift_bargain_flag = MsgAdapter.ReadChar()			-- 礼包砍价标记
	item.gift_buy_flag = MsgAdapter.ReadChar()				-- 礼包购买标记
	item.jieyi_id = MsgAdapter.ReadInt()        -- --结义队伍ID
	item.prof = MsgAdapter.ReadInt()


	local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME, photframe)
	item.photframe = image_cfg and image_cfg.resouce or 0

	AvatarManager.Instance:SetAvatarKey(item.uid, item.avatar_key_big, item.avatar_key_small)
	return item
end

-- 读取申请加入仙盟列表数据
function ProtocolStruct.ReadGuildApplyItem()
	local item = {}
	item.uid = MsgAdapter.ReadInt()
	item.role_name = MsgAdapter.ReadStrN(32)
	item.level = MsgAdapter.ReadInt()
	item.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	item.vip_type = MsgAdapter.ReadChar()
	item.vip_level = MsgAdapter.ReadChar()
	item.capability = MsgAdapter.ReadLL()
	item.applyfor_time = MsgAdapter.ReadUInt()
	item.shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    item.reserved1 = MsgAdapter.ReadChar()      --预留1
    item.reserved2 = MsgAdapter.ReadShort()     --预留2 
	item.prof = MsgAdapter.ReadInt()

	return item
end

-- 读取角色外观列表
function ProtocolStruct.ReadOpponentInfo()
	local role_vo = GameVoManager.Instance:CreateVo(RoleVo)
	role_vo.role_id = MsgAdapter.ReadInt()
	role_vo.level = MsgAdapter.ReadInt()
	role_vo.camp = MsgAdapter.ReadChar()
	MsgAdapter.ReadUChar()
	role_vo.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	role_vo.name = MsgAdapter.ReadStrN(32)
	role_vo.capability = MsgAdapter.ReadLL()
	role_vo.appearance = ProtocolStruct.ReadRoleAppearance()
	role_vo.prof = MsgAdapter.ReadInt()

	return role_vo
end

-- 读取仙盟成员列表数据
function ProtocolStruct.ReadGuildMijingItem()
	local item = {}
	item.roleid = MsgAdapter.ReadInt()
	item.user_name = MsgAdapter.ReadStrN(32)
	item.hurt_val = MsgAdapter.ReadLL()
	return item
end


-- 读取跨服组队列表数据
function ProtocolStruct.ReadCrossTeamMsgInfo()
	local item = {}
	item.index = MsgAdapter.ReadInt()
	item.must_check = MsgAdapter.ReadChar()
    item.member_can_invite = MsgAdapter.ReadChar()
    item.leader_index = MsgAdapter.ReadShort() + 1  --0 - 2
    item.capability_limit = MsgAdapter.ReadLL()
    item.min_level_limit = MsgAdapter.ReadShort()
    item.max_level_limit = MsgAdapter.ReadShort()
    item.member_list = {}
    local count = 0
    for i = 1, 3 do
        item.member_list[i] = ProtocolStruct.ReadCrossTeamMemberMsgInfo()
        item.member_list[i].index = i - 1
        item.member_list[i].is_leader = item.leader_index == i and 1 or 0
        if item.member_list[i].uuid.temp_low > 0 then
            count = count + 1
        end
    end
    item.cur_member_num = count
	return item
end

function ProtocolStruct.ReadCrossTeamMemberMsgInfo()
	local item = {}
	item.uuid = MsgAdapter.ReadUUID()
	item.usid = MsgAdapter.ReadUniqueServerID()
    item.name = MsgAdapter.ReadStrN(32)
    MsgAdapter.ReadChar()
    item.camp = MsgAdapter.ReadChar()
    item.sex = MsgAdapter.ReadChar()
    item.vip_level = MsgAdapter.ReadChar()
    item.level = MsgAdapter.ReadInt()
    item.capability = MsgAdapter.ReadLL()
    item.avatar_key_big = MsgAdapter.ReadUInt()
	item.avatar_key_small = MsgAdapter.ReadUInt()
    item.shizhuang_photoframe = MsgAdapter.ReadInt()
    item.jingjie_level = MsgAdapter.ReadInt()
    item.fame = MsgAdapter.ReadInt()
    item.shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    item.reserved1 = MsgAdapter.ReadChar()      --预留1
    item.reserved2 = MsgAdapter.ReadShort()     --预留2
    item.prof = MsgAdapter.ReadInt()
    AvatarManager.Instance:SetAvatarKey(item.uuid.temp_low, item.avatar_key_big, item.avatar_key_small)
	return item
end

--跨服组队附近的人
function ProtocolStruct.ReadCrossTeamMsgRoleInfo()
	local stu = {}
    stu.uuid = MsgAdapter.ReadUUID()
    stu.usid = MsgAdapter.ReadUniqueServerID()
	stu.name = MsgAdapter.ReadStrN(32)
	stu.avatar = MsgAdapter.ReadChar()
	stu.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadUChar()
	stu.camp = MsgAdapter.ReadChar()
	stu.capability = MsgAdapter.ReadLL()
	stu.level = MsgAdapter.ReadInt()
	stu.avatar_key_big = MsgAdapter.ReadUInt()
	stu.avatar_key_small = MsgAdapter.ReadUInt()
	stu.shizhuang_photoframe = MsgAdapter.ReadInt()
	stu.team_index = MsgAdapter.ReadInt()
	stu.vip_level = MsgAdapter.ReadInt()
	stu.orig_uid = MsgAdapter.ReadInt()
	stu.zhandui_id = MsgAdapter.ReadUUID()
	stu.relation_flag = MsgAdapter.ReadUInt()
	stu.shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    stu.reserved1 = MsgAdapter.ReadChar()      --预留1
    stu.reserved2 = MsgAdapter.ReadShort()     --预留2
	stu.prof = MsgAdapter.ReadInt()
    AvatarManager.Instance:SetAvatarKey(stu.uuid.temp_low, stu.avatar_key_big, stu.avatar_key_small)
	return stu
end

-- 读取仙盟成员列表数据
function ProtocolStruct.ReadZhuliUserList()
	local zhuli_list = {}
	zhuli_list.role_id = MsgAdapter.ReadInt()
	zhuli_list.role_name = MsgAdapter.ReadStrN(32)
	zhuli_list.is_online = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
	return zhuli_list
end

-- 8513
function ProtocolStruct.ReadTaskList()
	local task_list = {}
	task_list.status = MsgAdapter.ReadInt()
	task_list.process = MsgAdapter.ReadInt()
	task_list.complete_timestamp = MsgAdapter.ReadUInt()
	return task_list
end

-- 8514
function ProtocolStruct.ReadTaskInfoList()
	local taskinfo_list = {}
	taskinfo_list.status = MsgAdapter.ReadInt()
	taskinfo_list.process = MsgAdapter.ReadInt()
	taskinfo_list.complete_timestamp = MsgAdapter.ReadUInt()
	return taskinfo_list
end

--8511
function ProtocolStruct.ReadChangeInfo()
	local changeinfo_list = {}
	changeinfo_list.process = MsgAdapter.ReadInt()
	changeinfo_list.cur_status = MsgAdapter.ReadChar()
	changeinfo_list.stage = MsgAdapter.ReadChar()
	changeinfo_list.task_index = MsgAdapter.ReadShort()

	changeinfo_list.complete_timestamp = MsgAdapter.ReadUInt()
	return changeinfo_list
end

--1877
function ProtocolStruct.ReadGuildBuildTaskInfo()
	local list = {}
	list.task_id = MsgAdapter.ReadUShort()
	--服务器的0和客户端的1对应依次，具体有原因
	list.task_states = MsgAdapter.ReadUShort() + 1
	return list
end

-- 特殊形象索引从1开始 0默认形象
function ProtocolStruct.ReadTianshenUpgradeItem()
	local tianshen_list = {}
	tianshen_list.cao = MsgAdapter.ReadUChar() -- 激活了多少个槽
	tianshen_list.star = MsgAdapter.ReadUChar() -- 激活了多少个星
	tianshen_list.level = MsgAdapter.ReadUChar() -- 激活了多少阶级
	tianshen_list.zhan_index = MsgAdapter.ReadChar() -- 出战位置 -1 未出战 其他为出战位置
	tianshen_list.active_skill_flag = MsgAdapter.ReadUShort() -- 激活的技能
	tianshen_list.jingshen = MsgAdapter.ReadShort() -- 神饰晋升
	tianshen_list.activity_suit_sign = 0 --废弃保留
	tianshen_list.uplevel_exp_val = MsgAdapter.ReadUInt() -- 当前阶级溢出经验
	tianshen_list.equip_part = {}
	tianshen_list.bianshen_end_time = MsgAdapter.ReadUInt() -- 天神形象变身时间戳
	tianshen_list.is_bianshen = MsgAdapter.ReadChar() -- 是否变身状态 1变身状态
	tianshen_list.passskill_level = {}
	for i=0, TIANSHEN_PASSSKILL_COUNT - 1 do
		tianshen_list.passskill_level[i] = MsgAdapter.ReadUChar()
	end
	tianshen_list.next_bianshen_time = MsgAdapter.ReadUInt() -- 下次可变身时间戳
	tianshen_list.shenshi_part = ProtocolStruct.ReadTianShenShenShi()

	tianshen_list.shenshi_refine_value_list = {}
	for i = 1, TIANSHEN_SHENSHI_REFINE_MA do
		tianshen_list.shenshi_refine_value_list[i] = MsgAdapter.ReadInt()
	end
	tianshen_list.capability_score = MsgAdapter.ReadLL()
	return tianshen_list
end

function ProtocolStruct.ReadTianShenShenShi()
	local shenshi_info = {}
	local count = 4
	for i=1,4 do
		shenshi_info[i] = {}
		shenshi_info[i].item_id = MsgAdapter.ReadUShort()
		shenshi_info[i].zhanli = 0
		MsgAdapter.ReadShort()
	end
	return shenshi_info
end

-- 读取仙界boss掉落日志数据
function ProtocolStruct.ReadXianjieDropItem()
    local temp = {}
    temp.uuid = MsgAdapter.ReadUUID()
    temp.usid_str = MsgAdapter.ReadUniqueServerIDStr() --服务器id
    temp.role_name = MsgAdapter.ReadStrN(32)
    temp.pickup_timestamp = MsgAdapter.ReadUInt()
    temp.scene_id = MsgAdapter.ReadInt()
    temp.monster_id = MsgAdapter.ReadUShort()
    temp.item_id = MsgAdapter.ReadUShort()
    temp.item_data = ProtocolStruct.ReadItemDataWrapper()
	return temp
end

-- 读取仙界boss敌人信息数据
function ProtocolStruct.ReadXianjieEnemyItem()
    local temp = {}
    temp.uuid = MsgAdapter.ReadUUID()
    temp.usid = MsgAdapter.ReadUniqueServerID()
    temp.area_index = MsgAdapter.ReadInt()
    temp.role_name = MsgAdapter.ReadStrN(32)
    temp.drop_item_id = MsgAdapter.ReadUShort()
    MsgAdapter.ReadShort()
    temp.drop_time = MsgAdapter.ReadUInt()
    temp.guild_id = MsgAdapter.ReadInt()
	return temp
end


-- 天神背包
function ProtocolStruct.ReadTianshenBagItem()
	local bag_list = {}
	bag_list.bag_index = 0 -- 位置索引 == 列表索引
	bag_list.pingfen = 0 -- 综合评分，TianShenData那里赋值
	bag_list.base_pingfen = 0 -- 装备评分，TianShenData那里赋值
	bag_list.item_id = MsgAdapter.ReadUShort() --
	bag_list.bind_count = MsgAdapter.ReadUShort() -- 进阶消耗道具绑定数量 绑定数量=总数量-非绑定
	bag_list.stren_level = MsgAdapter.ReadUChar() -- 强化等级
	bag_list.grade_level = MsgAdapter.ReadUChar() -- 进阶等级
	bag_list.stren_exp = MsgAdapter.ReadUShort() -- 当前强化等级溢出 经验/精华
	bag_list.is_bind = MsgAdapter.ReadUChar() --
	bag_list.star_level = MsgAdapter.ReadUChar() --
	bag_list.reserve_ch1 = MsgAdapter.ReadChar() --
	bag_list.reserve_ch2 = MsgAdapter.ReadChar() --

	local item_count = 3
	bag_list.xianpin_list = {}
	for i = 1, item_count do
		bag_list.xianpin_list[i] = ProtocolStruct.ReadXianpinAttrItem()
	end
	return bag_list
end

-- 八卦背包
function ProtocolStruct.ReadBaGuaBagItem()
	local bag_list = {}
	bag_list.item_id = MsgAdapter.ReadUShort() --八卦id
	bag_list.level = MsgAdapter.ReadUShort() --八卦等级 默认1级
	bag_list.bind = MsgAdapter.ReadUChar() --是否绑定
	bag_list.reserve_hc0 = MsgAdapter.ReadUChar() --
	bag_list.reserve_ch1 = MsgAdapter.ReadChar() --
	bag_list.reserve_ch2 = MsgAdapter.ReadChar() --
	return bag_list
end

-- 仙品属性
function ProtocolStruct.ReadXianpinAttrItem()
	local xianpin_list = {}
	xianpin_list.attr_id = MsgAdapter.ReadUChar() -- 属性id
	xianpin_list.is_star = MsgAdapter.ReadChar() --
	xianpin_list.attr_value = MsgAdapter.ReadUShort() -- 仙品属性类型对应的值
	return xianpin_list
end

-- 主动技能列表信息
function ProtocolStruct.ReadTianshenSkillList()
	local skill_list = {}
	skill_list.index = MsgAdapter.ReadInt() -- 天神索引
	skill_list.skill_info_list = {}
	for i=1,10 do
		skill_list.skill_info_list[i] = ProtocolStruct.ReadSkillInfo()
	end

	return skill_list
end

-- 主动技能信息
function ProtocolStruct.ReadSkillInfo()
	local skill_info = {}
	skill_info.level = MsgAdapter.ReadUChar() --
	skill_info.reserve_uc = MsgAdapter.ReadUChar() --
	skill_info.id = MsgAdapter.ReadUShort() --

	return skill_info
end

-- 天神神器信息
function ProtocolStruct.ReadTianShenShenqiItem()
	local shenqi_info = {}
	shenqi_info.shenqi_id = MsgAdapter.ReadInt() -- 神器索引
	shenqi_info.is_active = MsgAdapter.ReadShort() -- 是否激活 0 未激活 1 激活
	shenqi_info.star = MsgAdapter.ReadShort() -- 星级
	shenqi_info.level = MsgAdapter.ReadShort() -- 等级
	shenqi_info.cur_huanhua_id = MsgAdapter.ReadShort() -- 当前使用的幻化id

	local count = 5
	shenqi_info.spirit_value_per = {} -- 附灵属性万分比
	for i=1,count do
		shenqi_info.spirit_value_per[i] = MsgAdapter.ReadInt()
	end

	count = 15
	shenqi_info.active_huandhua_ids = {} -- 已激活的幻化id
	for i=1,count do
		shenqi_info.active_huandhua_ids[i] = {}
		shenqi_info.active_huandhua_ids[i].star_lv = MsgAdapter.ReadUShort()
		shenqi_info.active_huandhua_ids[i].huandhua_ids = MsgAdapter.ReadUShort()
	end

	return shenqi_info
end

-- 商店物品信息
function ProtocolStruct.ReadShopItem()
	local ShopItem = {}
	ShopItem.item_index = MsgAdapter.ReadInt() -- 物品索引
	ShopItem.role_day_buy_cnt = MsgAdapter.ReadInt() -- 角色每日已购买次数
	ShopItem.guild_day_buy_cnt = MsgAdapter.ReadInt() -- 仙盟每日已购买次数
	ShopItem.server_day_buy_cnt = MsgAdapter.ReadInt() -- 全服每日购买次数
	ShopItem.role_week_buy_cnt = MsgAdapter.ReadInt() -- 角色每周已购买次数
	ShopItem.guild_week_buy_cnt = MsgAdapter.ReadInt() -- 仙盟每周已购买次数
	ShopItem.server_week_buy_cnt = MsgAdapter.ReadInt() -- 全服两周购买次数
	ShopItem.role_twoweek_buy_cnt = MsgAdapter.ReadInt() -- 角色两周已购买次数
	ShopItem.guild_twoweek_buy_cnt = MsgAdapter.ReadInt() -- 仙盟两周已购买次数
	ShopItem.server_twoweek_buy_cnt = MsgAdapter.ReadInt() -- 全服每周购买次数
	ShopItem.role_month_buy_cnt = MsgAdapter.ReadInt() -- 角色每月已购买次数
	ShopItem.guild_month_buy_cnt = MsgAdapter.ReadInt() -- 仙盟每月已购买次数
	ShopItem.server_month_buy_cnt = MsgAdapter.ReadInt() -- 全服每月购买次数
	ShopItem.role_whole_life_buy_cnt = MsgAdapter.ReadInt() -- 角色终身已购买次数
	ShopItem.guild_whole_life_buy_cnt = MsgAdapter.ReadInt() -- 仙盟终身已购买次数
	ShopItem.server_whole_life_buy_cnt = MsgAdapter.ReadInt() -- 全服终身已购买次数

	return ShopItem
end

-- 物品信息（离线挂机后）
function ProtocolStruct.ReadOfflineItemInfo()
	local item_info = {}
	item_info.item_id = MsgAdapter.ReadInt()
	item_info.num = MsgAdapter.ReadShort()
	item_info.is_bind = MsgAdapter.ReadShort()

	return item_info
end

function ProtocolStruct.ReadItemInfo()
	local item_info = {}
	item_info.item_id = MsgAdapter.ReadUShort()
	item_info.num = MsgAdapter.ReadShort()
	item_info.is_bind = MsgAdapter.ReadShort()
	item_info.star = MsgAdapter.ReadShort()

	return item_info
end

-- 市场单个商品信息
function ProtocolStruct.ReadMarketGoodsInfo()
	local t = {}
	t.item_data = ProtocolStruct.ReadItemDataWrapper()
	t.auction_index = MsgAdapter.ReadInt() 				-- 市场上架物品索引
	MsgAdapter.ReadInt()
	t.seller_id = MsgAdapter.ReadInt()  				-- 上架者 玩家即玩家uid 仙盟上架为仙盟id
	t.sale_time = MsgAdapter.ReadUInt() 				-- 上架时间
	t.total_price = MsgAdapter.ReadInt() 				-- 总价
	t.price_type = MsgAdapter.ReadShort() 				-- 1为铜币，2为元宝
	MsgAdapter.ReadShort()
	MsgAdapter.ReadInt()
	MsgAdapter.ReadChar()
	t.item_data.auction_index = t.auction_index
	t.item_data.total_price = t.total_price
	local has_password =  MsgAdapter.ReadChar() 		-- 是否有购买密码
	if has_password == 1 then
		t.has_password = true
	else
		t.has_password = false
	end

	t.item_data.has_password = t.has_password
	MsgAdapter.ReadShort()
	t.password = MsgAdapter.ReadInt()
	t.item_data.password = t.password
	t.next_yell_time = MsgAdapter.ReadUInt() 			-- 下次可吆喝时间戳
	return t
end

-- 市场单个求购信息
function ProtocolStruct.ReadMarketWantInfo()
	local t = {}
	t.wanted_index = MsgAdapter.ReadInt() 				-- 求购信息在列表的index
	t.wanted_uid = MsgAdapter.ReadInt() 				-- 求购者id
	t.wanted_name = MsgAdapter.ReadStrN(32) 			-- 求购者名称
	t.item_id = MsgAdapter.ReadUShort() 				-- 求购物品id
	t.equip_star = MsgAdapter.ReadShort() 				-- 求购装备星数
	t.item_num = MsgAdapter.ReadShort() 				-- 求购物品数目
	t.price_type = MsgAdapter.ReadShort() 				-- 1为铜币，2为元宝
	t.total_price = MsgAdapter.ReadInt() 				-- 总价
	t.wanted_time = MsgAdapter.ReadUInt() 				-- 求购时间
	return t
end

-- 投资卡购买信息
function ProtocolStruct.ReadInvestCardItem()
	local t = {}
	t.buy_timestamp = MsgAdapter.ReadUInt()				-- 购买时间
	t.fetch_reward_flag = MsgAdapter.ReadLL()			-- 奖励领取标记
	return t
end

--  深渊boss击杀历史信息
function ProtocolStruct.ReadShenyuanBossKillHistory()
	local t = {}
	t.killer_role_name = MsgAdapter.ReadStrN(32)
    t.kill_timestamp = MsgAdapter.ReadUInt()
    t.server_id = MsgAdapter.ReadInt()
    t.plat_type = MsgAdapter.ReadInt()
    t.box_country_index = MsgAdapter.ReadInt()
	return t
end

-- 深渊boss信息
function ProtocolStruct.ReadShenyuanBossInfo()
    local t = {}
    local KILL_HISTORY_MAX_COUNT = 10
	t.boss_id = MsgAdapter.ReadUShort()
    t.status = MsgAdapter.ReadShort()			-- 0不存在 1存在
    t.next_refresh_time = MsgAdapter.ReadUInt()
    t.top_hurt_role_name = MsgAdapter.ReadStrN(32)
    t.kill_count = MsgAdapter.ReadInt()
    t.kill_history = {}
    for i = 1, KILL_HISTORY_MAX_COUNT do
        local info = ProtocolStruct.ReadShenyuanBossKillHistory()
        if info.killer_role_name ~= nil and info.kill_timestamp ~= 0 then
            t.kill_history[# t.kill_history + 1] = info
        end
    end
	return t
end

--  深渊boss伤害列表
function ProtocolStruct.ReadShenyuanBossHurtRankItem()
	local t = {}
	t.user_id = MsgAdapter.ReadInt()
    t.uuid = MsgAdapter.ReadLL()
    t.role_name = MsgAdapter.ReadStrN(32)
    t.hurt = MsgAdapter.ReadLL()
	return t
end


function ProtocolStruct.ReadMsgItem()
	local t = {}
	t.item_id = MsgAdapter.ReadUShort()
	t.is_bind = MsgAdapter.ReadShort()
	t.num = MsgAdapter.ReadInt()
	t.star = MsgAdapter.ReadInt()
	return t
end

function ProtocolStruct.ReadKillPlayerItem()
	local t = {}
	t.uuid = MsgAdapter.ReadUUID()
	t.uuid_str = t.uuid.temp_low .. t.uuid.temp_high
	t.name = MsgAdapter.ReadStrN(32)
	t.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
	t.avatar_key_big = MsgAdapter.ReadUInt()
	t.avatar_key_small = MsgAdapter.ReadUInt()
	t.prof = MsgAdapter.ReadInt()
	return t
end

function ProtocolStruct:ReadTeamMemberInfoItem()
	local t = {}
	t.uid = MsgAdapter.ReadInt()
	t.name = MsgAdapter.ReadStrN(32)
	t.is_leader = MsgAdapter.ReadShort()
	t.level = MsgAdapter.ReadShort()
	t.vip_level = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	t.camp = MsgAdapter.ReadChar()
	t.sex = MsgAdapter.ReadChar()
	t.avatar_timestamp = MsgAdapter.ReadLL()
	t.shizhuang_photoframe = MsgAdapter.ReadInt()
	t.capability = MsgAdapter.ReadLL()
	t.shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    t.reserved1 = MsgAdapter.ReadChar()      --预留1
    t.reserved2 = MsgAdapter.ReadShort()     --预留2
	t.prof = MsgAdapter.ReadInt()
	return t
end

-- 小鸭疾走鸭子信息
function ProtocolStruct.ReadDuckInfo()
	local t = {}
	t.index = MsgAdapter.ReadInt() 				-- 鸭子下标
	t.duck_id = MsgAdapter.ReadInt()    		-- 鸭子id（对应配置表）
	t.bet_count = MsgAdapter.ReadInt()  		-- 下注数量
	t.peilv = MsgAdapter.ReadDouble() 			-- 赔率
	t.week_win_times = MsgAdapter.ReadShort() 	-- 本周胜利次数
	t.duck_objid = MsgAdapter.ReadUShort()		-- objid
	t.vote_times = MsgAdapter.ReadInt()			-- 投票次数
	t.rank = MsgAdapter.ReadInt()				-- 排名
	return t
end

-- 四象召唤特典
SCYuanShenZhaoHuanTeDianStruct = SCYuanShenZhaoHuanTeDianStruct or {}
function SCYuanShenZhaoHuanTeDianStruct.ReadSubActInfo()
	local product_sale_status = {}
	for i = 1, MAX_YUAN_SHEN_ZHAO_HUAN_SUBACT_SALE_PRODUCT_NUM do
		product_sale_status[i] = {}
		product_sale_status[i].status = MsgAdapter.ReadChar()	-- 0-未购买  1-已购买未领取（rmb商品会用到） 2-已购买已领取
		MsgAdapter.ReadChar()
		product_sale_status[i].buy_num = MsgAdapter.ReadShort()
	end
	return product_sale_status
end

-- 读取UniqueUserID
function ProtocolStruct:ReadUniqueUserID()
	local t = {}
	t.plat_type = MsgAdapter.ReadInt()							-- usid
	t.server_id = MsgAdapter.ReadInt()							-- 服务器ID
	t.user_id = MsgAdapter.ReadInt()							-- 角色id
	return t
end

-- 仙器装备读取
function ProtocolStruct:ReadXianQiInfo()
	local info = {}
	info.item_id = MsgAdapter.ReadInt()							-- 装备id
	info.color = MsgAdapter.ReadChar()							-- 品质
    info.star = MsgAdapter.ReadChar()							-- 星级
    info.level = MsgAdapter.ReadShort()							-- 等级
    info.grade = MsgAdapter.ReadChar()							-- 阶级
    info.special_effect_level = MsgAdapter.ReadChar()			-- 觉醒等级
    info.max_rand_index = MsgAdapter.ReadShort()				-- 刻铭上限索引
    info.rand_attr_list = {}									-- 刻铭属性
	for i=1,GameEnum.SHENJI_MAX_RAND_NUM do
		info.rand_attr_list[i] = MsgAdapter.ReadInt()
	end
	return info
end

-- 天神3v3匹配完成信息
function ProtocolStruct:ReadTianShen3v3MatchingPlayerInfo(side)
	local info = {}
	info.uid = MsgAdapter.ReadInt()
	info.server_id = MsgAdapter.ReadInt() 									-- 服务器id
	info.plat_type = MsgAdapter.ReadInt() 									-- 平台类型
	info.tianshen_index = MsgAdapter.ReadInt() 								-- 天神index
	info.role_name = MsgAdapter.ReadStrN(32) 								-- 角色名称
	info.score = MsgAdapter.ReadInt() 										-- 积分
	info.avatar_timestamp = MsgAdapter.ReadLL()								-- 头像
	info.photoframe = MsgAdapter.ReadShort()								-- 头像框
	info.sex = MsgAdapter.ReadChar()										-- 性别
	MsgAdapter.ReadChar()										-- 职业
	info.capability = MsgAdapter.ReadLL()									-- 战力
	info.prof = MsgAdapter.ReadInt()										-- 职业
	info.side = side
	return info
end


-- 天神3v3战斗场景实时玩家信息
function ProtocolStruct:ReadTianShen3v3ScenePlayerInfo(side)
	local info = {}
	info.uid = MsgAdapter.ReadInt() 										-- uid
	info.role_name = MsgAdapter.ReadStrN(32) 								-- 角色名称
	info.tianshen_index = MsgAdapter.ReadShort() 							-- 天神index
	info.server_id = MsgAdapter.ReadShort()  								-- 服务器id
	info.plat_type = MsgAdapter.ReadShort()  								-- 平台类型
	info.cur_hp_rate = MsgAdapter.ReadShort()  	 							-- 血量比例（0~100）
	info.relive_time = MsgAdapter.ReadUInt()  								-- 复活时间戳
	info.is_dead = info.cur_hp_rate <= 0
	info.side = side
	info.uuid_str = ToLLStr(info.plat_type, info.uid)
	return info
end

-- 天神3v3战斗场景占领点信息
function ProtocolStruct:ReadTianShen3v3OccupyPointData()
	local info = {}
	info.side_scene_score = {}
	info.occupying = false
	info.occupy_progress = MsgAdapter.ReadInt() 						-- 双方占领进度（-100~100）
	info.side_1_occupy_progress = (info.occupy_progress + 100) / 200
	info.side_0_occupy_progress = 1 - info.side_1_occupy_progress
	info.occupy_side = MsgAdapter.ReadShort() 							-- 占领方（-1是未占领）
	info.point_state = MsgAdapter.ReadShort()

	info.occupying = info.point_state == 2 								-- 是否抢夺中

	-- 占领状态
	info.occupy_state = TianShen3v3PointState.Unoccupy
	if info.occupying then
		info.occupy_state = TianShen3v3PointState.Occupying
	else
		if info.occupy_side >= 0 then
			info.occupy_state = TianShen3v3PointState.Occupyed
		end
	end

	return info
end

-- 天神3v3结算单个角色信息
function ProtocolStruct:ReadTianShen3v3FinishPlayerData()
	local info = {}
	info.role_name = MsgAdapter.ReadStrN(32)
	info.uuid_str = MsgAdapter.ReadUUIDStr()
	info.add_score = MsgAdapter.ReadInt()
	info.tianshen_index = MsgAdapter.ReadShort()
	info.kill_times = MsgAdapter.ReadShort()
	info.dead_times = MsgAdapter.ReadShort()
	info.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	info.server_id = MsgAdapter.ReadShort()
	MsgAdapter.ReadShort()
	info.prof = MsgAdapter.ReadInt()
	return info
end

-- 读取天神3v3小地图人物信息
function ProtocolStruct:ReadTianShen3v3MapInfo()
	local info = {}
	info.pos_x = MsgAdapter.ReadShort()
	info.pos_y = MsgAdapter.ReadShort()
	info.tianshen_index = MsgAdapter.ReadShort()
	info.side = MsgAdapter.ReadShort()
	info.uuid_str = MsgAdapter.ReadUUIDStr()
	return info
end

-- 天神3v3赛季排名单个角色信息
function ProtocolStruct:ReadSeasonRankRoleInfo()
	local info = {}
	info.role_name = MsgAdapter.ReadStrN(32)
	info.server_id = MsgAdapter.ReadInt()
	info.score = MsgAdapter.ReadInt()
	info.capability = MsgAdapter.ReadLL()
	return info
end

-- 天下第一单个排名信息
function ProtocolStruct:ReadWorldsNO1PlayerRankInfo()
	local info = {}
	info.role_name = MsgAdapter.ReadStrN(32)
	info.uuid_str = MsgAdapter.ReadUUIDStr()
	info.server_id = MsgAdapter.ReadInt()
	info.plat_type = MsgAdapter.ReadInt()
	info.avatar_key_big = MsgAdapter.ReadUInt()
	info.avatar_key_small = MsgAdapter.ReadUInt()
	info.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
	info.kill_count = MsgAdapter.ReadShort()
	info.dead_count = MsgAdapter.ReadShort()
	info.score_count = MsgAdapter.ReadInt()
	info.room_key = MsgAdapter.ReadShort()
	info.is_enter_room = MsgAdapter.ReadShort() > 0 				-- 是否曾经进过房间（进入下一子轮的准备时间才变回0）
	info.prof = MsgAdapter.ReadInt()
	info.room_is_end = info.room_key == 0 and info.is_enter_room	-- 是否已打完
	local _, uid = LLStrToInt(info.uuid_str)
	info.uid = uid
	AvatarManager.Instance:SetAvatarKey(uid, info.avatar_key_big, info.avatar_key_small)
	return info
end

-- 天下第一构造单个排名信息结构（对应ReadWorldsNO1PlayerRankInfo）
function ProtocolStruct:ConstructWorldsNO1PlayerRankInfo()
	local info = {}
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	info.role_name = main_vo.name
	info.uuid_str = RoleWGData.Instance:GetUUIDStr()
	info.server_id = RoleWGData.Instance:GetOriginServerId()
	info.plat_type = RoleWGData.Instance:GetPlatType()
	info.avatar_key_big = 0
	info.avatar_key_small = 0
	info.sex = RoleWGData.Instance:GetRoleSex()
	info.prof = RoleWGData.Instance:GetRoleProf()
	info.kill_count = 0
	info.dead_count = 0
	info.score_count = 0
	info.room_key = 0
	info.room_is_end = info.room_key == 0
	local _, uid = LLStrToInt(info.uuid_str)
	info.uid = uid
	info.rank = -1
	return info
end

-- 天下第一战斗场景单个角色实时信息
function ProtocolStruct:ReadWorldsNO1ScenePlayerInfo()
	local info = {}
	info.uuid_str = MsgAdapter.ReadUUIDStr()
	info.server_id = MsgAdapter.ReadInt()
	info.plat_type = MsgAdapter.ReadInt()
	info.role_name = MsgAdapter.ReadStrN(32)
	info.score = MsgAdapter.ReadInt()
	info.relive_count = MsgAdapter.ReadShort()
	info.obj_id = MsgAdapter.ReadUShort()

	info.avatar_key_big = MsgAdapter.ReadUInt()
	info.avatar_key_small = MsgAdapter.ReadUInt()
	local _, uid = LLStrToInt(info.uuid_str)
	info.uid = uid
	AvatarManager.Instance:SetAvatarKey(uid, info.avatar_key_big, info.avatar_key_small)

	info.max_hp = MsgAdapter.ReadLL()
	info.cur_hp = MsgAdapter.ReadLL()
	info.hp_percent = info.cur_hp / info.max_hp
	info.is_watch = MsgAdapter.ReadInt() == 1 													-- 是否是观战者
	info.is_out = (info.cur_hp <= 0 and info.relive_count <= 0) or info.is_watch 				-- 是否被淘汰
	return info
end

-- 天下第一结算单个角色信息
function ProtocolStruct:ReadWorldsNO1EndPlayerInfo()
	local info = {}
	info.rank = MsgAdapter.ReadInt() 				-- 排名
	info.role_name = MsgAdapter.ReadStrN(32) 		-- 角色名
	info.server_id = MsgAdapter.ReadInt() 			-- 服务器ID
	info.plat_type = MsgAdapter.ReadInt() 			-- 平台类型
	info.score = MsgAdapter.ReadInt() 				-- 积分
	return info
end

-- 仙界装备 - 神体信息
function ProtocolStruct.FairyLandEquipSlotInfo()
	local info = {}
	local flag = 0
	local max_page = GOD_BODY_ENUM.MAX_PAGE_COUNT

	info.grade = MsgAdapter.ReadShort()
	info.level = MsgAdapter.ReadShort()
	info.bless_value = MsgAdapter.ReadUInt()
	info.uplevel_end_time = MsgAdapter.ReadUInt()

	info.page_part_remind_flag = {}							-- 已读标记
	for page = 0, max_page - 1 do
		flag = MsgAdapter.ReadUInt()
		info.page_part_remind_flag[page] = bit:d2b(flag)
	end

	info.page_part_act_flag = {}
	for page = 0, max_page - 1 do
		flag = MsgAdapter.ReadUInt()
		info.page_part_act_flag[page] = bit:d2b(flag)
	end

	flag = MsgAdapter.ReadUInt()
	info.page_act_flag = bit:d2b(flag)
	return info
end

-- 圣装装备格子信息
function ProtocolStruct.ReadXianjieEquipBagGrid()
	local item_id = MsgAdapter.ReadUShort()
	local info = FairyLandEquipmentWGData.Instance:HolyEquipItemData(item_id)
	MsgAdapter.ReadShort()
	info.num = MsgAdapter.ReadInt()
	info.is_bind = MsgAdapter.ReadChar()
	info.param1 = MsgAdapter.ReadUChar()
	info.param2 = MsgAdapter.ReadUChar()
	info.param3 = MsgAdapter.ReadUChar()
	info.is_wear = item_id > 0

	return info
end

-- 圣装背包格子信息
function ProtocolStruct.ReadMsgXianjieEquipBagGrid()
	local index = MsgAdapter.ReadInt()
	local item_id = MsgAdapter.ReadUShort()
	local info = FairyLandEquipmentWGData.Instance:HolyEquipItemData(item_id)
	info.index = index
	MsgAdapter.ReadShort()
	info.num = MsgAdapter.ReadInt()
	info.is_bind = MsgAdapter.ReadChar()
	info.param1 = MsgAdapter.ReadUChar()
	info.param2 = MsgAdapter.ReadUChar()
	info.param3 = MsgAdapter.ReadUChar()
	return info
end

function ProtocolStruct.MsgXianjieSlotEquipInfo()
	local info = {}
	info.act_total_level = MsgAdapter.ReadInt()
	info.act_total_star = MsgAdapter.ReadInt()
	local equip_level = {}
	for i = 0,SHENG_ZHUANG_BROWSE.MAX_XIANJIE_EQUIP_SHENZHUANG_PART -1 do
		equip_level[i] = MsgAdapter.ReadShort()
	end

	local equip_grade = {}
	for i = 0,SHENG_ZHUANG_BROWSE.MAX_XIANJIE_EQUIP_SHENZHUANG_PART -1 do
		equip_grade[i] = MsgAdapter.ReadShort()
	end

	local rand_attr = {}
	for i = 0,SHENG_ZHUANG_BROWSE.MAX_XIANJIE_EQUIP_SHENZHUANG_PART-1 do
		rand_attr[i] = {}
		for t = 1, SHENG_ZHUANG_BROWSE.MAX_XIANJIE_EQUIP_RAND_ATTR do
			rand_attr[i][t] = MsgAdapter.ReadShort()
		end
	end
	local limit_color = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
	info.equip_list = {}
	for i = 0,SHENG_ZHUANG_BROWSE.MAX_XIANJIE_EQUIP_SHENZHUANG_PART -1 do
		info.equip_list[i] = ProtocolStruct.ReadXianjieEquipBagGrid()
		info.equip_list[i].strength_level = equip_level[i] or 0
		info.equip_list[i].grade = equip_grade[i] or 0
		info.equip_list[i].rand_attr = rand_attr[i] or {}
		info.equip_list[i].is_wear = true
		info.equip_list[i].star = FairyLandEquipmentWGData.Instance:GetHEEvolveStarNumByAttr(rand_attr[i])
	end
	return info
end

-- 背景信息
function ProtocolStruct:ReadBackInfo()
	local info = {}
	info.back_id = MsgAdapter.ReadInt() 				-- 背景id
	info.level = MsgAdapter.ReadInt() 					-- 背景等级
	return info
end

-- 武魂结构
function ProtocolStruct:ReadWuHunObjInfo()
	local info = {}
	info.wuhun_id = MsgAdapter.ReadInt() 				-- -1代表未激活
	info.exp =  MsgAdapter.ReadInt() 					-- 经验
	info.level =  MsgAdapter.ReadInt() 					-- 当前等级
	info.wuhun_type = MsgAdapter.ReadShort()			-- 武魂类型
	info.wuhun_value = MsgAdapter.ReadShort()			-- 武魂之力数值
	info.slot_index = MsgAdapter.ReadShort() 			-- -1代表未被任何技能槽挂载
	info.promotion_state =  MsgAdapter.ReadChar() 		-- 晋升状态，不需要晋升, 1 需要晋升
	info.lianhua_order =  MsgAdapter.ReadChar() 		-- 存储炼化激活状态
	info.break_times =  MsgAdapter.ReadInt() 			-- 突破次数
	info.shuxing_dan1 = MsgAdapter.ReadInt()            --属性丹的数量1
	info.shuxing_dan2 = MsgAdapter.ReadInt()            --属性丹的数量2
	info.shuxing_dan3 = MsgAdapter.ReadInt()            --属性丹的数量3
	info.star = MsgAdapter.ReadInt()                    --武魂星级
	info.material_buy_end_time = MsgAdapter.ReadUInt()  --武魂材料直购折扣结束.
	info.material_buy_grade = MsgAdapter.ReadUInt()     --武魂材料直购已购买的最高挡位.
	-- info.reserve1 =  MsgAdapter.ReadChar() 				-- 对齐一个字节
	return info
end

--套装定制技能信息
function ProtocolStruct:ReadWardrobeSkillInfo()
	local info = {}
	info.seq = MsgAdapter.ReadShort() 				-- 套装id
	info.skill_id =  MsgAdapter.ReadShort() 		-- 技能id
	info.skill_level =  MsgAdapter.ReadShort() 		-- 技能等级
	info.star = MsgAdapter.ReadShort()				-- 当前星级

	info.part_attr_list = {}
	for i = 1, 8 do
		info.part_attr_list[i] = {}
		info.part_attr_list[i].part = MsgAdapter.ReadShort() 				-- 套装id
		info.part_attr_list[i].attr1_id = {}
		for j = 1, 3 do
			info.part_attr_list[i].attr1_id[j] = MsgAdapter.ReadShort() 				-- 套装id
		end
		info.part_attr_list[i].attr2_id = MsgAdapter.ReadShort() 				-- 套装id
	end
	return info
end

-- 驭兽天下未孵化格子信息
function ProtocolStruct:ReadBeastEggBagInfo()
	local info = {}
	info.beast_id = MsgAdapter.ReadInt() 				-- 驭兽id

	return info
end

-- 驭兽天下孵化槽信息
function ProtocolStruct:ReadBeastBreedingSlot()
	local info = {}
	info.beast_id = MsgAdapter.ReadInt() 				-- 驭兽id
	info.effort_value = MsgAdapter.ReadInt() 			-- 成长值
	info.flair_values = {}
	for i = 1, BEAST_DEFINE.BEAST_FLAIR_COUNT_MAX do
		info.flair_values[i] = MsgAdapter.ReadInt()		-- 资质
	end
	info.skill_ids = {}
	for i = 1, BEAST_DEFINE.BEAST_PASSIVE_SKILL_COUNT_MAX do
		info.skill_ids[i] = MsgAdapter.ReadInt()		-- 技能
	end
	info.finish_timestamp = MsgAdapter.ReadLL()

	return info
end

-- 驭兽天下已孵化格子信息
function ProtocolStruct:ReadBeastBagInfo(i)
	local info = {}
	info.bag_index = i
	info.beast_id = MsgAdapter.ReadInt() 				-- 驭兽id
	info.exp = MsgAdapter.ReadInt() 					-- 经验
	info.effort_value = MsgAdapter.ReadInt() 			-- 成长值
	info.flair_values = {}
	for i = 1, BEAST_DEFINE.BEAST_FLAIR_COUNT_MAX do
		info.flair_values[i] = MsgAdapter.ReadInt()		-- 资质
	end
	info.skill_ids = {}
	for i = 1, BEAST_DEFINE.BEAST_PASSIVE_SKILL_COUNT_MAX do
		info.skill_ids[i] = MsgAdapter.ReadInt()		-- 技能
	end
	info.beast_level = MsgAdapter.ReadShort() 			-- 驭兽level
	info.stand_by_slot = MsgAdapter.ReadChar() 			-- 对应出战的位置（0-8）
	MsgAdapter.ReadChar()
	info.use_skin = MsgAdapter.ReadInt()
	info.refine_times = MsgAdapter.ReadInt()
	info.refine_item_num = MsgAdapter.ReadInt()
	info.refine_attr_list = {}
	for i = 1, BEAST_DEFINE.BEASTS_MAX_REFINE_ATTR_NUM do
		info.refine_attr_list[i] = {}
		info.refine_attr_list[i].attr_value = MsgAdapter.ReadLL()
		info.refine_attr_list[i].rand_value = MsgAdapter.ReadInt()
		info.refine_attr_list[i].pre_rand_value = MsgAdapter.ReadInt()
	end
	info.holy_spirit_link_index = MsgAdapter.ReadInt() -- 链接对象
	info.holy_spirit_level_list = {}					-- 圣魂等级信息
	for i = 0, BEAST_DEFINE.MAX_HOLY_SPIRIT_INDEX_COUNT - 1 do
		info.holy_spirit_level_list[i] = MsgAdapter.ReadChar()
	end
	return info
end

-- 驭兽天下上阵格子信息
function ProtocolStruct:ReadBeastStandBySlot()
	local info = {}
	info.bag_index = MsgAdapter.ReadInt() 				-- 映射到在BeastGridItem背包的下标
	info.slot_level = MsgAdapter.ReadInt() 				-- 槽位等级
	info.beast_id = MsgAdapter.ReadInt() 				-- 灵兽等级
	info.is_unlock = MsgAdapter.ReadChar() 				-- 是否解锁

	for i = 1, 3 do
		MsgAdapter.ReadChar() 							-- 3个占位符
	end

	return info
end

-- 驭兽天下星阵
function ProtocolStruct:ReadBeastStarCircle()
	local info = {}
	info.attr_list = {}

	for i = 1, BEAST_DEFINE.BEAST_CHANGE_STAR_CIRCLE_ATTR_COUNT_MAX do
		info.attr_list[i] = {}
		info.attr_list[i].id = MsgAdapter.ReadInt()
		info.attr_list[i].star_num = MsgAdapter.ReadInt()
	end

	info.interval_times = MsgAdapter.ReadInt()		-- 轮回数
	info.is_unlock = MsgAdapter.ReadChar()			-- 是否解锁
	info.is_need_promote = MsgAdapter.ReadChar() 	-- 是否需要突破到下一轮回才能继续升星

	for i = 1, 2 do
		MsgAdapter.ReadChar()
	end

	return info
end

-- 1vn积分排行Item
function ProtocolStruct.ReadCross1VNScoreRankItem()
	local info = {}
	info.uuid = MsgAdapter.ReadUUID()				-- 玩家uid
	info.name = MsgAdapter.ReadStrN(32)			-- 玩家名字
	info.sex = MsgAdapter.ReadChar()			-- 性别
	MsgAdapter.ReadChar()			-- 职业
	info.is_hide_vip = MsgAdapter.ReadChar() 	--
	info.rank = MsgAdapter.ReadChar()
	info.camp = MsgAdapter.ReadInt()			-- 玩家阵营(红蓝方)
	info.avatar = MsgAdapter.ReadLL()			-- 头像
	info.vip_level = MsgAdapter.ReadInt()		-- vip等级
	info.usid = MsgAdapter.ReadUniqueServerID()				-- 服务器id
	info.score = MsgAdapter.ReadLL()			-- 积分
	info.history_camp_flag = bit:d2b_two(MsgAdapter.ReadInt())
	info.stage_guess_flag = bit:d2b_two(MsgAdapter.ReadInt())		-- 对战场景竞猜标记 按bit计算 bit为SceneInfo的stage 如果当前stage未进行选择，则默认为己方阵营，否则使用stage_guess_camp字段
	info.stage_guess_camp_flag = bit:d2b_two(MsgAdapter.ReadInt())		-- 对战场景竞猜阵营
	info.appearance = ProtocolStruct.ReadRoleAppearance()			-- 外观
	info.prof = MsgAdapter.ReadInt()			-- 职业
	return info
end

-- 1vn膜拜Item
function ProtocolStruct.ReadCross1VNWorshipItem()
	local info = {}
	info.uid = MsgAdapter.ReadInt()				-- 玩家uid
	info.name = MsgAdapter.ReadStrN(32)			-- 玩家名字
	info.sex = MsgAdapter.ReadChar()			-- 性别
	MsgAdapter.ReadChar()			
	info.is_hide_vip = MsgAdapter.ReadChar() 	--
	info.reserve_ch = MsgAdapter.ReadChar()
	info.avatar = MsgAdapter.ReadLL()			-- 头像
	info.vip_level = MsgAdapter.ReadInt()		-- vip等级
	info.appearance = ProtocolStruct.ReadRoleAppearance()			-- 外观
	info.time = MsgAdapter.ReadUInt()
	info.worship_times = MsgAdapter.ReadInt()	
	info.worship_reward_times = MsgAdapter.ReadInt()	
	info.prof = MsgAdapter.ReadInt()			-- 职业
	
	return info
end

-- 武魂塔排行Item
function ProtocolStruct.ReadWuHunTowerRankItem()
	local info = {}
	info.uid = MsgAdapter.ReadInt()				-- 玩家uid
	info.name = MsgAdapter.ReadStrN(32)			-- 玩家名字
	info.sex = MsgAdapter.ReadChar()			-- 性别
	MsgAdapter.ReadChar()			-- 职业
	info.is_hide_vip = MsgAdapter.ReadChar() 	--
	info.rank = MsgAdapter.ReadChar()
	info.avatar = MsgAdapter.ReadLL()			-- 头像
	info.vip_level = MsgAdapter.ReadInt()		-- vip等级
	info.usid = MsgAdapter.ReadUniqueServerID()			-- 服务器id
	info.rank_value = MsgAdapter.ReadInt()			-- 积分
	info.rank_time = MsgAdapter.ReadUInt()		
	info.prof = MsgAdapter.ReadInt()			-- 职业
	return info
end

-- 天山修炼（历练副本）排行Item
function ProtocolStruct.ReadExpWestRankItem()
	local info = {}
	info.uid = MsgAdapter.ReadInt()						-- 玩家uid
	info.name = MsgAdapter.ReadStrN(32)					-- 玩家名字
	info.sex = MsgAdapter.ReadChar()					-- 性别
	MsgAdapter.ReadChar()					-- 职业
	info.is_hide_vip = MsgAdapter.ReadChar() 	
	info.rank = MsgAdapter.ReadChar()
	info.avatar = MsgAdapter.ReadLL()					-- 头像
	info.vip_level = MsgAdapter.ReadInt()				-- vip等级
	info.usid = MsgAdapter.ReadUniqueServerID()			-- 服务器id
	info.start_time = MsgAdapter.ReadUInt()				-- 挑战时间
	info.pass_time = MsgAdapter.ReadUInt()				-- 通关时间
	info.prof = MsgAdapter.ReadInt()					-- 职业
	return info
end

-- 装备洗练槽
function ProtocolStruct.ReadBaptizeSlotInfo()
	local info = {}
	info.is_buy = MsgAdapter.ReadChar()			-- 是否购买解锁 0:未解锁 1:已解锁
	info.is_lock = MsgAdapter.ReadChar()		-- 是否锁定 0:未锁定 1:已锁定
	info.word_type = MsgAdapter.ReadShort()		-- 词条(属性id)
	info.quality_value = MsgAdapter.ReadInt()   -- 品质值
	return info
end

-- 单个洗练部位
function ProtocolStruct.ReadEquipBaptizePart()
	local info = {}
	info.grade = MsgAdapter.ReadShort()						-- 阶级
	MsgAdapter.ReadShort()									-- 对齐
	info.total_score = MsgAdapter.ReadUInt()				-- 总评分
	info.slot_info = {}										-- 洗练槽信息
	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		info.slot_info[i] = ProtocolStruct.ReadBaptizeSlotInfo()
	end
	return info
end

-- 最强仙尊排名信息
function ProtocolStruct.ReadRankListInfo()
	local info = {}
	info.rank_pos = MsgAdapter.ReadInt()				--排名
	info.role_name = MsgAdapter.ReadName()				--角色名称
	info.capability = MsgAdapter.ReadLL()				--战力
	return info
end

-- 地鼠信息
function ProtocolStruct.ReadWhackMoleItem()
	local stu = {}
	stu.id = MsgAdapter.ReadShort()			-- id
	stu.hole = MsgAdapter.ReadChar()		-- 孔位信息
	stu.is_touch = MsgAdapter.ReadChar()	-- 是否点击
	stu.occur_ms = MsgAdapter.ReadInt()		-- 时间
	stu.stop_ms = MsgAdapter.ReadInt()		-- 时间
	return stu
end

-- 地鼠排行信息
function ProtocolStruct.ReadWhackMoleRankItem()
	local info = {}
	info.uid = MsgAdapter.ReadInt()						-- 玩家uid
	info.name = MsgAdapter.ReadStrN(32)					-- 玩家名字
	info.sex = MsgAdapter.ReadChar()					-- 性别
	MsgAdapter.ReadChar()					-- 职业
	info.is_hide_vip = MsgAdapter.ReadChar() 	
	info.rank = MsgAdapter.ReadChar()
	info.avatar = MsgAdapter.ReadLL()					-- 头像
	info.vip_level = MsgAdapter.ReadInt()				-- vip等级
	info.score = MsgAdapter.ReadInt()					-- 积分
	info.time = MsgAdapter.ReadUInt()					-- 时间
	info.prof = MsgAdapter.ReadInt()					-- 职业
	return info
end

-- 限时礼包信息
function ProtocolStruct.ReadPopGift()
	local info = {}
	info.grade = MsgAdapter.ReadInt()
	info.end_time = MsgAdapter.ReadUInt()
	info.gift = {}
	for i = 1, 4 do
		info.gift[i] = MsgAdapter.ReadChar()
	end
	return info
end

-- 幻梦秘境波次卡牌信息
function ProtocolStruct.ReadMsgCardItem()
	local info = {}
	info.choose_seq = MsgAdapter.ReadInt()							-- 选择卡牌索引
	info.random_seq_list = {}
	for i = 1, 3 do
		local card_info = {}
		card_info.seq = MsgAdapter.ReadShort()
		card_info.count = MsgAdapter.ReadShort() -- 已选择次数
		info.random_seq_list[i] = card_info
	end
	return info
end

-- 跨服空战拍卖道具信息
function ProtocolStruct.ReadAirWarAuctionItem()
	local info = {}
	info.auction_uuid 		= MsgAdapter.ReadUUID()		-- 角色uuid
    info.name 				= MsgAdapter.ReadStrN(32)	-- 角色名
	info.auction_price 		= MsgAdapter.ReadInt()		-- 拍卖出价价格
	info.auction_time 		= MsgAdapter.ReadUInt()		-- 拍卖出价时间戳
	return info
end

--百亿补贴玩家信息.
function ProtocolStruct.TenBillionSubsidyPlayerInfo()
	local info = {}
    info.uid = MsgAdapter.ReadInt()							-- Uid
	info.user_name = MsgAdapter.ReadName()					-- 玩家名
	info.sex = MsgAdapter.ReadChar()						-- 玩家性别
	MsgAdapter.ReadChar()						
	local reserve_ch_1 = MsgAdapter.ReadChar()					-- 预留
	local reserve_ch_2 = MsgAdapter.ReadChar()					-- 预留
	info.avatar_key_big = MsgAdapter.ReadUInt()				-- 头像
	info.avatar_key_small = MsgAdapter.ReadUInt()			-- 头像
	info.prof = MsgAdapter.ReadInt()						-- 玩家职业
	return info
end

--百亿补贴买一得多商店信息.
function ProtocolStruct.ReadPayOneGetMoreShopItemInfo()
	local info = {}
	info.item_seq = MsgAdapter.ReadUChar()			--商品索引
	info.refresh_flag = MsgAdapter.ReadUChar()		--是否刷新 刷新标识 0 未刷新 1已刷新
	info.buy_count = MsgAdapter.ReadUChar()			--购买次数
	info.reserve_ch = MsgAdapter.ReadChar()			--预留
	return info
end

-- 百倍爆率-排行榜信息.
function ProtocolStruct.ReadHundredfoldRankItem()
	local info = {}
	info.uid = MsgAdapter.ReadInt()				-- 玩家uid
	info.name = MsgAdapter.ReadName()			-- 玩家名字
	info.sex = MsgAdapter.ReadChar()			-- 性别
	MsgAdapter.ReadChar()
	info.is_hide_vip = MsgAdapter.ReadChar()
	info.rank = MsgAdapter.ReadChar()
	info.avatar = MsgAdapter.ReadLL()			-- 头像
	info.vip_level = MsgAdapter.ReadInt()		-- vip等级
	info.usid = MsgAdapter.ReadUniqueServerID()	-- 服务器id
	info.start_time = MsgAdapter.ReadUInt()		-- 挑战时间
	info.wave = MsgAdapter.ReadInt()			-- 波数
	info.prof = MsgAdapter.ReadInt()			-- 职业
	return info
end

-- 跨服空战-拍卖物品信息.
function ProtocolStruct.ReadAirAuctionItem()
	local info = {}
	info.seq = MsgAdapter.ReadShort()					-- 拍品Seq
	info.round = MsgAdapter.ReadShort()					-- 拍卖轮次
	info.auction_seq = MsgAdapter.ReadInt()				-- 拍品物品seq
	info.auction_uuid = MsgAdapter.ReadUUID()			-- 拍品UUID
	info.name = MsgAdapter.ReadName()					-- 玩家名字
	info.auction_price = MsgAdapter.ReadInt()			-- 拍品价格
	info.auction_time = MsgAdapter.ReadUInt()			-- 竞拍时间
	return info
end

--天财地宝-携手同行成员信息.
function ProtocolStruct.ReadTreasureTogetherPlayerInfo()
	local info = {}
	info.uid = MsgAdapter.ReadInt()							-- Uid
	info.user_name = MsgAdapter.ReadName()					-- 玩家名
	info.sex = MsgAdapter.ReadChar()						-- 玩家性别
	MsgAdapter.ReadChar()						
	info.shizhuang_photoframe = MsgAdapter.ReadShort()		-- 时装相框
	info.avatar_key_big = MsgAdapter.ReadUInt()				-- 头像
	info.avatar_key_small = MsgAdapter.ReadUInt()			-- 头像
	info.prof = MsgAdapter.ReadInt()						-- 玩家职业
	return info
end

--天财地宝-携手同行邀请好友信息
function ProtocolStruct.ReadTreasureTogetherFriendInfo()
	local info = {}
	info.uid = MsgAdapter.ReadInt()							-- Uid
	info.user_name = MsgAdapter.ReadName()					-- 玩家名
	info.level = MsgAdapter.ReadShort()						-- 等级
	info.shizhuang_photoframe = MsgAdapter.ReadShort()		-- 时装相框
	info.avatar_key_big = MsgAdapter.ReadUInt()				-- 头像
	info.avatar_key_small = MsgAdapter.ReadUInt()			-- 头像
	info.prof = MsgAdapter.ReadInt()						-- 玩家职业
	info.sex = MsgAdapter.ReadChar()						-- 玩家性别
	for i = 1, 3 do
		MsgAdapter.ReadChar()
	end
	return info
end

-- 跨服藏宝，灵珠信息
function ProtocolStruct.ReadMsgTreasureItem()
	local info = {}
	info.seq = MsgAdapter.ReadInt()						-- 灵珠seq
	info.level = MsgAdapter.ReadInt()					-- 灵珠等级
	info.gather_times = MsgAdapter.ReadInt()			-- 可采集次数
	info.time = MsgAdapter.ReadUInt()					-- 放置的时间
	info.pos_x = MsgAdapter.ReadInt()					-- 灵珠位置X
	info.pos_y = MsgAdapter.ReadInt()					-- 灵珠位置Y
	return info
end

-- 天财地宝-迷影寻踪任务
function ProtocolStruct.ReadPursuitTask()
	local info = {}
	info.process = MsgAdapter.ReadInt()					-- 任务进度
	info.fetch_flag = MsgAdapter.ReadChar()				-- 领取标记
	info.re_ch = MsgAdapter.ReadChar()
	info.re_sh = MsgAdapter.ReadShort()
	return info
end

-- 幻兽内丹-内丹出战孔位列表信息
function ProtocolStruct.ReadBeastEquipSlotItemListInfo()
	local info = {}
	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_SLOT_SEQ - 1 do
		info[j] = ProtocolStruct.ReadBeastEquipSlotItemInfo()
	end
	return info
end

-- 幻兽内丹-内丹孔位信息
function ProtocolStruct.ReadBeastEquipSlotItemInfo()
	local info = {}

	info.equip_info = ProtocolStruct.ReadBeastEquipSlotItemGridInfo()
	info.reserve_sh = MsgAdapter.ReadUShort()
	info.level = MsgAdapter.ReadUShort()
	info.exp = MsgAdapter.ReadLL()
	return info
end

-- 幻兽内丹-内丹孔位装备信息
function ProtocolStruct.ReadBeastEquipSlotItemGridInfo()
	local equip_data = {}
	equip_data.index = MsgAdapter.ReadShort()
	equip_data.reserve_sh = MsgAdapter.ReadShort()
	equip_data.item_id = MsgAdapter.ReadItemId()
	equip_data.is_bind = MsgAdapter.ReadChar()
	equip_data.reserve_ch = MsgAdapter.ReadChar()
	equip_data.words_list = {}

	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
		equip_data.words_list[j] = {}
		equip_data.words_list[j].words_seq = MsgAdapter.ReadShort()			-- 词条 seq = words / 100 words_value = words % 100
		equip_data.words_list[j].can_replace_words = MsgAdapter.ReadShort()	-- 可替换词条 seq = words / 100 words_value = words % 100
	end

	return equip_data
end

-- 仙盟商店-砍价记录
function ProtocolStruct.ReadGuildGiftBargainRecord()
	local data = {}
	data.uid = MsgAdapter.ReadInt()
	data.name = MsgAdapter.ReadName()
	data.reduce_value = MsgAdapter.ReadInt()			-- 砍价金额
	data.time = MsgAdapter.ReadUInt()					-- 时间
	return data
end

-- 仙盟任务信息
function ProtocolStruct.ReadGuildTaskInfo()
	local data = {}
	data.process = MsgAdapter.ReadInt()
	data.fetch_flag = MsgAdapter.ReadChar()
	data.re_ch = MsgAdapter.ReadChar()
	data.re_sh = MsgAdapter.ReadShort()
	return data
end

-- 仙盟活跃度排行榜信息
function ProtocolStruct.ReadGuildActiveScoreRankInfo()
	local data = {}
	data.uid = MsgAdapter.ReadInt()
	data.name = MsgAdapter.ReadName()
	data.sex = MsgAdapter.ReadShort()
	data.re_sh = MsgAdapter.ReadShort()
	data.prof = MsgAdapter.ReadInt()
	data.avatar_key_big = MsgAdapter.ReadUInt()				-- 头像
	data.avatar_key_small = MsgAdapter.ReadUInt()			-- 头像
	data.shizhuang_photoframe = MsgAdapter.ReadInt()		-- 时装相框
	data.active_score = MsgAdapter.ReadInt()				-- 活跃积分
	data.like_count  = MsgAdapter.ReadInt()					-- 点赞数
	return data
end