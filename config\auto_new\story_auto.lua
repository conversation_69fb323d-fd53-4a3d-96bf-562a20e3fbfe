-- J-剧情.xls

return {
robert_role={
[0]={id=0,born_x=347,relive_cd=0,sex=1,angle=90,max_hp=1000000,skill_id_list="1##1201##1202##1203##1204",},
[1000]={id=1000,name="大师兄",sex=1,min_gongji=1464,gongji=1564,skill_id_list="1##1201##1202##1203##1204",},
[1001]={id=1001,name="小师妹",born_y=503,relive_cd=0,min_gongji=2442,gongji=2542,skill_cd=2,atk_range=20,aoe_range=0,},
[1002]={id=1002,name="郑龙魂",born_x=51,born_y=97,relive_x=51,relive_y=97,min_gongji=2442,gongji=2542,},
[1003]={id=1003,name="安妮",born_x=62,relive_x=62,relive_y=103,angle=90,max_hp=1000000,},
[1004]={id=1004,name="<color=#72eba9>灵儿</color>",born_y=155,relive_x=266,relive_y=152,angle=90,model_res="actors/npc/6000_prefab##6000002",},
[1005]={id=1005,name="<color=#72eba9>江临宣</color>",born_x=71,born_y=171,relive_x=267,relive_y=170,wuqi_id=0,mount_imageid=0,wing_imageid=0,max_hp=80000,min_gongji=3500,gongji=4500,skill_id_list=1001,model_res="actors/npc/5029_prefab##5029002",},
[1006]={id=1006,name="<color=#dcfff6>ノ炎皇城守卫</color>",born_x=64,born_y=152,relive_x=260,relive_y=161,sex=1,wuqi_id=0,mount_imageid=0,wing_imageid=0,max_hp=70000,min_gongji=3000,gongji=4000,skill_id_list=1201,model_res="actors/npc/6031_prefab##6031002",},
[1007]={id=1007,name="<color=#dcfff6>炎皇城守卫</color>",born_x=63,born_y=175,relive_x=261,relive_y=154,model_res="actors/npc/6032_prefab##6032002",}
},

robert_role_meta_table_map={
[1000]=1001,	-- depth:1
[1004]=1005,	-- depth:1
[1007]=1006,	-- depth:1
},
robert_monster={
[1110]={id=1110,born_y=167,},
[1111]={id=1111,ai_type="",born_y=161,born_say="承让",},
[1112]={id=1112,born_y=152,},
[1113]={id=1113,born_y=146,},
[1114]={id=1114,ai_type="",born_say="身手不错嘛",},
[1115]={id=1115,born_y=131,},
[1116]={id=1116,born_y=167,},
[1117]={id=1117,born_x=218,},
[1118]={id=1118,born_y=152,},
[1119]={id=1119,born_x=218,},
[1120]={id=1120,born_x=218,},
[1121]={id=1121,born_x=218,},
[1122]={id=1122,born_x=32,born_y=143,monster_id=2000,},
[1123]={id=1123,born_x=26,born_y=143,monster_id=2001,},
[1124]={id=1124,born_x=34,born_y=141,monster_id=2002,},
[1125]={id=1125,born_x=28,born_y=140,monster_id=2003,},
[1126]={id=1126,born_x=36,born_y=139,monster_id=2004,},
[1127]={id=1127,born_x=30,born_y=147,monster_id=2005,},
[1128]={id=1128,born_x=38,monster_id=2006,},
[1129]={id=1129,born_x=31,born_y=145,monster_id=2007,},
[1130]={id=1130,born_x=40,born_y=135,monster_id=2008,},
[1131]={id=1131,born_x=33,born_y=132,monster_id=2009,},
[1132]={id=1132,born_x=42,born_y=133,monster_id=2010,},
[1133]={id=1133,born_x=35,born_y=128,monster_id=2011,},
[1134]={id=1134,born_x=43,born_y=134,monster_id=2012,},
[1135]={id=1135,born_x=37,born_y=125,monster_id=2013,},
[1136]={id=1136,born_x=44,born_y=129,monster_id=2014,},
[1137]={id=1137,born_x=39,born_y=121,monster_id=2015,},
[1138]={id=1138,born_x=45,born_y=127,monster_id=2016,},
[1139]={id=1139,born_x=41,born_y=118,monster_id=2017,},
[1140]={id=1140,born_x=46,born_y=125,monster_id=2018,},
[1141]={id=1141,born_x=43,born_y=115,monster_id=2019,},
[1142]={id=1142,born_x=47,born_y=123,monster_id=2020,},
[1143]={id=1143,born_x=45,born_y=112,monster_id=2021,},
[1144]={id=1144,born_x=49,born_y=120,monster_id=2022,},
[1145]={id=1145,born_x=47,born_y=108,monster_id=2023,},
[1146]={id=1146,born_x=50,born_y=118,monster_id=2024,},
[1147]={id=1147,born_x=49,born_y=106,monster_id=2025,},
[1148]={id=1148,born_x=52,born_y=116,monster_id=2026,},
[1149]={id=1149,born_x=51,born_y=103,monster_id=2027,},
[1150]={id=1150,born_x=54,born_y=113,monster_id=2028,},
[1151]={id=1151,born_x=53,born_y=100,monster_id=2029,},
[1152]={id=1152,born_x=56,born_y=111,monster_id=2030,},
[1153]={id=1153,born_x=55,born_y=97,monster_id=2031,},
[1154]={id=1154,born_x=58,born_y=108,monster_id=2032,},
[1155]={id=1155,born_x=56,born_y=95,monster_id=2033,},
[1156]={id=1156,born_x=58,born_y=106,monster_id=2034,},
[1157]={id=1157,born_x=57,born_y=93,monster_id=2035,},
[1158]={id=1158,born_x=60,born_y=103,monster_id=2036,},
[1159]={id=1159,born_x=59,born_y=91,monster_id=2037,},
[1160]={id=1160,born_x=62,born_y=100,monster_id=2038,},
[1161]={id=1161,born_x=60,born_y=92,monster_id=2039,},
[1162]={id=1162,born_x=61,born_y=93,monster_id=2040,},
[1163]={id=1163,born_x=26,born_y=28,monster_id=34510,max_hp=147336,min_gongji=4113,gongji=4113,},
[1164]={id=1164,born_x=63,born_y=163,monster_id=108,max_hp=800000,min_gongji=234,gongji=567,}
},

robert_monster_meta_table_map={
[1110]=1114,	-- depth:1
[1112]=1114,	-- depth:1
[1113]=1111,	-- depth:1
[1115]=1111,	-- depth:1
[1120]=1114,	-- depth:1
[1121]=1115,	-- depth:2
[1119]=1113,	-- depth:2
[1118]=1120,	-- depth:2
[1117]=1111,	-- depth:1
[1116]=1120,	-- depth:2
},
effect={
[305]={id=305,},
[306]={id=306,},
[307]={id=307,bundle_name="effects/prefab/environment/zhucheng/effect_zhuxian_smoke_prefab",asset_name="effect_zhuxian_smoke",is_wangqi_visable=1,pos_x=135,pos_y=225,pos_z=-200,scale_x=1,scale_y=1,scale_z=1,}
},

effect_meta_table_map={
},
husong_guide={

},

husong_guide_meta_table_map={
},
gongchengzhan_guide={

},

gongchengzhan_guide_meta_table_map={
},
be_robed_boss_guide={

},

be_robed_boss_guide_meta_table_map={
},
rob_boss_guide={

},

rob_boss_guide_meta_table_map={
},
shuijing_guide={

},

shuijing_guide_meta_table_map={
},
wing_story_fb={

},

wing_story_fb_meta_table_map={
},
mount_story_fb={

},

mount_story_fb_meta_table_map={
},
xiannv_story_fb={

},

xiannv_story_fb_meta_table_map={
},
normal_scene_story={
{scene_id=103,trigger_param="41##258##30##18",operate_param="-6##0##",},
{scene_id=106,trigger="move_into_area",trigger_param="180##160##80##70",operate_param="cg/a3_cg_guijie_prefab##A3_CG_GuiJie",},
{scene_id=198,trigger="move_into_area",trigger_param="100##200##20##20",operate="set_role_camera",operate_param="23####13",can_repeat=1,},
{scene_id=1000,trigger_param=2010,operate_param="cg/a3_cg_xinshoutiyan_prefab##A3_CG_XinShouTiYan_M2",can_repeat=0,},
{story_id=1,trigger_param=120,operate="create_effect_obj",operate_param=305,},
{trigger="gather_end",trigger_param=1007,operate="del_effect_obj",},
{scene_id=1001,story_id=2,trigger_param=185,operate_param="cg/a3_cg_xinshoucun_prefab##A3_CG_XinShouCun1",},
{story_id=6,trigger="received_task",trigger_param=50,operate_param="cg/a3_cg_xinshoucun_prefab##A3_CG_XinShouCun2",},
{operate="set_role_camera",operate_param="-7####8",can_repeat=1,},
{scene_id=1001,story_id=6,operate="auto_task",operate_param="",},
{scene_id=1001,story_id=7,trigger_param=100,operate_param="cg/a3_cg_xinshoucun_prefab##A3_CG_XinShouCun3",},
{scene_id=1001,story_id=8,trigger_param="193##316##30##30",operate_param="-6##363##10",},
{trigger="move_into_area",trigger_param="95##57##20##20",operate="delay_do",},
{story_id=1,},
{story_id=2,trigger_param=570,operate="break_task",operate_param="",},
{story_id=3,trigger_param=570,operate_param="cg/a3_cg_xinshoutiyan_prefab##A3_CG_XinShouTiYan_M",can_repeat=0,},
{story_id=3,trigger="cg_end",operate="change_scene",operate_param="1003##73##284",},
{story_id=5,trigger_param=530,operate="op_scene_obj_hidden",},
{story_id=6,trigger_param=530,operate_param="cg/a3_cg_guijie_prefab##A3_CG_GuiJie2",preload=1,},
{story_id=7,trigger_param=570,operate="op_scene_obj_show",operate_param=0,},
{story_id=8,trigger="received_task",trigger_param=510,operate="delay_do",},
{story_id=9,trigger_param=500,operate="delay_do",},
{scene_id=1003,trigger="close_loading_view",trigger_param="1003##role_level##9998",operate_param="cg/a3_cg_zhucheng_prefab##a3_cg_zhucheng",preload=1,can_repeat=0,},
{scene_id=1003,trigger="cg_end",operate="delay_do",operate_param="0.1##auto_task",can_repeat=0,},
{scene_id=1003,story_id=2,trigger_param=970,operate_param="cg/a3_cg_zhucheng_prefab##a3_cg_zhucheng2",},
{scene_id=1003,story_id=2,trigger="move_into_area",trigger_param="97##92##25##25",operate="delay_do",operate_param="1##auto_task",preload=1,can_repeat=0,},
{story_id=3,trigger="received_task",trigger_param=860,operate_param="4##auto_task",},
{scene_id=1003,story_id=4,trigger_param="140##100##50##50",operate_param="-40####7.2",},
{trigger="received_task",trigger_param=831,operate="create_effect_obj",},
{story_id=5,trigger_param=910,operate="del_effect_obj",operate_param=307,}
},

normal_scene_story_meta_table_map={
[14]=21,	-- depth:1
[20]=21,	-- depth:1
[18]=20,	-- depth:2
[16]=19,	-- depth:1
[30]=25,	-- depth:1
[10]=17,	-- depth:1
[8]=11,	-- depth:1
[5]=11,	-- depth:1
[2]=4,	-- depth:1
[9]=10,	-- depth:2
[7]=16,	-- depth:2
[6]=5,	-- depth:2
[29]=30,	-- depth:2
[1]=3,	-- depth:1
[12]=3,	-- depth:1
[27]=24,	-- depth:1
[28]=3,	-- depth:1
},
taoyuan_cg={

},

taoyuan_cg_meta_table_map={
},
zhucheng_fb={

},

zhucheng_fb_meta_table_map={
},
robots_fight={
{},
{id=1111,target_id=1110,},
{id=1112,target_id=1113,},
{id=1113,target_id=1112,},
{id=1114,target_id=1115,},
{id=1115,target_id=1114,},
{id=1116,target_id=1117,},
{id=1117,target_id=1116,},
{id=1118,target_id=1119,},
{id=1119,target_id=1118,},
{id=1120,target_id=1121,},
{id=1121,target_id=1120,}
},

robots_fight_meta_table_map={
},
yuanguxiandian_story_fb={
{trigger="enter_scene",},
{operate="do_next_wave",operate_param=0,},
{operate="fight_start",operate_param="1122##1123##1124##1125##1126##1127##1128##1129##1130##1131",},
{trigger_param=1,operate_param="1##create_roberts##1132##1133##1134##1135##1136##1137##1138##1139##1140##1141",},
{operate="do_next_wave",operate_param=1,},
{operate="fight_start",operate_param="1132##1133##1134##1135##1136##1137##1138##1139##1140##1141",},
{trigger_param=2,operate_param="1##create_roberts##1142##1143##1144##1145##1146##1147##1148##1149##1150##1151",},
{operate="do_next_wave",operate_param=2,},
{operate="fight_start",operate_param="1142##1143##1144##1145##1146##1147##1148##1149##1150##1151",},
{trigger_param=3,operate_param="1##create_roberts##1152##1153##1154##1155##1156##1157##1158##1159##1160##1161",},
{operate="do_next_wave",operate_param=3,},
{operate="fight_start",operate_param="1152##1153##1154##1155##1156##1157##1158##1159##1160##1161",},
{trigger_param=4,operate_param="1##create_roberts##1162##1163",},
{operate="do_next_wave",operate_param=4,},
{operate="fight_start",operate_param="1162##1163",},
{operate="do_next_wave",operate_param=5,},
{operate="s_drop",operate_param="",},
{trigger_param=5,operate_param="4##exit_fb",}
},

yuanguxiandian_story_fb_meta_table_map={
[6]=4,	-- depth:1
[8]=7,	-- depth:1
[17]=18,	-- depth:1
[3]=1,	-- depth:1
[11]=10,	-- depth:1
[12]=10,	-- depth:1
[2]=3,	-- depth:2
[14]=13,	-- depth:1
[15]=13,	-- depth:1
[16]=18,	-- depth:1
[5]=4,	-- depth:1
[9]=7,	-- depth:1
},
tianshen_fb_story={
{},
{operate="fight_start",operate_param=1163,},
{trigger="monster_hp_change",trigger_param="1163##0.8",operate="fight_pause",operate_param="",},
{operate="dialog_start",operate_param=104,},
{trigger="dialog_end",operate="icon_fly",operate_param="main_view##Skill9",},
{trigger="icon_fly_end",operate="guide_start",operate_param=5,},
{trigger="guide_end",operate_param="1##fight_continue",},
{trigger="fight_end",trigger_param=1,operate_param="4##exit_fb",}
},

tianshen_fb_story_meta_table_map={
[4]=3,	-- depth:1
},
world_boss_story={
{trigger="enter_scene",operate_param="1##create_roberts##1004##1005##1006##1007##1164",},
{operate="fight_start",operate_param=1164,},
{trigger="monster_hp_change",trigger_param="1164##0.8",operate="fight_pause",},
{operate="dialog_start",operate_param=109,},
{operate="error_remind",operate_param="<color=#72eba0>灵儿</color>加入队伍",},
{operate_param="<color=#72eba0>江临宣</color>加入队伍",},
{operate="fight_continue",},
{operate="team_add",operate_param="1004##1005",},
{trigger="fight_end",trigger_param=1,operate="team_leave",},
{trigger="fight_end",trigger_param=1,operate_param="4##exit_fb",}
},

world_boss_story_meta_table_map={
[6]=5,	-- depth:1
[2]=1,	-- depth:1
[4]=3,	-- depth:1
},
game_interlude={
{},
{interlude_id=2,interlude_title="神灵之力",interlude_txt="七六五四三二一七六五四三二一七六五四三二一七六五四三二一七六五四三二一七六五四三二一",}
},

game_interlude_meta_table_map={
},
robert_role_default_table={id=0,name="主角",ai_type="active_attack",side=0,born_x=443,born_y=103,relive_x=0,relive_y=0,relive_cd=5,sex=0,prof=1,move_speed=1200,angle=0,wuqi_id=1,mount_imageid=1,wing_imageid=1,max_hp=300000,min_gongji=88888,gongji=100000,skill_id_list="1##1001##1002##1003##1004",skill_cd=1,atk_range=10,aoe_range=8,born_say="",model_res="",},

robert_monster_default_table={id=1110,ai_type="active_attack",side=1,born_x=261,born_y=137,relive_x=0,relive_y=0,relive_cd=0,move_speed=600,angle=0,action="",monster_id=11878,max_hp=1000000,min_gongji=8888,gongji=8888,skill_id_list=20000,skill_cd=1.1,atk_range=5,aoe_range=0,born_say="",},

effect_default_table={id=305,bundle_name="effects/prefab/environment/xinshoucun/effect_fengyin_prefab",asset_name="Effect_fengyin",is_wangqi_visable=0,pos_x=297,pos_y=217.5,pos_z=714.5,scale_x=2.6,scale_y=2.6,scale_z=2.6,rotate_x=0,rotate_y=180,rotate_z=0,},

husong_guide_default_table={},

gongchengzhan_guide_default_table={},

be_robed_boss_guide_default_table={},

rob_boss_guide_default_table={},

shuijing_guide_default_table={},

wing_story_fb_default_table={},

mount_story_fb_default_table={},

xiannv_story_fb_default_table={},

normal_scene_story_default_table={scene_id=1002,story_id=0,trigger="task_can_commit",trigger_param="",operate="cg_start",operate_param="2##auto_task",preload="",can_repeat="",},

taoyuan_cg_default_table={},

zhucheng_fb_default_table={},

robots_fight_default_table={scene=101,charter_type=0,id=1110,target_type=0,target_id=1111,},

yuanguxiandian_story_fb_default_table={trigger="fight_end",trigger_param="",operate="delay_do",operate_param="4##create_roberts##1002##1003##1122##1123##1124##1125##1126##1127##1128##1129##1130##1131",desc="",},

tianshen_fb_story_default_table={trigger="enter_scene",trigger_param="",operate="delay_do",operate_param="4##create_roberts##1163",desc="",},

world_boss_story_default_table={trigger="dialog_end",trigger_param="",operate="delay_do",operate_param="",desc="",},

game_interlude_default_table={interlude_id=1,interlude_title="终焉宙战",interlude_txt="你用尽了全部神力，终于阻止了魔界尊主。可是不甘心的魔界尊主，在即将倒下的时候，决定爆发所有灵力与你同归于尽... ...",interlude_time=5,}

}

