--灵核概率展示面板
RebateFireWorksProbabilityView = RebateFireWorksProbabilityView or BaseClass(SafeBaseView)

function RebateFireWorksProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(650, 400)})
    self:AddViewResource(0, "uis/view/rebate_gift_ui_prefab", "rebate_fire_works_probability")
    self:SetMaskBg(true, true)
end

function RebateFireWorksProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function RebateFireWorksProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.RebateGiftAct.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(FireworksProItemRender, self.node_list.ph_pro_list) 
    end
end

function RebateFireWorksProbabilityView:OnFlush()
    local info = RebateGiftActivityWGData.Instance:GetFireWorksProbabilityInfo()
    self.probability_list:SetDataList(info)
end

----------------------------------------------------------------------------------
FireworksProItemRender = FireworksProItemRender or BaseClass(BaseRender)
function FireworksProItemRender:__delete()
    
end

function FireworksProItemRender:LoadCallBack()
    
end

function FireworksProItemRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
