ActivityWGCtrl = ActivityWGCtrl or BaseClass(BaseWGCtrl)
-- 日常活动协议处理

function ActivityWGCtrl:RegisterDailyActProtocols()
	-- 妖兽广场
	--self:RegisterProtocol(SCYaoShouGuangChangState, "OnYaoShouGuangChangState")
	--self:RegisterProtocol(SCYaoShouGuangChangFBInfo, "OnYaoShouGuangChangFBInfo")
	--self:RegisterProtocol(SCNotifyYaoShouGuangChangReward, "OnNotifyYaoShouGuangChangReward")

	--self:RegisterProtocol(CSGetYaoShouGuangChangState)
	--self:RegisterProtocol(CSGetYaoShouGuangChangReward)
	--self:RegisterProtocol(CSEnterYaoShouGuangChang)
	-- 锁妖塔
	--self:RegisterProtocol(SCSuoYaoTaState, "OnSuoYaoTaState")
	--self:RegisterProtocol(SCSuoYaoTaFBInfo, "OnSuoYaoTaFBInfo")
	--self:RegisterProtocol(SCNotifySuoYaoTaReward, "OnNotifySuoYaoTaReward")

	--self:RegisterProtocol(CSGetSuoYaoTaState)
	--self:RegisterProtocol(CSGetSuoYaoTaReward)
	--self:RegisterProtocol(CSEnterSuoYaoTa)
	--诛邪
	self:RegisterProtocol(CSZhuxieOperate)

	-- self:RegisterProtocol(SCZhuXieUserInfo, "OnZhuXieUserInfo")
	self:RegisterProtocol(SCZhuxieSceneInfo, "OnZhuxieSceneInfo")
	self:RegisterProtocol(SCZhuxieBossHp,"OnSCZhuxieBossHp")

	self:RegisterProtocol(SCTuMoTaskInfo, "OnTuMoTaskInfo")
	self:RegisterProtocol(SCPaohuanTaskInfo, "OnPaohuanTaskInfo")
	self:RegisterProtocol(SCPaoHuanRollPool, "OnPaoHuanRollPool")
	self:RegisterProtocol(SCPaoHuanRollInfo, "OnPaoHuanRollInfo")

	self:RegisterProtocol(CSGetTuMoTaskInfo)
	self:RegisterProtocol(CSTumoCommitTask)
	-- self:RegisterProtocol(CSTumoFetchCompleteAllReward)
	self:RegisterProtocol(CSTumoResetStar)
	self:RegisterProtocol(CSGetPaoHuanTaskInfo)
	self:RegisterProtocol(CSPaoHuanRollReq)
	self:RegisterProtocol(CSPaoHuanSkipTask)


	--钟馗捉鬼
	self:RegisterProtocol(SCZhuaGuiRoleInfo, "OnZhuaGuiRoleInfo")
	self:RegisterProtocol(SCZhuaGuiFbInfo, "OnZhuaGuiFbInfo")
	--跨服诛邪
	self:RegisterProtocol(SCCrossZhuxieSceneInfo, "OnSCCrossZhuxieSceneInfo")
	self:RegisterProtocol(SCCrossZhuxieBossHp, "OnSCCrossZhuxieBossHp")
	self:RegisterProtocol(CSCrossZhuxieOperate)

	-- 副本中击杀玩家数据改变1362
	self:RegisterProtocol(SCFBKillRoleInfo, "OnSCFBKillRoleInfo")


	--GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DaycountChange, self))
end

function ActivityWGCtrl:DaycountChange(day_counter_id)
	if DAY_COUNT.DAYCOUNT_ID_JOIN_SUOYAOTA == day_counter_id or
		DAY_COUNT.DAYCOUNT_ID_JOIN_YAOSHOUGUANGCHANG == day_counter_id then
		self.act_desc_view:Flush()
	elseif DAY_COUNT.DAYCOUNT_ID_SHUIJING_GATHER == day_counter_id then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.ShuiJing then
			FuBenWGCtrl.Instance:UpdataTaskFollow()
		end
	end
end

-- 请求妖兽广场状态
function ActivityWGCtrl:SendGetYaoShouGuangChangState()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetYaoShouGuangChangState)
	protocol:EncodeAndSend()
end

-- 请求妖兽广场奖励
function ActivityWGCtrl:SendGetYaoShouGuangChangReward(times)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetYaoShouGuangChangReward)
	protocol.times = times
	protocol:EncodeAndSend()
end

-- 请求进入妖兽广场
function ActivityWGCtrl:SendEnterYaoShouGuangChang(is_buy_times, min_level)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEnterYaoShouGuangChang)
	protocol.is_buy_times = is_buy_times
	protocol.min_level = min_level
	protocol:EncodeAndSend()
end

-- 下发妖兽广场状态
function ActivityWGCtrl:OnYaoShouGuangChangState(protocol)
	self.data:SetDailyActStatus(Day_Act_Type.YaoShouPlaza, protocol)
	if protocol.status == Daily_Fb_Status.Standby then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YAOSHOUPLAZA, ACTIVITY_STATUS.STANDY, protocol.next_status_time)
	elseif protocol.status == Daily_Fb_Status.Start then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YAOSHOUPLAZA, ACTIVITY_STATUS.CLOSE)
		FuBenWGCtrl.Instance:UpdataTaskFollow()
	end
	local open_time
	if protocol.status ~= Daily_Fb_Status.Standby then
		open_time = protocol.next_standby_time or 0
	else
		open_time = protocol.next_time or 0
	end
	-- self.ui_icon = MainuiWGCtrl.Instance:GetUiIcon(MainuiIconName.YaoShouPlaza)
	if open_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
		if CountDownManager.Instance:HasCountDown("yaoshou_fb_countdown") then
			CountDownManager.Instance:RemoveCountDown("yaoshou_fb_countdown")
		end
		self:UpdateCallBack(0, open_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown("yaoshou_fb_countdown", BindTool.Bind1(self.UpdateCallBack, self), BindTool.Bind1(self.CompleteCallBack, self), open_time, nil, 1)
	else
		self:CompleteCallBack()
	end
	-- YaoShouPlazaWGCtrl.Instance:Flush()
end


function ActivityWGCtrl:UpdateCallBack(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self:UiIconBtnEffect(false)
		if self.ui_icon then
			self.ui_icon:SetBottomContent(TimeUtil.FormatSecond2MS(total_time - elapse_time))
		end
	end
end

function ActivityWGCtrl:CompleteCallBack()
	if CountDownManager.Instance:HasCountDown("yaoshou_fb_countdown") then
		CountDownManager.Instance:RemoveCountDown("yaoshou_fb_countdown")
	end
	if self.ui_icon then
		self.ui_icon:RemoveCountDown()
		self.ui_icon:SetBottomContent("")
	end
	-- local enter_count = ActivityWGData.Instance:GetEnterCount(Day_Act_Type.YaoShouPlaza)
	-- local limit_count = 0
	-- limit_count = ConfigManager.Instance:GetAutoConfig("yaoshouguangchang_auto").other_cfg[1].free_join_times
	-- local buy_count = math.max(enter_count - limit_count, 0)
	-- local has_count = math.max(limit_count + buy_count - enter_count, 0)
	-- if has_count > 0 then
	-- 	self:UiIconBtnEffect(true)
	-- else
	-- 	self:UiIconBtnEffect(false)
	-- end
end

-- function ActivityWGCtrl:UiIconBtnEffect(visible)

-- end

-- 下发妖兽广场副本信息
function ActivityWGCtrl:OnYaoShouGuangChangFBInfo(protocol)
	self.data:SetYaoShouPlazaInfo(protocol)
	FuBenWGCtrl.Instance:UpdataTaskFollow()
end

-- 下发妖兽广场奖励
function ActivityWGCtrl:OnNotifyYaoShouGuangChangReward(protocol)
	local data = ActivityWGData.GetEndViewData()
	data.type = Day_Act_Type.YaoShouPlaza
	data.score = protocol.score
	data.exp = protocol.exp
	data.bind_coin = protocol.bind_coin
	if data.score ~= 0 and data.exp ~= 0 and data.bind_coin ~= 0 then
		FuBenWGCtrl.Instance:OpenFuBenEndView(data)
		if Scene.Instance:GetSceneType() == SceneType.YaoShouPlaza then
			local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 10
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
		end
	else
		FuBenWGCtrl.Instance:CloseFuBenEndView()
	end
end

-- 请求锁妖塔状态
function ActivityWGCtrl:SendGetSuoYaoTaState()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetSuoYaoTaState)
	protocol:EncodeAndSend()
end

-- 请求锁妖塔奖励
function ActivityWGCtrl:SendGetSuoYaoTaReward(times)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetSuoYaoTaReward)
	protocol.times = times
	protocol:EncodeAndSend()
end

-- 请求进入锁妖塔
function ActivityWGCtrl:SendEnterSuoYaoTa(is_buy_times)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEnterSuoYaoTa)
	protocol.is_buy_times = is_buy_times
	protocol:EncodeAndSend()
end

-- 下发锁妖塔状态
function ActivityWGCtrl:OnSuoYaoTaState(protocol)
	self.data:SetDailyActStatus(Day_Act_Type.SuoYaoTa, protocol)
	-- self.view:Flush(ActivityViewIndex.Daily)
	-- self.act_desc_view:Flush()
	-- YaoShouPlazaWGCtrl.Instance:Flush()
	if protocol.status == Daily_Fb_Status.Standby then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SUOYAOTA, ACTIVITY_STATUS.STANDY, protocol.next_status_time)
	elseif protocol.status == Daily_Fb_Status.Start then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SUOYAOTA, ACTIVITY_STATUS.CLOSE)
		FuBenWGCtrl.Instance:UpdataTaskFollow()
	end
end

-- 下发锁妖塔副本信息
function ActivityWGCtrl:OnSuoYaoTaFBInfo(protocol)
	local old_task_info = self.data:GetCurSYTTaskInfo()
	self.data:SetSuoYaoTaInfo(protocol)
	FuBenWGCtrl.Instance:UpdataTaskFollow()
	local cur_task_info = self.data:GetCurSYTTaskInfo()
	if nil == old_task_info or old_task_info.task_index ~= cur_task_info.task_index then
		self:CheckBlockZone()
	end
end

-- 下发锁妖塔奖励
function ActivityWGCtrl:OnNotifySuoYaoTaReward(protocol)
	local data = ActivityWGData.GetEndViewData()
	data.type = Day_Act_Type.SuoYaoTa
	data.score = protocol.score
	data.exp = protocol.exp
	data.bind_coin = protocol.bind_coin
	if data.score ~= 0 and data.exp ~= 0 and data.bind_coin ~= 0 then
		FuBenWGCtrl.Instance:OpenFuBenEndView(data)
		if Scene.Instance:GetSceneType() == SceneType.SuoYaoTa then
			local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 10
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
		end
	else
		FuBenWGCtrl.Instance:CloseFuBenEndView()
	end
end

-- 锁妖塔锁定区域检测
function ActivityWGCtrl:CheckBlockZone()

end


-- 诛邪信息
function ActivityWGCtrl:OnZhuxieSceneInfo(protocol)
	-- print_error("诛邪信息====================================",protocol)
	self.data:SetZhuXieInfo(protocol)
	if ActivityWGCtrl.Instance.zhuxie_task_view:IsOpen() then
		self:UpdataZhuXieFollow()
	end
	if protocol.finish_task_id > 0 then
		local data = self.data:GetZhuXieTaskInfoByid(protocol.finish_task_id)
		if not data then return end
		self:OpenZhuXieTaskFinishView(data)
	end
end

function ActivityWGCtrl:OnSCZhuxieBossHp(protocol)
	self.data:SetZhuXieBossHp(protocol)
	if ActivityWGCtrl.Instance.zhuxie_task_view:IsOpen() then
		self:UpdateZhuXieBossHp()
	end

	local scene_logic = Scene.Instance:GetSceneLogic()

	if protocol.is_boss_die > 0 then
		ActivityWGCtrl.Instance:OpenZhuXieHurtRankView()
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			main_role:StopMove()
		end
		GuajiWGCtrl.Instance:StopGuaji()

		if scene_logic then
			scene_logic:SetBossDieflag(true)
		end

		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end
--刷新诛邪boos血量
function ActivityWGCtrl:UpdateZhuXieBossHp()
	if ActivityWGCtrl.Instance.zhuxie_task_view:IsOpen() then
		self.zhuxie_task_view:FlushBossInfo()
	end

end

--诛邪提交任务
function ActivityWGCtrl:SendZhuXieFetchTaskReward(task_id)
	self:CSZhuxieOperate(ZHUXIE_OPERA_TYPE.TASK_REWARD, task_id)
end


--诛邪提交任务
function ActivityWGCtrl:CSZhuxieOperate(operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSZhuxieOperate)
	protocol.operate = operate
	protocol.param1 = param1
	protocol:EncodeAndSend()
end

function ActivityWGCtrl:CheckZhuXieActiIsOpen()
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ZHUXIE)
end


--屠魔任务信息
function ActivityWGCtrl:OnTuMoTaskInfo(protocol)
	-- print_error('屠魔任务信息-------------------------',protocol)
	-- self:ShowRewardTip(protocol)
	DailyWGData.Instance:SetTaskTuMoData(protocol)
	GlobalEventSystem:Fire(OtherEventType.TASK_INFO_CHANGE,protocol.task_id)
	-- self.view:Flush({ActivityViewIndex.DailyTask})
	RemindManager.Instance:Fire(RemindName.Daily)
end

function ActivityWGCtrl:ShowRewardTip(protocol)
	-- body
	-- if 0 ~= protocol.is_accept then return end

	local old_commint_times = DailyWGData.Instance:GetTaskCommintTimes()
	local commint_times = protocol.commit_times
	if 0 == old_commint_times or 21 == old_commint_times then return end
	local reward_list = {}
	if 10 >= old_commint_times and commint_times >= 11 and commint_times <21 then
		table.insert(reward_list, 10)
	elseif old_commint_times < 11 and commint_times >= 21 then
		table.insert(reward_list, 10)
		table.insert(reward_list, 20)
	elseif 20 >= old_commint_times and commint_times == 21 then
		table.insert(reward_list, 20)
	end
	if IsEmptyTable(reward_list) then return end
	TaskWGData.Instance:ShowDailyTaskList(reward_list)
	if Scene.Instance:GetSceneType() == SceneType.Common then
        TaskWGData.Instance:ShowTaskTypeTips(false)
        ViewManager.Instance:Open(GuideModuleName.TaskDailyRewardTip)
    else
        TaskWGData.Instance:ShowTaskTypeTips(GameEnum.TASK_TYPE_RI)
    end
end

--请求屠魔信息
function ActivityWGCtrl:SendGetTuMoTaskInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetTuMoTaskInfo)
	protocol:EncodeAndSend()
end

--请求跑环信息
function ActivityWGCtrl:SendGetPaoHuanTaskInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetPaoHuanTaskInfo)
	protocol:EncodeAndSend()
end

--请求跑环奖励信息
function ActivityWGCtrl:SendPaoHuanRollReq(client_click_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPaoHuanRollReq)
	protocol.client_click_index = client_click_index
	protocol:EncodeAndSend()
end

--请求跑环信息
function ActivityWGCtrl:SendPaoHuanSkipTask(skip_all, task_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPaoHuanSkipTask)
	protocol.skip_all = skip_all
	protocol.task_id = task_id or 0
	protocol:EncodeAndSend()
end

--请求屠魔任务 提交任务 commit_all task_id -- 1 x 一键完成 0 x 双倍奖励
function ActivityWGCtrl:SendTumoCommitTask(commit_all, task_id, is_force_max_star, commit_times)
	-- print_error('SendTumoCommitTask-------------------',commit_all, task_id, is_force_max_star, commit_times)
	commit_times = commit_times or 1
	local protocol = ProtocolPool.Instance:GetProtocol(CSTumoCommitTask)
	protocol.commit_all = commit_all
	protocol.task_id = task_id
	protocol.is_force_max_star = is_force_max_star
	protocol.commit_times = commit_times
	protocol:EncodeAndSend()
end
--一键完成所有屠魔任务请求
-- function ActivityWGCtrl:SendTumoFetchCompleteAllReward()
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSTumoFetchCompleteAllReward)
-- 	protocol:EncodeAndSend()
-- end

--跑环任务信息
function ActivityWGCtrl:OnPaohuanTaskInfo(protocol)
	DailyWGData.Instance:SetTaskPaohuanData(protocol)
	-- self.view:Flush()
end

--跑环翻牌摇奖信息
function ActivityWGCtrl:OnPaoHuanRollPool(protocol)
	DailyWGData.Instance:SetPaohuanPoolList(protocol.roll_item_list)
end

--跑环翻牌奖池
function ActivityWGCtrl:OnPaoHuanRollInfo(protocol)
	self.ph_reward_view:SetFinishData(protocol.data)
end

--刷新星星
function ActivityWGCtrl:SendTumoResetStar()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTumoResetStar)
	protocol:EncodeAndSend()
end

-- 抓鬼活力和次数
function ActivityWGCtrl:OnZhuaGuiRoleInfo(protocol)
	self.data:SetCurDayZhuaGuiInfo(protocol)
	-- self.view:Flush({ActivityViewIndex.GuiTask})
end

-- 抓鬼副本信息
function ActivityWGCtrl:OnZhuaGuiFbInfo(protocol)
	self.data:SetZhuaGuiFBInfo(protocol)
	if SceneType.ZhongKui == Scene.Instance:GetSceneType() then
		FuBenWGCtrl.Instance:UpdataTaskFollow()
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:OnZhongKuiInfoHandle()
		end
	end
	if protocol.reason == 2 then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.is_pass = 1
		pass_vo.tip1 = nil
		pass_vo.tip2 =Language.FuBen.GetHuoLi .. HtmlTool.GetHtml(ActivityWGData.Instance:GetCurFBSelfHuoLi(), COLOR3B.GREEN , 24)
		FuBenWGCtrl.Instance:OpenFuBenStarView(pass_vo)
	end
end

-----------------------------------------------------------跨服诛邪战场------------------------------------------------------------------
-- SCCrossZhuxieSceneInfo
-- SCCrossZhuxieBossHp
-- CSCrossZhuxieOperate

function ActivityWGCtrl:OnSCCrossZhuxieSceneInfo(protocol)
	self.data:SetKuafuZhuXieProtocol(protocol)
	if self.kf_zhuxie_follow_view:IsOpen() then
		self.kf_zhuxie_follow_view:FlushKFZhuXieFollowView()
	end

	if protocol.finish_task_id > 0 then
		local data = self.data:GetKFZhuXieInfoByid(protocol.finish_task_id)
		if not data then return end
		GuajiWGCtrl.Instance:ClearGuajiCache()
		self:OpenZhuXieTaskFinishView(data)
	end
end

function ActivityWGCtrl:OnSCCrossZhuxieBossHp(protocol)
	self.data:SetKuafuZhuXieBossProtocol(protocol)
	if self.kf_zhuxie_follow_view:IsOpen() then
		self.kf_zhuxie_follow_view:FlushBossInfo()
	end

	if protocol.is_boss_die_notice > 0 then
		ActivityWGCtrl.Instance:OpenZhuXieHurtRankView()
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			main_role:StopMove()
		end
		
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function ActivityWGCtrl:SendCSCrossZhuxieOperate(op_type,param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossZhuxieOperate)
	protocol.op_type = op_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function ActivityWGCtrl:CheckKFZhuXieActiIsOpen()
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KFZhuXieZhanChang)
end



------------------------------------------------------副本中击杀玩家数据改变1362-----------------------------------------------------------
function ActivityWGCtrl:OnSCFBKillRoleInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type == SceneType.ZhuXie or scene_type == SceneType.KFZhuXieZhanChang then
		local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
		local title_info = self.data:GetFBMltiKillTitle(protocol.fb_kill_role_times)
	    if role then
	        local title_res = title_info.title_res
	        role:SetAttr("used_title_list", {title_res})

	    end
	end
end