require("game/operation_activity/turntable/oa_turntable_wg_data")
require("game/operation_activity/turntable/oa_turntable_record")

OATurnTableWGCtrl = OATurnTableWGCtrl or BaseClass(BaseWGCtrl)

function OATurnTableWGCtrl:__init()
	if OATurnTableWGCtrl.Instance then
		ErrorLog("[OATurnTableWGCtrl] Attemp to create a singleton twice !")
	end
	OATurnTableWGCtrl.Instance = self
	self:RegisterAllProtocols()

	self.data = OATurnTableWGData.New()
	self.record_view = OATurnTableRecord.New(GuideModuleName.OATurnTableRecord)

	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_rabbitgirl_treasure_auto", BindTool.Bind(self.UpdateConfig, self))
end

function OATurnTableWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.record_view:DeleteMe()
	self.record_view = nil

	OATurnTableWGCtrl.Instance = nil
end

function OATurnTableWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCRATunvlangLayerInfo,'OnSCRATunvlangLayerInfo')
    self:RegisterProtocol(SCRATunvlangDrawRecord,'OnSCRATunvlangDrawRecord')
    self:RegisterProtocol(SCRATunvlangDrawResult,'OnSCRATunvlangDrawResult')
end

function OATurnTableWGCtrl:OnSCRATunvlangLayerInfo(protocol)
	-- print_error("layer", protocol)
	local old_num = self.data:GetLayerRewardNumByLayer(protocol.layer)
	self.data:SetLayerInfo(protocol)
	local new_num = self.data:GetLayerRewardNumByLayer(protocol.layer)
	if new_num - old_num ~= 1 then
		ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_turntable, "turntable", {param1 = "normal"})
	end
	--是否reset
	RemindManager.Instance:Fire(RemindName.OATurnTable)

	-- 已刷新提示
	if protocol.reset_sign == 1 and ViewManager.Instance:IsOpenByIndex(GuideModuleName.OperationActivityView, TabIndex.operation_act_turntable) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.Tip_1)
	end

end

function OATurnTableWGCtrl:OnSCRATunvlangDrawRecord(protocol)
	-- print_error("record", protocol)
	self.data:SetRecordInfo(protocol)
	-- ViewManager.Instance:FlushView(GuideModuleName.OATurnTableRecord)
	self:FlushRecordView()
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_turntable, "turntable",{param2 = "record"})
end

function OATurnTableWGCtrl:OnSCRATunvlangDrawResult(protocol)
	-- print_error("result", protocol)
	self.data:SetResultInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_turntable, "turntable", {param3 = "result"})
end

function OATurnTableWGCtrl:SendOpera(opera_type, param_1, param_2, param_3)
	-- print_error(opera_type, param_1, param_2, param_3)
    local t = {}
    t.rand_activity_type = ACTIVITY_TYPE.OPERA_ACT_TURNTABLE
    t.opera_type = opera_type
    t.param_1 = param_1
    t.param_2 = param_2
    t.param_3 = param_3
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function OATurnTableWGCtrl:UpdateConfig()
	self.data:LoadCfg()
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_turntable, "turntable", {param4 = "hot_update"})
end



function OATurnTableWGCtrl:OpenRecordView()
	if self.record_view then
		if self.record_view:IsOpen() then
			self.record_view:Flush()
		else
			self.record_view:Open()
			self.record_view:Flush()
		end
	end
end

function OATurnTableWGCtrl:FlushRecordView()
	if self.record_view then
		if self.record_view:IsOpen() then
			self.record_view:Flush()
		end
	end
end