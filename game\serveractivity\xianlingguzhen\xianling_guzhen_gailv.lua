--概率展示面板
XianLingGuZhenGaiLv = XianLingGuZhenGaiLv or BaseClass(SafeBaseView)

function XianLingGuZhenGaiLv:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_gailv")
    self:SetMaskBg(true, true)
    self.list_view = nil
end

function XianLingGuZhenGaiLv:ReleaseCallBack()
    if self.list_view then
        self.list_view:DeleteMe()
        self.list_view = nil
    end
end

function XianLingGuZhenGaiLv:SetDataAndOpen(info)
	self.info = info
    self:Open()
end

function XianLingGuZhenGaiLv:LoadCallBack()
    local asset_bundle = "uis/view/xianling_guzhen_prefab"
	local asset_name = "xianling_guzhen_gailv_render"
	if self.list_view == nil then
		self.list_view = AsyncBaseGrid.New()
		self.list_view:CreateCells({col = 6,
                        change_cells_num = 1, assetBundle = asset_bundle,
                        assetName = asset_name,
                        list_view = self.node_list["listview"],
				        itemRender = XianLingGuZhenGaiLvRender})
		self.list_view:SetStartZeroIndex(false)
	end
end

function XianLingGuZhenGaiLv:OnFlush()
    if not self.info then
        return
    end

    self.list_view:SetDataList(self.info)
end

----------------------------------------------------------------------------------
XianLingGuZhenGaiLvRender = XianLingGuZhenGaiLvRender or BaseClass(BaseRender)
function XianLingGuZhenGaiLvRender:__delete()
    if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function XianLingGuZhenGaiLvRender:LoadCallBack()
    if self.item_cell == nil then
        self.item_cell = ItemCell.New(self.node_list.item_cell)
    end
end

function XianLingGuZhenGaiLvRender:OnFlush()
    if not self.data then
        return
    end

    local data = {item_id = self.data.item_id}
    self.item_cell:SetData(data)
    self.node_list.probability_text.text.text = self.data.random_count.."%"
end
