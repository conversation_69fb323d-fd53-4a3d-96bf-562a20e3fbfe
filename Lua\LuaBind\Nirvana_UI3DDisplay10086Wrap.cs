﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_UI3DDisplay10086Wrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.UI3DDisplay10086), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SetDragSpeed", SetDragSpeed);
		<PERSON><PERSON>unction("DisplayPerspectiveWithOffset", DisplayPerspectiveWithOffset);
		<PERSON><PERSON>RegFunction("Display", Display);
		<PERSON><PERSON>RegFunction("ClearDisplay", ClearDisplay);
		<PERSON>.RegFunction("SetPosNodeLocalPosition", SetPosNodeLocalPosition);
		<PERSON><PERSON>unction("SetPosNodeRotation", SetPosNodeRotation);
		L.RegFunction("SetPosNodeLocalScale", SetPosNodeLocalScale);
		<PERSON>.RegFunction("OnDrag", OnDrag);
		<PERSON><PERSON>RegFunction("ResetRotation", ResetRotation);
		<PERSON><PERSON>RegFunction("SetDisplayTextureResolution", SetDisplayTextureResolution);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("displayBlurUIBackground", get_displayBlurUIBackground, set_displayBlurUIBackground);
		L.RegVar("uiCamera", get_uiCamera, set_uiCamera);
		L.RegVar("OffsetRoot", get_OffsetRoot, null);
		L.RegVar("DisplayCamera", get_DisplayCamera, set_DisplayCamera);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDragSpeed(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetDragSpeed(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DisplayPerspectiveWithOffset(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				obj.DisplayPerspectiveWithOffset(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				obj.DisplayPerspectiveWithOffset(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.UI3DDisplay10086.DisplayPerspectiveWithOffset");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Display(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				obj.Display(arg0);
				return 0;
			}
			else if (count == 3)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				obj.Display(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.Display(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.Display(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				obj.Display(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else if (count == 7)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				bool arg5 = LuaDLL.luaL_checkboolean(L, 7);
				obj.Display(arg0, arg1, arg2, arg3, arg4, arg5);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.UI3DDisplay10086.Display");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearDisplay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
			obj.ClearDisplay();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPosNodeLocalPosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetPosNodeLocalPosition(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPosNodeRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
			UnityEngine.Quaternion arg0 = ToLua.ToQuaternion(L, 2);
			obj.SetPosNodeRotation(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPosNodeLocalScale(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetPosNodeLocalScale(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
			obj.ResetRotation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDisplayTextureResolution(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				obj.SetDisplayTextureResolution();
				return 0;
			}
			else if (count == 2)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				obj.SetDisplayTextureResolution(arg0);
				return 0;
			}
			else if (count == 3)
			{
				Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay10086));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				obj.SetDisplayTextureResolution(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.UI3DDisplay10086.SetDisplayTextureResolution");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_displayBlurUIBackground(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)o;
			bool ret = obj.displayBlurUIBackground;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index displayBlurUIBackground on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uiCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)o;
			UnityEngine.Camera ret = obj.uiCamera;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uiCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OffsetRoot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)o;
			UnityEngine.Transform ret = obj.OffsetRoot;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OffsetRoot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DisplayCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)o;
			UnityEngine.Camera ret = obj.DisplayCamera;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DisplayCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_displayBlurUIBackground(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.displayBlurUIBackground = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index displayBlurUIBackground on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uiCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)o;
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.uiCamera = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uiCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DisplayCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay10086 obj = (Nirvana.UI3DDisplay10086)o;
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.DisplayCamera = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DisplayCamera on a nil value");
		}
	}
}

