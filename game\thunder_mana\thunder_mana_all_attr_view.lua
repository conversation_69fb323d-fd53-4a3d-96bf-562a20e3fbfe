local ATTR_COUNT = 13
local MAX_DISPLAY_NUM = 11

ThunderManaAllAttrView = ThunderManaAllAttrView or BaseClass(SafeBaseView)

function ThunderManaAllAttrView:__init()
	self:SetMaskBg(true, true)
	self.view_cache_time = 0
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_attr_tip")
end

function ThunderManaAllAttrView:__delete()

end

function ThunderManaAllAttrView:SetShowDataAndOpen(data)
	if data then
        self.show_data = data
		self:Open()
	end
end

function ThunderManaAllAttrView:ReleaseCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}
end

function ThunderManaAllAttrView:LoadCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}

	for i = 1, ATTR_COUNT do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
		if self.attr_list[i] then
			self.attr_name_list[i] = self.attr_list[i]:FindObj("attr_name")
			self.attr_value_list[i] = self.attr_list[i]:FindObj("attr_value")
		end
	end
end


function ThunderManaAllAttrView:OnFlush()
	if not self.show_data then
		return
	end

	self.node_list.attr_title_name.text.text = Language.ThunderMana.ThunderTitleName[3]

	local data_list = self:GetEquipAllAttr()
	local length = #data_list

	local index = 1
	if not IsEmptyTable(data_list) then
		for i = 1, ATTR_COUNT do
			local attr_data = data_list[i]
			if attr_data and self.attr_list[i] then
				---设置属性
				self.attr_name_list[i].text.text = EquipmentWGData.Instance:GetAttrName(attr_data.attr_str, false, false)
				---设置属性
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_data.attr_str)
				local per_desc = is_per and attr_data.attr_value / 100 .. "%" or attr_data.attr_value 
				---基础属性
				if attr_data.attr_value then
					self.attr_value_list[i].text.text = "+" .. per_desc
				end
		   end
			if i <= length then
			   index = index + 1
			end
		end
	end

	if index < MAX_DISPLAY_NUM then
        self.node_list.attr_scroll.scroll_rect.enabled = false
    else
        self.node_list.attr_scroll.scroll_rect.enabled = true
    end

	if not IsEmptyTable(data_list) then
		for i = 1, ATTR_COUNT do
			self.attr_list[i]:SetActive(i <= length)
		end
	end

	self.node_list.img_no_record:CustomSetActive(IsEmptyTable(data_list))
	self.node_list.attr_list:CustomSetActive(not IsEmptyTable(data_list))
end

function ThunderManaAllAttrView:GetEquipAllAttr()
	local all_attr_list = {}
	local thunder_type = self.show_data.view_type
	local thunder_info = ThunderManaWGData.Instance:GetThunderInfoByType(thunder_type)
	if not thunder_info then
		return all_attr_list
	end

	local function add_tab(attr_list)
		if not attr_list then
			return
		end

		for index, attr_cell in ipairs(attr_list) do
			if attr_cell.attr_str and attr_cell.attr_value > 0 then
				if all_attr_list[attr_cell.attr_str] then
					all_attr_list[attr_cell.attr_str].attr_value = all_attr_list[attr_cell.attr_str].attr_value + attr_cell.attr_value
				else
					all_attr_list[attr_cell.attr_str] = {}
					all_attr_list[attr_cell.attr_str].attr_str = attr_cell.attr_str
					all_attr_list[attr_cell.attr_str].attr_value = attr_cell.attr_value
					all_attr_list[attr_cell.attr_str].attr_sort =  attr_cell.attr_sort
				end
			end
		end
	end

	for k, v in pairs(thunder_info.part_item_list) do
		if v.item_id > 0 then
			local level_attr_list = ThunderManaWGData.Instance:GetEquipPartLevelAttrList(v.seq, v.part, v.level)
			add_tab(level_attr_list)

			local star_attr_list = ThunderManaWGData.Instance:GetEquipPartStarAttrList(v.item_id) 
			add_tab(star_attr_list)
		end
	end

	local return_table = {}

	for _, attr_data in pairs(all_attr_list) do
		local data = {}
		data.attr_str = attr_data.attr_str
		data.attr_value = attr_data.attr_value
		data.attr_sort = attr_data.attr_sort
		table.insert(return_table, data)
	end

	if not IsEmptyTable(return_table) then
		table.sort(return_table, SortTools.KeyLowerSorter("attr_sort"))
	end

	return return_table
end