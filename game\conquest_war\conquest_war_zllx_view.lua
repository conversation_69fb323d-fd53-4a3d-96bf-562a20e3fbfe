function ConquestWarView:LoadIndexCallBackZLLXView()
    XUI.AddClickEventListener(self.node_list.btn_zllx_reward_show, BindTool.Bind1(self.OnClickZLLXRewardShow, self))
	XUI.AddClickEventListener(self.node_list.btn_zllx_skill_show, BindTool.Bind(self.OnClickZLLXSkillShow, self))
    XUI.AddClickEventListener(self.node_list.go_zllx_btn, BindTool.Bind(self.OnClickGoZLLXBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_zllx_boss_quality, BindTool.Bind(self.OnClickZLLXBossQuality, self))
	XUI.AddClickEventListener(self.node_list.btn_zllx_boss_privilege, BindTool.Bind(self.OnClickZLLXBossPrivilege, self))
	
    if not self.zllx_reward_list then
		self.zllx_reward_list = AsyncListView.New(ItemCell, self.node_list.zllx_reward_list)
		self.zllx_reward_list:SetStartZeroIndex(true)
	end
    
    self.zllx_activity_change_callback = BindTool.Bind(self.OnZLLXActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.zllx_activity_change_callback)
end

function ConquestWarView:ReleaseZLLXView()
    if self.zllx_reward_list then
		self.zllx_reward_list:DeleteMe()
		self.zllx_reward_list = nil
	end

	if self.zllx_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.zllx_activity_change_callback)
		self.zllx_activity_change_callback = nil
	end
end

function ConquestWarView:ZLLXShowIndexCallBack()
    
end

function ConquestWarView:OnFlushZLLXView(param_t, index)
    local data = ConquestWarWGData.Instance:GetConquestWarActivityInfoById(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)
	if IsEmptyTable(data) then
		return
	end

    self.zllx_reward_list:SetDataList(data.activity_item)
    self.node_list["txt_zllx_title"].text.text = data.activity_title
	self.node_list["txt_zllx_open_time"].text.text = string.format(Language.FlagGrabbingBattlefield.ActivityTime1, data.time_1, data.time_2)
	self.node_list["txt_zllx_desc"].text.text = data.activity_illustrate

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)
	if not activity_info then return end

	self:FlushZLLXBtnState(activity_info.status)
end

function ConquestWarView:OnClickZLLXRewardShow()
    BOSSInvasionWGCtrl.Instance:OpenRankRewardView()
end

function ConquestWarView:OnClickZLLXSkillShow()
    BOSSInvasionWGCtrl.Instance:OpenPrivilegeSkillShowView()
	-- BOSSInvasionWGCtrl.Instance:OpenQualityView()
end

function ConquestWarView:OnClickGoZLLXBtn()
    local info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)
	if not info then return end

	if info.status ~= ACTIVITY_STATUS.OPEN and info.status ~= ACTIVITY_STATUS.STANDY then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.NotOpenAct))
		return
	end

	local status = BOSSInvasionWGData.Instance:GetCurActStatus()
	if status == CROSS_BOSS_STRIKE_STATUS.STATUS_END then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.ActivityIsEnd))
		return
	end

	CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)
end

function ConquestWarView:OnZLLXActChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION and self:IsOpen() then
		self:FlushZLLXBtnState(status)
	end
end

function ConquestWarView:FlushZLLXBtnState(status)
	if status == ACTIVITY_STATUS.STANDY then
		self.node_list["go_zllx_btn"]:CustomSetActive(true)
		self.node_list["go_zllx_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Standy
	elseif status == ACTIVITY_STATUS.OPEN then
		self.node_list["go_zllx_btn"]:CustomSetActive(true)
		self.node_list["go_zllx_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Doing
	else
		self.node_list["go_zllx_btn"]:CustomSetActive(false)
		self.node_list["go_zllx_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.NotOpenDesc
	end
end

function ConquestWarView:OnClickZLLXBossQuality()
	BOSSInvasionWGCtrl.Instance:OpenQualityView()
end

function ConquestWarView:OnClickZLLXBossPrivilege()
	BOSSInvasionWGCtrl.Instance:OpenPrivilegeView()
end