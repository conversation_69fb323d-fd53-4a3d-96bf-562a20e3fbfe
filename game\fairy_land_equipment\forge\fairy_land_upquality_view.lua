--------锻造 升品--------

function FairyLandEquipmentView:UpqualityLoadIndexCallBack()
	self.upquality_cur_item = ItemCell.New(self.node_list["upquality_cur_item"])
	self.upquality_next_item = ItemCell.New(self.node_list["upquality_next_item"])

	if nil == self.upquality_attr_list then
        self.upquality_attr_list = {}
        local attr_num = self.node_list.upquality_attrs_list.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(self.node_list.upquality_attrs_list:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.upquality_attr_list[i] = cell
        end
    end

	XUI.AddClickEventListener(self.node_list["upquality_up_btn"], BindTool.Bind(self.ClickUpqualityUpBtn, self))

	self.upquality_stuff_list = {}
end

function FairyLandEquipmentView:UpqualityReleaseCallBack()
	if self.upquality_attr_list then
        for k,v in pairs(self.upquality_attr_list) do
            v:DeleteMe()
        end
        self.upquality_attr_list = nil
    end

	if not IsEmptyTable(self.upquality_stuff_list) then
		for k,v in pairs(self.upquality_stuff_list) do
			v:DeleteMe()
		end
		self.upquality_stuff_list = {}
	end

	if self.upquality_cur_item then
		self.upquality_cur_item:DeleteMe()
		self.upquality_cur_item = nil
	end

	if self.upquality_next_item then
		self.upquality_next_item:DeleteMe()
		self.upquality_next_item = nil
	end
end

function FairyLandEquipmentView:UpqualityFlushContent()
	local slot = self:GetCurSelectSlot()
	local part = self.select_equip_part
	if slot < 0 or part < 0 then
		self.node_list["can_upquality_panel"]:SetActive(false)
		self.node_list["not_upquality_panel"]:SetActive(true)
		return
	end

	if not self.upquality_next_item or not self.upquality_cur_item then
		self.upquality_cur_item = ItemCell.New(self.node_list["upquality_cur_item"])
		self.upquality_next_item = ItemCell.New(self.node_list["upquality_next_item"])
	end

	local equip_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	if IsEmptyTable(equip_info) or equip_info.item_id <= 0 then
		self.upquality_next_item:ClearData()
		self.upquality_next_item:SetItemIcon(ResPath.GetCommonImages("a1_suo_bb2"))
		self.upquality_cur_item:ClearData()
		self.upquality_cur_item:SetItemIcon(ResPath.GetCommonImages("a1_suo_bb2"))
		self.node_list["can_upquality_panel"]:SetActive(false)
		self.node_list["not_upquality_panel"]:SetActive(true)
		return
	end

	self.upquality_cur_item:SetData(equip_info)
	local cur_equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(equip_info.item_id)
	local cur_color = cur_equip_cfg and cur_equip_cfg.color or 1
	local upcolor_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpcolorCfg(slot, cur_color)
	local stuff_id = upcolor_cfg and upcolor_cfg.stuff_id or 0
	local stuff_num = upcolor_cfg and upcolor_cfg.stuff_num or 1
	local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	local next_color = upcolor_cfg and upcolor_cfg.des_color or 0
	local next_equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemMapCfg(slot, part, next_color)
	local next_equip = {
		item_id = next_equip_cfg and next_equip_cfg.id or 0,
		num = 1,
		is_bind = 0,
	}

	local is_max = next_equip.item_id <= 0
	if equip_info.item_id > 0 then
		self.node_list["can_upquality_panel"]:SetActive(true)
		self.node_list["not_upquality_panel"]:SetActive(false)
		
		local item_cfg = ItemWGData.Instance:GetItemConfig(equip_info.item_id)
		if item_cfg then
			-- if is_max then
			-- 	self.node_list["upquality_cur_txt"].text.text = ""
			-- else
				local color_str = string.format(Language.FairyLandEquipment.QualityStr, Language.Common.ColorName[item_cfg.color])
				color_str = ToColorStr(color_str, ITEM_COLOR[item_cfg.color])
				self.node_list["upquality_cur_txt"].text.text = color_str
			--end

			self.node_list["upquality_item_name_text"].text.text = item_cfg.name
		end
	end

	self.node_list["upquality_next"]:SetActive(not is_max)
	if not is_max then
		self.upquality_next_item:SetData(next_equip)
		local item_cfg = ItemWGData.Instance:GetItemConfig(next_equip.item_id)
		if item_cfg then
			local color_str = string.format(Language.FairyLandEquipment.QualityStr, Language.Common.ColorName[item_cfg.color])
			color_str = ToColorStr(color_str, ITEM_COLOR[item_cfg.color])
			self.node_list["upquality_next_txt"].text.text = color_str
		end
	end

	self.node_list["upquality_max_flag"]:SetActive(is_max)
	self.node_list["upquality_up_remind"]:SetActive(has_stuff_num >= stuff_num and not is_max)
	self.node_list["upquality_stuff_list"]:SetActive(not is_max)
	self.node_list["upquality_up_btn"]:SetActive(not is_max)

	-- 属性
	local attr_list = FairyLandEquipmentWGData.Instance:GetUpqualityuSlotEquipAttr(slot, part)
	for k,v in pairs(self.upquality_attr_list) do
        v:SetData(attr_list[k])
    end

	local stuff_index = 1
	local upquality_stuff_data = {{item_id = stuff_id, is_bind = 0, num = stuff_num}}
	for i,v in ipairs(upquality_stuff_data) do
		if not self.upquality_stuff_list[i] then
			self.upquality_stuff_list[i] = ItemCell.New()
			self.upquality_stuff_list[i]:SetInstanceParent(self.node_list["upquality_stuff_list"])
		end

		self.upquality_stuff_list[i]:SetActive(true)
		self.upquality_stuff_list[i]:SetData(v)
		self.upquality_stuff_list[i]:SetRightBottomTextVisible(true)
		local str = ToColorStr(tostring(has_stuff_num) .. "/" .. stuff_num, has_stuff_num >= stuff_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
		self.upquality_stuff_list[i]:SetRightBottomColorText(str)
		stuff_index = stuff_index + 1
	end

	if #self.upquality_stuff_list > stuff_index then
		for i = stuff_index, #self.upquality_stuff_list do
			if self.upquality_stuff_list[i] then
				self.upquality_stuff_list[i]:SetActive(false)
			end
		end
	end
end

function FairyLandEquipmentView:ClickUpqualityUpBtn()
	local slot = self:GetCurSelectSlot()
	if slot < 0 then
		return
	end

	local part = self.select_equip_part
	local equip_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	local equip = equip_info
	if not IsEmptyTable(equip) then
		local cur_equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(equip.item_id)
		local cur_color = cur_equip_cfg and cur_equip_cfg.color or 1
		local upcolor_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpcolorCfg(slot, cur_color)
		local next_color = upcolor_cfg and upcolor_cfg.des_color or 1
		local op_type = GOD_BODY_OP_TYPE.EQUIP_UP_COLOR
		local stuff_id = upcolor_cfg and upcolor_cfg.stuff_id or 0
		local stuff_num = upcolor_cfg and upcolor_cfg.stuff_num or 1
		local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
		if stuff_num > has_stuff_num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.UpqualityNotStuff)
			return
		end
		FairyLandEquipmentWGCtrl.Instance:SendOperateReq(op_type, slot, part, next_color)
	end
end

function FairyLandEquipmentView:ShowUpQuaalitySuccess()
	if self.node_list["upquality_success_effect"] then
		local bundle, asset = ResPath.GetUIEffect("UI_juguang_baokai")
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["upquality_success_effect"].transform, 3)
	end
end


-----------------------------UpqualityAttrItem------------------
UpqualityAttrItem = UpqualityAttrItem or BaseClass(BaseRender)

function UpqualityAttrItem:__init()
	
end

function UpqualityAttrItem:__delete()
	
end

function UpqualityAttrItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list["icon"]:SetActive(self.data.icon ~= nil)
	self.node_list["value"].text.text = self.data.text
end