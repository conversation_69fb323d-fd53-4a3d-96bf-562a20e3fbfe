BaseCellManager = BaseCellManager or BaseClass()

function BaseCellManager:__init()
	if BaseCellManager.Instance then
		print_error("BaseCellManager to create singleton twice")
	end
	BaseCellManager.Instance = self

	self.wait_flush_num = 0
	self.all_cell_list = {}
	Runner.Instance:AddRunObj(self, 16)
end

function BaseCellManager:__delete()
	Runner.Instance:RemoveRunObj(self)
	BaseCellManager.Instance = nil
end

function BaseCellManager:Update(now_time, elapse_time)
	local finish_list
	for k,v in pairs(self.all_cell_list) do
		self.all_cell_list[k] = v - 1
		if v <= 1 then
			Trycall(function ()
				k:Flush()
			end)

			finish_list = finish_list or {}
			table.insert(finish_list, k)
		end
	end

	if finish_list then
		for k,v in pairs(finish_list) do
			self.all_cell_list[v] = nil
			self.wait_flush_num = self.wait_flush_num - 1
			if self.wait_flush_num < 0 then
				self.wait_flush_num = 0
				print_error("[BaseCellManager] Number is less 0")
			end
		end
	end
end

function BaseCellManager:TryFlush(cell)
	if nil ~= self.all_cell_list[cell] then
		return
	end

	local number = self:GetNumber()
	self.all_cell_list[cell] = number
	self.wait_flush_num = self.wait_flush_num + 1
end

function BaseCellManager:StopFlush(cell)
	if nil ~= self.all_cell_list[cell] then
		self.all_cell_list[cell] = nil
		self.wait_flush_num = self.wait_flush_num - 1
		if self.wait_flush_num < 0 then
			self.wait_flush_num = 0
			print_error("[BaseCellManager] Number is less 0")
		end
	end
end

function BaseCellManager:GetNumber()
	return math.floor(self.wait_flush_num / 5)
end