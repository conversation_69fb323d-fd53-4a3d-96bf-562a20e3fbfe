require("game/cangjin_shop/cangjin_shop_wg_data")
require("game/cangjin_shop/cangjin_shop_view")
require("game/cangjin_shop/cangjin_exchange_view")
require("game/cangjin_shop/cangjin_exchange_suit_view")
require("game/cangjin_shop/cangjin_exchange_shop_view")
require("game/cangjin_shop/cangjin_exchange_tequan_view")
require("game/cangjin_shop/cangjin_exchange_attr")
require("game/cangjin_shop/cangjin_exchange_privilege_view")
require("game/cangjin_shop/cangjin_exchange_exchange_view")
require("game/cangjin_shop/cangjin_exchange_shop_preview_view")

CangJinShopWGCtrl = CangJinShopWGCtrl or BaseClass(BaseWGCtrl)

function CangJinShopWGCtrl:__init()
	if CangJinShopWGCtrl.Instance then
		ErrorLog("[CangJinShopWGCtrl] attempt to create singleton twice!")
		return
	end
    CangJinShopWGCtrl.Instance = self

    self.data = CangJinShopWGData.New()
    self.view = CangJinShopView.New(GuideModuleName.CangJinShopView)
    self.exchange_view = CangJinExchangeView.New(GuideModuleName.CangJinExchangeView)
    self.exchange_attr_view = CangJinExchangeAttr.New(GuideModuleName.CangJinExchangeAttr)
    self.exchange_shop_preview_view = CangJinExchangeShopPreview.New(GuideModuleName.CangJinExchangeShopPreview)
    -- self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

    self:RegisterAllProtocols()
end

function CangJinShopWGCtrl:__delete()
	CangJinShopWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.exchange_view then
        self.exchange_view:DeleteMe()
        self.exchange_view = nil
    end

    if self.exchange_attr_view then
        self.exchange_attr_view:DeleteMe()
        self.exchange_attr_view = nil
    end

    if self.exchange_shop_preview_view then
        self.exchange_shop_preview_view:DeleteMe()
        self.exchange_shop_preview_view = nil
    end

    -- if self.open_fun_change then
    --     GlobalEventSystem:UnBind(self.open_fun_change)
    --     self.open_fun_change = nil
    -- end

    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function CangJinShopWGCtrl:RegisterAllProtocols()
	-- self:RegisterProtocol(CSCangJinShangPuClientReq)

	-- self:RegisterProtocol(SCCangJinShangPuAllInfo, "OnSCCangJinShangPuAllInfo")
	-- self:RegisterProtocol(SCCangJinShangPuSuitShopInfo, "OnSCCangJinShangPuSuitShopInfo")
	-- self:RegisterProtocol(SCCangJinShangPuLimitShopInfo, "OnSCCangJinShangPuLimitShopInfo")
    -- self:RegisterProtocol(SCCangJinShangPuTeQuanInfo, "OnSCCangJinShangPuTeQuanInfo")
    -- self:RegisterProtocol(SCCangJinShangPuScoreInfo, "OnSCCangJinShangPuScoreInfo")
    -- self:RegisterProtocol(SSCANGJINSHANGPUConvertInfo, "OnSSCANGJINSHANGPUConvertInfo")
end

-- 请求操作
function CangJinShopWGCtrl:SendCangJinShopRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCangJinShangPuClientReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function CangJinShopWGCtrl:OnSCCangJinShangPuAllInfo(protocol)
	--print_error("=======全部信息======", protocol)
    self.data:SetCangJinShopAllInfo(protocol)

    RemindManager.Instance:Fire(RemindName.CangJinExchangeTeQuan)
    RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_Mount)
    RemindManager.Instance:Fire(RemindName.CangJinExchangeLimitShop)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView)
end

function CangJinShopWGCtrl:OnSCCangJinShangPuSuitShopInfo(protocol)
	--print_error("=======套装商店======", protocol)
    self.data:SuitShopChange(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_suit)
end

function CangJinShopWGCtrl:OnSCCangJinShangPuLimitShopInfo(protocol)
	--print_error("=======限购商店======", protocol)
    self.data:LimitShopInfoChange(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_shop)
end

function CangJinShopWGCtrl:OnSCCangJinShangPuTeQuanInfo(protocol)
	--print_error("=======特权======", protocol)
    self.data:TeQuanInfoChange(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_tequan)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeAttr)
    ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_mount)

    RemindManager.Instance:Fire(RemindName.CangJinExchangeTeQuan)
    RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_Mount)
end

function CangJinShopWGCtrl:OnSCCangJinShangPuScoreInfo(protocol)
	--print_error("=======积分======", protocol)
    local old_score = self.data:GetCurScore()
    local diff = protocol.score - old_score
    if diff > 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.CangJinShopView.GetScore, diff))
    end

    self.data:ScoreInfoChange(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinShopView)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView)
end

function CangJinShopWGCtrl:OnSSCANGJINSHANGPUConvertInfo(protocol)
    --print_error("=======兑换信息======", protocol)
    self.data:SetConvertInfo(protocol)
    RemindManager.Instance:Fire(RemindName.CangJinExchangeLimitShop)
    ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_exchange)
end

-- function CangJinShopWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
--     if check_all or fun_name == FunName.CangJinShopView then 
--         local is_open = FunOpen.Instance:GetFunIsOpened(FunName.CangJinShopView)
--         local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
--         ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.CANGJINSHOP, state)
--     end
-- end

function CangJinShopWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:IsConvertCostItem(change_item_id) then
            RemindManager.Instance:Fire(RemindName.CangJinExchangeLimitShop)
            ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_exchange)
		end
	end
end