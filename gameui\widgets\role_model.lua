----------------------------------------------------
-- 角色模型
----------------------------------------------------
RoleModel = RoleModel or BaseClass()

local MODEL_EFF = {
	-- ["model/npc/"] = "NPC",
	-- ["model/beishi/"] = "JianZhen",
	-- ["model/fabao/"] = "BaoJu",
	-- ["model/chongwu/"] = "Chongwu",
	["model/tianshen/"] = "Tianshen",
	["model/zuoqi/"] = "zuoqi",
	["model/wuhun/"] = "wuhun",
	["model/yushou/"] = "yushou",
	["model/wings/"] = "wings",
}

TmpDisplayPosition = Vector3(0, -2.5, 0)
TmpDisplayRotation = Vector3(0, 180, 0)
local TmpRTCameraDepth = -3

----[[ RT 参数
--- 有新增 需要根据分辨率大小重新排序好，不然影响RT 后处理的品质选择的判断（见 SetDisplayData方法）
ModelRTSCaleType = {
	XS = 1,
	S = 2,
	M = 3,
	L = 4,
	XL = 5,
	DIY = 6,

	-- 等比缩放 Proportional scaling
	PS_XXS = 10,
	PS_S = 11,
	PS_TS = 12,
	PS_XS = 13,
	PS_M = 14,
	PS_L = 15,
}

local ModelRTSCaleTypeData = {
	-- 展示模型大小不变，范围大小区别
	[ModelRTSCaleType.XS] = {rect = Vector2(512, 512), camera_pos_z = -9.6, camera_field = 30, drag_rect = Vector2(512, 512)}, -- 1.0MB => 2.1MB
	[ModelRTSCaleType.S] = {rect = Vector2(600, 600), camera_pos_z = -11.2, camera_field = 30, drag_rect = Vector2(600, 600)}, -- 1.4MB => 2.8MB
	[ModelRTSCaleType.M] = {rect = Vector2(1024, 900), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(600, 900)}, -- 2.1MB => 4.3MB
	[ModelRTSCaleType.L] = {rect = Vector2(1500, 900), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(600, 900)}, -- 4.1MB => 8.5MB
	[ModelRTSCaleType.XL] = {rect = Vector2(2000, 900), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(600, 900)},
	[ModelRTSCaleType.DIY] = {rect = Vector2(2000, 1100), camera_pos_z = -13, camera_field = 30, drag_rect = Vector2(600, 900)},

	-- 基于L的等比缩放（展示内容与L一样多，RT尺寸的区别）
	[ModelRTSCaleType.PS_XXS] = {rect = Vector2(166, 225), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(160, 160)}, -- 0.2倍
	-- [ModelRTSCaleType.PS_S] = {rect = Vector2(300, 225), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(225, 225)}, -- 0.25倍
	[ModelRTSCaleType.PS_S] = {rect = Vector2(700, 450), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(450, 450)}, -- 0.5倍
	[ModelRTSCaleType.PS_M] = {rect = Vector2(1024, 675), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(600, 675)}, -- 0.75倍
	[ModelRTSCaleType.PS_XS] = {rect = Vector2(1334, 300), camera_pos_z = -16.8, camera_field = 30, drag_rect = Vector2(1334, 300)},
	[ModelRTSCaleType.PS_TS] = {rect = Vector2(800, 800), camera_pos_z = -20, camera_field = 30, drag_rect = Vector2(450, 450)},
}

RTDisplayDefaultRect = Vector2(ModelRTSCaleTypeData[ModelRTSCaleType.M].rect.x, ModelRTSCaleTypeData[ModelRTSCaleType.M].rect.y)

RT_MODLE_OFFSET_LIST = {}
--]]


ReflectionProbe = nil
RoleModelCount = 0



local UIObjLayer = GameObject.Find("GameRoot/UIObjLayer").transform
local UI3DModel = typeof(UI3DModel)
local TypeGameObjectAttach = typeof(Game.GameObjectAttach)
local SceneManager = UnityEngine.SceneManagement.SceneManager

function RoleModel:__init(root_node)
	self.draw_obj = DrawObj.New(self, UIObjLayer)
	self.draw_obj.is_rolemodel = true
	self.draw_obj:SetIsInQueueLoad(false)
	self.draw_obj:SetMaterialQuality(GameEnum.MATERIAL_QUALITY_UI)
	self.draw_obj:SetIsUseObjPool(true) 
	self.draw_obj.root.transform.position = Vector3(0, 0, 0)
	self.draw_obj:SetRemoveCallback(BindTool.Bind(self._OnModelRemove, self))
	self.draw_obj:SetStartDoActionCallBack(BindTool.Bind(self.OnStartDoActionCallBack, self))
	self.draw_obj.auto_fly = false
	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	if main_part then
		main_part:SetMainRole(false)
	end

	self.ui_scene_change_complete = GlobalEventSystem:Bind(SceneEventType.UI_SCENE_CHANGE_COMPLETE, BindTool.Bind1(self.OnUISceneChangeComplete, self))
	self.start_do_action_call_back = nil
	self.camera_type = nil
	self.offset_type = nil
	self.blur_bg = nil
	self.role_res_id = 0
	self.weapon_res_id = 0
	self.wing_res_id = 0
	self.mount_res_id = 0
	self.halo_res_id = 0
	self.baoju_res_id = 0
	self.mantle_res_id = 0
	self.fazhen_res_id = 0
	self.qilinbi_res_id = 0
	self.waist_res_id = 0
	self.mask_res_id = 0
	self.shouhuan_res_id = 0
	self.tail_res_id = 0
	self.jianzhen_res_id = 0
	self.align_type = 0
	self.god_or_demon_res_id = 0
	self.soul_formation_id = 0
	self.nuqi_type = -1
	self.anger_image = false
	self.foot_trail_res_id = 0
	self.show_soul_ring_num = 0

	self.first_frame_bool_name = nil
	self.cur_play_anim = nil
	self.wait_sync_anim_type = nil

	self.actor_trigger = ActorTrigger.New()
	self.load_complete = nil

	self.is_add_ui_probe = true

	self.need_wait_part = nil
	self.show_camera_cfg = nil
	self.cache_fix_local_rotation_list = {}
	self.is_rt_show_model = false
end

function RoleModel:__delete()
	if self.ui_scene_change_complete then
		GlobalEventSystem:UnBind(self.ui_scene_change_complete)
		self.ui_scene_change_complete = nil
	end

	self.ui_scene_camera = nil
	self.camera_type = nil
	self.offset_type = nil
	self:ResetPartDyeColor()
	self:DeleteExpand()

	if self.actor_trigger then
		self.actor_trigger:DeleteMe()
	end
	self.actor_trigger = nil

	if self.delay_update_model_timer then
		GlobalTimerQuest:CancelQuest(self.delay_update_model_timer)
		self.delay_update_model_timer = nil
	end

	self.cur_play_anim = nil
	---self.blur_bg = nil
	
	self.info = nil
	self.special_status_table = nil

	if not IsNil(self.root_node_transform) then
		ResMgr:Destroy(self.root_node_transform.gameObject)
	end

	self.model_pos_node = nil
	self.root_node_transform = nil
	self.draw_obj_transform = nil
	self.display_adjustment_node_trans = nil
	self.parent_view = nil
	self.wait_sync_anim_type = nil
	self.anger_image = nil

	if self.first_recharge_delay then
		GlobalTimerQuest:CancelQuest(self.first_recharge_delay)
		self.first_recharge_delay = nil
	end

    self.weapon_effect_loader = nil
    self.weapon_effect2_loader = nil

	self:ClearDoActionDelay()

   	self.need_wait_part = nil
	self.cache_fs_animation = nil
	self.cache_fs_wait_show_part_list = nil
	self.cache_fs_part_need_load_complete = nil
	self.show_camera_cfg = nil
	self.cache_fix_local_rotation_list = {}

	self.cache_part_scale_list = nil

	if self.draw_obj then
		self.draw_obj:DeleteMe()
		self.draw_obj = nil
	end
	
	if not IsNil(self.ui3d_model) then
        ResMgr:Destroy(self.ui3d_model)
        self.ui3d_model = nil
    end

	if self.display then
		self.display:ClearDisplay()
		self.display = nil
		self:SetIsCanDrag(true)
		self:SetDragRect()

		if self.is_change_tex_resolution then
			self:SetDisplayTextureResolution(RTDisplayDefaultRect.x, RTDisplayDefaultRect.y)
			self.is_change_tex_resolution = nil
		end
	end
	
	self.node_list = nil

	if self.offset then
		RT_MODLE_OFFSET_LIST[self.offset] = nil
		self.offset = nil
	end
	self.ui_model_offset = nil

	if self.u3d_obj then
		ResPoolMgr:Release(self.u3d_obj.gameObject)
		self.u3d_obj = nil
	end

	if self.sequence_tween then
		for k, v in pairs(self.sequence_tween) do
			v:Kill()
			v = nil
		end
		
		self.sequence_tween = nil
	end

	self.show_soul_ring_num = nil

	self:RemoveAransformationEffect()
	self:RemoveAransformationDelayTime()
end

function RoleModel:ResetRotation()
	if self.display then
		self.display:ResetRotation()
		return
	end

	if self.model_pos_node then
		self.model_pos_node.localRotation = Quaternion.New()
		self.perspective_localRotation = self.model_pos_node.localRotation
	end

	if not IsNil(self.draw_obj_transform) then
		self.draw_obj_transform.localRotation = Quaternion.New()
	end
end

function RoleModel:SetModelScale(scale)
	if not scale then return end

	if self.display then
		-- self.display:SetScale(scale)
		return
	end

	self.draw_obj.root.transform.localScale = scale
end

function RoleModel:GetIsNeedPartAnimSync()
	return self.wait_sync_anim_type ~= nil
end

function RoleModel:SetRoleResidValue(role_res_id)
	self.role_res_id = role_res_id
end

function RoleModel:SetCameraType(camera_type)
	self.camera_type = camera_type
end

function RoleModel:SetCameraOffsetType(offset_type)
	self.offset_type = offset_type
end

function RoleModel:GetCameraOffsetType()
	return self.offset_type
end

function RoleModel:SetObjRotate(value)
	local display_obj = self.draw_obj.root.gameObject
	display_obj.transform.rotation = Quaternion.Euler(value.x, value.y, value.z)
end

function RoleModel:SetIsUseObjPool(is_use_objpool)
	self.draw_obj:SetIsUseObjPool(is_use_objpool)
end

function RoleModel:SetRotation(rotation)
	if self.model_pos_node and rotation then
		local quaternion_euler = Quaternion.Euler(rotation.x, rotation.y, rotation.z)
		self.model_pos_node.localRotation = quaternion_euler
		self.perspective_localRotation = self.model_pos_node.localRotation
	end
end

function RoleModel:SetLoadComplete(complete)
	self.load_complete = complete
end

function RoleModel:ClearLoadComplete()
	self.load_complete = nil
end

local model_scale = 100
function RoleModel:SetUI3DModel(parent, event_trigger, zorder, is_need_shadow, camera_type, offset_type)
	if true then
		print_error("请使用SetRenderTexUI3DModel展示模型")
		return
	end
	
	if nil == self.root_node_transform then
		self.root_node_transform = GameObject.New("RoleModel").transform
		self.root_node_transform:SetParent(parent, false)
		self.root_node_transform:SetSiblingIndex(zorder or 1)
		self.root_node_transform.localPosition = Vector3(0, 0, 0)

		self.root_node_transform.localScale = Vector3(model_scale, model_scale, model_scale)

		self.model_pos_node = GameObject.New("PosNode").transform
		self.model_pos_node:SetParent(self.root_node_transform, false)

		self.draw_obj_transform = self.draw_obj:GetRoot().transform
		self.draw_obj_transform:SetParent(self.model_pos_node, false)
	end

	if event_trigger then
		event_trigger:AddDragListener(BindTool.Bind(self.OnRoleDrag, self))
	end

	self.is_ui3d_model = true
	if not self.ui3d_model then
	   self.ui3d_model = self.model_pos_node.transform:GetOrAddComponent(UI3DModel)
	   self:SetActorTriggerUI3D(self.ui3d_model)
	end

	self.offset_type = offset_type
	self.camera_type = camera_type
end

-- ==================================================================================================
-- ==================================        RT展示模型      =========================================
-- ==================================================================================================
function RoleModel:SetDragSpeed(speed)
	if self.display then
		self.display:SetDragSpeed(speed)
	end
end

function RoleModel:SetIsCanDrag(is_can)
	if not self.node_list then
		return
	end

	self.node_list.Display.raw_image.raycastTarget = is_can
end

-- 设置可滑动区域大小
function RoleModel:SetDragRect(width, height)
	if not self.node_list then
		return
	end

	width = width or 600
	height = height or 600
	RectTransform.SetSizeDeltaXY(self.node_list.UIModelDisplay.rect_transform, width, height)
end

-- 设置展示RT区域大小
function RoleModel:SetDisplayTextureResolution(width, height)
	if self.display then
		self.is_change_tex_resolution = width ~= RTDisplayDefaultRect.x or height ~= RTDisplayDefaultRect.y
		self.display:SetDisplayTextureResolution(width, height)
	end
end

function RoleModel:SetDisplayData(display_obj)
	if self.display and (not IsNil(display_obj)) then
		local high_quality = false
		if self.rt_scale_type 
			and (self.rt_scale_type >= ModelRTSCaleType.M and self.rt_scale_type < ModelRTSCaleType.PS_XXS)
			or (self.rt_scale_type >= ModelRTSCaleType.PS_M) then

			high_quality = true
		end

		if self.display.displayBlurUIBackground ~= nil then
			self.display.displayBlurUIBackground = self.blur_bg ~= nil
		end

		self.display:Display(display_obj, self.ui_model_offset, 30, 0.01, 60, high_quality)
	end
end

--[[
local display_data = {
	parent_node = obj,
	camera_type = MODEL_CAMERA_TYPE.BASE,
	offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
	drag_width = 600,
	drag_height = 800,
	can_drag = true,
	camera_depth = 0,
}
]]
function RoleModel:SetRenderTexUI3DModel(display_data)
	if type(display_data) ~= "table" or display_data.parent_node == nil then
		print_error("模型RT图渲染的设置数据异常")
		self.is_rt_show_model = false
		return
	end

	self.offset_type = display_data.offset_type
	self.camera_type = display_data.camera_type
	self.blur_bg = display_data.blur_bg

	self.is_rt_show_model = true
	local dis_bundle, dis_asset = ResPath.GetUIModelDisplay()
	local obj = ResPoolMgr:TryGetGameObject(dis_bundle, dis_asset)
	self.u3d_obj = U3DObject(obj)
	local name_table = obj:GetComponent(typeof(UINameTable))
	self.node_list = U3DNodeList(name_table, self)

	self.draw_obj_transform = self.draw_obj:GetRoot().transform
	
	self.u3d_obj.transform:SetParent(display_data.parent_node.transform)
	self.u3d_obj.transform:SetLocalScale(1, 1, 1)
	self.u3d_obj.transform:SetLocalPosition(0, 0, 0)
	if self.node_list then
		self.display = self.node_list.Display.ui3d_display
		self.ui3d_model = self.node_list.PosNode.gameObject:GetOrAddComponent(UI3DModel)
		self:SetActorTriggerUI3D(self.ui3d_model)

		self.rt_scale_type = display_data.rt_scale_type or ModelRTSCaleType.M
		local rt_scale_data = ModelRTSCaleTypeData[self.rt_scale_type]
		if rt_scale_data then
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.Camera.transform, 0, 0, rt_scale_data.camera_pos_z)
			self.node_list.Camera.camera.fieldOfView = rt_scale_data.camera_field or 30

			self:SetDisplayTextureResolution(rt_scale_data.rect.x, rt_scale_data.rect.y)

			if display_data.drag_width and display_data.drag_height then
				self:SetDragRect(display_data.drag_width, display_data.drag_height)
			else
				self:SetDragRect(rt_scale_data.drag_rect.x, rt_scale_data.drag_rect.y)
			end

			if display_data.camera_depth then
				self.node_list.Camera.camera.depth = display_data.camera_depth
			else
				self.node_list.Camera.camera.depth = TmpRTCameraDepth
			end
		end

		self.node_list.ShowRTRect:SetActive(GLOBAL_SHOW_RT_RECT)
		self.node_list.ShowDragRect:SetActive(GLOBAL_SHOW_RT_DRAG)
		self:ResetRTAdjustmentRootTransform()
	end

	local offest = 2000
	local count = 5
	local cur_model_offset = 0
	local calc_num = 0
	while RT_MODLE_OFFSET_LIST[cur_model_offset] ~= nil do
		cur_model_offset = cur_model_offset + offest
		if cur_model_offset > offest * count then
			cur_model_offset = - offest * count
		end

		-- 保险
		calc_num = calc_num + 1
		if calc_num > count * 2 then
			break
		end
	end

	RT_MODLE_OFFSET_LIST[cur_model_offset] = true
	self.offset = cur_model_offset
	self.ui_model_offset = Vector3(self.offset, self.offset, 0)
	local is_can_drag = display_data.can_drag ~= false
	self:SetIsCanDrag(is_can_drag)

	local display_obj = self.draw_obj:GetRoot().gameObject
	self:SetDisplayData(display_obj)
end

----[[
----- 修改模型调整节点的transform
----]]
function RoleModel:ResetRTAdjustmentRootTransform()
	if self.is_rt_show_model then
		self:SetRTAdjustmentRootLocalPosition(0, 0, 0)
		self:SetRTAdjustmentRootLocalRotation(0, 0, 0)
		self:SetRTAdjustmentRootLocalScale(1)
	end
end

function RoleModel:SetRTAdjustmentRootLocalPosition(x, y, z)
	if not self.node_list or not self.node_list.DisplayAdjustmentRoot then
    	return
    end

	self.node_list.DisplayAdjustmentRoot.transform.localPosition = Vector3(x or 0, y or 0, z or 0)
end

function RoleModel:SetRTAdjustmentRootLocalRotation(x, y, z)
	if not self.node_list or not self.node_list.DisplayAdjustmentRoot then
    	return
    end

	self.node_list.DisplayAdjustmentRoot.transform.localRotation = Quaternion.Euler(x or 0, y or 0, z or 0)
end

function RoleModel:SetRTAdjustmentRootLocalScale(val)
	if not self.node_list or not self.node_list.DisplayAdjustmentRoot then
    	return
    end

	val = val or 1
	Transform.SetLocalScaleXYZ(self.node_list.DisplayAdjustmentRoot.transform, val, val, val)
end


--]]

--[[
----- RT模型通用模型设置接口
		参数：目前最新的模型配置都是这样命名，不是这样命名的不能用该接口
		{
			model_pos,
			model_rot,
			model_scale,
		}
--]]

function RoleModel:SetRTAdjustmentRootTransform(model_cfg)
	if not IsEmptyTable(model_cfg) then

		if model_cfg.model_pos and model_cfg.model_pos ~= "" then
			local pos = Split(model_cfg.model_pos, "|")
			self:SetRTAdjustmentRootLocalPosition(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		if model_cfg.model_rot and model_cfg.model_rot ~= "" then
			local rot = Split(model_cfg.model_rot, "|")
			self:SetRTAdjustmentRootLocalRotation(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
		end
		
		if model_cfg.model_scale and model_cfg.model_scale ~= "" then
			self:SetRTAdjustmentRootLocalScale(model_cfg.model_scale)
		end
	end
end
--]]

-- ==================================================================================================
-- ==================================        UI场景模型      =========================================
-- ==================================================================================================
function RoleModel:SetUISceneModel(event_trigger, camera_type, offset_type, ui_scene_type)
	if nil == self.root_node_transform then
		self.root_node_transform = GameObject.New("RoleModel").transform
		
		--2.2 = 2.5 * 0.88为纠正UImodel的模型高度值
		self.root_node_transform.localPosition = Vector3(0, 2.2, 0)
		self.root_node_transform.localScale = Vector3(0.88, 0.88, 0.88)

		-- 展示随意摆放点
		self.display_adjustment_node_trans = GameObject.New("DisplayAdjustmentRoot").transform
		self.display_adjustment_node_trans:SetParent(self.root_node_transform, false)

		self.model_pos_node = GameObject.New("PosNode").transform
		self.model_pos_node:SetParent(self.display_adjustment_node_trans, false)

		self.draw_obj_transform = self.draw_obj:GetRoot().transform
		self.draw_obj_transform:SetParent(self.model_pos_node, false)
	end

	if event_trigger then
		event_trigger:AddDragListener(BindTool.Bind(self.OnRoleDrag, self))
	end

	self.is_uiscene_model = true
	if not self.ui3d_model then
		self.ui3d_model = self.model_pos_node:GetOrAddComponent(UI3DModel)
		self:SetActorTriggerUI3D(self.ui3d_model)
	 end
	self.offset_type = offset_type
	self.camera_type = camera_type

	self:SetUISceneType(ui_scene_type)
	self:SetIsNeedLayerMaskReflection(true)
end

function RoleModel:SetUISceneType(type)
	if not type then return end
	self.ui_scene_type = type
	local cfg = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto").ui_scene[type]
	self.ui_scene_asset = cfg.asset_name

	self:TrySetUISceneDisplayNode()
end

function RoleModel:TrySetUISceneDisplayNode()
	if IsNil(self.root_node_transform) or nil == self.ui_scene_asset then
		return
	end

	local scene = SceneManager.GetSceneByName(self.ui_scene_asset)
    if not IsNil(scene) and scene:IsValid() then
        local root_objects = scene:GetRootGameObjects()
        local scene_main_node = nil
        for i = 0, root_objects.Length - 1 do
            if root_objects[i].name == "Main" then
                scene_main_node = root_objects[i]
                break
            end
        end

		if not IsNil(scene_main_node) then
			local root = scene_main_node.transform:Find("ModelPos")
			if not IsNil(root) then
				self.root_node_transform:SetParent(root.transform, false)
			end

			self.ui_scene_camera = scene_main_node.transform:Find("Camera")
			self.model_parent_root = root
		end
    end
end

function RoleModel:OnUISceneChangeComplete(ui_scene_type)
	if self.ui_scene_type == ui_scene_type then
		self:TrySetUISceneDisplayNode()
	end
end

function RoleModel:ResetUSAdjustmentRootTransform()
	if self.is_uiscene_model then
		self:SetUSAdjustmentNodeLocalPosition(0, 0, 0)
		self:SetUSAdjustmentNodeLocalRotation(0, 0, 0)
		self:SetUSAdjustmentNodeLocalScale(1)
	end
end

function RoleModel:SetUSAdjustmentNodeLocalPosition(x, y, z)
	if not self.is_uiscene_model or IsNil(self.display_adjustment_node_trans) then
    	return
    end

	self.display_adjustment_node_trans.localPosition = Vector3(x or 0, y or 0, z or 0)
end

function RoleModel:SetUSAdjustmentNodeLocalRotation(x, y, z)
	if not self.is_uiscene_model or IsNil(self.display_adjustment_node_trans) then
    	return
    end

	self.display_adjustment_node_trans.localRotation = Quaternion.Euler(x or 0, y or 0, z or 0)
end

function RoleModel:SetUSAdjustmentNodeLocalRotationTween(x, y, z)
	if not self.is_uiscene_model or IsNil(self.display_adjustment_node_trans) then
    	return
    end

	self.display_adjustment_node_trans:DOLocalRotate(u3dpool.vec3(x, y, z), 0.8, DG.Tweening.RotateMode.Fast)
end

function RoleModel:SetUSAdjustmentNodeLocalScale(val)
	if not self.is_uiscene_model or IsNil(self.display_adjustment_node_trans) then
    	return
    end

	val = val or 1
	Transform.SetLocalScaleXYZ(self.display_adjustment_node_trans, val, val, val)
end


function RoleModel:SetUIRootParentNode(pos_node)
	if pos_node then
		self.root_node_transform:SetParent(pos_node.transform, false)
	end
end












function RoleModel:SetIsNeedLayerMaskReflection(bool)
	if self.draw_obj then
		-- 在RoleModel初始化时DrawObj:_CreatePart(SceneObjPart.Main)会执行过，需额外给main赋值
		local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
		main_part:SetLayerMaskReflectionSwitch(bool)

		self.draw_obj:SetIsNeedLayerMaskReflection(bool)
	end
end

function RoleModel:Rotate(x_angle, y_angle, z_angle)
	if self.draw_obj then
		self.draw_obj:Rotate(x_angle, y_angle, z_angle)
	end
end

function RoleModel:SetSiblingIndex(zorder)
	self.root_node_transform:SetSiblingIndex(zorder or 1)
end

----------------- Model Transform 更新相关 -----------------
-- 新界面禁止调用该方法，走通用的role_model_camera_set
function RoleModel:CustomDisplayPositionAndRotation(position, rotation, scale)
	self.display_position = position or self.display_position
	self.display_rotation = rotation or self.display_rotation
	self.display_scale = scale or self.display_scale

	self:UpdateSceneModelTransform()
end

-- 新界面禁止调用该方法，走通用的role_model_camera_set
function RoleModel:SetSceneModelPositionAndRotation(pos, rot, scale)
	self.display_position = pos or self.display_position
	self.display_rotation = rot or self.display_rotation
	self.display_scale = scale or self.display_scale

	self.display_position = self.display_position or TmpDisplayPosition
	self.display_rotation = self.display_rotation or TmpDisplayRotation
	self:UpdateSceneModelTransform()
end

function RoleModel:SetPositionAndRotation(display_position, display_rotation, scale)
	self.display_position = display_position or self.display_position
	self.display_rotation = display_rotation or self.display_rotation
	self.display_scale = scale or self.display_scale
end

function RoleModel:ClearCustomDisplayTranfromData()
	self.display_position = nil
	self.display_rotation = nil
	self.display_scale = nil
end

function RoleModel:UpdateSceneModelTransform()
	if nil == self.draw_obj then
		return
	end

	if IsNil(self.model_pos_node) then
		return
	end

	if self.is_ui3d_model then
		if self.display_position then
			self.model_pos_node.localPosition = Vector3(self.display_position.x, self.display_position.y, 0)
		end

		if self.display_rotation then
			self.model_pos_node.localRotation = self.display_rotation.w and self.display_rotation or Quaternion.Euler(self.display_rotation.x, self.display_rotation.y, self.display_rotation.z)
			self.perspective_localRotation = self.model_pos_node.localRotation
		end

		if self.display_scale then
			self.model_pos_node.localScale = self.display_scale
		end
	end
end

function RoleModel:GetModelPosNode()
	return self.model_pos_node
end

---------------------------------------------------
-- 角色被拖转动事件
function RoleModel:OnRoleDrag(data)
	if not IsNil(self.draw_obj_transform) then
		local x, y, z = 0, -data.delta.x * 0.25, 0
		self.draw_obj_transform:Rotate(x, y, z)
	end
end



------------------------ 摄像机参数相关 ------------------------
function RoleModel:UpdateCameraTransform(camera_setting)
	if nil == camera_setting then
		return
	end

	local position = self.display_position or camera_setting.position
	local rotation = self.display_rotation or camera_setting.rotation
	if rotation and not rotation.w then
		rotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z)
	end

	local scale = self.display_scale or camera_setting.scale
	if self.is_rt_show_model and self.display then
		if not IsNil(self.draw_obj_transform) then
			self.draw_obj_transform.localPosition = u3dpool.vec3(0, 0, 0)
		end

		self.display:ResetRotation()
		self.display:SetPosNodeLocalPosition(position)
		self.display:SetPosNodeRotation(rotation)
		self.display:SetPosNodeLocalScale(scale)
		self.show_camera_cfg = camera_setting
		return
	end

	if (self.is_ui3d_model or self.is_uiscene_model) then
		if not IsNil(self.draw_obj_transform) then
			self.draw_obj_transform.localPosition = u3dpool.vec3(0, 0, 0)
			self.draw_obj_transform.localRotation = Quaternion.New()
		end

        self.perspective_localRotation = rotation
		if not IsNil(self.model_pos_node) then
			self.model_pos_node.localPosition = position
			self.model_pos_node.localRotation = rotation
			self.model_pos_node.localScale = scale
		end
	end

	self.show_camera_cfg = camera_setting
    self.terrain_offset = camera_setting.TerrainOffset
end

-- 修正成正交摄像机的旋转
function RoleModel:FixToOrthographic(view_center_transform)
	if self.is_uiscene_model then
		self:FixToOrthographicOnUIScene()
		return
	end

	if self.is_ui3d_model and not IsNil(self.model_pos_node) and not IsNil(self.root_node_transform) then
		local key1 = self.camera_setting_key
		local key2 = self.role_res_id
		local cache_list = self.cache_fix_local_rotation_list
		if cache_list[key1] and cache_list[key1][key2] then
			self.model_pos_node.localRotation = cache_list[key1][key2]
			return
		end

		local perspective_localRotation = self.perspective_localRotation or self.model_pos_node.localRotation
		local model_transform = self.root_node_transform
		local camera_pos = UICameraGameObject.transform.position
		local model_pos = model_transform.position
		-- UI摄像机 和 UI节点 上移了500
		local root_offset_y = 500

		-- 处理界面移到很远假隐藏，模型朝向UI摄像机旋转角度计算异常
		local view_center_pos = Vector3(0, root_offset_y, 0)
		if view_center_transform then
			view_center_pos = view_center_transform.position
		end

		-- 计算模型相对于界面中心点的偏移
		local model_offest_pos = Vector3(model_pos.x - view_center_pos.x, model_pos.y - view_center_pos.y, model_pos.z)
		local dir = camera_pos - model_offest_pos

		-- 计算正Z轴方向的旋转
		local forward_dir = Vector3(0, root_offset_y, dir.z)
		local forward_rotation = Quaternion.LookRotation(forward_dir)
		local target_rotation = Quaternion.LookRotation(dir)
		-- 计算旋转偏差
		local offset = target_rotation.eulerAngles - forward_rotation.eulerAngles
		-- 计算旋转轴
		local y_axis = model_transform:InverseTransformDirection(Vector3.up)
		local x_axis = model_transform:InverseTransformDirection(Vector3.right)
		-- 对X轴进行Y轴方向的旋转修正
		x_axis = x_axis * Quaternion.AngleAxis(offset.y, y_axis)

		-- 计算最终的局部旋转
		local cache_fix_local_rotation = Quaternion.AngleAxis(-offset.x, x_axis)
										* Quaternion.AngleAxis(offset.y, y_axis)
										* perspective_localRotation

		self.model_pos_node.localRotation = cache_fix_local_rotation

		if key1 and key2 then
			if not self.cache_fix_local_rotation_list[key1] then
				self.cache_fix_local_rotation_list[key1] = {}
			end

			self.cache_fix_local_rotation_list[key1][key2] = cache_fix_local_rotation
		else
			-- print_error("-----【Which key no value】----", key1, key2)
		end
	end
end

-- UI场景修正成正交摄像机的旋转
function RoleModel:FixToOrthographicOnUIScene()
	if not self.is_uiscene_model or IsNil(self.model_pos_node) or IsNil(self.root_node_transform) or IsNil(self.ui_scene_camera) then
		return
	end

	local key1 = self.camera_setting_key
	local key2 = self.role_res_id
	local cache_list = self.cache_fix_local_rotation_list
	if cache_list[key1] and cache_list[key1][key2] then
		self.model_pos_node.localRotation = cache_list[key1][key2]
		return
	end

	local perspective_localRotation = self.perspective_localRotation or self.model_pos_node.localRotation
	local camera_pos = self.ui_scene_camera.position
	local camera_rotation = self.ui_scene_camera.rotation
	camera_pos.y = 0
	local model_pos = self.model_pos_node.position
	-- 计算摄像机和模型之间的方向向量
	local dir = (camera_pos - model_pos).normalized
	-- 前向向量
	local forward_dir = Vector3(0, 0, dir.z)
	-- 将摄像机旋转应用到方向向量上
	local adjusted_dir = camera_rotation * dir
	-- 计算前向旋转和目标旋转
	local forward_rotation = Quaternion.LookRotation(forward_dir)
	local target_rotation = Quaternion.LookRotation(adjusted_dir)
	-- 计算旋转差值并修正模型的本地旋转
	local rotation_difference = Quaternion.Inverse(forward_rotation) * target_rotation
	local cache_fix_local_rotation = rotation_difference * perspective_localRotation

	self.model_pos_node.localRotation = cache_fix_local_rotation

	if key1 and key2 then
		if not self.cache_fix_local_rotation_list[key1] then
			self.cache_fix_local_rotation_list[key1] = {}
		end

		self.cache_fix_local_rotation_list[key1][key2] = cache_fix_local_rotation
	end
end

-- 主要针对一个display要显示很多种模型的问题
-- 模型摄像机设置 (根据读取的主资源路径来获得摄像机参数，所以一做就做一套，每种模型都要设定好参数)
function RoleModel:GetModelCameraSetting(camera_type, key, bundle, asset)
	camera_type = camera_type or MODEL_CAMERA_TYPE.BASE
	key = key or "base"

	local path = self:GetConcatPath(bundle, asset)
	local seting_config = UI3D_MODEL_SETTING[camera_type][key]
	if SPECIAL_MODEL_ASSET_SETTING[camera_type] ~= nil and SPECIAL_MODEL_ASSET_SETTING[camera_type][path] ~= nil then
		seting_config = SPECIAL_MODEL_ASSET_SETTING[camera_type][path]
	else
		key = UI3D_MODEL_SETTING[camera_type][key] and key or "base"
		seting_config = UI3D_MODEL_SETTING[camera_type][key]
	end

	return self:TransSetting(seting_config, camera_type, key), seting_config
end

function RoleModel:TransSetting(setting, camera_type, key)
	local result = {}
	result.position = Vector3(setting.position[1], setting.position[2], 0)
	result.rotation = Quaternion.Euler(setting.rotation[1], setting.rotation[2], setting.rotation[3])
	result.scale = Vector3(setting.scale, setting.scale, setting.scale)
	if setting.TerrainOffset then
		result.TerrainOffset = Vector3(setting.TerrainOffset[1], setting.TerrainOffset[2], setting.TerrainOffset[3])
	end

	if self.offset_type then
		local offset_cfg = MODEL_OFFSET_SETTING[camera_type][self.offset_type]
		if offset_cfg and offset_cfg[key] then
			local cfg = offset_cfg[key]
			result.position = Vector3(result.position.x + cfg.pos[1], result.position.y + cfg.pos[2], 0)
			result.scale = result.scale * cfg.scale
            result.rotation = Quaternion.Euler(setting.rotation[1] + cfg.rotation[1], setting.rotation[2] + cfg.rotation[2],
            setting.rotation[3] + cfg.rotation[3])
		end
	end

	return result
end

-- 获取模型默认参数设置
function RoleModel:GetModelCameraDefSetting(camera_type, key)
	key = UI3D_MODEL_SETTING[camera_type][key] and key or "base"
	return UI3D_MODEL_SETTING[camera_type][key]
end

---------------------------------------------------------------

function RoleModel:SetModleStageIsActive(value)
	if not IsNil(self.model_pos_node) then
		self.model_pos_node.gameObject:SetActive(value)
	end
end

function RoleModel:GetDrawPart(value)
	if not self.draw_obj then 
		return nil
	end

	value = value or SceneObjPart.Main
	local part = self.draw_obj:GetPart(value)
	return part
end

function RoleModel:GetDrawObj()
	return self.draw_obj
end

function RoleModel:GetCurAssetPath()
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	return part:GetCurAssetPath()
end

function RoleModel:GetBundleType(bundle, nuqi_type)
	if nil == bundle then
		return ""
	end

	local start_pos, end_pos = string.find(bundle, "%/.-%/")
	if nil == start_pos or nil == end_pos then
		return ""
	end

	local key = string.sub(bundle, start_pos + 1, end_pos - 1)

	if "character" == key then
		if nuqi_type ~= nil then
			key = string.format("%s/%s", key, "realm")
		else
			start_pos, end_pos = string.find(bundle, "%/.-.prefab")
			key = string.sub(bundle, start_pos + 1, end_pos - 10)
		end
	elseif "weapon" == key then
		key = "weapon" .. string.sub(bundle, end_pos, end_pos + 4)
	-- elseif "other" == key then
		-- local sub_key = string.sub(bundle, end_pos + 1, end_pos + 7)
		-- if sub_key == "sixiang" then
		-- 	key = string.format("%s/%s", key, sub_key)
		-- end
	elseif "tianhunshenqi" == key then
		key = string.sub(bundle, start_pos + 1, end_pos + 1)
	end

	local halo_start_pos, halo_end_pos = string.find(bundle, "effects2/prefab/halo/")
	if halo_start_pos and halo_end_pos then
		key = "halo"
	end

	if bundle == "effects2/prefab/footlight_prefab" then
		key = "footlight"
	end

	return key
end

function RoleModel:GetConcatPath(bundle, asset)
	if bundle == nil or asset == nil then
		return ""
	end

	return string.format("%s#%s", bundle, asset)
end


---------------------- SetXXXResid相关 ----------------------
local had_weapon_ani_list = {
	"model/character/",
	"model/tianshen/",
}
function RoleModel:SetMainAsset(bundle, asset, func)
	-- print_error(string.format("SetMainAsset asset:%s, draw_model_type:%s" , asset, draw_model_type))
	self:RemoveMain(true)
	self.wait_sync_anim_type = nil

	if bundle then
		local eff_key = ""
		local i, j = string.find(bundle, "model/(.-)/")
		if i and j then
			eff_key = string.sub(bundle, i, j)
		end

		if eff_key and MODEL_EFF[eff_key] then
			self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig(MODEL_EFF[eff_key], asset))
			self.actor_trigger:SetTargetEffectTriggerCustomScale(Vector3(1, 1, 1))
		else
			self:SetActorConfigPrefabData(nil)
		end

		-- 模型带武器动画的
		for k,v in pairs(had_weapon_ani_list) do
			i, j = string.find(bundle, "model/character/")
			if i and j then
				self.wait_sync_anim_type = SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE
				break
			end
		end
	end

	self.draw_obj:ChangeWaitSyncAnimType(self.wait_sync_anim_type)
	if self.wait_sync_anim_type == SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE then
		self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.Weapon, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
	end

	self.cur_play_anim = nil

	self:SetRoleResidValue(asset)
	self.draw_obj:SetIsHdTexture(true)
	if not self.is_add_ui_probe then
		self.draw_obj:SetIsLerpProbe(true)
	end

	self.draw_obj:SetLoadComplete(BindTool.Bind(self._OnModelLoaded, self))

    local key = self:GetBundleType(bundle)
    self.camera_setting_key = key
	local camera_setting = self:GetModelCameraSetting(self.camera_type, key, bundle, asset)
	self:UpdateCameraTransform(camera_setting)

	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:CancleLoadInQueue()
	self:FakeHideDelayShow()
	self:ResetUSAdjustmentRootTransform()

	self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, func)
end

--[[
    extra_role_model_data = {
		prof = 职业
		sex = 性别
        d_face_res = 默认脸资源id
        d_hair_res = 默认头发资源id
		d_body_res = 默认身体资源id
		weapon_res_id = 武器资源id
		animation_name = 需要播发的动画名，默认UiIdle
		no_need_do_anim = 不需要播动画

		eye_size = 眼眶大小 - number
		eye_position  = 眼眶上下 - number
		eye_angle = 眼眶角度 - number
		eye_close = 眼眶开合 - number
		eyebrow_angle = 眉毛角度 - number
		eye_shadow_color = 眼影颜色 - table (r=255,g=255,b=255,a=255)

		left_pupil_type = 左-瞳孔款式 number
        left_iris_size = 左-虹膜大小 - number
        left_pupil_size = 左-瞳孔大小 - number
        left_pupil_color = 左-瞳孔颜色 - table (r=255,g=255,b=255,a=255)

        -- 瞳孔材质相同，赋值左眼即可，右眼可不赋值
		right_pupil_type = 右-瞳孔款式  number
        right_iris_size = 右-虹膜大小 - number
        right_pupil_size = 右-瞳孔大小 - number
        right_pupil_color = 右-瞳孔颜色 - table (r=255,g=255,b=255,a=255)

		nose_size = 鼻子大小 - number
		nose_angle = 鼻头角度 - number
		mouth_size = 嘴巴大小 - number
		mouth_position = 嘴巴上下 - number
		mouth_angle = 嘴角角度 - number
		mouth_color = 嘴巴颜色 - table (r=255,g=255,b=255,a=255)
		cheek_size = 脸颊大小 - number
		chin_length = 下巴长度 - number
		face_decal_id = 脸部贴花 number
		hair_color = 头发颜色 - table (r=255,g=255,b=255,a=255)
		preset_seq = 预设索引 -- number
    }
]]
-- no_need_do_anim 不需要默认动画
function RoleModel:SetRoleResid(role_res_id, callback, extra_role_model_data, is_not_reset_pos, is_not_main_role)
	-- print_error(string.format("RoleModel.SetRoleResid role_res_id:%s, extra_role_model_data:%s", role_res_id, extra_role_model_data))
	local sex, prof, d_body_res, d_face_res, d_hair_res, weapon_res_id, animation_name, no_need_do_anim
	local eye_size, eye_position, eye_shadow_color, left_pupil_type, left_pupil_size, left_pupil_color,
		right_pupil_type, right_pupil_size, right_pupil_color, mouth_size, mouth_position, mouth_color, face_decal_id, hair_color,
		preset_seq
			
	if extra_role_model_data then
		d_body_res = extra_role_model_data.d_body_res
		d_face_res = extra_role_model_data.d_face_res
		d_hair_res = extra_role_model_data.d_hair_res
		sex = extra_role_model_data.sex
		prof = extra_role_model_data.prof
		weapon_res_id = extra_role_model_data.weapon_res_id
		animation_name = extra_role_model_data.animation_name
		no_need_do_anim = extra_role_model_data.no_need_do_anim

		eye_size = extra_role_model_data.eye_size
		eye_position = extra_role_model_data.eye_position
		eye_shadow_color = extra_role_model_data.eye_shadow_color

		left_pupil_type = extra_role_model_data.left_pupil_type
		left_pupil_size = extra_role_model_data.left_pupil_size
		left_pupil_color = extra_role_model_data.left_pupil_color

		right_pupil_type = extra_role_model_data.right_pupil_type
		right_pupil_size = extra_role_model_data.right_pupil_size
		right_pupil_color = extra_role_model_data.right_pupil_color

		mouth_size = extra_role_model_data.mouth_size
		mouth_position = extra_role_model_data.mouth_position
		mouth_color = extra_role_model_data.mouth_color

		face_decal_id = extra_role_model_data.face_decal_id
		hair_color = extra_role_model_data.hair_color
		preset_seq = extra_role_model_data.preset_seq
	end

	local mr_def_body_res, mr_def_face_res, mr_def_hair_res = RoleWGData.Instance:GetMainRoleDefSkinPartRes()
	d_body_res = d_body_res or mr_def_body_res
	d_face_res = d_face_res or mr_def_face_res
	d_hair_res = d_hair_res or mr_def_hair_res

   	self:ClearDoActionDelay()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	sex = sex or main_vo.sex
	prof = prof or main_vo.prof
	prof = prof and prof % 10

	self:SetActorConfigPrefabData(ConfigManager.Instance:GetRoleAutoPrefabConfig(sex, prof))
	self.wait_sync_anim_type = SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE
	self.draw_obj:ChangeWaitSyncAnimType(self.wait_sync_anim_type, self.role_res_id ~= role_res_id)
	self:SetRoleResidValue(role_res_id)

	self.weapon_res_id = weapon_res_id

	self.draw_obj:SetLoadComplete(BindTool.Bind(self._OnModelLoaded, self))
	self.draw_obj:SetIsHdTexture(true)
	if not self.is_add_ui_probe then
		self.draw_obj:SetIsLerpProbe(true)
	end

	local bundle, asset = ResPath.GetRoleModel(RoleWGData.GetJobModelId(sex, prof))
	local role_res_id_number = tonumber(self.role_res_id) or 0
	local resouce = role_res_id_number % 1000

	-- 这里加了一个操作，时装标记染色才会使用创角编辑的头发颜色，
	-- 没有染色的直接使用默认材质色，不适用编辑捏脸头发色
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByResId(SHIZHUANG_TYPE.BODY, resouce)
	local is_show_dye = true

	if fashion_cfg then
		local is_open_dye = fashion_cfg and fashion_cfg.is_open_dyeing or 0
		is_show_dye = is_open_dye == 1
	end

	local def_hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, mr_def_hair_res, sex, prof)
	local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(sex, prof, resouce, d_body_res, d_face_res, d_hair_res)
	local key = self:GetBundleType(bundle)
	self.camera_setting_key = key
	local camera_setting = self:GetModelCameraSetting(self.camera_type, key, bundle, asset)
    self:UpdateCameraTransform(camera_setting)

	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:CancleLoadInQueue()
	self:FakeHideDelayShow()

	if not is_not_reset_pos then
		self:ResetUSAdjustmentRootTransform()
	end

	extra_role_model_data.sex = sex
	extra_role_model_data.prof = prof
	extra_role_model_data.role_body_res = body_res
	extra_role_model_data.role_face_res = face_res
	extra_role_model_data.role_hair_res = hair_res

	local role_diy_appearance = RoleWGData.Instance:GetMainRoleDiyAppearanceInfo()
	local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(sex, prof, preset_seq or 1)
	extra_role_model_data.eye_size = eye_size or role_diy_appearance.eye_size
	extra_role_model_data.eye_position = eye_position or role_diy_appearance.eye_position
	extra_role_model_data.eye_shadow_color = eye_shadow_color or role_diy_appearance.eye_shadow_color
	extra_role_model_data.left_pupil_type = left_pupil_type or role_diy_appearance.left_pupil_type
	extra_role_model_data.left_pupil_size = left_pupil_size or role_diy_appearance.left_pupil_size
	extra_role_model_data.left_pupil_color = left_pupil_color or role_diy_appearance.left_pupil_color
	extra_role_model_data.right_pupil_type = right_pupil_type or role_diy_appearance.right_pupil_type
	extra_role_model_data.right_pupil_size = right_pupil_size or role_diy_appearance.right_pupil_size
	extra_role_model_data.right_pupil_color = right_pupil_color or role_diy_appearance.right_pupil_color
	extra_role_model_data.mouth_size = mouth_size or role_diy_appearance.mouth_size
	extra_role_model_data.mouth_position = mouth_position or role_diy_appearance.mouth_position
	extra_role_model_data.mouth_color = mouth_color or role_diy_appearance.mouth_color
	extra_role_model_data.face_decal_id = face_decal_id or role_diy_appearance.face_decal_id
	extra_role_model_data.hair_color = hair_color or role_diy_appearance.hair_color
	-- 这里加了一个操作，时装标记染色才会使用创角编辑的头发颜色，
	-- 没有染色的直接使用默认材质色，不适用编辑捏脸头发色
	if not is_show_dye then
		extra_role_model_data.hair_color = nil
	end

	extra_role_model_data.eye_angle = preset_diy_cfg and preset_diy_cfg.eye_angle
	extra_role_model_data.eye_close = preset_diy_cfg and preset_diy_cfg.eye_close
	extra_role_model_data.eyebrow_angle = preset_diy_cfg and preset_diy_cfg.eyebrow_angle
	extra_role_model_data.nose_size = preset_diy_cfg and preset_diy_cfg.nose_size
	extra_role_model_data.nose_angle = preset_diy_cfg and preset_diy_cfg.nose_angle
	extra_role_model_data.mouth_angle = preset_diy_cfg and preset_diy_cfg.mouth_angle
	extra_role_model_data.cheek_size = preset_diy_cfg and preset_diy_cfg.cheek_size
	extra_role_model_data.chin_length = preset_diy_cfg and preset_diy_cfg.chin_length

	--self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, callback, DRAW_MODEL_TYPE.ROLE, extra_role_model_data, nil)
	self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, callback, DRAW_MODEL_TYPE.ROLE, extra_role_model_data, function (skin_type)
		local final_part_color = nil
		local final_project_index = nil
		-- 获取染色配置
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByResId(SHIZHUANG_TYPE.BODY, resouce)
		if not fashion_cfg then
			return
		end

		local fashion_dye_cfg = NewAppearanceDyeWGData.Instance:GetConsumeCfgByIndex(fashion_cfg.index, fashion_cfg.part_type)
		if not fashion_dye_cfg then
			return
		end

		if is_not_main_role then
			final_part_color = extra_role_model_data.part_color
			final_project_index = extra_role_model_data.server_project_index + 1
		else
			local server_project_index = NewAppearanceDyeWGData.Instance:GetDyeingIndexInfoBySeq(fashion_dye_cfg.seq)
			final_project_index = server_project_index + 1
			local project_info = NewAppearanceDyeWGData.Instance:GetDyeingInfoBySeqIndex(fashion_dye_cfg.seq, final_project_index)
			final_part_color = project_info and project_info.part_color
		end

		if (not final_part_color) or (not final_project_index) then
			return
		end

		local change_body_dye_color_fun = function()
			local dye_color_table = {}
			for show_part, color_data in ipairs(final_part_color) do
				local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(fashion_dye_cfg.seq, show_part)
				local index_list = dye_index_list_data and dye_index_list_data.dye_index_list
		
				local color = nil
				if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
					color = {}
					color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
				end
		
				if show_part ~= HAIR_PART then
					if index_list and color then
						for _, dye_index in ipairs(index_list) do
							local dye_color_data = {}
							dye_color_data.dye_index = dye_index
							dye_color_data.r = color.r
							dye_color_data.g = color.g
							dye_color_data.b = color.b
							dye_color_data.a = color.a
							table.insert(dye_color_table, dye_color_data)
						end
					end
				end
			end
		
			if not IsEmptyTable(dye_color_table) then
				self:ChangePartDyeColor(dye_color_table)
			end
		end
	
		local change_hair_dye_color_fun = function()
			local color_data = final_part_color[HAIR_PART]
			if color_data and (color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0) then
				self:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, color_data)
			end
		end

		if skin_type == ROLE_SKIN_TYPE.BODY then
			local body_material_id = NewAppearanceDyeWGData.Instance:GetProjectBodyIdCfgBySeq(fashion_dye_cfg.seq, final_project_index)
			self:ChangeRoleMaterialsByProjectId(skin_type, body_material_id, change_body_dye_color_fun, nil, extra_role_model_data)
		elseif skin_type == ROLE_SKIN_TYPE.FACE then
			local face_material_id = NewAppearanceDyeWGData.Instance:GetProjectFaceIdCfgBySeq(fashion_dye_cfg.seq, final_project_index)
			self:ChangeRoleMaterialsByProjectId(skin_type, face_material_id, nil, nil, extra_role_model_data)
		elseif skin_type == ROLE_SKIN_TYPE.HAIR then
			local hair_material_id = NewAppearanceDyeWGData.Instance:GetProjectHairIdCfgBySeq(fashion_dye_cfg.seq, final_project_index)
			self:ChangeRoleMaterialsByProjectId(skin_type, hair_material_id, change_hair_dye_color_fun, nil, extra_role_model_data)
		end
	end)

	-- 默认主体和武器部位都要加载
	-- 存在不展示武器的情况 比如跑步，这时需要移除武器的等待状态，额外调:SetWeaponModelFakeRemove()
	if weapon_res_id and weapon_res_id > 0 then
		local weapon_bundle, weapon_asset = ResPath.GetWeaponModelRes(self.weapon_res_id)
		self:SetWeaponModel(weapon_bundle, weapon_asset)
	end

	if not no_need_do_anim then
		-- if self.cur_play_anim ~= animation_name then
		self:PlayRoleAction(animation_name or SceneObjAnimator.UiIdle)
		-- end
	end
end

function RoleModel:SetGoddessResid(role_res_id)
	self:ClearDoActionDelay()
	self.wait_sync_anim_type = nil
	self.draw_obj:ChangeWaitSyncAnimType(self.wait_sync_anim_type)
	self:SetRoleResidValue(role_res_id)
	self.draw_obj:SetLoadComplete(BindTool.Bind(self._OnModelLoaded, self))
	local bundle, asset = ResPath.GetGoddessModel(self.role_res_id)
	self:ResetUSAdjustmentRootTransform()

	self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset)
end

function RoleModel:SetGoddessWeaponResid(weapon_res_id)
	self.weapon_res_id = weapon_res_id
	local bundle, asset = ResPath.GetGoddessWeaponModel(self.weapon_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Weapon, bundle, asset)
end

function RoleModel:SetMountResid(mount_res_id)
	self:ClearDoActionDelay()
	self.mount_res_id = mount_res_id
	local bundle, asset = ResPath.GetMountModel(self.mount_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Mount, bundle, asset)
end

function RoleModel:SetFightMountResid(mount_res_id)
	self:ClearDoActionDelay()
	self.mount_res_id = mount_res_id
	local bundle, asset = ResPath.GetMountModel(self.mount_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.FightMount, bundle, asset)
end

function RoleModel:SetGodOrDemonResid(god_or_demon_res_id)
	self.god_or_demon_res_id = god_or_demon_res_id
	if nil == god_or_demon_res_id or god_or_demon_res_id <= 0 then
		self:RemoveGodOrDemonHalo()
		return
	end

	local bundle, asset = ResPath.GetYiNianMagicHaloModel(god_or_demon_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.GodOrDemonHalo, bundle, asset)
end

function RoleModel:SetHaloResid(halo_res_id, call_back)
	self.halo_res_id = halo_res_id
	local bundle, asset = ResPath.GetHaloModel(halo_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Halo, bundle, asset, call_back)
end

function RoleModel:SetBaoJuResid(baoju_res_id, callback)
	self.baoju_res_id = baoju_res_id
	local bundle, asset = ResPath.GetFaBaoModel(self.baoju_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.BaoJu, bundle, asset, callback)
end

function RoleModel:SetWeaponResid(weapon_res_id, callback)
	self.weapon_res_id = weapon_res_id
	local bundle, asset = ResPath.GetWeaponModelRes(self.weapon_res_id)
	self:SetWeaponModel(bundle, asset, nil, callback)
end

function RoleModel:SetWingResid(wing_res_id, is_scene_idle, callback)
	self.wing_res_id = wing_res_id
	local bundle, asset = ResPath.GetWingModel(self.wing_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Wing, bundle, asset, callback)
end

function RoleModel:SetMantleResid(mantle_res_id)
	self.mantle_res_id = mantle_res_id
	local bundle, asset = ResPath.GetPifengModel(self.mantle_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Mantle, bundle, asset)
end

function RoleModel:SetSkillHaloResid(halo_res_id)
	local bundle, asset = ResPath.GetSkillHaloModel(halo_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.SkillHalo, bundle, asset)
end

function RoleModel:SetGoddessAsset(bundle, asset)
	self.draw_obj:ChangeModel(SceneObjPart.Weapon, bundle, asset)
end

-- 是否为怒气形象
function RoleModel:SetAngerImage(nuqi_type, is_need_reset, d_body_res, d_face_res, d_hair_res, is_no_effect)
	if nuqi_type ~= nil and nuqi_type > -1 then
		self.nuqi_type = nuqi_type
		self:RemoveAllModel()
		local nuqi_client_type = self.nuqi_type + 1
		local main_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_res_id = string.format("%s0%s", RoleWGData.GetJobModelId(main_vo.sex, 1), nuqi_client_type) 
		local bundle, asset = ResPath.GetRoleModel(special_res_id)

		local prof = main_vo.prof
		local sex = main_vo.sex
		local body_res, face_res, hair_res = RoleWGData.GetShowRoleRealmSkinPartRes(sex, prof, d_body_res, d_face_res, d_hair_res)
		-- self:ResetUSAdjustmentRootTransform()
		local extra_model_data = {
			role_body_res = body_res,
			role_face_res = face_res,
			role_hair_res = hair_res,
			is_realm = true,
		}

		local key = self:GetBundleType(bundle, nuqi_client_type)
		self.camera_setting_key = key
		local camera_setting = self:GetModelCameraSetting(self.camera_type, key, bundle, asset)
		self:UpdateCameraTransform(camera_setting)

		self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, nil, DRAW_MODEL_TYPE.ROLE, extra_model_data, function(skin_type)
			if skin_type ~= ROLE_SKIN_TYPE.BODY then
				return
			end

			self:RemoveAransformationEffect()
			if not is_no_effect then
				if is_need_reset then
					self:ExecuteAransformationAction2(nuqi_type)
				else
					self:ExecuteAransformationAction(nuqi_type)
				end
			end

			if self.ui3d_model then
				self.ui3d_model:ResetAllRenders()
			end
		end)

		self:SetWeaponModelFakeRemove()
	end
end
------------------------------------------------------------------

---------------------- Remove相关 ----------------------

function RoleModel:RemoveMain(real_remove)
	self:ClearDoActionDelay()
	self.draw_obj:RemoveModel(SceneObjPart.Main, real_remove)
end

function RoleModel:RemoveMount(real_remove)
	self.mount_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Mount, real_remove)
end

function RoleModel:RemoveFightMount(real_remove)
	self.mount_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.FightMount, real_remove)
end

function RoleModel:RemoveWeapon(real_remove)
	self.weapon_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Weapon, real_remove)
end

function RoleModel:RemoveWaist(real_remove)
	self.waist_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Waist, real_remove)
end

function RoleModel:RemoveMask(real_remove)
	self.mask_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Mask, real_remove)
end

function RoleModel:RemoveTail(real_remove)
	self.tail_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Tail, real_remove)
end

function RoleModel:RemoveShouHuan(real_remove)
	self.shouhuan_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.ShouHuan, real_remove)
end

function RoleModel:RemoveJianZhen(real_remove)
	self.jianzhen_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Jianling, real_remove)
end

function RoleModel:RemoveWing(real_remove)
	self.wing_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Wing, real_remove)
end

function RoleModel:RemoveHalo(real_remove)
	self.halo_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Halo, real_remove)
end

function RoleModel:RemoveSkillHalo(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.SkillHalo, real_remove)
end

function RoleModel:RemoveBaoJu(real_remove)
	self.baoju_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.BaoJu, real_remove)
end

function RoleModel:RemoveFoot(real_remove)
	self.foot_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.Foot, real_remove)
end

function RoleModel:RemoveFaZhen(real_remove)
	self.fazhen_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.FaZhen, real_remove)
end

function RoleModel:RemoveFootTrail(real_remove)
	self.foot_trail_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.FoorTrail, real_remove)
end

function RoleModel:RemoveShuangShengTianShenUI(real_remove)
	self.fazhen_res_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.ShuangShengTianShenUI, real_remove)
end

function RoleModel:RemoveSoulFormation(real_remove)
	self.soul_formation_id = 0
	self.draw_obj:RemoveModel(SceneObjPart.SoulFormation, real_remove)
end

function RoleModel:RemoveGodOrDemonHalo()
	self.god_or_demon_res_id = 0
	local part = self.draw_obj:GetPart(SceneObjPart.GodOrDemonHalo)
	part:RemoveModel()
end

function RoleModel:RemoveAllModel()
	self:RemoveMain(true)
	self:RemoveMount()
	self:RemoveFightMount()
	self:RemoveWeapon(true)
	self:RemoveWaist()
	self:RemoveMask()
	self:RemoveTail()
	self:RemoveShouHuan()
	self:RemoveJianZhen()
	self:RemoveWing()
	self:RemoveHalo()
	self:RemoveBaoJu()
	self:RemoveFoot()
	self:RemoveFaZhen()
	self:RemoveSkillHalo()
	self:RemoveGundamAll()
	self:RemoveGodOrDemonHalo()
	self:RemoveSoulFormation()
	self:RemoveFootTrail()
	self:RemoveAllSoulRing()
	self:RemoveSoulRingSelectModel()
end

------------------------------------------------------------------

function RoleModel:SetDrawObjCallBack()
	if not self.draw_obj then
		return
	end
	self.draw_obj:SetLoadComplete(BindTool.Bind(self._OnModelLoaded, self))
end

function RoleModel:SetWeaponModel(bundle, asset, scale, callback)
	self:RemoveWeapon(true)
	self.draw_obj:ChangeModel(SceneObjPart.Weapon, bundle, asset, function(obj)
		if callback then
			callback()
		end

		if scale then
			obj.gameObject.transform:SetLocalScale(scale, scale, scale)
		end
	end)
end

-- 临时解决主体和部位同步动画，但是武器部位不需加载的
function RoleModel:SetWeaponModelFakeRemove()
	self.draw_obj:SetWaitSyncAnimPartIsRemove(SceneObjPart.Weapon, false)
end

function RoleModel:ResetWeaponModelFakeRemove()
	self.draw_obj:SetWaitSyncAnimPartIsRemove(SceneObjPart.Weapon, true)
end

function RoleModel:SetGoddessWingResid(wing_res_id)
	self.wing_res_id = wing_res_id
	local bundle, asset = ResPath.GetGoddessWingModel(self.wing_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Wing, bundle, asset)
end

function RoleModel:SetWingAsset(bundle, asset)
	self.draw_obj:ChangeModel(SceneObjPart.Wing, bundle, asset)
end

function RoleModel:SetFaZhenResid(fazhen_res_id)
	self.fazhen_res_id = fazhen_res_id
	-- local has_fazhen = tonumber(self.fazhen_res_id) ~= nil and self.fazhen_res_id > 0 or self.fazhen_res_id ~= ""
	-- if part ~= nil and has_fazhen then
	-- 	part:ReSetOffsetY()
	-- end

	if nil == fazhen_res_id or fazhen_res_id <= 0 then
		self:RemoveFaZhen(true)
		return
	end

	local bundle, asset = ResPath.GetFaZhenModel(fazhen_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.FaZhen, bundle, asset)
end

-- function RoleModel:SetQiLinBiResid( qilinbi_res_id, sex )
-- 	self.qilinbi_res_id = qilinbi_res_id
-- 	local part = self.draw_obj:GetPart(SceneObjPart.QiLinBi)
-- 	if nil == qilinbi_res_id or qilinbi_res_id < 0 then
-- 		part:RemoveModel()
-- 		return
-- 	end

-- local bundle, asset = ResPath.GetQilinBiModel(qilinbi_res_id, sex)
-- self.draw_obj:ChangeModel(SceneObjPart.QiLinBi, bundle, asset)
-- end

-- 设置腰饰
function RoleModel:SetWaistResid(waist_res_id, callback)
	self.waist_res_id = waist_res_id

	if nil == waist_res_id or waist_res_id <= 0 then
		self:RemoveWaist(true)
		return
	end

	local bundle, asset = ResPath.GetBeltModel(waist_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Waist, bundle, asset, callback)
end

--设置面饰
function RoleModel:SetMaskResid(mask_res_id, callback)
	self.mask_res_id = mask_res_id

	if nil == mask_res_id or mask_res_id <= 0 then
		self:RemoveMask(true)
		return
	end

	local bundle, asset = ResPath.GetMaskModel(mask_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Mask, bundle, asset, callback)
end

--设置手环
function RoleModel:SetShouHuanResid(shouhuan_res_id)
	self.shouhuan_res_id = shouhuan_res_id
	if nil == shouhuan_res_id or (type(shouhuan_res_id) == "number" and shouhuan_res_id <= 0) then
		self:RemoveShouHuan(true)
		return
	end

	local bundle, asset = ResPath.GetShouhuanModel(shouhuan_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.ShouHuan, bundle, asset)
end

--设置尾巴
function RoleModel:SetTailResid(tail_res_id)
	self.tail_res_id = tail_res_id

	if nil == tail_res_id or tail_res_id <= 0 then
		self:RemoveTail(true)
		return
	end

	local bundle, asset = ResPath.GetWeibaModel(tail_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Tail, bundle, asset)
end

--设置剑阵
function RoleModel:SetJianZhenResid(jianzhen_res_id, callback)
	self.jianzhen_res_id = jianzhen_res_id

	if nil == jianzhen_res_id or jianzhen_res_id <= 0 then
		self:RemoveJianZhen(true)
		return
	end

	local bundle, asset = ResPath.GetJianZhenModel(jianzhen_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.Jianling, bundle, asset, callback)
end

--设置魂阵
function RoleModel:SetSoulFormationResid(soul_formation_id, wuhun_id)
	self.soul_formation_id = soul_formation_id

	if nil == soul_formation_id or soul_formation_id <= 0 then
		self:RemoveSoulFormation(true)
		return
	end

	local bundle, asset = ResPath.GetWuHunWunZhenModel(wuhun_id, soul_formation_id)
	self.draw_obj:ChangeModel(SceneObjPart.SoulFormation, bundle, asset)
end

-- 设置足迹拖尾 播放奔跑动画  设置成特殊足迹展示角度
function RoleModel:SetFootTrailModel(foot_trail_res_id)
	self.foot_trail_res_id = foot_trail_res_id
	if nil == foot_trail_res_id or foot_trail_res_id <= 0 then
		self:RemoveFootTrail(true)
		return
	end

	local bundle, asset = ResPath.GetUIFootEffect(foot_trail_res_id)
	self.draw_obj:ChangeModel(SceneObjPart.FoorTrail, bundle, asset)
	
	-- self:SetRotation(MODEL_ROTATION_TYPE.FOOT)
	-- self:PlayRoleAction(SceneObjAnimator.Move)
end

function RoleModel:SetVisible(state)
	if not state then
		self:ClearDoActionDelay()
	end

	self.draw_obj:SetVisible(state)
end


function RoleModel:CheckIsRoleModel(asset_res_id)
	if not asset_res_id or asset_res_id <= 0 then
		return false, -1
	end

	local cfg = ConfigManager.Instance:GetAutoConfig("job_auto").job
	for k,v in pairs(cfg) do
		if asset_res_id == v.default_model then
			return true, v.sex
		end
	end

	return false, -1
end

function RoleModel:_OnModelLoaded(part, obj, obj_class, bundle_name, asset_name)
	local part_obj = self.draw_obj:GetPart(part)
	if part == SceneObjPart.Main then
		--动态骨骼组件，只有角色模型才可以使用
		local is_role_model_res, sex = self:CheckIsRoleModel(tonumber(asset_name))
		if is_role_model_res and part_obj:GetObj() ~= nil then
			part_obj:CreateDynamicBone(sex)
		end

		if self.trigger_name then
			self:SetTrigger(self.trigger_name)
			self.trigger_name = nil
		end

		-- 角色武器特殊显示
		local is_weapon = false
		if string.find(bundle_name, "model/weapon/") then
			is_weapon = true
			-- self:SetFirstFrameBoolName(nil, true)
		end

		local first_frame_name = self:GetFirstFrameBoolName()
		if first_frame_name then
			self:SetOneFrameBool(first_frame_name, true)
		end

		if is_weapon then
			self.cur_play_anim = SceneObjAnimator.WeaponIdel
			self.draw_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.WeaponIdel)
		end
	end

	if self.load_complete then
		self.load_complete(part, obj)
	end

	if self.load_complete2 and self.need_wait_part ~= nil and self.need_wait_part == part then
		self.need_wait_part = nil
		local call_2 = self.load_complete2
		call_2()
	end

	if self.load_complete1 then
		self.load_complete1()
		self.load_complete1 = nil
	end

	self:CallChangePartScale(part)

	if nil ~= obj and not IsNil(obj.gameObject) then
		self:OnAddGameobject(obj.gameObject)
	end
end

function RoleModel:_OnModelRemove(part, obj)
	if nil ~= self.ui3d_model and nil ~= obj then
		if not IsNil(self.ui3d_model) then
			self.ui3d_model:OnRemoveGameObject(obj.gameObject)
		-- else
		-- 	print_error("[RoleModel] _OnModelRemove Big Bug!!!, ui3d_model had destroy")
		end
	end

	if nil ~= obj and not IsNil(obj.gameObject) then
		local attachs = obj:GetComponentsInChildren(TypeGameObjectAttach, true)
		if not IsGameStop then
			GameObjAttachEventHandle.DisableGameObjAttachArray(attachs)
		end
	end
end

function RoleModel:OnAddGameobject(obj)
	if not IsNil(self.ui3d_model) then
		self.ui3d_model:OnAddGameobject(obj)
	end
end

function RoleModel:OnRemoveGameObject(obj)
	if not IsNil(self.ui3d_model) then
		self.ui3d_model:OnRemoveGameObject(obj)
	end
end

function RoleModel:_OnPartnerModelLoaded(partner_ui3d_model, part, obj)
	if nil ~= obj then
		if nil ~= partner_ui3d_model then
			partner_ui3d_model:OnAddGameobject(obj.gameObject)
		else
			self:OnAddGameobject(obj.gameObject)
		end
	end
end

function RoleModel:_OnPartnerModelRemove(partner_ui3d_model, part, obj)
	if nil ~= obj then
		if nil ~= partner_ui3d_model then
			partner_ui3d_model:OnRemoveGameObject(obj.gameObject)
		else
			self.ui3d_model:OnRemoveGameObject(obj.gameObject)
		end
	end
end

function RoleModel:SetGoddessModelResInfo(info)
	for k, v in pairs(SceneObjPart) do
		local part = self.draw_obj:GetPart(v)
		if part then
			part:RemoveModel()
		end
	end

	if info ~= nil then
		self:SetRoleResidValue(info.role_res_id or -1)
		self.weapon_res_id = info.weapon_res_id or -1
		self.wing_res_id = info.wing_appeid or -1
	end

	if self.role_res_id ~= -1 then
		self:SetGoddessResid(self.role_res_id)
	end

	if self.weapon_res_id ~= -1 then
		self:SetGoddessWeaponResid(self.weapon_res_id)
	end

	if self.wing_res_id ~= -1 then
		local asset, bundle = ResPath.GetGoddessWingModel(self.wing_res_id)
		self:SetWingAsset(asset, bundle)
	end
end

function RoleModel:GetModelResInfo()
	return self.info
end

-- special_status_table = {ignore_wing, ignore_halo, ignore_weapon, ignore_fazhen, ignore_mantle
-- ignore_waist, ignore_mask, ignore_shouhuan, ignore_tail, ignore_jianzhen, is_preview, ignore_god_or_demon}
function RoleModel:SetModelResInfo(info, special_status_table, callback, action_name, use_eff)
	self.info = info
	self.special_status_table = special_status_table or {}

	if info == nil then return end
	local prof = info.prof
	local sex = info.sex
	if nil == prof or nil == sex then
		return
	end

	self:UpdateAppearance(info, self.special_status_table)
	
	local weapon_res_id = self.weapon_res_id
	if not info.is_not_show_weapon then
		-- self:SetWeaponResid(self.weapon_res_id)
	else
		local part_one = self.draw_obj:GetPart(SceneObjPart.Weapon)
		if part_one then
			part_one:RemoveModel()
		end
		weapon_res_id = 0
	end

	local appe_data = info.appearance or {}
	local role_diy_appearance_data = info.role_diy_appearance or {}
	local extra_role_model_data = {
		prof = prof,
		sex = sex,
        d_face_res = appe_data.default_face_res_id,
        d_hair_res = appe_data.default_hair_res_id,
		d_body_res = appe_data.default_body_res_id,
		weapon_res_id = weapon_res_id,
		animation_name = action_name,
		no_need_do_anim = false,

		eye_size = role_diy_appearance_data.eye_size,
		eye_position = role_diy_appearance_data.eye_position,
		eye_shadow_color = role_diy_appearance_data.eye_shadow_color,

		left_pupil_type = role_diy_appearance_data.left_pupil_type,
		left_pupil_size = role_diy_appearance_data.left_pupil_size,
		left_pupil_color = role_diy_appearance_data.left_pupil_color,

		right_pupil_type = role_diy_appearance_data.right_pupil_type,
		right_pupil_size = role_diy_appearance_data.right_pupil_size,
		right_pupil_color = role_diy_appearance_data.right_pupil_color,

		mouth_size = role_diy_appearance_data.mouth_size,
		mouth_position = role_diy_appearance_data.mouth_position,
		mouth_color = role_diy_appearance_data.mouth_color,

		face_decal_id = role_diy_appearance_data.face_decal_id,
		hair_color = role_diy_appearance_data.hair_color,
		preset_seq = role_diy_appearance_data.preset_seq,
		part_color = appe_data.part_color,
		server_project_index = appe_data.shizhuang_project_id,
    }

	self:SetRoleResid(self.role_res_id, callback, extra_role_model_data, nil, true)
			
	if use_eff then
		self:SetActorConfigPrefabData(ConfigManager.Instance:GetRoleAutoPrefabConfig(sex, prof))
		self.actor_trigger:SetTargetEffectTriggerCustomScale(Vector3(1, 1, 1))
	end

	self:SetWingResid(self.wing_res_id, true)
	self:SetHaloResid(self.halo_res_id)
	self:SetMantleResid(self.mantle_res_id)
	-- self:SetFaZhenResid(self.fazhen_res_id)
	-- self:SetQiLinBiResid(self.qilinbi_res_id, sex)
	self:SetWaistResid(self.waist_res_id)
	self:SetMaskResid(self.mask_res_id)
	self:SetShouHuanResid(self.shouhuan_res_id)
	self:SetTailResid(self.tail_res_id)
	self:SetJianZhenResid(self.jianzhen_res_id)
	self:SetGodOrDemonResid(self.god_or_demon_res_id)
end

function RoleModel:UpdateAppearance(info, special_status_table)
	local prof = info.prof
	local sex = info.sex

	if nil == prof or nil == sex then
		return
	end

	--清空缓存
	self.role_res_id = 0
	self.weapon_res_id = 0
	self.wing_res_id = 0
	self.halo_res_id = 0
	self.baoju_res_id = 0
	self.mantle_res_id = 0
	self.fazhen_res_id = 0
	self.qilinbi_res_id = 0
	self.waist_res_id = 0
	self.mask_res_id = 0
	self.shouhuan_res_id = 0
	self.tail_res_id = 0
	self.jianzhen_res_id = 0
	self.god_or_demon_res_id = 0
	self.foot_trail_res_id = 0


	local wing_index = 0
	local halo_index = 0
	local zhibao_index = 0
	local mantle_index = 0
	local fazhen_index = 0
	-- 先查找时装的武器和衣服

	special_status_table = special_status_table or {}
	local ignore_wing = special_status_table.ignore_wing
	local ignore_halo = special_status_table.ignore_halo
	local ignore_weapon = special_status_table.ignore_weapon
	local ignore_fazhen = special_status_table.ignore_fazhen
	local ignore_mantle = special_status_table.ignore_mantle
	local ignore_waist = special_status_table.ignore_waist
	local ignore_mask = special_status_table.ignore_mask
	local ignore_shouhuan = special_status_table.ignore_shouhuan
	local ignore_tail = special_status_table.ignore_tail
	local ignore_jianzhen = special_status_table.ignore_jianzhen
	local is_preview = special_status_table.is_preview
	local ignore_god_or_demon = special_status_table.ignore_god_or_demon
	
	local appearance = info.appearance
	if appearance == nil then
		local shizhuang_part_list = info.shizhuang_part_list
		if shizhuang_part_list then
			appearance = {fashion_body = shizhuang_part_list[2].use_index, fashion_wuqi = shizhuang_part_list[1].use_index}
		end
	else
		wing_index = appearance.wing_used_imageid or 0
		halo_index = appearance.halo_used_imageid or 0
		zhibao_index = appearance.zhibao_used_imageid or 0
		mantle_index = appearance.shenyi_used_imageid or 0
		fazhen_index = appearance.fazhen_id or -1
	end

	if appearance ~= nil then
		if not ignore_weapon then
		    if nil ~= appearance.shenwu_appeid and 0 ~= appearance.shenwu_appeid then
    			self.weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, appearance.shenwu_appeid)
			elseif nil ~= appearance.fashion_wuqi and 0 ~= appearance.fashion_wuqi then
				self.weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, appearance.fashion_wuqi)
    		else
				self.weapon_res_id = RoleWGData.GetJobWeaponId(info.sex, prof)
			end
		end
		
		self:SetRoleResidValue(ResPath.GetFashionModelId(prof, appearance.fashion_body))

		if not is_preview and appearance.qilinbi and appearance.qilinbi >= 0 then
			self.qilinbi_res_id = appearance.qilinbi
		end
		if appearance.waist and appearance.waist >= 0 and not ignore_waist then
			self.waist_res_id = appearance.waist
		end
		if appearance.mask and appearance.mask >= 0 and not ignore_mask then
			self.mask_res_id = appearance.mask
		end
		if appearance.shouhuan and appearance.shouhuan >= 0 and not ignore_shouhuan then
			self.shouhuan_res_id = appearance.shouhuan
		end
		if appearance.tail and appearance.tail >= 0 and not ignore_tail then
			self.tail_res_id = appearance.tail
		end
		if appearance.jianzhen_appeid and appearance.jianzhen_appeid >= 0 and not ignore_jianzhen then
			self.jianzhen_res_id = appearance.jianzhen_appeid
		end
	end

	-- 查找翅膀
	if wing_index == 0 then
		if info.wing_info then
			wing_index = info.wing_info.used_imageid or 0
		end
	end

	local image_cfg = nil
	if not ignore_wing then
		if appearance and appearance.wing_appeid then
			self.wing_res_id = appearance.wing_appeid
		elseif info.wing_appeid then
			self.wing_res_id = info.wing_appeid
		end
	end

	-- 查找光环
	if halo_index == 0 and not ignore_halo then
		if info.halo_info then
			halo_index = info.halo_info.used_imageid or 0
		end
	end

	if not ignore_halo then
		if appearance and appearance.fashion_guanghuan and appearance.fashion_guanghuan >= 0 then
			self.halo_res_id = appearance.fashion_guanghuan
		end
	end

	-- 查找披风
	if mantle_index == 0 and not ignore_mantle then
		if info.shenyi_info then
			mantle_index = info.shenyi_info.used_imageid or 0
		end
	end
	
	-- 最后查找职业表
	-- if self.role_res_id == 0 then
    --     self:SetRoleResidValue(RoleWGData.GetJobModelId(sex, prof))
    -- end

    if not ignore_weapon and self.weapon_res_id == 0 then
        self.weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
    end

	-- 法阵
	if not ignore_fazhen then
		self.fazhen_res_id = fazhen_index
	end

	if not ignore_god_or_demon then
		if info.god_or_demon_type and info.god_or_demon_grade then
			local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(info.god_or_demon_type, info.god_or_demon_grade) 
			local modle_type = cur_cfg and cur_cfg.modle_type or 0
			local show_model_type_cfg = YinianMagicWGData.Instance:GetModelTypeCfg(info.god_or_demon_type, modle_type)
			self.god_or_demon_res_id =  show_model_type_cfg and show_model_type_cfg.appe_image_id and show_model_type_cfg.appe_image_id or 0
		end
	end
end

function RoleModel:EquipDataChangeListen()
	self:SetModelResInfo(self.info, self.special_status_table)
end

function RoleModel:ClearModel(ignore_main)
	for k, v in pairs(SceneObjPart) do
		if not ignore_main or ignore_main and v ~= SceneObjPart.Main then
			local part = self.draw_obj:GetPart(v)
			if part then
				part:RemoveModel()
			end
		end
	end
end

function RoleModel:ShowAttachPoint(point, state)
	if nil == self.draw_obj then
		return
	end
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	local attach_point = part:GetAttachPoint(point)
	if nil ~= attach_point then
		attach_point.gameObject:SetActive(state)
	end
end

function RoleModel:OnMainRoleApperanceChange(appe_type)
	if appe_type ~= nil and not RoleWGData.IsNeedFlushModelAppeType(appe_type) then
		return
	end
end

----------------------------------------------------------------------------------------


function RoleModel:GetRootNodeTransform()
	return self.root_node_transform
end

function RoleModel:SetIsSupportClip(is_clip)
	if nil ~= self.ui3d_model then
		self.ui3d_model:SetIsSupportClip(is_clip)
	end
end





------------- 天神模型设置接口 -------------
-- is_show_huanhua  如果当前天神已幻化神器，是否显示
-- show_audio 天神展示语音，加载模型完毕后播放，nil为不播放
function RoleModel:SetTianShenModel(appe_image_id, tianshen_index, is_show_huanhua, show_audio, obj_animator, load_callback, weapon_scale)
	-- print_error("SetTianShenModel", appe_image_id, obj_animator)
	self:ClearDoActionDelay()

	is_show_huanhua = is_show_huanhua ~= false
	local bundle, asset = ResPath.GetBianShenModel(appe_image_id)
	local weapon_bundle, weapon_asset, is_weapon_anim = TianShenWGData.Instance:GetTianShenWeapon(tianshen_index, nil, is_show_huanhua, appe_image_id)

	if bundle == nil or asset == nil or weapon_bundle == nil or weapon_asset == nil then
		return
	end

	if bundle then
		local eff_key = ""
		local i, j = string.find(bundle, "model/(.-)/")
		if i and j then
			eff_key = string.sub(bundle, i, j)
		end

		if eff_key and MODEL_EFF[eff_key] then
			self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig(MODEL_EFF[eff_key], asset))
		else
			self:SetActorConfigPrefabData(nil)
		end
	end

	self.wait_sync_anim_type = is_weapon_anim and SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN or nil
	self.draw_obj:ChangeWaitSyncAnimType(self.wait_sync_anim_type)
	
	self.cur_play_anim = obj_animator

	self.draw_obj:SetIsHdTexture(true)
	if not self.is_add_ui_probe then
		self.draw_obj:SetIsLerpProbe(true)
	end

	self.draw_obj:SetLoadComplete(BindTool.Bind(self._OnModelLoaded, self))

	local key = self:GetBundleType(bundle)
	self.camera_setting_key = key
	local camera_setting = self:GetModelCameraSetting(self.camera_type, key, bundle, asset)
    self:UpdateCameraTransform(camera_setting)

	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:CancleLoadInQueue()
	self:FakeHideDelayShow()
	self:ResetUSAdjustmentRootTransform()

	self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, load_callback)
	self:SetWeaponModel(weapon_bundle, weapon_asset, weapon_scale)

	if nil ~= show_audio and "" ~= show_audio then
		AudioManager.PlayAndForget(ResPath.GetTianShenVoice(show_audio))
	end

	self:PlayRoleAction(self.cur_play_anim or SceneObjAnimator.UiIdle, nil, true)
end

function RoleModel:SetShuangShengTianShenModel(appe_image_id, is_not_need_reset)
	local bundle, asset = ResPath.GetShuangShengModelUI(appe_image_id)

	if bundle == nil or asset == nil then
		return
	end

	self.draw_obj:ChangeModel(SceneObjPart.ShuangShengTianShenUI, bundle, asset, function (obj)
		if not is_not_need_reset then
			self:TryPlayShuangShengAction(SceneObjAnimator.Rest)
		end
	end)
end


--======================================  高达  =======================================================
--[[
	高达
	part_info = {
		gundam_seq = 0,
		gundam_body_res = 0, gundam_weapon_res = 0,
		gundam_left_arm_res = 0, gundam_right_arm_res = 0,
		gundam_left_leg_res = 0, gundam_right_leg_res = 0,
		gundam_left_wing_res = 0, gundam_right_wing_res = 0,
	}
--]]
-- 单个武器展示
function RoleModel:SetGundamSingleWeaponResId(gundam_seq, res_id)
	res_id = res_id or 0
	local weapon_seq = math.floor(res_id / 100)
	local bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.WEAPON, res_id)
	self:ResetUSAdjustmentRootTransform()
	self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset)
	self.draw_obj:CrossAction(SceneObjPart.Main, "weapon_idle" .. weapon_seq, false, 0, true)
end

-- no_need_do_anim 不需要默认动画
function RoleModel:SetGundamModel(part_info, action_name, load_callback, no_need_do_anim)
	self:ClearDoActionDelay()
	if not part_info then return end
	local gundam_seq = part_info.gundam_seq
	if not gundam_seq or gundam_seq < 0 then
		return
	end

	gundam_seq = gundam_seq + 1 -- 机甲唯一seq + 1 
	if not part_info.gundam_body_res or part_info.gundam_body_res <= 0 then
		return
	end

	self.cur_play_anim = action_name
	self.wait_sync_anim_type = SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.GUNDAM
	self.draw_obj:ChangeWaitSyncAnimType(self.wait_sync_anim_type)

	self.draw_obj:SetLoadComplete(BindTool.Bind(self._OnModelLoaded, self))
	self.draw_obj:SetIsHdTexture(true)
	if not self.is_add_ui_probe then
		self.draw_obj:SetIsLerpProbe(true)
	end

	local bundle, asset
	if not part_info.gundam_weapon_res or part_info.gundam_weapon_res <= 0 then
		-- self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.Weapon, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
		self:RemoveWeapon()
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.WEAPON, part_info.gundam_weapon_res)
		self.draw_obj:ChangeModel(SceneObjPart.Weapon, bundle, asset)
	end

	if not part_info.gundam_left_arm_res or part_info.gundam_left_arm_res <= 0 then
		-- self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.GundamLArm, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
		self:RemoveGundamLArm()
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.LEFT_HAND, part_info.gundam_left_arm_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamLArm, bundle, asset)
	end

	if not part_info.gundam_right_arm_res or part_info.gundam_right_arm_res <= 0 then
		-- self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.GundamRArm, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
		self:RemoveGundamRArm()
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.RIGHT_HAND, part_info.gundam_right_arm_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamRArm, bundle, asset)
	end

	if not part_info.gundam_left_leg_res or part_info.gundam_left_leg_res <= 0 then
		-- self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.GundamLLeg, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
		self:RemoveGundamLLeg()
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.LEFT_FOOT, part_info.gundam_left_leg_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamLLeg, bundle, asset)
	end

	if not part_info.gundam_right_leg_res or part_info.gundam_right_leg_res <= 0 then
		-- self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.GundamRLeg, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
		self:RemoveGundamRLeg()
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.RIGHT_FOOT, part_info.gundam_right_leg_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamRLeg, bundle, asset)
	end

	if not part_info.gundam_left_wing_res or part_info.gundam_left_wing_res <= 0 then
		-- self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.GundamLWing, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
		self:RemoveGundamLWing()
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.LEFT_WING, part_info.gundam_left_wing_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamLWing, bundle, asset)
	end

	if not part_info.gundam_right_wing_res or part_info.gundam_right_wing_res <= 0 then
		-- self.draw_obj:ChangeWaitSyncAnimPartCache(SceneObjPart.GundamRWing, SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE)
		self:RemoveGundamRWing()
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.RIGHT_WING, part_info.gundam_right_wing_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamRWing, bundle, asset)
	end


	bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.BODY, part_info.gundam_body_res)
	local key = self:GetBundleType(bundle)
	self.camera_setting_key = key
	local camera_setting = self:GetModelCameraSetting(self.camera_type, key, bundle, asset)
    self:UpdateCameraTransform(camera_setting)
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:CancleLoadInQueue()
	self:FakeHideDelayShow()
	self:ResetUSAdjustmentRootTransform()

	self.draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, load_callback)
	if not no_need_do_anim then
		self:PlayRoleAction(self.cur_play_anim or SceneObjAnimator.UiIdle)
	end
end

function RoleModel:RemoveGundamAll()
	self:RemoveGundamLArm(true)
	self:RemoveGundamRArm(true)
	self:RemoveGundamLLeg(true)
	self:RemoveGundamRLeg(true)
	self:RemoveGundamLWing(true)
	self:RemoveGundamRWing(true)
	self:RemoveWeapon(true)
	self:RemoveMain(true)
end

function RoleModel:RemoveGundamLArm(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.GundamLArm, real_remove)
end

function RoleModel:RemoveGundamRArm(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.GundamRArm, real_remove)
end

function RoleModel:RemoveGundamLLeg(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.GundamLLeg, real_remove)
end

function RoleModel:RemoveGundamRLeg(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.GundamRLeg, real_remove)
end

function RoleModel:RemoveGundamLWing(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.GundamLWing, real_remove)
end

function RoleModel:RemoveGundamRWing(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.GundamRWing, real_remove)
end
--======================================  高达 end =======================================================

--======================================  怒气变身 start =======================================================
-- 增加怒气变身动作
function RoleModel:ExecuteAransformationAction(nuqi_type, callback)
    self:RemoveAransformationDelayTime()
    self.cancel_transformation_action = GlobalTimerQuest:AddDelayTimer(function ()
        self:RemoveAransformationDelayTime()
		local bianshen_name_2 = "qiliu"
		local bundle_name, asset_name = ResPath.GetAransformationEffect(bianshen_name_2, nuqi_type)
		self:CreateAransformationEffect(bundle_name, asset_name, nuqi_type)
		-- self:CreateAransformationWingEffect(nuqi_type)
		self.draw_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.UiIdle)
		if callback then
			callback()
		end
	end, 0.4)

end

-- 增加怒气变身动作
function RoleModel:ExecuteAransformationAction2(nuqi_type, callback)
    local anim_name = SceneObjAnimator.transformation 
	local clip_name = SceneObjAnimator.transformation 

    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    if not part or not part.obj then 
        return 
    end

    local anim = part:GetObj().animator
    if not anim then 
        return 
    end

    local clip = anim:GetAnimationClip(clip_name)
    if not clip then 
        return 
    end


    local action_end_time = clip.length or 0.1
    self.draw_obj:CrossAction(SceneObjPart.Main, anim_name)
    local bianshen_name_1 = "bianshen"

    local bundle_name, asset_name = ResPath.GetAransformationEffect(bianshen_name_1, nuqi_type)
    self:CreateAransformationEffect(bundle_name, asset_name, nuqi_type)
    self:RemoveAransformationDelayTime()
    self.cancel_transformation_action = GlobalTimerQuest:AddDelayTimer(function ()
        -- self:TransformationActionFinish()
        self.draw_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)

		local bianshen_name_2 = "qiliu"
		local bundle_name, asset_name = ResPath.GetAransformationEffect(bianshen_name_2, nuqi_type)
		self:CreateAransformationEffect(bundle_name, asset_name, nuqi_type)

        if callback then
            callback()
        end
    end, action_end_time)
end

-- 创建变身特效
function RoleModel:CreateAransformationEffect(bundle, asset, index)
    local foot_trail_point = self.draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
    local uuid = role_vo and role_vo.uuid or 0
    local load_str = string.format("aransformation_ui_effect_%s", uuid)

    if foot_trail_point then
        if self.aransformation_effect_loader == nil then
            self.aransformation_effect_loader = AllocAsyncLoader(self, load_str)
            self.aransformation_effect_loader:SetIsUseObjPool(true)
            self.aransformation_effect_loader:SetIsInQueueLoad(true)
            self.aransformation_effect_loader:SetParent(foot_trail_point)
			self.aransformation_effect_loader:SetLocalPosition(Vector3.zero)
        end

        self.aransformation_effect_loader:Load(bundle, asset, function()
			if self.ui3d_model then
				self.ui3d_model:ResetAllRenders()
			end
		end)
    end
end

-- 创建变身翅膀特效
function RoleModel:CreateAransformationWingEffect(index)
    local bundle, asset = ResPath.GetAransformationEffect("beishi", index)
    local wing_point = self.draw_obj:GetAttachPoint(AttachPoint.Wing)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
    local uuid = role_vo and role_vo.uuid or 0
    local load_str = string.format("aransformation_ui_wing_effect_%s", uuid)

    if wing_point then
        if self.aransformation_wing_effect_loader == nil then
            self.aransformation_wing_effect_loader = AllocAsyncLoader(self, load_str)
            self.aransformation_wing_effect_loader:SetIsUseObjPool(true)
            self.aransformation_wing_effect_loader:SetIsInQueueLoad(true)
            self.aransformation_wing_effect_loader:SetParent(wing_point)
        end

        self.aransformation_wing_effect_loader:Load(bundle, asset)
    end
end

-- 移除变身特效
function RoleModel:RemoveAransformationEffect()
	if self.aransformation_effect_loader then
        self.aransformation_effect_loader:Destroy()
		self.aransformation_effect_loader = nil
	end

    if self.aransformation_wing_effect_loader then
        self.aransformation_wing_effect_loader:Destroy()
		self.aransformation_wing_effect_loader = nil
	end
end

-- 移除定时器
function RoleModel:RemoveAransformationDelayTime()
    if self.cancel_transformation_action then
        GlobalTimerQuest:CancelQuest(self.cancel_transformation_action)
        self.cancel_transformation_action = nil
    end
end
--======================================  怒气变身 end =======================================================



function RoleModel:SetIsNeedUiProbe(flag)
	self.is_add_ui_probe = flag
end

function RoleModel:SetIsLerpProbe(is_lerp)
	if self.draw_obj ~= nil then
		self.draw_obj:SetIsLerpProbe(is_lerp, true)
	end
end

function RoleModel:SetPartAnimatorState(part, bool)
	part = part or SceneObjPart.Main
	local obj_part = self.draw_obj:GetPart(part)
	if obj_part then
		obj_part:SetAnimatorEnabled(bool)
	end
end

function RoleModel:SetIsForceMaxLod(is_max)
	if self.draw_obj ~= nil then
		self.draw_obj:SetIsForceMaxLod(is_max)
	end
end









--=============================================================================
--===========================   动作  ======================================
--=============================================================================
function RoleModel:SetFirstFrameBoolName(name, change_immediately)
	-- print_error("----SetFirstFrameBoolName------", name)
	local old_name = self.first_frame_bool_name
	self.first_frame_bool_name = name
	if change_immediately and old_name and name ~= old_name then
		if self.draw_obj and not self.draw_obj:IsDeleted() then
			self.draw_obj:SetActionBool(SceneObjPart.Main, old_name, false)
		end
	end
end

function RoleModel:GetFirstFrameBoolName()
	return self.first_frame_bool_name
end

function RoleModel:ViewOpenCallBack()
	local first_frame_name = self:GetFirstFrameBoolName()
	if first_frame_name then
		self:SetBool(first_frame_name, true, SceneObjPart.Main)
		self:SetBool(first_frame_name, true, SceneObjPart.Weapon)
	end
end

function RoleModel:SetIsAlwaysAnimate(part, bool)
	local draw_part = self:GetDrawPart(part)
	if draw_part then
		draw_part:SetIsAlwaysAnimate(bool)
	end
end

-- 假隐藏，延迟展示  临时处理模型第一次展示动画过渡问题
function RoleModel:FakeHideDelayShow()
	-- if not self.draw_obj then
	-- 	return
	-- end

	-- self.draw_obj:SetScale(0, 0, 0)
	-- GlobalTimerQuest:AddDelayTimer(function ()
	-- 	if not self.draw_obj then
	-- 		return
	-- 	end

	-- 	self.draw_obj:TryResetScale()
	-- end, 0.2)
end

--设置下一帧就播放动画
function RoleModel:SetOneFrameBool(name, state)
	GlobalTimerQuest:AddDelayTimer(function()
		if self.draw_obj and not self.draw_obj:IsDeleted() then
			self.draw_obj:SetActionBool(SceneObjPart.Main, name, state)
		end
	end, 0)
end

function RoleModel:SetTrigger(name, is_delay)
	if is_delay == nil then
		is_delay = true
	end

	if self.draw_obj and not self.draw_obj:IsDeleted() then
		local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
		if main_part ~= nil then
			if is_delay then
				GlobalTimerQuest:AddDelayTimer(function() self:_SetTrigger(main_part, name) end, 0.1)
			else
				self:_SetTrigger(main_part, name)
			end
		end
	end
end

--外部不要调用
function RoleModel:_SetTrigger(main_part, name)
	main_part:SetTrigger(name)
	self:PlayActionEffect(name)
end

function RoleModel:SetBool(name, state, part)
	part = part or SceneObjPart.Main

	if self.draw_obj and not self.draw_obj:IsDeleted() then
		local draw_part = self.draw_obj:GetPart(part)
		if draw_part then
			GlobalTimerQuest:AddDelayTimer(function() draw_part:SetBool(name, state) end, 0.1)
		end
	end
end

function RoleModel:SetInteger(key, value, part)
	if self.draw_obj and not self.draw_obj:IsDeleted() then
		part = part or SceneObjPart.Main
		local part_obj = self.draw_obj:GetPart(part)
		if part_obj then
			GlobalTimerQuest:AddDelayTimer(function() part_obj:SetInteger(key, value) end, 0.1)
		end
	end
end

function RoleModel:OnStartDoActionCallBack()
	-- print_error("-----call_back-----", self.start_do_action_call_back == nil)
	if self.start_do_action_call_back then
		self.start_do_action_call_back()
		self.start_do_action_call_back = nil
	end

	if self.cur_play_anim and self.actor_trigger and self.actor_trigger.prefab_data then
		self:PlayActionEffect(self.cur_play_anim)
	end
end

-- 播模型上一次的动画
function RoleModel:PlayLastAction(check_same_action)
	self:PlayRoleAction(self.cur_play_anim, check_same_action)
end

-- 清除部位的动作缓存 解决 part:CrossFade 上次action_name 不生效问题
function RoleModel:ClearPartCrossFadeAnimCache(part)
	part = part or SceneObjPart.Main
	if self.draw_obj then
		local part_obj = self.draw_obj:GetPart(part)
		if part_obj then
			part_obj:ClearCachePlayAnim()
		end
	end
end

function RoleModel:ClearDoActionDelay()
	self.start_do_action_call_back = nil
	self:RemoveActionLoopTime()
	self:RemoveActionDelayTime()
	self:RemoveTianShenShuangShengActionDelayTime()
	self.cache_part_scale_list = nil
end

function RoleModel:RemoveActionLoopTime()
	if self.cancel_quest_of_action_loop then
		GlobalTimerQuest:CancelQuest(self.cancel_quest_of_action_loop)
		self.cancel_quest_of_action_loop = nil
	end
end

-- 循环播放
-- wait_interval 当前动作播完 等待下次播放的时间
function RoleModel:DoLoopRoleAction(action_name, is_ui_idel, wait_interval)
	self:RemoveActionLoopTime()
	self:RemoveActionDelayTime()

	if not self.draw_obj or not action_name then
		return
	end

	self.cur_play_anim = action_name
	local anim_clip_name = action_name
	local idel_ani
	local idel_ani_time = 0

	if is_ui_idel then
		idel_ani = SceneObjAnimator.UiIdle
		idel_ani_time = self:GetActionTimeByName("ui_idle")
	else
		idel_ani = SceneObjAnimator.Idle
		idel_ani_time = self:GetActionTimeByName("idle")
	end

	local action_end_time = self:GetActionTimeByName(anim_clip_name)
	wait_interval = wait_interval or idel_ani_time

	self.draw_obj:CrossAction(SceneObjPart.Main, action_name, false, 0, true)

	-- 衔接待机
	if wait_interval > 1 then
		-- 让动作平滑点
		action_end_time = action_end_time - 0.01
		if action_end_time > 0 then
			self.cancel_quest_of_action = GlobalTimerQuest:AddDelayTimer(
				function ()
					if self.draw_obj then
						self:PlayRoleAction(idel_ani, nil)
					end
				end,
				action_end_time
			)
		end
	end

	-- 循环
	action_end_time = action_end_time + wait_interval - 0.01
	if action_end_time > 0 then
		self.cancel_quest_of_action_loop = GlobalTimerQuest:AddDelayTimer(
			function ()
				self:DoLoopRoleAction(action_name, is_ui_idel, wait_interval)
			end,
			action_end_time
		)
	end
end


function RoleModel:PlayRoleAction(action_name, check_same_action)
	if not self.draw_obj or not action_name then
		return
	end

	-- print_error("-----PlayRoleAction----", action_name, check_same_action)
	self.cur_play_anim = action_name
	local is_need_to_custom_anim = false
	local other_anim_clip_name
	local need_set_bool = false
	local bool_name
	local bool_state = false
	if action_name == SceneObjAnimator.Rest then
		is_need_to_custom_anim = true
	elseif action_name == SceneObjAnimator.FallRest then
		is_need_to_custom_anim = true
		other_anim_clip_name = SceneObjAnimator.Walk
	elseif action_name == SceneObjAnimator.transformation then
		is_need_to_custom_anim = true
	elseif action_name == SceneObjAnimator.UiIdle then
		need_set_bool = true
		bool_state = true
	end

	-- print_error("---PlayRoleAction---", action_name, is_need_to_custom_anim, other_anim_clip_name)
	self.start_do_action_call_back = function ()
		if action_name and is_need_to_custom_anim then
			self:CrossFadeToOtherAnim(action_name, other_anim_clip_name)
		end
	end

	-- print_error("-----PlayRoleAction----", need_set_bool, bool_name)
	if need_set_bool and bool_name then
		self.draw_obj:SetActionBool(SceneObjPart.Main, bool_name, bool_state)
	end
	self.draw_obj:CrossAction(SceneObjPart.Main, action_name, check_same_action, 0, true)
end

-- 尝试播放一下双生天神动作
function RoleModel:TryPlayShuangShengAction(animation)
	self.draw_obj:CrossAction(SceneObjPart.ShuangShengTianShenUI, animation)
	local action_end_time = self:GetActionTimeByName(SceneObjAnimator.D_Rest)
	self:RemoveTianShenShuangShengActionDelayTime()
	
	-- 让动作平滑点
	action_end_time = action_end_time - 0.01
	if action_end_time > 0 then
		self.cancel_quest_of_shuangsheng = GlobalTimerQuest:AddDelayTimer(
			function ()
				if self.draw_obj then
					self.draw_obj:CrossAction(SceneObjPart.ShuangShengTianShenUI, SceneObjAnimator.UiIdle)
				end
			end,
			action_end_time
		)
	end
end

-- 尝试修改一下对应part大小
-- 建议放在回调之后 part_root:GetIsLoaded()这个方法只会检测obj的状态
-- 当从一个已有的模型切换形象会直接是真的，导致改变大小在前，切换形象又会重置回1，所以使用这个方法请在改变某个回调中调用
function RoleModel:TryChangePartScale(part, part_scale)
	local part_root = self.draw_obj:GetPart(part)
	if part_root:GetIsLoaded() then
		part_root:SetPartScale(part_scale, part_scale, part_scale)
	end
end

-- 尝试修改一下对应part大小
function RoleModel:CallChangePartScale(part)
	if not self.cache_part_scale_list then
		return
	end

	if not self.cache_part_scale_list[part] then
		return
	end

	local part_root = self.draw_obj:GetPart(part)
	if part_root:GetIsLoaded() then
		local temp_scale = self.cache_part_scale_list[part]
		part_root:SetPartScale(temp_scale, temp_scale, temp_scale)
	end
end


-- 尝试修改一下对应part大小
function RoleModel:TryResetChangePartScale(part)
	local part_root = self.draw_obj:GetPart(part)
	if part_root:GetIsLoaded() then
		part_root:ReSetPartScale()
	end
end

function RoleModel:PlayRoleShowAction()
	self:PlayRoleAction(SceneObjAnimator.Rest)
end

-- 怪物 boss 动作
function RoleModel:PlayMonsterAction(is_need_play_ui_idle)
	-- print_error("-----PlayMonsterAction----", SceneObjAnimator.Rest)
	is_need_play_ui_idle = is_need_play_ui_idle ~= false
	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	self.cur_play_anim = SceneObjAnimator.Rest
	if main_part:GetIsLoaded() then
		self.draw_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.Rest)

		if is_need_play_ui_idle then
			self:CrossFadeToOtherAnim(SceneObjAnimator.D_Rest)
		end
	else
		self.need_wait_part = SceneObjPart.Main
		self.load_complete2 = BindTool.Bind(self.PlayMonsterAction, self, is_need_play_ui_idle)
	end
end

-- 护送的
function RoleModel:PlayHSRoleAction()
	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	self.cur_play_anim = SceneObjAnimator.Rest
	if main_part:GetIsLoaded() then
		main_part:CrossFade(SceneObjAnimator.Rest)
		self:CrossFadeToOtherAnim(SceneObjAnimator.Rest)
	else
		self.need_wait_part = SceneObjPart.Main
		self.load_complete2 = BindTool.Bind(self.PlayHSRoleAction, self)
	end
end

--移除 过渡到ui_idel 计算器
function RoleModel:RemoveActionDelayTime()
	if self.cancel_quest_of_action then
		GlobalTimerQuest:CancelQuest(self.cancel_quest_of_action)
		self.cancel_quest_of_action = nil
	end
end

--移除 过渡到ui_idel 计算器
function RoleModel:RemoveTianShenShuangShengActionDelayTime()
	if self.cancel_quest_of_shuangsheng then
		GlobalTimerQuest:CancelQuest(self.cancel_quest_of_shuangsheng)
		self.cancel_quest_of_shuangsheng = nil
	end
end

-- 过渡到其他动画
function RoleModel:CrossFadeToOtherAnim(first_animation, other_animation, callback)
	local action_end_time = self:GetActionTimeByName(first_animation)
	self:RemoveActionDelayTime()
	
	-- 让动作平滑点
	action_end_time = action_end_time - 0.01
	-- action_end_time = 1000
	-- print_error("-----过渡到其他动画----", first_animation, other_animation, action_end_time)
	if action_end_time > 0 then
		other_animation = other_animation or SceneObjAnimator.UiIdle
		self.cancel_quest_of_action = GlobalTimerQuest:AddDelayTimer(
			function ()
				if self.draw_obj and other_animation then
					self:PlayRoleAction(other_animation, nil)
				end

				if callback then
					callback()
				end
			end,
			action_end_time
		)
	end
end

-- 获取动作时间
function RoleModel:GetActionTimeByName(action_name)
	local main_obj = self:GetDrawPart(SceneObjPart.Main).obj
	if nil == main_obj or IsNil(main_obj.gameObject) then
		return 0
	end

	local anim = main_obj.animator
	if not anim or not anim.runtimeAnimatorController then
		return 0
	end

	local clip = anim:GetAnimationClip(action_name)
	if clip then
		return clip.length or 0.1
	end

	return 0
end

function RoleModel:PlayStartAction(animator_status_name)
	if self.draw_obj then
		self.draw_obj:CrossAction(SceneObjPart.Main, animator_status_name)
	end

	-- 这里不用切回到UI_Idel
	self.need_wait_part = SceneObjPart.Main
	self.load_complete2 = nil
	-- self.load_complete2 = BindTool.Bind(self.CrossFadeToOtherAnim, self, animator_status_name)
end

--播放坐骑动画
function RoleModel:PlayMountAction()
	self:SetTrigger(SceneObjAnimator.Rest, false)
end


function RoleModel:PlaySoulAction()
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	self.cur_play_anim = SceneObjAnimator.Rest
	if main_part.obj then
		main_part:CrossFade(SceneObjAnimator.Rest, nil, 0)
	else
		self.need_wait_part = SceneObjPart.Main
		self.load_complete2 = BindTool.Bind(self.PlaySoulAction, self)
    end
end

-- 灵剑动作
function RoleModel:PlayLingJianAction()
	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	self.cur_play_anim = SceneObjAnimator.Rest
	if main_part:GetIsLoaded() then
		self.draw_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.Rest)
		self:CrossFadeToOtherAnim(SceneObjAnimator.D_Rest)
	else
		self.need_wait_part = SceneObjPart.Main
		self.load_complete2 = BindTool.Bind(self.PlayLingJianAction, self)
	end
end


function RoleModel:PlaySitAction()
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	self.cur_play_anim = SceneObjAnimator.Sit_Idle
	if main_part.obj then
		main_part:CrossFade(SceneObjAnimator.Sit_Idle, false, 0)
	else
		self.need_wait_part = SceneObjPart.Main
		self.load_complete2 = BindTool.Bind(self.PlaySitAction, self)
    end
end

--播放羽翼动画
function RoleModel:PlayWingAction()
	self:SetTrigger(SceneObjAnimator.Rest, false)
end

--播放剑阵动画
function RoleModel:PlayJianZhenAction()
	self:SetTrigger(SceneObjAnimator.Rest, false)
end

function RoleModel:SetActorConfigPrefabData(data)
	if self.actor_trigger then
		self.actor_trigger:SetPrefabData(data,true)
		if nil ~= self.ui3d_model then
			self:SetActorTriggerUI3D(self.ui3d_model)
		end
	end
end

function RoleModel:SetActorTriggerUI3D(ui3d_model)
	if self.actor_trigger then
		self.actor_trigger:SetUI3dModel(ui3d_model)
	end
end

-- 播放技能特效
function RoleModel:PlayActionEffect(anim_name)
	if self.actor_trigger then
		-- 先移除上次的特效
		self.actor_trigger:RemoveEffectsAndSounds()

		if self.draw_obj then
			local main_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
			if main_obj and not IsNil(main_obj.gameObject) then
				self.actor_trigger:OnAnimatorEvent(nil, nil, main_obj, main_obj.transform, anim_name.."/begin")
			end
		end
	end
end

function RoleModel:RemoveActorTriggerEffectsAndSounds()
	if self.actor_trigger then
		self.actor_trigger:RemoveEffectsAndSounds()
	end
end

function RoleModel:PlayIdleAni()
	self:PlayRoleAction(SceneObjAnimator.UiIdle)
end
--=============================================================================
--===========================   动作 end  ======================================
--=============================================================================

--======================================  兽魂环 start =====================================================
function RoleModel:SoulRingTypeToSoulRingPart(soul_ring_type)
	local soul_ring_part 

	if soul_ring_type == SOUL_RING_TYPE.SoulRing1 then
		soul_ring_part = SceneObjPart.SoulRing1
	elseif soul_ring_type == SOUL_RING_TYPE.SoulRing2 then
		soul_ring_part = SceneObjPart.SoulRing2
	elseif soul_ring_type == SOUL_RING_TYPE.SoulRing3 then
		soul_ring_part = SceneObjPart.SoulRing3
	elseif soul_ring_type == SOUL_RING_TYPE.SoulRing4 then
		soul_ring_part = SceneObjPart.SoulRing4
	elseif soul_ring_type == SOUL_RING_TYPE.SoulRing5 then
		soul_ring_part = SceneObjPart.SoulRing5
	elseif soul_ring_type == SOUL_RING_TYPE.SoulRing6 then
		soul_ring_part = SceneObjPart.SoulRing6
	elseif soul_ring_type == SOUL_RING_TYPE.SoulRing7 then
		soul_ring_part = SceneObjPart.SoulRing7
	else
		soul_ring_part = SceneObjPart.SoulRing8
	end

	return soul_ring_part
end

--[[
	soul_ring_info = {
		[0] = {	shenshou_id = target_shenshou_id,
				soul_ring_effect = "eff_hunhuan1_bai",
				quality = 1,
				is_act = false,
				is_up = false},
		[1] = ,
		[2] = ,
		[3] = ,
		[4] = ,
		[5] = ,
		[6] = ,
		[7] = ,
	}
]]
function RoleModel:SetTotalSoulRingResid(soul_ring_info, need_show_tween, num)
	local bundle, asset

	self.show_soul_ring_num = (num and num > 0) and num or 0

	for i = 0, 7 do
		local res_id = (soul_ring_info[i] or {}).soul_ring_effect or ""

		if not res_id or "" == res_id then
			local scene_part = self:SoulRingTypeToSoulRingPart(i)
			self:RemoveSoulRing(scene_part)
		else
			if need_show_tween then
				-- ReDelayCall(self, function ()
				-- 	self:SetSoulRingResid(i, res_id)
				-- end, i * 0.2, "SetTotalSoulRingResid" .. i)
				self:SingleSoulRingShowAnim(i, res_id, (i + 1) * 0.3, 0.6)
			else
				local is_act = (soul_ring_info[i] or {}).is_act or false
				if is_act then
					self:SingleSoulRingShowAnim(i, res_id, 0, 0.6)
					return
				end

				local is_up = (soul_ring_info[i] or {}).is_up or false
				if is_up then
					self:SoulRingUpAnim(i, res_id)
				else
					self:SetSoulRingResid(i, res_id)
				end
			end
		end
	end
end

-- 魂环顺序出场动画
function RoleModel:SingleSoulRingShowAnim(soul_ring_type, soul_ring_res_id, delay_time, show_time)
	local callback = function()
		local cur_part = self:GetSoulRingPart(soul_ring_type)

		if cur_part then
			cur_part:SetPartScale(0, 0, 0)
			cur_part:SetPosition(Vector3(0, 0, 0))
			local obj = cur_part:GetObj()
			local trans = nil

			if obj ~= nil then
				trans = obj.transform
			end

			if trans then
				ReDelayCall(self, function ()
					
					local soul_ring_pos_cfg = ShenShouWGData.Instance:GetSoulRingPosCfg(soul_ring_type, self.show_soul_ring_num)

					if not self.sequence_tween then
						self.sequence_tween = {}
					end

					if self.sequence_tween[soul_ring_type] then
						self.sequence_tween[soul_ring_type]:Kill()
						self.sequence_tween[soul_ring_type] = nil
					end

					self.sequence_tween[soul_ring_type] = DG.Tweening.DOTween.Sequence()
		
					local scale = soul_ring_pos_cfg.scale or -1
					if scale > 0 then
						self.sequence_tween[soul_ring_type]:Join(trans:DOScale(Vector3(scale, scale, scale), show_time))
					end

					local pos = soul_ring_pos_cfg.pos
					if not IsEmptyTable(pos) then
						self.sequence_tween[soul_ring_type]:Join(trans:DOLocalMove(Vector3(pos[1], pos[2], pos[3]), show_time))
					end
				end, delay_time, "SetTotalSoulRingResid" .. soul_ring_type)
			end
		end
	end

	self:SetSoulRingResid(soul_ring_type, soul_ring_res_id, callback)
end

-- 设置单个魂环数据
function RoleModel:SetSoulRingResid(soul_ring_type, soul_ring_res_id, callback)
	if soul_ring_type == nil or soul_ring_res_id == nil then
		return
    end

	local scene_part = self:SoulRingTypeToSoulRingPart(soul_ring_type)
	local bundle, asset = ResPath.GeHunHuanEffect(soul_ring_res_id)
	self.draw_obj:ChangeModel(scene_part, bundle, asset, function ()
		local cur_part = self:GetSoulRingPart(soul_ring_type)

		if nil ~= cur_part then
			local soul_ring_pos_cfg = ShenShouWGData.Instance:GetSoulRingPosCfg(soul_ring_type, self.show_soul_ring_num)

			if not IsEmptyTable(soul_ring_pos_cfg) then
				local scale = soul_ring_pos_cfg.scale or -1
				if scale > 0 then
					cur_part:SetPartScale(scale, scale, scale)
				end
	
				local pos = soul_ring_pos_cfg.pos
				if not IsEmptyTable(pos) then
					cur_part:SetPosition(Vector3(pos[1], pos[2], pos[3]))
				end
	
				local rotation_data = soul_ring_pos_cfg.rotation
				if not IsEmptyTable(rotation_data) then
					cur_part:SetRotation(Vector3(rotation_data[1], rotation_data[2], rotation_data[3]))
				end
			end

			-- if not IsEmptyTable(soul_ring_cfg) then
			-- 	if nil ~= soul_ring_cfg.scale then
			-- 		cur_part:SetPartScale(soul_ring_cfg.scale, soul_ring_cfg.scale, soul_ring_cfg.scale)
			-- 	end

			-- 	local pos_data = ShenShouWGData.Instance:GetSoulRingPosCache(soul_ring_type)
			-- 	if not IsEmptyTable(pos_data) then
			-- 		cur_part:SetPosition(Vector3(pos_data[1], pos_data[2], pos_data[3]))
			-- 	end

			-- 	local rotation_data = ShenShouWGData.Instance:GetSoulRingRotationCache(soul_ring_type)
			-- 	if not IsEmptyTable(rotation_data) then
			-- 		cur_part:SetRotation(Vector3(rotation_data[1], rotation_data[2], rotation_data[3]))
			-- 	end
			-- end
		end

		if callback then
			callback()
		end
	end)
end

function RoleModel:SoulRingUpAnim(soul_ring_type, new_res_id)
	local cur_part_trans = self:GetSoulRingPart(soul_ring_type)

	if nil == cur_part_trans then
		return
	end

	local obj = cur_part_trans:GetObj()
	if not obj then
		return
	end

	local soul_ring_pos_cfg = ShenShouWGData.Instance:GetSoulRingPosCfg(soul_ring_type, self.show_soul_ring_num)
	if not IsEmptyTable(soul_ring_pos_cfg) then
		local trans = obj.transform
		local scale = soul_ring_pos_cfg.scale
		local amin_scale = scale * 0.1

		trans:DOScale(Vector3(amin_scale, amin_scale, amin_scale), 0.3):OnComplete(function ()
			self:SetSoulRingResid(soul_ring_type, new_res_id)
		end)
	end
end

function RoleModel:RemoveSoulRing(soul_ring_type, real_remove)
	local scene_part = self:SoulRingTypeToSoulRingPart(soul_ring_type)
	self.draw_obj:RemoveModel(scene_part, real_remove)
end

function RoleModel:RemoveAllSoulRing(real_remove)
	for i = 1, 8 do
		self:RemoveSoulRing(i, real_remove)
	end

	self.show_soul_ring_num = nil
end

function RoleModel:GetSoulRingPart(soul_ring_type)
	local scene_part = self:SoulRingTypeToSoulRingPart(soul_ring_type)
	return self.draw_obj:TryGetPart(scene_part)
end

-- 选中魂环
function RoleModel:SetSoulRingSelectModel(soul_ring_seq, res_id)
	if nil == res_id or "" == res_id then
		self:RemoveSoulRingSelectModel(true)
		return
	end

	local bundle, asset = ResPath.GeHunHuanEffect(res_id)
	self.draw_obj:ChangeModel(SceneObjPart.SoulRingSelect, bundle, asset, function ()
		local cur_part = self.draw_obj:TryGetPart(SceneObjPart.SoulRingSelect)
		local soul_ring_pos_cfg = ShenShouWGData.Instance:GetSoulRingPosCfg(soul_ring_seq, self.show_soul_ring_num)

		if not IsEmptyTable(soul_ring_pos_cfg) and nil ~= cur_part then
			local select_scale = soul_ring_pos_cfg.select_scale or -1
			if select_scale > 0 then
				cur_part:SetPartScale(select_scale, select_scale, select_scale)
			end

			local pos = soul_ring_pos_cfg.pos
			if not IsEmptyTable(pos) then
				cur_part:SetPosition(Vector3(pos[1], pos[2], pos[3]))
			end

			local rotation_data = soul_ring_pos_cfg.rotation
			if not IsEmptyTable(rotation_data) then
				cur_part:SetRotation(Vector3(rotation_data[1], rotation_data[2], rotation_data[3]))
			end
		end

		-- local soul_ring_cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(soul_ring_type)

		-- if not IsEmptyTable(soul_ring_cfg) then
		-- 	if nil ~= soul_ring_cfg.select_scale then
		-- 		cur_part:SetPartScale(soul_ring_cfg.select_scale, soul_ring_cfg.select_scale, soul_ring_cfg.select_scale)
		-- 	end

		-- 	local pos_data = ShenShouWGData.Instance:GetSoulRingPosCache(soul_ring_type)
		-- 	if not IsEmptyTable(pos_data) then
		-- 		cur_part:SetPosition(Vector3(pos_data[1], pos_data[2], pos_data[3]))
		-- 	end

		-- 	local rotation_data = ShenShouWGData.Instance:GetSoulRingRotationCache(soul_ring_type)
		-- 	if not IsEmptyTable(rotation_data) then
		-- 		cur_part:SetRotation(Vector3(rotation_data[1], rotation_data[2], rotation_data[3]))
		-- 	end
		-- end
	end)
end

function RoleModel:RemoveSoulRingSelectModel(real_remove)
	self.draw_obj:RemoveModel(SceneObjPart.SoulRingSelect, real_remove)
end
--======================================  兽魂环 end =======================================================
