--返利活动，包括七日返利和直购返利
--4545 连续充值
RebateActivityView = RebateActivityView or BaseClass(SafeBaseView)

function RebateActivityView:__init()
    self.view_style = ViewStyle.Full

    self:SetMaskBg(false, false)
    self:AddViewResource(0, "uis/view/rebate_activity_ui_prefab", "layout_rebate_bg")
    self:AddViewResource(TabIndex.rebate_activity_2250, "uis/view/rebate_activity_ui_prefab", "layout_day_rebate")
    self:AddViewResource(TabIndex.rebate_activity_2052, "uis/view/rebate_activity_ui_prefab", "layout_recharge_rebate")
    self:AddViewResource(TabIndex.rebate_activity_2251, "uis/view/rebate_activity_ui_prefab",
        "rebate_lianxu_recharge_view")
    self:AddViewResource(0, "uis/view/rebate_activity_ui_prefab", "VerticalTabbar")
    self:AddViewResource(0, "uis/view/rebate_activity_ui_prefab", "layout_common_panel")
    self:AddViewResource({ TabIndex.rebate_activity_2250 }, "uis/view/rebate_activity_ui_prefab", "rebate_view_time")

    self.default_index = TabIndex.rebate_activity_2250
    self.is_safe_area_adapter = true
end

function RebateActivityView:ReleaseCallBack()
    if nil ~= self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if CountDownManager.Instance:HasCountDown("rebate_activity_common_count_down") then
        CountDownManager.Instance:RemoveCountDown("rebate_activity_common_count_down")
    end

    self.tabbar_loading = false
    self:ReleaseDayView()
    self:ReleaseLXRechargeView()
    self:ReleseRechargeView()
end

function RebateActivityView:OpenCallBack()
    self:SetTabSate()
end

function RebateActivityView:LoadCallBack()
    self.activity_cfg = RebateActivityWGData.Instance:GetActivityAllCfg()
    if not self.tabbar then
        local tab_index_name, remind_tab = RebateActivityWGData.Instance:GetOperationViewInfo()
        self.remind_tab = remind_tab
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarInfo, self))
        self.tabbar:Init(tab_index_name, nil, "uis/view/rebate_activity_ui_prefab", nil, remind_tab, VerRebateItemRender)
        self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
    end
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
    end

    self.node_list.btn_close.button:AddClickListener(BindTool.Bind(self.Close, self))
    self.node_list.btn_rule.button:AddClickListener(BindTool.Bind(self.ShowRuleContent, self))
end

function RebateActivityView:Update(now_time, elapse_time)
    local show_index = self:GetShowIndex()
    if show_index == TabIndex.rebate_activity_2251 then
        self:LXRCUpdate(now_time, elapse_time)
    end
end

function RebateActivityView:SetRuleInfo(rule_content, rule_title)
    self.rule_content = rule_content
    self.rule_title = rule_title
end

function RebateActivityView:ClearRuleInfo()
    self.rule_content = nil
    self.rule_title = nil
end

function RebateActivityView:ShowRuleContent()
    if not self.rule_content or not self.rule_title then
        print_error("rule_content 或者 rule_title 是nil,  赶紧查直冲活动调了 SetRuleInfo 的地方")
        return
    elseif self.rule_content == "" or self.rule_title == "" then
        print_error("rule_content 或者 rule_title 赋了空， 赶紧查直冲活动调了 SetRuleInfo 的地方")
        return
    end

    RuleTip.Instance:SetContent(self.rule_content, self.rule_title)
end

--tabbar是否加载完毕
function RebateActivityView:SetTabbarInfo()
    self.tabbar_loading = true
    self:SetTabSate()
end

--按钮的显示和隐藏
function RebateActivityView:SetTabSate()
    if nil == self.tabbar or not self.tabbar_loading then
        return
    end
    -- 这里边的条件包含服务器的开启状态和你注册的开启条件，其他特殊处理你酌情来
    local num = 0
    for k, v in ipairs(self.activity_cfg) do
        local tab_index = v.rank_id * 10
        local is_open = RebateActivityWGData.Instance:GetActivityState(v.activity_type)
        if is_open then
            num = num + 1
        end

        self.tabbar:SetToggleVisible(tab_index, is_open) --is_open or false)
    end
    local ver_cell_list = self.tabbar:GetVerCellList()
    if nil == ver_cell_list then
        return
    end
    local index = 0
    if num > 0 then
        for k, v in pairs(ver_cell_list) do
            if v:GetView() and v:GetView().gameObject.activeInHierarchy then
                index = index + 1
                v:SetTailShow(index == num)
                v:SetUpLineShow(index == 1)
            end
        end
    end
end

function RebateActivityView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if "all" == k then
            if index == TabIndex.rebate_activity_2250 then     --
                self:FlushDayView()
            elseif index == TabIndex.rebate_activity_2052 then --
                self:FlushRechargeView()
            elseif index == TabIndex.rebate_activity_2251 then --连续充值
                local to_ui_param = v.to_ui_param and tonumber(v.to_ui_param)
                if to_ui_param then
                    if self.node_list and self.node_list["lxcz_recharge_btn_" .. to_ui_param] then
                        self.node_list["lxcz_recharge_btn_" .. to_ui_param].toggle.isOn = true
                        self:LXRCOnClickToggle(to_ui_param)
                    end
                end
                self:FlushLXRechargeView(true)
            end
        elseif "rebate_day_play_xianyu" == k then
            -- self:PlayXianyuEffect()
        end
    end
end

-- 计算要显示的index（可重写）
function RebateActivityView:CalcShowIndex()
    return RebateActivityWGData.Instance:GetOneOpenTabIndex()
end

function RebateActivityView:CloseCallBack()
    self.leichong_limit_jump = false
end

function RebateActivityView:LoadIndexCallBack(index)
    if index == TabIndex.rebate_activity_2250 then
        self:InitDayView()
    elseif index == TabIndex.rebate_activity_2052 then
        self:InitRechargeView()
    elseif index == TabIndex.rebate_activity_2251 then --连续充值
        self:InitLXRechargeView()
    end
end

function RebateActivityView:ShowIndexCallBack(index)
    if index == TabIndex.rebate_activity_2250 then
        local pos = Vector2.zero
        self:SetRemainTimePos(pos)
        self:SetActRemainTime(index)
    end
    if self.node_list["btn_rule"] then
        self.node_list["btn_rule"]:SetActive(index ~= TabIndex.rebate_activity_2052 and
        index ~= TabIndex.rebate_activity_2251)
    end
    if index == TabIndex.rebate_activity_2250 then
        self:ShowIndexDayView()
    elseif index == TabIndex.rebate_activity_2052 then
        self:ShowIndexRechargeView()
    elseif index == TabIndex.rebate_activity_2251 then --连续充值
        ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.RebateActivityView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LIANXUCHONGZHI)
        self:ShowIndexLXRechargeView()
    end
end

--设置活动剩余时间 index: tab_index , end_time:活动结束时间戳
function RebateActivityView:SetActRemainTime(index, end_time)
    local act_end_time = 0
    if end_time then
        act_end_time = end_time
    else
        local activity_type = RebateActivityWGData.Instance:GetCurSelectActivityType(index)
        act_end_time = RebateActivityWGData.Instance:GetActivityInValidTime(activity_type)
    end

    if CountDownManager.Instance:HasCountDown("rebate_activity_common_count_down") then
        CountDownManager.Instance:RemoveCountDown("rebate_activity_common_count_down")
    end

    if act_end_time ~= 0 then
        self.node_list.remain_time_root:SetActive(true)
        local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
        if time > 0 then
            CountDownManager.Instance:AddCountDown("rebate_activity_common_count_down",
                BindTool.Bind(self.CommonUpdateTime, self),
                BindTool.Bind(self.CommonCompleteTime, self), nil, time, 1)
        end
    end
end

function RebateActivityView:CommonUpdateTime(elapse_time, total_time)
    local temp_seconds = GameMath.Round(total_time - elapse_time)
    local str = string.format(Language.RebateDay.ActEndTime, TimeUtil.FormatSecondDHM6(temp_seconds))
    if self.node_list and self.node_list.common_remain_time_text then
        self.node_list.common_remain_time_text.text.text = str
    end
end

function RebateActivityView:CommonCompleteTime(elapse_time, total_time)
    if self.node_list and self.node_list.common_remain_time_text and self.node_list.remain_time_root then
        self.node_list.remain_time_root:SetActive(false)
        self.node_list.common_remain_time_text.text.text = ""
    end
end

--设置活动剩余时间位置
function RebateActivityView:SetRemainTimePos(vector2_pos)
    if self.node_list.remain_time_root then
        self.node_list.remain_time_root.rect.anchoredPosition = vector2_pos or Vector2.zero
    end
end

VerRebateItemRender = VerRebateItemRender or BaseClass(VerItemRender)

function VerRebateItemRender:SetTailShow(is_show)
    self.node_list["tail"]:SetActive(is_show)
end

function VerRebateItemRender:SetUpLineShow(is_show)
    if self.node_list["up_line"] then
        self.node_list["up_line"]:SetActive(is_show)
    end
end
