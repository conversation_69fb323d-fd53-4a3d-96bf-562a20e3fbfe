FightSoulBoneStrengthView = FightSoulBoneStrengthView or BaseClass(SafeBaseView)
function FightSoulBoneStrengthView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1026, 636)})
    self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_fight_soul_bone_strength")
end

function FightSoulBoneStrengthView:__delete()

end

function FightSoulBoneStrengthView:ReleaseCallBack()
    if nil ~= self.list_view then
        self.list_view:DeleteMe()
        self.list_view = nil
    end

    if self.show_cell ~= nil then
        self.show_cell:DeleteMe()
        self.show_cell = nil
    end

    if nil ~= self.stuff_item then
        self.stuff_item:DeleteMe()
        self.stuff_item = nil
    end

    if nil ~= self.attr_list then
        for k,v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end

	self:CleanUpDownTween()
    self.slot = nil
    self.jump_remind = nil
	self.model_asset = nil
end

function FightSoulBoneStrengthView:LoadCallBack()
    self.selet_part = -1
    self.node_list.title_view_name.text.text = Language.FightSoul.StrengthBoneTitle

    if self.show_cell == nil then
        self.show_cell = FSBoneEquipBaseItem.New(self.node_list.show_cell)
        self.show_cell:SetIsShowTips(true)
    end

    if nil == self.list_view then
        self.list_view = AsyncBaseGrid.New()
        self.list_view:SetStartZeroIndex(false)
        self.list_view:CreateCells({
            col = 3,
            cell_count = 6,
            list_view = self.node_list["list_view"],
            itemRender = FSBoneStrengthItem,
            assetBundle = "uis/view/fight_soul_ui_prefab",
            assetName = "fight_soul_bone_strength_cell",
            change_cells_num = 2,
        })
        self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectItemCB, self))
    end

    if nil == self.stuff_item then
        self.stuff_item = ItemCell.New(self.node_list.stuff_item)
    end

    if self.attr_list == nil then
        self.attr_list = {}
        for i = 1, 5 do
            self.attr_list[i] = FSBoneStrengthAttrRender.New(self.node_list.attrs_list:FindObj("attr_" .. i))
        end
    end
	--self:DoUpDownTween()

    XUI.AddClickEventListener(self.node_list.btn_go_call, BindTool.Bind(self.OnClickGoCall, self))
    XUI.AddClickEventListener(self.node_list.btn_strength, BindTool.Bind(self.OnClickStrength, self))
	XUI.AddClickEventListener(self.node_list.btn_one_key_strength, BindTool.Bind(self.OnClickOneKeyStrength, self))
end

function FightSoulBoneStrengthView:CleanUpDownTween()
	if self.model_tweener then
        self.model_tweener:Kill()
        self.model_tweener = nil
    end
end

function FightSoulBoneStrengthView:DoUpDownTween()
	self:CleanUpDownTween()

	local tween_time = 1.6
    local node = self.node_list["display"]
	if node then
        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -10)
        self.model_tweener = node.rect:DOAnchorPosY(10, tween_time)
        self.model_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.model_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function FightSoulBoneStrengthView:SetDataAndOpen(slot)
    self.slot = slot
    self:Open()
end

function FightSoulBoneStrengthView:ShowIndexCallBack()
    self.jump_remind = true
end

function FightSoulBoneStrengthView:OnFlush()
    self:FlushListView()
end

function FightSoulBoneStrengthView:FlushListView()
    local wear_list = FightSoulWGData.Instance:GetBonePartStrengthShowList(self.slot)
    if IsEmptyTable(wear_list) then
    	return
    end

    self.list_view:SetDataList(wear_list)
    if self.jump_remind or self.selet_part < 0 then
        local jump_index = FightSoulWGData.Instance:GetBoneStrengthJumpIndex(wear_list)
        self.list_view:JumpToIndexAndSelect(jump_index)
        self.jump_remind = false
    else
        self:FlushRightPanel()
    end
end

-- 选择列表项回调
function FightSoulBoneStrengthView:OnSelectItemCB(item, cell_index, is_default, is_click)
	if nil == item or nil == item.data then
		return
	end

    if self.selet_part == item.data.bone_part then
        return
    end

    self.selet_part = item.data.bone_part
    self:FlushRightPanel()
end

function FightSoulBoneStrengthView:FlushRightPanel()
    local part_data = FightSoulWGData.Instance:GetBonePartDataBySlotPart(self.slot, self.selet_part)
    if IsEmptyTable(part_data) then
    	return
    end

    local is_no_wear = IsEmptyTable(part_data.bone_data)
    if is_no_wear then
        self.node_list.no_data:SetActive(true)
        self.node_list.had_data:SetActive(false)
        return
    else
        self.node_list.no_data:SetActive(false)
        self.node_list.had_data:SetActive(true)

        local level = part_data.slot_level
        local is_max = FightSoulWGData.Instance:GetBoneStrengthIsMaxLevel(self.selet_part, level)
        local no_max = not is_max

        self.show_cell:SetData(part_data)

        -- 属性
        self.node_list.cur_level_value.text.text = string.format(Language.FightSoul.LevelStr, level)
        self.node_list.level_arrow:SetActive(no_max)
        self.node_list.next_level_value:SetActive(no_max)
        if no_max then
            self.node_list.next_level_value.text.text = string.format(Language.FightSoul.LevelStr, level + 1)
        end

        local attr_list = FightSoulWGData.Instance:GetBoneStrengthAttr(self.selet_part, level)
        for k,v in pairs(self.attr_list) do
            v:SetData(attr_list[k])
        end

        -- 材料
        local level_cfg
        if is_max then
            level_cfg = FightSoulWGData.Instance:GetBoneStrengthCfg(self.selet_part, level)
        else
            level_cfg = FightSoulWGData.Instance:GetBoneStrengthCfg(self.selet_part, level + 1)
        end

        local is_have_stuff = false
        if level_cfg then
            local item_str = ""
            local str_color
            local consume_item = level_cfg.stuff_id
            local consume_num = level_cfg.stuff_num
            self.stuff_item:SetData({item_id = consume_item})
            if no_max and consume_num > 0 then
                local num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
                is_have_stuff = num >= consume_num
                str_color = is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
                item_str = num .. "/" .. consume_num
            end

            self.stuff_item:SetRightBottomColorText(item_str, str_color)
            self.stuff_item:SetRightBottomTextVisible(true)
        end

        -- 按钮
        self.node_list.btn_strength:SetActive(no_max)
		self.node_list.btn_one_key_strength:SetActive(no_max)
        self.node_list.max_flag:SetActive(is_max)
        self.node_list.btn_strength_remind:SetActive(is_have_stuff)
		self.node_list.btn_one_key_strength_remind:SetActive(is_have_stuff)
    end
end

function FightSoulBoneStrengthView:OnClickStrength()
    local part_data = FightSoulWGData.Instance:GetBonePartDataBySlotPart(self.slot, self.selet_part)
    if IsEmptyTable(part_data) then
    	return
    end

    local level = part_data.slot_level
    local is_max = FightSoulWGData.Instance:GetBoneStrengthIsMaxLevel(self.selet_part, level)
    if is_max then
        return
    end

    local level_cfg = FightSoulWGData.Instance:GetBoneStrengthCfg(self.selet_part, level + 1)
    if level_cfg then
        local consume_item = level_cfg.stuff_id
        local consume_num = level_cfg.stuff_num
        if consume_num > 0 then
            local num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
            if num < consume_num then
                local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(consume_item)
                if item_cfg then
                    local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
                    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.NoEnoughTips, item_name))
                    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = consume_item})
                    return
                end
            end
        end
    end

    FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.BONE_UPLEVEL, self.slot, self.selet_part)
end

function FightSoulBoneStrengthView:OnClickOneKeyStrength()
	local remind = FightSoulWGData.Instance:GetBoneSlotStrengthRemind(self.slot)
	if remind then
		FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.BONE_ONE_KEY_UPLEVEL, self.slot)
	else
		local level_cfg = FightSoulWGData.Instance:GetBoneStrengthCfg(self.selet_part, 1)
		if level_cfg then
			local item_cfg = ItemWGData.Instance:GetItemConfig(level_cfg.stuff_id)
			if item_cfg then
				local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.NoEnoughTips, item_name))
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = level_cfg.stuff_id})
			end
		end
	end
end

function FightSoulBoneStrengthView:OnClickGoCall()
	FunOpen.Instance:OpenViewByName(GuideModuleName.SiXiangCallView, TabIndex.sixiang_call_sx)
end

function FightSoulBoneStrengthView:PlayStengthEffect()
	if self.node_list.effect_node then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua, is_success = true,
			pos = Vector2(0, 0), parent_node = self.node_list.effect_node})
	end
end

--------------------------------------------------------------------------------
FSBoneStrengthItem = FSBoneStrengthItem or BaseClass(FSBoneEquipBaseItem)
function FSBoneStrengthItem:OnFlush()
    FSBoneEquipBaseItem.OnFlush(self)

    if self.data == nil or IsEmptyTable(self.data.bone_data) then
        self.node_list.item_remind.image.enabled = false
        self.node_list.select_effect.image.enabled = false
        local b_bundle, b_asset = ResPath.GetFightSoulImg("a2_hbzb_bg_0")
        self.node_list.color_bg.image:LoadSprite(b_bundle, b_asset)
        --self.node_list.not_wear_str:SetActive(true)
        self.node_list.level.text.text = ""
        return
    end

    local strength_level = self.data.slot_level or 0
    self.node_list.level.text.text = strength_level > 0 and strength_level or ""
    if strength_level > 0 then
        self.node_list.level_bg:SetActive(true)
        self.node_list.star_part.transform.localPosition = Vector3(10,-91,0)
    else
        self.node_list.level_bg:SetActive(false)
        self.node_list.star_part.transform.localPosition = Vector3(0,-91,0)
    end
    --self.node_list.not_wear_str:SetActive(false)
    local remind = FightSoulWGData.Instance:GetBonePartStrengthRemind(self.data.fs_slot, self.data.bone_part)
    self.node_list.item_remind.image.enabled = remind
end

function FSBoneStrengthItem:OnSelectChange(is_select)
    self.node_list.select_effect.image.enabled = is_select
end