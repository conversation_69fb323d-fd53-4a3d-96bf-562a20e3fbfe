LongHunResultTips = LongHunResultTips or BaseClass(SafeBaseView)

function LongHunResultTips:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
end

function LongHunResultTips:__delete()

end

function LongHunResultTips:ReleaseCallBack()
	if self.get_item then
		for i = 1,2 do
			self.get_item[i]:DeleteMe()
		end
		self.get_item = nil
	end
end

-- 加载配置
function LongHunResultTips:LoadConfig()
	local bundle_name = "uis/view/long_hun_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_commmon_second_panel")
	self:AddViewResource(0,bundle_name,"layout_longhun_result")
end

function LongHunResultTips:LoadCallBack(index, loaded_times)
	self:SetSecondView(Vector2(740,411))
	self.node_list.title_view_name.text.text = Language.LongHunView.FenJieResult
	self.node_list["btn_confirm"].button:AddClickListener(BindTool.Bind(self.OnClickConfirm,self))
	self.node_list["btn_cancle"].button:AddClickListener(BindTool.Bind(self.OnClickCancle,self))
	

	self.get_item = {}
	for i = 1 , 2 do 
		self.get_item[i] = ItemCell.New(self.node_list["cell"..i])
		self.node_list["cell"..i]:SetActive(false)
		self.node_list["cellclick"..i].button:AddClickListener(BindTool.Bind(self.OnClickLongHunCell,self,i))
	end
end

function LongHunResultTips:OnFlush(param_t)
	if not IsEmptyTable(self.data) then
		local compose_data = LongHunWGData.Instance:GetItemComposeData(self.data.item_id)
		local base_cfg = LongHunWGData.Instance:GetMingWenBaseCfgByID(self.data.item_id)
		local cost_type = base_cfg.cost_type or 0
		local level_cfg = LongHunWGData.Instance:GetDeComposeCfgByLV(self.data.level)
		
		self.node_list["title_text"].text.text = base_cfg.name..Language.LongHunView.XianQianFenJieTitle
		if not IsEmptyTable(level_cfg) then
			local get_jingyan = level_cfg["get_jingyan_"..cost_type]
			self.node_list["cell1"]:SetActive(true)
			local item1 = LongHunWGData.Instance:GeiCostMoneyID()
			self.get_item[1]:SetData({item_id = item1,num = get_jingyan}) 
			if get_jingyan <= 0 then
				self.node_list["cell1"]:SetActive(false)
			end
		else
			self.node_list["cell1"]:SetActive(false)
		end

		if not IsEmptyTable(compose_data) then
			self.node_list["cell2"]:SetActive(true)
			self.get_item[2]:SetData({item_id = compose_data.stuff_id_vec,num = compose_data.get_stuff_num_vec})
		else
			self.node_list["cell2"]:SetActive(false)
		end
		self.node_list["tips"].text.text = Language.LongHunView.SweepTip1

		self.longhun_cell = {}
		--if compose_data.decompose_show == 1 then
			local compose_longhun = {}
			if compose_data.lifesoul_id_vec then
				compose_longhun = Split(compose_data.lifesoul_id_vec,"|")
			end

			if compose_longhun[1] and tonumber(compose_longhun[1]) <= 0 then
				self.node_list["tips"].text.text = Language.LongHunView.SweepTip2
			end
			for i=1,2 do
				if compose_longhun[i] then
					local item_id = tonumber(compose_longhun[i])
					local ui_index = i + 2

					if item_id > 0 then
						self.longhun_cell[i] = item_id
						local cfg = ItemWGData.Instance:GetItemConfig(item_id)
						local c_bundle, c_asset = ResPath.GetCommonImages("a3_ty_wpk_"..cfg.color)
						self.node_list["cellbg"..i].image:LoadSpriteAsync(c_bundle, c_asset)
						
 						local bundle, asset = ResPath.GetItem(cfg.icon_id)
 						self.node_list["cellicon"..i].image:LoadSpriteAsync(bundle, asset,function ()
							self.node_list["cellicon"..i].image:SetNativeSize()
						end)
						self.node_list["cell"..ui_index]:SetActive(true)
					else
						self.node_list["cell"..ui_index]:SetActive(false)
					end
				end
			end
		--end

	end
end

function LongHunResultTips:OnClickLongHunCell(index)
	if self.longhun_cell and self.longhun_cell[index] then
		local data = {}
		data.item_id = self.longhun_cell[index]
		data.level = 1
		LongHunWGCtrl.Instance:OpenLongHunOpera(data,{},{},false)
	end
end

function LongHunResultTips:SetData(data)
	self.data = data or {}
end

function LongHunResultTips:OnClickConfirm()
	if not IsEmptyTable(self.data) then
		local count = 1
		local list = {}
		list[1] = self.data.index
		LongHunWGCtrl.Instance:SendLongHunDeCompose(count, list)
	end
	self:OnClickCancle()
	LongHunWGCtrl.Instance:PlayFenJieAni()
end

function LongHunResultTips:OnClickCancle()
	self:Close()
end