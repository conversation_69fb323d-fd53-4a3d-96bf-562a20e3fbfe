-- 通天降临-专享商店

local count_down_key = "premium_store_show_model_countdown"

function WorldTreasureView:PremiumStoreReleaseCallBack()
	if self.premium_shop_list then
		self.premium_shop_list:DeleteMe()
		self.premium_shop_list = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.ps_model_display then
		self.ps_model_display:DeleteMe()
		self.ps_model_display = nil
	end

	self:CleanPremiunStoreTimer()
	self.model_cfg_index = nil
end

function WorldTreasureView:PremiumStoreLoadCallBack()
	if not self.premium_shop_list then
		self.premium_shop_list = AsyncBaseGrid.New()
		self.premium_shop_list:CreateCells({
			list_view = self.node_list["premium_store_list"], 
			assetBundle = "uis/view/world_treasure_ui_prefab", 
			assetName = "premium_store_item", 
			itemRender = PremiumStoreItem, 
			change_cells_num = 1, col = 3})
		self.premium_shop_list:SetStartZeroIndex(false)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_bind_gold = true,
			show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.ps_model_display then
		self.ps_model_display = OperationActRender.New(self.node_list["ps_model_display"])
		self.ps_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	local bundle, asset = WorldTreasureWGData.Instance:FormatGradeRawImage("a3_ttjl_ysz_")
    self.node_list["title_img_premium_store"].raw_image:LoadSprite(bundle, asset)

	self.model_cfg_index = 1
	XUI.AddClickEventListener(self.node_list["btn_show_next_model"], BindTool.Bind1(self.OnClickShowNextModel, self))
	XUI.AddClickEventListener(self.node_list["btn_goto"], BindTool.Bind1(self.OnClickGoToBtn, self))
end

function WorldTreasureView:PremiumStoreShowIndexCallBack()

end

function WorldTreasureView:PremiumStoreOnFlush(param_t)
	local list_data = WorldTreasureWGData.Instance:GetPremiumShopList()
	self.premium_shop_list:SetDataList(list_data)

	self:SetPremiumStoreModelShow()
	self:PremiumStoreShowGoToBtn()

	local model_cfg_list = WorldTreasureWGData.Instance:GetPremiumShopShowCfg()
	self.node_list["btn_show_next_model"]:SetActive(#model_cfg_list > 1)
end

function WorldTreasureView:CleanPremiunStoreTimer()
	if CountDownManager.Instance:HasCountDown(count_down_key) then
		CountDownManager.Instance:RemoveCountDown(count_down_key)
	end
end

function WorldTreasureView:OnClickShowNextModel()
	local model_cfg_list = WorldTreasureWGData.Instance:GetPremiumShopShowCfg()
	local next_index = self.model_cfg_index + 1
	if next_index > #model_cfg_list then
		next_index = 1
	end
	if next_index == self.model_cfg_index then
		return
	end
	self.model_cfg_index = next_index
	self:SetPremiumStoreModelShow()
end


function WorldTreasureView:SetPremiumStoreModelShow()
	self:CleanPremiunStoreTimer()

	local model_cfg = WorldTreasureWGData.Instance:GetPremiumShopShowCfgByIndex(self.model_cfg_index)
	if not model_cfg then
		return
	end
	local display_data = {}
	display_data.item_id = model_cfg.model_show_itemid
	display_data.should_ani = true
	display_data.render_type = model_cfg.model_show_type and model_cfg.model_show_type - 1 or 0
	display_data.bundle_name = model_cfg.model_bundle_name
	display_data.asset_name = model_cfg.model_asset_name
	display_data.model_rt_type = ModelRTSCaleType.XL

	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local position_tab = string.split(model_cfg.display_pos, "|")
		local rotation_tab = string.split(model_cfg.display_rotation, "|")
		local vector_pos = Vector3(position_tab[1], position_tab[2], position_tab[3])
		local vector_rot = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		display_data.model_adjust_root_local_position = vector_pos or Vector3(0, 0, 0)
		display_data.model_adjust_root_local_rotation = vector_rot or Vector3(0, 0, 0)
		local scale = model_cfg.display_scale or 1
		display_data.model_adjust_root_local_scale = Vector3(scale, scale, scale)
	end
	self.ps_model_display:SetData(display_data)

	local show_time = tonumber(model_cfg.show_time)
	CountDownManager.Instance:AddCountDown(count_down_key,
		-- 回调方法
		nil,
		-- 倒计时完成回调方法
		BindTool.Bind1(self.OnClickShowNextModel, self),
		nil, show_time)
end

function WorldTreasureView:PremiumStoreShowGoToBtn()
	local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	local is_show_btn = false
	if grade_cfg and grade_cfg.open_panel and grade_cfg.open_panel ~= "" then
		local is_open_activity = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
		is_show_btn = is_open_activity
	end
	self.node_list.btn_goto:CustomSetActive(is_show_btn)
end

function WorldTreasureView:OnClickGoToBtn()
	local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	if grade_cfg and grade_cfg.open_panel and grade_cfg.open_panel ~= "" then
		ViewManager.Instance:OpenByCfg(grade_cfg.open_panel)
	end
end

-----------------------------------
-- 专享商店Item
-----------------------------------
PremiumStoreItem = PremiumStoreItem or BaseClass(BaseRender)

function PremiumStoreItem:__init()
end

function PremiumStoreItem:LoadCallBack()
	self:ChangeStyle()

	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_root)
	end
	XUI.AddClickEventListener(self.node_list["btn_click"], BindTool.Bind(self.ClickExchange, self))
	XUI.AddClickEventListener(self.node_list["item_icon"], BindTool.Bind(self.ClickStuff, self))
end

function PremiumStoreItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function PremiumStoreItem:ChangeStyle()
	local bundle, asset = WorldTreasureWGData.Instance:FormatGradeImage("a3_ttjl_dk1_")
	self.node_list["item_bg"].image:LoadSprite(bundle, asset)
	bundle, asset = WorldTreasureWGData.Instance:FormatGradeImage("a3_ttjl_dk2_")
	self.node_list["img_exchange"].image:LoadSprite(bundle, asset)
end

function PremiumStoreItem:OnFlush()
	if self.data == nil then
		self.view:SetActive(false)
		return
	end
	self.view:SetActive(true)

	self.item_cell:SetData(self.data.item)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)
	local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	local text_color_2 = grade_cfg and grade_cfg.text_color2
	local unenough_color = COLOR3B.L_RED
	local name_str = item_cfg and item_cfg.name or ""
	local exchange_num_str = string.format(Language.WorldTreasure.PremiumShopExchange, self.data.can_exchange_times)
	if text_color_2 and text_color_2 ~= "" then
		name_str = ToColorStr(name_str, text_color_2)
		exchange_num_str = ToColorStr(exchange_num_str, text_color_2)
		unenough_color = text_color_2
	end
	self.node_list["name"].text.text = name_str

	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id)
	self.node_list["cost"].text.text = GetRightColor(num .. "/" .. self.data.stuff_count, num >= self.data.stuff_count, COLOR3B.L_GREEN, unenough_color)
	local duihuan_num = math.floor(num / self.data.stuff_count)
	self.node_list["exchange_num"].text.text = exchange_num_str
	-- self.node_list["exchange_num"].text.text = string.format(Language.WorldTreasure.ExchangeStr, 
	-- 	GetRightColor(duihuan_num, duihuan_num > 0, COLOR3B.L_GREEN, COLOR3B.L_RED), 
	-- 	GetRightColor(self.data.can_exchange_times, self.data.can_exchange_times > 0, COLOR3B.L_GREEN, COLOR3B.L_RED))

	self.node_list["remind"]:SetActive(self.data.can_exchange_times > 0 and num >= self.data.stuff_count)

	local stuff_config = ItemWGData.Instance:GetItemConfig(self.data.stuff_id)
	if stuff_config then
		local bundle, asset = ResPath.GetItem(stuff_config.icon_id)
		self.node_list["item_icon"].image:LoadSprite(bundle, asset, function ()
			self.node_list["item_icon"].image:SetNativeSize()
		end)
	else
		print_error("[ExchangeShopItem] item_config is invaild, item_id is:", self.data.stuff_id)
	end

	local sold_out = self.data.can_exchange_times == 0
	self.node_list["word_sold_out"]:SetActive(sold_out)
	self.node_list["img_exchange"]:SetActive(not sold_out)
	self.node_list["cost_group"]:SetActive(not sold_out)
	self.node_list["exchange_num"]:SetActive(not sold_out)
	--self.node_list["btn_click"].button.interactable = not sold_out

	-- local item_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_13")
	-- local bundle1, asset1 = ResPath.GetRawImagesPNG(item_bg_name)
	-- self.node_list["item_bg"].raw_image:LoadSprite(bundle1, asset1, function()
    --     self.node_list["item_bg"].raw_image:SetNativeSize()
    -- end)

	--local index = OperationActivityWGData.Instance:GetCurTypeId()
	local text_color_1 = grade_cfg and grade_cfg.text_color1
	local btn_str = self.node_list.btn_exchange_text.text.text
	if text_color_1 and text_color_1 ~= "" then
		btn_str = ToColorStr(btn_str, text_color_1)
	end
	self.node_list.btn_exchange_text.text.text = btn_str
end

function PremiumStoreItem:ClickExchange()
	if self.data.can_exchange_times == 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.Soldout)
		return
	end

	local num = self.data.item.num
	local empty = ItemWGData.Instance:GetEmptyNum()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)

	if empty < num and item_cfg and item_cfg.not_put_flag == 0 then
		RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
		return
	end
	
	local buy_func = function(num)
		TipWGCtrl.Instance:ShowGetItem(self.data.item)
		local buy_num = num or 1
		WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_CONVERT_SHOP_CONVERT, self.data.seq, buy_num)
	end

	local tips_data = {}
	tips_data.title_view_name = Language.Common.ExchangeItemTipsTitle
	tips_data.item_id = self.data.item.item_id
	tips_data.expend_item_id = self.data.stuff_id
	tips_data.expend_item_num = self.data.stuff_count
	tips_data.max_buy_count = self.data.can_exchange_times
	tips_data.is_show_limit = true
	TipWGCtrl.Instance:OpenCustomBuyItemTipsView(tips_data, buy_func)
end

function PremiumStoreItem:ClickStuff()
	TipWGCtrl.Instance:OpenItem({item_id = self.data.stuff_id}, ItemTip.FROM_NORMAL, nil)
end

--[[
PremiumStoreItem = PremiumStoreItem or BaseClass(BaseRender)

function PremiumStoreItem:LoadCallBack()
	if self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_root"])
	end
	
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind1(self.OnClickBuy, self))
end

function PremiumStoreItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function PremiumStoreItem:OnFlush()
	if not self.data then
		return
	end

	local num = WorldTreasureWGData.Instance:GetShopBuyNum(self.data.seq)
	local remain_num = self.data.buy_count_limit - num
	local btn_txt = ""
	--self.node_list.node_shifu_money:SetActive(self.data.type == 2 and remain_num > 0)
	if self.data.type == 2 then
		btn_txt = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
		local buy_str2 = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq, nil, true)
		--self.node_list.text_show_price.text.text = buy_str2
		--UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["image_shifu_bg"].rect)
	-- elseif self.data.type == 1 then
	--     btn_txt = string.format("%s%s", self.data.price, Language.WorldTreasure.LimitBuyBtn1)
	elseif self.data.type == 0 then
		btn_txt = self.data.price
	end

	if remain_num <= 0 then
		btn_txt = Language.WorldTreasure.LimitBuyTxt5
	end

	self.node_list.cost_icon:SetActive(remain_num > 0)
	self.node_list.text_price.text.text = btn_txt
	local reward_item = self.data.reward_item[0]
	self.item_cell:SetData(reward_item)

	local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
	local item_num_color = self.data.buy_count_limit - num >= 1 and COLOR3B.L_GREEN or COLOR3B.L_RED
	self.node_list.text_name.text.text = item_cfg.name
	self.node_list.text_num.text.text = string.format(Language.WorldTreasure.LimitBuyTxt1, item_num_color, self.data.buy_count_limit - num, self.data.buy_count_limit)
	--XUI.SetButtonEnabled(self.node_list["btn_buy"], remain_num > 0)
end

function PremiumStoreItem:OnClickBuy()
	local num = WorldTreasureWGData.Instance:GetShopBuyNum(self.data.seq)
	if num >= self.data.buy_count_limit then
		return
	end

	local langu = Language.WorldTreasure
	if self.data.type == 2 then
		RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
	else
		local reward_item = self.data.reward_item[0]
		local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
		local cost_name = self.data.type == 1 and langu.LimitBuyBtn1 or langu.LimitBuyBtn0
		WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_HUNZHEN_SHOP_BUY, self.data.grade, self.data.seq, 1)
	end
end
]]