SerOperationJuanXianTipView = SerOperationJuanXianTipView or BaseClass(SafeBaseView)

function SerOperationJuanXianTipView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/operation_juanxian_prefab", "layout_qfjxreward_tip_view")
end

function SerOperationJuanXianTipView:__delete()

end

function SerOperationJuanXianTipView:ReleaseCallBack()

end

function SerOperationJuanXianTipView:LoadCallBack()

end

function SerOperationJuanXianTipView:SetData(data)
	self.data = data
end

function SerOperationJuanXianTipView:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.item:SetActive(true)
	self.node_list.cell_bg:SetActive(false)
	self.node_list.name.text.text = self.data.name
	self.node_list.desc.text.text = self.data.desc
	
	if  self.data.icon_type == OPERATION_SHOW_TYPE.ONE then
		self.node_list.item:SetActive(false)
		self.node_list.cell_bg:SetActive(true)

		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.param1)
		if item_cfg then
			self.node_list.cell.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
			self.node_list.cell_bg.image:LoadSprite(ResPath.GetCommonBackGround("bg_cell_circle2_" .. item_cfg.color))
		end

		if self.data.param2 > 1 then
			self.node_list.item_num_bg:SetActive(true)
			self.node_list.item_num.text.text = self.data.param2
		else
			self.node_list.item_num_bg:SetActive(false)
		end

	elseif self.data.icon_type == OPERATION_SHOW_TYPE.TWO then
		self.node_list.item.image:LoadSprite(ResPath.GetOpenServerName(self.data.icon))
	end

	if self.data.icon_type == OPERATION_SHOW_TYPE.ONE then
		self.node_list.state_text.text.text = string.format(Language.OperationJuanXian.JuanXianStr2, self.data.server_day_juanxian_num)

		local state = OperationJuanXianWGData.Instance:GetGiftState(self.data.index)
		if state == ActivityRewardState.YLQ then
			self.node_list.time_text.text.text = Language.OperationJuanXian.JuanXianStr5
		elseif state == ActivityRewardState.KLQ then
			self.node_list.time_text.text.text = Language.OperationJuanXian.JuanXianStr6
		else
			self.node_list.time_text.text.text = Language.OperationJuanXian.JuanXianStr7
		end
	else
		local time_sec = OperationJuanXianWGData.Instance:GetRewardTime(self.data.index)
		self.node_list.state_text.text.text = string.format(Language.OperationJuanXian.JuanXianStr3, self.data.server_day_juanxian_num)
		if time_sec > 0 then
			self.node_list.time_text.text.text = string.format(Language.OperationJuanXian.JuanXianStr4, TimeUtil.FormatSecond2MYHM1(time_sec))
		else
			self.node_list.time_text.text.text = Language.OperationJuanXian.JuanXianStr7
		end
	end
end