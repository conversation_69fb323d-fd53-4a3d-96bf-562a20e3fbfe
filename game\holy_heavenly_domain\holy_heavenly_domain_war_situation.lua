HolyHeavenlyDomainWarSituationView = HolyHeavenlyDomainWarSituationView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainWarSituationView:__init()
    self:SetMaskBg(false, true)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(1, 1), sizeDelta = Vector2(886, 552)})
    self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_war_situation")
end

-- 需查询除去 主城池其余能占领的城池的信息  需要确认我国家位置 处理图标标识，所以需要全请求
function HolyHeavenlyDomainWarSituationView:OpenCallBack()
    local city_table = {}
    local city_data = HolyHeavenlyDomainWGData.Instance:GetMapCitiDataList()
    for k, v in pairs(city_data) do
        if v.type ~= 4 then
            city_table[v.seq] = 1
        end
    end

    HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainGetCitiInfoOperate(city_table)

    -- 需要确认自己阵营 补一条请求
    HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.ROOM_INFO)  --房间信息
end

function HolyHeavenlyDomainWarSituationView:LoadCallBack()
    if not self.country_list then
        self.country_list = AsyncListView.New(HHDWarSituationListItemRender, self.node_list["country_list"])
    end

    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind1(self.Close, self))

    self.node_list.title_view_name.text.text = Language.HolyHeavenlyDomain.WarSituation
end

function HolyHeavenlyDomainWarSituationView:ReleaseCallBack()
    if self.country_list then
        self.country_list:DeleteMe()
        self.country_list = nil
    end
end

function HolyHeavenlyDomainWarSituationView:OnFlush()
    local data_list, fatigue_value, efficiency = HolyHeavenlyDomainWGData.Instance:GetWarSituationCountryDataList()
    self.country_list:SetDataList(data_list)
    self.node_list.no_country_data:CustomSetActive(IsEmptyTable(data_list))

    local desc = HolyHeavenlyDomainWGData.Instance:GetWarSituationDescByfatigueValue(fatigue_value)
	self.node_list.desc_content.text.text = string.format(Language.HolyHeavenlyDomain.WarSituationDesc, fatigue_value, efficiency, desc)
end

HHDWarSituationListItemRender = HHDWarSituationListItemRender or BaseClass(BaseRender)

function HHDWarSituationListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_name.text.text = self.data.cfg.city_name
    self.node_list.desc_state.text.text = Language.HolyHeavenlyDomain.WarSituationState[self.data.status]
    local bundle, asset = ResPath.GetHolyHeavenlyDomainCountryImg(self.data.camp, self.data.cfg.city_icon_type)
    self.node_list.img_city_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.img_city_icon.image:SetNativeSize()
    end)

    local state_bundle, state_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_zlxs" .. self.data.status)
    self.node_list.state_bg.image:LoadSprite(state_bundle, state_asset, function ()
        self.node_list.state_bg.image:SetNativeSize()
    end)

    local flag_reward = false
    if self.data.status == 1 then
        local city_seq = self.data.cfg.seq
        local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(city_seq)

        if not IsEmptyTable(city_info) then
            flag_reward = HolyHeavenlyDomainWGData.Instance:GetCaptureRewardFlagBySeq(city_seq) == 0
        end
    end

    self.node_list.flag_reward:CustomSetActive(flag_reward)
end