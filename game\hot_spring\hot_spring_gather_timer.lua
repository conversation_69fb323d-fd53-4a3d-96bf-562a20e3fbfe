--采集物刷新倒计时
HotSpringGatherTimer = HotSpringGatherTimer or BaseClass(SafeBaseView)

function HotSpringGatherTimer:__init()
    self:AddViewResource(0, "uis/view/hot_spring_ui_prefab", "hotspring_gather_timer")
end

function HotSpringGatherTimer:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("kf_hot_spring_gather_timer") then
        CountDownManager.Instance:RemoveCountDown("kf_hot_spring_gather_timer")
    end
    self.refresh_time = nil
    self.root_gameobject = nil
end

function HotSpringGatherTimer:LoadCallBack()
    self.refresh_time = self.node_list["refresh_time"]
    self.root_gameobject = self.node_list["root"].gameObject
end

function HotSpringGatherTimer:OnFlush(param_t, index)
    if not IsNil(self.root_gameobject) then
        self.root_gameobject:SetActive(true)
    end

    local refresh_timestamp = HotSpringWGData.Instance:GatherRefreshRemainTime()
    refresh_timestamp = refresh_timestamp or TimeWGCtrl.Instance:GetServerTime()
    local time = refresh_timestamp - TimeWGCtrl.Instance:GetServerTime()

    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HOTSPRING)
    local activity_remains_time = 0
    if IsEmptyTable(activity_info) then
        activity_remains_time = activity_info.next_time - TimeWGCtrl.Instance:GetServerTime()
    end
    --策划需求，如果活动剩余时间小于采集刷新时间，不再显示采集倒计时
    if activity_remains_time < time then
        if CountDownManager.Instance:HasCountDown("kf_hot_spring_gather_timer") then
            CountDownManager.Instance:RemoveCountDown("kf_hot_spring_gather_timer")
        end
        self:Close()
        return
    end

    if CountDownManager.Instance:HasCountDown("kf_hot_spring_gather_timer") then
        CountDownManager.Instance:RemoveCountDown("kf_hot_spring_gather_timer")
    end
    self:UpdateGatherTimer(0, time)

    if time > 0.1 then
        CountDownManager.Instance:AddCountDown("kf_hot_spring_gather_timer", BindTool.Bind(self.UpdateGatherTimer,self)
            , BindTool.Bind(self.ComleteGatherTimer,self), nil, time, 1)
    else
        self:ComleteGatherTimer()
    end
end

function HotSpringGatherTimer:UpdateGatherTimer(elapse_time, total_time)
    local time = math.floor(total_time - elapse_time)
    self.refresh_time.text.text = TimeUtil.FormatSecond(time, 2)
end

function HotSpringGatherTimer:ComleteGatherTimer(elapse_time, total_time)
    if CountDownManager.Instance:HasCountDown("kf_hot_spring_gather_timer") then
        CountDownManager.Instance:RemoveCountDown("kf_hot_spring_gather_timer")
    end

    self.node_list["refresh_tips"]:SetActive(true)

    if not IsNil(self.root_gameobject) then
        self.root_gameobject:SetActive(false)
    end
end