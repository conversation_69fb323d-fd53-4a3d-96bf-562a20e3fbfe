﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FontTextureReBuildWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(FontTextureReBuild), typeof(Nirvana.Singleton<FontTextureReBuild>));
		<PERSON><PERSON>Function("OnGameStop", OnGameStop);
		<PERSON><PERSON>Function("SetIsOpen", SetIsOpen);
		<PERSON><PERSON>RegFunction("SetCanRefresh", SetCanRefresh);
		L<PERSON>RegFunction("Update", Update);
		<PERSON><PERSON>Function("New", _CreateFontTextureReBuild);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		L<PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateFontTextureReBuild(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				FontTextureReBuild obj = new FontTextureReBuild();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: FontTextureReBuild.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FontTextureReBuild obj = (FontTextureReBuild)ToLua.CheckObject<FontTextureReBuild>(L, 1);
			obj.OnGameStop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsOpen(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FontTextureReBuild obj = (FontTextureReBuild)ToLua.CheckObject<FontTextureReBuild>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsOpen(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCanRefresh(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FontTextureReBuild obj = (FontTextureReBuild)ToLua.CheckObject<FontTextureReBuild>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetCanRefresh(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FontTextureReBuild obj = (FontTextureReBuild)ToLua.CheckObject<FontTextureReBuild>(L, 1);
			obj.Update();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

