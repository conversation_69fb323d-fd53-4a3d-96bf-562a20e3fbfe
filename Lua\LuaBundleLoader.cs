﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using System.IO;
using System.Collections.Generic;
using LuaInterface;
using Nirvana;
using UnityEngine;
using UnityEngine.Assertions;
using System.Text.RegularExpressions;
using System.Text;

/// <summary>
/// The lua bundle loader.
/// </summary>
public sealed class LuaBundleLoader : LuaFileUtils
{
    private const string AssetBundlePrefix = "lua/";
    private const string AssetPrefxi = "Assets/Game/LuaBundle/";
    private const string AssetBundleLookupFile = "lua_bundle_lookup";

    private Dictionary<string, string> lookup = new Dictionary<string, string>(StringComparer.Ordinal);
    private Dictionary<string, AssetBundle> assetBundleDict = new Dictionary<string, AssetBundle>();

    private Dictionary<string, string> resAliasPathMap = new Dictionary<string, string>();
    private HashSet<string> notSaveBundleMap = new HashSet<string>();

    private bool isLoadAliasResPath = false;

    /// <summary>
    /// Initializes a new instance of the <see cref="LuaBundleLoader"/> class.
    /// </summary>
    public LuaBundleLoader()
    {
        LuaFileUtils.instance = this;
        this.beZip = false;
    }

    /// <summary>
    /// Prune all lua bundles.
    /// </summary>
    public void PruneLuaBundles()
    {
        foreach (var key in this.assetBundleDict.Keys)
        {
            var bundle = this.assetBundleDict[key];
            bundle.Unload(true);
        }

        this.assetBundleDict.Clear();
    }

    public void AddLuaBundle(string luaFile, string luaBundle)
    {
        if (!this.lookup.ContainsKey(luaFile))
        {
            this.lookup.Add(luaFile.ToLower(), luaBundle);
        }
    }

    public void SetupLuaLoader(LuaState luaState)
    {
        Debugger.Log("Start setup lua lookup");
        ConfoundMgr.RunRubbishCS(UnityEngine.Random.Range(2, 11));
        string initCode =
            @"
                local start_time = os.clock()
                local LUA_ASSET_BUNDLE_PREFIX = 'lua/'
                local LUA_ASSET_PREFIX = 'Assets/Game/LuaBundle/'
                local LUA_ASSET_PREFIX_LEN = string.len(LUA_ASSET_PREFIX) + 1
                local SysFile = System.IO.File
                local UnityApplication = UnityEngine.Application
                local UnityAppStreamingAssetsPath = UnityEngine.Application.streamingAssetsPath
                local _sformat = string.format
                if not UNITY_EDITOR then
                    local cache_path = _sformat('%s/%s', UnityApplication.persistentDataPath, EncryptMgr.GetEncryptPath('BundleCache'))
                    local lua_assetbundle = 'LuaAssetBundle/LuaAssetBundle.lua'
                    local en_lua_assetbundle_path = EncryptMgr.GetEncryptPath(lua_assetbundle)
                    --print_error('en_lua_assetbundle_path 111 =', en_lua_assetbundle_path)

                    local lua_assetbundle_data
                    local cache_file_path = _sformat('%s/%s', cache_path, en_lua_assetbundle_path)
                    local is_cache_file_exist = SysFile.Exists(cache_file_path)
                    --print_error(_sformat('[LuaBundleLoader] is_cache_file_exist:%s, cache_file_path:%s', is_cache_file_exist, cache_file_path))
                    local lua_assetbundle_data_path
        
                    if is_cache_file_exist then
                        lua_assetbundle_data_path = _sformat('%s/%s', cache_path, en_lua_assetbundle_path)
                        if EncryptMgr.IsEncryptAsset() then
                            lua_assetbundle_data = EncryptMgr.ReadEncryptFile(lua_assetbundle_data_path)
                        else
                            lua_assetbundle_data = SysFile.ReadAllText(lua_assetbundle_data_path)
                        end
                    else
                        local alias_path = GameRoot.GetAliasResPath('AssetBundle/' .. lua_assetbundle)
                        if EncryptMgr.IsEncryptAsset() then
            	            en_lua_assetbundle_path = EncryptMgr.GetStreamingEncryptPath(lua_assetbundle)
                            if UNITY_ANDROID == true then
                                lua_assetbundle_data_path = 'AssetBundle/'..en_lua_assetbundle_path
                                lua_assetbundle_data = StreamingAssets.EncryptReadAllText(lua_assetbundle_data_path, EncryptMgr.GetStreamingEncryptKey())
                            elseif UNITY_IOS == true then
                                lua_assetbundle_data_path = _sformat('%s/%s', UnityAppStreamingAssetsPath, GameRoot.GetAliasResPath('AssetBundle/'..en_lua_assetbundle_path))
                                lua_assetbundle_data = EncryptMgr.ReadStreamingEncryptFile(lua_assetbundle_data_path)
                                --print_error('IOS lua_assetbundle_data_path =', lua_assetbundle_data_path)
                            end
                            --print_error('en_lua_assetbundle_path 222 =', en_lua_assetbundle_path)
                        else
                            lua_assetbundle_data_path = alias_path
                            lua_assetbundle_data = StreamingAssets.ReadAllText(alias_path)
                            --print_error('not encrypt asset lua_assetbundle_data_path =', lua_assetbundle_data_path)
                        end
                    end
                    --print_error('[LuaBundleLoader] lua_assetbundle_data exist = ', lua_assetbundle_data ~= nil)
                    if nil == lua_assetbundle_data then
                        print_error('lua_assetbundle_data is nil ', lua_assetbundle_data ~= nil)
                    end
                    local lua_assetbundle_data_loader = loadstring(lua_assetbundle_data)()
                    --print_error('[LuaBundleLoader] lua_assetbundle_data_loader exist = ', lua_assetbundle_data_loader ~= nil)
                    local pattern = LUA_ASSET_BUNDLE_PREFIX.. '.+'
                    local lua_bundle_infos = lua_assetbundle_data_loader.bundleInfos
                    for bundle_name, bundle_info in pairs(lua_bundle_infos) do
                        if string.match(bundle_name, pattern) then
                            local hash = bundle_info.hash
                            local realtivePath = _sformat('LuaAssetBundle/%s-%s', bundle_name, hash)
                            realtivePath = EncryptMgr.GetEncryptPath(realtivePath)
                            local path = _sformat('%s/%s', cache_path, realtivePath)
                            if not SysFile.Exists(path) then
                                local alias_path = ''
                                if UNITY_ANDROID == true then
                                    alias_path = _sformat('AssetBundle/%s', EncryptMgr.GetStreamingEncryptPath(_sformat('LuaAssetBundle/%s-%s', bundle_name, hash)))
                                elseif UNITY_IOS == true then
                                    local ios_streaming_encrypt_path = EncryptMgr.GetStreamingEncryptPath(_sformat('LuaAssetBundle/%s-%s', bundle_name, hash))
                    	            alias_path = GameRoot.GetAliasResPath(_sformat('AssetBundle/%s', ios_streaming_encrypt_path))
                                    --print_error('IOS2 ios_streaming_encrypt_path =', ios_streaming_encrypt_path)
                                    --print_error('IOS2 alias_path =', alias_path)
                                else
                                    alias_path = GameRoot.GetAliasResPath(_sformat('AssetBundle/LuaAssetBundle/%s-%s', bundle_name, hash))
                                end
                                path = _sformat('%s/%s', UnityAppStreamingAssetsPath, alias_path)
                            end
                            for _, lua_file in ipairs(bundle_info.deps) do
                                AddLuaBundle(lua_file, path)
                            end
                        end
                    end
                end

                print_log('[LuaBundleLoader] Init Lua File Cost Time:', (os.clock() - start_time))
                AddLuaBundle = nil";

        luaState.DoString(initCode, "LuaState.cs", true);

        Debugger.Log(string.Format("setup lua lookup complete, lua count:{0}", lookup.Count));
    }

    private string GetLuaFileFullPath(string fileName)
    {
        if (!fileName.EndsWith(".lua"))
        {
            fileName += ".lua";
        }

        if (!fileName.EndsWith(".bytes"))
        {
            fileName += ".bytes";
        }

        var filePath = AssetPrefxi + fileName;
        return filePath.ToLower();
    }

    public bool IsLuaFileExist(string fileName)
    {
#if !UNITY_EDITOR
        var filePath = GetLuaFileFullPath(fileName);

        var bundleName = string.Empty;
        if (!this.lookup.TryGetValue(filePath, out bundleName))
        {
            return false;
        }

        return true;
#else
        string path = FindFile(fileName);
        if (!string.IsNullOrEmpty(path) && File.Exists(path))
        {
            return true;
        }

        return false;
#endif
    }

    /// <inheritdoc/>
    public override byte[] ReadFile(string fileName)
    {
        if (RuntimeGUIMgr.Instance.IsGUIOpening() && RuntimeGUIMgr.Instance.IsUseLocalLuaFile())
        {
            return base.ReadFile(fileName);
        }

#if UNITY_EDITOR
        return base.ReadFile(fileName);
#else
        return ReadAssetBundleFile(fileName);
#endif
    }

    static string CacheDirName = "BundleCache";
    static string CacheDirNameEncrypt = EncryptMgr.GetEncryptPath(CacheDirName);
    private byte[] ReadAssetBundleFile(string fileName)
    {
        var filePath = GetLuaFileFullPath(fileName);
        filePath = filePath.ToLower();
        var bundlePath = string.Empty;
        if (!this.lookup.TryGetValue(filePath, out bundlePath))
        {
            Debug.LogErrorFormat("Load lua file failed: {0}, bundle is not existed.", filePath);
            return null;
        }

        // 这个AB包用完需要立刻销毁，不能缓存
        bool notSave = notSaveBundleMap.Contains(bundlePath);
        AssetBundle assetBundle;
        if (!this.assetBundleDict.TryGetValue(bundlePath, out assetBundle))
        {
            string realBundlePath = bundlePath;
#if  UNITY_ANDROID || UNITY_IOS
            if (EncryptMgr.IsEncryptAsset())
            {
                string relativeBundleName;
                if (!bundlePath.Contains(CacheDirName) && !bundlePath.Contains(CacheDirNameEncrypt))
                {
                    //读取整包文件
                    realBundlePath = Regex.Replace(bundlePath, Application.persistentDataPath, Application.streamingAssetsPath);
                    relativeBundleName = bundlePath.Replace(Application.streamingAssetsPath, "").Replace("/AssetBundle/LuaAssetBundle/", "");
                }
                else
                {
                    //读取缓存文件
                    relativeBundleName = bundlePath.Replace(Application.persistentDataPath, "").Replace("/BundleCache/LuaAssetBundle/", "");
                }
                
                int indexOf = relativeBundleName.IndexOf("-");
                string bundleName = relativeBundleName.Substring(0, indexOf);
                if (EncryptMgr.GetIsBase64EncryptStreamingAsset())
                {
                    bundleName = EncryptMgr.GetStreamingEncryptPath(bundleName);
                }
                assetBundle = AssetBundle.LoadFromFile(realBundlePath, 0, (ulong)EncryptMgr.GetEncryptKeyLength(bundleName));
            }
#else
            assetBundle = AssetBundle.LoadFromFile(realBundlePath);
#endif
            if (null == assetBundle)
            {
                Debug.LogErrorFormat("[LuaBundleLoader] AssetBundle.LoadFromFile is failed! {0}, {1}, {2}", bundlePath, fileName, realBundlePath);
                return null;
            }

            if (!notSave)
                this.assetBundleDict.Add(bundlePath, assetBundle);
        }

        if (null == assetBundle)
        {
            Debug.LogErrorFormat("[LuaBundleLoader] bundle is failed! {0}, {1}", bundlePath, fileName);
            return null;
        }

        var textAsset = assetBundle.LoadAsset<TextAsset>(filePath);

        if (textAsset == null)
        {
            if (notSave)
                assetBundle.Unload(true);

            Debug.LogErrorFormat("Load lua file failed: {0}, can not load asset fomr bundle.", fileName);
            return null;
        }

        var buffer = textAsset.bytes;
        Resources.UnloadAsset(textAsset);

        if (notSave)
            assetBundle.Unload(true);

        return buffer;
    }

    public void LoadAliasResPathMap()
    {
#if UNITY_IOS
        //string path = Path.Combine(Application.streamingAssetsPath, "AssetBundle/res_alias_path_map.txt");

        if (string.IsNullOrEmpty(ChannelAgent.GetAliasPathMapPath()))
        {
            return;
        }

        string fullPath =  Path.Combine(Application.streamingAssetsPath, ChannelAgent.GetAliasPathMapPath());
        if (!File.Exists(fullPath))
        {
            Debug.LogErrorFormat("[LuaBundleLoader] not exists {0}", fullPath);
            return;
        }

        Debug.LogFormat("[LuaBundleLoader] start load aliasres path {0}", fullPath);
        this.isLoadAliasResPath = true;

        string data = EncryptMgr.ReadEncryptFile(fullPath);
        string[] lines = null;
        if (!string.IsNullOrEmpty(data))
        {
            lines = data.Split('\n');
        }
        else
        {
            lines = File.ReadAllLines(fullPath);
        }

        for (int i = 0; i < lines.Length; i++)
        {
            string[] ary = lines[i].Split(' ');
            if (ary.Length != 2)
            {
                Debug.LogErrorFormat("[LoadAliasResPathMap] error {0}", lines[i]);
                continue;
            }

            string originalPath = ary[0];
            string newPath = ary[1];
            resAliasPathMap[originalPath] = newPath;
        }

        Debug.LogFormat("[LoadAliasResPathMap] load succ, count: {0}", resAliasPathMap.Count);
#endif
    }

    public string GetAliasResPath(string path)
    {
        if (!isLoadAliasResPath)
        {
            return path;
        }

        string aliasPath = "";
        if (!resAliasPathMap.TryGetValue(path, out aliasPath))
        {
            return path;
        }

        return aliasPath;
    }

    // 客户端需要在线热更某个配置，可能会出现新旧AB包同时出现的情况
    // 所以这里需要记录这些AB包
    public void OverrideLuaBundle(string fileName, string bundleName)
    {
        var filePath = GetLuaFileFullPath(fileName);
        filePath = filePath.ToLower();

        if (!this.lookup.ContainsKey(filePath))
        {
            this.lookup.Add(filePath, bundleName);
        }
        else
        {
            var oldBundleName = this.lookup[filePath];
            this.lookup[filePath] = bundleName;

            // 记录这个AB包
            if (!notSaveBundleMap.Contains(oldBundleName))
            {
                notSaveBundleMap.Add(oldBundleName);

                AssetBundle assetBundle;
                if (this.assetBundleDict.TryGetValue(oldBundleName, out assetBundle))
                {
                    assetBundle.Unload(true);
                    assetBundleDict.Remove(oldBundleName);
                }
            }
        }

        // 记录这个AB包
        if (!notSaveBundleMap.Contains(bundleName))
        {
            notSaveBundleMap.Add(bundleName);

            AssetBundle assetBundle;
            if (this.assetBundleDict.TryGetValue(bundleName, out assetBundle))
            {
                assetBundle.Unload(true);
                assetBundleDict.Remove(bundleName);
            }
        }
    }
}
