GuildMiJingSceneLogic = GuildMiJingSceneLogic or BaseClass(CommonFbLogic)

function GuildMiJingSceneLogic:__init()
	self.guard_x = 0
	self.guard_y = 0
	self.obj_id_map = {}

	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
end

function GuildMiJingSceneLogic:__delete()
	if CountDownManager.Instance:HasCountDown(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE) then
		CountDownManager.Instance:RemoveCountDown(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE)
	end

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end

end

function GuildMiJingSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.Guild.GuildMiJingFbName)
		FuBenWGCtrl.Instance:OpenGuildMiJingView()
	end)

	self:SetLeaveFbTip(true)

 	local gold_times, coin_times = GuildWGData.Instance:GetGuWuTimes()
	local gold_total_times, coin_total_times = GuildWGData.Instance:GetGuWuTotalTimes()

	if gold_times < gold_total_times or coin_times < coin_total_times then
		GuildWGCtrl.Instance:OpenGuWuView()
	end
    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
 	-- GuildWGCtrl.Instance:OpenGuWuBtnView()
	self:OpenActivitySceneCd(ACTIVITY_TYPE.GUILD_FB)                -- GUILD_FB  27   活动倒计时
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	GuildWGCtrl.Instance:Close()

	GuildWGData.Instance:SetHideShiLianRemind(true)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ShiLian)

	MainuiWGCtrl.Instance:SetFbTimePos( 0,-14 )
    --MainuiWGCtrl.Instance:SetMianUITargetPos(nil,-135)
	self:HpDealWith()

	if not self.longhui_hurt_event then
		self.longhui_hurt_event = GlobalEventSystem:Bind(OtherEventType.GUILD_FB_LONGHUI_HURT, BindTool.Bind(self.OnBeautyHurt, self))
	end

	if not self.shouwei_hurt_event then
		self.shouwei_hurt_event = GlobalEventSystem:Bind(OtherEventType.GUILD_FB_SHOUWEI_HURT, BindTool.Bind(self.OnShouWeiHurt, self))
	end


	if not self.monster_enter_event then
		self.monster_enter_event = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER, BindTool.Bind(self.OnMonsterEnter, self))
	end

	--策划删除了所有雕像配置只剩下一个雕像默认1
	--self.shouhu_index = math.floor(math.random(1, 3))
	self.shouhu_index = 1
    GuildWGData.Instance:GetMiJingDianDoor(self.shouhu_index)

    ViewManager.Instance:Open(GuideModuleName.GuildShowHuRankView)
    -- ViewManager.Instance:AddMainUIFuPingChangeList(FuBenWGCtrl.Instance:GetGuildMiJingView())

	local view = FuBenWGCtrl.Instance:GetGuildShouHuRankView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)
end

function GuildMiJingSceneLogic:HpDealWith()
	local obj_list = Scene.Instance:GetMonsterList()
	for k,v in pairs(obj_list) do
		self:OnObjCreate(v)
	end
end

function GuildMiJingSceneLogic:Out()
	MainuiWGCtrl.Instance:SetFbTimePos( 0,54 )
	CommonFbLogic.Out(self)

	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)



	FuBenPanelWGCtrl.Instance:CloseCountDowmView()
	UiInstanceMgr.Instance:ColseFBStartDown()
	FuBenWGCtrl.Instance:CloseGuildMiJingView()
	GuildWGCtrl.Instance:CloseGuWuBtnView()
	GuildWGCtrl.Instance:CloseGuWuView()
    MainuiWGCtrl.Instance:SetMianUITargetPos(nil,0)
    ViewManager.Instance:Close(GuideModuleName.GuildShowHuRankView)
    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)

	if self.longhui_hurt_event then
		GlobalEventSystem:UnBind(self.longhui_hurt_event)
		self.longhui_hurt_event = nil
	end

	if self.shouwei_hurt_event then
		GlobalEventSystem:UnBind(self.shouwei_hurt_event)
		self.shouwei_hurt_event = nil
	end

	if self.monster_enter_event then
		GlobalEventSystem:UnBind(self.monster_enter_event)
		self.monster_enter_event = nil
	end
	self.obj_id_map = {}
	-- ViewManager.Instance:RemoveMainUIFuPingChangeList(FuBenWGCtrl.Instance:GetGuildMiJingView())

	local view = FuBenWGCtrl.Instance:GetGuildShouHuRankView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
end

function GuildMiJingSceneLogic:GetGuajiPos()
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		local pos_x,pos_y = GuildWGData.Instance:GetGuildFBMonsterPath()
		if pos_x and pos_y then
	    	local main_role = Scene.Instance:GetMainRole()
	        main_role:SetDirectionByXY(pos_x,pos_y)
    	end
    end)

	local pos_x,pos_y = GuildWGData.Instance:GetMjGuajiPos()
	return pos_x,pos_y
end

function GuildMiJingSceneLogic:CanGetMoveObj()
	return false
end

function GuildMiJingSceneLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

-- 更新守卫位置 并前往所在地
function GuildMiJingSceneLogic:OnAutoMoveToGuardPos(target_x, target_y)
	self.guard_x = target_x
	self.guard_y = target_y
	Scene.Instance:ClearAllOperate()
	GuajiWGCtrl.Instance:StopGuaji()

    local sence_id = Scene.Instance:GetSceneId()

	local role = Scene.Instance:GetMainRole()
	local role_x,role_y = role:GetLogicPos()
	if  role_x == target_x and role_y == target_y then
    	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
        GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
    	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end)
		GuajiWGCtrl.Instance:MoveToPos(sence_id ,target_x, target_y)
    end
end

function GuildMiJingSceneLogic:OnObjCreate(obj)
	if not obj then return end

    if obj:IsTower() then
    	local follow_ui = obj:GetFollowUi()
    	if follow_ui then
       		follow_ui:AddShieldRule(ShieldRuleWeight.High, function ()
				return false
			end)
			follow_ui:Show()
		end

		local vo = obj:GetVo()
		local index = GuildWGData.Instance:GetGuildFbIdMap(vo.monster_id)
		if index then
			self.obj_id_map[index] = obj
		end
    end

	if not obj:IsMainRole() and obj:IsRole() then
		local follow_ui = obj:GetFollowUi()
		follow_ui:GetHpBar():AddShieldRule(ShieldRuleWeight.High, function()
			return true
		end)
	end
end

function GuildMiJingSceneLogic:OnBeautyHurt()
	if  GuajiCache.target_obj ~= nil and GuajiCache.target_obj == Scene.Instance:GetObj(GuajiCache.target_obj_id) then
        return
	end

	local main_role = Scene.Instance:GetMainRole()
	local end_type = MoveCache.GetEndType()
	if main_role ~= nil and (GuajiWGCtrl.Instance:CheckCanPick() or main_role:GetIsGatherState()) then
		return
	end

	if GuajiCache.guaji_type == GuajiType.Auto then
		local pos_x, pos_y = GuildWGData.Instance:GetBeautyPos()
		local gcl = GuajiWGCtrl.Instance
	    gcl:SetMoveToPosCallBack(function ()
	    	gcl:ClearAllOperate()
	    	gcl:ClearGuajiCache()
	        gcl:SetGuajiType(GuajiType.Auto)
	    end)
		gcl:MoveToPos(Scene.Instance:GetSceneId(), pos_x, pos_y, 3) --仙女前方两个格子
	end
end

function GuildMiJingSceneLogic:OnShouWeiHurt(shou_wei_index, blood)
	local obj = self.obj_id_map[shou_wei_index]
	if obj and not obj:IsDeleted() then
		-- if obj.draw_obj then
		-- 	local bottom_point = obj.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
		-- 	if not IsNil(bottom_point) then
		-- 		FightText.Instance:ShowBeHurt(blood, nil, bottom_point)
		-- 	end
		-- end

		local data = {}
		data.fighttype = FIGHT_TYPE.NORMAL
		data.blood = blood
		HUDManager.Instance:ShowHurtEnter(obj, data)
	end
end

-- 活动时间(昨天加今天去，无语)
function GuildMiJingSceneLogic:OpenActivitySceneCd(act_type)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY
		if flag then
			FuBenPanelWGCtrl.Instance:SetCountDowmType( nil ,GameEnum.FU_BEN_ZHUNBEI )
		else
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end

		local time = activity_info.next_time
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(time,flag, flag and GameEnum.FU_BEN_ZHUNBEI or GameEnum.FU_BEN_OUT_SCENE)
	end
end

function GuildMiJingSceneLogic:OnMonsterEnter(monster_vo)
    if nil ~= GuajiCache.target_obj and not GuajiCache.target_obj:IsDeleted() then
        if Scene.Instance:IsEnemy(GuajiCache.target_obj) then
            return
        end
    end

    local main_role = Scene.Instance:GetMainRole()
	if main_role:IsFightState() then return end
	
	local monster_obj = Scene.Instance:GetObjectByObjId(monster_vo.obj_id)
    if not monster_obj then return end
    if not Scene.Instance:IsEnemy(monster_obj) then return end

	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, true, true)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	GuajiCache.target_obj = monster_obj
	MoveCache.is_move_scan = true
	GuajiWGCtrl.Instance:MoveToObj(monster_obj)
end