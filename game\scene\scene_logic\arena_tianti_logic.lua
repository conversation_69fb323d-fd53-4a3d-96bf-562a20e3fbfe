require("game/field1v1/field_head_panel")
ArenaTianTiSceneLogic = ArenaTianTiSceneLogic or BaseClass(CommonFbLogic)

function ArenaTianTiSceneLogic:__init()
	self.view = FieldHeadPanel.New(GuideModuleName.FieldHeadPanel)
	self.data = ArenaTianTiWGData.Instance

	self.update_status_event = nil
end

function ArenaTianTiSceneLogic:__delete()
	self:CleanCreatCheckTimer()
	self.view:DeleteMe()
	self.view = nil
	self.data = nil
	self.is_ignore_load = nil
end

-- 进入场景
function ArenaTianTiSceneLogic:Enter(old_scene_type, new_scene_type, is_ignore_load)
	ViewManager.Instance:CloseAll()
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self.field_add_robot_info = false
	self:CleanSetRobotState()
	self.is_ignore_load = is_ignore_load
	MainuiWGCtrl.Instance:ShowMainuiMenu(false)
	ArenaTianTiWGData.Instance:SetRoleLevel(RoleWGData.Instance.role_vo.level)
	MainuiWGCtrl.Instance.view:SetBtnLevel(false)
 	MainuiWGCtrl.Instance:SetCrossServerUI(false)
	MainuiWGCtrl.Instance:SetCrossServerTaskUI(false)
	MainuiWGCtrl.Instance:SetJoystickShowState(false)
	ViewManager.Instance:AddCanInactiveView(GuideModuleName.MainUIView)
	self.view:Open()

	self.update_status_event = GlobalEventSystem:Bind(Field1v1Type.FIELD_STATUS_CHANGE, BindTool.Bind(self.UpdataStatus, self))
	self.obj_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjectCreate, self))
	self:UpdataStatus()
	self:CheckSceneObjCreate()
end

function ArenaTianTiSceneLogic:Out(old_scene_type, new_scene_type)
	RobertManager.Instance:Stop()
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)
	self.data.scene_status = -1
	self.is_ignore_load = nil
	self.view:Close()
	MainuiWGCtrl.Instance:SetCrossServerUI(true)
	MainuiWGCtrl.Instance:SetCrossServerTaskUI(true)
	MainuiWGCtrl.Instance:SetJoystickShowState(true)
	ViewManager.Instance:RemoveCanInactiveView(GuideModuleName.MainUIView)

	if self.update_status_event then
		GlobalEventSystem:UnBind(self.update_status_event)
		self.update_status_event = nil
	end

	if self.obj_create_event then
		GlobalEventSystem:UnBind(self.obj_create_event)
		self.obj_create_event = nil
	end

	if ArenaTianTiWGData.Instance.is_fail_select then
		return
	end

	if old_scene_type ~= new_scene_type then
		MainCameraFollow.enabled = true
		MainCameraFollow.FieldOfView = 60
	end
end

function ArenaTianTiSceneLogic:CleanSetRobotState()
	self.field_data_ready = false
	self.field_create_obj_ready = false
	self.field_start_fight = false
end

function ArenaTianTiSceneLogic:UpdataStatus()
	if self.view == nil or nil == self.data or self.data.scene_status < 0 then
		return
	end

	local user_info = self.data.scene_user_list
	local status = self.data.scene_status
	local next_time = self.data.scene_next_time
	if status == FIELD1V1_STATUS.PREPARE then
		self:CheckSceneObjCreate()
		self.view:Flush(0, "prepare_time", {time = next_time - 0.5})
	else
		self.view:Flush(0, "prepare_time", {time = 0})
		if status == FIELD1V1_STATUS.PROCEED then
			self.view:Flush(0, "star_img", {is_show = true})
			self.view:Flush(0, "out_time", {time = next_time})
			self:StartFight()
		elseif status == FIELD1V1_STATUS.OVER then
			self.view:StopViewTimeCD()
		end
	end
	
	self.view:SetData(user_info)
end

function ArenaTianTiSceneLogic:CheckSceneObjCreate()
	if nil == self.data then
		return
	end

	local list_role = Scene.Instance:GetRoleList()
	for k,v in pairs(list_role) do
		self.field_create_obj_ready = true
		self.field_data_ready = self.data.scene_status == FIELD1V1_STATUS.PREPARE or self.data.scene_status == FIELD1V1_STATUS.PROCEED
		break
	end

	if self.field_create_obj_ready then
		self:SetFieldRoleDirection()
	end

	self:GetRoleListInfo()
end

function ArenaTianTiSceneLogic:OnObjectCreate(obj)
	if not obj:IsMainRole() and obj:IsRole() then
		self.field_create_obj_ready = true
		self:CheckSceneObjCreate()
	end
end

function ArenaTianTiSceneLogic:SetFieldRoleDirection()
	if self.data == nil or (self.data.scene_status ~= FIELD1V1_STATUS.PREPARE
		and self.data.scene_status ~= FIELD1V1_STATUS.PROCEED) then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local main_role_logic_pos_x, main_role_logic_pos_y = main_role:GetLogicPos()
	local list_role = Scene.Instance:GetRoleList()
	for k,v in pairs(list_role) do
		v:SetDirectionByXY(main_role_logic_pos_x, main_role_logic_pos_y)
		local x, y = v:GetLogicPos()
		main_role:SetDirectionByXY(x, y)
		break
	end
end

function ArenaTianTiSceneLogic:GetRoleListInfo()
	if not self.field_data_ready or not self.field_create_obj_ready or self.field_add_robot_info then
		return
	end

	self:SetFieldRoleDirection()
	local main_role = Scene.Instance:GetMainRole()
	local list_role = Scene.Instance:GetRoleList()
	for k,v in pairs(list_role) do
		self:AddRobotInfo(main_role, v)
		break
	end

	self:CleanSetRobotState()
end

function ArenaTianTiSceneLogic:AddRobotInfo(main_role, main_role2)
	local hurt = self.data.scene_user_list[1].capability or 1
	local hurt1 = self.data.scene_user_list[2].capability or 1
	local damage, damage1 = 1, 1
	if hurt >= hurt1 then
		damage = hurt/hurt1 >= 1.2 and hurt/hurt1 or 1.2
		damage1 = hurt1/hurt >= 0.8 and 0.8 or hurt1/hurt
	else
		damage = hurt1/hurt >= 0.8 and 0.8 or hurt1/hurt
		damage1 = hurt/hurt1 >= 1.2 and hurt/hurt1 or 1.2
	end

	main_role:SetRobot(true)
	local role_robert1 = {}
	role_robert1.id = main_role.vo.role_id
	role_robert1.name = main_role.vo.name
	role_robert1.ai_type = "active_attack"
	role_robert1.side = 0
	role_robert1.born_x = main_role.vo.pos_x
	role_robert1.born_y = main_role.vo.pos_y
	role_robert1.relive_x = main_role.vo.pos_x
	role_robert1.relive_y = main_role.vo.pos_y
	role_robert1.relive_cd = 60
	role_robert1.sex = main_role.vo.sex
	role_robert1.relive_y = main_role.vo.pos_y
	role_robert1.prof = main_role.vo.prof
	role_robert1.move_speed = main_role.vo.move_speed
	role_robert1.angle = 90
	role_robert1.max_hp = main_role.vo.max_hp
	role_robert1.hp = main_role.vo.hp
	role_robert1.min_gongji = math.ceil(damage * 0.06 * main_role2.vo.max_hp)
	role_robert1.gongji = math.ceil(damage * 0.06 * main_role2.vo.max_hp)
	role_robert1.skill_id_list = SkillWGData.Instance:GetRobertSkillList(main_role.vo.sex, main_role.vo.prof)
	role_robert1.skill_cd = 0.6
	role_robert1.atk_range = 10
	role_robert1.aoe_range = 8
	role_robert1.born_say = ""

	local role_robert2 = {}
	role_robert2.id = main_role2.vo.role_id
	role_robert2.name = main_role2.vo.name
	role_robert2.ai_type = "active_attack"
	role_robert2.side = 1
	role_robert2.born_x = main_role2.vo.pos_x
	role_robert2.born_y = main_role2.vo.pos_y
	role_robert2.relive_x = main_role2.vo.pos_x
	role_robert2.relive_y = main_role2.vo.pos_y
	role_robert2.relive_cd = 60
	role_robert2.sex = main_role2.vo.sex
	role_robert2.relive_y = main_role2.vo.pos_y
	role_robert2.prof = main_role2.vo.prof
	role_robert2.move_speed = main_role2.vo.move_speed
	role_robert2.angle = -48.814
	role_robert2.max_hp = main_role2.vo.max_hp
	role_robert2.hp = main_role2.vo.hp
	role_robert2.min_gongji = math.ceil(damage1 * 0.06 * main_role.vo.max_hp)
	role_robert2.gongji = math.ceil(damage1 * 0.06 * main_role.vo.max_hp)
	role_robert2.skill_id_list = SkillWGData.Instance:GetRobertSkillList(main_role2.vo.sex, main_role2.vo.prof)
	role_robert2.skill_cd = 0.6
	role_robert2.atk_range = 10
	role_robert2.aoe_range = 8
	role_robert2.born_say = ""

	RobertManager.Instance:AddRoleRobert(main_role, main_role2, role_robert1, role_robert2)

	self.field_add_robot_info = true
	-- print_error("----【竞技场】玩家-初始化---", hurt, role_robert1.max_hp, role_robert1.gongji, math.ceil(role_robert2.max_hp / role_robert1.gongji))
	-- print_error("----【竞技场】机器-初始化---", hurt1, role_robert2.max_hp, role_robert2.gongji, math.ceil(role_robert1.max_hp / role_robert2.gongji))
end

function ArenaTianTiSceneLogic:CleanCreatCheckTimer()
    if self.check_creat_timer then
        GlobalTimerQuest:CancelQuest(self.check_creat_timer)
        self.check_creat_timer = nil
    end
end

function ArenaTianTiSceneLogic:StartFight()
	if self.is_check_cd then
		return
	end

	if not self.field_add_robot_info then
		self.is_check_cd = true
		self:CleanCreatCheckTimer()
		self.check_creat_timer = GlobalTimerQuest:AddTimesTimer(function()
									self.is_check_cd = false
									self:CheckSceneObjCreate()
									self:StartFight()
								end, 0.2, 1)
		return
	end

	if self.field_start_fight then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local list_role = Scene.Instance:GetRoleList()
	local fight_end_callback = function ()
		ArenaTiantiWGCtrl.Instance:EndFightArna()
		if self.view ~= nil and self.view:IsLoaded() then
			self.view:HideOverBtn()
		end
	end

	local die_robot_list = {}
	if self.data.scene_user_list[1].capability >= self.data.scene_user_list[2].capability then
		for k,v in pairs(list_role) do
			table.insert(die_robot_list, v.vo.role_id)
		end
	else
		table.insert(die_robot_list, main_role.vo.role_id)
	end

	RobertManager.Instance:StartFight(die_robot_list, fight_end_callback)
	RobertManager.Instance:SetPause(false)
	local other_cfg = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfg()
	local role_base_info = RoleWGData.Instance.role_vo

	if self.view ~= nil and self.view:IsLoaded() then
		self.view.node_list.over_btn:SetActive(role_base_info.level >= other_cfg.skip_battle_level)
	end

	self.field_start_fight = true
end

function ArenaTianTiSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

-- 是否可以使用气血药
function ArenaTianTiSceneLogic:CanUseHpDrug()
	return false
end

function ArenaTianTiSceneLogic:CanMove()
	return ArenaTianTiWGData.Instance:Is1v1Proceed()
end

-- 角色是否是敌人
function ArenaTianTiSceneLogic:IsRoleEnemy(target_obj, main_role)
	return ArenaTianTiWGData.Instance:Is1v1Proceed()
end

-- 获取挂机打怪的敌人
function ArenaTianTiSceneLogic:GetGuiJiMonsterEnemy()
	if not ArenaTianTiWGData.Instance:Is1v1Proceed() then
		return nil
	end

	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	return Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
end

-- 可以点击地面
function ArenaTianTiSceneLogic:IsCanClickGround()
	return false
end

-- 是否可以操作摇杆移动
function ArenaTianTiSceneLogic:CanPlayJoystickDoMove()
	return false
end

function ArenaTianTiSceneLogic:IsSetAutoGuaji()
	return true
end

function ArenaTianTiSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end
