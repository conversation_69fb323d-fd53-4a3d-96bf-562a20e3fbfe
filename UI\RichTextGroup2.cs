﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

namespace Nirvana
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using UnityEngine;
    using UnityEngine.UI;

    /// <summary>
    /// The rich text group to extend the <see cref="FlowLayoutGroup2"/>, used as
    /// the rich text.
    /// </summary>
    [AddComponentMenu("Nirvana/UI/Layout/Rich Text Group2")]
    [RequireComponent(typeof(FlowLayoutGroup2))]
    public sealed class RichTextGroup2 : MonoBehaviour
    {
        [SerializeField]
        private Text textPrefab;

        private RectTransform rectTransform;
        private FlowLayoutGroup2 flowLayout;
        private List<GameObject> childrens = new List<GameObject>();
        private float lastRowWidth = 0;

                // 使用对象池
        private static Transform PoolTransform;
        private static Action<GameObject> BackPoolCallback;
        private static int NewLineCacheCount = 0;
        private static List<GameObject> NewLineCache = new List<GameObject>();
        private static int TextItemCacheCount = 0;
        private static List<GameObject> TextChache = new List<GameObject>();

        public static void ClearPool()
        {
            PoolTransform = null;
            BackPoolCallback = null;
            NewLineCacheCount = 0;
            NewLineCache.Clear();
            TextItemCacheCount = 0;
            TextChache.Clear();
        }

        public static void SetPool(Transform poolTransform, Action<GameObject> backPoolCallback)
        {
            PoolTransform = poolTransform;
            BackPoolCallback = backPoolCallback;
        }

        private static void PushPool(List<GameObject> childrens)
        {
            if (null == PoolTransform || null == childrens)
            {
                return;
            }

            foreach (var child in childrens)
            {
                if (child.name == "RichTextItem")
                {
                    if (TextItemCacheCount < 30)
                    {
                        TextItemCacheCount++;
                        TextChache.Add(child);
                        child.SetActive(false);
                        child.transform.SetParent(PoolTransform, false);
                    }
                    else
                    {
                        GameObject.Destroy(child);
                    }
                }
                else if (child.name == "RichTextNewLine")
                {
                    if (NewLineCacheCount < 30)
                    {
                        NewLineCacheCount++;
                        NewLineCache.Add(child);
                        child.SetActive(false);
                        child.transform.SetParent(PoolTransform, false);
                    }
                    else
                    {
                        GameObject.Destroy(child);
                    }
                }
               else
               {
                    if (null != BackPoolCallback)
                    {
                        BackPoolCallback(child);
                    }
                    else
                    {
                        GameObject.Destroy(child);
                    }
               }
            }

            childrens.Clear();
        }

        private static GameObject PopPool(string type)
        {
            GameObject gameObj = null;
            if (type == "RichTextItem" && TextItemCacheCount > 0)
            {
                --TextItemCacheCount;
                gameObj = TextChache[TextItemCacheCount];
                gameObj.SetActive(true);
                gameObj.transform.SetLocalPosition(0.0f, 0.0f, 0.0f);
                TextChache.RemoveAt(TextItemCacheCount);
            }
            else if (type == "RichTextNewLine" && NewLineCacheCount > 0)
            {
                --NewLineCacheCount;
                gameObj = NewLineCache[NewLineCacheCount];
                gameObj.SetActive(true);
                gameObj.transform.SetLocalPosition(0.0f, 0.0f, 0.0f);
                NewLineCache.RemoveAt(NewLineCacheCount);
            }

            return gameObj;
        }

        /// <summary>
        /// Clear this rich text group.
        /// </summary>
        public void Clear()
        {
            // 有指定池，则回池
            if (null != PoolTransform)
            {
                PushPool(this.childrens);
            }
            else
            {
                foreach (var child in this.childrens)
                {
                    child.transform.SetParent(null, false);
                    GameObject.Destroy(child);
                }
                this.childrens.Clear();
            }

            if (this.flowLayout == null)
            {
                this.flowLayout = this.GetComponent<FlowLayoutGroup2>();
            }

            this.flowLayout.LastRowWidth = 0;
            this.lastRowWidth = 0;
        }


        /// <summary>
        /// Add text to the rich text group.
        /// </summary>
        public void AddText(string text)
        {
            // Get the components.
            if (this.rectTransform == null)
            {
                this.rectTransform = this.GetComponent<RectTransform>();
            }

            if (this.flowLayout == null)
            {
                this.flowLayout = this.GetComponent<FlowLayoutGroup2>();
            }

            //LayoutRebuilder.ForceRebuildLayoutImmediate(this.rectTransform);
            // UpdateLayout();

            var font = this.textPrefab.font;
            font.RequestCharactersInTexture(
                text, this.textPrefab.fontSize, this.textPrefab.fontStyle);
            var workingRowWidth = this.flowLayout.WorkingRowWidth;

            var lineText = new StringBuilder();
            var lineWidth = 0;
            var tag = string.Empty;
            var tagName = string.Empty;
            for (int i = 0; i < text.Length; ++i)
            {
                var c = text[i];
                // Try to parse the rich text.
                if (c == '<')
                {
                    if (i + 1 < text.Length && text[i + 1] != '/')
                    {
                        var end = text.IndexOf('>', i + 1);
                        if (end >= 0)
                        {
                            tag = text.Substring(i + 1, end - i - 1);
                        }

                        var keyIndex = tag.IndexOf('=');
                        if (keyIndex >= 0)
                        {
                            tagName = tag.Substring(0, keyIndex);
                        }
                        else
                        {
                            tagName = tag;
                        }

                        lineText.Append('<');
                        lineText.Append(tag);
                        lineText.Append('>');
                        i = end;
                        continue;
                    }
                    else if (i + 1 < text.Length && text[i + 1] == '/')
                    {
                        var end = text.IndexOf('>', i + 1);
                        if (end >= 0)
                        {
                            tag = text.Substring(i + 2, end - i - 2);
                        }

                        lineText.Append("</");
                        lineText.Append(tag);
                        lineText.Append('>');

                        tag = string.Empty;
                        tagName = string.Empty;
                        i = end;
                        continue;
                    }
                }

                // Try to add this character to the flow layout.
                if (c == '\n')
                {
                    if (!string.IsNullOrEmpty(tagName))
                    {
                        lineText.Append("</");
                        lineText.Append(tagName);
                        lineText.Append(">");
                    }

                    this.AddTextObject(lineText.ToString(), lineWidth);
                    this.lastRowWidth = 0;
                    this.AddNewLine();
                    lineText = new StringBuilder();
                    lineWidth = 0;

                    if (!string.IsNullOrEmpty(tag))
                    {
                        lineText.Append("<");
                        lineText.Append(tag);
                        lineText.Append(">");
                    }

                    continue;
                }

                CharacterInfo info;
                if (!font.GetCharacterInfo(
                    c,
                    out info,
                    this.textPrefab.fontSize,
                    this.textPrefab.fontStyle))
                {
                    Debug.LogWarningFormat(
                        "Can not get character info: {0}, {1}",
                        c,
                        font.fontNames);
                }

                var freeSpace = workingRowWidth - this.lastRowWidth;
                if (Mathf.Approximately(freeSpace, 0.0f))
                {
                    freeSpace = workingRowWidth;
                    this.lastRowWidth = 0.0f;
                }
                if (lineWidth + info.advance >= freeSpace)
                {
                    if (!string.IsNullOrEmpty(tagName))
                    {
                        lineText.Append("</");
                        lineText.Append(tagName);
                        lineText.Append(">");
                    }
                    this.AddTextObject(lineText.ToString(), lineWidth);
                    this.lastRowWidth = 0;
                    //LayoutRebuilder.ForceRebuildLayoutImmediate(this.rectTransform);
                    // UpdateLayout();
                    lineText = new StringBuilder();
                    lineWidth = 0;
                    if (!string.IsNullOrEmpty(tag))
                    {
                        lineText.Append("<");
                        lineText.Append(tag);
                        lineText.Append(">");
                    }
                }

                lineText.Append(c);
                lineWidth += info.advance;
            }

            if (lineText.Length > 0)
            {
                this.AddTextObject(lineText.ToString(), lineWidth);
            }
        }

        /// <summary>
        /// Add an object to the rich text group.
        /// </summary>
        public void AddObject(GameObject go)
        {
            // Get the components.
            if (this.rectTransform == null)
            {
                this.rectTransform = this.GetComponent<RectTransform>();
            }

            if (this.flowLayout == null)
            {
                this.flowLayout = this.GetComponent<FlowLayoutGroup2>();
            }

            go.transform.SetParent(this.flowLayout.transform, false);
            var rectT = go.gameObject.GetComponent<RectTransform>();
            //LayoutRebuilder.ForceRebuildLayoutImmediate(rectT);
            var childWidth = rectT.sizeDelta.x;
            var text = go.GetComponentInChildren<Text>();
            if (text != null)
            {
                var text_w = GetTextBtnWidth(text, 10f);
                childWidth = Mathf.Max(childWidth, text_w);

            }
            if (childWidth > 0)
            {
                UpdateRowLastWidth(childWidth);
                this.childrens.Add(go);
            }
        }

        /// <summary>
        /// Add a new line to the rich text group.
        /// </summary>
        public void AddNewLine()
        {
            // Get the components.
            if (this.rectTransform == null)
            {
                this.rectTransform = this.GetComponent<RectTransform>();
            }

            if (this.flowLayout == null)
            {
                this.flowLayout = this.GetComponent<FlowLayoutGroup2>();
            }

            // LayoutRebuilder.ForceRebuildLayoutImmediate(this.rectTransform);
            // UpdateLayout();

            var workingRowWidth = this.flowLayout.WorkingRowWidth;

            var lastRowWidth = this.flowLayout.LastRowWidth;
            var freeSpace = workingRowWidth - lastRowWidth - 0.001f;
            freeSpace = Mathf.Max(freeSpace, 0.0f);
            var gameObj = PopPool("RichTextNewLine");
            if (null == gameObj)
            {
                gameObj = new GameObject("NewLine", typeof(RectTransform));
            }
            var layoutElement = gameObj.AddComponent<LayoutElement>();
            layoutElement.preferredWidth = freeSpace;

            gameObj.transform.SetParent(this.flowLayout.transform, false);
            this.childrens.Add(gameObj);
        }

        private void AddTextObject(string text, float width)
        {
            if (string.IsNullOrEmpty(text) || width <= 0)
            {
                return;
            }

            var gameObj = PopPool("RichTextItem");
            Text textCom = null;
            if (null != gameObj)
            {
                textCom = gameObj.GetComponent<Text>();
            }

            if (null == textCom)
            {
                textCom = GameObject.Instantiate(this.textPrefab, this.transform, false);
                textCom.name = "RichTextItem";
                gameObj = textCom.gameObject;
            }
            else
            {
                textCom.rectTransform.sizeDelta = this.textPrefab.rectTransform.sizeDelta;
                textCom.fontSize = this.textPrefab.fontSize;
                textCom.alignment = this.textPrefab.alignment;
                textCom.font = this.textPrefab.font;
            }
            textCom.text = text;
            textCom.horizontalOverflow = HorizontalWrapMode.Overflow;
            textCom.verticalOverflow = VerticalWrapMode.Overflow;

           // var preOutLine = this.textPrefab.GetComponent<Outline>();
           //if (preOutLine != null)
           // {
           //     var curOL = gameObj.GetOrAddComponent<Outline>();
           //     curOL.enabled = true;
           // }
           // else
           // {
           //     var curOL = gameObj.GetComponent<Outline>();
           //     if (curOL)
           //         curOL.enabled = false;
           // }

            textCom.transform.SetParent(this.flowLayout.transform, false);
            this.childrens.Add(gameObj);
            UpdateRowLastWidth(width);
        }

        private float GetTextBtnWidth(Text textPre, float off_x = 0)
        {
            var font = textPre.font;
            var text = textPre.text;
            font.RequestCharactersInTexture(
                text, textPre.fontSize, textPre.fontStyle);
             var lineWidth = off_x;
            for (int i = 0; i < text.Length; ++i)
            {
                var c = text[i];

                // Try to parse the rich text.
                if (c == '<')
                {
                    if (i + 1 < text.Length && text[i + 1] != '/')
                    {
                        var end = text.IndexOf('>', i + 1);
                        i = end;
                        continue;
                    }
                    else if (i + 1 < text.Length && text[i + 1] == '/')
                    {
                        var end = text.IndexOf('>', i + 1);
                        i = end;
                        continue;
                    }
                }
                CharacterInfo info;
                if (!font.GetCharacterInfo(
                    c,
                    out info,
                    textPre.fontSize,
                    textPre.fontStyle))
                {
                    Debug.LogWarningFormat(
                        "Can not get character info: {0}, {1}",
                        c,
                        font.fontNames);
                }
                lineWidth += info.advance;
            }
            return lineWidth;
        }

        private void UpdateRowLastWidth(float width)
        {
            if (this.flowLayout)
            {
                this.lastRowWidth += (width + this.flowLayout.Spacing.x);
                if ( this.lastRowWidth > this.flowLayout.WorkingRowWidth)
                    this.lastRowWidth = width;
            }

        }

        private void UpdateLayout()
        {
            if (this.flowLayout)
            {
                // this.flowLayout.UpdateLayoutHorizontal();
                // this.flowLayout.UpdateLayoutVertical();
            }
        }
    }
}
