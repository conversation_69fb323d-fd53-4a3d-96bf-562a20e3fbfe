--------------------------------------------------------------------------------
--	从开服助力那里复制一个开服嗨点过来
--------------------------------------------------------------------------------

function ServerActivityTabView:HiPoLoadCallBack()
    if self.hp_task_list == nil then
		local bundle, asset = "uis/view/open_server_activity_ui_prefab", "layout_high_point_task_cell"
		self.hp_task_list = AsyncBaseGrid.New()
		self.hp_task_list:CreateCells({
			col = 2,
			change_cells_num = 1,
			list_view = self.node_list.hp_task_list,
			assetBundle = bundle,
			assetName = asset,
			itemRender = ServerHighPointTaskRender
		})
		self.hp_task_list:SetStartZeroIndex(false)
	end

    self.hp_top_reward_list = AsyncListView.New(HighPointRwardItemRender, self.node_list["hp_top_reward_list"])

    self:InitHighPointActInfo()

    self.hp_cur_select_tog_index = 1
    self.node_list["hp_tog"..  self.hp_cur_select_tog_index].toggle.isOn = true

    for i = 1, 3 do
        self.node_list["hp_tog"..  i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickHpTogItem, self, i))
        local name_str = Language.Activity.HPTogName[i]
        self.node_list["hp_tog_text" .. i].text.text = name_str
        self.node_list["hp_tog_text_hl" .. i].text.text = name_str
    end
end

function ServerActivityTabView:OnClickHpTogItem(index, is_on)
    if is_on and nil ~= index then
        self.hp_cur_select_tog_index = index
        self:FlushHPTaskList()
    end
end

function ServerActivityTabView:HiPoReleaseCallBack()
    if self.hp_task_list then
        self.hp_task_list:DeleteMe()
        self.hp_task_list = nil
    end

    -- if self.hp_reward_list then
    --     self.hp_reward_list:DeleteMe()
    --     self.hp_reward_list = nil
    -- end

    if self.hp_top_reward_list then
        self.hp_top_reward_list:DeleteMe()
        self.hp_top_reward_list = nil
    end

    if CountDownManager.Instance:HasCountDown("high_point_act_time") then
        CountDownManager.Instance:RemoveCountDown("high_point_act_time")
    end

    self.hp_cur_select_tog_index = nil
end

function ServerActivityTabView:InitHighPointActInfo()
    local all_cfg = OpenServerAssistWGData.Instance:GetHighPointAllCfg()
    local other_cfg = all_cfg and all_cfg.other and all_cfg.other[1]
    local act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT
    local res_time = ActivityWGData.Instance:GetActivityResidueTime(act_type)
    self.node_list.hp_act_time.text.text = string.format(Language.Activity.ActTime ,TimeUtil.FormatSecondDHM6(res_time))
    if res_time > 0 then
        CountDownManager.Instance:AddCountDown("high_point_act_time", BindTool.Bind(self.UpdateHighPointCountDown, self),
                                                nil, nil, res_time, 1)
    end

    -- 当日提醒红点
    local get_today_remind = OpenServerAssistWGData.Instance:GetActivityTodayRemind(act_type)
    if get_today_remind == 0 then
        OpenServerAssistWGData.Instance:SetActivityTodayRemind(act_type)
        RemindManager.Instance:Fire(RemindName.OSA_High_Point)
    end
end

function ServerActivityTabView:UpdateHighPointCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 and self.node_list.hp_act_time then
        self.node_list.hp_act_time.text.text = string.format(Language.Activity.ActTime ,TimeUtil.FormatSecondDHM6(valid_time))
	end
end

function ServerActivityTabView:HighPointShowIndexCallBack()
    self:PlayHighPointAnim()
end

function ServerActivityTabView:HiPoOnFlush(param_t)
    self:FlushHighPointPanel()
end

function ServerActivityTabView:FlushHighPointPanel()
    local cur_hp = OpenServerAssistWGData.Instance:GetHighPointCount()
    -- self.node_list.hp_cur_value.text.text = string.format(Language.Activity.HighPointCurValue, cur_hp)

    self.node_list.hp_cur_value.text.text = cur_hp
    self:FlushHPRewardList()
    self:FlushHPTaskList()
end

function ServerActivityTabView:FlushHPRewardList()
    local reward_list = OpenServerAssistWGData.Instance:GetHighPointRewardList()
    self.hp_top_reward_list:SetDataList(reward_list)

    self:FlushHpSliderValue(reward_list)
    -- self.hp_reward_list:SetDataList(reward_list)
end

function ServerActivityTabView:FlushHpSliderValue(reward_list)
    local cur_hp = OpenServerAssistWGData.Instance:GetHighPointCount()
    local slider_value = 0
    local cell_value = 1 / 6

    if cur_hp <= 0 then
        slider_value = 0
    elseif cur_hp >= reward_list[#reward_list].need_hp then
        slider_value = 1
    else
        local last_value = 0
        for k, v in pairs(reward_list) do
            if cur_hp < v.need_hp then
                local diff = (1- (v.need_hp - cur_hp) / (v.need_hp - last_value)) * cell_value
                slider_value = cell_value * (k - 1) + diff
                break
            end

            last_value = v.need_hp
        end
    end

    self.node_list.hp_haidian_slider.slider.value = slider_value
end

function ServerActivityTabView:FlushHPTaskList()
    local task_list = OpenServerAssistWGData.Instance:GetHighPointTaskList()

    local target_data_list = {}
    for k, v in pairs(task_list) do
        if v.task_cfg.type == self.hp_cur_select_tog_index then
            table.insert(target_data_list, v)
        end
    end

    self.hp_task_list:SetDataList(target_data_list)
end

function ServerActivityTabView:PlayHighPointAnim()
    local tween_info = UITween_CONSTS.ServerActivityTab
    UITween.FakeHideShow(self.node_list["high_point_root"])
    UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["high_point_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

------------------------  ServerHighPointTaskRender -----------------------------------
ServerHighPointTaskRender = ServerHighPointTaskRender or BaseClass(BaseRender)
function ServerHighPointTaskRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_jump_view"], BindTool.Bind(self.OnClickJumpView, self))
end

function ServerHighPointTaskRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local task_cfg = self.data.task_cfg
    local task_desc = string.format(task_cfg.task_desc, CommonDataManager.GetDaXie(task_cfg.param))
    self.node_list.task_desc.text.text = task_desc --string.format(Language.Activity.HighPointTaskDesc, task_desc, task_cfg.once_haidian_count)
    self.node_list.getcout.text.text = string.format(Language.Activity.HightCout, task_cfg.once_haidian_count)
    self.node_list.task_num.text.text =  self.data.finish_count .. "/" .. task_cfg.times_limit

    self.node_list.is_finish:SetActive(self.data.is_finish)
    self.node_list.btn_jump_view:SetActive(not self.data.is_finish)

    local res = self.data.is_finish and "a3_kfkh_xtsl_btn_ywc" or "a3_kfkh_xtsl_btn_qw"
    local bundle, asset = ResPath.GetRawImagesPNG(res)
    self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["bg"].raw_image:SetNativeSize()
	end)
end

function ServerHighPointTaskRender:OnClickJumpView()
    if IsEmptyTable(self.data) then
        return
    end

    local task_cfg = self.data.task_cfg
    if task_cfg.task_type == 9 and task_cfg.jump_view == "husong" then --护送美人
        if NewTeamWGData.Instance:GetIsMatching() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
            return
        end

        if YunbiaoWGData.Instance:GetIsHuShong() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
            return
        end

        TaskGuide.Instance:CanAutoAllTask(false)
        ViewManager.Instance:Close(GuideModuleName.OpenServerAssistView)
        ActIvityHallWGCtrl.Instance:DoHuSong()
        return
    else
        if task_cfg.activity_id > 0 then
            local is_open = ActivityWGData.Instance:GetActivityIsOpen(task_cfg.activity_id)
            if not is_open then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
        end

        FunOpen.Instance:OpenViewNameByCfg(task_cfg.jump_view)
    end
end
---------------------------------------------------------------------------------

------------------------  HighPointRwardItemRender ----------------------------------
local ICON_POS_X = 0
local ICON_POS_Y = 4
HighPointRwardItemRender = HighPointRwardItemRender or BaseClass(BaseRender)

function HighPointRwardItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClickHandler, self))
end

function HighPointRwardItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.need_desc.text.text = self.data.need_hp

    local reward_list = OpenServerAssistWGData.Instance:GetHighPointRewardList()
    local is_big_prize =self.data.reward_grade >= #reward_list
    self.node_list.type_1:SetActive(not is_big_prize)
    self.node_list.type_2:SetActive(is_big_prize)

    local bundle, asset
    local other_cfg = OpenServerAssistWGData.Instance:GetHighPointOtherCfg()
    if not other_cfg then
        return
    end

    for i = 1, 2 do
        self.node_list["bg_has_get_" .. i]:SetActive(self.data.get_state == REWARD_STATE_TYPE.FINISH)
        self.node_list["bg_can_get_" .. i]:SetActive(self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH)
        self.node_list["remind_" .. i]:SetActive(self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH)

        local pos_x, pos_y = ICON_POS_X, ICON_POS_Y
        local scale = 1
        if is_big_prize then
            bundle, asset = ResPath.GetItem(other_cfg.big_prize)

            if other_cfg.display_pos and other_cfg.display_pos ~= "" then
                local pos_list = string.split(other_cfg.display_pos, "|")
                pos_x = tonumber(pos_list[1]) or pos_x
                pos_y = tonumber(pos_list[2]) or pos_y
            end

            scale = other_cfg.display_scale or scale
        else
            bundle, asset = ResPath.GetCommonIcon("a3_kfkh_xljq_bx")
        end

        self.node_list["bg_icon_" .. i].image:LoadSprite(bundle, asset, function()
            self.node_list["bg_icon_" .. i].image:SetNativeSize()
        end)

        RectTransform.SetAnchoredPositionXY(self.node_list["bg_icon_" .. i].rect, pos_x, pos_y)
        RectTransform.SetLocalScale(self.node_list["bg_icon_" .. i].rect, scale)
    end

    -- self.node_list.is_undone:SetActive(self.data.get_state == REWARD_STATE_TYPE.UNDONE)
end

function HighPointRwardItemRender:OnClickHandler()
    ServerActivityWGCtrl.Instance:OpenServerHighPointRewardView()
end
---------------------------------------------------------------------------------
