-----------------------------  师徒 -- 师徒   -------------------------------------
function SocietyView:InitInfoView()
	self.is_show_flag = false

	self.is_have_load_shifu_model = false
	self.is_have_load_shixiongdi_model = false



	self.st_loadingbar = self.node_list["prog_jingyan"]
	self.st_loadingbar.slider.value = 0
	self.st_loadingbar_text = self.node_list["lab_progvalue_text"]
	self.st_loadingbar_text.text.text = "26/100"


	self.node_list["tog_show_wing"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSysSetting, self))
	XUI.AddClickEventListener(self.node_list["btn_shitu_exp_uplevel"], BindTool.Bind(self.UpMasterLevel, self))
	XUI.AddClickEventListener(self.node_list["btn_master_tips"], BindTool.Bind(self.OnMasterTips, self))
	XUI.AddClickEventListener(self.node_list["btn_baishi_1"], BindTool.Bind(self.OpenBaiShiInfo, self))
	XUI.AddClickEventListener(self.node_list["btn_baishiteam_1"], BindTool.Bind(self.OpenTeamInfo, self,true))
	XUI.AddClickEventListener(self.node_list["btn_baishi_2"], BindTool.Bind(self.OpenTeamInfo, self,false))





	self:CreateRoleModelInfo()
	self:CreateSelfRoleModelInfo()
end
function SocietyView:CreateSelfRoleModelInfo()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	
	self.node_list.lbl_relation.text.text = vo.name
	self.node_list.lbl_relation_online.text.text = Language.Master.MasterOnLine
	self.mine_wing_id = vo.wing_appeid
	self:CreateRoleWingModelInfo()
end
function SocietyView:CreateRoleModelInfo()
	local shitu_info = MasterWGData.Instance:GetShituInfo()
	if shitu_info.shifu_id and  shitu_info.shifu_id > 0 then
		self.node_list.img_kongque_1:SetActive(false)
		self.node_list.lbl_relation_1:SetActive(true)
		self.node_list.btn_baishiteam_1:SetActive(true)
		self.node_list.btn_text_1.text.text = Language.Master.ShituBtnTxt[3]
		self.node_list.lbl_relation_1.text.text = shitu_info.shifu_role_name
		if shitu_info.shifu_is_online == 1 then
			self.node_list.lbl_relation_online_1.text.text = Language.Master.MasterOnLine
		else
			self.node_list.lbl_relation_online_1.text.text =  Language.Master.MasterNotOnLine

		end
		if 	not self.is_have_load_shifu_model  then
			self.is_have_load_shifu_model = true
			BrowseWGCtrl.Instance:BrowRoelInfo(shitu_info.shifu_id, BindTool.Bind(self.DrawRole1, self))
		end
	else

		self.is_have_load_shifu_model = false
		self.node_list.btn_text_1.text.text = Language.Master.ShituBtnTxt[2]
		self.node_list.btn_baishiteam_1:SetActive(false)
		self.node_list.img_kongque_1:SetActive(true)
		self.node_list.lbl_relation_1:SetActive(false)
	end 

	if shitu_info.shixiongdi_id and  shitu_info.shixiongdi_id > 0 then
		self.node_list.img_kongque_2:SetActive(false)
		self.node_list.lbl_relation_2:SetActive(true)
		self.node_list.btn_baishi_2:SetActive(true)
		if shitu_info.shixiongdi_is_online == 1 then
			self.node_list.lbl_relation_online_2.text.text = Language.Master.MasterOnLine
		else
			self.node_list.lbl_relation_online_2.text.text =  Language.Master.MasterNotOnLine
		end

		self.node_list.lbl_relation_2.text.text = shitu_info.shixiongdi_name
		if shitu_info.shixiongdi_is_online == 1 then
			self.node_list.lbl_relation_online_2.text.text = Language.Master.MasterOnLine
		else
			self.node_list.lbl_relation_online_2.text.text =  Language.Master.MasterNotOnLine

		end
		if 	not self.is_have_load_shixiongdi_model  then
			self.is_have_load_shixiongdi_model = true
			BrowseWGCtrl.Instance:BrowRoelInfo(shitu_info.shixiongdi_id, BindTool.Bind(self.DrawRole2, self))
		end
	else
		self.is_have_load_shixiongdi_model = false
		self.node_list.btn_baishi_2:SetActive(false)
		self.node_list.img_kongque_2:SetActive(true)
		self.node_list.lbl_relation_2:SetActive(false)
	end
end
function SocietyView:CreateRoleWingModelInfo()
end

--师傅
function SocietyView:DrawRole1(vo)
end

function SocietyView:CreateRoleWingModelInfo1()
end

function SocietyView:DrawRole2(vo)
end

function SocietyView:CreateRoleWingModelInfo2()
end


function SocietyView:OnClickSysSetting(isOn)
	self.node_list.Checkmark:SetActive(isOn)
	
	self:CreateRoleWingModelInfo()
	self:CreateRoleWingModelInfo1()
	self:CreateRoleWingModelInfo2()
end

function SocietyView:DeleteInfoView()
	if self.shitu_grid_view then
		self.shitu_grid_view:DeleteMe()
		self.shitu_grid_view = nil
	end
	if self.st_loadingbar then
		self.st_loadingbar = nil
	end
	self.success_display = nil

	if self.lbl_advanced_zhandoiuli then
		self.lbl_advanced_zhandoiuli:DeleteMe()
		self.lbl_advanced_zhandoiuli = nil
	end

	if self.lbl_next_zhandoiuli then
		self.lbl_next_zhandoiuli:DeleteMe()
		self.lbl_next_zhandoiuli = nil
	end

	if self.master_view then
		self.master_view:DeleteMe()
		self.master_view = nil
	end

	self.st_loadingbar = nil
	self.st_loadingbar_text = nil
end

function SocietyView:CreateShituGridView()
	

end
function SocietyView:OpenTeamInfo(bool)
	local shitu_info = MasterWGData.Instance:GetShituInfo()
	local team_list = SocietyWGData.Instance:GetTeamMemberList()
	if bool then
		if shitu_info.shifu_is_online == 1 then
			if shitu_info.shifu_id > 0 then 
				for k,v in pairs(team_list) do
					if v.role_id == shitu_info.shifu_id then
						NewTeamWGCtrl.Instance:Open()
						return
					end
				end
				MasterWGCtrl.Instance:OpenInvitationTeamPanel(shitu_info.shifu_id)
			end
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Master.FbFriendsOnline)
		end
	else
		if shitu_info.shixiongdi_is_online == 1 then
			for k,v in pairs(team_list) do
					if v.role_id == shitu_info.shixiongdi_id then
						NewTeamWGCtrl.Instance:Open()
						return
					end
				end
			if shitu_info.shixiongdi_id > 0 then 
				MasterWGCtrl.Instance:OpenInvitationTeamPanel(shitu_info.shixiongdi_id)
			end
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Master.FbFriendsOnline)
		end
	end
end

--拜师 解除
function SocietyView:OpenBaiShiInfo()
	local shitu_info = MasterWGData.Instance:GetShituInfo()
	if shitu_info.shifu_id > 0 then
		local alert_window = self:GetAlertWindow()
		alert_window:SetLableString(string.format(Language.Master.RemoveRelationTips, shitu_info.shifu_role_name))
		alert_window:SetOkFunc(function ()
			MasterWGCtrl.Instance:SendRemoveRelation(shitu_info.shifu_id)
			SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.INFO)
		end)
		alert_window:Open()
	else
		MasterWGCtrl.Instance:OpenSelectMasterView()
	end
end
function SocietyView:FlushInfoView()
	local shutu_datalist = MasterWGData.Instance:GetShituDataList()
	local shitu_info = MasterWGData.Instance:GetShituInfo()
	self:CreateRoleModelInfo()
	local max_exp = MasterWGData:GetShituLevelExp(shitu_info.shitu_level)
	local has_exp = RoleWGData.Instance.role_vo.shitu_exp
	local per = math.min(shitu_info.shitu_exp / max_exp * 100, 100)
	self.node_list.prog_jingyan.slider.value = per
	self.old_st_per = per
	local per_str = shitu_info.shitu_exp
	local add_value = math.min(has_exp, max_exp - shitu_info.shitu_exp)
	if add_value > 0 then
		per_str = per_str ..string.format(Language.Master.AddShituExpNum,add_value)
	end
	per_str = per_str .. "/" .. max_exp
	self.node_list["lab_progvalue_text"].text.text = per_str
	local add_per = (shitu_info.shitu_exp + add_value) / max_exp * 100
	self.node_list["label_shitu_exp"].text.text = has_exp
	self.node_list["lbl_shitu_level"].text.text = "Lv." .. shitu_info.shitu_level

	local cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").shitu_uplevel
	local uplevel_attr = {}
	for k,v in pairs(cfg) do
		if v.shitu_level == shitu_info.shitu_level then
			uplevel_attr = v
		end
	end
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	uplevel_attr = AttributeMgr.GetAttributteByClass(uplevel_attr)
	self.node_list["lbl_gongji_val"].text.text = uplevel_attr.gong_ji
	self.node_list["lbl_pojia_val"].text.text = uplevel_attr.po_jia
	self.node_list["lbl_qixue_val"].text.text = uplevel_attr.max_hp
	self.node_list["lbl_fangyu_val"].text.text = uplevel_attr.fang_yu
	self.node_list["lbl_fafangyu_val"].text.text = uplevel_attr.fa_fang_yu
	local capability_num = AttributeMgr.GetCapability(uplevel_attr)
	self.node_list.shitu_power.text.text = capability_num
	local next_uplevel_attr = {}
	for k,v in pairs(cfg) do
		if v.shitu_level == shitu_info.shitu_level + 1 then
			next_uplevel_attr = v
		end
	end
	if next(next_uplevel_attr) then
		local next_capability = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(next_uplevel_attr))
		self.node_list.shitu_next_power.text.text = next_capability - capability_num
		self.node_list["img_capability_add"]:SetActive(true)
		self.node_list["shitu_next_power"]:SetActive(true)

	else
		XUI.SetButtonEnabled(self.node_list.btn_shitu_exp_uplevel, false)
		self.node_list["img_capability_add"]:SetActive(false)
		self.node_list["shitu_next_power"]:SetActive(false)
		self.node_list["lab_progvalue_text"].text.text =  "--/--"
	end
end

function SocietyView:CreatCapability(num)
	
end

function SocietyView:SetNextCapability(num)

end

function SocietyView:MasterUpLevelSuc()
	
end

function SocietyView:UpMasterLevel()
	MasterWGCtrl.Instance:SendShituUpLevel()
end

function SocietyView:OnMasterTips()
end

----------------------------------------------------
-- ShituGridRender
----------------------------------------------------
ShituGridRender = ShituGridRender or BaseClass(BaseRender)
function ShituGridRender:__init()
	self.node_list["lbl_relation"].text.text = ""

	self.node_list["btn_baishi"].button:AddClickListener(BindTool.Bind1(self.OpenBaishi, self))
	self.display_view = self.node_list["display"]
end

function ShituGridRender:__delete()
	if self.role_display then
		self.role_display:DeleteMe()
		self.role_display = nil
	end
	
	self:DestoryAlertWindow()

	self.display_view = nil
end

function ShituGridRender:OnFlush()
	if nil == self.data then print_error("ShituGridRender data = nil") return end
	local btn_txt = ""
	local bg_path = ResPath.GetBigPainting("master_first_bg2")
	if self.data.role_id > 0 then
		btn_txt = Language.Master.ShituBtnTxt[3]
		self.node_list["lbl_relation"]:SetActive(true)
		BrowseWGCtrl.Instance:BrowRoelInfo(self.data.role_id, BindTool.Bind(self.DrawRole, self))
	else
		self.node_list["lbl_relation"]:SetActive(false)
		btn_txt = self.data.is_master and Language.Master.ShituBtnTxt[2] or Language.Master.ShituBtnTxt[1]
	end

	self.node_list["btn_text"].text.text = btn_txt
	self.node_list["btn_baishi"]:SetActive(self.data.is_master)

	self.node_list["img_kongque"]:SetActive(self.data.role_id <= 0)
end

function ShituGridRender:DrawRole(role_info)
end

function ShituGridRender:CreateSelectEffect()
end

function ShituGridRender:OpenBaishi()
	if nil == self.data then return end
	if self.data.role_id > 0 then
		local alert_window = self:GetAlertWindow()
		alert_window:SetLableString(string.format(Language.Master.RemoveRelationTips, self.data.role_name))
		alert_window:SetOkFunc(function ()
			MasterWGCtrl.Instance:SendRemoveRelation(self.data.role_id)
			SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.INFO)
		end)
		alert_window:Open()
	elseif self.data.is_master then
		MasterWGCtrl.Instance:OpenSelectMasterView()
	else
		local shoutu_level = MasterWGData.Instance:GetShouTuLevel()
		if shoutu_level > RoleWGData.Instance.role_vo.level then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Master.ShouTuLevelLimit, shoutu_level))
			return
		end
		ChatWGCtrl.Instance:SendChannelChat(0, string.format(Language.Master.FindApprenticeTxt, RoleWGData.Instance.role_vo.role_id, RoleWGData.Instance.role_vo.name, RoleWGData.Instance.role_vo.level), CHAT_CONTENT_TYPE.TEXT)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Master.ShoutuSendSuc)
	end
end