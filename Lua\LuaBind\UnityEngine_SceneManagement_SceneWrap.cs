﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_SceneManagement_SceneWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(UnityEngine.SceneManagement.Scene), null);
		<PERSON><PERSON>RegFunction("IsValid", IsValid);
		<PERSON><PERSON>("GetRootGameObjects", GetRootGameObjects);
		<PERSON><PERSON>unction("GetHashCode", GetHashCode);
		<PERSON><PERSON>ction("Equals", Equals);
		L<PERSON>RegFunction("New", _CreateUnityEngine_SceneManagement_Scene);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("handle", get_handle, null);
		<PERSON><PERSON>("path", get_path, null);
		<PERSON><PERSON>("name", get_name, set_name);
		<PERSON><PERSON>("isLoaded", get_isLoaded, null);
		<PERSON><PERSON>("buildIndex", get_buildIndex, null);
		<PERSON><PERSON>("isDirty", get_isDirty, null);
		<PERSON><PERSON>("rootCount", get_rootCount, null);
		L.RegVar("isSubScene", get_isSubScene, set_isSubScene);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_SceneManagement_Scene(IntPtr L)
	{
		UnityEngine.SceneManagement.Scene obj = new UnityEngine.SceneManagement.Scene();
		ToLua.PushValue(L, obj);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsValid(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
			bool o = obj.IsValid();
			LuaDLL.lua_pushboolean(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRootGameObjects(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
				UnityEngine.GameObject[] o = obj.GetRootGameObjects();
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 2)
			{
				UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
				System.Collections.Generic.List<UnityEngine.GameObject> arg0 = (System.Collections.Generic.List<UnityEngine.GameObject>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.GameObject>));
				obj.GetRootGameObjects(arg0);
				ToLua.SetBack(L, 1, obj);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.SceneManagement.Scene.GetRootGameObjects");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.SceneManagement.Scene arg0 = StackTraits<UnityEngine.SceneManagement.Scene>.To(L, 1);
			UnityEngine.SceneManagement.Scene arg1 = StackTraits<UnityEngine.SceneManagement.Scene>.To(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHashCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
			int o = obj.GetHashCode();
			LuaDLL.lua_pushinteger(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Equals(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)ToLua.CheckObject(L, 1, typeof(UnityEngine.SceneManagement.Scene));
			object arg0 = ToLua.ToVarObject(L, 2);
			bool o = obj.Equals(arg0);
			LuaDLL.lua_pushboolean(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_handle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			int ret = obj.handle;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index handle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_path(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			string ret = obj.path;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index path on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_name(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			string ret = obj.name;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index name on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isLoaded(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			bool ret = obj.isLoaded;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isLoaded on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_buildIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			int ret = obj.buildIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index buildIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isDirty(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			bool ret = obj.isDirty;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isDirty on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rootCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			int ret = obj.rootCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rootCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isSubScene(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			bool ret = obj.isSubScene;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isSubScene on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_name(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.name = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index name on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isSubScene(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.SceneManagement.Scene obj = (UnityEngine.SceneManagement.Scene)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isSubScene = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isSubScene on a nil value");
		}
	}
}

