GaoZhanFbLogic = GaoZhanFbLogic or BaseClass(CommonFbLogic)

function GaoZhanFbLogic:__init()
	self.open_view = false
end

function GaoZhanFbLogic:__delete()

end

function GaoZhanFbLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
end

function GaoZhanFbLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function GaoZhanFbLogic:Out()
	CommonFbLogic.Out(self)
	if RoleWGData.Instance.role_vo.level > 130 then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Daily)
	end
end


function GaoZhanFbLogic:IsRoleEnemy(target_obj, main_role)
	if target_obj:GetType() == SceneObjType.Role then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end


-- 是否是挂机打怪的敌人
function GaoZhanFbLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

function GaoZhanFbLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
end