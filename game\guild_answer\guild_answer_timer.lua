GuildAnswerStartTimer = GuildAnswerStartTimer or BaseClass(SafeBaseView)

function GuildAnswerStartTimer:__init()
	self:AddViewResource(0, "uis/view/guild_answer_ui_prefab", "layout_guild_answer_info")
	self:AddViewResource(0, "uis/view/guild_answer_ui_prefab", "layout_answer_timer")
    self.active_close = false
    self.is_safe_area_adapter = true
    self.view_name = "GuildAnswerStartTimer"
end

function GuildAnswerStartTimer:LoadCallBack()
	self.node_list["guild_answer_panel"]:SetActive(true)
	self.node_list["level_scene_panel"]:SetActive(false)

	XUI.AddClickEventListener(self.node_list["go_dati_btn"], BindTool.Bind(self.ClickGoDaTiBtn,self))
	XUI.AddClickEventListener(self.node_list["btn_pass"], BindTool.Bind(self.OnClickBtnPass,self))
	XUI.AddClickEventListener(self.node_list["btn_open_pass"], BindTool.Bind(self.OnClickBtnPass,self))
	XUI.AddClickEventListener(self.node_list["btn_eat"], BindTool.Bind(self.OnClickEat,self))

    MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list["btn_pass"])

	if self.need_flush_end then
		self:FlushAnswerEndTime()
	end

	self:FlushPassTimes()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
		self:InitCallBack()
	end)
end

function GuildAnswerStartTimer:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("answer_level_scene_timer") then
		CountDownManager.Instance:RemoveCountDown("answer_level_scene_timer")
	end

	self.is_out_fb = true

    if self.obj then
        self.obj:SetActive(false)
    end
end

function GuildAnswerStartTimer:ReleaseCallBack()
	if self.quest then
		CountDown.Instance:RemoveCountDown(self.quest)
		self.quest = nil
	end

	if self.answer_rank_list then
		self.answer_rank_list:DeleteMe()
		self.answer_rank_list = nil
	end

	self.load_complete = nil
	self.is_should_flush = nil

	if CountDownManager.Instance:HasCountDown("guild_answer_end_time") then
		CountDownManager.Instance:RemoveCountDown("guild_answer_end_time")
	end

	if CountDownManager.Instance:HasCountDown("answer_level_scene_timer") then
		CountDownManager.Instance:RemoveCountDown("answer_level_scene_timer")
	end
	
	if self.node_list["btn_pass"] then
		self.node_list["btn_pass"].gameObject.transform:SetParent(self.root_node_transform, false)
	end

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
end

function GuildAnswerStartTimer:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
	self:ShowOrHideRoot(is_open)
end

function GuildAnswerStartTimer:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()

	if self.node_list["task_root"] then
		self.obj = self.node_list["task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

	if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function GuildAnswerStartTimer:OnFlush(param_t)
	for k,v in pairs(param_t or {"all"}) do
		if k == "FlushBtnPassRed" then
			self:FlushBtnPassRed()
		elseif k == "FlushPassBtnState" then
			self:FlushPassBtnState()
		elseif k == "FlushPassTimes" then
			self:FlushPassTimes()
		elseif k == "FlushGoDaTiBtn" then
			self:FlushGoDaTiBtn()
		elseif k == "ShowOrHideAnswerRankList" then
			self:ShowOrHideAnswerRankList(v)
		else
			self:OnFlushView()
		end
	end
end

function GuildAnswerStartTimer:OnFlushView()
	local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
			
	if question_info.question_state == 0 and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) then
		-- if not self.quest then
		self.node_list["answer_prepare"]:SetActive(true)
		if self.quest then
			CountDown.Instance:RemoveCountDown(self.quest)
			self.quest = nil
		end
		local total_time = question_info.question_state_change_timestamp - TimeWGCtrl.Instance:GetServerTime()
		if total_time > 0 then
			self:UpdateStartFightTimer(TimeWGCtrl.Instance:GetServerTime() ,question_info.question_state_change_timestamp )
			self.quest = CountDown.Instance:AddCountDown(total_time, 1, BindTool.Bind(self.UpdateStartFightTimer, self),BindTool.Bind(self.EndOrCompleteStartFightTimer, self))
		end
	else
		if question_info.question_state == 1 and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) then
			if question_info.question_end_timestamp == 0 then
				self.node_list["answer_end2"]:SetActive(true)
				-- self.node_list["answer_end1"]:SetActive(true)
				self.node_list["answering"]:SetActive(false)
			else
				self.node_list["answering"]:SetActive(true)
			end
			self.node_list["answer_prepare"]:SetActive(false)
		else
			if question_info.question_end_timestamp == 0 then
				self.node_list["answer_end1"]:SetActive(false)
			end
			self.node_list["answering"]:SetActive(false)
			-- self.node_list["answer_end1"]:SetActive(true)
			self.node_list["answer_end2"]:SetActive(false)
		end
	end
	
	self:FlushGoDaTiBtn()
	self:FlushPassBtnState()
	self:FlushAnswerTimeCount()

	self:FlushDinnerPanel()
	self:FlushPassTimes()
	self:FlushRankPanel()
end

function GuildAnswerStartTimer:ShowOrHideRoot(enable)
	if self.node_list["root"] then
		self.node_list["root"]:SetActive(enable)
	end

	if self.node_list.task_root then
		self.node_list.task_root:SetActive(enable)
	end

	MainuiWGCtrl.Instance:SetFBNameState(enable, Language.GuildAnswer.AnswerFBName)
end

function GuildAnswerStartTimer:FlushGoDaTiBtn()
	local show_go_dati_btn = false
	local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
	if question_info.question_state == 1 and question_info.question_end_timestamp ~= 0 
		and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) then
		show_go_dati_btn = true
	end
	local chat_open = ChatWGCtrl.Instance:IsOpenTransmitPopView()
	self.node_list["go_dati_btn"]:SetActive(show_go_dati_btn and not chat_open)

	self:FlushDatiDescStateInfo()
end

function GuildAnswerStartTimer:ShowGoDaTiBtn()
	if self.node_list["go_dati_btn"] then
		local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
		if question_info.question_state == 1 and question_info.question_end_timestamp ~= 0 
			and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) then
			self.node_list["go_dati_btn"]:SetActive(true)
		else
			self.node_list["go_dati_btn"]:SetActive(false)
		end
	end
end

function GuildAnswerStartTimer:FlushDatiDescStateInfo()
	local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()

	if question_info.question_state == 1 and question_info.question_end_timestamp ~= 0 then
		local max_answer_num = GuildAnswerWGData.Instance:GetMaxAnswerNum()
		self.node_list.desc_dati_state.text.text = string.format(Language.GuildAnswer.AnswerProgress, question_info.question_index, max_answer_num)
	end
end

function GuildAnswerStartTimer:FlushAnswerEndTime()
	if not self:IsOpen() then
		return
	end

	if not self:IsLoaded() then
		self.need_flush_end = true
		return
	end

	if not self.answer_end then
		self.node_list["answering"]:SetActive(false)
		self.node_list["answer_end2"]:SetActive(true)
		--GlobalTimerQuest:AddDelayTimer(function()
		--	self.node_list["answer_end2"]:SetActive(false)
		--	self.node_list["answer_end1"]:SetActive(true)
		--end, 2)
		self.answer_end = true
	end
end


function GuildAnswerStartTimer:ClearEndTimeFlag()
	self.answer_end = nil
end

function GuildAnswerStartTimer:FlushPassBtnState()
	if not self:IsOpen() or not self:IsLoaded() then
		return
	end

	local is_chuan_gong_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
	self.node_list["btn_pass"]:SetActive(is_chuan_gong_state)
	if is_chuan_gong_state then
		-- self:FlushBtnPassEffect()
		self:FlushBtnPassRed()
	end
end

function GuildAnswerStartTimer:UpdateStartFightTimer(elapse_time, total_time)
	if not self.node_list["time_obj"] then return end
	if total_time - elapse_time > 0 then
		self.node_list["time_obj"].text.text = TimeUtil.FormatSecond(total_time - elapse_time, 2)
		--self.node_list["time_obj"].transform.localScale = Vector3(2,2,2)
		--self.node_list["time_obj"].transform:DOScale(1, 0.2):SetEase(DG.Tweening.Ease.Linear)
	end
end

function GuildAnswerStartTimer:EndOrCompleteStartFightTimer()
	self:Flush()
end

function GuildAnswerStartTimer:SetTime(total_time)
	self.total_time = total_time
end

function GuildAnswerStartTimer:OnClickBtnPass()
	local is_chuan_gong_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
	if not is_chuan_gong_state then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.ChuanGongNotOpen)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local is_chuan_gong = main_role:IsChuanGong()
	if is_chuan_gong then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.IsChuanGong)
		return
	end
	
	ViewManager.Instance:Open(GuideModuleName.GuildPass)
end

function GuildAnswerStartTimer:OnClickEat()
	local main_role = Scene.Instance:GetMainRole()
	local is_chuan_gong = main_role:IsChuanGong()
	if is_chuan_gong then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.IsChuanGong)
		return
	end

	local player_info = GuildAnswerWGData.Instance:GetQuestionPlayerInfo()
	if IsEmptyTable(player_info) then return end
	if player_info.is_gather == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.IsHasEat)
		return
	end

	local gather_id = GuildAnswerWGData.Instance:GetGatherId()
	local obj = Scene.Instance:SelectMinDisGather(gather_id)
	if obj == nil then
		local other_cfg = GuildAnswerWGData.Instance:GetGuildQuestionOtherCfg()
		MoveCache.end_type = MoveEndType.Gather
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			local obj = Scene.Instance:SelectMinDisGather(gather_id)
			if obj then
				obj:OnClick()
			end
		end)
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), other_cfg.gather_x, other_cfg.gather_y, 5)
	else
		MoveCache.end_type = MoveEndType.Gather
		MoveCache.target_obj = obj
		local posx, posy = obj:GetLogicPos()
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), posx, posy, 5)
	end
end

-- function GuildAnswerStartTimer:OnClickBtnTask(isOn)
-- 	self.right_panel = GuildAnswerStartTimer.LEFTPANEL.DINNER
-- 	self.node_list["dinner_panel"]:SetActive(isOn)
-- 	self.node_list["answer_rank"]:SetActive(not isOn)
-- 	self:FlushDinnerPanel()
-- 	self:FlushPassTimes()
-- end

function GuildAnswerStartTimer:FlushDinnerPanel()
	local player_info = GuildAnswerWGData.Instance:GetQuestionPlayerInfo()
	local exp = player_info and player_info.exp or 0
	local guild_gongxian = player_info and player_info.guild_gongxian or 0
	local value = SkillWGData.Instance:GetExpEfficiencyInfo()
	local value_per = value and value.world_extra_percent or 0
	local off_line_exp, total_addition = ExpAdditionWGData.Instance:GetCurOffLineExpExtra()
	local total_exp = exp * (1 + value_per + total_addition)
	total_exp = math.ceil(total_exp)
	self.node_list["txt_info3"].text.text = Language.GuildAnswer.PanelInfo3 .. ToColorStr(CommonDataManager.ConverExp(total_exp), COLOR3B.DEFAULT_NUM)
	self.node_list["txt_info4"].text.text = Language.GuildAnswer.PanelInfo4 .. ToColorStr(guild_gongxian, COLOR3B.DEFAULT_NUM)

	local is_gather = player_info and player_info.is_gather or -1
	local str = is_gather == 1 and ToColorStr(Language.GuildAnswer.HasComplete, COLOR3B.DEFAULT_NUM) or ToColorStr(Language.GuildAnswer.NoComplete, COLOR3B.D_RED)
	self.node_list["txt_info2"].text.text = Language.GuildAnswer.PanelInfo2 .. str
	local other_cfg = GuildAnswerWGData.Instance:GetGuildQuestionOtherCfg()
	self.node_list["scene_show"].text.text = other_cfg.scene_show

end

function GuildAnswerStartTimer:FlushPassTimes()
	local str = GuildAnswerWGData.Instance:HasGetPassExp() and ToColorStr(Language.GuildAnswer.HasComplete, COLOR3B.DEFAULT_NUM) or ToColorStr(Language.GuildAnswer.NoComplete, COLOR3B.D_RED)
	self.node_list["txt_info1"].text.text = Language.GuildAnswer.PanelInfo1 .. str
    -- self:FlushBtnPassEffect()
	self:FlushBtnPassRed()
end

function GuildAnswerStartTimer:FlushRankPanel()
	local guild_rank_info = GuildAnswerWGData.Instance:GetGuildRankInfo()
	
	if not self.answer_rank_list then
		self.answer_rank_list = AsyncListView.New(AnswerRankRender, self.node_list["answer_rank_list"])
	end

	self.answer_rank_list:SetDataList(guild_rank_info)
	local rank, guild_score = GuildAnswerWGData.Instance:GetSelfGuildInfo()
	if IsEmptyTable(guild_rank_info) then
		self.node_list["txt_answer_rank"].text.text = Language.GuildAnswer.NoRank
		self.node_list["txt_answer_num"].text.text = string.format(Language.GuildAnswer.GuildRankNum, 0)
	else
		self.node_list["txt_answer_rank"].text.text = string.format(Language.GuildAnswer.GuildRankPanel, rank)
		self.node_list["txt_answer_num"].text.text = string.format(Language.GuildAnswer.GuildRankNum, guild_score)
	end
	
end

-- function GuildAnswerStartTimer:OnClickBtnTeam(isOn)
-- 	self.right_panel = GuildAnswerStartTimer.LEFTPANEL.RANK
-- 	self.node_list["dinner_panel"]:SetActive(not isOn)
-- 	self.node_list["answer_rank"]:SetActive(isOn)
-- 	self:FlushRankPanel()
-- end

-- function GuildAnswerStartTimer:FlushBtnPassEffect()
-- 	self.node_list["btn_pass_effect"]:SetActive(GuildAnswerWGData.Instance:HasBtnPassOperate())
-- end

function GuildAnswerStartTimer:FlushBtnPassRed()
	-- self.node_list["btn_pass_red"]:SetActive(GuildAnswerWGData.Instance:HasInvite())
	GuildAnswerWGData.Instance:IsShowInviteAnim()
	local flag = GuildAnswerWGData.Instance:GetShowInviteAnimFlag()
	self:ShowPassBtnAnim(flag)
end

function GuildAnswerStartTimer:ShowPassBtnAnim(flag)
	self.node_list["btn_pass_ani"]:SetActive(flag)
	if flag then
		UITween.MoveLoop(self.node_list["btn_pass_ani"], Vector2(-18,64), Vector2(-18,74), 0.9)
	else
		UITween.KillMoveLoop(self.node_list["btn_pass_ani"])
	end
end

-- function GuildAnswerStartTimer:ShrinkButtonsValueChange(isOn)
--     local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
-- 	if isOn or menu_ison then

-- 	else

-- 	end
-- end

-- function GuildAnswerStartTimer:MainMenuIconChangeEvent(isOn)
-- 	if isOn then

-- 	else

-- 	end
-- end

function GuildAnswerStartTimer:FlushAnswerTimeCount()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
	if is_open then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
		local act_time = act_info.next_time
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local time = act_time - server_time 
		if time > 0 then
			if CountDownManager.Instance:HasCountDown("guild_answer_end_time") then
				CountDownManager.Instance:RemoveCountDown("guild_answer_end_time")
			end
			self:FinalUpdateTimeCallBack(0,time)
			CountDownManager.Instance:AddCountDown("guild_answer_end_time", BindTool.Bind(self.FinalUpdateTimeCallBack,self), BindTool.Bind(self.FinalCompleteTimeCallBack,self), nil, time,1)
		end
	end
end

function GuildAnswerStartTimer:FinalUpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.MSTime(time)
end

function GuildAnswerStartTimer:FinalCompleteTimeCallBack()

end

function GuildAnswerStartTimer:ClickGoDaTiBtn()
	ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[CHANNEL_TYPE.GUILD])
	MainuiWGCtrl.Instance:HideOrShowChatView(true)
	self.node_list["go_dati_btn"]:SetActive(false)
end

--倒计时离开副本
function GuildAnswerStartTimer:StartLevelSceneTimer()
	if not self:IsLoaded() then
		return
	end
	if CountDownManager.Instance:HasCountDown("answer_level_scene_timer") then
		CountDownManager.Instance:RemoveCountDown("answer_level_scene_timer")
	end
	self:UpdateLevelSceneTimer(0, 60)
	CountDownManager.Instance:AddCountDown("answer_level_scene_timer", BindTool.Bind(self.UpdateLevelSceneTimer,self), BindTool.Bind(self.CompleteLevelSceneTimer,self), nil, 60,1)
	self.node_list["level_scene_panel"]:SetActive(true)
end

function GuildAnswerStartTimer:UpdateLevelSceneTimer(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.MSTime(time)
	self.node_list["level_scene_time"].text.text = time_str
end

function GuildAnswerStartTimer:CompleteLevelSceneTimer()
	self.node_list["level_scene_panel"]:SetActive(false)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.GUILD_ANSWER_FB then
		 --当处于仙盟传功状态中，不让退出副本
	    local is_chuan_gong_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
	    local main_role = Scene.Instance:GetMainRole()
		local is_chuan_gong = main_role:IsChuanGong()
		if is_chuan_gong_state and is_chuan_gong then
			return
		end
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function GuildAnswerStartTimer:ShowOrHideAnswerRankList(state_info)
	if self.node_list.answer_rank_list_root then
		local start_num = state_info.state == true and 0 or 1
		local end_num = state_info.state == true and 1 or 0
		self.node_list.answer_rank_list_root.canvas_group:DoAlpha(start_num, end_num, 0.3)
	end
end

------------------------------------------------AnswerRankRender--------------------------------------------
AnswerRankRender = AnswerRankRender or BaseClass(BaseRender)

function AnswerRankRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.txt_rank.text.text = self.index >= 4 and self.index or ""
	self.node_list["txt_guild_name"].text.text = self.data.guild_name
	self.node_list["txt_answer_num"].text.text = GuildAnswerWGData.Instance:GetAnswerNumByScore(self.data.guild_score)
	local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")

	if self.index < 4 then    
		bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["img_rank"]:SetActive(true)
		self.node_list["img_rank"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
	else
		self.node_list["img_rank"]:SetActive(false)
    end

	self.node_list["bg"].image:LoadSprite(bg_bundle, bg_asset)

	-- if self.index <= 3 then
	-- 	self.node_list["txt_rank"].text.text = ""
	-- 	self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp".. self.index))
	-- 	self.node_list["img_rank"].image:SetNativeSize()
	-- 	self.node_list["img_rank"]:SetActive(true)
	-- else
	-- 	self.node_list["img_rank"]:SetActive(false)
	-- 	self.node_list["txt_rank"].text.text = self.index
	-- end
	-- self.node_list["txt_guild_name"].text.text = self.data.guild_name
	-- -- self.node_list["txt_answer_num"].text.text = string.format(Language.GuildAnswer.GuildRankNum, GuildAnswerWGData.Instance:GetAnswerNumByScore(self.data.guild_score))
	-- self.node_list["txt_answer_num"].text.text = GuildAnswerWGData.Instance:GetAnswerNumByScore(self.data.guild_score)
end