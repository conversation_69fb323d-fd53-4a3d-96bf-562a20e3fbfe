MarryBaiTangView = MarryBaiTangView or BaseClass(SafeBaseView)

function MarryBaiTangView:__init()
	self.view_layer = UiLayer.PopTop
	
	self:SetMaskBg(false)

	self:AddViewResource(0, "uis/view/wedding_ui_prefab", "layout_baitang_view")
end

function MarryBaiTangView:__delete()

end

function MarryBaiTangView:Release()

end

function MarryBaiTangView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_marry_tongyi"], BindTool.Bind(self.OnTongYiClick, self))
end

function MarryBaiTangView:ShowIndexCallBack()
	self.node_list["rich_tips"].text.text = self.rich_tips or ""
end

function MarryBaiTangView:SetLableString(str)
	self.rich_tips = str
end

function MarryBaiTangView:SetOkFunc(callback)
	self.ok_call_back = callback
end

function MarryBaiTangView:SetCancelFunc(callback)
	self.cancel_call_back = callback
end

--同意
function MarryBaiTangView:OnTongYiClick()
	if self.ok_call_back then
		self.ok_call_back()
		self.ok_call_back = nil
	end
	self.on_ok_click = true
	self:Close()
end

function MarryBaiTangView:CloseCallBack()
	if not self.on_ok_click and self.cancel_call_back then
		self.cancel_call_back()
		self.cancel_call_back = nil
	end
	self.on_ok_click = nil
end