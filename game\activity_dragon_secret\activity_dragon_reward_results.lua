DragonRewardResults = DragonRewardResults or BaseClass(SafeBaseView)

local ani_ten_flag_t = {}
local ANI_SPEED = 0.15
local MAX_COUNT = 50
local MAX_LINE_COUNT = 5

function DragonRewardResults:__init()
    self:SetMaskBg(true,true)
    self.view_layer = UiLayer.Pop
    self.view_name = "DragonRewardResults"
    self:AddViewResource(0, "uis/view/activity_dragon_secret_ui_prefab", "dragon_diydraw_result")
end

function DragonRewardResults:ReleaseCallBack()
    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end
    if self.cell_list then
        for i,v in ipairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = {}
    end

    self.click_delay = nil
end

function DragonRewardResults:LoadCallBack()
    --用于保存抽奖结果的格子
    self.cell_list = {}
    
    XUI.AddClickEventListener(self.node_list["list_mask"],BindTool.Bind(self.ImmPlayToBigReward,self))
    XUI.AddClickEventListener(self.node_list["btn_cancel"],BindTool.Bind(self.Close,self))
    XUI.AddClickEventListener(self.node_list["fish_btn"],BindTool.Bind(self.DrawAgain,self))

end

--初始化格子
local MAX_DELTA = 636
local SINGLE_DELTA = 105
local LINE_COUNT = 10
local ONE_LINE_DELTA = 350
function DragonRewardResults:FlushCell(id_list)
    if not id_list then
        return
    end

    local length = math.ceil(#id_list / 10)
    for i=1,MAX_LINE_COUNT do
        self.node_list['list_' .. i]:SetActive(length >= i)
    end

    if length == 1 then
        self.node_list.bg.rect.sizeDelta = Vector2(1380, ONE_LINE_DELTA)
    else
        self.node_list.bg.rect.sizeDelta = Vector2(1380, MAX_DELTA - SINGLE_DELTA * (5 - length))
    end

    for i=1,MAX_COUNT do
        if id_list[i] then
            if not self.cell_list[i] then
                self.cell_list[i] = DragonDrawRewardCell.New(self.node_list['single_cell_' .. i])
                self.cell_list[i]:SetAnimIndex(i)
            end
            self.cell_list[i]:SetAlpha(false)
            self.cell_list[i]:SetVisible(true)
            -- 刷新格子
            self.cell_list[i]:SetData(id_list[i])
        else
            if self.cell_list[i] then
                self.cell_list[i]:SetAlpha(false)
                self.cell_list[i]:SetVisible(false)
            end
        end
    end

    for i = (length - 1) * LINE_COUNT + 1,length * LINE_COUNT do
        self.node_list['single_cell_' .. i]:SetActive(id_list[i] ~= nil)
    end
end

function DragonRewardResults:CloseCallBack()
    if self.cell_list then --关闭的时候直接释放，因为跳过动画后显示会奇怪
        for i,v in ipairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = {}
    end
end

function DragonRewardResults:SetData(id_list)
    self.id_list = id_list
end

local Sort_Type = {
    [GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
    [GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
    [GameEnum.ITEM_BIGTYPE_GIF] = 8,
    [GameEnum.ITEM_BIGTYPE_OTHER] = 7,
    [GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}
function DragonRewardResults:SortItem(item_list)
    local item_cfg,item_type
    for i,v in ipairs(item_list) do
        item_cfg,item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
        v.color = item_cfg.color
        v.item_type = Sort_Type[item_type]
    end
    SortTools.SortDesc(item_list,"color","item_type")
    return item_list
end

--刷新数据
function DragonRewardResults:OnFlush(param)
    for i, v in pairs(param) do
        if i == "all" then
            self:FlushView()
        end
    end
end

function DragonRewardResults:FlushView()
    self.node_list.list_mask:SetActive(true)
    local gift_info = self.id_list
    -- gift_info = self:SortItem(gift_info)

    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end

    self:FlushCell(gift_info)
    --self:ChangeBlock(false)
    --local no_tween = DIYDrawWGData.Instance:GetSkipAnim() == 1

    -- if no_tween then
    --     self.tween_index = 1
    --     self:ImmPlayToBigReward()
    --else
        self.tween_index = 1
        self.tween_time_quest = GlobalTimerQuest:AddTimesTimer(function()
            if self.tween_index > MAX_COUNT then
                GlobalTimerQuest:CancelQuest(self.tween_time_quest)
                self.tween_time_quest = nil
                --self:ChangeBlock(true)
                return
            end
            if self.cell_list[self.tween_index - 1] and not self.cell_list[self.tween_index - 1]:GetCanPlayNext() then
                return
            end

            if self.cell_list[self.tween_index] then
                self.cell_list[self.tween_index]:DoAnim()
                self.cell_list[self.tween_index]:SetAlpha(true)
            end
            self.tween_index = self.tween_index + 1

            if self.tween_index >= #gift_info then
                self.node_list.list_mask:SetActive(false)
                return
            end

        end, ANI_SPEED, #gift_info*3)
    --end
    -- 刷新按钮
    self:FlushBtn()
end

-- 快速播放到下一个大奖
function DragonRewardResults:ImmPlayToBigReward()
    for i = self.tween_index, #self.cell_list do
        if self.id_list[i] then
            -- 如果是最后一个物品，需要隐藏遮罩
           
            -- if i == #self.id_list then
            --     self.node_list.list_mask:SetActive(false)
            --     self.tween_index = i
            -- end
            -- if self.id_list[i].reward_type ~= OAFishWGData.REWARD_TYPE.LOW then
            --     self.tween_index = i
            --     return
            -- end
            self.cell_list[i]:ImmShowItem()
        end
        self.tween_index = #self.id_list
        self.node_list.list_mask:SetActive(false)
    end
end

-- function DIYDrawReward:ChangeBlock(enable)
--     --for i=1,50 do
--     --    self.node_list['single_cell_' .. i].canvas_group.blocksRaycasts = enable
--     --end
-- end

function DragonRewardResults:DrawAgain()
    if self.click_delay then
        return
    end
    self.click_delay = true
    GlobalTimerQuest:AddDelayTimer(function()
        self.click_delay = nil
    end, 0.3)

    --获取点击的是哪个按钮
    local mode_type = ActivityDragonSecretWGData.Instance:SetOrGetDragonDrawIndex()

    --调用抽奖方法
    ActivityDragonSecretWGCtrl.Instance:ClickUse(mode_type)

    self:Close()
end

function DragonRewardResults:FlushBtn()
    local btn_index = ActivityDragonSecretWGData.Instance:SetOrGetDragonDrawIndex()
    if not btn_index then
        return
    end

    local other_cfg = ActivityDragonSecretWGData.Instance:GetQiTaCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if item_cfg ~= nil then
            self.node_list["fish_cost_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
            --物品道具的点击事件
            self.node_list["fish_cost_icon"].button:AddClickListener(BindTool.Bind(self.OnClickFishCostBtn, self))

            --拿到奖池物品剩余的总数
            local prize_sum = ActivityDragonSecretWGData.Instance:GetPrizeSum()
            --拿到背包的道具的总数
            local item_count = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
            --按钮1的默认次数
            local draw_number_ten = LOTTER_NUM_TYPE.TEN_NUMBER_DRAWS
            --按钮2的默认次数
            local repeatedly_draw_num = LOTTER_NUM_TYPE.DEFAULT_NUMBER 
            --抽奖需要的数量
            local lottery_number  
            if btn_index == 1 then
                if draw_number_ten >= prize_sum  then
                    lottery_number = other_cfg.cost_item_num * prize_sum
                    self:FlushBtnShow(prize_sum, lottery_number, item_count)
                else
                    lottery_number = other_cfg.cost_item_num * draw_number_ten
                    --   print_error("lottery_number",lottery_number)
                    self:FlushBtnShow(draw_number_ten, lottery_number, item_count)
                end

            elseif btn_index == 2 then
                if repeatedly_draw_num >= prize_sum then
                  --  print_error("item_count",item_count)
                    lottery_number = other_cfg.cost_item_num * prize_sum
                    self:FlushBtnShow(prize_sum, lottery_number, item_count)
                else
                    lottery_number = other_cfg.cost_item_num * repeatedly_draw_num
                 --   print_error("lottery_number",lottery_number)
                   self:FlushBtnShow(repeatedly_draw_num, lottery_number, item_count)
                end
            end
            
            if prize_sum <= 0 then
                self.node_list["fish_btn_txt"].text.text = string.format(Language.DragonSecret.DragonDrawTxt, 1)
            end
    end
end

function DragonRewardResults:FlushBtnShow(draw_num, lottery_number, item_count)
        local num_text = item_count .. "/" .. lottery_number
        local color = COLOR3B.D_GREEN
        if item_count >= lottery_number and item_count > 0 then
            color = COLOR3B.D_GREEN
        else
            color = COLOR3B.D_RED
        end
      --  local color = item_count > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list["fish_btn_cost"].text.text = ToColorStr(num_text, color)
        self.node_list["fish_btn_txt"].text.text = string.format(Language.DragonSecret.DragonBtnStr, draw_num)
end

function DragonRewardResults:OnClickFishCostBtn()
    local other_cfg = ActivityDragonSecretWGData.Instance:GetQiTaCfg()
    local item_id = other_cfg.cost_item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id })
end










------------列表格子
DragonDrawRewardCell = DragonDrawRewardCell or BaseClass(BaseRender)

function DragonDrawRewardCell:LoadCallBack()
    self.graphic_raycast = self.node_list.cell:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster))
    self.node_list.cell.image.enabled = false
end

function DragonDrawRewardCell:ReleaseCallBack()
    if self.base_cell then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self.graphic_raycast = nil
end

function DragonDrawRewardCell:SetAlpha(value)
    if self.graphic_raycast then
        self.graphic_raycast.enabled = value
    end
    self.view.canvas_group.alpha = value and 1 or 0
end

local scale1 = Vector3(1.4, 1.4, 1.4) --大小1
local scale2 = Vector3(0.9, 0.9, 0.9) --大小1
local effect_name = {

}
local start_effect_name = {
    [5] = "ui_jinjichenggong",
    [6] = "ui_jinjichenggong",
}

function DragonDrawRewardCell:GetCanPlayNext()
    return self.can_play_next or false
end


DragonDrawRewardCell.Time = 1
function DragonDrawRewardCell:DoAnim()
    self.can_play_next = false
    
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not cfg then
        return
    end
    local eff_level = cfg.color

    self.view.transform.localScale = scale1
    local scale_tween_2 = self.view.rect:DOScale(scale2, 0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InQuad)

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:AppendCallback(function ()
        self.can_play_next = true
        self.node_list.effect_attach.rect:SetAsFirstSibling()
        self:PlayStartEffect()
    end)
    self.sequence:Append(scale_tween_2)
    self.sequence:AppendCallback(function ()
        ani_ten_flag_t[#ani_ten_flag_t + 1] = true
        if not effect_name[eff_level] then
            self.node_list.effect_attach:SetActive(false)
            return
        end
        local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])

        self.node_list.effect_attach:SetActive(true)
        self.node_list.effect_attach:ChangeAsset(bundle,asset)
    end)

end

function DragonDrawRewardCell:ImmShowItem()
    self.can_play_next = true
    self:SetAlpha(true)
    self:FlushEffect()
end

function DragonDrawRewardCell:PlayStartEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local eff_level = cfg.color

    if not start_effect_name[eff_level] then
        self.node_list.effect_attach:SetActive(false)
        return
    end
    local bundle, asset = ResPath.GetEffectUi(start_effect_name[eff_level])

    self.node_list.effect_attach:SetActive(true)
    self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function DragonDrawRewardCell:FlushEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local eff_level = cfg.color

    if not effect_name[eff_level] then
        self.node_list.effect_attach:SetActive(false)
        return
    end

    local bundle = string.format("effects2/prefab/ui/%s_prefab", string.lower(effect_name[eff_level]))
    local asset = effect_name[eff_level]
     if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
         self.node_list.effect_attach.game_obj_attach.BundleName = nil
        self.node_list.effect_attach.game_obj_attach.AssetName = nil
     end
     self.node_list.effect_attach:SetActive(true)
     self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function DragonDrawRewardCell:OnFlush()
    if not self.data then
        return
    end
    self:FlushBaseCell()
    --self.node_list.cell.image.enabled = self.data.is_zhenxi
end

function DragonDrawRewardCell:FlushBaseCell()
    if not self.base_cell then
        self.base_cell = ItemCell.New(self.node_list["cell"])
        self.base_cell:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
    end
    self.base_cell:SetData({item_id = self.data.item_id})
end

function DragonDrawRewardCell:SetAnimIndex(index)
    self.anim_index = index
end