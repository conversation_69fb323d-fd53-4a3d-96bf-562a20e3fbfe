-- 鸭子对话面板
DuckRaceDialogView = DuckRaceDialogView or BaseClass(SafeBaseView)

function DuckRaceDialogView:__init()
	self.view_layer = UiLayer.Pop
	self.view_cache_time = 0

	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "duck_dialog_layout")
end

function DuckRaceDialogView:__delete()

end

function DuckRaceDialogView:OpenCallBack()
	ViewManager.Instance:AddCanInactiveView(GuideModuleName.MainUIView)
	ViewManager.Instance:AddCanInactiveView(GuideModuleName.DuckRaceFight)
	SafeBaseView.SetOnlyView(GuideModuleName.DuckDialog)
	GlobalTimerQuest:AddDelayTimer(function()
		self:Close()
	end, 5)
end

function DuckRaceDialogView:CloseCallBack()
	ViewManager.Instance:RemoveCanInactiveView(GuideModuleName.MainUIView)
	ViewManager.Instance:RemoveCanInactiveView(GuideModuleName.DuckRaceFight)
	SafeBaseView.SetOnlyView(nil)
end

function DuckRaceDialogView:SetData(duck_index, content)
	self.duck_index = duck_index -- 鸭子下标
	self.content = content    -- 对话内容
end

function DuckRaceDialogView:LoadCallBack()
	self.duck_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["duck_model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}

	self.duck_model:SetRenderTexUI3DModel(display_data)
	-- self.duck_model:SetUI3DModel(self.node_list["duck_model"].transform, nil, nil, false, MODEL_CAMERA_TYPE.BASE)
end

function DuckRaceDialogView:ReleaseCallBack()
	if self.duck_model then
		self.duck_model:DeleteMe()
		self.duck_model = nil
	end
end

function DuckRaceDialogView:OnFlush()
	-- 鸭子模型
	local monster_id = DuckRaceWGData.Instance:GetDuckMonsterId(self.duck_index)
	local mosnter_cfg = BossWGData.Instance:GetMonsterInfo(monster_id)
	local bundle, asset = ResPath.GetMonsterModel(mosnter_cfg.resid)
	self.duck_model:SetMainAsset(bundle, asset)

	-- 对话内容
	self.node_list["dialog_content"].text.text = self.content

	-- 鸭子名称
	self.node_list["duck_name"].text.text = string.format(Language.DuckRace.DuckNameInDarkView[self.duck_index],
		mosnter_cfg.name)
end
