MarryFbBuyTipsView = MarryFbBuyTipsView or BaseClass(SafeBaseView)

function MarryFbBuyTipsView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_jiehun_second_panel", {vector2 = Vector2(2, 42)})
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_buy_view")
end

function MarryFbBuyTipsView:__delete()

end

function MarryFbBuyTipsView:ReleaseCallBack()
	self.from = nil
end

function MarryFbBuyTipsView:LoadCallBack()
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClickCancel,self))
	self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSure,self))
end

function MarryFbBuyTipsView:ShowIndexCallBack()
	local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	local price = other_cfg.fb_buy_times_gold_cost
	local fb_info = MarryWGData.Instance:GetQyFbInfo()
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
	local cost_text = Language.Common.GoldText
	local can_buy_num = 0
	if fb_info ~= nil then
		can_buy_num = other_cfg.fb_buy_times_limit - fb_info.self_buy_jion_fb_times
	end

	if bind_gold_num >= price then
		cost_text = Language.Common.BindGoldText
	end 

	self.node_list["shengyu_num"].text.text = string.format(Language.Marry.ShengYuBuyCount,can_buy_num)
	if self.from == "role_buy" then
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
		if lover_id <= 0 then
			self.node_list["desc_text"].text.text = string.format(Language.Marry.BuyCountTips1, cost_text, price)
		else
			self.node_list["desc_text"].text.text = string.format(Language.Marry.BuyCountTips, cost_text, price)
		end
	elseif self.from == "lover_buy" then
		self.node_list["desc_text"].text.text = string.format(Language.Marry.BuyCountTips2, cost_text, price)
	end
end


function MarryFbBuyTipsView:OnFlush()

end

function MarryFbBuyTipsView:OnClickCancel()
	self:Close()
end

function MarryFbBuyTipsView:SetFbBuyDate(from)
	self.from = from
	self:Open()
end

function MarryFbBuyTipsView:OnClickSure()
	if self.from == "role_buy" then
		MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB)
	elseif self.from == "lover_buy" then
		MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB)
	end
	self:Close()
end

function MarryFbBuyTipsView:CloseCallBack()

end
