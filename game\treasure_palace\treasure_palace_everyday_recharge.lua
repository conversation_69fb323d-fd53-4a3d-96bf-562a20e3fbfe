function TreasurePalaceView:EveryDayRechargeInit()
    if not self.everyday_model then
        self.everyday_model = OperationActRender.New(self.node_list["everyday_model_pos"])
        self.everyday_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    if not self.everyday_reward_list then
        self.everyday_reward_list = AsyncListView.New(EveryDayRewardRender, self.node_list["everyday_reward_list"])
    end

    self:FlushEveryDayModel()
end

function TreasurePalaceView:EveryDayRechargeReleaseCallBack()
    if self.everyday_model then
        self.everyday_model:DeleteMe()
        self.everyday_model = nil
    end

    if self.everyday_reward_list then
        self.everyday_reward_list:DeleteMe()
        self.everyday_reward_list = nil
    end
end

function TreasurePalaceView:EveryDayRechargeFlush()
    local today_info = TreasurePalaceData.Instance:GetTodayInfo()
    self.everyday_reward_list:SetDataList(today_info)
end

function TreasurePalaceView:FlushEveryDayModel()
    local model_data_list = TreasurePalaceData.Instance:GetModelDataByTypeAndSeq(TreasurePalace_Model_Type.EveryDay, 1)
    if IsEmptyTable(model_data_list) then
        return
    end

    local display_data = model_data_list[1].display_data
    if display_data.render_type == -1 then
        return
    end

    local transform_info = model_data_list[1].transform_info
    if not display_data.model_click_func then
        display_data.model_click_func = function ()
            if display_data.itemid == nil or type(display_data.itemid) == "string" or display_data.itemid <= 0 then
                return
            end

            TipWGCtrl.Instance:OpenItem({item_id = display_data.itemid})
        end
    end

    self.everyday_model:SetData(display_data)
    RectTransform.SetAnchoredPositionXY(self.node_list.everyday_model_pos.rect, transform_info.pos_x, transform_info.pos_y)
    self.node_list.everyday_model_pos.rect.rotation = Quaternion.Euler(transform_info.rot_x, transform_info.rot_y, transform_info.rot_z)
    Transform.SetLocalScaleXYZ(self.node_list.everyday_model_pos.transform, transform_info.scale, transform_info.scale, transform_info.scale)
end


--------------- EveryDayRewardRender每日累充奖励格子 ---------------
EveryDayRewardRender = EveryDayRewardRender or BaseClass(BaseRender)

function EveryDayRewardRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function EveryDayRewardRender:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function EveryDayRewardRender:OnFlush()
    if not self.data then
        return
    end

    local today_rmb_num = TreasurePalaceData.Instance:GetTodayRMBNum()
    local cfg = self.data.cfg
    local can_get = today_rmb_num >= cfg.need_num
    local color = can_get and COLOR3B.DEFAULT_NUM or COLOR3B.PINK
    local price = RoleWGData.GetPayMoneyStr(cfg.need_num)
    self.node_list.des_txt.text.text = string.format(Language.TreasurePalace.EverydayTaskDes, color, today_rmb_num, price)

    local get_flag = self.data.lq_state > 0
    self.node_list.get_btn:SetActive(not get_flag)
    self.node_list.remind:SetActive(can_get and not get_flag)
    self.node_list.get_flag:SetActive(get_flag)

    if not get_flag then
        XUI.SetButtonEnabled(self.node_list.get_btn, can_get)
    end

    self.reward_list:SetDataList(cfg.reward_item)
end

function EveryDayRewardRender:OnClickGetBtn()
    if not self.data then
        return
    end

    local cfg = self.data.cfg
    local today_rmb_num = TreasurePalaceData.Instance:GetTodayRMBNum()
    if today_rmb_num < cfg.need_num then
        return
    end

    TreasurePalaceCtrl.Instance:SendZhenBaoDianClientReq(ZHENBAODIAN_OPERA_TYPE.GET_TODAY_REWARD, self.data.seq)
end