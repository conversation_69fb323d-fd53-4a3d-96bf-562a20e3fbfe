WorldsNO1WGData = WorldsNO1WGData or BaseClass()

function WorldsNO1WGData:__init()
	if WorldsNO1WGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[WorldsNO1WGData] attempt to create singleton twice!")
		return
	end
	WorldsNO1WGData.Instance = self
	self:InitCfg()
	self:InitProtocolInfo()


	RemindManager.Instance:Register(RemindName.WorldsNO1EnterRemind, BindTool.Bind(self.GetWorldsNO1EnterRemind, self))
	RemindManager.Instance:Register(RemindName.WorldsNO1LikeRemind, BindTool.Bind(self.GetWorldsNO1LikeRemind, self))
end

function WorldsNO1WGData:__delete()
	WorldsNO1WGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.WorldsNO1EnterRemind)
	RemindManager.Instance:UnRegister(RemindName.WorldsNO1LikeRemind)
end

------------------------------------ 协议数据存取 -----------------------------------
-- 初始化协议数据
function WorldsNO1WGData:InitProtocolInfo()
	-- 淘汰赛排名信息
	self.knockout_rank_info_list = {}
	self.main_role_knockout_rank_info_list = {}
	local cfg = self:GetKnockoutCfg()
	for _, v in ipairs(cfg) do
		self.knockout_rank_info_list[v.subround] = {}
	end

	-- 海选赛排名信息
	self.audition_rank_info_list = {}
	self.main_role_audition_rank_info = nil  					-- 主角海选赛排名信息

	-- 观战中战斗场景角色实时信息
	self.observational_scene_role_info_list = {}

	-- 战斗场景角色实时排名信息
	self.scene_rank_info_list = {}
	self.my_rank = 0  											-- 我的排名 0 表示未上榜
	self.remain_life_count = 0  								-- 主角剩余复活次数

	-- 剩余竞猜次数
	self.remain_guess_count = 0
	-- 当前已竞猜对象,key值是uuid_str
	self.guess_target_dic = {}

	self.buff_info_list = {} 									-- buff列表

	-- 观战目标uuid
	self.observation_uuid_str = ToLLStr(0, 0)

	self.show_barrage = true 												-- 是否显示弹幕
	self.pk_begin_countdown_timestamp = 0 									-- 进入pk场景后的开打倒计时
	self.like_flag = 0 														-- 点赞状态，0表示可点赞，1表示已点赞

	self.next_change_status_timestamp = 0 									-- 下一子轮状态开启时间
	self.cur_round = 0 														-- 当前轮次(0表示还没开启)
	self.cur_subround = 0 													-- 当前子轮次(0表示还没开启)
	self.subround_stage = 1 												-- 子轮状态
	self.can_join = true 													-- 是否有资格参赛
end

-- 设置轮次状态
function WorldsNO1WGData:SetRoundStatusInfo(protocol)
	if protocol.act_begin_timestamp > 0 then
		self.first_round_open_timestamp = protocol.act_begin_timestamp 							-- 活动开启时间
	end
	self.all_round_timetable = nil
	self.next_change_status_timestamp = protocol.next_change_status_timestamp 					-- 下一子轮状态开启时间
	self.cur_round = protocol.cur_round 														-- 当前轮次
	self.cur_subround = protocol.cur_subround 													-- 当前子轮次
	self.subround_stage = protocol.subround_stage 												-- 子轮阶段
end

-- 获得下次更变状态的时间戳
function WorldsNO1WGData:GetNextChangeStatusTimestamp()
	return self.next_change_status_timestamp
end

-- 结算时间戳
function WorldsNO1WGData:GetJiesuanTimestamp()
	return WorldsNO1WGData.Instance:GetNextChangeStatusTimestamp() -- 结算时间戳
end

-- 获得当前轮次
-- 取值: 	第一轮活动开启前：cur_round是0
--       	第一轮活动开启后到第二轮活动开启前：cur_round是1
-- 			最后一轮活动结束后到第二天凌晨：cur_round是4
-- 			第二天凌晨到新一轮的第一轮活动开启前：cur_round是0
function WorldsNO1WGData:GetCurRound()
	return self.cur_round
end

-- 获得下一轮次
function WorldsNO1WGData:GetNextRound()
	local cur_round = self:GetCurRound()
	local next_round = (cur_round + 1) % self:GetMaxRound()
	if next_round == 0 then
		next_round = self:GetMaxRound()
	end
	return next_round
end

-- 获得最大轮数
function WorldsNO1WGData:GetMaxRound()
	return #self:GetRoundCfg()
end

-- 获得当前子轮次
function WorldsNO1WGData:GetCurSubround()
	return self.cur_subround
end

-- 获取当前的子轮阶段（对应枚举：WORLDS_NO1_SUBROUND_STAGE）
function WorldsNO1WGData:GetCurSubroundStage()
	return self.subround_stage
end

-- 子轮状态
function WorldsNO1WGData:GetSubroundStatus(round, subround)
	local status = Language.WorldsNO1.OVER
	if WorldsNO1WGData.Instance:GetCurRound() > round then
		status = WORLDS_NO1_SUBROUND_STATUS.OVER
	elseif WorldsNO1WGData.Instance:GetCurRound() == round then
		if WorldsNO1WGData.Instance:GetCurSubround() < subround then
			status = WORLDS_NO1_SUBROUND_STATUS.UNOPENED
		elseif WorldsNO1WGData.Instance:GetCurSubround() == subround and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1) then
			status = WORLDS_NO1_SUBROUND_STATUS.OPENNING
		else
			status = WORLDS_NO1_SUBROUND_STATUS.OVER
		end
	else
		status = WORLDS_NO1_SUBROUND_STATUS.UNOPENED
	end
	return status
end

-- 获得当前子轮状态
function WorldsNO1WGData:GetCurSubroundStatus()
	return self:GetSubroundStatus(self:GetCurRound(), self:GetCurSubround())
end

function WorldsNO1WGData:SetRankListInfo(protocol)
	local match_type = WORLDS_RANK_INFO_TYPE_TO_MATCH_TYPE[protocol.rank_info_type]
	-- 如果是淘汰赛排名
	if match_type == WORLDS_NO1_MATCH_TYPE.KNOCKOUT then
		local subround = WORLDS_RANK_INFO_TYPE_TO_KNOCKOUT_SUBROUND[protocol.rank_info_type]
		self.knockout_rank_info_list[subround] = protocol.rank_list

		-- 主角排名信息（对应ReadWorldsNO1PlayerRankInfo字段）
		self.main_role_knockout_rank_info_list[subround] = protocol.main_role_rank_info
	-- 如果是海选赛
	elseif match_type == WORLDS_NO1_MATCH_TYPE.AUDITION then
		self.audition_rank_info_list = protocol.rank_list

		-- 主角排名信息（对应ReadWorldsNO1PlayerRankInfo字段）
		self.main_role_audition_rank_info = protocol.main_role_rank_info
	end
end

function WorldsNO1WGData:ConstructMainRoleRankInfo(rank_info_type)
	return {
		rank = -1,
		kill_count = -1,
		server_id = 0,
		score_count = 0,
		rank_info_type = rank_info_type,
	}
end

-- 设置参与资格
function WorldsNO1WGData:SetCanJoin(can_join)
	self.can_join = can_join
end

-- 参与资格
function WorldsNO1WGData:GetCanJoin()
	return self.can_join
end

-- 设置是否错过本子轮
function WorldsNO1WGData:SetCurSubroundIsMiss(is_miss)
	self.is_miss = is_miss
end

-- 是否错过本子轮
function WorldsNO1WGData:GetCurSubroundIsMiss()
	return self.is_miss
end

-- 设置是否轮空
function WorldsNO1WGData:SetCurSubroundIsEmpty(is_empty)
	self.is_empty = is_empty
end

-- 是否轮空
function WorldsNO1WGData:GetCurSubroundIsEmpty()
	return self.is_empty
end

-- 根据淘汰赛index获取对应排名信息
function WorldsNO1WGData:GetKnockoutRankInfo(knockout_subround)
	return self.knockout_rank_info_list[knockout_subround]
end

-- 获取对应轮次的主角排名信息
function WorldsNO1WGData:GetMianRoleKnockoutRankInfo(knockout_subround)
	return self.main_role_knockout_rank_info_list[knockout_subround]
end

-- 根据海选赛排名信息
function WorldsNO1WGData:GetAuditionRankInfo()
	return self.audition_rank_info_list
end

-- 获得主角海选赛排名信息
function WorldsNO1WGData:GetMainRoleAudtionRankInfo()
	return self.main_role_audition_rank_info
end

-- 退出描述
function WorldsNO1WGData:GetPrepareLeaveDesc()
	if self:GetCurSubroundStage() == WORLDS_NO1_SUBROUND_STAGE.PREPARE then
		local timestamp = self:GetNextChangeStatusTimestamp()
		local time_tab = os.date("*t", timestamp)
		return string.format(Language.WorldsNO1.PrepareLeaveTips, time_tab.hour, time_tab.min)
	else
		return Language.WorldsNO1.PrepareLeaveTips2
	end
end

-- 设置战斗场景角色实时信息(观战用)
function WorldsNO1WGData:SetObservationalSceneRoleListInfo(protocol)
	self.observational_scene_role_info_list = protocol.scene_role_info_list
end

-- 观战中获得战斗场景角色实时信息
function WorldsNO1WGData:GetObservationalSceneRoleListInfo()
	return self.observational_scene_role_info_list
end

-- 设置竞猜信息
function WorldsNO1WGData:SetGuessInfo(protocol)
	self.remain_guess_count = protocol.remain_guess_count
	self.guess_target_dic = protocol.guess_target_dic
end

-- 获得剩余投票次数
function WorldsNO1WGData:GetRemainBetCount()
	return self.remain_guess_count
end

-- 判断所给角色uuid是否已竞猜过了
function WorldsNO1WGData:GetIsGuess(uuid_str)
	return self.guess_target_dic[uuid_str] == true
end

-- 设置观战目标的uuid_str
function WorldsNO1WGData:SetObservationTarget(uuid_str)
	self.observation_uuid_str = uuid_str
end

-- 获得观战目标的uuid_str
function WorldsNO1WGData:GetObservationUUIDStr()
	return self.observation_uuid_str
end

-- 是否是观战状态
function WorldsNO1WGData:IsObservationStatus()
	return self:GetObservationUUIDStr() ~= ToLLStr(0, 0) and Scene.Instance:GetSceneType() == SceneType.WorldsNO1
end

-- 设置战斗场景实时排名信息
function WorldsNO1WGData:SetSceneRankInfo(protocol)
	self.scene_rank_info_list = protocol.role_rank_info_list
	self.pk_begin_countdown_timestamp = protocol.pk_begin_countdown_timestamp
	self.my_rank = protocol.my_rank
	self.remain_life_count = protocol.remain_life_count 		
end

-- 获得场景内主角实时排名
function WorldsNO1WGData:GetSceneMyRank()
	return self.my_rank
end

-- 获得战斗场景实时排名信息
function WorldsNO1WGData:GetSceneRankInfoList()
	return self.scene_rank_info_list
end

-- 获得进入pk场景的开打倒计时时间戳
function WorldsNO1WGData:GetPKBeginCountdownTimestamp()
	return self.pk_begin_countdown_timestamp
end

-- 获取剩余复活次数
function WorldsNO1WGData:GetReliveAmount()
	if Scene.Instance:GetSceneType() == SceneType.WorldsNO1Prepare then
		local round_cfg = self:GetCurRoundCfg()
		if round_cfg then
			return round_cfg.relive_count
		end
	end
	return self.remain_life_count
end

-- 获得第一轮的开启时间戳
function WorldsNO1WGData:GetFirstRoundOpenTimestamp()
	return self.first_round_open_timestamp or TimeWGCtrl.Instance:GetServerTime()
end

-- 获得上一次活动的最后一轮结束时间戳
function WorldsNO1WGData:GetLastActLastRoundCloseTimestamp()
	local last_round_time_info = self:GetAllRoundTimetable()[#self:GetAllRoundTimetable()]
	local first_round_time_tab = self:GetRoundTimeInfo(1)
	local weekday = CalendarWGData.Instance:GetWeekday(first_round_time_tab.open_serverday) 						-- 第一轮活动当天是星期几
	local timestamp_delta = (weekday - last_round_time_info.weekday) % 7 * 3600 * 24 							
	local last_act_end_timestamp = first_round_time_tab.close_timestamp - timestamp_delta  						-- 上次活动结束时间戳
	return last_act_end_timestamp
end

-- 获得所有轮次的时间信息
function WorldsNO1WGData:GetAllRoundTimetable()
	if self.all_round_timetable == nil then
		self.all_round_timetable = {}
		local round_cfg = self:GetRoundCfg()
		for round_index, v in ipairs(round_cfg) do
			if round_index == 1 then
				self.all_round_timetable[round_index] = self:ConstructTimeInfo(self:GetFirstRoundOpenTimestamp())
			else
				local last_round_open_timestamp = self.all_round_timetable[#self.all_round_timetable].open_timestamp
				local last_round_open_serverday = TimeWGCtrl.Instance:GetOpenServerDayByTimestamp(last_round_open_timestamp)
				self.all_round_timetable[round_index] = self:ConstructTimeInfo(TimeWGCtrl.Instance:GetServerTime())
				for i = 1, 7 do
					local open_serverday = last_round_open_serverday + i
					local hall_act_cfg = CalendarWGData.Instance:GetHallCfgByActType(ACTIVITY_TYPE.WORLDS_NO1, open_serverday)
					if hall_act_cfg then
						local is_open, act_cfg = CalendarWGData.Instance:ActIsOpen(open_serverday, hall_act_cfg.act_seq)
						if is_open then
							local timestamp = last_round_open_timestamp + i * 60 * 60 * 24
							self.all_round_timetable[round_index] = self:ConstructTimeInfo(timestamp)
							break
						end
					end
				end
			end
		end
	end
	return self.all_round_timetable
end

function WorldsNO1WGData:ConstructTimeInfo(open_timestamp)
	local open_serverday = TimeWGCtrl.Instance:GetOpenServerDayByTimestamp(open_timestamp)
	local close_timestamp = open_timestamp + self:GetSingleRoundDurationTime()
	local open_day_start_timestamp = TimeUtil.GetEarlyTime(open_timestamp) or 0
	local weekday = CalendarWGData.Instance:GetWeekday(open_serverday)
	local info = {
		open_timestamp = open_timestamp, 													-- 活动开启时间戳
		close_timestamp = close_timestamp,  												-- 活动关闭时间戳
		open_day_start_timestamp = open_day_start_timestamp, 								-- 活动开启当天0点时间戳
		open_day_end_timestamp = open_day_start_timestamp + 24 * 3600, 						-- 活动开启当天24点时间戳
		open_serverday = open_serverday, 													-- 活动开启开服天数
		weekday = weekday, 																	-- 周几开启
		weekday_str = NumberToChinaNumber(weekday),
		open_time_tab = os.date("*t", open_timestamp),
		close_time_tab = os.date("*t", close_timestamp),
	}
	return info
end

-- 根据所给轮次的开启时间戳推算出下一轮的时间信息
function WorldsNO1WGData:GetNextRoundTimeInfo(cur_round)
	local next_round = (cur_round + 1) % self:GetMaxRound()
	if next_round == 0 then
		next_round = self:GetMaxRound()
	end

	if cur_round == 0 then
		return self:ConstructTimeInfo(self:GetFirstRoundOpenTimestamp()), next_round
	end
	local cur_round_time_info = WorldsNO1WGData.Instance:GetRoundTimeInfo(cur_round)
	local cur_round_open_timestamp = cur_round_time_info.open_timestamp
	local cur_round_openserver_day = TimeWGCtrl.Instance:GetOpenServerDayByTimestamp(cur_round_open_timestamp)
	for i = 1, 7 do
		local open_serverday = cur_round_openserver_day + i
		local hall_act_cfg = CalendarWGData.Instance:GetHallCfgByActType(ACTIVITY_TYPE.WORLDS_NO1, open_serverday)
		if hall_act_cfg then
			local is_open, act_cfg = CalendarWGData.Instance:ActIsOpen(open_serverday, hall_act_cfg.act_seq)
			if is_open then
				local timestamp = cur_round_open_timestamp + i * 60 * 60 * 24
				return self:ConstructTimeInfo(timestamp), next_round
			end
		else
			return self:ConstructTimeInfo(TimeWGCtrl.Instance:GetServerTime()), next_round
		end
	end
	return self:ConstructTimeInfo(TimeWGCtrl.Instance:GetServerTime()), next_round
end

-- 活动海选赛阶段时间范围
function WorldsNO1WGData:GetAuditionTime()
	local start_open_tab = nil
	local start_close_tab = nil
	local end_close_tab = nil
	for round_index, v in ipairs(self:GetAllRoundTimetable()) do
		local round_cfg = self:GetRoundCfg(round_index, 1)
		if round_cfg.type == WORLDS_NO1_MATCH_TYPE.AUDITION then
			if start_open_tab == nil then
				start_open_tab = v.open_time_tab
				start_close_tab = v.close_time_tab
			end
			end_close_tab = v.close_time_tab
		end
	end
	return string.format(Language.WorldsNO1.DayRange, start_open_tab.month, start_open_tab.day, end_close_tab.month, end_close_tab.day)
		, string.format(Language.WorldsNO1.TimeStrRange, start_open_tab.hour, start_open_tab.min, start_close_tab.hour, start_close_tab.min)
end

-- 活动淘汰阶段时间范围
function WorldsNO1WGData:GetKnockoutTime(select_round_index)
	local start_open_tab = nil 
	local end_close_tab = nil
	local select_time_str = ""
	for round_index, v in ipairs(self:GetAllRoundTimetable()) do
		local round_cfg = self:GetRoundCfg(round_index, 1)
		if round_cfg.type == WORLDS_NO1_MATCH_TYPE.KNOCKOUT then
			if start_open_tab == nil then
				start_open_tab = v.open_time_tab
			end
			end_close_tab = v.close_time_tab
		end
		if round_index == select_round_index then
			select_time_str = round_cfg.subround_show_time
		end
	end
	return string.format(Language.WorldsNO1.DayStr, start_open_tab.month, start_open_tab.day), select_time_str
end

-- 获得开启状态(根据当前时间算的，如果用GM开活动会有问题)
function WorldsNO1WGData:GetRoundState(round)
	local timetable = self:GetRoundTimeInfo(round)
	if TimeWGCtrl.Instance:GetServerTime() > timetable.close_timestamp then
		return WORLDS_NO1_ROUND_STATUS.OVER
	elseif TimeWGCtrl.Instance:GetServerTime() < timetable.open_timestamp then
		return WORLDS_NO1_ROUND_STATUS.UNOPENED
	else
		return WORLDS_NO1_ROUND_STATUS.OPENNING
	end
end

-- 判断轮次是否今天即将开启
function WorldsNO1WGData:GetRoundTodayWillOpen(round)
	local time_info = self:GetRoundTimeInfo(round)
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	return cur_time > time_info.open_day_start_timestamp and cur_time < time_info.open_timestamp 
			and not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1) 					-- 避免服务端和客户端算的时间有偏差，这里多加个条件
end

-- 根据轮次index获得开启时间表
function WorldsNO1WGData:GetRoundTimeInfo(round_index)
	return self:GetAllRoundTimetable()[round_index]
end

-- 获取时间轴的值
function WorldsNO1WGData:GetTimeSliderValue()
	local full_prog = {0.27, 0.404, 0.541, 0.677}
	local empty_prog = {0.27 - 0.023, 0.404 - 0.023, 0.541 - 0.023, 0.677 - 0.023}
	local all_round_time = self:GetAllRoundTimetable()
	local cur_time = TimeWGCtrl.Instance:GetServerTime() 												-- 当前时间
	local cur_round = self:GetCurRound() 															-- 当前轮
	local next_round = cur_round + 1
	local cur_time_info = all_round_time[cur_round]
	local next_time_info = all_round_time[next_round]
	local last_round_time_info = all_round_time[#all_round_time]
	local value = 0
	-- 判断是否有时间信息，如果没有表示还在第0轮
	if cur_time_info then
		if next_time_info then
			-- 是否今天将要开启
			if cur_time > cur_time_info.open_day_start_timestamp and cur_time < cur_time_info.close_timestamp then
				value = full_prog[cur_round]
			elseif cur_time >= cur_time_info.close_timestamp and cur_time <= next_time_info.open_day_start_timestamp then
				value = Mathf.Lerp(full_prog[cur_round], empty_prog[next_round], GameMath.Divide(cur_time - cur_time_info.close_timestamp, next_time_info.open_day_start_timestamp - cur_time_info.close_timestamp))
			else
				value = full_prog[next_round]
			end
		else -- 最后一轮
			if cur_time > cur_time_info.close_timestamp then
				value = Mathf.Lerp(full_prog[cur_round], 1, GameMath.Divide(cur_time - cur_time_info.close_timestamp, cur_time_info.open_day_end_timestamp - cur_time_info.close_timestamp))
			else
				value = full_prog[cur_round]
			end
		end
	else -- 第0轮
		local first_round_time_tab = self:GetRoundTimeInfo(1)
		local last_act_end_timestamp = self:GetLastActLastRoundCloseTimestamp() 					-- 上次活动的最后一轮的结束时间戳
		if cur_time > first_round_time_tab.open_day_start_timestamp and cur_time < first_round_time_tab.close_timestamp then
			value = full_prog[1]
		else
			local sum = first_round_time_tab.open_day_start_timestamp - last_act_end_timestamp
			value = GameMath.Divide(cur_time - last_act_end_timestamp, sum) * empty_prog[1]
		end
	end
	return value
end

-- 设置上届冠军信息
function WorldsNO1WGData:SetChampionInfo(protocol)
	if protocol.champion_info.uid ~= 0 then
		self.champion_info = protocol.champion_info
	else
		self.champion_info = nil
	end
end

-- 获得上届冠军信息
function WorldsNO1WGData:GetChampionInfo()
	return self.champion_info
end

-- -- 获得当前轮次的子轮时间表
-- function WorldsNO1WGData:GetSubroundTimetable()
-- 	if not self.sub_round_timetable then
-- 		self.sub_round_timetable = {}
-- 		local subround_cfg = self:GetRoundCfg(self:GetCurRound())
-- 		for subround, cfg in ipairs(subround_cfg) do
			
-- 		end
-- 	end
-- 	return self.sub_round_timetable
-- end

function WorldsNO1WGData:SetLikeStatus(protocol)
	self.like_flag = protocol.like_flag
end

-- 是否已点赞
function WorldsNO1WGData:GetIsLiked()
	return self.like_flag == 1
end

-- 新增捡起来的buff数据
function WorldsNO1WGData:AddBuffInfo(protocol)
	local buff_cfg = self:GetBuffCfg(protocol.buff_type)
	if buff_cfg.show_in_buff_list == 1 then
		self.buff_info_list[protocol.buff_type] = {buff_type = protocol.buff_type, end_timestamp = protocol.end_timestamp}
	end
end

-- 获得buff列表
function WorldsNO1WGData:GetBuffInfoList()
	return self.buff_info_list
end

-- 获得有效的buff列表
function WorldsNO1WGData:GetVaildBuffInfoList()
	local result = {}
	for _, v in pairs(self:GetBuffInfoList()) do
		if v.end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
			table.insert(result, v)
		end
	end
	return result
end

-- 是否处于竞猜阶段
function WorldsNO1WGData:GetIsBetState()
	local round_cfg = WorldsNO1WGData.Instance:GetCurRoundCfg()
	local is_knockout = false
	if round_cfg then
		is_knockout = round_cfg.type == WORLDS_NO1_MATCH_TYPE.KNOCKOUT
	end
	return WorldsNO1WGData.Instance:GetCurSubroundStage() == WORLDS_NO1_SUBROUND_STAGE.PREPARE and is_knockout
end

-- 主角是否可竞猜
function WorldsNO1WGData:GetCanBet()
	if not FunOpen.Instance:GetFunIsOpened(FunName.WorldsNO1View) then
		return false
	end
	return self:GetIsBetState() and self:GetRemainBetCount() > 0
end
----------------------------------- 协议数据存取End ---------------------------------

------------------------------------ 配置数据存取 -----------------------------------
-- 初始化配置表
function WorldsNO1WGData:InitCfg()
	self.round_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").round, "round", "subround") 					-- 轮次配置
	self.type_key_round_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").round, "type") 					-- 轮次配置
	self.knockout_cfg = ListToMap(self.type_key_round_cfg[WORLDS_NO1_MATCH_TYPE.KNOCKOUT], "subround") 										-- 淘汰配置
	self.chuanwen_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").continue_kill_score, "type", "kill_count") 	-- 连杀传闻配置
	self.bet_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").guess_cfg, "index") 								-- 竞猜配置
end

-- 赛季排名奖励配置
function WorldsNO1WGData:GetSeasonRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").season_rank_reward
end

-- 单场排名奖励配置
function WorldsNO1WGData:GetSingleMatchRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").pk_rank_reward
end

-- 根据排名获得单场排名奖励配置
function WorldsNO1WGData:GetSingleMatchRewardCfgByRank(rank)
	for k,v in pairs(self:GetSingleMatchRewardCfg()) do
		if v.rank_min <= rank and v.rank_max >= rank then
			return v
		end
	end
	return nil
end

-- 获得轮次配置
function WorldsNO1WGData:GetRoundCfg(...)
	return CheckList(self.round_cfg, ...)
end

-- 获得当前轮当前子轮对应的配置
function WorldsNO1WGData:GetCurRoundCfg()
	return self:GetRoundCfg(self:GetCurRound(), self:GetCurSubround())
end

-- 获取当前轮次的比赛类型（淘汰赛、海选赛）
function WorldsNO1WGData:GetCurRoundType()
	local type = WORLDS_NO1_MATCH_TYPE.AUDITION
	local cfg = self:GetCurRoundCfg()
	if cfg then
		type = cfg.type
	end
	return type
end

-- 获得轮次配置
function WorldsNO1WGData:GetTypeKeyRoundCfg(...)
	return CheckList(self.type_key_round_cfg, ...)
end

-- 淘汰赛配置
function WorldsNO1WGData:GetKnockoutCfg()
	return self.knockout_cfg
end

-- 单场淘汰赛配置
function WorldsNO1WGData:GetKnockoutCfgByIndex(index)
	return self:GetKnockoutCfg()[index]
end

-- 连杀传闻配置
function WorldsNO1WGData:GetChuanWenCfg(chuanwen_type, kill_count)
	return self.chuanwen_cfg[chuanwen_type][kill_count]
end

-- 获得竞猜配置
function WorldsNO1WGData:GetBetCfgBySubround(subround)
	return self.bet_cfg[subround]
end

function WorldsNO1WGData:GetOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").other[1]
end

-- 单场活动持续时间
function WorldsNO1WGData:GetSingleRoundDurationTime()
	local hall_act_cfg = CalendarWGData.Instance:GetHallCfgByActType(ACTIVITY_TYPE.WORLDS_NO1)
	if hall_act_cfg then
		return CalendarWGData.Instance:ChangeToStamp(hall_act_cfg.close_time) - CalendarWGData.Instance:ChangeToStamp(hall_act_cfg.open_time)
	end
	return 0
end

-- 复活点配置
function WorldsNO1WGData:GetReliveCfg()
	return ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").relive_pos_list
end

-- 根据复活点index获得复活点配置
function WorldsNO1WGData:GetReliveCfgByIndex(pos_index)
	return self:GetReliveCfg()[pos_index]
end

-- 获取活动开启时间描述
function WorldsNO1WGData:GetWorldsNO1ActTimeDesc()
	local weekday, start_time, end_time, is_every_day = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.WORLDS_NO1)
	if is_every_day then
		return string.format(Language.WorldsNO1.ActTimeDesc2, weekday, start_time, end_time) 
	else
		return string.format(Language.WorldsNO1.ActTimeDesc, weekday, start_time, end_time) 
	end
end

-- buff配置
function WorldsNO1WGData:GetBuffCfg(index)
	return ConfigManager.Instance:GetAutoConfig("tianxiadiyi_cfg_auto").buff_cfg[index]
end

----------------------------------- 配置数据存取End ---------------------------------

-- 设置是否显示弹幕
function WorldsNO1WGData:SetShowBarrage(show_barrage)
	self.show_barrage = show_barrage
end

-- 获得是否显示弹幕面板
function  WorldsNO1WGData:GetShowBarrage()
	return self.show_barrage
end

-- 进入活动红点
function WorldsNO1WGData:GetWorldsNO1EnterRemind()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1) and self:GetCanJoin() then
		return 1
	end
	return 0
end

-- 点赞红点
function WorldsNO1WGData:GetWorldsNO1LikeRemind()
	if (not self:GetIsLiked()) and (self:GetChampionInfo() ~= nil) then
		return 1
	else
		return 0
	end
end

-- 刷新主界面聊天框上的竞猜提示按钮
function WorldsNO1WGData:FlushMainRoleChatBetBtn()
	if self:GetCanBet() then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WORLDS_NO1_BET, 1, function ()
			local subround_index = self:GetCurSubround()
			ViewManager.Instance:Open(GuideModuleName.WorldsNO1View, nil, "change_select_tab", {select_tab = WorldsNO1ViewTAB.KNOCKOUT, subround_index = subround_index})
			return true
	  	end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WORLDS_NO1_BET, 0)
	end
end