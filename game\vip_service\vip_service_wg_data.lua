VipServiceWindowWGData = VipServiceWindowWGData or BaseClass()
function VipServiceWindowWGData:__init()
    if VipServiceWindowWGData.Instance ~= nil then
		print_error("[VipServiceWindowWGData] attempt to create singleton twice!")
		return
	end
	VipServiceWindowWGData.Instance = self

	-- local vipService_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto")
    local cfg = ConfigManager.Instance:GetAutoConfig("agent_adapt_auto")
    self.vipService_list = ListToMap(cfg.agent_special_funopen, "spid")
    self.agent_service_reward_cfg = ListToMap(cfg.agent_service_reward, "spid")
    self.other_cfg = cfg.other[1]
    self.service_bubble_show_cfg = ListToMapList(cfg.service_bubble_show, "daily_reward_flag")

    self.service_bubble_show_index = 1
    self.pop_service_daily_reward_flag = 0
end

function VipServiceWindowWGData:__delete()
	VipServiceWindowWGData.Instance = nil
end

--按钮是否开启
function VipServiceWindowWGData:GetServiceIsOpen()
    if GLOBAL_CONFIG.param_list.is_open_vip_service == nil then
        return false
    end

    local role_level = RoleWGData.Instance:GetRoleLevel()
    local open_level = self:GetOpenLevel()
    if role_level < open_level then
        return false
    end

    if GLOBAL_CONFIG.param_list.is_open_vip_service > 0 then
        return true
    else
        return false
    end
end

function VipServiceWindowWGData:GetOpenViewLevelCfg()
	return self.other_cfg.level_open_panel_kefu
end

function VipServiceWindowWGData:GetBubbleIntervalTime()
    local time = self.other_cfg.bubble_interval_time
	return time * 60
end

function VipServiceWindowWGData:GetAgentServiceReward()
	local channel_id = ChannelAgent.GetChannelID()
	local cfg = self.agent_service_reward_cfg[channel_id]
    local ret_reward = {}
    if IsEmptyTable(cfg) then
        cfg = self.agent_service_reward_cfg["dev"]
    end

    if not IsEmptyTable(cfg) then
        ret_reward = cfg.show_reward_items
    end

    return ret_reward
end

function VipServiceWindowWGData:GetCurAgentServiceShowDesc()
    local channel_id = ChannelAgent.GetChannelID()
    return (self.agent_service_reward_cfg[channel_id] or {}).show_desc
end

function VipServiceWindowWGData:GetCurAgentServiceIcon()
    local channel_id = ChannelAgent.GetChannelID()
    return (self.agent_service_reward_cfg[channel_id] or {}).show_icon_name
end

function VipServiceWindowWGData:GetServiceBubbleShowCfg()
    local cfg = self.service_bubble_show_cfg[self.pop_service_daily_reward_flag][self.service_bubble_show_index]
    self:SetServiceBubbleShowIndex()
    return cfg
end

function VipServiceWindowWGData:SetServiceBubbleShowIndex()
    local max_len = #self.service_bubble_show_cfg[self.pop_service_daily_reward_flag]
    self.service_bubble_show_index = self.service_bubble_show_index < max_len and (self.service_bubble_show_index + 1) or 1
end

function VipServiceWindowWGData:SetAllInfo(protocol)
    self.pop_service_daily_reward_flag = protocol.pop_service_daily_reward_flag
end

function VipServiceWindowWGData:GetDailyRewardFlag()
    return self.pop_service_daily_reward_flag
end

function VipServiceWindowWGData:GetOpenLevel()
    local spid = CHANNEL_AGENT_ID
    local level = 1
    for k, v in pairs(self.vipService_list) do
        if v.spid == spid then
            level = v.open_level_kefu
        end
    end

    return level
end