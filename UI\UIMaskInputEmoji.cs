﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Text.RegularExpressions;
using System;


[RequireComponent(typeof(InputField))]
public class UIMaskInputEmoji : MonoBehaviour
{	
	private InputField m_input_field;
	List<string> _emojiPatten = new List<string>() { @"\p{Cs}", @"\p{Co}", @"\p{Cn}", @"[\u2702-\u27B0]", @"[\uD83D][\uDE01-\uDE4F]", @"[\uD83D][\uDEC0-\uDE80]" };

	void Awake()
	{
		this.m_input_field = this.gameObject.GetComponent<InputField> ();

		if (this.m_input_field == null) 
		{
			Debug.LogError ("UIMaskInputEmoji Not Find InputField Component!");			
			return;
		}
		this.m_input_field.onValidateInput = this.MyOnValidateInput;
	}
		

	private char MyOnValidateInput(string text, int char_index, char added_char)
	{
		if (_emojiPatten.Count > 0) 
		{
			string s = string.Format ("{0}", added_char);
			if (FilterEmoji (s)) 
			{
				return '\0';
			}
		}
		return added_char;
	}


	private bool FilterEmoji(string s)
	{
		bool is_emoji = false;
		//屏蔽emoji
		for (int i = 0; i < _emojiPatten.Count; i++) 
		{
			is_emoji = Regex.IsMatch (s, _emojiPatten [i]);//屏蔽emoji   
			if(is_emoji)
			{
				break;
			}
		}
		return is_emoji;
	}
}
