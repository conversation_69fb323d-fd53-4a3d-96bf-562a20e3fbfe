--守护精灵（经验精灵，减少精灵）
GuardObj = GuardObj or BaseClass(FollowObj)

function GuardObj:__init(vo)
    self.obj_type = SceneObjType.GuardObj
    self.draw_obj:SetObjType(self.obj_type)
    -- self.draw_obj:SetIsKeepGroundH(true)
    self.shield_obj_type = ShieldObjType.Guard
    self.shield_effect_type = ShieldObjType.GuardEffect
    self.shadow_hide_when_self_hide = true
    self.guard_res_id = 0
    self:SetObjId(vo.obj_id)
    self.follow_offset = 6
    self.is_wander = true
    self.mass = 0.5
    self.wander_cd = 8
    self:SetMaxForce(20)
    self.can_auto_pick = false

    local obj = Scene.Instance:GetObjectByObjId(self.vo.owner_obj_id)
    if nil ~= obj and obj:IsMainRole() then
        self.create_ovj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
    end 

    if not self.obj_leave_event and nil ~= obj and obj:IsMainRole() then
        self.obj_leave_event = GlobalEventSystem:Bind(SceneEventType.OBJ_LEVEL_ROLE, BindTool.Bind(self.CheckPickByQuitMove, self))
    end

    self:CheckPickByQuitMove()

end

function GuardObj:__delete()
    if self.parent_scene and self.vo and self.vo.owner_obj_id then
        local owner_obj = self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id)
        if owner_obj then
            owner_obj:ReleaseGuardObj()
        end
    end
    if self.create_ovj_event then
        GlobalEventSystem:UnBind(self.create_ovj_event)
        self.create_ovj_event = nil
    end

    if self.obj_leave_event then
        GlobalEventSystem:UnBind(self.obj_leave_event)
        self.obj_leave_event = nil
    end

    self.is_moveto_pcik = nil
end

function GuardObj:InitAppearance()
    if self.vo.guard_id ~= nil and self.vo.guard_id > 0 then
        self:ChangeModel(SceneObjPart.Main, ResPath.GetGuardModel(self.vo.guard_id))
    end
    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:SetHpVisiable(false)
        follow_ui:SetNameVis(false)
    end
end

function GuardObj:OnModelLoaded(part, obj)
    SceneObj.OnModelLoaded(self, part, obj)
    if part == SceneObjPart.Main then
        local scale = 1
        local cfg = EquipmentWGData.GetXiaoGuiCfg(self.vo.guard_id)
        if cfg and cfg.scenes_scale then
            scale = cfg.scenes_scale
        end

        local transform = self.draw_obj:GetRoot().transform
        transform.localScale = Vector3(scale, scale, scale)
        self.draw_obj:SetScale(scale, scale, scale)
    end
end

function GuardObj:SetAttr(key, value)
    FollowObj.SetAttr(self, key, value)
    if self.vo.guard_id ~= nil and self.vo.guard_id > 0 then
        self:ChangeModel(SceneObjPart.Main, ResPath.GetGuardModel(self.vo.guard_id))
    end
end

function GuardObj:UpdateModelResId()
    if self.vo.soulboy_lt_id ~= self.soulboy_res_id then
        self.soulboy_res_id = self.vo.soulboy_lt_id
        self:SetAttr("name", NewAppearanceWGData.Instance:GetQiChongNameByAppeId(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, self.soulboy_res_id))
    end
end

function GuardObj:UpdateHuanhuaModelResId()

end

function GuardObj:UpdateShenGongResId()
    self.soulboy_shengong_id = self.vo.soulboy_lg_id
    if self.soulboy_shengong_id == nil or self.soulboy_shengong_id == 1 or self.soulboy_shengong_id == 0 then
        self.soulboy_shengong_id = AppearanceWGData.GetLingTongBaseWeaponModelID()
    end
end

function GuardObj:IsCharacter()
    return false
end

function GuardObj:GetOwerRoleId()
    return self.vo.owner_role_id
end

function GuardObj:SetTrigger(key)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        -- local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetTrigger(key)
        end
        -- if weapon_part then
        --     weapon_part:SetTrigger(key)
        -- end
    end
end

function GuardObj:SetBool(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetBool(key, value)
        end
        if weapon_part then
            weapon_part:SetBool(key, value)
        end
    end
end

function GuardObj:SetInteger(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetInteger(key, value)
        end
        -- if weapon_part then
        --     weapon_part:SetInteger(key, value)
        -- end
    end
end

function GuardObj:IsMyGuard()
    local obj = self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id)
    local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
    if nil ~= obj and obj:IsRole() and role_id == self.vo.owner_role_id then
        return true
    end
    return false
end

function GuardObj:IsGuard()
    return true
end

function GuardObj:GetGuardCurPos()
    return self.draw_obj.curPos
end

function GuardObj:OnObjCreate(obj)
    if self.is_moveto_pcik or self.check_pick_timer > Status.NowTime then
        return
    end

    local obj_vo = obj:GetVo()
    local is_pb_shouhu = self.parent_scene:GetCurFbSceneCfg().pb_shouhu
    local can_auto = EquipWGData.Instance:CanAutoPick(is_pb_shouhu == 1)
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
    if not self.is_moveto_pcik and can_auto and obj and obj:GetType() == SceneObjType.FallItem and not obj:IsDeleted() and not obj:IsPicked() then
        local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(obj_vo.item_id)
        if obj_vo.owner_role_id <= 0 or obj_vo.owner_role_id == main_role_id
            or (obj_vo.lose_owner_time > 0 and obj_vo.lose_owner_time <= TimeWGCtrl.Instance:GetServerTime()) then
            if obj_vo.is_buff_falling == 1 or item_cfg and Scene.Instance:GetGuajiDataTable(obj:IsEquip(), item_cfg) then
                self.is_moveto_pcik = true
                local pos_table = {}
                pos_table.x = obj_vo.pos_x
                pos_table.y = obj_vo.pos_y
                self:DoMove(pos_table, true)
            end
        end
    end
end

function GuardObj:GetPickObj()
    local delay_time = nil
    local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem) or {}
    local main_role = Scene.Instance:GetMainRole()
    if not main_role then
        return nil, delay_time
    end

    local main_role_id = main_role:GetRoleId()
    local is_pb_shouhu = Scene.Instance:GetCurFbSceneCfg().pb_shouhu
    local can_auto = EquipWGData.Instance:CanAutoPick(is_pb_shouhu == 1)

    if IsEmptyTable(fall_item_list) then
        return nil, delay_time
    end

    for k,v in pairs(fall_item_list) do
        if not v:IsDeleted() and not v:IsPicked() and v:IsCanPickItem() and can_auto then
            local obj_vo = v:GetVo()
            local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(obj_vo.item_id)

            if obj_vo.owner_role_id <= 0 or obj_vo.owner_role_id == main_role_id or
                (obj_vo.lose_owner_time > 0 and obj_vo.lose_owner_time <= TimeWGCtrl.Instance:GetServerTime()) then

                if obj_vo.is_buff_falling == 1 or item_cfg and Scene.Instance:GetGuajiDataTable(v:IsEquip(), item_cfg) then
                    if TimeWGCtrl.Instance:GetServerTime() >= obj_vo.drop_time + COMMON_CONSTS.FALLING_WAIT_TIME then
                        local pos_table = {}
                        pos_table.x = obj_vo.pos_x
                        pos_table.y = obj_vo.pos_y
                        return pos_table, nil                  
                    else
                        local check_time = obj_vo.drop_time + COMMON_CONSTS.FALLING_WAIT_TIME - TimeWGCtrl.Instance:GetServerTime()
                        if delay_time == nil then
                            delay_time = check_time
                        else
                            if delay_time > check_time then
                                delay_time = check_time
                            end
                        end
                    end
                end
            end
        end
    end

    return nil, delay_time
end

function GuardObj:QuitStateMove()
    self.is_moveto_pcik = false
    Character.QuitStateMove(self)

    local pos_table, delay_time = self:GetPickObj()
    if pos_table ~= nil then
        self.is_moveto_pcik = true
        self.check_pick_timer = 0
        self:DoMove(pos_table, true)
    elseif delay_time ~= nil then
        self.is_moveto_pcik = true
        if self.check_pick_timer < Status.NowTime then
            self.check_pick_timer = Status.NowTime + delay_time
        end
    end
end

function GuardObj:CheckPickByQuitMove(obj_id, obj_type)
    --obj_type 由掉落物离开监听有obj_type，由DoMove驱动的话没有obj_type
    if obj_type ~= nil and obj_type ~= SceneObjType.FallItem then
        return
    end

    self:QuitStateMove()
end
