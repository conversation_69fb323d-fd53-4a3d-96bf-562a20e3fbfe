GuildWarZhanBaoView = GuildWarZhanBaoView or BaseClass(SafeBaseView)


function GuildWarZhanBaoView:__init()
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/guild_ui_prefab", "guild_war_zhanbao")
end

function GuildWarZhanBaoView:__delete()
end

function GuildWarZhanBaoView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if CountDownManager.Instance:HasCountDown("zhanbao_auto_close") then
		CountDownManager.Instance:RemoveCountDown("zhanbao_auto_close")
	end
end

function GuildWarZhanBaoView:LoadCallBack()
	self.rank_list = AsyncListView.New(GuildWarZhanBaoItem, self.node_list["rank_list"])
	self.role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = false,
	}
	
	self.role_model:SetRenderTexUI3DModel(display_data)
	-- self.role_model:SetUI3DModel(self.node_list["model"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)

	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	local reward_data = {}
	local guild_other_cfg = GuildBattleRankedWGData.Instance:GetGuildWarReward()
	if not IsEmptyTable(guild_other_cfg) then
		local bazhu_fashion_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shizhuang_index") or 1
		local bazhu_shenbing_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shenbing_index") or 1

		local fashion_item_id = NewAppearanceWGData.Instance:GetFashionItemId(1, bazhu_fashion_index)
		local shenbing_item_id = NewAppearanceWGData.Instance:GetFashionItemId(6, bazhu_shenbing_index)

		local title_other_cfg = TitleWGData.Instance:GetOtherCfg()
		local title_id = title_other_cfg and title_other_cfg.xianmengzhan_first or 0
		local title_cfg = TitleWGData.Instance:GetConfig(title_id)
		if fashion_item_id > 0 then
			local item = { item_id = fashion_item_id, num = 1, is_bind = 1, has_top = true }
			table.insert(reward_data, item)
		end

		if shenbing_item_id > 0 then
			local item = { item_id = shenbing_item_id, num = 1, is_bind = 1, has_top = true }
			table.insert(reward_data, item)
		end

		if title_cfg then
			local item = { item_id = title_cfg.item_id, num = 1, is_bind = 1, has_top = true }
			table.insert(reward_data, item)
		end
		self.reward_list:SetDataList(reward_data)
	end
	self.node_list["auto_close_text"]:SetActive(false)
	--self.node_list["right_top_text"].text.text = Language.Guild.XMZZhanBaoDesc
end

function GuildWarZhanBaoView:OnFlush(param_t, index)
	self.node_list["auto_close_text"]:SetActive(false)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.auto_close_text then
				self:NeedAutoClose()
			end
		end
	end
	local guild_datas = GuildWGData.Instance:GetXMZZhanBaoRankData()
	self.rank_list:SetDataList(guild_datas)
	local top_uuid = GuildWGData.Instance:GetXMZTop1GuildMengZhuUUId()
	local top_guild = guild_datas[1]
	local data_status = top_uuid and top_guild and top_guild.guild_id > 0

	self.node_list["img_no_tips"]:SetActive(not data_status)
	self.node_list["rank_list"]:SetActive(data_status)
	self.node_list["mengzhu"]:SetActive(data_status)
	self.node_list["chengzhu_image_nodata"]:SetActive(not data_status)
	self.node_list["herotitle"]:SetActive(data_status)

	if data_status then
		local title_other_cfg = TitleWGData.Instance:GetOtherCfg()
		local title_id = title_other_cfg and title_other_cfg.xianmengzhan_first or 0
		local b, a = ResPath.GetTitleModel(title_id)
		self.node_list["herotitle"]:ChangeAsset(b, a, false)

		if top_uuid.temp_low == 0 then
			local data_info = {}
			if top_guild then
				local cfg = GuildWGData.Instance:GetGuildRobotCfgByGuildName(top_guild.guild_name)
				self.node_list["mengzhu_name"].text.text = cfg.role_name
				data_info.sex = cfg.sex or 1
				data_info.prof = cfg.prof or 1
				data_info.capability = cfg.mengzhu_cap
				data_info.appearance = {}
				local bazhu_fashion_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shizhuang_index") or
				1
				local bazhu_shenbing_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shenbing_index") or
				1
				local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(1, bazhu_fashion_index)
				local shenbing_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(6, bazhu_shenbing_index)
				data_info.appearance.fashion_body = fashion_cfg and fashion_cfg.resouce or 0
				data_info.appearance.fashion_wuqi = shenbing_cfg and shenbing_cfg.resouce or 0
				data_info.updateattr = {}
				if self.role_model then
					self.role_model:SetModelResInfo(data_info)
				end
			end
			return
		end
		BrowseWGCtrl.Instance:SendCrossQueryRoleInfo(top_uuid.temp_high, top_uuid.temp_low,
			BindTool.Bind(self.RoleInfoCallBack, self))
	else
		self.node_list["mengzhu_name"].text.text = Language.Guild.Waitting_2
	end
end

function GuildWarZhanBaoView:RoleInfoCallBack(info)
	local top_uuid = GuildWGData.Instance:GetXMZTop1GuildMengZhuUUId()
	if top_uuid and top_uuid.temp_low == info.role_id then
		local role_appearance = {}
		role_appearance.role_id = info.role_id
		role_appearance.appearance = info.appearance
		role_appearance.sex = info.sex
		role_appearance.prof = info.prof
		role_appearance.updateattr = info.updateattr
		self.node_list["mengzhu_name"].text.text = info.role_name
		if self.role_model then
			-- if role_appearance.role_id ~= self.old_role_id then
			self.role_model:SetModelResInfo(role_appearance)
			-- end
			-- local temp_info = role_appearance.updateattr[1] or {}
			-- local fabao_appeid = temp_info.use_image_id or 0
			-- if fabao_appeid > 0 then
			-- 	-- if fabao_appeid ~= self.old_fabao_id or role_appearance.role_id ~= self.old_role_id then
			-- 	self.fabao_model:SetMainAsset(ResPath.GetFaBaoModel(fabao_appeid))
			-- 	-- end
			-- else
			-- 	self.fabao_model:ClearModel()
			-- end
			-- self.old_fabao_id = fabao_appeid
			-- self.old_role_id = role_appearance.role_id
		end
	end
end

function GuildWarZhanBaoView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("zhanbao_auto_close") then
		CountDownManager.Instance:RemoveCountDown("zhanbao_auto_close")
	end
end

function GuildWarZhanBaoView:NeedAutoClose()
	self.node_list["auto_close_text"]:SetActive(true)
	if CountDownManager.Instance:HasCountDown("zhanbao_auto_close") then
		CountDownManager.Instance:RemoveCountDown("zhanbao_auto_close")
	end
	CountDownManager.Instance:AddCountDown("zhanbao_auto_close", BindTool.Bind(self.UpdateAutoClose, self),
		BindTool.Bind(self.CompleteAutoClose, self), nil, 30, 1)
end

function GuildWarZhanBaoView:UpdateAutoClose(now_time, elapse_time)
	self.node_list["auto_close_text"]:SetActive(true)
	local lerp_time = math.ceil(elapse_time - now_time)
	self.node_list["auto_close_text"].text.text = string.format(Language.Common.AutoCloseTimerTxt, lerp_time)
end

function GuildWarZhanBaoView:CompleteAutoClose()
	self.node_list["auto_close_text"]:SetActive(false)
	self:Close()
end

---------------------------------------------------------------
GuildWarZhanBaoItem = GuildWarZhanBaoItem or BaseClass(BaseRender)

function GuildWarZhanBaoItem:__init()

end

function GuildWarZhanBaoItem:__delete()
end

function GuildWarZhanBaoItem:OnFlush()
	if IsEmptyTable(self.data) then return end
	local Icon_state = self.index <= 3
	if Icon_state then
		local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. self.index)
		self.node_list["rank_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["rank_img"].image:SetNativeSize()
		end)
	else
		self.node_list["rank_text"].text.text = self.index
	end
	self.node_list["rank_img"]:SetActive(Icon_state)
	self.node_list["rank_text"]:SetActive(not Icon_state)
	self.node_list["rank_bg"]:SetActive(self.index % 2 == 0)

	if self.data.guild_id > 0 then
		self.node_list["guild_name"].text.text = self.data.guild_name
	else
		self.node_list["guild_name"].text.text = Language.Guild.Waitting_2
	end
end
