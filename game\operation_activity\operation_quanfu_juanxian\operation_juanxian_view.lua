function OperationActivityView:ReleaseCallBackJuanXianView()
	if self.jx_role_reward_list ~= nil then
		self.jx_role_reward_list:DeleteMe()
		self.jx_role_reward_list = nil
	end

	if self.qf_reward_list ~= nil then
		self.qf_reward_list:DeleteMe()
		self.qf_reward_list = nil
	end

	self.reward_juanxian_cfg  = nil
end


function OperationActivityView:LoadCallBackJuanXianView()
	self.juanxian_view_info_cfg = OperationJuanXianWGData.Instance:GetViewCfgByInterface()
	--寻玉录数据储存
	if OperationJuanXianWGData.Instance:GetXunYuLuBtnOpen() then
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_OPEN_SERVER_XUNYULU, XUNYULU_OPERA_TYPE.YPE_REQ_INFO)
	end

	self:InitJuanXianView()
	self:FlushXylRedPoint()
	self:SetJuanXianViewShowInfo()
	self:OnJXBtnTipClickHnadler()
end

function OperationActivityView:ShowIndexCallBackJuanXianView()
	--每次打开界面的时候请求一次
	OperationJuanXianWGCtrl.Instance:SendActivityRewardOp(OPERATION_QUANFU_JUANXIAN_OP_TYPE.TYPE_INFO)
	self.is_can_jump = true
end

function OperationActivityView:OnFlushJuanXianView(param_t, index)
	self:FlushJuanXianView()
	self:InitRewardNode()
end

function OperationActivityView:InitJuanXianView()
	XUI.AddClickEventListener(self.node_list.jx_btn_jx_one, BindTool.Bind(self.OnJXOneBtnClickHandler,self))
	XUI.AddClickEventListener(self.node_list.jx_btn_jx_ten, BindTool.Bind(self.OnJXTenBtnClickHandler,self))
	XUI.AddClickEventListener(self.node_list.jx_one_icon, BindTool.Bind(self.OnJXBtnItemClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.jx_ten_icon, BindTool.Bind(self.OnJXBtnItemClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.jx_item_icon, BindTool.Bind(self.OnJXBtnItemClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.btn_xunyulu, BindTool.Bind(self.OnBtnXYLClickHnadler,self))

	self.node_list["btn_xunyulu"]:SetActive(OperationJuanXianWGData.Instance:GetXunYuLuBtnOpen())

	local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
	local item_cfg = ItemWGData.Instance:GetItemConfig(juanxian_stuff_item_id)
	if item_cfg then
		self.node_list.jx_one_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
		self.node_list.jx_ten_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
		self.node_list.jx_item_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	end
end

--初始化奖励节点
function OperationActivityView:InitRewardNode()
	if nil == self.reward_juanxian_cfg then
		local cfg = OperationJuanXianWGData.Instance:GetQfjxDayRewardCfg()
		local reward_cfg = {}
		if cfg then
			for k,v in pairs(cfg) do
				if v then
					table.insert(reward_cfg, v)
				end
			end
		end

		local len = #reward_cfg
		if len % 2 ~= 0 then
			local data = {}
			data.index = 1000
			table.insert(reward_cfg, data) 	--增加多一个多适配，不显示
		end
		self.reward_juanxian_cfg = reward_cfg
	end

	if not self.qf_reward_list then
		self.qf_reward_list = AsyncListView.New(SerOperationQfjxRewardItemRender, self.node_list.jx_slide_list)
	end

	if self.reward_juanxian_cfg and self.is_can_jump then
		self.qf_reward_list:SetDataList(self.reward_juanxian_cfg)
		self:JxGotoLastProgress()
	else
		self.qf_reward_list:RefreshActiveCellViews()
	end
end

--物品变化
function OperationActivityView:ChangeViewInfoShow()
	local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(juanxian_stuff_item_id)
	self.node_list.jx_one_redpoint:SetActive(item_num > OPERATION_QUANFU_JUANXIAN.JUAN_XIAN_NUM_1)
	self.node_list.jx_ten_redpoint:SetActive(item_num >= OPERATION_QUANFU_JUANXIAN.JUAN_XIAN_NUM_2)
	if self.node_list.jx_my_own then
		self.node_list.jx_my_own.text.text = item_num
	end
end

--跳转到最新的进度
function OperationActivityView:JxGotoLastProgress()
	if self.qf_reward_list and self.is_can_jump then
		local active_num, total_num = OperationJuanXianWGData.Instance:GetActiveNum()
		self.qf_reward_list:ReloadData(active_num / total_num)
		self.is_can_jump = false
	end
end

--刷新界面状态
function OperationActivityView:FlushJuanXianView()
	local qf_cfg = OperationJuanXianWGData.Instance:GetQfjxDayRewardCfg()
	if qf_cfg ~= nil then
		local max_jx_count = 0
		if #qf_cfg > 0 then
			max_jx_count = qf_cfg[#qf_cfg].server_day_juanxian_num or 0
		end

		local server_count = OperationJuanXianWGData.Instance:GetServerJuanXianCount()
		local next_jindu = 0
		if max_jx_count > server_count then
			for i=1, #qf_cfg do
				if qf_cfg[i].server_day_juanxian_num > server_count then
					next_jindu = qf_cfg[i].server_day_juanxian_num
					break
				end
			end
			self.node_list.biaoyu_text_num.text.text = server_count .. "/" .. next_jindu
		else
			self.node_list.biaoyu_text_num.text.text = server_count
		end
	end

	local role_reward_cfg = OperationJuanXianWGData.Instance:GetQFRoleRewardCfg()
	if role_reward_cfg ~= nil then
		if not self.jx_role_reward_list then
			self.jx_role_reward_list = AsyncListView.New(SerOperationJXRoleRewardItemRender, self.node_list.jx_role_reward_list)
		end

		local list = {}
		local state = 0
		for i,v in ipairs(role_reward_cfg) do
			local item = {}
			item.cfg = v
			item.index = v.index
			state = OperationJuanXianWGData.Instance:GetRoleRewardState(v.index)
			if state == OPERATION_REAWAD_STATE.KLQ then
				item.sort = 0
			elseif state == OPERATION_REAWAD_STATE.BKL then
				item.sort = 1
			else
				item.sort = 2
			end
			table.insert(list, item)
		end

		table.sort(list, SortTools.KeyLowerSorter("sort","index"))
		self.jx_role_reward_list:SetDataList(list)
	end

	local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(juanxian_stuff_item_id)
	self.node_list.jx_one_redpoint:SetActive(item_num > OPERATION_QUANFU_JUANXIAN.JUAN_XIAN_NUM_1)
	self.node_list.jx_ten_redpoint:SetActive(item_num >= OPERATION_QUANFU_JUANXIAN.JUAN_XIAN_NUM_2)
	self.node_list.jx_my_own.text.text = item_num

	self:FlushXylRedPoint()
	--这段先保留，因为我相信策划的尿性肯定会让加回来
	-- if self.juanxian_view_info_cfg then
	-- 	local num = OperationJuanXianWGData.Instance:GetRoleJuanXianCount()
	-- 	local desc = self.juanxian_view_info_cfg.jx_my_own
	-- 	desc = string.format(desc, num)
	-- 	self.node_list.jx_my_own.text.text = desc
	-- end
end

function OperationActivityView:FlushXylRedPoint()
	local is_show = OperationJuanXianWGData.Instance:GetXunYuLuBtnOpen() and ServerActivityWGData.Instance:IsXunYuLuRedPoint()
	self.node_list.xyl_redpoint:SetActive(is_show)
end

function OperationActivityView:OnJXBtnTipClickHnadler()
	local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(ACTIVITY_TYPE.OPERA_ACT_QUANFU_JUANXIAN)
	local name = cfg and cfg.active_name or ""
	local desc = self.juanxian_view_info_cfg and self.juanxian_view_info_cfg.rule_1 or ""
	local rule_1 = self.juanxian_view_info_cfg and self.juanxian_view_info_cfg.tip_label or ""
	self:SetOutsideRuleTips(rule_1)
	self:SetRuleInfo(desc, name)
end

function OperationActivityView:OnJXOneBtnClickHandler()
	local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(juanxian_stuff_item_id)
	if item_num > 0 then
		--优先判断背包是否已满
		local bag_shengyu_num = ItemWGData.Instance:GetEmptyNum()
		if bag_shengyu_num and 0 == bag_shengyu_num then
			RoleBagWGCtrl.Instance:OpenOtherRoleBagCleanTips()
			return
		end

		OperationJuanXianWGCtrl.Instance:SendActivityRewardOp(OPERATION_QUANFU_JUANXIAN_OP_TYPE.TYPE_JUANXIAN, OPERATION_QUANFU_JUANXIAN.JUAN_XIAN_NUM_1, OPERATION_JUANXINA_STUFF_TYPE.TYPE_ITEM)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.OperationJuanXian.JuanXianStr10)
	end
end

function OperationActivityView:OnJXBtnItemClickHnadler()
	local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
	TipWGCtrl.Instance:OpenItem({item_id = juanxian_stuff_item_id, num = 1, is_bind = 1}, ItemTip.FROM_NORMAL, nil)
end

function OperationActivityView:OnJXTenBtnClickHandler()
	local juanxian_stuff_item_id = OperationJuanXianWGData.Instance:GetJuanXianOtherNeedId()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(juanxian_stuff_item_id)
	if item_num >= 10 then
		local bag_shengyu_num = ItemWGData.Instance:GetEmptyNum()
		if bag_shengyu_num and 0 == bag_shengyu_num then
			RoleBagWGCtrl.Instance:OpenOtherRoleBagCleanTips()
			return
		end

		OperationJuanXianWGCtrl.Instance:SendActivityRewardOp(OPERATION_QUANFU_JUANXIAN_OP_TYPE.TYPE_JUANXIAN, OPERATION_QUANFU_JUANXIAN.JUAN_XIAN_NUM_2, OPERATION_JUANXINA_STUFF_TYPE.TYPE_ITEM)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.OperationJuanXian.JuanXianStr10)
	end
end

--设置界面的所有显示信息包括底图(只需要赋值一次的)
function OperationActivityView:SetJuanXianViewShowInfo()
	local view_info_cfg = self.juanxian_view_info_cfg
	if nil == view_info_cfg or IsEmptyTable(view_info_cfg) then
		return
	end

	local asset1, bundle1 = ResPath.GetOperationActivityImagePath(view_info_cfg.text_bg)
	self.node_list["text_bg"].image:LoadSprite( asset1, bundle1,function ()
		self.node_list["text_bg"].image:SetNativeSize()
	end)

	local asset2, bundle2 = ResPath.GetF2RawImagesPNG(view_info_cfg.jx_xuanchuantu)
	self.node_list["jx_xuanchuantu"].raw_image:LoadSprite(asset2, bundle2,function ()
		self.node_list["jx_xuanchuantu"].raw_image:SetNativeSize()
	end)

	local asset3, bundle3 = ResPath.GetF2RawImagesPNG(view_info_cfg.jx_xuanchuantu_line)
	self.node_list["jx_xuanchuantu_line"].raw_image:LoadSprite(asset3, bundle3,function ()
		self.node_list["jx_xuanchuantu_line"].raw_image:SetNativeSize()
	end)

	local asset4, bundle4 = ResPath.GetOperationJuanXianImagePath(view_info_cfg.bg_left_person)
	self.node_list["bg_left_person"].image:LoadSprite(asset4, bundle4,function ()
		self.node_list["bg_left_person"].image:SetNativeSize()
	end)

	local asset5, bundle5 = ResPath.GetF2RawImagesPNG(view_info_cfg.jx_xuanchuantu_bg)
	self.node_list["jx_xuanchuantu_bg"].raw_image:LoadSprite(asset5, bundle5,function ()
		self.node_list["jx_xuanchuantu_bg"].raw_image:SetNativeSize()
	end)

	self.node_list["slider_bg"].image:LoadSprite(ResPath.GetOperationActivityImagePath(view_info_cfg.slider_bg))
	self.node_list["reward_bg"].image:LoadSprite(ResPath.GetOperationActivityImagePath(view_info_cfg.reward_bg))

	self.node_list["jx_biaoyu_text"].text.text = view_info_cfg.biaoyu_text
end

function OperationActivityView:OnBtnXYLClickHnadler()
	ServerActivityWGCtrl.Instance:OpenXunYuLuView()
end


---------------------------------- 全服每日捐献奖励---------------------------
SerOperationQfjxRewardItemRender = SerOperationQfjxRewardItemRender or BaseClass(BaseRender)
function SerOperationQfjxRewardItemRender:__init(instance)
	
end

function SerOperationQfjxRewardItemRender:LoadCallBack()
	self:SetSliderImg()
end

function SerOperationQfjxRewardItemRender:__delete()

end

function SerOperationQfjxRewardItemRender:OnFlush()
	if not self.data then
		return
	end
	--self:SetRenderInfo()
end

-- function SerOperationQfjxRewardItemRender:SetRenderInfo()
-- 	--最后一个做适配的，非有效数据
-- 	if self.data.index == 1000 then
-- 		self.node_list.slide_bg:SetActive(false)
-- 		self.node_list.up:SetActive(false)
-- 		self.node_list.down:SetActive(false)
-- 		self.node_list["slide_value"]:SetActive(false)
-- 		return
-- 	end

-- 	local max_count = #OperationJuanXianWGData.Instance:GetQfjxDayRewardCfg()
-- 	local state = OperationJuanXianWGData.Instance:GetGiftState(self.data.index)
-- 	local rate = OperationJuanXianWGData.Instance:IsActiveServerJXReward(self.data.index)
-- 	self.node_list.up:SetActive(self.data.index % 2 ~= 0)
-- 	self.node_list.down:SetActive(self.data.index % 2 == 0)
-- 	self.node_list.left_line:SetActive(self.data.index == 1)
-- 	self.node_list.right_line:SetActive(self.data.index == max_count)
-- 	self.node_list.slide_bg:SetActive(true)

-- 	self.node_list["slide_value"].slider.value = rate
-- 	self.node_list["slide_value"]:SetActive(true)

-- 	if self.data.index % 2 ~= 0 then
-- 		self.node_list.up_slide.slider.value = rate
-- 		self.node_list["up_type_" .. OPERATION_SHOW_TYPE.TWO]:SetActive(self.data.icon_type == OPERATION_SHOW_TYPE.TWO)
-- 		self.node_list["up_type_" .. OPERATION_SHOW_TYPE.ONE]:SetActive(self.data.icon_type == OPERATION_SHOW_TYPE.ONE)
-- 		self.node_list["up_type_"..self.data.icon_type].button:AddClickListener(BindTool.Bind(self.OnBtnClick, self))

-- 		if self.data.icon_type == OPERATION_SHOW_TYPE.ONE then
-- 			self.node_list.up_bx_redpoint:SetActive(state == OPERATION_REAWAD_STATE.KLQ)
-- 			self.node_list.up_ke_lingqu:SetActive(state == OPERATION_REAWAD_STATE.KLQ)
-- 			self.node_list.up_yilingqu:SetActive(state == OPERATION_REAWAD_STATE.YLQ)
-- 			XUI.SetGraphicGrey(self.node_list["up_parent"], state == OPERATION_REAWAD_STATE.YLQ)

-- 			local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.param1)
-- 			if item_cfg then
-- 				self.node_list.up_bx.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
-- 				self.node_list.up_quality_bg.image:LoadSprite(ResPath.GetCommonBackGround("bg_cell_circle2_" .. item_cfg.color))
-- 			end

-- 			if self.data.param2 > 1 then
-- 				self.node_list.up_num_bg:SetActive(true)
-- 				self.node_list.up_num.text.text = self.data.param2
-- 			else
-- 				self.node_list.up_num_bg:SetActive(false)
-- 			end

-- 		elseif self.data.icon_type == OPERATION_SHOW_TYPE.TWO then
-- 			self.node_list["up_boss_type"].text.text = self.data.desc
-- 			self.node_list.up_bx_redpoint:SetActive(false)
-- 			self.node_list.up_ke_lingqu:SetActive(false)
-- 			self.node_list.up_yilingqu:SetActive(state == OPERATION_REAWAD_STATE.KLQ)
-- 			XUI.SetGraphicGrey(self.node_list["up_parent"], state == OPERATION_REAWAD_STATE.KLQ)
-- 			self.node_list["up_type_2"].image:LoadSprite(ResPath.GetOperationJuanXianImagePath(self.data.icon))
-- 		end

-- 	else
-- 		self.node_list.down_slide.slider.value = rate
-- 		self.node_list["down_type_" .. OPERATION_SHOW_TYPE.TWO]:SetActive(self.data.icon_type == OPERATION_SHOW_TYPE.TWO)
-- 		self.node_list["down_type_".. OPERATION_SHOW_TYPE.ONE]:SetActive(self.data.icon_type == OPERATION_SHOW_TYPE.ONE)
-- 		self.node_list["down_type_"..self.data.icon_type].button:AddClickListener(BindTool.Bind(self.OnBtnClick, self))

-- 		if self.data.icon_type == OPERATION_SHOW_TYPE.ONE then
-- 			self.node_list.down_bx_redpoint:SetActive(state == OPERATION_REAWAD_STATE.KLQ)
-- 			self.node_list.down_yilingqu:SetActive(state == OPERATION_REAWAD_STATE.YLQ)
-- 			self.node_list.down_ke_lingqu:SetActive(state == OPERATION_REAWAD_STATE.KLQ)
-- 			XUI.SetGraphicGrey(self.node_list["down_parent"], state == OPERATION_REAWAD_STATE.YLQ)
		
-- 			local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.param1)
-- 			if item_cfg then
-- 				self.node_list.down_bx.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
-- 				self.node_list.down_quality_bg.image:LoadSprite(ResPath.GetCommonBackGround("bg_cell_circle2_" .. item_cfg.color))
-- 			end

-- 			if self.data.param2 > 1 then
-- 				self.node_list.down_num_bg:SetActive(true)
-- 				self.node_list.down_num.text.text = self.data.param2
-- 			else
-- 				self.node_list.down_num_bg:SetActive(false)
-- 			end

-- 		elseif self.data.icon_type == OPERATION_SHOW_TYPE.TWO then
-- 			self.node_list["down_boss_type"].text.text = self.data.desc
-- 			self.node_list.down_bx_redpoint:SetActive(false)
-- 			self.node_list.down_ke_lingqu:SetActive(false)
-- 			self.node_list.down_yilingqu:SetActive(state == OPERATION_REAWAD_STATE.KLQ)
-- 			XUI.SetGraphicGrey(self.node_list["down_parent"], state == OPERATION_REAWAD_STATE.KLQ)
-- 			self.node_list["down_type_2"].image:LoadSprite(ResPath.GetOperationJuanXianImagePath(self.data.icon))
-- 		end
-- 	end
-- end

-- function SerOperationQfjxRewardItemRender:OnBtnClick()
-- 	if self.data.icon_type == OPERATION_SHOW_TYPE.ONE then
-- 		local state = OperationJuanXianWGData.Instance:GetGiftState(self.data.index)
-- 		if state == OPERATION_REAWAD_STATE.KLQ then
-- 			OperationJuanXianWGCtrl.Instance:SendActivityRewardOp(OPERATION_QUANFU_JUANXIAN_OP_TYPE.TYPE_SERVER_REWARD, self.data.index)
-- 		else
-- 			OperationJuanXianWGCtrl.Instance:OpenJXRewardTip(self.data)
-- 		end
-- 	else
-- 		--打开礼包界面
-- 		OperationJuanXianWGCtrl.Instance:OpenJXRewardTip(self.data)
-- 	end
-- end

function SerOperationQfjxRewardItemRender:SetSliderImg()
	local juanxian_view_info_cfg = OperationJuanXianWGData.Instance:GetViewCfgByInterface()
	if juanxian_view_info_cfg then
		self.node_list.down_slide_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(juanxian_view_info_cfg.one_slider_bg))
		self.node_list.down_slider_fill.image:LoadSprite(ResPath.GetOperationActivityImagePath(juanxian_view_info_cfg.one_slider_fill))	
		self.node_list.up_slide_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(juanxian_view_info_cfg.one_slider_bg))
		self.node_list.up_slider_fill.image:LoadSprite(ResPath.GetOperationActivityImagePath(juanxian_view_info_cfg.one_slider_fill))
		self.node_list["slide_value_bg"].image:LoadSprite(ResPath.GetOperationActivityImagePath(juanxian_view_info_cfg.slider_value_bg))	
	end
end

-------------------------------------------个人捐献奖励------------------------------------
SerOperationJXRoleRewardItemRender = SerOperationJXRoleRewardItemRender or BaseClass(BaseRender)
function SerOperationJXRoleRewardItemRender:__init()

end

function SerOperationJXRoleRewardItemRender:LoadCallBack()
	self.node_list.btn_mask.button:AddClickListener(BindTool.Bind(self.OnBtnMaskClick, self))
end

function SerOperationJXRoleRewardItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function SerOperationJXRoleRewardItemRender:OnFlush()
	if not self.data then
		return
	end

	if self.cell == nil then
		self.cell = ItemCell.New(self.node_list["cell"])
	end
	self.cell:SetData(self.data.cfg.reward_item[0])

	local show_num = 0
	local my_count = OperationJuanXianWGData.Instance:GetRoleJuanXianCount()
	local color = "<color=#432912>"
	if my_count < self.data.cfg.juanxian_num then
		color = "<color=#ff0000>"
		show_num = my_count
	else
		show_num = self.data.cfg.juanxian_num
	end
	self.node_list.txt.text.text = string.format("%s%d/%d</color>",color, show_num, self.data.cfg.juanxian_num)

	local state = OperationJuanXianWGData.Instance:GetRoleRewardState(self.data.cfg.index)
	self.node_list.btn_mask:SetActive(state == OPERATION_REAWAD_STATE.KLQ)
	self.cell:SetLingQuVisible(state == OPERATION_REAWAD_STATE.YLQ)
	self.cell:SetCanOperateIconVisible(state == OPERATION_REAWAD_STATE.KLQ)

end

function SerOperationJXRoleRewardItemRender:OnBtnMaskClick()
	OperationJuanXianWGCtrl.Instance:SendActivityRewardOp(OPERATION_QUANFU_JUANXIAN_OP_TYPE.TYPE_ROLE_REWARD, self.data.cfg.index)
end

