HolyHeavenlyDomainMoBaiTaskView = HolyHeavenlyDomainMoBaiTaskView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainMoBaiTaskView:__init()
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_mobai_task")
	self.view_cache_time = 0
    self.active_close = false
    self.is_safe_area_adapter = true

	self.select_boss_data = {}
end

function HolyHeavenlyDomainMoBaiTaskView:LoadCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

	if not self.player_score_rank_list then
		self.player_score_rank_list = AsyncListView.New(HHDPlayerScoreRankListItemRender, self.node_list.player_score_rank)
	end

	self.node_list.desc_task.text.text = Language.HolyHeavenlyDomain.FBMoBaiDesc
	XUI.AddClickEventListener(self.node_list.btn_go_worship, BindTool.Bind(self.OnClickGoWorship, self))
	XUI.AddClickEventListener(self.node_list.btn_start_worship, BindTool.Bind(self.OnClickStartWorship, self))
end

function HolyHeavenlyDomainMoBaiTaskView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end

	local task_pro, total_task_pro = HolyHeavenlyDomainWGData.Instance:GetWorshipProgress()
	self.node_list.btn_start_worship:CustomSetActive(task_pro < total_task_pro)
end

function HolyHeavenlyDomainMoBaiTaskView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	if self.player_score_rank_list then
		self.player_score_rank_list:DeleteMe()
		self.player_score_rank_list = nil
	end
end

function HolyHeavenlyDomainMoBaiTaskView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	if self.node_list["fb_task_root"] then
		self.obj = self.node_list["fb_task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

	if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function HolyHeavenlyDomainMoBaiTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function HolyHeavenlyDomainMoBaiTaskView:OnFlush()
	local data_list = HolyHeavenlyDomainWGData.Instance:GetMoBaiFbPlayerRankList()
	local no_data = IsEmptyTable(data_list)
	self.node_list.no_rank_data:CustomSetActive(no_data)
	self.player_score_rank_list:SetDataList(data_list)

	local task_pro, total_task_pro = HolyHeavenlyDomainWGData.Instance:GetWorshipProgress()
	local color = task_pro >= total_task_pro and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.desc_go_worship.text.text = string.format(Language.HolyHeavenlyDomain.MoBaiProgress, color, task_pro, total_task_pro)
end

function HolyHeavenlyDomainMoBaiTaskView:OnClickGoWorship()
	local task_pro, total_task_pro = HolyHeavenlyDomainWGData.Instance:GetWorshipProgress()

	if task_pro < total_task_pro then
		local scene_type = Scene.Instance:GetSceneType()

		if scene_type == SceneType.CROSS_DIVINE_DOMAIN_MOBAI then
			local scene_logic = Scene.Instance:GetSceneLogic()

			if scene_logic and scene_logic.GoToWorshipPos then
				scene_logic:GoToWorshipPos()
			end
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.NoWorShipObject)
	end
end

function HolyHeavenlyDomainMoBaiTaskView:OnClickStartWorship()
	self.node_list.btn_start_worship:CustomSetActive(false)
	self:OnClickGoWorship()
end

---------------------------HHDPlayerScoreRankListItemRender-----------------------------
HHDPlayerScoreRankListItemRender = HHDPlayerScoreRankListItemRender or BaseClass(BaseRender)

function HHDPlayerScoreRankListItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local rank = self.data.rank
	local is_top_three = rank <= 3
	local rank_str = is_top_three and "" or rank
	self.node_list.rank.text.text = rank_str

	if is_top_three then
		local b,a = ResPath.GetCommonIcon("a3_tb_jp" .. rank)
		self.node_list.rank_img.image:LoadSprite(b, a, function()
			self.node_list.rank_img.image:SetNativeSize()
		end)

		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_"..rank))
	else
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
	end

	self.node_list.rank_img:CustomSetActive(is_top_three)
	self.node_list.name.text.text = self.data.name
	self.node_list.score.text.text = self.data.rank_score
end
