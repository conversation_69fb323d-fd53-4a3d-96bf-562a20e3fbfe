KfOneVOneSceneLogic = KfOneVOneSceneLogic or BaseClass(CrossServerSceneLogic)

function KfOneVOneSceneLogic:__init()
	self.is_change_camera_mode = false
	self.next_get_all_move_obj_time = 0					-- 下次获取移动对象的时间
end

function KfOneVOneSceneLogic:__delete()
	if self.close_timer_quest12 ~= nil then
	GlobalTimerQuest:CancelQuest(self.close_timer_quest12)
	self.close_timer_quest12 = nil
	end
	self.is_change_camera_mode = nil
	self.camera_distance = nil
end

function KfOneVOneSceneLogic:ReleaseCallBack()

end

-- 进入场景
function KfOneVOneSceneLogic:Enter(old_scene_type, new_scene_type)
	if CAMERA_TYPE == 1 then
		MainuiWGCtrl.Instance:ChangeCameraMode()
		self.is_change_camera_mode = true
	end
	self.next_get_all_move_obj_time = 0
	GlobalEventSystem:Fire(OtherEventType.ONEVONE_STATUS_CHANGE,false)
	self.camera_distance = MainCameraFollow.Distance
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()
	self.updatekf1v1_status_event = GlobalEventSystem:Bind(KFONEVONE1v1Type.KF_STATUS_CHANGE, BindTool.Bind1(self.UpdataStatus, self))
	self:UpdataStatus()
	Field1v1WGCtrl.Instance:OpenOneVOneHeadView()--血条计时特效
	MainuiWGCtrl.Instance:SetShrinkButtonIsOn(false)
	MainuiWGCtrl.Instance:SetCrossServerTaskUI(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelCJ)
	MainuiWGCtrl.Instance:SetBtnLevel(false)
	-- MainuiWGCtrl.Instance:SetMianUILeftContentState(false)

	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))
	self.shadow_enter_event = GlobalEventSystem:Bind(SceneEventType.OBJ_ENTER_SHADOW, BindTool.Bind(self.OnShadowEnter, self))
	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))
	self.guaji_change_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.BindGuaJiFireEvent,self))


	local cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
	local info = KFOneVOneWGData.Instance:GetMyEnemyInfo()
	local main_role = Scene.Instance:GetMainRole()
	local list_role = Scene.Instance:GetRoleList()
		for k,v in pairs(list_role) do
			v:SetDirectionByXY(cfg.pos_1_x_1, cfg.pos_1_y_1)
		end
		main_role:SetDirectionByXY(cfg.pos_1_x_1, cfg.pos_1_y_1)
-------------------------------------------注释代码暂时留着  策划暂时该地图  后续还要调整-------------------------------------------------------------------
	 	-- local cg_name = info.side == 1 and "CG_JingJiChang" or "CG_JingJiChangRight"
	 	-- if info.side == 1 then
	 	local angle_x
	 	local angle_y
	 	local info_side = KFOneVOneWGData.Instance:GetMyEnemyInfoSide()
	 	if info_side == 0 then
	 		angle_x = 22
	 		angle_y = 275
	 	else
	 		angle_x = 24
	 		angle_y = 98
	 	end

		local scene_id = Scene.Instance:GetSceneId()
		local param_t = {scene_id = scene_id}
		Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, angle_x, angle_y, 12, param_t)
		GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
	  	GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
	  		GlobalEventSystem:Fire(OtherEventType.ONEVONE_STATUS_CHANGE,true)
	  	end, self), 0.3)
	  	end, self), 0.05)
---------------------------------------------------------------------------------------------------------
end

function KfOneVOneSceneLogic:OnRoleEnter(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	local role_vo = role:GetVo()
	if role and role:IsRole() and Scene.Instance:IsEnemy(role) then
		GuajiWGCtrl.Instance:ClearTemporary()
		if GuajiCache.guaji_type == GuajiType.None then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
		GuajiWGCtrl.Instance:DoAttackTarget(role)
	end
end

function KfOneVOneSceneLogic:OnShadowEnter(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	local role_vo = role:GetVo()
	if role and role:IsRole() and Scene.Instance:IsEnemy(role) then
		GuajiWGCtrl.Instance:ClearTemporary()
		if GuajiCache.guaji_type == GuajiType.None then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
		GuajiWGCtrl.Instance:DoAttackTarget(role)
	end
end

function KfOneVOneSceneLogic:Out(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		KF3V3WGCtrl.Instance:CheckOpenActEndView(new_scene_type, {act_type = ACTIVITY_TYPE.KF_ONEVONE})
	end

	GlobalEventSystem:UnBind(self.fly_down_end_event)
	self.fly_down_end_event = nil

	GlobalEventSystem:UnBind(self.role_enter_event)
	self.role_enter_event = nil

	GlobalEventSystem:UnBind(self.shadow_enter_event)
	self.shadow_enter_event = nil

	GlobalEventSystem:UnBind(self.guaji_change_event)
	self.guaji_change_event = nil

	if self.is_change_camera_mode then
		MainuiWGCtrl.Instance:ChangeCameraMode(true)
		self.is_change_camera_mode = false
	end
	CrossServerSceneLogic.Out(self)
	Field1v1WGCtrl.Instance:CloseOneVOneHeadView()
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelFB)
	if self.updatekf1v1_status_event then
		GlobalEventSystem:UnBind(self.updatekf1v1_status_event)
		self.updatekf1v1_status_event = nil
	end
	MainuiWGCtrl.Instance:SetCrossServerTaskUI(true)
	MainCameraFollow.enabled = true
	KFOneVOneWGData.Instance:SetFightTimestampType(KUAFUONEVONE_STATUS.END)
	MainuiWGCtrl.Instance:SetMianUILeftContentState(true)
end

function KfOneVOneSceneLogic:UpdataStatus()
	local fight_timestamp_type = KFOneVOneWGData.Instance:GetFightTimestampType()
	if fight_timestamp_type == KUAFUONEVONE_STATUS.PREPARE then
		AStarFindWay:SetBlockChangeCallback(BindTool.Bind(self.HandleMovePosOperate, self))
		self:RemoveSceneBlock()
		self:HandleMovePosOperate()
	elseif fight_timestamp_type == KUAFUONEVONE_STATUS.AWAIT then
		local info_time = KFOneVOneWGData.Instance:GetFightStartTimestamp()
		if not info_time then
			info_time = TimeWGCtrl.Instance:GetServerTime() + 3
		end
		UiInstanceMgr.Instance:ShowFBStartDown2(info_time,BindTool.Bind(self.HandleMovePosOperate,self))
	end
end

function KfOneVOneSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end

	local go_flag = KFOneVOneWGData.Instance:GetNeedGoPosFlag()
	if go_flag then
		KFOneVOneWGData.Instance:SetNeedGoPosFlag(false)
		local cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
		local scene_id = Scene.Instance:GetSceneId()
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type ~= SceneType.Kf_OneVOne then
			return
		end
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, cfg.pos_1_x_1, cfg.pos_1_y_1,3)
	end
end

function KfOneVOneSceneLogic:HandleMovePosOperate()
	local cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type ~= SceneType.Kf_OneVOne then
		return
	end

	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		KFOneVOneWGData.Instance:SetNeedGoPosFlag(false)
	end)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, cfg.pos_1_x_1, cfg.pos_1_y_1,3)
	KFOneVOneWGData.Instance:SetNeedGoPosFlag(true)
end

function KfOneVOneSceneLogic:CheckSceneMapBlock(scene_id)
	local fight_timestamp_type = KFOneVOneWGData.Instance:GetFightTimestampType()
	if fight_timestamp_type == KUAFUONEVONE_STATUS.PREPARE then
		self:RemoveSceneBlock()
		return
	end
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		return
	end

	self.act_id = act_scene.act_id
	self.is_map_block = true
	self:SetSceneBlock()
 	local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
	if nil == self.role_bubble_timer and map_block_bubble_cfg then
		self.role_bubble_timer = GlobalTimerQuest:AddRunQuest(
			function()
				self:SetRoleBubble(scene_id)
			end, map_block_bubble_cfg.show_time)
	end
end


function KfOneVOneSceneLogic:GuaiJiRoleUpdate(now_time, elapse_time)
	self:SetGuaiJi(GUAI_JI_TYPE.MONSTER)
end

-- 角色是否是敌人
function KfOneVOneSceneLogic:IsRoleEnemy(target_obj, main_role)
	return true
end

-- 是否是挂机打怪的敌人
function KfOneVOneSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() ~= SceneObjType.Role
		or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

-- 获取挂机打怪的敌人
function KfOneVOneSceneLogic:GetGuiJiMonsterEnemy()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local fight_start_timestmap = KFOneVOneWGData.Instance:GetFightStartTimestamp()
	if fight_start_timestmap >= server_time then
		return nil
	end

	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	return Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
end

function KfOneVOneSceneLogic:CanAutoRotation()
	return true
end

function KfOneVOneSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 获取角色名字颜色
function KfOneVOneSceneLogic:GetColorName(role_vo)
	local my_uuid = RoleWGData.Instance:GetUUid()
	local color_name = ""
	local color = role_vo.vo.uuid == my_uuid and COLOR3B.WHITE or COLOR3B.RED
	color_name = role_vo.vo.name
	return color_name, color
end

function KfOneVOneSceneLogic:GetGuajiPos()
	local pos_x,pos_y = self:GetGuiJiEnemyPos()
	if pos_x == nil and pos_y == nil then
		local cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
		pos_x = cfg.pos_1_x_1
		pos_y = cfg.pos_1_y_1
	end
	return pos_x,pos_y
end

-- 获取挂机打怪的位置
function KfOneVOneSceneLogic:GetGuiJiEnemyPos()
    local target_x = nil
    local target_y = nil
    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil or main_role:IsDeleted() then
		return
    end
    local x, y = main_role:GetLogicPos()
    local my_vo = main_role:GetVo()
    local mian_obj_id = my_vo.obj_id

    local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
    for k, v in pairs(obj_move_info_list) do
        local vo = v:GetVo()
        if vo.obj_type == SceneObjType.Role and vo.obj_id ~= mian_obj_id then
            target_x = vo.pos_x
            target_y = vo.pos_y
        end
    end
    return target_x, target_y
end

function KfOneVOneSceneLogic:BindGuaJiFireEvent(guaji_type)
	if guaji_type == GuajiType.Auto then
		if Status.NowTime >= self.next_get_all_move_obj_time then
			self.next_get_all_move_obj_time = Status.NowTime + 10
			Scene.SendGetAllObjMoveInfoReq()
		end
	end
end