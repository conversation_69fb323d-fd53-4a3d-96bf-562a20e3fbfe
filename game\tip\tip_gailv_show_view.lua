--概率展示面板
TipsGaiLvCommonShowView = TipsGaiLvCommonShowView or BaseClass(SafeBaseView)

function TipsGaiLvCommonShowView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(866, 516)})
    self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_common_probability_show")
    self:SetMaskBg(true, true)
end

function TipsGaiLvCommonShowView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function TipsGaiLvCommonShowView:SetDataAndOpen(info)
	self.info = info
    self:Open()
end

function TipsGaiLvCommonShowView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TreasureHunt.ProbabilityTitle
    if not self.probability_list then
        self.probability_list = AsyncListView.New(TipsGaiLvRender, self.node_list.ph_pro_list) 
    end
end

function TipsGaiLvCommonShowView:OnFlush()
    if not self.info then
        return
    end

    self.probability_list:SetDataList(self.info)
end

----------------------------------------------------------------------------------
TipsGaiLvRender = TipsGaiLvRender or BaseClass(BaseRender)
function TipsGaiLvRender:__delete()
    
end

function TipsGaiLvRender:LoadCallBack()
    
end

function TipsGaiLvRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.bg:SetActive(self.index % 2 == 1)
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id) or ""
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count.."%"
end
