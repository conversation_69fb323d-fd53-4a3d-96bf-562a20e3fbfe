GuildView = GuildView or BaseClass()

function GuildView:InitGuildActShouHuView()
	if self.node_list.shouhu_tip_btn then
		XUI.AddClickEventListener(self.node_list["shouhu_tip_btn"], BindTool.Bind(self.OnClickGuildActShouHuTipBtn, self))
	end
	if self.node_list.shouhu_reward_list then
		self.shouhu_reward_list = AsyncListView.New(ItemCell, self.node_list.shouhu_reward_list)
	end

	if self.node_list.shouhu_goto_btn then
		XUI.AddClickEventListener(self.node_list["shouhu_goto_btn"], BindTool.Bind(self.GuildActShouHuGoFunc, self))
	end

	-- local item_list = {}
	-- for i=1,3 do
	-- 	item_list[i] = GuildActShouHuRankItem.New(self.node_list["rank_item_" .. i])
	-- end
	-- self.rank_item_list = item_list
end

function GuildView:DeleteGuildActShouHuView()
	if self.shouhu_reward_list then
		self.shouhu_reward_list:DeleteMe()
		self.shouhu_reward_list = nil
	end

	-- if self.rank_item_list then
	-- 	for _,v in pairs(self.rank_item_list) do
	-- 		v:DeleteMe()
	-- 	end
	-- 	self.rank_item_list = nil
	-- end
end

function GuildView:ShowGuildActShouHuCallBack()

end

function GuildView:OnFlushGuildActShouHuView(param_t, index)
	local data = GuildActivityWGData.Instance:GetActDataByType(GuildActivityWGData.Act_Type.ShouHu)
	if not data or not data.cfg then
		return 
	end
	local str = string.format("%s %s-%s", data.act_hall_cfg.open_tips, data.act_hall_cfg.open_time, data.act_hall_cfg.close_time)
	str = ToColorStr(str, COLOR3B.D_GREEN)
	self.node_list.shouhu_kaiqi_label.text.text = string.format(Language.GuildBoss.OpenTimeStr, str)
	local reward_list = data.reward_list
	self.shouhu_reward_list:SetDataList(reward_list)
	--self:FlushGuildActShouHuRank()
	local act_is_open = false
	act_is_open = ActivityWGData.Instance:GetActivityIsOpen(data.cfg.act_id)
	self.node_list.shouhu_remind:SetActive(act_is_open)
end

-- function GuildView:FlushGuildActShouHuRank()
-- 	local data_list = GuildWGData.Instance:GetGuildFbRankInfo()
-- 	-- if IsEmptyTable(data_list) then
-- 	-- 	self.node_list.not_rank_img:SetActive(true)
-- 	-- 	self.node_list.rank_root:SetActive(false)
-- 	-- 	return
-- 	-- end
-- 	-- self.node_list.not_rank_img:SetActive(false)
-- 	-- self.node_list.rank_root:SetActive(true)

-- 	-- local item_list = self.rank_item_list
-- 	-- for i=1,#item_list do
-- 	-- 	item_list[i]:SetData(data_list[i])
-- 	-- end
-- end

function GuildView:OnClickGuildActShouHuTipBtn()
	local data = GuildActivityWGData.Instance:GetActDataByType(GuildActivityWGData.Act_Type.ShouHu)
	if not data or not data.cfg then
		return 
	end
	local cfg = data.cfg
	local tips_content = cfg.act_rule
	local act_hall_cfg = data.act_hall_cfg
	if act_hall_cfg then
		local str = string.format(Language.BiZuo.Act_Time_Segment_2, act_hall_cfg.open_tips, act_hall_cfg.open_time, act_hall_cfg.close_time)
		tips_content = string.format(cfg.act_rule, str)
	end
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.Activity.HuoDongShuoMing)
	role_tip:SetContent(tips_content)
end

function GuildView:GuildActShouHuGoFunc()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
    if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return
    end
    local is_finish = GuildWGData.Instance:IsFinishGuildFb()
    if is_finish then
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ShouHuFinish)
    	return
    end
    GuildWGCtrl.Instance:SendGuildFbEnterReq()
end

-- GuildActShouHuRankItem = GuildActShouHuRankItem or BaseClass(BaseRender)

-- function GuildActShouHuRankItem:OnFlush()
-- 	local data = self:GetData()
-- 	if IsEmptyTable(data) then
-- 		self:SetVisible(false)
-- 		return
-- 	end
-- 	self:SetVisible(true)

-- 	local key = string.format("xm_flag%d_%d", data.flag_color, data.flag_id)
-- 	local asset,bundle = ResPath.GetGuildSystemImage(key)
-- 	self.node_list.flag_img.image:LoadSprite(asset, bundle, function ()
-- 		self.node_list.flag_img.image:SetNativeSize()
-- 	end)
-- 	self.node_list.flag_name.text.text = data.flag_name
-- 	self.node_list.guide_name.text.text = data.guild_name
-- end