GuildWarRuleView = GuildWarRuleView or BaseClass(SafeBaseView)

local TOGGLE_MAX = 4

function GuildWarRuleView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(30, 10), sizeDelta = Vector2(922, 586)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_war_rule")
end

function GuildWarRuleView:__delete()
end

function GuildWarRuleView:ReleaseCallBack()
	if self.rule_list_view then
		for k,v in pairs(self.rule_list_view) do
			for i,f in pairs(v) do
				f:DeleteMe()
			end
		end
		self.rule_list_view = {}
	end
	
	self.select_list_index = nil
end

function GuildWarRuleView:LoadCallBack()
	self.rule_list_view = {}

	self.node_list.title_view_name.text.text = Language.Guild.GuildWarRuleTitle
	--self:SetSecondView(nil,self.node_list["size"])
	self.data_cfg = GuildBattleRankedWGData.Instance:GetBattleRuleCfg()
	self.accor_list = {}
	for i = 1, TOGGLE_MAX do
		local btn_obj = self.node_list["SelectBtn" .. i]
		local data = self.data_cfg[i]
		if data then
			self.node_list["SelectBtn" .. i]:SetActive(true)
			if btn_obj then
				self.accor_list[i] = {}
		        self.accor_list[i].text_name = btn_obj:FindObj("nor/BtnText"..i.."_nor")
		        self.accor_list[i].high_text_name = btn_obj:FindObj("hl/text_high_btn"..i)
		        self.accor_list[i].arrow_right = btn_obj:FindObj("nor/ArrowRight")
		        self.accor_list[i].arrow_down = btn_obj:FindObj("hl/ArrowDown")
		        self.accor_list[i].hl = btn_obj:FindObj("hl")
		        self.accor_list[i].nor = btn_obj:FindObj("nor")
				self.accor_list[i].select_State = false
		        self.accor_list[i].btn = self.node_list["SelectBtn" .. i]
		        self.accor_list[i].select_btn = self.node_list["SelectBtn" .. i].gameObject:GetComponent(typeof(UnityEngine.UI.Button))
				self.accor_list[i].select_btn:AddClickListener(BindTool.Bind(self.AccorBtnClickCallback, self, i))
				self.accor_list[i].text_name.text.text = self.data_cfg[i][1].title
		    	self.accor_list[i].high_text_name.text.text = self.data_cfg[i][1].title
		    	self.accor_list[i].arrow_right:SetActive(#data > 1)
		        self.accor_list[i].arrow_down:SetActive(#data > 1)
			end
			
			if #data > 1 and self.accor_list[i] then
				self.accor_list[i].list = self.node_list["List" .. i]
				self.accor_list[i].list_item_cell = {}
				self:LoadCell(i,self.data_cfg[i])
			end
		else
			self.node_list["SelectBtn" .. i]:SetActive(false)
		end
	end
end

function GuildWarRuleView:LoadCell(index,data) -- 加载左边list中的 cell
	self.rule_list_view[index] = {}
	local prefab_obj = self.node_list["item_type_prefab"].gameObject
	for i = 1,#data do
		local obj = ResMgr:Instantiate(prefab_obj)
		local obj_transform = obj.transform
		if self.accor_list[index].list then
			obj_transform:SetParent(self.accor_list[index].list.transform, false)
			obj:GetComponent("Toggle").group = self.accor_list[index].list.toggle_group
			self.accor_list[index].list_item_cell[i] = obj
		end
		local item_cell = GuildWarRuleItem.New(obj)
		item_cell:SetClickCallBack(BindTool.Bind(self.ClickRuleItem,self))
		item_cell:SetData(data[i])
		self.rule_list_view[index][i] = item_cell
	end
end

function GuildWarRuleView:ShowIndexCallBack()
	-- self.accor_list[1].select_btn.isOn = true
	self:AccorBtnClickCallback(1)
end

function GuildWarRuleView:OnFlush()

end

function GuildWarRuleView:AccorBtnClickCallback(index)
	local cfg = GuildBattleRankedWGData.Instance:GetBattleRuleByIndexCfg(index)
	self.select_list_index = index
	--self:HideAllBtnHL()
	self:HideAllList()

	for k, v in pairs(self.accor_list) do
		local state = k == index
		v.hl:SetActive(state)
		v.nor:SetActive(not state)
	end

	-- if self.accor_list[index] then
	-- 	local state = self.accor_list[index].select_State
	-- 	self.accor_list[index].hl:SetActive(not state)
	-- 	self.accor_list[index].nor:SetActive(state)
	-- 	self.accor_list[index].select_State = not state
	-- end
	
	if cfg and #cfg > 1 then
		local list_show = self.accor_list[index].list:GetActive()
		self.accor_list[index].list:SetActive(not list_show)
		local obj = self.accor_list[index].list_item_cell[1]
		if obj ~= nil then
			self:HideAllItemType(index)
			obj:GetComponent("Toggle").isOn = true
		end
	else
		self:FlushContentInfo(cfg[1])
	end
	
end

function GuildWarRuleView:ClickRuleItem(data)
	self:FlushContentInfo(data)
end

function GuildWarRuleView:HideAllBtnHL()
	for k,v in pairs(self.accor_list) do
		if v.hl and v.nor then
			v.hl:SetActive(false)
			v.nor:SetActive(true)
			v.select_state = false
		end
	end
end

function GuildWarRuleView:HideAllList()
	for k,v in pairs(self.accor_list) do
		if v.list and self.select_list_index ~= k then
			v.list:SetActive(false)
		end
	end
end
function GuildWarRuleView:HideAllItemType(index)
	if self.rule_list_view[index] then
		for k,v in pairs(self.rule_list_view[index]) do
			v:SetToggleIsOn(false)
		end
	end
end

function GuildWarRuleView:FlushContentInfo(data)
	self.node_list["rule_text"].text.text = data.content
	self.node_list["title"].text.text = data.child_title
	-- self.node_list["icon_" .. data.img_id]:SetActive(true)
	self.node_list["img"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_zmjh_dt"..data.img_id))
	-- self.node_list["img"].raw_image:SetNativeSize()
end

-----------------------GuildWarRuleItem------------
GuildWarRuleItem = GuildWarRuleItem or BaseClass(BaseRender)

function GuildWarRuleItem:__init()
end

function GuildWarRuleItem:LoadCallBack()
	self.ningju_name = self.node_list["Name"]
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function GuildWarRuleItem:__delete()
	
end

function GuildWarRuleItem:SetToggleIsOn(value)
	self.view.toggle.isOn = value
end

function GuildWarRuleItem:OnClickItem(is_on)
	if is_on then
		--测试代码
		--local cfg = TitleWGData.GetTitleConfig(self.data.title_id)
		--print_error(cfg.item_id)
		if self.click_callback then
			self.click_callback(self.data)
		end
	end
end

function GuildWarRuleItem:OnFlush()
	if self.data == nil then return end
	self.node_list["Name"].text.text = self.data.child_title
	self.node_list["hl_name"].text.text = self.data.child_title
end