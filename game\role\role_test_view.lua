RoleTestView = RoleTestView or BaseClass(SafeBaseView)

function RoleTestView:__init()
	self.view_style = ViewStyle.Full
	self:LoadConfig()
end

function RoleTestView:__delete()

end

function RoleTestView:ReleaseCallBack()

end

function RoleTestView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_skill")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function RoleTestView:LoadCallBack(index, loaded_times)

end

function RoleTestView:OpenCallBack()

end

function RoleTestView:CloseCallBack()
end

function RoleTestView:ShowIndexCallBack(index)
	-- self:Flush()
end

function RoleTestView:OnFlush(param_t)

end

