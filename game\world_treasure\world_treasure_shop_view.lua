function WorldTreasureView:ReleaseCallBack_Shop()
    if self.shop_list_view then
        self.shop_list_view:DeleteMe()
        self.shop_list_view = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
end

function WorldTreasureView:LoadIndexCallBack_Shop()
    self.flash_sale2_grade_change = true
    local bundle = "uis/view/world_treasure_ui_prefab"
	local asset = "shop_render"
    self.shop_list_view = AsyncBaseGrid.New()
    self.shop_list_view:CreateCells({list_view = self.node_list["shop_list_view"],
            assetBundle = bundle, assetName = asset, itemRender = WorldTreasureShopItem, change_cells_num = 1, col = 4})
    self.shop_list_view:SetStartZeroIndex(false)

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

    XUI.AddClickEventListener(self.node_list["btn_grow1"], BindTool.Bind1(self.OnClickBtnGrow, self))
    XUI.AddClickEventListener(self.node_list["btn_grow2"], BindTool.Bind1(self.OnClickBtnGrow, self))
end

function WorldTreasureView:LoadShopImg()
	local bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_2")
	self.node_list.shop_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.shop_bg.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_bt_2")
	self.node_list.shop_title_img.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.shop_title_img.raw_image:SetNativeSize()
	end)

    local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    self.node_list.btn_grow1:SetActive(cur_grade == 1)
    self.node_list.btn_grow2:SetActive(cur_grade > 1)
end

function WorldTreasureView:ShowIndexCallBack_Shop()

end

function WorldTreasureView:OnFlush_Shop(param_t, index)
    if self.flash_sale2_grade_change then
        self.flash_sale2_grade_change = false
        self:LoadShopImg()
    end
    self:UpdateShopView()
end

function WorldTreasureView:UpdateShopView()
    local list_data = WorldTreasureWGData.Instance:GetShopListData()
    self.shop_list_view:SetDataList(list_data)

    -- 图片资源
	-- local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	-- if not IsEmptyTable(grade_cfg) then
	-- 	self:SetImageRes("img_title_shop", grade_cfg.shop_title)
	-- else
    --     local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
	-- 	print_error("未找到对应档位配置,grade:" .. grade)
	-- end
end

function WorldTreasureView:OnClickBtnGrow()
    local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
    FunOpen.Instance:OpenViewNameByCfg(grade_cfg.open_panel_name)
end


WorldTreasureShopItem = WorldTreasureShopItem or BaseClass(BaseRender)

function WorldTreasureShopItem:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind1(self.OnClickBuy, self))

    self.item_cell = ItemCell.New(self.node_list.item_parent)
end

function WorldTreasureShopItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    if self.buy_tips then
        self.buy_tips:DeleteMe()
        self.buy_tips = nil
    end
end

function WorldTreasureShopItem:OnFlush()
    if not self.data then
        return
    end

    local num = WorldTreasureWGData.Instance:GetShopBuyNum(self.data.seq)
    local remain_num = self.data.buy_count_limit - num
    local btn_txt = ""
    --self.node_list.node_shifu_money:SetActive(self.data.type == 2 and remain_num > 0)
    if self.data.type == 2 then
        btn_txt = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
        local buy_str2 = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq, nil, true)
        --self.node_list.text_show_price.text.text = buy_str2
        --UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["image_shifu_bg"].rect)
    -- elseif self.data.type == 1 then
    --     btn_txt = string.format("%s%s", self.data.price, Language.WorldTreasure.LimitBuyBtn1)
    elseif self.data.type == 0 then
        btn_txt = self.data.price
    end

    if remain_num <= 0 then
        btn_txt = Language.WorldTreasure.LimitBuyTxt5
    end

    self.node_list.cost_icon:SetActive(remain_num > 0)
    self.node_list.text_price.text.text = btn_txt
    local reward_item = self.data.reward_item[0]
    self.item_cell:SetData(reward_item)

    local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
    local item_num_color = self.data.buy_count_limit - num >= 1 and COLOR3B.L_GREEN or COLOR3B.L_RED
    self.node_list.text_name.text.text = item_cfg.name
    self.node_list.text_num.text.text = string.format(Language.WorldTreasure.LimitBuyTxt1, item_num_color, self.data.buy_count_limit - num, self.data.buy_count_limit)
    --XUI.SetButtonEnabled(self.node_list["btn_buy"], remain_num > 0)
end

function WorldTreasureShopItem:OnClickBuy()
    local num = WorldTreasureWGData.Instance:GetShopBuyNum(self.data.seq)
    if num >= self.data.buy_count_limit then
        return
    end

    local langu = Language.WorldTreasure
    if self.data.type == 2 then
        RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    else
        local reward_item = self.data.reward_item[0]
        local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
        local cost_name = self.data.type == 1 and langu.LimitBuyBtn1 or langu.LimitBuyBtn0
        WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_HUNZHEN_SHOP_BUY, self.data.grade, self.data.seq, 1)
        -- if not self.buy_tips then
        --     self.buy_tips = Alert.New()
        -- end

        -- if self.data.buy_count_limit - num > 1 then
        --     self.buy_tips:SetSliderShow(true, self.data.buy_count_limit - num, function(buy_num)
        --         if self.buy_tips and self.buy_tips:IsOpen() then
        --             self.buy_tips:SetLableString(string.format(langu.LimitBuyTxt2, self.data.price * buy_num, cost_name, buy_num, item_cfg.name))
        --         end
        --     end)
        -- else
        --     self.buy_tips:SetSliderShow(false)
        -- end

        -- self.buy_tips:SetLableString(string.format(langu.LimitBuyTxt2, self.data.price, cost_name, 1, item_cfg.name))
        -- self.buy_tips:SetOkFunc(function ()
        --     if self:IsOpen() then
        --         if self.data.type == 1 then
        --                 local score = YanYuGeWGData.Instance:GetCurScore()
        --                 if score < self.data.price then
        --                     TipWGCtrl.Instance:ShowSystemMsg(langu.LimitBuyTxt4)
        --                     return
        --                 end
        --         else
        --             if not RoleWGData.Instance:GetIsEnoughUseGold(self.data.price) then
        --                 TipWGCtrl.Instance:ShowSystemMsg(langu.LimitBuyTxt3)
        --                 return
        --             end
        --         end

        --         WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_HUNZHEN_SHOP_BUY, self.data.grade, self.data.seq, self.buy_tips.slider_cur_num)
        --     end
        -- end)
        -- self.buy_tips:Open()
    end
end