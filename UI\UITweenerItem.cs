﻿using System;
using UnityEngine;
using DG.Tweening;

[Serializable]
public sealed class UITweenerItem
{
    [SerializeField]
    private RectTransform targetRect;

    public RectTransform TargetRect
    {
        get { return targetRect; }
    }

    [SerializeField]
    private MoveTypeEnum moveType;

    public MoveTypeEnum MoveType
    {
        get { return moveType; }
    }

    [SerializeField]
    private uint distance;

    public uint Distance
    {
        get { return distance; }
    }

    [SerializeField]
    private Ease ease;

    public Ease Ease
    {
        get { return ease; }
    }

    [SerializeField]
    private bool isAlpha;

    public bool IsAlpha
    {
        get { return isAlpha; }
    }

    [SerializeField]
    private Ease alphaEase;

    public Ease AlphaEase
    {
        get { return alphaEase; }
    }

    [SerializeField]
    private float moveTime;

    public float MoveTime
    {
        get { return moveTime; }
    }

    [SerializeField]
    private bool copyEase;
}

public enum MoveTypeEnum
{
    LeftToRight,
    RightToLeft,
    TopToDown,
    DownToTop,
}