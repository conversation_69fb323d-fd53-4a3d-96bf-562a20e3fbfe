DungeonWeaponSceneLogic = DungeonWeaponSceneLogic or BaseClass(CommonFbLogic)

function DungeonWeaponSceneLogic:__init()
end

function DungeonWeaponSceneLogic:__delete()
	
end

function DungeonWeaponSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- if MainuiWGCtrl.Instance:GetMenuIsShow() then
	-- 	DungeonWGCtrl.Instance:CloseCommonFollowView()
	-- else
	-- 	DungeonWGCtrl.Instance:OpenCommonFollowView(DUNGEON_COMMONFOLLOW_TYPE.WEAPON)
	-- end
end

function DungeonWeaponSceneLogic:Out()
	CommonFbLogic.Out(self)
	
	-- DungeonWGCtrl.Instance:CloseRollWin()
	-- DungeonWGCtrl.Instance:CloseCommonFollowView()
	-- local role_level = RoleWGData.Instance.role_vo.level
	-- if DungeonData.Instance:GetWeaponCanEnterTimes() > 0 and role_level >= DUNGEON_OPEN_WIN_LEVEL then
	-- 	-- FunOpen.Instance:OpenViewByName(GuideModuleName.Fuben, TabIndex.fb_weapon)
	-- 	DungeonWGCtrl.Instance:Open(DUNGEON_TAB_TYPE.WEAPON)
	-- end
end

function DungeonWeaponSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
	-- DungeonWGCtrl.Instance:ShowCommonFollowAction(is_show)
end