TodaySpecialWGData = TodaySpecialWGData or BaseClass()

function TodaySpecialWGData:__init()
	if TodaySpecialWGData.Instance ~= nil then
		ErrorL<PERSON>("[TodaySpecialWGData] attempt to create singleton twice!")
		return
	end

	TodaySpecialWGData.Instance = self

	self:InitCfg()
	self.grade = 1 -- 档次
	self.rmb_buy_times_list = {}
end

function TodaySpecialWGData:__delete()
	TodaySpecialWGData.Instance = nil
	self.grade = nil
	self.rmb_buy_times_list = nil
end

function TodaySpecialWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_limit_rmb_buy2_auto")
	self.rmb_buy_seq_cfg = ListToMapList(cfg.rmb_buy, "grade", "activity_day")
	self.once_rmb_buy_cfg = ListToMap(cfg.rmb_buy_one_key, "grade", "activity_day")
end

function TodaySpecialWGData:SetAllBuyInfo(protocol)
	self.grade = protocol.grade
	self.rmb_buy_times_list = protocol.rmb_buy_times_list
end

function TodaySpecialWGData:GetAllBuyGradeInfo()
	local cur_grade = self.grade
	return cur_grade or 1
end

function TodaySpecialWGData:GetIsCanAllBuy()
	local can_all_buy = true
	local rmb_buy_times_list = self.rmb_buy_times_list
	if not IsEmptyTable(rmb_buy_times_list) then
		for k, v in pairs(rmb_buy_times_list) do
			if v == 1 then
				can_all_buy = false
			end
		end
	end

	return can_all_buy 
end

function TodaySpecialWGData:GetCurDayBuyList()
	local cur_grade_cfg = self.rmb_buy_seq_cfg[self.grade]
	if not IsEmptyTable(cur_grade_cfg) then
		local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY2)
		if cur_grade_cfg[act_day] then
			return cur_grade_cfg[act_day]
		else
			return {}
		end
	else
		return {}
	end
end

function TodaySpecialWGData:GetCurDayOnceBuyList()
	local cur_allbuy_grade_cfg = self.once_rmb_buy_cfg[self.grade]
	if not IsEmptyTable(cur_allbuy_grade_cfg) then
		local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY2)
		if cur_allbuy_grade_cfg[act_day] then
			return cur_allbuy_grade_cfg[act_day]
		else
			return {}
		end
	else
		return {}
	end
end

function TodaySpecialWGData:GetRewardListCfg(index)
	local data_list = self:GetCurDayBuyList()
	return data_list and data_list[index] or {}
end

function TodaySpecialWGData:GetBuyCountBySeq(seq)
	return self.rmb_buy_times_list[seq] or 0
end

function TodaySpecialWGData:GetBuyStateBySeq(index)
	local reward_cfg = self:GetRewardListCfg(index)
	if reward_cfg == nil then
		return false
	end
	
	local pro_buy_times = self.rmb_buy_times_list[reward_cfg.seq]
	return reward_cfg.buy_times <= pro_buy_times
end

function TodaySpecialWGData:GetBuyJumpSeq() -- 获取索引物品是否买完
	local seq_list = self:GetCurDayBuyList()
	if IsEmptyTable(seq_list) then
		return 1
	end

	local jump_index

	for k, v in pairs(seq_list) do
		if not self:GetBuyStateBySeq(k) then
			jump_index = k
			return jump_index
		end

		if jump_index == nil then
			jump_index = k
		end
	end

	return jump_index
end
