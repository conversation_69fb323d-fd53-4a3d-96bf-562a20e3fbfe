BiZuoWGData = BiZuoWGData or BaseClass()

BiZuoType = {
	Type_1 = 1,-- 日常任务
	Type_2 = 2,-- 竞技场
	Type_3 = 3,-- 渡劫仙舟/伏魔战船
	Type_4 = 4,-- 遗迹之地
	Type_5 = 5,-- 世界首领
	Type_6 = 6,-- 护送活动
	Type_7 = 7,-- 六道轮回
	Type_8 = 8,-- 龙王宝藏
	Type_9 = 9,-- 仙灵圣殿
	Type_10 = 10,--
	Type_11 = 11,-- 仙盟周任务
	
	Type_13 = 13,-- 异界宝墟
	Type_14 = 14,-- 无尽试炼
	Type_31 = 31,-- 上古遗迹
	Type_35 = 35,-- 仙盟建设任务
	Type_36 = 36,-- 仙灵圣殿
	Type_37 = 37,-- 藏宝湾
	Type_44 = 44,-- 摘星阁
	Type_73 = 73,-- 幻兽副本
	Type_74 = 74,-- 女神副本
	Type_75 = 75,-- 武魂副本
	Type_76 = 76,-- 符文塔副本
}

--活动界面枚举:用于打开 跨服进度单个活动信息二级弹窗 时传入
CSProActViewType = {
	SYBoss = 1,		-- 深渊BOSS
	MHSH = 2,		-- 蛮荒神兽
	WQXJ = 3,		-- 温泉仙境
	XLZC = 4,		-- 修罗试炼
	YEZC = 5,		-- 永夜战场
	JJC = 6,		-- 竞技场
	ZXZC = 7,		-- 诛仙战场
	KF1V1 = 8,		-- 跨服1V1
	KF3V3 = 9,		-- 跨服3V3
	XMZB = 10,		-- 仙盟争霸
	LOVERPK = 21,   -- 仙侣PK
}

BiZuoWGData.GetStrongerSubtype = {
	Module = 1, 							-- 系统
	Act = 2, 								-- 活动
}

function BiZuoWGData:__init()
	if BiZuoWGData.Instance ~= nil then
		return
	end
	BiZuoWGData.Instance = self
	self.bizuo_info = {}
	self.complete_times_list = {}
	self.find_list = {}
	self.activity_hall_join_flag = {}  -- 活动参与记录
	self.daily_award_fetch_flag = {}  -- 活动参与记录
	self.activity_hall_signup_flag = {}	-- 活动报名标记

	self.activity_hall_all_info = {} 	--活动大厅界面数据
	self.bizuo_week_cfg_list = {} -- 周历

	self.offline_exp_cfg = ConfigManager.Instance:GetAutoConfig("rest_auto").rest_reward
	self.daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto").week
	self.activity_hall_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").activity_hall
	self.activity_hall_map_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("dailywork_auto").activity_hall, "act_type")
	local crosssetting_cfg = ConfigManager.Instance:GetAutoConfig("crosssetting_auto")
	self.cross_server_progress_act_cfg = ListToMap(crosssetting_cfg.cross_activity, "index")--跨服进度活动配置
	self.cross_activity_open_time_cfg = ListToMap(crosssetting_cfg.cross_activity_open_time, "cross_activity_type") --跨服活动开启时间
	self.cross_activity_open_time1_cfg = ListToMapList(crosssetting_cfg.cross_activity_open_time1, "cross_activity_type") --跨服活动开启时间
	self.get_stronger_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("dailywork_auto").get_stronger, "type") 			-- 每日必做-我要变强配置

	self.task_cfg = {}
	self.is_one_open = false
	self.activity_hall_info_is_dirty = false

	self.day_total_exp = 0
	self.xiuwei_exp = 0

	RemindManager.Instance:Register(RemindName.BiZuo,BindTool.Bind(self.CheckLevelUpRemind,self))
	RemindManager.Instance:Register(RemindName.Daily,BindTool.Bind(self.BizuoDailyRedShow,self))
	--RemindManager.Instance:Register(RemindName.ActivityHall,BindTool.Bind(self.BizuoActivityHallRedShow,self))
	RemindManager.Instance:Register(RemindName.ZhaoHui,BindTool.Bind(self.ZhaoHuiRedShow,self))
	self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.NextDaySetActivityHallCfg, self))

	self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function BiZuoWGData:__delete()
	BiZuoWGData.Instance = nil
	GlobalEventSystem:UnBind(self.pass_day_event)
	RemindManager.Instance:UnRegister(RemindName.BiZuo)
	RemindManager.Instance:UnRegister(RemindName.Daily)
	--RemindManager.Instance:UnRegister(RemindName.ActivityHall)
	RemindManager.Instance:UnRegister(RemindName.ZhaoHui)

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	self.bz_zhaohui_open_flag = nil

	self:CancelDelayFlushTimer()
end

function BiZuoWGData:SetAllInfo(info)
	if IsEmptyTable(info) then return end
	self.bizuo_info = info or {}
	self.activity_hall_join_flag = bit:d2b(info.activity_hall_join_flag_hight or 0)
	self:SetCompleteTimesList(info)
	self:UpdateBiZuoInfo()
	self:SetFindList()
	self.daily_award_fetch_flag = bit:d2b(info.daily_award_fetch_flag or 0)
	self.day_total_exp = info.day_total_exp
	self.xiuwei_exp = info.xiuwei_exp
	self.activity_hall_signup_flag = bit:d2b(info.activity_hall_signup_flag_hight or 0)
end

function BiZuoWGData:FlushAllInfo()
	self:SetAllInfo(self.bizuo_info)
end

function BiZuoWGData:GetIsParticipationBySeq(seq)
	return self.activity_hall_join_flag[32 - seq] == 1
end

function BiZuoWGData:GetIaGetAwrdFlag(seq)
	return self.daily_award_fetch_flag[32 - seq] == 1
end

function BiZuoWGData:GetTotalExp()
	return self.day_total_exp
end

function BiZuoWGData:GetTotalXiuWei()
	return self.xiuwei_exp
end

function BiZuoWGData:GetTime(seq)
	return self.daily_award_fetch_flag[32 - seq] == 1
end

function BiZuoWGData:GetIsSignUpFlagBySeq(seq)
	return self.activity_hall_signup_flag[32 - seq] == 1
end

--报名过的活动，在进入活动后，保存标记 避免再次弹出活动窗口
function BiZuoWGData:SetHasSignUpFlagByEnter(seq)
	if not self.activity_has_signup_enter_list then
		self.activity_has_signup_enter_list = {}
	end
	self.activity_has_signup_enter_list[seq] = true
end

function BiZuoWGData:GetHasSignUpFlagByEnter(seq)
	return self.activity_has_signup_enter_list and self.activity_has_signup_enter_list[seq] or false
end

function BiZuoWGData:ClearHasSignUpFlagByEnter()
	self.activity_has_signup_enter_list = {}
end

function BiZuoWGData:CheckLevelUpRemind()
	if ShowRedPoint.SHOW_RED_POINT == self:BizuoDailyRedShow() then
		return 1
	elseif ShowRedPoint.SHOW_RED_POINT == self:ZhaoHuiRedShow() then
		return 1
	elseif ShowRedPoint.SHOW_RED_POINT == CultivationWGData.Instance:BuffRedShow() then
		return 1
	end
	-- if ShowRedPoint.SHOW_RED_POINT == self:BizuoActivityHallRedShow() then
	-- 	print_error(3)
	-- 	return 1
	-- end
	return 0
end

function BiZuoWGData:BizuoDailyRedShow()
	local total_exp = self:GetTotalExp()
	local _,exp_list = BiZuoWGData.Instance:GetHuoYueRewardListAndValue()
	for i = 1, 8 do
		if not self:GetIaGetAwrdFlag(i-1) then
			if nil ~= exp_list and nil ~= exp_list[i] and nil ~= total_exp then
				if total_exp ~=0 and exp_list[i] <= total_exp then
					return 1
				end
			end
		end
	end

	--local level_info
	--local bizuo_info = self:GetAllInfo()
	-- if bizuo_info then
	-- 	level_info = self:GetLevelCfgByLevel(bizuo_info.level)
	-- end
	-- if bizuo_info and level_info and bizuo_info.exp and level_info.need_exp and bizuo_info.level < self:GetMaxLevel() then
	-- 	if bizuo_info.exp >= level_info.need_exp then
	-- 		return 1
	-- 	end
	-- end

	if self:CheckIfFindData() then
		return 1
	end

	if TaskWGData.Instance:CheckRiChangRedPoint() then
		return 1
	end

	return 0
end

function BiZuoWGData:BizuoActivityHallRedShow()
	local data = self:GetTaskCfg()
	if not data or IsEmptyTable(data) then return 0 end
	local signup_cfg
	for k,v in pairs(data) do
		signup_cfg = self:GetSignupConfigByActType(v.act_type)
		if signup_cfg then
			if v.level_limit == 0 and v.is_can_signup == 1
				and v.is_husong_count_finish == 0 and not self:GetIsSignUpFlagBySeq(signup_cfg.seq) then
				return 1
			end
		else
			print_error("日常活动大厅取报名配置为空,活动类型:", v.act_type)
		end
	end
	return 0
end

--/cmd showremindinfo Remind_ActivityHall
function BiZuoWGData:PrintActivityHallRedShow()
	local data = self:GetTaskCfg()
	if not data or IsEmptyTable(data) then
		SysMsgWGCtrl.Instance:ErrorRemind("日常--活动大厅配置为空,红点状态: 0 ")
		return
	end
	local signup_cfg
	for k,v in pairs(data) do
		signup_cfg = self:GetSignupConfigByActType(v.act_type)
		if signup_cfg and v.level_limit == 0 and v.is_can_signup == 1
			and v.is_husong_count_finish == 0 and not self:GetIsSignUpFlagBySeq(signup_cfg.seq) then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format("日常--活动大厅有红点, 红点来源: %s", v.name))
			print_error("日常--活动大厅红点调试数据", v)
			return
		end
	end

	SysMsgWGCtrl.Instance:ErrorRemind("日常--活动大厅红点逻辑执行完毕,红点状态: 0 ")
	return
end

function BiZuoWGData:ZhaoHuiRedShow()
	local is_open = FunOpen.Instance:GetFunIsOpened("welfare_dailyfind")
	if not is_open then
		return 0
	end

	if self:GetZhaoHuiOpenFlag() then
		return 0
	end

	-- local dailyfind_list = WelfareWGData.Instance:GetDailyFindItems()
	local dailyfind_list = WelfareWGData.Instance:ResetDailyFindList(true)
	if not IsEmptyTable(dailyfind_list) then
		for k,v in pairs(dailyfind_list) do
			if v.times and v.vip_times and (v.times + v.vip_times) > 0 then
				return 1
			end
		end
	end
	return 0
end

function BiZuoWGData:GetZhaoHuiOpenFlag()
	return self.bz_zhaohui_open_flag
end

function BiZuoWGData:SetZhaoHuiOpenFlag()
	if not self.bz_zhaohui_open_flag then
		self.bz_zhaohui_open_flag = true
		RemindManager.Instance:Fire(RemindName.ZhaoHui)
	end
end

--服务端发来的协议  有些数据是空的 需要删除
function BiZuoWGData:SetCompleteTimesList(info)
	--先清空列表 再读入数据
	self.complete_times_list = {}
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}
	local work_list = daily_work_auto.work or {}
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local updata_time = math.max(server_time , 0) 	-- 0点刷新时间
	local w_day = tonumber(os.date("%w", updata_time))
	local open_day_flag = {}
	local data

	local daily_cfg = self.activity_hall_cfg
	daily_cfg = self:GetConfigByServerOpenDay(daily_cfg)
	daily_cfg = self:GetConfigByRoleLevel(daily_cfg)
	for k,v in pairs(work_list) do
		if v.parent_type == 2 then
			for k1, v1 in pairs(daily_cfg) do
				if v.act_type == v1.act_type then
					data = self:GetActivityHallCfg(v1)
					if data then
						data.index = v.type
						table.insert(self.complete_times_list, data)
					end
				end
			end
		elseif v.parent_type == 1 then
			local is_open_day = 0
			local open_day_list = Split(v.show_day, "|")
			for k2, v2 in pairs(open_day_list) do
				if tonumber(v2) == w_day then
					is_open_day = 1
					open_day_flag[v.type] = is_open_day
					break
				end
			end
		end
	end

	for k, v in pairs(info.complete_times_list) do
		for k1 ,v1 in pairs(open_day_flag) do
			if k1 == v.index and 1 == v1 then
				table.insert(self.complete_times_list, v)
				break
			end
		end
	end
end

function BiZuoWGData:GetCompleteTimesList()
	return self.complete_times_list
end

function BiZuoWGData:GetCompleteTimesData(activity_type)
	if self.activity_hall_info_is_dirty then
		self:SetActivityHallCfg()
		self.activity_hall_info_is_dirty = false
	end

	local data_list = {}
	for k, v in pairs(self.complete_times_list) do
		if not (v.close_level ~= 0 and v.close_level < RoleWGData.Instance.role_vo.level)
		and (activity_type == 0 or v.activity_type == activity_type) then --
			table.insert(data_list, v)
		end
	end

	for k, v in pairs(self.activity_hall_all_info) do
		if activity_type == 0 or v.dailywork_cfg.activity_type == activity_type then
			table.insert(data_list, v)
		end
	end

	return data_list
end

function BiZuoWGData:GetDailyAllData(activity_type)
	if self.activity_hall_info_is_dirty then
		self:SetActivityHallCfg()
		self.activity_hall_info_is_dirty = false
	end

	local data_list = {}
	for k, v in pairs(self.complete_times_list) do
		local is_open = FunOpen.Instance:GetCfgPathIsOpen(v.open_panel)
		if not (v.close_level ~= 0 and v.close_level < RoleWGData.Instance.role_vo.level)
		and (activity_type == 0 or v.activity_type == activity_type) and is_open then
			table.insert(data_list, v)
		end
	end

	return data_list
end

function BiZuoWGData:GetIsComplete(type)
	if type == BIZUO_TYPE.HU_SONG then
		local husong_free_times = YunbiaoWGData.Instance:GetTotalFreeHusonTimes()
		local husong_buytimes = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT) --已购买次数
		local husong_complete_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) --完成次数
		return husong_complete_times >= husong_free_times + husong_buytimes
	end
	if BIZUO_TYPE.QIFU == type then
		return  QiFuWGData.Instance:RemainQifuTime() <= 0
	end
	local complete_list = self:GetCompleteTimesList()
	local data = nil
	for k, v in pairs(complete_list) do
		if v.type == type then
			data = v
			break
		end
	end
	if data == nil then return true end
	return data.complete_times >= (data.complete_max_times - data.buy_times)
end
--- 判断是否可以显示找回功能
function BiZuoWGData:CheckIfFindData()
	-- if self.bizuo_info and next(self.bizuo_info) ~= nil then
	-- 	if self.bizuo_info.is_can_findback > 0 then
	-- 		if self.find_list and next(self.find_list) ~= nil then
	-- 			return true
	-- 		end
	-- 	end
	-- end
	return false
end

function BiZuoWGData:UpdateBiZuoInfo()
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local index_info = {}
	local complete_times_list = self:GetCompleteTimesList()
	local work_list = daily_work_auto.work or {}
	local role_lv = RoleWGData.Instance.role_vo.level
	for k = #complete_times_list, 1, -1 do
		local v = complete_times_list[k]

		v.not_complete = 1
		v.complete = 0
		v.type_sort = 1
		v.sort_layer = 0
		v.complete_max_times = 0
		v.buy_times = 0
		v.open_level = 0
		v.tips_icon = 0
		v.tips_icon_text = ""

		for _, value in ipairs(work_list) do
			if value.type == v.index then
				if value.parent_type == 1 then
					if RoleWGData.Instance.role_vo.level >= value.open_level then
						v.is_open = 1
					else
						v.is_open = 0
					end

					if value.close_level > 0 and RoleWGData.Instance.role_vo.level >= value.close_level then
						v.is_open = 0
					end

					if v.complete_times >= (value.complete_max_times - value.buy_times) then
						v.not_complete = 0
						v.complete = 1
					else
						v.not_complete = 1
						v.complete = 0
					end
					v.sort_layer = 999 - value.type			-- 仅排序用
					v.type = value.type

					v.type_sort = 1 -- 仅排序使用
					if v.type == 2 then
						if v.complete_times >= 0 then
							v.type_sort = 0
						end
					elseif v.type == 7 then
						if v.complete_times >= 10 then
							v.type_sort = 0
						end
					end
				end

				v.res_name = value.res_name
				v.open_level = value.open_level
				v.close_level = value.close_level
				v.complete_max_times = value.complete_max_times
				v.exp_per_times = value.exp_per_times
				v.name = value.name
				v.icon = value.icon
				v.buy_times = value.buy_times
				v.tips_icon = value.tips_icon
				v.sort = value.sort
				v.tips_icon_text = value.tips_icon_text
				v.xiuwei_per_times = value.xiuwei_per_times
				v.activity_type = value.activity_type
				v.parent_type = value.parent_type
				v.task_type = value.task_type
				v.open_panel = value.open_panel

				--排序顺序
				local sort2 = 10000
				if value.parent_type == 1 then
					local red_flag = v.type == BIZUO_TYPE.RI_CHANG and TaskWGData.Instance:CheckRiChangRedPoint()
					local is_finish = v.not_complete == 0 and not red_flag
					if is_finish then
						sort2 = 1000
					end

					local is_kaiqi = (v.not_complete ~= 0 and v.is_open == 1) or red_flag
					local level_limit_flag = not is_kaiqi and not is_finish
					if level_limit_flag then
						sort2 = 1
					end

					v.red_flag = red_flag
					v.is_finish = is_finish
					v.is_kaiqi = is_kaiqi
					v.level_limit_flag = level_limit_flag
				elseif value.parent_type == 2 then
					local is_participation = self:GetIsParticipationBySeq(v.act_seq)
					local is_finish = 1 == v.is_husong_count_finish or (0 == v.is_act_open_day and 1 == v.is_overdue and is_participation)
					if is_finish then
						sort2 = 1000
					end

					local is_end = 0 == v.is_act_open_day and 1 == v.is_overdue
					local show_end_flag = is_end and not is_participation
					if show_end_flag then
						sort2 = 100
					end

					local level_limit_flag = 1 == v.level_limit
					if level_limit_flag then
						sort2 = 1
					end

					local act_time_limit = not level_limit_flag and not is_finish and not (1 == v.is_open and 0 == v.is_act_open_day) and not is_end
					if act_time_limit then
						sort2 = 10
					end

					v.is_finish = is_finish
					v.is_end = is_end
					v.level_limit_flag = level_limit_flag
					v.act_time_limit = act_time_limit
				end
				v.sort2 = sort2
			end

			if v.type == BiZuoType.Type_11 then
				self.guild_old_status = v.not_complete
			end
		end
	end
	-- table.sort(complete_times_list, SortTools.KeyLowerSorter("sort","not_complete","is_open"))--
end

function BiZuoWGData:GetWeekGuildUpDataStatus()
	if self.guild_old_status then
		return self.guild_old_status
	else
		return 0
	end
end

function BiZuoWGData:SetFindList()
	local complete_times_list = self:GetCompleteTimesList()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	self.find_list = {}
	for k,v in ipairs(complete_times_list) do
		if v then
			if v.parent_type == 1 and v.yesterday_complete_times >= 0 and v.complete_max_times - v.buy_times > v.yesterday_complete_times and v.type ~= 11 and v.open_level <= role_level then -- 仙盟任务不能找回
				table.insert(self.find_list, v)
			end
		end
	end
end

function BiZuoWGData:GetIsCanFind()
	local can_find = false
	local other = BiZuoWGData:GetWorkOther()
	for i,v in ipairs(self.find_list) do
		local jinyan = (v.complete_max_times - v.buy_times - v.yesterday_complete_times) * v.exp_per_times
		local gold_per_exp = other[1].gold_per_exp or 0
		local need_yuanbao = math.ceil(gold_per_exp * jinyan / 100)
		if need_yuanbao <= RoleWGData.Instance.role_info.gold + RoleWGData.Instance.role_info.bind_gold then
			can_find = true
			return can_find and self:CheckIfFindData()
		end
	end
	return can_find and self:CheckIfFindData()
end

function BiZuoWGData:GetFindAllYuanBao()
	local yuanbao = 0
	local other = self:GetWorkOther()
	local gold_per_exp = other[1].gold_per_exp or 0
	for k,v in ipairs(self.find_list) do
		local temp_yuanbao = ((v.complete_max_times - v.yesterday_complete_times - v.buy_times) * v.exp_per_times) * gold_per_exp
		yuanbao = yuanbao + math.ceil(temp_yuanbao / 10000)
	end
	return yuanbao
end

function BiZuoWGData:GetFindList()
	return self.find_list
end

--检测是否有可以找回的
function BiZuoWGData:ChekIfCanFind()
	if self.find_list and next(self.find_list) ~= nil then
		return true
	end
	return false
end

function BiZuoWGData:GetAllInfo()
	return self.bizuo_info
end

function BiZuoWGData:GetWorkOther()
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	return daily_work_auto.other or {}
end

function BiZuoWGData:SetLevelInfo(info)
	self.bizuo_info.level = info.level
	self.bizuo_info.exp = info.exp
end

function BiZuoWGData:GetLevelCfgByLevel(level)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local cfg = daily_work_auto.level or {}
	local level_cfg = {}
	for k, v in ipairs(cfg) do
		if v.level == level then
			level_cfg = v
			break
		end
	end
	return level_cfg
end

function BiZuoWGData:GetMaxLevel()
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	return #(daily_work_auto.level or {}) - 1
end

function BiZuoWGData:GetOpenViewByIndex(index)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local cfg = daily_work_auto.work or {}
	for k, v in ipairs(cfg) do
		if v.type == index then
			local t = Split(v.open_panel, "#")
			local view_name = t[1]
			local tab_index = t[2]

			return view_name, tab_index, v.is_close_view_after_jump
		end
	end
end

-- 根据任务索引获得该任务完成情况
function BiZuoWGData:GetcompleteTimeByIndex(index)
	local time_list = self:GetCompleteTimesList()
	for i,v in ipairs(time_list) do  -- 该任务在开启天数
		if v.type == index then
			return v
		end
	end

	-- 该任务不在开启天数
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}
	local cfg = daily_work_auto.work or {}
	for i,v in ipairs(cfg) do
		if v.type == index then
			return v
		end
	end

	return {}
end

function BiZuoWGData:CanLevelUpRemind()
	local num = 0
	local level_info
	local bizuo_info = self:GetAllInfo()
	if bizuo_info then
		level_info = self:GetLevelCfgByLevel(bizuo_info.level)
	end
	if bizuo_info and level_info and bizuo_info.exp and level_info.need_exp and bizuo_info.level < self:GetMaxLevel() then
		if bizuo_info.exp >= level_info.need_exp then
			num = 1
		end
	end

	return num
end

function BiZuoWGData:LevelUpBtnIsVisible()
	local bizuo_info = self:GetAllInfo()

	if nil ~= next(bizuo_info) then
		if self:CanLevelUpRemind() > 0 and bizuo_info.level < self:GetMaxLevel() then
			return true
		end
	end
	return false
end

-- 获取幻化名字
function BiZuoWGData:GetBiZuoName(bizuo_type)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	for k,v in ipairs(bizuo_cfg) do
		if bizuo_type == v.type then
			return v.name
		end
	end
	return nil
end

-- 获取幻化特效
function BiZuoWGData:GetBiZuoEffect(bizuo_type)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	for k,v in ipairs(bizuo_cfg) do
		if bizuo_type == v.type then
			return v.res_id
		end
	end
	return nil
end

-- 获取幻化特效
function BiZuoWGData:GetBiZuoIcon(bizuo_type)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	for k,v in ipairs(bizuo_cfg) do
		if bizuo_type == v.type then
			return v.res_id
		end
	end
	return nil
end

-- 获取战骑最大类型
function BiZuoWGData:GetBiZuoMaxType()
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	return bizuo_cfg[#bizuo_cfg].type
end

-- 获取特效参数配置
function BiZuoWGData:GetEffectScaleValue(bizuo_type)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	for k,v in ipairs(bizuo_cfg) do
		if bizuo_type == v.type then
			return v
		end
	end
	return nil
end

function BiZuoWGData:GetHuanHuaLevel(bizuo_type)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	for k,v in ipairs(bizuo_cfg) do
		if bizuo_type == v.type then
			if bizuo_type == 0 then
				return v.level + 1
			else
				return v.level
			end
		end
	end

	return 1
end

function BiZuoWGData:GetBiZuoTypeByLevel(level)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	for k,v in ipairs(bizuo_cfg) do
		if level == v.level then
			return v.type
		end
	end
	return nil
end

function BiZuoWGData:GetBiZuoNameByName(level)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	local bizuo_cfg = daily_work_auto.level
	for k,v in ipairs(bizuo_cfg) do
		if level == v.level then
			return v.name
		end
	end
	return nil
end



function BiZuoWGData:GetBiZuoBuyTimes()
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}

	return daily_work_auto.work.buy_times
end

-- 获取最大的战骑路劲
function BiZuoWGData:GetMaxEffectPath()
	for i=0,7 do
		local hua_lv = self:GetHuanHuaLevel(i)
		if hua_lv > self.bizuo_info.level then
			local cur_lv = i - 1
			cur_lv = cur_lv > 0 and cur_lv or 0
			return self:GetBiZuoEffect(cur_lv)
		end
	end
end

function BiZuoWGData:GetShowMaxLevel()
	for i=1,7 do
		local hua_lv = self:GetHuanHuaLevel(i)
		if hua_lv > self.bizuo_info.level then
			local cur_lv = i - 1
			cur_lv = cur_lv > 0 and cur_lv or 0
			return cur_lv
		end
	end
	return -1
end


-- 获取活动时间表
function BiZuoWGData:GetWeekCalendarItemData(need_flush)
	if need_flush or not self.bizuo_week_cfg_list then
		local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto").week or {}
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local kf_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
		local open_day = TimeWGCtrl.Instance:GetCrossActivityOpenDay()
		local daily_work_list = {}
		local level = nil
		local cfg_list = BiZuoWGData.Instance:GetConfigByServerOpenDay(daily_work_auto)
		if not cfg_list or not next(cfg_list) then
			return
		end

		for i,v in ipairs(cfg_list) do
			if (nil == level or v.level == level) and role_level <= v.level and v.cross_gs_level and v.cross_gs_level <= kf_world_level and open_day >= v.day_open then
				level = v.level
				table.insert(daily_work_list, v)
			end
		end

		self.bizuo_week_cfg_list = daily_work_list
	end
	return self.bizuo_week_cfg_list
end

function BiZuoWGData:GetConfigByServerOpenDay(cfg)
	local cfg_list = {}
	if IsEmptyTable(cfg) then
		return cfg_list
	end

	local cur_openserver_day = TimeWGCtrl.Instance:GetCrossActivityOpenDay()
	local temp_day = nil
	local open_day

	for i,v in ipairs(cfg) do
		if v.server_open_day or v.open_day or v.opengame_day then
			open_day = v.server_open_day and v.server_open_day or v.open_day or v.opengame_day
			open_day = tonumber(open_day)
			if (not temp_day or temp_day == open_day) and cur_openserver_day <= open_day then
				temp_day = open_day
				table.insert(cfg_list,v)
			end
		end
	end

	return cfg_list
end

function BiZuoWGData:GetConfigByRoleLevel(cfg)
	local cfg_list = {}
	if IsEmptyTable(cfg) then
		return cfg_list
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cur_openserver_day = TimeWGCtrl.Instance:GetCrossActivityOpenDay()
	local kf_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
	local level_condition

	local temp_level = nil
	for k,v in pairs(cfg) do
		if v.level_part or v.level_max then
			level_condition = v.level_part and v.level_part or v.level_max or 0
			if (nil == temp_level or level_condition == temp_level) and role_level <= level_condition then
				temp_level = level_condition

				local act_cfg = DailyWGData.Instance:GetActivityConfig(v.act_type)
				if act_cfg and cur_openserver_day >= act_cfg.act_serveropen and cur_openserver_day <= act_cfg.act_serverover_day then
					if role_level <= act_cfg.level_max and v.cross_gs_level and v.cross_gs_level <= kf_world_level then
						table.insert(cfg_list, v)
					end
				end
			end
		end
	end

	return cfg_list
end

function BiZuoWGData:GetActivityIsToday(str, act_type, info)
	local is_open = 1
	if not string then
		return is_open
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local week_day = TimeUtil.FormatSecond3MYHM1(server_time)
	local cur_openday = TimeWGCtrl.Instance:GetCrossActivityOpenDay()
	local list = Split(str,":")
	for k,v in pairs(list) do
		if tonumber(v) == week_day then
			is_open = 0
			break
		end
	end

	local open_day = info.server_open_day and info.server_open_day or info.open_day or info.opengame_day
	for k,v in pairs(self.daily_work_auto) do
		if cur_openday <= 3 and v.open_day <= open_day and v.replace_name ~= "" then
			if self.activity_hall_map_cfg[act_type].name == v["name"..week_day] then
				is_open = 1
			end
		end
	end

	local openserverday_limit = info.openserverday_limit
	if openserverday_limit and openserverday_limit > 0 and openserverday_limit <= 3 and cur_openday == openserverday_limit then
		is_open = 0
	end

	return is_open
end

function BiZuoWGData:NextDaySetActivityHallCfg()
	self:SetActivityHallCfg()
	BiZuoWGCtrl.Instance:FlushActHall()
	self:ClearHasSignUpFlagByEnter()
end

function BiZuoWGData:RoleLevelChange(attr_name, value)
	if attr_name == "level" then
		self.activity_hall_info_is_dirty = true
		self:CancelDelayFlushTimer()
		self.delay_flush_timer = GlobalTimerQuest:AddDelayTimer(function ()
			if self.activity_hall_info_is_dirty then
				self.activity_hall_info_is_dirty = false
				self:SetActivityHallCfg()
				self:SetAllInfo(self.bizuo_info)
			end
			--RemindManager.Instance:Fire(RemindName.ActivityHall)
		end, 3)
	end
end

function BiZuoWGData:CancelDelayFlushTimer()
	if self.delay_flush_timer then
		GlobalTimerQuest:CancelQuest(self.delay_flush_timer)
		self.delay_flush_timer = nil
	end
end

--策划说前三天只显示开启的活动
function BiZuoWGData:GetSpecailDayActivity(act_type, open_day)
	local day_list = Split(open_day, ":")
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local w_day = TimeUtil.FormatSecond3MYHM1(server_time)
	for i,v in ipairs(day_list) do
		local day = tonumber(v)
		if day == w_day then
			return true
		end
	end

	return false
end

-- 活动大厅
-- 护送活动号
local HuSongActType = 3
function BiZuoWGData:SetActivityHallCfg()
	local daily_cfg = self.activity_hall_cfg
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local cur_openserver_day = TimeWGCtrl.Instance:GetCrossActivityOpenDay()
	local curr_server_time = server_time
	local timer = os.date("%X", curr_server_time)

	local task_cfg = {}
	daily_cfg = self:GetConfigByServerOpenDay(daily_cfg)
	daily_cfg = self:GetConfigByRoleLevel(daily_cfg)
	--print_error(w_day, cur_openserver_day)
	for k, cfg_v in pairs(daily_cfg) do
		-- 特殊活动一天开多次，第一次结束返回第二次
		local can_show_flag = true
		local is_show = self:GetSpecailDayActivity(cfg_v.act_type, cfg_v.open_day)
		local specail_act_cfg = self:GetSpecailIsOverdue(cfg_v.act_type)
		if specail_act_cfg then
			can_show_flag = specail_act_cfg.act_seq == cfg_v.act_seq
		end

		--print_error("2", cfg_v.name, is_show)
		--print_error(1 == cfg_v.is_show_in_daily, can_show_flag, cur_openserver_day >= cfg_v.openserverday_limit, cfg_v.act_type == 3076, cur_openserver_day == 3)
		local openserverday_limit = cfg_v.openserverday_limit
		if 1 == cfg_v.is_show_in_daily and can_show_flag and ((cur_openserver_day >= openserverday_limit)) and is_show then
			local v = {}
			setmetatable(v, {__index = cfg_v}) -- 容错原tablecopy，不可模仿
			-- 是否在开启的天数里 (排序的原因1表示不在开启天数里)
			v.is_act_open_day = self:GetActivityIsToday(cfg_v.activity_open_day, v.act_type, v)
			v.cur_openserver_day = cur_openserver_day
			-- 是否时间过期了
			v.is_overdue = 0
			-- 根据开启时间来算主要用于预告活动里去

			if timer >= v.close_time then
				v.is_overdue = 1
			end

			v.day = 0
			if openserverday_limit > 0 and openserverday_limit <= 3 and cur_openserver_day == openserverday_limit then
				v.day = openserverday_limit
			end

			v.is_open = 0    -- 是否开启中
			if timer >= v.open_time and timer <= v.close_time and 0 == v.is_act_open_day then
				v.is_open = 1
			end

			--是否等级不足
			v.level_limit = GameVoManager.Instance:GetMainRoleVo().level < (v.level) and 1 or 0

			-- 图标排序考虑是否正在开启中
			v.is_in_open = 0
			if ActivityWGData.Instance:GetActivityIsOpen(v.act_type) then
				v.is_in_open = 1
			end

			v.is_husong_count_finish = 0
			if v.act_type == HuSongActType then
				if YunbiaoWGData.Instance:GetHusongRemainTimes() <= 0 then
					v.is_husong_count_finish = 1
				end
			end

			local dailywork_cfg = BiZuoWGData.Instance:GetWorkConfigByActType(v.act_type)
			v.dailywork_cfg = dailywork_cfg
			v.is_activity_hall = true
			v.sort = dailywork_cfg.sort

			-- 在开放天数内 并且未开启
			v.is_can_signup = 0   		--是否处于可报名阶段
			if v.is_act_open_day == 0 and timer < v.open_time then
				if v.signup_begin_time and v.signup_end_time and timer >= v.signup_begin_time and timer < v.signup_end_time then
					v.is_can_signup = 1
				end
			end

			table.insert(task_cfg, v)
		end
	end

	table.sort(task_cfg, SortTools.KeyLowerSorters("is_act_open_day","level_limit", "is_husong_count_finish", "is_overdue", "open_time"))
	self.task_cfg = task_cfg
	self.activity_hall_all_info = task_cfg

	self:DelayFlushActivityHallRed()
end

function BiZuoWGData:GetActivityHallCfg(cfg_v)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local cur_openserver_day = TimeWGCtrl.Instance:GetCrossActivityOpenDay()
	local curr_server_time = server_time
	local timer = os.date("%X", curr_server_time)

	local can_show_flag = true
	local is_show = self:GetSpecailDayActivity(cfg_v.act_type, cfg_v.open_day)
	local specail_act_cfg = self:GetSpecailIsOverdue(cfg_v.act_type)
	if specail_act_cfg then
		can_show_flag = specail_act_cfg.act_seq == cfg_v.act_seq
	end

	--print_error("2", cfg_v.name, is_show)
	--print_error(1 == cfg_v.is_show_in_daily, can_show_flag, cur_openserver_day >= cfg_v.openserverday_limit, cfg_v.act_type == 3076, cur_openserver_day == 3)
	local openserverday_limit = cfg_v.openserverday_limit
	if 1 == cfg_v.is_show_in_daily and can_show_flag and ((cur_openserver_day >= openserverday_limit)) and is_show then
		local v = {}
		setmetatable(v, {__index = cfg_v}) -- 容错原tablecopy，不可模仿
		-- 是否在开启的天数里 (排序的原因1表示不在开启天数里)
		v.is_act_open_day = self:GetActivityIsToday(cfg_v.activity_open_day, v.act_type, v)
		v.cur_openserver_day = cur_openserver_day
		-- 是否时间过期了
		v.is_overdue = 0
		-- 根据开启时间来算主要用于预告活动里去

		if timer >= v.close_time then
			v.is_overdue = 1
		end

		v.day = 0
		if openserverday_limit > 0 and openserverday_limit <= 3 and cur_openserver_day == openserverday_limit then
			v.day = openserverday_limit
		end

		v.is_open = 0    -- 是否开启中
		if timer >= v.open_time and timer <= v.close_time and 0 == v.is_act_open_day then
			v.is_open = 1
		end

		--是否等级不足
		v.level_limit = GameVoManager.Instance:GetMainRoleVo().level < (v.level) and 1 or 0

		-- 图标排序考虑是否正在开启中
		v.is_in_open = 0
		if ActivityWGData.Instance:GetActivityIsOpen(v.act_type) then
			v.is_in_open = 1
		end

		v.is_husong_count_finish = 0
		if v.act_type == HuSongActType then
			if YunbiaoWGData.Instance:GetHusongRemainTimes() <= 0 then
				v.is_husong_count_finish = 1
			end
		end

		-- 在开放天数内 并且未开启
		v.is_can_signup = 0   		--是否处于可报名阶段
		if v.is_act_open_day == 0 and timer < v.open_time then
			if v.signup_begin_time and v.signup_end_time and timer >= v.signup_begin_time and timer < v.signup_end_time then
				v.is_can_signup = 1
			end
		end

		return v
	end

	return nil
end

--延时刷新活动大厅红点,防止上线等频繁刷新
function BiZuoWGData:DelayFlushActivityHallRed()
	self:CancelDelayFlushActivityHallRed()
	self.delay_flush_acthall_red_timer = GlobalTimerQuest:AddDelayTimer(function ()
		--RemindManager.Instance:Fire(RemindName.ActivityHall)
	end, 1)
end

function BiZuoWGData:CancelDelayFlushActivityHallRed()
	if self.delay_flush_acthall_red_timer then
		GlobalTimerQuest:CancelQuest(self.delay_flush_acthall_red_timer)
		self.delay_flush_acthall_red_timer = nil
	end
end

function BiZuoWGData:GetActHallCfgByTypeAndOpenDay(act_type)
	local temp_daily_cfg = self:GetConfigByServerOpenDay(self.activity_hall_cfg)
	local daily_cfg = self:GetConfigByRoleLevel(temp_daily_cfg)
	local temp_list = {}

	if not IsEmptyTable(daily_cfg) then
		for i,v in ipairs(daily_cfg) do
			if act_type == v.act_type then
				temp_list[#temp_list + 1] = v
			end
		end
	end

	return temp_list
end

-- 一天开多次的特殊活动，第一次结束返回第二次
function BiZuoWGData:GetSpecailIsOverdue(act_type)
	if SPECAIL_MANY_TIMES_ACT[act_type] then
		local curr_server_time = TimeWGCtrl.Instance:GetServerTime()
		local timer = os.date("%X", curr_server_time)
		local cfg_list = self:GetActHallCfgByTypeAndOpenDay(act_type)

		if #cfg_list > 1 then
			if timer >= cfg_list[1].close_time or (timer >= cfg_list[1].open_time) then
				return cfg_list[2]
			else
				return cfg_list[1]
			end
		end
	end

	return
end

function BiZuoWGData:GetTaskCfg()
	if self.activity_hall_info_is_dirty then
		self:SetActivityHallCfg()
		self.activity_hall_info_is_dirty = false
	end

	return self.task_cfg
end

function BiZuoWGData:GetActData(act_id)
	self:GetActivityHallAllInfo()
	if not self.activity_hall_all_info or not act_id then
		return nil
	end

	for k,v in pairs(self.activity_hall_all_info) do
		if v.act_type == act_id then
			return v
		end
	end

	return nil
end

function BiZuoWGData:GetActivityHallAllInfo()
	if self.activity_hall_info_is_dirty then
		self:SetActivityHallCfg()
		self.activity_hall_info_is_dirty = false
	end

	return self.activity_hall_all_info
end

-- 获得活动开启描述
function BiZuoWGData:GetActOpenDesc(act_cfg)
	if not act_cfg then
		return ""
	end
	local open_server_day = TimeWGCtrl.Instance:GetCrossActivityOpenDay()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local open_day_des,_,_,is_every_day = self:GetActOpenTimeStr(act_cfg.act_type)
	open_day_des = act_cfg.open_tips
	-- if open_day_des == "" then
	-- 	open_day_des = act_cfg.open_tips
	-- else
	-- 	if is_every_day then
	-- 		open_day_des = open_day_des
	-- 	else
	-- 		open_day_des = string.format(Language.BiZuo.WeekStr,open_day_des)
	-- 	end

	-- end

	-- if act_cfg.act_type == 7 then
	-- 	if open_server_day <= 3 then
	-- 		local time = server_time + ((3 - open_server_day)*24*60*60)
	-- 		local week_day = TimeUtil.FormatSecond3MYHM1(time)
	-- 		if week_day ~= 7 then
	-- 			open_day_des = CommonDataManager.GetWeekTextByDay(tonumber(week_day)).. "、".. act_cfg.open_tips
	-- 		else
	-- 			open_day_des = act_cfg.open_tips
	-- 		end
	-- 	else
	-- 		open_day_des = act_cfg.open_tips
	-- 	end
	-- else
 -- 		open_day_des = act_cfg.open_tips
 -- 	end

 	local time_desc_day_1 = open_day_des
 	local time_desc_1 = string.format(Language.BiZuo.Act_Time_Segment_4, act_cfg.open_time, act_cfg.close_time)
	local time_desc_day_2 = nil
 	local time_desc_2 = nil
 	-- 判断是否有两个时间段
 	if act_cfg.open_time_2 ~= "" and act_cfg.close_time_2 ~= "" then
	 	local open_time_tab = Split(act_cfg.open_time_2, "|")
		local close_time_tab = Split(act_cfg.close_time_2, "|")
 		local open_time_1 = open_time_tab[1]
 		local close_time_1 = close_time_tab[1]
 		local open_time_2 = open_time_tab[2]
 		local close_time_2 = close_time_tab[2]
		time_desc_day_1 = open_day_des
 		time_desc_1 = string.format(Language.BiZuo.Act_Time_Segment_4, open_time_1, close_time_1)
		time_desc_day_2 = open_day_des
 		time_desc_2 = string.format(Language.BiZuo.Act_Time_Segment_4, open_time_2, close_time_2)
 	end

	return time_desc_day_1, time_desc_1, time_desc_day_2, time_desc_2
end

-- 获得活动开启描述(返回的周几和时间格式为换行)
function BiZuoWGData:GetActOpenDescNew(act_cfg)
	if not act_cfg then
		return ""
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local open_day_des,_,_,is_every_day = self:GetActOpenTimeStr(act_cfg.act_type)
	if open_day_des == "" then
		open_day_des = act_cfg.open_tips
	else
		if is_every_day then
			open_day_des = open_day_des
		else
			open_day_des = string.format(Language.BiZuo.WeekStr,open_day_des)
		end

	end

 	local time_desc_1 = string.format(Language.BiZuo.Act_Time_Segment_3, open_day_des, act_cfg.open_time, act_cfg.close_time)
 	local time_desc_2 = nil
 	-- 判断是否有两个时间段
 	if act_cfg.open_time_2 ~= "" and act_cfg.close_time_2 ~= "" then
	 	local open_time_tab = Split(act_cfg.open_time_2, "|")
		local close_time_tab = Split(act_cfg.close_time_2, "|")
 		local open_time_1 = open_time_tab[1]
 		local close_time_1 = close_time_tab[1]
 		local open_time_2 = open_time_tab[2]
 		local close_time_2 = close_time_tab[2]
 		time_desc_1 = string.format(Language.BiZuo.Act_Time_Segment_3, open_day_des, open_time_1, close_time_1)
 		time_desc_2 = string.format(Language.BiZuo.Act_Time_Segment_4, open_time_2, close_time_2)
 	end

	return time_desc_1, time_desc_2
end

--根据活动类型拿活动报名配置
function BiZuoWGData:GetSignupConfigByActType(act_type)
	local signup_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").signup
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local timer = os.date("%X",server_time)
	local timer_list = Split(timer,":")
	local signup_time = (timer_list[1]*100 + timer_list[2])+timer_list[3]/100
	if not signup_cfg then return end
	local temp_list = {}
	for k,v in ipairs(signup_cfg) do
		if act_type == v.act_type then
			table.insert(temp_list,v)
		end
	end
	--同一个活动可能一天开两次,如果第一个报名时间未结束,返回第一个,否则返回第二个
	if #temp_list > 1 then
		if signup_time > temp_list[1].signup_end_hhmm then
			return temp_list[2]
		end
		return temp_list[1]
	end
	return temp_list[1]
end

--根据活动索引拿活动报名配置
function BiZuoWGData:GetSignupConfigByActSeq(act_seq)
	local signup_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").signup
	if not signup_cfg then return end
	for k,v in ipairs(signup_cfg) do
		if act_seq == v.seq then
			return v
		end
	end
end

--根据活动类型拿活动大厅配置
function BiZuoWGData:GetActivityHallCfgByActType(act_type)
	local data_lsit = self:GetTaskCfg()
	local temp_list = {}
	for k,v in pairs(data_lsit) do
		if v.act_type == act_type then
			table.insert(temp_list,v)
		end
	end

	--同一个活动可能一天开两次,如果第一个未结束,返回第一个,否则返回第二个
	if #temp_list > 1 then
		if temp_list[1].is_overdue == 0 then
			return temp_list[1]
		else
			return temp_list[2]
		end
	end
	return temp_list[1]
end

function BiZuoWGData:GetActivityHallCfgTwo()
	local daily_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").activity_hall
	local daily_work_list = {}
	local level_part = nil
	for k,v in ipairs(daily_cfg) do
		if (nil == level_part or v.level_part == level_part) and RoleWGData.Instance:GetRoleLevel() <= v.level_part then
			level_part = v.level_part
			table.insert(daily_work_list, v)
		end
	end
	return daily_work_list
end

function BiZuoWGData:GetNoOpenActivityHallCfg()
	local daily_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").activity_hall
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local updata_time = math.max(server_time - 6 * 3600, 0)
	local w_day = tonumber(os.date("%w", updata_time))
	if 0 == w_day then w_day = 7 end
	if w_day == self.w_day then
		if RoleWGData.Instance:GetRoleLevel() <= self.level_part then
			return self.daily_work_list
		end
	end

	local daily_work_list = {}
	local level_part = nil
	for k,v in ipairs(daily_cfg) do
		if (nil == level_part or v.level_part == level_part) and RoleWGData.Instance:GetRoleLevel() <= v.level_part then
			level_part = v.level_part
			local is_open_day = 1
			if v.open_day ~= 0 then
				local open_day_list = Split(v.open_day, ":")
				for k2, v2 in pairs(open_day_list) do
					if tonumber(v2) == w_day then
						is_open_day = 0
						break
					end
				end
			else
				is_open_day = 1
			end
			if is_open_day == 1 then
				daily_work_list[v.act_type] = true
			end
		end
	end

	self.w_day = w_day
	self.level_part = level_part
	self.daily_work_list = daily_work_list
	return self.daily_work_list
end

function BiZuoWGData:IsShieldByActType(activity_type)
	if activity_type == nil then
		return false
	end

	local list = BiZuoWGData.Instance:GetNoOpenActivityHallCfg()
	-- 活动大厅周历里面有的才更新
	--先屏蔽方便测试
	if list[activity_type] then
		return false
	else
		return false
	end
	-- for k,v in ipairs(cfg) do
	-- 	if v.act_type == activity_type then
	-- 		return true
	-- 	end
	-- end
	-- return false
end

--该类型的活动不显示 准备 开启期间的传闻
local ShieldChuanWenActType = {
	[36] = true,
}
function BiZuoWGData:IsShieldChuanWenByActType(activity_type)
	return ShieldChuanWenActType[activity_type] or false
end

function BiZuoWGData:GetHuoYueAwardCfg()
	local role_level = RoleWGData.Instance.role_vo.level
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto").award or {}
	local award_cfg = {}
	local reward_list = {}
	for k,v in pairs(daily_work_auto) do
		if role_level >= v.grade_rise then
			award_cfg = v
			return award_cfg
		end
	end
	return award_cfg
end

function BiZuoWGData:GetHuoYueRewardListAndValue()
	local role_level = RoleWGData.Instance.role_vo.level
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto").award or {}
	local reward_list = {}
	local award_cfg = {}
	local exp_list = {}
	for k,v in pairs(daily_work_auto) do
		if role_level >= v.grade_rise then
			award_cfg = v
		end
	end
	for i=1,HUOYUEDU_REWARD do
		if award_cfg["reward_item_"..i][0] or next(award_cfg["reward_item_"..i][0]) then
			table.insert(reward_list,award_cfg["reward_item_"..i][0])
		end
		if award_cfg["exp_condition_"..i] then
			table.insert(exp_list,award_cfg["exp_condition_"..i])
		end
	end
	return reward_list , exp_list
end

function BiZuoWGData:CanShowTips(index)
	local total_exp = self:GetTotalExp()
	local _,exp_list = BiZuoWGData.Instance:GetHuoYueRewardListAndValue()
	local is_can_show = true
	if self:GetIaGetAwrdFlag(index - 1) then
		is_can_show = true
	else
		if total_exp ~= 0 and exp_list[index] and exp_list[index] <= total_exp then
			is_can_show = false
		else
			is_can_show = true
		end
	end
	return is_can_show
end

function BiZuoWGData:GetBiZuoShowTipData(type)
	local cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").work_show
	for i, v in pairs(cfg) do
		if v.type == type then
			return v
		end
	end
	return nil
end

function BiZuoWGData:GetActForecastData()
	local task_cfg = self:GetTaskCfg()
	local act_forecast_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").prevue
	local data = {}
	if not act_forecast_cfg then return data end
	for k,v in pairs(task_cfg) do
		if v.level_limit == 0 and v.is_act_open_day == 0 then
			for k1,v1 in pairs(act_forecast_cfg) do
				if v.act_type == v1.act_type then
					table.insert(data,v1)
				end
			end
		end
	end
	return data
end

function BiZuoWGData:GetActForecastDataByActType(act_type)
	local data = self:GetActForecastData()
	if not data or IsEmptyTable(data) then return end

	for k,v in pairs(data) do
		if act_type == v.act_type then
			return v
		end
	end
end

function BiZuoWGData:GetWorkConfigByActType(act_type)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}
	for k,v in pairs(daily_work_auto.work) do
		if act_type == v.act_type then
			return v
		end
	end
end

function BiZuoWGData:IsInForecastPeriod(act_type)
	local data = self:GetActForecastDataByActType(act_type)
	if not data or IsEmptyTable(data) then return false end
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local timer = os.date("%X",server_time)

	if timer >= data.begin_time and timer < data.end_time then
		return true
	end
	return false
end

--获取任务大厅的任务配置不考虑是否开启
function BiZuoWGData:GetAllActivityHallCfg(act_type)
	if self.activity_hall_map_cfg[act_type] then
		return self.activity_hall_map_cfg[act_type]
	end
end

function BiZuoWGData:GetAllActivityHallCfgByActType(act_type)
	local daily_cfg = self.activity_hall_cfg
	daily_cfg = self:GetConfigByServerOpenDay(daily_cfg)
	daily_cfg = self:GetConfigByRoleLevel(daily_cfg)

	for k,v in ipairs(daily_cfg) do
		if v.act_type == act_type then
			return v
		end
	end

	return nil
end

function BiZuoWGData:GetOffLineGuaJiInfo()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if self.offline_exp_cfg and self.offline_exp_cfg[role_level] then
		return self.offline_exp_cfg[role_level]
	end
	return nil
end

--模型id
function BiZuoWGData:GetModelId()
	local other = BiZuoWGData:GetWorkOther()
	return other[1].model_id or 0, other[1].scale
end

--点击周历获取活动配置描述(根据名字获取)
function BiZuoWGData:GetWeekCalenderShowTipData(activity_name)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}
	local cur_openday = math.min(TimeWGCtrl.Instance:GetCrossActivityOpenDay(), 9999)
	if not activity_name or not daily_work_auto then
		return nil
	end

	for i,v in ipairs(daily_work_auto.work_show) do
		if activity_name == v.name then
			return v
		end
	end

	for i,v in ipairs(daily_work_auto.activity_hall) do
		if activity_name == v.name and cur_openday <= v.server_open_day then
			return v
		end
	end
	return nil
end

function BiZuoWGData:SortReward(reward_list)  -- 将奖励排序
	if IsEmptyTable(reward_list) then
		return {}
	end

	local list = {}
	for k, v in pairs(reward_list) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if not IsEmptyTable(item_cfg) then
			local key = #list + 1
			list[key] = v
			list[key].sort = item_cfg.color * 10 + k
		end
	end

	if not IsEmptyTable(list) then
		SortTools.SortDesc(list, "sort")
	end

	return list
end

-------------------跨服进度相关---------------------------start
function BiZuoWGData:GetCrossActivityOpenTimeCfg(act_type)
	return self.cross_activity_open_time_cfg[act_type]
end

--获取跨服活动配置的开启时间
--例如 一、三、五   21:00  22:00
function BiZuoWGData:GetActOpenTimeStr(act_type)
	local open_act_cfg = BiZuoWGData.Instance:GetCrossActivityOpenTimeCfg(act_type)
	local open_weekday_str = ""
	if open_act_cfg then
		open_weekday_str = open_act_cfg.open_weekday
	end

	local open_weekday_list = Split(open_weekday_str, ",")
	local open_day_list = open_weekday_list
	local zhou_str = ""
	local is_every_day = false
	if #open_day_list >= 7 then
		zhou_str = Language.BiZuo.Everyday
		is_every_day = true
	else
		for k,v in pairs(open_day_list) do
			local day = tonumber(v) == 0 and 7 or tonumber(v)
			local day_str = Language.Common.WeekDay[day]
			if k == #open_day_list then
				zhou_str = zhou_str .. day_str
			else
				zhou_str = zhou_str .. day_str .. "、"
			end
		end
	end

	local open_act_cfg1 = self.cross_activity_open_time1_cfg and self.cross_activity_open_time1_cfg[act_type]
	open_act_cfg1 = open_act_cfg1 and open_act_cfg1[1]
	local activity_start_time = open_act_cfg1 and open_act_cfg1.activity_start_time or 0
	local activity_end_time = open_act_cfg1 and open_act_cfg1.activity_end_time or 0
	local start_two_time = math.floor(activity_start_time%100) == 0 and "00" or math.floor(activity_start_time%100)
	if tonumber(start_two_time) > 0 and tonumber(start_two_time) < 10 then
		start_two_time = "0" .. start_two_time
	end

	local start_time = math.floor(activity_start_time/100) .. ":" .. start_two_time
	local end_two_time = math.floor(activity_end_time%100) == 0 and "00" or math.floor(activity_end_time%100)
	local end_time = math.floor(activity_end_time/100) .. ":" .. end_two_time
	return zhou_str,start_time,end_time,is_every_day
end

--把名字传过来，把xxx_s190 改成 [s190]xxx
function BiZuoWGData:GetSetverNameFormat(name)
    if name == nil then
        return ""
    end

	local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
    local name_list = Split(name, "_")
    local format_name = name
	if not IsEmptyTable(name_list) then
		if not is_cross_server_stage then
			format_name = name_list[1]
		else
			if name_list[2] then
				local has_s = string.find(name_list[2],"s")
				if has_s then
					format_name = string.format(Language.BiZuo.ServerName_2,name_list[2],name_list[1])
				else
					format_name = string.format(Language.BiZuo.ServerName_1,name_list[2],name_list[1])
				end
			else
				format_name = name_list[1]
			end
		end
	end

    return format_name
end

function BiZuoWGData:GetBiZuoActShowTipData(activity_type, corss_count)
	local daily_work_auto = ConfigManager.Instance:GetAutoConfig("dailywork_auto") or {}
	--特殊处理:有些活动开启所需跨服数 大于 当前跨服数,防止在活动大厅取不到配置
	local cur_openday = math.min(TimeWGCtrl.Instance:GetCrossActivityOpenDay(), 9999)
	if not activity_type or not daily_work_auto then
		return nil
	end

	for i,v in ipairs(daily_work_auto.activity_hall) do
		if activity_type == v.act_type and cur_openday <= v.server_open_day then
			return v
		end
	end
	return nil
end
-------------------跨服进度相关---------------------------end

-------------------我要变强相关---------------------------start
-- 我要变强配置
function BiZuoWGData:GetStrongerCfg()
	return self.get_stronger_cfg
end

-- 根据变强类型获得我要变强配置
function BiZuoWGData:GetStrongerCfgByType(type)
	return self:GetStrongerCfg()[type]
end


function BiZuoWGData:GetStrongerDataByCfg(get_stronger_cfg)
	local data = {}
	data.cfg = get_stronger_cfg
	data.open_panel = ""
	data.open_desc = ""
	-- 系统
	if get_stronger_cfg.subtype == BiZuoWGData.GetStrongerSubtype.Module then
		local fun_open_cfg = FunOpen.Instance:GetFunByName(get_stronger_cfg.module_name)
		if fun_open_cfg then
			-- 面板打开配置
			local open_str = fun_open_cfg.open_param
			if open_str and open_str ~= "" then
				local str_list = Split(open_str, '#')
				if str_list[2] then
					data.open_panel = str_list[2] .. '#' .. str_list[1]
				else
					data.open_panel = str_list[1]
				end
			end

			-- 未开启描述
			local is_open, reason = FunOpen.Instance:GetFunIsOpened(fun_open_cfg.name, true)
			data.open_desc = reason

			-- 图标
			local bundle, asset = ResPath.GetF2MainUIImage("act_" .. get_stronger_cfg.module_icon)
			data.icon_bundle = bundle
			data.icon_name = asset
		else
			print_error("未拿到功能开启配置，请检查M-每日必做-我要变强里面的module_name：", get_stronger_cfg.module_name)
		end
	-- 活动
	elseif get_stronger_cfg.subtype == BiZuoWGData.GetStrongerSubtype.Act then
		local act_cfg = CalendarWGData.Instance:GetHallCfgByActType(get_stronger_cfg.act_type)
		if act_cfg then
			-- 面板打开配置
			data.open_panel = act_cfg.open_panel_name
			-- 未开启描述
			local open_time_1, open_time_2 = BiZuoWGData.Instance:GetActOpenDescNew(act_cfg)
			if open_time_2 then
				data.open_desc = open_time_1 .. "\n" .. open_time_2
			else
				data.open_desc = open_time_1
			end
		else
			print_error("未拿到活动大厅对应活动配置，活动id：", get_stronger_cfg.act_type)
		end

		-- 图标
		local bundle, asset = ResPath.GetF2MainUIImage("act_" .. get_stronger_cfg.module_icon)
		data.icon_bundle = bundle
		data.icon_name = asset
	end
	return data
end

-- 判断我要变强的系统或活动是否开启
function BiZuoWGData:GetStrongerIsOpen(get_stronger_cfg)
	if get_stronger_cfg.subtype == BiZuoWGData.GetStrongerSubtype.Module then
		return FunOpen.Instance:GetFunIsOpened(get_stronger_cfg.module_name)
	elseif get_stronger_cfg.subtype == BiZuoWGData.GetStrongerSubtype.Act then
		return ActivityWGData.Instance:GetActivityIsOpen(get_stronger_cfg.act_type)
	end
	return false
end
-------------------我要变强相关---------------------------end

function BiZuoWGData:GetActForFuBenByFunName(type)
	local complete_times_list = self:GetCompleteTimesList()
	local max_huoyuedu = 0
	local has_huoyuedu = 0
	for k,v in ipairs(complete_times_list) do
		if v.type == type then
			max_huoyuedu = v.exp_per_times * (v.complete_max_times - v.buy_times)
			has_huoyuedu = max_huoyuedu > 0 and v.exp_per_times * v.complete_times or 0
			return max_huoyuedu > 0 and string.format(Language.FuBenPanel.HYD, COLOR3B.BLUE_TITLE, has_huoyuedu, max_huoyuedu) or ""
		end
	end

	return ""
end

function BiZuoWGData:GetActNumForFuBenByFunName(type)
	local complete_times_list = self:GetCompleteTimesList()
	local max_huoyuedu = 0
	local has_huoyuedu = 0
	for k,v in ipairs(complete_times_list) do
		if v.type == type then
			max_huoyuedu = v.exp_per_times * (v.complete_max_times - v.buy_times)
			has_huoyuedu = max_huoyuedu > 0 and v.exp_per_times * v.complete_times or 0
			return max_huoyuedu > 0 and has_huoyuedu, max_huoyuedu or -1,-1
		end
	end

	return -1,-1
end