function MengLingView:InitMengLingUpGradePanel()
    if not self.mengling_upgrade_cur_item then
        self.mengling_upgrade_cur_item = ItemCell.New(self.node_list.mengling_upgrade_cur_item)
        -- self.mengling_upgrade_cur_item:SetItemTipFrom(ItemTip.FROM_MENGLING_EQUIP)
    end

    if not self.mengling_upgrade_next_item then
        self.mengling_upgrade_next_item = ItemCell.New(self.node_list.mengling_upgrade_next_item)
    end

    if not self.mengling_upgrade_cost_item then
        self.mengling_upgrade_cost_item = ItemCell.New(self.node_list.mengling_upgrade_cost_item)
    end

    XUI.AddClickEventListener(self.node_list.btn_mengling_upgrade, BindTool.Bind(self.OnCLickMengLingUpGrade, self)) 
end

function MengLingView:DeleteMengLingUpGradePanel()
    if self.mengling_upgrade_cur_item then
        self.mengling_upgrade_cur_item:DeleteMe()
        self.mengling_upgrade_cur_item = nil
    end

    if self.mengling_upgrade_next_item then
        self.mengling_upgrade_next_item:DeleteMe()
        self.mengling_upgrade_next_item = nil
    end

    if self.mengling_upgrade_cost_item then
        self.mengling_upgrade_cost_item:DeleteMe()
        self.mengling_upgrade_cost_item = nil
    end
end

function MengLingView:FlushMengLingUpGradePanel()
    if self:MengLingCanShowOperaPanel() then
        self.node_list.mengling_no_wearequip:CustomSetActive(true)
        self.node_list.mengling_upgrade_panel_root:CustomSetActive(false)
        return
    end

    self.node_list.mengling_no_wearequip:CustomSetActive(false)
    self.node_list.mengling_upgrade_panel_root:CustomSetActive(true)

    local data_info = MengLingWGData.Instance:GetMengLingEquipCellInfo(self.select_equip_suit_seq, self.select_equip_item_slot)
    local target_item_id = data_info.item_id
    self.mengling_upgrade_cur_item:SetData(data_info)

    local color_str, color = ItemWGData.Instance:GetItemColor(target_item_id)
    self.node_list.mengling_upgrade_cur_color.text.text = Language.MengLing.MengLingEquipPinZhi .. ToColorStr(Language.Common.ColorName[color], color_str)

    local up_grade_cfg = MengLingWGData.Instance:GetMengLingEquipUpGradeCfg(target_item_id)
    local is_max_grade = IsEmptyTable(up_grade_cfg)

    if not is_max_grade then
        self.mengling_upgrade_next_item:SetData({item_id = up_grade_cfg.item_id})
        local next_color_str, next_color = ItemWGData.Instance:GetItemColor(up_grade_cfg.item_id)
        self.node_list.mengling_upgrade_next_color.text.text = Language.MengLing.MengLingEquipPinZhi .. ToColorStr(Language.Common.ColorName[next_color], next_color_str)

        local item_num = MengLingWGData.Instance:GetMengLingEquipNumByItemId(target_item_id)
        local need_num = up_grade_cfg.stuff_num1
        local enough = item_num >= need_num
        self.mengling_upgrade_cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. need_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.mengling_upgrade_cost_item:SetRightBottomColorText(right_text)
            self.mengling_upgrade_cost_item:SetRightBottomTextVisible(true)
        end)
        self.mengling_upgrade_cost_item:SetData({item_id = target_item_id})
    end

    self.node_list.mengling_upgrade_cost:CustomSetActive(not is_max_grade)
    self.node_list.mengling_upgrade_max_flag:CustomSetActive(is_max_grade)
    self.node_list.mengling_upgrade_arrow:CustomSetActive(not is_max_grade)
    self.node_list.mengling_upgrade_next_item:CustomSetActive(not is_max_grade)
end

function MengLingView:OnCLickMengLingUpGrade()
    if IsEmptyTable(self.select_equip_item_data) then
        return
    end

    local data_info = MengLingWGData.Instance:GetMengLingEquipCellInfo(self.select_equip_suit_seq, self.select_equip_item_slot)
    local target_item_id = data_info.item_id
    local up_grade_cfg = MengLingWGData.Instance:GetMengLingEquipUpGradeCfg(target_item_id)

    if not IsEmptyTable(up_grade_cfg) then
        local item_num = MengLingWGData.Instance:GetMengLingEquipNumByItemId(target_item_id)

        if item_num >= up_grade_cfg.stuff_num1 then
            MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_UPGRADE, 
                self.select_equip_suit_seq, self.select_equip_item_slot, up_grade_cfg.item_id)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengjie, is_success = true, pos = Vector2(0, 0)})
        else
            TipWGCtrl.Instance:OpenItem({item_id = target_item_id})
        end
    end
end