﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Nirvana;
using UnityEngine.EventSystems;
using System;

[RequireComponent(typeof(ScrollRect))]
public class LoopScrollView : <PERSON>o<PERSON><PERSON><PERSON>our, IEndDragHandler, IBeginDragHandler
{
	public bool isVertical;
	public Action<GameObject, int> flushAction;
	public bool isPage = false;
	public float snapTweenTime = 0.2f;
	public float startSnapSpeed = 400;
	[HideInInspector]
	public int childCount;
	[HideInInspector]
	public int showChildCount;
	[HideInInspector]
	public float moveDistance;
	float childSize;
	float viewSize;
	float lastPos;
	float speed;
	float lastCalSpeedTime;
	[HideInInspector]
	public int firstGoIndex = 0;
	bool needSnap = false;
	[HideInInspector]
	public bool drapping = false;

	public GameObject childItemPrefab;
	[HideInInspector]
	public GameObject loopRoot;
	RectTransform loopRootRect;
	ScrollRect scrollRect;
	[HideInInspector]
	public LinkedList<GameObject> goLinkedList;
	HorizontalOrVerticalLayoutGroup contentGroup;

	void Awake()
	{
		goLinkedList = new LinkedList<GameObject>();

		scrollRect = GetComponent<ScrollRect>();
		scrollRect.vertical = isVertical;
		scrollRect.horizontal = !isVertical;
		scrollRect.movementType = ScrollRect.MovementType.Unrestricted;

		// 初始化content
		if (scrollRect.content == null)
		{
			scrollRect.content = new GameObject("content", typeof(RectTransform)).GetComponent<RectTransform>();
			scrollRect.content.anchorMax = new Vector2(0, 1);
			scrollRect.content.anchorMin = new Vector2(0, 1);
			scrollRect.content.SetParent(scrollRect.viewport, false);
		}
		scrollRect.content.pivot = new Vector2(0, 1);

		// 初始化循环父节点
		if (loopRoot == null)
		{
			loopRoot = new GameObject("loopRoot(auto)", typeof(RectTransform));
			loopRoot.transform.SetParent(scrollRect.content, false);
		}
		loopRootRect = loopRoot.GetComponent<RectTransform>();

		if (isVertical)
		{
			contentGroup = loopRoot.GetOrAddComponent<VerticalLayoutGroup>();
		}
		else
		{
			contentGroup = loopRoot.GetOrAddComponent<HorizontalLayoutGroup>();
		}
		contentGroup.childForceExpandWidth = false;
		contentGroup.childControlWidth = false;
		contentGroup.childForceExpandHeight = false;
		contentGroup.childControlHeight = false;

		this.InitContnet();
	}

	void Update()
	{
		float topHeight;
		if (isVertical)
		{
			topHeight = (scrollRect.content.anchoredPosition.y + loopRootRect.anchoredPosition.y);
		}
		else
		{
			topHeight = -(scrollRect.content.anchoredPosition.x + loopRootRect.anchoredPosition.x);
		}
		float bottomHeight = moveDistance - topHeight;

		if (topHeight - bottomHeight > childSize * 2)
		{
			int count = Mathf.FloorToInt((topHeight - bottomHeight) / childSize / 2);
			for (int i = 0; i < count; i++)
			{
				MoveCellToBottom();
			}
		}
		if (bottomHeight - topHeight > childSize * 2)
		{
			int count = Mathf.FloorToInt((bottomHeight - topHeight) / childSize / 2);
			for (int i = 0; i < count; i++)
			{
				MoveCellToTop();
			}
		}

		if(Time.unscaledTime - lastCalSpeedTime > 0.05f)
		{
			lastCalSpeedTime = Time.unscaledTime;
			if (isVertical)
			{
				speed = (lastPos - scrollRect.content.anchoredPosition.y) / 0.05f;
				lastPos = scrollRect.content.anchoredPosition.y;
			}
			else
			{
				speed = (lastPos - scrollRect.content.anchoredPosition.x) / 0.05f;
				lastPos = scrollRect.content.anchoredPosition.x;
			}
		}

		if (Mathf.Abs(speed) < startSnapSpeed && needSnap && !drapping)
		{
			needSnap = false;
			Snap();
		}
	}

	void MoveCellToTop()
	{
		GameObject cell = goLinkedList.Last.Value;
		goLinkedList.RemoveLast();
		cell.transform.SetAsFirstSibling();
		goLinkedList.AddFirst(cell);
		Vector2 pos = loopRootRect.anchoredPosition;
		if (isVertical)
		{
			pos.y += childSize;
		}
		else
		{
			pos.x -= childSize;
		}
		firstGoIndex -= 1;
		loopRootRect.anchoredPosition = pos;
		this.FlushItem(cell, firstGoIndex);
	}

	void MoveCellToBottom()
	{
		GameObject cell = goLinkedList.First.Value;
		goLinkedList.RemoveFirst();
		cell.transform.SetAsLastSibling();
		goLinkedList.AddLast(cell);
		Vector2 pos = loopRootRect.anchoredPosition;
		if (isVertical)
		{
			pos.y -= childSize;
		}
		else
		{
			pos.x += childSize;
		}
		firstGoIndex += 1;
		loopRootRect.anchoredPosition = pos;
		this.FlushItem(cell, firstGoIndex + childCount - 1);
	}

	public float GetChildSize()
	{
		if (isVertical)
		{
			return childItemPrefab.GetComponent<RectTransform>().rect.height;
		}
		else
		{
			return childItemPrefab.GetComponent<RectTransform>().rect.width;
		}
	}

	public float GetViewSize()
	{
		if (isVertical)
		{
			return scrollRect.viewport.rect.height;
		}
		else
		{
			return scrollRect.viewport.rect.width;
		}
	}

	void InitContnet()
	{
		childSize = this.GetChildSize();
		viewSize = this.GetViewSize();
		showChildCount = Mathf.CeilToInt(viewSize / childSize);
		childCount = showChildCount + 4;
		for (int i = 0; i < childCount; i++)
		{
			GameObject go = Instantiate(childItemPrefab);
			go.GetOrAddComponent<LayoutElement>();
			go.name = go.name + i;
			go.transform.SetParent(loopRootRect, false);
			goLinkedList.AddLast(go);
		}

		float contentSize = childCount * childSize + (childCount - 1) * contentGroup.spacing + contentGroup.padding.top + contentGroup.padding.bottom;

		moveDistance = contentSize - viewSize;

		Vector2 size = loopRootRect.sizeDelta;
		if (isVertical)
		{
			size.y = contentSize;
		}
		else
		{
			size.x = contentSize;
		}
		loopRootRect.sizeDelta = size;

		size = scrollRect.content.sizeDelta;
		if (isVertical)
		{
			size.y = contentSize;
		}
		else
		{
			size.x = contentSize;
		}
		scrollRect.content.sizeDelta = size;
	}

	void FlushItem(GameObject go, int index)
	{
		if(flushAction != null)
		{
			flushAction(go, index);
		}
	}

	public float GetNearestPagePos()
	{
		int pos = 0;
		if (isVertical)
		{
			pos = Mathf.RoundToInt(scrollRect.verticalNormalizedPosition / GetSingleChildNormalizePos());
		}
		else
		{
			pos = Mathf.RoundToInt(scrollRect.horizontalNormalizedPosition / GetSingleChildNormalizePos());
		}
		return pos * GetSingleChildNormalizePos();
	}

	public float GetSingleChildNormalizePos()
	{
		return childSize / moveDistance;
	}

	public void AddFlushListener(Action<GameObject, int> action)
	{
		flushAction = action;
	}

	public void FlushAllChild()
	{
		var node = goLinkedList.First;
		int i = firstGoIndex;
		while (node != null)
		{
			this.FlushItem(node.Value, i++);
			node = node.Next;
		}
	}

	public void Move(float stepCount = 1, float duration = 0.3f, Action callback = null)
	{
		if (isVertical)
		{
			float curPos = scrollRect.verticalNormalizedPosition;
			scrollRect.DoVerticalPosition(curPos, curPos + GetSingleChildNormalizePos() * stepCount, duration, callback);
		}
		else
		{
			float curPos = scrollRect.horizontalNormalizedPosition;
			scrollRect.DoHorizontalPosition(curPos, curPos + GetSingleChildNormalizePos() * stepCount, duration, callback);
		}
	}

	public void OnBeginDrag(PointerEventData eventData)
	{
		needSnap = false;
		drapping = true;
	}

	public void OnEndDrag(PointerEventData eventData)
	{
		needSnap = true;
		drapping = false;
	}

	public void Snap()
	{
		if (isPage)
		{
			if (isVertical)
			{
				float curPos = scrollRect.verticalNormalizedPosition;
				scrollRect.DoVerticalPosition(curPos, this.GetNearestPagePos(), snapTweenTime, null);
			}
			else
			{
				float curPos = scrollRect.horizontalNormalizedPosition;
				scrollRect.DoHorizontalPosition(curPos, this.GetNearestPagePos(), snapTweenTime, null);
			}
		}
	}
}

