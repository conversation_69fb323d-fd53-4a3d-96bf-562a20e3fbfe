TransFerNewView = TransFerNewView or BaseClass(SafeBaseView)

function TransFerNewView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg()
	self:LoadConfig()
	self.view_name = GuideModuleName.ZhuanSheng

	self.task_data_change = BindTool.Bind(self.OnOneTaskDataChange, self)
	self.is_safe_area_adapter = true

	self.cur_prof_level = 1
end

function TransFerNewView:ReleaseCallBack()

	self:CleanTimer()
	if self.star_attr_list then
        for i = 1, 6 do
            self.star_attr_list[i]:DeleteMe()
            self.star_attr_list[i] = nil
        end
        self.star_attr_list = nil
    end

	if self.level_list then
		self.level_list:DeleteMe()
		self.level_list = nil
	end

    if self.stage_llist then
		self.stage_llist:DeleteMe()
		self.stage_llist = nil
	end

    if self.transfer_task_list then
		self.transfer_task_list:DeleteMe()
		self.transfer_task_list = nil
	end

	if self.star_list then
		self.star_list:DeleteMe()
		self.star_list = nil
	end
	if self.star_item_cell then
		self.star_item_cell:DeleteMe()
		self.star_item_cell = nil
	end

	if self.stage_reward_list then
		self.stage_reward_list:DeleteMe()
		self.stage_reward_list = nil
	end
	
	if self.stage_word_list then
		self.stage_word_list:DeleteMe()
		self.stage_word_list = nil
	end

	
	if self.stage_place_list then
		self.stage_place_list:DeleteMe()
		self.stage_place_list = nil
	end
    
	
	if self.transfer_alert then
		self.transfer_alert:DeleteMe()
		self.transfer_alert = nil
	end

	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	if self.get_guide_ui_event then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideUIName.ZhuanSheng, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	
	if self.cur_equip_list then
		for k,v in pairs(self.cur_equip_list) do
			v:DeleteMe()
		end
		self.cur_equip_list = nil
	end
end

-- 加载配置
function TransFerNewView:LoadConfig()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")

	self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "layout_transfer_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	-- self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "VerticalTabbar")
end

function TransFerNewView:ChangeRes()
	self.node_list.spine_root_god:SetActive(false)
	self.node_list.spine_root_demons:SetActive(true)

	-- 背景
	local bundle2, asset2 = ResPath.GetF2RawImagesPNG("a3_zztpcg_cm_bg_1")
	self.node_list["spine_bg"].raw_image:LoadSprite(bundle2, asset2)
	-- 二阶段标题
	local bundle2, asset2 = ResPath.GetF2RawImagesPNG("a3_js_di_bt1_1")
	self.node_list["img_stage_2_1"].raw_image:LoadSprite(bundle2, asset2)

	self.node_list["img_stage_2_2"].raw_image:LoadSprite(bundle2, asset2)

	self.node_list.task_name_stage_2_1.text.text = Language.TransFer.StageTitleStr_2_1_1
	self.node_list.task_name_stage_2_2.text.text = Language.TransFer.StageTitleStr_2_2_1
	self.node_list.text_desc_stage_2.text.text = ToColorStr(Language.TransFer.StageDescStr_2, COLOR3B.C7)
	-- 三阶段标题
	bundle2, asset2 = ResPath.GetF2RawImagesPNG("a3_js_di_bt_1")
	self.node_list["img_stage_3_1"].raw_image:LoadSprite(bundle2, asset2)

	self.node_list["img_stage_3_2"].raw_image:LoadSprite(bundle2, asset2)

	self.node_list.task_name_stage_1.text.text = Language.TransFer.StageTitleStr_3_1_1
	self.node_list.task_name_stage_2.text.text = Language.TransFer.StageTitleStr_3_2_1

	-- 状态
	bundle2, asset2 = ResPath.GetTransFer("a3_js_di_1_1")
	self.node_list["btn_attr_add"].image:LoadSprite(bundle2, asset2)


end

function TransFerNewView:CloseCallBack()
	self.is_need_auto_close_view = nil
end

function TransFerNewView:LoadCallBack()
	self.is_need_auto_close_view = nil

	self.god_and_demons_type = TransFerWGData.Instance:GetGodAndDemonsType()

	local bundle, asset = ResPath.GetF2RawImagesJPG("a3_js_s_bg")
	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		bundle, asset = ResPath.GetF2RawImagesJPG("a3_js_m_bg")

		self:ChangeRes()
		self.node_list["title_view_name"].text.text = TransFerWGData.Instance:GetOtherCfgByKey("demons_title")
	else
		self.node_list.spine_root_god:SetActive(true)
		self.node_list.spine_root_demons:SetActive(false)
		self.node_list["title_view_name"].text.text = TransFerWGData.Instance:GetOtherCfgByKey("god_title")
	end

	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)


	if nil == self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.OnItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end

	
	self.role_prof_level = RoleWGData.Instance:GetZhuanZhiNumber()

    self:CreateLevelList()
    self:CreateStageList()

	self.node_list.star_attr_list:CustomSetActive(self.god_and_demons_type ~= 1)
	self.node_list.star_attr_list_1:CustomSetActive(self.god_and_demons_type == 1)
	if not self.star_attr_list then
        self.star_attr_list = {}
		local str = self.god_and_demons_type == 1 and "star_attr_1_" or  "star_attr_"
        for i = 1, 6 do
            local attr_render = CommonAddAttrRender.New(self.node_list[ str .. i])
            self.star_attr_list[i] = attr_render
            self.star_attr_list[i]:SetIndex(i)
        end
    end

	self.transfer_task_list = AsyncFancyRectView.New(TransferTaskItemRender, self.node_list.tansfer_task_list)
	self.star_list = AsyncListView.New(TransferStarItemRender, self.node_list.star_list)
	self.star_item_cell = ItemCell.New(self.node_list.star_item_cell)

	self.stage_reward_list = AsyncListView.New(ItemCell,self.node_list["stage_reward_list"])
	self.stage_reward_list:SetStartZeroIndex(true)
	-- 判断神魔
	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		self.stage_word_list = AsyncListView.New(ZhuanShengStageWordItemRender, self.node_list["stage_word_list_1"])
		self.node_list.stage_word_list_1:CustomSetActive(true)

	else
		self.stage_word_list = AsyncListView.New(ZhuanShengStageWordItemRender, self.node_list["stage_word_list"])
		self.node_list.stage_word_list:CustomSetActive(true)

	end
	
	self.stage_place_list = AsyncListView.New(ZhuanShengStagePlaceItemRender, self.node_list["stage_place_list"])
	
	--点亮
	self.node_list["btn_transfer_light"].button:AddClickListener(BindTool.Bind(self.OnClickTransferLight, self))
	-- 一键点亮
	self.node_list["btn_auto_complse"].button:AddClickListener(BindTool.Bind(self.OnClickAllLight, self))
	--属性加成
	self.node_list["btn_attr_add"].button:AddClickListener(BindTool.Bind(self.OnClickAttrShowTip,self))
	--转职
	self.node_list["btn_transfer_active"].button:AddClickListener(BindTool.Bind(self.OnClickActive,self))
	--副本 前往
	self.node_list["btn_transfer_goto"].button:AddClickListener(BindTool.Bind(self.OnClickFbGoto,self))
	
	if not self.cur_equip_list then
		self.cur_equip_list = {}
		for i = 0, 7 do
			self.cur_equip_list[i] = ItemCell.New(self.node_list.cur_equip_list:FindObj("cur_equip_cell_" .. i))
		end
	end


	local equip_bg_name = self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS and "a3_js_zbsjdi" or "a3_js_zbsjdi2"
	local equip_bg_bundle, equip_bg_asset = ResPath.GetRawImagesPNG(equip_bg_name)
	self.node_list["equip_root_bg"].raw_image:LoadSprite(equip_bg_bundle, equip_bg_asset, function()
		self.node_list["equip_root_bg"].raw_image:SetNativeSize()
	end)

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ZhuanSheng, self.get_guide_ui_event)
end

function TransFerNewView:OpenIndexCallBack(index)
	self.role_prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	self.is_first = true
	self:SetLevelList()
end

function TransFerNewView:ShowIndexCallBack(index)
	-- self.node_list.effect_loop:CustomSetActive(false)
	-- self.delay_timer =  GlobalTimerQuest:AddDelayTimer(function()
	-- 	self.node_list.effect_loop:CustomSetActive(true)
	-- end, 1)
end

function TransFerNewView:CleanTimer()
    -- if self.delay_timer then
    --     GlobalTimerQuest:CancelQuest(self.delay_timer)
    --     self.delay_timer = nil
    -- end
end


function TransFerNewView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "guide_right_root" then
		return self.node_list["guide_right_root"]
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

-- 创建转职等级列表
function TransFerNewView:CreateLevelList()
	if not self.level_list then
		self.level_list = AsyncListView.New(ZhuanShengLevelItemRender, self.node_list["level_list"])
        self.level_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectLevelItemHandler, self))
        self.level_list.IsLimitSelectByIndex = function(list, cell_index)

            local cfg = TransFerWGData.Instance:GetTransFerSingleRewardCfg(cell_index)
            local last_cfg = TransFerWGData.Instance:GetTransFerSingleRewardCfg(cell_index - 1)

			if not last_cfg then
				return false
			end
            if not cfg then
                return true
            end

            local role_level = GameVoManager.Instance:GetMainRoleVo().level
            local prof_zhuan = self.role_prof_level


            local str = ""
			if cfg then
				if cfg.open_level  > role_level then
					str = string.format(Language.TransFer.ShowTip, RoleWGData.Instance:TransToDianFengLevelStr(cfg.open_level))
				elseif cfg.zhuanzhi_level > prof_zhuan + 1 then
					str = string.format(Language.TransFer.ShowTip1, last_cfg.level_name)
				else
					return false
				end 
				
			end

            SysMsgWGCtrl.Instance:ErrorRemind(str)
            return true
        end
		
	end
end


-- 设置转职等级列表数据
function TransFerNewView:SetLevelList(jump_prof)
	if self.level_list then
        local data_list = TransFerWGData.Instance:GetTransFerRewardCfg()
		local role_level = GameVoManager.Instance:GetMainRoleVo().level
		local fiter_list = {}
		for index, value in ipairs(data_list) do
			if role_level>=value.show_level then
				table.insert(fiter_list,value)
			end
		end
		self.level_list:SetDataList(fiter_list)

		local prof_zhuan = self.role_prof_level
		if fiter_list[self.role_prof_level + 1] and role_level >= fiter_list[self.role_prof_level + 1].open_level then
			prof_zhuan = self.role_prof_level + 1
		end

		if jump_prof and prof_zhuan then
			local jump_to_prof = math.min(jump_prof, prof_zhuan)
			self.level_list:JumpToIndex(jump_to_prof)
			return
		end

		if self.is_update then
			self.level_list:JumpToIndex(self.cur_prof_level)
			return
		end

		self.level_list:JumpToIndex(prof_zhuan)
	end
end

function TransFerNewView:OnSelectLevelItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

	if self.cur_prof_level ~= item.data.zhuanzhi_level then
		self.is_need_auto_close_view = nil
	end

	self.cur_prof_data = item.data
	self.cur_prof_level = item.data.zhuanzhi_level

	self:SetStageList()
	self:FlushCurEquipment(self.cur_prof_level)

	local spe_title_img = self.cur_prof_data.spe_title_img
	local title_bundle, title_asset = ResPath.GetTransFer(spe_title_img)
	self.node_list.spe_title_img.image:LoadSprite(title_bundle, title_asset, function()
        self.node_list.spe_title_img.image:SetNativeSize()
    end)

	self.node_list.spe_title_str.tmp.text = self.cur_prof_data.spe_title_str
end

--刷新当前装备.
function TransFerNewView:FlushCurEquipment(prof_level)
	local order = prof_level or 1

	for i = 0, 7 do
		local equip_type = EquipWGData.GetEquipTypeByIndex(i)
		local equip_body_index = EquipBodyWGData.Instance:CalRoleEquipWearIndex(equip_type, order)
		local data = EquipWGData.Instance:GetGridData(equip_body_index)

		if self.cur_equip_list[i] then
			if data and data.item_id > 0 then
				self.cur_equip_list[i]:SetData(data)
			else
				self.cur_equip_list[i]:ClearData()
				self.cur_equip_list[i]:SetItemIcon(ResPath.GetEquipIcon(i))  -- 设置物品图标
			end
		end
	end
end


-- 创建转职阶段列表
function TransFerNewView:CreateStageList()
	if not self.stage_llist then
		self.stage_llist = AsyncListView.New(ZhuanShengStageItemRender, self.node_list["stage_llist"])
        self.stage_llist:SetSelectCallBack(BindTool.Bind1(self.OnSelectStageItemHandler, self))
        self.stage_llist.IsLimitSelectByIndex = function(list, cell_index)

            local cfg = TransFerWGData.Instance:GetStageCfgByLevelAndStage(self.cur_prof_level, cell_index)
            if not cfg then
                return true
            end

            local role_level = GameVoManager.Instance:GetMainRoleVo().level
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

            local str = ""
			if cfg then
				if cfg.open_day > open_day then
					str = string.format(Language.TransFer.ShowTip3, cfg.open_day)
				elseif cfg and  cfg.open_level  > role_level then
					str = string.format(Language.TransFer.ShowTip, RoleWGData.Instance:TransToDianFengLevelStr(cfg.open_level))
				else
					return false
				end 

			end

            SysMsgWGCtrl.Instance:ErrorRemind(str)
            return true
        end
		
	end

end

-- 设置转职等级列表数据
function TransFerNewView:SetStageList()
	if self.stage_llist then
        local data_list = TransFerWGData.Instance:GetStageCfgByLevel(self.cur_prof_level)
		self.stage_llist:SetDataList(data_list)

		if not self.is_update then
			local role_level = GameVoManager.Instance:GetMainRoleVo().level
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local index = 0
			for i, value in ipairs(data_list) do
				if role_level >= value.open_level and open_day >= value.open_day and not TransFerWGData.Instance:IsStageFinish(value) and index == 0 then
					index = i 
				end
			end
			self.stage_llist:JumpToIndex(index == 0 and 1 or index)
		end

		-- 阶段完成显示
		self.stage_word_list:SetDataList(data_list)

		self.is_update = false
		self.is_first = false
	end
end

function TransFerNewView:OnSelectStageItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

	if self.cur_stage ~= item.data.stage then
		self.is_need_auto_close_view = nil
	end

	self.cur_stage_data = item.data
	self.cur_stage = item.data.stage

	self:FlushLayer()
end

-- 设置转职等级列表数据
function TransFerNewView:SetStarList()

	if self.star_list then
        local data_list = TransFerWGData.Instance:GetStarCfgByLevel(self.cur_prof_level)
		self.star_list:SetDataList(data_list)
	end
end

-- 设置转职任务列表数据
function TransFerNewView:SetTasList()
	if self.transfer_task_list then
        local data_list = TransFerWGData.Instance:GetTaskCfgByLevelAndType(self.cur_prof_level, self.cur_stage)
		local task_num, all_mind_num = 0, 0
		for k,v in ipairs(data_list) do
			task_num = task_num + 1
			local task_info = TransFerWGData.Instance:GetTaskInfoBySeq(v.task_seq)
			if task_info then
				if task_info.is_complete and task_info.has_fetched_reward then
					all_mind_num = all_mind_num + 1
				end
			end
		end

		-- 直接关闭界面
		local is_all_mind = all_mind_num == task_num

		if self.is_need_auto_close_view == nil then
			self.is_need_auto_close_view = is_all_mind
		else
			if self.is_need_auto_close_view ~= is_all_mind then
				local cfg = TransFerWGData.Instance:GetStageCfgByLevelAndStage(self.cur_prof_level, self.cur_stage)
				if cfg and cfg.auto_close == 1 then
					local role_level = RoleWGData.Instance:GetRoleLevel()
					local next_cfg = TransFerWGData.Instance:GetStageCfgByLevelAndStage(self.cur_prof_level, self.cur_stage + 1)
					if next_cfg and role_level < next_cfg.open_level then
						self:Close()
						return
					end
				end
			end
		end

		table.sort(data_list, function (a, b)
			local task_info_a = TransFerWGData.Instance:GetTaskInfoBySeq(a.task_seq)
			local task_info_b = TransFerWGData.Instance:GetTaskInfoBySeq(b.task_seq)
			local sort_a = (task_info_a.is_complete and task_info_a.has_fetched_reward) and 3 or (task_info_a.is_complete and 0 or 1)
			local sort_b = (task_info_b.is_complete and task_info_b.has_fetched_reward) and 3 or (task_info_b.is_complete and 0 or 1)
			if sort_a == sort_b then
				return a.task_seq < b.task_seq
			end
			return sort_a < sort_b
		end)

		self.transfer_task_list:SetDataList(data_list)
	end
end

function TransFerNewView:FlushLayer()
	if not self.cur_stage_data then
		return 
	end

    self.node_list.layout_tansfer_stage_1:CustomSetActive(self.cur_stage_data.type == TransFerWGData.StageType.Task)
    self.node_list.layout_tansfer_stage_2:CustomSetActive(self.cur_stage_data.type == TransFerWGData.StageType.Star)
    self.node_list.layout_tansfer_stage_3:CustomSetActive(self.cur_stage_data.type == TransFerWGData.StageType.Fb)


	-- 根据阶段刷新界面
    if self.cur_stage_data.type == TransFerWGData.StageType.Task then
        self:FlushTask()
    elseif self.cur_stage_data.type == TransFerWGData.StageType.Star then
        self:FlushStar()
    elseif self.cur_stage_data.type == TransFerWGData.StageType.Fb then
        self:FlushFb()
    end

	-- 肉身信息显示
	local reward_cfg = TransFerWGData.Instance:GetTransFerSingleRewardCfg(self.cur_prof_level)
	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		self.node_list.text_body_name.text.text = ToColorStr("："..reward_cfg.body_name, COLOR3B.C10)
		self.node_list.text_level_name.text.text = ToColorStr(reward_cfg.level_name..Language.TransFer.BodyStr, COLOR3B.C7)

	else
		self.node_list.text_body_name.text.text = "："..reward_cfg.body_name
		self.node_list.text_level_name.text.text = reward_cfg.level_name..Language.TransFer.BodyStr

	end


	local skill_cfg = TransFerWGData.Instance:GetTransFerSingleRewardList(self.cur_prof_level)
	self.stage_reward_list:SetDataList(skill_cfg)


	if self.stage_word_list then
        local data_list = TransFerWGData.Instance:GetStageCfgByLevel(self.cur_prof_level)
		self.stage_word_list:SetDataList(data_list)

	end


end

function TransFerNewView:FlushTask()
    self:SetTasList()
end

function TransFerNewView:FlushStar()
	self:SetStarList()

	


	local star_level
	local prof_level = self.role_prof_level
	local next_star_cfg
	local star_cfg
	if prof_level < self.cur_prof_level then
		local cur_star_level = TransFerWGData.Instance:GetStarLevel()
		star_level = cur_star_level
		star_cfg = TransFerWGData.Instance:GetStarCfgByLevelAndStar(self.cur_prof_level, cur_star_level)
		next_star_cfg = TransFerWGData.Instance:GetStarCfgByLevelAndStar(self.cur_prof_level, cur_star_level + 1)
		if not IsEmptyTable(star_cfg) then
			local is_finish = TransFerWGData.Instance:IsAllLight()
			self.node_list.btn_transfer_light:CustomSetActive(not is_finish)
			self.node_list.btn_auto_complse:CustomSetActive(not is_finish)
			self.node_list.star_item_cell:CustomSetActive(true)
	
			local have_num = ItemWGData.Instance:GetItemNumInBagById(star_cfg.need_stuff_id)
			local show_color = have_num >= star_cfg.need_stuff_num and COLOR3B.GREEN or COLOR3B.PINK
	
			self.star_item_cell:SetData({item_id = star_cfg.need_stuff_id})
			self.star_item_cell:SetRightBottomColorText(ToColorStr(have_num, show_color) .. "/" .. star_cfg.need_stuff_num)
			self.star_item_cell:SetRightBottomTextVisible(true)
	
			self.node_list.btn_transfer_light_red:CustomSetActive(have_num >= star_cfg.need_stuff_num)

			self.node_list.img_stage_finish_2:CustomSetActive(is_finish)

			if is_finish then
				self:LightFinishShow()
			end
		else
			self:LightFinishShow()
			local max_level =TransFerWGData.Instance:GetStarCfgNumByLevel(self.cur_prof_level)
			star_cfg = TransFerWGData.Instance:GetStarCfgByLevelAndStar(self.cur_prof_level, max_level)
		end
	else
		self:LightFinishShow()
		local max_level =TransFerWGData.Instance:GetStarCfgNumByLevel(self.cur_prof_level)
		star_cfg = TransFerWGData.Instance:GetStarCfgByLevelAndStar(self.cur_prof_level, max_level)
	end

	-- 刷新属性显示
	local need_show_effect = false
	if self.star_level_cache and star_level and self.star_level_cache >= 0 and star_level > self.star_level_cache then
		need_show_effect = true
	end

	self.star_level_cache = star_level

	local show_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(star_cfg, next_star_cfg, nil, nil, 1, 6)

	for i = 1, #self.star_attr_list do
		self.star_attr_list[i]:SetData(show_attr_list[i])

		if need_show_effect then
			self.star_attr_list[i]:PlayAttrValueUpEffect()
		end
	end

end

function TransFerNewView:LightFinishShow()
	self.node_list.img_stage_finish_2:CustomSetActive(true)
	self.node_list.btn_transfer_light:CustomSetActive(false)
	self.node_list.btn_auto_complse:CustomSetActive(false)
	self.node_list.star_item_cell:CustomSetActive(false)
end

function TransFerNewView:FlushFb()
	local stage_cfg = TransFerWGData.Instance:GetStageCfgByLevelAndStage(self.cur_prof_level, self.cur_stage)
	local is_stage_finish = TransFerWGData.Instance:IsStageFinish(stage_cfg)
	local is_finish_fb = TransFerWGData.Instance:IsFinishFb()

	
	local data_list = TransFerWGData.Instance:GetStageCfgByLevel(self.cur_prof_level)
	local fiter_list = {}
	for index, value in ipairs(data_list) do
		if value.type ~= TransFerWGData.StageType.Fb then
			table.insert(fiter_list,value)
		end
	end
	self.stage_place_list:SetDataList(fiter_list)

	-- 是否其他所有已完成
	local is_other_finish =true
	for key, value in pairs(fiter_list) do
		if not TransFerWGData.Instance:IsStageFinish(value) then
			is_other_finish = false
		end
	end

	-- self.node_list.text_place.text.text = stage_cfg.place_puzzle

	local fb_cfg = TransFerWGData.Instance:GetProfFbCfg(self.cur_prof_level)
	
	if  is_other_finish or fb_cfg.task_type == 1 then
		self.node_list.text_pos.text.text = fb_cfg.task_desc
	else
		self.node_list.text_pos.text.text = Language.TransFer.PlaceTipsStr
	end

	if fb_cfg.task_type == 1 then
		self.node_list.text_btn_transfer_goto.text.text = Language.TransFer.GotoDujieStr
		self.node_list.text_need_capability.text.text = ""
		self.node_list.task_name_stage_2.text.text = Language.TransFer.StageTitleStr_3_2_1
	else
		self.node_list.text_btn_transfer_goto.text.text = Language.Common.GoTo
		
		-- local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
		-- local is_exceed_capability = role_zhanli >= fb_cfg.need_capability
		-- local capability = ToColorStr(fb_cfg.need_capability, is_exceed_capability and "#99ffbb" or "#ff9292")
		-- self.node_list.text_need_capability.text.text = string.format(Language.TransFer.RecommendCap,capability)
		self.node_list.text_need_capability.text.text = ""
		self.node_list.task_name_stage_2.text.text = Language.TransFer.StageTitleStr_3_2_1

	end

	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		-- self.node_list.text_place.text.text  = ToColorStr(stage_cfg.place_puzzle,COLOR3B.C7)
		
		if  is_other_finish then
			self.node_list.text_pos.text.text  = ToColorStr(fb_cfg.task_desc,COLOR3B.C7)
		else
			self.node_list.text_pos.text.text = ToColorStr(Language.TransFer.PlaceTipsStr,COLOR3B.C7)
		end
	end







	self.node_list.text_need_capability:CustomSetActive(not is_stage_finish and not is_finish_fb)
	self.node_list.btn_transfer_goto:CustomSetActive(not is_stage_finish and not is_finish_fb)
	self.node_list.btn_transfer_goto_red:CustomSetActive(is_other_finish and DujieWGData.Instance:IsCanDujie() )
	self.node_list.btn_transfer_active:CustomSetActive(is_finish_fb and not is_stage_finish)

end


function TransFerNewView:OnFlush(param_t, index)
	self.role_prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	for k, v in pairs(param_t) do
		if k == "all" then
			-- 首次进入触发跳转
			self.is_update = not self.is_first
			-- 完成后触发跳转
			if self.cur_prof_level and self.cur_stage then
				local data_list = TransFerWGData.Instance:GetStageCfgByLevel(self.cur_prof_level)
				for i, value in ipairs(data_list) do
					if self.cur_stage == i and TransFerWGData.Instance:IsStageFinish(value) then
						self.is_update = false
					end
				end
			end

			self:SetLevelList(v.jump_prof)
			self:FlushLayer()
		end
	end
end


function TransFerNewView:OnOneTaskDataChange(task_id, reason)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
		self:Flush(self.show_index)
	end
end

function TransFerNewView:OnItemDataChange()
	self:Flush()
end

function TransFerNewView:OnClickTransferLight()
	local cur_star_level = TransFerWGData.Instance:GetStarLevel()
	local star_cfg = TransFerWGData.Instance:GetStarCfgByLevelAndStar(self.cur_prof_level, cur_star_level + 1)
	if not IsEmptyTable(star_cfg) then

		local have_num = ItemWGData.Instance:GetItemNumInBagById(star_cfg.need_stuff_id)
		local show_color = have_num >= star_cfg.need_stuff_num and COLOR3B.GREEN or COLOR3B.PINK
		if have_num >= star_cfg.need_stuff_num then
			TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_LIGHT)
		else
			TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_LIGHT)
		end
	end
	
end

function TransFerNewView:OnClickAllLight()
	local cur_star_level = TransFerWGData.Instance:GetStarLevel()
	local star_cfg = TransFerWGData.Instance:GetStarCfgByLevelAndStar(self.cur_prof_level, cur_star_level + 1)
	if not IsEmptyTable(star_cfg) then
		local need_gold = TransFerWGData.Instance:GetAllLightNeedGold()
		if need_gold > 0 then
			local str = string.format(Language.TransFer.TransFerTips2, need_gold)
			local ok_fun = function ()
				TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_ONE_KEY_LIGHT)
			end

			if not self.alert then
				self.alert = Alert.New()
			end
			self.alert:SetOkFunc(ok_fun)
			self.alert:SetLableString(str)
			self.alert:SetLableRectWidth(450)
			self.alert:Open()
		else
			TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_ONE_KEY_LIGHT)
		end
	end
end

function TransFerNewView:OnClickActive()
	TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_ZHUANZHI)
end

function TransFerNewView:OnClickFbGoto()
	local data_list = TransFerWGData.Instance:GetStageCfgByLevel(self.cur_prof_level)
	local fiter_list = {}
	for index, value in ipairs(data_list) do
		if value.type ~= TransFerWGData.StageType.Fb then
			table.insert(fiter_list,value)
		end
	end
	-- 是否其他所有已完成
	local is_other_finish =true
	for key, value in pairs(fiter_list) do
		if not TransFerWGData.Instance:IsStageFinish(value) then
			is_other_finish = false
		end
	end

	if not is_other_finish then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TransFer.FBGotoTips)
		return 
	end

	if Scene.Instance:GetSceneType() ~= SceneType.Common then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotFindPath)
		return
	end
	local fb_cfg = TransFerWGData.Instance:GetProfFbCfg(self.cur_prof_level)
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	if fb_cfg and role_level < fb_cfg.level_limit then
		TipsSystemManager.Instance:ShowSystemTips(string.format(Language.TransFer.AcceptTip_2, RoleWGData.Instance:TransToDianFengLevelStr(fb_cfg.level_limit)))
		return 
	end
	if fb_cfg and fb_cfg.task_id_list  then
		local task_id_list = string.split(fb_cfg.task_id_list, ",")
		for index, value in ipairs(task_id_list) do
			local task_id = tonumber(value)
			if TaskWGData.Instance:GetTaskStatus(task_id) ~= GameEnum.TASK_STATUS_NONE and not TaskWGData.Instance:GetTaskIsCompleted(task_id) then
				-- TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_ZHUAN, true)
				MainuiWGCtrl.Instance:DoTask(task_id, TaskWGData.Instance:GetTaskStatus(task_id), true)
			end
		end
		self:Close()
	end
	
end

function TransFerNewView:OnClickAttrShowTip()
	TransFerWGCtrl.Instance:OpenAttrTipsView(self.cur_prof_level,self.cur_stage)
end
------------------------------Render---------------------------------------------

--------------------------ZhuanShengLevelItemRender------------------------

ZhuanShengLevelItemRender = ZhuanShengLevelItemRender or BaseClass(BaseRender)
function ZhuanShengLevelItemRender:__init()
	-- self.node_list["Lock"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
end

function ZhuanShengLevelItemRender:OnFlush()
	local god_and_demons_type = TransFerWGData.Instance:GetGodAndDemonsType()

	local bg_name = god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS and "a3_js_btn01" or "a3_js_btn02"
	local bg_bundle, bg_asset = ResPath.GetTransFer(bg_name)
	self.node_list.Image.image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.Image.image:SetNativeSize()
    end)

	local text_bg_anme = god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS and "a3_js_btn04" or "a3_js_btn05"
	local text_bg_bundle, text_bg_asset = ResPath.GetTransFer(text_bg_anme)
	self.node_list.text_bg.image:LoadSprite(text_bg_bundle, text_bg_asset, function()
        self.node_list.text_bg.image:SetNativeSize()
    end)

	self.node_list.Text.text.text = self.data.level_name

	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	if self.data.zhuanzhi_level == prof_level + 1 then
		local remind = TransFerWGData.Instance:GetTransFerRemind()
		self.node_list.RedPoint:CustomSetActive(remind > 0)
	else
		self.node_list.RedPoint:CustomSetActive(false)
	end

end

function ZhuanShengLevelItemRender:OnSelectChange(is_select)
    self.node_list.hl_bg:CustomSetActive(is_select)
end

-- function ZhuanShengLevelItemRender:OnClickCell()
-- 	local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
-- 	local role_level = GameVoManager.Instance:GetMainRoleVo().level
-- 	local transferopenfaster = TransFerWGData.Instance:GetTransferOpenFactor()
-- 	local str = ""
-- 	if transferopenfaster["transfer_" .. self.index] > role_level then
-- 		str = string.format(Language.TransFer.ShowTip, transferopenfaster["transfer_" .. self.index])
-- 	elseif self.index > prof_zhuan + 1 then
-- 		str = string.format(Language.TransFer.ShowTip1, prof_zhuan + 1)
-- 	end 

-- 	SysMsgWGCtrl.Instance:ErrorRemind(str)
-- end

--------------------------ZhuanShengStageItemRender------------------------

ZhuanShengStageItemRender = ZhuanShengStageItemRender or BaseClass(BaseRender)
function ZhuanShengStageItemRender:__init()

end

function ZhuanShengStageItemRender:OnFlush()

    self.node_list.Text.text.text = self.data.stage_name
    self.node_list.TextHL.text.text = self.data.stage_name
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if self.data.prof_level == prof_level + 1 and role_level >= self.data.open_level and open_day >= self.data.open_day then
		local remind = false
		if self.data.type == TransFerWGData.StageType.Task then
			-- 是否可领取
			if TransFerWGData.Instance:IsCanReceive(self.data.stage) then
				remind = true
			end
		elseif self.data.type == TransFerWGData.StageType.Star then
			-- 是否可点亮
			if TransFerWGData.Instance:IsCanLight() then
				remind = true
			end
		elseif self.data.type == TransFerWGData.StageType.Fb then
			-- 是否可转职
			if TransFerWGData.Instance:IsFinishFb() then
				remind = true
			end
			local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
			local fb_cfg = TransFerWGData.Instance:GetProfFbCfg(self.data.prof_level)
			if fb_cfg and role_level >= fb_cfg.level_limit  and TransFerWGData.Instance:IsAllLight() and TransFerWGData.Instance:IsAllReceive() and DujieWGData.Instance:IsCanDujie() then
				remind = true
			end
		end

		self.node_list.RedPoint:CustomSetActive(remind)
	else
		self.node_list.RedPoint:CustomSetActive(false)
	end
end

function ZhuanShengStageItemRender:OnSelectChange(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
    self.node_list.HLImage:CustomSetActive(is_select)
end

ZhuanShengStageWordItemRender = ZhuanShengStageWordItemRender or BaseClass(BaseRender)
function ZhuanShengStageWordItemRender:__init()

end

function ZhuanShengStageWordItemRender:OnFlush()

    self.node_list.text_word.text.text = self.data.stage_word
    self.node_list.text_word_hl.text.text = self.data.stage_word
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	
	self.node_list.normal_img:CustomSetActive(prof_level >= self.data.prof_level or (self.data.type ~= TransFerWGData.StageType.Fb and TransFerWGData.Instance:IsStageFinish(self.data)))
end

ZhuanShengStagePlaceItemRender = ZhuanShengStagePlaceItemRender or BaseClass(BaseRender)
function ZhuanShengStagePlaceItemRender:__init()


end

function ZhuanShengStagePlaceItemRender:OnFlush()
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local god_and_demons_type = TransFerWGData.Instance:GetGodAndDemonsType()
	if god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		local bundle_name, asset_name = ResPath.GetTransFer("a3_js_di_3_1")
		self.node_list.place_item.image:LoadSprite(bundle_name, asset_name)

		if prof_level >= self.data.prof_level or TransFerWGData.Instance:IsStageFinish(self.data) then
			self.node_list.text_place.text.text = ToColorStr(self.data.place_puzzle, COLOR3B.C7)
		else
			self.node_list.text_place.text.text = ToColorStr(Language.TransFer.PlaceStr, COLOR3B.C7)
		end
		
		if self.data.type == TransFerWGData.StageType.Fb then
			self.node_list.text_place.text.text = ToColorStr(self.data.place_puzzle, COLOR3B.C7)
		end
	else
		
		
		if prof_level >= self.data.prof_level or TransFerWGData.Instance:IsStageFinish(self.data) then
			self.node_list.text_place.text.text = self.data.place_puzzle
		else
			self.node_list.text_place.text.text = Language.TransFer.PlaceStr
		end

		if self.data.type == TransFerWGData.StageType.Fb then
			self.node_list.text_place.text.text = self.data.place_puzzle
		end
	end
end

-----------------------------TransferTaskItemRender-----------------------------
TransferTaskItemRender = TransferTaskItemRender or BaseClass(BaseRender)
function TransferTaskItemRender:__init()
	XUI.AddClickEventListener(self.node_list["task_btn"], BindTool.Bind(self.ClickTaskBtn, self))
	XUI.AddClickEventListener(self.node_list["task_get_btn"], BindTool.Bind(self.ClickGet, self))
	self.view.rect_fancy_cell.RefreshPosDel = BindTool.Bind(self.UpdatePosition, self)

	self.god_and_demons_type = TransFerWGData.Instance:GetGodAndDemonsType()

	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then

		local bundle2, asset2 = ResPath.GetF2RawImagesPNG("a3_js_di_bt2_1")
		self.node_list["img_bg"].raw_image:LoadSprite(bundle2, asset2, function()
			self.node_list["img_bg"].raw_image:SetNativeSize()
		end)

	end

	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function TransferTaskItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TransferTaskItemRender:OnFlush()
	if not self.data then return end

	local reward_item = nil
	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		reward_item = self.data.demons_reward[0]
	else
		reward_item = self.data.reward_item[0]
	end

	self.item_cell:SetData({item_id = reward_item.item_id})

	self.item_cell:SetRightBottomTextVisible(true)
	self.item_cell:SetRightBottomColorText(reward_item.num)

	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		self.node_list.task_name.text.text = ToColorStr(self.data.task_title, "#381211")
	else
		self.node_list.task_name.text.text = self.data.task_title
	end

	--进度显示

	local task_info = TransFerWGData.Instance:GetTaskInfoBySeq(self.data.task_seq)

	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()

	local is_complete =prof_level >= self.data.zhuanzhi_level and true or (task_info.is_complete or false) 
	local has_fetched_reward = prof_level >= self.data.zhuanzhi_level and true or (task_info.has_fetched_reward or false)
	-- 是否可领取
	local is_can_get = is_complete and not has_fetched_reward
	local per = ""
	local progress_num = 0
	if is_complete then
		per = ToColorStr(string.format("（%s/%s）", CommonDataManager.ConverGoldByThousand(self.data.param1), CommonDataManager.ConverGoldByThousand(self.data.param1)) , COLOR3B.C7)
	else
		if task_info and task_info.progress then
			progress_num = task_info.progress
		end

		if progress_num >= self.data.param1 then
			per = ToColorStr(string.format("（%s/%s）", CommonDataManager.ConverPowerByThousand(progress_num), CommonDataManager.ConverPowerByThousand(self.data.param1)),  COLOR3B.DEFAULT_NUM)
			is_can_get = true
		else
			per = ToColorStr(string.format("（%s/%s）", CommonDataManager.ConverPowerByThousand(progress_num), CommonDataManager.ConverPowerByThousand(self.data.param1)), COLOR3B.PINK)
		end
	end

	self.node_list["task_desc"].text.text = ToColorStr(self.data.task_des, is_complete and COLOR3B.C7 or COLOR3B.C4)
	self.node_list["task_num"].text.text = per

	self.node_list["task_finish"]:SetActive(is_complete and not is_can_get)
	self.node_list["task_get_btn"]:SetActive(is_can_get)
	self.node_list["remind_effect"]:SetActive(is_can_get)
	self.node_list["task_btn"]:SetActive(not is_complete and not is_can_get)

	-- local level = RoleWGData.Instance:GetAttr('level')

	self.item_cell:SetLingQuVisible(has_fetched_reward)
	-- self.node_list["remind"]:SetActive(task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 and level >= task_cfg.min_level)
	-- self.node_list["get_remind"]:SetActive(task_cfg.condition ~= GameEnum.TASK_COMPLETE_CONDITION_3 and can_commit and level >= task_cfg.min_level)
end

function TransferTaskItemRender:ClickGet()
	TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_FETCH_REWARD, self.data.task_seq)
end

function TransferTaskItemRender:ClickTaskBtn()
	TransFerWGCtrl.Instance:GoToTask(self.data)
end

function TransferTaskItemRender:UpdatePosition(normalizedPosition)
	-- 左右滑动效果 策划不要 插件无用武之地
	-- local wave = (math.sin((normalizedPosition/0.2164+0.5) * math.pi)) * 41;
	-- self.view.transform.localPosition = self.view.transform.localPosition + Vector3(1 * wave, 0, 0)
	-- print_error("self.index:"..tostring(self.index))
	local wave = ((self.index %2 ==0 and 1 or -1) ) * 41;
	self.view.transform.localPosition = self.view.transform.localPosition + Vector3(1 * wave, 0, 0)
end


-----------------------------TransferStarItemRender-----------------------------
TransferStarItemRender = TransferStarItemRender or BaseClass(BaseRender)
function TransferStarItemRender:__init()

end

function TransferStarItemRender:__delete()

end

function TransferStarItemRender:OnFlush()
	if not self.data then return end
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	if prof_level>=self.data.zhuanzhi_level then
		self.node_list.hl_img:CustomSetActive(true)
	else
		local star_level = TransFerWGData.Instance:GetStarLevel()
		self.node_list.hl_img:CustomSetActive(star_level >= self.data.level)
	end

end

