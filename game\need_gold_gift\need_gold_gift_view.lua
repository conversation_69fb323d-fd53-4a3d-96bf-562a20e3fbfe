-- 特推宝箱
NeedGoldGiftView = NeedGoldGiftView or BaseClass(SafeBaseView)
function NeedGoldGiftView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)

	self:AddViewResource(0, "uis/view/need_gold_gift_ui_prefab", "layout_need_gold_gift")
end

function NeedGoldGiftView:__delete()

end

function NeedGoldGiftView:OpenCallBack()
end

function NeedGoldGiftView:CloseCallBack()
end

function NeedGoldGiftView:SetData()
end

function NeedGoldGiftView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))

	-- 仙玉宝箱列表
	self.box_list = self.node_list["box_list"]
	self.page_list = {}
    local list_delegate = self.box_list.page_simple_delegate
    list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
    list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCell, self)
    self.box_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnValueChanged, self))
    self.box_list.page_view:Reload()
    self.select_box_index = 1 
    self.box_list.page_view:JumpToIndex(self.select_box_index - 1, 0)

    self.gift_item_list = {}
    self.node_list["gift_item_prefab"]:SetActive(false)
end

function NeedGoldGiftView:ReleaseCallBack()
	self.box_list = nil

	if self.page_list then
		for i,v in ipairs(self.page_list) do
			v:DeleteMe()
		end
	end
	self.page_list = nil

	self.all_box_data = nil

	if self.gift_item_list then
		for i,v in ipairs(self.gift_item_list) do
			v:DeleteMe()
		end
		self.gift_item_list = nil
	end
end

function NeedGoldGiftView:OnFlush(param_t)
	local select_item_id = nil
	for k,v in pairs(param_t) do
		if k == "all" then
			if v.select_item_id then
				select_item_id = v.select_item_id
			end
		end
	end
	self:CalAllBoxInfo(select_item_id)
	self:FlushAllView()
end

function NeedGoldGiftView:FlushAllView()
    self.box_list.page_view:Reload()
    self.box_list.page_view:JumpToIndex(self.select_box_index - 1, 0)
    self:FlushCurBoxItemList()
    self:FlushOther()
end

function NeedGoldGiftView:FlushOther()
	local gift_bag_info = self.all_box_data[self.select_box_index]
	if gift_bag_info then
		local gift_cfg = ItemWGData.Instance:GetItemConfig(gift_bag_info.item_id)
    	local str = string.format(Language.NeedGoldGift.BuyBtnStr, gift_cfg.need_gold)
    	-- 购买按钮文字
    	EmojiTextUtil.ParseRichText(self.node_list["buy_btn_text"].tmp, str, 22, "#A3662BFF")
    	-- 物品名称
    	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(gift_bag_info.item_id, nil, true)
    end
end

-- 刷新当前选择的礼包内物品列表
function NeedGoldGiftView:FlushCurBoxItemList()
	local gift_bag_info = self.all_box_data[self.select_box_index]
	if gift_bag_info then
		for i,v in ipairs(self.gift_item_list) do
			v:SetActive(false)
		end

		local gift_cfg = ItemWGData.Instance:GetItemConfig(gift_bag_info.item_id) 				-- 礼包物品配置
		local drop_cfg = ItemWGData.Instance:GetDropCfgByDropId(gift_cfg.drop_device_id) 			-- 掉落配置
		for i,v in ipairs(drop_cfg.item_list) do
			if self.gift_item_list[i] == nil then
				local prefab = self.node_list["gift_item_prefab"].gameObject
				local go = ResMgr:Instantiate(prefab)
				go.transform:SetParent(self.node_list["gift_item_list"].transform, false)
				go.transform.localPosition = Vector3.zero
				go:SetActive(true)
				self.gift_item_list[i] = NeedGoldGiftItem.New(go)
			end
			v.param = nil
			self.gift_item_list[i]:SetData(v)
			self.gift_item_list[i]:SetActive(true)
		end
	end
end

-- 计算角色背包里所有的仙玉礼包
function NeedGoldGiftView:CalAllBoxInfo(select_item_id)
	self.all_box_data = {}
	local i = 1
	for _, v in pairs(ItemWGData.Instance:GetBagItemDataList()) do 		-- 普通背包
		if ItemWGData.Instance:IsNeedGoldGift(v.item_id) then
			self.all_box_data[i] = v
			if v.item_id == select_item_id then
				self.select_box_index = i
			end
			i = i + 1
		end
	end
end

-- 角色背包仙玉礼包数目
function NeedGoldGiftView:GetNumberOfCells()
	if self.all_box_data then
		return #self.all_box_data
	else
		return 0
	end
end

-- 刷新单个仙玉礼包
function NeedGoldGiftView:RefreshCell(cell_index, cell)
	if self.all_box_data then
		local box_cell = self.page_list[cell]

		if nil == box_cell then
			box_cell = NeedGoldGiftBox.New(cell.gameObject)
			box_cell:SetCurSelectFunc(BindTool.Bind(self.GetSelectBoxIndex, self))
			self.page_list[cell] = box_cell
		end
		box_cell:SetData(self.all_box_data[cell_index + 1])
		box_cell:SetIndex(cell_index + 1)
		box_cell:Flush()
	end
end

function NeedGoldGiftView:OnValueChanged(progress_vec2)
	local index = self.box_list.page_view.ActiveCellsMiddleIndex
    if index < 0 or index > self:GetNumberOfCells() - 1 then
        return
    end

    local box_index = index + 1
    if self.select_box_index ~= box_index then
        self.select_box_index = box_index
	    self:FlushCurBoxItemList()
	    self:FlushOther()
    end
end

-- 获取当前选中的礼包index
function NeedGoldGiftView:GetSelectBoxIndex()
	return self.select_box_index
end

function NeedGoldGiftView:JumpToPageIndex(index)
    self.box_list.page_view:JumpToIndex(index - 1, 0, 1)
end

-- 点击购买
function NeedGoldGiftView:OnClickBuy()
	local gift_bag_info = self.all_box_data[self.select_box_index]
	if gift_bag_info then
		local gift_cfg = ItemWGData.Instance:GetItemConfig(gift_bag_info.item_id)
		BagWGCtrl.Instance:SendUseItem(gift_bag_info.index, 1, nil, gift_cfg.need_gold)
	end
end
--------------------特推礼包item----------------------------
NeedGoldGiftBox = NeedGoldGiftBox or BaseClass(BaseRender)
function NeedGoldGiftBox:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function NeedGoldGiftBox:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function NeedGoldGiftBox:OnFlush()
	if self.data then
		local cur_select_box_index = self.cur_select_func()
		self.item_cell:SetData(self.data)
	end
end

function NeedGoldGiftBox:SetCurSelectFunc(cur_select_func)
	self.cur_select_func = cur_select_func
end

--------------------礼包内物品item----------------------------
NeedGoldGiftItem = NeedGoldGiftItem or BaseClass(BaseRender)
function NeedGoldGiftItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function NeedGoldGiftItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function NeedGoldGiftItem:OnFlush()
	if self.data then
		self.item_cell:SetData(self.data)
	end
end
