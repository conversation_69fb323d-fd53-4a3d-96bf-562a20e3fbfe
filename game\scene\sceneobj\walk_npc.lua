-- 行走NPC
WalkNpc = WalkNpc or BaseClass(Npc)

function WalkNpc:__init(vo)
	self.cur_path_index = 1
	self.forward = true
	self.stack1 = {{x = vo.pos_x, y = vo.pos_y}}
	self.stack2 = {}
	for k,v in ipairs(self.vo.paths) do
		table.insert(self.stack2, v)
	end
	self.bubble_config = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other
	self.bubble_leisure_npc_config = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").bubble_leisure_npc_list

	self.same_npc_text_list = {}
	for k,v in ipairs(self.bubble_leisure_npc_config) do
		if v.leisure_npc_id == self.vo.npc_id then						--如果话语属于当前NPC，则加入到当前NPC话语表中
			table.insert(self.same_npc_text_list, v.bubble_leisure_npc_text)
		end
	end
	self.next_update_time = 0
	self.target_pos_x = 0
	self.target_pos_y = 0
	self.is_active_follow_ui_root = false
end

function WalkNpc:__delete()
	if nil ~= self.bubble_change_timer then
		GlobalTimerQuest:CancelQuest(self.bubble_change_timer)
		self.bubble_change_timer = nil
	end

	if self.animator_handle then
		self.animator_handle:Dispose()
	end
end

function WalkNpc:OnEnterScene()
	SceneObj.OnEnterScene(self)
	self:GetFollowUi()
	self:BubbleControl()
end

function WalkNpc:Update(now_time, elapse_time)
	Npc.Update(self, now_time, elapse_time)
	if Status.NowTime >= self.next_update_time then
		self.next_update_time = Status.NowTime + 0.2
		local root = self.draw_obj:GetRoot()
		if root then
			local pos = self:GetLuaPosition()
			self:SetRealPos(pos.x, pos.z)
		end
	end
end

function WalkNpc:DoWalk()
	if #self.stack2 > 0 and not self.stop then
		local target_pos = self.stack2[1]
		local flag = true

		if nil ~= self.last_target_pos then
			local root = self.draw_obj:GetRoot()
			if root then
				local pos = self:GetLuaPosition()
				self:SetRealPos(pos.x, pos.z)
			end

			local diff_x = self.logic_pos.x - self.last_target_pos.x
			local diff_y = self.logic_pos.y - self.last_target_pos.y

			-- 如果距离上一个目标点比较远，继续保持上一个目标点
			if diff_x * diff_x + diff_y * diff_y > 9 then
				target_pos = self.last_target_pos
				flag = false
			end
		end

		if flag then
			if #self.stack2 > 1 then
				table.insert(self.stack1, 1, target_pos)
				table.remove(self.stack2, 1)
			else
				local temp = self.stack1
				self.stack1 = self.stack2
				self.stack2 = temp
			end
		end

		self.last_target_pos = target_pos
		self:SetDirectionByXY(target_pos.x, target_pos.y)
		local part = self.draw_obj:GetPart(SceneObjPart.Main)
		part:SetInteger(ANIMATOR_PARAM.STATUS, 1)
		self.target_pos_x, self.target_pos_y = GameMapHelper.LogicToWorld(target_pos.x, target_pos.y)
		self.draw_obj:MoveTo(self.target_pos_x, self.target_pos_y, COMMON_CONSTS.NPC_WALK_SPEED)
		self.draw_obj:SetMoveCallback(function (flag)
			if flag == 1 then
				part:SetInteger(ANIMATOR_PARAM.STATUS, 0)
				part:SetTrigger(SceneObjAnimator.Rest)
			end
		end)
	end
end

function WalkNpc:PlayAction()

end

function WalkNpc:OnModelLoaded(part, obj)
	Npc.OnModelLoaded(self, part, obj)
	SceneObj.OnModelLoaded(self, part, obj)
	if part == SceneObjPart.Main then
		if obj and obj.animator then
			self:DoWalk()
			if self.animator_handle then
				self.animator_handle:Dispose()
			end
			self.animator_handle = obj.animator:ListenEvent("rest/exit", BindTool.Bind1(self.DoWalk, self))
		end
	end
end

function WalkNpc:BubbleControl()
	local exist_time = self.bubble_config[1].exist_time

	if nil ~= self.bubble_change_timer then
		GlobalTimerQuest:CancelQuest(self.bubble_change_timer)
		self.bubble_change_timer = nil
	end

	self.bubble_change_timer = GlobalTimerQuest:AddRunQuest(function()
		local text = self.same_npc_text_list[math.random(1,#self.same_npc_text_list)] or ""	--随机选择话语
		self.follow_ui:ChangeBubble(text, exist_time)
	end, math.random(25, 35))
end

function WalkNpc:IsWalkNpc()
	return true
end

function WalkNpc:Stop()
	self.draw_obj:StopMove()
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:SetInteger(ANIMATOR_PARAM.STATUS, 0)
	self.stop = true
end

function WalkNpc:Continue()
	self.stop = false
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	self:SetDirectionByXY(GameMapHelper.WorldToLogic(self.target_pos_x, self.target_pos_y))
	part:SetInteger(ANIMATOR_PARAM.STATUS, 1)
	self.draw_obj:MoveTo(self.target_pos_x, self.target_pos_y, COMMON_CONSTS.NPC_WALK_SPEED)
	self.draw_obj:SetMoveCallback(function (flag)
		if flag == 1 then
			part:SetInteger(ANIMATOR_PARAM.STATUS, 0)
			part:SetTrigger(SceneObjAnimator.Rest)
		end
	end)
end

function WalkNpc:GetRandomStr()
	return self.same_npc_text_list[math.random(1,#self.same_npc_text_list)]
end