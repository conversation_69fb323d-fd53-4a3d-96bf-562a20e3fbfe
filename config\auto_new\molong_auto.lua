-- M-魔龙.xls
local item_table={
[1]={item_id=0,num=1,is_bind=1},
}
return {
reward={
{},
{grid=2,},
{grid=3,},
{grid=4,},
{grid=5,},
{grid=6,},
{grid=7,},
{grid=8,},
{grid=9,},
{grid=10,},
{grid=11,},
{grid=12,},
{grid=13,},
{grid=14,},
{grid=15,},
{grid=16,},
{grid=17,},
{grid=18,},
{grid=19,},
{grid=20,},
{grid=21,},
{grid=22,},
{grid=23,},
{grid=24,},
{grid=25,},
{grid=26,},
{grid=27,},
{grid=28,},
{grid=29,},
{grid=30,},
{grid=31,},
{grid=32,}},

move={
{},
{step=2,consume_gold=680,},
{step=3,consume_gold=1280,},
{step=4,consume_gold=3280,},
{step=5,consume_gold=6480,}},

reward_default_table={grid=1,reward_item=item_table[1],fanli_rate=0,},

move_default_table={step=1,consume_gold=300,}

}

