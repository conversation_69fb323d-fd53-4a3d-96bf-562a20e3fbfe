function ControlBeastsWGView:LoadStableViewCallBack()
    self.select_egg_index = nil
    self.select_egg_data = nil
    self.incubate_index = nil
    self.incubate_data = nil

    -- 初始化4孵化池
    if not self.incubate_list then
        self.incubate_list = {}
        
        for i = 1, 3 do
            local incubate_obj = self.node_list.beasts_incubate_list:FindObj(string.format("beasts_incubate%d", i))
            if incubate_obj then
                local cell = BeststsIncubateRender.New(incubate_obj, self)
                cell:SetViewCenterRoot(self.root_node_transform)
                cell:SetIndex(i)
                self.incubate_list[i] = cell
            end
        end
    end

    if not self.bag_flair_list then
        self.bag_flair_list = {}
        
        for i = 1, 5 do
            local incubate_obj = self.node_list.incubate_bag_flair:FindObj(string.format("flair_%d", i))
            if incubate_obj then
                local cell = BeststBagFlairItemRender.New(incubate_obj)
                cell:SetIndex(i)
                self.bag_flair_list[i] = cell
            end
        end
    end

    if not self.message_flair_list then
        self.message_flair_list = {}
        
        for i = 1, 5 do
            local incubate_obj = self.node_list.incubate_flair:FindObj(string.format("flair_%d", i))
            if incubate_obj then
                local cell = BeststBagFlairItemRender.New(incubate_obj)
                cell:SetIndex(i)
                self.message_flair_list[i] = cell
            end
        end
    end

    if not self.beast_egg_list_view then
        self.beast_egg_list_view = AsyncBaseGrid.New()
        self.beast_egg_list_view:CreateCells({col = 4, cell_count = BEAST_DEFINE.BEAST_PACK_COUNT_MAX, list_view = self.node_list["beasts_stable_bag_grid"], itemRender = BeastsEggItem})
		self.beast_egg_list_view:SetStartZeroIndex(false)
        self.beast_egg_list_view:SetSelectCallBack(BindTool.Bind(self.SelectBeastEggCellCallBack, self))
	end

    if not self.incubate_item then
        self.incubate_item = ItemCell.New(self.node_list.item_pos)
        self.incubate_item:SetIsShowTips(true)
    end

    if not self.beasts_stable_star_list then
		self.beasts_stable_star_list = {}
		for i = 1,5 do
			self.beasts_stable_star_list[i] = self.node_list["stable_beasts_star" .. i]
		end
	end
    
    self.bag_show_type = BAG_SHOW_TYPE.REVERSE
    self.bag_root_tween = self.node_list.bag_root:GetComponent(typeof(UGUITweenPosition))
    -- self.message_root_tween = self.node_list.beasts_incubate_message:GetComponent(typeof(UGUITweenPosition))
    self.message_root_tween = nil

	XUI.AddClickEventListener(self.node_list.bag_forward_button, BindTool.Bind2(self.PlayStableBagTweenPosition, self, BAG_SHOW_TYPE.FORWARD))
    XUI.AddClickEventListener(self.node_list.bag_reverse_button, BindTool.Bind2(self.PlayStableBagTweenPosition, self, BAG_SHOW_TYPE.REVERSE))
    XUI.AddClickEventListener(self.node_list.bag_close_button, BindTool.Bind2(self.PlayStableBagTweenPosition, self, BAG_SHOW_TYPE.REVERSE))
    XUI.AddClickEventListener(self.node_list.incubate_bag_btn, BindTool.Bind2(self.IncubateBeast, self))
    XUI.AddClickEventListener(self.node_list.incubate_bag_message, BindTool.Bind2(self.IncubateMessage, self))
    XUI.AddClickEventListener(self.node_list.beasts_bag_skill_show, BindTool.Bind2(self.BeastsBagSkillShow, self))
    XUI.AddClickEventListener(self.node_list.incubate_quick_const_icon, BindTool.Bind2(self.IncubateQuickItemShow, self))

    -- 孵化操作
    XUI.AddClickEventListener(self.node_list.incubate_speed, BindTool.Bind2(self.IncubateSpeed, self))                  -- 加速
    XUI.AddClickEventListener(self.node_list.incubate_quick, BindTool.Bind2(self.IncubateQuick, self))                  -- 一键孵化
    XUI.AddClickEventListener(self.node_list.incubate_release, BindTool.Bind2(self.IncubateRelease, self))              -- 重新孵化
    XUI.AddClickEventListener(self.node_list.incubate_owner, BindTool.Bind2(self.IncubateOwner, self))                  -- 认主
    XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind2(self.GoToGet, self))                              -- 前往获取
    XUI.AddClickEventListener(self.node_list.goto_contract_button, BindTool.Bind2(self.GoToGet, self))                              -- 前往获取
end

function ControlBeastsWGView:OpenStableViewCallBack()

end

function ControlBeastsWGView:CloseStableViewCallBack()
    self:PlayStableBagTweenPosition(BAG_SHOW_TYPE.REVERSE)
    self:PlayBeastsMessagePositionTween(false)
    self.select_egg_index = nil
    self.select_egg_data = nil
    self.incubate_index = nil
    self.incubate_data = nil
    self.show_message_root_tween = nil
end

function ControlBeastsWGView:ShowStableViewCallBack()

end

function ControlBeastsWGView:ReleaseStableViewCallBack()
    if self.incubate_list and #self.incubate_list > 0 then
		for _, incubate_cell in ipairs(self.incubate_list) do
			incubate_cell:DeleteMe()
			incubate_cell = nil
		end

		self.incubate_list = nil
	end

    if self.bag_flair_list and #self.bag_flair_list > 0 then
		for _, incubate_cell in ipairs(self.bag_flair_list) do
			incubate_cell:DeleteMe()
			incubate_cell = nil
		end

		self.bag_flair_list = nil
	end

    if self.message_flair_list and #self.message_flair_list > 0 then
		for _, incubate_cell in ipairs(self.message_flair_list) do
			incubate_cell:DeleteMe()
			incubate_cell = nil
		end

		self.message_flair_list = nil
	end

    self.bag_show_type = BAG_SHOW_TYPE.NONE
    self.bag_root_tween = nil
    self.message_root_tween = nil

    if self.beast_egg_list_view then
		self.beast_egg_list_view:DeleteMe()
		self.beast_egg_list_view = nil
	end

    if self.incubate_item then
		self.incubate_item:DeleteMe()
		self.incubate_item = nil
	end

    self.show_message_root_tween = nil
    self.beasts_stable_star_list = nil
end

function ControlBeastsWGView:CleanTimeDown()
    if CountDownManager.Instance:HasCountDown("item_incubate_time") then
        CountDownManager.Instance:RemoveCountDown("item_incubate_time")
    end
end

-----------------------------------------------------------------------------
-- 孵化池点击
function ControlBeastsWGView:OnSelectIncubateCB(beasts_item, is_force, is_click)
	if nil == beasts_item or nil == beasts_item.data then
		return
	end

    if (beasts_item.data.status == BEASTS_BORN_STATUS.UNBORN or beasts_item.data.status == BEASTS_BORN_STATUS.BORN) then    -- 显示当前的孵化的详细界面（显示加速等）
        self:PlayBeastsMessagePositionTween(true)
        if is_click or is_force then
            self:PlayStableBagTweenPosition(BAG_SHOW_TYPE.REVERSE)
        end
    elseif (beasts_item.data.status == BEASTS_BORN_STATUS.NONE or beasts_item.data.status == BEASTS_BORN_STATUS.CAN_BORN) then      -- 只展示是否需要红点
        self:PlayBeastsMessagePositionTween(false)
        if is_click then
            self:PlayStableBagTweenPosition(BAG_SHOW_TYPE.FORWARD)
        end
    end

    local index = beasts_item.index
    if self.incubate_index == index and not (is_force) then
        return
    end

    self.incubate_index = index
    self.incubate_data = beasts_item.data

    if beasts_item.data.status == BEASTS_BORN_STATUS.UNBORN or beasts_item.data.status == BEASTS_BORN_STATUS.BORN then    -- 显示当前的孵化的详细界面（显示加速等）
        self:SetBeastsIncubateData(beasts_item.data)
    end

    self:RefreshIncubateHl()
end

-- 刷新高亮
function ControlBeastsWGView:RefreshIncubateHl()
    if self.incubate_list  then
        for index, incubate_cell in ipairs(self.incubate_list) do
            if incubate_cell then
                incubate_cell:FlushSelectHl(self.incubate_index == index)
            end
        end
    end
end

-- 设置下方数据
function ControlBeastsWGView:SetBeastsIncubateData(incubate_data)
    local server_data = incubate_data.server_data
    if not server_data then
        return
    end
    
    local is_egg = incubate_data.status == BEASTS_BORN_STATUS.UNBORN
    self.incubate_item:SetData({item_id = server_data.beast_id, is_beast = false, bag_id = incubate_data.bag_id})
    self.node_list.stable_flair_pz:CustomSetActive(not is_egg)

    if not is_egg then
        local score_index, flair_score = ControlBeastsWGData.Instance:GetBeastBestFlairScoreIndex(incubate_data, true)
        local battle_type_str = string.format("a3_hs_bs_pz%d", score_index) 
        local bundle, asset = ResPath.GetControlBeastsImg(battle_type_str)
        self.node_list.stable_flair_pz.image:LoadSprite(bundle, asset)
        self.node_list.stable_flair_txt.text.text = string.format(Language.ContralBeasts.AttrTitle5, math.floor(flair_score)) 
    end

    ---设置品质
    self.node_list.incubate_speed_root:CustomSetActive(is_egg)
    self.node_list.incubate_quick_root:CustomSetActive(is_egg)
    self.node_list.incubate_release_root:CustomSetActive(not is_egg)
    self.node_list.incubate_owner_root:CustomSetActive(not is_egg)
    self.node_list.item_incubate_text:CustomSetActive(is_egg)
    self.node_list.incubate_quick_red:CustomSetActive(is_egg and incubate_data.can_quick)
    self.node_list.incubate_owner_red:CustomSetActive(incubate_data.status == BEASTS_BORN_STATUS.BORN)
    
    --设置四个按钮下方消耗状态
    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg then
        self.node_list.incubate_speed_const_text.text.text = base_cfg.spend_xianyu

        local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(base_cfg.incubate_item.item_id)
        if item_cfg then
            local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
            self.node_list.incubate_quick_const_icon.image:LoadSprite(bundle, asset)
            local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.incubate_item.item_id)
            local color = item_num >= base_cfg.incubate_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
            self.node_list.incubate_quick_const_text.text.text = ToColorStr(string.format("%s/%s", base_cfg.incubate_item.num, item_num), color) 
        end
    end

    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    if beast_cfg then
        local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
        self.node_list.stable_beasts_color_img.image:LoadSprite(bundle, asset, function()
            self.node_list.stable_beasts_color_img.image:SetNativeSize()
        end)

		local star_res_list = GetStarImgResByStar(beast_cfg.beast_star)
    	for k,v in pairs(self.beasts_stable_star_list) do
        	v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
    	end

        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
        self.node_list.stable_beasts_element_img.image:LoadSprite(bundle, asset, function()
            self.node_list.stable_beasts_element_img.image:SetNativeSize()
        end)
        
        self.node_list.stable_beasts_name.text.text = beast_cfg.beast_name
    end

    local map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
    if map_data then
        if is_egg then
            if map_data.beast_flair_preview then
                for index, bag_flair_cell in ipairs(self.message_flair_list) do
                    if map_data.beast_flair_preview[index] then
                        local flair_data = {}
                        flair_data.preview_data = map_data.beast_flair_preview[index]
                        bag_flair_cell:SetData(flair_data)
                    end
                end
            end
        else
            if server_data.flair_values and map_data.beast_flair_preview then
                for index, bag_flair_cell in ipairs(self.message_flair_list) do
                    local flair_data = {}
                    flair_data.preview_data = map_data.beast_flair_preview[index]

                    if server_data.flair_values[index] then
                        flair_data.flair_value = server_data.flair_values[index]
                        bag_flair_cell:SetData(flair_data)
                    else
                        flair_data.flair_value = server_data.effort_value / 10000
                        bag_flair_cell:SetData(flair_data)    -- 成长值
                    end
                end
            end
        end
    end

    if is_egg then
        local cur_time_stamp = TimeWGCtrl.Instance:GetServerTime()
        local last_time = server_data.finish_timestamp - cur_time_stamp
        if last_time <= 0 then
            ControlBeastsWGData.Instance:SetSingleBreedingInfoForSelf(incubate_data.bag_id)
            self:Flush()
        else
            self:CleanTimeDown()
            self:FlushStatusAndTime(0, last_time)
            CountDownManager.Instance:AddCountDown("item_incubate_time", 
            BindTool.Bind(self.FlushStatusAndTime, self), 
            BindTool.Bind(self.FlushStatusComplete, self), 
            nil, last_time, 1)
        end
    end
end

---刷新倒计时
function ControlBeastsWGView:FlushStatusAndTime(elapse_time, total_time)
    if total_time - elapse_time > 0 then
        if self.node_list.item_incubate_text ~= nil then
            self.node_list.item_incubate_text.text.text = TimeUtil.FormatSecond(total_time - elapse_time)
        end
    end
end

---刷新倒计时
function ControlBeastsWGView:FlushStatusComplete()
    self:CleanTimeDown()
    self:Flush()
end

-- 未孵化背包点击
function ControlBeastsWGView:SelectBeastEggCellCallBack(egg_cell)
    if not egg_cell.data then
        return
    end

    self.select_egg_index = egg_cell.index
    self.select_egg_data = egg_cell.data
    --展示一下详情
    if egg_cell.data and egg_cell.data.server_data then
        local server_data = egg_cell.data.server_data
        local map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)

        self.node_list.no_data:CustomSetActive(server_data.beast_id == -1)
        self.node_list.beasts_bag_incubate_message:CustomSetActive(server_data.beast_id ~= -1)

        if map_data then
            if map_data.beast_flair_preview then
                for index, bag_flair_cell in ipairs(self.bag_flair_list) do
                    if map_data.beast_flair_preview[index] then
                        bag_flair_cell:SetData(map_data.beast_flair_preview[index])
                    end
                end
            end

            if map_data.beast_data then
                local skill_id = map_data.beast_data.skill_id
                --去技能数据类查
                local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, 3)
                if client_cfg then
                    self.node_list.beasts_bag_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
                end

                self.node_list.item_bag_name_text.text.text = map_data.beast_data.beast_name
                self.node_list.item_bag_incubate_text.text.text = string.format(Language.ContralBeasts.Incubate19, TimeUtil.FormatSecond(map_data.beast_data.incubate_time))
            end
        end
    end
end

---------------------------------------------------------------------
function ControlBeastsWGView:FlushStableViewCallBack(param_t)
    self:FlushIncubateList()
    self:FlushBeastsEggBag()
    self:FlushStableBagButtonStatus()
end

---刷新灵兽蛋背包
function ControlBeastsWGView:FlushIncubateList()
    local breeding_slot_list = ControlBeastsWGData.Instance:GetBreedingSlotData()

    if self.incubate_list and breeding_slot_list then
        for index, incubate_cell in ipairs(self.incubate_list) do
            if incubate_cell and breeding_slot_list[index] then
                incubate_cell:SetData(breeding_slot_list[index])
            end
        end
    end

    if self.incubate_index == nil then
        if self.incubate_list and self.incubate_list[1] then
            self:OnSelectIncubateCB(self.incubate_list[1], true, true)
        end
    else
        if self.incubate_list and self.incubate_list[self.incubate_index] then
            self:OnSelectIncubateCB(self.incubate_list[self.incubate_index], true)
        end
    end
end

---刷新灵兽蛋背包
function ControlBeastsWGView:FlushBeastsEggBag()
    local beasts_egg_list = ControlBeastsWGData.Instance:GetBeastsEggList()
    self.beast_egg_list_view:SetDataList(beasts_egg_list)

    -- 默认选中一个可孵化的
    for index, beast_egg_data in ipairs(beasts_egg_list) do
		if beast_egg_data and beast_egg_data.is_have_beast then
            self.select_egg_index = index
            break
		end
	end

    if self.select_egg_index == nil then    --默认选中第一个
        self.select_egg_index = 1
        self.beast_egg_list_view:JumpToIndexAndSelect(self.select_egg_index, 16)
    else
        self.beast_egg_list_view:JumpToIndexAndSelect(self.select_egg_index, 16)
    end
end

--- 设置按钮状态(反着来)
function ControlBeastsWGView:FlushStableBagButtonStatus()
    -- if self.node_list and self.node_list.bag_forward_button then
    --     self.node_list.bag_forward_button:CustomSetActive(self.bag_show_type == BAG_SHOW_TYPE.REVERSE)
    -- end

    if self.node_list and self.node_list.bag_reverse_button then
        self.node_list.bag_reverse_button:CustomSetActive(self.bag_show_type == BAG_SHOW_TYPE.FORWARD)
    end

    if self.node_list and self.node_list.bag_close_button then
        self.node_list.bag_close_button:CustomSetActive(self.bag_show_type == BAG_SHOW_TYPE.FORWARD)
    end
end

--- 播放动画
function ControlBeastsWGView:PlayStableBagPositionTween()
    if not IsNil(self.bag_root_tween) then
        if self.bag_show_type == BAG_SHOW_TYPE.FORWARD then
            self.bag_root_tween:PlayForward()
        else
            self.bag_root_tween:PlayReverse()
        end
    end
end

--- 播放动画2
function ControlBeastsWGView:PlayStableIncubatePositionTween()
    if self.incubate_index == nil then
        return
    end

    local pos_x = 500
    local real_pos_x = pos_x - (300 * (self.incubate_index - 1))

    if self.bag_show_type == BAG_SHOW_TYPE.FORWARD then
        UITween.MoveToShowPanel(GuideModuleName.ControlBeastsView, self.node_list["beasts_mid"], Vector2(-73, -23), Vector2(real_pos_x, -23), 0.5, DG.Tweening.Ease.Linear)
    else
        UITween.MoveToShowPanel(GuideModuleName.ControlBeastsView, self.node_list["beasts_mid"], Vector2(real_pos_x, -23), Vector2(-73, -23), 0.5, DG.Tweening.Ease.Linear)
    end
end

--- 播放孵化详情动画
function ControlBeastsWGView:PlayBeastsMessagePositionTween(is_forward)
    -- self.node_list.beasts_incubate_message:CustomSetActive(is_forward)
    if self.node_list and self.node_list.incubate_have_message then
        self.node_list.incubate_have_message:CustomSetActive(is_forward)
    end

    if self.node_list and self.node_list.incubate_not_have_message then
        self.node_list.incubate_not_have_message:CustomSetActive(not is_forward)
    end

    if not IsNil(self.message_root_tween) then
        if is_forward then
            self.show_message_root_tween = true
            self.message_root_tween:PlayForward()
        else
            self.show_message_root_tween = nil
            self.message_root_tween:PlayReverse()
        end
    end
end

function ControlBeastsWGView:CheckHaveDataAndServerData(aim_data)
    if not aim_data then
        return false
    end

    if not aim_data.server_data or aim_data.server_data.beast_id == -1 then
        return false
    end

    return true
end

-- 展示技能
function ControlBeastsWGView:ShowBeastSkill(server_data, is_nor)
    if not server_data then
        return
    end

    local map_data = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    if map_data then
        local skill_id = map_data.skill_id
        
        if is_nor then
            skill_id = map_data.normal_skill_id
        end
        --去技能数据类查
        local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
        local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(skill_id)
        if client_cfg and beast_cfg then
            local show_data = {
                icon = client_cfg.icon_resource,
                top_text = beast_cfg.skill_name,
                capability = beast_cfg.capability_inc,
                body_text = client_cfg.description,
                skill_level = map_data.beast_star,
                x = 0,
                y = -120,
                set_pos = true,
                hide_next = true,
                is_nor
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end
end

---------------------------------------------------------------------
-- 点击背包展示按钮
function ControlBeastsWGView:PlayStableBagTweenPosition(bag_show_type)
    if self.show_message_root_tween and bag_show_type == BAG_SHOW_TYPE.FORWARD then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.Incubate21)
        return
    end

    if self.bag_show_type == bag_show_type then
        return
    end
    
    self.bag_show_type = bag_show_type
    self:FlushStableBagButtonStatus()
    self:PlayStableBagPositionTween()
    -- self:PlayStableIncubatePositionTween()
end

-- 开始孵化
function ControlBeastsWGView:IncubateBeast()
    if (not self.select_egg_index) or (not self.incubate_index) then
        return
    end

    if self:CheckHaveDataAndServerData(self.incubate_data) then-- 有已经在孵化的幻兽
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.Incubate23)
        return
    end

    ControlBeastsWGCtrl.Instance:SendOperateTypeBreed(self.select_egg_index, self.incubate_index)
end

-- 查看蛋的详情
function ControlBeastsWGView:IncubateMessage()
    if not self:CheckHaveDataAndServerData(self.select_egg_data) then
        return
    end
    TipWGCtrl.Instance:OpenItem({item_id = self.select_egg_data.server_data.beast_id, is_beast = false}, ItemTip.FROM_NORMAL, nil, nil, nil)
end

-- 查看蛋的详情
function ControlBeastsWGView:BeastsBagSkillShow()
    if not self:CheckHaveDataAndServerData(self.select_egg_data) then
        return
    end

    if self.select_egg_data and self.select_egg_data.server_data then
        self:ShowBeastSkill(self.select_egg_data.server_data)
    end
end

-- 加速
function ControlBeastsWGView:IncubateSpeed()
    if not self:CheckHaveDataAndServerData(self.incubate_data) then
        return
    end

    local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg then
        if role_gold < base_cfg.spend_xianyu then
            VipWGCtrl.Instance:OpenTipNoGold()
        else
            ControlBeastsWGCtrl.Instance:SendOperateTypeIncubateSpeed(self.incubate_index)
        end
    end
end

-- 一键孵化
function ControlBeastsWGView:IncubateQuick()
    if not self:CheckHaveDataAndServerData(self.incubate_data) then
        return
    end

    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg then
        local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(base_cfg.incubate_item.item_id)
        if item_cfg then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.incubate_item.item_id)
            if item_num < base_cfg.incubate_item.num then
                TipWGCtrl.Instance:OpenItemTipGetWay({item_id = base_cfg.incubate_item.item_id})
            else
                ControlBeastsWGCtrl.Instance:SendOperateTypeIncubateQuick(self.incubate_index)
            end
        end
    end
end

--重新孵化
function ControlBeastsWGView:IncubateRelease()
    if not self:CheckHaveDataAndServerData(self.incubate_data) then
        return
    end

    ControlBeastsWGCtrl.Instance:OpenBeastStableResetTipsView(self.incubate_data, self.incubate_index)
end

--认主
function ControlBeastsWGView:IncubateOwner()
    if not self:CheckHaveDataAndServerData(self.incubate_data) then
        return
    end

    ControlBeastsWGCtrl.Instance:SendOperateTypeIncubateOwner(self.incubate_index)
end

-- 前往获取
function ControlBeastsWGView:GoToGet()
    self:ChangeToIndex(TabIndex.beasts_contract)
end

-- 快速孵化的物品展示
function ControlBeastsWGView:IncubateQuickItemShow()
    --设置四个按钮下方消耗状态
    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = base_cfg.incubate_item.item_id})
    end
end

----------------------------------------------------------------------
BeastsEggItem = BeastsEggItem or BaseClass(StorgeCell)

function BeastsEggItem:OnFlush()
	StorgeCell.OnFlush(self)

    -- local bundle, asset = ResPath.GetCommonImages("a2_ty_liebiao_bg_xz2")
    -- local size = {width = 110, height = 110,}
    -- self:SetSelectEffectImageRes(bundle, asset,size)
end

function BeastsEggItem:OnClick()
	--当有的礼包需要消耗仙玉时，显示的红点 一天只显示一次
    BaseRender.OnClick(self)
end

-- 设置是否选中
function BeastsEggItem:SetSelect(is_select, item_call_back)
	if self.select_call_back and not item_call_back then
		self.select_call_back(self.index, is_select)
	end

	self.is_select = is_select
	-- ItemCell.SetSelectEffect(self, is_select)	
    ItemCell.SetSelectEffectSp(self, is_select)	
    local bundle, asset = ResPath.GetCommon("a3_ty_xzk4")
	ItemCell.SetSelectSpEffectImageRes(self, bundle, asset)

	self:OnSelectChange(self.is_select)
end

