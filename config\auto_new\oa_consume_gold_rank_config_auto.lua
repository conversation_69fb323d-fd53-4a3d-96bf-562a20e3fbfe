-- Y-运营活动-本服消费榜.xls
local item_table={
[1]={item_id=26130,num=200,is_bind=1},
[2]={item_id=38936,num=1,is_bind=1},
[3]={item_id=26151,num=1,is_bind=1},
[4]={item_id=18703,num=1,is_bind=1},
[5]={item_id=26548,num=1,is_bind=1},
[6]={item_id=26506,num=1,is_bind=1},
[7]={item_id=26130,num=100,is_bind=1},
[8]={item_id=38937,num=1,is_bind=1},
[9]={item_id=26130,num=50,is_bind=1},
[10]={item_id=18702,num=1,is_bind=1},
[11]={item_id=26505,num=1,is_bind=1},
[12]={item_id=26463,num=1,is_bind=1},
[13]={item_id=26130,num=30,is_bind=1},
[14]={item_id=18701,num=1,is_bind=1},
[15]={item_id=26504,num=1,is_bind=1},
[16]={item_id=26459,num=1,is_bind=1},
[17]={item_id=26130,num=300,is_bind=1},
[18]={item_id=38935,num=1,is_bind=1},
[19]={item_id=18704,num=1,is_bind=1},
[20]={item_id=26557,num=1,is_bind=1},
[21]={item_id=26506,num=3,is_bind=1},
[22]={item_id=26344,num=3,is_bind=1},
[23]={item_id=26346,num=3,is_bind=1},
[24]={item_id=22072,num=1,is_bind=1},
[25]={item_id=22623,num=1,is_bind=1},
}

return {
grade={
[1]={min_open_day=1,},
[9]={min_open_day=9,max_open_day=999,grade=2,}
},

grade_meta_table_map={
},
reward={
{},
{reach_value=468000,min_rank=2,max_rank=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{reach_value=420000,min_rank=3,max_rank=3,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{reach_value=360000,min_rank=4,max_rank=10,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12]},},
{reach_value=250000,min_rank=11,max_rank=20,reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

reward_meta_table_map={
[7]=2,	-- depth:1
[8]=3,	-- depth:1
[9]=4,	-- depth:1
[10]=5,	-- depth:1
},
other={
{}
},

other_meta_table_map={
},
jump={
{},
{jump_icon="a3_zjm_icon_xb",jump_path="TreasureHunt#treasurehunt_equip",},
{jump_icon="a3_zjm_icon_hsqy",jump_path="ControlBeastsPrizeDrawWGView",},
{jump_icon="a3_zjm_icon_shangcheng",jump_path="market#shop_limit",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

jump_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
grade_default_table={min_open_day=1,max_open_day=8,grade=1,},

reward_default_table={grade=1,reach_value=500000,min_rank=1,max_rank=1,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[3],[3]=item_table[19],[4]=item_table[20],[5]=item_table[21]},},

other_default_table={end_time=2350,daily_reward={[0]=item_table[22],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25]},},

jump_default_table={grade=1,jump_icon="a3_zjm_icon_zerobuy",jump_path="LayoutZeroBuyView",show_level=1,}

}

