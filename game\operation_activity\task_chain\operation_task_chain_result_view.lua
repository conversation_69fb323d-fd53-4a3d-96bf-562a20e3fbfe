OperationTaskChainResultView = OperationTaskChainResultView or BaseClass(SafeBaseView)

function OperationTaskChainResultView:__init()
	self.view_style = ViewStyle.Half
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_result")
	self:SetMaskBg(true, true)

	self.close_show_time = 10
end

function OperationTaskChainResultView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("task_chain_result_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_result_timer")
	end

    if self.reward_list ~= nil then
    	self.reward_list:DeleteMe()
    	self.reward_list = nil
	end
	
	if CountDownManager.Instance:HasCountDown(self.rwwc_spine_time) then
		CountDownManager.Instance:RemoveCountDown(self.rwwc_spine_time)
		self.rwwc_spine_time = nil
	end

	self.rwwc_ske_graphic = nil

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function OperationTaskChainResultView:LoadCallBack()
	self.node_list.btn_define.button:AddClickListener(BindTool.Bind1(self.OnClickDefine, self))
	self.item_cell = ItemCell.New(self.node_list.root_item)
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.list_reward)
end

function OperationTaskChainResultView:LoadIndexCallBack(index)
	if self.node_list.lose_jiesuan_bg then
		RectTransform.SetSizeDeltaXY(self.node_list.lose_jiesuan_bg.rect, 0, 342)
		self.node_list.lose_title_img.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("jiesuan_task_lose"))
	elseif self.node_list.success_bg then
		self.node_list.win_title_img.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("jiesuan_task_success"))
	end
end

function OperationTaskChainResultView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("task_chain_result_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_result_timer")
	end
end

function OperationTaskChainResultView:Open()
	local info = OperationTaskChainWGData.Instance:GetTaskChainFbResult()
	if info then
		self.node_list.victory:SetActive(info.succ == 1)
		self.node_list.lose:SetActive(not info.succ == 1)
	end
end

function OperationTaskChainResultView:OnClickDefine()
	self:Close()

	local info = OperationTaskChainWGData.Instance:GetTaskChainFbResult()
	if info == nil then
		return
	end

    local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
    if day_index_cfg == nil then
    	return
    end

	local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
	if task_chain_cfg == nil then
		return
	end

	local task_tab = Split(task_chain_cfg.task_ids, "|")
	if task_tab ~= nil and #task_tab > 0 then
		if info.task_idx + 1 ~= #task_tab then
			OperationTaskChainWGCtrl.Instance:SendTaskChainLeaveTaskScene()
		else
			FuBenWGCtrl.Instance:SendLeaveFB()
		end
	end
end

function OperationTaskChainResultView:ShowIndexCallBack(index)
	if CountDownManager.Instance:HasCountDown("task_chain_result_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_result_timer")
	end

	self.close_show_time = 10
	if self.node_list.str_btn then
		self.node_list.str_btn.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainDefineStr, self.close_show_time)
	end
	
    CountDownManager.Instance:AddCountDown("task_chain_result_timer", BindTool.Bind1(self.FlushAutoClick, self), BindTool.Bind(self.CompleteAutoClick, self), nil, self.close_show_time, 1)
end

function OperationTaskChainResultView:OnFlush(param_t, index)
	local info = OperationTaskChainWGData.Instance:GetTaskChainFbResult()
	if info == nil then
		return
	end

	local asset, bundle = ResPath.GetOperationTaskChainF2("score_level_" .. info.level)
	self.node_list.img_grade.image:LoadSprite(asset, bundle, function ()
		self.node_list.img_grade.image:SetNativeSize()
	end)

	local reward_flag = info.have_reward == 1
	self.node_list.str_tip:SetActive(not reward_flag)
	self.node_list.root_reward:SetActive(reward_flag)

	local is_suc = info.succ == 1
	if reward_flag then
		local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(info.reward_array)
		local data_list = {}
		data_list[1] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_EXP, num = info.reward_exp}
		for i=1,#reward_list do
			data_list[i + 1] = reward_list[i]
		end

		self.reward_list:SetDataList(data_list)
	end

	local interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if interface_cfg then
		self.item_cell:SetData({item_id = interface_cfg.item_id, num = info.mingwang})
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(interface_cfg.item_id)
	local item_name = item_cfg and item_cfg.name or ""
	self.node_list.const_name.text.text = item_name .. "："
	self.node_list.str_tip.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainTaskGetMingWang, item_name)
end

function OperationTaskChainResultView:FlushAutoClick(elapse_time, total_time)
	if self.node_list.str_btn then
		self.close_show_time = self.close_show_time - 1
		self.node_list.str_btn.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainDefineStr, self.close_show_time)
	end
end

function OperationTaskChainResultView:CompleteAutoClick()
	self:OnClickDefine()
end