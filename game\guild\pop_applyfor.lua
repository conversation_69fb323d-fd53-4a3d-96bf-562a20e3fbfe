require("game/guild/guild_applyfor_item")
--帮派申请界面
GuildPopApplyfor = GuildPopApplyfor or BaseClass(SafeBaseView)
function GuildPopApplyfor:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
						{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_pop_applyfor")
	self.opt_type = {
		pass = 0,
		refuse = 1,
	}
end

function GuildPopApplyfor:__delete()
	
end

function GuildPopApplyfor:ReleaseCallBack()
	if nil ~= self.apply_list then
		self.apply_list:DeleteMe()
		self.apply_list = nil
	end
	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end
end

function GuildPopApplyfor:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Guild.ApplyforList

	self.pop_alert = Alert.New()
	self:CreateApplyforList()
	self:RegisterAllEvent()
end

-- 注册事件
function GuildPopApplyfor:RegisterAllEvent()	
	self.node_list.btn_paseall.button:AddClickListener(BindTool.Bind2(self.OnConfirmHandler, self,self.opt_type.pass))
	self.node_list.btn_refuseall.button:AddClickListener(BindTool.Bind2(self.OnConfirmHandler, self,self.opt_type.refuse))
end

-- 申请列表操作确认事件
function GuildPopApplyfor:OnConfirmHandler(opt_type)
	if GuildDataConst.GUILD_APPLYFOR_LIST.count > 0 then
		self.pop_alert:Open()
		if self.opt_type.refuse == opt_type then
			self.pop_alert:SetLableString(Language.Guild.RefuseAll)		
		else		
			self.pop_alert:SetLableString(Language.Guild.PassAll)
		end
		self.pop_alert:SetOkFunc(BindTool.Bind2(self.OnApplyforHandler, self, opt_type))
	end	
end

-- 申请列表操作事件
function GuildPopApplyfor:OnApplyforHandler(opt_type)
	local count = GuildDataConst.GUILD_APPLYFOR_LIST.count
	local uid_list = {}
	if 0 == count then
		return
	end
	for i = 1, count do
		table.insert(uid_list, GuildDataConst.GUILD_APPLYFOR_LIST.list[i].uid)
	end
	GuildWGCtrl.Instance:SendGuildApplyforJoinReq(GuildDataConst.GUILDVO.guild_id, opt_type, count, uid_list)
	self:Close()
end

-- 创建邀请列表
function GuildPopApplyfor:CreateApplyforList()
	if nil == self.apply_list then
		self.apply_list = AsyncListView.New(GuildApplyforItem, self.node_list["ph_list"])
		self.apply_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectMemberListItemHandler, self))
	end
	self:FlushApplyforListDatasource()
end

-- 点击某个成员事件
function GuildPopApplyfor:OnSelectMemberListItemHandler(item)
	if nil == item then
		return
	end
	if nil == item.data then
		return
	end
	GuildWGData.Instance:SetGuildMemberSelectIndex(item:GetIndex())
end

-- 刷新邀请列表数据源
function GuildPopApplyfor:FlushApplyforListDatasource()
	local applyfor_list = GuildDataConst.GUILD_APPLYFOR_LIST
	local applyfor_datasource = {}
	for i=1, applyfor_list.count do
		local item = applyfor_list.list[applyfor_list.count - i + 1]
		local datasource = {uid = item.uid, role_name = item.role_name, level = item.level, sex = item.sex, prof = item.prof, 
			vip_type = item.vip_type, vip_level = item.vip_level, capability = item.capability, applyfor_time = item.applyfor_time}
		table.insert(applyfor_datasource, datasource)
	end
	if not IsEmptyTable(applyfor_datasource) then
		self.apply_list:SetDataList(applyfor_datasource)
	end
	self.node_list.btn_paseall:SetActive(#applyfor_datasource > 0)
	self.node_list.btn_refuseall:SetActive(#applyfor_datasource > 0)
	self.node_list.content_bg:SetActive(#applyfor_datasource > 0)

	if not IsEmptyTable(applyfor_datasource) then
		self.node_list.img_apply:SetActive(false)
		self.node_list.ph_list:SetActive(true)
	else
		self.node_list.img_apply:SetActive(true)
		self.node_list.ph_list:SetActive(false)
	end
end

function GuildPopApplyfor:OnFlush()
	self:FlushApplyforListDatasource()
end

function GuildPopApplyfor:CloseCallBack()
	local applyfor_list = GuildDataConst.GUILD_APPLYFOR_LIST
	GuildWGCtrl.Instance:MainuiTipApplyfor(applyfor_list.count > 0 and 1 or 0)
end