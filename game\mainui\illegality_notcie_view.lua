IllegalityNoticeView = IllegalityNoticeView or BaseClass(SafeBaseView)
local STATIC_TIME = 10					--倒计时时间

function IllegalityNoticeView:__init()
    self.view_layer = UiLayer.Disconnect
    self:SetMaskBg(false)

	self:AddViewResource(0, "uis/view/main_ui_prefab", "IllegalityNoticeView")
end

function IllegalityNoticeView:__delete()

end

function IllegalityNoticeView:ReleaseCallBack()

end

function IllegalityNoticeView:CloseCallBack()

end

function IllegalityNoticeView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.IllegalityNoticeView.title_view_name
    XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind(self.OnDisconnect, self))
end

function IllegalityNoticeView:OpenCallBack()
    self:StartCountDown()
end

function IllegalityNoticeView:OnFlush()
    self.node_list.desc_text.text.text =  Language.IllegalityNoticeView.DescStr
end

function IllegalityNoticeView:StopCountDown()
	if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end
end

function IllegalityNoticeView:StartCountDown()
	self:StopCountDown()

    local function time_func(elapse_time, total_time)
        self.node_list["btn_text"].text.text = string.format(Language.IllegalityNoticeView.BtnStr, math.ceil(total_time - elapse_time))
        if elapse_time >= total_time then
            GameNet.Instance:DisconnectGameServer()
            return
        end
    end

    self.static_count_down = CountDown.Instance:AddCountDown(STATIC_TIME, 1, time_func)
end

function IllegalityNoticeView:OnDisconnect()
	GameNet.Instance:DisconnectGameServer()
end