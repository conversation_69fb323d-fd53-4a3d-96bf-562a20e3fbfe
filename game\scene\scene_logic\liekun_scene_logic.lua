LieKunSceneLogic = LieKunSceneLogic or BaseClass(CrossServerSceneLogic)

function LieKunSceneLogic:__init()
	self.door_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE,BindTool.Bind(self.DoorCreate,self))
	self.door_delete_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DELETE,BindTool.Bind(self.DoorDelete,self))
end

function LieKunSceneLogic:__delete()
	if CountDownManager.Instance:HasCountDown("liekun_fb_boss_flush") then
		CountDownManager.Instance:RemoveCountDown("liekun_fb_boss_flush")
	end
	self.before_act_mode = nil

	if nil ~= self.boss_flush_countdown_ui then
		self.boss_flush_countdown_ui:removeFromParent()
		self.boss_flush_countdown_ui = nil
	end

	if self.door_create_event then
		GlobalEventSystem:UnBind(self.door_create_event)
		self.door_create_event = nil
	end

	if self.door_delete_event then
		GlobalEventSystem:UnBind(self.door_delete_event)
		self.door_delete_event = nil
	end

end

function LieKunSceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	self.move_to_gather = false
	-- 打开猎鲲侧栏
	GuildWGCtrl.Instance:OpenLieKunFollow()
	self:OpenActivitySceneCd(ACTIVITY_TYPE.KF_LIEKUN)
	-- MainuiWGCtrl.Instance:SetTaskActive(false)
	local main_role = Scene.Instance:GetMainRole()
	-- self.before_act_mode = main_role:GetVo().attack_mode
	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.GUILD)			--进副本切强制

	self.flush_tip_flag  = true
	self.is_show_door = false
	self.FlushTipEvent = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushTip,self),2)
	-- self.before_act_mode = main_role:GetVo().attack_mode
	-- MainuiWGCtrl.Instance:SendSetAttackMode(GameEnum.ATTACK_MODE_BANGPAI)
end

function LieKunSceneLogic:FlushTip()
	self.flush_tip_flag = true
end

function LieKunSceneLogic:GetFlushTipFlag()
	return self.flush_tip_flag
end

function LieKunSceneLogic:SetFlushTipFlag(falg)
	self.flush_tip_flag = falg
end

function LieKunSceneLogic:Out(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		CrossServerSceneLogic.Out(self)
		-- if self.before_act_mode ~= nil then
		-- 	MainuiWGCtrl.Instance:SendSetAttackMode(self.before_act_mode)
		-- end
	end

	GuildWGCtrl.Instance:CloseLieKunFollow()
	-- MainuiWGCtrl.Instance:SetTaskActive(true)
	ViewManager.Instance:CloseAll()
	self.gathering = false

	GlobalTimerQuest:CancelQuest(self.FlushTipEvent)
	self.FlushTipEvent = nil
	self.is_show_door = false

	-- if self.before_act_mode ~= nil then
	-- 	MainuiWGCtrl.Instance:SendSetAttackMode(self.before_act_mode)
	-- end
end

function LieKunSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)

	if GuajiCache.guaji_type == GuajiType.Auto then
		self:GuaiJiGatherUpdate(now_time, elapse_time)
	end
end
function LieKunSceneLogic:DoorCreate(obj)
	if obj and obj.vo and obj.vo.door_id then
		local doors = Scene.Instance:GetSceneConfig().doors
		if doors and doors[1].id == obj.vo.door_id and not GuildWGData.Instance:GetCrossLieKunDoorActive() then
			local root = obj:GetRoot()
			if root then
				root.gameObject:SetActive(false)
				obj:SetFollowUIState(false)
				self.is_show_door = false
			end
		end
	end
end

function LieKunSceneLogic:DoorDelete(obj)
	if obj and obj.vo and obj.vo.door_id then
		local root = obj:GetRoot()
		root.gameObject:SetActive(true)
		obj:SetFollowUIState(true)
	end
end

function LieKunSceneLogic:ChangeDoorState(data)
	local doors = Scene.Instance:GetObjListByType(SceneObjType.Door)
	if doors and GuildWGData.Instance:GetCrossLieKunDoorActive() then
		for k,v in pairs(doors) do
			local door = v
			local root = door:GetRoot()
			if root then
				root.gameObject:SetActive(true)
				door:SetFollowUIState(true)
				door:AddMoreName(ToColorStr(data,"#7cffb2"))
				self.is_show_door = true
				MapWGCtrl.Instance:FlushMapLocalView()
			end
		end
	end
end

function LieKunSceneLogic:GetShowDoorFlag()
	return self.is_show_door
end

function LieKunSceneLogic:CheckIsOurGuildFall(vo)
	local monster_id = vo.monster_id
	local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
	local index
	for i,v in ipairs(cross_lieKun_info.boss_id) do
		if v.boss_id == monster_id then
			index = i
			break
		end
	end
	if index then
		local main_role = Scene.Instance:GetMainRole()
		local my_guild_id = main_role.vo.guild_id
		if my_guild_id == cross_lieKun_info.guild_id[index] then
			return true
		end
	else
		return true
	end
	return false
end

function LieKunSceneLogic:IsEnemy(target_obj,main_role)
	if target_obj == nil then return false end
	if not target_obj:IsCharacter() then return false end

 --    if GuajiCache.guaji_type == GuajiType.Auto then
 --    	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
 --   		for i,v in pairs(fall_item_list) do
 --       		if v:GetVo().owner_role_id < 0 or v:GetVo().owner_role_id == main_role:GetRoleId() then
 --       			return false
 --       		end
 --   		end
	-- end

	if main_role:IsRealDead() then												-- 自己死亡
		return false, Language.Fight.SelfDead
	end

	if target_obj.IsRealDead and target_obj:IsRealDead() then												-- 目标死亡
		return false, Language.Fight.TargetDead
	end

    local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
    local has_fall = false
    for k, v in pairs(fall_item_list) do
    	if self:CheckIsOurGuildFall(v:GetVo()) then
    		has_fall = true
    		break
    	end
    end

    if target_obj and target_obj:IsInSafeArea() then-- 目标在安全区
		if target_obj and target_obj:IsMonster() and not has_fall then
			return true
		else
       		return false, Language.Fight.TargetInSafe
		end
    end

    if main_role and main_role:IsInSafeArea() then-- 自己在安全区
		if target_obj and target_obj:IsMonster() then
			return true
		else
       		return false, Language.Fight.InSafe
		end
    end

    if target_obj.vo.guild_id == main_role.vo.guild_id then
    	return false,Language.Fight.TargetGuild
    end

	if (target_obj:IsRole() and not target_obj:IsMainRole()) or (target_obj:IsMonster() and not has_fall) then
		return true
	end
	return false
end

function LieKunSceneLogic:GuaiJiGatherUpdate(now_time, elapse_time)
	if GuajiCache.target_obj and LieKunSceneLogic:IsEnemy(GuajiCache.target_obj ,Scene.Instance:GetMainRole()) then return end
	local gather_info = GuildWGData.Instance:GetLieKunGatherInfo()
	if not gather_info then return end
    local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
    local has_fall = false
    for k, v in pairs(fall_item_list) do
    	if self:CheckIsOurGuildFall(v:GetVo()) then
    		has_fall = true
    		break
    	end
    end
    if has_fall then return end
	local gather_list = Scene.Instance:GetGatherList()
	local guild_owner_name = GuildDataConst.GUILDVO.guild_name
	local gather_obj

	for k,v in pairs(gather_list) do
		local name_list = Split(v.vo.param2,"_")
		local num = #name_list[#name_list] + 1
		local name = string.sub(v.vo.param2,1,#v.vo.param2 - num)
		if name == guild_owner_name then
			for n,m in pairs(gather_info) do
				if v.vo.obj_id == m.obj_id and m.times<= 0 then
					gather_obj = v
				end
			end
		end
	end

	if nil ~= gather_obj then
		GuajiWGCtrl.Instance:StopGuaji()
		local scene_id = Scene.Instance:GetSceneId()
		local gjc = GuajiWGCtrl.Instance
		local x, y = gather_obj:GetLogicPos()
		local call_back = BindTool.Bind(self.StartGather,self,gather_obj)
		gjc:SetMoveToPosCallBack(call_back)
		gjc:MoveToPos(scene_id, x, y, 3, nil, nil, nil, call_back)
		self.move_to_gather = true
	end

end

function LieKunSceneLogic:MoveToGatherRange()
	return 3
end

function LieKunSceneLogic:StartGather(gather_obj)
	if gather_obj then
		self.gathering = true
		GuajiWGCtrl.Instance:OnSelectObj(gather_obj, SceneTargetSelectType.SCENE)
	end
end

function LieKunSceneLogic:GetGathering()
	return self.gathering
end

function LieKunSceneLogic:SetGathering(gathering)
	self.gathering = gathering
end

function LieKunSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
			self:SetGuaiJi(GUAI_JI_TYPE.ROLE)
		end
	end

	BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
end

-- 获取挂机打怪的位置
function LieKunSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = nil
    local target_y = nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

-- 是否是挂机打怪的敌人
function LieKunSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function LieKunSceneLogic:GetFbSceneMonsterListCfg( monsters_list_cfg )
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	GuildWGData.Instance:GetLieKunBossListMapCfg(monsters_list)
	return monsters_list, true
end

--获取BOSS
function LieKunSceneLogic:GetFbSceneMonsterBossCfg()
	return GuildWGData.Instance:GetLieKunBossListCfgByZone()
end
