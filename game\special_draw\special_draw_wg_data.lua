SpecialActivityWGData = SpecialActivityWGData or BaseClass()

function SpecialActivityWGData:__init()
	if SpecialActivityWGData.Instance then 
		ErrorLog("[SpecialActivityWGData] Attemp to create a singleton twice !")
	end

	SpecialActivityWGData.Instance = self

    --烟花抽奖2
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_special_draw_auto")
	self.trun_table_mode_cfg = ListToMapList(cfg.mode, "activity_type") --抽奖模式
	self.trun_table_reward_pool_cfg = ListToMapList(cfg.reward_pool, "activity_type", "grade", "activity_day") --奖池
	self.trun_table_other_cfg = ListToMap(cfg.type, "activity_type") --抽奖道具
    self.trun_table_times_reward_cfg = ListToMapList(cfg.times_reward, "activity_type", "grade") --次数奖励
    self.trun_table_baodi_cfg = ListToMapList(cfg.baodi, "activity_type", "grade", "activity_day")

    self.trun_table_result_info = {}
    self.times_reward_flag = {}
    self.draw_ani_state = {}
    self.all_draw_info = {}
	RemindManager.Instance:Register(RemindName.SpecialDrawTrunTable, BindTool.Bind(self.GetDrawRed, self, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1))
    
end


function SpecialActivityWGData:__delete()
	SpecialActivityWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.SpecialDrawTrunTable)
end

function SpecialActivityWGData:SetAllInfo(protocol)
	self.rand_activity_type = protocol.rand_activity_type
	self.grade = protocol.grade
    self.draw_times = protocol.draw_times
    self.times_reward_flag = protocol.times_reward_flag

    self.all_draw_info[self.rand_activity_type] = {
        grade = self.grade,
        draw_times = self.draw_times,
        times_reward_flag = self.times_reward_flag,
    }

    --print_error(self.all_draw_info)
end


function SpecialActivityWGData:GetActivityGrade(act_type)
    return (self.all_draw_info[act_type] or -1).grade or -1
end

-- function SpecialActivityWGData:GetDrawActivityType()
--     return self.rand_activity_type or 0
-- end

--抽奖次数
function SpecialActivityWGData:GetCurDrawTimes(act_type)
    return (self.all_draw_info[act_type] or 0).draw_times or 0
end

function SpecialActivityWGData:GetCurDrawFlag(act_type)
    return (self.all_draw_info[act_type] or {}).times_reward_flag or {}
end

--是否领取
function SpecialActivityWGData:GetTimesRewardIsGet(act_type, seq)
    local reward_flag = self:GetCurDrawFlag(act_type)
    local flag = reward_flag[seq] or 0
    return flag > 0
end

function SpecialActivityWGData:GetDrawAllInfo(act_type)
    return (self.all_draw_info or {})[act_type] or {}
end

function SpecialActivityWGData:SetResultInfo(protocol)
	self.trun_table_result_info.mode = protocol.mode
	self.trun_table_result_info.count = protocol.count
	self.trun_table_result_info.record_list = protocol.record_list
end

--结果信息
function SpecialActivityWGData:GetResultInfo()
	return self.trun_table_result_info
end

--抽奖模式
function SpecialActivityWGData:GetDrawConsumeCfg(act_type)
    --local act_type = self:GetDrawActivityType()
    return self.trun_table_mode_cfg[act_type] or {}
end

--抽奖道具
function SpecialActivityWGData:GetDrawItem(act_type)
    -- local act_type
    -- if act == nil then
    --     act_type = self:GetDrawActivityType()
    -- else
    --     act_type = act
    -- end
    
	return self.trun_table_other_cfg[act_type] or {}
end

--抽奖道具list
function SpecialActivityWGData:GetItemDataChangeList(act_type)
    local cfg = self:GetDrawItem(act_type)
    -- if act == nil then
    --     cfg = self:GetDrawItem()
    -- else
    --     cfg = self:GetDrawItem(act)
    -- end

    local item_change_list = {}
    table.insert(item_change_list, cfg.cost_item_id)

    return item_change_list
end

--主界面红点
function SpecialActivityWGData:GetDrawRed(act_type)
    --print_error(act_type)
    --背包有道具
    if self:GetBagItem(act_type) then
        return 1
    end

    if self:GetFanliRed(act_type) then
        return 1
    end

    return 0
end

function SpecialActivityWGData:GetFanliRed(act_type)
    local draw_time = self:GetCurDrawTimes(act_type)
    local cfg = self:GetDrawTimesRewardCfg(act_type)
    for i, v in ipairs(cfg) do
        if draw_time >= v.need_times then
            if not self:GetTimesRewardIsGet(act_type, v.seq) then
                return true
            end
        else
            return false
        end
    end
end

--是否有道具
function SpecialActivityWGData:GetBagItem(act_type)
    local item_list = self:GetItemDataChangeList(act_type)
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--获取抽奖的选项
function SpecialActivityWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end

--珍稀奖励展示配置
function SpecialActivityWGData:GetGradeInfo(act_type, grade, act_day)
    --local act_type = self:GetDrawActivityType()
    return (((self.trun_table_reward_pool_cfg[act_type] or {})[grade] or {})[act_day]) or {}
end

--珍稀奖励展示
function SpecialActivityWGData:GetRewardShowCfg(act_type)
	local show_list = {}
    local zhenxi_list = {}
	local grade = self:GetActivityGrade(act_type)
    --local act_type = self:GetDrawActivityType()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(act_type)
	--print_error(grade, day)
	local data_list = self:GetGradeInfo(act_type, grade, day)
	if data_list == nil then
		return
	end

    show_list = data_list
	for k, v in pairs(data_list) do
		if v.is_rare == 1 then --珍稀奖励
			table.insert(zhenxi_list, v)
		end
	end

	return show_list, zhenxi_list
end


--抽奖次数奖励
function SpecialActivityWGData:GetDrawTimesRewardCfg(act_type)
    --local act_type = self:GetDrawActivityType()
    local grade = self:GetActivityGrade(act_type)
    --print_error(act_type, grade)
    return (self.trun_table_times_reward_cfg[act_type] or {})[grade] or {}
    
end

--奖池保底
function SpecialActivityWGData:GetDrawBaodiCfg(act_type)
    --local act_type = self:GetDrawActivityType()
    local grade = self:GetActivityGrade(act_type)
    local day = ActivityWGData.Instance:GetActivityCurOpenday(act_type)
    --print_error(grade, day)
    return ((self.trun_table_baodi_cfg[act_type] or {})[grade] or {})[day] or {}
end

--幸运转盘次数显示
function SpecialActivityWGData:GetNextBigRewardTimes()
    local cur_draw_times = self:GetCurDrawTimes(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    local baodi_cfg = self:GetDrawBaodiCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    local next_times = 0

    if baodi_cfg == nil then
        return ""
    end

    --雪薇说抽奖到最大 就显示个空
    if baodi_cfg[#baodi_cfg] then
        if cur_draw_times >= baodi_cfg[#baodi_cfg].times then
            return ""
        end
    end

    for k, v in pairs(baodi_cfg) do
        if cur_draw_times >= v.times then
            next_times = baodi_cfg[k + 1].times
        else 
            next_times = v.times
            break
        end
    end

    return next_times - cur_draw_times
end

function SpecialActivityWGData:GetDrawTimesRewardList(act_type)
    local cfg = self:GetDrawTimesRewardCfg(act_type)
    local list = {}
    if cfg == nil then
        return list
    end

    local max = #cfg
    local len =  5
    local t = {}
    for i = 0, len-1 do
        t = {
            data = cfg[max - i],
        }
        table.insert(list, 1, t)
    end

    local flag
    for i = max - len, 1, -1 do
        flag = self:GetTimesRewardIsGet(act_type, i)
        if not flag then
            t = {
                data = cfg[i],
            }
            table.insert(list, 1, t)
            table.remove(list, len + 1)
        end
    end

    return list
end

--背包道具改变红点
-- function SpecialActivityWGData:RegisterRemindInBag(remind_name)
--     local check_list
--     if remind_name == RemindName.SpecialDrawTrunTable then
--         check_list = self:GetItemDataChangeList(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
--         BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
--         return
--     end
    
-- end

--是否跳过动画对应的延时
function SpecialActivityWGData:GetDelayTime(act_type)
    --是否跳过动画
    if self:GetJumpAni(act_type) then
        return 0
    else
        return 1.5
    end
end


function SpecialActivityWGData:GetJumpAni(act_type)
	return self.draw_ani_state[act_type] or false
end

function SpecialActivityWGData:SetJumpAni(act_type)
	self.draw_ani_state[act_type] = not self.draw_ani_state[act_type] 
end