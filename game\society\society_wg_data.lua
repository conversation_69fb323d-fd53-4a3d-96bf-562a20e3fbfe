SocietyWGData = SocietyWGData or BaseClass()

SocietyDataConst = {
	TABBAR_CONTENT = {
		MAIL_VIEW = 1,
		TEAM_VIEW = 2,
		FRIEND_VIEW = 3,
		ENEMY_VIEW = 4,
	}
}

OUT_OF_TEAM_REASON =
{
	OFT_DISMISS =  0,		-- 解散
	OFT_KICK_OFF = 1,		-- 踢出队伍
	OFT_SECEDE = 2,			-- 退出队伍
	OFT_HEBING = 3, 		-- 因为合并退出队伍
}

TEAM_TYPE =
{
	DEFAULT = 0,					--默认组队
	WUJINJITAN = 1,					--无尽祭坛 组队副本
	DUORENTAFANG = 2,				--多人塔防 组队副本
	MIGONGXIANFU = 3,				--迷宫仙府 组队副本
	MAX = 4,
}

function SocietyWGData:__init()
	if SocietyWGData.Instance then
		ErrorLog("[SocietyWGData]:Attempt to create singleton twice!")
	end
	SocietyWGData.Instance = self

	-- self:InitSpaceData()

	--好友数据
	self.friend_list = {}							-- 好友列表
	self.friend_sorted_list = {}					-- 好友排列列表，用于好友排列，把所有涉及到排列的项添加到此列表中
	self.req_list = {}								-- 好友请求
	self.liaoyiliao_req_list = {}								-- 撩一撩好友请求
	self.real_liaoyiliao_req_list = {}								-- 撩一撩好友请求
	self.auto_addfriend_list = {}					-- 一键加好友列表
	self.friend_list_size = 0
	self.req_list_size = 0
	self.liaoyiliao_req_list_size = 0
	self.real_liaoyiliao_req_list_size = 0
	self.send_addfriend_list = {}					-- 一键加好友发送列表
	self.friend_bless_list = {}   					-- 标识好友祝福的列表，只有有所改变的好友才会在此列表中

	--邮件数据
	self.mail_list = {}								-- 邮件列表
	self.has_no_read_mail = false					-- 是否有未读邮件，上线时服务端通知的
	self.unread_mail_num = 0
	self.is_write_mail = false                      --是否打开了写邮件界面

	self.has_lyl_role_info_init = false 			--近期联系人聊一聊玩家数据初始化

	--队伍数据
	self.team_data = {}								-- 队伍数据
	self.team_data.team_member_list = {}			-- 成员列表
	self.auto_join_team = 0
	self.near_team_list = {}						-- 附近队伍
	self.m_is_in_team = 0
	self.team_is_leader = 0

	self.team_data.teamfb_enter_timestamp = 0                 -- 进入组队副本准备时间戳

	self.team_apply_list = {}							-- 组队申请
	self.team_invite_list = {}							-- 组队邀请
	self.old_team_member_list = {}						-- 存储队友ID

	--仇人数据
	self.enemy_list = {}
	self.enemy_uuid_list = {}

	--祝福信息
	self.bless_times = 0							--已祝福次数
	self.fetch_reward_times = 0						--已领取奖励次数

	self.blessing = {}								-- 好友祝福通知信息
	self.fetch_blessing = {}						-- 好友祝福回赠通知信息
	self.has_select_mail_tab = {}                   -- 选中邮件列表

	self.notify_team_info_change_callback_list = {}

	self.is_not_receive_in_today = 0--今日不在接受该玩家请求

	self.less_all_info = {
		bless_times = 0,
		bless_item_count = 0,
		bless_list = {},
		today_bless_val = 0,
		yesterday_bless_val = 0,
	}												-- 祝福信息

	----------------------捕鱼------------------------------------------

	self.fishpond_all_info = {} 			-- 鱼塘所有信息

	self.fishpond_friend_info = {} 			-- 鱼塘好友信息
	self.fishpond_guild_friend_info = {} 	-- 鱼塘盟友信息
	self.show_pond_uid = 0					-- 当前显示的鱼塘主人id

	self.real_enemy_record_num = 0

	local config = ConfigManager.Instance:GetAutoConfig("lingchibuyu_auto")
	self.other_cfg = config.other[1]
	self.pool_level = config.pool_level
	self.role_level_base_reward = config.role_level_base_reward
	self.fish_type = config.fish_type
	self.fish_level = config.fish_level
	self.extend_capacity = config.extend_capacity
	self.stolen_info_list = {}
	self.all_people_info_list = {}

	self.lyl_unread_list = {}


	self.my_send_gift_score = {}
	self.my_receive_gift_score = {}
	self.intimacy_cfg = ConfigManager.Instance:GetAutoConfig("friendcfg_auto").intimacy
	self.intimacy_buff_cfg = ConfigManager.Instance:GetAutoConfig("friendcfg_auto").team_intimacy_buff

	local no_show_enemy_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1].no_record_kill_scene_list
	self.limit_add_enemy_scene_type_list = {}
	if no_show_enemy_cfg ~= nil then
		local tab = Split(no_show_enemy_cfg, "|")
		for k,v in pairs(tab) do
			local value = tonumber(v)
			self.limit_add_enemy_scene_type_list[value] = 1
		end
	end
	
	-- RemindManager.Instance:Register(RemindName.SocietyFriends, BindTool.Bind(self.GetFirendRedShow, self))
	RemindManager.Instance:Register(RemindName.SocietyFriends2, BindTool.Bind(self.GetFirendRedShow2, self))
	RemindManager.Instance:Register(RemindName.ScoietyMail, BindTool.Bind(self.GetMailRemind, self))

end

function SocietyWGData:__delete()
	self:ClearAllLYLRolePlayerprefsinfo()
	self.notify_team_info_change_callback_list = nil
	self.has_lyl_role_info_init = false
	SocietyWGData.Instance = nil
	self.stolen_info_list = {}
	self.all_people_info_list = {}
	RemindManager.Instance:UnRegister(RemindName.SocietyFriends2)
	RemindManager.Instance:UnRegister(RemindName.ScoietyMail)
end

function SocietyWGData:GetFriendList()
	for k,v in pairs(self.friend_list) do
		v.has_bless = 0
	 	v.bless_me = 0
		v.has_fetch_reward = 0
		v.can_get_reward = 0

		for key,value in pairs(self.friend_bless_list) do
	  		if v.user_id == value.user_id then
	  		    v.has_bless = value.has_bless or 0
	  			v.bless_me = value.bless_me or 0
	  		    v.has_fetch_reward = value.has_fetch_reward or 0
	  		    if v.bless_me == 1 and v.has_fetch_reward == 0 then
	  		    	v.can_get_reward = 1
	  		    end
	  		end
		end
	end
	table.sort(self.friend_list, SortTools.KeyUpperSorters("is_online", "can_get_reward", "intimacy", "level"))
	return self.friend_list
end
function SocietyWGData:GetAfterSortFriendList(uid)
	local friend_list = self:GetFriendList2()
	for k,v in pairs(friend_list) do
		if v.user_id == uid then
			return v
		end
	end
	return nil
end

--------------求婚专用--------------------
function SocietyWGData:GetMarryFriendListNoSex()
	local marry_friend_list = {}
	local my_lover_id = RoleWGData.Instance.role_vo.role_id
	local min_level = MarryWGData.Instance:GetTiQinLimitMinLevel()
	for k,v in pairs(self:GetFriendList()) do
		if v.is_online == 1 and (0 == v.user_lover_id or (my_lover_id ~= 0 and my_lover_id == v.user_lover_id)) then
			table.insert(marry_friend_list, v)
		end
	end
	return marry_friend_list
end

function SocietyWGData:GetMarryFriendList()
	local marry_friend_list = {}
	local role_sex = RoleWGData.Instance.role_vo.sex
	local min_level = MarryWGData.Instance:GetTiQinLimitMinLevel()
	for k,v in pairs(self:GetFriendList()) do
		if v.sex ~= role_sex and v.is_online == 1 and v.level >= min_level and 0 == v.user_lover_id then
			table.insert(marry_friend_list, v)
		end
	end
	return marry_friend_list
end

function SocietyWGData:GetFirstFriend(data_gamename)
	local select_friend = nil
	for k, v in pairs(self:GetFriendList2()) do
		if v.gamename == data_gamename then
			select_friend = v
			break
		end
	end
	return select_friend
end

--结婚好友列表
function SocietyWGData:GetMarryFriendListIncludeLover()
	local tx_friend_list = {} --同性
	local yx_friend_list = {} --异性
	local role_sex = RoleWGData.Instance.role_vo.sex
	local min_level = MarryWGData.Instance:GetTiQinLimitMinLevel()
	for k,v in pairs(self:GetFriendList()) do
		if v.is_online == 1 and v.level >= min_level and 0 == v.user_lover_id then
			if v.sex == role_sex then
				table.insert(tx_friend_list, v)
			end
			
			if v.sex ~= role_sex then
				table.insert(yx_friend_list, v)
			end

		end

	end
	return tx_friend_list, yx_friend_list
end

------------------------------------------

--获取所有好友 (包括离线)
function SocietyWGData:GetFriendList2()
	local if_love = false
	local friend_info_list = __TableCopy(self.friend_list)
	for k,v in pairs(friend_info_list) do
		v.has_bless = 0
	 	v.bless_me = 0
		v.has_fetch_reward = 0
		v.can_get_reward = 0
		v.is_love_id = 0
		v.is_guaji = 0
		v.is_kuafu = 0
		v.un_read_num = 0
		if v.is_online == 2 then
			v.is_guaji = 2
			v.is_online = 0
		elseif v.is_online == 3 then
			v.is_kuafu = 3
			v.is_online = 1
		end

		-- if if_love == false then
		 	if RoleWGData.Instance.role_vo.lover_uid == v.user_id then
		    	v.is_love_id = 1
		    	if_love = true
	    	else
		    	v.is_love_id = 0
		    	if_love = false
	    	end
	    -- end

		for key,value in pairs(self.friend_bless_list) do
	  		if v.user_id == value.user_id then
	  		    v.has_bless = value.has_bless or 0
	  			v.bless_me = value.bless_me or 0
	  		    v.has_fetch_reward = value.has_fetch_reward or 0
	  		    if v.bless_me == 1 and v.has_fetch_reward == 0 then
	  		    	v.can_get_reward = 1
	  		    end
	  		end
		end
		
		--策划要求有信息排在线前面
		 v.un_read_num = ChatWGData.Instance:GetPrivateUnreadMsgNum(v.user_id)
	end

	table.sort(friend_info_list, SortTools.KeyUpperSorters("un_read_num", "is_online","intimacy","level", "is_love_id", "user_id"))--"un_read_num", "is_online", "intimacy", "level",  "is_love_id", "touch_me_total_count"))
	return friend_info_list
end

function SocietyWGData:GetIsMyFriend(user_id)
	local friend_list = self:GetFriendList()
	for k,v in pairs(friend_list) do
		if v.user_id == user_id then
			return true
		end
	end
	return false
end

function SocietyWGData:GetAutoaddfriendList()
	return self.auto_addfriend_list
end

function SocietyWGData:GetReqFriendList()
	return self.req_list
end

function SocietyWGData:GetReqFriendListSize()
	return self.req_list_size
end

function SocietyWGData:GetMailList()
	return self.mail_list
end
function SocietyWGData:GetNewMail(mail_info)
	if nil == mail_info then return end
	local is_new = true
	for k,v in pairs(self.mail_list) do
		if v.mail_index == mail_info.mail_index then
			is_new = false
			self.mail_list[k] = mail_info
		end
	end
	if is_new then
		table.insert(self.mail_list,mail_info)
	end
end
function SocietyWGData:GetMailListSize()
	return #self.mail_list
end

function SocietyWGData:SetHasNoReadMail(value, num)
	self.has_no_read_mail = value
	self.unread_mail_num = num
end

function SocietyWGData:GetTeamList()
	return self.team_data
end

function SocietyWGData:GetTeamIndex()
	return self.team_data.team_index
end

-- 申请入队是否需要验证，0否，1是
function SocietyWGData:GetTeamMustCheck()
	return self.team_data.must_check or 1
end

-- 申是否自由拾取，1否，2是
function SocietyWGData:GetTeamAssignModeCheck()
	return self.team_data.assign_mode or 1
end

-- 自动接受入队邀请，0否，1是
function SocietyWGData:SetAutoJoinTeam(value)
	self.auto_join_team = value
end

-- 自动接受入队邀请，0否，1是
function SocietyWGData:GetAutoJoinTeamCheck()
	return self.auto_join_team or 0
end

function SocietyWGData:GetTargetIsTeamMember(uid)
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	local list = self:GetTeamMemberList()
	for i, v in ipairs(list) do
		if my_uid ~= v.orgin_role_id and uid == v.orgin_role_id then
			return true
		end
	end

	if NewTeamWGData.Instance:GetIsTeamRobort(uid) then --组队副本的机器人
		return true
	end
	
	return false
end

--获取队伍成员列表
function SocietyWGData:GetTeamMemberList()
	local member_list = {}
	for i,v in ipairs(self.team_data.team_member_list) do
		if 1 == v.is_online then
			v.is_empty_data = false
			table.insert(member_list, v)
		end
	end
	for i,v in ipairs(self.team_data.team_member_list) do
		if 1 ~= v.is_online then
			v.is_empty_data = false
			table.insert(member_list, v)
		end
	end
	return member_list
end

--获取队伍除自己外的成员列表
function SocietyWGData:GetTeamOtherMemberList()
	local member_list = {}
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	for i,v in ipairs(self.team_data.team_member_list) do
		if v.orgin_role_id ~= my_uid then
			table.insert(member_list, v)
		end
	end
	return member_list
end

function SocietyWGData:GetTeamLeader()
	local member_list = self:GetTeamMemberList()
	for k,v in pairs(member_list) do
		if v.is_leader == 1 then
			return v
		end
	end
	return nil
end

function SocietyWGData:GetIsLoverOnline()
	local lover_uid = RoleWGData.Instance:GetLoverRoleId()
	if lover_uid == 0 then
		return false
	end

	local info = self:FindFriend(lover_uid)
	if not info then
		return false
	end
	return info.is_online ~= 0
end

function SocietyWGData:SetIsWriteMailBool(bool)
	self.is_write_mail = bool
end

function SocietyWGData:GetIsWriteMailBool()
	return self.is_write_mail
end

function SocietyWGData:GetTeamMemberCount()
	return #self.team_data.team_member_list
end

function SocietyWGData:GetCurTeamMemberNum()
	return self.team_data.member_count
end

function SocietyWGData:GetAllTeamMember()
	return self.team_data.team_member_list
end

function SocietyWGData:GetNearTeamList()
	return self.near_team_list
end

function SocietyWGData:GetIsInTeam()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        return CrossTeamWGData.Instance:GetIsInTeam()
    else
        return self.m_is_in_team
    end
end

function SocietyWGData:GetIsTeamLeader()
	return self.team_is_leader
end

function SocietyWGData:GetTeamMemberInfoByRoleId(role_id)
	for k,v in pairs(self.team_data.team_member_list) do
		if v.role_id == role_id then
			return v
		end
	end
end



function SocietyWGData:GetInviteList()
	return self.team_invite_list
end

function SocietyWGData:GetInviteListSize()
	return #self.team_invite_list
end

--最近联系人
function SocietyWGData:SetConnectorInfo(protocol)
	self.connector_frind_list = protocol.connector_frind_list
	--print_error(protocol.connector_frind_list)
end
function SocietyWGData:GetConnectorInfo()
	return	self.connector_frind_list
end


function SocietyWGData:GetEnemyList()
	table.sort(self.enemy_list, SortTools.KeyUpperSorter("is_online")) --将在线敌人排前面
	--print_error(self.enemy_list)
	return self.enemy_list
end

function SocietyWGData:GetIsHasEnemy()
	return #self.enemy_list > 0
end

function SocietyWGData:GetAddfriendList()
	return self.send_addfriend_list
end

--好友数据操作接口
function SocietyWGData.GetPrivaPrefsKey(key, index)
	return RoleWGData.Instance:InCrossGetOriginUid()..key..index
end

--添加好友
function SocietyWGData:AddFriendToFdList(friend_info)
	if nil ~= friend_info then
		self:RemoveFdFormFdList(friend_info.user_id,true)
		table.insert(self.friend_list, friend_info)
		self.friend_list_size = self.friend_list_size + 1

		for i=1, 10 do
			if PlayerPrefsUtil.HasKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i)) then
				local uid = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i))
				if uid == friend_info.user_id then
					PlayerPrefsUtil.SetInt(SocietyWGData.GetPrivaPrefsKey("friend_isfromlyl", i), 0)
					-- break
				end
			end
		end

		--将撩话添加进客户端私聊缓存
		local lyl_interaction = MarryWGData.Instance:GetLiaoYiLiaoInteraction(friend_info.user_id)
		if lyl_interaction then
			local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
			local other_role_id = lyl_interaction.role_id == main_role_id and lyl_interaction.target_role_id or lyl_interaction.role_id
			local main_role_vo = RoleWGData.Instance:GetRoleVo()
			local other_role_vo = self:GetLYLRoleInfo(other_role_id)
			for k,v in pairs(lyl_interaction.content_ids) do
				if v.is_lyl_harass ~= 1 then
					local role_info = v.sender_role_id == main_role_id and RoleWGData.Instance:GetRoleVo() or other_role_vo
					if role_info then
						local content = SocietyWGData.Instance:GetLYLContentCfg(v.content_id)[1].content
						local msg_info = ChatWGData.CreateMsgInfo()
						msg_info.from_uid = v.sender_role_id
						msg_info.from_cross_uuid = v.sender_role_id
						msg_info.username = role_info.gamename
						msg_info.sex = role_info.sex
						msg_info.camp = role_info.camp
						msg_info.prof = role_info.prof
						msg_info.authority_type = 0
						msg_info.content_type = 0
						msg_info.rank = role_info.rank
						msg_info.level = role_info.level
						msg_info.add_type = 0
						msg_info.vip_level = role_info.vip_level
						msg_info.tuhaojin_color = role_info.tuhaojin_color
						msg_info.bigchatface_status = role_info.bigchatface_status
						msg_info.channel_window_bubble_type = role_info.personalize_window_bubble_type
						msg_info.channel_type = CHANNEL_TYPE.PRIVATE
						msg_info.content = content
						msg_info.city_name = role_info.city_name
						msg_info.msg_timestamp = v.msg_timestamp or TimeWGCtrl.Instance:GetServerTime()
						msg_info.fashion_bubble = role_info.fashion_bubble
						msg_info.fashion_photoframe = role_info.fashion_photoframe
						msg_info.send_time_str = v.msg_timestamp
						ChatWGData.Instance:AddPrivateMsg(other_role_id, msg_info)
					end
				end
			end
		end

		self:SetListNeedRefreshActive(false)
		SocietyWGCtrl.Instance:OpenFriendViewFromAddFriend(friend_info.user_id)
		if not ViewManager.Instance:IsOpenByIndex(GuideModuleName.Society, SocietyView.Tab_F) then
			self:RemoveLYLRoleInfo(friend_info.user_id)
		end
	end
end

--添加请求
function SocietyWGData:AddFriendToReqtList(req_info)
	-- if nil ~= req_info then
	-- 	self:RemoveFdFormReqList(req_info.user_id)
	-- 	table.insert(self.req_list, req_info)
	-- 	self.req_list_size = self.req_list_size + 1
	-- end
	self.req_list = req_info
	self.req_list_size = #self.req_list
	-- self.req_list_size = req_info.applicant_count_list
end

--移除好友信息  add_friend true 表示改变或添加好友 false 删除好友 
function SocietyWGData:RemoveFdFormFdList(user_id,add_friend)
	if nil ~= user_id then
		for k,v in pairs(self.friend_list) do
			if v.user_id == user_id then
				table.remove(self.friend_list, k)
				self.friend_list_size = self.friend_list_size - 1
				ChatWGData.Instance:RemoveCache(user_id)
				ChatWGData.Instance:RemoveMsgCahce(user_id)
				MarryWGData.Instance:RemoveLiaoYiLiaoInteraction(user_id)
				SocietyWGData.Instance:RemoveLYLInfo(user_id,add_friend)
			end
		end
	end
end

--移除请求添加好友信息
function SocietyWGData:RemoveFdFormReqList(user_id)
	-- if nil ~= user_id then
	-- 	for k,v in pairs( self.req_list) do
	-- 		if v.user_id == user_id then
	-- 			table.remove(self.req_list, k)
	-- 			self.req_list_size = self.req_list_size - 1
	-- 			break
	-- 		end
	-- 	end
	-- end
end

--清空所有请求数据
function SocietyWGData:ClearnReqList()
	-- self.req_list = {}
	-- self.req_list_size = 0
end

--撩一撩添加请求
function SocietyWGData:AddLYLFriendToReqtList(req_info)
	local req_info_list = {}
	for k,v in pairs(req_info) do
		local is_black = ChatWGData.Instance:InBlacklist(v.user_id)
		if not is_black then
			table.insert(req_info_list,v)
		end
	end
	self.liaoyiliao_req_list = req_info_list
	self.liaoyiliao_req_list_size = #self.liaoyiliao_req_list
	self.real_liaoyiliao_req_list = req_info_list
	self.real_liaoyiliao_req_list_size = #self.liaoyiliao_req_list

end

function SocietyWGData:GetLYLReqFriendList()
	return self.liaoyiliao_req_list
end

function SocietyWGData:GetLYLReqFriendListSize()
	return self.liaoyiliao_req_list_size
end

function SocietyWGData:ClearLYLReqFriendList()
	self.liaoyiliao_req_list = {}
	self.liaoyiliao_req_list_size = 0
end


-- 获取好友列表(完全没做过数据处理，直接拿服务端返回的)
function SocietyWGData:GetSCFriendList()
	return self.friend_list, self.friend_list_size
end

--设置好友列表
function SocietyWGData:SetFriendList(friend_infos, size)
	if nil == friend_infos then
		return
	end
	self.friend_list = friend_infos
	if nil ~= size and "number" == type(size) and size >= 0 then
		self.friend_list_size = size
	else
		self.friend_list_size = self:GetListSize(self.friend_list)
	end

	if not self.has_lyl_role_info_init then
		SocietyWGData.Instance:UpdateLYLRoleInfo()
		self.has_lyl_role_info_init = true
	end
end

-- 设置随机在线玩家列表
function SocietyWGData:SetAutoaddfriendList(autoadd_list)
	if nil == autoadd_list then
		return
	end

	self.auto_addfriend_list = autoadd_list
	self.send_addfriend_list = {}
	for k,v in pairs(self.auto_addfriend_list) do
		local user_id = v.user_id
		self.send_addfriend_list[user_id] = true
	end
end

--通过user_id查找好友信息
function SocietyWGData:FindFriend(user_id)
	if nil == user_id then
		return nil
	end

	for k,v in pairs(self.friend_list) do
		if v.user_id == user_id then
			return v, k
		end
	end

	return nil
end

--检测是否是好友
function SocietyWGData:CheckIsFriend(user_id)
	if nil == user_id then
		return false
	end

	for k,v in pairs(self.friend_list) do
		if v.user_id == user_id then
			return true
		end
	end

	return false
end

--通过gamename查找好友信息
function SocietyWGData:FindFriendByName(game_name)
	if nil == game_name then
		return nil
	end

	for k,v in pairs(self.friend_list) do
		if v.gamename == game_name then
			return v
		end
	end

	return nil
end

--通过user_id查找请求信息
function SocietyWGData:FindAddReq(user_id)
	if nil == user_id then
		return nil
	end
	for k,v in pairs(self.req_list) do
		if v.user_id == user_id then
			return v
		end
	end

	for k,v in pairs(self.real_liaoyiliao_req_list) do
		if v.user_id == user_id then
			return v
		end
	end
	return nil
end

function SocietyWGData:GetListSize(list)
	if nil == list then
		return 0
	end
	local size = 0
	for k,v in pairs(list) do
		size = size + 1
	end
	return size
end

--邮件接口

--添加邮件
function SocietyWGData:AddMail(mail_info)
	if nil ~= mail_info then
		self:RemoveMail(mail_info.mail_index)
		table.insert(self.mail_list, mail_info)
	end
end

--删除邮件
function SocietyWGData:RemoveMail(mail_index, flag)
	if nil ~= mail_index and "number" == type(mail_index) then
		for k,v in pairs(self.mail_list) do
			if v.mail_index == mail_index then
				table.remove(self.mail_list, k)
				break
			end
		end
	end
end

function SocietyWGData:GetMailRemind()
	local num = 0
	for k,v in pairs(self.mail_list) do
		if 0 == v.mail_status.is_read or v.has_attachment > 0 then
			num = num + 1
		end
	end
	return num
	--return num > self.unread_mail_num and num or self.unread_mail_num
end

--获取已读没有附件邮件
function SocietyWGData:GetAllReadMail()
	local read_mails = {}
	local count = 0				--用于累计，已读取，有附件，的邮件
	local count1 = 0
	for k, v in pairs(self.mail_list) do
		if nil ~= v.mail_status and 0 == v.has_attachment then
			if 1 == v.mail_status.is_read  then
				table.insert(read_mails, v.mail_index)
			end
		end
	end

	return read_mails
end

--获取有附件的邮件
function SocietyWGData:GetAllHaveFujian()
	local attachment_mails = {}
	for k,v in pairs(self.mail_list) do
		if 1 == v.has_attachment then
			table.insert(attachment_mails, v.mail_index)
		end
	end
	return attachment_mails
end

--查找邮件
function SocietyWGData:FindMail(mail_index)
	if nil ~= mail_index then
		for k,v in pairs(self.mail_list) do
			if v.mail_index == mail_index then
				return v , k
			end
		end
	end
	return nil
end

--设置邮件为已读
function SocietyWGData:SetMailRead(mail_index)
	local info = self:FindMail(mail_index)
	if nil ~= info and nil ~= info.mail_status then
		if 1 == info.mail_status.is_read then
			return 0, nil
		end
		info.mail_status.is_read = 1

		if self:GetMailRemind() == 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MAIL, 0)
			RemindManager.Instance:Fire(RemindName.ScoietyMail)
		end
		return 1, info
	end

	return 0, nil
end




--设置是否全选邮件标记
function SocietyWGData:SetMailMark(state)
	self.mail_mark = state
end
function SocietyWGData:GetMailMark()
	return self.mail_mark or false
end
--添加选中的邮件  全选
function SocietyWGData:AddSelectMailList(list)
	self.has_select_mail_tab = list or {}
	--print_error("全选",list)
end
--添加选中的邮件  单个
function SocietyWGData:AddSelectMail( mail_info )
	--print_error(self.has_select_mail_tab)
	for k,v in pairs(self.has_select_mail_tab) do

		if v.mail_index == mail_info.mail_index then
			return
		end
	end
	--print_error("添加邮件 单个")
	table.insert(self.has_select_mail_tab,mail_info)
--	print_error("添加邮件 单个",self.has_select_mail_tab)
end
--检查是否已经被选中的邮件
function SocietyWGData:IsCheckHasSelectMain(mail_index)
	if nil == self.has_select_mail_tab or IsEmptyTable(self.has_select_mail_tab) then
		return false
	end
	for k,v in pairs(self.has_select_mail_tab) do
		if v.mail_index == mail_index then
		return true
		end
	end
	return false
end
function SocietyWGData:SetIsReadMail(mail_index)
	for k,v in pairs(self.has_select_mail_tab) do
		if v.mail_index == mail_index then
			v.mail_status.is_read = 1
		end
	end
end
--移除选中的邮件
function SocietyWGData:RemoveHasSelectMail(mail_index)
	-- if nil == self.has_select_mail_tab or IsEmptyTable(self.has_select_mail_tab) then
	-- 	return
	-- end
	local temp_list = {}
	for k,v in pairs(self.has_select_mail_tab) do
		if v.mail_index ~= mail_index then
			--print_error("移除index",mail_index)
			table.insert(temp_list,v)
		end
	end
	self.has_select_mail_tab = temp_list
end
function SocietyWGData:SetAllSelectMailNoFuJian(protocol_list)
	local table_temp = self:GetAllSelectMail()
	if protocol_list then
		for k1,v1 in pairs(table_temp) do
			for k2,v2 in pairs(protocol_list) do
				if v2 == v1.mail_index then
				v1.mail_status.is_read = 1
				v1.has_attachment = 0
				end
			end
		end
	else
		for k,v in pairs(table_temp) do
			v.mail_status.is_read = 1
			v.has_attachment = 0
		end

	end
	self:AddSelectMailList(table_temp)
--	print_error(table_temp)
	-- body
end
function SocietyWGData:RemoveAllSelectMail()
	self.has_select_mail_tab = {}
end
--获取全部选中的邮件
function SocietyWGData:GetAllSelectMail()
	-- local is_has = self:IsCheckHasSelectMain(self.cur_select_mail_item.mail_index)
	-- if is_has then
	-- else
	-- 	table.insert(self.has_select_mail_tab,self.cur_select_mail_item)
	-- end
	local table_temp = __TableCopy(self.has_select_mail_tab)
	return table_temp  or {}
end
--单击选中后 邮件是否打勾
function SocietyWGData:SetIsDanXuan(bool)
	self.is_dan_xuan_mail = bool
--	print_error(bool)
end
function SocietyWGData:GetIsDanXuan()

	return self.is_dan_xuan_mail or false
end
function SocietyWGData:SetHightmailItem (mail_info)
	--self.cur_select_mail_item = mail_info or {}
	self.cur_select_mail_item = {} or {}
end

function SocietyWGData:GetHightmailItem ()
	return self.cur_select_mail_item or {}
end
function SocietyWGData:SetCurMailItem(mail)
 	self.curmail_info = mail
 end
 function SocietyWGData:GetCurMailItem()
 	return self.curmail_info or {}
 end
--设置邮件为没有附件
function SocietyWGData:SetMailNotHavdFujian(mail_index)
	local mail , k = self:FindMail(mail_index)

	if nil == mail or nil == mail.mail_status then
		return
	end
	mail.has_attachment = 0
	mail.mail_status.is_read = 1
	return mail , k
end

-- 队伍信息
function SocietyWGData:SetTeamData(datas)
	if nil == datas then
		return
	end

	self.team_data.team_index = datas.team_index
	self.team_data.team_leader_index = datas.team_leader_index + 1
	self.team_data.must_check = datas.must_check
	--print_error("入队验证::::: 	", self.team_data.must_check)
	self.team_data.member_count = datas.member_count
	self.team_data.assign_mode = datas.assign_mode
	self.team_data.team_type = datas.team_type
	self.team_data.limit_level = datas.limit_level
	self.team_data.max_level = datas.max_level
	self.team_data.teamfb_enter_timestamp = datas.teamfb_enter_timestamp
	self.team_data.team_member_list = {}
	local my_id = RoleWGData.Instance:InCrossGetOriginUid()--GameVoManager.Instance:GetMainRoleVo().role_id
	local role_type = false
	self.team_is_leader = 0

	if my_id == datas.team_member_list[self.team_data.team_leader_index].role_id then
		self.team_is_leader = 1
		role_type = true
	else
		if self:GetReqTeamListSize() > 0 then
			self:TeamJoinReqClear()
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
		end
    end
    local mine_country_str = "" --自身所在服务器
	for i=1, datas.member_count do
		self.team_data.team_member_list[i] = {}
		for k,v in pairs(datas.team_member_list[i]) do
			self.team_data.team_member_list[i][k] = v
		end

		self.team_data.team_member_list[i].is_visible = role_type

		if false == role_type and my_id == datas.team_member_list[i].role_id then
			self.team_data.team_member_list[i].is_visible = true
		end

		if i == self.team_data.team_leader_index then
			self.team_data.team_member_list[i].is_leader = 1
		else
			self.team_data.team_member_list[i].is_leader = 0
			if false == role_type then
				self.team_data.team_member_list[i].is_leader = 2
			end
        end
        local is_my = my_id == datas.team_member_list[i].role_id
        if is_my then
            mine_country_str = ToLLStr(self.team_data.team_member_list[i].goto_plat_type, self.team_data.team_member_list[i].goto_server_id) --当前所在国
        end
    end
    for k, v in pairs(self.team_data.team_member_list) do
        local local_server_str = ToLLStr(v.goto_plat_type, v.goto_server_id) --当前所在国
        v.is_in_different_country = local_server_str ~= mine_country_str
    end

	if 1 ~=  self.team_data.team_leader_index then
		local temp = self.team_data.team_member_list[self.team_data.team_leader_index]
		self.team_data.team_member_list[self.team_data.team_leader_index] = self.team_data.team_member_list[1]
		self.team_data.team_member_list[1] = temp
	end
	self.m_is_in_team = 1


	for i=#self.old_team_member_list,1,-1 do
		-- local obj = Scene.Instance:GetObjectByRoleId(self.old_team_member_list[i])
		-- if obj ~= nil then
		-- 	obj:LeaveFightState()
		-- 	obj:UpdateNameBoard()
		-- end
		table.remove(self.old_team_member_list, i)
	end

	for k,v in pairs(datas.team_member_list) do
		table.insert(self.old_team_member_list, v.role_id)
		-- local obj = Scene.Instance:GetObjectByRoleId(v.role_id)
		-- if obj ~= nil then
		-- 	obj:EnterFightState()
		-- 	obj:UpdateNameBoard()
		-- end
		NewTeamWGCtrl.Instance:RemoveHeBingItem(v.role_id)
	end

	if self.team_is_leader == 0 then
		NewTeamWGData.Instance:RestHeBing()
	end

	-- 组队数据变化触发回调
    self:TeamDataChange()
    
    NewTeamWGCtrl.Instance:FlushInviteView()
end

-- 附近队伍信息
function SocietyWGData:SetNearTeamData(datas)
	if nil == datas then
		return
	end
	self.near_team_list = {}
	for i=1,datas.count do
		local team = datas.team_list[i]
		if nil == team then
			return
		end
		local item = {}
		item.leader_name = team.leader_name
		item.cur_member_num = team.cur_member_num
		item.team_index = team.team_index
		item.leader_vip_level = team.leader_vip_level
		item.leader_prof = team.leader_prof
		item.leader_camp = team.leader_camp
		item.leader_sex = team.leader_sex
		item.sort_data = team.cur_member_num < 5 and team.cur_member_num or 0
		item.member_uid_list = team.member_uid_list
		self.near_team_list[i] = item
	end
end


function SocietyWGData:IsTeamMember(role_id)
	for k, v in pairs(self.team_data.team_member_list) do
	 	if v.role_id == role_id then
	 		return true
	 	end
	end

	return false
end

function SocietyWGData:IsTeamMemberByUuid(uuid)
	if uuid == nil then
		return false
	end

	for k, v in pairs(self.team_data.team_member_list) do
	 	if v.uuid ~= nil and v.uuid == uuid then
	 		return true
	 	end
	end

	return false
end

function SocietyWGData:IsTeamMemberByUid(role_id)
	for k, v in pairs(self.team_data.team_member_list) do
		if IS_ON_CROSSSERVER then
			if v.orgin_role_id == role_id then
				return true
			end
		else
			if v.role_id == role_id then
				return true
			end
		end
	end

	return false
end

function SocietyWGData:ClearTeamData()
	self.team_data = {}
	self.team_data.team_member_list = {}
	self.team_is_leader = 0
	self.m_is_in_team = 0
	self.team_apply_list = {}

	for i=#self.old_team_member_list,1,-1 do
		table.remove(self.old_team_member_list, i)
	end
end

--获取申请列表
function SocietyWGData:GetReqTeamList()
	return self.team_apply_list
end

--获取申请数量
function SocietyWGData:GetReqTeamListSize()
	return #self.team_apply_list
end

--移除一条申请
function SocietyWGData:RemoveTeamJoinReq(role_id)
	for k,v in pairs(self.team_apply_list) do
		if v.req_role_id == role_id then
			table.remove(self.team_apply_list, k)
			break
		end
	end
	RemindManager.Instance:Fire(RemindName.NewTeam_MyTeam)
end

-- 添加一条申请
function SocietyWGData:TeamAddJoinReq(req_info)
	if nil == req_info then
		return
	end
	local info = {}
	info.req_role_id = req_info.req_role_id
	info.req_role_name = req_info.req_role_name
	info.req_role_camp = req_info.req_role_camp
	info.req_role_prof = req_info.req_role_prof
	info.req_role_sex = req_info.req_role_sex
	info.req_role_vip_level = req_info.req_role_vip_level
	info.req_role_level = req_info.req_role_level
	info.req_role_photoframe = req_info.req_role_photoframe
	info.req_role_capability = req_info.req_role_capability
	info.req_role_relation_flag = req_info.req_role_relation_flag
	self:RemoveTeamJoinReq(info.req_role_id)
	table.insert(self.team_apply_list, info)
end

function SocietyWGData:TeamJoinReqClear()
	self.team_apply_list = {}
	RemindManager.Instance:Fire(RemindName.NewTeam_MyTeam)
end

--邀请
function SocietyWGData:RemoveTeamInviteReq(role_id)
	for k,v in pairs(self.team_invite_list) do
		if v.req_role_id == role_id then
			table.remove(self.team_invite_list, k)
			break
		end
	end
end

-- 组队邀请SC
function SocietyWGData:TeamAddInviteReq(invite_info)
	if nil == invite_info then
		return
	end
	local info = {}
	info.req_role_id = invite_info.inviter
	info.req_role_name = invite_info.inviter_name
	info.req_role_camp = invite_info.inviter_camp
	info.req_role_prof = invite_info.inviter_prof
	info.req_role_sex = invite_info.inviter_sex
	info.inviter_vip_level = invite_info.inviter_vip_level
	info.req_role_level = invite_info.inviter_level
	info.team_type = invite_info.team_type
    info.teamfb_mode = invite_info.teamfb_mode
    info.team_min_level = invite_info.team_min_level
    info.team_max_level = invite_info.team_max_level
    info.inviter_level = invite_info.inviter_level
    info.inviter_relation_flag = invite_info.inviter_relation_flag
	self:RemoveTeamInviteReq(info.req_role_id)
	while(#self.team_invite_list >= 20)
	do
		local team_invite = table.remove(self.team_invite_list, 1)
		SocietyWGCtrl.Instance:SendInviteUserTransmitRet(team_invite.req_role_id, 1)
		SocietyWGCtrl.Instance:DeleteReq(team_invite.req_role_id)
	end
	table.insert(self.team_invite_list, info)
end

function SocietyWGData:TeamInviteReqClear()
	self.team_invite_list = {}
end

function SocietyWGData:SynEnemyRecordRealNum()
	self.real_enemy_record_num = self.enemy_record_count or 0
end

function SocietyWGData:GetIsHasNewEnemyRecord()
	local is_has = false
	if self.enemy_record_count ~= nil and self.enemy_record_count ~= self.real_enemy_record_num then
		is_has = true
	end

	return is_has
end

function SocietyWGData:SetEnemyRecordData(protocol)
	self.enemy_record_count = protocol.count
	self.enemy_record_list = protocol.role_list
end

function SocietyWGData:GetEnemyRecordInfo()
	return self.enemy_record_count, self.enemy_record_list
end

function SocietyWGData:SetEnemyData(protocol)
	if nil ~= protocol and nil ~= protocol.enemy_list then
		self.enemy_uuid_list = {}

		local datas = {}
		for i=1,protocol.count do
			datas[i] = {}
		  	datas[i].gamename = protocol.enemy_list[i].gamename
		    datas[i].uuid = protocol.enemy_list[i].uuid
		    datas[i].user_id = protocol.enemy_list[i].user_id
		    datas[i].camp = protocol.enemy_list[i].camp
            datas[i].fashion_photoframe = protocol.enemy_list[i].fashion_photoframe
		    datas[i].sex = protocol.enemy_list[i].sex
		    datas[i].prof = protocol.enemy_list[i].prof
		    datas[i].level = protocol.enemy_list[i].level
		    datas[i].vip_level = protocol.enemy_list[i].vip_level
		    datas[i].is_online = protocol.enemy_list[i].is_online
			datas[i].capability = protocol.enemy_list[i].capability
			datas[i].plat_type = protocol.enemy_list[i].plat_type
			datas[i].server_id = protocol.enemy_list[i].server_id
			datas[i].shield_vip_flag = protocol.enemy_list[i].shield_vip_flag

			self.enemy_uuid_list[ToLLStr(datas[i].plat_type, datas[i].user_id)] = 1
		end
		self.enemy_list = datas
	end
end

function SocietyWGData:UpdateEnemyData(protocol)
	local enemy_info = protocol.enemyinfo
	self:RemoveEnemy(enemy_info.uuid)
	local data = {}
    data.fashion_photoframe = enemy_info.fashion_photoframe
    data.uuid = enemy_info.uuid
    data.gamename = enemy_info.gamename
    data.camp = enemy_info.camp
    data.sex = enemy_info.sex
    data.prof = enemy_info.prof
    data.level = enemy_info.level
    data.vip_level = enemy_info.vip_level
    data.is_online = enemy_info.is_online
	data.capability = enemy_info.capability
	data.user_id = enemy_info.user_id
	data.plat_type = enemy_info.plat_type
	data.server_id = enemy_info.server_id
	data.shield_vip_flag = enemy_info.shield_vip_flag
	self.enemy_uuid_list[ToLLStr(data.plat_type, data.user_id)] = 1

	table.insert(self.enemy_list, data)
end

function SocietyWGData:RemoveEnemy(uuid)
	if nil == self.enemy_list or nil == uuid then
		return
	end
	for k,v in pairs(self.enemy_list) do
		if v.uuid == uuid then
			table.remove(self.enemy_list, k)
			self.enemy_uuid_list[ToLLStr(v.plat_type, v.user_id)] = nil
			break
		end
	end
end

function SocietyWGData:CheckisRoleEnemy(plat_type, user_id)
	if plat_type ~= nil and user_id ~= nil then
		return self.enemy_uuid_list[ToLLStr(plat_type, user_id)] ~= nil
	else
		return false
	end
end

function SocietyWGData:GetIsHaveUnreadChongZhiMail()
	for k,v in pairs(self.mail_list) do
		if v.mail_status.kind == 4 and (v.has_attachment == 1 or v.mail_status.is_read == 0) then
			return true
		end
	end
	return false
end

--设置好友祝福列表
function SocietyWGData:SetBlessList(bless_infos)
	self.friend_bless_list = bless_infos or {}
	-- ListDataSrcMgr.Reflesh(self:GetFriendList())
end

--更改好友祝福列表
function SocietyWGData:ChangeBlessList(bless_change_info)
	if nil == bless_change_info then
		return
	end

	local find_element = false

	for k,v in pairs(self.friend_bless_list) do
		if v.user_id == bless_change_info.user_id then
			v.has_bless = bless_change_info.has_bless or 0
			v.bless_me = bless_change_info.bless_me or 0
			v.has_fetch_reward = bless_change_info.has_fetch_reward or 0
			find_element = true
		end
	end

	if not find_element then
		table.insert(self.friend_bless_list,bless_change_info)
	end
	-- ListDataSrcMgr.Reflesh(self:GetFriendList())
end

--删除好友祝福列表中的部分信息
function SocietyWGData:DeleteBlessList(bless_change_info)
	if nil == bless_change_info then
		return
	end

	for k,v in pairs(self.friend_bless_list) do
		if v.user_id == bless_change_info.user_id then
			table.remove(self.friend_bless_list,k)
		end
	end

	-- ListDataSrcMgr.Reflesh(self:GetFriendList())
end

--查看该好友的祝福信息状态
function SocietyWGData:CheckBlessInfo(target_id)
	if nil == target_id then
		return
	end
	for k,v in pairs(self.friend_bless_list) do
		if v.user_id == target_id then
			return v.has_bless or 0,v.bless_me or 0,v.has_fetch_reward or 0
		end
	end
	return 0,0,0
end

function SocietyWGData:GetBlessRemind()
	local count = 0
	for k,v in pairs(self.friend_bless_list) do
		if v.bless_me == 1 and v.has_fetch_reward == 0 then
			count = count + 1
		end
	end
	return count
end

function SocietyWGData:GetBlessTimesData()
	return self.bless_times,self.fetch_reward_times
end

function SocietyWGData:SetBlessTimesData(bless_times,fetch_reward_times)
	self.bless_times = bless_times or 0
	self.fetch_reward_times = fetch_reward_times or 0
end

function SocietyWGData:IsBlessTimesFull()
	local config_bless =  ConfigManager.Instance:GetAutoConfig("friendcfg_auto").bless[1] or {}
	local fetch_reward_times = self.fetch_reward_times or 0
	local friend_bless_reward_tili = config_bless.friend_bless_reward_tili or 5
	local friend_bless_reward_max_count = config_bless.friend_bless_reward_max_count or 20
	if (fetch_reward_times * friend_bless_reward_tili) == (friend_bless_reward_tili * friend_bless_reward_max_count) then
		return true
	end
	return false
end



----------------------------------------------------
--保存好友祝福通知信息
function SocietyWGData:SetFriendblessNotice(protocol)
	-- 好友升级达到要求通知
	if protocol.notice_type == FRIENDBLESS_NOTICE.UPLEVEL then
		local t = self.blessing[protocol.operate_role_id]
		if nil == t then
			t = {}
			t.notice_type = protocol.notice_type
			t.operate_role_id = protocol.operate_role_id
			t.operate_role_name = protocol.operate_role_name
			t.param1 = protocol.param1
			self.blessing[protocol.operate_role_id] = t
		end

		t.param1 = protocol.param1

	-- 收到被祝福通知
	elseif protocol.notice_type == FRIENDBLESS_NOTICE.BEBLESSED then
		local t = self.fetch_blessing[protocol.operate_role_id]
		if nil == t then
			t = {}
			t.notice_type = protocol.notice_type
			t.operate_role_id = protocol.operate_role_id
			t.operate_role_name = protocol.operate_role_name
			t.param1 = protocol.param1

			self.fetch_blessing[protocol.operate_role_id] = t
		end

		t.param1 = protocol.param1
		t.is_fetched = 0
		t.is_presented = 0

	-- 单次祝福成功	(参数1:值为1时代表礼物已赠送) -- 删除列表中对应记录
	elseif protocol.notice_type == FRIENDBLESS_NOTICE.BLESS_SUCCESS then
		self.blessing[protocol.operate_role_id] = nil

		if next(self.blessing) == nil then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BLESSING, 0)
			SocietyWGCtrl.Instance:CloseFriendblessPanel()
		end

	-- 领取经验成功 领取按钮状态变化
	elseif protocol.notice_type == FRIENDBLESS_NOTICE.FETCH_SUCCESS then		-- 领取经验成功
		local t = self.fetch_blessing[protocol.operate_role_id]
		if nil ~= t then
			t.is_fetched = 1
		end

	-- 赠送礼物成功 赠送按钮状态变化
	elseif protocol.notice_type == FRIENDBLESS_NOTICE.PRESENT_GIFT_SUCCESS then -- 赠送礼物成功
		local t = self.fetch_blessing[protocol.operate_role_id]
		if nil ~= t then
			t.is_presented = 1
		end
	end

	-- 领取祝福经验与赠送礼物都操作后
	if protocol.notice_type == FRIENDBLESS_NOTICE.FETCH_SUCCESS or
		protocol.notice_type == FRIENDBLESS_NOTICE.PRESENT_GIFT_SUCCESS then
		local t = self.fetch_blessing[protocol.operate_role_id]
		if nil ~= t and 1 == t.is_fetched and 1 == t.is_presented then
			self.fetch_blessing[protocol.operate_role_id] = nil
		end
		if next(self.fetch_blessing) == nil then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REBATE, 0)
			SocietyWGCtrl.Instance:CloseFriendReceivePanel()
		end
	end
end

--获取好友祝福通知信息
function SocietyWGData:GetBlessingNotice()
	local blessing = {}
	local i = 1
	for k,v in pairs(self.blessing) do
		blessing[i] = v
		i = i + 1
	end
	return blessing
end

--设置好友祝福通知信息
function SocietyWGData:SetBlessingNotice()
	self.blessing = {}
	SysMsgWGCtrl.Instance:ErrorRemind(Language.FriendsBlessing.FriendsReturnSuccessTis)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BLESSING, 0)
	SocietyWGCtrl.Instance:CloseFriendblessPanel()
end

--设置好友回赠通知信息
function SocietyWGData:GetReceiveNotice()
	local fetch_blessing = {}
	local i = 1
	for k,v in pairs(self.fetch_blessing) do
		fetch_blessing[i] = v
		i = i + 1
	end
	return fetch_blessing
end



-- 保存好友祝福次数
function SocietyWGData:SetFriendblessTimes(num)
	local day_bless_cfg = ConfigManager.Instance:GetAutoConfig("friendblessconfig_auto").other[1]
	if day_bless_cfg then
		self.friendbless_times = day_bless_cfg.max_bless_daytimes - num
	end
end

-- 获取好友祝福次数
function SocietyWGData:GetFriendblessTimes()
	return self.friendbless_times
end

-- 保存好友祝福领取奖励次数
function SocietyWGData:SetFetchFriendblessTimes(num)
	local day_friendbless_cfg = ConfigManager.Instance:GetAutoConfig("friendblessconfig_auto").other[1]
	if day_friendbless_cfg then
		self.fetch_friendbless_times = day_friendbless_cfg.max_beblessed_daytimes - num
	end
end

-- 获取好友祝福领取奖励次数
function SocietyWGData:GetFetchFriendblessTimes()
	return self.fetch_friendbless_times
end


--获取按钮元宝礼包
function SocietyWGData:GetGiftById(id)
	local gift_cfg = ConfigManager.Instance:GetAutoConfig("friendblessconfig_auto").gift
	for k,v in pairs(gift_cfg) do
		if id == v.id then
			return v
		end
	end
end

--获取奖励信息
function SocietyWGData:GetRewardInfoCfg()
	local reward_cfg = ConfigManager.Instance:GetAutoConfig("friendblessconfig_auto").other[1]
	if reward_cfg then
		return reward_cfg
	end
end

function SocietyWGData:SetReceiveNotice()
	local fetch_blessing = {}
	for k,v in pairs(self.fetch_blessing) do
		if v.is_fetched ~= 1 then
			fetch_blessing[k] = v
		end
	end
	self.fetch_blessing = {}
	self.fetch_blessing = fetch_blessing
	if next(self.fetch_blessing) == nil then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REBATE, 0)
	end
end

function SocietyWGData:TisReceivBool()
	for k,v in pairs(self.fetch_blessing) do
		if v.is_fetched == 1 then
			return true
		end
	end
end

--------------------------------------------------------------------------
-- 注册组队回调
function SocietyWGData:NotifyTeamChangeCallBack(callback)
	self.notify_team_info_change_callback_list[#self.notify_team_info_change_callback_list + 1] = callback
end

-- 组队数据变化触发回调
function SocietyWGData:TeamDataChange()
	for k,v in pairs(self.notify_team_info_change_callback_list) do
		if v ~= nil then
			v()
		end
	end
end


-- 获取祝福配置
function SocietyWGData:GetNewfriendblessCfg(value)
	local bless_info = ConfigManager.Instance:GetAutoConfig("newfriendblesscfg_auto").friendbless_addition
	local cur_info = {add_hp = 0, add_gongji = 0, bless_val = 0, dailytask_reward_add_extra_coin = 0}
	local add_info = {add_hp = 0, add_gongji = 0, bless_val = 15, dailytask_reward_add_extra_coin = 0}

	for i = 1, #bless_info do
		local add_i = i + 1 <= #bless_info and i + 1 or #bless_info

		if value >= bless_info[#bless_info].bless_val then
			cur_info = bless_info[#bless_info]
			add_info = bless_info[#bless_info]
			return {cur_info,add_info}
		elseif value < bless_info[1].bless_val then
			add_info = bless_info[1]
			return {cur_info,add_info}
		elseif value >= bless_info[i].bless_val and value < bless_info[add_i].bless_val then
			cur_info = bless_info[i]
			add_info = bless_info[add_i]
			return {cur_info,add_info}
		end
	end
	return {cur_info,add_info}
end

--祝福所有信息
function SocietyWGData:FriendBlessAllInfo(protocol)

	self.less_all_info.bless_times = protocol.bless_times
	self.less_all_info.bless_item_count = protocol.bless_item_count
	self.less_all_info.bless_list = protocol.bless_list

	self.less_all_info.today_bless_val = protocol.today_bless_val							--自己当天祝福值
	self.less_all_info.yesterday_bless_val = protocol.yesterday_bless_val					--昨天的祝福值

end

--更改时获得更改的祝福信息
function SocietyWGData:FriendBlessChangeInfo(protocol)
	-- self.notify_reason = protocol.notify_reason
	-- self.bless_times = protocol.bless_times
	local bless_change_info = protocol.bless_change_info
	self.less_all_info.today_bless_val = bless_change_info.friend_bless

	local t = self.less_all_info.bless_list[bless_change_info.user_id]
	if nil == t then
		t = bless_change_info
		self.less_all_info.bless_list[bless_change_info.user_id] = t
	end
	t.has_bless = bless_change_info.has_bless
	t.bless_me = bless_change_info.bless_me

end

function SocietyWGData:GetLessAllInfo()
	return self.less_all_info
end

function SocietyWGData:IsMaxBlessValue()
	local max_value = ConfigManager.Instance:GetAutoConfig("newfriendblesscfg_auto").other[1].max_bless
	return self.less_all_info.today_bless_val >= max_value
end

-- 获取当前好友的祝福信息
function SocietyWGData:GetBlessChangeInfo(user_id)
	return self.less_all_info.bless_list[user_id]
end

function SocietyWGData:GetTeamFbEnterTimeStamp()
	return self.team_data.teamfb_enter_timestamp
end

-----------------捕鱼------------------------------
SocietyWGData.GetInfoType = {
	AllInfo = 0,			--详细信息
	FishsInfo = 1,			--放养信息
	FriendInfo = 2, 		--好友简要信息
	GuildFriendInfo = 3, 	--盟友简要信息
	ServerAllInfo = 4,      --全服简要信息
	StealRecord = 5,        --偷鱼记录
}
SocietyWGData.GuardFishVo = {	--守卫鱼类型
	fish_objid = -1,
	fish_type = 1,
	raise_timestamp = 0,
	is_buy_boot = 0,
}

-- 设置鱼池所有信息
function SocietyWGData:SetFishpondAllInfo(info)
	if nil == info then return end
	if nil == self.fishpond_all_info[info.normal_info.owner_uid] then
		self.fishpond_all_info[info.normal_info.owner_uid] = {}
	end
	local all_info = self.fishpond_all_info[info.normal_info.owner_uid]

	all_info.normal_info = info.normal_info 						--基本信息
	all_info.day_raise_record_list = info.day_raise_record_list 	--各个鱼种养殖量
end

-- 获取鱼池所有信息
function SocietyWGData:GetFishpondAllInfo(uid)
	if nil == uid then return end
	--print_error(self.fishpond_all_info)
	return self.fishpond_all_info[uid]
end

-- 设置鱼塘基本信息
function SocietyWGData:SetFishpondNormalInfo(info)
	if nil == info then return end

	if nil == self.fishpond_all_info[info.normal_info.owner_uid] then
		self.fishpond_all_info[info.normal_info.owner_uid] = {}
	end
	self.fishpond_all_info[info.normal_info.owner_uid].normal_info = info.normal_info
end

-- 获取鱼塘基本信息
function SocietyWGData:GetFishpondNormalInfo(uid)
	if nil == uid then return end

	if self.fishpond_all_info[uid] then
		return self.fishpond_all_info[uid].normal_info
	end
	return nil
end

-- 设置鱼塘中所有鱼的信息
function SocietyWGData:SetFishpondFishInfo(info)
	if nil == info then return end

	if nil == self.fishpond_all_info[info.owner_uid] then
		self.fishpond_all_info[info.owner_uid] = {}
	end
	--print_error(info.raise_list)
	self.fishpond_all_info[info.owner_uid].raise_list = info.raise_list
end

-- 获取鱼塘中所有鱼的信息
function SocietyWGData:GetFishpondFishInfo(uid)
	if nil == uid then return end

	if self.fishpond_all_info[uid] then
		return self.fishpond_all_info[uid].raise_list
	end
	return nil
end

--jump condition: 鱼塘中鱼数量>0
function SocietyWGData:GetIsNeedJumpGuideFish()
	local role_id = RoleWGData.Instance.role_vo.role_id
	local list = self:GetFishpondFishInfo(role_id)
	if nil == list then return false end
	return #list > 0
end

-- 获得鱼塘中所有鱼的信息(过滤掉了守卫鱼)
function SocietyWGData:GetFishpondFishInfoNoGuard(uid)
	if nil == uid then return end
	local fishpond_all_info_no_guard = {}
	local i = 1

	if self.fishpond_all_info[uid] then
		local local_list = self:GetFishpondAllInfo(uid)
		--print_error("鱼列表",uid,self.fishpond_all_info[uid].raise_list,"9999999999999999999999",self.fishpond_all_info[uid])
		for k,v in pairs(local_list.raise_list) do
			--print_error(v)
			if v.fish_type ~= SocietyWGData.GuardFishVo.fish_type then --当鱼不是守卫鱼的时候，添加
				fishpond_all_info_no_guard[i] = v
				i = i + 1
			end
		end
	end
	return fishpond_all_info_no_guard
end

-- 获取鱼塘中各个鱼种养殖量信息
function SocietyWGData:GetFishpondRecordInfo(uid)
	if nil == uid then return end

	if self.fishpond_all_info[uid] then
		return self.fishpond_all_info[uid].day_raise_record_list
	end
	return nil
end

-- 设置好友列表
function SocietyWGData:SetFishPoolFriendList(info)
	if nil == info then return end
	self.fishpond_friend_info = info
end

-- 获取好友列表
function SocietyWGData:GetFishpondFriendList()
	return self.fishpond_friend_info
end

-- 设置盟友列表
function SocietyWGData:SetGuildFriendList(info)
	if nil == info then return end
	self.fishpond_guild_friend_info = info
end

-- 获取盟友列表
function SocietyWGData:GetGuildFriendList()
	return self.fishpond_guild_friend_info
end

-- 是否当前鱼塘
function SocietyWGData:IsShowPond(uid)
	if uid and 0 < uid and uid == self.show_pond_uid then
		return true
	end
	return false
end

-- 当前鱼塘为自己的鱼塘
function SocietyWGData:CurIsOwnPond()
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	--print_error(role_id,self.show_pond_uid)
	if role_id > 0 and role_id == self.show_pond_uid then
		return true
	else
		return false
	end
end

-- 显示鱼塘改变
function SocietyWGData:ChangeShowPond(uid)
	if nil == uid then return end
	self.show_pond_uid = uid
end

-- 获取当前鱼塘Uid
function SocietyWGData:GetShowPond()
	return self.show_pond_uid
end

-- 通关等级获取鱼塘等级配置
function SocietyWGData:GetFishpondCfgByLv(lv)
	if nil == lv then return end
	return self.pool_level[lv]
end

-- 判断鱼塘等级是不是最高等级
function SocietyWGData:IsFishpondMaxLv(lv)
	if nil == lv then return false end
	return #self.pool_level == lv
end

-- 获取扩展格子需求配置
function SocietyWGData:GetExtendCapacityByNum(gird_num)
	if nil == gird_num then return end
	for k, v in pairs(self.extend_capacity) do
		if v and v.extend_grid == gird_num then
			return v
		end
	end
	return nil
end

-- 获取当前养殖非守卫鱼数量
function SocietyWGData:GetRewardFishNum(uid)
	local num = 0
	local fishs_info = self:GetFishpondFishInfo(uid) or {}
	for k, v in pairs(fishs_info) do
		if v.fish_type ~= SocietyWGData.GuardFishVo.fish_type then
			num = num + 1
		end
	end
	return num
end

-- 通过鱼的类型获取鱼的配置
function SocietyWGData:GetFishCfgByType(type)
	return self.fish_type[type]
end

-- 通过鱼的类型和养殖时间获取获取鱼的等级配置
function SocietyWGData:GetFishLvCfgByTypeAndTime(fish_type, time)
	if nil == fish_type or nil == time then return end
	local cfg = nil
	local time_long = math.max(TimeWGCtrl.Instance:GetServerTime() - time, 0)
	for k, v in pairs(self.fish_level) do
		if v and v.fish_type == fish_type and v.need_time_s <= time_long and
		 (nil == cfg or cfg.need_time_s < v.need_time_s) then
		 cfg = v
		end
	end
	return cfg
end

-- 通过鱼的类型和等级获取获取鱼的等级配置
function SocietyWGData:GetFishLvCfgByTypeAndLv(fish_type, lv)
	if nil == fish_type or nil == lv then return end
	local cfg = nil
	for k, v in pairs(self.fish_level) do
		if v and v.fish_type == fish_type and v.fish_grade == lv then
			cfg = v
		end
	end
	return cfg
end

-- -- 通过类型获取收获时间(最大等级的收获时间)
-- function SocietyWGData:GetCanHarvestTimeByType(fish_type)
-- 	for i = 1, #self.fish_level do
-- 		local cfg = self.fish_level[i]
-- 		if cfg and cfg.fish_type == fish_type and 1 == cfg.can_harvest then
-- 			local cfg2 = self.fish_level[i-1]
-- 			if cfg2 and cfg2.fish_type == fish_type and 0 == cfg2.can_harvest then
-- 				return cfg.need_time_s
-- 			end
-- 		end
-- 	end
-- 	return 0
-- end


-- 通过类型获取收获时间(最大等级的收获时间)
function SocietyWGData:GetCanHarvestTimeByType(fish_type)
	local fish_max_level = 0
	for i,v in pairs(self.fish_type) do
		if v.fish_type == fish_type then
			fish_max_level = v.max_level
		end
	end
	for i,v in pairs(self.fish_level) do
		if v.fish_type == fish_type and v.fish_grade == fish_max_level then
			return v.need_time_s
		end
	end
	return 0
end

-- 通过鱼的类型获取获取鱼的最大等级
function SocietyWGData:GetFishMaxLvByType(fish_type)
	local max_lv = 0
	for k, v in pairs(self.fish_level) do
		if v and v.fish_type == fish_type and v.fish_grade > max_lv  then
			max_lv = v.fish_grade
		end
	end
	return max_lv
end

-- 通过等级获取奖励配置
function SocietyWGData:GetRewardCfgByLv(lv)
	for i = 1, #self.role_level_base_reward do
		local t = self.role_level_base_reward[i]
		local t1 = self.role_level_base_reward[i + 1]
		if t1 == nil then
			t1 = t
		end

		if lv == t.role_level or lv > t.role_level and lv < t1.role_level then
			return self.role_level_base_reward[i]
		end
	end
	return nil
end

-- 过滤所有可收获鱼信息
function SocietyWGData:FilterCanGainFishData(info)
	local data = {}
	for k, v in pairs(info) do
		if v and SocietyWGData.GuardFishVo.fish_type ~= v.fish_type then
			table.insert(data, v)
		end
	end
	return data
end

-- 获取成熟鱼提示
function SocietyWGData:GetAdultFishRemind()
	local fish_list = self:GetFishpondFishInfoNoGuard(RoleWGData.Instance.role_vo.role_id)
	for i,v in ipairs(fish_list) do
		local fish_time = self:GetCanHarvestTimeByType(v.fish_type)
		if math.floor(TimeWGCtrl.Instance:GetServerTime() - v.raise_timestamp) >= fish_time then
			return 1
		end
	end
	return 0
end

-- 获取可养鱼提示
function SocietyWGData:GetFramFishRemind()
	if not RoleWGData.GetIsEnoughAllCoin(1000) then
		return 0
	end
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local role_level = RoleWGData.Instance.role_vo.level or 1
	local all_info = self.fishpond_all_info[role_id]
	if all_info and all_info.normal_info then
		local normal_info = all_info.normal_info
		local fish_info = self:GetFishpondFishInfo(role_id) or {}
		fish_info = self:FilterCanGainFishData(fish_info) or {}
		local lv_cfg = self:GetFishpondCfgByLv(normal_info.pool_level) or {}
		local common_fish_capacity = lv_cfg.common_fish_capacity or 0
		local max_capacity = common_fish_capacity + normal_info.extend_capacity
		if normal_info.pool_level >= 1 and #fish_info < max_capacity and role_level >= 1 then
			return 1
		end
	end
	return 0
end

-- 获取有子弹可用提示
function SocietyWGData:GetFramBulletRemind()
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local all_info = self.fishpond_all_info[role_id]
	if all_info and all_info.normal_info then
		local normal_info = all_info.normal_info
		local bullet_num = self.other_cfg.base_bullet_num + normal_info.bullet_buy_num - normal_info.bullet_consume_num
		if normal_info.pool_level > 1 and bullet_num > 0 then
			return 1
		else
			return 0
		end
	end
	return 0
end

function SocietyWGData:GetFishRewardDesc(cfg)
	if nil == cfg then return "" end
	local level = RoleWGData.Instance.role_vo.level
	local reward_content = Language.Common.NoRewardTips
	local reward_cfg = SocietyWGData.Instance:GetRewardCfgByLv(level)
	local reward_value = 0

	if cfg.reward_type == 1 then	--铜币
		if cfg.can_harvest > 0 and reward_cfg and cfg.harvest_coin_factor > 0 then
			reward_value = math.floor(reward_cfg.base_coin * cfg.harvest_coin_factor)
			reward_content = reward_value .. ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_COIN)
		end
	elseif cfg.reward_type == 2 then
		local item_name = ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_XINGHUN)
		local reward_cfg = self:GetRewardCfgByLv(level)
		reward_content = item_name and item_name .. "x" .. reward_cfg.base_xinhun * cfg.harvest_xinhun_factor or ""

	elseif cfg.reward_type == 3 then	--物品
		if cfg.harvest_item.item_id then
			local item_name = ItemWGData.Instance:GetItemName(cfg.harvest_item.item_id)
			reward_content = item_name and item_name .. "x" .. cfg.harvest_item.num or ""
		end
	elseif cfg.reward_type == 4 then
		local item_name = ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_YUANLI)
		local reward_cfg = self:GetRewardCfgByLv(level)
		reward_content = item_name and item_name .. "x" .. reward_cfg.base_yuanli * cfg.harvest_yuanli_factor or ""

	end
	if cfg.fish_type == SocietyWGData.GuardFishVo.fish_type then --守卫鱼
		reward_content = Language.Fishpond.CanNotGain
	end
	return reward_content
end

--购买护罩需要的元宝数量
function SocietyWGData:GetBuyProtectPrice()
	if nil ~= self.other_cfg then
		return self.other_cfg.protect_price
	end
end

function SocietyWGData:GetOtherCfg()
	return self.other_cfg
end

--偷鱼记录
function SocietyWGData:SetStolenInfoList(info_list)
	self.stolen_info_list = info_list
end

function SocietyWGData:GetStolenInfoList()
	return self.stolen_info_list or {}
end

--全服鱼塘信息
function SocietyWGData:SetAllPeopleInfoList(info_list)
	self.all_people_info_list = info_list
end

function SocietyWGData:GetAllPeopleInfoList()
	return self.all_people_info_list or {}
end

-- 获取鱼池子弹数量
function SocietyWGData:GetBulletNum()
	local all_info = SocietyWGData.Instance:GetFishpondAllInfo(RoleWGData.Instance.role_vo.role_id)
	if all_info == nil or all_info.normal_info == nil then
		return 0
	end
	local normal_info = all_info.normal_info
	local bullet_num = self.other_cfg.base_bullet_num + normal_info.bullet_buy_num - normal_info.bullet_consume_num
	return bullet_num
end
-- 检查鱼塘扩建
function SocietyWGData:GetFishExtendRemind()
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local normal_info = self:GetFishpondNormalInfo(role_id)
	if nil == normal_info then return end
	local pood_cfg =  self:GetExtendCapacityByNum(normal_info.extend_capacity + 1)
	if nil == pood_cfg then
		return 0
	end
	if pood_cfg.use_coin_need_pool_level <= normal_info.pool_level then
		return 1
	end
	return 0
end

-- 检查好友鱼塘是否可以偷取
function SocietyWGData:GetPoolFriendListCanSteal()
	for i,v in pairs(self.all_people_info_list) do
		if v.can_steal == 1 then
			return true
		end
	end
	return false
end


--赠送记录数据  0 送出  1 接收
function SocietyWGData:SetReceiveGiftRecord(protocol)
	if protocol then
		if protocol.history_type == 0 then
			self.my_send_gift_score = protocol.history_list
		else
			self.my_receive_gift_score = protocol.history_list
		end
	end
end
--送出
function SocietyWGData:GetMySendRecord(index)
	if index == 0 then
		return self.my_send_gift_score
	else
		return self.my_receive_gift_score
	end
end
--接收
function SocietyWGData:GetMyReceiveRecord()
	return self.my_receive_gift_score
end
function SocietyWGData:SetSelectFriendSendId(id)
	self.select_friend_send_id = id
end
function SocietyWGData:GetShouldSelectIndex()
	if not self.select_friend_send_id then return 1 end
	local friend_list = self:GetFriendList2()
	local index = 1
	for k,v in pairs(friend_list) do
		if v.user_id == self.select_friend_send_id then
			index = k
			break
		end
	end
	return index,#friend_list
end

--求婚按钮是否需要展示
function SocietyWGData:GetQiuHunBtnShow(user_id)
	local info = self:FindFriend(user_id)
	if nil == info then
		return false
	end
	local role_sex = RoleWGData.Instance.role_vo.sex
	local lover_id = RoleWGData.Instance.role_vo.lover_uid
	local min_level = MarryWGData.Instance:GetTiQinLimitMinLevel()
	-- print_error(info.sex,role_sex,info.is_online,info.level,min_level,info.user_lover_id,lover_id)
	if info.sex ~= role_sex and info.is_online == 1 and info.level >= min_level and (0 == info.user_lover_id or lover_id == info.user_id) then
		return true
	end
	return false
end

--虎摸信息
function SocietyWGData:SetFriendTouchBaseInfo(protocol)
	self.humo_info = {}
	self.humo_info.src_user_uid = protocol.src_user_uid --info_type == 1 时有效虎摸者id
	self.humo_info.today_touch_count = protocol.today_touch_count --今日虎摸次数
	self.humo_info.today_be_touch_count = protocol.today_be_touch_count --今日被虎摸次数
	self.humo_info.info_type = protocol.info_type --0虎摸1被虎摸
end

function SocietyWGData:GetFriendTouchBaseInfo()
	return self.humo_info
end

--设置虎摸记录
function SocietyWGData:SetTouchRecordInfo(protocol)
	self.recorddata = protocol.recorddata
end
--获取虎摸记录
function SocietyWGData:GetTouchRecordInfo()
	return self.recorddata
end
--获取今日剩余虎摸次数
function SocietyWGData:GetDayTouchShengYuNum()
	local vip_level = RoleWGData.Instance.role_vo.vip_level
	local cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.DAY_TOUCH_MAX_NUM)
	if cfg == nil then
		return 0
	end
	local max_num = cfg["param_"..vip_level] or 0
	local humo_info = self:GetFriendTouchBaseInfo()
	if nil == humo_info then
		return 0
	end
	local shengyu_num = max_num - humo_info.today_touch_count
	return shengyu_num
end


function SocietyWGData:GetDayBeTouchShengYuNum(vip_level,total_touched_count)
	local cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.FRIENDDAY_TOUCH_MAX_NUM)
	if cfg == nil then
		return 0
	end
	local max_num = cfg["param_"..vip_level] or 0
	local humo_info = self:GetFriendTouchBaseInfo()
	if nil == humo_info then
		return 0
	end
	local shengyu_num = max_num - total_touched_count
	return shengyu_num
end

--获取虎摸奖励信息
function SocietyWGData:GetHuMoRewardInfo(level)
	local humo_cfg = ConfigManager.Instance:GetAutoConfig("friendtouchconfig_auto").touch_reward
	for k,v in pairs(humo_cfg) do
		if level >= v.low_level_limit and level >= v.high_level_limit then
			return v
		end
	end
end

--获取虎摸其他
function SocietyWGData:GetHuMoOtherCfg()
	local humo_other_cfg = ConfigManager.Instance:GetAutoConfig("friendtouchconfig_auto").other[1]
	return humo_other_cfg
end
-- --获取被摸次数剩余
-- function SocietyWGData:GetDayBeTouchShengYuNum(vip_level,alearday_touch_num)
-- 	local vip_level = RoleWGData.Instance.role_vo.vip_level
-- 	local cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.FRIENDDAY_TOUCH_MAX_NUM)
-- 	if cfg == nil then
-- 		return 0
-- 	end
-- 	local max_num = cfg["param_"..vip_level] or 0
-- 	local shengyu_num = max_num - alearday_touch_num
-- 	return shengyu_num
-- end

function SocietyWGData:SetInputShow(str)
	self.input_show = str
end

function SocietyWGData:GetInputShow()
	return self.input_show
end

function SocietyWGData:GetRecentlyFirendRedShow(data)
	local friend_list = data
	-- if self:RecentRemindOneRed(data) == 1 then
	-- 	return 1
	-- end

	--是否有私聊信息未读
	for k,v in pairs(friend_list) do
		local un_lyl_num = self:GetLYLUnreadMsgNum(v.user_id)
		if un_lyl_num > 0 then
			return 1
		end
		local un_chat_num = ChatWGData.Instance:GetPrivateUnreadMsgNum(v.user_id )
		if un_chat_num > 0 then
			return 1
		end
	end
	return 0
end


function SocietyWGData:GetUnreadMsgRoleInfo()
	local friend_list = self:GetFriendList2()
	local data_list = {}

	--是否有私聊信息未读
	for k,v in pairs(friend_list) do
		local un_chat_num = ChatWGData.Instance:GetPrivateUnreadMsgNum(v.user_id )
		if un_chat_num > 0 then
			table.insert(data_list, v)
		end
	end

	return data_list
end


--是否可虎摸
function SocietyWGData:RecentRemindOneRed(data)
	local total_exp = BiZuoWGData.Instance:GetTotalExp() or 0
	local other_cfg = SocietyWGData.Instance:GetHuMoOtherCfg()
	local limit_huoyue = other_cfg.daily_liveness_limit or 0
	if total_exp < limit_huoyue then
		return 0
	end

	local friend_list = data
	local humo_num = self:GetDayTouchShengYuNum()
	if friend_list and #friend_list > 0 and humo_num > 0 then

		--判断好友是由存在未被虎摸的
		for k,v in pairs(friend_list) do
			if self:GetIsMyFriend(v.user_id) then
				if v.today_total_touched_count then
					local be_shengyu_num = SocietyWGData.Instance:GetDayBeTouchShengYuNum(v.vip_level,v.today_total_touched_count)
					if v.today_touch_friend_flag == 0 and be_shengyu_num > 0 then
						return 1
					end
				end
			end
		end
		return 0
	end
	return 0
end

function SocietyWGData:GetFirendRedShowList()
	local friend_list = self:GetFriendList2()
	-- if self:RemindOneRed() == 1 then
	-- 	return 1
	-- end

	-- for k,v in pairs(friend_list) do
	-- 	if v.touch_me_total_count > 0 then
	-- 		return 1
	-- 	end
	-- end

	--是否有私聊信息未读
	for k,v in pairs(friend_list) do
		local un_chat_num = ChatWGData.Instance:GetPrivateUnreadMsgNum(v.user_id )
		if un_chat_num > 0 then
			return 1
		end
	end
	return 0
end

function SocietyWGData:GetFirendRedShow2() --界面内红点
	local other_red = self:GetFirendRedShowList()
	if other_red > 0 then
		return other_red
	end

	local new_friend_red = SocietyWGData.Instance:HadFriendReqRed()
	return new_friend_red
end

function SocietyWGData:GetFirendRedShow()
--添加好友申请红点
	local is_mail = SocietyWGData.Instance:GetMailRemind()
	if is_mail > 0 then
		return 0
	end
	return self:GetFirendRedShow2()
end

function SocietyWGData:HadFriendReqRed()
	local size = self:GetReqFriendListSize()
	if size > 0 then
		return 1
	end
	return 0 
end

function SocietyWGData:GetHuoYueOFHuMo()
	local total_exp = BiZuoWGData.Instance:GetTotalExp() or 0
	local other_cfg = SocietyWGData.Instance:GetHuMoOtherCfg()
	local limit_huoyue = other_cfg.daily_liveness_limit or 0
	if total_exp < limit_huoyue then
		return false
	end
	return true
end

--是否可虎摸
function SocietyWGData:RemindOneRed()
	local total_exp = BiZuoWGData.Instance:GetTotalExp() or 0
	local other_cfg = SocietyWGData.Instance:GetHuMoOtherCfg()
	local limit_huoyue = other_cfg.daily_liveness_limit or 0
	if total_exp < limit_huoyue then
		return 0
	end

	local friend_list = self:GetFriendList2()
	local humo_num = self:GetDayTouchShengYuNum()
	if friend_list and #friend_list > 0 and humo_num > 0 then
		--判断好友是由存在未被虎摸的
		for k,v in pairs(friend_list) do
			if v.today_total_touched_count then
				local be_shengyu_num = SocietyWGData.Instance:GetDayBeTouchShengYuNum(v.vip_level, v.today_total_touched_count)
				if 0 == v.today_touch_friend_flag and be_shengyu_num > 0 then
					return 1
				end
			end
		end
		return 0
	end
	return 0
end

--移除好友列表中的数据
function SocietyWGData:RemoveFriendListInfo(user_id)
	if nil == user_id or nil == self.req_list then
		return 0
	end

	for k,v in pairs(self.req_list) do
		if v.user_id == user_id then
			table.remove(self.req_list, k)
			break
		end
	end

	self.req_list_size = #self.req_list
	return self.req_list_size
end

function SocietyWGData:GetintimacyColor(value)
	local color = 1
	for k,v in pairs(self.intimacy_cfg) do
	 	if value < v.need_intimacy then
	 		color = v.color
	 		break
	 	else
	 		color = v.color
	 	end
	end
	return color
end

function SocietyWGData:GetintimacyBuffCfg()
	return self.intimacy_buff_cfg
end

function SocietyWGData:ChackZhanliRank(id)
	local rank_list = RankWGData.Instance:GetRankData(RankKind.Person,PersonRankType.ZhanLi)

	if IsEmptyTable(rank_list) then
		return 11
	end

	for i=1,10 do
		if rank_list[i] then
			if rank_list[i].user_id == id then
				return i
			end 
		end 
	end
	return 11 
end

function SocietyWGData:GetIsMaxTeamMemberCount()
	local is_in_team = self:GetIsInTeam() == 1
	local is_leader = self:GetIsTeamLeader() == 1

	if not is_in_team then
		return false, is_leader
	end

	local team_type = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local max_count = GoalQingYuanFbMaxCount[team_type] or 5
	local cur_count = self:GetTeamMemberCount()
	return cur_count >= max_count, is_leader
end

-- 设置CD结束时间
function SocietyWGData:SetChannelCdEndTime()

	local cd_time, clear_cd = ChatWGData.Instance:GetChatCdLimint(CHANNEL_TYPE.PRIVATE)
	self.cd_end_time = Status.NowTime + cd_time
	self.is_clear_cd = clear_cd
end

-- 获取CD结束时间,是否个人cd
function SocietyWGData:GetChannelCdEndTime()
	return self.cd_end_time or Status.NowTime, self.is_clear_cd
end

--设置刷新好友列表只刷新可显示的格子
function SocietyWGData:SetListNeedRefreshActive(flag)
	-- print_error("设置刷新好友列表只刷新可显示的格子", flag)
	self.list_need_refresh_active = flag
end

function SocietyWGData:GetListNeedRefreshActive()
	return self.list_need_refresh_active
end

--设置 今日不在接受该玩家请求
function SocietyWGData:SetFriendNotReceiveFlag(flag)
	self.is_not_receive_in_today = flag
end

function SocietyWGData:GetFriendNotReceiveFlag()
	return self.is_not_receive_in_today or 0
end

function SocietyWGData:GetPrintLog()
	local tab_str = "SocietyWGData:GetPrintLog\n"
	tab_str = tab_str .. "虎摸红点信息"
	local total_exp = BiZuoWGData.Instance:GetTotalExp() or 0
	local other_cfg = SocietyWGData.Instance:GetHuMoOtherCfg()
	local limit_huoyue = other_cfg.daily_liveness_limit or 0
	tab_str = tab_str .. " total_exp:" .. total_exp
	tab_str = tab_str .. " limit_huoyue:" .. limit_huoyue

	local friend_list = self:GetFriendList2()
	local humo_num = self:GetDayTouchShengYuNum()
	local humo_list = {}
	if friend_list and #friend_list > 0 and humo_num > 0 then
		--判断好友是由存在未被虎摸的
		for k,v in pairs(friend_list) do
			if 0 == v.today_touch_friend_flag then
				table.insert(humo_list,v.name)
			end
		end
	end

	tab_str = tab_str .. "可虎摸的好友列表"
	if not IsEmptyTable(humo_list) then
		for k,v in pairs(humo_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end

	local touch_me_list = {}
	for k,v in pairs(friend_list) do
		if v.touch_me_total_count > 0 then
			table.insert(touch_me_list,v.name)
		end
	end

	tab_str = tab_str .. "好友的虎摸数量:"
	if not IsEmptyTable(touch_me_list) then
		for k,v in pairs(touch_me_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end

	local unread_list = {}
	--是否有私聊信息未读
	for k,v in pairs(friend_list) do
		local un_chat_num = ChatWGData.Instance:GetPrivateUnreadMsgNum(v.user_id )
		if un_chat_num > 0 then
			table.insert(unread_list,v.name)
		end
	end

	tab_str = tab_str .. "好友的未读信息:"
	if not IsEmptyTable(unread_list) then
		for k,v in pairs(unread_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end
	return tab_str
end

function SocietyWGData:GetIsLimitAddEnemyScene(scene_type)
	if scene_type ~= nil then
		return self.limit_add_enemy_scene_type_list[scene_type] ~= nil
	else
		return false
	end
end

local LianXiRenNum = 10
-- is_form_lyl 是否来自情缘聊一聊跳转
function SocietyWGData:SetPlayerprefsinfo(user_id, set_friend_time, is_form_lyl)
	--print_error("SocietyWGData.SetPlayerprefsinfo", user_id, set_friend_time, is_form_lyl)
	local name_list = {}
	local old_user_id_list = {}
	for i=1,10 do
		local temp_name = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i))
		local temp_is_form_lyl = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_isfromlyl", i))
		local is_friend = SocietyWGData.Instance:CheckIsFriend(temp_name)
		if PlayerPrefsUtil.HasKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid",i)) and (is_friend or temp_is_form_lyl ~= nil and temp_is_form_lyl == 1) then
			local friend_data = {}
			friend_data.friend_last_uid = temp_name
			friend_data.friend_last_time = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_time", i))
			friend_data.friend_send_time = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_send_time", i))
			friend_data.is_form_lyl = temp_is_form_lyl ~= nil and temp_is_form_lyl or 0  --是不是来自情缘聊一聊
			-- 容错，避免以前的数据有问题，判断一下uid是否重复
			if not old_user_id_list[friend_data.friend_last_uid] then
				table.insert(name_list, friend_data)
				old_user_id_list[friend_data.friend_last_uid] = true
			end
		end
	end

	local data = {}
	data.friend_last_uid = user_id
	data.friend_last_time = TimeWGCtrl.Instance:GetServerTime()
	data.is_form_lyl = is_form_lyl and 1 or 0
	if set_friend_time or is_form_lyl then
		data.friend_send_time = TimeWGCtrl.Instance:GetServerTime()
	end

	if not old_user_id_list[user_id] then
		table.insert(name_list, data)
	else
		for i,v in ipairs(name_list) do
			if v.friend_last_uid == user_id then
				name_list[i].friend_last_uid = data.friend_last_uid
				name_list[i].friend_last_time = data.friend_last_time
				name_list[i].friend_send_time = data.friend_send_time or name_list[i].friend_send_time
				name_list[i].is_form_lyl = data.is_form_lyl
			end
		end
	end

	table.sort(name_list, SortTools.KeyUpperSorter("friend_last_time"))

	local insert_count = 1
	for i=1, #name_list do
		local user_id = name_list[i].friend_last_uid
		if user_id then
			local is_friend = SocietyWGData.Instance:CheckIsFriend(user_id)
			if name_list[i] and (is_friend or name_list[i].is_form_lyl == 1) then
				PlayerPrefsUtil.SetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i), name_list[i].friend_last_uid)
				PlayerPrefsUtil.SetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_time", i), name_list[i].friend_last_time)
				PlayerPrefsUtil.SetInt(SocietyWGData.GetPrivaPrefsKey("friend_send_time", i), name_list[i].friend_send_time or 0)
				PlayerPrefsUtil.SetInt(SocietyWGData.GetPrivaPrefsKey("friend_isfromlyl", i), name_list[i].is_form_lyl)
				insert_count = insert_count + 1
				if insert_count > LianXiRenNum then
					break
				end
			end
		end
	end
end

function SocietyWGData:UpdateLYLRoleInfo(call_back)
	if IsEmptyTable(self.lyl_role_info) then
		self.lyl_role_info = {}
	end

	local empty_role_uid_list = {}
	for i = 1, 10 do
		if PlayerPrefsUtil.HasKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i)) then
			local uid = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i))
			local temp_time  = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_time", i))
			local send_time  = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_send_time", i))
			local is_form_lyl = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_isfromlyl", i))
			local is_friend = SocietyWGData.Instance:FindFriend(uid)
			if uid > 0 and not is_friend and is_form_lyl and is_form_lyl == 1 and not self.lyl_role_info[uid] then
				empty_role_uid_list[#empty_role_uid_list + 1] = uid
			end
		end
	end
	--print_error("empty_role_uid_list =", empty_role_uid_list)

	if not IsEmptyTable(empty_role_uid_list) then
		local num = #empty_role_uid_list
		for k, v in pairs(empty_role_uid_list) do
			local role_id = v
			if role_id > 0 then
				BrowseWGCtrl.Instance:SendQueryRoleInfoReq(role_id, function (role_info)
					self.lyl_role_info[role_id] = SocietyWGData.GetLYLRoleVo(role_info)
					num = num - 1
					if num <= 0 and call_back then
						call_back()
					end
				end)
			end
		end
	elseif call_back then
		call_back()
	end
end

function SocietyWGData:GetLYLRoleInfo(role_id)
	return self.lyl_role_info and self.lyl_role_info[role_id]
end

function SocietyWGData:RemoveLYLRoleInfo(role_id)
	if self.lyl_role_info and self.lyl_role_info[role_id] then
		self.lyl_role_info[role_id] = nil
	end
end

function SocietyWGData:UpdateLYLRoleInfoOnLine(role_id,is_online)
	if self.lyl_role_info[role_id] then
		self.lyl_role_info[role_id].is_online = is_online
	end
	SocietyWGCtrl.Instance:Flush("friend_list")
end

function SocietyWGData:OnSCLiaoYiLiaoInteraction(lyl_info)
	local target_role_id = lyl_info.role_id == RoleWGData.Instance:InCrossGetOriginUid() and lyl_info.target_role_id or lyl_info.role_id
	self:SetPlayerprefsinfo(target_role_id, true, true)
	self.new_liaoyiliao_user_id = target_role_id
	if SocietyWGCtrl.Instance:GetSocietyViewPrivateRoleId() ~= lyl_info.role_id then
		self:AddLYLUnreadMsg(lyl_info)
	end
	-- local is_friend = self:CheckIsFriend(target_role_id)
	-- if lyl_info.role_id ~= RoleWGData.Instance:InCrossGetOriginUid() and is_friend and not ViewManager.Instance:IsOpen(GuideModuleName.Society) then
	-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO, 1, function()
	-- 		SocietyWGCtrl.Instance:OpenFriendViewFromLiaoYiLiao(target_role_id,true)
	-- 		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO)
	-- 	end)
	-- end

	self:UpdateLYLRoleInfo(function()
		-- print_error("~~~~~~~ OnSCLiaoYiLiaoInteraction", target_role_id, RoleWGData.Instance:InCrossGetOriginUid())
		-- if lyl_info.role_id == RoleWGData.Instance:InCrossGetOriginUid() then
			SocietyWGCtrl.Instance:OpenFriendViewFromLiaoYiLiao(target_role_id, lyl_info.role_id == RoleWGData.Instance:InCrossGetOriginUid())
		-- end
	end)
end

function SocietyWGData:GetNewLiaoYiLiaoUserId()
	return self.new_liaoyiliao_user_id or 0
end

-- 撩一撩未读列表
function SocietyWGData:GetLYLUnreadList()
	return self.lyl_unread_list
end

-- 添加私聊未读消息
function SocietyWGData:AddLYLUnreadMsg(msg_info)
	if not self.lyl_unread_list then
		self.lyl_unread_list = {}
	end
	table.insert(self.lyl_unread_list, msg_info)
end

-- 移除撩一撩未读消息
function SocietyWGData:RemLYLUnreadMsg(uid)
	local is_flush = false
	for k,v in pairs(self.lyl_unread_list) do
		if v.role_id == uid then
			table.remove(self.lyl_unread_list,k)
			is_flush = true
		end
	end
	
	if not is_flush then
		return
	end
	
	SocietyWGData.Instance:SetListNeedRefreshActive(true)
	-- MainuiWGCtrl.Instance:SetFriendRemind()
	SocietyWGCtrl.Instance:Flush("friend_list")
	-- RemindManager.Instance:Fire(RemindName.SocietyFriends)
	-- RemindManager.Instance:Fire(RemindName.SocietyFriends2)

end

--获取撩一撩未读数量
function SocietyWGData:GetLYLUnreadMsgNum( uid )
	local count = 0
	for k,v in pairs(self.lyl_unread_list) do
		if v.role_id == uid then
			count = count + 1
		end
	end
	return count
end


function SocietyWGData:ClearAllLYLRolePlayerprefsinfo()
	for i=1, 10 do
		if PlayerPrefsUtil.HasKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i)) then
			local isfromlyl = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_isfromlyl", i))
			if isfromlyl and isfromlyl == 1 then
				PlayerPrefsUtil.DeleteKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i))
			end
		end
	end
end

function SocietyWGData.GetLYLRoleVo(role_info)
	local uuid = {}
	uuid.temp_low = RoleWGData.Instance:GetMergePlatType()
	uuid.temp_high = RoleWGData.Instance:GetMergeServerId()
	local role_vo = {
		user_id = role_info.role_id,
		user_lover_id = 0,
		gamename = role_info.role_name,
		intimacy = 0,
		camp = role_info.camp,
		sex = role_info.sex,
		prof = role_info.prof,
		is_online = role_info.is_online,
		resver_ch = "",
		level = role_info.level,
		vip_level = role_info.vip_level,
		capability = role_info.capability,
		avatar_key_big = role_info.avatar_key_big,
		avatar_key_small = role_info.avatar_key_small,
		last_logout_timestamp = 0,
		team_index = 0,
		touch_me_total_count = 0, 				--好友虎摸本人总次数
		today_touch_friend_flag = 0, 			--今日互摸好友标志
		today_total_touched_count = 0, 			--今日被虎摸总次数
		zhandui_id  = 0,						--战队id
		resver_sh = 0,
		relation_flag = 0,
		uuid = uuid,
		is_lyl = true,
		is_guaji = 0,
		bigchatface_status = role_info.bigchatface_status,
		city_name = role_info.city_name,
		fashion_bubble = role_info.appearance.fashion_bubble,
		fashion_photoframe = role_info.appearance.fashion_photoframe,					--头像框
		tuhaojin_color = role_info.tuhaojin_color,
	}
	return role_vo
end

function SocietyWGData:GetLYLContentCfg(content_id)
	if not self.lyl_content_cfg then
		local cfg = ConfigManager.Instance:GetAutoConfig("liao_yi_liao_auto")
		self.lyl_content_cfg = ListToMapList(cfg.liaoyiliao_info, "id")
	end
	return self.lyl_content_cfg[content_id]
end

function SocietyWGData:GetRandomContent()
	self:GetLYLContentCfg(0)
	local random = math.random(1, #self.lyl_content_cfg)
	return self:GetLYLContentCfg(random)
end

function SocietyWGData:RemoveLYLInfo(role_id,add_friend)
	for i = 1, 10 do
		if PlayerPrefsUtil.HasKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid",i)) then
			local uid = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_uid",i))
			local is_friend = SocietyWGData.Instance:CheckIsFriend(role_id)
			if uid == role_id and not is_friend and not add_friend then
				PlayerPrefsUtil.DeleteKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid",i))
			end
		end
	end
end

function SocietyWGData:GetIsSameTeam(target_team_index)
    local my_team_index
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        my_team_index = CrossTeamWGData.Instance:GetTeamIndex()
    else
        my_team_index = self:GetTeamIndex()
    end
    my_team_index = my_team_index or -1
    return my_team_index == target_team_index
end