require("game/boss/boss_view")
require("game/boss/boss_world_view")
require("game/boss/boss_vip_view")
require("game/boss/boss_dabao_view")
require("game/boss/boss_mijing_view")
require("game/boss/boss_kf_boss_view")
require("game/boss/boss_sg_boss_view")
require("game/boss/boss_kf_yunluo_boss_view")
require("game/boss/boss_kf_shenyu_boss_view")
require("game/boss/boss_kf_shenyu_boss_scene_view")
require("game/boss/boss_personal_view")
require("game/boss/boss_wg_data")
require("game/boss/treasure_hunt_boss_wg_data")
-- require("game/boss/boss_hurt_view")
-- require("game/boss/boss_dice_view")

require("game/boss/boss_drop_record_view")
require("game/boss/kfboss_drop_record_view")
require("game/boss/bosspanel/boss_kill_record_view")
require("game/boss/bosspanel/boss_forenotice_view")
require("game/boss/bosspanel/boss_special_forenotice_view")
require("game/boss/bosspanel/dabao_enter_consum_view")
require("game/boss/bosspanel/person_enter_consume_view")
require("game/boss/bosspanel/be_killed_view")
--require("game/boss/bosspanel/boss_alert_view")
-- require("game/boss/bosspanel/boss_elite_view")
require("game/boss/bosspanel/drive_out_dabao")
require("game/boss/boss_tip_view")
require("game/boss/boss_shenyun_view")
require("game/boss/boss_mijing_scene_view")
require("game/boss/boss_vip_times")
require("game/boss/boss_new_sy_view")
require("game/boss/boss_hmsy_tip_view")
require("game/boss/quick_use_item_view")
require("game/boss/quick_vip_buy")
require("game/boss/boss_level_limit_tip")
require("game/boss/vip_not_enough")
require("game/boss/special_fuhuo_view")
require("game/worldserver/shenyuan_hurt_view")
require("game/boss/bosspanel/boss_reward_view")
require("game/boss/bosspanel/world_boss_buytimes_view")
require("game/boss/bosspanel/boss_first_kill_reward")
require("game/boss/bosspanel/boss_reward_choose_view")
require("game/boss/bosspanel/boss_firstkill_redpacket")
require("game/boss/bosspanel/boss_invoke_view")
require("game/boss/boss_kf_gather_info_view")
require("game/boss/boss_reward_thsgiven_view")
require("game/boss/bosspanel/boss_focus_view")
require("game/boss/boss_firstkill_view")
require("game/boss/boss_xianjie_wg_ctrl")
require("game/boss/special_personal_boss_scene_view")
require("game/boss/boss_dabao_equip_show")
require("game/boss/bosspanel/boss_dead_tips_view")
require("game/boss/boss_quick_rebirth_view")		-- 快速复活

BossWGCtrl = BossWGCtrl or BaseClass(BaseWGCtrl)

function BossWGCtrl:__init()
	if BossWGCtrl.Instance then
		error("[BossWGCtrl]:Attempt to create singleton twice!")
	end
	BossWGCtrl.Instance = self

	self.data = BossWGData.New()
	self.view = BossView.New(GuideModuleName.Boss)

	self.boss_forenotice_view = BossForenoticeView.New()
	self.boss_special_forenotice_view = BossSpecialForenoticeView.New()
	self.dabao_enter_view = DabaoEnterConsumView.New()
	self.bekilled_view = BeKilledView.New()
	-- self.fatigue_count_view = FatigueCountTimeView.New()
	self.vip_alert_view = Alert.New()
	self.drive_out_dabao = DabaoBossDriveView.New()
	self.tip_view = BossDescTip.New()
	self.sy_att_view = SYAttView.New()
	self.shengyin_view = ShenYinBossView.New()
	self.sy_ticket_view = SYTicketView.New()
	self.sy_ticket_time_view = SYTicketTimeView.New()
	--self.shenyu_rank_list = ShengYuBossSceneUI.New(GuideModuleName.Boss_ShengYu_FollowUi)
	--self.boss_scene_logic_panel = BossSceneLogicView.New()
	self.mj_boss_view = MiJingBossView.New()
	self.vip_time_view = BossVipTimesView.New()
	self.hmsy_tip = BossHMSYTipView.New(GuideModuleName.BossHMSYTip)
	self.boss_drop_record_view = BossDropRecordView.New(GuideModuleName.BossDropRecord)
    self.quick_use_view = QuickItemUseView.New(GuideModuleName.QuickUse)
	self.limit_tip = BossLevelLimitTip.New()

	self.quick_vip_buy = QuickVipBuyView.New()
	self.world_boss_buy_view = WorldBossBuyTimesView.New()
    self.boss_kill_record_view = BossKillRecordView.New(GuideModuleName.BossKillRecordView)
    self.boss_reward_view = BossRewardView.New()
    self.vip_not_enough_view = VipNotEnough.New()
    self.special_fuhuo_view = SpecialFuhuoView.New()
    self.shenyuan_hurt_view = ShenyuanBossHurtView.New(GuideModuleName.ShenyuanBossHurtView)
    self.shenyuan_flag_tip = ShenyuanBossFlagTipView.New()
    self.person_enter_cosume_view = PersonEnterCosumeView.New()
    self.first_kill_reward_view = BossFirstKillRewardView.New()
    self.first_choose_reward_view = BossFirstRewardChooseView.New()
    self.firstkill_redpacket_view = BossFirstKillRedPacket.New()
    self.boss_invoke_view = BossInvokeView.New()
    self.kf_boss_gather_info_view = KFBossGatherView.New()

    self.boss_focus_view = BossFocusView.New()
    self.treasure_boss_data = TreasureBossWGData.New()
    self.boss_reward_thsgiven_view = BossRewardThsGivenView.New(GuideModuleName.BossRewardThsGivenView)
    self.boss_first_kill_view = BossFirstKillView.New()
	self.special_personal_boss_scene_view = SpecialPersonalBossSceneView.New()
	self.dabao_boss_equip_show_view = DabaoBossEquipShow.New()
	self.boss_quick_rebirth_view = BossQuickRebirthView.New()

	self.boss_dead_tips_view = BossDeadTipsView.New()

    self:RegisterAllProtocals()
    self.create_role_event = GlobalEventSystem:Bind(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind(self.ReqProtocol,self))
    self:InitXianjieWGCtrl()

	if not self.today_real_recharge_event then
		self.today_real_recharge_event = GlobalEventSystem:Bind(ROLE_REAL_RECHARGE_NUM_CHANGE.TODAY_REAL_RECHARGE_CHANGE, BindTool.Bind(self.OnTodayRealRechargeNumChange, self))
	end
end

function BossWGCtrl:__delete()
    self:DeleteXianjieWGCtrl()
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil
    if nil ~= self.world_boss_buy_view then
		self.world_boss_buy_view:DeleteMe()
		self.world_boss_buy_view = nil
    end
    
	if self.sy_ticket_view then
		self.sy_ticket_view:DeleteMe()
		self.sy_ticket_view = nil
	end
	if self.person_enter_cosume_view then
		self.person_enter_cosume_view:DeleteMe()
		self.person_enter_cosume_view = nil
	end
	if self.sy_ticket_time_view then
		self.sy_ticket_time_view:DeleteMe()
		self.sy_ticket_time_view = nil
	end
    if self.create_role_event then
		GlobalEventSystem:UnBind(self.create_role_event)
		self.create_role_event = nil
	end
	if self.first_kill_reward_view then
		self.first_kill_reward_view:DeleteMe()
		self.first_kill_reward_view = nil
    end

    if self.first_choose_reward_view then
		self.first_choose_reward_view:DeleteMe()
		self.first_choose_reward_view = nil
    end

    if self.firstkill_redpacket_view then
		self.firstkill_redpacket_view:DeleteMe()
		self.firstkill_redpacket_view = nil
    end

    if self.boss_invoke_view then
		self.boss_invoke_view:DeleteMe()
		self.boss_invoke_view = nil
    end

    if self.boss_focus_view then
		self.boss_focus_view:DeleteMe()
		self.boss_focus_view = nil
    end
    
    if self.shengyin_view then
		self.shengyin_view:DeleteMe()
		self.shengyin_view = nil
	end

	if self.sy_att_view then
		self.sy_att_view:DeleteMe()
		self.sy_att_view = nil
	end

	self.tip_view:DeleteMe()
	self.tip_view = nil

	if self.mj_boss_view then
		self.mj_boss_view:DeleteMe()
		self.mj_boss_view = nil
	end

	if self.shenyuan_flag_tip then
		self.shenyuan_flag_tip:DeleteMe()
		self.shenyuan_flag_tip = nil
	end

	--if self.shenyu_rank_list then
	--	self.shenyu_rank_list:DeleteMe()
	--	self.shenyu_rank_list = nil
	--end
	if self.level_fb_alert then
		self.level_fb_alert:DeleteMe()
		self.level_fb_alert = nil
	end

	if nil ~= self.hurt_view then
		self.hurt_view:DeleteMe()
		self.hurt_view = nil
	end

	if nil ~= self.kf_boss_gather_info_view then
		self.kf_boss_gather_info_view:DeleteMe()
		self.kf_boss_gather_info_view = nil
	end

	if nil ~= self.boss_reward_thsgiven_view then
		self.boss_reward_thsgiven_view:DeleteMe()
		self.boss_reward_thsgiven_view = nil
	end

	--if nil ~= self.boss_scene_logic_panel then
	--	self.boss_scene_logic_panel:DeleteMe()
	--	self.boss_scene_logic_panel = nil
	--end

	--if self.kill_record_view then
	--	self.kill_record_view:DeleteMe()
	--	self.kill_record_view = nil
	--end

	if nil ~= self.dice_view then
		self.dice_view:DeleteMe()
		self.dice_view = nil
	end

	if nil ~= self.vip_time_view then
		self.vip_time_view:DeleteMe()
		self.vip_time_view = nil
	end

	if nil ~= self.hmsy_tip then
		self.hmsy_tip:DeleteMe()
		self.hmsy_tip = nil
	end

	if nil ~= self.boss_drop_record_view then
		self.boss_drop_record_view:DeleteMe()
		self.boss_drop_record_view = nil
	end

	if nil ~= self.boss_kill_record_view then
		self.boss_kill_record_view:DeleteMe()
		self.boss_kill_record_view = nil
    end

    if nil ~= self.boss_reward_view then
		self.boss_reward_view:DeleteMe()
		self.boss_reward_view = nil
    end


    if nil ~= self.vip_not_enough_view then
		self.vip_not_enough_view:DeleteMe()
		self.vip_not_enough_view = nil
	end

    if nil ~= self.special_fuhuo_view then
		self.special_fuhuo_view:DeleteMe()
		self.special_fuhuo_view = nil
    end

	if nil ~= self.quick_use_view then
		self.quick_use_view:DeleteMe()
		self.quick_use_view = nil
	end

	if self.mainui_open_comlete then
		GlobalEventSystem:UnBind(self.mainui_open_comlete)
		self.mainui_open_comlete = nil
	end

	if self.scene_change_complete then
		GlobalEventSystem:UnBind(self.scene_change_complete)
		self.scene_change_complete = nil
	end

	if self.today_real_recharge_event then
		GlobalEventSystem:UnBind(self.today_real_recharge_event)
		self.today_real_recharge_event = nil
	end

	if nil ~= self.boss_forenotice_view then
		self.boss_forenotice_view:DeleteMe()
		self.boss_forenotice_view = nil
	end

	if nil ~= self.boss_special_forenotice_view then
		self.boss_special_forenotice_view:DeleteMe()
		self.boss_special_forenotice_view = nil
	end

	if nil ~= self.dabao_enter_view then
		self.dabao_enter_view:DeleteMe()
		self.dabao_enter_view = nil
	end

	if nil ~= self.bekilled_view then
		self.bekilled_view:DeleteMe()
		self.bekilled_view = nil
	end

	if nil ~= self.vip_alert_view then
		self.vip_alert_view:DeleteMe()
		self.vip_alert_view = nil
	end

	if nil ~= self.boss_elite_view then
		self.boss_elite_view:DeleteMe()
		self.boss_elite_view = nil
	end

	if nil ~= self.drive_out_dabao then
		self.drive_out_dabao:DeleteMe()
		self.drive_out_dabao = nil
	end

	if nil ~= self.limit_tip then
		self.limit_tip:DeleteMe()
		self.limit_tip = nil
	end

	if nil ~= self.quick_vip_buy then
		self.quick_vip_buy:DeleteMe()
		self.quick_vip_buy = nil
	end

	if self.kf_boss_gather_info_view then
		self.kf_boss_gather_info_view:DeleteMe()
		self.kf_boss_gather_info_view = nil
	end

	if self.treasure_boss_data then
		self.treasure_boss_data:DeleteMe()
		self.treasure_boss_data = nil
	end

	if self.boss_first_kill_view then
		self.boss_first_kill_view:DeleteMe()
		self.boss_first_kill_view = nil
	end

	if self.dabao_boss_equip_show_view then
		self.dabao_boss_equip_show_view:DeleteMe()
		self.dabao_boss_equip_show_view = nil
	end

	if self.boss_dead_tips_view then
		self.boss_dead_tips_view:DeleteMe()
		self.boss_dead_tips_view = nil
	end

	if self.boss_quick_rebirth_view then
		self.boss_quick_rebirth_view:DeleteMe()
		self.boss_quick_rebirth_view = nil
	end

	self.special_personal_boss_scene_view:DeleteMe()
	self.special_personal_boss_scene_view = nil

	BossWGCtrl.Instance = nil
end

function BossWGCtrl:RegisterAllProtocals()
	--个人boss
	self:RegisterProtocol(SCPersonBossFirstKillRecordInfo, "OnPersonBossFirstKillRecordInfo")
	--世界boss进入次数信息
	self:RegisterProtocol(SCWorldBossEnterTimeInfo, "OnWorldBossEnterTimeInfo")
	--boss之家
	self:RegisterProtocol(SCBossHomeEnterTimeInfo, "OnBossHomeEnterTimeInfo")

	--- 原协议
	-------------------------------
	self:RegisterProtocol(SCWorldBossDead, "OnWorldBossDead")

	-----------------------------------------------------------------
	self:RegisterProtocol(SCWorldBossReliveTire, "OnWorldBossReliveTire") --复活疲劳(世界)
	self:RegisterProtocol(SCCrossBossReliveTire, "OnSCCrossBossReliveTire") --复活疲劳(跨服)
	self:RegisterProtocol(SCWorldBossForenotice, "OnWorldBossForenotice") -- 关注的boss刷新
	self:RegisterProtocol(SCWorldBossOnDie, "OnWorldBossOnDie")           -- 所有boss死亡通知
	--------------------世界BOSS-------------------------------------
	self:RegisterProtocol(CSWorldBossReq)
	self:RegisterProtocol(SCWorldBossInfo, "OnWorldBossInfo")
    self:RegisterProtocol(SCWorldBossRoleRealiveInfo, "OnSCWorldBossRoleRealiveInfo")

	---------------------掉落记录-----------------------------------
	self:RegisterProtocol(SCWorldBossDropHistory, "OnWorldBossDropHistory")


	---------------------boss击杀记录-----------------------------------
	self:RegisterProtocol(CSBossKillRoleRecordReq)
	self:RegisterProtocol(SCBossKillRoleRecordAck, "OnSCBossKillRoleRecordAck")

	---------------------打宝boss(洞窟boss)现在用的-----------------
	self:RegisterProtocol(SCDongKuBossAllInfo, "OnSCDongKuBossAllInfo")
	self:RegisterProtocol(SCDongKuBossLayerInfo, "OnSCDongKuBossLayerInfo")
	self:RegisterProtocol(SCDabaoBossSceneInfo, "OnDabaoBossSceneInfo")           -- 打宝地图怒气值
	self:RegisterProtocol(CSDongKuBossEnterReq)

	---------------------Vip boss-------------------------
	self:RegisterProtocol(SCVipBossAllInfo, "OnSCVipBossAllInfo")
	self:RegisterProtocol(SCVipBossLayerInfo, "OnSCVipBossLayerInfo")
	self:RegisterProtocol(CSVipBossEnterReq)

	---------------------跨服boss--------------------------
	self:RegisterProtocol(SCCrossBossPlayerInfo, "OnCrossBossPlayerInfo")        -- 玩家信息
	self:RegisterProtocol(SCCrossBossSceneInfo, "OnCrossBossSceneInfo")	         -- 场景内信息
	self:RegisterProtocol(SCCrossBossBossKillRecord, "OnCrossBossBossKillRecord")-- 击杀记录
	self:RegisterProtocol(SCCrossBossDropRecord, "OnCrossBossDropRecord")        -- 掉落记录
	self:RegisterProtocol(SCCrossBossForenotice, "OnCrossBossForenotice")        -- 刷新提醒
	self:RegisterProtocol(SCCrossBossCrystalGathertTimes, "OnSCCrossBossCrystalGathertTimes") --新采集物信息
	self:RegisterProtocol(CSCrossBossReq)
	---------------------上古遗迹--------------------------
	--self:RegisterProtocol(CSShangGuBossEnterReq)
	--self:RegisterProtocol(SCShangGuBossAllInfo, "OnShangGuBossAllInfo")
	--self:RegisterProtocol(SCShangGuBossLayerInfo, "OnShangGuBossLayerInfo")
	--self:RegisterProtocol(SCShangGuBossSceneInfo, "OnShangGuBossSceneInfo")
	--self:RegisterProtocol(SCShangGuBossSceneOtherInfo, "OnShangGuBossSceneOtherInfo")

	self:RegisterProtocol(CSShangGuYiJiOperaReq)
	self:RegisterProtocol(CSShangGuYiJiEnterOpera)
	--self:RegisterProtocol(SCShangGuYiJiUserInfo, "OnSCShangGuYiJiUserInfo")
	--self:RegisterProtocol(SCShangGuYiJiSceneUserInfo, "OnSCShangGuYiJiSceneUserInfo")
	--self:RegisterProtocol(SCShangGuYiJiSceneInfo, "OnSCShangGuYiJiSceneInfo")
	--self:RegisterProtocol(SCShangGuYiJIAllInfo, "OnSCShangGuYiJIAllInfo")

	---------------------召唤boss--------------------------
	--self:RegisterProtocol(SCBossOwnerChangeInfo, "OnSCBossOwnerChangeInfo")

-------------------------boss图鉴--------------------------
	self:RegisterProtocol(CSBossCardReq)
	self:RegisterProtocol(SCBossCardAllInfo, "OnSCBossCardAllInfo")
	-------------------------------------------------------
	-- -------------------------圣域boss--------------------------
	self:RegisterProtocol(CSSacredBossReq)
	self:RegisterProtocol(SCSacredBossPlayerInfo, "OnSCSacredBossPlayerInfo")
	self:RegisterProtocol(SCSacredBossSceneInfo, "OnSCSacredBossSceneInfo")	 	--7545
	-- self:RegisterProtocol(SCSacredBossSceneAllInfo, "SacredBossSceneAllInfo")--全部返回圣域相关刷新时间的信息
	self:RegisterProtocol(SCSacredBossBossKillRecord, "OnSCSacredBossBossKillRecord")
	self:RegisterProtocol(SCSacredBossDropRecord, "OnSCSacredBossDropRecord")
	self:RegisterProtocol(SCSacredBossForenotice, "OnSCSacredBossForenotice")
	self:RegisterProtocol(SCSacredBossOwnBossInfoChange, "OnSCSacredBossOwnBossInfoChange")
	-- -------------------------------------------------------
	------------------------神陨之地-------------------------------
	self:RegisterProtocol(SCCrossShengYinFbRoleInfo, "OnShenYUNJinliInfo")
	self:RegisterProtocol(SCCrossShengYinFbTotemInfo, "OnShenYunTutengInfo")
	self:RegisterProtocol(SCCrossShengYinFbTime, "OnShenYunFushTime")
	self:RegisterProtocol(CSCrossShengYinFbReq)
	--------------------------秘境boss--------------------------------------
	self:RegisterProtocol(CSSecretBossReq)										--请求秘境boss信息
	self:RegisterProtocol(SCSecretBossInfo,"OnSCScsecretBossInfo")				--所有信息
	self:RegisterProtocol(SCSecretBossLayerInfo,"OnSCSecretBossLayerInfo")		--单层信息
	self:RegisterProtocol(CSSecretBossPhysicalReq)										--请求体力信息
	self:RegisterProtocol(SCSecretBossPhysicalInfo,"OnSCSecretBossPhysicalInfo")		--体力信息返回

	self:RegisterProtocol(SCWorldBossSpecialDropHistory, "OnSCWorldBossSpecialDropHistory") -- 上古，秘境掉落记录

	-----------------------------------鸿蒙神域---------------------------------------------
	self:RegisterProtocol(CSCAHongMengBossOpera)
	self:RegisterProtocol(CSCrossDropRecordReq)
	self:RegisterProtocol(CSCAHongMengBossEnterReq)
	self:RegisterProtocol(SCHongMengBossSelfInfo, "OnSCHongMengBossSelfInfo")
	self:RegisterProtocol(SCHongMengBossSceneInfo, "OnSCHongMengBossSceneInfo")
	self:RegisterProtocol(SCCrossDropItemInfo, "OnSCCrossDropItemInfo")
	self:RegisterProtocol(SCHongMengBossTotalScene, "OnSCHongMengBossTotalScene")
	self:RegisterProtocol(SCHongMengBossSceneInsideInfo, "OnSCHongMengBossSceneInsideInfo")
	self:RegisterProtocol(SCHongMengBossPreNotice, "OnSCHongMengBossPreNotice")
    -----------------------------------------------------------------------------------------
    -----------------------------------深渊魔王---------------------------------------------
	self:RegisterProtocol(ShenYuanBossReq)
	self:RegisterProtocol(SCSendShenyuanAllBossInfo, "OnSCSendShenyuanAllBossInfo")  --深渊boss所有信息返回
	self:RegisterProtocol(SCSendShenyuanSingleBossInfo, "OnSCSendShenyuanSingleBossInfo")
	self:RegisterProtocol(SCShenyuanBossHurtInfo, "OnSCShenyuanBossHurtInfo")
    self:RegisterProtocol(SCShenyuanBossPersonHurtInfo, "OnSCShenyuanBossPersonHurtInfo")
    self:RegisterProtocol(SCShenYuanBossCountryHurtlist, "OnSCShenYuanBossCountryHurtlist")
	self:RegisterProtocol(SCShenYuanBossConcernlist, "OnSCShenYuanBossConcernlist")
    -----------------------------------------------------------------------------------------
    self:RegisterProtocol(CSBossfirstkillOpera) --Boss首杀
    self:RegisterProtocol(CSFetchBossfirstkillWorldReward)
    self:RegisterProtocol(SCBossfirstkillPersonInfo, "OnSCBossfirstkillPersonInfo") --
    self:RegisterProtocol(SCBossfirstkillWorldInfo, "OnSCBossfirstkillWorldInfo") --
    self:RegisterProtocol(SCBossfirstkillOneBossInfo, "OnSCBossfirstkillOneBossInfo") --
    self:RegisterProtocol(SCBossfirstkillInfiniteInfo, "OnSCBossfirstkillInfiniteInfo") --

    self:RegisterProtocol(CSShenyuanBossBuyTimesReq)

        -----------------------------------寻宝boss---------------------------------------------
	self:RegisterProtocol(CSTreasureHuntBossReq)
	self:RegisterProtocol(SCSendTreasureHuntAllBossInfo, "OnSCSendTreasureHuntAllBossInfo")
	self:RegisterProtocol(SCSendTreasureHuntSingleBossInfo, "OnSCSendTreasureHuntSingleBossInfo")
	--self:RegisterProtocol(SCTreasureHuntBossHurtInfo, "OnSCTreasureHuntBossHurtInfo")
	--self:RegisterProtocol(SCTreasureHuntBossPersonHurtInfo, "OnSCTreasureHuntBossPersonHurtInfo")
	self:RegisterProtocol(SCChestshopBossFinishInfo,"OnSCChestshopBossFinishInfo")
	self:RegisterProtocol(SCChestshopBossResetInfo,"OnSCChestshopBossResetInfo")
    -----------------------------------------------------------------------------------------

	-----------------------------------每日臻充boss---------------------------------------------
	self:RegisterProtocol(CSCrossRealChargeBossOperate)
	self:RegisterProtocol(SCCrossRealChargeBossAllInfo, "OnSCCrossRealChargeBossAllInfo")
	self:RegisterProtocol(SCCrossRealChargeBossMonsterUpdate, "OnSCCrossRealChargeBossMonsterUpdate")
	self:RegisterProtocol(SCCrossRealChargeBossMonsterList, "OnSCCrossRealChargeBossMonsterList")
	self:RegisterProtocol(SCCrossRealChangeBossRecordList, "OnSCCrossRealChangeBossRecordList")
	-------------------------------------------------------------------------------------------

    self:RegisterProtocol(CSRefreshBossRewardGet)
	self:RegisterProtocol(SCRefreshBossRewardInfo, "OnSCRefreshBossRewardInfo")

	self:RegisterProtocol(CSReqMonsterIsExsit)
	self:RegisterProtocol(SCAckMonsterIsExsit, "OnSCAckMonsterIsExsit")

	self:RegisterProtocol(SCPersonSpecialBossSceneInfo, "OnPersonSpecialBossSceneInfo")

	self:RegisterProtocol(SCBOSSDeadNotice, "OnSCBOSSDeadNotice")

	self:RegisterProtocol(SCMonsterSecKillInfo, "OnSCMonsterSecKillInfo")

	self.mainui_open_comlete = self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	self.scene_change_complete = self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, function()
		ViewManager.Instance:FlushView(GuideModuleName.Boss)
		ViewManager.Instance:FlushView(GuideModuleName.WorldServer)
        self.data:ChangeDayPassAct()
        self:PassDay()
	end)
    self:BindGlobalEvent(MainUIEventType.SHOW_ASCRIPTION, function(name)
        if name == nil then
            self:ReLoadRoleTiredIcon()
        end
    end)
end

function BossWGCtrl:PassDay()
    self:RequestXianJieBossInfo()
    self:RequestXianJieBaseInfo()
end

function BossWGCtrl:MainuiOpenCreate()
	self:SendWorldBossReq(BossView.ReqType.ALLINFO)
	self:SendDongKuBossReq(BossView.ReqType.ALLINFO)
	self:SendShangGuBossReq(WorldServerView.SG_OPERA.ALLINFO)
	-- self:SendBossTuJianReq(BOSS_CARD_OPERA_TYPE.BOSS_CARD_OPERA_TYPE_ALL_INFO)
    self:SendVipAllReq()
    self:RequestXianJieBossInfo()
end

--个人bosss首杀信息
function BossWGCtrl:OnPersonBossFirstKillRecordInfo(protocol)
	self.data:SetPersonBossFirstKillRecordInfo(protocol)
	self.view:Flush(TabIndex.boss_personal)
	self.vip_time_view:Flush()
    GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
    GlobalEventSystem:Fire(OtherEventType.BossInfoChange, BOSS_TASK_TYPE.PERSON)
	RemindManager.Instance:Fire(RemindName.Boss_Per)
end

-- 世界boss进入次数信息
function BossWGCtrl:OnWorldBossEnterTimeInfo(protocol)
	self.data:SetWorldBossEnterTimeInfo(protocol)
	self.view:Flush(TabIndex.boss_world)
    GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
    GlobalEventSystem:Fire(OtherEventType.BossInfoChange, BOSS_TASK_TYPE.WORLD)
    self:SetRoleTiredIcon(DAY_COUNT.DAYCOUNT_ID_WORLD_BOSS_TIRE)
	RemindManager.Instance:Fire(RemindName.Boss_World)
	if Scene.Instance:GetSceneType() == SceneType.WorldBoss then
		local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
		local has_times = wb_other_cfg.world_boss_day_default_times + protocol.world_boss_day_extra_kill_times + protocol.world_boss_flush_can_kill_times
						- protocol.world_boss_day_kill_times
		-- if has_times < 1 and not BossAssistWGData.Instance:IsAssist() and not FuBenPanelCountDown.Instance:IsOpen()  then
		-- 	FuBenPanelCountDown.Instance:SetTimerInfo(TimeWGCtrl.Instance:GetServerTime() + 30, function ()
		-- 		FuBenWGCtrl.Instance:SendLeaveFB()
		-- 	end)
		-- end
	end
end

-- boss之家进入次数信息
function BossWGCtrl:OnBossHomeEnterTimeInfo(protocol)
	self.data:SetBossHomeEnterTimeInfo(protocol)
	self.view:Flush(TabIndex.boss_vip)
	self.vip_time_view:Flush()
    GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
    GlobalEventSystem:Fire(OtherEventType.BossInfoChange, BOSS_TASK_TYPE.VIP)
	RemindManager.Instance:Fire(RemindName.Boss_Vip)
	self:SetRoleTiredIcon(TabIndex.boss_vip)
	--if Scene.Instance:GetSceneType() == SceneType.VIP_BOSS then
	--	local wb_other_cfg =ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
	--	local has_times = wb_other_cfg.boss_home_day_default_times + protocol.boss_home_day_item_add_kill_times + protocol.boss_home_day_vip_buy_kill_times
	--					- protocol.boss_home_day_kill_times
	--	if has_times < 1 and not BossAssistWGData.Instance:IsAssist() and not FuBenPanelCountDown.Instance:IsOpen()  then
	--		FuBenPanelCountDown.Instance:SetTimerInfo(TimeWGCtrl.Instance:GetServerTime() + 30, function ()
	--			FuBenWGCtrl.Instance:SendLeaveFB()
	--		end)
	--	end
	--end
end

--个人boss增加次数请求
function BossWGCtrl.SendPersonBossBuyTimesReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSPersonBossBuyTimesReq)
	protocol:EncodeAndSend()
end

--深渊boss增加次数请求
function BossWGCtrl.SendShenyuanBossBuyTimesReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSShenyuanBossBuyTimesReq)
	protocol:EncodeAndSend()
end

--boss之家购买次数
function BossWGCtrl.SendBossHomeBossBuyTimesReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSBossHomeBossBuyTimesReq)
	protocol:EncodeAndSend()
end

------------------------------
--原有接口
------------------------------
function BossWGCtrl:OpenFirstRewardView(boss_id)
    self.first_kill_reward_view:SetBossId(boss_id)
    self.first_kill_reward_view:Open()
end

function BossWGCtrl:SendVipAllReq()
	BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.ALLINFO)
end

function BossWGCtrl:OnSceneChangeComplete(old_scene_type,new_scene_type)
	self.data:ClearOnwerInfo()
end

function BossWGCtrl:Open(tab_index, param_t)
	-- if IS_ON_CROSSSERVER then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
	-- 	return
	-- end
	if param_t and param_t.open_boss_id then
		local enum = {
			[BossWGData.BossType.WORLD_BOSS] = BossViewIndex.WorldBoss,
			[BossWGData.BossType.VIP_BOSS] = BossViewIndex.VipBoss,
			[BossWGData.BossType.PERSON_BOSS] = BossViewIndex.PersonalBoss,
			[BossWGData.BossType.DABAO_BOSS] = BossViewIndex.DabaoBoss,
			[BossWGData.BossType.KF_BOSS] = TabIndex.worserv_boss_mh,
			[BossWGData.BossType.SG_BOSS] = TabIndex.worserv_boss_sgyj,
            [BossWGData.BossType.HMSY_BOSS] = TabIndex.worserv_boss_hmsy,
            [BossWGData.BossType.ShenYuanBoss] = TabIndex.world_new_shenyuan_boss,
		}

		local boss_data = BossWGData.Instance:GetBossInfoByBossId(param_t.open_boss_id)
		if boss_data ~= nil then
			if boss_data.boss_type == BossWGData.BossType.VIP_BOSS then
				self.view:Open(enum[boss_data.boss_type])
				self.view:Flush(enum[boss_data.boss_type], "jum_info", {boss_list_index = boss_data.boss_index or 1, boss_list_layer = boss_data.layer + 1, boss_type = enum[boss_data.boss_type]})
            elseif boss_data.boss_type == BossWGData.BossType.KF_BOSS or boss_data.boss_type == BossWGData.BossType.HMSY_BOSS
            or boss_data.boss_type == BossWGData.BossType.ShenYuanBoss then
				ViewManager.Instance:Open(GuideModuleName.WorldServer, enum[boss_data.boss_type])
				ViewManager.Instance:FlushView(GuideModuleName.WorldServer, enum[boss_data.boss_type], "jum_info", {boss_list_index = boss_data.boss_index + 2 or 1, boss_list_layer = boss_data.layer, boss_type = enum[boss_data.boss_type]})
			else
				self.view:Open(enum[boss_data.boss_type])
				self.view:Flush(enum[boss_data.boss_type], "jum_info", {boss_list_index = boss_data.boss_index or 1, boss_list_layer = boss_data.layer, boss_type = enum[boss_data.boss_type]})
			end
			return
		end
	end
	self.view:Open(tab_index, param_t)
end

function BossWGCtrl:OpenBossViewByScene(old_scene_type, new_scene_type)
	--策划说屏蔽 从boss场景退出打开界面

	-- if self.data:GetSetNoOpenViewFlag() then
	-- 	return false
	-- end

	-- if old_scene_type ~= nil and new_scene_type ~= nil then
	-- 	if BossWGData.BossViewScene[new_scene_type] then
	-- 		return
	-- 	end
	-- end

 --    local scene_type = Scene.Instance:GetSceneType()
	-- if BossWGData.BossViewScene[scene_type] then
	-- 	local info = BossWGData.Instance:GetIsInBossScene(scene_type)
	-- 	if info.layer ~= nil or info.index ~= nil then
	-- 		--self:Open(tab_index, parm)
	-- 		self.data:SetBossTuJianIndex(info.view_index, info.layer, info.index, scene_type)

	-- 		if scene_type == SceneType.KF_BOSS or scene_type == SceneType.SG_BOSS or
	-- 				scene_type == SceneType.HONG_MENG_SHEN_YU or scene_type == SceneType.Shenyuan_boss then
	-- 			ViewManager.Instance:Open(GuideModuleName.WorldServer, info.view_index)
	-- 		else
	-- 			ViewManager.Instance:Open(GuideModuleName.Boss, info.view_index)
	-- 		end
	-- 	end
	-- end
end

function BossWGCtrl:FulshWorldBossTired()
	if self.view:IsLoadedIndex(TabIndex.boss_world) then
		self.view:OnFlushWorldBossView()
	end
end

function BossWGCtrl:FulshScrectBossTired()
	if self.view:IsOpen() then
		self.view:SetMJPhysical()
	end
end

function BossWGCtrl:SetGuaJiPosInfo(scene_id, x, y, range, monster_id)
	if scene_id == nil or x == nil or y == nil or range == nil then
		return
	end

	local cur_scene_id = Scene.Instance:GetSceneId()
	if scene_id ~= cur_scene_id then
		return
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		local mian_role = Scene.Instance:GetMainRole()
		if mian_role ~= nil and not mian_role:IsDeleted() then
			local pos_x, pos_y = mian_role:GetLogicPos()
			if GameMath.GetDistance(x, y, pos_x, pos_y, false) <= range * range then
				scene_logic:SetGuaJiInfoPos(pos_x, pos_y, range, monster_id)
			end
		end
	end
end


function BossWGCtrl:OpenCurViewIndex(view_name)
    local scene_id = Scene.Instance and Scene.Instance:GetSceneId()
    local tabIndex, layer = nil, nil
    if scene_id then
        local info = self.data:GetBossTypeBySceneId(scene_id)
        if info and info.view_name == view_name then
            tabIndex = info.view_tab_index
            layer = info.boss_view_layer
            self.data:SetBossTuJianIndex(tabIndex, layer)
        end
    end
    ViewManager.Instance:Open(view_name, tabIndex)
end

function BossWGCtrl:InitGoToPos()
	local type1,layer, boss_id, reason,pos_x,pos_y = BossWGData.Instance:GetCurSelectBossID()
	local data = BossWGData.Instance:GetBossInfoByBossId(boss_id)
	if Scene.Instance:GetSceneType() == SceneType.WorldBoss then
		local cur_boss = BossWGData.Instance:GetWorldBossPos(boss_id)
		data = cur_boss or data
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:ClearGuaJiInfo()
	end

	if data then
        Scene.Instance:ClearAllOperate()
        local sence_id = Scene.Instance:GetSceneId()

		MoveCache.end_type = MoveEndType.FightByMonsterId
		GuajiCache.monster_id = boss_id
		MoveCache.param1 = boss_id
		if not data.x_pos or not data.y_pos then return end
		local data_x_pos = data.x_pos
		local data_y_pos = data.y_pos
		if reason == SELECT_BOSS_REASON.CONVENE then
			if not pos_x or not pos_y then
				return
			end
			data_x_pos = pos_x
			data_y_pos = pos_y
		end

		local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
		local function call()
			local new_scene = Scene.Instance:GetSceneId()
			if sence_id ~= new_scene then
				if IS_DEBUG_BUILD then
					print_error("内网日志：BOSS寻路场景错误")
				end
				return
			end

			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
					if reason ~= nil and reason == SELECT_BOSS_REASON.VIEW then
						self:SetGuaJiPosInfo(sence_id, data_x_pos, data_y_pos, range, boss_id)
					end

					GuajiWGCtrl.Instance:StopGuaji()
					GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				end)

			GuajiWGCtrl.Instance:MoveToPos(sence_id, data_x_pos, data_y_pos, range)
		end

		if not TaskWGCtrl.Instance:IsFly() then
			call()
		else
			TaskWGCtrl.Instance:AddFlyUpList(call)
		end
	end
end

function BossWGCtrl:Close()
	self.view:Close()
end

function BossWGCtrl:ArticeClose()

end

function BossWGCtrl:DabaoBossBossEquipShow()
	if self.dabao_boss_equip_show_view and not self.dabao_boss_equip_show_view:IsOpen() then
		self.dabao_boss_equip_show_view:Open()
	end
end

function BossWGCtrl:OpenBossQuickRebirthShow()
	if self.boss_quick_rebirth_view and not self.boss_quick_rebirth_view:IsOpen() then
		self.boss_quick_rebirth_view:Open()
	end
end

-- 打开特殊复活面板
function BossWGCtrl:OpenSpecialFuhuoView(data)
    self.special_fuhuo_view:SetData(data)
end

-- 打开Vip等级不足面板
function BossWGCtrl:OpenVipNotEnoughView(data)
    self.vip_not_enough_view:SetData(data)
	self.vip_not_enough_view:Open()
end

-- 打开Boss奖励面板
function BossWGCtrl:OpenBossRewardView(cfg)
	self.boss_reward_view:SetData(cfg)
end

function BossWGCtrl:CloseBossRewardView()
	if self.boss_reward_view and self.boss_reward_view:IsOpen() then
		self.boss_reward_view:Close()
	end
end

-- 打开击杀记录面板
function BossWGCtrl:OpenKillRecordView()
	self.boss_kill_record_view:Open()
end

-- 打开Boss首杀弹窗
function BossWGCtrl:OpenFirstKillView(jump, tab_index, layer, boss_id)
	BossWGData.Instance:SetCurFirstKillShowView(tab_index or BossViewIndex.VipBoss)
	self.boss_first_kill_view:Open()
	if jump and tab_index > -1 and layer > -1 and boss_id > -1 then
		self.boss_first_kill_view:Flush(nil, "jump_info", {tab_index = tab_index, layer = layer, boss_id = boss_id})
	end
end

-- 打开击杀记录面板并设置数据
function BossWGCtrl:OpenKillRecordAndSetData(data, is_from_first_kill)
    self.boss_kill_record_view:SetData(data, is_from_first_kill)
	self.boss_kill_record_view:Open()
end

-- 进入上古遗迹、神魔禁地、专属魔王
function BossWGCtrl:SetEnterBossComsunData(tiky_id_seq, enter_comsun, map_tip, consum_tip, ok_func)
	self.dabao_enter_view:SetEnterBossComsunData(tiky_id_seq, enter_comsun, map_tip, consum_tip, ok_func)
end

-- 世界地图内被击杀
function BossWGCtrl:OpenBeKilledView()
	self.bekilled_view:Open()
end

------------------------------------BossSceneLogicView-------------------------------------------------------

function BossWGCtrl:SetEliteMonsterList(flag)
	local boss_list
	if flag then
		boss_list = self.data:GetEliteListBySceneType(Scene.Instance:GetSceneType())
	else
		boss_list = self.old_boss_list
	end
	if boss_list == nil then return end
	if self.boss_list_view then
		self.boss_list_view:SetDataList(boss_list)
	end
end

function BossWGCtrl:OpenDriveView(boss_type)
	-- print_error("open  boos ====panel,boos_type",boss_type)
	boss_type = boss_type or Scene.Instance:GetSceneType()
	self.drive_out_dabao:setFromBossType(boss_type)
	self.drive_out_dabao:Open()
end

function BossWGCtrl:CloseDriveView()
	self.drive_out_dabao:Close()
end

function BossWGCtrl:OnEnterVipBoss()
    local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
    local _, boss_layer, boss_id = self.data:GetCurSelectBossID()
    if not boss_layer then
        return
    end
    local enter_vip_cfg = BossWGData.Instance:GetEnterVipCfgbyIndex(boss_layer)
    local boss_level = boss_layer - 1
    if boss_id ~= GameEnum.BossEnterPos then
        boss_level = BossWGData.Instance:GetVipBossLevelByBossId(boss_id)
    end
    if nil == enter_vip_cfg then
		print_log(string.format("OnEnterVipBoss Error, boss_layer: %s can not get enter_vip_cfg", boss_layer))
		return
    end
 
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    if role_lv < enter_vip_cfg.need_role_level then
        local lv_str = RoleWGData.GetLevelString(enter_vip_cfg.need_role_level)
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.LevelNotEnough, lv_str))
        return
    end

    if role_vip_level < enter_vip_cfg.pay_vip_level then --vip等级不满足进入等级
        local data = {}
        data.tip = string.format(Language.Boss.VipBossLvNotEnough, enter_vip_cfg.free_vip_level, NumberToChinaNumber(boss_layer))
        data.click_ok = function ()
            ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
        end
        BossWGCtrl.Instance:OpenVipNotEnoughView(data)
        return
    elseif role_vip_level >= enter_vip_cfg.free_vip_level then
        BossWGData.Instance:SetCurSelectBossID(BossViewIndex.VipBoss, boss_layer, boss_id)
        BossWGData.Instance:SetBossEnterFlag(false)
		BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.ENTER, boss_level, boss_id)
		BossWGData.Instance:SetOldBossID(BossViewIndex.VipBoss,boss_id)
    elseif role_vip_level < enter_vip_cfg.free_vip_level then --vip等级不足免费进入等级
        local enter_vip_comsun = enter_vip_cfg.pay_vip_consume_gold
        local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
        local cost_text = Language.Common.GoldText

        if bind_gold_num >= enter_vip_comsun then
            cost_text = Language.Common.BindGoldText
        end
        if self.vip_alert_view == nil then
            self.vip_alert_view = Alert.New()
        end
		self.vip_alert_view:SetOkString(Language.Boss.Enter)
		self.vip_alert_view:SetLableString(string.format(Language.Boss.VipNoenough, cost_text, enter_vip_comsun))
		self.vip_alert_view:SetOkFunc(
            function()
                BossWGData.Instance:SetCurSelectBossID(BossViewIndex.VipBoss, boss_layer, boss_id)
				BossWGData.Instance:SetBossEnterFlag(false)
				BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.ENTER, boss_level, boss_id)
				BossWGData.Instance:SetOldBossID(BossViewIndex.VipBoss,boss_id)
			end
		)
		self.vip_alert_view:Open()
    end
end

function BossWGCtrl:GuideEnterVipBoss()
	BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.ENTER, 0, 0,1)
end


function BossWGCtrl:GetBossView()
	return self.view
end
---------------------世界boss------------------------------

--下发世界boss的玩家复活疲劳buff
function BossWGCtrl:OnSCWorldBossRoleRealiveInfo(protocol)
    self.data:SetWorldBossRoleRealiveInfo(protocol)
    local main_role = Scene.Instance:GetMainRole()
    local obj_id = main_role.vo.obj_id
    if protocol.realive_count > 0 then
        local effectinfo = SCEffectInfo.New()
        effectinfo.buff_type = 0
        effectinfo.obj_id = obj_id
        effectinfo.effect_type = 0
        effectinfo.product_method = 0
        effectinfo.product_id = 0
        effectinfo.unique_key = EFFECT_CLIENT_TYPE.ECT_WORLD_BOSS_ROLE_REALIVE
        effectinfo.client_effect_type = EFFECT_CLIENT_TYPE.ECT_WORLD_BOSS_ROLE_REALIVE
        effectinfo.merge_layer = protocol.realive_count
        effectinfo.param_count = 0
        effectinfo.param_list = {}
        effectinfo.param_list[1] = 0
        effectinfo.param_list[2] = protocol.realive_time
        effectinfo.param_list[3] = protocol.die_time

        if Scene.Instance:GetSceneType() == SceneType.WorldBoss then
            FightWGData.Instance:OnEffectInfo(effectinfo)
        else
            FightWGData.Instance:OnEffectRemove(obj_id, EFFECT_CLIENT_TYPE.ECT_WORLD_BOSS_ROLE_REALIVE)
        end
        TipWGCtrl.Instance:ReFlushBuffTip()
    elseif protocol.realive_count == 0 then
        FightWGData.Instance:OnEffectRemove(obj_id, EFFECT_CLIENT_TYPE.ECT_WORLD_BOSS_ROLE_REALIVE)
        TipWGCtrl.Instance:ReFlushBuffTip()
    end
end

--下发世界boss信息
function BossWGCtrl:OnWorldBossInfo(protocol)
	self.data:SetWorldBossInfo(protocol)
	self.view:Flush(BossViewIndex.DabaoBoss)
	self.view:Flush(BossViewIndex.WorldBoss)
	--self.boss_scene_logic_panel:Flush()
	self:RefershBossStone()
	self:SetRoleTiredIcon(DAY_COUNT.DAYCOUNT_ID_WORLD_BOSS_TIRE)
	GlobalEventSystem:Fire(BOSS_SERVER_INFO.ALL_INFO, BossViewIndex.WorldBoss)
	GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:SetRoleTiredIcon(key, key2)
	local main_role = Scene.Instance:GetMainRole()
	local tire_value, max_tire_value = -1, -1
	local cur_info_scene = -1
	local scene_type = Scene.Instance:GetSceneType()
	if key == DAY_COUNT.DAYCOUNT_ID_WORLD_BOSS_TIRE then
        tire_value, max_tire_value = BossWGData.Instance:GetWorldBossTimes() --世界boss次数
        tire_value = max_tire_value - tire_value --世界boss疲劳
        cur_info_scene = SceneType.WorldBoss
	elseif key == DAY_COUNT.DAYCOUNT_ID_MJ_BOSS_TIRE then
		-- if scene_type == SceneType.MJ_BOSS then
			-- local own_power = BossWGData.Instance:GetNowOwnPower()
			-- main_role:SetAttr("tired_value_icon",own_power <= 0 and 1 or 0)
		-- end
		return
	elseif key == DAY_COUNT.DAYCOUNT_ID_SHENYU_BOSS_TIRE then
		if scene_type == SceneType.SHENGYU_BOSS then
			local max_tired = self.data:GetSacredCfg().other[1].daily_boss_tire
			main_role:SetAttr("tired_value_icon",key2 >= max_tired and 1 or 0)
		end
		return
	elseif key == TabIndex.boss_vip then--这里的daycount和tabindex存在潜在风险，以后有时间可以修复
        cur_info_scene = SceneType.VIP_BOSS
        local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
        if wb_other_cfg.boss_home_day_default_times == -1 then --无限
            return
        end
		local info = BossWGData.Instance:GetBossHomeEnterTimeInfo()
		local other_cfg = BossWGData.Instance:GetOtherCfg()
		tire_value = info.boss_home_day_kill_times or 0
		max_tire_value = other_cfg.boss_home_day_default_times + (info.boss_home_day_item_add_kill_times or 0) + (info.boss_home_day_vip_buy_kill_times or 0)
	end

	if key2 == TabIndex.worserv_boss_sgyj then
		tire_value, max_tire_value = BossWGData.Instance:GetSgBossTire()
		cur_info_scene = SceneType.SG_BOSS
	elseif key2 == TabIndex.worserv_boss_mh then
		cur_info_scene = SceneType.KF_BOSS
		tire_value, max_tire_value = BossWGData.Instance:GetCrossBossTire()
	elseif key2 == TabIndex.worserv_boss_hmsy then
		cur_info_scene = SceneType.HONG_MENG_SHEN_YU
		local cfg = BossWGData.Instance:GetHMSYOtherCfg()
		local hmsy_times, hmsy_add_times = BossWGData.Instance:GetHMSYAllTimesInfo()
		tire_value = hmsy_times
		max_tire_value = cfg.reward_times + hmsy_add_times
	end

	if cur_info_scene == scene_type then
        if tire_value and max_tire_value and tire_value ~= -1 then
			main_role:SetAttr("tired_value_icon",tire_value >= max_tire_value and 1 or 0)
		end
	end
end

 --获取BOSS信息
function BossWGCtrl:SendWorldBossReq(opera_type, param1, param2, param3)
	-- if BossView.ReqType.ENTER == opera_type then
	-- 	if not FuBenWGCtrl.CanEnterFuben() then
	-- 		return
	-- 	end

	-- 	local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(param1)
	-- 	if boss_server_info == nil then
	-- 		return
	-- 	end

	-- 	if (boss_server_info.next_refresh_time or 0) > TimeWGCtrl.Instance:GetServerTime() then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotReburn)
	-- 		return
	-- 	end
    -- end
	local protocol = ProtocolPool.Instance:GetProtocol(CSWorldBossReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

----------------------打宝boss现在用的----------------------
function BossWGCtrl:OnSCDongKuBossAllInfo(protocol)
	 --print_error("--------打宝boss现在用的----,",protocol)
	self.data:SetDabaoBossAllInfo(protocol)
    self:RefershBossStone()
    if self.view:IsOpen() then
        self.view:Flush(BossViewIndex.DabaoBoss)
    end
    GlobalEventSystem:Fire(OtherEventType.BossInfoChange, BOSS_TASK_TYPE.DABAO)
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
	RemindManager.Instance:Fire(RemindName.Boss_Dabao)
end

function BossWGCtrl:OnDabaoBossSceneInfo(protocol)
	 --print_error("errordab打宝场景 VIP_BOSS",protocol)
	self.data:SetDabaoBossAngryValue(protocol)
	MainuiWGCtrl.Instance:FlushView(0, "flush_boss_nuqi")
end

-- 单层信息
function BossWGCtrl:OnSCDongKuBossLayerInfo(protocol)
	 --print_error("layer,",protocol)
	self.data:SetDabaoBossLayer(protocol)
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:SendDongKuBossReq(opera_type, param1, param2, param3)
	if opera_type == BossView.ReqType.ENTER and not FuBenWGCtrl.CanEnterFuben() then
		return
	end
	--print_error("dabao", opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDongKuBossEnterReq)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnFlushCaveBossData()
	self.view:Flush(BossViewIndex.CaveBoss)
end
function BossWGCtrl:OnFlushWroldBossData()
	self.view:Open(BossViewIndex.WorldBoss)
end
----------------------Vip boss现在用的----------------------------
function BossWGCtrl:OnSCVipBossAllInfo(protocol)
	self.data:SetVipBossAllInfo(protocol)
	self.view:Flush(BossViewIndex.VipBoss)
	GlobalEventSystem:Fire(BOSS_SERVER_INFO.ALL_INFO, BossViewIndex.VipBoss)
	self:RefershBossStone()
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:OnSCVipBossLayerInfo(protocol)
	self.data:SetVipBossLayer(protocol.boss_list)
	GlobalEventSystem:Fire(BOSS_SERVER_INFO.ALL_INFO, BossViewIndex.VipBoss)
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:SendVipBossReq(opera_type, param1, param2, param3)
	if opera_type == BossView.ReqType.ENTER and not FuBenWGCtrl.CanEnterFuben() then
		return
    end
	local protocol = ProtocolPool.Instance:GetProtocol(CSVipBossEnterReq)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
	if self.data:GetSetNoOpenViewFlag() then
		self.data:GetSetNoOpenViewFlag(true)
	end
	-- print_error('请求VIPBoss 信息················································')
end
------------------------跨服boss--------------------------------
function BossWGCtrl:SendCrossBossReq(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossBossReq)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnCrossBossPlayerInfo(protocol)
	--print_error(protocol)
	self.data:SetCrossBossPalyerInfo(protocol)
	-- self.view:Flush(TabIndex.worserv_boss_mh)
	self:SetRoleTiredIcon(0, TabIndex.worserv_boss_mh)
    GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, nil, true, true)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_mh, "kf_concern")
	self:FlushWorldBossBuyView()
	RemindManager.Instance:Fire(RemindName.WorldServer_MHSS)
end

function BossWGCtrl:OnCrossBossBossKillRecord(protocol)
	--self.data:SetCrossBossBossKillRecord(protocol)
	self.data:SetBossKillRecordData(protocol)
	if IsEmptyTable(self.data:GetBossKillRecordData()) then  --没有击杀记录
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoKillHistory)
	else
		BossWGCtrl.Instance:OpenKillRecordView()
		ViewManager.Instance:FlushView(GuideModuleName.BossKillRecordView)
	end
	--BossWGCtrl.Instance:OpenKillRecordView()
	--self.kill_record_view:Flush()
end

function BossWGCtrl:OnCrossBossDropRecord(protocol)
	self.data:SetCrossBossDropRecord(protocol)
	self.view:Flush(BossViewIndex.KFDropRecord)
end

function BossWGCtrl:OnCrossBossSceneInfo(protocol)
	-- print_error("下发跨服boss",protocol)
	self.data:SetCrossBossSceneInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_mh, "kf_scene")
		-- for k, v in pairs(protocol.boss_list) do
		-- 	-- if v.next_flush_time > TimeWGCtrl.Instance:GetServerTime() then
		-- 		self:UpdataBossStone(v.boss_id)
		-- 	-- end
		-- end
	self:RefershBossStone()
	--self.kill_record_view:Flush()
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:OnCrossBossForenotice(protocol)
	local tire_value = self.data:GetCrossBossTire()

	if tire_value > 0 then
		local data = {}
		data.boss_id = protocol.boss_id
		data.boss_type = protocol.boss_type
		data.level = protocol.level
		data.scene_type = SceneType.KF_BOSS
		self.boss_forenotice_view:SetShowData(data)
		self.boss_forenotice_view:Open()
	end
end

----------------------------------------------------------------------------
-----------------------------鸿蒙神域----------------------------------------
----------------------------------------------------------------------------
function BossWGCtrl:OnSCHongMengBossSelfInfo(protocol)
--print_error("self", protocol)
	self.data:SetHMSYSelfInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_hmsy, "all", {WorldServerView.FLUSHPARAM.SELF})
	ViewManager.Instance:FlushView(GuideModuleName.BossHMSYTip)
	GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, nil, true, true)
	RemindManager.Instance:Fire(RemindName.WorldServer_HMSY)
	self:SetRoleTiredIcon(0, TabIndex.worserv_boss_hmsy)
end

function BossWGCtrl:OnSCHongMengBossSceneInfo(protocol)
	--print_error("scene", protocol)
	self.data:SetHMSYSceneInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_hmsy, "all", {WorldServerView.FLUSHPARAM.COMMON})
	ViewManager.Instance:FlushView(GuideModuleName.BossHMSYTip)
	self:RefershBossStone()
end

function BossWGCtrl:OnSCHongMengBossPreNotice(protocol)
	--print_error("Notice", protocol)
	local data = {}
	data.scene_id = protocol.scene_id
	data.boss_id = protocol.boss_id
	data.scene_index = protocol.scene_index
	data.scene_type = SceneType.HONG_MENG_SHEN_YU
	if self.data:HasHMSYEnoughTimes() then
		self.boss_forenotice_view:SetShowData(data)
		self.boss_forenotice_view:Open()
	end
end

--蛮荒神兽，上古遗迹，鸿蒙神域掉落
function BossWGCtrl:OnSCCrossDropItemInfo(protocol)
	--print_error("drop", protocol)
	self.data:SetHMSYDropInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BossDropRecord)
end

function BossWGCtrl:OnSCHongMengBossTotalScene(protocol)
	--print_error("total", protocol)
	self.data:SetHMSYTotalInfo(protocol)
	local info = protocol.scene_list[1]
	local cfg = self.data:GetHMSYLayerBySceneIdAndIndex(info.scene_id)
	if cfg.layer ~= WorldServerView.SCENETYPE.ORI then
		ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_hmsy, "sy_total", {WorldServerView.FLUSHPARAM.TOTAL})
	end
end

function BossWGCtrl:OnSCHongMengBossSceneInsideInfo(protocol)
	--print_error("total", protocol)
	self.data:SetHMSYInsideInfo(protocol)
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:ReloadUIServerName()
	end
    self:RefershBossStone()
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:SendHMSYOpera(opera_type,param1,param2,param3)
	--print_error("OperaReq------",opera_type,param1,param2,param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCAHongMengBossOpera)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:SendHMSYEnterReq(scene_id,scene_index)
	--print_error("OperaReq------",scene_id,scene_index)
	--if not self.data:HasHMSYEnoughTimes() then
	--	SysMsgWGCtrl.Instance:ErrorRemind(Language.HMSY.NoTimeSTip)
	--	return
	--end
	BossWGData.Instance:SetBossEnterFlag(true)

    local protocol = ProtocolPool.Instance:GetProtocol(CSCAHongMengBossEnterReq)
	--protocol.opera_type = opera_type or 0
	protocol.scene_id = scene_id or 0
	protocol.scene_index = scene_index or 0
    protocol:EncodeAndSend()
end

function BossWGCtrl:SendHMSYDropRecord()
    local protocol = ProtocolPool.Instance:GetProtocol(CSCrossDropRecordReq)
    protocol:EncodeAndSend()
end

------------------------------------------------------------------------------
------------------------------------------------------------------------------
-- boss刷新提醒
function BossWGCtrl:OnWorldBossForenotice(protocol)
	if BossWGData.SceneType[protocol.boss_type] == SceneType.WorldBoss then
		local wb_other_cfg =ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
		local world_times_info = self.data:GetWorldBossEnterTimeInfo()
		local max_times = wb_other_cfg.world_boss_day_default_times + world_times_info.world_boss_day_extra_kill_times
		if max_times - world_times_info.world_boss_day_kill_times + world_times_info.world_boss_flush_can_kill_times <= 0 then
			return
		end
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.VIP_BOSS then
		local wb_other_cfg =ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
        local vipboss_times_info = self.data:GetBossHomeEnterTimeInfo()
        if wb_other_cfg.boss_home_day_default_times == -1 then
        else
            local max_times = wb_other_cfg.boss_home_day_default_times + vipboss_times_info.boss_home_day_item_add_kill_times + vipboss_times_info.boss_home_day_vip_buy_kill_times
            if max_times - vipboss_times_info.boss_home_day_kill_times <= 0 then
                return
            end
        end
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.SG_BOSS then
		local enter_times, max_enter_times = self.data:GetSgBossEnterTimes()
		if enter_times >= max_enter_times then
			return
		end
		local tire_value, max_tire_value = BossWGData.Instance:GetSgBossTire()
		if tire_value >= max_tire_value then
			return
		end
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.DABAO_BOSS then
		local enter_times, max_times = self.data:GetDaBaoBossRemainEnterTimes()
		if enter_times >= max_times then
			return
		end
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.TIANSHEN_JIANLIN then
		-- ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.TIANSHENJIANLIN)
		return
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.WORLD_TREASURE_JIANLIN then
		-- ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.TIANSHENJIANLIN)
		return
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.XINGTIANLAIXI_FB then
		-- ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.XINGTIANLAIXI)
		return
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.MOWU_JIANLIN then
		-- ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
		return
	elseif BossWGData.SceneType[protocol.boss_type] == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then --寻宝boss
		TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
		local is_open,boss_id = TreasureBossWGData.Instance:NeedShowTreasureBossFlushTip(protocol.boss_id)
		if is_open then
			protocol.boss_id = boss_id
			--TreasureHuntWGData.Instance:SetOpenSelectEffect(true)
		else
			return
		end
	end

	local data = {}
	data.boss_type = protocol.boss_type
	data.level = protocol.level
	data.boss_id = protocol.boss_id
    data.scene_type = BossWGData.SceneType[protocol.boss_type]
    if protocol.boss_type == 5 then --深渊boss特殊处理
        local info = BossWGData.Instance:GetShenyuanBossData()
        local index = BossWGData.Instance:GetShenYuanBossDefaultIndex()
        data.boss_id = info[index].boss_id
        if FunOpen.Instance:GetFunIsOpened(FunName.ShenYuanBoss) and BossWGData.Instance:CheckShenyuanBossIsOpen() then
            self.boss_special_forenotice_view:SetShowData(data)
            self.boss_special_forenotice_view:Open()
        end
	elseif protocol.boss_type == 14 then --寻宝boss特殊处理
		TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
		TreasureHuntWGCtrl.Instance:ShowBossActivityTip()
    else
        self.boss_forenotice_view:SetShowData(data)
        self.boss_forenotice_view:Open()
    end
end

function BossWGCtrl:OnWorldBossOnDie(protocol)
	self.data:UpdateBossInfoWhenBossDie(protocol)
	if self.data:IsInBossSceneByBossId(protocol.boss_id) then
		self:UpdataBossStone(protocol.boss_id)
	end

	if Scene.Instance:GetSceneType() == SceneType.WorldBoss then
        self:SendWorldBossReq(BossView.ReqType.ALLINFO)
        FuBenWGCtrl.Instance:CloseTeamExpCheerView()
	end
	self.view:Flush()
    self.boss_forenotice_view:JudgeNeedCloseView(protocol.boss_id)
    GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

-- 掉落记录
function BossWGCtrl:OnWorldBossDropHistory(protocol)
	--print_error("drop", protocol)
	self.data:SetBossDropNewsInfo(protocol)
	--self.view:Flush(BossViewIndex.DropRecord)
	ViewManager.Instance:FlushView(GuideModuleName.BossDropRecord)
	-- self.view:Flush(BossViewIndex.KFDropRecord)
end

function BossWGCtrl:SendBossKillRecordReq(boss_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBossKillRoleRecordReq)
	protocol.boss_id = boss_id
	protocol:EncodeAndSend()
end

-- boss击杀记录
function BossWGCtrl:OnSCBossKillRoleRecordAck(protocol)
	self.data:SetBossKillRecordData(protocol)
	if IsEmptyTable(self.data:GetBossKillRecordData()) then  --没有击杀记录
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoKillHistory)
	else
		BossWGCtrl.Instance:OpenKillRecordView()
		ViewManager.Instance:FlushView(GuideModuleName.BossKillRecordView)
	end
end

function BossWGCtrl:OnWorldBossReliveTire(protocol)
	self.data:SetBossReliveTire(protocol)
	self.data:SetCrossReliveTire(protocol)
	FuhuoWGCtrl.Instance:FlushFuHuoView()
end

function BossWGCtrl:OnSCCrossBossReliveTire(protocol)
	-- self.data:SetCrossReliveTire(protocol)
	-- FuhuoWGCtrl.Instance:FlushFuHuoView()
end

function BossWGCtrl:RefreshTireValue()
	if Scene.Instance:GetSceneType() == SceneType.HONG_MENG_SHEN_YU then
		self:SetRoleTiredIcon(0, TabIndex.worserv_boss_hmsy)
    elseif Scene.Instance:GetSceneType() == SceneType.VIP_BOSS then
		self:SetRoleTiredIcon(TabIndex.boss_vip)
	end
end

-- 进入场景回调
function BossWGCtrl:EnterSceneCallback()
    local _, _, boss_id, reason,pos_x,pos_y = self.data:GetCurSelectBossID()
    if boss_id ~= nil then
        GuajiWGCtrl.Instance:StopGuaji()
        MainuiWGCtrl.Instance:SetLightBossId(boss_id)
    end

    self:ShowBossPosOnMapView()
    BossAssistWGCtrl.Instance:ClearSceneInfo()
    MainuiWGCtrl.Instance:SetOtherContents(false)
	self:CreateBossTaskList()
	if boss_id == GameEnum.BossEnterPos then
		self:MoveToTargetPos()
	elseif boss_id == GameEnum.TeamInvite then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic.BindObjCreateEvent then
			scene_logic:BindObjCreateEvent()
		end
		-- if scene_logic.AtkTeamLeaderTarget then
		-- 	scene_logic:AtkTeamLeaderTarget()
		-- end
	elseif boss_id == GameEnum.BossXiezhu then
		--新 boss协助
		if BossXiezhuWGData.Instance:IsGotoXiezhu() then
			BossXiezhuWGCtrl.Instance:GoToXizhuBoss()
		end
	else
	-- print_error("move to bossid :",boss_id,"boss cur_layer:",cur_layer)
		if reason == SELECT_BOSS_REASON.CONVENE then
			self:MoveToBoss(boss_id, reason,pos_x,pos_y)
		else
			self:MoveToBoss(boss_id, reason)
		end
	end
	
	self:RefreshTireValue()
	self:RefershBossStone()
	self.data:NotifyBossChangeCallBack(BindTool.Bind(self.RefershBossStone,self))
	if BossAssistWGData.Instance:IsAssist() then
		BossWGCtrl.Instance:GoToHelpBossAssist()
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.DABAO_BOSS then
		self:DabaoBossBossEquipShow()
	end
end

function BossWGCtrl:RefershBossStone()
	local scene_id = Scene.Instance:GetSceneId()
	local boss_list = self.data:GetBossListBySceneId(scene_id)
	local _, cur_layer = self.data:GetCurSelectBossID()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.KF_BOSS then
		boss_list = self.data:GetCrossLayerBossBylayer(cur_layer)
	elseif scene_type == SceneType.SG_BOSS then
		boss_list = self.data:GetSGBossListBySceneId(scene_id)
	elseif scene_type == SceneType.HONG_MENG_SHEN_YU then
        boss_list = self.data:GetHMSYBossListById(scene_id)
    elseif scene_type == SceneType.Shenyuan_boss then
        boss_list = self.data:GetShenYuanBossCfgSceneId(scene_id)
    elseif scene_type == SceneType.XianJie_Boss then
		boss_list = XianJieBossWGData.Instance:GetBossListBySceneId(scene_id)
	elseif scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
		boss_list = {}
		boss_list[1] = TreasureBossWGData.Instance:GetHuntBossInfoByScene(scene_id)
	end

	if boss_list == nil then return end
	local scene_index
	for i, v in pairs(boss_list) do
		local boss_info = self.data:GetBossRefreshInfoByBossId(v.boss_id)
		if boss_info == nil then
			if scene_type == SceneType.KF_BOSS then
				boss_info = self.data:GetCrossBossById(v.boss_id)
			elseif scene_type == SceneType.SG_BOSS then
				boss_info = self.data:GetSGBossInfoByBossId(v.boss_id)
			elseif scene_type == SceneType.HONG_MENG_SHEN_YU then
				scene_index = self.data:GetCurHMSYSceneIndex()
                boss_info = self.data:GetBossRefreshInfoByBossId(v.boss_id, scene_index)
            elseif scene_type == SceneType.XianJie_Boss then
                boss_info = XianJieBossWGData.Instance:GetBossCfgById(v.boss_id)
            elseif scene_type == SceneType.Shenyuan_boss then
				boss_info = self.data:GetShenYuanBossInfoById(v.boss_id)
			elseif scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
				boss_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(v.boss_id)
			end
		end
		if boss_info ~= nil then
			self:UpdataBossStone(v.boss_id, scene_index)
 		end
	end
end

-- 场景中boss死了创建墓碑
function BossWGCtrl:UpdataBossStone(boss_id, scene_index)
	local boss_stone_list = Scene.Instance:GetBossStoneList()
	local boss_server_info = self.data:GetBossRefreshInfoByBossId(boss_id, scene_index)
	if boss_server_info == nil then
		boss_server_info = self.data:GetCrossBossById(boss_id)
    end

    if boss_server_info == nil then
		boss_server_info = self.data:GetShenYuanBossInfoById(boss_id)
    end

    if boss_server_info == nil then
    	boss_server_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(boss_id)
    end

    if boss_server_info == nil then
    	boss_server_info = XianJieBossWGData.Instance:GetBossCfgById(boss_id)
    end

	if boss_server_info == nil then return end
	for k, v in pairs(boss_stone_list)do
		if v:GetVo().boss_id == boss_id then
			v:SetBossStoneInfo(boss_server_info.next_refresh_time)
			return
		end
	end
	self:CreateBossStone(boss_id, scene_index)
end

function BossWGCtrl:CreateBossStone(boss_id, scene_index)
	local boss_info = self.data:GetBossInfoByBossId(boss_id)
	local boss_server_info = self.data:GetBossRefreshInfoByBossId(boss_id, scene_index)
	if Scene.Instance:GetSceneType() == SceneType.KF_BOSS then
		boss_info = self.data:GetCrossBossInfoByBossId(boss_id)
		boss_server_info = self.data:GetCrossBossById(boss_id)
	elseif Scene.Instance:GetSceneType() == SceneType.SG_BOSS then
        boss_info = self.data:GetSGAllBossByBossId(boss_id)
    elseif Scene.Instance:GetSceneType() == SceneType.Shenyuan_boss then
        boss_info = self.data:GetShenYuanBossDataInfoByid(boss_id)
        boss_server_info = self.data:GetShenYuanBossDataInfoByid(boss_id)
        if boss_info.status == 1 or boss_server_info.next_refresh_time == 0 then --boss还存活
            return
        end
    elseif Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
        boss_info = XianJieBossWGData.Instance:GetBossCfgById(boss_id)
        if boss_info.next_refresh_time == 0 then --boss还存活
            return
        end
    elseif Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
    	boss_info = TreasureBossWGData.Instance:GetTreasureDataByBossId(boss_id)
        boss_server_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(boss_id)
        if boss_server_info.status == 1 or boss_server_info.next_refresh_time == 0 then --boss还存活
            return
        end
	end

    if boss_info == nil or boss_server_info == nil or boss_info.scene_id ~= Scene.Instance:GetSceneId() then
        return
    end
	local stone_vo = GameVoManager.Instance:CreateVo(BossStoneVo)   -- 创建一个VO对象  参数为vo类
	stone_vo.pos_x = boss_info.x_pos
	stone_vo.pos_y = boss_info.y_pos
	stone_vo.name = boss_info.boss_name or ""
	stone_vo.boss_level = boss_info.boss_level
    stone_vo.live_time = boss_server_info.next_refresh_time
	stone_vo.boss_id = boss_info.boss_id
	stone_vo.obj_id = Scene.Instance:GetSceneClientId()
	Scene.Instance:CreateObj(stone_vo, SceneObjType.BossStoneObj)   -- 创建一个场景对象 参数为 vo对象 和场景对象的类型
end

function BossWGCtrl:CreateBossTaskList()			--BOSS进入场景报错注释
	local boss_type, boss_layer, _ = self.data:GetCurSelectBossID()

	local boss_list = self.data:GetBossListBySceneId(Scene.Instance:GetSceneId())
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type == SceneType.KF_BOSS then
		boss_list = self.data:GetCrossLayerBossAndGatherBylayer(boss_layer)
	elseif scene_type == SceneType.SG_BOSS then
		boss_list = self.data:GetSGBossListBySceneId(Scene.Instance:GetSceneId())
	elseif scene_type == SceneType.HONG_MENG_SHEN_YU then
		local scene_id = Scene.Instance:GetSceneId()
        boss_list = BossWGData.Instance:GetHMSYBossListById(scene_id)
    elseif scene_type == SceneType.XianJie_Boss then
        local scene_id = Scene.Instance:GetSceneId()
        boss_list = XianJieBossWGData.Instance:GetBossListBySceneId(scene_id)
	elseif scene_type == SceneType.CROSS_EVERYDAY_RECHARGE_BOSS then
		local scene_id = Scene.Instance:GetSceneId()
		boss_list = self.data:GetBossListBySceneId(scene_id)
	elseif scene_type == SceneType.HUNDRED_EQUIP then
		local scene_id = Scene.Instance:GetSceneId()
        boss_list = HundredEquipWGData.Instance:GetBossListBySceneId(scene_id)
	end

	if boss_list == nil then return end
    self.old_boss_list = boss_list
  
	-- self.boss_list_view:SetDataList(boss_list)
	GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, boss_list, true)
end
function BossWGCtrl:CreateBossAscription()
	ViewManager.Instance:Open(GuideModuleName.Boss_ShengYu_FollowUi)
end

function BossWGCtrl:OutSceneCallback(need_ticket)
	BossAssistWGCtrl.Instance:ClearGuildAssistBossDamageListInfo()
	MainuiWGCtrl.Instance:SetOtherContents(true)
    BossWGData.Instance:ClearOnwerInfo()
    self:CloseXianJieTiredView()
	if need_ticket then
		BossAssistWGCtrl.Instance:ClearNormalHurtList()
		GlobalEventSystem:Fire(OtherEventType.SHOW_BOSS_CHANGE, false)
	end
	self.dabao_boss_equip_show_view:Close()
end

function BossWGCtrl:MoveToBoss(boss_id, reason,pos_x,pos_y)
	local boss_cfg = self.data:GetBossAllInfoByBossId(boss_id)
	if nil == boss_cfg then return end
	Scene.Instance:ClearAllOperate()
	
	local boss_pos_x = boss_cfg.x_pos
	local boss_pos_y = boss_cfg.y_pos
	if reason == SELECT_BOSS_REASON.CONVENE then
		if not pos_x then
			return
		end
		boss_pos_x = pos_x
		boss_pos_y = pos_y
	end

	if not (boss_cfg.x_pos or boss_cfg.flush_pos_x) then
		return
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:ClearGuaJiInfo()
	end

	local function call()
		local new_scene = Scene.Instance:GetSceneId()
		if boss_cfg~= nil and boss_cfg.scene_id ~= new_scene then
			if IS_DEBUG_BUILD then
				print_error("内网日志：BOSS寻路场景错误", boss_cfg)
			end

			return
		end

		local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
		MoveCache.end_type = MoveEndType.FightByMonsterId
		GuajiCache.monster_id = boss_id
		MoveCache.param1 = boss_id

		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
				if reason ~= nil and reason == SELECT_BOSS_REASON.VIEW then
					self:SetGuaJiPosInfo(boss_cfg.scene_id, boss_pos_x or boss_cfg.flush_pos_x, boss_pos_y or boss_cfg.flush_pos_y, range, boss_id)
				end

				--GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)

		GuajiWGCtrl.Instance:MoveToPos(boss_cfg.scene_id,
				boss_pos_x or boss_cfg.flush_pos_x,
				boss_pos_y or boss_cfg.flush_pos_y,
				range)
	end

	if not TaskWGCtrl.Instance:IsFly() then
		call()
	else
		TaskWGCtrl.Instance:AddFlyUpList(call)
	end
	-- Scene.Instance:GetSceneLogic():MoveToPos(boss_cfg.x_pos, boss_cfg.y_pos, nil, true)
end

function BossWGCtrl:ShowBossPosOnMapView()
	-- MapWGCtrl.Instance:AddTagToMapViewBySceneId(Scene.Instance:GetSceneId())
end

-- boss死亡广播
function BossWGCtrl:OnWorldBossDead(protocol)
	self:SendWorldBossReq(BossView.ReqType.ALLINFO)
end

-- 个人boss
function BossWGCtrl:GetPersonBossRemind()
	return self.data:GetPersonBossRemindNum()
end

function BossWGCtrl:CheckBossTujianRemind()
	return self.data:GetBossTujianRemind()
end

-------------------上古遗迹---------------------------
function BossWGCtrl:SendShangGuBossReq(opera_type, param1, param2)
	--print_error("opera", opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShangGuYiJiOperaReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	--protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:SendSGEnterReq(layer)
	if not FuBenWGCtrl.CanEnterFuben() then
		return
	end
	--print_error('enter', layer)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShangGuYiJiEnterOpera)
	protocol.layer = layer
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnSCShangGuYiJIAllInfo(protocol)
	 --print_error("all", protocol)
	BossWGData.Instance:SetSgBossAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_sgyj)
	--self:SetRoleTiredIcon(0, TabIndex.worserv_boss_sgyj)
	self:RefershBossStone()
	GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:OnSCShangGuYiJiSceneInfo(protocol)
	 --print_error("scene", protocol.hidden_boss_id_list)
	BossWGData.Instance:SetSgBossLayer(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_sgyj)
	--GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
	local boss_list = self.data:GetSGBossListBySceneId(Scene.Instance:GetSceneId())
	GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, boss_list, true)
end

function BossWGCtrl:OnSCShangGuYiJiSceneUserInfo(protocol)
	 --print_error("scene_user",protocol)
	self.data:SetDabaoBossAngryValue(protocol)
	MainuiWGCtrl.Instance:FlushView(0, "flush_boss_nuqi")
	self:RefershBossStone()
end

function BossWGCtrl:OnSCShangGuYiJiUserInfo(protocol)
	--print_error("user",protocol)
	self.data:SetShangGuBossSceneOtherInfo(protocol)
	self:SetRoleTiredIcon(0, TabIndex.worserv_boss_sgyj)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_sgyj, "sg_concern")
	GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, nil, true, true)
	RemindManager.Instance:Fire(RemindName.WorldServer_SGYJ)
end
----------------------------------------------------------------------------------
function BossWGCtrl:GetBossViewShowIndex()
	if self.view:IsOpen() then
		return self.view:GetShowIndex()
	end
end

function BossWGCtrl:OnSCBossCardAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Achievement)--成就主界面按钮

	-- 山海经逻辑
	ShanHaiJingWGData.Instance:SetSLServerData(protocol)
	-- 刷新收录界面
	--[[RemindManager.Instance:Fire(RemindName.ShanHaiJing_SL_CW)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_SL_TS)

	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView,TabIndex.shj_chongwu)
	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView,TabIndex.shj_tianshen)--]]
end

function BossWGCtrl:SendBossTuJianReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBossCardReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-----------------------------------------------------

--boss召唤令
function BossWGCtrl:OpenCallBossView()
	if self.call_boss_view then
		self.call_boss_view:Open()
	end
end
----召唤boss归属，策划不要，服务端没下发
--function BossWGCtrl:OnSCBossOwnerChangeInfo(protocol)
--	local obj = Scene.Instance:GetObjectByObjId(protocol.user_id)
--	local time_quest
--
--	local function result_fun()
--		if protocol.is_show_title == 0 then
--			-- obj:SetAttr("call_boss_title", 0)
--			-- obj:UpdateNameBoard()
--		else
--			-- obj:SetAttr("call_boss_title", 1)
--			-- obj:UpdateNameBoard()
--		end
--	end
--	local function call_fun()
--		obj = Scene.Instance:GetObjectByObjId(protocol.user_id)
--		if obj then
--			GlobalTimerQuest:CancelQuest(time_quest)
--			result_fun()
--		end
--	end
--
--	if not obj then
--		time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(call_fun), 1)
--	else
--		result_fun()
--	end
--end
---------------------------圣域boss-------------------------------------------------
function BossWGCtrl:SendSacredBossReq(type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSacredBossReq)
	protocol.type = type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnSCSacredBossPlayerInfo(protocol)
	self.data:SacredBossTireInfo(protocol)
	self:SetRoleTiredIcon(DAY_COUNT.DAYCOUNT_ID_SHENYU_BOSS_TIRE, protocol.boss_tire)
	if self.view:IsOpen() then
		self.view:Flush(BossViewIndex.ShengYuBoss)
	end
end

--圣域已屏蔽
function BossWGCtrl:OnSCSacredBossSceneInfo(protocol)
	--self.data:SetSacredBossSceneInfo(protocol)
	--if self.shenyu_rank_list:IsOpen() then
	--	self.shenyu_rank_list:Flush()
	--end
	--
	--self:RefershBossStone()
	--if protocol.kick_out_timestamp > 0 then
	--	FuBenPanelCountDown.Instance:SetTimerInfo(protocol.kick_out_timestamp)
	--end
end

-- function BossWGCtrl:SacredBossSceneAllInfo(protocol)
	-- self.data:SetSacredBossSceneInfo1(protocol)
-- end

function BossWGCtrl:OnSCSacredBossBossKillRecord(protocol)
	self.data:SetSacredBossBossKillRecord(protocol)
	--BossWGCtrl.Instance:SetShengyuBossRecord(BossKillRecordView.RecordType.shengyu)
	--self.kill_record_view:Flush()
end

function BossWGCtrl:OnSCSacredBossDropRecord(protocol)
	self.data:SetSacredBossDropRecord(protocol)
	self.view:Flush(BossViewIndex.KFDropRecord)
end

function BossWGCtrl:OnSCSacredBossForenotice(protocol)
	-- print_error("OnSCSacredBossForenotice",protocol)
	self.boss_forenotice_view:SetShowData(protocol)
	self.boss_forenotice_view:Open()
end

function BossWGCtrl:OnSCSacredBossOwnBossInfoChange(protocol)
	self.data:SetSacredBossOwnBossInfoChange(protocol)
end

function BossWGCtrl:OutShengyuSceneCallback()
	self.view:OnClickShengyuAscriptionEnd()
end

function BossWGCtrl:ShengyuAscriptionUpdate()
	self.view:ShengyuAscriptionUpdate()
end

function BossWGCtrl:CloseSYBattleFollow()
	if self.shengyin_view then
		self.shengyin_view:DestroyTaskView()
	end
end

function BossWGCtrl:OpenSYAttInfo()
	self.sy_att_view:Open()
end

function BossWGCtrl:GetXuShiCallback()
		local enter_comsun = BossWGData.Instance:GetTicketNeed()
		local tiky_id = BossWGData.Instance:GetShenYunTicketID()
		local  is_max = BossWGData.Instance:MaxGetintoNum()
		local is_xushi = 1
		if is_max then
			BossWGCtrl.Instance:SetSYBossTimeView(is_xushi,tiky_id, enter_comsun.need_ticket,Language.Boss.XuShiSYBoss, Language.Boss.XuShiBossConsum,function()
				BossWGCtrl.Instance:SendSYXuShiBossReq(GameEnum.SHENGYINFB_TYPE_BUY_JINGLI)
				self.sy_ticket_time_view:Close()
			end)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.MaxGointo)
		end
end

function BossWGCtrl:OpenNoRewardView()
	self.noreward_view:Open()
end

function BossWGCtrl:GoEnterBossScene()
	self.view:GoEnterBossScene()
end

--------------------神陨------------------
function BossWGCtrl:OnShenYunFushTime(protocol)
	-- print_error("陨落 OnShenYunFushTime >>>>>> ",protocol)
	self.data:OnSyFBTimeFush(protocol)
	if BossWGCtrl.Instance:GetBossViewShowIndex() == BossViewIndex.SYBOSS  then
		self.view:OnFlushSYBossInfo()
	end
	if Scene.Instance:GetSceneType() == SceneType.KFSHENYUN_FB then
		self.shengyin_view:FlushShenYuView()
	end
end

function BossWGCtrl:OnShenYunTutengInfo(protocol)
	-- print_error("陨落++++++++++++",protocol)
	self.data:ShenYunTuTengObj(protocol)


	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role.vo.obj_id = main_role:GetObjId()
		--main_role:UpdateNameBoard()
	end

	local role_list = Scene.Instance:GetRoleList()
	for k ,v in pairs(role_list) do
		v.vo.obj_id = v:GetObjId()
		--v:UpdateNameBoard()
	end

	GlobalEventSystem:Fire(ObjectEventType.STOP_GATHER)
end

function BossWGCtrl:OnShenYUNJinliInfo(protocol)
	-- print_error("陨落 >>>>>> ", protocol)
	self.data:GetShenYinMapInfo(protocol)
	if Scene.Instance:GetSceneType() == SceneType.KFSHENYUN_FB then
		self.shengyin_view:FlushShenYuView()
	end
end
function BossWGCtrl:SetSYBossComsunData(is_xushi,tiky_item_id, enter_comsun, map_tip, consum_tip, ok_func)
	self.sy_ticket_view:SetSYBossComsunData(is_xushi,tiky_item_id, enter_comsun, map_tip, consum_tip, ok_func)
end

function BossWGCtrl:SetSYBossTimeView(is_xushi,tiky_item_id, enter_comsun, map_tip, consum_tip, ok_func)
	self.sy_ticket_time_view:SetSYBossComsunData(is_xushi,tiky_item_id, enter_comsun, map_tip, consum_tip, ok_func)
end

function BossWGCtrl:SendSYXuShiBossReq(opera_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossShengYinFbReq)
	protocol.opera_type = opera_type or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:EnterMoveSYBoss()
	local boss_type, cur_layer, boss_id = self.data:GetCurSelectBossID()
	local boss_list = self.data:GetShenYunBylayer(cur_layer)
	if not boss_list then return end
	for i, v in pairs(boss_list) do
		if boss_id == v.boss_id then
	   		Scene.Instance:ClearAllOperate()
	        local sence_id = Scene.Instance:GetSceneId()

			MoveCache.end_type = MoveEndType.FightByMonsterId
			GuajiCache.monster_id = boss_id
			MoveCache.param1 = boss_id
			if not v.x_pos or not v.y_pos then return end

			local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
   			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
   				-- 切换挂机模式，以优先选择玩家 --
				GuajiWGCtrl.Instance:StopGuaji()
        		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    		end)
			GuajiWGCtrl.Instance:MoveToPos(sence_id,v.x_pos, v.y_pos, range)
		end

	end
end
function BossWGCtrl:OpenSyFbTask()
	 self.shengyin_view:Open()
end
function BossWGCtrl:SetShengyuBossRecord(is_shengyu)
	--self.kill_record_view:SetShengyuBossRecord(is_shengyu)
	--self.kill_record_view:Open()
end


-------------------------------------------------------------------------------------------
--秘境boss

--所有信息
function BossWGCtrl:OnSCScsecretBossInfo(protocol)
	-- print_error("所有信息",protocol.layer_list)
	self.data:SetMiJingBossInfo(protocol)
	if self.mj_boss_view:IsOpen() then
		self.mj_boss_view:Flush()
	end
	if BossWGData.Instance:GetCurCanFlush(protocol.is_cross,self.view:GetShowIndex()) then
		self.view:refreshBossList()
	end
	self:RefershBossStone()
	self:SetRoleTiredIcon(DAY_COUNT.DAYCOUNT_ID_MJ_BOSS_TIRE)
end

--单层信息
function BossWGCtrl:OnSCSecretBossLayerInfo( protocol )
	-- print_error("单层信息",protocol)
	self.data:SetMiJingBossLayer(protocol.boss_list)
end

--体力信息返回
function BossWGCtrl:OnSCSecretBossPhysicalInfo( protocol )
	-- print_error("体力信息返回",protocol)
	self.data:SetNowPhysicalInfo(protocol)
	self.view:SetMJPhysical()
	self.view:OnFlushMiJingBossView()
	self:SetRoleTiredIcon(DAY_COUNT.DAYCOUNT_ID_MJ_BOSS_TIRE)
	if self.mj_boss_view:IsOpen() then
		self.mj_boss_view:Flush()
	end
end

--获取体力信息
function BossWGCtrl:SendCSSecretBossPhysicalReq(opera_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSecretBossPhysicalReq)
	protocol.opera_type = opera_type
	protocol:EncodeAndSend()
end

--本服秘境boss操作
function BossWGCtrl:SendMiJingBossReq( opera_type, param1, param2,param3 )
	-- print_error("本服秘境boss操作",opera_type,param1,param2,param3)
	if opera_type == BossView.SgReqType.ENTER and not FuBenWGCtrl.CanEnterFuben() then
		return
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSSecretBossReq)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:OpenMJBossList()
	self.mj_boss_view:Open()
end

function BossWGCtrl:CloseMJBossList()
	self.mj_boss_view:Close()
end

function BossWGCtrl:SetMJBossRecord( is_kf_mj )
	--self.kill_record_view:SetKFMJBossRecord(BossKillRecordView.RecordType.kf_mj)
	--self.kill_record_view:Open()
end

function BossWGCtrl:ResetLightSelectBoss()
	self.mj_boss_view:SetCurIndex(0)
end

function BossWGCtrl:CloseAllRecordView()
	--if self.kill_record_view:IsOpen() then
	--	self.kill_record_view:Close()
	--end
	--if self.boss_forenotice_view:IsOpen() then
	--	self.kill_record_view:Close()
	--end
end

function BossWGCtrl:OpenBossVipTimesView(vip_type)
	self.vip_time_view:SetVipType(vip_type)
	self.vip_time_view:Open()
end

function BossWGCtrl:FlushBossVipTimesView()
	self.vip_time_view:Flush()
end

function BossWGCtrl:OnSCWorldBossSpecialDropHistory(protocol)
	self.data:SetSGMJBossDrop(protocol)
	self.view:Flush(BossViewIndex.KFDropRecord)
end

function BossWGCtrl:GoToHelpGuildAssist(param)
    local scene_id = tonumber(param[2])
    self.data:GetSetBossHelpPos(param[3], param[4], param[5])
    if Scene.Instance:GetSceneId() == scene_id then
		self:MoveToTargetPos()
		return
	end
    local vip_cfg = BossWGData.Instance:GetEnterVipCfgbySceneid(scene_id)
    if vip_cfg then
        self.data:SetCurSelectBossID(BossViewIndex.VipBoss, vip_cfg.level + 1, GameEnum.BossEnterPos)
    else
        self.data:SetCurSelectBossID(param[3], param[4], GameEnum.BossEnterPos)
    end
	local scene_type, layer = self.data:GetGuildHelpBossInfo(scene_id)
	if scene_type == SceneType.WorldBoss then
		self:HelpGuildWolrdBoss(scene_id)
	elseif scene_type == SceneType.VIP_BOSS then
        self:HelpGuildVipBoss(scene_id)
    elseif scene_type == SceneType.Shenyuan_boss then
		self:MoveToShenyuanBoss(scene_id)
	elseif scene_type == SceneType.MJ_BOSS then
		self:HelpGuildMJBoss(scene_id)
	elseif scene_type == SceneType.KF_BOSS then
		self:HelpGuildKFBoss(scene_id)
	elseif scene_type == SceneType.DABAO_BOSS then
		self:HelpGuildDaBaoBoss(scene_id)
	elseif scene_type == SceneType.SG_BOSS then
		self:HelpGuildSGBoss(scene_id, layer)
	elseif scene_type == SceneType.SHENGYU_BOSS then
		self:HelpGuildShengYuBoss(scene_id, layer)
	elseif scene_type == SceneType.KFSHENYUN_FB then
		self:HelpGuildYunLuo(scene_id, layer)
	end
end

function BossWGCtrl:MoveToTargetPos()
    local pos_x, pos_y, call_back = self.data:GetSetBossHelpPos()
    local scene_id = Scene.Instance:GetSceneId()
    if pos_x and pos_y then     
        if Scene.Instance:GetSceneType() == SceneType.Shenyuan_boss then
            if WorldServerWGData.Instance:GetCanPickBox() then
                GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
                    local scene_logic = Scene.Instance:GetSceneLogic()
                    if scene_logic and scene_logic.GetPickedObj then
                        local pick_obj = scene_logic:GetPickedObj()
                        if pick_obj then
                            MoveCache.SetEndType(MoveEndType.Gather)
                            MoveCache.target_obj = pick_obj
                        end
                    end
                end)
            end
        end

		local function end_jump()
			GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 3, nil, nil, nil, call_back)
		end

		if not TaskWGCtrl.Instance:IsFly() then
			end_jump()
		else
			TaskWGCtrl.Instance:AddFlyUpList(end_jump)
		end
        
    end
end

function BossWGCtrl:MoveToShenyuanBoss(scene_id)
    local level = RoleWGData.Instance:GetRoleLevel()
    local cfg = BossWGData.Instance:GetShenYuanBossCfgSceneId(scene_id)
    if not IsEmptyTable(cfg) and not IsEmptyTable(cfg[1]) then
        local shenyuan_select_data = cfg[1]
        if level < shenyuan_select_data.enter_level then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.LevelNotEnough, RoleWGData.GetLevelString(shenyuan_select_data.enter_level)))
            return
        end
        BossWGCtrl.Instance:SendShenYuanOpera(WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_ENTER, shenyuan_select_data.seq)
    end
    --BossWGData.Instance:SetCurSelectBossID(0 , 0, shenyuan_select_data.boss_id)
end

function BossWGCtrl:HelpGuildWolrdBoss(scene_id)
	local boss_list = self.data:GetBossListBySceneId(scene_id)
	if not IsEmptyTable(boss_list) then
		local boss_data = boss_list[1]
		if boss_data and boss_data.need_role_level and boss_data.need_role_level > RoleWGData.Instance:GetRoleLevel() then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelUnLock2, RoleWGData.GetLevelString(boss_data.need_role_level)))
			return
	    end
		self:SendWorldBossReq(BossView.ReqType.ENTER, boss_list[1].layer)
	end
end

function BossWGCtrl:HelpGuildVipBoss(scene_id)
	local boss_list = self.data:GetBossListBySceneId(scene_id)
    if not IsEmptyTable(boss_list) then
        self:OnEnterVipBoss()
		-- local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
		-- local cfg = self.data:GetVipBossLayerCfgBySceneId(scene_id)
		-- if role_vip_level >= cfg.free_vip_level then
		-- 	self:SendVipBossReq(BossView.ReqType.ENTER, boss_list[1].layer, boss_list[1].boss_id)
		-- else
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.VipTips)
		-- end
	end
end

function BossWGCtrl:HelpGuildMJBoss(scene_id)
	local boss_list = self.data:GetBossListBySceneId(scene_id)
	if not IsEmptyTable(boss_list) then
		self:SendMiJingBossReq(BossView.ReqType.ENTER, boss_list[1].layer, boss_list[1].boss_id)
	end
end

function BossWGCtrl:HelpGuildKFBoss(scene_id)
	local boss_list = self.data:GetBossListBySceneId(scene_id)
	if not IsEmptyTable(boss_list) then
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_BOSS, boss_list[1].layer)
	end
end

function BossWGCtrl:HelpGuildDaBaoBoss(scene_id)
	local boss_list = self.data:GetBossListBySceneId(scene_id)
	if not IsEmptyTable(boss_list) then
		local enter_comsun = BossWGData.Instance:GetDaBaoBossEnterComsun()
		local tiky_id = BossWGData.Instance:GetDabaoBossTikyId()
		local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(tiky_id)
		if has_tiky_num >= enter_comsun then
			BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.ENTER, boss_list[1].layer, boss_list[1].boss_id)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoTicket)
		end
	end
end

function BossWGCtrl:HelpGuildSGBoss(scene_id, layer)
	--local boss_list = self.data:GetSGAllBossByLayer(layer)
	--if not IsEmptyTable(boss_list) then
		local enter_comsun = BossWGData.Instance:GetSGBossEnterComsun()
		local tiky_id = BossWGData.Instance:GetSGBossTikyId()
		local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(tiky_id)
		if has_tiky_num >= enter_comsun then
			self:SendSGEnterReq(layer)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoTicket)
		end
	--end
end

function BossWGCtrl:HelpGuildShengYuBoss(scene_id, layer)
	CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_SHENGYU_BOSS, layer)
end

function BossWGCtrl:HelpGuildYunLuo(scene_id, layer)
	local role_vip_level = RoleWGData.Instance.role_vo.vip_level
	if BossWGData.Instance:SetShenYunTicket(role_vip_level) - BossWGData.Instance:GetShenYinFbDayCount() > 0 then
		local enter_comsun = BossWGData.Instance:GetTicketNeed()
		local tiky_id = BossWGData.Instance:GetShenYunTicketID()
		local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(tiky_id)
		if has_tiky_num > enter_comsun.need_ticket then
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_SHENYIN, layer)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoTicket)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoPerBossEnter)
	end
end

function BossWGCtrl:OpenQuickUseView(list)
    self.quick_use_view:SetData(list)
    self.quick_use_view:Open()
end

--1表示本服，2表示跨服
function BossWGCtrl:SetRecordView(state)
	self.boss_drop_record_view:SetState(state)
end

function BossWGCtrl:ReLoadRoleTiredIcon()
    local scene_type = Scene.Instance:GetSceneType()
    local tire_value, max_tire_value = -1, -1
    if scene_type == SceneType.KF_BOSS then
        tire_value, max_tire_value = BossWGData.Instance:GetCrossBossTire()
    end
    if max_tire_value ~= -1 then
        local main_role = Scene.Instance:GetMainRole()
        main_role:SetAttr("tired_value_icon",tire_value >= max_tire_value and 1 or 0)
    end
end

function BossWGCtrl:GoToHelpBossAssist()
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	GuajiWGCtrl.Instance:StopGuaji()
	MoveCache.SetEndType(MoveEndType.FightByMonsterId)
	MoveCache.param1 = assist_info.target_boss_id
	local range = BossWGData.Instance:GetMonsterRangeByid(assist_info.target_boss_id)
	GuajiWGCtrl.Instance:MoveToPos(assist_info.target_boss_scene_id, assist_info.target_boss_pos_x, assist_info.target_boss_pos_y, range)
end

--显示等级过高无掉落提示
function BossWGCtrl:ShowLevelLimitTip(time)
	if not self.limit_tip:IsOpen() then
		self.limit_tip:Open()
	end
	self.limit_tip:Flush(0, "time", {time = time})
end

--快速vip等级购买, 跨服系列tabinde+1和本服错开
function BossWGCtrl:OpenQuickVipBuyView(tab_index)
	self.quick_vip_buy:SetTabIndex(tab_index)
	self.quick_vip_buy:Open()
end

--深渊boss请求
function BossWGCtrl:SendShenYuanOpera(op_type,param_0)
	local protocol = ProtocolPool.Instance:GetProtocol(ShenYuanBossReq)
	protocol.op_type = op_type or 0
    protocol.param_0 = param_0 or 0
	protocol:EncodeAndSend()
end

-- 深渊boss所有信息
function BossWGCtrl:OnSCSendShenyuanAllBossInfo(protocol)
    self.data:SetShenYuanBossData(protocol)
    RemindManager.Instance:Fire(RemindName.WorldServer_SYMW)
    ViewManager.Instance:FlushView(GuideModuleName.WorldServer)
end

-- 深渊boss单个信息
function BossWGCtrl:OnSCSendShenyuanSingleBossInfo(protocol)
    self.data:SetShenYuanSingleBossData(protocol)
    RemindManager.Instance:Fire(RemindName.WorldServer_SYMW)
    ViewManager.Instance:FlushView(GuideModuleName.WorldServer)
end

-- 深渊boss所有伤害信息
function BossWGCtrl:OnSCShenyuanBossHurtInfo(protocol)
    self.data:SetShenYuanHurtInfo(protocol)
    self:FlushShenYuanHurtView()
end

-- 深渊boss国家伤害信息
function BossWGCtrl:OnSCShenYuanBossCountryHurtlist(protocol)
    self.data:SetShenYuanCountryHurtInfo(protocol)
    if self.shenyuan_hurt_view:IsOpen() then
        self.shenyuan_hurt_view:Flush(nil, "country_damage")
    end
end

function BossWGCtrl:OnSCShenYuanBossConcernlist(protocol)
	self.data:SetShenYuanBossConcernList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
end

function BossWGCtrl:FlushShenYuanHurtView()
    if self.shenyuan_hurt_view:IsOpen() then
        self.shenyuan_hurt_view:Flush(nil, "person_damage")
    end
end

function BossWGCtrl:OpenPersonCosumeView(ok_func)
    if self.person_enter_cosume_view then
        self.person_enter_cosume_view:SetData(ok_func)
    end
end

function BossWGCtrl:CloseHurtView()
    if self.shenyuan_hurt_view:IsOpen() then
        self.shenyuan_hurt_view:Close()
    end
end

function BossWGCtrl:GetHurtView()
	return self.shenyuan_hurt_view
end

function BossWGCtrl:OpenShenYuanHurtFlag()
    self.shenyuan_flag_tip:Open()
end

function BossWGCtrl:SetShenYuanFlagIndex(index)
	self.shenyuan_flag_index = index
end

function BossWGCtrl:GetShenYuanFlagIndex()
	return self.shenyuan_flag_index or 1
end

-- 深渊boss个人伤害信息
function BossWGCtrl:OnSCShenyuanBossPersonHurtInfo(protocol)
    self.data:SetShenYuanSingleHurtInfo(protocol)
    self:FlushShenYuanHurtView()
end

function BossWGCtrl:OpenShenYuanHurtView()
    self.shenyuan_hurt_view:Open()
end

function BossWGCtrl:OpenWorldBossBuyView(data)
	self.world_boss_buy_view:SetData(data)
end

function BossWGCtrl:CloseWorldBossBuyView()
	if self.world_boss_buy_view:IsOpen() then
		self.world_boss_buy_view:Close()
	end
end

function BossWGCtrl:FlushWorldBossBuyView()
	self.world_boss_buy_view:Flush()
end

function BossWGCtrl:FlushDabaoBossEquipShowView()
	self.dabao_boss_equip_show_view:Flush()
end

function BossWGCtrl:OpenBossInvokeView()
	self.boss_invoke_view:Open()
end

function BossWGCtrl:CloseBossInvokeView()
    MainuiWGCtrl.Instance:CloseBossCardContainer()
	self.boss_invoke_view:Close()
end

--boss的个人首杀
function BossWGCtrl:OnSCBossfirstkillPersonInfo(protocol)
    --print_error("个人",protocol)
    self.data:SetFirstKillPersonInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end
    if self.firstkill_redpacket_view:IsOpen() then
        self.firstkill_redpacket_view:Flush()
    end
    self.data:InvateBossFirstKillTip()
    RemindManager.Instance:Fire(RemindName.Boss_World)
    RemindManager.Instance:Fire(RemindName.Boss_Vip)
    RemindManager.Instance:Fire(RemindName.Boss)
    GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

--boss的无限次数世界首杀奖励
function BossWGCtrl:OnSCBossfirstkillInfiniteInfo(protocol)
    --print_error("个人",protocol)
    self.data:SetFirstKillInfinitePersonInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end
    self.data:InvateBossFirstKillTip()

    if self.boss_first_kill_view:IsOpen() then
    	self.boss_first_kill_view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.Boss_World)
    RemindManager.Instance:Fire(RemindName.Boss_Vip)
    RemindManager.Instance:Fire(RemindName.Boss)
    GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
    MainuiWGCtrl.Instance:FlushView(0, "boss_first_kill")
end

--boss的世界首杀
function BossWGCtrl:OnSCBossfirstkillWorldInfo(protocol)
    self.data:SetFirstKillWorldInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end
    if self.first_choose_reward_view:IsOpen() then
        self.first_choose_reward_view:Flush()
    end

    if self.boss_first_kill_view:IsOpen() then
    	self.boss_first_kill_view:Flush()
    end
    
    self.data:InvateBossFirstKillTip()
    RemindManager.Instance:Fire(RemindName.Boss_World)
    RemindManager.Instance:Fire(RemindName.Boss_Vip)
    RemindManager.Instance:Fire(RemindName.Boss)
    MainuiWGCtrl.Instance:FlushView(0, "boss_first_kill")
end

--boss的世界首杀
function BossWGCtrl:OnSCBossfirstkillOneBossInfo(protocol)
    self.data:SetSingleFirstKillWorldInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    if self.boss_first_kill_view:IsOpen() then
    	self.boss_first_kill_view:Flush()
    end

    if self.first_choose_reward_view:IsOpen() then
        self.first_choose_reward_view:Flush()
    end
    self.data:InvateBossFirstKillTip()
    RemindManager.Instance:Fire(RemindName.Boss_World)
    RemindManager.Instance:Fire(RemindName.Boss_Vip)
    RemindManager.Instance:Fire(RemindName.Boss)
    MainuiWGCtrl.Instance:FlushView(0, "boss_first_kill")
    GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
end

function BossWGCtrl:SendFirstKillOpera(opera_type, param_0, param_1)
    local protocol = ProtocolPool.Instance:GetProtocol(CSBossfirstkillOpera)
	protocol.opera_type = opera_type or 0
    protocol.param_0 = param_0 or 0
    protocol.param_1 = param_1 or 0
    protocol:EncodeAndSend()
end

function BossWGCtrl:OpenRewardChooseView(boss_id, boss_type, is_open)
    self.data:SetCurFirstChoosedData(boss_id, boss_type)
	is_open = nil == is_open and true
    self.first_choose_reward_view:SetData(boss_id, is_open)
end

function BossWGCtrl:SendRewardChooseView(boss_id, boss_type, item_id)
	local cmd = ProtocolPool.Instance:GetProtocol(CSFetchBossfirstkillWorldReward)
	cmd.boss_id = boss_id
	cmd.boss_type = boss_type
    cmd.item_id = item_id
	cmd:EncodeAndSend()
end

function BossWGCtrl:OpenFirstkillRedPacketView(boss_id, boss_type)
	self.firstkill_redpacket_view:SetBossIdData(boss_id, boss_type)
end

function BossWGCtrl:DoOperaBossRefresh(item_id)
	local num, role_id = NewTeamWGData.Instance:GetSelectUseShareItemInfo(item_id)
	local main_role_id = RoleWGData.Instance:GetMainRoleId()
    local idx = MainuiWGData.Instance:GetRefreshOrInvokeCardtype(item_id)
    if idx == 1 then --boss单个刷新
        MainuiWGCtrl.Instance:CloseBossCardContainer()
        FuBenWGCtrl.Instance:SendBossRebirth(FB_USE_ITEM.REBIRTHBOSS, 0, 0, role_id)
    elseif idx == 2 then--boss层刷新
        MainuiWGCtrl.Instance:CloseBossCardContainer()
    	FuBenWGCtrl.Instance:SendBossRebirth(FB_USE_ITEM.REBIRTHBOSS, 1, 0, role_id)
    elseif idx == 3 then  --boss召唤
        self:OpenBossInvokeView()
    elseif idx == 4 then  --boss通用刷新
        MainuiWGCtrl.Instance:CloseBossCardContainer()
    	FuBenWGCtrl.Instance:SendBossRebirth(FB_USE_ITEM.RebirthBossCommon, 0, 0, role_id)
    end
end


function BossWGCtrl:ReqProtocol()
    BossWGCtrl.Instance:SendShenYuanOpera(WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_INFO)
    BossWGCtrl.Instance:SendTreasureBossOpera(TREASURE_BOSS_REQ_TYPE.REQ_TYPE_INFO)
end


function BossWGCtrl:OpenKFBossGatherInfoView()
	if self.kf_boss_gather_info_view then
		self.kf_boss_gather_info_view:Open()
	end
end

function BossWGCtrl:GetKFBossGatherInfoView()
	return self.kf_boss_gather_info_view
end

function BossWGCtrl:FlushKFBossGatherInfoView()
	if self.kf_boss_gather_info_view:IsOpen() and self.kf_boss_gather_info_view:IsLoaded() then
		self.kf_boss_gather_info_view:Flush()
	end
end

function BossWGCtrl:CloseKFBossGatherInfoView()
	if self.kf_boss_gather_info_view then
		self.kf_boss_gather_info_view:Close()
	end
end

function BossWGCtrl:OnSCCrossBossCrystalGathertTimes(protocol)
	self.data:OnSCCrossBossCrystalGathertTimes(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_mh, "kf_scene")
	self:FlushKFBossGatherInfoView()
end

function BossWGCtrl:FullBagGatherHandle()
	local str = Language.Boss.BagFullTip
	local cancel_btn_text = Language.Boss.GoToCheckBag
	local cancel_func = function ()
		ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_bag_all)
	end
	TipWGCtrl.Instance:OpenAlertTips(str,nil,cancel_func, nil, nil, nil, nil, nil, cancel_btn_text)
end





function BossWGCtrl:OnSCSendTreasureHuntAllBossInfo(protocol)
	self.treasure_boss_data:OnSCSendTreasureHuntAllBossInfo(protocol)
	TreasureHuntWGCtrl.Instance:FlushTreasureBossList()
	self:RefershBossStone()
end

function BossWGCtrl:OnSCSendTreasureHuntSingleBossInfo(protocol)
	self.treasure_boss_data:OnSCSendTreasureHuntSingleBossInfo(protocol)
	TreasureHuntWGCtrl.Instance:FlushTreasureBossList()
	WorldServerWGCtrl.Instance:FlushTreasureHurtBossInfoView()
	self:RefershBossStone()
end

-- function BossWGCtrl:OnSCTreasureHuntBossHurtInfo(protocol)
-- 	self.treasure_boss_data:OnSCTreasureHuntBossHurtInfo(protocol)
-- end

-- function BossWGCtrl:OnSCTreasureHuntBossPersonHurtInfo(protocol)
-- 	self.treasure_boss_data:OnSCTreasureHuntBossPersonHurtInfo(protocol)
-- end

--深渊boss请求
function BossWGCtrl:SendTreasureBossOpera(op_type,param_0)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTreasureHuntBossReq)
	protocol.op_type = op_type or 0
    protocol.param_0 = param_0 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnSCChestshopBossFinishInfo(protocol)
	WorldServerWGCtrl.Instance:OpenTreasureJieSuan(protocol)
	WorldServerWGCtrl.Instance:FlushTreasureHurtBossInfoView()
end

function BossWGCtrl:OnSCChestshopBossResetInfo(protocol)
	self.treasure_boss_data:OnSCChestshopBossResetInfo(protocol)
	TreasureHuntWGCtrl.Instance:FlushTreasureBossList()
end

function BossWGCtrl:OnSCRefreshBossRewardInfo(protocol)
	self.data:OnSCRefreshBossRewardInfo(protocol)
	if not self.boss_reward_thsgiven_view:IsOpen() and self.data:GetIsIgnoreThankMsg() == 0 then
		self.boss_reward_thsgiven_view:Open()
	else
		local reward_data = self.data:GetBossThsGivenRewardData()
		if not IsEmptyTable(reward_data) then
			self:OnCSRefreshBossRewardGet(reward_data.boss_type, reward_data.boss_id, reward_data.user_id,
														reward_data.plat_type, reward_data.item_ids, reward_data.reward_id)
			SocietyWGCtrl.Instance:AddFriend(reward_data.user_id, 0)
		end
	end
end

function BossWGCtrl:OnCSRefreshBossRewardGet(boss_type, boss_id, user_id, plat_type, item_ids, reward_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRefreshBossRewardGet)
	protocol.boss_type = boss_type or 0
	protocol.boss_id = boss_id or 0
	protocol.user_id = user_id or 0
	protocol.plat_type = plat_type or 0
	protocol.item_ids = item_ids or 0
	protocol.reward_id = reward_id or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:SetFocusViewDataAndOpen(data)
    WorldServerWGData.Instance:SetCurFocusViewData(data)
	self.boss_focus_view:SetDataAndOpen(data)
end

function BossWGCtrl:OnSCAckMonsterIsExsit(protocol)
	self.data:OnSCAckMonsterIsExsit(protocol)
end

function BossWGCtrl:RequestBossExit(scene_id,scene_key,monster_id,monster_key)
	if not scene_id or not scene_key or not monster_id or not monster_key then
		return
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSReqMonsterIsExsit)
	protocol.scene_id = scene_id or 0
	protocol.scene_key = scene_key or 0
	protocol.monster_id = monster_id or 0
	protocol.monster_key = monster_key or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnPersonSpecialBossSceneInfo(protocol)
	local info = protocol.info
	self.data:SetPersonSpecialBossSceneInfo(protocol)

	local out_time = info.kick_out_time > 0 and info.kick_out_time or info.fb_end_time
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time, true)
end

--Boss死亡广播.
--protocol.boss_type:boss类型.
--protocol.monster_type:怪物类型.
--protocol.boss_id:怪物ID.
function BossWGCtrl:OnSCBOSSDeadNotice(protocol)
	local target_obj = SceneObj.select_obj
	if nil == target_obj or target_obj:IsDeleted() or target_obj:GetType() == SceneObjType.MainRole then
		return
	end

	local boss_data = BossWGData.Instance:GetMonsterInfo(target_obj:GetVo().monster_id)
	if not boss_data then
		return
	end

	if protocol.boss_id == target_obj:GetVo().monster_id then
		if self.boss_dead_tips_view and not self.boss_dead_tips_view:IsOpen() then
			self.boss_dead_tips_view:Open()
		end
	end
end

function BossWGCtrl:OpenPersonSpecialBossSceneView()
	self.special_personal_boss_scene_view:Open()
end

function BossWGCtrl:ClosePersonSpecialBossSceneView()
	self.special_personal_boss_scene_view:Close()
end

-----------------------------------每日臻充boss_start---------------------------------------------
function BossWGCtrl:SendCrossRealChargeBossOperate(operate_type, param1, param2, param3)
	-- print_error("--------SendCrossRealChargeBossOperate--------", operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossRealChargeBossOperate)
	protocol.operate_type = operate_type or 0
    protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnSCCrossRealChargeBossAllInfo(protocol)
	-- print_error("--------SCCrossRealChargeBossAllInfo--------", protocol)
	self.data:SetCrossRealChargeBossAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_everyday_recharge_boss)
	WorldServerWGCtrl.Instance:FlushERBSceneInfoView()
end

function BossWGCtrl:OnSCCrossRealChargeBossMonsterUpdate(protocol)
	-- print_error("--------SCCrossRealChargeBossMonsterUpdate--------", protocol)
	local new_boss_info = protocol.monster_info
	local boss_id = new_boss_info.monster_id
	local old_boss_info = self.data:GetERBBossInfoByBossId(boss_id)

	local is_special_boss = self.data:IsERBSpecialBoss(boss_id)
	if is_special_boss and old_boss_info.alive == false and new_boss_info.alive == true then
		WorldServerWGCtrl.Instance:SetSpecialBossRebirthNotice()
	end

	self.data:UpdateCrossRealChargeBossMonster(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_everyday_recharge_boss)
	WorldServerWGCtrl.Instance:FlushERBSceneInfoView()
end

function BossWGCtrl:OnSCCrossRealChargeBossMonsterList(protocol)
	-- print_error("--------SCCrossRealChargeBossMonsterList--------", protocol)
	self.data:SetCrossRealChargeBossMonsterList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_everyday_recharge_boss)
	WorldServerWGCtrl.Instance:FlushERBSceneInfoView()
end

function BossWGCtrl:OnTodayRealRechargeNumChange(today_real_recharge_num, recharge_change_value)
	-- print_error("--------OnTodayRealRechargeNumChange--------", today_real_recharge_num, recharge_change_value)
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_everyday_recharge_boss)
	RemindManager.Instance:Fire(RemindName.WorldServer_EveryDay_Recharge_Boss)
end

function BossWGCtrl:OnSCCrossRealChangeBossRecordList(protocol)
	-- print_error("-------OnSCCrossRealChangeBossRecordList--------", protocol)
	self.data:SetCrossRealChangeBossRecordList(protocol)
end
-------------------------------------每日臻充boss_end---------------------------------------------

--------------------------------触发boss秒杀 start--------------------------------
function BossWGCtrl:OnSCMonsterSecKillInfo(protocol)
	-- print_error("-------OnSCMonsterSecKillInfo--------", protocol)
	self.data:SetSecKillBossInfo(protocol)
	MainuiWGCtrl.Instance:FlushSecKillBossPart()
end

function BossWGCtrl:TriggerSecKillMonster(result, sec_kill_type, monster_obj_id)
	if result == 0 then
		return
	end

	local cfg = BossWGData.Instance:GetMonsterShieldSecKillCfgByType(sec_kill_type)
	if not cfg then
		return
	end

	if TaskWGCtrl.Instance:IsFly() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role or main_role:IsDeleted() or main_role:IsJump() or main_role:IsXMZCar() or main_role:IsTianShenAppearance()
	or main_role:IsXiuWeiBianShen() or main_role:IsRidingFightMount() or main_role:IsGundam() then
		return
	end

	main_role:CrossAction(SceneObjPart.Main, cfg.sec_kill_ani_name)
	main_role:CharacterAnimatorEvent(nil, nil, cfg.sec_kill_ani_name .. "/begin", true)
end

--------------------------------触发boss秒杀 end--------------------------------