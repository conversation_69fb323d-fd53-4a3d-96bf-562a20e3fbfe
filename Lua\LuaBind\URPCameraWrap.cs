﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class URPCameraWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(URPCamera), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("ChangeCameraToBase", ChangeCameraToBase);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("m_AutoOnCameraEnableToBase", get_m_AutoOnCameraEnableToBase, set_m_AutoOnCameraEnableToBase);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeCameraToBase(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			URPCamera obj = (URPCamera)ToLua.CheckObject<URPCamera>(L, 1);
			obj.ChangeCameraToBase();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_AutoOnCameraEnableToBase(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			URPCamera obj = (URPCamera)o;
			bool ret = obj.m_AutoOnCameraEnableToBase;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_AutoOnCameraEnableToBase on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_AutoOnCameraEnableToBase(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			URPCamera obj = (URPCamera)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.m_AutoOnCameraEnableToBase = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_AutoOnCameraEnableToBase on a nil value");
		}
	}
}

