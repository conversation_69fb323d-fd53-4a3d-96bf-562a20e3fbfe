require("game/singlerecharge/single_recharge_wg_data")
require("game/singlerecharge/single_recharge_view")

SingleRechargeWgCtrl = SingleRechargeWgCtrl or BaseClass(BaseWGCtrl)

function SingleRechargeWgCtrl:__init()
    if SingleRechargeWgCtrl.Instance then
        error("[SingleRechargeWgCtrl]:Attempt to create singleton twice!")
        return
    end
    SingleRechargeWgCtrl.Instance = self

    self.data = SingleRechargeWgData.New()
    self.view = SingleReChargeView.New(GuideModuleName.SingleReChargeView)
    self:RegisterAllProtocals()
end

function SingleRechargeWgCtrl:__delete()
    SingleRechargeWgCtrl.Instance = nil
    self.data:DeleteMe()
    self.data = nil
    self.view:DeleteMe()
    self.view = nil
end

function SingleRechargeWgCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCRASingleChongzhiInfo, "OnSCRASingleChongzhiInfo")
end

function SingleRechargeWgCtrl:OnSCRASingleChongzhiInfo(protocol)
    self.data:SetSingleRechargeListData(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end
end
 
--发送请求  operate_type 0请求数据  1领奖
function SingleRechargeWgCtrl:SendOperateReq(operate_type, param_1)
    local operate_type = operate_type or 0
    local param_1 = param_1 or 0
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_SINGLE_CHARGE,operate_type, param_1)
end