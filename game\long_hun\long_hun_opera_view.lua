LongHunOperaView = LongHunOperaView or BaseClass(SafeBaseView)

function LongHunOperaView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:InitLoadUIConfig()
end

function LongHunOperaView:InitLoadUIConfig()
	self:AddViewResource(0,"uis/view/long_hun_ui_prefab","layout_longhun_opera_view")
end

function LongHunOperaView:LoadCallBack()
	for i=1,4 do
		self.node_list["btn"..i].button:AddClickListener(BindTool.Bind(self.OnClickBtn, self,i))
	end
end

function LongHunOperaView:SetData(data,from_view,click_call_back,need_flush_and_hold)
	if IsEmptyTable(data) then self:Close() end
	self.data = data
	self.click_call_back = click_call_back
	self.need_flush_and_hold = need_flush_and_hold or false
end

function LongHunOperaView:OnFlush()
	if IsEmptyTable(self.data) then self:Close() end
	self:FlushAppearance()
	self:FlushAttr()
	self:FlushButton()
end

function LongHunOperaView:FlushAppearance()
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local base_cfg = LongHunWGData.Instance:GetMingWenBaseCfgByID(self.data.item_id)

	if IsEmptyTable(cfg) or IsEmptyTable(base_cfg) then
		return
	end

	local is_special = LongHunWGData.Instance:IsSpecialPosyType(base_cfg.type)
	local index1 = 1
	local index2 = 1
	if base_cfg.type == 1 then
		index1 = 2
		index2 = 3
	elseif is_special then
		index1 = 1
		index2 = 2
	else
		index1 = 1
		index2 = 1
	end
	self.node_list["bottom_label"].text.text = cfg.get_msg
	self.node_list["item_equip_type"].text.text = Language.LongHunView.EquipTypeTitle[index1]..Language.LongHunView.EquipType[index2]
	self.node_list["item_name"].text.text = ToColorStr(cfg.name,ITEM_COLOR[cfg.color])

	local t_bundle, t_asset = ResPath.GetNoPackPNG("tips_top_"..cfg.color)
	self.node_list["top_color_bg"].image:LoadSpriteAsync(t_bundle, t_asset)

	local c_bundle, c_asset = ResPath.GetCommonImages("a3_ty_wpk_"..cfg.color)
	self.node_list["cell_pos"].image:LoadSpriteAsync(c_bundle, c_asset)

 	local bundle, asset = ResPath.GetItem(cfg.icon_id)
 	self.node_list["icon"].image:LoadSpriteAsync(bundle, asset,function ()
		self.node_list["icon"].image:SetNativeSize()
	end)

end

function LongHunOperaView:FlushAttr()
	local attr_type,attr_value,add_value = LongHunWGData.Instance:GetEquipPosyAttrBySlot(self.data)
	local name_list, is_pre = LongHunWGData.Instance:GetAttributeNameListByType(attr_type,true)
	local attr_text = ""
	if IsEmptyTable(name_list) then
		local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		attr_text = cfg.description
	else
		local str_format = "<color=#C4B8A8ff>%s：</color><color=#28e53aff>%s</color>%s"
		local length = #name_list
		for k, v in ipairs(name_list) do
			local huan_hang = k == length and "" or "\n"
			local value = is_pre[k] and (attr_value[k] / 100) .. "%" or attr_value[k]
			attr_text = attr_text .. string.format(str_format, v, value, huan_hang)
		end
	end
	self.node_list["attr_text"].text.text = attr_text
end

function LongHunOperaView:FlushButton()
	for i=1,4 do
		if self.click_call_back and self.click_call_back[i] then
			self.node_list["btn_text"..i].text.text = self.click_call_back[i].btn_text
			self.node_list["btn"..i]:SetActive(true)
			if self.click_call_back[i].show_red and self.click_call_back[i].show_red == 1 then
				self.node_list["btn_red"..i]:SetActive(true)
			else
				self.node_list["btn_red"..i]:SetActive(false)
			end
		else
			self.node_list["btn"..i]:SetActive(false)
		end
	end
end

function LongHunOperaView:OnClickBtn(index)
	if self.click_call_back and self.click_call_back[index] then
		self:Close()
		self.click_call_back[index].callback()
		--if self.need_flush_and_hold and index == 1 then
		--else
		--end
	end
end
