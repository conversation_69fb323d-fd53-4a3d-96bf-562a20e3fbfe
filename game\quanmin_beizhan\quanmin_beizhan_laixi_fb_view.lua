QuanMinBeiZhanLaiXiFBView = QuanMinBeiZhanLaiXiFBView or BaseClass(SafeBaseView)

function QuanMinBeiZhanLaiXiFBView:__init(view_name)
	self.view_name = "QuanMinBeiZhanLaiXiFBView"
	self.view_layer = UiLayer.MainUILow
	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/quanmin_beizhan_ui_prefab", "layout_xtlx_fb_view")
end

function QuanMinBeiZhanLaiXiFBView:LoadCallBack()
	self:InitPanel()
	self:RefreshRewardList()
end

function QuanMinBeiZhanLaiXiFBView:ReleaseCallBack()
	if self.reward_partake_list then
		self.reward_partake_list:DeleteMe()
		self.reward_partake_list = nil
	end

	if self.reward_mabay_list then
		self.reward_mabay_list:DeleteMe()
		self.reward_mabay_list = nil
	end

	if not IsNil(self.obj) then
        ResMgr:Destroy(self.obj)
    end
    self.obj = nil

    CountDownManager.Instance:RemoveCountDown("laixi_fb_count_down")
end

function QuanMinBeiZhanLaiXiFBView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:RefreshView()
		elseif k == "xingtian_laixi" then
			self:RefreshRewardList()
		end
	end
end

function QuanMinBeiZhanLaiXiFBView:InitPanel()
	self.reward_partake_list = AsyncListView.New(ItemCell, self.node_list.reward_partake_list)
	self.reward_mabay_list = AsyncListView.New(ItemCell, self.node_list.reward_mabay_list)
	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.XINGTIANLAIXI, BindTool.Bind(self.InitCallBack, self))

	self.max_round_count = 0
	local monster_cfg = QuanMinBeiZhanWGData.Instance:GetJiangLinMonster_cfg()
	if monster_cfg then
		self.max_round_count = monster_cfg[#monster_cfg].refresh_times
	end
end

function QuanMinBeiZhanLaiXiFBView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.obj = self.node_list["layout_view"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform, false)
end

function QuanMinBeiZhanLaiXiFBView:OpenCallBack()
	QuanMinBeiZhanWGCtrl.Instance:CSRandActXTLXInfoReq()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function QuanMinBeiZhanLaiXiFBView:RefreshRewardList()
	local cfg_list = QuanMinBeiZhanWGData.Instance:GetJiangLinReward()
	if cfg_list then
		local partake_reward_list = OperationActivityWGData.Instance:SortDataByItemColor(cfg_list.act_reward_item)
		self.reward_partake_list:SetDataList(partake_reward_list)

		local drop_reward_list = OperationActivityWGData.Instance:SortDataByItemColor(cfg_list.drop_reward_item)
		self.reward_mabay_list:SetDataList(drop_reward_list)
	end
end

function QuanMinBeiZhanLaiXiFBView:RefreshView()
	local jianglin_data = QuanMinBeiZhanWGData.Instance:GetJingLinData()
	if not jianglin_data then
		return
	end

	if jianglin_data.refresh_times then
		if jianglin_data.refresh_times == self.max_round_count then
			self.node_list.next_round.text.text = Language.OpertionAcitvity.ActivityTime
		elseif jianglin_data.cur_status == XingTianLaiXiStatus.REFRESH_STATUS_REFRESHING then
			self.node_list.next_round.text.text = Language.QuanMinBeiZhan.LaiXiStr1
		else
			self.node_list.next_round.text.text = Language.QuanMinBeiZhan.LaiXiStr2
		end

		self.node_list.round_num.text.text = string.format("%d/%d", jianglin_data.refresh_times, self.max_round_count)
	end

	local next_time = jianglin_data.next_change_time or 0
	if jianglin_data.refresh_times == self.max_round_count then
		next_time = jianglin_data.next_time
	end
	self:FlushFBCountDown(next_time)
end

function QuanMinBeiZhanLaiXiFBView:FlushFBCountDown(next_time)
	CountDownManager.Instance:RemoveCountDown("laixi_fb_count_down")
	local ser_time = TimeWGCtrl.Instance:GetServerTime()
	local count_down_time = math.floor(next_time - ser_time)
	if count_down_time > 0 then
		self.node_list.next_round_time.text.text = TimeUtil.FormatSecondDHM4(count_down_time)
		CountDownManager.Instance:AddCountDown("laixi_fb_count_down", BindTool.Bind(self.UpdateFBCountDown, self), nil, nil, count_down_time, 1)
	else
		self.node_list.next_round_time.text.text = ""
	end
end

function QuanMinBeiZhanLaiXiFBView:UpdateFBCountDown(elapse_time, total_time)
	self.node_list.next_round_time.text.text = TimeUtil.FormatSecondDHM4(total_time - elapse_time)
end