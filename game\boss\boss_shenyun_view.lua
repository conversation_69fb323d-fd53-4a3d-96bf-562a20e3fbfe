ShenYinBossView = ShenYinBossView or BaseClass(SafeBaseView)
function ShenYinBossView:__init()
	if ShenYinBossView.Instance then
		ErrorLog("[ShenYinBossView] attempt to create singleton twice!")
		return
	end

    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_yluo_boss_panel")
    self.last_time_stamp = -1
    self.is_first_open_list = true
    self.view_layer = UiLayer.MainUILow
end

function ShenYinBossView:ReleaseCallBack()
	if self.prog_upstar_progress then
		self.prog_upstar_progress:DeleteMe()
		self.prog_upstar_progress = nil
	end
	self.last_time_stamp = nil
	if self.moster_rank then
		self.moster_rank:DeleteMe()
		self.moster_rank = nil
	end

	if self.mt_layout_follow then
		self.mt_layout_follow:removeFromParent()
	end
	self.mt_layout_follow = nil

	if CountDownManager.Instance:HasCountDown("syboss_fushtime1") then
		CountDownManager.Instance:RemoveCountDown("syboss_fushtime1")
	end
	if nil ~= self.show_hide_btn then
		GlobalEventSystem:UnBind(self.show_hide_btn)
		self.show_hide_btn = nil
	end
end

function ShenYinBossView:LoadCallBack()
	self.moster_rank = AsyncListView.New(MosterItemRender,self.node_list.TeamList)
	self.moster_rank:SetSelectCallBack(BindTool.Bind1(self.ItemSelectCallBack,self))
	XUI.AddClickEventListener(self.node_list["TaskButton"],BindTool.Bind(self.FlushInfo, self))
	XUI.AddClickEventListener(self.node_list["TeamButton"],BindTool.Bind(self.FlushMoster, self))
	XUI.AddClickEventListener(self.node_list["btn_xushi"],BindTool.Bind(self.XuShiBtnEvent, self))


	self:FlushShenYuView()
	self.show_hide_btn = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK,BindTool.Bind(self.ChangeXuShiBtn, self))
	self.node_list["asc_fresh_time"].text.text = Language.Boss.KFYunLuoDesc
end

function ShenYinBossView:ChangeXuShiBtn( isOn )
	if isOn then
		self.node_list.btn_xushi.rect:DOAnchorPosY(50, 0.3)
	else
		self.node_list.btn_xushi.rect:DOAnchorPosY(-40, 0.3)
	end
end

function ShenYinBossView:XuShiBtnEvent()
	BossWGCtrl.Instance:GetXuShiCallback()
end
function ShenYinBossView:ItemSelectCallBack(item)
	local sence_id = Scene.Instance:GetSceneId()
	GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
        -- 切换挂机模式，以优先选择玩家 --
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end)
	GuajiWGCtrl.Instance:MoveToPos(sence_id,item.data.x_pos, item.data.y_pos, COMMON_CONSTS.GUAJI_MAX_RANGE)
end

function ShenYinBossView:FlushShenYuView()
	self:FlushMoster()
	self:FlushInfo()

end



function ShenYinBossView:DestroyTaskView()

	if self.mt_layout_follow then
		self.mt_layout_follow:removeFromParent()
	end

	self.mt_layout_follow = nil
	if self.prog_upstar_progress then
		self.prog_upstar_progress = nil
	end

	if CountDownManager.Instance:HasCountDown("syboss_fushtime1") then
		CountDownManager.Instance:RemoveCountDown("syboss_fushtime1")
	end
	
	self.sy_status = nil
	self:Close()
end

function ShenYinBossView:FlushMoster()
	if nil == self.moster_rank then return end
	local scene_id = Scene.Instance:GetSceneId()
	local cfg = BossWGData.Instance:GetMapBossCfg(scene_id)
	self.moster_rank:SetDataList(cfg)
	if self.is_first_open_list then
		self.is_first_open_list = false
	end
end

function ShenYinBossView:ShowIndexCallBack()

end

function ShenYinBossView:FlushInfo()
	if self.node_list.lbl_jingli  then
		local other_cfg = BossWGData.Instance:GetShenYunOther()
		local jing_li, total_jingli = BossWGData.Instance:GetSHenYinJinLi()
		self.node_list.lbl_jingli.text.text = jing_li .."/"..total_jingli
		self.node_list.prog_upstar_progress.slider.value = (jing_li / total_jingli) * 100
		local  sy_bosstime = BossWGData.Instance:GetSYFulshTime()
		local mul_time = sy_bosstime - TimeWGCtrl.Instance:GetServerTime()
		self.sy_status = BossWGData.Instance:GetSYTiemStatus()

		if  self.last_time_stamp ~= sy_bosstime then
			self.last_time_stamp = sy_bosstime
		else
			return
		end
		if CountDownManager.Instance:HasCountDown("syboss_fushtime1") then
			CountDownManager.Instance:RemoveCountDown("syboss_fushtime1")
		end
		if mul_time > 0  then
			self:UpdateOpenCountDownTime(1, mul_time)
			CountDownManager.Instance:AddCountDown("syboss_fushtime1", BindTool.Bind1(self.UpdateOpenCountDownTime, self), BindTool.Bind1(self.CompleteOpenCountDownTime, self), nil, mul_time, 1)
		else
			self:CompleteOpenCountDownTime()
		end
	end
end


function ShenYinBossView:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		if self.sy_status == 0 then
			self.node_list.lbl_sy_time.text.text =Language.Boss.TeTengXiaoShi.. string.format(Language.Boss.GreenColor,TimeUtil.FormatSecond(total_time - elapse_time, 3))
		else
			self.node_list.lbl_sy_time.text.text =Language.Boss.TuTengShuaXin.. string.format(Language.Boss.RedColor,TimeUtil.FormatSecond(total_time - elapse_time, 3))
		end
	end
end

function ShenYinBossView:CompleteOpenCountDownTime()
		self.node_list.lbl_sy_time.text.text = ""
end

function ShenYinBossView:OnFlush(param_t, index)

end



MosterItemRender = MosterItemRender or BaseClass(BaseRender)
function MosterItemRender:__init()

end

function MosterItemRender:__delete()

end
function MosterItemRender:LoadCallBack()
end
function MosterItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.boss_name.text.text = self.data.name
	local tuteng_type  =  BossWGData.Instance:GetTutengType(self:GetIndex())
	if tuteng_type == 1  then
		self.node_list.boss_state.text.text = Language.Boss.GoldT
	elseif tuteng_type == 2 then
		self.node_list.boss_state.text.text = Language.Boss.SilverT
	else
		self.node_list.boss_state.text.text = Language.Boss.LetGo
	end

end

function MosterItemRender:OnSelectChange(is_select)
	if nil ==  is_select then
		is_select = false
	end
	self.node_list.Image_selsect:SetActive(is_select)
end
