GuildPopAppoint = GuildPopAppoint or BaseClass(SafeBaseView)
function GuildPopAppoint:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_pop_appoint")
	self.membervo = nil
	self.select_post_enum = 0
end

function GuildPopAppoint:__delete()
end

function GuildPopAppoint:LoadCallBack()
	self:RegisterAllEvent()
end

-- 注册事件
function GuildPopAppoint:RegisterAllEvent()
	self.node_list.btn_select_post.button:AddClickListener(BindTool.Bind1(self.OnOpen<PERSON>ostViewHand<PERSON>, self))
	self.node_list.btn_ok.button:AddClickListener(BindTool.Bind1(self.OnAppointHandler, self))
	self.node_list.btn_cancel.button:AddClickListener(BindTool.Bind1(self.OnCloseAppointViewHand<PERSON>, self))
	self.point_data_list = nil
	self.point_name = ""
	self.point_index = 0
	self.point_data_list = Language.Guild.GuildPointName
	self.isok  = true
	self.pointlist = {}
	for i = 1, #self.point_data_list do
			self.pointlist[i] = PointListRender.New()
			self.pointlist[i]:SetParent(self.node_list.point_list)
	end
	for i = 1,#self.point_data_list do
		self.pointlist[i].node_list.lbl_caozuo_name.text.text = self.point_data_list[i][1]
		local info = self.point_data_list[i]
		XUI.AddClickEventListener(self.pointlist[i].node_list.btn, BindTool.Bind2(self.OnClickAppointItemHandler,self,info))
	end
	self.node_list.point_list:SetActive(false)

end

-- 打开职务列表界面
function GuildPopAppoint:OnOpenPostViewHandler()
	self.node_list.point_list:SetActive(self.isok)
	self.node_list.jiantou_down:SetActive(not self.isok)
	if not self.isok then
		self.isok  = true
		return
	end
	self.isok  = false
end

-- 发送职务变更申请
function GuildPopAppoint:OnAppointHandler()
	if self.membervo and self.select_post_enum > 0 then
		GuildWGCtrl.Instance:SendGuildAppointReq(GuildDataConst.GUILDVO.guild_id, self.membervo.uid, self.select_post_enum)
	end
end

--关闭任务界面
function GuildPopAppoint:OnCloseAppointViewHandler()
	-- print_error("关闭任务界面")
	self.node_list.point_list:SetActive(false)
	self.node_list.jiantou_down:SetActive(true)
	self:Close()
end

-- 点击职务列表
function GuildPopAppoint:OnClickAppointItemHandler(info)
	self.node_list.point_list:SetActive(false)
	self.node_list.jiantou_down:SetActive(true)
	-- print_error(info)
	local post_index = info[2] + 1
	local post_name = GuildDataConst.GUILD_POST_LIST
	local post_enum = GuildDataConst.GUILD_POST_ENUM

	if nil ~= post_name[post_index] and nil ~= post_enum[post_name[post_index]] then
		self.select_post_enum = post_enum[post_name[post_index]]
		self.node_list.input_name.text.text = info[1]
	end
end

function GuildPopAppoint:Open(membervo)
	if nil == membervo then
		return
	end
	self.membervo = membervo
	SafeBaseView.Open(self)
end

function GuildPopAppoint:OpenCallBack()
end

function GuildPopAppoint:ShowIndexCallBack()
	if nil == self.membervo then
		return
	end
	local post_authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST
	if nil ~= post_authority[self.membervo.post] then
		self.node_list.input_name.text.text = post_authority[self.membervo.post].post
	end
	self.node_list.lbl_member_name.text.text = self.membervo.role_name
	self.select_post_enum = 0
end

function GuildPopAppoint:CloseCallBack()
	if not self.isok then
		self:OnOpenPostViewHandler()
	end
	self.membervo = nil
end

----------------CKCaoZuoListRender-------------------
PointListRender = PointListRender or BaseClass(BaseRender)

function PointListRender:__init(instance)
end

function PointListRender:__delete()
end

function PointListRender:SetParent(parent)
	if nil == self.root_node then
		local function GetWidgets(res_name)
			return "uis/view/guild_ui_prefab", res_name
		end

		local bundle, asset = GetWidgets("btn_point_item")
        local async_loader = AllocAsyncLoader(self, "btn_point_item")
		async_loader:SetParent(parent.transform)
		async_loader:Load(bundle, asset, function(obj)
        	if not obj then
        		-- print_error("can not find the asset!!")
        		return
        	end
			self.name_table = obj:GetComponent(typeof(UINameTable))			-- 名字绑定
			self.node_list = U3DNodeList(self.name_table, self)
			self.obj = obj
			self.obj.transform.localScale = Vector3(1,1,1)
			self.obj.transform.localPosition = Vector3(0,0,0)
        end)
	end
end

function PointListRender:CreateChild()
	BaseRender.CreateChild(self)
end

function PointListRender:OnFlush()

end