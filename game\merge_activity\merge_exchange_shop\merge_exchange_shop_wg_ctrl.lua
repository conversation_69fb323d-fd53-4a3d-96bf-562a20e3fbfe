require("game/merge_activity/merge_exchange_shop/merge_exchange_shop_wg_data")
require("game/merge_activity/merge_exchange_shop/merge_exchange_shop_item")

MergeExchangeShopWGCtrl = MergeExchangeShopWGCtrl or BaseClass(BaseWGCtrl)

function MergeExchangeShopWGCtrl:__init()
	if MergeExchangeShopWGCtrl.Instance ~= nil then
		ErrorLog("[MergeExchangeShopWGCtrl] Attemp to create a singleton twice !")
	end
	MergeExchangeShopWGCtrl.Instance = self

	self.data = MergeExchangeShopWGData.New()

	self:RegisterAllProtocols()
end

function MergeExchangeShopWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	MergeExchangeShopWGCtrl.Instance = nil
end

function MergeExchangeShopWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCActCSAConvertInfo, "OnSCActCSAConvertInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

end

function MergeExchangeShopWGCtrl:MainuiOpenCreate()
	-- RemindManager.Instance:Fire(RemindName.ExchangeShop)n
	self:SendOperExchange(RAND_ACTIVITY_TYPE_CSA_CONVERT_OPERA_TYPE.INFO)
end

function MergeExchangeShopWGCtrl:ItemDataCallBack()

end

function MergeExchangeShopWGCtrl:OnDayChange()
	self.data:CheckPeriod()
end

function MergeExchangeShopWGCtrl:OnSCActCSAConvertInfo(protocol)
	if protocol.cur_act_period == 0 then
		return
	end
	self.data:SaveData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2249)
	RemindManager.Instance:Fire(RemindName.Merge_ExchangeShop)
end

function MergeExchangeShopWGCtrl:SendOperExchange(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_CONVERT
	protocol.opera_type = opera_type
	protocol.param_1 = param1 or 0
	protocol.param_2 = param2 or 0
	protocol.param_3 = 0
	protocol:EncodeAndSend()
end
