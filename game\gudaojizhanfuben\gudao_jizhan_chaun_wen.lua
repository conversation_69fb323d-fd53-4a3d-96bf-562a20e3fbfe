GuDaoScrollView = GuDaoScrollView or BaseClass(SafeBaseView)

local SPEED = 100						-- 字幕滚动的速度(像素/秒)

function GuDaoScrollView:__init()
	self.view_layer = UiLayer.PopTop
	self:AddViewResource(0, "uis/view/gudao_jizhan_prefab", "layout_gudao_chuanwen")
	self.view_name = "GuDaoScrollView"
	self.is_open = false
	self.str_list = {}
	self.current_index = 1
	self.total_count = 0
	self.calculate_time_quest = nil
end

function GuDaoScrollView:__delete()
	if nil ~= self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
		self.calculate_time_quest = nil
	end

	self.is_open = false
end

function GuDaoScrollView:LoadCallBack()
	self.text_trans = self.node_list["EmojiText"]:GetComponent(typeof(UnityEngine.RectTransform))

	self.mask_width = self.node_list["EmojiText"]:GetComponent(typeof(UnityEngine.RectTransform)).parent:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta.x
	self.mask_width_cost_time = self.mask_width / SPEED
	self.is_open = true
end

function GuDaoScrollView:ReleaseCallBack()
	-- 清理变量和对象
	self.text_trans = nil
	self.tweener = nil
end

function GuDaoScrollView:OnShieldHearsay(value)
	if value then
		self:Close()
	end
end

function GuDaoScrollView:OpenCallBack()
	self.tweener = nil
end

function GuDaoScrollView:CloseCallBack()
	if self.shield_hearsay then
		GlobalEventSystem:UnBind(self.shield_hearsay)
		self.shield_hearsay = nil
	end

	if self.tweener then
		self.tweener:Pause()
	end

end

function GuDaoScrollView:SetNotice(str)
	if not self.is_open then
		self:Open()
		self.str_list = {}
		self.current_index = 1
		self.total_count = 0
		self:AddNotice(str)
		self:Flush()
	else
		self:AddNotice(str)
	end
end

function GuDaoScrollView:AddNotice(str)
	self.total_count = self.total_count + 1
	self.str_list[self.total_count] = str
end

function GuDaoScrollView:OnFlush()
	if(self.current_index <= self.total_count) then
		local str = self.str_list[self.current_index]
		self.node_list.EmojiText.tmp.text = str
		self.text_trans.anchoredPosition = Vector2(0, 0)

		self.calculate_time_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Calculate, self), 0.1)
	end
end

-- 计算滚动的时间的位置
function GuDaoScrollView:Calculate()
	self.calculate_time_quest = nil
	if nil == self.text_trans then
		return
	end
	local width = self.text_trans.sizeDelta.x
	local duration = width / SPEED + self.mask_width_cost_time
	width = width + self.mask_width
	local tweener = self.text_trans:DOAnchorPosX(-width, duration, false)
	self.tweener = tweener
	tweener:SetEase(DG.Tweening.Ease.Linear)
	tweener:OnComplete(BindTool.Bind(self.OnMoveEnd, self))
end

function GuDaoScrollView:OnMoveEnd()
	self.current_index = self.current_index + 1
	if(self.current_index > self.total_count) then
		self:Close()
	else
		self:Flush()
	end
end