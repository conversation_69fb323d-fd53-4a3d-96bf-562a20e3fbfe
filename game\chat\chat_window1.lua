-- require("game/chat/chat_win_private")
-- require("game/chat/chat_word_face")
require("game/chat/chat_cell")

NewChatWindow = NewChatWindow or BaseClass(SafeBaseView)

CHAT_MORE = {
	FACE = 1,
	ITEM = 2,
	POS = 3,
	TOUZI = 4,
	HON<PERSON>AO = 5,
	ZHAOJI = 6,
	REAL_POS = 7,
	ZHAOHU = 8,
	QUICKSPEAK = 9,
}

--聊天更多按钮默认显示
CHAT_MORE_DEF_SHOW = {
	[CHAT_MORE.FACE] = true,
	[CHAT_MORE.ITEM] = true,
	[CHAT_MORE.POS] = true,
	[CHAT_MORE.TOUZI] = true,
	[CHAT_MORE.HONGBAO] = false,
	[CHAT_MORE.ZHAOJI] = false,
	[CHAT_MORE.REAL_POS] = true,
	[CHAT_MORE.ZHAOHU] = true,
	[CHAT_MORE.QUICKSPEAK] = true,

}

ChatTabIndex = {
	[CHANNEL_TYPE.SYSTEM] = 10,
	[CHANNEL_TYPE.CHUAN_WEN] = 20,
	[CHANNEL_TYPE.ZUDUI] =30,
	[CHANNEL_TYPE.TEAM] = 40,
	[CHANNEL_TYPE.SCENE] = 50,--场景(附近)频道
	[CHANNEL_TYPE.GUILD] = 60,
	-- [CHANNEL_TYPE.ZHANDUI3V3] = 80,
	[CHANNEL_TYPE.WORLD] = 70,
	[CHANNEL_TYPE.CROSS] = 80,
	
}
TabeIndexChannel = {
	[10] = CHANNEL_TYPE.SYSTEM ,
	[20] = CHANNEL_TYPE.CHUAN_WEN ,
	[30] = CHANNEL_TYPE.ZUDUI,
	[40] = CHANNEL_TYPE.TEAM,
	[50] = CHANNEL_TYPE.SCENE,--场景(附近)频道
	[60] = CHANNEL_TYPE.GUILD,
	-- [80] = CHANNEL_TYPE.ZHANDUI3V3,	
	[70] = CHANNEL_TYPE.WORLD,
	[80] = CHANNEL_TYPE.CROSS,
}
TabIndexUnReadInviteTip = {
	[70] = MAINUI_TIP_TYPE.CHAT_WORLD,                              -- 世界聊天
	[80] = MAINUI_TIP_TYPE.CHAT_KUAFU,								-- 跨服聊天
	[60] = MAINUI_TIP_TYPE.CHAT_GUILD,								-- 仙盟聊天
	[40] = MAINUI_TIP_TYPE.CHAT_UNREAD_TEAM, 						-- 队伍未读消息
	[30] = MAINUI_TIP_TYPE.CHAT_UNREAD_ZUDUI,						-- 组队未读消息
}

local Content_Name = {
	[10] = "ContentSystem",
	[20] = "ContentChuanWen",
	[30] = "ContentZuDui",
	[40] = "ContentTeam",
	[50] = "ContentScene",--场景(附近)频道
	[60] = "ContentGuild",
	-- [80] = "ContentZhanDui",
	[70] = "ContentWorld",
	[80] = "ContentKuaFu",
}

local DATI_OPTION_STR = {
	[1] = "A",
	[2] = "B",
	[3] = "C",
	[4] = "D",
}

local HIDE_VOICE_WIDTH = 340
local SHOW_VOICE_WIDTH = 300


function NewChatWindow:__init()
	self.is_safe_area_adapter = true
	self.close_mode = CloseMode.CloseVisible
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "layout_chatwindow")
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "VerticalTabbar")
	-- self.is_show = false
	-- self.curr_channel = CHANNEL_TYPE.WORLD			-- 当前频道
	-- self.need_show_tabbar = nil
	-- self.roll_now = false							-- 是否有滚动字幕
	-- self.left_bound = 0
	-- self.bottom_icon_is_show = false
	-- self.last_select_index = 1
	-- self.is_any_click_close = true
	self.open_tween = nil--UITween.ShowFadeUp
	self.close_tween = nil--UITween.HideFadeUp


	self.curr_send_channel = CHANNEL_TYPE.WORLD	-- 发送频道
	self.curr_show_channel = CHANNEL_TYPE.SYSTEM	-- 显示频道

	self.cur_active = false
	self.click_voice_btn = false
	self.cd_timer = nil

	self.private_role_id = 0
	self.more_index = 1
	self.cur_show_panel = nil

	self.is_down = false
	-- self.view_layer = UiLayer.MainUIHigh
	self.guide_module_namee = nil
	self.guide_module_index = -1
	self.default_index = ChatTabIndex[CHANNEL_TYPE.WORLD]

	self.old_channel_msg_count_list = {} 			--旧的频道消息个数
	for k,v in pairs(CHANNEL_TYPE) do
		self.old_channel_msg_count_list[v] = 0
	end

	self.measuring_cell_obj_list = {}
	self.chat_measuring_list = {}

	self.chat_old_msg_string = ""
	self.old_read_msg_index_list = {}
	self.new_read_msg_index_list = {}

	self.new_msg_read_sign = {
		[10] = false,
		[20] = false,
		[30] = false,
		[40] = false,
		[50] = false,
		[60] = false,
		[70] = false,
		[80] = false,
	}
end

function NewChatWindow:__delete()
	self.cur_active = false
	self.click_voice_btn = false
	self.guide_module_namee = nil
	self.guide_module_index = -1
	self.curr_send_channel = CHANNEL_TYPE.SYSTEM	-- 发送频道
	if self.chat_measuring ~= nil then
		--ResMgr:Destroy(self.chat_measuring.gameObject)
		self.chat_measuring:DeleteMe()
		self.chat_measuring = nil
	end

	for k,v in pairs(self.chat_measuring_list) do
		v:DeleteMe()
	end
	self.chat_measuring_list = {}

	for k,v in pairs(self.measuring_cell_obj_list) do
		ResMgr:Destroy(v)
	end
	self.measuring_cell_obj_list = {}
	self.text_warner = nil
	self.worn_center_text = nil
	self.dati = nil

	self.chat_old_msg_string = nil
end


function NewChatWindow:GetCurWin()
	return self.curr_send_channel
end


function NewChatWindow:ReleaseCallBack()
	self.curr_send_channel = CHANNEL_TYPE.SYSTEM	-- 发送频道
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.input_view then
		self.input_view:DeleteMe()
		self.input_view = nil
	end
	if nil ~= self.roll_timer then
		GlobalTimerQuest:CancelQuest(self.roll_timer)
		self.roll_timer = nil
	end

	self.list_view_list = {}
	self.list_view_first_flush = {}

	self.rich_text = nil

	if self.chat_window_time_quest then
		GlobalTimerQuest:CancelQuest(self.chat_window_time_quest)
		self.chat_window_time_quest = nil
	end

	if self.botton_icon_time_quest then
		GlobalTimerQuest:CancelQuest(self.botton_icon_time_quest)
		self.botton_icon_time_quest = nil
	end

	if self.chat_pb_change_event then
		GlobalEventSystem:UnBind(self.chat_pb_change_event)
		self.chat_pb_change_event = nil
	end

	if self.chat_measuring then
		self.chat_measuring:DeleteMe()
		self.chat_measuring = nil
	end

	for k,v in pairs(self.chat_measuring_list) do
		v:DeleteMe()
	end
	self.chat_measuring_list = {}

	for k,v in pairs(self.measuring_cell_obj_list) do
		ResMgr:Destroy(v)
	end
	self.measuring_cell_obj_list = {}


	if CountDownManager.Instance:HasCountDown("question_begin_countdown") then
		CountDownManager.Instance:RemoveCountDown("question_begin_countdown")
	end
	if CountDownManager.Instance:HasCountDown("question_star_countdown") then
		CountDownManager.Instance:RemoveCountDown("question_star_countdown")
	end
	if nil ~= self.cd_timer then
		GlobalTimerQuest:CancelQuest(self.cd_timer)
		self.cd_timer = nil
	end

	if self.bag then
		self.bag:DeleteMe()
		self.bag = nil
	end
	if self.stuff_bag then
		self.stuff_bag:DeleteMe()
		self.stuff_bag = nil
	end
	if self.cur_show_panel ~= nil then
		self.cur_show_panel=nil
	end

	if self.grid_normal_face ~= nil then
		self.grid_normal_face:DeleteMe()
		self.grid_normal_face = nil
	end

	if self.grid_word_face ~= nil then
		self.grid_word_face:DeleteMe()
		self.grid_word_face = nil
	end

	if self.grid_font_face ~= nil then
		self.grid_font_face:DeleteMe()
		self.grid_font_face = nil
	end

	if self.event_listener ~= nil then
		self.event_listener = nil
	end

	self.text_warner = nil
	self.worn_center_text = nil
	self.answer_num = nil
	self.dati = nil

	if self.role_head_cell ~= nil then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	if self.system_list then
		self.system_list:DeleteMe()
		self.system_list = nil
	end
	if self.chuanwen_list then
		self.chuanwen_list:DeleteMe()
		self.chuanwen_list = nil
	end
	if self.world_list then
		self.world_list:DeleteMe()
		self.world_list = nil
	end
	if self.guild_list then
		self.guild_list:DeleteMe()
		self.guild_list = nil
	end
	if self.team_list then
		self.team_list:DeleteMe()
		self.team_list = nil
	end
	if self.zudui_list then
		self.zudui_list:DeleteMe()
		self.zudui_list = nil
	end
	if self.kuafu_list then
		self.kuafu_list:DeleteMe()
		self.kuafu_list = nil
	end
	if self.zhandui_list then
		self.zhandui_list:DeleteMe()
		self.zhandui_list = nil
	end
	if self.scene_list then
		self.scene_list:DeleteMe()
		self.scene_list = nil
	end

    if self.guild_answer_true then
        GlobalEventSystem:UnBind(self.guild_answer_true)
        self.guild_answer_true = nil
    end

    if self.guild_answer_alert then
    	self.guild_answer_alert:DeleteMe()
    	self.guild_answer_alert = nil
    end

    --销毁拍一拍
    self:PYPReleaseCallBack()
    ChatWGData.Instance:ReSetAiTeBlockList()

    if self.cur_show_scroll_go then
    	self.cur_show_scroll_go = nil
    end

    self.new_msg_read_sign = {
		[10] = false,
		[20] = false,
		[30] = false,
		[40] = false,
		[50] = false,
		[60] = false,
		[70] = false,
		[80] = false,
	}

	self.question_id_cache = nil
end

function NewChatWindow:CloseCallBack()
	MainuiWGCtrl.Instance:SetChangeChatState(true)
	ChatWGData.Instance:SetIsChatBag(false)
	self:ClearReadIndexList()
	self:ClearCheckAiteTimer()
end

function NewChatWindow:GetEditText()
	return self.node_list.chat_input.input_field.text or ""
end

function NewChatWindow:AddEditText(text)
	self.node_list.chat_input.input_field.text = self.node_list.chat_input.input_field.text .. text
end

function NewChatWindow:ChangeInputFieldWidth()
	local is_shield_voice = GLOBAL_CONFIG.param_list.shield_chat_voice == 1 -- ChatWGData.IS_PB_AUDIO_CHAT
	local width = is_shield_voice and HIDE_VOICE_WIDTH or SHOW_VOICE_WIDTH
	self.node_list.chat_input.rect.sizeDelta = Vector2(width, self.node_list.chat_input.rect.sizeDelta.y)
	self.node_list["voice_btn"]:SetActive(not is_shield_voice)
end

function NewChatWindow:RegisterBtnClickEvent()
	-- self.node_list["select_city_btn"].button:AddClickListener(BindTool.Bind(self.OnSelectCity, self)) --城市
	self.node_list.setting_btn.button:AddClickListener(BindTool.Bind(self.OnSetting, self))		--设置
	self.node_list.open_horn_btn.button:AddClickListener(BindTool.Bind(self.OpenHorn, self)) --打开右侧喇叭面板
	-- self.node_list.voice_btn.button:AddClickListener(BindTool.Bind(self.ClickVoiceBtn, self)) 	--点击语音按钮
	self.node_list.send_btn.button:AddClickListener(BindTool.Bind(self.OnSendMessage, self))		--发送出去按钮
	self.node_list.translate_btn.button:AddClickListener(BindTool.Bind(self.OperateActive, self)) --转换按钮
	self.node_list["bottom_mask_more_btn"].button:AddClickListener(BindTool.Bind(self.OperateActive, self)) --不可聊天时的更多按钮
	--self.node_list.btn_location.button:AddClickListener(BindTool.Bind(self.OperateLocation, self))	--游戏位置
	self.node_list.hongbao_btn.button:AddClickListener(BindTool.Bind(self.OpenWorldPaper, self))	--游戏位置
	self.node_list.Block.button:AddClickListener(BindTool.Bind(self.Close, self, true))		--关闭页面
	self.node_list.Block1.button:AddClickListener(BindTool.Bind(self.CloseMore, self))
	self.node_list.to_go.button:AddClickListener(BindTool.Bind(self.ToGo, self))
	self.node_list["btn_aite_me"].button:AddClickListener(BindTool.Bind(self.OnClickAiTeMeBtn, self))
	self.node_list["btn_new_msg"].button:AddClickListener(BindTool.Bind(self.OnClickNewMsg, self))
	self.node_list["btn_friend"].button:AddClickListener(BindTool.Bind(self.OnClickBtnFriend, self))
	self.node_list["PositionBtn"].button:AddClickListener(BindTool.Bind(self.ToPos, self))
	
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local limint_level, limint_vip_level ,limint_real_chongzhi = ChatWGData.Instance:GetChatLevelLimint(CHANNEL_TYPE.SPEAKER)
	--暂时直接屏蔽
	self.node_list.open_horn_btn:SetActive(false)--limint_level <= role_level and not IS_AUDIT_VERSION)
end

function NewChatWindow:CreateFaceGirdList()
	local bundle = "uis/view/chat_ui_prefab"
	local asset = "NorItemRender"
	if self.grid_normal_face == nil then
		self.grid_normal_face  = AsyncBaseGrid.New()
		self.table_data = ChatWGData.Instance:GetNormalFaceCfg()
		self.grid_normal_face:CreateCells({col = 8, cell_count = #self.table_data,
										   assetName = asset,
										   assetBundle = bundle,
										   itemRender = EmojiIconCell,
										   list_view = self.node_list.normal_face_list
										  })
		self.grid_normal_face:SetSelectCallBack(BindTool.Bind(self.ClickCallBack, self))
		self.grid_normal_face:SetStartZeroIndex(false)
	end


	-- self.grid_font_face = AsyncListView.New(ChatWordFaceRender, self.node_list.font_face_list)
	if nil == self.wordface_cfg then
		self.wordface_cfg = ChatWGData.Instance:GetWordFaceCfg()
	end	
	local bundle1 = "uis/view/chat_ui_prefab"
	local asset1 = "Font_ItemRender"
	self.grid_font_face  = AsyncBaseGrid.New()
	self.grid_font_face:CreateCells({col = 3, cell_count = #self.wordface_cfg,
									 assetName = asset1,
									 assetBundle = bundle1,
									 itemRender = ChatWordFaceRender,
									 list_view = self.node_list.font_face_list
									})

	self.grid_font_face:SetSelectCallBack(BindTool.Bind(self.ClickFontFace, self))
	self.grid_font_face:SetStartZeroIndex(false)

	if self.grid_word_face == nil then
		self.big_table_data = ChatWGData.Instance:GetGifFaceCfg()
		local bundle2 = "uis/view/chat_ui_prefab"
		local asset2 = "Big_ItemRender"
		self.grid_word_face = AsyncBaseGrid.New()
		self.grid_word_face:CreateCells({col = 8, cell_count = #self.big_table_data,
										 assetName = asset2,
										 assetBundle = bundle2,
										 itemRender = BigFaceIconCell,
										 list_view = self.node_list.big_face_list })
		self.grid_word_face:SetStartZeroIndex(false)
		self.grid_word_face:SetSelectCallBack(BindTool.Bind(self.ClickCallBack, self))
	end
end


function NewChatWindow:SetTabVisible()
	if nil == self.tabbar then
		return
    end

    --跨服组数限制页签显示
    ---[[
    for channel_type, channel_tab in pairs(ChatTabIndex) do
    	if channel_type == CHANNEL_TYPE.CROSS then
			local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
    		self.tabbar:SetToggleVisible(channel_tab, is_cross_server_stage)
    	end
    end
    --]]
end

function NewChatWindow:SetMoreToggleVisible()
	--[[--聊天--更多界面:跨服频道的特殊处理(策划又说去掉)
	local is_spc_show = self.curr_send_channel == CHANNEL_TYPE.CROSS
	for k, chat_more_type in pairs(CHAT_MORE) do
		if self.node_list["more_toggle" .. chat_more_type] then
			if is_spc_show then
				self.node_list["more_toggle" .. chat_more_type]:SetActive(chat_more_type == CHAT_MORE.FACE or chat_more_type == CHAT_MORE.ZHAOHU)
			else
				self.node_list["more_toggle" .. chat_more_type]:SetActive(CHAT_MORE_DEF_SHOW[chat_more_type])
			end
		end
	end
	--]]

	--互动功能开启
	local is_shield_pyp = ChatWGData.Instance:GetIsShieldPYP()
	if self.node_list["more_toggle" .. CHAT_MORE.ZHAOHU] then
		self.node_list["more_toggle" .. CHAT_MORE.ZHAOHU]:SetActive(not is_shield_pyp)
	end
end

function NewChatWindow:LoadCallBack()
	-- if loaded_times <= 1 then
        if not self.tabbar then
        	local remind_tab = {
        		{RemindName.ChatChannelSystem},
        		{RemindName.ChatChannelChuanWen},
        		{RemindName.ChatChannelZuDui},
				{RemindName.ChatChannelTeam},
				{RemindName.ChatChannelScene},
				{RemindName.ChatChannelGuild},
				{RemindName.ChatChannelWorld},
				{RemindName.ChatChannelCross},
        	}
            self.tabbar = Tabbar.New(self.node_list)
            local tab_group = IS_AUDIT_VERSION and Language.Chat.AuditTabGroup or Language.Chat.TabGroup
            self.tabbar:Init(tab_group, nil, "uis/view/chat_ui_prefab", nil, remind_tab)
            self.tabbar:SetSelectCallback(BindTool.Bind1(self.TabChange, self))
            self.tabbar:SetInstanceParent(self.node_list.TabbarContainer)
            self.tabbar:SetLocalPosition(0, 0, 0)
            --self.tabbar:SetCreateVerCallBack(function ()
            --	self:TabChange(ChatTabIndex[CHANNEL_TYPE.WORLD])
            --end)
            self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabVisible, self))
        end
        self:RegisterBtnClickEvent()

        self:CreateFaceGirdList()
        self:HideMsgInput()

		
        for i=2,6 do
            self.node_list["more_toggle"..i].toggle.isOn =false
        end
		self.node_list["more_toggle"..1].toggle.isOn =true

        for i=1,10 do
            self.node_list["more_toggle"..i].toggle:AddValueChangedListener(BindTool.Bind(self.SelectToggleCallback, self, i))
        end

        -- for i=1,2 do
        --     self.node_list["face_toggle"..i].toggle:AddValueChangedListener(BindTool.Bind(self.SelectFaceToggleCallback, self, i))
        -- end

        -- for i=1,2 do
        -- 	self.node_list["toggle_bag_"..i].toggle:AddValueChangedListener(BindTool.Bind(self.SelectBagToggleCallback, self, i))
        -- end

		XUI.AddClickEventListener(self.node_list["anti_fraud_cartoon_btn"], BindTool.Bind(self.OnClickAntiFraudCartoonBtn, self)) 		-- 打开防骗漫画的按钮
		self.node_list.PYP_InputField.input_field.onSelect:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, true))
		self.node_list.PYP_InputField.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, false))


		self:FlushHideOrActive(self.cur_active)
		--创建不同类型的聊天内容
		self.list_view_list = {}

		self.system_list = ChatListView.New()
		self.system_list:Create(ChatCell, self.node_list.chat_system_list)
		self.system_list:SetChannelType(CHANNEL_TYPE.SYSTEM)
		self.system_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.system_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.SYSTEM)] = self.system_list

		self.chuanwen_list = ChatListView.New()
		self.chuanwen_list:Create(ChatCell, self.node_list.chat_chuanwen_list)
		self.chuanwen_list:SetChannelType(CHANNEL_TYPE.CHUAN_WEN)
		self.chuanwen_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.chuanwen_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.CHUAN_WEN)] = self.chuanwen_list

		self.world_list = ChatListView.New()
		self.world_list:Create(ChatCell, self.node_list.chat_world_list)
		self.world_list:SetChannelType(CHANNEL_TYPE.WORLD)
		self.world_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.world_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.WORLD)] = self.world_list

		self.guild_list = ChatListView.New()
		self.guild_list:Create(ChatCell, self.node_list["chat_guild_list"])
		self.guild_list:SetChannelType(CHANNEL_TYPE.GUILD)
		self.guild_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.guild_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.GUILD)] = self.guild_list

		self.team_list = ChatListView.New()
		self.team_list:Create(ChatCell, self.node_list["chat_team_list"])
		self.team_list:SetChannelType(CHANNEL_TYPE.TEAM)
		self.team_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.team_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.TEAM)] = self.team_list

		self.zudui_list =  ChatListView.New()
		self.zudui_list:Create(ChatCell, self.node_list["chat_zudui_list"])
		self.zudui_list:SetChannelType(CHANNEL_TYPE.ZUDUI)
		self.zudui_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.zudui_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.ZUDUI)] = self.zudui_list

		---[[
		self.kuafu_list =  ChatListView.New()
		self.kuafu_list:Create(ChatCell, self.node_list["chat_kuafu_list"])
		self.kuafu_list:SetChannelType(CHANNEL_TYPE.CROSS)
		self.kuafu_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.kuafu_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.CROSS)] = self.kuafu_list
		--]]

		---[[
		--场景(附近)频道
		self.scene_list = ChatListView.New()
		self.scene_list:Create(ChatCell, self.node_list["chat_scene_list"])
		self.scene_list:SetChannelType(CHANNEL_TYPE.SCENE)
		self.scene_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
		self.scene_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
		self.list_view_list[tostring(CHANNEL_TYPE.SCENE)] = self.scene_list
		--]]

		--[[--屏蔽战队频道
		self.zhandui_list =  ChatListView.New()
		self.zhandui_list:Create(ChatCell, self.node_list["chat_zhandui_list"])
		self.zhandui_list:SetChannelType(CHANNEL_TYPE.ZHANDUI3V3)
		self.list_view_list[tostring(CHANNEL_TYPE.ZHANDUI3V3)] = self.zhandui_list
		--]]

        self:HandleRedPacketEntry()

        self.text_warner = self.node_list["worn_text"]
        self.worn_center_text = self.node_list["worn_center_text"]
        self.answer_num = self.node_list["answer_num"]
        self.dati = self.node_list["dati"]

        

        self.on_point_down = BindTool.Bind1(self.OnPointDown, self)
        self.on_point_up = BindTool.Bind1(self.OnPointUp, self)
		self.on_drag = BindTool.Bind1(self.OnDrag, self)
        self.event_listener = self.node_list["voice_btn"].event_trigger_listener
        if self.event_listener ~= nil then
            self.event_listener:AddPointerDownListener(self.on_point_down)		--按下
            self.event_listener:AddPointerUpListener(self.on_point_up)
			self.event_listener:AddDragListener(self.on_drag)
        end

        self:ToBag()
        if self.role_head_cell == nil then
            self.role_head_cell = RoleHeadCell.New(false)
        end
		self:ChangeInputFieldWidth()
		-- self.chat_pb_change_event = GlobalEventSystem:Bind(OtherEventType.CHAT_PB_CHANGE, BindTool.Bind1(self.ChangeInputFieldWidth, self))
	-- end
	self.guild_answer_true = GlobalEventSystem:Bind(OtherEventType.GUILD_ANSWER_TRUE, BindTool.Bind(self.PlayAnswerTrueEffect, self))

	self.node_list["chat_input_def"].text.text = Language.Chat.MsgDefInputDesc_1
	self.node_list["chat_input"].input_field.onValueChanged:AddListener(BindTool.Bind(self.OnChatInputChange, self))

	self.node_list.new_msg.rect.anchoredPosition = Vector2(-50, -120)
	self.node_list.new_msg:SetActive(false)
	self.node_list["btn_new_msg"].transform:DOLocalMoveY(self.node_list["btn_new_msg"].transform.localPosition.y + 10, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)

	for i = 1, 4 do
		XUI.AddClickEventListener(self.node_list["btn_dati_ans" .. i], BindTool.Bind(self.OnClickDatiOptionBtn, self, i)) 
	end
end

function NewChatWindow:DoAniOpenOrHide()
	if self.m_IsBusy then
		return
	end
	self.m_IsBusy=true

	if self.m_IsShow then
		self.rect_target:DOPlayForward()
	else
		self.rect_target:DOPlayBackwards()
	end
	self.m_IsShow = not self.m_IsShow
end

function NewChatWindow:OnInputFieldClickOrEndEdit(is_need_clear)
	if is_need_clear then
		self.node_list.Placeholder.text.text = ""
	else
		self.node_list.Placeholder.text.text = Language.Chat.PlaceholderText
	end
end

function NewChatWindow:OnPointDown(event_data)
	self:HandleVoiceStart()
	AutoVoiceWGCtrl.Instance:SetIsMainChatVoice(false)
	self.record_point_down_y = event_data.position.y
end

function NewChatWindow:OnDrag(event_data)
	local delta = math.abs(self.record_point_down_y - event_data.position.y)
	local is_in_range = delta < 46
	AutoVoiceWGCtrl.Instance:SetState(is_in_range and VoiceViewDragState.InRange or VoiceViewDragState.OutRange)
end

function NewChatWindow:OnPointUp(event_data)
	local delta = math.abs(self.record_point_down_y - event_data.position.y)
	local is_cancel_voice = delta > 46
	self:HandleVoiceStop(is_cancel_voice)
end

function NewChatWindow:CloseChatView()
end

function NewChatWindow:Close(is_click_close)
	if self:IsOpen() == false then return end
	if not self.node_list["layout_chatwindow_root"] then return end
	if is_click_close then
		local ani = self.node_list["layout_chatwindow_root"].animator
		if ani:GetBool("show") == false then
			--if self:CheckIsShowAnswer() then
				--GuildAnswerWGCtrl.Instance:CloseRankView()
			--end
			return
		end
		ani:SetBool("show",false)
		GlobalTimerQuest:AddDelayTimer(function()
			ani:SetBool("show",true)
			SafeBaseView.Close(self)

		end,0.5)
	else
		SafeBaseView.Close(self)
	end
	MainuiWGCtrl.Instance:HideOrShowChatView(false)
	if self:CheckIsShowAnswer() then
		GuildAnswerWGCtrl.Instance:CloseRankView()
	end

	GuildBossWGCtrl.Instance:CloseGuildBossReward()
	GuildAnswerWGCtrl.Instance:ShowOrHideGoDaTiBtn()
	GuildAnswerWGCtrl.Instance:ShowOrHideAnswerRankList(true)
	GuildBossWGCtrl.Instance:ShowOrHideHurtRankList(true)
end

function NewChatWindow:OpenCallBack()
	ChatWGCtrl.Instance:FinishCalcH()
end

function NewChatWindow:OnFlush(param_list)
	for k,v in pairs(param_list) do
		if k == "all" then
            for k1,v1 in pairs(v) do
				if k1 == "open_param" then
                    if self.node_list["chat_input"] then
                        self:CleanInput()
						self:InptuValueChange(v1)
					end
				elseif k1 == "guild_baoxiang_param" then 	--打开聊天界面输入内容带物品id
					if self.node_list["chat_input"] then
                        self:CleanInput()
                        -- local item_data = {item_id = v1.item_id,is_bind = 0,num = 1}
						-- ChatWGData.Instance:InsertItemTab(item_data, true)
						-- ChatWGData.Instance:InsertOpenLinkTab(v1.openLink,v1.quality,v1.role_id)
						self.guild_baoxiang_param = {}
						self.guild_baoxiang_param["openLink"] = v1.openLink
						self.guild_baoxiang_param["quality"] = v1.quality
						self.guild_baoxiang_param["role_id"] = v1.role_id
						self:InptuValueChange(v1.text)
					end
				end
			end
			self.node_list["LeftBottomContainer"]:SetActive(not IS_AUDIT_VERSION)
			self.node_list["translate_btn"]:SetActive(not IS_AUDIT_VERSION)
			--self.node_list["hongbao_btn"]:SetActive(not IS_AUDIT_VERSION)
		end
	end
end

function NewChatWindow:SelectToggleCallback(i, isOn)
    if isOn then
        if i == SOCOETY_BTN_TYPE.BAG then
            self:ToBag(SOCOETY_BTN_TYPE.BAG)
        elseif i == SOCOETY_BTN_TYPE.BAG2 then
            self:ToBag(SOCOETY_BTN_TYPE.BAG2)
        elseif i == SOCOETY_BTN_TYPE.NORNAL_FACE then
            self:ToFace(SOCOETY_BTN_TYPE.NORNAL_FACE)
		elseif i == SOCOETY_BTN_TYPE.BIG_FACE then
            self:ToFace(SOCOETY_BTN_TYPE.BIG_FACE)
        elseif i == SOCOETY_BTN_TYPE.TOUZI then
            self:ToTouZi()
        elseif i == SOCOETY_BTN_TYPE.HONGBAO then
            self:ToHongBao()
        elseif i == SOCOETY_BTN_TYPE.ZHAOJI then
            self:ToZhaoJi()
        elseif i == SOCOETY_BTN_TYPE.POS then
            self:ToPos()
        elseif i == SOCOETY_BTN_TYPE.ZHAOHU then
        	self:ToZhaoHu()
        	self:PYPShowIndexCallBack()
        elseif i == SOCOETY_BTN_TYPE.SPEAK then
            self:ToFontFace()
        end
        self.more_index = i
    end
end
function NewChatWindow:OpenWorldPaper()
	ViewManager.Instance:Open(GuideModuleName.WorldRedPaper)
end

function NewChatWindow:SelectFaceToggleCallback(i)
	if i == SOCOETY_BTN_TYPE.NORNAL_FACE then
		self:ToNormalFace()
	elseif i == SOCOETY_BTN_TYPE.BIG_FACE then
		self:ToBigFace()
	-- elseif i ==3 then
	-- 	self:ToFontFace()
	end
	self.face_index = i
end

function NewChatWindow:ToNormalFace()
	self.node_list["normal_face_list"]:SetActive(true)
	self.node_list["big_face_list"]:SetActive(false)
	self.grid_normal_face:SetDataList(self.table_data, 3)
end

function NewChatWindow:CloseMore()
	self.cur_active = false
	self:FlushHideOrActive(false)
	--end
end

function NewChatWindow:HandleBagOnClick(cell)
	if nil == cell then
		return
	end
	local item_data = cell:GetData()
	if nil == item_data then
		return
	end
	local cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	--self:FlushHideOrActive(false)
	--self.cur_active = not self.cur_active
	local edit_text = ChatWGCtrl.Instance:GetEditTextByCurPanel()
	-- --转换物品名字到输入框
	if ChatWGData.ExamineEditText(edit_text, 2) then
		edit_text = "[" .. cfg.name .. "]"
		ChatWGData.Instance:InsertItemTab(item_data, true)
		ChatWGCtrl.Instance:AddTransmitInputEdit(edit_text)
	end
end

function NewChatWindow:ClickFontFace(cell, cell_index, is_default, is_click)
	-- if ChatWGData.Instance:IsPingBiChannel(self.curr_send_channel) then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PingBiCanNotChat)
	-- 	return
	-- end
	-- if not is_click then
	-- 	return
	-- end

	if nil == cell then
		return
	end

	if self:CurrentIsPingBi() then return end
	local data = cell:GetData()
	if nil == data then
		return
	end
	self.wordface_cfg = ChatWGData.Instance:GetWordFaceCfg()
	ChatWGCtrl.Instance:SendChatText(data.content,true)--self.wordface_cfg[data-1].content
	self:CloseMore()
end

function NewChatWindow:CurrentIsPingBi()
	if ChatWGData.Instance:IsPingBiChannel(self.curr_send_channel) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PingBiCanNotChat)
		return true
	end
	return false
end

function NewChatWindow:ClickCallBack(cell)
	local data = cell:GetData()
	if nil == data then
		return
	end

	if data.vip_limit and data.vip_limit > VipWGData.Instance:GetVipLevel() then
		--VIP等级不足
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_4)
		ChatWGCtrl.Instance:OpenChatVipLimitTipView()
		return
	end

	local id = data.id
	local face_desc = "/" .. id
	if self:CheckFaceAutoSend() then
		-- print_error("FFFFF====== 自动发送", face_desc)
		ChatWGCtrl.Instance:SendChatText(face_desc,true)
		self:CloseMore()
		return
	end

	-- --数据转换
	if nil ~= ChatWGCtrl.Instance:IsOpenHornPopView() then
		if ChatWGData.ExamineEditText(ChatWGCtrl.Instance:GetHornInputEdit(), 2) then
			ChatWGCtrl.Instance:AddHornInputEdit(face_desc)
			-- ChatWGData.Instance:InsertItemTab(data)
		end
	else
		if ChatWGData.ExamineEditText(ChatWGCtrl.Instance:GetTransmitInputEdit(), 2) then
			local cur_data = self.face_index == 1 and self.table_data or self.big_table_data
			ChatWGCtrl.Instance:AddTransmitInputEdit(face_desc)
			-- ChatWGData.Instance:InsertItemTab(data)
		end
	end
end

function NewChatWindow:ToBigFace()
	self.node_list["normal_face_list"]:SetActive(false)
	self.node_list["big_face_list"]:SetActive(true)
	self.grid_word_face:SetDataList(self.big_table_data, 0)
end

function NewChatWindow:ToFontFace()
	self:ChangeToPanel(self.node_list["speak_root"])
	self.grid_font_face:SetDataList(self.wordface_cfg)
end


function NewChatWindow:TabChange(index)
	if self.show_index ~= index then
		self.node_list.new_msg:SetActive(self.new_msg_read_sign[index])
	end
	self:ChangeToIndex(index)
end

function NewChatWindow:ChangeToContent(tab_index)
	local content_name = Content_Name[tab_index]
	if not content_name then
		return
	end

	for k,v in pairs(Content_Name) do
		self.node_list[v]:CustomSetActive(k == tab_index)
	end
end

function NewChatWindow:ToGo()
	if self.guide_module_namee and self.guide_module_index then
		if self.guide_module_namee == GuideModuleName.Guild then--特殊处理:未加入仙盟,仙盟在功能开启处做了判断,故改用接口跳转
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild)
			TipWGCtrl:ShowSystemMsg(Language.Common.CanNotEnterNoGuild)
		else
			FunOpen.Instance:OpenViewByName(self.guide_module_namee, self.guide_module_index)
		end
		self:Close()
		--ViewManager.Instance:Open(self.guide_module_namee, self.guide_module_index)
	end
end

function NewChatWindow:HandleMask()
	local is_show_mask, str, is_middle, name, index, is_can = self:CheckBottomMaskActive()
	if is_show_mask then
		local chat_index = ChatTabIndex[CHANNEL_TYPE.TEAM]
		if Content_Name[chat_index] then
			self.node_list[Content_Name[chat_index]]:CustomSetActive(false)
		end

		self.node_list["bottom_mask"]:SetActive(true)
		self.node_list["InputPanel"]:SetActive(false)
		if is_middle then
			self.node_list["bottom_mask_bg"]:SetActive(false)
			self.node_list["mask_yueliang_bg"]:SetActive(true)
			self.node_list["middle_text"]:SetActive(not is_can)
			self.node_list["middle_text1"]:SetActive(is_can)
			self.node_list["middle_text"].text.text = str
			self.node_list["middle_text1"].text.text = str
			self.node_list["to_go"]:SetActive(name ~= nil and index ~= nil)
			if name ~= nil and index ~= nil then
				self.guide_module_namee = name
				self.guide_module_index = index
			end
		else
			self.node_list["to_go"]:SetActive(false)
			self.node_list["bottom_mask_bg"]:SetActive(true)
			self.node_list["mask_yueliang_bg"]:SetActive(false)
			self.node_list["bottom_mask_text"].text.text = str
		end

		self:CheckBottomMaskMoreBtnActive()
	else
		self.node_list["bottom_mask"]:SetActive(false)
		self.node_list["InputPanel"]:SetActive(true)
	end
	self:ShowBtnCd()
	self.cur_active = false
	self:FlushHideOrActive(false)
end

function NewChatWindow:IsChatLevelLimit(chat_type)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local real_chongzhi_rmb = RechargeWGData.Instance:GetRealChongZhiRmb()

	local limint_level, limint_vip_level ,limint_real_chongzhi= ChatWGData.Instance:GetChatLevelLimint(chat_type)

	local is_open = false
	if role_level >= limint_level and ((limint_vip_level > 0 and vip_level >= limint_vip_level) or limint_vip_level == 0 )
		and real_chongzhi_rmb >= limint_real_chongzhi then
   		is_open = true
	end

	local str = ""
	if real_chongzhi_rmb < limint_real_chongzhi then
		str = string.format(Language.Chat.PindaoOpenHint[4], limint_real_chongzhi)
	else
		if limint_level > 0 and limint_vip_level > 0 then
			str = string.format(Language.Chat.PindaoOpenHint[3], limint_level, limint_vip_level)	
		elseif limint_vip_level > 0 then
			str = string.format(Language.Chat.PindaoOpenHint[2], limint_vip_level)
		elseif limint_level > 0 then
			str = string.format(Language.Chat.PindaoOpenHint[1], limint_level)
		end
	end


	return is_open, str
end

function NewChatWindow:CheckBottomMaskActive()
	--系统,组队,传闻  显示  请切换到其他频道聊天
	if self.curr_send_channel == CHANNEL_TYPE.SYSTEM or self.curr_send_channel == CHANNEL_TYPE.CHUAN_WEN then
		return true, Language.Chat.BottomMaskText_Sys, false, nil, nil, false
	end

	if self.curr_send_channel == CHANNEL_TYPE.ZUDUI then
		return true, Language.Chat.BottomMaskText_Sys,false,nil,nil,false
	end
	--[[
	--跨服 跨服频道禁止聊天，可发送表情
	if self.curr_send_channel == CHANNEL_TYPE.CROSS then
		return true, Language.Chat.BottomMaskText_Cross_1, false, nil, nil, false
	end
	--]]
	--仙盟 请先加入一个仙盟
	if self.curr_send_channel == CHANNEL_TYPE.GUILD then
		if RoleWGData.Instance.role_vo.guild_id <= 0 then
			return true, Language.Chat.BottomMaskText_Guild, true, GuideModuleName.Guild, TabIndex.guild_guildlist, false
		end
	end
	--队伍 请先加入一个队伍
	if self.curr_send_channel == CHANNEL_TYPE.TEAM then
        if 0 == SocietyWGData.Instance:GetIsInTeam() then
            if Scene.Instance:GetIsOpenCrossViewByScene() then
                return true, Language.Chat.BottomMaskText_Team, true, GuideModuleName.CrossTeamView, 0, false
            else
                return true, Language.Chat.BottomMaskText_Team, true, GuideModuleName.NewTeamView, TabIndex.team_pingtai, false
            end
		end
	end
	--战队 请先加入一个战队
	if self.curr_send_channel == CHANNEL_TYPE.ZHANDUI3V3 then
		if not ZhanDuiWGData.Instance:GetIsInZhanDui() then
			return true, Language.Chat.BottomMaskText_ZhanDui, true, nil, nil, true
		end
	end

	local is_open, str = self:IsChatLevelLimit(self.curr_send_channel)
	if not is_open then
		return true, str, false
	end	

	return false, "", false, nil, nil, false
end

function NewChatWindow:CheckBottomMaskMoreBtnActive()
	local is_show = false
	-- if self.curr_send_channel == CHANNEL_TYPE.CROSS then--跨服频道特殊处理
	-- 	is_show = true
	-- end
	self.node_list["bottom_mask_more_btn"]:SetActive(is_show)
end

--检测表情是否自动发送
function NewChatWindow:CheckFaceAutoSend()
	-- local is_auto = self.curr_send_channel == CHANNEL_TYPE.CROSS
	local is_auto = false
	return is_auto
end

function NewChatWindow:ShowIndexCallBack(index)
	MainuiWGCtrl.Instance:SetChangeChatState(false)

	local _, is_clear_cd = ChatWGData.Instance:GetChannelCdEndTime(self.curr_send_channel)
	if is_clear_cd then
		self:ClearCdTimer()
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Send
		end
	end

	self.curr_send_channel = TabeIndexChannel[index]
    self:ChangeToContent(index)

	if self.curr_send_channel == CHANNEL_TYPE.GUILD then
		self:RefreshChannel(nil, true)
	else
		self:RefreshChannel()
	end

    self:HandleMask()
	local channel_type = TabeIndexChannel[index]
	self.tabbar:ChangeToIndex(index)
	ChatWGData.Instance:ClearChannelUnreadMsg(channel_type)

	GuildBossWGCtrl.Instance:ShowOrHideHurtRankList(false)

	if self:CheckIsShowAnswer() then 	--符合条件才显示
		if index == ChatTabIndex[CHANNEL_TYPE.GUILD] and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) then
			GuildAnswerWGCtrl.Instance:OpenRankView()
			GuildAnswerWGCtrl.Instance:ShowOrHideAnswerRankList(false)
			--去掉限制，每次打开都弹提示
			-- if Scene.Instance:GetSceneType() ~= SceneType.GUILD_ANSWER_FB then
			-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.SceneChatTip)
			-- end
		else
			GuildAnswerWGCtrl.Instance:CloseRankView()
			GuildAnswerWGCtrl.Instance:ShowOrHideAnswerRankList(true)
		end
	end

	if GuildBossWGData.Instance:GetGuildBossIsPass() then
		if index == ChatTabIndex[CHANNEL_TYPE.GUILD] then
			GuildBossWGCtrl.Instance:OpenGuildBossReward()
		else
			GuildBossWGCtrl.Instance:CloseGuildBossReward()
		end
	end

	self:HandleTimeStamp(channel_type)
	self:RemoveInvityTip(index)
    self:HandleRedPacketEntry()
    self:SetTabVisible()
end

function NewChatWindow:HandleRedPacketEntry()
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    self.node_list.more_toggle5:SetActive(false)
end

function NewChatWindow:RemoveInvityTip(index)
	if not TabIndexUnReadInviteTip[index] then return end
	MainuiWGCtrl.Instance:RemoveTipIconByIconObj(TabIndexUnReadInviteTip[index])
end

function NewChatWindow:HandleTimeStamp(channel_type)
	if not channel_type then return end
	if channel_type ~= CHANNEL_TYPE.GUILD and channel_type ~= CHANNEL_TYPE.WORLD then return end
	local chat_data = ChatWGData.Instance
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	chat_data:SetChannelTimeStamp(channel_type,math.ceil(server_time))
	local msg_list = chat_data:GetChannel(channel_type).msg_list
	if #msg_list < 0 then return end
	if not msg_list[#msg_list] then return end
	if not msg_list[#msg_list].msg_timestamp then return end
	if msg_list[#msg_list].msg_timestamp > self.old_channel_msg_count_list[channel_type] then --发送时间戳
		ChatWGCtrl.Instance:SaveChannelInfo()
	end
	self.old_channel_msg_count_list[channel_type] = msg_list[#msg_list].msg_timestamp
end

-- 当前频道
function NewChatWindow:GetChannel()
	return self.curr_send_channel
end

-- 刷新频道
function NewChatWindow:RefreshChannel(flush_channel, is_flush_guild_anser, force_flush)
	if nil == self.list_view_list then
		return
	end

	if not self:IsOpen() then
		return
	end

	local flush_chan = flush_channel or self.curr_send_channel
	local channel = ChatWGData.Instance:GetChannel(flush_chan)
	if nil == channel then
		return
	end

	NewChatWindow.UpdateContentListView(self.list_view_list[tostring(flush_chan)], channel.msg_list, channel.unread_num, flush_channel, self.curr_send_channel, force_flush)
	self:FlushCheckAiteTimerState()
	channel.unread_num = 0
	self:HandleTimeStamp(TabeIndexChannel[self.show_index])

	if is_flush_guild_anser then
		self:GuildAnswer()
	end
	if flush_channel == CHANNEL_TYPE.GUILD and self:IsOpen() then
		if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) and self:CheckIsShowAnswer() then
			if not GuildAnswerWGCtrl.Instance:IsRankViewOpen() then
				GuildAnswerWGCtrl.Instance:FlushRankView()
			end
		end
	end
end

function NewChatWindow:GetChatMeasuring(delegate)
	if not delegate then
		return nil
	end

	if nil == self.measuring_cell_obj_list[delegate] or nil == self.chat_measuring_list[delegate] then
		local cell = delegate:CreateCell()
		GameObject.DontDestroyOnLoad(cell.gameObject)
		if self.node_list["layout_chatwindow_root"] then
			cell.transform:SetParent(self.node_list["layout_chatwindow_root"].transform, false)
		end
		cell.transform.localPosition = Vector3(9999, 9999, 0)
		self.chat_measuring_list[delegate] = ChatCell.New(cell.gameObject)
		self.chat_measuring_list[delegate]:SetIsCalchighCell(true)
		self.measuring_cell_obj_list[delegate] = cell.gameObject
	end

	return self.chat_measuring_list[delegate]
end

function NewChatWindow:UpdateRollTransmit()
	if self.roll_now then							-- 当前有滚动的字幕
		return
	end

	if nil == self.rich_text then
		return
	end

	local roll_transmit = ChatWGData.Instance:PopTransmit()
	if nil ~= roll_transmit then
		local content
		if roll_transmit.speaker_type == SPEAKER_TYPE.SPEAKER_TYPE_CROSS then
			content = string.format("{wordcolor;ffff00;%d%s-%s}:%s", roll_transmit.server_id, Language.Login.Fu, roll_transmit.username, roll_transmit.content)
		else
			content = string.format("{wordcolor;ffff00;%s}:%s", roll_transmit.username, roll_transmit.content)
		end
		EmojiTextUtil.ParseRichText(self.rich_text, content, 21)

		self.roll_now = true
		-- if nil == self.roll_timer then
		-- 	self.roll_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateRoll, self), 0.01)
		-- end
	end
	self.rich_text:SetActive(self.roll_now)
end

function NewChatWindow:UpdateRoll()
	local new_x = self.rich_text:getPositionX() - 3
	if new_x < self.left_bound - self.rich_text:getInnerContainerSize().width - 50 then
		new_x = self.com_title_view_w + 1

		GlobalTimerQuest:CancelQuest(self.roll_timer)
		self.roll_timer = nil

		self.roll_now = false
		self:UpdateRollTransmit()
	end

	self.rich_text:setPositionX(new_x)
end

function NewChatWindow:OnClickClose()
	self:Close()
end

-- 刷新聊天列表
-- force_flush--主要用于 msg_list 作为空表时刷新
function NewChatWindow.UpdateContentListView(list_view, msg_list, unread_num, flush_channel, curr_send_channel, force_flush)
	if nil == list_view or (IsEmptyTable(msg_list) and not force_flush) then
		return
	end
	local msg_count = #msg_list
	if unread_num > 0 and unread_num < msg_count then
		list_view:MoveFrontToLast(unread_num - (msg_count - list_view:GetCount()))
	end
	list_view:SetDataList(msg_list, nil, curr_send_channel)
end

function NewChatWindow:InptuValueChange(text)
	local max = self.node_list["chat_input"].input_field.characterLimit
	self.node_list["chat_input"].input_field.text = self.node_list["chat_input"].input_field.text .. text
	local length = StringUtil.GetCharacterCount(self.node_list["chat_input"].input_field.text)
	if length > max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.TooLong)
	end
end

function NewChatWindow:OnChatInputChange(text)
	if self.chat_old_msg_string == "" or text == "" then
		self.chat_old_msg_string = text
	else
		self:CheckDeleteStrBlock(text)
	end
end

function NewChatWindow:CheckDeleteStrBlock(text)
	local str_block_list = ChatWGData.Instance:GetChatMagHasAiTeInfo()
	if not IsEmptyTable(str_block_list) then
		--检测删除@内容块
		local start_idx, end_idx
		local new_block_list = {}
		local del_flag = false
		for i, v in ipairs(str_block_list) do
			start_idx, end_idx = string.find(text, v.block_str)
			-- print_error("FFF start_idx, end_idx", start_idx, end_idx, v.block_str)
			if not start_idx or not end_idx then
				del_flag = true
				self.chat_old_msg_string = string.gsub(self.chat_old_msg_string, v.block_str, "", 1)
			else
				table.insert(new_block_list, v)
			end
		end

		if del_flag then
			ChatWGData.Instance:SetNewAiTeBlockList(new_block_list)
			self.node_list["chat_input"].input_field.text = self.chat_old_msg_string
		else
			self.chat_old_msg_string = text
		end
	else
		self.chat_old_msg_string = text
	end
end

function NewChatWindow:OnSelectCity()
	ChatWGCtrl.Instance:OpenLocation()
end

function NewChatWindow:OnSetting()
	ChatWGCtrl.Instance:OpenSettings()
end

function NewChatWindow:OpenEmoji()
	ChatWGCtrl.Instance:OpenFace()
end

--打开气泡面板，暂时传3
function NewChatWindow:OpenFace()
	ChatWGCtrl.Instance:Open(3)
end

function NewChatWindow:ClickVoiceBtn()
	self.click_voice_btn = not self.click_voice_btn
	--self:HideMsgInput(self.click_voice_btn)
end

function NewChatWindow:HideMsgInput(is_valid)
	--self.node_list["recording_btn"]:SetActive(is_valid)
	self.node_list["msg_send_content"]:SetActive(not is_valid)
end


function NewChatWindow:SetPrivateRoleId(private_role_id)
	self.private_role_id = private_role_id
end

function NewChatWindow:HandleVoiceStart()
	--[[等级限制
	local level = PlayerData.Instance:GetRoleLevel()
		if level < COMMON_CONSTS.COMMON_CONSTS.CHAT_LEVEL_LIMIT then
		local level_str = PlayerData.GetLevelString(COMMON_CONSTS.COMMON_CONSTS.CHAT_LEVEL_LIMIT)
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, level_str))
		return
	end
	--]]
	if self.curr_send_channel == CHANNEL_TYPE.WORLD then
		if not ChatWGData.Instance:GetChannelCdIsEnd(self.curr_send_channel) then
			local time = ChatWGData.Instance:GetChannelCdEndTime(self.curr_send_channel) - Status.NowTime
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(time)))
			return
		end
	elseif self.curr_send_channel == CHANNEL_TYPE.CROSS then
		
	elseif self.curr_send_channel == CHANNEL_TYPE.ZHANDUI3V3 then

	elseif self.curr_send_channel == CHANNEL_TYPE.SCENE then

	elseif self.curr_send_channel == CHANNEL_TYPE.SYSTEM then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoChatChannel)
		return
	elseif self.curr_send_channel == CHANNEL_TYPE.GUILD then
		if RoleWGData.Instance.role_vo.guild_id <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.NotEnterGuild)
			return
		end
	elseif self.curr_send_channel == CHANNEL_TYPE.TEAM then
		if 0 == SocietyWGData.Instance:GetIsInTeam() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoTeam)
			return
		end
	elseif self.curr_send_channel == CHANNEL_TYPE.PRIVATE then
		local private_role_id = self.private_role_id
		if private_role_id == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.CorrentObj)
			return
		end
	else
		print_error("HandleChangeChannel with unknow index:", self.curr_send_channel)
		return
	end


	-- ChatWGData.Instance:SetCanSendVoice(true)
	-- self.show_listen_trigger:SetValue(true)
	AutoVoiceWGCtrl.Instance:ShowVoiceView(self.curr_send_channel)
end

function NewChatWindow:HandleVoiceStop(is_cancel_voice)
	-- self.show_listen_trigger:SetValue(false)
	if AutoVoiceWGCtrl.Instance.view:IsOpen() then
		AutoVoiceWGCtrl.Instance:SetIsCancelVoice(is_cancel_voice)
		AutoVoiceWGCtrl.Instance.view:Close()
	end
end


-- 添加表情
function NewChatWindow:SetFace(index)
	local face_id = string.format("%03d", index)
	local edit_text = self.node_list["chat_input"].input_field
	if edit_text and ChatWGData.ExamineEditText(edit_text.text, 3) then
		local max = self.chat_input.input_field.characterLimit
		if StringUtil.GetCharacterCount(edit_text.text .. "/" .. face_id) > max then
			self.chat_input.input_field.text = edit_text.text
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.TooLong)
		else
			self.chat_input.input_field.text = edit_text.text .. "/" .. face_id
		end
		ChatWGData.Instance:InsertFaceTab(face_id)
	end
end

function NewChatWindow:CleanInput()
	self.node_list["chat_input"].input_field.text = ""
	ChatWGData.Instance:ClearInput()
	ChatWGData.Instance:ReSetAiTeBlockList()
	self.guild_baoxiang_param = {}   --清空仙盟宝箱的数据信息
end

function NewChatWindow:ClearCdTimer()
	if nil ~= self.cd_timer then
		GlobalTimerQuest:CancelQuest(self.cd_timer)
		self.cd_timer = nil
	end
end

-- 聊天CD
function NewChatWindow:GetCdTime()
	local end_time = ChatWGData.Instance:GetChannelCdEndTime(self.curr_send_channel) or Status.NowTime
	if (end_time - Status.NowTime) > 0 then
		return math.ceil(end_time - Status.NowTime)
	else
		return math.floor(end_time - Status.NowTime)
	end
end

function NewChatWindow:SetCdTime()
	local cd_time = ChatWGData.Instance:GetChatCdLimint(self.curr_send_channel)
	if cd_time > 0 then
		ChatWGData.Instance:SetChannelCdEndTime(self.curr_send_channel)
		self:ShowBtnCd()
	end

	-- if self.curr_send_channel == CHANNEL_TYPE.WORLD or self.curr_send_channel == CHANNEL_TYPE.CROSS then
	-- 	ChatWGData.Instance:SetChannelCdEndTime(self.curr_send_channel)
	-- 	self:ShowBtnCd()
	-- end
end

function NewChatWindow:ShowBtnCd()
	if nil == self.node_list["left_time"] then
		return
	end

	self:ClearCdTimer()
	local cd_time = self:GetCdTime()
	-- if nil ~= self.record_btn_view then
	-- 	self.record_btn_view:setTouchEnabled(cd_time <= 0)
	-- end
	self:UpdateBtnCd()
	if cd_time >= 0 then
		self.cd_timer = GlobalTimerQuest:AddTimesTimer(BindTool.Bind1(self.UpdateBtnCd, self), 1, cd_time)
	end
end

function NewChatWindow:UpdateBtnCd()
	local cd_time = self:GetCdTime()
	if cd_time <= 0 then
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Send
		end
		self:ClearCdTimer()
	else
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Wait .. "(" .. cd_time .. ")"
		end
	end
end

function NewChatWindow:OnSendMessage()
	-- if ChatWGData.Instance:IsPingBiChannel(self.curr_send_channel) then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PingBiCanNotChat)
	-- 	return
	-- end
	if self:CurrentIsPingBi() then return end

	if ChatWGData.Instance:CheckChatDailyLivenessLimit(self.curr_send_channel, true) then
		return
	end

	--在点击发送的时候 保存仙盟宝箱的信息参数
	if not IsEmptyTable(self.guild_baoxiang_param) then
		local data = self.guild_baoxiang_param
		ChatWGData.Instance:InsertOpenLinkTab(data.openLink,data.quality,data.role_id,self.curr_send_channel)
		self.guild_baoxiang_param = {}
	end

	local text = self.node_list["chat_input"].input_field.text
	local len = string.len(text)
	local tmp_time = self:GetCdTime()
	if tmp_time > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
	end

	--判断是否是是gm命令
	if len >= 6 and string.sub(text, 1 , 6) == "/jy_gm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendGmCommand(type, command)
		end
		self:CleanInput()
		return
	elseif len >= 11 and string.sub(text, 1 , 11) == "/jy_crossgm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendCrossGmCommand(type, command)
		end
		self:CleanInput()
		return
	elseif len >= 7 and string.sub(text, 1 , 7) == "/jy_cmd" then
		local blank_begin, blank_end = string.find(text, " ")
		if blank_begin and blank_end then
			ClientCmdWGCtrl.Instance:Cmd(string.sub(text, blank_end + 1, len))
		end
		self:CleanInput()
		return
	end

	-- if len >=7 then
	-- 	local startValue,endValue = string.find(text,"<color>")
	-- 	local startValue1,endValue1 = string.find(text,"<color=")

	-- 	if (startValue and endValue) or (startValue1 and endValue1) then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind("您输入的字符不合法")
	-- 		self:CleanInput()
	-- 		return
	-- 	end
	-- end

	if CHANNEL_TYPE.PRIVATE == self.curr_send_channel then
		if 0 == self.private_role_id then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.CorrentObj)
			return
		end
	else
		local channel = ChatWGData.Instance:GetChannel(self.curr_send_channel)
		if nil == channel or channel.cd_end_time > Status.NowTime then
			return
		elseif self.curr_send_channel == CHANNEL_TYPE.SYSTEM then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoChatChannel)
			self:CleanInput()
			return
		end
	end

	if len <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end
	if len >= COMMON_CONSTS.MAX_CHAT_MSG_LEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		return
	end

	if ChatWGData.ExamineEditText(text, 0) == false then return end

	-- 聊天内容检测
	local message = ChatWGData.Instance:FormattingMsg(text, CHAT_CONTENT_TYPE.TEXT)
	if "" == message then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end
		--开启拦截换地方

	--检测玩家输入的文本中是否包含GIF特殊表情
	if not ChatWGData.Instance:CheckCanShowStrGifFace(text) then
		ChatWGCtrl.Instance:OpenChatVipLimitTipView()
		return
	end

	local is_open,str =	self:IsChatLevelLimit(self.curr_send_channel)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		self:CleanInput()
		return false
	end
	-- if not ChatWGData.Instance:CheckBottomMaskActive(self.curr_send_channel) then
	-- 	self:CleanInput()
	-- 	return false
	-- end

	-- --最小等級檢測
	-- local main_role_vo = GameVoManager.Instance:GetMainRoleVo()

	-- --最小跨服等级
	-- if CHANNEL_TYPE.GUILD == self.curr_send_channel then
	-- 	if main_role_vo.level < COMMON_CONSTS.GUILD_CHAT_LEVEL_LIMIT then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.GuildLevelDeficient, COMMON_CONSTS.GUILD_CHAT_LEVEL_LIMIT))
	-- 		self:CleanInput()
	-- 		return false
	-- 	end
	-- elseif CHANNEL_TYPE.CROSS == self.curr_send_channel then
	-- 	if main_role_vo.level < COMMON_CONSTS.CHAT_CROSS_LEVEL_LIMIT then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CrossLevelDeficient, COMMON_CONSTS.CHAT_CROSS_LEVEL_LIMIT))
	-- 		self:CleanInput()
	-- 		return false
	-- 	end
	-- elseif main_role_vo.level < COMMON_CONSTS.CHAT_LEVEL_LIMIT then	--聊天等級是否足夠
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.CHAT_LEVEL_LIMIT))
	-- 	self:CleanInput()
	-- 	return false
	-- end
	local not_report = false
	if CHANNEL_TYPE.GUILD == self.curr_send_channel then
		if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) and Scene.Instance:GetSceneType() == SceneType.GUILD_ANSWER_FB then 
			not_report = true
		end
	end
	if CHANNEL_TYPE.PRIVATE == self.curr_send_channel then--发送私聊
		ChatWGCtrl.Instance:SendPrivateChatMsg(self.private_role_id, message, CHAT_CONTENT_TYPE.TEXT, nil, false)
	else--发送公共聊天
		ChatWGCtrl.Instance:SendChannelChat(self.curr_send_channel, message, CHAT_CONTENT_TYPE.TEXT, nil, nil, not_report, nil, true)
	end

	if CHANNEL_TYPE.GUILD == self.curr_send_channel then
		if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) and
				Scene.Instance:GetSceneType() ~= SceneType.GUILD_ANSWER_FB then
			local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
			if question_info.question_state == 1 and question_info.question_end_timestamp ~= 0 then
				-- SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.IsScene)
				self:OpenGuildAnswerAlert()
				local send_guild_answer_flag = GuildAnswerWGData.Instance:GetSendGuildAnswerFlag()
				local need_send_chuangwen = GuildAnswerWGData.Instance:GetToDayNeedSendChuangWen()
				if not send_guild_answer_flag and not need_send_chuangwen then
					GuildAnswerWGData.Instance:SetSendGuildAnswerFlag(true)
					GuildAnswerWGData.Instance:SetToDayNotSendChuangWen()
				end
			end
		end
	end

	self:CleanInput()
	self:SetCdTime()

	if nil ~= self.send_callback then
		self.send_callback()
	end

	self.cur_active = false
	self:FlushHideOrActive(false)

end

function NewChatWindow:OpenGuildAnswerAlert()
	local is_need_open = GuildAnswerWGData.Instance:GetIsNeedOpenAlert()
	if not self.guild_answer_alert then
		self.guild_answer_alert = Alert.New()
		self.guild_answer_alert:SetLableString(Language.GuildAnswer.EnterAnserScene)
		self.guild_answer_alert:SetCheckBoxText(Language.Common.DontTip)
		self.guild_answer_alert:SetCheckBoxDefaultSelect(false)
		self.guild_answer_alert:SetShowCheckBox(true)
		self.guild_answer_alert:SetOkFunc(function ()
			GuildAnswerWGCtrl.Instance:SendGuildQuestionEnterReq()
			GuildAnswerWGData.Instance:SetToDayNotAlert()
		end)
		self.guild_answer_alert:SetCancelFunc(function ()
			local is_nolonger_tips = self.guild_answer_alert:GetIsNolongerTips()
			if is_nolonger_tips then
				GuildAnswerWGData.Instance:SetToDayNotAlert()
			end
		end)
	end
	if not is_need_open then
		self.guild_answer_alert:Open()					
	end
end

function NewChatWindow:SendText(text,not_report)
	if self.curr_send_channel == CHANNEL_TYPE.PRIVATE then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PrivateNoImg)
		return
	end

	if self.curr_send_channel == CHANNEL_TYPE.SYSTEM then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoChatChannel)
		return
	end

	if ChatWGData.Instance:CheckChatDailyLivenessLimit(self.curr_send_channel, true) then
		return
	end

	local channel = ChatWGData.Instance:GetChannel(self.curr_send_channel)

	if self.curr_channel ~= CHANNEL_TYPE.PRIVATE and (nil == channel or channel.cd_end_time > Status.NowTime) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.CDTime)
		return
	end

	-- 聊天内容检测
	local message = ChatWGData.Instance:FormattingMsg(text, CHAT_CONTENT_TYPE.TEXT)
	if "" == message then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end

	if self:IsPrivate() then
		ChatWGCtrl.Instance:SendPrivateChatMsg(self.private_role_id, message, CHAT_CONTENT_TYPE.TEXT, nil, false)
	else
		not_report = not_report or false
		ChatWGCtrl.Instance:SendChannelChat(self.curr_send_channel, message, CHAT_CONTENT_TYPE.TEXT, nil, nil, not_report, nil, true)
	end
	self:SetCdTime()
end

function NewChatWindow:HandleOpenItem()
	-- TipWGCtrl.Instance:ShowPropView(TipsShowProViewFrom.FROM_CHAT)
end

function NewChatWindow:OpenHorn()
	ChatWGCtrl.Instance:OpenTransmitPopView()
end

--是否显示背包定位等
function NewChatWindow:OperateActive(index)
	-- ChatWGCtrl.Instance:OpenItem()
	self.cur_active = not self.cur_active
	self:FlushHideOrActive(self.cur_active)

	if self.cur_active then
		local jump_to_index = index or 1
		if not self.node_list["more_toggle" .. jump_to_index] then
			jump_to_index = 1
		end
		self.node_list["more_toggle" .. jump_to_index].toggle.isOn =true
		self:SelectToggleCallback(jump_to_index, true)
	end
	self:SetMoreToggleVisible()
end

function NewChatWindow:FlushHideOrActive(is_active)
	local y_value = 0
	local input_y_value = 104

	if is_active then
		y_value = 0
		self.node_list.Block1:SetActive(true)
		input_y_value = 320
	else
		self.node_list.Block1:SetActive(false)
		y_value = -500
		input_y_value = 104
	end
	local Hide_animator = self.node_list["more_container"].rect:DOAnchorPosY(y_value, 0.2)
	Hide_animator:SetEase(DG.Tweening.Ease.Linear)

	local input_anim = self.node_list["InputPanel"].rect:DOAnchorPosY(input_y_value, 0.2)
	input_anim:SetEase(DG.Tweening.Ease.Linear)
end

function NewChatWindow:OperateEquip()
	ChatWGCtrl.Instance:OpenItem()
end

--点击位置
function NewChatWindow:OperateLocation()
	--过滤
	if self.curr_send_channel == CHANNEL_TYPE.SYSTEM or self.curr_send_channel == CHANNEL_TYPE.CHUAN_WEN or self.curr_send_channel == CHANNEL_TYPE.ZUDUI then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoPoint)
		return
	end
	
	if self.curr_send_channel == CHANNEL_TYPE.TEAM then
		if 0 == SocietyWGData.Instance:GetIsInTeam() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoTeam)
			return
		end
	end

	self:GetMainRolePos()
end

function NewChatWindow:ToZhaoHu()
	self:ChangeToPanel(self.node_list["zhaohu_root"])
	self:PYPLoadCallBack()
end

--点击文字表情
function NewChatWindow:OperateWordFace()
	ChatWGCtrl.Instance:OpenWordFace()
end

function NewChatWindow:SendDice()

	if self.curr_send_channel == CHANNEL_TYPE.SYSTEM then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoChatChannel)
		return
	end
	local tmp_time = self:GetCdTime()
	if tmp_time > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
	end
	local rand_num = math.random(1, 100)
	--local color_param = "7cffb2"
	--if 100 == rand_num then
	--	color_param = "ff0000"
	--elseif rand_num > 80 then
	--	color_param = "a335ee"
	--elseif rand_num > 50 then
	--	color_param = "36c4ff"
	--end
	--local text = string.format(Language.Chat.DiceWord, color_param, rand_num)
    local text = string.format(Language.Chat.DiceWord2, rand_num)

	if self:IsPrivate() then
		ChatWGCtrl.Instance:SendPrivateChatMsg(self.private_role_id, text, CHAT_CONTENT_TYPE.TEXT, nil, false, true)
	else
		ChatWGCtrl.Instance:SendChannelChat(self.curr_send_channel, text, CHAT_CONTENT_TYPE.TEXT, nil, nil, true, nil, true)
	end
	self:SetCdTime()
end

function NewChatWindow:IsPrivate()
	return CHANNEL_TYPE.PRIVATE == self.curr_send_channel
end

function NewChatWindow:OperateRedPack()
	if self.curr_send_channel ~= CHANNEL_TYPE.GUILD then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.HongBaoTips)
		return
	end

	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local red_pocket_vip_level = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].red_pocket_vip_level
	if role_vip_level < red_pocket_vip_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.VipLevelBuZu)
		return
	end

	local num = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].red_pocket_vip_distribute_times
	local distribute_times = GuildWGData.Instance:GetRedDistributeTimes()
	if distribute_times >= num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.VipRedNumBuZu)
		return
	end
end

function NewChatWindow:OperateCollect()
	if self.curr_send_channel ~= CHANNEL_TYPE.GUILD then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.GuildCallTips)
		return
	end

	if RoleWGData.Instance.role_vo.guild_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.NoGuild)
		return
	end

	GuildWGCtrl.Instance:SendGuildCallOperate(GUILD_CALL_OPERA_TYPE.GUILD_CALL_CALL)
end

function NewChatWindow:GetMainRolePos()
	local tmp_time = self:GetCdTime()
	if tmp_time > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
    end

    if Scene.Instance:GetSceneId() == 4600 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.SceneLimitPos)
        return
    end

	local main_role = Scene.Instance.main_role
	if nil ~= main_role then
		local x, y = main_role:GetLogicPos()
		local main_role_vo = main_role.vo
		local pos_msg = string.format(Language.Chat.PosFormat, Scene.Instance:GetSceneName(), x, y, main_role_vo.cur_plat_name, main_role_vo.current_server_id)
		local edit_text = self.node_list["chat_input"].input_field.text
		if ChatWGData.ExamineEditText(edit_text, 1) then
			self.node_list["chat_input"].input_field.text = edit_text .. pos_msg
			ChatWGData.Instance:InsertPointTab(self.curr_send_channel == CHANNEL_TYPE.CROSS and 1 or 0)
            self:OnSendMessage()
		end
	end
end

function NewChatWindow:SendChatText(content)
	if nil ~= self.input_view then
		self.input_view:SendText(content)
	end
end

---------------------------------------------------帮派答题处理------------------------------------------------------
function NewChatWindow:GuildAnswer()
	if not self:IsLoaded() then
		return
	end

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) then
		ChatWGCtrl.Instance:ShowAnswerContent(true)
		local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
		if self.curr_send_channel == CHANNEL_TYPE.GUILD and question_info.question_state == 0 then     -- 答题准备
			local time = question_info.question_state_change_timestamp - TimeWGCtrl.Instance:GetServerTime()
			if time > 0 then
				self.node_list["answer_index"]:SetActive(false)
				self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), question_info.question_state_change_timestamp)
				if CountDownManager.Instance:HasCountDown("question_begin_countdown") then
					return
					--CountDownManager.Instance:RemoveCountDown("question_begin_countdown")
				end
				CountDownManager.Instance:AddCountDown("question_begin_countdown", BindTool.Bind1(self.UpdateTime, self), nil, nil, time, 0.3)
				self.node_list["anwser_time_flag"]:SetActive(false)
			end

			self:SetDaTiXuanXianState(false)
		elseif self.curr_send_channel == CHANNEL_TYPE.GUILD and question_info.question_state == 1 and question_info.question_end_timestamp ~= 0 then       -- 有帮派问题
			self.node_list["answer_index"]:SetActive(true)
			self.node_list["anwser_time_flag"]:SetActive(true)

			local round_info = GuildAnswerWGData.Instance:GetRoundInfo()
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			local is_round_status
            if IsEmptyTable(round_info) then
                is_round_status = false
            else
                is_round_status = round_info.next_round_time > server_time
            end

			local star_time
			if is_round_status then
				star_time = round_info.next_round_time - server_time
                local str
                if round_info.answer_role_uid > 0 then
                    str = string.format(Language.GuildAnswer.AnswerRightTip, round_info.answer_role_name)
                else
                    str = string.format(Language.GuildAnswer.NoAnswerTip)
                end
                self.text_warner.text.text = ""
				self.node_list["guild_question_options_text"].text.text = ""
                self.worn_center_text.text.text = str

				if round_info.answer_role_uid <= 0 then
					self:SetAllDaTiXuanXianGray()
				end
			else
				star_time = question_info.question_end_timestamp - server_time
				local q_cfg = GuildAnswerWGData.Instance:GetQuestionCfgById(question_info.question_id)
                self.text_warner.text.text = q_cfg and q_cfg.content or ""
				self.node_list["guild_question_options_text"].text.text = q_cfg and q_cfg.options or ""
				self.worn_center_text.text.text = ""

				self:SetDaTiXuanXianInfo()
			end

			local max_answer_num = GuildAnswerWGData.Instance:GetMaxAnswerNum()
			self.answer_num.text.text = string.format(Language.GuildAnswer.AnswerProgress, question_info.question_index, max_answer_num)
			if star_time > 0 then
				GlobalTimerQuest:AddDelayTimer(function()
					self:UpStarTime(is_round_status, TimeWGCtrl.Instance:GetServerTime(), is_round_status and round_info.next_round_time or question_info.question_end_timestamp)
					if CountDownManager.Instance:HasCountDown("question_star_countdown") then
						CountDownManager.Instance:RemoveCountDown("question_star_countdown")
					end
					CountDownManager.Instance:AddCountDown("question_star_countdown", BindTool.Bind(self.UpStarTime, self, is_round_status),
						BindTool.Bind(self.CompelteGuildAnswer, self), nil, star_time, 0.3)
				end, 0)
			end
		else
			self.node_list["answer_index"]:SetActive(false)
			self.node_list["anwser_time_flag"]:SetActive(false)
            self:CompleteTime()
		end
	else
		if CountDownManager.Instance:HasCountDown("question_begin_countdown") then
			CountDownManager.Instance:RemoveCountDown("question_begin_countdown")
		end

		if CountDownManager.Instance:HasCountDown("question_star_countdown") then
			CountDownManager.Instance:RemoveCountDown("question_star_countdown")
		end
		
		self:RefreshChannel(CHANNEL_TYPE.GUILD) 	--活动阶数强行刷新仙盟频道
		self:CompleteTime()
	end
end

function NewChatWindow:UpStarTime(is_round_status, elapse_time, total_time)
	if self.curr_send_channel ~= CHANNEL_TYPE.GUILD then
		return
	end

	local time = math.ceil(total_time - elapse_time)
    self.node_list["dati_time_count"].text.text = string.format(Language.GuildAnswer.DescDaojishi, time)
end

function NewChatWindow:CompelteGuildAnswer()
	self:RefreshChannel(CHANNEL_TYPE.GUILD, true)
end

function NewChatWindow:UpdateTime(elapse_time, total_time)
	local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
	if question_info and question_info.question_state == 1 then
		local q_cfg = GuildAnswerWGData.Instance:GetQuestionCfgById(question_info.question_id)
		self.text_warner.text.text = q_cfg and q_cfg.content or ""
		self.node_list["guild_question_options_text"].text.text = q_cfg and q_cfg.options or ""
		self.worn_center_text.text.text = ""
		return
	end

	if total_time - elapse_time > 0 then
		local desc = ""
		local time = math.ceil(total_time - elapse_time)
		desc = string.format(Language.GuildAnswer.AnswerReady, time)
		self.text_warner.text.text = (desc)
		self.worn_center_text.text.text = ""
		self.node_list["guild_question_options_text"].text.text = ""
		self:SetDaTiXuanXianState(false)
		self.node_list["dati_time_count"].text.text = string.format(Language.GuildAnswer.DescDaojishi, time)
	end
end

function NewChatWindow:CompleteTime()
	ChatWGCtrl.Instance:ShowAnswerContent(false)
end

function NewChatWindow:PlayAnswerTrueEffect()
	EffectManager.Instance:PlayAtTransform("effects2/prefab/ui/ui_hdzq_prefab", "UI_hdzq", self.node_list["answer_effect"].transform)
end

function NewChatWindow:ChangeToPanel(node_panel)
	if self.cur_show_panel ~= nil then
		self.cur_show_panel:SetActive(false)
	end
	self.cur_show_panel = node_panel
	self.cur_show_panel:SetActive(true)
end

--显示背包
function NewChatWindow:ToBag(type)
	ChatWGData.Instance:SetIsChatBag(true)
	self:ChangeToPanel(self.node_list["bag_root"])
	self:SelectBagToggleCallback(type)
end

function NewChatWindow:SelectBagToggleCallback(type)
	if type == SOCOETY_BTN_TYPE.BAG then--背包
		self:ToNorBag()
	elseif type == SOCOETY_BTN_TYPE.BAG2 then--材料背包
		self:ToStuffBag()
	end
	self.node_list["bag_list"]:SetActive(type == SOCOETY_BTN_TYPE.BAG)
	self.node_list["stuff_bag_list"]:SetActive(type == SOCOETY_BTN_TYPE.BAG2)
end

-- 点击防骗漫画按钮
function NewChatWindow:OnClickAntiFraudCartoonBtn()
	ViewManager.Instance:Open(GuideModuleName.AntiFraudCartoonView) 			-- 打开防骗漫画
end

function NewChatWindow:ToNorBag()
	local show_data_list = ChatWGData.Instance:GetShowItemDataList()
	if #show_data_list <= 0 then
		return
	end
	if not self.bag then
		self.bag = AsyncBaseGrid.New()
		-- local count = math.ceil( #show_data_list / 5 ) * 5
		self.bag:CreateCells({col = 8, itemRender = FriendChatBagItemRender,
							assetBundle = "uis/view/chat_ui_prefab", assetName = "item_image",
							cell_count = #show_data_list , list_view = self.node_list["bag_list"]})
		self.bag:SetSelectCallBack(BindTool.Bind(self.HandleBagOnClick, self))
		self.bag:SetStartZeroIndex(false)
	end
	self.bag:SetDataList(show_data_list)
end

function NewChatWindow:ToStuffBag()
	local show_data_list = ChatWGData.Instance:GetShowStuffItemDataList()
	if #show_data_list <= 0 then
		return
	end

	if not self.stuff_bag then
		self.stuff_bag = AsyncBaseGrid.New()
		-- local count = math.ceil( #show_data_list / 5 ) * 5
		self.stuff_bag:CreateCells({col = 8, itemRender = FriendChatBagItemRender,
								assetBundle = "uis/view/chat_ui_prefab", assetName = "item_image",
								cell_count = #show_data_list , list_view = self.node_list["stuff_bag_list"]})
		self.stuff_bag:SetSelectCallBack(BindTool.Bind(self.HandleBagOnClick, self))
		self.stuff_bag:SetStartZeroIndex(false)
	end
	self.stuff_bag:SetDataList(show_data_list)
end

--发送位置
function NewChatWindow:ToPos()
    local is_spc_show = self.curr_send_channel == CHANNEL_TYPE.CROSS
    if is_spc_show and not ChatWGData.Instance:CanSharePosInCrossChannel() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CorssChannelLimitPos)
        return
    end

	if Scene.Instance:GetSceneType() == SceneType.GHOST_FB_GLOBAL then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuoGuiFuBen.LimitPosSend)
		return
	end

	if ChatWGData.Instance:CheckChatDailyLivenessLimit(self.curr_send_channel, true) then
		self:CloseMore()
		return
	end
	
    self:OperateLocation()
end

function NewChatWindow:ToQiPao()
	ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_zhuangban_bubble)
	self.cur_active = false
	self:FlushHideOrActive(false)
end

--发送骰子
function NewChatWindow:ToTouZi()
	-- if ChatWGData.Instance:IsPingBiChannel(self.curr_send_channel) then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PingBiCanNotChat)
	-- 	return
	-- end
	if self:CurrentIsPingBi() or ChatWGData.Instance:CheckChatDailyLivenessLimit(self.curr_send_channel, true) then
		self:CloseMore()
		return
	end

	self:SendDice()
	self.cur_active = false
	self:FlushHideOrActive(false)
end

--显示红包界面
function NewChatWindow:ToHongBao()
	-- if ChatWGData.Instance:IsPingBiChannel(self.curr_send_channel) then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PingBiCanNotChat)
	-- 	return
	-- end
	if self:CurrentIsPingBi() then return end
	self:OperateRedPack()
	self.cur_active = false
	self:FlushHideOrActive(false)
end

--显示召集
function NewChatWindow:ToZhaoJi()
    self:OperateCollect()
    self.cur_active = false
    self:FlushHideOrActive(false)
end

--显示表情
function NewChatWindow:ToFace(type)
	--普通表情_默认
	--特殊表情
	--文字表情
	self:ChangeToPanel(self.node_list["face_list"])

	self:SelectFaceToggleCallback(type)
	
end


function NewChatWindow:SetCurrSendChannel(channel)
	self.curr_send_channel = channel
end

function NewChatWindow:GetCurrSendChannel()
	return self.curr_send_channel
end

function NewChatWindow:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
	if self.role_head_cell == nil then
		self.role_head_cell = RoleHeadCell.New(false)
	end

	NewTeamWGCtrl.Instance:QueryTeamInfo(role_id, function(protocol)
		if self:IsOpen() and self:IsLoaded() then
			local role_info = {
				role_id = role_id,
				role_name = role_name,
				prof = prof,
				sex = sex,
				is_online = is_online,
				team_index = protocol.team_index,
				team_type = TEAM_INVITE_TYPE.CHAT,
				plat_type = plat_type,
			}

			if IS_ON_CROSSSERVER then
				local main_role = Scene.Instance:GetMainRole()
				local main_role_vo = main_role.vo
				role_info.plat_name = plat_name or main_role_vo.plat_name
				role_info.server_id = server_id or main_role_vo.origin_server_id
				self.role_head_cell:SetRoleInfo(role_info)
			else
				role_info.plat_name = plat_name
				role_info.server_id = server_id
				self.role_head_cell:SetRoleInfo(role_info)
			end
			self.role_head_cell:OpenMenu(node)
		end
	end)
end

function NewChatWindow:SetGuildListSizeDelta(bo)
	--local boo = self:CheckIsShowAnswer()
	--local x = self.node_list["chat_guild_list"].rect.sizeDelta.x
	--if not boo then
	--	self.node_list["chat_guild_list"].rect.sizeDelta = Vector2(x ,653)
	--	self.node_list["dati"]:SetActive(false)
	--	GuildAnswerWGCtrl.Instance:CloseRankView()
	--	return
	--end
	if bo == true then
		self.node_list["chat_guild_list"].rect.offsetMax = Vector2(0 , -162)
	else
		self.node_list["chat_guild_list"].rect.offsetMax = Vector2(0 ,0)
	end
end

--获取当前是周几
function NewChatWindow:GetCurWeekDay()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	return TimeUtil.FormatSecond3MYHM1(server_time)
end

function NewChatWindow:IsOneThreeFiveDay()
	local weekday =  self:GetCurWeekDay()
	return weekday == 1 or weekday == 3 or weekday == 5
end

function NewChatWindow:IsSunday()
	return self:GetCurWeekDay() == 7
end

function NewChatWindow:CheckIsShowAnswer()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
    if RoleWGData.Instance.role_vo.guild_id > 0 and is_open then
        if self:IsOneThreeFiveDay() then
            local level = RoleWGData.Instance:GetRoleInfo().level
            local limit_min_level,limit_max_level = GuildAnswerWGData.Instance:GetJoinMinMax()
            if level >= limit_min_level and level <= limit_max_level then
                return true
            end
        else
            return true
        end
    end
	return false
end

function NewChatWindow:NewMsgRefresh()
	self.new_msg_read_sign[self.show_index] = true
	self.node_list.new_msg:SetActive(true)
end

function NewChatWindow:ScrollerEndHandle()
	self.new_msg_read_sign[self.show_index] = false
	self.node_list.new_msg:SetActive(false)
end

function NewChatWindow:OnClickNewMsg()
	local list_view = self.list_view_list[tostring(self.curr_send_channel)]
	if list_view then
		list_view:FlushCompreView()
		self.new_msg_read_sign[self.show_index] = false
		self.node_list.new_msg:SetActive(false)
	end
end

-- 跳转好友
function NewChatWindow:OnClickBtnFriend()
	SocietyWGCtrl.Instance:OpenFriendChatView()
end



function NewChatWindow:OnClickAiTeMeBtn()
	-- print_error("FFFF===== 点击跳转当前频道@我的第一处")
	local aite_info = ChatWGData.Instance:GetAiteInfoByChannel(self.curr_send_channel)
	local channel = ChatWGData.Instance:GetChannel(self.curr_send_channel)
	if not IsEmptyTable(aite_info) and not IsEmptyTable(channel) and not IsEmptyTable(channel.msg_list) then
		local list_view = self.list_view_list[tostring(self.curr_send_channel)]
		local first_aite_info = aite_info[1]
		local check_info, check_time_stamp, msg_index
		local total_height = 0
		for i, v in ipairs(channel.msg_list) do
			check_info = ChatWGData.Instance:GetSendAiteInfo(v)
			check_time_stamp = ChatWGData.Instance:GetAiteMsgTime(v.content)			
			if check_time_stamp == first_aite_info.timestamp and check_info.channel_type == first_aite_info.channel_type 
				and check_info.server_id == first_aite_info.server_id and check_info.plat_type == first_aite_info.plat_type
				and check_info.role_uid == first_aite_info.uid then
				msg_index = i
				break
			end
			total_height = total_height + (v.chat_cell_height or 0)
		end
		-- print_error("FFF==== msg_index and list_view", msg_index, list_view ~= nil)
		if msg_index and list_view then--存在第一条@我的消息
			local list_view_comp = list_view:GetView()
			if list_view_comp and list_view_comp.scroll_rect and list_view_comp.scroll_rect.content then
				--滑到@消息处
				-- print_error("FFF==== --滑到@消息处 total_height", total_height)
				RectTransform.SetAnchoredPositionXY(list_view_comp.scroll_rect.content.transform, 0, total_height)
				if list_view:GetScrollRectVerNorPos() <= 0 then
					self.new_msg_read_sign[self.show_index] = false
					self.node_list.new_msg:SetActive(false)
				end
			end
		end
	end
end

--刷新艾特互动操作按钮:延时检测一下,否则会出现按钮闪一下被刷掉了
function NewChatWindow:FlushAiTeBtnState()
	if self.delay_check_aite_btn_show ~= nil then
		--print_error("FFFFF   ===== 存在延时")
		self:HideAiTeBtn()
		return
	end
	local is_show = false
	local aite_info = ChatWGData.Instance:GetAiteInfoByChannel(self.curr_send_channel)
	if not IsEmptyTable(aite_info) then
		local count = #aite_info
		self.node_list["text_aite_me"].text.text = string.format(Language.Chat.SomeoneAiTeMe, count)
		is_show = true
	end
	self.node_list["btn_aite_me"]:SetActive(is_show)
	self.node_list.new_msg.rect.anchoredPosition = is_show and Vector2(-50, -70) or Vector2(-50, -120)
end

function NewChatWindow:HideAiTeBtn()
	if self.node_list["btn_aite_me"] then
		self.node_list["btn_aite_me"]:SetActive(false)
		self.node_list.new_msg.rect.anchoredPosition = Vector2(-50, -120)
	end
end

function NewChatWindow:FlushCheckAiteTimerState()
	local channel_aite_red = ChatWGData.Instance:IsShowChatAiteRemind(self.curr_send_channel)
	if channel_aite_red == 1 then
		self:SetCheckAiteTime()
	else
		self:ClearCheckAiteTimer()
	end
end

function NewChatWindow:SetCheckAiteTime()
	if not self.check_aite_timer then
		self.check_aite_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.AiteTimeCallBack,self), 0.3)
		self:SetAiteBtnDelay()
	end
end

function NewChatWindow:AiteTimeCallBack()
	local list_view = self.list_view_list[tostring(self.curr_send_channel)]
	if list_view then
		local cur_show_index_list = list_view:GetAllShowItemIndexList()
		self:SendIsReadIndexList(cur_show_index_list, list_view)
		self:SetOldReadIndexList(cur_show_index_list)
	end
	self:FlushAiTeBtnState()
end

function NewChatWindow:ClearCheckAiteTimer()
	-- print_error("FFF====== 关闭@计时器检测")
	self:HideAiTeBtn()
	if self.check_aite_timer then
		GlobalTimerQuest:CancelQuest(self.check_aite_timer)
		self.check_aite_timer = nil
	end
	self:ClearAiteBtnDelay()
end

--设置有人@我按钮延时检测
function NewChatWindow:SetAiteBtnDelay()
	if not self.delay_check_aite_btn_show then
		self.delay_check_aite_btn_show = GlobalTimerQuest:AddDelayTimer(function ()
        	self:ClearAiteBtnDelay()
		end, 0.3)
	end
end

function NewChatWindow:ClearAiteBtnDelay()
	if self.delay_check_aite_btn_show then
		GlobalTimerQuest:CancelQuest(self.delay_check_aite_btn_show)
		self.delay_check_aite_btn_show = nil
	end
end

function NewChatWindow:ClearReadIndexList()
	self.old_read_msg_index_list = {}
	self.new_read_msg_index_list = {}
end

function NewChatWindow:SetNewReadIndexList(start_idx, end_idx)
	self.new_read_msg_index_list = {}
	for i = start_idx, end_idx do
		table.insert(self.new_read_msg_index_list, i)
	end
end

function NewChatWindow:SetOldReadIndexList(new_read_list)
	self.old_read_msg_index_list = {}
	for i, v in ipairs(new_read_list) do
		table.insert(self.old_read_msg_index_list, v)
	end
end

function NewChatWindow:SendIsReadIndexList(new_read_list, list_view)
	for old_i, old_v in ipairs(self.old_read_msg_index_list) do
		for new_i, new_v in pairs(new_read_list) do
			if old_v == new_v then
				if list_view and list_view.data_list and list_view.data_list[old_v] then
					--发送消息已读给服务器
					ChatWGData.Instance:CheckSendIsReadAiteMsg(list_view.data_list[old_v])
				end
				break
			end
		end
	end
end

-------------------------------------答题选项---------------------------------------
function NewChatWindow:ReSetDaTiXuanXianState()
	for i = 1, 4 do
		XUI.SetButtonEnabled(self.node_list["btn_dati_ans" .. i], true)
		self.node_list["btn_dati_ans" .. i].button.enabled = true
		self.node_list["flag_dati_true" .. i]:CustomSetActive(false)
		self.node_list["flag_dati_false" .. i]:CustomSetActive(false)
		self.node_list["select_dati" .. i]:CustomSetActive(false)
	end

	GuildAnswerWGData.Instance:SetGuildLastAnswerTime(0)
end

function NewChatWindow:SetDaTiXuanXianState(state)
	if self.node_list.dati_xuanxiang then
		self.node_list.dati_xuanxiang:CustomSetActive(state)
	end
end

function NewChatWindow:SetAllDaTiXuanXianGray()
	for i = 1, 4 do
		XUI.SetButtonEnabled(self.node_list["btn_dati_ans" .. i], false)
	end
end

function NewChatWindow:SetDaTiXuanXianInfo()
	local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
	local q_cfg = GuildAnswerWGData.Instance:GetQuestionCfgById(question_info.question_id)

	if not IsEmptyTable(q_cfg) then
		for i = 1, 4 do
			self.node_list["desc_dati_ans" .. i].text.text = q_cfg["option" .. i]
		end

		if nil == self.question_id_cache or self.question_id_cache ~= question_info.question_id then
			self.question_id_cache = question_info.question_id
			self:ReSetDaTiXuanXianState()
		end

		self:SetDaTiXuanXianState(true)
	else
		self:SetDaTiXuanXianState(false)
	end
end

function NewChatWindow:OnClickDatiOptionBtn(index)
	local can_answer, time = GuildAnswerWGData.Instance:IsCanGuildAnswer()

	if not can_answer then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.GuildAnswer.AnswerTimeLimit, math.ceil(time)))
		return
	end

	GuildAnswerWGData.Instance:SetGuildLastAnswerTime(TimeWGCtrl.Instance:GetServerTime())

	local answer_str = DATI_OPTION_STR[index]
	self.node_list["chat_input"].input_field.text = DATI_OPTION_STR[index]
	self:OnSendMessage()

	-- 选中，判定结果
	local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
	local q_cfg = GuildAnswerWGData.Instance:GetQuestionCfgById(question_info.question_id)

	local is_true = q_cfg.answer == answer_str

	if is_true then
		self.node_list["btn_dati_ans" .. index].button.enabled = false

		for i = 1, 4 do
			if i ~= index then
				XUI.SetButtonEnabled(self.node_list["btn_dati_ans" .. i], false)
			end
		end
	else
		XUI.SetButtonEnabled(self.node_list["btn_dati_ans" .. index], false)
	end

	self.node_list["flag_dati_true" .. index]:CustomSetActive(is_true)
	self.node_list["flag_dati_false" .. index]:CustomSetActive(not is_true)

	for i = 1, 4 do
		self.node_list["select_dati" .. i]:CustomSetActive(i == index)
	end
end