﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[ExecuteInEditMode]
public class YYCelestialObject : MonoBehaviour {

    /// <summary>Shafts multiplier. Brightens up cloud or fog shafts. Useful for moons.</summary>
    [Range(0.0f, 100.0f)]
    [Tooltip("Shafts multiplier. Brightens up cloud or fog shafts. Useful for moons.")]
    public float ShaftMultiplier = 1.0f;

    /// <summary>Horizon multiplier (x = multiplier, y = max intensity, z = power reducer). Increases light intensity in certain effects like clouds as object reaches horizon. Great for brighter sunsets.</summary>
    [Tooltip("Horizon multiplier (x = multiplier, y = max intensity, z = power reducer). Increases light intensity in certain effects like clouds as object reaches horizon. Great for brighter sunsets.")]
    public Vector3 HorizonMultiplier;


    public Renderer Renderer { get; private set; }
    public MeshFilter MeshFilter { get; private set; }

    public Light Light { get; private set; }

    public Collider Collider { get; private set; }


    public bool IsSun;
    public WeatherOrbitType OrbitType = WeatherOrbitType.FromEarth;
    public float RotateYDegrees;

    public bool OrbitTypeIsPerspective { get { return OrbitType == WeatherOrbitType.FromEarth || OrbitType == WeatherOrbitType.CustomPerspective; } }



    ///  ////
    public Color TintColor = Color.white;
    public float TintIntensity = 1.0f;
    public float Scale = 0.02f;


    public float LightPower = 8.0f;
    public float LightMultiplier = 1.0f;


    public Vector3 ViewportPosition
    {
        get; internal set;
    }


    private void Awake()
    {
        UpdateInternal();
    }


    private void UpdateInternal()
    {
        if (YYLightManager.Instance != null)
        {
            if (IsSun)
            {
                if (!YYLightManager.Instance.Suns.Contains(this))
                {
                    YYLightManager.Instance.Suns.Add(this);
                }
            }
        }

        Light = (Light == null ? GetComponent<Light>() : Light);
        Renderer = (Renderer == null ? GetComponent<Renderer>() : Renderer);
        MeshFilter = (MeshFilter == null ? GetComponent<MeshFilter>() : MeshFilter);
        Collider = (Collider == null ? GetComponent<Collider>() : Collider);

        if (Renderer != null)
        {
            Renderer.enabled = false;
        }


    }

    public Color GetGradientColor(Gradient gradient)
    {
        if (gradient == null)
        {
            return YYLightManager.EvaluateGradient(gradient, 1.0f);
        }
        float sunGradientLookup = GetGradientLookup();
        return YYLightManager.EvaluateGradient(gradient, sunGradientLookup);
    }

    public float GetGradientLookup()
    {
        if (!LightIsActive)
        {
            return 1.0f;
        }

        float sunGradientLookup;
        CameraMode mode = YYWeather.ResolveCameraMode();
        if (mode == CameraMode.OrthographicXY && !OrbitTypeIsPerspective)
        {
            sunGradientLookup = transform.forward.z;
        }
        else if (mode == CameraMode.OrthographicXZ && !OrbitTypeIsPerspective)
        {
            sunGradientLookup = transform.forward.x;
        }
        else
        {
            // TODO: Support full spherical world
            sunGradientLookup = -transform.forward.y;
        }
        sunGradientLookup = ((sunGradientLookup + 1.0f) * 0.5f);
        return sunGradientLookup;
    }

    public bool IsActive
    {
        get { return gameObject.activeInHierarchy; }
    }
    public bool LightIsActive
    {
        get { return Light.enabled && IsActive; }
    }

}
