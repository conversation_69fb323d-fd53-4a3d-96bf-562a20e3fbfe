TargetBuffTip = TargetBuffTip or BaseClass(SafeBaseView)

function TargetBuffTip:__init()
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.is_maskbg_button_click = true
	self.text_height = 0
	self:SetMaskBg(true, true)
	if TargetBuffTip.Instance then
		ErrorLog("[TargetBuffTip] Attemp to create a singleton twice !")
	end
	TargetBuffTip.Instance = self
	self:LoadConfig()
end

function TargetBuffTip:__delete()
	TargetBuffTip.Instance = nil
end

function TargetBuffTip:LoadConfig()
	self:AddViewResource(0, "uis/view/main_ui_prefab", "target_buff_tip")
	self.is_modal = false
end

function TargetBuffTip:ReleaseCallBack()
	self.open_node = nil
end

function TargetBuffTip:LoadCallBack()

end

function TargetBuffTip:SetDataOpen(node, data)
	self.data = data
	self.open_node = node
	self:Open()
	self:Flush()
end

function TargetBuffTip:OnFlush()
	if nil == self.open_node or nil == self.data then
		return
	end
	local desc = FightWGData.Instance:GetEffectDesc(self.data) or ""
	local pos = self.open_node.transform.position
	pos.x = pos.x --
	pos.y = pos.y - 4
	self.node_list.Container.transform.position = pos
	self.node_list.rule_text.text.text = desc
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.Container.rect)

	if self.data.info.residue_time and self.data.info.residue_time > 0 then
		if self.node_list and self.node_list["buff_time"] ~= nil then
			self.node_list["buff_time"]:CustomSetActive(true)
		end
		if self.data.info.residue_time > 86400 then		--一天
			self.node_list["buff_time"].text.text = TimeUtil.FormatSecondDHM(math.ceil(self.data.info.residue_time))
		else
			self.node_list["buff_time"].text.text = math.ceil(self.data.info.residue_time) .. Language.Common.TimeList.s--TimeUtil.FormatSecond(math.ceil(self.data.info.residue_time))
		end
	else
		if self.node_list and self.node_list["buff_time"] ~= nil then
			if self.data.info.client_effect_type == EFFECT_CLIENT_TYPE.ECT_TIANSHEN_FB_BUFF then
				self.node_list["buff_time"]:CustomSetActive(false)
			else
				self.node_list["buff_time"]:CustomSetActive(true)
			end
		end

		local cd_time = math.max(0, self.data.info.cd_time - Status.NowTime)
		if cd_time > 0 then
			if not CountDownManager.Instance:HasCountDown("target_buff_countdown") then
				self:UpdataNextTime(0, cd_time)
				CountDownManager.Instance:AddCountDown("target_buff_countdown", BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind(self.CompleteNextTime, self), nil, cd_time, 0)
			end
		else
			self.node_list["buff_time"].text.text = ""
			CountDownManager.Instance:RemoveCountDown("target_buff_countdown")
		end
	end
end

function TargetBuffTip:UpdataNextTime(elapse_time, total_time)
	local on_time = total_time - elapse_time
	if on_time > 86400 then		--一天
		on_time = TimeUtil.FormatSecondDHM(math.ceil(on_time))
	else
		on_time = math.ceil(on_time) .. Language.Common.TimeList.s -- TimeUtil.FormatSecond(math.ceil(on_time))
	end
	if self.node_list and self.node_list["buff_time"] ~= nil then
		self.node_list["buff_time"].text.text = on_time
	end
end

function TargetBuffTip:CompleteNextTime()
	if self.node_list and self.node_list["buff_time"] ~= nil then
		self.node_list["buff_time"].text.text = ""
	end
	CountDownManager.Instance:RemoveCountDown("target_buff_countdown")
	self:Close()
end