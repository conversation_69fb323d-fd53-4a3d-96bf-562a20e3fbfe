SocietyBaseRender = SocietyBaseRender or BaseClass(BaseRender)
function SocietyBaseRender:__init()

end

function SocietyBaseRender:__delete()
	if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end
end

function SocietyBaseRender:LoadCallBack()
--print_error("self.role_avatar = RoleHeadCell.New(false)+++++++++++++++++++++++++++++img_cell")
	self.role_avatar = RoleHeadCell.New(false)

	--self.role_avatar:SetInstance(self.node_list["ph_avatar"])
	--self.role_avatar:SetInstance(self.node_list["img_cell"])
end


----------------------------------------------------------------------------------------------------
-- 组队邀请回复面板
----------------------------------------------------------------------------------------------------
SocietyTeamInviteReqRender = SocietyTeamInviteReqRender or BaseClass(SocietyBaseRender)
function SocietyTeamInviteReqRender:__init()
end

function SocietyTeamInviteReqRender:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
end

function SocietyTeamInviteReqRender:LoadCallBack()
	self.node_list["btn_refuse"].button:AddClickListener(function()
		SocietyWGCtrl.Instance:RefuseInviteTeam(self.data.req_role_id)
		if SocietyWGCtrl.Instance:GetTodayCheckActive() then
			NewTeamWGCtrl.Instance:SendNoLongerOperateReq(LOGIN_NO_LONGER_TYPE.LOGIN_NO_LONGER_TYPE_INVITE_ME_TEAM, self.data.req_role_id, 0, NO_LONGER_RECORD_TYPE.LOGIN)
		end
	end)

	self.node_list["btn_agree"].button:AddClickListener(function()
		local role_id = self.data.req_role_id
		SocietyWGCtrl.Instance:SendInviteUserTransmitRet(role_id, 0)
		GlobalTimerQuest:AddDelayTimer(function ()
			SocietyWGCtrl.Instance:DeleteReq(role_id)
		end, 0)
	end)
	self.node_list["lbl_name"].button:AddClickListener(function()
		local role_id = self.data.req_role_id
		BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
            SocietyWGCtrl.Instance:CreateRoleHeadCell(param_protocol.role_id, self.data.req_role_name, param_protocol.prof, param_protocol.sex,
            param_protocol.is_online, self.node_list.lbl_name, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
		end)
	end)
end

function SocietyTeamInviteReqRender:OnFlush()
	if self.role_avatar then
		local role_info = {
			role_id = self.data.req_role_id,
			role_name = self.data.req_role_name,
			prof = self.data.req_role_prof,
			sex = self.data.req_role_sex,
			is_online = true,
		}
		if IS_ON_CROSSSERVER then
			local main_role = Scene.Instance:GetMainRole()
	        local main_role_vo = main_role.vo
			role_info.plat_name = main_role_vo.plat_name
			role_info.server_id = main_role_vo.merge_server_id
			self.role_avatar:SetRoleInfo(role_info)
		else
			self.role_avatar:SetRoleInfo(role_info)
		end
	end

	local level_str = string.format(Language.NewTeam.InvaterInfo, self.data.req_role_name, self.data.inviter_level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_name"].emoji_text, level_str, 20, COLOR3B.DEFAULT)
	-- self.node_list["lbl_team_name"].text.text = string.format(Language.NewTeam.PTTeamName1, self.data.req_role_name)
    local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(self.data.team_type, self.data.teamfb_mode)
    local team_type_name = (not IsEmptyTable(goal_info) and goal_info.team_type_name and goal_info.team_type_name ~= "")
							and goal_info.team_type_name or Language.NewTeam.NoTeamTypeTarget
	local str = string.format(Language.NewTeam.BeApplyViewOtherInfoStr, team_type_name, self.data.team_min_level, self.data.team_max_level)
	EmojiTextUtil.ParseRichText(self.node_list["other_info_richtext"].emoji_text, str, 20, COLOR3B.DEFAULT)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["hor_layout"].rect)

	local relation_flag = bit:d2b_two(self.data.inviter_relation_flag or 0)
	local index = -1
	for k,v in pairs(relation_flag) do
		if v == 1 then
			index = k
			break
		end
	end
	local relation_str = Language.NewTeam.TeamMateRelation[index + 2]
	if index + 2 == 1 then
		relation_str = ToColorStr(relation_str, COLOR3B.DEFAULT)
	end
	self.node_list["lbl_relation"].text.text = relation_str

	--local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.req_role_level)
	--if is_vis then
	--	self.node_list["label_level"].text.text = "         "..role_level
	--else
	--	self.node_list["label_level"].text.text = "Lv."..role_level--RoleWGData.GetLevelString(self.data.level)
	--end
	--self.node_list.img_fire:SetActive(is_vis)
	--self.node_list["label_prof"].text.text = tostring(Language.Common.ProfName[self.data.req_role_prof])

end

function SocietyTeamInviteReqRender:OnSelectChange(is_select)
	--if true == is_select then
	--	self.node_list.img_choose:SetActive(true)	--高亮
	--else
	--	self.node_list.img_choose:SetActive(false)	--非高亮
	--end
end

----------------------------------------------------------------------------------------------------
--仇人
----------------------------------------------------------------------------------------------------
SocietyEnemyRender = SocietyEnemyRender or BaseClass(SocietyBaseRender)
function SocietyEnemyRender:__init()
	-- self.node_list["label_killcount"].text.text = 666
	--print_error("self.data")

	self.role_avatar = RoleHeadCell.New(false)
	self.head_cell = BaseHeadCell.New(self.node_list.img_cell)
	XUI.AddClickEventListener(self.node_list["img_cell"], BindTool.Bind1(self.RoleInfoList, self))
end

function SocietyEnemyRender:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function SocietyEnemyRender:RoleInfoList()
	self.role_avatar:OpenMenu(nil, SocietyWGCtrl.Instance:GetCacularPos(self.node_list["default_head_icon"]), nil, MASK_BG_ALPHA_TYPE.Normal)--Vector2(-141, -82))
end

function SocietyEnemyRender:SetKuaFuState()
	-- body
end

function SocietyEnemyRender:OnFlush()

	if self.data == nil then return end

	self.node_list["lbl_camp_username"].text.text = self.data.gamename
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.img9_fire:SetActive(is_vis)
	if is_vis then
		self.node_list["label_level"].text.text = role_level
		-- TODO 需要改为新版处理方式
		--self.node_list["label_level"].text.alignment = UnityEngine.TextAnchor.MiddleLeft
	else
		self.node_list["label_level"].text.text = "LV."..role_level--RoleWGData.GetLevelString(self.data.level)
		-- TODO 需要改为新版处理方式
		--self.node_list["label_level"].text.alignment = UnityEngine.TextAnchor.MiddleCenter
	end

	-- XUI.SetGraphicGrey(self.node_list["img_cell"], 1 ~= self.data.is_online)
	local is_online_flag
    if self.data.is_online == 0 then-- 0离线
        is_online_flag = false
    else
        is_online_flag = true
    end

	--XUI.SetGraphicGrey(self.node_list["lbl_camp_username"], not is_online_flag)
	XUI.SetGraphicGrey(self.node_list["img_vip"], not is_online_flag)
	-- XUI.SetGraphicGrey(self.node_list["label_level"], not is_online_flag)
	-- XUI.SetGraphicGrey(self.node_list["img9_fire"], not is_online_flag)
	--XUI.SetGraphicGrey(self.node_list["img_rank_ten"], not is_online_flag)

    if self.data.vip_level then
    	local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
        self.node_list["img_vip"].gameObject:SetActive(self.data.vip_level > 0)
		self.node_list["img_vip_bg"]:CustomSetActive(false)
        self.node_list.img_vip.text.text = is_hide_vip and "V" or "V".. self.data.vip_level
	else
		self.node_list["img_vip"]:CustomSetActive(false)
		self.node_list["img_vip_bg"]:CustomSetActive(false)
	end
    
	local bundle, asset = ResPath.GetProfIcon(self.data.sex, self.data.prof%10, true)
    self.node_list.prof_icon.image:LoadSprite(bundle, asset, function()        
    	-- self.node_list.prof_icon.image:SetNativeSize()      
    end)
    local fashion_photoframe = self.data.fashion_photoframe or 0
	local data = {role_id = self.data.user_id, prof = self.data.prof, sex = self.data.sex, fashion_photoframe = fashion_photoframe}
	self.head_cell:SetData(data)
	self.head_cell:SetGray(not is_online_flag)
	-- self.node_list["img_rank_ten"]:SetActive(SocietyWGData.Instance:ChackZhanliRank(self.data.user_id) <= 10)
	local server_id = self.data.server_id
	local plat_type = self.data.uuid.temp_high
	local main_role_plat_type = RoleWGData.Instance:GetPlatType()
	local main_role_server = RoleWGData.Instance:GetOriginUid()

	-- self.role_avatar:AddItems(Language.Menu.DeleteEnemy)
	local role_info = {
		role_id = self.data.user_id,
		role_name = self.data.gamename,
		prof = self.data.prof,
		sex = self.data.sex,
		is_online = 0 ~= self.data.is_online,
		plat_type = plat_type,
		server_id = server_id,
		role_level = self.data.level,
	}
	if server_id ~= main_role_server or plat_type ~= main_role_server then
		self.role_avatar:SetRoleInfo(role_info)
	else
		self.role_avatar:SetRoleInfo(role_info)
	end

	-- 亲密度
	self.node_list["layout_qinmidu"]:SetActive(false)
end

function SocietyEnemyRender:OnSelectChange(is_select)
	--无用但暂时保存，因为我不确定
	-- local vas = self.node_list.img_choose:GetActive()
	-- if vas and is_select then
	-- 	self.role_avatar:OpenMenu()
	-- end
	self.node_list.img_choose:SetActive(is_select)

end

----------------------------------------------------------------------------------------------------
--单独好友列表
----------------------------------------------------------------------------------------------------
SocietyFriendListRender = SocietyFriendListRender or BaseClass(SocietyBaseRender)
function SocietyFriendListRender:__init()
end

function SocietyFriendListRender:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
end

function SocietyFriendListRender:OnFlush()

	if self.data == nil then return end

	self.node_list["lbl_name"].text.text = ToColorStr(self.data.gamename, SEX_COLOR[self.data.sex][3])
	self.node_list["label_level"].text.text = RoleWGData.GetLevelString(self.data.level)
	self.node_list["label_prof"].text.text = ToColorStr(Language.Common.ProfName[self.data.sex][self.data.prof], PROF_COLOR3B[self.data.prof])
	if self.node_list["default_head_icon"] and self.node_list["custom_head_icon"] then
		XUI.UpdateRoleHead(self.node_list["default_head_icon"], self.node_list["custom_head_icon"],self.data.user_id,self.data.sex,self.data.prof,self.data.is_online ~= 1,nil,true)
	end


end

function SocietyFriendListRender:OnSelectChange(is_select)
	if is_select then
		self.node_list.img_choose:SetActive(true)
	else
		self.node_list.img_choose:SetActive(false)
	end
end

----------------------------------------------------------------------------------------------------
--好友列表
----------------------------------------------------------------------------------------------------
SocietyFriendRender = SocietyFriendRender or BaseClass(SocietyBaseRender)

function SocietyFriendRender:__init()
	self.is_requst_btn = true
	self.role_avatar = RoleHeadCell.New(false)
	self.head_cell = BaseHeadCell.New(self.node_list.img_cell)
end

function SocietyFriendRender:__delete()
	if CountDownManager.Instance:HasCountDown("btn_timer" .. self:GetIndex()) then
		CountDownManager.Instance:RemoveCountDown("btn_timer" .. self:GetIndex())
	end
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end

	if nil ~= self.hint_alert then
		self.hint_alert:DeleteMe()
		self.hint_alert = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function SocietyFriendRender:LoadCallBack()
	-- XUI.AddClickEventListener(self.node_list["img_heart"], BindTool.Bind1(self.OnClickOpenQinMi, self))
	XUI.AddClickEventListener(self.node_list["img_cell"], BindTool.Bind1(self.RoleInfoList, self))
	-- XUI.AddClickEventListener(self.node_list["btn_humo"], BindTool.Bind1(self.OnClickHuoMo, self))
	-- XUI.AddClickEventListener(self.node_list["btn_lq"], BindTool.Bind1(self.OnClickHuoMoLingQu, self))

end
function SocietyFriendRender:RoleInfoList()
	self.role_avatar:OpenMenu(nil, SocietyWGCtrl.Instance:GetCacularPos(self.node_list["default_head_icon"]),nil,MASK_BG_ALPHA_TYPE.Normal)--Vector2(-141, -82))
end



function SocietyFriendRender:OnSelectChange(is_select,is_show_menu)
	if next(self.node_list) == nil then return end
	--暂时保留此段逻辑
	-- local vas = self.node_list.img_choose:GetActive()
	-- if vas and is_select and not is_show_menu then
	-- 	self.role_avatar:OpenMenu(nil,Vector2(-141, -82))
	-- end
	if is_select then
		self.node_list.img_choose:SetActive(true)
	else
		self.node_list.img_choose:SetActive(false)
	end
end

function SocietyFriendRender:FlushUnChat()
	self.node_list.label_num_chat_bg:SetActive(false)
	-- self.node_list.label_guaji:SetActive(self.data.is_guaji == 2)
end
function SocietyFriendRender:SetKuaFuState()
	-- if self.data.is_kuafu == 3 then
	-- 	self.node_list.label_kuafu:SetActive(true)
	-- else
	-- 	self.node_list.label_kuafu:SetActive(false)
	-- end
end

function SocietyFriendRender:OnFlush()
	if self.data == nil then return end
	self.married_img = self.node_list["img9_married"]

    local is_state = false
    if self.data.is_online == 0 then-- 0离线
        is_state = true
    else
        is_state = false
    end

    --print_error("self.data =", self.data.is_lyl, self.data.gamename)
	
	--XUI.SetGraphicGrey(self.node_list["lbl_camp_username"], is_state)
	-- XUI.SetGraphicGrey(self.node_list["img9_fire"], is_state)
	-- XUI.SetGraphicGrey(self.node_list["label_level"], is_state)
	XUI.SetGraphicGrey(self.node_list["img_vip"], is_state)

	--XUI.SetGraphicGrey(self.node_list["img_rank_ten"],is_state)
	self.head_cell:SetGray(is_state)

	local un_chat_num = ChatWGData.Instance:GetPrivateUnreadMsgNum( self.data.user_id )
	local un_lyl_chat_num = SocietyWGData.Instance:GetLYLUnreadMsgNum( self.data.user_id )
	-- local firend_last_message = ""
	-- if self.data and self.data.is_recently_friend then
	-- 	firend_last_message = PlayerPrefsUtil.GetString("friend_last_message"..self.data.user_id)
	-- end

	if un_chat_num > 0 or un_lyl_chat_num > 0 then
		self.node_list.label_num_chat_bg:SetActive(true)
		UITween.MoveLoop(self.node_list["label_num_chat_bg"], Vector2(143, 30), Vector2(143, 34), 1)
	else
		self.node_list.label_num_chat_bg:SetActive(false)
	end

	self.role_avatar:AddItems(Language.Menu.DeleteFriend)
	
	local is_online = false
	if self.data.is_online == 1 and self.data.is_guaji == 0 or self.data.is_lyl then
		is_online = true
	end


	--if IS_ON_CROSSSERVER then
	--	local main_role = Scene.Instance:GetMainRole()
    --    local main_role_vo = main_role.vo
    --    self.role_avatar:SetRoleInfo(self.data.user_id, self.data.gamename, self.data.prof, self.data.sex, is_online,self.data.team_index, TEAM_INVITE_TYPE.FRIEND,nil,main_role_vo.plat_name,main_role_vo.merge_server_id)
	--else
		
		local bundle, asset = ResPath.GetProfIcon(self.data.sex, self.data.prof%10, true)
	    self.node_list.prof_icon.image:LoadSprite(bundle, asset, function()        
	    	-- self.node_list.prof_icon.image:SetNativeSize()      
	    end)
		local plat_type = self.data.uuid and self.data.uuid.temp_low or -1
		local server_id = self.data.uuid and self.data.uuid.temp_high or -1
		local role_info = {
			role_id = self.data.user_id,
			role_name = self.data.gamename,
			prof = self.data.prof,
			sex = self.data.sex,
			is_online = is_online,
			team_index = self.data.team_index,
			team_type = TEAM_INVITE_TYPE.FRIEND,
			plat_type = plat_type,
			server_id = server_id,
			role_level = self.data.level,
		}
		 --print_error("SocietyFriendRender:OnFlush", self.data.user_id, self.data.gamename, self.data.prof, self.data.sex, is_online,self.data.team_index, plat_type,server_id)
		self.role_avatar:SetRoleInfo(role_info)
	--end

	self.node_list["lbl_camp_username"].text.text = self.data.gamename--ToColorStr(self.data.gamename, SEX_COLOR[self.data.sex][3])
	local fashion_photoframe = self.data.fashion_photoframe or 0
	local data = {role_id = self.data.user_id, prof = self.data.prof, sex = self.data.sex, fashion_photoframe = fashion_photoframe}
	self.head_cell:SetData(data)

	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.img9_fire:SetActive(is_vis)-- and self.data.is_kuafu ~= 3)

	if is_vis then
		self.node_list["label_level"].text.text = role_level
		-- TODO 需要改为新版处理方式
		--self.node_list["label_level"].text.alignment = UnityEngine.TextAnchor.MiddleLeft
	else
		self.node_list["label_level"].text.text = "LV."..role_level--RoleWGData.GetLevelString(self.data.level)
		-- TODO 需要改为新版处理方式
		--self.node_list["label_level"].text.alignment = UnityEngine.TextAnchor.MiddleCenter
	end

	if self.data.vip_level then
    	local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
        self.node_list["img_vip"].gameObject:SetActive(self.data.vip_level > 0)
		self.node_list["img_vip_bg"]:CustomSetActive(false)
        self.node_list.img_vip.text.text = is_hide_vip and "V" or "V".. self.data.vip_level
	else
		self.node_list["img_vip"]:CustomSetActive(false)
		self.node_list["img_vip_bg"]:CustomSetActive(false)
	end

	-- 亲密度
	if self.data.intimacy then
		local need_intimacy = 1
		local intimacy_level = 0
		local intimacy_cfg = SocietyWGData.Instance:GetintimacyBuffCfg()
		for k,v in pairs(intimacy_cfg) do
		 	if self.data.intimacy >= v.intimacy then
		 		intimacy_level = v.buff_level
		 	else
		 		break
		 	end
		end

		for i = 1, 5 do
			if intimacy_level > 5 and i <= (intimacy_level - 5) then
				local asset, bundle = ResPath.GetF2Society("a3_sj_xx2")
				self.node_list["img_heart"..i].image:LoadSprite(asset, bundle, function()
					self.node_list["img_heart"..i].image:SetNativeSize()
				end)

			elseif i <= intimacy_level or intimacy_level > 5 then
				local asset, bundle = ResPath.GetF2Society("a3_sj_xx1")
				self.node_list["img_heart"..i].image:LoadSprite(asset, bundle, function()
					self.node_list["img_heart"..i].image:SetNativeSize()
				end)
			else
				local asset, bundle = ResPath.GetF2Society("a3_sj_xx3")
				self.node_list["img_heart"..i].image:LoadSprite(asset, bundle, function()
					self.node_list["img_heart"..i].image:SetNativeSize()
				end)
			end
		end

		self.node_list["layout_qinmidu"]:SetActive(true)
		-- need_intimacy = need_intimacy > 0 and need_intimacy or 1
		-- self.node_list["img_heart1"].image:LoadSprite(ResPath.GetF2Society("sj_haogandu"..color))
		-- self.node_list["img_heart1"].image:SetNativeSize()
		-- self.node_list["img_heart1"].image.fillAmount = 1-- self.data.intimacy / need_intimacy
		-- self.node_list["img_rank_ten"]:SetActive(SocietyWGData.Instance:ChackZhanliRank(self.data.user_id) <= 10)
		-- self.node_list["layout_qinmidu"]:SetActive(true)
		--self:SetHuMoInfoShow()
	end

	if not SocietyWGData.Instance:GetIsMyFriend(self.data.user_id) then
		self.node_list["layout_qinmidu"]:SetActive(false)
		--self.node_list["btn_humo"]:SetActive(false)
		-- self.role_avatar:AddItems(Language.Menu.AddFriend)
		--self.role_avatar:RemoveItems(Language.Menu.Profess)
		self.role_avatar:RemoveItems(Language.Menu.InviteTeam)
		self.role_avatar:RemoveItems(Language.Menu.DeleteFriend)
	else
		self.role_avatar:RemoveItems(Language.Menu.AddFriend)
		--self.role_avatar:RemoveItemsList(Language.Menu.Profess)
		self.role_avatar:RemoveItemsList(Language.Menu.InviteTeam)
		self.role_avatar:RemoveItemsList(Language.Menu.DeleteFriend)
	end
end

--前往祝福
function SocietyFriendRender:OnClickGoBlessing()
	if self.data == nil then return end
	SocietyWGCtrl.Instance:ToSendBless(self.data.user_id)
end

-- 打开结婚
function SocietyFriendRender:OnClickMarriedIcon()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_jiehun")
end
--赠送虎摸
function SocietyFriendRender:OnClickHuoMo()
	local be_shengyu_num = SocietyWGData.Instance:GetDayBeTouchShengYuNum(self.data.vip_level,self.data.today_total_touched_count)
	local shengyu_num = SocietyWGData.Instance:GetDayTouchShengYuNum()

	if self.data.today_touch_friend_flag == 1 then--已熊抱对方
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.DayYmHint)
		return
	elseif be_shengyu_num <= 0 then--对方次数不足
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.HuMoNumLimitHint)
		return
	elseif shengyu_num <= 0 then--自己次数不足
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.HuMoNumLimitHint2)
		return
	end

	local total_exp = BiZuoWGData.Instance:GetTotalExp() or 0
	local other_cfg = SocietyWGData.Instance:GetHuMoOtherCfg()
	local limit_huoyue = other_cfg.daily_liveness_limit or 0
	if total_exp < limit_huoyue then
		self:NoCanHuMoHint(limit_huoyue)
		return
	end

	SocietyWGCtrl.Instance:SendTouchOperReq(HUMO_TYPE.HM_FRIEND,self.data.user_id)
	--请求成功后播放特效
	--TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_humo, is_success = true, pos = Vector2(0, 0)})
end

function SocietyFriendRender:NoCanHuMoHint(limit_huoyue)
	if not self.hint_alert then
		self.hint_alert = Alert.New()
	end
	self.hint_alert:SetLableString(string.format(Language.Society.HuMoHuoYueLimint,limit_huoyue))
	self.hint_alert:SetOkFunc(function()
		ViewManager.Instance:Open(GuideModuleName.BiZuo,"bizuo_bizuo")
	end)
	self.hint_alert:Open()
end

--领取虎摸奖励
function SocietyFriendRender:OnClickHuoMoLingQu()
	SocietyWGCtrl.Instance:SendTouchOperReq(HUMO_TYPE.LQ_REWARD,self.data.user_id)
	SocietyWGCtrl.Instance:OpenBeTouchView(self.data)
end

-- --虎摸信息标识的显示
-- function SocietyFriendRender:SetHuMoInfoShow()
-- 	local shengyu_num = SocietyWGData.Instance:GetDayTouchShengYuNum()
-- 	local can_lingqu = self.data.touch_me_total_count > 0
-- 	self.node_list["btn_lq"]:SetActive(can_lingqu)
-- 	self.node_list["btn_humo"]:SetActive(not can_lingqu)
-- 	--self.node_list["ing_humo"]:SetActive(self.data.touch_me_total_count >= 1)
-- 	--self.node_list["humo_num"].text.text = self.data.touch_me_total_count
-- 	local is_enabled = true
-- 	if 1 == self.data.today_touch_friend_flag or shengyu_num <= 0 then
-- 		is_enabled = false
-- 	end
-- 	-- self.node_list["btn_humo"].button.enabled = is_enabled
-- 	local icon_name = not is_enabled and "sj_xiongbao2" or "sj_xiongbao1"
-- 	local be_shengyu_num = SocietyWGData.Instance:GetDayBeTouchShengYuNum(self.data.vip_level,self.data.today_total_touched_count)
-- 	--self.node_list["img_humo_no"]:SetActive(be_shengyu_num <= 0 and is_enabled)
-- 	self.node_list["btn_humo"].image:LoadSprite(ResPath.GetF2Society(icon_name))
-- 	self.node_list["img_humo_red"]:SetActive(be_shengyu_num > 0 and is_enabled and SocietyWGData.Instance:GetHuoYueOFHuMo())
-- end

function SocietyFriendRender:OnClickOpenMenu()

end

function SocietyFriendRender:OnClickOpenQinMi()
	MarryWGCtrl.Instance:SetIntimacyData(self.data.intimacy, self.data.gamename)
end

function SocietyFriendRender:CreateSelectEffect()
end

--邀请祝福
function SocietyFriendRender:OnClickRequestBlessing()
end

function SocietyFriendRender:UpdateOpenCountDownTime(elapse_time, total_time)
end

function SocietyFriendRender:CompleteOpenCountDownTime()
end




----------------------------------------------------------------------------------------------------
-- 好友请求列表
----------------------------------------------------------------------------------------------------
SocietyReqFriendRender = SocietyReqFriendRender or BaseClass(SocietyBaseRender)
function SocietyReqFriendRender:__init()
	self.node_list["btn_refuse"].button:AddClickListener(function()
		local data = self.data
		GlobalTimerQuest:AddDelayTimer(function () SocietyWGCtrl.Instance.add_req_view:OnReplyReq(0, true, data) end, 0)
	end)

	self.node_list["btn_agree"].button:AddClickListener(function()
		local data = self.data
		GlobalTimerQuest:AddDelayTimer(function () SocietyWGCtrl.Instance.add_req_view:OnReplyReq(1, true, data) end, 0)
	end)
end

function SocietyReqFriendRender:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
end

function SocietyReqFriendRender:SetRoleHead()
	if self.node_list["img_head"] and self.node_list["custom_img_head"] then		-- 头像
		XUI.UpdateRoleHead(self.node_list["img_head"], self.node_list["custom_img_head"], self.data.user_id, self.data.req_sex,self.data.req_prof,false, false, true)
	end
end

function SocietyReqFriendRender:OnFlush()
	if nil  == self.data then
		return
	end

	self.node_list["lbl_name"].text.text = self.data.req_gamename--ToColorStr(self.data.req_gamename, SEX_COLOR[self.data.req_sex][3])

	local bundle, asset = ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex))
    self.node_list.label_prof.image:LoadSprite(bundle, asset)
--	self.node_list["label_prof"].text.text = Language.Common.ProfName[self.data.req_prof]--ToColorStr(tostring(Language.Common.ProfName[self.data.req_prof]), PROF_COLOR3B[self.data.req_prof])
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.req_level)
	self.node_list.img_fire:SetActive(is_vis)
	self:SetRoleHead()
	if is_vis then
		self.node_list["label_level"].text.text = role_level
	else
	self.node_list["label_level"].text.text = "Lv." .. role_level--RoleWGData.GetLevelString(self.data.level)
	end
end
function SocietyReqFriendRender:OnSelectChange(is_select)
	-- self.node_list.img_choose:SetActive(is_select)
end


----------------------------------------------------------------------------------------------------
-- 一键添加好友列表
----------------------------------------------------------------------------------------------------
SocietyAutoAddFriendRender = SocietyAutoAddFriendRender or BaseClass(SocietyBaseRender)
function SocietyAutoAddFriendRender:__init()
	self.is_choose = true
	self.node_list["tog_choose"].toggle:AddValueChangedListener(BindTool.Bind(self.OnChoose, self))
end

function SocietyAutoAddFriendRender:__delete()

end


function SocietyAutoAddFriendRender:OnFlush()
	if nil == self.data then
		return
	end

	local bundle, asset = ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex))
    self.node_list.label_prof.image:LoadSprite(bundle, asset)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.img_fire:SetActive(is_vis)
	if is_vis then
		self.node_list["label_level"].text.text = role_level
	else
	self.node_list["label_level"].text.text = "Lv."..role_level--RoleWGData.GetLevelString(self.data.level)
	end

	self:SetRoleHead()
	self.node_list["lbl_name"].text.text = self.data.gamename-- ToColorStr(self.data.gamename, SEX_COLOR[self.data.sex][3])
	--self.node_list["label_prof"].text.text = Language.Common.ProfName[self.data.prof]--ToColorStr(tostring(Language.Common.ProfName[self.data.prof]), PROF_COLOR3B[self.data.prof])


end

function SocietyAutoAddFriendRender:SetRoleHead()
	if self.node_list["img_head"] and self.node_list["custom_img_head"] then		-- 头像
		XUI.UpdateRoleHead(self.node_list["img_head"], self.node_list["custom_img_head"], self.data.user_id, self.data.sex, self.data.prof, false)
	end
end

function SocietyAutoAddFriendRender:OnSelectChange(is_select)
	-- if next(self.node_list) == nil then return end
	-- if is_select then
	-- 	self.node_list.img_choose:SetActive(true)
	-- else
	-- 	self.node_list.img_choose:SetActive(false)
	-- end
end

function SocietyAutoAddFriendRender:OnChoose(isOn)
	self.is_choose = isOn
	SocietyWGData.Instance:GetAddfriendList()[self.data.user_id] = self.is_choose
end



----------------------------------------------------------------------------------------------------
--邮件列表
----------------------------------------------------------------------------------------------------
SocietyMailRender = SocietyMailRender or BaseClass(SocietyBaseRender)
function SocietyMailRender:__init()
end

function SocietyMailRender:LoadCallBack()

end

function SocietyMailRender:__delete()
end

function SocietyMailRender:OnFlush()
	if nil == self.data then
		return
	end

	local mail_status = self.data.mail_status
	self.node_list["label_msgtime"].text.text = os.date("%Y/%m/%d %H:%M", mail_status.recv_time)
	local is_read = mail_status.is_read

	local msg_begin = ""
	if nil ~= Language.Society["From"] then
		msg_begin = Language.Society["From"]
	end

	local msg = ""
	if nil ~= Language.Society["GuangFang"] then
		msg = Language.Society["GuangFang"]
	end

	local msg_end = ""
	if nil ~= Language.Society["Mail"] then
		msg_end = Language.Society["Mail"]
	end

	--私人
	if 1 == mail_status.kind then
		msg = mail_status.sender_name
	end

	if 3 == mail_status.kind then
		msg = Language.Society.guild
	end

	if self.data.subject and self.data.subject ~= ""then
		msg = self.data.subject
	end

	self.node_list.img_new:CustomSetActive(is_read ~= 1)
	
	self.node_list.attachment_flag:SetActive(self.data.has_attachment > 0)

	local str11 = EmojiTextUtil.GetAnalysisText(msg, "#A84B05FF", RICH_CONTENT_TYPE.Mail)
	self.node_list["label_msg"].text.text = str11

	
	XUI.SetGraphicGrey(self.node_list.img_open,self.data.has_attachment <= 0)
	if is_read == 1 then
		self.node_list.img_open:SetActive(true)
		self.node_list.img_close:SetActive(false)

	else
		self.node_list.img_open:SetActive(false)
		self.node_list.img_close:SetActive(true)
	end

	-- if 0 == self.data.has_attachment then
	-- 	self.node_list.img9_render_bg.image:LoadSprite(ResPath.GetCommonButton("a2_ty_anniu_12"))
	-- else
	-- 	self.node_list.img9_render_bg.image:LoadSprite(ResPath.GetCommonButton("a2_ty_anniu_12"))
	-- end
end

function SocietyMailRender:OnSelectChange(is_select)
	-- self.node_list.img9_render_bg:SetActive(not is_select)
	self.node_list.img9_render_select:SetActive(is_select)
end

function SocietyMailRender:CreateSelectEffect()

end

--单独好友列表
----------------------------------------------------------------------------------------------------
SendflowerFriendListRender = SendflowerFriendListRender or BaseClass(BaseRender)
function SendflowerFriendListRender:__init()
end

function SendflowerFriendListRender:__delete()
end

function SendflowerFriendListRender:OnFlush()
	if self.data == nil then return end
	self.node_list["lbl_name"].text.text = self.data.gamename
end

function SendflowerFriendListRender:OnSelectChange(is_select)
	if is_select then
		self.node_list.img_choose:SetActive(true)
	else
		self.node_list.img_choose:SetActive(false)
	end
end


FriendChatBagItemRender = FriendChatBagItemRender or BaseClass(BaseRender)

function FriendChatBagItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.cell_node)
	self.item_cell:SetIsShowTips(false)
	self.item_cell:IsCanDJ(false)
end

function FriendChatBagItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
	end
end

function FriendChatBagItemRender:OnFlush()
	if self.data == nil then
		return
	end

	self.item_cell:SetData(self.data)
	self.item_cell:SetButtonComp(false)
	if self.data.frombody and ChatWGData.Instance:GetIsChatBag() then
		self.node_list.equip_flag:SetActive(true)
	else
		self.node_list.equip_flag:SetActive(false)
	end
end
