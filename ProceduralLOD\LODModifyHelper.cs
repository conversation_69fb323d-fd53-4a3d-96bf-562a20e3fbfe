#if UNITY_EDITOR

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.Serialization;

namespace ProceduralLOD
{
    [ExecuteAlways]
    public class LODModifyHelper : MonoBehaviour
    {
        public delegate IEnumerator OnCompleted(ModifyResult result);
        public OnCompleted onCompleted;
        
        public GameObject target;
        public int lodIndex;
        public Simplifier targetSimplifier;

        [SerializeField] private Dictionary<Renderer, Renderer> m_RawRendererDict;

        public static LODModifyHelper Modify(GameObject target)
        {
            GameObject temp = new GameObject(target.name + " (Editing)");
            temp.transform.SetParent(target.transform.parent);
            temp.transform.position = target.transform.position;
            temp.transform.rotation = target.transform.rotation;
            temp.transform.localScale = Vector3.one;
            LODModifyHelper modifyHelper = temp.AddComponent<LODModifyHelper>();
            modifyHelper.target = target;
            target.SetActive(false);
            return modifyHelper;
        }

        public void Setup(float strength)
        {
            targetSimplifier.BeginModify(this.gameObject, strength);
            
            try
            {
                LODGroup lodGroup = this.GetComponentInParent<LODGroup>();
                LOD[] lods = lodGroup.GetLODs();
                LOD lod0 = lods[0];
                this.m_RawRendererDict = new Dictionary<Renderer, Renderer>();
                for (int i = 0; i < lod0.renderers.Length; i++)
                {
                    Renderer renderer = lod0.renderers[i];
                    Renderer cloneRenderer = null;
                    GameObject clone = new GameObject(renderer.name);
                    if (renderer is MeshRenderer mr)
                    {
                        cloneRenderer = clone.AddComponent<MeshRenderer>();
                        cloneRenderer.sharedMaterials = mr.sharedMaterials;
                        clone.AddComponent<MeshFilter>().mesh = mr.GetComponent<MeshFilter>().sharedMesh;
                    }
                    else if (renderer is SkinnedMeshRenderer smr)
                    {
                        cloneRenderer = clone.AddComponent<SkinnedMeshRenderer>();
                        cloneRenderer.sharedMaterials = smr.sharedMaterials;
                        (cloneRenderer as SkinnedMeshRenderer).sharedMesh = smr.sharedMesh;
                    }
                    clone.transform.SetParent(this.transform);
                    clone.transform.position = renderer.transform.position;
                    clone.transform.rotation = renderer.transform.rotation;
                    clone.transform.localScale = renderer.transform.localScale;
                    renderer.forceRenderingOff = true;
                    this.m_RawRendererDict.Add(cloneRenderer, renderer);
                }

                lodGroup.ForceLOD(0);
                Selection.activeGameObject = this.gameObject;
            }
            catch (Exception e)
            {
                Debug.LogError(e);
                Clear();
            }
        }
        
        public void Complete()
        {
            var result = new ModifyResult();
            //result.target = this.target;
            result.lod = this.lodIndex;
            result.strength = targetSimplifier.EndModify();
            
            if (onCompleted != null)
                CoroutineRunner.RunCoroutine(onCompleted.Invoke(result));
            
            Clear(true);
        }

        public void Clear(bool jumpSelection = false)
        {
            if (this.m_RawRendererDict != null)
            {
                foreach (var rawRenderer in this.m_RawRendererDict.Values)
                {
                    if (rawRenderer != null)
                    {
                        rawRenderer.forceRenderingOff = false;
                    }
                }
            }

            LODGroup parentLODGroup = this.GetComponentInParent<LODGroup>();
            if (parentLODGroup != null)
            {
                parentLODGroup.ForceLOD(-1);
                if (jumpSelection)
                    Selection.activeGameObject = parentLODGroup.gameObject;
            }

            if (this.target != null)
            {
                this.target.SetActive(true);
            }

            DestroyImmediate(this.gameObject);
            
            EditorSceneManager.MarkAllScenesDirty();
        }
        
    }
    
    public class ModifyResult
    {
        //public GameObject target;
        public int lod;
        public float strength;
    }
}

#endif