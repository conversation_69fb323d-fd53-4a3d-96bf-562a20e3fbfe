{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "6715337ad3874c11bc75692f02c2634e",
    "m_Properties": [
        {
            "m_Id": "3c90fc44a52f4566bdfd58d493c99cf5"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "6b9ef6ae45264f1497e45d974fb4d7e6"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "c6029d218f0b4f6e9378e4228371eb42"
        },
        {
            "m_Id": "3ac90bcee1a042ee8abb1bb84b753da2"
        },
        {
            "m_Id": "99c48b6fb2e942f7ba37f5832a4c53d7"
        },
        {
            "m_Id": "e9a627dbe38340afa2dc5a9109cc83bd"
        },
        {
            "m_Id": "744b67192e5849349b6cd9563a9a2a98"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3ac90bcee1a042ee8abb1bb84b753da2"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "99c48b6fb2e942f7ba37f5832a4c53d7"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "744b67192e5849349b6cd9563a9a2a98"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e9a627dbe38340afa2dc5a9109cc83bd"
                },
                "m_SlotId": 4
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "99c48b6fb2e942f7ba37f5832a4c53d7"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e9a627dbe38340afa2dc5a9109cc83bd"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e9a627dbe38340afa2dc5a9109cc83bd"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c6029d218f0b4f6e9378e4228371eb42"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "c6029d218f0b4f6e9378e4228371eb42"
    },
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0762e06de25444f6a0b30aa1fdb5ea12",
    "m_Id": 1,
    "m_DisplayName": "AngleOffset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "AngleOffset",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 2.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2acc1e9ffc13449ea22e49e838081b66",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "330aea940fb54298ae99f0ce0e299a01",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionNode",
    "m_ObjectId": "3ac90bcee1a042ee8abb1bb84b753da2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Screen Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -646.0,
            "y": 97.00003814697266,
            "width": 145.0,
            "height": 128.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "330aea940fb54298ae99f0ce0e299a01"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_ScreenSpaceType": 3
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "3c90fc44a52f4566bdfd58d493c99cf5",
    "m_Guid": {
        "m_GuidSerialized": "20a77555-871e-440e-989e-0af77d070d4a"
    },
    "m_Name": "InOut",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "InOut",
    "m_DefaultReferenceName": "_InOut",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 2,
    "m_Hidden": false,
    "m_Value": 0.5,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "57c809e69f964097bc4844b054100d6c",
    "m_Id": 2,
    "m_DisplayName": "CellDensity",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "CellDensity",
    "m_StageCapability": 3,
    "m_Value": 32.0,
    "m_DefaultValue": 5.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "599bba6cc57947bf98c921e090c67601",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5e09fe6a0d4e4373a7a7be6d87dab35c",
    "m_Id": 0,
    "m_DisplayName": "InOut",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "6b9ef6ae45264f1497e45d974fb4d7e6",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "3c90fc44a52f4566bdfd58d493c99cf5"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "744b67192e5849349b6cd9563a9a2a98",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -305.0,
            "y": 33.0,
            "width": 104.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5e09fe6a0d4e4373a7a7be6d87dab35c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "3c90fc44a52f4566bdfd58d493c99cf5"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "86e7ec1876c940559e0baa19723c9a36",
    "m_Id": 4,
    "m_DisplayName": "Cells",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cells",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8bc0c293e1ce41e781c3ff3497c167fc",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.VoronoiNode",
    "m_ObjectId": "99c48b6fb2e942f7ba37f5832a4c53d7",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Voronoi",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -416.0,
            "y": 97.00003814697266,
            "width": 188.00001525878907,
            "height": 176.99993896484376
        }
    },
    "m_Slots": [
        {
            "m_Id": "599bba6cc57947bf98c921e090c67601"
        },
        {
            "m_Id": "0762e06de25444f6a0b30aa1fdb5ea12"
        },
        {
            "m_Id": "57c809e69f964097bc4844b054100d6c"
        },
        {
            "m_Id": "2acc1e9ffc13449ea22e49e838081b66"
        },
        {
            "m_Id": "86e7ec1876c940559e0baa19723c9a36"
        }
    ],
    "synonyms": [
        "worley noise"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_HashType": 1
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ad72c733906a49dc9645c5ffe6bc8934",
    "m_Id": 2,
    "m_DisplayName": "mask",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "mask",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "c6029d218f0b4f6e9378e4228371eb42",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 69.00000762939453,
            "y": 0.00001239776611328125,
            "width": 91.00000762939453,
            "height": 76.99999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "c846ab5ec1d84ba1b8cadeea0ce710ae"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c846ab5ec1d84ba1b8cadeea0ce710ae",
    "m_Id": 1,
    "m_DisplayName": "Output",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Output",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "e9a627dbe38340afa2dc5a9109cc83bd",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "CrossFade (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -183.00003051757813,
            "y": 0.0000247955322265625,
            "width": 216.0000762939453,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ef68ad81b25a406ab80c9a89c0d41da6"
        },
        {
            "m_Id": "ad72c733906a49dc9645c5ffe6bc8934"
        },
        {
            "m_Id": "8bc0c293e1ce41e781c3ff3497c167fc"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "CrossFade",
    "m_FunctionSource": "",
    "m_FunctionBody": "Out = Input;\r\n#if LOD_FADE_CROSSFADE\r\n#ifdef UNITY_PROCEDURAL_INSTANCING_ENABLED\r\n\tif (LODLevel >= 0)\r\n\t{\r\n#ifdef GPUI_MHT_COPY_TEXTURE\r\n\t\tuint textureWidth = min(bufferSize, maxTextureSize);\r\n\t\tfloat indexX = ((gpui_InstanceID % maxTextureSize) + 0.5f) / textureWidth;\r\n\t\tfloat rowCount = ceil(bufferSize / maxTextureSize);\r\n\t\tfloat rowIndex = floor(gpui_InstanceID / maxTextureSize) + 0.5f;\r\n\r\n\t\tuint4 lodData = uint4(tex2Dlod(gpuiInstanceLODDataTexture, float4(indexX, rowIndex / rowCount, 0.0f, 0.0f)));\r\n#else\r\n\t\tuint4 lodData = gpuiInstanceLODData[gpui_InstanceID];\r\n#endif\r\n\tif (lodData.w > 0)\r\n\t{\r\n\t\tfloat fadeLevel = floor(lodData.w * fadeLevelMultiplier) * 0.0625;\r\n\r\n\t\tif (lodData.z == uint(LODLevel))\r\n\t\t\tfadeLevel = - fadeLevel;\r\n\r\n\t\tclip(fadeLevel - mask * sign(fadeLevel));\r\n\t}\r\n}\r\n\r\n#else\r\n\tfloat sgn = unity_LODFade.x > 0 ? 1.0f : -1.0f;\r\n\tclip(unity_LODFade.x - mask * sgn);\r\n#endif\r\n#endif\r\n"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ef68ad81b25a406ab80c9a89c0d41da6",
    "m_Id": 4,
    "m_DisplayName": "Input",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Input",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

