
--[[local string = string

AStarFindWay = AStarFindWay or {
	w = 0,											-- 宽
	h = 0,											-- 高
	mask_table = {},								-- 阻挡信息，二维数组
	start_pos = {x = 0, y = 0},						-- 起点
	end_pos = {x = 0, y = 0},						-- 终点
	open_list = {},									-- 开放列表
	map = {},										-- 寻路信息缓存
}

AStarFindWay.BLOCK = string.byte("0")				-- 阻挡
AStarFindWay.WAY = string.byte("1")					-- 可通行
AStarFindWay.SAFE_AREA = string.byte("2")				-- 安全区

function AStarFindWay.PointInfo()
	return {
		x = 0,
		y = 0,
		block = false,
		g = 0,
		h = 0,
		parent = nil,
		dir = 0,
	}
end]]

AStarFindWay = AStarFindWay or {}
GridFindWay = GridFindWay.New(512, 512)

MoveableObject.SetGridFindWay(GridFindWay)

function AStarFindWay:Init(mask_str, w, h)
	GridFindWay:LoadData(w, h, mask_str)
end

--[[
	执行A*寻路算法
	@param start_pos 起点坐标{table(x,y)}
	@param end_pos 终点坐标{table(x,y)}
	@param ignore_high_area 是否忽略高地阻挡
	@param ignore_block 是否忽略所有阻挡（调试模式）
	@return bool 是否存在可行路径
]]
function AStarFindWay:FindWay(start_pos, end_pos, ignore_high_area, ignore_block)
	return GridFindWay:FindWay(start_pos.x, start_pos.y, end_pos.x, end_pos.y, ignore_high_area, ignore_block)
end

--[[
	生成优化后的路径拐点列表
	@param range 终点附近优化范围（单位：网格）
	@return table 路径点列表{ {x,y}, ... }
]]
function AStarFindWay:GenerateInflexPoint(range)
	GridFindWay:GenerateInflexPoints(range)
	local pos_len = GridFindWay:GetPathLength()
	local pos_list = {}
	for i = 0, pos_len-1 do
		local had_point, x, y = GridFindWay:GetPathPoint(i, nil, nil)
		table.insert(pos_list, {x = x, y = y})
	end
	return pos_list
end

--是否水区
function AStarFindWay:IsWaterWay(x, y)
	return GridFindWay:IsWaterWay(x, y)
end

-- 是否阻挡区域
-- @param ignore_high_area 是否忽略高地地形
-- @param ignore_block 是否忽略所有阻挡
function AStarFindWay:IsBlock(x, y, ignore_high_area, ignore_block)
	local is_block = GridFindWay:IsBlock(x, y, ignore_high_area, ignore_block)
	return is_block
end

-- 是否安全区域
function AStarFindWay:IsInSafeArea(x, y)
	return GridFindWay:IsInSafeArea(x, y)
end

--[[
	从起点 向 目标方向直线查有效点
	@param x,y 起点坐标
	@param end_x,end_y 目标坐标
	@return 最后一个可达点的x,y坐标
]]
function AStarFindWay:GetLineEndXY(x, y, end_x, end_y, ignore_high_area, ignore_block)
	return GridFindWay:GetLineEndXY(x, y, end_x, end_y, nil, nil, ignore_high_area, ignore_block)
end

--[[
	从目标 向 起点方向直线查有效点
	@param x,y 起点坐标
	@param end_x,end_y 目标坐标
	@return 第一个可通行点的x,y坐标
]]
function AStarFindWay:GetLineEndXY2(x, y, end_x, end_y, ignore_high_area, ignore_block)
	return GridFindWay:GetLineEndXY2(x, y, end_x, end_y, nil, nil, ignore_high_area, ignore_block)
end

--[[
	获取目标点周围最近的有效站立位置
	@param x 起始点X坐标
	@param y 起始点Y坐标
	@param endX 目标点X坐标
	@param endY 目标点Y坐标
	@param range 最大检测范围
	@param targetX 输出有效点X坐标
	@param targetY 输出有效点Y坐标
	@param range 输出实际有效距离
]]
function AStarFindWay:GetTargetXY(x, y, end_x, end_y, range)
	return GridFindWay:GetTargetXY(x, y, end_x, end_y, range, nil, nil, nil)
end

-- 是否可直线通行
function AStarFindWay:IsWayLine(x, y, end_x, end_y, ignore_high_area, ignore_block)
	return GridFindWay:IsWayLine(x, y, end_x, end_y, ignore_high_area, ignore_block)
end

--[[
	计算路径实际路程（考虑地形因素）
	@return 实际移动距离（单位：网格）
	-- 实现流程：
    -- 1. 直线通行检测
    -- 2. 需要寻路时生成路径点
    -- 3. 累加各路径段距离
]]
function AStarFindWay:GetWaySpatium(x, y, end_x, end_y, range)
	-- 判断是否可以直接走过去
	local is_way_line = GridFindWay:IsWayLine(x, y, end_x, end_y)
	if is_way_line then
		local delta_pos = u3d.v2Sub(u3d.vec2(x, y), u3d.vec2(end_x, end_y))
	    local distance = u3d.v2Length(delta_pos)
		return distance
	end

	local point_list = AStarFindWay:GenerateInflexPoint(range)

	local spatium = 0
	local last_v = nil
	for i,v in ipairs(point_list) do
		if last_v ~= nil then
			local delta_pos = u3d.v2Sub(u3d.vec2(last_v.x, last_v.y), u3d.vec2(v.x, v.y))
	    	local distance = u3d.v2Length(delta_pos)
	    	spatium = spatium + distance
		end
		last_v = v
	end
	return spatium
end

-- 设置阻挡变更回调函数
function AStarFindWay:SetBlockChangeCallback(block_change_callback)
	AStarFindWay.block_change_callback = block_change_callback
end

-- 设置指定坐标的阻挡状态
function AStarFindWay:SetBlockInfo(x, y)
	GridFindWay:SetBlock(x, y)

	if nil ~= AStarFindWay.block_change_callback then
		AStarFindWay.block_change_callback()
	end
end

-- 清除指定坐标的阻挡状态
function AStarFindWay:RevertBlockInfo(x, y)
	GridFindWay:RevertBlock(x, y)

	if nil ~= AStarFindWay.block_change_callback then
		AStarFindWay.block_change_callback()
	end
end

--[[
	自身点以及范围内找点
	@param dir 方向向量{x,y}
	@param check_self 如果为false，排除自身点，只在周围找
	@return 沿该方向找到的第一个有效点
	（应用场景：用于技能释放位置修正）
]]
function AStarFindWay:GetAroundVaildXY(x, y, range, check_self, dir)
	if false ~= check_self and not AStarFindWay:IsBlock(x, y) then
		return x, y
	end

	-- 优先沿指定方向搜索
	if dir ~= nil and dir.x ~= nil and dir.y ~= nil then
		local d_x = math.floor(x + range * dir.x)
		local d_y = math.floor(y + range * dir.y)
		if not AStarFindWay:IsBlock(d_x, d_y) then
			return d_x, d_y
		end
	end

	for _x = x - range, x + range do
		for _y = y - range, y + range do
			if not AStarFindWay:IsBlock(_x, _y) then
				return _x, _y
			end
		end
	end

	return x, y
end

--[[
	【避险位移方法】沿目标反方向寻找最近安全点
	检测顺序: 远→近
	@param range 最大检测距离
	应用场景：被追击时的紧急避险
]]
function AStarFindWay:GetRangeVaildXY(x, y, self_x, self_y, range)
	local _x, _y
	local move_dir = u3dpool.v2Normalize(Vector2(self_x, self_y) - Vector2(x, y))
	for i=range,1,-1 do
		-- 计算逃离方向（远离自身位置）
		-- 计算候选坐标
   		_x, _y = math.floor(x + move_dir.x * i), math.floor(y + move_dir.y * i)
   		if not AStarFindWay:IsBlock(_x, _y) then
			return _x, _y
		end
	end

	return x, y
end

--[[
	【追击位移方法】沿目标方向寻找最近可达点
	检测顺序: 近→远
	@param range 最大接近距离
	应用场景：接近目标
]]
function AStarFindWay:GetOppositeRangeVaildXY(x, y, self_x, self_y, range)
	local _x, _y
	local move_dir = u3dpool.v2Normalize(Vector2(self_x, self_y) - Vector2(x, y))
	for i = range, 0, -1 do
		-- 计算接近方向（朝向自身位置）
   		_x, _y = math.floor(x + move_dir.x * i), math.floor(y + move_dir.y * i)
   		if not AStarFindWay:IsBlock(_x, _y) then
			return _x, _y
		end
	end

	return x, y
end

-- 找到目标附近的最佳点
function AStarFindWay:FindOptimalPointNearTarget(x, y, target_x, target_y, range)
	--[[
	print_error("--找到目标附近的最佳点--", x, y, target_x, target_y, range)
    local dirX = x - target_x
    local dirY = y - target_y
    local distance = math.sqrt(dirX * dirX + dirY * dirY)

    if distance > 0 then
        dirX = dirX / distance
        dirY = dirY / distance
    else
        -- A和B点重合的情况
		return x, y
	end

    -- 第一阶段：从B向A方向查找
    for i = range, 1, -1 do
        local checkX = GameMath.Round(target_x + dirX * i)
        local checkY = GameMath.Round(target_y + dirY * i)
        if (not AStarFindWay:IsBlock(checkX, checkY)) then
			print_error("--第一阶段：从B向A方向查找--",  dirX * i, dirY * i, checkX, checkY, i)
            return checkX, checkY
		end
	end

    -- 第二阶段：从B背向A方向查找
    for i = range, 1, -1 do
        -- 计算待检测点坐标（方向取反）
        local checkX = GameMath.Round(target_x - dirX * i)
        local checkY = GameMath.Round(target_y - dirY * i)
        if (not AStarFindWay:IsBlock(checkX, checkY)) then
			print_error("--第二阶段：从B背向A方向查找--", checkX, checkY, i)
            return checkX, checkY
		end
	end

	if (not AStarFindWay:IsBlock(target_x, target_y)) then
		return target_x, target_y
	end

	return x, y
	]]

	return GridFindWay:FindOptimalPointNearTarget(x, y, target_x, target_y, range, nil, nil)
end







--[[
	获取指定范围内的随机有效坐标
	@param range 搜索半径
	@param check_self 是否优先检查原点
	@return x,y 有效坐标（找不到时返回nil）
]]
function AStarFindWay:GetRandomVaildXY(x, y, range, check_self)
	-- 使用蒙特卡洛方法进行随机采样
	check_self = check_self or false
	if false ~= check_self and not AStarFindWay:IsBlock(x, y) then
		return x, y
	end

	local num = 1
	local random_num = range * range
	random_num = random_num > 20 and 20 or random_num
	local totle_num = range * 2
	local r_x, r_y = 0, 0
	while(num <= random_num) do
		r_x = math.floor(math.random(0, totle_num)) - range + x
		r_y = math.floor(math.random(0, totle_num)) - range + y
		if not AStarFindWay:IsBlock(r_x, r_y) then
			return r_x, r_y
		end
		num = num + 1
	end
	return nil, nil
end

--[[
	【区域采样方法】生成指定区域内的多个随机有效点
	1. 限制最大尝试次数（防止高阻挡区域死循环）
	2. 距离筛选（min_range~max_range环形区域）

	@param min_range 最小生成距离（避免过于集中）
	@param max_range 最大生成范围
	@param num_point 需要生成的点的数量
	@return 有效坐标列表（可能少于请求数量）
	应用场景：刷怪点/任务物品的多位置生成
]]
function AStarFindWay:GetRandomVaildXYList(x, y, min_range, max_range, num_point)
	local list = {}
	local num = 1
	local distance = 0
	local random_num = max_range * max_range
	local totle_num = max_range * 2
	local r_x, r_y = 0, 0
	while(num <= random_num) do
		r_x = math.floor(math.random(0, totle_num)) - max_range + x
		r_y = math.floor(math.random(0, totle_num)) - max_range + y

		distance = GameMath.GetDistance(x, y, r_x, r_y, true)
		if distance >= min_range and not AStarFindWay:IsBlock(r_x, r_y) then
			table.insert(list, {x = r_x, y = r_y})
		end
		num = num + 1
		if num > num_point then
			break
		end
	end
	return list
end

function AStarFindWay:FindNearestValidPoint(x, y, range)
	return GridFindWay:FindNearestValidPoint(x, y, range, nil, nil)
end

function AStarFindWay:IsHighArea(x, y)
	return GridFindWay:IsHighArea(x, y)
end

function AStarFindWay:GetCellType(x, y)
	return GridFindWay:GetCellType(x, y)
end

-- 检测场景水区域显示人物水波纹
function AStarFindWay:IsWaterRipple(x, y)
	return GridFindWay:IsWaterRipple(x, y)
end

-- 是否不在场景区域内
function AStarFindWay:IsNotInGrid(x, y)
	return GridFindWay:IsNotInGrid(x, y)
end


--[[
	判断点A是否在目标点B的指定范围内（精确版）
	@param ax, ay 点A坐标
	@param bx, by 点B坐标
	@param range 检测范围（单位：网格）
	@param is_square 是否使用平方比较优化性能（默认true）
	@return bool 是否在范围内
]]
function AStarFindWay:IsInRange(ax, ay, bx, by, range)
    if not ax or not ay or not bx or not by or not range then
        return false
    end
    
    local delta_x = ax - bx
    local delta_y = ay - by
    
	local distance_sq = delta_x * delta_x + delta_y * delta_y
	return distance_sq <= (range * range)
end

--[[
	方向性范围检测（扇形区域检测）
	@param ax, ay 检测点坐标
	@param bx, by 中心点坐标
	@param dir_angle 扇形朝向角度（0-360度）
	@param angle_range 扇形角度范围（0-180度）
	@param radius 检测半径
	@return bool 是否在扇形区域内
]]
function AStarFindWay:IsInSector(ax, ay, bx, by, dir_angle, angle_range, radius)
    local dx = ax - bx
    local dy = ay - by
 
    -- 距离检测
    local distance_sq = dx * dx + dy * dy
    if distance_sq > radius * radius then
        return false
    end

    -- 角度计算
    local target_angle = math.deg(math.atan2(dy, dx))
    local angle_diff = math.abs(target_angle - dir_angle)
    angle_diff = angle_diff > 180 and 360 - angle_diff or angle_diff

    return angle_diff <= angle_range / 2
end


