MainUIView.LAST_DO_TASK_TIME = 0
local NEW_TASK_T = {}

local TASK_SHOWNA_PANEL_FUNC_CFG = {
	btn_customized_rumors = {["module_name"] = GuideModuleName.CustomizedRumorsView, ["remind"] = RemindName.CustomizedRumorsView},
}

local mainui_task_top_btn_list = {
    ["btn_top_task_xiuzhen"] = true,
    ["btn_top_task_cap_wel"] = true,
    ["btn_top_zhuansheng"] = true,
    ["btn_top_xiuwei_big"] = true,
    ["btn_top_xiuwei"] = true,
}

function MainUIView:__initTask()
	self.task_data = {}
    self.boss_data = {}
    self.person_data = {}
    self.elite_data = {}
    self.team_list = {}
    self.task_cell_list = {}
    self.boss_cell_list = {}
    self.elite_cell_list = {}
    self.team_cell_list = {}
	self.task_delay_func = {}
    self.task_func_key_list = {}
    self.boss_reward_t = {}
    self.delay_timer = {}
    self.money_p_timer = {}
    self.money_timer_count = 0
    self.item_p_timer = {}
    self.item_timer_count = 0
	self.today_act_jump_to_tip_timer = nil
	self.next_today_act_tip = nil

    self.has_load_callback = false
    -- self.is_shrink_btn_on = false
    self.can_open_team = false
    -- self.is_show_team = false
    self.is_show_task_list = true
    self.btn_is_active = true
    self.cur_boss_id = 0
    self.is_init_gold_change_particle = true
    self.is_can_flush_advance_notice = false

	self.task_event = GlobalEventSystem:Bind(OtherEventType.SHOW_TASK_CHANGE, BindTool.Bind(self.OnShowTaskChange, self)) 					-- 显示中的任务改变
    self.team_info_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind(self.OnTeamChange, self)) 					-- 队伍信息改变
    self.show_task_type_event = GlobalEventSystem:Bind(MainUIEventType.SHOW_TASK_TYPE, BindTool.Bind(self.ShowTaskTypeActive, self)) 		-- 更换任务栏显示
    self.refresh_boss_list = GlobalEventSystem:Bind(OtherEventType.BOSS_CHANGE, BindTool.Bind(self.RefreshBossList, self))
    self.show_team_invite_btn = GlobalEventSystem:Bind(OtherEventType.GUILD_ASSIST, BindTool.Bind(self.FlushTeamInvite, self))
    self.a3_gold_change_event = GlobalEventSystem:Bind(OtherEventType.Gold_Change_Event, BindTool.Bind(self.MainTaskBindGoldChangeFunc, self))
    -- self.mainui_item_data_event = BindTool.Bind(self.MainTaskItemChangeGetParticle, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.mainui_item_data_event)

    self.boss_info_change = GlobalEventSystem:Bind(OtherEventType.SHOW_BOSS_CHANGE, BindTool.Bind(self.OnClickSwitchOther, self))
    self.loaded_scene = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnThisSceneLoaded, self))
    self.task_arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind(self.OnTopPanelAniCallBack, self))
end

function MainUIView:__deleteTask()
	self:CancelBossShrinkTween()
    self:CleanOnlineRewardTimer()
    self:CancelTodayActTipTimer()
    GlobalEventSystem:UnBind(self.task_event)
    GlobalEventSystem:UnBind(self.team_info_event)
    GlobalEventSystem:UnBind(self.show_task_type_event)
    GlobalEventSystem:UnBind(self.task_item_event)
    GlobalEventSystem:UnBind(self.refresh_boss_list)
    GlobalEventSystem:UnBind(self.show_team_invite_btn)
    GlobalEventSystem:UnBind(self.main_task_gold_change_event)
    GlobalEventSystem:UnBind(self.a3_gold_change_event)
    GlobalEventSystem:UnBind(self.boss_info_change)
    GlobalEventSystem:UnBind(self.loaded_scene)
    GlobalEventSystem:UnBind(self.task_arrow_click_event)
    -- if self.mainui_item_data_event then
	-- 	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.mainui_item_data_event)
	-- 	self.mainui_item_data_event = nil
	-- end
    -- if self.task_button_tween_reversal_timer ~= nil then
    --     GlobalTimerQuest:CancelQuest(self.task_button_tween_reversal_timer)
    --     self.task_button_tween_reversal_timer = nil
    -- end
    for key, value in pairs(self.delay_timer) do
        if value then
            GlobalTimerQuest:CancelQuest(value)
            value = nil
        end
    end
    self.delay_timer = {}
    self.money_p_timer = {}
    self.item_p_timer = {}
    self.item_timer_count = 0
    self:CleanMoneyAllParticleTimer()
    self:CleanAllItemParticleTimer()

    if self.boss_invasion_privilege_tween_shake then
        self.boss_invasion_privilege_tween_shake:Kill()
        self.boss_invasion_privilege_tween_shake = nil
    end

    if self.advance_notice_time_quest then
        GlobalTimerQuest:CancelQuest(self.advance_notice_time_quest)
        self.advance_notice_time_quest = nil
    end
end

function MainUIView:TaskLoadCallBack()
	self.has_load_callback = true
    self.benti_btn_state = 1
	self.is_show_task = true
    self.show_add_exp = self.node_list["PersonContent"]
    self.button_content = self.node_list["ButtonContent"]
    -- self.task_parent_pos = self.node_list["TaskParent"].rect.anchoredPosition

    -- 任务
    self.task_list = self.node_list["TaskList"]
    local list_view_delegate = self.task_list.list_simple_delegate
    list_view_delegate.NumberOfCellsDel = function()
        return #self.task_data
    end
    list_view_delegate.CellRefreshDel = BindTool.Bind(self.RefresTaskListView, self)
    -- self.task_list.scroller:RefreshAndReloadActiveCellViews(true)

    -- boss
    self.boss_list = self.node_list["boss_list"]
    local list_view_delegate = self.boss_list.list_simple_delegate
    list_view_delegate.NumberOfCellsDel = function()
        return #self.boss_data
    end
    list_view_delegate.CellRefreshDel = BindTool.Bind(self.RefresBossListView, self)

	--精英boss
	self.elite_list = self.node_list["elite_list"]
	local list_view_delegate = self.elite_list.list_simple_delegate
	list_view_delegate.NumberOfCellsDel = function()
		return # self.elite_data
	end
	list_view_delegate.CellRefreshDel = BindTool.Bind(self.RefresEliteBossListView, self)

    -- 组队
    self.list_view = self.node_list["TeamList"]
    local scroller_delegate = self.list_view.list_simple_delegate
    scroller_delegate.NumberOfCellsDel = function()
        return #self.team_list or 0
    end
    scroller_delegate.CellRefreshDel = BindTool.Bind(self.RefreshTeamListView, self)

    --宣传图
    self.task_advance_notice = MainUIFPAdvanceNotice.New(self.node_list.advance_notice_task_content)

	XUI.AddClickEventListener(self.node_list.BtnCreateTeam, BindTool.Bind(self.CreateTeam, self))       						--创建队伍
    XUI.AddClickEventListener(self.node_list.BtnFindTeam, BindTool.Bind(self.FindTeam, self))           						--寻找队伍
    XUI.AddClickEventListener(self.node_list.team_task_clone_btn, BindTool.Bind(self.OnClickTeamTaskCloneBtn, self))           	--分身
    -- XUI.AddClickEventListener(self.node_list.BindNearTeam, BindTool.Bind(self.FindNearTeam, self))      						--附近队伍

    XUI.AddClickEventListener(self.node_list.btn_change_common_boss, BindTool.Bind(self.SetMonsterListActive, self, 1))			--刷新boss列表
    XUI.AddClickEventListener(self.node_list.btn_change_elite_boss, BindTool.Bind(self.SetMonsterListActive, self, 2))			--刷新boss列表
    XUI.AddClickEventListener(self.node_list.boss_count_add, BindTool.Bind(self.OnBossCountAdd, self))							--增加次数
    XUI.AddClickEventListener(self.node_list.cancle_match, BindTool.Bind(self.CancelTeamMatch, self))         					--取消匹配
    XUI.AddClickEventListener(self.node_list.btn_word_talk, BindTool.Bind(self.OnClickWorldTalk, self))							--世界喊话
    XUI.AddClickEventListener(self.node_list.quit_btn, BindTool.Bind(self.OnClickQuitBtn, self))							    --退出队伍
    XUI.AddClickEventListener(self.node_list.zhaoji_btn, BindTool.Bind(self.OnClickZhaoJiBtn, self))							--召唤队友
    XUI.AddClickEventListener(self.node_list.follow_btn, BindTool.Bind(self.OnClickFollowBtn, self))							--跟随
    XUI.AddClickEventListener(self.node_list["btn_switch_damage"], BindTool.Bind(self.OnClickSwitchOther, self, true))
    -- XUI.AddClickEventListener(self.node_list["xiuxian_btn"], BindTool.Bind(self.OnClickXiuxian, self))							--修仙之旅
    -- XUI.AddClickEventListener(self.node_list["xiuxian_icon_btn"], BindTool.Bind(self.OnClickXiuxianSkill, self))				--修仙之旅技能图标
    -- XUI.AddClickEventListener(self.node_list["xiuxian_task_btn"], BindTool.Bind(self.OnClickXiuxian, self, true))				--仙道试炼
    -- XUI.AddClickEventListener(self.node_list["xiuzhen_task_btn"], BindTool.Bind(self.OnClickXiuZhen, self))						--修仙缘梦

    XUI.AddClickEventListener(self.node_list["btn_top_task_xiuzhen"], BindTool.Bind(self.OnClickXiuZhen, self))						--修仙缘梦
    XUI.AddClickEventListener(self.node_list["btn_top_task_cap_wel"], BindTool.Bind(self.OnClickCapWelBtn, self))					--战力福利
    XUI.AddClickEventListener(self.node_list["btn_top_zhuansheng"], BindTool.Bind(self.OnClickZhuanshengBtn, self))					--转职
    XUI.AddClickEventListener(self.node_list["btn_top_xiuwei_big"], BindTool.Bind(self.OnClickXiuWeiBtn, self))					--修为
    XUI.AddClickEventListener(self.node_list["btn_top_xiuwei"], BindTool.Bind(self.OnClickXiuWeiBtn, self))				--修为

    -- XUI.AddClickEventListener(self.node_list["TaskButton"], BindTool.Bind(self.TaskButtonCallBack, self))
    -- XUI.AddClickEventListener(self.node_list["TeamButton"], BindTool.Bind(self.TeamButtonCallBack, self))
    -- XUI.AddClickEventListener(self.node_list["ShrinkButtons"], BindTool.Bind(self.OnShrinkToggleChange, self))
    -- XUI.AddClickEventListener(self.node_list["BossShrinkBtns"], BindTool.Bind(self.OnClickBossShrinkBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_boss_firstkill"], BindTool.Bind(self.OnClickExchangeBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_lord_everyday_shop"], BindTool.Bind(self.OnClickLoadEveryDayShopBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_duiwu_anniu"], BindTool.Bind(self.OnClickDuiwuBtn, self))
    XUI.AddClickEventListener(self.node_list["xiezhu_tip_btn"], BindTool.Bind(self.OnClickXieZhuTipBtn, self))
	-- XUI.AddClickEventListener(self.node_list["btn_tianxian_pavilion"], BindTool.Bind(self.OnClickTianxian, self))
	-- XUI.AddClickEventListener(self.node_list["tianxian_task_btn"], BindTool.Bind(self.OnClickTianxian, self))
	-- XUI.AddClickEventListener(self.node_list["tianxian_btn"], BindTool.Bind(self.OnClickTianxian, self))
    XUI.AddClickEventListener(self.node_list.xianli_btn_add, BindTool.Bind(self.AddXianli, self))
    XUI.AddClickEventListener(self.node_list.xianli_btn_icon, BindTool.Bind(self.OpenXianliRule, self))
    XUI.AddClickEventListener(self.node_list.boss_nuqi, BindTool.Bind(self.OpenBossNuQiTips, self))
    XUI.AddClickEventListener(self.node_list.btn_bufflist, BindTool.Bind(self.OnClickBuffList, self))
    XUI.AddClickEventListener(self.node_list.btn_custom_action, BindTool.Bind(self.OnClickOpenCustomActionWindow, self))
    XUI.AddClickEventListener(self.node_list.btn_task_lilian, BindTool.Bind(self.OnClickTaskLiLian, self))
    XUI.AddClickEventListener(self.node_list.btn_screen_shot, BindTool.Bind(self.OnClickTaskScreenShot, self))
    XUI.AddClickEventListener(self.node_list.btn_kf_boss_gather, BindTool.Bind(self.OnClickKFBossGather, self))
    -- 装备收集
    XUI.AddClickEventListener(self.node_list.btn_equip_collect, BindTool.Bind(self.OnClickEquipCollect, self))
    --转职装备收集
    --XUI.AddClickEventListener(self.node_list.btn_transfer_equip_collect, BindTool.Bind(self.OnClickTransferEquipCollect, self))
    -- 定制技能
	XUI.AddClickEventListener(self.node_list["btn_customized_rumors"], BindTool.Bind(self.OnClickCustomizedRumors, self))

    -- boss入侵
    XUI.AddClickEventListener(self.node_list.btn_boss_invasion_privilege, BindTool.Bind(self.OnClickBossInvasionPrivilege, self))
    XUI.AddClickEventListener(self.node_list.btn_boss_invasion_quality, BindTool.Bind(self.OnClickBossInvasionQuality, self))

	FunOpen.Instance:RegisterFunUi(FunName.CustomizedRumorsView, self.node_list["btn_customized_rumors"])
	FunOpen.Instance:RegisterFunUi(GuideModuleName.CustomActionView, self.node_list["btn_custom_action"])
    FunOpen.Instance:RegisterFunUi(GuideModuleName.TianShenLiLianView, self.node_list["btn_task_lilian"])
    FunOpen.Instance:RegisterFunUi(GuideModuleName.ScreenShotView, self.node_list["btn_screen_shot"])
	FunOpen.Instance:RegisterFunUi(FunName.CrossTreasureView, self.node_list["btn_task_cross_lingzhu"])

    -- 左侧边按钮
    self.node_list.btn_task_mail.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMailTog, self))
    self.node_list.btn_task_shouna.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickShouNaTog, self))
    self.node_list.btn_task_advance_notice.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTaskAdvanceNotice, self))
    self.node_list.btn_task_cross_lingzhu.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickCrossLingzhu, self))

    local fun_open = FunOpen.Instance
    -- self.node_list["team_apply_redpoint"]:CustomSetActive(SocietyWGData.Instance:GetIsTeamLeader() and SocietyWGData.Instance:GetReqTeamListSize() > 0)
	self.node_list["btn_change_boss_list"]:SetActive(false)

	self.team_state_change = GlobalEventSystem:Bind(SceneEventType.SCENE_ALL_LOAD_COMPLETE,
            BindTool.Bind(self.TeamCellFlush, self))     --队伍成员刷新
    self.team_world_talk = GlobalEventSystem:Bind(TeamWorldTalk.MAIN_WORLD_TALK, BindTool.Bind(self.OnWorldTalkCallBack, self))


    local remind_list = {RemindName.SocietyFriends, RemindName.NewTeam_MyTeam, RemindName.XiuWei,RemindName.XiuWei, RemindName.Daily, RemindName.XiuWeiMainEnter, RemindName.YanYuGe_Noble_Privilege}
    for k,v in pairs(remind_list) do
        RemindManager.Instance:Bind(self.remind_change, v)
    end

    -- 收纳盒子
    for k, v in pairs(TASK_SHOWNA_PANEL_FUNC_CFG) do
        RemindManager.Instance:Bind(self.remind_change, v.remind)
    end


	self:DoAllDelayFunc()
	-- self:FlushCountryBtnShow()
    self:InitOnlineReward()

    -- 日常活动
    if not self.task_today_act_list then
        self.task_today_act_list = AsyncListView.New(TaskToDayActListItemRender, self.node_list.task_today_act_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_see_calendar, BindTool.Bind(self.OnClickSeeCalendarBtn, self))   
end

function MainUIView:Task__ReleaseCallBack()
	self.has_load_callback = false

    if self.team_state_change then
        GlobalEventSystem:UnBind(self.team_state_change)
        self.team_state_change = nil
    end

    if self.team_world_talk then
        GlobalEventSystem:UnBind(self.team_world_talk)
        self.team_world_talk = nil
    end

	if CountDownManager.Instance:HasCountDown("team_world_talk") then
        CountDownManager.Instance:RemoveCountDown("team_world_talk")
    end

	if CountDownManager.Instance:HasCountDown("tianxian_baoge_time") then
        CountDownManager.Instance:RemoveCountDown("tianxian_baoge_time")
    end

    self:RemoveBossNuQiCountDownTime()
	for _, v in pairs(self.task_cell_list) do
        if v then
            v:DeleteMe()
        end
    end
    self.task_cell_list = {}

    for _, v in pairs(self.boss_cell_list) do
        if v then
            v:DeleteMe()
        end
    end
    self.boss_cell_list = {}

    for _, v in pairs(self.elite_cell_list) do
        if v then
            v:DeleteMe()
        end
    end
    self.elite_cell_list = {}

    for _, v in pairs(self.team_cell_list) do
        if v then
            v:DeleteMe()
        end
    end
    self.team_cell_list = {}

    for k, v in pairs(self.boss_reward_t) do
       v:DeleteMe()
    end
    self.boss_reward_t = {}

    if nil ~= self.confirm_team_alert then
		self.confirm_team_alert:DeleteMe()
		self.confirm_team_alert = nil
	end

    if self.task_advance_notice then
		self.task_advance_notice:DeleteMe()
		self.task_advance_notice = nil
	end

    -- if self.xianjie_boss_reward_list then
    --     self.xianjie_boss_reward_list:DeleteMe()
    --     self.xianjie_boss_reward_list = nil
    -- end

	self.task_delay_func = {}
    self.task_func_key_list = {}
    -- self.is_shrink_btn_on = false
    self.is_show_task = true
    self.task_list_active_state = false
    self.task_callback = nil
    self.can_open_team = false
    self.cur_boss_id = nil
    self.btn_word_talk_gray = nil
    self.show_monster = nil
    self.team_invite_state = nil
    self.elite_tip_state = nil
    self.show_add_exp = nil
    self.button_content = nil
    -- self.task_btn_click_callback = nil
    -- self.team_btn_click_callback = nil
    -- 任务
    self.task_list = nil
    self.boss_list = nil
     self.elite_list = nil
    self.list_view = nil
    self.can_open_team = false

    if nil ~= self.zhaoji_cd_timer then
        GlobalTimerQuest:CancelQuest(self.zhaoji_cd_timer)
        self.zhaoji_cd_timer = nil
    end

    self.is_init_gold_change_particle = true
    self:CleanMoneyAllParticleTimer()
    self:CleanAllItemParticleTimer()

    if self.task_today_act_list then
        self.task_today_act_list:DeleteMe()
        self.task_today_act_list = nil
    end

    if self.main_fxs_tip then
        self.main_fxs_tip:DeleteMe()
        self.main_fxs_tip = nil
    end

    if self.main_xxym_tip then
        self.main_xxym_tip:DeleteMe()
        self.main_xxym_tip = nil
    end

    self:ReleaseCrossLingZhuRender()
end

-- 是否是主界面任务面板上的按钮
function MainUIView:GetIsMainuiTaskTopBtn(btn_name)
    return mainui_task_top_btn_list[btn_name] ~= nil
end

-------------------
---   延迟方法   ---
-------------------
--有key的单独处理执行，没key的叠加执行
function MainUIView:AddDelayLoadFunc(fun, key)
    if key then
        if not self.has_load_callback then
            for i, v in pairs(self.task_func_key_list) do
                if v == key then
                    table.remove(self.task_func_key_list, i)
                    break
                end
            end

            table.insert(self.task_func_key_list, key)
        end

        self.task_delay_func[key] = fun
        return
    end

    table.insert(self.task_delay_func, fun)
end

function MainUIView:HasDelayFunc(key)
    return self.task_delay_func[key] ~= nil
end

function MainUIView:DoOneDelayFunc(key)
    if self.task_delay_func[key] then
        self.task_delay_func[key]()
        self.task_delay_func[key] = nil
    end
end

function MainUIView:DoAllDelayFunc()
    for _, v in ipairs(self.task_delay_func) do
        v()
    end

    for i, v in ipairs(self.task_func_key_list) do
        self:DoOneDelayFunc(v)
    end

    self.task_delay_func = {}
    self.task_func_key_list = {}
end

-------------------
--- 全局事件监听 ---
-------------------
-- 显示中的任务改变
function MainUIView:OnShowTaskChange(data, is_boss_list, flush_time)
    -- self:RefreshTaskListRedPoint()
    if not self.has_load_callback and is_boss_list then
        if data then
            self:AddDelayLoadFunc(BindTool.Bind(self.OnShowTaskChange, self, data, is_boss_list), "task_change")
        end
        return
    end

    if is_boss_list then
        local scene_type = Scene.Instance:GetSceneType()
        if self.node_list.person_des_container then
            self.node_list.person_des_container:SetActive(scene_type == SceneType.PERSON_BOSS)
        end
        self.node_list.boss_progress_des.text.text = Language.XianJieBoss.BossTimes
        self:FlushXianli()
        self:DoSingelBoss(data, scene_type, flush_time)
        self:ShowElite(scene_type)

        if scene_type == SceneType.PERSON_BOSS then
            if not IsEmptyTable(data) then
                self.person_data = data
            end
            self:DoPersonlBoss()
        elseif scene_type == SceneType.WorldBoss then
            self:DoWorldBoss()
        elseif scene_type == SceneType.VIP_BOSS then
            self:DoVipBoss()
        elseif scene_type == SceneType.XianJie_Boss then
            --self:DoXianJieBoss()
        elseif scene_type == SceneType.KF_BOSS then
            self:DoKFBoss()
        else
            self.node_list.boss_count_add_eff:SetActive(false)
        end
        self:ShowBossCount(scene_type)
    else
        --新增任务标记
        local old_task_data = {}
        for k,v in pairs(self.task_data) do
           old_task_data[v.task_id] = 1
        end

        self.task_data = data or self.task_data
        if next(old_task_data) then
            for k,v in pairs(self.task_data) do
                if old_task_data[v.task_id] == nil then
                    NEW_TASK_T[v.task_id] = Status.NowTime
                end
            end
        end

        local role_level = RoleWGData.Instance:GetRoleLevel()
        local is_inster_zhuan = false
        local had_gd_type = TransFerWGData.Instance:GetGodAndDemonsType() ~= -1
        if had_gd_type then
            -- 判断主线是否可做 转职任务特殊处理插入
            for key, value in pairs(self.task_data) do
                if value.task_type == GameEnum.TASK_TYPE_ZHU then
                    if role_level < value.min_level then
                        local task_cfg, task_data = TransFerWGData.Instance:GetNotCompleteTask()
                        if task_cfg then
                            local show_cfg = TransFerWGData.Instance:GetStageCfgByLevelAndStage(task_cfg.zhuanzhi_level, task_cfg.tab_type)
                            if not IsEmptyTable(show_cfg) and role_level >= show_cfg.open_level then
                                local temp_data = {}
                                temp_data.task_type = GameEnum.TASK_TYPE_ZHUAN
                                temp_data.task_cfg = task_cfg
                                temp_data.task_data = task_data
                                temp_data.task_id = TransFerWGData.Instance:GetOtherCfgByKey("fake_taskid")
                                temp_data.min_level = show_cfg.open_level
                                table.insert(self.task_data, key+1, temp_data)
                                is_inster_zhuan = true
                            end
                        end
                    end

                    break
                end
            end

            -- 或者可以做副本任务的时候把任务插入
            if not is_inster_zhuan then
                local task_id = TransFerWGData.Instance:GetFbTask()
                if task_id ~= 0 then
                    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
                    if task_cfg then
                        local temp_data = {}
                        temp_data.task_type = GameEnum.TASK_TYPE_ZHUAN
                        temp_data.task_cfg = task_cfg
                        -- temp_data.task_data = task_data
                        temp_data.task_id = TransFerWGData.Instance:GetOtherCfgByKey("fake_taskid")
                        temp_data.min_level = 1
                        table.insert(self.task_data, 2, temp_data)
                        is_inster_zhuan = true
                    end
                end
            end
        end

        -- 接取赏金任务后，需要将赏金任务提到前面
        if TaskWGData.Instance:IsAcceptBounty() then
            table.sort(self.task_data, function (a, b)
                if a.task_type == GameEnum.TASK_TYPE_RI and b.task_type ~= GameEnum.TASK_TYPE_RI then
                    return true
                end

                return false
            end)
        end

        if self.task_list then
            if TaskWGData.Instance:RollFlag() then
                self.task_list.scroller:ReloadData(0)
                TaskWGData.Instance:RollFlag(false)
            end
            self.task_list.scroller:RefreshAndReloadActiveCellViews(true)
        end
    end
end

-- 队伍信息改变
function MainUIView:OnTeamChange()
    if self.has_load_callback == false then
        self:AddDelayLoadFunc(BindTool.Bind(self.OnTeamChange, self), "team")
        return
    end

    if not self.list_view then
        return
    end

    self.team_list = {}
    local member_list, is_team, show_apply_red_point, is_qingyuan, online_member_count
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        member_list = CrossTeamWGData.Instance:GetTeamMemberList()
        is_team = CrossTeamWGData.Instance:GetIsInTeam() == 1
        is_qingyuan = false
        show_apply_red_point = CrossTeamWGData.Instance:GetIsTeamLeader() and CrossTeamWGData.Instance:GetReqTeamListSize() > 0
        local member_real_count = CrossTeamWGData.Instance:GetTeamMemberCount()
        if member_real_count < 3 then
            local data = {}
            data.is_empty_data = true
            table.insert(member_list, data)
        end
        online_member_count = member_real_count
    else
        member_list = SocietyWGData.Instance:GetTeamMemberList()
        local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        local member_real_count = #member_list
        is_qingyuan = team_type == GoalTeamType.QingYuanFb        -- 情缘组队特殊处理
        for k,v in pairs(member_list) do
            if v.is_leader == 1 then
                local temp = v
                table.remove(member_list, k)
                table.insert(member_list, 1, temp)
                break
            end
        end

        if (member_real_count < 5 and not is_qingyuan) or (member_real_count < QingYuanLimitCount and is_qingyuan) then
            local data = {}
            data.is_empty_data = true
            table.insert(member_list, data)
        end

        is_team = SocietyWGData.Instance:GetIsInTeam() == 1 --not IsEmptyTable(self.team_list)
        show_apply_red_point = SocietyWGData.Instance:GetIsTeamLeader() == 1 and SocietyWGData.Instance:GetReqTeamListSize() > 0
        online_member_count = 0
        for k,v in pairs(member_list) do
            if v.is_online == 1 then
                online_member_count = online_member_count + 1
            end
        end
    end

    local is_team_leader = 1 == SocietyWGData.Instance:GetIsTeamLeader()

    self.team_list = member_list
    self.list_view:SetActive(is_team)
    self.show_add_exp:SetActive(is_team)
    self.button_content:SetActive(not is_team)

    if self.btn_is_active then
        self.node_list.zhaoji_btn:SetActive(is_team_leader)
        self.node_list.follow_btn:SetActive(not is_team_leader)
    end

    self.list_view.scroller:ReloadData(0)
    -- self.node_list["team_apply_redpoint"]:CustomSetActive()
    local count, index_list = NewTeamWGData.Instance:GetExpAdd()
    if self.btn_word_talk_gray then
        self.btn_word_talk_gray = nil
    end

    self.node_list.btn_word_talk_txt.text.text = ""

    local str = Language.Task.task_text4
    self:ChangeTeamBtnName(str, true)
    self:FlushTeamInvite(self.team_invite_state)
    self:Flush(0, "FlushCurSpeak")
    self:FlushLocationPanel()
end

-- 更换任务栏显示
function MainUIView:ShowTaskTypeActive(bool, need_tween)
    if bool == nil then
    	return
    end

    self.is_show_task_list = not bool
    if not need_tween then
        self:SetTaskListActive(not bool)
        if self.is_show_content then
            self:SetOtherContentsActive(bool)
            self:SetBossListActive(false)
        else
            self:SetOtherContentsActive(false)
            self:SetBossListActive(bool)
        end

        if not bool then
            self:SetEliteTip(false)
        end
        self:RefreshLeftPanel()
    else
	    if self.left_panel_shrink_tween then
	        self.left_panel_shrink_tween:Kill()
	        self.left_panel_shrink_tween = nil
	    end

    	local task_tween_time, other_tween_time, boss_tween_time = 0.6, 0.3, 0.3
        self.left_panel_shrink_tween = DG.Tweening.DOTween.Sequence()
        if bool then
            if self.is_show_content then
                -- self.node_list.OtherContent.rect.anchoredPosition = u3dpool.vec2(-414, 0)
                self:SetOtherContentsActive(true)
                --self:SetBossListActive(true)
                self.left_panel_shrink_tween:Append(self.node_list.TaskMask.rect:DOAnchorPosX(-1124, task_tween_time)):OnComplete(function()
                    self:SetTaskListActive(false)
                end)
                -- self.left_panel_shrink_tween:Append(self.node_list.OtherContent.rect:DOAnchorPosX(62 , other_tween_time))
            else
                self.node_list.BossList.rect.anchoredPosition = u3dpool.vec2(-414, 0)
                self:SetBossListActive(true)
                self:SetOtherContentsActive(false)
                self.left_panel_shrink_tween:Append(self.node_list.TaskMask.rect:DOAnchorPosX(-1124, task_tween_time)):
                OnComplete(function()
                    self:SetTaskListActive(false)
                end)

                self.left_panel_shrink_tween:Append(self.node_list.BossList.rect:DOAnchorPosX(0, boss_tween_time))
            end
        else
            if self.is_show_content then -- -155到512 y =0
                self.node_list.TaskMask.rect.anchoredPosition = u3dpool.vec2(-1124, 0)
                self:SetTaskListActive(true)
                -- self.left_panel_shrink_tween:Append(self.node_list.OtherContent.rect:DOAnchorPosX(-414, other_tween_time)):
                -- OnComplete(function()
                --     self:SetOtherContentsActive(false)
                --     self:SetBossListActive(false)
                -- end)

                self.left_panel_shrink_tween:Append(self.node_list.TaskMask.rect:DOAnchorPosX(0, task_tween_time))
            else
                self.node_list.TaskMask.rect.anchoredPosition = u3dpool.vec2(-1124, 0)
                self:SetTaskListActive(not bool)
                self.left_panel_shrink_tween:Append(self.node_list.BossList.rect:DOAnchorPosX(-414, boss_tween_time)):OnComplete(function()
                    self:SetBossListActive(false)
                end)

                self.left_panel_shrink_tween:Append(self.node_list.TaskMask.rect:DOAnchorPosX(0, task_tween_time))
            end
        end

        self.left_panel_shrink_tween:OnComplete(function()
            self:RefreshLeftPanel()
        end)
    end
end

-- 切换boss列表
function MainUIView:OnClickSwitchOther(status)
    if self.node_list["BossList"] then
        self:SetOtherContentsActive(status)
        local need_refresh = not status and not (self.boss_list and self.boss_list:GetActive())
        self.is_show_content = status
        if self.is_show_task_list then
            self:ShowTaskTypeActive(false)
        end

        self.node_list["BossList"]:SetActive(not status)
        local is_single = self:IsSingleBoss()
        local has_demage = BossAssistWGData.Instance and BossAssistWGData.Instance:HasDemageHurt()
        self.node_list["btn_switch_damage"]:SetActive(not is_single and not status and has_demage)
        if need_refresh then
            self.node_list.btn_change_common_boss.toggle.isOn = true
        end

        self:RefreshLeftPanel()
    end
end

-- 设置boss提示显示
function MainUIView:SetBossTips(is_show)
    if  self.node_list.boss_tips_group then
        local scene_type = Scene.Instance:GetSceneType()
        local level_limit = BossWGData.Instance:GetOtherCfgByKey("vip_boss_tips_level_limit")
        local role_level = RoleWGData.Instance:GetRoleLevel()
        self.node_list.boss_tips_group:CustomSetActive(is_show and scene_type == SceneType.VIP_BOSS and role_level<level_limit)
    end
end

-- 金钱发生变化之前
--主界面获得绑定仙玉播放特效
local SHOW_EFFECT_ENUM =
{
    ["GOLD"]   = {type = GameEnum.NEW_MONEY_BAR.XIANYU, end_obj_name = "Gold"},
    ["BIND_GOLD"] = {type = GameEnum.NEW_MONEY_BAR.BIND_XIANYU, end_obj_name = "BindGold"},
    ["RECHARGE_VOLUME"] = {type = GameEnum.NEW_MONEY_BAR.RECHARGE_VOLUME, end_obj_name = "volume"},
}

function MainUIView:MainTaskBindGoldChangeFunc(protocol)
    if protocol.reason_type == COST_REASON_TYPE.GOLD_ADD_FORTUNE_CAT then
        return
    end

    local main_role_vo = RoleWGData.Instance:GetRoleInfo()
    if self.is_init_gold_change_particle then 
        self.is_init_gold_change_particle = false
        return
    end

    local recharge_volume = protocol.gold_virtual and protocol.gold_virtual[3]
    if nil ~= protocol.gold or nil ~= protocol.bind_gold or nil ~= recharge_volume then
        local is_add_gold = (protocol.gold or 0) > (main_role_vo["gold"] or 0)
        local is_add_bind_gold =  (protocol.bind_gold or 0) > (main_role_vo["bind_gold"] or 0)
        local is_add_recharge_volume =  (recharge_volume or 0) > (main_role_vo["recharge_volume"] or 0)
        if is_add_gold or is_add_bind_gold or is_add_recharge_volume then
            local effect_enum = "GOLD"
            if is_add_bind_gold then
                effect_enum = "BIND_GOLD"
            elseif is_add_recharge_volume then
                effect_enum = "RECHARGE_VOLUME"
            end

            local use_type_cfg = SHOW_EFFECT_ENUM[effect_enum]
            if nil == use_type_cfg then
                return
            end

            -- 2025.8.1 策划要求日月修行打开财神爷不飘特效
            local is_open_rv_view = ViewManager.Instance:IsOpen(GuideModuleName.RechargeVolumeView)
            local scene_type = Scene.Instance:GetSceneType()
            if use_type_cfg.type == GameEnum.NEW_MONEY_BAR.RECHARGE_VOLUME and is_open_rv_view and scene_type == SceneType.LingHunGuangChang then
                return
            end

            local is_view_had_money_bar = false
            local top_view = ViewManager.Instance:GetNormalLayerTopView()
            local money_bar = nil
            if top_view then
                money_bar = top_view.money_bar
                is_view_had_money_bar = money_bar ~= nil
            end
            local end_obj = nil
            if is_view_had_money_bar then 
                end_obj = money_bar.node_list[use_type_cfg.end_obj_name]
            end

            MainuiWGCtrl.Instance:CleanMarkerTimer()
            self.money_timer_count = self.money_timer_count + 1
            local count = self.money_timer_count
            self.money_p_timer[count] = GlobalTimerQuest:AddDelayTimer(function ()
                self:PlayEffectByRechargeSecond(nil, nil, end_obj, 1, use_type_cfg.type, 2)
                self:CleanMoneyTargetParticleTimer(count)
            end, 0.3)
        end
    end
end

function MainUIView:CleanMoneyTargetParticleTimer(idx)
	if not IsEmptyTable(self.money_p_timer) and idx then
        if self.money_p_timer[idx] then
            GlobalTimerQuest:CancelQuest(self.money_p_timer[idx])
            self.money_p_timer[idx] = nil
        end
	end
end

function MainUIView:CleanMoneyAllParticleTimer()
	if not IsEmptyTable(self.money_p_timer) then
        for key, value in pairs(self.money_p_timer) do
            GlobalTimerQuest:CancelQuest(value)
        end
        self.money_p_timer = {}
	end
    self.money_timer_count = 0
end

local SHOW_EFFECT_USE_TYPE =
{
    [Item_Use_Type.BIND_XIANYU] = {type = GameEnum.NEW_MONEY_BAR.BIND_XIANYU, end_obj_name = "BindGold"},
    [Item_Use_Type.XianYu2] = {type = GameEnum.NEW_MONEY_BAR.XIANYU, end_obj_name = "Gold"},
}
function MainUIView:MainTaskItemChangeGetParticle(item_id, index, reason, put_reason, old_num, new_num)
    local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return
	end

    local use_type_cfg = SHOW_EFFECT_USE_TYPE[item_cfg.use_type]
	if (not new_num or not old_num or new_num >= old_num) or nil == use_type_cfg then
		return
	end

    local is_view_had_money_bar = false
    local top_view = ViewManager.Instance:GetNormalLayerTopView()
    local money_bar = nil
    if top_view then
        money_bar = top_view.money_bar
        is_view_had_money_bar = money_bar ~= nil
    end
    local end_obj = nil
    if is_view_had_money_bar then 
        end_obj = money_bar.node_list[use_type_cfg.end_obj_name]
    end

    self.item_timer_count = self.item_timer_count + 1
    local count = self.item_timer_count
    self.item_particle_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self:PlayEffectByRechargeSecond(nil, nil, end_obj, 1, use_type_cfg.type, 2)
        self:CleanItemTargetParticleTimer(count)
    end, 0.5)
end

function MainUIView:CleanItemTargetParticleTimer(idx)
	if not IsEmptyTable(self.item_p_timer) and idx then
        if self.item_p_timer[idx] then
            GlobalTimerQuest:CancelQuest(self.item_p_timer[idx])
            self.item_p_timer[idx] = nil
        end
	end
end

function MainUIView:CleanAllItemParticleTimer()
    if not IsEmptyTable(self.item_particle_timer) then
        for key, value in pairs(self.item_particle_timer) do
            GlobalTimerQuest:CancelQuest(value)
        end
        self.item_particle_timer = {}
	end
    self.item_timer_count = 0
end

function MainUIView:PlayEffectByRechargeSecond(view_name, start_obj, end_obj, effect_count, money_type, duration)
    duration = duration or 0.5
    view_name = view_name or "MainTaskGoldGetParticle"
    TipWGCtrl.Instance:DestroyFlyEffectByViewName(view_name)
    if self.node_list.role_bag_icon then
        start_obj = start_obj 
        end_obj = end_obj
        effect_count = effect_count or 20
        local eff = MONEY_BAR_EFFECT[money_type]
        if nil == eff or nil == eff.FLY then
        	return
        end

        self:ChangeMoneyFlyToBagPos()

        if money_type == GameEnum.NEW_MONEY_BAR.RECHARGE_VOLUME then
            self:ChangeRechargeVolumeFlyToPos()
        end

        local bundle, asset = ResPath.GetUIEffect(eff.FLY)
        TipWGCtrl.Instance:ShowFlyEffectManager(
            view_name, bundle, asset, 
            start_obj, end_obj, DG.Tweening.Ease.OutCubic, duration,
            nil, nil, effect_count, 150, nil,
            false, true, self.end_eff_pos, nil, self.start_eff_pos)
    end
end


-- 场景加载结束
function MainUIView:OnThisSceneLoaded()
    if not self.is_mainui_view_load then
        self:AddInitCallBack(nil, BindTool.Bind(self.OnThisSceneLoaded, self))
    else
        -- self:RefreshTaskListRedPoint()
        local is_show_content
        local scene_type = Scene.Instance:GetSceneType()
        self.is_show_boss_list, is_show_content = MainuiWGData.Instance:NeedShowBossShrinkBtn()
        -- self.node_list.BossShrinkBtns:SetActive(self.is_show_boss_list)
        -- self:SetShrinkButtonsActive(not self.is_show_boss_list)
        self:CancelBossShrinkTween()
        self.node_list["btn_boss_firstkill"]:SetActive(false) -- scene_type == SceneType.VIP_BOSS or scene_type == SceneType.WorldBoss -- 暂时不需要

        local lord_everyday_shop_isopen = FunOpen.Instance:GetFunIsOpened(FunName.FashionExchangeShopView)
        self.node_list["btn_lord_everyday_shop"]:SetActive(false) -- lord_everyday_shop_isopen and (scene_type == SceneType.VIP_BOSS or scene_type == SceneType.WorldBoss) -- 暂时不需要

        if scene_type == SceneType.Common then
            self:SetAdvanceNoticeBtnState(true, true)
        else
            self:SetAdvanceNoticeBtnState(true, false)
        end

        self.node_list.boss_nuqi:SetActive(scene_type == SceneType.XianJie_Boss or scene_type == SceneType.DABAO_BOSS)
        self.node_list.btn_kf_boss_gather:SetActive(scene_type == SceneType.KF_BOSS)

        self:FlushXianli()
        self.is_can_flush_advance_notice = true
        self:FlushTaskAdvanceNotice()

        local is_team_leader = 1 == SocietyWGData.Instance:GetIsTeamLeader()
        self.btn_is_active = scene_type ~= SceneType.XianJie_Boss
        self.node_list.zhaoji_btn:SetActive(self.btn_is_active and is_team_leader)
        self.node_list.follow_btn:SetActive(self.btn_is_active and not is_team_leader)

        self.node_list.btn_boss_invasion_privilege:CustomSetActive(scene_type == SceneType.BOSS_INVASION)
        self.node_list.btn_boss_invasion_quality:CustomSetActive(scene_type == SceneType.BOSS_INVASION)

        if self.boss_invasion_privilege_tween_shake then
            self.boss_invasion_privilege_tween_shake:Kill()
            self.boss_invasion_privilege_tween_shake = nil
        end
        
        if scene_type == SceneType.BOSS_INVASION and not BOSSInvasionWGData.Instance:IsGetPrivilege() then
            self.boss_invasion_privilege_tween_shake = DG.Tweening.DOTween.Sequence()
            UITween.ShakeAnimi(self.node_list["btn_boss_invasion_privilege"].transform, self.boss_invasion_privilege_tween_shake, 1)
        end

        self:UpdateMainTaskTodayActTip()
    end
end

-- 右上角按钮点击事件
function MainUIView:OnTopPanelAniCallBack(is_on)

end




-------------------
---    列表     ---
-------------------
function MainUIView:SetTaskRootNodeActive(is_active)
    if self.node_list.task_root_view then
        self.node_list.task_root_view:SetActive(is_active)
    end
end

-- function MainUIView:SetTaskPanelAndTeamActive(enable)
-- 	local task = self.node_list["TaskParent"]
-- 	if nil == task then
-- 		return
-- 	end

-- 	task:SetActive(enable)
-- end

function MainUIView:GetTaskOtherContent()
    -- return self.node_list.OtherContent
    return self.node_list.other_task_panel
end

function MainUIView:GetTaskShouNaContent()
    return self.node_list.shouna_task_content
end

-- function MainUIView:OnShrinkToggleChange(isOn)
--     self.is_shrink_btn_on = isOn
--     GlobalEventSystem:Fire(MainUIEventType.SHOW_OR_HIDE_TASK_BUTTON, isOn)

--     if isOn then
--         UITween.CleanAlphaShow("show_with_task_parent_move")
--         UITween.FakeHideShow(self.node_list["show_with_task_parent_move"])
--     else
--         UITween.AlphaShow("show_with_task_parent_move", self.node_list["show_with_task_parent_move"])
--     end
-- end

-- function MainUIView:GetShrinkBtnState()
--     return self.is_shrink_btn_on or false
-- end

--刷新左侧任务列表,伤害列表, boss列表,组队列表状态
function MainUIView:RefreshLeftPanel()
    -- print_error(" self.is_show_team ", self.is_show_team , "self.is_show_task_list", self.is_show_task_list,"self.is_show_content", self.is_show_content)
    -- if self.is_show_team then
    --     self:SetBossListActive(false)
    --     self:SetOtherContentsActive(false)
    --     self.is_show_boss_list = not self.is_show_task_list
    -- else
    --     if self.is_show_task_list then
    --         self:SetTaskListActive(true)
    --         self:SetBossListActive(false)
    --         self:SetOtherContentsActive(false)
    --         self.is_show_boss_list = false
    --     else
    --         self.is_show_boss_list = true
    --         self:SetTaskListActive(false)
    --         self:SetBossListActive(not self.is_show_content)
    --         if not self.is_show_content then
    --             self:RefreshBossList()
    --         end

    --         self:SetOtherContentsActive(self.is_show_content)
    --     end
    -- end

    self.is_show_boss_list = not self.is_show_task_list

    if self.node_list.btn_task_normal then
        self.node_list.btn_task_normal:CustomSetActive(self.is_show_boss_list or self.is_show_content)
        if self.is_show_boss_list or self.is_show_content then
            self.node_list.btn_task_normal:CustomSetActive(true)
            self.node_list.btn_task_normal.toggle.isOn = true
        else
            self.node_list.btn_task_normal:CustomSetActive(false)
            self.node_list.btn_task_normal.toggle.isOn = false
        end
    end

    if self.is_show_task_list then
        self:SetTaskListActive(true)
        self:SetBossListActive(false)
        self:SetOtherContentsActive(false)
    else
        self:SetTaskListActive(false)
        self:SetBossListActive(not self.is_show_content)
        if not self.is_show_content then
            self:RefreshBossList()
        end

        self:SetOtherContentsActive(self.is_show_content)
    end

    -- local text = ""
    -- local scene_type = Scene.Instance:GetSceneType()
    -- if scene_type ~= SceneType.Kf_PvP_Prepare and scene_type ~= SceneType.Kf_Honorhalls then --这些场景自己修改
        -- if BossWGData.IsBossScene(scene_type) then --boss场景处理
        --     if self.is_show_content then
        --         text = Language.Common.Rank
        --         if not self.is_show_boss_list then
        --             text = Language.Task.task_text
        --         end
        --     else
        --         if self.is_show_boss_list then
        --             text = Language.Task.Boss_text
        --         else
        --             text = Language.Task.task_text
        --         end
        --     end
        -- elseif SceneType.HIGH_TEAM_EQUIP_FB == scene_type then --幻灵境处理
        --     text = Language.FuBenPanel.ViewNameYuanGuXianDian
        -- elseif scene_type == SceneType.TianShen3v3Prepare then
        --     text = Language.TianShen3v3.SceneLeftPanelTabName
        -- elseif scene_type == SceneType.Wujinjitan or scene_type == SceneType.LingHunGuangChang
        -- 	or scene_type == SceneType.ManHuangGuDian_FB then
        --     text = Scene.Instance:GetSceneName()
        -- else
        --     text = Language.Task.task_text
        -- end
        -- if self.node_list and self.node_list["text_word_task_1"] and self.node_list["text_word_task_2"] then
        --     self:ChangeTaskBtnName(text)
        -- end
    -- end

    self:CancelBossShrinkTween() --重置位置
end

-- 显示隐藏收起按钮
-- function MainUIView:SetShrinkButtonsActive(enable)
--     self.node_list.ShrinkButtons:SetActive(enable)
-- end

-- function MainUIView:GetPlayTaskButtonStatus()
--     if self.node_list["ShrinkButtons"] then
--         return self.node_list["ShrinkButtons"].toggle.isOn
--     end

--     return false
-- end

-- -- 隐藏任务面板（动画）
-- function MainUIView:DoHideTaskParent()
--     if self.node_list.TaskParent then
--         self.node_list.TaskParent.rect:DOAnchorPos(Vector2(self.task_parent_pos.x - 800, self.task_parent_pos.y), 0.5)
--     end
-- end

-- -- 显示任务面板（动画）
-- function MainUIView:DoShowTaskParent()
--     if self.node_list.TaskParent then
--         self.node_list.TaskParent.rect:DOAnchorPos(self.task_parent_pos, 0.5)
--     end
-- end

--显示隐藏任务栏 进入副本时使用
-- function MainUIView:SetTaskActive(enable)
--     if self.is_show_task ~= enable then
--         self.is_show_task = enable
--         self.node_list.TaskParent:SetActive(enable)
--         -- self:SetShrinkButtonsActive(enable)
--     end
-- end

--播放按钮点击动画
-- function MainUIView:PlayTaskButtonTween(enable, is_reversal)
--     if not self.has_load_callback then
--         self:AddDelayLoadFunc(BindTool.Bind(self.PlayTaskButtonTween, self, enable), "btn_tween")
--         return
--     end

--     if not is_reversal then
--         if self.task_button_tween_reversal_timer ~= nil then
--             GlobalTimerQuest:CancelQuest(self.task_button_tween_reversal_timer)
--             self.task_button_tween_reversal_timer = nil
--         end
--     end

--     -- self.node_list["ShrinkButtons"].toggle.isOn = enable
-- end

-- function MainUIView:SetTaskButtonTrue()
--     self.node_list.TaskButton.toggle.isOn = true
--     self.node_list.TeamButton.toggle.isOn = false
-- end

--外部调用
function MainUIView:SetTaskContents(enable)
    self.is_show_task_list = enable
    self:RefreshLeftPanel()
end

--外部调用
function MainUIView:SetOtherContents(enable)
    self.is_show_content = enable
    self:RefreshLeftPanel()
end

--内部调用
function MainUIView:SetOtherContentsActive(enable)
    -- if self.node_list.OtherContent then
    --     self.node_list.OtherContent:SetActive(enable)
    -- end

    if self.node_list.other_task_panel then
        self.node_list.other_task_panel:SetActive(enable)
    end
end

--任务栏按钮点击
-- function MainUIView:TaskButtonCallBack(isOn)
--     if self.task_btn_click_callback then
--         self.task_btn_click_callback(isOn)
--     end

--     self.is_show_team = not isOn
--     self:RefreshLeftPanel()
-- end

--任务按钮点击回调回调
-- function MainUIView:SetTaskCallBack(callback)
--     self.task_btn_click_callback = callback
-- end

-- --队伍按钮点击回调
-- function MainUIView:SetTeamCallBack(callback)
--     self.team_btn_click_callback = callback
-- end

-- --设置显示伤害面板，要记录状态，
-- function MainUIView:SetLeftPanelShowContent()
--     if not self.old_status_tb then
--         self.old_status_tb = {}
--     end

--     local show_task_list = self.is_show_task_list
--     if Scene.Instance:GetSceneType() == SceneType.KF_BOSS then --重新登录因为执行顺序问题会导致存储的变量不对
--         show_task_list = false
--     end

--     self.old_status_tb = {show_task_list = show_task_list}
--     self.is_show_task_list = false
--     self.is_show_content = true
--     self:RefreshLeftPanel()
-- end

-- --隐藏伤害面板，设置成之前的状态
-- function MainUIView:RevertLeftPanelStatus()
--     if not IsEmptyTable(self.old_status_tb) then
--         self.is_show_task_list = self.old_status_tb.show_task_list
--     else
--         self.is_show_task_list = false
--     end

--     self.is_show_content = false
--     self:RefreshLeftPanel()
--     self.old_status_tb = nil
-- end

-------------------
---    任务     ---
-------------------
-- 刷新任务List
function MainUIView:RefresTaskListView(cell, data_index, cell_index)
    data_index = data_index + 1
    local icon_cell = self.task_cell_list[cell]
    if icon_cell == nil then
        icon_cell = TaskFollowTask.New(cell.gameObject)
        icon_cell:SetClickCallBack(BindTool.Bind(self.DoTask, self))
        self.task_cell_list[cell] = icon_cell
    end

    local data = self.task_data[data_index]
    if data == nil then
        return
    end

    icon_cell:SetIndex(data_index)
    local task_status = TaskWGData.Instance:GetTaskStatus(data.task_id)
    if (data.task_type == GameEnum.TASK_TYPE_RI or
        data.task_type == GameEnum.TASK_TYPE_MENG or
        data.task_type == GameEnum.TASK_TYPE_ZHUAN) and
        "" == data.commit_npc and task_status == GameEnum.TASK_STATUS_COMMIT and
        (icon_cell.data and icon_cell.data.task_type == data.task_type) then
        -- 这里的任务不需要更新,服务器己经做了自动提交任务
        return
    end

    icon_cell:SetData(data)
end

function MainUIView:FlushTaskView()
    if self.task_list == nil then
        return
    end
    if IsNil(self.task_list.gameObject) then
        return
    end

    if not self.task_list.gameObject.activeInHierarchy then
        return
    end

    self.task_list.scroller:RefreshActiveCellViews()
end

-- 任务格子点击回调
function MainUIView:DoTask(task_id, task_status, fly_shoes, is_handle)
    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
    if nil == task_cfg then
        return
    end

    if IS_ON_CROSSSERVER then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
        return
    end

    TaskWGData.Instance.shang_rand_monst_id = 0
    local main_role = Scene.Instance:GetMainRole()
    if main_role:IsJump() then
        return
    end

    if main_role:CantPlayerDoMoveIgnoreHusong(task_cfg.task_type , true) then
        return
    end

    if TaskWGCtrl.Instance:IsFly() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FlyShoeCanNotDo)
        return
    end

    -- for _, v in pairs(self.task_cell_list) do
    --     if v:GetActive() and v:IsZhuTask() then
    --         v:SetGuideDialogVis(false)
    --         break
    --     end
    -- end

    MainUIView.LAST_DO_TASK_TIME = 0
    if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_0 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_2 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_4 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_6 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_11 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_14 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_21 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_23 then
        GuajiWGCtrl.Instance:StopGuaji()
    end

    if task_cfg.task_type == GameEnum.TASK_TYPE_RI then -- 改成悬赏了，这里不用切换跑日常模式
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, is_handle)
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_ZHU)
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_HU then
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_HU)
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)
        if TaskGuide.Instance:NoviceCheckTask() then
            return
        end
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_MENG)
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)

         -- 该判断会导致仙盟任务点击没反应，先屏蔽
        -- if TaskGuide.Instance:NoviceCheckTask() then
        --     return
        -- end
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_ZHUAN, true)
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)
    elseif task_cfg.task_id == GameEnum.TASK_TYPE_GUILD_BUILD then
        --这个只是为了打开界面客户端做得假任务展示
        if task_id == GUILD_BUILD_TASK_OTHER_TYPE.CLIENT_SHOW_TASK_ID then
            FunOpen.Instance:OpenViewByName(GuideModuleName.guild_task)
            return
        end

        --此状态比较特殊，跟任务状态偶尔不同
        local guild_task_states = GuildWGData.Instance:GetGuildBuildTaskStates(task_id)
        if guild_task_states == GameEnum.TASK_STATUS_COMMIT then
            FunOpen.Instance:OpenViewByName(GuideModuleName.guild_task)
            return
        end
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_GUILD_BUILD)
        -- TaskGuide.Instance:SpecialConditions(true, 5)
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)
    elseif task_cfg.task_id == GameEnum.ASSIGN_TASK_ID then --委托假任务点击
        FunOpen.Instance:OpenViewByName(GuideModuleName.AssignmentView)
        return
    end

    local task_link_type = ""
    if RoleWGData.Instance.role_vo.level < task_cfg.min_level then
        task_link_type = "level_up"
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_GUAJI then
        task_link_type = "guaji"
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_LING then
        task_link_type = "ling"
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_TREASURE_MAP then
        task_link_type = "treasure_map"
    else
        task_link_type = "common"
        local curr_task_is_other_first = TaskWGData.Instance:CurrTaskIsOtherFirst()
        if not curr_task_is_other_first then
            TaskWGData.Instance:CurrTaskId(task_id)
        end
    end

    if task_link_type == "common" then
        TaskWGCtrl.Instance:OperateFollowTask(task_cfg, fly_shoes)
    elseif task_link_type == "level_up" then
        TaskWGCtrl.Instance:OpenTaskGainExpTipView()
    elseif task_link_type == "guaji" then
        Scene.Instance:GetSceneLogic():FlyToPos(task_cfg.x, task_cfg.y, task_cfg.scene_id, SceneObjType.Monster, false)
    elseif task_link_type == "ling" then
        local commit_npc = task_cfg.commit_npc
        TaskWGCtrl.Instance:SendFlyByShoe(commit_npc.scene, commit_npc.x, commit_npc.y)
    end
end

function MainUIView:ResetTaskPanel()
    if not self.is_show_task then
        self:AddDelayLoadFunc(BindTool.Bind(self.ResetTaskPanel, self), "reset")
        return
    end

    self.is_show_content = false
    self:SetTaskListActive(true)
    self:SetBossListActive(false)
    -- self:ChangeTaskBtnName(Language.Task.task_text2)
    self:SetBtnLevel(false)
    self:RefreshLeftPanel()
end

-- 刷新任务列表红点
-- function MainUIView:RefreshTaskListRedPoint()
--     if not self.node_list.task_list_red then
--         return
--     end

--     local had_get = false
--     for k, v in pairs(self.task_data) do
--         local task_status = TaskWGData.Instance:GetTaskStatus(v.task_id)
--         if task_status == GameEnum.TASK_STATUS_COMMIT or task_status == GameEnum.TASK_STATUS_FINISH then
--             had_get = true
--             break
--         end
--     end

--     local is_open_act = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XIUZHEN_ROAD)
--     if is_open_act and XiuZhenRoadWGData.Instance:IsShowXiuZhenRoadRedPoint() == 1 then
--         had_get = true
--     end

--     if XiuXianShiLianWGData.Instance:GetXiuxianRemind() == 1 then
--         had_get = true
--     end

--     self.node_list.task_list_red:SetActive(had_get)
-- end

--更改任务按钮名字
-- function MainUIView:ChangeTaskBtnName(str)
--     str = str or ""
--     -- if self.node_list["text_word_task_1"] then
--     --     self.node_list["text_word_task_1"].text.text = str
--     --     self.node_list["text_word_task_2"].text.text = str
--     -- end
--     --a1是文本a2改成了图片
--     if str ~= "" then
--         local image_str = "a2_zjm_rw_icon_fb"

--         if str == Language.Task.Boss_text or str == Language.Task.task_text3 then
--             image_str = "a2_zjm_rw_icon_boss"
--         elseif str == Language.Task.task_text or str == Language.Task.task_text2 then
--             image_str = "a2_zjm_rw_icon_rw"
--         elseif str == Language.KuafuPVP.KuaFuZhanDuiInfo or str == Language.Task.task_text6 then
--             image_str = "a2_zjm_rw_icon_xx"
--         elseif str == Language.Common.Rank then
--             image_str = "a2_zjm_rw_icon_ph"
--         end

--         if self.node_list["text_word_task_2"] then
--             local bundle, assets = ResPath.GetMainUIIcon(image_str)
--             self.node_list["text_word_task_2"].image:LoadSprite(bundle, assets)
--             bundle, assets = ResPath.GetMainUIIcon(image_str.."_1")
--             self.node_list["text_word_task_1"].image:LoadSprite(bundle, assets)
--         end
--     end
-- end

--任务列表
function MainUIView:SetTaskListActive(enable)
    if not self.is_show_task then
        self:AddDelayLoadFunc(BindTool.Bind(self.SetTaskListActive, self, enable), "show_task")
        return
    end

    if self.node_list["TaskMask"] then
        self.node_list["TaskMask"]:SetActive(enable)
    end

    if enable then
        self:FlushTaskTopPanel()
    else
        -- if self.node_list["inlet_container"] then
        --     self.node_list["inlet_container"]:SetActive(false)
        -- end

        if self.node_list["xiuxian_container"] then
            self.node_list["xiuxian_container"]:SetActive(false)
        end
    end
end

function MainUIView:FindTaskCellByTaskType(task_type)
    if not self.task_cell_list then
        return nil
    end
    for k, v in pairs(self.task_cell_list) do
        if v:GetActive() and v.data.task_type == task_type then
            return v
        end
    end
    return nil
end

function MainUIView:GetTaskMaskRootNode(callback)
    if self.has_load_callback then
         callback(self.node_list.Mask)
    else
        if not self.is_show_task then
            self:AddDelayLoadFunc(BindTool.Bind(self.GetTaskMaskRootNode, self, callback), "mask")
            return
        end
        --self.need_to_load_task = true
        --self.task_callback = callback
    end
end

function MainUIView:ClsoeTaskCellGuide(task_id)
    if self.task_cell_list == nil then
        return
    end

    for k, v in pairs(self.task_cell_list) do
        if v:GetData().task_id == task_id then
            v:SetGuideDialogVis(false)
            return
        end
    end
end

function MainUIView:GetOneTask(task_type)
    for _, v in pairs(self.task_cell_list) do
        if v:GetActive() and v.data ~= nil and v.data.task_type == task_type then
            return v.view
        end
    end
end


------------------------------------
-- 关闭天仙宝阁
-- function MainUIView:SetBtnTianxianPavilionStatus(status)
--     if self.node_list.btn_tianxian_pavilion then
--         self.node_list.btn_tianxian_pavilion:SetActive(status)
--     end
-- end
------------------------------------
----- 修仙试炼 修真之路 天仙宝阁 -----
------------------------------------
-- function MainUIView:FlushTaskTopPanel()
-- 	local is_show_task = self.node_list["TaskList"].gameObject.activeSelf
-- 	local is_xiuxian_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuXianShiLian)							-- 修仙试炼是否开启
-- 	local is_xiuzhen_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XIUZHEN_ROAD)					-- 修真之路是否开启
-- 	local is_tianxiange_show = FunOpen.Instance:GetFunIsOpened(GuideModuleName.TianxianPavilionView)				-- 天仙宝阁是否开启

-- 	local is_xiuxian_finish = XiuXianShiLianWGData.Instance:IsAllRewardGet()										-- 修仙试炼已结束
-- 	local is_xiuzhen_close = ActivityWGData.Instance:GetActivityIsClose(ACTIVITY_TYPE.XIUZHEN_ROAD)					-- 修真之路是否关闭
-- 	local is_xiuxian_show = is_xiuxian_open and not is_xiuxian_finish												-- 修仙试炼是否显示
-- 	local is_xiuzhen_show = is_xiuzhen_open or not is_xiuzhen_close													-- 修真之路是否显示
	
-- 	if not self.node_list["xiuxian_container"] or not self.node_list["tianxian_container"] or not self.node_list["inlet_container"] then
-- 		return
-- 	end
-- 	-- xiuxian_task_btn			修仙入口
-- 	-- xiuzhen_task_btn			修真入口
-- 	-- xiuxian_container		修仙长box 显示
-- 	-- btn_tianxian_pavilion	天仙阁圆入口
-- 	-- tianxian_task_btn		天仙阁半入口
-- 	-- inlet_container			分割box
-- 	-- tianxian_container 		宝阁box

-- 	self:RefreshTaskListRedPoint()
-- 	self.node_list.xiuxian_task_btn:SetActive(is_xiuxian_show)
-- 	self.node_list.xiuzhen_task_btn:SetActive(is_xiuzhen_show)
--     self.node_list.tianxian_task_btn:SetActive(false)
--     self.node_list.btn_tianxian_pavilion:SetActive(false)
-- 	self.node_list["xiuxian_container"]:SetActive(false)
-- 	self.node_list["inlet_container"]:SetActive(false)
-- 	self.node_list["tianxian_container"]:SetActive(false)

-- 	if not is_xiuxian_open and not is_xiuzhen_show and not is_tianxiange_show then
-- 		self.node_list["xiuxian_container"]:SetActive(is_show_task)
--     elseif is_xiuxian_show and not is_xiuzhen_show and not is_tianxiange_show then
--         self.node_list["xiuxian_container"]:SetActive(is_show_task)
-- 	elseif is_xiuxian_show and is_xiuzhen_show and not is_tianxiange_show then
-- 		self:Flushinletdata()
-- 		self.node_list["inlet_container"]:SetActive(true)
-- 	elseif is_xiuxian_show and is_xiuzhen_show and is_tianxiange_show then
-- 		self.node_list.btn_tianxian_pavilion:SetActive(true)
-- 		self:Flushinletdata()
-- 		self.node_list["inlet_container"]:SetActive(true)
-- 	elseif is_xiuxian_show and not is_xiuzhen_show and is_tianxiange_show then
-- 		self:Flushinletdata()
-- 		self.node_list["inlet_container"]:SetActive(true)
-- 		self.node_list.btn_tianxian_pavilion:SetActive(false)
-- 		self.node_list.xiuzhen_task_btn:SetActive(false)
-- 		self.node_list.tianxian_task_btn:SetActive(true)
-- 	elseif is_xiuxian_finish and not is_xiuzhen_show and is_tianxiange_show then
-- 		self:FlushPaviliondata()
-- 		self.node_list["xiuxian_container"]:SetActive(false)
-- 		self.node_list["inlet_container"]:SetActive(false)
-- 		self.node_list["tianxian_container"]:SetActive(true)
--         self:FlushBaogeTime()
--     elseif is_xiuxian_finish and is_xiuxian_open and not is_xiuzhen_show and not is_tianxiange_show then
-- 		self.node_list["xiuxian_container"]:SetActive(false)
-- 		self.node_list["inlet_container"]:SetActive(false)
-- 		self.node_list["tianxian_container"]:SetActive(false)
-- 	else
-- 		self.node_list["xiuxian_container"]:SetActive(false)
-- 		self.node_list["inlet_container"]:SetActive(false)
-- 		self.node_list["tianxian_container"]:SetActive(false)
-- 	end
-- end

function MainUIView:FlushTaskTopPanel()
    -- if not self.is_mainui_view_load then
    --     self:AddInitCallBack(nil, BindTool.Bind(self.FlushTaskTopPanel, self))
    -- end

    -- 换个地方显示
    -- local is_xiuzhen_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XIUZHEN_ROAD)				-- 修真之路是否开启
	-- local is_xiuzhen_close = ActivityWGData.Instance:GetActivityIsClose(ACTIVITY_TYPE.XIUZHEN_ROAD)				-- 修真之路是否关闭
    -- local is_xiuzhen_show = is_xiuzhen_open  or not is_xiuzhen_close	--or is_equip_collect_open			        -- 修真之路是否显示
    -- local is_equip_collect_open =  self:GetEquipTargetFuncOpen()
	-- local is_tianxiange_show = FunOpen.Instance:GetFunIsOpened(GuideModuleName.TianxianPavilionView)				-- 天仙宝阁是否开启
	-- local is_cap_wel_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CAPABILITY_WELFARE)             -- 战力福利是否开启
	local is_xiuwei_open = FunOpen.Instance:GetFunIsOpened(FunName.XiuWei)
	local is_zhuansheng_show = FunOpen.Instance:GetFunIsOpened(GuideModuleName.ZhuanSheng)						    -- 转职是否显示(开启并还能转职)

    -- 显示数量
    local show_count = 0

    -- 转职
    if is_zhuansheng_show then
        show_count = show_count + 1
    end
    -- 修仙(修为)
    if is_xiuwei_open then
        show_count = show_count + 1
    end



    self.node_list.top_task_xiuxian_xiuzhen_root:CustomSetActive(show_count > 0)

    self.node_list.btn_top_xiuwei:CustomSetActive(is_xiuwei_open and show_count ~= 1)  -- 修仙(修为)
    self.node_list.btn_top_xiuwei_big:CustomSetActive(is_xiuwei_open and show_count == 1)
    --转职
    self.node_list.btn_top_zhuansheng:CustomSetActive(is_zhuansheng_show)

    self.node_list.xiuxian_xiuzhen_root_bg:CustomSetActive(is_zhuansheng_show or (is_xiuwei_open and show_count ~= 1))

    -- 引导节点 不会自己变位置，top改变的时候也要改下坐标
    local y = show_count > 0 and 38 or 108
    self.node_list.first_task_guide.rect.anchoredPosition = Vector2(195, y)
    self.node_list.first_task_act_jjc_guide.rect.anchoredPosition = Vector2(195, y)
    -- if self.node_list.btn_top_task_xiuzhen and is_xiuzhen_open then
    --     local xiuzhen_title_name, complse_num, max_num = XiuZhenRoadWGData.Instance:GetShowMainUIInfo()
    --     local color = complse_num >= max_num and "#99ffbb" or "#ff9292"
    --     self.node_list.desc_xiuzhen_progress.text.text = ToColorStr(ToColorStr(complse_num, color) .. "/" .. max_num, "#99ffbb")
    --     local xiuzhen_remind = XiuZhenRoadWGData.Instance:IsShowXiuZhenRoadRedPoint()
    --     self.node_list.btn_top_task_xiuzhen_remind:SetActive(xiuzhen_remind > 0)
    -- end

    -- if self.node_list.btn_top_task_cap_wel and is_cap_wel_open then
    --     local cap_wel_remind = CapabilityWelfareWGData.Instance:GetRemind()
    --     local cur_num, max_num = CapabilityWelfareWGData.Instance:GetTaskProgress()
    --     local color = cur_num >= max_num and "#99ffbb" or "#ff9292"
	-- 	self.node_list.desc_cap_task_progress.text.text = ToColorStr(ToColorStr(cur_num, color) .. "/" .. max_num, "#99ffbb")
    --     self.node_list.btn_top_task_cap_remind:SetActive(cap_wel_remind > 0)
    -- end

    self:FlushTaskTopPanelXiuWeiRemind()

    if self.node_list.btn_top_task_xiuwei_remind_big and is_xiuwei_open then
        local is_remind = CultivationWGData.Instance:GetXiuWeiRemind() > 0
        self.node_list.btn_top_task_xiuwei_remind_big:SetActive(is_remind)
    end

    if self.node_list.btn_top_zhuansheng and show_count ~= 1 then
        local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
        local stage_cfg = TransFerWGData.Instance:GetStageCfgByLevel(prof_level +1 )
        local role_level = RoleWGData.Instance:GetRoleLevel()
        if IsEmptyTable(stage_cfg) or role_level < stage_cfg[1].open_level then
            local stage_num = TransFerWGData.Instance:GetStageNumByLevel(prof_level)
		    self.node_list.desc_zhuangsheng_progress.text.text = ToColorStr(string.format("%s/%s",stage_num,stage_num), "#99ffbb")
        else
            local is_finish,cur_num, max_num = TransFerWGData.Instance:IsAllFinish()
            local color = is_finish and "#99ffbb" or "#ff9292"
		    self.node_list.desc_zhuangsheng_progress.text.text = ToColorStr(ToColorStr(cur_num, color) .. "/" .. max_num, "#99ffbb")
        end
        local transfer_remind = TransFerWGData.Instance:GetTransFerRemind()
        self.node_list.btn_top_task_zhuansheng_remind:SetActive(transfer_remind > 0)
    end

    self:EquipCollectFlushInfo()
    -- 实际是跳到转职 说入口重复要屏蔽
    -- self:FlushTransferEquipCollectInfo()
end

function MainUIView:FlushTaskTopPanelXiuWeiRemind()
    local is_xiuwei_open = FunOpen.Instance:GetFunIsOpened(FunName.XiuWei)
    local remind = RemindManager.Instance:GetRemind(RemindName.XiuWeiMainEnter)

    if self.node_list.btn_top_xiuwei_remind then
        self.node_list.btn_top_xiuwei_remind:SetActive(is_xiuwei_open and remind > 0)
    end
end

-- function MainUIView:Flushinletdata()
-- 	local is_xiuxian_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuXianShiLian)
-- 	local is_xiuzhen_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XIUZHEN_ROAD)
-- 	local is_tianxiange_show = FunOpen.Instance:GetFunIsOpened(GuideModuleName.TianxianPavilionView)

-- 	if is_xiuxian_open then
-- 		local chapter_index = XiuXianShiLianWGData.Instance:GetLastChapterIndex()
-- 		local xiuxian_info = XiuXianShiLianWGData.Instance:GetChapterListInfo()[chapter_index]
-- 		local xiuxian_remind = XiuXianShiLianWGData.Instance:GetXiuxianRemind()
-- 		self.node_list.xiuxian_title_txt.text.text = xiuxian_info.chapter_name or Language.Task.XiuXianTitle
-- 		self.node_list.xiuxian_progress_txt.text.text = string.format(Language.Task.inletProgress, xiuxian_info.cur_task_index, xiuxian_info.all_task_count)
-- 		self.node_list.xiuxian_task_remind:SetActive(xiuxian_remind > 0)
-- 	end

-- 	if is_xiuzhen_open then
-- 		local xiuzhen_title_name, complse_num, max_num = XiuZhenRoadWGData.Instance:GetShowMainUIInfo()
-- 		local xiuzhen_remind = XiuZhenRoadWGData.Instance:IsShowXiuZhenRoadRedPoint()
-- 		self.node_list.xiuzhen_title_txt.text.text = xiuzhen_title_name or Language.Task.XiuZhenTitle
-- 		self.node_list.xiuzhen_progress_txt.text.text = string.format(Language.Task.inletProgress, complse_num, max_num)
-- 		self.node_list.xiuzhen_task_remind:SetActive(xiuzhen_remind > 0)
-- 	end

-- 	if is_tianxiange_show then
-- 		local interface_cfg = TianxianPavilionWGData.Instance:GetInterfaceCfg(1)
-- 		if not IsEmptyTable(interface_cfg) then
-- 			local task_bundle, task_asset = ResPath.GetMainUIIcon(interface_cfg.tianxian_task_bg)
-- 			self.node_list.tianxian_task_bg.image:LoadSprite(task_bundle, task_asset, function()
-- 				self.node_list.tianxian_task_bg.image:SetNativeSize()
-- 			end)
-- 		end
-- 	end
-- end

-- function MainUIView:FlushPaviliondata()
-- 	local interface_cfg = TianxianPavilionWGData.Instance:GetInterfaceCfg(1)
-- 	if not IsEmptyTable(interface_cfg) then
-- 		local tianxian_baoge_bundle, tianxian_baoge_asset = ResPath.GetMainUIIcon(interface_cfg.tianxian_baoge_bg)
-- 		self.node_list.tianxian_baoge_bg.image:LoadSprite(tianxian_baoge_bundle, tianxian_baoge_asset, function()
-- 			self.node_list.tianxian_baoge_bg.image:SetNativeSize()
-- 		end)
-- 	end
-- end


-- function MainUIView:OnClickXiuxian(is_no_parem)
--     local is_xiuxian_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuXianShiLian)
--     if is_xiuxian_open then
--         local cur_data_index = is_no_parem and nil or XiuXianShiLianWGData.Instance:GetMainViewDisplayChapterId()
--         ViewManager.Instance:Open(GuideModuleName.XiuXianShiLian, nil, "all", {open_param = cur_data_index})
--     else
--         local fun_cfg = FunOpen.Instance:GetFunByName(GuideModuleName.XiuXianShiLian)
--         SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.XiuXianShiLian.LockDes, fun_cfg.trigger_param))
--     end
-- end

function MainUIView:OnClickXiuZhen()
    local error_str = nil
    local is_open_act = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XIUZHEN_ROAD)
    local is_xiuzhen_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuZhenRoadView)
    if not is_open_act then
        error_str = Language.XiuZhenRoad.LockTips1
    elseif not is_xiuzhen_open then
        local fun_cfg = FunOpen.Instance:GetFunByName(ACTIVITY_TYPE.XIUZHEN_ROAD)
        error_str = string.format(Language.XiuZhenRoad.LockTips2, fun_cfg.trigger_param)
    end

    if error_str then
        SysMsgWGCtrl.Instance:ErrorRemind(error_str)
        return
    end

    ViewManager.Instance:Open(GuideModuleName.XiuZhenRoadView, nil, "all", {})
end

function MainUIView:OnClickTianxian()
    local is_tianxian_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.TianxianPavilionView)
    if is_tianxian_open then
		ViewManager.Instance:Open(GuideModuleName.TianxianPavilionView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianxianText.FunNotOpen)
    end
end

function MainUIView:OnClickCapWelBtn()
    -- local is_open_act = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CAPABILITY_WELFARE)
    local is_cultivation_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.CultivationView)

    if is_cultivation_open then
		ViewManager.Instance:Open(GuideModuleName.CultivationView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianxianText.FunNotOpen)
    end
end

function MainUIView:OnClickZhuanshengBtn()
    local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.ZhuanSheng)
    if is_open then
		ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianxianText.FunNotOpen)
    end
end

function MainUIView:OnClickXiuWeiBtn()
    local is_open = FunOpen.Instance:GetFunIsOpened(FunName.XiuWei)
    if is_open then
		ViewManager.Instance:Open(GuideModuleName.XiuWeiView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianxianText.FunNotOpen)
    end
end

--修仙试炼技能
function MainUIView:OnClickXiuxianSkill()
    local is_xiuxian_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuXianShiLian)
    local data = {}
    if not is_xiuxian_open then
        data = XiuXianShiLianWGData.Instance:GetChapterListInfo()[1]
    else
        local cur_data_index = XiuXianShiLianWGData.Instance:GetMainViewDisplayChapterId() --策划让显示已开启等级最高的
        data = XiuXianShiLianWGData.Instance:GetChapterListInfo()[cur_data_index]
    end

    local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(data.skill_index)
    if skill_data and not IsEmptyTable(skill_data) and data and not IsEmptyTable(data) then
        local capability = XiuXianShiLianWGData.Instance:GetSkillAttrCap(data)
        local show_data = {
            icon = skill_data.icon,
            top_text = skill_data.name,
            body_text = skill_data.desc,
            x = -49,
            y = -90,
            set_pos = true,
            capability = capability,
        }

        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

function MainUIView:OnClickBuffList()
    BuffTip.Instance:SetContent()
end

function MainUIView:OnClickOpenCustomActionWindow()
	if YunbiaoWGData.Instance:GetIsHuShong() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
        return
    end

	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if scene_cfg and scene_cfg.pb_custom_action == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	CustomActionCtrl.Instance:OpenCustomActionWindowView()
end

function MainUIView:OnClickTaskLiLian()
    ViewManager.Instance:Open(GuideModuleName.TianShenLiLianView)
end

function MainUIView:OnClickTaskScreenShot()
    ViewManager.Instance:Open(GuideModuleName.ScreenShotView)
end

-- 定制传闻入口
function MainUIView:OnClickCustomizedRumors()
	ViewManager.Instance:Open(GuideModuleName.CustomizedRumorsView)
end

--点击装备收集
function MainUIView:OnClickEquipCollect()
    -- FunOpen.Instance:OpenViewByName(GuideModuleName.EquipTargetView)
    FunOpen.Instance:OpenViewByName(GuideModuleName.CultivationView,TabIndex.equip_target)
end

--点击转职装备收集
function MainUIView:OnClickTransferEquipCollect()
    local show_suit_index = TransFerWGData.Instance:GetCurShowSuitIndex()
    local suit_cfg = TransFerWGData.Instance:GetEquipCollectCfgBySuit(show_suit_index)
    local jump_prof = suit_cfg and suit_cfg.jump_prof or 1 
    ViewManager.Instance:Open(GuideModuleName.ZhuanSheng, nil, "all", {jump_prof = jump_prof})
end
-------------------
---    组队     ---
-------------------
-- 刷新组队List
function MainUIView:RefreshTeamListView(cell, data_index)
    data_index = data_index + 1
    local menber_cell = self.team_cell_list[cell]
    if menber_cell == nil then
        menber_cell = MainUiMenberCell.New(cell.gameObject)
        menber_cell.team_view = self
        self.team_cell_list[cell] = menber_cell
    end

    menber_cell:SetIndex(data_index)
    menber_cell:SetData(self.team_list[data_index])
end

--队伍成员刷新
function MainUIView:TeamCellFlush()
    if not self.is_mainui_view_load then
        return
    end

    local member_list, is_qingyuan, member_real_count
    local online_member_count = 0
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        member_list = CrossTeamWGData.Instance:GetTeamMemberList()
        is_qingyuan = false
        member_real_count = CrossTeamWGData.Instance:GetTeamMemberCount()
        online_member_count = member_real_count
        if member_real_count < 3 then
            local data = {}
            data.is_empty_data = true
            table.insert(member_list, data)
        end
    else
        member_list = SocietyWGData.Instance:GetTeamMemberList()
        local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        member_real_count = #member_list
        is_qingyuan = team_type == GoalTeamType.QingYuanFb        -- 情缘组队特殊处理
        for k, v in pairs(member_list) do
            if v.is_leader == 1 then
                local temp = v
                table.remove(member_list, k)
                table.insert(member_list, 1, temp)
                break
            end
        end

        if (member_real_count < 3 and not is_qingyuan) or (member_real_count < QingYuanLimitCount and is_qingyuan) then
            local data = {}
            data.is_empty_data = true
            table.insert(member_list, data)
        end

        online_member_count = 0
        for k, v in pairs(member_list) do
            if v.is_online == 1 then
                online_member_count = online_member_count + 1
            end
        end
    end

    if member_real_count >= 1 then
        self.list_view.scroller:ReloadData(0)
    end

    -- self.node_list["team_apply_redpoint"]:CustomSetActive(SocietyWGData.Instance:GetIsTeamLeader() and SocietyWGData.Instance:GetReqTeamListSize() > 0)
end

--更改组队按钮名字
function MainUIView:ChangeTeamBtnName(str, is_team, scene_type)
    str = str or Language.Task.TeamMember

    --优先使用自定义名字，防止被一些调用顺序影响
     local cfg = MainuiWGCtrl.Instance:GetFbSceneCfg(scene_type)
    if cfg.is_custom_team_btn_name == 1 then
        if not is_team then
            self:UpdateTeamBtnName(str)
        end

        return
    end

    --没有使用自定义名字的, 都强制显示‘组队’ 或者 ‘组队 X 数量’
    if self:GetRoomActive() then
       str = Language.Task.TeamMember
    end

    self:UpdateTeamBtnName(str)
end

function MainUIView:GetRoomActive()
    return self.node_list.RoomFrame.gameObject.activeSelf == true
end

function MainUIView:UpdateTeamBtnName(str)
    -- A2改成了图片
    -- if self.node_list["text_word_task_3"] then
    --     self.node_list["text_word_task_3"].text.text = str
    --     self.node_list["text_word_task_4"].text.text = str
    -- end
end

-- 队伍邀请前往
function MainUIView:FlushTeamInvite(state)
    if self.has_load_callback == false then
        self:AddDelayLoadFunc(BindTool.Bind(self.FlushTeamInvite, self, state), "team_goto")
        return
    end

    local member_real_count
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        member_real_count = CrossTeamWGData.Instance:GetTeamMemberCount()
    else
        local member_list = SocietyWGData.Instance:GetTeamMemberList()
        member_real_count = #member_list
    end

    if member_real_count < 2 then
        state = false
    end

    if self.team_invite_state ~= state then
        self.team_invite_state = state
    end
end

--创建队伍
function MainUIView:CreateTeam()
    local is_open, tips = FunOpen.Instance:GetFunIsOpened("NewTeamView", true)
    if not is_open then
        if tips then
            SysMsgWGCtrl.Instance:ErrorRemind(tips)
        end
        return
    end

    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:SendCreateTeamReq()
        ViewManager.Instance:Open(GuideModuleName.CrossTeamView)
    else
        local team_type = 0
        local fb_mode = 1
        local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
        local min_level, max_level = COMMON_CONSTS.NoalGoalLimitMinLevel, top_user_level
        NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
        SocietyWGCtrl.Instance:SendTeamListReq(team_type, fb_mode)
        ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
    end
end

--寻找队伍
function MainUIView:FindTeam()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:OpenAllTeam()
    else
        ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
    end
end

-- 点击分身按钮
function MainUIView:OnClickTeamTaskCloneBtn()
    ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_clone)
end

--附近队伍
function MainUIView:FindNearTeam()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:OpenAllTeam()
    else
        ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_near)
    end
end

--邀请前往
function MainUIView:InviteTeamGoto()
    BossAssistWGCtrl.SendInviteTeamGoto()
end

--世界喊话按钮点击
function MainUIView:OnClickWorldTalk()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        local name = RoleWGData.Instance:GetRoleVo().name
        CrossTeamWGCtrl.Instance:ShowTalkView(name)
    else
        NewTeamWGCtrl.Instance:OpenRecruitView()
        --NewTeamWGCtrl.Instance:ShowTalkView()
    end
end

function MainUIView:OnClickQuitBtn()
    if Scene.Instance:GetSceneType() == SceneType.GUIDE_BOSS then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Guide.WorldBossGuideTip)
        return
    end

    local content = ""
    local main_role = Scene.Instance:GetMainRole()
    content = string.format(Language.NewTeam.QuitTeamStr, main_role.vo.name, main_role.vo.level)
    ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1, nil, true, false)
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_EXIT)
        CrossTeamWGData.Instance:ClearTeamData()
        GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE) --因为不下发
    else
        SocietyWGCtrl.Instance:SendExitTeam()
        NewTeamWGData.Instance:ClearTeamInfo()
    end
end

function MainUIView:OnClickZhaoJiBtn(zhaoji_role_id, name)
    local tmp_time = self:GetZhaoJiCdTime()
    if tmp_time > 0 and self.zhaoji_cd_timer then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.CanNotZhaoJi, math.ceil(tmp_time)))
        return
    end

    local member_list = SocietyWGData.Instance:GetTeamMemberList()
    local role_id
    for k, v in pairs(member_list) do
        if v.is_leader == 1 then
            role_id = v.role_id
            break
        end
    end

    local cur_role_id = zhaoji_role_id or 0
    local str

    if zhaoji_role_id and name then
        str = string.format(Language.NewTeam.IsZhaoJiRole, name)
    else
        str = Language.NewTeam.IsZhaoJi
    end

    local ok_fun = function ()
        SocietyWGCtrl.Instance:SendReqTeamCallTogether(cur_role_id)
        self:SetZhaoJiCdTime()
    end

    if role_id == RoleWGData.Instance:InCrossGetOriginUid() then
        if not self.confirm_team_alert then
            self.confirm_team_alert = Alert.New()
        end

        self.confirm_team_alert:SetOkFunc(ok_fun)
        self.confirm_team_alert:SetLableString(str)
        self.confirm_team_alert:Open()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanDo)
    end
end

-- 召集CD
function MainUIView:GetZhaoJiCdTime()
    local end_time = NewTeamWGData.Instance:GetZhaoJiCdEndTime()
    if (end_time - Status.NowTime) > 0 then
        return math.ceil(end_time - Status.NowTime)
    else
        return math.floor(end_time - Status.NowTime)
    end

end

function MainUIView:SetZhaoJiCdTime()
    NewTeamWGData.Instance:SetZhaoJiCdEndTime()
    local end_time = NewTeamWGData.Instance:GetZhaoJiCdEndTime()
    if end_time <= Status.NowTime then
        return
    end

    self:ShowZhaoJiBtnCd()
end

function MainUIView:ShowZhaoJiBtnCd()
    if nil == self.node_list["zhaoji_text"] then
        return
    end

    self:ClearZhaoJiCdTimer()
    local cd_time = self:GetZhaoJiCdTime()
    self:UpdateZhaoJiBtnCd()

    if cd_time > 0 then
        self.zhaoji_cd_timer = GlobalTimerQuest:AddTimesTimer(BindTool.Bind1(self.UpdateZhaoJiBtnCd, self), 1, cd_time)
    end
end

function MainUIView:ClearZhaoJiCdTimer()
    if nil ~= self.zhaoji_cd_timer then
        GlobalTimerQuest:CancelQuest(self.zhaoji_cd_timer)
        self.zhaoji_cd_timer = nil
    end
end

function MainUIView:UpdateZhaoJiBtnCd()
    -- local cd_time = self:GetZhaoJiCdTime()
    -- if cd_time < 0 then
    --     if nil ~= self.node_list["zhaoji_text"] then
    --         self.node_list["zhaoji_text"].text.text = Language.NewTeam.ZhaoJiStr
    --     end
    --     self:ClearZhaoJiCdTimer()
    -- else
    --     if nil ~= self.node_list["zhaoji_text"] then
    --         self.node_list["zhaoji_text"].text.text = Language.NewTeam.Wait .. "(" .. cd_time .. ")"
    --     end
    -- end
end

function MainUIView:OnClickFollowBtn()
    local member_list = SocietyWGData.Instance:GetTeamMemberList()
    local role_id
    local plat_type
    for k, v in pairs(member_list) do
        if v.is_leader == 1 then
            role_id = v.role_id
            plat_type = v.plat_type
            break
        end
    end

    if not role_id then
        return
    end

    if role_id == RoleWGData.Instance:InCrossGetOriginUid() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NoFollowMe)
    else
        local uuid = MsgAdapter.ReadUUIDByValue(role_id, plat_type)
        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:SetTrackRoleData(OBJ_FOLLOW_TYPE.TEAM, uuid)
        end
        
        Scene.SendGetRoleMoveInfo(uuid, OBJ_FOLLOW_TYPE.TEAM, CLIENT_MOVE_REQ_PARAM.GUAJI_RECOVERY)
    end
end

--队伍按钮点击回调
-- function MainUIView:TeamButtonCallBack(isOn)
--     if self:GetRoomActive() then
--         self:FlushRoom()
--         return
--     end

--     if self.team_btn_click_callback then
--         self.team_btn_click_callback(isOn)
--     end

--     self.is_show_team = isOn
--     self:RefreshLeftPanel()
--     if not self.can_open_team then
--         self.can_open_team = isOn
--         return
--     end

--     self.can_open_team = isOn
--     if not isOn then
--     	return
--     end

--     if not self.node_list["TaskContents"]:GetActive() then
--     	return
--     end

--     local scene_type = Scene.Instance:GetSceneType()
--     if scene_type == SceneType.GUIDE_BOSS then
--         SysMsgWGCtrl.Instance:ErrorRemind(Language.Guide.WorldBossGuideTip)
--         return
--     end

--     if Scene.Instance:GetIsOpenCrossViewByScene(scene_type) then
--         ViewManager.Instance:Open(GuideModuleName.CrossTeamView)
--     else
--         if BossWGData.IsBossScene(scene_type) then
--             ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
--         elseif scene_type == SceneType.Kf_PvP_Prepare then
--             NewTeamWGData.Instance:SetTeamTypeAndMode(0, 1)
--             if SocietyWGData.Instance:GetIsInTeam() == 0 then
--                 ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
--             else
--                 ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
--             end
--         else
--             if SocietyWGData.Instance:GetIsInTeam() == 0 then
--                 ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
--             else
--                 ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
--             end
--         end
--     end
-- end

--刷新房间
function MainUIView:FlushRoom()
    self.mainui_room_info:Flush()
end

function MainUIView:OnClickDuiwuBtn()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        ViewManager.Instance:Open(GuideModuleName.CrossTeamView)
    else
        ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
    end
end

-- 协助
function MainUIView:OnClickXieZhuTipBtn()
   BossXiezhuWGCtrl.Instance:ShowOrHideXieZhuRule()
end

-- 世界喊话
function MainUIView:OnWorldTalkCallBack()
    XUI.SetGraphicGrey(self.node_list.btn_word_talk, true)
    local cd_time = ChatWGData.Instance:GetChatCdLimint(CHANNEL_TYPE.ZUDUI)
    cd_time = cd_time or COMMON_CONSTS.TEAM_WORLD_TALK
    if cd_time > 0 then
        self.node_list.btn_word_talk_txt.text.text = string.format(Language.NewTeam.WorldTalk6, cd_time)
        CountDownManager.Instance:AddCountDown("team_world_talk", BindTool.Bind(self.UpdateTeamWordTalkBtn, self),
            BindTool.Bind(self.ComleteTeamWoldTalkCD, self), nil, cd_time, 0.5)
    else
        self:ComleteTeamWoldTalkCD()
    end
end

function MainUIView:UpdateTeamWordTalkBtn(elapse_time, total_time)
    local time = math.ceil(total_time - elapse_time)
    self.node_list.btn_word_talk_txt.text.text = string.format(Language.NewTeam.WorldTalk6, time)
    GlobalEventSystem:Fire(TeamWorldTalk.NEW_TEAM_WORLD_TALK, time)
    if not self.btn_word_talk_gray then
        self.btn_word_talk_gray = true
    end
end

function MainUIView:ComleteTeamWoldTalkCD()
    if CountDownManager.Instance:HasCountDown("team_world_talk") then
        CountDownManager.Instance:RemoveCountDown("team_world_talk")
    end
    local len = GetTableLen(self.team_list)
    local str = Language.NewTeam.WorldTalk2
    --self.node_list.btn_word_talk_txt.text.text = len == 3 and str or Language.NewTeam.WorldTalk2
    self.node_list.btn_word_talk_txt.text.text = ""
    self.btn_word_talk_gray = nil
    XUI.SetGraphicGrey(self.node_list.btn_word_talk, false)
    GlobalEventSystem:Fire(TeamWorldTalk.COMPLETE_CALL_BACK)
end

function MainUIView:FlushWorkTalkBtnActive(condition)
    local member_real_count
    local is_full_team = false
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        member_real_count = CrossTeamWGData.Instance:GetTeamMemberCount()
        is_full_team = member_real_count == 3
    else
        local member_list = SocietyWGData.Instance:GetTeamMemberList()
        local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        member_real_count = #member_list
        local is_qingyuan = team_type == GoalTeamType.QingYuanFb        -- 情缘组队特殊处理
        is_full_team = (is_qingyuan and member_real_count == 2) or (not is_qingyuan and member_real_count == 3)
    end

    --self.node_list.btn_word_talk:SetActive(not is_full_team and condition == true)
end

-- function MainUIView:FlushBaogeTime()
--     if CountDownManager.Instance:HasCountDown("tianxian_baoge_time") then
--         return
--     end

--     local server_time = TimeWGCtrl.Instance:GetServerTime()
-- 	local next_time = TianxianPavilionWGData.Instance:GetEndTime()
--     local time = next_time - server_time

--     if time > 0 then
--         CountDownManager.Instance:AddCountDown("tianxian_baoge_time",
--             BindTool.Bind(self.UpdateTxbgCountDown, self),
--             BindTool.Bind(self.OnTxbgComplete, self),
--             nil, time, 1)
--     else
--         self:OnTxbgComplete()
--     end
-- end

-- function MainUIView:UpdateTxbgCountDown(elapse_time, total_time)
--     local time = math.ceil(total_time - elapse_time)
--     local time_str = TimeUtil.FormatSecondDHM8(time)
--     self.node_list["tianxian_activity_time"].text.text = string.format(Language.TianxianText.TianxianActTime, time_str)
-- end

-- function MainUIView:OnTxbgComplete()
--     self.node_list.tianxian_activity_time.text.text = ""
--     if self.node_list["tianxian_container"] then
--         self.node_list["tianxian_container"]:SetActive(false)
--     end

--     if self.node_list.btn_tianxian_pavilion then
--         self.node_list.btn_tianxian_pavilion:SetActive(false)
--     end

--     if self.node_list.tianxian_task_btn then
--         self.node_list.tianxian_task_btn:SetActive(false)
--     end
-- end

--设置房间信息显隐
function MainUIView:SetRoomActive(is_active)
    self.node_list.TeamFrame:SetActive(not is_active)
    self.node_list.RoomFrame:SetActive(is_active)
    if is_active then
        self:FlushRoom()
    end
end

--取消匹配
function MainUIView:CancelTeamMatch()
    local goal_info = NewTeamWGData.Instance:GetNowGoalInfo()
    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    NewTeamWGCtrl.Instance:SendAutoMatchTeam(1, team_type, fb_mode)
end

function MainUIView:ChangeMatchState(info)
    local state = info.is_matching == 1
    local is_team
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        is_team = CrossTeamWGData.Instance:GetIsInTeam() == 1
        state = false
    else
        is_team = SocietyWGData.Instance:GetIsInTeam() == 1
    end

    self.show_add_exp:SetActive(is_team or state)
    self.button_content:SetActive(not is_team and not state)
    self.node_list["TeamMatingState"]:SetActive(state)
    self.node_list["TeamNormalState"]:SetActive(not state)
    if state then
        self:FlushMatchStateInfo(info)
    end
end

function MainUIView:FlushMatchStateInfo(info)
    if not self.has_load_callback then
        self:AddDelayLoadFunc(BindTool.Bind(self.FlushMatchStateInfo, self, info), "match")
        return
    end

    local is_team = not IsEmptyTable(self.team_list)
    local cut_count = is_team and SocietyWGData.Instance:GetTeamMemberCount() or 1
    if info then
        local team_target = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(info.team_type, info.teamfb_mode)
        self.node_list["team_target"].text.text = team_target.team_type_name
        self.node_list["matching_num"].text.text = Language.NewTeam.Matching4 .. cut_count .. "/" .. GameEnum.TEAM_MAX_COUNT
        self.node_list["cancle_match"]:SetActive(SocietyWGData.Instance:GetIsTeamLeader() == 1 or not is_team)
    else
        self.node_list["matching_num"].text.text = Language.NewTeam.Matching4 .. cut_count .. "/" .. GameEnum.TEAM_MAX_COUNT
        self.node_list["cancle_match"]:SetActive(SocietyWGData.Instance:GetIsTeamLeader() == 1 or not is_team)
    end
end

function MainUIView:RoomSceneTypeFlushMemberCount()
    local scene_type = Scene.Instance:GetSceneType()
    if NewTeamWGData.Instance:GetIsInRoomScene(scene_type) then
        local str = Language.Task.task_text4
        self:ChangeTeamBtnName(str, true)
    end
end

function MainUIView:ReloadTeamList()
    self.list_view.scroller:ReloadData(0)
    local show_apply_red_point
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        show_apply_red_point = CrossTeamWGData.Instance:GetIsTeamLeader() and CrossTeamWGData.Instance:GetReqTeamListSize() > 0
    else
        show_apply_red_point = SocietyWGData.Instance:GetIsTeamLeader() and SocietyWGData.Instance:GetReqTeamListSize() > 0
    end

    -- self.node_list["team_apply_redpoint"]:CustomSetActive(show_apply_red_point)
end

-- function MainUIView:IsShowTeamBtn(is_show)
    -- if is_show then
    --     self:SetTaskButtonTrue() --进入副本重置一下状态防止重叠
    -- end

    -- if self.node_list.TeamButton then
    --     self.node_list.TeamButton:SetActive(not is_show)
    -- end

    -- if self.node_list.TaskButton then
    --     self.node_list.TaskButton:SetActive(not is_show)
    -- end

    -- if self.node_list.WeddingButton then
    --     self.node_list.WeddingButton:SetActive(is_show)
    -- end

--     if self.node_list.task_fb_name_root then
--         self.node_list.task_fb_name_root:SetActive(is_show)
--     end

--     if self.node_list.btn_task_team then
--         self.node_list.btn_task_team:SetActive(not is_show)
--     end
-- end

-- 设置侧边栏组队按钮状态
function MainUIView:SetTeamBtnState(is_show)
    if self.node_list.btn_task_team then
        self.node_list.btn_task_team:SetActive(is_show)
        if not is_show then
            self.node_list.btn_task_team.toggle.isOn = false
        end
    end
end

-- 设置侧边栏宣传图按钮状态
function MainUIView:SetAdvanceNoticeBtnState(is_active, is_show)
    if self.node_list.btn_task_advance_notice then
        local data_list = MainuiWGData.Instance:GetFPAdvanceNoticeList()
        if IsEmptyTable(data_list) then
            self.node_list.btn_task_advance_notice:SetActive(false)
            self.node_list.btn_task_advance_notice.toggle.isOn = false
        else
            self.node_list.btn_task_advance_notice:SetActive(is_active)
            if is_active and is_show then
                self.node_list.btn_task_advance_notice.toggle.isOn = true
            elseif is_show == false then
                self.node_list.btn_task_advance_notice.toggle.isOn = false
            end
        end
    end
end

-- 设置活动名及状态
function MainUIView:SetFBNameState(is_show, fb_name_str)
    if self.node_list.task_fb_name_root then
        self.node_list.task_fb_name_root:CustomSetActive(is_show)

        if is_show and self.node_list.desc_task_fb_name then
            self.node_list.desc_task_fb_name.text.text = fb_name_str or ""
        end
    end
end

-- function MainUIView:SetFBName(str)
--     -- if self.node_list.text_three_name then
--     --     self.node_list.text_three_name.text.text = str
--     -- end

--     if self.node_list.task_fb_name_root then
--         self.node_list.task_fb_name_root:SetActive(true)
--     end
--     if self.node_list.desc_task_fb_name then
--         self.node_list.desc_task_fb_name.text.text = str
--     end
-- end

-------------------
---    BOSS     ---
-------------------
-- 刷新Boss列表
function MainUIView:RefresBossListView(cell, data_index, cell_index)
    data_index = data_index + 1
    local icon_cell = self.boss_cell_list[cell]
    if icon_cell == nil then
        icon_cell = BossFollowRender.New(cell.gameObject, self)
        self.boss_cell_list[cell] = icon_cell
    end
    local data = self.boss_data[data_index]
    icon_cell:SetIndex(data_index)
    icon_cell:SetData(data)
    icon_cell:FlushHl()
end

-- 刷新精英Boss列表
function MainUIView:RefresEliteBossListView(cell, data_index, cell_index)
    data_index = data_index + 1
    local icon_cell = self.elite_cell_list[cell]
    if icon_cell == nil then
        icon_cell = EliteFollowRender.New(cell.gameObject, self)
        self.elite_cell_list[cell] = icon_cell
    end
    local data = self.elite_data[data_index]
    icon_cell:SetIndex(data_index)
    icon_cell:SetData(data)
    icon_cell:FlushHl()
end

-- function MainUIView:OnClickBossShrinkBtn(show_boss_list, need_tween)
--     if show_boss_list == nil then
--         self.is_show_boss_list = not self.is_show_boss_list
--     else
--         self.is_show_boss_list = show_boss_list
--     end

--     -- self.node_list.boss_shrink_right:SetActive(self.is_show_boss_list)
--     self:CancelBossShrinkTween()
--     need_tween = need_tween or true
--     self:ShowTaskTypeActive(self.is_show_boss_list, need_tween)
-- end

function MainUIView:CancelBossShrinkTween()
    if self.left_panel_shrink_tween then
        self.left_panel_shrink_tween:Kill()
        self.node_list.TaskMask.rect.anchoredPosition = u3dpool.vec2(0, 0)
        self.node_list.BossList.rect.anchoredPosition = u3dpool.vec2(0, 0)
        -- self.node_list.OtherContent.rect.anchoredPosition = u3dpool.vec2(62, 0)
        self.left_panel_shrink_tween = nil
    end
end

-- boss改变
function MainUIView:RefreshBossList()
    local is_active = self.boss_list and self.boss_list:GetActive()
    if not self.has_load_callback or not is_active then
        self:AddDelayLoadFunc(BindTool.Bind(self.RefreshBossList, self), "refresh")
        return
    end

    if self.boss_list and is_active then
        self.boss_list.scroller:RefreshAndReloadActiveCellViews(true)
    end
end

--boss列表 enable 为true 显示boss列表或者 伤害列表
function MainUIView:SetBossListActive(enable)
    if not self:IsOpen() or not self.boss_list then
        self:AddDelayLoadFunc(BindTool.Bind(self.ShowTaskTypeActive, self, true), "boss_list_active")
        return
    end

    local is_single = self:IsSingleBoss()
    local has_demage = BossAssistWGData.Instance and BossAssistWGData.Instance:HasDemageHurt()
    if enable and self.is_show_content and has_demage then
        self.boss_list:CustomSetActive(false)
        self.node_list["btn_switch_damage"]:SetActive(not is_single)
        return
    end

    self.node_list["BossList"]:SetActive(enable)
    self.node_list["btn_switch_damage"]:SetActive(not is_single and not self.is_show_content and has_demage)
    local can_add = self:IsBossCanAddCout()

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.DABAO_BOSS then
        if self.show_monster == 2 then
            self.node_list.btn_change_elite_boss.toggle.isOn = true
        else
            self.node_list.btn_change_common_boss.toggle.isOn = true
        end

    elseif scene_type == SceneType.WorldBoss then
        if not IsEmptyTable(self.elite_data) then
            if self.show_monster == 2 then
                self.node_list.btn_change_elite_boss.toggle.isOn = true
            else
                self.node_list.btn_change_common_boss.toggle.isOn = true
            end
        else
            self.boss_list:SetActive(not is_single)
        end
    elseif scene_type == SceneType.VIP_BOSS then
        self.node_list.btn_task_normal.toggle.isOn = enable
        self.boss_list:SetActive(not is_single)
    else
        self.boss_list:SetActive(not is_single)
    end

    if not is_single then
       self:RefreshBossList()
    end

    self.node_list.boss_info:SetActive(is_single)
    self.node_list.boss_count_add:SetActive(can_add)
    self.node_list.add_img:SetActive(can_add)
    BossXiezhuWGCtrl.Instance:ShowOrHideXiezhuInfoBtn(enable)
end

function MainUIView:IsBossCanAddCout()
    local scene_type = Scene.Instance:GetSceneType()
    local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(scene_type)
    if cfg then
        return true
    else
        return scene_type == SceneType.SG_BOSS
    end
end

function MainUIView:SetMonsterListActive(index, isOn)
    if (self.show_monster == index and isOn) or not isOn then
        return
    end

    self.show_monster = index

    local is_elite = index == 2
    self.boss_list:SetActive(not is_elite)
    self:SetEliteTip(is_elite)
     if is_elite then
        if self.elite_list and self.elite_list:GetActive() then
            self.elite_list.scroller:RefreshAndReloadActiveCellViews(true)
        end
     else
        if self.boss_list and self.boss_list:GetActive() then
            self.boss_list.scroller:RefreshAndReloadActiveCellViews(true)
        end
     end
end

function MainUIView:ShowBossCount(scene_type)
    self.node_list["boss_count"]:SetActive(true)
    self.node_list.reward_pa:SetActive(false)
    self.node_list.real_recharge_boss_info:SetActive(false)
    self.node_list.boss_detail_info:SetActive(true)

    self.node_list.boss_detail_info.layout_element.minHeight = 0
    self.node_list.boss_list.layout_element.minHeight = 180
    self.node_list.elite_list.layout_element.minHeight = 180

    if scene_type == SceneType.DABAO_BOSS then
        self.node_list["boss_count"]:SetActive(false)
        self.node_list.boss_list.layout_element.minHeight = 260
    elseif scene_type == SceneType.WorldBoss then
        self.node_list.up_container:SetActive(true)
        self.node_list.boss_detail_info.layout_element.minHeight = 44
        self.node_list.boss_list.layout_element.minHeight = 234
        self.node_list.elite_list.layout_element.minHeight = 234

    elseif scene_type == SceneType.VIP_BOSS then
        local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
        if wb_other_cfg.boss_home_day_default_times == -1 then --无限
        	self.node_list["boss_count"]:SetActive(false)
            self.node_list.boss_list.layout_element.minHeight = 320
        else
            self.node_list.boss_list.layout_element.minHeight = 150
        end
    elseif scene_type == SceneType.XianJie_Boss then
        self.node_list["boss_count"]:SetActive(false)

    elseif scene_type == SceneType.PERSON_BOSS then
        self.node_list.boss_detail_info.layout_element.minHeight = 120
        self.node_list.reward_pa:SetActive(true)

    elseif scene_type == SceneType.KF_BOSS then
        self.node_list.boss_detail_info.layout_element.minHeight = 44
        self.node_list.boss_list.layout_element.minHeight = 150

    elseif scene_type == SceneType.CROSS_EVERYDAY_RECHARGE_BOSS then
        self.node_list.real_recharge_boss_info:SetActive(true)
        self.node_list["boss_count"]:SetActive(false)
    elseif SceneType.HUNDRED_EQUIP == scene_type then
        self.node_list.boss_detail_info:SetActive(false)
    end

    -- if scene_type == SceneType.KF_BOSS then
    --     self.node_list["xiezhu_count"]:SetActive(true)
    --     local xiezhu_count = BossXiezhuWGData.Instance:GetAssistCount()
    --     local xiezhu_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST)
    --     local color = xiezhu_count - xiezhu_num > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
    --     local xiezhu_num_text = string.format(Language.BossXiezhu.XiezhuNumColorText, color, xiezhu_count - xiezhu_num, xiezhu_count)
    --     -- self.node_list["xiezhu_num_text"].text.text = xiezhu_num_text
    --     self.node_list["xiezhu_num_per"].text.text = string.format("%s/%s", xiezhu_count - xiezhu_num, xiezhu_count)
    --     self.node_list.xiezhu_img:SetActive(true)
    -- else
    --     self.node_list["xiezhu_count"]:SetActive(false)
    --     self.node_list.xiezhu_img:SetActive(false)
    -- end

end

function MainUIView:OnBossCountAdd()
    --local quick_item_list = MainuiWGData.Instance:GetQuickItemHasList()
    local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType()
    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.PERSON_BOSS then
        local buy_count = BossWGData.Instance:GetPersonBossBuyCount()
        local can_buy = VipPower.Instance:GetPowerCfg(VipPowerId.vat_person_boss_extra_buy_times)
        can_buy = can_buy["param_" .. role_vip] or 0
        if buy_count >= can_buy then
            if cfg then
                BossWGCtrl.Instance:OpenQuickUseView(cfg)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.AddCountMax)
            end
        else
            BossWGCtrl.Instance:OpenBossVipTimesView(VipPowerId.vat_person_boss_extra_buy_times)
        end
    elseif scene_type == SceneType.VIP_BOSS then
        local vipboss_times_info = BossWGData.Instance:GetBossHomeEnterTimeInfo()
        local buy_count = vipboss_times_info.boss_home_day_vip_buy_kill_times
        local can_buy = VipPower.Instance:GetPowerCfg(VipPowerId.boss_home_buy_times)
        can_buy = can_buy["param_" .. role_vip] or 0
        if buy_count >= can_buy and cfg ~= nil then
            BossWGCtrl.Instance:OpenQuickUseView(cfg)
        else
            BossWGCtrl.Instance:OpenBossVipTimesView(VipPowerId.boss_home_buy_times)
        end
    elseif scene_type == SceneType.WorldBoss then
        BossWGCtrl.Instance:OpenWorldBossBuyView(VIP_LEVEL_AUTH_TYPE.WORLDBOSS_ENTER_TIMES)

    elseif scene_type == SceneType.KF_BOSS then
        BossWGCtrl.Instance:OpenWorldBossBuyView(VIP_LEVEL_AUTH_TYPE.MAMHUANG_ENTER_TIMES)
    else
        if cfg ~= nil then
            BossWGCtrl.Instance:OpenQuickUseView(cfg)
        end
    end
end

function MainUIView:GetCurIndex()
    return self.cur_boss_id
end

function MainUIView:SetCurIndex(boss_id)
    self.cur_boss_id = boss_id
    self:FlushAllHl()
end

function MainUIView:FlushAllHl()
    for k, v in pairs(self.boss_cell_list) do
        v:FlushHl()
    end
end

function MainUIView:FlushMonsterHl()
    for k, v in pairs(self.elite_cell_list) do
        v:FlushHl()
    end
end

function MainUIView:FindNoticeBossCell()
    for k, v in pairs(self.boss_cell_list) do
        if v:IsShowBossNotice() then
            return v
        end
    end
    return nil
end


------------
-- 打宝boss
------------
function MainUIView:OpenBossNuQiTips()
    local role_tip = RuleTip.Instance
    local scene_type = Scene.Instance:GetSceneType()
    local title_str, content_str
    if scene_type == SceneType.XianJie_Boss then
        title_str = Language.XianJieBoss.PlayTitle
	    local str = FuBenWGData.GetFbSceneConfig(scene_type)
        content_str = str.fb_desc
    elseif scene_type == SceneType.DABAO_BOSS then
        title_str = Language.Boss.PlayTitle
        content_str = Language.Boss.DaBaoPlayDes
    end

    role_tip:SetTitle(title_str or "")
	role_tip:SetContent(content_str or "")
end

function MainUIView:FlushBossNuQi()
    local num, end_time = BossWGData.Instance:GetDabaoBossAngryValue()
    num = num < 100 and num or 100
    local progress_value = num / 100
    self.node_list["boss_nuqi_num"].text.text = num
    --self.node_list["boss_nuqi_progress"].image.fillAmount = progress_value
    
    local scene_type = Scene.Instance:GetSceneType()
    self.node_list.boss_nuqi_cd_part:SetActive(num >= 100 and scene_type == SceneType.DABAO_BOSS)

    if num >= 100 then
        if self.is_open_drive_view then
            self.is_open_drive_view = false
        end

        if not CountDownManager.Instance:HasCountDown("boss_angry_count") and end_time > 0 then
            self.is_boss_angry_cd = true
            CountDownManager.Instance:AddCountDown("boss_angry_count",
                BindTool.Bind(self.UpdateBossNuQiCountDownTime, self),
                function()
                    self.is_boss_angry_cd = false
                    self.is_open_drive_view = false
                    self.node_list.boss_nuqi_cd_part:SetActive(false)
                end,
                end_time, nil, 0.5)
        end
    end
end

function MainUIView:RemoveBossNuQiCountDownTime()
    self.is_boss_angry_cd = false
    self.is_open_drive_view = false
    self.node_list.boss_nuqi_cd_part:SetActive(false)
	if CountDownManager.Instance:HasCountDown("boss_angry_count") then
		CountDownManager.Instance:RemoveCountDown("boss_angry_count")
	end
end

function MainUIView:UpdateBossNuQiCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
    self.node_list["boss_nuqi_cd"].text.text = last_time
    if last_time <= 5 and not self.is_open_drive_view then
        self.is_open_drive_view = true
        BossWGCtrl.Instance:OpenDriveView()
    end
end




------------
-- 个人boss
------------
function MainUIView:IsSingleBoss()
    return Scene.Instance:GetSceneType() == SceneType.PERSON_BOSS
end

function MainUIView:DoSingelBoss(data, scene_type, flush_time)
    if self:IsSingleBoss() then
        self.boss_data = data or self.boss_data
        local boss_id = 0
        local is_pass = false
        local drop_item_list = {}
        if scene_type == SceneType.PERSON_BOSS then
            local single_boss_info = BossWGData.Instance:GetSingleBossInfo()
            boss_id = single_boss_info.boss_id
            is_pass = FuBenWGData.Instance:IsPass()
            drop_item_list = single_boss_info.reward_t
        elseif scene_type == SceneType.WorldBoss then
            if nil == self.boss_data[1] then
                return
            end
            boss_id = self.boss_data[1].boss_id
            drop_item_list = self.boss_data[1].drop_item_list or {}
            local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(boss_id)
            if boss_info then
                is_pass = boss_info.next_refresh_time > TimeWGCtrl.Instance:GetServerTime()
            end
        end

        local boss_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
        if boss_cfg then
            local color = is_pass and COLOR3B.D_GREEN or COLOR3B.D_RED
            self.node_list.boss_condition.text.text = string.format(Language.Boss.GuideBossDec1, ToColorStr(boss_cfg.name, COLOR3B.D_PURPLE) , color, is_pass and 1 or 0)
            for k, v in pairs(drop_item_list) do
                if self.boss_reward_t[k] == nil then
                    self.boss_reward_t[k] = ItemCell.New(self.node_list.boss_reward)
                    self.boss_reward_t[k]:SetItemTipFrom(ItemTip.FROM_BOSS)
                end
                self.boss_reward_t[k]:SetData(v)
            end
        end
    elseif not flush_time then
        self.boss_data = data or self.boss_data
        print_log("左侧任务数据信息.data>>>>>>>>", data," self.boss_data魔王数据>>>>>>>>\n", self.boss_data)
        if self.boss_list and self.boss_list:GetActive() then
            self.boss_list.scroller:RefreshAndReloadActiveCellViews(true)
        end
    end
end

function MainUIView:DoPersonlBoss()
    local left_enter_num, max_enter_num = BossWGData.Instance:GetPersonalBossEnterInfo()
    local color = left_enter_num > 0 and COLOR3B.WHITE or COLOR3B.RED
    self.node_list.boss_count_text.text.text = ToColorStr(string.format("%s/%d", left_enter_num, max_enter_num), color)
    local buy_count = BossWGData.Instance:GetPersonBossBuyCount()
    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local vip_buy_cfg = VipPower.Instance:GetPowerCfg(VipPowerId.vat_person_boss_extra_buy_times) or {}
    local can_buy = vip_buy_cfg["param_" .. role_vip] or 0

    local scene_id = Scene.Instance:GetSceneId()
    local boss_cfg = BossWGData.Instance:GetPersonBossCfg(scene_id)
    if boss_cfg and boss_cfg.describe then
        self.node_list.person_boss_des.text.text = boss_cfg.describe-- string.format("%s", boss_cfg.describe)
    end
end




------------
--- vip Boss
------------
function MainUIView:DoVipBoss()
    local vipboss_times_info = BossWGData.Instance:GetBossHomeEnterTimeInfo()
    local wb_other_cfg =ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
    if wb_other_cfg.boss_home_day_default_times == -1 then --无限
        return
    end
    local max_times = wb_other_cfg.boss_home_day_default_times + vipboss_times_info.boss_home_day_item_add_kill_times + vipboss_times_info.boss_home_day_vip_buy_kill_times
    local cur_times = max_times - vipboss_times_info.boss_home_day_kill_times
    local color = cur_times > 0 and COLOR3B.WHITE or COLOR3B.RED
    self.node_list.boss_count_text.text.text = ToColorStr(string.format("%s/%d", cur_times, max_times), color)

    local buy_count = vipboss_times_info.boss_home_day_vip_buy_kill_times
    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local vip_buy_cfg = VipPower.Instance:GetPowerCfg(VipPowerId.boss_home_buy_times) or {}
    local can_buy = vip_buy_cfg["param_" .. role_vip] or 0
    self.node_list.boss_count_add_eff:SetActive(cur_times == 0 and can_buy > buy_count)
end

-- 刷新仙力
function MainUIView:FlushXianli()
    if self.node_list and self.node_list.energy_num then
        local scene_type = Scene.Instance:GetSceneType()
        local is_show_lingli = BossWGData.Instance:GetIsShowLingLi()
        self.node_list.up_tili_container:SetActive(scene_type == SceneType.VIP_BOSS and is_show_lingli > 0)
        --self.node_list["boss_kill_times"]:SetActive(scene_type == SceneType.VIP_BOSS)
        self.node_list["boss_kill_times"]:SetActive(scene_type == SceneType.VIP_BOSS and is_show_lingli < 1)

        -- local kill_times = BossAssistWGData.Instance:GetVipBossKillTimes()
        -- self.node_list["boss_kill_times"].text.text = kill_times .. "/" .. 100
        -- local boss_id = self:GetCurIndex()
        -- if not boss_id then
        --     local _, _, cur_boss_id = BossWGData.Instance:GetCurSelectBossID()
        --     boss_id = cur_boss_id
        -- end

        local scene_id = Scene.Instance and Scene.Instance:GetSceneId()
        local is_enough = BossAssistWGData.Instance:JudgeIsEnoughBossBySceneId(scene_id)
        self.node_list.boss_xianli_state:SetActive(is_show_lingli > 0 and scene_id > 0 and not is_enough)
        self.node_list.xianli_enough:SetActive(false)
        self.node_list.xianli_not_enough:SetActive(not is_enough and scene_id > 0)
        --self.node_list.xianli_enough.text.text = Language.Boss.XianLiEnough
        self.node_list.xianli_not_enough.text.text = Language.Boss.XianLinotEnough
        local img_str = is_enough and "a3_zjm_bl_xqcz_dk" or "a3_zjm_xqbz_dk"
        local bundle, asset = ResPath.GetF2MainUIImage(img_str)
        self.node_list["xianqi_enough_bg"].image:LoadSprite(bundle, asset, function()
            self.node_list["xianqi_enough_bg"].image:SetNativeSize()
        end)

        img_str = is_enough and "a3_zjm_xqcz" or "a3_zjm_xqbz"
        bundle, asset = ResPath.GetF2MainUIImage(img_str)
        self.node_list["xianqi_enough_img"].image:LoadSprite(bundle, asset, function()
            self.node_list["xianqi_enough_img"].image:SetNativeSize()
        end)

        local total_xianli = BossAssistWGData.Instance:GetBossXianli()
        local color = is_enough and COLOR3B.C8 or COLOR3B.C10
        local max_xianli = BossWGData.Instance:GetBossXianliMax()
        local num_str = ToColorStr(total_xianli, color) .. "/" .. max_xianli
        self.node_list.energy_num.text.text = num_str

        local can_get = BossWGData.Instance:JudgeCanGetBossXianli()
        self.node_list.boss_xianli_remind:SetActive(can_get)
    end
end

function MainUIView:FlushBossFirstKillRed()
    local first_kill_red = BossWGData.Instance:GetBossFirstKillRed()
    if self.node_list["firstkill_red"] then
        self.node_list["firstkill_red"]:SetActive(first_kill_red > 0)
    end
end

function MainUIView:OnClickExchangeBtn()
    --ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
    local show_index = BossWGData.Instance:GetCurFirstKillShowView()
    BossWGData.Instance:SetCurFirstKillShowView(show_index or 10)
    BossWGCtrl.Instance:OpenFirstKillView()
end

function MainUIView:OnClickLoadEveryDayShopBtn()
    local other_cfg = FashionExchangeShopWGData.Instance:GetOtherConfig()
	if other_cfg and other_cfg.boss_shop_panel then
		FunOpen.Instance:OpenViewNameByCfg(other_cfg.boss_shop_panel)
	end
end

function MainUIView:AddXianli()
    self.node_list.boss_xianli_state:SetActive(false)
    local can_get = BossWGData.Instance:JudgeCanGetBossXianli()
    if can_get then
        BossAssistWGCtrl.Instance:OpenGetEnergyView()
    else
        BossAssistWGCtrl.Instance:OpenQuickUseEnergyView()
    end
    BossAssistWGCtrl.Instance:CloseXianliInvateTip()
end

function MainUIView:OpenXianliRule()
   local title = Language.Boss.BossXianliTitile
   local content = Language.Boss.BossXianliContent
   RuleTip.Instance:SetContent(content, title)
end

-- 魔王仙藏按钮红点
function MainUIView:FlushLoadEveryDayShopRed()
    -- local load_everyday_shop_red = LordEveryDayShopWGData.Instance:MainLoadShopRemind()
    -- if self.node_list["load_everyday_shop_red"] then
    --     self.node_list["load_everyday_shop_red"]:SetActive(load_everyday_shop_red > 0)
    -- end
end

------------
-- 世界boss
------------
-- 精英怪
function MainUIView:ShowElite(scene_type)
	local scene_id = Scene.Instance:GetSceneId()
    if scene_type == SceneType.DABAO_BOSS then
        self.elite_data = BossWGData.Instance:GetEliteListBySceneId(scene_id)
        if self.node_list["btn_change_boss_list"] then
            self.node_list["btn_change_boss_list"]:SetActive(true)
        end
    elseif scene_type == SceneType.WorldBoss then
        self.elite_data = BossWGData.Instance:GetWorldEliteListBySceneId(scene_id)
        if self.node_list["btn_change_boss_list"] then
            self.node_list["btn_change_boss_list"]:SetActive(not IsEmptyTable(self.elite_data))
        end
    else
        self.elite_data = {}
        if self.node_list["btn_change_boss_list"] then
            self.node_list["btn_change_boss_list"]:SetActive(false)
        end
    end
end

function MainUIView:SetEliteTip(active)
    if self.node_list.elite_list and self.elite_tip_state ~= active then
        self.node_list.elite_list:SetActive(active)
        self.elite_tip_state = active
    end
end

function MainUIView:DoWorldBoss()
    local boss_list = BossWGData.Instance:GetCurBossListBySceneId(Scene.Instance:GetSceneId())
    local layer = 1
    if boss_list and boss_list[1] then
        layer = boss_list[1].layer
    end
    local boss_enter_cfg = BossWGData.Instance:GetWorldBossEnterCfgByLayer(layer)

    if boss_enter_cfg.is_free == 1 then
        self.node_list.boss_count_text.text.text = Language.Boss.WorldBossTimesStr
    else
        local world_times_info = BossWGData.Instance:GetWorldBossEnterTimeInfo()
        local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
        self.wb_max_times = wb_other_cfg.world_boss_day_default_times + world_times_info.world_boss_day_extra_kill_times
        self.wb_cur_times = self.wb_max_times + world_times_info.world_boss_flush_can_kill_times - world_times_info.world_boss_day_kill_times
        local color = self.wb_cur_times > 0 and COLOR3B.WHITE or COLOR3B.RED
        self.node_list.boss_count_text.text.text = ToColorStr(string.format("%s/%d", self.wb_cur_times, self.wb_max_times), color)
    end

    self.node_list.boss_count_add_eff:SetActive(false)
end


------------
-- 跨服boss
------------
function MainUIView:DoKFBoss()
    local times, max_times = BossWGData.Instance:GetCrossBossTire()
    local color = times < max_times and COLOR3B.WHITE or COLOR3B.RED
    self.node_list["boss_count_text"].text.text = ToColorStr(string.format("%s/%d", max_times - times, max_times), color)
end


------------
-- 仙界boss
------------
function MainUIView:DoXianJieBoss()
    local num, end_time = XianJieBossWGData.Instance:GetXianJieTiredValue()
    num = num < 100 and num or 100
    local progress_value = num / 100
    local color = num < 100 and COLOR3B.C8 or COLOR3B.C10
    self.node_list["boss_nuqi_num"].text.text = ToColorStr(num, color)
    --self.node_list["boss_nuqi_progress"].image.fillAmount = progress_value
    BossWGCtrl.Instance:FlushXianJieTiredView()
end

function MainUIView:OnClickMailTog(isOn)
    if isOn then
        self.node_list.btn_task_mail.toggle.isOn = false

        local scene_logic = Scene.Instance:GetSceneLogic()

        if not scene_logic:CanOpenMail() then
            return
        end
    
        SocietyWGCtrl.Instance:OpenMailView()
    end
end

function MainUIView:OnClickShouNaTog(isOn)
end

function MainUIView:CheckTaskLeftTogRemind(remind_name, num)
    self:CheckTaskShouNaRemind()
    self:CheckTaskMailTogRemind(remind_name, num)
    self:CheckTaskRumorRemind(remind_name, num)
end

-- 后续如果加入活动到这个面板中需要添加红点按处理    固定功能按钮处理 TASK_SHOWNA_PANEL_FUNC_CFG
function MainUIView:CheckTaskShouNaRemind()
	local is_show_task_shouna_remind = false

	-- 固定功能 + 活动
	-- if not IsEmptyTable(self.chat_activity_btn_list) then
	-- 	for act_id, v in pairs(self.chat_activity_btn_list) do
	-- 		if v:RedPointIsShow() then
	-- 			is_show_task_shouna_remind = true
	-- 			break
	-- 		end
	-- 	end
	-- end

	if not is_show_task_shouna_remind then
		for k, v in pairs(TASK_SHOWNA_PANEL_FUNC_CFG) do
            if RemindManager.Instance:GetRemind(v.remind) > 0 then
                is_show_task_shouna_remind = true
            end
			break
		end
	end

	self:ChengeTaskShouNaBtnRemindEnable(is_show_task_shouna_remind)
end

function MainUIView:CheckTaskRumorRemind(remind_name, num)
    if remind_name == RemindName.CustomizedRumorsView then
        if self.node_list.customized_rumors_remind then
            self.node_list.customized_rumors_remind:SetActive(num > 0)   
        end
    end
end

function MainUIView:ChengeTaskShouNaBtnRemindEnable(enable)
	if self.node_list.btn_task_shouna_red then
		self.node_list.btn_task_shouna_red:CustomSetActive(enable)
	end
end

function MainUIView:CheckTaskMailTogRemind(remind_name, num)
    if remind_name == RemindName.SocietyFriends then
        if self.node_list.btn_task_mail_red then
            self.node_list.btn_task_mail_red:SetActive(num > 0)   
        end
    end
end

-- function MainUIView:PlayTaskButtonTweenReversal(enable)
--     if not self.has_load_callback then
--         return
--     end

--     if self.task_button_tween_reversal_timer ~= nil then
--         GlobalTimerQuest:CancelQuest(self.task_button_tween_reversal_timer)
--         self.task_button_tween_reversal_timer = nil
--     end

--     self:PlayTaskButtonTween(enable, true)
--     self.task_button_tween_reversal_timer = GlobalTimerQuest:AddDelayTimer(function ()
--         self:PlayTaskButtonTween(not enable)
--     end, 0.4)
-- end

function MainUIView:FlushActiveTaskItem()
    if TaskWGCtrl and TaskWGCtrl.Instance then
        TaskWGCtrl.Instance:UpdateTaskPanelShow()
    end

    -- self:FlushCountryBtnShow()
end


------------
---小飞鞋
------------
local guide_first = true
function MainUIView:GetFlyShoesGuide()
    if self.task_list == nil then return end
    if guide_first then
        guide_first = false
        self.task_list.scroller:ReloadData(0)
    end
    for _, v in pairs(self.task_cell_list) do
        if v:GetActive() and v:IsZhuTask() then
            return v.view, BindTool.Bind(v.OnClickFly, v)
        end
    end
end

------------
--- 在线奖励
------------
function MainUIView:InitOnlineReward()
    self.open_big_online_reward = true
    XUI.AddClickEventListener(self.node_list["btn_online_reward_big_go"], BindTool.Bind(self.OpenOnlineRewardView, self))
    XUI.AddClickEventListener(self.node_list["btn_online_reward_big_close"], BindTool.Bind(self.CloseBigOnlineRewardBtn, self))
    XUI.AddClickEventListener(self.node_list["online_reward_small"], BindTool.Bind(self.OpenOnlineRewardView, self))
end

function MainUIView:OpenOnlineRewardView()
    ViewManager.Instance:Open(GuideModuleName.OnlineRewardView)
end

function MainUIView:CloseBigOnlineRewardBtn()
    self.open_big_online_reward = false
    local tween = self.node_list["online_reward_big"].transform:DOScale(Vector3(0,0,0), 0.5)
    tween:SetEase(DG.Tweening.Ease.OutBack)
    tween:OnComplete(function ()
        self.node_list["online_reward_big"]:CustomSetActive(false)
        self.node_list["online_reward_small"]:CustomSetActive(true)
        local reward_status = OnlineRewardWGData.Instance:GetRewardSatus()
        if reward_status == REWARD_STATE_TYPE.CAN_FETCH then
            self.node_list["online_reward_small_icon"].animator.enabled = true
            self.node_list["online_reward_small_icon"].animator:SetBool("is_shake", true)
        else
            self.node_list["online_reward_small_icon"].animator.enabled = false
        end
	end)
end

function MainUIView:FlushOnlineRewardPart(param_t)
    if not param_t then
		return
	end

    self:CleanOnlineRewardTimer()
    if not param_t.is_open then
        self.node_list["online_reward_part"]:CustomSetActive(false)
        return
    end

    self.node_list["online_reward_part"]:CustomSetActive(true)
    if self.open_big_online_reward then
        Transform.SetLocalScaleXYZ(self.node_list["online_reward_big"].transform, 1, 1, 1)
        self.node_list["online_reward_big"]:CustomSetActive(true)
    else
        self.node_list["online_reward_small"]:CustomSetActive(true)
    end

    self.node_list["online_reward_small_remind"]:CustomSetActive(param_t.is_remind)
    if param_t.is_remind then
        self.node_list["online_reward_small_icon"].animator.enabled = true
        self.node_list["online_reward_small_icon"].animator:SetBool("is_shake", true)
        self.node_list["btn_online_reward_big_go_image"].animator.enabled = true
    else
        self.node_list["online_reward_small_icon"].animator.enabled = false
        self.node_list["btn_online_reward_big_go_image"].animator.enabled = false
    end

    local remain_time = OnlineRewardWGData.Instance:GetRemainTime()
	self:ShowOnlineRewardTime(remain_time)
	if remain_time > 0 then
		self.online_reward_timer = CountDown.Instance:AddCountDown(remain_time, 0.5,
		function(elapse_time, total_time)
			local time = math.floor(total_time - elapse_time)
			self:ShowOnlineRewardTime(time)
		end,
		function()
			self:ShowOnlineRewardTime(0)
		end)
	end
end

function MainUIView:CleanOnlineRewardTimer()
    if self.online_reward_timer and CountDown.Instance:HasCountDown(self.online_reward_timer) then
        CountDown.Instance:RemoveCountDown(self.online_reward_timer)
        self.online_reward_timer = nil
    end
end

function MainUIView:ShowOnlineRewardTime(time)
	local time_str = TimeUtil.FormatSecondDHM4(time)
	if self.node_list["online_reward_big_time"] then
		self.node_list["online_reward_big_time"].text.text = time_str
	end

	if self.node_list["online_reward_small_time"] then
		self.node_list["online_reward_small_time"].text.text = time_str
	end
end

----------------------------今日活动--------------------------
function MainUIView:FlushTaskToDayActInfo()
    local calendar_cfg = CalendarWGData.Instance:GetCalendarActivityNoticeCfg()
    local has_act_data = not IsEmptyTable(calendar_cfg)

    self.node_list.task_no_act_tip:CustomSetActive(not has_act_data)
    self.node_list.task_today_act_list:CustomSetActive(has_act_data)
    
    if has_act_data then
        self.task_today_act_list:SetDataList(calendar_cfg)
    end
end

function MainUIView:OnClickSeeCalendarBtn()
    ViewManager.Instance:Open(GuideModuleName.BiZuo, BiZuoView.TabIndex.BTT)
end

-- 普通场景下，如果最近活动开启前还剩半小时，10分钟，5分钟， 自动切换到左侧这个活动面板
function MainUIView:UpdateMainTaskTodayActTip()
    self:ScheduleTodayActTipTimer()
end

function MainUIView:JumpToMainTaskTodayAct()
    if self.node_list.btn_task_todayact then
        self.node_list.btn_task_todayact:CustomSetActive(true)
        self.node_list.btn_task_todayact.toggle.isOn = true
    end
end

-- 计算下一次需要自动切换到“今日活动”面板的延迟时间（秒）
function MainUIView:ComputeNextTodayActTipDelay()
    local calendar_cfg = CalendarWGData.Instance:GetCalendarActivityNoticeCfg()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local best_delay = nil
    local best_info = nil

    if not IsEmptyTable(calendar_cfg) then
        for _, v in pairs(calendar_cfg) do
            if v.cfg and v.open_type == CalendarWGData.OPEN_TYPE.TODAY_OPEN then
                local act_type = v.cfg.act_type or -1
                if act_type > 0 and v.act_time_open_stamp then
                    local time_diff = v.act_time_open_stamp - server_time
                    local targets = {30 * 60, 10 * 60, 5 * 60}
                    for _, target in ipairs(targets) do
                        local d = time_diff - target
                        if d and d >= 0 then
                            if best_delay == nil or d < best_delay then
                                best_delay = d
                                best_info = { act_type = act_type, target = target, open_at = v.act_time_open_stamp }
                            end
                        end
                    end
                end
            end
        end
    end

    if best_delay ~= nil then
        if best_delay < 0.1 then best_delay = 0.1 end
        return best_delay, best_info
    end

    -- 无待触发点时，定期复查（例如新增活动、等级变化等会刷新配置）
    return 60, nil
end

function MainUIView:CancelTodayActTipTimer()
    if self.today_act_jump_to_tip_timer then
        GlobalTimerQuest:CancelQuest(self.today_act_jump_to_tip_timer)
        self.today_act_jump_to_tip_timer = nil
    end
    self.next_today_act_tip = nil
end

function MainUIView:ScheduleTodayActTipTimer()
    self:CancelTodayActTipTimer()
    local delay, info = self:ComputeNextTodayActTipDelay()
    self.next_today_act_tip = info
    if delay and delay > 0 then
        self.today_act_jump_to_tip_timer = GlobalTimerQuest:AddDelayTimer(function()
            self:OnTodayActTipTimer()
        end, delay)
    end
end

function MainUIView:OnTodayActTipTimer()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    if self:CanShowCalendar() then
        local cur_scene_type = Scene.Instance:GetSceneType()
        if cur_scene_type == SceneType.Common and self.node_list.btn_task_todayact and not self.node_list.btn_task_todayact.toggle.isOn then
            local expect_act_type = self.next_today_act_tip and self.next_today_act_tip.act_type or -1
            if expect_act_type and expect_act_type > 0 then
                local calendar_cfg = CalendarWGData.Instance:GetCalendarActivityNoticeCfg()
                if not IsEmptyTable(calendar_cfg) then
                    for _, v in pairs(calendar_cfg) do
                        local cfg = v.cfg or {}
                        if v.open_type == CalendarWGData.OPEN_TYPE.TODAY_OPEN and cfg.act_type == expect_act_type then
                            self:JumpToMainTaskTodayAct()
                            break
                        end
                    end
                end
            end
        end
    end

    -- 触发后继续安排下一次
    self:ScheduleTodayActTipTimer()
end

----------------------------装备收集--------------------------

function MainUIView:GetEquipTargetFuncOpen()
    local is_equip_collect_open = FunOpen.Instance:GetFunIsOpened(FunName.MainEquipTargetView)

    local list_data = EquipTargetWGData.Instance:GetEquipBigType()
    local active_count = false
    for k_1, v_1 in ipairs(list_data) do
        local list = EquipTargetWGData.Instance:GetEquipList(v_1.suit_index)
        for k_2, v_2 in ipairs(list) do
            if v_2 and tonumber(v_2) > 0 then

                local state = EquipTargetWGData.Instance:GetEquipSortStateByIndex(v_1.suit_index, k_2, true)
                if not state then
                    active_count = true
                    break
                end
            end
        end

        if active_count then
            break
        end
    end

    return is_equip_collect_open and active_count
end

function MainUIView:EquipCollectFlushInfo()
    if not self.node_list.Image_equip_collect_progress then
        return
    end

    local is_open = self:GetEquipTargetFuncOpen()
    local list_data = EquipTargetWGData.Instance:GetEquipBigType()
    local not_active_count = 0
    local max_count = 0
    local vaule_sum = 0
    local name = ""
    for k_1, v_1 in ipairs(list_data) do
        local list = EquipTargetWGData.Instance:GetEquipList(v_1.suit_index)
        vaule_sum = 0
        for k_2, v_2 in ipairs(list) do
            if v_2 and tonumber(v_2) > 0 then
                local state = EquipTargetWGData.Instance:GetEquipSortStateByIndex(v_1.suit_index, k_2, true)
                if state then
                    not_active_count = not_active_count + 1
                end

                name = v_1.suit_name
                vaule_sum = vaule_sum + 1
            end
        end

        if not_active_count > 0 then
            max_count = vaule_sum
            break
        end

        if k_1 == #list_data and max_count == 0 then
            max_count = #list
        end
    end

    -- local is_xiuzhen_open = TianShenJuexingWGData.Instance:GetMainTianShenAwakenBtnState()
    local scene_type = Scene.Instance:GetSceneType()
    local btn_show = is_open and (scene_type ~= SceneType.LingHunGuangChang) --not is_xiuzhen_open and 
    if btn_show then
        self.node_list.Image_equip_collect_progress.image.fillAmount = (max_count - not_active_count) / max_count
        self.node_list.text_equip_collect_progress.text.text = string.format("%s:%s/%s", name, max_count - not_active_count, max_count)
        local red_show = EquipTargetWGData.Instance:GetEquipTargetAllRemind()
        self.node_list.image_equip_collect_red:SetActive(red_show > 0)

        -- 特殊处理 一阶和二阶没有集齐的时候显示特效 suit_index:1,3
        local is_show_effect = false
        for suit_index = 1, 3, 2 do
            if not EquipTargetWGData.Instance:IsAllActive(suit_index) then
                is_show_effect = true
            end
        end
        
        self.node_list.equip_collect_effect:CustomSetActive(is_show_effect)
    end

    self.node_list.btn_equip_collect:SetActive(btn_show)
end

function MainUIView:OnClickKFBossGather()
    BossWGCtrl.Instance:OpenKFBossGatherInfoView()
end

function MainUIView:FlushTransferEquipCollectInfo()
    if not self.node_list.btn_transfer_equip_collect then
        return
    end

    local fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.ZhuanSheng)
    local show_suit_index = TransFerWGData.Instance:GetCurShowSuitIndex()
    local show_btn = fun_is_open and show_suit_index >= 0

	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    self.node_list.btn_transfer_equip_collect:SetActive(show_btn and scene_cfg.is_show_zhuangbeishouji == 0)
    if show_btn and scene_cfg.is_show_zhuangbeishouji == 0 then
        local all_num, progress_num = TransFerWGData.Instance:GetSuitCollectProgress(show_suit_index)
        local suit_cfg = TransFerWGData.Instance:GetEquipCollectCfgBySuit(show_suit_index)
        local name = suit_cfg.suit_name
        self.node_list.transfer_equip_collect_progress.image.fillAmount = progress_num / all_num
        self.node_list.transfer_equip_collect_text.text.text = string.format("%s:%s/%s", name, progress_num, all_num)
    end
end

-----------------------------------------飞仙书/修仙圆梦_START-------------------------------------------
function MainUIView:SetSpecialACTPanelToggle(is_on)
    if  self.node_list.btn_task_special_act then
        self.node_list.btn_task_special_act.toggle.isOn = is_on
    end
end

function MainUIView:SetSpecialACTPanelBtnState(state)
    if self.node_list.btn_task_special_act then
        self.node_list.btn_task_special_act:CustomSetActive(state)
    end
end

function MainUIView:FlushSpecialACTPanelState()
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type == SceneType.Common then
        self:SetSpecialACTPanelState(true)
    else
        self:SetSpecialACTPanelState(false)
    end
end

function MainUIView:SetSpecialACTPanelState(state)
    local is_show_fxs_tip = self:IsShowMainUIFXSTip()
    local is_show_xxym_tip = false--self:IsShowMainUIXXYMTip()            --功能屏蔽.

    if state and (is_show_fxs_tip or is_show_xxym_tip) then
        self.node_list.btn_task_special_act.toggle.isOn = true
        self.node_list.btn_task_special_act:CustomSetActive(true)

        if is_show_fxs_tip then
            self:FlushMainUIFXSTip()
            self:ReleaseMainUIXXYMTip()
            return
        else
            self:ReleaseMainUIFXSTip()
        end

        if is_show_xxym_tip then
            self:FlushMainUIXXYMTip()
            self:ReleaseMainUIFXSTip()
            return
        else
            self:ReleaseMainUIXXYMTip()
        end
    else
        self.node_list.btn_task_special_act.toggle.isOn = false
        self.node_list.btn_task_special_act:CustomSetActive(false)
        self:ReleaseMainUIFXSTip()
        self:ReleaseMainUIXXYMTip()
    end
end

-- 是否显示修仙圆梦
function MainUIView:IsShowMainUIXXYMTip()
    if self:IsShowMainUIFXSTip() then
        return false
    end

    local is_open_act = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XIUZHEN_ROAD)
    local is_xiuzhen_open = FunOpen.Instance:GetFunIsOpened(FunName.XiuZhenRoadView)

    return is_open_act and is_xiuzhen_open
end

-- 是否显示飞仙书
function MainUIView:IsShowMainUIFXSTip()
    local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.XiuXianShiLian)
    local max_chapter_id = XiuXianShiLianWGData.Instance:GetMaxUnLockChapterId()
    local all_capter_is_complete = XiuXianShiLianWGData.Instance:GetAllCapterIsComplete()
    return fun_open and max_chapter_id > 0 and not all_capter_is_complete
end

-- 修仙圆梦
function MainUIView:FlushMainUIXXYMTip()
    if not self.main_xxym_tip then
        self.main_xxym_tip = MainViewXXYMACTTip.New(self.node_list.special_act_root_xxym)
    end

    self.main_xxym_tip:Flush()
    self.node_list.special_act_root_xxym:CustomSetActive(true)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.special_act_task_panel.rect)
end

function MainUIView:ReleaseMainUIXXYMTip()
    if self.main_xxym_tip then
        self.main_xxym_tip:DeleteMe()
        self.main_xxym_tip = nil
    end

    self.node_list.special_act_root_xxym:CustomSetActive(false)
end


-- 飞仙书
function MainUIView:FlushMainUIFXSTip()
    if not self.main_fxs_tip then
        self.main_fxs_tip = MainViewFXSTip.New(self.node_list.special_act_root_fxs)
    end

    self.main_fxs_tip:Flush()
    self.node_list.special_act_root_fxs:CustomSetActive(true)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.special_act_task_panel.rect)
end

function MainUIView:ReleaseMainUIFXSTip()
    if self.main_fxs_tip then
        self.main_fxs_tip:DeleteMe()
        self.main_fxs_tip = nil
    end

    self.node_list.special_act_root_fxs:CustomSetActive(false)
end

function MainUIView:OnClickBossInvasionPrivilege()
    BOSSInvasionWGCtrl.Instance:OpenPrivilegeView()
end

function MainUIView:FlushBossInvasionPrivilege()
    if self.node_list.desc_boss_invasion_privilege_status then
        local is_privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()
        self.node_list.desc_boss_invasion_privilege_status.text.text = is_privilege and Language.BOSSInvasion.PrivilegeActive or Language.BOSSInvasion.PrivilegeNoActive

        if is_privilege then
            if self.boss_invasion_privilege_tween_shake then
                self.boss_invasion_privilege_tween_shake:Kill()
                self.boss_invasion_privilege_tween_shake = nil
            end
        end
    end
end

function MainUIView:OnClickBossInvasionQuality()
    BOSSInvasionWGCtrl.Instance:OpenQualityView()
end

function MainUIView:FlushBossInvasionQuality()
    if self.node_list.desc_boss_invasion_quality then
        local color_cfg = BOSSInvasionWGData.Instance:GetCurBossColorCfg()
        self.node_list.desc_boss_invasion_quality.text.text = color_cfg and color_cfg.color_name or ""
    end
end

--宣传图
function MainUIView:FlushTaskAdvanceNotice()
    if self.is_can_flush_advance_notice then
        local data_list = MainuiWGData.Instance:GetFPAdvanceNoticeList()
        if not IsEmptyTable(data_list) then
            self.task_advance_notice:SetData(data_list)
        end

        self:SetAdvanceNoticeBtnState(not IsEmptyTable(data_list))

        if self.advance_notice_time_quest then
            GlobalTimerQuest:CancelQuest(self.advance_notice_time_quest)
            self.advance_notice_time_quest = nil
        end

        local final_time = MainuiWGData.Instance:GetAdvanceNoticePopFinalTime()
        local day_start_time = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
        local cur_time = TimeWGCtrl.Instance:GetServerTime()
        local time = cur_time - day_start_time
        local times = math.ceil(time / 60)

        if not IsEmptyTable(data_list) and time < final_time then
            self:OnUpdateAdvanceNotice()
            self.advance_notice_time_quest = GlobalTimerQuest:AddTimesTimer(
                BindTool.Bind(self.OnUpdateAdvanceNotice, self), 60, times)
        end
    end
end

function MainUIView:OnUpdateAdvanceNotice()
    local day_start_time = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local time = cur_time - day_start_time
    local cfg = MainuiWGData.Instance:GetAdvanceNoticePopTimeCfg()
    local data_list = MainuiWGData.Instance:GetFPAdvanceNoticeList()
    local scene_type = Scene.Instance:GetSceneType()

    if not IsEmptyTable(data_list) and self.node_list and not self.node_list.advance_notice_task_panel.gameObject.activeSelf
        and scene_type == SceneType.Common then
        for k, v in ipairs(cfg) do
            if (time < v.pop_time and (time + 60) >= v.pop_time) then
                self.node_list.btn_task_advance_notice.toggle.isOn = true
                break
            end
        end
    end
end

function MainUIView:OnClickTaskAdvanceNotice(isOn)
    if isOn then
        self:FlushTaskAdvanceNotice()
    end
end

-----------------------------------------跨服藏宝_START-------------------------------------------
-- 切换跨服藏宝
function MainUIView:OnClickCrossLingzhu(isOn)
    if isOn then
        self:FlushrossLingZhuPanel(true)
    end

    self.node_list.task_btn_trans:CustomSetActive(not isOn)
end

-- 刷新跨服藏宝
function MainUIView:FlushrossLingZhuPanel(is_force)
    local is_show = self.node_list.btn_task_cross_lingzhu.toggle.isOn
    if is_force then
        self.node_list.btn_task_cross_lingzhu.toggle.isOn = true
    end

    if not self.cross_lingzhu_panel then
        self.cross_lingzhu_panel = MainCrossLingZhuTaskPanel.New(self.node_list.cross_lingzhu_task_panel)    
    end

    if (is_show or is_force) and self.cross_lingzhu_panel then
        self.cross_lingzhu_panel:Flush(0, {is_force = is_force})
    end

    local red = CrossTreasureWGData.Instance:GetCrossTreasureLinzuRemind() == 1 or CrossTreasureWGData.Instance:GetCrossTreasureBeastRemind() == 1
    self.node_list.btn_task_cross_lingzhu_red:CustomSetActive(red)
end

-- 设置跨服藏宝状态
function MainUIView:SetCrossLingZhuPanelState(status)
    if not self.cross_lingzhu_panel then
        return
    end

    self.cross_lingzhu_panel:SetVisible(status)
end

-- 销毁跨服藏宝
function MainUIView:ReleaseCrossLingZhuRender()
    if self.cross_lingzhu_panel then
        self.cross_lingzhu_panel:DeleteMe()
        self.cross_lingzhu_panel = nil
    end
end
-----------------------------------------跨服藏宝_END-------------------------------------------

----------------------------------MainViewXXYMACTTip-------------------------------
MainViewXXYMACTTip = MainViewXXYMACTTip or BaseClass(BaseRender)

function MainViewXXYMACTTip:LoadCallBack()
    self.cur_show_index = 1

    local miji_cfg = XiuZhenRoadWGData.Instance:GetMiJiShowItemInfo()
    if not IsEmptyTable(miji_cfg) then
        local eff_bundle, eff_asset = ResPath.GetUIEffect(miji_cfg.ui_effect_asset)
        self.node_list["model_display3"]:ChangeAsset(eff_bundle, eff_asset)
    end

    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickCloseTip, self))
    XUI.AddClickEventListener(self.node_list.btn_enter, BindTool.Bind(self.OnClick, self))
    XUI.AddClickEventListener(self.node_list.btn_jump, BindTool.Bind(self.OnClickJump, self))
end

function MainViewXXYMACTTip:__delete()
    if self.model_display1 then
        self.model_display1:DeleteMe()
        self.model_display1 = nil
    end

    if self.model_display2 then
        self.model_display2:DeleteMe()
        self.model_display2 = nil
    end

    if self.notice_timer_quest then
		GlobalTimerQuest:CancelQuest(self.notice_timer_quest)
		self.notice_timer_quest = nil
	end

    self.cur_show_index = nil
    self.info = nil
end

function MainViewXXYMACTTip:SetModelInfo()
    local other_cfg = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuOtherCfg()

    if not self.model_display1 then
		self.model_display1 = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_display1"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.XS,
			can_drag = false,
            camera_depth = -3,
		}
		
		self.model_display1:SetRenderTexUI3DModel(display_data)
        self.model_display1:SetRTAdjustmentRootLocalScale(0.25)

        -- local bundle, asset = ResPath.GetBianShenModel(other_cfg.pet_id)
        if not other_cfg.index then
            return
        end

        self.model_display1:SetTianShenModel(other_cfg.pet_id, other_cfg.index, false, nil, SceneObjAnimator.UiIdle)
	end

	if not self.model_display2 then
		self.model_display2 = OperationActRender.New(self.node_list["model_display2"])
		if other_cfg.chibang_id > 0 then
			local data = {}
			data.item_id = other_cfg.chibang_id
			data.render_type = 0
			data.model_adjust_root_local_scale = 0.3
            data.model_adjust_root_local_position =  Vector3(0, 0, 0)
            data.model_adjust_root_local_rotation =  Vector3(0, 0, 0)
            data.rt_scale_type = ModelRTSCaleType.XS
            data.can_drag = false
            data.camera_depth = -3
            data.hide_model_block = true
			data.model_click_func = function()
				TipWGCtrl.Instance:OpenItem({ item_id = data.item_id })
			end
			self.model_display2:SetData(data)
		end
	end


    if not self.notice_timer_quest then
		self.notice_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.OnFlushModelSwitch, self), 5)
	end
end

function MainViewXXYMACTTip:OnFlushModelSwitch()
    self.cur_show_index = self.cur_show_index + 1

    if self.cur_show_index > 3 then
        self.cur_show_index = 1
    end

    for i = 1, 3 do
        self.node_list["model_display" .. i]:CustomSetActive(i == self.cur_show_index) 
    end
end

function MainViewXXYMACTTip:OnFlush()
    local info, complse_num, max_num = XiuZhenRoadWGData.Instance:GetMainViewXXYMACTTipData()
    self.info = info

    if IsEmptyTable(info) then
        return
    end

    self:SetModelInfo()

    -- self.node_list.desc_cur_task.text.text = complse_num
    -- self.node_list.desc_next_task.text.text = max_num
    self.node_list.desc_task_pro.text.text = complse_num .. "/" .. max_num
    self.node_list.slider_pro.slider.value = complse_num / max_num

    -- self.node_list.desc_title.text.text = info.task_name
    self.node_list.desc_task_content.text.text = info.task_desc

    -- 任务信息

    -- local xiuzhen_title_name, complse_num, max_num = XiuZhenRoadWGData.Instance:GetShowMainUIInfo()
    -- local xiuzhen_remind = XiuZhenRoadWGData.Instance:IsShowXiuZhenRoadRedPoint()
end

function MainViewXXYMACTTip:OnClick()
    local error_str = nil
    local is_open_act = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XIUZHEN_ROAD)
    local is_xiuzhen_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuZhenRoadView)
    if not is_open_act then
        error_str = Language.XiuZhenRoad.LockTips1
    elseif not is_xiuzhen_open then
        local fun_cfg = FunOpen.Instance:GetFunByName(ACTIVITY_TYPE.XIUZHEN_ROAD)
        error_str = string.format(Language.XiuZhenRoad.LockTips2, fun_cfg.trigger_param)
    end

    if error_str then
        SysMsgWGCtrl.Instance:ErrorRemind(error_str)
        return
    end

    ViewManager.Instance:Open(GuideModuleName.XiuZhenRoadView, nil, "all", {})
end

function MainViewXXYMACTTip:OnClickJump()
    if IsEmptyTable(self.info) then
       return 
    end

    if self.info.open_panel then
        local item_id
		if self.info.task_type == 56 then
			item_id = self.info.param_2
		end

        local open_name = self.info.open_panel
		local open_limit_type = self.info.open_limit_type
        if open_limit_type == 1 then
            local open_list = Split(self.info.open_panel, "|")
            local sex = RoleWGData.Instance:GetRoleSex()
            local def_index = sex == GameEnum.MALE and 1 or 2
            open_name = open_list[def_index] and open_list[def_index] or open_list[1]
            open_name = open_name ~= "" and open_name or open_list[1]
        end
        FunOpen.Instance:OpenViewNameByCfg(open_name, item_id)
    end
end

function MainViewXXYMACTTip:OnClickCloseTip()
    MainuiWGCtrl.Instance:SetSpecialACTPanelToggle(false)
end

---------------------MainViewFXSTip--------------------------
MainViewFXSTip = MainViewFXSTip or BaseClass(BaseRender)

function MainViewFXSTip:LoadCallBack()
    if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_display"])
	end

    XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
    XUI.AddClickEventListener(self.node_list.btn_enter, BindTool.Bind(self.OnClick, self))
    XUI.AddClickEventListener(self.node_list.btn_jump, BindTool.Bind(self.OnClickJump, self))
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickCloseTip, self))

    self.cur_fxs_rander_type = -1
    self.cur_fxs_show_model_id = 0
    self.cur_task_cfg = {}
end

function MainViewFXSTip:__delete()
    if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
    end

    self.cur_task_cfg = nil
end

function MainViewFXSTip:OnFlush()
    local cur_chapter_id = XiuXianShiLianWGData.Instance:GetLastChapterIndex()
    local data = XiuXianShiLianWGData.Instance:GetXiuXianShiLianInfoByCaptherId(cur_chapter_id)
    if data then
        self.node_list.slider_pro.slider.value = data.cur_task_index / data.all_task_count
        self.node_list.desc_task_pro.text.text = string.format(Language.Exchange.Expend, data.cur_task_index, data.all_task_count)
    end

    local task_cfg = XiuXianShiLianWGData.Instance:GetNoCompleteTaskCfgByCapterID(cur_chapter_id)
    if task_cfg then
        local task_data = XiuXianShiLianWGData.Instance:GetTaskProgressByTaskType(task_cfg.task_type)
        local progress = task_data and task_data.progress or 0
        local color = progress >= task_cfg.param1 and COLOR3B.C8 or COLOR3B.C10
        self.node_list.desc_task_content.text.text = string.format(Language.XiuXianShiLian.NewTaskDes, task_cfg.task_des, color, progress, task_cfg.param1)
        self.node_list.desc_title.text.text = Language.XiuXianShiLian.TaskTitle
    else
        self.node_list.desc_title.text.text = data and data.chapter_name or ""
        self.node_list.desc_task_content.text.text = data and data.chapter_des or ""
    end

    self.cur_task_cfg = task_cfg
    self:FlushModel(cur_chapter_id)
end

function MainViewFXSTip:FlushModel(chapter_id)
    local display_data = XiuXianShiLianWGData.Instance:GetModelShowInfo(chapter_id)
    if not IsEmptyTable(display_data) then
        if self.cur_fxs_rander_type == display_data.render_type and self.cur_fxs_show_model_id == display_data.show_item_id then
            if self.cur_fxs_rander_type == OARenderType.RoleModel and self.cur_fxs_show_model_id > 0 then
                self.model_display:ActModelPlayLastAction()
            end
            return
        end

        self.cur_fxs_rander_type = display_data.render_type
        self.cur_fxs_show_model_id = display_data.show_item_id
        local mainui_display_data = {}
        mainui_display_data.render_type = display_data.render_type
        mainui_display_data.item_id = display_data.item_id
        mainui_display_data.model_item_id_list = display_data.model_item_id_list
        mainui_display_data.should_ani = display_data.should_ani
        mainui_display_data.bundle_name = display_data.bundle_name
        mainui_display_data.asset_name = display_data.asset_name
        mainui_display_data.model_adjust_root_local_scale = display_data.mainui_display_scale
        mainui_display_data.model_adjust_root_local_position = display_data.mainui_display_pos
        mainui_display_data.model_adjust_root_local_rotation = display_data.mainui_rotation
        mainui_display_data.rt_scale_type = ModelRTSCaleType.XS
        mainui_display_data.can_drag = false
        mainui_display_data.camera_depth = -3
        mainui_display_data.hide_model_block = true
        local position
        local rotation
        local scale
        if self.cur_fxs_rander_type == OARenderType.Image then
            position = display_data.mainui_display_pos
            rotation = display_data.mainui_rotation
            scale = Vector3(display_data.mainui_display_scale, display_data.mainui_display_scale, display_data.mainui_display_scale)
        end

        mainui_display_data.position = position or Vector3(0, 0, 0)
        mainui_display_data.rotation = rotation or Vector3(0, 0, 0)
        mainui_display_data.scale = scale or Vector3(1, 1, 1)

        mainui_display_data.model_click_func = function()
            TipWGCtrl.Instance:OpenItem({item_id = display_data.show_item_id})
        end

        self.model_display:SetData(mainui_display_data)
    end
end

function MainViewFXSTip:OnClickJump()
    if IsEmptyTable(self.cur_task_cfg) then
        return
    end

    if self.cur_task_cfg.go_param then
        local tb = Split(self.cur_task_cfg.go_param, "#")
        if tb[3] then
            local layer, index = 1, 1
            if tonumber(tb[3]) and tonumber(tb[4]) then
                layer = tonumber(tb[3])
                index = tonumber(tb[4])
            end

            BossWGData.Instance:SetBossTuJianIndex(tonumber(TabIndex[tb[2]]), layer, index)
            ViewManager.Instance:Open(tb[1], tb[2])
        else
            FunOpen.Instance:OpenViewNameByCfg(self.cur_task_cfg.go_param)
        end
    end
end

function MainViewFXSTip:OnClick()
    FunOpen.Instance:OpenViewByName(GuideModuleName.XiuXianShiLian)
end

function MainViewFXSTip:OnClickCloseTip()
    MainuiWGCtrl.Instance:SetSpecialACTPanelToggle(false)
end

-----------------------------------------灵剑降世/修仙圆梦_END---------------------------------------------


----------------
---- render ----
----------------

--------------------------------------------------------------
-- 任务item
--------------------------------------------------------------
TaskFollowTask = TaskFollowTask or BaseClass(BaseRender)
TaskFollowTask.Height = 60

function TaskFollowTask:__init()
    self.task_link_type = "common"
    self.task_type_icon = self.node_list["TaskTypeIcon"]
    self.lingqu_icon = self.node_list["BtnLingQu"]

    self.node_list["BtnSelf"].button:AddClickListener(BindTool.Bind(self.OnClick, self))

    self.btn_levelguide = self.node_list["BtnLevelYingDao"]
    self.btn_levelguide.button:AddClickListener(BindTool.Bind(self.OnClickLevelGuide, self))

    self.btn_capabilityguide = self.node_list["BtnCapaGuide"]
    self.btn_capabilityguide.button:AddClickListener(BindTool.Bind(self.OnClickCapaGuide, self))

    self.btn_shangjin = self.node_list["Button_shangjin"]
    self.btn_shangjin.button:AddClickListener(BindTool.Bind(self.OnClickShangJin, self))
    self.node_list["btn_fly"].button:AddClickListener(BindTool.Bind(self.OnClickFly, self))
    self.node_list["task_guide_log"]:SetActive(false)
    -- self.node_list["task_guide_log"].button:AddClickListener(BindTool.Bind(self.OnClick, self))
    self.node_list["BtnLingQu"].button:AddClickListener(BindTool.Bind(self.OnClick, self))
    -- self.reward_cell = ItemCell.New(self.node_list["item"])
    -- self.reward_cell:NeedDefaultEff(false)
    self.is_husong_countdowning = false
    self.is_ling_countdowning = false
end

function TaskFollowTask:__delete()
    self:ClearLingCD()
    self:CleanAssignTimer()
    if nil ~= self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    -- if self.reward_cell then
    --     self.reward_cell:DeleteMe()
    --     self.reward_cell = nil
    -- end

    self.task_type_icon = nil
    self.lingqu_icon = nil
    self.btn_capabilityguide = nil
    self.btn_levelguide = nil
    self.btn_shangjin =nil
end

function TaskFollowTask:ShowCommitEffect()
    local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_renwuwancheng)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.over_renwu_eff.transform,
    										0.5, Vector3(-200, 0, 0))
end

function TaskFollowTask:OnClickCapaGuide()
    TaskWGCtrl.Instance:OpenTaskGainCapaTipView()
end

function TaskFollowTask:OnClickLevelGuide()
    TaskWGCtrl.Instance:OpenTaskGainExpTipView()
end

function TaskFollowTask:OnClickShangJin()
    if self.data == nil then
        return
    end

    if self.data.task_type == GameEnum.TASK_TYPE_RI then
        ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
    elseif self.data.task_type == GameEnum.TASK_TYPE_MENG then
        local task_cfg = TaskWGData.Instance:GetTaskConfig(self.data.task_id)
        ViewManager.Instance:Open(GuideModuleName.TaskDailyBatchTips, 0, nil, task_cfg)
    end
end

function TaskFollowTask:OnFlush()
    if self.data == nil then
        return
    end

    if NEW_TASK_T[self.data.task_id] and NEW_TASK_T[self.data.task_id] + 0.5 > Status.NowTime then
        self:ShowCommitEffect()
    end

    NEW_TASK_T[self.data.task_id] = nil
    self.btn_capabilityguide:SetActive(false)
    self.btn_levelguide:SetActive(false)
    self.btn_shangjin:SetActive(false)
    self.lingqu_icon:SetActive(false)

    local state = TaskWGData.Instance:GetTaskStatus(self.data.task_id)
    if not self.old_task_id or self.old_task_id ~= self.data.task_id then --判断是否是同一个任务
        self.old_task_id = self.data.task_id
        self.old_state = TaskWGData.Instance:GetTaskStatus(self.data.task_id)
        self.is_show_task_dialog = true
    elseif self.old_state and state ~= self.old_state then --判断同一个任务状态是否不同
        self.is_show_task_dialog = true
    end

    local can_show_effect = self:CanShowEffect()
    self:SetGuideDialogVis(can_show_effect)
    self:SetZhuEffVis(can_show_effect)
    self.node_list["btn_fly"]:SetActive(false)

    --[[
    local reward_item = TaskWGData.Instance:GetTaskCellReward(self.data.task_id)
    if reward_item then
        self.node_list["item_contianer"]:SetActive(true)
        self.reward_cell:SetData(reward_item)
    else
        self.node_list["item_contianer"]:SetActive(false)
    end

    --任务类型图标显示 -- 赏金任务 转职任务 转生任务 仙盟建设任务 仙盟周任务 护送任务

    if self.data.task_type == GameEnum.TASK_TYPE_SHANG or self.data.task_type == GameEnum.TASK_TYPE_RI
   	or self.data.task_type == GameEnum.TASK_TYPE_ZHUAN or self.data.task_type == GameEnum.TASK_TYPE_MENG
   	or self.data.task_type == GameEnum.TASK_TYPE_GUILD_BUILD or self.data.task_type == GameEnum.TASK_TYPE_HU
   	or self.data.task_type == GameEnum.TASK_TYPE_HUSONG_SPC then
        self.node_list["task_type_contianer"]:SetActive(true)
        local str = Language.Task.task_type_icon[self.data.task_type]
        local b, a = ResPath.GetF2MainUIImage(str)
        self.task_type_icon.image:LoadSprite(b, a, function()
             XUI.ImageSetNativeSize(self.task_type_icon)
        end)
    else
        self.node_list["task_type_contianer"]:SetActive(false)
    end
    ]]

    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_show_level_guide = false
	if role_level < self.data.min_level and self.data.task_type == GameEnum.TASK_TYPE_ZHU
	and not EquipTargetWGCtrl.Instance:CheckIsShowBossNoticeTask() then
		is_show_level_guide = true
	end

    self.btn_levelguide:SetActive(is_show_level_guide)

    local one_key_finish_is_open = role_level >= TaskWGData.Instance:GetOtherInfo().daily_task_start
    local task_meet = self.data.task_type == GameEnum.TASK_TYPE_RI or self.data.task_type == GameEnum.TASK_TYPE_MENG
    local extra_task_condition = self.data.task_type == GameEnum.TASK_TYPE_MENG or DailyWGData.Instance:GetIsAcceptTask() 
    self.btn_shangjin:SetActive(task_meet and not can_show_effect and one_key_finish_is_open and extra_task_condition)
    if self.data.task_id == GameEnum.ASSIGN_TASK_ID then
        self:FlushAssignmentTask()
        self.task_link_type = "assignment"
    elseif role_level < self.data.min_level then
        self:FlushByDefineZhuTask()
        self.task_link_type = "level_up"
    elseif self.data.task_type == GameEnum.TASK_TYPE_GUAJI then
        self:FlushByVirtualGuajiTask()
        self.task_link_type = "guaji"
    elseif self.data.task_type == GameEnum.TASK_TYPE_LING then
        self:FlushByVirtualLingTask()
        self.task_link_type = "ling"
    elseif self.data.task_type == GameEnum.TASK_TYPE_TREASURE_MAP then
        self:FlushByVirtualTreasureMapTask()
        self.task_link_type = "treasure_map"
    elseif self.data.task_type == GameEnum.TASK_TYPE_ZHUAN then
        self:FlushByZhuanshengTask()
        self.task_link_type = "zhuansheng"
    else
        self:FlushByTaskCfg()
        self.task_link_type = "common"
    end
end

function TaskFollowTask:NeedShowTaskBubbleTime()
    -- 主线任务
    if self:IsZhuTask() then
        -- 新手阶段
        if TaskGuide.Instance:IsNoviceStage() then
            local state = TaskWGData.Instance:GetTaskStatus(self.data.task_id)
            --任务未完成
            if state == GameEnum.TASK_STATUS_NONE or state == GameEnum.TASK_STATUS_CAN_ACCEPT then 
                -- 显示任务
                if MainuiWGCtrl.Instance.view:GetShrinkButtonIsOn() == true then
                    return true
                end
            end
        end
    end

    return false
end

function TaskFollowTask:ClearAll()
    self:ClearLingCD()
    self:CleanAssignTimer()
end

function TaskFollowTask:CleanAssignTimer()
    if self.timer_assign and CountDown.Instance:HasCountDown(self.timer_assign) then
        CountDown.Instance:RemoveCountDown(self.timer_assign)
        self.timer_assign = nil
    end
end

function TaskFollowTask:SetRitchTextDsc(str)
    local task_status = TaskWGData.Instance:GetTaskStatus(self.data.task_id)
    if self.data.task_type == GameEnum.TASK_TYPE_GUILD_BUILD
    and self.data.condition == GameEnum.TASK_COMPLETE_CONDITION_6 then
        task_status = GuildWGData.Instance:GetGuildBuildTaskStates(self.data.task_id)
    end

    if self.data.task_type == GameEnum.TASK_TYPE_ZHU
    and EquipTargetWGCtrl.Instance:CheckIsShowBossNoticeTask()
    and RoleWGData.Instance.role_vo.level < self.data.min_level then
        str = BossNoticeTargetTipsWGData.Instance:GetCurGradeTaskDesc()
    end

    if (task_status == GameEnum.TASK_STATUS_COMMIT)
    and self.data.task_type ~= GameEnum.TASK_TYPE_HU then
        self.node_list["TextDesc"].tmp.text = string.format(Language.Task.task_color_green, str)
    elseif str then
        self.node_list["TextDesc"].tmp.text = str
    else
        self.node_list["TextDesc"].tmp.text = ""
    end
end

local task_type_text_color = {
    [GameEnum.TASK_TYPE_ZHU] = "#ffffa3",               --主线
	[GameEnum.TASK_TYPE_ZHI] = "#79b9fa",               --支线
	[GameEnum.TASK_TYPE_RI] = "#79fa82",                --日常
	[GameEnum.TASK_TYPE_HU] = "#cb79fa",                 --护送
	[GameEnum.TASK_TYPE_MENG] = "#f97878",               --仙盟
	[GameEnum.TASK_TYPE_CAMP] = "#2de4f0",               --阵营任务
	[GameEnum.TASK_TYPE_HUAN] = "#fa79bf",               --跑环任务
	[GameEnum.TASK_TYPE_SHANG] = "#7982fa",              --悬赏任务
    [GameEnum.TASK_TYPE_ZHUAN] = "#7ae1f0",              --转职任务
    [GameEnum.TASK_TYPE_GUILD_BUILD] = "#f97878",        --仙盟建设
    [GameEnum.TASK_TYPE_FAKE_XIUZHENROAD] = "#ff9e63",   --修真路假任务
    [GameEnum.TASK_TYPE_TREASURE_MAP] = "#e8bc82",       --藏宝图
    [GameEnum.TASK_TYPE_HUSONG_SPC] = "#cb79fa",         --护送假任务
}
function TaskFollowTask:SetTaskNameTextStr(str)
    local task_status = TaskWGData.Instance:GetTaskStatus(self.data.task_id)
    --类型为仙盟建设 and 啥也不干
    if self.data.task_type == GameEnum.TASK_TYPE_GUILD_BUILD
        and self.data.condition == GameEnum.TASK_COMPLETE_CONDITION_6 then
        task_status = GuildWGData.Instance:GetGuildBuildTaskStates(self.data.task_id)
    end

    if self.data.task_type == GameEnum.TASK_TYPE_ZHU
    and EquipTargetWGCtrl.Instance:CheckIsShowBossNoticeTask()
    and RoleWGData.Instance.role_vo.level < self.data.min_level then
        str = BossNoticeTargetTipsWGData.Instance:GetCurGradeTaskStr()
    end

    local task_type = self.data.task_type
    self.node_list.zhu_task_eff:SetActive(task_type == GameEnum.TASK_TYPE_ZHU)
    if str then
        -- local is_have = task_type_text_color[task_type] ~= nil
        local color = task_type_text_color[task_type] or COLOR3B.WHITE
        local task_title_text = Language.Task.task_type[task_type]

        str = ToColorStr(str, "#ffffa3")

        if task_title_text then
            str = string.format(Language.Common.ColorStr, color, task_title_text.." "..str)
        end

        EmojiTextUtil.ParseRichText(self.node_list["EmojiText"].emoji_text, str, 18)

        -- self.node_list.task_type_icon:SetActive(is_have)
        -- if is_have then
        --     local bundle, asset = ResPath.GetMainUIIcon("task_type_icon" .. task_type)
        --     self.node_list.task_type_icon.image:LoadSprite(bundle, asset)
        -- end
    end
end

-- 刷新自己定义的主线任务
function TaskFollowTask:FlushByDefineZhuTask()
    self:ClearAll()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local str = string.format(string.format("%s(%s/%s)", Language.Task.ContinueLevelup, role_level, RoleWGData.GetLevelString(self.data.min_level)))
    self:SetTaskNameTextStr(str)
    self:SetRitchTextDsc(string.format(Language.Task.TaskSugest, RoleWGData.GetLevelString(self.data.min_level)))
    self.lingqu_icon:SetActive(false)
    self.node_list.BtnAuto:SetActive(false)
end

-- 刷新挂机item
function TaskFollowTask:FlushByVirtualGuajiTask()
    self:ClearAll()
    if self.lingqu_icon then
        self.lingqu_icon:SetActive(false)
    end

    self:SetTaskNameTextStr(Language.Task.GuaJiTaskName)
    local desc = ""
    if nil ~= Config_scenelist[self.data.scene_id] then
        desc = string.format(Language.Task.GuaJiTaskDesc, Config_scenelist[self.data.scene_id].name)
    end

    self:SetRitchTextDsc(desc)
end

-- 刷新运灵石item
function TaskFollowTask:FlushByVirtualLingTask()
    self:ClearAll()
    self:SetTaskNameTextStr(Language.Task.LingTaskName)
    local desc = ""
    local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.data.commit_npc)
    if nil ~= npc_cfg then
        desc = string.format(Language.Task.LingTaskDesc, npc_cfg.name)
    end

    self:SetRitchTextDsc(ToColorStr(desc))
end

-- 刷新藏宝图
function TaskFollowTask:FlushByVirtualTreasureMapTask()
    self:ClearAll()

    local desc = string.format("(%s)", self.data.item_num)
    self:SetTaskNameTextStr(desc)
    self:SetRitchTextDsc(ToColorStr(Language.Task.TreasureMapTaskDesc))
end

-- 刷新转职任务
function TaskFollowTask:FlushByZhuanshengTask()
    self:ClearAll()

    
    if self.data.task_data then
        self:SetTaskNameTextStr(self.data.task_cfg.task_title_main)
        local color = self.data.task_data.progress < self.data.task_cfg.param1 and IS_CAN_COLOR.NOT_ENOUGH or IS_CAN_COLOR.ENOUGH
        self:SetRitchTextDsc(string.format("%s<color=%s>(%s/%s)</color>", self.data.task_cfg.task_des, color, self.data.task_data.progress,self.data.task_cfg.param1))
    else
        self:SetTaskNameTextStr(self.data.task_cfg.task_name)
        self:SetRitchTextDsc(self.data.task_cfg.progress_desc)
    end
    
    self.lingqu_icon:SetActive(false)
    self.node_list.BtnAuto:SetActive(false)
end

-- 刷新委托任务内容
function TaskFollowTask:FlushAssignmentTask()
    self:ClearAll()

    local data = AssignmentWGData.Instance:GetOneAssignTask()
    local task_id = (data or {}).task_id or 0
    local total_time = AssignmentWGData.Instance:GetTaskRestTime(task_id)
    local content_desc = Language.Assignment.ComplitTaskContent
    local time_str = TimeUtil.FormatSecond(total_time)
    local title_str = string.format(Language.Assignment.AssignTaskTitle, time_str)
    if total_time > 0 then
        local assign_num =  AssignmentWGData.Instance:GetAssignTaskNum()
        content_desc = string.format(Language.Assignment.AssignTaskContent, assign_num)
        self.timer_assign = CountDown.Instance:AddCountDown(total_time, 1,
                    -- 回调方法
                    function()
                        total_time = AssignmentWGData.Instance:GetTaskRestTime(task_id)
                        time_str = TimeUtil.FormatSecond(total_time)
                        title_str = string.format(Language.Assignment.AssignTaskTitle, time_str)
                        self:SetTaskNameTextStr(title_str)
                    end,
                    -- 倒计时完成回调方法
                    function()
                        AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.INFO)
                    end
                )
    else
        title_str = Language.Assignment.ComplitTaskTitle
    end
    self:SetTaskNameTextStr(title_str)
    self:SetRitchTextDsc(content_desc)
end

-- 根据任务配置刷新刷新
function TaskFollowTask:FlushByTaskCfg()
    self:ClearAll()
    self.lingqu_icon:SetActive(false)
    self.node_list.BtnAuto:SetActive(false)

    local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_vo = RoleWGData.Instance.role_vo
    local task_info = TaskWGData.Instance:GetTaskInfo(self.data.task_id)
    self.task_status = TaskWGData.Instance:GetTaskStatus(self.data.task_id)
    --仙盟建设任务状态特殊处理
    if self.data.task_type == GameEnum.TASK_TYPE_GUILD_BUILD
    and self.data.condition == GameEnum.TASK_COMPLETE_CONDITION_6 then
        local task_status = GuildWGData.Instance:GetGuildBuildTaskStates(self.data.task_id)
        self.task_status = task_status
    end

    local desc = ""
    if self.task_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
        desc = self.data.accept_desc
    elseif self.task_status == GameEnum.TASK_STATUS_COMMIT then
        desc = self.data.commit_desc
        if self.data.task_type == GameEnum.TASK_TYPE_MENG then
            local task_data = TaskWGData.Instance:GetGuildInfo()
            if task_data and next(task_data) ~= nil then
                if task_data.complete_task_count % 10 == 0 and task_data.complete_task_count > 0 then
                    TaskWGCtrl.Instance:OpenTaskHandView("xian_meng", self.data.task_id)
                end
            end
        elseif self.data.task_type == GameEnum.TASK_TYPE_SHANG then
            TaskWGCtrl.Instance:OpenTaskHandView("xuan_shang", self.data.task_id)
        elseif self.data.task_type == GameEnum.TASK_TYPE_ZHI then
            self:SetZhuEffVis(true)
            self.lingqu_icon:SetActive(false)
        elseif self.data.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
            desc = Language.Guild.GuildBuildRewardLq
        elseif self.data.task_type == GameEnum.TASK_TYPE_HU then
            desc = YunbiaoWGData.Instance:GetCurHusongDesc()
        end

        --仙盟建设任务不显示可领取
        if self.data.task_type ~= GameEnum.TASK_TYPE_GUILD_BUILD and self.data.task_type ~= GameEnum.TASK_TYPE_MENG 
        and self.data.task_type ~= GameEnum.TASK_TYPE_RI then
            --可提交都显示
            self:SetZhuEffVis(true)
            self.lingqu_icon:SetActive(true)
        end

    elseif self.task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
        desc = self.data.progress_desc

        if nil ~= task_info then
            local per = task_info.progress_num .. "/" .. self.data.c_param2
            if task_info.progress_num < self.data.c_param2 then
                per = ToColorStr(task_info.progress_num .. "/" .. self.data.c_param2, COLOR3B.C10)
            end
            desc = XmlUtil.RelaceTagContent(desc, "per", per)
        end
    elseif self.task_status == GameEnum.TASK_STATUS_NONE then
        local task_data = DailyWGData.Instance:GetTaskTuMoData()
        -- 虚拟日常，描述
        if self.data.task_type == GameEnum.TASK_TYPE_RI and task_data.commit_times > COMMON_CONSTS.TASK_DAILY_DAY_MAX_COUNT then
            local num = 0
            if task_data.has_fetch_complete_all_reward == ShangJinBox.Box1 or task_data.has_fetch_complete_all_reward == ShangJinBox.Box2 then
                num = 1
            elseif task_data.has_fetch_complete_all_reward == ShangJinBox.None then
                num = 0
            elseif task_data.has_fetch_complete_all_reward == ShangJinBox.AllDone then
                num = 2
            end
            desc = string.format(Language.Task.shangjin_task6, num)
        else
            if open_server_day < self.data.open_day then
                local off_day = self.data.open_day - open_server_day
                if off_day == 1 then
                    desc = Language.Task.task_day_limit2
                else
                    desc = string.format(Language.Task.task_day_limit, self.data.open_day)
                end
            elseif role_vo.level < self.data.min_level then
                desc = string.format(Language.Task.task_recommend, RoleWGData.Instance:TransToDianFengLevelStr(self.data.min_level))
            else
                desc = self.data.accept_desc
            end
        end

        if self.data.task_id == GUILD_BUILD_TASK_OTHER_TYPE.CLIENT_SHOW_TASK_ID then
            local task_cfg = TaskWGData.Instance:GetTaskConfig(self.data.task_id)
            desc = task_cfg.accept_desc
        end
    end

    self:SetTaskNameTextStr(self.data.task_name)

    local level_open = ConfigManager.Instance:GetAutoConfig("tasklist_auto").other[1].daily_guildtask_level
    if self.data.task_type == GameEnum.TASK_TYPE_FAKE_XIUZHENROAD then
        local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
        local cur_capability = GameVoManager.Instance:GetMainRoleVo().capability
        local Role_lv = RoleWGData.Instance:GetRoleLevel()
        local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(cur_level + 1)
        local str = cfg.capability
        if cfg and cfg.capability > 10000000 then
            str = math.ceil(cfg.capability / 10000) .. Language.Task.Wan
        end

        if cfg and cfg.open_level and cfg.open_level > Role_lv then
            local lv_str = RoleWGData.GetLevelString(cfg.open_level)
            local str_1 = string.format(Language.Task.LevelOpenDes, lv_str)
            desc = ToColorStr(str_1, MAIN_VIEW_COLOR.RED)
        elseif IsEmptyTable(cfg) or cur_capability >= cfg.capability then
            desc = string.format(Language.Task.Zhanli, ToColorStr(str, COLOR3B.DEFAULT_NUM))
		else
			desc = string.format(Language.Task.Zhanli, ToColorStr(str, MAIN_VIEW_COLOR.RED))
        end

        self:SetZhuEffVis(cur_capability >= cfg.capability and Role_lv >= cfg.open_level)
        self:SetTaskNameTextStr(string.format(Language.Task.CurLv, cur_level + 1))
    elseif self.data.task_type == GameEnum.TASK_TYPE_HUSONG_SPC then
        if role_vo.level < self.data.min_level then
            desc = string.format(Language.Task.task_recommend, RoleWGData.Instance:TransToDianFengLevelStr(self.data.min_level))
        else
            local need_time = self.data.c_param2 or 3--需要护送美女完成次数
            local complete_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) or 0 --完成次数
            desc = string.format(Language.Task.husong_time_format, complete_times, need_time)
        end

        self:SetZhuEffVis(true)
    elseif self.data.task_type == GameEnum.TASK_TYPE_RI then
        local task_data = DailyWGData.Instance:GetTaskTuMoData()
        if task_data and next(task_data) ~= nil then
            local task_id = TaskWGData.Instance:GetCurrBountyTask()
            if task_id ~= nil then
                local bounty_task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
                local bounty_data = TaskWGData.Instance:GetBountyListInfo()
                local num_num = bounty_data and bounty_data.cur_execulte_task_seq + 1 or 0
                local max_num = bounty_data and bounty_data.cur_all_task_num or 0
                local temp_count = "[".. num_num .. "/" .. max_num.. "]"
                self:SetTaskNameTextStr(bounty_task_cfg.task_name .. temp_count)
                desc = bounty_task_cfg.accept_desc
            else
                local base_cfg = TaskWGData.Instance:GetBountyBaseCfg()
                local info = TaskWGData.Instance:GetBountyListInfo()
                local bounty_accept_limit = base_cfg and base_cfg.bounty_accept_limit or 0
                local today_accept_bounty_num = info and info.today_accept_bounty_num or 0
                local last_accept_num = bounty_accept_limit - today_accept_bounty_num
                local str = ToColorStr(string.format("[%d/%d]", last_accept_num, bounty_accept_limit), COLOR3B.GREEN)
                self:SetTaskNameTextStr(string.format("%s%s", self.data.task_name, str))
            end
        end
    elseif self.data.task_type == GameEnum.TASK_TYPE_MENG then
        local task_data = TaskWGData.Instance:GetGuildInfo()
        if task_data and next(task_data) ~= nil then
            local commit_times = task_data.complete_task_count
            if self.task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS or self.task_status == GameEnum.TASK_STATUS_COMMIT then
                commit_times = commit_times + 1
            end
             local temp_count = "[".. commit_times .. "/" .. TaskWGData.Instance:GetTaskGuildWeekMaxCount() .. "]"
            self:SetTaskNameTextStr(self.data.task_name .. temp_count)
        end
    elseif self.data.task_type == GameEnum.TASK_TYPE_SHANG then
        local task_data = TaskWGData.Instance:GetBountyInfo()
        if task_data and next(task_data) ~= nil then
            local temp_count = "["..task_data.commit_times.. "/"..COMMON_CONSTS.TASK_GUILD_SHANG_MAX_COUNT.. "]"
            self:SetTaskNameTextStr(self.data.task_name .. temp_count)
        end
    elseif self.data.task_type == GameEnum.TASK_TYPE_HU then
        local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.HUSONG)
        local name = self.data.task_name
        if act_isopen then
            name = name .. Language.YunBiao.DoubleHint
        end
        self:SetTaskNameTextStr(name)
    end

    if self.data.condition == GameEnum.TASK_COMPLETE_CONDITION_3 and GameEnum.TASK_STATUS_ACCEPT_PROCESS == self.task_status then
        MainuiWGData.Instance:SaveTaskDesc(desc, self.data.task_id, self.task_status)
        GlobalEventSystem:Fire(MainUIEventType.SETMAINTASK)
    end

    self:SetRitchTextDsc(desc)
    --任务状态
    if self.data.task_type == GameEnum.TASK_TYPE_HU then
         --任务类型护送和仙盟建设一样恶心，提交状态时其实是进行任务的状态
         if self.task_status == GameEnum.TASK_STATUS_COMMIT then
             if self.data.task_type ~= GameEnum.TASK_TYPE_ZHI then
                 self.node_list.BtnAuto:SetActive(true)
             end

             self.lingqu_icon:SetActive(false)
             return
         end
    end

    local cur_task_type = TaskWGData.Instance:GetCurAuToTaskType()
    local is_auto_state = self.task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS or self.task_status == GameEnum.TASK_STATUS_CAN_ACCEPT
    self.node_list.BtnAuto:SetActive(self.data.task_type == cur_task_type and is_auto_state and self.data.task_type ~= GameEnum.TASK_TYPE_ZHI and self.data.task_type ~= GameEnum.TASK_TYPE_RI and self.data.task_type ~= GameEnum.TASK_TYPE_MENG)
end

function TaskFollowTask:ClearLingCD()
    if self.is_ling_countdowning then
        self.is_ling_countdowning = false
        CountDownManager.Instance:RemoveCountDown("ling_task_countdown")
    end
end

function TaskFollowTask:OnClickFly(event)
    if Scene.Instance:IsChangeSceneIng() then
    	return
    end

    if self.click_callback then
        self.click_callback(self.data.task_id, self.task_status, true)
    end
end

function TaskFollowTask:CanShowEffect()
    local flag = not TaskWGData.Instance:GetShowEffectTaskListById(self.data.task_id)
    local task_info = TaskWGData.Instance:GetTaskConfig(self.data.task_id)
    local role_lv = GameVoManager.Instance:GetMainRoleVo().level
    if self.data.task_type == GameEnum.TASK_TYPE_RI then  --赏金任务显示气泡,
        local is_show = false
        if TaskWGData.Instance:GetZhuTaskIsBlockByLevel() and TaskWGData.Instance:GetCurrTaskId() ~= self.data.task_id then  --当主线任务卡等级或者赏金任务没在进行
            is_show = true
        end
        return self.is_show_task_dialog and flag and is_show and role_lv>= self.data.min_level and
        task_info.Bubble_Tips ~= nil and task_info.Bubble_Tips ~= ""
    elseif self.data.task_type == GameEnum.TASK_TYPE_ZHU and EquipTargetWGCtrl.Instance:CheckIsShowBossNoticeTask() and RoleWGData.Instance.role_vo.level < self.data.min_level then
        return BossNoticeTargetTipsWGData.Instance:GetCurHaveNumIsEnough()
    -- 主线任务可提交
    elseif self.data.task_type == GameEnum.TASK_TYPE_ZHU and TaskWGData.Instance:GetTaskStatus(self.data.task_id) == GameEnum.TASK_STATUS_COMMIT and task_info.complete_bubble and task_info.complete_bubble ~= "" then
        return true
    else
        local is_first_flag = 1 == task_info.is_first
        local other_first_flag = TaskWGData.Instance:IsInOtherFirst(self.data.task_id)
        return self.is_show_task_dialog and flag
        		and role_lv >= self.data.min_level
        		and 1 == self.index
        		and (is_first_flag or other_first_flag)
    end
end

function TaskFollowTask:IsZhuTask()
    return self.data ~= nil and self.data.task_type == GameEnum.TASK_TYPE_ZHU
end

function TaskFollowTask:SetZhuEffVis(value)
    self.node_list.eff:SetActive(value)
end

function TaskFollowTask:SetGuideDialogVis(value)
    self.node_list.task_guide_log:SetActive(value)
    if value then
        local task_info = TaskWGData.Instance:GetTaskConfig(self.data.task_id)
        if not task_info or ((not task_info.Bubble_Tips or task_info.Bubble_Tips == "") and (not task_info.complete_bubble or task_info.complete_bubble == "")) then
            self.node_list.task_guide_log:SetActive(false)
            return
        end

        local str = task_info.Bubble_Tips
        if TaskWGData.Instance:GetTaskStatus(self.data.task_id) == GameEnum.TASK_STATUS_COMMIT
        and task_info.complete_bubble and task_info.complete_bubble ~= "" then
            str = task_info.complete_bubble
            MainuiWGCtrl.Instance:PlayTopButtonTween(true)
        end

        self.node_list.task_guide_log_txt.text.text = str
        UITween.MoveLoop(self.node_list["task_guide_log"], Vector2(-428, 28), Vector2(-438, 28), 0.5)
        TaskWGCtrl.Instance:AddShowGuide(self.data.task_id)
    end

    local index = self:GetIndex()
    MainuiWGCtrl.Instance:FlushView(0, "flush_ser_rec_tip", {["GuideDialogVis" .. index] = value})
end

function TaskFollowTask:GetActive()
    if self.view.gameObject and not IsNil(self.view.gameObject) then
        return self.view.gameObject.activeInHierarchy
    end

    return false
end

function TaskFollowTask:SetClickCallBack(event)
    self.click_callback = event
end

function TaskFollowTask:OnClick()
    if self.node_list["task_guide_log"] and self.node_list["task_guide_log"].gameObject.activeInHierarchy then
        self.is_show_task_dialog = false
        self:SetGuideDialogVis(false)
    end

    if Scene.Instance:IsChangeSceneIng() then
    	return
    end

    local role_level = RoleWGData.Instance:GetRoleLevel()
    if self.data.task_type == GameEnum.TASK_TYPE_ZHU and role_level < self.data.min_level then
        if EquipTargetWGCtrl.Instance:CheckIsShowBossNoticeTask() then
            EquipTargetWGCtrl.Instance:ClickBossNoticeTask()
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.ZhuXianLevelTips)
            TaskWGCtrl.Instance:OpenTaskGainExpTipView()
        end

        return
    end


    local task_data = DailyWGData.Instance:GetTaskTuMoData()
    -- 伪日常，打开赏金面板
    if self.data.task_type == GameEnum.TASK_TYPE_FAKE_XIUZHENROAD then
        local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
        local xiuzhen_cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(cur_level + 1)
        if xiuzhen_cfg and xiuzhen_cfg.open_level > role_level then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.LevelNotEnough)
        else
            ViewManager.Instance:OpenByCfg(self.data.open_panel_name)
        end
        return
    end
    -- 伪日常，护送特殊任务
    if self.data.task_type == GameEnum.TASK_TYPE_HUSONG_SPC then
        -- print_error("===== 护送特殊任务 =====")
        if NewTeamWGData.Instance:GetIsMatching() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
            return
        end

        if YunbiaoWGData.Instance:GetIsHuShong() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
            return
        end

        TaskGuide.Instance:CanAutoAllTask(false)
        ActIvityHallWGCtrl.Instance:DoHuSong()
        return
    end

    -- 转职任务 直接走转职相关接口
    if self.data.task_type == GameEnum.TASK_TYPE_ZHUAN then
	    TransFerWGCtrl.Instance:GoToTask(self.data.task_cfg)
        return
    end

    if self.data.task_type == GameEnum.TASK_TYPE_RI then
        NEXT_CAN_AUTO_GUIDE_TIME = -1
        local fly_shoes = VipWGData.Instance:GetRoleVipLevel() >= 1

        local task_id = TaskWGData.Instance:GetCurrBountyTask()
        if task_id ~= nil then
            self.click_callback(task_id, self.task_status, fly_shoes, true)
        else
            self.click_callback(self.data.task_id, self.task_status, fly_shoes, true)
        end
        return
    end

    if self.click_callback then
        NEXT_CAN_AUTO_GUIDE_TIME = -1
        local fly_shoes = VipWGData.Instance:GetRoleVipLevel() >= 1
        self.click_callback(self.data.task_id, self.task_status, fly_shoes, true)
    end
end




----------------------------------------------------------------------------
--MainUiMenberCell 队伍滚动条格子
----------------------------------------------------------------------------

MainUiMenberCell = MainUiMenberCell or BaseClass(BaseRender)

function MainUiMenberCell:__init()
    self.team_view = nil
    self.role_name = self.node_list["TextName"]
    self.level_text = self.node_list["TextLevel"]
    self.TextMenberState = self.node_list["TextMenberState"]
    --self.offline_state = self.node_list["offline_state"]
    self.leader_img = self.node_list["leader_img"]
    self.fenshen_img = self.node_list["fenshen_img"]
    self.follow_state = self.node_list["follow_state"]
    self.hp_slider = self.node_list["hp_slider"]
    self.hp_percent = self.node_list["hp_percent"]
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
    self.node_list["BtnClick"].button:AddClickListener(BindTool.Bind(self.ClickItem, self))
    self.node_list["BtnClick2"].button:AddClickListener(BindTool.Bind(self.ClickItem, self))
    XUI.AddClickEventListener(self.node_list.btn_add_member, BindTool.Bind(self.ClickAddMember, self))
    XUI.AddClickEventListener(self.node_list.btn_quit_team, BindTool.Bind(self.ClickQuitTeam, self))
end

function MainUiMenberCell:__delete()
    self.role_name = nil
    self.level_text = nil
    self.TextMenberState = nil
    --self.offline_state = nil
    self.leader_img = nil
    self.fenshen_img = nil
    self.follow_state = nil
    self.hp_slider = nil
    self.hp_percent = nil

    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function MainUiMenberCell:OnFlush()
    if not self.data or not next(self.data) then
    	return
    end

    self.node_list["NormalState"]:CustomSetActive(not self.data.is_empty_data)
    self.node_list["EmptyState"]:CustomSetActive(self.data.is_empty_data)
    if self.data.is_empty_data then
        self.node_list.scene_bg:SetActive(false)
        return
    end

    self.leader_img:CustomSetActive(self.data.is_leader == 1)
    self.fenshen_img:CustomSetActive(self.data.is_fenshen == 1)
    self.follow_state:CustomSetActive(self.data.is_follow_flag == 1)
    --self.hp_slider.slider.value = self.data.hp_percent
    --self.hp_percent.text.text = self.data.hp_percent * 100 .. "%"

    ----头像
    local data = {}
    data.role_id = Scene.Instance:GetIsOpenCrossViewByScene() and self.data.uuid.temp_low or self.data.role_id
    data.prof = self.data.prof
    data.sex = self.data.sex
    local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME, self.data.shizhuang_photoframe)
    data.fashion_photoframe = image_cfg and image_cfg.resouce or 0
    self.head_cell:SetData(data)

    local is_me
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        is_me = self.data.uuid == RoleWGData.Instance:GetUUid()
    else
        is_me = self.data.role_id == RoleWGData.Instance:InCrossGetOriginUid()
    end

    self.role_name.text.text = self.data.name
    local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)

    if is_vis then
        self.level_text.text.text = role_level
    else
        self.level_text.text.text = "Lv." .. role_level
    end

    self.node_list["level_icon"]:SetActive(is_vis)
    self.node_list["interval"]:SetActive(is_vis)
    --self.node_list["btn_quit_team"]:SetActive(is_me)
    self.TextMenberState:SetActive(not is_me)
    --self.offline_state:SetActive(false)

    --当前在不同国
    if self.data.is_in_different_country and 0 ~= self.data.is_online and 2 ~= self.data.is_online then
        self.TextMenberState.text.text = ToColorStr(Language.NewTeam.Away, "#ff9292")
        local local_server_str = ToLLStr(self.data.goto_plat_type, self.data.goto_server_id) --当前所在国
        local cur_server_str = RoleWGData.Instance:GetOriginalUSIDStr() --本国
        if not is_me and local_server_str ~= cur_server_str then
            self.node_list.scene_bg:SetActive(true)
            self.node_list.scene_des.text.text = Language.NewTeam.Cross
        else
            self.node_list.scene_bg:SetActive(false)
        end
    --如果队友所在场景和自己不一样 and 队友ID不等于自己的ID and（队友在线或者队友在离线挂机）
    elseif self.data.scene_id ~= Scene.Instance:GetSceneId() and self.data.role_id ~= RoleWGData.Instance:InCrossGetOriginUid() and (1 == self.data.is_online or 1 == self.data.is_hang) and self.data.scene_id ~= 0 then
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.data.scene_id)
        if scene_cfg.scene_type ~= SceneType.Common then
            self.node_list.scene_bg:SetActive(true)
            local map_name = Config_scenelist[self.data.scene_id] and Config_scenelist[self.data.scene_id].name or ""
            self.node_list.scene_des.text.text = string.format(Language.NewTeam.InMap, map_name)
        else
            self.node_list.scene_bg:SetActive(false)
        end
        self.TextMenberState.text.text = ToColorStr(Language.NewTeam.Away, "#ff9292")
    elseif (0 == self.data.is_online and 0 == self.data.is_hang) or self.data.scene_id == 0 or 2 == self.data.is_online then
        self.node_list.scene_bg:SetActive(false)
        self.TextMenberState.text.text = ToColorStr(Language.NewTeam.OffLine, COLOR3B.GRAY)
        --self.offline_state:SetActive(not is_me)
    elseif 3 == self.data.is_online then
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.data.scene_id)
        if scene_cfg.scene_type ~= SceneType.Common then
            self.node_list.scene_bg:SetActive(true)
            local map_name = Config_scenelist[self.data.scene_id] and Config_scenelist[self.data.scene_id].name or ""
            self.node_list.scene_des.text.text = string.format(Language.NewTeam.InMap, map_name)
        else
            self.node_list.scene_bg:SetActive(false)
        end
        self.TextMenberState.text.text = ToColorStr(Language.NewTeam.Cross, "#66c7ff")
    else
        self.node_list.scene_bg:SetActive(false)
        self.TextMenberState.text.text = ToColorStr(Language.NewTeam.Near, COLOR3B.DEFAULT_NUM)
    end
end

function MainUiMenberCell:GetItems()
    local items = {}
    local callback_param = {}
    local index = 0
    if self.data.is_empty_data then
        table.insert(items, Language.NewTeam.MenuRecruit)
        index = index + 1
        callback_param.recruit_index = index

        table.insert(items, Language.NewTeam.MenuInvite)
        index = index + 1
        callback_param.invite_index = index
    elseif self.data.role_id == RoleWGData.Instance:InCrossGetOriginUid() then
        table.insert(items, Language.NewTeam.MenuLeaveTeam)
        index = index + 1
        callback_param.leave_team_index = index
    else
        table.insert(items, Language.NewTeam.MenuBrouse)
        index = index + 1
        callback_param.brouse_index = index

        if nil == SocietyWGData.Instance:FindFriend(self.data.role_id) and not UserVo.IsCrossServer(self.data.role_id) then
            table.insert(items, Language.NewTeam.MenuAddFriend)
            callback_param.has_add_friend = true
            index = index + 1
            callback_param.add_friend_index = index
        end

        if 1 == SocietyWGData.Instance:GetIsTeamLeader() then
            table.insert(items, Language.NewTeam.MenuTurnLeader)
            table.insert(items, Language.NewTeam.MenuCallReturnTeam)
            table.insert(items, Language.NewTeam.MenuQuitTeam)

            callback_param.is_leader = true
            index = index + 1
            callback_param.turn_leader_index = index
            index = index + 1
            callback_param.call_return_team_index = index
            index = index + 1
            callback_param.quit_team_index = index
        elseif 1 == self.data.is_leader then
            -- table.insert(items, Language.NewTeam.ApplyToBeTeamLeader)
            -- index = index + 1
            -- callback_param.apply_team_leader_index = index
        end
    end

    return items, callback_param
end

function MainUiMenberCell:GetCrossItems()
    local items = {}
    local callback_param = {}
    local index = 0
    if self.data.is_empty_data then
        table.insert(items, Language.NewTeam.MenuRecruit)
        index = index + 1
        callback_param.recruit_index = index

        table.insert(items, Language.NewTeam.MenuInvite)
        index = index + 1
        callback_param.invite_index = index
    elseif self.data.uuid.temp_low == RoleWGData.Instance:InCrossGetOriginUid() then
        table.insert(items, Language.NewTeam.MenuLeaveTeam)
        index = index + 1
        callback_param.leave_team_index = index
    else
        table.insert(items, Language.NewTeam.MenuBrouse)
        index = index + 1
        callback_param.brouse_index = index

        if nil == SocietyWGData.Instance:FindFriend(self.data.uuid.temp_low) and self:IsSameServer() then
            table.insert(items, Language.NewTeam.MenuAddFriend)
            callback_param.has_add_friend = true
            index = index + 1
            callback_param.add_friend_index = index
        end

        if 1 == CrossTeamWGData.Instance:GetIsTeamLeader() then
            table.insert(items, Language.NewTeam.MenuTurnLeader)
            table.insert(items, Language.NewTeam.MenuQuitTeam)

            callback_param.is_leader = true
            index = index + 1
            callback_param.turn_leader_index = index
            index = index + 1
            callback_param.quit_team_index = index
        elseif 1 == self.data.is_leader then
            -- table.insert(items, Language.NewTeam.ApplyToBeTeamLeader)
            -- index = index + 1
            -- callback_param.apply_team_leader_index = index
        end
    end

    return items, callback_param
end

function MainUiMenberCell:ClickItem()
    if Scene.Instance:GetSceneType() == SceneType.GUIDE_BOSS then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Guide.WorldBossGuideTip)
        return
    end

    if not Scene.Instance:GetIsOpenCrossViewByScene() then
        -- if self.data.role_id == RoleWGData.Instance:InCrossGetOriginUid() then
        --     return
        -- end

        local items, callback_param = self:GetItems()
        NewTeamWGCtrl.Instance:OpenPopMenu(items, self:GetPos(self.node_list["point"])
                                                , BindTool.Bind(self.OnClickMenuCallback, self)
                                                , callback_param)
    else
        -- if self.data.uuid == RoleWGData.Instance:GetUUid() then
        --     return
        -- end

        -- local item = {}
        -- item.role_id = self.data.uuid.temp_low
        -- item.name = self.data.uuid
        -- item.prof = self.data.prof
        -- item.sex = self.data.sex
        -- item.capability = self.data.capability
        -- item.vip_level = self.data.vip_level
        -- item.plat_type = self.data.usid.temp_high
        -- item.server_id = self.data.usid.temp_low
        -- item.is_online = 1 --跨服组队下线则会退队
        local items, callback_param = self:GetCrossItems()
        NewTeamWGCtrl.Instance:OpenPopMenu(items, self:GetPos(self.node_list["point"])
                                                , BindTool.Bind(self.OnClickMenuCallback, self)
                                                , callback_param)
    end
end



function MainUiMenberCell:ClickAddMember()
    if Scene.Instance:GetSceneType() == SceneType.GUIDE_BOSS then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Guide.WorldBossGuideTip)
        return
    end

    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:OpenInviteView()
    else
        NewTeamWGCtrl.Instance:OpenInviteView()
    end
end

function MainUiMenberCell:IsSameServer()
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
    local main_role_plat_type = RoleWGData.Instance:GetPlatType()
    local plat, server
    if self.data.usid then
        plat, server = self.data.usid.temp_high, self.data.usid.temp_low
    end

	local server_id = server or main_role_server_id
    local plat_type = plat or main_role_plat_type
    self.plat_type = plat_type
	return server_id == main_role_server_id and plat_type == main_role_plat_type
end

function MainUiMenberCell:OnClickMenuCallback(index, sender, callback_param)
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        if not self.data then
            return
        end

        if callback_param.brouse_index == index then                                               --查看信息
            BrowseWGCtrl.Instance:OpenWithUid(self.data.uuid.temp_low, nil, not self:IsSameServer())
        elseif callback_param.leave_team_index == index then                                       --离开队伍
            self:ClickQuitTeam()
        elseif callback_param.has_add_friend and callback_param.add_friend_index == index then     --加为好友
            SocietyWGCtrl.Instance:IAddFriend(self.data.uuid.temp_low)
        elseif callback_param.recruit_index == index then                                          --招募
            self.team_view:OnClickWorldTalk()
        elseif callback_param.invite_index == index then                                           --邀请入队
            self:ClickAddMember()
        elseif callback_param.is_leader and callback_param.turn_leader_index == index then         --转移队长
            local ok_fun = function ()
                CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_CHANGE_LEADER, self.data.index)
            end

            if not self.confirm_team_alert then
                self.confirm_team_alert = Alert.New()
            end

            self.confirm_team_alert:SetOkFunc(ok_fun)
            local str = string.format(Language.NewTeam.IsTurnLeader, self.data.name)
            self.confirm_team_alert:SetLableString(str)
            self.confirm_team_alert:Open()
        elseif callback_param.is_leader and callback_param.quit_team_index == index then           --请离队伍
            local ok_fun = function ()
                CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_KICK_OUT, self.data.index)
            end

            if not self.confirm_team_alert then
                self.confirm_team_alert = Alert.New()
            end

            self.confirm_team_alert:SetOkFunc(ok_fun)
            local str = string.format(Language.NewTeam.IsQuitTeam, self.data.name)
            self.confirm_team_alert:SetLableString(str)
            self.confirm_team_alert:Open()
        elseif callback_param.apply_team_leader_index == index then                                --请求队长
            CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_REQ_LEADER)
        end
    else
        if not self.data then
            return
        end

        if callback_param.brouse_index == index then                                               --查看信息
            BrowseWGCtrl.Instance:OpenWithUid(self.data.role_id)
        elseif callback_param.leave_team_index == index then                                       --离开队伍
            self:ClickQuitTeam()
        elseif callback_param.has_add_friend and callback_param.add_friend_index == index then     --加为好友
            SocietyWGCtrl.Instance:IAddFriend(self.data.role_id)
        elseif callback_param.recruit_index == index then                                          --招募
            self.team_view:OnClickWorldTalk()
        elseif callback_param.invite_index == index then                                           --邀请入队
            self:ClickAddMember()
        elseif callback_param.is_leader and callback_param.turn_leader_index == index then         --转移队长
            local ok_fun = function ()
                if self.data.is_empty_data then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.TargetNotInTeam)
                else
                    SocietyWGCtrl.Instance:SendChangeTeamLeader(self.data.role_id)
                end
                
            end

            if not self.confirm_team_alert then
                self.confirm_team_alert = Alert.New()
            end

            self.confirm_team_alert:SetOkFunc(ok_fun)
            local str = string.format(Language.NewTeam.IsTurnLeader, self.data.name)
            self.confirm_team_alert:SetLableString(str)
            self.confirm_team_alert:Open()
        elseif callback_param.is_leader and callback_param.call_return_team_index == index then    --召唤归队
            self.team_view:OnClickZhaoJiBtn(self.data.role_id, self.data.name)
        elseif callback_param.is_leader and callback_param.quit_team_index == index then           --请离队伍
            local ok_fun = function ()
                SocietyWGCtrl.Instance:SendKickOutOfTeam(self.data.role_id)
            end

            if not self.confirm_team_alert then
                self.confirm_team_alert = Alert.New()
            end

            self.confirm_team_alert:SetOkFunc(ok_fun)
            local str = string.format(Language.NewTeam.IsQuitTeam, self.data.name)
            self.confirm_team_alert:SetLableString(str)
            self.confirm_team_alert:Open()
        elseif callback_param.apply_team_leader_index == index then                                --请求队长
            SocietyWGCtrl.Instance:SendReqMemChangeTeamLeader()
        end
    end
end

function MainUiMenberCell:OnInviteMenuCallback(index, sender)
    if 1 == index then
        NewTeamWGCtrl.Instance:OpenInviteView()
	elseif 2 == index then
        NewTeamWGCtrl.Instance:OpenApplyView()
	end
end

function MainUiMenberCell:GetPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x, y)
end

function MainUiMenberCell:ClickQuitTeam()
    if Scene.Instance:GetSceneType() == SceneType.GUIDE_BOSS then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Guide.WorldBossGuideTip)
        return
    end

    local content = ""
    local main_role = Scene.Instance:GetMainRole()
    content = string.format(Language.NewTeam.QuitTeamStr, main_role.vo.name, main_role.vo.level)
    ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1, nil, true, false)
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_EXIT)
        CrossTeamWGData.Instance:ClearTeamData()
        GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE) --因为不下发
    else
        SocietyWGCtrl.Instance:SendExitTeam()
        NewTeamWGData.Instance:ClearTeamInfo()
    end
end

----------------------------------------------------
------------ 仙界Boss
----------------------------------------------------
XianJieBossRewardRender = XianJieBossRewardRender or BaseClass(BaseRender)
function XianJieBossRewardRender:__init()
end

function XianJieBossRewardRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item_pos"])
end

function XianJieBossRewardRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function XianJieBossRewardRender:OnFlush()
	if self.data then
		self.item_cell:SetData(self.data)
	end
end

---------------------------------今日活动render---------------------------------
TaskToDayActListItemRender = TaskToDayActListItemRender or BaseClass(BaseRender)

function TaskToDayActListItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.OnClickAct, self))
    self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function TaskToDayActListItemRender:__delete()
    if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

    self:CancelDelayFlushTimer()
end

function TaskToDayActListItemRender:OnFlush()
	local bundle, asset = ResPath.GetF2MainUIImage(self.data.cfg.zjm_zp_btn_name)
	self.node_list["act_icon"].image:LoadSprite(bundle, asset, function ()
        self.node_list["act_icon"].image:SetNativeSize()
    end)

	self.node_list["open_time"].text.text = self.data.str
    self.node_list.act_name.text.text = self.data.cfg.name

    self.node_list.openning_label:SetActive(self.data.is_openning)
	self.node_list.close_label:SetActive(not self.data.is_openning)
end

function TaskToDayActListItemRender:OnClickAct()
	CalendarWGCtrl.Instance:OnClickAct(self.data)
	-- ViewManager.Instance:Close(GuideModuleName.CalendarView)
end

function TaskToDayActListItemRender:ActivityChangeCallBack(activity_type, status, next_time, open_type)
    local act_type = ((self.data or {}).cfg or {}).act_type or -1
    if act_type > 0 and activity_type == act_type then
        self:CancelDelayFlushTimer()

        self.flush_delay_time = GlobalTimerQuest:AddDelayTimer(function ()
            self:Flush()
        end, 1)
    end
end

function TaskToDayActListItemRender:CancelDelayFlushTimer()
    if self.flush_delay_time ~= nil then
        GlobalTimerQuest:CancelQuest(self.flush_delay_time)
    end
    self.flush_delay_time = nil
end