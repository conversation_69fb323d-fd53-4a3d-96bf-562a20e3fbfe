﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_LightmapSettingsWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.LightmapSettings), typeof(UnityEngine.Object));
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>un<PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("lightmaps", get_lightmaps, set_lightmaps);
		<PERSON><PERSON>("lightmapsMode", get_lightmapsMode, set_lightmapsMode);
		<PERSON><PERSON>("lightProbes", get_lightProbes, set_lightProbes);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lightmaps(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.LightmapSettings.lightmaps);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lightmapsMode(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.LightmapSettings.lightmapsMode);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lightProbes(IntPtr L)
	{
		try
		{
			ToLua.PushSealed(L, UnityEngine.LightmapSettings.lightProbes);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lightmaps(IntPtr L)
	{
		try
		{
			UnityEngine.LightmapData[] arg0 = ToLua.CheckObjectArray<UnityEngine.LightmapData>(L, 2);
			UnityEngine.LightmapSettings.lightmaps = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lightmapsMode(IntPtr L)
	{
		try
		{
			UnityEngine.LightmapsMode arg0 = (UnityEngine.LightmapsMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.LightmapsMode));
			UnityEngine.LightmapSettings.lightmapsMode = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lightProbes(IntPtr L)
	{
		try
		{
			UnityEngine.LightProbes arg0 = (UnityEngine.LightProbes)ToLua.CheckObject(L, 2, typeof(UnityEngine.LightProbes));
			UnityEngine.LightmapSettings.lightProbes = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

