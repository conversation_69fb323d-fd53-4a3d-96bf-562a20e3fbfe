--废弃的
KuafuYeZhanWangChengView = KuafuYeZhanWangChengView or BaseClass(SafeBaseView)

function KuafuYeZhanWangChengView:__init()
	self.view_style = ViewStyle.Full
	self.is_modal = true
	self.default_index = 0

	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	self:AddViewResource(0, "uis/view/kuafu_yezhanwangcheng_ui_prefab", "layout_yezhanwangcheng2")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
	self.item_cell = {}	
end

function KuafuYeZhanWangChengView:__delete()
	for k,v in pairs(self.item_cell) do
		v:DeleteMe()
	end
	self.item_cell = {}
end

function KuafuYeZhanWangChengView:ReleaseCallBack()
	
end

function KuafuYeZhanWangChengView:LoadCallBack()
	self.node_list.title_view_name.text.text = "夜战云巅"
	self:CreateItemSlot()
	XUI.AddClickEventListener(self.node_list.btn_action, BindTool.Bind1(self.OnClickHandler, self))
	XUI.AddClickEventListener(self.node_list.btn_question, BindTool.Bind1(self.OnBtnTipsClickHandler, self))
end

function KuafuYeZhanWangChengView:InitCallBack()
	-- GlobalTimerQuest:AddDelayTimer(function()
	--self.node_list["layout_star"]:SetActive(true)
	local mainui_ctrl = MainuiWGCtrl.Instance

	local parent = mainui_ctrl:GetTaskOtherContent()
	--print(" PetTaskView:InitCallBack---s parent ",parent," mainui_ctrl : ",mainui_ctrl," self.node_list.root_fuben_info : ",self.node_list.root_fuben_info,"self : ",self," node_list : ",self.node_list)
	self.info = PetTaskList.New(self.node_list.root_fuben_info)
	self.info:SetInstanceParent(parent)
	self.info:AnchorPosition(0,-63)

	self.task_panel_change = true
	mainui_ctrl:SetTaskPanel(false,0,-200)
	-- mainui_ctrl:ChangeTaskBtnName(Language.Task.task_text3)
	mainui_ctrl:SetAutoGuaJi(true)

	self.main_ui_is_init = true
	self:Flush()
end	

function KuafuYeZhanWangChengView:OpenCallBack()
end

function KuafuYeZhanWangChengView:CloseCallBack()
end

function KuafuYeZhanWangChengView:OnFlush(param_list, index)
	
end

function KuafuYeZhanWangChengView:CreateItemSlot()
	local xiuluota_item =   KuafuYeZhanWangChengWGData:GetJieMianReward()
	local len = GetTableLen(xiuluota_item)
	for i = 0, len - 1 do
		if self.node_list["ph_item_cell_"..i] then
			self.item_cell[i] = ItemCell.New()
			self.item_cell[i]:SetInstanceParent(self.node_list["ph_item_cell_"..i])
		end
	end
	if xiuluota_item then
		for k,v in pairs(self.item_cell) do
			v:SetData(xiuluota_item[k])
		end
	end
end

function KuafuYeZhanWangChengView:ShowIndexCallBack(index)

end

function KuafuYeZhanWangChengView:OnClickHandler()
	KuafuYeZhanWangChengWGCtrl.Instance:SendNightFightEnterReq()
end

function KuafuYeZhanWangChengView:OnBtnTipsClickHandler()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.YeZhanWangCheng.TipsTitle)
		role_tip:SetContent(Language.YeZhanWangCheng.Tips)
	end	
end