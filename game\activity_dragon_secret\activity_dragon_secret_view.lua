ActivityDragonSecretView = ActivityDragonSecretView or BaseClass(SafeBaseView)

function ActivityDragonSecretView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()

    self:AddViewResource(0, "uis/view/activity_dragon_secret_ui_prefab", "layout_dragon_secret")

end

function ActivityDragonSecretView:ReleaseCallBack()
    self:CleanActTimer()
    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
        self.item_data_change = nil
    end

    self.num_show_index = nil
    if self.right_award_cell then
        self.right_award_cell:DeleteMe()
        self.right_award_cell = nil
    end    

    if self.reseting_num_list then
        self.reseting_num_list:DeleteMe()
        self.reseting_num_list = nil
    end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function ActivityDragonSecretView:OpenCallBack()
    --请求协议信息
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_DRAGONSECRET, 1)
end

function ActivityDragonSecretView:LoadCallBack()
    --抽奖按钮
    for i = 1, 2 do
        self.node_list["lottery_btn_"..i].button:AddClickListener(BindTool.Bind2(self.OnClickLotery, self, i))
        self.node_list["cost_icon_"..i].button:AddClickListener(BindTool.Bind(self.ShowCostIconClick, self))
    end
    --提示按钮
    self.node_list["btn_prompt"].button:AddClickListener(BindTool.Bind(self.PromptBtnClick, self))
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
    --重置奖池按钮
    self.node_list["reset_btn"].button:AddClickListener(BindTool.Bind(self.ResetBtnClick, self))
    
    --当前列表按钮
    self.node_list["current_list_btn"].button:AddClickListener(BindTool.Bind(self.CurrentBtnClick, self))
    --全部列表按钮
    self.node_list["all_list_btn"].button:AddClickListener(BindTool.Bind(self.AllBtnClick, self))
    
    --右侧大奖物品
    self.right_award_cell = ItemCell.New(self.node_list["awards_item"])

    --右侧重置次数列表
    self.reseting_num_list = AsyncListView.New(ResetNumDragonSecretRender, self.node_list["cumulative_receive_list"])

    --活动时间
    local total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.OPERA_ACT_DRAGONSECRET)
    self:UpdataTimerStr(total_time)
    if total_time > 0 then
        self.act_timer = CountDown.Instance:AddCountDown(total_time, 0.5,
        function(elapse_time, total_time)
            local time = math.floor(total_time - elapse_time)
            self:UpdataTimerStr(time)
        end,
        function()
            self:UpdataTimerStr(0)
        end
        )
    end

   --物品发生改变的监听
   self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
   ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

--清除计时器
function ActivityDragonSecretView:CleanActTimer()
    if self.act_timer and CountDown.Instance:HasCountDown(self.act_timer) then
        CountDown.Instance:RemoveCountDown(self.act_timer)
        self.act_timer = nil
    end
end

function ActivityDragonSecretView:UpdataTimerStr(time)
    if self.node_list["dragon_down_time"] then
        self.node_list["dragon_down_time"].text.text = TimeUtil.FormatSecondDHM9(time)
    end
end

function ActivityDragonSecretView:OnFlush()
    --给列表赋值,获取重置次数奖励配置表
    local num_data_list = ActivityDragonSecretWGData.Instance:GetResetTashList()
    self.reseting_num_list:SetDataList(num_data_list)

    local jump_index = 1
    for k, v in ipairs(num_data_list) do
        local is_remind = ActivityDragonSecretWGData.Instance:GetIsCanReceive(v.times,v.seq)
        if is_remind then
            jump_index = k
            break
        end
    end

    if self.num_show_index ~= jump_index then
        self.num_show_index = jump_index
        self.reseting_num_list:JumpToIndex(jump_index)  -- 列表跳转到没有领取的地方
    else
        self.reseting_num_list:JumpToIndex(1)
    end

    --刷新每轮大奖物品
    self:FlushRightAward()

    --刷新按钮信息
    self:FlushShowBtn()
end

--物品改变更新
function ActivityDragonSecretView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    -- 物品数量增加
    -- 拿到物品ID == change_item_id 就刷新
    local other_cfg = ActivityDragonSecretWGData.Instance:GetQiTaCfg()
    if change_item_id == other_cfg.cost_item_id then
        self:FlushShowBtn()
    end
end

function ActivityDragonSecretView:FlushShowBtn()
    local other_cfg = ActivityDragonSecretWGData.Instance:GetQiTaCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if item_cfg == nil then
        return
    end
    for i = 1, 2 do
        self.node_list["cost_icon_"..i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        
        --拿到奖池物品剩余的总数
        local prize_sum = ActivityDragonSecretWGData.Instance:GetPrizeSum()
        --拿到背包的道具的总数
        local item_count = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
        --按钮1的默认次数
        local draw_number_ten = LOTTER_NUM_TYPE.TEN_NUMBER_DRAWS
        --按钮2的默认次数
        local repeatedly_draw_num = LOTTER_NUM_TYPE.DEFAULT_NUMBER 
        --抽奖需要的数量
        local lottery_number  
        if i == 1 then
            if draw_number_ten >= prize_sum then
                lottery_number = other_cfg.cost_item_num * prize_sum
                self:FlushTextShow(i, item_count, lottery_number, prize_sum)
            else
                lottery_number = other_cfg.cost_item_num * draw_number_ten
                self:FlushTextShow(i, item_count, lottery_number, draw_number_ten)
            end
        elseif i == 2 then
            if repeatedly_draw_num >= prize_sum then
                lottery_number = other_cfg.cost_item_num * prize_sum
                self:FlushTextShow(i, item_count, lottery_number, prize_sum)
            else
                lottery_number = other_cfg.cost_item_num * repeatedly_draw_num
                self:FlushTextShow(i, item_count, lottery_number, repeatedly_draw_num)
            end
        end

        if prize_sum <= 0 then
            self.node_list["lottery_txt_"..i].text.text = string.format(Language.DragonSecret.DragonDrawTxt, 1)
        end
    end
end

--文本显示更新
function ActivityDragonSecretView:FlushTextShow(i, item_count, lottery_number, prize_sum)
    local num_text = item_count .. "/" .. prize_sum
    local color = COLOR3B.D_GREEN
    if item_count >= lottery_number and item_count > 0 then
        color = COLOR3B.D_GREEN
    else
        color = COLOR3B.D_RED
    end
   -- local color = item_count > lottery_number and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list["cost_num_" .. i].text.text = ToColorStr(num_text, color)
    self.node_list["lottery_txt_"..i].text.text = string.format(Language.DragonSecret.DragonDrawTxt, prize_sum)
end

function ActivityDragonSecretView:FlushRightAward()
    local grade = ActivityDragonSecretWGData.Instance:GetActDraGrade()
    local round = ActivityDragonSecretWGData.Instance:GetActDraRound()
    local is_show = ActivityDragonSecretWGData.Instance:GetAwardsIsCanFinish()
    local item_cell  = ActivityDragonSecretWGData.Instance:GetCurrentList(grade,round)
    if not item_cell[1] then
        return
    end
    self.right_award_cell:SetData(item_cell[1].item)

    self.node_list["awards_txt"].text.text = Language.DragonSecret.DragonDaJiang

    --重置按钮的显示
    --获得当前轮次 大于等于第6轮就不显示出来了
    local round = ActivityDragonSecretWGData.Instance:GetActDraRound()
    local total_rounds = ActivityDragonSecretWGData.Instance:GetRoundTagsList()
    if round < #total_rounds - 1 then
    self.node_list["reset_btn"]:CustomSetActive(is_show) 
    else
    self.node_list["reset_btn"]:CustomSetActive(false) 
    end

    self.node_list["get_flag_reard"]:CustomSetActive(is_show)
end


function ActivityDragonSecretView:OnClickLotery(mode_type)
    --点击了抽奖按钮
    --保存点击的是那个按钮
    ActivityDragonSecretWGData.Instance:SetOrGetDragonDrawIndex(mode_type)

    ActivityDragonSecretWGCtrl.Instance:ClickUse(mode_type)
end

function ActivityDragonSecretView:PromptBtnClick()
    local role_tip = RuleTip.Instance

    role_tip:SetContent(Language.DragonSecret.DragonRuleContent, Language.DragonSecret.DragonRule)
end

function ActivityDragonSecretView:ResetBtnClick()
    if not self.alert then
    self.alert = Alert.New()
    end

    self.alert:ClearCheckHook()
    self.alert:SetShowCheckBox(true, "dragon_reward")
    self.alert:SetCheckBoxDefaultSelect(false)

    local func = function ()
      ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_DRAGONSECRET, 3)
    end

    local str = string.format(Language.DragonSecret.DragonResetRule)

    self.alert:SetLableString(str)
    self.alert:SetOkFunc(func)
    self.alert:Open()
   
end

function ActivityDragonSecretView:CurrentBtnClick()
    ActivityDragonSecretWGCtrl.Instance:OpenCurRewardView()
end

function ActivityDragonSecretView:AllBtnClick()
    ActivityDragonSecretWGCtrl.Instance:OpenAllRewardView()
end

function ActivityDragonSecretView:ShowCostIconClick()
    local other_cfg = ActivityDragonSecretWGData.Instance:GetQiTaCfg()
    local item_id = other_cfg.cost_item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id })
end






--- 列表格子
ResetNumDragonSecretRender = ResetNumDragonSecretRender or BaseClass(BaseRender)

function ResetNumDragonSecretRender:__init()
    self.reward_list = {}
    
    for i = 1, 2 do
        self.reward_list[i] = ItemCell.New(self.node_list["rewards_items_list"])    
    end
    
    XUI.AddClickEventListener(self.node_list["get_btn"], BindTool.Bind(self.OnClickGetBtn, self))
end

function ResetNumDragonSecretRender:__delete()
    if self.reward_list then
        for k,v in pairs(self.reward_list) do
            v:DeleteMe()
        end

        self.reward_list = nil
    end
end

function ResetNumDragonSecretRender:OnFlush()
    if self.data == nil then
        return
    end

    --拿到当前刷新次数
    local chongzhi_num = ActivityDragonSecretWGData.Instance:GetActDraRefresh()
    for i = 1, #self.reward_list do
        if self.data.reward_item[i-1] then
            self.reward_list[i]:SetData(self.data.reward_item[i-1])
            self.reward_list[i]:SetActive(true)
        else
            self.reward_list[i]:SetActive(false)
        end
    end
    
    local color =  chongzhi_num >= self.data.times and COLOR3B.D_GREEN or COLOR3B.D_RED
    local str = ToColorStr(string.format("%s/%s", chongzhi_num, self.data.times), color)
    self.node_list["rewards_txt"].text.text = string.format(Language.DragonSecret.DragonChongZhiNum, str)
   
   
    local round_reward = ActivityDragonSecretWGData.Instance:GetRoundReward(self.data.seq)  

    local is_grey = chongzhi_num >= self.data.times
    if  is_grey then
        XUI.SetGraphicGrey(self.node_list["get_btn"], not is_grey)
        self.node_list["get_txt"].text.text = Language.DragonSecret.DragonLingQu
    else
        XUI.SetButtonEnabled(self.node_list["get_btn"], not is_grey )
        self.node_list["get_txt"].text.text = Language.DragonSecret.DragonNotLingQu
    end

    --红点
    self.node_list["red_mind"]:CustomSetActive(self.data.is_remind)

    
   -- local is_get = ActivityDragonSecretWGData.Instance:GetIsCanReceive(self.data.times, self.data.seq)
    --已领取按钮
    self.node_list["get_flag"]:CustomSetActive(round_reward)
    self.node_list["get_btn"]:CustomSetActive(not round_reward)

end

function ResetNumDragonSecretRender:OnClickGetBtn()
    if not self.data then
        return
    end

    local is_get = ActivityDragonSecretWGData.Instance:GetIsCanReceive(self.data.times, self.data.seq)    
    if  not is_get then
        return
    end

    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_DRAGONSECRET, 4, self.data.seq)
end