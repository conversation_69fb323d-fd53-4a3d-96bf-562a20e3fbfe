-----------------------------------------------------
-- BaseScrollGridRender
-----------------------------------------------------

BaseGridRender = BaseGridRender or BaseClass(BaseRender)
function BaseGridRender:__init(instance, bundle, asset)
	self.grid_call_back = nil						-- grid回调函数
	self.select_call_back = nil
	self.parent = nil
	self.columns = nil
	self.rows = nil
	self.select_tab = {}
end

function BaseGridRender:__delete()

end

function BaseGridRender:SetColumns(columns)
	self.columns = columns
end

function BaseGridRender:SetRows(rows)
	self.rows = rows
end

function BaseGridRender:GetRow(rows)
	return self.rows
end

-- 选择表
function BaseGridRender:SetSelectTab(select_tab)
	self.select_tab = select_tab
end

function BaseGridRender:IsSelect(index)
	return self.select_tab[self.rows][index] or false
end

function BaseGridRender:SetGridData(data_tab)
	self.data_list = data_tab
end

-- 回调函数（scorll_grid）
function BaseGridRender:SetGridCallBack(callback)
	self.grid_call_back = callback
end

-- 回调返回下标
function BaseGridRender:GridItemOnCLick(index)
	local item_index = index + (self.rows - 1) * self.columns
	if nil ~= self.grid_call_back then
		self.grid_call_back(item_index, self)
	end
	if nil ~= self.select_call_back then
		self.select_call_back(self.rows, index)
	end
	-- if nil ~= self.click_callback then
	-- 	self.click_callback()
	-- end
end

-- 回调函数（用于特效）
function BaseGridRender:SetSelectEffectCallBack(callback)
	self.select_call_back = callback
end

function BaseGridRender:CreateItemCells()
	for i = 1, self.columns do
		if self.node_list["ItemRender" .. i] and next(self.node_list["ItemRender" .. i]) then
			self.node_list["ItemRender" .. i].button:AddClickListener(BindTool.Bind(self.GridItemOnCLick, self, i))
		end
	end
end

--------- 重写清空数据
function BaseGridRender:ClearAllDatas()

end
