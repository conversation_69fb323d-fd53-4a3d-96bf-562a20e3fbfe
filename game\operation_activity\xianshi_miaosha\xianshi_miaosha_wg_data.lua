XianshiMiaoshaWGData = XianshiMiaoshaWGData or BaseClass()
XianshiMiaoshaWGData.ConfigPath = "config/auto_new/operaact_timed_spike_cfg_auto"

function XianshiMiaoshaWGData:__init()
	if XianshiMiaoshaWGData.Instance then
		ErrorLog("[XianshiMiaoshaWGData] Attemp to create a singleton twice !")
	end

	XianshiMiaoshaWGData.Instance = self
	self.xianshi_miaosha_cfg = ConfigManager.Instance:GetAutoConfig("operaact_timed_spike_cfg_auto")
	self:CreateXiaoShiMiaoShaInfoSturt()

	self:FlushXianShiMiaoShaCfg()


	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA, {[1] = OPERATION_EVENT_TYPE.LEVEL},
	BindTool.Bind(self.GetAvtivityIsOpen, self), BindTool.Bind(self.ShowXianshiMiaoshaRem<PERSON>, self))

	RemindManager.Instance:Register(RemindName.OperationXianshiMiaosha, BindTool.Bind(self.ShowXianshiMiaoshaRemind, self))
end

function XianshiMiaoshaWGData:__delete()
	XianshiMiaoshaWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.OperationXianshiMiaosha)
end

function XianshiMiaoshaWGData:FlushXianShiMiaoShaCfg()
	self.xianshi_miaosha_cfg = ConfigManager.Instance:GetAutoConfig("operaact_timed_spike_cfg_auto")
	self:InitBuyTimesList()
end

-----------------------------------------限时秒杀-----------------------------------------

function XianshiMiaoshaWGData:CreateXiaoShiMiaoShaInfoSturt()
	self.xianshi_miaosha_data = {}

	self.xianshi_miaosha_select_type = 0

	self:CreateFictitiousDataList()

	self.item_lib_cfg = {}

	self.refresh_time_list = {}

	for i=1,3 do

		self.xianshi_miaosha_data[i] = {}
		self.xianshi_miaosha_data[i]["shop_id"] = 0 				--商品库id
		self.xianshi_miaosha_data[i]["type"] = 0 					--专属类型
		self.xianshi_miaosha_data[i]["refresh_time"] = 0 			--刷新时间
		self.xianshi_miaosha_data[i]["total_quota"] = 0 			--累计值
		self.xianshi_miaosha_data[i]["quota_reward_tag"] = {}		--累计奖励领取标记
		self.xianshi_miaosha_data[i]["item_list"] = {}				--商品列表
	end

	-- self:InitBuyTimesList()
end

--创建一个虚拟的数据表（无出售物品时使用）
function XianshiMiaoshaWGData:CreateFictitiousDataList()
	local other_cfg = self.xianshi_miaosha_cfg.other[1]
	self.xianshi_miaosha_fictitious_list = {}
	for i=1,4 do
		self.xianshi_miaosha_fictitious_list[i] = {}
		for j=1,3 do
			self.xianshi_miaosha_fictitious_list[i][j] = {}
			self.xianshi_miaosha_fictitious_list[i][j].item_id = other_cfg.show_id
			self.xianshi_miaosha_fictitious_list[i][j].is_fictitious = true
		end
	end
end

function XianshiMiaoshaWGData:GetFictitiousDataList()
	return self.xianshi_miaosha_fictitious_list
end

function XianshiMiaoshaWGData:InitBuyTimesList()
	self.buy_times_list = {}

	for k,v in pairs(self.xianshi_miaosha_cfg.item_lib) do
		self.buy_times_list[v.id] = {}
		self.item_lib_cfg[v.id] = v
		self.buy_times_list[v.id].id = v.id
		self.buy_times_list[v.id].times = 0
		self.buy_times_list[v.id].hidded_flag = 0
	end
end

----2624// 运营活动-限时秒杀信息
function XianshiMiaoshaWGData:SetSCOATimedSpikeInfo(protocol)
	self.xianshi_miaosha_data[protocol.type].shop_id = protocol.shop_id
	self.xianshi_miaosha_data[protocol.type].type = protocol.type
	self.xianshi_miaosha_data[protocol.type].refresh_time = protocol.refresh_time
	self.xianshi_miaosha_data[protocol.type].total_quota = protocol.total_quota
	self.xianshi_miaosha_data[protocol.type].quota_reward_tag = protocol.quota_reward_tag
	self.xianshi_miaosha_data[protocol.type].item_count = protocol.item_count
	self.xianshi_miaosha_data[protocol.type].item_list = protocol.item_list

	self.refresh_time_list[protocol.type] = protocol.refresh_time - TimeWGCtrl.Instance:GetServerTime()
end

--2625// 运营活动-限时秒杀商品更新
function XianshiMiaoshaWGData:SetSCOATimedSpikeItemUpdate(protocol)
	self.xianshi_miaosha_data[protocol.type].shop_id = protocol.shop_id
	self.xianshi_miaosha_data[protocol.type].type = protocol.type
	self.xianshi_miaosha_data[protocol.type].refresh_time = protocol.refresh_time
	self.xianshi_miaosha_data[protocol.type].item_count = protocol.item_count
	self.xianshi_miaosha_data[protocol.type].item_list = protocol.item_list

	self.refresh_time_list[protocol.type] = protocol.refresh_time - TimeWGCtrl.Instance:GetServerTime()
end

--2626// 运营活动-限时秒杀额度奖励更新
function XianshiMiaoshaWGData:SetSCOATimedSpikeQuotaUpdate(protocol)
	self.xianshi_miaosha_data[protocol.type].type = protocol.type
	self.xianshi_miaosha_data[protocol.type].total_quota = protocol.total_quota
	self.xianshi_miaosha_data[protocol.type].quota_reward_tag = protocol.quota_reward_tag
end

--2627,// 运营活动-限时秒杀购买次数更新
function XianshiMiaoshaWGData:SetSCOATimedSpikeBuyTimesInfo(protocol)
	for k,v in pairs(protocol.buy_times_list) do
		if self.buy_times_list[v.id] then
			if v.id == self.buy_times_list[v.id].id then
				self.buy_times_list[v.id].times = v.times
				self.buy_times_list[v.id].hidded_flag = v.hidded_flag
			end
		end
	end
end

function XianshiMiaoshaWGData:GetSCOATimedSpikeBuyTimesInfoById(id)
	if self.buy_times_list[id] then
		return self.buy_times_list[id]
	end
end

--根据专属类型拿数据
function XianshiMiaoshaWGData:GetXianShiMiaoShaInfoByType(type)
	return self.xianshi_miaosha_data[type]
end

function XianshiMiaoshaWGData:GetRefreshTimeList()
	return self.refresh_time_list
end

-- type:专属类型
-- is_need_slider：是否需要进度条
function XianshiMiaoshaWGData:GetXianShiMiaoShaListData(type)
	local data_list = {}

	if not self.xianshi_miaosha_data[type] then
		return data_list
	end

	local temp_data_list = self.xianshi_miaosha_data[type].item_list

	if not temp_data_list or IsEmptyTable(temp_data_list) then
		return data_list
	end

	local data_list = self:GetSortDataList(temp_data_list)

	return data_list
end

function XianshiMiaoshaWGData:GetSortDataList(data)
	local sort_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = VipWGData.Instance:IsVip() and RoleWGData.Instance.role_vo.vip_level or 0

	local index = 0
	for k,v in pairs(data) do

 		local item_lib_cfg = self:GetItemLibConfigById(v.id)
 		local buy_info = self:GetSCOATimedSpikeBuyTimesInfoById(v.id)
		 if item_lib_cfg and buy_info and buy_info.hidded_flag ~= 1 then
			index = index + 1
 			sort_list[index] = {}
			sort_list[index].id = v.id
			sort_list[index].last_num = v.last_num
			sort_list[index].num = v.num
			sort_list[index].sort_index = 0
 			sort_list[index].sort_priority = item_lib_cfg.sort_priority
 			sort_list[index].bought_times = buy_info.times 					--已购次数

 			--有个字段还未订号
			if v.last_num <= 0 and buy_info.times < item_lib_cfg.buy_limit_value then
				sort_list[index].sort_index = 500
			elseif v.last_num <= 0 and buy_info.times >= item_lib_cfg.buy_limit_value then
				sort_list[index].sort_index = 500
			elseif v.last_num > 0 and buy_info.times >= item_lib_cfg.buy_limit_value then
				sort_list[index].sort_index = 400
			elseif role_level < item_lib_cfg.level_limit then
				sort_list[index].sort_index = 300
			elseif vip_level < item_lib_cfg.vip_level_limit then
				sort_list[index].sort_index = 200
			else
				sort_list[index].sort_index = 100
			end
		end
	end
	table.sort(sort_list, SortTools.KeyLowerSorters("sort_index", "sort_priority"))
	return sort_list
end

--根据专属类型拿配置
function XianshiMiaoshaWGData:GetShopLibConfigByType(type, shop_id)
	local cur_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)
	local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
	local cfg = self.xianshi_miaosha_cfg.shop_lib
	-- print_error("FFF=====Watering open_time", week, open_time)
	for k,v in pairs(cfg) do
		if v.id == shop_id and type == v.type then
			if cur_day >= v.start_day and cur_day < v.end_day and week == v.week_index then
				return v
			end
		end
	end
end

function XianshiMiaoshaWGData:GetItemLibConfigById(id)
	if self.item_lib_cfg[id] then
		return self.item_lib_cfg[id]
	end
end

function XianshiMiaoshaWGData:SetCurSelectType(select_type)
	self.xianshi_miaosha_select_type = select_type
end

function XianshiMiaoshaWGData:GetCurSelectType()
	return self.xianshi_miaosha_select_type
end

function XianshiMiaoshaWGData:ShowXianshiMiaoshaRemind()
	local is_enough_level = self:GetAvtivityIsOpen()
	local state = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)

	if not is_enough_level or not state then
		return 0
	end

	local role_id = RoleWGData.Instance:GetRoleVo().role_id

	for i=1,3 do

		if PlayerPrefsUtil.GetInt("refresh_remind_flag" .. i .. role_id) == 1 then
			return 1
		end
	end

	for i=1,3 do
		local data = self.xianshi_miaosha_data[i]
		if data then
			local shop_lib_cfg = self:GetShopLibConfigByType(i, data.shop_id)
			if shop_lib_cfg and shop_lib_cfg.gift_quota ~= "" then
				local gift_quota_list = Split(shop_lib_cfg.gift_quota, ",")
				for j=1,#gift_quota_list do
					if gift_quota_list[j] and data.total_quota >= tonumber(gift_quota_list[j]) and data.quota_reward_tag[j] == 0 then
						return 1
					end
				end
			end
		end
	end

	return 0
end

--获取当天的商品库配置数据
function XianshiMiaoshaWGData:GetXianShiMiaoShaShopLibList()
	local shop_lib_list = {}
	local cur_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)
	local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
	local cfg = self.xianshi_miaosha_cfg.shop_lib

	if not cfg then
		return shop_lib_list
	end

	local index = 0
	for k,v in pairs(cfg) do
		if cur_day >= v.start_day and cur_day < v.end_day and week == v.week_index then
			index = index + 1
			shop_lib_list[index] = v
		end
	end

	return shop_lib_list
end

function XianshiMiaoshaWGData:GetAvtivityIsOpen()
	local is_open = false

	local xianshi_miaosha_data = self.xianshi_miaosha_data[1]
	local shop_lib_cfg = self:GetShopLibConfigByType(1, xianshi_miaosha_data.shop_id)
	if not shop_lib_cfg then
		return is_open
	end

	local other_cfg = self.xianshi_miaosha_cfg.other[1]

	if not other_cfg then
		return is_open
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = other_cfg.open_role_level
	return role_level >= need_level
end

function XianshiMiaoshaWGData:GetInterfaceCfg()
	local xianshi_miaosha_data = self.xianshi_miaosha_data[1]

	local shop_lib_cfg = self:GetShopLibConfigByType(1, xianshi_miaosha_data.shop_id)
	if not shop_lib_cfg then
		return
	end

	if self.xianshi_miaosha_cfg.interface[shop_lib_cfg.interface] then
		return self.xianshi_miaosha_cfg.interface[shop_lib_cfg.interface]
	end
end

function XianshiMiaoshaWGData:GetOtherCfg()
	return self.xianshi_miaosha_cfg.other[1]
end


-------------------------------------------------------------------------------------------
