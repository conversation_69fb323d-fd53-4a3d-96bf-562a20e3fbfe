using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.UI;

public class UIGlassBlurCtrl : MonoBehaviour
{
    [SerializeField] private Camera m_Camera = null;
    [SerializeField] private Shader m_BlurShader = null;
    [SerializeField] private Vector2Int m_BlurTextureSize = new Vector2Int(1024, 512);
    
    public delegate void OnRenderTextureSetup(Texture texture);
    public OnRenderTextureSetup onRenderTextureSetup;
    
    private RenderTexture m_GlassBlurTexture;
    private RawImage m_RawImage;
    private Camera m_OriginCamera = null;
    private Material m_BlitMaterial;
    private GlassBlurRenderPass m_GlassBlurRenderPass;
    private bool m_IsInitialized = false;
    
    // 状态跟踪变量
    private bool m_OriginCameraWasEnabled = false;      // 记录原始相机的初始启用状态
    private bool m_ControlledOriginCamera = false;    // 记录是否控制了原始相机的状态
    private bool m_EventRegistered = false;             // 记录渲染事件是否已注册

    void Awake()
    {
        m_Camera = this.GetComponent<Camera>();
        m_OriginCamera = m_Camera;
        
        // 记录原始相机的初始状态
        if (m_OriginCamera != null)
        {
            m_OriginCameraWasEnabled = m_OriginCamera.enabled;
        }

        if (m_BlurShader == null)
        {
            Debug.LogError("模糊着色器未设置！", this);
            enabled = false;
            return;
        }

        m_GlassBlurRenderPass = new GlassBlurRenderPass();
    }

    void OnEnable()
    {
        if (m_BlurShader == null)
        {
            Debug.LogError("模糊着色器未设置！", this);
            enabled = false;
            return;
        }
        
        if (m_Camera == null)
            m_Camera = m_OriginCamera;

        InitializeResources();

        if (m_OriginCamera == m_Camera && m_OriginCamera != null)
        {
            if (!m_OriginCamera.enabled)
            {
                m_OriginCamera.enabled = true;
                m_ControlledOriginCamera = true;
            }
        }

        // 防止重复注册事件
        if (!m_EventRegistered && m_Camera != null && m_BlitMaterial != null)
        {
            RenderPipelineManager.beginCameraRendering += OnCameraRendered;
            m_EventRegistered = true;
        }
    }

    private void InitializeResources()
    {
        if (m_BlitMaterial == null)
        {
            m_BlitMaterial = new Material(m_BlurShader);
        }

        if (m_GlassBlurTexture == null)
        {
            CreateBlurTexture();
        }

        if (onRenderTextureSetup == null)
        {
            SetupUIComponent();
        }
        else
        {
            onRenderTextureSetup.Invoke(m_GlassBlurTexture);
        }
        
        m_IsInitialized = true;
    }

    private void CreateBlurTexture()
    {
        m_GlassBlurTexture = new RenderTexture(
            m_BlurTextureSize.x, 
            m_BlurTextureSize.y, 
            0, 
            GraphicsFormat.R8G8B8A8_UNorm)
        {
            name = "CameraSnapshot",
            autoGenerateMips = false,
            anisoLevel = 0
        };
    }

    private void SetupUIComponent()
    {
        Transform uiLayer = GameObject.Find("GameRoot")?.transform?.Find("UILayer");
        if (uiLayer == null)
        {
            //Debug.LogError("未找到UI层：GameRoot/UILayer");
            return;
        }

        Transform snapShotBg = uiLayer.Find("SnapShotBackground");
        if (snapShotBg == null)
        {
            //Debug.LogError("未找到SnapShotBackground对象");
            return;
        }

        if (m_RawImage == null)
        {
            m_RawImage = snapShotBg.GetComponent<RawImage>();
            if (m_RawImage == null)
            {
                m_RawImage = snapShotBg.gameObject.AddComponent<RawImage>();
            }
        }

        if (m_RawImage != null)
        {
            m_RawImage.enabled = true;
            m_RawImage.texture = m_GlassBlurTexture;
        }
    }

    public void SetShotCamera(Camera camera)
    {
        // 在切换相机前，恢复原有相机状态
        RestoreOriginCameraState();
        
        m_Camera = camera;
        
        // 重置控制状态，因为现在使用不同的相机
        m_ControlledOriginCamera = false;
    }

    private void OnCameraRendered(ScriptableRenderContext context, Camera camera)
    {
        if (camera != m_Camera || !m_IsInitialized) return;

        var renderer = UniversalRenderPipeline.asset?.scriptableRenderer;
        if (renderer == null)
        {
            Debug.LogError("无法获取URP渲染器");
            return;
        }

        var source = renderer.cameraColorTarget;
        
        try
        {
            m_GlassBlurRenderPass.Setup(source, new RenderTargetIdentifier(m_GlassBlurTexture), m_BlitMaterial);
            m_GlassBlurRenderPass.renderPassEvent = RenderPassEvent.AfterRenderingPostProcessing;
            renderer.EnqueuePass(m_GlassBlurRenderPass);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"模糊渲染执行失败: {e.Message}");
        }
        finally
        {
            // 无论是否发生异常，都要处理相机状态
            HandleCameraStateAfterRender();
            
            // 移除事件监听
            if (m_EventRegistered)
            {
                RenderPipelineManager.beginCameraRendering -= OnCameraRendered;
                m_EventRegistered = false;
            }
        }
    }

    private void HandleCameraStateAfterRender()
    {
        if (m_OriginCamera == null) return;
        
        if (m_OriginCamera == m_Camera)
        {
            // 如果控制了原始相机，现在禁用它
            if (m_ControlledOriginCamera)
            {
                m_Camera.enabled = false;
            }
        }
        else
        {
            // 如果使用了不同的相机，恢复原始相机状态
            if (m_OriginCamera.enabled && !m_OriginCameraWasEnabled)
            {
                m_OriginCamera.enabled = false;
            }
        }
    }

    private void RestoreOriginCameraState()
    {
        if (m_OriginCamera == null) return;
        
        // 如果我们修改了原始相机的状态，需要恢复它
        if (m_ControlledOriginCamera)
        {
            m_OriginCamera.enabled = m_OriginCameraWasEnabled;
            m_ControlledOriginCamera = false;
        }
    }
    
    private void Cleanup()
    {
        // 恢复原始相机状态
        RestoreOriginCameraState();
        
        if (m_RawImage != null)
        {
            m_RawImage.enabled = false;
        }

        if (m_GlassBlurTexture != null)
        {
            m_GlassBlurTexture.Release();
            m_GlassBlurTexture = null;
        }

        onRenderTextureSetup = null;
        
        // if (m_BlitMaterial != null)
        // {
        //     DestroyImmediate(m_BlitMaterial);
        //     m_BlitMaterial = null;
        // }

        m_IsInitialized = false;
    }

    void OnDisable()
    {
        // 移除事件监听
        if (m_EventRegistered)
        {
            RenderPipelineManager.beginCameraRendering -= OnCameraRendered;
            m_EventRegistered = false;
        }
        
        Cleanup();
    }

    void OnDestroy()
    {
        // 移除事件监听
        if (m_EventRegistered)
        {
            RenderPipelineManager.beginCameraRendering -= OnCameraRendered;
            m_EventRegistered = false;
        }
        
        Cleanup();
    }

    public class GlassBlurRenderPass : ScriptableRenderPass
    {
        private const string m_ProfileTag = "Glass Blur";
        private RenderTargetIdentifier m_Descriptor;
        private Material m_BlurMat;
        private readonly int m_GlassBlurPassHorizontalRT = Shader.PropertyToID("_GlassBlurPassHorizontalRT");
        private readonly int m_GlassBlurPassVerticalRT = Shader.PropertyToID("_GlassBlurPassVerticalRT");
        private readonly int m_OffsetsPropertyID = Shader.PropertyToID("_Offsets");

        public void Setup(RenderTargetIdentifier source, RenderTargetIdentifier descriptor, Material material)
        {
            m_Descriptor = descriptor;
            m_BlurMat = material;
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            if (m_BlurMat == null)
            {
                Debug.LogError("模糊材质为空，无法执行渲染");
                return;
            }

            CommandBuffer cmd = CommandBufferPool.Get(m_ProfileTag);
            Camera camera = renderingData.cameraData.camera;

            try
            {
                var descriptor = new RenderTextureDescriptor(camera.pixelWidth, camera.pixelHeight)
                {
                    graphicsFormat = GraphicsFormat.B10G11R11_UFloatPack32,
                    msaaSamples = 1
                };

                cmd.GetTemporaryRT(m_GlassBlurPassHorizontalRT, descriptor, FilterMode.Bilinear);
                cmd.GetTemporaryRT(m_GlassBlurPassVerticalRT, descriptor, FilterMode.Bilinear);

                float widthOverHeight = (float)camera.pixelWidth / camera.pixelHeight;
                float oneOverBaseSize = 1.0f / 512.0f;
                float blurAmount = 3.0f;

                cmd.Blit(colorAttachment, m_GlassBlurPassVerticalRT);
                Vector4 offset = new Vector4(0, blurAmount * oneOverBaseSize, 0, 0);
                cmd.SetGlobalVector(m_OffsetsPropertyID, offset);
                cmd.Blit(m_GlassBlurPassVerticalRT, m_GlassBlurPassHorizontalRT, m_BlurMat);

                offset = new Vector4((blurAmount / widthOverHeight) * oneOverBaseSize, 0.0f, 0.0f, 0.0f);
                cmd.SetGlobalVector(m_OffsetsPropertyID, offset);
                cmd.Blit(m_GlassBlurPassHorizontalRT, m_Descriptor, m_BlurMat);

                cmd.ReleaseTemporaryRT(m_GlassBlurPassHorizontalRT);
                cmd.ReleaseTemporaryRT(m_GlassBlurPassVerticalRT);
                
                context.ExecuteCommandBuffer(cmd);
            }
            finally
            {
                cmd.Clear();
                CommandBufferPool.Release(cmd);
            }
        }
    }
}