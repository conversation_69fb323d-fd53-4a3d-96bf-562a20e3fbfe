﻿#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using System.IO;
using System;
using Nirvana;

public class UIViewDynamicChecker : DynamicChecker
{
    private static readonly string rootPath = Application.dataPath.Replace("/Assets", "");
    private static readonly string UIViewDir = "uis/view/";
    private static HashSet<string> filterList = new HashSet<string>()
    {
        //"uis/view/main_ui_prefab",
    };
    private static Dictionary<UnityEngine.Object, string> refPaths = new Dictionary<UnityEngine.Object, string>();
    private static HashSet<string> bundleList = new HashSet<string>();
    private static Dictionary<string, string> cacheBundleNameDic = new Dictionary<string, string>();

    // 排除依赖的目录 -- 这个是必须一样的AB名字(依赖于一些公共目录是正常的）
    private static HashSet<string> excludeDependBudlesHashSet = new HashSet<string> {
            "uis/images_atlas",
            "uis/fonts_bundle",
            "uis/ttf_bundle",
            "shaders",
            "uis/emoji",
            "uis/icons/toxiang_atlas",
            "uis/icons/toxiang_big_atlas",
            "uis/view/main_ui_prefab",
            "uis/view/mainui/images_atlas",
        };

    // 排除依赖的目录(模糊搜索) -- 这个有相关的AB名字不需要完整的(依赖于一些公共目录是正常的）
    private static List<string> ab_path = new List<string>{
            "uis/images",
            "uis/rawimages",
            "uis/nopack",
			"uis/view/mainui",
            "uis/icons/item_",
            "misc_",
            "misc/",
            "uis/view/miscpre_load",
        };

    protected override bool NeedCheck(string bundleName)
    {
        if (bundleName.StartsWith(UIViewDir))
        {
            return true;
        }
        return false;
    }

    protected override void Check(string assetPath, Action<CheckObject[]> callback)
    {
		if (!Application.isPlaying)
		{
			CheckPrefabByEditor(assetPath, callback);
		}
		else
		{
			Scheduler.RunCoroutine(CheckPrefab(assetPath, callback));
		}
	}

    private IEnumerator CheckPrefab(string assetPath, Action<CheckObject[]> callback)
    {
        List<CheckObject> list = new List<CheckObject>();
        GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
        if (null != go)
        {
			CheckPrefabNormal(assetPath, go, list);
			yield return CalcStaticsDepend(assetPath, go, list);
        }

        yield return CheckOtherPrefab(assetPath, list);

        callback(list.ToArray());
        yield return 0;
    }

	private void CheckPrefabByEditor(string assetPath, Action<CheckObject[]> callback)
	{
		List<CheckObject> list = new List<CheckObject>();
		GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
		if (null != go)
		{
			CheckPrefabNormal(assetPath, go, list);
			CalcStaticsDependByEditor(assetPath, go, list);
		}

		CheckOtherPrefabByEditor(assetPath, list);

		callback(list.ToArray());
	}

	private void CheckPrefabNormal(string assetPath, GameObject go, List<CheckObject> list)
	{
		bool isMainView = assetPath.StartsWith("Assets/Game/UIs/View/main_ui");

		if (isMainView)
		{
			CheckMainUICanvas(list);
		}

		//int nodeCount = GetNodeCount(go);
		//if (!isMainView && nodeCount > 250)
		//{
		//    WarningLevel warningLevel = WarningLevel.Normal;
		//    if (nodeCount > 400)
		//    {
		//        warningLevel = WarningLevel.High;
		//    }
		//    CheckObject checkObj = new CheckObject(go, string.Format("UI预制体结点数量过多: {0} (请控制在250个以内)", nodeCount), warningLevel);
		//    list.Add(checkObj);
		//}

		CanvasRenderer[] canvasRenderers = go.GetComponentsInChildren<CanvasRenderer>(true);
		for (int i = 0; i < canvasRenderers.Length; ++i)
		{
			var canvasRender = canvasRenderers[i];
			if (null == canvasRender.GetComponent<Graphic>())
			{
				CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的CanvasRender组件没有关联Graphic", canvasRender.name), WarningLevel.Low, canvasRender.gameObject);
				list.Add(checkObj);
			}
		}

		Text[] texts = go.GetComponentsInChildren<Text>(true);
		for (int i = 0; i < texts.Length; ++i)
		{
			var text = texts[i];
			var font = text.font;
			if (null == font)
			{
				CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的Text组件没有设置Font", text.name), WarningLevel.Low, text.gameObject);
				list.Add(checkObj);
			}
			else
			{
				if (null == AssetDatabase.LoadAssetAtPath<Font>(AssetDatabase.GetAssetPath(font.GetInstanceID())))
				{
					CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的Text组件Font使用了Unity默认的字体", text.name), WarningLevel.Low, text.gameObject);
					list.Add(checkObj);
				}
			}
		}

		CheckMissingReferences(go, list);
		var dependencies = AssetDatabase.GetDependencies(assetPath);
		for (int i = 0; i < dependencies.Length; ++i)
		{
			var dependencePath = dependencies[i];
			GameObject dependenceObj = AssetDatabase.LoadAssetAtPath<GameObject>(dependencePath);
			if (null != dependenceObj && go != dependenceObj)
			{
				CheckMissingReferences(dependenceObj, list);
			}
		}

		CheckUselessImage(go, list);
		CheckUselessRawImage(go, list);
		CheckCamera(go, list);
		CheckRichText(go, list);
		CheckFlowLayoutGroup(go, list);
		CheckUI3D(go, list);
		CheckParticle(go, list);
		if (!Application.isPlaying)
		{
			CheckRectTransform(go, list);
		}
		//long fileLength = GetFileLength(assetPath);
		//if (!isMainView && fileLength > 1024 * 1024)
		//{
		//    WarningLevel warningLevel = WarningLevel.Normal;
		//    if (fileLength > 2 * 1024 * 1024)
		//    {
		//        warningLevel = WarningLevel.High;
		//    }
		//    CheckObject checkObj = new CheckObject(go, string.Format("UI预制体文件太大: {0}MB (请控制在1MB以内)", (float)fileLength / 1024 / 1024), warningLevel);
		//    list.Add(checkObj);
		//}
	}

	protected override BaseWarningWindow GetWindow()
    {
        return UnityEditor.EditorWindow.GetWindowWithRect<UiViewWarningWindow>(new Rect(1200, 600, 800, 600));
    }

    protected override HashSet<string> GetFilterList()
    {
        return filterList;
    }

    private static int GetNodeCount(GameObject go)
    {
        return GetNodeCount(new GameObject[] { go });
    }

    private static long GetFileLength(string assetPath)
    {
        string filePath = Path.Combine(rootPath, assetPath).Replace("\\", "/");
        if (File.Exists(filePath))
        {
            FileInfo info = new FileInfo(filePath);
            return info.Length;
        }
        return 0;
    }

    private void CheckMissingReferences(GameObject go, List<CheckObject> list)
    {
        var missingObjects = GetMissingComponents(go);
        for (int i = 0; i < missingObjects.Length; ++i)
        {
            var missingObj = missingObjects[i];
            CheckObject checkObj;
            if (refPaths.ContainsKey(missingObj))
                checkObj = new CheckObject(go, string.Format("{0}结点上的{1}组件引用丢失: {2}", missingObj.name, missingObj.GetType(), refPaths[missingObj]), WarningLevel.Low, missingObj.gameObject);
            else
                checkObj = new CheckObject(go, string.Format("{0}结点上组件丢失", missingObj.name), WarningLevel.Low);

            list.Add(checkObj);
        }

        if (missingObjects.Length > 0)
        {
            WarningLevel level = WarningLevel.Normal;
            if (missingObjects.Length > 20)
                level = WarningLevel.High;
            CheckObject checkObj = new CheckObject(go, string.Format("预制体上一共有{0}个引用丢失", missingObjects.Length), level);
            list.Add(checkObj);
        }
    }

    private IEnumerator CheckOtherPrefab(string assetPath, List<CheckObject> list)
    {
        string bundleName = GetBundleName(assetPath);
        if (bundleList.Contains(bundleName))
            yield break;

        bundleList.Add(bundleName);
        string[] paths = AssetDatabase.GetAssetPathsFromAssetBundle(bundleName);
        for (int i = 0; i < paths.Length; ++i)
        {
            yield return 0;

            var path = paths[i];
            GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == go)
                continue;


            var missingComponents = GetMissingComponents(go);
            long fileLength = GetFileLength(path);

            int count = missingComponents.Length;
            if (count >= 1000 && fileLength >= 100 * 1024)
            {
                WarningLevel level = WarningLevel.Normal;
                if (fileLength >= 500 * 1024)
                    level = WarningLevel.High;

                CheckObject checkObj = new CheckObject(go, string.Format("可能是废弃的预制体，请及时处理 {0}MB", (float)fileLength / 1024 / 1024), level);
                list.Add(checkObj);
            }
            else
            {
                yield return CalcStaticsDepend(assetPath, go, list);
            }
        }
    }

	private void CheckOtherPrefabByEditor(string assetPath, List<CheckObject> list)
	{
		string bundleName = GetBundleName(assetPath);
		if (bundleList.Contains(bundleName))
			return;

		bundleList.Add(bundleName);
		string[] paths = AssetDatabase.GetAssetPathsFromAssetBundle(bundleName);
		for (int i = 0; i < paths.Length; ++i)
		{
			var path = paths[i];
			GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(path);
			if (null == go)
				continue;

			var missingComponents = GetMissingComponents(go);
			long fileLength = GetFileLength(path);

			int count = missingComponents.Length;
			if (count >= 1000 && fileLength >= 100 * 1024)
			{
				WarningLevel level = WarningLevel.Normal;
				if (fileLength >= 500 * 1024)
					level = WarningLevel.High;

				CheckObject checkObj = new CheckObject(go, string.Format("可能是废弃的预制体，请及时处理 {0}MB", (float)fileLength / 1024 / 1024), level);
				list.Add(checkObj);
			}
		}
	}

	private void CheckMainUICanvas(List<CheckObject> list)
    {
        GameObject mainView = GameObject.Find("GameRoot/UILayer/MainView/Root");
        if (null == mainView)
            return;

        var canvas = mainView.GetComponentsInChildren<Canvas>();
        if (canvas.Length > 15)
        {
            WarningLevel level = WarningLevel.High;
            CheckObject checkObj = new CheckObject(null, string.Format("主界面Canvas数量({0})过多，请不要在主界面使用UIOverrideOrder组件！", canvas.Length), level);
            list.Add(checkObj);
        }
    }

    private static int GetNodeCount(GameObject[] gos)
    {
        Queue<Transform> queue = new Queue<Transform>();
        int totalCount = 0;
        for (int i = 0; i < gos.Length; ++i)
        {
            queue.Clear();
            var root = gos[i].transform;
            int count = 0;
            queue.Enqueue(root);
            while (queue.Count > 0)
            {
                ++count;
                var trans = queue.Dequeue();
                for (int j = 0; j < trans.childCount; ++j)
                {
                    var child = trans.GetChild(j);
                    if (child.name.Contains("Clone"))
                        continue;

                    queue.Enqueue(child);
                }
            }
            totalCount += count;
        }
        return totalCount;
    }

    private static Component[] GetMissingComponents(GameObject go)
    {
        List<Component> list = new List<Component>();

        Queue<Transform> queue = new Queue<Transform>();
        queue.Enqueue(go.transform);
        while (queue.Count > 0)
        {
            var trans = queue.Dequeue();
            for (int i = 0; i < trans.childCount; ++i)
            {
                queue.Enqueue(trans.GetChild(i));
            }

            Component[] cps = trans.GetComponents<Component>();
            foreach (var cp in cps)
            {
                if (null == cp)
                {
                    list.Add(trans);
                    continue;
                }
                SerializedObject so = new SerializedObject(cp);
                var iter = so.GetIterator();
                while (iter.NextVisible(true))
                {
                    if (iter.propertyType == SerializedPropertyType.ObjectReference)
                    {
                        if (iter.objectReferenceValue == null && iter.objectReferenceInstanceIDValue != 0)
                        {
                            list.Add(cp);
                            if (!refPaths.ContainsKey(cp))
                                refPaths.Add(cp, iter.propertyPath);
                            else
                                refPaths[cp] += " + " + iter.propertyPath;
                        }
                    }
                }
            }
        }

        return list.ToArray();
    }

    private static bool IsNameTable(GameObject go)
    {
        var root = go.transform;
        while (root.parent)
        {
            root = root.parent;
        }

        UINameTable[] uINameTables = root.GetComponentsInChildren<UINameTable>(true);
        bool flag = uINameTables.Length > 0 ? false : true;
        for (int i = 0; i < uINameTables.Length; ++i)
        {
            UINameTable uINameTable = uINameTables[i];
            if (uINameTable.Lookup.ContainsValue(go))
            {
                flag = true;
                break;
            }
        }

        return flag;
    }

    private static void CheckUselessImage(GameObject go, List<CheckObject> list)
    {
        Image[] images = go.GetComponentsInChildren<Image>(true);
        CircleImage[] circleImages = go.GetComponentsInChildren<CircleImage>();
        HashSet<Image> hashSet = new HashSet<Image>();
        foreach (var circleImage in circleImages)
        {
            hashSet.Add(circleImage);
        }

        for (int i = 0; i < images.Length; ++i)
        {
            Image image = images[i];
            if (hashSet.Contains(image))
                continue;

            var sprite = image.sprite;
            if (image.color == Color.white && (null == sprite || null == AssetDatabase.LoadAssetAtPath<Sprite>(AssetDatabase.GetAssetPath(sprite.GetInstanceID())))
                && null != image.material)
            {
                if (!IsNameTable(image.gameObject))
                {
                    CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的Image组件可能是废弃的，请及时处理", image.name), WarningLevel.Low, image.gameObject);
                    list.Add(checkObj);
                }
            }
        }
    }

    private static void CheckUselessRawImage(GameObject go, List<CheckObject> list)
    {
        RawImage[] images = go.GetComponentsInChildren<RawImage>(true);
        for (int i = 0; i < images.Length; ++i)
        {
            RawImage image = images[i];
            var texture = image.texture;
            if (image.color == Color.white && (null == texture || null == AssetDatabase.LoadAssetAtPath<Texture>(AssetDatabase.GetAssetPath(texture.GetInstanceID()))))
            {
                if (!IsNameTable(image.gameObject))
                {
                    CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的RawImage组件可能是废弃的，请及时处理", image.name), WarningLevel.Low, image.gameObject);
                    list.Add(checkObj);
                }
            }
        }
    }

    private static void CheckCamera(GameObject go, List<CheckObject> list)
    {
        Camera[] cameras = go.GetComponentsInChildren<Camera>(true);
        for (int i = 0; i < cameras.Length; ++i)
        {
            var camera = cameras[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的camera组件是多余的，请及时处理", camera.name), WarningLevel.Normal, camera.gameObject);
            list.Add(checkObj);
        }
    }

    private static void CheckRichText(GameObject go, List<CheckObject> list)
    {
        RichTextGroup[] richTextGroups = go.GetComponentsInChildren<RichTextGroup>(true);
        for (int i = 0; i < richTextGroups.Length; ++i)
        {
            var richText = richTextGroups[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的RichTextGroup组件已经废弃，请改为EmojiText去实现", richText.name), WarningLevel.Normal, richText.gameObject);
            list.Add(checkObj);
        }
    }

    private static void CheckFlowLayoutGroup(GameObject go, List<CheckObject> list)
    {
        FlowLayoutGroup[] flowLayoutGroups = go.GetComponentsInChildren<FlowLayoutGroup>(true);
        for (int i = 0; i < flowLayoutGroups.Length; ++i)
        {
            var flowLayoutGroup = flowLayoutGroups[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的FlowLayoutGroup组件已经废弃，请改为EmojiText去实现", flowLayoutGroup.name), WarningLevel.Normal, flowLayoutGroup.gameObject);
            list.Add(checkObj);
        }
    }

    private static void CheckUI3D(GameObject go, List<CheckObject> list)
    {
        UI3DDisplay[] uI3DDisplays = go.GetComponentsInChildren<UI3DDisplay>(true);
        for (int i = 0; i < uI3DDisplays.Length; ++i)
        {
            var uI3DDisplay = uI3DDisplays[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的UI3DDisplay组件是废弃的，请换成UI3DModel的做法", uI3DDisplay.name), WarningLevel.Normal, uI3DDisplay.gameObject);
            list.Add(checkObj);
        }
    }

    private static void CheckParticle(GameObject go, List<CheckObject> list)
    {
        ParticleSystem[] particleSystems = go.GetComponentsInChildren<ParticleSystem>(true);
        for (int i = 0; i < particleSystems.Length; ++i)
        {
            var particleSystem = particleSystems[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0} 请用GameObjectAttach组件去加载ParticleSystem (如果是特效文件，请不要放在UI文件夹)", particleSystem.name), WarningLevel.Normal, particleSystem.gameObject);
            list.Add(checkObj);
        }
    }

	private static void CheckRectTransform(GameObject go, List<CheckObject> list)
	{
		float[] rectVars = null;
		RectTransform[] rectComponents = go.GetComponentsInChildren<RectTransform>(true);

		for (int i = 0; i < rectComponents.Length; i++)
		{
			RectTransform rect = rectComponents[i];
			rectVars = new float[] {
				rect.localPosition.x, rect.localPosition.y, rect.localPosition.z,
				rect.sizeDelta.x, rect.sizeDelta.y
			};

			for (int j = 0; j < rectVars.Length; j++)
			{
				if (!Mathf.Approximately(rectVars[j], Mathf.Floor(rectVars[j])))
				{
					CheckObject checkObj = new CheckObject(go, string.Format("{0} 中的节点 {1} 有小数, 请确认是否异常", rect.name, rect), WarningLevel.Normal, rect.gameObject);
					list.Add(checkObj);
					break;
				}
			}

			if (rect.GetComponent<Text>() != null)
			{
				for (int j = 3; j < rectVars.Length; j++)
				{
					if (rectVars[j] % 2 != 0)
					{
						CheckObject checkObj = new CheckObject(go, string.Format("{0} 中的节点 {1} 长宽有奇数, 请确认是否异常", rect.name, rect), WarningLevel.Normal, rect.gameObject);
						list.Add(checkObj);
						break;
					}
				}
			}
		}
	}

	private IEnumerator CalcStaticsDepend(string assetPath, GameObject go, List<CheckObject> list)
    {
        string bundleName = GetBundleName(assetPath);
        int index = 0;
        int totalCount = 0;
        Component[] cps = go.GetComponentsInChildren<Component>(true);
        foreach (var cp in cps)
        {
            if (null == cp)
            {
                continue;
            }

            SerializedObject so = new SerializedObject(cp);
            var iter = so.GetIterator();
            while (iter.NextVisible(true))
            {
                if (iter.propertyType == SerializedPropertyType.ObjectReference)
                {
                    if (iter.objectReferenceValue != null)
                    {
                        string dependAssetPath = AssetDatabase.GetAssetPath(iter.objectReferenceValue);
                        string dependBundleName = GetBundleName(dependAssetPath);
                        if (IsWrongDepend(bundleName, dependBundleName))
                        {
                            CheckObject checkObj1 = new CheckObject(iter.objectReferenceValue, string.Format("AB路径({0})被{1} AB路径({2})非法引用", dependBundleName, go.name, bundleName), WarningLevel.Normal);
                            CheckObject checkObj2 = new CheckObject(go, string.Format("{0}结点上的{1}的{2}引用了{3}(禁止跨界面引用)", cp.name, cp.GetType(), iter.propertyPath, iter.objectReferenceValue.name), WarningLevel.Normal, cp.gameObject);
                            list.Add(checkObj1);
                            list.Add(checkObj2);
                            ++totalCount;
                        }
                        ++index;
                        if (index % 10 == 0)
                            yield return 0;
                    }
                }
            }
        }

        if (totalCount > 0)
        {
            WarningLevel level = WarningLevel.Normal;
            if (totalCount > 5)
                level = WarningLevel.High;
            CheckObject checkObj = new CheckObject(go, string.Format("预制体上一共有{0}个引用非法", totalCount), level);
            list.Add(checkObj);
        }
    }

	private void CalcStaticsDependByEditor(string assetPath, GameObject go, List<CheckObject> list)
	{
		string bundleName = GetBundleName(assetPath);
		int totalCount = 0;
		Component[] cps = go.GetComponentsInChildren<Component>(true);
		foreach (var cp in cps)
		{
			if (null == cp)
			{
				continue;
			}

			SerializedObject so = new SerializedObject(cp);
			var iter = so.GetIterator();
			while (iter.NextVisible(true))
			{
				if (iter.propertyType == SerializedPropertyType.ObjectReference)
				{
					if (iter.objectReferenceValue != null)
					{
						string dependAssetPath = AssetDatabase.GetAssetPath(iter.objectReferenceValue);
						string dependBundleName = GetBundleName(dependAssetPath);
						if (IsWrongDepend(bundleName, dependBundleName))
						{
							CheckObject checkObj1 = new CheckObject(iter.objectReferenceValue, string.Format("AB路径({0})被{1} AB路径({2})非法引用", dependBundleName, go.name, bundleName), WarningLevel.Normal);
							CheckObject checkObj2 = new CheckObject(go, string.Format("{0}结点上的{1}的{2}引用了{3}(禁止跨界面引用)", cp.name, cp.GetType(), iter.propertyPath, iter.objectReferenceValue.name), WarningLevel.Normal, cp.gameObject);
							list.Add(checkObj1);
							list.Add(checkObj2);
							++totalCount;
						}
					}
				}
			}
		}

		if (totalCount > 0)
		{
			WarningLevel level = WarningLevel.Normal;
			if (totalCount > 5)
				level = WarningLevel.High;
			CheckObject checkObj = new CheckObject(go, string.Format("预制体上一共有{0}个引用非法", totalCount), level);
			list.Add(checkObj);
		}
	}

	private static bool IsWrongDepend(string bundleName, string dependBundleName)
    {
		bool isWrong = false;
        if (bundleName != dependBundleName                                                        // 相同的bundle排除
                    && !string.IsNullOrEmpty(dependBundleName)                                    // 没有指定bundle排除
                    && !excludeDependBudlesHashSet.Contains(dependBundleName)                    // 公共模块排除
                    && !GetMatching(dependBundleName)                                            // 公共模块排除 -- 这个取的是模糊的
                    && !IsSameChunk(bundleName, dependBundleName)                                // 自已模块下的排除
                    )
        {
            isWrong = true;
		}

		return isWrong;
    }

    private static bool GetMatching(string bundle_name)
    {
        var is_matching = false;
        foreach (var item in ab_path)
        {
			if (null != bundle_name && "" != bundle_name && bundle_name.Contains(item))
            {
                is_matching = true;
                break;
            }
        }
        return is_matching;
    }

    private static string GetReplaceData(string str)
	{
		if (null == str || "" == str)
		{
			return "";
		}

		var new_str = str.Replace("_prefab", "");
		new_str = new_str.Replace("/images/nopack_atlas", "");
        new_str = new_str.Replace("/images_atlas", "");
        new_str = new_str.Replace("_atlas", "");
        new_str = new_str.Replace("_images", "");
        return new_str;
    }

    private static bool IsSameChunk(string bundleName, string dependBundleName)
    {
        bundleName = GetReplaceData(bundleName);
        dependBundleName = GetReplaceData(dependBundleName);
        if (bundleName == dependBundleName)
        {
            return true;
        }
        else
        {
            var splits = bundleName.Split('/');
            var dependSplits = dependBundleName.Split('/');
            if (splits.Length > 2 && dependSplits.Length > 2)
            {
                for (int i = 0; i < 3; ++i)
                {
                    if (splits[i] != dependSplits[i])
                    {
                        return false;
                    }
                }
                return true;
            }
        }

        return false;
    }

    private static string GetBundleName(string path)
    {
        string bundle_name = string.Empty;
        if (!cacheBundleNameDic.TryGetValue(path, out bundle_name))
        {
            var importer = AssetImporter.GetAtPath(path);
            if (null != importer)
            {
                bundle_name = importer.assetBundleName;
                cacheBundleNameDic.Add(path, bundle_name);
            }
        }

        return bundle_name;
    }
}
#endif