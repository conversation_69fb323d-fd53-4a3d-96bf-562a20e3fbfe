-------------------------福利 在线奖励begin-----------------------------------
CSFetchOnlineReward = CSFetchOnlineReward or BaseClass(BaseProtocolStruct)     -- 6800 在线奖励领取奖励请求
function CSFetchOnlineReward:__init()
	self.msg_type = 6800
	self.reward_type = 0
end

function CSFetchOnlineReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.reward_type)
end

CSOnlineRewardInfo = CSOnlineRewardInfo or BaseClass(BaseProtocolStruct)                  --6802 在线奖励信息请求
function CSOnlineRewardInfo:__init()
	self.msg_type = 6802
end

function CSOnlineRewardInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

SCOnlineRewardInfo = SCOnlineRewardInfo or BaseClass(BaseProtocolStruct)         -- 6801 在线奖励信息
function SCOnlineRewardInfo:__init()
	self.msg_type = 6801
	self.fetch_online_reward_flag = 0    --在线奖励领取标记
	self.reserve_sh = 0                  --保留值，暂时无用
	self.today_online_time = 0           --今日在线时长
	self.online_reward_record_list = {}
end

function SCOnlineRewardInfo:Decode()
	self.fetch_online_reward_flag = MsgAdapter.ReadShort()
	MsgAdapter.ReadShort()
	self.today_online_time = MsgAdapter.ReadInt()

	self.online_reward_record_list = {}
	for i = 1, 4 do
		self.online_reward_record_list[i] = MsgAdapter.ReadChar()
	end
end

-------------------------福利 在线奖励end-----------------------------------

-------------------------排行榜 评价begin-----------------------------------

CSGetRankRoleEvaluateInfo = CSGetRankRoleEvaluateInfo or BaseClass(BaseProtocolStruct)     -- 6805 排行榜评价信息请求
function CSGetRankRoleEvaluateInfo:__init()
	self.msg_type = 6805
	self.target_uid = 0
end

function CSGetRankRoleEvaluateInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.target_uid)
end

CSRankRoleEvaluateReq = CSRankRoleEvaluateReq or BaseClass(BaseProtocolStruct)     -- 6806 评价请求
function CSRankRoleEvaluateReq:__init()
	self.msg_type = 6806
	self.target_uid = 0
	self.is_admire = 0    -- 0为鄙视 1为仰慕
	self.reserve_sh = 0
end

function CSRankRoleEvaluateReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.target_uid or 0)
	MsgAdapter.WriteShort(self.is_admire or 0)
	MsgAdapter.WriteShort(self.reserve_sh or 0)
end

SCRankRoleEvaluateInfo = SCRankRoleEvaluateInfo or BaseClass(BaseProtocolStruct)         -- 6807 返回评价信息
function SCRankRoleEvaluateInfo:__init()
	self.msg_type = 6807
	self.target_uid = 0    --玩家UID
	self.admire_num = 0                  --赞美数
	self.can_admire_num = 0         -- 剩余赞美数
	self.contempt_num = 0           --鄙视数
	self.rank_uid_list = {}         -- 被赞美的用户列表
end

function SCRankRoleEvaluateInfo:Decode()
	self.target_uid = MsgAdapter.ReadInt()
	self.admire_num = MsgAdapter.ReadInt()
	self.contempt_num = MsgAdapter.ReadInt()
	self.can_admire_num = MsgAdapter.ReadShort()
	MsgAdapter.ReadShort()
	for i = 1, 10 do
		self.rank_uid_list[i] = MsgAdapter.ReadInt()
	end
end

SCServerReloadConfig = SCServerReloadConfig or BaseClass(BaseProtocolStruct)         -- 6808 服务器热更
function SCServerReloadConfig:__init()
	self.msg_type = 6808
end

function SCServerReloadConfig:Decode()
	self.version = MsgAdapter.ReadInt()
	self.type = MsgAdapter.ReadInt()
end

-------------------------排行榜 评价end-----------------------------------


-- 灵童 ------------------------------------------------------------------

-- 所有信息：都为0
-- 进阶操作：param1是升级次数，填1就可以；param2填是否自动购买，0不自动
-- 使用灵童丹：param1填灵童丹type
-- 灵童出战：param1填灵童id
-- 升级技能：param1填技能id

-- 进阶成功后下发
-- 另外在 SCOperateResult（1154）中加了枚举 OP_LINGTONG_UPGRADE = 41
SCLingTongUpgradeInfo = SCLingTongUpgradeInfo or BaseClass(BaseProtocolStruct)
function SCLingTongUpgradeInfo:__init()
	self.msg_type = 6802
	self.grade_type = 0
	self.grade = 0
	self.bless_val = 0
	self.active_id_flag = {}
end

function SCLingTongUpgradeInfo:Decode()
	self.grade_type = MsgAdapter.ReadShort()
	self.grade = MsgAdapter.ReadShort()
	self.bless_val = MsgAdapter.ReadInt()
	self.active_id_flag = bit:d2b(MsgAdapter.ReadInt())
	self.fail_times = MsgAdapter.ReadInt()
end

-- LINGTONG_DAN_MAX_TYPE_COUNT = 2
-- 成功使用灵丹后下发
SCLingTongDanInfo = SCLingTongDanInfo or BaseClass(BaseProtocolStruct)
function SCLingTongDanInfo:__init()
	self.msg_type = 6803
	self.type = 0
	self.used_dan_num_list = {}
end

function SCLingTongDanInfo:Decode()
	self.type = MsgAdapter.ReadInt()
	self.used_dan_num_list = {}
	for i = 1, GameEnum.LINGTONG_DAN_MAX_TYPE_COUNT do
		self.used_dan_num_list[i] = MsgAdapter.ReadUShort()
	end
end

-- 进阶成功并激活了新的灵童后下发
-- 主动请求后下发
SCLingTongUsedInfo = SCLingTongUsedInfo or BaseClass(BaseProtocolStruct)
function SCLingTongUsedInfo:__init()
	self.msg_type = 6804
	self.type = 0
	self.used_id = 0

end

function SCLingTongUsedInfo:Decode()
	self.type = MsgAdapter.ReadShort()
	self.used_id = MsgAdapter.ReadShort()
end


-- 只要下发了SCLingTongUsedInfo，接着就会广播此协议
SCLingTongViewChange = SCLingTongViewChange or BaseClass(BaseProtocolStruct)
function SCLingTongViewChange:__init()
	self.msg_type = 6805

	self.obj_id = 0
	self.used_id_list = {}
	self.lingtong_name = 0
end

function SCLingTongViewChange:Decode()
	self.obj_id = MsgAdapter.ReadUShort()
	self.used_id_list = {}
	for i = 0, GameEnum.LINGTONG_TYPE_MAX_COUNT - 1 do
		self.used_id_list[i] = MsgAdapter.ReadShort()
	end
	self.lingtong_name = MsgAdapter.ReadStrN(32)
end


-- LINGTONG_SKILL_MAX_COUNT = 5
-- 进阶成功并激活了新的技能后下发
-- 升级技能成功后下发
SCLingTongSkillInfo = SCLingTongSkillInfo or BaseClass(BaseProtocolStruct)
function SCLingTongSkillInfo:__init()
	self.msg_type = 6806

	self.skill_level_list =	{}
end

function SCLingTongSkillInfo:Decode()
	self.skill_level_list = {}
	for i = 0, GameEnum.LINGTONG_SKILL_MAX_COUNT - 1 do
		local vo = {}
		vo.skill_type = i
		vo.owner_type = MsgAdapter.ReadChar()
		vo.skill_level = MsgAdapter.ReadChar()
		self.skill_level_list[i] = vo
	end
	 MsgAdapter.ReadUChar()
end

-- -- 送礼排行信息
-- SCRASendgiftInfo = SCRASendgiftInfo or BaseClass(BaseProtocolStruct)
-- function SCRASendgiftInfo:__init()
-- 	self.msg_type = 6816
-- 	self.send_gift_time = 0
-- 	self.be_send_gift_time = 0
-- end

-- function SCRASendgiftInfo:Decode()
-- 	self.send_gift_time = MsgAdapter.ReadShort()			-- 送礼次数
-- 	self.be_send_gift_time = MsgAdapter.ReadShort()			-- 收礼次数
-- end

-- -- 临时技能数据
-- SCTmpSkillEndTimestamp = SCTmpSkillEndTimestamp or BaseClass(BaseProtocolStruct)
-- function SCTmpSkillEndTimestamp:__init()
-- 	self.msg_type = 6818
-- 	self.euqiped_skill_seq = 0
-- 	self.skill_end_timestamp_list = {}
-- end

-- function SCTmpSkillEndTimestamp:Decode()
-- 	self.euqiped_skill_seq = MsgAdapter.ReadInt()
-- 	self.skill_end_timestamp_list = {}

-- 	for i = 0, GameEnum.SKILL_MAX_COUNT - 1 do
-- 		self.skill_end_timestamp_list[i] = MsgAdapter.ReadUInt()
-- 	end
-- end

-- 宝藏遗迹信息
SCRAHgyjAllInfo = SCRAHgyjAllInfo or BaseClass(BaseProtocolStruct)
function SCRAHgyjAllInfo:__init()
	self.msg_type = 6820
end

function SCRAHgyjAllInfo:Decode()
	self.free_times = MsgAdapter.ReadShort()			-- 免费次数
	self.reset_times = MsgAdapter.ReadShort()			-- 重置次数
	self.now_index = MsgAdapter.ReadShort()				-- 人物所在位置
	self.reale_count = MsgAdapter.ReadShort()			-- 奖励个数

	self.reward_list = {}
	for i = 1, self.reale_count do
		self.reward_list[i] = {}
		self.reward_list[i].item_id = MsgAdapter.ReadInt() 			-- 奖励id
		self.reward_list[i].item_num = MsgAdapter.ReadInt() 		-- 奖励数量
	end
end
-- --首充团购
-- SCRAFirstChongzhiGroupBuyInfo = SCRAFirstChongzhiGroupBuyInfo or BaseClass(BaseProtocolStruct)
-- function SCRAFirstChongzhiGroupBuyInfo:__init()
-- 	self.msg_type = 6815
-- end

-- function SCRAFirstChongzhiGroupBuyInfo:Decode()
-- 	self.today_total_chongzhi = MsgAdapter.ReadUInt()		-- 玩家累计充值数
-- 	self.server_chongzhi_num  =	MsgAdapter.ReadUInt()		-- 全服首冲人数
-- 	self.active_flag = MsgAdapter.ReadInt()					--激活标记
-- 	self.fecth_flag  = MsgAdapter.ReadInt()					--领取标记
-- end

-- 灵童改名请求
CSLingTongChangeNameReq = CSLingTongChangeNameReq or BaseClass(BaseProtocolStruct)
function CSLingTongChangeNameReq:__init()
	self.msg_type = 6808
	self.lingtong_name = 0
end

function CSLingTongChangeNameReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteStrN(self.lingtong_name,32)
end


------- 灵兽系统Began ------------
CSLingshouOperaReq = CSLingshouOperaReq or BaseClass(BaseProtocolStruct)
function CSLingshouOperaReq:__init()
	self.msg_type = 6835

	self.req_type = 0
	self.param_1 = 0
	self.param_2 = 0
	self.param_3 = 0
end

function CSLingshouOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteUShort(self.req_type)
	MsgAdapter.WriteUShort(self.param_1)
	MsgAdapter.WriteUShort(self.param_2)
	MsgAdapter.WriteUShort(self.param_3)
end

SCLingshouAllInfo = SCLingshouAllInfo or BaseClass(BaseProtocolStruct)
function SCLingshouAllInfo:__init()
	self.msg_type = 6836

	self.score = 0
	self.all_lingshou_list = {}
end

function SCLingshouAllInfo:Decode()
	self.score = MsgAdapter.ReadInt()
	self.all_lingshou_list = {}
	for i = 0 , GameEnum.LINGSHOU_MAX_LINGSHOU_COUNT -1 do
		local item = {}
		item.seq = MsgAdapter.ReadShort()
		item.level = MsgAdapter.ReadShort()
		item.bless = MsgAdapter.ReadShort()
		item.star_level = MsgAdapter.ReadShort()
		self.all_lingshou_list[i] = item
	end
end

SCLingshouSingleInfo = SCLingshouSingleInfo or BaseClass(BaseProtocolStruct)
function SCLingshouSingleInfo:__init()
	self.msg_type = 6837

	self.item = {}
end

function SCLingshouSingleInfo:Decode()
	self.item = {}
	self.item.seq = MsgAdapter.ReadShort()
	self.item.level = MsgAdapter.ReadShort()
	self.item.bless = MsgAdapter.ReadShort()
	self.item.star_level = MsgAdapter.ReadShort()
end

SCLingshouScore = SCLingshouScore or BaseClass(BaseProtocolStruct)
function SCLingshouScore:__init()
	self.msg_type = 6838

	self.score = 0
end

function SCLingshouScore:Decode()
	self.score = MsgAdapter.ReadInt()
end
---------灵兽系统End----------

---------灵兽系统End----------end

------------------灯塔寻宝------------------------------
SCRALightTowerExploreInfo = SCRALightTowerExploreInfo or BaseClass(BaseProtocolStruct)
function SCRALightTowerExploreInfo:__init()
	self.msg_type = 6830
	self.extern_reward_fetch_flag = 0                       --收集奖领取标志
	self.server_reward_fetcht_flag = 0						--全服奖励领取标志
	self.cur_index = 0										--当前抽中index
	self.cur_layer = 0										--当前层数
	self.reserver_sh = 0
	self.server_draw_total_times = 0						--全服抽奖次数
end

function SCRALightTowerExploreInfo:Decode()
	self.light_tower_info = {}								--存储每层的奖励领取标记的列表
	for i = 1, GameEnum.RA_LIGHT_TOWER_EXPLORE_MAX_LAYER do
		local seek_fetch = MsgAdapter.ReadChar()
		self.light_tower_info[i] = seek_fetch
	end

	self.extern_reward_fetch_flag = MsgAdapter.ReadChar()
	self.server_reward_fetcht_flag = MsgAdapter.ReadChar()
	self.cur_index = MsgAdapter.ReadChar()
	self.cur_layer = MsgAdapter.ReadShort()
	self.reserver_sh = MsgAdapter.ReadShort()
	self.server_draw_total_times = MsgAdapter.ReadInt()
end

LightTowerExploreGridInfo = LightTowerExploreGridInfo or BaseClass(BaseProtocolStruct)
function LightTowerExploreGridInfo:__init()
	self.msg_type = 6831
	self.reward_count = 0
end

function LightTowerExploreGridInfo:Decode()
	self.reward_count = MsgAdapter.ReadInt()
	self.reward_list = {}
	for i = 1, self.reward_count do
		local data = {}
		data.layer = MsgAdapter.ReadChar()
		data.index = MsgAdapter.ReadChar()
		table.insert(self.reward_list, data)
	end
end

------------------地图寻宝------------------------------
SCRAMapHuntAllInfo = SCRAMapHuntAllInfo or BaseClass(BaseProtocolStruct)
function SCRAMapHuntAllInfo:__init()
	self.msg_type = 6821
	self.server_flush_times = 0
	self.next_flush_timestamp = 0
	self.server_reward_fetch_flag = 0
	self.cur_has_used_free_count = 0
	self.can_extern_reward = 0
end

function SCRAMapHuntAllInfo:Decode()
	self.route_info = {}
	self.route_info.route_index = MsgAdapter.ReadChar()   --当前路径下标
	self.route_info.route_active_flag = MsgAdapter.ReadChar() --路径激活标记(当前激活该路径的第几个)
	MsgAdapter.ReadShort()								  -- 占位
	self.route_info.city_list = {}							 --刷出来的城市列表
	for i = 1, GameEnum.MAX_RA_MAP_HUNT_CITY_COUNT_OF_ROUTE do
		local city = MsgAdapter.ReadChar()
		table.insert(self.route_info.city_list, city)
	end
	self.route_info.city_fetch_flag = MsgAdapter.ReadChar()   -- 城市领取标记
	self.server_flush_times = MsgAdapter.ReadInt()		  -- 全服刷新次数
	self.next_flush_timestamp = MsgAdapter.ReadUInt()	  -- 下次系统刷新时间
	self.server_reward_fetch_flag = MsgAdapter.ReadShort()  -- 全服奖励领取标志
	self.cur_has_used_free_count = MsgAdapter.ReadShort()    -- 剩余可用免费次数
	self.can_extern_reward = MsgAdapter.ReadInt()				-- 获得最后奖励的路线下标

end

-- 装备转性信息
SCChangeItemSexInfo = SCChangeItemSexInfo or BaseClass(BaseProtocolStruct)
function SCChangeItemSexInfo:__init()
	self.msg_type = 6858
end

function SCChangeItemSexInfo:Decode()
	self.is_free = MsgAdapter.ReadInt() == 0 		-- 是否免费
end

--------------------------- 藏宝阁 ------------------------
--  6825 藏宝阁领取奖励请求
CSFetchTreasureHouseRewardReq = CSFetchTreasureHouseRewardReq or BaseClass(BaseProtocolStruct)
function CSFetchTreasureHouseRewardReq:__init()
	self.msg_type = 6825
	self.seq = 0 -- 领取奖励序号
end

function CSFetchTreasureHouseRewardReq:Encode()
    MsgAdapter.WriteBegin(self.msg_type)

    MsgAdapter.WriteShort(self.seq)
  	MsgAdapter.WriteShort(0)
end

--  6826 藏宝阁信息
SCTreasureHouseInfo = SCTreasureHouseInfo or BaseClass(BaseProtocolStruct)
function SCTreasureHouseInfo:__init()
	 self.msg_type = 6826
end

function SCTreasureHouseInfo:Decode()
	self.first_chongzhi_time = MsgAdapter.ReadUInt() -- 特殊首充时间戳
	self.fetch_reward_flag = MsgAdapter.ReadShort() --	领取奖励标记
	-- self.fetch_reward_flag = bit:_rshift(self.fetch_reward_flag,1)
	MsgAdapter.ReadShort()
	self.interval_days = MsgAdapter.ReadInt() 	-- 领取奖励天数
end


-- 七天投资投资请求
CSSevenDayInvestReq = CSSevenDayInvestReq or BaseClass(BaseProtocolStruct)
function CSSevenDayInvestReq:__init()
	self.msg_type = 6830
end

function CSSevenDayInvestReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 七天投资领取奖励请求
CSSevenDayInvestFetchReq = CSSevenDayInvestFetchReq or BaseClass(BaseProtocolStruct)
function CSSevenDayInvestFetchReq:__init()
	self.msg_type = 6831
	self.seq = 0
	self.type = 0
end

function CSSevenDayInvestFetchReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteShort(self.seq)
	MsgAdapter.WriteShort(self.type)
end

-- 七天投资投资标记
SCSevenDayInvestInfo = SCSevenDayInvestInfo or BaseClass(BaseProtocolStruct)
function SCSevenDayInvestInfo:__init()
	 self.msg_type = 6832
	 self.invest_flag = 0
end

function SCSevenDayInvestInfo:Decode()
	self.invest_flag = MsgAdapter.ReadShort()
	MsgAdapter.ReadShort()
end


-- 七天投资领取奖励标记
SCSevenDayInvestFetchInfo = SCSevenDayInvestFetchInfo or BaseClass(BaseProtocolStruct)
function SCSevenDayInvestFetchInfo:__init()
	 self.msg_type = 6833
	 self.invest_day_fetch_flag = 0
	 self.invest_vip_fetch_flag = 0
end

function SCSevenDayInvestFetchInfo:Decode()
	self.invest_vip_fetch_flag = MsgAdapter.ReadShort()
	self.invest_day_fetch_flag = MsgAdapter.ReadShort()
end

--///////////////////////////////////// 活动兑换 /////////////////////////////////////////
CSConvertActivityRewardReq = CSConvertActivityRewardReq or BaseClass(BaseProtocolStruct)
function CSConvertActivityRewardReq:__init()
	self.msg_type = 6840

	self.seq = 0
	self.reserve_sh = 0
end

function CSConvertActivityRewardReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.seq)
	MsgAdapter.WriteShort(self.reserve_sh)
end

SCActivityConvertInfo = SCActivityConvertInfo or BaseClass(BaseProtocolStruct)
function SCActivityConvertInfo:__init()
	 self.msg_type = 6841
	 self.perfect_plate_convert_mark = {}
end

function SCActivityConvertInfo:Decode()
	self.perfect_plate_convert_mark = {}

	for i=1, 16 do
		local perfect_plate_convert_mark = {}
		perfect_plate_convert_mark.times = MsgAdapter.ReadChar()
		table.insert(self.perfect_plate_convert_mark, perfect_plate_convert_mark)
	end
end

------------------------------- 道具转性 ------------------------------------
CSChangeItemSexOper = CSChangeItemSexOper or BaseClass(BaseProtocolStruct)
function CSChangeItemSexOper:__init()
	self.msg_type = 6855
end

function CSChangeItemSexOper:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.grid_index) 			-- 道具在角色背包的下标
	MsgAdapter.WriteInt(self.num) 					-- 转化数目
	print_log("#请求道具转性6855#", self.grid_index, self.num)
end


SCChangeItemSexRet = SCChangeItemSexRet or BaseClass(BaseProtocolStruct)
function SCChangeItemSexRet:__init()
	 self.msg_type = 6856
end

function SCChangeItemSexRet:Decode()
	self.ret = MsgAdapter.ReadInt() 				-- 1 转化成功 2 转化失败
	print_log("#转化结果6856#", self.ret)
end

------------------------------- 道具转性End ------------------------------------

----------------------仙玉-----------------------
-- 仙玉操作
CSLingYuOperate = CSLingYuOperate or BaseClass(BaseProtocolStruct)
function CSLingYuOperate:__init()
	self.msg_type = 6860
end

function CSLingYuOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate)
	MsgAdapter.WriteInt(self.param1 or 0)
	MsgAdapter.WriteInt(self.param2 or 0)
	MsgAdapter.WriteInt(self.param3 or 0)
end

-- 灵玉信息
SCLingYuInfo = SCLingYuInfo or BaseClass(BaseProtocolStruct)
function SCLingYuInfo:__init()
	self.msg_type = 6861
	self.stone_list = {}
	self.refine_level = {}
	self.refine_val = {}
	self.polish_list = {}
end

function SCLingYuInfo:Decode()
	self.lingyu_infos = {}
	self.refine_level = {}
	-- self.total_lingyu_level = MsgAdapter.ReadInt()
	-- self.lingyu_active_star_level = MsgAdapter.ReadInt()

	for i = 0, GameEnum.MAX_ROLE_EQUIP_NUM - 1 do
		self.lingyu_infos[i] = {}
		self.refine_level[i] = MsgAdapter.ReadInt()
		for k = 0, GameEnum.MAX_STONE_COUNT - 1 do
			self.lingyu_infos[i][k] = {}
			self.lingyu_infos[i][k].item_id = MsgAdapter.ReadUShort()
			self.lingyu_infos[i][k].is_bind = MsgAdapter.ReadChar()
			self.lingyu_infos[i][k].is_open = MsgAdapter.ReadChar()
			self.lingyu_infos[i][k].is_inlay = self.lingyu_infos[i][k].item_id > 0
		end
	end
end

-- 仙玉 一键镶嵌
CSLingYuOneKeyInlay = CSLingYuOneKeyInlay or BaseClass(BaseProtocolStruct)
function CSLingYuOneKeyInlay:__init()
	self.msg_type = 6862
end

function CSLingYuOneKeyInlay:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(#self.item_list)
	for k,v in ipairs(self.item_list) do
		MsgAdapter.WriteInt(v.part_index)
		MsgAdapter.WriteShort(v.slot_index)
		MsgAdapter.WriteShort(v.bag_index)
	end
end

SCLingYuBaseInfo = SCLingYuBaseInfo or BaseClass(BaseProtocolStruct)
function SCLingYuBaseInfo:__init()
	self.msg_type = 6863
end

function SCLingYuBaseInfo:Decode()
	local total_lingyu_level = {}

	for i = 0, 24 do
		total_lingyu_level[i] = MsgAdapter.ReadInt()
	end

	self.total_lingyu_level = total_lingyu_level

	local lingyu_active_star_level = {}

	for i = 0, 24 do
		lingyu_active_star_level[i] = MsgAdapter.ReadInt()
	end

	self.lingyu_active_star_level = lingyu_active_star_level
end

SCLingYuUpdate = SCLingYuUpdate or BaseClass(BaseProtocolStruct)
function SCLingYuUpdate:__init()
	self.msg_type = 6864
end

function SCLingYuUpdate:Decode()
	self.equip_body_index = MsgAdapter.ReadInt()
	self.refine_level = MsgAdapter.ReadInt()

	local lingyu_info = {}

	for k = 0, GameEnum.MAX_STONE_COUNT - 1 do
		lingyu_info[k] = {}
		lingyu_info[k].item_id = MsgAdapter.ReadUShort()
		lingyu_info[k].is_bind = MsgAdapter.ReadChar()
		lingyu_info[k].is_open = MsgAdapter.ReadChar()
		lingyu_info[k].is_inlay = lingyu_info[k].item_id > 0
	end

	self.lingyu_info = lingyu_info
end


----------------------仙玉 end-----------------------


--------------------------------------- 战力前十开始 -------------------------------------
--战力前十登录广播 6865
SCCapabilityRankTopInfo = SCCapabilityRankTopInfo or BaseClass(BaseProtocolStruct)
function SCCapabilityRankTopInfo:__init()
	 self.msg_type = 6865
end

function SCCapabilityRankTopInfo:Decode()
	self.user_id = MsgAdapter.ReadInt()
	self.rank = MsgAdapter.ReadInt()
end
--------------------------------------- 战力前十结束-------------------------------------


------------------------------------ 经验玉 ------------------------------------------

-- 6882 经验玉信息请求
CSExpJadeFetchReward = CSExpJadeFetchReward or BaseClass(BaseProtocolStruct)
function CSExpJadeFetchReward:__init()
	self.msg_type = 6882
	self.fetch_type = 0 			              -- 领取类型：0.一倍 1.一点五倍 2.两倍
end

function CSExpJadeFetchReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.fetch_type)
end

-- 6883 经验玉信息下发
SCExpJadeInfo = SCExpJadeInfo or BaseClass(BaseProtocolStruct)
function SCExpJadeInfo:__init()
	self.msg_type = 6883
	self.exp_jade_store_exp = 0 				  -- 当前经验玉累计经验
end

function SCExpJadeInfo:Decode()
	self.exp_jade_store_exp = MsgAdapter.ReadInt()
end

--------------------- 点唇 --------------------
-- 邀请伴侣
CSInviteLoverReq = CSInviteLoverReq or BaseClass(BaseProtocolStruct)
function CSInviteLoverReq:__init()
	self.msg_type = 6885
	self.type = 0 									-- 请求类型(0表示邀请伴侣点唇，1表示确认邀请信息，2进入界面， 3取消邀请)
end

function CSInviteLoverReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.type)
end

-- 邀请确认
SCInviteLoverInfo = SCInviteLoverInfo or BaseClass(BaseProtocolStruct)
function SCInviteLoverInfo:__init()
	self.msg_type = 6886
	self.type = 0 									-- (0表示邀请已经发送，1表示可以开始点唇)
end

function SCInviteLoverInfo:Decode()
	self.type = MsgAdapter.ReadInt()
end

-- 点唇请求
CSDianChunReq = CSDianChunReq or BaseClass(BaseProtocolStruct)
function CSDianChunReq:__init()
	self.msg_type = 6887
	self.type = 0 									-- 请求类型(0表示开始点唇，1表示逆天改命, 2 cd状态清0)
	self.param = 0 									-- 当type为1时可用(0,1,2,3,4,5,6),0代表所有的都变为唇
end

function CSDianChunReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.type)
	MsgAdapter.WriteShort(self.param)
end

-- 夫妻点唇信息
SCCoupleDianChunInfo = SCCoupleDianChunInfo or BaseClass(BaseProtocolStruct)
function SCCoupleDianChunInfo:__init()
	self.msg_type = 6888
	self.reason = 0 								-- 通知原因(0是其他，1是购买)
	self.role_id = 0 								-- 人物ID
	self.qingyuan_dianchun_flage = 0 				-- 伴侣是否点唇
	self.couple_dianchun_count = 0 					-- 点唇次数
	self.buy_couple_dianchun_count = 0 				-- 点唇购买次数
	self.couple_dianchun_cd_time_stamp = 0 			-- 时间戳
	self.couple_dianchun_changelife_time_stamp = 0  -- 逆天改命时间戳
	self.self_dice_data = {} 						-- 自己点唇数据
	self.qingyuan_dice_data = {} 					-- 伴侣点唇数据
end

function SCCoupleDianChunInfo:Decode()
	self.reason = MsgAdapter.ReadInt()
	self.role_id = MsgAdapter.ReadInt()
	self.qingyuan_dianchun_flage = MsgAdapter.ReadInt()
	self.couple_dianchun_count = MsgAdapter.ReadShort()
	self.buy_couple_dianchun_count = MsgAdapter.ReadShort()
	self.couple_dianchun_cd_time_stamp = MsgAdapter.ReadUInt()
	self.couple_dianchun_changelife_time_stamp = MsgAdapter.ReadUInt()

	self.self_dice_data = {}
	for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
		self.self_dice_data[i] = MsgAdapter.ReadShort()
	end

	self.qingyuan_dice_data = {}
	for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
		self.qingyuan_dice_data[i] = MsgAdapter.ReadShort()
	end
end

-- 领取奖励请求
CSCoupleDianChunGetRewardReq = CSCoupleDianChunGetRewardReq or BaseClass(BaseProtocolStruct)
function CSCoupleDianChunGetRewardReq:__init()
	self.msg_type = 6889
end

function CSCoupleDianChunGetRewardReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 领取奖励信息
SCCoupleDianChunGetRewardInfo = SCCoupleDianChunGetRewardInfo or BaseClass(BaseProtocolStruct)
function SCCoupleDianChunGetRewardInfo:__init()
	self.msg_type = 6890
	self.get_reward_flage = 0
	self.double_reward = 0					-- 双倍奖励
end

function SCCoupleDianChunGetRewardInfo:Decode()
	self.get_reward_flage = MsgAdapter.ReadShort()
	self.double_reward = MsgAdapter.ReadShort()
end

-- 夫妻点唇关闭请求
CSCoupleDianChunCloseReq = CSCoupleDianChunCloseReq or BaseClass(BaseProtocolStruct)
function CSCoupleDianChunCloseReq:__init()
	self.msg_type = 6891
end

function CSCoupleDianChunCloseReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 夫妻点唇关闭信息
SCCoupleDianChunCloseInfo = SCCoupleDianChunCloseInfo or BaseClass(BaseProtocolStruct)
function SCCoupleDianChunCloseInfo:__init()
	self.msg_type = 6892
end

function SCCoupleDianChunCloseInfo:Decode()
end

-- 摸金请求
CSTouchGoldReq = CSTouchGoldReq or BaseClass(BaseProtocolStruct)
function CSTouchGoldReq:__init()
	self.msg_type = 6895
	self.index = 0 									-- //罗盘下标（0 绿,1蓝,2紫,3橙）
end

function CSTouchGoldReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.index)
end

-- 领取奖励信息
SCTouchGoldInfo = SCTouchGoldInfo or BaseClass(BaseProtocolStruct)
function SCTouchGoldInfo:__init()
	self.msg_type = 6896
	self.item_id = 0
end

function SCTouchGoldInfo:Decode()
	self.item_id = MsgAdapter.ReadInt()
end


-- 装备回收
CSZhuanShenRecycle = CSZhuanShenRecycle or BaseClass(BaseProtocolStruct)
function CSZhuanShenRecycle:__init()
	self.msg_type = 6899
	self.equip_index_count = 0
	self.recycle_equip_index_list = {}
end

function CSZhuanShenRecycle:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.equip_index_count)
	self.recycle_equip_index_list = {}
	for i = 1, GameEnum.MAX_ZHUANSHEN_RECYCLE_EQUIP_INDEX_COUNT do
		MsgAdapter.WriteShort(self.recycle_equip_index_list[i])
	end
end

-----------------------------------问卦-------------------------------------
--问卦信息
SCWenGuaInfo = SCWenGuaInfo or BaseClass(BaseProtocolStruct)
function SCWenGuaInfo:__init()
	self.msg_type = 6842
end

function SCWenGuaInfo:Decode()
	self.current_pool_id = MsgAdapter.ReadChar()
    self.buy_open_pool_id = MsgAdapter.ReadChar()
    self.week_buy_open_pool_times = MsgAdapter.ReadShort()
	self.draw_id_list = {}
	for i = 1, 20 do
		self.draw_id_list[i] = MsgAdapter.ReadShort()
	end
end

-- 问挂操作
CSWenGuaOpera = CSWenGuaOpera or BaseClass(BaseProtocolStruct)
function CSWenGuaOpera:__init()
	self.msg_type = 6843
	self.op_type = 1
	self.param1 = 0
	self.param2 = 0
	self.param3 = 0
end

function CSWenGuaOpera:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.op_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--问挂个人记录
SCWenGuaPersonRecord = SCWenGuaPersonRecord or BaseClass(BaseProtocolStruct)
function SCWenGuaPersonRecord:__init()
	self.msg_type = 6844
end

function SCWenGuaPersonRecord:Decode()
	self.record_count = MsgAdapter.ReadInt()
	self.record_list = {}
	for i = 1, self.record_count do
		self.record_list[i] = {}
		self.record_list[i].reward_id = MsgAdapter.ReadShort()
		self.record_list[i].draw_pool_id = MsgAdapter.ReadChar()
		self.record_list[i].open_next_pool_id = MsgAdapter.ReadChar()
		self.record_list[i].timestamp = MsgAdapter.ReadUInt()
	end
end

--问挂全服记录
SCWenGuaServerRecord = SCWenGuaServerRecord or BaseClass(BaseProtocolStruct)
function SCWenGuaServerRecord:__init()
	self.msg_type = 6845
end

function SCWenGuaServerRecord:Decode()
	self.record_count = MsgAdapter.ReadInt()
	self.record_list = {}
	for i = 1, self.record_count do
		self.record_list[i] = {}
		self.record_list[i].name = MsgAdapter.ReadStrN(32)
		self.record_list[i].reward_id = MsgAdapter.ReadInt()
		self.record_list[i].timestamp = MsgAdapter.ReadUInt()
	end
end


------------------------------   战令   ----------------------------------------
CSZhanLingOpera = CSZhanLingOpera or BaseClass(BaseProtocolStruct)
function CSZhanLingOpera:__init()
	self.msg_type = 6846
	self.opera_type = 0
	self.param_1 = 0
	self.param_2 = 0
	self.param_3 = 0
end

function CSZhanLingOpera:Encode()
	-- print_error("----请求----", self.opera_type, self.param_1, self.param_2, self.param_3)
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.opera_type)
	MsgAdapter.WriteShort(self.param_1)
	MsgAdapter.WriteShort(self.param_2)
	MsgAdapter.WriteShort(self.param_3)
end

SCZhanLingInfo = SCZhanLingInfo or BaseClass(BaseProtocolStruct)
function SCZhanLingInfo:__init()
	self.msg_type = 6847
end

function SCZhanLingInfo:Decode()
	self.zhanling_level = MsgAdapter.ReadShort()
	self.act_value = MsgAdapter.ReadShort()
	self.zhanling_round = MsgAdapter.ReadShort()
	self.day_in_round = MsgAdapter.ReadShort()
	self.isopen_zhanling_sys = MsgAdapter.ReadChar()
	self.buy_high_flag = MsgAdapter.ReadChar()
	self.overflow_nor_reward_num = MsgAdapter.ReadShort()
	self.overflow_high_reward_num = MsgAdapter.ReadShort()
	self.is_new_round = MsgAdapter.ReadShort()

	self.level_reward_flag = {}
	for i = 1, ZhanLing.MaxLevel do
		self.level_reward_flag[i] = MsgAdapter.ReadChar()
	end
end

SCZhanLingTaskInfo = SCZhanLingTaskInfo or BaseClass(BaseProtocolStruct)
function SCZhanLingTaskInfo:__init()
	self.msg_type = 6848
end

function SCZhanLingTaskInfo:Decode()
	local daily_task_num = MsgAdapter.ReadInt()
	self.week_task_list = {}
	self.daily_task_list = {}

	for i = 1, ZhanLing.MaxRoundExchangeLimitCount do
		local data = {}
		data.index = i
		data.task_id = MsgAdapter.ReadInt()
		data.process = MsgAdapter.ReadShort()
		data.task_state = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		self.week_task_list[i] = data
	end

	for i = 1, daily_task_num do
		local data = {}
		data.index = i
		data.task_id = MsgAdapter.ReadInt()
		data.process = MsgAdapter.ReadShort()
		data.task_state = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		self.daily_task_list[i] = data
	end
end

-- 限购物品信息
SCZhanLingLimitExchangeItemInfo = SCZhanLingLimitExchangeItemInfo or BaseClass(BaseProtocolStruct)
function SCZhanLingLimitExchangeItemInfo:__init()
	self.msg_type = 6850
end

function SCZhanLingLimitExchangeItemInfo:Decode()
	self.zhanling_coin_num = MsgAdapter.ReadInt()
	self.round_limit_list = {}
	self.daily_limit_list = {}
	for i = 1, ZhanLing.MaxRoundExchangeLimitCount do
		local data = {}
		local shop_seq = MsgAdapter.ReadShort()
		local has_exchange_num = MsgAdapter.ReadShort()
		self.round_limit_list[shop_seq] = has_exchange_num
	end

	for i = 1, ZhanLing.MaxRoundExchangeLimitCount do
		local data = {}
		local shop_seq = MsgAdapter.ReadShort()
		local has_exchange_num = MsgAdapter.ReadShort()
		self.daily_limit_list[shop_seq] = has_exchange_num
	end
end

-- 各档等级购买次数
SCZhanLingLevelBuyInfo = SCZhanLingLevelBuyInfo or BaseClass(BaseProtocolStruct)
function SCZhanLingLevelBuyInfo:__init()
	self.msg_type = 6851
end

function SCZhanLingLevelBuyInfo:Decode()
	self.level_buy_count_list = {}
	for i = 1, ZhanLing.MaxLevelBuyType do
		self.level_buy_count_list[i] = MsgAdapter.ReadShort()
	end
end
--------------------------------------------------------------------------------

------------------------新 跨服1v1-----------------------
CSCross1V1Opera = CSCross1V1Opera or BaseClass(BaseProtocolStruct)
function CSCross1V1Opera:__init()
	self.msg_type = 6810
end

function CSCross1V1Opera:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--通知匹配成功（可以进场景了） 6811
SCCross1V1MatchSucc = SCCross1V1MatchSucc or BaseClass(BaseProtocolStruct)
function SCCross1V1MatchSucc:__init()
	self.msg_type = 6811
end

function SCCross1V1MatchSucc:Decode()
	self.opponent_info = {}
	self.opponent_info.uuid = MsgAdapter.ReadUUID()
	self.opponent_info.side = MsgAdapter.ReadInt()
	self.opponent_info.name = MsgAdapter.ReadStrN(32)
	self.opponent_info.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	self.opponent_info.level = MsgAdapter.ReadShort()
	self.opponent_info.avatar_key_big = MsgAdapter.ReadUInt()
	self.opponent_info.avatar_key_small = MsgAdapter.ReadUInt()
	self.opponent_info.capability = MsgAdapter.ReadLL()
	self.opponent_info.prof = MsgAdapter.ReadInt()


	local uuid = self.opponent_info.uuid
	local key = uuid.temp_low --.. uuid.temp_high
	local avatar_key_big = self.opponent_info.avatar_key_big
	local avatar_key_small = self.opponent_info.avatar_key_small
	AvatarManager.Instance:SetAvatarKey(key, avatar_key_big, avatar_key_small)
end

--淘汰赛赛程表 6812
SCCross1V1KnockoutMatchInfo = SCCross1V1KnockoutMatchInfo or BaseClass(BaseProtocolStruct)
function SCCross1V1KnockoutMatchInfo:__init()
	self.msg_type = 6812
end

function SCCross1V1KnockoutMatchInfo:Decode()
	self.knockout_item_list = {}
	for i=1,16 do
		local item = {}
		item.uuid = MsgAdapter.ReadUUID()
		item.name = MsgAdapter.ReadStrN(32)
		item.sex = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		item.level = MsgAdapter.ReadShort()
		item.avatar_key_big = MsgAdapter.ReadUInt()
		item.avatar_key_small = MsgAdapter.ReadUInt()
		item.capability = MsgAdapter.ReadLL()
		item.prof = MsgAdapter.ReadInt()

		item.index = i
		self.knockout_item_list[i] = item
		local uuid = item.uuid
		local key = uuid.temp_low --.. uuid.temp_high
		local avatar_key_big = item.avatar_key_big
		local avatar_key_small = item.avatar_key_small
		AvatarManager.Instance:SetAvatarKey(key, avatar_key_big, avatar_key_small)
	end
	self.indexes_16_to_8 = {}		--16进8名单   knockout_item_list 的下标index
	self.indexes_8_to_4 = {}		--8进4名单
	self.indexes_4_to_2 = {}		--4进2名单
	self.indexes_16_to_8_index = {}		--16进8名单 index 为key的列表
	self.indexes_8_to_4_index = {}		--8进4名单 index 为key的列表
	self.indexes_4_to_2_index = {}		--4进2名单 index 为key的列表
	-- self.indexes_winner = 0		--冠军
	for i=1,8 do
		local to_index = MsgAdapter.ReadInt() + 1	--服务端从0开始  客户端
		local item = {}
		item.index = i
		item.win = true
		self.indexes_16_to_8[i] = to_index
		self.indexes_16_to_8_index[to_index] = item
	end
	for i=1,4 do
		local to_index = MsgAdapter.ReadInt() + 1
		local item = {}
		item.index = i
		item.win = true
		self.indexes_8_to_4[i] = to_index
		self.indexes_8_to_4_index[to_index] = item
	end
	for i=1,2 do
		local to_index = MsgAdapter.ReadInt() + 1
		local item = {}
		item.index = i
		item.win = true
		self.indexes_4_to_2[i] = to_index
		self.indexes_4_to_2_index[to_index] = item
	end
	self.indexes_winner = MsgAdapter.ReadInt() + 1
	self.cur_opponent_index = MsgAdapter.ReadInt() + 1		--当前对手的下标
end

--积分榜 6813
SCCross1V1ScoreRank = SCCross1V1ScoreRank or BaseClass(BaseProtocolStruct)
function SCCross1V1ScoreRank:__init()
	self.msg_type = 6813
end

function SCCross1V1ScoreRank:Decode()
	self.my_rank_index = MsgAdapter.ReadInt() -- 自己的排名索引
	local rank_count = MsgAdapter.ReadInt()
	self.rank_list = {}
	self.rank_key_list = {}
	for i=1,rank_count do
		local item = {}
		item.uuid = MsgAdapter.ReadUUID()
		item.plat_type = MsgAdapter.ReadInt()
		item.uid = MsgAdapter.ReadInt()
		item.name = MsgAdapter.ReadStrN(32)
		item.guild_name = MsgAdapter.ReadStrN(32)
		item.score = MsgAdapter.ReadInt()
		item.server_id = MsgAdapter.ReadInt()
		item.sex = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		item.vip = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		item.avatar_key_big = MsgAdapter.ReadUInt()
		item.avatar_key_small = MsgAdapter.ReadUInt()
		item.prof = MsgAdapter.ReadInt()

		item.rank = i
		local key = item.uuid.temp_low .. item.uuid.temp_high
		self.rank_key_list[key] = item
		self.rank_list[i] = item
	end
end

--玩家个人信息 6814
local GUESS_ROUND_COUNT = 4
SCCross1V1PersonInfo = SCCross1V1PersonInfo or BaseClass(BaseProtocolStruct)
function SCCross1V1PersonInfo:__init()
	self.msg_type = 6814
end

function SCCross1V1PersonInfo:Decode()
	self.score = MsgAdapter.ReadInt()				--积分
	self.knockout_rank = MsgAdapter.ReadInt()		-- 淘汰赛排名
	self.knockout_guess_winner_flag = {}
	self.knockout_guess_reward_fetch_flag = {}
	self.knockout_guess_winner_flag_num = {}
	self.knockout_guess_reward_fetch_flag_num = {}
	for i=1,GUESS_ROUND_COUNT do
		local flag = MsgAdapter.ReadUInt()
		self.knockout_guess_winner_flag[i] = bit:d2b(flag)	--// 竞猜标记
		self.knockout_guess_winner_flag_num[i] = flag	--// 竞猜标记
	end
	for i=1,GUESS_ROUND_COUNT do
		local flag = MsgAdapter.ReadUInt()
		self.knockout_guess_reward_fetch_flag[i] = bit:d2b(flag)	--// 竞猜奖励领取标记
		self.knockout_guess_reward_fetch_flag_num[i] = flag	--// 竞猜标记
	end
	-- self.knockout_guess_winner_flag = MsgAdapter.ReadUInt()			-- 竞猜标记
	self.win_times = MsgAdapter.ReadInt()
	self.lose_times = MsgAdapter.ReadInt()
	self.score_reward_fetch_flag = MsgAdapter.ReadUInt()			--积分奖励领取标记
	self.rank_reward_fetch_flag = MsgAdapter.ReadUInt()				--段位奖励领取标记
	self.match_count_reward_fetch_flag = MsgAdapter.ReadUInt()		--场次奖励领取标记
end

--比赛信息 6815
SCCross1V1MatchInfo = SCCross1V1MatchInfo or BaseClass(BaseProtocolStruct)
function SCCross1V1MatchInfo:__init()
	self.msg_type = 6815
end

function SCCross1V1MatchInfo:Decode()
	self.match_state = MsgAdapter.ReadInt()						-- 0没开始 1预选赛 2预选赛结算 3淘汰赛
	self.next_fight_start_time = MsgAdapter.ReadUInt()			-- 下一场战斗开始时间
	self.prematch_round = MsgAdapter.ReadInt()  				-- 预选赛轮次
	self.knockout_round = MsgAdapter.ReadInt()					-- 淘汰赛轮次 0:没开始 1:16进8 2:8进4 3:4进2 4:2进1 5:已决出冠军
	self.is_prematch_all_matched = MsgAdapter.ReadInt()			-- 本轮预选赛是否已全部匹配完成
end

-- 6816 跨服1V1战斗结束
SCCross1V1FightResult = SCCross1V1FightResult or BaseClass(BaseProtocolStruct)
function SCCross1V1FightResult:__init()
	self.msg_type = 6816
end

function SCCross1V1FightResult:Decode()
	self.is_win = MsgAdapter.ReadInt()
end


--// 6817 跨服1V1冠军
SCCross1V1WinnerInfo = SCCross1V1WinnerInfo or BaseClass(BaseProtocolStruct)
function SCCross1V1WinnerInfo:__init()
	self.msg_type = 6817
end

function SCCross1V1WinnerInfo:Decode()
	self.palyer_info_list = {}
	local MAX_WINNER_COUNT = MsgAdapter.ReadInt()
	for i = 1, MAX_WINNER_COUNT do
		local play_data = {}
		play_data.plat_type = MsgAdapter.ReadInt()
		play_data.role_id = MsgAdapter.ReadInt()
		self.palyer_info_list[i] = play_data
	end
	
end

------------------------新 跨服1v1-----------------------

-- 装备合成新
CSEquipComposeReq = CSEquipComposeReq or BaseClass(BaseProtocolStruct)
function CSEquipComposeReq:__init()
	self.msg_type = 6852
	self.equip_list = {}
end

function CSEquipComposeReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.target_id)
	MsgAdapter.WriteInt(self.star_num)

	local data, is_bag, index
	for i = 1, GameEnum.COMPOSE_EQUIP_STUFF_NUM do
		data = self.equip_list[i] or {}
		is_bag = data.is_bag or 0
		index = data.index or -1
		MsgAdapter.WriteInt(is_bag)
		MsgAdapter.WriteInt(index)
	end
end


-- 个人特殊Boss信息
SCPersonSpecialBossSceneInfo = SCPersonSpecialBossSceneInfo or BaseClass(BaseProtocolStruct)
function SCPersonSpecialBossSceneInfo:__init()
	self.msg_type = 6853
end

function SCPersonSpecialBossSceneInfo:Decode()
	self.info = {
		seq = MsgAdapter.ReadInt(),
		monster_id = MsgAdapter.ReadInt(),
		monster_obj_id = MsgAdapter.ReadUShort(),
		is_end = MsgAdapter.ReadChar(),
		is_pass = MsgAdapter.ReadChar(),
		kick_out_time = MsgAdapter.ReadUInt(),			-- 踢出时间
		fb_end_time = MsgAdapter.ReadUInt(),			-- 副本结束时间
	}
end






-- 通用物品分解
CSItemResolveReq = CSItemResolveReq or BaseClass(BaseProtocolStruct)
function CSItemResolveReq:__init()
	self.msg_type = 6857
end

function CSItemResolveReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.bag_index)
	MsgAdapter.WriteUShort(self.item_id)
	MsgAdapter.WriteShort(self.resolve_count)

end

SCRoleNoticeBossStatus = SCRoleNoticeBossStatus or BaseClass(BaseProtocolStruct)
function SCRoleNoticeBossStatus:__init()
	self.msg_type = 6870
end

function SCRoleNoticeBossStatus:Decode()
	self.cur_notice_grade = MsgAdapter.ReadInt()
end

CSBossNoticeTaskStatus = CSBossNoticeTaskStatus or BaseClass(BaseProtocolStruct)
function CSBossNoticeTaskStatus:__init()
	self.msg_type = 6871
end

function CSBossNoticeTaskStatus:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.suit_index)
end

SCRoleNoticBossSaveClientInfo = SCRoleNoticBossSaveClientInfo or BaseClass(BaseProtocolStruct)
function SCRoleNoticBossSaveClientInfo:__init()
	self.msg_type = 6872
	self.client_suit_index_flag = {}
end

function SCRoleNoticBossSaveClientInfo:Decode()
	local flag = MsgAdapter.ReadUInt()
	self.client_suit_index_flag = bit:d2b(flag)
end

-- 装备一键合成新
CSEquipBatchComposeReq = CSEquipBatchComposeReq or BaseClass(BaseProtocolStruct)
function CSEquipBatchComposeReq:__init()
	self.msg_type = 6873
	self.equip_list = {}
end

function CSEquipBatchComposeReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.target_id)
	MsgAdapter.WriteInt(self.star_num)
	MsgAdapter.WriteShort(self.compose_num)
	MsgAdapter.WriteChar(self.sign_stuff_count)
	MsgAdapter.WriteChar(#self.equip_list)
	
	local data, is_bag, index
	for i = 1, #self.equip_list do
		data = self.equip_list[i] or {}
		is_bag = data.is_bag or 0
		index = data.index or -1
		MsgAdapter.WriteInt(is_bag)
		MsgAdapter.WriteInt(index)
	end
end
