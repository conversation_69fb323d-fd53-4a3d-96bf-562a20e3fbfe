﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_DeviceInfoWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("DeviceInfo");
		<PERSON><PERSON>RegFunction("GetDeviceInfoJsonData", GetDeviceInfoJsonData);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDeviceInfoJsonData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = Nirvana.DeviceInfo.GetDeviceInfoJsonData();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

