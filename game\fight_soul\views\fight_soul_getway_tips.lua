FightSoulGetWayTips = FightSoulGetWayTips or BaseClass(SafeBaseView)

function FightSoulGetWayTips:__init()
	self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "fight_soul_get_way_panel")
    self.view_name = "FightSoulGetWayTips"
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(0)
end

function FightSoulGetWayTips:ReleaseCallBack()
    if nil ~= self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function FightSoulGetWayTips:LoadCallBack()
    self.item_list = AsyncListView.New(FightSoulGetWayRender, self.node_list["way_list"])
end

function FightSoulGetWayTips:SetDataAndOpen(get_way_list)
    self.get_way_list = get_way_list
	self:Open()
end

function FightSoulGetWayTips:OnFlush()
    if nil ~= self.item_list then
        self.item_list:SetDataList(self.get_way_list)
    end

    self:ChangePanelHeight()
end


function FightSoulGetWayTips:ChangePanelHeight()
    local length = #self.get_way_list
    if length <= 2 then
		RectTransform.SetSizeDeltaXY(self.node_list.bg_root.rect, 324, 226)
		RectTransform.SetSizeDeltaXY(self.node_list.way_list.rect, 302, 172)
    else
        local high_offset = math.min(76 * (length - 2), 152)
		RectTransform.SetSizeDeltaXY(self.node_list.bg_root.rect, 324, 226 + high_offset)
		RectTransform.SetSizeDeltaXY(self.node_list.way_list.rect, 302, 172 + high_offset)
    end
end

-------------------------------------------------------------------------------------------
FightSoulGetWayRender = FightSoulGetWayRender or BaseClass(BaseRender)
function FightSoulGetWayRender:__init()

end

function FightSoulGetWayRender:__delete()

end

function FightSoulGetWayRender:LoadCallBack()
    self.node_list.btn_goto.button:AddClickListener(BindTool.Bind(self.OnClickGoTo, self))
end

function FightSoulGetWayRender:OnClickGoTo()
	if IsEmptyTable(self.data) then
		return
	end

    ViewManager.Instance:OpenByCfg(self.data.cfg.open_panel)
	FightSoulWGCtrl.Instance:CloseFightSoulGetWayTips()
end

function FightSoulGetWayRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetF2MainUIImage(self.data.cfg.icon)
    self.node_list.icon.image:LoadSprite(bundle, asset)
    self.node_list.text_name.text.text = self.data.cfg.discription
end
