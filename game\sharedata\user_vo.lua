
UserVo = UserVo or BaseClass()

-- 仅用来处理服务器登录流程
-- 主要是保存着平台相关的信息、角色列表、session_key、场景ID，场景key
local DB_INDEX_MARK_BIT = 16 --根据角色id获取服id的偏移值


function UserVo:__init()
	if UserVo.Instance then
		ErrorLog("[UserVo] Attempt to create singleton twice!")
		return
	end
	UserVo.Instance = self

	--登录平台信息
	self.account_user_id = ""
	self.is_white = 0						-- 1白名单
	self.plat_session_key = ""				-- 加密sign
	self.plat_login_time = 0
	self.plat_server_id = 1
	self.real_server_id = 1
	self.plat_is_verify = false
	self.plat_server_name = ""
	self.uid = ""
	self.access_token = ""

	-- 角色登录信息
	self.now_role_index = 0
	self.role_list = {}
	self.role_count = 0
	self.is_role_list_get = false

	self.login_time = 0
	self.anti_wallow = 0
	self.session_key = ""
	self.gs_index = 0

	self.scene_id = 0
	self.scene_key = 0
	self.last_scene_id = 0
	self.origin_role_id = 0
	self.is_in_game_server = false				-- 是否在游戏服
end

function UserVo:__delete()
	UserVo.Instance = nil
end

function UserVo:ClearRoleList()
	self.role_list = {}
	self.role_count = 0
end

--添加角色到角色列表
function UserVo:AddRole(role_id, role_name, avatar, sex, prof, country, level, create_time, online_time, last_logout_time, capability, avatar_key_big, avatar_key_small)
	local role_info = {}
	self.role_count = self.role_count + 1
	self.role_list[self.role_count] = role_info
	role_info.role_id = role_id
	role_info.role_name = role_name
	role_info.avatar = avatar
	role_info.prof = prof
	role_info.country = country
	role_info.level = level
	role_info.create_time = create_time
	role_info.online_time = online_time
	role_info.last_logout_time = last_logout_time
	role_info.capability = capability
	role_info.avatar_key_big = avatar_key_big
	role_info.avatar_key_small = avatar_key_small
end

function UserVo:SetNowRole(role_id)
	self.origin_role_id = role_id
	local is_set_suc = false
	for i = 1, self.role_count, 1 do
		if self.role_list[i].role_id == role_id then
			local now_role = self.role_list[i]
			local main_role_vo = GameVoManager.Instance:GetMainRoleVo()

			main_role_vo.role_id = now_role.role_id
			main_role_vo.server_id = UserVo.GetServerId(now_role.role_id)
			main_role_vo.role_name = now_role.role_name
			main_role_vo.avatar = now_role.avatar
			main_role_vo.prof = now_role.prof
			main_role_vo.country = now_role.country
			main_role_vo.level = now_role.level
			main_role_vo.create_time = now_role.create_time
			main_role_vo.online_time = now_role.online_time
			main_role_vo.last_logout_time = now_role.last_logout_time
			main_role_vo.capability = now_role.capability
			main_role_vo.avatar_key_big = now_role.avatar_key_big
			main_role_vo.avatar_key_small = now_role.avatar_key_small

			self.now_role_index = i
			self.real_server_id = main_role_vo.server_id

			is_set_suc = true

			break
		end
	end

	if not is_set_suc then
		ErrorLog("try to set now role to a unexist role!")
	end
end

function UserVo:GetOriginalRoleID()
	return self.origin_role_id
end

-- 根据角色id获取服id
function UserVo.GetServerId(role_id)
	return bit:_rshift(role_id, DB_INDEX_MARK_BIT)
end

-- 根据角色id判断是否跨服
function UserVo.IsCrossServer(role_id)
    return bit:d2b_two(role_id)[0] == 1
end
