-----------------------------------------
-- 心魔副本
-----------------------------------------
FuBenDemonsLogic = FuBenDemonsLogic or BaseClass(CommonFbLogic)

function FuBenDemonsLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.obj_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))

end

function FuBenDemonsLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil

	if self.obj_create_event then
		GlobalEventSystem:UnBind(self.obj_create_event)
		self.obj_create_event = true
	end

end

function FuBenDemonsLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	local obj_move_info_list = Scene.SendGetAllObjMoveInfoReq()
	FuBenPanelWGCtrl.Instance:OpenBountyTaskView()
	local layer_cfg = ConfigManager.Instance:GetAutoConfig("xinmo_fb_cfg_auto").layer
	local scene_id  = Scene.Instance:GetSceneId()
	local cfg_scene_id = layer_cfg[1].scene_id
	GuajiWGCtrl.Instance:StopGuaji()
	if scene_id == cfg_scene_id then
		local role_prof = RoleWGData.Instance:GetRoleProf()
		local cg_name
		if role_prof == 3 then
			cg_name = "CG_FB_PaTa_CG02"
		else
			cg_name = "CG_FB_PaTa_CG01"
		end

		CgManager.Instance:Play(BaseCg.New("cg/cg_fb_pata_prefab", cg_name),function ()
		  	FuBenWGCtrl.Instance:CSFBPlayedCG()
		end,nil,nil)
	else
	  	FuBenWGCtrl.Instance:CSFBPlayedCG()
	end

	local role_list = Scene.Instance:GetRoleList()
	for i, v in pairs(role_list) do
		if not v:IsMainRole() then
			v:SetAttr("is_demons" , EnumDemonsTpye.IsDemons)
			v:ReloadUIName()
			v:GetDrawObj():GetRoot().transform.localScale = Vector3(2, 2, 2)
			local follow_ui = v:GetFollowUi()
			follow_ui:SetNameTextOffY(25)
		end
	end

end

function FuBenDemonsLogic:Out()
	CommonFbLogic.Out(self)
	GuajiWGCtrl.Instance:StopGuaji()
	if GameVoManager.Instance:GetMainRoleVo().hp <= 0 then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
	end
	
	FuBenPanelWGCtrl.Instance:CloseBountyTaskView()
end

function FuBenDemonsLogic:IsEnemy(target_obj, main_role)
	return true
end

function FuBenDemonsLogic:OnObjCreate(obj)
	if not obj:IsMainRole() and obj:IsRole() then
		obj:SetAttr("is_demons" , EnumDemonsTpye.IsDemons)
		obj:ReloadUIName()
		obj:GetDrawObj():GetRoot().transform.localScale = Vector3(2, 2, 2)
		local follow_ui = obj:GetFollowUi()
		follow_ui:SetNameTextOffY(25)
	end
end

function FuBenDemonsLogic:GetSceneLogicMoveState()
    local info = FuBenPanelWGData.Instance:GetDemonsInfo()
    if info and info.fb_next_refresh_timestamp - TimeWGCtrl.Instance:GetServerTime() <= 0 then
        return true
    end
	return false
end
