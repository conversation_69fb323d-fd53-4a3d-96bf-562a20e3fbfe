-- /jy_gm dreamspiritgmoperate:1 0 0 0 0
-- enum DREAM_SPIRIT_GM_TYPE
-- {
-- 	    DREAM_SPIRIT_GM_TYPE_MIN = 0,
-- 		DREAM_SPIRIT_GM_TYPE_SET_REFINE_VALUE,	    // 设置洗练值	    param1:seq	param2:solt	 param3:index (-1 表示全部)   param3:value
-- 	    DREAM_SPIRIT_GM_TYPE_SET_REFINE_LEVEL,		// 设置洗练等级		param1:seq	param2:solt	 param3:level
-- };

MengLingWGData = MengLingWGData or BaseClass()

function MengLingWGData:__init()
	if MengLingWGData.Instance then 
		print_error("[MengLingWGData] Attemp to create a singleton twice !")
	end

	MengLingWGData.Instance = self

    --cfg
    local cfg = ConfigManager.Instance:GetAutoConfig("dream_spirit_cfg_auto")
    self.other_cfg = cfg.other[1]
    self.grade_cfg = cfg.grade
    self.stone_cfg = cfg.stone
    self.equip_cfg = ListToMap(cfg.equip, "item_id")
    self.equip_list_cfg = ListToMap(cfg.equip, "seq", "slot", "color")
    self.slot_level_cfg = ListToMap(cfg.slot_level, "seq", "slot", "level")
    self.suit_cfg = cfg.suit
    self.suit_active_cfg = ListToMap(cfg.suit_active, "seq", "suit", "need_num", "color")
    self.suit_preview_cfg = ListToMapList(cfg.suit_active, "seq", "suit", "color")
    self.compose_show_cfg = ListToMap(cfg.compose_show, "big_type", "small_type")
    self.equip_compos_cfg = ListToMapList(cfg.equip_compos, "big_type", "small_type")
    self.equip_item_compos_cfg = ListToMap(cfg.equip_compos, "item_id")
    self.stone_item_cfg = ListToMap(cfg.stone, "item_id")

    -- pro
    self.mengling_score = 0                    -- 梦灵积分
    self.stone_list = {}                       -- 梦灵属性丹等级
    self.mengling_suit_item_list = {}          -- 梦灵11个孔位数据
    self.mengling_bag_grid_list = {}           -- 梦灵背包数据
    self.mengling_bag_grid_count = 0           -- 梦灵背包容量

    -- cache
    self.mengling_slot_to_suit_id = {}
    self.mengling_suit_icon_cfg_cache = {}
    self.mengling_onekeywear_datalist = {}      -- 每一套得一件穿戴数据缓存
    self.mengling_onekeywear_cache = {}         -- 装备的穿戴缓存 
    self.mengling_strength_auto_up_state = false-- 自动升级标记
    self.mengling_suit_cache = {}                -- 梦灵套装数据缓存
    self.mengling_equip_compose_cache = {}      -- 梦灵装备合成缓存      seq   color = {}

    self:InitCfgCache()

    RemindManager.Instance:Register(RemindName.MengLingBag, BindTool.Bind(self.GetMengLingBagRemind, self))
    RemindManager.Instance:Register(RemindName.MengLingStrong, BindTool.Bind(self.GetMengLingStrongRemind, self))
    RemindManager.Instance:Register(RemindName.MengLingSuit, BindTool.Bind(self.GetMengLingSuitRemind, self))
    RemindManager.Instance:Register(RemindName.MengLingCompose, BindTool.Bind(self.GetMengLingComposeRemind, self))
    RemindManager.Instance:Register(RemindName.MengLingSXD, BindTool.Bind(self.GetMengLingSXDRemind, self))
end

function MengLingWGData:__delete()
	MengLingWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.MengLingView)

    self.mengling_slot_to_suit_id = nil
    self.mengling_suit_icon_cfg_cache = nil
    self.sxd_item_cache = nil
    self.mengling_onekeywear_datalist = nil
    self.mengling_onekeywear_cache = nil
    self.mengling_strength_auto_up_state = nil
    self.mengling_suit_cache = nil
end

---------------------------------------------------init_start----------------------------------------------
function MengLingWGData:InitCfgCache()
    local sxd_item_cache = {}

    for k, v in pairs(self:GetStoneCfg()) do
        sxd_item_cache[v.item_id] = v.item_id
    end

    self.sxd_item_cache = sxd_item_cache
end
---------------------------------------------------init_end------------------------------------------------

---------------------------------------------------remind_start----------------------------------------------
function MengLingWGData:GetMengLingBagRemind()
    -- if self:GetMengLingSXDRemind() > 0 then
    --     return 1
    -- end

    for k, v in pairs(self:GetMengLingShowGradeDataList()) do
        local wear_data = MengLingWGData.Instance:GetMengLingOneKeyWearDataList(v.seq)

        if not IsEmptyTable(wear_data) then
            return 1
        end
    end
    
    return 0 
end

function MengLingWGData:GetMengLingStrongRemind()
    if self:GetMengLingSXDRemind() > 0 then
        return 1
    end

    for k, v in pairs(self:GetMengLingShowGradeDataList()) do
        if self:IsCanStrongMengLingSuit(v.seq) then
            return 1
        end
    end
    
    return 0 
end

function MengLingWGData:GetMengLingSuitRemind()
    -- if self:GetMengLingSXDRemind() > 0 then
    --     return 1
    -- end
    
    return 0 
end

function MengLingWGData:GetMengLingComposeRemind()
    local data_list = self:GetComposeBigItemListCfg()
    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if self:GetMengLingComposeBigTypeRemind(k) then 
               return 1
            end
        end
    end

    return 0 
end

function MengLingWGData:GetMengLingSXDRemind()
    local stone_cfg = MengLingWGData.Instance:GetStoneCfg()
    
    for k, v in pairs(stone_cfg) do
        local use_num = self:GetMengLingStoneDataByHole(v.hole)

        if v.limit_count > use_num then
            local has_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)

            if has_num > 0 then
                return 1
            end
        end
    end

    return 0
end

--大类型红点
function MengLingWGData:GetMengLingComposeBigTypeRemind(big_type)
    local data_list = self:GetEquipComposeBigTypeDataList(big_type)
    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if self:GetMengLingComposeSmallTypeRemind(big_type, k) then
                return true
            end
        end
    end

    return false
end

-- 小类型红点
function MengLingWGData:GetMengLingComposeSmallTypeRemind(big_type, small_type)
    local compose_data = self:GetEquipComposeDataList(big_type, small_type)
    if not IsEmptyTable(compose_data) then
        for k, v in pairs(compose_data) do
            if self:GetMengLingEquipComposeRemind(v) then
                return true
            end
        end
    end

    return false
end

-- 装备列表装备红点
function MengLingWGData:GetMengLingEquipComposeRemind(equip_cfg)
    local check_stuff = function(compose_cell_data)
        local stuff_list = self:GetMengLingEquipComposeStuffList(compose_cell_data.need_grade, compose_cell_data.need_color)
        local stuff_num = #stuff_list
        -- 概率满
		if stuff_num < compose_cell_data.min_consume_num then
			return false
        else
            local probability = compose_cell_data.probability
            probability = probability + (stuff_num - compose_cell_data.min_consume_num) * compose_cell_data.add_probability

            if probability >= 100 then
               return true
            end
		end

        return false
    end

    local senior_item_id = equip_cfg.senior_item_id
    local senior_item_num = equip_cfg.senior_item_num
    local min_consume_num = equip_cfg.min_consume_num
    local show_senior = senior_item_num > 0 and senior_item_id > 0

    if show_senior then
        local had_num = ItemWGData.Instance:GetItemNumInBagById(senior_item_id)
        if had_num < senior_item_num then
            return false
        else
            return check_stuff(equip_cfg)
        end
    else
        return check_stuff(equip_cfg)
    end

    return false
end

---------------------------------------------------remind_end------------------------------------------------

---------------------------------------------------protocol_start----------------------------------------------
function MengLingWGData:SetDreamSpiritInfo(protocol)
    self.mengling_score = protocol.score          -- 梦灵积分 分解获得 用于强化
    self.stone_list = protocol.stone_list         -- 属性丹等级
end

function MengLingWGData:SetDreamSpiritItemInfo(protocol)
    self.mengling_suit_item_list = protocol.mengling_suit_item_list
    self:CalMengLingSuitOneKeyWearData()
    self:CalMengLingSuitEquipCache()
end

function MengLingWGData:UpdateDreamSpiritItemInfo(protocol)
    local seq = protocol.mengling_seq
    self.mengling_suit_item_list[seq] = protocol.mengling_suit_item
    self:CalMengLingSuitOneKeyWearData()
    self:UpdateMengLingSuitEquipCache(seq)
end

function MengLingWGData:SetMengLingBagInfo(protocol)
    self.mengling_bag_grid_count = protocol.grid_count
    self.mengling_bag_grid_list = protocol.grid_list
    self:CalculationMengLingBagCache(self.mengling_bag_grid_list)
end

function MengLingWGData:UpdateMengLingBagInfo(protocol)
    local new_data = protocol.grid_info
      local old_data = self.mengling_bag_grid_list[new_data.index]
      local no_old_data = IsEmptyTable(old_data)
      local is_add = false
      local add_num = 0

      if (no_old_data and new_data.item_id > 0 and new_data.num > 0) or (not no_old_data and old_data.item_id <= 0 and new_data.item_id > 0 and new_data.num > 0) then
        -- print_error("添加------------  " ,protocol.grid_info)
          self.mengling_bag_grid_list[new_data.index] = new_data
          is_add = true
          add_num = new_data.num
      elseif not no_old_data and old_data.item_id > 0 and old_data.num > 0 and new_data.item_id > 0 and new_data.num > 0  then
        -- print_error("修改------------  " ,protocol.grid_info)
        self.mengling_bag_grid_list[new_data.index] = new_data
        is_add = new_data.num > old_data.num
        add_num = new_data.num - old_data.num
    else
        -- print_error("删除------------  " ,protocol.grid_info)
        self.mengling_bag_grid_list[new_data.index] = nil
    end

    self:CalculationMengLingBagCache(self.mengling_bag_grid_list)
    return is_add, add_num
end

function MengLingWGData:GetMengLingScore()
    return self.mengling_score
end

function MengLingWGData:GetMengLingStoneDataByHole(hole)
    return self.stone_list[hole] or 0
end

function MengLingWGData:GetMengLingSuitItemDataBy(seq)
    return self.mengling_suit_item_list[seq]
end

function MengLingWGData:GetMengLingSuitSlotItemData(seq, slot)
    return (self.mengling_suit_item_list[seq] or {})[slot]
end

function MengLingWGData:GetTotalWearSuitItemList(seq)
    local data_list = {}

    local target_data_list = self:GetMengLingSuitItemDataBy(seq)

    if not IsEmptyTable(target_data_list) then
        for k, v in pairs(target_data_list) do
            if v.item_id > 0 then
                table.insert(data_list, v)
            end
        end
    end

    return data_list
end

-- 品质 阶数排序
function MengLingWGData:GetMengLingBagDataList()
    -- 下标从1开始
	-- table.sort(self.mengling_bag_grid_list, function (a, b)
    --     local a_cfg = self:GetMengLingEquipCfgByItemId(a.item_id)
    --     local b_cfg = self:GetMengLingEquipCfgByItemId(b.item_id)

    --     if a_cfg.color > b_cfg.color then
    --         return true
    --     elseif a_cfg.seq > b_cfg.seq then
    --         return true
    --     else
    --         return false
    --     end
    -- end)

    -- return self.mengling_bag_grid_list

    return SortDataByItemColor(self.mengling_bag_grid_list)
end

function MengLingWGData:GetMengLingBagDataListBySeq(seq)
    local data_list = {}

    if nil ~= seq then
        if not IsEmptyTable(self.mengling_bag_grid_list) then
            for k, v in pairs(self.mengling_bag_grid_list) do
                if v.item_id > 0 then
                   local equip_cfg = self:GetMengLingEquipCfgByItemId(v.item_id)

                   if not IsEmptyTable(equip_cfg) and equip_cfg.seq == seq then
                        table.insert(data_list, v)
                   end
                end
            end
        end
    end 

    return SortDataByItemColor(data_list)
end

function MengLingWGData:GetBagCurNum()
    return self.mengling_bag_grid_count
end

function MengLingWGData:GetBagItemByIndex(index)
    return self.mengling_bag_grid_list[index]
end

---------------------------------------------------protocol_end------------------------------------------------

---------------------------------------------------cfg_get_start----------------------------------------------
function MengLingWGData:GetMengLingShowGradeDataList()
    local data_list = {}
    for k, v in pairs(self:GetGradeCfgList()) do
        if self:IsGradeUnLock(v.seq) then
            table.insert(data_list, v)
        end
    end

    table.sort(data_list, SortTools.KeyLowerSorters("seq"))

    return data_list
end

function MengLingWGData:GetGradeCfgList()
    return self.grade_cfg
end

function MengLingWGData:GetGradeCfgBySeq(seq)
    return self.grade_cfg[seq]
end

function MengLingWGData:GetStoneCfg()
    return self.stone_cfg
end

function MengLingWGData:GetStoneCfgById(item_id)
    return self.stone_item_cfg[item_id]
end

function MengLingWGData:GetMengLingOtherCfg(attr_name)
    return self.other_cfg[attr_name]
end

function MengLingWGData:GetMengLingEquipCfgByItemId(item_id)
    return self.equip_cfg[item_id] or {}
end

function MengLingWGData:GetMengLingSlotLevelCfg(seq, slot, level)
    return ((self.slot_level_cfg[seq] or {})[slot] or {})[level]
end

function MengLingWGData:GetSlotSuitId(slot)
    if IsEmptyTable(self.mengling_slot_to_suit_id) then
        self:InitMengLingSlotToSuitData()
    end

    return self.mengling_slot_to_suit_id[slot]
end

function MengLingWGData:GetMengLingSuitCfgBySuit(suit)
    return self.suit_cfg[suit]
end

function MengLingWGData:GetMengLingSuitCfg()
    return self.suit_cfg
end

function MengLingWGData:GetSuitActiveColorDataList(seq, suit, need_num)
    return ((self.suit_active_cfg[seq] or {})[suit] or {})[need_num] or {}
end

function MengLingWGData:GetSuitActiveNumDataList(seq, suit)
    return (self.suit_active_cfg[seq] or {})[suit] or {}
end

function MengLingWGData:GetSuitActiveCfgListBySeq(seq)
    return self.suit_active_cfg[seq] or {}
end

function MengLingWGData:GetSuitActiveCfg()
    return self.suit_active_cfg
end

function MengLingWGData:GetSuitPreviewCfg()
    return self.suit_preview_cfg
end

function MengLingWGData:GetSuitPreviewSeqList(seq)
    return self.suit_preview_cfg[seq] or {}
end

function MengLingWGData:GetSuitPreviewSuitList(seq, suit)
    return (self.suit_preview_cfg[seq] or {})[suit] or {}
end

function MengLingWGData:GetComposeBigItemListCfg()
	return self.compose_show_cfg
end

function MengLingWGData:GetComposeBigItemTypeListCfg(big_type)
	return self.compose_show_cfg[big_type]
end

function MengLingWGData:GetEquipComposeBigTypeDataList(big_type)
    return self.equip_compos_cfg[big_type] or {}
end

function MengLingWGData:GetEquipComposeDataList(big_type, small_type)
    return (self.equip_compos_cfg[big_type] or {})[small_type] or {}
end
---------------------------------------------------cfg_get_end------------------------------------------------

---------------------------------------------------com_get_start----------------------------------------------
-- 获得套装的一键穿戴数据   用于直接发送协议请求
function MengLingWGData:GetMengLingOneKeyWearDataList(seq)
    local data_list = {}
    local one_key_wear_datalist = self.mengling_onekeywear_datalist[seq] or {}

    if not IsEmptyTable(one_key_wear_datalist) then
        for k, v in pairs(one_key_wear_datalist) do
            table.insert(data_list, {item_id = v.item_id, bag_index = v.bag_index, slot = v.slot})
        end
    end

    return data_list
end

function MengLingWGData:GetMengLingStrengthAutoUpState()
	return self.mengling_strength_auto_up_state
end

function MengLingWGData:SetMengLingStrengthAutoUpState(state)
	self.mengling_strength_auto_up_state = state
end

function MengLingWGData:GetMengLingSuitCacheNum(seq, suit, color)
    return ((((self.mengling_suit_cache or {})[seq] or {})[suit] or {})[color] or {}).num or 0
end

function MengLingWGData:GetMengLingSuitIconCfg(seq, suit)
    if IsEmptyTable(self.mengling_suit_icon_cfg_cache) then
        self:InitMengLingSuitIconCache()
    end

    return ((self.mengling_suit_icon_cfg_cache or {})[seq] or {})[suit] or {}
end

function MengLingWGData:GetMengLingEquipComposeStuffList(seq, color)
    return ((self.mengling_equip_compose_cache or {})[seq] or {})[color] or {}
end

function MengLingWGData:GetMengLingResolveOrderList()
    local data_list = {}
    table.insert(data_list, {name = Language.MengLing.MengLingMetingGradeNameList[1]}) 

    local order_show_list = MengLingWGData.Instance:GetGradeCfgList()
    for i = 0, #order_show_list do
        if order_show_list[i].seq > 0 then
            local index = order_show_list[i].seq
            table.insert(data_list, {name = Language.MengLing.MengLingMetingGradeNameList[index + 1]}) 
        end
    end

    return data_list
end

-- 梦灵宗属性及战力
function MengLingWGData:GetMengLingTotalAttrInfo(need_cap)
    local attribute
    local capability = 0
    local index_list = {}
    local id_index = 1
    local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.LINGMENG)
    charm_rate = charm_rate / 10000

    if need_cap then
        attribute = AttributePool.AllocAttribute()
    end

    local attr_list = {}    -- { attr_str, attr_value }

    local add_sttr = function(data, beilu)
        beilu = beilu or 1

        for i = 1, 7 do
            local attr_id = data["attr_id" .. i]
            local attr_value = data["attr_value" .. i]
            
            if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
                local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
                local value = attr_value * beilu + attr_value * beilu * charm_rate

                if need_cap then
                    if attribute[attr_str] then
                        attribute[attr_str] = attribute[attr_str] + value
                    end
                end

                -- 存在标记  ["pojia"] = 1
                local index = index_list[attr_str] or -1

                if index > 0 then
                    attr_list[index].attr_value = attr_list[index].attr_value + value
                else
                    index_list[attr_str] = id_index
                    attr_list[id_index] = {attr_str = attr_str, attr_value = value, attr_sort = attr_sort}
                    id_index = id_index + 1
                end
            end
        end
    end

    local grade_cfg = self:GetMengLingShowGradeDataList()

    -- 套装
    for k, v in pairs(grade_cfg) do
        -- 11个孔位
        local wear_data_info = self:GetMengLingSuitItemDataBy(v.seq)

        for i, u in pairs(wear_data_info) do
            if u.item_id > 0 then
                -- 镶嵌的装备
                local equip_data = self:GetMengLingEquipCfgByItemId(u.item_id)
                if not IsEmptyTable(equip_data) then
                    add_sttr(equip_data)
                end

                -- 升级
                local level_cfg = self:GetMengLingSlotLevelCfg(u.seq, u.slot, u.level)

                if not IsEmptyTable(level_cfg) then
                    add_sttr(level_cfg)
                end
            end
        end
        
        -- 11个技能
        local skill_unlock = self:IsMengLingSuitSkillActive(v.seq)
        if skill_unlock then
            add_sttr(v)
        end

        -- 套装属性
        local seq_list = self:GetSuitActiveCfgListBySeq(v.seq)
        for suit, suit_list in pairs(seq_list) do
            for num, num_list in pairs(suit_list) do
                for color, color_cfg in pairs(num_list) do
                    -- 2, 4, 6
                    local cache_num = self:GetMengLingSuitCacheNum(v.seq, suit, color)
                    if cache_num >= num then
                        add_sttr(color_cfg)
                    end
                end
            end
        end
    end

    -- 属性石头
    local stone_data_list = self:GetStoneCfg()
    for k, v in pairs(stone_data_list) do
        local hole_level = self:GetMengLingStoneDataByHole(v.hole)

        if hole_level > 0 then
            add_sttr(v, hole_level)
        end
    end

    if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    if need_cap then
        capability = AttributeMgr.GetCapability(attribute)
    end

    return capability, attr_list
end

-- 梦灵单grade + 属性丹得战力
function MengLingWGData:GetMengLingGradeTotalAttrInfo(grade_seq, need_cap)
    local attribute
    local capability = 0
    local index_list = {}
    local id_index = 1
    local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.LINGMENG)
    charm_rate = charm_rate / 10000

    if need_cap then
        attribute = AttributePool.AllocAttribute()
    end

    local attr_list = {}    -- { attr_str, attr_value }

    local add_sttr = function(data, beilu)
        beilu = beilu or 1

        for i = 1, 7 do
            local attr_id = data["attr_id" .. i]
            local attr_value = data["attr_value" .. i]
            
            if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
                local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
                local value = attr_value * beilu + attr_value * beilu * charm_rate

                if need_cap then
                    if attribute[attr_str] then
                        attribute[attr_str] = attribute[attr_str] + value
                    end
                end

                -- 存在标记  ["pojia"] = 1
                local index = index_list[attr_str] or -1

                if index > 0 then
                    attr_list[index].attr_value = attr_list[index].attr_value + value
                else
                    index_list[attr_str] = id_index
                    attr_list[id_index] = {attr_str = attr_str, attr_value = value, attr_sort = attr_sort}
                    id_index = id_index + 1
                end
            end
        end
    end

    local grade_cfg = self:GetGradeCfgBySeq(grade_seq)
    -- 11个孔位
    local wear_data_info = self:GetMengLingSuitItemDataBy(grade_seq)
    for i, u in pairs(wear_data_info) do
        if u.item_id > 0 then
            -- 镶嵌的装备
            local equip_data = self:GetMengLingEquipCfgByItemId(u.item_id)
            if not IsEmptyTable(equip_data) then
                add_sttr(equip_data)
            end

            -- 升级
            local level_cfg = self:GetMengLingSlotLevelCfg(u.seq, u.slot, u.level)

            if not IsEmptyTable(level_cfg) then
                add_sttr(level_cfg)
            end
        end
    end
    
    -- 11个技能
    local skill_unlock = self:IsMengLingSuitSkillActive(grade_seq)
    if skill_unlock then
        add_sttr(grade_cfg)
    end
    
    -- 套装属性
    local seq_list = self:GetSuitActiveCfgListBySeq(grade_seq)
    for suit, suit_list in pairs(seq_list) do
        for num, num_list in pairs(suit_list) do
            for color, color_cfg in pairs(num_list) do
                -- 2, 4, 6
                local cache_num = self:GetMengLingSuitCacheNum(grade_seq, suit, color)
                if cache_num >= num then
                    add_sttr(color_cfg)
                end
            end
        end
    end

    -- 属性石头
    local stone_data_list = self:GetStoneCfg()
    for k, v in pairs(stone_data_list) do
        local hole_level = self:GetMengLingStoneDataByHole(v.hole)

        if hole_level > 0 then
            add_sttr(v, hole_level)
        end
    end

    if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    if need_cap then
        capability = AttributeMgr.GetCapability(attribute)
    end

    return capability, attr_list
end
---------------------------------------------------com_get_end------------------------------------------------

---------------------------------------------------cal_start----------------------------------------------
function MengLingWGData:IsGradeUnLock(seq)
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local cfg = self:GetGradeCfgBySeq(seq)
    if IsEmptyTable(cfg) then
        return false
    end

    return role_level >= cfg.show_level and open_day >= cfg.show_day
end

function MengLingWGData:IsMengLingSXDItem(item_id)
    return nil ~= self.sxd_item_cache[item_id]
end

function MengLingWGData:IsMengLingEquip(item_id)
    return nil ~= self.equip_cfg[item_id]
end

function MengLingWGData:CalculationMengLingBagCache(bag_grid_list)
    local mengling_onekeywear_cache = {}    -- 一键镶嵌缓存
    local mengling_equip_compose_cache = {}    -- 合成装备缓存

    if not IsEmptyTable(bag_grid_list) then
        for k, v in pairs(bag_grid_list) do
            if self:IsMengLingEquip(v.item_id) then
                local equip_cfg = self:GetMengLingEquipCfgByItemId(v.item_id)
                local _, equip_color = ItemWGData.Instance:GetItemColor(v.item_id)
                local data = {cfg = equip_cfg, color = equip_color, index = v.index, item_id = v.item_id, slot = equip_cfg.slot}
                mengling_onekeywear_cache[equip_cfg.seq] = mengling_onekeywear_cache[equip_cfg.seq] or {}
                mengling_onekeywear_cache[equip_cfg.seq][equip_cfg.slot] = mengling_onekeywear_cache[equip_cfg.seq][equip_cfg.slot] or {}
                table.insert(mengling_onekeywear_cache[equip_cfg.seq][equip_cfg.slot], data)

                mengling_equip_compose_cache[equip_cfg.seq] = mengling_equip_compose_cache[equip_cfg.seq] or {}
                mengling_equip_compose_cache[equip_cfg.seq][equip_color] =  mengling_equip_compose_cache[equip_cfg.seq][equip_color] or {}

                for i = 1, v.num do
                    table.insert(mengling_equip_compose_cache[equip_cfg.seq][equip_color], data)
                end
            end
        end

        if not IsEmptyTable(mengling_onekeywear_cache) then
            for k, v in pairs(mengling_onekeywear_cache) do
                for i, u in pairs(v) do
                    table.sort(u, SortTools.KeyUpperSorters("color"))
                end
            end
        end
    end

    self.mengling_onekeywear_cache = mengling_onekeywear_cache
    self.mengling_equip_compose_cache = mengling_equip_compose_cache

    self:CalMengLingSuitOneKeyWearData()
    self.mengling_bag_grid_count = #bag_grid_list + 1
end

function MengLingWGData:GetMengLingSuitOneKeyWearDataCache(seq, slot)
    return (self.mengling_onekeywear_cache[seq] or {})[slot] or {}
end

-- 计算一件穿戴数据
function MengLingWGData:CalMengLingSuitOneKeyWearData()
	local data_list = {}

	for k, v in pairs(self:GetGradeCfgList()) do
        for i = 0, 11 do
            local data_cache = self:GetMengLingSuitOneKeyWearDataCache(v.seq, i)
            local best_quality_data = ((data_cache or {})[1] or {})

            if not IsEmptyTable(best_quality_data) then
                if self:IsCanMengLingEquipWear(best_quality_data.item_id) then
                    data_list[v.seq] = data_list[v.seq] or {}
                    data_list[v.seq][i] = {item_id = best_quality_data.item_id, bag_index = best_quality_data.index, slot = best_quality_data.slot}
                end
            end
        end
    end

	self.mengling_onekeywear_datalist = data_list
end

-- 最好的装备能否穿戴上去
function MengLingWGData:IsCanMengLingEquipWear(item_id)
    local cfg = self:GetMengLingEquipCfgByItemId(item_id)

	if not IsEmptyTable(cfg) then
        local cur_info = self:GetMengLingSuitSlotItemData(cfg.seq, cfg.slot)

        if not IsEmptyTable(cur_info) then
            if cur_info.item_id <= 0 then
                return true
            end

            local current_data_cfg = self:GetMengLingEquipCfgByItemId(cur_info.item_id)

            if not IsEmptyTable(current_data_cfg) then
                local _, item_color = ItemWGData.Instance:GetItemColor(item_id)
                local _, slot_color = ItemWGData.Instance:GetItemColor(cur_info.item_id)
                
                if (item_color > slot_color) then
                    return true
                end
            end
        end
    end

    return false
end

-- 套装是否存在能升级情况
function MengLingWGData:IsCanStrongMengLingSuit(seq)
    if not self:IsGradeUnLock(seq) then
        return false
    end

    local equip_data_list = self:GetMengLingSuitItemDataBy(seq)
    local score = self:GetMengLingScore()
    
    for k, v in pairs(equip_data_list) do
        if v.item_id > 0 then
            local level_cfg = self:GetMengLingSlotLevelCfg(v.seq, v.slot, v.level)
            local next_level_cfg = self:GetMengLingSlotLevelCfg(v.seq, v.slot, v.level + 1)
            
            if not IsEmptyTable(next_level_cfg) then
                if score >= level_cfg.need_score then
                    return true
                end
            end
        end
    end

    return false
end

-- 套装装备槽位是否存在能升级情况
function MengLingWGData:IsCanStrongMengLingSuitEquip(seq, slot)
    if not self:IsGradeUnLock(seq) then
        return false
    end

    local item_info = self:GetMengLingSuitSlotItemData(seq, slot) 

    if not IsEmptyTable(item_info) then
        if item_info.item_id > 0 then
            local level_cfg = self:GetMengLingSlotLevelCfg(seq, slot, item_info.level)
            local next_level_cfg = self:GetMengLingSlotLevelCfg(seq, slot, item_info.level + 1)
            
            if IsEmptyTable(next_level_cfg) then
                return false
            end
            
            local score = MengLingWGData.Instance:GetMengLingScore()
            if score >= level_cfg.need_score then
                return true
            end
        end
    end

    return false
end

-- 梦灵套装数据缓存    seq suit color = num
function MengLingWGData:CalMengLingSuitEquipCache()
    for k, v in pairs(self:GetGradeCfgList()) do
        self:UpdateMengLingSuitEquipCache(v.seq)
    end
end

function MengLingWGData:UpdateMengLingSuitEquipCache(seq)
    local data_list = {}

    local info_list = self:GetMengLingSuitItemDataBy(seq)
    if not IsEmptyTable(info_list) then
        for k, v in pairs(info_list) do
            local suit_id = self:GetSlotSuitId(v.slot)
            data_list[suit_id] = data_list[suit_id] or {}

            if v.item_id > 0 then
                local _, color = ItemWGData.Instance:GetItemColor(v.item_id)

                for i = GameEnum.ITEM_COLOR_WHITE, color do
                    data_list[suit_id][i] = data_list[suit_id][i] or {num = 0}
                    data_list[suit_id][i].num = data_list[suit_id][i].num + 1
                end
            end
        end
    end

    self.mengling_suit_cache[seq] = data_list
end

function MengLingWGData:GetMengLingSuitAttrCfg(seq, suit)
    local suit_data_list = {}

    local get_suit_attr_data = function (seq, suit, num)
        local cfg_list = self:GetSuitActiveColorDataList(seq, suit, num)
        local active_data = {}
        local default_data = {}
        local min_data = {}

        for k, v in pairs(cfg_list) do
            local color_num = self:GetMengLingSuitCacheNum(seq, suit, v.color)

            if IsEmptyTable(default_data) or default_data.color > v.color then
                default_data = v
            end

            if IsEmptyTable(min_data) or (min_data.color > v.color) then
                min_data = v
            end
    
            if color_num >= v.need_num then
                if IsEmptyTable(active_data) then
                    active_data = v
                elseif active_data.color < v.color then
                    active_data = v
                end
            end
        end
        
        return active_data, default_data, min_data
    end

    local number_data_list = self:GetSuitActiveNumDataList(seq, suit)
    
    -- k = 套装数量
    for k, v in pairs(number_data_list) do
        local suit_active_data, suit_default_data, suit_min_data = get_suit_attr_data(seq, suit, k)
        table.insert(suit_data_list, {suit_active_data = suit_active_data, suit_default_data = suit_default_data, suit_min_data = suit_min_data})
    end

    return suit_data_list
end

function MengLingWGData:InitMengLingSlotToSuitData()
    local mengling_slot_to_suit_id = {}

    for k, v in pairs(self:GetMengLingSuitCfg()) do
        local slot_data_list = Split(v.slot, ",")

        if not IsEmptyTable(slot_data_list) then
            for i, u in pairs(slot_data_list) do
                mengling_slot_to_suit_id[tonumber(u)] = tonumber(v.suit)
            end
        end
    end

    self.mengling_slot_to_suit_id = mengling_slot_to_suit_id
end

function MengLingWGData:InitMengLingSuitIconCache()
    -- 梦灵套装导航栏显示的套装图标数据
    local mengling_suit_icon_cfg_cache = {}
    for k, v in pairs(self:GetSuitActiveCfg()) do
        -- k = seq,  v = suit_list
        mengling_suit_icon_cfg_cache[k] = mengling_suit_icon_cfg_cache[k] or {}
        for i, j in pairs(v) do
            -- i = suit , j = number_list
            local suit_data = {}

            for n, m in pairs(j) do
                -- n number   m  = color_list
                for color, color_data in pairs(m) do
                    if IsEmptyTable(suit_data) or (suit_data.need_num < color_data.need_num)
                     or ((suit_data.need_num == color_data.need_num) and (suit_data.color < color_data.color)) then 
                        suit_data = color_data
                    end
                end
            end
            
            mengling_suit_icon_cfg_cache[k][i] = suit_data
        end
    end

    self.mengling_suit_icon_cfg_cache = mengling_suit_icon_cfg_cache
end

function MengLingWGData:IsMengLingSuitSkillActive(seq)
    local suit_cfg = self:GetGradeCfgBySeq(seq)
    local skill_active_color = suit_cfg.skill_active_color
    local data_info = self:GetMengLingSuitItemDataBy(seq)

    if IsEmptyTable(data_info) then
        return false
    end

    for k, v in pairs(data_info) do
        if v.item_id <= 0 then
            return false
        else
            local _, color = ItemWGData.Instance:GetItemColor(v.item_id)
            if color < skill_active_color then
                return false
            end
        end
    end

    return true
end

function MengLingWGData:IsMengLingSuitSkillUnLock(info_data, grade_cfg)
    if IsEmptyTable(info_data) or IsEmptyTable(grade_cfg) then
        return false
    end

    local skill_active_color = grade_cfg.skill_active_color

    for k, v in pairs(info_data) do
        if v.item_id <= 0 then
            return false
        else
            local _, color = ItemWGData.Instance:GetItemColor(v.item_id)
            if color < skill_active_color then
                return false
            end
        end
    end

    return true
end

function  MengLingWGData:IsMengLingComposeItem(change_item_id)
    return nil ~= self.equip_item_compos_cfg[change_item_id]
end

function MengLingWGData:IsMengLingSuitUnLock(seq, suit, color, num)
    local cache_num = self:GetMengLingSuitCacheNum(seq, suit, color)
    return num <= cache_num
end

function MengLingWGData:GetDefaultShowItemId(seq, slot)
    local equip_list = (self.equip_list_cfg[seq] or {})[slot] or {}
    local default_item_id = 0
    local color = GameEnum.ITEM_COLOR_COLOR_FUL

    if not IsEmptyTable(equip_list) then
        for k, v in pairs(equip_list) do
            local _, item_color = ItemWGData.Instance:GetItemColor(v.item_id)

            if default_item_id <= 0 or item_color <= color then
                default_item_id = v.item_id
                color = item_color
            end
        end
    end

    return default_item_id
end

function MengLingWGData:GetMengLingItemUpFlag(seq, slot, item_id)
    if nil == seq or nil == slot or nil == item_id or item_id <= 0 then
        return false
    end

    local cur_slot_data = self:GetMengLingSuitSlotItemData(seq, slot)

    if IsEmptyTable(cur_slot_data) then
        return false
    end

    if cur_slot_data.item_id <= 0 then
        return true
    end

    local _, cur_item_color = ItemWGData.Instance:GetItemColor(cur_slot_data.item_id)
    local _,target_item_color = ItemWGData.Instance:GetItemColor(item_id)

    if target_item_color > cur_item_color then
        return true
    end

    return false
end

-- 五行 五件套  乾坤 六件套的
function MengLingWGData:GetMengLingSuitInfo(seq)
    local suit_data = {}

    for k, v in pairs(self:GetMengLingSuitCfg()) do
        local suit_id = v.suit
        local show_suit_cfg = {}

        local data_list = self:GetSuitActiveNumDataList(seq, suit_id)
       
        if not IsEmptyTable(data_list) then
            -- need_num  
            for need_num, need_num_list in pairs(data_list) do
                if (suit_id == 1 and need_num >= 6) or (suit_id == 2 and need_num >= 5) then
                    for color, color_data in pairs(need_num_list) do
                        local active_num = self:GetMengLingSuitCacheNum(color_data.seq, color_data.suit, color_data.color)
    
                        if active_num >= color_data.need_num then
                            -- 数量更多/ 品质更好
                           if IsEmptyTable(show_suit_cfg) or ((show_suit_cfg.need_num < color_data.need_num) or 
                                ((show_suit_cfg.need_num == color_data.need_num) and (show_suit_cfg.color < color_data.color))) then
                                show_suit_cfg = color_data
                           end
                        end
                    end
                end
            end
        end
        
        suit_data[suit_id] = show_suit_cfg
    end

    return suit_data
end
---------------------------------------------------cal_end------------------------------------------------

function MengLingWGData:CheckCanResolve(item_id)
    local item_cfg = self:GetMengLingEquipCfgByItemId(item_id)
    if IsEmptyTable(item_cfg) then
        return true
    end

    local slot_data = self:GetMengLingSuitSlotItemData(item_cfg.seq, item_cfg.slot)
    if not slot_data then
        return true
    end

    if slot_data.item_id > 0 then
        local equip_item_cfg = self:GetMengLingEquipCfgByItemId(slot_data.item_id)
        local equip_item_color = equip_item_cfg.color or 1
        return item_cfg.color <= equip_item_color
    end

    return false
end

function MengLingWGData:GetSuitAttrList(targer_seq, targer_suit, targer_num, targer_color)
    local fake_attr_cfg = {}
    local suit_list = self:GetSuitActiveNumDataList(targer_seq, targer_suit)
    if IsEmptyTable(suit_list) then
        return fake_attr_cfg
    end

    local add_attr_func = function (save_table, cfg)
        for i = 1, 7 do
            local attr_id = cfg["attr_id" .. i] and cfg["attr_id" .. i] or 0
            if not save_table["attr_id" .. i] and attr_id > 0 then
                save_table["attr_id" .. i] = attr_id
            end

            if not save_table["attr_value" .. i] then
                save_table["attr_value" .. i] = 0
            end

            local attr_value = cfg["attr_value" .. i] and cfg["attr_value" .. i] or 0
            if attr_id > 0 and attr_value > 0 then
                save_table["attr_value" .. i] = save_table["attr_value" .. i] + attr_value
            end
        end

    end

    for num, num_list in pairs(suit_list) do
        if targer_num == num then
            for color, color_cfg in pairs(num_list) do
                if targer_color >= color then
                    local cache_num = self:GetMengLingSuitCacheNum(targer_seq, targer_suit, color)
                    if cache_num >= num then
                        add_attr_func(fake_attr_cfg, color_cfg)
                    end
                end
            end
        end
    end


    return EquipWGData.GetSortAttrListByTypeCfg(fake_attr_cfg, "attr_id", "attr_value", 1, 7)
end