﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_RenderPipelineWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.RenderPipeline), typeof(System.Object));
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("disposed", get_disposed, null);
		<PERSON><PERSON>("defaultSettings", get_defaultSettings, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_disposed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipeline obj = (UnityEngine.Rendering.RenderPipeline)o;
			bool ret = obj.disposed;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disposed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSettings(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipeline obj = (UnityEngine.Rendering.RenderPipeline)o;
			UnityEngine.Rendering.RenderPipelineGlobalSettings ret = obj.defaultSettings;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultSettings on a nil value");
		}
	}
}

