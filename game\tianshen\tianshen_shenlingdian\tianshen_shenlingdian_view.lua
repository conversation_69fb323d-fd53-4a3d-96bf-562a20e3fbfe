local SHENDIAN_BTN_TYPE = {
	GO_HAL_ATTR = 1,
	UPGRADE_ATTR = 2,
}

local SHENDIAN_IS_NOT_HAVE_SLOT = -1

function TianShenView:ReleaseTianshenTempleCallback()
	if self.tianshen_shendian_list_view then
		self.tianshen_shendian_list_view:DeleteMe()
		self.tianshen_shendian_list_view = nil
	end

    if self.ts_shendian_upgrade_cell then
		self.ts_shendian_upgrade_cell:DeleteMe()
		self.ts_shendian_upgrade_cell = nil
	end

    if self.shendian_upgrade_attr_list and #self.shendian_upgrade_attr_list > 0 then
		for _, cell in ipairs(self.shendian_upgrade_attr_list) do
			cell:DeleteMe()
			cell = nil
		end

		self.shendian_upgrade_attr_list = nil
	end

    if self.shendian_attr_list and #self.shendian_attr_list > 0 then
		for _, cell in ipairs(self.shendian_attr_list) do
			cell:DeleteMe()
			cell = nil
		end

		self.shendian_attr_list = nil
	end

    if self.shendian_slot_list and #self.shendian_slot_list > 0 then
		for _, cell in ipairs(self.shendian_slot_list) do
			cell:DeleteMe()
			cell = nil
		end

		self.shendian_slot_list = nil
	end

    self.now_capability = nil
end

function TianShenView:LoadTianshenTempleCallback()
    if not self.tianshen_shendian_list_view then
        self.tianshen_shendian_list_view = AsyncListView.New(TianshenShendianRender, self.node_list.tianshen_shendian_list_view)
        self.tianshen_shendian_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectTianShenShenDianCB, self))
        self.tianshen_shendian_list_view:SetStartZeroIndex(true)
    end

    if not self.ts_shendian_upgrade_cell then
        self.ts_shendian_upgrade_cell = ItemCell.New(self.node_list.ts_shendian_upgrade_cell)
    end

    -- 升阶属性
    if self.shendian_upgrade_attr_list == nil then
        self.shendian_upgrade_attr_list = {}
        for i = 1, 8 do
            local attr_obj = self.node_list.ts_shendian_upgrade_attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.shendian_upgrade_attr_list[i] = cell
            end
        end
    end

    -- 基础属性
    if self.shendian_attr_list == nil then
        self.shendian_attr_list = {}
        
        for i = 1, 8 do
            local attr_obj = self.node_list.ts_shendian_attr_list_root:FindObj(string.format("ts_shendian_attr_%d", i))
            if attr_obj then
                local cell = TSShendianAttrRender.New(attr_obj)
                self.shendian_attr_list[i] = cell
            end
        end
    end

    -- 孔位信息
    if self.shendian_slot_list == nil then
        self.shendian_slot_list = {}
        for i = 1, 8 do
            local attr_obj = self.node_list.ts_shendian_slot_root:FindObj(string.format("ts_shendian_slot_%d", i))
            if attr_obj then
                local cell = TSShendianSlotRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetClickCallBack(BindTool.Bind(self.OnClickShenDianSlot, self))
                self.shendian_slot_list[i] = cell
            end
        end
    end
    
    XUI.AddClickEventListener(self.node_list.ts_shendian_attr_btn, BindTool.Bind(self.OnClickShenDianAttrBtn, self))
	XUI.AddClickEventListener(self.node_list.ts_shendian_upgrade_btn, BindTool.Bind(self.OnClickShenDianUpgradeBtn, self))
    XUI.AddClickEventListener(self.node_list.ts_shendian_active_btn, BindTool.Bind(self.OnClickShenDianActiveBtn, self))
    XUI.AddClickEventListener(self.node_list.ts_shendian_upgrade_up_btn, BindTool.Bind(self.OnClickShenDianUpgradeUpBtn, self))
    XUI.AddClickEventListener(self.node_list.ts_shendian_star_attr_btn, BindTool.Bind(self.OnClickShenDianStarAttrBtn, self))
    XUI.AddClickEventListener(self.node_list.ts_shendian_skill_btn, BindTool.Bind(self.OnClickShenDianSkillBtn, self))
    XUI.AddClickEventListener(self.node_list.ts_shendian_quick_go_btn, BindTool.Bind(self.OnClickShenDianQuickGoBtn, self))
    XUI.AddClickEventListener(self.node_list.ts_shendian_quick_down_btn, BindTool.Bind(self.OnClickShenDianQuickDownBtn, self))
end

function TianShenView:OnSelectTianShenShenDianCB(shendian_item, cell_index, is_default, is_click)
    if nil == shendian_item or nil == shendian_item.data then
		return
	end

	if self.succinct_jump_index == cell_index then
		return
	end

    self.shendian_select_data = shendian_item.data
	self.shendian_jump_index = cell_index
    self.shendian_btn_type = nil
    self.now_capability = 0
    self:FlushShenDianGoHallAttrMessage()
    self:FlushShenDianUpgradeAttrMessage()
end

-- 上阵位置点击
function TianShenView:OnClickShenDianSlot(shendian_slot_item)
    if nil == shendian_slot_item or nil == shendian_slot_item.data then
		return
	end

    local is_lock = shendian_slot_item:GetSlotLockStatus()
    if is_lock then
        return
    end

    local shendian_seq = shendian_slot_item:GetShenDaianSeq()
    TianShenWGCtrl.Instance:OpenTSShenLingDianSelectView(shendian_seq, shendian_slot_item.data, shendian_slot_item.index - 1)
end

function TianShenView:ShowTianshenTempleCallback()
end

function TianShenView:TianshenTempleItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if TSShenLingDianData.Instance:CheckIsUpItemId(change_item_id) then
        self:Flush()
	end
end

function TianShenView:FlushTianshenTempleCallback(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushShenDianGoHallMessage()
        -- elseif k == "exp" then
        --     self:FlushBeastsExp()
        end
    end
end

function TianShenView:FlushShenDianGoHallMessage(jump_seq)
    local hall_list = TSShenLingDianData.Instance:GetHallListCfg()
    self.tianshen_shendian_list_view:SetDataList(hall_list)

    -- 当前选中不为空刷新
    if self.shendian_jump_index then
        self.shendian_select_data = hall_list[self.shendian_jump_index]
        self.now_capability = 0
        self:FlushShenDianGoHallAttrMessage()
        self:FlushShenDianUpgradeAttrMessage()
    else
        local need_jump_seq = 0
        self.tianshen_shendian_list_view:JumpToIndex(need_jump_seq, 8)
    end
end

function TianShenView:FlushShenDianGoHallAttrMessage()
    if not self.shendian_select_data then
        return
    end

    local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
    local now_order = hall_info and hall_info.order or 0
    local now_name = self.shendian_select_data.name or ""
    local is_unlock = now_order > 0
    local desc_str = is_unlock and string.format("%d%s", now_order, Language.Common.Jie) 
                        or ToColorStr(string.format("(%s)",Language.TianShen.ClickSkillTip2), COLOR3B.RED)
    local str = string.format("%s  %s", now_name, desc_str)

    self.node_list.ts_shendian_name_txt.text.text = str
    local list = TSShenLingDianData.Instance:GetGoHallAttrListBySeqOrder(self.shendian_select_data.seq)
    local act_cfg = TSShenLingDianData.Instance:GetHallCfgBySeq(self.shendian_select_data.seq)
    local can_go_list = TSShenLingDianData.Instance:GetHallIndexListBySeqOrder(self.shendian_select_data.seq, now_order)
    local need_go_hall_num = act_cfg and act_cfg.need_go_hall_num or 0
    local go_num = 0
    self:FlushShendianBtnType()

    if hall_info and hall_info.index_list then
        for i, cell in ipairs(self.shendian_slot_list) do
            local data = hall_info.index_list[i]
            cell:SetVisible(data ~= nil and can_go_list[i - 1])
            -- 神殿未激活或者未解锁孔位
            local is_lock = (not is_unlock) or can_go_list[i - 1] == nil
            cell:SetSlotLockStatus(is_lock)
            cell:SetShenDaianSeq(self.shendian_select_data.seq)

            if data ~= nil then
                if data ~= SHENDIAN_IS_NOT_HAVE_SLOT then
                    go_num = go_num + 1
                end

                cell:SetData(data)
            end
        end
    end

    local use_seq = TSShenLingDianData.Instance:GetCurrTSHallUseSeq()
    self.node_list.ts_shendian_skill_lock:CustomSetActive(not (go_num >= need_go_hall_num))
    self.node_list.ts_shendian_skill_use_remind:CustomSetActive(TSShenLingDianData.Instance:CheckTianShenTempleUpCanUseRemindBySeq(self.shendian_select_data.seq))
    self.node_list.ts_shendian_quick_go_remind:CustomSetActive(is_unlock and TSShenLingDianData.Instance:CheckTianShenTempleGoRemindBySeq(self.shendian_select_data.seq, true))

    local skill_id = act_cfg and act_cfg.bs_skill or 0
    local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, 1)
    local skill_cfg = SkillWGData.Instance:GetTianShenHeJiSkillById(skill_id, 1)
    local skill_dec = Language.TianShen.TSShenDianSkillTips

    if go_num >= need_go_hall_num then
        if use_seq ~= self.shendian_select_data.seq then
            skill_dec = ToColorStr(Language.TianShen.TSShenDianSkillTips2, COLOR3B.ORANGE)
        else
            skill_dec = ToColorStr(Language.TianShen.TSShenDianSkillTips1, COLOR3B.GREEN)
        end
    end

    if client_skill_cfg and client_skill_cfg then
        self.node_list.ts_shendian_skill_dec.text.text = skill_dec
        local bundle, name = ResPath.GetSkillIconById(client_skill_cfg.icon_resource)
        self.node_list.ts_shendian_skill_icon.image:LoadSprite(bundle, name)
    end

    for i, cell in ipairs(self.shendian_attr_list) do
        local data = list[i]
        cell:SetVisible(data ~= nil)

        if data ~= nil then
            cell:SetHallGoNum(go_num)
            cell:SetData(data)
            local cell_capability = cell:GetNowCapability()
            self.now_capability = self.now_capability + cell_capability
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.ts_shendian_attr_list_root.rect)
end

function TianShenView:FlushShenDianUpgradeAttrMessage()
    if not self.shendian_select_data then
        return
    end
    local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
    local now_order = hall_info and hall_info.order or 0
    local cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, now_order)
    local next_cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, now_order + 1)

    if not cfg then
        return
    end

    local is_unlock = now_order > 0
    local next_cfg = nil

    if is_unlock then
        next_cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, now_order + 1)
    end

    self.node_list.ts_shendian_active_btn:CustomSetActive(not is_unlock)
    self.node_list.ts_shendian_upgrade_up_btn:CustomSetActive(is_unlock and next_cfg ~= nil)
    self.node_list.ts_shendian_upgrade_max:CustomSetActive(is_unlock and next_cfg == nil)

    -- 升阶物品
    local up_item_id = cfg.cost_item_id or 0
    local need_num = cfg.cost_item_num or 0

    -- --没有激活时使用激活物品
    -- if not is_unlock then
    --     local act_cfg = TSShenLingDianData.Instance:GetHallCfgBySeq(self.shendian_select_data.seq)
    --     up_item_id = act_cfg and act_cfg.unlock_item_id or 0
    --     need_num = act_cfg and act_cfg.unlock_item_num or 0
    -- end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(up_item_id)
	local color = item_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.ts_shendian_upgrade_remind:CustomSetActive(item_num >= need_num and next_cfg ~= nil)
    self.node_list.ts_shendian_upgrade_up_remind:CustomSetActive(item_num >= need_num)
    self.node_list.ts_shendian_active_remind:CustomSetActive(item_num >= need_num)
	self.ts_shendian_upgrade_cell:SetData({item_id = up_item_id})
	self.ts_shendian_upgrade_cell:SetRightBottomTextVisible(true)
	self.ts_shendian_upgrade_cell:SetRightBottomColorText(string.format("%d/%d", item_num, need_num), color)

    -- 设置属性
    if not is_unlock then   -- 未解锁时使用1阶的属性
        cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, 1)
    end

    local list, cap = TSShenLingDianData.Instance:OutStrAttrListAndCapalityByAttrId(cfg, next_cfg, "attr_id", "attr_value", 1, 5)
    for i, cell in ipairs(self.shendian_upgrade_attr_list) do
        local data = list[i]
        cell:SetVisible(data ~= nil)

        if data ~= nil then
            cell:SetData(data)
        end
    end

    self.now_capability = self.now_capability + cap
	-- 获取列表当前的和下一个
	local star_num = TSShenLingDianData.Instance:GetHallAllStarNumBySeq(self.shendian_select_data.seq)
	local list = TSShenLingDianData.Instance:GetHallAttrListBySeqStar(self.shendian_select_data.seq, star_num)
    -- 获取当前属性
    local aim_data = nil
    for k, v in pairs(list) do
        if star_num >= v.star_num then
            aim_data = v
        end
    end

    if aim_data ~= nil then
        local star_list, star_cap = TSShenLingDianData.Instance:OutStrAttrListAndCapalityByAttrId(aim_data, nil, "attr_id", "attr_value", 1, 5)
        self.now_capability = self.now_capability + star_cap
    end

    self.node_list.ts_shendian_capability.text.text = self.now_capability
end

function TianShenView:FlushShendianBtnType()
	if self.shendian_btn_type == nil then
		self.shendian_btn_type = SHENDIAN_BTN_TYPE.GO_HAL_ATTR

		local red_1 = false
		local red_2 = false

        if self.shendian_select_data then
            local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
            local now_order = hall_info and hall_info.order or 0
            local cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, now_order)

            if cfg then
                local is_unlock = now_order > 0
                local next_cfg = nil
            
                if is_unlock then
                    next_cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, now_order + 1)
                end
            
                -- 升阶物品
                local up_item_id = cfg.cost_item_id or 0
                local need_num = cfg.cost_item_num or 0
            
                -- --没有激活时使用激活物品
                -- if not is_unlock then
                --     local act_cfg = TSShenLingDianData.Instance:GetHallCfgBySeq(self.shendian_select_data.seq)
                --     up_item_id = act_cfg and act_cfg.unlock_item_id or 0
                --     need_num = act_cfg and act_cfg.unlock_item_num or 0
                -- end

                local item_num = ItemWGData.Instance:GetItemNumInBagById(up_item_id)
                red_2 = item_num >= need_num
            end
        end

		if (not red_1) and red_2 then
			self.shendian_btn_type = SHENDIAN_BTN_TYPE.UPGRADE_ATTR
		end
	end

	self:FlushShenDianBtnStatus()
end

-- 按钮切换状态展示
function TianShenView:FlushShenDianBtnStatus()
	self.node_list.ts_shendian_attr_root:CustomSetActive(self.shendian_btn_type == SHENDIAN_BTN_TYPE.GO_HAL_ATTR)
	self.node_list.ts_shendian_upgrade_root:CustomSetActive(self.shendian_btn_type == SHENDIAN_BTN_TYPE.UPGRADE_ATTR)

	self.node_list.ts_shendian_attr_hl:CustomSetActive(self.shendian_btn_type == SHENDIAN_BTN_TYPE.GO_HAL_ATTR)
	self.node_list.ts_shendian_upgrade_nor:CustomSetActive(self.shendian_btn_type == SHENDIAN_BTN_TYPE.GO_HAL_ATTR)
	self.node_list.ts_shendian_upgrade_hl:CustomSetActive(self.shendian_btn_type == SHENDIAN_BTN_TYPE.UPGRADE_ATTR)
	self.node_list.ts_shendian_attr_nor:CustomSetActive(self.shendian_btn_type == SHENDIAN_BTN_TYPE.UPGRADE_ATTR)
end

---------------------------------------------------------------------
-- 切换上阵
function TianShenView:OnClickShenDianAttrBtn()
	if self.shendian_btn_type == SHENDIAN_BTN_TYPE.GO_HAL_ATTR then
		return
	end

	self.shendian_btn_type = SHENDIAN_BTN_TYPE.GO_HAL_ATTR
	self:FlushShenDianBtnStatus()
end

-- 切换升阶
function TianShenView:OnClickShenDianUpgradeBtn()
	if self.shendian_btn_type == SHENDIAN_BTN_TYPE.UPGRADE_ATTR then
		return
	end

	self.shendian_btn_type = SHENDIAN_BTN_TYPE.UPGRADE_ATTR
	self:FlushShenDianBtnStatus()
end

-- 激活
function TianShenView:OnClickShenDianActiveBtn()
    if not self.shendian_select_data then
        return
    end

    local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
    local now_order = hall_info and hall_info.order or 0
    local cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, now_order)
    local is_unlock = now_order > 0
    -- 升阶物品
    local up_item_id = cfg.cost_item_id or 0
    local need_num = cfg.cost_item_num or 0

    --没有激活时使用激活物品
    -- if not is_unlock then
    --     local act_cfg = TSShenLingDianData.Instance:GetHallCfgBySeq(self.shendian_select_data.seq)
    --     up_item_id = act_cfg and act_cfg.unlock_item_id or 0
    --     need_num = act_cfg and act_cfg.unlock_item_num or 0
    -- end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(up_item_id)

    if item_num >= need_num then
        TianShenWGCtrl.Instance:RequestTianShenHallActive(self.shendian_select_data.seq)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = up_item_id })
    end
end

-- 升阶
function TianShenView:OnClickShenDianUpgradeUpBtn()
    if not self.shendian_select_data then
        return
    end

    local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
    local now_order = hall_info and hall_info.order or 0
    local cfg = TSShenLingDianData.Instance:GetOrderCfgBySeqOrder(self.shendian_select_data.seq, now_order)
    local is_unlock = now_order > 0
    -- 升阶物品
    local up_item_id = cfg.cost_item_id or 0
    local need_num = cfg.cost_item_num or 0

    -- --没有激活时使用激活物品
    -- if not is_unlock then
    --     local act_cfg = TSShenLingDianData.Instance:GetHallCfgBySeq(self.shendian_select_data.seq)
    --     up_item_id = act_cfg and act_cfg.unlock_item_id or 0
    --     need_num = act_cfg and act_cfg.unlock_item_num or 0
    -- end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(up_item_id)

    if item_num >= need_num then
        TianShenWGCtrl.Instance:RequestTianShenHallUpOrder(self.shendian_select_data.seq)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = up_item_id })
    end
end

-- 星数加成
function TianShenView:OnClickShenDianStarAttrBtn()
    if not self.shendian_select_data then
        return
    end
    TianShenWGCtrl.Instance:OpenTSShenDianLingStarAttrView(self.shendian_select_data.seq)
end

-- 技能按钮
function TianShenView:OnClickShenDianSkillBtn()
    if not self.shendian_select_data then
        return
    end

    local use_seq = TSShenLingDianData.Instance:GetCurrTSHallUseSeq()
    local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
    local now_order = hall_info and hall_info.order or 0
    local act_cfg = TSShenLingDianData.Instance:GetHallCfgBySeq(self.shendian_select_data.seq)
    local need_go_hall_num = act_cfg and act_cfg.need_go_hall_num or 0
    local can_go_list = TSShenLingDianData.Instance:GetHallIndexListBySeqOrder(self.shendian_select_data.seq, now_order)
    local go_num = 0
    local is_unlock = now_order > 0

    if hall_info and hall_info.index_list then
        for i, index_data in ipairs(hall_info.index_list) do
            -- 神殿未激活或者未解锁孔位
            local is_lock = (not is_unlock) or can_go_list[i - 1] == nil
            if index_data ~= nil then
                if index_data ~= -1 then
                    go_num = go_num + 1
                end
            end
        end
    end

    local show_skill_tips = function()
        local skill_id = act_cfg and act_cfg.bs_skill or 0
        local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, 1)
        local skill_cfg = SkillWGData.Instance:GetTianShenHeJiSkillById(skill_id, 1)
    
        if client_skill_cfg and client_skill_cfg then
            self.node_list.ts_shendian_skill_dec.text.text = skill_cfg.skill_name
            local bundle, name = ResPath.GetSkillIconById(client_skill_cfg.icon_resource)
            self.node_list.ts_shendian_skill_icon.image:LoadSprite(bundle, name)
    
            local show_data = {
                icon = client_skill_cfg.icon_resource,
                top_text = skill_cfg.skill_name,
                body_text = client_skill_cfg.description,
                skill_id = skill_id,
                x = 0,
                y = 0,
                set_pos2 = true,
                hide_next = true,
            }
        
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end

    if (not is_unlock) or go_num < need_go_hall_num then
        show_skill_tips()
    else
        if go_num >= need_go_hall_num then
            if use_seq ~= self.shendian_select_data.seq then
                self:OnClickShenDianSkillUseBtn()
            else
                show_skill_tips()
            end
        end
    end
end

-- 技能按钮
function TianShenView:OnClickShenDianSkillUseBtn()
    if not self.shendian_select_data then
        return
    end

    print_error("请求上阵", self.shendian_select_data.seq)
    TianShenWGCtrl.Instance:RequestTianShenHallUseSeq(self.shendian_select_data.seq)
end

-- 快速上阵
function TianShenView:OnClickShenDianQuickGoBtn()
    if not self.shendian_select_data then
        return
    end

    local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
    local has_list = TSShenLingDianData.Instance:GetCanSlotAllTianShen()
    local now_order = hall_info and hall_info.order or 0
    local is_unlock = now_order > 0

    if not is_unlock then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSShenDianError)
        return
    end

    local can_go_list = TSShenLingDianData.Instance:GetHallIndexListBySeqOrder(self.shendian_select_data.seq, now_order)

    local send_index_list = {}
    local can_go_index = 1

    if hall_info and hall_info.index_list then
        for i, now_index in ipairs(hall_info.index_list) do
            -- 神殿未激活或者未解锁孔位
            local is_lock = (not is_unlock) or can_go_list[i - 1] == nil
            -- 可上阵
            send_index_list[i] = now_index
            if (not is_lock) and now_index == SHENDIAN_IS_NOT_HAVE_SLOT then
                send_index_list[i] = has_list and has_list[can_go_index] and has_list[can_go_index].index or SHENDIAN_IS_NOT_HAVE_SLOT
                can_go_index = can_go_index + 1
            end
        end
    end

    -- 快速上阵
    TianShenWGCtrl.Instance:SendCSTianshenHallQuickOperate(self.shendian_select_data.seq, send_index_list)
end

-- 快速下阵
function TianShenView:OnClickShenDianQuickDownBtn()
    if not self.shendian_select_data then
        return
    end

    local hall_info = TSShenLingDianData.Instance:GetClentTSHallItemInfo(self.shendian_select_data.seq)
    local now_order = hall_info and hall_info.order or 0
    local is_unlock = now_order > 0

    if not is_unlock then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSShenDianError)
        return
    end

    TianShenWGCtrl.Instance:SendCSTianshenHallQuickOperate(self.shendian_select_data.seq)
end

---------------------------------------------------------------------
---------------------------------天神神殿item-----------------------
TianshenShendianRender = TianshenShendianRender or BaseClass(BaseRender)
function TianshenShendianRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.lbl_ml_item_name.text.text = self.data.name
    local str = string.format("a3_sld_hall_type_%d", self.data.seq)
    self.node_list.hall_icon.image:LoadSprite(ResPath.GetF2TianShenImage(str))
    self.node_list.remind:CustomSetActive(TSShenLingDianData.Instance:GetTianShenTempleRemindBySeq(self.data.seq))
end

function TianshenShendianRender:OnSelectChange(is_select)
	if self.data == nil then return end
	self.node_list.img_hl:CustomSetActive(is_select)
end

---------------------------------天神属性item-----------------------
TSShendianAttrRender = TSShendianAttrRender or BaseClass(BaseRender)
function TSShendianAttrRender:LoadCallBack()
    -- 基础属性
    if self.attr_list == nil then
        self.attr_list = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.attr_list[i] = cell
            end
        end
    end

    self.now_go_num = 0
    self.now_capability = 0
end

function TSShendianAttrRender:ReleaseCallBack()
	if self.attr_list and #self.attr_list > 0 then
		for _, cell in ipairs(self.attr_list) do
			cell:DeleteMe()
			cell = nil
		end

		self.attr_list = nil
	end

    self.now_go_num = nil
    self.now_capability = nil
end

function TSShendianAttrRender:SetHallGoNum(go_num)
    self.now_go_num = go_num
end

function TSShendianAttrRender:GetNowCapability()
    if not self.data then
        return 0
    end

    if self.now_go_num >= self.data.go_num then
        return self.now_capability or 0
    end
  
    return 0
end


function TSShendianAttrRender:OnFlush()
    if not self.data then
        return
    end

    self.now_capability = 0
    self.node_list.need_flag_green:CustomSetActive(self.now_go_num >= self.data.go_num)
    self.node_list.need_flag_red:CustomSetActive(self.now_go_num < self.data.go_num)
    local color = self.now_go_num >= self.data.go_num and COLOR3B.GREEN or COLOR3B.RED
    local desc1 = string.format("[%d/%d]", self.now_go_num, self.data.go_num)
    local desc2 = ToColorStr(desc1, color)
    local desc3 = string.format(Language.TianShen.TSShenDianCondition, self.data.go_num, desc2)
    self.node_list.need_num.text.text = desc3

    local list, cap = TSShenLingDianData.Instance:OutStrAttrListAndCapalityByAttrId(self.data, nil, "attr_id", "attr_value", 1, 5)
    for i, cell in ipairs(self.attr_list) do
        local data = list[i]
        cell:SetVisible(data ~= nil)

        if data ~= nil then
            cell:SetData(data)
        end
    end

    self.now_capability = self.now_capability + cap

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.attr_list.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

---------------------------------神殿上阵位item-----------------------
TSShendianSlotRender = TSShendianSlotRender or BaseClass(BaseRender)
function TSShendianSlotRender:LoadCallBack()
    if self.show_model == nil then
        self.show_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["tianshen_skill_icon"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
    
        self.show_model:SetRenderTexUI3DModel(display_data)
    end
end

function TSShendianSlotRender:ReleaseCallBack()
    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    self.is_lock = nil
    self.shendian_seq = nil
    self.old_tianshen_id = nil
end

function TSShendianSlotRender:SetSlotLockStatus(is_lock)
    self.is_lock = is_lock
end

function TSShendianSlotRender:GetSlotLockStatus()
    return self.is_lock
end

function TSShendianSlotRender:SetShenDaianSeq(shendian_seq)
    self.shendian_seq = shendian_seq
end

function TSShendianSlotRender:GetShenDaianSeq()
    return self.shendian_seq
end

function TSShendianSlotRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.not_active:CustomSetActive(self.is_lock)
    self.node_list.tianshen_skill_icon:CustomSetActive((not self.is_lock) and self.data ~= SHENDIAN_IS_NOT_HAVE_SLOT)
    self.node_list.no_item:CustomSetActive(self.is_lock or self.data == SHENDIAN_IS_NOT_HAVE_SLOT)
    self.node_list.add_btn:CustomSetActive((not self.is_lock) and self.data == SHENDIAN_IS_NOT_HAVE_SLOT)
    local has_list = TSShenLingDianData.Instance:GetCanSlotAllTianShen()
    self.node_list.remind:CustomSetActive((not self.is_lock) and ((self.data == SHENDIAN_IS_NOT_HAVE_SLOT and #has_list > 0) or
                            TSShenLingDianData.Instance:CheckTianShenTempleGoRemindBySlot(self.data, has_list)))
    
    -- if (not self.is_lock) and self.data ~= SHENDIAN_IS_NOT_HAVE_SLOT then
    --     local bundle, asset = ResPath.GetTianShenNopackImg("ts_ring_" .. self.data)
    --     self.node_list.tianshen_skill_icon.image:LoadSprite(bundle, asset, function()
    --         self.node_list.tianshen_skill_icon.image:SetNativeSize()
    --     end)
    -- end

    -- 刷新模型
    if self.old_tianshen_id ~= self.data and self.data ~= SHENDIAN_IS_NOT_HAVE_SLOT then
        local cfg = TianShenWGData.Instance:GetTianShenCfg(self.data)
        local appe_image_id = cfg and cfg.appe_image_id or 0
        self.show_model:SetTianShenModel(appe_image_id, self.data, false, nil, nil)
        self.old_tianshen_id = self.data
    end

    local str = "a3_sld_slot_di_03"
    if not self.is_lock then
        if self.data ~= SHENDIAN_IS_NOT_HAVE_SLOT then
            str = "a3_sld_slot_di_01"
        else
            str = "a3_sld_slot_di_02"
        end
    end

    self.node_list.slot_di.image:LoadSprite(ResPath.GetF2TianShenImage(str))
end