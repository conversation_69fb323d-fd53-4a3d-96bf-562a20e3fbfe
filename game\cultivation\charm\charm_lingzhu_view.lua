-------------------------------
--天道石-天罪
-------------------------------
CharmTianZuiView = CharmTianZuiView or BaseClass(SafeBaseView)

-- local longzhu_circle2_rotate_time = 10
-- local longzhu_circle3_rotate_time = 4
-- local longzhu_circle5_scale_time = 3
-- local tween_holy_longzhu_circle2 = Vector3(0, 0, 360)
-- local tween_holy_longzhu_circle3 = Vector3(0, 0, -360)

function CharmTianZuiView:__init()
    self:SetMaskBg()
    self.view_style = ViewStyle.Full

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self:AddViewResource(0, "uis/view/cultivation_ui/charm_prefab", "layout_charm_lingzhu")
end

function CharmTianZuiView:LoadCallBack()
    -- self.node_list.longzhu_desc.text.text = Language.Charm.CharmLongZhuDesc

    local bundle, asset = ResPath.GetRawImagesPNG("a3_jj_bj_dbeijing")
    if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
    self.node_list.title_view_name.text.text = Language.Charm.TianZuiTitleName
    --self.tween_info = UITween_CONSTS.MultiFunction
    if not self.charm_longzhu_list then
        self.charm_longzhu_list = {}

        for i = 0, 7 do
             self.charm_longzhu_list[i] = CharmLongZhuRender.New(self.node_list["lingzhu_item" .. i])
             self.charm_longzhu_list[i]:SetIndex(i)
        end
    end

    if not self.lingzhu_attr_list then
        self.lingzhu_attr_list = AsyncListView.New(CharmLongZhuAttrRender, self.node_list.lingzhu_attr_list)
    end

    if not self.lingzhu_addition_list then
        self.lingzhu_addition_list = AsyncBaseGrid.New()
        local bundle = "uis/view/cultivation_ui/charm_prefab"
		local asset = "lingzhu_addition_item"
		self.lingzhu_addition_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.lingzhu_addition_list,
			assetBundle = bundle, assetName = asset, itemRender = LongZhuAdditionItemRender})
		self.lingzhu_addition_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_lingzhu_tunshi, BindTool.Bind(self.OnClickLingZhuTunShi, self))
end

function CharmTianZuiView:ShowIndexCallBack(index)
	-- self:RightPanleShowTween(self.node_list.lingzhu_right_tween_root, self.node_list.lingzhu_mid)

    -- local rotate_mode = DG.Tweening.RotateMode.FastBeyond360
	-- if self.tween_holy_longzhu_circle2 then
    --     self.tween_holy_longzhu_circle2:Restart()
    -- else
    --     local end_v = self.node_list.holy_longzhu_circle2.transform.localEulerAngles + tween_holy_longzhu_circle2
    --     self.tween_holy_longzhu_circle2 = self.node_list.holy_longzhu_circle2.transform:DORotate(end_v, longzhu_circle2_rotate_time, rotate_mode)
    --     self.tween_holy_longzhu_circle2:SetEase(DG.Tweening.Ease.Linear)
    --     self.tween_holy_longzhu_circle2:SetLoops(-1)
    -- end

    -- if self.tween_holy_longzhu_circle3 then
    --     self.tween_holy_longzhu_circle3:Restart()
    -- else
    --     local end_v = self.node_list.holy_longzhu_circle3.transform.localEulerAngles + tween_holy_longzhu_circle3
    --     self.tween_holy_longzhu_circle3 = self.node_list.holy_longzhu_circle3.transform:DORotate(end_v, longzhu_circle3_rotate_time, rotate_mode)
    --     self.tween_holy_longzhu_circle3:SetEase(DG.Tweening.Ease.Linear)
    --     self.tween_holy_longzhu_circle3:SetLoops(-1)
    -- end

    -- if self.tween_holy_longzhu_circle1 then
    --     self.tween_holy_longzhu_circle1:Restart()
    -- else
    --     self.node_list.holy_longzhu_circle1.transform.localScale = Vector3.one
    --     self.tween_holy_longzhu_circle1 = DG.Tweening.DOTween.Sequence()
	--     self.tween_holy_longzhu_circle1:Append(self.node_list.holy_longzhu_circle1.rect:DOScale(Vector3.one * 1.05, longzhu_circle5_scale_time))
	--     self.tween_holy_longzhu_circle1:Append(self.node_list.holy_longzhu_circle1.rect:DOScale(Vector3.one, longzhu_circle5_scale_time))
    --     self.tween_holy_longzhu_circle1:SetEase(DG.Tweening.Ease.Linear)
    --     self.tween_holy_longzhu_circle1:SetLoops(-1)
    -- end
end

function CharmTianZuiView:ReleaseCallBack()
    self.tween_info = nil

    if self.charm_longzhu_list then
        for k, v in pairs(self.charm_longzhu_list) do
            v:DeleteMe()
        end

        self.charm_longzhu_list = nil
    end

    if self.lingzhu_attr_list then
        self.lingzhu_attr_list:DeleteMe()
        self.lingzhu_attr_list = nil
    end

    if self.lingzhu_addition_list then
        self.lingzhu_addition_list:DeleteMe()
        self.lingzhu_addition_list = nil
    end

    -- self:DeleteLingZhuAnim()
end

function CharmTianZuiView:DeleteLingZhuAnim()
    if self.tween_holy_longzhu_circle2 then
        self.tween_holy_longzhu_circle2:Kill()
        self.tween_holy_longzhu_circle2 = nil
    end

    if self.tween_holy_longzhu_circle3 then
        self.tween_holy_longzhu_circle3:Kill()
        self.tween_holy_longzhu_circle3 = nil
    end

    if self.tween_holy_longzhu_circle1 then
        self.tween_holy_longzhu_circle1:Kill()
        self.tween_holy_longzhu_circle1 = nil
    end
end

function CharmTianZuiView:OnFlush(param_list, index)
    local longzhu_level = CultivationWGData.Instance:GetCharmYingYangLevel()
    local is_max_level = CultivationWGData.Instance:IsCharmYingYangMaxLevel()
    self.node_list.lingzhu_arrow:CustomSetActive(not is_max_level)
    self.node_list.lingzhu_next_level_bg:CustomSetActive(not is_max_level)
    self.node_list.lingzhu_current_level.text.text = string.format(Language.Charm.CharmLongZhuLevel, longzhu_level)
    local progress = is_max_level and 1 or 0
    local fill_amount = is_max_level and 1 or 0

    local current_level_cfg = CultivationWGData.Instance:GetCharmYingYangLevelCfg(longzhu_level)

    if not is_max_level then
        self.node_list.lingzhu_next_level.text.text = string.format(Language.Charm.CharmLongZhuLevel, longzhu_level + 1)
        local need_exp = current_level_cfg.need_exp
        local has_exp = CultivationWGData.Instance:GetCharmYingYangExp()
        -- progress = math.floor((has_exp / need_exp) * 100)
        progress = string.format("%.2f%%", tonumber(has_exp / need_exp * 100))

        fill_amount = has_exp / need_exp
        local btn_index = has_exp >= need_exp and 1 or 0
        self.node_list.btn_lingzhu_tunshi_text.text.text = Language.Charm.CharmLongZhuOpera[btn_index]
        self.node_list.btn_lingzhu_tunshi_remind:CustomSetActive(has_exp >= need_exp)
    else
        self.node_list.btn_lingzhu_tunshi_text.text.text = Language.Charm.CharmLongZhuOpera[0]
        self.node_list.btn_lingzhu_tunshi_remind:CustomSetActive(false)
    end

    self.node_list.lingzhu_level_process.text.text = progress
    self.node_list.lingzhu_level_process_slider.image.fillAmount = fill_amount

    if self.charm_longzhu_list then
        for k, v in pairs(self.charm_longzhu_list) do
            v:Flush()

            -- local level = CultivationWGData.Instance:GetCharmYingYangBeadLevel(k)
            -- self.node_list["holy_longzhu_light" .. k]:CustomSetActive(level > 0)
        end
    end

    local longzhu_attr_data_list = CultivationWGData.Instance:GetCharmLongZhuAttrDataList()
    self.lingzhu_attr_list:SetDataList(longzhu_attr_data_list)

    local longzhu_additon_datalist = CultivationWGData.Instance:GetCharmLongZhuAdditionCache()
    self.lingzhu_addition_list:SetDataList(longzhu_additon_datalist)
end

function CharmTianZuiView:RightPanleShowTween(right_node, canvas_group_node)
	RectTransform.SetAnchoredPositionXY(right_node.rect, 500, 0)
	right_node.rect:DOAnchorPos(Vector2(0, 0), self.tween_info.movetime)
	canvas_group_node.canvas_group.alpha = 0
	canvas_group_node.canvas_group:DoAlpha(0, 1, self.tween_info.canvas_group_show)
end

function CharmTianZuiView:OnClickLingZhuTunShi()
    local is_max_level, can_up_level = CultivationWGData.Instance:CanCharmLingZhuUpLevel()
    
    if not is_max_level then
        if can_up_level then
            CultivationWGCtrl.Instance:OnCSCharmOperate(CHARM_OPERATE_TYPE.YINYANG_UPLEVEL)
        else
            CultivationWGCtrl.Instance:OpenCharmLingZhuTunShiView()
        end
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.Charm.CharmLongZhuMaxLevel)
    end
end

-- 对应位置的升级特效
function CharmTianZuiView:PlayCharmLingZhuUpEffect(index)
    EffectManager.Instance:PlayAtTransform("effects2/prefab/ui/ui_effect_fuzhou2_prefab", "UI_effect_fuzhou2", 
        self.node_list["lingzhu_item" .. index].transform, 2, Vector2(0, -20))
end

-----------------------------------------------------------------------------------------------------
CharmLongZhuRender = CharmLongZhuRender or BaseClass(BaseRender)
function CharmLongZhuRender:OnFlush()
    if not self.index then
        return
    end

    local level = CultivationWGData.Instance:GetCharmYingYangBeadLevel(self.index)
    local active = level > 0
    XUI.SetGraphicGrey(self.node_list.item, not active)
    self.node_list.Image:CustomSetActive(active)
    self.node_list.img_grey:CustomSetActive(not active)

    self.node_list.grade_bg:CustomSetActive(active)

    if active then
        self.node_list.grade_text.text.text = tostring(level)-- string.format(Language.Charm.CharmLongZhuLevel, level)
    end
end

-----------------------------------------------------------------------------------------------------
CharmLongZhuAttrRender = CharmLongZhuAttrRender or BaseClass(BaseRender)
function CharmLongZhuAttrRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name
    self.node_list.attr_value.text.text = self.data.attr_value
    local add_per = self.data.add_value_sign
    self.node_list.attr_arrow:CustomSetActive(add_per)
    self.node_list.attr_add_value:CustomSetActive(add_per)

    if add_per then
        self.node_list.attr_add_value.text.text = self.data.add_value
    end
end

----------------------------------------------------------------------------------------------------
LongZhuAdditionItemRender = LongZhuAdditionItemRender or BaseClass(BaseRender)
function LongZhuAdditionItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.attr_id and  self.data.attr_id > 0 then
        local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(self.data.attr_id))
        local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
        -- local value_str = AttributeMgr.PerAttrValue(attr_str, self.data.add_per)
        local value_str = self.data.add_per == 0 and "0%" or  (self.data.add_per / 100) .. "%"
        self.node_list.attr_name.text.text = attr_name
        self.node_list.attr_value.text.text = value_str
        --self.node_list.lingzhu_addition_item.text.text = attr_name .. " " .. ToColorStr(value_str, COLOR3B.GREEN)
    end
end