ExperienceFbWGData = ExperienceFbWGData or BaseClass()
local FIXED_GET_NUM = 5
local FIXED_MIN_NUM = 1
local FIXED_WAVE_GET_NUM = 8
local FIXED_WAVE_MIN_NUM = 1

function ExperienceFbWGData:__init()
	if ExperienceFbWGData.Instance ~= nil then
		ErrorLog("[ExperienceFbWGData] Attemp to create a singleton twice !")
	end
	ExperienceFbWGData.Instance = self

	-- 离线挂机卡表只是为了判断是否是离线挂机卡用的
	local auto_cfg = ConfigManager.Instance:GetAutoConfig("fb_exp_west_auto")
	if auto_cfg then
		self.base_cfg = auto_cfg.other[1]
		self.level_cfg = auto_cfg.level
		self.level_wave_cfg = ListToMap(auto_cfg.wave, "level", "wave")
		self.wave_card_cfg = ListToMap(auto_cfg.card, "index", "seq", "level")
	end
	-- RemindManager.Instance:Register(RemindName.TianShenLiLianFB, BindTool.Bind(self.GetLiLianRemind, self))
end

function ExperienceFbWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.TianShenLiLianFB)

	ExperienceFbWGData.Instance = nil
end

---------------------------------------------------------------------------------
--获取基础配置表
function ExperienceFbWGData:GetBaseCfg()
	return self.base_cfg
end

--获取所有的
function ExperienceFbWGData:GetLevelList()
	return self.level_cfg
end

--获取关卡表根据关卡
function ExperienceFbWGData:GetLevelCfgByLevel(level)
	local empty = {}
	return (self.level_cfg or empty)[level]
end

--获取关卡所有的波次
function ExperienceFbWGData:GetWaveListByLevel(level)
	local empty = {}
	return (self.level_wave_cfg or empty)[level]
end

--获取关卡某个波次配置
function ExperienceFbWGData:GetLevelWaveCfgByLevelWave(level, wave)
	local empty = {}
	return ((self.level_wave_cfg or empty)[level] or empty)[wave]
end

--获取下标所有卡牌配置
function ExperienceFbWGData:GetCardListByIndex(index)
	local empty = {}
	return (self.wave_card_cfg or empty)[index]
end

--获取下标某个卡牌配置
function ExperienceFbWGData:GetCardCfgByIndexSeq(index, seq, level)
	local empty = {}
	return (((self.wave_card_cfg or empty)[index] or empty)[seq] or empty)[level]
end

---------------------------------------------------------------------------------
-- 副本基础信息
function ExperienceFbWGData:SetExpWestBaseInfo(protocol)
	self.base_info = {}
	self.base_info.level = protocol.level
	self.base_info.wave_pass_reward_flag = protocol.wave_pass_reward_flag
end

-- 获取当前的等级
function ExperienceFbWGData:GetCurrExpWestLevel()
	local empty = {}
	local level = (self.base_info or empty).level or 0
	-- 这个等级是已通关的，所以当前的关卡需要加一
	level = level + 1
	return level
end

-- 获取当前的等级
function ExperienceFbWGData:GetCurrExpWestPassLevel()
	local empty = {}
	local level = (self.base_info or empty).level or 0
	return level
end

-- 获取当前的波数
function ExperienceFbWGData:GetCurrExpWestWave()
	local wave_list = self:GetCurrExpWestAllWavePass()
	-- 加一个i过滤掉0号位置
	for i, v in ipairs(wave_list) do
		if v == 0 then
			return i
		end
	end

	return 1
end

-- 获取当前的波数的信息
function ExperienceFbWGData:GetCurrExpWestWavePass(wave)
	local empty = {}
	return ((self.base_info or empty).wave_pass_reward_flag or empty)[wave]
end

-- 获取当前所有的波数
function ExperienceFbWGData:GetCurrExpWestAllWavePass()
	local empty = {}
	return (self.base_info or empty).wave_pass_reward_flag
end

-- 场景信息
function ExperienceFbWGData:SetExpWestSceneInfo(protocol)
	self.scene_info = protocol
end

-- 获取场景信息
function ExperienceFbWGData:GetExpWestSceneInfo()
	return self.scene_info
end

-- 排行信息
function ExperienceFbWGData:SetExpWestRankInfo(protocol)
	self.rank_info = {}
	self.rank_info[protocol.level] = protocol.rank_item_list
end

-- 获取某个等级的排行信息
function ExperienceFbWGData:GetRankListByLevel(level)
	local empty = {}
	return (self.rank_info or empty)[level]
end

-- 卡片信息
function ExperienceFbWGData:SetExpWestCardInfo(protocol)
	self.card_item_list = protocol.card_item_list
end

-- 获取某一波的卡牌信息
function ExperienceFbWGData:GetCardInfoByWave(wave)
	local empty = {}
	return (self.card_item_list or empty)[wave]
end

-- 获取所有的卡牌信息
function ExperienceFbWGData:GetAllCardInfo()
	return self.card_item_list
end

-- 设置技能能量
function ExperienceFbWGData:SetExpWestSkillInfo(protocol)
	self.skill_power = protocol.skill_power
end

-- 获取技能能量
function ExperienceFbWGData:GetExpWestSkillPower()
	return self.skill_power or 0
end
---------------------------------------------------------------------------------
-- 获取当前的关卡是否为已挑战关卡
function ExperienceFbWGData:CheckStageIsNow(stage)
	local curr_stage = self:GetCurrExpWestLevel()
	return curr_stage == stage
end

-- 获取当前的关卡是否为已挑战关卡
function ExperienceFbWGData:CheckStageIsPass(stage)
	local curr_stage = self:GetCurrExpWestLevel()
	return curr_stage > stage
end

-- 获取当前的5个列表
function ExperienceFbWGData:LiLianShowStageList()
	if not self.level_cfg then
		return nil
	end

	local all_level_count = #self.level_cfg
    local now_level = self:GetCurrExpWestLevel()
	local start_index = now_level
	local max_start_index = all_level_count - FIXED_GET_NUM + 1
	-- 最大
	if all_level_count - now_level <= FIXED_GET_NUM then
		start_index = max_start_index
	elseif now_level ~= FIXED_MIN_NUM and now_level > FIXED_MIN_NUM then
		start_index = now_level - 1
	end

	-- print_error("数据", all_level_count, max_start_index, now_level, start_index)

	local return_table = {}
	for i = start_index, start_index + FIXED_GET_NUM - 1 do
		local cfg = self.level_cfg[i]
		local info = {}
		info.level = cfg.level
		info.monster_id = cfg.monster_id
		local wave_list = self:GetWaveListByLevel(cfg.level)
		info.wave = #wave_list
		info.stage_name = cfg.stage_name
		info.now_level = now_level

		table.insert(return_table, info)
	end

	return return_table
end

-- 获取当前关卡的8个波次
function ExperienceFbWGData:LiLianShowWaveList(level, wave)
	local wave_list = self:GetWaveListByLevel(level)

	if (not wave_list) or #wave_list <= 0 then
		return nil
	end

	local all_level_count = #wave_list
	local start_index = wave
	local max_start_index = all_level_count - FIXED_WAVE_GET_NUM + 1

	-- 最大
	-- print_error("最大展示", all_level_count, wave)
	if all_level_count - wave <= FIXED_WAVE_GET_NUM then
		start_index = max_start_index
	elseif wave ~= FIXED_WAVE_MIN_NUM and wave > FIXED_WAVE_MIN_NUM then
		start_index = wave - 1
	end

	local return_table = {}
	for i = start_index, start_index + FIXED_WAVE_GET_NUM - 1 do
		local cfg = wave_list[i]
		table.insert(return_table, cfg)
	end

	return return_table
end