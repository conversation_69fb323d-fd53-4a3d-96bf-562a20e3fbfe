YangLongSiLSGWRankView = YangLongSiLSGWRankView or BaseClass(SafeBaseView)

function YangLongSiLSGWRankView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(942, 590)})
	self:AddViewResource(0, "uis/view/country_map_ui/yanglonsi_ui_prefab", "layout_yanglongsi_lsgw_rank")
end

function YangLongSiLSGWRankView:__delete()
end

function YangLongSiLSGWRankView:LoadCallBack()
	if not self.score_rank_list then
		self.score_rank_list = AsyncListView.New(LSGWScoreRankListItemRender, self.node_list.score_rank_list)
	end

	self.node_list.title_view_name = Language.Rank.PaiMing
end

function YangLongSiLSGWRankView:ReleaseCallBack()
	if self.score_rank_list then
		self.score_rank_list:DeleteMe()
		self.score_rank_list = nil
	end
end

function YangLongSiLSGWRankView:OnFlush()
	local rank_data_list = YangLongSiaWGData.Instance:GetLSGWRankInfo()
	local has_data = not IsEmptyTable(rank_data_list)
	self.node_list.not_rank_info:SetActive(not has_data)

	if has_data then
		self.score_rank_list:SetDataList(rank_data_list)
	end

	local my_rank_data = YangLongSiaWGData.Instance:GetMyRankData()
	local has_my_rank_data = not IsEmptyTable(my_rank_data)
	local my_rank_id = has_my_rank_data and my_rank_data.rank or Language.YangLongSi.NotOnTheList
	local mt_rank_score = has_my_rank_data and my_rank_data.rank_value or 0
	self.node_list.my_rank_text.text.text = my_rank_id
	self.node_list.my_score_text.text.text = mt_rank_score
end

LSGWScoreRankListItemRender = LSGWScoreRankListItemRender or BaseClass(BaseRender)
function LSGWScoreRankListItemRender:__init()
end

function LSGWScoreRankListItemRender:__delete()
	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end
end

function LSGWScoreRankListItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	if not self.rank_reward_list then
		self.rank_reward_list = AsyncListView.New(ItemCell, self.node_list.rank_reward_list)
		self.rank_reward_list:SetStartZeroIndex(true)
	end

	local rank_index = self.data.rank
	local is_top_three = rank_index <= 3
	local word_color = is_top_three and COLOR3B.WHITE or COLOR3B.DEFAULT

	self.node_list.rank:SetActive(not is_top_three)
	self.node_list.img_rank:SetActive(is_top_three)

	if is_top_three then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..rank_index))
		self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_"..rank_index))
		self.node_list.root_bg:SetActive(true)
	else
		self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_bt_2"))
		self.node_list.root_bg:SetActive(rank_index % 2 ~= 0)
	end

	self.node_list.rank.text.text = self.data.rank
	self.node_list.player_name.text.text = ToColorStr(self.data.name, word_color)
	self.node_list.player_score.text.text = ToColorStr(self.data.rank_value, word_color)

	local reward_data = YangLongSiaWGData.Instance:GetRankRewardCfgByRankId(self.data.rank)
	self.rank_reward_list:SetDataList(reward_data.reward_show_item)
end