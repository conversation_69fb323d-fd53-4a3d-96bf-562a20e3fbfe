
MountLingChongDisplayBox = MountLingChongDisplayBox or BaseClass(SafeBaseView)
function MountLingChongDisplayBox:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_skill_tip_view")
	self.cur_wuxing_index = WUXING_HURT_TYPE.WUXING_JIN
end

function MountLingChongDisplayBox:ReleaseCallBack()
	if self.stuff_item then
		self.stuff_item:DeleteMe()
		self.stuff_item = nil
	end

	if self.item_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
		self.item_change_callback = nil
	end

	if self.next_desc_list and #self.next_desc_list > 0 then
        for i, next_desc_cell in ipairs(self.next_desc_list) do
            next_desc_cell:DeleteMe()
            next_desc_cell = nil
        end

        self.next_desc_list = nil
    end 

	self.show_data = nil
	self.old_level = nil
	self.cur_wuxing_index = WUXING_HURT_TYPE.WUXING_JIN
	self.use_default_pos = nil
end

function MountLingChongDisplayBox:OpenCallBack()
	TipWGCtrl.Instance:CloseOtherView(function ()
		self:Close()
	end)
end

function MountLingChongDisplayBox:CloseCallBack()
	self.old_level = nil
	self.cur_wuxing_index = WUXING_HURT_TYPE.WUXING_JIN
	TipWGCtrl.Instance:CloseOtherView(nil)
end

function MountLingChongDisplayBox:LoadCallBack()
	if self.item_change_callback == nil then
     	self.item_change_callback = BindTool.Bind1(self.OnItemChange, self)
    	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback, false)
    end

    -- 初始化11个技能
	if not self.next_desc_list then
        self.next_desc_list = {}

        for i = 1, 11 do
            local cell_obj = self.node_list.next_desc:FindObj(string.format("next_title_root_%d", i))
            if cell_obj then
                local cell = SkillNextDescRender.New(cell_obj)
                cell:SetIndex(i)
                self.next_desc_list[i] = cell
            end
        end
	end

	XUI.AddClickEventListener(self.node_list.skill_levelup_btn, BindTool.Bind1(self.OnClickUpSkillLevel, self))
	-- self.stuff_item = ItemCell.New(self.node_list.phItemCell)
	-- XUI.AddClickEventListener(self.node_list.btn_wuxing_1, BindTool.Bind(self.OnClickSelectMulti, self, WUXING_HURT_TYPE.WUXING_JIN))
	-- XUI.AddClickEventListener(self.node_list.btn_wuxing_2, BindTool.Bind(self.OnClickSelectMulti, self, WUXING_HURT_TYPE.WUXING_MU))
	-- XUI.AddClickEventListener(self.node_list.btn_wuxing_3, BindTool.Bind(self.OnClickSelectMulti, self, WUXING_HURT_TYPE.WUXING_SHUI))
	-- XUI.AddClickEventListener(self.node_list.btn_wuxing_4, BindTool.Bind(self.OnClickSelectMulti, self, WUXING_HURT_TYPE.WUXING_HUO))
	-- XUI.AddClickEventListener(self.node_list.btn_wuxing_5, BindTool.Bind(self.OnClickSelectMulti, self, WUXING_HURT_TYPE.WUXING_TU))
end

function MountLingChongDisplayBox:OnItemChange()
	self:Flush()
end

function MountLingChongDisplayBox:LevelUpCallback(show_data)
	self:SetData(show_data)
	self:Flush()
end

function MountLingChongDisplayBox:SetData(show_data)
	self.show_data = show_data
end

function MountLingChongDisplayBox:GetData()
	-- body
	return self.show_data
end

function MountLingChongDisplayBox:OnFlush(param_t, index)
	self:FlushTopMessage()
	local skill_desc_list_height = self:FlushSkillDescNow()
	self:SetContentPos()
	self:SetSkillLimit()
	self:SetSkillShowBgKuang()
	self:ShowCapability()
	self:ChangePanelHeight(skill_desc_list_height)
end

-- 上部分技能信息
-- limit_tip : 限制提示
-- icon	: 技能图标(如果是物品名称跟随物品颜色)
-- top_text : 技能名称
-- top_text_color : 技能名称颜色
function MountLingChongDisplayBox:FlushTopMessage()
	--技能图标
	if self.show_data.icon ~= nil then
		XUI.SetSkillIcon(self.node_list.ph_ml_skill_bg, self.node_list.ph_ml_skill_item, self.show_data.icon, self.show_data.icon_res_fun)
	end

	-- -- 限制提示文本
	-- self.node_list.limit_tip:CustomSetActive(self.show_data.limit_tip ~= nil and self.show_data.limit_tip ~= "")
	-- self.node_list.limit_tip.text.text = self.show_data.limit_tip or ""

	local name_str = self.show_data.top_text or ""
	-- 改为固定颜色 适配底板
	if self.show_data.is_active_skill then
		name_str = ToColorStr(name_str, COLOR3B.C9)
	else
		name_str = ToColorStr(name_str, COLOR3B.C4)
	end
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(self.show_data.icon)
	-- if self.show_data.top_text_color then
	-- 	name_str = ToColorStr(name_str, self.show_data.top_text_color)
	-- elseif item_cfg then
	-- 	name_str = ToColorStr(name_str, ITEM_COLOR[item_cfg.color] or COLOR3B.DEFAULT)
	-- end

	self.node_list.skill_name.text.text = name_str
end

-- 刷新当前技能信息
-- body_text : 当前技能描述
-- skill_box_type : 技能展示类型
-- skill_id ：技能id
-- eff_url ：技能变化特效
function MountLingChongDisplayBox:FlushSkillDescNow()
	local skill_cfg, skill_level, next_skill_cfg, next_data, desc, skill_index
	if self.show_data.skill_box_type == SKILL_BOX_TYPE.QI_CHONG_SKILL then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data = self:FlushQiChongSkill()
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_PASSSKILL then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data = self:FlushTianshenPassiveSkill()
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_SKILL then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data, skill_index = self:FlushTianshenSkill()
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.CUI_ZHUO_SKILL then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data = self:FlushCuiZhuoSkill()
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_MO then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data = self:FlushTianshenMoSkill()
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_WUQI then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data, skill_index = self:FlushTianshenShenQiSkill()
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.ARTIFACT_SKILL then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data = self:FlushArtifactSkill()
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.CONTROL_BEASTS then
		skill_cfg, desc, skill_level, next_skill_cfg, next_data = self:FlushControlBeastSkill()
	else
		skill_cfg, desc, skill_level, next_skill_cfg, next_data = self:AdvancedSkill()
	end

	local skill_desc_list_height = self:SetSkillDescMsg(skill_cfg, desc, skill_level, next_skill_cfg, next_data, skill_index)
	self:SetSkillLevel(skill_level)
	return skill_desc_list_height
end

--封装一份技能
function MountLingChongDisplayBox:GetNextSkillDescData(is_unlock, title, desc, unlock_desc)
	local info = {}
	local title_str = title and title or Language.TianShen.SkillDes2
	info.is_unlock = is_unlock
	info.unlock_desc = unlock_desc or ""
	info.title = title
	info.desc = desc
	info.is_active_skill = self.show_data.is_active_skill
	
	return info
end


-- 刷新骑宠技能信息
-- type : 骑宠类型
function MountLingChongDisplayBox:FlushQiChongSkill()
	local skill_level = NewAppearanceWGData.Instance:GetBaseQiChongSkillLevel(self.show_data.type, self.show_data.skill_id) or 1
	if skill_level <= 0 then
		skill_level = 1
	end

	local skill_cfg = NewAppearanceWGData.Instance:GetBaseQiChongSkillCfg(self.show_data.type, self.show_data.skill_id, skill_level)
	local next_skill_cfg = NewAppearanceWGData.Instance:GetBaseQiChongSkillCfg(self.show_data.type, self.show_data.skill_id, skill_level + 1)
	local desc = skill_cfg and skill_cfg.skill_describe or ""
	local next_desc = next_skill_cfg and next_skill_cfg.skill_describe or ""
	local next_data = {}
	next_data[1] = self:GetNextSkillDescData(false, Language.LongZhu.NextStr, next_desc)
	return skill_cfg, desc, skill_level, next_skill_cfg, next_data
end

-- 刷新天神被动技能信息
function MountLingChongDisplayBox:FlushTianshenPassiveSkill()
	local skill_level = TianShenWGData.Instance:GetTianShenPassiveSkill(self.show_data.tianshen_index, self.show_data.skill_id) or 1
	local skill_cfg = TianShenWGData.Instance:GetSpecialImagePasvSkillLevelCfg(self.show_data.tianshen_index, self.show_data.skill_id, skill_level)
	local next_skill_cfg = TianShenWGData.Instance:GetSpecialImagePasvSkillLevelCfg(self.show_data.tianshen_index, self.show_data.skill_id, skill_level + 1)
	local desc = skill_cfg and skill_cfg.skill_describe or ""
	local next_desc = next_skill_cfg and next_skill_cfg.skill_describe or ""

	local next_data = {}
	next_data[1] = self:GetNextSkillDescData(false, Language.LongZhu.NextStr, next_desc)
	return skill_cfg, desc, skill_level, next_skill_cfg, next_data
end

-- 刷新天神主动技能信息
-- tianshen_index : 天神下标
-- other_hurt_percent : 天神伤害比例
-- other_description : 天神技能其他组装的描述
function MountLingChongDisplayBox:FlushTianshenSkill()
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.show_data.tianshen_index)
	local is_active = TianShenWGData.Instance:IsActivation(self.show_data.tianshen_index)
	local shenshi_rank = tianshen_info and tianshen_info.star or 0
	local check_skill_id = self:GetTianShenGetLevelSkillId()
	local skill_level = TianShenWGData.Instance:GetTianShenSkillLv(self.show_data.tianshen_index, shenshi_rank, check_skill_id) or 1
	local desc = ""
	if is_active then
		desc = SkillWGData.Instance:GetTianShenSkillDes(self.show_data.skill_id, skill_level, true, 
		self.show_data.other_hurt_percent, 
		self.show_data.other_description
	)
	end

	local next_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(self.show_data.skill_id, skill_level + 1)
	local skill_data = TianShenWGData.Instance:GetTianShenSkillLvStarBySkill(self.show_data.tianshen_index, self.show_data.skill_id)
	local next_data = {}

	for i, v in ipairs(skill_data) do
		local next_desc = SkillWGData.Instance:GetTianShenSkillDes(self.show_data.skill_id, v.skill_lv, true, 
			self.show_data.other_hurt_percent, 
			self.show_data.other_description
		)

		next_data[i] = self:GetNextSkillDescData(v.star_level <= shenshi_rank, 
			string.format(Language.TianShen.TSSkillTipsShow, v.skill_lv), 
			next_desc, 
			string.format(Language.TianShen.TSSkillTipsShow2, v.star_level)
		)
	end

	return nil, desc, skill_level, next_skill_cfg, next_data, skill_level
end

-- 获取天神等级的技能id
function MountLingChongDisplayBox:GetTianShenGetLevelSkillId()
	local get_skill_id = nil
	if self.show_data == nil then
		return get_skill_id
	end	

	get_skill_id = self.show_data.skill_id
	local cfg = TianShenWGData.Instance:GetTianShenCfg(self.show_data.tianshen_index)
	if cfg ~= nil and cfg.is_multi_wuxing == 1 then
		local data = TianShenWGData.Instance:GetMultiSkillListByIndex(cfg.appe_image_id, 1)
		local cur_data = TianShenWGData.Instance:GetMultiSkillListByIndex(cfg.appe_image_id, self.cur_wuxing_index)
		local skill_index = nil
		for k,v in pairs(cur_data) do
			if v == get_skill_id then
				skill_index = k
				break
			end
		end

		if skill_index ~= nil and data[skill_index] ~= nil then
			get_skill_id = data[skill_index]
		end		
	end

	return get_skill_id
end

-- 刷新淬琢技能信息
-- solt : 位置
-- seq : 索引
function MountLingChongDisplayBox:FlushCuiZhuoSkill()
	local data_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.show_data.solt)
	local skill_level = (data_info.skill_level_list or {}) [self.show_data.seq]
	local real_level = skill_level >= 1 and skill_level or 1
	local skill_cfg = FightSoulWGData.Instance:GetCuiZhuoSkillLevelCfg(self.show_data.solt, self.show_data.seq, real_level)
	local next_skill_cfg = FightSoulWGData.Instance:GetCuiZhuoSkillLevelCfg(self.show_data.solt, self.show_data.seq, skill_level + 1)
	local desc = skill_cfg and skill_cfg.skill_desc or ""
	local next_desc = next_skill_cfg and next_skill_cfg.skill_describe or ""

	local next_data = {}
	next_data[1] = self:GetNextSkillDescData(false, Language.LongZhu.NextStr, next_desc)
	return skill_cfg, desc, skill_level, next_skill_cfg, next_data
end

-- 刷新天神入魔技能信息
-- tianshen_index : 天神下标
-- skill_lv : 技能等级
function MountLingChongDisplayBox:FlushTianshenMoSkill()
	local skill_cfg = TianShenHuamoWGData.Instance:GetSkillCfgById(self.show_data.tianshen_index, self.show_data.skill_id)
	local level_cfg = TianShenHuamoWGData.Instance:GetSkillLevelCfgById(self.show_data.tianshen_index, self.show_data.skill_lv)
	local temp_skill_des = nil
	local temp_next_skill_des = nil
	local now_describe = skill_cfg and skill_cfg.skill_describe or ""
	local skill_level = self.show_data.skill_lv or 1
	local next_skill_cfg = TianShenHuamoWGData.Instance:GetSkillLevelCfgById(self.show_data.tianshen_id, skill_level + 1)

	if level_cfg then
		temp_skill_des = string.format("%d%%", (level_cfg.add_per / 10000) * 100) 
	end

	if next_skill_cfg then
		temp_next_skill_des = string.format("%d%%", (next_skill_cfg.add_per / 10000) * 100) 
	end

	local desc = temp_skill_des and string.format(now_describe, temp_skill_des) or now_describe
	local next_desc = temp_next_skill_des and string.format(now_describe, temp_next_skill_des) or now_describe

	local next_data = {}
	next_data[1] = self:GetNextSkillDescData(false, Language.LongZhu.NextStr, next_desc)
	return skill_cfg, desc, nil, next_skill_cfg, next_data
end

-- 刷新天神神器技能
-- type : 技能类型
function MountLingChongDisplayBox:FlushTianshenShenQiSkill()
	local cfg_list = TianShenWGData.Instance:GetShenQiSkillCfgById(self.show_data.skill_id)
	local now_level = self.show_data.skill_level or 1
	local next_data = {}
	-- local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.show_data.skill_box_type)
	for i, v in ipairs(cfg_list) do
		next_data[i] = self:GetNextSkillDescData(now_level >= v.skill_level, 
			string.format(Language.TianShen.TSSkillTipsShow, v.skill_level), 
			v.skill_des, 
			string.format(Language.TianShen.TSSkillTipsShow2, v.skill_level - 1) -- 技能从1级开始 星级从0星开始
		)
	end
	return nil, nil, now_level, nil, next_data, now_level
end

-- 刷新双修技能
function MountLingChongDisplayBox:FlushArtifactSkill()
	local next_skill_cfg
	if self.show_data.skill_type == 1 then
		next_skill_cfg = ArtifactWGData.Instance:GetSkillCfgBySkillLevel(self.show_data.seq, self.show_data.skill_level + 1)
	elseif self.show_data.skill_type == 2 then
		next_skill_cfg = ArtifactWGData.Instance:GetAwakeSkillCfgByLevel(self.show_data.seq, self.show_data.skill_level + 1)
	end
	local now_level = self.show_data.skill_level or 1
	local next_data = {}
	if next_skill_cfg ~= nil then
		local next_desc = next_skill_cfg and next_skill_cfg.skill_desc or ""
		local next_level = self.show_data.skill_type == 1 and next_skill_cfg.star_level or next_skill_cfg.awake_level
		local unlock_desc = string.format(Language.TianShen.TSSkillTipsShow2, next_level)
		next_data[1] = self:GetNextSkillDescData(false, Language.LongZhu.NextStr, next_desc, unlock_desc)
	end
	return nil, nil, now_level, nil, next_data
end

-- 刷新幻兽技能
function MountLingChongDisplayBox:FlushControlBeastSkill()
	local next_data = {}
	if not IsEmptyTable(self.show_data.spirit_data) then
		for i, v in ipairs(self.show_data.spirit_data) do
			local spirit_desc_data = self:GetNextSkillDescData(true, v.skill_title, v.skill_des)
			table.insert(next_data, spirit_desc_data)
		end
	end
	return nil, nil, nil, nil, next_data
end

-- 刷新普通技能信息
-- type : 技能类型
function MountLingChongDisplayBox:AdvancedSkill()
	local skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(self.show_data.type, self.show_data.skill_id) or 1
	if skill_level <= 0 then
		skill_level = 1
	end

	local skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(self.show_data.type, self.show_data.skill_id, skill_level)
	local next_skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(self.show_data.type, self.show_data.skill_id, skill_level + 1)
	local desc = skill_cfg and skill_cfg.skill_describe or ""
	local next_desc = next_skill_cfg and next_skill_cfg.skill_describe or ""

	local next_data = {}
	next_data[1] = self:GetNextSkillDescData(false, Language.LongZhu.NextStr, next_desc)
	return skill_cfg, desc, skill_level, next_skill_cfg, next_data
end
----------------------------------------------------------
--刷新位置信息
-- 默认（0，1）
-- set_pos （0.5, 0）
-- set_pos2 （0.5, 0.5）
function MountLingChongDisplayBox:SetContentPos()
	local pos = nil

	if self.show_data.set_pos then 	--手动计算位置
		self.node_list.all_content.rect.pivot = Vector2(0.5, 0)
		pos = Vector2(self.show_data.x,self.show_data.y)
	elseif self.show_data.set_pos2 then 	--手动计算位置
		self.node_list.all_content.rect.pivot = Vector2(0.5, 0.5)
		pos = Vector2(self.show_data.x,self.show_data.y)
	else
		self.node_list.all_content.rect.pivot = Vector2(0, 1)
		local len = 0
		
		if self.show_data.body_text ~= nil and self.show_data.body_text ~= "" then
			len = string.len(self.show_data.body_text)
		end

		local height = math.ceil((len / 15)) * 22 - 100
		pos = Vector2(self.show_data.x - 140, height)
	end

	self.node_list.all_content.rect.anchoredPosition = pos
end

-- 展示技能等级
-- hide_level : 隐藏等级
function MountLingChongDisplayBox:SetSkillLevel(skill_level)
	local hide_level = self.show_data.hide_level--是否隐藏等级显示
	self.node_list.ph_ml_skill_lv_root:CustomSetActive(not hide_level)

	if not hide_level then
		local show_level = skill_level ~= 0 and skill_level or self.show_data.skill_level
		show_level = show_level or 1
		self.node_list.skill_level_text.text.text = show_level
	
		--如果等级发生变化
		if self.old_level and self.old_level ~= -1 and skill_level ~= nil and self.old_level < skill_level then
			TipWGCtrl.Instance:ShowEffect({effect_type = self.show_data.eff_url or UIEffectName.s_shengji, 
				is_success = true,pos = Vector2(0, 0), 
				parent_node = self.node_list["go_effect"]}
			)
		end
		self.old_level = skill_level
	end
end

-- 设置限制信息
-- limit_text : 限制文本
function MountLingChongDisplayBox:SetSkillLimit()
	self.node_list.limit_text:CustomSetActive(self.show_data.limit_text ~= nil and self.show_data.limit_text ~= "")
	self.node_list.limit_text.text.text = self.show_data.limit_text
end

-- 设置当前文本等级描述和下一等级文本描述
-- is_up_operate : 是否升级操作
-- is_lock : 技能是否解锁
-- hide_next : 隐藏下一级
-- is_active_skill : 是否为主动技能
-- passive_str ：蓝字底文本 默认：被动
-- limit_tip :  红色字体 默认：未解锁
function MountLingChongDisplayBox:SetSkillDescMsg(skill_cfg, desc, skill_level, next_skill_cfg, next_data,skill_index)
	-- if self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_WUQI then
	-- 	next_desc = self.show_data.next_skill_descxx
	-- end
	if self.show_data.skill_box_type == SKILL_BOX_TYPE.CONTROL_BEASTS or self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_WUQI then
		self.node_list.scroll_view:CustomSetActive(next_data ~= nil and #next_data > 0 and (not self.show_data.hide_next))
	else
		self.node_list.scroll_view:CustomSetActive(next_skill_cfg ~= nil and next_data ~= nil and #next_data > 0 and (not self.show_data.hide_next))
	end
	
	self.node_list.bottomPanel:CustomSetActive(self.show_data.is_up_operate ~= nil and self.show_data.is_up_operate and (not self.show_data.is_lock))

	local body_text = self.show_data.body_text or ""
	if desc ~= nil and desc ~= "" then
		body_text = desc
	end

	if self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_WUQI then
		self.node_list.cur_desc:SetActive(not self.show_data.is_lock)
		if self.show_data.is_lock then
			body_text = ""
		end
	else
		self.node_list.cur_desc:SetActive(body_text ~= "")
	end
	
	local title_new_color = self.show_data.is_active_skill and "#FFF8BB" or "#CBFBFD"
	local desc_new_color = self.show_data.is_active_skill and "#FFE58C" or "#8CB5CC"
	local title_str = Language.LongZhu.NowStr

	if self.show_data.is_lock then
		title_new_color = COLOR3B.GRAY
		desc_new_color = COLOR3B.GRAY
		title_str = Language.LongZhu.ActiveStr
	end

	self.node_list.now_title.text.text = ToColorStr(title_str, title_new_color)
	self.node_list.skill_dsc.text.text = body_text == "" and "" or ToColorStr(body_text, desc_new_color)
	local image_str = self.show_data.is_active_skill and "a3_ty_zsd2" or "a3_ty_zsd"
    self.node_list.skill_now_image.image:LoadSprite(ResPath.GetCommon(image_str))
	image_str = self.show_data.is_active_skill and "a3_ty_tips_10" or "a3_ty_tips_2"
	self.node_list.auto_bg.image:LoadSprite(ResPath.GetCommonPanel(image_str))
	image_str = self.show_data.is_active_skill and "a3_ty_tips_skill_cut3" or "a3_ty_tips_skill_cut2"
    self.node_list.auto_bg_raw_image.raw_image:LoadSprite(ResPath.GetRawImagesPNG(image_str))

	self.node_list.skill_active_root:CustomSetActive(self.show_data.is_active_skill)
	self.node_list.skill_not_active_root:CustomSetActive(not self.show_data.is_active_skill)
	self.node_list.skill_not_active_txt.text.text = self.show_data.passive_str or Language.Common.BeiDong
	
	local skill_desc_list_height = 0
	-- 下一级新表现
	for i, next_desc_cell in ipairs(self.next_desc_list) do
		if next_data[i] ~= nil then
			next_desc_cell:SetVisible(true)
			next_desc_cell:SetData(next_data[i])
			if skill_index and skill_index > i then
				-- 加上Spacing
				skill_desc_list_height = skill_desc_list_height + 2
				skill_desc_list_height = skill_desc_list_height + next_desc_cell:GetHeight()
			end
		else
			next_desc_cell:SetVisible(false)
		end
	end
	-- 加上Top
	if skill_desc_list_height > 0 then
		skill_desc_list_height = skill_desc_list_height + 4
	end


	self.node_list.limit_tip:CustomSetActive(self.show_data.limit_tip_str ~= nil or self.show_data.is_lock)
	if self.show_data.is_lock ~= nil and self.show_data.is_lock then
		-- 限制提示文本
		self.node_list.limit_tip:CustomSetActive(true)
		self.node_list.limit_tip.text.text = Language.Common.NoActivate
	elseif self.show_data.limit_tip_str ~= nil then
		self.node_list.limit_tip.text.text = self.show_data.limit_tip_str
	end

	-- 展示下一级升级信息
	local item_id, uplevel_item_num
	if self.show_data.is_up_operate then
		if self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_SKILL then
			item_id = self.show_data.skill_id
			uplevel_item_num = 1
		elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.CUI_ZHUO_SKILL then
			item_id = self.show_data.item_id
			uplevel_item_num = self.show_data.item_num
		elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_MO then
			item_id = next_skill_cfg and next_skill_cfg.item or 0
			uplevel_item_num = next_skill_cfg and next_skill_cfg.stuff_num or 0
		else
			item_id = next_skill_cfg and (next_skill_cfg.uplevel_item_id or next_skill_cfg.item_id) or 0
			uplevel_item_num = next_skill_cfg and (next_skill_cfg.uplevel_item_num or next_skill_cfg.num) or 0
		end

		if item_id == 0 or uplevel_item_num == 0 then
			self.node_list.bottomPanel:CustomSetActive(false)
			return
		end

		if not self.stuff_item then
			self.stuff_item = ItemCell.New(self.node_list.phItemCell)
		end

		self.stuff_item:SetData({item_id = item_id})
		local bag_have_item_num = ItemWGData.Instance:GetItemNumInBagById(item_id) or 0
		local color = bag_have_item_num >= uplevel_item_num and COLOR3B.GREEN or COLOR3B.RED
		local str = string.format("%s/%s", bag_have_item_num, uplevel_item_num)
		self.node_list.BottomText.text.text = ToColorStr(str, color)

		if bag_have_item_num >= uplevel_item_num then
			self.node_list.img_btn_remind:SetActive(true)
		else
			self.node_list.img_btn_remind:SetActive(false)
		end
	end

	return skill_desc_list_height
end

-- 设置是否是盘旋
-- show_bg_kunag ：是否是
function MountLingChongDisplayBox:SetSkillShowBgKuang()
	self.node_list["bg_kuang"]:CustomSetActive(self.show_data.show_bg_kunag ~= nil and self.show_data.show_bg_kunag)
end

-- 展示战斗力
function MountLingChongDisplayBox:ShowCapability()
	local cap = 0

	if self.show_data.skill_box_type == SKILL_BOX_TYPE.CUI_ZHUO_SKILL then
		local data_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.show_data.solt)
		local skill_level = (data_info.skill_level_list or {}) [self.show_data.seq]
				
		if skill_level then
			local skill_data_cfg = FightSoulWGData.Instance:GetCuiZhuoSkillLevelCfg(self.show_data.solt, self.show_data.seq, skill_level)

			if not IsEmptyTable(skill_data_cfg) then
				local _,capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(skill_data_cfg, "attr_id", "attr_value")
				cap = capability
			end
		end
	else
		if self.show_data.capability and self.show_data.capability > 0 then
			cap = self.show_data.capability
		end
	end

	self.node_list.capability_value.text.text = cap
	self.node_list.capability_part:SetActive(cap > 0 and (not self.show_data.is_active_skill))
end

-- 设置高度
function MountLingChongDisplayBox:ChangePanelHeight(skill_desc_list_height)
	local scroll_view = self.node_list["scroll_view"]
	local scroll_content = self.node_list["layout_skill_tip"]
	-- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(cur_desc.rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(scroll_content.rect)

	local max_center_height = 424
	local scroll_content_height = scroll_content.rect.sizeDelta.y
	local cur_desc_height = 0
	if self.node_list.skill_dsc.text.text ~= "" then
		cur_desc_height = self.node_list.skill_dsc.text.preferredHeight + 44
	end

	max_center_height = max_center_height - cur_desc_height

	local scroll_view_height = 0
	if scroll_content_height > max_center_height then
		scroll_view.layout_element.preferredHeight = max_center_height
		scroll_view.scroll_rect.vertical = true
		scroll_view_height = max_center_height
	else
		scroll_view.layout_element.preferredHeight = scroll_content_height
		scroll_view.scroll_rect.vertical = false
		scroll_view_height = scroll_content_height
	end

	if skill_desc_list_height then
		local percent = 0
		local scroll_max = scroll_content_height - scroll_view_height
		if skill_desc_list_height > scroll_max then
			percent = 1
		else
			percent = skill_desc_list_height / scroll_max 
		end
		scroll_view.scroll_rect.verticalNormalizedPosition = 1 - percent
	else
		scroll_view.scroll_rect.verticalNormalizedPosition = 1
	end
	-- scroll_view.scroll_rect.verticalNormalizedPosition = 1
end

-----------------------------------------------------------------------------------------------------------------------------
--技能升级按钮
function MountLingChongDisplayBox:OnClickUpSkillLevel()
	local skill_level, next_skill_cfg
	local item_id = 0
	local uplevel_item_num = 0

	if self.show_data.skill_box_type == SKILL_BOX_TYPE.QI_CHONG_SKILL then
		skill_level = NewAppearanceWGData.Instance:GetBaseQiChongSkillLevel(self.show_data.type, self.show_data.skill_id)
		next_skill_cfg = NewAppearanceWGData.Instance:GetBaseQiChongSkillCfg(self.show_data.type, self.show_data.skill_id, skill_level + 1)
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_PASSSKILL then
		skill_level = TianShenWGData.Instance:GetTianShenPassiveSkill(0, self.show_data.skill_id)
		next_skill_cfg = TianShenWGData.Instance:GetSpecialImagePasvSkillLevelCfg(-1, self.show_data.skill_id, skill_level + 1)
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_SKILL then
		local skill_info = TianShenWGData.Instance:GetTianShensMainkill(self.show_data.tianshen_index, self.show_data.skill_id)
		skill_level = skill_info and skill_info.level or 1
		next_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(self.show_data.skill_id, skill_level + 1)
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.CUI_ZHUO_SKILL then
		local data_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.show_data.solt)
		skill_level = (data_info.skill_level_list or {}) [self.show_data.seq]
		next_skill_cfg = FightSoulWGData.Instance:GetCuiZhuoSkillLevelCfg(self.show_data.solt, self.show_data.seq, skill_level + 1)
		item_id = self.show_data.item_id
		uplevel_item_num = self.show_data.item_num
	elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_MO then
		skill_level = self.show_data.skill_lv
		next_skill_cfg = TianShenHuamoWGData.Instance:GetSkillLevelCfgById(self.show_data.tianshen_id, skill_level + 1)
		item_id = next_skill_cfg.item
		uplevel_item_num = next_skill_cfg.stuff_num
	else
		skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(self.show_data.type, self.show_data.skill_id)
		next_skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(self.show_data.type, self.show_data.skill_id, skill_level + 1)
	end

	if item_id == 0 and uplevel_item_num == 0 then
		local skill_cfg = TianShenWGData.Instance:GetSpecialSkill(self.show_data.skill_id, skill_level)
		if skill_cfg then
			item_id = skill_cfg.item_id
			uplevel_item_num = skill_cfg.item_num
		elseif next_skill_cfg then
			item_id = next_skill_cfg.uplevel_item_id or next_skill_cfg.item_id
			uplevel_item_num = next_skill_cfg.uplevel_item_num or next_skill_cfg.num
		end
	end

	local bag_have_item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	local next_uplevel_item_num = uplevel_item_num
	--预留传闻类型
	if next_skill_cfg then
		if bag_have_item_num >= next_uplevel_item_num then
			if self.show_data.skill_box_type == SKILL_BOX_TYPE.QI_CHONG_SKILL then
				if self.show_data.type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
					NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.UP_SKILL, self.show_data.skill_id)
				elseif self.show_data.type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
					NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.SKILL_UP_LEVEL, self.show_data.skill_id)
				end
			elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_PASSSKILL then
				TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type17, -1, self.show_data.skill_id)
			elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_SKILL then
				TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type18, self.show_data.tianshen_index, self.show_data.skill_id)
			elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.CUI_ZHUO_SKILL then
				FightSoulWGCtrl.Instance:SendFightSoulCuiZhuoOperate(SIXIANG_QUENCH_OPERATE_TYPE.SKILL_UPLEVEL, self.show_data.solt, self.show_data.seq)
			elseif self.show_data.skill_box_type == SKILL_BOX_TYPE.TIANSHEN_MO then
				TianShenHuamoWGCtrl.Instance:CSTianShenRuMoSkillUp(self.show_data.tianshen_id, self.show_data.tianshen_index, self.show_data.solt - 1)
			else
				NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.UPSKILL, self.show_data.type, self.show_data.skill_id)
			end
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
			TipWGCtrl.Instance:CloseOtherView(BindTool.Bind(self.Close, self))
		end
	end
end

----------------------------------------------------技能描述-------------------------------------------------
--技能描述对象
SkillNextDescRender = SkillNextDescRender or BaseClass(BaseRender)
function SkillNextDescRender:OnFlush()
	if not self.data then
		return
	end
	
	local title_new_color = self.data.is_active_skill and "#FFF8BB" or "#CBFBFD"
	local desc_new_color = self.data.is_active_skill and "#FFE58C" or "#8CB5CC"
	local image_str = self.data.is_active_skill and "a3_ty_zsd2" or "a3_ty_zsd"
    self.node_list.title_image.image:LoadSprite(ResPath.GetCommon(image_str))
	local title_color = self.data.is_unlock and title_new_color or COLOR3B.GRAY 
	local desc_color = self.data.is_unlock and desc_new_color or COLOR3B.GRAY
	local unlock_str = self.data.is_unlock and "" or ToColorStr(self.data.unlock_desc, COLOR3B.RED)
	local title_format = "%s%s"
	self.node_list.title.text.text = string.format(title_format, ToColorStr(self.data.title, title_color), unlock_str) 
	self.node_list.desc.text.text = ToColorStr(self.data.desc, desc_color)
end

function SkillNextDescRender:GetHeight()
	local height = 30
	height = self.node_list.desc.text.preferredHeight + 30
	return height
end