AppearanceGetNew = AppearanceGetNew or BaseClass(SafeBaseView)
--静止时间
local STATIC_TIME = 10 
local MAX_TIANSHEN_ZHUDONG_SKILL = 4			--天神主动技能最大个数
local MAX_TIANSHEN_BEIDONG_SKILL = 6			--天神被动技能最大个数
local AUTO_CLOSE_TIME = 3
--是否为图片
local IS_IMAGES = {    
	[ROLE_APPE_TYPE.PHOTO] = true,
	[ROLE_APPE_TYPE.BUBBLE] = true,
}

local RIGHT_PART_TYPE = {
	ONLY_ATTR_PART 	= 0,
	TIANSHEN_PART 	= 1,
	BEASTS_PART 	= 2,
	NUQI_PART 		= 3,
	SINGLE_SKILL	= 4,
	THUNDER_PART    = 5, 
	ESOTERICA       = 6,
}

--模型播放动作
local model_action_tab =
{
	[ROLE_APPE_TYPE.MOUNT] = RoleModel.PlayMountAction,
	[ROLE_APPE_TYPE.MOUNT_NORMAL] = RoleModel.PlayMountAction,
	[ROLE_APPE_TYPE.LINGCHONG] = RoleModel.PlaySoulAction,
	[ROLE_APPE_TYPE.LINGCHONG_NORMAL] = RoleModel.PlaySoulAction,
	[ROLE_APPE_TYPE.BABY] = RoleModel.PlaySoulAction,
	[ROLE_APPE_TYPE.WING] = RoleModel.PlayWingAction,
	[ROLE_APPE_TYPE.WING_NORMAL] = RoleModel.PlayWingAction,
	[ROLE_APPE_TYPE.BIANSHEN] = RoleModel.PlayMonsterAction,
	[ROLE_APPE_TYPE.IMP] = RoleModel.PlayMonsterAction,
	[ROLE_APPE_TYPE.BEAST] = RoleModel.PlaySoulAction,
}

function AppearanceGetNew:__init()
	self.view_layer = UiLayer.PopTop
	self.view_style = ViewStyle.Half
	self.is_need_depth = true
	self.can_do_fade = false
	self.is_auto_task = false
	self.info = nil
	self.full_screen = true

	self:SetMaskBg(true, true, nil, BindTool.Bind1(self.DoCloseTween, self))
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:AddViewResource(0, "uis/view/appearance_ui_prefab", "layout_get_new_view")
end

function AppearanceGetNew:ReleaseCallBack()
	if self.show_head_cell then
		self.show_head_cell:DeleteMe()
		self.show_head_cell = nil
	end

	if self.only_attr_list then
		for k, v in pairs(self.only_attr_list) do
			v:DeleteMe()
		end
		self.only_attr_list = nil
	end

	if self.tianshen_attr_list then
		for k, v in pairs(self.tianshen_attr_list) do
			v:DeleteMe()
		end
		self.tianshen_attr_list = nil
	end

	if self.ts_zhudong_skill_list then
		for i, v in ipairs(self.ts_zhudong_skill_list) do
			v:DeleteMe()
		end
		self.ts_zhudong_skill_list = nil
	end

	if self.ts_beidong_skill_list then
		for i, v in ipairs(self.ts_beidong_skill_list) do
			v:DeleteMe()
		end
		self.ts_beidong_skill_list = nil
	end

	if self.beast_attr_list then
		for k, v in pairs(self.beast_attr_list) do
			v:DeleteMe()
		end
		self.beast_attr_list = nil
	end

	if self.beast_skill_llist and #self.beast_skill_llist > 0 then
		for _, skill_cell in ipairs(self.beast_skill_llist) do
			skill_cell:DeleteMe()
			skill_cell = nil
		end

		self.beast_skill_llist = nil
	end

	if self.nuqi_skill_list and #self.nuqi_skill_list > 0 then
		for _, nuqi_skill_cell in ipairs(self.nuqi_skill_list) do
			nuqi_skill_cell:DeleteMe()
			nuqi_skill_cell = nil
		end

		self.nuqi_skill_list = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.xiuwei_model then
		self.xiuwei_model:DeleteMe()
		self.xiuwei_model = nil
	end

	if self.fz_display then
		self.fz_display:DeleteMe()
		self.fz_display = nil
	end

	if self.thunder_attr_list then
		for k, v in pairs(self.thunder_attr_list) do
			v:DeleteMe()
		end
		self.thunder_attr_list = nil
	end

	if self.get_guide_ui_event then
        FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.AppearanceGetNew, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
    end

	self.info = nil
	self.show_bubble_cell = nil
	self.show_star_root = nil

	self:StopCountDown()
	self:ClearCloseTimer()
end

function AppearanceGetNew:LoadCallBack()
	-- 模型展示
	if not self.model_display then
		self.model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.model_display:SetRenderTexUI3DModel(display_data)
		-- self.model_display:SetUI3DModel(self.node_list.ph_display.transform, self.node_list.ph_display.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	-- 修为变身模型
	if not self.xiuwei_model then
		self.xiuwei_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["xiuwei_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.xiuwei_model:SetRenderTexUI3DModel(display_data)
		-- self.xiuwei_model:SetUI3DModel(self.node_list.xiuwei_model.transform, self.node_list.xiuwei_model.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	-- 法阵展示
	if not self.fz_display then
		self.fz_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["fz_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.fz_display:SetRenderTexUI3DModel(display_data)
		-- self.fz_display:SetUI3DModel(self.node_list.fz_model.transform, self.node_list.fz_model.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	-- 列表属性信息
	if self.only_attr_list == nil then
		self.only_attr_list = {}
		local node_num = self.node_list.only_attr_list.transform.childCount
		for i = 1, node_num do
			self.only_attr_list[i] = CommonAttrRender.New(self.node_list.only_attr_list:FindObj("attr_" .. i))
			self.only_attr_list[i]:SetAttrNameNeedSpace(true)
		end
	end

	-- 天神属性信息
	if self.tianshen_attr_list == nil then
		self.tianshen_attr_list = {}
		local node_num = self.node_list.tianshen_attr_list.transform.childCount
		for i = 1, node_num do
			self.tianshen_attr_list[i] = CommonAttrRender.New(self.node_list.tianshen_attr_list:FindObj("attr_" .. i))
		end
	end

	-- 天神技能
	if not self.ts_zhudong_skill_list then
		self.ts_zhudong_skill_list = {}
		for i = 1, MAX_TIANSHEN_ZHUDONG_SKILL do
			local root = self.node_list.tianshen_active_skill_list:FindObj(string.format("zhudong_skill_bg_%d", i))
			self.ts_zhudong_skill_list[i] = AppearanceTianShenSkillRender.New(root)
		end
	end

	if not self.ts_beidong_skill_list then
		self.ts_beidong_skill_list = {}
		for i = 1, MAX_TIANSHEN_BEIDONG_SKILL do
			local root = self.node_list.tianshen_passive_skill_list:FindObj(string.format("beidong_skill_bg_%d", i))
			self.ts_beidong_skill_list[i] = AppearanceTianShenSkillRender.New(root)
		end
	end

	-- 幻兽资质属性
	if self.beast_skill_llist == nil then
		self.beast_skill_llist = {}
		for i = 1, 3 do
			local attr_obj = self.node_list.beast_other_skill_llist:FindObj(string.format("skill_render_0%d", i))
			if attr_obj then
				local cell = BeastsBookSkillItemRender.New(attr_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind1(self.ShowBeastSkill, self))
				self.beast_skill_llist[i] = cell
			end
		end
	end

	-- 幻兽属性
	if self.beast_attr_list == nil then
		self.beast_attr_list = {}
		local node_num = self.node_list.beast_attr_list.transform.childCount
		for i = 1, node_num do
			self.beast_attr_list[i] = CommonAttrRender.New(self.node_list.beast_attr_list:FindObj("attr_" .. i))
		end
	end

	-- 星级展示
	if not self.show_star_root then
        self.show_star_root = {}
        
        for i = 1, 5 do
			self.show_star_root[i] = self.node_list[string.format("star_0%d", i)]
        end
    end

	-- 怒气技能
	if not self.nuqi_skill_list then
		self.nuqi_skill_list = {}
		for i = 1, 6 do
			local nuqi_skill_render_obj = self.node_list[string.format("nuqi_skill_render_%d", i)]
			self.nuqi_skill_list[i] = AppearanceNuQiSkillRender.New(nuqi_skill_render_obj)
		end
	end

	-- 雷法属性信息
	if self.thunder_attr_list == nil then
		self.thunder_attr_list = {}
		local node_num = self.node_list.thunder_attr_list.transform.childCount
		for i = 1, node_num do
			self.thunder_attr_list[i] = CommonAttrRender.New(self.node_list.thunder_attr_list:FindObj("attr_" .. i))
		end
	end

		
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.DoCloseTween, self))
	XUI.AddClickEventListener(self.node_list.btn_save, BindTool.Bind(self.MagicallyChange, self))
	XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.DoCloseTween, self))
	XUI.AddClickEventListener(self.node_list.btn_tianshen_save, BindTool.Bind(self.MagicallyChange, self))

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.AppearanceGetNew, self.get_guide_ui_event)
end

function AppearanceGetNew:OpenCallBack()
	self.is_auto_task = TaskGuide.Instance.can_auto_all_task
	TaskGuide.Instance:CanAutoAllTask(false) --停止接受任务
end

function AppearanceGetNew:CloseCallBack()
	if self.is_auto_task or TaskGuide.Instance:NoviceCheckTask() then
		if Scene.Instance:GetSceneType() == SceneType.Common then
			GuajiWGCtrl.Instance:StopGuaji()
		end

		TaskGuide.Instance:CanAutoAllTask(true) --是否自动做任务
	end

	TipWGCtrl.Instance:PlayAniTab(GuideModuleName.AppearanceGetNew, "sub")
	----[[ 处理任务弹出恭喜获得后， 弹功能开启
	FunOpen.Instance:DoShowOpenFunFlyView()
	AppearanceWGData.Instance:SetOpenGetNewViewState(false)
	--]]

	-- 御兽特殊处理一下
	local body_show_type = self.param and self.param.type or 0
	if body_show_type == ROLE_APPE_TYPE.BEAST then -- 幻兽需要弹出新技能获得
		local beast_data = self.param.beast_data
		if beast_data and beast_data.server_data and beast_data.pop_param == 1 then
			local server_data = beast_data.server_data
			local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
			if beast_cfg then
				TipWGCtrl.Instance:ShowGetNewSkillView(beast_cfg.skill_id, XINMO_FUBEN_SKILL_POS.BEAST_SKILL)
				GlobalEventSystem:Fire(MainUIEventType.ROLE_BEASTS_SKILL_LIST, true)
			end
		end
	end

	--系统预告激活后需要外观获得界面关闭再弹对应系统界面
	if SystemForceWGCtrl.Instance:IsPrepareOpenSystemForceSeqView() then
		SystemForceWGCtrl.Instance:OpenSystemForceSeqView()
	end
end

--模型路径，模型id，模型名字
--@attr.name 模型的名字
--@attr.model_type 模型的类型：坐骑，羽翼，法宝，灵宠等
--@attr.content  该模型说的话
function AppearanceGetNew:SetContent(info)
	if nil == info or IsEmptyTable(info) then
		return
	end

	self.info = info
	local param = AppearanceWGData.Instance:GetNewAppearanceCfg(info.appe_type, info.appe_image_id, info.index_param)
	self.param = param
	self.auto_cutdown = info.auto_cutdown == nil
	if self:IsOpen() then
		self:Flush()
	end
end

-- 获取数据
function AppearanceGetNew:GetInfo()
	return self.info
end

-- 关闭动画
function AppearanceGetNew:DoCloseTween()
	self.info = nil
	if self:OpenNextAppeInfo() then
		return
	end

	self:Close()
end

--看看是否还有下一个形象可以展示
function AppearanceGetNew:OpenNextAppeInfo()
	local list = AppearanceWGCtrl.Instance:GetNewAppeInfoList()
	if IsEmptyTable(list) then
		return false
	end

	local can_open = AppearanceWGCtrl.Instance:CanOpenGetNewView()
	AppearanceWGCtrl.Instance:OpenGetNewView()
	if can_open then
		return true
	end

	return false
end

-- 展示御兽技能
function AppearanceGetNew:ShowBeastSkill(skill_cell)
	if skill_cell == nil or skill_cell.data == nil then
		return
	end

	local data = skill_cell.data
	local show_data = {
		icon = data.skill_icon,
		top_text = data.skill_name,
		body_text = data.desc,
		x = 0,
		y = -120,
		set_pos = true,
		hide_next = true,
		is_active_skill = (not data.is_normal) and (not data.is_zhuan_shu),
		skill_level = data.skill_level or 0,
		passive_str = data.passive_str,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function AppearanceGetNew:OnFlush()
	self:StartCountDown()
	self:FlushDisplayModel()
	self:FlushDisplayMessage()
end

-- 刷新模型
function AppearanceGetNew:FlushDisplayModel()
	if self.param == nil or self.model_display == nil then
		return
	end

	local btn_text_str = Language.NewAppearance.HuanHua2
	local is_auto_operate = true
	local is_need_show_auto_close = false
	local body_show_type = self.param.type
	self.model_display:RemoveAllModel()
	self.xiuwei_model:RemoveAllModel()
	self.fz_display:RemoveAllModel()

	self.node_list.fz_model:CustomSetActive(body_show_type == ROLE_APPE_TYPE.ZHIZUN)
	self.node_list.ph_display:CustomSetActive(body_show_type ~= ROLE_APPE_TYPE.ZHIZUN and body_show_type ~= ROLE_APPE_TYPE.BIANSHEN_XIUWEI)
	self.node_list.xiuwei_model:CustomSetActive(body_show_type == ROLE_APPE_TYPE.BIANSHEN_XIUWEI)
	self.node_list.esoterica_center_root:CustomSetActive(body_show_type == ROLE_APPE_TYPE.ESOTERICA)

	self:SetDisplayShow(not IS_IMAGES[self.param.type])

	if body_show_type == ROLE_APPE_TYPE.FASHION then --时装
		self:UpdateFashion()
	elseif body_show_type == ROLE_APPE_TYPE.SHENBING then --武器/神兵  -- or self.param.special_type and self.param.special_type == 2
		self:UpdateWeapon()
	elseif body_show_type == ROLE_APPE_TYPE.FOOT then --足迹
		self:ShowFootModel()
	elseif body_show_type == ROLE_APPE_TYPE.JIANZHEN then --剑阵
		self:SetMainAsset(self.param.appe_image_id)
	elseif body_show_type == ROLE_APPE_TYPE.PHOTO then --相框
		self:ShowBubbleAndPhotoFrame(true, false)
		self:ShowPhotoFrame()
	elseif body_show_type == ROLE_APPE_TYPE.BUBBLE then --气泡
		self:ShowBubbleAndPhotoFrame(false, true)
		self:ShowBubble()
	elseif body_show_type == ROLE_APPE_TYPE.FACE then --装饰 脸
		self:ShowCurRoleModel()
		self:ShowMaskFashion()
	elseif body_show_type == ROLE_APPE_TYPE.YAO then --装饰 腰
		self:ShowCurRoleModel()
		self:ShowYaoFashion()
	elseif body_show_type == ROLE_APPE_TYPE.WEI then --装饰 尾
		self:ShowCurRoleModel()
		self:ShowWeiBaFashion()
	elseif body_show_type == ROLE_APPE_TYPE.HAND then --装饰 手
		self:ShowCurRoleModel()
		self:ShowHandgFashion()
	elseif body_show_type == ROLE_APPE_TYPE.HALO then --装饰 魂息
		self:ShowCurRoleModel()
		self:ShowHaloFashion()
	elseif body_show_type == ROLE_APPE_TYPE.RELIC_TYPE and self.param.path then -- 圣器
		local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(self.param.appe_image_id)
		self:SetMainAsset(relic_cfg.model_id)
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.GOD_BODY and self.param.path then -- 神体
		self:ShowGodBody()
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.CANGMING then -- 沧溟
		self:ShowCangMing()
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.WU_HUN_ZHEN_SHEN and self.param.path then -- 武魂真身
		self:SetMainAsset(self.param.appe_image_id)
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.NEW_FIGHT_MOUNT and self.param.path then -- 新战斗坐骑
		local cfg = NewFightMountWGData.Instance:GetMountTypeCfgBySeq(self.param.appe_image_id)
		local appe_image_id = cfg and cfg.appe_image_id or 0
		self:SetMainAsset(appe_image_id)
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.BEAST and self.param.path then -- 御兽
		self:SetMainAsset(self.param.appe_image_id)
		btn_text_str = Language.Welfare.LingQu
		is_need_show_auto_close = true
	elseif body_show_type == ROLE_APPE_TYPE.GOD_OR_DEMON then --一念神魔
		self:ShowGodOrDemon()
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.ZHIZUN then
		self:ShowZhiZun()
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.BABY then
		self:ShowBaby()
		btn_text_str = Language.Welfare.LingQu
	elseif body_show_type == ROLE_APPE_TYPE.BIANSHEN_XIUWEI then
		self:ShowBianShenXiuWei()
	elseif body_show_type == ROLE_APPE_TYPE.THUNDER_MANA then
		self:ShowCurRoleModel()
		self:ShowHaloFashion()
		btn_text_str = Language.ThunderMana.BtnNameStr[6]
	elseif body_show_type == ROLE_APPE_TYPE.ESOTERICA then
		self:ShowEsoterica()
		btn_text_str = Language.Welfare.LingQu
	elseif self.param.path then
		if body_show_type == ROLE_APPE_TYPE.BIANSHEN then
			local attr_cfg = TianShenWGData.Instance:GetImageModelByAppeId(self.param.appe_image_id)
			self.model_display:SetTianShenModel(self.param.appe_image_id, attr_cfg.index, true, attr_cfg.show_audio, SceneObjAnimator.Rest)
			btn_text_str = Language.Welfare.LingQu
		else
			self:SetMainAsset(self.param.appe_image_id)
			self:SetWeaponModel()
		end
	end

	--	修改恭喜获得新形象不同表现
	local is_show_get_btn = btn_text_str == Language.NewAppearance.HuanHua2 
	is_auto_operate = TaskGuide.Instance:IsAutoCloseGetNewAppViewLevel() and is_auto_operate
	self:SetAutoCloseViewOperate(is_auto_operate, btn_text_str, is_show_get_btn or is_need_show_auto_close)
	self.node_list["btn_get"]:SetActive(is_show_get_btn)

	self.node_list.btn_text.text.text = btn_text_str
	self.node_list.text_btn_tianshen_save.text.text = btn_text_str
	self:SetDisPlayContent()
end

--设置模型的显示和隐藏
function AppearanceGetNew:SetDisplayShow(enable)
	self.node_list.display_image.gameObject:SetActive(not enable)
	self.node_list.ph_display.gameObject:SetActive(enable)
end

--时装
function AppearanceGetNew:UpdateFashion()
	local fashion_id, _, _ = AppearanceWGData.GetFashionBodyResIdByResViewId(self.param.appe_image_id)
	local _, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
	self:ShowCurRoleModel(fashion_id, true)
	self:SetWeaponAsset(weapon_res_id)
end

--设置角色
function AppearanceGetNew:ShowCurRoleModel(res_id, is_need_weapon, anim_name)
	local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()

	if not res_id then
		res_id = role_res_id
	end

	local extra_role_model_data = {
		animation_name = anim_name,
    }
	self.model_display:SetRoleResid(res_id, nil, extra_role_model_data)
	if is_need_weapon then
		self.model_display:SetWeaponResid(weapon_res_id)
	end
end

--设置武器神兵
function AppearanceGetNew:UpdateWeapon()
	local weapon_res_id, weapon_res_id_2 = RoleWGData.GetFashionWeaponId(nil, nil, self.param.appe_image_id)
	local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
	self.model_display:SetMainAsset(bundle, asset)
end

--设置足迹
function AppearanceGetNew:ShowFootModel()
	self:ShowCurRoleModel(nil, nil, SceneObjAnimator.Move)
	self.model_display:SetFootTrailModel(self.param.appe_image_id)
	self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
	self.model_display:PlayRoleAction(SceneObjAnimator.Move)
end

-- 相框气泡
function AppearanceGetNew:ShowBubbleAndPhotoFrame(frame_show, bubble_show)
	self.node_list.display_image.image.enabled = false
	self.node_list.photo_frame_pos:SetActive(frame_show)
	self.node_list.bubble_pos:SetActive(bubble_show)
end

-- 展示相框
function AppearanceGetNew:ShowPhotoFrame()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.param.cfg.stuff_id)
	if item_cfg then
		if not self.show_head_cell then
			self.show_head_cell = BaseHeadCell.New(self.node_list.photo_frame_pos)
		end

		local photoframe_id = item_cfg.param2
		local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(SHIZHUANG_TYPE.PHOTOFRAME, photoframe_id)
		photoframe_id = cfg and cfg.resouce or photoframe_id
		local data = { fashion_photoframe = photoframe_id }
		self.show_head_cell:SetImgBg(true)
		self.show_head_cell:SetData(data)
		self.show_head_cell:SetBgActive(false)
	end
end

--展示气泡
function AppearanceGetNew:ShowBubble()
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(self.param.cfg.stuff_id)
	if not fashion_cfg or IsEmptyTable(fashion_cfg) then return end

	if not IsNil(self.node_list.bubble_pos.transform) then
		self.show_bubble_cell = self.show_bubble_cell or AllocAsyncLoader(self, "get_new_bubble_cell")
		self.show_bubble_cell:SetIsUseObjPool(true)
		self.show_bubble_cell:SetParent(self.node_list.bubble_pos.transform)

		local bubble_id = fashion_cfg.resouce or 0
		local asset, bundle = ResPath.ChatBigBubbleBig(bubble_id)
		self.show_bubble_cell:Load(asset, bundle)
	end
end

--设置脸 + 人
function AppearanceGetNew:ShowMaskFashion()
	local other_asset = self.param.other_asset
	if not other_asset or IsEmptyTable(other_asset) then return end
	-- 底层字段限制了0.05s can_get_main_point
	GlobalTimerQuest:AddDelayTimer(function()
		if other_asset.mask_res_id then
			self.model_display:SetMaskResid(other_asset.mask_res_id)
		end
	end, 0.1)
end

--设置腰 + 人
function AppearanceGetNew:ShowYaoFashion()
	local other_asset = self.param.other_asset
	if not other_asset or IsEmptyTable(other_asset) then return end
	-- 底层字段限制了0.05s can_get_main_point
	GlobalTimerQuest:AddDelayTimer(function()
		if other_asset.belt_res_id then
			self.model_display:SetWaistResid(other_asset.belt_res_id)
			-- self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
		end
	end, 0.1)
end

--设置尾巴 + 人
function AppearanceGetNew:ShowWeiBaFashion()
	local other_asset = self.param.other_asset
	if not other_asset or IsEmptyTable(other_asset) then return end
	self.model_display:SetRotation(MODEL_ROTATION_TYPE.WEIBA) --先旋转
	-- 底层字段限制了0.05s can_get_main_point
	GlobalTimerQuest:AddDelayTimer(function()
		if other_asset.weiba_res_id then
			self.model_display:SetTailResid(other_asset.weiba_res_id)
		end
	end, 0.1)
end

--设置手 + 人
function AppearanceGetNew:ShowHandgFashion()
	local other_asset = self.param.other_asset
	if not other_asset or IsEmptyTable(other_asset) then return end
	-- 底层字段限制了0.05s can_get_main_point
	GlobalTimerQuest:AddDelayTimer(function()
		if other_asset.shouhuan_res_id then
			self.model_display:SetShouHuanResid(other_asset.shouhuan_res_id)
			-- self.model_display:SetRotation(Vector3(0, -160, 0))
		end
	end, 0.1)
end

--设置魂息 + 人
function AppearanceGetNew:ShowHaloFashion()
	-- 底层字段限制了0.05s can_get_main_point
	GlobalTimerQuest:AddDelayTimer(function()
		if self.param.appe_image_id then
			self.model_display:SetHaloResid(self.param.appe_image_id)
		end

		local _, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
		self:SetWeaponAsset(weapon_res_id)
	end, 0.1)
end

--设置神体
function AppearanceGetNew:ShowGodBody()
	local bundle, asset = self.param.path(self.param.appe_image_id, RoleWGData.Instance:GetRoleSex())
	self.model_display:SetMainAsset(bundle, asset)
	self.model_display:FixToOrthographic(self.root_node_transform)
	self.model_display:PlayJianZhenAction()
end

--设置沧溟
function AppearanceGetNew:ShowCangMing()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.model_display:SetModelResInfo(role_vo)
	self.model_display:SetSkillHaloResid(self.param.appe_image_id)
	self.model_display:FixToOrthographic(self.root_node_transform)
	self.node_list.ph_display.transform:SetLocalScale(0.6, 0.6, 0.6)
end

--设置一念神魔
function AppearanceGetNew:ShowGodOrDemon()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local special_status_table = { ignore_god_or_demon = true }
	self.model_display:SetModelResInfo(role_vo, special_status_table)
	self.model_display:SetGodOrDemonResid(self.param.appe_image_id)
end

--设置至尊领域
function AppearanceGetNew:ShowZhiZun()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.model_display:SetModelResInfo(role_vo)
	self.model_display:FixToOrthographic(self.root_node_transform)
	self.model_display:SetRTAdjustmentRootLocalScale(0.6)
	local bundle, asset = ResPath.GetSkillFaZhenModel(self.param.zhizun_type_index)
	self.fz_display:SetMainAsset(bundle, asset)
end

--设置宝宝
function AppearanceGetNew:ShowBaby()
	local bundle, asset = ResPath.GetHaiZiModel(self.param.appe_image_id)
	self.model_display:SetMainAsset(bundle, asset, function()
		self.model_display:PlaySoulAction()
	end)
end

-- 设置修为变身形象
function AppearanceGetNew:ShowBianShenXiuWei()
	local show_nuqi_type = self.param.appe_image_id
	local show_nuqi_lv = self.param.index_param

	local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(show_nuqi_type, show_nuqi_lv)
	if cfg ~= nil then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true, ignore_wing = true}
		self.xiuwei_model:SetModelResInfo(role_vo, special_status_table)
		self.xiuwei_model:SetAngerImage(show_nuqi_type, true, cfg.default_body, cfg.default_face, cfg.default_hair)
	end

	local image_str = show_nuqi_lv > 5 and "a3_gxhd_z_5" or "a3_gxhd_z_1"
	local bundle, asset = ResPath.GetRawImagesPNG(image_str)
	self.node_list.get_new_title.raw_image:LoadSprite(bundle, asset)
end

--播放动画
function AppearanceGetNew:SetDisPlayContent()
	local model_type = self.param.type
	if model_action_tab[model_type] then
		local fun = model_action_tab[model_type]
		fun(self.model_display)
	end
end

--设置武器信息
function AppearanceGetNew:SetWeaponAsset(weapon_res_id)
	self.model_display:SetWeaponResid(weapon_res_id)
end

-- 设置秘籍
function AppearanceGetNew:ShowEsoterica()
	local esoterica_cfg = self.param.esoterica_cfg

	if not IsEmptyTable(esoterica_cfg) then
		local bundle, asset = ResPath.GetRawImagesPNG("a3_xf_rwlh" .. esoterica_cfg.img_id)
		self.node_list.esoterica_img.raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["esoterica_img"].raw_image:SetNativeSize()
		end)
	
		bundle, asset = ResPath.GetRawImagesPNG("a3_xf_wb" .. esoterica_cfg.skill_img)
		self.node_list.esoterica_skill_img.raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["esoterica_skill_img"].raw_image:SetNativeSize()
		end)

		bundle, asset = ResPath.GetA2Effect(esoterica_cfg.ui_effect_asset)
		self.node_list.esoterica_img_effect:ChangeAsset(bundle, asset)

		if esoterica_cfg.skill_label and esoterica_cfg.skill_label ~= "" then
			self.node_list.skill_label_panel:SetActive(true)
			self.node_list.esoterica_label_bg:SetActive(true)
			local split_list = string.split(esoterica_cfg.skill_label, "|")
			for i = 1, 3 do
				if split_list[i] then
					self.node_list["skill_label_" .. i]:SetActive(true)
					self.node_list["skill_label_text_" .. i].text.text =split_list[i]
				else
					self.node_list["skill_label_" .. i]:SetActive(false)
				end
			end
	
			if split_list[1] then
				self.node_list["esoterica_label"].text.text = split_list[1]
			end
		else
			self.node_list.esoterica_label_bg:SetActive(false)
			self.node_list.skill_label_panel:SetActive(false)
		end
	end
end

----------------------------------------------------------------------------------------
-- 刷新信息
function AppearanceGetNew:FlushDisplayMessage()
	local part_type = RIGHT_PART_TYPE.ONLY_ATTR_PART
	local body_show_type = self.param.type
	local btn_text_str = Language.NewAppearance.HuanHua
	local skill_cap_data
	local skill_icon_res_path = ResPath.GetSkillIconById
	self.node_list.obj_name.text.text = self.param.name or ""
	local skill_info_data = {}

	if body_show_type == ROLE_APPE_TYPE.MOUNT then -- 幻化坐骑
		local image_id = self.param.cfg.image_id
		local mount_image_skill = NewAppearanceWGData.Instance:GetSpecialMountSkillCfg(image_id, 0)
		if not IsEmptyTable(mount_image_skill) then
			table.insert(skill_info_data, mount_image_skill)
		end
	elseif body_show_type == ROLE_APPE_TYPE.MOUNT_NORMAL then -- 进阶坐骑
		local star_up_cfg = NewAppearanceWGData.Instance:GetMountBaseUpStarCfgByAppeId(self.param.appe_image_id, 1)
		if not IsEmptyTable(star_up_cfg) then
			local mount_skill = NewAppearanceWGData.Instance:GetMountBaseSkillCfgByStarLevel(star_up_cfg.star_level, 1)
			if not IsEmptyTable(mount_skill) then
				table.insert(skill_info_data, mount_skill)
			end
		end
	elseif body_show_type == ROLE_APPE_TYPE.WING then -- 羽翼
		local shizhuang_uplevel = self.param.grade_cfg
		if not IsEmptyTable(shizhuang_uplevel) and shizhuang_uplevel.skill_icon and shizhuang_uplevel.skill_icon > 0 then
			table.insert(skill_info_data, shizhuang_uplevel)
			skill_cap_data = NewAppearanceWGData.Instance:GetFashionSkillCfg(SHIZHUANG_TYPE.WING, shizhuang_uplevel.index, 1)
		end
	elseif body_show_type == ROLE_APPE_TYPE.BIANSHEN then -- 天神
		local tianshen_skill = self.param.cfg
		local bedong_skill = {}
		if not IsEmptyTable(tianshen_skill) then
			bedong_skill = TianShenWGData.Instance:GetBeSkillShowCfgList(tianshen_skill.index)
			table.insert(skill_info_data, tianshen_skill)
			if not IsEmptyTable(bedong_skill) then
				table.insert(skill_info_data, bedong_skill)
			end
		end

		part_type = RIGHT_PART_TYPE.TIANSHEN_PART
	elseif body_show_type == ROLE_APPE_TYPE.TIANSHENSHENQI then -- 天神神器
		local tianshen_sq_qh_cfg = self.param.cfg
		if tianshen_sq_qh_cfg and tianshen_sq_qh_cfg.index then
			local tianshen_sq_star_cfg = TianShenWGData.Instance:GetShenQiStarCfg(tianshen_sq_qh_cfg.index, 0)
			if tianshen_sq_star_cfg then
				local tianshen_sq_skill_cfg = TianShenWGData.Instance:GetShenQiSkillCfg(tianshen_sq_star_cfg.skill,
					tianshen_sq_star_cfg.skill_level)
				if tianshen_sq_skill_cfg then
					--设置天神神器技能描述
					table.insert(skill_info_data, tianshen_sq_skill_cfg)
					skill_icon_res_path = ResPath.GetSkillIcon
				end
			end
		end
	elseif body_show_type == ROLE_APPE_TYPE.RELIC_TYPE then -- 圣器
		local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(self.param.appe_image_id)
		if not IsEmptyTable(relic_cfg) then
			local relic_skill_info = {}
			relic_skill_info.skill_icon = relic_cfg.seal_skill_id
			relic_skill_info.skill_name = relic_cfg.seal_skill_name
			table.insert(skill_info_data, relic_skill_info)
		end
	elseif body_show_type == ROLE_APPE_TYPE.BIANSHEN_XIUWEI then
		local skill_list = CultivationWGData.Instance:GetActiveSkillListByType(self.param.appe_image_id)
		if not IsEmptyTable(skill_list) then
			for index, skill_id in ipairs(skill_list) do
				local temp_data = {}
				if index == 1 then
					temp_data.skill_icon = skill_id
					temp_data.skill_name = Language.NewAppearance.NuQiTypeSkillName[self.param.appe_image_id]
				else
					local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
					local xiuxian_cfg = SkillWGData.Instance:GetXiuXianSkillConfig(skill_id, 1)

					if skill_cfg and xiuxian_cfg then
						temp_data.skill_icon = skill_cfg.icon_resource
						temp_data.skill_name = xiuxian_cfg.skill_name
					end
				end

				if not IsEmptyTable(temp_data)  then
					table.insert(skill_info_data, temp_data)
				end
			end
		end

		part_type = RIGHT_PART_TYPE.NUQI_PART
	elseif body_show_type == ROLE_APPE_TYPE.BEAST then
		part_type = RIGHT_PART_TYPE.BEASTS_PART
	elseif body_show_type == ROLE_APPE_TYPE.THUNDER_MANA then
		part_type = RIGHT_PART_TYPE.THUNDER_PART
	elseif body_show_type == ROLE_APPE_TYPE.ESOTERICA then
		part_type = RIGHT_PART_TYPE.ESOTERICA
	end

	local is_show_skill = not IsEmptyTable(skill_info_data)
	if is_show_skill and part_type == RIGHT_PART_TYPE.ONLY_ATTR_PART then
		part_type = RIGHT_PART_TYPE.SINGLE_SKILL 
	end

	self:SetRightPartStatus(part_type)
	local capability = 0

	if part_type == RIGHT_PART_TYPE.SINGLE_SKILL then
		local data = skill_info_data[1]
		self.node_list.single_skill_name.text.text = data.skill_name
		XUI.SetSkillIcon(self.node_list.single_skill_bg, self.node_list.single_skill_icon, data.skill_icon)
		local cap_data = skill_cap_data or data
		local skill_cap_info = {
			attack_power = cap_data.attack_power,
			defence_power = cap_data.defence_power,
			capability_inc = cap_data.capability_inc,
		}

		local temp_list = AttributeMgr.GetAttributteByClass(data)
		if body_show_type == ROLE_APPE_TYPE.RELIC_TYPE then -- 圣器
			capability = HolyDarkWeaponWGData.Instance:GetRelicCapabilityBySeq(self.param.appe_image_id)
		else
			capability = AttributeMgr.GetCapability(temp_list, skill_cap_info)
		end
	elseif part_type == RIGHT_PART_TYPE.TIANSHEN_PART then
		capability = TianShenWGData.Instance:GetActivationZhanLi(skill_info_data[1].index)
		self:SetTianshenSkillContent(skill_info_data)
	elseif part_type == RIGHT_PART_TYPE.NUQI_PART then
		self:SetNuQiSkillContent(skill_info_data)
	elseif part_type == RIGHT_PART_TYPE.BEASTS_PART then
		self:SetBeastFlairMessage()
		local beast_data = self.param.beast_data
		capability = ControlBeastsWGData.Instance:GetBeastBestCapability(beast_data)
	elseif part_type == RIGHT_PART_TYPE.THUNDER_PART then
		capability = self:SetThunderManaMessage()
	elseif part_type == RIGHT_PART_TYPE.ONLY_ATTR_PART then
		if body_show_type == ROLE_APPE_TYPE.GOD_OR_DEMON or body_show_type == ROLE_APPE_TYPE.BEAST_SKIN then
			local attr_list = self.param.cap_cfg or {}
			for k, v in pairs(self.only_attr_list) do
				v:SetData(attr_list[k])
			end

			local base_attribute = AttributePool.AllocAttribute()
			if attr_list ~= nil then
				for key, value in pairs(attr_list) do
					if base_attribute[value.attr_str] ~= nil and value.attr_value > 0 then
						base_attribute[value.attr_str] = base_attribute[value.attr_str] + value.attr_value
					end
				end
			end

			capability = AttributeMgr.GetCapability(base_attribute)
		else
			local attr_list = EquipWGData.GetSortAttrListByCfg(self.param.cap_cfg)

			local role_appe_type = self.param.type
			local cfg = self.param.cfg or {}
			local image_id = cfg.index and cfg.index or 0
			image_id = image_id > 0 and image_id or (cfg.image_id or 0)
			local idx = ROLE_APPE_TYPE_2_IDX[role_appe_type]
			local per_attr_data = {}

			if image_id > 0 and nil ~= idx and nil ~= role_appe_type then
				if role_appe_type == ROLE_APPE_TYPE.LINGCHONG or role_appe_type == ROLE_APPE_TYPE.HUAKUN then
					per_attr_data = NewAppearanceWGData.Instance:GetSpecialQiChongPerAddAttr(idx, image_id, 1)
				else
					per_attr_data = NewAppearanceWGData.Instance:GetFashionPerAddAttr(idx, image_id, 1)
				end
			end

			local insert_key = -1
			for key, value in pairs(attr_list) do
				if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
					value.attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrName(value.attr_str, false, false), COLOR3B.GLOD)
					if insert_key < 0 then
						insert_key = key
					end
				end
			end
			local extra_cap = 0
			if not IsEmptyTable(per_attr_data) then
				if per_attr_data.cur_value then
					per_attr_data.attr_value = per_attr_data.cur_value or 0
				end
				if per_attr_data.attr_value > 0 then
					table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, per_attr_data)
					local add_attr_list = AttributeMgr.GetAttributteByClass(self.param.cap_cfg)
					local t_attr = AttributeMgr.MulAttribute(add_attr_list, per_attr_data.attr_value / 10000)
					extra_cap = AttributeMgr.GetCapability(t_attr)
				end
			end

			for k, v in pairs(self.only_attr_list) do
				v:SetData(attr_list[k])
			end

			capability = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(self.param.cap_cfg))
			capability = capability + extra_cap
		end
	elseif part_type == RIGHT_PART_TYPE.ESOTERICA then
		self:SetEsotericaRight()
	end

	self.node_list.cap_value.text.text = capability
end

-- 右侧面板状态刷新
function AppearanceGetNew:SetRightPartStatus(part_type)
	self.node_list.btn_save:CustomSetActive(part_type ~= RIGHT_PART_TYPE.TIANSHEN_PART and self.param.type ~= ROLE_APPE_TYPE.BEAST_SKIN)
	self.node_list.capability:CustomSetActive(part_type ~= RIGHT_PART_TYPE.NUQI_PART and part_type ~= RIGHT_PART_TYPE.ESOTERICA)
	self.node_list.beast_flair_panel:CustomSetActive(part_type == RIGHT_PART_TYPE.BEASTS_PART)
	self.node_list.tianshen_panel:CustomSetActive(part_type == RIGHT_PART_TYPE.TIANSHEN_PART)
	self.node_list.nuqi_panel:CustomSetActive(part_type == RIGHT_PART_TYPE.NUQI_PART)
	self.node_list.single_skill_part:CustomSetActive(part_type == RIGHT_PART_TYPE.SINGLE_SKILL)
	self.node_list.only_attr_part:CustomSetActive(part_type == RIGHT_PART_TYPE.ONLY_ATTR_PART)
	self.node_list.beast_left_msg_root:CustomSetActive(part_type == RIGHT_PART_TYPE.BEASTS_PART)
	self.node_list.show_star_root:CustomSetActive(part_type == RIGHT_PART_TYPE.BEASTS_PART or part_type == RIGHT_PART_TYPE.TIANSHEN_PART)
	self.node_list.thunder_panel:CustomSetActive(part_type == RIGHT_PART_TYPE.THUNDER_PART)
	self.node_list.esoterica_right_root:CustomSetActive(part_type == RIGHT_PART_TYPE.ESOTERICA)
end

-- 幻化
function AppearanceGetNew:MagicallyChange()
	local body_show_type = self.param.type
	local index_param = self.param.index_param
	local appe_image_id = self.param.appe_image_id

	local part_type = ROLE_APPE_2_SHIZHUANG_TYPE[body_show_type]
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, index_param)
	local res_id = fashion_cfg and fashion_cfg.resouce or 0
	local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(part_type)
	local is_can_use = used_index == 0 or used_index ~= index_param

	if body_show_type == ROLE_APPE_TYPE.BUBBLE or --气泡
		body_show_type == ROLE_APPE_TYPE.FACE or --装饰 脸
		body_show_type == ROLE_APPE_TYPE.YAO or --装饰 腰
		body_show_type == ROLE_APPE_TYPE.WEI or --装饰 尾
		body_show_type == ROLE_APPE_TYPE.HAND or --装饰 手
		body_show_type == ROLE_APPE_TYPE.HALO or -- 光环
		body_show_type == ROLE_APPE_TYPE.FASHION or --时装
		body_show_type == ROLE_APPE_TYPE.FOOT or --足迹	
		body_show_type == ROLE_APPE_TYPE.PHOTO then --相框
		if is_can_use then
			NewAppearanceWGCtrl.Instance:OnUseFashion(part_type, index_param, 1)
		end
	elseif body_show_type == ROLE_APPE_TYPE.SHENBING and is_can_use then                                     --武器/神兵
		NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
			ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, res_id, index_param)
	elseif body_show_type == ROLE_APPE_TYPE.JIANZHEN and is_can_use then                                     --剑阵
		NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
			ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, res_id, index_param)
	elseif body_show_type == ROLE_APPE_TYPE.WING and is_can_use then                                         -- 羽翼特殊形象
		NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING,
			res_id, index_param)
	elseif body_show_type == ROLE_APPE_TYPE.FABAO and is_can_use then                                        --法宝
		NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO,
			res_id, index_param)
	elseif body_show_type == ROLE_APPE_TYPE.LINGGONG then                                                    -- 灵弓

	elseif body_show_type == ROLE_APPE_TYPE.BABY then                                                        -- 宝宝

	elseif body_show_type == ROLE_APPE_TYPE.LINGCHONG_NORMAL or body_show_type == ROLE_APPE_TYPE.LINGCHONG then -- 普通形象灵宠-- 灵宠特殊形象
		local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG)
		local used_imageid = all_info and all_info.used_imageid or 0
		if used_imageid == 0 or used_imageid ~= appe_image_id then
			NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_IMAGE, appe_image_id)
		end
	elseif body_show_type == ROLE_APPE_TYPE.MOUNT_NORMAL or body_show_type == ROLE_APPE_TYPE.MOUNT then -- 进阶坐骑-- 坐骑特殊形象
		local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
		local used_imageid = all_info and all_info.used_imageid or 0

		if used_imageid == 0 or used_imageid ~= appe_image_id then
			NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_image_id, index_param)
		end
	elseif body_show_type == ROLE_APPE_TYPE.HUAKUN then -- 化鲲
		local kun_cfg = NewAppearanceWGData.Instance:GetKunActCfgByAppeId(appe_image_id)
		local kun_id = kun_cfg and kun_cfg.id or 0
		if kun_id <= 0 then
			return
		end

		local used_id = NewAppearanceWGData.Instance:GetKunUsedId()
		if used_id == 0 or used_id ~= kun_id then
			NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, kun_id)
		end
	elseif body_show_type == ROLE_APPE_TYPE.BIANSHEN_XIUWEI then
		CultivationWGCtrl.Instance:ChooseAngerType(appe_image_id)
	end

	self:DoCloseTween()
end

function AppearanceGetNew:SetTianshenSkillContent(skill_info_data)
	local ts_zhudong_cfg = skill_info_data[1].skill
	local show_zhu_skill_num = skill_info_data[1].show_zhu_skill_num
	local ts_t = Split(ts_zhudong_cfg, "|")
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.param.cfg.index)
	local star_level = tianshen_info and tianshen_info.star or 0

	-- 主动
	for i, skill_render in ipairs(self.ts_zhudong_skill_list) do
		skill_render:SetVisible(ts_t[i] ~= nil)
		
		if ts_t[i] ~= nil then
			local skill_data = {}
			local skill_id = tonumber(ts_t[i]) or 0
			local cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, 1)
			skill_data.skill_icon = cfg and cfg.icon_resource or 0
			skill_data.skill_id = skill_id
			skill_data.is_zhu = true		-- 是否为主动
			skill_data.tianshen_index = self.param.cfg.index
			skill_render:SetData(skill_data)
		end
	end

	-- 被动
	local beidong_list = skill_info_data[2]
	for i, skill_render in ipairs(self.ts_beidong_skill_list) do
		skill_render:SetVisible(beidong_list[i] ~= nil)
		
		if beidong_list[i] ~= nil then
			local skill_data = {}
			skill_data.skill_icon = beidong_list[i].skill_icon or 0
			skill_data.skill_id = 0
			skill_data.is_zhu = false		-- 是否为主动
			skill_data.tianshen_index = self.param.cfg.index
			skill_render:SetData(skill_data)
		end
	end

	-- 属性
	local attr_cfg = TianShenWGData.Instance:GetTianShenStar1(skill_info_data[1].index, 0)
	local attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
	for k, v in pairs(self.tianshen_attr_list) do
		v:SetData(attr_list[k])
	end

	if tianshen_info then
		local star_res_list = GetStarImgResByStar(star_level)
		for k,v in pairs(self.show_star_root) do
			v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
		end

		local image_str = star_level > 0 and "a3_gxhd_z_5" or "a3_gxhd_z_1"
		local bundle, asset = ResPath.GetRawImagesPNG(image_str)
		self.node_list.get_new_title.raw_image:LoadSprite(bundle, asset)
	end
end

-- 设置灵兽相关属性
function AppearanceGetNew:SetBeastFlairMessage()
	-- 属性
	local beast_data = self.param.beast_data
	if (not beast_data) or (not beast_data.server_data) then
		return
	end

	local server_data = beast_data.server_data
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)

	if not beast_cfg then
		return
	end

	local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
	self.node_list.beast_color_img.image:LoadSprite(bundle, asset, function()
		self.node_list.beast_color_img.image:SetNativeSize()
	end)

	local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
	local aim_anim_star_root = nil
	for k, v in pairs(self.show_star_root) do
		v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
	end

	-- -- 音频
	local audio_id = beast_cfg.talk_audio
	if audio_id and audio_id ~= "" then
		local bundle, asset = ResPath.GetNpcTalkVoiceResByResName(audio_id)
		TalkCache.PlayGuideTalkAudio(bundle, asset)
	end

	-- 属性
	local attr_list, exp_base_value = ControlBeastsWGData.Instance:GetBeastLevelAttrList(server_data.beast_id, server_data.beast_level, true, server_data.flair_values, server_data.effort_value)
	if attr_list then
		for i, beast_attr_cell in pairs(self.beast_attr_list) do
			if attr_list[i] then
				beast_attr_cell:SetVisible(true)
				beast_attr_cell:SetData(attr_list[i])
			else
				beast_attr_cell:SetVisible(false)
			end
		end
	end

	--去技能数据类查
	local now_skill_list = {}
	local function add_skill_func(skill_id, is_normal, skill_level)
		local info = nil

		if skill_id and skill_id ~= 0 then
			info = {}
			local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
			local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(skill_id)
	
			if client_cfg and beast_cfg then
				info.skill_icon = client_cfg.icon_resource
				info.desc = client_cfg.description
				info.skill_name = beast_cfg.skill_name
				info.skill_id = skill_id
				info.is_normal = is_normal 
				info.passive_str = is_normal and Language.Common.PuGong or ""
				info.skill_level = skill_level
			end
		end

		return info
	end

	local active_skill = add_skill_func(beast_cfg.skill_id, true, beast_cfg.skill_level)
	table.insert(now_skill_list, add_skill_func(beast_cfg.normal_skill_id, true, beast_cfg.normal_skill_level))
	--专属技能
	local be_skill_cfg = ControlBeastsWGData.Instance:GetBeastBeSkillCfgBySeq(beast_cfg.be_skill_id)

	if be_skill_cfg ~= nil then
		local info = {}
		info.skill_id = beast_cfg.be_skill_id
		info.is_zhuan_shu = true
		info.skill_icon = be_skill_cfg.skill_icon
		info.skill_name = be_skill_cfg.skill_name
		info.desc = be_skill_cfg.skill_des
		info.skill_level = beast_cfg.be_skill_level or 0
		info.passive_str = Language.Common.ZhuanShu
		table.insert(now_skill_list, info)
	end
	
	for i, skill_cell in ipairs(self.beast_skill_llist) do
		local data = now_skill_list[i]
		skill_cell:SetVisible(data ~= nil)

		if data ~= nil then
			skill_cell:SetData(data)
		end
	end

	if active_skill then
		bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_jnd_%d", beast_cfg.skill_type_id))
		self.node_list.beast_skill_di.image:LoadSprite(bundle, asset, function()
			self.node_list.beast_skill_di.image:SetNativeSize()
		end)

		self.node_list.beast_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(active_skill.skill_icon))
		self.node_list.beast_skill_name.text.text = beast_cfg.skill_des
		self.node_list.beast_skill_desc.text.text = active_skill.desc
	end
end

--设置雷法相关
function AppearanceGetNew:SetThunderManaMessage()
	local attr_list = EquipWGData.GetSortAttrListByCfg(self.param.cap_cfg)

	local role_appe_type = ROLE_APPE_TYPE.HALO
	local image_id = self.param.thunder_suit_cfg and self.param.thunder_suit_cfg.halo_index or 0
	local idx = ROLE_APPE_TYPE_2_IDX[role_appe_type]
	local per_attr_data = {}

	if image_id > 0 and nil ~= idx and nil ~= role_appe_type then
		per_attr_data = NewAppearanceWGData.Instance:GetFashionPerAddAttr(idx, image_id, 1)
	end

	local insert_key = -1
	for key, value in pairs(attr_list) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			value.attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrName(value.attr_str, false, false), COLOR3B.GLOD)
			if insert_key < 0 then
				insert_key = key
			end
		end
	end
	local extra_cap = 0
	if not IsEmptyTable(per_attr_data) then
		if per_attr_data.cur_value then
			per_attr_data.attr_value = per_attr_data.cur_value or 0
		end
		if per_attr_data.attr_value > 0 then
			table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, per_attr_data)
			local add_attr_list = AttributeMgr.GetAttributteByClass(self.param.cap_cfg)
			local t_attr = AttributeMgr.MulAttribute(add_attr_list, per_attr_data.attr_value / 10000)
			extra_cap = AttributeMgr.GetCapability(t_attr)
		end
	end

	for k, v in pairs(self.thunder_attr_list) do
		v:SetData(attr_list[k])
	end
	
	self.node_list.thunder_skill_group:SetActive(false)
	self.node_list.thunder_last_skill_group:SetActive(false)
	if self.param.thunder_suit_cfg then
		local cur_buff_desc_id = self.param.thunder_suit_cfg.cur_buff_desc_id or 0
		local cur_buff_icon = self.param.thunder_suit_cfg.cur_buff_icon or 0
		local last_buff_desc_id = self.param.thunder_suit_cfg.last_buff_desc_id or 0
		local last_buff_icon = self.param.thunder_suit_cfg.last_buff_icon or 0

		if cur_buff_desc_id > 0 then
			self.node_list.thunder_skill_group:SetActive(true)
			local buff_cfg = FightWGData.Instance:GetBuffDescCfgByType(cur_buff_desc_id)
			self.node_list.thunder_cur_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(cur_buff_icon))
			self.node_list.thunder_cur_skill_name.tmp.text = buff_cfg and buff_cfg.buff_name or ""
			self.node_list.thunder_cur_skill_desc.tmp.text = buff_cfg and buff_cfg.desc or ""
		end

		if last_buff_desc_id > 0 then
			self.node_list.thunder_last_skill_group:SetActive(true)
			local buff_cfg = FightWGData.Instance:GetBuffDescCfgByType(last_buff_desc_id)
			self.node_list.thunder_last_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(last_buff_icon))
			self.node_list.thunder_last_skill_name.tmp.text = buff_cfg and buff_cfg.buff_name or ""
			self.node_list.thunder_last_skill_desc.tmp.text = buff_cfg and buff_cfg.desc or ""
		end
	end

	local capability = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(self.param.cap_cfg))
	capability = capability + extra_cap

	return capability
end

-- 设置怒气技能
function AppearanceGetNew:SetNuQiSkillContent(skill_info_data)
	for i, nuqi_skill_cell in ipairs(self.nuqi_skill_list) do
		if skill_info_data[i] then
			nuqi_skill_cell:SetVisible(true)
			nuqi_skill_cell:SetData(skill_info_data[i])
		else
			nuqi_skill_cell:SetVisible(false)
		end
	end
end

function AppearanceGetNew:SetMainAsset(res_id, load_callback)
	local bundle, asset = self.param.path(res_id)
	self.model_display:SetMainAsset(bundle, asset, load_callback)
end

function AppearanceGetNew:SetWeaponModel()
	local other_asset = self.param.other_asset
	if not other_asset or IsEmptyTable(other_asset) then
		return
	end

	self.model_display:SetWeaponModel(other_asset.bundle, other_asset.asset, other_asset.scale)
end

function AppearanceGetNew:SetEsotericaRight()
	local esoterica_cfg = self.param.esoterica_cfg
	self.node_list.esoterica_skill_desc.text.text = esoterica_cfg.skill_desc

	if not IsEmptyTable(esoterica_cfg) then
		local skill_level = CultivationWGData.Instance:GetEsotericaSlotSkillLevel(self.param.appe_image_id)
		local cur_skill_level = skill_level >= 1 and skill_level or 1
		local skill_cfg = SkillWGData.Instance:GetEsotericaSkillById(esoterica_cfg.skill_id, cur_skill_level)
		self.node_list.es_name_text.text.text = skill_cfg.skill_name

		local list = CultivationWGData.Instance:GetEsotericaSkillLevelList(self.param.appe_image_id)
		local cur_skill_level = CultivationWGData.Instance:GetEsotericaSlotSkillLevel(self.param.appe_image_id)
		local target_data = list[1]

		if not IsEmptyTable(target_data) then
			local slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(target_data.seq)
			if not IsEmptyTable(slot_cfg) then
				local skill_id = slot_cfg.skill_id
				local is_act = cur_skill_level >= 1
				self.node_list["es_skill_mask"]:CustomSetActive(not is_act)
				local show_level = is_act and cur_skill_level or target_data.level

				local skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, show_level)
				if skill_client_cfg then
					local bundle, asset = ResPath.GetSkillIconById(skill_client_cfg.icon_resource)
					self.node_list["es_skill_icon"].image:LoadSprite(bundle, asset, function()
						self.node_list["es_skill_icon"].image:SetNativeSize()
					end)
				end
			end
		end
	end
end

function AppearanceGetNew:StopCountDown()
	if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end
end

--开始倒计时，结束后自动开始移动图标
function AppearanceGetNew:StartCountDown()
	self:StopCountDown()

	if self.auto_cutdown then
		local function time_func(elapse_time, total_time)
			if elapse_time >= total_time then
				self:DoCloseTween()
				self:StopCountDown()
				return
			end
		end

		self.static_count_down = CountDown.Instance:AddCountDown(STATIC_TIME, 1, time_func)
	end
end

function AppearanceGetNew:GetGuideUiCallBack(ui_name, ui_param)
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

-- 设置自动关闭恭喜获得的信息
function AppearanceGetNew:SetAutoCloseViewOperate(is_auto_operate, btn_text_str, is_show_get_btn)
	if (not is_show_get_btn) or (not is_show_get_btn) then
		self.node_list.auto_btn_save_txt.text.text = ""
		return
	end

	self:ClearCloseTimer()
	self.node_list.auto_btn_save_txt.text.text = string.format(Language.Common.SecondAutoTips, AUTO_CLOSE_TIME, btn_text_str)

	local update_fun = function(elapse_time, total_time)
		local floor_elapse_time = math.floor(elapse_time)
		if total_time - floor_elapse_time > 0 then
			self.node_list.auto_btn_save_txt.text.text = string.format(Language.Common.SecondAutoTips, math.floor(total_time - floor_elapse_time), btn_text_str)
		end
	end

	CountDownManager.Instance:AddCountDown("new_appearance_close_timer", update_fun, BindTool.Bind1(self.MagicallyChange, self), nil, AUTO_CLOSE_TIME, 1)
end

function AppearanceGetNew:ClearCloseTimer()
    if CountDownManager.Instance:HasCountDown("new_appearance_close_timer") then
		CountDownManager.Instance:RemoveCountDown("new_appearance_close_timer")
	end
end
-----------------------------------------------------------------------------------------------------------------
-- 技能类别(怒气技能)
AppearanceNuQiSkillRender = AppearanceNuQiSkillRender or BaseClass(BaseRender)
function AppearanceNuQiSkillRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.nuqi_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
	self.node_list.nuqi_skill_name.text.text = self.data.skill_name
end

-- 天神技能
AppearanceTianShenSkillRender = AppearanceTianShenSkillRender or BaseClass(BaseRender)
function AppearanceTianShenSkillRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.skill_image.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))

	if self.data.is_zhu then
		local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.tianshen_index)
		local check_skill_id = self.data.skill_id
		local shenshi_rank = tianshen_info and tianshen_info.star or 0
		local skill_level_now = TianShenWGData.Instance:GetTianShenSkillLv(self.data.tianshen_index, shenshi_rank, check_skill_id) or 1
		local shenshi_rank_last = shenshi_rank - 1

		if shenshi_rank_last <= 0 then
			shenshi_rank_last = 0
		end

		local skill_level_last = TianShenWGData.Instance:GetTianShenSkillLv(self.data.tianshen_index, shenshi_rank_last, check_skill_id) or 1
		self.node_list.skill_level.text.text = skill_level_now
		self.node_list.up_flag:CustomSetActive(skill_level_last ~= skill_level_now)
	else
		self.node_list.up_flag:CustomSetActive(false)
		self.node_list.skill_level.text.text = tostring(1)
	end
end