RoleEquipAttrView = RoleEquipAttrView or BaseClass(SafeBaseView)

function RoleEquipAttrView:__init()
	self.is_async_load = false
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self.view_name = "RoleEquipAttrView"
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_role_euqip")
	self.choose_index = 0
	self.equip_body_seq = 0
end

function RoleEquipAttrView:OpenCallBack()
	EquipmentWGData.Instance:GetStoneActiveLevelChange()
	EquipWGData.Instance:GetStarActiveLevelChange()
	EquipmentWGData.Instance:GetStrengthActiveLevelChange()
	EquipmentLingYuWGData.Instance:GetLingYuActiveLevelChange()
end

function RoleEquipAttrView:ReleaseCallBack()
	self.rich_attr = nil
	if self.role_equip_attr_tip then
		self.role_equip_attr_tip:DeleteMe()
		self.role_equip_attr_tip = nil
	end
	if self.role_equip_lingyu_attr_tip then
		self.role_equip_lingyu_attr_tip:DeleteMe()
		self.role_equip_lingyu_attr_tip = nil
	end

	if self.level_change_event then
		GlobalEventSystem:UnBind(self.level_change_event)
		self.level_change_event = nil
	end

	if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	end
	self.cur_attr_list = nil

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	end
	self.next_attr_list = nil
end

function RoleEquipAttrView:LoadCallBack()
	self.cur_attr_list = {}
	self.next_attr_list = {}
	for i = 1, 5 do
		self.cur_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end
	self.level_change_event = GlobalEventSystem:Bind(OtherEventType.StrangerStarLevel, BindTool.Bind(self.LevelChangeCallBack, self))

	self.node_list["left_active_btn"].button:AddClickListener(BindTool.Bind(self.OnClickActive, self))
end

function RoleEquipAttrView:ShowIndexCallBack(index)
end

function RoleEquipAttrView:OnClickActive()
	if self.choose_index == 1 then
		local active_level = EquipmentWGData.Instance:GetStoneActiveLevel(self.equip_body_seq)
		local next_total_cfg = EquipmentWGData.Instance:GetBaoShiTotalStoneCfg(self.equip_body_seq, active_level, true)
		if IsEmptyTable(next_total_cfg) then return end
		EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.ACTIVE_STONE_LEVEL, next_total_cfg.total_stone, self.equip_body_seq, nil, nil, nil)
	elseif self.choose_index == 2 then
		local active_level = EquipWGData.Instance:GetEquipStarActiveLevel(self.equip_body_seq)
		local total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipTotalStarCfg(self.equip_body_seq, active_level)
		if IsEmptyTable(next_total_cfg) then return end
		RoleWGCtrl.Instance:SendActiceEquipStarLevel(next_total_cfg.total_star_level)
	elseif self.choose_index == 3 then
		local active_level = EquipmentWGData.Instance:GetEquipStrengthLV(self.equip_body_seq)
		local all_total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipTotalStrengthCfg(self.equip_body_seq, active_level)
		if IsEmptyTable(next_total_cfg) then return end
		EquipmentWGCtrl.Instance:SendExtraAttrActive(EQUIP_GRID_STRENGTH_OPERA_TYPE.EQUIP_GRID_STRENGTH_OPERA_TYPE_ACTIVE_SRTEBGTH, next_total_cfg.total_level, self.equip_body_seq)
	elseif self.choose_index == 4 then
		local active_level = EquipmentLingYuWGData.Instance:GetLingYuActiveLevel(self.equip_body_seq)
		local next_total_cfg = EquipmentLingYuWGData.Instance:GetLingYuTotalStoneCfg(self.equip_body_seq, active_level, true)
		if IsEmptyTable(next_total_cfg) then return end
		EquipmentWGCtrl.Instance:SendLingYuOperate(LINGYU_OPERA_TYPE.LINGYU_ACTIVE_ALL_LEVEL, next_total_cfg.lingyu_level, self.equip_body_seq)
	elseif self.choose_index == 5 then
		local active_level = EquipmentWGData.Instance:GetStoneActiveLevel(self.equip_body_seq)
		local next_total_cfg = EquipmentWGData.Instance:GetBaoShiTotalStoneCfg(self.equip_body_seq, active_level, true)
		if IsEmptyTable(next_total_cfg) then return end
		EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.ACTIVE_STONE_LEVEL, next_total_cfg.total_stone, self.equip_body_seq, nil, nil, nil)
	end
end

function RoleEquipAttrView:LevelChangeCallBack()
	self:Flush()
	if self.role_equip_attr_tip then
		self.role_equip_attr_tip:Flush()
	end
	if self.role_equip_lingyu_attr_tip then
		self.role_equip_lingyu_attr_tip:Flush()
	end
	self:CheckAndPlayActiveEffect()
end

function RoleEquipAttrView:CheckAndPlayActiveEffect()
	if self.choose_index == 1 then
		if EquipmentWGData.Instance:GetStoneActiveLevelChange() then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
		end
	-- elseif self.choose_index == 2 then
	-- 	if EquipWGData.Instance:GetStarActiveLevelChange() then
	-- 		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
	-- 	end
	elseif self.choose_index == 3 then
		if EquipmentWGData.Instance:GetStrengthActiveLevelChange() then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
		end
	elseif self.choose_index == 4 then
		if EquipmentLingYuWGData.Instance:GetLingYuActiveLevelChange() then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
		end
	end
end

function RoleEquipAttrView:OnFlush()
	if not self.choose_index then
		return
	end

	--强化分为普通装备强化和仙器装备强化，左边是普通装备强化，右边是仙器装备强化
	--从背包里点宝石 如果灵石开了 宝石和灵石一起显示
	self.node_list["Content_right"]:SetActive(self.choose_index == 3)
	self.node_list["Content_lingyu_right"]:SetActive(self.choose_index == 5)
	local curr_attr, next_attr = {}, {}
	local total_cfg, next_total_cfg
	local now_level, next_level, all_level = "", "", ""
	local active_level = ""
	local total_level = ""
	local tem_ratio = "<color=%s>(%s/%s)</color>"

	local lbl_attr_title_str = Language.Role.RoleEuqipAttrName[self.choose_index]

	if self.choose_index == 1 then
		local cur_equip_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(self.equip_body_seq)
		lbl_attr_title_str = string.format(Language.Common.Order, cur_equip_body_cfg.equip_order)

		local baoshi_total_level = EquipmentWGData.Instance:GetTotalStoneLevel(self.equip_body_seq)
		total_level = baoshi_total_level
		active_level = EquipmentWGData.Instance:GetStoneActiveLevel(self.equip_body_seq)
		total_cfg = EquipmentWGData.Instance:GetBaoShiTotalStoneCfg(self.equip_body_seq, active_level, false)
		next_total_cfg = EquipmentWGData.Instance:GetBaoShiTotalStoneCfg(self.equip_body_seq, active_level, true)
		curr_attr, next_attr = self:UpdataAttrStr(total_cfg, next_total_cfg)
		if not total_cfg then
			now_level = ""
			local is_enough = (next_total_cfg.total_stone - baoshi_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, baoshi_total_level, next_total_cfg.total_stone)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[1], next_total_cfg.total_stone) .. ratio_str

		elseif not next_total_cfg then
			next_level = ""
			now_level = string.format(Language.Role.RoleEuqipNowLevel[1], active_level)
			all_level = string.format(Language.Role.RoleEuqipAllLevel_1[1], baoshi_total_level)
		else
			now_level = string.format(Language.Role.RoleEuqipNowLevel[1], active_level)
			local is_enough = (next_total_cfg.total_stone - baoshi_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, baoshi_total_level, next_total_cfg.total_stone)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[1], next_total_cfg.total_stone) .. ratio_str
		end
	elseif self.choose_index == 2 then
		local cur_equip_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(self.equip_body_seq)
		lbl_attr_title_str = string.format(Language.Common.Order, cur_equip_body_cfg.equip_order)

		self.desc = ""
		local star_total_level = EquipWGData.Instance:GetEquipTotalStar(self.equip_body_seq)
		active_level = EquipWGData.Instance:GetEquipStarActiveLevel(self.equip_body_seq)
		total_level = star_total_level
		total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipTotalStarCfg(self.equip_body_seq, active_level)
		curr_attr, next_attr = self:UpdataAttrStr(total_cfg, next_total_cfg)
		if not total_cfg then
			now_level = ""
			local is_enough = (next_total_cfg.total_star_level - star_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, star_total_level, next_total_cfg.total_star_level)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[2], next_total_cfg.total_star_level) .. ratio_str

		elseif not next_total_cfg then
			next_level = ""
			now_level = string.format(Language.Role.RoleEuqipNowLevel[2], active_level)
			all_level = string.format(Language.Role.RoleEuqipAllLevel_1[2], star_total_level)
		else
			now_level = string.format(Language.Role.RoleEuqipNowLevel[2], active_level)
			local is_enough = (next_total_cfg.total_star_level - star_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, star_total_level, next_total_cfg.total_star_level)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[2], next_total_cfg.total_star_level) .. ratio_str

		end
	elseif self.choose_index == 3 then
		local cur_equip_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(self.equip_body_seq)
		lbl_attr_title_str = string.format(Language.Role.RoleEuqipCuiHuoAttrName[1], cur_equip_body_cfg.equip_order)

		if nil == self.role_equip_attr_tip then
			self.role_equip_attr_tip = RoleEquipAttrRender.New(self.node_list["Content_right"])
		end
		
		self.role_equip_attr_tip:SetRoleEquipBodySeq(self.equip_body_seq)
		self.role_equip_attr_tip:Flush()
		local shenzhu_total_level = EquipmentWGData.Instance:GetTotalStrengthLevel(self.equip_body_seq)
		total_level = shenzhu_total_level
		active_level = EquipmentWGData.Instance:GetEquipStrengthLV(self.equip_body_seq)
		total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipTotalStrengthCfg(self.equip_body_seq, active_level)

		curr_attr, next_attr = self:UpdataAttrStr(total_cfg, next_total_cfg, true)
		if not total_cfg then
			now_level = ""
			local is_enough = (next_total_cfg.total_level - shenzhu_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, shenzhu_total_level, next_total_cfg.total_level)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[3], next_total_cfg.total_level) .. ratio_str
		elseif not next_total_cfg then
			next_level = ""
			now_level = string.format(Language.Role.RoleEuqipNowLevel[3], active_level)
		else
			now_level = string.format(Language.Role.RoleEuqipNowLevel[3], active_level)
			local is_enough = (next_total_cfg.total_level - shenzhu_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, shenzhu_total_level, next_total_cfg.total_level)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[3], next_total_cfg.total_level) .. ratio_str
		end
	elseif self.choose_index == 4 then
		local cur_equip_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(self.equip_body_seq)
		lbl_attr_title_str = string.format(Language.Common.Order, cur_equip_body_cfg.equip_order)

		local lingyu_total_level = EquipmentLingYuWGData.Instance:GetTotalLingYuLevel(self.equip_body_seq)
		total_level = lingyu_total_level
		active_level = EquipmentLingYuWGData.Instance:GetLingYuActiveLevel(self.equip_body_seq)
		total_cfg = EquipmentLingYuWGData.Instance:GetLingYuTotalStoneCfg(self.equip_body_seq, active_level, false)
		next_total_cfg = EquipmentLingYuWGData.Instance:GetLingYuTotalStoneCfg(self.equip_body_seq, active_level, true)
		curr_attr, next_attr = self:UpdataAttrStr(total_cfg, next_total_cfg)
		if not total_cfg then
			now_level = ""
			local is_enough = (next_total_cfg.lingyu_level - lingyu_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, lingyu_total_level, next_total_cfg.lingyu_level)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[5], next_total_cfg.lingyu_level) .. ratio_str

		elseif not next_total_cfg then
			next_level = ""
			now_level = string.format(Language.Role.RoleEuqipNowLevel[5], active_level)
			all_level = string.format(Language.Role.RoleEuqipAllLevel_1[4], lingyu_total_level)
		else
			now_level = string.format(Language.Role.RoleEuqipNowLevel[5], active_level)
			local is_enough = (next_total_cfg.lingyu_level - lingyu_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, lingyu_total_level, next_total_cfg.lingyu_level)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[5], next_total_cfg.lingyu_level) .. ratio_str
		end
	elseif self.choose_index == 5 then
		local baoshi_total_level = EquipmentWGData.Instance:GetTotalStoneLevel()
		total_level = baoshi_total_level
		active_level = EquipmentWGData.Instance:GetStoneActiveLevel(self.equip_body_seq)
		total_cfg = EquipmentWGData.Instance:GetBaoShiTotalStoneCfg(self.equip_body_seq, active_level, false)
		next_total_cfg = EquipmentWGData.Instance:GetBaoShiTotalStoneCfg(self.equip_body_seq, active_level, true)
		curr_attr, next_attr = self:UpdataAttrStr(total_cfg, next_total_cfg)
		if not total_cfg then
			now_level = ""
			local is_enough = (next_total_cfg.total_stone - baoshi_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, baoshi_total_level, next_total_cfg.total_stone)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[1], next_total_cfg.total_stone) .. ratio_str

		elseif not next_total_cfg then
			next_level = ""
			now_level = string.format(Language.Role.RoleEuqipNowLevel[1], active_level)
			all_level = string.format(Language.Role.RoleEuqipAllLevel_1[1], baoshi_total_level)
		else
			now_level = string.format(Language.Role.RoleEuqipNowLevel[1], active_level)
			local is_enough = (next_total_cfg.total_stone - baoshi_total_level) <= 0
			local color = is_enough and "#99ffbb" or "#ff9292"
			local ratio_str = string.format(tem_ratio, color, baoshi_total_level, next_total_cfg.total_stone)
			ratio_str = is_enough and "" or ratio_str
			next_level = string.format(Language.Role.RoleEuqipNowLevel[1], next_total_cfg.total_stone) .. ratio_str
		end

		if nil == self.role_equip_lingyu_attr_tip then
			self.role_equip_lingyu_attr_tip = RoleEquipLingYuAttrRender.New(self.node_list["Content_lingyu_right"])
		end
		self.role_equip_lingyu_attr_tip:Flush()
	end

	local next_total_level = 0
	if not IsEmptyTable(next_total_cfg) then
		next_total_level = next_total_cfg.total_level or next_total_cfg.total_stone or next_total_cfg.total_star_level or next_total_cfg.lingyu_level
	end

	if next_total_cfg and next_total_level <= total_level and active_level <= next_total_level then
		XUI.SetButtonEnabled(self.node_list["left_active_btn"],true)
		self.node_list["yimanji"]:SetActive(false)
		self.node_list["remind"]:SetActive(true)
		self.node_list["left_active_btn"]:SetActive(true)
		self.node_list.desc_bom_tip:CustomSetActive(true)
	elseif next_total_cfg then
		XUI.SetButtonEnabled(self.node_list["left_active_btn"],false)
		self.node_list["yimanji"]:SetActive(false)
		self.node_list["left_active_btn"]:SetActive(true)
		self.node_list["remind"]:SetActive(false)
		self.node_list.desc_bom_tip:CustomSetActive(true)
	else
		self.node_list["left_active_btn"]:SetActive(false)
		self.node_list["yimanji"]:SetActive(true)
		self.node_list["remind"]:SetActive(false)
		self.node_list.desc_bom_tip:CustomSetActive(false)
	end

	local desc_bom_tip = ""
	if self.choose_index == 2 then
		-- XUI.SetButtonEnabled(self.node_list["left_active_btn"], false)
		self.node_list["left_active_btn"]:CustomSetActive(false)
		self.node_list["remind"]:SetActive(false)
		desc_bom_tip = Language.Equip.StrengthActiveLevelTip
	end
	self.node_list.desc_bom_tip.text.text = desc_bom_tip

	self.node_list["cur_level_bg"]:SetActive(total_cfg ~= nil)
	self.node_list["now_level"].text.text = now_level
	self.node_list["next_level_bg"]:SetActive(next_total_cfg ~= nil)
	self.node_list["next_level"].text.text = next_level
	self.node_list["no_attr_tip"]:SetActive(not total_cfg)
	self.node_list["max_attr_tip"]:SetActive(not next_total_cfg)
    self.node_list["lbl_attr_title"].text.text = lbl_attr_title_str

	self.node_list.role_equip_title.text.text = Language.Role.RoleEuqipAttrNameTitle[self.choose_index]

	for i = 1, 5 do
		self.cur_attr_list[i]:SetData(curr_attr[i])
		self.next_attr_list[i]:SetData(next_attr[i])
	end
end

function RoleEquipAttrView:SetTextData(index, equip_body_seq)
	self.choose_index = index
	self.equip_body_seq = equip_body_seq
end

function RoleEquipAttrView:CloseCallBack(is_all)

end

function RoleEquipAttrView:UpdataAttrStr(total_cfg, next_total_cfg, is_need_attr_per)
	local function get_attr_tab(cfg)
		if not cfg then
			return
		end

		local tab = EquipWGData.GetSortAttrListByCfg(cfg)
		local attr_str = {}
		for k,v in pairs(tab) do
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			local per_value = is_per and v.attr_value / 100 or v.attr_value
			local per_str = is_per and "%" or ""
			attr_str[k] = {name = Language.Common.AttrNameList2[v.attr_str] or "", value = "+" .. per_value .. per_str}
		end

		if is_need_attr_per then
			table.insert(attr_str, {name = Language.Role.RoleEuqipAttrPerName[1], value = "+" ..  cfg.per_zhuangbeiqianghua / 100 .. "%"})
		end

		return attr_str
	end

	local curr_attr = get_attr_tab(total_cfg) or {}
	local next_attr = get_attr_tab(next_total_cfg) or {}
	return curr_attr, next_attr
end

RoleEquipAttrRender = RoleEquipAttrRender or BaseClass(BaseRender)
function RoleEquipAttrRender:__init(  )
	self.cur_attr_list = {}
	self.next_attr_list = {}
	self.equip_body_seq = 0
	for i = 1, 5 do
		self.cur_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end
end

function RoleEquipAttrRender:__delete()
	if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	end
	self.cur_attr_list = nil

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	end
	self.next_attr_list = nil
	self.equip_body_seq = nil
end

function RoleEquipAttrRender:LoadCallBack()
	self.node_list["left_active_btn"].button:AddClickListener(BindTool.Bind(self.OnRightClickActive, self))
end

function RoleEquipAttrRender:SetRoleEquipBodySeq(equip_body_seq)
	self.equip_body_seq = equip_body_seq
end

function RoleEquipAttrRender:OnRightClickActive()
	local active_level =  EquipmentWGData.Instance:GetXianQiStrengthLV(self.equip_body_seq)
	local total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipXianQiTotalStrengthCfg(self.equip_body_seq, active_level)
	if IsEmptyTable(next_total_cfg) then return end
	EquipmentWGCtrl.Instance:SendExtraAttrActive(EQUIP_GRID_STRENGTH_OPERA_TYPE.EQUIP_GRID_STRENGTH_OPERA_TYPE_ACTIVE_XIANQI, next_total_cfg.total_level, self.equip_body_seq)
end

--刷新的是仙器装备属性
function RoleEquipAttrRender:OnFlush(  )
	self:FlushXianQiStrength()
end

function RoleEquipAttrRender:UpdataAttrStr(total_cfg, next_total_cfg)
	local function get_attr_tab(cfg)
		if not cfg then
			return
		end

		local tab = EquipWGData.GetSortAttrListByCfg(cfg)
		local attr_str = {}
		for k,v in pairs(tab) do
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			local per_value = is_per and v.attr_value / 100 or v.attr_value
			local per_str = is_per and "%" or ""
			attr_str[k] = {name = Language.Common.AttrNameList2[v.attr_str] or "", value = "+" .. per_value .. per_str}
		end

		table.insert(attr_str, {name = Language.Role.RoleEuqipAttrPerName[2], value = "+" ..  cfg.per_shipinqianghua / 100 .. "%"})

		return attr_str
	end

	local curr_attr = get_attr_tab(total_cfg) or {}
	local next_attr = get_attr_tab(next_total_cfg) or {}
	return curr_attr, next_attr
end

function RoleEquipAttrRender:FlushXianQiStrength()
	local cur_equip_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(self.equip_body_seq)
	self.node_list["lbl_attr_title"].text.text = string.format(Language.Role.RoleEuqipCuiHuoAttrName[2], cur_equip_body_cfg.equip_order)

	local curr_attr, next_attr = {}, {}
	local total_cfg, next_total_cfg
	local now_level, next_level, all_level = "", "", ""
	local shenzhu_total_level = EquipmentWGData.Instance:GetTotalXianQiStrengthLevel(self.equip_body_seq)
	local active_level =  EquipmentWGData.Instance:GetXianQiStrengthLV(self.equip_body_seq)
	total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipXianQiTotalStrengthCfg(self.equip_body_seq, active_level)
	curr_attr, next_attr = self:UpdataAttrStr(total_cfg, next_total_cfg)
	local tem_ratio = "<color=%s>(%s/%s)</color>"
	if not total_cfg then
		now_level = ""
		local is_enough = (next_total_cfg.total_level - shenzhu_total_level) <= 0
		local color = is_enough and "#99ffbb" or "#ff9292"
		local ratio_str = string.format(tem_ratio, color, shenzhu_total_level, next_total_cfg.total_level)
		ratio_str = is_enough and "" or ratio_str
		next_level = string.format(Language.Role.RoleEuqipNowLevel[4], next_total_cfg.total_level) .. ratio_str

	elseif not next_total_cfg then
		next_level = ""
		now_level = string.format(Language.Role.RoleEuqipNowLevel[4], active_level)
		all_level = string.format(Language.Role.RoleEuqipAllLevel_1[3], shenzhu_total_level)
	else
		now_level = string.format(Language.Role.RoleEuqipNowLevel[4], active_level)
		local is_enough = (next_total_cfg.total_level - shenzhu_total_level) <= 0
		local color = is_enough and "#99ffbb" or "#ff9292"
		local ratio_str = string.format(tem_ratio, color, shenzhu_total_level, next_total_cfg.total_level)
		ratio_str = is_enough and "" or ratio_str
		next_level = string.format(Language.Role.RoleEuqipNowLevel[4], next_total_cfg.total_level) .. ratio_str
	end

	if next_total_cfg and next_total_cfg.total_level <= shenzhu_total_level and active_level <= next_total_cfg.total_level then
		XUI.SetButtonEnabled(self.node_list["left_active_btn"],true)
		self.node_list["yimanji"]:SetActive(false)
		self.node_list["left_active_btn"]:SetActive(true)
		self.node_list["remind"]:SetActive(true)
	elseif next_total_cfg then
		XUI.SetButtonEnabled(self.node_list["left_active_btn"],false)
		self.node_list["yimanji"]:SetActive(false)
		self.node_list["left_active_btn"]:SetActive(true)
		self.node_list["remind"]:SetActive(false)
	else
		self.node_list["left_active_btn"]:SetActive(false)
		self.node_list["yimanji"]:SetActive(true)
		self.node_list["remind"]:SetActive(false)
	end

	self.node_list["cur_level_bg"]:SetActive(total_cfg ~= nil)
	self.node_list["now_level"].text.text = now_level
	self.node_list["next_level_bg"]:SetActive(next_total_cfg ~= nil)
	self.node_list["next_level"].text.text = next_level
	self.node_list["no_attr_tip"]:SetActive(not total_cfg)
	self.node_list["max_attr_tip"]:SetActive(not next_total_cfg)

	for i = 1, 5 do
		self.cur_attr_list[i]:SetData(curr_attr[i])
		self.next_attr_list[i]:SetData(next_attr[i])
	end
end

--灵玉在右侧属性的显示
RoleEquipLingYuAttrRender = RoleEquipLingYuAttrRender or BaseClass(BaseRender)
function RoleEquipLingYuAttrRender:__init(  )
	self.cur_attr_list = {}
	self.next_attr_list = {}
	for i = 1, 5 do
		self.cur_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end
end

function RoleEquipLingYuAttrRender:__delete()
	if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	end
	self.cur_attr_list = nil

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	end
	self.next_attr_list = nil
end

function RoleEquipLingYuAttrRender:LoadCallBack()
	self.node_list["left_active_btn"].button:AddClickListener(BindTool.Bind(self.OnRightClickActive, self))
end

function RoleEquipLingYuAttrRender:OnRightClickActive()
	local active_level = EquipmentLingYuWGData.Instance:GetLingYuActiveLevel(self.equip_body_seq)
	local next_total_cfg = EquipmentLingYuWGData.Instance:GetLingYuTotalStoneCfg(self.equip_body_seq, active_level, true)
	if IsEmptyTable(next_total_cfg) then return end
	EquipmentWGCtrl.Instance:SendLingYuOperate(LINGYU_OPERA_TYPE.LINGYU_ACTIVE_ALL_LEVEL, next_total_cfg.lingyu_level, self.equip_body_seq)
end

--刷新的是仙器装备属性
function RoleEquipLingYuAttrRender:OnFlush(  )
	self:FlushLingYuAttr()
end

function RoleEquipLingYuAttrRender:FlushLingYuAttr()
	local now_level, next_level, all_level = "", "", ""
	local lingyu_total_level = EquipmentLingYuWGData.Instance:GetTotalLingYuLevel(self.equip_body_seq)
	local total_level = lingyu_total_level
	local active_level = EquipmentLingYuWGData.Instance:GetLingYuActiveLevel(self.equip_body_seq)
	local total_cfg = EquipmentLingYuWGData.Instance:GetLingYuTotalStoneCfg(self.equip_body_seq, active_level, false)
	local next_total_cfg = EquipmentLingYuWGData.Instance:GetLingYuTotalStoneCfg(self.equip_body_seq, active_level, true)
	local curr_attr, next_attr = self:UpdataAttrStr(total_cfg, next_total_cfg)
	local tem_ratio = "<color=%s>(%s/%s)</color>"
	if not total_cfg then
		now_level = ""
		local is_enough = (next_total_cfg.lingyu_level - lingyu_total_level) <= 0
		local color = is_enough and "#99ffbb" or "#ff9292"
		local ratio_str = string.format(tem_ratio, color, lingyu_total_level, next_total_cfg.lingyu_level)
		ratio_str = is_enough and "" or ratio_str
		next_level = string.format(Language.Role.RoleEuqipNowLevel[5], next_total_cfg.lingyu_level) .. ratio_str

	elseif not next_total_cfg then
		next_level = ""
		now_level = string.format(Language.Role.RoleEuqipNowLevel[5], active_level)
		all_level = string.format(Language.Role.RoleEuqipAllLevel_1[4], lingyu_total_level)
	else
		now_level = string.format(Language.Role.RoleEuqipNowLevel[5], active_level)
		local is_enough = (next_total_cfg.lingyu_level - lingyu_total_level) <= 0
		local color = is_enough and "#99ffbb" or "#ff9292"
		local ratio_str = string.format(tem_ratio, color, lingyu_total_level, next_total_cfg.lingyu_level)
		ratio_str = is_enough and "" or ratio_str
		next_level = string.format(Language.Role.RoleEuqipNowLevel[5], next_total_cfg.lingyu_level) .. ratio_str
	end

	if next_total_cfg and next_total_cfg.lingyu_level <= total_level and active_level <= next_total_cfg.lingyu_level then
		XUI.SetButtonEnabled(self.node_list["left_active_btn"],true)
		self.node_list["yimanji"]:SetActive(false)
		self.node_list["left_active_btn"]:SetActive(true)
		self.node_list["remind"]:SetActive(true)
	elseif next_total_cfg then
		XUI.SetButtonEnabled(self.node_list["left_active_btn"],false)
		self.node_list["yimanji"]:SetActive(false)
		self.node_list["left_active_btn"]:SetActive(true)
		self.node_list["remind"]:SetActive(false)
	else
		self.node_list["left_active_btn"]:SetActive(false)
		self.node_list["yimanji"]:SetActive(true)
		self.node_list["remind"]:SetActive(false)
	end

	self.node_list["cur_level_bg"]:SetActive(total_cfg ~= nil)
	self.node_list["now_level"].text.text = now_level
	self.node_list["next_level_bg"]:SetActive(next_total_cfg ~= nil)
	self.node_list["next_level"].text.text = next_level
	self.node_list["no_attr_tip"]:SetActive(not total_cfg)
	self.node_list["max_attr_tip"]:SetActive(not next_total_cfg)

	local cur_equip_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(self.equip_body_seq)
	self.node_list["lbl_attr_title"].text.text = string.format(Language.Common.Order, cur_equip_body_cfg.equip_order)
	-- self.node_list["lbl_attr_title"].text.text = Language.Role.RoleEuqipAttrName[4]

	for i = 1, 5 do
		self.cur_attr_list[i]:SetData(curr_attr[i])
		self.next_attr_list[i]:SetData(next_attr[i])
	end
end

function RoleEquipLingYuAttrRender:UpdataAttrStr(total_cfg, next_total_cfg)
	local function get_attr_tab(cfg)
		if not cfg then
			return
		end

		local tab = EquipWGData.GetSortAttrListByCfg(cfg)
		local attr_str = {}
		for k,v in pairs(tab) do
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			local per_value = is_per and v.attr_value / 100 or v.attr_value
			local per_str = is_per and "%" or ""
			attr_str[k] = {name = Language.Common.AttrNameList2[v.attr_str] or "", value = "+" .. per_value .. per_str}
		end

		return attr_str
	end

	local curr_attr = get_attr_tab(total_cfg) or {}
	local next_attr = get_attr_tab(next_total_cfg) or {}
	return curr_attr, next_attr
end



-- ==============  EquipAddAttrRender  ===============================
EquipAddAttrRender = EquipAddAttrRender or BaseClass(BaseRender)

function EquipAddAttrRender:OnFlush()
	if not IsEmptyTable(self.data) then
		self.node_list.name.text.text = self.data.name
		self.node_list.value.text.text = self.data.value
		self.view:SetActive(true)
	else
		self.node_list.name.text.text = ""
		self.node_list.value.text.text = ""
		self.view:SetActive(false)
	end
end
