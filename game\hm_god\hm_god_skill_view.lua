HmGodSkillView = HmGodSkillView or BaseClass(SafeBaseView)
function HmGodSkillView:__init()
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/hm_god_ui_prefab", "layout_hmgod_skill")
end

function HmGodSkillView:LoadCallBack()
	if nil == self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)
		-- self.role_model:SetUI3DModel(self.node_list["ph_display"].transform,
		-- 	self.node_list["EventTriggerListener"].event_trigger_listener, 1, true, M<PERSON><PERSON>_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_model)
	end

	self.attr_list = {}
	for i = 1, 6 do
		self.attr_list[i] = self.node_list["attr_" .. i]
	end

	if not self.part_condition_list then
		self.part_condition_list = {}
		for i = 1, 6 do
			self.part_condition_list[i] = PartConditionCell.New(self.node_list["part_con_" .. i])
			self.part_condition_list[i]:SetIndex(i)
		end
	end
end

function HmGodSkillView:ReleaseCallBack()
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.part_condition_list then
		for k, v in pairs(self.part_condition_list) do
			v:DeleteMe()
		end
		self.part_condition_list = nil
	end

	self.suit_seq = nil
	self.attr_list = nil
end

function HmGodSkillView:SetDataAndOpen(suit_seq)
	self.suit_seq = suit_seq
	self:Open()
end

function HmGodSkillView:ShowIndexCallBack()

end

function HmGodSkillView:OnFlush()
	if self.suit_seq == nil then
		return
	end

	self:FlushPanelView()
	self:FlushSuitModel()
end

function HmGodSkillView:FlushPanelView()
	local theme_cfg = WardrobeWGData.Instance:GetThemeCfgBySuit(self.suit_seq)
	if theme_cfg and theme_cfg.skill_icon then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(theme_cfg.skill_icon))
	end

	local suit_info = HmGodWGData.Instance:GetHmSuitInfoBySuit(self.suit_seq)
	if suit_info then
		for i = 1, 6 do
			if self.part_condition_list[i] then
				if suit_info.part_list[i] then
					self.part_condition_list[i]:SetData(suit_info.part_list[i])
					self.part_condition_list[i]:SetActive(true)
				else
					self.part_condition_list[i]:SetActive(false)
				end
			end
		end
	end

	local attr_data = WardrobeWGData.Instance:GetAttrBySuit(self.suit_seq) -- 特殊处理显示最后一个套装属性
	if attr_data then
		local list = attr_data[#attr_data].attr_list
		for k, v in ipairs(self.attr_list) do
			if list[k] then
				local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
				local name = ""
				local attr_str = ""
				local value = ""
				if list[k].attr_type == "add_per" then
					name = Language.HmGosView.SpecialAttr
					value = is_per and list[k].value or list[k].value / 100 .. "%"
					local format_str = self.is_from_itemtip and "%s   %s" or "%s %s"
					attr_str = string.format(format_str, name, value)
				else
					name = EquipmentWGData.Instance:GetAttrName(list[k].attr_type, false)
					value = is_per and list[k].value or list[k].value / 100 .. "%"
					if self.is_from_itemtip then
						attr_str = string.format("%s %s", name, value)
					elseif is_per then
						attr_str = string.format("%s %s", name, value)
					else
						attr_str = string.format("%s %s", name, value)
					end
				end

				v.text.text = attr_str
				v:SetActive(true)
			else
				v:SetActive(false)
			end
		end
	end
end

function HmGodSkillView:FlushSuitModel()
	if self.suit_seq == nil then
		return
	end
	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.suit_seq)

	if IsEmptyTable(show_list) then
		return
	end

	--清理掉回调
	self.role_model:RemoveAllModel()

	self.is_foot_view = false
	self.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	self.mount_res_id = 0
	self.mount_action = ""
	local has_fashion_show = false

	local res_id, fashion_cfg
	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				self.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
				has_fashion_show = true
			end
		elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
			fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
				data.param1)
			if fashion_cfg then
				self.mount_res_id = fashion_cfg.appe_image_id
				self.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					self.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
			fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
			if fashion_cfg then
				self.mount_res_id = fashion_cfg.active_id
				self.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					self.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		end
	end


	local d_body_res, d_hair_res, d_face_res
	local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
	local vo = main_role and main_role:GetVo()
	if not has_fashion_show and vo and vo.appearance then
		if vo.appearance.fashion_body == 0 then
			d_body_res = vo.appearance.default_body_res_id
			d_hair_res = vo.appearance.default_hair_res_id
			d_face_res = vo.appearance.default_face_res_id
		end
	end

	local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
		no_need_do_anim = true,
    }

	if self.mount_res_id > 0 then
		self.role_model:SetRoleResid(self.body_res_id, nil, extra_role_model_data)
	else
		self.role_model:SetRoleResid(self.body_res_id, function()
			if self.mount_res_id == 0 then
				if self.show_role_idel_ani then
					self.role_model:PlayRoleShowAction()
					self.show_role_idel_ani = false
				else
					self.role_model:PlayIdleAni()
				end
			end
		end, extra_role_model_data)
	end

	if self.mount_res_id > 0 then
		self.role_model:SetMountResid(self.mount_res_id)
		self.role_model:PlayStartAction(self.mount_action)
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v)
	end

	self:ChangeModelShowScale()
end

function HmGodSkillView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end

	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce

			if data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			end
		end
	end
end

function HmGodSkillView:ChangeModelShowScale()
	if self.suit_seq == nil then
		return
	end

	local data = WardrobeWGData.Instance:GetThemeCfgBySuit(self.suit_seq)
	if IsEmptyTable(data) then
		return
	end

	local rotate_str = data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.role_model then
			self.role_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 180, rot[3] or 0))
		end
	end

	local scale = 0.2
	if scale and scale ~= "" then
		RectTransform.SetLocalScale(self.node_list.ph_display.rect, scale)
	end
end

PartConditionCell = PartConditionCell or BaseClass(BaseRender)

function PartConditionCell:__init()
end

function PartConditionCell:LoadCallBack()

end

function PartConditionCell:__delete()

end

function PartConditionCell:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.hl_img:SetActive(self.data.state ~= REWARD_STATE_TYPE.UNDONE)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.show_item_id)
	--local color = self.data.state == REWARD_STATE_TYPE.UNDONE and COLOR3B.GRAY or COLOR3B.WHITE
	self.node_list.name.text.text = item_cfg.name --ToColorStr(item_cfg.name, color)
end
