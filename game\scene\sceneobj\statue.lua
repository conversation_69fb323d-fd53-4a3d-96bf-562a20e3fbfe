--雕像
Statue = Statue or BaseClass(Role)

function Statue:__init(vo)
    self.obj_type = SceneObjType.Statue
    self.followui_class = RoleFollow
    self.draw_obj:SetObjType(self.obj_type)
    self.shield_obj_type = ShieldObjType.Statue
    self:SetObjId(vo.obj_id)
end

function Statue:InitAppearance()
    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:SetHpVisiable(false)
        follow_ui:ForceSetVisible(true)
    end
end

function Statue:CreateFollowUi()
    Role.CreateFollowUi(self)

    if self:IsMainRole() then
        self.follow_ui:IsMainRole(true)
    end

    -- 默认不显示血条
    self.follow_ui:SetHpVisiable(false)

    if self.draw_obj then
        local point = self.draw_obj:GetAttachPoint(AttachPoint.UI)
        self.follow_ui:SetFollowTarget(point, self.draw_obj:GetName())
    end

    self:SyncShowHp(true)
end

function Statue:IsStatue()
    return true
end
