DiscountPurchaseView = DiscountPurchaseView or BaseClass(SafeBaseView)

function DiscountPurchaseView:__init()
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/discount_purchase_ui_prefab", "layout_discount_purchase")
end

function DiscountPurchaseView:ReleaseCallBack()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

    self:CleanTimeDown()
end

function DiscountPurchaseView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickCloseBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnBuyOnClick, self))
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.display_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	end
end

function DiscountPurchaseView:OnFlush()
	local show_data = DiscountPurchaseWGData.Instance:GetCurrShowData()
	local gift_data = ((show_data or {}).gift_data or {})[1]
	if IsEmptyTable(gift_data)then
		return
	end
	-- 刷新倒计时
	self:FlushTimeCountDown(show_data.end_time)
    -- 刷新模型
	local cfg = DiscountPurchaseWGData.Instance:GetCurrPopupConditionCfg()
	self:FlushModel(cfg)
    -- 刷新标签名称
	self.node_list.purchase_name_txt.text.text = show_data.title_name
    -- 刷新限购次数与购买价格
	local color = nil
	local is_can_buy = DiscountPurchaseWGData.Instance:CkeckCurrGradeCanBuy()
	local can_buy_num = gift_data.can_buy_num
	local limitNum = gift_data.num
	local str = gift_data.price
	if not is_can_buy then--已售罄
		color = COLOR3B.RED
		self.node_list["price_text"].transform.anchoredPosition = Vector2(0,2)
		str = Language.DiscountPurchase.DiscountAllOut
		can_buy_num = 0
	else
		self.node_list["price_text"].transform.anchoredPosition = Vector2(-53, 2)
		color = COLOR3B.L_GREEN
	end

    self.node_list.limit_num_text.text.text = string.format(Language.DiscountPurchase.BuyLimit,color,can_buy_num,limitNum)
	self.node_list.price_text.text.text = str
	self.node_list.org_price_text.text.text = gift_data.org_price
	self.node_list.org_price_text:SetActive(is_can_buy)
	self.node_list.discount_line:SetActive(is_can_buy)
    -- 刷新物品格子
    local item_list = gift_data.item_list
    if item_list and self.reward_list then
		self.reward_list:SetDataList(item_list)
    end
end

-- 刷新模型啥的
function DiscountPurchaseView:FlushModel(chapter_data)
	if not chapter_data then
		return
	end

	local display_data = {}
	if chapter_data["model_show_itemid"] ~= 0 and chapter_data["model_show_itemid"] ~= "" then
		local split_list = string.split(chapter_data["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = chapter_data["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = chapter_data["model_bundle_name"]
	display_data.asset_name = chapter_data["model_asset_name"]
	local model_show_type = tonumber(chapter_data["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1

	self.model_display:SetData(display_data)
	local scale = chapter_data["display_scale"]
	Transform.SetLocalScaleXYZ(self.node_list["display_root"].transform, scale, scale, scale)

	local pos_x, pos_y = 0, 0
	if chapter_data.display_pos and chapter_data.display_pos ~= "" then
		local pos_list = string.split(chapter_data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, pos_x, pos_y)

	if chapter_data.rotation and chapter_data.rotation ~= "" then
		local rotation_tab = string.split(chapter_data.rotation,"|")
		self.node_list.display_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function DiscountPurchaseView:OnClickCloseBtn()
	if DiscountPurchaseWGData.Instance:HasCanBuyGift() then
		TipWGCtrl.Instance:OpenCheckTodayAlertTips(Language.DiscountPurchase.CloseStr, BindTool.Bind(self.Close, self), "DiscountPurchaseClose", nil, Language.DiscountPurchase.CloseBtnSure, Language.DiscountPurchase.CloseBtnCancel)
		return
	end
	self:Close()
end

function DiscountPurchaseView:OnBuyOnClick()
	local showData = DiscountPurchaseWGData.Instance:GetCurrShowData()
    local data = showData and showData.gift_data[1]
	local is_can_buy =  DiscountPurchaseWGData.Instance:CkeckCurrGradeCanBuy()
	if not is_can_buy then
		TipWGCtrl.Instance:ShowSystemMsg(Language.DiscountPurchase.AllShopBuy)
		return
	end
    if not showData or not data then return end
	-- 发送协议
	RechargeWGCtrl.Instance:Recharge(data.RMB, data.rmb_type,data.rmb_seq)
end

-----------------活动时间倒计时-------------------
function DiscountPurchaseView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("discount_purchase_down") then
		CountDownManager.Instance:RemoveCountDown("discount_purchase_down")
	end
end

function DiscountPurchaseView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanTimeDown()
		if self.node_list["time_down"] then
			self.node_list["time_down"].text.text = TimeUtil.FormatTimeDHMS(invalid_time - TimeWGCtrl.Instance:GetServerTime())
			CountDownManager.Instance:AddCountDown("discount_purchase_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
		end
	end
end

function DiscountPurchaseView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		if self.node_list["time_down"] then
			self.node_list["time_down"].text.text =TimeUtil.FormatTimeDHMS(valid_time)
		end
	end
end

function DiscountPurchaseView:OnComplete()
	if self.node_list["time_down"] then
    	self.node_list["time_down"].text.text = ""
	end
	self:Close()
end

----------------------------------------------------------------------------------------------------------------------