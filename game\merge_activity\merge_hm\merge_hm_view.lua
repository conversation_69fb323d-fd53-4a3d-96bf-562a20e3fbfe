
function MergeActivityView:DelHongMengView()
	if self.hmwd_pm_list then
		self.hmwd_pm_list:DeleteMe()
		self.hmwd_pm_list = nil
	end
	if CountDownManager.Instance:HasCountDown("hongmeng_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("hongmeng_end_countdown")
	end

	if self.hmwd_role_data_change_callback then
		RoleWGData.Instance:UnNotifyAttrChange(self.hmwd_role_data_change_callback)
		self.hmwd_role_data_change_callback = nil
	end

	if self.hmwd_animator_time then
		GlobalTimerQuest:CancelQuest(self.hmwd_animator_time)
		self.hmwd_animator_time = nil
	end
end

function MergeActivityView:OpenMergeHMWD()
	MergeHMWGCtrl.Instance:SendReq(CSA_EXPBUY_OP.INFO)
	RankWGCtrl.Instance:SendRankReq(RankKind.Person, PersonRankType.Level)
end

function MergeActivityView:InitHongMengView()
	self.node_list["btn_ganwu"].button:AddClickListener(BindTool.Bind1(self.OnClickGanWu, self))

	self.hmwd_role_data_change_callback = BindTool.Bind1(self.OnRoleDataChangeHongMeng, self)
	RoleWGData.Instance:NotifyAttrChange(self.hmwd_role_data_change_callback, {"level"})

	self.node_list["skip_effect_check"].button:AddClickListener(BindTool.Bind(self.OnClickSkipEffect, self))
	self:SetHMWDViewAnimator()
	self:FlushSkipEffect()
end

function MergeActivityView:ShowIndexHMWD(index)
	MergeHMWGData.Instance:SetRedFlag()
	RemindManager.Instance:Fire(RemindName.Merge_HMWD)
	self:FlushCountDown(index)
	if index == TabIndex.merge_activity_2118 then
		self:SetOutsideRuleTips(Language.MergeHMWD.Tip)
	end
end

function MergeActivityView:FlushCountDown(index)
	if index == TabIndex.merge_activity_2118 then
		--if CountDownManager.Instance:HasCountDown("hongmeng_end_countdown") then
		--	CountDownManager.Instance:RemoveCountDown("hongmeng_end_countdown")
		--end
		--
		--local end_time = MergeHMWGData.Instance:GetEndTime()
		--if end_time > TimeWGCtrl.Instance:GetServerTime() then
		--	self:UpdataHMWDEndTime(TimeWGCtrl.Instance:GetServerTime(), end_time)
		--	CountDownManager.Instance:AddCountDown("hongmeng_end_countdown",
		--			BindTool.Bind1(self.UpdataHMWDEndTime, self),
		--			BindTool.Bind1(self.EndHMWDTimeCallBack, self),
		--			end_time, nil, 1)
		--end
	else
		if CountDownManager.Instance:HasCountDown("hongmeng_end_countdown") then
			CountDownManager.Instance:RemoveCountDown("hongmeng_end_countdown")
		end
	end
end

function MergeActivityView:OnRoleDataChangeHongMeng(attr_name, value, old_value)
	if attr_name == "level" then
		TryDelayCall(self, function()
			self:Flush(TabIndex.qifu_hmwd)
		end, 0.5, "merge_hmwd_level")
	end
end

function MergeActivityView:SetHMWDViewAnimator()
	-- if not self.hmwd_animator_time then --动画先屏蔽
	-- 	self.hmwd_animator_time = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.PlayHMWDAnimator, self), UITween_CONSTS.QiFuHmwd.WaitTime)
	-- 	self.node_list.hongmeng_parent.canvas_group.alpha = UITween_CONSTS.QiFuHmwd.FromAlpha
	-- 	self.node_list["shuijingqiu"].transform.anchoredPosition = Vector2(1.8, -523)
	-- end
end

function MergeActivityView:PlayHMWDAnimator()
	if self.hmwd_animator_time then
		GlobalTimerQuest:CancelQuest(self.hmwd_animator_time)
		self.hmwd_animator_time = nil
	end

	UITween.AlpahShow(self.node_list.hongmeng_parent.gameObject, UITween_CONSTS.QiFuHmwd.FromAlpha, UITween_CONSTS.QiFuHmwd.ToAlpha, UITween_CONSTS.QiFuHmwd.ExpQiuMoveTime, DG.Tweening.Ease.OutCubic)
	local shuijingqiu_animator = self.node_list["shuijingqiu"].rect:DOAnchorPosY(-52.9, UITween_CONSTS.QiFuHmwd.ExpQiuMoveTime)
	shuijingqiu_animator:SetEase(DG.Tweening.Ease.Linear)
end

function MergeActivityView:UpdataHMWDEndTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	local str = ""
	if format_time.day > 0 then
		str = string.format(Language.QiFu.EndTimeShow[1],format_time.day,format_time.hour,format_time.min,format_time.s)
	elseif format_time.hour > 0 then
		str = string.format(Language.QiFu.EndTimeShow[2],format_time.hour,format_time.min,format_time.s)
	elseif format_time.min > 0 then
		str = string.format(Language.QiFu.EndTimeShow[3],format_time.min,format_time.s)
	elseif format_time.s > 0 then
		str = string.format(Language.QiFu.EndTimeShow[4],format_time.s)
	end
	self.node_list["shengyu_time"].text.text = str
end

function MergeActivityView:EndHMWDTimeCallBack()
	--self:Close()
end

function MergeActivityView:CreatHMWDPmList()
	local data_list = RankWGData.Instance:GetRankData(RankKind.Person, PersonRankType.Level)
	local data_info = {}
	if not self.hmwd_pm_list then
		self.hmwd_pm_list = AsyncListView.New(MergeHMRender, self.node_list["ph_pm_list"])
	end

	for k,v in pairs(data_list) do
		if v.rank_index <= 10 then
			table.insert( data_info, v )
		else
			break
		end
    end
	self.hmwd_pm_list:SetDataList(data_info,0)
end

function MergeActivityView:FlushHongMengView(param)
    self:CreatHMWDPmList()
    self:FlushHMWDView()
    self:SetHMWDRuleTip()
    if not CountDownManager.Instance:HasCountDown("hongmeng_end_countdown") then
        self:FlushCountDown(self.show_index)
    end
end

function MergeActivityView:FlushHMWDView()
	local cur_cfg = MergeHMWGData.Instance:GetHMCurCfg()
	if not cur_cfg then
		return
	end

	local mid_world_level = MergeHMWGData.Instance:GetMidWorldLevel()
	local buy_count = MergeHMWGData.Instance:GetBuyCount()
	local limit_times = MergeHMWGData.Instance:GetShowBuyTimesLimit()
	local sheng_num = limit_times - buy_count
	local color = sheng_num > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
	sheng_num = ToColorStr(sheng_num, color)
	local cur_level = RoleWGData.Instance:GetRoleLevel()
	local can_get_level = mid_world_level - cur_cfg.correction_value
	local display_level = cur_level >= can_get_level and cur_level or can_get_level
	self.node_list["shengyu_num"].text.text = string.format(Language.QiFu.GanWuShengYuNun, sheng_num, limit_times)
	self.node_list["ganwu_limint"].text.text = RoleWGData.GetLevelString(display_level)

	local vo = GameVoManager.Instance:GetMainRoleVo()
    color = vo.level < mid_world_level - cur_cfg.correction_value and COLOR3B.D_GREEN or COLOR3B.D_RED
    local str = RoleWGData.GetLevelString(vo.level)
	self.node_list["hmwd_my_level"].text.text = str

    local price = MergeHMWGData.Instance:GetGanWuPrice()
    if price > 0 then
        self.node_list.ganwu_icon:SetActive(true)
		self.node_list["btnganwu_txt"]:SetActive(true)
        self.node_list["btnganwu_txt"].text.text = price
		XUI.SetGraphicGrey(self.node_list.btn_ganwu, false)
    else
        self.node_list.ganwu_icon:SetActive(false)
		self.node_list["btnganwu_txt"]:SetActive(false)
        self.node_list["btnganwu_txt"].text.text = ""
		XUI.SetGraphicGrey(self.node_list.btn_ganwu, true)
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.btnganwu_txt.rect)
    local get_exp = MergeHMWGData.Instance:GetGanWuExP()
	get_exp = get_exp.exp or 0
	--self.node_list["cur_get_exp"].text.text = string.format(Language.QiFu.GanWuGetExp, CommonDataManager.ConverExpByThousand(get_exp, nil, false))
	self.node_list["exp_value"].text.text = string.format(Language.MergeHMWD.HongMengGetExp, CommonDataManager.ConverExpByThousand(get_exp, nil, false))

	local max_level = RoleWGData.GetRoleMaxLevel() --默认100级别
	local new_vo_exp = vo.exp + get_exp
	local role_level = RoleWGData.Instance:GetRoleLevel() or 0
	local exp_cfg = RoleWGData.Instance.GetRoleExpCfgByLv(role_level) or {}
	local max_exp = exp_cfg.exp or 0

	if role_level >= max_level then
		self.node_list["exp_show"]:SetActive(false)
		self.node_list["level_show"]:SetActive(true)
		self.node_list["hmwd_up_level"].text.text = 0
	else
		if new_vo_exp >= max_exp then
			local enough_exp = new_vo_exp - max_exp
			local add_level = 1
			local level_cfg
			for i = 1, 300 do
				level_cfg = RoleWGData.Instance.GetRoleExpCfgByLv(vo.level + add_level)

				if level_cfg and level_cfg.exp and enough_exp > level_cfg.exp then
					enough_exp = enough_exp - level_cfg.exp
					add_level = add_level + 1
				else
					break
				end
			end
			if vo.level + add_level >= max_level then
				add_level = max_level - role_level
			end
			self.node_list["hmwd_up_level"].text.text = add_level
			self.node_list["exp_show"]:SetActive(false)
			self.node_list["level_show"]:SetActive(true)
		else
			self.node_list["hmwd_up_level"].text.text = 0
			--self.node_list["exp_show"]:SetActive(true)
			self.node_list["level_show"]:SetActive(true)
			max_exp = max_exp > 0 and max_exp or 1
			local per_exp = math.ceil((100 * new_vo_exp) / max_exp)
			self.node_list["get_exp"].text.text = per_exp
		end
	end
end

function MergeActivityView:SetHMWDRuleTip()
    local cfg = MergeHMWGData.Instance:GetHMCurCfg()
    local des = cfg and cfg.tips or Language.MergeHMWD.HongMengDesc
	self:SetRuleInfo(des, Language.MergeHMWD.HongMengTitle)
end

function MergeActivityView:OnClickGanWu()
	local cur_cfg = MergeHMWGData.Instance:GetHMCurCfg()
	if not cur_cfg then
		return
	end

	local mid_world_level = MergeHMWGData.Instance:GetMidWorldLevel()
	local str = RoleWGData.GetLevelString(CrossServerWGData.Instance:GetServerMeanWorldLevel())
	if RoleWGData.Instance:GetRoleLevel() >= mid_world_level - cur_cfg.correction_value then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeHMWD.Hint2)
		return
	end

	local buy_count = MergeHMWGData.Instance:GetBuyCount()
	local limit_times = MergeHMWGData.Instance:GetCanBuyTimes()
	if limit_times <= buy_count then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.QiFu.Hint2)
		return
	end

	local is_skip = MergeHMWGData.Instance:GetSkipEffectStatus()
	local price = MergeHMWGData.Instance:GetGanWuPrice()
	local is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(price)
	if is_skip or not is_enough_money then
		MergeHMWGCtrl.Instance:SendReq(CSA_EXPBUY_OP.FETCH)
	else
		self.node_list.btn_ganwu.button.interactable = false
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_xuyuanshenshu_choujiang")
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["hm_effect_pos"].transform, 3, nil, nil, nil, nil, function ()
			MergeHMWGCtrl.Instance:SendReq(CSA_EXPBUY_OP.FETCH)
			self.node_list.btn_ganwu.button.interactable = true
		end)
	end
end

function MergeActivityView:OnClickSkipEffect()
    local is_skip = MergeHMWGData.Instance:GetSkipEffectStatus()
    is_skip = not is_skip
    MergeHMWGData.Instance:SetSkipEffectStatus(is_skip)
    self:FlushSkipEffect()
end

function MergeActivityView:FlushSkipEffect()
	local is_skip = MergeHMWGData.Instance:GetSkipEffectStatus()
    self.node_list["skip_effect_yes"]:SetActive(is_skip)
end
--------------------------------------------------------------------------------------------
MergeHMRender = MergeHMRender or BaseClass(BaseRender)
function MergeHMRender:OnFlush()
	if not self.data then
		return
	end
	self.node_list["img_pm"]:SetActive(self.data.rank_index <= 3)
	self.node_list["lbl_pm"]:SetActive(self.data.rank_index > 3)
	self.node_list["lbl_pm"].text.text = self.data.rank_index
	if self.data.rank_index <= 3 then
		self.node_list.img_pm.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank_index))
		self.node_list.bg.image:LoadSprite(ResPath.GetMergeImg("a2_hf_pmyq" .. self.data.rank_index))
	else
		self.node_list.bg.image:LoadSprite(ResPath.GetMergeImg("a2_hf_tmdi"))
	end
	self.node_list["lbl_name"].text.text = self.data.user_name
	self.node_list["lbl_level"].text.text = RoleWGData.GetLevelString(self.data.level)
end






