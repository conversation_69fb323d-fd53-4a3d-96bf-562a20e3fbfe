OperationCtnRechargeWGData = OperationCtnRechargeWGData or BaseClass()
OperationCtnRechargeWGData.ConfigPath= "config/auto_new/operation_activity_continuity_recharge_auto"

function OperationCtnRechargeWGData:__init()
	if OperationCtnRechargeWGData.Instance then
		ErrorLog("[OperationCtnRechargeWGData] Attemp to create a singleton twice !")
	end
	OperationCtnRechargeWGData.Instance = self

    self:InitCtnRechargeConfig()

    OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE, {[1] = OPERATION_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self))

	RemindManager.Instance:Register(RemindName.OperationCtnRecharge, BindTool.Bind(self.IsShowCtnRechargeRedPoint, self))
end

function OperationCtnRechargeWGData:__delete()
	OperationCtnRechargeWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.OperationCtnRecharge)
end

function OperationCtnRechargeWGData:SetData(protocol)

end

-------------------------- 连续充值 -----------------------------
function OperationCtnRechargeWGData:SetCtnRechargeData(protocol)
	self.ctn_day_money = protocol.day_money										--每天充值金额
	self.ctn_day_recharge_reward_flag = protocol.day_recharge_reward_flag		--每天奖励领取标记
	self.ctn_total_day_recharge_reward_flag = protocol.total_day_recharge_reward_flag --累计天数奖励领取标记
end

function OperationCtnRechargeWGData:GetCtnDayMoneyByActDay(activity_day)
	if self.ctn_day_money and self.ctn_day_money[activity_day] then
		return self.ctn_day_money[activity_day]
	end
	return 0
end

--获取累计充值多少天
function OperationCtnRechargeWGData:GetLeiJiRechargeDayCount(target_money)
	local count = 0

	if not self.ctn_day_money then
		return count
	end

	for i = 1, 100 do
		if not self.ctn_day_money[i] then
			break
		end
		if self.ctn_day_money[i] >= target_money then
			count = count + 1
		end
	end
	return count
end

--获取每天充值金额奖励是否领取
function OperationCtnRechargeWGData:GetDayRechargeIsFetch(index)
	if not index then
		return false
	end
	if self.ctn_day_recharge_reward_flag and self.ctn_day_recharge_reward_flag[index] then
		return self.ctn_day_recharge_reward_flag[index] == 1
	end
	return false
end

--获取累计充值天数奖励是否领取
function OperationCtnRechargeWGData:GetTotalRechargeIsFetch(index)
	if not index then
		return false
	end
	if self.ctn_total_day_recharge_reward_flag and self.ctn_total_day_recharge_reward_flag[index] then
		return self.ctn_total_day_recharge_reward_flag[index] == 1
	end
	return false
end

function OperationCtnRechargeWGData:InitCtnRechargeConfig()
	self.ctn_recharge_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_continuity_recharge_auto")
	self.ctn_recharge_other_cfg = self.ctn_recharge_cfg.other[1]
	self.ctn_recharge_day_recharge_reward_map = ListToMapList(self.ctn_recharge_cfg.day_recharge_reward, "grade")
	self.ctn_recharge_total_recharge_day_reward_map = ListToMapList(self.ctn_recharge_cfg.total_recharge_day_reward, "grade")

	--档次对应有多少天数
	self.grade_day_count_map = {}
	local flag_map = {}
	for i, v in pairs(self.ctn_recharge_cfg.day_recharge_reward) do
		if not self.grade_day_count_map[v.grade] then
			self.grade_day_count_map[v.grade] = 0
		end
		if not flag_map[v.grade] then
			flag_map[v.grade] = {}
		end
		if not flag_map[v.grade][v.activity_day] then
			flag_map[v.grade][v.activity_day] = true
			self.grade_day_count_map[v.grade] = self.grade_day_count_map[v.grade] + 1
		end
	end
end

function OperationCtnRechargeWGData:GetCtnRechargeOtherConfig()
	return self.ctn_recharge_other_cfg
end

--获取连续充值活动已开启的天数
function OperationCtnRechargeWGData:GetCtnRechargeOpenDay()
	--local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE)
	--if not activity_info then
	--	return -1
	--end
	--local server_time = TimeWGCtrl.Instance:GetServerTime()
	--if ACTIVITY_STATUS.OPEN == activity_info.status then
	--	local pass_time = server_time - activity_info.start_time
	--	local day = math.ceil(pass_time / 86400)
	--	if day <= 0 then
	--		day = 1
	--	end
	--	return day
	--end
	--return -1
	local day = OperationActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE)
	if day == 0 then
		day = self:GetTotalDayCount()
	end
	return day
end

--根据活动开启时的开服天数获取档次配置
function OperationCtnRechargeWGData:GetParamCfgByServerOpenDay()
	local return_cfg = {}
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE) --TimeWGCtrl.Instance:GetCurOpenServerDay()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
    local param_cfg = self.ctn_recharge_cfg.config_param
	for i = #param_cfg, 1, -1 do
		if param_cfg[i] and open_day >= param_cfg[i].start_server_day and open_day < param_cfg[i].end_server_day and week == param_cfg[i].week_index then
			return_cfg = param_cfg[i]
			break
		end
	end
	return return_cfg
end

--获取累计充值天数奖励配置列表
function OperationCtnRechargeWGData:GetTotalRehargeCfgList(not_need_sort)
	local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return {}
	end

	local cfg_list = self.ctn_recharge_total_recharge_day_reward_map[param_cfg.grade]
	if not cfg_list then
		return {}
	end

	local return_cfg_list = {}
	local money_flag = {}
	local index = 1
	for i = #cfg_list, 1, -1 do
		if not money_flag[cfg_list[i].money] then
			--return_cfg_list[cfg_list[i].money] = {}
			return_cfg_list[index] = {}
			money_flag[cfg_list[i].money] = index
			index = index + 1
		end
		table.insert(return_cfg_list[money_flag[cfg_list[i].money]], cfg_list[i])
	end
	--for i, v in ipairs(cfg_list) do
	--	if not return_cfg_list[v.money] then
	--		return_cfg_list[v.money] = {}
	--	end
	--	table.insert(return_cfg_list[v.money], v)
	--end

	if not not_need_sort then
		for i, v in ipairs(return_cfg_list) do
			--天数小的排前面
			table.sort(v, SortTools.KeyLowerSorter("total_day"))
		end
	end

	return return_cfg_list
end

--获取每日充值奖励配置列表
function OperationCtnRechargeWGData:GetDayRehargeCfgList(activity_day, grade, not_need_sort)
	activity_day = activity_day or self:GetCtnRechargeOpenDay() --不传就是获取当前活动天数
	if activity_day == -1 then
		return {}
	end

	if not grade then
		return {}
	end

	local cfg_list = self.ctn_recharge_day_recharge_reward_map[grade]
	if not cfg_list then
		return {}
	end

	local cur_open_day = self:GetCtnRechargeOpenDay()

	local return_cfg_list = {}
	for i, v in pairs(cfg_list) do
		if v.activity_day == activity_day then

			local data = {
				--配置数据
				index = v.index,
				grade = v.grade,
				seq = v.seq,
				activity_day = v.activity_day,
				money = v.money,
				reward_item = v.reward_item,
				describe = v.describe,
				describe_mid = v.describe_mid
			}

			if not not_need_sort then
				local has_fetch = self:GetDayRechargeIsFetch(data.seq + 1)
				local day_money = self:GetCtnDayMoneyByActDay(data.activity_day)
				local can_fetch = day_money >= data.money
				local is_yesterday = data.activity_day < cur_open_day

				local sort_value = data.money * (-1)
				--还没领取的排最前面
				if can_fetch and not has_fetch then
					sort_value = sort_value + 1000000
				end
				--未达成排第二
				if not can_fetch then
					sort_value = sort_value + 100000
				end
				--已领取排第三
				if has_fetch then
					sort_value = sort_value + 10000
				end
				--已错过排第四
				if is_yesterday and not can_fetch then
					sort_value = sort_value + 1000
				end

				data.sort_value = sort_value
			end

			table.insert(return_cfg_list, data)
		end
	end

	if not not_need_sort then
		table.sort(return_cfg_list, SortTools.KeyUpperSorter("sort_value"))
	end

	return return_cfg_list
end

--获取该档次总的活动持续天数
function OperationCtnRechargeWGData:GetTotalDayCount()
	local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return -1
	end
	return self.grade_day_count_map[param_cfg.grade]
end

--获取按钮数据列表
function OperationCtnRechargeWGData:GetDayButtonDataList()
	local return_data_list = {}
	local day_count = self:GetTotalDayCount()
	if day_count == -1 then
		--print_error("运营活动连续充值获取天数错误！")
		return return_data_list
	end

	local activity_day = self:GetCtnRechargeOpenDay()
	for i = 1, day_count do
		local data = {}
		data.day = i
		data.cur_act_day = activity_day
		data.max_day = day_count
		table.insert(return_data_list, data)
	end

	return return_data_list
end

--红点
--连续充值总红点
function OperationCtnRechargeWGData:IsShowCtnRechargeRedPoint()
	if not OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE) then
		return 0
	end
	if not self:GetActCanOpen() then
		return 0
	end
	local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return 0
	end
	local day_count = self:GetTotalDayCount()
	for i = 1, day_count do
		if self:GetDayButtonIsShowRedPoint(i, param_cfg.grade) then
			return 1
		end
	end

	local data_list = OperationCtnRechargeWGData.Instance:GetTotalRehargeCfgList(true)
	for i, list in pairs(data_list) do
		for k, v in pairs(list) do
			local has_fetch = OperationCtnRechargeWGData.Instance:GetTotalRechargeIsFetch(v.seq + 1)
			local total_recharge_day_count = OperationCtnRechargeWGData.Instance:GetLeiJiRechargeDayCount(v.money)
			local can_fetch = total_recharge_day_count >= v.total_day
			if can_fetch and not has_fetch then
				return 1
			end
		end
	end

	return 0
end

--是否显示天数按钮红点
function OperationCtnRechargeWGData:GetDayButtonIsShowRedPoint(day, grade)
	--该天数下，是否有可领取的奖励
	--当天充值的金额
	local day_chongzhi_value = self:GetCtnDayMoneyByActDay(day)
	--红点判断不需要排序
	local data_list = self:GetDayRehargeCfgList(day, grade, true)
	for i, v in ipairs(data_list) do
		local has_fetch = self:GetDayRechargeIsFetch(v.seq + 1)
		if day_chongzhi_value >= v.money and not has_fetch then
			return true
		end
	end
	return false
end

function OperationCtnRechargeWGData:GetActCanOpen()
    local vo = GameVoManager.Instance:GetMainRoleVo()
    local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return false
	end
    
	if vo.level >= self.ctn_recharge_other_cfg.open_role_level then
		return true
	end
	return false
end

function OperationCtnRechargeWGData:GetActEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE)
	if activity_info and activity_info.end_time then
		return activity_info.end_time
	end
	return -1
end