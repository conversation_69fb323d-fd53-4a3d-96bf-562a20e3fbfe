QiWuItemRender  = QiWuItemRender or BaseClass(BaseRender)

function QiWuItemRender:__init()
	self.item_render_tab = {}
	self.select_callback = nil
	self.asset_bundle = nil
	self.asset_name = nil
	self.select_tab = {[1] = {}}
end

function QiWuItemRender:__delete()
	for k, v in pairs(self.item_render_tab) do
		v:DeleteMe()
	end
	self.item_render_tab = {}
end

function QiWuItemRender:SetItemRender(item_render)
	self.item_render = item_render
end

function QiWuItemRender:SetAssetBundle(asset_bundle, asset_name)
	self.asset_bundle = asset_bundle
	self.asset_name = asset_name
end

function QiWuItemRender:SetSelectTab(select_tab)
	self.select_tab = select_tab
end

function QiWuItemRender:GetCellBySeq(seq)
	for k,v in pairs(self.item_render_tab) do
		local data = v:GetData()
		if seq == data.seq then
			return v
		end
	end
end

function QiWuItemRender:SetData(data)
	self.data = data
	-- print_error("---child---", #self.data, self.asset_bundle, self.asset_name)
	local render_length = #self.item_render_tab
	local data_length = #self.data
	local length = data_length > render_length and data_length or render_length
	for i = 1, length do
		local item_render = self.item_render_tab[i]
		if nil == item_render then
			if self.asset_bundle and self.asset_name then
				item_render = self.item_render.New()
				self.item_render_tab[i] = item_render
				item_render:LoadAsset(self.asset_bundle, self.asset_name, self.view.transform)
			else
				item_render = self.item_render.New(self.view)
				self.item_render_tab[i] = item_render
			end
		end

		if self.data[i] then
			item_render:SetIndex(i)
			-- item_render:SetSelectCallBack(BindTool.Bind(self.SelectCallBack, self))
			item_render:SetClickCallBack(self.click_callback)
			item_render:SetData(self.data[i])
			item_render:SetActive(true)
		else
			item_render:SetActive(false)
		end
	end

	if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function QiWuItemRender:OnFlush()
	local qua_name = Language.StrangeCatalog.quality_name[self.index]
	self.node_list.quality_name.text.text = qua_name
	self:RefreshSelectState()
end

function QiWuItemRender:RefreshSelectState()
	for k,v in pairs(self.item_render_tab) do
		local data = v:GetData()
		local is_select = data and self.select_tab[1][data.seq] == true
		v:SetSelect(is_select, true)
	end
end