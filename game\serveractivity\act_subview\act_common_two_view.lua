----------------------------------------------------
-- 通用界面二
----------------------------------------------------
ActCommonTwoView = ActCommonTwoView or BaseClass(SafeBaseView)

function ActCommonTwoView:__init(act_id,zodaer)
	self.act_id = act_id
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_act_common_two_view")
	self:AddViewResource(0, "uis/view/act_operate_ui_prefab", "flower")
end

function ActCommonTwoView:ReleaseCallBack()
	self.has_load = nil
	self.need_refresh = nil
end

function ActCommonTwoView:LoadCallBack()
	self.has_load = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ActCommonTwoView:RefreshView(param_list)
	if not self:IsOpen() then return end
	if not self.has_load then
		self.need_refresh = true
		return
	end
	self:RefreshTopDesc()

	-- if param_list and param_list.flush_param == "change_index" then
	local data_list = ActCombineData.Instance:GetCommonTwoViewData(self.act_id)
	if data_list ~= nil then
		-- self.node_list.img_text_pos:loadTexture(data_list.img_res_path)
		-- self.node_list.img_text_pos:setVisible(true)
		
		if data_list.btn_click_fun ~= nil then
			self.node_list.btn_goto:SetActive(true)
			self.node_list.goto_text.text.text = (data_list.btn_text or "")

			XUI.AddClickEventListener(self.node_list.btn_goto, function()
				data_list.btn_click_fun()
			end)
		end
	else
		self.node_list.btn_goto:SetActive(false)
		-- self.node_list.img_text_pos:setVisible(false)
	end
	-- end
end

function ActCommonTwoView:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)

	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 60)
		end
		self.node_list.version_act_time.text.text = star_str .. "————" .. end_str
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text = Language.OpenServer.ActShuoMing_1 .. (open_act_cfg.top_desc)
	end
end

