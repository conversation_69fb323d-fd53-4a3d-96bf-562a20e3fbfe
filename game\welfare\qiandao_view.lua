WelfareView = WelfareView or BaseClass(SafeBaseView)
local Can_DoMove = true

function WelfareView:InitQianDaoView()
	local bundle = "uis/view/welfare_ui_prefab"
	local asset = "cel_qiadao_item"
	local qiandao_cfg = WelfareWGData.Instance:GetQianDaoItemCfg()
	if self.qiandao_grid == nil and qiandao_cfg ~= nil then
		self.qiandao_grid = AsyncBaseGrid.New()
		self.qiandao_grid:CreateCells({col = 8, cell_count = #qiandao_cfg, list_view = self.node_list["ph_qiandao_grid"],
					assetBundle = bundle, assetName = asset, itemRender = QianDaoItemRender})
		self.qiandao_grid:SetStartZeroIndex(false)
		self.qiandao_grid:SetSelectCallBack(BindTool.Bind1(self.OnClickQianDao, self))
	end

	if self.ph_baoxiang_list == nil then
        self.ph_baoxiang_list = {}
        for i = 0, 4 do
        	self.ph_baoxiang_list[i] = BaoXiangItemRender.New(self.node_list.ph_baoxiang_list:FindObj("ph_baoxiang_list" .. i))
        	self.ph_baoxiang_list[i]:SetIndex(i)
        end
    end

	self.is_all_fuzhi = false

	self.qd_light_list = {}
	local point_count = self.node_list["light_point_list"].transform.childCount
	for i = 1, point_count do
		self.qd_light_list[i] = self.node_list["light_point_list"]:FindObj("point_".. i)
	end
end

function WelfareView:ShowIndexCallBackQianDao()
	Can_DoMove = true
	if self.node_list["ph_qiandao_grid"] and self.is_all_fuzhi then
		local qiandao_day = WelfareWGData.Instance:GetQiandaoDay()
		self:TiaoZhuanDay(qiandao_day)
	end
end

--跳转到当天可领取的格子（坐标时根据游戏实际获取的，所以如果改动预制体这里也要相应的改动）
function WelfareView:TiaoZhuanDay(day)
	local hua_dong_float = day < 15 and 0 or 1
	self.node_list.ph_qiandao_grid.scroller:ReloadData(hua_dong_float)
end

function WelfareView:DeleteQianDaoView()
	if self.qiandao_grid then
		self.qiandao_grid:DeleteMe()
		self.qiandao_grid = nil
	end

	if self.ph_baoxiang_list then
		for k, v in pairs(self.ph_baoxiang_list) do
			v:DeleteMe()
		end
		self.ph_baoxiang_list = nil
	end

	if self.qiandao_alert then
		self.qiandao_alert:DeleteMe()
		self.qiandao_alert = nil
	end

	self.buqiandao_day = nil
	self.qd_light_list = nil
	self.qd_old_slider_val = nil
end

function WelfareView:FlushQianDaoView()
	local qiandao_info = WelfareWGData.Instance:GetQianDaoInfo()
	if not next(qiandao_info) then
		return
	end

	local _,month = TimeUtil.FormatSecond5MYHM1(TimeWGCtrl.Instance:GetServerTime())
	local bundle, asset = ResPath.GetRawImagesPNG("a3_fldt_yue"..month)
	self.node_list.raw_image_month.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.raw_image_month.raw_image:SetNativeSize()
    end)
	--self.node_list.label_qindao_num1.text.text = qiandao_info.sign_in_days
	self.node_list.label_qindao_num1.text.text = string.format(Language.Welfare.QianDaoItem, qiandao_info.sign_in_days)
	--self.node_list.qiandao_card_name.text.text =  WelfareWGData.Instance:GetTokenNameBySignInDays()
	local qiandao_cfg = WelfareWGData.Instance:GetQianDaoItemCfg()

	if self.qiandao_grid and qiandao_cfg then
		self.qiandao_grid:SetDataList(qiandao_cfg, 2)
		if not self.is_all_fuzhi then
			self.is_all_fuzhi = true
			self:ShowIndexCallBackQianDao()
		end
	end

	-- 签到进度逻辑
	local add_value, act_light = WelfareWGData.Instance:GetCurQDAccProgress()
	if not self.qd_old_slider_val then
		self.node_list.qd_sign_slider.slider.value = add_value
		self:FlushQDAccLightPoint(act_light)
	else
		self.node_list.qd_sign_slider.slider:DOValue(add_value, 1):OnComplete(function ()
			self:FlushQDAccLightPoint(act_light)
		end)
	end

	self.qd_old_slider_val = add_value
	local leiji_cfg = WelfareWGData.Instance:GetLeiJiItemCfg()
	if self.ph_baoxiang_list and leiji_cfg then
		for k, v in pairs(self.ph_baoxiang_list) do
            v:SetData(leiji_cfg[k + 1])
        end
	end
end

function WelfareView:FlushQDAccLightPoint(num)
	num = num or 0
	if self.qd_light_list then
		for k, v in pairs(self.qd_light_list) do
			v:SetActive(num >= k)
		end
	end
end

-- 点击签到
function WelfareView:OnClickQianDao(cell)
	local data = cell:GetData()
	if nil ~= data and data.flag == QIANDDAO_STATUS.QIANDAO then    --可领取4
		WelfareWGCtrl.Instance:SendWelfareSignInReward(data.day, 0)
	elseif  nil ~= data and data.flag == QIANDDAO_STATUS.BACK then    --找回
		WelfareWGCtrl.Instance:OnclcikQianDaoItem(data.day, cell)
	else
		cell:OpenTip()
	end
end

function WelfareView:OnclcikQianDaoItem(day, cell)
	local other_cfg_data = WelfareWGData.Instance:GetWelfareOtherCfg()[1]
	if other_cfg_data == nil then
		return
	end
	self.buqiandao_day = day
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
	local cost_text = Language.Common.GoldText
	if bind_gold_num >= other_cfg_data.find_sign_in_cost then
		cost_text = Language.Common.BindGoldText
	end

	if other_cfg_data.retroactive_open == 1 then
		cell:OpenTip()
		TipWGCtrl.Instance:ShowSystemMsg(Language.WelfareText.WelfareNotSign)
		return
	end

	if self.qiandao_alert == nil then
		self.qiandao_alert = Alert.New(nil,nil,nil,nil,true)
		self.qiandao_alert:SetShowCheckBox(true)
		self.qiandao_alert:SetLableString(string.format(Language.Welfare.SingFindText, cost_text, other_cfg_data.find_sign_in_cost))
		self.qiandao_alert:SetOkFunc(function ()
				self:OnSignInFind()
		end)
	end

	self.qiandao_alert:Open()
end

function WelfareView:OnSignInFind()
	WelfareWGCtrl.Instance:SendWelfareSignInFindBack(self.buqiandao_day)
end
---------------------------itemRender----------------------------------
QianDaoItemRender = QianDaoItemRender or BaseClass(BaseRender)
HUAFEI_MONEY = 20

function QianDaoItemRender:__init()
end

function QianDaoItemRender:LoadCallBack()
--后面要放到loadcallback
	self.qiandao_cell = ItemCell.New(self.node_list["ph_qindao_cell"])
	XUI.AddClickEventListener(self.node_list.cel_qiandao_item, BindTool.Bind(self.QIanDaoClick,self))
end

function QianDaoItemRender:QIanDaoClick()
	local data = self:GetData()
	if nil ~= data and data.flag == QIANDDAO_STATUS.QIANDAO then    --可领取4
		WelfareWGCtrl.Instance:SendWelfareSignInReward(data.day, 0)
	elseif  nil ~= data and data.flag == QIANDDAO_STATUS.BACK then    --找回
		WelfareWGCtrl.Instance:OnclcikQianDaoItem(data.day, data)
	else
		self:OpenTip()
	end
end

function QianDaoItemRender:__delete()
	if self.qiandao_cell then
		self.qiandao_cell:DeleteMe()
		self.qiandao_cell = nil
	end

	if self.qiandao_eff then
		self.qiandao_eff:DeleteMe()
		self.qiandao_eff = nil
	end

	if self.qiangdao_item_alert then
		self.qiangdao_item_alert:DeleteMe()
		self.qiangdao_item_alert = nil
	end

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.lingqu_effect = nil
end

function QianDaoItemRender:OnFlush()
	if not self.data or not self.data.flag then
		return
	end
	self.node_list.text_vip_num.text.text = ""
	self.node_list.img_vip:SetActive(false)
 	if self.data.vip_times ~= nil and self.data.vip_times ~= 0 then
 		self.node_list.img_vip:SetActive(true)
		local str = Language.Welfare.VipDouble[self.data.vip_times]
		self.node_list.text_vip_num.text.text = string.format(Language.Welfare.VipDoubleShow,self.data.show_vip,str)
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
	if not item_cfg then
		return
	end
	--local qiandao_info = WelfareWGData.Instance:GetQianDaoInfo()
	self.qiandao_cell:SetData(self.data.reward_item)

	if self.data.day == WelfareWGData.Instance:GetQianDaoInfo().cur_day then
		self:DoArrowTween()
	end

	local other_cfg_data = WelfareWGData.Instance:GetWelfareOtherCfg()[1]
	if other_cfg_data == nil then
		return
	end

	self.qiandao_cell:SetEffectRootEnable(self.data.flag ~= QIANDDAO_STATUS.ALREADYGET)
	-- self.qiandao_cell:SetDefaultEff(false)
	self.node_list.fill_flag:SetActive(other_cfg_data.retroactive_open ~= 1 and self.data.flag == QIANDDAO_STATUS.BACK)				--可以找回
	self.node_list.qd_can_get:SetActive(self.data.flag == QIANDDAO_STATUS.QIANDAO)			--可领取
	self.node_list.qd_already_get:SetActive(self.data.flag == QIANDDAO_STATUS.ALREADYGET)	--已经签到
	--XUI.SetGraphicGrey(self.node_list.tween_root, self.data.flag == QIANDDAO_STATUS.ALREADYGET)
end

function QianDaoItemRender:SetLineDir( day )
	local line=self.view.transform.parent:Find("line")
	if line~=nil and day>7 and day<15 or day>21 and day<29 then
		line:Find("lineimg").transform.localRotation = Quaternion.Euler(0, 180, 0)
	else 
		line:Find("lineimg").transform.localRotation = Quaternion.Euler(0, 0, 0)
	end
end

-- 播放箭头动画
function QianDaoItemRender:DoArrowTween()
	if not self.node_list or not Can_DoMove then
		return
	end

	self.node_list.tween_root.transform.localScale = Vector3(1.5, 1.5, 1.5)

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.sequence = DG.Tweening.DOTween.Sequence()
	local tween_scale = self.node_list.tween_root.transform:DOScale(Vector3(1, 1, 1), 0.5)
	self.sequence:Append(tween_scale)
	self.sequence:SetEase(DG.Tweening.Ease.InQuad)
	self.sequence:OnComplete(function ()
		Can_DoMove = false
	end)
end

--右下角特殊处理
function QianDaoItemRender:ReturnNeedShow(data)

end

-- 创建选中特效
function QianDaoItemRender:OpenTip()
	if self.qiandao_cell then
		self.qiandao_cell:OnClick()
	end
end

---------------------------itemRender----------------------------------
BaoXiangItemRender = BaoXiangItemRender or BaseClass(BaseRender)

function BaoXiangItemRender:__init()
	self.acc_cell = ItemCell.New(self.node_list["acc_item"])
	self.acc_cell:SetIsShowTips(false)
	self.acc_cell:AddClickEventListener(BindTool.Bind1(self.OnClickLeiJi, self))
end

function BaoXiangItemRender:__delete()
	if self.acc_cell then
		self.acc_cell:DeleteMe()
		self.acc_cell = nil
	end
end

function BaoXiangItemRender:OnFlush()
	if not self.data or not self.data.flag then
		return
	end

	self.acc_cell:SetData(self.data.reward_item)
	-- QIANDDAO_STATUS.QIANDAO  	可领取
	-- QIANDDAO_STATUS.WAIT			即将领取
	-- QIANDDAO_STATUS.ALREADYGET 	已领取

	self.acc_cell:SetEffectRootEnable(self.data.flag ~= QIANDDAO_STATUS.ALREADYGET)
	self.node_list["acc_red"]:SetActive(self.data.flag == QIANDDAO_STATUS.QIANDAO)
	self.node_list["acc_canget"]:SetActive(self.data.flag == QIANDDAO_STATUS.QIANDAO)
	--self.node_list["acc_normal"]:SetActive(self.data.flag ~= QIANDDAO_STATUS.QIANDAO)
	self.node_list["acc_isget"]:SetActive(self.data.flag == QIANDDAO_STATUS.ALREADYGET)
	self.node_list["day_str_kl"]:SetActive(self.data.flag ~= QIANDDAO_STATUS.NONE)

	local cur_str = string.format(Language.Welfare.QianDaoItem, self.data.heap_times)
	self.node_list.day_str_kl.text.text = cur_str
	self.node_list.day_str.text.text = cur_str

end

function BaoXiangItemRender:OnClickLeiJi(cell)
	local data = cell:GetData()
	if self.data then
		if self.data.flag == QIANDDAO_STATUS.QIANDAO then
			WelfareWGCtrl.Instance:SendQianDaoOperate(self.index)
			AudioService.Instance:PlayRewardAudio()
		else
			TipWGCtrl.Instance:OpenItem(self.data.reward_item, ItemTip.FROM_NORMAL)
		end
	end
end