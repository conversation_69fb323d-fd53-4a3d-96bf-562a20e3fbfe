require("game/weiwoduzun/weiwo_dunzun_wg_data")
require("game/weiwoduzun/weiwo_duzun_view")

WeiWoDunZunWGCtrl = WeiWoDunZunWGCtrl or BaseClass(BaseWGCtrl)

function WeiWoDunZunWGCtrl:__init()
	if WeiWoDunZunWGCtrl.Instance then
		ErrorLog("[WeiWoDunZunWGCtrl] attempt to create singleton twice!")
		return
	end

	WeiWoDunZunWGCtrl.Instance = self
	self.data = WeiWoDunZunWGData.New()
    self.view = WeiWoDunZunView.New(GuideModuleName.WeiWoDunZunView)
  
    self:RegisterAllProtocols()
end

function WeiWoDunZunWGCtrl:__delete()
	WeiWoDunZunWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil
end

function WeiWoDunZunWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRAWeiWoDuZunInfo,"OnSCRAWeiWoDuZunInfo")
	self:RegisterProtocol(SCRAWeiWoDuZunUpdate,"OnSCRAWeiWoDuZunUpdate")
end

function WeiWoDunZunWGCtrl:ReqWeiWoDunZunInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WEIWODUZUN
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function WeiWoDunZunWGCtrl:OnSCRAWeiWoDuZunInfo(protocol)
	--print_error("WeiWoDunZunWGCtrl====",protocol)
	self.data:SetAllTaskInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WeiWoDunZun)
	ViewManager.Instance:FlushView(GuideModuleName.WeiWoDunZunView)
end

function WeiWoDunZunWGCtrl:OnSCRAWeiWoDuZunUpdate(protocol)
	--print_error("OnSCRAWeiWoDuZunUpdate====",protocol)
	self.data:SetSingleTaskInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WeiWoDunZun)
	ViewManager.Instance:FlushView(GuideModuleName.WeiWoDunZunView, nil, "flush_task")
end