SiXiangTeDianTeHuiView = SiXiangTeDianTeHuiView or BaseClass(SafeBaseView)

function SiXiangTeDianTeHuiView:__init(view_name)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/sixiang_call_prefab", "sixiang_tehui_view")
	self:SetMaskBg(true, true)
end

function SiXiangTeDianTeHuiView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitMoneyBar()
end

function SiXiangTeDianTeHuiView:ReleaseCallBack()
	if self.tehui_item_list then
		for k,v in pairs(self.tehui_item_list) do
			v:DeleteMe()
		end
		self.tehui_item_list = nil
	end
	if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
	CountDownManager.Instance:RemoveCountDown("sixiang_tedian_tehui")
end

function SiXiangTeDianTeHuiView:ShowIndexCallBack()
	self.need_play_show_tween = true
	self:PlayShowPanelTween()
end

function SiXiangTeDianTeHuiView:OnFlush(param_t)
	self:RefreshView()
end

function SiXiangTeDianTeHuiView:InitParam()
	self.need_play_show_tween = false
end

function SiXiangTeDianTeHuiView:InitPanel()
	local res_async_loader = AllocResAsyncLoader(self, "sixiang_tehui_item")
	res_async_loader:Load("uis/view/sixiang_call_prefab", "sixiang_tehui_item", nil,
		function(new_obj)
			if IsNil(new_obj) then
				return
			end
			local tehui_item_list = {}
			for i=1,3 do
				local item_root = self.node_list["tehui_root_" .. i]
				if item_root then
					local obj = ResMgr:Instantiate(new_obj)
					obj.transform:SetParent(item_root.transform, false)
					tehui_item_list[i] = SiXiangTeHuiItem.New(obj)
					tehui_item_list[i]:SetIndex(i)
				end
			end
			self.tehui_item_list = tehui_item_list
			self:FlushTeHuiItemList()
			self:PlayShowPanelTween()
		end)
end

function SiXiangTeDianTeHuiView:InitMoneyBar()
	self.money_bar = MoneyBar.New()
    local bundle, asset = ResPath.GetWidgets("MoneyBar")
    local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
    self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
end

function SiXiangTeDianTeHuiView:RefreshView()
	self:FlushActTime()
	self:FlushTeHuiItemList()
end

function SiXiangTeDianTeHuiView:FlushTeHuiItemList()
	local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(YuanShenZhaoHuanSubActId.TeHuiZhaoHuan)
	if not IsEmptyTable(cfg_list) and not IsEmptyTable(self.tehui_item_list) then
		local item_list = self.tehui_item_list
		for i=1,#item_list do
			item_list[i]:SetData(cfg_list[i])
		end
	end
end

---[[ 活动结束倒计时
function SiXiangTeDianTeHuiView:FlushActTime()
	CountDownManager.Instance:RemoveCountDown("sixiang_tedian_tehui")
    local sever_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = SiXiangTeDianWGData.Instance:GetTeDianEndTimeStamp()
    if end_time <= sever_time then
        self.node_list.time_label.text.text = ""
        return
    end

    self:UpdateCountDown(sever_time, end_time)

    CountDownManager.Instance:AddCountDown(
        "sixiang_tedian_tehui",
        BindTool.Bind(self.UpdateCountDown, self),
        BindTool.Bind(self.FlushActTime, self),
        end_time,
        nil,
        1
    )
end

function SiXiangTeDianTeHuiView:UpdateCountDown(elapse_time, total_time)
	local time_str = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
	self.node_list.time_label.text.text = string.format(Language.SiXiangCall.CloseTimeDesc1, time_str)
end
--]]

function SiXiangTeDianTeHuiView:PlayShowPanelTween()
	if not self.tehui_item_list or not self.need_play_show_tween then
		return
	end
	local item_list = self.tehui_item_list
	for i=1,#item_list do
		item_list[i]:PlayShowTween()
	end
end

----------------------------------------------------------------

SiXiangTeHuiItem = SiXiangTeHuiItem or BaseClass(BaseRender)

function SiXiangTeHuiItem:__delete()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
	end
	if self.show_tween then
		self.show_tween:Kill()
		self.show_tween = nil
	end
end

function SiXiangTeHuiItem:LoadCallBack()
	self.save_act_mark = -1

	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))

	local cell_root = self.node_list.item_cell_root
	local item_list = {}
	for i=1,4 do
		item_list[i] = ItemCell.New(cell_root)
	end
	self.item_list = item_list
end

function SiXiangTeHuiItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	self:FlushItem()
	self:FlushBuyNum()
end

function SiXiangTeHuiItem:FlushBuyNum()
	local data = self:GetData()
	local act_info = SiXiangTeDianWGData.Instance:GetSubActSaleInfo(YuanShenZhaoHuanSubActId.TeHuiZhaoHuan, data.product_id)
	local buy_num = act_info and act_info.buy_num or 0
	local limit_num = data.limit_buy_times or 0
	if limit_num > buy_num then
		self.node_list.limit_lbl.text.text = string.format(Language.SiXiangCall.TeHuiLimitText, limit_num - buy_num, limit_num)
	else
		self.node_list.limit_lbl.text.text = Language.SiXiangCall.AllSellStr
	end
	self.node_list.all_sell_img:SetActive(buy_num >= limit_num)
	self.node_list.buy_btn:SetActive(buy_num < limit_num)
end

function SiXiangTeHuiItem:FlushItem()
	local data = self:GetData()
	local mark = data.cycle * 100 + data.product_id
	if mark == self.save_act_mark then
		return
	end
	self.save_act_mark = mark

	local reward_list = SortTableKey(data.item)
	local item_list = self.item_list
	for i=1,#item_list do
		if reward_list[i] then
			item_list[i]:SetData(reward_list[i])
			item_list[i]:SetVisible(true)
		else
			item_list[i]:SetVisible(false)
		end
	end
	
	self.node_list.title_lable.text.text = data.name
	self.node_list.price_lbl.text.text = data.special_sale_price
	self.node_list.dicount_lbl.text.text = string.format(Language.SiXiangCall.ZheKou, data.discount)
end

function SiXiangTeHuiItem:OnClickBuyBtn()
	local data = self:GetData()
	if not IsEmptyTable(data) then
		SiXiangTeDianWGCtrl.Instance:BuySiXiangTeDian(data.subactivity_id, data.product_id)
	end
end

function SiXiangTeHuiItem:PlayShowTween()
	if self.show_tween then
		return
	end

	local index = self:GetIndex()

	local bg_canvas_group = self.node_list.root_bg.canvas_group
	bg_canvas_group.alpha = 0

	local bg_rect = self.node_list.root_bg.rect
	local size_delta = RectTransform.GetSizeDelta(bg_rect)
	--RectTransform.SetSizeDeltaXY(bg_rect, size_delta.x, 0)

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_alpha = bg_canvas_group:DoAlpha(0, 1, 0.5)
	local tween_size = bg_rect:DOSizeDelta(size_delta, 0.5)
	--tween_sequence:AppendInterval(0.2 * (index - 1))
	--tween_sequence:Append(tween_size)
	tween_sequence:Join(tween_alpha)
	tween_sequence:OnComplete(function ()
		self.show_tween:Kill()
		self.show_tween = nil
	end)

	self.show_tween = tween_sequence
end