NearbySingleView = NearbySingleView or BaseClass(SafeBaseView)

function NearbySingleView:__init()
	self:LoadConfig()
	self.is_any_click_close = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function NearbySingleView:__delete()

end

function NearbySingleView:LoadConfig()
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_nearby_single")
end


function NearbySingleView:ReleaseCallBack()
	if self.nearbysingle_list ~= nil then
		self.nearbysingle_list:RemoveAllItem()
		self.nearbysingle_list = nil
	end
end

function NearbySingleView:LoadCallBack()
	self.node_list["layout_blank_tip"]:SetActive(true)
	XUI.AddClickEventListener(self.node_list.btn_close,BindTool.Bind1(self.Close,self))
	if not self.nearbysingle_list then
		self.nearbysingle_list = AsyncListView.New(MarryNearbySingleRender, self.node_list["ph_nearby_single_list"])
	end
end

function NearbySingleView:OnFlush(param_list, index)
	if self.nearbysingle_list == nil then
		print_error("can not init the list!!!!")
		return
	end
	local my_sex =  GameVoManager.Instance:GetMainRoleVo().sex
	local role_list = MarryWGData.Instance:GetSingleList()--Scene.Instance:GetRoleList()
	
	if #role_list ==0 then
		self.node_list["layout_blank_tip"]:SetActive(true)
	else
		self.node_list["layout_blank_tip"]:SetActive(false)
	end
	self.nearbysingle_list:SetDataList(role_list,0)
end

-------------itemrender--------------
MarryNearbySingleRender = MarryNearbySingleRender or BaseClass(BaseRender)
function MarryNearbySingleRender:__init()
	self.is_choose = false
	self.head_frame_cell = BaseHeadCell.New(self.node_list.head_frame)
	XUI.AddClickEventListener(self.node_list["btn_add_friends"], BindTool.Bind1(self.OnClickAddFriends, self))
end

function MarryNearbySingleRender:__delete()
	if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end
	self.data = nil

	if self.head_frame_cell then
		self.head_frame_cell:DeleteMe()
		self.head_frame_cell = nil
	end
end

function MarryNearbySingleRender:OnFlush()
	if self.data == nil then return end

	self.node_list["label_name"].text.text = self.data.name
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list["dianfen_img"]:SetActive(is_vis)
	self.node_list["TextLevel"].text.text = level
    local fashion_photoframe = CheckList(self.data, "appearance", "fashion_photoframe") or 0
	local data = {role_id = self.data.role_id, prof = self.data.prof, fashion_photoframe = fashion_photoframe, sex = self.data.sex}
    self.head_frame_cell:SetData(data)
    self.head_frame_cell:SetImgBg(false)
	-- if self.data.appearance.fashion_photoframe > 0 then
	-- 	MarryView.ChangeRoleFrame(self.node_list["head_icon_frame"], self.data.appearance.fashion_photoframe)
	-- end
end

function MarryNearbySingleRender:OnClickAddFriends()
	if self.data ~= nil then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Marry.AddFriendMarry,self.data.name))
		SocietyWGCtrl.Instance:AddFriend(self.data.role_id, 1)
	end
end

function MarryNearbySingleRender:CreateSelectEffect()
end
