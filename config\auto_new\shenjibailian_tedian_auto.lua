-- S-圣武降世-百炼特典.xls
local item_table={
[1]={item_id=48454,num=1,is_bind=0},
[2]={item_id=29855,num=1,is_bind=0},
[3]={item_id=29854,num=1,is_bind=0},
[4]={item_id=27946,num=2,is_bind=1},
[5]={item_id=39196,num=1,is_bind=0},
[6]={item_id=36977,num=35,is_bind=1},
[7]={item_id=29853,num=2,is_bind=1},
[8]={item_id=27946,num=5,is_bind=1},
[9]={item_id=39197,num=1,is_bind=0},
[10]={item_id=36977,num=50,is_bind=1},
[11]={item_id=29853,num=3,is_bind=1},
[12]={item_id=27946,num=12,is_bind=1},
[13]={item_id=39198,num=1,is_bind=0},
[14]={item_id=36977,num=70,is_bind=1},
[15]={item_id=29853,num=4,is_bind=1},
[16]={item_id=16507,num=1,is_bind=1},
[17]={item_id=16512,num=1,is_bind=1},
[18]={item_id=16552,num=1,is_bind=1},
[19]={item_id=16557,num=1,is_bind=1},
[20]={item_id=16562,num=1,is_bind=1},
[21]={item_id=26357,num=1,is_bind=1},
[22]={item_id=26357,num=3,is_bind=1},
[23]={item_id=26357,num=4,is_bind=1},
[24]={item_id=26357,num=5,is_bind=1},
[25]={item_id=26500,num=1,is_bind=1},
[26]={item_id=26500,num=2,is_bind=1},
[27]={item_id=26500,num=3,is_bind=1},
[28]={item_id=26500,num=4,is_bind=1},
[29]={item_id=26500,num=5,is_bind=1},
[30]={item_id=16508,num=1,is_bind=1},
[31]={item_id=16513,num=1,is_bind=1},
[32]={item_id=16504,num=1,is_bind=1},
[33]={item_id=16509,num=1,is_bind=1},
[34]={item_id=16514,num=1,is_bind=1},
[35]={item_id=16502,num=1,is_bind=1},
[36]={item_id=16501,num=1,is_bind=1},
[37]={item_id=16506,num=1,is_bind=1},
[38]={item_id=16511,num=1,is_bind=1},
[39]={item_id=16551,num=1,is_bind=1},
[40]={item_id=16556,num=1,is_bind=1},
[41]={item_id=16561,num=1,is_bind=1},
[42]={item_id=16500,num=1,is_bind=1},
[43]={item_id=16505,num=1,is_bind=1},
[44]={item_id=16510,num=1,is_bind=1},
[45]={item_id=16550,num=1,is_bind=1},
[46]={item_id=16555,num=1,is_bind=1},
[47]={item_id=16560,num=1,is_bind=1},
[48]={item_id=16554,num=1,is_bind=1},
[49]={item_id=16559,num=1,is_bind=1},
[50]={item_id=16564,num=1,is_bind=1},
[51]={item_id=16503,num=1,is_bind=1},
[52]={item_id=16553,num=1,is_bind=1},
[53]={item_id=16558,num=1,is_bind=1},
[54]={item_id=16563,num=1,is_bind=1},
[55]={item_id=48134,num=1,is_bind=0},
[56]={item_id=26357,num=2,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
cycle_subactivity={
{},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

cycle_subactivity_meta_table_map={
},
subactivity_relation={
{subactivity_show=0,},
{subactivity_id=2,},
{subactivity_id=3,pre_subactivity=2,},
{subactivity_id=4,subactivity_show=2,},
{subactivity_id=5,},
{subactivity_id=6,subactivity_show=3,}
},

subactivity_relation_meta_table_map={
[5]=4,	-- depth:1
},
subactivity_sale={
{subactivity_id=2,limit_buy_type=1,subactivity_name="1折十连",},
{subactivity_id=3,special_sale_price=50,subactivity_name="2折十连",},
{subactivity_id=4,special_sale_price=160,item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},limit_buy_type=1,name="<color=#ff0000>防御圣武自选</color>",subactivity_name="防御圣武",},
{price_type=1,special_sale_price=980,discount=3,item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7]},limit_buy_times=4,name="<color=#ff0000>圣武超值礼包</color>",},
{product_id=2,special_sale_price=1880,item={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11]},name="<color=#ff0000>圣武极品礼包</color>",},
{product_id=3,special_sale_price=4680,item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15]},name="<color=#ff0000>圣武至尊礼包</color>",},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

subactivity_sale_meta_table_map={
[7]=1,	-- depth:1
[8]=2,	-- depth:1
[26]=8,	-- depth:2
[25]=7,	-- depth:2
[13]=25,	-- depth:3
[14]=26,	-- depth:3
[20]=14,	-- depth:4
[19]=13,	-- depth:4
[28]=4,	-- depth:1
[27]=3,	-- depth:1
[22]=28,	-- depth:2
[21]=27,	-- depth:2
[15]=21,	-- depth:3
[16]=22,	-- depth:3
[10]=16,	-- depth:4
[9]=15,	-- depth:4
[6]=4,	-- depth:1
[5]=4,	-- depth:1
[18]=6,	-- depth:2
[29]=5,	-- depth:2
[12]=18,	-- depth:3
[11]=29,	-- depth:3
[23]=11,	-- depth:4
[24]=12,	-- depth:4
[17]=23,	-- depth:5
[30]=24,	-- depth:5
},
first_single_lotto={
{},
{first_single_lotto_reward=2,reward_item=item_table[16],},
{first_single_lotto_reward=3,reward_item=item_table[17],},
{first_single_lotto_reward=4,reward_item=item_table[18],},
{first_single_lotto_reward=5,reward_item=item_table[19],},
{first_single_lotto_reward=6,reward_item=item_table[20],},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

first_single_lotto_meta_table_map={
[28]=4,	-- depth:1
[20]=2,	-- depth:1
[26]=20,	-- depth:2
[24]=6,	-- depth:1
[23]=5,	-- depth:1
[22]=28,	-- depth:2
[21]=3,	-- depth:1
[27]=21,	-- depth:2
[15]=27,	-- depth:3
[17]=23,	-- depth:2
[16]=22,	-- depth:3
[29]=17,	-- depth:3
[14]=26,	-- depth:3
[12]=24,	-- depth:2
[11]=29,	-- depth:4
[10]=16,	-- depth:4
[9]=15,	-- depth:4
[8]=14,	-- depth:4
[18]=12,	-- depth:3
[30]=18,	-- depth:4
},
first_multi_lotto_group={
{},
{first_multi_lotto_group=2,group_count=9,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,}
},

first_multi_lotto_group_meta_table_map={
[4]=2,	-- depth:1
[6]=4,	-- depth:2
[8]=6,	-- depth:3
[10]=8,	-- depth:4
},
first_multi_lotto={
{first_single_lotto_id=1,reward_item=item_table[21],},
{first_multi_lotto_group=1,reward_type=1,broadcast=1,},
{first_single_lotto_id=3,reward_item=item_table[22],},
{first_single_lotto_id=4,reward_item=item_table[23],},
{first_single_lotto_id=5,reward_item=item_table[24],},
{first_single_lotto_id=1,reward_item=item_table[25],},
{reward_item=item_table[26],},
{first_single_lotto_id=3,reward_item=item_table[27],},
{first_single_lotto_id=4,reward_item=item_table[28],},
{first_single_lotto_id=5,reward_item=item_table[29],},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

first_multi_lotto_meta_table_map={
[35]=7,	-- depth:1
[17]=35,	-- depth:2
[26]=17,	-- depth:3
[44]=26,	-- depth:4
[19]=9,	-- depth:1
[27]=8,	-- depth:1
[25]=6,	-- depth:1
[46]=19,	-- depth:2
[34]=25,	-- depth:2
[28]=46,	-- depth:3
[20]=10,	-- depth:1
[47]=20,	-- depth:2
[36]=27,	-- depth:2
[16]=34,	-- depth:3
[29]=47,	-- depth:3
[37]=28,	-- depth:4
[38]=29,	-- depth:4
[43]=16,	-- depth:4
[45]=36,	-- depth:3
[18]=45,	-- depth:4
[21]=2,	-- depth:1
[12]=21,	-- depth:2
[39]=12,	-- depth:3
[30]=39,	-- depth:4
[1]=2,	-- depth:1
[5]=1,	-- depth:2
[4]=1,	-- depth:2
[3]=1,	-- depth:2
[32]=4,	-- depth:3
[33]=5,	-- depth:3
[23]=32,	-- depth:4
[22]=3,	-- depth:3
[15]=33,	-- depth:4
[14]=23,	-- depth:5
[13]=22,	-- depth:4
[11]=1,	-- depth:2
[40]=13,	-- depth:5
[41]=14,	-- depth:6
[42]=15,	-- depth:5
[31]=40,	-- depth:6
[24]=42,	-- depth:6
},
special_multi_lotto_group={
{},
{special_multi_lotto_group=2,special_group_count=9,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,}
},

special_multi_lotto_group_meta_table_map={
[4]=2,	-- depth:1
[6]=4,	-- depth:2
[8]=6,	-- depth:3
[10]=8,	-- depth:4
},
special_multi_lotto={
{special_multi_lotto_group=1,reward_type=1,broadcast=1,},
{special_single_lotto_id=2,reward_item=item_table[30],},
{special_single_lotto_id=3,reward_item=item_table[31],},
{special_single_lotto_id=4,reward_item=item_table[32],},
{special_single_lotto_id=5,reward_item=item_table[33],},
{special_single_lotto_id=6,reward_item=item_table[34],},
{reward_item=item_table[35],},
{special_single_lotto_id=2,reward_item=item_table[16],},
{special_single_lotto_id=3,reward_item=item_table[17],},
{special_single_lotto_id=4,reward_item=item_table[18],},
{special_single_lotto_id=5,reward_item=item_table[19],},
{special_single_lotto_id=6,reward_item=item_table[20],},
{special_single_lotto_id=7,reward_item=item_table[36],},
{special_single_lotto_id=8,reward_item=item_table[37],},
{special_single_lotto_id=9,reward_item=item_table[38],},
{special_single_lotto_id=10,reward_item=item_table[39],},
{special_single_lotto_id=11,reward_item=item_table[40],},
{special_single_lotto_id=12,reward_item=item_table[41],},
{special_single_lotto_id=13,reward_item=item_table[42],},
{special_single_lotto_id=14,reward_item=item_table[43],},
{special_single_lotto_id=15,reward_item=item_table[44],},
{special_single_lotto_id=16,reward_item=item_table[45],},
{special_single_lotto_id=17,reward_item=item_table[46],},
{special_single_lotto_id=18,reward_item=item_table[47],},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

special_multi_lotto_meta_table_map={
[79]=7,	-- depth:1
[55]=79,	-- depth:2
[103]=55,	-- depth:3
[31]=103,	-- depth:4
[83]=11,	-- depth:1
[85]=13,	-- depth:1
[86]=14,	-- depth:1
[87]=15,	-- depth:1
[84]=12,	-- depth:1
[88]=16,	-- depth:1
[89]=17,	-- depth:1
[81]=9,	-- depth:1
[80]=8,	-- depth:1
[72]=24,	-- depth:1
[90]=18,	-- depth:1
[71]=23,	-- depth:1
[70]=22,	-- depth:1
[69]=21,	-- depth:1
[68]=20,	-- depth:1
[67]=19,	-- depth:1
[82]=10,	-- depth:1
[91]=67,	-- depth:2
[107]=83,	-- depth:2
[93]=69,	-- depth:2
[118]=70,	-- depth:2
[117]=93,	-- depth:3
[116]=68,	-- depth:2
[115]=91,	-- depth:3
[114]=90,	-- depth:2
[113]=89,	-- depth:2
[112]=88,	-- depth:2
[111]=87,	-- depth:2
[92]=116,	-- depth:3
[110]=86,	-- depth:2
[108]=84,	-- depth:2
[66]=114,	-- depth:3
[106]=82,	-- depth:2
[105]=81,	-- depth:2
[104]=80,	-- depth:2
[96]=72,	-- depth:2
[95]=71,	-- depth:2
[94]=118,	-- depth:3
[109]=85,	-- depth:2
[65]=113,	-- depth:3
[60]=108,	-- depth:3
[63]=111,	-- depth:3
[46]=94,	-- depth:4
[45]=117,	-- depth:4
[44]=92,	-- depth:4
[43]=115,	-- depth:4
[42]=66,	-- depth:4
[64]=112,	-- depth:3
[40]=64,	-- depth:4
[39]=63,	-- depth:4
[38]=110,	-- depth:3
[37]=109,	-- depth:3
[36]=60,	-- depth:4
[35]=107,	-- depth:3
[34]=106,	-- depth:3
[33]=105,	-- depth:3
[32]=104,	-- depth:3
[47]=95,	-- depth:3
[48]=96,	-- depth:3
[41]=65,	-- depth:4
[120]=48,	-- depth:4
[62]=38,	-- depth:4
[61]=37,	-- depth:4
[119]=47,	-- depth:4
[59]=35,	-- depth:4
[58]=34,	-- depth:4
[57]=33,	-- depth:4
[56]=32,	-- depth:4
[25]=1,	-- depth:1
[97]=25,	-- depth:2
[49]=97,	-- depth:3
[73]=49,	-- depth:4
[2]=1,	-- depth:1
[3]=2,	-- depth:2
[4]=2,	-- depth:2
[5]=2,	-- depth:2
[6]=2,	-- depth:2
[74]=2,	-- depth:2
[51]=3,	-- depth:3
[26]=74,	-- depth:3
[27]=51,	-- depth:4
[28]=4,	-- depth:3
[29]=5,	-- depth:3
[30]=6,	-- depth:3
[52]=28,	-- depth:4
[54]=30,	-- depth:4
[78]=54,	-- depth:5
[77]=29,	-- depth:4
[50]=26,	-- depth:4
[101]=77,	-- depth:5
[100]=52,	-- depth:5
[99]=27,	-- depth:5
[98]=50,	-- depth:5
[76]=100,	-- depth:6
[75]=99,	-- depth:6
[53]=101,	-- depth:6
[102]=78,	-- depth:6
},
reward={
{reward_type=1,broadcast=1,},
{reward_id=2,reward_item=item_table[33],},
{reward_id=3,reward_item=item_table[34],},
{reward_id=4,reward_item=item_table[48],},
{reward_id=5,reward_item=item_table[49],},
{reward_id=6,reward_item=item_table[50],},
{reward_id=7,reward_item=item_table[51],},
{reward_id=8,reward_item=item_table[30],},
{reward_id=9,reward_item=item_table[31],},
{reward_id=10,reward_item=item_table[52],},
{reward_id=11,reward_item=item_table[53],},
{reward_id=12,reward_item=item_table[54],},
{reward_id=13,reward_item=item_table[35],},
{reward_id=14,reward_item=item_table[16],},
{reward_id=15,reward_item=item_table[17],},
{reward_id=16,reward_item=item_table[18],},
{reward_id=17,reward_item=item_table[19],},
{reward_id=18,reward_item=item_table[20],},
{reward_id=19,reward_item=item_table[36],},
{reward_id=20,reward_item=item_table[37],},
{reward_id=21,reward_item=item_table[38],},
{reward_id=22,reward_item=item_table[39],},
{reward_id=23,reward_item=item_table[40],},
{reward_id=24,reward_item=item_table[41],},
{reward_id=25,reward_item=item_table[42],},
{reward_id=26,reward_item=item_table[43],},
{reward_id=27,reward_item=item_table[44],},
{reward_id=28,reward_item=item_table[45],},
{reward_id=29,reward_item=item_table[46],},
{reward_id=30,reward_item=item_table[47],},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

reward_meta_table_map={
[109]=19,	-- depth:1
[108]=18,	-- depth:1
[107]=17,	-- depth:1
[106]=16,	-- depth:1
[105]=15,	-- depth:1
[104]=14,	-- depth:1
[103]=13,	-- depth:1
[91]=1,	-- depth:1
[90]=30,	-- depth:1
[89]=29,	-- depth:1
[88]=28,	-- depth:1
[76]=106,	-- depth:2
[77]=107,	-- depth:2
[78]=108,	-- depth:2
[79]=109,	-- depth:2
[80]=20,	-- depth:1
[81]=21,	-- depth:1
[82]=22,	-- depth:1
[83]=23,	-- depth:1
[110]=80,	-- depth:2
[84]=24,	-- depth:1
[85]=25,	-- depth:1
[86]=26,	-- depth:1
[87]=27,	-- depth:1
[111]=81,	-- depth:2
[121]=91,	-- depth:2
[113]=83,	-- depth:2
[148]=88,	-- depth:2
[147]=87,	-- depth:2
[146]=86,	-- depth:2
[145]=85,	-- depth:2
[144]=84,	-- depth:2
[143]=113,	-- depth:3
[142]=82,	-- depth:2
[141]=111,	-- depth:3
[140]=110,	-- depth:3
[139]=79,	-- depth:3
[138]=78,	-- depth:3
[112]=142,	-- depth:3
[137]=77,	-- depth:3
[135]=105,	-- depth:2
[134]=104,	-- depth:2
[133]=103,	-- depth:2
[149]=89,	-- depth:2
[120]=90,	-- depth:2
[119]=149,	-- depth:3
[118]=148,	-- depth:3
[117]=147,	-- depth:3
[116]=146,	-- depth:3
[115]=145,	-- depth:3
[114]=144,	-- depth:3
[136]=76,	-- depth:3
[74]=134,	-- depth:3
[75]=135,	-- depth:3
[45]=75,	-- depth:4
[31]=121,	-- depth:3
[43]=133,	-- depth:3
[44]=74,	-- depth:4
[73]=43,	-- depth:4
[46]=136,	-- depth:4
[47]=137,	-- depth:4
[48]=138,	-- depth:4
[49]=139,	-- depth:4
[50]=140,	-- depth:4
[51]=141,	-- depth:4
[52]=112,	-- depth:4
[53]=143,	-- depth:4
[54]=114,	-- depth:4
[55]=115,	-- depth:4
[56]=116,	-- depth:4
[150]=120,	-- depth:3
[58]=118,	-- depth:4
[57]=117,	-- depth:4
[61]=31,	-- depth:4
[60]=150,	-- depth:4
[59]=119,	-- depth:4
[12]=1,	-- depth:1
[10]=12,	-- depth:2
[9]=12,	-- depth:2
[11]=12,	-- depth:2
[7]=12,	-- depth:2
[6]=12,	-- depth:2
[5]=12,	-- depth:2
[4]=12,	-- depth:2
[3]=12,	-- depth:2
[2]=12,	-- depth:2
[8]=12,	-- depth:2
[128]=8,	-- depth:3
[124]=4,	-- depth:3
[125]=5,	-- depth:3
[126]=6,	-- depth:3
[127]=7,	-- depth:3
[129]=9,	-- depth:3
[71]=11,	-- depth:3
[131]=71,	-- depth:4
[132]=12,	-- depth:2
[35]=125,	-- depth:4
[34]=124,	-- depth:4
[33]=3,	-- depth:3
[32]=2,	-- depth:3
[130]=10,	-- depth:3
[123]=33,	-- depth:4
[37]=127,	-- depth:4
[36]=126,	-- depth:4
[92]=32,	-- depth:4
[93]=123,	-- depth:5
[94]=34,	-- depth:5
[95]=35,	-- depth:5
[96]=36,	-- depth:5
[97]=37,	-- depth:5
[98]=128,	-- depth:4
[99]=129,	-- depth:4
[100]=130,	-- depth:4
[101]=131,	-- depth:5
[102]=132,	-- depth:3
[62]=92,	-- depth:5
[63]=93,	-- depth:6
[64]=94,	-- depth:6
[65]=95,	-- depth:6
[66]=96,	-- depth:6
[67]=97,	-- depth:6
[68]=98,	-- depth:5
[69]=99,	-- depth:5
[70]=100,	-- depth:5
[42]=102,	-- depth:4
[41]=101,	-- depth:6
[40]=70,	-- depth:6
[39]=69,	-- depth:6
[38]=68,	-- depth:6
[122]=62,	-- depth:6
[72]=42,	-- depth:5
},
group_multi_limit={
{},
{cycle=1,},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

group_multi_limit_meta_table_map={
},
group_cycle_limit={
{},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

group_cycle_limit_meta_table_map={
},
pool_cycle={
{},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

pool_cycle_meta_table_map={
},
reward_cycle={
{},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,},
{reward_id=11,},
{reward_id=12,},
{cycle=2,},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,},
{reward_id=11,},
{cycle=2,},
{cycle=3,},
{reward_id=8,},
{cycle=3,},
{reward_id=10,},
{reward_id=11,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{reward_id=9,},
{reward_id=10,},
{reward_id=11,},
{reward_id=12,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

reward_cycle_meta_table_map={
[28]=4,	-- depth:1
[20]=2,	-- depth:1
[26]=20,	-- depth:2
[24]=20,	-- depth:2
[23]=24,	-- depth:3
[22]=23,	-- depth:4
[21]=22,	-- depth:5
[27]=21,	-- depth:6
[15]=27,	-- depth:7
[17]=15,	-- depth:8
[16]=17,	-- depth:9
[29]=17,	-- depth:9
[14]=16,	-- depth:10
[12]=24,	-- depth:3
[11]=12,	-- depth:4
[10]=11,	-- depth:5
[9]=10,	-- depth:6
[8]=9,	-- depth:7
[18]=12,	-- depth:4
[30]=18,	-- depth:5
},
client_sub_act={
{name="act_name_1",open_param="XianQi_ProbabilityUpView",},
{act_id=2,name="act_name_2",item_bg="act_bg_2",},
{act_id=3,name="act_name_3",},
{act_id=4,item_bg="act_bg_4",open_param="XianQi_ZhenXuanView",},
{act_id=5,},
{act_id=6,name="act_name_5",item_bg="act_bg_5",open_param="XianQi_TeHuiZhaoHuanView",},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{act_id=4,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{act_id=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

client_sub_act_meta_table_map={
[27]=3,	-- depth:1
[25]=1,	-- depth:1
[21]=27,	-- depth:2
[19]=25,	-- depth:2
[13]=19,	-- depth:3
[9]=21,	-- depth:3
[15]=9,	-- depth:4
[7]=13,	-- depth:4
[5]=4,	-- depth:1
[10]=4,	-- depth:1
[28]=10,	-- depth:2
[26]=2,	-- depth:1
[23]=5,	-- depth:2
[22]=23,	-- depth:3
[8]=26,	-- depth:2
[20]=8,	-- depth:3
[17]=23,	-- depth:3
[16]=17,	-- depth:4
[29]=17,	-- depth:4
[14]=20,	-- depth:4
[11]=29,	-- depth:5
[18]=6,	-- depth:1
[24]=18,	-- depth:2
[12]=24,	-- depth:3
[30]=12,	-- depth:4
},
probability_up_show={
{},
{shenji_id=16504,},
{shenji_id=16508,},
{shenji_id=16509,},
{shenji_id=16513,},
{shenji_id=16514,},
{shenji_id=16553,},
{shenji_id=16554,},
{shenji_id=16558,},
{shenji_id=16559,},
{shenji_id=16563,},
{shenji_id=16564,},
{cycle=2,},
{cycle=2,},
{shenji_id=16508,},
{shenji_id=16509,},
{shenji_id=16513,},
{shenji_id=16514,},
{shenji_id=16553,},
{shenji_id=16554,},
{shenji_id=16558,},
{shenji_id=16559,},
{shenji_id=16563,},
{shenji_id=16564,},
{cycle=3,},
{shenji_id=16504,},
{shenji_id=16508,},
{shenji_id=16509,},
{shenji_id=16513,},
{cycle=3,},
{shenji_id=16553,},
{shenji_id=16554,},
{shenji_id=16558,},
{shenji_id=16559,},
{shenji_id=16563,},
{shenji_id=16564,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{shenji_id=16509,},
{shenji_id=16513,},
{shenji_id=16514,},
{shenji_id=16553,},
{shenji_id=16554,},
{shenji_id=16558,},
{shenji_id=16559,},
{shenji_id=16563,},
{shenji_id=16564,},
{cycle=5,},
{shenji_id=16504,},
{shenji_id=16508,},
{shenji_id=16509,},
{shenji_id=16513,},
{shenji_id=16514,},
{shenji_id=16553,},
{shenji_id=16554,},
{shenji_id=16558,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

probability_up_show_meta_table_map={
[39]=3,	-- depth:1
[40]=39,	-- depth:2
[41]=40,	-- depth:3
[42]=41,	-- depth:4
[43]=42,	-- depth:5
[44]=43,	-- depth:6
[45]=44,	-- depth:7
[46]=45,	-- depth:8
[47]=46,	-- depth:9
[58]=46,	-- depth:9
[50]=58,	-- depth:10
[51]=50,	-- depth:11
[52]=51,	-- depth:12
[53]=52,	-- depth:13
[38]=50,	-- depth:11
[55]=53,	-- depth:14
[56]=55,	-- depth:15
[57]=56,	-- depth:16
[48]=38,	-- depth:12
[54]=57,	-- depth:17
[30]=54,	-- depth:18
[35]=30,	-- depth:19
[14]=38,	-- depth:12
[15]=14,	-- depth:13
[16]=15,	-- depth:14
[17]=16,	-- depth:15
[18]=17,	-- depth:16
[19]=18,	-- depth:17
[20]=19,	-- depth:18
[21]=20,	-- depth:19
[22]=21,	-- depth:20
[36]=35,	-- depth:20
[23]=22,	-- depth:21
[26]=36,	-- depth:21
[27]=26,	-- depth:22
[28]=27,	-- depth:23
[29]=28,	-- depth:24
[59]=23,	-- depth:22
[31]=29,	-- depth:25
[32]=31,	-- depth:26
[33]=32,	-- depth:27
[34]=33,	-- depth:28
[24]=23,	-- depth:22
[60]=24,	-- depth:23
},
item_random_desc={
[1]={number=1,random_count=0.0257,},
[2]={number=2,random_count=0.0257,},
[3]={number=3,random_count=0.0257,},
[4]={number=4,random_count=0.0045,},
[5]={number=5,random_count=0.0045,},
[6]={number=6,random_count=0.0045,},
[7]={number=7,item_id=16502,},
[8]={number=8,item_id=16507,},
[9]={number=9,item_id=16512,},
[10]={number=10,item_id=16552,},
[11]={number=11,item_id=16557,},
[12]={number=12,item_id=16562,},
[13]={number=13,random_count=0.0606,},
[14]={number=14,random_count=0.0606,},
[15]={number=15,random_count=0.0606,},
[16]={number=16,random_count=0.0606,},
[17]={number=17,random_count=0.0606,},
[18]={number=18,random_count=0.0606,},
[19]={number=19,item_id=16500,},
[20]={number=20,item_id=16505,},
[21]={number=21,item_id=16510,},
[22]={number=22,item_id=16550,},
[23]={number=23,item_id=16555,},
[24]={number=24,item_id=16560,},
[25]={number=25,random_count=0.63,},
[26]={number=26,random_count=0.65,},
[27]={number=27,random_count=0.67,},
[28]={number=28,random_count=0.69,},
[29]={number=29,random_count=0.71,},
[30]={number=30,random_count=0.73,},
[31]={number=31,random_count=0.75,},
[32]={number=32,random_count=0.77,},
[33]={number=33,random_count=0.79,},
[34]={number=34,random_count=0.81,},
[35]={number=35,random_count=0.83,},
[36]={number=36,random_count=0.85,},
[37]={number=37,random_count=0.87,},
[38]={number=38,random_count=0.89,},
[39]={number=39,random_count=0.91,},
[40]={number=40,random_count=0.93,},
[41]={number=41,random_count=0.95,},
[42]={number=42,random_count=0.97,},
[43]={number=43,random_count=0.99,},
[44]={number=44,random_count=1.01,},
[45]={number=45,random_count=1.03,},
[46]={number=46,random_count=1.05,},
[47]={number=47,random_count=1.07,},
[48]={number=48,random_count=1.09,},
[49]={number=49,random_count=1.11,},
[50]={number=50,random_count=1.13,},
[51]={number=51,random_count=1.15,},
[52]={number=52,random_count=1.17,},
[53]={number=53,random_count=1.19,},
[54]={number=54,random_count=1.21,},
[55]={number=55,random_count=1.23,},
[56]={number=56,random_count=1.25,},
[57]={number=57,random_count=1.27,},
[58]={number=58,random_count=1.29,},
[59]={number=59,random_count=1.31,},
[60]={number=60,random_count=1.33,},
[61]={number=61,random_count=1.35,},
[62]={number=62,random_count=1.37,},
[63]={number=63,random_count=1.39,},
[64]={number=64,random_count=1.41,},
[65]={number=65,random_count=1.43,},
[66]={number=66,random_count=1.45,},
[67]={number=67,random_count=1.47,},
[68]={number=68,random_count=1.49,},
[69]={number=69,random_count=1.51,},
[70]={number=70,random_count=1.53,},
[71]={number=71,random_count=1.55,},
[72]={number=72,random_count=1.57,},
[73]={number=73,random_count=1.59,},
[74]={number=74,item_id=16508,random_count=1.61,},
[75]={number=75,item_id=16513,random_count=1.63,},
[76]={number=76,item_id=16504,random_count=1.65,},
[77]={number=77,item_id=16509,random_count=1.67,},
[78]={number=78,item_id=16514,random_count=1.69,},
[79]={number=79,random_count=1.71,},
[80]={number=80,random_count=1.73,},
[81]={number=81,random_count=1.75,},
[82]={number=82,random_count=1.77,},
[83]={number=83,random_count=1.79,},
[84]={number=84,random_count=1.81,},
[85]={number=85,item_id=16501,random_count=1.83,},
[86]={number=86,item_id=16506,random_count=1.85,},
[87]={number=87,item_id=16511,random_count=1.87,},
[88]={number=88,item_id=16551,random_count=1.89,},
[89]={number=89,item_id=16556,random_count=1.91,},
[90]={number=90,item_id=16561,random_count=1.93,},
[91]={number=91,random_count=1.95,},
[92]={number=92,random_count=1.97,},
[93]={number=93,random_count=1.99,},
[94]={number=94,random_count=2.01,},
[95]={number=95,random_count=2.03,},
[96]={number=96,random_count=2.05,},
[97]={number=97,random_count=2.07,},
[98]={number=98,random_count=2.09,},
[99]={number=99,random_count=2.11,},
[100]={number=100,random_count=2.13,},
[101]={number=101,random_count=2.15,},
[102]={number=102,random_count=2.17,},
[103]={number=103,random_count=2.19,},
[104]={number=104,random_count=2.21,},
[105]={number=105,random_count=2.23,},
[106]={number=106,random_count=2.25,},
[107]={number=107,random_count=2.27,},
[108]={number=108,random_count=2.29,},
[109]={number=109,random_count=2.31,},
[110]={number=110,random_count=2.33,},
[111]={number=111,random_count=2.35,},
[112]={number=112,random_count=2.37,},
[113]={number=113,random_count=2.39,},
[114]={number=114,random_count=2.41,},
[115]={number=115,random_count=2.43,},
[116]={number=116,random_count=2.45,},
[117]={number=117,random_count=2.47,},
[118]={number=118,random_count=2.49,},
[119]={number=119,random_count=2.51,},
[120]={number=120,random_count=2.53,}
},

item_random_desc_meta_table_map={
[70]=22,	-- depth:1
[71]=23,	-- depth:1
[84]=12,	-- depth:1
[83]=11,	-- depth:1
[72]=24,	-- depth:1
[81]=9,	-- depth:1
[91]=19,	-- depth:1
[80]=8,	-- depth:1
[79]=7,	-- depth:1
[82]=10,	-- depth:1
[92]=20,	-- depth:1
[100]=76,	-- depth:1
[94]=22,	-- depth:1
[118]=22,	-- depth:1
[117]=21,	-- depth:1
[116]=20,	-- depth:1
[115]=19,	-- depth:1
[114]=90,	-- depth:1
[113]=89,	-- depth:1
[112]=88,	-- depth:1
[111]=87,	-- depth:1
[110]=86,	-- depth:1
[109]=85,	-- depth:1
[108]=12,	-- depth:1
[107]=11,	-- depth:1
[106]=10,	-- depth:1
[105]=9,	-- depth:1
[104]=8,	-- depth:1
[103]=7,	-- depth:1
[102]=78,	-- depth:1
[101]=77,	-- depth:1
[69]=21,	-- depth:1
[99]=75,	-- depth:1
[98]=74,	-- depth:1
[96]=24,	-- depth:1
[95]=23,	-- depth:1
[93]=21,	-- depth:1
[68]=20,	-- depth:1
[60]=12,	-- depth:1
[66]=90,	-- depth:1
[37]=85,	-- depth:1
[36]=12,	-- depth:1
[35]=11,	-- depth:1
[34]=10,	-- depth:1
[33]=9,	-- depth:1
[32]=8,	-- depth:1
[31]=7,	-- depth:1
[30]=78,	-- depth:1
[29]=77,	-- depth:1
[28]=76,	-- depth:1
[27]=75,	-- depth:1
[26]=74,	-- depth:1
[18]=90,	-- depth:1
[17]=89,	-- depth:1
[16]=88,	-- depth:1
[15]=87,	-- depth:1
[14]=86,	-- depth:1
[13]=85,	-- depth:1
[6]=78,	-- depth:1
[5]=77,	-- depth:1
[4]=76,	-- depth:1
[3]=75,	-- depth:1
[2]=74,	-- depth:1
[38]=86,	-- depth:1
[67]=19,	-- depth:1
[39]=87,	-- depth:1
[41]=89,	-- depth:1
[65]=89,	-- depth:1
[64]=88,	-- depth:1
[63]=87,	-- depth:1
[62]=86,	-- depth:1
[61]=85,	-- depth:1
[119]=23,	-- depth:1
[59]=11,	-- depth:1
[58]=10,	-- depth:1
[57]=9,	-- depth:1
[56]=8,	-- depth:1
[55]=7,	-- depth:1
[54]=78,	-- depth:1
[53]=77,	-- depth:1
[52]=76,	-- depth:1
[51]=75,	-- depth:1
[50]=74,	-- depth:1
[48]=24,	-- depth:1
[47]=23,	-- depth:1
[46]=22,	-- depth:1
[45]=21,	-- depth:1
[44]=20,	-- depth:1
[43]=19,	-- depth:1
[42]=90,	-- depth:1
[40]=88,	-- depth:1
[120]=24,	-- depth:1
},
other_default_table={tedian_cycle_step=4,first_tedian_days=3,cycle_tedian_start_weekday=3,cycle_tedian_days=3,multi_lotto=10,},

cycle_subactivity_default_table={cycle=1,start_subactivitys="1|2|3|4|6",},

subactivity_relation_default_table={subactivity_id=1,pre_subactivity="",subactivity_show=1,},

subactivity_sale_default_table={cycle=1,subactivity_id=6,product_id=1,price_type=0,special_sale_price=25,origin_price=0,discount=0,item={[0]=item_table[55]},limit_buy_type=0,limit_buy_times=1,name="<color=#ff0000>圣武召唤礼包</color>",subactivity_name="特惠召唤",},

first_single_lotto_default_table={cycle=1,first_single_lotto_reward=1,reward_item=item_table[35],reward_type=2,broadcast=0,},

first_multi_lotto_group_default_table={cycle=1,first_multi_lotto_group=1,group_count=1,},

first_multi_lotto_default_table={cycle=1,first_multi_lotto_group=2,first_single_lotto_id=2,reward_item=item_table[56],reward_type=2,broadcast=2,},

special_multi_lotto_group_default_table={cycle=1,special_multi_lotto_group=1,special_group_count=1,},

special_multi_lotto_default_table={cycle=1,special_multi_lotto_group=2,special_single_lotto_id=1,reward_item=item_table[51],reward_type=2,broadcast=2,},

reward_default_table={cycle=1,reward_id=1,reward_item=item_table[32],reward_type=2,broadcast=0,},

group_multi_limit_default_table={cycle=0,reward_multi_group=1,reward_id="1|2|3|4|5|6|7|8|9|10|11|12",},

group_cycle_limit_default_table={cycle=1,reward_group=1,reward_id="1|2|3|4|5|6|7|8|9|10|11|12",},

pool_cycle_default_table={cycle=1,reward_pool=1,},

reward_cycle_default_table={cycle=1,reward_pool=1,reward_id=7,},

client_sub_act_default_table={cycle=1,act_id=1,name="act_name_4",item_bg="act_bg_3",open_param="XianQi_ShiLianView",},

probability_up_show_default_table={cycle=1,shenji_id=16503,base_color=4,base_star=5,},

item_random_desc_default_table={number=1,item_id=16503,random_count=0.0454,is_rare=1,}

}

