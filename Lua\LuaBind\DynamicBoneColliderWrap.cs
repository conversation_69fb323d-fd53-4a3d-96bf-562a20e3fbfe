﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class DynamicBoneColliderWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>ginClass(typeof(DynamicBoneCollider), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>("Collide", Collide);
		<PERSON><PERSON>("__eq", op_Equality);
		<PERSON><PERSON>ction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("m_Center", get_m_Center, set_m_Center);
		<PERSON><PERSON>("m_Radius", get_m_Radius, set_m_Radius);
		<PERSON><PERSON>("m_Height", get_m_Height, set_m_Height);
		<PERSON><PERSON>("m_Direction", get_m_Direction, set_m_Direction);
		<PERSON>.<PERSON>("m_Bound", get_m_Bound, set_m_Bound);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Collide(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			DynamicBoneCollider obj = (DynamicBoneCollider)ToLua.CheckObject<DynamicBoneCollider>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.Collide(ref arg0, arg1);
			ToLua.Push(L, arg0);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Center(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			UnityEngine.Vector3 ret = obj.m_Center;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Center on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			float ret = obj.m_Radius;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Height(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			float ret = obj.m_Height;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Height on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Direction(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			DynamicBoneCollider.Direction ret = obj.m_Direction;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Direction on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Bound(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			DynamicBoneCollider.Bound ret = obj.m_Bound;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Bound on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Center(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.m_Center = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Center on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Radius = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Height(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Height = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Height on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Direction(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			DynamicBoneCollider.Direction arg0 = (DynamicBoneCollider.Direction)ToLua.CheckObject(L, 2, typeof(DynamicBoneCollider.Direction));
			obj.m_Direction = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Direction on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Bound(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBoneCollider obj = (DynamicBoneCollider)o;
			DynamicBoneCollider.Bound arg0 = (DynamicBoneCollider.Bound)ToLua.CheckObject(L, 2, typeof(DynamicBoneCollider.Bound));
			obj.m_Bound = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Bound on a nil value");
		}
	}
}

