-- 跨服真充boss
CrossEverydayRechargeBossLogic = CrossEverydayRechargeBossLogic or BaseClass(CommonFbLogic)

function CrossEverydayRechargeBossLogic:__init()
end

function CrossEverydayRechargeBossLogic:__delete()
end

function CrossEverydayRechargeBossLogic:Enter(old_scene_type, new_scene_type)
    BossWGCtrl.Instance:Close()
	-- MainuiWGCtrl.Instance:SetTaskButtonTrue()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    BossWGCtrl.Instance:EnterSceneCallback()

    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        WorldServerWGCtrl.Instance:OpenERBSceneInfoView()
        local view = WorldServerWGCtrl.Instance:GetERBSceneInfoView()
        ViewManager.Instance:AddMainUIFuPingChangeList(view)
    end)
end

function CrossEverydayRechargeBossLogic:Out()
    BossWGCtrl.Instance:OutSceneCallback(true)
    MainuiWGCtrl.Instance:ResetTaskPanel()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    WorldServerWGCtrl.Instance:CloseERBSceneInfoView()

    local view = WorldServerWGCtrl.Instance:GetERBSceneInfoView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
end