--新旧称号对比界面
TitleComparisonView = TitleComparisonView or BaseClass(SafeBaseView)

function TitleComparisonView:__init()
    self:SetMaskBg(true,true)
    self.view_layer = UiLayer.Pop
    self.view_name = "TitleComparisonView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/chenghao_prefab", "layout_title_comparison")
    self.active_close = false
end

function TitleComparisonView:ReleaseCallBack()

end

function TitleComparisonView:LoadCallBack()
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list["title_view_name"].text.text = Language.Title.TitleChange
    --XUI.AddClickEventListener(self.node_list.btn_cancel,BindTool.Bind1(self.OnClickCancel,self))
    XUI.AddClickEventListener(self.node_list.btn_ok,BindTool.Bind1(self.OnClickOK,self))
end

function TitleComparisonView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if "title_id" == k then
            self:FlushView(v.new_title_id, v.use_title_id)
        end
    end
end

function TitleComparisonView:FlushView(new_title_id, use_title_id)
    self:SetTitleImgLeft(use_title_id)
    self.title_id = new_title_id
    self:SetTitleImgRight(new_title_id)
end

function TitleComparisonView:SetTitleImgLeft(title_id)
    if title_id then
        local b,a = ResPath.GetTitleModel(title_id)
        self.node_list["cur_title_img"]:ChangeAsset(b, a, false, function (obj)
            if IsNil(obj) then
                return
            end
            
            local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
            local title_cfg = TitleWGData.GetTitleConfig(title_id)
            if not diy_cfg or not title_cfg then
                return
            end

            local text_obj = obj.gameObject.transform:Find("Text")
            if text_obj == nil then
                return
            end
            
            local title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
            local title_name = TitleWGData.Instance:GetDiyTitleName(title_id)
            if title_name and title_name ~= "" then
                title_text.text = title_name
            else
                title_text.text = title_cfg.name
            end
        end)
    end
end

function TitleComparisonView:SetTitleImgRight(title_id)
    if title_id then
        local b,a = ResPath.GetTitleModel(title_id)
        self.node_list["new_title_img"]:ChangeAsset(b, a, false, function (obj)
            if IsNil(obj) then
                return
            end

            local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
            local title_cfg = TitleWGData.GetTitleConfig(title_id)
            if not diy_cfg or not title_cfg then
                return
            end

            local text_obj = obj.gameObject.transform:Find("Text")
            if text_obj == nil then
                return
            end
            
            local title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
            local title_name = TitleWGData.Instance:GetDiyTitleName(title_id)
            if title_name and title_name ~= "" then
                title_text.text = title_name
            else
                title_text.text = title_cfg.name
            end
        end)
    end
end

function TitleComparisonView:OnClickOK()
    TitleWGCtrl.Instance:SendUseTitleReq({self.title_id})
    self:Close()
end

--function TitleComparisonView:OnClickCancel()
--    self:Close()
--end