SwornEquipAttrView = SwornEquipAttrView or BaseClass(SafeBaseView)

function SwornEquipAttrView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/sworn_ui_prefab", "sworn_euqip_attr")
end

function SwornEquipAttrView:LoadCallBack()
    self.cur_attr_list = {}
	self.next_attr_list = {}
    for i = 1, 5 do
		self.cur_attr_list[i] = SwornEquipAddAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = SwornEquipAddAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end

    self.node_list["left_active_btn"].button:AddClickListener(BindTool.Bind(self.OnClickActive, self))
end

function SwornEquipAttrView:ReleaseCallBack()
    if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	    self.cur_attr_list = nil
	end

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	    self.next_attr_list = nil
	end

end

function SwornEquipAttrView:SetDataAndOpen(suit_index)
    self.suit_index = suit_index
    self:Open()
end

function SwornEquipAttrView:OnClickActive()
    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_SUIT_ACTIVE, self.suit_index)
end

function SwornEquipAttrView:OnFlush()
    local suit_seq = SwornWGData.Instance:GetStarSuitSeq(self.suit_index)
    local cur_attr_list = SwornWGData.Instance:GetStarSuitAttrList(self.suit_index, suit_seq)
    local next_attr_list = SwornWGData.Instance:GetStarSuitAttrList(self.suit_index, suit_seq + 1)
    local cur_star_suit_cfg = SwornWGData.Instance:GetStarSuitCfg(self.suit_index, suit_seq)
    local next_star_suit_cfg = SwornWGData.Instance:GetStarSuitCfg(self.suit_index, suit_seq + 1)

    self.node_list["no_attr_tip"]:SetActive(cur_star_suit_cfg == nil)
    self.node_list["max_attr_tip"]:SetActive(next_star_suit_cfg == nil)
    
    local is_active = SwornWGData.Instance:GetStarSuitRemind(self.suit_index)
    XUI.SetButtonEnabled(self.node_list["left_active_btn"], is_active)
    self.node_list["remind"]:SetActive(is_active)
    
    if cur_star_suit_cfg ~= nil then
        local cur_str = string.format(Language.Sworn.EquipSuitActDesc, cur_star_suit_cfg.need_num, cur_star_suit_cfg.need_level)
        local enough_cur_num = SwornWGData.Instance:GetStarSuitEnoughStarNum(self.suit_index, cur_star_suit_cfg.need_level)
        local color = enough_cur_num >= cur_star_suit_cfg.need_num and COLOR3B.DEFAULT_NUM  or COLOR3B.D_RED
        local cur_act_str = string.format(Language.Sworn.EquipSuitCurAct, enough_cur_num, cur_star_suit_cfg.need_num)
        self.node_list["now_level"].text.text = cur_str .. ToColorStr(cur_act_str, color)
    else
        self.node_list["now_level"].text.text = ""
    end

    if next_star_suit_cfg ~= nil then
        local next_str = string.format(Language.Sworn.EquipSuitActDesc, next_star_suit_cfg.need_num, next_star_suit_cfg.need_level)
        local enough_next_num = SwornWGData.Instance:GetStarSuitEnoughStarNum(self.suit_index, next_star_suit_cfg.need_level)
        local next_color = enough_next_num >= next_star_suit_cfg.need_num and COLOR3B.DEFAULT_NUM  or COLOR3B.D_RED
        local next_act_str = string.format(Language.Sworn.EquipSuitCurAct, enough_next_num, next_star_suit_cfg.need_num)
        self.node_list["next_level"].text.text = next_str .. ToColorStr(next_act_str, next_color)
    else
        self.node_list["next_level"].text.text = ""
    end

    for i = 1, 5 do
        self.cur_attr_list[i]:SetData(cur_attr_list[i])
		self.next_attr_list[i]:SetData(next_attr_list[i])
    end
end

SwornEquipAddAttrRender = SwornEquipAddAttrRender or BaseClass(BaseRender)
function SwornEquipAddAttrRender:OnFlush()
	if not IsEmptyTable(self.data) then
        local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
        local per_desc = is_per and "%" or ""
        local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
        self.node_list.name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
        self.node_list.value.text.text = string.format("%s%s", value_str, per_desc)
        self.view:SetActive(true)
	else
		self.view:SetActive(false)
	end
end