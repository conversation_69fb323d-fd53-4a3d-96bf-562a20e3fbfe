-- 客户端命令
ClientCmdWGCtrl = ClientCmdWGCtrl or BaseClass(BaseWGCtrl)

function ClientCmdWGCtrl:__init()
	if ClientCmdWGCtrl.Instance then
		print_error("[ClientCmdWGCtrl] Attempt to create singleton twice!")
		return
	end
	ClientCmdWGCtrl.Instance = self

	self.cmd_info_list = {}

	self.block_gameobj = nil
	self.is_show_pos = false
	self:InitConsoleCmd()
end

function ClientCmdWGCtrl:__delete()
	ClientCmdWGCtrl.Instance = nil

	if self.skill_tool ~= nil then
		self.skill_tool:DeleteMe()
		self.skill_tool = nil
	end
end

function ClientCmdWGCtrl:Cmd(text)
	if nil == text or "" == text then
		return
	end

	local params = Split(text, " ")
	if nil == next(params) then
		return
	end

	local name = params[1]

	local cmd_info = self.cmd_info_list[name]
	if nil ~= cmd_info then
		table.remove(params, 1)
		cmd_info.func(params)
	end
end

function ClientCmdWGCtrl:RegCmdFunc(name, help, callback_func)
	self.cmd_info_list[name] = {desc = help, func = callback_func}
end

-- 初始化命令
function ClientCmdWGCtrl:InitConsoleCmd()
	self:RegCmdFunc("disconnect", "disconnect game server", BindTool.Bind1(self.OnDisconnect, self))
	self:RegCmdFunc("block", "show block", BindTool.Bind1(self.OnBlock, self))
	self:RegCmdFunc("exec", "execute lua", BindTool.Bind1(self.OnExecute, self))
	self:RegCmdFunc("guide", "force guide", BindTool.Bind1(self.Guide, self))
	self:RegCmdFunc("gmlist", "gmlist [cmd_list_level]", BindTool.Bind1(self.GmCmdList, self))
	self:RegCmdFunc("pos", "show role pos [on/off]", BindTool.Bind1(self.ShowRolePos, self))
	self:RegCmdFunc("mem", "mem", BindTool.Bind1(self.CalcMem, self))
	self:RegCmdFunc("lock", "name", BindTool.Bind1(self.OnLock, self))
	self:RegCmdFunc("view", "view", BindTool.Bind1(self.OnOpenView, self))
	self:RegCmdFunc("role_part_tools_view", "role_part_tools_view", BindTool.Bind1(self.OnOpenRolePartToolsView, self))
	self:RegCmdFunc("openserverday", "openserverday", BindTool.Bind1(self.OnLookOpenServerDay, self))
	self:RegCmdFunc("liangdu", "liangdu [on/off]", BindTool.Bind(self.OnLiangDu, self))
	self:RegCmdFunc("leak", "log", BindTool.Bind1(self.CheckLeak, self))
	self:RegCmdFunc("log", "log", BindTool.Bind1(self.OutputLog, self))
	self:RegCmdFunc("outputbundle", "outputbundle", BindTool.Bind1(self.OutputBundle, self))
	self:RegCmdFunc("addchat", "addchat [chat_num]", BindTool.Bind1(self.AddChat, self))
	self:RegCmdFunc("addsystem", "addsystem [num]", BindTool.Bind1(self.AddSystemMsg, self))
	self:RegCmdFunc("addsystem2", "addsystem [num]", BindTool.Bind1(self.AddSystemMsg2, self))
	self:RegCmdFunc("addexp", "addexp [chat_num]", BindTool.Bind1(self.AddEXP, self))
	self:RegCmdFunc("addfighttext", "addfighttext [on/off]", BindTool.Bind1(self.AddFightText, self))
	self:RegCmdFunc("addfallitem", "addfallitem [num]", BindTool.Bind1(self.AddFallItme, self))
	self:RegCmdFunc("addfallitem2", "addfallitem2 [on/off]", BindTool.Bind1(self.AddFallItme2, self))
	self:RegCmdFunc("addmonster", "addmonster [num]", BindTool.Bind1(self.AddMonster, self))
	self:RegCmdFunc("addmonster2", "addmonster2 [on/off]", BindTool.Bind1(self.AddMonster2, self))
	self:RegCmdFunc("addrole", "addrole", BindTool.Bind1(self.AddRole, self))
	self:RegCmdFunc("showfps", "showfps [on/off]", BindTool.Bind1(self.ShowFPS, self))
	self:RegCmdFunc("setfps", "setfps [fps]", BindTool.Bind1(self.SetFps, self))
	self:RegCmdFunc("addfightandexp", "execute lua", BindTool.Bind1(self.AddFightAndExp, self))
	self:RegCmdFunc("addrule", "execute lua", BindTool.Bind1(self.AddRule, self))
	self:RegCmdFunc("changevisiblecount", "execute lua", BindTool.Bind1(self.ChangeVisibleCount, self))
	self:RegCmdFunc("stopcamera", "execute lua", BindTool.Bind1(self.StopCamera, self))
	self:RegCmdFunc("addfollowui", "execute lua", BindTool.Bind1(self.AddFollowUI, self))
	self:RegCmdFunc("check_lua_cost_time", "open [on/off]", BindTool.Bind1(self.CheckLuaCostTime, self))
	self:RegCmdFunc("check_lua_cost_mem", "open [on/off]", BindTool.Bind1(self.CheckLuaCostMem, self))
	self:RegCmdFunc("check_classobj_count", "open [on/off]", BindTool.Bind1(self.CheckClassObjCount, self))
	self:RegCmdFunc("LuaReloadTest", "LuaReloadTest", BindTool.Bind1(self.TestReloadFunc, self))
	self:RegCmdFunc("quickitem", "quickitem", BindTool.Bind1(self.QuickItem, self))
	self:RegCmdFunc("InsertLua", "", BindTool.Bind(self.InsertLua, self))
	self:RegCmdFunc("mianuivisib", "mianuivisib", BindTool.Bind(self.MainUiVisib, self))
	self:RegCmdFunc("moneyeffect", "moneyeffect", BindTool.Bind(self.DoMoneyEffect, self))
	self:RegCmdFunc("UpdateLua", "UpdateLua", BindTool.Bind(self.UpdateLua, self))
	self:RegCmdFunc("UpdateGM", "UpdateGM", BindTool.Bind(self.UpdateGM, self))
	self:RegCmdFunc("playcg", "PlayCG", BindTool.Bind(self.OnPlayCg, self))
	self:RegCmdFunc("playuicg", "PlayUICG", BindTool.Bind(self.OnPlayUICg, self))
	self:RegCmdFunc("testchangescene", "TestChangeScene", BindTool.Bind(self.TestChangeScene, self))
	self:RegCmdFunc("debugfight", "debugfight", BindTool.Bind1(self.DebugFight, self))
	self:RegCmdFunc("fifteenthqeuip", "fifteenthqeuip", BindTool.Bind1(self.GetRoleProfFifteenthOrderEquips, self))
	self:RegCmdFunc("zhuanzhi", "zhuanzhi: [num]", BindTool.Bind1(self.OnZhuanzhi, self))
	self:RegCmdFunc("showbaseattr", "", BindTool.Bind(self.ShowBaseAttr, self))
	self:RegCmdFunc("AttrMonitor", "AttrMonitor [on/off]", BindTool.Bind(self.AttrMonitor, self))
	self:RegCmdFunc("OpenTaskGame", "OpenTaskGame", BindTool.Bind(self.OpenTaskGame, self))
	self:RegCmdFunc("QingGongGuide", "QingGongGuide", BindTool.Bind(self.OnQingGongGuide, self))
	self:RegCmdFunc("OpenRoleModelTestView", "OpenRoleModelTestView", BindTool.Bind(self.OnOpenRoleModelTestView, self))
	self:RegCmdFunc("modelshot", "modelshot", BindTool.Bind(self.OnOpenScreenShotModelView, self))
	self:RegCmdFunc("GMHotupdate", "GMHotupdate", BindTool.Bind1(self.GMHotupdate, self))
	self:RegCmdFunc("movetopos", "movetopos [scene_id] [pos_x] [pos_y]", BindTool.Bind1(self.MoveToPos, self))
	self:RegCmdFunc("client_debug_cache_log", "client_debug_cache_log", BindTool.Bind1(self.GetGuaJiState, self))
	self:RegCmdFunc("addrobert", "addrobert", BindTool.Bind1(self.AddRobert, self))
	self:RegCmdFunc("restart", "", BindTool.Bind1(self.GameRestart, self))
	self:RegCmdFunc("reloadjump", "", BindTool.Bind1(self.ReloadJumpConfig, self))
	self:RegCmdFunc("showremind","showremind",BindTool.Bind(self.ShowRemindChange,self))
	self:RegCmdFunc("showremindinfo","showremindinfo",BindTool.Bind(self.ShowRemindInfo,self))
	self:RegCmdFunc("AddShenShouEquip","AddShenShouEquip",BindTool.Bind(self.AddShenShouEquip,self))
	self:RegCmdFunc("gmdraw","gmdraw[on/off]",BindTool.Bind(self.DrawGuaJiPos,self))
	self:RegCmdFunc("showremindqueue","showremindqueue",BindTool.Bind(self.ShowRemindQueue,self))
	self:RegCmdFunc("gmskill","gmskill[on/off]",BindTool.Bind(self.DrawSkillShow,self))
	self:RegCmdFunc("addallbaguaequip", "addallbaguaequip [star_count] [num]", BindTool.Bind1(self.AddAllBaGuaEquip, self))
	self:RegCmdFunc("addbaguaequip", "addbaguaequip [star_count] [num]", BindTool.Bind1(self.AddBaGuaEquip, self))
	self:RegCmdFunc("showremindwait","showremindwait",BindTool.Bind(self.ShowRemindWait,self))
	self:RegCmdFunc("showremindtabbar","showremindtabbar",BindTool.Bind(self.ShowRemindTabbar,self))
	self:RegCmdFunc("setlogstate","setlogstate",BindTool.Bind(self.SetLogState,self))
	self:RegCmdFunc("setstrike","setstrike",BindTool.Bind(self.SetStrikeParam,self))
	self:RegCmdFunc("liansha_chuanwen","liansha_chuanwen",BindTool.Bind(self.LianShaChuanWen,self))
	self:RegCmdFunc("uihdr","uihdr",BindTool.Bind(self.SwitchUIHDR,self))
	self:RegCmdFunc("playaudio","playaudio",BindTool.Bind(self.PlayAudio,self))
	self:RegCmdFunc("cleardownloadbuffer","cleardownloadbuffer",BindTool.Bind(self.ClearDownloadBuffer,self))
	self:RegCmdFunc("barragetest", "barragetest", BindTool.Bind(self.BarrageTest, self))
	self:RegCmdFunc("circlelimit","circlelimit",BindTool.Bind(self.DrawCircleLimit,self))
	self:RegCmdFunc("gmopenview", "gmopenview", BindTool.Bind(self.GmOpenView, self))
	self:RegCmdFunc("screenshot", "screenshot", BindTool.Bind1(self.GmScreenshot, self))
	self:RegCmdFunc("LimitScreen", "LimitScreen", BindTool.Bind1(self.LimitScreenResolution, self))
	self:RegCmdFunc("showmanager", "showmanager", BindTool.Bind(self.ShowHardwareTrackManager, self))
    self:RegCmdFunc("baoxiangcode", "baoxiangcode", BindTool.Bind1(self.GetBaoXiangCode, self))
	self:RegCmdFunc("AddSiXiangEquip", "AddSiXiangEquip", BindTool.Bind(self.AddSiXiangEquip, self))
	self:RegCmdFunc("AddHolyEquip", "AddHolyEquip", BindTool.Bind(self.AddHolyEquip, self))
	self:RegCmdFunc("AddDayNum", "day_num", BindTool.Bind1(self.AddDayNum, self))
	self:RegCmdFunc("TestExitGame", "TestExitGame", BindTool.Bind1(self.TestExitGame, self))
	self:RegCmdFunc("showactlog", "showactlog", BindTool.Bind1(self.ShowActLog, self))
	self:RegCmdFunc("addallchannel", "addallchannel", BindTool.Bind1(self.OnAddAllChannelMsg, self))
	self:RegCmdFunc("doluagc", "doluagc", BindTool.Bind1(self.DoLuaGC, self))
	self:RegCmdFunc("checkfuntioncostmem", "checkfuntioncostmem", BindTool.Bind1(self.CheckFuntionCostMem, self))
	self:RegCmdFunc("showrolelist", "showrolelist", BindTool.Bind1(self.ShowRoleList, self))
	self:RegCmdFunc("TestRareItemShow", "TestRareItemShow", BindTool.Bind(self.TestRareItemShow, self))
	self:RegCmdFunc("ClearPools", "ClearPools", BindTool.Bind1(self.ClearPools, self))
	self:RegCmdFunc("collect_graphic_mem", "collect_graphic_mem", BindTool.Bind1(self.CollectGraphicMem, self))
    self:RegCmdFunc("collectprotocoltime", "CollectProtocolTime", BindTool.Bind1(self.CollectProtocolTime, self))
    self:RegCmdFunc("CleanCacheServerList", "CleanCacheServerList", BindTool.Bind1(self.CleanCacheServerList, self))
	self:RegCmdFunc("ClearMem", "clearmem", BindTool.Bind1(self.ClearMem, self))
	self:RegCmdFunc("AddGodBookPageChip", "AddGodBookPageChip", BindTool.Bind(self.AddGodBookPageChip, self))
	self:RegCmdFunc("PrintOpenView", "PrintOpenView", BindTool.Bind(self.PrintOpenView, self))
	self:RegCmdFunc("PrintSysInfo", "PrintSysInfo", BindTool.Bind(self.PrintSysInfo, self))
	self:RegCmdFunc("closerolesit", "closerolesit", BindTool.Bind(self.CloseRoleSit, self)) 										-- 关闭打坐
	self:RegCmdFunc("pbpet", "pbpet", BindTool.Bind(self.PBPet, self)) 																-- 屏蔽灵宠
	self:RegCmdFunc("pbguard", "pbguard", BindTool.Bind(self.PBGuard, self)) 														-- 屏蔽守护
	self:RegCmdFunc("pbpetguard", "pbpetguard", BindTool.Bind(self.PBPetGuard, self)) 												-- 屏蔽灵宠和守护
	self:RegCmdFunc("SetInt", "SetInt key value", BindTool.Bind(self.PlayerPrefsUtilSetInt, self))
	self:RegCmdFunc("MainBuffMonitor", "MainBuffMonitor [on/off]", BindTool.Bind(self.MainBuffMonitor, self))
	self:RegCmdFunc("quality", "Quality", BindTool.Bind(self.quality, self))
	self:RegCmdFunc("autodaynight", "AutoDayNight", BindTool.Bind(self.AutoDayNight, self))
	self:RegCmdFunc("debuggraphic", "DebugGraphic", BindTool.Bind(self.DebugGraphic, self))
	self:RegCmdFunc("lualoop", "SetLuaLoop", BindTool.Bind(self.SetLuaLoop, self))
	self:RegCmdFunc("opengm", "OpenGM", BindTool.Bind(self.OpenGM, self))
	self:RegCmdFunc("openTest1", "OpenTest1", BindTool.Bind(self.OpenTest1, self))
	self:RegCmdFunc("openTest2", "OpenTest2", BindTool.Bind(self.OpenTest2, self))
	self:RegCmdFunc("ShowRTRect", "ShowRTRect 0/1", BindTool.Bind(self.ShowRTRect, self))
	self:RegCmdFunc("ShowRTDrag", "ShowRTDrag 0/1", BindTool.Bind(self.ShowRTDrag, self))
	self:RegCmdFunc("autoRunTask", "autoRunTask", BindTool.Bind(self.AutoRunTask, self))
	self:RegCmdFunc("showPrintStr", "showPrintStr", BindTool.Bind(self.ShowPrintStr, self))
	self:RegCmdFunc("uiwaterwave", "uiwaterwave", BindTool.Bind(self.UIWaterWave, self))
	self:RegCmdFunc("resettmp", "resettmp", BindTool.Bind(self.ResetTMP, self))
	self:RegCmdFunc("SetGlobalSwitch", "key", BindTool.Bind(self.SetGlobalSwitch, self))
end

-- 当前视野内的玩家列表名
function ClientCmdWGCtrl:ShowRoleList()
	local role_list = Scene.Instance:GetRoleList()
	print_error("--------------- 开始打印RoleList信息 ---------------")
	for k,v in pairs(role_list) do
		print_error("玩家：", k, v and v:GetName() or "数据为空，取不到，有问题！")
	end
	print_error("--------------- 结束打印RoleList信息 ---------------")
end

-- 移动到
function ClientCmdWGCtrl:MoveToPos(params)
	local scene_id = params[1]
	local pos_x = params[2]
	local pos_y = params[3]
	print_error("MoveToPos", params, scene_id, pos_x, pos_y)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 1)
end

-- 连杀传闻
function ClientCmdWGCtrl:LianShaChuanWen(params)
	GuildBattleRankedWGCtrl.Instance:TestGuildBattleContinueKillMSG(tonumber(params[1]), tonumber(params[2]))
end

-- 播放CG
function ClientCmdWGCtrl:OnPlayCg(params)
	CgManager.Instance:Play(BaseCg.New(params[1], params[2]),
			function() end,
			function(cg_obj)
			end,
		nil)
end

-- 播放UI CG
function ClientCmdWGCtrl:OnPlayUICg(params)
	CgManager.Instance:Play(UICg.New(params[1], params[2]))
end

-- 轻功引导
function ClientCmdWGCtrl:OnQingGongGuide()
	GuajiWGCtrl.Instance:MoveToPos(1001, 469, 128, 1, true, nil, true, function ()
		local cfg = FunctionGuide.Instance:GetGuideCfgByTrigger(2, 60)
		print_error("cfg =", cfg)
		FunctionGuide.Instance:StartGuide(cfg)
	end)
end

-- 设置模型角度界面
function ClientCmdWGCtrl:OnOpenRoleModelTestView()
	self:RestLoadLua("game/role_model_test/role_model_test_view")
	RoleModelTestWGCtrl.Instance:OpenRoleModelTestView()
end

-- 设置模型挂点部件界面
function ClientCmdWGCtrl:OpenRolePartToolsView()
	RolePartSetToolsWGCtrl.Instance:OpenRolePartToolsView()
end

-- 模型截图界面
function ClientCmdWGCtrl:OnOpenScreenShotModelView()
	RoleModelTestWGCtrl.Instance:OpenScreenShotModelView()
end

-- 打印玩家属性
function ClientCmdWGCtrl:ShowBaseAttr()
	local role_attack_special_attr_list = RoleWGData.Instance:GetNewRoleAttr(1)
	local role_defence_special_attr_list = RoleWGData.Instance:GetNewRoleAttr(2)
	local attr_str = ""
	local index = 0
	for k, v in pairs(role_attack_special_attr_list) do
		attr_str = index == 0 and string.format("%s：%s", v.name, v.cur_value) or (attr_str .. string.format("， %s：%s", v.name, v.cur_value))
		index = index + 1
	end

	for k, v in pairs(role_defence_special_attr_list) do
		attr_str = attr_str .. "， " .. v.name .. "：" .. v.cur_value
	end

	local move_speed = GameVoManager.Instance:GetMainRoleVo().move_speed
	attr_str = attr_str .. "， 移速：" .. move_speed
	attr_str = attr_str .. "， 当前血量：" .. RoleWGData.Instance:GetAttr("hp")

	-- local buff_effect_list = FightWGData.Instance:GetMainRoleEffectList()
	-- print_error("buff_effect_list =", buff_effect_list)
	-- if not IsEmptyTable(buff_effect_list) then
	-- 	index = 0
	-- 	local buff_str = ""
	-- 	local buff_auto_cfg = ConfigManager.Instance:GetAutoConfig("buff_auto")
	-- 	local buff_auto_cfg_buff = ListToMapList(buff_auto_cfg.buff, "buff_id")
	-- 	for k, v in pairs(buff_effect_list) do
	-- 		local buff_desc_cfg = buff_auto_cfg.desc[v.client_effect_type]
	-- 		local buff_buff_cfg = buff_auto_cfg_buff[v.client_effect_type]
	-- 		local during_time_ms = buff_buff_cfg and buff_buff_cfg.during_time_ms or 0
	-- 		if buff_desc_cfg then
	-- 			local buff_name = buff_desc_cfg.name
	-- 			local remain_time = (during_time_ms / 1000) - (Status.NowTime - v.recv_time)
	-- 			buff_str = buff_str .. string.format("\n  名称：%s，剩余时间：%s秒", buff_name, v.recv_time)
	-- 		end
	-- 		index = index + 1
	-- 	end
	-- 	print_log("<color=#00ff00>==>> 玩家Buff列表 <<==  " .. buff_str .. "</color>")
	-- end

	local role_exp = RoleWGData.Instance:GetAttr("exp")
	print_log("\n<color=#00ff00>==>> 玩家基础属性列表 <<==  \n" .. attr_str .. "</color>"
		.. "\n\n<color=#00ff00>==>> 玩家当前经验值 <<==  \n" .. role_exp .. "</color>")


	-- 战力打印开关
	RoleWGData.Instance:ChangeShowCapPrintSwitch()
end

-- 战斗打印开关
function ClientCmdWGCtrl:DebugFight()
	FightWGCtrl.Instance:SetDebugFight()
end

-- 断开网络连接
function ClientCmdWGCtrl:OnDisconnect(params)
	GameNet.Instance.custom_disconnect_notice_type = DISCONNECT_NOTICE_TYPE.INVALID
	GameNet.Instance:DisconnectGameServer()
end

-- 触发引导
function ClientCmdWGCtrl:Guide(params)
	FunctionGuide.Instance:TriggerGuideById(params[1])
end


function ClientCmdWGCtrl:OnBlock(params)
	if params[1] == "off" then
		if nil ~= self.block_gameobj then
			ResMgr:Destroy(self.block_gameobj)
			self.block_gameobj = nil
		end
	else
		if nil ~= self.block_gameobj then
			ResMgr:Destroy(self.block_gameobj)
		end

		self.block_gameobj = GameObject.New()

		for y = 0, GridFindWay.Height - 1 do
			local begin_i, end_i = -1, -1
			local is_block = true
			for x = 0, GridFindWay.Width - 1 do
				is_block = GridFindWay:IsBlock(x, y)
				if is_block then
					if begin_i < 0 then begin_i = x end
					end_i = x
				end

				if begin_i >= 0 and end_i >= begin_i and (x == GridFindWay.Width - 1 or not is_block) then
					local pos_x, pos_y = GameMapHelper.LogicToWorld(begin_i + (end_i - begin_i) / 2, y)
					local scale_x = (end_i - begin_i + 1) / 2

					local obj = GameObject.CreatePrimitive(UnityEngine.PrimitiveType.Cube)
					local obj_transform = obj.transform
					obj_transform:SetParent(self.block_gameobj.transform)
					obj_transform:SetPosition(pos_x, -3, pos_y)
					obj_transform:SetLocalScale(scale_x, 8, 0.5)
					begin_i = -1
				end
			end
		end
	end
end

function ClientCmdWGCtrl:OnExecute(fd, str)
	_G.package.loaded["game.clientcmd.client_cmd_script"] = nil
	require("game.clientcmd.client_cmd_script")
end

-- GM命令List表
local gm_list = {};

-- 成为低级菜鸟命令
local dijicainiao_gm_list =
{
	{"addchongzhi", "999999"},
	{"setrolelevel", "498"},

}
gm_list["1"] = dijicainiao_gm_list;

-- 成为高级菜鸟命令
local gaojicainiao_gm_list =
{
	{"addchongzhi", "999999"},
	{"setrolelevel", "498"},
}
gm_list["2"] = gaojicainiao_gm_list;

-- 成为低级高手命令
local dijigaoshou_gm_list =
{
	{"addchongzhi", "999999"},
	{"setrolelevel", "498"},
}
gm_list["3"] = dijigaoshou_gm_list;

-- 成为高级级高手命令
local gaojigaoshou_gm_list =
{
	{"addchongzhi", "999999"},
	{"addmoney", "999999"},
	{"setrolelevel", "900"},
	-- {"addjungong", "99999"},
	-- {"setjxlevel", "19"},
	{"jumptotrunk","3210"},
	{"commitalltask",""},
	{"commitalltask",""},
	{"commitalltask",""},
	{"commitalltask",""},
	{"commitalltask",""},
}
gm_list["4"] = gaojigaoshou_gm_list;

-- 成为高级级高手命令
local wudi_gm_list =
{
	{"changegongji", "99999"},
	{"changemaxhp", "99999"},
	{"addchongzhi", "99999"},
	{"setrolelevel", "999"},
}
gm_list["5"] = wudi_gm_list;

-- 自动进阶
local wudi_gm_list =
{
	{"autoupgradeall", " "},
}
gm_list["6"] = wudi_gm_list;

-- 获得装备
local add_zhuangbei_list =
{
	{"additem", "110 1 0"},
	{"additem", "101 1 0"},
	{"additem", "102 1 0"},
	{"additem", "103 1 0"},
	{"additem", "104 1 0"},
	{"additem", "105 1 0"},
	{"additem", "106 1 0"},
	{"additem", "107 1 0"},
	{"additem", "108 1 0"},
	{"additem", "109 1 0"},
	{"additem", "110 1 0"},
	{"additem", "111 1 0"},
	{"additem", "112 1 0"},
	{"additem", "113 1 0"},
	{"additem", "114 1 0"},
	{"additem", "115 1 0"},
	{"additem", "116 1 0"},
	{"additem", "117 1 0"},
	{"additem", "118 1 0"},
	{"additem", "119 1 0"},
	{"additem", "120 1 0"},
	{"additem", "121 1 0"},
	{"additem", "122 1 0"},
	{"additem", "123 1 0"},
	{"additem", "124 1 0"},
	{"additem", "125 1 0"},
	{"additem", "126 1 0"},
	{"additem", "127 1 0"},
	{"additem", "128 1 0"},
	{"additem", "129 1 0"},
	{"additem", "130 1 0"},
	{"additem", "131 1 0"},
	{"additem", "132 1 0"},
	{"additem", "133 1 0"},
	{"additem", "134 1 0"},
	{"additem", "135 1 0"},
	{"additem", "136 1 0"},
	{"additem", "137 1 0"},
	{"additem", "138 1 0"},
	{"additem", "139 1 0"},
	{"additem", "140 1 0"},
	{"additem", "141 1 0"},
	{"additem", "142 1 0"},
	{"additem", "143 1 0"},
	{"additem", "144 1 0"},
	{"additem", "145 1 0"},
	{"additem", "146 1 0"},
	{"additem", "147 1 0"},
	{"additem", "148 1 0"},
	{"additem", "149 1 0"},
	{"additem", "150 1 0"},
	{"additem", "151 1 0"},
	{"additem", "152 1 0"},
	{"additem", "153 1 0"},
	{"additem", "154 1 0"},
	{"additem", "155 1 0"},
	{"additem", "156 1 0"},
	{"additem", "157 1 0"},
	{"additem", "158 1 0"},
	{"additem", "159 1 0"},
	{"additem", "160 1 0"},
	{"additem", "161 1 0"},
	{"additem", "162 1 0"},
	{"additem", "163 1 0"},
	{"additem", "164 1 0"},
	{"additem", "165 1 0"},
	{"additem", "166 1 0"},
	{"additem", "167 1 0"},
	{"additem", "168 1 0"},
	{"additem", "169 1 0"},
	{"additem", "170 1 0"},
	{"additem", "171 1 0"},
	{"additem", "172 1 0"},
	{"additem", "173 1 0"},
	{"additem", "174 1 0"},
	{"additem", "175 1 0"},
	{"additem", "176 1 0"},
	{"additem", "177 1 0"},
	{"additem", "178 1 0"},
	{"additem", "179 1 0"},
	{"additem", "180 1 0"},
	{"additem", "181 1 0"},
	{"additem", "182 1 0"},
	{"additem", "183 1 0"},
	{"additem", "184 1 0"},
	{"additem", "185 1 0"},
	{"additem", "186 1 0"},
	{"additem", "187 1 0"},
	{"additem", "188 1 0"},
	{"additem", "189 1 0"},
	{"additem", "190 1 0"},
	{"additem", "191 1 0"},
	{"additem", "192 1 0"},
	{"additem", "193 1 0"},
	{"additem", "194 1 0"},
	{"additem", "140 1 0"},
	{"additem", "210 1 0"},
	{"additem", "211 1 0"},
	{"additem", "212 1 0"},
	{"additem", "213 1 0"},
	{"additem", "214 1 0"},
	{"additem", "300 1 0"},
	{"additem", "301 1 0"},
	{"additem", "302 1 0"},
	{"additem", "303 1 0"},
	{"additem", "304 1 0"},
	{"additem", "305 1 0"},
	{"additem", "306 1 0"},
	{"additem", "307 1 0"},
	{"additem", "308 1 0"},
	{"additem", "309 1 0"},
	{"additem", "310 1 0"},
	{"additem", "311 1 0"},
	{"additem", "312 1 0"},
	{"additem", "313 1 0"},
	{"additem", "314 1 0"},
	{"additem", "315 1 0"},
	{"additem", "316 1 0"},
	{"additem", "317 1 0"},
	{"additem", "318 1 0"},
	{"additem", "319 1 0"},
	{"additem", "320 1 0"},
	{"additem", "321 1 0"},
	{"additem", "322 1 0"},
	{"additem", "323 1 0"},
	{"additem", "324 1 0"},
	{"additem", "325 1 0"},
	{"additem", "326 1 0"},
	{"additem", "327 1 0"},
	{"additem", "328 1 0"},
	{"additem", "329 1 0"},
	{"additem", "330 1 0"},
	{"additem", "331 1 0"},
	{"additem", "332 1 0"},
	{"additem", "333 1 0"},
	{"additem", "334 1 0"},
	{"additem", "335 1 0"},
	{"additem", "336 1 0"},
	{"additem", "337 1 0"},
	{"additem", "338 1 0"},
	{"additem", "339 1 0"},
	{"additem", "340 1 0"},
	{"additem", "341 1 0"},
	{"additem", "342 1 0"},
	{"additem", "343 1 0"},
	{"additem", "344 1 0"},
	{"additem", "345 1 0"},
	{"additem", "346 1 0"},
	{"additem", "347 1 0"},
	{"additem", "348 1 0"},
	{"additem", "349 1 0"},
	{"additem", "350 1 0"},
	{"additem", "351 1 0"},
	{"additem", "352 1 0"},
	{"additem", "353 1 0"},
	{"additem", "354 1 0"},
	{"additem", "355 1 0"},
	{"additem", "356 1 0"},
	{"additem", "357 1 0"},
	{"additem", "358 1 0"},
	{"additem", "359 1 0"},
	{"additem", "360 1 0"},
	{"additem", "361 1 0"},
	{"additem", "362 1 0"},
	{"additem", "363 1 0"},
	{"additem", "364 1 0"},
	{"additem", "365 1 0"},
	{"additem", "366 1 0"},
	{"additem", "367 1 0"},
	{"additem", "368 1 0"},
	{"additem", "369 1 0"},
	{"additem", "370 1 0"},
	{"additem", "371 1 0"},
	{"additem", "372 1 0"},
	{"additem", "373 1 0"},
	{"additem", "374 1 0"},
	{"additem", "375 1 0"},
	{"additem", "376 1 0"},
	{"additem", "377 1 0"},
	{"additem", "378 1 0"},
	{"additem", "379 1 0"},
	{"additem", "380 1 0"},
	{"additem", "381 1 0"},
	{"additem", "382 1 0"},
	{"additem", "383 1 0"},
	{"additem", "384 1 0"},
	{"additem", "385 1 0"},
	{"additem", "386 1 0"},
	{"additem", "387 1 0"},
	{"additem", "388 1 0"},
	{"additem", "389 1 0"},
	{"additem", "390 1 0"},
	{"additem", "391 1 0"},
	{"additem", "392 1 0"},
	{"additem", "393 1 0"},
	{"additem", "394 1 0"},
	{"additem", "340 1 0"},
	{"additem", "410 1 0"},
	{"additem", "411 1 0"},
	{"additem", "412 1 0"},
	{"additem", "413 1 0"},
	{"additem", "414 1 0"},
}
gm_list["zb"] = add_zhuangbei_list;

local add_wuzhuangzhuangbei_list =
{
	{"additem", "128 1 0"},
	{"additem", "328 1 0"},
	{"additem", "1128 1 0"},
	{"additem", "1328 1 0"},
	{"additem", "2128 1 0"},
	{"additem", "2328 1 0"},
	{"additem", "4128 1 0"},
	{"additem", "4328 1 0"},
	{"additem", "7128 1 0"},
	{"additem", "7328 1 0"},
	{"additem", "5128 1 0"},
	{"additem", "5328 1 0"},
	{"additem", "5528 1 0"},
	{"additem", "5728 1 0"},
	{"additem", "3128 1 0"},
	{"additem", "6128 1 0"},
	{"additem", "8128 1 0"},
	{"additem", "9128 1 0"},
}
gm_list["5zzb"] = add_wuzhuangzhuangbei_list;

local add_fjzb_list =
{
	{"additem", "28939 1 0"},
	{"additem", "28940 1 0"},
	{"additem", "28941 1 0"},
	{"additem", "28942 1 0"},
	{"additem", "28943 1 0"},
	{"additem", "28944 1 0"},
	{"additem", "28945 1 0"},
	{"additem", "28946 1 0"},
	{"additem", "28947 1 0"},
	{"additem", "28948 1 0"},
	{"additem", "28949 1 0"},
	{"additem", "28950 1 0"},
	{"additem", "28951 1 0"},
	{"additem", "28952 1 0"},
	{"additem", "28953 1 0"},
	{"additem", "28954 1 0"},
	{"additem", "28955 1 0"},
	{"additem", "28956 1 0"},
	{"additem", "28957 1 0"},
}
gm_list["fjzb"] = add_fjzb_list;

local open_fastact =
{
	{"activitynextstate", "2262"},
	{"activitynextstate", "2263"},
	{"activitynextstate", "2264"},
	{"activitynextstate", "2265"},
	{"activitynextstate", "2267"},
	{"activitynextstate", "2268"},
	{"activitynextstate", "2269"},
	{"activitynextstate", "2271"},
	{"activitynextstate", "2261"},
	{"activitynextstate", "2270"},
	{"activitynextstate", "2293"},
	{"activitynextstate", "2292"},
	{"activitynextstate", "2300"},
	{"activitynextstate", "2301"},
	{"activitynextstate", "2299"},
}
gm_list["jrhd"] = open_fastact;

local open_newfastact =
{
	{"activitynextstate", "2344"},
	{"activitynextstate", "2345"},
	{"activitynextstate", "2346"},
	{"activitynextstate", "2347"},
	{"activitynextstate", "2348"},
	{"activitynextstate", "2349"},
	{"activitynextstate", "2350"},
	{"activitynextstate", "2351"},
	{"activitynextstate", "2352"},
	-- {"activitynextstate", "2353"},

}
gm_list["xjrhd"] = open_newfastact;

local open_mergeact =
{
	{"activitynextstate", "2128"},
	{"activitynextstate", "2129"},
	{"activitynextstate", "2118"},
	{"activitynextstate", "2119"},
	{"activitynextstate", "2248"},
	{"activitynextstate", "2109"},
	{"activitynextstate", "2110"},
	{"activitynextstate", "2111"},
	{"activitynextstate", "2247"},
	{"activitynextstate", "2249"},
	{"activitynextstate", "2127"},
	{"activitynextstate", "2246"},
	{"activitynextstate", "2275"},
}
gm_list["hfhd"] = open_mergeact;


local add_hongzhuang_list =
{
	{"additem", "104 1 0"},
	{"additem", "110 1 0"},
	{"additem", "116 1 0"},
	{"additem", "122 1 0"},
	{"additem", "128 1 0"},
	{"additem", "134 1 0"},
	{"additem", "140 1 0"},
	{"additem", "146 1 0"},
	{"additem", "152 1 0"},
	{"additem", "158 1 0"},
	{"additem", "164 1 0"},
	{"additem", "170 1 0"},
	{"additem", "176 1 0"},
	{"additem", "182 1 0"},
	{"additem", "188 1 0"},
	{"additem", "1104 1 0"},
	{"additem", "1110 1 0"},
	{"additem", "1116 1 0"},
	{"additem", "1122 1 0"},
	{"additem", "1128 1 0"},
	{"additem", "1134 1 0"},
	{"additem", "1140 1 0"},
	{"additem", "1146 1 0"},
	{"additem", "1152 1 0"},
	{"additem", "1158 1 0"},
	{"additem", "1164 1 0"},
	{"additem", "1170 1 0"},
	{"additem", "1176 1 0"},
	{"additem", "1182 1 0"},
	{"additem", "1188 1 0"},
	{"additem", "2104 1 0"},
	{"additem", "2110 1 0"},
	{"additem", "2116 1 0"},
	{"additem", "2122 1 0"},
	{"additem", "2128 1 0"},
	{"additem", "2134 1 0"},
	{"additem", "2140 1 0"},
	{"additem", "2146 1 0"},
	{"additem", "2152 1 0"},
	{"additem", "2158 1 0"},
	{"additem", "2164 1 0"},
	{"additem", "2170 1 0"},
	{"additem", "2176 1 0"},
	{"additem", "2182 1 0"},
	{"additem", "2188 1 0"},
	{"additem", "4104 1 0"},
	{"additem", "4110 1 0"},
	{"additem", "4116 1 0"},
	{"additem", "4122 1 0"},
	{"additem", "4128 1 0"},
	{"additem", "4134 1 0"},
	{"additem", "4140 1 0"},
	{"additem", "4146 1 0"},
	{"additem", "4152 1 0"},
	{"additem", "4158 1 0"},
	{"additem", "4164 1 0"},
	{"additem", "4170 1 0"},
	{"additem", "4176 1 0"},
	{"additem", "4182 1 0"},
	{"additem", "4188 1 0"},
	{"additem", "7104 1 0"},
	{"additem", "7110 1 0"},
	{"additem", "7116 1 0"},
	{"additem", "7122 1 0"},
	{"additem", "7128 1 0"},
	{"additem", "7134 1 0"},
	{"additem", "7140 1 0"},
	{"additem", "7146 1 0"},
	{"additem", "7152 1 0"},
	{"additem", "7158 1 0"},
	{"additem", "7164 1 0"},
	{"additem", "7170 1 0"},
	{"additem", "7176 1 0"},
	{"additem", "7182 1 0"},
	{"additem", "7188 1 0"},
	{"additem", "5104 1 0"},
	{"additem", "5110 1 0"},
	{"additem", "5116 1 0"},
	{"additem", "5122 1 0"},
	{"additem", "5128 1 0"},
	{"additem", "5134 1 0"},
	{"additem", "5140 1 0"},
	{"additem", "5146 1 0"},
	{"additem", "5152 1 0"},
	{"additem", "5158 1 0"},
	{"additem", "5164 1 0"},
	{"additem", "5170 1 0"},
	{"additem", "5176 1 0"},
	{"additem", "5182 1 0"},
	{"additem", "5188 1 0"},
	{"additem", "3104 1 0"},
	{"additem", "3110 1 0"},
	{"additem", "3116 1 0"},
	{"additem", "3122 1 0"},
	{"additem", "3128 1 0"},
	{"additem", "3134 1 0"},
	{"additem", "3140 1 0"},
	{"additem", "3146 1 0"},
	{"additem", "3152 1 0"},
	{"additem", "3158 1 0"},
	{"additem", "3164 1 0"},
	{"additem", "3170 1 0"},
	{"additem", "3176 1 0"},
	{"additem", "3182 1 0"},
	{"additem", "3188 1 0"},
	{"additem", "3140 1 0"},
	{"additem", "6104 1 0"},
	{"additem", "6110 1 0"},
	{"additem", "6116 1 0"},
	{"additem", "6122 1 0"},
	{"additem", "6128 1 0"},
	{"additem", "6134 1 0"},
	{"additem", "6140 1 0"},
	{"additem", "6146 1 0"},
	{"additem", "6152 1 0"},
	{"additem", "6158 1 0"},
	{"additem", "6164 1 0"},
	{"additem", "6170 1 0"},
	{"additem", "6176 1 0"},
	{"additem", "6182 1 0"},
	{"additem", "6188 1 0"},
	{"additem", "6140 1 0"},
	{"additem", "8104 1 0"},
	{"additem", "8110 1 0"},
	{"additem", "8116 1 0"},
	{"additem", "8122 1 0"},
	{"additem", "8128 1 0"},
	{"additem", "8134 1 0"},
	{"additem", "8140 1 0"},
	{"additem", "8146 1 0"},
	{"additem", "8152 1 0"},
	{"additem", "8158 1 0"},
	{"additem", "8164 1 0"},
	{"additem", "8170 1 0"},
	{"additem", "8176 1 0"},
	{"additem", "8182 1 0"},
	{"additem", "8188 1 0"},
	{"additem", "8140 1 0"},
	{"additem", "9104 1 0"},
	{"additem", "9110 1 0"},
	{"additem", "9116 1 0"},
	{"additem", "9122 1 0"},
	{"additem", "9128 1 0"},
	{"additem", "9134 1 0"},
	{"additem", "9140 1 0"},
	{"additem", "9146 1 0"},
	{"additem", "9152 1 0"},
	{"additem", "9158 1 0"},
	{"additem", "9164 1 0"},
	{"additem", "9170 1 0"},
	{"additem", "9176 1 0"},
	{"additem", "9182 1 0"},
	{"additem", "9188 1 0"},
	{"additem", "9140 1 0"},

}
gm_list["hz"] = add_hongzhuang_list;


local add_dult2_list =
{
	{"additem", "27026 1 0"},
	{"additem", "27027 1 0"},
	{"additem", "27028 1 0"},
	{"additem", "27029 1 0"},
	{"additem", "27030 1 0"},
	{"additem", "27031 1 0"},
	{"additem", "27032 1 0"},
	{"additem", "27033 1 0"},
	{"additem", "27034 1 0"},
	{"additem", "27035 1 0"},
	{"additem", "27036 1 0"},
	{"additem", "27037 1 0"},
	{"additem", "27038 1 0"},
	{"additem", "27039 1 0"},
	{"additem", "27040 1 0"},
	{"additem", "27041 1 0"},
	{"additem", "27042 1 0"},
	{"additem", "27043 1 0"},
	{"additem", "27044 1 0"},
	{"additem", "27045 1 0"},
	{"additem", "27046 1 0"},
	{"additem", "27047 1 0"},
	{"additem", "27048 1 0"},
	{"additem", "27049 1 0"},
	{"additem", "27050 1 0"},
	{"additem", "27051 1 0"},
	{"additem", "27052 1 0"},
	{"additem", "27053 1 0"},
	{"additem", "27054 1 0"},
	{"additem", "27055 1 0"},
	{"additem", "27056 1 0"},
	{"additem", "27057 1 0"},
	{"additem", "27058 1 0"},
	{"additem", "27059 1 0"},
	{"additem", "27060 1 0"},
	{"additem", "27061 1 0"},
	{"additem", "27062 1 0"},
	{"additem", "27063 1 0"},
	{"additem", "27064 1 0"},
	{"additem", "27065 1 0"},
	{"additem", "27066 1 0"},
	{"additem", "27067 1 0"},
	{"additem", "27068 1 0"},
	{"additem", "27069 1 0"},
	{"additem", "27070 1 0"},
	{"additem", "27071 1 0"},
	{"additem", "27072 1 0"},
	{"additem", "27073 1 0"},
	{"additem", "27074 1 0"},
	{"additem", "27075 1 0"},
	{"additem", "27076 1 0"},
	{"additem", "27077 1 0"},
	{"additem", "27078 1 0"},
	{"additem", "27079 1 0"},
	{"additem", "27080 1 0"},
	{"additem", "27081 1 0"},
	{"additem", "27082 1 0"},
	{"additem", "27083 1 0"},
	{"additem", "27084 1 0"},
	{"additem", "27085 1 0"},
	{"additem", "27086 1 0"},
	{"additem", "27087 1 0"},
	{"additem", "27088 1 0"},
	{"additem", "27089 1 0"},
	{"additem", "27090 1 0"},
	{"additem", "27091 1 0"},
	{"additem", "27092 1 0"},
	{"additem", "27093 1 0"},
	{"additem", "27094 1 0"},
	{"additem", "27095 1 0"},
	{"additem", "27096 1 0"},
	{"additem", "27097 1 0"},
	{"additem", "27098 1 0"},
	{"additem", "27099 1 0"},
	{"additem", "27100 1 0"},
	{"additem", "27101 1 0"},
	{"additem", "27102 1 0"},
	{"additem", "27103 1 0"},
	{"additem", "27104 1 0"},
	{"additem", "27105 1 0"},
	{"additem", "27106 1 0"},
	{"additem", "27107 1 0"},
	{"additem", "27108 1 0"},
	{"additem", "27109 1 0"},
	{"additem", "27110 1 0"},
	{"additem", "27111 1 0"},
	{"additem", "27112 1 0"},
	{"additem", "27113 1 0"},
	{"additem", "27114 1 0"},
	{"additem", "27115 1 0"},
	{"additem", "27116 1 0"},
	{"additem", "27117 1 0"},
	{"additem", "27118 1 0"},
	{"additem", "27119 1 0"},
	{"additem", "27120 1 0"},
	{"additem", "27121 1 0"},
	{"additem", "27122 1 0"},
	{"additem", "27123 1 0"},
	{"additem", "27124 1 0"},
	{"additem", "27125 1 0"},
	{"additem", "27126 1 0"},
	{"additem", "27127 1 0"},
	{"additem", "27128 1 0"},
	{"additem", "27129 1 0"},
	{"additem", "27130 1 0"},
	{"additem", "27131 1 0"},
	{"additem", "27132 1 0"},
	{"additem", "27133 1 0"},
	{"additem", "27134 1 0"},
	{"additem", "27135 1 0"},
	{"additem", "27136 1 0"},
	{"additem", "27137 1 0"},

}
gm_list["dult2"] = add_dult2_list;

local add_duanlingruti_list =
{
	{"additem", "26828 1 0"},
	{"additem", "26829 1 0"},
	{"additem", "26830 1 0"},
	{"additem", "26831 1 0"},
	{"additem", "26832 1 0"},
	{"additem", "26833 1 0"},
	{"additem", "26834 1 0"},
	{"additem", "26835 1 0"},
	{"additem", "26836 1 0"},
	{"additem", "26837 1 0"},
	{"additem", "26838 1 0"},
	{"additem", "26839 1 0"},
	{"additem", "26840 1 0"},
	{"additem", "26841 1 0"},
	{"additem", "26842 1 0"},
	{"additem", "26843 1 0"},
	{"additem", "26844 1 0"},
	{"additem", "26845 1 0"},
	{"additem", "26846 1 0"},
	{"additem", "26847 1 0"},
	{"additem", "26848 1 0"},
	{"additem", "26849 1 0"},
	{"additem", "26850 1 0"},
	{"additem", "26851 1 0"},
	{"additem", "26852 1 0"},
	{"additem", "26853 1 0"},
	{"additem", "26854 1 0"},
	{"additem", "26855 1 0"},
	{"additem", "26856 1 0"},
	{"additem", "26857 1 0"},
	{"additem", "26858 1 0"},
	{"additem", "26859 1 0"},
	{"additem", "26860 1 0"},
	{"additem", "26861 1 0"},
	{"additem", "26862 1 0"},
	{"additem", "26863 1 0"},
	{"additem", "26864 1 0"},
	{"additem", "26865 1 0"},
	{"additem", "26866 1 0"},
	{"additem", "26867 1 0"},
	{"additem", "26868 1 0"},
	{"additem", "26869 1 0"},
	{"additem", "26870 1 0"},
	{"additem", "26871 1 0"},
	{"additem", "26872 1 0"},
	{"additem", "26873 1 0"},
	{"additem", "26874 1 0"},
	{"additem", "26875 1 0"},
	{"additem", "26876 1 0"},
	{"additem", "26877 1 0"},
	{"additem", "26878 1 0"},
	{"additem", "26879 1 0"},
	{"additem", "26880 1 0"},
	{"additem", "26881 1 0"},
	{"additem", "26882 1 0"},
	{"additem", "26883 1 0"},
	{"additem", "26884 1 0"},
	{"additem", "26885 1 0"},
	{"additem", "26886 1 0"},
	{"additem", "26887 1 0"},
	{"additem", "26888 1 0"},
	{"additem", "26889 1 0"},
	{"additem", "26890 1 0"},
	{"additem", "26891 1 0"},
	{"additem", "26892 1 0"},
	{"additem", "26893 1 0"},
	{"additem", "26894 1 0"},
	{"additem", "26895 1 0"},
	{"additem", "26896 1 0"},
	{"additem", "26897 1 0"},
	{"additem", "26898 1 0"},
	{"additem", "26899 1 0"},
	{"additem", "26900 1 0"},
	{"additem", "26901 1 0"},
	{"additem", "26902 1 0"},
	{"additem", "26903 1 0"},
	{"additem", "26904 1 0"},
	{"additem", "26905 1 0"},
	{"additem", "26906 1 0"},
	{"additem", "26907 1 0"},
	{"additem", "26908 1 0"},
	{"additem", "26909 1 0"},
	{"additem", "26910 1 0"},
	{"additem", "26911 1 0"},
	{"additem", "26912 1 0"},
	{"additem", "26913 1 0"},
	{"additem", "26915 1 0"},
	{"additem", "26916 1 0"},
	{"additem", "26917 1 0"},
	{"additem", "26918 1 0"},
	{"additem", "26919 1 0"},
	{"additem", "26920 1 0"},
	{"additem", "26921 1 0"},
	{"additem", "26922 1 0"},
	{"additem", "26923 1 0"},
	{"additem", "26924 1 0"},
	{"additem", "26925 1 0"},
	{"additem", "26926 1 0"},
	{"additem", "26927 1 0"},
	{"additem", "26928 1 0"},
	{"additem", "26931 1 0"},
	{"additem", "26932 1 0"},
	{"additem", "26933 1 0"},
	{"additem", "26934 1 0"},
	{"additem", "26935 1 0"},
	{"additem", "26936 1 0"},
	{"additem", "26937 1 0"},
	{"additem", "26938 1 0"},
	{"additem", "26939 1 0"},
	{"additem", "26940 1 0"},
	{"additem", "26941 1 0"},
	{"additem", "26942 1 0"},
	{"additem", "26945 1 0"},
	{"additem", "26946 1 0"},
	{"additem", "26947 1 0"},
	{"additem", "26948 1 0"},
	{"additem", "26949 1 0"},
	{"additem", "26950 1 0"},
	{"additem", "26951 1 0"},
	{"additem", "26952 1 0"},
	{"additem", "26953 1 0"},
	{"additem", "26954 1 0"},
	{"additem", "26955 1 0"},
	{"additem", "26956 1 0"},
	{"additem", "26957 1 0"},
	{"additem", "26958 1 0"},
	{"additem", "26959 1 0"},
	{"additem", "26960 1 0"},
	{"additem", "26961 1 0"},
	{"additem", "26962 1 0"},
	{"additem", "26963 1 0"},
	{"additem", "26965 1 0"},
	{"additem", "26966 1 0"},
	{"additem", "26967 1 0"},
	{"additem", "26968 1 0"},
	{"additem", "26969 1 0"},
	{"additem", "26970 1 0"},
	{"additem", "26971 1 0"},
	{"additem", "26972 1 0"},
	{"additem", "26973 1 0"},
	{"additem", "26974 1 0"},
	{"additem", "26975 1 0"},
	{"additem", "26976 1 0"},
	{"additem", "26977 1 0"},
	{"additem", "26978 1 0"},
	{"additem", "26979 1 0"},
	{"additem", "26980 1 0"},
	{"additem", "26981 1 0"},
	{"additem", "26982 1 0"},
	{"additem", "26983 1 0"},
	{"additem", "26984 1 0"},
	{"additem", "26985 1 0"},
	{"additem", "26986 1 0"},
	{"additem", "26987 1 0"},
	{"additem", "26988 1 0"},
	{"additem", "26989 1 0"},
	{"additem", "26990 1 0"},
	{"additem", "26991 1 0"},
	{"additem", "26992 1 0"},
	{"additem", "26993 1 0"},
	{"additem", "26994 1 0"},
	{"additem", "26995 1 0"},
	{"additem", "26996 1 0"},
	{"additem", "26997 1 0"},
	{"additem", "26998 1 0"},
	{"additem", "26999 1 0"},
	{"additem", "27000 1 0"},
	{"additem", "27001 1 0"},
	{"additem", "27002 1 0"},
	{"additem", "27003 1 0"},
	{"additem", "27004 1 0"},
	{"additem", "27005 1 0"},
	{"additem", "27006 1 0"},
	{"additem", "27007 1 0"},
	{"additem", "27008 1 0"},
	{"additem", "27009 1 0"},
	{"additem", "27010 1 0"},
	{"additem", "27011 1 0"},
	{"additem", "27012 1 0"},
	{"additem", "27013 1 0"},
	{"additem", "27014 1 0"},
	{"additem", "27015 1 0"},
	{"additem", "27016 1 0"},
	{"additem", "27017 1 0"},
	{"additem", "27018 1 0"},
	{"additem", "27019 1 0"},
	{"additem", "27020 1 0"},
	{"additem", "27021 1 0"},
	{"additem", "27022 1 0"},
	{"additem", "27023 1 0"},
	{"additem", "27024 1 0"},
	{"additem", "27025 1 0"},
}
gm_list["dlrt"] = add_duanlingruti_list;


function ClientCmdWGCtrl:GmCmdList(params)
	for k,v in pairs(params) do
		if nil == gm_list[v] then
			return
		end

		for i, v1 in ipairs(gm_list[v]) do
			SysMsgWGCtrl.SendGmCommand(v1[1], v1[2])
		end		

		if tonumber(v) == 4 then
			self:OnZhuanzhi({4})
			SysMsgWGCtrl.SendGmCommand("setrolelevel", RoleWGData.GetRoleMaxLevel()) 	-- 升满级测试
		end
	end

	-- local task_list = gaojigaoshou_gm_task_list[PlayerData.Instance.role_vo.camp]
	-- if task_list ~= nil then
	-- 	SysMsgWGCtrl.SendGmCommand(task_list[1], task_list[2])
	-- end
end

-- 显示角色位置开关
function ClientCmdWGCtrl:ShowRolePos(params)
	local on_off = "on" == params[1] and true or false
	self.is_show_pos = on_off
end

-- 统计内存
local calc_mem_timer = nil
function ClientCmdWGCtrl:CalcMem(params)
	if "on" == params[1] then
		local t = {
			res_count = 0,  				-- 资源数
			res_pool_count = 0,				-- 资源池
			gameobj_cache_count = 0,		-- 池GO
			gameobj_pool_count = 0,			-- GO池数
			gameobj_count = 0,				-- GO数
			time_quest_count = 0,			---定时器个数
			itemlist_change_count = 0,		-- 物品监听数量1
			item_change_count = 0,			-- 物品监听数量2
			attr_listen_count = 0,			-- 属性监听数量
			event_count = 0,				-- 事件监听数量
			lua_obj_count = 0				-- lua对象数量
		}

		calc_mem_timer = GlobalTimerQuest:AddRunQuest(function()
			ResPoolMgr:GetPoolDebugInfo(t)
			ResMgr:GetDebugGameObjCount(t)
			GlobalTimerQuest:GetQuestCount(t)
			ItemWGData.Instance:GetDebugNotifyChangeCount(t)
			RoleWGData.Instance:GetDeubgListenCount(t)
			GlobalEventSystem:GetDebugEventCount(t)
			BundleCache:GetBundleCount(t)
			GetDebugLuaObjCount(t)

			if not self.attr_text then
				local async_loader = AllocAsyncLoader(self, "AssertAttrContent")
				async_loader:SetIsUseObjPool(true)
				async_loader:Load("uis/view/common_panel_prefab", "AssertAttrContent", function(obj)
					if IsNil(obj) then
						async_loader:Destroy()
						return
					end

					local UIRoot = GameObject.Find("GameRoot/UILayer").transform
					if UIRoot then
						local obj_transform = obj.transform
						obj_transform:SetParent(UIRoot, false)
						obj_transform:SetAsLastSibling()
						self.attr_text = obj_transform:Find("AttrText").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
					else
						async_loader:Destroy()
					end
				end)
			end

			local str_attr = "\n资源数量: " .. t.res_count .. "\n\n资源池: " .. t.res_pool_count .. "\n\nGO池: " .. t.gameobj_cache_count
				.. "\n\n池中GO数量: " .. t.gameobj_pool_count .. "\n\nGO数: " .. t.gameobj_count .. "\n\n定时器个数: " .. t.time_quest_count
				.. "\n\nbunlde数量: " .. t.bundle_count .. "\n\n物品监听数量: " .. t.item_change_count .. "\n\n属性监听数量: " .. t.attr_listen_count
				.. "\n\n事件监听数量: " .. t.event_count .. "\n\nlua对象数量: " .. t.lua_obj_count
			if self.attr_text and self.attr_text.text then
				self.attr_text.text = str_attr
			end
		end, 0.5)
	elseif "off" == params[1] then
		if nil ~= calc_mem_timer then
			GlobalTimerQuest:CancelQuest(calc_mem_timer)
		end

		if self.attr_text then
			ResMgr:Destroy(self.attr_text.gameObject.transform.parent.gameObject)
			self.attr_text = nil
		end
	end
end

-- 打开锁屏界面
function ClientCmdWGCtrl:OnLock(params)
	SettingWGCtrl.Instance:GmOpenUnLockView()
end

-- 屏幕亮度
function ClientCmdWGCtrl:OnLiangDu(params)
	local liangdu = "on" == params[1] and true or false
	if liangdu then
		ApplicationBrightness.SetApplicationBrightnessTo(0.2)
	else
		ApplicationBrightness.SetApplicationBrightnessTo(-1)
	end
end

-- lua热更新
function ClientCmdWGCtrl:PCallLuaFeild(params)
	if package.loaded["hot_file"] then
		package.loaded["hot_file"] = nil
		require("hot_file")
	else
		require("hot_file")
	end

	local hot_flie = require("hot_file")
	if not hot_flie then
		return
	end

	for k,v in ipairs(hot_flie) do
		self:RestLoadLua(v)
	end
end

-- lua单个文件热更新
function ClientCmdWGCtrl:RestLoadLua(filename)
	if type(filename) == "table" then
		filename = filename[1]
	end
	local lua_name = string.gsub(filename, "/", ".")
	local old_tb = package.loaded[lua_name]
	if old_tb then
		package.loaded[lua_name] = nil
		local ok, err = pcall(require, filename)
		if not ok then
			package.loaded[lua_name] = old_tb
			print_error("重载失败 -- ", filename)
			return
		end
		print_error(string.format("<color=#28e53aFF>重载成功 -- %s</color>", filename))
	else
		print_error("未找到 -- ", filename)
	end
end

-- 打开调整模型展示界面
function ClientCmdWGCtrl:OnOpenView(params)
	self:OnOpenRoleModelTestView()
	--ViewManager.Instance:Open(GuideModuleName.ThunderManaSelectView)
end

-- 打开调整模型展示界面
function ClientCmdWGCtrl:OnOpenRolePartToolsView(params)
	self:OpenRolePartToolsView()
end

-- 开服时间 顺便热更lua
function ClientCmdWGCtrl:OnLookOpenServerDay(params)
	local real_start_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	print_error("开服时间：", TimeUtil.FormatYMD(real_start_time), string.format("开服第%d天", open_server_day))
	self:PCallLuaFeild()
end

function ClientCmdWGCtrl:CheckLeak(params)
	local out_content = BundleCache:CheckAsetBundleLeak()
	local file_path = UnityEngine.Application.persistentDataPath .. "/assetbundle_leak.txt"
	print_log("已输出日志到", file_path)
	local f = io.open(file_path,'w')
	if f == nil then
		return
	end
	
	f:write(out_content)
	f:close()

	local content = BundleCache:CheckAsetBundleDetailLeak()
	local file_path = UnityEngine.Application.persistentDataPath .. "/assetbundle_leak_detail.txt"
	print_log("已输出日志到", file_path)
	local g = io.open(file_path,'w')
	if g == nil then
		return
	end

	g:write(content)
	g:close()

	local debug_str = ResPoolMgr:CheckLeak()
	local file_path = UnityEngine.Application.persistentDataPath .. "/res_pool.txt"
	print_log("已输出日志到", file_path)
	local h = io.open(file_path,'w')
	if h == nil then
		return
	end
	
	h:write(debug_str)
	h:close()
end

function ClientCmdWGCtrl:OutputLog(params)
	ResUtil.OutputLog()
end

function ClientCmdWGCtrl:OutputBundle(params)
	BundleCache:OutputBundle()
end

-- 测试消息发送所有频道
function ClientCmdWGCtrl:OnAddAllChannelMsg(params)
	local num = tonumber(params[1])

	local t = {
	CHANNEL_TYPE.SYSTEM ,
	CHANNEL_TYPE.CHUAN_WEN ,
	CHANNEL_TYPE.WORLD,
	CHANNEL_TYPE.ZUDUI,
	CHANNEL_TYPE.TEAM,
	CHANNEL_TYPE.GUILD,
	CHANNEL_TYPE.CROSS,
	CHANNEL_TYPE.ZHANDUI3V3,}

	for k,v in pairs(t) do
		self:AddChat({num, v})
	end
end

-- 测试消息发送的频道
function ClientCmdWGCtrl:AddChat(params)
	if params[1] ~= nil and params[1] ~= "" then
		local num = tonumber(params[1])
		local channel_type = tonumber(params[2]) or CHANNEL_TYPE.WORLD
		if num ~= nil and num > 0 then
			for i = 1, num do
				local chat = {}
				local role = GameVoManager.Instance:GetMainRoleVo()
				chat.from_uid = role.role_id
				chat.username = role.name .. "->text: 	" .. channel_type .. "	index:" .. i
				chat.sex = 0
				chat.camp = 1
				chat.prof = 1
				chat.authority_type = 0
				chat.content_type = 0
				chat.tuhaojin_color = 0
				chat.bigchatface_status = 0
				chat.personalize_window_bubble_type = math.random(1, 5)
				chat.avatar_key_big = 0
				chat.avatar_key_small = 0
				chat.rank = math.random(1, 10)
				chat.fashion_photoframe = math.random(1, 5)
				chat.channel_window_bubble_type = math.random(1, 10)
				chat.fashion_bubble = math.random(1, 10)

				chat.personalize_window_avatar_type = 0

				chat.level = role.level
				chat.vip_level = 0
				chat.channel_type = channel_type
				chat.guild_signin_count = 0
				chat.is_msg_record = 0
				chat.use_head_frame = 0
				chat.msg_timestamp = TimeWGCtrl.Instance:GetServerTime()
				chat.msg_length = 0
				chat.content = string.format("{showpos;2} 惊闻一声天雷，{r;1051387;郎晓啸;3} 的坐骑成功进阶至{mount;41}，战斗力直线飙升，来顶礼膜拜吧%s！{openLink;4}", math.random(1, 1000000))

				ChatWGCtrl.Instance:OnChannelChat(chat)
			end
		end
	end
end

-- 飘字测试
function ClientCmdWGCtrl:AddSystemMsg(params)
	if params[1] ~= nil and params[1] ~= "" then
		local num = tonumber(params[1])
		local msg_type = tonumber(params[2])
		if num ~= nil and num > 0 then
			for i = 1, num do
				local cmd = SCSystemMsg.New()
				cmd.send_time = 1540405388
				cmd.msg_type = msg_type or SYS_MSG_TYPE.SYS_MSG_CENTER_NOTICE
				cmd.msg_length = 157
				cmd.display_pos = 0
				cmd.color = 0

				cmd.content = string.format("{showpos;2} 惊闻一声天雷，{r;1051387;郎晓啸;3} 的坐骑成功进阶至{mount;41}，战斗力直线飙升，来顶礼膜拜吧%s！{openLink;4}", math.random(1, 1000000))
				TipWGCtrl.Instance:ShowSystemNotice(cmd.content)
				ChatWGCtrl.Instance:OnSystemMsg(cmd)
				--cmd.msg_type = SYS_MSG_TYPE.SYS_MSG_ONLY_CHAT_WORLD
			end
		end
	end
end

-- 飘字测试
function ClientCmdWGCtrl:AddSystemMsg2(params)
	if params[1] ~= nil and params[1] ~= "" then
		local num = tonumber(params[1])
		if num ~= nil and num > 0 then
			for i = 1, num do
				local cmd = SCSystemMsg.New()
				cmd.send_time = 1540405388
				cmd.msg_type = SYS_MSG_TYPE.SYS_MSG_CENTER_NOTICE
				cmd.msg_length = 157
				cmd.display_pos = 0
				cmd.color = 0

				-- cmd.content = " 惊闻一声天雷的坐骑成功进阶至战斗力直线飙升"
				cmd.content = "恭喜{r;1065026;晏心怡;0}在战魂寻宝十次中获得{i;23361}{openLink;71}"
				ChatWGCtrl.Instance:OnSystemMsg(cmd)

				cmd.msg_type = SYS_MSG_TYPE.SYS_MSG_ONLY_CHAT_WORLD
				ChatWGCtrl.Instance:OnSystemMsg(cmd)
			end
		end
	end
end

-- 经验飘字
function ClientCmdWGCtrl:AddEXP(params)
	-- if params[1] ~= nil and params[1] ~= "" then
	-- 	local num = tonumber(params[1])
	-- 	if num ~= nil and num > 0 then
	-- 		for i = 1, num do
	-- 			TipWGCtrl.Instance:ShowExp("exp + 9999", true)
	-- 		end
	-- 	end
	-- end

	local on_off = "on" == params[1] and true or false
	if on_off and not self.time_quester then
		self.time_quester = GlobalTimerQuest:AddRunQuest(function()
			for i = 1, 20 do
				TipWGCtrl.Instance:ShowExp("exp + 9999", true)
			end
		end, 0.5)
	else
		GlobalTimerQuest:CancelQuest(self.time_quester)
		self.time_quester = nil
	end
end

local asset_name_list =
{
	"HurtLeft1",
	"HurtRight1",
	"CriticalLeft",
	"CriticalRight",
}
-- 战斗飘字
function ClientCmdWGCtrl:AddFightText(params)
	local on_off = "on" == params[1] and true or false
	if on_off and not self.time_quester then
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			local attach_point = main_role.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
			local bundle_name = "uis/view/floatingtext_ui_prefab"
			self.time_quester = GlobalTimerQuest:AddRunQuest(function()
				for i = 1, 20 do
					local asset_name = asset_name_list[math.random(1, 4)]
					FightText.Instance:ShowText(bundle_name, asset_name, "123456789", attach_point)
				end
			end, 0.5)
		end
	else
		GlobalTimerQuest:CancelQuest(self.time_quester)
		self.time_quester = nil
	end
end

-- 战斗飘字+经验飘字
function ClientCmdWGCtrl:AddFightAndExp(params)
	local on_off = "on" == params[1] and true or false
	if on_off and not self.time_quester then
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			local attach_point = main_role.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
			local bundle_name = "uis/view/floatingtext_ui_prefab"
			self.time_quester = GlobalTimerQuest:AddRunQuest(function()
				for i = 1, 20 do
					local asset_name = asset_name_list[math.random(1, 4)]
					FightText.Instance:ShowText(bundle_name, asset_name, "123456789", attach_point)
				end

				for i = 1, 20 do
					TipWGCtrl.Instance:ShowExp("exp + 9999", true)
				end
			end, 0.5)
		end
	else
		GlobalTimerQuest:CancelQuest(self.time_quester)
		self.time_quester = nil
	end
end

local fall_item_list =
{
	384, 385, 386, 387, 388, 1100, 1101, 1102, 1103, 1104, 2100, 2101, 2102, 2103, 2104, 3100, 3101, 3102, 3103, 3104,
	4100, 4101, 4102, 4103, 4104, 5100, 5101, 5102, 5103, 5104, 8100, 8101, 8102, 8103, 8104, 22311, 22312, 22313, 22314, 22315,
}
local fall_itemobj_list = {}
-- 掉落物测试
function ClientCmdWGCtrl:AddFallItme(params)
	if params[1] ~= nil and params[1] ~= "" then
		local num = tonumber(params[1])
		if num ~= nil and num > 0 then
			local main_role = Scene.Instance:GetMainRole()
			if main_role then
				local pos_x, pos_y = main_role:GetLogicPos()
				for i = 1, num do
					local fallitem_vo = GameVoManager.Instance:CreateVo(FallItemVo)
					fallitem_vo.item_id = fall_item_list[math.random(1, #fall_item_list)]
					fallitem_vo.pos_x = pos_x + math.random(-10, 10)
					fallitem_vo.pos_y = pos_y + math.random(-10, 10)
					local item_obj = Scene.Instance:CreateFallItem(fallitem_vo)
					table.insert(fall_itemobj_list, item_obj)
				end
			end
		else
			for k,v in pairs(fall_itemobj_list) do
				local obj_id = v:GetObjId()
				Scene.Instance:DeleteObj(obj_id)
			end
			fall_itemobj_list = {}
		end
	end
end

-- 掉落物测试
function ClientCmdWGCtrl:AddFallItme2(params)
	if params[1] == "on" then
		local main_role = Scene.Instance:GetMainRole()
		if main_role == nil then
			return
		end

		local pos_x, pos_y = main_role:GetLogicPos()

		self.time_quester = GlobalTimerQuest:AddRunQuest(function()
			for k,v in pairs(fall_itemobj_list) do
				local obj_id = v:GetObjId()
				Scene.Instance:DeleteObj(obj_id)
			end
			fall_itemobj_list = {}

			local m_role = Scene.Instance:GetMainRole()
			if m_role == nil then
				return
			end

			local pos_xx, pos_yy = m_role:GetLogicPos()
			for i = 1, 10 do
				local fallitem_vo = GameVoManager.Instance:CreateVo(FallItemVo)
				fallitem_vo.item_id = fall_item_list[math.random(1, #fall_item_list)]
				fallitem_vo.pos_xx = pos_xx + math.random(-10, 10)
				fallitem_vo.pos_yy = pos_yy + math.random(-10, 10)
				local item_obj = Scene.Instance:CreateFallItem(fallitem_vo)
				table.insert(fall_itemobj_list, item_obj)
			end
		end, 0.2)
	else
		GlobalTimerQuest:CancelQuest(self.time_quester)
		self.time_quester = nil
		for k,v in pairs(fall_itemobj_list) do
			local obj_id = v:GetObjId()
			Scene.Instance:DeleteObj(obj_id)
		end
		fall_itemobj_list = {}
	end
end

local monster_list = {}
-- 添加怪物
function ClientCmdWGCtrl:AddMonster(params)
	if params[1] ~= nil and params[1] ~= "" then
		local num = tonumber(params[1])
		if num ~= nil and num > 0 then
			local main_role = Scene.Instance:GetMainRole()
			if main_role == nil then
				return
			end

			local pos_x, pos_y = main_role:GetLogicPos()
			for i = 1, num do
				local monster_vo = GameVoManager.Instance:CreateVo(MonsterVo)
				monster_vo.monster_id = 11476
				monster_vo.max_hp = 10000
				monster_vo.hp = 10000
				monster_vo.pos_x = pos_x + math.random(-10, 10)
				monster_vo.pos_y = pos_y + math.random(-10, 10)
				table.insert(monster_list, Scene.Instance:CreateMonster(monster_vo))
			end
		else
			for k, v in pairs(monster_list) do
				Scene.Instance:DeleteObj(v:GetObjId())
			end
			monster_list = {}
		end
	end
end

-- 添加怪物
function ClientCmdWGCtrl:AddMonster2(params)
	if params[1] == "on" then
		self.time_quester = GlobalTimerQuest:AddRunQuest(function()
			for k, v in ipairs(monster_list) do
				local obj_id = v:GetObjId()
				Scene.Instance:DeleteObj(obj_id)
				GlobalEventSystem:Fire(SceneEventType.OBJ_LEVEL_ROLE, obj_id)
			end
			monster_list = {}

			local main_role = Scene.Instance:GetMainRole()
			if main_role == nil then
				return
			end

			local pos_x, pos_y = main_role:GetLogicPos()
			for i = 1, 10 do
				local monster_vo = GameVoManager.Instance:CreateVo(MonsterVo)
				monster_vo.monster_id = 10702
				monster_vo.pos_x = pos_x + math.random(-10, 10)
				monster_vo.pos_y = pos_y + math.random(-10, 10)
				table.insert(monster_list, Scene.Instance:CreateMonster(monster_vo))
				GlobalEventSystem:Fire(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER, monster_vo)
			end
		end, 0.2)
	else
		GlobalTimerQuest:CancelQuest(self.time_quester)
		self.time_quester = nil
		for k, v in ipairs(monster_list) do
			local obj_id = v:GetObjId()
			Scene.Instance:DeleteObj(obj_id)
			GlobalEventSystem:Fire(SceneEventType.OBJ_LEVEL_ROLE, obj_id)
		end
		monster_list = {}
	end
end

local role_list = {}
-- 添加角色
function ClientCmdWGCtrl:AddRole(params)
	local num = tonumber(params[1])
	if num > 0 then
		local main_vo = GameVoManager.Instance:GetMainRoleVo()
		local pos_x = main_vo.pos_x
		local pos_y = main_vo.pos_y
		for i = 1, num do
			main_vo.obj_id = -1
			main_vo.pos_x = pos_x + math.random(-10, 10)
			main_vo.pos_y = pos_y + math.random(-10, 10)
			local role = Scene.Instance:CreateRole(main_vo)
			if role then
				table.insert(role_list, role:GetObjId())
				main_vo.pos_x = pos_x
				main_vo.pos_y = pos_y
			end
		end
	else
		for k,v in pairs(role_list) do
			Scene.Instance:DeleteObj(v)
		end
		role_list = {}
	end
end

-- 显示FPS
function ClientCmdWGCtrl:ShowFPS(params)
	local on_off = "on" == params[1] and true or false
	local c_on_off = on_off and "true" or "false"
	GameObject.Find("GameRoot/UILayer/FPSControl"):GetComponent(typeof(ShowFPS)).SetSwich(c_on_off)
	local fps_ctrl_obj = GameObject.Find("GameRoot/UILayer/FPSControl")
	if fps_ctrl_obj and not IsNil(fps_ctrl_obj) then
		fps_ctrl_obj:SetActive(on_off)
	end
end

-- 设置FPS
function ClientCmdWGCtrl:SetFps(params)
	local fps = 60
	if "" ~= params[1] and nil ~= params[1] and tonumber(params[1]) >= -1 and tonumber(params[1]) <= 80 then
		fps = tonumber(params[1])
	end

	fps = fps or 60
	QualityManager.Instance:SetCustomTargetFrame(fps)
end

local follow_ui_shield_rule = {}
-- 添加规则
function ClientCmdWGCtrl:AddRule(params)
	local shield_type = ShieldObjType[params[1]]
	local hide = "on" == params[2] and true or false
	if hide then
		if nil == follow_ui_shield_rule[shield_type] then
			follow_ui_shield_rule[shield_type] = SimpleRule.New(shield_type, ShieldRuleWeight.High, function ()
				return true
			end)
		end
		follow_ui_shield_rule[shield_type]:Register()
	else
		if follow_ui_shield_rule[shield_type] then
			follow_ui_shield_rule[shield_type]:DeleteMe()
			follow_ui_shield_rule[shield_type] = nil
		end
	end
end

-- 设置可视obj 最大显示数量
function ClientCmdWGCtrl:ChangeVisibleCount(params)
	local shield_type = ShieldObjType[params[1]]
	local count = tonumber(params[2])
	if count then
		QualityManager.Instance:SetMaxAppearCount(shield_type, count)
	end
end

local camera_follow_obj
-- 取消主摄相机目标
function ClientCmdWGCtrl:StopCamera(params)
	local on_off = "on" == params[1] and true or false
	if on_off then
		camera_follow_obj = MainCameraFollow.Target
		MainCameraFollow.Target = nil
	elseif camera_follow_obj then
		MainCameraFollow.Target = camera_follow_obj
	end
end

local followui_list = {}
-- 创建 头顶信息
function ClientCmdWGCtrl:AddFollowUI(params)
	local num = tonumber(params[1])
	if num > 0 then
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			local point = main_role.draw_obj:GetAttachPoint(AttachPoint.UI)
			local pos = point.position
			for i = 1, num do
				local follow_ui = RoleFollow.New()
				follow_ui:Create(SceneObjType.Role)
					-- 默认不显示血条
				follow_ui:SetHpVisiable(false)
				local obj = ResMgr:CreateEmptyGameObj(nil, true)
				obj.transform.position = Vector3(pos.x + math.random(-10, 10), pos.y, pos.z + math.random(-10, 10))
				follow_ui:SetFollowTarget(obj.transform, main_role.draw_obj:GetName())
				follow_ui:SetJinjieIcon(1)
				follow_ui:SetZhuanzhiIconVisible(false)
				follow_ui:SetName("傻嗨福建鹏", main_role)
				table.insert(followui_list, {follow_ui = follow_ui, obj = obj})
			end
		end
	else
		for k,v in pairs(followui_list) do
			ResMgr:Destroy(v.obj)
			v.follow_ui:DeleteMe()
		end
		followui_list = {}
	end
end

-- 检测lua耗时
function ClientCmdWGCtrl:CheckLuaCostTime(params)
	if not UNITY_EDITOR then
		print_error("not UNITY_EDITOR")
		return
	end

	local on_off = "on" == params[1] and true or false
	if on_off then
		local max_depth = tonumber(params[2]) or 0
		CheckFuntionUseTime:Start(max_depth)
	else
		CheckFuntionUseTime:Stop()
	end
end

-- 检测lua耗内存
function ClientCmdWGCtrl:CheckLuaCostMem(params)
	if not CheckFuntionUseMem:IsCollecting() then
		TipWGCtrl.Instance:OpenConfirmAlertTips("开始内存统计")
		CheckFuntionUseMem:StartCollectMem()
	else
		TipWGCtrl.Instance:OpenConfirmAlertTips("保存成功")
		CheckFuntionUseMem:WriteAllStack()
	end
end

-- 检查类Obj计数
function ClientCmdWGCtrl:CheckClassObjCount(params)
	CheckClassCount:WriteAllStack()
end

-- 更新lua
function ClientCmdWGCtrl:UpdateLua(params)
	local param = ""
	if type(params) == "table" then
		param = params[1]
	elseif type(params) == "string" then
		param = params
	end

	local num = string.find(param, "Lua")
	if num then
		param = string.sub(param, num + 4)
	end

	param = string.gsub(param, "%.lua", "")
	param = string.gsub(param, '\\', '/')
	param = string.gsub(param, '/', '.')

	local old_tb = package.loaded[param]
	if old_tb then
		package.loaded[param] = nil
		local ok, err = pcall(require, param)
		if not ok then
			package.loaded[param] = old_tb
			print_error("重载失败 -- ", param)
			return
		end
		print_error(string.format("<color=#28e53aFF>重载成功 -- %s</color>", param))
	else
		print_error("未找到 -- ", param)
	end
end

-- 更新lua
function ClientCmdWGCtrl:UpdateGM()
	if package.loaded["hot_file"] then
		package.loaded["hot_file"] = nil
		require("hot_file")
	else
		require("hot_file")
	end

	local hot_flie = require("hot_file")
	for k,v in pairs(hot_flie) do
		self:UpdateLua(v)
	end
end

function ClientCmdWGCtrl:TestReloadFunc()
	local load_list = {
        "game/clientcmd/reload_test",
    }
    for i, v in pairs(load_list) do
        _G.package.loaded[string.gsub(v, "/", ".")] = nil
	    require(v)
    end
	ReloadTest()
end

-- GM获得
function ClientCmdWGCtrl:QuickItem(params)
	QUICK_ADDITEM = true
end

-- 执行lua方法
function ClientCmdWGCtrl:InsertLua(params)
	-- print_error(params[1])
	local func = assert(loadstring(params[1]))
	func()
end

-- 主界面ui可视
function ClientCmdWGCtrl:MainUiVisib(params)
	if MainuiWGCtrl and MainuiWGCtrl.Instance then
		MIANUI_VIEW_EDITOR_FLAG = true
		MainuiWGCtrl.Instance:OpenMainUiModeView()
	end
end

-- 元宝特效
function ClientCmdWGCtrl:DoMoneyEffect(params)
	FightWGCtrl.Instance:DoMoneyEffect(GameEnum.NEW_MONEY_BAR.SILVER_TICKET)
end

-- 随机切场景
function ClientCmdWGCtrl:TestChangeScene(params)
	local count = tonumber(params[1]) or 999
	local change_scene_func = function ()
		count = count - 1
		if count < 0 then
			return
		end

		local scene_list = {101,102,103,104,105,106,107,108,109}
		local target_scene_id = scene_list[count % 9 + 1]
		GlobalTimerQuest:AddDelayTimer(function ()
			GuajiWGCtrl.Instance:FlyToScene(target_scene_id, true, 1)
		end, 1)
	end

	GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, change_scene_func)
	change_scene_func()
end

-- 热更新所有文件
function ClientCmdWGCtrl:GMHotupdate(params)
	local is_debug = params[1] and true or false
	Hotupdate.Instance:GMHotupdate(is_debug)
end

-------------------------- 一键获取转职高阶装备 --------------------------
local RoleProfFifteenthOrderEquips = {
	[0] =  {
		[1] = {414, 1414, 2414, 3214, 4414, 5414, 7414, 6214, 8214, 9214},
		[3] = {414, 1414, 2414, 3214, 4414, 5814, 7414, 6214, 8214, 9214}
	},
	[1] =  {
		[1] = {214, 1214, 2214, 3214, 4214, 5214, 7214, 6214, 8214, 9214},
		[3] = {214, 1214, 2214, 3214, 4214, 5614, 7214, 6214, 8214, 9214}
	}
}

function ClientCmdWGCtrl:GetRoleProfFifteenthOrderEquips()
	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local equip_list = RoleProfFifteenthOrderEquips[sex][prof]

	self:GmCmdList({[1] = 4})

	for k,v in pairs(equip_list) do
		local item_gm = string.format("%s 1 0", v)
		SysMsgWGCtrl.SendGmCommand("additem", item_gm)
	end
end
------------------------------------------------------------------------------

-------------------------- 转职 --------------------------
local zhuanzhi_task_id = {
	[1] = {29001, 29002, 29003, 29004, 29005},
	[2] = {29006, 29007, 29008, 29009, 29010},
	[3] = {29011, 29012, 29013, 29014, 29015, 29016},
	[4] = {29017, 29018, 29019, 29020, 29021, 29022},
	[5] = {29023, 29024, 29025, 29026, 29027, 29028},
	[6] = {29029, 29030, 29031, 29032, 29033, 29034},
}
function ClientCmdWGCtrl:OnZhuanzhi(params)
	local zhuan_num = tonumber(params[1])
	-- if zhuan_num <= 1 and zhuan_num >= #zhuanzhi_task_id then
	-- 	return
	-- end
	local zhuanzhi_task_id = TaskWGData.Instance:GetZhuanzhiCfg()
	for i = 1, zhuan_num do
		local task_data = zhuanzhi_task_id[i]
		if task_data then
			for i = task_data.first_task, task_data.end_task, 1 do
				SysMsgWGCtrl.SendGmCommand("finishzhuanzhitask", i)
			end
		end
		RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_ZHUANZHI)
	end
end
-----------------------------------------------------------

-- 属性监听
function ClientCmdWGCtrl:AttrMonitor(params)
	local is_on = "on" == params[1]
	if RoleWGCtrl and RoleWGCtrl.Instance then
		TipWGCtrl.Instance:ShowSystemNotice(is_on and "开启属性监听" or "关闭属性监听")
		RoleWGCtrl.Instance:SetAttrMonitorValue(is_on)
	end
end

----------------------------任务小游戏860-------------------------------
function ClientCmdWGCtrl:OpenTaskGame(params)
	if not params[1] then
		print_error("请输入小游戏任务id")
		return
	end
	local task_id = params[1]
	local task_cfg = TaskWGData.Instance:GetTaskConfig(tonumber(task_id))
	TaskWGCtrl.Instance:OpenTaskDice(task_cfg)
	TaskWGCtrl.Instance:ClearOldDiceTask()
end

-- 打印挂机状态信息
function ClientCmdWGCtrl:GetGuaJiState()
	print_error("========>GetGuaJiState")

	local str1 = string.format(
		"AtkCache.atk_type = %s, AtkCache.skill_id = %s, AtkCache.x = %s, AtkCache.y = %s, AtkCache.dir = %s, AtkCache.is_specialskill = %s, AtkCache.special_distance = %s, AtkCache.target_obj == nil = %s, AtkCache.target_obj_id = %s, AtkCache.range = %s, AtkCache.next_sync_pos_time = %s, AtkCache.attack_index = %s, AtkCache.is_valid = %s\n",
		AtkCache.atk_type,
		AtkCache.skill_id,
		AtkCache.x,
		AtkCache.y,
		AtkCache.dir,
		AtkCache.is_specialskill,
		AtkCache.special_distance,
		AtkCache.target_obj == nil,
		AtkCache.target_obj_id,
		AtkCache.range,
		AtkCache.next_sync_pos_time,
		AtkCache.attack_index,
		AtkCache.is_valid)


	local str2 = string.format(
		"GuajiCache.guaji_type = %s, GuajiCache.target_obj == nil = %s, GuajiCache.target_obj_id = %s, GuajiCache.is_click_select = %s, GuajiCache.monster_id = %s, GuajiCache.event_guaji_type = %s\n",
		GuajiCache.guaji_type,
		GuajiCache.target_obj == nil,
		GuajiCache.target_obj_id,
		GuajiCache.is_click_select,
		GuajiCache.monster_id,
		GuajiCache.event_guaji_type)

	local str3 = string.format(
		"GuajiCache.cant_fly = %s, GuajiCache.is_valid = %s, GuajiCache.is_move_scan = %s, GuajiCache.end_type = %s, GuajiCache.move_type = %s, GuajiCache.scene_id = %s, GuajiCache.x = %s, GuajiCache.y = %s, GuajiCache.target_obj == nil = %s, GuajiCache.target_obj_id = %s, GuajiCache.range = %s, GuajiCache.task_id = %s, GuajiCache.task_type = %s, GuajiCache.param1 = %s, GuajiCache.param2 = %s\n",
		GuajiCache.cant_fly,
		GuajiCache.is_valid,
		GuajiCache.is_move_scan,
		GuajiCache.end_type,
		GuajiCache.move_type,
		GuajiCache.scene_id,
		GuajiCache.x,
		GuajiCache.y,
		GuajiCache.target_obj == nil,
		GuajiCache.target_obj_id,
		GuajiCache.range,
		GuajiCache.task_id,
		GuajiCache.task_type,
		GuajiCache.param1,
		GuajiCache.param2)

	local goto_pick_x, goto_pick_y, next_can_goto_pick_time = GuajiWGCtrl.Instance:GetPickCache()
	local server = TimeWGCtrl.Instance:GetServerTime()

	local str4 = string.format(
		"goto_pick_x = %s, goto_pick_y = %s, next_can_goto_pick_time = %s, server = %s\n",
		goto_pick_x,
		goto_pick_y,
		next_can_goto_pick_time,
		server)

	local cfg, step = FunctionGuide.Instance:GetGuideInfo()

	local str5 = string.format(
		"IsCgIng() = %s, IsSceneLoading() = %s, IsOpen(GuideModuleName.TaskDialog) = %s, cfg = %s, step = %s\n",
		CgManager.Instance:IsCgIng(),
		Scene.Instance:IsSceneLoading(),
		ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog),
		cfg,
		step)

	local str6 = ""
	local main_role = Scene.Instance:GetMainRole()

	if main_role ~= nil then
		str6 = string.format(
			"IsDead = %s, GetIsGatherState = %s, IsAtkPlaying = %s, IsFollowState = %s, IsQingGong = %s, IsJump = %s, CanDoMove = %s, IsRealDead = %s, IsChongfenging = %s, IsStand = %s, IsMove = %s, IsAtk = %s, IsHurt = %s, IsMoving = %s\n",
			main_role:IsDead(),
			main_role:GetIsGatherState(),
			main_role:IsAtkPlaying(),
			main_role:IsFollowState(),
			main_role:IsQingGong(),
			main_role:IsJump(),
			main_role:CanDoMove(),
			main_role:IsRealDead(),
			main_role:IsChongfenging(),
			main_role:IsStand(),
			main_role:IsMove(),
			main_role:IsAtk(),
			main_role:IsHurt(),
			main_role:IsMoving())
	else
		str6 = "MainRole Is Nil"
	end

	local str7 = "Accept List "
	local table_list1 = TaskWGData.Instance:GetTaskAcceptedInfoList()
	if table_list1 ~= nil then
		local index = 0
		for k,v in pairs(table_list1) do
			if type(v) == "table" then
				str7 = str7 .. k .. " = " ..  TableToStr(v) .. "\n"
			else
				str7 = str7 .. k .. " = " ..  v .. "  "
			end
			index = index + 1
		end

		str7 = str7 .. "\n"
	else
		str7 = "Accept List Is Nil\n"
	end

	local str8 = string.format(
		"MoveCache cant_fly = %s, is_valid = %s, is_move_scan = %s, end_type = %s, move_type = %s, scene_id = %s, x = %s, y = %s, target_obj = %s, target_obj_id = %s, range = %s, task_id = %s, task_type = %s, param1 = %s, param2 = %s \n",
		MoveCache.cant_fly,
		MoveCache.is_valid,
		MoveCache.is_move_scan,
		MoveCache.end_type,
		MoveCache.move_type,
		MoveCache.scene_id,
		MoveCache.x,
		MoveCache.y,
		MoveCache.target_obj == nil,
		MoveCache.target_obj_id,
		MoveCache.range,
		MoveCache.task_id,
		MoveCache.task_type,
		MoveCache.param1,
		MoveCache.param2)


	local str9 = "Complete List "
	local table_list2 = TaskWGData.Instance:GetTaskCompletedList()
	if table_list2 ~= nil then
		local index = 0
		for k,v in pairs(table_list2) do
			if type(v) == "table" then
				str9 = str9 .. k .. " = " ..  TableToStr(v) .. "\n"
			else
				str9 = str9 .. k .. " = " ..  v .. "  "
			end

			index = index + 1
		end

		str9 = str9 .. "\n"
	else
		str9 = "Complete List Is Nil\n"
	end

	local str10 = "CanAccept List "
	local table_list3 = TaskWGData.Instance:GetTaskCapAcceptedIdList()
	if table_list3 ~= nil then
		local index = 0
		for k,v in pairs(table_list3) do
			if type(v) == "table" then
				str10 = str10 .. k .. " = " ..  TableToStr(v) .. "\n"
			else
				str10 = str10 .. k .. " = " ..  v .. "  "
			end

			index = index + 1
		end

		str10 = str10 .. "\n"
	else
		str10 = "CanAccept List Is Nil\n"
	end

	local str11 = "TaskCache List "
	local table_list4 = TaskWGData.Instance:GetTaskListCache()
	if table_list4 ~= nil then
		local index = 0
		for k,v in pairs(table_list4) do
			if type(v) == "table" then
				str11= str11.. k .. " = " ..  TableToStr(v) .. "\n"
			else
				str11= str11.. k .. " = " ..  v .. "  "
			end

			index = index + 1
		end

		str11 = str11 .. "\n"
	else
		str11 = "TaskCache List Is Nil\n"
	end

	local str12 = string.format("CurrTaskId List = %s\n", TaskWGData.Instance:GetCurrTaskId())

	local scene_logic = Scene.Instance:GetSceneLogic()
	local str13 = ""
	if scene_logic ~= nil then
		if GuajiCache.target_obj ~= nil then
			if GuajiCache.target_obj:IsDeleted() then
				str13 = "SceneLogic State  Select Obj Is Nil%s\n"
			else
				str13 = string.format("SceneLogic State = %s, %s\n", GuajiCache.target_obj.vo.obj_id, GuajiCache.target_obj.vo.name or "IsNil", scene_logic:IsEnemy(GuajiCache.target_obj))
			end
		end
	else
		str13 = "SceneLogic Is Nil%s\n"
	end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local str14 = string.format("role_name = %s\n", main_role_vo.role_name or "")

	print_error(str14 .. str1 .. str2 .. str3 .. str4 .. str5 .. str6 .. str7 .. str8 .. str9 .. str10 .. str11 .. str12 .. str13)
end

-- 添加机器人
function ClientCmdWGCtrl:AddRobert(params)
	local num = tonumber(params[1])

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
        return
    end
	local m_x, m_y = main_role:GetLogicPos()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local robert_cfg = ConfigManager.Instance:GetAutoConfig("story_auto")["robert_role"]
	local show_cfg = TableCopy(robert_cfg[1006])
	show_cfg.born_x = m_x + math.random(5, 10)
	show_cfg.born_y = m_y + math.random(5, 10)

	for i = 1, num do
		RobertManager.Instance:CreateRoleRobert(show_cfg, false)
	end
end

-- 游戏重启
function ClientCmdWGCtrl:GameRestart()
	GameRestart()
end


function ClientCmdWGCtrl:ReloadJumpConfig()
	self:UpdateLua("game/scene/scene_config")
end

-- 所有红点
function ClientCmdWGCtrl:ShowRemindChange()
	RemindManager.Instance:CmdShowAllSystemRemind()
end

-- 红点等待的执行回调队列信息
function ClientCmdWGCtrl:ShowRemindWait()
	RemindManager.Instance:CmdWaitExecuteInfo()
end

-- 红点等待的计算队列信息
function ClientCmdWGCtrl:ShowRemindQueue()
	RemindManager.Instance:CmdWaitQueueInfo()
end

-- 单个红点
function ClientCmdWGCtrl:ShowRemindInfo(params)
	local remind_name = params[1]
	RemindManager.Instance:CmdShowRemindInfo(remind_name)
end

-- 显示标签红点
function ClientCmdWGCtrl:ShowRemindTabbar(params)
	local view = ViewManager.Instance:GetView(params[1])
	if view ~= nil then
		local tab = view.tabbar
		if tab ~= nil then
			tab:GetRemindInfo()
		end
	end
end

-- 添加神兽装备
function ClientCmdWGCtrl:AddShenShouEquip()
	ShenShouWGData.Instance:CMDAddShenShouEquip()
end

function ClientCmdWGCtrl:GetSkillRangeTool()
	if self.skill_tool == nil then
		self.skill_tool = SkillRangeTool.New()
		local async_loader = AllocAsyncLoader(self, "GmDraw")
		async_loader:SetIsUseObjPool(true)
		async_loader:Load("misc_prefab", "SkillDraw", function(obj)
			if IsNil(obj) then
				async_loader:Destroy()
				return
			end

			local Root = GameObject.Find("GameRoot").transform
			if Root then
				local obj_transform = obj.transform
				obj_transform:SetParent(Root, false)
				self.skill_tool:SetDrawLineObj(obj)
				self:DrawGuaJiPos({"on"})
			else
				async_loader:Destroy()
			end
		end)
	end

	return self.skill_tool
end

-- 绘制挂机点
function ClientCmdWGCtrl:DrawGuaJiPos(params)
	local is_on = "on" == params[1]
	local draw_param = params[2] or 0
	local draw_type = tonumber(draw_param)

	local data = nil
	if is_on then
		local logic = Scene.Instance:GetSceneLogic()
		if logic ~= nil then
			local info = logic:GetGuaJiInfo()
			if info == nil then
				is_on = false
			else
				data = info
			end
		end
	end

	local tool = self:GetSkillRangeTool()
	if tool ~= nil then
		tool:SetOpenDraw(is_on)

		local main_role = Scene.Instance:GetMainRole()
		if is_on and main_role ~= nil then
			local m_p = main_role:GetLuaPosition()
			local real_x, real_y = GameMapHelper.LogicToWorld(data.x, data.y)
			local p = {x = real_x, z = real_y, y = m_p.y}
			local r = draw_type == 0 and data.aoi_range or data.run_range
			tool:DrawSkillRange(ATTACK_RANGE_TYPE.CIRCULAR_TARGET, p, r)
		end
	end
end

-- 绘制技能范围
function ClientCmdWGCtrl:DrawSkillShow(params)
	local is_on = "on" == params[1]
	local draw_param = params[2] or 0
	local r = params[3] ~= nil and tonumber(params[3]) or 0
	local r2 = params[4] ~= nil and tonumber(params[4]) or 0
	local skill_id = tonumber(draw_param)

	local data = nil
	if is_on then
		data = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
	end

	local tool = self:GetSkillRangeTool()
	if tool ~= nil then
		tool:SetOpenDraw(is_on)

		local main_role = Scene.Instance:GetMainRole()
		if is_on and main_role ~= nil and data ~= nil then
			local m_p = main_role:GetLuaPosition()
			-- local real_x, real_y = GameMapHelper.LogicToWorld(data.x, data.y)
			-- local p = {x = real_x, z = real_y, y = m_p.y}
			-- local r = draw_type == 0 and data.aoi_range or data.run_range
			local angle = 0
			r = r ==  0 and data.attack_range or r
			r2 = r2 ==  0 and data.attack_range2 or r2
			if data.range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
				local draw_obj = main_role:GetDrawObj()
				angle = draw_obj.root_transform.eulerAngles.y
			elseif data.range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
				m_p = Vector3(m_p.x + r * 0.5, m_p.y, m_p.z + r2 * 0.5)
			end
			tool:DrawSkillRange(data.range_type, m_p, r, r2, angle)
		end
	end

	-- local main_role = Scene.Instance:GetMainRole()
	-- main_role:SetSpecailStateInfo(OBJ_SPECIAL_STATE.STRIKE_FLY, 1)
end

-- 绘制圆形限制
function ClientCmdWGCtrl:DrawCircleLimit(params)
	local is_on = "on" == params[1]
	local rang = tonumber(params[2])

	local tool = self:GetSkillRangeTool()
	if tool ~= nil then
		tool:SetOpenDraw(is_on)

		local main_role = Scene.Instance:GetMainRole()
		if is_on and main_role ~= nil then
			local m_p = main_role:GetLuaPosition()
			local limit_pos = main_role:GetCircleLimitPos()
			local x = tonumber(params[3])
			local y = tonumber(params[4])
			if (x == nil or y == nil) and limit_pos ~= nil then
				x = limit_pos.x
				y = limit_pos.y
			end

			if x == nil or y == nil then
				SysMsgWGCtrl.Instance:ErrorRemind("当前没有限制移动的区域")
				return
			end

			local real_x, real_y = GameMapHelper.LogicToWorld(x, y)
			local p = {x = real_x, z = real_y, y = m_p.y}
			local r = rang
			tool:DrawSkillRange(ATTACK_RANGE_TYPE.CIRCULAR_TARGET, p, r)
		end
	end
end

-- 添加八卦装备 星数+数量
function ClientCmdWGCtrl:AddAllBaGuaEquip(param_t)
	local star_num = tonumber(param_t[1])
	local num = tonumber(param_t[2])
	local all_cfg = TianShenBaGuaWGData.Instance:GetAllBaGuaEquipCfg()
	for k,v in pairs(all_cfg) do
		if v.star == star_num then
			for n = 1,num do
				SysMsgWGCtrl.SendGmCommand("additem", k.." 1 0")
			end
		end
	end
end

-- 添加八卦装备 星数+品质
function ClientCmdWGCtrl:AddBaGuaEquip(param_t)
	local star_num = tonumber(param_t[1])
	local color = tonumber(param_t[2])
	local num = 1
	color = color -1
	color = color >= 0 and color or 0
	local all_cfg = TianShenBaGuaWGData.Instance:GetAllBaGuaEquipCfg()
	for k,v in pairs(all_cfg) do
		if v.index == color and v.star == star_num then
			for n = 1,num do
				SysMsgWGCtrl.SendGmCommand("additem", k.." 1 0")
			end
		end
	end
end

-- 一个打印开启标记
function ClientCmdWGCtrl:SetLogState(param)
	if param[1] ~= nil and param[1] ~= "" then
		CLIENT_DEBUG_LOG_STATE = tonumber(param[1]) ~= 0
	end
end

-- 模拟击飞
function ClientCmdWGCtrl:SetStrikeParam(param)
	if param == nil or #param < 9 then
		print_error("GM 参数错误, 参数数量不足，要8个")
		return
	end

	-- 击飞时间
	local time = param[1] ~= nil and tonumber(param[1]) or 1
	-- 上升过程百分比
	STRIKE_FLY_UP_PRO = param[2] ~= nil and tonumber(param[2]) or STRIKE_FLY_UP_PRO
	-- 下降过程百分比
	STRIKE_FLY_DOWN_PRO = param[3] ~= nil and tonumber(param[3]) or STRIKE_FLY_DOWN_PRO
	-- 浮空过程百分比
	STRIKE_FLY_KEEP_PRO = param[4] ~= nil and tonumber(param[4]) or STRIKE_FLY_KEEP_PRO
	-- 开始触发角度变化时间
	STRIKE_ROTATIE_BEGIN_TIME = param[5] ~= nil and tonumber(param[5]) or STRIKE_ROTATIE_BEGIN_TIME
	-- 触发抖动的时间，必须要浮空时间比这个大，才会抖动
	STRIKE_SHAKE_TIME = param[6] ~= nil and tonumber(param[6]) or STRIKE_SHAKE_TIME
	-- 击飞角度变化
	STRIKE_ROTATIE = param[7] ~= nil and tonumber(param[7]) or STRIKE_ROTATIE
	-- 击飞过程最大高度
	STRIKE_FLY = param[8] ~= nil and tonumber(param[8]) or STRIKE_FLY
	-- 浮空高度
	STRIKE_SHAKE_HIGH = param[9] ~= nil and tonumber(param[9]) or STRIKE_SHAKE_HIGH

	Scene.Instance:GetMainRole():SetSpecailStateInfoByBuff(BUFF_TYPE.EBT_KNOCK_FLY, nil, time)
end

-- 主摄+ui摄 allowHDR开关
function ClientCmdWGCtrl:SwitchUIHDR(param)
	local is_on = param[1] == "on"
	local main_is_on = param[2] ~= nil and param[2] == "on" or false
	local ui_camera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
	ui_camera.allowHDR = is_on

	if not IsNil(MainCamera) then
		MainCamera.allowHDR = main_is_on
	end

	print_error("SwitchUIHDR", is_on, main_is_on)
end

-- 播放音效
function ClientCmdWGCtrl:PlayAudio(param)
	local bundle = param[1]
	local asset = param[2]
	if bundle ~= nil and asset ~= nil then
       AudioManager.PlayAndForget(bundle, asset, nil, nil)
	end
end

function ClientCmdWGCtrl:ClearDownloadBuffer(param)
	local size = tonumber(param[1]) or 0
	local count = tonumber(param[2]) or 0

	if size <= 0 then
		DownloadBufferMgr.DestoryAllBuffer()
	else
		DownloadBufferMgr.DestoryBuffer(size, count)
	end
end

-- 鸭子赛跑 弹幕
function ClientCmdWGCtrl:BarrageTest(params)
	local count = params[1] or 100
	local str = params[2] or "弹幕弹幕弹幕。。。。。"
	for i = 1, tonumber(count) do
		DuckRaceWGCtrl.Instance:OpenBarrageView(str)
	end
end

-- 打开界面
function ClientCmdWGCtrl:GmOpenView(param_t)
	local view_name = param_t[1]
	if view_name == nil or view_name == "" then
		return
	end

	ViewManager.Instance:Open(param_t[1], param_t[2], param_t[3], param_t[4])
end

-- 添加四象装备   param_t = 四象类型  套装类型  品质  数量
function ClientCmdWGCtrl:AddSiXiangEquip(param_t)
	local fs_type = param_t[1] or 1
	local suit_type = param_t[2] or 0
	local color = param_t[3] or 3
	local num = param_t[4] or 1

	local item_id = 0
	for i = 0, FIGHT_SOUL_BONE_TYPE.MAX - 1 do
		item_id = FightSoulWGData.GetBoneItemIdByParam(fs_type, i, suit_type, color)
		SysMsgWGCtrl.SendGmCommand("additem", string.format("%s %s 0", item_id, num))
	end
end

-- 添加仙界装备    param_t = 神体类型  品质  数量
function ClientCmdWGCtrl:AddHolyEquip(param_t)
	local slot = tonumber(param_t[1]) or 0
	local color = tonumber(param_t[2]) or 1
	local num = param_t[3] or 1
	local fle_data = FairyLandEquipmentWGData.Instance
	local max_part = XIANJIE_EQUIP_TYPE.XIANYIN
	for part = 0, max_part do
		local cfg = fle_data:GetHolyEquipItemMapCfg(slot, part, color)
		if cfg then
			SysMsgWGCtrl.SendGmCommand("additem", string.format("%s %s 0", cfg.id, num))
		end
	end
end

-- addday n次
function ClientCmdWGCtrl:AddDayNum(param_t)
	local num = param_t[1] or 0
	for i = 1, num do
		SysMsgWGCtrl.SendGmCommand("addday", "")
	end
end

-- 退出游戏
function ClientCmdWGCtrl:TestExitGame()
	AgentAdapter:OnExit()
end

-- 珍稀掉落界面
function ClientCmdWGCtrl:TestRareItemShow()
	RareItemDropWGCtrl.Instance:TestRareItemShow()
end

local orginal_screen_width = 0
local orginal_screen_height = 0
-- 限制屏幕分辨率
function ClientCmdWGCtrl:LimitScreenResolution(params)
	local limit = tonumber(params[1]) or 0
	local screen = UnityEngine.Screen
	if 0 == orginal_screen_width then
		orginal_screen_width = screen.width
	end

	if 0 == orginal_screen_height then
		orginal_screen_height = screen.height
	end

	if orginal_screen_width <= 0 or orginal_screen_height <= 0 then
		return
	end

	if orginal_screen_width > orginal_screen_height then
		if orginal_screen_height > limit then
			local radio = orginal_screen_width / orginal_screen_height
			screen.SetResolution(math.floor(limit * radio), limit, true)
		else
			screen.SetResolution(orginal_screen_width, orginal_screen_height, true)
		end
	else
		if orginal_screen_width > limit then
			local radio = orginal_screen_width / orginal_screen_height
            screen.SetResolution(limit, math.floor(limit * radio), true)
		else
			screen.SetResolution(orginal_screen_width, orginal_screen_height, true)
		end
	end
end

-- Android硬件跟踪管理器
function ClientCmdWGCtrl:ShowHardwareTrackManager(params)
	local is_on = "on" == params[1]
	local t = GameObject.Find("GameRoot").transform
	if is_on then
		local manager = t:GetOrAddComponent(typeof(YYAndroidHardwareTrackManager))
		if not IsNil(manager) then
			manager.enabled = true
		end
	else
		local manager = t:GetComponent(typeof(YYAndroidHardwareTrackManager))
		if not IsNil(manager) then
			manager.enabled = false
		end
	end
end

local protocol_t = {}
local protocol_test_time = 0
local protocol_step = 60
local protocol_cost_time_list = {}
function ClientCmdWGCtrl:CollectProtocolTime(params)
	local is_on = "on" == params[1]
	local step = params[2]
	BaseProtocolStruct.COLLECT_PRINT = is_on or false
	if step ~= nil then
		protocol_step = tonumber(step) or 60
	end
end

function ClientCmdWGCtrl:AddProtocolMsg(msg_id, time)
	protocol_t[msg_id] = protocol_t[msg_id] and protocol_t[msg_id] + 1 or 1
	protocol_cost_time_list[msg_id] = protocol_cost_time_list[msg_id] or {}
	table.insert(protocol_cost_time_list[msg_id], time or 0)
	if protocol_test_time == 0 then
		protocol_test_time = Status.NowTime + protocol_step
	else
		if protocol_test_time <= Status.NowTime then
			print_error("~~~~~~~~~~~日志开始~~~~~~~~~~~~")
			for k,v in pairs(protocol_t) do
				print_error(string.format("协议id：%s， 协议次数：%s", k,v))
				if protocol_cost_time_list[k] ~= nil then
					for k1, v1 in pairs(protocol_cost_time_list[k]) do
						if v1 > 0 then
							print_error(string.format("协议id：%s， 协议次数：%s, 协议耗时：%.5f", k, k1, v1))
						end
					end
				end
			end
			print_error("~~~~~~~~~~~日志结束~~~~~~~~~~~~")
			protocol_t = {}
			protocol_cost_time_list = {}
			protocol_test_time = 0
		end
	end
end

-- 仙盟宝箱密码
function ClientCmdWGCtrl:GetBaoXiangCode()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	local password_list = info.password_list
	print_error("每日宝箱的密码:",password_list)
end

-- 打印活动状态
function ClientCmdWGCtrl:ShowActLog(params)
	local act_id = tonumber(params[1])
	print_error("--------------------Act Log:", ActivityWGData.Instance:GetActivityStatuByType(act_id), TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetCurOpenServerDay())
end

-- 执行LUA GC
function ClientCmdWGCtrl:DoLuaGC(params)
	collectgarbage("collect")
end

-- 清除ResPool
function ClientCmdWGCtrl:ClearPools()
	ResPoolMgr:Clear()
end

-- 检测方法消耗内存
function ClientCmdWGCtrl:CheckFuntionCostMem(params)
	local is_on = "on" == params[1]
	local flag = UnityEngine.PlayerPrefs.GetInt("START_CHECK_FUNTION_COST_MEM", 0)
	if 0 == flag and is_on then
		if TipWGCtrl and TipWGCtrl.Instance then
			TipWGCtrl.Instance:OpenConfirmAlertTips("需要重启游戏", function ()
				UnityEngine.PlayerPrefs.SetInt("START_CHECK_FUNTION_COST_MEM", 1)
				if IS_LOCLA_WINDOWS_DEBUG_EXE then
					GameRestart()
				else
					AgentAdapter.Instance:Logout()
				end
			end)
		end

		return
	end

	if IS_CHECK_FUNTION_COST_MEM then
		if is_on then
			if TipWGCtrl and TipWGCtrl.Instance then
				TipWGCtrl.Instance:OpenConfirmAlertTips("开始内存统计")
			end

		else
			if TipWGCtrl and TipWGCtrl.Instance then
				TipWGCtrl.Instance:OpenConfirmAlertTips("停止内存统计")
				UnityEngine.PlayerPrefs.SetInt("START_CHECK_FUNTION_COST_MEM", 0)
			end
		end
	end
end

-- 收集图形内存
function ClientCmdWGCtrl:CollectGraphicMem(params)
	local is_on = "on" == params[1] and true or false
	local flag = UnityEngine.PlayerPrefs.GetInt("COLLECT_GRAPHIC_MEM", 0)
	if is_on then
		if 0 == flag then
			UnityEngine.PlayerPrefs.SetInt("COLLECT_GRAPHIC_MEM", 1)
			if TipWGCtrl and TipWGCtrl.Instance then
				TipWGCtrl.Instance:OpenConfirmAlertTips("需要重启游戏", function ()
					if IS_LOCLA_WINDOWS_DEBUG_EXE then
						GameRestart()
					else
						AgentAdapter.Instance:Logout()
					end
				end)
			end
		end
	else
		UnityEngine.PlayerPrefs.SetInt("COLLECT_GRAPHIC_MEM", 0)
	end
end

-- 清除本地缓存登录服务器列表
function ClientCmdWGCtrl:CleanCacheServerList(param_t)
	print_error("【-----清除本地缓存登录服务器列表----】：")
	PlayerPrefsUtil.DeleteKey("PRVE_SRVER_ID")
end

-- 清除内存
function ClientCmdWGCtrl:ClearMem(param_t)
	local clear_type = tonumber(param_t[1] or 1)
	if clear_type == 1 then
		ChangeSkin.ClearBoneArray()
	elseif clear_type == 2 then
		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
		ResPoolMgr:Clear()
		BundleCache:Clear()
		UnityEngine.Resources.UnloadUnusedAssets()
	elseif clear_type == 3 then
		collectgarbage("collect")
	elseif clear_type == 4 then
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
	elseif clear_type == 5 then
		ResPoolMgr:Clear()
	elseif clear_type == 6 then
		BundleCache:Clear()
	elseif clear_type == 7 then
		UnityEngine.Resources.UnloadUnusedAssets()
	elseif clear_type == 8 then
		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
		UnityEngine.Resources.UnloadUnusedAssets()
	elseif clear_type == 9 then
		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
		ResPoolMgr:Clear()
		UnityEngine.Resources.UnloadUnusedAssets()
	elseif clear_type == 10 then
		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
		BundleCache:Clear()
		UnityEngine.Resources.UnloadUnusedAssets()
	elseif clear_type == 11 then
		BundleCache:GetRefList()
	elseif clear_type == 12 then
		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
	elseif clear_type == 13 then
		collectgarbage("collect")
		UnityEngine.Resources.UnloadUnusedAssets()	
	elseif clear_type == 14 then
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
		UnityEngine.Resources.UnloadUnusedAssets()
	elseif clear_type == 15 then
		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end

		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end		
	else
		collectgarbage("collect")
		if nil ~= System.GC then -- 兼容旧版本
			System.GC.Collect()
		end
		ChangeSkin.ClearBoneArray()
		ResPoolMgr:Clear()
		BundleCache:Clear()
		UnityEngine.Resources.UnloadUnusedAssets()	
	end
end

-- 获取天书碎片
function ClientCmdWGCtrl:AddGodBookPageChip(param_t)
	local slot = tonumber(param_t[1]) or 0
	local page = tonumber(param_t[2]) or 0
	local fle_data = FairyLandEquipmentWGData.Instance
	local max_part = fle_data:GetActPageNeedNum()
	for part = 0, max_part - 1 do
		local cfg = fle_data:GetGodBookChipCfgByData(slot, page, part)
		if cfg then
			SysMsgWGCtrl.SendGmCommand("additem", string.format("%s %s 0", cfg.item_id, 1))
		end
	end
end

-- 打印开启的界面有哪些
function ClientCmdWGCtrl:PrintOpenView()
	ViewManager.Instance:PrintOpenView()
end

-- 打印系统信息
function ClientCmdWGCtrl:PrintSysInfo()
	local sysInfo = UnityEngine.SystemInfo
	local screen = UnityEngine.Screen
	
	print_error("sysInfo ",
		"\ndeviceName=", sysInfo.deviceName,
		"\ndeviceModel=", sysInfo.deviceModel,
		"\ndeviceUniqueIdentifier=",sysInfo.deviceUniqueIdentifier,
		"\nsystemMemorySize=",sysInfo.systemMemorySize,
		"\ngraphicsMemorySize=",sysInfo.graphicsMemorySize,
		"\ngraphicsDeviceID=",sysInfo.graphicsDeviceID,
		"\ngraphicsDeviceName=",sysInfo.graphicsDeviceName,
		"\ngraphicsDeviceVendorID=",sysInfo.graphicsDeviceVendorID,
		"\ngraphicsDeviceType=",sysInfo.graphicsDeviceType,
		"\ngraphicsDeviceVersion=",sysInfo.graphicsDeviceVersion,
		"\ngraphicsShaderLevel=",sysInfo.graphicsShaderLevel,
		"\ngraphicsMultiThreaded=",sysInfo.graphicsMultiThreaded,
		"\nsupportsShadows=",sysInfo.supportsShadows,
		"\ngraphicsDeviceVendor=",sysInfo.graphicsDeviceVendor,
		"\nmaxCubemapSize=",sysInfo.maxCubemapSize,
		"\nscreenWidth=",screen.width,
		"\nscreenHeight=",screen.height)
end

-- 设置品质等级
function ClientCmdWGCtrl:quality(params)
	QualityConfig.QualityLevel = tonumber(params[1])
end

-- 昼夜自动开关
function ClientCmdWGCtrl:AutoDayNight(params)
	local feature = UnityEngine.Object.FindObjectOfType(typeof(DayNight.DayNightFeature))
	local frequency = tonumber(params[1])
	if frequency > 0 then
		feature:AutoPlay(frequency)
	else
		feature:StopAutoPlay()
	end
end

-- 图形
function ClientCmdWGCtrl:DebugGraphic(params)
	local mode = tonumber(params[1])
	if mode == 1 then
		UnityEngine.QualitySettings.masterTextureLimit = tonumber(params[2])
		print("贴图质量："..params[2])
	elseif mode == 2 then
		local renderers = UnityEngine.Object.FindObjectsOfType(typeof(UnityEngine.Renderer))
		for i=1,renderers.Length do
			if params[3] == "1" then
				renderers[i-1].material:EnableKeyword(params[2])
			else
				renderers[i-1].material:DisableKeyword(params[2])
			end
		end
		print("材质宏："..params[2]..", 状态："..params[3])
	elseif mode == 3 then
		table.remove(params, 1)

		local active = params[1] == "1"
		table.remove(params, 1)

		local name = table.concat(params, " ")

		local arr = {}
		table.insert(arr, UnityEngine.GameObject.Find("GameRoot"))
		table.insert(arr, UnityEngine.GameObject.Find("Main"))

		local recursionChildren = nil
		recursionChildren = function (node)
			  if node.name == name then
            
                node.gameObject:SetActive(active)
            end

            for i=1,node.childCount do
            	recursionChildren(node:GetChild(i - 1))
            end
            
		end
		
		for _, root in pairs(arr) do
			recursionChildren(root.transform)
		end
	
		print("设置物件："..name.. "  " .. (active and "Active" or "Inactive"))
	elseif mode == 4 then
		local renderers = UnityEngine.Object.FindObjectsOfType(typeof(UnityEngine.Renderer))
		for i=1,renderers.Length do
			renderers[i-1].forceRenderingOff = Vector3.Distance(renderers[i-1].bounds.center, UnityEngine.Camera.main.transform.position) > tonumber(params[2]);
		end
		print("简单距离剔除： 距离 "..params[2])
	elseif mode == 5 then
		local renderers = UnityEngine.Object.FindObjectsOfType(typeof(UnityEngine.Renderer))
		for i=1,renderers.Length do
			local renderer = renderers[i-1]
			local box =  renderer.gameObject:AddComponent(typeof(UnityEngine.BoxCollider))
            box.center = renderer.bounds.center
            box.size = renderer.bounds.size
		end

		for i=1,renderers.Length do
			local renderer = renderers[i-1]
			local dir = UnityEngine.Camera.main.transform.position - renderer.bounds.center

			local box =  renderer.gameObject:GetComponent(typeof(UnityEngine.BoxCollider))
            if box.bounds.size.magnitude < tonumber(params[2]) then
	            if UnityEngine.Physics.Raycast(UnityEngine.Camera.main.transform.position, dir.normalized, dir.magnitude) then	            
	                renderer.forceRenderingOff = true
	            end
	        end
		end

       	for i=1,renderers.Length do
			local renderer = renderers[i-1]
            local box = renderer.gameObject:GetComponent(typeof(UnityEngine.BoxCollider))
            UnityEngine.GameObject.Destroy(box);
        end

        print("简单遮挡剔除： 阈值 "..params[2])
    elseif mode == 6 then
    	local renderers = UnityEngine.Object.FindObjectsOfType(typeof(UnityEngine.Renderer))
    	for i=1,renderers.Length do
			local renderer = renderers[i-1]
			if renderer.sharedMaterial == nil or
				renderer.sharedMaterial.shader.name ~= "Srp/Standard/SrpRole_PbrCf" or
			renderer:GetComponentInParent(typeof(ActorRender)) == nil then
				renderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off
			end
    	end
    	print("关闭角色外实时阴影")
    elseif mode == 7 then
    	UnityEngine.QualitySettings.lodBias = tonumber(params[2])
    	print("LOD Bias：" .. params[2])
	elseif mode == 8 then
    	local scrrenAcreage = UnityEngine.Screen.height * UnityEngine.Screen.width;
    	local camera = UnityEngine.Camera.main
	    local renderers = UnityEngine.Object.FindObjectsOfType(typeof(UnityEngine.Renderer))
	    for i=1,renderers.Length do
			local renderer = renderers[i-1]
		    local delta = camera:WorldToScreenPoint(renderer.bounds.max) - camera:WorldToScreenPoint(renderer.bounds.min)
		    local projAcreage = Mathf.Abs(delta.x * delta.y)
		    local ratePerc = projAcreage / scrrenAcreage * 100
		    renderer.forceRenderingOff = ratePerc < tonumber(params[2])
		end

		print("投影面积剔除： 面积阈值" .. params[2])
	elseif mode == 9 then
		CharacterShadows.CharacterShadowManager.Instance.maxAvailableShadowCount = tonumber(params[2])
		print("实时阴影数量" .. params[2])
	end
end

-- 关闭自动打坐功能
function ClientCmdWGCtrl:CloseRoleSit()
	FunOpen.Instance:ForceCloseFunByName(FunName.RoleSit)
end

-- 屏蔽灵宠
function ClientCmdWGCtrl:PBPet()
	local shield_pet_rule = PetRule.New(ShieldObjType.Pet, ShieldRuleWeight.Max, function (obj)
		return true
	end)
	shield_pet_rule:Register()
end

-- 屏蔽守护
function ClientCmdWGCtrl:PBGuard()
	local shield_guard_rule = PetRule.New(ShieldObjType.Guard, ShieldRuleWeight.Max, function (obj)
		return true
	end)
	shield_guard_rule:Register()
end

-- 屏蔽灵宠 + 守护
function ClientCmdWGCtrl:PBPetGuard()
	self:PBPet()
	self:PBGuard()
end

function ClientCmdWGCtrl:SetLuaLoop(params)
    GameObject.Find("GameRoot/UILayer/ChatView"):SetActive(false)
	local arr = GameObject.Find("GameRoot"):GetComponents(typeof(UnityEngine.MonoBehaviour));
    arr[4].enabled = params[1] == 1;
end

-- 设置本地缓存
function ClientCmdWGCtrl:PlayerPrefsUtilSetInt(params)
	local key = tostring(params[1])
	local value = tonumber(params[2]) or 0
	PlayerPrefsUtil.SetInt(key, value)
end

-- 主角Buff监听
function ClientCmdWGCtrl:MainBuffMonitor(params)
	local is_on = "on" == params[1]
	if RoleWGCtrl and RoleWGCtrl.Instance then
		TipWGCtrl.Instance:ShowSystemNotice(is_on and "开启主角Buff监听" or "关闭主角Buff监听")
		RoleWGCtrl.Instance:SetMainBuffMonitorValue(is_on)
	end
end

-- GM面板
function ClientCmdWGCtrl:OpenGM()
	BulitInGMCtrl.Instance.gm_top_view:Open()
end

function ClientCmdWGCtrl:OpenTest1()
	DoTestLogic1()
end

function ClientCmdWGCtrl:OpenTest2()
	DoTestLogic2()
end

function ClientCmdWGCtrl:ShowRTRect(params)
	GLOBAL_SHOW_RT_RECT = params[1] == 1
end

function ClientCmdWGCtrl:ShowRTDrag(params)
	GLOBAL_SHOW_RT_DRAG = params[1] == 1
end

--截图.
function ClientCmdWGCtrl:GmScreenshot(params)
    UtilU3d.ScreenshotByPng(function (texture)
        ScreenShotWGCtrl.Instance:OpenSnapShotView(texture)
    end)
end

function ClientCmdWGCtrl:AutoRunTask()
	local auto_run_task_state = PlayerPrefsUtil.GetInt("auto_run_task_switch")
	PlayerPrefsUtil.SetInt("auto_run_task_switch", auto_run_task_state == 1 and 0 or 1)
	self:InsertLua({[1] = "GameDebugSwitch(\"autoRunTask\")"})
end

-- 塞入需要打印信息
function ClientCmdWGCtrl:AddPrintStr(str)
	if not str then return end
	if not self.need_print_str_list then
		self.need_print_str_list = {}
	end

	table.insert(self.need_print_str_list, str)
end

-- 输出打印
function ClientCmdWGCtrl:ShowPrintStr()
	if not self.need_print_str_list then return end
	local str = table.concat(self.need_print_str_list, "\n")
	print_error(str)
end

-- 输出打印
function ClientCmdWGCtrl:UIWaterWave()
	SceneLoading:ShowWaterEffect()
end

function ClientCmdWGCtrl:ResetTMP(params)
	local onload = function(asset)
		TMPro.TMP_Settings.instance.defaultSpriteAsset = asset
	end

	if params[1] == "1" then
		ResPoolMgr:GetTMPSpriteAsset("uis/emoji", "emoji_tex", onload, false)
	else
		onload(UnityEngine.Resources.Load("Sprite Assets/EmojiOne"))
	end
end

GLOBAL_CHECK_NO_MOVE_PRINT = false
-- 设置开关
function ClientCmdWGCtrl:SetGlobalSwitch(params)
	local key = params[1]
	if key == "check_no_move" then
		GLOBAL_CHECK_NO_MOVE_PRINT = not GLOBAL_CHECK_NO_MOVE_PRINT
	end
end