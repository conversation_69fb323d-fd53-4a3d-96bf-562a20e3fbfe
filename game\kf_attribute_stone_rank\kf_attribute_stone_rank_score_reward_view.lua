KFAttributeStoneRankScoreRewardView = KFAttributeStoneRankScoreRewardView or BaseClass(SafeBaseView)

function KFAttributeStoneRankScoreRewardView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/kf_attribute_stone_rank_ui_prefab", "layout_kf_attribute_stone_score_reward_view")
end

function KFAttributeStoneRankScoreRewardView:LoadCallBack()
    if not self.score_reward_list then
        self.score_reward_list = AsyncListView.New(KFAttributeStoneRankScoreRewardItem, self.node_list.score_reward_list)
        self.score_reward_list:SetStartZeroIndex(true)
    end
end

function KFAttributeStoneRankScoreRewardView:ReleaseCallBack()
    if self.score_reward_list then
        self.score_reward_list:DeleteMe()
        self.score_reward_list = nil
    end
end

function KFAttributeStoneRankScoreRewardView:OnFlush()
    local score_reward_cfg = KFAttributeStoneRankWGData.Instance:GetPersonScoreRewardCfg()
    if IsEmptyTable(score_reward_cfg) then
        return
    end

    self.score_reward_list:SetDataList(score_reward_cfg)
end


-------------------KFAttributeStoneRankScoreRewardItem
KFAttributeStoneRankScoreRewardItem = KFAttributeStoneRankScoreRewardItem or BaseClass(BaseRender)

function KFAttributeStoneRankScoreRewardItem:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.item_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.get_reward_btn, BindTool.Bind(self.OnClickGetRewardBtn, self))
end

function KFAttributeStoneRankScoreRewardItem:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function KFAttributeStoneRankScoreRewardItem:OnFlush()
    if not self.data then
        return
    end

    local self_score = KFAttributeStoneRankWGData.Instance:GetSelfScore()
    local can_get = self_score >= self.data.score

    local color = can_get and COLOR3B.DEFAULT_NUM or COLOR3B.PINK
    local progress_str = ToColorStr(string.format(Language.KFAttributeStoneRank.ScoreRewardProgress,self_score, self.data.score), color)
    self.node_list.des.text.text = string.format(Language.KFAttributeStoneRank.ScoreRewardDes, self.data.score, progress_str)
    self.reward_list:SetDataList(self.data.reward_item)

    local is_fetch = KFAttributeStoneRankWGData.Instance:GetScoreRewardFlag(self.data.seq) > 0

    self.node_list.btn_txt.text.text = is_fetch and Language.Common.YiLingQu or Language.Common.LingQu
    self.node_list.remind:SetActive(can_get and not is_fetch)
    XUI.SetButtonEnabled(self.node_list.get_reward_btn, not is_fetch)
end

function KFAttributeStoneRankScoreRewardItem:OnClickGetRewardBtn()
    if not self.data then
        return
    end

    local self_score = KFAttributeStoneRankWGData.Instance:GetSelfScore()
    local can_get = self_score >= self.data.score
    if not can_get then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.KFAttributeStoneRank.ScoreNotEnough)
        return
    end

    KFAttributeStoneRankWGCtrl.Instance:SendKFRankReq(KF_ATTR_STONE_RANK.KF_ATTR_STONE_RANK_PERSON_SCORE_REWARD, self.data.seq)
end