ZhanDuiHandleInviteView = ZhanDuiHandleInviteView or BaseClass(SafeBaseView)

function ZhanDuiHandleInviteView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_join")
end

function ZhanDuiHandleInviteView:ReleaseCallBack()
    if self.handle_invite_list then
        self.handle_invite_list:DeleteMe()
        self.handle_invite_list = nil
    end
end

function ZhanDuiHandleInviteView:LoadCallBack()
    --XUI.AddClickEventListener(self.node_list.btn_create, BindTool.Bind(self.OnClickCreate, self))
    self.handle_invite_list = AsyncListView.New(ZhanDuiHandleInviteRender, self.node_list.handle_invite_list)
end

function ZhanDuiHandleInviteView:OnFlush()
    local data_list = {} --TODO GetDataList
    self.handle_invite_list:SetDataList(data_list)
end

function ZhanDuiHandleInviteView:OnClickCreate()
end

ZhanDuiHandleInviteRender = ZhanDuiHandleInviteRender or BaseClass(BaseRender)
function ZhanDuiHandleInviteRender:__init()
end

function ZhanDuiHandleInviteRender:OnFlush()
end
