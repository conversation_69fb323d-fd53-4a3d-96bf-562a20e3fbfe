HunJieSkillTipView = HunJieSkillTipView or BaseClass(SafeBaseView)

function HunJieSkillTipView:__init()
    self.view_layer = UiLayer.Pop
    self.view_name = "HunJieSkillTipView"
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_hj_skill_tip_view")

	self.view_layer = UiLayer.Pop		-- 弹出框
end

function HunJieSkillTipView:__delete()

end

function HunJieSkillTipView:ReleaseCallBack()
	self.data = nil
end

function HunJieSkillTipView:LoadCallBack()

end

function HunJieSkillTipView:SetSkillData(data)
	self.data = data
end

function HunJieSkillTipView:OnFlush()
	if not self.data then
		return
	end

	local color = 0

	if self.data.skill_color then
		color = self.data.skill_color
	end

	self.node_list["skill_name"].text.text = ToColorStr(self.data.skill_name, ITEM_COLOR[color])
	self.node_list["skill_dsc"].text.text = self.data.skill_desc
	self.node_list["limit_text"].text.text = self.data.limit_text
	--self.node_list["skill_type_desc"].text.text = self.data.skill_type
	XUI.SetSkillIcon(self.node_list.ph_ml_skill_bg, self.node_list.ph_ml_skill_item, self.data.skill_image)

	if self.data.is_jh then
		self.node_list["skill_level_text"].text.text = self.data.level
		self.node_list["level_root"]:SetActive(true)
		self.node_list["no_active_text"]:SetActive(false)

    	if self.data.next_info then
			self.node_list["next_desc"]:SetActive(true)
			self.node_list["skill_dsc_nextLevel"].text.text = self.data.next_info.skill_desc
		else
			self.node_list["next_desc"]:SetActive(false)
		end
	else
		self.node_list["next_desc"]:SetActive(false)
		self.node_list["level_root"]:SetActive(false)
		self.node_list["no_active_text"]:SetActive(true)
		self.node_list["no_active_text"].text.text = Language.Marry.SkillNoActive
	end

	if self.data.skill_color then
		color = self.data.skill_color - 1
	end

	self:ChangePanelHeight()
    self:ShowCapability()
end

function HunJieSkillTipView:ChangePanelHeight()
	local center_content = self.node_list["center_content"]
	local all_content = self.node_list["all_content"]
	local scroll_view = self.node_list["scroll_view"]
	local scroll_content = self.node_list["layout_skill_tip"]

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(scroll_content.rect)

	local max_center_height = 496
	local max_all_height = 635
	local scroll_content_height = scroll_content.rect.sizeDelta.y

	if scroll_content_height > max_center_height then
		all_content.layout_element.enabled = true
		all_content.layout_element.preferredHeight = max_all_height
		scroll_view.layout_element.preferredHeight = max_center_height
		center_content.layout_element.preferredHeight = max_center_height
		scroll_view.scroll_rect.vertical = true
	else
		all_content.layout_element.enabled = false
		center_content.layout_element.preferredHeight = scroll_content_height
		scroll_view.layout_element.preferredHeight = scroll_content_height
		scroll_view.scroll_rect.vertical = false
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(all_content.rect)
	scroll_view.scroll_rect.verticalNormalizedPosition = 1
end

function HunJieSkillTipView:ShowCapability()
	local cap = 0
	if self.data then
		if self.data.capability then
			cap = self.data.capability
		end
	end

	self.node_list.cap_value.text.text = cap
	self.node_list.capability_part:SetActive(cap > 0)
end
