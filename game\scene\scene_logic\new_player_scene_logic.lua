NewPlayerFbSceneLogic = NewPlayerFbSceneLogic or BaseClass(CommonFbLogic)
local CG_SCENE = {
	MHXW = 351, 	-- 魔化玄武
	LYQQ = 357, 	-- 烈焰穷奇
	SSYZ = 371,  	-- 殊死一战
}
local new_player_battle_scene_id_1 = 152 --新手战斗场景id
local new_player_battle_scene_id_2 = 155 --新手战斗场景id
local new_player_bianshen_scene_id = 253 --新手副本默认变身场景id

function NewPlayerFbSceneLogic:__init()
	self.end_effect_node = nil
	self.audio_done = false
	self.guai_ji_next_time = 0
	self.move_to_gather = false
	self.zhu_task_id = 0
	self.monster_index = 0
	self.main_role_hit_callback = nil
	self.is_battle_1_cg_played = false
	self.story_cfg = {}
	self.new_player_battle_1_monster_id = 0
	self.fb_monster_cg_cfg = nil
	self.trigger_area = nil
	self.trigger_area_show = true
	self.trigger_param_t = nil
	self.operate_param_t = nil
	self.camera_offset_t = nil
end

function NewPlayerFbSceneLogic:__delete()
	self.fb_cfg = nil
	self.fb_monster_cg_cfg = nil
	self.move_to_gather = nil

	if self.main_role_hit_callback then
		GlobalEventSystem:UnBind(ObjectEventType.MAIN_ROLE_BE_HIT)
		self.main_role_hit_callback = nil
	end

    self.is_battle_1_cg_played = false
    if nil ~= self.story then
		self.story:DeleteMe()
		self.story = nil
    end
	self.story_cfg = {}
	self.trigger_area = nil
	self.trigger_area_show = true
	self.trigger_param_t = nil
	self.operate_param_t = nil
	self.camera_offset_t = nil
end

function NewPlayerFbSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    local scene_id = Scene.Instance:GetSceneId()
    self.story = XinShouStorys.New(scene_id)
	
	if self.story:GetStoryNum() > 0 then
		RobertManager.Instance:Start()
	end

	self.fb_monster_cg_cfg = FuBenWGData.Instance:GetNewPlayerFbBossShowCfg(scene_id)

	if self.fb_monster_cg_cfg then
		self.trigger_param_t = Split(self.fb_monster_cg_cfg.trigger_param, "##")
		self.operate_param_t = Split(self.fb_monster_cg_cfg.operate_param, "##")
		self.camera_offset_t = Split(self.fb_monster_cg_cfg.camera_offset, "##")
	end

	self.zhu_task_id = TaskWGData.Instance:GetOneActiveTask(GameEnum.TASK_TYPE_ZHU)
	self.move_to_gather = false
	self.start_gather_event = BindTool.Bind1(self.StartGather, self)
	Scene.SendGetAllObjMoveInfoReq()

	-- 检查是否需要等CG播完再刷怪
	local need_wait_cg_end = self:CheckNeedWaitCgEndReqMonsterBorn()
	if not need_wait_cg_end then
		FuBenWGCtrl.Instance:CSFBPlayedCG()
	end

	local cg_end_callback = function()
		if need_wait_cg_end then
			FuBenWGCtrl.Instance:CSFBPlayedCG()
		end

		if self.monster_born_timer ~= nil then
			GlobalTimerQuest:CancelQuest(self.monster_born_timer)
			self.monster_born_timer = nil
		end
	end

	self.cg_event = GlobalEventSystem:Bind(ObjectEventType.CG_EVENT_END, cg_end_callback)
	-- 避免CG播放结束事件没回来
	self.monster_born_timer = GlobalTimerQuest:AddDelayTimer(function ()
		cg_end_callback()
	end, 15)

	if scene_id == new_player_bianshen_scene_id then
		if self.tianshen_bianshen_timer ~= nil then
			GlobalTimerQuest:CancelQuest(self.tianshen_bianshen_timer)
			self.tianshen_bianshen_timer = nil
		end
	
		self.tianshen_bianshen_timer = GlobalTimerQuest:AddDelayTimer(function ()
			--直接让玩家变身
			TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type15, 0)
		end, 4)
	end

	--副本task
	MainuiWGCtrl.Instance:SetTeamBtnState(false)
    MainuiWGCtrl.Instance:SetOtherContents(true)
    MainuiWGCtrl.Instance:SetTaskContents(false)
	FuBenWGCtrl.Instance:OpenNewPlayerView()

	--副本task
	local fuben_data
	local callback = function ()
		fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
		self.fb_cfg = FuBenWGData.Instance:GetNewPlayerFbCfg(fuben_data.fb_type)
		if self.fb_cfg == nil then
			self.audio_done = true
		else
			-- MainuiWGCtrl.Instance:SetFBNameState(true, self.fb_cfg.name)
			MainuiWGCtrl.Instance:SetFBNameState(false)
		end
		self.trigger_area_show = false

		local is_can_auto = false
		if self.fb_cfg and self.fb_cfg.temp_xiuwei_index == -1 then
			is_can_auto = true
		end

		if is_can_auto then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end

		if self.fb_cfg and self.fb_cfg.temp_xiuwei_index ~= -1 then		--打开选择堕魔还是飞仙
			FuBenWGCtrl.Instance:OpenNewPlayerXiuWeiView()
		end
	end

	GlobalTimerQuest:AddDelayTimer(function ()
		fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
		if not fuben_data or IsEmptyTable(fuben_data) then
			FuBenWGData.Instance:SetNewPlayerFBInfoCallBack(callback)
			return
		else
			callback()
		end
	end, 1)

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.XianZhuoZi, nil, true))
end

function NewPlayerFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)

	if self.fb_monster_cg_cfg ~= nil and (not self.trigger_area_show) then
		local monster_obj = Scene.Instance:GetMonstObjByMonstID(self.fb_monster_cg_cfg.boss_id)

		if nil ~= monster_obj then
			local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
			if nil == self.trigger_area and self.trigger_param_t then
				self.trigger_area = {}
				self.trigger_area.x = tonumber(self.trigger_param_t[1])
				self.trigger_area.y = tonumber(self.trigger_param_t[2])
				self.trigger_area.w = tonumber(self.trigger_param_t[3])
				self.trigger_area.h = tonumber(self.trigger_param_t[4])
			end

			if GameMath.IsInRect(role_x, role_y, self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h) then
				self.trigger_area = nil
				self.trigger_area_show = true
				BossCamera.Instance:BossFollowCaneraShow(monster_obj, self.fb_monster_cg_cfg.cg_time, self.operate_param_t, self.camera_offset_t,
				self.fb_monster_cg_cfg.boss_name_bundle, self.fb_monster_cg_cfg.boss_name_assets, self.fb_monster_cg_cfg.interlude_name_img)

				-- -- 音频
				local audio_id = self.fb_monster_cg_cfg.talk_audio
				if audio_id and audio_id ~= "" then
					local bundle, asset = ResPath.GetNpcTalkVoiceResByResName(audio_id)
					TalkCache.PlayGuideTalkAudio(bundle, asset)
				end
			end
		end
	end
	
	self.Audio_open_time = self.Audio_open_time or now_time + 3
	if self.Audio_open_time <= now_time and not self.audio_done then
		self.Audio_open_time = now_time + 3
		if self.fb_cfg == nil then
			return
		end

		local monster_obj = Scene.Instance:GetMonstObjByMonstID(self.fb_cfg.boss_id)
		if nil ~= monster_obj then
			local audio_res = ResPath.GetOtherAudioEffectResPath(self.fb_cfg.boss_audio)
			AudioManager.Instance:PlayEffect(audio_res, AudioInterval.Common)
			self.audio_done = true
		end
	end

	if not self.move_to_gather then
		self:GuaiJiMonsterUpdate(now_time, elapse_time)
	end
end

local prof_cg_name = {
	[1] = "jian",
	[2] = "dizang",
	[3] = "qiang",
}
function NewPlayerFbSceneLogic:OnMainRoleHit(deliverer)
	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local hp = tonumber(main_role:GetAttr("hp"))
	local max_hp = tonumber(main_role:GetAttr("max_hp"))
	if self.trigger_param and (hp / max_hp) <= self.trigger_param then
		if self.story_cfg and not CgManager.Instance:IsCgIng() and not self.is_battle_1_cg_played then
			local monster = Scene.Instance:GetMonstObjByMonstID(self.new_player_battle_1_monster_id)
			if monster then
				monster:CancelSelect()
			end
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)

			local param = Split(self.story_cfg.operate_param, "##")
			local bundle, asset = param[1], param[2]
			local sex = GameVoManager.Instance:GetMainRoleVo().sex
			local prof = GameVoManager.Instance:GetMainRoleVo().prof
			asset = sex == GameEnum.MALE and (asset .. "_nan") or (asset .. "_nv")
			asset = asset .. (prof_cg_name[prof] or "")
			CgManager.Instance:Play(BaseCg.New(bundle, asset), function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				self.is_battle_1_cg_played = true
				--直接让玩家变身
				TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type15, 0)

				local pet_list = main_role:GetPetObjList()
				if pet_list then
					for k, v in pairs(pet_list) do
						v:ForceSetVisible(true)
					end
				end

				local beast_obj = main_role:GetBeastObjList()
				if beast_obj then
					beast_obj:ForceSetVisible(true)
				end
			end, function()
				CgManager.Instance:SetOperaBtnCallback(0, function(btn_index)
					local time_point = prof == GameEnum.ROLE_PROF_1 and 7.666667 or 5.33333333
					CgManager.Instance:SetCurTimePoint(time_point)
				end)

				--屏蔽所有宠物
				local pet_list = main_role:GetPetObjList()
				if pet_list then
					for k, v in pairs(pet_list) do
						v:ForceSetVisible(false)
					end
				end

				local beast_obj = main_role:GetBeastObjList()
				if beast_obj then
					beast_obj:ForceSetVisible(true)
				end
			end, false)
		end
	end
end

function NewPlayerFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	if self.end_effect_node ~= nil then
		self.end_effect_node:removeFromParent()
		self.end_effect_node = nil
	end

	if self.cg_event then
		GlobalEventSystem:UnBind(self.cg_event)
		self.cg_event = nil
	end

	if self.monster_born_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.monster_born_timer)
		self.monster_born_timer = nil
	end

	if self.tianshen_bianshen_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.tianshen_bianshen_timer)
		self.tianshen_bianshen_timer = nil
	end

	if self.main_role_hit_callback then
		GlobalEventSystem:UnBind(self.main_role_hit_callback)
		self.main_role_hit_callback = nil
	end
    FuBenWGCtrl.Instance:CloseNewPlayerView()
	--副本task
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:ResetTaskPanel()

	FuBenWGData.Instance:ClearNewPlayerFBInfo()

	self.start_gather_event = nil
	self.audio_done = false
	self.move_to_gather = false

	if self.zhu_task_id ~= TaskWGData.Instance:GetOneActiveTask(GameEnum.TASK_TYPE_ZHU) then
		GuajiWGCtrl.Instance:StopGuaji()
		TaskGuide.Instance:CanAutoAllTask(true)
	else
		GuajiWGCtrl.Instance:StopGuaji(false)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	end

	self.zhu_task_id = 0
end

function NewPlayerFbSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	-- if BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time) then
	-- 	return
	-- end

	-- local list = Scene.Instance:GetMonsterList()
	-- if not IsEmptyTable(list) then return end

	-- local pos_x, pos_y = GuajiWGCtrl.Instance:GetPickPos()
	-- print_error(pos_x, pos_y)
	-- if not (pos_x == 0 and pos_y == 0) then return end

	-- local gather_list = Scene.Instance:GetGatherList()
	-- local _, gather_obj = next(gather_list)
	-- if nil ~= gather_obj and MainuiWGData.Instance:GetTargetObj() == nil then
	-- 	-- self:MoveToObj(gather_obj)
	-- 	-- MainuiWGCtrl.Instance:SetTargetObj(gather_obj)
	-- 	GuajiWGCtrl.Instance:StopGuaji()
	-- 	local scene_id = Scene.Instance:GetSceneId()
	-- 	local gjc = GuajiWGCtrl.Instance
	-- 	local x, y = gather_obj:GetLogicPos()
	-- 	gjc:SetMoveToPosCallBack(self.start_gather_event)
	-- 	gjc:MoveToPos(scene_id, x, y)
	-- 	self.move_to_gather = true
	-- end
end

function NewPlayerFbSceneLogic:StartGather()
	-- local gather_list = Scene.Instance:GetGatherList()
	-- local _, gather_obj = next(gather_list)
	-- if gather_obj then
		-- GuajiWGCtrl.Instance:OnSelectObj(gather_obj, SceneTargetSelectType.SCENE)
		-- AtkCache.is_valid = false
	-- end
end

function NewPlayerFbSceneLogic:GetMonsterJumpLogicPos(monster_obj)
	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then
		return nil, nil
	end
	local seq = monster_obj.vo.special_param % 10000
	local wave = (monster_obj.vo.special_param - seq) / 10000
	local cfg = FuBenWGData.Instance:GetNewPlayerFbMonsterCfg(fuben_data.fb_type, wave, seq)
	if cfg and cfg.jumppos_y ~= -1 and cfg.jumppos_x ~= -1 then
		self.monster_index = self.monster_index + 1
		return cfg.jumppos_x, cfg.jumppos_y
	end
	return nil, nil
end

function NewPlayerFbSceneLogic:CanMonsterDoJump(special_param)
	if not special_param then
		return false
	end

	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then
		return false
	end
	local seq = special_param % 10000
	local wave = (special_param - seq) / 10000
	local cfg = FuBenWGData.Instance:GetNewPlayerFbMonsterCfg(fuben_data.fb_type, wave, seq)
	if cfg and cfg.jumppos_y ~= -1 and cfg.jumppos_x ~= -1 then
		return true
	end
	return false
end

-- 检查是否需要等CG播完再刷怪
function NewPlayerFbSceneLogic:CheckNeedWaitCgEndReqMonsterBorn()
	local scene_id = Scene.Instance:GetSceneId()
	local cfg = FunOpen.Instance:GetNorSceneStoryCfg()
	if cfg ~= nil then
		for _, story_cfg in ipairs(cfg) do
			if story_cfg.trigger == S_STEP_TRIGGER.ENTER_SCENE then
				if story_cfg.operate == S_STEP_OPERATE.CG_START then
					return true
				elseif story_cfg.operate == S_STEP_OPERATE.DELAY_DO then
					local operate_list = Split(story_cfg.operate_param, "##")
					if operate_list[2] == S_STEP_OPERATE.CG_START then
						return true
					end
				end
			end
		end
	end
	return false
end


