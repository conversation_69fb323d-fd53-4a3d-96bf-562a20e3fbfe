local MFP_MODLE_ANI_CFG = {
    [MECHA_PART_TYPE.BODY]          = {position = Vector3(-108, 0, 0),      rotation = Vector3(0, 0 , 0),       scale = Vector3(1, 1, 1)},
    [MECHA_PART_TYPE.LEFT_HAND]     = {position = Vector3(-96, -187, 49),   rotation = Vector3(0, 30, 0),       scale = Vector3(1.5, 1.5, 1.5)},
    [MECHA_PART_TYPE.RIGHT_HAND]    = {position = Vector3(-96, -187, 49),   rotation = Vector3(0, -110 , 0),    scale = Vector3(1.5,1.5,1.5)},
    [MECHA_PART_TYPE.LEFT_FOOT]     = {position = Vector3(-160, 122, 142),  rotation = Vector3(0, 30, 0),       scale = Vector3(1.2, 1.2, 1.2)},
    [MECHA_PART_TYPE.RIGHT_FOOT]    = {position = Vector3(-64, 122, 142),   rotation = Vector3(0, -80, 0),      scale = Vector3(1.2, 1.2, 1.2)},
}

function MechaView:LoadFighterPlaneCallBack()
    if not self.mfp_mecha_list then
        self.mfp_mecha_list = AsyncListView.New(MFPMechaListRender, self.node_list.mfp_mecha_list)
        self.mfp_mecha_list:SetDefaultSelectIndex(nil)
        self.mfp_mecha_list:SetStartZeroIndex(true)
        self.mfp_mecha_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMFBMechaHandler, self))
    end

    if not self.mfp_mecha_part_list then
        self.mfp_mecha_part_list = AsyncListView.New(MFPMechaPartListRender, self.node_list.mfp_mecha_part_list)
        self.mfp_mecha_part_list:SetDefaultSelectIndex(nil)
        self.mfp_mecha_part_list:SetStartZeroIndex(false)
        self.mfp_mecha_part_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMFBMechaPartHandler, self))
    end

    if not self.mfp_mecha_part_cell_list then
        self.mfp_mecha_part_cell_list = AsyncListView.New(MFPMechaPartCellListRender, self.node_list.mfp_mecha_part_cell_list)
        self.mfp_mecha_part_cell_list:SetDefaultSelectIndex(nil)
        self.mfp_mecha_part_cell_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMFBMechaPartCellHandler, self))
    end

    if not self.mfp_attr_info_list then
        self.mfp_attr_info_list = AsyncListView.New(MFPAttrInfoListRender, self.node_list.mfp_attr_info_list)
    end

    if not self.mfp_active_item then
        self.mfp_active_item = ItemCell.New(self.node_list.mfp_active_item)
    end

    if not self.mfp_skill_info_list then
        self.mfp_skill_info_list = AsyncListView.New(MFPSkillInfoItemRender, self.node_list.mfp_skill_info_list)
    end

    if not self.mfp_gundam_model then
        self.mfp_gundam_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["mfp_modle_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.mfp_gundam_model:SetRenderTexUI3DModel(display_data)
        -- self.mfp_gundam_model:SetUI3DModel(self.node_list["mfp_modle_root"].transform, self.node_list["mfp_modle_root"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    if not self.mfp_up_level_item then
        self.mfp_up_level_item = ItemCell.New(self.node_list.mfp_up_level_item)
    end

    self.node_list.mfp_attr_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMFBTog,self, 1))
    self.node_list.mfp_uplevel_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMFBTog,self, 2))
    XUI.AddClickEventListener(self.node_list.btn_mfp_active, BindTool.Bind(self.OnClickMFPActive, self))
    XUI.AddClickEventListener(self.node_list.btn_mfp_up_level, BindTool.Bind(self.OnClickMFPUpLevel, self))
    XUI.AddClickEventListener(self.node_list.btn_mfb_jnyl, BindTool.Bind(self.OnClickMFPJnyl, self))
    XUI.AddClickEventListener(self.node_list.btn_mfp_zbk, BindTool.Bind(self.OnClickMFPUpZbk, self))
    XUI.AddClickEventListener(self.node_list.mfp_tips, BindTool.Bind(self.OnClickMFPTip, self))

    self.mfb_mecha_seq = -1 
    self.mfb_mecha_data = {}
    self.mfb_mecha_part = -1
    self.mfb_mecha_part_index = -1
    self.mfb_mecha_part_data = {}
    self.mfb_mecha_part_cell_sort = -1 
    self.mfb_mecha_part_cell_data = {}
    self.mfb_right_tog_select_id = 1

    self.mfp_show_action = false

    self.mfp_can_show_sort_tip = true
end

function MechaView:ShowFighterPlaneCallBack()
    self:RightPanleShowTween(self.node_list.mfp_right_tween_root, self.node_list.mfp_right_tween_root)

    if self.mfp_gundam_model then
        self.mfp_gundam_model:PlayRoleShowAction()
    end
end

function MechaView:ChangeFighterPlaneCallBack()
end

function MechaView:ReleaseFighterPlaneCallBack()
    if self.mfp_mecha_list then
        self.mfp_mecha_list:DeleteMe()
        self.mfp_mecha_list = nil
    end

    if self.mfp_mecha_part_list then
        self.mfp_mecha_part_list:DeleteMe()
        self.mfp_mecha_part_list = nil
    end

    if self.mfp_mecha_part_cell_list then
        self.mfp_mecha_part_cell_list:DeleteMe()
        self.mfp_mecha_part_cell_list = nil
    end

    if self.mfp_attr_info_list then
        self.mfp_attr_info_list:DeleteMe()
        self.mfp_attr_info_list = nil
    end

    if self.mfp_active_item then
        self.mfp_active_item:DeleteMe()
        self.mfp_active_item = nil
    end

    if self.mfp_skill_info_list then
        self.mfp_skill_info_list:DeleteMe()
        self.mfp_skill_info_list = nil
    end

    if self.mfp_gundam_model then
        self.mfp_gundam_model:DeleteMe()
        self.mfp_gundam_model = nil
    end

    if self.mfp_up_level_item then
        self.mfp_up_level_item:DeleteMe()
        self.mfp_up_level_item = nil
    end

    self.mfp_model_cache = nil
    self.mfp_show_action = false
end

function MechaView:OnFlushFighterPlaneCallBack()
    local mecha_show_data = MechaWGData.Instance:GetMechaShowDataList()
    self.mfp_mecha_list:SetDataList(mecha_show_data)
    self.mfp_mecha_list:JumpToIndex(self:GetMFBSelectMecha(mecha_show_data))
end

function MechaView:FlushFighterPlaneMidModel()
    local part_list = {}
    local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(self.mfb_mecha_seq)
    for k, v in pairs(base_part) do
        local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
        part_list[part_cfg.part] = part_cfg.res_id
    end

    local putton_list = MechaWGData.Instance:GetMechaPartWearPartList(self.mfb_mecha_seq)
    if not IsEmptyTable(putton_list) then
        for k, v in pairs(putton_list) do
            if v >= 0 then
                local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
                part_list[part_cfg.part] = part_cfg.res_id
            end
        end
    end

    part_list[self.mfb_mecha_part_cell_data.part] = self.mfb_mecha_part_cell_data.res_id

    local part_info = {
		gundam_seq = self.mfb_mecha_seq,
		gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
        gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
		gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
        gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
		gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
        gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
		gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
        gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
	}

    if self:IsMFPModelChange(part_info) then
        self.mfp_model_cache = part_info
        self.mfp_gundam_model:SetGundamModel(part_info)

        if not self.mfp_show_action and self.mfb_mecha_part == MECHA_PART_TYPE.BODY then
            self.mfp_gundam_model:PlayRoleShowAction()
        end
        
        self.mfp_show_action = true
    end
end

function MechaView:IsMFPModelChange(part_info)
    if IsEmptyTable(self.mfp_model_cache) then
        return true
    end

    for k, v in pairs(part_info) do
        if self.mfp_model_cache[k] ~= v then
            return true
        end
    end
    
    return false
end

function MechaView:FlushFighterPlaneRight()
    local tog_select_id = self:GetMFBRightTogSelect()

    if self.mfb_right_tog_select_id == tog_select_id then
        if tog_select_id == 1 then
            self:FlushFighterPlaneAttrInfoPanel()
        else
            self:FlushFighterPlaneUplevelPanel()
        end
    else
        if tog_select_id == 1 then
            self.node_list.mfp_attr_tog.toggle.isOn = true
        else
            self.node_list.mfp_uplevel_tog.toggle.isOn = true
        end
    end

    self.mfb_right_tog_select_id = tog_select_id
end

function MechaView:FlushFighterPlaneAttrInfoPanel()
    local mecha_is_active = MechaWGData.Instance:IsMechaActive(self.mfb_mecha_seq)
    self.node_list.mfp_active_info:CustomSetActive(not mecha_is_active)

    if mecha_is_active then
        local is_wear_weapon, weapon_part_seq = MechaWGData.Instance:IsMechaActiveWeapon(self.mfb_mecha_seq)
        self.node_list.mfp_noweapon_info:CustomSetActive(not is_wear_weapon) 
        self.node_list.mfp_skill_info:CustomSetActive(is_wear_weapon)
        local all_attr_info = MechaWGData.Instance:GetMechaActiveShowAllAttrDataList(self.mfb_mecha_seq)
        self.mfp_attr_info_list:SetDataList(all_attr_info)
        self.node_list.img_mfp_attr_top_title.text.text = Language.Mecha.MFPMechaAttrTitle

        if is_wear_weapon then
            local skill_list = MechaWGData.Instance:GetWeaponToSkillInfo(weapon_part_seq)
            if not IsEmptyTable(skill_list) then
                local skil_data_list = {}
                for k, v in pairs(skill_list) do
                    table.insert(skil_data_list, {skill_id = tonumber(v)})
                end

                self.mfp_skill_info_list:SetDataList(skil_data_list)
            end
        end
    else
        self.node_list.mfp_skill_info:CustomSetActive(false)
        self.node_list.mfp_noweapon_info:CustomSetActive(false)

        self:FlushMFBAcvtiveInfo()
    end
end

function MechaView:FlushFighterPlaneUplevelPanel()
    local mecha_is_active = MechaWGData.Instance:IsMechaActive(self.mfb_mecha_seq)
    self.node_list.mfp_active_info:CustomSetActive(not mecha_is_active)

    if mecha_is_active then
        self.node_list.mfp_uplevel_panel_title:CustomSetActive(true)
        local part_seq = self.mfb_mecha_part_cell_data.seq
        local is_max_level, attr_data_list = MechaWGData.Instance:GetPartUpLevelAttrDataList(part_seq)
        self.mfp_attr_info_list:SetDataList(attr_data_list)

        self.node_list.mfp_up_level_max:CustomSetActive(is_max_level)
        self.node_list.msp_up_level_info:CustomSetActive(not is_max_level)

        self.node_list.img_mfp_attr_top_title.text.text = string.format(Language.Mecha.MechaAttrNameTitle, Language.Mecha.MechaPartName[self.mfb_mecha_part])

        if is_max_level then
        else
            local cur_star = MechaWGData.Instance:GetPartStarBySeq(part_seq)
            self.node_list.desc_btn_mfp_up_level.text.text = cur_star >= 0 and Language.Mecha.OperaTypeName[2] or Language.Mecha.OperaTypeName[1]
            cur_star = cur_star >= 0 and cur_star or 0
            local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(part_seq, cur_star)
            if not IsEmptyTable(up_star_cfg) then
                local cust_item_id = up_star_cfg.cost_item_id
                local cost_item_num = up_star_cfg.cost_item_num
                local has_num = ItemWGData.Instance:GetItemNumInBagById(cust_item_id)
                local color = has_num >= cost_item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
                local str = has_num .. "/" .. cost_item_num
                self.mfp_up_level_item:SetFlushCallBack(function ()
                    self.mfp_up_level_item:SetRightBottomColorText(str, color)
                    self.mfp_up_level_item:SetRightBottomTextVisible(true)
                end)
                self.mfp_up_level_item:SetData({item_id = cust_item_id})
                
                self.node_list.btn_mfp_up_level_remind:CustomSetActive(has_num >= cost_item_num)
            end

            for i = 1, 5 do
                local asset_index = 2
                if cur_star > 0 then
                    if cur_star > 5 then
                        asset_index = (cur_star - 5) >= i and 3 or 1
                    else
                        asset_index = cur_star >= i and 1 or 2
                    end
                end

                local bundle, asset = ResPath.GetMechaImg("a2_jj_di_xing" .. asset_index)
                self.node_list["mfp_star" .. i].image:LoadSprite(bundle, asset, function ()
                    self.node_list["mfp_star" .. i].image:SetNativeSize()
                end)
            end
        end
    else
        self.node_list.mfp_uplevel_panel_title:CustomSetActive(false)
        self.node_list.mfp_up_level_max:CustomSetActive(false)
        self.node_list.msp_up_level_info:CustomSetActive(false)

        self:FlushMFBAcvtiveInfo()
    end
end

-- 激活面板
function MechaView:FlushMFBAcvtiveInfo()
    local attr_info = MechaWGData.Instance:GetMechaActiveShowAttrDataList(self.mfb_mecha_seq, "attr_name", "add_value")
    self.mfp_attr_info_list:SetDataList(attr_info)
    self.node_list.img_mfp_attr_top_title.text.text = Language.Mecha.MFPMechaAttrTitle

    local cost_item_id = self.mfb_mecha_data.active_item
    local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    local color = has_num > 0 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    local str = has_num .. "/" .. 1
    self.mfp_active_item:SetFlushCallBack(function ()
        self.mfp_active_item:SetRightBottomColorText(str, color)
        self.mfp_active_item:SetRightBottomTextVisible(true)
    end)

    self.mfp_active_item:SetData({item_id = cost_item_id})
    self.node_list.btn_mfp_active_remind:CustomSetActive(has_num > 0)

    local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
    if not IsEmptyTable(item_cfg) then
        self.node_list.mfp_desc_active_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    else
        self.node_list.mfp_desc_active_name.text.text = ""
    end
end

function MechaView:GetMFBRightTogSelect()
    local remind = MechaWGData.Instance:GetMFPMechaPartCellRemind(self.mfb_mecha_seq, self.mfb_mecha_part , self.mfb_mecha_part_cell_sort)
    if remind then
        return 2
    end

    return self.mfb_right_tog_select_id >= 1 and self.mfb_right_tog_select_id or 1
end

function MechaView:OnClickMFBTog(index)
    if index == 1 then
        self:FlushFighterPlaneAttrInfoPanel()
    else
        self:FlushFighterPlaneUplevelPanel()
    end

    self.mfb_right_tog_select_id = index
end

function MechaView:OnClickMFPTip()
    RuleTip.Instance:SetContent(Language.Mecha.MFPTIPContent, Language.Mecha.MFPTipTitle)
end

------------------------------------选择回调------------------------------
function MechaView:OnSelectMFBMechaHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    self.mfp_can_show_sort_tip = false

    if self.mfb_mecha_seq ~= data.seq then
        self.mfp_show_action = false
    end

    self.mfb_mecha_seq = data.seq
    self.mfb_mecha_data = data
    -- self.node_list.mfp_desc_name.text.text = data.name
    local part_data_list_cfg = MechaWGData.Instance:GetMechaPartShowDataList(self.mfb_mecha_seq)

    local is_wear_complete = MechaWGData.Instance:IsMechaWearComplete(self.mfb_mecha_seq)
    self.node_list.flag_mfp_need_zbk:CustomSetActive(not is_wear_complete)

    local part_data_list = {}
    for k, v in pairs(part_data_list_cfg) do
        if k <= MECHA_PART_TYPE.RIGHT_FOOT then
            table.insert(part_data_list, v)
        end
    end

    self.mfp_mecha_part_list:SetDataList(part_data_list)
    self.mfp_mecha_part_list:JumpToIndex(self:GetMFBSelectMechaPart(part_data_list))

    local mfp_cap = MechaWGData.Instance:GetMFPShowCap(self.mfb_mecha_seq)
    self.node_list.mfp_cap_value.text.text = mfp_cap
end

function MechaView:OnSelectMFBMechaPartHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    self.mfp_can_show_sort_tip = false
    self.mfb_mecha_part_index = item.index
    self.mfb_mecha_part = data[1].part
    self.mfb_mecha_part_data = data

    local part_cell_data_list = MechaWGData.Instance:GetMechaPartCellShowDataList(self.mfb_mecha_seq, self.mfb_mecha_part)

    self.mfp_mecha_part_cell_list:SetDataList(part_cell_data_list)
    self.mfp_mecha_part_cell_list:JumpToIndex(self:GetMFBSelectMechaPartCell(part_cell_data_list))
end

function MechaView:OnSelectMFBMechaPartCellHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data

    if self.mfp_can_show_sort_tip and self.mfb_mecha_part_cell_sort == data.sort then
        TipWGCtrl.Instance:OpenItem({item_id = data.active_item_id})
    end

    self.mfb_mecha_part_cell_sort = data.sort
    self.mfb_mecha_part_cell_data = data
    self.node_list.mfp_desc_name.text.text = data.part_name

    self:FlushFighterPlaneMidModel()
    self:FlushFighterPlaneRight()
    self:DoMFPModelAni()

    self.mfp_can_show_sort_tip = true
end

function MechaView:DoMFPModelAni()
    local ani_cfg = MFP_MODLE_ANI_CFG[self.mfb_mecha_part] or MFP_MODLE_ANI_CFG[1]

    if not IsEmptyTable(ani_cfg) then
        if self.mfp_model_tween then
            self.mfp_model_tween:Kill()
            self.mfp_model_tween = nil 
        end

        self.node_list.mfp_modle_root.rect:DOLocalMove(ani_cfg.position, 0.5)
        self.node_list.mfp_modle_root.rect:DOLocalRotate(ani_cfg.rotation, 0.8)
        self.node_list.mfp_modle_root.rect:DOScale(ani_cfg.scale, 0.5)
    end
end

------------------------------------计算选择------------------------------
function MechaView:GetMFBSelectMecha(mecha_show_data)
    if self.mfb_mecha_seq >= 0 then
        local cur_remind = MechaWGData.Instance:GetMFPMechaRemind(self.mfb_mecha_seq)

        if cur_remind then
            return self.mfb_mecha_seq
        end
    end

    if not IsEmptyTable(mecha_show_data) then
        for k, v in pairs(mecha_show_data) do
            if self.mfb_mecha_seq ~= v.seq then
                local remind = MechaWGData.Instance:GetMFPMechaRemind(v.seq)
                if remind then
                    return v.seq
                end
            end
        end 
    end

    return  self.mfb_mecha_seq >= 0 and  self.mfb_mecha_seq or 0
end

function MechaView:GetMFBSelectMechaPart(part_data_list)
    if not IsEmptyTable(part_data_list) then
        if self.mfb_mecha_part_index > 0 then
            local cur_data = part_data_list[self.mfb_mecha_part_index]

            if not IsEmptyTable(cur_data) then
               local mecha_seq = cur_data[1].mechan_seq
               local part = cur_data[1].part
                local remind = MechaWGData.Instance:GetMFPMechaPartRemind(mecha_seq, part)

                if remind then
                    return self.mfb_mecha_part_index
                end
            end
        end

        for k, v in pairs(part_data_list) do
            if k ~= self.mfb_mecha_part_index then
                local mecha_seq = v[1].mechan_seq
                local part = v[1].part
                 local remind = MechaWGData.Instance:GetMFPMechaPartRemind(mecha_seq, part)
                 if remind then
                    return k
                end
            end
        end
    end

    return self.mfb_mecha_part_index > 0 and  self.mfb_mecha_part_index or 1
end

function MechaView:GetMFBSelectMechaPartCell(part_cell_data_list)
    if self.mfb_mecha_part_cell_sort > 0 then
        local remind = MechaWGData.Instance:GetMFPMechaPartCellRemind(self.mfb_mecha_seq, self.mfb_mecha_part, self.mfb_mecha_part_cell_sort)
        if remind then
            return self.mfb_mecha_part_cell_sort
        end
    end

    if not IsEmptyTable(part_cell_data_list) then
        for k, v in pairs(part_cell_data_list) do
            if v.sort ~= self.mfb_mecha_part_cell_sort then
                local remind = MechaWGData.Instance:GetMFPMechaPartCellRemind(v.mechan_seq, v.part, v.sort)

                if remind then
                    return v.sort
                end
            end
        end
    end

    return self.mfb_mecha_part_cell_sort > 0 and  self.mfb_mecha_part_cell_sort or 1
end

------------------------------------btns------------------------------
function MechaView:OnClickMFPActive()
    local mecha_is_active = MechaWGData.Instance:IsMechaActive(self.mfb_mecha_seq)

    if not mecha_is_active then
        local cost_item_id = self.mfb_mecha_data.active_item
        local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

        if has_num >= 1 then
            MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.ACTIVE, self.mfb_mecha_seq)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
        else
            TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
        end
    end 
end

function MechaView:OnClickMFPUpLevel()
    local part_seq = self.mfb_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(part_seq)
    cur_star = cur_star >= 0 and cur_star or 0
    local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(part_seq, cur_star)
    if not IsEmptyTable(up_star_cfg) then
        local cust_item_id = up_star_cfg.cost_item_id
        local cost_item_num = up_star_cfg.cost_item_num
        local has_num = ItemWGData.Instance:GetItemNumInBagById(cust_item_id)

        if has_num >= cost_item_num then
            MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.PART_STAR, part_seq)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0)})
        else
            TipWGCtrl.Instance:OpenItem({item_id = cust_item_id})
        end
    end
end

function MechaView:OnClickMFPJnyl()
    if not self.mfb_mecha_data then
        return
    end

    CommonSkillShowCtrl.Instance:SetMechaSkillViewDataAndOpen({gundam_seq = self.mfb_mecha_data.seq, weapon_type = 0})
end

function MechaView:OnClickMFPUpZbk()
    if not MechaWGData.Instance:IsMechaActive(self.mfb_mecha_seq) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Mecha.ArnanentNotActiveTips)
        return
    end

    MechaWGCtrl.Instance:OpenMechaArmamentView(self.mfb_mecha_seq)
end
------------------------------------MFPMechaListRender------------------------------
MFPMechaListRender = MFPMechaListRender or BaseClass(BaseRender)

function MFPMechaListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local icon_bundle, icon_asset = ResPath.GetMechaImg(self.data.icon)
    self.node_list.mecha_icon.image:LoadSprite(icon_bundle, icon_asset, function ()
        self.node_list.mecha_icon.image:SetNativeSize()
    end)

    self.node_list.desc_name.text.text = self.data.name
    local remind = MechaWGData.Instance:GetMFPMechaRemind(self.data.seq)
    self.node_list.remind:CustomSetActive(remind)

    local is_wear_complete = MechaWGData.Instance:IsMechaWearComplete(self.data.seq)
    self.node_list.flag_wear_complete:CustomSetActive(is_wear_complete)
end

function MFPMechaListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_nor:CustomSetActive(not is_select)
    self.node_list.bg_select:CustomSetActive(is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end

------------------------------------MFPMechaPartListRender------------------------------
MFPMechaPartListRender = MFPMechaPartListRender or BaseClass(BaseRender)

function MFPMechaPartListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local part = self.data[1].part
    local mecha_seq = self.data[1].mechan_seq
    local part_cfg = MechaWGData.MECHA_PART_SHOW_CFG[part] or MechaWGData.MECHA_PART_SHOW_CFG[1]

    local nor_bundle, nor_asset = ResPath.GetMechaImg(part_cfg.icon)
    self.node_list.icon_nor.image:LoadSprite(nor_bundle, nor_asset, function ()
        self.node_list.icon_nor.image:SetNativeSize()
    end)

    local hl_bundle, hl_asset = ResPath.GetMechaImg(part_cfg.icon_hl)
    self.node_list.icon_hl.image:LoadSprite(hl_bundle, hl_asset, function ()
        self.node_list.icon_hl.image:SetNativeSize()
    end)

    local mirror = part == MECHA_PART_TYPE.RIGHT_FOOT or part == MECHA_PART_TYPE.RIGHT_HAND
    local scale = mirror and -1 or 1
    RectTransform.SetLocalScaleXYZ(self.node_list.icon_nor.rect, scale, 1, 1)
    RectTransform.SetLocalScaleXYZ(self.node_list.icon_hl.rect, scale, 1, 1)

    local name = Language.Mecha.MechaPartName[part]
    self.node_list.desc_name.text.text = name
    self.node_list.desc_name_hl.text.text = name

    local remind = MechaWGData.Instance:GetMFPMechaPartRemind(mecha_seq, part)
    self.node_list.remind:CustomSetActive(remind)
end

function MFPMechaPartListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end

------------------------------------MFPMechaPartCellListRender------------------------------
MFPMechaPartCellListRender = MFPMechaPartCellListRender or BaseClass(BaseRender)

function MFPMechaPartCellListRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function MFPMechaPartCellListRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetIsShowTips(false)
        self.item:SetCellBgEnabled(false)
        self.item:SetUseButton(false)
    end
end

function MFPMechaPartCellListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData({item_id = self.data.active_item_id})
    local seq = self.data.seq

    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    self.node_list.flag_lock:CustomSetActive(cur_star < 0)

    self.item:MakeGray(cur_star < 0)

    local remind = MechaWGData.Instance:GetMFPMechaPartCellRemind(self.data.mechan_seq, self.data.part, self.data.sort)
    self.node_list.remind:CustomSetActive(remind)
end

function MFPMechaPartCellListRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

------------------------------------MFPAttrInfoListRender------------------------------
MFPAttrInfoListRender = MFPAttrInfoListRender or BaseClass(BaseRender)

function MFPAttrInfoListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text = self.data.attr_value or 0
    local has_add_value = self.data.add_value and self.data.add_value ~= "" and self.data.add_value ~= "0%"
    self.node_list.add_value.text.text = has_add_value and self.data.add_value or ""
    self.node_list.arrow:CustomSetActive(has_add_value)
end

-----------------------------------MFPSkillInfoItemRender-------------------------------------
MFPSkillInfoItemRender = MFPSkillInfoItemRender or BaseClass(BaseRender)

function MFPSkillInfoItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local skill_info = SkillWGData.Instance:GetJiJiaSkillConfig(self.data.skill_id)
    if IsEmptyTable(skill_info) then
        return
    end

    self.node_list.skill_name.text.text = skill_info.skill_name

    local common_skill_info = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id)
    if IsEmptyTable(common_skill_info) then
        return         
    end

    local bundle, asset = ResPath.GetSkillIconById(common_skill_info.icon_resource)
    self.node_list.skill_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.skill_icon.image:SetNativeSize()
    end)

    self.node_list.skill_desc.text.text = common_skill_info.description
end