TipsImgSystemView = TipsImgSystemView or BaseClass(BaseRender)

function TipsImgSystemView:__init()
	self.messge = {}
	self.anim_speed = 1
	self.convertion = -1
	self.index = 0

	self.chuanwen_panel = self.node_list["chuanwen_panel"]

	self.anim = self.chuanwen_panel.animator
	self.canvas_group = self.chuanwen_panel.canvas_group
	self.begin_pos = self.chuanwen_panel.transform.localPosition
	self.has_finished = false
end

function TipsImgSystemView:__delete()
	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	if self.tween then
		self.tween:Kill()
	end
end

function TipsImgSystemView:SystemTipsVis()
	return self.canvas_group and self.canvas_group.alpha > 0
end

function TipsImgSystemView:Show(msg, speed, index)
	self:SetActive(true)
	self.has_finished = false

	if self.begin_pos then
		self.chuanwen_panel.transform.localPosition = self.begin_pos
	end

	self.index = index or 0
	self.convertion = -1
	self.area_tips_con = 0
	speed = speed or 1
	self.anim_speed = speed
	self.convertion = nil

	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	self.close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CloseTips, self,true), 2)
	self.messge = msg

	self.chuanwen_panel:SetActive(true)
	self:FlushView()
end


function TipsImgSystemView:ChangeSpeed(speed)
	self.anim_speed = speed
	self:DoMove()
end

function TipsImgSystemView:AddIndex()
	self.index = self.index + 1
end

function TipsImgSystemView:DoMove()
	if nil == self.begin_pos or nil == self.chuanwen_panel then
		return
	end

	if self.tween then
		self.tween:Kill()
	end

	self.chuanwen_panel.transform.localPosition = Vector3(self.begin_pos.x, self.begin_pos.y + 35 * self.index, self.begin_pos.z)
	self.tween = self.chuanwen_panel.transform:DOLocalMoveY(self.begin_pos.y + 35 * (self.index + 1), 1 / self.anim_speed)
	self.tween:SetEase(DG.Tweening.Ease.Linear)
end

function TipsImgSystemView:CloseTips(delay_flag)
	self.has_finished = true
	
	if delay_flag then
		UITween.AlpahShowPanel(self.node_list["chuanwen_panel"],false,0.5,DG.Tweening.Ease.Linear,function ()
			self:SetActive(false)
			if self.chuanwen_panel and self.begin_pos then
				self.chuanwen_panel.transform.localPosition = self.begin_pos
			end
		end)
	else
		self.canvas_group.alpha = 1
		self:SetActive(false)
		if self.chuanwen_panel and self.begin_pos then
			self.chuanwen_panel.transform.localPosition = self.begin_pos
		end
	end

	

	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	if self.tween then
		self.tween:Kill()
	end

	
end

function TipsImgSystemView:FlushView(param_list)
	self:DoMove()
	local data = self.messge
	for i=1,5 do
		if not IsEmptyTable(data.text_list) and data.text_list[i] ~= "" then
			self.node_list["chuangwen_text"..i]:SetActive(true)
			self.node_list["chuangwen_text"..i].text.text = data.text_list[i]
			if not IsEmptyTable(data.need_color) and not IsEmptyTable(data.need_color[i]) then
				local color = data.need_color[i]
				local gradient1 = color and color.gradient1 or COLOR3B.WHITE
				local gradient2 = color and color.gradient2 or COLOR3B.WHITE
				local out_line = color and color.out_line or COLOR3B.WHITE
				self.node_list["chuangwen_text"..i].gradient.Color1 = StrToColor(gradient1)
				self.node_list["chuangwen_text"..i].gradient.Color2 = StrToColor(gradient2)
				self.node_list["chuangwen_text"..i].out_line.effectColor = StrToColor(out_line)
			end
		else
			self.node_list["chuangwen_text"..i]:SetActive(false)
		end
		if not IsEmptyTable(data.img_list) and not IsEmptyTable(data.img_list[i]) then
			local bundle = data.img_list[i] and data.img_list[i].bundle or ""
			local asset = data.img_list[i] and data.img_list[i].asset or ""
			if bundle ~= "" and asset ~= "" then
				self.node_list["chuangwen_img"..i]:SetActive(true)
				self.node_list["chuangwen_img"..i].image:LoadSprite(bundle,asset,function ()
					self.node_list["chuangwen_img"..i].image:SetNativeSize()
				end)
			else
				self.node_list["chuangwen_img"..i]:SetActive(false)
			end
		else
			self.node_list["chuangwen_img"..i]:SetActive(false)
		end
	end
	
end

function TipsImgSystemView:GetAnimSpeed()
	return self.anim_speed
end

function TipsImgSystemView:IsOpen()
	return not self.has_finished
end