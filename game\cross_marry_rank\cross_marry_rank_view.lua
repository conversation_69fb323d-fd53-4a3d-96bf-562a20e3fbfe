CrossMarryRankView = CrossMarryRankView or BaseClass(SafeBaseView)

function CrossMarryRankView:__init()
    self.view_layer = UiLayer.Normal

    self:SetMaskBg()

    self:AddViewResource(0, "uis/view/marry_rank_ui_prefab", "layout_cross_marry_rank")
end

function CrossMarryRankView:ReleaseCallBack()
    if self.rank_reward_list then
        self.rank_reward_list:DeleteMe()
        self.rank_reward_list = nil
    end

    if self.role_model_list then
        for i = 1, #self.role_model_list do
            self.role_model_list[i]:DeleteMe()
        end
        self.role_model_list = nil
    end
    self:CleanTimer()
end

function CrossMarryRankView:OpenCallBack()
    CrossMarryRankWGCtrl.Instance:SendMarryRechargedRank(CROSS_MARRY_RANK_OPERA.RANK_INFO)
end

function CrossMarryRankView:LoadCallBack()
    --查看排行榜按钮
    self.node_list["btn_rank"].button:AddClickListener(BindTool.Bind(self.OnClickRankView, self))

    --提示按钮
    self.node_list["btn_tips"].button:AddClickListener(BindTool.Bind(self.OnTipsBtnClick, self))

    --前往结婚按钮
    self.node_list["btn_go_marry"].button:AddClickListener(BindTool.Bind(self.OnGoMarryBtnClick, self))

    --奖励列表
    self.rank_reward_list = AsyncListView.New(MarryRankRender, self.node_list["rank_reward_list"])

    --活动时间文本的显示
    self.node_list["time_text"].text.text = ""

    self:FlushTimer()
end

function CrossMarryRankView:OnClickRankView()
    CrossMarryRankWGCtrl.Instance:OpenShowRankView()
end

function CrossMarryRankView:OnTipsBtnClick()
    local role_tip = RuleTip.Instance
    if role_tip then
        role_tip:SetContent(Language.MarryRechargeRank.RechargeRuleContent, Language.MarryRechargeRank.RechargeRankRule)
    end
end

--计时器
function CrossMarryRankView:FlushTimer()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
    .CROSS_CHANNEL_ACTIVITY_TYPE_MARRY_RANK)
    local total_time = activity_data.end_time - TimeWGCtrl.Instance:GetServerTime()
    local interval = 1
    self:CleanTimer()
    self.timer = CountDown.Instance:AddCountDown(total_time, interval,
        BindTool.Bind(self.OnUpdateCountDown, self),
        BindTool.Bind(self.Close, self)
    )
end

function CrossMarryRankView:OnUpdateCountDown(elapse_time, total_time)
    local time_str = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
    self.node_list.time_text.text.text = string.format(Language.MarryRechargeRank.TimeText, time_str)
end

-- 清除倒计时器
function CrossMarryRankView:CleanTimer()
    if self.timer and CountDown.Instance:HasCountDown(self.timer) then
        CountDown.Instance:RemoveCountDown(self.timer)
        self.timer = nil
    end
end

function CrossMarryRankView:OnFlush()
    self:FlushView()
end

function CrossMarryRankView:FlushView()
    --获取排行榜信息
    local rank_data = CrossMarryRankWGData.Instance:GetRankData()
    --拿到配置表信息
    local rank_reward_list = CrossMarryRankWGData.Instance:GetRankCfgList()

    if IsEmptyTable(rank_data) or IsEmptyTable(rank_reward_list) then
        return
    end

    --更新榜一模型
    self:FlushModelView()

    --更新排行榜奖励信息
    if self.rank_reward_list then
        self.rank_reward_list:SetDataList(rank_reward_list)
    end

    --更新排名
    local self_rank = rank_data.self_rank
    local str = self_rank > 0 and self_rank or Language.MarryRechargeRank.WeiShangBang
    self.node_list["rank_text"].text.text = string.format(Language.MarryRechargeRank.PaiMing, str)

    --更新累计金额
    local self_recharge_value = rank_data.self_recharge_value
    self.node_list["recharge_text"].text.text = string.format(Language.MarryRechargeRank.LeiJi, self_recharge_value)
end

function CrossMarryRankView:FlushModelView()
    --获取到榜一的信息
    local top_data = CrossMarryRankWGData.Instance:GetTopRankInfo()
    local is_empty = IsEmptyTable(top_data)
    self.node_list["display_left"]:CustomSetActive(not is_empty)
    self.node_list["display_right"]:CustomSetActive(not is_empty)
    self.node_list["no_display_left"]:CustomSetActive(is_empty)
    self.node_list["no_display_right"]:CustomSetActive(is_empty)
    if is_empty then
        self.node_list["left_name_text"].text.text = Language.MarryRechargeRank.XuWeiYiDai
        self.node_list["right_name_text"].text.text = Language.MarryRechargeRank.XuWeiYiDai
        return
    end

    local couple_info = top_data.couple_info
    local self_uuid = RoleWGData.Instance:GetUUid()
    for i = 1, #couple_info do
        --设置模型
        local trans = i == 1 and self.node_list["display_left"] or self.node_list["display_right"]
        if nil == self.role_model_list then self.role_model_list = {} end
        if nil == self.role_model_list[i] then
            self.role_model_list[i] = RoleModel.New()
            local display_data = {
                parent_node = trans,
                camera_type = MODEL_CAMERA_TYPE.BASE,
                -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                rt_scale_type = ModelRTSCaleType.M,
                can_drag = true,
            }
    
            self.role_model_list[i]:SetRenderTexUI3DModel(display_data)

            -- self.role_model_list[i]:SetUI3DModel(trans.transform, trans.event_trigger_listener,
            --     1, false, MODEL_CAMERA_TYPE.BASE)
        else
            self.role_model_list[i]:PlayLastAction()
        end

        --加载模型
        local is_self = couple_info[i].uuid == self_uuid
        if is_self then
            self:LoadModel(self.role_model_list[i], couple_info[i])
        else
            self:BrowRoelInfo(couple_info[i], i) --如果不是自己的模型，需要请求角色信息
        end


        --设置名字
        local name_data = Split(couple_info[i].name, "_")
        local user_name = string.format(Language.MarryRechargeRank.UserNameStr, name_data[2], name_data[1])

        trans = i == 1 and self.node_list["left_name_text"] or self.node_list["right_name_text"]
        trans.text.text = user_name
    end
end

--加载模型
function CrossMarryRankView:LoadModel(model_display, role_info, appe_data)
    if not self.node_list or not model_display then
        return
    end

    local resource = MarryWGData.Instance:GetMarryOtherCfg().wedding_dress_show
    local res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(resource, role_info.sex, role_info.prof)
    appe_data = appe_data or {}
    if model_display.role_res_id ~= res_id then
        local extra_role_model_data = {
            prof = role_info.prof,
            sex = role_info.sex,
            d_face_res = appe_data.default_face_res_id,
            d_hair_res = appe_data.default_hair_res_id,
            d_body_res = appe_data.default_body_res_id,
        }

        model_display:SetRoleResid(res_id, nil, extra_role_model_data)
        model_display:SetWeaponModelFakeRemove()
    end
end

--请求角色信息
function CrossMarryRankView:BrowRoelInfo(role_info, index)
    local flush_fun = function(protocol)
        local model_display = self.role_model_list and self.role_model_list[index]
        self:LoadModel(model_display, role_info, protocol.appearance)
    end
    BrowseWGCtrl.Instance:BrowRoelInfo(role_info.uuid.temp_low, flush_fun)
end

function CrossMarryRankView:OnGoMarryBtnClick()
    MarryWGCtrl.Instance:OpenSelectLoverView(1) --1结婚2离婚
end

---排名奖励列表格子
MarryRankRender = MarryRankRender or BaseClass(BaseRender)

function MarryRankRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function MarryRankRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function MarryRankRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local desc = ""
    if self.data.min_rank == self.data.max_rank then
        desc = string.format(Language.MarryRechargeRank.RankText1, self.data.min_rank)
    else
        desc = string.format(Language.MarryRechargeRank.RankText2, self.data.min_rank, self.data.max_rank)
    end

    self.node_list["title_text"].text.text = string.format(Language.MarryRechargeRank.RankTitle1, self.data.reach_value,
        desc)

    self.reward_list:SetDataList(self.data.reward_item)
end
