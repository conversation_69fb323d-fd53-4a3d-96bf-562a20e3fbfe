-- 天裳豪礼
GloryCrystalHaoLiView = GloryCrystalHaoLiView or BaseClass(SafeBaseView)

function GloryCrystalHaoLiView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.default_index = TabIndex.glory_crystal_daily_task

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    local assetbundle = "uis/view/glory_crystal_ui_prefab"
	self:AddViewResource(0, assetbundle, "glory_craystal_bg_panel")
    self:AddViewResource(TabIndex.glory_crystal_daily_task, assetbundle, "glory_crystal_daily_task_view")
    self:AddViewResource(TabIndex.glory_crystal_purchase, assetbundle, "glory_crystal_purchase_view")
    self:AddViewResource(TabIndex.glory_crystal_acc_recharge, assetbundle, "glory_crystal_acc_recharge_view")
    
    self:AddViewResource(0, assetbundle, "VerticalTabbar")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
    
    self:SetTabIndex()
end

function GloryCrystalHaoLiView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
    end
    
    self:ReleaseDailyTask()
    self:ReleasePurchase()
    self:ReleaseAccRecharge()
end

function GloryCrystalHaoLiView:LoadCallBack()
	if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("a3_tcxy_icon_")
		self.tabbar:SetVerTabbarIconPath(ResPath.GetGloryCrystalExchangeShopImg)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:Init(self.tab_name_list, nil, "uis/view/glory_crystal_ui_prefab", nil, self.remind_name_list)
    end
    
    self:ChangeViewStyle()
end

function GloryCrystalHaoLiView:LoadIndexCallBack(index)
    if index == TabIndex.glory_crystal_daily_task then
        self:LoadDailyTaskCallBack()
    elseif index == TabIndex.glory_crystal_purchase then
        self:LoadPurchaseCallBack()
    elseif index == TabIndex.glory_crystal_acc_recharge then
        self:LoadAccRechargeCallBack()
    end
end

function GloryCrystalHaoLiView:ShowIndexCallBack(index)
    local title_name = self.tab_name_list[math.floor(index / 10)] or ""
    self.node_list.title_view_name.text.text = title_name
end

function GloryCrystalHaoLiView:OnFlush(param_list, index)
    if index == TabIndex.glory_crystal_daily_task then
        self:FlushDailyTask()
    elseif index == TabIndex.glory_crystal_purchase then
        self:FlushPurchase()
    elseif index == TabIndex.glory_crystal_acc_recharge then
        self:FlushAccRecharge()
    end 
end

function GloryCrystalHaoLiView:SetTabIndex()
	self.tab_name_list = Language.GloryCrystal.TabGrop2
	self.remind_name_list = {
        {RemindName.GloryCrystalDailyTask}, 
        {RemindName.GloryCrystalPurchase}, 
        {RemindName.GloryCrystalAccRecharge}
    }
end

function GloryCrystalHaoLiView:ChangeViewStyle()
	local cfg = GloryCrystalWGData.Instance:GetCurViewStyle()
	if not cfg then
		return
	end

	local bundle, asset = ResPath.GetRawImagesJPG("a3_tcxy_ll_bg_" .. cfg.color_index)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end