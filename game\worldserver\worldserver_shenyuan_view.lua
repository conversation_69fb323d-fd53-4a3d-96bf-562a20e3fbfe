--新增 深渊魔王
function WorldServerView:InitShenYuanView()
    BossWGCtrl.Instance:SendShenYuanOpera(WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_INFO)
    self.node_list["sy_box_text"].button:AddClickListener(BindTool.Bind1(self.BrowseShenYuan<PERSON>illR<PERSON>ord, self))
    self.node_list["btn_shenyuan_kill"].button:AddClickListener(BindTool.Bind1(self.GoToKillShenYuan, self)) --深渊boss前往击杀
    self.node_list["btn_shenyuan_des"].button:AddClickListener(BindTool.Bind1(self.OnClickShenYuanRule, self)) --规则
    -- self.node_list["btn_guishu"].button:AddClickListener(BindTool.Bind2(self.OnClickReward, self, -1)) --归属
    -- self.node_list["btn_canyu"].button:AddClickListener(BindTool.Bind2(self.OnClickReward, self, 1)) --参与
    self.node_list["shenyuan_add_times"].button:AddClickListener(BindTool.Bind1(self.OnClickAddShenyuan, self)) --深渊boss增加次数
    self.node_list["layout_shenyuan_check_hook"].button:AddClickListener(BindTool.Bind1(self.OnClickShenyuanCheckHook, self))
	XUI.AddClickEventListener(self.node_list["sy_boss_text_btn"], BindTool.Bind(self.SYBossConcernBtn, self))

    self.select_reward_idx = 1

	self.shenyuan_list_data = nil
	self.shenyuan_cambered_list = nil
	self.shenyuan_btn_select_index = 1
	self.shenyuan_drag_select_index = -1

    self:CreateShenYuanCell()
    self:InitCreateShenYuanCamberedList()

    local time_str = BossWGData.Instance:GetShenYuanRefreshTime()
    self.node_list["shenyuan_txt_tip"].text.text = string.format(Language.Boss.ShenYuanTip, time_str)
end

function WorldServerView:OnClickAddShenyuan()
    BossWGCtrl.Instance:OpenBossVipTimesView(VipPowerId.vat_shenyuanboss_buy_times)
end

function WorldServerView:CreateShenYuanCell()
    if not self.reward_cell_list1 then
        self.reward_cell_list1 = AsyncBaseGrid.New()
        local t = {}
        t.col = 3
        t.change_cells_num = 1
        t.itemRender = BossRewardCell
        t.list_view = self.node_list["shenyuan_cell_list_1"]
        self.reward_cell_list1:CreateCells(t)
        self.reward_cell_list1:SetStartZeroIndex(false)
    end

    if not self.reward_cell_list2 then
        self.reward_cell_list2 = AsyncBaseGrid.New()
        local t = {}
        t.col = 3
        t.change_cells_num = 1
        t.itemRender = BossRewardCell
        t.list_view = self.node_list["shenyuan_cell_list_2"]
        self.reward_cell_list2:CreateCells(t)
        self.reward_cell_list2:SetStartZeroIndex(false)
    end
end

function WorldServerView:InitCreateShenYuanCamberedList()
	local cambered_list_data = {
		item_render = BossShenYuanItemrender,
		asset_bundle = "uis/view/boss_ui_prefab",
		asset_name = "ph_shenyuan_boss_render",

		scroll_list = self.node_list.ph_shenyuan_boss_cambered_list,
		center_x = 800,
		center_y = -230,
		radius_x = 800,
		radius_y = 800,
		angle_delta = Mathf.PI / BossView.ANGLE_DELTA,
		origin_rotation = Mathf.PI * 0.41,
		is_drag_horizontal = false,
		is_clockwise_list = false,
		speed = 1,
		arg_speed = 0.2,
		viewport_count = BossView.DRAG_COUNT,

		click_item_cb = BindTool.Bind(self.OnClickShenYuanBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragShenYuanToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragShenYuanToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragShenYuanEndCallBack, self),
	}

	self.shenyuan_cambered_list = CamberedList.New(cambered_list_data)
end

function WorldServerView:OnClickShenYuanBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.shenyuan_btn_select_index == select_index) then
		return
	end

	self.shenyuan_btn_select_index = select_index
	self.shenyuan_drag_select_index = select_index

	self:OnShenYuanSelectedBtnChange(function()
		self:ShenYuanBossSelected(item_cell)
	end, true)
end

function WorldServerView:OnDragShenYuanToNextCallBack()
	local max_index = self.shenyuan_list_data and #self.shenyuan_list_data or BossView.DRAG_COUNT
	self.shenyuan_drag_select_index = self.shenyuan_drag_select_index < BossView.DRAG_COUNT and BossView.DRAG_COUNT or self.shenyuan_drag_select_index
	self.shenyuan_drag_select_index = self.shenyuan_drag_select_index + 1
	self.shenyuan_drag_select_index = self.shenyuan_drag_select_index > max_index and max_index or self.shenyuan_drag_select_index
end

function WorldServerView:OnDragShenYuanToLastCallBack()
	self.shenyuan_drag_select_index = self.shenyuan_drag_select_index - 1
	self.shenyuan_drag_select_index = self.shenyuan_drag_select_index < BossView.DRAG_COUNT and BossView.DRAG_COUNT or self.shenyuan_drag_select_index
end

function WorldServerView:OnDragShenYuanEndCallBack()
	self:OnShenYuanSelectedBtnChange(nil, false, self.shenyuan_drag_select_index)
end

function WorldServerView:OnShenYuanSelectedBtnChange(callback, is_click, drag_index)
	if self.shenyuan_cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.shenyuan_btn_select_index or 1
	self.shenyuan_cambered_list:ScrollToIndex(to_index, callback, is_click)

	if is_click then
		local item_list = self.shenyuan_cambered_list:GetRenderList()
		for k, item_cell in ipairs(item_list) do
			item_cell:SetSelectedHL(to_index)
		end
	end
end

-- 归属国宝箱预览
function WorldServerView:BrowseShenYuanKillRecord()
    local cfg = self.shenyuan_select_data.box_drop_item_list
    local list = {}
    local data
    for i, v in ipairs(cfg) do
        local str = Split(v, ":")
        data = {
            item_id = tonumber(str[1]),
            num = tonumber(str[2]),
            is_bind = tonumber(str[3]),
        }
        table.insert(list, data)
    end
    BossAssistWGCtrl.Instance:OpenShenyuanBoxReward(list)

end

-- function WorldServerView:OnClickReward(index)
--     index = self.select_reward_idx + index
--     index = index > 2 and 1 or index
--     index = index < 1 and 2 or index
--     self.select_reward_idx = index
--     self:OnFlushShenYuanReward()
-- end

function WorldServerView:OnFlushShenYuanReward()
    if IsEmptyTable(self.reward_cell_list1) or IsEmptyTable(self.reward_cell_list2) then
        self:CreateShenYuanCell()
    end

    --self.node_list.shenyuan_reward_title.text.text = Language.Boss.ShenYuanBossRewardTitle[self.select_reward_idx]
    if not self.shenyuan_select_data then
        return
    end

    local cfg1 = self.shenyuan_select_data.drop_item_list1
    local cfg2 = self.shenyuan_select_data.drop_item_list2
    local list1 = {}
    local data1
    for i, v in ipairs(cfg1) do
        local str = Split(v, ":")
        data1 = {
            item_id = tonumber(str[1]),
            num = tonumber(str[2]),
            is_bind = tonumber(str[3]),
            cell_scale = 0.9,
        }
        table.insert(list1, data1)
    end

    local list2 = {}
    local data2
    for i, v in ipairs(cfg2) do
        local str = Split(v, ":")
        data2 = {
            item_id = tonumber(str[1]),
            num = tonumber(str[2]),
            is_bind = tonumber(str[3]),
            cell_scale = 0.9,
        }
        table.insert(list2, data2)
    end
    self.reward_cell_list1:SetDataList(list1, 3)
    self.reward_cell_list2:SetDataList(list2, 3)
end

function WorldServerView:OnClickShenYuanRule()
    local des, title = Language.Boss.ShenYuanRuleTip, Language.Boss.PlayTitle
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(title)
    role_tip:SetContent(des)
end

function WorldServerView:FlushShenyuanView()
    local default_index = self.shenyuan_btn_select_index or 1
    if not self.shenyuan_btn_select_index then
        default_index = BossWGData.Instance:GetShenYuanBossDefaultIndex()
		self.shenyuan_btn_select_index = default_index
    end
    local data_list = BossWGData.Instance:GetShenyuanBossData() or {}

    self.shenyuan_list_data = data_list

    self.shenyuan_cambered_list:CreateCellList(#data_list)
    local btn_item_list = self.shenyuan_cambered_list:GetRenderList()
    for k, item_cell in ipairs(btn_item_list) do
        local item_data = self.shenyuan_list_data[k]
        item_cell:SetData(item_data)
        if self.shenyuan_btn_select_index == item_cell:GetIndex() then
            self:OnShenYuanSelectedBtnChange(function()
                self:ShenYuanBossSelected(item_cell)
            end, true)
        end
    end

    --boss次数
    local kill_time, join_time = BossWGData.Instance:GetShenYuanTimes()
    local other_cfg = BossWGData.Instance:ShenYuanBossOtherCfg()
    local max_kill_time = other_cfg.kill_reward_times
    local str1 = ""
    if max_kill_time == -1 then
        str1 = Language.Boss.Infinite
    else
        local color1 = kill_time >= max_kill_time and COLOR3B.D_RED or COLOR3B.DEFAULT_NUM
        local cur_time1 = max_kill_time - kill_time >= 0 and max_kill_time - kill_time or 0
        str1 = ToColorStr(string.format("%d/%d", cur_time1, max_kill_time), color1)
    end

    --self.node_list.guishu_times_txt.text.text = string.format(Language.Boss.GuiShuDes, ToColorStr(str1, COLOR3B.D_GREEN))
    local join_time, max_join_time = BossWGData.Instance:GetShenYuanTimesInfo()
    local color2 = join_time >= max_join_time and COLOR3B.D_RED or COLOR3B.DEFAULT_NUM
    local cur_time2 = max_join_time - join_time >= 0 and max_join_time - join_time or 0
    local str2 = ToColorStr(string.format("%d/%d", cur_time2, max_join_time), color2)
    self.node_list.join_times_txt.text.text = string.format(Language.Boss.JoinDes, str2)

	if IsEmptyTable(self.shenyuan_select_data) then
		return
	end
	local is_concern = BossWGData.Instance:GetShenYuanBossIsConcern(self.shenyuan_select_data.seq)
	self.node_list.sy_boss_text_btn.text.text = is_concern and Language.Boss.BossResurgenceBtnTips2 or Language.Boss.BossResurgenceBtnTips
end

function WorldServerView:ShenYuanBossSelected(cell)
    if cell and cell:GetData() then
        self.shenyuan_select_data = cell.data
        self.node_list.shenyuan_boss_name.text.text = self.shenyuan_select_data.boss_name

		local is_concern = BossWGData.Instance:GetShenYuanBossIsConcern(self.shenyuan_select_data.seq)
		self.node_list.sy_boss_text_btn.text.text = is_concern and Language.Boss.BossResurgenceBtnTips2 or Language.Boss.BossResurgenceBtnTips

        self:OnFlushBossAnim()
        self:OnFlushShenYuanReward()
    end
end

function WorldServerView:GoToKillShenYuan()
    -- if Scene.Instance:GetSceneType() ~= SceneType.Common then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutFubenTip)
    --     return
    -- end

    if not IsEmptyTable(self.shenyuan_select_data) then
        local level = RoleWGData.Instance:GetRoleLevel()
        if level < self.shenyuan_select_data.enter_level then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelUnLock2,
                RoleWGData.GetLevelString(self.shenyuan_select_data.enter_level)))
            return
        end
        BossWGData.Instance:GetSetLastSelectInfo(1, self.shenyuan_btn_select_index)
        BossWGData.Instance:SetCurSelectBossID(0, 0, self.shenyuan_select_data.boss_id)
        BossWGCtrl.Instance:SendShenYuanOpera(WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_ENTER,
            self.shenyuan_select_data.seq)
    end
end

function WorldServerView:ReleaseShenYuanView()
    if nil ~= self.reward_cell_list1 then
        self.reward_cell_list1:DeleteMe()
        self.reward_cell_list1 = nil
    end

    if nil ~= self.reward_cell_list2 then
        self.reward_cell_list2:DeleteMe()
        self.reward_cell_list2 = nil
    end

    self.shenyuan_cache_resid = nil

    if self.shenyuan_cambered_list then
		self.shenyuan_cambered_list:DeleteMe()
		self.shenyuan_cambered_list = nil
	end

    self.shenyuan_list_data = nil
    self.shenyuan_drag_select_index = -1
	self.shenyuan_btn_select_index = nil
end

function WorldServerView:OnClickShenyuanCheckHook()
	local data = {}
	data.tabindex = TabIndex.world_new_shenyuan_boss
	data.cur_layer = self.cur_kflayer
	data.is_world_server = true
	BossWGCtrl.Instance:SetFocusViewDataAndOpen(data)
end

function WorldServerView:SYBossConcernBtn()
	if IsEmptyTable(self.shenyuan_select_data) then
		return
	end

	local is_concern = BossWGData.Instance:GetShenYuanBossIsConcern(self.shenyuan_select_data.seq)
	local opa_type = is_concern and WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_UN_CONCERN or
		WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_CONCERN
	BossWGCtrl.Instance:SendShenYuanOpera(opa_type, self.shenyuan_select_data.seq)

	SysMsgWGCtrl.Instance:ErrorRemind(is_concern and Language.Boss.BossResurgenceMsg2 or Language.Boss.BossResurgenceMsg)
end

BossShenYuanItemrender = BossShenYuanItemrender or BaseClass(BaseRender)
function BossShenYuanItemrender:__init()
    self.click_callback = nil
end

function BossShenYuanItemrender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end

    self.click_callback = nil
end

function BossShenYuanItemrender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function BossShenYuanItemrender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.is_grey_show = false
    local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. self.data.big_icon)
    self.node_list["img_boss"].image:LoadSprite(bundle, asset, function()
        self.node_list.img_boss.image:SetNativeSize()
    end)

    -- local bottom_color = self.data.bottom_color
    -- if bottom_color and bottom_color ~= "" and self.node_list.boss_bg then
    -- 	local bg_bundle, bg_asset = ResPath.GetBossUI("a1_boss_bg_" .. bottom_color)
    -- 	self.node_list["boss_bg"].image:LoadSprite(bg_bundle, bg_asset, function()
    -- 		self.node_list.boss_bg.image:SetNativeSize()
    -- 	end)
    -- end

	self.node_list["text_boss_name"].text.text = self.data.boss_name
	self.node_list.text_boss_lv.text.text = string.format(Language.Boss.LvText, self.data.boss_level)
	self.node_list["text_boss_state"].text.text = ""

	--self.node_list.boss_suo:SetActive(false)
	self:OnFlushServerInfo()

    local role_level = RoleWGData.Instance:GetRoleLevel()
    if self.data.enter_level and role_level < self.data.enter_level then
        self.is_grey_show = true
        self:ShowLockInfo()
    else
        self:ShowNormalInfo()
    end

	-- self.node_list.gray_mask:SetActive(self.is_grey_show)
end

function BossShenYuanItemrender:ShowLockInfo()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end

    self.node_list.lbl_time.text.text = ""
	--self.node_list.boss_suo:SetActive(true)
	self.node_list["text_boss_state"].text.text = string.format(ToColorStr(Language.XiuXianShiLian.Lock, "#ff9292"),
		RoleWGData.GetLevelString(self.data.enter_level))
end

function BossShenYuanItemrender:ShowNormalInfo()
    local color1 = COLOR3B.DEFAULT
    local role_level = RoleWGData.Instance:GetRoleLevel()
    -- self.node_list.gray_mask:SetActive(false)

    if role_level - self.data.boss_level >= self.data.max_delta_level then
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
        self.node_list["text_boss_state"].text.text = Language.Boss.LevelHighLimit
        self.is_grey_show = true
		--self.node_list.boss_suo:SetActive(true)
    else
        self:RefreshRemainTime()
        if self.refresh_event == nil then
            self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 1)
        end
    end

end

function BossShenYuanItemrender:OnFlushServerInfo()
    local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
    if monster_info and monster_info.boss_jieshu > 0 and not self.is_grey_show then
        self.node_list["text_boss_jie"].text.text = string.format(Language.Boss.JieShu,
            NumberToChinaNumber(monster_info.boss_jieshu))
    end
end

--刷新时间
function BossShenYuanItemrender:RefreshRemainTime()
    local boss_server_info = BossWGData.Instance:GetShenYuanBossInfoById(self.data.boss_id)
    local time = 0
    if not IsEmptyTable(boss_server_info) then
        if boss_server_info.status and boss_server_info.status == 1 then
            self.node_list["lbl_time"].text.text = ""
        else
            -- self.node_list.gray_mask:SetActive(true)
            time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
        end
    end

    local state = time > 1
	self.node_list["text_boss_state"].text.text = not state and Language.Boss.RefreshTime3 or ""
    if state then
        self.is_grey_show = true
        self.node_list["lbl_time"].text.text = TimeUtil.FormatSecond(time)
    else
        self.node_list["lbl_time"].text.text = ""
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
    end
end

function BossShenYuanItemrender:OnSelectChange(is_select)
    self.node_list.select_image:SetActive(is_select)
end

-- 设置点击回调
function BossShenYuanItemrender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function BossShenYuanItemrender:OnClick()
	if self.click_callback then
		self.click_callback()
	end
end

function BossShenYuanItemrender:SetSelectedHL(index)
	local is_select = self.index == index

	self.node_list.select_image:SetActive(is_select)
end