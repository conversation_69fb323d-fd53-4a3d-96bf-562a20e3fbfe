TianShenFightAlert = TianShenFightAlert or BaseClass(SafeBaseView)

function TianShenFightAlert:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/item_unlock_tips_prefab", "layout_tianshen_fight_alert")

	self:SetMaskBg(false)
end

function TianShenFightAlert:ReleaseCallBack()
	if self.stuff_cell then
		self.stuff_cell:DeleteMe()
		self.stuff_cell = nil
	end
	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end
	self.zhan_sit = nil
end

function TianShenFightAlert:LoadCallBack()
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClinkOk<PERSON><PERSON><PERSON>, self))

	self.stuff_cell = ItemCell.New(self.node_list["cell"])
	self.stuff_cell:SetNeedItemGetWay(true)

	if nil == self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.OnItemDataChange,self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end
end

function TianShenFightAlert:SetOpenData(data)
	self.zhan_sit = data
end

function TianShenFightAlert:OnFlush()
	if not self.zhan_sit then
		local zhan_sit_cfg = TianShenWGData.Instance.tianshen_cfg_auto.zhan_sit
		for k,v in pairs(zhan_sit_cfg) do
			if 0 ~= v.item_id then
				self.zhan_sit = v
			end
		end
	end

	local stuff_num = 1
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.zhan_sit.item_id)
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.zhan_sit.item_id)
	local color = item_num >= stuff_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH

	local string_num= item_num .. "/" .. stuff_num
	self.stuff_cell:SetData({item_id = self.zhan_sit.item_id, is_bind = 1})
	self.stuff_cell:SetRightBottomColorText(string_num, color)
    self.stuff_cell:SetRightBottomTextVisible(true)

    local str = string.format(Language.TianShen.OpenLimit22, stuff_num, EQUIP_COLOR[item_cfg.color], item_cfg.name)
	self.node_list["rich_des_1"].text.text = str
	local activation_num = TianShenWGData.Instance:GetActiveZhanSitNum()
	str = string.format(Language.TianShen.OpenLimit23, activation_num, #TianShenWGData.Instance.tianshen_cfg_auto.zhan_sit)
	self.node_list["rich_des_2"].text.text = str
end

function TianShenFightAlert:OnClinkOkHandler()
	if not self.zhan_sit then return end
	local stuff_num = 1
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.zhan_sit.item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.zhan_sit.item_id)
	local content = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color]) .. Language.Common.NotEnough
	if item_num < stuff_num then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.zhan_sit.item_id})
		-- SysMsgWGCtrl.Instance:ErrorRemind(content)
		return
	end

	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type19, self.zhan_sit.sit_index)
	self:Close()
end

function TianShenFightAlert:OnItemDataChange()
	self:Flush()
end