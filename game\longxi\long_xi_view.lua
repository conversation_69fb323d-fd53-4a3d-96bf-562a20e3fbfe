LongXiView = LongXiView or BaseClass(SafeBaseView)

function LongXiView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self.default_index = TabIndex.dragon_king_token
	self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(TabIndex.super_dragon_seal, "uis/view/long_xi_ui_prefab", "super_dragon_seal_view")
	self:AddViewResource(TabIndex.dragon_king_token, "uis/view/long_xi_ui_prefab", "dragon_king_token_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

	self.tab_sub = {}
	self.remind_tab = {
		{RemindName.DragonKingToken}, 
		{RemindName.SuperDragonSeal}
	}
end

function LongXiView:OpenCallBack()
    LongXiWGCtrl.Instance:SetLongXIOpraterFlag(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
	LongXiWGCtrl.Instance:SetLongXIOpraterFlag(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
end

function LongXiView:CloseCallBack()
    LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, false)
	LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, false)
end

function LongXiView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:ReleaseSuperDragonSealView()
	self:ReleaseDragonKingTokenView()

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
end

function LongXiView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
            show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:Init(Language.LongXi.TabGroup, self.tab_sub, nil, nil, self.remind_tab)
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.LongXiView, self.tabbar)
	end
end

function LongXiView:LoadIndexCallBack(index)
	if index == TabIndex.super_dragon_seal then
		self:InitSuperDragonSealView()
	elseif index == TabIndex.dragon_king_token then
		self:InitDragonKingTokenView()
	end
end

function LongXiView:ShowIndexCallBack(index)
	self.node_list.title_view_name.text.text = Language.LongXi.TabGroup[index / 10] or ""

	local bundle, asset = ResPath.GetRawImagesPNG("a3_mssh_bg_1")
	if index == TabIndex.super_dragon_seal then
		self:ShowIndexSuperDragonSealView()
	elseif index == TabIndex.dragon_king_token then
		self:ShowIndexDragonKingTokenView()
		bundle, asset = ResPath.GetRawImagesPNG("a3_lszl_bg_1")
	end

    if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function LongXiView:OnFlush(param_t, index)
	if index == TabIndex.super_dragon_seal  then
        self:FlushSuperDragonSealView()
	elseif index == TabIndex.dragon_king_token then
		self:FlushDragonKingTokenView()
	end
end