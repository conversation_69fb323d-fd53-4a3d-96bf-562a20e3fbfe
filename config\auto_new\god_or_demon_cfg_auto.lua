-- Y-一念神魔.xls
local item_table={
[1]={item_id=26529,num=1,is_bind=1},
[2]={item_id=26194,num=1,is_bind=1},
[3]={item_id=26458,num=1,is_bind=1},
[4]={item_id=26464,num=3,is_bind=1},
[5]={item_id=57801,num=1,is_bind=1},
[6]={item_id=38439,num=1,is_bind=1},
[7]={item_id=38438,num=1,is_bind=1},
[8]={item_id=26464,num=5,is_bind=1},
[9]={item_id=57802,num=1,is_bind=1},
[10]={item_id=38437,num=1,is_bind=1},
[11]={item_id=38442,num=1,is_bind=1},
[12]={item_id=38441,num=1,is_bind=1},
[13]={item_id=38440,num=1,is_bind=1},
[14]={item_id=38419,num=1,is_bind=1},
[15]={item_id=37731,num=1,is_bind=1},
[16]={item_id=37631,num=1,is_bind=1},
[17]={item_id=37224,num=1,is_bind=1},
[18]={item_id=37476,num=1,is_bind=1},
[19]={item_id=38148,num=1,is_bind=1},
[20]={item_id=30496,num=1,is_bind=1},
[21]={item_id=57320,num=1,is_bind=1},
[22]={item_id=57321,num=1,is_bind=1},
[23]={item_id=57322,num=1,is_bind=1},
[24]={item_id=57323,num=1,is_bind=1},
[25]={item_id=57324,num=1,is_bind=1},
[26]={item_id=37095,num=1,is_bind=1},
[27]={item_id=37796,num=1,is_bind=1},
[28]={item_id=37633,num=1,is_bind=1},
[29]={item_id=37291,num=1,is_bind=1},
[30]={item_id=37477,num=1,is_bind=1},
[31]={item_id=38153,num=1,is_bind=1},
[32]={item_id=37532,num=1,is_bind=1},
[33]={item_id=38765,num=1,is_bind=1},
[34]={item_id=26455,num=1,is_bind=1},
[35]={item_id=38790,num=1,is_bind=1},
[36]={item_id=38791,num=1,is_bind=1},
[37]={item_id=57800,num=1,is_bind=1},
[38]={item_id=57305,num=1,is_bind=1},
[39]={item_id=57356,num=1,is_bind=1},
[40]={item_id=26194,num=2,is_bind=1},
[41]={item_id=26455,num=2,is_bind=1},
[42]={item_id=38794,num=1,is_bind=1},
[43]={item_id=38800,num=1,is_bind=1},
[44]={item_id=57803,num=1,is_bind=1},
[45]={item_id=57306,num=1,is_bind=1},
[46]={item_id=57357,num=1,is_bind=1},
[47]={item_id=26194,num=5,is_bind=1},
[48]={item_id=26461,num=1,is_bind=1},
[49]={item_id=38793,num=1,is_bind=1},
[50]={item_id=38799,num=1,is_bind=1},
[51]={item_id=57804,num=1,is_bind=1},
[52]={item_id=57307,num=1,is_bind=1},
[53]={item_id=57358,num=1,is_bind=1},
[54]={item_id=26194,num=10,is_bind=1},
[55]={item_id=26460,num=2,is_bind=1},
[56]={item_id=38792,num=1,is_bind=1},
[57]={item_id=38798,num=1,is_bind=1},
[58]={item_id=57805,num=1,is_bind=1},
[59]={item_id=57308,num=1,is_bind=1},
[60]={item_id=57365,num=1,is_bind=1},
[61]={item_id=26194,num=20,is_bind=1},
[62]={item_id=26459,num=5,is_bind=1},
[63]={item_id=38797,num=1,is_bind=1},
[64]={item_id=38032,num=1,is_bind=1},
[65]={item_id=57811,num=2,is_bind=1},
[66]={item_id=57309,num=1,is_bind=1},
[67]={item_id=57366,num=1,is_bind=1},
[68]={item_id=26194,num=30,is_bind=1},
[69]={item_id=26463,num=5,is_bind=1},
[70]={item_id=38796,num=1,is_bind=1},
[71]={item_id=38031,num=1,is_bind=1},
[72]={item_id=57817,num=3,is_bind=1},
[73]={item_id=28106,num=1,is_bind=1},
[74]={item_id=57367,num=1,is_bind=1},
[75]={item_id=37297,num=2,is_bind=1},
[76]={item_id=38795,num=2,is_bind=1},
[77]={item_id=38030,num=3,is_bind=1},
[78]={item_id=57823,num=4,is_bind=1},
[79]={item_id=28107,num=1,is_bind=1},
[80]={item_id=57368,num=3,is_bind=1},
[81]={item_id=37950,num=2,is_bind=1},
[82]={item_id=26553,num=5,is_bind=1},
[83]={item_id=37872,num=2,is_bind=1},
[84]={item_id=37864,num=2,is_bind=1},
[85]={item_id=57829,num=5,is_bind=1},
[86]={item_id=28108,num=1,is_bind=1},
[87]={item_id=57369,num=3,is_bind=1},
[88]={item_id=37300,num=3,is_bind=1},
[89]={item_id=26549,num=7,is_bind=1},
[90]={item_id=37689,num=2,is_bind=1},
[91]={item_id=37680,num=2,is_bind=1},
[92]={item_id=57835,num=6,is_bind=1},
[93]={item_id=28109,num=1,is_bind=1},
[94]={item_id=57370,num=3,is_bind=1},
[95]={item_id=37299,num=5,is_bind=1},
[96]={item_id=26551,num=7,is_bind=1},
[97]={item_id=37977,num=2,is_bind=1},
[98]={item_id=37556,num=2,is_bind=1},
[99]={item_id=57817,num=7,is_bind=1},
[100]={item_id=28110,num=1,is_bind=1},
[101]={item_id=23353,num=1,is_bind=1},
[102]={item_id=37953,num=5,is_bind=1},
[103]={item_id=37966,num=2,is_bind=1},
[104]={item_id=38177,num=2,is_bind=1},
[105]={item_id=37492,num=2,is_bind=1},
[106]={item_id=57829,num=10,is_bind=1},
[107]={item_id=38074,num=2,is_bind=1},
[108]={item_id=23354,num=1,is_bind=1},
[109]={item_id=37952,num=10,is_bind=1},
[110]={item_id=26547,num=10,is_bind=1},
[111]={item_id=37097,num=2,is_bind=1},
[112]={item_id=38172,num=2,is_bind=1},
[113]={item_id=38037,num=2,is_bind=1},
[114]={item_id=37039,num=2,is_bind=1},
[115]={item_id=37096,num=1,is_bind=1},
[116]={item_id=36998,num=1,is_bind=1},
[117]={item_id=36999,num=1,is_bind=1},
[118]={item_id=46588,num=100,is_bind=1},
[119]={item_id=26539,num=1,is_bind=1},
[120]={item_id=26457,num=1,is_bind=1},
[121]={item_id=26464,num=1,is_bind=1},
[122]={item_id=38436,num=1,is_bind=1},
[123]={item_id=26193,num=2,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
type={
[1]={type=1,},
[2]={type=2,rmb_seq=2,choose_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},}
},

type_meta_table_map={
},
rmb_buy={
[1]={level=1,},
[2]={level=2,name="铂金",rmb_seq=2,rmb_price=328,exp_add_per=50000,bless_add_per=50000,rmb_reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7]},},
[3]={level=3,name="钻石",rmb_seq=3,rmb_price=648,exp_add_per=100000,bless_add_per=100000,rmb_reward_item={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13]},},
[4]={level=4,name="星耀",rmb_seq=4,rmb_price=2888,exp_add_per=300000,bless_add_per=300000,rmb_reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18],[5]=item_table[19]},},
[5]={level=5,name="王者",rmb_seq=5,rmb_price=5888,exp_add_per=500000,bless_add_per=500000,rmb_reward_item={[0]=item_table[20],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
[6]={level=6,name="最强王者",rmb_seq=6,rmb_price=8888,exp_add_per=1000000,bless_add_per=1000000,rmb_reward_item={[0]=item_table[26],[1]=item_table[27],[2]=item_table[28],[3]=item_table[29],[4]=item_table[30],[5]=item_table[31],[6]=item_table[32],[7]=item_table[33]},}
},

rmb_buy_meta_table_map={
},
level={
{},
{stage=1,need_exp=1500,reward_item={[0]=item_table[34]},},
{stage=2,need_exp=3000,reward_item={[0]=item_table[35]},},
{stage=3,need_exp=3500,reward_item={[0]=item_table[36]},},
{stage=4,need_exp=5000,reward_item={[0]=item_table[37]},},
{stage=5,need_exp=7500,reward_item={[0]=item_table[38]},},
{stage=6,need_exp=10000,reward_item={[0]=item_table[39]},},
{level=2,need_exp=11000,reward_item={[0]=item_table[40]},},
{level=2,stage=1,need_exp=13000,reward_item={[0]=item_table[41]},},
{level=2,stage=2,need_exp=16000,reward_item={[0]=item_table[42]},},
{level=2,stage=3,need_exp=17000,reward_item={[0]=item_table[43]},},
{level=2,stage=4,need_exp=20000,reward_item={[0]=item_table[44]},},
{level=2,stage=5,need_exp=25000,reward_item={[0]=item_table[45]},},
{level=2,stage=6,need_exp=30000,reward_item={[0]=item_table[46]},},
{level=3,need_exp=32000,reward_item={[0]=item_table[47]},},
{level=3,stage=1,need_exp=36000,reward_item={[0]=item_table[48]},},
{level=3,stage=2,need_exp=42000,reward_item={[0]=item_table[49]},},
{level=3,stage=3,need_exp=44000,reward_item={[0]=item_table[50]},},
{level=3,stage=4,need_exp=50000,reward_item={[0]=item_table[51]},},
{level=3,stage=5,need_exp=60000,reward_item={[0]=item_table[52]},},
{level=3,stage=6,need_exp=70000,reward_item={[0]=item_table[53]},},
{level=4,need_exp=74000,reward_item={[0]=item_table[54]},},
{level=4,stage=1,need_exp=82000,reward_item={[0]=item_table[55]},},
{level=4,stage=2,need_exp=94000,reward_item={[0]=item_table[56]},},
{level=4,stage=3,need_exp=98000,reward_item={[0]=item_table[57]},},
{level=4,stage=4,need_exp=110000,reward_item={[0]=item_table[58]},},
{level=4,stage=5,need_exp=130000,reward_item={[0]=item_table[59]},},
{level=4,stage=6,need_exp=150000,reward_item={[0]=item_table[60]},},
{level=5,need_exp=162500,reward_item={[0]=item_table[61]},},
{level=5,stage=1,need_exp=187500,reward_item={[0]=item_table[62]},},
{level=5,stage=2,need_exp=225000,reward_item={[0]=item_table[63]},},
{level=5,stage=3,need_exp=237500,reward_item={[0]=item_table[64]},},
{level=5,stage=4,need_exp=275000,reward_item={[0]=item_table[65]},},
{level=5,stage=5,need_exp=337500,reward_item={[0]=item_table[66]},},
{level=5,stage=6,need_exp=400000,reward_item={[0]=item_table[67]},},
{level=6,need_exp=420000,reward_item={[0]=item_table[68]},},
{level=6,stage=1,need_exp=460000,reward_item={[0]=item_table[69]},},
{level=6,stage=2,need_exp=520000,reward_item={[0]=item_table[70]},},
{level=6,stage=3,need_exp=540000,reward_item={[0]=item_table[71]},},
{level=6,stage=4,need_exp=600000,reward_item={[0]=item_table[72]},},
{level=6,stage=5,need_exp=700000,reward_item={[0]=item_table[73]},},
{level=6,stage=6,need_exp=800000,reward_item={[0]=item_table[74]},},
{level=7,need_exp=830000,reward_item={[0]=item_table[75]},},
{level=7,stage=1,need_exp=890000,reward_item={[0]=item_table[8]},},
{level=7,stage=2,need_exp=980000,reward_item={[0]=item_table[76]},},
{level=7,stage=3,need_exp=1010000,reward_item={[0]=item_table[77]},},
{level=7,stage=4,need_exp=1100000,reward_item={[0]=item_table[78]},},
{level=7,stage=5,need_exp=1250000,reward_item={[0]=item_table[79]},},
{level=7,stage=6,need_exp=1400000,reward_item={[0]=item_table[80]},},
{level=8,need_exp=1450000,reward_item={[0]=item_table[81]},},
{level=8,stage=1,need_exp=1550000,reward_item={[0]=item_table[82]},},
{level=8,stage=2,need_exp=1700000,reward_item={[0]=item_table[83]},},
{level=8,stage=3,need_exp=1750000,reward_item={[0]=item_table[84]},},
{level=8,stage=4,need_exp=1900000,reward_item={[0]=item_table[85]},},
{level=8,stage=5,need_exp=2150000,reward_item={[0]=item_table[86]},},
{level=8,stage=6,need_exp=2400000,reward_item={[0]=item_table[87]},},
{level=9,need_exp=2550000,reward_item={[0]=item_table[88]},},
{level=9,stage=1,need_exp=2850000,reward_item={[0]=item_table[89]},},
{level=9,stage=2,need_exp=3300000,reward_item={[0]=item_table[90]},},
{level=9,stage=3,need_exp=3450000,reward_item={[0]=item_table[91]},},
{level=9,stage=4,need_exp=3900000,reward_item={[0]=item_table[92]},},
{level=9,stage=5,need_exp=4650000,reward_item={[0]=item_table[93]},},
{level=9,stage=6,need_exp=5400000,reward_item={[0]=item_table[94]},},
{level=10,need_exp=5700000,reward_item={[0]=item_table[95]},},
{level=10,stage=1,need_exp=6300000,reward_item={[0]=item_table[96]},},
{level=10,stage=2,need_exp=7200000,reward_item={[0]=item_table[97]},},
{level=10,stage=3,need_exp=7500000,reward_item={[0]=item_table[98]},},
{level=10,stage=4,need_exp=8400000,reward_item={[0]=item_table[99]},},
{level=10,stage=5,need_exp=9900000,reward_item={[0]=item_table[100]},},
{level=10,stage=6,need_exp=11400000,reward_item={[0]=item_table[101]},},
{level=11,need_exp=12400000,reward_item={[0]=item_table[102]},},
{level=11,stage=1,need_exp=14400000,reward_item={[0]=item_table[103]},},
{level=11,stage=2,need_exp=17400000,reward_item={[0]=item_table[104]},},
{level=11,stage=3,need_exp=18400000,reward_item={[0]=item_table[105]},},
{level=11,stage=4,need_exp=21400000,reward_item={[0]=item_table[106]},},
{level=11,stage=5,need_exp=26400000,reward_item={[0]=item_table[107]},},
{level=11,stage=6,need_exp=31400000,reward_item={[0]=item_table[108]},},
{level=12,need_exp=33900000,reward_item={[0]=item_table[109]},},
{level=12,stage=1,need_exp=38900000,reward_item={[0]=item_table[110]},},
{level=12,stage=2,need_exp=46400000,reward_item={[0]=item_table[111]},},
{level=12,stage=3,need_exp=48900000,reward_item={[0]=item_table[112]},},
{level=12,stage=4,need_exp=56400000,reward_item={[0]=item_table[113]},},
{level=12,stage=5,need_exp=68900000,reward_item={[0]=item_table[114]},},
{level=12,stage=6,need_exp=81400000,reward_item={[0]=item_table[115]},},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,}
},

level_meta_table_map={
[134]=50,	-- depth:1
[127]=43,	-- depth:1
[148]=64,	-- depth:1
[120]=36,	-- depth:1
[113]=29,	-- depth:1
[106]=22,	-- depth:1
[99]=15,	-- depth:1
[92]=8,	-- depth:1
[91]=7,	-- depth:1
[155]=71,	-- depth:1
[90]=6,	-- depth:1
[89]=5,	-- depth:1
[88]=4,	-- depth:1
[87]=3,	-- depth:1
[86]=2,	-- depth:1
[162]=78,	-- depth:1
[141]=57,	-- depth:1
[139]=55,	-- depth:1
[140]=56,	-- depth:1
[137]=53,	-- depth:1
[142]=58,	-- depth:1
[136]=52,	-- depth:1
[135]=51,	-- depth:1
[143]=59,	-- depth:1
[144]=60,	-- depth:1
[145]=61,	-- depth:1
[146]=62,	-- depth:1
[147]=63,	-- depth:1
[138]=54,	-- depth:1
[163]=79,	-- depth:1
[150]=66,	-- depth:1
[151]=67,	-- depth:1
[152]=68,	-- depth:1
[153]=69,	-- depth:1
[154]=70,	-- depth:1
[156]=72,	-- depth:1
[157]=73,	-- depth:1
[158]=74,	-- depth:1
[159]=75,	-- depth:1
[160]=76,	-- depth:1
[161]=77,	-- depth:1
[164]=80,	-- depth:1
[165]=81,	-- depth:1
[166]=82,	-- depth:1
[149]=65,	-- depth:1
[133]=49,	-- depth:1
[114]=30,	-- depth:1
[131]=47,	-- depth:1
[107]=23,	-- depth:1
[105]=21,	-- depth:1
[104]=20,	-- depth:1
[103]=19,	-- depth:1
[102]=18,	-- depth:1
[101]=17,	-- depth:1
[108]=24,	-- depth:1
[100]=16,	-- depth:1
[97]=13,	-- depth:1
[96]=12,	-- depth:1
[95]=11,	-- depth:1
[94]=10,	-- depth:1
[93]=9,	-- depth:1
[167]=83,	-- depth:1
[98]=14,	-- depth:1
[109]=25,	-- depth:1
[110]=26,	-- depth:1
[111]=27,	-- depth:1
[130]=46,	-- depth:1
[129]=45,	-- depth:1
[128]=44,	-- depth:1
[126]=42,	-- depth:1
[125]=41,	-- depth:1
[124]=40,	-- depth:1
[123]=39,	-- depth:1
[122]=38,	-- depth:1
[121]=37,	-- depth:1
[119]=35,	-- depth:1
[118]=34,	-- depth:1
[117]=33,	-- depth:1
[116]=32,	-- depth:1
[115]=31,	-- depth:1
[112]=28,	-- depth:1
[132]=48,	-- depth:1
[168]=84,	-- depth:1
},
grade={
{modle_type=1,},
{grade=1,need_bless=120,modle_type=1,attr_value1=64,attr_value2=635,attr_value3=32,attr_value4=21,},
{grade=2,need_bless=140,modle_type=1,attr_value1=134,attr_value2=1341,attr_value3=67,attr_value4=45,},
{grade=3,need_bless=160,modle_type=1,attr_value1=223,attr_value2=2224,attr_value3=111,attr_value4=74,},
{grade=4,need_bless=180,modle_type=1,attr_value1=329,attr_value2=3282,attr_value3=164,attr_value4=109,},
{grade=5,need_bless=200,modle_type=1,attr_value1=451,attr_value2=4518,attr_value3=226,attr_value4=151,},
{grade=6,need_bless=240,modle_type=1,attr_value1=593,attr_value2=5929,attr_value3=296,attr_value4=198,},
{grade=7,need_bless=280,modle_type=1,attr_value1=751,attr_value2=7518,attr_value3=376,attr_value4=251,},
{grade=8,need_bless=320,modle_type=1,attr_value1=929,attr_value2=9282,attr_value3=464,attr_value4=309,},
{grade=9,need_bless=360,modle_type=1,attr_value1=1123,attr_value2=11224,attr_value3=561,attr_value4=374,},
{grade=10,need_bless=400,modle_type=1,attr_value1=1334,attr_value2=13341,attr_value3=667,attr_value4=445,},
{grade=11,need_bless=460,modle_type=1,attr_value1=1564,attr_value2=15635,attr_value3=782,attr_value4=521,},
{grade=12,need_bless=520,modle_type=1,attr_value1=1810,attr_value2=18106,attr_value3=905,attr_value4=604,},
{grade=13,need_bless=580,modle_type=1,attr_value1=2075,attr_value2=20753,attr_value3=1038,attr_value4=692,},
{grade=14,need_bless=640,modle_type=1,attr_value1=2357,attr_value2=23576,attr_value3=1179,attr_value4=786,},
{grade=15,need_bless=700,modle_type=1,attr_value1=2657,attr_value2=26576,attr_value3=1329,attr_value4=886,},
{grade=16,need_bless=780,modle_type=1,attr_value1=2975,attr_value2=29753,attr_value3=1488,attr_value4=992,},
{grade=17,need_bless=860,modle_type=1,attr_value1=3310,attr_value2=33106,attr_value3=1655,attr_value4=1104,},
{grade=18,need_bless=940,modle_type=1,attr_value1=3664,attr_value2=36635,attr_value3=1832,attr_value4=1221,},
{grade=19,need_bless=1020,modle_type=1,attr_value1=4034,attr_value2=40341,attr_value3=2017,attr_value4=1345,},
{grade=20,need_bless=1100,modle_type=1,attr_value1=4423,attr_value2=44224,attr_value3=2211,attr_value4=1474,},
{grade=21,need_bless=1200,modle_type=1,attr_value1=4829,attr_value2=48282,attr_value3=2414,attr_value4=1609,},
{grade=22,need_bless=1300,modle_type=1,attr_value1=5251,attr_value2=52518,attr_value3=2626,attr_value4=1751,},
{grade=23,need_bless=1400,modle_type=1,attr_value1=5693,attr_value2=56929,attr_value3=2846,attr_value4=1898,},
{grade=24,need_bless=1500,modle_type=1,attr_value1=6151,attr_value2=61518,attr_value3=3076,attr_value4=2051,},
{grade=25,need_bless=1600,modle_type=1,attr_value1=6629,attr_value2=66282,attr_value3=3314,attr_value4=2209,},
{grade=26,need_bless=1750,modle_type=1,attr_value1=7123,attr_value2=71224,attr_value3=3561,attr_value4=2374,},
{grade=27,need_bless=1900,modle_type=1,attr_value1=7634,attr_value2=76341,attr_value3=3817,attr_value4=2545,},
{grade=28,need_bless=2050,modle_type=1,attr_value1=8164,attr_value2=81635,attr_value3=4082,attr_value4=2721,},
{grade=29,need_bless=2200,modle_type=1,attr_value1=8710,attr_value2=87106,attr_value3=4355,attr_value4=2904,},
{grade=30,need_bless=2350,modle_type=1,attr_value1=9275,attr_value2=92753,attr_value3=4638,attr_value4=3092,},
{grade=31,need_bless=2550,modle_type=1,attr_value1=9857,attr_value2=98576,attr_value3=4929,attr_value4=3286,},
{grade=32,need_bless=2750,modle_type=1,attr_value1=10457,attr_value2=104576,attr_value3=5229,attr_value4=3486,},
{grade=33,need_bless=2950,modle_type=1,attr_value1=11075,attr_value2=110753,attr_value3=5538,attr_value4=3692,},
{grade=34,need_bless=3150,modle_type=1,attr_value1=11710,attr_value2=117106,attr_value3=5855,attr_value4=3904,},
{grade=35,need_bless=3350,modle_type=1,attr_value1=12364,attr_value2=123635,attr_value3=6182,attr_value4=4121,},
{grade=36,need_bless=3600,modle_type=1,attr_value1=13034,attr_value2=130341,attr_value3=6517,attr_value4=4345,},
{grade=37,need_bless=3850,modle_type=1,attr_value1=13723,attr_value2=137224,attr_value3=6861,attr_value4=4574,},
{grade=38,need_bless=4100,modle_type=1,attr_value1=14429,attr_value2=144282,attr_value3=7214,attr_value4=4809,},
{grade=39,need_bless=4350,modle_type=1,attr_value1=15151,attr_value2=151518,attr_value3=7576,attr_value4=5051,},
{grade=40,need_bless=4600,modle_type=1,attr_value1=15893,attr_value2=158929,attr_value3=7946,attr_value4=5298,},
{grade=41,need_bless=4900,modle_type=1,attr_value1=16651,attr_value2=166518,attr_value3=8326,attr_value4=5551,},
{grade=42,need_bless=5200,modle_type=1,attr_value1=17429,attr_value2=174282,attr_value3=8714,attr_value4=5809,},
{grade=43,need_bless=5500,modle_type=1,attr_value1=18223,attr_value2=182224,attr_value3=9111,attr_value4=6074,},
{grade=44,need_bless=5800,modle_type=1,attr_value1=19034,attr_value2=190341,attr_value3=9517,attr_value4=6345,},
{grade=45,need_bless=6100,modle_type=1,attr_value1=19864,attr_value2=198635,attr_value3=9932,attr_value4=6621,},
{grade=46,need_bless=6450,modle_type=1,attr_value1=20710,attr_value2=207106,attr_value3=10355,attr_value4=6904,},
{grade=47,need_bless=6800,modle_type=1,attr_value1=21575,attr_value2=215753,attr_value3=10788,attr_value4=7192,},
{grade=48,need_bless=7150,modle_type=1,attr_value1=22457,attr_value2=224576,attr_value3=11229,attr_value4=7486,},
{grade=49,need_bless=7500,modle_type=1,attr_value1=23357,attr_value2=233576,attr_value3=11679,attr_value4=7786,},
{grade=50,need_bless=7850,modle_type=2,attr_value1=24275,attr_value2=242753,attr_value3=12138,attr_value4=8092,},
{grade=51,need_bless=8300,modle_type=2,attr_value1=25210,attr_value2=252106,attr_value3=12605,attr_value4=8404,},
{grade=52,need_bless=8750,modle_type=2,attr_value1=26164,attr_value2=261635,attr_value3=13082,attr_value4=8721,},
{grade=53,need_bless=9200,modle_type=2,attr_value1=27134,attr_value2=271341,attr_value3=13567,attr_value4=9045,},
{grade=54,need_bless=9650,modle_type=2,attr_value1=28123,attr_value2=281224,attr_value3=14061,attr_value4=9374,},
{grade=55,need_bless=10100,modle_type=2,attr_value1=29129,attr_value2=291282,attr_value3=14564,attr_value4=9709,},
{grade=56,need_bless=10650,modle_type=2,attr_value1=30151,attr_value2=301518,attr_value3=15076,attr_value4=10051,},
{grade=57,need_bless=11200,modle_type=2,attr_value1=31193,attr_value2=311929,attr_value3=15596,attr_value4=10398,},
{grade=58,need_bless=11750,modle_type=2,attr_value1=32251,attr_value2=322518,attr_value3=16126,attr_value4=10751,},
{grade=59,need_bless=12300,modle_type=2,attr_value1=33329,attr_value2=333282,attr_value3=16664,attr_value4=11109,},
{grade=60,need_bless=12850,modle_type=2,attr_value1=34423,attr_value2=344224,attr_value3=17211,attr_value4=11474,},
{grade=61,need_bless=13500,modle_type=2,attr_value1=35534,attr_value2=355341,attr_value3=17767,attr_value4=11845,},
{grade=62,need_bless=14150,modle_type=2,attr_value1=36664,attr_value2=366635,attr_value3=18332,attr_value4=12221,},
{grade=63,need_bless=14800,modle_type=2,attr_value1=37810,attr_value2=378106,attr_value3=18905,attr_value4=12604,},
{grade=64,need_bless=15450,modle_type=2,attr_value1=38975,attr_value2=389753,attr_value3=19488,attr_value4=12992,},
{grade=65,need_bless=16100,modle_type=2,attr_value1=40157,attr_value2=401576,attr_value3=20079,attr_value4=13386,},
{grade=66,need_bless=16850,modle_type=2,attr_value1=41357,attr_value2=413576,attr_value3=20679,attr_value4=13786,},
{grade=67,need_bless=17600,modle_type=2,attr_value1=42575,attr_value2=425753,attr_value3=21288,attr_value4=14192,},
{grade=68,need_bless=18350,modle_type=2,attr_value1=43810,attr_value2=438106,attr_value3=21905,attr_value4=14604,},
{grade=69,need_bless=19100,modle_type=2,attr_value1=45064,attr_value2=450635,attr_value3=22532,attr_value4=15021,},
{grade=70,need_bless=19850,modle_type=2,attr_value1=46334,attr_value2=463341,attr_value3=23167,attr_value4=15445,},
{grade=71,need_bless=20700,modle_type=2,attr_value1=47623,attr_value2=476224,attr_value3=23811,attr_value4=15874,},
{grade=72,need_bless=21550,modle_type=2,attr_value1=48929,attr_value2=489282,attr_value3=24464,attr_value4=16309,},
{grade=73,need_bless=22400,modle_type=2,attr_value1=50251,attr_value2=502518,attr_value3=25126,attr_value4=16751,},
{grade=74,need_bless=23250,modle_type=2,attr_value1=51593,attr_value2=515929,attr_value3=25796,attr_value4=17198,},
{grade=75,need_bless=24100,modle_type=2,attr_value1=52951,attr_value2=529518,attr_value3=26476,attr_value4=17651,},
{grade=76,need_bless=25050,modle_type=2,attr_value1=54329,attr_value2=543282,attr_value3=27164,attr_value4=18109,},
{grade=77,need_bless=26000,modle_type=2,attr_value1=55723,attr_value2=557224,attr_value3=27861,attr_value4=18574,},
{grade=78,need_bless=26950,modle_type=2,attr_value1=57134,attr_value2=571341,attr_value3=28567,attr_value4=19045,},
{grade=79,need_bless=27900,modle_type=2,attr_value1=58564,attr_value2=585635,attr_value3=29282,attr_value4=19521,},
{grade=80,need_bless=28850,modle_type=2,attr_value1=60010,attr_value2=600106,attr_value3=30005,attr_value4=20004,},
{grade=81,need_bless=29900,modle_type=2,attr_value1=61475,attr_value2=614753,attr_value3=30738,attr_value4=20492,},
{grade=82,need_bless=30950,modle_type=2,attr_value1=62957,attr_value2=629576,attr_value3=31479,attr_value4=20986,},
{grade=83,need_bless=32000,modle_type=2,attr_value1=64457,attr_value2=644576,attr_value3=32229,attr_value4=21486,},
{grade=84,need_bless=33050,modle_type=2,attr_value1=65975,attr_value2=659753,attr_value3=32988,attr_value4=21992,},
{grade=85,need_bless=34100,modle_type=2,attr_value1=67510,attr_value2=675106,attr_value3=33755,attr_value4=22504,},
{grade=86,need_bless=35250,modle_type=2,attr_value1=69064,attr_value2=690635,attr_value3=34532,attr_value4=23021,},
{grade=87,need_bless=36400,modle_type=2,attr_value1=70634,attr_value2=706341,attr_value3=35317,attr_value4=23545,},
{grade=88,need_bless=37550,modle_type=2,attr_value1=72223,attr_value2=722224,attr_value3=36111,attr_value4=24074,},
{grade=89,need_bless=38700,modle_type=2,attr_value1=73829,attr_value2=738282,attr_value3=36914,attr_value4=24609,},
{grade=90,need_bless=39850,modle_type=2,attr_value1=75451,attr_value2=754518,attr_value3=37726,attr_value4=25151,},
{grade=91,need_bless=41100,modle_type=2,attr_value1=77093,attr_value2=770929,attr_value3=38546,attr_value4=25698,},
{grade=92,need_bless=42350,modle_type=2,attr_value1=78751,attr_value2=787518,attr_value3=39376,attr_value4=26251,},
{grade=93,need_bless=43600,modle_type=2,attr_value1=80429,attr_value2=804282,attr_value3=40214,attr_value4=26809,},
{grade=94,need_bless=44850,modle_type=2,attr_value1=82123,attr_value2=821224,attr_value3=41061,attr_value4=27374,},
{grade=95,need_bless=46100,modle_type=2,attr_value1=83834,attr_value2=838341,attr_value3=41917,attr_value4=27945,},
{grade=96,need_bless=47450,modle_type=2,attr_value1=85564,attr_value2=855635,attr_value3=42782,attr_value4=28521,},
{grade=97,need_bless=48800,modle_type=2,attr_value1=87310,attr_value2=873106,attr_value3=43655,attr_value4=29104,},
{grade=98,need_bless=50150,modle_type=2,attr_value1=89075,attr_value2=890753,attr_value3=44538,attr_value4=29692,},
{grade=99,need_bless=51500,modle_type=2,attr_value1=90857,attr_value2=908576,attr_value3=45429,attr_value4=30286,},
{grade=100,need_bless=52850,modle_type=3,attr_value1=92657,attr_value2=926576,attr_value3=46329,attr_value4=30886,},
{grade=101,need_bless=54400,modle_type=3,attr_value1=94475,attr_value2=944753,attr_value3=47238,attr_value4=31492,},
{grade=102,need_bless=55950,modle_type=3,attr_value1=96310,attr_value2=963106,attr_value3=48155,attr_value4=32104,},
{grade=103,need_bless=57500,modle_type=3,attr_value1=98164,attr_value2=981635,attr_value3=49082,attr_value4=32721,},
{grade=104,need_bless=59050,modle_type=3,attr_value1=100034,attr_value2=1000341,attr_value3=50017,attr_value4=33345,},
{grade=105,need_bless=60600,modle_type=3,attr_value1=101923,attr_value2=1019224,attr_value3=50961,attr_value4=33974,},
{grade=106,need_bless=62350,modle_type=3,attr_value1=103829,attr_value2=1038282,attr_value3=51914,attr_value4=34609,},
{grade=107,need_bless=64100,modle_type=3,attr_value1=105751,attr_value2=1057518,attr_value3=52876,attr_value4=35251,},
{grade=108,need_bless=65850,modle_type=3,attr_value1=107693,attr_value2=1076929,attr_value3=53846,attr_value4=35898,},
{grade=109,need_bless=67600,modle_type=3,attr_value1=109651,attr_value2=1096518,attr_value3=54826,attr_value4=36551,},
{grade=110,need_bless=69350,modle_type=3,attr_value1=111629,attr_value2=1116282,attr_value3=55814,attr_value4=37209,},
{grade=111,need_bless=71300,modle_type=3,attr_value1=113623,attr_value2=1136224,attr_value3=56811,attr_value4=37874,},
{grade=112,need_bless=73250,modle_type=3,attr_value1=115634,attr_value2=1156341,attr_value3=57817,attr_value4=38545,},
{grade=113,need_bless=75200,modle_type=3,attr_value1=117664,attr_value2=1176635,attr_value3=58832,attr_value4=39221,},
{grade=114,need_bless=77150,modle_type=3,attr_value1=119710,attr_value2=1197106,attr_value3=59855,attr_value4=39904,},
{grade=115,need_bless=79100,modle_type=3,attr_value1=121775,attr_value2=1217753,attr_value3=60888,attr_value4=40592,},
{grade=116,need_bless=81250,modle_type=3,attr_value1=123857,attr_value2=1238576,attr_value3=61929,attr_value4=41286,},
{grade=117,need_bless=83400,modle_type=3,attr_value1=125957,attr_value2=1259576,attr_value3=62979,attr_value4=41986,},
{grade=118,need_bless=85550,modle_type=3,attr_value1=128075,attr_value2=1280753,attr_value3=64038,attr_value4=42692,},
{grade=119,need_bless=87700,modle_type=3,attr_value1=130210,attr_value2=1302106,attr_value3=65105,attr_value4=43404,},
{grade=120,need_bless=89850,modle_type=3,attr_value1=132364,attr_value2=1323635,attr_value3=66182,attr_value4=44121,},
{grade=121,need_bless=92200,modle_type=3,attr_value1=134534,attr_value2=1345341,attr_value3=67267,attr_value4=44845,},
{grade=122,need_bless=94550,modle_type=3,attr_value1=136723,attr_value2=1367224,attr_value3=68361,attr_value4=45574,},
{grade=123,need_bless=96900,modle_type=3,attr_value1=138929,attr_value2=1389282,attr_value3=69464,attr_value4=46309,},
{grade=124,need_bless=99250,modle_type=3,attr_value1=141151,attr_value2=1411518,attr_value3=70576,attr_value4=47051,},
{grade=125,need_bless=101600,modle_type=3,attr_value1=143393,attr_value2=1433929,attr_value3=71696,attr_value4=47798,},
{grade=126,need_bless=104150,modle_type=3,attr_value1=145651,attr_value2=1456518,attr_value3=72826,attr_value4=48551,},
{grade=127,need_bless=106700,modle_type=3,attr_value1=147929,attr_value2=1479282,attr_value3=73964,attr_value4=49309,},
{grade=128,need_bless=109250,modle_type=3,attr_value1=150223,attr_value2=1502224,attr_value3=75111,attr_value4=50074,},
{grade=129,need_bless=111800,modle_type=3,attr_value1=152534,attr_value2=1525341,attr_value3=76267,attr_value4=50845,},
{grade=130,need_bless=114350,modle_type=3,attr_value1=154864,attr_value2=1548635,attr_value3=77432,attr_value4=51621,},
{grade=131,need_bless=117100,modle_type=3,attr_value1=157210,attr_value2=1572106,attr_value3=78605,attr_value4=52404,},
{grade=132,need_bless=119850,modle_type=3,attr_value1=159575,attr_value2=1595753,attr_value3=79788,attr_value4=53192,},
{grade=133,need_bless=122600,modle_type=3,attr_value1=161957,attr_value2=1619576,attr_value3=80979,attr_value4=53986,},
{grade=134,need_bless=125350,modle_type=3,attr_value1=164357,attr_value2=1643576,attr_value3=82179,attr_value4=54786,},
{grade=135,need_bless=128100,modle_type=3,attr_value1=166775,attr_value2=1667753,attr_value3=83388,attr_value4=55592,},
{grade=136,need_bless=131050,modle_type=3,attr_value1=169210,attr_value2=1692106,attr_value3=84605,attr_value4=56404,},
{grade=137,need_bless=134000,modle_type=3,attr_value1=171664,attr_value2=1716635,attr_value3=85832,attr_value4=57221,},
{grade=138,need_bless=136950,modle_type=3,attr_value1=174134,attr_value2=1741341,attr_value3=87067,attr_value4=58045,},
{grade=139,need_bless=139900,modle_type=3,attr_value1=176623,attr_value2=1766224,attr_value3=88311,attr_value4=58874,},
{grade=140,need_bless=142850,modle_type=3,attr_value1=179129,attr_value2=1791282,attr_value3=89564,attr_value4=59709,},
{grade=141,need_bless=146000,modle_type=3,attr_value1=181651,attr_value2=1816518,attr_value3=90826,attr_value4=60551,},
{grade=142,need_bless=149150,modle_type=3,attr_value1=184193,attr_value2=1841929,attr_value3=92096,attr_value4=61398,},
{grade=143,need_bless=152300,modle_type=3,attr_value1=186751,attr_value2=1867518,attr_value3=93376,attr_value4=62251,},
{grade=144,need_bless=155450,modle_type=3,attr_value1=189329,attr_value2=1893282,attr_value3=94664,attr_value4=63109,},
{grade=145,need_bless=158600,modle_type=3,attr_value1=191923,attr_value2=1919224,attr_value3=95961,attr_value4=63974,},
{grade=146,need_bless=161950,modle_type=3,attr_value1=194534,attr_value2=1945341,attr_value3=97267,attr_value4=64845,},
{grade=147,need_bless=165300,modle_type=3,attr_value1=197164,attr_value2=1971635,attr_value3=98582,attr_value4=65721,},
{grade=148,need_bless=168650,modle_type=3,attr_value1=199810,attr_value2=1998106,attr_value3=99905,attr_value4=66604,},
{grade=149,need_bless=172000,modle_type=3,attr_value1=202475,attr_value2=2024753,attr_value3=101238,attr_value4=67492,},
{grade=150,need_bless=175350,attr_value1=205157,attr_value2=2051576,attr_value3=102579,attr_value4=68386,},
{grade=151,need_bless=178900,attr_value1=207857,attr_value2=2078576,attr_value3=103929,attr_value4=69286,},
{grade=152,need_bless=182450,attr_value1=210575,attr_value2=2105753,attr_value3=105288,attr_value4=70192,},
{grade=153,need_bless=186000,attr_value1=213310,attr_value2=2133106,attr_value3=106655,attr_value4=71104,},
{grade=154,need_bless=189550,attr_value1=216064,attr_value2=2160635,attr_value3=108032,attr_value4=72021,},
{grade=155,need_bless=193100,attr_value1=218834,attr_value2=2188341,attr_value3=109417,attr_value4=72945,},
{grade=156,need_bless=196850,attr_value1=221623,attr_value2=2216224,attr_value3=110811,attr_value4=73874,},
{grade=157,need_bless=200600,attr_value1=224429,attr_value2=2244282,attr_value3=112214,attr_value4=74809,},
{grade=158,need_bless=204350,attr_value1=227251,attr_value2=2272518,attr_value3=113626,attr_value4=75751,},
{grade=159,need_bless=208100,attr_value1=230093,attr_value2=2300929,attr_value3=115046,attr_value4=76698,},
{grade=160,need_bless=211850,attr_value1=232951,attr_value2=2329518,attr_value3=116476,attr_value4=77651,},
{grade=161,need_bless=215800,attr_value1=235829,attr_value2=2358282,attr_value3=117914,attr_value4=78609,},
{grade=162,need_bless=219750,attr_value1=238723,attr_value2=2387224,attr_value3=119361,attr_value4=79574,},
{grade=163,need_bless=223700,attr_value1=241634,attr_value2=2416341,attr_value3=120817,attr_value4=80545,},
{grade=164,need_bless=227650,attr_value1=244564,attr_value2=2445635,attr_value3=122282,attr_value4=81521,},
{grade=165,need_bless=231600,attr_value1=247510,attr_value2=2475106,attr_value3=123755,attr_value4=82504,},
{grade=166,need_bless=235750,attr_value1=250475,attr_value2=2504753,attr_value3=125238,attr_value4=83492,},
{grade=167,need_bless=239900,attr_value1=253457,attr_value2=2534576,attr_value3=126729,attr_value4=84486,},
{grade=168,need_bless=244050,attr_value1=256457,attr_value2=2564576,attr_value3=128229,attr_value4=85486,},
{grade=169,need_bless=248200,attr_value1=259475,attr_value2=2594753,attr_value3=129738,attr_value4=86492,},
{grade=170,need_bless=252350,attr_value1=262510,attr_value2=2625106,attr_value3=131255,attr_value4=87504,},
{grade=171,need_bless=256700,attr_value1=265564,attr_value2=2655635,attr_value3=132782,attr_value4=88521,},
{grade=172,need_bless=261050,attr_value1=268634,attr_value2=2686341,attr_value3=134317,attr_value4=89545,},
{grade=173,need_bless=265400,attr_value1=271723,attr_value2=2717224,attr_value3=135861,attr_value4=90574,},
{grade=174,need_bless=269750,attr_value1=274829,attr_value2=2748282,attr_value3=137414,attr_value4=91609,},
{grade=175,need_bless=274100,attr_value1=277951,attr_value2=2779518,attr_value3=138976,attr_value4=92651,},
{grade=176,need_bless=278650,attr_value1=281093,attr_value2=2810929,attr_value3=140546,attr_value4=93698,},
{grade=177,need_bless=283200,attr_value1=284251,attr_value2=2842518,attr_value3=142126,attr_value4=94751,},
{grade=178,need_bless=287750,attr_value1=287429,attr_value2=2874282,attr_value3=143714,attr_value4=95809,},
{grade=179,need_bless=292300,attr_value1=290623,attr_value2=2906224,attr_value3=145311,attr_value4=96874,},
{grade=180,need_bless=296850,attr_value1=293834,attr_value2=2938341,attr_value3=146917,attr_value4=97945,},
{grade=181,need_bless=301600,attr_value1=297064,attr_value2=2970635,attr_value3=148532,attr_value4=99021,},
{grade=182,need_bless=306350,attr_value1=300310,attr_value2=3003106,attr_value3=150155,attr_value4=100104,},
{grade=183,need_bless=311100,attr_value1=303575,attr_value2=3035753,attr_value3=151788,attr_value4=101192,},
{grade=184,need_bless=315850,attr_value1=306857,attr_value2=3068576,attr_value3=153429,attr_value4=102286,},
{grade=185,need_bless=320600,attr_value1=310157,attr_value2=3101576,attr_value3=155079,attr_value4=103386,},
{grade=186,need_bless=325550,attr_value1=313475,attr_value2=3134753,attr_value3=156738,attr_value4=104492,},
{grade=187,need_bless=330500,attr_value1=316810,attr_value2=3168106,attr_value3=158405,attr_value4=105604,},
{grade=188,need_bless=335450,attr_value1=320164,attr_value2=3201635,attr_value3=160082,attr_value4=106721,},
{grade=189,need_bless=340400,attr_value1=323534,attr_value2=3235341,attr_value3=161767,attr_value4=107845,},
{grade=190,need_bless=345350,attr_value1=326923,attr_value2=3269224,attr_value3=163461,attr_value4=108974,},
{grade=191,need_bless=350500,attr_value1=330329,attr_value2=3303282,attr_value3=165164,attr_value4=110109,},
{grade=192,need_bless=355650,attr_value1=333751,attr_value2=3337518,attr_value3=166876,attr_value4=111251,},
{grade=193,need_bless=360800,attr_value1=337193,attr_value2=3371929,attr_value3=168596,attr_value4=112398,},
{grade=194,need_bless=365950,attr_value1=340651,attr_value2=3406518,attr_value3=170326,attr_value4=113551,},
{grade=195,need_bless=371100,attr_value1=344129,attr_value2=3441282,attr_value3=172064,attr_value4=114709,},
{grade=196,need_bless=376450,attr_value1=347623,attr_value2=3476224,attr_value3=173811,attr_value4=115874,},
{grade=197,need_bless=381800,attr_value1=351134,attr_value2=3511341,attr_value3=175567,attr_value4=117045,},
{grade=198,need_bless=387150,attr_value1=354664,attr_value2=3546635,attr_value3=177332,attr_value4=118221,},
{grade=199,need_bless=392500,attr_value1=358210,attr_value2=3582106,attr_value3=179105,attr_value4=119404,},
{grade=200,need_bless=397850,attr_value1=361775,attr_value2=3617753,attr_value3=180888,attr_value4=120592,},
{grade=201,need_bless=403500,attr_value1=365357,attr_value2=3653576,attr_value3=182679,attr_value4=121786,},
{grade=202,need_bless=409150,attr_value1=368957,attr_value2=3689576,attr_value3=184479,attr_value4=122986,},
{grade=203,need_bless=414800,attr_value1=372575,attr_value2=3725753,attr_value3=186288,attr_value4=124192,},
{grade=204,need_bless=420450,attr_value1=376210,attr_value2=3762106,attr_value3=188105,attr_value4=125404,},
{grade=205,need_bless=426100,attr_value1=379864,attr_value2=3798635,attr_value3=189932,attr_value4=126621,},
{grade=206,need_bless=432050,attr_value1=383534,attr_value2=3835341,attr_value3=191767,attr_value4=127845,},
{grade=207,need_bless=438000,attr_value1=387223,attr_value2=3872224,attr_value3=193611,attr_value4=129074,},
{grade=208,need_bless=443950,attr_value1=390929,attr_value2=3909282,attr_value3=195464,attr_value4=130309,},
{grade=209,need_bless=449900,attr_value1=394651,attr_value2=3946518,attr_value3=197326,attr_value4=131551,},
{grade=210,need_bless=455850,attr_value1=398393,attr_value2=3983929,attr_value3=199196,attr_value4=132798,},
{grade=211,need_bless=462100,attr_value1=402151,attr_value2=4021518,attr_value3=201076,attr_value4=134051,},
{grade=212,need_bless=468350,attr_value1=405929,attr_value2=4059282,attr_value3=202964,attr_value4=135309,},
{grade=213,need_bless=474600,attr_value1=409723,attr_value2=4097224,attr_value3=204861,attr_value4=136574,},
{grade=214,need_bless=480850,attr_value1=413534,attr_value2=4135341,attr_value3=206767,attr_value4=137845,},
{grade=215,need_bless=487100,attr_value1=417364,attr_value2=4173635,attr_value3=208682,attr_value4=139121,},
{grade=216,need_bless=493650,attr_value1=421210,attr_value2=4212106,attr_value3=210605,attr_value4=140404,},
{grade=217,need_bless=500200,attr_value1=425075,attr_value2=4250753,attr_value3=212538,attr_value4=141692,},
{grade=218,need_bless=506750,attr_value1=428957,attr_value2=4289576,attr_value3=214479,attr_value4=142986,},
{grade=219,need_bless=513300,attr_value1=432857,attr_value2=4328576,attr_value3=216429,attr_value4=144286,},
{grade=220,need_bless=519850,attr_value1=436775,attr_value2=4367753,attr_value3=218388,attr_value4=145592,},
{grade=221,need_bless=526700,attr_value1=440710,attr_value2=4407106,attr_value3=220355,attr_value4=146904,},
{grade=222,need_bless=533550,attr_value1=444664,attr_value2=4446635,attr_value3=222332,attr_value4=148221,},
{grade=223,need_bless=540400,attr_value1=448634,attr_value2=4486341,attr_value3=224317,attr_value4=149545,},
{grade=224,need_bless=547250,attr_value1=452623,attr_value2=4526224,attr_value3=226311,attr_value4=150874,},
{grade=225,need_bless=554100,attr_value1=456629,attr_value2=4566282,attr_value3=228314,attr_value4=152209,},
{grade=226,need_bless=561250,attr_value1=460651,attr_value2=4606518,attr_value3=230326,attr_value4=153551,},
{grade=227,need_bless=568400,attr_value1=464693,attr_value2=4646929,attr_value3=232346,attr_value4=154898,},
{grade=228,need_bless=575550,attr_value1=468751,attr_value2=4687518,attr_value3=234376,attr_value4=156251,},
{grade=229,need_bless=582700,attr_value1=472829,attr_value2=4728282,attr_value3=236414,attr_value4=157609,},
{grade=230,need_bless=589850,attr_value1=476923,attr_value2=4769224,attr_value3=238461,attr_value4=158974,},
{grade=231,need_bless=597300,attr_value1=481034,attr_value2=4810341,attr_value3=240517,attr_value4=160345,},
{grade=232,need_bless=604750,attr_value1=485164,attr_value2=4851635,attr_value3=242582,attr_value4=161721,},
{grade=233,need_bless=612200,attr_value1=489310,attr_value2=4893106,attr_value3=244655,attr_value4=163104,},
{grade=234,need_bless=619650,attr_value1=493475,attr_value2=4934753,attr_value3=246738,attr_value4=164492,},
{grade=235,need_bless=627100,attr_value1=497657,attr_value2=4976576,attr_value3=248829,attr_value4=165886,},
{grade=236,need_bless=634850,attr_value1=501857,attr_value2=5018576,attr_value3=250929,attr_value4=167286,},
{grade=237,need_bless=642600,attr_value1=506075,attr_value2=5060753,attr_value3=253038,attr_value4=168692,},
{grade=238,need_bless=650350,attr_value1=510310,attr_value2=5103106,attr_value3=255155,attr_value4=170104,},
{grade=239,need_bless=658100,attr_value1=514564,attr_value2=5145635,attr_value3=257282,attr_value4=171521,},
{grade=240,need_bless=665850,attr_value1=518834,attr_value2=5188341,attr_value3=259417,attr_value4=172945,},
{grade=241,need_bless=673900,attr_value1=523123,attr_value2=5231224,attr_value3=261561,attr_value4=174374,},
{grade=242,need_bless=681950,attr_value1=527429,attr_value2=5274282,attr_value3=263714,attr_value4=175809,},
{grade=243,need_bless=690000,attr_value1=531751,attr_value2=5317518,attr_value3=265876,attr_value4=177251,},
{grade=244,need_bless=698050,attr_value1=536093,attr_value2=5360929,attr_value3=268046,attr_value4=178698,},
{grade=245,need_bless=706100,attr_value1=540451,attr_value2=5404518,attr_value3=270226,attr_value4=180151,},
{grade=246,need_bless=714450,attr_value1=544829,attr_value2=5448282,attr_value3=272414,attr_value4=181609,},
{grade=247,need_bless=722800,attr_value1=549223,attr_value2=5492224,attr_value3=274611,attr_value4=183074,},
{grade=248,need_bless=731150,attr_value1=553634,attr_value2=5536341,attr_value3=276817,attr_value4=184545,},
{grade=249,need_bless=739500,attr_value1=558064,attr_value2=5580635,attr_value3=279032,attr_value4=186021,},
{grade=250,need_bless=748050,attr_value1=562510,attr_value2=5625106,attr_value3=281255,attr_value4=187504,},
{grade=251,need_bless=756900,attr_value1=566975,attr_value2=5669753,attr_value3=283488,attr_value4=188992,},
{grade=252,need_bless=765750,attr_value1=571457,attr_value2=5714576,attr_value3=285729,attr_value4=190486,},
{grade=253,need_bless=774600,attr_value1=575957,attr_value2=5759576,attr_value3=287979,attr_value4=191986,},
{grade=254,need_bless=783450,attr_value1=580475,attr_value2=5804753,attr_value3=290238,attr_value4=193492,},
{grade=255,need_bless=792500,attr_value1=585010,attr_value2=5850106,attr_value3=292505,attr_value4=195004,},
{grade=256,need_bless=801850,attr_value1=589564,attr_value2=5895635,attr_value3=294782,attr_value4=196521,},
{grade=257,need_bless=811200,attr_value1=594134,attr_value2=5941341,attr_value3=297067,attr_value4=198045,},
{grade=258,need_bless=820550,attr_value1=598723,attr_value2=5987224,attr_value3=299361,attr_value4=199574,},
{grade=259,need_bless=829900,attr_value1=603329,attr_value2=6033282,attr_value3=301664,attr_value4=201109,},
{grade=260,need_bless=839450,attr_value1=607951,attr_value2=6079518,attr_value3=303976,attr_value4=202651,},
{grade=261,need_bless=849300,attr_value1=612593,attr_value2=6125929,attr_value3=306296,attr_value4=204198,},
{grade=262,need_bless=859150,attr_value1=617251,attr_value2=6172518,attr_value3=308626,attr_value4=205751,},
{grade=263,need_bless=869000,attr_value1=621929,attr_value2=6219282,attr_value3=310964,attr_value4=207309,},
{grade=264,need_bless=878850,attr_value1=626623,attr_value2=6266224,attr_value3=313311,attr_value4=208874,},
{grade=265,need_bless=888900,attr_value1=631334,attr_value2=6313341,attr_value3=315667,attr_value4=210445,},
{grade=266,need_bless=899250,attr_value1=636064,attr_value2=6360635,attr_value3=318032,attr_value4=212021,},
{grade=267,need_bless=909600,attr_value1=640810,attr_value2=6408106,attr_value3=320405,attr_value4=213604,},
{grade=268,need_bless=919950,attr_value1=645575,attr_value2=6455753,attr_value3=322788,attr_value4=215192,},
{grade=269,need_bless=930300,attr_value1=650357,attr_value2=6503576,attr_value3=325179,attr_value4=216786,},
{grade=270,need_bless=940850,attr_value1=655157,attr_value2=6551576,attr_value3=327579,attr_value4=218386,},
{grade=271,need_bless=951700,attr_value1=659975,attr_value2=6599753,attr_value3=329988,attr_value4=219992,},
{grade=272,need_bless=962550,attr_value1=664810,attr_value2=6648106,attr_value3=332405,attr_value4=221604,},
{grade=273,need_bless=973400,attr_value1=669664,attr_value2=6696635,attr_value3=334832,attr_value4=223221,},
{grade=274,need_bless=984250,attr_value1=674534,attr_value2=6745341,attr_value3=337267,attr_value4=224845,},
{grade=275,need_bless=995300,attr_value1=679423,attr_value2=6794224,attr_value3=339711,attr_value4=226474,},
{grade=276,need_bless=1006650,attr_value1=684329,attr_value2=6843282,attr_value3=342164,attr_value4=228109,},
{grade=277,need_bless=1018000,attr_value1=689251,attr_value2=6892518,attr_value3=344626,attr_value4=229751,},
{grade=278,need_bless=1029350,attr_value1=694193,attr_value2=6941929,attr_value3=347096,attr_value4=231398,},
{grade=279,need_bless=1040700,attr_value1=699151,attr_value2=6991518,attr_value3=349576,attr_value4=233051,},
{grade=280,need_bless=1052250,attr_value1=704129,attr_value2=7041282,attr_value3=352064,attr_value4=234709,},
{grade=281,need_bless=1064100,attr_value1=709123,attr_value2=7091224,attr_value3=354561,attr_value4=236374,},
{grade=282,need_bless=1075950,attr_value1=714134,attr_value2=7141341,attr_value3=357067,attr_value4=238045,},
{grade=283,need_bless=1087800,attr_value1=719164,attr_value2=7191635,attr_value3=359582,attr_value4=239721,},
{grade=284,need_bless=1099650,attr_value1=724210,attr_value2=7242106,attr_value3=362105,attr_value4=241404,},
{grade=285,need_bless=1111700,attr_value1=729275,attr_value2=7292753,attr_value3=364638,attr_value4=243092,},
{grade=286,need_bless=1124050,attr_value1=734357,attr_value2=7343576,attr_value3=367179,attr_value4=244786,},
{grade=287,need_bless=1136400,attr_value1=739457,attr_value2=7394576,attr_value3=369729,attr_value4=246486,},
{grade=288,need_bless=1148750,attr_value1=744575,attr_value2=7445753,attr_value3=372288,attr_value4=248192,},
{grade=289,need_bless=1161100,attr_value1=749710,attr_value2=7497106,attr_value3=374855,attr_value4=249904,},
{grade=290,need_bless=1173650,attr_value1=754864,attr_value2=7548635,attr_value3=377432,attr_value4=251621,},
{grade=291,need_bless=1186500,attr_value1=760034,attr_value2=7600341,attr_value3=380017,attr_value4=253345,},
{grade=292,need_bless=1199350,attr_value1=765223,attr_value2=7652224,attr_value3=382611,attr_value4=255074,},
{grade=293,need_bless=1212200,attr_value1=770429,attr_value2=7704282,attr_value3=385214,attr_value4=256809,},
{grade=294,need_bless=1225050,attr_value1=775651,attr_value2=7756518,attr_value3=387826,attr_value4=258551,},
{grade=295,need_bless=1238100,attr_value1=780893,attr_value2=7808929,attr_value3=390446,attr_value4=260298,},
{grade=296,need_bless=1251450,attr_value1=786151,attr_value2=7861518,attr_value3=393076,attr_value4=262051,},
{grade=297,need_bless=1264800,attr_value1=791429,attr_value2=7914282,attr_value3=395714,attr_value4=263809,},
{grade=298,need_bless=1278150,attr_value1=796723,attr_value2=7967224,attr_value3=398361,attr_value4=265574,},
{grade=299,need_bless=1291500,attr_value1=802034,attr_value2=8020341,attr_value3=401017,attr_value4=267345,},
{grade=300,need_bless=1305050,attr_value1=807364,attr_value2=8073635,attr_value3=403682,attr_value4=269121,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,},
{type=2,}
},

grade_meta_table_map={
[302]=1,	-- depth:1
[505]=204,	-- depth:1
[504]=203,	-- depth:1
[503]=202,	-- depth:1
[502]=201,	-- depth:1
[501]=200,	-- depth:1
[500]=199,	-- depth:1
[499]=198,	-- depth:1
[498]=197,	-- depth:1
[497]=196,	-- depth:1
[496]=195,	-- depth:1
[495]=194,	-- depth:1
[494]=193,	-- depth:1
[493]=192,	-- depth:1
[492]=191,	-- depth:1
[491]=190,	-- depth:1
[506]=205,	-- depth:1
[490]=189,	-- depth:1
[507]=206,	-- depth:1
[509]=208,	-- depth:1
[524]=223,	-- depth:1
[523]=222,	-- depth:1
[522]=221,	-- depth:1
[521]=220,	-- depth:1
[520]=219,	-- depth:1
[519]=218,	-- depth:1
[518]=217,	-- depth:1
[517]=216,	-- depth:1
[516]=215,	-- depth:1
[515]=214,	-- depth:1
[514]=213,	-- depth:1
[513]=212,	-- depth:1
[512]=211,	-- depth:1
[511]=210,	-- depth:1
[510]=209,	-- depth:1
[508]=207,	-- depth:1
[525]=224,	-- depth:1
[489]=188,	-- depth:1
[487]=186,	-- depth:1
[467]=166,	-- depth:1
[466]=165,	-- depth:1
[465]=164,	-- depth:1
[464]=163,	-- depth:1
[463]=162,	-- depth:1
[462]=161,	-- depth:1
[461]=160,	-- depth:1
[460]=159,	-- depth:1
[459]=158,	-- depth:1
[458]=157,	-- depth:1
[457]=156,	-- depth:1
[456]=155,	-- depth:1
[455]=154,	-- depth:1
[454]=153,	-- depth:1
[453]=152,	-- depth:1
[468]=167,	-- depth:1
[488]=187,	-- depth:1
[469]=168,	-- depth:1
[471]=170,	-- depth:1
[486]=185,	-- depth:1
[485]=184,	-- depth:1
[484]=183,	-- depth:1
[483]=182,	-- depth:1
[482]=181,	-- depth:1
[481]=180,	-- depth:1
[480]=179,	-- depth:1
[479]=178,	-- depth:1
[478]=177,	-- depth:1
[477]=176,	-- depth:1
[476]=175,	-- depth:1
[475]=174,	-- depth:1
[474]=173,	-- depth:1
[473]=172,	-- depth:1
[472]=171,	-- depth:1
[470]=169,	-- depth:1
[452]=151,	-- depth:1
[526]=225,	-- depth:1
[528]=227,	-- depth:1
[581]=280,	-- depth:1
[580]=279,	-- depth:1
[579]=278,	-- depth:1
[578]=277,	-- depth:1
[577]=276,	-- depth:1
[576]=275,	-- depth:1
[575]=274,	-- depth:1
[574]=273,	-- depth:1
[573]=272,	-- depth:1
[572]=271,	-- depth:1
[571]=270,	-- depth:1
[570]=269,	-- depth:1
[569]=268,	-- depth:1
[568]=267,	-- depth:1
[567]=266,	-- depth:1
[582]=281,	-- depth:1
[566]=265,	-- depth:1
[583]=282,	-- depth:1
[585]=284,	-- depth:1
[600]=299,	-- depth:1
[599]=298,	-- depth:1
[598]=297,	-- depth:1
[597]=296,	-- depth:1
[596]=295,	-- depth:1
[595]=294,	-- depth:1
[594]=293,	-- depth:1
[593]=292,	-- depth:1
[592]=291,	-- depth:1
[591]=290,	-- depth:1
[590]=289,	-- depth:1
[589]=288,	-- depth:1
[588]=287,	-- depth:1
[587]=286,	-- depth:1
[586]=285,	-- depth:1
[584]=283,	-- depth:1
[527]=226,	-- depth:1
[565]=264,	-- depth:1
[563]=262,	-- depth:1
[543]=242,	-- depth:1
[542]=241,	-- depth:1
[541]=240,	-- depth:1
[540]=239,	-- depth:1
[539]=238,	-- depth:1
[538]=237,	-- depth:1
[537]=236,	-- depth:1
[536]=235,	-- depth:1
[535]=234,	-- depth:1
[534]=233,	-- depth:1
[533]=232,	-- depth:1
[532]=231,	-- depth:1
[531]=230,	-- depth:1
[530]=229,	-- depth:1
[529]=228,	-- depth:1
[544]=243,	-- depth:1
[564]=263,	-- depth:1
[545]=244,	-- depth:1
[547]=246,	-- depth:1
[562]=261,	-- depth:1
[561]=260,	-- depth:1
[560]=259,	-- depth:1
[559]=258,	-- depth:1
[558]=257,	-- depth:1
[557]=256,	-- depth:1
[556]=255,	-- depth:1
[555]=254,	-- depth:1
[554]=253,	-- depth:1
[553]=252,	-- depth:1
[552]=251,	-- depth:1
[551]=250,	-- depth:1
[550]=249,	-- depth:1
[549]=248,	-- depth:1
[548]=247,	-- depth:1
[546]=245,	-- depth:1
[602]=301,	-- depth:1
[601]=300,	-- depth:1
[305]=4,	-- depth:1
[451]=150,	-- depth:1
[356]=55,	-- depth:1
[355]=54,	-- depth:1
[354]=53,	-- depth:1
[353]=52,	-- depth:1
[352]=51,	-- depth:1
[351]=50,	-- depth:1
[357]=56,	-- depth:1
[350]=49,	-- depth:1
[348]=47,	-- depth:1
[347]=46,	-- depth:1
[346]=45,	-- depth:1
[345]=44,	-- depth:1
[344]=43,	-- depth:1
[343]=42,	-- depth:1
[349]=48,	-- depth:1
[358]=57,	-- depth:1
[359]=58,	-- depth:1
[360]=59,	-- depth:1
[375]=74,	-- depth:1
[374]=73,	-- depth:1
[373]=72,	-- depth:1
[372]=71,	-- depth:1
[371]=70,	-- depth:1
[370]=69,	-- depth:1
[369]=68,	-- depth:1
[368]=67,	-- depth:1
[367]=66,	-- depth:1
[366]=65,	-- depth:1
[365]=64,	-- depth:1
[364]=63,	-- depth:1
[363]=62,	-- depth:1
[362]=61,	-- depth:1
[361]=60,	-- depth:1
[342]=41,	-- depth:1
[376]=75,	-- depth:1
[341]=40,	-- depth:1
[339]=38,	-- depth:1
[319]=18,	-- depth:1
[318]=17,	-- depth:1
[317]=16,	-- depth:1
[316]=15,	-- depth:1
[315]=14,	-- depth:1
[314]=13,	-- depth:1
[320]=19,	-- depth:1
[313]=12,	-- depth:1
[311]=10,	-- depth:1
[310]=9,	-- depth:1
[309]=8,	-- depth:1
[308]=7,	-- depth:1
[307]=6,	-- depth:1
[306]=5,	-- depth:1
[312]=11,	-- depth:1
[321]=20,	-- depth:1
[322]=21,	-- depth:1
[323]=22,	-- depth:1
[338]=37,	-- depth:1
[337]=36,	-- depth:1
[336]=35,	-- depth:1
[335]=34,	-- depth:1
[334]=33,	-- depth:1
[333]=32,	-- depth:1
[332]=31,	-- depth:1
[331]=30,	-- depth:1
[330]=29,	-- depth:1
[329]=28,	-- depth:1
[328]=27,	-- depth:1
[327]=26,	-- depth:1
[326]=25,	-- depth:1
[325]=24,	-- depth:1
[324]=23,	-- depth:1
[340]=39,	-- depth:1
[303]=2,	-- depth:1
[378]=77,	-- depth:1
[379]=78,	-- depth:1
[431]=130,	-- depth:1
[430]=129,	-- depth:1
[429]=128,	-- depth:1
[428]=127,	-- depth:1
[427]=126,	-- depth:1
[426]=125,	-- depth:1
[432]=131,	-- depth:1
[425]=124,	-- depth:1
[423]=122,	-- depth:1
[422]=121,	-- depth:1
[421]=120,	-- depth:1
[420]=119,	-- depth:1
[419]=118,	-- depth:1
[418]=117,	-- depth:1
[424]=123,	-- depth:1
[433]=132,	-- depth:1
[434]=133,	-- depth:1
[435]=134,	-- depth:1
[450]=149,	-- depth:1
[449]=148,	-- depth:1
[448]=147,	-- depth:1
[447]=146,	-- depth:1
[446]=145,	-- depth:1
[445]=144,	-- depth:1
[444]=143,	-- depth:1
[443]=142,	-- depth:1
[442]=141,	-- depth:1
[441]=140,	-- depth:1
[440]=139,	-- depth:1
[439]=138,	-- depth:1
[438]=137,	-- depth:1
[437]=136,	-- depth:1
[436]=135,	-- depth:1
[417]=116,	-- depth:1
[416]=115,	-- depth:1
[415]=114,	-- depth:1
[414]=113,	-- depth:1
[394]=93,	-- depth:1
[393]=92,	-- depth:1
[392]=91,	-- depth:1
[391]=90,	-- depth:1
[390]=89,	-- depth:1
[389]=88,	-- depth:1
[388]=87,	-- depth:1
[387]=86,	-- depth:1
[386]=85,	-- depth:1
[385]=84,	-- depth:1
[384]=83,	-- depth:1
[383]=82,	-- depth:1
[382]=81,	-- depth:1
[381]=80,	-- depth:1
[380]=79,	-- depth:1
[395]=94,	-- depth:1
[304]=3,	-- depth:1
[396]=95,	-- depth:1
[398]=97,	-- depth:1
[413]=112,	-- depth:1
[412]=111,	-- depth:1
[411]=110,	-- depth:1
[410]=109,	-- depth:1
[409]=108,	-- depth:1
[408]=107,	-- depth:1
[407]=106,	-- depth:1
[406]=105,	-- depth:1
[405]=104,	-- depth:1
[404]=103,	-- depth:1
[403]=102,	-- depth:1
[402]=101,	-- depth:1
[401]=100,	-- depth:1
[400]=99,	-- depth:1
[399]=98,	-- depth:1
[397]=96,	-- depth:1
[377]=76,	-- depth:1
},
skill={
{},
{skill_id=2,need_grade=60,attr_value1=1094,attr_value2=365,attr_value3=10941,attr_value4=547,skill_name="幻纵",skill_describe="攻    击<color=#9df5a7>            +1094</color>\n破    甲<color=#9df5a7>            +10941</color>\n生    命<color=#9df5a7>            +365</color>\n防    御<color=#9df5a7>            +547</color>\n系统总加成<color=#9df5a7>          +1%</color>",skill_icon=2039,skill_open="60阶可激活",},
{skill_id=3,need_grade=100,attr_value1=1800,attr_value2=600,attr_value3=18000,attr_value4=900,attr_per=150,skill_name="幻横",skill_describe="攻    击<color=#9df5a7>            +1800</color>\n破    甲<color=#9df5a7>            +18000</color>\n生    命<color=#9df5a7>            +600</color>\n防    御<color=#9df5a7>            +900</color>\n系统总加成<color=#9df5a7>          +1.5%</color>",skill_icon=2042,skill_open="100阶可激活",},
{skill_id=4,need_grade=150,attr_value1=2683,attr_value2=894,attr_value3=26824,attr_value4=1341,attr_per=150,skill_name="纵横",skill_describe="攻    击<color=#9df5a7>            +2683</color>\n破    甲<color=#9df5a7>            +26824</color>\n生    命<color=#9df5a7>            +894</color>\n防    御<color=#9df5a7>            +1341</color>\n系统总加成<color=#9df5a7>          +1.5%</color>",skill_icon=2040,skill_open="150阶可激活",},
{type=2,},
{type=2,},
{type=2,},
{type=2,}
},

skill_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
task={
{param1=100,target=100,},
{task_id=1,param1=150,target=150,},
{task_id=2,param1=200,target=200,},
{task_id=3,task_type=2,param1=2,param2=48,des="通关日月修行",target=2,open_panel="fubenpanel#fubenpanel_exp",},
{task_id=4,param2=32,des="通关熔火之心",open_panel="fubenpanel#fubenpanel_copper",},
{task_id=5,task_type=3,des="击杀伏魔战场boss",open_panel="boss#boss_world",},
{task_id=6,task_type=4,des="击杀仙遗洞天boss",open_panel="boss#boss_vip",},
{task_id=7,task_type=5,param1=1,des="完成天道苍茫",target=1,open_panel="boss#boss_dabao",},
{task_id=8,task_type=6,des="参与谪仙之境boss击杀",open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=9,task_type=8,des="参与神秘海域boss击杀",open_panel="country_map_act_view#country_map_yanglongsi",}
},

task_meta_table_map={
[5]=4,	-- depth:1
},
title={
{},
{level=2,title="采阴明卒",},
{level=3,title="化形明使",},
{level=4,title="采灵明魉",},
{level=5,title="吞元明将",},
{level=6,title="嗜魂明帅",},
{level=7,title="夺体明仙",},
{level=8,title="开窍明王",},
{level=9,title="通明明皇",},
{level=10,title="凝型明帝",},
{level=11,title="成形明尊",},
{level=12,title="光明帝尊",},
{type=2,title="聚魂暗灵",},
{type=2,title="采阴暗卒",},
{type=2,title="化形暗使",},
{type=2,title="采灵暗魉",},
{type=2,title="吞元暗将",},
{type=2,title="嗜魂暗帅",},
{type=2,title="夺体暗仙",},
{type=2,title="开窍暗王",},
{type=2,title="通明暗皇",},
{type=2,title="凝型暗帝",},
{type=2,title="成形暗尊",},
{type=2,title="黑暗帝尊",}
},

title_meta_table_map={
[22]=10,	-- depth:1
[21]=9,	-- depth:1
[20]=8,	-- depth:1
[19]=7,	-- depth:1
[16]=4,	-- depth:1
[17]=5,	-- depth:1
[15]=3,	-- depth:1
[14]=2,	-- depth:1
[23]=11,	-- depth:1
[18]=6,	-- depth:1
[24]=12,	-- depth:1
},
model_type={
{},
{model_type=2,appe_image_id=2,min_grade=50,},
{model_type=3,appe_image_id=3,min_grade=100,},
{model_type=4,appe_image_id=4,min_grade=150,},
{type=2,appe_image_id=101,},
{type=2,appe_image_id=102,},
{type=2,appe_image_id=103,},
{type=2,appe_image_id=104,}
},

model_type_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
other_default_table={chongzhi_add_exp=10,chongzhi_add_exp_limit=0,chongzhi_add_bless=10,chongzhi_add_bless_limit=0,vip_limit=30,},

type_default_table={type=1,rmb_type=171,rmb_seq=1,rmb_price=38,rmb_reward_item={[0]=item_table[116],[1]=item_table[117],[2]=item_table[118]},choose_reward_item={[0]=item_table[119],[1]=item_table[2],[2]=item_table[120]},},

rmb_buy_default_table={level=1,name="黄金",rmb_type=172,rmb_seq=1,rmb_price=128,exp_add_per=10000,bless_add_per=10000,virtual_gold_2_daily_use=1000,rmb_reward_item={[0]=item_table[121],[1]=item_table[37],[2]=item_table[122]},},

level_default_table={type=1,level=1,stage=0,need_exp=500,reward_item={[0]=item_table[123]},},

grade_default_table={type=1,grade=0,need_bless=100,modle_type=4,attr_id1=102,attr_value1=0,attr_id2=101,attr_value2=0,attr_id3=103,attr_value3=0,attr_id4=104,attr_value4=0,attr_id5=0,attr_value5=0,},

skill_default_table={type=1,skill_id=1,need_level=0,need_grade=30,attr_id1=102,attr_value1=565,attr_id2=104,attr_value2=188,attr_id3=101,attr_value3=5647,attr_id4=103,attr_value4=282,attr_per=100,skill_name="幻云",skill_describe="攻    击<color=#9df5a7>            +565</color>\n破    甲<color=#9df5a7>            +5647</color>\n生    命<color=#9df5a7>            +188</color>\n防    御<color=#9df5a7>            +282</color>\n系统总加成<color=#9df5a7>          +1%</color>",skill_icon=2041,skill_open="30阶可激活",},

task_default_table={task_id=0,task_type=1,param1=3,param2=0,param3=0,add_exp=10,add_bless=10,des="日常活跃度",target=3,open_panel="bizuo#bizuo_bizuo",},

title_default_table={type=1,level=1,title="聚魂明灵",},

model_type_default_table={type=1,model_type=1,appe_image_id=1,min_grade=0,}

}

