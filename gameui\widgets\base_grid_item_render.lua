------------------------
BaseGridItemRender  = BaseGridItemRender or BaseClass(BaseRender)

function BaseGridItemRender:__init()
	self.item_render_tab = {}
	self.cloumns = 0
	self.select_callback = nil
	self.asset_bundle = nil
	self.asset_name = nil
	self.is_show_tips = nil
	-- self.select_tab = {}
end

function BaseGridItemRender:__delete()
	for k, v in pairs(self.item_render_tab) do
		v:DeleteMe()
	end
	self.item_render_tab = {}
end

function BaseGridItemRender:SetItemRender(item_render)
	self.item_render = item_render
end

function BaseGridItemRender:SetColumns(columns)
	self.columns = columns
end

function BaseGridItemRender:SetRows(rows)
	self.rows = rows
end

function BaseGridItemRender:GetRows()
	return self.rows
end

function BaseGridItemRender:SetAssetBundle(asset_bundle, asset_name)
	self.asset_bundle = asset_bundle
	self.asset_name = asset_name
end

function BaseGridItemRender:SetSelectTab(select_tab)
	self.select_tab = select_tab
end

function BaseGridItemRender:SetSelectEffectCallBack(callback)
	self.select_callback = callback
end

function BaseGridItemRender:GetCell(index)
	for i = 1, self.columns do
		if self.item_render_tab[i] and self.item_render_tab[i]:GetIndex() == index then
			return self.item_render_tab[i]
		end
	end
end

function BaseGridItemRender:GetAllCell()
	return self.item_render_tab
end

function BaseGridItemRender:SetIgnoreDataToSelect(value)
	for i = 1, self.columns do
		if self.item_render_tab[i] then
			self.item_render_tab[i]:SetIgnoreDataToSelect(value)
		end
	end
end

function BaseGridItemRender:GetFirstCellByItemId(item_id)
	for i = 1, self.columns do
		if self.item_render_tab[i]:GetData().item_id and self.item_render_tab[i]:GetData().item_id == item_id then
			return self.item_render_tab[i]
		end
	end
	return nil
end

function BaseGridItemRender:SetHideEmptyCell(is_no_data_hide)
	self.is_no_data_hide = is_no_data_hide
end

function BaseGridItemRender:SetData(data)
	self.data = data

	for i = 1, self.columns do
		local item_render = self.item_render_tab[i]
		if self.data and self.data[i] then
			if nil == item_render then
				if self.asset_bundle and self.asset_name then
					item_render = self.item_render.New()
					self.item_render_tab[i] = item_render
					item_render:LoadAsset(self.asset_bundle, self.asset_name, self.view.transform)

				else
					item_render = self.item_render.New(self.view)
					self.item_render_tab[i] = item_render
				end
			else
				item_render:SetVisible(true)
			end

			local cell_index = (self.rows * self.columns + i)
			item_render:SetIndex(cell_index)
			item_render:SetSelectCallBack(BindTool.Bind(self.SelectCallBack, self))
			item_render:SetClickCallBack(self.click_callback)
			item_render:SetData(self.data[i])
			if nil ~= self.is_show_tips then
				item_render:SetIsShowTips(self.is_show_tips)
			end
		else
			if item_render then
				item_render:SetActive(false)
			end
		end
	end

	if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function BaseGridItemRender:OnFlush()
	self:RefreshSelectState()
	if self.is_no_data_hide then
		self:SetCellsActive()
	end
end

function BaseGridItemRender:RefreshSelectState()
	for i = 1, self.columns do
		if self.data[i] then
			local item_render = self.item_render_tab[i]
			local cell_index = item_render:GetIndex()
			local is_select = false
			for k, v in pairs(self.select_tab[1]) do
				if k == cell_index and v then
					is_select = true
					break
				end
			end
			item_render:SetSelect(is_select, true)
		end
	end
end

function BaseGridItemRender:SetCellsActive()
	for k,v in pairs(self.item_render_tab) do
		v:SetActive(self.data ~= nil and self.data[k] ~= nil and not IsEmptyTable(self.data[k]))
	end
end

function BaseGridItemRender:SelectCallBack(index, is_select)
	self.select_tab[1][index] = is_select
end

function BaseGridItemRender:SetIsShowTips(is_show_tips)
	self.is_show_tips = is_show_tips
	for k,v in pairs(self.item_render_tab) do
		v:SetIsShowTips(is_show_tips)
	end
end

