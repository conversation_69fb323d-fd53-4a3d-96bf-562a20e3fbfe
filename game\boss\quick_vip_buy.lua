QuickVipBuyView = QuickVipBuyView or BaseClass(SafeBaseView)

function QuickVipBuyView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third3_panel")
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_quick_vip")
	self:SetMaskBg(true, true)
	self.boss_tab_index = -1
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
end

function QuickVipBuyView:ReleaseCallBack()
    self.list = nil
	self.boss_tab_index = nil
end

function QuickVipBuyView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Boss.TimesAdd
    self.node_list["layout_commmon_third_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_third_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list["Button"].button:AddClickListener(BindTool.Bind(self.OnClickUse, self))
end

function QuickVipBuyView:ShowIndexCallBack()
    self:Flush()
end

function QuickVipBuyView:SetTabIndex(tab_index)
    self.boss_tab_index = tab_index
end

function QuickVipBuyView:OnFlush()
	if self.boss_tab_index == TabIndex.worserv_boss_sgyj + 1 then
		if self.node_list["ver_2"] then
			self.node_list["ver_2"]:SetActive(true)
		end
		self:FlushSgInfo()
	elseif self.boss_tab_index == TabIndex.boss_dabao then
		if self.node_list["ver_2"] then
			self.node_list["ver_2"]:SetActive(false)
		end
		self:FlushDabaoInfo()
	end
end

function QuickVipBuyView:FlushDabaoInfo()
	local _, max_times = BossWGData.Instance:GetDaBaoBossRemainEnterTimes()
	self.node_list["time1"].text.text = Language.Boss.DaBaoBossRichTip1 .. ToColorStr(max_times, COLOR3B.D_GREEN)

	local next_enter_times, next_vip_level = BossWGData.Instance:GetIsShowDabaoVipTip()
	if next_enter_times and next_vip_level then
		self.node_list["desc"].text.text = Language.Boss.UpVipTip .. next_vip_level
		self.node_list["next_time1"].text.text = next_enter_times
	end
end

function QuickVipBuyView:FlushSgInfo()
	local _, max_tire_value = BossWGData.Instance:GetSgBossTire()
	self.node_list["time2"].text.text = Language.Boss.DaBaoBossRichTip1 .. ToColorStr(max_tire_value, COLOR3B.D_GREEN)

	local _, max_enter_times = BossWGData.Instance:GetSgBossEnterTimes()
	self.node_list["time1"].text.text = Language.Boss.WorldBossTimesValue .. ToColorStr(max_enter_times, COLOR3B.D_GREEN)

	local show_enter_tip, show_tire_tip, next_vip_level = BossWGData.Instance:GetIsShowSGVipTip()

	if next_vip_level ~= 0 then
		local other_cfg = BossWGData.Instance:GetSGOtherCfg()
		self.node_list["desc"].text.text = Language.Boss.UpVipTip .. next_vip_level
		self.node_list["next_time1"].text.text = show_tire_tip ~= -1 and show_tire_tip + other_cfg.reward_time or ""
		self.node_list["next_time2"].text.text = show_enter_tip ~= -1 and show_enter_tip + other_cfg.enter_time or ""
	end
end

function QuickVipBuyView:OnClickUse()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
	self:Close()
end