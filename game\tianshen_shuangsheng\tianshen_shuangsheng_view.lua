TianShenShuangShengView = TianShenShuangShengView or BaseClass(SafeBaseView)

function TianShenShuangShengView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	
	self:SetMaskBg(false)
	local common_bundle = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/tianshen_shuangsheng_prefab"
    self:AddViewResource(0, bundle_name, "layout_tianshen_shuangsheng")
	self:AddViewResource(0, common_bundle, "layout_a2_common_top_panel")
end

function TianShenShuangShengView:__delete()

end

function TianShenShuangShengView:OpenCallBack()

end

function TianShenShuangShengView:CloseCallBack()
	self.curr_show_model_index = nil
end

function TianShenShuangShengView:LoadCallBack()
	if not self.show_model then
		self.show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["Block"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.show_model:SetRenderTexUI3DModel(display_data)
		-- self.show_model:SetUI3DModel(self.node_list.Block.transform, self.node_list.Block.event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.show_model)
	end

	if not self.stuff_item then
		self.stuff_item = ItemCell.New(self.node_list.stuff_item)
	end

	-- 基础属性
	if not self.attrlist then
		self.attrlist = {}
		for i = 1, 9 do
			local attr_obj = self.node_list.layout_flair_attr:FindObj(string.format("attr_%d", i))
			if attr_obj then
				local cell = CommonAddAttrRender.New(attr_obj)
				cell:SetIndex(i)
				cell:SetAttrNameNeedSpace(true)
				self.attrlist[i] = cell
			end
		end
	end

	if not self.fs_star_list then
        self.fs_star_list = {}
        for i = 1, 5 do
            self.fs_star_list[i] = self.node_list[string.format("shuangsheng_star_%d", i)]
        end
    end

	if not self.info_skill_list then
        self.info_skill_list = {}
        local num = self.node_list.skill_root.transform.childCount
        for i = 1, num do
            local cell = TianShenSkillItem.New(self.node_list.skill_root:FindObj("info_skill_" .. i))
            cell:SetIndex(i)
            self.info_skill_list[i] = cell
        end
    end

	self.node_list.title_view_name.text.text = Language.TianShenShuangSheng.Title
	self:RegisterButtonListen()
end

function TianShenShuangShengView:ReleaseCallBack()
	if self.show_model then
		self.show_model:DeleteMe()
		self.show_model = nil
	end

	if self.stuff_item then
		self.stuff_item:DeleteMe()
		self.stuff_item = nil
	end

	if self.attrlist and #self.attrlist > 0 then
		for _, culture_cell in ipairs(self.attrlist) do
			culture_cell:DeleteMe()
			culture_cell = nil
		end

		self.attrlist = nil
	end

	self.fs_star_list = nil

	if nil ~= self.info_skill_list then
        for k,v in pairs(self.info_skill_list) do
            v:DeleteMe()
        end

        self.info_skill_list = nil
    end

	self.curr_show_data = nil
	self.curr_show_index = nil
	self.curr_show_model_list = nil
	self.curr_show_model_index = nil
	self.curr_show_model_id = nil
end

function TianShenShuangShengView:OnFlush(param_list)	-- 切换当前的神灵(取当前最优选择（优先拿已幻化的展示）)
	self:FlushShuangshengData(param_list)
	self:FlushShowModel()
	self:FlushLevelMessage()
end

---注册按钮方法
function TianShenShuangShengView:RegisterButtonListen()
	XUI.AddClickEventListener(self.node_list.shuangshen_skill_yulan, BindTool.Bind(self.OnClickSkillYuLanBtn, self))
	XUI.AddClickEventListener(self.node_list.shuangshen_huanhua, BindTool.Bind(self.OnClickHuanHuaBtn, self))
	XUI.AddClickEventListener(self.node_list.shuangshen_yi_huanhua, BindTool.Bind(self.OnClickYiHuanHuaBtn, self))
	XUI.AddClickEventListener(self.node_list.shuangshen_yulan, BindTool.Bind(self.OnClickYuLanBtn, self))
	XUI.AddClickEventListener(self.node_list.shuangshen_left, BindTool.Bind(self.OnClickShowLeftBtn, self))
	XUI.AddClickEventListener(self.node_list.shuangshen_right, BindTool.Bind(self.OnClickShowRightBtn, self))
	XUI.AddClickEventListener(self.node_list.shuangsheng_btn_active, BindTool.Bind(self.OnClickActiveBtn, self))
	XUI.AddClickEventListener(self.node_list.shuangsheng_btn_up_level, BindTool.Bind(self.OnClickLevelUpBtn, self))
end

-- 确定当前的选择数据
function TianShenShuangShengView:FlushShuangshengData(param_list)
	local cur_select_index = tonumber(TianShenWGData.Instance:InfoTsSelectIndex())
	
	if cur_select_index == -1 then
		if param_list and param_list.all and param_list.all.open_param then
			cur_select_index = tonumber(param_list.all.open_param)
		end
	else
		if param_list and param_list.all and param_list.all.open_param then
			cur_select_index = tonumber(param_list.all.open_param)
		end
	end
	
    self.curr_show_index = cur_select_index
    self.curr_show_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarData(self.curr_show_index)

	if self.curr_show_data then
		local list, best_index = TianShenShuangShengWGData.Instance:GetTianShenAvatarModelList(self.curr_show_index, self.curr_show_data.star_level)
		self.curr_show_model_list = list
		
		-- 这个地方有点绕，表示为没有当前幻化直接取最优解，有幻化取幻化，列表最后一个不能幻化
		if not self.curr_show_model_index then
			if self.curr_show_data.curr_aura_id == -1 then	-- 取最优解(不是满级则为列表倒数第二个)
				self.curr_show_model_index = best_index
			else
				local avatar_image_id_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(self.curr_show_data.curr_aura_id)
				local start_level = avatar_image_id_cfg and avatar_image_id_cfg.star_level or 0
				self.curr_show_model_index = TianShenShuangShengWGData.Instance:GetCurModelListIndex(self.curr_show_model_list, start_level)
			end
		end

		if not self.curr_show_model_index then		-- 上面没有取到值则取第一个
			self.curr_show_model_index = 1
		end
	end
end


-- 刷新模型
function TianShenShuangShengView:FlushShowModel()
	if (not self.curr_show_model_list) or (not self.curr_show_index) or (not self.curr_show_data) then
		return
	end

	-- 刷新左右模型切换按钮
	self.node_list.shuangshen_left:CustomSetActive(self.curr_show_model_index and self.curr_show_model_index ~= 1)
	self.node_list.shuangshen_right:CustomSetActive(self.curr_show_model_index and self.curr_show_model_index ~= #self.curr_show_model_list)

	local show_model_data = self.curr_show_model_list[self.curr_show_model_index]
	local star_level = show_model_data and show_model_data.star_level or 0
	local lv_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarLvCfgData(self.curr_show_index, star_level)
	if lv_data and show_model_data then
		local app_image_id_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(lv_data.avatar_id)

		local is_model_un_lock = not show_model_data.is_lock
		local is_active = self.curr_show_data.is_unlock
		self.node_list.shuangshen_suo:CustomSetActive(show_model_data.is_lock)

		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.curr_show_index)
		if tianshen_cfg then
			local front_name = tianshen_cfg.bianshen_name

			if lv_data.need_rumo_level > 0 then
				local rumo_name = Language.TianShenHuaMo.DemonName[lv_data.need_rumo_level]
				front_name = string.format(rumo_name, tianshen_cfg.bianshen_name or "")
			end

			self.node_list.shuangshen_suo_text.text.text = string.format(Language.TianShenShuangSheng.ActiveCondition, front_name, show_model_data.star_level)
		end
	
		self.node_list.shuangshen_huanhua:CustomSetActive(lv_data.avatar_id ~= self.curr_show_data.curr_aura_id and is_model_un_lock and is_active)
		self.node_list.shuangshen_yi_huanhua:CustomSetActive(lv_data.avatar_id == self.curr_show_data.curr_aura_id and is_model_un_lock and is_active)

		if app_image_id_data then
			self.node_list.name_text.text.text = app_image_id_data.name

			if self.curr_show_model_id ~= app_image_id_data.appe_image_id then
				local data = TianShenWGData.Instance:GetTianShenCfg(self.curr_show_index)
				local tianshen_appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(data.index) or data.appe_image_id
				self.show_model:RemoveShuangShengTianShenUI(true)
				self.show_model:SetTianShenModel(tianshen_appe_image_id, data.index, true, nil, SceneObjAnimator.Rest, function ()
					if app_image_id_data ~= nil then
						self.show_model:TryChangePartScale(SceneObjPart.Main, app_image_id_data.model_scale)
					end
				end, app_image_id_data and app_image_id_data.shiqi_scale or 1)
				self.show_model:SetShuangShengTianShenModel(app_image_id_data.appe_image_id)

				self.curr_show_model_id = app_image_id_data.appe_image_id
			end
		end
	end
end

-- 刷新属性
function TianShenShuangShengView:FlushLevelMessage()
	if (not self.curr_show_data) or (not self.curr_show_index) then
		return
	end

    local attr_list = TianShenShuangShengWGData.Instance:GetTianShenAvatarAttrList(self.curr_show_index, self.curr_show_data.star_level, true)
    if attr_list then
        for index, attr_cell in ipairs(self.attrlist) do
            if attr_cell and attr_list[index] then
                attr_cell:SetData(attr_list[index])
			else
				attr_cell:SetData({})
            end
        end
    end

	local star_res_list = GetStarImgResByStar(self.curr_show_data.star_level)
	for k,v in ipairs(self.fs_star_list) do
		v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
	end

	local level_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarLvCfgData(self.curr_show_index, self.curr_show_data.star_level)

	if self.curr_show_data.is_unlock then
		if level_cfg then
			self:FlushStuffItemMessage(level_cfg.consume_item)
		end
	else
		local cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarCfgData(self.curr_show_index)
		if cfg then
			self:FlushStuffItemMessage(cfg.active_item)
		end
	end
	local next_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarLvCfgData(self.curr_show_index, self.curr_show_data.star_level + 1)
	local is_full = next_cfg == nil
	local cap = TianShenShuangShengWGData.Instance:GetTianShenAvatarCapValue(self.curr_show_index, self.curr_show_data.star_level, attr_list)
	self.node_list.cap_value.text.text = cap
	self.node_list.shuangsheng_stars_root:CustomSetActive(self.curr_show_data.is_unlock)
	self.node_list.shuangsheng_btn_active:CustomSetActive(not self.curr_show_data.is_unlock)
	self.node_list.shuangsheng_btn_up_level:CustomSetActive(self.curr_show_data.is_unlock and (not is_full))
	self.node_list.shuangsheng_is_max_level:CustomSetActive(is_full)
	self.node_list.shuangsheng_btn_active_remind:CustomSetActive(self.curr_show_data.remind)
	self.node_list.up_level_remind:CustomSetActive(self.curr_show_data.remind)

	if level_cfg then
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.curr_show_index)
		local color = nil
		local limit_str = ""
		local is_active = false

		if level_cfg.need_rumo_level == 0 then		--不需要入魔等级检测是否激活天神
			is_active = TianShenWGData.Instance:GetTianshenInfoStatus(self.curr_show_index) == 1
			limit_str = string.format(Language.TianShenHuaMo.ActiveCondotion, tianshen_cfg.bianshen_name)
		else
			local sub_data = TianShenHuamoWGData.Instance:GetHuaMoSubDataById(self.curr_show_index, level_cfg.need_rumo_level)

			if sub_data and not sub_data.lock then
				is_active = true
			else
				is_active = false
			end
			local rumo_name = Language.TianShenHuaMo.DemonName[level_cfg.need_rumo_level]
			local front_name = string.format(rumo_name, tianshen_cfg.bianshen_name)
			limit_str = string.format(Language.TianShenHuaMo.ActiveCondotion, front_name)
		end

		color = is_active and COLOR3B.GREEN or COLOR3B.PINK
		limit_str = ToColorStr(limit_str, color)
		self.node_list.limit_text.text.text = limit_str

		local zhu_skill = TianShenWGData.Instance:GetTianShenZhuSkill(self.curr_show_index)
		for k,v in pairs(self.info_skill_list) do
			local skill_id = tonumber(zhu_skill[k])
			v:SetData({skill_id = skill_id, tianshen_index = self.curr_show_index, from_view = FROM_TIANSHEN_UPGRADE, is_open_skill = true, is_tianshen_select_view = false, is_shuangsheng = true, avatar_id = level_cfg.avatar_id})
		end
	end
end

-- 展示消耗物
function TianShenShuangShengView:FlushStuffItemMessage(item_data)
	if not self.stuff_item then
		return
	end

	self.stuff_item:SetData({item_id = item_data.item_id})
	local item_num = ItemWGData.Instance:GetItemNumInBagById(item_data.item_id)
	local color = item_num >= item_data.num and COLOR3B.D_GREEN or COLOR3B.D_RED
	self.stuff_item:SetRightBottomTextVisible(true)
	self.stuff_item:SetRightBottomColorText(item_num .. '/' .. item_data.num, color)
end

-----------------------------------------------------------------------------------------
-- 技能预览
function TianShenShuangShengView:OnClickSkillYuLanBtn()
	CommonSkillShowCtrl.Instance:SetTianShenSkillViewDataAndOpen({tianshen_index = self.curr_show_index, shaungsheng_tianshen_index = self.curr_show_model_index})
end

-- 幻化
function TianShenShuangShengView:OnClickHuanHuaBtn()
	if not self.curr_show_data.is_unlock then	-- 物品未激活检查一下条件为啥显示了按钮
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenShuangSheng.NotUnLock)
		return
	end

	if (not self.curr_show_model_list) or (not self.curr_show_index) then
		return
	end

	local show_model_data = self.curr_show_model_list[self.curr_show_model_index]
	local star_level = show_model_data and show_model_data.star_level or 0
	TianShenShuangShengWGCtrl.Instance:SendOperateChangeAvatar(self.curr_show_index, 0, star_level)
end

-- 已幻化
function TianShenShuangShengView:OnClickYiHuanHuaBtn()
	if not self.curr_show_data.is_unlock then	-- 物品未激活检查一下条件为啥显示了按钮
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenShuangSheng.NotUnLock)
		return
	end

	if (not self.curr_show_model_list) or (not self.curr_show_index) then
		return
	end

	TianShenShuangShengWGCtrl.Instance:SendOperateChangeAvatar(self.curr_show_index, 0, -1)
end

-- 形象预览
function TianShenShuangShengView:OnClickYuLanBtn()
	local preview_list = TianShenShuangShengWGData.Instance:GetTianShenAvatarPreviewList(self.curr_show_index)
	local show_data = {}
	show_data.tianshen_index = self.curr_show_index
	show_data.list = preview_list
	TianShenShuangShengWGCtrl.Instance:OpenPreView(show_data)
end

-- 形象左切换
function TianShenShuangShengView:OnClickShowLeftBtn()
	if (not self.curr_show_model_list) or (not self.curr_show_index) then
		return
	end

	self.curr_show_model_index = self.curr_show_model_index - 1
	if self.curr_show_model_index <= 1 then
		self.curr_show_model_index = 1
	end

	self:FlushShowModel()
end

-- 形象右切换
function TianShenShuangShengView:OnClickShowRightBtn()
	if (not self.curr_show_model_list) or (not self.curr_show_index) then
		return
	end

	local len = #self.curr_show_model_list
	self.curr_show_model_index = self.curr_show_model_index + 1

	if self.curr_show_model_index >= len then
		self.curr_show_model_index = len
	end

	self:FlushShowModel()
end

-- 双生神灵激活
function TianShenShuangShengView:OnClickActiveBtn()
	if (not self.curr_show_data) or (not self.curr_show_index) then
		return
	end

	if self.curr_show_data.is_unlock then	-- 物品已激活检查一下条件为啥显示了按钮
		return
	end

	local is_active = TianShenWGData.Instance:GetTianshenInfoStatus(self.curr_show_index) == 1
	if not is_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenHuaMo.ConditionError)
		return
	end
	 
	local cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarCfgData(self.curr_show_index)
	if cfg and cfg.active_item then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.active_item.item_id)
		if item_num < cfg.active_item.num then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.active_item.item_id})
		else
			-- 发送激活协议
			TianShenShuangShengWGCtrl.Instance:SendOperateActiveAvatar(self.curr_show_index)
		end
	end
end

-- 双生神灵升星
function TianShenShuangShengView:OnClickLevelUpBtn()
	if (not self.curr_show_data) or (not self.curr_show_index) then
		return
	end

	if not self.curr_show_data.is_unlock then	-- 物品未激活检查一下条件为啥显示了按钮
		return
	end

	local level_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarLvCfgData(self.curr_show_index, self.curr_show_data.star_level)
	if level_cfg then
		if level_cfg.need_rumo_level > 0 then
			local sub_data = TianShenHuamoWGData.Instance:GetHuaMoSubDataById(self.curr_show_index, level_cfg.need_rumo_level)
			local is_active = sub_data and (not sub_data.lock)
			if not is_active then
				TianShenHuamoWGCtrl.Instance:OpenView()
				-- SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenShuangSheng.ConditionError)
				return
			end
		end

		if level_cfg.consume_item then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.consume_item.item_id)
			if item_num < level_cfg.consume_item.num then
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = level_cfg.consume_item.item_id})
			else
				-- 发送升星协议
				TianShenShuangShengWGCtrl.Instance:SendOperateUpgradeAvatar(self.curr_show_index)
			end
		end
	end
end
