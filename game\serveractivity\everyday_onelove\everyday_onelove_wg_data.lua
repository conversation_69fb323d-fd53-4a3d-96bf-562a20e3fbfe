EveryDayOneLoveWGData = EveryDayOneLoveWGData or BaseClass()

function EveryDayOneLoveWGData:__init()
	if EveryDayOneLoveWGData.Instance ~= nil then
		print_error("[EveryDayOneLoveWGData] attempt to create singleton twice!")
		return
	end

	EveryDayOneLoveWGData.Instance = self
	self.is_red = true
	RemindManager.Instance:Register(RemindName.EveryDayOneLove,BindTool.Bind(self.RemindChange,self))
	self.config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().daily_love_reward_percent
end

function EveryDayOneLoveWGData:__delete()
	EveryDayOneLoveWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.EveryDayOneLove)
end

function EveryDayOneLoveWGData:SetInfo(protocol)
	if MainuiWGCtrl.Instance.view:IsLoaded() then
		local act_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ACTIVITY_DAILY_LOVE)
		if act_data and next(act_data) then
			if protocol.flag == 0 then
				ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.ACTIVITY_DAILY_LOVE,ACTIVITY_STATUS.CLOSE, act_data.next_time, act_data.start_time, act_data.end_time, act_data.open_type)
			elseif protocol.flag == 1 then
				ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.ACTIVITY_DAILY_LOVE,ACTIVITY_STATUS.OPEN, act_data.next_time, act_data.start_time, act_data.end_time, act_data.open_type)
			end
		end
	end
end


function EveryDayOneLoveWGData:RemindChange()
	local is_red = 0
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACTIVITY_DAILY_LOVE) and self.is_red then
		is_red = 1
	end
	return is_red
end

function EveryDayOneLoveWGData:SetRemindFlage(is_red)
	self.is_red  = is_red
end

function EveryDayOneLoveWGData:GetConfigValue()

	local config = self:GetConfigByServerOpenDay(self.config)
	if config and next(config) then
		return config.gold_percent
	end
end

function EveryDayOneLoveWGData:GetConfigByServerOpenDay(cfg)
	if not cfg or not next(cfg) then return end
	local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for i=#cfg,1,-1 do
		if cur_openserver_day >= cfg[i].opengame_day then
			return cfg[i]
		end
	end
end
