 -- 天神激活面板
TianShen_Upgrade_Type = {
	None = 0,
	ShengXing = 1,
	<PERSON>g<PERSON>ie = 2,
	<PERSON>Hua = 3,
}

local TOGGLE_MAX = 5
function TianShenView:LoadTianShenActivationCallBack()
	self.is_play_ani = false
	self.active_ts_select_index = -1
	self.is_oneKey = false
	self.activation_select_index = 1
	self.select_skill = 0 -- 外部跳转，选中技能
	self.zhu_skill = {}
	self.cur_is_on_idx = 0

	if not self.activation_role_model then
		self.activation_role_model = RoleModel.New()
		self.activation_role_model:SetUISceneModel(self.node_list["acti_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.activation_role_model, TabIndex.tianshen_activation)
	end

	if not self.ac_skill_list then
        self.ac_skill_list = {}
        local num = self.node_list.ac_zhu_skill.transform.childCount
        for i = 1, num do
            local cell = TianShenSkillItem.New(self.node_list.ac_zhu_skill:FindObj("info_skill_" .. i))
            cell:SetIndex(i)
            self.ac_skill_list[i] = cell
        end
    end

	if not self.consume_list then
		self.consume_list = {}
		for i=1,3 do
			self.consume_list[i] = ItemCell.New(self.node_list["ph_bianshen_pos_" .. i])
			self.consume_list[i]:SetHideRightDownBgLessNum(-1)
		end
	end

	if not self.ts_star_list then
		self.ts_star_list = {}
		for i=1,5 do
			self.ts_star_list[i] = self.node_list["ts_star" .. i]
		end
	end

	self:ArrowTween()
	self.node_list.btn_shengxing.toggle:AddValueChangedListener(BindTool.Bind(self.OnShengXingClick, self))
	self.node_list.btn_shengjie.toggle:AddValueChangedListener(BindTool.Bind(self.OpenShengJieClick, self))
	XUI.AddClickEventListener(self.node_list["btn_bianshen_upstar"], BindTool.Bind(self.OnAppearanceUpLevelClick, self))
	XUI.AddClickEventListener(self.node_list["jinjie_btn_lock"], BindTool.Bind(self.ClickJinjieBlock, self))
	XUI.AddClickEventListener(self.node_list["btn_zhenfa"], BindTool.Bind(self.OnClickZhenFaBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_peiyang"], BindTool.Bind(self.OnClickPeiYangBtn, self))
	XUI.AddClickEventListener(self.node_list["ac_ts_skill_yulan_btn"], BindTool.Bind(self.OnClickSkillYuLanBtn, self))
	XUI.AddClickEventListener(self.node_list["ac_ts_sz_btn"], BindTool.Bind(self.OnClickTSShangZhenBtn, self))
	
	-- 绑定红点
	self.remind_change = BindTool.Bind(self.ShenTongRemindCallBack, self)
    RemindManager.Instance:Bind(self.remind_change, RemindName.TianShen_ST)

	self:CreateActivationAccordion(TianShenView.TabIndex.Activation)
	self:FlushActivationAccordionTable()
end

function TianShenView:ReleaseTianShenActivationCallBack()
	if self.remind_change  then
		RemindManager.Instance:UnBind(self.remind_change)
	end
	self.remind_change = nil
	self.zhu_skill_list_view = nil

	if self.ac_skill_list then
        for k,v in pairs(self.ac_skill_list) do
            v:DeleteMe()
        end
        self.ac_skill_list = nil
    end

	if self.consume_list then
		for k, v in pairs(self.consume_list) do
			v:DeleteMe()
		end
		self.consume_list = nil
	end

	self.ts_star_list = nil
	self.active_ts_select_index = -1
	self.old_t_item_id = nil
	self.old_t_btn_state = nil
	self.need_clear = nil

	if self.activation_role_model then
		self.activation_role_model:DeleteMe()
		self.activation_role_model = nil
	end

	if MainuiWGCtrl.Instance then
		MainuiWGCtrl.Instance:CacheTableClearData()
	end

    if self.shengxing_timer then
        GlobalTimerQuest:CancelQuest(self.shengxing_timer)
        self.shengxing_timer = nil
    end

	self.activation_select_index = 1
	self.select_skill = 0
	self.tianshen_huanhua_show_audio = nil
	self.tianshen_activition_first_open = nil
	self.last_show_tianshen_id = nil

	if self.ts_ac_accor_list then
		self.ts_ac_accor_list = nil
	end

	if self.ts_ac_big_cell_list then
        for k,v in pairs(self.ts_ac_big_cell_list) do
            v:DeleteMe()
        end
        self.ts_ac_big_cell_list = nil
    end

	if self.arrow_act_tweener then
		for k,v in pairs(self.arrow_act_tweener) do
			v:Kill()
		end
		
        self.arrow_act_tweener = nil
    end

	self.act_need_jump = nil
	self.old_select_tianshen_index = nil
	self.old_is_shangzheng = nil
end

function TianShenView:FlushTsIconList()
	local tianshen_wg_data = TianShenWGData.Instance
	local data_list = tianshen_wg_data:GetTianShenInfoDataList(TianShenView.TabIndex.Activation)
    self:RefreshAccordionData(TianShenView.TabIndex.Activation)
    self:RefreshAccordionRed(TianShenView.TabIndex.Activation)

	local jump_ts_index = nil
	if self.jump_ts_index then -- 跳转指定的
		jump_ts_index = self.jump_ts_index
		self.jump_ts_index = nil
	elseif self.act_need_jump then
		self.act_need_jump = false
		for i, v in ipairs(data_list) do -- 1.跳转可激活
			if v.can_active_status == 1 then
				jump_ts_index = v.index
				break
			end
		end
		if not jump_ts_index then
			for i, v in ipairs(data_list) do -- 2.跳转可升星
				if v.active_status == 1 and tianshen_wg_data:SpecialTianshenCanUpStar(v.index) then
					jump_ts_index = v.index
					break
				end
			end
		end
		if not jump_ts_index then -- 3.跳转可升级
			local min_ts_level = 999
			for i, v in ipairs(data_list) do
				if v.level < min_ts_level and v.active_status == 1 and tianshen_wg_data:SpecialTianshenCanUpGrade(v.index) then
					min_ts_level = v.level
					jump_ts_index = v.index
				end
			end
		end
	else
		if self.active_ts_select_index >= 0 then -- 跳转之前选中的天神
			jump_ts_index = self.active_ts_select_index
		end
	end

	jump_ts_index = jump_ts_index or -1
	self:AccordionJumpToSelectForIndex(jump_ts_index)
	self:FlushActivationAllPart()
end

function TianShenView:ActivationOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			local show_index_data
			if v.open_param == "shengjie" then
				local num, cfg = TianShenWGData.Instance:GetUpgradeRemind()
				if num > 0 and cfg then
					show_index_data = {all = cfg}
				end
			elseif v.open_param == "shengxing" then
				local num, cfg = TianShenWGData.Instance:GetUpStarRemind()
				if num > 0 and cfg then
					show_index_data = {all = cfg}
				end
			else
				show_index_data = param_t
			end
			self:ShowIndexActivationXingXiang(show_index_data)

			if v.open_param == "shengjie" then
				self:ChangeToShengJie()
			elseif v.open_param == "shengxing" then
				self:ChangeToShengXing()
			end

			if not self.slider_tween then
				self:FlushActivationSlider()
			end
			if v.to_ui_param then
				self.jump_ts_index = tonumber(v.to_ui_param)
			end
			self:FlushTsIconList()
		elseif k == "flush_display" then
			self:FlushActivationModelDisPlay()
			self:FlushShenXing()
		elseif k == "flush_skill" then
		elseif k == "succes" then
		end
	end
end

function TianShenView:CloseTianShenActivationCallBack()
	self.tianshen_active_audio_play_t = {}
	self.is_oneKey = false
	self.activation_series = nil
	if self.slider_tween then
		self.slider_tween:Kill()
		self.slider_tween = nil
	end
end

function TianShenView:OpenTianShenActivationCallBack()
	self.old_grade = nil
	self.old_star = nil
	self.old_cao = nil
	self.is_play_shengxing = false
	self.tianshen_active_audio_play_t = {}
end

function TianShenView:ShowTianShenActivationIndexCallBack()
	--self:FlushActivationModelDisPlay()
	self.node_list.jinjie_btn_lock:SetActive(not FunOpen.Instance:GetFunIsOpened(FunName.TianshenHuanhuaJinjie))

	if self.acti_select_data then
		-- 未激活时默认选中升星
		if not TianShenWGData.Instance:IsActivation(self.acti_select_data.index) or not FunOpen.Instance:GetFunIsOpened(FunName.TianshenHuanhuaJinjie) then
			self.node_list.btn_shengxing.toggle.isOn = true
			self.upgrade_type = TianShen_Upgrade_Type.ShengXing
			self.node_list.btn_shengjie.toggle.isOn = false
		else
			--已激活的时候，默认选中有红点操作的。
			if TianShenWGData.Instance:SpecialTianshenCanUpStar(self.acti_select_data.index) then
				--可升星
				self.node_list.btn_shengxing.toggle.isOn = true
				self.upgrade_type = TianShen_Upgrade_Type.ShengXing
				self.node_list.btn_shengjie.toggle.isOn = false
			elseif TianShenWGData.Instance:SpecialTianshenCanUpGrade(self.acti_select_data.index) then
				--可进阶
				self.node_list.btn_shengjie.toggle.isOn = true
				self.upgrade_type = TianShen_Upgrade_Type.ShengJie
				self.node_list.btn_shengxing.toggle.isOn = false
			else
				self.node_list.btn_shengxing.toggle.isOn = true
				self.upgrade_type = TianShen_Upgrade_Type.ShengXing
				self.node_list.btn_shengjie.toggle.isOn = false
			end
		end
	else
		self.node_list.btn_shengxing.toggle.isOn = true
		self.upgrade_type = TianShen_Upgrade_Type.ShengXing
		self.node_list.btn_shengjie.toggle.isOn = false
	end

	self.act_need_jump = true
end

-- 刷新属性面板
function TianShenView:FlushAttrView(level, force_attr_change)
	if not self:IsLoadedIndex(0) then return end
	local select_data = self:GetActivationCurSelectListData()
	if not select_data then return end
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(select_data.index)
	local ts_is_active = TianShenWGData.Instance:IsActivation(select_data.index)
	local list = TianShenWGData.Instance:GetUpgradeAttribute(self.upgrade_type, select_data.index, true, level)

	local level = (tianshen_info or {}).level or 0
	local star = (tianshen_info or {}).star or 0
	local need_show_effect = false

	if force_attr_change then
		need_show_effect = true
	else
		if nil ~= self.upgrade_type_cache and nil ~= self.type_index_cache and nil ~= self.upgrade_type_level_cache and nil ~= self.upgrade_type_star_cache then
			if (self.upgrade_type_cache == self.upgrade_type) and (self.type_index_cache == select_data.index) then
				if (level > self.upgrade_type_level_cache) or (star > self.upgrade_type_star_cache) then
					need_show_effect = true
				end
			end
		end
	end

	self.upgrade_type_cache = self.upgrade_type
	self.type_index_cache = select_data.index
	self.upgrade_type_level_cache = level
	self.upgrade_type_star_cache = star
	local attr_name = Language.Common.AttrNameList2

	self.node_list.layout_attr:SetActive(#list > 0)
	for i = 1, 8 do
		local data = list[i]
		local attr_type = data and data.attr_type or 0
		local attr_value = data and data.attr_value or 0
		local is_zero_not_show = SPECIAL_ATTR_COLOR[attr_type] ~= nil and attr_value == 0

		self.node_list["attr_" .. i]:SetActive(data ~= nil and (not is_zero_not_show))

		if data ~= nil then
			self.node_list["arrow_" .. i]:SetActive(data.attr_add and data.attr_add ~= 0)
			self.node_list["next_value_" .. i]:SetActive(data.attr_add and data.attr_add ~= 0)
			self.node_list["next_value_" .. i].text.text = data.attr_add or ""

			if data.attr_value then
				local special_color = SPECIAL_ATTR_COLOR[data.attr_type]
				local need_special_color = nil ~= special_color
				local target_name_color = need_special_color and special_color or COLOR3B.DEFAULT
				self.node_list["name_" .. i].text.text = ToColorStr(attr_name[data.attr_type], target_name_color)
				self.node_list["cur_value_" .. i].text.text = ToColorStr(data.attr_value, target_name_color)
				
				if need_show_effect then
					self:PlayAttrInfoUpEffect(i)
				end
			end
		end
	end

	self.node_list["text_bianshen_zhandouli_num"].text.text = TianShenWGData.Instance:GetActivationZhanLi(select_data.index, true)
end

function TianShenView:PlayAttrInfoUpEffect(index)
	if self.node_list["attr_value_upeffect" .. index] then
		local particls = self.node_list["attr_value_upeffect" .. index]:GetComponentsInChildren(typeof(UnityEngine.ParticleSystem))
		if particls.Length > 0 then
			for i = 0, particls.Length - 1 do
				local effect = particls[i]

				if effect then
					if effect.isPlaying then
						effect:Stop()
						effect:Clear()
					end

					effect:Play()
				end
			end
		end
	end
end

function TianShenView:ArrowTween()
	if self.arrow_act_tweener then
		for k,v in pairs(self.arrow_act_tweener) do
			v:Kill()
		end

        self.arrow_act_tweener = nil
    end

    self.arrow_act_tweener = {}
	local tween_time = 0.8
    for i=1, 8 do
		local node = self.node_list["arrow_" .. i]
		if node then
	        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, 5)
	        self.arrow_act_tweener[i] = node.rect:DOAnchorPosY(-1, tween_time)
	        self.arrow_act_tweener[i]:SetEase(DG.Tweening.Ease.InOutSine)
	        self.arrow_act_tweener[i]:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		end
	end
end

function TianShenView:FlushSlider()
	-- body
	if not self.acti_select_data then return end
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.acti_select_data.index)
	
	if tianshen_info then
		local curr_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(self.acti_select_data.index, tianshen_info.level)
		
		if not TianShenWGData.Instance:IsActivation(self.acti_select_data.index) then
			self.node_list["prog_upstar_progress"]:SetActive(false)
		elseif self.upgrade_type == TianShen_Upgrade_Type.ShengJie then
			local next_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(self.acti_select_data.index, tianshen_info.level + 1)
			self.node_list["prog_upstar_progress"]:SetActive(true)
			if next_upgrade_cfg and curr_upgrade_cfg then
				self.node_list["prog_upstar_progress"].slider.value = tianshen_info.uplevel_exp_val / curr_upgrade_cfg.upgrade_need_exp
				self.node_list["lbl_upstar_progress_num"].text.text = tianshen_info.uplevel_exp_val .. "/" .. curr_upgrade_cfg.upgrade_need_exp
			else
				self.node_list["prog_upstar_progress"].slider.value = 1
				self.node_list["lbl_upstar_progress_num"].text.text = "-- / --"
			end
		else
			self.node_list["prog_upstar_progress"]:SetActive(false)
		end
	end
end

function TianShenView:ShenTongRemindCallBack(remind_name, num)
	if remind_name == RemindName.TianShen_ST then
		self.node_list["shentong_remind"]:SetActive(num > 0)
	end
end

function TianShenView:ClickJinjieBlock()
	local is_open, tips = FunOpen.Instance:GetFunIsOpened(FunName.TianshenHuanhuaJinjie, true)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(tips)
	elseif not self.node_list.btn_shengjie.toggle.isOn then
		self.node_list.btn_shengjie.toggle.isOn = true
		self.node_list.btn_shengxing.toggle.isOn = false
	end
end

function TianShenView:OnClickZhenFaBtn()
	TianShenWGCtrl.Instance:OpenTianShenZhenFaView()
end

function TianShenView:OnClickPeiYangBtn()
	-- 打开神通面板
	if ViewManager.Instance:IsOpen(GuideModuleName.TianShenShenTongView) then
		ViewManager.Instance:Close(GuideModuleName.TianShenShenTongView)
	end
	FunOpen.Instance:OpenViewByName(GuideModuleName.TianShenShenTongView)
end

--天神升级提示
function TianShenView:OnClickBtnTianShenTip()
	RuleTip.Instance:SetContent(Language.TianShen.TianShenUpTips2, Language.TianShen.TianShenUpTips1)
end

function TianShenView:PlaySucEffect(name, node)
	TipWGCtrl.Instance:ShowEffect({effect_type = name, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["effect_content"]})
end

function TianShenView:PlayShengxingEff(star)
	if self.is_play_shengxing then return end
	local silver_star_num = star % GameEnum.ITEM_MAX_STAR
	if star > 0 and silver_star_num == 0 then
		silver_star_num = GameEnum.ITEM_MAX_STAR
    end

	self.is_play_shengxing = true
    if self.shengxing_timer then
        GlobalTimerQuest:CancelQuest(self.shengxing_timer)
        self.shengxing_timer = nil
    end
	self.shengxing_timer = GlobalTimerQuest:AddDelayTimer(function()
    	self.is_play_shengxing = false
        self:FlushShenXing()
        self:PlaySucEffect(UIEffectName.s_shengxing)
	end, 0)
end

function TianShenView:ShowIndexActivationXingXiang(param_t)
	local tianshen_cfg = nil
	if param_t and param_t.all then
		if param_t.all.to_ui_param then
			tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(tonumber(param_t.all.to_ui_param))
			self.select_skill = tonumber(param_t.all.to_ui_name)
		else
			local min_info = TianShenWGData.Instance:GetDefaultOpenActiveSelect()
			if not IsEmptyTable(min_info) and not self.tianshen_activition_first_open then
				tianshen_cfg = min_info
			else

				tianshen_cfg = param_t.all
			end
		end
	end
	self.tianshen_activition_first_open = true
	local need = false
	if nil == self.activation_series then
		self.activation_select_index = 1
		self.activation_series = TianShenWGData.Instance:GetCurUseImageToggleIndex()
		need = true
	end
	if tianshen_cfg and not IsEmptyTable(tianshen_cfg) and tianshen_cfg.series then
		local ss_list = TianShenWGData.Instance:GetMagicImageListCfg(tianshen_cfg.series)
		self.activation_series = tianshen_cfg.series
		for k,v in pairs(ss_list) do
			if v.index == tianshen_cfg.index then
				self.activation_select_index = tonumber(k)
				self.activation_series = tianshen_cfg.series
				need = true
				break
			end
		end
	end
end

function TianShenView:FlushActivationImageRender(index)
	if not index then
		local select_data = self:GetActivationCurSelectListData()
		if not select_data then return end
		index = select_data.index
	end
end

function TianShenView:FlushActivationXingXiangRenderClick(cell)
	if cell == nil then
		return
	end

	local data = cell:GetData()
	TianShenWGData.Instance:ActivationTsSelectIndex(data.index)
	if self.active_ts_select_index == data.index then
		return
	end

	self.old_grade = nil
	self.old_star = nil
	self.old_cao = nil
	self.is_oneKey = false
	self.active_ts_select_index = data.index
	self:GetActivationCurSelectListData(TianShenWGData.Instance:GetTianShenCfg(data.index))
	self:FlushActivationAllPart()
end

function TianShenView:FlushActivationAllPart()
	self:FlushActivationModelDisPlay()
	self:FlushImagePasvSkill()
	self:StartUpgrade()
end

function TianShenView:ChangeToShengXing()
	self.node_list.btn_shengxing.toggle.isOn = true
	self.upgrade_type = TianShen_Upgrade_Type.ShengXing
	self.node_list.btn_shengjie.toggle.isOn = false
	self:FlushActivationAllPart()
end

function TianShenView:ChangeToShengJie()
	self.node_list.btn_shengjie.toggle.isOn = true
	self.upgrade_type = TianShen_Upgrade_Type.ShengJie
	self.node_list.btn_shengxing.toggle.isOn = false
	self:FlushActivationAllPart()
end

-- 刷新天神技能
function TianShenView:FlushImagePasvSkill()
	local select_data = self:GetActivationCurSelectListData()
    if not select_data then return end
	
	local ts_active_status = TianShenWGData.Instance:GetTianshenInfoStatus(select_data.index)
    local zhu_skill = TianShenWGData.Instance:GetTianShenZhuSkill(select_data.index)
    for k,v in pairs(self.ac_skill_list) do
        local skill_id = tonumber(zhu_skill[k])
        v:SetData({skill_id = skill_id, tianshen_index = select_data.index, from_view = FROM_TIANSHEN_UPGRADE, is_open_skill = ts_active_status == 1, is_tianshen_select_view = false})
    end
end

function TianShenView:FlushShenXing()
	if self.is_play_shengxing then return end
	if not self.acti_select_data then return end

	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.acti_select_data.index)
	local data = TianShenWGData.Instance:GetTianShenCfg(self.acti_select_data.index)
	local is_activate = TianShenWGData.Instance:IsActivation(self.acti_select_data.index)
	local tianshen_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.acti_select_data.index)
	local shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.acti_select_data.index)
	if tianshen_info then
		local ts_star_res_list = GetStarImgResByStar(tianshen_info.star)
		for i = 1, GameEnum.ITEM_MAX_STAR do
			self.ts_star_list[i].image:LoadSprite(ResPath.GetCommonImages(ts_star_res_list[i]))
		end
	end
	self.node_list.ts_active_stars:SetActive(self.upgrade_type == TianShen_Upgrade_Type.ShengXing and is_activate)
end

--刷新模型展示
function TianShenView:FlushActivationModelDisPlay()
	local data = self:GetActivationCurSelectListData()
	if data then
		local audio = self.tianshen_active_audio_play_t[data.index] == nil and data.show_audio or nil
		self.tianshen_active_audio_play_t[data.index] = 1
		---添加化魔展示
		local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(data.index) or data.appe_image_id
        local shiqi_scale = data.shiqi_scale or 1
		if self.last_show_tianshen_id and appe_image_id == self.last_show_tianshen_id then
			return
		end

		self.last_show_tianshen_id = appe_image_id
		local is_have_shuangsheng, app_image_id_data = TianShenWGData.Instance:CheckHaveShuangShengTianShen(data.index)
        self.activation_role_model:RemoveShuangShengTianShenUI(true)
        self.activation_role_model:SetTianShenModel(appe_image_id, data.index, true, audio, SceneObjAnimator.Rest, function ()
            if is_have_shuangsheng and app_image_id_data ~= nil then
                self.activation_role_model:TryChangePartScale(SceneObjPart.Main, app_image_id_data.model_scale)
            end
        end, app_image_id_data and app_image_id_data.shiqi_scale or shiqi_scale)

		if is_have_shuangsheng and app_image_id_data ~= nil then
			-- 这里加多一项改变大小，相同的资源未变化会导致不会回调
			self.activation_role_model:TryChangePartScale(SceneObjPart.Main, app_image_id_data.model_scale)
            self.activation_role_model:SetShuangShengTianShenModel(app_image_id_data.appe_image_id)
        else
            self.activation_role_model:TryResetChangePartScale(SceneObjPart.Main)
            self.activation_role_model:TryResetChangePartScale(SceneObjPart.Weapon)
        end
	end
end

function TianShenView:ShowAudio(show_audio)
	if nil == show_audio or "" == show_audio then
		return
	end
	local bundle, asset = ResPath.GetTianShenVoice(show_audio)

	AudioManager.PlayAndForget(bundle, asset)
end

--激活/提升按钮点击
function TianShenView:OnAppearanceUpLevelClick()
	if not self.acti_select_data then return end
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.acti_select_data.index)
	if not tianshen_info then
		return
	end

	local item = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.acti_select_data.index)
	local star = TianShenWGData.Instance:GetTianShenStar1(self.acti_select_data.index, tianshen_info.star)
	local upgrade = TianShenWGData.Instance:GetTianShenUpgrade(self.acti_select_data.index, tianshen_info.level)
	if not item or not star or not upgrade then
		print_error("暂无配置！", not item, not star, not upgrade)
		return
	end

	local item_id1 = nil
	local item_id2 = nil
	local item_id3 = nil
	local item_count = 0
	local item_count2 = 0
	local item_count3 = 0
	local need_count = 0
	if not TianShenWGData.Instance:IsActivation(self.acti_select_data.index) then
		self.is_oneKey = false
		item_id1 = item.act_item_id
		need_count = item.act_item_cost
		item_count = ItemWGData.Instance:GetItemNumInBagById(item_id1)
		if item_count < need_count then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id1})
			return
		end
		TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type6, self.acti_select_data.index)
	elseif self.upgrade_type == TianShen_Upgrade_Type.ShengXing then
		self.is_oneKey = false
		item_id1 = item.star_item_id
		need_count = star.star_cost

		item_count = ItemWGData.Instance:GetItemNumInBagById(item_id1)
		if item_count < need_count then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id1})
			return
		end

		TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type5, self.acti_select_data.index)
	elseif self.upgrade_type == TianShen_Upgrade_Type.ShengJie then
		self.is_oneKey = not self.is_oneKey
		item_id1 = item.upgrade_item_id1
		item_id2 = item.upgrade_item_id2
		item_id3 = item.upgrade_item_id3
		item_count = ItemWGData.Instance:GetItemNumInBagById(item_id1)
		item_count2 = ItemWGData.Instance:GetItemNumInBagById(item_id2)
		item_count3 = ItemWGData.Instance:GetItemNumInBagById(item_id3)
		if item_count + item_count2 + item_count3 <= 0 then
			self.is_oneKey = false
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id1})
			return
		end
		self:StartUpgrade()
		-- TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type1, self.acti_select_data.index)
	end
end

function TianShenView:GetActivationCurSelectListData(select_data)
	if nil == select_data then
		if self.acti_select_data then
			TianShenWGData.Instance:SetCurSelectTianShenIndex(self.acti_select_data.index)
		end
		return self.acti_select_data
	end

	--print_error("select_data =", select_data)
	self.acti_select_data = select_data
	TianShenWGData.Instance:SetCurSelectTianShenIndex(self.acti_select_data.index)
end

function TianShenView:OnClickSkillYuLanBtn()
    CommonSkillShowCtrl.Instance:SetTianShenSkillViewDataAndOpen({tianshen_index = self.active_ts_select_index})
end

function TianShenView:OnClickTSShangZhenBtn()
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type3, self.active_ts_select_index, 0)
end

-- 点击升星页签
function TianShenView:OnShengXingClick(ison)
	if ison then
		if self.upgrade_type == TianShen_Upgrade_Type.ShengXing then return end
		self.is_oneKey = false
		self.upgrade_type = TianShen_Upgrade_Type.ShengXing
		self:StartUpgrade()
	end
end

-- 点击升阶页签
function TianShenView:OpenShengJieClick(ison)
	if ison then
		-- 如果未激活则选中升星
		local select_data = self:GetActivationCurSelectListData()
		if select_data then 
			local is_selected_ts_active = TianShenWGData.Instance:IsActivation(select_data.index)
			if not is_selected_ts_active then
				TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.TianShenNoActive)
				self.upgrade_type = TianShen_Upgrade_Type.ShengXing
				self:StartUpgrade()
				--self.node_list.btn_shengxing.toggle.isOn = true
				--self.node_list.btn_shengjie.toggle.isOn = false
				return
			end
		end

		if self.upgrade_type ~= TianShen_Upgrade_Type.ShengJie then
			self.is_oneKey = false
			self.upgrade_type = TianShen_Upgrade_Type.ShengJie
			self:StartUpgrade()
		end
	end
end

function TianShenView:FlushConsume()
	-- body
	if not self.acti_select_data then return end
	local tianshen_data = TianShenWGData.Instance
	local tianshen_info = tianshen_data:GetTianShenInfoByIndex(self.acti_select_data.index)
	local item = tianshen_data:GetTianshenItemCfgByIndex(self.acti_select_data.index)

	local star = tianshen_data:GetTianShenStar1(self.acti_select_data.index, tianshen_info.star)

	if not item or not star then
		print_error("暂无配置！")
		return
	end
	if not self.node_list or not self.node_list.upstar_toggle_remind then
		return
	end

	self.node_list.upstar_toggle_remind:SetActive(tianshen_data:SpecialTianshenCanUpStar(self.acti_select_data.index))
	self.node_list.shengjie_toggle_remind:SetActive(tianshen_data:SpecialTianshenCanUpGrade(self.acti_select_data.index))
	self:SetXingXiangRemind(tianshen_data:SpecialTianshenCanActive(self.acti_select_data.index) or
		(self.upgrade_type == TianShen_Upgrade_Type.ShengXing and tianshen_data:SpecialTianshenCanUpStar(self.acti_select_data.index))
			or (self.upgrade_type == TianShen_Upgrade_Type.ShengJie and tianshen_data:SpecialTianshenCanUpGrade(self.acti_select_data.index)))

	local item_id1 = nil
	local item_id2 = nil
	local item_id3 = nil
	local item_count = 0
	local need_count = nil

	if not tianshen_data:IsActivation(self.acti_select_data.index) then
		item_id1 = item.act_item_id
		need_count = item.act_item_cost
	elseif self.upgrade_type == TianShen_Upgrade_Type.ShengXing then
		--if tianshen_info.cao == TIANSHEN_CAO_MAX then
			need_count = star.star_cost
			item_id1 = item.star_item_id
		--[[else
			need_count = star.cao_cost
			item_id1 = item.cao_item_id
		end--]]
	elseif self.upgrade_type == TianShen_Upgrade_Type.ShengJie then
		item_id1 = item.upgrade_item_id1
		item_id2 = item.upgrade_item_id2
		item_id3 = item.upgrade_item_id3
	end

	item_count = ItemWGData.Instance:GetItemNumInBagById(item_id1)
	self.consume_list[1]:SetData({item_id = item_id1, num = item_count})

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id1)
	if item_cfg and item_cfg.is_display_role and item_cfg.is_display_role <= 0 then
		self.consume_list[1]:SetIsShowTips(false)

		self.consume_list[1]:SetClickCallBack(function ()
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id1})
		end)
	else
		self.consume_list[1]:SetClickCallBack(nil)
		self.consume_list[1]:SetIsShowTips(true)
	end

	if need_count then
		local str = string.format("%s/%s",item_count, need_count)
		local color = item_count >= need_count and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		self.consume_list[1]:SetRightBottomColorText(str, color)
	end

	self.node_list["ph_bianshen_pos_2"]:SetActive(nil ~= item_id2)
	if item_id2 then
		item_count = ItemWGData.Instance:GetItemNumInBagById(item_id2)
		self.consume_list[2]:SetData({item_id = item_id2, num = item_count})

		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id2)
		if item_cfg and item_cfg.is_display_role and item_cfg.is_display_role <= 0 then
			self.consume_list[2]:SetIsShowTips(false)

			self.consume_list[2]:SetClickCallBack(function ()
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id2})
			end)
		else
			self.consume_list[2]:SetClickCallBack(nil)
			self.consume_list[2]:SetIsShowTips(true)
		end
	end

	self.node_list["ph_bianshen_pos_3"]:SetActive(nil ~= item_id3)
	if item_id3 then
		item_count = ItemWGData.Instance:GetItemNumInBagById(item_id3)
		self.consume_list[3]:SetData({item_id = item_id3, num = item_count})

		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id3)
		if item_cfg and item_cfg.is_display_role and item_cfg.is_display_role <= 0 then
			self.consume_list[3]:SetIsShowTips(false)

			self.consume_list[3]:SetClickCallBack(function ()
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id3})
			end)
		else
			self.consume_list[3]:SetClickCallBack(nil)
			self.consume_list[3]:SetIsShowTips(true)
		end
	end

	--self.node_list.bianshen_upgrade_itemlist.rect.anchoredPosition = Vector2(0, (not item_id2 and not item_id3) and 35 or 15)
end

function TianShenView:FlushActivationSlider()
	if not self.acti_select_data then return end
	local cur_data = TianShenWGData.Instance:GetTianShenInfoByIndex(self.acti_select_data.index)
	local old_data = TianShenWGData.Instance:GetOldTianShenInfoByIndex(self.acti_select_data.index)
	local cur_exp = cur_data.uplevel_exp_val
	local cur_level = cur_data.level
	local old_exp = old_data.uplevel_exp_val
	local old_level = old_data.level

	local level_add = cur_level - old_level

	local curr_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(self.acti_select_data.index, cur_data.level)
	local next_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(self.acti_select_data.index, cur_data.level + 1)

	local add_exp_num = 0
	for i=1,level_add do
		add_exp_num = add_exp_num + TianShenWGData.Instance:GetTianShenUpgrade(self.acti_select_data.index, old_level + i - 1).upgrade_need_exp
	end
	add_exp_num = add_exp_num - old_exp + cur_exp

	local need_exp = curr_upgrade_cfg.upgrade_need_exp

	self:PlayAni(need_exp, next_upgrade_cfg == nil, level_add, old_level, cur_exp, add_exp_num)

	--刷新进度条
	local is_max_level = next_upgrade_cfg == nil
	if not is_max_level then
		self.node_list["lbl_upstar_progress_num"].text.text = cur_data.uplevel_exp_val .. "/" .. need_exp
	else
		self.node_list["lbl_upstar_progress_num"].text.text = "-- / --"
	end

end

-- 播放动画
function TianShenView:PlayAni(total_exp, is_max_level, level_add, slider_level, exp, add_exp_num)
	if self.is_play_ani then return end
	self.is_play_ani = true
	local add_value = level_add + exp/total_exp

	if not self.first_tween and add_exp_num and add_exp_num > 0 then
		TipWGCtrl.Instance:ShowNumberMsg(string.format(Language.Bag.GetRoleItemTxt2, add_exp_num), nil, Vector2(560,62))
	end
	self.first_tween = false

	if is_max_level then
		self:FlushAttrView(slider_level + level_add)
		local tween_max = self.node_list["prog_upstar_progress"].slider:DOValue(1,1/2)
		tween_max:SetId('slider_tween')
		tween_max:OnComplete(function ()
			self.is_play_ani = false
			self.is_oneKey = false
			self:StartUpgrade()
		end)
		return
	end
	if level_add < 1 then
		self:FlushAttrView(slider_level)
	end

	self.func = function (add_value)
		if add_value <= 0 then
			self.is_play_ani = false
			self:StartUpgrade()
			return
		end

		local tween
		if add_value > 1 then
			local value = 1 - self.node_list["prog_upstar_progress"].slider.value
			tween = self.node_list["prog_upstar_progress"].slider:DOValue(1,value/2)

		else
			local value = add_value - self.node_list["prog_upstar_progress"].slider.value
			tween = self.node_list["prog_upstar_progress"].slider:DOValue(add_value,value/2)
		end
		self.slider_tween = tween
		add_value = add_value - 1

		tween:OnComplete(function ()
			if add_value >= 0 then
				self.node_list["prog_upstar_progress"].slider.value = 0
				slider_level = slider_level + 1
				self:FlushAttrView(slider_level, add_value > 1)
			end
			if add_value < 1 then
				MainuiWGCtrl.Instance:DelayShowCachePower(0) --延时调用
			end
			self.slider_tween = nil
			self.func(add_value)
		end)
	end
	self.func(add_value)
end

-- 开始升阶
function TianShenView:StartUpgrade()
	if not self:IsLoadedIndex(0) then return end
	local select_data = self:GetActivationCurSelectListData()
	if not select_data then return end

	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(select_data.index)
	if not tianshen_info then return end
	
	local is_chuzhan = TianShenWGData.Instance:GetTianShenIsChuZhanByIndex(select_data.index)
	self.node_list["ac_ts_sz_btn"]:SetActive(not is_chuzhan) -- 上阵按钮

	local cap_score = tianshen_info.capability_score
	self.node_list["tianshen_cap_score"]:SetActive(cap_score > 0)
	if cap_score > 0 then
		self.node_list["tianshen_cap_score"].text.text = string.format(Language.TianShen.TSCapScore, cap_score)
	end
	
	if self.old_select_tianshen_index ~= nil and self.old_is_shangzheng ~= nil 
		and self.old_select_tianshen_index == select_data.index and self.old_is_shangzheng ~= is_chuzhan and is_chuzhan then
		self:PlaySucEffect(UIEffectName.s_shangzhen)
	end

	self.old_is_shangzheng = is_chuzhan
	self.old_select_tianshen_index = select_data.index
	--[[ 新版UI 不需要显示技能信息
	local is_show = false
    if self.ts_act_list_view ~= nil then
    	local id = tianshen_data:GetTianShenSkillInfoNeedShow(select_data.index, tianshen_info.star)
		for k, v in pairs(self.ts_act_list_view:GetDataList()) do
			is_show = v.index == select_data.index and v.active_status == 1 and self.upgrade_type == TianShen_Upgrade_Type.ShengXing and id ~= nil
			if is_show then
				break
			end
		end
	end
    self.node_list.ac_skill_info:SetActive(is_show)
	]]

	local next_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(self.acti_select_data.index, tianshen_info.level + 1)

	if self.old_grade and self.old_grade < tianshen_info.level and self.old_grade > 0 then
		self:PlaySucEffect(UIEffectName.s_jinjie)
	end
	if self.old_star and self.old_star < tianshen_info.star then
		self.is_oneKey = false
		self:PlayShengxingEff(tianshen_info.star)
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.acti_select_data.index)
		local appe_image_id_new = tianshen_cfg and tianshen_cfg.appe_image_id
		AppearanceWGCtrl.Instance:OnGetNewAppearance({appe_type = ROLE_APPE_TYPE.BIANSHEN, appe_image_id = appe_image_id_new}) --模拟服务器下发
	elseif self.old_cao and self.old_cao < tianshen_info.cao then
		self.is_oneKey = false
		self:PlaySucEffect(UIEffectName.s_shengxing)
	end
	self.old_grade = tianshen_info.level
	self.old_star = tianshen_info.star
	self.old_cao = tianshen_info.cao

	--重置状态标记，下面该设置的时候再设置
	self.node_list["img_star_max"]:SetActive(false)
	self.node_list["img_level_max"]:SetActive(false)
	self.node_list["btn_bianshen_upstar"]:SetActive(true)
	self.node_list["lbl_cost_title"].text.text = self.upgrade_type == TianShen_Upgrade_Type.ShengJie and Language.TianShen.UpgradeText[2] or Language.TianShen.UpgradeText[4]
	XUI.SetButtonEnabled(self.node_list["btn_bianshen_upstar"], true)
	
	local ts_is_active = TianShenWGData.Instance:IsActivation(select_data.index)
	if not ts_is_active then --未激活
		self.node_list.btn_shengxing.toggle.isOn = true
		self.node_list.btn_shengjie.toggle.isOn = false
		self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[7]
	elseif self.upgrade_type == TianShen_Upgrade_Type.ShengXing then --升星
		if tianshen_info.star == TIANSHEN_STAR_MAX then
			self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[6]
			self.node_list["img_star_max"]:SetActive(true)
			self.node_list["btn_bianshen_upstar"]:SetActive(false)
		elseif tianshen_info.cao < TIANSHEN_CAO_MAX then
			self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[5]
		else
			self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[5]
		end
	elseif self.upgrade_type == TianShen_Upgrade_Type.ShengJie then --升阶
		if not next_upgrade_cfg then
			self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[3]
			self.node_list["img_level_max"]:SetActive(true)
			self.node_list["btn_bianshen_upstar"]:SetActive(false)
			self.is_oneKey = false
		elseif self.is_oneKey then
			if self:GeConsumeItemSelect() then
				TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type1, self.acti_select_data.index)
				self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[2]
			else
				self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[1]
				self.is_oneKey = false
			end
		else
			self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[1]
		end
	else
		self.node_list["lbl_btn_bianshen_tex"].text.text = Language.TianShen.OneKeyBtnText[1]
	end
	self:FlushShenXing()
	self:FlushSlider()
	self:FlushAttrView()
	self:FlushConsume()
end

function TianShenView:GeConsumeItemSelect()
	if self.consume_list then
		for i,v in ipairs(self.consume_list) do
	  		if v.data and v.data.item_id and ItemWGData.Instance:GetItemNumInBagById(v.data.item_id) > 0 then
	  			return true
	  		end
	  	end
  	end
  	return false
end

--幻装红点更新
function TianShenView:SetXingXiangRemind( enable )
	self.node_list["img_bianshen_remind"]:SetActive(enable)
end

function TianShenView:OnClickResetHuaMo()
	if self.acti_select_data then
		local cur_index = self.acti_select_data.index
		---添加化魔展示
		local _, huanhua_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(cur_index)
		if huanhua_id == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenHuaMo.ResetError)
			return
		end
		TianShenHuamoWGCtrl.Instance:CSTianShenRuMoHuanHua(cur_index, 0)
	end
end

---------------------------------------- 
-- TianShenItemRender 天神ListItem基类
----------------------------------------
TianShenItemRender = TianShenItemRender or BaseClass(BaseRender)

function TianShenItemRender:OnFlush()
	if self.data == nil then return end

	self.node_list.bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zs_pz_" .. self.data.series))
	self.node_list.img_ts_icon.image:LoadSprite(ResPath.GetNoPackPNG("ts_" .. self.data.index))

	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.index)
	if not tianshen_info then
		return
	end

	if self.data.active_status == 1 then
		local star_res_list = GetSpecialStarImgResByStar2(tianshen_info.star)
		for i = 1, GameEnum.ITEM_MAX_STAR do
			self.node_list["star" .. i]:SetActive(true)
			self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
		end
	end

	self.node_list.lbl_level:SetActive(self.data.active_status == 1)
	self.node_list.star:SetActive(self.data.active_status == 1)
	self.node_list.no_get_sign:SetActive(self.data.active_status == 0)
	self.node_list.lbl_ml_item_name.text.text = self.data.bianshen_name
	self.node_list.lbl_level.text.text = string.format(Language.TianShen.Level, tianshen_info.level)
	self.node_list.img_battle:SetActive(tianshen_info.zhan_index >= 0) -- 是否出战
end

function TianShenItemRender:OnSelectChange(is_select)
	self.node_list.img_hl:SetActive(is_select)
end

---------------------------------------- 
-- TianShenActItemRender
----------------------------------------
TianShenActItemRender = TianShenActItemRender or BaseClass(TianShenItemRender)

function TianShenActItemRender:OnFlush()
	TianShenItemRender.OnFlush(self)
	if self.data == nil then return end

	local tianshen_wg_data = TianShenWGData.Instance
	local is_remind = false
	if tianshen_wg_data:SpecialTianshenCanActive(self.data.index) or 
		tianshen_wg_data:SpecialTianshenCanUpStar(self.data.index) or
		tianshen_wg_data:SpecialTianshenCanUpGrade(self.data.index) then
		is_remind = true
	end
	self.node_list.remind:SetActive(is_remind)
end

-------------------------------------------
-- 形象技能item
-------------------------------------------
TianShenSkillItem = TianShenSkillItem or BaseClass(BaseRender)
--是否默认显示技能
function TianShenSkillItem:SetDefaultFlag( state )
	self.default_flag = state
	self.is_grey = false
end

function TianShenSkillItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["Img"], BindTool.Bind(self.OnClick,self))
	XUI.AddClickEventListener(self.node_list.skill_lock,BindTool.Bind(self.OnClick,self))
end

function TianShenSkillItem:OnFlush()
	local data = self:GetData()
	if not data or IsEmptyTable(data) then
		local bundel, asset = ResPath.GetCommonImages("a2_ty_suo")
		self.node_list["img_skill_icon"].image:LoadSprite(bundel, asset, function ()
			self.node_list["img_skill_icon"].image:SetNativeSize()
		end)
		self.node_list["Img"].button.interactable = false
		return
	end
	local cur_tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(data.tianshen_index)
	local is_jiban_skill = data.skill_id == cur_tianshen_cfg.jiban_skill
	local jiban_tianshen_index = nil
	local jiban_tishen_active = nil
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(data.tianshen_index)
	local shenshi_rank = tianshen_info and tianshen_info.star or 0--tianshen_shenshi_data.jingshen
	if is_jiban_skill then
		jiban_tianshen_index = TianShenWGData.Instance:GetTianShenJiBanSkillAct(data.tianshen_index, shenshi_rank, data.skill_id)
		jiban_tishen_active = jiban_tianshen_index and TianShenWGData.Instance:IsActivation(jiban_tianshen_index) or false
	end
	

	local skill_level = TianShenWGData.Instance:GetTianShenSkillLv(data.tianshen_index, shenshi_rank, data.skill_id) or 1

	if not self.old_skill_id or self.old_skill_id ~= data.skill_id then
		self.old_skill_id = data.skill_id
		self.old_skill_lv = skill_level
	else
		if self.old_skill_lv ~= skill_level then
			if self.old_skill_lv and self.old_skill_lv ~= nil and data.from_view == FROM_TIANSHEN_SHENSHI then -- 技能升级只需在神饰界面播放特效（技能等级只能改变神饰等级）
				self:SkillActiveEffect()
			end
			self.old_skill_lv = skill_level
		end
	end

	self.cfg = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id, skill_level)
	if self.cfg then
		local bundel, asset = ResPath.GetSkillIconById(self.cfg.icon_resource)

		self.node_list["img_skill_icon"].image:LoadSprite(bundel, asset, function ()
				--self.node_list["img_skill_icon"].rect.sizeDelta = Vector2(COMMON_CONSTS.ItemCellSize, COMMON_CONSTS.ItemCellSize)
			end)
		-- local skill_type_tab = Split(self.cfg.damage_icon,"|")
		-- if skill_type_tab and skill_type_tab[1] and tonumber(skill_type_tab[1]) ~= 0 then
		-- 	self.node_list["skill_type"]:SetActive(true)
		-- 	local s_asset,s_bound = ResPath.GetF2CommonImages("m_skill_type"..skill_type_tab[1])
		-- 	self.node_list["skill_type"].image:LoadSprite(s_asset,s_bound)
		-- else
			--self.node_list["skill_type"]:SetActive(false)
		-- end


		--self.node_list["Img"].button.interactable = true
		if not is_jiban_skill then
			self.node_list.skill_lock:SetActive(not ((self.default_flag and not self.is_grey) or self.data.is_open_skill))
		else
			self.node_list.skill_lock:SetActive(not ((self.default_flag and not self.is_grey) or (self.data.is_open_skill and jiban_tishen_active)))
		end
	end

	if data.is_shuangsheng then
		local TianShenAvatarCfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarCfgData(data.tianshen_index)
		if TianShenAvatarCfg then
			local icon_resource = TianShenAvatarCfg[string.format("icon_resource_%d", self.index)]
			local bundel, asset = ResPath.GetSkillIconById(icon_resource)
			self.node_list["img_skill_icon"].image:LoadSprite(bundel, asset, function ()
				--self.node_list["img_skill_icon"].rect.sizeDelta = Vector2(COMMON_CONSTS.ItemCellSize, COMMON_CONSTS.ItemCellSize)
			end)
		end
	end

	local ts_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(data.skill_id, skill_level and skill_level or 1)
	local limit_text = ts_skill_cfg.summary_txt
	self.node_list.skill_name.text.text = limit_text
	if skill_level then
		self.node_list.skill_level.text.text = skill_level
	end
end

function TianShenSkillItem:SkillActiveEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tianshen_jiesuo)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.skill_effect.transform, 2)
end

function TianShenSkillItem:OnClick()
	local data = self:GetData()
	if not data or IsEmptyTable(data) or nil == self.cfg then
		return
	end

	local cur_tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(data.tianshen_index)
	local is_jiban_skill = data.skill_id == cur_tianshen_cfg.jiban_skill
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(data.tianshen_index)
	local shenshi_rank = tianshen_info and tianshen_info.star or 0
	local jiban_tianshen_index = nil
	local jiban_tishen_active = nil
	local next_rank = TianShenWGData.Instance:GetTianShenSkillNextLv(data.tianshen_index,shenshi_rank,data.skill_id)
	if is_jiban_skill then
	 	jiban_tianshen_index = TianShenWGData.Instance:GetTianShenJiBanSkillAct(data.tianshen_index, shenshi_rank, data.skill_id)
		jiban_tishen_active = jiban_tianshen_index and TianShenWGData.Instance:IsActivation(jiban_tianshen_index) or false
	end
	-- local skill_describe = self.cfg.description
	local limit_text = ""
	local cur_ts_skill_des = nil
	local jiabn_ts_cfg = nil
	if data.is_open_skill ~= nil and false == data.is_open_skill and data.is_tianshen_select_view == false then
		if is_jiban_skill then
			jiabn_ts_cfg = TianShenWGData.Instance:GetTianShenCfg(jiban_tianshen_index)
			limit_text = string.format(Language.TianShen.TianShenTips6,ToColorStr(jiabn_ts_cfg.bianshen_name,TIANSHEN_DARK_COLOR3B[jiabn_ts_cfg.series]))
		else
			limit_text = Language.TianShen.TianShenTips2
		end
	else
		if is_jiban_skill and  not jiban_tishen_active then
			jiabn_ts_cfg = TianShenWGData.Instance:GetTianShenCfg(jiban_tianshen_index)
			limit_text = string.format(Language.TianShen.TianShenTips6,ToColorStr(jiabn_ts_cfg.bianshen_name,TIANSHEN_DARK_COLOR3B[jiabn_ts_cfg.series]))
		else
			if next_rank then -- 激活沒滿級
				cur_ts_skill_des = string.format(Language.TianShen.TianShenTips7,next_rank)
			else              --激活满级
				limit_text = "" 
			end
		end
	end
	-- local parent_view = self:GetParentView()
	-- local ts_index = parent_view:GetActivationCurSelectListData() and parent_view:GetActivationCurSelectListData().index or 0
	-- local skill_info = TianShenWGData.Instance:GetTianShensMainkill(ts_index, data.skill_id)

	-- local skill_level = skill_info and skill_info.level or 1
	local skill_level = TianShenWGData.Instance:GetTianShenSkillLv(data.tianshen_index, shenshi_rank, data.skill_id)
	local ts_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(data.skill_id, skill_level and skill_level or 1)
	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(data.skill_id)
	local skill_type_tab = Split(client_cfg.damage_icon,"|")
	local skill_name = ""
	if ts_skill_cfg then
		skill_name = ts_skill_cfg.skill_name -- ToColorStr(ts_skill_cfg.skill_name,TIANSHEN_DARK_COLOR3B[cur_tianshen_cfg.series])  
	end

	local other_hurt_percent = 0
	local other_description = nil
	local other_skill_icon = nil

	if data.is_shuangsheng then
		local cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(data.avatar_id)

		if cfg then
			other_hurt_percent = cfg.inc_hurt_fix or 0
		end

		local TianShenAvatarCfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarCfgData(data.tianshen_index)
		if TianShenAvatarCfg then
			other_description = TianShenAvatarCfg[string.format("description_%d", self.index)]
			other_skill_icon = TianShenAvatarCfg[string.format("icon_resource_%d", self.index)]
		end
	end

	local show_data = {
		icon = other_skill_icon ~= nil and other_skill_icon or self.cfg.icon_resource,
		top_text = skill_name,
		body_text = "",
		limit_text = limit_text,
		tianshen_index = data.tianshen_index,
		skill_id = data.skill_id,
		eff_url = UIEffectName.s_juexing,
		x = 0,
		y = 0,
		set_pos2 = true,
		skill_box_type = SKILL_BOX_TYPE.TIANSHEN_SKILL,
		capability = ts_skill_cfg.capability_inc,
		ts_skill_des = cur_ts_skill_des,
		other_hurt_percent = other_hurt_percent,
		other_description = other_description,
		is_active_skill = true,
	}

	if data.from_view == FROM_TIANSHEN_SHENSHI then
		show_data.show_bottom = 0--TianShenWGData.Instance:CheckSkillIsUpGradeMax(skill_id,ts_index) and 0 or 1
	end
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function TianShenSkillItem:SetGrey(is_grey)
	self.is_grey = is_grey
	if self.view == nil or self.data == nil then return end

end

function TianShenSkillItem:GetParentView()
	-- body
	return ViewManager.Instance:GetView(GuideModuleName.TianShenView)
end