require("game/country_map/yanglongsi/yanglongsi_view")
require("game/country_map/yanglongsi/yanglongsi_rank_reward_view")
require("game/country_map/yanglongsi/yanglongsi_wg_data")
require("game/country_map/yanglongsi/yanglongsi_task_view")
require("game/country_map/yanglongsi/yanglongsi_lsgw_view")
require("game/country_map/yanglongsi/yanglongsi_lsgw_rank_view")
require("game/country_map/yanglongsi/yanglongsi_boss_notice_view")

YangLongSiWGCtrl = YangLongSiWGCtrl or BaseClass(BaseWGCtrl)

function YangLongSiWGCtrl:__init()
	if YangLongSiWGCtrl.Instance ~= nil then
		ErrorLog("[YangLongSiWGCtrl] attempt to create singleton twice!")
		return
	end
	YangLongSiWGCtrl.Instance = self

	self.data = YangLongSiaWGData.New()
	self.rank_reward_view = YangLongSiRankRewardView.New()
	self.yanglongsi_task_view = YangLongSiTaskView.New()
	self.yanglongsi_lsgw_view = YangLongSiLSGWView.New()
	self.yanglongsi_lsgw_rank_view = YangLongSiLSGWRankView.New()
	self.yanglongsi_bossnotice_view = YangLongSiBossNoticeView.New()
	self.get_reward_index = -1

	self.act_cur_step = -1
	self.is_mainui_btn_show = false
	self:RegisterAllProtocols()

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function YangLongSiWGCtrl:__delete()
	YangLongSiWGCtrl.Instance = nil
	self:CleanMianUIBtnRemoveCDTimer()

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.rank_reward_view then
		self.rank_reward_view:DeleteMe()
		self.rank_reward_view = nil
	end

	if self.yanglongsi_task_view then
		self.yanglongsi_task_view:DeleteMe()
		self.yanglongsi_task_view = nil
	end

	if self.yanglongsi_lsgw_view then
		self.yanglongsi_lsgw_view:DeleteMe()
		self.yanglongsi_lsgw_view = nil
	end

	if self.yanglongsi_lsgw_rank_view then
		self.yanglongsi_lsgw_rank_view:DeleteMe()
		self.yanglongsi_lsgw_rank_view = nil
	end

	if self.yanglongsi_bossnotice_view then
		self.yanglongsi_bossnotice_view:DeleteMe()
		self.yanglongsi_bossnotice_view = nil
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function YangLongSiWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossYangLongOperate)
	self:RegisterProtocol(SCCrossYangLongTeamRankInfo,"OnSCCrossYangLongTeamRankInfo")
	self:RegisterProtocol(SCCrossYangLongRewardInfo,"OnSCCrossYangLongRewardInfo")
	self:RegisterProtocol(SCCrossYangLongSceneInfo,"OnSCCrossYangLongSceneInfo")
	self:RegisterProtocol(SCCrossYangLongScoreRankInfo,"OnSCCrossYangLongScoreRankInfo")
	self:RegisterProtocol(SCCrossYangLongBaseInfo,"OnSCCrossYangLongBaseInfo")
	self:RegisterProtocol(SCCrossYangLongRefreshRemind,"OnSCCrossYangLongRefreshRemind")
	self:RegisterProtocol(SCYangLongStartRefreshBossTime,"OnYangLongStartRefreshBossTime")
end

function YangLongSiWGCtrl:SendCSCrossYangLongOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossYangLongOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function YangLongSiWGCtrl:OnSCCrossYangLongTeamRankInfo(protocol)
	local data_state = self.data:SetTeamInfo(protocol)

	if self.yanglongsi_task_view and self.yanglongsi_task_view:IsOpen() then
		self.yanglongsi_task_view:Flush(0, nil, data_state)
	end
end

function YangLongSiWGCtrl:OnSCCrossYangLongRewardInfo(protocol)
	local get_reward_flag, get_reward_list = self.data:SetSCCrossYangLongRewardInfo(protocol)
	if get_reward_flag then
		local data_list = SortDataByItemColor(get_reward_list)
		TipWGCtrl.Instance:ShowGetReward(nil, data_list, nil, nil, nil)
	end

    RemindManager.Instance:Fire(RemindName.CrossYangLongSi)
	ViewManager.Instance:FlushView(GuideModuleName.CountryMapActView, TabIndex.country_map_yanglongsi)

	if self.yanglongsi_task_view and self.yanglongsi_task_view:IsOpen() then
		self.yanglongsi_task_view:Flush()
	end
end

function YangLongSiWGCtrl:OnSCCrossYangLongSceneInfo(protocol)
	self.data:SetBossStateInfo(protocol)
	if self.yanglongsi_task_view and self.yanglongsi_task_view:IsOpen() then
		self.yanglongsi_task_view:Flush()
	end
end

function YangLongSiWGCtrl:OpenRankRewardPanel()
	if self.rank_reward_view and not self.rank_reward_view:IsOpen() then
		self.rank_reward_view:Open()
	end
end

function YangLongSiWGCtrl:OpenTaskView()
	if self.yanglongsi_task_view and not self.yanglongsi_task_view:IsOpen() then
		self.yanglongsi_task_view:Open()
	end
end

function YangLongSiWGCtrl:CloseTaskView()
	if self.yanglongsi_task_view and self.yanglongsi_task_view:IsOpen() then
		self.yanglongsi_task_view:Close()
	end
end

function YangLongSiWGCtrl:GetTaskView()
	return self.yanglongsi_task_view
end

function YangLongSiWGCtrl:SetCurBossIndex(index)
	if self.yanglongsi_task_view and self.yanglongsi_task_view:IsOpen()  then
		self.yanglongsi_task_view:SetCurIndex(index)
	end
end

function YangLongSiWGCtrl:OpenYangLongSiLSGWView()
	if self.yanglongsi_lsgw_view and not self.yanglongsi_lsgw_view:IsOpen() then
		self.yanglongsi_lsgw_view:Open()
	end
end

function YangLongSiWGCtrl:OnSCCrossYangLongScoreRankInfo(protocol)
	self.data:SetLSGWRankInfo(protocol)

	if self.yanglongsi_lsgw_view and self.yanglongsi_lsgw_view:IsOpen() then
		self.yanglongsi_lsgw_view:Flush(0, "score_rank_info")
	end
end

function YangLongSiWGCtrl:OnSCCrossYangLongBaseInfo(protocol)
	self.data:SetLSGWBaseInfo(protocol)
    RemindManager.Instance:Fire(RemindName.CrossYangLongSi)
	ViewManager.Instance:FlushView(GuideModuleName.CountryMapActView, TabIndex.country_map_yanglongsi)
	if self.yanglongsi_lsgw_view and self.yanglongsi_lsgw_view:IsOpen() then
		self.yanglongsi_lsgw_view:Flush(0, "base_info")
	end

	self:CheckMianUIActBtnShow()
end

function YangLongSiWGCtrl:OnSCCrossYangLongRefreshRemind(protocol)
	if protocol.is_show_remind == 1 then
		local data = {}
		data.name = Language.YangLongSi.BossNoticeTitle
		data.desc = Language.YangLongSi.BossNoticeDesc
		data.boss_id = self.data:GetOtherCfg().show_boss_head_id
		data.click_call_back = function ()
			CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.KF_YANGLONGSI)
		end
		self:OpenBossNoticeView(data)
	end
end

function YangLongSiWGCtrl:OnYangLongStartRefreshBossTime()
	self:CheckMianUIActBtnShow()
end

function YangLongSiWGCtrl:OpenYangLongSiLSGWRankView()
	if self.yanglongsi_lsgw_rank_view and not self.yanglongsi_lsgw_rank_view:IsOpen() then
		self.yanglongsi_lsgw_rank_view:Open()
	end
end

function YangLongSiWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:CheckIsYangLongSiLSGWItem(change_item_id) then
			self.data:CalculateLSGWRemind()
			RemindManager.Instance:Fire(RemindName.CrossYangLongSi)
			ViewManager.Instance:FlushView(GuideModuleName.CountryMapActView, TabIndex.country_map_yanglongsi)

			if self.yanglongsi_lsgw_view and self.yanglongsi_lsgw_view:IsOpen() then
				self.yanglongsi_lsgw_view:Flush(0, "base_info")
			end
		end
	end
end

function YangLongSiWGCtrl:OpenBossNoticeView(data)
	if self.yanglongsi_bossnotice_view then
		self.yanglongsi_bossnotice_view:SetYangLongSiBossNoticeData(data)
		self.yanglongsi_bossnotice_view:Open()
		self.yanglongsi_bossnotice_view:Flush()
	end
end

function YangLongSiWGCtrl:CleanMianUIBtnRemoveCDTimer()
    if self.mainui_btn_remove_timer then
        GlobalTimerQuest:CancelQuest(self.mainui_btn_remove_timer)
        self.mainui_btn_remove_timer = nil
    end
end

function YangLongSiWGCtrl:CheckMianUIActBtnShow()
	local is_show, step, start_time, end_time = self.data:GetCurMianUIBtnStatus()
	if self.is_mainui_btn_show == is_show and self.act_cur_step == step then
		return
	end

	self:CleanMianUIBtnRemoveCDTimer()
	self.is_mainui_btn_show = is_show
	self.act_cur_step = step
	if is_show then
		self:ShowYangLongReflushIcon(start_time, end_time)
		local rest_time = end_time - TimeWGCtrl.Instance:GetServerTime()
		if rest_time > 0 then
			self.mainui_btn_remove_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self:CloseYangLongReflushIcon()
			end, rest_time)
		end
	else
		self:CloseYangLongReflushIcon()
	end
end

function YangLongSiWGCtrl:ShowYangLongReflushIcon(start_time, end_time)
    ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHEN_MI_HAI_YU_ICON, ACTIVITY_STATUS.OPEN,
	end_time, start_time, end_time, RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
end

function YangLongSiWGCtrl:CloseYangLongReflushIcon()
    ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHEN_MI_HAI_YU_ICON, ACTIVITY_STATUS.CLOSE,
	0, nil, nil, RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
end

