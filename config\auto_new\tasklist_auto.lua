-- R-任务.xls
local item_table={
[1]={item_id=26344,num=10,is_bind=1},
[2]={item_id=39783,num=10,is_bind=1},
[3]={item_id=10100,num=1,is_bind=1},
[4]={item_id=26200,num=10,is_bind=1},
[5]={item_id=23600,num=2,is_bind=1},
[6]={item_id=30487,num=1,is_bind=1},
[7]={item_id=26516,num=1,is_bind=1},
[8]={item_id=30447,num=10,is_bind=1},
[9]={item_id=39987,num=2,is_bind=1},
[10]={item_id=39987,num=1,is_bind=1},
[11]={item_id=26376,num=5,is_bind=1},
[12]={item_id=22575,num=1,is_bind=1},
[13]={item_id=22614,num=1,is_bind=1},
[14]={item_id=26200,num=1,is_bind=1},
[15]={item_id=36420,num=10,is_bind=1},
[16]={item_id=22531,num=1,is_bind=0},
[17]={item_id=43090,num=1,is_bind=0},
[18]={item_id=22537,num=1,is_bind=1},
[19]={item_id=27611,num=5,is_bind=1},
[20]={item_id=36420,num=20,is_bind=1},
[21]={item_id=26415,num=5,is_bind=1},
[22]={item_id=26162,num=1,is_bind=1},
[23]={item_id=26416,num=2,is_bind=1},
[24]={item_id=43009,num=1,is_bind=1},
[25]={item_id=26376,num=2,is_bind=1},
[26]={item_id=26200,num=2,is_bind=1},
[27]={item_id=26165,num=1,is_bind=1},
[28]={item_id=26500,num=1,is_bind=1},
[29]={item_id=26121,num=1,is_bind=1},
[30]={item_id=26515,num=1,is_bind=1},
[31]={item_id=28812,num=1,is_bind=1},
[32]={item_id=26415,num=2,is_bind=1},
[33]={item_id=27907,num=1,is_bind=1},
[34]={item_id=32281,num=1,is_bind=1},
[35]={item_id=32281,num=2,is_bind=1},
[36]={item_id=28893,num=1,is_bind=1},
[37]={item_id=39988,num=1,is_bind=1},
[38]={item_id=45011,num=1,is_bind=1},
[39]={item_id=32292,num=12,is_bind=1},
[40]={item_id=26477,num=1,is_bind=1},
[41]={item_id=26478,num=1,is_bind=1},
[42]={item_id=26479,num=1,is_bind=1},
[43]={item_id=26480,num=1,is_bind=1},
[44]={item_id=26481,num=1,is_bind=1},
[45]={item_id=26482,num=1,is_bind=1},
[46]={item_id=26483,num=1,is_bind=1},
[47]={item_id=26484,num=1,is_bind=1},
[48]={item_id=26485,num=1,is_bind=1},
[49]={item_id=26486,num=1,is_bind=1},
[50]={item_id=26487,num=1,is_bind=1},
[51]={item_id=26488,num=1,is_bind=1},
[52]={item_id=26489,num=1,is_bind=1},
[53]={item_id=26490,num=1,is_bind=1},
[54]={item_id=26349,num=5,is_bind=1},
[55]={item_id=26349,num=6,is_bind=1},
[56]={item_id=26349,num=10,is_bind=1},
[57]={item_id=26349,num=12,is_bind=1},
[58]={item_id=26349,num=1,is_bind=1},
[59]={item_id=26349,num=15,is_bind=1},
[60]={item_id=26349,num=8,is_bind=1},
[61]={item_id=26349,num=14,is_bind=1},
[62]={item_id=26349,num=7,is_bind=1},
[63]={item_id=26349,num=9,is_bind=1},
[64]={item_id=26349,num=18,is_bind=1},
[65]={item_id=26349,num=16,is_bind=1},
[66]={item_id=26349,num=20,is_bind=1},
[67]={item_id=26349,num=11,is_bind=1},
[68]={item_id=26349,num=23,is_bind=1},
[69]={item_id=26349,num=13,is_bind=1},
[70]={item_id=26349,num=25,is_bind=1},
[71]={item_id=0,num=0,is_bind=0},
[72]={item_id=22000,num=1,is_bind=1},
[73]={item_id=39785,num=1,is_bind=1},
}

return {
task_list={
[10]={task_id=10,task_name="梦境醒来",task_type=0,min_level=1,commit_npc={id=10101,scene=1001,x=198,y=58},coin_bind=100000,exp=1500,showitem_0_1="36417,10000",showitem_0_2="36417,10000",showitem_0_3="36417,10000",showitem_1_1="36417,10000",showitem_1_2="36417,10000",showitem_1_3="36417,10000",showitem_0_4="36417,10000",showitem_1_4="36417,10000",commit_dialog=1000,accept_desc="发生了什么？",progress_desc="发生了什么？",commit_desc="发生了什么？",show_chaper_id=0,},
[20]={task_id=20,task_name="魔族拦路",pretaskid=10,c_param1=10101,c_param2=4,coin_bind=100500,exp=3244,showitem_1_2="36417,10000",showitem_1_3="36417,10000",showitem_0_4="36417,10000",showitem_1_4="36417,10000",accept_desc="清除拦路魔族",progress_desc="清除拦路魔族(<per>0/4</per>) ",commit_desc="清除拦路魔族（4/4）",target_obj={{id=10101,scene=1001,x=199,y=131},{id=10101,scene=1001,x=196,y=126},{id=10101,scene=1001,x=203,y=128},{id=10101,scene=1001,x=203,y=131},{id=10101,scene=1001,x=199,y=128},{id=10101,scene=1001,x=207,y=126},{id=10101,scene=1001,x=207,y=130},{id=10101,scene=1001,x=196,y=130},},},
[30]={task_id=30,task_name="门派危机",task_type=0,min_level=1,pretaskid=20,commit_npc={id=10102,scene=1001,x=203,y=147},coin_bind=100500,showitem_0_1="36417,10000",showitem_0_2="36417,10000",showitem_0_3="36417,10000",showitem_1_1="36417,10000",showitem_1_2="36417,10000",showitem_1_3="36417,10000",showitem_0_4="36417,10000",showitem_1_4="36417,10000",commit_dialog=1001,accept_desc="了解门派危机",progress_desc="青崖是宗门管事，一定知道原因",commit_desc="了解门派危机",show_chaper_id=0,},
[40]={task_id=40,task_name="玉清吐息",pretaskid=30,condition=2,c_param1=1006,c_param2=1,coin_bind=102000,exp=1000,accept_desc="唤醒护山龙神",progress_desc="唤醒护山龙神",commit_desc="唤醒护山龙神",target_obj={{id=1006,scene=1001,x=201,y=155},},},
[50]={task_id=50,task_name="小葵遇险",task_type=0,min_level=1,pretaskid=40,condition=1,c_param1=10108,c_param2=5,coin_bind=102500,exp=2113,showitem_0_1="2300,1",showitem_0_2="2300,1",showitem_0_3="2300,1",showitem_1_1="2100,1",showitem_1_2="2100,1",showitem_1_3="2100,1",showitem_0_4="2100,1",showitem_1_4="2100,1",accept_desc="不好！小葵被魔族包围了",progress_desc="营救被魔族包围的小葵(<per>0/5</per>) ",commit_desc="救被魔族包围的小葵（5/5）",show_chaper_id=0,target_obj={{id=10108,scene=1001,x=192,y=350},{id=10108,scene=1001,x=196,y=350},{id=10108,scene=1001,x=189,y=348},{id=10108,scene=1001,x=198,y=348},{id=10108,scene=1001,x=192,y=347},{id=10108,scene=1001,x=196,y=347},{id=10108,scene=1001,x=189,y=346},{id=10108,scene=1001,x=198,y=346},},},
[60]={task_id=60,task_name="灵宠园",task_type=0,min_level=1,pretaskid=50,accept_npc={id=10135,scene=1001,x=188,y=394},coin_bind=205000,exp=6339,showitem_0_1="2300,1",showitem_0_2="2300,1",showitem_0_3="2300,1",showitem_1_1="2100,1",showitem_1_2="2100,1",showitem_1_3="2100,1",showitem_0_4="2100,1",showitem_1_4="2100,1",accept_dialog=1004,accept_desc="安慰受到惊吓的小葵",progress_desc="安慰受到惊吓的小葵",commit_desc="安慰受到惊吓的小葵",show_chaper_id=0,},
[70]={task_id=70,task_name="灵骑栖息之地",task_type=0,min_level=1,pretaskid=60,commit_npc={id=10104,scene=1001,x=165,y=433},coin_bind=102500,exp=4226,showitem_0_1="2300,1",showitem_0_2="2300,1",showitem_0_3="2300,1",showitem_1_1="2100,1",showitem_1_2="2100,1",showitem_1_3="2100,1",showitem_0_4="2100,1",showitem_1_4="2100,1",commit_dialog=1003,accept_desc="了解灵宠园魔气四散的情况",progress_desc="了解灵宠园魔气四散的情况",commit_desc="了解灵宠园魔气四散的情况",show_chaper_id=0,},
[90]={task_id=90,task_name="治疗宝蓝云鹿",pretaskid=70,condition=27,c_param1=1001,c_param2=163,c_param3=453,c_param4=3,c_param5=4128,exp=4200,accept_desc="小葵的治愈之力对灵鹿就有奇效",progress_desc="小葵对灵鹿释放治愈技能",commit_desc="小葵的治愈之力对灵鹿就有奇效",target_obj={},},
[100]={task_id=100,task_name="护山玉清",task_type=0,min_level=1,pretaskid=90,accept_npc={id=10105,scene=1001,x=164,y=487},showitem_0_1="36417,10000",showitem_0_2="36417,10000",showitem_0_3="36417,10000",showitem_1_1="36417,10000",showitem_1_2="36417,10000",showitem_1_3="36417,10000",showitem_0_4="36417,10000",showitem_1_4="36417,10000",accept_dialog=1005,accept_desc="疑似护山龙神莫名失控",progress_desc="疑似护山龙神莫名失控",commit_desc="疑似护山龙神莫名失控",show_chaper_id=0,},
[110]={task_id=110,task_name="驱除邪祟",pretaskid=100,condition=3,c_param1=42,c_param3=5,coin_bind=101500,exp=2526,accept_desc="唤醒被操控的八荒玉清龙 ",progress_desc="唤醒被操控的八荒玉清龙 ",commit_desc="唤醒被操控的八荒玉清龙 ",target_obj={},},
[120]={task_id=120,pretaskid=110,c_param1=10105,c_param2=3,progress_desc="清除拦路魔族(<per>0/3</per>) ",commit_desc="清除拦路魔族（3/3）",target_obj={{id=10105,scene=1001,x=160,y=588},{id=10105,scene=1001,x=165,y=588},{id=10105,scene=1001,x=168,y=586},{id=10105,scene=1001,x=157,y=583},{id=10105,scene=1001,x=157,y=586},{id=10105,scene=1001,x=168,y=583},{id=10105,scene=1001,x=160,y=585},{id=10105,scene=1001,x=165,y=585},},},
[130]={task_id=130,task_name="前路漫漫",pretaskid=120,accept_npc={id=10134,scene=1001,x=163,y=662},coin_bind=101500,exp=2526,accept_dialog=1077,accept_desc="坚守中的大师兄忧心忡忡",progress_desc="坚守中的大师兄忧心忡忡",commit_desc="坚守中的大师兄忧心忡忡",},
[140]={task_id=140,task_name="魔族拦路",task_type=0,min_level=1,pretaskid=130,condition=1,c_param1=10103,c_param2=5,coin_bind=102500,exp=6102,showitem_0_1="36417,10000",showitem_0_2="36417,10000",showitem_0_3="36417,10000",showitem_1_1="36417,10000",showitem_1_2="36417,10000",showitem_1_3="36417,10000",showitem_0_4="36417,10000",showitem_1_4="36417,10000",prof_list_0_1={[0]={item_id=2318,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=2318,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=2318,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=2300,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=2118,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=2300,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=2118,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=2100,num=1,is_bind=0,param0=0}},accept_desc="清除拦路魔族",progress_desc="清除拦路魔族(<per>0/5</per>) ",commit_desc="清除拦路魔族（5/5）",show_chaper_id=0,target_obj={{id=10103,scene=1001,x=157,y=719},{id=10103,scene=1001,x=156,y=714},{id=10103,scene=1001,x=173,y=719},{id=10103,scene=1001,x=162,y=717},{id=10103,scene=1001,x=162,y=722},{id=10103,scene=1001,x=168,y=717},{id=10103,scene=1001,x=167,y=722},{id=10103,scene=1001,x=173,y=715},},},
[150]={task_id=150,task_name="解除封印",pretaskid=140,commit_npc={id=10103,scene=1001,x=163,y=792},condition=2,c_param1=1007,c_param2=1,item_list={[0]=item_table[1]},prof_list_0_1={[0]={item_id=318,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=318,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=318,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=3113,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=118,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=3113,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=118,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=3113,num=1,is_bind=0,param0=0}},commit_dialog=1002,accept_desc="天翎剑尊受困",progress_desc="天翎剑尊受困",commit_desc="天翎剑尊受困",target_obj={{id=1007,scene=1001,x=163,y=785},},},
[160]={task_id=160,task_name="幽冥主宰",task_type=0,min_level=1,pretaskid=150,condition=3,c_param1=42,c_param2=1,c_param3=6,coin_bind=105000,exp=7846,bind_gold=10,showitem_0_1="27611,10",showitem_0_2="27611,10",showitem_0_3="27611,10",showitem_1_1="27611,10",showitem_1_2="27611,10",showitem_1_3="27611,10",showitem_0_4="27611,10",showitem_1_4="27611,10",accept_desc="击败魔尊的门徒",progress_desc="击败魔尊的门徒",commit_desc="击败魔尊的门徒",show_chaper_id=0,target_obj={},},
[170]={task_id=170,task_name="魔族四散",pretaskid=160,accept_npc={},commit_npc={id=10108,scene=1001,x=220,y=974},coin_bind=104500,exp=6121,showitem_1_2="26344,10",showitem_1_3="26344,10",showitem_0_4="26344,10",showitem_1_4="26344,10",commit_dialog=1073,accept_desc="魔界之门前聚集了大量魔族",progress_desc="魔界之门前聚集了大量魔族",commit_desc="魔界之门前聚集了大量魔族",},
[180]={task_id=180,task_name="披荆斩棘",task_type=0,min_level=1,pretaskid=170,condition=1,c_param1=10104,c_param2=5,coin_bind=104500,exp=6121,showitem_0_1="36417,10000",showitem_0_2="36417,10000",showitem_0_3="36417,10000",showitem_1_1="36417,10000",showitem_1_2="26344,10",showitem_1_3="26344,10",showitem_0_4="26344,10",showitem_1_4="26344,10",accept_desc="清除魔界入口的魔族",progress_desc="清除魔界入口的魔族(<per>0/5</per>) ",commit_desc="清除魔界入口的魔族（5/5）",show_chaper_id=0,target_obj={{id=10104,scene=1001,x=153,y=985},{id=10104,scene=1001,x=152,y=990},{id=10104,scene=1001,x=158,y=996},{id=10104,scene=1001,x=165,y=989},{id=10104,scene=1001,x=160,y=990},{id=10104,scene=1001,x=166,y=992},},},
[185]={task_id=185,task_name="魔界之门",pretaskid=180,accept_npc={id=10110,scene=1001,x=157,y=1019},coin_bind=209000,exp=12242,showitem_1_2="26344,10",showitem_1_3="26344,10",showitem_0_4="26344,10",showitem_1_4="26344,10",accept_dialog=1006,accept_desc="魔界之门即将开启，为时已晚",progress_desc="魔界之门即将开启，为时已晚",commit_desc="魔界之门即将开启，为时已晚",},
[190]={task_id=190,task_name="深渊之主",pretaskid=185,commit_npc={},c_param3=7,accept_desc="击退深渊之主 ",progress_desc="击退深渊之主(<per>0/1</per>) ",commit_desc="击退深渊之主1/1",},
[200]={task_id=200,task_name="征讨魔界",pretaskid=190,accept_npc={id=10109,scene=1001,x=155,y=1078},exp=3007,item_list={[0]=item_table[2]},accept_dialog=1074,accept_desc="师祖在魔界等待相助",progress_desc="师祖在魔界等待相助",commit_desc="师祖在魔界等待相助",},
[500]={task_id=500,task_name="初入魔界",pretaskid=200,accept_npc={id=10201,scene=1002,x=78,y=87},item_list={[0]=item_table[3]},accept_dialog=2027,accept_desc="前方似有个人影",progress_desc="前方似有个人影",commit_desc="偶遇火元魔童",},
[510]={task_id=510,task_name="封印加固",pretaskid=500,commit_npc={id=10202,scene=1002,x=86,y=129},c_param1=10205,prof_list_0_1={[0]={item_id=4318,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=4318,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=4318,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=4118,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=4118,num=1,is_bind=0,param0=0}},commit_dialog=2028,accept_desc="解救被魔族缠住的师叔",target_obj={{id=10205,scene=1002,x=79,y=118},{id=10205,scene=1002,x=79,y=115},{id=10205,scene=1002,x=72,y=116},{id=10205,scene=1002,x=72,y=113},{id=10205,scene=1002,x=82,y=113},{id=10205,scene=1002,x=75,y=115},{id=10205,scene=1002,x=82,y=116},{id=10205,scene=1002,x=75,y=118},},},
[520]={task_id=520,pretaskid=510,c_param1=10208,item_list={[0]=item_table[2]},accept_desc="击败魔族",target_obj={{id=10208,scene=1002,x=170,y=175},{id=10208,scene=1002,x=174,y=174},{id=10208,scene=1002,x=165,y=175},{id=10208,scene=1002,x=169,y=173},{id=10208,scene=1002,x=168,y=166},{id=10208,scene=1002,x=174,y=167},{id=10208,scene=1002,x=171,y=169},{id=10208,scene=1002,x=175,y=171},},},
[521]={task_id=521,task_name="再见师祖",pretaskid=520,accept_npc={id=10212,scene=1002,x=197,y=183},coin_bind=108500,exp=4500,accept_dialog=2029,accept_desc="师祖和师姐合力封印司魔使",progress_desc="师祖和师姐合力封印司魔使",commit_desc="师祖和师姐合力封印司魔使",},
[530]={task_id=530,task_name="十二司魔使",task_type=0,min_level=1,pretaskid=521,condition=3,c_param1=42,c_param2=1,c_param3=10,coin_bind=109500,exp=6600,showitem_0_1="7306,1",showitem_0_2="7306,1",showitem_0_3="7306,1",showitem_1_1="7106,1",showitem_1_2="7106,1",showitem_1_3="7106,1",showitem_0_4="7106,1",showitem_1_4="7106,1",prof_list_0_1={[0]={item_id=2319,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=2319,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=2319,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=18306,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=2119,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=5901,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=2119,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=18106,num=1,is_bind=0,param0=0}},accept_desc="击败司魔之一的焚骸无相",progress_desc="击败司魔之一的焚骸无相(<per>0/1</per>) ",commit_desc="击败司魔之一的焚骸无相1/1",show_chaper_id=0,target_obj={},},
[540]={task_id=540,task_name="天翎负伤",pretaskid=530,accept_npc={id=10204,scene=1002,x=234,y=197},item_list={[0]=item_table[2]},accept_dialog=2030,accept_desc="查看天翎剑尊的伤处",progress_desc="查看天翎剑尊的伤处",commit_desc="查看天翎剑尊的伤处",},
[550]={task_id=550,task_name="魔族阻拦",task_type=0,min_level=1,pretaskid=540,condition=1,c_param1=10207,c_param2=5,coin_bind=109000,exp=6600,showitem_0_1="5306,1",showitem_0_2="5706,1",showitem_0_3="5706,1",showitem_1_1="5106,1",showitem_1_2="5906,1",showitem_1_3="5506,1",showitem_0_4="18506,1",showitem_1_4="18506,1",prof_list_0_1={[0]={item_id=3118,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=3118,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=3118,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=18306,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=3118,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=5901,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=3118,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=18106,num=1,is_bind=0,param0=0}},accept_desc="击败阻拦前进的魔族",progress_desc="击败魔族(<per>0/5</per>) ",commit_desc="击败魔族5/5",show_chaper_id=0,target_obj={{id=10207,scene=1002,x=248,y=171},{id=10207,scene=1002,x=252,y=178},{id=10207,scene=1002,x=257,y=175},{id=10207,scene=1002,x=249,y=182},{id=10207,scene=1002,x=251,y=175},{id=10207,scene=1002,x=255,y=172},{id=10207,scene=1002,x=256,y=182},{id=10207,scene=1002,x=258,y=178},},},
[560]={task_id=560,task_name="魔族阻拦",task_type=0,min_level=1,pretaskid=550,condition=1,c_param1=10206,c_param2=6,coin_bind=111000,exp=8700,showitem_0_1="10101,1",showitem_0_2="10101,1",showitem_0_3="10101,1",showitem_1_1="10101,1",showitem_1_2="10101,1",showitem_1_3="10101,1",showitem_0_4="10101,1",showitem_1_4="10101,1",prof_list_0_1={[0]={item_id=1319,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=1319,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=1319,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=7306,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=1119,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=7306,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=1119,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=7106,num=1,is_bind=0,param0=0}},accept_desc="击败阻拦前进的魔族",progress_desc="击败魔族(<per>0/5</per>) ",commit_desc="击败魔族5/5",show_chaper_id=0,target_obj={},},
[561]={task_id=561,task_name="报仇雪恨",task_type=0,min_level=1,pretaskid=560,condition=26,c_param1=1002,c_param2=435,c_param3=198,c_param4=5,coin_bind=111500,showitem_0_1="306,1",showitem_0_2="306,1",showitem_0_3="306,1",showitem_1_1="106,1",showitem_1_2="106,1",showitem_1_3="106,1",showitem_0_4="106,1",showitem_1_4="106,1",prof_list_0_1={[0]={item_id=319,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=319,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=319,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=306,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=119,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=306,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=119,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=106,num=1,is_bind=0,param0=0}},accept_desc="为剑尊报仇雪恨",progress_desc="为剑尊报仇雪恨",commit_desc="为剑尊报仇雪恨",show_chaper_id=0,camera="2##5##88##",target_obj={},},
[570]={task_id=570,task_name="报仇雪恨",pretaskid=561,c_param3=11,coin_bind=111500,exp=8700,showitem_0_1="306,1",showitem_0_2="306,1",showitem_0_3="306,1",showitem_1_1="106,1",showitem_1_2="106,1",showitem_1_3="106,1",showitem_0_4="106,1",showitem_1_4="106,1",prof_list_0_1={[0]={item_id=319,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=319,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=319,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=306,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=119,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=306,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=119,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=106,num=1,is_bind=0,param0=0}},accept_desc="为剑尊报仇雪恨",progress_desc="为剑尊报仇雪恨",commit_desc="为剑尊报仇雪恨",camera=1,},
[800]={task_id=800,task_name="初入烟雨城",task_type=0,min_level=50,pretaskid=570,commit_npc={id=10303,scene=1003,x=73,y=284},coin_bind=117000,exp=12300,showitem_0_1="7307,1",showitem_0_2="7307,1",showitem_0_3="7307,1",showitem_1_1="7107,1",showitem_1_2="7107,1",showitem_1_3="7107,1",showitem_0_4="7107,1",showitem_1_4="7107,1",commit_dialog=2000,accept_desc="楼台烟雨中，竟有拦路者..",progress_desc="楼台烟雨中，竟有拦路者..",commit_desc="楼台烟雨中，竟有拦路者..",show_chaper_id=0,},
[810]={task_id=810,task_name="不打不成器",pretaskid=800,condition=3,c_param1=42,c_param2=1,c_param3=12,exp=12300,prof_list_0_1={[0]={item_id=3119,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=3119,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=6119,num=1,is_bind=0,param0=0}},prof_list_0_4={[0]={item_id=307,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=6119,num=1,is_bind=0,param0=0}},prof_list_1_2={[0]={item_id=107,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=6119,num=1,is_bind=0,param0=0}},prof_list_1_4={[0]={item_id=107,num=1,is_bind=0,param0=0}},accept_desc="替护送队教训拦路帮派",progress_desc="替护送队教训拦路帮派",commit_desc="替护送队教训拦路帮派",},
[820]={task_id=820,task_name="了解内情",task_type=0,min_level=50,pretaskid=810,accept_npc={id=10304,scene=1003,x=159,y=264},commit_npc={id=10305,scene=1003,x=216,y=200},coin_bind=117000,exp=12300,bind_gold=10,showitem_0_1="7307,1",showitem_0_2="7307,1",showitem_0_3="7307,1",showitem_1_1="7107,1",showitem_1_2="7107,1",showitem_1_3="7107,1",showitem_0_4="7107,1",showitem_1_4="7107,1",prof_list_0_1={},prof_list_0_3={},prof_list_0_4={},prof_list_1_1={},prof_list_1_2={},prof_list_1_3={},prof_list_1_4={},accept_dialog=2001,commit_dialog=2002,accept_desc="向师兄了解城内情况",progress_desc="向师兄了解城内情况",commit_desc="向师兄了解城内情况",show_chaper_id=0,camera="3####-150##",},
[830]={task_id=830,task_name="五色莲花",pretaskid=820,condition=2,c_param1=1200,c_param2=1,exp=12300,accept_desc="五色莲花？采下会长出哪吒吗",progress_desc="五色莲花？采下会长出哪吒吗",commit_desc="五色莲花？采下会长出哪吒吗",camera="2##20##120##7.2",target_obj={{id=1200,scene=1003,x=153,y=100},},},
[831]={task_id=831,task_name="望气之术",pretaskid=830,accept_npc={id=10310,scene=1003,x=184,y=71},commit_npc={id=10310,scene=1003,x=184,y=71},prof_list_0_2={},accept_dialog=2014,commit_dialog=2015,accept_desc="城中走访了解情况",progress_desc="静心望气，目注而达心",commit_desc="对岸竟魔气漫天",camera="1##10##110##",},
[840]={task_id=840,task_name="追击魔族",task_type=0,min_level=50,pretaskid=831,condition=26,c_param1=1003,c_param2=208,c_param3=66,c_param4=5,coin_bind=117000,bind_gold=10,showitem_0_1="7307,1",showitem_0_2="7307,1",showitem_0_3="7307,1",showitem_1_1="7107,1",showitem_1_2="7107,1",showitem_1_3="7107,1",showitem_0_4="7107,1",showitem_1_4="7107,1",prof_list_0_3={},prof_list_0_4={},prof_list_1_1={},prof_list_1_2={},prof_list_1_3={},prof_list_1_4={},accept_desc="前往喜鹊阁",progress_desc="前往喜鹊阁",commit_desc="前往喜鹊阁",show_chaper_id=0,camera="2####-10##",target_obj={},},
[850]={task_id=850,task_name="有眼如盲之者",pretaskid=840,condition=3,c_param1=42,c_param3=13,exp=20300,prof_list_0_1={[0]={item_id=4320,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=4320,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=4320,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=4120,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=4120,num=1,is_bind=0,param0=0}},accept_desc="人族中竟有魔族信众",progress_desc="人族中竟有魔族信众",commit_desc="人族中竟有魔族信众",target_obj={},},
[860]={task_id=860,task_name="求神算命",pretaskid=850,accept_npc={},exp=20300,commit_dialog=2003,accept_desc="观今日运势",progress_desc="观今日运势",commit_desc="观今日运势",},
[870]={task_id=870,task_name="江湖骗子",task_type=0,min_level=50,pretaskid=860,commit_npc={id=10308,scene=1003,x=195,y=172},coin_bind=117000,exp=26300,bind_gold=10,showitem_0_1="7307,1",showitem_0_2="7307,1",showitem_0_3="7307,1",showitem_1_1="7107,1",showitem_1_2="7107,1",showitem_1_3="7107,1",showitem_0_4="7107,1",showitem_1_4="7107,1",prof_list_0_1={},prof_list_0_2={},prof_list_0_3={},prof_list_0_4={},prof_list_1_1={},prof_list_1_2={},prof_list_1_3={},prof_list_1_4={},commit_dialog=2004,accept_desc="差点上当受骗",progress_desc="差点上当受骗",commit_desc="差点上当受骗",show_chaper_id=0,},
[880]={task_id=880,task_name="师兄的担忧",pretaskid=870,accept_npc={id=10309,scene=1003,x=309,y=178},prof_list_0_1={[0]={item_id=5320,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=5320,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=5320,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=18120,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=18120,num=1,is_bind=0,param0=0}},accept_dialog=2005,accept_desc="师兄似乎有话要说",progress_desc="提升技能等级",commit_desc="提升技能等级",open_panel_name="skill_view",},
[890]={task_id=890,task_name="魔族拦路",task_type=0,min_level=50,pretaskid=880,condition=1,c_param1=10303,c_param2=6,coin_bind=117000,exp=26300,bind_gold=10,showitem_0_1="7307,1",showitem_0_2="7307,1",showitem_0_3="7307,1",showitem_1_1="7107,1",showitem_1_2="7107,1",showitem_1_3="7107,1",showitem_0_4="7107,1",showitem_1_4="7107,1",prof_list_0_1={},prof_list_0_2={},prof_list_0_3={},prof_list_0_4={},prof_list_1_1={},prof_list_1_2={},prof_list_1_3={},prof_list_1_4={},accept_desc="驱除拦路的魔族",progress_desc="魔族拦路(<per>0/5</per>) ",commit_desc="魔族拦路5/5",show_chaper_id=0,target_obj={},},
[900]={task_id=900,task_name="齐心协力",pretaskid=890,commit_npc={id=10311,scene=1003,x=350,y=74},commit_dialog=2006,accept_desc="原来师姐已在清魔族，马上相助",progress_desc="原来师姐已在清魔族，马上相助",commit_desc="原来师姐已在清魔族，马上相助",},
[910]={task_id=910,task_name="阁内驱魔",pretaskid=900,commit_npc={id=10312,scene=1003,x=402,y=69},condition=3,c_param1=42,c_param2=1,c_param3=14,prof_list_0_1={[0]={item_id=2320,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=2320,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=2320,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=2120,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=2120,num=1,is_bind=0,param0=0}},commit_dialog=2007,accept_desc="和师姐一起击败魔族",progress_desc="和师姐一起击败魔族(<per>0/1</per>) ",commit_desc="和师姐一起击败魔族1/1",},
[911]={task_id=911,task_name="一路同行",task_type=0,min_level=50,pretaskid=910,accept_npc={id=10311,scene=1003,x=350,y=74},coin_bind=117000,bind_gold=10,showitem_0_1="7307,1",showitem_0_2="7307,1",showitem_0_3="7307,1",showitem_1_1="7107,1",showitem_1_2="7107,1",showitem_1_3="7107,1",showitem_0_4="7107,1",showitem_1_4="7107,1",prof_list_0_1={[0]={item_id=2320,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=2320,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=2320,num=1,is_bind=0,param0=0}},prof_list_0_4={},prof_list_1_1={[0]={item_id=2120,num=1,is_bind=0,param0=0}},prof_list_1_2={},prof_list_1_3={[0]={item_id=2120,num=1,is_bind=0,param0=0}},prof_list_1_4={},accept_dialog=2019,accept_desc="护送灵儿师姐",progress_desc="护送灵儿师姐",commit_desc="护送灵儿师姐",show_chaper_id=0,},
[920]={task_id=920,task_name="九重劫塔",pretaskid=911,accept_npc={id=10316,scene=1003,x=284,y=269},condition=3,c_param1=20,c_param3=1,item_list={[0]=item_table[4]},accept_dialog=2008,accept_desc="成神者，需经历万千劫炼",progress_desc="成神者，需经历万千劫炼",commit_desc="成神者，需经历万千劫炼",is_first=1,Bubble_Tips="九重劫塔奖励多多",},
[921]={task_id=921,task_name="气衍万劫",pretaskid=920,accept_npc={id=10316,scene=1003,x=284,y=269},commit_npc={id=10316,scene=1003,x=284,y=269},c_param1=120,c_param2=2,accept_dialog=2017,commit_dialog=2021,accept_desc="九重劫塔似有灵力",progress_desc="气衍万劫达到炼气二重",commit_desc="气衍万劫达到炼气二重",open_panel_name="XiuWeiView",is_first=1,Bubble_Tips="前往气衍万劫",},
[930]={task_id=930,task_name="悬赏揭榜",pretaskid=921,accept_npc={id=10302,scene=1003,x=307,y=308},condition=7,c_param1=73,c_param2=1,accept_desc="解决城中人的困扰",progress_desc="解决城中人的困扰",commit_desc="解决城中人的困扰",},
[940]={task_id=940,task_name="华物商铺",pretaskid=930,accept_npc={id=10319,scene=1003,x=227,y=396},exp=26300,prof_list_0_1={},prof_list_0_2={},prof_list_0_3={},prof_list_1_1={},prof_list_1_3={},accept_dialog=2009,accept_desc="好热闹的商业街，去看看",progress_desc="好热闹的商业街，去看看",commit_desc="好热闹的商业街，去看看",},
[950]={task_id=950,task_name="昆仑玉虚",pretaskid=940,accept_npc={id=10346,scene=1003,x=187,y=399},commit_npc={id=10346,scene=1003,x=187,y=399},condition=3,c_param1=68,accept_dialog=2010,commit_dialog=2016,accept_desc="城中似有幻兽出没",progress_desc="挑战(<per>0/1</per>)次副本·昆仑玉虚",commit_desc="挑战1/1次副本·昆仑玉虚",open_panel_name="fubenpanel#fubenpanel_control_beasts",},
[952]={task_id=952,task_name="神秘奇遇",pretaskid=950,accept_npc={id=10345,scene=1003,x=192,y=278},accept_dialog=2018,accept_desc="萤火的谢礼",progress_desc="萤火的谢礼",commit_desc="萤火的谢礼",},
[960]={task_id=960,task_name="魔族的密谋",pretaskid=952,accept_npc={id=10321,scene=1003,x=243,y=278},prof_list_0_1={[0]={item_id=1320,num=1,is_bind=0,param0=0}},prof_list_0_2={[0]={item_id=1320,num=1,is_bind=0,param0=0}},prof_list_0_3={[0]={item_id=1320,num=1,is_bind=0,param0=0}},prof_list_1_1={[0]={item_id=1120,num=1,is_bind=0,param0=0}},prof_list_1_3={[0]={item_id=1120,num=1,is_bind=0,param0=0}},accept_dialog=2011,accept_desc="青崖先生似有新消息",progress_desc="青崖先生似有新消息",commit_desc="青崖先生似有新消息",camera="1####95##",},
[970]={task_id=970,task_name="将计就计",pretaskid=960,c_param1=1208,exp=26300,accept_desc="燃放烟花信号",progress_desc="燃放烟花信号",commit_desc="燃放烟花信号",camera=1,target_obj={{id=1208,scene=1003,x=266,y=284},},},
[980]={task_id=980,task_name="魔族未出现",pretaskid=970,commit_npc={id=10322,scene=1003,x=321,y=276},commit_dialog=2012,accept_desc="魔族没有出现，难道是消息有诈",progress_desc="魔族没有出现，难道是消息有诈",commit_desc="魔族没有出现，难道是消息有诈",},
[990]={task_id=990,task_name="日月修行",pretaskid=980,commit_npc={id=10322,scene=1003,x=321,y=276},condition=9,c_param1=111,c_param2=1,c_param3=26,commit_dialog=2020,accept_desc="以日月为镜，照修行之道",progress_desc="以日月为镜，照修行之道",commit_desc="以日月为镜，照修行之道",},
[1000]={task_id=1000,task_name="勇闯异界",pretaskid=990,accept_npc={id=10323,scene=1003,x=420,y=284},condition=7,c_param1=85,c_param3=1,c_param4=5,c_param5=3,accept_dialog=2013,accept_desc="前往异界痛击BOSS",progress_desc="装备(<per>0/6</per>)件1阶红品3星装备",commit_desc="装备6件1阶红品3星装备",open_panel_name="boss#boss_vip",accept_bubble="魔物涌动，赶紧前往仙遗洞天击败魔物",},
[1193]={task_id=1193,task_name="天地神躯",pretaskid=1000,condition=7,c_param1=134,c_param2=3,c_param3=1,coin_bind=600000,exp=8204,item_list={[0]=item_table[5]},accept_desc="完成天地神躯·问道出行篇章",progress_desc="完成天地神躯·问道出行篇章",commit_desc="完成天地神躯·问道出行篇章",open_panel_name="zhuansheng",is_first=1,Bubble_Tips="领取衣橱外观",},
[1195]={task_id=1195,task_name="飞升试炼",pretaskid=1193,commit_npc={id=10401,scene=1004,x=102,y=256},coin_bind=300000,exp=4102,item_list={[0]=item_table[4]},commit_dialog=4001,accept_desc="苏映雪在临仙城等待已久",progress_desc="苏映雪在临仙城等待已久",commit_desc="苏映雪在临仙城等待已久",},
[1200]={task_id=1200,task_name="前往试炼",pretaskid=1195,condition=26,c_param1=1004,c_param2=95,c_param3=204,c_param4=5,accept_desc="临仙风雪漫天，谨慎前行",progress_desc="临仙风雪漫天，谨慎前行",commit_desc="临仙风雪漫天，谨慎前行",},
[1210]={task_id=1210,task_name="失魂剑修",pretaskid=1200,commit_npc={id=10402,scene=1004,x=97,y=189},c_param3=16,item_list={[0]=item_table[6]},commit_dialog=4003,accept_desc="注意随时会冒出来的失魂剑修",progress_desc="击败突然冒出的失魂剑修",commit_desc="击败突然冒出的失魂剑修",},
[1230]={task_id=1230,task_name="小凤求救",min_level=110,pretaskid=1210,commit_npc={id=10402,scene=1004,x=97,y=189},exp=2046,commit_dialog=4004,accept_desc="竟有女子在雪地里哭泣",progress_desc="竟有女子在雪地里哭泣",commit_desc="了解女子为何而泣",},
[1240]={task_id=1240,task_name="入门条件",task_type=0,min_level=110,pretaskid=1230,accept_npc={id=10403,scene=1004,x=87,y=179},commit_npc={id=10403,scene=1004,x=87,y=179},condition=7,c_param1=120,c_param2=3,coin_bind=150000,exp=2046,showitem_0_1="36417,10000",showitem_0_2="36417,10000",showitem_0_3="36417,10000",showitem_1_1="36417,10000",showitem_1_2="36417,10000",showitem_1_3="36417,10000",showitem_0_4="36417,10000",showitem_1_4="36417,10000",accept_dialog=4005,commit_dialog=4006,accept_desc="询问门卫如何进入临仙",progress_desc="气衍万劫达到炼气三重",commit_desc="气衍万劫达到炼气三重",open_panel_name="XiuWeiView",show_chaper_id=0,is_first=1,Bubble_Tips="前往气衍万劫提升",target_obj={},},
[1250]={task_id=1250,task_name="击败雪怪",task_type=0,min_level=110,pretaskid=1240,condition=1,c_param1=10402,c_param2=10,coin_bind=150000,exp=2046,showitem_0_1="36417,14000",showitem_0_2="36417,14000",showitem_0_3="36417,14000",showitem_1_1="36417,14000",showitem_1_2="36417,14000",showitem_1_3="36417,14000",showitem_0_4="36417,14000",showitem_1_4="36417,14000",accept_desc="击杀10个失魂异魔",progress_desc="击杀(<per>0/10</per>)个失魂异魔",commit_desc="击杀10/10个失魂异魔",show_chaper_id=0,target_obj={{id=10402,scene=1004,x=58,y=139},{id=10402,scene=1004,x=55,y=134},{id=10402,scene=1004,x=51,y=143},{id=10402,scene=1004,x=61,y=135},{id=10402,scene=1004,x=55,y=144},{id=10402,scene=1004,x=52,y=140},{id=10402,scene=1004,x=61,y=141},{id=10402,scene=1004,x=64,y=137},},},
[1260]={task_id=1260,pretaskid=1250,condition=26,c_param1=1004,c_param2=110,c_param3=111,c_param4=5,},
[1270]={task_id=1270,task_name="第一试炼·冰天",pretaskid=1260,condition=3,c_param1=42,c_param2=1,c_param3=17,accept_desc="前往冰湖突破第一试炼",progress_desc="前往冰湖突破第一试炼",commit_desc="前往冰湖突破第一试炼",target_obj={},},
[1272]={task_id=1272,task_name="第二试炼·战力",pretaskid=1270,commit_npc={id=10420,scene=1004,x=135,y=117},condition=7,c_param1=8,c_param2=38,bind_gold=10,commit_dialog=4007,accept_desc="询问第二试炼要求",progress_desc="战力达到<per>0/38</per>万",commit_desc="战力达到38万",open_panel_name="MainUIStrongMenuView",target_obj={},},
[1273]={task_id=1273,task_name="第二试炼·气衍万劫",pretaskid=1272,c_param1=120,c_param2=4,commit_dialog=4008,accept_desc="气衍万劫达到筑基一重",progress_desc="气衍万劫达到筑基一重",commit_desc="气衍万劫达到筑基一重",open_panel_name="XiuWeiView",is_first=1,Bubble_Tips="前往气衍万劫提升",},
[1280]={task_id=1280,task_name="第三试炼·斩魔",min_level=110,pretaskid=1273,accept_npc={id=10405,scene=1004,x=187,y=108},exp=2046,accept_dialog=4009,accept_desc="试炼难度再升，了解第三试炼",progress_desc="试炼难度再升，了解第三试炼",commit_desc="试炼难度再升，了解第三试炼",},
[1310]={task_id=1310,task_name="再续试炼",pretaskid=1280,commit_npc={id=10405,scene=1004,x=187,y=108},commit_dialog=4010,accept_desc="向临仙考官确认试炼要求",progress_desc="向临仙考官确认试炼要求",commit_desc="向临仙考官确认试炼要求",},
[1340]={task_id=1340,task_name="第四试炼·天梯",task_type=0,min_level=120,pretaskid=1310,accept_npc={id=10406,scene=1004,x=246,y=112},condition=7,c_param1=123,c_param2=1,coin_bind=150000,exp=9559,item_list={[0]=item_table[7]},showitem_0_1="36417,14000",showitem_0_2="36417,14000",showitem_0_3="36417,14000",showitem_1_1="36417,14000",showitem_1_2="36417,14000",showitem_1_3="36417,14000",showitem_0_4="36417,14000",showitem_1_4="36417,14000",accept_dialog=4012,accept_desc="等待白晶晶发布试炼",progress_desc="参与(<per>0/1</per>)次天梯争霸",commit_desc="参与1/1次天梯争霸",open_panel_name="act_jjc",show_chaper_id=0,is_first=1,Bubble_Tips="点击前往天梯争霸",target_obj={},},
[1350]={task_id=1350,task_name="第五试炼·境界",pretaskid=1340,exp=9559,commit_dialog=4013,accept_desc="开启飞升第五试炼",progress_desc="开启飞升第五试炼",commit_desc="开启飞升第五试炼",},
[1360]={task_id=1360,task_name="幻兽委托",pretaskid=1350,condition=7,c_param1=135,c_param2=1,commit_dialog=4028,accept_desc="派遣1次幻兽历练",progress_desc="派遣(<per>0/1</per>)次幻兽历练",commit_desc="派遣1/1次幻兽历练",open_panel_name="TianShenLiLianView",is_first=1,Bubble_Tips="解锁历练见习委托",target_obj={},},
[1380]={task_id=1380,task_name="试炼通过",pretaskid=1360,exp=17003,commit_dialog=4026,accept_desc="大圣有所嘱托",progress_desc="大圣有所嘱托",commit_desc="大圣有所嘱托",open_panel_name="fubenpanel#fubenpanel_welkin",},
[1390]={task_id=1390,task_name="流风溯羽",min_level=120,pretaskid=1380,commit_npc={id=10407,scene=1004,x=333,y=113},exp=34006,commit_dialog=4027,accept_desc="天降法宝是何物？",progress_desc="天降法宝是何物？",commit_desc="天降法宝是何物？",},
[1410]={task_id=1410,task_name="修仙缘梦",pretaskid=1390,item_list={[0]=item_table[8]},accept_dialog=4015,accept_desc="有缘之人方有机遇",progress_desc="有缘之人方有机遇",commit_desc="有缘之人方有机遇",},
[1420]={task_id=1420,task_name="幻兽神石",task_type=0,min_level=130,pretaskid=1410,accept_npc={id=10407,scene=1004,x=333,y=113},coin_bind=150000,exp=3640,showitem_0_1="36417,14000",showitem_0_2="36417,14000",showitem_0_3="36417,14000",showitem_1_1="36417,14000",showitem_1_2="36417,14000",showitem_1_3="36417,14000",showitem_0_4="36417,14000",showitem_1_4="36417,14000",accept_dialog=4016,accept_desc="如何获取更多的幻兽神石",progress_desc="如何获取更多的幻兽神石",commit_desc="如何获取更多的幻兽神石",show_chaper_id=0,},
[1430]={task_id=1430,task_name="找到小龙",task_type=0,min_level=130,pretaskid=1420,commit_npc={id=10408,scene=1004,x=332,y=165},coin_bind=150000,exp=3640,showitem_0_1="36417,14000",showitem_0_2="36417,14000",showitem_0_3="36417,14000",showitem_1_1="36417,14000",showitem_1_2="36417,14000",showitem_1_3="36417,14000",showitem_0_4="36417,14000",showitem_1_4="36417,14000",commit_dialog=4017,accept_desc="前往找到小龙踪迹",progress_desc="前往找到小龙踪迹",commit_desc="前往找到小龙踪迹",show_chaper_id=0,},
[1440]={task_id=1440,task_name="守护小龙",pretaskid=1430,accept_npc={id=10409,scene=1004,x=328,y=183},commit_npc={id=10409,scene=1004,x=328,y=183},c_param4=0,c_param5=0,accept_dialog=4018,commit_dialog=4019,accept_desc="难道这也是试炼的一部分？",progress_desc="小龙身体抱恙，为其护法",commit_desc="小龙身体抱恙，为其护法",},
[1450]={task_id=1450,task_name="提升幻兽",pretaskid=1440,accept_npc={id=10410,scene=1004,x=332,y=216},condition=7,c_param1=126,c_param2=1,c_param3=16,accept_dialog=4020,accept_desc="难道试炼者的性命无人在乎？！",progress_desc="任意1只幻兽达到16级",commit_desc="任意1只幻兽达到16级",open_panel_name="ControlBeastsView",is_first=1,Bubble_Tips="点击前往提升幻兽",target_obj={},},
[1470]={task_id=1470,task_name="临城仙地",min_level=130,pretaskid=1450,commit_npc={id=10410,scene=1004,x=332,y=216},exp=7280,commit_dialog=4021,accept_desc="前往临仙城的秘宝仙地",progress_desc="前往临仙城的秘宝仙地",commit_desc="前往临仙城的秘宝仙地",},
[1480]={task_id=1480,task_name="秘宝迷踪",min_level=130,pretaskid=1470,accept_npc={id=10411,scene=1004,x=298,y=300},c_param1=1303,coin_bind=150000,exp=3640,accept_dialog=4022,accept_desc="青崖先生有秘宝的消息",progress_desc="青崖先生有秘宝的消息",commit_desc="青崖先生有秘宝的消息",target_obj={{id=1303,scene=1004,x=313,y=329},},},
[1490]={task_id=1490,task_name="幻兽之主",pretaskid=1480,c_param1=127,c_param2=3,c_param3=4,accept_dialog=4029,accept_desc="固本定心之法",progress_desc="拥有(<per>0/3</per>)只4星幻兽",commit_desc="拥有3只4星幻兽",open_panel_name="ControlBeastsView",Bubble_Tips="点击前往提升幻兽",},
[1500]={task_id=1500,task_name="再临劫塔",min_level=130,pretaskid=1490,accept_npc={id=10411,scene=1004,x=298,y=300},c_param1=62,c_param2=12,coin_bind=150000,exp=3640,accept_desc="达到九重劫塔12层",progress_desc="达到九重劫塔(<per>0/12</per>)层",commit_desc="达到九重劫塔12/12层",open_panel_name="fubenpanel#fubenpanel_welkin",is_first=1,Bubble_Tips="点击前往九重劫塔",},
[1530]={task_id=1530,task_name="离开临仙",pretaskid=1500,accept_npc={id=10412,scene=1004,x=305,y=321},c_param1=1304,accept_dialog=4025,accept_desc="回到烟雨城修整一番",progress_desc="回到烟雨城修整一番",commit_desc="回到烟雨城修整一番",target_obj={{id=1304,scene=1004,x=310,y=322},},},
[2100]={task_id=2100,task_name="回归烟雨",pretaskid=1530,commit_npc={id=10346,scene=1003,x=187,y=399},xiuwei_exp=500,commit_dialog=5005,accept_desc="再见幻兽之仆",progress_desc="再见幻兽之仆",commit_desc="再见幻兽之仆",},
[2110]={task_id=2110,task_name="历练委托",min_level=130,pretaskid=2100,accept_npc={id=10347,scene=1003,x=342,y=404},exp=7280,accept_dialog=5006,accept_desc="城主已听闻你历练之事",progress_desc="城主已听闻你历练之事",commit_desc="城主已听闻你历练之事",open_panel_name="TianShenLiLianView",is_first=1,Bubble_Tips="前往历练",},
[2120]={task_id=2120,min_level=145,pretaskid=2110,accept_npc={id=10320,scene=1003,x=292,y=449},c_param2=6,c_param3=1,coin_bind=150000,exp=22353,accept_dialog=5007,accept_desc="异界魔族不断再生，事发紧急",progress_desc="装备(<per>0/6</per>)件1阶红品5星装备",commit_desc="装备6件1阶红品5星装备",is_first=1,Bubble_Tips="前往仙遗洞天击杀魔王",},
[2130]={task_id=2130,task_name="神躯·锻体一重",pretaskid=2120,accept_npc={id=10347,scene=1003,x=342,y=404},c_param1=68,c_param2=1,exp=44706,accept_dialog=5008,accept_desc="仙法之力即将冲破躯体",progress_desc="完成天地神躯，锻体一重试炼",commit_desc="完成天地神躯，锻体一重试炼",open_panel_name="zhuansheng",},
[2150]={task_id=2150,task_name="师姐的委托",min_level=145,pretaskid=2130,commit_npc={id=10322,scene=1003,x=321,y=276},exp=22353,commit_dialog=5010,accept_desc="云熙欲言又止似有困扰",progress_desc="云熙师姐的求助",commit_desc="云熙师姐的求助",},
[2160]={task_id=2160,task_name="灵植破坏者",min_level=145,pretaskid=2150,condition=3,c_param1=42,c_param3=44,coin_bind=150000,exp=22353,accept_desc="驱逐破坏灵植的魔族",progress_desc="驱逐破坏灵植的魔族",commit_desc="驱逐破坏灵植的魔族",target_obj={},},
[2170]={task_id=2170,task_name="灵妖奇脉",pretaskid=2160,accept_npc={id=10322,scene=1003,x=321,y=276},condition=1,c_param1=61,c_param2=1,coin_bind=150000,accept_dialog=5011,accept_desc="为何魔族会出现在此",progress_desc="挑战灵妖奇脉(<per>0/1</per>)次",commit_desc="挑战1/1次灵妖奇脉",open_panel_name="boss#boss_personal",},
[2171]={task_id=2171,task_name="百倍爆装",min_level=145,pretaskid=2170,condition=7,c_param1=129,c_param2=3,coin_bind=300000,exp=22353,accept_desc="可接取",progress_desc="爆率等级提升至3级",commit_desc="爆率等级提升至3级",open_panel_name="HundredEquipView#0#op=showAward",target_obj={},},
[2180]={task_id=2180,min_level=145,pretaskid=2171,c_param5=3,coin_bind=300000,exp=22353,item_list={[0]=item_table[4]},accept_desc="穿戴3件2阶红3星装备",progress_desc="穿戴(<per>0/3</per>)件2阶红3星装备",commit_desc="穿戴3 件2阶红3星装备",},
[2240]={task_id=2240,task_name="每日一逛",pretaskid=2180,commit_npc={id=10322,scene=1003,x=321,y=276},coin_bind=300000,commit_dialog=5016,accept_desc="华物商铺上新啦",progress_desc="华物商铺上新啦",commit_desc="华物商铺上新啦",},
[2250]={task_id=2250,task_name="修仙探索",pretaskid=2240,condition=7,c_param1=92,c_param2=80,item_list={[0]=item_table[9]},commit_dialog=5017,accept_desc="获得80点日常活跃值",progress_desc="获得(<per>0/80</per>)点日常活跃值",commit_desc="获得80/80点日常活跃值",open_panel_name="bizuo",is_first=1,Bubble_Tips="前往每日必做达成目标",target_obj={},},
[2260]={task_id=2260,task_name="拜访大圣",pretaskid=2250,commit_npc={id=10323,scene=1003,x=420,y=284},exp=86002,commit_dialog=5018,accept_desc="大圣为何如此低调",progress_desc="大圣为何如此低调",commit_desc="大圣为何如此低调",},
[2270]={task_id=2270,task_name="与大圣切磋",min_level=150,pretaskid=2260,commit_npc={id=10323,scene=1003,x=420,y=284},c_param3=45,exp=86002,commit_dialog=5019,accept_desc="实力是真是假，一试便知",progress_desc="实力是真是假，一试便知",commit_desc="实力是真是假，一试便知",},
[2280]={task_id=2280,task_name="门派试炼",min_level=150,pretaskid=2270,commit_npc={id=10324,scene=1003,x=487,y=284},exp=43001,commit_dialog=5020,accept_desc="前往完成门派晋升试炼",progress_desc="前往完成门派晋升试炼",commit_desc="前往完成门派晋升试炼",},
[2290]={task_id=2290,task_name="三关生死",pretaskid=2280,commit_npc={id=10324,scene=1003,x=487,y=284},c_param3=46,exp=43001,commit_dialog=5021,accept_desc="完成三关生死关",progress_desc="完成三关生死关",commit_desc="完成三关生死关",},
[2300]={task_id=2300,task_name="神躯·锻体二重",min_level=150,pretaskid=2290,c_param1=134,c_param3=2,coin_bind=150000,exp=43001,accept_desc="完成天地神躯·地煞淬体篇章",progress_desc="完成天地神躯·地煞淬体篇章",commit_desc="完成天地神躯·地煞淬体篇章",open_panel_name="zhuansheng",is_first=1,Bubble_Tips="完成神躯铸造",},
[2310]={task_id=2310,task_name="得到令牌",pretaskid=2300,accept_npc={id=10324,scene=1003,x=487,y=284},accept_dialog=5022,accept_desc="区区门派试炼考核，拿下！",progress_desc="得到门派令牌，成为门派修士",commit_desc="得到门派令牌，成为门派修士",},
[2320]={task_id=2320,task_name="云妖之处",min_level=160,pretaskid=2310,accept_npc={id=10342,scene=1003,x=565,y=287},coin_bind=150000,exp=55787,accept_dialog=5023,accept_desc="登神派飞来传音",progress_desc="前往登神派",commit_desc="前往登神派",},
[2330]={task_id=2330,task_name="万妖古阵",pretaskid=2320,commit_npc={id=10515,scene=1005,x=12,y=52},coin_bind=150000,exp=55787,commit_dialog=5024,accept_desc="被镇守的上古万妖似有异动",progress_desc="被镇守的上古万妖似有异动",commit_desc="被镇守的上古万妖似有异动",},
[3000]={task_id=3000,task_name="天峰夺宝·一",pretaskid=2330,condition=7,c_param1=132,commit_dialog=6001,accept_desc="通过天峰夺宝一层",progress_desc="通过天峰夺宝一层",commit_desc="通过天峰夺宝一层",open_panel_name="fubenpanel#fubenpanel_equip_high",is_first=1,Bubble_Tips="前往天峰夺宝",target_obj={},},
[3005]={task_id=3005,task_name="修复阵法",pretaskid=3000,commit_npc={id=10501,scene=1005,x=134,y=122},c_param1=1501,commit_dialog=6038,accept_desc="阵法遭到破坏",progress_desc="阵法遭到破坏",commit_desc="阵法遭到破坏",target_obj={{id=1501,scene=1005,x=229,y=297},},},
[3010]={task_id=3010,task_name="破棋阵·黑",pretaskid=3005,accept_npc={id=10502,scene=1005,x=185,y=232},c_param1=1501,accept_dialog=6002,accept_desc="术业有专攻，修复之术交给他人",progress_desc="完成棋阵残局的胜利 0/3",commit_desc="完成棋阵残局的胜利 1/3",target_obj={{id=1501,scene=1005,x=229,y=297},},},
[3020]={task_id=3020,task_name="破棋阵·白",task_type=0,min_level=160,pretaskid=3010,condition=2,c_param1=1502,c_param2=1,coin_bind=140000,exp=55787,showitem_0_1="36417,16000",showitem_0_2="36417,16000",showitem_0_3="36417,16000",showitem_1_1="36417,16000",showitem_1_2="36417,16000",showitem_1_3="36417,16000",showitem_0_4="36417,16000",showitem_1_4="36417,16000",accept_desc="完成棋阵残局的胜利 1/3",progress_desc="完成棋阵残局的胜利 1/3",commit_desc="完成棋阵残局的胜利 2/3",show_chaper_id=0,target_obj={{id=1502,scene=1005,x=265,y=288},},},
[3030]={task_id=3030,task_name="破棋阵·胜",pretaskid=3020,c_param1=1503,accept_desc="完成棋阵残局的胜利 2/3",progress_desc="完成棋阵残局的胜利 2/3",commit_desc="完成棋阵残局的胜利 3/3",target_obj={{id=1503,scene=1005,x=257,y=276},},},
[3040]={task_id=3040,task_name="仙修秘密",min_level=160,pretaskid=3030,accept_npc={id=10502,scene=1005,x=185,y=232},c_param1=10508,exp=55787,accept_dialog=6003,accept_desc="荒神暴怒，毁灭仅在瞬间",target_obj={{id=10508,scene=1005,x=122,y=330},{id=10508,scene=1005,x=127,y=331},{id=10508,scene=1005,x=124,y=332},{id=10508,scene=1005,x=131,y=333},{id=10508,scene=1005,x=121,y=327},{id=10508,scene=1005,x=120,y=334},{id=10508,scene=1005,x=127,y=335},},},
[3060]={task_id=3060,task_name="结界位置",min_level=160,pretaskid=3040,commit_npc={id=10503,scene=1005,x=176,y=244},coin_bind=280000,exp=111574,commit_dialog=6005,accept_desc="了解荒神设立的四个结界",progress_desc="了解荒神设立的四个结界",commit_desc="了解荒神设立的四个结界",},
[3080]={task_id=3080,task_name="破结界·日",pretaskid=3060,accept_npc={id=10506,scene=1005,x=210,y=200},c_param1=10501,accept_dialog=6007,accept_desc="破除结界受到阻挠",target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[3090]={task_id=3090,task_name="破结界·日",min_level=170,pretaskid=3080,commit_npc={id=10507,scene=1005,x=262,y=191},c_param1=1504,exp=136949,commit_dialog=6008,accept_desc="破除结界日石",progress_desc="破除结界日石1/2",commit_desc="破除结界日石2/2",target_obj={{id=1504,scene=1005,x=275,y=199},},},
[3100]={task_id=3100,task_name="前往集合地",min_level=170,pretaskid=3090,commit_npc={id=10508,scene=1005,x=246,y=122},c_param1=10501,exp=136949,commit_dialog=6009,target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[3110]={task_id=3110,task_name="映雪顾虑",task_type=0,min_level=170,pretaskid=3100,commit_npc={id=10509,scene=1005,x=222,y=107},coin_bind=140000,exp=136949,showitem_0_1="36417,16000",showitem_0_2="36417,16000",showitem_0_3="36417,16000",showitem_1_1="36417,16000",showitem_1_2="36417,16000",showitem_1_3="36417,16000",showitem_0_4="36417,16000",showitem_1_4="36417,16000",commit_dialog=6010,accept_desc="苏映雪欲言又止",progress_desc="了解苏映雪的担忧",commit_desc="了解苏映雪的担忧",show_chaper_id=0,},
[3120]={task_id=3120,task_name="斩妖除魔",min_level=170,pretaskid=3110,commit_npc={id=10524,scene=1005,x=199,y=81},c_param1=10502,exp=136949,commit_dialog=6011,commit_desc="击杀15个荒恶念聚合",target_obj={{id=10502,scene=1005,x=196,y=100},{id=10502,scene=1005,x=209,y=99},{id=10502,scene=1005,x=203,y=102},{id=10502,scene=1005,x=192,y=96},{id=10502,scene=1005,x=202,y=98},{id=10502,scene=1005,x=198,y=95},{id=10502,scene=1005,x=199,y=100},{id=10502,scene=1005,x=196,y=97},},},
[3130]={task_id=3130,task_name="破结界·月",min_level=170,pretaskid=3120,accept_npc={id=10511,scene=1005,x=176,y=19},c_param1=10503,c_param2=8,exp=136949,accept_dialog=6012,accept_desc="破除结界受到阻挠",progress_desc="设法让它们冷静下来(<per>0/8</per>) ",commit_desc="设法让它们冷静下来8/8",target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[3140]={task_id=3140,task_name="破结界·月",pretaskid=3130,commit_npc={id=10510,scene=1005,x=48,y=70},c_param1=1505,commit_dialog=6013,target_obj={{id=1505,scene=1005,x=83,y=98},},},
[3150]={task_id=3150,task_name="装备收集",min_level=170,pretaskid=3140,c_param1=85,c_param3=2,c_param4=5,c_param5=5,coin_bind=140000,exp=136949,accept_desc="穿戴3件2阶红品5星装备",progress_desc="穿戴(<per>0/3</per>)件2阶红品5星装备",commit_desc="穿戴3件2阶红品5星装备",open_panel_name="boss#boss_vip",},
[3170]={task_id=3170,task_name="再寻结界",task_type=0,min_level=180,pretaskid=3150,accept_npc={id=10510,scene=1005,x=48,y=70},coin_bind=280000,exp=303865,showitem_0_1="36417,16000",showitem_0_2="36417,16000",showitem_0_3="36417,16000",showitem_1_1="36417,16000",showitem_1_2="36417,16000",showitem_1_3="36417,16000",showitem_0_4="36417,16000",showitem_1_4="36417,16000",accept_dialog=6014,accept_desc="荒神之徒四散，需要留人清除",progress_desc="再次前往寻找下一个结界石",commit_desc="再次前往寻找下一个结界石",show_chaper_id=0,},
[3180]={task_id=3180,task_name="深严防守",min_level=180,pretaskid=3170,accept_npc={id=10512,scene=1005,x=20,y=85},condition=1,c_param1=10504,c_param2=15,exp=166916,accept_dialog=6015,accept_desc="需保证城镇中普通人的安危",progress_desc="击杀(<per>0/15</per>)个吞噬魔怪",commit_desc="击杀15个吞噬魔怪",target_obj={{id=10504,scene=1005,x=22,y=111},{id=10504,scene=1005,x=21,y=115},{id=10504,scene=1005,x=28,y=113},{id=10504,scene=1005,x=26,y=115},{id=10504,scene=1005,x=21,y=108},{id=10504,scene=1005,x=28,y=119},{id=10504,scene=1005,x=33,y=111},{id=10504,scene=1005,x=21,y=119},},},
[3190]={task_id=3190,task_name="守卫加强",min_level=180,pretaskid=3180,commit_npc={id=10513,scene=1005,x=19,y=146},exp=166916,commit_dialog=6016,accept_desc="守卫加强，寻找新办法",progress_desc="守卫加强，寻找新办法",commit_desc="守卫加强，寻找新办法",},
[3200]={task_id=3200,task_name="暂且修整",min_level=180,pretaskid=3190,accept_npc={id=10514,scene=1005,x=40,y=179},c_param1=1511,exp=166916,item_list={[0]=item_table[10]},accept_dialog=6017,accept_desc="向前辈请教一二",progress_desc="原地修整",commit_desc="原地修整",target_obj={{id=1511,scene=1005,x=39,y=177},},},
[3210]={task_id=3210,task_name="神算算子",pretaskid=3200,commit_npc={id=10515,scene=1005,x=12,y=52},commit_dialog=6018,accept_desc="请算算子掐指一算",progress_desc="请算算子掐指一算",commit_desc="请算算子掐指一算",},
[3230]={task_id=3230,task_name="神躯·锻体二重",pretaskid=3210,commit_npc={id=10516,scene=1005,x=31,y=183},condition=7,c_param1=134,c_param2=7,c_param3=2,commit_dialog=6020,accept_desc="完成天地神躯·天罡炼魂篇章",progress_desc="完成天地神躯·天罡炼魂篇章",commit_desc="完成天地神躯·天罡炼魂篇章",open_panel_name="zhuansheng",target_obj={},},
[3240]={task_id=3240,task_name="寻求突破",pretaskid=3230,commit_npc={id=10517,scene=1005,x=62,y=225},commit_dialog=6021,accept_desc="了解并寻求突破",progress_desc="了解并寻求突破",commit_desc="了解并寻求突破",},
[3250]={task_id=3250,task_name="登天梯战仙友",pretaskid=3240,commit_npc={id=10516,scene=1005,x=31,y=183},condition=7,c_param1=133,c_param2=500,commit_dialog=6022,accept_desc="天梯争霸达到500名",progress_desc="天梯争霸达到500名",commit_desc="天梯争霸达到500名",open_panel_name="act_jjc",is_first=1,Bubble_Tips="点击前往天梯争霸",target_obj={},},
[3260]={task_id=3260,task_name="事态升级",min_level=190,pretaskid=3250,accept_npc={id=10513,scene=1005,x=19,y=146},coin_bind=140000,exp=169047,accept_dialog=6023,accept_desc="担心登神派的弟子们",progress_desc="担心登神派的弟子们",commit_desc="担心登神派的弟子们",},
[3270]={task_id=3270,task_name="提升幻兽",pretaskid=3260,accept_npc={id=10515,scene=1005,x=12,y=52},condition=7,c_param1=126,c_param3=40,accept_dialog=6024,accept_desc="寻找云阙天宗的弟子",progress_desc="任意1只幻兽达到40级",commit_desc="任意1只幻兽达到40级",open_panel_name="ControlBeastsView",is_first=1,Bubble_Tips="点击前往提升幻兽",target_obj={},},
[3280]={task_id=3280,task_name="前往后门",pretaskid=3270,commit_npc={id=10512,scene=1005,x=20,y=85},c_param1=10505,commit_dialog=6025,accept_desc="击杀15个虚无之灵",progress_desc="击杀(<per>0/15</per>)个虚无之灵",commit_desc="击杀15个虚无之灵",target_obj={{id=10505,scene=1005,x=61,y=242},{id=10505,scene=1005,x=62,y=247},{id=10505,scene=1005,x=67,y=249},{id=10505,scene=1005,x=66,y=241},{id=10505,scene=1005,x=68,y=251},{id=10505,scene=1005,x=68,y=244},{id=10505,scene=1005,x=72,y=247},},},
[3290]={task_id=3290,task_name="清除阻碍",pretaskid=3280,commit_npc={id=10521,scene=1005,x=160,y=226},c_param1=10508,c_param2=15,commit_dialog=6026,accept_desc="击杀15个恶念聚合",progress_desc="击杀(<per>0/15</per>)个恶念聚合",commit_desc="击杀15个恶念聚合",target_obj={{id=10508,scene=1005,x=122,y=330},{id=10508,scene=1005,x=127,y=331},{id=10508,scene=1005,x=124,y=332},{id=10508,scene=1005,x=131,y=333},{id=10508,scene=1005,x=121,y=327},{id=10508,scene=1005,x=120,y=334},{id=10508,scene=1005,x=127,y=335},},},
[3300]={task_id=3300,task_name="破结界·星",pretaskid=3290,accept_npc={id=10518,scene=1005,x=143,y=232},accept_dialog=6027,accept_desc="寻找守卫修复阵法",progress_desc="击败守卫，破除结界石 0/2",commit_desc="击败守卫，破除结界石 1/2",},
[3310]={task_id=3310,task_name="破结界·星",min_level=190,pretaskid=3300,commit_npc={id=10503,scene=1005,x=176,y=244},c_param1=1512,exp=169047,item_list={[0]=item_table[10]},commit_dialog=6028,accept_desc="击败守卫，破除第三个结界石",progress_desc="击败守卫，破除结界石 1/2",commit_desc="击败守卫，破除结界石 2/2",target_obj={{id=1512,scene=1005,x=164,y=268},},},
[3320]={task_id=3320,task_name="诸神之力",pretaskid=3310,commit_npc={id=10523,scene=1005,x=179,y=201},condition=1,c_param1=10501,c_param2=8,commit_dialog=6029,accept_desc="击杀8个虚无之灵",progress_desc="击杀(<per>0/8</per>)个虚无之灵",commit_desc="击杀8个虚无之灵",target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[3340]={task_id=3340,task_name="破门",pretaskid=3320,commit_npc={id=10520,scene=1005,x=155,y=216},c_param3=52,commit_dialog=6031,accept_desc="攻破荒神大殿的大门",progress_desc="攻破荒神大殿的大门",commit_desc="攻破荒神大殿的大门",},
[3350]={task_id=3350,task_name="齐同并进",pretaskid=3340,accept_npc={id=10521,scene=1005,x=160,y=226},accept_dialog=6032,accept_desc="前往阵法修复之地",progress_desc="与同伴一起闯入大殿",commit_desc="与同伴一起闯入大殿",},
[3360]={task_id=3360,task_name="诸神真武",pretaskid=3350,condition=2,c_param1=1509,c_param2=1,accept_dialog=6033,accept_desc="胜券还未握在手中",progress_desc="强行拔出被荒神污染的真武",commit_desc="强行拔出被荒神污染的真武",target_obj={{id=1509,scene=1005,x=163,y=224},},},
[3370]={task_id=3370,task_name="妖国荒魔",min_level=190,pretaskid=3360,commit_npc={id=10522,scene=1005,x=133,y=151},exp=169047,commit_dialog=6034,accept_desc="对峙占据登神派的荒神",progress_desc="对峙占据登神派的荒神",commit_desc="对峙占据登神派的荒神",},
[3380]={task_id=3380,task_name="斩王",min_level=190,pretaskid=3370,commit_npc={id=10522,scene=1005,x=133,y=151},c_param3=53,coin_bind=140000,exp=169047,commit_dialog=6035,accept_desc="击败荒神",progress_desc="击败荒神",commit_desc="击败荒神",},
[3381]={task_id=3381,task_name="神躯·锻体二重",pretaskid=3380,commit_npc={id=10522,scene=1005,x=133,y=151},condition=7,c_param1=68,c_param2=2,commit_dialog=6039,accept_desc="完成天地神躯，锻体二重试炼",progress_desc="完成天地神躯，锻体二重试炼",commit_desc="完成天地神躯，锻体二重试炼",open_panel_name="zhuansheng",target_obj={},},
[3382]={task_id=3382,open_day=2,min_level=200,pretaskid=3381,commit_npc={id=10520,scene=1005,x=155,y=216},c_param3=3,c_param5=3,exp=320087,commit_dialog=6030,accept_desc="穿戴3件3阶红品3星装备",progress_desc="穿戴(<per>0/3</per>)件3阶红品3星装备",commit_desc="穿戴3件3阶红品3星装备",is_first=1,Bubble_Tips="前往仙遗洞天击杀魔王",},
[3390]={task_id=3390,task_name="镇压妖国",open_day=2,min_level=200,pretaskid=3382,commit_npc={id=10502,scene=1005,x=185,y=232},exp=320087,commit_dialog=6036,accept_desc="成功镇守登神派，与长老对话",progress_desc="成功镇守登神派，与长老对话",commit_desc="成功镇守登神派，与长老对话",},
[3400]={task_id=3400,task_name="新的未来",pretaskid=3390,condition=2,c_param1=1510,c_param2=1,commit_dialog=6037,accept_desc="采集天命珠，前往新的地点",progress_desc="采集天命珠，前往新的地点",commit_desc="采集天命珠，前往新的地点",target_obj={{id=1510,scene=1005,x=118,y=155},},},
[5040]={task_id=5040,task_name="回归人界",min_level=200,pretaskid=3400,commit_npc={id=10501,scene=1005,x=134,y=122},exp=320087,commit_dialog=12601,accept_desc="回归人界",progress_desc="回归人界",commit_desc="回归人界",},
[5050]={task_id=5050,task_name="妖魔丛生",min_level=200,pretaskid=5040,c_param2=35,exp=320087,showitem_0_1="36417,17000",showitem_0_2="36417,17000",showitem_0_3="36417,17000",showitem_1_1="36417,17000",showitem_1_2="36417,17000",showitem_1_3="36417,17000",showitem_0_4="36417,17000",showitem_1_4="36417,17000",progress_desc="击败魔怪(<per>0/35</per>) ",commit_desc="击败魔怪35/35",},
[5060]={task_id=5060,min_level=200,pretaskid=5050,commit_npc={id=10501,scene=1005,x=134,y=122},exp=320087,is_first=1,},
[5070]={task_id=5070,min_level=200,pretaskid=5060,exp=320087,},
[5080]={task_id=5080,min_level=220,pretaskid=5070,exp=850428,},
[5090]={task_id=5090,task_name="虚空破魔",pretaskid=5080,c_param1=10503,c_param2=45,progress_desc="击败魔怪(<per>0/45</per>) ",commit_desc="击败魔怪45/45",target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[5100]={task_id=5100,min_level=220,pretaskid=5090,exp=850428,},
[5110]={task_id=5110,task_name="击败妖魔",min_level=220,pretaskid=5100,c_param1=10504,c_param2=50,exp=850428,progress_desc="击败魔怪(<per>0/50</per>) ",commit_desc="击败魔怪50/50",target_obj={{id=10504,scene=1005,x=22,y=111},{id=10504,scene=1005,x=21,y=115},{id=10504,scene=1005,x=28,y=113},{id=10504,scene=1005,x=26,y=115},{id=10504,scene=1005,x=21,y=108},{id=10504,scene=1005,x=28,y=119},{id=10504,scene=1005,x=33,y=111},{id=10504,scene=1005,x=21,y=119},},},
[5120]={task_id=5120,task_name="神躯·锻体三重",task_type=0,ver=2,open_day=2,min_level=240,pretaskid=5110,condition=7,c_param1=68,c_param2=3,coin_bind=100000,exp=1285895,bind_gold=10,accept_dialog=30005,commit_dialog=30006,accept_desc="完成天地神躯，锻体三重试炼",progress_desc="完成天地神躯，锻体三重试炼",commit_desc="完成天地神躯，锻体三重试炼",open_panel_name="zhuansheng",show_chaper_id=0,fb_tip_desc="击败六臂邪魔，才可以使自身的修为突飞猛进！",fb_tip_title="诛杀心魔",is_first=1,Bubble_Tips="重塑神躯，自选UR幻兽",target_obj={},},
[5130]={task_id=5130,min_level=240,pretaskid=5120,exp=1285895,},
[5140]={task_id=5140,task_name="击败妖兽",min_level=240,pretaskid=5130,c_param1=10505,c_param2=55,exp=1285895,progress_desc="击败魔怪(<per>0/55</per>) ",commit_desc="击败魔怪55/55",target_obj={{id=10505,scene=1005,x=61,y=242},{id=10505,scene=1005,x=62,y=247},{id=10505,scene=1005,x=67,y=249},{id=10505,scene=1005,x=66,y=241},{id=10505,scene=1005,x=68,y=251},{id=10505,scene=1005,x=68,y=244},{id=10505,scene=1005,x=72,y=247},},},
[5150]={task_id=5150,min_level=240,pretaskid=5140,exp=1285895,},
[5160]={task_id=5160,min_level=240,pretaskid=5150,exp=1285895,},
[5170]={task_id=5170,min_level=260,pretaskid=5160,exp=3252475,},
[5180]={task_id=5180,min_level=260,pretaskid=5170,c_param1=1504,exp=3252475,target_obj={{id=1504,scene=1005,x=275,y=199},},},
[5190]={task_id=5190,min_level=260,pretaskid=5180,exp=3252475,},
[5200]={task_id=5200,min_level=260,pretaskid=5190,exp=3252475,},
[5210]={task_id=5210,min_level=280,pretaskid=5200,exp=5035157,},
[5220]={task_id=5220,task_name="破阵而出",min_level=280,pretaskid=5210,camp="",c_param1=10501,c_param2=45,exp=5035157,progress_desc="击败魔怪(<per>0/45</per>) ",commit_desc="击败魔怪45/45",target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[5230]={task_id=5230,task_name="到达隐月",pretaskid=5220,camp="",commit_npc={id=10510,scene=1005,x=48,y=70},commit_dialog=13602,accept_desc="到达隐月",progress_desc="到达隐月",commit_desc="到达隐月",},
[5240]={task_id=5240,task_name="隐匿踪迹",min_level=280,pretaskid=5230,exp=5035157,accept_desc="隐匿踪迹",progress_desc="隐匿踪迹(<per>0/1</per>) ",commit_desc="隐匿踪迹1/1",},
[5250]={task_id=5250,min_level=300,pretaskid=5240,exp=10610765,show_chaper_info="撞钟击鼓凝檀香\n遗庙丹青草木长",},
[5260]={task_id=5260,min_level=300,pretaskid=5250,c_param1=10502,exp=10610765,target_obj={{id=10502,scene=1005,x=196,y=100},{id=10502,scene=1005,x=209,y=99},{id=10502,scene=1005,x=203,y=102},{id=10502,scene=1005,x=192,y=96},{id=10502,scene=1005,x=202,y=98},{id=10502,scene=1005,x=198,y=95},{id=10502,scene=1005,x=199,y=100},{id=10502,scene=1005,x=196,y=97},},},
[5270]={task_id=5270,min_level=300,pretaskid=5260,exp=10610765,},
[5280]={task_id=5280,min_level=300,pretaskid=5270,exp=10610765,},
[5290]={task_id=5290,min_level=320,pretaskid=5280,exp=16897670,},
[5300]={task_id=5300,min_level=320,pretaskid=5290,exp=16897670,},
[5310]={task_id=5310,min_level=320,pretaskid=5300,exp=16897670,},
[5320]={task_id=5320,min_level=320,pretaskid=5310,c_param1=10503,exp=16897670,target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[5330]={task_id=5330,min_level=340,pretaskid=5320,exp=26222585,},
[5340]={task_id=5340,min_level=340,pretaskid=5330,c_param1=10504,exp=26222585,target_obj={{id=10504,scene=1005,x=22,y=111},{id=10504,scene=1005,x=21,y=115},{id=10504,scene=1005,x=28,y=113},{id=10504,scene=1005,x=26,y=115},{id=10504,scene=1005,x=21,y=108},{id=10504,scene=1005,x=28,y=119},{id=10504,scene=1005,x=33,y=111},{id=10504,scene=1005,x=21,y=119},},},
[5350]={task_id=5350,min_level=340,pretaskid=5340,exp=26222585,},
[5360]={task_id=5360,min_level=340,pretaskid=5350,c_param1=10505,exp=26222585,target_obj={{id=10505,scene=1005,x=61,y=242},{id=10505,scene=1005,x=62,y=247},{id=10505,scene=1005,x=67,y=249},{id=10505,scene=1005,x=66,y=241},{id=10505,scene=1005,x=68,y=251},{id=10505,scene=1005,x=68,y=244},{id=10505,scene=1005,x=72,y=247},},},
[5370]={task_id=5370,min_level=360,pretaskid=5360,exp=40693399,},
[5380]={task_id=5380,task_name="突破阵眼",min_level=360,pretaskid=5370,c_param2=45,exp=40693399,progress_desc="击败魔怪(<per>0/45</per>) ",commit_desc="击败魔怪45/45",},
[5390]={task_id=5390,min_level=360,pretaskid=5380,exp=40693399,Bubble_Tips="完成主线，可开启新功能",},
[5400]={task_id=5400,min_level=360,pretaskid=5390,exp=40693399,},
[5410]={task_id=5410,min_level=380,pretaskid=5400,condition=3,c_param1=42,c_param2=1,c_param3=39,exp=63149871,progress_desc="击败魔怪",commit_desc="击败魔怪",target_obj={},},
[5420]={task_id=5420,min_level=380,pretaskid=5410,exp=63149871,},
[5430]={task_id=5430,task_name="瀚海二层",pretaskid=5420,c_param3=40,Bubble_Tips="完成主线，可开启新功能",},
[5440]={task_id=5440,min_level=380,pretaskid=5430,exp=63149871,},
[5450]={task_id=5450,task_name="瀚海三层",min_level=400,pretaskid=5440,commit_npc={id=10517,scene=1005,x=62,y=225},c_param3=41,exp=101022717,commit_dialog=14801,},
[5460]={task_id=5460,task_name="异魔真身",pretaskid=5450,c_param3=42,commit_dialog=14802,},
[5470]={task_id=5470,min_level=400,pretaskid=5460,exp=101022717,Bubble_Tips="完成主线，可开启新功能",},
[5480]={task_id=5480,min_level=400,pretaskid=5470,exp=101022717,},
[5490]={task_id=5490,task_name="陌生男子",min_level=420,pretaskid=5480,commit_npc={id=10502,scene=1005,x=185,y=232},exp=216332998,commit_dialog=12702,accept_desc="陌生男子",progress_desc="陌生男子",commit_desc="陌生男子",},
[5500]={task_id=5500,min_level=420,pretaskid=5490,exp=216332998,Bubble_Tips="完成主线，可开启新功能",},
[5510]={task_id=5510,min_level=420,pretaskid=5500,exp=216332998,},
[5520]={task_id=5520,task_name="虚空破魔",min_level=420,pretaskid=5510,c_param3=32,exp=216332998,},
[5530]={task_id=5530,min_level=440,pretaskid=5520,exp=344902110,},
[5540]={task_id=5540,min_level=440,pretaskid=5530,exp=344902110,Bubble_Tips="完成主线，可开启新功能",},
[5550]={task_id=5550,min_level=440,pretaskid=5540,exp=344902110,},
[5560]={task_id=5560,min_level=440,pretaskid=5550,exp=344902110,},
[5570]={task_id=5570,min_level=460,pretaskid=5560,exp=557977475,},
[5580]={task_id=5580,min_level=460,pretaskid=5570,exp=557977475,Bubble_Tips="完成主线，可开启新功能",},
[5590]={task_id=5590,min_level=460,pretaskid=5580,exp=557977475,},
[5600]={task_id=5600,task_name="拾取腰牌",min_level=460,pretaskid=5590,exp=557977475,accept_desc="击败魔怪",progress_desc="击败魔怪(<per>0/60</per>) ",commit_desc="击败魔怪60/60",},
[5610]={task_id=5610,min_level=480,pretaskid=5600,exp=1133003974,},
[5620]={task_id=5620,min_level=480,pretaskid=5610,exp=1133003974,Bubble_Tips="完成主线，可开启新功能",},
[5630]={task_id=5630,min_level=480,pretaskid=5620,exp=1133003974,},
[5640]={task_id=5640,task_name="破阵而出",min_level=480,pretaskid=5630,c_param3=33,exp=1133003974,},
[5650]={task_id=5650,min_level=500,pretaskid=5640,exp=1798904916,},
[5660]={task_id=5660,min_level=500,pretaskid=5650,c_param1=1512,exp=1798904916,Bubble_Tips="完成主线，可开启新功能",target_obj={{id=1512,scene=1005,x=164,y=268},},},
[5670]={task_id=5670,task_name="被人识破",min_level=500,pretaskid=5660,commit_npc={id=10511,scene=1005,x=176,y=19},exp=1798904916,commit_dialog=13703,accept_desc="被人识破",progress_desc="被人识破",commit_desc="被人识破",},
[5680]={task_id=5680,task_name="无奈苦战",min_level=500,pretaskid=5670,exp=1798904916,},
[5690]={task_id=5690,min_level=520,pretaskid=5680,exp=2791622635,},
[5700]={task_id=5700,min_level=520,pretaskid=5690,c_param1=1504,exp=2791622635,Bubble_Tips="完成主线，可开启新功能",target_obj={{id=1504,scene=1005,x=275,y=199},},},
[5710]={task_id=5710,min_level=520,pretaskid=5700,exp=2791622635,},
[5720]={task_id=5720,min_level=520,pretaskid=5710,exp=2791622635,},
[5730]={task_id=5730,min_level=540,pretaskid=5720,c_param1=1505,exp=6302425109,target_obj={{id=1505,scene=1005,x=83,y=98},},},
[5740]={task_id=5740,task_name="击败弟子",min_level=540,pretaskid=5730,exp=6302425109,Bubble_Tips="完成主线，可开启新功能",},
[5750]={task_id=5750,min_level=540,pretaskid=5740,exp=6302425109,},
[5760]={task_id=5760,task_name="击败魔王",min_level=540,pretaskid=5750,c_param1=10504,exp=6302425109,commit_dialog=14105,target_obj={{id=10504,scene=1005,x=22,y=111},{id=10504,scene=1005,x=21,y=115},{id=10504,scene=1005,x=28,y=113},{id=10504,scene=1005,x=26,y=115},{id=10504,scene=1005,x=21,y=108},{id=10504,scene=1005,x=28,y=119},{id=10504,scene=1005,x=33,y=111},{id=10504,scene=1005,x=21,y=119},},},
[5770]={task_id=5770,min_level=560,pretaskid=5760,exp=10084281820,},
[5780]={task_id=5780,task_name="隐月霜阵",pretaskid=5770,c_param1=10505,Bubble_Tips="完成主线，可开启新功能",target_obj={{id=10505,scene=1005,x=61,y=242},{id=10505,scene=1005,x=62,y=247},{id=10505,scene=1005,x=67,y=249},{id=10505,scene=1005,x=66,y=241},{id=10505,scene=1005,x=68,y=251},{id=10505,scene=1005,x=68,y=244},{id=10505,scene=1005,x=72,y=247},},},
[5790]={task_id=5790,min_level=560,pretaskid=5780,exp=10084281820,},
[5800]={task_id=5800,task_name="突破阵眼",min_level=560,pretaskid=5790,c_param1=10508,exp=10084281820,target_obj={{id=10508,scene=1005,x=122,y=330},{id=10508,scene=1005,x=127,y=331},{id=10508,scene=1005,x=124,y=332},{id=10508,scene=1005,x=131,y=333},{id=10508,scene=1005,x=121,y=327},{id=10508,scene=1005,x=120,y=334},{id=10508,scene=1005,x=127,y=335},},},
[5810]={task_id=5810,min_level=580,pretaskid=5800,exp=10432832078,},
[5820]={task_id=5820,min_level=580,pretaskid=5810,exp=10432832078,Bubble_Tips="完成主线，可开启新功能",},
[5830]={task_id=5830,task_name="瀚海一层",pretaskid=5820,c_param1=10501,target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[5840]={task_id=5840,min_level=580,pretaskid=5830,exp=10432832078,},
[5850]={task_id=5850,task_name="瀚海二层",min_level=580,pretaskid=5840,c_param1=10502,c_param2=40,exp=10432832078,progress_desc="击败魔怪(<per>0/40</per>) ",commit_desc="击败魔怪40/40",target_obj={{id=10502,scene=1005,x=196,y=100},{id=10502,scene=1005,x=209,y=99},{id=10502,scene=1005,x=203,y=102},{id=10502,scene=1005,x=192,y=96},{id=10502,scene=1005,x=202,y=98},{id=10502,scene=1005,x=198,y=95},{id=10502,scene=1005,x=199,y=100},{id=10502,scene=1005,x=196,y=97},},},
[5860]={task_id=5860,min_level=580,pretaskid=5850,exp=10432832078,},
[5870]={task_id=5870,task_name="瀚海三层",pretaskid=5860,c_param1=10503,commit_dialog=14801,target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[5880]={task_id=5880,task_name="异魔真身",min_level=600,pretaskid=5870,commit_npc={id=10517,scene=1005,x=62,y=225},exp=24285216413,commit_dialog=14802,},
[5890]={task_id=5890,min_level=600,pretaskid=5880,exp=24285216413,},
[5900]={task_id=5900,min_level=600,pretaskid=5890,exp=24285216413,},
[5910]={task_id=5910,min_level=620,pretaskid=5900,exp=47108631946,},
[5920]={task_id=5920,min_level=620,pretaskid=5910,exp=47108631946,},
[5930]={task_id=5930,min_level=620,pretaskid=5920,exp=47108631946,},
[5940]={task_id=5940,task_name="虚空破魔",min_level=620,pretaskid=5930,c_param1=10503,exp=47108631946,target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[5950]={task_id=5950,min_level=640,pretaskid=5940,exp=74745860034,},
[5960]={task_id=5960,min_level=640,pretaskid=5950,exp=74745860034,},
[5970]={task_id=5970,ver=2,min_level=640,pretaskid=5960,exp=74745860034,},
[5980]={task_id=5980,min_level=640,pretaskid=5970,exp=74745860034,},
[5990]={task_id=5990,min_level=660,pretaskid=5980,exp=115994032051,},
[6000]={task_id=6000,min_level=660,pretaskid=5990,exp=115994032051,},
[6010]={task_id=6010,task_name="妖魔的请求",min_level=660,pretaskid=6000,commit_npc={id=10507,scene=1005,x=262,y=191},exp=115994032051,commit_dialog=13202,accept_desc="魔族的请求",progress_desc="魔族的请求",commit_desc="魔族的请求",},
[6020]={task_id=6020,min_level=660,pretaskid=6010,c_param1=1512,exp=115994032051,target_obj={{id=1512,scene=1005,x=164,y=268},},},
[6030]={task_id=6030,ver=2,min_level=680,pretaskid=6020,exp=205719800303,},
[6040]={task_id=6040,min_level=680,pretaskid=6030,exp=205719800303,},
[6050]={task_id=6050,task_name="隐月愁阵",min_level=680,pretaskid=6040,commit_npc={id=10509,scene=1005,x=222,y=107},exp=205719800303,commit_dialog=13503,accept_desc="隐月愁阵",progress_desc="隐月愁阵",commit_desc="隐月愁阵",},
[6060]={task_id=6060,task_name="破阵而出",min_level=680,pretaskid=6050,c_param1=10502,exp=205719800303,target_obj={{id=10502,scene=1005,x=196,y=100},{id=10502,scene=1005,x=209,y=99},{id=10502,scene=1005,x=203,y=102},{id=10502,scene=1005,x=192,y=96},{id=10502,scene=1005,x=202,y=98},{id=10502,scene=1005,x=198,y=95},{id=10502,scene=1005,x=199,y=100},{id=10502,scene=1005,x=196,y=97},},},
[6070]={task_id=6070,min_level=700,pretaskid=6060,exp=319245361517,},
[6080]={task_id=6080,min_level=700,pretaskid=6070,c_param1=1504,exp=319245361517,target_obj={{id=1504,scene=1005,x=275,y=199},},},
[6090]={task_id=6090,ver=2,min_level=700,pretaskid=6080,exp=319245361517,},
[6100]={task_id=6100,task_name="无奈苦战",min_level=700,pretaskid=6090,c_param1=10503,exp=319245361517,target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[6110]={task_id=6110,min_level=720,pretaskid=6100,exp=577989418190,},
[6120]={task_id=6120,min_level=720,pretaskid=6110,c_param1=1505,exp=577989418190,target_obj={{id=1505,scene=1005,x=83,y=98},},},
[6130]={task_id=6130,task_name="藏书阁内",pretaskid=6120,commit_npc={id=10513,scene=1005,x=19,y=146},commit_dialog=13902,accept_desc="藏书阁内",progress_desc="藏书阁内",commit_desc="藏书阁内",},
[6140]={task_id=6140,task_name="长老之计",min_level=720,pretaskid=6130,commit_npc={id=10514,scene=1005,x=40,y=179},exp=577989418190,commit_dialog=14103,accept_desc="长老之计",progress_desc="长老之计",commit_desc="长老之计",},
[6150]={task_id=6150,ver=2,min_level=740,pretaskid=6140,exp=896950320414,},
[6160]={task_id=6160,min_level=740,pretaskid=6150,exp=896950320414,},
[6170]={task_id=6170,task_name="战神大阵",min_level=740,pretaskid=6160,exp=896950320414,commit_dialog=14104,accept_desc="战神大阵",progress_desc="战神大阵",commit_desc="战神大阵",},
[6180]={task_id=6180,min_level=740,pretaskid=6170,exp=896950320414,},
[6190]={task_id=6190,task_name="新的委托",min_level=760,pretaskid=6180,commit_npc={id=10515,scene=1005,x=12,y=52},exp=1391928384743,commit_dialog=14201,accept_desc="新的委托",progress_desc="新的委托",commit_desc="新的委托",},
[6200]={task_id=6200,task_name="隐月霜阵",pretaskid=6190,},
[6210]={task_id=6210,ver=2,min_level=760,pretaskid=6200,exp=1391928384743,},
[6220]={task_id=6220,min_level=760,pretaskid=6210,exp=1391928384743,},
[6230]={task_id=6230,task_name="进入瀚海",pretaskid=6220,commit_npc={id=10516,scene=1005,x=31,y=183},commit_dialog=14401,accept_desc="进入瀚海",progress_desc="进入瀚海",commit_desc="进入瀚海",},
[6240]={task_id=6240,task_name="奇怪的声音",pretaskid=6230,commit_dialog=14402,accept_desc="奇怪的声音",progress_desc="奇怪的声音",commit_desc="奇怪的声音",},
[6250]={task_id=6250,task_name="瀚海一层",min_level=780,pretaskid=6240,exp=2160057903031,},
[6260]={task_id=6260,task_name="再次回响",min_level=780,pretaskid=6250,exp=2160057903031,commit_dialog=14403,accept_desc="再次回响",progress_desc="再次回响",commit_desc="再次回响",},
[6270]={task_id=6270,task_name="瀚海二层",min_level=800,pretaskid=6260,exp=4022491555479,},
[6280]={task_id=6280,task_name="静心调息",min_level=800,pretaskid=6270,commit_npc={id=10517,scene=1005,x=62,y=225},exp=4022491555479,commit_dialog=14602,accept_desc="静心调息",progress_desc="静心调息",commit_desc="静心调息",},
[6290]={task_id=6290,task_name="瀚海三层",pretaskid=6280,commit_dialog=14801,},
[6300]={task_id=6300,task_name="异魔真身",min_level=800,pretaskid=6290,commit_npc={id=10517,scene=1005,x=62,y=225},exp=4022491555479,commit_dialog=14802,},
[6310]={task_id=6310,task_name="回归人界",min_level=820,pretaskid=6300,commit_npc={id=10501,scene=1005,x=134,y=122},exp=6373135108740,commit_dialog=12601,accept_desc="回归人界",progress_desc="回归人界",commit_desc="回归人界",},
[6320]={task_id=6320,task_name="妖魔丛生",min_level=820,pretaskid=6310,c_param2=35,exp=6373135108740,progress_desc="击败魔怪(<per>0/35</per>) ",commit_desc="击败魔怪35/35",},
[6330]={task_id=6330,ver=2,min_level=820,pretaskid=6320,exp=6373135108740,},
[6340]={task_id=6340,task_name="同行前往",min_level=820,pretaskid=6330,exp=6373135108740,accept_dialog=12801,},
[6350]={task_id=6350,task_name="隐月难阵",pretaskid=6340,commit_npc={id=10503,scene=1005,x=176,y=244},commit_dialog=12802,accept_desc="隐月难阵",progress_desc="隐月难阵",commit_desc="隐月难阵",},
[6360]={task_id=6360,task_name="虚空破魔",min_level=840,pretaskid=6350,exp=9890121510522,},
[6370]={task_id=6370,task_name="阵法奇特",min_level=840,pretaskid=6360,commit_npc={id=10504,scene=1005,x=241,y=286},exp=9890121510522,commit_dialog=12902,accept_desc="阵法奇特",progress_desc="阵法奇特",commit_desc="阵法奇特",},
[6380]={task_id=6380,task_name="击败妖魔",min_level=840,pretaskid=6370,c_param1=10504,c_param2=50,exp=9890121510522,progress_desc="击败魔怪(<per>0/50</per>) ",commit_desc="击败魔怪50/50",target_obj={{id=10504,scene=1005,x=22,y=111},{id=10504,scene=1005,x=21,y=115},{id=10504,scene=1005,x=28,y=113},{id=10504,scene=1005,x=26,y=115},{id=10504,scene=1005,x=21,y=108},{id=10504,scene=1005,x=28,y=119},{id=10504,scene=1005,x=33,y=111},{id=10504,scene=1005,x=21,y=119},},},
[6390]={task_id=6390,task_name="妖兽统领",task_type=0,open_day=2,min_level=860,pretaskid=6380,commit_npc={id=10505,scene=1005,x=46,y=72},coin_bind=100000,exp=15347941291648,showitem_0_1="36417,15000",showitem_0_2="36417,15000",showitem_0_3="36417,15000",showitem_1_1="36417,15000",showitem_1_2="36417,15000",showitem_1_3="36417,15000",showitem_0_4="36417,15000",showitem_1_4="36417,15000",commit_dialog=12903,accept_desc="妖兽统领",progress_desc="妖兽统领",commit_desc="妖兽统领",show_chaper_id=0,},
[6400]={task_id=6400,task_name="击败妖兽",pretaskid=6390,c_param1=10505,c_param2=55,progress_desc="击败魔怪(<per>0/55</per>) ",commit_desc="击败魔怪55/55",target_obj={{id=10505,scene=1005,x=61,y=242},{id=10505,scene=1005,x=62,y=247},{id=10505,scene=1005,x=67,y=249},{id=10505,scene=1005,x=66,y=241},{id=10505,scene=1005,x=68,y=251},{id=10505,scene=1005,x=68,y=244},{id=10505,scene=1005,x=72,y=247},},},
[6410]={task_id=6410,task_name="继续前进",pretaskid=6400,commit_npc={id=10506,scene=1005,x=210,y=200},commit_dialog=13102,accept_desc="继续前进",progress_desc="继续前进",commit_desc="继续前进",},
[6420]={task_id=6420,task_name="杀出重围",min_level=860,pretaskid=6410,c_param1=10508,exp=15347941291648,showitem_0_1="36417,15000",showitem_0_2="36417,15000",showitem_0_3="36417,15000",showitem_1_1="36417,15000",showitem_1_2="36417,15000",showitem_1_3="36417,15000",showitem_0_4="36417,15000",showitem_1_4="36417,15000",target_obj={{id=10508,scene=1005,x=122,y=330},{id=10508,scene=1005,x=127,y=331},{id=10508,scene=1005,x=124,y=332},{id=10508,scene=1005,x=131,y=333},{id=10508,scene=1005,x=121,y=327},{id=10508,scene=1005,x=120,y=334},{id=10508,scene=1005,x=127,y=335},},},
[6430]={task_id=6430,min_level=880,pretaskid=6420,exp=26958214482979,},
[6440]={task_id=6440,task_name="拾取腰牌",min_level=880,pretaskid=6430,condition=2,c_param1=1504,c_param2=1,exp=26958214482979,accept_desc="拾取腰牌",progress_desc="拾取腰牌",commit_desc="拾取腰牌",target_obj={{id=1504,scene=1005,x=275,y=199},},},
[6450]={task_id=6450,task_name="暖雪的疑惑",min_level=880,pretaskid=6440,commit_npc={id=10508,scene=1005,x=246,y=122},exp=26958214482979,commit_dialog=13302,accept_desc="暖雪的疑惑",progress_desc="暖雪的疑惑",commit_desc="暖雪的疑惑",},
[6460]={task_id=6460,task_name="继续前进",min_level=880,pretaskid=6450,c_param1=10509,exp=26958214482979,target_obj={{id=10509,scene=1005,x=166,y=65},{id=10509,scene=1005,x=169,y=63},{id=10509,scene=1005,x=167,y=62},{id=10509,scene=1005,x=172,y=64},{id=10509,scene=1005,x=170,y=66},{id=10509,scene=1005,x=171,y=59},{id=10509,scene=1005,x=171,y=62},{id=10509,scene=1005,x=165,y=61},},},
[6470]={task_id=6470,min_level=900,pretaskid=6460,exp=46201613858442,Bubble_Tips="完成主线，可开启新功能",},
[6480]={task_id=6480,task_name="破阵而出",min_level=900,pretaskid=6470,c_param1=10501,exp=46201613858442,target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[6490]={task_id=6490,task_name="到达隐月",min_level=900,pretaskid=6480,commit_npc={id=10510,scene=1005,x=48,y=70},exp=46201613858442,commit_dialog=13602,accept_desc="到达隐月",progress_desc="到达隐月",commit_desc="到达隐月",},
[6500]={task_id=6500,task_name="隐匿踪迹",min_level=900,pretaskid=6490,c_param1=1505,exp=46201613858442,accept_desc="隐匿踪迹",progress_desc="隐匿踪迹(<per>0/1</per>) ",commit_desc="隐匿踪迹1/1",target_obj={{id=1505,scene=1005,x=83,y=98},},},
[6510]={task_id=6510,min_level=920,pretaskid=6500,exp=71697769974029,Bubble_Tips="完成主线，可开启新功能",},
[6520]={task_id=6520,task_name="无奈苦战",min_level=920,pretaskid=6510,exp=71697769974029,},
[6530]={task_id=6530,min_level=920,pretaskid=6520,exp=71697769974029,},
[6540]={task_id=6540,task_name="乔装打扮",min_level=920,pretaskid=6530,c_param1=1512,exp=71697769974029,accept_desc="乔装打扮",progress_desc="乔装打扮(<per>0/1</per>) ",commit_desc="乔装打扮1/1",target_obj={{id=1512,scene=1005,x=164,y=268},},},
[6550]={task_id=6550,min_level=940,pretaskid=6540,exp=111263866993782,Bubble_Tips="完成主线，可开启新功能",},
[6560]={task_id=6560,min_level=940,pretaskid=6550,exp=111263866993782,},
[6570]={task_id=6570,task_name="设法逃脱",min_level=940,pretaskid=6560,exp=111263866993782,accept_desc="设法逃脱",progress_desc="设法逃脱(<per>0/1</per>) ",commit_desc="设法逃脱1/1",},
[6580]={task_id=6580,task_name="击败弟子",min_level=940,pretaskid=6570,exp=111263866993782,},
[6590]={task_id=6590,min_level=960,pretaskid=6580,exp=172664339530998,Bubble_Tips="完成主线，可开启新功能",},
[6600]={task_id=6600,task_name="击败魔王",pretaskid=6590,commit_dialog=14105,},
[6610]={task_id=6610,min_level=960,pretaskid=6600,exp=172664339530998,},
[6620]={task_id=6620,task_name="隐月霜阵",min_level=960,pretaskid=6610,exp=172664339530998,},
[6630]={task_id=6630,min_level=980,pretaskid=6620,exp=315057086886864,Bubble_Tips="完成主线，可开启新功能",},
[6640]={task_id=6640,task_name="突破阵眼",min_level=980,pretaskid=6630,exp=315057086886864,},
[6650]={task_id=6650,min_level=980,pretaskid=6640,exp=315057086886864,},
[6660]={task_id=6660,min_level=980,pretaskid=6650,exp=315057086886864,},
[6670]={task_id=6670,task_name="瀚海一层",pretaskid=6660,Bubble_Tips="完成主线，可开启新功能",},
[6680]={task_id=6680,min_level=1000,pretaskid=6670,exp=522517853685938,},
[6690]={task_id=6690,task_name="瀚海二层",min_level=1000,pretaskid=6680,exp=522517853685938,},
[6700]={task_id=6700,min_level=1000,pretaskid=6690,exp=522517853685938,},
[6710]={task_id=6710,task_name="瀚海三层",pretaskid=6700,commit_dialog=14801,Bubble_Tips="完成主线，可开启新功能",},
[6720]={task_id=6720,min_level=1020,pretaskid=6710,exp=609080353685938,},
[6730]={task_id=6730,min_level=1020,pretaskid=6720,exp=609080353685938,},
[6740]={task_id=6740,min_level=1020,pretaskid=6730,exp=609080353685938,},
[6750]={task_id=6750,min_level=1040,pretaskid=6740,exp=695642853685939,Bubble_Tips="完成主线，可开启新功能",},
[6760]={task_id=6760,min_level=1040,pretaskid=6750,exp=695642853685939,},
[6770]={task_id=6770,min_level=1040,pretaskid=6760,exp=695642853685939,},
[6780]={task_id=6780,min_level=1040,pretaskid=6770,exp=695642853685939,},
[6790]={task_id=6790,min_level=1060,pretaskid=6780,exp=782205353685938,Bubble_Tips="完成主线，可开启新功能",},
[6800]={task_id=6800,min_level=1060,pretaskid=6790,exp=782205353685938,},
[6810]={task_id=6810,min_level=1060,pretaskid=6800,exp=782205353685938,},
[6820]={task_id=6820,min_level=1060,pretaskid=6810,exp=782205353685938,},
[6830]={task_id=6830,ver=2,min_level=1080,pretaskid=6820,exp=868767853685940,Bubble_Tips="完成主线，可开启新功能",},
[6840]={task_id=6840,ver=2,min_level=1080,pretaskid=6830,exp=868767853685940,},
[6850]={task_id=6850,ver=2,min_level=1080,pretaskid=6840,exp=868767853685940,},
[6860]={task_id=6860,ver=2,min_level=1080,pretaskid=6850,c_param1=1505,exp=868767853685940,target_obj={{id=1505,scene=1005,x=83,y=98},},},
[6870]={task_id=6870,min_level=1100,pretaskid=6860,exp=955330353685939,Bubble_Tips="完成主线，可开启新功能",},
[6880]={task_id=6880,ver=2,min_level=1100,pretaskid=6870,exp=955330353685939,},
[6890]={task_id=6890,ver=2,min_level=1100,pretaskid=6880,exp=955330353685939,},
[6900]={task_id=6900,ver=2,min_level=1100,pretaskid=6890,exp=955330353685939,},
[6910]={task_id=6910,ver=2,min_level=1120,pretaskid=6900,exp=1041892853685940,Bubble_Tips="完成主线，可开启新功能",},
[6920]={task_id=6920,ver=2,min_level=1120,pretaskid=6910,c_param1=1512,exp=1041892853685940,target_obj={{id=1512,scene=1005,x=164,y=268},},},
[6930]={task_id=6930,min_level=1120,pretaskid=6920,exp=1041892853685940,},
[6940]={task_id=6940,ver=2,min_level=1120,pretaskid=6930,exp=1041892853685940,},
[6950]={task_id=6950,ver=2,min_level=1140,pretaskid=6940,exp=1128455353685950,Bubble_Tips="完成主线，可开启新功能",},
[6960]={task_id=6960,ver=2,min_level=1140,pretaskid=6950,c_param1=1504,exp=1128455353685950,target_obj={{id=1504,scene=1005,x=275,y=199},},},
[6970]={task_id=6970,ver=2,min_level=1140,pretaskid=6960,exp=1128455353685950,},
[6980]={task_id=6980,ver=2,min_level=1140,pretaskid=6970,exp=1128455353685950,},
[6990]={task_id=6990,ver=2,min_level=1160,pretaskid=6980,exp=1215017853685940,Bubble_Tips="完成主线，可开启新功能",},
[7000]={task_id=7000,ver=2,min_level=1160,pretaskid=6990,exp=1215017853685940,},
[7010]={task_id=7010,ver=2,min_level=1160,pretaskid=7000,exp=1215017853685940,},
[7020]={task_id=7020,task_name="击败魔王",pretaskid=7010,commit_dialog=14105,},
[7030]={task_id=7030,ver=2,min_level=1180,pretaskid=7020,exp=1301580353685920,Bubble_Tips="完成主线，可开启新功能",},
[7040]={task_id=7040,task_name="隐月霜阵",pretaskid=7030,},
[7050]={task_id=7050,min_level=1180,pretaskid=7040,exp=1301580353685920,},
[7060]={task_id=7060,ver=2,min_level=1180,pretaskid=7050,exp=1301580353685920,},
[7070]={task_id=7070,ver=2,min_level=1200,pretaskid=7060,exp=1393154199728110,},
[7080]={task_id=7080,ver=2,min_level=1200,pretaskid=7070,exp=1393154199728110,},
[7090]={task_id=7090,task_name="瀚海一层",min_level=1200,pretaskid=7080,exp=1393154199728110,},
[7100]={task_id=7100,ver=2,min_level=1200,pretaskid=7090,exp=1393154199728110,},
[7110]={task_id=7110,min_level=1220,pretaskid=7100,exp=1480029199728120,Bubble_Tips="完成主线，可开启新功能",},
[7120]={task_id=7120,ver=2,min_level=1220,pretaskid=7110,exp=1480029199728120,},
[7130]={task_id=7130,task_name="瀚海三层",pretaskid=7120,commit_dialog=14801,},
[7140]={task_id=7140,ver=2,min_level=1220,pretaskid=7130,exp=1480029199728120,},
[7150]={task_id=7150,task_name="最后一关",pretaskid=7140,commit_npc={},commit_dialog=23001,accept_desc="最后的关卡",progress_desc="最后的关卡",commit_desc="最后的关卡",Bubble_Tips="完成主线，可开启新功能",},
[7160]={task_id=7160,task_name="击败魔裔",min_level=1240,pretaskid=7150,exp=1566904199728120,commit_desc="击败来敌0/100",},
[7170]={task_id=7170,task_name="力挽狂澜",min_level=1240,pretaskid=7160,exp=1566904199728120,commit_dialog=23002,accept_desc="不论谁挡在我面前",progress_desc="不论谁挡在我面前",commit_desc="不论谁挡在我面前",},
[7180]={task_id=7180,task_name="给我留下",pretaskid=7170,commit_npc={id=10517,scene=1005,x=62,y=225},commit_dialog=23102,commit_desc="击败100/100",},
[7190]={task_id=7190,task_name="破坏法阵",pretaskid=7180,commit_npc={id=10519,scene=1005,x=167,y=264},commit_dialog=23201,accept_desc="破话天之痕法阵",progress_desc="破话天之痕法阵",commit_desc="破话天之痕法阵",Bubble_Tips="完成主线，可开启新功能",},
[7200]={task_id=7200,task_name="击败守卫",min_level=1260,pretaskid=7190,exp=2152519209646550,accept_desc="击败来敌",progress_desc="击败来敌(<per>0/100</per>) ",commit_desc="击败100/100",},
[7210]={task_id=7210,task_name="血债血偿",ver=2,min_level=1260,pretaskid=7200,commit_npc={id=10522,scene=1005,x=133,y=151},exp=2152519209646550,commit_dialog=23202,accept_desc="最后的决战",progress_desc="最后的决战",commit_desc="最后的决战",},
[7220]={task_id=7220,task_name="最后的战斗",pretaskid=7210,commit_npc={id=10502,scene=1005,x=185,y=232},commit_dialog=23301,},
[7230]={task_id=7230,min_level=1280,pretaskid=7220,exp=2610981299592160,accept_desc="与雪儿与雪儿对话",},
[7240]={task_id=7240,min_level=1280,pretaskid=7230,exp=2610981299592160,},
[7250]={task_id=7250,task_name="凝练真身",min_level=1280,pretaskid=7240,commit_npc={id=10515,scene=1005,x=12,y=52},exp=2610981299592160,commit_dialog=23501,accept_desc="与雪儿对话",progress_desc="与雪儿对话",commit_desc="与雪儿对话",},
[7260]={task_id=7260,task_name="磨炼能力",pretaskid=7250,},
[7270]={task_id=7270,min_level=1300,pretaskid=7260,commit_npc={id=10515,scene=1005,x=12,y=52},exp=2741293799592130,},
[7280]={task_id=7280,task_name="能力试炼",pretaskid=7270,},
[7290]={task_id=7290,min_level=1300,pretaskid=7280,commit_npc={id=10518,scene=1005,x=143,y=232},exp=2741293799592130,},
[7300]={task_id=7300,min_level=1300,pretaskid=7290,exp=2741293799592130,},
[7310]={task_id=7310,min_level=1320,pretaskid=7300,exp=2871606299592140,},
[7320]={task_id=7320,min_level=1320,pretaskid=7310,exp=2871606299592140,},
[7330]={task_id=7330,min_level=1320,pretaskid=7320,commit_npc={id=10522,scene=1005,x=133,y=151},exp=2871606299592140,},
[7340]={task_id=7340,task_name="磨炼能力",pretaskid=7330,},
[7350]={task_id=7350,min_level=1340,pretaskid=7340,exp=3001918799592190,},
[7360]={task_id=7360,min_level=1340,pretaskid=7350,exp=3001918799592190,},
[7370]={task_id=7370,min_level=1340,pretaskid=7360,commit_npc={id=10519,scene=1005,x=167,y=264},exp=3001918799592190,},
[7380]={task_id=7380,task_name="磨炼能力",pretaskid=7370,},
[7390]={task_id=7390,min_level=1360,pretaskid=7380,commit_npc={id=10519,scene=1005,x=167,y=264},exp=3132231299592180,},
[7400]={task_id=7400,min_level=1360,pretaskid=7390,exp=3132231299592180,},
[7410]={task_id=7410,min_level=1360,pretaskid=7400,commit_npc={id=10502,scene=1005,x=185,y=232},exp=3132231299592180,},
[7420]={task_id=7420,task_name="磨炼能力",pretaskid=7410,},
[7430]={task_id=7430,min_level=1380,pretaskid=7420,commit_npc={id=10517,scene=1005,x=62,y=225},exp=3262543799592210,},
[7440]={task_id=7440,task_name="能力试炼",pretaskid=7430,},
[7450]={task_id=7450,min_level=1380,pretaskid=7440,commit_npc={id=10516,scene=1005,x=31,y=183},exp=3262543799592210,},
[7460]={task_id=7460,min_level=1380,pretaskid=7450,exp=3262543799592210,},
[7470]={task_id=7470,min_level=1400,pretaskid=7460,commit_npc={id=10518,scene=1005,x=143,y=232},exp=3392856299592180,},
[7480]={task_id=7480,task_name="能力试炼",pretaskid=7470,},
[7490]={task_id=7490,min_level=1400,pretaskid=7480,exp=3392856299592180,},
[7500]={task_id=7500,min_level=1400,pretaskid=7490,exp=3392856299592180,},
[7510]={task_id=7510,task_name="增强实力",pretaskid=7500,commit_npc={id=10501,scene=1005,x=134,y=122},commit_dialog=23402,Bubble_Tips="完成主线，可开启新功能",},
[7520]={task_id=7520,task_name="能力试炼",pretaskid=7510,},
[7530]={task_id=7530,min_level=1420,pretaskid=7520,commit_npc={id=10503,scene=1005,x=176,y=244},exp=3523168799592160,},
[7540]={task_id=7540,task_name="磨炼能力",min_level=1420,pretaskid=7530,c_param2=100,exp=3523168799592160,accept_desc="击败魔族",progress_desc="击败魔族(<per>0/100</per>) ",commit_desc="击败魔族100/100",},
[7550]={task_id=7550,min_level=1440,pretaskid=7540,exp=4871308399456290,},
[7560]={task_id=7560,min_level=1440,pretaskid=7550,exp=4871308399456290,},
[7570]={task_id=7570,min_level=1440,pretaskid=7560,exp=4871308399456290,},
[7580]={task_id=7580,min_level=1440,pretaskid=7570,exp=4871308399456290,},
[7590]={task_id=7590,min_level=1460,pretaskid=7580,exp=5045058399456300,showitem_0_1="36417,17000",showitem_0_2="36417,17000",showitem_0_3="36417,17000",showitem_1_1="36417,17000",showitem_1_2="36417,17000",showitem_1_3="36417,17000",showitem_0_4="36417,17000",showitem_1_4="36417,17000",},
[7600]={task_id=7600,min_level=1460,pretaskid=7590,c_param1=10501,exp=5045058399456300,target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[7610]={task_id=7610,min_level=1460,pretaskid=7600,exp=5045058399456300,},
[7620]={task_id=7620,min_level=1460,pretaskid=7610,exp=5045058399456300,},
[7630]={task_id=7630,min_level=1480,pretaskid=7620,exp=5218808399456290,},
[7640]={task_id=7640,min_level=1480,pretaskid=7630,exp=5218808399456290,},
[7650]={task_id=7650,min_level=1480,pretaskid=7640,exp=5218808399456290,},
[7660]={task_id=7660,min_level=1480,pretaskid=7650,exp=5218808399456290,},
[7670]={task_id=7670,min_level=1500,pretaskid=7660,exp=5392558399456300,},
[7680]={task_id=7680,min_level=1500,pretaskid=7670,c_param1=1512,exp=5392558399456300,target_obj={{id=1512,scene=1005,x=164,y=268},},},
[7690]={task_id=7690,min_level=1500,pretaskid=7680,exp=5392558399456300,},
[7700]={task_id=7700,min_level=1500,pretaskid=7690,exp=5392558399456300,},
[7710]={task_id=7710,task_name="隐月愁阵",min_level=1520,pretaskid=7700,commit_npc={id=10509,scene=1005,x=222,y=107},exp=5566308399456320,commit_dialog=13503,accept_desc="隐月愁阵",progress_desc="隐月愁阵",commit_desc="隐月愁阵",},
[7720]={task_id=7720,min_level=1520,pretaskid=7710,exp=5566308399456320,},
[7730]={task_id=7730,min_level=1520,pretaskid=7720,exp=5566308399456320,},
[7740]={task_id=7740,min_level=1520,pretaskid=7730,c_param1=1504,exp=5566308399456320,target_obj={{id=1504,scene=1005,x=275,y=199},},},
[7750]={task_id=7750,min_level=1540,pretaskid=7740,exp=5740058399456290,},
[7760]={task_id=7760,min_level=1540,pretaskid=7750,exp=5740058399456290,},
[7770]={task_id=7770,task_name="计上心头",min_level=1540,pretaskid=7760,commit_npc={id=10512,scene=1005,x=20,y=85},exp=5740058399456290,commit_dialog=13803,accept_desc="计上心头",progress_desc="计上心头",commit_desc="计上心头",},
[7780]={task_id=7780,min_level=1540,pretaskid=7770,exp=5740058399456290,},
[7790]={task_id=7790,min_level=1560,pretaskid=7780,exp=5913808399456320,},
[7800]={task_id=7800,min_level=1560,pretaskid=7790,exp=5913808399456320,},
[7810]={task_id=7810,min_level=1560,pretaskid=7800,c_param1=1512,exp=5913808399456320,target_obj={{id=1512,scene=1005,x=164,y=268},},},
[7820]={task_id=7820,min_level=1560,pretaskid=7810,exp=5913808399456320,},
[7830]={task_id=7830,min_level=1580,pretaskid=7820,exp=6087558399456290,},
[7840]={task_id=7840,min_level=1580,pretaskid=7830,commit_npc={id=10514,scene=1005,x=40,y=179},exp=6087558399456290,},
[7850]={task_id=7850,min_level=1580,pretaskid=7840,exp=6087558399456290,},
[7860]={task_id=7860,min_level=1580,pretaskid=7850,exp=6087558399456290,},
[7870]={task_id=7870,task_name="隐月潮阵",pretaskid=7860,commit_npc={id=10516,scene=1005,x=31,y=183},commit_dialog=14203,accept_desc="隐月潮阵",progress_desc="隐月潮阵",commit_desc="隐月潮阵",},
[7880]={task_id=7880,min_level=1600,pretaskid=7870,exp=6261308399456290,},
[7890]={task_id=7890,min_level=1600,pretaskid=7880,exp=6261308399456290,},
[7900]={task_id=7900,min_level=1600,pretaskid=7890,exp=6261308399456290,},
[7910]={task_id=7910,min_level=1620,pretaskid=7900,exp=6435058399456290,},
[7920]={task_id=7920,min_level=1620,pretaskid=7910,exp=6435058399456290,},
[7930]={task_id=7930,min_level=1620,pretaskid=7920,exp=6435058399456290,},
[7940]={task_id=7940,min_level=1620,pretaskid=7930,exp=6435058399456290,},
[7950]={task_id=7950,task_name="瀚海三层",pretaskid=7940,commit_dialog=14801,},
[7960]={task_id=7960,min_level=1640,pretaskid=7950,exp=6608808399456290,},
[7970]={task_id=7970,ver=2,min_level=1640,pretaskid=7960,exp=6608808399456290,},
[7980]={task_id=7980,min_level=1640,pretaskid=7970,exp=6608808399456290,},
[7990]={task_id=7990,min_level=1660,pretaskid=7980,exp=6782558399456290,},
[8000]={task_id=8000,min_level=1660,pretaskid=7990,exp=6782558399456290,},
[8010]={task_id=8010,min_level=1660,pretaskid=8000,exp=6782558399456290,},
[8020]={task_id=8020,min_level=1660,pretaskid=8010,exp=6782558399456290,},
[8030]={task_id=8030,task_name="阵法奇特",min_level=1680,pretaskid=8020,commit_npc={id=10504,scene=1005,x=241,y=286},exp=6956308399456290,commit_dialog=12902,accept_desc="阵法奇特",progress_desc="阵法奇特",commit_desc="阵法奇特",},
[8040]={task_id=8040,min_level=1680,pretaskid=8030,exp=6956308399456290,},
[8050]={task_id=8050,min_level=1680,pretaskid=8040,exp=6956308399456290,},
[8060]={task_id=8060,min_level=1680,pretaskid=8050,exp=6956308399456290,},
[8070]={task_id=8070,task_name="继续前进",task_type=0,open_day=2,min_level=1700,pretaskid=8060,commit_npc={id=10506,scene=1005,x=210,y=200},coin_bind=100000,exp=7130058399456290,showitem_0_1="36417,18000",showitem_0_2="36417,18000",showitem_0_3="36417,18000",showitem_1_1="36417,18000",showitem_1_2="36417,18000",showitem_1_3="36417,18000",showitem_0_4="36417,18000",showitem_1_4="36417,18000",commit_dialog=13102,accept_desc="继续前进",progress_desc="继续前进",commit_desc="继续前进",show_chaper_id=0,},
[8080]={task_id=8080,min_level=1700,pretaskid=8070,exp=7130058399456290,},
[8090]={task_id=8090,task_name="妖魔的请求",pretaskid=8080,commit_npc={id=10507,scene=1005,x=262,y=191},commit_dialog=13202,accept_desc="魔族的请求",progress_desc="魔族的请求",commit_desc="魔族的请求",},
[8100]={task_id=8100,min_level=1700,pretaskid=8090,exp=7130058399456290,},
[8110]={task_id=8110,min_level=1720,pretaskid=8100,exp=8764570079347520,},
[8120]={task_id=8120,min_level=1720,pretaskid=8110,exp=8764570079347520,},
[8130]={task_id=8130,min_level=1720,pretaskid=8120,exp=8764570079347520,},
[8140]={task_id=8140,min_level=1720,pretaskid=8130,exp=8764570079347520,},
[8150]={task_id=8150,min_level=1740,pretaskid=8140,exp=8973070079347490,},
[8160]={task_id=8160,min_level=1740,pretaskid=8150,exp=8973070079347490,},
[8170]={task_id=8170,min_level=1740,pretaskid=8160,exp=8973070079347490,},
[8180]={task_id=8180,min_level=1740,pretaskid=8170,exp=8973070079347490,},
[8190]={task_id=8190,min_level=1760,pretaskid=8180,exp=9181570079347200,},
[8200]={task_id=8200,min_level=1760,pretaskid=8190,exp=9181570079347200,},
[8210]={task_id=8210,min_level=1760,pretaskid=8200,exp=9181570079347200,},
[8220]={task_id=8220,min_level=1760,pretaskid=8210,exp=9181570079347200,},
[8230]={task_id=8230,min_level=1780,pretaskid=8220,exp=9390070079347270,},
[8240]={task_id=8240,min_level=1780,pretaskid=8230,exp=9390070079347270,},
[8250]={task_id=8250,min_level=1780,pretaskid=8240,exp=9390070079347270,},
[8260]={task_id=8260,min_level=1780,pretaskid=8250,exp=9390070079347270,},
[8270]={task_id=8270,min_level=1800,pretaskid=8260,exp=9598570079347140,},
[8280]={task_id=8280,min_level=1800,pretaskid=8270,exp=9598570079347140,},
[8290]={task_id=8290,min_level=1800,pretaskid=8280,exp=9598570079347140,},
[8300]={task_id=8300,task_name="突破阵眼",pretaskid=8290,},
[8310]={task_id=8310,min_level=1820,pretaskid=8300,exp=9807070079347200,},
[8320]={task_id=8320,min_level=1820,pretaskid=8310,exp=9807070079347200,},
[8330]={task_id=8330,min_level=1820,pretaskid=8320,exp=9807070079347200,},
[8340]={task_id=8340,min_level=1820,pretaskid=8330,exp=9807070079347200,},
[8350]={task_id=8350,min_level=1840,pretaskid=8340,exp=10015570079346800,Bubble_Tips="完成主线，可开启新功能",},
[8360]={task_id=8360,min_level=1840,pretaskid=8350,exp=10015570079346800,},
[8370]={task_id=8370,task_name="瀚海三层",pretaskid=8360,commit_dialog=14801,},
[8380]={task_id=8380,min_level=1840,pretaskid=8370,exp=10015570079346800,},
[8390]={task_id=8390,min_level=1860,pretaskid=8380,exp=10224070079347100,},
[8400]={task_id=8400,min_level=1860,pretaskid=8390,exp=10224070079347100,},
[8410]={task_id=8410,task_name="陌生男子",min_level=1860,pretaskid=8400,commit_npc={id=10502,scene=1005,x=185,y=232},exp=10224070079347100,commit_dialog=12702,accept_desc="陌生男子",progress_desc="陌生男子",commit_desc="陌生男子",Bubble_Tips="完成主线，可开启新功能",},
[8420]={task_id=8420,min_level=1860,pretaskid=8410,accept_npc={id=10502,scene=1005,x=185,y=232},exp=10224070079347100,item_list={[0]=item_table[11]},showitem_0_1="26376,5",showitem_0_2="26376,5",showitem_0_3="26376,5",showitem_1_1="26376,5",showitem_1_2="26376,5",showitem_1_3="26376,5",showitem_0_4="26376,5",showitem_1_4="26376,5",},
[8430]={task_id=8430,min_level=1880,pretaskid=8420,exp=10432570079348700,},
[8440]={task_id=8440,min_level=1880,pretaskid=8430,exp=10432570079348700,},
[8450]={task_id=8450,min_level=1880,pretaskid=8440,exp=10432570079348700,},
[8460]={task_id=8460,min_level=1880,pretaskid=8450,exp=10432570079348700,},
[8470]={task_id=8470,task_name="妖兽统领",pretaskid=8460,commit_npc={id=10505,scene=1005,x=46,y=72},commit_dialog=12903,accept_desc="妖兽统领",progress_desc="妖兽统领",commit_desc="妖兽统领",Bubble_Tips="完成主线，可开启新功能",},
[8480]={task_id=8480,min_level=1900,pretaskid=8470,exp=10641070079348800,},
[8490]={task_id=8490,min_level=1900,pretaskid=8480,exp=10641070079348800,},
[8500]={task_id=8500,task_name="杀出重围",min_level=1900,pretaskid=8490,c_param1=10508,exp=10641070079348800,target_obj={{id=10508,scene=1005,x=122,y=330},{id=10508,scene=1005,x=127,y=331},{id=10508,scene=1005,x=124,y=332},{id=10508,scene=1005,x=131,y=333},{id=10508,scene=1005,x=121,y=327},{id=10508,scene=1005,x=120,y=334},{id=10508,scene=1005,x=127,y=335},},},
[8510]={task_id=8510,min_level=1920,pretaskid=8500,exp=10849570079349000,},
[8520]={task_id=8520,task_name="拾取腰牌",pretaskid=8510,condition=2,c_param1=1505,c_param2=1,accept_desc="拾取腰牌",progress_desc="拾取腰牌",commit_desc="拾取腰牌",target_obj={{id=1505,scene=1005,x=83,y=98},},},
[8530]={task_id=8530,task_name="暖雪的疑惑",pretaskid=8520,commit_npc={id=10508,scene=1005,x=246,y=122},commit_dialog=13302,accept_desc="暖雪的疑惑",progress_desc="暖雪的疑惑",commit_desc="暖雪的疑惑",Bubble_Tips="完成主线，可开启新功能",},
[8540]={task_id=8540,task_name="继续前进",task_type=0,open_day=2,min_level=1920,pretaskid=8530,condition=1,c_param1=10509,c_param2=60,coin_bind=100000,exp=10849570079349000,showitem_0_1="36417,18000",showitem_0_2="36417,18000",showitem_0_3="36417,18000",showitem_1_1="36417,18000",showitem_1_2="36417,18000",showitem_1_3="36417,18000",showitem_0_4="36417,18000",showitem_1_4="36417,18000",accept_desc="击败魔怪",progress_desc="击败魔怪(<per>0/60</per>) ",commit_desc="击败魔怪60/60",show_chaper_id=0,target_obj={{id=10509,scene=1005,x=166,y=65},{id=10509,scene=1005,x=169,y=63},{id=10509,scene=1005,x=167,y=62},{id=10509,scene=1005,x=172,y=64},{id=10509,scene=1005,x=170,y=66},{id=10509,scene=1005,x=171,y=59},{id=10509,scene=1005,x=171,y=62},{id=10509,scene=1005,x=165,y=61},},},
[24001]={task_id=24001,task_name="护送任务",task_type=3,min_level=57,pretaskid=0,accept_npc={id=10311,scene=1003,x=350,y=74},commit_npc={id=10315,scene=1003,x=293,y=141},coin_bind=100000,item_list={[0]=item_table[12]},accept_dialog=999,commit_dialog=999,progress_desc="",commit_desc="消灭野猪1/20",fb_tip_desc="进入副本挑战无限战争",fb_tip_title="神族之战",Bubble_Tips="护送可获海量经验",},
[17000]={task_id=17000,task_name="技能升级",open_love=100,min_level=120,c_param1=87,c_param2=40,progress_desc="技能总等级达到 <per>0/40</per> 级",commit_desc="技能总等级达到 40/40 级",open_panel_name="skill_view#skill_zhudong",taskgroup=101,},
[17001]={task_id=17001,task_name="技能升级",task_type=1,open_love=100,min_level=160,camp="",condition=7,c_param1=87,c_param2=80,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[13]},showitem_0_1="26000,1",showitem_0_2="26000,1",showitem_0_3="26000,1",showitem_1_1="26000,1",showitem_1_2="26000,1",showitem_1_3="26000,1",showitem_0_4="26000,1",showitem_1_4="26000,1",progress_desc="技能总等级达到 <per>0/80</per> 级",commit_desc="技能总等级达到 80/80 级",open_panel_name="skill_view#skill_zhudong",is_first="",taskgroup=102,target_obj={},},
[17002]={task_id=17002,min_level=180,c_param2=100,progress_desc="技能总等级达到 <per>100/100</per> 级",commit_desc="技能总等级达到 100/100 级",},
[17003]={task_id=17003,min_level=220,c_param2=120,},
[17030]={task_id=17030,task_name="宠物上阵",open_love=2000,min_level=2000,c_param1=50,c_param2=1,c_param3=2,progress_desc="孵化并上阵 <per>0/1</per> 只紫宠",commit_desc="孵化并上阵 1/1 只紫宠",open_panel_name="PetView#pet_hatch",taskgroup=130,},
[17040]={task_id=17040,task_name="装备强化",task_type=1,open_love=100,min_level=110,camp="",condition=7,c_param1=65,c_param2=10,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[14]},showitem_0_1="26200,1",showitem_0_2="26200,1",showitem_0_3="26200,1",showitem_1_1="26200,1",showitem_1_2="26200,1",showitem_1_3="26200,1",showitem_0_4="26200,1",showitem_1_4="26200,1",progress_desc="装备总强化达到 <per>0/15</per> 级",commit_desc="装备总强化达到 15/15 级",open_panel_name="equipment#equipment_strength",is_first="",taskgroup=103,target_obj={},},
[17060]={task_id=17060,task_name="灵妖奇脉",open_love=100,min_level=255,condition=1,c_param1=63,c_param4=66798383,c_param5=2,progress_desc="击杀灵妖奇脉的骨灵女魔0/1次",commit_desc="击杀灵妖奇脉的骨灵女魔1/1次",open_panel_name="boss#boss_personal",taskgroup=104,},
[17061]={task_id=17061,min_level=305,c_param1=64,c_param5=3,progress_desc="击杀灵妖奇脉的幽邃魔罗0/1次",commit_desc="击杀灵妖奇脉的幽邃魔罗1/1次",},
[17062]={task_id=17062,min_level=355,c_param1=65,c_param5=4,progress_desc="击杀灵妖奇脉的噬渊阎魔0/1次",commit_desc="击杀灵妖奇脉的噬渊阎魔1/1次",},
[17120]={task_id=17120,task_name="兑换元宝",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=72,c_param2=1,coin_bind=100000,progress_desc="兑换 <per>0/1</per> 次元宝",commit_desc="兑换 1/1 次元宝",is_first="",taskgroup=109,target_obj={},},
[17121]={task_id=17121,task_name="购买仙镯",c_param3=9126,progress_desc="购买仙镯 <per>0/1</per>",commit_desc="购买仙镯 1/1",},
[17122]={task_id=17122,task_name="购买仙戒",c_param1=25,c_param3=8126,progress_desc="购买仙戒 <per>0/1</per>",commit_desc="购买仙戒 1/1",taskgroup=110,},
[17160]={task_id=17160,task_name="装备熔炼",c_param1=56,item_list={[0]=item_table[15]},showitem_0_1="36420,10",showitem_0_2="36420,10",showitem_0_3="36420,10",showitem_1_1="36420,10",showitem_1_2="36420,10",showitem_1_3="36420,10",showitem_0_4="36420,10",showitem_1_4="36420,10",progress_desc="进行 <per>0/1</per> 次装备熔炼",commit_desc="进行 1/1 次装备熔炼",open_panel_name="bag_view",},
[17170]={task_id=17170,task_name="能说会道",c_param1=44,progress_desc="在世界发言 <per>0/1</per> 次",commit_desc="在世界发说话 1/1 次",open_panel_name="ChatView#30#op=冒个泡，求妹子摸/3",},
[17171]={task_id=17171,task_name="交朋结伴",c_param1=60,c_param2=5,progress_desc="拥有 <per>0/5</per> 个好友",commit_desc="拥有 5/5 个好友",open_panel_name="open_keyaddfriend",},
[17172]={task_id=17172,task_name="惺惺相惜",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=74,c_param2=1,c_param4=0,c_param5=0,coin_bind=100000,shengwang=10,showitem_0_1="90071,10",showitem_0_2="90071,10",showitem_0_3="90071,10",showitem_1_1="90071,10",showitem_1_2="90071,10",showitem_1_3="90071,10",showitem_0_4="90071,10",showitem_1_4="90071,10",progress_desc="进行 <per>0/1</per> 次好友虎摸",commit_desc="进行 1/1 次好友虎摸",open_panel_name="society#society_friend",is_first="",taskgroup=117,target_obj={},},
[17190]={task_id=17190,task_name="同道中人",c_param1=16,showitem_0_1="36425,30",showitem_0_2="36425,30",showitem_0_3="36425,30",showitem_1_1="36425,30",showitem_1_2="36425,30",showitem_1_3="36425,30",showitem_0_4="36425,30",showitem_1_4="36425,30",progress_desc="加入 <per>0/1</per> 个仙盟",commit_desc="加入 1/1 个仙盟",open_panel_name="guild",taskgroup=112,},
[17191]={task_id=17191,task_name="同道中人",c_param1=44,c_param2=1,c_param3=4,progress_desc="在仙盟频道说话 <per>0/1</per> 次",commit_desc="在仙盟频道说话 1/1 次",open_panel_name="ChatView#60#op=萌新报道！求大腿！/108",taskgroup=112,},
[17193]={task_id=17193,task_name="名望任务",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=64,c_param2=5,c_param4=0,c_param5=0,coin_bind=100000,shengwang=50,showitem_0_1="90071,50",showitem_0_2="90071,50",showitem_0_3="90071,50",showitem_1_1="90071,50",showitem_1_2="90071,50",showitem_1_3="90071,50",showitem_0_4="90071,50",showitem_1_4="90071,50",progress_desc="完成仙盟名望任务 <per>0/5</per> 次",commit_desc="完成仙盟名望任务 5/5 次",open_panel_name="guild_task",is_first="",taskgroup=141,target_obj={},},
[17194]={task_id=17194,task_name="仙盟协助",c_param1=47,c_param2=1,progress_desc="仙盟协助 <per>0/1</per> 次",commit_desc="仙盟协助 1/1 次",open_panel_name="boss_assist",taskgroup=112,},
[17210]={task_id=17210,task_name="心魔·贪",condition=3,c_param1=20,c_param2=1,c_param3=1,progress_desc="通关心魔·贪 <per>0/1</per>",commit_desc="通关心魔·贪  1/1",open_panel_name="TianJiPuXinMoView",taskgroup=113,},
[17230]={task_id=17230,task_name="九重劫塔",task_type=1,open_love=100,min_level=120,camp="",condition=7,c_param1=62,c_param2=10,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[16]},showitem_0_1="43090,1",showitem_0_2="43090,1",showitem_0_3="43090,1",showitem_1_1="43090,1",showitem_1_2="43090,1",showitem_1_3="43090,1",showitem_0_4="43090,1",showitem_1_4="43090,1",progress_desc="达到<per>0/10</per>重",commit_desc="达到10/10重",open_panel_name="fubenpanel#fubenpanel_welkin",is_first="",taskgroup=114,target_obj={},},
[17231]={task_id=17231,min_level=145,pretaskid=17230,c_param2=20,progress_desc="达到<per>0/20</per>重",commit_desc="达到20/20重",},
[17232]={task_id=17232,min_level=150,pretaskid=17231,c_param2=30,progress_desc="达到<per>0/30</per>重",commit_desc="达到30/30重",},
[17233]={task_id=17233,min_level=155,pretaskid=17232,c_param2=40,progress_desc="达到<per>0/40</per>重",commit_desc="达到40/40重",},
[17234]={task_id=17234,min_level=160,pretaskid=17233,c_param2=50,progress_desc="达到<per>0/50</per>重",commit_desc="达到50/50重",},
[17235]={task_id=17235,min_level=180,pretaskid=17234,c_param2=70,progress_desc="达到<per>0/70</per>重",commit_desc="达到70/70重",},
[17236]={task_id=17236,min_level=190,pretaskid=17235,c_param2=90,item_list={[0]=item_table[17]},progress_desc="达到<per>0/90</per>重",commit_desc="达到90/90重",},
[17290]={task_id=17290,task_name="装备寄售",c_param1=82,progress_desc="出售 <per>0/1</per> 件4阶或以上红装",commit_desc="出售 1/1 件4阶或以上红装",open_panel_name="market#market_jisou_list",taskgroup=118,},
[17291]={task_id=17291,task_name="购买装备",c_param1=83,progress_desc="购买 <per>0/1</per> 件4阶或以上红装",commit_desc="购买 1/1 件4阶或以上红装",open_panel_name="market#market_buy",},
[17312]={task_id=17312,task_name="装备合成",c_param1=86,bind_gold=10,showitem_0_1="22636,1",showitem_0_2="22636,1",showitem_0_3="22636,1",showitem_1_1="22636,1",showitem_1_2="22636,1",showitem_1_3="22636,1",showitem_0_4="22636,1",showitem_1_4="22636,1",progress_desc="合成 <per>0/1</per> 件红3星装备",commit_desc="合成 1/1 件红3星装备",open_panel_name="other_compose#other_compose_eq_hecheng_one",taskgroup=141,},
[17330]={task_id=17330,task_name="元宝祈福",open_love=1,min_level=180,c_param1=75,c_param2=1,progress_desc="进行 <per>0/1</per> 次元宝祈福",commit_desc="进行 1/1 次元宝祈福",open_panel_name="qifu#qifu_qf",taskgroup=117,},
[17340]={task_id=17340,task_name="竞技·天梯争霸",open_love=1,min_level=200,c_param1=123,c_param2=10,progress_desc="挑战 <per>0/10</per> 次",commit_desc="挑战 10/10 次",open_panel_name="act_jjc#arena_field1v1",taskgroup=121,},
[17380]={task_id=17380,task_name="仙遗洞天",open_love=140,min_level=140,c_param1=53,c_param3=5,progress_desc="击杀<per>0/6</per> 只仙遗洞天BOSS",commit_desc="击杀6/6 只仙遗洞天BOSS",open_panel_name="boss#boss_vip",taskgroup=124,},
[17381]={task_id=17381,open_love=160,min_level=160,pretaskid=17380,},
[17382]={task_id=17382,open_love=180,min_level=180,pretaskid=17381,c_param2=8,progress_desc="击杀<per>0/8</per> 只仙遗洞天BOSS",commit_desc="击杀8/8 只仙遗洞天BOSS",},
[17383]={task_id=17383,open_love=200,min_level=200,pretaskid=17382,},
[17384]={task_id=17384,open_love=220,min_level=220,pretaskid=17383,},
[17385]={task_id=17385,open_love=240,min_level=240,pretaskid=17384,},
[17470]={task_id=17470,task_name="离线挂机",task_type=1,open_love=2000,min_level=2000,camp="",commit_npc={id=10315,scene=1003,x=293,y=141},c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[18]},showitem_0_1="22537,1",showitem_0_2="22537,1",showitem_0_3="22537,1",showitem_1_1="22537,1",showitem_1_2="22537,1",showitem_1_3="22537,1",showitem_0_4="22537,1",showitem_1_4="22537,1",commit_dialog=170812,progress_desc="找算半仙了解离线挂机",commit_desc="找算半仙了解离线挂机",is_first="",taskgroup=111,},
[17471]={task_id=17471,task_name="离线挂机",c_param1=48,showitem_0_1="36417,10000",showitem_0_2="36417,10000",showitem_0_3="36417,10000",showitem_1_1="36417,10000",showitem_1_2="36417,10000",showitem_1_3="36417,10000",showitem_0_4="36417,10000",showitem_1_4="36417,10000",progress_desc="补充 <per>0/1</per> 次离线挂机时间",commit_desc="补充 1/1 次离线挂机时间",taskgroup=111,},
[17480]={task_id=17480,task_name="今日活跃",open_love=130,min_level=130,c_param1=92,c_param2=30,progress_desc="今日活跃到达 <per>30/30</per>",commit_desc="今日活跃到达 30/30",open_panel_name="bizuo#bizuo_bizuo",taskgroup=131,},
[17481]={task_id=17481,open_love=160,min_level=160,pretaskid=17480,c_param2=120,exp=105000,progress_desc="今日活跃到达 <per>120/120</per>",commit_desc="今日活跃到达 120/120",},
[17482]={task_id=17482,open_love=200,min_level=2000,pretaskid=17481,c_param2=180,progress_desc="今日活跃到达 <per>180/180</per>",commit_desc="今日活跃到达 180/180",},
[17483]={task_id=17483,open_love=2000,pretaskid=17482,c_param2=200,progress_desc="今日活跃到达 <per>200/200</per>",commit_desc="今日活跃到达 200/200",},
[17490]={task_id=17490,task_name="跨服3v3",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=91,c_param4=0,c_param5=0,coin_bind=100000,progress_desc="在跨服3v3加入1个战队",commit_desc="在跨服3v3加入 1 个战队",open_panel_name="KF3v3View#kf_pvp_3v3info",is_first="",taskgroup=132,target_obj={},},
[17500]={task_id=17500,task_name="神炼宝塔",task_type=1,open_love=2000,min_level=2000,camp="",condition=3,c_param1=52,c_param2=1,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[19]},showitem_0_1="27611,5",showitem_0_2="27611,5",showitem_0_3="27611,5",showitem_1_1="27611,5",showitem_1_2="27611,5",showitem_1_3="27611,5",showitem_0_4="27611,5",showitem_1_4="27611,5",progress_desc="进行 <per>0/1</per> 次神炼宝塔",commit_desc="进行 1 次神炼宝塔",open_panel_name="fubenpanel#fubenpanel_bagua",is_first="",taskgroup=133,target_obj={},},
[17510]={task_id=17510,task_name="洗炼开槽",c_param1=95,item_list={[0]=item_table[20]},showitem_0_1="36420,20",showitem_0_2="36420,20",showitem_0_3="36420,20",showitem_1_1="36420,20",showitem_1_2="36420,20",showitem_1_3="36420,20",showitem_0_4="36420,20",showitem_1_4="36420,20",progress_desc="解锁 <per> 0/1</per> 个装备洗炼孔",commit_desc="解锁 1 个装备洗炼孔",open_panel_name="equipment#equipment_xilian",taskgroup=134,},
[17511]={task_id=17511,task_name="装备洗炼",c_param1=96,c_param2=2,item_list={[0]=item_table[21]},showitem_0_1="26415,5",showitem_0_2="26415,5",showitem_0_3="26415,5",showitem_1_1="26415,5",showitem_1_2="26415,5",showitem_1_3="26415,5",showitem_0_4="26415,5",showitem_1_4="26415,5",progress_desc="进行 <per> 0/2</per> 次装备洗炼",commit_desc="进行 2 次装备洗炼",},
[17520]={task_id=17520,task_name="诛魔套装",c_param1=97,item_list={[0]=item_table[22]},showitem_0_1="26162,1",showitem_0_2="26162,1",showitem_0_3="26162,1",showitem_1_1="26162,1",showitem_1_2="26162,1",showitem_1_3="26162,1",showitem_0_4="26162,1",showitem_1_4="26162,1",progress_desc="锻造 <per>0/1</per> 件诛魔套",commit_desc="锻造 1 件诛魔套",open_panel_name="equipment#equipment_suit",taskgroup=135,},
[17530]={task_id=17530,task_name="巨鲲降世",c_param1=46,c_param3=23,progress_desc="修复天机谱·二卷获得<color=#fffc00>鲲</color>",commit_desc="修复天机谱·二卷获得鲲",open_panel_name="TianJiPuView#2#23",taskgroup=136,},
[17540]={task_id=17540,task_name="仙器升阶",c_param1=98,c_param2=6,progress_desc="手镯提升至 <per>0/6</per> 阶",commit_desc="手镯提升至 6 阶",open_panel_name="other_compose#other_compose_shengjie",taskgroup=137,},
[17550]={task_id=17550,task_name="蛮荒妖谷",c_param1=44,item_list={[0]=item_table[23]},showitem_0_1="26416,2",showitem_0_2="26416,2",showitem_0_3="26416,2",showitem_1_1="26416,2",showitem_1_2="26416,2",showitem_1_3="26416,2",showitem_0_4="26416,2",showitem_1_4="26416,2",progress_desc="进行 <per>0/1</per> 次蛮荒妖谷",commit_desc="进行 1 次蛮荒妖谷",open_panel_name="fubenpanel#fubenpanel_equip_high",taskgroup=138,},
[17560]={task_id=17560,task_name="上古兽魂",c_param1=101,item_list={[0]=item_table[24]},showitem_0_1="43009,1",showitem_0_2="43009,1",showitem_0_3="43009,1",showitem_1_1="43009,1",showitem_1_2="43009,1",showitem_1_3="43009,1",showitem_0_4="43009,1",showitem_1_4="43009,1",progress_desc="激活 <per>0/1</per> 只异兽",commit_desc="激活 1 只异兽",open_panel_name="shenshou",taskgroup=139,},
[17570]={task_id=17570,task_name="龙魂附身",task_type=1,open_day=999,open_love=2000,min_level=2000,camp="",condition=7,c_param1=103,c_param2=1,c_param3=1,c_param4=0,c_param5=0,coin_bind=100000,showitem_0_1="22529,100",showitem_0_2="22529,100",showitem_0_3="22529,100",showitem_1_1="22529,100",showitem_1_2="22529,100",showitem_1_3="22529,100",showitem_0_4="22529,100",showitem_1_4="22529,100",progress_desc="镶嵌 <per>0/1</per> 个龙魂",commit_desc="镶嵌 1 个龙魂",open_panel_name="fuwen#long_hun_xiangqian",is_first="",taskgroup=140,target_obj={},},
[17600]={task_id=17600,task_name="收集神装",open_love=100,min_level=120,c_param1=85,c_param2=4,c_param3=1,c_param4=5,c_param5=3,progress_desc="穿戴<per>0/4</per>件1阶红品以上装备",commit_desc="穿戴 4件1阶红品以上装备",open_panel_name="boss#boss_vip",},
[17601]={task_id=17601,min_level=140,pretaskid=17600,c_param2=6,progress_desc="穿戴<per>0/6</per>件1阶红品以上装备",commit_desc="穿戴 6件1阶红品以上装备",},
[17602]={task_id=17602,min_level=150,c_param3=2,progress_desc="穿戴<per>0/4</per>件2阶红品以上装备",commit_desc="穿戴 4件2阶红品以上装备",},
[17603]={task_id=17603,min_level=170,pretaskid=17602,c_param2=6,progress_desc="穿戴<per>0/6</per>件2阶红品以上装备",commit_desc="穿戴 6件2阶红品以上装备",},
[17604]={task_id=17604,open_love=2000,min_level=2000,c_param2=2,progress_desc="穿戴<per>0/4</per>件3阶红品以上装备",commit_desc="穿戴 4件3阶红品以上装备",},
[17605]={task_id=17605,pretaskid=17604,progress_desc="穿戴<per>0/6</per>件3阶红品以上装备",commit_desc="穿戴 6件3阶红品以上装备",},
[17606]={task_id=17606,progress_desc="穿戴<per>0/4</per>件4阶红品以上装备",commit_desc="穿戴 4件4阶红品以上装备",},
[17607]={task_id=17607,pretaskid=17606,progress_desc="穿戴<per>0/6</per>件4阶红品以上装备",commit_desc="穿戴 6件4阶红品以上装备",},
[17610]={task_id=17610,task_name="斗技场壹",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=123,c_param2=1,c_param4=0,c_param5=0,coin_bind=200000,exp=210000,showitem_0_1="36417,200000",showitem_0_2="36417,200000",showitem_0_3="36417,200000",showitem_1_1="36417,200000",showitem_1_2="36417,200000",showitem_1_3="36417,200000",showitem_0_4="36417,200000",showitem_1_4="36417,200000",progress_desc="进行 <per>0/1</per> 次竞技·天梯争霸1V1",commit_desc="进行 1 次竞技·天梯争霸1V1",open_panel_name="act_jjc#arena_field1v1",is_first="",taskgroup=142,target_obj={},},
[17615]={task_id=17615,task_name="副本·暗翼之巢",task_type=1,open_day=4,open_love=100,min_level=130,camp="",condition=3,c_param1=33,c_param2=1,c_param3=1,c_param4=0,c_param5=0,coin_bind=100000,exp=105000,item_list={[0]=item_table[25]},showitem_0_1="26376,2",showitem_0_2="26376,2",showitem_0_3="26376,2",showitem_1_1="26376,2",showitem_1_2="26376,2",showitem_1_3="26376,2",showitem_0_4="26376,2",showitem_1_4="26376,2",progress_desc="通关 <per>0/1</per> 次暗翼之巢一层",commit_desc="进行 1 次暗翼之巢一层",open_panel_name="fubenpanel#fubenpanel_pet",is_first="",taskgroup=143,target_obj={},},
[17616]={task_id=17616,task_name="副本·暗翼之巢",task_type=1,open_day=4,open_love=2000,min_level=2000,pretaskid=17615,camp="",condition=3,c_param1=33,c_param2=1,c_param3=2,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[25]},showitem_0_1="26376,2",showitem_0_2="26376,2",showitem_0_3="26376,2",showitem_1_1="26376,2",showitem_1_2="26376,2",showitem_1_3="26376,2",showitem_0_4="26376,2",showitem_1_4="26376,2",progress_desc="进行 <per>0/1</per> 次暗翼之巢二层",commit_desc="进行 1 次暗翼之巢二层",open_panel_name="fubenpanel#fubenpanel_pet",is_first="",taskgroup=143,target_obj={},},
[17617]={task_id=17617,task_name="荒古神冢·壹",condition=9,c_param1=83,c_param2=1,progress_desc="通关 <per>0/1</per> 次荒古神冢",commit_desc="进行 1 次荒古神冢",open_panel_name="fubenpanel#fubenpanel_copper",taskgroup=144,},
[17620]={task_id=17620,task_name="战力目标壹",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=8,c_param2=10,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[26]},showitem_0_1="26200,2",showitem_0_2="26200,2",showitem_0_3="26200,2",showitem_1_1="26200,2",showitem_1_2="26200,2",showitem_1_3="26200,2",showitem_0_4="26200,2",showitem_1_4="26200,2",progress_desc="战力达到<per>0/10</per> 万",commit_desc="战力达到10万",open_panel_name="fubenpanel#fubenpanel_welkin",is_first="",taskgroup=145,target_obj={},},
[17621]={task_id=17621,task_name="战力目标贰",pretaskid=17620,c_param2=30,exp=210000,progress_desc="战力达到<per>0/30</per> 万",commit_desc="战力达到30万",},
[17622]={task_id=17622,task_name="战力目标叁",pretaskid=17621,c_param2=45,progress_desc="战力达到<per>0/45</per> 万",commit_desc="战力达到45万",},
[17623]={task_id=17623,task_name="战力目标肆",pretaskid=17622,c_param2=95,progress_desc="战力达到<per>0/95</per> 万",commit_desc="战力达到95万",open_panel_name="boss#boss_vip",},
[17624]={task_id=17624,task_name="战力目标伍",pretaskid=17623,c_param2=200,progress_desc="战力达到<per>0/200</per> 万",commit_desc="战力达到200万",},
[17625]={task_id=17625,task_name="战力目标陆",pretaskid=17624,c_param2=300,progress_desc="战力达到<per>0/300</per> 万",commit_desc="战力达到300万",},
[17626]={task_id=17626,task_name="战力目标柒",pretaskid=17625,c_param2=350,progress_desc="战力达到<per>0/350</per> 万",commit_desc="战力达到350万",},
[17627]={task_id=17627,task_name="战力目标捌",pretaskid=17626,c_param2=500,progress_desc="战力达到<per>0/500</per> 万",commit_desc="战力达到500万",},
[17628]={task_id=17628,task_name="战力目标玖",pretaskid=17627,c_param2=600,progress_desc="战力达到<per>0/600</per> 万",commit_desc="战力达到600万",},
[17629]={task_id=17629,task_name="战力目标拾",pretaskid=17628,c_param2=800,progress_desc="战力达到<per>0/800</per> 万",commit_desc="战力达到800万",},
[17630]={task_id=17630,task_name="主城护送",task_type=13,open_love=100,min_level=130,camp="",accept_npc={id=10318,scene=1003,x=238,y=394},condition=7,c_param1=10,c_param2=1,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[27]},showitem_0_1="26165,1",showitem_0_2="26165,1",showitem_0_3="26165,1",showitem_1_1="26165,1",showitem_1_2="26165,1",showitem_1_3="26165,1",showitem_0_4="26165,1",showitem_1_4="26165,1",progress_desc="护送<per>0/1</per> 次",commit_desc="护送1次",open_panel_name="YunbiaoView",is_first="",taskgroup=146,target_obj={},},
[17631]={task_id=17631,},
[17632]={task_id=17632,task_name="挑战魔王",task_type=1,open_love=2000,min_level=2000,pretaskid=17630,camp="",condition=7,c_param1=53,c_param2=3,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[28]},showitem_0_1="26500,1",showitem_0_2="26500,1",showitem_0_3="26500,1",showitem_1_1="26500,1",showitem_1_2="26500,1",showitem_1_3="26500,1",showitem_0_4="26500,1",showitem_1_4="26500,1",progress_desc="挑战混沌魔域<per>0/3</per> 次",commit_desc="挑战混沌魔域3次",open_panel_name="boss#boss_vip",is_first="",taskgroup=147,target_obj={},},
[17633]={task_id=17633,task_name="结婚庆典",c_param1=12,item_list={[0]=item_table[29]},showitem_0_1="26121,1",showitem_0_2="26121,1",showitem_0_3="26121,1",showitem_1_1="26121,1",showitem_1_2="26121,1",showitem_1_3="26121,1",showitem_0_4="26121,1",showitem_1_4="26121,1",progress_desc="结婚<per>0/1</per> 次",commit_desc="结婚1次",open_panel_name="marry#marry_jiehun",taskgroup=148,},
[17634]={task_id=17634,task_name="神魔之井",c_param1=15,item_list={[0]=item_table[30]},showitem_0_1="26515,1",showitem_0_2="26515,1",showitem_0_3="26515,1",showitem_1_1="26515,1",showitem_1_2="26515,1",showitem_1_3="26515,1",showitem_0_4="26515,1",showitem_1_4="26515,1",progress_desc="进入神魔之井<per>0/1</per> 次",commit_desc="进入神魔之井1次",open_panel_name="boss#boss_dabao",taskgroup=149,},
[17636]={task_id=17636,task_name="收集套装",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=17,c_param2=2,c_param3=5,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[31]},showitem_0_1="28812,1",showitem_0_2="28812,1",showitem_0_3="28812,1",showitem_1_1="28812,1",showitem_1_2="28812,1",showitem_1_3="28812,1",showitem_0_4="28812,1",showitem_1_4="28812,1",progress_desc="激活5阶以上套装<per>0/2</per>件",commit_desc="激活5阶以上套装2/2件",open_panel_name="equipment#equipment_suit",is_first="",taskgroup=151,target_obj={},},
[17637]={task_id=17637,task_name="猎龙魔渊",c_param1=18,progress_desc="挑战猎龙魔渊<per>0/1</per>次",commit_desc="挑战猎龙魔渊1/1次",open_panel_name="WorldServer#world_new_shenyuan_boss",taskgroup=152,},
[17638]={task_id=17638,task_name="装备洗炼",task_type=1,min_level=300,camp="",condition=7,c_param1=19,c_param2=2,c_param3=3,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[32]},showitem_0_1="26415,2",showitem_0_2="26415,2",showitem_0_3="26415,2",showitem_1_1="26415,2",showitem_1_2="26415,2",showitem_1_3="26415,2",showitem_0_4="26415,2",showitem_1_4="26415,2",progress_desc="成功洗出<per>0/2</per>条以上紫色属性",commit_desc="成功洗出2/2条以上紫色属性",open_panel_name="equipment#equipment_xilian",is_first="",taskgroup=153,target_obj={},},
[17639]={task_id=17639,task_name="神鲲",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=20,c_param2=1,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[33]},showitem_0_1="27907,1",showitem_0_2="27907,1",showitem_0_3="27907,1",showitem_1_1="27907,1",showitem_1_2="27907,1",showitem_1_3="27907,1",showitem_0_4="27907,1",showitem_1_4="27907,1",progress_desc="鲲升到<per>0/1</per>阶",commit_desc="鲲升到1/1阶",open_panel_name="NewAppearanceWGView#new_appearance_kun_upstar",is_first="",taskgroup=154,target_obj={},},
[17640]={task_id=17640,task_name="悬壶济世附体",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=21,c_param2=3,c_param3=3,c_param4=0,c_param5=0,coin_bind=100000,item_list={[0]=item_table[34]},showitem_0_1="32281,1",showitem_0_2="32281,1",showitem_0_3="32281,1",showitem_1_1="32281,1",showitem_1_2="32281,1",showitem_1_3="32281,1",showitem_0_4="32281,1",showitem_1_4="32281,1",progress_desc="穿戴<per>0/3</per>个紫色悬壶济世",commit_desc="穿戴3/3个紫色悬壶济世",open_panel_name="LongHunView",is_first="",taskgroup=155,target_obj={},},
[17641]={task_id=17641,task_name="斗转脱凡",c_param1=22,c_param4=0,c_param5=0,progress_desc="达到巅峰<per>0/1</per>级",commit_desc="达到巅峰1/1级",open_panel_name="TaskGainExpTipView",taskgroup=156,},
[17642]={task_id=17642,task_name="上古战场",c_param1=104,item_list={[0]=item_table[35]},showitem_0_1="32281,2",showitem_0_2="32281,2",showitem_0_3="32281,2",showitem_1_1="32281,2",showitem_1_2="32281,2",showitem_1_3="32281,2",showitem_0_4="32281,2",showitem_1_4="32281,2",progress_desc="进入上古战场<per>0/1</per>次",commit_desc="进入上古战场1/1次",open_panel_name="WorldServer#worserv_boss_mh",taskgroup=157,},
[17643]={task_id=17643,task_name="购买小炎狮",c_param1=45,c_param3=10100,progress_desc="购买<per>0/1</per>个小炎狮",commit_desc="购买1/1个小炎狮",open_panel_name="shop#20#op=10100",taskgroup=158,},
[17644]={task_id=17644,task_name="购买小雏鹰",pretaskid=17643,c_param3=10200,bind_gold=10,progress_desc="购买<per>0/1</per>个小雏鹰",commit_desc="购买1/1个小雏鹰",open_panel_name="shop#20#op=10200",},
[17645]={task_id=17645,task_name="升级守护",pretaskid=17644,c_param3=10300,c_param4=10400,progress_desc="升级<per>0/1</per>次守护",commit_desc="升级1/1次守护",open_panel_name="other_compose#other_compose_xiaogui",},
[17650]={task_id=17650,task_name="魔谷异兽",task_type=1,open_love=2000,min_level=2000,camp="",condition=7,c_param1=108,c_param2=1,c_param4=0,c_param5=0,coin_bind=50000,showitem_0_1="36417,50000",showitem_0_2="36417,50000",showitem_0_3="36417,50000",showitem_1_1="36417,50000",showitem_1_2="36417,50000",showitem_1_3="36417,50000",showitem_0_4="36417,50000",showitem_1_4="36417,50000",progress_desc="击败猎魔深渊内的小怪<per>0/1</per>次",commit_desc="击败猎魔深渊内的小怪1/1次",is_first="",taskgroup=159,target_obj={},},
[17651]={task_id=17651,task_name="神武降世",condition=8,c_param1=38,progress_desc="完成<per>0/1</per>次神武降世任务",commit_desc="完成1/1次神武降世任务",open_panel_name="TianShenJuexing",taskgroup=160,},
[17652]={task_id=17652,task_name="每日灵脉",c_param1=109,progress_desc="解封<per>0/1</per>次灵脉",commit_desc="解封1/1次灵脉",open_panel_name="guild#guild_baoxiang",taskgroup=161,},
[17653]={task_id=17653,task_name="星界狩猎",c_param1=112,item_list={[0]=item_table[36]},showitem_0_1="28893,1",showitem_0_2="28893,1",showitem_0_3="28893,1",showitem_1_1="28893,1",showitem_1_2="28893,1",showitem_1_3="28893,1",showitem_0_4="28893,1",showitem_1_4="28893,1",progress_desc="完成<per>0/1</per>次星界狩猎",commit_desc="完成1/1次星界狩猎",open_panel_name="country_map_act_view#country_map_act_farm",taskgroup=162,},
[17654]={task_id=17654,task_name="炼化妖兽·一",pretaskid=17653,c_param3=32287,progress_desc="炼化<per>0/1</per>次脱凡丹",commit_desc="炼化1/1次脱凡丹",},
[17655]={task_id=17655,task_name="神灵八卦",open_day=8,c_param1=38,c_param2=8,progress_desc="穿戴<per>0/8</per>个青龙八卦牌",commit_desc="穿戴8/8个青龙八卦牌",open_panel_name="TianShenView#tianshen_bagua",taskgroup=163,},
[17656]={task_id=17656,task_name="国家刺探",open_day=8,c_param1=40,progress_desc="进行<per>0/1</per>次国家刺探",commit_desc="进行1/1次国家刺探",open_panel_name="country_map_act_view#cross_spy",taskgroup=164,},
[17657]={task_id=17657,task_name="仙器百炼",open_day=4,c_param1=42,progress_desc="进行<per>0/1</per>次仙器抽奖",commit_desc="进行1/1次仙器抽奖",open_panel_name="SiXiangCallView#sixiang_call_xqzl",taskgroup=165,},
[17658]={task_id=17658,task_name="炼化妖兽·二",task_type=1,open_love=2000,min_level=2000,pretaskid=17654,camp="",condition=7,c_param1=39,c_param2=1,c_param3=29615,c_param4=0,c_param5=0,coin_bind=50000,item_list={[0]=item_table[36]},showitem_0_1="28893,1",showitem_0_2="28893,1",showitem_0_3="28893,1",showitem_1_1="28893,1",showitem_1_2="28893,1",showitem_1_3="28893,1",showitem_0_4="28893,1",showitem_1_4="28893,1",progress_desc="炼化<per>0/1</per>次种战纹令牌",commit_desc="炼化1/1次战纹令牌",open_panel_name="farm_compose_view",is_first="",taskgroup=162,target_obj={},},
[17659]={task_id=17659,task_name="添加好友",c_param1=116,c_param2=3,progress_desc="主动添加<per>0/3</per>个好友",commit_desc="主动添加3/3个好友",open_panel_name="society#society_friend",taskgroup=166,},
[17660]={task_id=17660,task_name="装备转化",max_level=2001,c_param1=117,progress_desc="转化<per>0/1</per>次装备",commit_desc="转化1/1次装备",open_panel_name="bag_view#rolebag_equip_transsex",taskgroup=167,},
[17661]={task_id=17661,task_name="异界领主",c_param1=118,progress_desc="进行<per>0/1</per>次异界领主",commit_desc="进行1/1次异界领主",open_panel_name="WorldServer#xianjie_boss",taskgroup=168,},
[17662]={task_id=17662,task_name="八荒星图",open_day=2,condition=4,c_param1=4599,progress_desc="进行<per>0/1</per>次八荒星图的战斗",commit_desc="进行1/1次八荒星图的战斗",open_panel_name="counrty_map_map_view",taskgroup=169,},
[17663]={task_id=17663,task_name="道年提升",task_type=1,open_love=2000,min_level=2000,condition=7,c_param1=121,c_param2=30,exp_factor=6,item_list={[0]=item_table[37]},progress_desc="修炼至(<per>0/30</per>)道年",commit_desc="修炼至30/30道年",open_panel_name="CultivationView",target_obj={},},
[17664]={task_id=17664,pretaskid=17663,c_param2=50,progress_desc="修炼至(<per>0/50</per>)道年",commit_desc="修炼至50/50道年",},
[17665]={task_id=17665,pretaskid=17664,c_param2=65,progress_desc="修炼至(<per>0/65</per>)道年",commit_desc="修炼至65/65道年",},
[17666]={task_id=17666,pretaskid=17665,c_param2=75,progress_desc="修炼至(<per>0/75</per>)道年",commit_desc="修炼至75/75道年",},
[17667]={task_id=17667,pretaskid=17666,c_param2=80,progress_desc="修炼至(<per>0/80</per>)道年",commit_desc="修炼至75/80道年",},
[17668]={task_id=17668,pretaskid=17667,c_param2=90,progress_desc="修炼至(<per>0/90</per>)道年",commit_desc="修炼至90/90道年",},
[17669]={task_id=17669,pretaskid=17668,c_param2=105,progress_desc="修炼至(<per>0/105</per>)道年",commit_desc="修炼至105/105道年",},
[17670]={task_id=17670,pretaskid=17669,c_param2=125,progress_desc="修炼至(<per>0/125</per>)道年",commit_desc="修炼至125/125道年",},
[17671]={task_id=17671,pretaskid=17670,c_param2=140,progress_desc="修炼至(<per>0/140</per>)道年",commit_desc="修炼至140/140道年",},
[17672]={task_id=17672,pretaskid=17671,c_param2=170,progress_desc="修炼至(<per>0/170</per>)道年",commit_desc="修炼至170/170道年",},
[17673]={task_id=17673,pretaskid=17672,c_param2=220,progress_desc="修炼至(<per>0/220</per>)道年",commit_desc="修炼至220/220道年",},
[17674]={task_id=17674,pretaskid=17673,c_param2=260,progress_desc="修炼至(<per>0/260</per>)道年",commit_desc="修炼至260/260道年",},
[17675]={task_id=17675,pretaskid=17674,c_param2=300,progress_desc="修炼至(<per>0/300</per>)道年",commit_desc="修炼至300/300道年",},
[17676]={task_id=17676,pretaskid=17675,c_param2=340,progress_desc="修炼至(<per>0/340</per>)道年",commit_desc="修炼至340/340道年",},
[17677]={task_id=17677,pretaskid=17676,c_param2=380,progress_desc="修炼至(<per>0/380</per>)道年",commit_desc="修炼至380/380道年",},
[17678]={task_id=17678,pretaskid=17677,c_param2=420,progress_desc="修炼至(<per>0/420</per>)道年",commit_desc="修炼至420/420道年",},
[17679]={task_id=17679,pretaskid=17678,c_param2=460,progress_desc="修炼至(<per>0/460</per>)道年",commit_desc="修炼至460/460道年",},
[17680]={task_id=17680,pretaskid=17679,c_param2=500,progress_desc="修炼至(<per>0/500</per>)道年",commit_desc="修炼至500/500道年",},
[17681]={task_id=17681,pretaskid=17680,c_param2=540,progress_desc="修炼至(<per>0/540</per>)道年",commit_desc="修炼至540/540道年",},
[17682]={task_id=17682,pretaskid=17681,c_param2=580,progress_desc="修炼至(<per>0/580</per>)道年",commit_desc="修炼至580/580道年",},
[17683]={task_id=17683,pretaskid=17682,c_param2=620,progress_desc="修炼至(<per>0/620</per>)道年",commit_desc="修炼至620/620道年",},
[17684]={task_id=17684,pretaskid=17683,c_param2=660,progress_desc="修炼至(<per>0/660</per>)道年",commit_desc="修炼至660/660道年",},
[17687]={task_id=17687,task_name="气衍万劫境界突破",task_type=1,open_love=155,min_level=155,condition=7,c_param1=120,c_param2=3,exp_factor=6,item_list={[0]=item_table[37]},progress_desc="气衍万劫境界突破至练气三重",commit_desc="气衍万劫境界突破至练气三重",open_panel_name="XiuWeiView",target_obj={},},
[17688]={task_id=17688,open_love=240,min_level=240,pretaskid=17687,c_param2=4,progress_desc="气衍万劫境界突破至筑基一重",commit_desc="气衍万劫境界突破至筑基一重",},
[17689]={task_id=17689,open_love=280,min_level=280,pretaskid=17688,c_param2=5,progress_desc="气衍万劫境界突破至筑基二重",commit_desc="气衍万劫境界突破至筑基二重",},
[17690]={task_id=17690,open_love=300,min_level=300,pretaskid=17689,c_param2=6,progress_desc="气衍万劫境界突破至筑基三重",commit_desc="气衍万劫境界突破至筑基三重",},
[17691]={task_id=17691,pretaskid=17690,c_param2=14,progress_desc="气衍万劫境界突破至融合中期",commit_desc="气衍万劫境界突破至出窍二重",},
[17692]={task_id=17692,pretaskid=17691,c_param2=15,progress_desc="气衍万劫境界突破至融合后期",commit_desc="气衍万劫境界突破至融合后期",},
[17693]={task_id=17693,pretaskid=17692,c_param2=16,progress_desc="气衍万劫境界突破至离合前期",commit_desc="气衍万劫境界突破至离合前期",},
[17694]={task_id=17694,pretaskid=17693,c_param2=17,progress_desc="气衍万劫境界突破至离合中期",commit_desc="气衍万劫境界突破至离合中期",},
[17695]={task_id=17695,pretaskid=17694,c_param2=18,progress_desc="气衍万劫境界突破至离合后期",commit_desc="气衍万劫境界突破至离合后期",},
[17696]={task_id=17696,pretaskid=17695,c_param2=19,progress_desc="气衍万劫境界突破至化神前期",commit_desc="气衍万劫境界突破至化神前期",},
[17697]={task_id=17697,pretaskid=17696,c_param2=20,progress_desc="气衍万劫境界突破至化神中期",commit_desc="气衍万劫境界突破至化神中期",},
[17698]={task_id=17698,pretaskid=17697,c_param2=21,progress_desc="气衍万劫境界突破至化神后期",commit_desc="气衍万劫境界突破至化神后期",},
[17699]={task_id=17699,pretaskid=17698,c_param2=22,progress_desc="气衍万劫境界突破至破妄前期",commit_desc="气衍万劫境界突破至破妄前期",},
[17700]={task_id=17700,pretaskid=17699,c_param2=23,progress_desc="气衍万劫境界突破至破妄中期",commit_desc="气衍万劫境界突破至破妄中期",},
[17701]={task_id=17701,pretaskid=17700,c_param2=24,progress_desc="气衍万劫境界突破至破妄后期",commit_desc="气衍万劫境界突破至破妄后期",},
[17702]={task_id=17702,pretaskid=17701,c_param2=25,progress_desc="气衍万劫境界突破至大乘前期",commit_desc="气衍万劫境界突破至大乘前期",},
[17703]={task_id=17703,pretaskid=17702,c_param2=26,progress_desc="气衍万劫境界突破至大乘中期",commit_desc="气衍万劫境界突破至大乘中期",},
[17704]={task_id=17704,pretaskid=17703,c_param2=27,progress_desc="气衍万劫境界突破至大乘后期",commit_desc="气衍万劫境界突破至大乘后期",},
[17705]={task_id=17705,pretaskid=17704,c_param2=28,progress_desc="气衍万劫境界突破至上仙前期",commit_desc="气衍万劫境界突破至上仙前期",},
[17706]={task_id=17706,pretaskid=17705,c_param2=29,progress_desc="气衍万劫境界突破至上仙中期",commit_desc="气衍万劫境界突破至上仙中期",},
[17707]={task_id=17707,pretaskid=17706,c_param2=30,progress_desc="气衍万劫境界突破至上仙后期",commit_desc="气衍万劫境界突破至上仙后期",},
[17708]={task_id=17708,pretaskid=17707,c_param2=31,progress_desc="气衍万劫境界突破至地仙前期",commit_desc="气衍万劫境界突破至地仙前期",},
[17709]={task_id=17709,pretaskid=17708,c_param2=32,progress_desc="气衍万劫境界突破至地仙中期",commit_desc="气衍万劫境界突破至地仙中期",},
[17710]={task_id=17710,pretaskid=17709,c_param2=33,progress_desc="气衍万劫境界突破至地仙后期",commit_desc="气衍万劫境界突破至地仙后期",},
[17711]={task_id=17711,pretaskid=17710,c_param2=34,progress_desc="气衍万劫境界突破至天仙前期",commit_desc="气衍万劫境界突破至天仙前期",},
[17712]={task_id=17712,pretaskid=17711,c_param2=35,progress_desc="气衍万劫境界突破至天仙中期",commit_desc="气衍万劫境界突破至天仙中期",},
[17713]={task_id=17713,pretaskid=17712,c_param2=36,progress_desc="气衍万劫境界突破至天仙后期",commit_desc="气衍万劫境界突破至天仙后期",},
[17714]={task_id=17714,pretaskid=17713,c_param2=37,progress_desc="气衍万劫境界突破至金仙前期",commit_desc="气衍万劫境界突破至金仙前期",},
[17715]={task_id=17715,pretaskid=17714,c_param2=38,progress_desc="气衍万劫境界突破至金仙中期",commit_desc="气衍万劫境界突破至金仙中期",},
[17716]={task_id=17716,pretaskid=17715,c_param2=39,progress_desc="气衍万劫境界突破至金仙后期",commit_desc="气衍万劫境界突破至金仙后期",},
[17717]={task_id=17717,pretaskid=17716,c_param2=40,progress_desc="气衍万劫境界突破至仙君前期",commit_desc="气衍万劫境界突破至仙君前期",},
[17718]={task_id=17718,pretaskid=17717,c_param2=41,progress_desc="气衍万劫境界突破至仙君中期",commit_desc="气衍万劫境界突破至仙君中期",},
[17719]={task_id=17719,open_love=2000,min_level=2000,pretaskid=17718,c_param2=42,progress_desc="气衍万劫境界突破至仙君后期",commit_desc="气衍万劫境界突破至仙君后期",},
[17720]={task_id=17720,pretaskid=17719,c_param2=43,progress_desc="气衍万劫境界突破至仙王前期",commit_desc="气衍万劫境界突破至仙王前期",},
[17721]={task_id=17721,pretaskid=17720,c_param2=44,progress_desc="气衍万劫境界突破至仙王中期",commit_desc="气衍万劫境界突破至仙王中期",},
[17722]={task_id=17722,pretaskid=17721,c_param2=45,progress_desc="气衍万劫境界突破至仙王后期",commit_desc="气衍万劫境界突破至仙王后期",},
[17723]={task_id=17723,pretaskid=17722,c_param2=46,progress_desc="气衍万劫境界突破至仙帝前期",commit_desc="气衍万劫境界突破至仙帝前期",},
[17724]={task_id=17724,pretaskid=17723,c_param2=47,progress_desc="气衍万劫境界突破至仙帝中期",commit_desc="气衍万劫境界突破至仙帝中期",},
[17725]={task_id=17725,pretaskid=17724,c_param2=48,progress_desc="气衍万劫境界突破至仙帝后期",commit_desc="气衍万劫境界突破至仙帝后期",},
[17730]={task_id=17730,task_name="玄天录",task_type=1,open_love=10,min_level=2000,pretaskid=0,condition=2,c_param1=1800,c_param2=5,exp_factor=6,item_list={[0]=item_table[38]},progress_desc="完成玄天录任务",commit_desc="法宝开启",open_panel_name="XiuXianShiLian",target_obj={{id=1800,scene=1003,x=92,y=55},},},
[17731]={task_id=17731,task_name="沧源录",progress_desc="沧源录开启",commit_desc="沧源录开启",},
[17732]={task_id=17732,},
[17733]={task_id=17733,},
[17734]={task_id=17734,},
[17735]={task_id=17735,},
[17736]={task_id=17736,},
[17737]={task_id=17737,},
[17738]={task_id=17738,},
[17739]={task_id=17739,},
[17800]={task_id=17800,task_name="幻兽之主",open_love=120,min_level=120,c_param1=126,c_param3=50,c_param4=0,c_param6="",item_list={[0]=item_table[38]},progress_desc="拥有3只50级的幻兽",commit_desc="拥有3只50级的幻兽",open_panel_name="ControlBeastsView",},
[17801]={task_id=17801,open_love=140,min_level=140,pretaskid=17800,c_param1=127,c_param3=4,progress_desc="拥有6只4星的幻兽",commit_desc="拥有6只4星的幻兽",},
[17802]={task_id=17802,open_love=155,min_level=155,pretaskid=17801,c_param1=125,c_param3=3,progress_desc="拥有10只SR品质的幻兽",commit_desc="拥有10只SR品质的幻兽",},
[17803]={task_id=17803,open_love=165,min_level=165,pretaskid=17802,c_param2=6,c_param3=100,progress_desc="拥有6只100级的幻兽",commit_desc="拥有6只100级的幻兽",},
[17804]={task_id=17804,open_love=175,min_level=175,pretaskid=17803,c_param2=5,progress_desc="拥有5只5星的幻兽",commit_desc="拥有5只5星的幻兽",},
[17805]={task_id=17805,open_love=185,min_level=185,pretaskid=17804,c_param3=4,progress_desc="拥有6只SSR的幻兽",commit_desc="拥有6只SSR的幻兽",},
[17806]={task_id=17806,open_love=195,min_level=195,pretaskid=17805,c_param2=8,progress_desc="拥有8只200级的幻兽",commit_desc="拥有8只200级的幻兽",},
[17807]={task_id=17807,open_love=205,min_level=205,pretaskid=17806,c_param1=127,c_param2=8,progress_desc="拥有8只5星的幻兽",commit_desc="拥有8只5星的幻兽",},
[17808]={task_id=17808,open_love=210,min_level=210,pretaskid=17807,c_param2=3,progress_desc="拥有3只UR品质的幻兽",commit_desc="拥有3只UR品质的幻兽",},
[17809]={task_id=17809,open_love=215,min_level=215,pretaskid=17808,c_param2=10,c_param3=200,progress_desc="拥有10只200级的幻兽",commit_desc="拥有10只200级的幻兽",},
[17810]={task_id=17810,open_love=225,min_level=225,pretaskid=17809,c_param1=125,c_param2=6,c_param3=5,progress_desc="拥有6只UR品质的幻兽",commit_desc="拥有6只UR品质的幻兽",},
[17900]={task_id=17900,task_name="既穿且搭",task_type=1,open_love=200,min_level=200,condition=7,c_param1=136,c_param2=1,c_param3=1,c_param4=15,bind_gold=10,item_list={[0]=item_table[39]},progress_desc="激活天地神躯衣",commit_desc="合成并激活天地神躯衣",open_panel_name="WardrobeView#wardrobe_fashion#op=1#item_id=37715",show_chaper_id=0,is_first="",taskgroup=141,target_obj={},},
[17901]={task_id=17901,task_name="外观染色",task_type=1,open_love=200,min_level=200,pretaskid=17900,condition=7,c_param1=137,c_param2=1,c_param4=0,bind_gold=10,progress_desc="保存一套染色方案",commit_desc="保存一套染色方案",open_panel_name="NewAppearanceDyeView#0#op=0#item_id=37715",show_chaper_id=0,is_first="",taskgroup=141,target_obj={},},
[29004]={task_id=29004,task_name="链接",task_type=8,ver=2,min_level=145,condition=7,c_param1=128,c_param2=1,c_param4=0,bind_gold=10,accept_desc="完成职业第一次转职",progress_desc="",commit_desc="已完成当前转职任务",fb_tip_desc="击败六臂邪魔，才可以使自身的修为突飞猛进！",fb_tip_title="诛杀心魔",target_obj={},},
[29005]={task_id=29005,task_name="雷劫铸躯",task_type=8,ver=2,min_level=145,pretaskid=29004,condition=7,c_param1=130,c_param2=1,bind_gold=10,accept_desc="完成职业第一次转职",progress_desc="渡过凝息境雷劫",commit_desc="已完成当前转职任务",open_panel_name="DujieView",fb_tip_desc="击败六臂邪魔，才可以使自身的修为突飞猛进！",fb_tip_title="诛杀心魔",target_obj={},},
[29014]={task_id=29014,min_level=200,prof_level=1,},
[29015]={task_id=29015,open_day=2,min_level=200,pretaskid=29014,prof_level=1,c_param2=2,accept_desc="完成职业第二次转职",progress_desc="渡过问宫境雷劫",},
[29023]={task_id=29023,min_level=240,pretaskid=29015,prof_level=2,c_param3=14044,c_param6=14058,bind_gold=10,item_list={[0]=item_table[40]},accept_desc="完成职业第三次转职",},
[29024]={task_id=29024,min_level=250,pretaskid=29015,prof_level=2,accept_desc="完成职业第三次转职",},
[29025]={task_id=29025,min_level=250,pretaskid=29024,prof_level=2,c_param2=3,commit_dialog=30006,accept_desc="完成职业第三次转职",progress_desc="渡过缚龙境雷劫",},
[29033]={task_id=29033,task_name="星罗织脉",min_level=290,pretaskid=29025,prof_level=3,c_param3=14045,c_param6=14059,item_list={[0]=item_table[41]},accept_desc="完成职业第四次转职",},
[29034]={task_id=29034,min_level=150,pretaskid=29025,},
[29035]={task_id=29035,open_day=3,min_level=300,pretaskid=29034,prof_level=3,c_param2=4,accept_desc="完成职业第四次转职",progress_desc="渡过燃灯境雷劫",},
[29043]={task_id=29043,min_level=340,pretaskid=29035,prof_level=4,c_param3=14046,c_param6=14060,item_list={[0]=item_table[42]},accept_desc="完成职业第五次转职",},
[29044]={task_id=29044,pretaskid=29035,accept_desc="完成职业第五次转职",},
[29045]={task_id=29045,open_day=4,min_level=350,pretaskid=29044,prof_level=4,c_param2=5,accept_desc="完成职业第五次转职",progress_desc="渡过渡厄境雷劫",},
[29053]={task_id=29053,task_name="九窍通神",min_level=400,prof_level=5,c_param1=93,c_param2=10,c_param3=14047,c_param5=0,c_param6=14061,bind_gold=50,item_list={[0]=item_table[43]},progress_desc="灵脉感知天地，采集仙物锻体",},
[29054]={task_id=29054,pretaskid=29045,accept_desc="完成职业第六次转职",},
[29055]={task_id=29055,open_day=6,min_level=420,pretaskid=29054,prof_level=5,c_param2=6,accept_desc="完成职业第六次转职",progress_desc="渡过蜕灵境雷劫",},
[29063]={task_id=29063,min_level=500,pretaskid=29055,prof_level=6,c_param3=14048,c_param6=14062,item_list={[0]=item_table[44]},accept_desc="完成职业第七次转职",},
[29064]={task_id=29064,min_level=350,pretaskid=29055,prof_level=4,accept_desc="完成职业第七次转职",},
[29065]={task_id=29065,min_level=520,pretaskid=29064,prof_level=6,c_param2=7,accept_desc="完成职业第七次转职",progress_desc="渡过种道境雷劫",},
[29073]={task_id=29073,min_level=600,pretaskid=29065,prof_level=7,c_param3=14049,c_param6=14063,item_list={[0]=item_table[45]},accept_desc="完成职业第八次转职",},
[29074]={task_id=29074,pretaskid=29065,accept_desc="完成职业第八次转职",},
[29075]={task_id=29075,min_level=620,pretaskid=29074,prof_level=7,c_param2=8,accept_desc="完成职业第八次转职",progress_desc="渡过辟界境雷劫",},
[29083]={task_id=29083,min_level=700,pretaskid=29075,prof_level=8,c_param3=14050,c_param6=14064,item_list={[0]=item_table[46]},accept_desc="完成职业第九次转职",},
[29084]={task_id=29084,pretaskid=29075,accept_desc="完成职业第九次转职",},
[29085]={task_id=29085,min_level=720,pretaskid=29084,prof_level=8,c_param2=9,bind_gold=50,accept_desc="完成职业第九次转职",progress_desc="渡过窃天境雷劫",},
[29093]={task_id=29093,min_level=800,pretaskid=29085,prof_level=9,c_param3=14051,c_param6=14065,item_list={[0]=item_table[47]},accept_desc="完成职业第十次转职",},
[29094]={task_id=29094,pretaskid=29085,accept_desc="完成职业第十次转职",},
[29095]={task_id=29095,pretaskid=29094,prof_level=9,c_param2=10,accept_desc="完成职业第十次转职",progress_desc="渡过法则境雷劫",},
[29103]={task_id=29103,min_level=900,pretaskid=29095,prof_level=10,c_param3=14052,c_param6=14066,item_list={[0]=item_table[48]},accept_desc="完成职业第十一次转职",},
[29104]={task_id=29104,pretaskid=29095,accept_desc="完成职业第十一次转职",},
[29105]={task_id=29105,min_level=820,pretaskid=29104,prof_level=10,c_param2=11,accept_desc="完成职业第十一次转职",progress_desc="渡过劫火境雷劫",},
[29113]={task_id=29113,min_level=1000,pretaskid=29105,prof_level=11,c_param3=14053,c_param6=14067,bind_gold=100,item_list={[0]=item_table[49]},accept_desc="完成职业第十二次转职",},
[29114]={task_id=29114,pretaskid=29105,accept_desc="完成职业第十二次转职",},
[29115]={task_id=29115,min_level=1020,pretaskid=29114,prof_level=11,c_param2=12,accept_desc="完成职业第十二次转职",progress_desc="渡过归墟境雷劫",},
[29123]={task_id=29123,min_level=1100,pretaskid=29115,prof_level=12,c_param3=14054,c_param6=14068,item_list={[0]=item_table[50]},accept_desc="完成职业第十三次转职",},
[29124]={task_id=29124,pretaskid=29115,accept_desc="完成职业第十三次转职",},
[29125]={task_id=29125,min_level=1120,pretaskid=29124,prof_level=12,c_param2=13,accept_desc="完成职业第十三次转职",progress_desc="渡过掌劫境雷劫",},
[29133]={task_id=29133,min_level=1200,pretaskid=29125,prof_level=13,c_param3=14055,c_param6=14069,item_list={[0]=item_table[51]},accept_desc="完成职业第十四次转职",},
[29134]={task_id=29134,pretaskid=29125,accept_desc="完成职业第十四次转职",},
[29135]={task_id=29135,min_level=1220,pretaskid=29134,prof_level=13,c_param2=14,bind_gold=100,commit_dialog=30006,accept_desc="完成职业第十四次转职",progress_desc="渡过天衍境雷劫",},
[29143]={task_id=29143,min_level=1300,pretaskid=29135,prof_level=14,c_param3=14056,c_param6=14070,item_list={[0]=item_table[52]},accept_desc="完成职业第十五次转职",},
[29144]={task_id=29144,pretaskid=29135,accept_desc="完成职业第十五次转职",},
[29145]={task_id=29145,min_level=1320,pretaskid=29144,prof_level=14,c_param2=15,accept_desc="完成职业第十五次转职",progress_desc="渡过虚无境雷劫",},
[29153]={task_id=29153,min_level=1400,pretaskid=29145,prof_level=15,c_param3=14057,c_param6=14071,item_list={[0]=item_table[53]},accept_desc="完成职业第十六次转职",},
[29154]={task_id=29154,min_level=150,pretaskid=29145,accept_desc="完成职业第十六次转职",},
[29155]={task_id=29155,min_level=1420,pretaskid=29154,prof_level=15,c_param2=16,accept_desc="完成职业第十六次转职",progress_desc="渡过无上境雷劫",},
[30001]={task_id=30001,task_name="五术之山",task_type=8,min_level=100,prof_level=20,condition=7,c_param1=13,c_param2=1,accept_desc="完成转职",progress_desc="完成当前转职任务(<per>0/6</per>)",commit_desc="完成当前转职",is_first="",target_obj={},},
[31001]={task_id=31001,min_level=120,max_level=149,item_list={[0]=item_table[54]},},
[31002]={task_id=31002,min_level=120,max_level=149,item_list={[0]=item_table[54]},},
[31003]={task_id=31003,min_level=120,max_level=149,},
[31004]={task_id=31004,progress_desc="击杀魔族精锐(<per>1/20</per>)",},
[31005]={task_id=31005,},
[31006]={task_id=31006,min_level=120,max_level=149,c_param1=10402,item_list={[0]=item_table[55]},accept_desc="<color=#72eba9>击杀魔族精锐</color>",progress_desc="击杀魔族精锐<per>1/20</per>)",commit_desc="<color=#72eba9>击杀魔族精锐</color>",target_obj={{id=10402,scene=1004,x=58,y=139},{id=10402,scene=1004,x=55,y=134},{id=10402,scene=1004,x=51,y=143},{id=10402,scene=1004,x=61,y=135},{id=10402,scene=1004,x=55,y=144},{id=10402,scene=1004,x=52,y=140},{id=10402,scene=1004,x=61,y=141},{id=10402,scene=1004,x=64,y=137},},},
[31007]={task_id=31007,c_param1=10402,c_param4=29605,accept_desc="<color=#72eba9>收集恶魔之魂</color>",progress_desc="收集恶魔之魂(<per>1/10</per>)",commit_desc="<color=#72eba9>收集恶魔之魂</color>",target_obj={{id=10402,scene=1004,x=58,y=139},{id=10402,scene=1004,x=55,y=134},{id=10402,scene=1004,x=51,y=143},{id=10402,scene=1004,x=61,y=135},{id=10402,scene=1004,x=55,y=144},{id=10402,scene=1004,x=52,y=140},{id=10402,scene=1004,x=61,y=141},{id=10402,scene=1004,x=64,y=137},},},
[31008]={task_id=31008,task_name="魔怪遗物",min_level=120,max_level=149,condition=23,c_param1=10405,c_param2=10,c_param3=3333,c_param4=29606,condition_find_obj_type=1,gongxian=1111,shengwang=180,exp_copies=222.2224,accept_desc="<color=#72eba9>收集火灵花</color>",progress_desc="收集火灵花(<per>1/10</per>)",commit_desc="<color=#72eba9>收集火灵花</color>",},
[31009]={task_id=31009,min_level=120,max_level=149,c_param1=10407,},
[31010]={task_id=31010,min_level=120,max_level=149,},
[31051]={task_id=31051,min_level=150,max_level=179,},
[31052]={task_id=31052,min_level=150,max_level=179,},
[31053]={task_id=31053,min_level=150,max_level=179,item_list={[0]=item_table[54]},},
[31054]={task_id=31054,min_level=150,max_level=179,c_param1=10408,item_list={[0]=item_table[55]},accept_desc="<color=#72eba9>击杀森林巨猿</color>",progress_desc="击杀森林巨猿(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀森林巨猿</color>",},
[31055]={task_id=31055,c_param1=10410,accept_desc="<color=#72eba9>击杀怪化猫妖</color>",progress_desc="击杀怪化猫妖(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀怪化猫妖</color>",},
[31056]={task_id=31056,accept_desc="<color=#72eba9>击杀幻影蝶妖</color>",progress_desc="击杀幻影蝶妖<per>1/20</per>)",commit_desc="<color=#72eba9>击杀幻影蝶妖</color>",},
[31057]={task_id=31057,min_level=150,max_level=179,c_param1=10408,target_obj={},},
[31058]={task_id=31058,min_level=150,max_level=179,c_param1=10408,},
[31059]={task_id=31059,min_level=150,max_level=179,c_param1=10410,},
[31060]={task_id=31060,min_level=150,max_level=179,},
[31101]={task_id=31101,min_level=180,max_level=209,},
[31102]={task_id=31102,min_level=180,max_level=209,},
[31103]={task_id=31103,min_level=180,max_level=209,},
[31104]={task_id=31104,min_level=180,max_level=209,c_param1=10501,accept_desc="<color=#72eba9>击杀上古机关</color>",progress_desc="击杀上古机关(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀上古机关</color>",target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[31105]={task_id=31105,c_param1=10502,accept_desc="<color=#72eba9>击杀拦路恶霸</color>",progress_desc="击杀拦路恶霸(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀拦路恶霸</color>",target_obj={{id=10502,scene=1005,x=196,y=100},{id=10502,scene=1005,x=209,y=99},{id=10502,scene=1005,x=203,y=102},{id=10502,scene=1005,x=192,y=96},{id=10502,scene=1005,x=202,y=98},{id=10502,scene=1005,x=198,y=95},{id=10502,scene=1005,x=199,y=100},{id=10502,scene=1005,x=196,y=97},},},
[31106]={task_id=31106,c_param1=10503,accept_desc="<color=#72eba9>击杀山中劫匪</color>",progress_desc="击杀山中劫匪猴<per>1/20</per>)",commit_desc="<color=#72eba9>击杀山中劫匪</color>",target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[31107]={task_id=31107,min_level=180,max_level=209,c_param1=10501,target_obj={{id=10501,scene=1005,x=204,y=152},{id=10501,scene=1005,x=206,y=149},{id=10501,scene=1005,x=201,y=143},{id=10501,scene=1005,x=208,y=152},{id=10501,scene=1005,x=207,y=146},{id=10501,scene=1005,x=201,y=147},},},
[31108]={task_id=31108,min_level=180,max_level=209,c_param1=10502,target_obj={{id=10502,scene=1005,x=196,y=100},{id=10502,scene=1005,x=209,y=99},{id=10502,scene=1005,x=203,y=102},{id=10502,scene=1005,x=192,y=96},{id=10502,scene=1005,x=202,y=98},{id=10502,scene=1005,x=198,y=95},{id=10502,scene=1005,x=199,y=100},{id=10502,scene=1005,x=196,y=97},},},
[31109]={task_id=31109,min_level=180,max_level=209,c_param1=10503,target_obj={{id=10503,scene=1005,x=97,y=77},{id=10503,scene=1005,x=94,y=80},{id=10503,scene=1005,x=93,y=82},{id=10503,scene=1005,x=101,y=74},{id=10503,scene=1005,x=103,y=78},{id=10503,scene=1005,x=99,y=80},},},
[31110]={task_id=31110,min_level=180,max_level=209,},
[31151]={task_id=31151,min_level=210,max_level=239,},
[31152]={task_id=31152,min_level=210,max_level=239,},
[31153]={task_id=31153,min_level=210,max_level=239,},
[31154]={task_id=31154,min_level=210,max_level=239,c_param1=10504,accept_desc="<color=#72eba9>击杀荒漠蜘蛛</color>",progress_desc="击杀荒漠蜘蛛(<per>1/20</per>)",commit_desc="<color=#72eba9>荒漠蜘蛛</color>",target_obj={{id=10504,scene=1005,x=22,y=111},{id=10504,scene=1005,x=21,y=115},{id=10504,scene=1005,x=28,y=113},{id=10504,scene=1005,x=26,y=115},{id=10504,scene=1005,x=21,y=108},{id=10504,scene=1005,x=28,y=119},{id=10504,scene=1005,x=33,y=111},{id=10504,scene=1005,x=21,y=119},},},
[31155]={task_id=31155,c_param1=10505,accept_desc="<color=#72eba9>击杀远古战甲</color>",progress_desc="击杀远古战甲(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀远古战甲</color>",target_obj={{id=10505,scene=1005,x=61,y=242},{id=10505,scene=1005,x=62,y=247},{id=10505,scene=1005,x=67,y=249},{id=10505,scene=1005,x=66,y=241},{id=10505,scene=1005,x=68,y=251},{id=10505,scene=1005,x=68,y=244},{id=10505,scene=1005,x=72,y=247},},},
[31156]={task_id=31156,c_param1=10506,accept_desc="<color=#72eba9>击杀荒野豪猪</color>",progress_desc="击杀荒野豪猪<per>1/20</per>)",commit_desc="<color=#72eba9>击杀荒野豪猪</color>",target_obj={},},
[31157]={task_id=31157,min_level=210,max_level=239,c_param1=10504,target_obj={{id=10504,scene=1005,x=22,y=111},{id=10504,scene=1005,x=21,y=115},{id=10504,scene=1005,x=28,y=113},{id=10504,scene=1005,x=26,y=115},{id=10504,scene=1005,x=21,y=108},{id=10504,scene=1005,x=28,y=119},{id=10504,scene=1005,x=33,y=111},{id=10504,scene=1005,x=21,y=119},},},
[31158]={task_id=31158,min_level=210,max_level=239,c_param1=10505,target_obj={{id=10505,scene=1005,x=61,y=242},{id=10505,scene=1005,x=62,y=247},{id=10505,scene=1005,x=67,y=249},{id=10505,scene=1005,x=66,y=241},{id=10505,scene=1005,x=68,y=251},{id=10505,scene=1005,x=68,y=244},{id=10505,scene=1005,x=72,y=247},},},
[31159]={task_id=31159,min_level=210,max_level=239,c_param1=10506,item_list={[0]=item_table[56]},exp_copies=222.2224,},
[31160]={task_id=31160,min_level=210,max_level=239,},
[31201]={task_id=31201,min_level=240,max_level=269,},
[31202]={task_id=31202,min_level=240,max_level=269,},
[31203]={task_id=31203,min_level=240,max_level=269,},
[31204]={task_id=31204,min_level=240,max_level=269,c_param1=10601,accept_desc="<color=#72eba9>击杀冰川魅魔</color>",progress_desc="击杀冰川魅魔(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀冰川魅魔</color>",},
[31205]={task_id=31205,c_param1=10602,accept_desc="<color=#72eba9>击杀昆仑女弟子</color>",progress_desc="击杀昆仑女弟子(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀昆仑女弟子</color>",},
[31206]={task_id=31206,c_param1=10603,accept_desc="<color=#72eba9>击杀寒霜仆从</color>",progress_desc="击杀寒霜仆从<per>1/20</per>)",commit_desc="<color=#72eba9>击杀寒霜仆从</color>",},
[31207]={task_id=31207,min_level=240,max_level=269,c_param1=10601,},
[31208]={task_id=31208,min_level=240,max_level=269,c_param1=10602,},
[31209]={task_id=31209,min_level=240,max_level=269,c_param1=10603,},
[31210]={task_id=31210,min_level=240,max_level=269,item_list={[0]=item_table[56]},exp_copies=277.778,},
[31251]={task_id=31251,min_level=270,max_level=304,},
[31252]={task_id=31252,min_level=270,max_level=304,},
[31253]={task_id=31253,min_level=270,max_level=304,},
[31254]={task_id=31254,c_param1=10607,accept_desc="<color=#72eba9>击杀上古冰灵</color>",progress_desc="击杀上古冰灵(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀上古冰灵</color>",},
[31255]={task_id=31255,min_level=270,max_level=304,c_param1=10608,accept_desc="<color=#72eba9>击杀寒天恶狼</color>",progress_desc="击杀寒天恶狼(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀寒天恶狼</color>",},
[31256]={task_id=31256,c_param1=10609,accept_desc="<color=#72eba9>击杀狂暴凶兽</color>",progress_desc="击杀狂暴凶兽<per>1/20</per>)",commit_desc="<color=#72eba9>击杀狂暴凶兽</color>",},
[31257]={task_id=31257,min_level=270,max_level=304,c_param1=10607,item_list={[0]=item_table[57]},},
[31258]={task_id=31258,min_level=270,max_level=304,c_param1=10608,item_list={[0]=item_table[57]},},
[31259]={task_id=31259,min_level=270,max_level=304,c_param1=10609,item_list={[0]=item_table[57]},},
[31260]={task_id=31260,min_level=270,max_level=304,item_list={[0]=item_table[58],[1]=item_table[59]},},
[31301]={task_id=31301,min_level=305,max_level=329,item_list={[0]=item_table[55]},},
[31302]={task_id=31302,min_level=305,max_level=329,item_list={[0]=item_table[55]},exp_copies=111.1112,},
[31303]={task_id=31303,min_level=305,max_level=329,item_list={[0]=item_table[55]},},
[31304]={task_id=31304,c_param1=10701,accept_desc="<color=#72eba9>击杀暴戾熊精</color>",progress_desc="击杀暴戾熊精(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀暴戾熊精</color>",},
[31305]={task_id=31305,min_level=305,max_level=329,c_param1=10702,item_list={[0]=item_table[60]},exp_copies=138.889,accept_desc="<color=#72eba9>击杀不周石灵</color>",progress_desc="击杀不周石灵(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀不周石灵</color>",},
[31306]={task_id=31306,c_param1=10703,accept_desc="<color=#72eba9>击杀山中拳师</color>",progress_desc="击杀山中拳师<per>1/20</per>)",commit_desc="<color=#72eba9>击杀山中拳师</color>",},
[31307]={task_id=31307,min_level=305,max_level=329,c_param1=10701,},
[31308]={task_id=31308,min_level=305,max_level=329,c_param1=10702,},
[31309]={task_id=31309,min_level=305,max_level=329,c_param1=10703,},
[31310]={task_id=31310,min_level=305,max_level=329,},
[31351]={task_id=31351,min_level=330,max_level=364,exp_copies=111.1112,},
[31352]={task_id=31352,min_level=330,max_level=364,exp_copies=111.1112,},
[31353]={task_id=31353,task_name="调解矛盾",accept_npc={id=10319,scene=1003,x=227,y=396},commit_npc={id=10317,scene=1003,x=307,y=141},accept_dialog=40004,commit_dialog=40005,accept_desc="<color=#72eba9>调解矛盾</color>",progress_desc="调解矛盾",commit_desc="<color=#72eba9>调解矛盾</color>",},
[31354]={task_id=31354,min_level=330,max_level=364,c_param1=10704,exp_copies=138.889,accept_desc="<color=#72eba9>击杀魔化狼人</color>",progress_desc="击杀魔化狼人(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀魔化狼人</color>",},
[31355]={task_id=31355,c_param1=10705,accept_desc="<color=#72eba9>击杀边境飞贼</color>",progress_desc="击杀边境飞贼(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀边境飞贼</color>",},
[31356]={task_id=31356,c_param1=10706,accept_desc="<color=#72eba9>击杀玄幻异兽</color>",progress_desc="击杀玄幻异兽<per>1/20</per>)",commit_desc="<color=#72eba9>击杀重玄幻异兽</color>",},
[31357]={task_id=31357,min_level=330,max_level=364,c_param1=10704,exp_copies=222.2224,},
[31358]={task_id=31358,min_level=330,max_level=364,c_param1=10705,item_list={[0]=item_table[61]},},
[31359]={task_id=31359,min_level=330,max_level=364,c_param1=10706,exp_copies=222.2224,},
[31360]={task_id=31360,min_level=330,max_level=364,exp_copies=277.778,},
[31401]={task_id=31401,min_level=365,max_level=389,item_list={[0]=item_table[62]},exp_copies=133.334,},
[31402]={task_id=31402,task_name="收集情报",accept_npc={},commit_npc={id=10304,scene=1003,x=159,y=264},accept_dialog=40002,commit_dialog=40003,accept_desc="<color=#72eba9>跟马进了解情报</color>",progress_desc="跟马进了解情报",commit_desc="<color=#72eba9>跟马进了解情报</color>",},
[31403]={task_id=31403,min_level=365,max_level=389,exp_copies=133.334,},
[31404]={task_id=31404,c_param1=10707,accept_desc="<color=#72eba9>击杀婆娑花妖</color>",progress_desc="击杀婆娑花妖(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀婆娑花妖</color>",},
[31405]={task_id=31405,task_name="邪灵突破",task_type=9,min_level=365,max_level=389,condition=1,c_param1=10708,c_param2=30,coin_bind=100000,item_list={[0]=item_table[63]},gongxian=694,shengwang=113,exp_copies=133.334,accept_desc="<color=#72eba9>击杀魔音琴妖</color>",progress_desc="击杀魔音琴妖(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀魔音琴妖</color>",is_first="",target_obj={},},
[31406]={task_id=31406,c_param1=10709,accept_desc="<color=#72eba9>击杀秘境强盗</color>",progress_desc="击杀秘境强盗<per>1/20</per>)",commit_desc="<color=#72eba9>击杀秘境强盗</color>",},
[31407]={task_id=31407,min_level=365,max_level=389,c_param1=10707,item_list={[0]=item_table[61]},exp_copies=166.667,},
[31408]={task_id=31408,min_level=365,max_level=389,c_param1=10708,item_list={[0]=item_table[61]},exp_copies=166.667,},
[31409]={task_id=31409,min_level=365,max_level=389,c_param1=10709,item_list={[0]=item_table[61]},exp_copies=166.667,},
[31410]={task_id=31410,min_level=365,max_level=389,item_list={[0]=item_table[58],[1]=item_table[64]},exp_copies=166.667,},
[31451]={task_id=31451,task_name="捎口信",task_type=9,min_level=390,max_level=424,commit_npc={id=10303,scene=1003,x=73,y=284},coin_bind=100000,item_list={[0]=item_table[60]},gongxian=556,shengwang=90,exp_copies=266.667,commit_dialog=40001,accept_desc="<color=#72eba9>帮莫风情带口信</color>",progress_desc="帮莫风情带去口信",commit_desc="<color=#72eba9>帮莫风情带去口信</color>",is_first="",},
[31452]={task_id=31452,min_level=390,max_level=424,item_list={[0]=item_table[60]},exp_copies=266.667,},
[31453]={task_id=31453,min_level=390,max_level=424,item_list={[0]=item_table[60]},exp_copies=266.667,},
[31454]={task_id=31454,min_level=390,max_level=424,c_param1=10801,item_list={[0]=item_table[56]},exp_copies=333.334,accept_desc="<color=#72eba9>击杀噬血妖兽</color>",progress_desc="击杀噬血妖兽(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀噬血妖兽</color>",},
[31455]={task_id=31455,c_param1=10802,exp_copies=133.334,accept_desc="<color=#72eba9>击杀邪魅妖女</color>",progress_desc="击杀邪魅妖女(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀邪魅妖女</color>",},
[31456]={task_id=31456,c_param1=10803,item_list={[0]=item_table[65]},accept_desc="<color=#72eba9>击杀残暴判官</color>",progress_desc="击杀残暴判官<per>1/20</per>)",commit_desc="<color=#72eba9>击杀残暴判官</color>",},
[31457]={task_id=31457,c_param1=10801,c_param4=29605,accept_desc="<color=#72eba9>收集恶魔之魂</color>",progress_desc="收集恶魔之魂(<per>1/10</per>)",commit_desc="<color=#72eba9>收集恶魔之魂</color>",},
[31458]={task_id=31458,min_level=390,max_level=424,c_param1=10802,item_list={[0]=item_table[65]},exp_copies=133.334,},
[31459]={task_id=31459,min_level=390,max_level=424,c_param1=10803,item_list={[0]=item_table[65]},},
[31460]={task_id=31460,min_level=390,max_level=424,item_list={[0]=item_table[58],[1]=item_table[66]},},
[31501]={task_id=31501,min_level=425,max_level=449,item_list={[0]=item_table[63]},exp_copies=166.667,},
[31502]={task_id=31502,min_level=425,max_level=449,item_list={[0]=item_table[63]},},
[31503]={task_id=31503,min_level=425,max_level=449,item_list={[0]=item_table[63]},},
[31504]={task_id=31504,min_level=425,max_level=449,c_param1=10804,item_list={[0]=item_table[67]},accept_desc="<color=#72eba9>击杀地狱恶灵</color>",progress_desc="击杀地狱恶灵(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀地狱恶灵</color>",},
[31505]={task_id=31505,c_param1=10805,exp_copies=266.667,accept_desc="<color=#72eba9>击杀混沌僵尸</color>",progress_desc="击杀混沌僵尸(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀混沌僵尸</color>",},
[31506]={task_id=31506,c_param1=10806,accept_desc="<color=#72eba9>击杀赤铁之怒</color>",progress_desc="击杀赤铁之怒<per>1/20</per>)",commit_desc="<color=#72eba9>击杀赤铁之怒</color>",},
[31507]={task_id=31507,min_level=425,max_level=449,c_param1=10804,},
[31508]={task_id=31508,min_level=425,max_level=449,c_param1=10805,},
[31509]={task_id=31509,min_level=425,max_level=449,c_param1=10806,},
[31510]={task_id=31510,min_level=425,max_level=449,},
[31551]={task_id=31551,min_level=450,max_level=469,},
[31552]={task_id=31552,min_level=450,max_level=469,},
[31553]={task_id=31553,min_level=450,max_level=469,},
[31554]={task_id=31554,min_level=450,max_level=469,exp_copies=166.667,progress_desc="击杀赤铁之怒(<per>1/20</per>)",},
[31555]={task_id=31555,c_param1=10807,accept_desc="<color=#72eba9>击杀熔岩之灵</color>",progress_desc="击杀熔岩之灵(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀熔岩之灵</color>",},
[31556]={task_id=31556,min_level=450,max_level=469,c_param1=10808,accept_desc="<color=#72eba9>击杀夺命骷髅</color>",progress_desc="击杀夺命骷髅<per>1/20</per>)",commit_desc="<color=#72eba9>击杀夺命骷髅</color>",},
[31557]={task_id=31557,min_level=450,max_level=469,c_param1=10806,item_list={[0]=item_table[64]},},
[31558]={task_id=31558,min_level=450,max_level=469,c_param1=10807,item_list={[0]=item_table[64]},exp_copies=333.334,},
[31559]={task_id=31559,min_level=450,max_level=469,c_param1=10808,c_param4=29607,item_list={[0]=item_table[64]},exp_copies=133.334,accept_desc="<color=#72eba9>收集紫莲地心火</color>",progress_desc="收集紫莲地心火(<per>1/10</per>)",commit_desc="<color=#72eba9>收集紫莲地心火</color>",},
[31560]={task_id=31560,task_name="魔将讨伐",accept_npc={id=312,scene=1003,x=292,y=453},condition=3,c_param1=42,c_param2=1,c_param3=21,item_list={[0]=item_table[58],[1]=item_table[68]},gongxian=1389,shengwang=225,exp_copies=133.334,accept_dialog=40006,accept_desc="<color=#72eba9>阻止魔界入侵</color>",progress_desc="阻止魔界入侵(<per>1/1</per>)",commit_desc="<color=#72eba9>阻止魔界入侵</color>",},
[31601]={task_id=31601,min_level=470,max_level=499,item_list={[0]=item_table[56]},},
[31602]={task_id=31602,min_level=470,max_level=499,},
[31603]={task_id=31603,min_level=470,max_level=499,},
[31604]={task_id=31604,c_param1=10809,exp_copies=166.667,accept_desc="<color=#72eba9>击杀魔界守卫</color>",progress_desc="击杀魔界守卫(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀魔界守卫</color>",},
[31605]={task_id=31605,c_param1=10810,accept_desc="<color=#72eba9>击杀阴间孤灯</color>",progress_desc="击杀阴间孤灯(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀阴间孤灯</color>",},
[31606]={task_id=31606,min_level=470,max_level=499,c_param1=10811,item_list={[0]=item_table[69]},exp_copies=266.667,accept_desc="<color=#72eba9>击杀烈火凶兽</color>",progress_desc="击杀烈火凶兽<per>1/20</per>)",commit_desc="<color=#72eba9>击杀烈火凶兽</color>",},
[31607]={task_id=31607,min_level=470,max_level=499,c_param1=10809,c_param4=29605,item_list={[0]=item_table[66]},exp_copies=266.667,accept_desc="<color=#72eba9>收集恶魔之魂</color>",progress_desc="收集恶魔之魂(<per>1/10</per>)",commit_desc="<color=#72eba9>收集恶魔之魂</color>",},
[31608]={task_id=31608,min_level=470,max_level=499,c_param1=10810,},
[31609]={task_id=31609,min_level=470,max_level=499,c_param1=10811,},
[31610]={task_id=31610,min_level=470,max_level=499,},
[31651]={task_id=31651,min_level=500,max_level=529,},
[31652]={task_id=31652,min_level=500,max_level=529,item_list={[0]=item_table[56]},exp_copies=166.667,},
[31653]={task_id=31653,min_level=500,max_level=529,item_list={[0]=item_table[56]},exp_copies=166.667,},
[31654]={task_id=31654,min_level=500,max_level=529,c_param1=10901,exp_copies=167,accept_desc="<color=#72eba9>击杀南天门守卫</color>",progress_desc="击杀南天门守卫(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀南天门守卫</color>",},
[31655]={task_id=31655,c_param1=10902,exp_copies=266.667,accept_desc="<color=#72eba9>击杀上古天灵</color>",progress_desc="击杀上古天灵(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀上古天灵</color>",},
[31656]={task_id=31656,c_param1=10903,accept_desc="<color=#72eba9>击杀卷帘天兵</color>",progress_desc="击杀卷帘天兵<per>1/20</per>)",commit_desc="<color=#72eba9>击杀卷帘天兵</color>",},
[31657]={task_id=31657,min_level=500,max_level=529,c_param1=10901,},
[31658]={task_id=31658,min_level=500,max_level=529,c_param1=10902,},
[31659]={task_id=31659,min_level=500,max_level=529,c_param1=10903,},
[31660]={task_id=31660,min_level=500,max_level=529,},
[31701]={task_id=31701,min_level=530,max_level=559,},
[31702]={task_id=31702,min_level=530,max_level=559,},
[31703]={task_id=31703,min_level=530,max_level=559,},
[31704]={task_id=31704,min_level=530,max_level=559,exp_copies=166.667,progress_desc="击杀卷帘天兵(<per>1/20</per>)",},
[31705]={task_id=31705,c_param1=10904,exp_copies=266.667,accept_desc="<color=#72eba9>击杀佑胜大将</color>",progress_desc="击杀佑胜大将(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀佑胜大将</color>",},
[31706]={task_id=31706,c_param1=10905,accept_desc="<color=#72eba9>击杀内庭天兵</color>",progress_desc="击杀内庭天兵<per>1/20</per>)",commit_desc="<color=#72eba9>击杀内庭天兵</color>",},
[31707]={task_id=31707,min_level=530,max_level=559,c_param1=10903,},
[31708]={task_id=31708,min_level=530,max_level=559,c_param1=10904,item_list={[0]=item_table[66]},},
[31709]={task_id=31709,min_level=530,max_level=559,c_param1=10905,},
[31710]={task_id=31710,min_level=530,max_level=559,item_list={[0]=item_table[58],[1]=item_table[70]},},
[31751]={task_id=31751,min_level=560,max_level=589,},
[31752]={task_id=31752,min_level=560,max_level=589,},
[31753]={task_id=31753,min_level=560,max_level=589,},
[31754]={task_id=31754,min_level=560,max_level=589,exp_copies=166.667,},
[31755]={task_id=31755,c_param1=10905,exp_copies=266.667,accept_desc="<color=#72eba9>击杀武曲星君</color>",progress_desc="击杀武曲星君(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀武曲星君</color>",},
[31756]={task_id=31756,c_param1=10906,accept_desc="<color=#72eba9>击杀飞蓬天兵</color>",progress_desc="击杀飞蓬天兵<per>1/20</per>)",commit_desc="<color=#72eba9>击杀飞蓬天兵</color>",},
[31757]={task_id=31757,min_level=560,max_level=589,c_param1=10904,},
[31758]={task_id=31758,min_level=560,max_level=589,c_param1=10905,},
[31759]={task_id=31759,min_level=560,max_level=589,c_param1=10906,item_list={[0]=item_table[66]},},
[31760]={task_id=31760,min_level=560,max_level=589,},
[31801]={task_id=31801,min_level=590,max_level=619,},
[31802]={task_id=31802,min_level=590,max_level=619,},
[31803]={task_id=31803,min_level=590,max_level=619,},
[31804]={task_id=31804,min_level=590,max_level=619,},
[31805]={task_id=31805,c_param1=10908,accept_desc="<color=#72eba9>击杀北天门守卫</color>",progress_desc="击杀北天门守卫(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀北天门守卫</color>",},
[31806]={task_id=31806,min_level=590,max_level=619,c_param1=10909,accept_desc="<color=#72eba9>击杀玄圣天兵</color>",progress_desc="击杀玄圣天兵<per>1/20</per>)",commit_desc="<color=#72eba9>击杀玄圣天兵</color>",},
[31807]={task_id=31807,min_level=590,max_level=619,c_param1=10907,},
[31808]={task_id=31808,min_level=590,max_level=619,c_param1=10908,},
[31809]={task_id=31809,min_level=590,max_level=619,c_param1=10909,},
[31810]={task_id=31810,min_level=590,max_level=619,},
[31851]={task_id=31851,min_level=620,max_level=1000,},
[31852]={task_id=31852,min_level=620,max_level=1000,},
[31853]={task_id=31853,min_level=620,max_level=1000,},
[31854]={task_id=31854,min_level=620,max_level=1000,item_list={[0]=item_table[69]},exp_copies=166.667,accept_desc="<color=#72eba9>击杀飞蓬天兵</color>",progress_desc="击杀飞蓬天兵(<per>1/20</per>)",commit_desc="<color=#72eba9>击杀飞蓬天兵</color>",},
[31855]={task_id=31855,min_level=620,max_level=1000,},
[31856]={task_id=31856,min_level=620,max_level=1000,},
[31857]={task_id=31857,min_level=620,max_level=1000,},
[31858]={task_id=31858,min_level=620,max_level=1000,},
[31859]={task_id=31859,min_level=620,max_level=1000,},
[31860]={task_id=31860,min_level=620,max_level=1000,},
[32001]={task_id=32001,task_name="仙盟名望",task_type=9,min_level=55,max_level=1000,coin_bind="",exp_factor="",silverticket="",zhenqi="",yuanli="",item_list={[0]=item_table[71]},gongxian="",shengwang="",exp_copies="",accept_desc="<color=#72eba9>点击领取任务</color>",progress_desc="",commit_desc="<color=#72eba9>点击领取任务</color>",is_first="",},
[33001]={task_id=33001,min_level=145,max_level=149,},
[33002]={task_id=33002,min_level=145,max_level=149,},
[33003]={task_id=33003,min_level=145,max_level=149,},
[33004]={task_id=33004,min_level=145,max_level=149,},
[33005]={task_id=33005,min_level=145,max_level=149,},
[33006]={task_id=33006,min_level=145,max_level=149,},
[33007]={task_id=33007,min_level=145,max_level=149,},
[33008]={task_id=33008,min_level=145,max_level=149,},
[33009]={task_id=33009,min_level=145,max_level=149,},
[33010]={task_id=33010,min_level=145,max_level=149,},
[33011]={task_id=33011,},
[33012]={task_id=33012,min_level=145,max_level=149,},
[33013]={task_id=33013,min_level=145,max_level=149,},
[33014]={task_id=33014,min_level=145,max_level=149,},
[33015]={task_id=33015,min_level=145,max_level=149,},
[33016]={task_id=33016,},
[33017]={task_id=33017,},
[33018]={task_id=33018,},
[33051]={task_id=33051,min_level=150,max_level=159,},
[33052]={task_id=33052,min_level=150,max_level=159,},
[33053]={task_id=33053,min_level=150,max_level=159,},
[33054]={task_id=33054,min_level=150,max_level=159,},
[33055]={task_id=33055,min_level=150,max_level=159,},
[33056]={task_id=33056,min_level=150,max_level=159,},
[33057]={task_id=33057,min_level=150,max_level=159,},
[33058]={task_id=33058,min_level=150,max_level=159,},
[33059]={task_id=33059,min_level=150,max_level=159,},
[33060]={task_id=33060,min_level=150,max_level=159,},
[33061]={task_id=33061,min_level=150,max_level=159,},
[33062]={task_id=33062,},
[33063]={task_id=33063,},
[33064]={task_id=33064,},
[33065]={task_id=33065,},
[33066]={task_id=33066,min_level=150,max_level=159,},
[33067]={task_id=33067,min_level=150,max_level=159,},
[33068]={task_id=33068,min_level=150,max_level=159,},
[33101]={task_id=33101,min_level=160,max_level=169,},
[33102]={task_id=33102,min_level=160,max_level=169,},
[33103]={task_id=33103,min_level=160,max_level=169,},
[33104]={task_id=33104,min_level=160,max_level=169,},
[33105]={task_id=33105,min_level=160,max_level=169,},
[33106]={task_id=33106,min_level=160,max_level=169,},
[33107]={task_id=33107,min_level=160,max_level=169,},
[33108]={task_id=33108,min_level=160,max_level=169,},
[33109]={task_id=33109,min_level=160,max_level=169,},
[33110]={task_id=33110,min_level=160,max_level=169,},
[33111]={task_id=33111,},
[33112]={task_id=33112,min_level=160,max_level=169,},
[33113]={task_id=33113,min_level=160,max_level=169,},
[33114]={task_id=33114,min_level=160,max_level=169,},
[33115]={task_id=33115,min_level=160,max_level=169,},
[33116]={task_id=33116,},
[33117]={task_id=33117,},
[33118]={task_id=33118,},
[33151]={task_id=33151,min_level=170,max_level=179,},
[33152]={task_id=33152,min_level=170,max_level=179,},
[33153]={task_id=33153,min_level=170,max_level=179,},
[33154]={task_id=33154,min_level=170,max_level=179,},
[33155]={task_id=33155,min_level=170,max_level=179,},
[33156]={task_id=33156,min_level=170,max_level=179,},
[33157]={task_id=33157,min_level=170,max_level=179,},
[33158]={task_id=33158,min_level=170,max_level=179,},
[33159]={task_id=33159,min_level=170,max_level=179,},
[33160]={task_id=33160,min_level=170,max_level=179,},
[33161]={task_id=33161,},
[33162]={task_id=33162,min_level=170,max_level=179,},
[33163]={task_id=33163,min_level=170,max_level=179,},
[33164]={task_id=33164,min_level=170,max_level=179,},
[33165]={task_id=33165,min_level=170,max_level=179,},
[33166]={task_id=33166,},
[33167]={task_id=33167,},
[33168]={task_id=33168,},
[33201]={task_id=33201,min_level=180,max_level=189,},
[33202]={task_id=33202,min_level=180,max_level=189,},
[33203]={task_id=33203,min_level=180,max_level=189,},
[33204]={task_id=33204,min_level=180,max_level=189,},
[33205]={task_id=33205,min_level=180,max_level=189,},
[33206]={task_id=33206,min_level=180,max_level=189,},
[33207]={task_id=33207,min_level=180,max_level=189,},
[33208]={task_id=33208,min_level=180,max_level=189,},
[33209]={task_id=33209,min_level=180,max_level=189,},
[33210]={task_id=33210,min_level=180,max_level=189,},
[33211]={task_id=33211,},
[33212]={task_id=33212,min_level=180,max_level=189,},
[33213]={task_id=33213,min_level=180,max_level=189,},
[33214]={task_id=33214,min_level=180,max_level=189,},
[33215]={task_id=33215,min_level=180,max_level=189,},
[33216]={task_id=33216,},
[33217]={task_id=33217,},
[33218]={task_id=33218,},
[33251]={task_id=33251,min_level=190,max_level=199,},
[33252]={task_id=33252,min_level=190,max_level=199,},
[33253]={task_id=33253,min_level=190,max_level=199,},
[33254]={task_id=33254,min_level=190,max_level=199,},
[33255]={task_id=33255,min_level=190,max_level=199,},
[33256]={task_id=33256,min_level=190,max_level=199,},
[33257]={task_id=33257,min_level=190,max_level=199,},
[33258]={task_id=33258,min_level=190,max_level=199,},
[33259]={task_id=33259,min_level=190,max_level=199,},
[33260]={task_id=33260,min_level=190,max_level=199,},
[33261]={task_id=33261,},
[33262]={task_id=33262,min_level=190,max_level=199,},
[33263]={task_id=33263,min_level=190,max_level=199,},
[33264]={task_id=33264,min_level=190,max_level=199,},
[33265]={task_id=33265,min_level=190,max_level=199,},
[33266]={task_id=33266,},
[33267]={task_id=33267,},
[33268]={task_id=33268,},
[33301]={task_id=33301,min_level=200,max_level=209,},
[33302]={task_id=33302,min_level=200,max_level=209,},
[33303]={task_id=33303,min_level=200,max_level=209,},
[33304]={task_id=33304,min_level=200,max_level=209,},
[33305]={task_id=33305,min_level=200,max_level=209,},
[33306]={task_id=33306,min_level=200,max_level=209,},
[33307]={task_id=33307,min_level=200,max_level=209,},
[33308]={task_id=33308,min_level=200,max_level=209,},
[33309]={task_id=33309,min_level=200,max_level=209,},
[33310]={task_id=33310,min_level=200,max_level=209,},
[33311]={task_id=33311,min_level=200,max_level=209,},
[33312]={task_id=33312,min_level=200,max_level=209,},
[33313]={task_id=33313,min_level=200,max_level=209,},
[33314]={task_id=33314,min_level=200,max_level=209,},
[33315]={task_id=33315,},
[33316]={task_id=33316,},
[33317]={task_id=33317,},
[33318]={task_id=33318,},
[33351]={task_id=33351,min_level=210,max_level=219,},
[33352]={task_id=33352,min_level=210,max_level=219,},
[33353]={task_id=33353,min_level=210,max_level=219,},
[33354]={task_id=33354,min_level=210,max_level=219,},
[33355]={task_id=33355,min_level=210,max_level=219,},
[33356]={task_id=33356,min_level=210,max_level=219,},
[33357]={task_id=33357,min_level=210,max_level=219,},
[33358]={task_id=33358,min_level=210,max_level=219,},
[33359]={task_id=33359,min_level=210,max_level=219,},
[33360]={task_id=33360,min_level=210,max_level=219,},
[33361]={task_id=33361,},
[33362]={task_id=33362,min_level=210,max_level=219,},
[33363]={task_id=33363,min_level=210,max_level=219,},
[33364]={task_id=33364,min_level=210,max_level=219,},
[33365]={task_id=33365,min_level=210,max_level=219,},
[33366]={task_id=33366,},
[33367]={task_id=33367,},
[33368]={task_id=33368,},
[33401]={task_id=33401,min_level=220,max_level=229,},
[33402]={task_id=33402,min_level=220,max_level=229,},
[33403]={task_id=33403,min_level=220,max_level=229,},
[33404]={task_id=33404,min_level=220,max_level=229,},
[33405]={task_id=33405,min_level=220,max_level=229,},
[33406]={task_id=33406,min_level=220,max_level=229,},
[33407]={task_id=33407,min_level=220,max_level=229,},
[33408]={task_id=33408,min_level=220,max_level=229,},
[33409]={task_id=33409,min_level=220,max_level=229,},
[33410]={task_id=33410,min_level=220,max_level=229,},
[33411]={task_id=33411,min_level=220,max_level=229,},
[33412]={task_id=33412,min_level=220,max_level=229,},
[33413]={task_id=33413,min_level=220,max_level=229,},
[33414]={task_id=33414,min_level=220,max_level=229,},
[33415]={task_id=33415,},
[33416]={task_id=33416,},
[33417]={task_id=33417,},
[33418]={task_id=33418,},
[33451]={task_id=33451,min_level=230,max_level=239,},
[33452]={task_id=33452,min_level=230,max_level=239,},
[33453]={task_id=33453,min_level=230,max_level=239,},
[33454]={task_id=33454,min_level=230,max_level=239,},
[33455]={task_id=33455,min_level=230,max_level=239,},
[33456]={task_id=33456,min_level=230,max_level=239,},
[33457]={task_id=33457,min_level=230,max_level=239,},
[33458]={task_id=33458,min_level=230,max_level=239,},
[33459]={task_id=33459,min_level=230,max_level=239,},
[33460]={task_id=33460,min_level=230,max_level=239,},
[33461]={task_id=33461,},
[33462]={task_id=33462,},
[33463]={task_id=33463,min_level=230,max_level=239,},
[33464]={task_id=33464,},
[33465]={task_id=33465,min_level=230,max_level=239,},
[33466]={task_id=33466,min_level=230,max_level=239,},
[33467]={task_id=33467,},
[33468]={task_id=33468,min_level=230,max_level=239,},
[33501]={task_id=33501,min_level=240,max_level=249,},
[33502]={task_id=33502,min_level=240,max_level=249,},
[33503]={task_id=33503,min_level=240,max_level=249,},
[33504]={task_id=33504,min_level=240,max_level=249,},
[33505]={task_id=33505,min_level=240,max_level=249,},
[33506]={task_id=33506,min_level=240,max_level=249,},
[33507]={task_id=33507,min_level=240,max_level=249,},
[33508]={task_id=33508,min_level=240,max_level=249,},
[33509]={task_id=33509,min_level=240,max_level=249,},
[33510]={task_id=33510,min_level=240,max_level=249,},
[33511]={task_id=33511,min_level=240,max_level=249,},
[33512]={task_id=33512,},
[33513]={task_id=33513,min_level=240,max_level=249,},
[33514]={task_id=33514,min_level=240,max_level=249,},
[33515]={task_id=33515,},
[33516]={task_id=33516,min_level=240,max_level=249,},
[33517]={task_id=33517,},
[33518]={task_id=33518,},
[33551]={task_id=33551,min_level=250,max_level=259,},
[33552]={task_id=33552,min_level=250,max_level=259,},
[33553]={task_id=33553,min_level=250,max_level=259,},
[33554]={task_id=33554,min_level=250,max_level=259,},
[33555]={task_id=33555,min_level=250,max_level=259,},
[33556]={task_id=33556,min_level=250,max_level=259,},
[33557]={task_id=33557,min_level=250,max_level=259,},
[33558]={task_id=33558,min_level=250,max_level=259,},
[33559]={task_id=33559,min_level=250,max_level=259,},
[33560]={task_id=33560,min_level=250,max_level=259,},
[33561]={task_id=33561,},
[33562]={task_id=33562,},
[33563]={task_id=33563,min_level=250,max_level=259,},
[33564]={task_id=33564,min_level=250,max_level=259,},
[33565]={task_id=33565,min_level=250,max_level=259,},
[33566]={task_id=33566,min_level=250,max_level=259,},
[33567]={task_id=33567,},
[33568]={task_id=33568,},
[33601]={task_id=33601,min_level=260,max_level=269,},
[33602]={task_id=33602,min_level=260,max_level=269,},
[33603]={task_id=33603,min_level=260,max_level=269,},
[33604]={task_id=33604,min_level=260,max_level=269,},
[33605]={task_id=33605,min_level=260,max_level=269,},
[33606]={task_id=33606,min_level=260,max_level=269,},
[33607]={task_id=33607,min_level=260,max_level=269,},
[33608]={task_id=33608,min_level=260,max_level=269,},
[33609]={task_id=33609,min_level=260,max_level=269,},
[33610]={task_id=33610,min_level=260,max_level=269,},
[33611]={task_id=33611,},
[33612]={task_id=33612,min_level=260,max_level=269,},
[33613]={task_id=33613,min_level=260,max_level=269,},
[33614]={task_id=33614,min_level=260,max_level=269,},
[33615]={task_id=33615,min_level=260,max_level=269,},
[33616]={task_id=33616,},
[33617]={task_id=33617,},
[33618]={task_id=33618,},
[33651]={task_id=33651,min_level=270,max_level=279,},
[33652]={task_id=33652,min_level=270,max_level=279,},
[33653]={task_id=33653,min_level=270,max_level=279,},
[33654]={task_id=33654,min_level=270,max_level=279,},
[33655]={task_id=33655,min_level=270,max_level=279,},
[33656]={task_id=33656,min_level=270,max_level=279,},
[33657]={task_id=33657,min_level=270,max_level=279,},
[33658]={task_id=33658,min_level=270,max_level=279,},
[33659]={task_id=33659,min_level=270,max_level=279,},
[33660]={task_id=33660,min_level=270,max_level=279,},
[33661]={task_id=33661,},
[33662]={task_id=33662,min_level=270,max_level=279,},
[33663]={task_id=33663,},
[33664]={task_id=33664,min_level=270,max_level=279,},
[33665]={task_id=33665,min_level=270,max_level=279,},
[33666]={task_id=33666,},
[33667]={task_id=33667,min_level=270,max_level=279,},
[33668]={task_id=33668,},
[33701]={task_id=33701,min_level=280,max_level=289,},
[33702]={task_id=33702,min_level=280,max_level=289,},
[33703]={task_id=33703,min_level=280,max_level=289,},
[33704]={task_id=33704,min_level=280,max_level=289,},
[33705]={task_id=33705,min_level=280,max_level=289,},
[33706]={task_id=33706,min_level=280,max_level=289,},
[33707]={task_id=33707,min_level=280,max_level=289,},
[33708]={task_id=33708,min_level=280,max_level=289,},
[33709]={task_id=33709,min_level=280,max_level=289,},
[33710]={task_id=33710,min_level=280,max_level=289,},
[33711]={task_id=33711,},
[33712]={task_id=33712,},
[33713]={task_id=33713,},
[33714]={task_id=33714,},
[33715]={task_id=33715,min_level=280,max_level=289,},
[33716]={task_id=33716,min_level=280,max_level=289,},
[33717]={task_id=33717,min_level=280,max_level=289,},
[33718]={task_id=33718,min_level=280,max_level=289,},
[33751]={task_id=33751,min_level=290,max_level=299,},
[33752]={task_id=33752,min_level=290,max_level=299,},
[33753]={task_id=33753,min_level=290,max_level=299,},
[33754]={task_id=33754,min_level=290,max_level=299,},
[33755]={task_id=33755,min_level=290,max_level=299,},
[33756]={task_id=33756,min_level=290,max_level=299,},
[33757]={task_id=33757,min_level=290,max_level=299,},
[33758]={task_id=33758,min_level=290,max_level=299,},
[33759]={task_id=33759,min_level=290,max_level=299,},
[33760]={task_id=33760,min_level=290,max_level=299,},
[33761]={task_id=33761,min_level=290,max_level=299,},
[33762]={task_id=33762,min_level=290,max_level=299,},
[33763]={task_id=33763,min_level=290,max_level=299,},
[33764]={task_id=33764,min_level=290,max_level=299,},
[33765]={task_id=33765,},
[33766]={task_id=33766,},
[33767]={task_id=33767,},
[33768]={task_id=33768,},
[33801]={task_id=33801,min_level=300,max_level=309,},
[33802]={task_id=33802,min_level=300,max_level=309,},
[33803]={task_id=33803,min_level=300,max_level=309,},
[33804]={task_id=33804,min_level=300,max_level=309,},
[33805]={task_id=33805,min_level=300,max_level=309,},
[33806]={task_id=33806,min_level=300,max_level=309,},
[33807]={task_id=33807,min_level=300,max_level=309,},
[33808]={task_id=33808,min_level=300,max_level=309,},
[33809]={task_id=33809,min_level=300,max_level=309,},
[33810]={task_id=33810,min_level=300,max_level=309,},
[33811]={task_id=33811,},
[33812]={task_id=33812,min_level=300,max_level=309,},
[33813]={task_id=33813,min_level=300,max_level=309,},
[33814]={task_id=33814,min_level=300,max_level=309,},
[33815]={task_id=33815,min_level=300,max_level=309,},
[33816]={task_id=33816,},
[33817]={task_id=33817,},
[33818]={task_id=33818,},
[33851]={task_id=33851,min_level=310,max_level=319,},
[33852]={task_id=33852,min_level=310,max_level=319,},
[33853]={task_id=33853,min_level=310,max_level=319,},
[33854]={task_id=33854,min_level=310,max_level=319,},
[33855]={task_id=33855,min_level=310,max_level=319,},
[33856]={task_id=33856,min_level=310,max_level=319,},
[33857]={task_id=33857,min_level=310,max_level=319,},
[33858]={task_id=33858,min_level=310,max_level=319,},
[33859]={task_id=33859,min_level=310,max_level=319,},
[33860]={task_id=33860,min_level=310,max_level=319,},
[33861]={task_id=33861,min_level=310,max_level=319,},
[33862]={task_id=33862,min_level=310,max_level=319,},
[33863]={task_id=33863,min_level=310,max_level=319,},
[33864]={task_id=33864,},
[33865]={task_id=33865,},
[33866]={task_id=33866,},
[33867]={task_id=33867,},
[33868]={task_id=33868,min_level=310,max_level=319,},
[33901]={task_id=33901,min_level=320,max_level=329,},
[33902]={task_id=33902,min_level=320,max_level=329,},
[33903]={task_id=33903,min_level=320,max_level=329,},
[33904]={task_id=33904,min_level=320,max_level=329,},
[33905]={task_id=33905,min_level=320,max_level=329,},
[33906]={task_id=33906,min_level=320,max_level=329,},
[33907]={task_id=33907,min_level=320,max_level=329,},
[33908]={task_id=33908,min_level=320,max_level=329,},
[33909]={task_id=33909,min_level=320,max_level=329,},
[33910]={task_id=33910,min_level=320,max_level=329,},
[33911]={task_id=33911,min_level=320,max_level=329,},
[33912]={task_id=33912,},
[33913]={task_id=33913,},
[33914]={task_id=33914,},
[33915]={task_id=33915,},
[33916]={task_id=33916,min_level=320,max_level=329,},
[33917]={task_id=33917,min_level=320,max_level=329,},
[33918]={task_id=33918,min_level=320,max_level=329,},
[33951]={task_id=33951,min_level=330,max_level=339,},
[33952]={task_id=33952,min_level=330,max_level=339,},
[33953]={task_id=33953,min_level=330,max_level=339,},
[33954]={task_id=33954,min_level=330,max_level=339,},
[33955]={task_id=33955,min_level=330,max_level=339,},
[33956]={task_id=33956,min_level=330,max_level=339,},
[33957]={task_id=33957,min_level=330,max_level=339,},
[33958]={task_id=33958,min_level=330,max_level=339,},
[33959]={task_id=33959,min_level=330,max_level=339,},
[33960]={task_id=33960,min_level=330,max_level=339,},
[33961]={task_id=33961,},
[33962]={task_id=33962,min_level=330,max_level=339,},
[33963]={task_id=33963,min_level=330,max_level=339,},
[33964]={task_id=33964,},
[33965]={task_id=33965,min_level=330,max_level=339,},
[33966]={task_id=33966,},
[33967]={task_id=33967,},
[33968]={task_id=33968,min_level=330,max_level=339,},
[34001]={task_id=34001,min_level=340,max_level=349,},
[34002]={task_id=34002,min_level=340,max_level=349,},
[34003]={task_id=34003,min_level=340,max_level=349,},
[34004]={task_id=34004,min_level=340,max_level=349,},
[34005]={task_id=34005,min_level=340,max_level=349,},
[34006]={task_id=34006,min_level=340,max_level=349,},
[34007]={task_id=34007,min_level=340,max_level=349,},
[34008]={task_id=34008,min_level=340,max_level=349,},
[34009]={task_id=34009,min_level=340,max_level=349,},
[34010]={task_id=34010,min_level=340,max_level=349,},
[34011]={task_id=34011,min_level=340,max_level=349,},
[34012]={task_id=34012,},
[34013]={task_id=34013,min_level=340,max_level=349,},
[34014]={task_id=34014,min_level=340,max_level=349,},
[34015]={task_id=34015,},
[34016]={task_id=34016,min_level=340,max_level=349,},
[34017]={task_id=34017,},
[34018]={task_id=34018,},
[34051]={task_id=34051,min_level=350,max_level=359,},
[34052]={task_id=34052,min_level=350,max_level=359,},
[34053]={task_id=34053,min_level=350,max_level=359,},
[34054]={task_id=34054,min_level=350,max_level=359,},
[34055]={task_id=34055,min_level=350,max_level=359,},
[34056]={task_id=34056,min_level=350,max_level=359,},
[34057]={task_id=34057,min_level=350,max_level=359,},
[34058]={task_id=34058,min_level=350,max_level=359,},
[34059]={task_id=34059,min_level=350,max_level=359,},
[34060]={task_id=34060,min_level=350,max_level=359,},
[34061]={task_id=34061,min_level=350,max_level=359,},
[34062]={task_id=34062,min_level=350,max_level=359,},
[34063]={task_id=34063,min_level=350,max_level=359,},
[34064]={task_id=34064,min_level=350,max_level=359,},
[34065]={task_id=34065,},
[34066]={task_id=34066,},
[34067]={task_id=34067,},
[34068]={task_id=34068,},
[34101]={task_id=34101,min_level=360,max_level=369,},
[34102]={task_id=34102,min_level=360,max_level=369,},
[34103]={task_id=34103,min_level=360,max_level=369,},
[34104]={task_id=34104,min_level=360,max_level=369,},
[34105]={task_id=34105,min_level=360,max_level=369,},
[34106]={task_id=34106,min_level=360,max_level=369,},
[34107]={task_id=34107,min_level=360,max_level=369,},
[34108]={task_id=34108,min_level=360,max_level=369,},
[34109]={task_id=34109,min_level=360,max_level=369,},
[34110]={task_id=34110,min_level=360,max_level=369,},
[34111]={task_id=34111,},
[34112]={task_id=34112,min_level=360,max_level=369,},
[34113]={task_id=34113,min_level=360,max_level=369,},
[34114]={task_id=34114,min_level=360,max_level=369,},
[34115]={task_id=34115,min_level=360,max_level=369,},
[34116]={task_id=34116,},
[34117]={task_id=34117,},
[34118]={task_id=34118,},
[34151]={task_id=34151,min_level=370,max_level=379,},
[34152]={task_id=34152,min_level=370,max_level=379,},
[34153]={task_id=34153,min_level=370,max_level=379,},
[34154]={task_id=34154,min_level=370,max_level=379,},
[34155]={task_id=34155,min_level=370,max_level=379,},
[34156]={task_id=34156,min_level=370,max_level=379,},
[34157]={task_id=34157,min_level=370,max_level=379,},
[34158]={task_id=34158,min_level=370,max_level=379,},
[34159]={task_id=34159,min_level=370,max_level=379,},
[34160]={task_id=34160,min_level=370,max_level=379,},
[34161]={task_id=34161,min_level=370,max_level=379,},
[34162]={task_id=34162,min_level=370,max_level=379,},
[34163]={task_id=34163,min_level=370,max_level=379,},
[34164]={task_id=34164,min_level=370,max_level=379,},
[34165]={task_id=34165,},
[34166]={task_id=34166,},
[34167]={task_id=34167,},
[34168]={task_id=34168,},
[34201]={task_id=34201,min_level=380,max_level=389,},
[34202]={task_id=34202,min_level=380,max_level=389,},
[34203]={task_id=34203,min_level=380,max_level=389,},
[34204]={task_id=34204,min_level=380,max_level=389,},
[34205]={task_id=34205,min_level=380,max_level=389,},
[34206]={task_id=34206,min_level=380,max_level=389,},
[34207]={task_id=34207,min_level=380,max_level=389,},
[34208]={task_id=34208,min_level=380,max_level=389,},
[34209]={task_id=34209,min_level=380,max_level=389,},
[34210]={task_id=34210,min_level=380,max_level=389,},
[34211]={task_id=34211,min_level=380,max_level=389,},
[34212]={task_id=34212,min_level=380,max_level=389,},
[34213]={task_id=34213,},
[34214]={task_id=34214,min_level=380,max_level=389,},
[34215]={task_id=34215,},
[34216]={task_id=34216,},
[34217]={task_id=34217,min_level=380,max_level=389,},
[34218]={task_id=34218,},
[34251]={task_id=34251,min_level=390,max_level=399,},
[34252]={task_id=34252,min_level=390,max_level=399,},
[34253]={task_id=34253,min_level=390,max_level=399,},
[34254]={task_id=34254,min_level=390,max_level=399,},
[34255]={task_id=34255,min_level=390,max_level=399,},
[34256]={task_id=34256,min_level=390,max_level=399,},
[34257]={task_id=34257,min_level=390,max_level=399,},
[34258]={task_id=34258,min_level=390,max_level=399,},
[34259]={task_id=34259,min_level=390,max_level=399,},
[34260]={task_id=34260,min_level=390,max_level=399,},
[34261]={task_id=34261,},
[34262]={task_id=34262,min_level=390,max_level=399,},
[34263]={task_id=34263,min_level=390,max_level=399,},
[34264]={task_id=34264,min_level=390,max_level=399,},
[34265]={task_id=34265,min_level=390,max_level=399,},
[34266]={task_id=34266,},
[34267]={task_id=34267,},
[34268]={task_id=34268,},
[34301]={task_id=34301,min_level=400,max_level=409,},
[34302]={task_id=34302,min_level=400,max_level=409,},
[34303]={task_id=34303,min_level=400,max_level=409,},
[34304]={task_id=34304,min_level=400,max_level=409,},
[34305]={task_id=34305,min_level=400,max_level=409,},
[34306]={task_id=34306,min_level=400,max_level=409,},
[34307]={task_id=34307,min_level=400,max_level=409,},
[34308]={task_id=34308,min_level=400,max_level=409,},
[34309]={task_id=34309,min_level=400,max_level=409,},
[34310]={task_id=34310,min_level=400,max_level=409,},
[34311]={task_id=34311,min_level=400,max_level=409,},
[34312]={task_id=34312,},
[34313]={task_id=34313,},
[34314]={task_id=34314,},
[34315]={task_id=34315,},
[34316]={task_id=34316,min_level=400,max_level=409,},
[34317]={task_id=34317,min_level=400,max_level=409,},
[34318]={task_id=34318,min_level=400,max_level=409,},
[34351]={task_id=34351,min_level=410,max_level=419,},
[34352]={task_id=34352,min_level=410,max_level=419,},
[34353]={task_id=34353,min_level=410,max_level=419,},
[34354]={task_id=34354,min_level=410,max_level=419,},
[34355]={task_id=34355,min_level=410,max_level=419,},
[34356]={task_id=34356,min_level=410,max_level=419,},
[34357]={task_id=34357,min_level=410,max_level=419,},
[34358]={task_id=34358,min_level=410,max_level=419,},
[34359]={task_id=34359,min_level=410,max_level=419,},
[34360]={task_id=34360,min_level=410,max_level=419,},
[34361]={task_id=34361,},
[34362]={task_id=34362,min_level=410,max_level=419,},
[34363]={task_id=34363,min_level=410,max_level=419,},
[34364]={task_id=34364,min_level=410,max_level=419,},
[34365]={task_id=34365,min_level=410,max_level=419,},
[34366]={task_id=34366,},
[34367]={task_id=34367,},
[34368]={task_id=34368,},
[34401]={task_id=34401,min_level=420,max_level=429,},
[34402]={task_id=34402,min_level=420,max_level=429,},
[34403]={task_id=34403,min_level=420,max_level=429,},
[34404]={task_id=34404,min_level=420,max_level=429,},
[34405]={task_id=34405,min_level=420,max_level=429,},
[34406]={task_id=34406,min_level=420,max_level=429,},
[34407]={task_id=34407,min_level=420,max_level=429,},
[34408]={task_id=34408,min_level=420,max_level=429,},
[34409]={task_id=34409,min_level=420,max_level=429,},
[34410]={task_id=34410,min_level=420,max_level=429,},
[34411]={task_id=34411,},
[34412]={task_id=34412,min_level=420,max_level=429,},
[34413]={task_id=34413,min_level=420,max_level=429,},
[34414]={task_id=34414,min_level=420,max_level=429,},
[34415]={task_id=34415,min_level=420,max_level=429,},
[34416]={task_id=34416,},
[34417]={task_id=34417,},
[34418]={task_id=34418,},
[34451]={task_id=34451,min_level=430,max_level=439,},
[34452]={task_id=34452,min_level=430,max_level=439,},
[34453]={task_id=34453,min_level=430,max_level=439,},
[34454]={task_id=34454,min_level=430,max_level=439,},
[34455]={task_id=34455,min_level=430,max_level=439,},
[34456]={task_id=34456,min_level=430,max_level=439,},
[34457]={task_id=34457,min_level=430,max_level=439,},
[34458]={task_id=34458,min_level=430,max_level=439,},
[34459]={task_id=34459,min_level=430,max_level=439,},
[34460]={task_id=34460,min_level=430,max_level=439,},
[34461]={task_id=34461,min_level=430,max_level=439,},
[34462]={task_id=34462,min_level=430,max_level=439,},
[34463]={task_id=34463,min_level=430,max_level=439,},
[34464]={task_id=34464,min_level=430,max_level=439,},
[34465]={task_id=34465,},
[34466]={task_id=34466,},
[34467]={task_id=34467,},
[34468]={task_id=34468,},
[34501]={task_id=34501,min_level=440,max_level=449,},
[34502]={task_id=34502,min_level=440,max_level=449,},
[34503]={task_id=34503,min_level=440,max_level=449,},
[34504]={task_id=34504,min_level=440,max_level=449,},
[34505]={task_id=34505,min_level=440,max_level=449,},
[34506]={task_id=34506,min_level=440,max_level=449,},
[34507]={task_id=34507,min_level=440,max_level=449,},
[34508]={task_id=34508,min_level=440,max_level=449,},
[34509]={task_id=34509,min_level=440,max_level=449,},
[34510]={task_id=34510,min_level=440,max_level=449,},
[34511]={task_id=34511,},
[34512]={task_id=34512,min_level=440,max_level=449,},
[34513]={task_id=34513,min_level=440,max_level=449,},
[34514]={task_id=34514,min_level=440,max_level=449,},
[34515]={task_id=34515,min_level=440,max_level=449,},
[34516]={task_id=34516,},
[34517]={task_id=34517,},
[34518]={task_id=34518,},
[34551]={task_id=34551,min_level=450,max_level=459,},
[34552]={task_id=34552,min_level=450,max_level=459,},
[34553]={task_id=34553,min_level=450,max_level=459,},
[34554]={task_id=34554,min_level=450,max_level=459,},
[34555]={task_id=34555,min_level=450,max_level=459,},
[34556]={task_id=34556,min_level=450,max_level=459,},
[34557]={task_id=34557,min_level=450,max_level=459,},
[34558]={task_id=34558,min_level=450,max_level=459,},
[34559]={task_id=34559,min_level=450,max_level=459,},
[34560]={task_id=34560,min_level=450,max_level=459,},
[34561]={task_id=34561,},
[34562]={task_id=34562,min_level=450,max_level=459,},
[34563]={task_id=34563,min_level=450,max_level=459,},
[34564]={task_id=34564,min_level=450,max_level=459,},
[34565]={task_id=34565,min_level=450,max_level=459,},
[34566]={task_id=34566,},
[34567]={task_id=34567,},
[34568]={task_id=34568,},
[34601]={task_id=34601,min_level=460,max_level=469,},
[34602]={task_id=34602,min_level=460,max_level=469,},
[34603]={task_id=34603,min_level=460,max_level=469,},
[34604]={task_id=34604,min_level=460,max_level=469,},
[34605]={task_id=34605,min_level=460,max_level=469,},
[34606]={task_id=34606,min_level=460,max_level=469,},
[34607]={task_id=34607,min_level=460,max_level=469,},
[34608]={task_id=34608,min_level=460,max_level=469,},
[34609]={task_id=34609,min_level=460,max_level=469,},
[34610]={task_id=34610,min_level=460,max_level=469,},
[34611]={task_id=34611,},
[34612]={task_id=34612,},
[34613]={task_id=34613,},
[34614]={task_id=34614,},
[34615]={task_id=34615,min_level=460,max_level=469,},
[34616]={task_id=34616,min_level=460,max_level=469,},
[34617]={task_id=34617,min_level=460,max_level=469,},
[34618]={task_id=34618,min_level=460,max_level=469,},
[34651]={task_id=34651,min_level=470,max_level=479,},
[34652]={task_id=34652,min_level=470,max_level=479,},
[34653]={task_id=34653,min_level=470,max_level=479,},
[34654]={task_id=34654,min_level=470,max_level=479,},
[34655]={task_id=34655,min_level=470,max_level=479,},
[34656]={task_id=34656,min_level=470,max_level=479,},
[34657]={task_id=34657,min_level=470,max_level=479,},
[34658]={task_id=34658,min_level=470,max_level=479,},
[34659]={task_id=34659,min_level=470,max_level=479,},
[34660]={task_id=34660,min_level=470,max_level=479,},
[34661]={task_id=34661,min_level=470,max_level=479,},
[34662]={task_id=34662,min_level=470,max_level=479,},
[34663]={task_id=34663,min_level=470,max_level=479,},
[34664]={task_id=34664,},
[34665]={task_id=34665,},
[34666]={task_id=34666,},
[34667]={task_id=34667,},
[34668]={task_id=34668,min_level=470,max_level=479,},
[34701]={task_id=34701,min_level=480,max_level=489,},
[34702]={task_id=34702,min_level=480,max_level=489,},
[34703]={task_id=34703,min_level=480,max_level=489,},
[34704]={task_id=34704,min_level=480,max_level=489,},
[34705]={task_id=34705,min_level=480,max_level=489,},
[34706]={task_id=34706,min_level=480,max_level=489,},
[34707]={task_id=34707,min_level=480,max_level=489,},
[34708]={task_id=34708,min_level=480,max_level=489,},
[34709]={task_id=34709,min_level=480,max_level=489,},
[34710]={task_id=34710,min_level=480,max_level=489,},
[34711]={task_id=34711,min_level=480,max_level=489,},
[34712]={task_id=34712,min_level=480,max_level=489,},
[34713]={task_id=34713,},
[34714]={task_id=34714,min_level=480,max_level=489,},
[34715]={task_id=34715,},
[34716]={task_id=34716,},
[34717]={task_id=34717,min_level=480,max_level=489,},
[34718]={task_id=34718,},
[34751]={task_id=34751,min_level=490,max_level=499,},
[34752]={task_id=34752,min_level=490,max_level=499,},
[34753]={task_id=34753,min_level=490,max_level=499,},
[34754]={task_id=34754,min_level=490,max_level=499,},
[34755]={task_id=34755,min_level=490,max_level=499,},
[34756]={task_id=34756,min_level=490,max_level=499,},
[34757]={task_id=34757,min_level=490,max_level=499,},
[34758]={task_id=34758,min_level=490,max_level=499,},
[34759]={task_id=34759,min_level=490,max_level=499,},
[34760]={task_id=34760,min_level=490,max_level=499,},
[34761]={task_id=34761,},
[34762]={task_id=34762,},
[34763]={task_id=34763,min_level=490,max_level=499,},
[34764]={task_id=34764,min_level=490,max_level=499,},
[34765]={task_id=34765,min_level=490,max_level=499,},
[34766]={task_id=34766,min_level=490,max_level=499,},
[34767]={task_id=34767,},
[34768]={task_id=34768,},
[34801]={task_id=34801,min_level=500,max_level=509,},
[34802]={task_id=34802,min_level=500,max_level=509,},
[34803]={task_id=34803,min_level=500,max_level=509,},
[34804]={task_id=34804,min_level=500,max_level=509,},
[34805]={task_id=34805,min_level=500,max_level=509,},
[34806]={task_id=34806,min_level=500,max_level=509,},
[34807]={task_id=34807,min_level=500,max_level=509,},
[34808]={task_id=34808,min_level=500,max_level=509,},
[34809]={task_id=34809,min_level=500,max_level=509,},
[34810]={task_id=34810,min_level=500,max_level=509,},
[34811]={task_id=34811,},
[34812]={task_id=34812,},
[34813]={task_id=34813,},
[34814]={task_id=34814,},
[34815]={task_id=34815,min_level=500,max_level=509,},
[34816]={task_id=34816,min_level=500,max_level=509,},
[34817]={task_id=34817,min_level=500,max_level=509,},
[34818]={task_id=34818,min_level=500,max_level=509,},
[34851]={task_id=34851,min_level=510,max_level=519,},
[34852]={task_id=34852,min_level=510,max_level=519,},
[34853]={task_id=34853,min_level=510,max_level=519,},
[34854]={task_id=34854,min_level=510,max_level=519,},
[34855]={task_id=34855,min_level=510,max_level=519,},
[34856]={task_id=34856,min_level=510,max_level=519,},
[34857]={task_id=34857,min_level=510,max_level=519,},
[34858]={task_id=34858,min_level=510,max_level=519,},
[34859]={task_id=34859,min_level=510,max_level=519,},
[34860]={task_id=34860,min_level=510,max_level=519,},
[34861]={task_id=34861,min_level=510,max_level=519,},
[34862]={task_id=34862,min_level=510,max_level=519,},
[34863]={task_id=34863,min_level=510,max_level=519,},
[34864]={task_id=34864,min_level=510,max_level=519,},
[34865]={task_id=34865,},
[34866]={task_id=34866,},
[34867]={task_id=34867,},
[34868]={task_id=34868,},
[34901]={task_id=34901,min_level=520,max_level=529,},
[34902]={task_id=34902,min_level=520,max_level=529,},
[34903]={task_id=34903,min_level=520,max_level=529,},
[34904]={task_id=34904,min_level=520,max_level=529,},
[34905]={task_id=34905,min_level=520,max_level=529,},
[34906]={task_id=34906,min_level=520,max_level=529,},
[34907]={task_id=34907,min_level=520,max_level=529,},
[34908]={task_id=34908,min_level=520,max_level=529,},
[34909]={task_id=34909,min_level=520,max_level=529,},
[34910]={task_id=34910,min_level=520,max_level=529,},
[34911]={task_id=34911,min_level=520,max_level=529,},
[34912]={task_id=34912,min_level=520,max_level=529,},
[34913]={task_id=34913,min_level=520,max_level=529,},
[34914]={task_id=34914,min_level=520,max_level=529,},
[34915]={task_id=34915,},
[34916]={task_id=34916,},
[34917]={task_id=34917,},
[34918]={task_id=34918,},
[34951]={task_id=34951,min_level=530,max_level=539,},
[34952]={task_id=34952,min_level=530,max_level=539,},
[34953]={task_id=34953,min_level=530,max_level=539,},
[34954]={task_id=34954,min_level=530,max_level=539,},
[34955]={task_id=34955,min_level=530,max_level=539,},
[34956]={task_id=34956,min_level=530,max_level=539,},
[34957]={task_id=34957,min_level=530,max_level=539,},
[34958]={task_id=34958,min_level=530,max_level=539,},
[34959]={task_id=34959,min_level=530,max_level=539,},
[34960]={task_id=34960,min_level=530,max_level=539,},
[34961]={task_id=34961,},
[34962]={task_id=34962,min_level=530,max_level=539,},
[34963]={task_id=34963,min_level=530,max_level=539,},
[34964]={task_id=34964,min_level=530,max_level=539,},
[34965]={task_id=34965,min_level=530,max_level=539,},
[34966]={task_id=34966,},
[34967]={task_id=34967,},
[34968]={task_id=34968,},
[35001]={task_id=35001,min_level=540,max_level=549,},
[35002]={task_id=35002,min_level=540,max_level=549,},
[35003]={task_id=35003,min_level=540,max_level=549,},
[35004]={task_id=35004,min_level=540,max_level=549,},
[35005]={task_id=35005,min_level=540,max_level=549,},
[35006]={task_id=35006,min_level=540,max_level=549,},
[35007]={task_id=35007,min_level=540,max_level=549,},
[35008]={task_id=35008,min_level=540,max_level=549,},
[35009]={task_id=35009,min_level=540,max_level=549,},
[35010]={task_id=35010,min_level=540,max_level=549,},
[35011]={task_id=35011,min_level=540,max_level=549,},
[35012]={task_id=35012,min_level=540,max_level=549,},
[35013]={task_id=35013,min_level=540,max_level=549,},
[35014]={task_id=35014,min_level=540,max_level=549,},
[35015]={task_id=35015,},
[35016]={task_id=35016,},
[35017]={task_id=35017,},
[35018]={task_id=35018,},
[35051]={task_id=35051,min_level=550,max_level=559,},
[35052]={task_id=35052,min_level=550,max_level=559,},
[35053]={task_id=35053,min_level=550,max_level=559,},
[35054]={task_id=35054,min_level=550,max_level=559,},
[35055]={task_id=35055,min_level=550,max_level=559,},
[35056]={task_id=35056,min_level=550,max_level=559,},
[35057]={task_id=35057,min_level=550,max_level=559,},
[35058]={task_id=35058,min_level=550,max_level=559,},
[35059]={task_id=35059,min_level=550,max_level=559,},
[35060]={task_id=35060,min_level=550,max_level=559,},
[35061]={task_id=35061,},
[35062]={task_id=35062,min_level=550,max_level=559,},
[35063]={task_id=35063,min_level=550,max_level=559,},
[35064]={task_id=35064,min_level=550,max_level=559,},
[35065]={task_id=35065,min_level=550,max_level=559,},
[35066]={task_id=35066,},
[35067]={task_id=35067,},
[35068]={task_id=35068,},
[35101]={task_id=35101,min_level=560,max_level=569,},
[35102]={task_id=35102,min_level=560,max_level=569,},
[35103]={task_id=35103,min_level=560,max_level=569,},
[35104]={task_id=35104,min_level=560,max_level=569,},
[35105]={task_id=35105,min_level=560,max_level=569,},
[35106]={task_id=35106,min_level=560,max_level=569,},
[35107]={task_id=35107,min_level=560,max_level=569,},
[35108]={task_id=35108,min_level=560,max_level=569,},
[35109]={task_id=35109,min_level=560,max_level=569,},
[35110]={task_id=35110,min_level=560,max_level=569,},
[35111]={task_id=35111,},
[35112]={task_id=35112,min_level=560,max_level=569,},
[35113]={task_id=35113,min_level=560,max_level=569,},
[35114]={task_id=35114,},
[35115]={task_id=35115,min_level=560,max_level=569,},
[35116]={task_id=35116,},
[35117]={task_id=35117,},
[35118]={task_id=35118,min_level=560,max_level=569,},
[35151]={task_id=35151,min_level=570,max_level=579,},
[35152]={task_id=35152,min_level=570,max_level=579,},
[35153]={task_id=35153,min_level=570,max_level=579,},
[35154]={task_id=35154,min_level=570,max_level=579,},
[35155]={task_id=35155,min_level=570,max_level=579,},
[35156]={task_id=35156,min_level=570,max_level=579,},
[35157]={task_id=35157,min_level=570,max_level=579,},
[35158]={task_id=35158,min_level=570,max_level=579,},
[35159]={task_id=35159,min_level=570,max_level=579,},
[35160]={task_id=35160,min_level=570,max_level=579,},
[35161]={task_id=35161,min_level=570,max_level=579,},
[35162]={task_id=35162,min_level=570,max_level=579,},
[35163]={task_id=35163,min_level=570,max_level=579,},
[35164]={task_id=35164,min_level=570,max_level=579,},
[35165]={task_id=35165,},
[35166]={task_id=35166,},
[35167]={task_id=35167,},
[35168]={task_id=35168,},
[35201]={task_id=35201,min_level=580,max_level=589,},
[35202]={task_id=35202,min_level=580,max_level=589,},
[35203]={task_id=35203,min_level=580,max_level=589,},
[35204]={task_id=35204,min_level=580,max_level=589,},
[35205]={task_id=35205,min_level=580,max_level=589,},
[35206]={task_id=35206,min_level=580,max_level=589,},
[35207]={task_id=35207,min_level=580,max_level=589,},
[35208]={task_id=35208,min_level=580,max_level=589,},
[35209]={task_id=35209,min_level=580,max_level=589,},
[35210]={task_id=35210,min_level=580,max_level=589,},
[35211]={task_id=35211,min_level=580,max_level=589,},
[35212]={task_id=35212,},
[35213]={task_id=35213,},
[35214]={task_id=35214,},
[35215]={task_id=35215,},
[35216]={task_id=35216,min_level=580,max_level=589,},
[35217]={task_id=35217,min_level=580,max_level=589,},
[35218]={task_id=35218,min_level=580,max_level=589,},
[35251]={task_id=35251,min_level=590,max_level=599,},
[35252]={task_id=35252,min_level=590,max_level=599,},
[35253]={task_id=35253,min_level=590,max_level=599,},
[35254]={task_id=35254,min_level=590,max_level=599,},
[35255]={task_id=35255,min_level=590,max_level=599,},
[35256]={task_id=35256,min_level=590,max_level=599,},
[35257]={task_id=35257,min_level=590,max_level=599,},
[35258]={task_id=35258,min_level=590,max_level=599,},
[35259]={task_id=35259,min_level=590,max_level=599,},
[35260]={task_id=35260,min_level=590,max_level=599,},
[35261]={task_id=35261,},
[35262]={task_id=35262,min_level=590,max_level=599,},
[35263]={task_id=35263,min_level=590,max_level=599,},
[35264]={task_id=35264,min_level=590,max_level=599,},
[35265]={task_id=35265,min_level=590,max_level=599,},
[35266]={task_id=35266,},
[35267]={task_id=35267,},
[35268]={task_id=35268,},
[35301]={task_id=35301,min_level=600,max_level=609,},
[35302]={task_id=35302,min_level=600,max_level=609,},
[35303]={task_id=35303,min_level=600,max_level=609,},
[35304]={task_id=35304,min_level=600,max_level=609,},
[35305]={task_id=35305,min_level=600,max_level=609,},
[35306]={task_id=35306,min_level=600,max_level=609,},
[35307]={task_id=35307,min_level=600,max_level=609,},
[35308]={task_id=35308,min_level=600,max_level=609,},
[35309]={task_id=35309,min_level=600,max_level=609,},
[35310]={task_id=35310,min_level=600,max_level=609,},
[35311]={task_id=35311,min_level=600,max_level=609,},
[35312]={task_id=35312,min_level=600,max_level=609,},
[35313]={task_id=35313,min_level=600,max_level=609,},
[35314]={task_id=35314,min_level=600,max_level=609,},
[35315]={task_id=35315,},
[35316]={task_id=35316,},
[35317]={task_id=35317,},
[35318]={task_id=35318,},
[35351]={task_id=35351,min_level=610,max_level=619,},
[35352]={task_id=35352,min_level=610,max_level=619,},
[35353]={task_id=35353,min_level=610,max_level=619,},
[35354]={task_id=35354,min_level=610,max_level=619,},
[35355]={task_id=35355,min_level=610,max_level=619,},
[35356]={task_id=35356,min_level=610,max_level=619,},
[35357]={task_id=35357,min_level=610,max_level=619,},
[35358]={task_id=35358,min_level=610,max_level=619,},
[35359]={task_id=35359,min_level=610,max_level=619,},
[35360]={task_id=35360,min_level=610,max_level=619,},
[35361]={task_id=35361,min_level=610,max_level=619,},
[35362]={task_id=35362,min_level=610,max_level=619,},
[35363]={task_id=35363,min_level=610,max_level=619,},
[35364]={task_id=35364,min_level=610,max_level=619,},
[35365]={task_id=35365,},
[35366]={task_id=35366,},
[35367]={task_id=35367,},
[35368]={task_id=35368,},
[35401]={task_id=35401,min_level=620,max_level=629,},
[35402]={task_id=35402,min_level=620,max_level=629,},
[35403]={task_id=35403,min_level=620,max_level=629,},
[35404]={task_id=35404,min_level=620,max_level=629,},
[35405]={task_id=35405,min_level=620,max_level=629,},
[35406]={task_id=35406,min_level=620,max_level=629,},
[35407]={task_id=35407,min_level=620,max_level=629,},
[35408]={task_id=35408,min_level=620,max_level=629,},
[35409]={task_id=35409,min_level=620,max_level=629,},
[35410]={task_id=35410,min_level=620,max_level=629,},
[35411]={task_id=35411,},
[35412]={task_id=35412,min_level=620,max_level=629,},
[35413]={task_id=35413,},
[35414]={task_id=35414,},
[35415]={task_id=35415,min_level=620,max_level=629,},
[35416]={task_id=35416,},
[35417]={task_id=35417,min_level=620,max_level=629,},
[35418]={task_id=35418,min_level=620,max_level=629,},
[35451]={task_id=35451,min_level=630,max_level=639,},
[35452]={task_id=35452,min_level=630,max_level=639,},
[35453]={task_id=35453,min_level=630,max_level=639,},
[35454]={task_id=35454,min_level=630,max_level=639,},
[35455]={task_id=35455,min_level=630,max_level=639,},
[35456]={task_id=35456,min_level=630,max_level=639,},
[35457]={task_id=35457,min_level=630,max_level=639,},
[35458]={task_id=35458,min_level=630,max_level=639,},
[35459]={task_id=35459,min_level=630,max_level=639,},
[35460]={task_id=35460,min_level=630,max_level=639,},
[35461]={task_id=35461,},
[35462]={task_id=35462,},
[35463]={task_id=35463,},
[35464]={task_id=35464,min_level=630,max_level=639,},
[35465]={task_id=35465,min_level=630,max_level=639,},
[35466]={task_id=35466,min_level=630,max_level=639,},
[35467]={task_id=35467,min_level=630,max_level=639,},
[35468]={task_id=35468,},
[35501]={task_id=35501,min_level=640,max_level=649,},
[35502]={task_id=35502,min_level=640,max_level=649,},
[35503]={task_id=35503,min_level=640,max_level=649,},
[35504]={task_id=35504,min_level=640,max_level=649,},
[35505]={task_id=35505,min_level=640,max_level=649,},
[35506]={task_id=35506,min_level=640,max_level=649,},
[35507]={task_id=35507,min_level=640,max_level=649,},
[35508]={task_id=35508,min_level=640,max_level=649,},
[35509]={task_id=35509,min_level=640,max_level=649,},
[35510]={task_id=35510,min_level=640,max_level=649,},
[35511]={task_id=35511,},
[35512]={task_id=35512,min_level=640,max_level=649,},
[35513]={task_id=35513,min_level=640,max_level=649,},
[35514]={task_id=35514,min_level=640,max_level=649,},
[35515]={task_id=35515,min_level=640,max_level=649,},
[35516]={task_id=35516,},
[35517]={task_id=35517,},
[35518]={task_id=35518,},
[35551]={task_id=35551,min_level=650,max_level=659,},
[35552]={task_id=35552,min_level=650,max_level=659,},
[35553]={task_id=35553,min_level=650,max_level=659,},
[35554]={task_id=35554,min_level=650,max_level=659,},
[35555]={task_id=35555,min_level=650,max_level=659,},
[35556]={task_id=35556,min_level=650,max_level=659,},
[35557]={task_id=35557,min_level=650,max_level=659,},
[35558]={task_id=35558,min_level=650,max_level=659,},
[35559]={task_id=35559,min_level=650,max_level=659,},
[35560]={task_id=35560,min_level=650,max_level=659,},
[35561]={task_id=35561,min_level=650,max_level=659,},
[35562]={task_id=35562,min_level=650,max_level=659,},
[35563]={task_id=35563,min_level=650,max_level=659,},
[35564]={task_id=35564,},
[35565]={task_id=35565,},
[35566]={task_id=35566,},
[35567]={task_id=35567,},
[35568]={task_id=35568,min_level=650,max_level=659,},
[35601]={task_id=35601,min_level=660,max_level=669,},
[35602]={task_id=35602,min_level=660,max_level=669,},
[35603]={task_id=35603,min_level=660,max_level=669,},
[35604]={task_id=35604,min_level=660,max_level=669,},
[35605]={task_id=35605,min_level=660,max_level=669,},
[35606]={task_id=35606,min_level=660,max_level=669,},
[35607]={task_id=35607,min_level=660,max_level=669,},
[35608]={task_id=35608,min_level=660,max_level=669,},
[35609]={task_id=35609,min_level=660,max_level=669,},
[35610]={task_id=35610,min_level=660,max_level=669,},
[35611]={task_id=35611,min_level=660,max_level=669,},
[35612]={task_id=35612,min_level=660,max_level=669,},
[35613]={task_id=35613,min_level=660,max_level=669,},
[35614]={task_id=35614,min_level=660,max_level=669,},
[35615]={task_id=35615,},
[35616]={task_id=35616,},
[35617]={task_id=35617,},
[35618]={task_id=35618,},
[35651]={task_id=35651,min_level=670,max_level=679,},
[35652]={task_id=35652,min_level=670,max_level=679,},
[35653]={task_id=35653,min_level=670,max_level=679,},
[35654]={task_id=35654,min_level=670,max_level=679,},
[35655]={task_id=35655,min_level=670,max_level=679,},
[35656]={task_id=35656,min_level=670,max_level=679,},
[35657]={task_id=35657,min_level=670,max_level=679,},
[35658]={task_id=35658,min_level=670,max_level=679,},
[35659]={task_id=35659,min_level=670,max_level=679,},
[35660]={task_id=35660,min_level=670,max_level=679,},
[35661]={task_id=35661,},
[35662]={task_id=35662,},
[35663]={task_id=35663,},
[35664]={task_id=35664,},
[35665]={task_id=35665,min_level=670,max_level=679,},
[35666]={task_id=35666,min_level=670,max_level=679,},
[35667]={task_id=35667,min_level=670,max_level=679,},
[35668]={task_id=35668,min_level=670,max_level=679,},
[35701]={task_id=35701,min_level=680,max_level=689,},
[35702]={task_id=35702,min_level=680,max_level=689,},
[35703]={task_id=35703,min_level=680,max_level=689,},
[35704]={task_id=35704,min_level=680,max_level=689,},
[35705]={task_id=35705,min_level=680,max_level=689,},
[35706]={task_id=35706,min_level=680,max_level=689,},
[35707]={task_id=35707,min_level=680,max_level=689,},
[35708]={task_id=35708,min_level=680,max_level=689,},
[35709]={task_id=35709,min_level=680,max_level=689,},
[35710]={task_id=35710,min_level=680,max_level=689,},
[35711]={task_id=35711,min_level=680,max_level=689,},
[35712]={task_id=35712,min_level=680,max_level=689,},
[35713]={task_id=35713,min_level=680,max_level=689,},
[35714]={task_id=35714,min_level=680,max_level=689,},
[35715]={task_id=35715,},
[35716]={task_id=35716,},
[35717]={task_id=35717,},
[35718]={task_id=35718,},
[35751]={task_id=35751,min_level=690,max_level=699,},
[35752]={task_id=35752,min_level=690,max_level=699,},
[35753]={task_id=35753,min_level=690,max_level=699,},
[35754]={task_id=35754,min_level=690,max_level=699,},
[35755]={task_id=35755,min_level=690,max_level=699,},
[35756]={task_id=35756,min_level=690,max_level=699,},
[35757]={task_id=35757,min_level=690,max_level=699,},
[35758]={task_id=35758,min_level=690,max_level=699,},
[35759]={task_id=35759,min_level=690,max_level=699,},
[35760]={task_id=35760,min_level=690,max_level=699,},
[35761]={task_id=35761,min_level=690,max_level=699,},
[35762]={task_id=35762,min_level=690,max_level=699,},
[35763]={task_id=35763,min_level=690,max_level=699,},
[35764]={task_id=35764,min_level=690,max_level=699,},
[35765]={task_id=35765,},
[35766]={task_id=35766,},
[35767]={task_id=35767,},
[35768]={task_id=35768,},
[35801]={task_id=35801,min_level=700,max_level=709,},
[35802]={task_id=35802,min_level=700,max_level=709,},
[35803]={task_id=35803,min_level=700,max_level=709,},
[35804]={task_id=35804,min_level=700,max_level=709,},
[35805]={task_id=35805,min_level=700,max_level=709,},
[35806]={task_id=35806,min_level=700,max_level=709,},
[35807]={task_id=35807,min_level=700,max_level=709,},
[35808]={task_id=35808,min_level=700,max_level=709,},
[35809]={task_id=35809,min_level=700,max_level=709,},
[35810]={task_id=35810,min_level=700,max_level=709,},
[35811]={task_id=35811,min_level=700,max_level=709,},
[35812]={task_id=35812,min_level=700,max_level=709,},
[35813]={task_id=35813,min_level=700,max_level=709,},
[35814]={task_id=35814,min_level=700,max_level=709,},
[35815]={task_id=35815,},
[35816]={task_id=35816,},
[35817]={task_id=35817,},
[35818]={task_id=35818,},
[35851]={task_id=35851,min_level=710,max_level=719,},
[35852]={task_id=35852,min_level=710,max_level=719,},
[35853]={task_id=35853,min_level=710,max_level=719,},
[35854]={task_id=35854,min_level=710,max_level=719,},
[35855]={task_id=35855,min_level=710,max_level=719,},
[35856]={task_id=35856,min_level=710,max_level=719,},
[35857]={task_id=35857,min_level=710,max_level=719,},
[35858]={task_id=35858,min_level=710,max_level=719,},
[35859]={task_id=35859,min_level=710,max_level=719,},
[35860]={task_id=35860,min_level=710,max_level=719,},
[35861]={task_id=35861,},
[35862]={task_id=35862,min_level=710,max_level=719,},
[35863]={task_id=35863,min_level=710,max_level=719,},
[35864]={task_id=35864,min_level=710,max_level=719,},
[35865]={task_id=35865,min_level=710,max_level=719,},
[35866]={task_id=35866,},
[35867]={task_id=35867,},
[35868]={task_id=35868,},
[35901]={task_id=35901,min_level=720,max_level=729,},
[35902]={task_id=35902,min_level=720,max_level=729,},
[35903]={task_id=35903,min_level=720,max_level=729,},
[35904]={task_id=35904,min_level=720,max_level=729,},
[35905]={task_id=35905,min_level=720,max_level=729,},
[35906]={task_id=35906,min_level=720,max_level=729,},
[35907]={task_id=35907,min_level=720,max_level=729,},
[35908]={task_id=35908,min_level=720,max_level=729,},
[35909]={task_id=35909,min_level=720,max_level=729,},
[35910]={task_id=35910,min_level=720,max_level=729,},
[35911]={task_id=35911,},
[35912]={task_id=35912,min_level=720,max_level=729,},
[35913]={task_id=35913,min_level=720,max_level=729,},
[35914]={task_id=35914,min_level=720,max_level=729,},
[35915]={task_id=35915,min_level=720,max_level=729,},
[35916]={task_id=35916,},
[35917]={task_id=35917,},
[35918]={task_id=35918,},
[35951]={task_id=35951,min_level=730,max_level=739,},
[35952]={task_id=35952,min_level=730,max_level=739,},
[35953]={task_id=35953,min_level=730,max_level=739,},
[35954]={task_id=35954,min_level=730,max_level=739,},
[35955]={task_id=35955,min_level=730,max_level=739,},
[35956]={task_id=35956,min_level=730,max_level=739,},
[35957]={task_id=35957,min_level=730,max_level=739,},
[35958]={task_id=35958,min_level=730,max_level=739,},
[35959]={task_id=35959,min_level=730,max_level=739,},
[35960]={task_id=35960,min_level=730,max_level=739,},
[35961]={task_id=35961,},
[35962]={task_id=35962,min_level=730,max_level=739,},
[35963]={task_id=35963,min_level=730,max_level=739,},
[35964]={task_id=35964,min_level=730,max_level=739,},
[35965]={task_id=35965,min_level=730,max_level=739,},
[35966]={task_id=35966,},
[35967]={task_id=35967,},
[35968]={task_id=35968,},
[36001]={task_id=36001,min_level=740,max_level=749,},
[36002]={task_id=36002,min_level=740,max_level=749,},
[36003]={task_id=36003,min_level=740,max_level=749,},
[36004]={task_id=36004,min_level=740,max_level=749,},
[36005]={task_id=36005,min_level=740,max_level=749,},
[36006]={task_id=36006,min_level=740,max_level=749,},
[36007]={task_id=36007,min_level=740,max_level=749,},
[36008]={task_id=36008,min_level=740,max_level=749,},
[36009]={task_id=36009,min_level=740,max_level=749,},
[36010]={task_id=36010,min_level=740,max_level=749,},
[36011]={task_id=36011,},
[36012]={task_id=36012,min_level=740,max_level=749,},
[36013]={task_id=36013,min_level=740,max_level=749,},
[36014]={task_id=36014,min_level=740,max_level=749,},
[36015]={task_id=36015,min_level=740,max_level=749,},
[36016]={task_id=36016,},
[36017]={task_id=36017,},
[36018]={task_id=36018,},
[36051]={task_id=36051,min_level=750,max_level=759,},
[36052]={task_id=36052,min_level=750,max_level=759,},
[36053]={task_id=36053,min_level=750,max_level=759,},
[36054]={task_id=36054,min_level=750,max_level=759,},
[36055]={task_id=36055,min_level=750,max_level=759,},
[36056]={task_id=36056,min_level=750,max_level=759,},
[36057]={task_id=36057,min_level=750,max_level=759,},
[36058]={task_id=36058,min_level=750,max_level=759,},
[36059]={task_id=36059,min_level=750,max_level=759,},
[36060]={task_id=36060,min_level=750,max_level=759,},
[36061]={task_id=36061,},
[36062]={task_id=36062,min_level=750,max_level=759,},
[36063]={task_id=36063,min_level=750,max_level=759,},
[36064]={task_id=36064,},
[36065]={task_id=36065,min_level=750,max_level=759,},
[36066]={task_id=36066,},
[36067]={task_id=36067,},
[36068]={task_id=36068,min_level=750,max_level=759,},
[36101]={task_id=36101,min_level=760,max_level=769,},
[36102]={task_id=36102,min_level=760,max_level=769,},
[36103]={task_id=36103,min_level=760,max_level=769,},
[36104]={task_id=36104,min_level=760,max_level=769,},
[36105]={task_id=36105,min_level=760,max_level=769,},
[36106]={task_id=36106,min_level=760,max_level=769,},
[36107]={task_id=36107,min_level=760,max_level=769,},
[36108]={task_id=36108,min_level=760,max_level=769,},
[36109]={task_id=36109,min_level=760,max_level=769,},
[36110]={task_id=36110,min_level=760,max_level=769,},
[36111]={task_id=36111,min_level=760,max_level=769,},
[36112]={task_id=36112,},
[36113]={task_id=36113,},
[36114]={task_id=36114,min_level=760,max_level=769,},
[36115]={task_id=36115,},
[36116]={task_id=36116,min_level=760,max_level=769,},
[36117]={task_id=36117,min_level=760,max_level=769,},
[36118]={task_id=36118,},
[36151]={task_id=36151,min_level=770,max_level=779,},
[36152]={task_id=36152,min_level=770,max_level=779,},
[36153]={task_id=36153,min_level=770,max_level=779,},
[36154]={task_id=36154,min_level=770,max_level=779,},
[36155]={task_id=36155,min_level=770,max_level=779,},
[36156]={task_id=36156,min_level=770,max_level=779,},
[36157]={task_id=36157,min_level=770,max_level=779,},
[36158]={task_id=36158,min_level=770,max_level=779,},
[36159]={task_id=36159,min_level=770,max_level=779,},
[36160]={task_id=36160,min_level=770,max_level=779,},
[36161]={task_id=36161,},
[36162]={task_id=36162,min_level=770,max_level=779,},
[36163]={task_id=36163,min_level=770,max_level=779,},
[36164]={task_id=36164,min_level=770,max_level=779,},
[36165]={task_id=36165,min_level=770,max_level=779,},
[36166]={task_id=36166,},
[36167]={task_id=36167,},
[36168]={task_id=36168,},
[36201]={task_id=36201,min_level=780,max_level=789,},
[36202]={task_id=36202,min_level=780,max_level=789,},
[36203]={task_id=36203,min_level=780,max_level=789,},
[36204]={task_id=36204,min_level=780,max_level=789,},
[36205]={task_id=36205,min_level=780,max_level=789,},
[36206]={task_id=36206,min_level=780,max_level=789,},
[36207]={task_id=36207,min_level=780,max_level=789,},
[36208]={task_id=36208,min_level=780,max_level=789,},
[36209]={task_id=36209,min_level=780,max_level=789,},
[36210]={task_id=36210,min_level=780,max_level=789,},
[36211]={task_id=36211,min_level=780,max_level=789,},
[36212]={task_id=36212,min_level=780,max_level=789,},
[36213]={task_id=36213,},
[36214]={task_id=36214,min_level=780,max_level=789,},
[36215]={task_id=36215,},
[36216]={task_id=36216,},
[36217]={task_id=36217,min_level=780,max_level=789,},
[36218]={task_id=36218,},
[36251]={task_id=36251,min_level=790,max_level=799,},
[36252]={task_id=36252,min_level=790,max_level=799,},
[36253]={task_id=36253,min_level=790,max_level=799,},
[36254]={task_id=36254,min_level=790,max_level=799,},
[36255]={task_id=36255,min_level=790,max_level=799,},
[36256]={task_id=36256,min_level=790,max_level=799,},
[36257]={task_id=36257,min_level=790,max_level=799,},
[36258]={task_id=36258,min_level=790,max_level=799,},
[36259]={task_id=36259,min_level=790,max_level=799,},
[36260]={task_id=36260,min_level=790,max_level=799,},
[36261]={task_id=36261,},
[36262]={task_id=36262,min_level=790,max_level=799,},
[36263]={task_id=36263,min_level=790,max_level=799,},
[36264]={task_id=36264,min_level=790,max_level=799,},
[36265]={task_id=36265,min_level=790,max_level=799,},
[36266]={task_id=36266,},
[36267]={task_id=36267,},
[36268]={task_id=36268,},
[36301]={task_id=36301,task_name="拜访云虎",task_type=4,min_level=800,accept_npc={id=312,scene=1003,x=292,y=453},commit_npc={id=312,scene=1003,x=292,y=453},c_param4=0,accept_dialog=330011,commit_dialog=330012,accept_desc="接取仙盟任务",progress_desc="拜访云虎(<per>0/1</per>)",commit_desc="拜访云虎",Bubble_Tips="完成任务可获得丰厚奖励",},
[36302]={task_id=36302,task_name="拜访外员",commit_npc={id=10326,scene=1003,x=338,y=428},commit_dialog=330022,accept_desc="拜访钱外员",progress_desc="拜访钱外员(<per>0/1</per>)",commit_desc="拜访钱外员",},
[36303]={task_id=36303,task_type=4,min_level=800,commit_npc={id=10327,scene=1003,x=291,y=414},c_param4=0,commit_dialog=330032,accept_desc="拜访钱宅管家",progress_desc="拜访钱宅管家(<per>0/1</per>)",commit_desc="拜访钱宅管家",Bubble_Tips="完成任务可获得丰厚奖励",},
[36304]={task_id=36304,task_name="护城传说",commit_npc={id=10328,scene=1003,x=132,y=80},commit_dialog=330042,accept_desc="拜访护城灵",progress_desc="拜访护城灵(<per>0/1</per>)",commit_desc="拜访护城灵",},
[36305]={task_id=36305,task_name="神器信息",commit_npc={id=10329,scene=1003,x=88,y=105},commit_dialog=330052,accept_desc="探访神器的下落",progress_desc="探访神器的下落(<per>0/1</per>)",commit_desc="探访神器的下落",},
[36306]={task_id=36306,task_name="幻兽之主",commit_npc={id=10305,scene=1003,x=216,y=200},commit_dialog=330062,accept_desc="拜访白晶晶",progress_desc="拜访白晶晶(<per>0/1</per>)",commit_desc="拜访白晶晶",},
[36307]={task_id=36307,task_name="仙盟夜宴",commit_npc={id=10316,scene=1003,x=284,y=269},commit_dialog=330072,accept_desc="拜访凌昭华",progress_desc="拜访凌昭华(<per>0/1</per>)",commit_desc="拜访凌昭华",},
[36308]={task_id=36308,task_name="仙盟守护",commit_npc={id=10304,scene=1003,x=159,y=264},commit_dialog=330082,accept_desc="拜访云渊停",progress_desc="拜访云渊停(<per>0/1</per>)",commit_desc="拜访云渊停",},
[36309]={task_id=36309,task_name="仙盟布阵",commit_npc={id=10332,scene=1003,x=271,y=179},commit_dialog=330092,accept_desc="拜访张德",progress_desc="拜访张德(<per>0/1</per>)",commit_desc="拜访张德",},
[36310]={task_id=36310,task_name="山海异兽",commit_npc={id=10336,scene=1003,x=401,y=97},commit_dialog=330102,accept_desc="拜访炼丹师，了解山海经",progress_desc="拜访炼丹师，了解山海经(<per>0/1</per>)",commit_desc="拜访炼丹师，了解山海经",},
[36311]={task_id=36311,task_type=4,min_level=800,accept_npc={id=10327,scene=1003,x=291,y=414},condition=3,c_param1=42,c_param2=1,c_param3=21,c_param4=0,accept_dialog=330111,accept_desc="钱宅管家的委托",Bubble_Tips="完成任务可获得丰厚奖励",target_obj={},},
[36312]={task_id=36312,},
[36313]={task_id=36313,},
[36314]={task_id=36314,},
[36315]={task_id=36315,},
[36316]={task_id=36316,task_name="力挫江天问",accept_npc={id=10333,scene=1003,x=175,y=178},c_param3=22,accept_dialog=330121,accept_desc="友好切磋",progress_desc="友好切磋(<per>0/1</per>)",commit_desc="友好切磋",},
[36317]={task_id=36317,task_name="月老试炼",accept_npc={id=10312,scene=1003,x=402,y=69},c_param3=23,accept_dialog=330131,accept_desc="拜访月老",progress_desc="寻找真凶(<per>0/1</per>)",commit_desc="寻找真凶",},
[36318]={task_id=36318,task_name="护送切磋",accept_npc={id=10313,scene=1003,x=405,y=93},c_param3=24,accept_dialog=330141,accept_desc="加入护送队伍不是那么容易的",progress_desc="加入护送队伍不是那么容易的(<per>0/1</per>)",commit_desc="加入护送队伍不是那么容易的",},
[40001]={task_id=40001,task_name="了解详情",accept_npc={id=10314,scene=1003,x=278,y=324},condition=3,c_param1=42,c_param2=1,c_param3=27,exp_factor=6,accept_dialog=21038,accept_desc="钱外员的请求",progress_desc="前往员外府",commit_desc="心头旧事",chapter_desc="心头旧事",Bubble_Tips="完成日常任务快速升级",target_obj={},},
[40005]={task_id=40005,accept_npc={id=10350,scene=1003,x=291,y=418},commit_npc={id=10350,scene=1003,x=291,y=418},},
[40006]={task_id=40006,accept_npc={id=10347,scene=1003,x=342,y=404},},
[40007]={task_id=40007,accept_npc={id=10347,scene=1003,x=342,y=404},},
[40008]={task_id=40008,task_name="故人相认",commit_npc={id=10332,scene=1003,x=271,y=179},c_param3=21,accept_dialog=21011,commit_dialog=21012,accept_desc="魔族横行",progress_desc="清扫魔族(<per>0/5</per>)",commit_desc="再次相见",chapter_desc="再次相见",},
[40009]={task_id=40009,},
[40010]={task_id=40010,},
[40012]={task_id=40012,},
[40013]={task_id=40013,},
[40014]={task_id=40014,},
[40015]={task_id=40015,},
[40016]={task_id=40016,},
[40017]={task_id=40017,},
[40018]={task_id=40018,},
[40019]={task_id=40019,},
[40020]={task_id=40020,},
[40021]={task_id=40021,},
[40022]={task_id=40022,task_name="清理魔窟",c_param3=24,accept_dialog=21026,accept_desc="清理魔窟",progress_desc="清理魔窟(<per>0/5</per>)",commit_desc="清理魔窟",chapter_desc="清理魔窟",},
[40023]={task_id=40023,},
[40024]={task_id=40024,},
[40025]={task_id=40025,},
[40027]={task_id=40027,},
[40028]={task_id=40028,},
[40029]={task_id=40029,},
[40030]={task_id=40030,},
[40031]={task_id=40031,},
[40032]={task_id=40032,task_name="清理鬼怪",accept_npc={id=10338,scene=1003,x=201,y=419},condition=3,c_param1=42,c_param2=1,c_param3=26,exp_factor=6,accept_dialog=21047,accept_desc="清理鬼怪",progress_desc="清理鬼怪(<per>0/5</per>)",commit_desc="清理鬼怪",chapter_desc="清理鬼怪",Bubble_Tips="完成日常任务快速升级",target_obj={},},
[40033]={task_id=40033,},
[40034]={task_id=40034,},
[40035]={task_id=40035,task_name="来生再见",accept_npc={id=10339,scene=1003,x=235,y=416},commit_npc={id=10338,scene=1003,x=201,y=419},condition=2,c_param1=2072,c_param2=1,exp_factor=6,accept_dialog=21050,commit_dialog=21051,accept_desc="来生再见",progress_desc="来生再见",commit_desc="来生再见",chapter_desc="来生再见",Bubble_Tips="完成日常任务快速升级",target_obj={{id=2072,scene=1003,x=235,y=415},},},
[40036]={task_id=40036,},
[40101]={task_id=40101,},
[40102]={task_id=40102,},
[40103]={task_id=40103,task_name="探寻魔窟",accept_npc={id=10347,scene=1003,x=342,y=404},condition=3,c_param1=42,c_param2=1,c_param3=24,exp_factor=6,accept_dialog=210401,accept_desc="探寻魔窟",progress_desc="击败魔窟守卫(<per>0/10</per>)",commit_desc="继续前进",chapter_desc="继续前进",Bubble_Tips="完成日常任务快速升级",target_obj={},},
[40104]={task_id=40104,},
[40105]={task_id=40105,},
[40106]={task_id=40106,},
[40107]={task_id=40107,},
[40108]={task_id=40108,},
[40109]={task_id=40109,task_name="前往琅琊",accept_npc={id=10333,scene=1003,x=175,y=178},condition=3,c_param1=42,c_param2=1,c_param3=22,exp_factor=6,accept_dialog=21013,accept_desc="寻路江宅",progress_desc="战胜江天问",commit_desc="战胜江天问",chapter_desc="战胜江天问",Bubble_Tips="完成日常任务快速升级",target_obj={},},
[40110]={task_id=40110,},
[40112]={task_id=40112,},
[40113]={task_id=40113,},
[40114]={task_id=40114,},
[40115]={task_id=40115,},
[40116]={task_id=40116,},
[40117]={task_id=40117,},
[40118]={task_id=40118,},
[40119]={task_id=40119,},
[40120]={task_id=40120,},
[40121]={task_id=40121,task_name="义薄云天",accept_npc={id=10336,scene=1003,x=401,y=97},condition=2,c_param1=2070,c_param2=1,exp_factor=6,accept_dialog=21025,accept_desc="义薄云天",progress_desc="义薄云天",commit_desc="义薄云天",chapter_desc="义薄云天",Bubble_Tips="完成日常任务快速升级",target_obj={{id=2070,scene=1003,x=228,y=367},},},
[40122]={task_id=40122,},
[40123]={task_id=40123,},
[40124]={task_id=40124,},
[40125]={task_id=40125,},
[40127]={task_id=40127,},
[40128]={task_id=40128,},
[40129]={task_id=40129,},
[40130]={task_id=40130,task_name="路见不平",accept_npc={id=10338,scene=1003,x=201,y=419},commit_npc={id=10338,scene=1003,x=201,y=419},exp_factor=6,accept_dialog=21045,commit_dialog=21046,accept_desc="路见不平",progress_desc="路见不平",commit_desc="路见不平",chapter_desc="路见不平",Bubble_Tips="完成日常任务快速升级",},
[40131]={task_id=40131,task_name="坏人何处",condition=2,c_param1=2078,c_param2=1,exp_factor=6,accept_desc="坏人何处",progress_desc="附近调查(<per>0/1</per>)",commit_desc="坏人何处",chapter_desc="坏人何处",Bubble_Tips="完成日常任务快速升级",target_obj={{id=2078,scene=1003,x=209,y=416},},},
[40132]={task_id=40132,},
[40133]={task_id=40133,task_name="似是故人",accept_npc={id=10339,scene=1003,x=235,y=416},commit_npc={id=10338,scene=1003,x=201,y=419},exp_factor=6,accept_dialog=21048,commit_dialog=21053,accept_desc="似是故人",progress_desc="似是故人",commit_desc="似是故人",chapter_desc="似是故人",Bubble_Tips="完成日常任务快速升级",},
[40134]={task_id=40134,task_name="往生",accept_npc={id=10339,scene=1003,x=235,y=416},condition=2,c_param1=2072,c_param2=1,exp_factor=6,accept_dialog=21049,accept_desc="往生",progress_desc="往生",commit_desc="往生",chapter_desc="往生",Bubble_Tips="完成日常任务快速升级",target_obj={{id=2072,scene=1003,x=235,y=415},},},
[40135]={task_id=40135,},
[40136]={task_id=40136,},
[40201]={task_id=40201,task_name="了解详情",accept_npc={id=10302,scene=1003,x=307,y=308},commit_npc={id=10347,scene=1003,x=342,y=404},exp_factor=6,accept_dialog=21038,commit_dialog=21039,accept_desc="钱外员的请求",progress_desc="前往员外府",commit_desc="心头旧事",chapter_desc="心头旧事",Bubble_Tips="完成日常任务快速升级",},
[40202]={task_id=40202,task_name="往事回首",accept_dialog=21040,commit_dialog=210400,accept_desc="回忆往事",progress_desc="回忆往事(<per>0/1</per>)",commit_desc="回忆往事",chapter_desc="回忆往事",},
[40203]={task_id=40203,},
[40204]={task_id=40204,task_name="物归原主",accept_dialog=21041,commit_dialog=210410,accept_desc="物归原主",progress_desc="物归原主",commit_desc="物归原主",chapter_desc="物归原主",},
[40205]={task_id=40205,task_name="宝剑英雄",accept_npc={id=10347,scene=1003,x=342,y=404},commit_npc={id=10347,scene=1003,x=342,y=404},exp_factor=6,accept_dialog=21042,commit_dialog=210420,accept_desc="宝剑英雄",progress_desc="宝剑英雄",commit_desc="宝剑英雄",chapter_desc="宝剑英雄",Bubble_Tips="完成日常任务快速升级",},
[40206]={task_id=40206,task_name="意外之喜",accept_npc={id=10326,scene=1003,x=338,y=428},commit_npc={id=10326,scene=1003,x=338,y=428},exp_factor=6,accept_dialog=21043,commit_dialog=210430,accept_desc="意外之喜",progress_desc="意外之喜",commit_desc="意外之喜",chapter_desc="意外之喜",Bubble_Tips="完成日常任务快速升级",},
[40207]={task_id=40207,task_name="归去来兮",accept_dialog=21044,commit_dialog=210440,accept_desc="归去来兮",progress_desc="归去来兮",commit_desc="归去来兮",chapter_desc="归去来兮",},
[40208]={task_id=40208,},
[40209]={task_id=40209,},
[40210]={task_id=40210,task_name="身世之谜",accept_npc={id=10333,scene=1003,x=175,y=178},commit_npc={id=10333,scene=1003,x=175,y=178},exp_factor=6,accept_dialog=21014,commit_dialog=21015,accept_desc="身世之谜",progress_desc="身世之谜",commit_desc="身世之谜",chapter_desc="身世之谜",Bubble_Tips="完成日常任务快速升级",},
[40212]={task_id=40212,task_name="悯众生",accept_npc={id=10334,scene=1003,x=179,y=184},commit_npc={id=10333,scene=1003,x=175,y=178},exp_factor=6,accept_dialog=21016,commit_dialog=21055,accept_desc="悯众生",progress_desc="悯众生",commit_desc="悯众生",chapter_desc="悯众生",Bubble_Tips="完成日常任务快速升级",},
[40213]={task_id=40213,task_name="何为魔",accept_npc={id=10333,scene=1003,x=175,y=178},commit_npc={id=10335,scene=1003,x=219,y=211},exp_factor=6,accept_dialog=21017,commit_dialog=21056,accept_desc="何为魔",progress_desc="何为魔",commit_desc="何为魔",chapter_desc="何为魔",Bubble_Tips="完成日常任务快速升级",},
[40214]={task_id=40214,task_name="正邪难辨",accept_dialog=21018,commit_dialog=21057,accept_desc="正邪难辨",progress_desc="正邪难辨",commit_desc="正邪难辨",chapter_desc="正邪难辨",},
[40215]={task_id=40215,task_name="可疑人员",accept_npc={id=10347,scene=1003,x=342,y=404},commit_npc={id=10337,scene=1003,x=305,y=140},exp_factor=6,accept_dialog=21019,commit_dialog=21020,accept_desc="可疑人员",progress_desc="可疑人员",commit_desc="可疑人员",chapter_desc="可疑人员",Bubble_Tips="完成日常任务快速升级",},
[40216]={task_id=40216,task_name="调查住所",c_param1=2069,accept_desc="调查住所",progress_desc="调查住所(<per>0/1</per>)",commit_desc="调查住所",chapter_desc="调查住所",target_obj={{id=2069,scene=1003,x=426,y=116},},},
[40217]={task_id=40217,task_name="对峙",accept_npc={id=10336,scene=1003,x=401,y=97},condition=3,c_param1=42,c_param2=1,c_param3=23,exp_factor=6,accept_dialog=21021,accept_desc="对峙",progress_desc="拿下炼丹师(<per>0/1</per>)",commit_desc="拿下炼丹师",chapter_desc="拿下炼丹师",Bubble_Tips="完成日常任务快速升级",target_obj={},},
[40218]={task_id=40218,task_name="返程复命·一",accept_npc={id=10336,scene=1003,x=401,y=97},commit_npc={id=10347,scene=1003,x=342,y=404},exp_factor=6,accept_dialog=21022,commit_dialog=21023,accept_desc="返程复命",progress_desc="返程复命",commit_desc="返程复命",chapter_desc="返程复命",Bubble_Tips="完成日常任务快速升级",},
[40219]={task_id=40219,task_name="返程复命·二",commit_npc={id=10336,scene=1003,x=401,y=97},accept_dialog=21058,commit_dialog=210580,},
[40220]={task_id=40220,task_name="知错就改",accept_dialog=21024,commit_dialog=210240,accept_desc="知错就改",progress_desc="知错就改",commit_desc="知错就改",chapter_desc="知错就改",},
[40221]={task_id=40221,},
[40222]={task_id=40222,},
[40223]={task_id=40223,task_name="护城之灵",accept_npc={id=10328,scene=1003,x=132,y=80},commit_npc={id=10329,scene=1003,x=88,y=105},exp_factor=6,accept_dialog=21027,commit_dialog=21028,accept_desc="寻找护城之灵",progress_desc="寻找护城之灵",commit_desc="寻找护城之灵",chapter_desc="寻找护城之灵",Bubble_Tips="完成日常任务快速升级",},
[40224]={task_id=40224,task_name="荒废之地",c_param1=2067,accept_desc="探寻",progress_desc="探寻",commit_desc="探寻",chapter_desc="探寻",target_obj={{id=2067,scene=1003,x=114,y=101},},},
[40225]={task_id=40225,task_name="陈年往事",accept_npc={id=10328,scene=1003,x=132,y=80},commit_npc={id=10328,scene=1003,x=132,y=80},exp_factor=6,accept_dialog=21029,commit_dialog=210290,accept_desc="原路返回",progress_desc="陈年往事",commit_desc="陈年往事",chapter_desc="陈年往事",Bubble_Tips="完成日常任务快速升级",},
[40227]={task_id=40227,task_name="浮生一梦",accept_npc={id=10330,scene=1003,x=126,y=130},commit_npc={id=10328,scene=1003,x=132,y=80},accept_dialog=21030,commit_dialog=21032,accept_desc="浮生一梦",progress_desc="净化地缚灵(<per>0/1</per>)",commit_desc="浮生一梦",chapter_desc="浮生一梦",},
[40228]={task_id=40228,task_name="相见时难",accept_npc={id=10330,scene=1003,x=126,y=130},commit_npc={id=10328,scene=1003,x=132,y=80},exp_factor=6,accept_dialog=21033,commit_dialog=21034,accept_desc="相见时难",progress_desc="相见时难",commit_desc="相见时难",chapter_desc="相见时难",Bubble_Tips="完成日常任务快速升级",},
[40229]={task_id=40229,task_name="重获新生",accept_npc={id=10330,scene=1003,x=126,y=130},commit_npc={id=10328,scene=1003,x=132,y=80},condition=2,c_param1=2068,c_param2=1,exp_factor=6,accept_dialog=21035,commit_dialog=21037,accept_desc="重获新生",progress_desc="拾取心石(<per>0/1</per>)",commit_desc="重获新生",chapter_desc="重获新生",Bubble_Tips="完成日常任务快速升级",target_obj={{id=2068,scene=1003,x=124,y=128},},},
[40230]={task_id=40230,},
[40231]={task_id=40231,},
[40232]={task_id=40232,},
[40233]={task_id=40233,},
[40234]={task_id=40234,},
[40235]={task_id=40235,},
[40236]={task_id=40236,task_name="除魔卫道",accept_npc={id=10339,scene=1003,x=235,y=416},commit_npc={id=10339,scene=1003,x=235,y=416},accept_dialog=21052,commit_dialog=21054,accept_desc="除魔卫道",progress_desc="除魔卫道",commit_desc="除魔卫道",chapter_desc="除魔卫道",},
[40307]={task_id=40307,},
[40308]={task_id=40308,},
[40309]={task_id=40309,},
[40310]={task_id=40310,},
[40311]={task_id=40311,},
[40312]={task_id=40312,},
[40313]={task_id=40313,},
[40314]={task_id=40314,},
[40315]={task_id=40315,},
[40316]={task_id=40316,Bubble_Tips="完成日常任务快速升级",},
[40317]={task_id=40317,},
[40351]={task_id=40351,},
[40352]={task_id=40352,},
[40353]={task_id=40353,Bubble_Tips="完成日常任务快速升级",},
[40354]={task_id=40354,Bubble_Tips="完成日常任务快速升级",},
[40355]={task_id=40355,Bubble_Tips="完成日常任务快速升级",},
[40356]={task_id=40356,},
[40357]={task_id=40357,},
[40358]={task_id=40358,},
[40359]={task_id=40359,},
[40360]={task_id=40360,},
[40361]={task_id=40361,},
[40362]={task_id=40362,},
[40363]={task_id=40363,},
[40364]={task_id=40364,},
[40365]={task_id=40365,},
[40366]={task_id=40366,},
[40367]={task_id=40367,},
[40401]={task_id=40401,},
[40402]={task_id=40402,},
[40403]={task_id=40403,},
[40404]={task_id=40404,},
[40405]={task_id=40405,},
[40406]={task_id=40406,},
[40407]={task_id=40407,},
[40408]={task_id=40408,},
[40409]={task_id=40409,},
[40410]={task_id=40410,},
[40411]={task_id=40411,},
[40412]={task_id=40412,},
[40413]={task_id=40413,},
[40414]={task_id=40414,},
[40415]={task_id=40415,},
[40416]={task_id=40416,},
[40417]={task_id=40417,},
[40451]={task_id=40451,},
[40452]={task_id=40452,},
[40453]={task_id=40453,},
[40454]={task_id=40454,},
[40455]={task_id=40455,},
[40456]={task_id=40456,},
[40457]={task_id=40457,},
[40458]={task_id=40458,},
[40459]={task_id=40459,},
[40460]={task_id=40460,},
[40461]={task_id=40461,},
[40462]={task_id=40462,},
[40463]={task_id=40463,},
[40464]={task_id=40464,},
[40465]={task_id=40465,},
[40466]={task_id=40466,},
[40467]={task_id=40467,},
[40501]={task_id=40501,},
[40502]={task_id=40502,},
[40503]={task_id=40503,},
[40504]={task_id=40504,},
[40505]={task_id=40505,},
[40506]={task_id=40506,Bubble_Tips="完成日常任务快速升级",},
[40507]={task_id=40507,Bubble_Tips="完成日常任务快速升级",},
[40508]={task_id=40508,},
[40509]={task_id=40509,},
[40510]={task_id=40510,},
[40511]={task_id=40511,},
[40512]={task_id=40512,},
[40513]={task_id=40513,},
[40514]={task_id=40514,},
[40515]={task_id=40515,},
[40516]={task_id=40516,},
[40517]={task_id=40517,Bubble_Tips="完成日常任务快速升级",},
[40551]={task_id=40551,Bubble_Tips="完成日常任务快速升级",},
[40552]={task_id=40552,Bubble_Tips="完成日常任务快速升级",},
[40553]={task_id=40553,},
[40554]={task_id=40554,},
[40555]={task_id=40555,},
[40556]={task_id=40556,},
[40557]={task_id=40557,},
[40558]={task_id=40558,Bubble_Tips="完成日常任务快速升级",},
[40559]={task_id=40559,Bubble_Tips="完成日常任务快速升级",},
[40560]={task_id=40560,},
[40561]={task_id=40561,},
[40562]={task_id=40562,},
[40563]={task_id=40563,},
[40564]={task_id=40564,Bubble_Tips="完成日常任务快速升级",},
[40565]={task_id=40565,Bubble_Tips="完成日常任务快速升级",},
[40566]={task_id=40566,},
[40567]={task_id=40567,},
[40601]={task_id=40601,},
[40602]={task_id=40602,},
[40603]={task_id=40603,},
[40604]={task_id=40604,},
[40605]={task_id=40605,},
[40606]={task_id=40606,},
[40607]={task_id=40607,},
[40608]={task_id=40608,},
[40609]={task_id=40609,},
[40610]={task_id=40610,Bubble_Tips="完成日常任务快速升级",},
[40611]={task_id=40611,Bubble_Tips="完成日常任务快速升级",},
[40612]={task_id=40612,Bubble_Tips="完成日常任务快速升级",},
[40613]={task_id=40613,Bubble_Tips="完成日常任务快速升级",},
[40614]={task_id=40614,},
[40615]={task_id=40615,},
[40616]={task_id=40616,},
[40617]={task_id=40617,},
[40651]={task_id=40651,},
[40652]={task_id=40652,},
[40653]={task_id=40653,},
[40654]={task_id=40654,},
[40655]={task_id=40655,},
[40656]={task_id=40656,},
[40657]={task_id=40657,task_name="笨拙伞女",accept_npc={id=10326,scene=1003,x=338,y=428},condition=2,c_param1=314,c_param2=1,exp_factor=6,accept_dialog=400071,accept_desc="拜访卖伞女",progress_desc="帮卖伞女清点货物(<per>0/1</per>)",commit_desc="帮卖伞女清点货物",chapter_desc="拜访卖伞女",target_obj={{id=314,scene=1003,x=242,y=181},},},
[40658]={task_id=40658,},
[40659]={task_id=40659,},
[40660]={task_id=40660,},
[40661]={task_id=40661,},
[40662]={task_id=40662,},
[40663]={task_id=40663,},
[40664]={task_id=40664,},
[40665]={task_id=40665,},
[40666]={task_id=40666,},
[40667]={task_id=40667,},
[40701]={task_id=40701,},
[40702]={task_id=40702,},
[40703]={task_id=40703,},
[40704]={task_id=40704,},
[40705]={task_id=40705,},
[40706]={task_id=40706,},
[40707]={task_id=40707,},
[40708]={task_id=40708,},
[40709]={task_id=40709,},
[40710]={task_id=40710,},
[40711]={task_id=40711,},
[40712]={task_id=40712,},
[40713]={task_id=40713,},
[40714]={task_id=40714,},
[40715]={task_id=40715,},
[40716]={task_id=40716,},
[40717]={task_id=40717,},
[40751]={task_id=40751,},
[40752]={task_id=40752,},
[40753]={task_id=40753,},
[40754]={task_id=40754,},
[40755]={task_id=40755,},
[40756]={task_id=40756,},
[40757]={task_id=40757,},
[40758]={task_id=40758,},
[40759]={task_id=40759,},
[40760]={task_id=40760,},
[40761]={task_id=40761,},
[40762]={task_id=40762,},
[40763]={task_id=40763,},
[40764]={task_id=40764,},
[40765]={task_id=40765,},
[40766]={task_id=40766,},
[40767]={task_id=40767,},
[40801]={task_id=40801,},
[40802]={task_id=40802,},
[40803]={task_id=40803,},
[40804]={task_id=40804,},
[40805]={task_id=40805,},
[40806]={task_id=40806,},
[40807]={task_id=40807,},
[40808]={task_id=40808,},
[40809]={task_id=40809,},
[40810]={task_id=40810,},
[40811]={task_id=40811,},
[40812]={task_id=40812,},
[40813]={task_id=40813,},
[40814]={task_id=40814,},
[40815]={task_id=40815,},
[40816]={task_id=40816,},
[40817]={task_id=40817,},
[40851]={task_id=40851,},
[40852]={task_id=40852,},
[40853]={task_id=40853,},
[40854]={task_id=40854,},
[40855]={task_id=40855,},
[40856]={task_id=40856,},
[40857]={task_id=40857,},
[40858]={task_id=40858,},
[40859]={task_id=40859,},
[40860]={task_id=40860,},
[40861]={task_id=40861,},
[40862]={task_id=40862,},
[40863]={task_id=40863,},
[40864]={task_id=40864,},
[40865]={task_id=40865,},
[40866]={task_id=40866,},
[40867]={task_id=40867,},
[40901]={task_id=40901,},
[40902]={task_id=40902,task_name="四季酒家",commit_npc={id=10325,scene=1003,x=219,y=347},commit_dialog=400022,accept_desc="拜访四季酒家老板",progress_desc="拜访四季酒家老板(<per>0/1</per>)",commit_desc="拜访四季酒家老板",chapter_desc="拜访四季酒家老板",},
[40903]={task_id=40903,task_name="得道成仙",commit_npc={id=10326,scene=1003,x=338,y=428},commit_dialog=400032,accept_desc="拜访云虎",progress_desc="打探得道成仙的传闻(<per>0/1</per>)",commit_desc="打探得道成仙的传闻",chapter_desc="拜访云虎",},
[40904]={task_id=40904,task_name="兰陵军神",commit_npc={id=10327,scene=1003,x=291,y=414},commit_dialog=400042,accept_desc="拜访军神",progress_desc="拜访军神(<per>0/1</per>)",commit_desc="拜访军神",chapter_desc="拜访军神",},
[40905]={task_id=40905,task_name="五术转职",commit_npc={id=10328,scene=1003,x=132,y=80},commit_dialog=400052,accept_desc="拜访五术修士",progress_desc="拜访五术修士(<per>0/1</per>)",commit_desc="拜访五术修士",chapter_desc="拜访五术修士",},
[40906]={task_id=40906,},
[40907]={task_id=40907,},
[40908]={task_id=40908,},
[40909]={task_id=40909,},
[40910]={task_id=40910,},
[40911]={task_id=40911,},
[40912]={task_id=40912,},
[40913]={task_id=40913,},
[40914]={task_id=40914,},
[40915]={task_id=40915,},
[40916]={task_id=40916,},
[40917]={task_id=40917,},
[40951]={task_id=40951,},
[40952]={task_id=40952,},
[40953]={task_id=40953,},
[40954]={task_id=40954,},
[40955]={task_id=40955,},
[40956]={task_id=40956,},
[40957]={task_id=40957,},
[40958]={task_id=40958,},
[40959]={task_id=40959,},
[40960]={task_id=40960,},
[40961]={task_id=40961,},
[40962]={task_id=40962,},
[40963]={task_id=40963,},
[40964]={task_id=40964,},
[40965]={task_id=40965,},
[40966]={task_id=40966,},
[40967]={task_id=40967,},
[41001]={task_id=41001,},
[41002]={task_id=41002,},
[41003]={task_id=41003,},
[41004]={task_id=41004,},
[41005]={task_id=41005,},
[41006]={task_id=41006,},
[41007]={task_id=41007,},
[41008]={task_id=41008,},
[41009]={task_id=41009,},
[41010]={task_id=41010,},
[41011]={task_id=41011,},
[41012]={task_id=41012,},
[41013]={task_id=41013,},
[41014]={task_id=41014,},
[41015]={task_id=41015,},
[41016]={task_id=41016,},
[41017]={task_id=41017,},
[41051]={task_id=41051,},
[41052]={task_id=41052,},
[41053]={task_id=41053,},
[41054]={task_id=41054,},
[41055]={task_id=41055,},
[41056]={task_id=41056,},
[41057]={task_id=41057,},
[41058]={task_id=41058,},
[41059]={task_id=41059,},
[41060]={task_id=41060,},
[41061]={task_id=41061,},
[41062]={task_id=41062,},
[41063]={task_id=41063,},
[41064]={task_id=41064,},
[41065]={task_id=41065,},
[41066]={task_id=41066,},
[41067]={task_id=41067,},
[41101]={task_id=41101,},
[41102]={task_id=41102,},
[41103]={task_id=41103,},
[41104]={task_id=41104,},
[41105]={task_id=41105,},
[41106]={task_id=41106,},
[41107]={task_id=41107,},
[41108]={task_id=41108,},
[41109]={task_id=41109,},
[41110]={task_id=41110,},
[41111]={task_id=41111,},
[41112]={task_id=41112,},
[41113]={task_id=41113,},
[41114]={task_id=41114,},
[41115]={task_id=41115,},
[41116]={task_id=41116,},
[41117]={task_id=41117,},
[41151]={task_id=41151,},
[41152]={task_id=41152,},
[41153]={task_id=41153,},
[41154]={task_id=41154,},
[41155]={task_id=41155,},
[41156]={task_id=41156,},
[41157]={task_id=41157,},
[41158]={task_id=41158,},
[41159]={task_id=41159,},
[41160]={task_id=41160,},
[41161]={task_id=41161,},
[41162]={task_id=41162,},
[41163]={task_id=41163,},
[41164]={task_id=41164,},
[41165]={task_id=41165,},
[41166]={task_id=41166,},
[41167]={task_id=41167,},
[41201]={task_id=41201,},
[41202]={task_id=41202,},
[41203]={task_id=41203,},
[41204]={task_id=41204,},
[41205]={task_id=41205,},
[41206]={task_id=41206,},
[41207]={task_id=41207,},
[41208]={task_id=41208,},
[41209]={task_id=41209,},
[41210]={task_id=41210,},
[41211]={task_id=41211,},
[41212]={task_id=41212,},
[41213]={task_id=41213,},
[41214]={task_id=41214,},
[41215]={task_id=41215,},
[41216]={task_id=41216,},
[41217]={task_id=41217,},
[41251]={task_id=41251,},
[41252]={task_id=41252,},
[41253]={task_id=41253,},
[41254]={task_id=41254,},
[41255]={task_id=41255,},
[41256]={task_id=41256,},
[41257]={task_id=41257,},
[41258]={task_id=41258,},
[41259]={task_id=41259,},
[41260]={task_id=41260,},
[41261]={task_id=41261,},
[41262]={task_id=41262,},
[41263]={task_id=41263,},
[41264]={task_id=41264,},
[41265]={task_id=41265,},
[41266]={task_id=41266,},
[41267]={task_id=41267,},
[41301]={task_id=41301,},
[41302]={task_id=41302,},
[41303]={task_id=41303,},
[41304]={task_id=41304,},
[41305]={task_id=41305,},
[41306]={task_id=41306,},
[41307]={task_id=41307,},
[41308]={task_id=41308,},
[41309]={task_id=41309,},
[41310]={task_id=41310,},
[41311]={task_id=41311,},
[41312]={task_id=41312,},
[41313]={task_id=41313,},
[41314]={task_id=41314,},
[41315]={task_id=41315,},
[41316]={task_id=41316,},
[41317]={task_id=41317,},
[41351]={task_id=41351,},
[41352]={task_id=41352,},
[41353]={task_id=41353,},
[41354]={task_id=41354,},
[41355]={task_id=41355,},
[41356]={task_id=41356,},
[41357]={task_id=41357,},
[41358]={task_id=41358,},
[41359]={task_id=41359,},
[41360]={task_id=41360,},
[41361]={task_id=41361,},
[41362]={task_id=41362,},
[41363]={task_id=41363,},
[41364]={task_id=41364,},
[41365]={task_id=41365,},
[41366]={task_id=41366,},
[41367]={task_id=41367,},
[41401]={task_id=41401,},
[41402]={task_id=41402,},
[41403]={task_id=41403,},
[41404]={task_id=41404,},
[41405]={task_id=41405,},
[41406]={task_id=41406,},
[41407]={task_id=41407,},
[41408]={task_id=41408,},
[41409]={task_id=41409,},
[41410]={task_id=41410,},
[41411]={task_id=41411,},
[41412]={task_id=41412,},
[41413]={task_id=41413,},
[41414]={task_id=41414,},
[41415]={task_id=41415,},
[41416]={task_id=41416,},
[41417]={task_id=41417,},
[41451]={task_id=41451,},
[41452]={task_id=41452,},
[41453]={task_id=41453,},
[41454]={task_id=41454,},
[41455]={task_id=41455,},
[41456]={task_id=41456,},
[41457]={task_id=41457,},
[41458]={task_id=41458,},
[41459]={task_id=41459,},
[41460]={task_id=41460,},
[41461]={task_id=41461,},
[41462]={task_id=41462,},
[41463]={task_id=41463,},
[41464]={task_id=41464,},
[41465]={task_id=41465,},
[41466]={task_id=41466,},
[41467]={task_id=41467,},
[41501]={task_id=41501,},
[41502]={task_id=41502,},
[41503]={task_id=41503,},
[41504]={task_id=41504,},
[41505]={task_id=41505,},
[41506]={task_id=41506,},
[41507]={task_id=41507,},
[41508]={task_id=41508,},
[41509]={task_id=41509,},
[41510]={task_id=41510,},
[41511]={task_id=41511,},
[41512]={task_id=41512,},
[41513]={task_id=41513,},
[41514]={task_id=41514,},
[41515]={task_id=41515,},
[41516]={task_id=41516,},
[41517]={task_id=41517,},
[41551]={task_id=41551,},
[41552]={task_id=41552,},
[41553]={task_id=41553,},
[41554]={task_id=41554,},
[41555]={task_id=41555,},
[41556]={task_id=41556,},
[41557]={task_id=41557,},
[41558]={task_id=41558,},
[41559]={task_id=41559,},
[41560]={task_id=41560,},
[41561]={task_id=41561,},
[41562]={task_id=41562,},
[41563]={task_id=41563,},
[41564]={task_id=41564,},
[41565]={task_id=41565,},
[41566]={task_id=41566,},
[41567]={task_id=41567,},
[41601]={task_id=41601,},
[41602]={task_id=41602,},
[41603]={task_id=41603,},
[41604]={task_id=41604,},
[41605]={task_id=41605,},
[41606]={task_id=41606,},
[41607]={task_id=41607,},
[41608]={task_id=41608,},
[41609]={task_id=41609,},
[41610]={task_id=41610,},
[41611]={task_id=41611,},
[41612]={task_id=41612,},
[41613]={task_id=41613,},
[41614]={task_id=41614,},
[41615]={task_id=41615,},
[41616]={task_id=41616,},
[41617]={task_id=41617,},
[41651]={task_id=41651,},
[41652]={task_id=41652,},
[41653]={task_id=41653,},
[41654]={task_id=41654,},
[41655]={task_id=41655,},
[41656]={task_id=41656,},
[41657]={task_id=41657,},
[41658]={task_id=41658,},
[41659]={task_id=41659,},
[41660]={task_id=41660,},
[41661]={task_id=41661,},
[41662]={task_id=41662,},
[41663]={task_id=41663,},
[41664]={task_id=41664,},
[41665]={task_id=41665,},
[41666]={task_id=41666,},
[41667]={task_id=41667,},
[41701]={task_id=41701,},
[41702]={task_id=41702,},
[41703]={task_id=41703,},
[41704]={task_id=41704,},
[41705]={task_id=41705,},
[41706]={task_id=41706,},
[41707]={task_id=41707,},
[41708]={task_id=41708,},
[41709]={task_id=41709,},
[41710]={task_id=41710,},
[41711]={task_id=41711,},
[41712]={task_id=41712,},
[41713]={task_id=41713,},
[41714]={task_id=41714,},
[41715]={task_id=41715,},
[41716]={task_id=41716,},
[41717]={task_id=41717,},
[41751]={task_id=41751,},
[41752]={task_id=41752,},
[41753]={task_id=41753,},
[41754]={task_id=41754,},
[41755]={task_id=41755,},
[41756]={task_id=41756,},
[41757]={task_id=41757,},
[41758]={task_id=41758,},
[41759]={task_id=41759,},
[41760]={task_id=41760,},
[41761]={task_id=41761,},
[41762]={task_id=41762,},
[41763]={task_id=41763,},
[41764]={task_id=41764,},
[41765]={task_id=41765,},
[41766]={task_id=41766,},
[41767]={task_id=41767,},
[41801]={task_id=41801,},
[41802]={task_id=41802,},
[41803]={task_id=41803,},
[41804]={task_id=41804,},
[41805]={task_id=41805,},
[41806]={task_id=41806,},
[41807]={task_id=41807,},
[41808]={task_id=41808,},
[41809]={task_id=41809,},
[41810]={task_id=41810,},
[41811]={task_id=41811,},
[41812]={task_id=41812,},
[41813]={task_id=41813,},
[41814]={task_id=41814,},
[41815]={task_id=41815,},
[41816]={task_id=41816,},
[41817]={task_id=41817,},
[41851]={task_id=41851,},
[41852]={task_id=41852,},
[41853]={task_id=41853,},
[41854]={task_id=41854,},
[41855]={task_id=41855,},
[41856]={task_id=41856,},
[41857]={task_id=41857,},
[41858]={task_id=41858,},
[41859]={task_id=41859,},
[41860]={task_id=41860,},
[41861]={task_id=41861,},
[41862]={task_id=41862,},
[41863]={task_id=41863,},
[41864]={task_id=41864,},
[41865]={task_id=41865,},
[41866]={task_id=41866,},
[41867]={task_id=41867,},
[41901]={task_id=41901,},
[41902]={task_id=41902,},
[41903]={task_id=41903,},
[41904]={task_id=41904,},
[41905]={task_id=41905,},
[41906]={task_id=41906,},
[41907]={task_id=41907,},
[41908]={task_id=41908,},
[41909]={task_id=41909,},
[41910]={task_id=41910,},
[41911]={task_id=41911,},
[41912]={task_id=41912,},
[41913]={task_id=41913,},
[41914]={task_id=41914,},
[41915]={task_id=41915,},
[41916]={task_id=41916,},
[41917]={task_id=41917,},
[41951]={task_id=41951,},
[41952]={task_id=41952,},
[41953]={task_id=41953,},
[41954]={task_id=41954,},
[41955]={task_id=41955,},
[41956]={task_id=41956,},
[41957]={task_id=41957,},
[41958]={task_id=41958,},
[41959]={task_id=41959,},
[41960]={task_id=41960,},
[41961]={task_id=41961,},
[41962]={task_id=41962,},
[41963]={task_id=41963,},
[41964]={task_id=41964,},
[41965]={task_id=41965,},
[41966]={task_id=41966,},
[41967]={task_id=41967,},
[42001]={task_id=42001,},
[42002]={task_id=42002,},
[42003]={task_id=42003,},
[42004]={task_id=42004,},
[42005]={task_id=42005,},
[42006]={task_id=42006,},
[42007]={task_id=42007,},
[42008]={task_id=42008,},
[42009]={task_id=42009,},
[42010]={task_id=42010,},
[42011]={task_id=42011,},
[42012]={task_id=42012,},
[42013]={task_id=42013,},
[42014]={task_id=42014,},
[42015]={task_id=42015,},
[42016]={task_id=42016,},
[42017]={task_id=42017,},
[42051]={task_id=42051,task_name="珍兽奇观",accept_npc={id=10302,scene=1003,x=307,y=308},commit_npc={id=10324,scene=1003,x=487,y=284},exp_factor=6,accept_dialog=400011,commit_dialog=400012,accept_desc="前往兰陵城拜访珍兽店老板",progress_desc="前往兰陵城拜访珍兽店老板(<per>0/1</per>)",commit_desc="前往兰陵城拜访珍兽店老板",chapter_desc="前往兰陵城拜访珍兽店老板",},
[42052]={task_id=42052,},
[42053]={task_id=42053,},
[42054]={task_id=42054,},
[42055]={task_id=42055,},
[42056]={task_id=42056,},
[42057]={task_id=42057,},
[42058]={task_id=42058,},
[42059]={task_id=42059,},
[42060]={task_id=42060,task_name="清除恶灵",accept_npc={id=10327,scene=1003,x=291,y=414},condition=3,c_param1=42,c_param2=1,c_param3=29,exp_factor=6,accept_dialog=400101,accept_desc="拜访皇城禁卫",progress_desc="清除捣乱魔族(<per>0/1</per>)",commit_desc="清除捣乱魔族",chapter_desc="拜访皇城禁卫",target_obj={},},
[42061]={task_id=42061,task_name="维持治安",c_param3=25,accept_dialog=400111,accept_desc="拜访四季酒家老板",progress_desc="制止捣乱的客人(<per>0/1</per>)",commit_desc="制止捣乱的客人",chapter_desc="拜访四季酒家老板",},
[42062]={task_id=42062,task_name="切磋镇龙",c_param3=26,accept_dialog=400121,accept_desc="拜访镇龙",progress_desc="与镇龙切磋(<per>0/1</per>)",commit_desc="与镇龙切磋",chapter_desc="拜访镇龙",},
[42063]={task_id=42063,task_name="云虎修炼",accept_npc={id=10329,scene=1003,x=88,y=105},condition=3,c_param1=42,c_param2=1,c_param3=27,exp_factor=6,accept_dialog=400131,accept_desc="拜访云虎",progress_desc="完成云虎的试炼(<per>0/1</per>)",commit_desc="完成云虎的试炼",chapter_desc="拜访云虎",target_obj={},},
[42064]={task_id=42064,},
[42065]={task_id=42065,},
[42066]={task_id=42066,},
[42067]={task_id=42067,},
[42101]={task_id=42101,},
[42102]={task_id=42102,},
[42103]={task_id=42103,},
[42104]={task_id=42104,},
[42105]={task_id=42105,},
[42106]={task_id=42106,},
[42107]={task_id=42107,},
[42108]={task_id=42108,},
[42109]={task_id=42109,},
[42110]={task_id=42110,},
[42111]={task_id=42111,},
[42112]={task_id=42112,},
[42113]={task_id=42113,},
[42114]={task_id=42114,},
[42115]={task_id=42115,},
[42116]={task_id=42116,},
[42117]={task_id=42117,},
[42151]={task_id=42151,},
[42152]={task_id=42152,},
[42153]={task_id=42153,},
[42154]={task_id=42154,},
[42155]={task_id=42155,},
[42156]={task_id=42156,},
[42157]={task_id=42157,},
[42158]={task_id=42158,},
[42159]={task_id=42159,},
[42160]={task_id=42160,},
[42161]={task_id=42161,},
[42162]={task_id=42162,},
[42163]={task_id=42163,},
[42164]={task_id=42164,},
[42165]={task_id=42165,},
[42166]={task_id=42166,},
[42167]={task_id=42167,},
[42201]={task_id=42201,},
[42202]={task_id=42202,},
[42203]={task_id=42203,},
[42204]={task_id=42204,},
[42205]={task_id=42205,},
[42206]={task_id=42206,},
[42207]={task_id=42207,},
[42208]={task_id=42208,},
[42209]={task_id=42209,},
[42210]={task_id=42210,},
[42211]={task_id=42211,},
[42212]={task_id=42212,},
[42213]={task_id=42213,},
[42214]={task_id=42214,},
[42215]={task_id=42215,task_name="禁卫传话",accept_npc={id=10332,scene=1003,x=271,y=179},commit_npc={id=10333,scene=1003,x=175,y=178},exp_factor=6,accept_dialog=400151,commit_dialog=400152,accept_desc="拜访兰陵禁卫",progress_desc="找到城门守卫并传达口信(<per>0/1</per>)",commit_desc="找到城门守卫并传达口",chapter_desc="拜访兰陵禁卫",},
[42216]={task_id=42216,},
[42217]={task_id=42217,},
[42251]={task_id=42251,},
[42252]={task_id=42252,},
[42253]={task_id=42253,},
[42254]={task_id=42254,},
[42255]={task_id=42255,},
[42256]={task_id=42256,},
[42257]={task_id=42257,},
[42258]={task_id=42258,},
[42259]={task_id=42259,},
[42260]={task_id=42260,},
[42261]={task_id=42261,},
[42262]={task_id=42262,},
[42263]={task_id=42263,},
[42264]={task_id=42264,},
[42265]={task_id=42265,},
[42266]={task_id=42266,},
[42267]={task_id=42267,},
[42301]={task_id=42301,},
[42302]={task_id=42302,},
[42303]={task_id=42303,},
[42304]={task_id=42304,},
[42305]={task_id=42305,},
[42306]={task_id=42306,},
[42307]={task_id=42307,},
[42308]={task_id=42308,},
[42309]={task_id=42309,},
[42310]={task_id=42310,},
[42311]={task_id=42311,},
[42312]={task_id=42312,},
[42313]={task_id=42313,},
[42314]={task_id=42314,},
[42315]={task_id=42315,},
[42316]={task_id=42316,},
[42317]={task_id=42317,},
[42351]={task_id=42351,},
[42352]={task_id=42352,},
[42353]={task_id=42353,},
[42354]={task_id=42354,},
[42355]={task_id=42355,},
[42356]={task_id=42356,},
[42357]={task_id=42357,},
[42358]={task_id=42358,},
[42359]={task_id=42359,},
[42360]={task_id=42360,},
[42361]={task_id=42361,},
[42362]={task_id=42362,},
[42363]={task_id=42363,},
[42364]={task_id=42364,},
[42365]={task_id=42365,},
[42366]={task_id=42366,},
[42367]={task_id=42367,task_name="奇怪的人",accept_npc={id=10333,scene=1003,x=175,y=178},commit_npc={id=10334,scene=1003,x=179,y=184},exp_factor=6,commit_dialog=400172,accept_desc="寻找人们说的奇怪的人",progress_desc="找到奇怪的人(<per>0/1</per>)",commit_desc="找到奇怪的人",chapter_desc="寻找人们说的奇怪的人",},
[42401]={task_id=42401,},
[42402]={task_id=42402,},
[42403]={task_id=42403,},
[42404]={task_id=42404,},
[42405]={task_id=42405,},
[42406]={task_id=42406,},
[42407]={task_id=42407,},
[42408]={task_id=42408,},
[42409]={task_id=42409,},
[42410]={task_id=42410,},
[42411]={task_id=42411,},
[42412]={task_id=42412,},
[42413]={task_id=42413,},
[42414]={task_id=42414,},
[42415]={task_id=42415,},
[42416]={task_id=42416,},
[42417]={task_id=42417,},
[42451]={task_id=42451,},
[42452]={task_id=42452,},
[42453]={task_id=42453,},
[42454]={task_id=42454,},
[42455]={task_id=42455,},
[42456]={task_id=42456,},
[42457]={task_id=42457,},
[42458]={task_id=42458,},
[42459]={task_id=42459,},
[42460]={task_id=42460,},
[42461]={task_id=42461,},
[42462]={task_id=42462,},
[42463]={task_id=42463,},
[42464]={task_id=42464,},
[42465]={task_id=42465,},
[42466]={task_id=42466,},
[42467]={task_id=42467,},
[42501]={task_id=42501,},
[42502]={task_id=42502,},
[42503]={task_id=42503,},
[42504]={task_id=42504,},
[42505]={task_id=42505,},
[42506]={task_id=42506,},
[42507]={task_id=42507,},
[42508]={task_id=42508,},
[42509]={task_id=42509,},
[42510]={task_id=42510,},
[42511]={task_id=42511,},
[42512]={task_id=42512,},
[42513]={task_id=42513,},
[42514]={task_id=42514,},
[42515]={task_id=42515,},
[42516]={task_id=42516,},
[42517]={task_id=42517,},
[42551]={task_id=42551,},
[42552]={task_id=42552,},
[42553]={task_id=42553,},
[42554]={task_id=42554,},
[42555]={task_id=42555,},
[42556]={task_id=42556,},
[42557]={task_id=42557,},
[42558]={task_id=42558,},
[42559]={task_id=42559,},
[42560]={task_id=42560,},
[42561]={task_id=42561,},
[42562]={task_id=42562,},
[42563]={task_id=42563,},
[42564]={task_id=42564,},
[42565]={task_id=42565,},
[42566]={task_id=42566,},
[42567]={task_id=42567,},
[42601]={task_id=42601,},
[42602]={task_id=42602,},
[42603]={task_id=42603,},
[42604]={task_id=42604,},
[42605]={task_id=42605,},
[42606]={task_id=42606,},
[42607]={task_id=42607,},
[42608]={task_id=42608,},
[42609]={task_id=42609,},
[42610]={task_id=42610,},
[42611]={task_id=42611,},
[42612]={task_id=42612,},
[42613]={task_id=42613,},
[42614]={task_id=42614,},
[42615]={task_id=42615,},
[42616]={task_id=42616,},
[42617]={task_id=42617,},
[42651]={task_id=42651,},
[42652]={task_id=42652,},
[42653]={task_id=42653,},
[42654]={task_id=42654,},
[42655]={task_id=42655,},
[42656]={task_id=42656,},
[42657]={task_id=42657,},
[42658]={task_id=42658,},
[42659]={task_id=42659,},
[42660]={task_id=42660,},
[42661]={task_id=42661,},
[42662]={task_id=42662,},
[42663]={task_id=42663,},
[42664]={task_id=42664,},
[42665]={task_id=42665,},
[42666]={task_id=42666,},
[42667]={task_id=42667,},
[42701]={task_id=42701,},
[42702]={task_id=42702,},
[42703]={task_id=42703,},
[42704]={task_id=42704,},
[42705]={task_id=42705,},
[42706]={task_id=42706,},
[42707]={task_id=42707,},
[42708]={task_id=42708,},
[42709]={task_id=42709,},
[42710]={task_id=42710,},
[42711]={task_id=42711,},
[42712]={task_id=42712,},
[42713]={task_id=42713,},
[42714]={task_id=42714,},
[42715]={task_id=42715,},
[42716]={task_id=42716,},
[42717]={task_id=42717,},
[42751]={task_id=42751,},
[42752]={task_id=42752,},
[42753]={task_id=42753,},
[42754]={task_id=42754,},
[42755]={task_id=42755,},
[42756]={task_id=42756,},
[42757]={task_id=42757,},
[42758]={task_id=42758,},
[42759]={task_id=42759,},
[42760]={task_id=42760,},
[42761]={task_id=42761,},
[42762]={task_id=42762,},
[42763]={task_id=42763,},
[42764]={task_id=42764,},
[42765]={task_id=42765,},
[42766]={task_id=42766,task_name="话痨市民",commit_npc={id=10331,scene=1003,x=294,y=196},exp_factor=6,commit_dialog=400162,accept_desc="拜访话痨市民",progress_desc="听话痨市民埋怨(<per>0/1</per>)",commit_desc="听话痨市民埋怨",chapter_desc="拜访话痨市民",},
[42767]={task_id=42767,},
[42801]={task_id=42801,},
[42802]={task_id=42802,},
[42803]={task_id=42803,},
[42804]={task_id=42804,},
[42805]={task_id=42805,},
[42806]={task_id=42806,},
[42807]={task_id=42807,},
[42808]={task_id=42808,},
[42809]={task_id=42809,},
[42810]={task_id=42810,},
[42811]={task_id=42811,},
[42812]={task_id=42812,},
[42813]={task_id=42813,},
[42814]={task_id=42814,},
[42815]={task_id=42815,},
[42816]={task_id=42816,},
[42817]={task_id=42817,},
[42851]={task_id=42851,},
[42852]={task_id=42852,},
[42853]={task_id=42853,},
[42854]={task_id=42854,},
[42855]={task_id=42855,},
[42856]={task_id=42856,},
[42857]={task_id=42857,},
[42858]={task_id=42858,},
[42859]={task_id=42859,},
[42860]={task_id=42860,},
[42861]={task_id=42861,},
[42862]={task_id=42862,},
[42863]={task_id=42863,},
[42864]={task_id=42864,},
[42865]={task_id=42865,},
[42866]={task_id=42866,},
[42867]={task_id=42867,},
[42901]={task_id=42901,},
[42902]={task_id=42902,},
[42903]={task_id=42903,},
[42904]={task_id=42904,},
[42905]={task_id=42905,},
[42906]={task_id=42906,},
[42907]={task_id=42907,},
[42908]={task_id=42908,},
[42909]={task_id=42909,},
[42910]={task_id=42910,},
[42911]={task_id=42911,},
[42912]={task_id=42912,},
[42913]={task_id=42913,},
[42914]={task_id=42914,},
[42915]={task_id=42915,},
[42916]={task_id=42916,},
[42917]={task_id=42917,},
[42951]={task_id=42951,},
[42952]={task_id=42952,},
[42953]={task_id=42953,},
[42954]={task_id=42954,},
[42955]={task_id=42955,},
[42956]={task_id=42956,},
[42957]={task_id=42957,},
[42958]={task_id=42958,},
[42959]={task_id=42959,},
[42960]={task_id=42960,},
[42961]={task_id=42961,},
[42962]={task_id=42962,},
[42963]={task_id=42963,},
[42964]={task_id=42964,},
[42965]={task_id=42965,},
[42966]={task_id=42966,},
[42967]={task_id=42967,},
[43001]={task_id=43001,},
[43002]={task_id=43002,},
[43003]={task_id=43003,},
[43004]={task_id=43004,},
[43005]={task_id=43005,},
[43006]={task_id=43006,},
[43007]={task_id=43007,},
[43008]={task_id=43008,},
[43009]={task_id=43009,},
[43010]={task_id=43010,},
[43011]={task_id=43011,},
[43012]={task_id=43012,},
[43013]={task_id=43013,},
[43014]={task_id=43014,},
[43015]={task_id=43015,},
[43016]={task_id=43016,},
[43017]={task_id=43017,},
[43051]={task_id=43051,},
[43052]={task_id=43052,},
[43053]={task_id=43053,},
[43054]={task_id=43054,},
[43055]={task_id=43055,},
[43056]={task_id=43056,},
[43057]={task_id=43057,},
[43058]={task_id=43058,},
[43059]={task_id=43059,},
[43060]={task_id=43060,},
[43061]={task_id=43061,},
[43062]={task_id=43062,},
[43063]={task_id=43063,},
[43064]={task_id=43064,},
[43065]={task_id=43065,},
[43066]={task_id=43066,},
[43067]={task_id=43067,},
[43101]={task_id=43101,},
[43102]={task_id=43102,},
[43103]={task_id=43103,},
[43104]={task_id=43104,},
[43105]={task_id=43105,},
[43106]={task_id=43106,},
[43107]={task_id=43107,},
[43108]={task_id=43108,},
[43109]={task_id=43109,},
[43110]={task_id=43110,},
[43111]={task_id=43111,},
[43112]={task_id=43112,},
[43113]={task_id=43113,},
[43114]={task_id=43114,task_name="云幽长老",accept_npc={id=10331,scene=1003,x=294,y=196},condition=3,c_param1=42,c_param2=1,c_param3=28,exp_factor=6,accept_dialog=400141,accept_desc="拜访云幽长老",progress_desc="完成云幽长老的试炼(<per>0/1</per>)",commit_desc="完成云幽长老的试炼",chapter_desc="拜访云幽长老",target_obj={},},
[43115]={task_id=43115,},
[43116]={task_id=43116,},
[43117]={task_id=43117,},
[43151]={task_id=43151,},
[43152]={task_id=43152,},
[43153]={task_id=43153,},
[43154]={task_id=43154,},
[43155]={task_id=43155,},
[43156]={task_id=43156,},
[43157]={task_id=43157,},
[43158]={task_id=43158,},
[43159]={task_id=43159,},
[43160]={task_id=43160,},
[43161]={task_id=43161,},
[43162]={task_id=43162,},
[43163]={task_id=43163,},
[43164]={task_id=43164,},
[43165]={task_id=43165,},
[43166]={task_id=43166,},
[43167]={task_id=43167,},
[43201]={task_id=43201,},
[43202]={task_id=43202,},
[43203]={task_id=43203,},
[43204]={task_id=43204,},
[43205]={task_id=43205,},
[43206]={task_id=43206,},
[43207]={task_id=43207,},
[43208]={task_id=43208,},
[43209]={task_id=43209,},
[43210]={task_id=43210,},
[43211]={task_id=43211,},
[43212]={task_id=43212,},
[43213]={task_id=43213,},
[43214]={task_id=43214,},
[43215]={task_id=43215,},
[43216]={task_id=43216,},
[43217]={task_id=43217,},
[43251]={task_id=43251,},
[43252]={task_id=43252,},
[43253]={task_id=43253,},
[43254]={task_id=43254,},
[43255]={task_id=43255,},
[43256]={task_id=43256,},
[43257]={task_id=43257,},
[43258]={task_id=43258,},
[43259]={task_id=43259,},
[43260]={task_id=43260,},
[43261]={task_id=43261,},
[43262]={task_id=43262,},
[43263]={task_id=43263,},
[43264]={task_id=43264,},
[43265]={task_id=43265,},
[43266]={task_id=43266,},
[43267]={task_id=43267,},
[43301]={task_id=43301,},
[43302]={task_id=43302,},
[43303]={task_id=43303,},
[43304]={task_id=43304,},
[43305]={task_id=43305,},
[43306]={task_id=43306,},
[43307]={task_id=43307,},
[43308]={task_id=43308,},
[43309]={task_id=43309,},
[43310]={task_id=43310,},
[43311]={task_id=43311,},
[43312]={task_id=43312,},
[43313]={task_id=43313,},
[43314]={task_id=43314,},
[43315]={task_id=43315,},
[43316]={task_id=43316,},
[43317]={task_id=43317,},
[43351]={task_id=43351,},
[43352]={task_id=43352,},
[43353]={task_id=43353,},
[43354]={task_id=43354,},
[43355]={task_id=43355,},
[43356]={task_id=43356,},
[43357]={task_id=43357,},
[43358]={task_id=43358,},
[43359]={task_id=43359,},
[43360]={task_id=43360,},
[43361]={task_id=43361,},
[43362]={task_id=43362,},
[43363]={task_id=43363,},
[43364]={task_id=43364,},
[43365]={task_id=43365,},
[43366]={task_id=43366,},
[43367]={task_id=43367,},
[43401]={task_id=43401,},
[43402]={task_id=43402,},
[43403]={task_id=43403,},
[43404]={task_id=43404,},
[43405]={task_id=43405,},
[43406]={task_id=43406,},
[43407]={task_id=43407,},
[43408]={task_id=43408,},
[43409]={task_id=43409,},
[43410]={task_id=43410,},
[43411]={task_id=43411,},
[43412]={task_id=43412,},
[43413]={task_id=43413,},
[43414]={task_id=43414,},
[43415]={task_id=43415,},
[43416]={task_id=43416,},
[43417]={task_id=43417,},
[43451]={task_id=43451,},
[43452]={task_id=43452,},
[43453]={task_id=43453,},
[43454]={task_id=43454,},
[43455]={task_id=43455,},
[43456]={task_id=43456,},
[43457]={task_id=43457,},
[43458]={task_id=43458,},
[43459]={task_id=43459,task_name="灯笼维修",accept_npc={id=10329,scene=1003,x=88,y=105},commit_npc={id=10330,scene=1003,x=126,y=130},condition=2,c_param1=314,c_param2=1,exp_factor=6,accept_dialog=400091,commit_dialog=400092,accept_desc="拜访杂货铺",progress_desc="购买灯笼并送到客栈(<per>0/1</per>)",commit_desc="购买灯笼并送到客栈",chapter_desc="拜访杂货铺",target_obj={{id=314,scene=1003,x=242,y=181},},},
[43460]={task_id=43460,},
[43461]={task_id=43461,},
[43462]={task_id=43462,},
[43463]={task_id=43463,},
[43464]={task_id=43464,},
[43465]={task_id=43465,},
[43466]={task_id=43466,},
[43467]={task_id=43467,},
[43501]={task_id=43501,},
[43502]={task_id=43502,},
[43503]={task_id=43503,},
[43504]={task_id=43504,},
[43505]={task_id=43505,},
[43506]={task_id=43506,},
[43507]={task_id=43507,},
[43508]={task_id=43508,},
[43509]={task_id=43509,},
[43510]={task_id=43510,},
[43511]={task_id=43511,},
[43512]={task_id=43512,},
[43513]={task_id=43513,},
[43514]={task_id=43514,},
[43515]={task_id=43515,},
[43516]={task_id=43516,},
[43517]={task_id=43517,},
[43551]={task_id=43551,},
[43552]={task_id=43552,},
[43553]={task_id=43553,},
[43554]={task_id=43554,},
[43555]={task_id=43555,},
[43556]={task_id=43556,},
[43557]={task_id=43557,},
[43558]={task_id=43558,},
[43559]={task_id=43559,},
[43560]={task_id=43560,},
[43561]={task_id=43561,},
[43562]={task_id=43562,},
[43563]={task_id=43563,},
[43564]={task_id=43564,},
[43565]={task_id=43565,},
[43566]={task_id=43566,},
[43567]={task_id=43567,},
[43601]={task_id=43601,},
[43602]={task_id=43602,},
[43603]={task_id=43603,},
[43604]={task_id=43604,},
[43605]={task_id=43605,},
[43606]={task_id=43606,},
[43607]={task_id=43607,},
[43608]={task_id=43608,task_name="外送服务",accept_npc={id=10325,scene=1003,x=219,y=347},commit_npc={id=10329,scene=1003,x=88,y=105},condition=2,c_param1=313,c_param2=1,exp_factor=6,accept_dialog=400081,commit_dialog=400082,accept_desc="拜访豆腐店",progress_desc="将商品送到四季酒家(<per>0/1</per>)",commit_desc="将商品送到四季酒家",chapter_desc="拜访豆腐店",target_obj={{id=313,scene=1003,x=179,y=133},},},
[43609]={task_id=43609,},
[43610]={task_id=43610,},
[43611]={task_id=43611,},
[43612]={task_id=43612,},
[43613]={task_id=43613,},
[43614]={task_id=43614,},
[43615]={task_id=43615,},
[43616]={task_id=43616,},
[43617]={task_id=43617,},
[43651]={task_id=43651,},
[43652]={task_id=43652,},
[43653]={task_id=43653,},
[43654]={task_id=43654,},
[43655]={task_id=43655,},
[43656]={task_id=43656,task_name="绝世好茶",accept_npc={id=10325,scene=1003,x=219,y=347},condition=2,c_param1=313,c_param2=1,exp_factor=6,accept_dialog=400061,accept_desc="拜访茶铺店家",progress_desc="品尝信阳毛尖(<per>0/1</per>)",commit_desc="品尝信阳毛尖",chapter_desc="拜访茶铺店家",target_obj={{id=313,scene=1003,x=179,y=133},},},
[43657]={task_id=43657,},
[43658]={task_id=43658,},
[43659]={task_id=43659,},
[43660]={task_id=43660,},
[43661]={task_id=43661,},
[43662]={task_id=43662,},
[43663]={task_id=43663,},
[43664]={task_id=43664,},
[43665]={task_id=43665,},
[43666]={task_id=43666,},
[43667]={task_id=43667,},
[50000]={task_id=50000,task_name="悬赏任务",min_level=89,accept_npc={id=10302,scene=1003,x=307,y=308},commit_npc={},exp_factor=6,accept_desc="前往接取悬赏任务",progress_desc="前往接取悬赏任务",commit_desc="前往接取悬赏任务",chapter_desc="前往接取悬赏任务",},
[32002]={task_id=32002,task_name="修真任务",task_type=10,min_level=110,showitem_0_1="90050,1",showitem_0_2="90050,1",showitem_0_3="90050,1",showitem_1_1="90050,1",showitem_1_2="90050,1",showitem_1_3="90050,1",showitem_0_4="90050,1",showitem_1_4="90050,1",accept_desc="",progress_desc="",commit_desc="",open_panel_name="fubenpanel#fubenpanel_welkin",is_first="",},
[51000]={task_id=51000,task_name="刺探任务",},
[51001]={task_id=51001,task_name="本服刺探",task_type=11,open_love=2000,min_level=2000,condition=1,c_param1=10909,c_param2=25,accept_desc="",progress_desc="",commit_desc="",open_panel_name="fubenpanel#fubenpanel_welkin",is_first="",target_obj={},},
[52000]={task_id=52000,task_name="护送灵兽",task_type=13,open_love=2000,min_level=2000,camp="",condition=7,c_param1=10,c_param2=3,c_param4=0,c_param5=0,coin_bind=100000,progress_desc="护送<per>0/3</per> 次",commit_desc="护送3次",open_panel_name="YunbiaoView",is_first="",target_obj={},},
[52001]={task_id=52001,task_name="委托任务",task_type=14,open_love=2000,min_level=2000,condition=24,c_param4=0,c_param5=0,exp_factor=6,accept_desc="委托任务可接",progress_desc="委托任务完成",commit_desc="任务一次",open_panel_name="assignmentView",chapter_desc="前往委托任务",is_first="",target_obj={},}
},

task_list_meta_table_map={
[40902]=42766,	-- depth:1
[40903]=42766,	-- depth:1
[40904]=42766,	-- depth:1
[40905]=42766,	-- depth:1
[41604]=40904,	-- depth:2
[41605]=40905,	-- depth:2
[42916]=42766,	-- depth:1
[42952]=40902,	-- depth:2
[41316]=42916,	-- depth:2
[42953]=40903,	-- depth:2
[42955]=41605,	-- depth:3
[42155]=42955,	-- depth:4
[42154]=41604,	-- depth:3
[41855]=42155,	-- depth:5
[42153]=42953,	-- depth:3
[42152]=42952,	-- depth:3
[41854]=42154,	-- depth:4
[41853]=42153,	-- depth:4
[42954]=41854,	-- depth:5
[42905]=41855,	-- depth:6
[42904]=42954,	-- depth:6
[42903]=41853,	-- depth:5
[41903]=42903,	-- depth:6
[40866]=41316,	-- depth:3
[42205]=42905,	-- depth:7
[41902]=42152,	-- depth:4
[42204]=42904,	-- depth:7
[41866]=40866,	-- depth:4
[42203]=41903,	-- depth:7
[42816]=41866,	-- depth:5
[42202]=41902,	-- depth:5
[42852]=42202,	-- depth:6
[42853]=42203,	-- depth:8
[42854]=42204,	-- depth:8
[42855]=42205,	-- depth:8
[41166]=42816,	-- depth:6
[42166]=41166,	-- depth:7
[41602]=42852,	-- depth:7
[41603]=42853,	-- depth:9
[42866]=42166,	-- depth:8
[42902]=41602,	-- depth:8
[41155]=42855,	-- depth:9
[40766]=42866,	-- depth:9
[42966]=40766,	-- depth:10
[43003]=41603,	-- depth:10
[43102]=42902,	-- depth:9
[43103]=43003,	-- depth:11
[43104]=42854,	-- depth:9
[43105]=41155,	-- depth:10
[41116]=42966,	-- depth:11
[40652]=43102,	-- depth:10
[40653]=43103,	-- depth:12
[42103]=40653,	-- depth:13
[42104]=43104,	-- depth:10
[42102]=40652,	-- depth:11
[40655]=43105,	-- depth:11
[40705]=40655,	-- depth:12
[43116]=41116,	-- depth:12
[42066]=43116,	-- depth:13
[43152]=42102,	-- depth:12
[43153]=42103,	-- depth:14
[43154]=42104,	-- depth:11
[43155]=40705,	-- depth:13
[40654]=43154,	-- depth:12
[43066]=42066,	-- depth:14
[42105]=43155,	-- depth:14
[41152]=43152,	-- depth:13
[43004]=40654,	-- depth:13
[43005]=42105,	-- depth:15
[41852]=41152,	-- depth:14
[42116]=43066,	-- depth:15
[41816]=42116,	-- depth:16
[41352]=41852,	-- depth:15
[41353]=43153,	-- depth:15
[41354]=43004,	-- depth:14
[43016]=41816,	-- depth:17
[41252]=41352,	-- depth:16
[41355]=43005,	-- depth:16
[43052]=41252,	-- depth:17
[43053]=41353,	-- depth:16
[43054]=41354,	-- depth:15
[43055]=41355,	-- depth:17
[40716]=43016,	-- depth:18
[40916]=40716,	-- depth:19
[41154]=43054,	-- depth:16
[41153]=43053,	-- depth:17
[43002]=43052,	-- depth:18
[42805]=43055,	-- depth:18
[42804]=41154,	-- depth:17
[42803]=41153,	-- depth:18
[42503]=42803,	-- depth:19
[42504]=42804,	-- depth:18
[42505]=42805,	-- depth:19
[42316]=40916,	-- depth:20
[41554]=42504,	-- depth:19
[41555]=42505,	-- depth:20
[42516]=42316,	-- depth:21
[42552]=43002,	-- depth:19
[42502]=42552,	-- depth:20
[42553]=42503,	-- depth:20
[42555]=41555,	-- depth:21
[41516]=42516,	-- depth:22
[42305]=42555,	-- depth:22
[42304]=41554,	-- depth:20
[42303]=42553,	-- depth:21
[42302]=42502,	-- depth:21
[42566]=41516,	-- depth:23
[42266]=42566,	-- depth:24
[42554]=42304,	-- depth:21
[42466]=42266,	-- depth:25
[41216]=42466,	-- depth:26
[42352]=42302,	-- depth:22
[41254]=42554,	-- depth:22
[42366]=41216,	-- depth:27
[41255]=42305,	-- depth:23
[42402]=42352,	-- depth:23
[42403]=42303,	-- depth:22
[42404]=41254,	-- depth:23
[42405]=41255,	-- depth:24
[40804]=42404,	-- depth:24
[40805]=42405,	-- depth:25
[42355]=40805,	-- depth:26
[42416]=42366,	-- depth:28
[42354]=40804,	-- depth:25
[42452]=42402,	-- depth:24
[42453]=42403,	-- depth:23
[42454]=42354,	-- depth:26
[42455]=42355,	-- depth:27
[41552]=42452,	-- depth:25
[41553]=42453,	-- depth:24
[42353]=41553,	-- depth:25
[42602]=41552,	-- depth:26
[42603]=42353,	-- depth:26
[42604]=42454,	-- depth:27
[42605]=42455,	-- depth:28
[42705]=42605,	-- depth:29
[42252]=42602,	-- depth:27
[40855]=42705,	-- depth:30
[41566]=42416,	-- depth:29
[42216]=41566,	-- depth:30
[41205]=40855,	-- depth:31
[41204]=42604,	-- depth:28
[42716]=42216,	-- depth:31
[40803]=42603,	-- depth:27
[42752]=42252,	-- depth:28
[42753]=40803,	-- depth:28
[42754]=41204,	-- depth:29
[42755]=41205,	-- depth:32
[41203]=42753,	-- depth:29
[41202]=42752,	-- depth:29
[40802]=41202,	-- depth:30
[41905]=42755,	-- depth:33
[41904]=42754,	-- depth:30
[42802]=40802,	-- depth:31
[42704]=41904,	-- depth:31
[40704]=42704,	-- depth:32
[42703]=41203,	-- depth:30
[42253]=42703,	-- depth:31
[41952]=42802,	-- depth:32
[40816]=42716,	-- depth:32
[40852]=41952,	-- depth:33
[40853]=42253,	-- depth:32
[40854]=40704,	-- depth:33
[42616]=40816,	-- depth:33
[41266]=42616,	-- depth:34
[42652]=40852,	-- depth:34
[42653]=40853,	-- depth:33
[42654]=40854,	-- depth:34
[42655]=41905,	-- depth:34
[41916]=41266,	-- depth:35
[41302]=42652,	-- depth:35
[41303]=42653,	-- depth:34
[41304]=42654,	-- depth:35
[41305]=42655,	-- depth:35
[42255]=41305,	-- depth:36
[42254]=41304,	-- depth:36
[42666]=41916,	-- depth:36
[42702]=41302,	-- depth:36
[41805]=42255,	-- depth:37
[41253]=41303,	-- depth:35
[43454]=42254,	-- depth:37
[43366]=42666,	-- depth:37
[43603]=41253,	-- depth:36
[43602]=42702,	-- depth:37
[41416]=43366,	-- depth:38
[41403]=43603,	-- depth:37
[43402]=43602,	-- depth:38
[43604]=43454,	-- depth:38
[43403]=41403,	-- depth:38
[43405]=41805,	-- depth:38
[42005]=43405,	-- depth:39
[42004]=43604,	-- depth:39
[43566]=41416,	-- depth:39
[41066]=43566,	-- depth:40
[42003]=43403,	-- depth:39
[43404]=42004,	-- depth:40
[42002]=43402,	-- depth:39
[41402]=42002,	-- depth:40
[41016]=41066,	-- depth:41
[43316]=41016,	-- depth:42
[41666]=43316,	-- depth:43
[41052]=41402,	-- depth:41
[43352]=41052,	-- depth:42
[40666]=41666,	-- depth:44
[43354]=43404,	-- depth:41
[43605]=42005,	-- depth:40
[43355]=43605,	-- depth:41
[41755]=43355,	-- depth:42
[41754]=43354,	-- depth:42
[41753]=42003,	-- depth:40
[41752]=43352,	-- depth:43
[41103]=41753,	-- depth:41
[41102]=41752,	-- depth:44
[41654]=41754,	-- depth:43
[41452]=41102,	-- depth:45
[40753]=41103,	-- depth:42
[41966]=40666,	-- depth:45
[41655]=41755,	-- depth:43
[43552]=41452,	-- depth:46
[41405]=41655,	-- depth:44
[41503]=40753,	-- depth:43
[41502]=43552,	-- depth:47
[43466]=41966,	-- depth:46
[41404]=41654,	-- depth:44
[43516]=43466,	-- depth:47
[43502]=41502,	-- depth:48
[43503]=41503,	-- depth:44
[43504]=41404,	-- depth:45
[43505]=41405,	-- depth:45
[41705]=43505,	-- depth:46
[41954]=43504,	-- depth:46
[41953]=43503,	-- depth:45
[40754]=41954,	-- depth:47
[41005]=41705,	-- depth:47
[41003]=41953,	-- depth:46
[43416]=43516,	-- depth:48
[41702]=43502,	-- depth:49
[41703]=41003,	-- depth:47
[43452]=41702,	-- depth:50
[40755]=41005,	-- depth:48
[41704]=40754,	-- depth:48
[41004]=41704,	-- depth:49
[43555]=40755,	-- depth:49
[40952]=43452,	-- depth:51
[43554]=41004,	-- depth:50
[43455]=43555,	-- depth:50
[41716]=43416,	-- depth:49
[41002]=40952,	-- depth:52
[43553]=41703,	-- depth:48
[43453]=43553,	-- depth:49
[41453]=43453,	-- depth:50
[43353]=41453,	-- depth:51
[41454]=43554,	-- depth:51
[42052]=41002,	-- depth:53
[42053]=43353,	-- depth:52
[40702]=42052,	-- depth:54
[40703]=42053,	-- depth:53
[42054]=41454,	-- depth:52
[41466]=41716,	-- depth:50
[43255]=43455,	-- depth:51
[40966]=41466,	-- depth:51
[43253]=40703,	-- depth:54
[43252]=40702,	-- depth:55
[42055]=43255,	-- depth:52
[41055]=42055,	-- depth:53
[43216]=40966,	-- depth:52
[41054]=42054,	-- depth:53
[41766]=43216,	-- depth:53
[41504]=41054,	-- depth:54
[41053]=43253,	-- depth:55
[40953]=41053,	-- depth:56
[40954]=41504,	-- depth:55
[40752]=43252,	-- depth:56
[43166]=41766,	-- depth:54
[41505]=41055,	-- depth:54
[43202]=40752,	-- depth:57
[41616]=43166,	-- depth:55
[43666]=41616,	-- depth:56
[43204]=40954,	-- depth:56
[43205]=41505,	-- depth:55
[40955]=43205,	-- depth:56
[41804]=43204,	-- depth:57
[41803]=40953,	-- depth:57
[41802]=43202,	-- depth:58
[43203]=41803,	-- depth:58
[43655]=40955,	-- depth:57
[43254]=41804,	-- depth:58
[41955]=43655,	-- depth:58
[41366]=43666,	-- depth:57
[43616]=41366,	-- depth:58
[41105]=41955,	-- depth:59
[43305]=41105,	-- depth:60
[43304]=43254,	-- depth:59
[43303]=43203,	-- depth:59
[43302]=41802,	-- depth:59
[41104]=43304,	-- depth:60
[41653]=43303,	-- depth:60
[41652]=43302,	-- depth:60
[42016]=43616,	-- depth:59
[41455]=43305,	-- depth:61
[43652]=41652,	-- depth:61
[43653]=41653,	-- depth:61
[43654]=41104,	-- depth:61
[43266]=42016,	-- depth:60
[40353]=40903,	-- depth:2
[40667]=42367,	-- depth:1
[40552]=40902,	-- depth:2
[40354]=40904,	-- depth:2
[41467]=40667,	-- depth:2
[40355]=40905,	-- depth:2
[40403]=40353,	-- depth:3
[40553]=40403,	-- depth:4
[40554]=40354,	-- depth:3
[43517]=41467,	-- depth:3
[42467]=43517,	-- depth:4
[41917]=42467,	-- depth:5
[42317]=41917,	-- depth:6
[43667]=42317,	-- depth:7
[41267]=43667,	-- depth:8
[41417]=41267,	-- depth:9
[40404]=40554,	-- depth:4
[40605]=40355,	-- depth:3
[43567]=41417,	-- depth:10
[42267]=43567,	-- depth:11
[41017]=42267,	-- depth:12
[41217]=41017,	-- depth:13
[42567]=41217,	-- depth:14
[41667]=42567,	-- depth:15
[40405]=40605,	-- depth:4
[42517]=41667,	-- depth:16
[40352]=40552,	-- depth:3
[42417]=42517,	-- depth:17
[40602]=40352,	-- depth:4
[40316]=42766,	-- depth:1
[40817]=42417,	-- depth:18
[40402]=40602,	-- depth:5
[41517]=40817,	-- depth:19
[43617]=41517,	-- depth:20
[40366]=40316,	-- depth:2
[40603]=40553,	-- depth:5
[42617]=43617,	-- depth:21
[40516]=40366,	-- depth:3
[40604]=40404,	-- depth:5
[43017]=42617,	-- depth:22
[40505]=40405,	-- depth:5
[43367]=43017,	-- depth:23
[40867]=43367,	-- depth:24
[42867]=40867,	-- depth:25
[40967]=42867,	-- depth:26
[40555]=40505,	-- depth:6
[40452]=40402,	-- depth:6
[42917]=40967,	-- depth:27
[40453]=40603,	-- depth:6
[40503]=40453,	-- depth:7
[43317]=42917,	-- depth:28
[40502]=40452,	-- depth:7
[40454]=40604,	-- depth:6
[41367]=43317,	-- depth:29
[40466]=40516,	-- depth:4
[42967]=41367,	-- depth:30
[42117]=42967,	-- depth:31
[43267]=42117,	-- depth:32
[40717]=43267,	-- depth:33
[43167]=40717,	-- depth:34
[43117]=43167,	-- depth:35
[42067]=43117,	-- depth:36
[41767]=42067,	-- depth:37
[43067]=41767,	-- depth:38
[42167]=43067,	-- depth:39
[43217]=42167,	-- depth:40
[40917]=43217,	-- depth:41
[40566]=40466,	-- depth:5
[41617]=40917,	-- depth:42
[42017]=41617,	-- depth:43
[40455]=40555,	-- depth:7
[41817]=42017,	-- depth:44
[41117]=41817,	-- depth:45
[42817]=41117,	-- depth:46
[41317]=42817,	-- depth:47
[43417]=41317,	-- depth:48
[42717]=43417,	-- depth:49
[41067]=42717,	-- depth:50
[40416]=40566,	-- depth:6
[41567]=41067,	-- depth:51
[42217]=41567,	-- depth:52
[43467]=42217,	-- depth:53
[42767]=43467,	-- depth:54
[40767]=42767,	-- depth:55
[41167]=40767,	-- depth:56
[41967]=41167,	-- depth:57
[40616]=40416,	-- depth:7
[40504]=40454,	-- depth:7
[42667]=41967,	-- depth:58
[41867]=42667,	-- depth:59
[41717]=41867,	-- depth:60
[35303]=36303,	-- depth:1
[34403]=36303,	-- depth:1
[35003]=36303,	-- depth:1
[42065]=42215,	-- depth:1
[41465]=42065,	-- depth:2
[40751]=42051,	-- depth:1
[35203]=36303,	-- depth:1
[34353]=36303,	-- depth:1
[41501]=40751,	-- depth:2
[41301]=41501,	-- depth:3
[35103]=36303,	-- depth:1
[35153]=36303,	-- depth:1
[42101]=41301,	-- depth:4
[40517]=42367,	-- depth:1
[34253]=36303,	-- depth:1
[41365]=41465,	-- depth:3
[42251]=42101,	-- depth:5
[35403]=36303,	-- depth:1
[40567]=40517,	-- depth:2
[42301]=42251,	-- depth:6
[42015]=41365,	-- depth:4
[42115]=42015,	-- depth:5
[42201]=42301,	-- depth:7
[34453]=36303,	-- depth:1
[41515]=42115,	-- depth:6
[35053]=36303,	-- depth:1
[34853]=36303,	-- depth:1
[42165]=41515,	-- depth:7
[34303]=36303,	-- depth:1
[40715]=42165,	-- depth:8
[41415]=40715,	-- depth:9
[41315]=41415,	-- depth:10
[35253]=36303,	-- depth:1
[41401]=42201,	-- depth:8
[41351]=41401,	-- depth:9
[42001]=41351,	-- depth:10
[41965]=41315,	-- depth:11
[42315]=41965,	-- depth:12
[41451]=42001,	-- depth:11
[40417]=40567,	-- depth:3
[42265]=42315,	-- depth:13
[41265]=42265,	-- depth:14
[40467]=40417,	-- depth:4
[42151]=41451,	-- depth:12
[34953]=36303,	-- depth:1
[42351]=42151,	-- depth:13
[40801]=42351,	-- depth:14
[35353]=36303,	-- depth:1
[40765]=41265,	-- depth:15
[34903]=36303,	-- depth:1
[34153]=36303,	-- depth:1
[42401]=40801,	-- depth:15
[43215]=40765,	-- depth:16
[43251]=42401,	-- depth:16
[33803]=36303,	-- depth:1
[36053]=36303,	-- depth:1
[41615]=43215,	-- depth:17
[34653]=36303,	-- depth:1
[41651]=43251,	-- depth:17
[41765]=41615,	-- depth:18
[43265]=41765,	-- depth:19
[33753]=36303,	-- depth:1
[40965]=43265,	-- depth:20
[33403]=36303,	-- depth:1
[36103]=36303,	-- depth:1
[43315]=40965,	-- depth:21
[43351]=41651,	-- depth:18
[41001]=43351,	-- depth:19
[43301]=41001,	-- depth:20
[33353]=36303,	-- depth:1
[41801]=43301,	-- depth:21
[43201]=41801,	-- depth:22
[40651]=43201,	-- depth:23
[43015]=43315,	-- depth:22
[43051]=40651,	-- depth:24
[35903]=36303,	-- depth:1
[40915]=43015,	-- depth:23
[41151]=43051,	-- depth:25
[43065]=40915,	-- depth:24
[43101]=41151,	-- depth:26
[35953]=36303,	-- depth:1
[33303]=36303,	-- depth:1
[43115]=43065,	-- depth:25
[43151]=43101,	-- depth:27
[33853]=36303,	-- depth:1
[40951]=43151,	-- depth:28
[41115]=43115,	-- depth:26
[36003]=36303,	-- depth:1
[43165]=41115,	-- depth:27
[43365]=43165,	-- depth:28
[43401]=40951,	-- depth:29
[41101]=43401,	-- depth:30
[41751]=41101,	-- depth:31
[41015]=43365,	-- depth:29
[43565]=41015,	-- depth:30
[43601]=41751,	-- depth:32
[41051]=43601,	-- depth:33
[36302]=36303,	-- depth:1
[36304]=36303,	-- depth:1
[36305]=36303,	-- depth:1
[43615]=43565,	-- depth:31
[43651]=41051,	-- depth:34
[36306]=36303,	-- depth:1
[36307]=36303,	-- depth:1
[36308]=36303,	-- depth:1
[36309]=36303,	-- depth:1
[36310]=36303,	-- depth:1
[41665]=43615,	-- depth:32
[43665]=41665,	-- depth:33
[33553]=36303,	-- depth:1
[33603]=36303,	-- depth:1
[42365]=43665,	-- depth:34
[41701]=43651,	-- depth:35
[33503]=36303,	-- depth:1
[36153]=36303,	-- depth:1
[41065]=42365,	-- depth:35
[33703]=36303,	-- depth:1
[33453]=36303,	-- depth:1
[43415]=41065,	-- depth:36
[43451]=41701,	-- depth:36
[41715]=43415,	-- depth:37
[36203]=36303,	-- depth:1
[43465]=41715,	-- depth:38
[43501]=43451,	-- depth:37
[34553]=36303,	-- depth:1
[34603]=36303,	-- depth:1
[40665]=43465,	-- depth:39
[33653]=36303,	-- depth:1
[40701]=43501,	-- depth:38
[43515]=40665,	-- depth:40
[43551]=40701,	-- depth:39
[36253]=36303,	-- depth:1
[41815]=43515,	-- depth:41
[33903]=36303,	-- depth:1
[41851]=43551,	-- depth:40
[42565]=41815,	-- depth:42
[42601]=41851,	-- depth:41
[40851]=42601,	-- depth:42
[35603]=36303,	-- depth:1
[42615]=42565,	-- depth:43
[42651]=40851,	-- depth:43
[41951]=42651,	-- depth:44
[41915]=42615,	-- depth:44
[34503]=36303,	-- depth:1
[35653]=36303,	-- depth:1
[33253]=36303,	-- depth:1
[42665]=41915,	-- depth:45
[42701]=41951,	-- depth:45
[34103]=36303,	-- depth:1
[41565]=42665,	-- depth:46
[34803]=36303,	-- depth:1
[34753]=36303,	-- depth:1
[33053]=36303,	-- depth:1
[40317]=40467,	-- depth:5
[41251]=42701,	-- depth:46
[40367]=40317,	-- depth:6
[33003]=36303,	-- depth:1
[35453]=36303,	-- depth:1
[41551]=41251,	-- depth:47
[42415]=41565,	-- depth:47
[42451]=41551,	-- depth:48
[34203]=36303,	-- depth:1
[35503]=36303,	-- depth:1
[42465]=42415,	-- depth:48
[42501]=42451,	-- depth:49
[41215]=42465,	-- depth:49
[40815]=41215,	-- depth:50
[42515]=40815,	-- depth:51
[42551]=42501,	-- depth:50
[35553]=36303,	-- depth:1
[41601]=42551,	-- depth:51
[33103]=36303,	-- depth:1
[42751]=41601,	-- depth:52
[40617]=40367,	-- depth:7
[34003]=36303,	-- depth:1
[42715]=42515,	-- depth:52
[42865]=42715,	-- depth:53
[42901]=42751,	-- depth:53
[35803]=36303,	-- depth:1
[41865]=42865,	-- depth:54
[33203]=36303,	-- depth:1
[42951]=42901,	-- depth:54
[34703]=36303,	-- depth:1
[33953]=36303,	-- depth:1
[35853]=36303,	-- depth:1
[42965]=41865,	-- depth:55
[43001]=42951,	-- depth:55
[42915]=42965,	-- depth:56
[41165]=42915,	-- depth:57
[40901]=43001,	-- depth:56
[33153]=36303,	-- depth:1
[42851]=40901,	-- depth:57
[35703]=36303,	-- depth:1
[34053]=36303,	-- depth:1
[42815]=41165,	-- depth:58
[41901]=42851,	-- depth:58
[35753]=36303,	-- depth:1
[41201]=41901,	-- depth:59
[42801]=41201,	-- depth:60
[42765]=42815,	-- depth:59
[40865]=42765,	-- depth:60
[34507]=36307,	-- depth:2
[34552]=36302,	-- depth:2
[34505]=36305,	-- depth:2
[34410]=36310,	-- depth:2
[34555]=36305,	-- depth:2
[34509]=36309,	-- depth:2
[34508]=36308,	-- depth:2
[34409]=36309,	-- depth:2
[34510]=36310,	-- depth:2
[34506]=36306,	-- depth:2
[34456]=36306,	-- depth:2
[34457]=36307,	-- depth:2
[34452]=36302,	-- depth:2
[34458]=36308,	-- depth:2
[34455]=36305,	-- depth:2
[34454]=36304,	-- depth:2
[34459]=36309,	-- depth:2
[34460]=36310,	-- depth:2
[34554]=36304,	-- depth:2
[34502]=36302,	-- depth:2
[34504]=36304,	-- depth:2
[34408]=36308,	-- depth:2
[35152]=36302,	-- depth:2
[34406]=36306,	-- depth:2
[33802]=36302,	-- depth:2
[33804]=36304,	-- depth:2
[33805]=36305,	-- depth:2
[33806]=36306,	-- depth:2
[33807]=36307,	-- depth:2
[33808]=36308,	-- depth:2
[33809]=36309,	-- depth:2
[33810]=36310,	-- depth:2
[33852]=36302,	-- depth:2
[33854]=36304,	-- depth:2
[33855]=36305,	-- depth:2
[33856]=36306,	-- depth:2
[33857]=36307,	-- depth:2
[33858]=36308,	-- depth:2
[33760]=36310,	-- depth:2
[33859]=36309,	-- depth:2
[33902]=36302,	-- depth:2
[33904]=36304,	-- depth:2
[33905]=36305,	-- depth:2
[33906]=36306,	-- depth:2
[33907]=36307,	-- depth:2
[33908]=36308,	-- depth:2
[33909]=36309,	-- depth:2
[33910]=36310,	-- depth:2
[33952]=36302,	-- depth:2
[33954]=36304,	-- depth:2
[33955]=36305,	-- depth:2
[33956]=36306,	-- depth:2
[33957]=36307,	-- depth:2
[33958]=36308,	-- depth:2
[33860]=36310,	-- depth:2
[33759]=36309,	-- depth:2
[33758]=36308,	-- depth:2
[33757]=36307,	-- depth:2
[33558]=36308,	-- depth:2
[33559]=36309,	-- depth:2
[33560]=36310,	-- depth:2
[33602]=36302,	-- depth:2
[33604]=36304,	-- depth:2
[33605]=36305,	-- depth:2
[33606]=36306,	-- depth:2
[33607]=36307,	-- depth:2
[33608]=36308,	-- depth:2
[33609]=36309,	-- depth:2
[33610]=36310,	-- depth:2
[33652]=36302,	-- depth:2
[33654]=36304,	-- depth:2
[33655]=36305,	-- depth:2
[33656]=36306,	-- depth:2
[33657]=36307,	-- depth:2
[33658]=36308,	-- depth:2
[33756]=36306,	-- depth:2
[33755]=36305,	-- depth:2
[33754]=36304,	-- depth:2
[33752]=36302,	-- depth:2
[33710]=36310,	-- depth:2
[33709]=36309,	-- depth:2
[33959]=36309,	-- depth:2
[33708]=36308,	-- depth:2
[33706]=36306,	-- depth:2
[33705]=36305,	-- depth:2
[33704]=36304,	-- depth:2
[33702]=36302,	-- depth:2
[33660]=36310,	-- depth:2
[33659]=36309,	-- depth:2
[33707]=36307,	-- depth:2
[33960]=36310,	-- depth:2
[34002]=36302,	-- depth:2
[34004]=36304,	-- depth:2
[34207]=36307,	-- depth:2
[34208]=36308,	-- depth:2
[34209]=36309,	-- depth:2
[34210]=36310,	-- depth:2
[34252]=36302,	-- depth:2
[34254]=36304,	-- depth:2
[34255]=36305,	-- depth:2
[34256]=36306,	-- depth:2
[34257]=36307,	-- depth:2
[34258]=36308,	-- depth:2
[34259]=36309,	-- depth:2
[34260]=36310,	-- depth:2
[34302]=36302,	-- depth:2
[34304]=36304,	-- depth:2
[34305]=36305,	-- depth:2
[34306]=36306,	-- depth:2
[34307]=36307,	-- depth:2
[34405]=36305,	-- depth:2
[34404]=36304,	-- depth:2
[34402]=36302,	-- depth:2
[34360]=36310,	-- depth:2
[34359]=36309,	-- depth:2
[34358]=36308,	-- depth:2
[34206]=36306,	-- depth:2
[34357]=36307,	-- depth:2
[34355]=36305,	-- depth:2
[34354]=36304,	-- depth:2
[34352]=36302,	-- depth:2
[34310]=36310,	-- depth:2
[34309]=36309,	-- depth:2
[34308]=36308,	-- depth:2
[34356]=36306,	-- depth:2
[34407]=36307,	-- depth:2
[34205]=36305,	-- depth:2
[34202]=36302,	-- depth:2
[34005]=36305,	-- depth:2
[34006]=36306,	-- depth:2
[34007]=36307,	-- depth:2
[34008]=36308,	-- depth:2
[34009]=36309,	-- depth:2
[34010]=36310,	-- depth:2
[34052]=36302,	-- depth:2
[34054]=36304,	-- depth:2
[34055]=36305,	-- depth:2
[34056]=36306,	-- depth:2
[34057]=36307,	-- depth:2
[34058]=36308,	-- depth:2
[34059]=36309,	-- depth:2
[34060]=36310,	-- depth:2
[34102]=36302,	-- depth:2
[34104]=36304,	-- depth:2
[34105]=36305,	-- depth:2
[34160]=36310,	-- depth:2
[34159]=36309,	-- depth:2
[34158]=36308,	-- depth:2
[34157]=36307,	-- depth:2
[34156]=36306,	-- depth:2
[34155]=36305,	-- depth:2
[34204]=36304,	-- depth:2
[34154]=36304,	-- depth:2
[34152]=36302,	-- depth:2
[34110]=36310,	-- depth:2
[34109]=36309,	-- depth:2
[34108]=36308,	-- depth:2
[34107]=36307,	-- depth:2
[34106]=36306,	-- depth:2
[34556]=36306,	-- depth:2
[34557]=36307,	-- depth:2
[35157]=36307,	-- depth:2
[34559]=36309,	-- depth:2
[35857]=36307,	-- depth:2
[35856]=36306,	-- depth:2
[35855]=36305,	-- depth:2
[35854]=36304,	-- depth:2
[35852]=36302,	-- depth:2
[40207]=40206,	-- depth:1
[35810]=36310,	-- depth:2
[35809]=36309,	-- depth:2
[35808]=36308,	-- depth:2
[35807]=36307,	-- depth:2
[35806]=36306,	-- depth:2
[35805]=36305,	-- depth:2
[35804]=36304,	-- depth:2
[35858]=36308,	-- depth:2
[35859]=36309,	-- depth:2
[35860]=36310,	-- depth:2
[35956]=36306,	-- depth:2
[35955]=36305,	-- depth:2
[35954]=36304,	-- depth:2
[35952]=36302,	-- depth:2
[35910]=36310,	-- depth:2
[35802]=36302,	-- depth:2
[35909]=36309,	-- depth:2
[35907]=36307,	-- depth:2
[35906]=36306,	-- depth:2
[35905]=36305,	-- depth:2
[35904]=36304,	-- depth:2
[35902]=36302,	-- depth:2
[40202]=40205,	-- depth:1
[40204]=40205,	-- depth:1
[35908]=36308,	-- depth:2
[40214]=40213,	-- depth:1
[40216]=40131,	-- depth:1
[40230]=40130,	-- depth:1
[40231]=40131,	-- depth:1
[40233]=40133,	-- depth:1
[35660]=36310,	-- depth:2
[35659]=36309,	-- depth:2
[35658]=36308,	-- depth:2
[35657]=36307,	-- depth:2
[35702]=36302,	-- depth:2
[35656]=36306,	-- depth:2
[35654]=36304,	-- depth:2
[35652]=36302,	-- depth:2
[35610]=36310,	-- depth:2
[35609]=36309,	-- depth:2
[35608]=36308,	-- depth:2
[35607]=36307,	-- depth:2
[35606]=36306,	-- depth:2
[35605]=36305,	-- depth:2
[35655]=36305,	-- depth:2
[35957]=36307,	-- depth:2
[35704]=36304,	-- depth:2
[35706]=36306,	-- depth:2
[40219]=40218,	-- depth:1
[35760]=36310,	-- depth:2
[35759]=36309,	-- depth:2
[35758]=36308,	-- depth:2
[35757]=36307,	-- depth:2
[35756]=36306,	-- depth:2
[35755]=36305,	-- depth:2
[35705]=36305,	-- depth:2
[35754]=36304,	-- depth:2
[40220]=40219,	-- depth:2
[40224]=40131,	-- depth:1
[35710]=36310,	-- depth:2
[35709]=36309,	-- depth:2
[35708]=36308,	-- depth:2
[35707]=36307,	-- depth:2
[35752]=36302,	-- depth:2
[35958]=36308,	-- depth:2
[35959]=36309,	-- depth:2
[35960]=36310,	-- depth:2
[36210]=36310,	-- depth:2
[36209]=36309,	-- depth:2
[36208]=36308,	-- depth:2
[36207]=36307,	-- depth:2
[36206]=36306,	-- depth:2
[36205]=36305,	-- depth:2
[36204]=36304,	-- depth:2
[36202]=36302,	-- depth:2
[40028]=40228,	-- depth:1
[40030]=40230,	-- depth:2
[40033]=40233,	-- depth:2
[36160]=36310,	-- depth:2
[36159]=36309,	-- depth:2
[36158]=36308,	-- depth:2
[36157]=36307,	-- depth:2
[36156]=36306,	-- depth:2
[36155]=36305,	-- depth:2
[36154]=36304,	-- depth:2
[40031]=40231,	-- depth:2
[36152]=36302,	-- depth:2
[40025]=40225,	-- depth:1
[40023]=40223,	-- depth:1
[40005]=40205,	-- depth:1
[40010]=40210,	-- depth:1
[40012]=40212,	-- depth:1
[40013]=40213,	-- depth:1
[40014]=40214,	-- depth:2
[40015]=40215,	-- depth:1
[40016]=40216,	-- depth:2
[40018]=40218,	-- depth:1
[40024]=40224,	-- depth:2
[40019]=40219,	-- depth:2
[36260]=36310,	-- depth:2
[36259]=36309,	-- depth:2
[36258]=36308,	-- depth:2
[36257]=36307,	-- depth:2
[36256]=36306,	-- depth:2
[36255]=36305,	-- depth:2
[36254]=36304,	-- depth:2
[36252]=36302,	-- depth:2
[40020]=40220,	-- depth:3
[35604]=36304,	-- depth:2
[40101]=40201,	-- depth:1
[40104]=40204,	-- depth:2
[36052]=36302,	-- depth:2
[40115]=40015,	-- depth:2
[40116]=40016,	-- depth:3
[40118]=40018,	-- depth:2
[40119]=40019,	-- depth:3
[40120]=40020,	-- depth:4
[36010]=36310,	-- depth:2
[36009]=36309,	-- depth:2
[36054]=36304,	-- depth:2
[36008]=36308,	-- depth:2
[36006]=36306,	-- depth:2
[36005]=36305,	-- depth:2
[36004]=36304,	-- depth:2
[36002]=36302,	-- depth:2
[40123]=40023,	-- depth:2
[40124]=40024,	-- depth:3
[40125]=40025,	-- depth:2
[40128]=40028,	-- depth:2
[36007]=36307,	-- depth:2
[40102]=40202,	-- depth:2
[36055]=36305,	-- depth:2
[36057]=36307,	-- depth:2
[40105]=40205,	-- depth:1
[40106]=40206,	-- depth:1
[36110]=36310,	-- depth:2
[36109]=36309,	-- depth:2
[36108]=36308,	-- depth:2
[36107]=36307,	-- depth:2
[36106]=36306,	-- depth:2
[36105]=36305,	-- depth:2
[36056]=36306,	-- depth:2
[36104]=36304,	-- depth:2
[40107]=40207,	-- depth:2
[40110]=40010,	-- depth:2
[40112]=40012,	-- depth:2
[40113]=40013,	-- depth:2
[40114]=40014,	-- depth:3
[36060]=36310,	-- depth:2
[36059]=36309,	-- depth:2
[36058]=36308,	-- depth:2
[36102]=36302,	-- depth:2
[34558]=36308,	-- depth:2
[35602]=36302,	-- depth:2
[35560]=36310,	-- depth:2
[34952]=36302,	-- depth:2
[34910]=36310,	-- depth:2
[34909]=36309,	-- depth:2
[34908]=36308,	-- depth:2
[34907]=36307,	-- depth:2
[34906]=36306,	-- depth:2
[34905]=36305,	-- depth:2
[34904]=36304,	-- depth:2
[34902]=36302,	-- depth:2
[40565]=42215,	-- depth:1
[34860]=36310,	-- depth:2
[34859]=36309,	-- depth:2
[34858]=36308,	-- depth:2
[34857]=36307,	-- depth:2
[34856]=36306,	-- depth:2
[34855]=36305,	-- depth:2
[34854]=36304,	-- depth:2
[34954]=36304,	-- depth:2
[34955]=36305,	-- depth:2
[34956]=36306,	-- depth:2
[34957]=36307,	-- depth:2
[35057]=36307,	-- depth:2
[35056]=36306,	-- depth:2
[35055]=36305,	-- depth:2
[35054]=36304,	-- depth:2
[35052]=36302,	-- depth:2
[40515]=40565,	-- depth:2
[35010]=36310,	-- depth:2
[35009]=36309,	-- depth:2
[34852]=36302,	-- depth:2
[35008]=36308,	-- depth:2
[35006]=36306,	-- depth:2
[35005]=36305,	-- depth:2
[35004]=36304,	-- depth:2
[35002]=36302,	-- depth:2
[40551]=42051,	-- depth:1
[34960]=36310,	-- depth:2
[34959]=36309,	-- depth:2
[34958]=36308,	-- depth:2
[35007]=36307,	-- depth:2
[40601]=40551,	-- depth:2
[34810]=36310,	-- depth:2
[34809]=36309,	-- depth:2
[34702]=36302,	-- depth:2
[34660]=36310,	-- depth:2
[34659]=36309,	-- depth:2
[34658]=36308,	-- depth:2
[34657]=36307,	-- depth:2
[34656]=36306,	-- depth:2
[34655]=36305,	-- depth:2
[34654]=36304,	-- depth:2
[34704]=36304,	-- depth:2
[34652]=36302,	-- depth:2
[34609]=36309,	-- depth:2
[34608]=36308,	-- depth:2
[34607]=36307,	-- depth:2
[34606]=36306,	-- depth:2
[34605]=36305,	-- depth:2
[34604]=36304,	-- depth:2
[34602]=36302,	-- depth:2
[34560]=36310,	-- depth:2
[34610]=36310,	-- depth:2
[35058]=36308,	-- depth:2
[34705]=36305,	-- depth:2
[34707]=36307,	-- depth:2
[34808]=36308,	-- depth:2
[34807]=36307,	-- depth:2
[34806]=36306,	-- depth:2
[34805]=36305,	-- depth:2
[34804]=36304,	-- depth:2
[34802]=36302,	-- depth:2
[34760]=36310,	-- depth:2
[34759]=36309,	-- depth:2
[34706]=36306,	-- depth:2
[34758]=36308,	-- depth:2
[34756]=36306,	-- depth:2
[34755]=36305,	-- depth:2
[34754]=36304,	-- depth:2
[34752]=36302,	-- depth:2
[40615]=40515,	-- depth:3
[34710]=36310,	-- depth:2
[34709]=36309,	-- depth:2
[34708]=36308,	-- depth:2
[34757]=36307,	-- depth:2
[35059]=36309,	-- depth:2
[35060]=36310,	-- depth:2
[35102]=36302,	-- depth:2
[35454]=36304,	-- depth:2
[35452]=36302,	-- depth:2
[40365]=40615,	-- depth:4
[35410]=36310,	-- depth:2
[35409]=36309,	-- depth:2
[35408]=36308,	-- depth:2
[35407]=36307,	-- depth:2
[35406]=36306,	-- depth:2
[35455]=36305,	-- depth:2
[35405]=36305,	-- depth:2
[35402]=36302,	-- depth:2
[40401]=40601,	-- depth:3
[35360]=36310,	-- depth:2
[35359]=36309,	-- depth:2
[35358]=36308,	-- depth:2
[35357]=36307,	-- depth:2
[35356]=36306,	-- depth:2
[35355]=36305,	-- depth:2
[35404]=36304,	-- depth:2
[35354]=36304,	-- depth:2
[35456]=36306,	-- depth:2
[35458]=36308,	-- depth:2
[35559]=36309,	-- depth:2
[35558]=36308,	-- depth:2
[35557]=36307,	-- depth:2
[35556]=36306,	-- depth:2
[35555]=36305,	-- depth:2
[35554]=36304,	-- depth:2
[35552]=36302,	-- depth:2
[40351]=40401,	-- depth:4
[35457]=36307,	-- depth:2
[35510]=36310,	-- depth:2
[35508]=36308,	-- depth:2
[35507]=36307,	-- depth:2
[35506]=36306,	-- depth:2
[35505]=36305,	-- depth:2
[35504]=36304,	-- depth:2
[35502]=36302,	-- depth:2
[35460]=36310,	-- depth:2
[35459]=36309,	-- depth:2
[35509]=36309,	-- depth:2
[40315]=40365,	-- depth:5
[35352]=36302,	-- depth:2
[35309]=36309,	-- depth:2
[35204]=36304,	-- depth:2
[35202]=36302,	-- depth:2
[35160]=36310,	-- depth:2
[35159]=36309,	-- depth:2
[35158]=36308,	-- depth:2
[33557]=36307,	-- depth:2
[35156]=36306,	-- depth:2
[35155]=36305,	-- depth:2
[35205]=36305,	-- depth:2
[35154]=36304,	-- depth:2
[40501]=40351,	-- depth:5
[35110]=36310,	-- depth:2
[35109]=36309,	-- depth:2
[35108]=36308,	-- depth:2
[35107]=36307,	-- depth:2
[35106]=36306,	-- depth:2
[35105]=36305,	-- depth:2
[35104]=36304,	-- depth:2
[40465]=40315,	-- depth:6
[35310]=36310,	-- depth:2
[35206]=36306,	-- depth:2
[35208]=36308,	-- depth:2
[35308]=36308,	-- depth:2
[35307]=36307,	-- depth:2
[35306]=36306,	-- depth:2
[35305]=36305,	-- depth:2
[35304]=36304,	-- depth:2
[35302]=36302,	-- depth:2
[40415]=40465,	-- depth:7
[40451]=40501,	-- depth:6
[35207]=36307,	-- depth:2
[35260]=36310,	-- depth:2
[35258]=36308,	-- depth:2
[35257]=36307,	-- depth:2
[35256]=36306,	-- depth:2
[35255]=36305,	-- depth:2
[35254]=36304,	-- depth:2
[35252]=36302,	-- depth:2
[35210]=36310,	-- depth:2
[35209]=36309,	-- depth:2
[35259]=36309,	-- depth:2
[33556]=36306,	-- depth:2
[33406]=36306,	-- depth:2
[33554]=36304,	-- depth:2
[33204]=36304,	-- depth:2
[33205]=36305,	-- depth:2
[33206]=36306,	-- depth:2
[33207]=36307,	-- depth:2
[33208]=36308,	-- depth:2
[33209]=36309,	-- depth:2
[33210]=36310,	-- depth:2
[33252]=36302,	-- depth:2
[33254]=36304,	-- depth:2
[33255]=36305,	-- depth:2
[33256]=36306,	-- depth:2
[33257]=36307,	-- depth:2
[33258]=36308,	-- depth:2
[33259]=36309,	-- depth:2
[33260]=36310,	-- depth:2
[33302]=36302,	-- depth:2
[33304]=36304,	-- depth:2
[33305]=36305,	-- depth:2
[33306]=36306,	-- depth:2
[33307]=36307,	-- depth:2
[33308]=36308,	-- depth:2
[33309]=36309,	-- depth:2
[33310]=36310,	-- depth:2
[33352]=36302,	-- depth:2
[33354]=36304,	-- depth:2
[33355]=36305,	-- depth:2
[33356]=36306,	-- depth:2
[33357]=36307,	-- depth:2
[33358]=36308,	-- depth:2
[33202]=36302,	-- depth:2
[33160]=36310,	-- depth:2
[33159]=36309,	-- depth:2
[33158]=36308,	-- depth:2
[33555]=36305,	-- depth:2
[33002]=36302,	-- depth:2
[33004]=36304,	-- depth:2
[33005]=36305,	-- depth:2
[33006]=36306,	-- depth:2
[33007]=36307,	-- depth:2
[33008]=36308,	-- depth:2
[33009]=36309,	-- depth:2
[33010]=36310,	-- depth:2
[33052]=36302,	-- depth:2
[33054]=36304,	-- depth:2
[33055]=36305,	-- depth:2
[33056]=36306,	-- depth:2
[33057]=36307,	-- depth:2
[33359]=36309,	-- depth:2
[33058]=36308,	-- depth:2
[33060]=36310,	-- depth:2
[33102]=36302,	-- depth:2
[33104]=36304,	-- depth:2
[33105]=36305,	-- depth:2
[33106]=36306,	-- depth:2
[33107]=36307,	-- depth:2
[33108]=36308,	-- depth:2
[33109]=36309,	-- depth:2
[33110]=36310,	-- depth:2
[33152]=36302,	-- depth:2
[33154]=36304,	-- depth:2
[33155]=36305,	-- depth:2
[33156]=36306,	-- depth:2
[33157]=36307,	-- depth:2
[33059]=36309,	-- depth:2
[33360]=36310,	-- depth:2
[40007]=40207,	-- depth:2
[33506]=36306,	-- depth:2
[33460]=36310,	-- depth:2
[33459]=36309,	-- depth:2
[33505]=36305,	-- depth:2
[33455]=36305,	-- depth:2
[33507]=36307,	-- depth:2
[33508]=36308,	-- depth:2
[33509]=36309,	-- depth:2
[33552]=36302,	-- depth:2
[33510]=36310,	-- depth:2
[33407]=36307,	-- depth:2
[33408]=36308,	-- depth:2
[33409]=36309,	-- depth:2
[33410]=36310,	-- depth:2
[33452]=36302,	-- depth:2
[33454]=36304,	-- depth:2
[33458]=36308,	-- depth:2
[33457]=36307,	-- depth:2
[40006]=40206,	-- depth:1
[33405]=36305,	-- depth:2
[33456]=36306,	-- depth:2
[33402]=36302,	-- depth:2
[33502]=36302,	-- depth:2
[33504]=36304,	-- depth:2
[33404]=36304,	-- depth:2
[41706]=43656,	-- depth:1
[41957]=40657,	-- depth:1
[43006]=41706,	-- depth:2
[42357]=41957,	-- depth:2
[42356]=43006,	-- depth:3
[41057]=42357,	-- depth:3
[41056]=42356,	-- depth:4
[43556]=41056,	-- depth:5
[42507]=41057,	-- depth:4
[41657]=42507,	-- depth:5
[42506]=43556,	-- depth:6
[41707]=41657,	-- depth:6
[42957]=41707,	-- depth:7
[42956]=42506,	-- depth:7
[41507]=42957,	-- depth:8
[40856]=42956,	-- depth:8
[42407]=41507,	-- depth:9
[42457]=42407,	-- depth:10
[41956]=40856,	-- depth:9
[42456]=41956,	-- depth:10
[41206]=42456,	-- depth:11
[41207]=42457,	-- depth:11
[43657]=41207,	-- depth:12
[43156]=41206,	-- depth:12
[41757]=43657,	-- depth:13
[43157]=41757,	-- depth:14
[41756]=43156,	-- depth:13
[42406]=41756,	-- depth:14
[43407]=43157,	-- depth:15
[42006]=42406,	-- depth:15
[40807]=43407,	-- depth:16
[43106]=42006,	-- depth:16
[43056]=43106,	-- depth:17
[43057]=40807,	-- depth:17
[42207]=43057,	-- depth:18
[42206]=43056,	-- depth:18
[41406]=42206,	-- depth:19
[41407]=42207,	-- depth:19
[43607]=41407,	-- depth:20
[41557]=43607,	-- depth:21
[41556]=41406,	-- depth:20
[40757]=41557,	-- depth:22
[40756]=41556,	-- depth:21
[42157]=40757,	-- depth:23
[42156]=40756,	-- depth:22
[41456]=42156,	-- depth:23
[41457]=42157,	-- depth:24
[43606]=41456,	-- depth:24
[42106]=43606,	-- depth:25
[42107]=41457,	-- depth:25
[42057]=42107,	-- depth:26
[41656]=42106,	-- depth:26
[42056]=41656,	-- depth:27
[42256]=42056,	-- depth:28
[40806]=42256,	-- depth:29
[43007]=42057,	-- depth:27
[42307]=43007,	-- depth:28
[41256]=40806,	-- depth:30
[41257]=42307,	-- depth:29
[42306]=41256,	-- depth:31
[42007]=41257,	-- depth:30
[40706]=42306,	-- depth:32
[40707]=42007,	-- depth:31
[41306]=40706,	-- depth:33
[41307]=40707,	-- depth:32
[43557]=41307,	-- depth:33
[41607]=43557,	-- depth:34
[41606]=41306,	-- depth:34
[42257]=41607,	-- depth:35
[41356]=41606,	-- depth:35
[41357]=42257,	-- depth:36
[43107]=41357,	-- depth:37
[43206]=41356,	-- depth:36
[40857]=43107,	-- depth:38
[41806]=43206,	-- depth:37
[43207]=40857,	-- depth:39
[43506]=41806,	-- depth:38
[41006]=43506,	-- depth:39
[41156]=41006,	-- depth:40
[41907]=43207,	-- depth:40
[41906]=41156,	-- depth:41
[43306]=41906,	-- depth:42
[42657]=41907,	-- depth:41
[40907]=42657,	-- depth:42
[40906]=43306,	-- depth:43
[42656]=40906,	-- depth:44
[41157]=40907,	-- depth:43
[43257]=41157,	-- depth:44
[43256]=42656,	-- depth:45
[36315]=36311,	-- depth:1
[43307]=43257,	-- depth:45
[42706]=43256,	-- depth:46
[42707]=43307,	-- depth:46
[41106]=42706,	-- depth:47
[41107]=42707,	-- depth:47
[43406]=41106,	-- depth:48
[42856]=43406,	-- depth:49
[42807]=41107,	-- depth:48
[42806]=42856,	-- depth:50
[43456]=42806,	-- depth:51
[43457]=42807,	-- depth:49
[40957]=43457,	-- depth:50
[43357]=40957,	-- depth:51
[42857]=43357,	-- depth:52
[42757]=42857,	-- depth:53
[42756]=43456,	-- depth:52
[43356]=42756,	-- depth:53
[40956]=43356,	-- depth:54
[43507]=42757,	-- depth:54
[41007]=43507,	-- depth:55
[41506]=40956,	-- depth:55
[42607]=41007,	-- depth:56
[42606]=41506,	-- depth:56
[42557]=42607,	-- depth:57
[41807]=42557,	-- depth:58
[41856]=42606,	-- depth:57
[42556]=41856,	-- depth:58
[40656]=42556,	-- depth:59
[42907]=41807,	-- depth:59
[42906]=40656,	-- depth:60
[41857]=42907,	-- depth:60
[35161]=36311,	-- depth:1
[35011]=36311,	-- depth:1
[42062]=42060,	-- depth:1
[42061]=42063,	-- depth:1
[34861]=36311,	-- depth:1
[35001]=36301,	-- depth:1
[35915]=36311,	-- depth:1
[43161]=42061,	-- depth:2
[35015]=35011,	-- depth:2
[35965]=36311,	-- depth:1
[35151]=36301,	-- depth:1
[43314]=43114,	-- depth:1
[34865]=34861,	-- depth:2
[43313]=42063,	-- depth:1
[42014]=43314,	-- depth:2
[42013]=43313,	-- depth:2
[43360]=42060,	-- depth:1
[43361]=43161,	-- depth:3
[43060]=43360,	-- depth:2
[43061]=43361,	-- depth:4
[43164]=42014,	-- depth:3
[34851]=36301,	-- depth:1
[42064]=43164,	-- depth:4
[35201]=36301,	-- depth:1
[43163]=42013,	-- depth:3
[43364]=42064,	-- depth:5
[43062]=42062,	-- depth:2
[43063]=43163,	-- depth:4
[43363]=43063,	-- depth:5
[36111]=36311,	-- depth:1
[35911]=35915,	-- depth:2
[43210]=43060,	-- depth:3
[43362]=43062,	-- depth:3
[35165]=35161,	-- depth:2
[43162]=43362,	-- depth:4
[42012]=43162,	-- depth:5
[43064]=43364,	-- depth:6
[36001]=36301,	-- depth:1
[36101]=36301,	-- depth:1
[42011]=43061,	-- depth:5
[34901]=36301,	-- depth:1
[43261]=42011,	-- depth:6
[34965]=36311,	-- depth:1
[41961]=43261,	-- depth:7
[43213]=43363,	-- depth:6
[41960]=43210,	-- depth:4
[34951]=36301,	-- depth:1
[35101]=36301,	-- depth:1
[43110]=41960,	-- depth:5
[43260]=43110,	-- depth:6
[40506]=43656,	-- depth:1
[43214]=43064,	-- depth:7
[34961]=34965,	-- depth:2
[35065]=36311,	-- depth:1
[40507]=40657,	-- depth:1
[43111]=41961,	-- depth:8
[43112]=42012,	-- depth:6
[35061]=35065,	-- depth:2
[36051]=36301,	-- depth:1
[36015]=36311,	-- depth:1
[35961]=35965,	-- depth:2
[43312]=43112,	-- depth:7
[41962]=43312,	-- depth:8
[40556]=40506,	-- depth:2
[35115]=36311,	-- depth:1
[43113]=43213,	-- depth:7
[43311]=43111,	-- depth:9
[36065]=36311,	-- depth:1
[42010]=43260,	-- depth:7
[43310]=42010,	-- depth:8
[43160]=43310,	-- depth:9
[35951]=36301,	-- depth:1
[35111]=35115,	-- depth:2
[43211]=43311,	-- depth:10
[36061]=36065,	-- depth:2
[36011]=36015,	-- depth:2
[43212]=41962,	-- depth:9
[34911]=36311,	-- depth:1
[43264]=43214,	-- depth:8
[43263]=43113,	-- depth:8
[43262]=43212,	-- depth:10
[34915]=34911,	-- depth:2
[41964]=43264,	-- depth:9
[35051]=36301,	-- depth:1
[41963]=43263,	-- depth:9
[33751]=36301,	-- depth:1
[40557]=40507,	-- depth:2
[35311]=36311,	-- depth:1
[42111]=43211,	-- depth:11
[42562]=43262,	-- depth:11
[42563]=41963,	-- depth:10
[42564]=41964,	-- depth:10
[35561]=36311,	-- depth:1
[42911]=42111,	-- depth:12
[42910]=43160,	-- depth:10
[42561]=42911,	-- depth:13
[35565]=35561,	-- depth:2
[42610]=42910,	-- depth:11
[42611]=42561,	-- depth:14
[42612]=42562,	-- depth:12
[42613]=42563,	-- depth:11
[42614]=42564,	-- depth:11
[42864]=42614,	-- depth:12
[35601]=36301,	-- depth:1
[35801]=36301,	-- depth:1
[42560]=42610,	-- depth:12
[42913]=42613,	-- depth:12
[42461]=42611,	-- depth:15
[42462]=42612,	-- depth:13
[42463]=42913,	-- depth:13
[42464]=42864,	-- depth:13
[42914]=42464,	-- depth:14
[35811]=36311,	-- depth:1
[42912]=42462,	-- depth:14
[42510]=42560,	-- depth:13
[42511]=42461,	-- depth:16
[42512]=42912,	-- depth:15
[42513]=42463,	-- depth:14
[35515]=36311,	-- depth:1
[42514]=42914,	-- depth:15
[35551]=36301,	-- depth:1
[35511]=35515,	-- depth:2
[35611]=36311,	-- depth:1
[42863]=42513,	-- depth:15
[42862]=42512,	-- depth:16
[35701]=36301,	-- depth:1
[42714]=42514,	-- depth:16
[42760]=42510,	-- depth:14
[42761]=42511,	-- depth:17
[42762]=42862,	-- depth:17
[42763]=42863,	-- depth:16
[42713]=42763,	-- depth:17
[35711]=36311,	-- depth:1
[35715]=35711,	-- depth:2
[40221]=40121,	-- depth:1
[42814]=42714,	-- depth:17
[35751]=36301,	-- depth:1
[42813]=42713,	-- depth:18
[42810]=42760,	-- depth:15
[42764]=42814,	-- depth:18
[35761]=36311,	-- depth:1
[42712]=42762,	-- depth:18
[35665]=36311,	-- depth:1
[42861]=42761,	-- depth:18
[35615]=35611,	-- depth:2
[40307]=40557,	-- depth:3
[35765]=35761,	-- depth:2
[40234]=40134,	-- depth:1
[35651]=36301,	-- depth:1
[42660]=42810,	-- depth:16
[42661]=42861,	-- depth:19
[42662]=42712,	-- depth:19
[42663]=42813,	-- depth:19
[42664]=42764,	-- depth:19
[42860]=42660,	-- depth:17
[35661]=35665,	-- depth:2
[42710]=42860,	-- depth:18
[42711]=42661,	-- depth:20
[35501]=36301,	-- depth:1
[42110]=42710,	-- depth:19
[40356]=40556,	-- depth:3
[35465]=36311,	-- depth:1
[43014]=42664,	-- depth:20
[35901]=36301,	-- depth:1
[35301]=36301,	-- depth:1
[43013]=42663,	-- depth:20
[42213]=43013,	-- depth:21
[42214]=43014,	-- depth:21
[42212]=42662,	-- depth:20
[43012]=42212,	-- depth:21
[42260]=42110,	-- depth:20
[42261]=42711,	-- depth:21
[36115]=36111,	-- depth:2
[43011]=42261,	-- depth:22
[43010]=42260,	-- depth:21
[35315]=35311,	-- depth:2
[35865]=36311,	-- depth:1
[35861]=35865,	-- depth:2
[35265]=36311,	-- depth:1
[42210]=43010,	-- depth:22
[42112]=43012,	-- depth:22
[42113]=42213,	-- depth:22
[35211]=36311,	-- depth:1
[40457]=40307,	-- depth:4
[40456]=40356,	-- depth:4
[42114]=42214,	-- depth:22
[42211]=43011,	-- depth:23
[35215]=35211,	-- depth:2
[42160]=42210,	-- depth:23
[42161]=42211,	-- depth:24
[42162]=42112,	-- depth:23
[42163]=42113,	-- depth:23
[42164]=42114,	-- depth:23
[35261]=35265,	-- depth:2
[35251]=36301,	-- depth:1
[40407]=40457,	-- depth:5
[35351]=36301,	-- depth:1
[42262]=42162,	-- depth:24
[42962]=42262,	-- depth:25
[42410]=42160,	-- depth:24
[35415]=36311,	-- depth:1
[42961]=42161,	-- depth:25
[42960]=42410,	-- depth:25
[35851]=36301,	-- depth:1
[42963]=42163,	-- depth:24
[35451]=36301,	-- depth:1
[42412]=42962,	-- depth:26
[42413]=42963,	-- depth:25
[42414]=42164,	-- depth:24
[42460]=42960,	-- depth:26
[35461]=35465,	-- depth:2
[35815]=35811,	-- depth:2
[42411]=42961,	-- depth:26
[35411]=35415,	-- depth:2
[42364]=42414,	-- depth:25
[42363]=42413,	-- depth:26
[42263]=42363,	-- depth:27
[42264]=42364,	-- depth:26
[42310]=42460,	-- depth:27
[42311]=42411,	-- depth:27
[35361]=36311,	-- depth:1
[40406]=40456,	-- depth:5
[42312]=42412,	-- depth:27
[42313]=42263,	-- depth:28
[35365]=35361,	-- depth:2
[42314]=42264,	-- depth:27
[35401]=36301,	-- depth:1
[42964]=42314,	-- depth:28
[42360]=42310,	-- depth:28
[42361]=42311,	-- depth:28
[42362]=42312,	-- depth:28
[40357]=40407,	-- depth:6
[33401]=36301,	-- depth:1
[36201]=36301,	-- depth:1
[34311]=36311,	-- depth:1
[33061]=36311,	-- depth:1
[34151]=36301,	-- depth:1
[33601]=36301,	-- depth:1
[41014]=42964,	-- depth:29
[33051]=36301,	-- depth:1
[41212]=42362,	-- depth:29
[41213]=42313,	-- depth:29
[34161]=36311,	-- depth:1
[41214]=41014,	-- depth:30
[40814]=41214,	-- depth:31
[40813]=41213,	-- depth:30
[34165]=34161,	-- depth:2
[40812]=41212,	-- depth:30
[40811]=42361,	-- depth:29
[40810]=42360,	-- depth:29
[34115]=36311,	-- depth:1
[41211]=40811,	-- depth:30
[41210]=40810,	-- depth:30
[34111]=34115,	-- depth:2
[33615]=36311,	-- depth:1
[33151]=36301,	-- depth:1
[33115]=36311,	-- depth:1
[33111]=33115,	-- depth:2
[41011]=41211,	-- depth:31
[41012]=40812,	-- depth:31
[41013]=40813,	-- depth:31
[34201]=36301,	-- depth:1
[34061]=36311,	-- depth:1
[43660]=41210,	-- depth:31
[34065]=34061,	-- depth:2
[33611]=33615,	-- depth:2
[34101]=36301,	-- depth:1
[33501]=36301,	-- depth:1
[33101]=36301,	-- depth:1
[33065]=33061,	-- depth:2
[40860]=43660,	-- depth:32
[41010]=40860,	-- depth:33
[33015]=36311,	-- depth:1
[33511]=36311,	-- depth:1
[41310]=41010,	-- depth:34
[41311]=41011,	-- depth:32
[34301]=36301,	-- depth:1
[41312]=41012,	-- depth:32
[41313]=41013,	-- depth:32
[41314]=40814,	-- depth:32
[36265]=36311,	-- depth:1
[41360]=41310,	-- depth:35
[41361]=41311,	-- depth:33
[34815]=36311,	-- depth:1
[40764]=41314,	-- depth:33
[40763]=41313,	-- depth:33
[40762]=41312,	-- depth:33
[34315]=34311,	-- depth:2
[40761]=41361,	-- depth:34
[34265]=36311,	-- depth:1
[43610]=41360,	-- depth:36
[34261]=34265,	-- depth:2
[41264]=40764,	-- depth:34
[33565]=36311,	-- depth:1
[43614]=41264,	-- depth:35
[33561]=33565,	-- depth:2
[33515]=33511,	-- depth:2
[34211]=36311,	-- depth:1
[43613]=40763,	-- depth:34
[43612]=40762,	-- depth:34
[33011]=33015,	-- depth:2
[34215]=34211,	-- depth:2
[33001]=36301,	-- depth:1
[34251]=36301,	-- depth:1
[43611]=40761,	-- depth:35
[41260]=43610,	-- depth:37
[41261]=43611,	-- depth:36
[41262]=43612,	-- depth:35
[41263]=43613,	-- depth:35
[33551]=36301,	-- depth:1
[34051]=36301,	-- depth:1
[40861]=41261,	-- depth:37
[40862]=41262,	-- depth:36
[33815]=36311,	-- depth:1
[41113]=41263,	-- depth:36
[41114]=43614,	-- depth:36
[33311]=36311,	-- depth:1
[33851]=36301,	-- depth:1
[33411]=36311,	-- depth:1
[33415]=33411,	-- depth:2
[33451]=36301,	-- depth:1
[33301]=36301,	-- depth:1
[33861]=36311,	-- depth:1
[33265]=36311,	-- depth:1
[33261]=33265,	-- depth:2
[33865]=33861,	-- depth:2
[40914]=41114,	-- depth:37
[40913]=41113,	-- depth:37
[33315]=33311,	-- depth:2
[41112]=40862,	-- depth:37
[33811]=33815,	-- depth:2
[41111]=40861,	-- depth:38
[33365]=36311,	-- depth:1
[33361]=33365,	-- depth:2
[40963]=40913,	-- depth:38
[40964]=40914,	-- depth:38
[33761]=36311,	-- depth:1
[40962]=41112,	-- depth:38
[40912]=40962,	-- depth:39
[40961]=41111,	-- depth:39
[33765]=33761,	-- depth:2
[51000]=51001,	-- depth:1
[33801]=36301,	-- depth:1
[33715]=36311,	-- depth:1
[33711]=33715,	-- depth:2
[33351]=36301,	-- depth:1
[41110]=41260,	-- depth:38
[40960]=41110,	-- depth:39
[33901]=36301,	-- depth:1
[33701]=36301,	-- depth:1
[33665]=36311,	-- depth:1
[34001]=36301,	-- depth:1
[41161]=40961,	-- depth:40
[41162]=40912,	-- depth:40
[41163]=40963,	-- depth:39
[33165]=36311,	-- depth:1
[41164]=40964,	-- depth:39
[33161]=33165,	-- depth:2
[33201]=36301,	-- depth:1
[33465]=36311,	-- depth:1
[41060]=40960,	-- depth:40
[34011]=36311,	-- depth:1
[33651]=36301,	-- depth:1
[43661]=41161,	-- depth:41
[40864]=41164,	-- depth:40
[34015]=34011,	-- depth:2
[40863]=41163,	-- depth:40
[41061]=43661,	-- depth:42
[40760]=41060,	-- depth:41
[41062]=41162,	-- depth:41
[33965]=36311,	-- depth:1
[33251]=36301,	-- depth:1
[33911]=36311,	-- depth:1
[40911]=41061,	-- depth:43
[40910]=40760,	-- depth:42
[43664]=40864,	-- depth:41
[33915]=33911,	-- depth:2
[33951]=36301,	-- depth:1
[41063]=40863,	-- depth:41
[33215]=36311,	-- depth:1
[43662]=41062,	-- depth:42
[41160]=40910,	-- depth:43
[33211]=33215,	-- depth:2
[33661]=33665,	-- depth:2
[33961]=33965,	-- depth:2
[33461]=33465,	-- depth:2
[41064]=43664,	-- depth:42
[43663]=41063,	-- depth:42
[34351]=36301,	-- depth:1
[42812]=43662,	-- depth:43
[41363]=43663,	-- depth:43
[36211]=36311,	-- depth:1
[43511]=40911,	-- depth:44
[34651]=36301,	-- depth:1
[41760]=41160,	-- depth:44
[41761]=43511,	-- depth:45
[41762]=42812,	-- depth:44
[34615]=36311,	-- depth:1
[41763]=41363,	-- depth:44
[43510]=41760,	-- depth:45
[34661]=36311,	-- depth:1
[41810]=43510,	-- depth:46
[34665]=34661,	-- depth:2
[41811]=41761,	-- depth:46
[41812]=41762,	-- depth:45
[41764]=41064,	-- depth:43
[41813]=41763,	-- depth:45
[40660]=41810,	-- depth:47
[40662]=41812,	-- depth:46
[43514]=41764,	-- depth:44
[34565]=36311,	-- depth:1
[36215]=36211,	-- depth:2
[40664]=43514,	-- depth:45
[40663]=41813,	-- depth:46
[34601]=36301,	-- depth:1
[40661]=41811,	-- depth:47
[41710]=40660,	-- depth:48
[41712]=40662,	-- depth:47
[41713]=40663,	-- depth:47
[41362]=41712,	-- depth:48
[43513]=41713,	-- depth:48
[43512]=41362,	-- depth:49
[34611]=34615,	-- depth:2
[41711]=40661,	-- depth:48
[34701]=36301,	-- depth:1
[41814]=40664,	-- depth:46
[41860]=41710,	-- depth:49
[36165]=36311,	-- depth:1
[34765]=36311,	-- depth:1
[40034]=40234,	-- depth:2
[40607]=40357,	-- depth:7
[40606]=40406,	-- depth:6
[34801]=36301,	-- depth:1
[34761]=34765,	-- depth:2
[36161]=36165,	-- depth:2
[43413]=43513,	-- depth:49
[43412]=43512,	-- depth:50
[43411]=41711,	-- depth:49
[43410]=41860,	-- depth:50
[34811]=34815,	-- depth:2
[36151]=36301,	-- depth:1
[43414]=41814,	-- depth:47
[43460]=43410,	-- depth:51
[43461]=43411,	-- depth:50
[41914]=43414,	-- depth:48
[41861]=43461,	-- depth:51
[41862]=43412,	-- depth:51
[41863]=43413,	-- depth:50
[34711]=36311,	-- depth:1
[41864]=41914,	-- depth:49
[34715]=34711,	-- depth:2
[43464]=41864,	-- depth:50
[43463]=41863,	-- depth:51
[34751]=36301,	-- depth:1
[43462]=41862,	-- depth:52
[42811]=41861,	-- depth:52
[41910]=43460,	-- depth:52
[41911]=42811,	-- depth:53
[41912]=43462,	-- depth:53
[41913]=43463,	-- depth:52
[34561]=34565,	-- depth:2
[41664]=43464,	-- depth:51
[41714]=41664,	-- depth:52
[41511]=41911,	-- depth:54
[40711]=41511,	-- depth:55
[43564]=41714,	-- depth:53
[40713]=41913,	-- depth:53
[34365]=36311,	-- depth:1
[34461]=36311,	-- depth:1
[41560]=41910,	-- depth:53
[43560]=41560,	-- depth:54
[43561]=40711,	-- depth:56
[41514]=43564,	-- depth:54
[41513]=40713,	-- depth:54
[41412]=41912,	-- depth:54
[41512]=41412,	-- depth:55
[41663]=41513,	-- depth:55
[41413]=41663,	-- depth:56
[34451]=36301,	-- depth:1
[41414]=41514,	-- depth:55
[40714]=41414,	-- depth:56
[41510]=43560,	-- depth:55
[43562]=41512,	-- depth:56
[34415]=36311,	-- depth:1
[43563]=41413,	-- depth:57
[34411]=34415,	-- depth:2
[41464]=40714,	-- depth:57
[41463]=43563,	-- depth:58
[41462]=43562,	-- depth:57
[34401]=36301,	-- depth:1
[41461]=43561,	-- depth:57
[34465]=34461,	-- depth:2
[40710]=41510,	-- depth:56
[40712]=41462,	-- depth:58
[34515]=36311,	-- depth:1
[41662]=40712,	-- depth:59
[41364]=41464,	-- depth:58
[41661]=41461,	-- depth:58
[41660]=40710,	-- depth:57
[40021]=40221,	-- depth:2
[34551]=36301,	-- depth:1
[41614]=41364,	-- depth:59
[41613]=41463,	-- depth:59
[41612]=41662,	-- depth:60
[41410]=41660,	-- depth:58
[41611]=41661,	-- depth:59
[41411]=41611,	-- depth:60
[34511]=34515,	-- depth:2
[41460]=41410,	-- depth:59
[41610]=41460,	-- depth:60
[34501]=36301,	-- depth:1
[34361]=34365,	-- depth:2
[41562]=41612,	-- depth:61
[36261]=36265,	-- depth:2
[41563]=41613,	-- depth:60
[41561]=41411,	-- depth:61
[41564]=41614,	-- depth:60
[36251]=36301,	-- depth:1
[42859]=43459,	-- depth:1
[43409]=42859,	-- depth:2
[43359]=43409,	-- depth:3
[43358]=43608,	-- depth:1
[43609]=43359,	-- depth:4
[43059]=43609,	-- depth:5
[43058]=43358,	-- depth:2
[40009]=40109,	-- depth:1
[42858]=43058,	-- depth:3
[43458]=42858,	-- depth:4
[40209]=40009,	-- depth:2
[43658]=43458,	-- depth:5
[42958]=43658,	-- depth:6
[43659]=43059,	-- depth:6
[42959]=43659,	-- depth:7
[43109]=42959,	-- depth:8
[40022]=40217,	-- depth:1
[43108]=42958,	-- depth:7
[43158]=43108,	-- depth:8
[43159]=43109,	-- depth:9
[40122]=40022,	-- depth:2
[43558]=43158,	-- depth:9
[43559]=43159,	-- depth:10
[43208]=43558,	-- depth:10
[43209]=43559,	-- depth:11
[43509]=43209,	-- depth:12
[42909]=43509,	-- depth:13
[42908]=43208,	-- depth:11
[40132]=40032,	-- depth:1
[43508]=42908,	-- depth:12
[40117]=40217,	-- depth:1
[43008]=43508,	-- depth:13
[43258]=43008,	-- depth:14
[43259]=42909,	-- depth:14
[43009]=43259,	-- depth:15
[40017]=40117,	-- depth:2
[40203]=40103,	-- depth:1
[43308]=43258,	-- depth:15
[43309]=43009,	-- depth:16
[43408]=43308,	-- depth:16
[17669]=17663,	-- depth:1
[42808]=43408,	-- depth:17
[17719]=17687,	-- depth:1
[17720]=17719,	-- depth:2
[17721]=17719,	-- depth:2
[17722]=17719,	-- depth:2
[17723]=17719,	-- depth:2
[17724]=17719,	-- depth:2
[17725]=17719,	-- depth:2
[17731]=17730,	-- depth:1
[17732]=17731,	-- depth:2
[17718]=17719,	-- depth:2
[17733]=17732,	-- depth:3
[17735]=17733,	-- depth:4
[17736]=17735,	-- depth:5
[17737]=17736,	-- depth:6
[40610]=42060,	-- depth:1
[40611]=42061,	-- depth:2
[17738]=17737,	-- depth:7
[17739]=17738,	-- depth:8
[41909]=43309,	-- depth:17
[41908]=42808,	-- depth:18
[40612]=42062,	-- depth:2
[17734]=17739,	-- depth:9
[40613]=42063,	-- depth:1
[17717]=17719,	-- depth:2
[17715]=17719,	-- depth:2
[42809]=41909,	-- depth:18
[40560]=40610,	-- depth:2
[17699]=17719,	-- depth:2
[17700]=17719,	-- depth:2
[17701]=17719,	-- depth:2
[17702]=17719,	-- depth:2
[17703]=17719,	-- depth:2
[17704]=17719,	-- depth:2
[17705]=17719,	-- depth:2
[17706]=17719,	-- depth:2
[17716]=17719,	-- depth:2
[17707]=17719,	-- depth:2
[40562]=40612,	-- depth:3
[40563]=40613,	-- depth:2
[40564]=43114,	-- depth:1
[17708]=17719,	-- depth:2
[17709]=17719,	-- depth:2
[17710]=17719,	-- depth:2
[17711]=17719,	-- depth:2
[17712]=17719,	-- depth:2
[17713]=17719,	-- depth:2
[17714]=17719,	-- depth:2
[40561]=40611,	-- depth:3
[17697]=17719,	-- depth:2
[40614]=40564,	-- depth:2
[41858]=41908,	-- depth:19
[41309]=42809,	-- depth:19
[41308]=41858,	-- depth:20
[41259]=41309,	-- depth:20
[41258]=41308,	-- depth:21
[40808]=41258,	-- depth:22
[40809]=41259,	-- depth:21
[41058]=40808,	-- depth:23
[41209]=40809,	-- depth:22
[41208]=41058,	-- depth:24
[41059]=41209,	-- depth:23
[41358]=41208,	-- depth:25
[40858]=41358,	-- depth:26
[41009]=41059,	-- depth:24
[41008]=40858,	-- depth:27
[41159]=41009,	-- depth:25
[41158]=41008,	-- depth:28
[40908]=41158,	-- depth:29
[40909]=41159,	-- depth:26
[40958]=40908,	-- depth:30
[40959]=40909,	-- depth:27
[41109]=40959,	-- depth:28
[41108]=40958,	-- depth:31
[40859]=41109,	-- depth:29
[41859]=40859,	-- depth:30
[41359]=41859,	-- depth:31
[41408]=41108,	-- depth:32
[41809]=41359,	-- depth:32
[41808]=41408,	-- depth:33
[40658]=41808,	-- depth:34
[40659]=41809,	-- depth:33
[41759]=40659,	-- depth:34
[41758]=40658,	-- depth:35
[41709]=41759,	-- depth:35
[41708]=41758,	-- depth:36
[41659]=41709,	-- depth:36
[41658]=41708,	-- depth:37
[40759]=41659,	-- depth:37
[41609]=40759,	-- depth:38
[40708]=41658,	-- depth:38
[40709]=41609,	-- depth:39
[41559]=40709,	-- depth:40
[41558]=40708,	-- depth:39
[41509]=41559,	-- depth:41
[41508]=41558,	-- depth:40
[41459]=41509,	-- depth:42
[41458]=41508,	-- depth:41
[40758]=41458,	-- depth:42
[41409]=41459,	-- depth:43
[41608]=40758,	-- depth:43
[17696]=17719,	-- depth:2
[17698]=17719,	-- depth:2
[17693]=17719,	-- depth:2
[40364]=40614,	-- depth:3
[42409]=41409,	-- depth:44
[42408]=41608,	-- depth:44
[42359]=42409,	-- depth:45
[42358]=42408,	-- depth:45
[42309]=42359,	-- depth:46
[42308]=42358,	-- depth:46
[40410]=40560,	-- depth:3
[40411]=40561,	-- depth:4
[40363]=40563,	-- depth:3
[40412]=40562,	-- depth:4
[42258]=42308,	-- depth:47
[40413]=40363,	-- depth:4
[40414]=40364,	-- depth:4
[42209]=42309,	-- depth:47
[42208]=42258,	-- depth:48
[42159]=42209,	-- depth:48
[17695]=17719,	-- depth:2
[42109]=42159,	-- depth:49
[42259]=42109,	-- depth:50
[40362]=40412,	-- depth:5
[42458]=42208,	-- depth:49
[42459]=42259,	-- depth:51
[40222]=40122,	-- depth:3
[42759]=42459,	-- depth:52
[42758]=42458,	-- depth:50
[40232]=40132,	-- depth:2
[42709]=42759,	-- depth:53
[42708]=42758,	-- depth:51
[40310]=40410,	-- depth:4
[42659]=42709,	-- depth:54
[42658]=42708,	-- depth:52
[40311]=40411,	-- depth:5
[40312]=40362,	-- depth:6
[40313]=40413,	-- depth:5
[40314]=40414,	-- depth:5
[42609]=42659,	-- depth:55
[42608]=42658,	-- depth:53
[42559]=42609,	-- depth:56
[42558]=42608,	-- depth:54
[42509]=42559,	-- depth:57
[42508]=42558,	-- depth:55
[40360]=40310,	-- depth:5
[40361]=40311,	-- depth:6
[42108]=42508,	-- depth:56
[40460]=40360,	-- depth:6
[42158]=42108,	-- depth:57
[40513]=40313,	-- depth:6
[40511]=40361,	-- depth:7
[40512]=40312,	-- depth:7
[40461]=40511,	-- depth:8
[40514]=40314,	-- depth:6
[17671]=17663,	-- depth:1
[17672]=17663,	-- depth:1
[17673]=17663,	-- depth:1
[17674]=17663,	-- depth:1
[17675]=17663,	-- depth:1
[17676]=17663,	-- depth:1
[17677]=17663,	-- depth:1
[17678]=17663,	-- depth:1
[17679]=17663,	-- depth:1
[17680]=17663,	-- depth:1
[17681]=17663,	-- depth:1
[17682]=17663,	-- depth:1
[17683]=17663,	-- depth:1
[17684]=17663,	-- depth:1
[17688]=17687,	-- depth:1
[17689]=17687,	-- depth:1
[17690]=17687,	-- depth:1
[17691]=17719,	-- depth:2
[17692]=17719,	-- depth:2
[40510]=40460,	-- depth:7
[17670]=17663,	-- depth:1
[17694]=17719,	-- depth:2
[17667]=17663,	-- depth:1
[40463]=40513,	-- depth:7
[42059]=42509,	-- depth:58
[42058]=42158,	-- depth:58
[40464]=40514,	-- depth:7
[17668]=17663,	-- depth:1
[42009]=42059,	-- depth:59
[42008]=42058,	-- depth:59
[40462]=40512,	-- depth:8
[41958]=42008,	-- depth:60
[41959]=42009,	-- depth:60
[17664]=17663,	-- depth:1
[17665]=17663,	-- depth:1
[17666]=17663,	-- depth:1
[40559]=43459,	-- depth:1
[40558]=43608,	-- depth:1
[40235]=40035,	-- depth:1
[40458]=40558,	-- depth:2
[40459]=40559,	-- depth:2
[40608]=40458,	-- depth:3
[40308]=40608,	-- depth:4
[40129]=40229,	-- depth:1
[40135]=40235,	-- depth:2
[40029]=40129,	-- depth:2
[40508]=40308,	-- depth:5
[40358]=40508,	-- depth:6
[40359]=40459,	-- depth:3
[40509]=40359,	-- depth:4
[40409]=40509,	-- depth:5
[40408]=40358,	-- depth:7
[40309]=40409,	-- depth:6
[40609]=40309,	-- depth:7
[36318]=36311,	-- depth:1
[31501]=31451,	-- depth:1
[31551]=31501,	-- depth:2
[31601]=31501,	-- depth:2
[31651]=31601,	-- depth:3
[31401]=31451,	-- depth:1
[31701]=31601,	-- depth:3
[31751]=31601,	-- depth:3
[17290]=17120,	-- depth:1
[17122]=17120,	-- depth:1
[17121]=17122,	-- depth:2
[31851]=31601,	-- depth:3
[17291]=17290,	-- depth:2
[31351]=31401,	-- depth:2
[31801]=31601,	-- depth:3
[31001]=31351,	-- depth:3
[36314]=36318,	-- depth:2
[31051]=31001,	-- depth:4
[36316]=36311,	-- depth:1
[31101]=31001,	-- depth:4
[36317]=36311,	-- depth:1
[31151]=31001,	-- depth:4
[36312]=36316,	-- depth:2
[36313]=36317,	-- depth:2
[31301]=31351,	-- depth:3
[31251]=31301,	-- depth:4
[31201]=31001,	-- depth:4
[33963]=36317,	-- depth:2
[33962]=36316,	-- depth:2
[33967]=33963,	-- depth:3
[33968]=36318,	-- depth:2
[33918]=36318,	-- depth:2
[33917]=36317,	-- depth:2
[33916]=36316,	-- depth:2
[33966]=33962,	-- depth:3
[33763]=36317,	-- depth:2
[33914]=33918,	-- depth:3
[34013]=36317,	-- depth:2
[34014]=36318,	-- depth:2
[34016]=36316,	-- depth:2
[34017]=34013,	-- depth:3
[34018]=34014,	-- depth:3
[34062]=36316,	-- depth:2
[34063]=36317,	-- depth:2
[34064]=36318,	-- depth:2
[34066]=34062,	-- depth:3
[34067]=34063,	-- depth:3
[34068]=34064,	-- depth:3
[34012]=34016,	-- depth:3
[33913]=33917,	-- depth:3
[33863]=36317,	-- depth:2
[33868]=36318,	-- depth:2
[33764]=36318,	-- depth:2
[33762]=36316,	-- depth:2
[33766]=33762,	-- depth:3
[33767]=33763,	-- depth:3
[33768]=33764,	-- depth:3
[33718]=36318,	-- depth:2
[33717]=36317,	-- depth:2
[33716]=36316,	-- depth:2
[33812]=36316,	-- depth:2
[33912]=33916,	-- depth:3
[33813]=36317,	-- depth:2
[34112]=36316,	-- depth:2
[33814]=36318,	-- depth:2
[33816]=33812,	-- depth:3
[33817]=33813,	-- depth:3
[33818]=33814,	-- depth:3
[33862]=36316,	-- depth:2
[33864]=33868,	-- depth:3
[33866]=33862,	-- depth:3
[33867]=33863,	-- depth:3
[33714]=33718,	-- depth:3
[34113]=36317,	-- depth:2
[34217]=36317,	-- depth:2
[34116]=34112,	-- depth:3
[34318]=36318,	-- depth:2
[34362]=36316,	-- depth:2
[34363]=36317,	-- depth:2
[34364]=36318,	-- depth:2
[34366]=34362,	-- depth:3
[34367]=34363,	-- depth:3
[34368]=34364,	-- depth:3
[34412]=36316,	-- depth:2
[34413]=36317,	-- depth:2
[34414]=36318,	-- depth:2
[34416]=34412,	-- depth:3
[34417]=34413,	-- depth:3
[34418]=34414,	-- depth:3
[34462]=36316,	-- depth:2
[34463]=36317,	-- depth:2
[34464]=36318,	-- depth:2
[34466]=34462,	-- depth:3
[34467]=34463,	-- depth:3
[34468]=34464,	-- depth:3
[34512]=36316,	-- depth:2
[34513]=36317,	-- depth:2
[34317]=36317,	-- depth:2
[34114]=36318,	-- depth:2
[34316]=36316,	-- depth:2
[34313]=34317,	-- depth:3
[34117]=34113,	-- depth:3
[34118]=34114,	-- depth:3
[34162]=36316,	-- depth:2
[34163]=36317,	-- depth:2
[34164]=36318,	-- depth:2
[34166]=34162,	-- depth:3
[34167]=34163,	-- depth:3
[34168]=34164,	-- depth:3
[34212]=36316,	-- depth:2
[34213]=34217,	-- depth:3
[34214]=36318,	-- depth:2
[34216]=34212,	-- depth:3
[33713]=33717,	-- depth:3
[34218]=34214,	-- depth:3
[34262]=36316,	-- depth:2
[34263]=36317,	-- depth:2
[34264]=36318,	-- depth:2
[34266]=34262,	-- depth:3
[34267]=34263,	-- depth:3
[34268]=34264,	-- depth:3
[34312]=34316,	-- depth:3
[34314]=34318,	-- depth:3
[33712]=33716,	-- depth:3
[33566]=36316,	-- depth:2
[33667]=36317,	-- depth:2
[33112]=36316,	-- depth:2
[33113]=36317,	-- depth:2
[33114]=36318,	-- depth:2
[33116]=33112,	-- depth:3
[33117]=33113,	-- depth:3
[33118]=33114,	-- depth:3
[33162]=36316,	-- depth:2
[33163]=36317,	-- depth:2
[33164]=36318,	-- depth:2
[33068]=36318,	-- depth:2
[33166]=33162,	-- depth:3
[33168]=33164,	-- depth:3
[33212]=36316,	-- depth:2
[33213]=36317,	-- depth:2
[33214]=36318,	-- depth:2
[33216]=33212,	-- depth:3
[33217]=33213,	-- depth:3
[33218]=33214,	-- depth:3
[33262]=36316,	-- depth:2
[33263]=36317,	-- depth:2
[33167]=33163,	-- depth:3
[33067]=36317,	-- depth:2
[33066]=36316,	-- depth:2
[33064]=33068,	-- depth:3
[29154]=29004,	-- depth:1
[29134]=29154,	-- depth:2
[29114]=29154,	-- depth:2
[29094]=29154,	-- depth:2
[29074]=29154,	-- depth:2
[29054]=29154,	-- depth:2
[40008]=40564,	-- depth:2
[29034]=29004,	-- depth:1
[29014]=29004,	-- depth:1
[17800]=17687,	-- depth:1
[33012]=36316,	-- depth:2
[33013]=36317,	-- depth:2
[33014]=36318,	-- depth:2
[33016]=33012,	-- depth:3
[33017]=33013,	-- depth:3
[33018]=33014,	-- depth:3
[33062]=33066,	-- depth:3
[33063]=33067,	-- depth:3
[33264]=36318,	-- depth:2
[33266]=33262,	-- depth:3
[33267]=33263,	-- depth:3
[33268]=33264,	-- depth:3
[33513]=36317,	-- depth:2
[33514]=36318,	-- depth:2
[33516]=36316,	-- depth:2
[33517]=33513,	-- depth:3
[33518]=33514,	-- depth:3
[33562]=33566,	-- depth:3
[33563]=36317,	-- depth:2
[33564]=36318,	-- depth:2
[34514]=36318,	-- depth:2
[33567]=33563,	-- depth:3
[33568]=33564,	-- depth:3
[33612]=36316,	-- depth:2
[33613]=36317,	-- depth:2
[33614]=36318,	-- depth:2
[33616]=33612,	-- depth:3
[33617]=33613,	-- depth:3
[33618]=33614,	-- depth:3
[33662]=36316,	-- depth:2
[33663]=33667,	-- depth:3
[33664]=36318,	-- depth:2
[33666]=33662,	-- depth:3
[33512]=33516,	-- depth:3
[33668]=33664,	-- depth:3
[33468]=36318,	-- depth:2
[33466]=36316,	-- depth:2
[33312]=36316,	-- depth:2
[33313]=36317,	-- depth:2
[33314]=36318,	-- depth:2
[33316]=33312,	-- depth:3
[33317]=33313,	-- depth:3
[33318]=33314,	-- depth:3
[33362]=36316,	-- depth:2
[33363]=36317,	-- depth:2
[33364]=36318,	-- depth:2
[33366]=33362,	-- depth:3
[33367]=33363,	-- depth:3
[33368]=33364,	-- depth:3
[33412]=36316,	-- depth:2
[33413]=36317,	-- depth:2
[33414]=36318,	-- depth:2
[33416]=33412,	-- depth:3
[33417]=33413,	-- depth:3
[33418]=33414,	-- depth:3
[33462]=33466,	-- depth:3
[33463]=36317,	-- depth:2
[33464]=33468,	-- depth:3
[33467]=33463,	-- depth:3
[34516]=34512,	-- depth:3
[33964]=33968,	-- depth:3
[34518]=34514,	-- depth:3
[35668]=36318,	-- depth:2
[40227]=40611,	-- depth:3
[35712]=36316,	-- depth:2
[35713]=36317,	-- depth:2
[35714]=36318,	-- depth:2
[35716]=35712,	-- depth:3
[35717]=35713,	-- depth:3
[35718]=35714,	-- depth:3
[35762]=36316,	-- depth:2
[35763]=36317,	-- depth:2
[35764]=36318,	-- depth:2
[35667]=36317,	-- depth:2
[35766]=35762,	-- depth:3
[35768]=35764,	-- depth:3
[35812]=36316,	-- depth:2
[35813]=36317,	-- depth:2
[35814]=36318,	-- depth:2
[35816]=35812,	-- depth:3
[40208]=40008,	-- depth:3
[35817]=35813,	-- depth:3
[35818]=35814,	-- depth:3
[35862]=36316,	-- depth:2
[35863]=36317,	-- depth:2
[35864]=36318,	-- depth:2
[35767]=35763,	-- depth:3
[35866]=35862,	-- depth:3
[35666]=36316,	-- depth:2
[35663]=35667,	-- depth:3
[35464]=36318,	-- depth:2
[35466]=36316,	-- depth:2
[35467]=36317,	-- depth:2
[35468]=35464,	-- depth:3
[35512]=36316,	-- depth:2
[35513]=36317,	-- depth:2
[35514]=36318,	-- depth:2
[35516]=35512,	-- depth:3
[35517]=35513,	-- depth:3
[35518]=35514,	-- depth:3
[35562]=36316,	-- depth:2
[35664]=35668,	-- depth:3
[35563]=36317,	-- depth:2
[35566]=35562,	-- depth:3
[35567]=35563,	-- depth:3
[35568]=36318,	-- depth:2
[35612]=36316,	-- depth:2
[35613]=36317,	-- depth:2
[35614]=36318,	-- depth:2
[35616]=35612,	-- depth:3
[40236]=40032,	-- depth:1
[35617]=35613,	-- depth:3
[35618]=35614,	-- depth:3
[35662]=35666,	-- depth:3
[35564]=35568,	-- depth:3
[35463]=35467,	-- depth:3
[35867]=35863,	-- depth:3
[35868]=35864,	-- depth:3
[36114]=36318,	-- depth:2
[36116]=36316,	-- depth:2
[36117]=36317,	-- depth:2
[36118]=36114,	-- depth:3
[40036]=40236,	-- depth:2
[36162]=36316,	-- depth:2
[36163]=36317,	-- depth:2
[36164]=36318,	-- depth:2
[36166]=36162,	-- depth:3
[36167]=36163,	-- depth:3
[36168]=36164,	-- depth:3
[36113]=36117,	-- depth:3
[36212]=36316,	-- depth:2
[40027]=40227,	-- depth:4
[36214]=36318,	-- depth:2
[36216]=36212,	-- depth:3
[36217]=36317,	-- depth:2
[36218]=36214,	-- depth:3
[36262]=36316,	-- depth:2
[36263]=36317,	-- depth:2
[36264]=36318,	-- depth:2
[36266]=36262,	-- depth:3
[36267]=36263,	-- depth:3
[36268]=36264,	-- depth:3
[36213]=36217,	-- depth:3
[40136]=40036,	-- depth:3
[36112]=36116,	-- depth:3
[40108]=40208,	-- depth:4
[35912]=36316,	-- depth:2
[35913]=36317,	-- depth:2
[35914]=36318,	-- depth:2
[35916]=35912,	-- depth:3
[35917]=35913,	-- depth:3
[35918]=35914,	-- depth:3
[35962]=36316,	-- depth:2
[40127]=40027,	-- depth:5
[35963]=36317,	-- depth:2
[35964]=36318,	-- depth:2
[35966]=35962,	-- depth:3
[36068]=36318,	-- depth:2
[34517]=34513,	-- depth:3
[36012]=36316,	-- depth:2
[36013]=36317,	-- depth:2
[36014]=36318,	-- depth:2
[36016]=36012,	-- depth:3
[36017]=36013,	-- depth:3
[36018]=36014,	-- depth:3
[36062]=36316,	-- depth:2
[36063]=36317,	-- depth:2
[36064]=36068,	-- depth:3
[36066]=36062,	-- depth:3
[36067]=36063,	-- depth:3
[35968]=35964,	-- depth:3
[35462]=35466,	-- depth:3
[35967]=35963,	-- depth:3
[35417]=36317,	-- depth:2
[34714]=36318,	-- depth:2
[34563]=36317,	-- depth:2
[34818]=36318,	-- depth:2
[34562]=36316,	-- depth:2
[34862]=36316,	-- depth:2
[34668]=36318,	-- depth:2
[34712]=36316,	-- depth:2
[34863]=36317,	-- depth:2
[34864]=36318,	-- depth:2
[34866]=34862,	-- depth:3
[34867]=34863,	-- depth:3
[34868]=34864,	-- depth:3
[34912]=36316,	-- depth:2
[34913]=36317,	-- depth:2
[34914]=36318,	-- depth:2
[34617]=36317,	-- depth:2
[34917]=34913,	-- depth:3
[34918]=34914,	-- depth:3
[34962]=36316,	-- depth:2
[34963]=36317,	-- depth:2
[34964]=36318,	-- depth:2
[34966]=34962,	-- depth:3
[34967]=34963,	-- depth:3
[34716]=34712,	-- depth:3
[34817]=36317,	-- depth:2
[34564]=36318,	-- depth:2
[34816]=36316,	-- depth:2
[35418]=36318,	-- depth:2
[34763]=36317,	-- depth:2
[34764]=36318,	-- depth:2
[34618]=36318,	-- depth:2
[34616]=36316,	-- depth:2
[34718]=34714,	-- depth:3
[34614]=34618,	-- depth:3
[34766]=36316,	-- depth:2
[34662]=36316,	-- depth:2
[34663]=36317,	-- depth:2
[34613]=34617,	-- depth:3
[34968]=34964,	-- depth:3
[34612]=34616,	-- depth:3
[34767]=34763,	-- depth:3
[34768]=34764,	-- depth:3
[34717]=36317,	-- depth:2
[34568]=34564,	-- depth:3
[34812]=34816,	-- depth:3
[34567]=34563,	-- depth:3
[34813]=34817,	-- depth:3
[34814]=34818,	-- depth:3
[34666]=34662,	-- depth:3
[34667]=34663,	-- depth:3
[34566]=34562,	-- depth:3
[34664]=34668,	-- depth:3
[35012]=36316,	-- depth:2
[34916]=34912,	-- depth:3
[35014]=36318,	-- depth:2
[35217]=36317,	-- depth:2
[35218]=36318,	-- depth:2
[35013]=36317,	-- depth:2
[35262]=36316,	-- depth:2
[35263]=36317,	-- depth:2
[35264]=36318,	-- depth:2
[35266]=35262,	-- depth:3
[35267]=35263,	-- depth:3
[35268]=35264,	-- depth:3
[35312]=36316,	-- depth:2
[35313]=36317,	-- depth:2
[35314]=36318,	-- depth:2
[35316]=35312,	-- depth:3
[35317]=35313,	-- depth:3
[35318]=35314,	-- depth:3
[35362]=36316,	-- depth:2
[35363]=36317,	-- depth:2
[35364]=36318,	-- depth:2
[35366]=35362,	-- depth:3
[35367]=35363,	-- depth:3
[35368]=35364,	-- depth:3
[35412]=36316,	-- depth:2
[35413]=35417,	-- depth:3
[35414]=35418,	-- depth:3
[35416]=35412,	-- depth:3
[35216]=36316,	-- depth:2
[35214]=35218,	-- depth:3
[34762]=34766,	-- depth:3
[35213]=35217,	-- depth:3
[35016]=35012,	-- depth:3
[35017]=35013,	-- depth:3
[35018]=35014,	-- depth:3
[35062]=36316,	-- depth:2
[35063]=36317,	-- depth:2
[35064]=36318,	-- depth:2
[35066]=35062,	-- depth:3
[35067]=35063,	-- depth:3
[35068]=35064,	-- depth:3
[34713]=34717,	-- depth:3
[35112]=36316,	-- depth:2
[35113]=36317,	-- depth:2
[35116]=35112,	-- depth:3
[35117]=35113,	-- depth:3
[35118]=36318,	-- depth:2
[35114]=35118,	-- depth:3
[35212]=35216,	-- depth:3
[35162]=36316,	-- depth:2
[35163]=36317,	-- depth:2
[35164]=36318,	-- depth:2
[35166]=35162,	-- depth:3
[35168]=35164,	-- depth:3
[35167]=35163,	-- depth:3
[31353]=31351,	-- depth:3
[31403]=31353,	-- depth:4
[29064]=29004,	-- depth:1
[31402]=31401,	-- depth:2
[31302]=31402,	-- depth:3
[31303]=31353,	-- depth:4
[31305]=31405,	-- depth:1
[31304]=31305,	-- depth:2
[31354]=31405,	-- depth:1
[31306]=31305,	-- depth:2
[31356]=31354,	-- depth:2
[29044]=29064,	-- depth:2
[31404]=31405,	-- depth:1
[31352]=31402,	-- depth:3
[31355]=31354,	-- depth:2
[31406]=31405,	-- depth:1
[29024]=29004,	-- depth:1
[31053]=31353,	-- depth:4
[31255]=31305,	-- depth:2
[31006]=31354,	-- depth:2
[31003]=31053,	-- depth:5
[31002]=31352,	-- depth:4
[31052]=31002,	-- depth:5
[31054]=31354,	-- depth:2
[31055]=31054,	-- depth:3
[31056]=31055,	-- depth:4
[29144]=29064,	-- depth:2
[31102]=31002,	-- depth:5
[31103]=31053,	-- depth:5
[31104]=31054,	-- depth:3
[31105]=31104,	-- depth:4
[31106]=31104,	-- depth:4
[29124]=29064,	-- depth:2
[31152]=31002,	-- depth:5
[31153]=31053,	-- depth:5
[31154]=31054,	-- depth:3
[31155]=31154,	-- depth:4
[31156]=31154,	-- depth:4
[29104]=29064,	-- depth:2
[31202]=31002,	-- depth:5
[31203]=31053,	-- depth:5
[31204]=31054,	-- depth:3
[31205]=31204,	-- depth:4
[31206]=31204,	-- depth:4
[29084]=29064,	-- depth:2
[31252]=31302,	-- depth:4
[31253]=31303,	-- depth:5
[31254]=31255,	-- depth:3
[31256]=31255,	-- depth:3
[31452]=31402,	-- depth:3
[31004]=31006,	-- depth:3
[31454]=31405,	-- depth:1
[31854]=31104,	-- depth:4
[31606]=31405,	-- depth:1
[17540]=17490,	-- depth:1
[31652]=31402,	-- depth:3
[31653]=31353,	-- depth:4
[31654]=31606,	-- depth:2
[31655]=31654,	-- depth:3
[31656]=31655,	-- depth:4
[31853]=31653,	-- depth:5
[31852]=31652,	-- depth:4
[31453]=31353,	-- depth:4
[17060]=17120,	-- depth:1
[17061]=17060,	-- depth:2
[31605]=31606,	-- depth:2
[31702]=31652,	-- depth:4
[31704]=31656,	-- depth:5
[31705]=31704,	-- depth:6
[17062]=17060,	-- depth:2
[31706]=31705,	-- depth:7
[31806]=31606,	-- depth:2
[31805]=31806,	-- depth:3
[17330]=17490,	-- depth:1
[31752]=31652,	-- depth:4
[31753]=31653,	-- depth:5
[31754]=31705,	-- depth:7
[31755]=31754,	-- depth:8
[31804]=31854,	-- depth:5
[31756]=31755,	-- depth:9
[31703]=31653,	-- depth:5
[31803]=31653,	-- depth:5
[31855]=31805,	-- depth:4
[31603]=31653,	-- depth:5
[31455]=31454,	-- depth:2
[31456]=31455,	-- depth:3
[31005]=31004,	-- depth:4
[17810]=17800,	-- depth:2
[17809]=17800,	-- depth:2
[31502]=31652,	-- depth:4
[31503]=31653,	-- depth:5
[31504]=31704,	-- depth:6
[31505]=31504,	-- depth:7
[31506]=31505,	-- depth:8
[17808]=17810,	-- depth:3
[31604]=31606,	-- depth:2
[31802]=31652,	-- depth:4
[17807]=17810,	-- depth:3
[31602]=31652,	-- depth:4
[17801]=17810,	-- depth:3
[17802]=17809,	-- depth:3
[17803]=17800,	-- depth:2
[17804]=17807,	-- depth:4
[31556]=31505,	-- depth:8
[31856]=31806,	-- depth:3
[31555]=31556,	-- depth:9
[31554]=31506,	-- depth:9
[31553]=31503,	-- depth:6
[31552]=31502,	-- depth:5
[17805]=17810,	-- depth:3
[17806]=17809,	-- depth:3
[29135]=29005,	-- depth:1
[29145]=29135,	-- depth:2
[29155]=29135,	-- depth:2
[29085]=29135,	-- depth:2
[17210]=17490,	-- depth:1
[29025]=29005,	-- depth:1
[29065]=29085,	-- depth:3
[29075]=29085,	-- depth:3
[17191]=17490,	-- depth:1
[17530]=17191,	-- depth:2
[29105]=29085,	-- depth:3
[17380]=17540,	-- depth:2
[29115]=29135,	-- depth:2
[29125]=29135,	-- depth:2
[29095]=29105,	-- depth:4
[17382]=17380,	-- depth:3
[17383]=17382,	-- depth:4
[17384]=17382,	-- depth:4
[17381]=17380,	-- depth:3
[29055]=29085,	-- depth:3
[29045]=29025,	-- depth:2
[29035]=29025,	-- depth:2
[29015]=29025,	-- depth:2
[17385]=17382,	-- depth:4
[31008]=31454,	-- depth:2
[3060]=3110,	-- depth:1
[2330]=3060,	-- depth:2
[2320]=3170,	-- depth:1
[2310]=2320,	-- depth:2
[2280]=2330,	-- depth:3
[2260]=2280,	-- depth:4
[2240]=2260,	-- depth:5
[2150]=2330,	-- depth:3
[1470]=2330,	-- depth:3
[1390]=1430,	-- depth:1
[1350]=1390,	-- depth:2
[1310]=1350,	-- depth:3
[1280]=1420,	-- depth:1
[1230]=1430,	-- depth:1
[31607]=31008,	-- depth:3
[31560]=31556,	-- depth:9
[31559]=31008,	-- depth:3
[31558]=31008,	-- depth:3
[31557]=31607,	-- depth:4
[3190]=3110,	-- depth:1
[31510]=31560,	-- depth:10
[3210]=3190,	-- depth:2
[3260]=3170,	-- depth:1
[31759]=31559,	-- depth:4
[31757]=31607,	-- depth:4
[31710]=31560,	-- depth:10
[31760]=31710,	-- depth:11
[31709]=31759,	-- depth:5
[31807]=31607,	-- depth:4
[31708]=31558,	-- depth:4
[31707]=31607,	-- depth:4
[31660]=31710,	-- depth:11
[31659]=31759,	-- depth:5
[31658]=31708,	-- depth:5
[31657]=31607,	-- depth:4
[31808]=31708,	-- depth:5
[31809]=31759,	-- depth:5
[31810]=31710,	-- depth:11
[31610]=31710,	-- depth:11
[31857]=31807,	-- depth:5
[31609]=31759,	-- depth:5
[31608]=31708,	-- depth:5
[31007]=31008,	-- depth:3
[3370]=3110,	-- depth:1
[3350]=3260,	-- depth:2
[3300]=3260,	-- depth:2
[3240]=3190,	-- depth:2
[31509]=31559,	-- depth:4
[31758]=31708,	-- depth:5
[31507]=31557,	-- depth:5
[31159]=31559,	-- depth:4
[31210]=31560,	-- depth:10
[185]=100,	-- depth:1
[31209]=31159,	-- depth:5
[31208]=31008,	-- depth:3
[31057]=31007,	-- depth:4
[31207]=31057,	-- depth:5
[31860]=31710,	-- depth:11
[31058]=31008,	-- depth:3
[31059]=31159,	-- depth:5
[31859]=31809,	-- depth:6
[31060]=31210,	-- depth:11
[31107]=31007,	-- depth:4
[31108]=31008,	-- depth:3
[31109]=31159,	-- depth:5
[31508]=31558,	-- depth:4
[31160]=31210,	-- depth:11
[31110]=31210,	-- depth:11
[31157]=31007,	-- depth:4
[31858]=31808,	-- depth:6
[31158]=31008,	-- depth:3
[31010]=31210,	-- depth:11
[31258]=31008,	-- depth:3
[31257]=31057,	-- depth:5
[31407]=31607,	-- depth:4
[31460]=31560,	-- depth:10
[31459]=31559,	-- depth:4
[31458]=31008,	-- depth:3
[31457]=31458,	-- depth:4
[31410]=31560,	-- depth:10
[31409]=31559,	-- depth:4
[31009]=31159,	-- depth:5
[31408]=31008,	-- depth:3
[31259]=31159,	-- depth:5
[31360]=31410,	-- depth:11
[130]=100,	-- depth:1
[31308]=31258,	-- depth:4
[31260]=31210,	-- depth:11
[31358]=31008,	-- depth:3
[31357]=31407,	-- depth:5
[521]=100,	-- depth:1
[31310]=31260,	-- depth:12
[31309]=31259,	-- depth:6
[31359]=31409,	-- depth:5
[31307]=31257,	-- depth:6
[6370]=6390,	-- depth:1
[6350]=6370,	-- depth:2
[6140]=6390,	-- depth:1
[6280]=6390,	-- depth:1
[6260]=6280,	-- depth:2
[6230]=6260,	-- depth:3
[6190]=6390,	-- depth:1
[6170]=6140,	-- depth:2
[6310]=6390,	-- depth:1
[6240]=6260,	-- depth:3
[7590]=6350,	-- depth:3
[6450]=6390,	-- depth:1
[7940]=6280,	-- depth:2
[7920]=6260,	-- depth:3
[7900]=6240,	-- depth:4
[8010]=7590,	-- depth:4
[6130]=6140,	-- depth:2
[8030]=7590,	-- depth:4
[8090]=8070,	-- depth:1
[7850]=6190,	-- depth:2
[7830]=6170,	-- depth:3
[7800]=6140,	-- depth:2
[7790]=6130,	-- depth:3
[7770]=6390,	-- depth:1
[6410]=6390,	-- depth:1
[7710]=8070,	-- depth:1
[7650]=8070,	-- depth:1
[7610]=8030,	-- depth:5
[6810]=6390,	-- depth:1
[6770]=6350,	-- depth:3
[6730]=6310,	-- depth:2
[6700]=6280,	-- depth:2
[6680]=6260,	-- depth:3
[6660]=6240,	-- depth:4
[6650]=6230,	-- depth:4
[6610]=6190,	-- depth:2
[6560]=6140,	-- depth:2
[6530]=7770,	-- depth:2
[6490]=6390,	-- depth:1
[7670]=8090,	-- depth:2
[7870]=7900,	-- depth:5
[5300]=6140,	-- depth:2
[6070]=6490,	-- depth:2
[5420]=6260,	-- depth:3
[5400]=6240,	-- depth:4
[5370]=7870,	-- depth:6
[5350]=6190,	-- depth:2
[5330]=6170,	-- depth:3
[8130]=7710,	-- depth:2
[5290]=6130,	-- depth:3
[5270]=7770,	-- depth:2
[5210]=7710,	-- depth:2
[5170]=8090,	-- depth:2
[5150]=8070,	-- depth:1
[5100]=8030,	-- depth:5
[5080]=7590,	-- depth:4
[5040]=7590,	-- depth:4
[3390]=3110,	-- depth:1
[2100]=1470,	-- depth:4
[1410]=1420,	-- depth:1
[1380]=1390,	-- depth:2
[1195]=1230,	-- depth:2
[540]=521,	-- depth:2
[500]=521,	-- depth:2
[200]=130,	-- depth:2
[170]=10,	-- depth:1
[5440]=6280,	-- depth:2
[6110]=7770,	-- depth:2
[5490]=6390,	-- depth:1
[5530]=6370,	-- depth:2
[6050]=6390,	-- depth:1
[6010]=6390,	-- depth:1
[5990]=6410,	-- depth:2
[5950]=6370,	-- depth:2
[5930]=6350,	-- depth:3
[5910]=5490,	-- depth:2
[5890]=6310,	-- depth:2
[5860]=6280,	-- depth:2
[5840]=6260,	-- depth:3
[5810]=6230,	-- depth:4
[5790]=7870,	-- depth:6
[5770]=6190,	-- depth:2
[5750]=6170,	-- depth:3
[5720]=6140,	-- depth:2
[5710]=6130,	-- depth:3
[5690]=7770,	-- depth:2
[5670]=6390,	-- depth:1
[5650]=6490,	-- depth:2
[5630]=6050,	-- depth:2
[5610]=6450,	-- depth:2
[5590]=6010,	-- depth:2
[5570]=6410,	-- depth:2
[5550]=6390,	-- depth:1
[5510]=6350,	-- depth:3
[8190]=7770,	-- depth:2
[29053]=29054,	-- depth:3
[8210]=6130,	-- depth:3
[8340]=6260,	-- depth:3
[8320]=6240,	-- depth:4
[8360]=6280,	-- depth:2
[8290]=7870,	-- depth:6
[29023]=29053,	-- depth:4
[8270]=6190,	-- depth:2
[29033]=29023,	-- depth:5
[29043]=29023,	-- depth:5
[8510]=8090,	-- depth:2
[29073]=29053,	-- depth:4
[29083]=29053,	-- depth:4
[8250]=6170,	-- depth:3
[29093]=29053,	-- depth:4
[29063]=29053,	-- depth:4
[29103]=29053,	-- depth:4
[8430]=7590,	-- depth:4
[8450]=8030,	-- depth:5
[29113]=29053,	-- depth:4
[29123]=29113,	-- depth:5
[29133]=29113,	-- depth:5
[29143]=29113,	-- depth:5
[8490]=8070,	-- depth:1
[29153]=29113,	-- depth:5
[8220]=6140,	-- depth:2
[7210]=6390,	-- depth:1
[6090]=5670,	-- depth:2
[7170]=7210,	-- depth:2
[6210]=7870,	-- depth:6
[5470]=6310,	-- depth:2
[7250]=7210,	-- depth:2
[6030]=6450,	-- depth:2
[7330]=7250,	-- depth:3
[5970]=6390,	-- depth:1
[17471]=17120,	-- depth:1
[7370]=7250,	-- depth:3
[7490]=7370,	-- depth:4
[7450]=7250,	-- depth:3
[5820]=6240,	-- depth:4
[7120]=6280,	-- depth:2
[7410]=7250,	-- depth:3
[7290]=7250,	-- depth:3
[7100]=6260,	-- depth:3
[8410]=7590,	-- depth:4
[6330]=5490,	-- depth:2
[6630]=7870,	-- depth:6
[6590]=6170,	-- depth:3
[6750]=5490,	-- depth:2
[6550]=6130,	-- depth:3
[6790]=6370,	-- depth:2
[6510]=5670,	-- depth:2
[6850]=6010,	-- depth:2
[6470]=6050,	-- depth:2
[6890]=6050,	-- depth:2
[5390]=6230,	-- depth:4
[8530]=8510,	-- depth:3
[6930]=6090,	-- depth:3
[6430]=6850,	-- depth:3
[6970]=6130,	-- depth:3
[6980]=6140,	-- depth:2
[7010]=6170,	-- depth:3
[7050]=6210,	-- depth:7
[7080]=6240,	-- depth:4
[7530]=7250,	-- depth:3
[8470]=8490,	-- depth:2
[5230]=5210,	-- depth:3
[5190]=8530,	-- depth:4
[7890]=5390,	-- depth:5
[8310]=5390,	-- depth:5
[7730]=5230,	-- depth:4
[3030]=3020,	-- depth:1
[7970]=5040,	-- depth:5
[7990]=8410,	-- depth:5
[7690]=8530,	-- depth:4
[7630]=8470,	-- depth:3
[8050]=8470,	-- depth:3
[17631]=17471,	-- depth:2
[20]=180,	-- depth:1
[7570]=8410,	-- depth:5
[8150]=5230,	-- depth:4
[8390]=7970,	-- depth:6
[40]=20,	-- depth:2
[8110]=8530,	-- depth:4
[5130]=8470,	-- depth:3
[7550]=7970,	-- depth:6
[17000]=17471,	-- depth:2
[6420]=8540,	-- depth:1
[6380]=6420,	-- depth:2
[6400]=6420,	-- depth:2
[6440]=6420,	-- depth:2
[2171]=3020,	-- depth:1
[5380]=6420,	-- depth:2
[2110]=2320,	-- depth:2
[6460]=6420,	-- depth:2
[1270]=1250,	-- depth:1
[6480]=5380,	-- depth:3
[6360]=6480,	-- depth:4
[8520]=8540,	-- depth:1
[6500]=6440,	-- depth:3
[6520]=6480,	-- depth:4
[6540]=6440,	-- depth:3
[110]=40,	-- depth:3
[8500]=8540,	-- depth:1
[6570]=6440,	-- depth:3
[6580]=6480,	-- depth:4
[6620]=6480,	-- depth:4
[2160]=3020,	-- depth:1
[5050]=6480,	-- depth:4
[5110]=5050,	-- depth:5
[6320]=6480,	-- depth:4
[8200]=6540,	-- depth:4
[5850]=6420,	-- depth:2
[5250]=6510,	-- depth:3
[5830]=5850,	-- depth:3
[5260]=6520,	-- depth:5
[6640]=6480,	-- depth:4
[5940]=5850,	-- depth:3
[5730]=6570,	-- depth:4
[5680]=5850,	-- depth:3
[5600]=6500,	-- depth:4
[5560]=6400,	-- depth:3
[5310]=6570,	-- depth:4
[5480]=6320,	-- depth:5
[5320]=6580,	-- depth:5
[5360]=6620,	-- depth:5
[5280]=6540,	-- depth:4
[5240]=8520,	-- depth:2
[5960]=6380,	-- depth:3
[5980]=6400,	-- depth:3
[5090]=5110,	-- depth:6
[6250]=6480,	-- depth:4
[6220]=6640,	-- depth:5
[5140]=8540,	-- depth:1
[6200]=6220,	-- depth:6
[6160]=6580,	-- depth:5
[17190]=17120,	-- depth:1
[6120]=6540,	-- depth:4
[6100]=6420,	-- depth:2
[5160]=8500,	-- depth:2
[5180]=8520,	-- depth:2
[6080]=6500,	-- depth:4
[6060]=6420,	-- depth:2
[6040]=6460,	-- depth:3
[6020]=6440,	-- depth:3
[6000]=6420,	-- depth:2
[5200]=8540,	-- depth:1
[5060]=8410,	-- depth:5
[5800]=5850,	-- depth:3
[5900]=6320,	-- depth:5
[7750]=5250,	-- depth:4
[8480]=5140,	-- depth:2
[7740]=5240,	-- depth:3
[7700]=8540,	-- depth:1
[7680]=8520,	-- depth:2
[7660]=8500,	-- depth:2
[7640]=5140,	-- depth:2
[7620]=5110,	-- depth:6
[7600]=5090,	-- depth:7
[7760]=6520,	-- depth:5
[8400]=5050,	-- depth:5
[7510]=7530,	-- depth:4
[7470]=7510,	-- depth:5
[7430]=7510,	-- depth:5
[7390]=7510,	-- depth:5
[7350]=7510,	-- depth:5
[7310]=7430,	-- depth:6
[7270]=7510,	-- depth:5
[7230]=7510,	-- depth:5
[7560]=5050,	-- depth:5
[7190]=7210,	-- depth:2
[7780]=6120,	-- depth:5
[7810]=6570,	-- depth:4
[8180]=6520,	-- depth:5
[8170]=5250,	-- depth:4
[8160]=5240,	-- depth:3
[8120]=8540,	-- depth:1
[8100]=5180,	-- depth:3
[8230]=6570,	-- depth:4
[8080]=8500,	-- depth:2
[8240]=6580,	-- depth:5
[8330]=6250,	-- depth:5
[8060]=5140,	-- depth:2
[8020]=7600,	-- depth:8
[7980]=5050,	-- depth:5
[8280]=6620,	-- depth:5
[7910]=6250,	-- depth:5
[7880]=6640,	-- depth:5
[8300]=8280,	-- depth:6
[7860]=6620,	-- depth:5
[7820]=6580,	-- depth:5
[8040]=5110,	-- depth:6
[7150]=7170,	-- depth:3
[6910]=6490,	-- depth:2
[6800]=6380,	-- depth:3
[7070]=5390,	-- depth:5
[6830]=6410,	-- depth:2
[6950]=7770,	-- depth:2
[6820]=6400,	-- depth:3
[6870]=6030,	-- depth:3
[6780]=6360,	-- depth:5
[7030]=6190,	-- depth:2
[6740]=6320,	-- depth:5
[8440]=7600,	-- depth:8
[6690]=6480,	-- depth:4
[8460]=5110,	-- depth:6
[6940]=6520,	-- depth:5
[3320]=3370,	-- depth:2
[6920]=6500,	-- depth:4
[8350]=6690,	-- depth:5
[6960]=6540,	-- depth:4
[7000]=6580,	-- depth:5
[3360]=3350,	-- depth:3
[3290]=3320,	-- depth:3
[7060]=6640,	-- depth:5
[5220]=8540,	-- depth:1
[7040]=7060,	-- depth:6
[6340]=5850,	-- depth:3
[3280]=3290,	-- depth:4
[7540]=7060,	-- depth:6
[7520]=7540,	-- depth:7
[5410]=5830,	-- depth:4
[7500]=7540,	-- depth:7
[5520]=5410,	-- depth:5
[5540]=6380,	-- depth:3
[7720]=5220,	-- depth:2
[3180]=3020,	-- depth:1
[3090]=3020,	-- depth:1
[3130]=3180,	-- depth:2
[8140]=5220,	-- depth:2
[6600]=6620,	-- depth:5
[6760]=6340,	-- depth:4
[7930]=8350,	-- depth:6
[1200]=1270,	-- depth:2
[1260]=1270,	-- depth:2
[6840]=6420,	-- depth:2
[1440]=1420,	-- depth:1
[1480]=3020,	-- depth:1
[3140]=3090,	-- depth:2
[1530]=1480,	-- depth:2
[6880]=6460,	-- depth:3
[3005]=3020,	-- depth:1
[3010]=3020,	-- depth:1
[6900]=6480,	-- depth:4
[3040]=3130,	-- depth:3
[3080]=3040,	-- depth:4
[7480]=7500,	-- depth:8
[3100]=3280,	-- depth:5
[3120]=3290,	-- depth:4
[6860]=6440,	-- depth:3
[5580]=6420,	-- depth:2
[6670]=6690,	-- depth:5
[6270]=6900,	-- depth:5
[7400]=7520,	-- depth:8
[7200]=7540,	-- depth:7
[7300]=7540,	-- depth:7
[5700]=6540,	-- depth:4
[7280]=7300,	-- depth:8
[7420]=7400,	-- depth:9
[7460]=7540,	-- depth:7
[17160]=17471,	-- depth:2
[7240]=7520,	-- depth:8
[7440]=7460,	-- depth:8
[7260]=7240,	-- depth:9
[5740]=5940,	-- depth:4
[5780]=5800,	-- depth:4
[5920]=6340,	-- depth:4
[7090]=6900,	-- depth:5
[5660]=6500,	-- depth:4
[5760]=5850,	-- depth:3
[17312]=17120,	-- depth:1
[6180]=6600,	-- depth:6
[7360]=7520,	-- depth:8
[5620]=6460,	-- depth:3
[7320]=7520,	-- depth:8
[7160]=7200,	-- depth:8
[7340]=7320,	-- depth:9
[6150]=7810,	-- depth:5
[5640]=5410,	-- depth:5
[7380]=7360,	-- depth:9
[17340]=17160,	-- depth:3
[2270]=2160,	-- depth:2
[5880]=5760,	-- depth:4
[17652]=17650,	-- depth:1
[5870]=5880,	-- depth:5
[3400]=3390,	-- depth:2
[7840]=6600,	-- depth:6
[17651]=17650,	-- depth:1
[3200]=3020,	-- depth:1
[2130]=2171,	-- depth:2
[3380]=2160,	-- depth:2
[6300]=6480,	-- depth:4
[6990]=5730,	-- depth:5
[3340]=3380,	-- depth:3
[3310]=3020,	-- depth:1
[6290]=6300,	-- depth:5
[2170]=2171,	-- depth:2
[2290]=2270,	-- depth:3
[17659]=17650,	-- depth:1
[8260]=7840,	-- depth:7
[190]=160,	-- depth:1
[5340]=7840,	-- depth:7
[7960]=6300,	-- depth:5
[7950]=7960,	-- depth:6
[90]=40,	-- depth:3
[5430]=5410,	-- depth:5
[6720]=6300,	-- depth:5
[5500]=6340,	-- depth:4
[17661]=17650,	-- depth:1
[8380]=6300,	-- depth:5
[8370]=8380,	-- depth:6
[7020]=7000,	-- depth:6
[7110]=6270,	-- depth:6
[6710]=6720,	-- depth:6
[17171]=17172,	-- depth:1
[17641]=17312,	-- depth:2
[17642]=17639,	-- depth:1
[2300]=2171,	-- depth:2
[7180]=7160,	-- depth:9
[17194]=17193,	-- depth:1
[17170]=17172,	-- depth:1
[8420]=6340,	-- depth:4
[17657]=17650,	-- depth:1
[8000]=8420,	-- depth:5
[7220]=7200,	-- depth:8
[17662]=17650,	-- depth:1
[1500]=2171,	-- depth:2
[1210]=1270,	-- depth:2
[1272]=1250,	-- depth:1
[17655]=17650,	-- depth:1
[17656]=17650,	-- depth:1
[17643]=17650,	-- depth:1
[17660]=17650,	-- depth:1
[7140]=6300,	-- depth:5
[17480]=17641,	-- depth:3
[5070]=8420,	-- depth:5
[17002]=17001,	-- depth:1
[3381]=3390,	-- depth:2
[17617]=17620,	-- depth:1
[7580]=8420,	-- depth:5
[17510]=17639,	-- depth:1
[17511]=17510,	-- depth:2
[5450]=5410,	-- depth:5
[7130]=7140,	-- depth:6
[17003]=17002,	-- depth:2
[17520]=17639,	-- depth:1
[17633]=17639,	-- depth:1
[3230]=3190,	-- depth:2
[3150]=2171,	-- depth:2
[17560]=17639,	-- depth:1
[17637]=17641,	-- depth:3
[17634]=17639,	-- depth:1
[5460]=5450,	-- depth:6
[17482]=17480,	-- depth:4
[17623]=17620,	-- depth:1
[17622]=17623,	-- depth:2
[17231]=17230,	-- depth:1
[17550]=17210,	-- depth:2
[17600]=17312,	-- depth:2
[1193]=1270,	-- depth:2
[17602]=17600,	-- depth:3
[17236]=17230,	-- depth:1
[17604]=17600,	-- depth:3
[1360]=1350,	-- depth:3
[17606]=17604,	-- depth:4
[17483]=17482,	-- depth:5
[17624]=17623,	-- depth:2
[17625]=17623,	-- depth:2
[17645]=17643,	-- depth:2
[2180]=3150,	-- depth:3
[17030]=17001,	-- depth:1
[17629]=17623,	-- depth:2
[3250]=3190,	-- depth:2
[3000]=3005,	-- depth:2
[17234]=17230,	-- depth:1
[17235]=17236,	-- depth:2
[17233]=17230,	-- depth:1
[17627]=17623,	-- depth:2
[17626]=17623,	-- depth:2
[17653]=17657,	-- depth:2
[17628]=17623,	-- depth:2
[17232]=17230,	-- depth:1
[17603]=17602,	-- depth:4
[17481]=17480,	-- depth:4
[1450]=1420,	-- depth:1
[17607]=17604,	-- depth:4
[3270]=3360,	-- depth:4
[2250]=2240,	-- depth:6
[17605]=17604,	-- depth:4
[17654]=17658,	-- depth:1
[1273]=1272,	-- depth:2
[17601]=17600,	-- depth:3
[17644]=17643,	-- depth:2
[17621]=17623,	-- depth:2
[1490]=1500,	-- depth:3
[900]=870,	-- depth:1
[940]=911,	-- depth:1
[952]=940,	-- depth:2
[980]=870,	-- depth:1
[880]=940,	-- depth:2
[120]=140,	-- depth:1
[960]=940,	-- depth:2
[2120]=3150,	-- depth:3
[860]=870,	-- depth:1
[520]=550,	-- depth:1
[3382]=3150,	-- depth:3
[831]=820,	-- depth:1
[510]=550,	-- depth:1
[570]=530,	-- depth:1
[810]=890,	-- depth:1
[830]=890,	-- depth:1
[930]=890,	-- depth:1
[970]=830,	-- depth:2
[850]=970,	-- depth:3
[150]=180,	-- depth:1
[910]=890,	-- depth:1
[990]=890,	-- depth:1
[950]=930,	-- depth:2
[920]=930,	-- depth:2
[921]=930,	-- depth:2
[1000]=890,	-- depth:1
},
hide_npc={
[10208]={npc_id=10208,},
[10212]={npc_id=10212,},
[10213]={npc_id=10213,},
[10207]={npc_id=10207,sections="550,1;570,3",},
[10135]={npc_id=10135,sections="30,2;60,3",},
[10103]={npc_id=10103,sections="130,2;150,3",},
[10204]={npc_id=10204,sections="530,2;560,3",},
[10303]={npc_id=10303,sections="570,2;810,2",}
},

hide_npc_meta_table_map={
},
hide_gather={
[1303]={gather_id=1303,},
[1000]={gather_id=1000,sections="1520,1;2010,3",},
[1509]={gather_id=1509,sections="3340,1;3360,3",},
[1510]={gather_id=1510,sections="3382,1;3400,3",},
[1511]={gather_id=1511,sections="3180,1;3200,3",},
[1301]={gather_id=1301,sections="1200,3;1240,2",},
[1305]={gather_id=1305,sections="1200,3;1290,3",}
},

hide_gather_meta_table_map={
},
push={
[1]={index=1,},
[2]={index=2,param1=860,param2=3,view_url="qifu",open_param=30,},
[3]={index=3,param1=1340,view_url="LongZhuView",},
[4]={index=4,param1=1410,param2=1,view_url="XiuZhenRoadView",},
[5]={index=5,param1=1290,view_url="BossNewPrivilegeView",open_param=40,},
[6]={index=6,param1=910,view_url="ProfessWallView",open_param=10,}
},

push_meta_table_map={
[6]=2,	-- depth:1
},
weather_effect={
{}
},

weather_effect_meta_table_map={
},
dice_task={
{id=1,opponent_name="姜灵儿",opp_end_lose_dec="我居然输了QAQ",my_win_dec="(≧∇≦)ﾉ",my_fail_dec="可恶！我大意了！",},
{win_rate=0,opp_start_dec="骰子我从小就会，你可赢不了我",my_pre_dec="来吧",},
{rounds=2,show_cheats=1,opp_start_dec="少侠，我能赢一次，就能赢你第二次",my_pre_dec="这可不一定，看我的",},
{id=3,opp_start_dec="检验我闭关成果的时候到了！",my_pre_dec="有进步（摸摸头）",my_win_dec="噢耶！",my_fail_dec="QAQ 又输了……",}
},

dice_task_meta_table_map={
},
change_res={
{},
{parm=2,}
},

change_res_meta_table_map={
},
change_mount_res={
{}
},

change_mount_res_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
task_sort={
{},
{task_type=3,s10001=9900,s10002=9900,s10003=9900,s10004=9900,s10005=9900,s10006=9900,s10007=9900,s10008=9900,s10009=9900,s10010=9900,s10011=9900,s10012=9900,s10013=9900,s10014=9900,s10015=9900,s10016=9900,s10017=9900,s10018=9900,s10019=9900,s10020=9900,s10021=9900,s10022=9900,s10023=9900,},
{task_type=0,s10001=9800,s10002=9800,s10003=9800,s10004=9800,s10005=9800,s10006=9800,s10007=9800,s10008=9800,s10009=9800,s10010=9800,s10011=9800,s10012=9800,s10013=9800,s10014=9800,s10015=9800,s10016=9800,s10017=9800,s10018=9800,s10019=9800,s10020=9800,s10021=9800,s10022=9800,s10023=9800,},
{task_type=2,s10001=9700,s10002=9700,s10003=9700,s10004=9700,s10005=9700,s10006=9700,s10007=9700,s10008=9700,s10009=9700,s10010=9700,s10011=9700,s10012=9700,s10013=9700,s10014=9700,s10015=9700,s10016=9700,s10017=9700,s10018=9700,s10019=9700,s10020=9700,s10021=9700,s10022=9700,s10023=9700,},
{task_type=9,s10001=9600,s10002=9600,s10003=9600,s10004=9600,s10005=9600,s10006=9600,s10007=9600,s10008=9600,s10009=9600,s10010=9600,s10011=9600,s10012=9600,s10013=9600,s10014=9600,s10015=9600,s10016=9600,s10017=9600,s10018=9600,s10019=9600,s10020=9600,s10021=9600,s10022=9600,s10023=9600,},
{task_type=4,s10001=9500,s10002=9500,s10003=9500,s10004=9500,s10005=9500,s10006=9500,s10007=9500,s10008=9500,s10009=9500,s10010=9500,s10011=9500,s10012=9500,s10013=9500,s10014=9500,s10015=9500,s10016=9500,s10017=9500,s10018=9500,s10019=9500,s10020=9500,s10021=9500,s10022=9500,s10023=9500,},
{task_type=146,s10001=9400,s10002=9400,s10003=9400,s10004=9400,s10005=9400,s10006=9400,s10007=9400,s10008=9400,s10009=9400,s10010=9400,s10011=9400,s10012=9400,s10013=9400,s10014=9400,s10015=9400,s10016=9400,s10017=9400,s10018=9400,s10019=9400,s10020=9400,s10021=9400,s10022=9400,s10023=9400,},
{task_type=143,s10001=9300,s10002=9300,s10003=9300,s10004=9300,s10005=9300,s10006=9300,s10007=9300,s10008=9300,s10009=9300,s10010=9300,s10011=9300,s10012=9300,s10013=9300,s10014=9300,s10015=9300,s10016=9300,s10017=9300,s10018=9300,s10019=9300,s10020=9300,s10021=9300,s10022=9300,s10023=9300,},
{task_type=147,s10001=9200,s10002=9200,s10003=9200,s10004=9200,s10005=9200,s10006=9200,s10007=9200,s10008=9200,s10009=9200,s10010=9200,s10011=9200,s10012=9200,s10013=9200,s10014=9200,s10015=9200,s10016=9200,s10017=9200,s10018=9200,s10019=9200,s10020=9200,s10021=9200,s10022=9200,s10023=9200,},
{task_type=141,s10001=9100,s10002=9100,s10003=9100,s10004=9100,s10005=9100,s10006=9100,s10007=9100,s10008=9100,s10009=9100,s10010=9100,s10011=9100,s10012=9100,s10013=9100,s10014=9100,s10015=9100,s10016=9100,s10017=9100,s10018=9100,s10019=9100,s10020=9100,s10021=9100,s10022=9100,s10023=9100,},
{task_type=114,s10001=9750,s10002=9750,s10003=9750,s10004=9750,s10005=9750,s10006=9750,s10007=9750,s10008=9750,s10009=9750,s10010=9750,s10011=9750,s10012=9750,s10013=9750,s10014=9750,s10015=9750,s10016=9750,s10017=9750,s10018=9750,s10019=9750,s10020=9750,s10021=9750,s10022=9750,s10023=9750,},
{task_type=142,s10001=8900,s10002=8900,s10003=8900,s10004=8900,s10005=8900,s10006=8900,s10007=8900,s10008=8900,s10009=8900,s10010=8900,s10011=8900,s10012=8900,s10013=8900,s10014=8900,s10015=8900,s10016=8900,s10017=8900,s10018=8900,s10019=8900,s10020=8900,s10021=8900,s10022=8900,s10023=8900,},
{task_type=131,s10001=8800,s10002=8800,s10003=8800,s10004=8800,s10005=8800,s10006=8800,s10007=8800,s10008=8800,s10009=8800,s10010=8800,s10011=8800,s10012=8800,s10013=8800,s10014=8800,s10015=8800,s10016=8800,s10017=8800,s10018=8800,s10019=8800,s10020=8800,s10021=8800,s10022=8800,s10023=8800,},
{task_type=133,s10001=8700,s10002=8700,s10003=8700,s10004=8700,s10005=8700,s10006=8700,s10007=8700,s10008=8700,s10009=8700,s10010=8700,s10011=8700,s10012=8700,s10013=8700,s10014=8700,s10015=8700,s10016=8700,s10017=8700,s10018=8700,s10019=8700,s10020=8700,s10021=8700,s10022=8700,s10023=8700,},
{task_type=138,s10001=8600,s10002=8600,s10003=8600,s10004=8600,s10005=8600,s10006=8600,s10007=8600,s10008=8600,s10009=8600,s10010=8600,s10011=8600,s10012=8600,s10013=8600,s10014=8600,s10015=8600,s10016=8600,s10017=8600,s10018=8600,s10019=8600,s10020=8600,s10021=8600,s10022=8600,s10023=8600,},
{task_type=139,s10001=8500,s10002=8500,s10003=8500,s10004=8500,s10005=8500,s10006=8500,s10007=8500,s10008=8500,s10009=8500,s10010=8500,s10011=8500,s10012=8500,s10013=8500,s10014=8500,s10015=8500,s10016=8500,s10017=8500,s10018=8500,s10019=8500,s10020=8500,s10021=8500,s10022=8500,s10023=8500,},
{task_type=144,s10001=8400,s10002=8400,s10003=8400,s10004=8400,s10005=8400,s10006=8400,s10007=8400,s10008=8400,s10009=8400,s10010=8400,s10011=8400,s10012=8400,s10013=8400,s10014=8400,s10015=8400,s10016=8400,s10017=8400,s10018=8400,s10019=8400,s10020=8400,s10021=8400,s10022=8400,s10023=8400,},
{task_type=148,s10001=8300,s10002=8300,s10003=8300,s10004=8300,s10005=8300,s10006=8300,s10007=8300,s10008=8300,s10009=8300,s10010=8300,s10011=8300,s10012=8300,s10013=8300,s10014=8300,s10015=8300,s10016=8300,s10017=8300,s10018=8300,s10019=8300,s10020=8300,s10021=8300,s10022=8300,s10023=8300,},
{task_type=149,s10001=8200,s10002=8200,s10003=8200,s10004=8200,s10005=8200,s10006=8200,s10007=8200,s10008=8200,s10009=8200,s10010=8200,s10011=8200,s10012=8200,s10013=8200,s10014=8200,s10015=8200,s10016=8200,s10017=8200,s10018=8200,s10019=8200,s10020=8200,s10021=8200,s10022=8200,s10023=8200,},
{task_type=151,s10001=8100,s10002=8100,s10003=8100,s10004=8100,s10005=8100,s10006=8100,s10007=8100,s10008=8100,s10009=8100,s10010=8100,s10011=8100,s10012=8100,s10013=8100,s10014=8100,s10015=8100,s10016=8100,s10017=8100,s10018=8100,s10019=8100,s10020=8100,s10021=8100,s10022=8100,s10023=8100,},
{task_type=152,s10001=8000,s10002=8000,s10003=8000,s10004=8000,s10005=8000,s10006=8000,s10007=8000,s10008=8000,s10009=8000,s10010=8000,s10011=8000,s10012=8000,s10013=8000,s10014=8000,s10015=8000,s10016=8000,s10017=8000,s10018=8000,s10019=8000,s10020=8000,s10021=8000,s10022=8000,s10023=8000,},
{task_type=153,s10001=7900,s10002=7900,s10003=7900,s10004=7900,s10005=7900,s10006=7900,s10007=7900,s10008=7900,s10009=7900,s10010=7900,s10011=7900,s10012=7900,s10013=7900,s10014=7900,s10015=7900,s10016=7900,s10017=7900,s10018=7900,s10019=7900,s10020=7900,s10021=7900,s10022=7900,s10023=7900,},
{task_type=159,s10001=7800,s10002=7800,s10003=7800,s10004=7800,s10005=7800,s10006=7800,s10007=7800,s10008=7800,s10009=7800,s10010=7800,s10011=7800,s10012=7800,s10013=7800,s10014=7800,s10015=7800,s10016=7800,s10017=7800,s10018=7800,s10019=7800,s10020=7800,s10021=7800,s10022=7800,s10023=7800,},
{task_type=160,s10001=7700,s10002=7700,s10003=7700,s10004=7700,s10005=7700,s10006=7700,s10007=7700,s10008=7700,s10009=7700,s10010=7700,s10011=7700,s10012=7700,s10013=7700,s10014=7700,s10015=7700,s10016=7700,s10017=7700,s10018=7700,s10019=7700,s10020=7700,s10021=7700,s10022=7700,s10023=7700,},
{task_type=161,s10001=7600,s10002=7600,s10003=7600,s10004=7600,s10005=7600,s10006=7600,s10007=7600,s10008=7600,s10009=7600,s10010=7600,s10011=7600,s10012=7600,s10013=7600,s10014=7600,s10015=7600,s10016=7600,s10017=7600,s10018=7600,s10019=7600,s10020=7600,s10021=7600,s10022=7600,s10023=7600,},
{task_type=162,s10001=7500,s10002=7500,s10003=7500,s10004=7500,s10005=7500,s10006=7500,s10007=7500,s10008=7500,s10009=7500,s10010=7500,s10011=7500,s10012=7500,s10013=7500,s10014=7500,s10015=7500,s10016=7500,s10017=7500,s10018=7500,s10019=7500,s10020=7500,s10021=7500,s10022=7500,s10023=7500,},
{task_type=163,s10001=7400,s10002=7400,s10003=7400,s10004=7400,s10005=7400,s10006=7400,s10007=7400,s10008=7400,s10009=7400,s10010=7400,s10011=7400,s10012=7400,s10013=7400,s10014=7400,s10015=7400,s10016=7400,s10017=7400,s10018=7400,s10019=7400,s10020=7400,s10021=7400,s10022=7400,s10023=7400,},
{task_type=166,s10001=7300,s10002=7300,s10003=7300,s10004=7300,s10005=7300,s10006=7300,s10007=7300,s10008=7300,s10009=7300,s10010=7300,s10011=7300,s10012=7300,s10013=7300,s10014=7300,s10015=7300,s10016=7300,s10017=7300,s10018=7300,s10019=7300,s10020=7300,s10021=7300,s10022=7300,s10023=7300,},
{task_type=167,s10001=7200,s10002=7200,s10003=7200,s10004=7200,s10005=7200,s10006=7200,s10007=7200,s10008=7200,s10009=7200,s10010=7200,s10011=7200,s10012=7200,s10013=7200,s10014=7200,s10015=7200,s10016=7200,s10017=7200,s10018=7200,s10019=7200,s10020=7200,s10021=7200,s10022=7200,s10023=7200,}
},

task_sort_meta_table_map={
},
section_level={
{},
{sections_id=10002,min_level=10,max_level=16,},
{sections_id=10003,min_level=17,max_level=24,},
{sections_id=10004,min_level=25,max_level=29,},
{sections_id=10005,min_level=30,max_level=34,},
{sections_id=10006,min_level=35,max_level=49,},
{sections_id=10007,min_level=50,max_level=55,},
{sections_id=10008,min_level=56,max_level=56,},
{sections_id=10009,min_level=57,max_level=57,},
{sections_id=10010,min_level=58,max_level=59,},
{sections_id=10011,min_level=60,max_level=61,},
{sections_id=10012,min_level=62,max_level=64,},
{sections_id=10013,min_level=65,max_level=66,},
{sections_id=10014,min_level=67,max_level=69,},
{sections_id=10015,min_level=70,max_level=74,},
{sections_id=10016,min_level=75,max_level=89,},
{sections_id=10017,min_level=90,max_level=99,},
{sections_id=10018,min_level=100,max_level=119,},
{sections_id=10019,min_level=120,max_level=124,},
{sections_id=10020,min_level=125,max_level=129,},
{sections_id=10021,min_level=130,max_level=149,},
{sections_id=10022,min_level=150,max_level=189,},
{sections_id=10023,min_level=190,max_level=999999,}
},

section_level_meta_table_map={
},
first_daily_task={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

first_daily_task_meta_table_map={
},
daily_task_reward={
[1]={level=1,},
[80]={level=80,level_max=89,},
[90]={level=90,level_max=99,},
[100]={level=100,level_max=109,},
[110]={level=110,level_max=119,},
[120]={level=120,level_max=129,},
[130]={level=130,level_max=139,},
[140]={level=140,level_max=149,},
[150]={level=150,level_max=159,},
[160]={level=160,level_max=169,},
[170]={level=170,level_max=179,},
[180]={level=180,level_max=189,},
[190]={level=190,level_max=199,},
[200]={level=200,level_max=209,},
[210]={level=210,level_max=219,},
[220]={level=220,level_max=229,},
[230]={level=230,level_max=239,},
[240]={level=240,level_max=249,},
[250]={level=250,level_max=259,},
[260]={level=260,level_max=269,},
[270]={level=270,level_max=279,},
[280]={level=280,level_max=289,},
[290]={level=290,level_max=299,},
[300]={level=300,level_max=309,},
[310]={level=310,level_max=319,},
[320]={level=320,level_max=329,},
[330]={level=330,level_max=339,},
[340]={level=340,level_max=349,},
[350]={level=350,level_max=359,},
[360]={level=360,level_max=369,},
[370]={level=370,level_max=379,},
[380]={level=380,level_max=389,},
[390]={level=390,level_max=399,},
[400]={level=400,level_max=409,},
[410]={level=410,level_max=419,},
[420]={level=420,level_max=429,},
[430]={level=430,level_max=439,},
[440]={level=440,level_max=449,},
[450]={level=450,level_max=459,},
[460]={level=460,level_max=469,},
[470]={level=470,level_max=479,},
[480]={level=480,level_max=489,},
[490]={level=490,level_max=499,},
[500]={level=500,level_max=509,},
[510]={level=510,level_max=519,},
[520]={level=520,level_max=529,},
[530]={level=530,level_max=539,},
[540]={level=540,level_max=549,},
[550]={level=550,level_max=559,},
[560]={level=560,level_max=569,},
[570]={level=570,level_max=579,},
[580]={level=580,level_max=589,},
[590]={level=590,level_max=599,},
[600]={level=600,level_max=609,},
[610]={level=610,level_max=619,},
[620]={level=620,level_max=629,},
[630]={level=630,level_max=639,},
[640]={level=640,level_max=649,},
[650]={level=650,level_max=659,},
[660]={level=660,level_max=669,},
[670]={level=670,level_max=679,},
[680]={level=680,level_max=689,},
[690]={level=690,level_max=699,},
[700]={level=700,level_max=709,},
[710]={level=710,level_max=719,},
[720]={level=720,level_max=729,},
[730]={level=730,level_max=739,},
[740]={level=740,level_max=749,},
[750]={level=750,level_max=759,},
[760]={level=760,level_max=769,},
[770]={level=770,level_max=779,},
[780]={level=780,level_max=789,},
[790]={level=790,level_max=799,},
[800]={level=800,level_max=2000,}
},

daily_task_reward_meta_table_map={
},
first_guild_task={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

first_guild_task_meta_table_map={
},
guild_task_reward={
[145]={level=145,},
[150]={level=150,level_max=159,},
[160]={level=160,level_max=169,},
[170]={level=170,level_max=179,},
[180]={level=180,level_max=189,},
[190]={level=190,level_max=199,},
[200]={level=200,level_max=209,},
[210]={level=210,level_max=219,},
[220]={level=220,level_max=229,},
[230]={level=230,level_max=239,},
[240]={level=240,level_max=249,},
[250]={level=250,level_max=259,},
[260]={level=260,level_max=269,},
[270]={level=270,level_max=279,},
[280]={level=280,level_max=289,},
[290]={level=290,level_max=299,},
[300]={level=300,level_max=309,},
[310]={level=310,level_max=319,},
[320]={level=320,level_max=329,},
[330]={level=330,level_max=339,},
[340]={level=340,level_max=349,},
[350]={level=350,level_max=359,},
[360]={level=360,level_max=369,},
[370]={level=370,level_max=379,},
[380]={level=380,level_max=389,},
[390]={level=390,level_max=399,},
[400]={level=400,level_max=409,},
[410]={level=410,level_max=419,},
[420]={level=420,level_max=429,},
[430]={level=430,level_max=439,},
[440]={level=440,level_max=449,},
[450]={level=450,level_max=459,},
[460]={level=460,level_max=469,},
[470]={level=470,level_max=479,},
[480]={level=480,level_max=489,},
[490]={level=490,level_max=499,},
[500]={level=500,level_max=509,},
[510]={level=510,level_max=519,},
[520]={level=520,level_max=529,},
[530]={level=530,level_max=539,},
[540]={level=540,level_max=549,},
[550]={level=550,level_max=559,},
[560]={level=560,level_max=569,},
[570]={level=570,level_max=579,},
[580]={level=580,level_max=589,},
[590]={level=590,level_max=599,},
[600]={level=600,level_max=609,},
[610]={level=610,level_max=619,},
[620]={level=620,level_max=629,},
[630]={level=630,level_max=639,},
[640]={level=640,level_max=649,},
[650]={level=650,level_max=659,},
[660]={level=660,level_max=669,},
[670]={level=670,level_max=679,},
[680]={level=680,level_max=689,},
[690]={level=690,level_max=699,},
[700]={level=700,level_max=709,},
[710]={level=710,level_max=719,},
[720]={level=720,level_max=729,},
[730]={level=730,level_max=739,},
[740]={level=740,level_max=749,},
[750]={level=750,level_max=759,},
[760]={level=760,level_max=769,},
[770]={level=770,level_max=779,},
[780]={level=780,level_max=789,},
[790]={level=790,level_max=799,},
[800]={level=800,level_max=2000,}
},

guild_task_reward_meta_table_map={
},
guild_task_mythicallist={
{},
{society_level=2,shenshogongpin=20,},
{society_level=3,shenshogongpin=25,},
{society_level=4,shenshogongpin=30,finish_gongpin=55,},
{society_level=5,shenshogongpin=35,finish_gongpin=60,},
{society_level=6,shenshogongpin=40,finish_gongpin=65,},
{society_level=7,shenshogongpin=45,finish_gongpin=70,},
{society_level=8,shenshogongpin=50,finish_gongpin=75,},
{society_level=9,shenshogongpin=55,finish_gongpin=80,},
{society_level=10,shenshogongpin=60,finish_gongpin=85,}
},

guild_task_mythicallist_meta_table_map={
},
guild_task_list={
{},
{first_task=33051,end_task=33068,},
{first_task=33101,end_task=33118,},
{first_task=33151,end_task=33168,},
{first_task=33201,end_task=33218,},
{first_task=33251,end_task=33268,},
{first_task=33301,end_task=33318,},
{first_task=33351,end_task=33368,},
{first_task=33401,end_task=33418,},
{first_task=33451,end_task=33468,},
{first_task=33501,end_task=33518,},
{first_task=33551,end_task=33568,},
{first_task=33601,end_task=33618,},
{first_task=33651,end_task=33668,},
{first_task=33701,end_task=33718,},
{first_task=33751,end_task=33768,},
{first_task=33801,end_task=33818,},
{first_task=33851,end_task=33868,},
{first_task=33901,end_task=33918,},
{first_task=33951,end_task=33968,},
{first_task=34001,end_task=34018,},
{first_task=34051,end_task=34068,},
{first_task=34101,end_task=34118,},
{first_task=34151,end_task=34168,},
{first_task=34201,end_task=34218,},
{first_task=34251,end_task=34268,},
{first_task=34301,end_task=34318,},
{first_task=34351,end_task=34368,},
{first_task=34401,end_task=34418,},
{first_task=34451,end_task=34468,},
{first_task=34501,end_task=34518,},
{first_task=34551,end_task=34568,},
{first_task=34601,end_task=34618,},
{first_task=34651,end_task=34668,},
{first_task=34701,end_task=34718,},
{first_task=34751,end_task=34768,},
{first_task=34801,end_task=34818,},
{first_task=34851,end_task=34868,},
{first_task=34901,end_task=34918,},
{first_task=34951,end_task=34968,},
{first_task=35001,end_task=35018,},
{first_task=35051,end_task=35068,},
{first_task=35101,end_task=35118,},
{first_task=35151,end_task=35168,},
{first_task=35201,end_task=35218,},
{first_task=35251,end_task=35268,},
{first_task=35301,end_task=35318,},
{first_task=35351,end_task=35368,},
{first_task=35401,end_task=35418,},
{first_task=35451,end_task=35468,},
{first_task=35501,end_task=35518,},
{first_task=35551,end_task=35568,},
{first_task=35601,end_task=35618,},
{first_task=35651,end_task=35668,},
{first_task=35701,end_task=35718,},
{first_task=35751,end_task=35768,},
{first_task=35801,end_task=35818,},
{first_task=35851,end_task=35868,},
{first_task=35901,end_task=35918,},
{first_task=35951,end_task=35968,},
{first_task=36001,end_task=36018,},
{first_task=36051,end_task=36068,},
{first_task=36101,end_task=36118,},
{first_task=36151,end_task=36168,},
{first_task=36201,end_task=36218,},
{first_task=36251,end_task=36268,},
{first_task=36301,end_task=36318,}
},

guild_task_list_meta_table_map={
},
zhaunzhi_task_list={
{},
{zhuanzhi_level=2,first_task=29014,end_task=29015,},
{zhuanzhi_level=3,first_task=29023,end_task=29025,},
{zhuanzhi_level=4,first_task=29033,end_task=29035,},
{zhuanzhi_level=5,first_task=29043,end_task=29045,},
{zhuanzhi_level=6,first_task=29053,end_task=29055,},
{zhuanzhi_level=7,first_task=29063,end_task=29065,},
{zhuanzhi_level=8,first_task=29073,end_task=29075,},
{zhuanzhi_level=9,first_task=29083,end_task=29085,},
{zhuanzhi_level=10,first_task=29093,end_task=29095,},
{zhuanzhi_level=11,first_task=29103,end_task=29105,},
{zhuanzhi_level=12,first_task=29113,end_task=29115,},
{zhuanzhi_level=13,first_task=29123,end_task=29125,},
{zhuanzhi_level=14,first_task=29133,end_task=29135,},
{zhuanzhi_level=15,first_task=29143,end_task=29145,},
{zhuanzhi_level=16,first_task=29153,end_task=29155,}
},

zhaunzhi_task_list_meta_table_map={
},
monst_level={
{monst_name="魔族余孽",monst_id=10103,monst_x=101,monst_y=155,},
{min_level=1,max_level=30,monst_name="魔族影卫",monst_id=10105,scene_id=1001,monst_x=302,monst_y=197,},
{monst_name="寒冰魔傀",monst_id=10202,monst_x=250,monst_y=314,},
{monst_name="雪山冰猿",monst_id=10203,monst_x=262,monst_y=372,},
{monst_name="魔化雪人",monst_id=10204,monst_x=118,monst_y=258,},
{monst_name="魔族影卫",monst_id=10205,monst_x=121,monst_y=196,},
{monst_id=10206,scene_id=1002,monst_x=371,monst_y=147,},
{min_level=91,max_level=94,monst_name="魔瘴妖灯",monst_id=10401,scene_id=1008,monst_x=603,monst_y=412,},
{min_level=95,max_level=98,monst_id=10402,scene_id=1008,monst_x=608,monst_y=347,},
{min_level=99,max_level=100,monst_name="噬魂恶鬼",monst_id=10405,scene_id=1008,monst_x=609,monst_y=261,},
{min_level=101,max_level=105,monst_name="魔族小队",monst_id=10408,scene_id=1008,monst_x=623,},
{min_level=106,max_level=110,monst_name="冥王守卫",monst_id=10405,scene_id=1008,monst_x=727,monst_y=176,},
{min_level=111,max_level=120,monst_name="追击魔物",monst_id=10410,scene_id=1008,monst_x=671,monst_y=130,},
{min_level=121,max_level=125,monst_name="净天教徒",monst_id=10414,scene_id=1008,monst_x=627,monst_y=127,},
{min_level=126,max_level=130,monst_name="迷魂魔物",monst_id=10416,scene_id=1008,monst_x=503,monst_y=233,},
{min_level=130,max_level=135,monst_name="迷魂魔物",monst_id=10411,scene_id=1008,monst_x=437,monst_y=214,},
{min_level=136,max_level=137,monst_name="潜藏教徒",monst_id=10418,scene_id=1008,monst_x=406,monst_y=224,},
{min_level=138,max_level=140,monst_name="魔尊亲随",monst_id=10419,scene_id=1008,monst_x=391,monst_y=297,},
{monst_name="镜湖水怪",monst_id=10501,monst_x=334,monst_y=431,},
{min_level=141,max_level=150,monst_name="镜湖水妖",monst_id=10502,scene_id=1005,monst_x=278,monst_y=423,},
{monst_name="镜湖水魔",monst_id=10503,monst_x=201,monst_y=386,},
{min_level=151,max_level=160,monst_name="熔岩蜘蛛",monst_id=10504,scene_id=1005,monst_x=138,monst_y=374,},
{min_level=161,max_level=170,monst_name="神灵傀儡",monst_id=10505,scene_id=1005,monst_x=249,monst_y=329,},
{monst_name="凶猛野猪",monst_id=10506,monst_x=108,monst_y=350,},
{min_level=171,max_level=180,monst_name="罗汉僧人",monst_id=10507,scene_id=1005,monst_x=119,monst_y=323,},
{monst_name="魅惑女妖",monst_id=10508,monst_x=149,monst_y=229,},
{min_level=181,max_level=190,monst_name="财宝盗匪",monst_id=10509,scene_id=1005,monst_x=184,monst_y=185,},
{monst_name="幻影魔卫",monst_id=10510,monst_x=214,monst_y=267,},
{min_level=191,max_level=200,monst_name="幽冥鬼怪",monst_id=10511,scene_id=1005,monst_x=224,},
{monst_name="魂迷鬼灯",monst_id=10512,monst_x=276,monst_y=158,},
{monst_name="狱血侍卫",monst_id=10513,monst_x=335,monst_y=189,},
{min_level=201,max_level=210,monst_name="魔气聚合体",monst_id=10514,scene_id=1005,monst_x=320,monst_y=376,},
{monst_name="魔气聚合体",monst_id=10515,monst_x=377,monst_y=377,},
{min_level=211,max_level=220,monst_name="暴食",monst_id=10516,scene_id=1005,monst_x=440,monst_y=369,},
{monst_name="问心心魔",monst_id=10517,monst_x=489,monst_y=271,},
{min_level=221,max_level=230,monst_id=10518,scene_id=1005,monst_x=465,monst_y=248,},
{min_level=231,max_level=245,monst_name="精灵石妖",monst_id=10600,scene_id=1006,monst_x=439,monst_y=415,},
{min_level=245,max_level=250,monst_name="树灵魔怪",monst_id=10601,scene_id=1006,monst_x=401,monst_y=424,},
{min_level=251,max_level=255,monst_id=10602,scene_id=1006,monst_x=349,monst_y=454,},
{min_level=256,max_level=260,monst_name="嗜血妖蛇",monst_id=10603,scene_id=1006,monst_x=335,monst_y=451,},
{min_level=261,max_level=265,monst_name="幽冥山鬼",monst_id=10604,scene_id=1006,monst_x=290,monst_y=425,},
{min_level=266,max_level=270,monst_name="金刚罗汉",monst_id=10605,scene_id=1006,monst_x=273,monst_y=360,},
{min_level=271,max_level=280,monst_name="失心魔僧",monst_id=10607,scene_id=1006,monst_y=263,},
{min_level=281,max_level=285,monst_name="净天教徒",monst_id=10608,scene_id=1006,monst_y=209,},
{min_level=286,max_level=290,monst_name="毁经暴徒",monst_id=10609,scene_id=1006,monst_y=159,},
{min_level=291,max_level=300,monst_name="蟒白蛇妖",monst_id=10610,scene_id=1006,monst_x=198,monst_y=152,},
{min_level=301,max_level=305,monst_name="迷魂僧人",monst_id=10611,scene_id=1006,monst_x=192,monst_y=215,},
{min_level=306,max_level=310,monst_name="毁经暴徒",monst_id=10612,scene_id=1006,monst_x=132,monst_y=228,},
{min_level=311,max_level=320,monst_id=10613,scene_id=1006,monst_x=188,monst_y=235,},
{min_level=321,max_level=325,monst_name="魔化妖僧",monst_id=10614,scene_id=1006,monst_x=187,monst_y=289,},
{min_level=326,max_level=330,monst_name="迷魂幽花",monst_id=10615,scene_id=1006,monst_x=141,monst_y=300,},
{min_level=331,max_level=335,monst_name="心魔化形",monst_id=10616,scene_id=1006,monst_x=134,monst_y=355,},
{min_level=336,max_level=340,monst_name="控魂魔怪",monst_id=10617,scene_id=1006,monst_x=200,monst_y=366,},
{min_level=341,max_level=345,monst_name="天门守卫",monst_id=10700,scene_id=1007,monst_x=373,monst_y=429,},
{min_level=346,max_level=350,monst_name="天宫守卫",monst_id=10701,scene_id=1007,monst_x=378,monst_y=363,},
{min_level=351,max_level=355,monst_name="失控灵火",monst_id=10703,scene_id=1007,monst_x=455,monst_y=352,},
{min_level=356,max_level=360,monst_name="魅惑蝶仙",monst_id=10704,scene_id=1007,monst_x=458,monst_y=317,},
{min_level=361,max_level=365,monst_name="巡逻天兵",monst_id=10705,scene_id=1007,monst_x=421,monst_y=245,},
{min_level=366,max_level=370,monst_name="百战天将",monst_id=10706,scene_id=1007,monst_x=385,monst_y=213,},
{min_level=371,max_level=380,monst_name="仙君亲随",monst_id=10707,scene_id=1007,monst_x=370,monst_y=241,},
{min_level=381,max_level=390,monst_name="蟠桃树精",monst_id=10708,scene_id=1007,monst_x=288,monst_y=290,},
{min_level=391,max_level=395,monst_name="白虎分身",monst_id=10709,scene_id=1007,monst_x=270,monst_y=220,},
{min_level=396,max_level=400,monst_name="玄武分身",monst_id=10710,scene_id=1007,monst_x=275,monst_y=182,},
{min_level=401,max_level=405,monst_name="青龙分身",monst_id=10711,scene_id=1007,monst_x=227,monst_y=186,},
{min_level=406,max_level=410,monst_name="朱雀分身",monst_id=10712,scene_id=1007,monst_x=225,monst_y=203,},
{min_level=411,max_level=415,monst_name="封龙守卫",monst_id=10713,scene_id=1007,monst_x=152,monst_y=224,},
{min_level=416,max_level=420,monst_name="护阁守卫",monst_id=10714,scene_id=1007,monst_x=160,monst_y=286,},
{min_level=421,max_level=425,monst_name="护阁精锐",monst_id=10715,scene_id=1007,monst_x=153,monst_y=321,},
{min_level=426,max_level=430,monst_name="天帝亲兵",monst_id=10716,scene_id=1007,monst_x=167,monst_y=343,},
{min_level=431,max_level=435,monst_name="天宫守卫",monst_id=10717,scene_id=1007,monst_x=169,monst_y=377,},
{min_level=436,max_level=440,monst_name="守关天兵",monst_id=10718,scene_id=1007,monst_x=238,monst_y=388,},
{min_level=441,max_level=450,monst_id=10800,monst_x=57,monst_y=332,},
{min_level=451,max_level=460,monst_name="魔族斥候",monst_id=10801,monst_x=61,monst_y=263,},
{min_level=461,max_level=470,monst_name="恶念魔怪",monst_id=10802,monst_x=83,monst_y=167,},
{min_level=471,max_level=480,monst_name="净天教精锐",monst_id=10803,monst_x=101,monst_y=159,},
{min_level=481,max_level=490,monst_name="幻影心魔",monst_id=10804,monst_x=188,monst_y=154,},
{min_level=491,max_level=500,monst_name="魔族统领",monst_id=10805,monst_x=182,monst_y=187,},
{min_level=501,max_level=510,monst_name="嗜血魔怪",monst_id=10806,monst_x=188,monst_y=208,},
{min_level=511,max_level=520,monst_name="熔岩魔蛛",monst_id=10807,monst_x=232,monst_y=235,},
{min_level=521,max_level=530,monst_name="魔族大军",monst_id=10808,monst_x=282,monst_y=198,},
{min_level=531,max_level=540,monst_name="净天教徒",monst_id=10809,monst_x=267,monst_y=144,},
{min_level=541,max_level=550,monst_name="无尽魔军",monst_id=10810,monst_x=204,monst_y=55,},
{min_level=551,max_level=560,monst_name="魔化沙兽",monst_id=10811,monst_x=332,monst_y=57,},
{min_level=561,max_level=570,monst_name="魔族王殿",monst_id=10812,monst_x=354,monst_y=123,},
{min_level=571,max_level=580,monst_name="魔尊分身",monst_id=10813,monst_x=363,monst_y=163,},
{min_level=581,max_level=590,monst_name="净天教护法",monst_id=10814,monst_x=364,monst_y=227,},
{min_level=591,max_level=600,monst_name="净天教统领",monst_id=10815,monst_x=390,monst_y=313,},
{min_level=601,max_level=610,monst_name="魔教使者",monst_id=10816,monst_x=297,monst_y=348,},
{min_level=611,max_level=620,monst_name="魔族魔王",monst_x=265,monst_y=325,},
{min_level=621,max_level=630,},
{min_level=631,max_level=999,monst_name="古木亲随",monst_x=234,monst_y=308,}
},

monst_level_meta_table_map={
[90]=91,	-- depth:1
[6]=7,	-- depth:1
[4]=7,	-- depth:1
[3]=7,	-- depth:1
[5]=7,	-- depth:1
[21]=22,	-- depth:1
[19]=20,	-- depth:1
[24]=23,	-- depth:1
[26]=25,	-- depth:1
[35]=36,	-- depth:1
[33]=34,	-- depth:1
[31]=32,	-- depth:1
[30]=29,	-- depth:1
[28]=27,	-- depth:1
[1]=2,	-- depth:1
},
bounty_task_reward={
{single_exp=300000,},
{min_level=60,max_level=64,},
{min_level=65,max_level=69,single_exp=360000,},
{min_level=70,max_level=74,},
{min_level=75,max_level=79,},
{min_level=80,max_level=84,},
{min_level=85,max_level=89,},
{min_level=90,max_level=94,},
{min_level=95,max_level=99,single_exp=480000,},
{min_level=100,max_level=104,},
{min_level=105,max_level=109,single_exp=576000,},
{min_level=110,max_level=114,single_exp=633600,},
{min_level=115,max_level=120,single_exp=2534400,},
{min_level=121,max_level=130,single_exp=2787840,},
{min_level=131,max_level=140,single_exp=3066624,},
{min_level=141,max_level=150,single_exp=3373286,},
{min_level=151,max_level=160,single_exp=3541950,},
{min_level=161,max_level=170,single_exp=3719048,},
{min_level=171,max_level=180,single_exp=3905000,},
{min_level=181,max_level=190,single_exp=4100250,},
{min_level=191,max_level=200,single_exp=4305263,}
},

bounty_task_reward_meta_table_map={
[10]=11,	-- depth:1
[8]=9,	-- depth:1
[2]=3,	-- depth:1
},
task_star={
[1]={level=1,},
[2]={level=2,weight=25,reward_rate=50,},
[3]={level=3,weight=30,reward_rate=60,},
[4]={level=4,weight=15,reward_rate=80,},
[5]={level=5,weight=10,reward_rate=100,}
},

task_star_meta_table_map={
},
paohuan_task_reward={
[0]={huan=0,},
[1]={huan=1,exp=271096,bind_coin=3389,},
[2]={huan=2,exp=273754,bind_coin=3422,},
[3]={huan=3,exp=276412,bind_coin=3455,},
[4]={huan=4,exp=279070,bind_coin=3488,},
[5]={huan=5,exp=281728,bind_coin=3522,},
[6]={huan=6,exp=284385,bind_coin=3555,},
[7]={huan=7,exp=287043,bind_coin=3588,},
[8]={huan=8,exp=289701,bind_coin=3621,},
[9]={huan=9,exp=292359,bind_coin=3654,},
[10]={huan=10,exp=295017,bind_coin=3688,},
[11]={huan=11,exp=297674,bind_coin=3721,},
[12]={huan=12,exp=300332,bind_coin=3754,},
[13]={huan=13,exp=302990,bind_coin=3787,},
[14]={huan=14,exp=305648,bind_coin=3821,},
[15]={huan=15,exp=308306,bind_coin=3854,},
[16]={huan=16,exp=310963,bind_coin=3887,},
[17]={huan=17,exp=313621,bind_coin=3920,},
[18]={huan=18,exp=316279,bind_coin=3953,},
[19]={huan=19,exp=318937,bind_coin=3987,},
[20]={huan=20,exp=321595,bind_coin=4020,},
[21]={huan=21,exp=324252,bind_coin=4053,},
[22]={huan=22,exp=326910,bind_coin=4086,},
[23]={huan=23,exp=329568,bind_coin=4120,},
[24]={huan=24,exp=332226,bind_coin=4153,},
[25]={huan=25,exp=334884,bind_coin=4186,},
[26]={huan=26,exp=337542,bind_coin=4219,},
[27]={huan=27,exp=340199,bind_coin=4252,},
[28]={huan=28,exp=342857,bind_coin=4286,},
[29]={huan=29,exp=345515,bind_coin=4319,},
[30]={huan=30,exp=348173,bind_coin=4352,},
[31]={huan=31,exp=350831,bind_coin=4385,},
[32]={huan=32,exp=353488,bind_coin=4419,},
[33]={huan=33,exp=356146,bind_coin=4452,},
[34]={huan=34,exp=358804,bind_coin=4485,},
[35]={huan=35,exp=361462,bind_coin=4518,},
[36]={huan=36,exp=364120,bind_coin=4551,},
[37]={huan=37,exp=366777,bind_coin=4585,},
[38]={huan=38,exp=369435,bind_coin=4618,},
[39]={huan=39,exp=372093,bind_coin=4651,},
[40]={huan=40,exp=374751,bind_coin=4684,},
[41]={huan=41,exp=377409,bind_coin=4718,},
[42]={huan=42,exp=380066,bind_coin=4751,},
[43]={huan=43,exp=382724,bind_coin=4784,},
[44]={huan=44,exp=385382,bind_coin=4817,},
[45]={huan=45,exp=388040,bind_coin=4850,},
[46]={huan=46,exp=390698,bind_coin=4884,},
[47]={huan=47,exp=393355,bind_coin=4917,},
[48]={huan=48,exp=396013,bind_coin=4950,},
[49]={huan=49,exp=398671,bind_coin=4983,},
[50]={huan=50,exp=401329,bind_coin=5017,},
[51]={huan=51,exp=403987,bind_coin=5050,},
[52]={huan=52,exp=406645,bind_coin=5083,},
[53]={huan=53,exp=409302,bind_coin=5116,},
[54]={huan=54,exp=411960,bind_coin=5150,},
[55]={huan=55,exp=414618,bind_coin=5183,},
[56]={huan=56,exp=417276,bind_coin=5216,},
[57]={huan=57,exp=419934,bind_coin=5249,},
[58]={huan=58,exp=422591,bind_coin=5282,},
[59]={huan=59,exp=425249,bind_coin=5316,},
[60]={huan=60,exp=427907,bind_coin=5349,},
[61]={huan=61,exp=430565,bind_coin=5382,},
[62]={huan=62,exp=433223,bind_coin=5415,},
[63]={huan=63,exp=435880,bind_coin=5449,},
[64]={huan=64,exp=438538,bind_coin=5482,},
[65]={huan=65,exp=441196,bind_coin=5515,},
[66]={huan=66,exp=443854,bind_coin=5548,},
[67]={huan=67,exp=446512,bind_coin=5581,},
[68]={huan=68,exp=449169,bind_coin=5615,},
[69]={huan=69,exp=451827,bind_coin=5648,},
[70]={huan=70,exp=454485,bind_coin=5681,},
[71]={huan=71,exp=457143,bind_coin=5714,},
[72]={huan=72,exp=459801,bind_coin=5748,},
[73]={huan=73,exp=462458,bind_coin=5781,},
[74]={huan=74,exp=465116,bind_coin=5814,},
[75]={huan=75,exp=467774,bind_coin=5847,},
[76]={huan=76,exp=470432,bind_coin=5880,},
[77]={huan=77,exp=473090,bind_coin=5914,},
[78]={huan=78,exp=475748,bind_coin=5947,},
[79]={huan=79,exp=478405,bind_coin=5980,},
[80]={huan=80,exp=481063,bind_coin=6013,},
[81]={huan=81,exp=483721,bind_coin=6047,},
[82]={huan=82,exp=486379,bind_coin=6080,},
[83]={huan=83,exp=489037,bind_coin=6113,},
[84]={huan=84,exp=491694,bind_coin=6146,},
[85]={huan=85,exp=494352,bind_coin=6179,},
[86]={huan=86,exp=497010,bind_coin=6213,},
[87]={huan=87,exp=499668,bind_coin=6246,},
[88]={huan=88,exp=502326,bind_coin=6279,},
[89]={huan=89,exp=504983,bind_coin=6312,},
[90]={huan=90,exp=507641,bind_coin=6346,},
[91]={huan=91,exp=510299,bind_coin=6379,},
[92]={huan=92,exp=512957,bind_coin=6412,},
[93]={huan=93,exp=515615,bind_coin=6445,},
[94]={huan=94,exp=518272,bind_coin=6478,},
[95]={huan=95,exp=520930,bind_coin=6512,},
[96]={huan=96,exp=523588,bind_coin=6545,},
[97]={huan=97,exp=526246,bind_coin=6578,},
[98]={huan=98,exp=528904,bind_coin=6611,},
[99]={huan=99,exp=531561,bind_coin=6645,}
},

paohuan_task_reward_meta_table_map={
},
paohuan_reward_fix={
{exp_addpercent=10000,},
{min_level=201,max_level=300,exp_addpercent=12000,},
{min_level=301,max_level=400,exp_addpercent=14400,},
{min_level=401,max_level=500,exp_addpercent=17200,},
{min_level=501,max_level=600,exp_addpercent=20700,},
{min_level=601,max_level=700,},
{min_level=701,max_level=800,},
{min_level=801,max_level=900,},
{min_level=901,max_level=1000,},
{min_level=1001,max_level=1100,},
{min_level=1101,max_level=1200,},
{min_level=1201,max_level=1300,},
{min_level=1301,max_level=1400,},
{min_level=1401,max_level=1500,},
{min_level=1501,max_level=1600,},
{min_level=1601,max_level=1700,},
{min_level=1701,max_level=1800,},
{min_level=1801,max_level=1900,},
{min_level=1901,max_level=2000,}
},

paohuan_reward_fix_meta_table_map={
},
paohuan_rolllist={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

paohuan_rolllist_meta_table_map={
},
pahuan_reward_show={
{},
{huan=20,description="完成20环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=30,description="完成30环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=40,description="完成40环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=50,description="完成50环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=60,description="完成60环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=70,description="完成70环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=80,description="完成80环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=90,description="完成90环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",},
{huan=100,description="完成100环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",}
},

pahuan_reward_show_meta_table_map={
},
reset_cond_cfg={
{},
{index=2,},
{index=3,},
{index=4,},
{index=5,},
{index=6,},
{index=7,},
{index=8,},
{index=9,},
{index=10,},
{index=11,},
{index=12,},
{index=13,},
{index=14,},
{index=15,},
{index=16,},
{index=17,},
{index=18,},
{index=19,},
{index=20,},
{index=21,},
{index=22,},
{index=23,},
{index=24,},
{index=25,},
{index=26,},
{index=27,},
{index=28,},
{index=29,},
{index=30,},
{index=31,},
{index=32,},
{index=33,},
{index=34,condition="2|3",},
{index=35,},
{index=36,},
{index=37,},
{index=38,},
{index=39,},
{index=40,},
{task_type=4,},
{index=2,},
{task_type=4,},
{index=4,},
{index=5,},
{index=6,},
{index=7,},
{index=8,condition=3,},
{index=9,},
{index=10,}
},

reset_cond_cfg_meta_table_map={
[36]=34,	-- depth:1
[4]=36,	-- depth:2
[16]=4,	-- depth:3
[40]=16,	-- depth:4
[2]=40,	-- depth:5
[43]=3,	-- depth:1
[44]=43,	-- depth:2
[47]=44,	-- depth:3
[38]=2,	-- depth:6
[14]=38,	-- depth:7
[6]=14,	-- depth:8
[30]=6,	-- depth:9
[8]=30,	-- depth:10
[28]=8,	-- depth:11
[26]=28,	-- depth:12
[49]=47,	-- depth:4
[24]=26,	-- depth:13
[10]=24,	-- depth:14
[22]=10,	-- depth:15
[32]=22,	-- depth:16
[20]=32,	-- depth:17
[12]=20,	-- depth:18
[18]=12,	-- depth:19
[48]=43,	-- depth:2
[42]=48,	-- depth:3
[45]=42,	-- depth:4
[46]=45,	-- depth:5
[50]=46,	-- depth:6
},
xinshou_view_ctrl={
{},
{scene_type=124,task_id=1080,},
{scene_type=45,task_id=1240,},
{scene_type=94,task_id=1300,},
{scene_type=123,task_id=826,}
},

xinshou_view_ctrl_meta_table_map={
},
task_call={
{}
},

task_call_meta_table_map={
},
task_monster={
{},
{task_id=890,scene_id=1003,call_monster_id=10303,call_monster_pos="305,115",}
},

task_monster_meta_table_map={
},
task_list_default_table={task_id=10,task_name="钱宅管家",task_type=2,ver=1,open_day=0,open_love=0,min_level=60,max_level=2000,pretaskid="",prof_level=0,camp=0,accept_npc={},commit_npc={},condition=6,c_param1=0,c_param2=0,c_param3=0,c_param4="",c_param5="",c_param6=0,condition_find_obj_type=0,coin_bind=0,exp_factor=0,exp=0,xiuwei_exp=0,silverticket=0,bind_gold=0,zhenqi=0,yuanli=0,item_list={},gongxian=0,shengwang=0,exp_copies=0,showitem_0_1="",showitem_0_2="",showitem_0_3="",showitem_1_1="",showitem_1_2="",showitem_1_3="",showitem_0_4="",showitem_1_4="",prof_list_0_1={},prof_list_0_2={},prof_list_0_3={},prof_list_0_4={},prof_list_1_1={},prof_list_1_2={},prof_list_1_3={},prof_list_1_4={},accept_dialog="",commit_dialog="",accept_desc="<color=#72eba9>可接取</color>",progress_desc="钱宅管家的委托(<per>0/1</per>)",commit_desc="钱宅管家的委托",open_panel_name="",chapter_desc="",show_chaper_id="",show_chaper_info="",show_story_type="",npc_action="",hide_npc="",hide_gather="",fb_tip_desc="",fb_tip_title="",is_first=0,camera="",Bubble_Tips="",accept_bubble="",complete_bubble="",Submit_bubble="",taskgroup="",target_obj={},},

hide_npc_default_table={npc_id=10208,sections="510,3;530,3",},

hide_gather_default_table={gather_id=1303,sections="1460,1;1480,3",},

push_default_table={index=1,type=1,param1=1460,param2=2,view_url="DujieView",open_param=0,open_key="",shut_time=0,},

weather_effect_default_table={open_task_id=823,close_task_id=827,scene_id=102,asset="",bundle="xiayu",voice="thunder",},

dice_task_default_table={id=2,rounds=1,show_cheats=0,modle_id=6028001,pic_id="",opponent_name="小灵儿",win_rate=100,opp_start_dec="赢了我就告诉你\n(/≧▽≦)/",param=2,opp_end_lose_dec="我输了",opp_end_win_dec="我赢了",my_pre_dec="放马过来吧！",my_win_dec="哈哈理当如此",my_fail_dec="可惜，运气总归会有耗尽的一天",},

change_res_default_table={parm=1,res="model/npc/3002_prefab",id=3002001,},

change_mount_res_default_table={parm=1,res="actors/mount/8022_prefab",id=8022,},

other_default_table={daily_pretask=920,daily_onekey_gold=2,daily_double_gold=2,daily_double_maxstar_gold=10,guild_task_complete_all_reward_item=item_table[72],guild_task_gold=3,commit_equip_guild_task_gold=20,max_star_cost_coin=50000,one_task_need_gold=3,one_key_commit_task_count=10,bounty_one_key_gold=2,auto_task_viplevel=0,clear_guildtask_level=300,daily_guildtask_level=130,task_notice="{[1] = {{task_id = 17011, level = 55}}, [8] = {{task_id = 29001, level = 150}}}",task_auto=1450,other_first="{ [2] = {level = {1,2}}}",side_taskID=17011,tree_task_info="{[1] = {task_id = 130, gather_id = 306,show_effect=0},[2] = {task_id = 980, gather_id = 303,show_effect=1}}",injured_npc_task="",injured_npc_id="",first_des="{[2] = '日常任务可前往凌霄城找<color=#01AB26FF>日常使者</color>领取 ',[4]  = '仙盟任务可前往凌霄城找<color=#01AB26FF>仙盟使者</color> 领取',[8] = ' 转职任务可前往凌霄城找<color=#01AB26FF>转职使者</color>领取 ',}",exp_daily_task=750000,kill_monster_prob_max=10000,guild_task_week_limit=70,daily_scene=1003,special_task=1010,special_pos="145#293#-8",area_pos_list="1003|145,300:145,260:180,260:180,300",daily_task_start=140,close_get_new_lv=100,},

task_sort_default_table={task_type=8,s10001=10000,s10002=10000,s10003=10000,s10004=10000,s10005=10000,s10006=10000,s10007=10000,s10008=10000,s10009=10000,s10010=10000,s10011=10000,s10012=10000,s10013=10000,s10014=10000,s10015=10000,s10016=10000,s10017=10000,s10018=10000,s10019=10000,s10020=10000,s10021=10000,s10022=10000,s10023=10000,},

section_level_default_table={sections_id=10001,min_level=1,max_level=9,},

first_daily_task_default_table={},

daily_task_reward_default_table={level=1,level_max=79,bind_coin=30000,first_round_reward_item={[0]=item_table[73]},second_round_reward_item={[0]=item_table[73]},role_exp=1,},

first_guild_task_default_table={},

guild_task_reward_default_table={level=145,level_max=149,exp=0,coin=50000,gongxian=300,reward_item={},finish_exp=0,finish_coin=0,finish_gongxian=1000,finish_reward_item={},fun=10,monocycle_guild_exp=0,finish_guild_exp=0,},

guild_task_mythicallist_default_table={society_level=1,shenshogongpin=15,finish_gongpin=50,},

guild_task_list_default_table={first_task=33001,end_task=33018,},

zhaunzhi_task_list_default_table={zhuanzhi_level=1,first_task=29004,end_task=29005,},

monst_level_default_table={min_level=31,max_level=90,monst_name="魔族精锐",monst_id=10817,scene_id=1004,monst_x=274,monst_y=174,},

bounty_task_reward_default_table={min_level=52,max_level=59,single_exp=400000,exp=0,single_reward_item={[0]=item_table[72]},reward_item={[0]=item_table[72]},},

task_star_default_table={level=1,weight=20,reward_rate=40,},

paohuan_task_reward_default_table={huan=0,reward_nvwashi=0,exp=268439,bind_coin=3355,},

paohuan_reward_fix_default_table={min_level=1,max_level=200,nvwashi_addpercent=10000,exp_addpercent=24800,bind_coin_addpercent=10000,},

paohuan_rolllist_default_table={},

pahuan_reward_show_default_table={huan=10,description="完成10环跑环任务可以随机抽取一件道具，其中更有珍贵商城道具",item1=22000,item2=22000,item3=22000,},

reset_cond_cfg_default_table={task_type=2,index=1,condition=6,},

xinshou_view_ctrl_default_table={scene_type=83,task_id=860,},

task_call_default_table={task_id=190,end_task_id=550,model_id=10105,weapon_id=101050101,skill="505|506",name="东皇太一",},

task_monster_default_table={task_id=560,scene_id=1002,call_monster_id=10206,call_monster_pos="408,197",call_monster_range=22,call_monster_refresh_range=5,}

}

