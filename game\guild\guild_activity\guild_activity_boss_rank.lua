GuildActivityBossRank = GuildActivityBossRank or BaseClass(SafeBaseView)

function GuildActivityBossRank:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_boss_rank")
	self:SetMaskBg()
end

function GuildActivityBossRank:ReleaseCallBack()
    if self.role_list then
        self.role_list:DeleteMe()
        self.role_list = nil
    end
end

function GuildActivityBossRank:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Guild.RankInfoTitle
    self:SetSecondView(nil, self.node_list["size"])
    self.role_list = AsyncListView.New(GuildBossRankItem, self.node_list["role_list"])
end

function GuildActivityBossRank:OnFlush(param)
    local boss_status_info = GuildBossWGData.Instance:GetGuildBossStatus()
    local rank_list = GuildBossWGData.Instance:GetGuildBossOutRank()  --总排行榜信息
    self.role_list:SetDataList(rank_list)

     --通关星数
    local star_num
    if boss_status_info.is_pass == 1 then
        star_num = boss_status_info.star_level
    else
        star_num = 0
    end
    local asset_name = {[1] = "star_s_1",[2] = "star_s_3",} --new_star_3实心
    for i = 1, 3 do
        if i <= star_num then
            self.node_list["star"..i].image:LoadSprite(ResPath.GetF2CommonImages(asset_name[1]))
        else
            self.node_list["star"..i].image:LoadSprite(ResPath.GetF2CommonImages(asset_name[2]))
        end
    end
end

--------------------------------------------------------------------------------------
GuildBossRankItem = GuildBossRankItem or BaseClass(BaseRender)

function GuildBossRankItem:__init()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
    
end

function GuildBossRankItem:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function GuildBossRankItem:OnFlush()
    self.node_list["bg"]:SetActive(self.index % 2 == 1)
    self.node_list["rank_img"]:CustomSetActive(self.index <= 3)
    local fb_status_cfg = GuildBossWGData.Instance:GetGuildBossStatus()
    local is_pass = fb_status_cfg.is_pass == 1

    if self.index <= 3 then
        local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
        self.node_list["rank_img"].image:SetNativeSize()
         self.node_list["rank_num"].text.text = ""
    else
        self.node_list["rank_num"].text.text = self.data.rank_str or self.index
    end

    if self.data.uid and self.data.uid ~= 0 and self.data.total_hurt and self.data.total_hurt > 0 and is_pass then
        if self.data.total_hurt ~= nil then
            self.node_list["hurt_text"].text.text = CommonDataManager.ConverExp(BigNumFormat(self.data.total_hurt))
        end
        self.node_list["hurt_text"]:CustomSetActive(true)
        self.node_list["name"]:CustomSetActive(true)
        self.node_list["name"].text.text = self.data.game_name
    else
        if self.data.rank_str then
            self.node_list["hurt_text"]:CustomSetActive(false)
            self.node_list["name"]:CustomSetActive(false)
        else
            self.node_list["hurt_text"]:CustomSetActive(true)
            self.node_list["name"]:CustomSetActive(true)
            self.node_list["hurt_text"].text.text = Language.Guild.SHJingQingQiDai
            self.node_list["name"].text.text = Language.Guild.SHJingQingQiDai
        end
    end
    local data_list = self:GetRewardDataList()
    self.reward_list:SetDataList(data_list)
end

function GuildBossRankItem:GetRewardDataList()
    local data_list = {}
    if self.index > 10 then
        --10名以后的格子数据
        return GuildBossWGData.Instance:GetAfterTenRankItemRewardList(self.index)
    end
    local fb_status_cfg = GuildBossWGData.Instance:GetGuildBossStatus()
    local is_pass = fb_status_cfg.is_pass == 1
     if self.data.uid and self.data.uid ~= 0 and is_pass then
         return GuildBossWGData.Instance:GetPersonItemRewardByRank(self.index)
    end
    --3是代表 默认取三星
    return GuildBossWGData.Instance:GetPersonItemRewardByRank(self.index, 3)
end
