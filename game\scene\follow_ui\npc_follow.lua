NpcFollow = NpcFollow or BaseClass(FollowUi)

function NpcFollow:__init()

end

function NpcFollow:__delete()

end

function NpcFollow:OnRootCreateCompleteCallback(gameobj)
	FollowUi.OnRootCreateCompleteCallback(self, gameobj)

	local async_loader = AllocAsyncLoader(self, "task_icon")
	async_loader:SetIsUseObjPool(true)
	async_loader:SetIsInQueueLoad(true)
	async_loader:SetParent(gameobj.transform, false)
	async_loader:Load("uis/view/healthbar_ui_prefab", "TaskIcon",
		function (task_icon)
			if IsNil(task_icon) then
				return
			end

			self.task_icon = U3DObject(task_icon)
			self.task_icon.transform:SetLocalPosition(0, 60, 0)
			self:UpdateTaskIcon()
		end)
end

function NpcFollow:SetTaskIcon(bundle, sp)
	self.icon_bundle = bundle
	self.icon_sp = sp
	self:UpdateTaskIcon()
end

function NpcFollow:UpdateTaskIcon()
	if nil == self.task_icon then
		return
	end

	if nil ~= self.icon_bundle and nil ~= self.icon_sp and "" ~= self.icon_sp then
		self.task_icon:SetActive(true)
		self.task_icon:ChangeAsset(self.icon_bundle, self.icon_sp)
	else
		self.task_icon:SetActive(false)
	end
end

function NpcFollow:SetName(name, secne_obj)
	local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.vo.npc_id)
	local bool = npc_cfg and 1 == tonumber(npc_cfg.task_can_see) --可见npc才显示名称
	if bool then
		self.namebar:SetName(name, secne_obj)
	end
	self.hpbar:SetSceneObj(secne_obj)
end