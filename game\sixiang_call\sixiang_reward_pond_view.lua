SiXiangRewardPondView = SiXiangRewardPondView or BaseClass(SafeBaseView)

function SiXiangRewardPondView:__init()
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(896, 540)})
    self:AddViewResource(0, "uis/view/sixiang_call_prefab", "sixiang_reward_pond_view")
    self:SetMaskBg(true, true)
end

function SiXiangRewardPondView:LoadCallBack()
    self:InitPanel()
end

function SiXiangRewardPondView:ReleaseCallBack()
    if self.reward_pond_item_list then
        for k,v in pairs(self.reward_pond_item_list) do
            v:DeleteMe()
        end
        self.reward_pond_item_list = nil
    end
end

function SiXiangRewardPondView:OnFlush(param_t)
    self:RefreshView()
end

function SiXiangRewardPondView:InitPanel()
    self.node_list.title_view_name.text.text = Language.SiXiangCall.RewardPondTitle
    self.reward_pond_item_list = {}
end

function SiXiangRewardPondView:RefreshView()
    local reward_list = SiXiangCallWGData.Instance:GetRewardPondShowCfg()
    local item_list = self.reward_pond_item_list
    if #reward_list > #item_list then
        local parent = self.node_list.item_content
        for i = #item_list + 1, #reward_list do
            item_list[i] = SiXiangRewardPondItem.New()
            item_list[i]:DoLoad(parent)
        end
        self.reward_pond_item_list = item_list
    end
    for i=1,#item_list do
        item_list[i]:SetData(reward_list[i])
    end
end

------------------------------------------------------------------

SiXiangRewardPondItem = SiXiangRewardPondItem or BaseClass(BaseRender)

function SiXiangRewardPondItem:DoLoad(parent)
    self:LoadAsset("uis/view/sixiang_call_prefab", "sixiang_reward_pond_item", parent.transform)
end

function SiXiangRewardPondItem:__delete()
    if self.item_list then
        for k,v in pairs(self.item_list) do
            v:DeleteMe()
        end
        self.item_list = nil
    end
end

function SiXiangRewardPondItem:LoadCallBack()
    self.item_list = {}
end

function SiXiangRewardPondItem:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    self.node_list.title_lbl.text.text = data.title_name
    self:SetItemList(data.item_list)
end

function SiXiangRewardPondItem:SetItemList(reward_list)
    local item_list = self.item_list
    local reward_data_list = {}
    local reward_count = #reward_list + 1
    if reward_count > #item_list then
        local parent = self.node_list.item_root
        for i = #item_list + 1, reward_count do
            item_list[i] = SiXiangTeDianProUpItem.New()
            item_list[i]:DoLoad(parent)
        end
        self.item_list = item_list
    end

    for i=1,#item_list do
        item_list[i]:SetData(reward_list[i - 1])
    end
end