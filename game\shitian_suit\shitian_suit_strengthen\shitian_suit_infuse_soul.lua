function ShiTianSuitStrengthenView:InfuseSoulLoadCallBack()
    self.use_luck_item = 0
    self.infuse_soul_old_select_index = -1
    self.infuse_soul_list_loader = nil

    if not self.infuse_soul_item_list then
        self.infuse_soul_item_list = {}
        for i = 0, 8 do
            local infuse_soul_cell = InfuseSoulPartItem.New()
            infuse_soul_cell:SetIndex(i)
            self.infuse_soul_item_list[i] = infuse_soul_cell
        end
    end

    if not self.infusesoul_attr_list then
        self.infusesoul_attr_list = {}
        for i = 1, MAX_STTZ_ATTR_NUM do
            self.infusesoul_attr_list[i] = CommonAddAttrRender.New(self.node_list["infusesoul_attr_item" .. i])
        end
    end

    if not self.infusesoul_stuff_item_list then
        self.infusesoul_stuff_item_list = {}
        for i = 1, 2 do
            local cell = ShiTianStuffItem.New(self.node_list["infusesoul_stuff_pos" .. i])
            cell:SetClickCallBack(BindTool.Bind(self.SetIsUseLuckitem, self))
            self.infusesoul_stuff_item_list[i] = cell
        end
    end

    XUI.AddClickEventListener(self.node_list.infusesoul_btn, BindTool.Bind(self.OnClickInfuseSoulBtn, self, true))
    XUI.AddClickEventListener(self.node_list.auto_infusesoul_btn, BindTool.Bind(self.OnClickAutoInfuseSoulBtn, self))
    XUI.AddClickEventListener(self.node_list.infusesoul_level_btn, BindTool.Bind(self.OnClickResonanceBtn, self))
end

function ShiTianSuitStrengthenView:InfuseSoulReleaseCallBack()
    if self.infuse_soul_item_list and #self.infuse_soul_item_list > 0 then
		for k, v in pairs(self.infuse_soul_item_list) do
			v:DeleteMe()
		end

        self.infuse_soul_item_list = nil
	end

    if self.infusesoul_attr_list then
		for k, v in pairs(self.infusesoul_attr_list) do
			v:DeleteMe()
		end

        self.infusesoul_attr_list = nil
	end

    if self.infusesoul_stuff_item_list then
		for k, v in pairs(self.infusesoul_stuff_item_list) do
			v:DeleteMe()
		end

        self.infusesoul_stuff_item_list = nil
	end

    self.infuse_soul_list_loader = nil
end

function ShiTianSuitStrengthenView:InfuseSoulOnFlush()
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    local title_bundle, title_asset = ResPath.GetF2RawImagesPNG("a2_lhtz_epname_" .. suit_seq .. "_" .. part)
    self.node_list.infusesoul_title_img.raw_image:LoadSprite(title_bundle, title_asset, function()
        self.node_list.infusesoul_title_img.raw_image:SetNativeSize()
    end)

    local ep_bundle, ep_asset = ResPath.GetF2RawImagesPNG("a2_lhtz_epimg_" .. suit_seq .. "_" .. part)
    self.node_list.infuse_soul_ep.raw_image:LoadSprite(ep_bundle, ep_asset, function()
        self.node_list.infuse_soul_ep.raw_image:SetNativeSize()
    end)

    self:FlushInfuseSoulPanel()
end

function ShiTianSuitStrengthenView:FlushInfuseSoulTotalLevelPanel()
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local cur_total_cfg, next_total_cfg = ShiTianSuitStrengthenWGData.Instance:GetCurAndNextTotalLevelCfg(suit_seq, SHOW_TYPE.InfuseSoul)
    local total_level = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulTotalLevelBySeq(suit_seq)
    local total_is_max = ShiTianSuitStrengthenWGData.Instance:GetCurTotalCanActiveOrIsMax(suit_seq, SHOW_TYPE.InfuseSoul, true)
    local next_get_level = next_total_cfg.total_quality or cur_total_cfg.total_quality or 0
    local slider_value = total_level / STSUIT_MAX_PART_COUNT
    self.node_list.infusesoul_pro_text.text.text = string.format(Language.ShiTianSuit.InfuseSoulTotalQuality, Language.ShiTianSuit.InfuseSoulQuality[next_get_level], total_level, STSUIT_MAX_PART_COUNT)
    self.node_list.infusesoul_slider.slider.value = slider_value > 1 and 1 or slider_value
    self.node_list.infusesoul_level_remind:SetActive(not total_is_max and total_level >= STSUIT_MAX_PART_COUNT)
end

function ShiTianSuitStrengthenView:FlushInfuseSoulPanel(is_update)
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    local cur_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulLevelCfg(suit_seq, part)
    if IsEmptyTable(cur_level_cfg) then
        return
    end

    local cur_level, cur_quality = ShiTianSuitStrengthenWGData.Instance:GetEpInfuseSoulCurLevelAndQuality(suit_seq, part)
    if is_update then
        local is_success = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulIsLevelUp()
        local effect_type = self:GetEffectType()
        TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
							is_success = is_success, pos = Vector2(0, 0), parent_node = self.node_list.infuse_soul_effect})
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))

        local jump_ep_index, is_remind_jump = ShiTianSuitStrengthenWGData.Instance:GetOneRemindEpIndex(suit_seq, part, SHOW_TYPE.InfuseSoul)
        if jump_ep_index ~= part and is_remind_jump then
            self.shitian_ep_list:JumpToIndex(jump_ep_index)
            return
        end
    end

    self:FlushInfuseSoulTotalLevelPanel()
    local level_max, quality_max = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulCurLevelAndQualityIsMax(suit_seq, part)
    local next_level = level_max and not quality_max and 0 or cur_level + 1
    local next_quality = level_max and not quality_max and cur_quality + 1 or cur_quality
    local next_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulLevelCfg(suit_seq, part, next_level, next_quality)
    self.node_list.infusesoul_btn_group:SetActive(not level_max or not quality_max)
    self.node_list.infusesoul_max_flag:SetActive(level_max and quality_max)

    self:FlushInfuseSoulStuff(cur_level_cfg)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 2)
    for k, v in pairs(self.infusesoul_attr_list) do
        v:SetData(attr_list[k])
    end

    self:FlushInfuseSoulSuccessRate()
    self:InfuseSoulFlushPartViewRoot(function()
        if self.infuse_soul_item_list and #self.infuse_soul_item_list > 0  then
            local fumo_level, fumo_quality = ShiTianSuitStrengthenWGData.Instance:GetEpInfuseSoulCurLevelAndQuality(suit_seq, part)
            for index, cell in pairs(self.infuse_soul_item_list) do
                if cell then
                    cell:SetData({cur_fumo_level = fumo_level, cur_fumo_quality = fumo_quality})
                end
            end
        end
    end)

    if self.use_luck_item > 0 then
        local is_max = level_max and quality_max
        local can_use_item = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_luck_id) >= cur_level_cfg.cost_luck_num
        self.infusesoul_stuff_item_list[2]:OnClickSpecialItem(can_use_item and not is_max)
    end

    local can_infuse_soul = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_fumo_id) >= cur_level_cfg.cost_fumo_num
    if self.auto_infuse_soul and can_infuse_soul then
        local timer_callback = function()
            if self.auto_infuse_soul then
                self:OnClickInfuseSoulBtn()
            end
        end

        self.auto_infuse_soul_timer = GlobalTimerQuest:AddTimesTimer(timer_callback, 0, 0.5)
    else
        self:CancelAutoInfuseSoul()
    end

    self:FlushAutoInfuseSoulBtnTxt()
end

function ShiTianSuitStrengthenView:FlushInfuseSoulStuff(level_cfg)
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part

    if not level_cfg then
        level_cfg = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulLevelCfg(suit_seq, part)
    end

    if IsEmptyTable(level_cfg) then
        return
    end

    local level_max, quality_max = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulCurLevelAndQualityIsMax(suit_seq, part)
    for k, cell in pairs(self.infusesoul_stuff_item_list) do
        local item_id = k == 1 and level_cfg.cost_fumo_id or level_cfg.cost_luck_id
        local need_num = k == 1 and level_cfg.cost_fumo_num or level_cfg.cost_luck_num
        cell:SetData({item_id = item_id, is_special = k > 1, need_num = need_num, is_max = level_max and quality_max})
    end

    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    local is_max = level_max and quality_max
    local can_infuse_soul = ItemWGData.Instance:GetItemNumInBagById(level_cfg.cost_fumo_id) >= level_cfg.cost_fumo_num
    self.node_list.infusesoul_btn_remind:SetActive(is_act and can_infuse_soul and not is_max)
    self.node_list.auto_infusesoul_btn_remind:SetActive(is_act and can_infuse_soul and not is_max)
end

function ShiTianSuitStrengthenView:FlushInfuseSoulSuccessRate()
    if not self.cur_select_ep_data then
        return
    end

    local cur_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulLevelCfg(self.cur_select_ep_data.suit_seq, self.cur_select_ep_data.part)
    local luck_item_success_rate = self.use_luck_item > 0 and 100 - cur_level_cfg.success_rate or 0
    self.node_list.infusesoul_success_rate.text.text = string.format(Language.ShiTianSuit.InfusesoulSuccessRate, cur_level_cfg.success_rate, luck_item_success_rate)
end

function ShiTianSuitStrengthenView:InfuseSoulFlushPartViewRoot(callback)
    if not self.cur_select_ep_data then
        return
    end

    if self.infuse_soul_old_select_index and self.infuse_soul_old_select_index == self.cur_select_ep_data.part then
        if callback then
            callback()
        end
        return
    end

    self.infuse_soul_old_select_index = self.cur_select_ep_data.part
    local asset_bundle = "uis/view/shitian_suit_ui_prefab"
    local asset_name = "infuse_soul_equip_slot_%d"
    asset_name = string.format(asset_name, self.cur_select_ep_data.part)
    if not self.infuse_soul_list_loader then
        local infuse_soul_list_loader = AllocAsyncLoader(self, "infuse_soul_list_loader")
        if infuse_soul_list_loader then
            infuse_soul_list_loader:SetIsUseObjPool(true)
            infuse_soul_list_loader:SetParent(self.node_list["infuse_soul_ep_slot_root"].transform)
            self.infuse_soul_list_loader = infuse_soul_list_loader
        end
    end

    if self.infuse_soul_list_loader then
        self.infuse_soul_list_loader:Load(asset_bundle, asset_name, function ()
            self:InfuseSoulFlushPartViewRender()
            if callback then
                callback()
            end
        end)
    end
end

function ShiTianSuitStrengthenView:InfuseSoulFlushPartViewRender()
    if not self.infuse_soul_list_loader then
        return
    end

    if (not self.infuse_soul_item_list) or (#self.infuse_soul_item_list <= 0) then
        return
    end

    local list_obj = self.infuse_soul_list_loader:GetGameObj()
    local list_u3d_obj = U3DObject(list_obj, list_obj.transform, self)

    for i = 0, 8 do
        local cell = self.infuse_soul_item_list[i]
        if cell and list_u3d_obj then
            local path = string.format("bead_part_%d", i)
            local root = list_u3d_obj:FindObj(path).gameObject
            cell:SetInstance(root)
        end
    end
end

function ShiTianSuitStrengthenView:CleanInfuseSoulTimer()
	if self.auto_infuse_soul_timer then
        GlobalTimerQuest:CancelQuest(self.auto_infuse_soul_timer)
        self.auto_infuse_soul_timer = nil
    end
end

function ShiTianSuitStrengthenView:SetIsUseLuckitem(is_use)
    self.use_luck_item = is_use and 1 or 0
    self:FlushInfuseSoulSuccessRate()
end

function ShiTianSuitStrengthenView:OnClickInfuseSoulBtn(is_click)
    if not self.cur_select_ep_data then
        self.auto_infuse_soul = false
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    if not is_act then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.NoActiveShiTianEp)
        self.auto_infuse_soul = false
        return
    end

    if is_click and self.auto_infuse_soul then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.AutoMode)
        return
    end

    local cur_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulLevelCfg(suit_seq, part)
    if IsEmptyTable(cur_level_cfg) then
        self.auto_infuse_soul = false
        return
    end

    local can_infuse_soul = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_fumo_id) >= cur_level_cfg.cost_fumo_num
    if not can_infuse_soul then
        self.auto_infuse_soul = false
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.NotCanUp[SHOW_TYPE.InfuseSoul])
        return
    end

    ShiTianSuitStrengthenWGCtrl.Instance:SendCSShiTianStrengthenReq(SHITIANSTONE_OPERA_TYPE.FUMO_LEVEL_UP, suit_seq, part, self.use_luck_item)
end

function ShiTianSuitStrengthenView:OnClickAutoInfuseSoulBtn()
    self.auto_infuse_soul = not self.auto_infuse_soul
    if self.auto_infuse_soul then
        self:OnClickInfuseSoulBtn()
    end

    self:FlushAutoInfuseSoulBtnTxt()
end

function ShiTianSuitStrengthenView:FlushAutoInfuseSoulBtnTxt()
    local btn_str = self.auto_infuse_soul and Language.ShiTianSuit.CancelAuto or Language.ShiTianSuit.AutoInfuseSoul
    self.node_list.auto_infusesoul_text.text.text = btn_str
end

function ShiTianSuitStrengthenView:CancelAutoInfuseSoul()
    if self.auto_infuse_soul then
        self.auto_infuse_soul = false
        self:CleanInfuseSoulTimer()
    end
end

function ShiTianSuitStrengthenView:GetEffectType()
    local is_success, is_level_up = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulIsLevelUp()
    if is_level_up then
        return is_success and UIEffectName.s_rulincg or UIEffectName.f_rulinsb
    else
        return is_success and UIEffectName.s_tupo or UIEffectName.f_tupo
    end
end

---------------------入灵穴位属性item------------------
local INFUSE_SOUL_EFFECT = {
	[0] = "UI_linghuang_jihuo_bai",
	[1] = "UI_linghuang_jihuo_lv",
	[2] = "UI_linghuang_jihuo_lan",
	[3] = "UI_linghuang_jihuo_zi",
	[4] = "UI_linghuang_jihuo_cheng",
	[5] = "UI_linghuang_jihuo_hong",
	[6] = "UI_linghuang_jihuo_fen",
	[7] = "UI_linghuang_jihuo_fen",
	[8] = "UI_linghuang_jihuo_jin",
	[9] = "UI_linghuang_jihuo_jin",
	[10] = "UI_linghuang_jihuo_huancai",
	[11] = "UI_linghuang_jihuo_huancai",
	[12] = "UI_linghuang_jihuo_huancai",
}

InfuseSoulPartItem = InfuseSoulPartItem or BaseClass(BaseRender)
function InfuseSoulPartItem:LoadCallBack()
    self.game_obj_attach = self.node_list["effect"].gameObject:GetComponent(typeof(Game.GameObjectAttach))
end

function InfuseSoulPartItem:ReleaseCallBack()
    self.game_obj_attach = nil
	self.data = nil
end

function InfuseSoulPartItem:OnFlush()
	if not self.data then
		return
	end

    local is_act = self.data.cur_fumo_level > self.index
    if is_act then
        local bundle, asset = ResPath.GetShiTianSuitIcon("a2_lhtz_bead_" .. self.data.cur_fumo_quality)
        self.node_list.icon.image:LoadSprite(bundle, asset)
    end

    self.node_list.icon:SetActive(is_act)
    self.node_list.line:SetActive(is_act)
    self:SetEffect(is_act)
end

function InfuseSoulPartItem:SetEffect(is_act)
    if is_act then
        local effect_id = math.min(12, self.data.cur_fumo_quality)
        local bundle, asset = ResPath.GetA2Effect(INFUSE_SOUL_EFFECT[effect_id])
	    self.game_obj_attach.BundleName = bundle
	    self.game_obj_attach.AssetName = asset
    end

    self.node_list.effect:SetActive(is_act)
end