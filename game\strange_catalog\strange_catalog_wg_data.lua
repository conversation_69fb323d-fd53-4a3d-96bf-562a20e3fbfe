-- 奇闻图鉴常量定义
QWTJ_DEFINE = {
	MAX_NUM = 1024,													-- 最大秘笈数
}

local empty_table = {}
StrangeCatalogWGData = StrangeCatalogWGData or BaseClass()
function StrangeCatalogWGData:__init()
	if StrangeCatalogWGData.Instance then
		error("[StrangeCatalogWGData] Attempt to create singleton twice!")
		return
	end
	-- 单例
	StrangeCatalogWGData.Instance = self
	self.handbook_list = {}
	self.qw_item_id_num = {}
	self.tujian_list = {}
	self.jiban_item_list = {}
	self.jiban_level_list = {}
	self.grid_list = {}
	self:InitConfig()

	RemindManager.Instance:Register(RemindName.StrangeCatalog, BindTool.Bind(self.GetStrangeCatalogRemind, self))
	RemindManager.Instance:Register(RemindName.StrangeCatalogJiBan, BindTool.Bind(self.GetJiBanRemind, self))
end

function StrangeCatalogWGData:__delete()
	StrangeCatalogWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.StrangeCatalog)
	RemindManager.Instance:UnRegister(RemindName.StrangeCatalogJiBan)
end

function StrangeCatalogWGData:InitConfig()
	local cfg = ConfigManager.Instance:GetAutoConfig("handbook_config_auto")
	self.qw_act_type_cfg = ListToMapList(cfg.active, "big_type")
	self.qw_act_cfg = ListToMap(cfg.active, "seq")
	self.qw_display_cfg = cfg.display
	self.level_attr_cfg = cfg.level_attr_per
	self.base_attr_cfg = ListToMap(cfg.base_attr, "base_attr_seq")
	self.special_attr_cfg = ListToMap(cfg.special_attr, "special_attr_seq", "zhuling_level")
	self.qw_zhuling_cfg = cfg.zhuling
	self.jiban_show_cfg = ListToMapList(cfg.jiban_show, "jiban_group")
	self.jiban_show_seq_cfg = ListToMap(cfg.jiban_show, "seq")
	self.jiban_cfg = ListToMap(cfg.jiban, "seq", "level")
	self.act_itemid_cfg = ListToMap(cfg.active, "active_item_id")
	self.decompose_cfg = ListToMap(cfg.decompose, "decompose_item_id")
end

--============================ 配置 ==================================
function StrangeCatalogWGData:GetDisplayCfg()
	return self.qw_display_cfg
end

function StrangeCatalogWGData:GetActPartItem(seq)
	return self.qw_act_cfg[seq]
end

-- 通过索引拿对应信息
function StrangeCatalogWGData:GetSingleInfo(seq)
	local active_cfg = self:GetActPartItem(seq)
	local data = {}
	if not active_cfg then
		return data
	end

	data.seq = seq
	data.big_type = active_cfg.big_type
	data.quality_type = active_cfg.quality_type
	data.active_item_id = active_cfg.active_item_id
	data.base_attr_seq = active_cfg.base_attr_seq
	data.special_attr_seq = active_cfg.special_attr_seq
	data.pic_seq = active_cfg.pic_seq
	data.is_remind = self:SingleRemind(seq)

	return data
end

function StrangeCatalogWGData:GetLevelAttrCfg()
	return self.level_attr_cfg
end

function StrangeCatalogWGData:GetBaseAttrCfgBySeq(base_attr_seq)
	return self.base_attr_cfg[base_attr_seq]
end

function StrangeCatalogWGData:GetZhuLingAllCfg()
	return self.qw_zhuling_cfg
end

function StrangeCatalogWGData:GetZhuLingSingleCfg(level)
	return self.qw_zhuling_cfg[level]
end

function StrangeCatalogWGData:GetSpecialBySeq(seq)
	return self.special_attr_cfg[seq]
end

function StrangeCatalogWGData:GetCurSpecialAttr(seq, level)
	return (self.special_attr_cfg[seq] or empty_table)[level] or empty_table
end

function StrangeCatalogWGData:GetJiBanAllGroup()
	return self.jiban_show_cfg
end

function StrangeCatalogWGData:GetJiBanCfg()
	return self.jiban_cfg
end

function StrangeCatalogWGData:GetJiBanCfgBySeq(seq, level)
	return (self.jiban_cfg[seq] or empty_table)[level] or empty_table
end

function StrangeCatalogWGData:GetJiBanShowList(group)
	return self.jiban_show_cfg[group]
end

function StrangeCatalogWGData:GetQiWuItemCfg(item_id)
	if item_id == nil then
		return
	end

	return self.act_itemid_cfg[item_id]
end

function StrangeCatalogWGData:GetDecomposeCfgByItemId(item_id)
	return (self.decompose_cfg or {})[item_id] or {}
end

function StrangeCatalogWGData:GetDecomposeItemId()
	local key, cfg = next(self.decompose_cfg)
	return (((cfg or {}).reward_item or {})[0] or {}).item_id or 0
end



--============================== 协议 ================================
-- 图鉴 - 所有信息
function StrangeCatalogWGData:SetHandBookAllInfo(protocol)
	self.tujian_list = protocol.tujian_list
	self.jiban_level_list = protocol.jiban_level_list
end

function StrangeCatalogWGData:GetQWAllServerInfo()
	return self.tujian_list
end

function StrangeCatalogWGData:GetQWSingleServerInfo(seq)
	return self.tujian_list[seq]
end

-- 图鉴 - 单个信息
function StrangeCatalogWGData:SetHandBookSingleInfo(protocol)
	local single_info = self:GetQWSingleServerInfo(protocol.seq)
	if not IsEmptyTable(single_info) then
		single_info.level = protocol.level
		single_info.zhuling_level = protocol.zhuling_level
		single_info.zhuling_exp = protocol.zhuling_exp
	end
end




-- 图鉴羁绊
function StrangeCatalogWGData:GetQWJiBanServerInfo()
	return self.jiban_level_list
end

function StrangeCatalogWGData:GetQWJiBanServerInfoBySeq(seq)
	return self.jiban_level_list[seq]
end

-- 图鉴羁绊 - 单个信息
function StrangeCatalogWGData:SetHandBookJibanSingleInfo(protocol)
	self.jiban_level_list[protocol.jiban_seq] = protocol.jiban_level
end




-- 图鉴 - 背包列表
function StrangeCatalogWGData:SetHandBookBag(protocol)
	self.grid_list = protocol.grid_list
	self:CacheAllHandBookList()
end

function StrangeCatalogWGData:GetQWBagSingleServerInfo(seq)
	return self.grid_list[seq]
end

function StrangeCatalogWGData:GetQWBagAllServerInfo()
	return self.grid_list
end

function StrangeCatalogWGData:GetBigTypeDataList()
	local data_list = {}
	for k, cfg_list in pairs(self.qw_act_type_cfg) do
		local data = {}
		data.big_type = k
		data.is_remind = false
		for i, v in ipairs(cfg_list) do
			if self:SingleRemind(v.seq) then
				data.is_remind = true
				break
			end
		end
		table.insert(data_list, data)
	end
	return data_list
end

function StrangeCatalogWGData:GetQiWuDataList(big_type)
	local cfg_list = self.qw_act_type_cfg[big_type]
	if not cfg_list then
		return
	end

	local data_list = {}
	for i, v in ipairs(cfg_list) do
		local data = self:GetSingleInfo(v.seq)
		table.insert(data_list, data)
	end
	return data_list
end

--[[
-- 更新显示数据
function StrangeCatalogWGData:UpdateShowList()
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local display_cfg = self:GetDisplayCfg()
	local big_type, sub_type, quality_type = 0, 1, 1
	local big_type_list = {}

	for k,v in ipairs(display_cfg) do
		big_type = v.big_type
		sub_type = v.sub_type
		if sub_type == 0 and v.open_day <= day then
			big_type_list[big_type] = {big_type = big_type, name = v.name, is_remind = false, sub_type_list = {}}
		end

		if sub_type ~= 0 and big_type_list[big_type] then
			big_type_list[big_type].sub_type_list[sub_type] = {sub_type = sub_type, name = v.name, is_remind = false, quality_type_list = {}}
		end
	end

	local remind = false
	for i = 0, #self.qw_act_cfg do
		local info = self.qw_act_cfg[i]
		big_type = info.big_type
		sub_type = info.sub_type
		quality_type = info.quality_type

		local qua_table = (((big_type_list[big_type] or empty_table).sub_type_list or empty_table)[sub_type] or empty_table).quality_type_list
		if qua_table ~= nil then
			remind = self:SingleRemind(info.seq)
			if remind then
				big_type_list[big_type].is_remind = true
				big_type_list[big_type].sub_type_list[sub_type].is_remind = true
			end

			if not qua_table[quality_type] then
				qua_table[quality_type] = {}
			end

			local single_info = self:GetSingleInfo(info.seq)
			table.insert(qua_table[quality_type], single_info)
		end
	end

	return big_type_list
end
]]

-- 图鉴 - 背包信息变化
function StrangeCatalogWGData:UpdateBagInfo(protocol)
	local new_data = protocol.equip_item
	local index = new_data.index
	local item_id = new_data.item_id
	local old_data = self:GetQWBagSingleServerInfo(index)
	local old_num = old_data and old_data.num or 0
	local new_num = new_data.num

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	if (old_data == nil or old_data.item_id == nil or old_data.item_id <= 0) and new_num > old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_ADD
		self:CacheHandBookData(change_reason, new_data, new_num - old_num)
	elseif old_data and old_data.item_id > 0 and new_num < old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_REMOVE
		if new_data.item_id <= 0 then
			item_id = old_data.item_id
			new_data = nil
			self:CacheHandBookData(change_reason, old_data, old_num - new_num)
		else
			self:CacheHandBookData(change_reason, new_data, old_num - new_num)
		end
	else
		self:CacheHandBookData(change_reason, new_data, new_num - old_num)
	end

	self.grid_list[index] = new_data

	StrangeCatalogWGCtrl.Instance:OnItemDataChange(item_id, index, change_reason, old_num, new_num)
end

-- 缓存列表
function StrangeCatalogWGData:CacheAllHandBookList()
	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	local bag_list = self:GetQWBagAllServerInfo()
	for k,v in pairs(bag_list) do
		self:CacheHandBookData(change_reason, v, v.num)
	end
end




-- 获得有红点的第一个大标签
--[[
function StrangeCatalogWGData:GetListRemindBigType()
	local big_index = 1
	local list = self:UpdateShowList()
	if IsEmptyTable(list) then
		return big_index
	end

	for k,v in ipairs(list) do
		if v.is_remind then
			big_index = v.big_type
			break
		end
	end

	return big_index
end

-- 获得有红点的第一个子标签
function StrangeCatalogWGData:GetListRemindSubType(index)
	local sub_index = 1
	local list = self:UpdateShowList()
	if IsEmptyTable(list) then
		return sub_index
	end

	if list[index].sub_type_list then
		for k, v in ipairs(list[index].sub_type_list) do
			if v.is_remind then
				sub_index = v.sub_type
				break
			end
		end
	end

	return sub_index
end

-- 获得有红点的第一个品质类 以及索引
function StrangeCatalogWGData:GetListRemindQuaType(big_index, sub_index)
	local qua_index = 1
	local seq = 0
	local list = self:UpdateShowList()
	if IsEmptyTable(list) then
		return qua_index, seq
	end

	local quality_list = list[big_index].sub_type_list[sub_index].quality_type_list
	if quality_list then
		for k, v in ipairs(quality_list) do
			for k1, v1 in ipairs(v) do
				if v1.is_remind then
					qua_index = k
					seq = v1.seq
					return qua_index, seq
				end
			end
		end

		seq = quality_list[1][1].seq												-- 默认第一个
	end

	return qua_index, seq
end
]]

-- 图鉴 - 羁绊
function StrangeCatalogWGData:GetJiBanItemListBySeq(seq)
	if self.jiban_item_list[seq] then
		return self.jiban_item_list[seq]
	end

	local item_list = {}
	local level = math.max(1, self:GetQWJiBanServerInfoBySeq(seq))
	local jiban_cfg = self:GetJiBanCfgBySeq(seq, level)
	if IsEmptyTable(jiban_cfg) then
		return item_list
	end

	local item_str_list = Split(jiban_cfg.need_id, ",")
	for k, v in pairs(item_str_list) do
		local stuff_seq = tonumber(v)
		local active_cfg = self:GetActPartItem(stuff_seq)
		if active_cfg then
			table.insert(item_list, {item_id = active_cfg.active_item_id, seq = active_cfg.seq})
		end
	end

	if #item_list > 0 then
		self.jiban_item_list[seq] = item_list
	end

	return item_list
end

-- 改变缓存数据
function StrangeCatalogWGData:CacheHandBookData(change_reason, data, change_num)
	if data == nil or change_num == 0 then
		return
	end

	local item_id = data.item_id
	local item_num = self.qw_item_id_num[item_id] or 0
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		item_num = item_num - change_num
		item_num = item_num > 0 and item_num or 0
	else
		item_num = item_num + change_num
	end

	self.qw_item_id_num[item_id] = item_num
end

-- 获取 道具数量
function StrangeCatalogWGData:GetItemNum(item_id)
	return self.qw_item_id_num[item_id] or 0
end

-- 图鉴 - 分解列表
function StrangeCatalogWGData:GetQWTJResloveBagList()
	local reslove_list = {}
	local bag_list = self:GetQWBagAllServerInfo()
	for k, v in pairs(bag_list) do
		table.insert(reslove_list, v)
	end

	return reslove_list
end

-- 图鉴 - 分解获得
function StrangeCatalogWGData:GetDecomposeItemGetNum(item_id)
	local cfg = self:GetDecomposeCfgByItemId(item_id)
	if IsEmptyTable(cfg) then
		return 0
	end

	return (((cfg or {}).reward_item or {})[0] or {}).num
end

-- 图鉴 - 分解获得
function StrangeCatalogWGData:CalculateDecomposeGetNum(reslove_list)
	if IsEmptyTable(reslove_list) then
		return 0
	end

	local total, cfg_count,cfg = 0, 0, nil
	for k, v in pairs(reslove_list) do
		cfg = self:GetDecomposeCfgByItemId(v.item_id)
		cfg_count = (((cfg or {}).reward_item or {})[0] or {}).num or 0
		total = total + (v.num * cfg_count)
	end
	return total
end


--========================属性战力========================
--根据索引获取对应的索引属性
function StrangeCatalogWGData:GetCurBaseAttrList(base_attr_seq, level, add_value)
	local attr_list = {}
	local cfg = self:GetBaseAttrCfgBySeq(base_attr_seq)
	local max_level = self.level_attr_cfg[#self.level_attr_cfg].max_level
	local attr_id, attr_value = 0, 0
	local max_attr_num = 4
	local level_attr_cfg = self:GetLevelAttrCfg()

	if not cfg then
		return attr_list
	end

	for i = 1, max_attr_num do
		attr_id = cfg["attr_id" .. i]
		attr_value = cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			local data = {}
			data.attr_id = attr_id
			data.attr_value = math.ceil(attr_value * add_value)
			data.attr_str = attr_str
			if level < max_level then
				local next_level = level + 1
				for k, v in pairs(level_attr_cfg) do
					if next_level >= v.min_level and next_level <= v.max_level then
						data.add_value = math.ceil(attr_value * v.attr_per / 10000)						-- 2023/12/26 策划骚需求 要求计算衰减值
					end
				end
			else
				data.add_value = 0
			end

			table.insert(attr_list, data)
		end
	end

	return attr_list
end

--根据索引获取对应的羁绊属性
function StrangeCatalogWGData:GetJiBanAttrListBySeq(seq)
	local attr_list = {}
	local special_attr = {}
	local level = self:GetQWJiBanServerInfoBySeq(seq)
	local jiban_cfg = {}
	local next_jiban_cfg = self:GetJiBanCfgBySeq(seq, level + 1)

	if level == 0 and not IsEmptyTable(next_jiban_cfg) then
		jiban_cfg.special_attr_value = 0
		for i = 1, 2 do
			jiban_cfg["attr_id" .. i] = next_jiban_cfg["attr_id" .. i]
			jiban_cfg["attr_value" .. i] = 0
		end
	else
		jiban_cfg = self:GetJiBanCfgBySeq(seq, level)
	end

	if IsEmptyTable(jiban_cfg) then
		return attr_list, special_attr
	end

	attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(jiban_cfg, next_jiban_cfg, "attr_id", "attr_value", 1, 2)
	special_attr = {attr_str = Language.StrangeCatalog.JiBanAdd, attr_value = jiban_cfg.special_attr_value, add_value = next_jiban_cfg.special_attr_value}

	return attr_list, special_attr
end

function StrangeCatalogWGData:GetLevelAddValue(level)
	local add_value = 0
	for k, v in ipairs(self.level_attr_cfg) do
		if level <= v.max_level then
			add_value = add_value + (v.attr_per / 10000) * (level - v.min_level + 1)
			break
		else
			add_value = add_value + (v.attr_per / 10000) * (v.max_level - v.min_level + 1)
		end
	end

	return add_value
end

function StrangeCatalogWGData:GetJBSpecialAttr(jb_seq)
	local level = self.jiban_level_list[jb_seq]
	if level > 0 then
		local jiban_cfg = self:GetJiBanCfgBySeq(jb_seq, level)
		if not IsEmptyTable(jiban_cfg) then
			local special_attr_value = jiban_cfg.special_attr_value
			return special_attr_value
		end
	end

	return 0
end

-- 战力计算
function StrangeCatalogWGData:TuJianRecordList()
	local attribute = AttributePool.AllocAttribute()
	self.tujian_spe_attr = {}
	self.tujian_jiban_attr = {}
	local add_jiban_per = 1

	--图鉴总激活属性
	for k, v in pairs(self.tujian_list) do
		if v.level > 0 then
			local add_value = self:GetLevelAddValue(v.level)
			if self:GetActPartItem(v.seq) then
				local base_attr_seq = self:GetActPartItem(v.seq).base_attr_seq
				local tj_attr = self:GetCurBaseAttrList(base_attr_seq, v.level, add_value)	-- 索引对应属性
				if not IsEmptyTable(tj_attr) then
					for key, value in pairs(tj_attr) do
						if attribute[value.attr_str] then
							attribute[value.attr_str] = attribute[value.attr_str] + value.attr_value
						end
					end
				end

				local qiwu_xl_level = v.zhuling_level
				local special_attr_seq = self:GetActPartItem(v.seq).special_attr_seq
				local cur_spe_attr = StrangeCatalogWGData.Instance:GetCurSpecialAttr(special_attr_seq, qiwu_xl_level)
				if not IsEmptyTable(cur_spe_attr) then
					local spe_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_spe_attr, nil, "attr_id", "attr_value", 1, 1)
					table.insert(self.tujian_spe_attr, spe_list)
				end
			end
		end
	end

	for k, v in pairs(self.jiban_show_seq_cfg) do
		local jb_special_attr_value = self:GetJBSpecialAttr(v.seq)
		add_jiban_per = add_jiban_per + jb_special_attr_value
		local level = self:GetQWJiBanServerInfoBySeq(v.seq)
		if level > 0 then
			local jiban_cfg = self:GetJiBanCfgBySeq(v.seq, level)
			local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(jiban_cfg, nil, "attr_id", "attr_value", 1, 2)
			table.insert(self.tujian_jiban_attr, attr_level_list)
		end
	end

	-- 图鉴总激活属性万分比
	for k, v in pairs(attribute) do
		if v > 0 then
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
			if is_per then
				if attribute[k] then
					attribute[k] = v * ((add_jiban_per * 0.0001))
				end
			end
		end
	end

	-- 羁绊总激活属性
	for k, v in pairs(self.tujian_jiban_attr) do
		for key, value in ipairs(v) do
			if attribute[value.attr_str] then
				attribute[value.attr_str] = attribute[value.attr_str] + value.attr_value
			end
		end
	end

	-- 图鉴特殊属性
	for k, v in pairs(self.tujian_spe_attr) do
		for key, value in ipairs(v) do
			if attribute[value.attr_str] then
				attribute[value.attr_str] = attribute[value.attr_str] + value.attr_value
			end
		end
	end

	local cap_value = AttributeMgr.GetCapability(attribute)

	return cap_value
end




-- 红点
function StrangeCatalogWGData:GetStrangeCatalogRemind()
	for k, v in pairs(self.qw_act_cfg) do
		if self:SingleRemind(k) then
			return 1
		end
	end
	-- 羁绊红点

	return 0
end

-- 红点 大类型
function StrangeCatalogWGData:GetBigTypeRemind(big_type)
	local cfg_list = self.qw_act_type_cfg[big_type]
	if not cfg_list then
		return
	end

	for k, v in pairs(cfg_list) do
		if self:SingleRemind(v.seq) then
			return true
		end
	end

	return false
end

function StrangeCatalogWGData:SingleRemind(seq)
	if self:QiangHuaRemind(seq) then
		return true
	end

	if self:ZhuLingRemind(seq) then
		return true
	end

	return false
end

function StrangeCatalogWGData:QiangHuaRemind(seq)
	local act_cfg = self:GetActPartItem(seq)
	if not act_cfg then
		return false
	end

	-- 未解锁双修妹子不能激活
	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(act_cfg.big_type)
	if not artifact_data or artifact_data.level <= 0 then
		return false
	end

	local cur_sevrer_info = self:GetQWSingleServerInfo(seq)
	if not cur_sevrer_info then
		return false
	end

	local level = cur_sevrer_info.level
	local level_attr_cfg = StrangeCatalogWGData.Instance:GetLevelAttrCfg()
	local max_level = level_attr_cfg[#level_attr_cfg].max_level
	if max_level and max_level <= level then
		return false
	end

	local id_num = self:GetItemNum(act_cfg.active_item_id)

	return id_num >= 1
end

function StrangeCatalogWGData:ZhuLingRemind(seq)
	local active_cfg = self:GetActPartItem(seq)
	if not active_cfg then
		return false
	end

	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(active_cfg.big_type)
	if not artifact_data or artifact_data.level <= 0 then
		return false
	end

	local cur_sevrer_info = self:GetQWSingleServerInfo(seq)
	if not cur_sevrer_info then
		return false
	end

	local special_attr_seq = active_cfg.special_attr_seq
	local spe_seq_cfg = self:GetSpecialBySeq(special_attr_seq)
	if not spe_seq_cfg then
		return false
	end

	local max_zhuling_level = #spe_seq_cfg
	local qiwu_xl_level = cur_sevrer_info.zhuling_level
	local qiwu_qh_level = cur_sevrer_info.level
	if max_zhuling_level <= qiwu_xl_level then
		return false
	end

	local next_zhuling_cfg = self:GetZhuLingSingleCfg(qiwu_xl_level + 1)
	if not next_zhuling_cfg then
		return false
	end

	if qiwu_qh_level < next_zhuling_cfg.need_level then
		return false
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(next_zhuling_cfg.consume_item_id)
	local need_num = next_zhuling_cfg.consume_item_num

	return num >= need_num
end

--羁绊红点
function StrangeCatalogWGData:GetJiBanRemind()
	local cfg_list = self:GetJiBanAllGroup()
	for k, v in pairs(cfg_list) do
		if self:GetJibanGroupRemind(k) then
			return 1
		end
	end

	return 0
end

--羁绊组红点
function StrangeCatalogWGData:GetJibanGroupRemind(jiban_group)
	local cfg_list = self:GetJiBanShowList(jiban_group)
	if not cfg_list then
		return false
	end
	for k, v in pairs(cfg_list) do
		if self:GetJibanSingleRemind(v.seq) then
			return true
		end
	end

	return false
end

-- 单个羁绊红点
function StrangeCatalogWGData:GetJibanSingleRemind(seq)
	local item_list = self:GetJiBanItemListBySeq(seq)

	local level = self:GetQWJiBanServerInfoBySeq(seq)
	local next_jiban_cfg = self:GetJiBanCfgBySeq(seq, level + 1)

	local can_up_level = false  --判断升级的条件
	if not IsEmptyTable(next_jiban_cfg) then
		for k, v in ipairs(item_list) do
			local cur_info = self:GetQWSingleServerInfo(v.seq)
			local qiwu_xl_level = cur_info.zhuling_level
			if qiwu_xl_level >= next_jiban_cfg.need_zhuling_level then
				can_up_level = true
			else
				can_up_level = false
				break
			end
		end
	end
	return can_up_level
end

function StrangeCatalogWGData:IsQiWuItem(item_id)
	local cfg = self:GetZhuLingAllCfg()
	if not cfg then
		return false
	end

	for k, v in pairs(cfg) do
		local qiwu_item = v.consume_item_id
		if qiwu_item ~= "" and qiwu_item == item_id then
			return true
		end
	end

	return false
end