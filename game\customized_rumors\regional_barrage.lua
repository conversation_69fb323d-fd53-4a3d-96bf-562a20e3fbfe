-- 区域弹幕
RegionalBarrage = RegionalBarrage or BaseClass(BaseRender)

local INTERVAL_TIME = 0.5

function RegionalBarrage:__init(instance, bundle, asset)
    if nil == self.root_node then
        local common_bundle, common_asset = ResPath.GetCustomizedRumorsWidgets("BullscreenBarrageCell")
        self:LoadAsset(common_bundle, common_asset, instance.transform, function ()
            self.danmu_item_list = {}
            self.occupy_danmu_item_cache = {}
            self.is_init_danmu_pos = false

            self.bullscreen_barrage_cell = self.node_list.bullscreen_barrage_cell
            self.bullscreen_barrage_area = self.node_list.bullscreen_barrage_area
        end)
    end

    self.create_danmu_num = 0
    self.temporary_danmu_list = {}
end

function RegionalBarrage:__delete()
    self.bullscreen_barrage_cell = nil
    self.bullscreen_barrage_area = nil
    self.get_desc_str_fun = nil

    if self.occupy_danmu_item_cache then
        for k, v in pairs(self.occupy_danmu_item_cache) do
            v:DeleteMe()
        end
        
        self.occupy_danmu_item_cache = nil
    end

    self.danmu_item_list = nil
    self.is_init_danmu_pos = nil
    self.danmu_track_list = nil
    self.create_danmu_num = nil
    self.temporary_danmu_list = nil
    self.cell_bundle = nil
    self.cell_asset = nil

    self:StopAndClearDanMu()
end

function RegionalBarrage:StopAndClearDanMu()
    self:StopShowDanMu()
    self:ClearAllDanMu()
end

function RegionalBarrage:SetData(...)
    BaseRender.SetData(self, ...)
end

function RegionalBarrage:StartShowDanMu(interval)
    self:StopShowDanMu()

    local interval = interval or INTERVAL_TIME
    self.count_down_timer = GlobalTimerQuest:InvokeRepeating(function()
       self:DanMuScheduling()
    end, 0, interval, 99999999)
end

function RegionalBarrage:ClearAllDanMu()
    if not IsEmptyTable(self.danmu_item_list) then
        for k, v in pairs(self.danmu_item_list) do
            if nil ~= v then
                v:DeleteMeInfo()
            end
        end
    end
end

-- 修改实例的弹幕路径
function RegionalBarrage:ChangeCellAsset(cell_bundle, cell_asset)
    self.cell_bundle = cell_bundle
    self.cell_asset = cell_asset
end

function RegionalBarrage:StopShowDanMu()
    if self.count_down_timer then
        GlobalTimerQuest:CancelQuest(self.count_down_timer)
        self.count_down_timer = nil
    end
end

function RegionalBarrage:InitBarrageTrajectory()
    if self.bullscreen_barrage_area then
        local cell_height = 30

        local height = self.bullscreen_barrage_area.rect.rect.height
        local width = self.bullscreen_barrage_area.rect.rect.width
        local start_x = width / 2
        local track_num =  math.floor((height + cell_height) / (cell_height * 2))
        
        if track_num > 0 then
            local danmu_track_list = {}

            if track_num % 2 == 0 then
                for i = 1, track_num do
                    local height = cell_height * 2 * math.floor((i - 1) / 2) + cell_height
                    height = height * (i % 2 == 0 and -1 or 1)
                    table.insert(danmu_track_list, {track_id = i, start_y = height, start_x = start_x})
                end
            else
                for i = 1, track_num do
                    if i == 1 then
                        table.insert(danmu_track_list, {track_id = i, start_y = 0, start_x = start_x})
                    else
                        local height = cell_height * 2 + cell_height* 2 * math.floor((i - 2) / 2)
                        height = height * (i % 2 == 0 and -1 or 1)
                        table.insert(danmu_track_list, {track_id = i, start_y = height, start_x = start_x})
                    end
                end
            end
    
            self.danmu_track_list = danmu_track_list
        end
    end
end

function RegionalBarrage:DanMuScheduling()
    if self.get_danmu_str_func then
        local desc_danmu_info = self:StackDanMuInfoData()

        if not IsEmptyTable(desc_danmu_info) then
            if not self.is_init_danmu_pos then
                self.is_init_danmu_pos = true
                self:InitBarrageTrajectory()
            end

            local danmu_track = self:GetDaumuTrack()
            if not IsEmptyTable(danmu_track) then
                self:CreatDanMuRender(desc_danmu_info, danmu_track)
            end
        end
    end
end

function RegionalBarrage:StackDanMuInfoData()
    if not IsEmptyTable(self.temporary_danmu_list) then
        local danmu_info = table.remove(self.temporary_danmu_list)

        if not IsEmptyTable(danmu_info) then
            return danmu_info
        end
    end

    return self:get_danmu_str_func()
end

-- 插入一条特殊弹幕
function RegionalBarrage:AddOneTemporaryDanMu(danmu_info)
    if not IsEmptyTable(danmu_info) then
        table.insert(self.temporary_danmu_list, danmu_info)
    end
end

function RegionalBarrage:GetDaumuTrack()
    if IsEmptyTable(self.danmu_track_list) then
        return
    end

    for k, v in pairs(self.danmu_track_list) do
        local danmu_item = self:GetCurOccupyTrackDanMuItem(v.track_id)
        
        if nil ~= danmu_item then
            if danmu_item.IsNotOccupyTrack and danmu_item:IsNotOccupyTrack() then
                return v
            end
        else
            return v
        end
    end
end

function RegionalBarrage:CreatDanMuRender(danmu_info, danmu_track)
    local bundle_name, asset_name = ResPath.GetCustomizedRumorsWidgets("BullscreenBarrageItemCell")
    local final_bundle_name = self.cell_bundle  or bundle_name
    local final_bundle_asset = self.cell_asset  or asset_name
    local cell = RegionalBarrageDanmuItemRender.New()
    cell:SetStopCallBack(BindTool.Bind(self.OnDanMuItemStop,self))
    cell:LoadAsset(final_bundle_name, final_bundle_asset, self.node_list.bullscreen_barrage_area.transform)

    self.danmu_item_list[cell] = cell
    cell:SetData(danmu_info, danmu_track)

    if self.occupy_danmu_item_cache[danmu_track.track_id] then
        self.occupy_danmu_item_cache[danmu_track.track_id] = nil
    end

    self.occupy_danmu_item_cache[danmu_track.track_id] = cell
    self.create_danmu_num = self.create_danmu_num + 1
end

function RegionalBarrage:GetCurOccupyTrackDanMuItem(track_id)
    return (self.occupy_danmu_item_cache or {})[track_id]
end

function RegionalBarrage:SetGetDanMuInfoFunc(get_info_func)
    if self.get_danmu_str_func then
        self.get_danmu_str_func = nil
    end

    self.get_danmu_str_func = get_info_func
end

function RegionalBarrage:OnDanMuItemStop(item)
    if nil == item then
        return
    end

    if self.danmu_item_list and self.danmu_item_list[item] then
        self.danmu_item_list[item] = nil
    end
end

-------------------------------------- 霸屏弹幕Item --------------------------------------
--[[
    {desc_content = "内容",
	need_random_color = false,
	color_data_list = ITEM_COLOR_DARK,
    bg_bundle, bg_asset = , -- 背景
    move_speed = 100,
}
]]

RegionalBarrageDanmuItemRender = RegionalBarrageDanmuItemRender or BaseClass(BaseRender)

local DANMU_MOVE_SPEED = 100

function RegionalBarrageDanmuItemRender:__delete()
    self.danmu_info = nil
    self.danmu_track = nil
    self:StopDanMuAni()
end

function RegionalBarrageDanmuItemRender:SetData(danmu_info, danmu_track)
    if IsEmptyTable(danmu_info) or IsEmptyTable(danmu_track) then
        return
    end

    self.danmu_info = danmu_info
    self.danmu_track = danmu_track
    BaseRender.SetData(self)
end

function RegionalBarrageDanmuItemRender:OnFlush()
    if IsEmptyTable(self.danmu_info) or IsEmptyTable(self.danmu_track) then
        return
    end

    self.node_list.ph_danmu_item.rect.anchoredPosition = Vector2(1000, 0)
    self.node_list.des_text.text.text = self.danmu_info.desc_content or ""

    if self.danmu_info.need_random_color and self.danmu_info.color_data_list then
        self.node_list.des_text.text.color = Str2C3b(self.danmu_info.color_data_list[math.random(0, 7)])
    else
        self.node_list.des_text.text.color = Str2C3b(self.danmu_info.text_color)
    end

    if self.danmu_info.bg_bundle and self.danmu_info.bg_asset then
        self.node_list.ph_danmu_item.image:LoadSprite(self.danmu_info.bg_bundle, self.danmu_info.bg_asset)
    end

    self.node_list.ph_danmu_item.image.enabled = not self.danmu_info.is_shield_bg
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.des_text.rect)

    self:StopDanMuAni()

    self.calculate_time_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
        self:DoMove(self.danmu_info, self.danmu_track)
    end, self), 0.1)
end

function RegionalBarrageDanmuItemRender:DoMove(danmu_info, danmu_track)
    local sizeDelta = ((self.node_list.des_text or {}).rect or {}).sizeDelta
    
    if sizeDelta and sizeDelta.x then
        local width = sizeDelta.x
        local pos_x, pos_y = danmu_track.start_x, danmu_track.start_y
        pos_x = pos_x + width / 2 + math.floor(math.random(1, width))
        self.node_list.ph_danmu_item.rect.anchoredPosition = Vector2(pos_x, pos_y)
        local speed = danmu_info.move_speed or DANMU_MOVE_SPEED
        local duration = pos_x * 2 / speed
        self.move_tween = self.node_list.ph_danmu_item.rect:DOAnchorPosX(-pos_x, duration):SetEase(DG.Tweening.Ease.Linear):OnComplete(function ()
            self:DeleteMeInfo()
        end)
    else
        self:DeleteMeInfo()
    end
end

function RegionalBarrageDanmuItemRender:IsNotOccupyTrack()
    if self.node_list and self.node_list.ph_danmu_item then
        local width = self.node_list.des_text.rect.sizeDelta.x
        return self.node_list.ph_danmu_item.rect.anchoredPosition.x < (self.danmu_track.start_x - width / 2 - 50)
    end

    return true
end

function RegionalBarrageDanmuItemRender:SetStopCallBack(call_back)
    self.stop_call_back = call_back
end

function RegionalBarrageDanmuItemRender:DeleteMeInfo()
    if self.stop_call_back then
        self.stop_call_back(self)
    end

    self:StopDanMuAni()
    self:DeleteMe()
end

function RegionalBarrageDanmuItemRender:StopDanMuAni()
    if self.calculate_time_quest then
        GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
        self.calculate_time_quest = nil
    end

    if self.move_tween then
        self.move_tween:Kill()
        self.move_tween = nil
    end
end