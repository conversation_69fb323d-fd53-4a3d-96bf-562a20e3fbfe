FuhuoNoBtnView = FuhuoNoBtnView or BaseClass(FuhuoView)

function FuhuoNoBtnView:__init()

end

function FuhuoNoBtnView:__delete()

end

function FuhuoNoBtnView:LoadCallBack()
	FuhuoView.LoadCallBack(self)
	self.gongneng_sort = {
		{	-- 角色
			img_name = 'btn_role',
			view_name = GuideModuleName.RoleView,
			tab_index = TabIndex.role_intro,
		},
		{	-- 装备
			img_name = 'btn_equip',
			view_name = GuideModuleName.Equipment,
			tab_index = TabIndex.equipment_strength,
		},
		-- {	-- 坐骑
		-- 	img_name = 'btn_ride',
		-- 	view_name = GuideModuleName.Mount,
		-- 	tab_index = TabIndex.mount_huanhua,
		-- },
	}
	self.btn_free_fuhuo:SetActive(false)
	self.btn_gold_fuhuo:SetActive(false)
end

function FuhuoNoBtnView:OnFlush(param_t)
	if  self.killer_name == "" then  --self.killer_objid == 0 or
		self.rich_tips:SetActive(false)
	else
		self.rich_tips:SetActive(true)
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.killer_level)
		self.node_list.feixian_img:SetActive(false)
		self.node_list.tips_1.text.text = string.format(Language.Fuhuo.FuHuoTips_1, self.killer_name)
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_1.rect)
		self.node_list.tips_2.text.text = string.format(Language.Fuhuo.FuHuoTips_2,(self.is_role and role_level) or self.killer_level)
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_2.rect)
	end
	for k,v in pairs(param_t) do
		if k == "all" then
			local tips = string.format(Language.Fuhuo.FuHuoTips_1, self.killer_name)
			EmojiTextUtil.ParseRichText(self.rich_tips, tips, FuhuoView.FONTSIZE, COLOR3B.WHITE)
			self.rich_tips:refreshView()

		elseif k == "daojishi" then
			self:DaoJiShi(v.time)
		end
	end
end
