local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

ConfigManager = ConfigManager or BaseClass()

local cfg_list = {}
function ConfigManager:__init()
	if ConfigManager.Instance ~= nil then
		error("[ConfigManager] attempt to create singleton twice!")
		return
	end
	ConfigManager.Instance = self
	self.load_model_data = {}

	if is_develop then
		self.cfg_list = cfg_list
	end
end

function ConfigManager:__delete()
	for k, v in pairs(cfg_list) do
		_G.package.loaded[k] = nil
	end
	self.load_model_data = {}

	ConfigManager.Instance = nil
end

local real_pairs = pairs
function pairs(t)
	if is_develop then
		develop_mode:OnLuaCall("pairs", t)
	end

	local metable = getmetatable(t)
	if nil ~= metable and nil ~= rawget(metable, "__pairs") then
		return metable.__pairs(t)
	end
	return real_pairs(t)
end

local real_iparis = ipairs
function ipairs(t)
	if is_develop then
		develop_mode:OnLuaCall("pairs", t)
	end
	return real_iparis(t)
end

local real_next = next
function next(t)
	local metable = getmetatable(t)
	if nil ~= metable and rawget(metable, "__next") then
		return metable.__next(t)
	end
	return real_next(t)
end

-- 设置分表原表
function SetConfigSplitMetable(cfg_name, cfg)
	local mt = {}
	mt.__index = function (tbl, key)
		local old_metatable = getmetatable(tbl)
		setmetatable(tbl, nil)

		local lua_name = "config/auto_new/" .. cfg_name .. "/" .. key .. "_auto"
		local sub_t = require(lua_name)
		local meta_table_map = sub_t["meta_table_map"]
		SetConfigMetatables(sub_t, cfg[key .. "_default_table"], lua_name, nil, meta_table_map)
		if nil ~= meta_table_map then
			sub_t["meta_table_map"] = nil
		end
		tbl[key] = sub_t

		setmetatable(tbl, old_metatable)

		-- 监测lua性能
		if is_develop then
			develop_mode:OnLuaCall("require_config", lua_name)
		end
		return sub_t
	end
	setmetatable(cfg, mt)
end

function ConfigManager:GetConfig(lua_name)
	local cfg = cfg_list[lua_name]
	if nil == cfg then
		cfg = require(lua_name)
		if nil ~= cfg then
			cfg_list[lua_name] = cfg
			CheckConfigMetable(cfg, lua_name)
		end

		-- 监测lua性能
		if is_develop then
			develop_mode:OnLuaCall("require_config", lua_name)
		end
	end

	return cfg
end

-- 获取数据配置
function ConfigManager:GetAutoConfig(lua_name)
	return self:GetConfig("config/auto_new/" .. lua_name)
end

-- 获取物品数据配置
function ConfigManager:GetAutoItemConfig(lua_name)
	return self:GetConfig("config/auto_new/item/" .. lua_name)
end

-- 获取场景配置
function ConfigManager:GetSceneConfig(scene_id)
	local new_scene_id = tonumber(scene_id)
	if new_scene_id == nil or new_scene_id == 0 then
		local server_id = "_"
		if GameVoManager ~= nil and GameVoManager.Instance ~= nil then
			local user_vo = GameVoManager.Instance:GetUserVo()
			if user_vo and user_vo.plat_server_id ~= nil and user_vo.plat_server_id ~= "" then
				server_id = user_vo.plat_server_id
			end
		end
	
		local error_str = string.format("加载场景配置失败! scene_id = %s,  哪个 屌平台:%s, 屌服:%s", scene_id, ChannelAgent.GetChannelID(), server_id)
		print_error(error_str)
		return
	end

	return self:GetConfig("config/scenes/scene_" .. new_scene_id)
end

function ConfigManager:GetRoleAutoPrefabConfig(sex, prof)
	return self:GetConfig(string.format("config/prefab_data/config/character/%s_config", RoleWGData.GetJobModelId(sex, prof)))
end

function ConfigManager:GetRolePowerAutoPrefabConfig(sex, prof, power_type)
	local str = string.format("config/prefab_data/config/character/%s0%s_config", RoleWGData.GetJobModelId(sex, prof), power_type)
	return self:GetConfig(str)
end

local TouchFileFlags = {}
local IsLuaFileExist = GameRoot.Instance.IsLuaFileExist
-- 获取模型配置数据
function ConfigManager:GetPrefabDataAutoConfig(model_type, model_id)
	local path = "config/prefab_data/config/".. model_type .. "/" .. model_id.."_config"
	if nil == self.load_model_data[path] and nil == TouchFileFlags[path] then
		if IsLuaFileExist(path) then
			self.load_model_data[path] = true
		end
		TouchFileFlags[path] = true
	end

	if self.load_model_data[path] then
		return self:GetConfig(path)
	else
		return nil
	end
end

-- 配置分表读取常驻内存表格(只require一次,key就是配置的名字)
local split_persistent_cfg_list = {
	-- ["config/auto_new/randactivityconfig_1_auto"] = "randactivityconfig_1_auto",
	-- ["config/auto_new/randactivityconfig_2_auto"] = "randactivityconfig_2_auto",
}

function CheckLuaConfig(path, lua_cfg)
	local split_lua_name = split_persistent_cfg_list[path]
	if split_lua_name then
		SetConfigSplitMetable(split_lua_name, lua_cfg)
	else
		CheckConfigMetable(lua_cfg, path)
	end
	cfg_list[path] = lua_cfg
end

function CheckConfigMetable(lua_cfg, lua_name)
	if nil == lua_cfg then
		return
	end

	local lua_cfg_old_metable = getmetatable(lua_cfg)
	setmetatable(lua_cfg, nil)

	local depth = nil ~= lua_cfg["default_table"] and 1 or 2

	if 1 == depth then
		local default_table = lua_cfg["default_table"]
		local meta_table_map = lua_cfg["meta_table_map"]
		if nil ~= default_table then
			lua_cfg["default_table"] = nil
			if nil ~= meta_table_map then
				lua_cfg["meta_table_map"] = nil
			end
			SetConfigMetatables(lua_cfg, default_table, lua_name, nil, meta_table_map)
		end

	elseif 2 == depth then
		for k, v in real_pairs(lua_cfg) do
			local default_table = lua_cfg[k .. "_default_table"]
			local meta_table_map = lua_cfg[k .. "_meta_table_map"]
			if nil ~= default_table then
				lua_cfg[k .. "_default_table"] = nil
				if nil ~= meta_table_map then
					lua_cfg[k .. "_meta_table_map"] = nil
				end
				SetConfigMetatables(v, default_table, lua_name, k, meta_table_map)
			end
		end
	end

	setmetatable(lua_cfg, lua_cfg_old_metable)
end

local config_key_record_table = {}
local warning_table = {}

local function WarningFliter(source)
	return not string.find(source, "attribute_mgr") and not string.find(source, "item_cell")
end

local function _SetDebugMetaTable(cfg, default_table, lua_name, _key, meta_table)
    local func = function (tbl, key)
        local nk, nv = real_next(default_table, key)
        if nk then
            nv = tbl[nk]
        end

        return nk, nv
    end

	local mt = {}
	local vtbl = {}

	for k,v in pairs(cfg) do
		vtbl[k] = v
		cfg[k] = nil
	end

	mt.__index = function(tbl, key)
		local t = config_key_record_table[lua_name]
		if nil ~= _key then
			t = t[_key]
		end

		local value = vtbl[key]
		if nil ~= value then
			t[key] = t[key] + 1
			return value
		end

		-- 如果是获取的数据是由公式算的话执行公式的逻辑
		if "function" == type(default_table[key]) then
			local t_key = default_table[key .. "_params"]
			-- 不知道什么原因导致关闭的时候会有个function存在导致直接调用了报错
			if nil == t_key then
				return default_table[key]
			end

			local len = #t_key
			-- 目前最大只供4个参数作为运算，以后有需要的再加吧。
			if 1 == len then
				return default_table[key](tbl[t_key[1]])
			elseif 2 == len then
				return default_table[key](tbl[t_key[1]], tbl[t_key[2]])
			elseif 3 == len then
				return default_table[key](tbl[t_key[1]], tbl[t_key[2]], tbl[t_key[3]])
			elseif 4 == len then
				return default_table[key](tbl[t_key[1]], tbl[t_key[2]], tbl[t_key[3]], tbl[t_key[4]])
			end
		else
			if nil == t[key] then
				local flag = nil == warning_table[lua_name]
				warning_table[lua_name] = warning_table[lua_name] or {}
				if nil == _key then
					if flag or nil == warning_table[lua_name][key] then
						local info = debug.getinfo(2, "Sln")
						-- 过滤一些基础类
						if WarningFliter(info.source) then
							print_warning("找不到对应字段", lua_name, key)
						end
						warning_table[lua_name][key] = true
					end
				else
					flag = nil == warning_table[lua_name][_key]
					warning_table[lua_name][_key] = warning_table[lua_name][_key] or {}
					if flag or nil == warning_table[lua_name][_key][key] then
						local info = debug.getinfo(2, "Sln")
						-- 过滤一些基础类
						if WarningFliter(info.source) then
							print_warning("找不到对应字段", lua_name, _key, key)
						end
						warning_table[lua_name][_key][key] = true
					end
				end
			else
				t[key] = t[key] + 1
			end

			if meta_table then
				return meta_table[key]
			else
				return default_table[key]
			end
		end
	end

	mt.__pairs = function(tbl, key)
		return func, tbl, key
	end

	mt.__next = function(tbl)
		return next(default_table)
	end

	mt.__newindex = function (t, k, v)
        print_error("please do not try to modify config, Ok?", k, v)
    end

    setmetatable(cfg, mt)
end

local function _SetConfigMetatables(list_cfg, default_table, lua_name, meta_table_map)
	if nil == list_cfg or nil == default_table then
		return
	end

    local func = function (tbl, key)
        local nk, nv = real_next(default_table, key)
        if nk then
            nv = tbl[nk]
        end

        return nk, nv
    end

    local meta_map = nil
    if nil ~= meta_table_map and next(meta_table_map) then
    	meta_map = {}
    end

	local mt = {}
	mt.__index = function(tbl, key)
		-- 如果是获取的数据是由公式算的话执行公式的逻辑
		if "function" == type(default_table[key]) then
			local t_key = default_table[key .. "_params"]
			-- 不知道什么原因导致关闭的时候会有个function存在导致直接调用了报错
			if nil == t_key then
				return default_table[key]
			end

			local len = #t_key
			-- 目前最大只供4个参数作为运算，以后有需要的再加吧。
			if 1 == len then
				return default_table[key](tbl[t_key[1]])
			elseif 2 == len then
				return default_table[key](tbl[t_key[1]], tbl[t_key[2]])
			elseif 3 == len then
				return default_table[key](tbl[t_key[1]], tbl[t_key[2]], tbl[t_key[3]])
			elseif 4 == len then
				return default_table[key](tbl[t_key[1]], tbl[t_key[2]], tbl[t_key[3]], tbl[t_key[4]])
			end
		else
			if meta_map then
				local meta_table = meta_map[tbl]
				if meta_table then
					return meta_table[key]
				else
					return default_table[key]
				end
			else
				return default_table[key]
			end
		end
	end

	mt.__pairs = function(tbl, key)
		return func, tbl, key
	end

	mt.__next = function(tbl)
		return next(default_table)
	end

	mt.__newindex = function (t, k, v)
        print_error("please do not try to modify config, Ok?", k, v)
    end

	for k,v in pairs(list_cfg) do
		setmetatable(v, mt)
		if nil ~= meta_table_map then
			local meta_index = meta_table_map[k]
			if meta_index then
				meta_map[v] = list_cfg[meta_index]
			end
		end
	end
end

local function _SetConfigMetatablesDebugMode(list_cfg, default_table, lua_name, _key, meta_table_map)
	-- drop_list_auto导出规则比较特殊
	if string.find(lua_name, "drop_list_auto") then
		_SetConfigMetatables(list_cfg, default_table, lua_name, meta_table_map)
		return
	end

	if nil == list_cfg or nil == default_table then
		return
	end

	config_key_record_table[lua_name] = config_key_record_table[lua_name] or {}
	local tbl = config_key_record_table[lua_name]
	if nil ~= _key then
		config_key_record_table[lua_name][_key] = config_key_record_table[lua_name][_key] or {}
		tbl = config_key_record_table[lua_name][_key]
	end

	for k,_ in pairs(default_table) do
		tbl[k] = 0
	end

    for k,v in pairs(list_cfg) do
    	local meta_table = nil
    	if nil ~= meta_table_map then
	    	local meta_index = meta_table_map[k]
	    	if meta_index then
	    		meta_table = list_cfg[meta_index]
	    	end
	    end

    	_SetDebugMetaTable(v, default_table, lua_name, _key, meta_table)
	end
end

function SetConfigMetatables(list_cfg, default_table, lua_name, _key, meta_table_map)
	if is_develop then
		_SetConfigMetatablesDebugMode(list_cfg, default_table, lua_name, _key, meta_table_map)
	else
		_SetConfigMetatables(list_cfg, default_table, lua_name, meta_table_map)
	end
end

-- 因为tablecopy配置吃性能，但逻辑层已经很难再进行修改
-- 所以如果是拷配置的恶心代码将改调用这个进行优化,
-- 注意：不支持pairs, 不支持深层拷贝
-- 后面写的代码禁止再TableCopy, TableCopy去拷贝配置, 也不建议使用TableCopyCfg！！！
local mt_cfg_dic = {}
local mt_cfg_count = 0
function TableCopyCfg(cfg)
	local data = {}

	-- 必须是真正的配置项才允许拷贝，防止mt_cfg_dic无限增长
	-- if is_develop then
		local metable = getmetatable(cfg)
		if nil == metable or nil == rawget(metable, "__pairs")  then
			print_error("[TableCopyCfg]你正在拷贝不是真正的配置")
		end

		-- if nil ~= rawget(metable, "is_cfg_cloned") then
		-- 	print_error("[TableCopyCfg]你已经拷过该配置，不用再拷")
		-- end
	-- end

	local mt = mt_cfg_dic[cfg]
	if nil == mt then
		mt = {}
		-- 逻辑层已经在很多地方复制出配置后进行修改配置，这块代码不好改。这里将改配置的值记录到override_value_t里
		mt.is_cfg_cloned = true
		mt.override_value_t = nil
		mt_cfg_dic[cfg] = mt
	    mt_cfg_count = mt_cfg_count + 1
	    mt.key_values = nil

	    if mt_cfg_count >= 100000 then
	    	mt_cfg_count = 0
	    	print_error("[TableCopy]TableCopyCfg造成内存占用过大，请检查！")
	    end

		mt.__index = function(tbl, key)
			if nil ~= mt.override_value_t and mt.override_value_t[key] then
				return mt.override_value_t[key]
			else
				return cfg[key]
			end
		end

		mt.__pairs = function(tbl, key)
			print_error("你通过TableCopyCfg方法复制了份配置，不支持使用pairs!!!,确有需要请改用mt.__get_key_values()后进行for")
			return nil, tbl, key
		end

		mt.__next = function(tbl)
			local nv = real_next(tbl)
			if nil == nv then
				nv = next(cfg)
			end
			return nv
		end

		mt.__newindex = function (t, k, v)
			if nil ~= cfg[k] then
				if nil == mt.override_value_t then
					mt.override_value_t = {}
				end
				mt.override_value_t[k] = v
			else
				rawset(t, k, v)
			end
		end

		mt.__get_key_values = function ()
			if nil == mt.key_values then
				mt.key_values = {}
				for k,v in pairs(cfg) do
					mt.key_values[k] = v
				end
			end

			if nil ~= mt.override_value_t then
				for k,v in pairs(mt.override_value_t) do
					mt.key_values[k]= v
				end
			end

			return mt.key_values
		end

    end

	setmetatable(data, mt)
	return data
end

function ConfigManager.WriteConfigRecord()
	local tbl = {}

	for path, config in pairs(config_key_record_table) do
		table.insert(tbl, path)
		table.insert(tbl, "\n")
		for k,v in pairs(config) do
			if type(v) == "number" then
				table.insert(tbl, string.format("\tkey: %s count: %s\n", k, v))
			end
		end

		for k,v in pairs(config) do
			if type(v) == "table" then
				table.insert(tbl, string.format("\tkey: %s\n", k))
				for k2, v2 in pairs(v) do
					table.insert(tbl, string.format("\t\tkey: %s count: %s\n", k2, v2))
				end
			end
		end
		table.insert(tbl, "\n")
	end

	local file_path, error_info = ResUtil.GetCachePath("config_key_record.txt")
	local file = io.open(file_path, "w")
	local content = table.concat(tbl)
	file:write(content)
	file:close()
end