BootyBayRewardTips = BootyBayRewardTips or BaseClass(SafeBaseView)

function BootyBayRewardTips:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/bootybay_ui_prefab", "bootybay_reward_tips")
	self:SetMaskBg(true)
	self.time = 5
	self.data = {}
	self.desc = ""
	self.ok_call_back = nil

	self.reward_list = {}
end

function BootyBayRewardTips:__delete()

end

function BootyBayRewardTips:LoadCallBack()
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    
	self.node_list.title_view_name.text.text = Language.BootyBay.TanBoaReward
	self.node_list.btn_ok.button:AddClickListener(BindTool.Bind(self.OnClickOKBtn,self))
	--self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtn,self))
end

-- 切换标签调用
function BootyBayRewardTips:ShowIndexCallBack()
	self:Flush()
end

function BootyBayRewardTips:SetData(data)
	self.data = data
end

function BootyBayRewardTips:SetDesc(desc)
	self.desc = desc
end

function BootyBayRewardTips:SetOKCallBack(ok_call_back)
	self.ok_call_back = ok_call_back
end

function BootyBayRewardTips:ReleaseCallBack()
	self.data = nil
	self.desc = nil
	self.ok_call_back = nil
	if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end

    if self.reward_list then
    	for k,v in pairs(self.reward_list) do
    		v:DeleteMe()
    	end

    	self.reward_list = {}
    end
end

function BootyBayRewardTips:OnFlush()
	if self.desc ~= nil then
		self.node_list.desc.text.text = self.desc
	end

	if not self.data or not next(self.data) then return end

	local index = 1
	for k,v in pairs(self.data) do
		if self.reward_list[index] == nil then
			self.reward_list[index] = ItemCell.New(self.node_list.reward_list)
		end

		self.reward_list[index]:SetActive(true)
		self.reward_list[index]:SetData(v)
		index = index + 1
	end

	for i = index, #self.reward_list do
		if self.reward_list[index] then
			self.reward_list[index]:SetActive(false)
		end
	end

	if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end
    local remain_time = 3
	self:ChangeTime(0, remain_time)
	self.quest = CountDown.Instance:AddCountDown(remain_time, 1, BindTool.Bind(self.ChangeTime, self),BindTool.Bind(self.CompleteTime, self))
end

function BootyBayRewardTips:ChangeTime(elapse_time, total_time)

end

function BootyBayRewardTips:CompleteTime()
    -- self:Close()
end

-- 关闭前调用
function BootyBayRewardTips:CloseCallBack()
	-- override
end

function BootyBayRewardTips:OnClickOKBtn()
	if self.ok_call_back then
		self.ok_call_back()
	end
	self:Close()
end

function BootyBayRewardTips:OnClickCloseBtn()
	self:Close()
end