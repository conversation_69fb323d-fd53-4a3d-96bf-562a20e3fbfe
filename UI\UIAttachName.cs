﻿using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class UIAttachName : MonoBehaviour {
    public Transform target;
    public Transform parent;
    private TMP_Text m_Text;

    private void Awake()
    {
        m_Text = GetComponent<TMP_Text>();
    }
    private void OnEnable()
    {
        UpdateTransform();
    }

    void LateUpdate ()
    {
        UpdateTransform();
    }

    void UpdateTransform()
    {
        if (null != target)
        {
            if (!target.gameObject.activeInHierarchy)
            {
                //设置scale可能会导致TMP变成色块，先改为直接控制组件
                //transform.localScale = Vector3.zero;
                m_Text.enabled = false;
                return;
            }

            transform.localPosition = transform.parent.InverseTransformPoint(target.position);
        }

        if (null != parent && parent.gameObject.activeInHierarchy)
        {
            if (parent.localScale.x <0.1f)
            {
                m_Text.enabled = false;
            }
            else
            {
                m_Text.enabled = true;
                transform.localScale = parent.localScale;
            }
            
        }
    }
}
