TipsGetNewSkillView = TipsGetNewSkillView or BaseClass(SafeBaseView)

function TipsGetNewSkillView:__init()
	self:SetMaskBg(true, true, false, BindTool.Bind(self.BlockClick, self))
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "GetNewSkillTips")
	self.view_name = "GetNewSkillTips"
	self.delay_time = 1.5
	self.fade_speed = 1.5
	self.move_speed = 90
	self.play_audio = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.is_auto_task = false
	self.view_cache_time = 0
	self.can_do_fade = false
end

function TipsGetNewSkillView:LoadCallBack()
	self:SetSkillButtonposRoot()
	XUI.AddClickEventListener(self.node_list["ok_btn"], BindTool.Bind(self.ClickOkBtn,self))
end

-- 设置位置
function TipsGetNewSkillView:SetSkillButtonposRoot()
    MainuiWGCtrl.Instance:AddInitCallBack(nil,function ()
		self.skill_button_pos_list = MainuiWGCtrl.Instance:GetSkillButtonPosition()
		self.beast_skill_button_pos_list = MainuiWGCtrl.Instance:GetBeastSkillButtonPosition()
	end)
end

function TipsGetNewSkillView:ReleaseCallBack()
	-- 清理变量和对象
	self.target_icon = nil
	self.from_mark  = nil
	if SkillWGData.Instance then
		SkillWGData.Instance:SetSkillisFunctionOpen(false)
	end

	if self.sequence_tween then
		self.sequence_tween:Kill()
		self.sequence_tween = nil
	end

	self.ok_fun_callback = nil
end

function TipsGetNewSkillView:ShowViewByCfg(skill_cfg)
	self.skill_cfg = skill_cfg
	self:Open()
end

function TipsGetNewSkillView:ShowView(skill_id,from_mark)

	self.id_value = skill_id
	self.from_mark = from_mark
	local skill_cfg
	if from_mark and from_mark == XINMO_FUBEN_SKILL_POS.XINMO_SKILL then
		skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(self.id_value)
	elseif from_mark and from_mark == XINMO_FUBEN_SKILL_POS.BEAST_SKILL then
		skill_cfg = SkillWGData.Instance:GetBeastsSkillById(self.id_value)
	else
		skill_cfg = SkillWGData.GetSkillFunOpenCfg(self.id_value)
	end

	if skill_cfg == nil then
		return
	end
	self.index = skill_cfg.pos_index or 0
	if self.index >= 5 and nil == from_mark then
		return
	end

	self:Open()
end

function TipsGetNewSkillView:BlockClick()
	if self.fly_flag == false then
		self.fly_flag = true
		if self.timer and self.timer > 0 then
			if self.timer_hide_quest then
			   GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
			   self.timer_hide_quest = nil
			end

			self:DoMoveToTargetTween()
		end
	end
end

function TipsGetNewSkillView:ShowIndexCallBack(index)
	local view_manager = ViewManager.Instance
	if view_manager:IsOpen(GuideModuleName.TaskDialog) then
		view_manager:Close(GuideModuleName.TaskDialog)
	end

	self.fly_flag = false
	if self.mask_bg then
		self.hide_mask = false
		self.mask_bg:SetActive(true)
	end

	self.is_auto_task = TaskGuide.Instance.can_auto_all_task
	-- TaskGuide.Instance:CanAutoAllTask(false)		--停止接受任务
	-- ViewManager.Instance:CloseAll()				--关闭所有界面

	local skill_cfg
	if self.skill_cfg then
		skill_cfg = self.skill_cfg
	else
		if self.from_mark then
			if self.from_mark == XINMO_FUBEN_SKILL_POS.BEAST_SKILL then
				skill_cfg = SkillWGData.Instance:GetBeastsSkillById(self.id_value)
			else
				skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(self.id_value)
			end
		else
			skill_cfg = SkillWGData.GetSkillFunOpenCfg(self.id_value)
		end
	end

	if not skill_cfg then
		self:Close()
		return
	end

	UITween.DoViewDuangOpenTween(self.node_list.Frame, self.node_list["SkillIcon"])

	local res_fun = skill_cfg.res_fun or ResPath.GetSkillIconById
	local need_hide_bg = true
	if self.from_mark == XINMO_FUBEN_SKILL_POS.XINMO_SKILL or self.skill_cfg then
		local title_str = Language.Skill.ActSkillTitle1
		self.node_list["SkillName"].text.text = skill_cfg.name
		self.node_list["SkillDesc"].text.text = skill_cfg.desc
		XUI.SetSkillIcon(self.node_list.icon_bg, self.node_list["SkillIcon"], skill_cfg.icon, skill_cfg.res_fun)

		if skill_cfg.img_get_newskill then
			title_str = skill_cfg.img_get_newskill
		end

		self.node_list["img_get_newskill"].text.text = title_str

		if skill_cfg.ok_fun_callback then
			self.ok_fun_callback = skill_cfg.ok_fun_callback
			self.node_list["ok_btn"]:SetActive(true)
			need_hide_bg = false
		end

		if skill_cfg.ok_btn_text then
			self.node_list["ok_btn_text"].text.text = skill_cfg.ok_btn_text
		end
	else
		self.node_list["SkillName"].text.text = skill_cfg.skill_name
		self.node_list["SkillIcon"].image:LoadSprite(res_fun(SkillWGData.Instance:GetSkillIconId(skill_cfg.skill_id)))
		local description = skill_cfg.description

		if self.from_mark == XINMO_FUBEN_SKILL_POS.BEAST_SKILL then
			--去技能数据类查
			local client_cfg = SkillWGData.Instance:GetSkillClientConfig(self.id_value)
			if client_cfg then
				local description = client_cfg.description
			end
		end
		
		self.node_list["SkillDesc"].text.text = description
	end

    local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["SkillIcon"].transform, nil)
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.XinJiNengKaiQi, nil, true))
	self.node_list["SkillIcon"]:SetActive(true)

	if need_hide_bg then
		self:CalTimeToHideBg()
	end

	if self.skill_cfg or (nil == self.skill_button_pos_list and skill_cfg.pos_index == 0 and nil == self.from_mark) then
		return
	end

	local target
	if self.from_mark == XINMO_FUBEN_SKILL_POS.XINMO_SKILL then
		target = MainuiWGCtrl.Instance:GetSkillBottomButton()
	elseif self.from_mark == XINMO_FUBEN_SKILL_POS.BEAST_SKILL then
		target = self.beast_skill_button_pos_list and self.beast_skill_button_pos_list[1]
	else
		target = self.skill_button_pos_list and self.skill_button_pos_list[skill_cfg.pos_index]
	end

	if target then
		if self.from_mark == XINMO_FUBEN_SKILL_POS.XINMO_SKILL then
			self.target_icon = target.transform:FindHard("Icon")
			MainuiWGCtrl.Instance:SetMenuIconIsOn(true)
		else
			self.target_icon = target.transform:FindHard("icon")
			self.target_icon.gameObject:SetActive(false)
			MainuiWGCtrl.Instance:SetMenuIconIsOn(false)
		end
	end
end

function TipsGetNewSkillView:CloseCallBack()
	if self.skill_cfg and self.skill_cfg.call_back then
		self.skill_cfg.call_back()
	end

	self.skill_cfg = nil
	self.ok_fun_callback = nil
	if self:IsLoaded() and self.node_list["ok_btn"] then
		self.node_list["ok_btn"]:SetActive(false)
	end
	-- if self.is_auto_task then
	-- 	if Scene.Instance:GetSceneType() == SceneType.Common then
	-- 		GuajiWGCtrl.Instance:StopGuaji()
	-- 	end

	-- 	TaskGuide.Instance:CanAutoAllTask(true)		--继续自动做任务
	-- end
	self.fly_flag = false
	if self.timer_hide_quest then
		GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
		self.timer_hide_quest = nil
	end
	SkillWGData.Instance:SetSkillisFunctionOpen(false)
	GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_LIST)
end

function TipsGetNewSkillView:MoveToTarget()
	if self.mask_bg then
		self.mask_bg:SetActive(false)
		self.hide_mask = true
	end

	local timer = 0.7
	if nil == self.from_mark then
		if nil == self.skill_button_pos_list and nil == self.skill_button_pos_list[self.index] or self.index == 0 then
			if self.skill_cfg and self.skill_cfg.need_move then
			else
				self:Close()
				return
			end
		end
	end

	if self.skill_cfg and not self.skill_cfg.need_move then
		self:Close()
		return
	end

	local target
	if self.from_mark == XINMO_FUBEN_SKILL_POS.XINMO_SKILL then
		target = MainuiWGCtrl.Instance:GetSkillBottomButton()
	elseif self.skill_cfg and self.skill_cfg.need_move then
		target = self.skill_cfg.target
	elseif self.from_mark == XINMO_FUBEN_SKILL_POS.BEAST_SKILL then
		target = self.beast_skill_button_pos_list and self.beast_skill_button_pos_list[1]
	else
		target = self.skill_button_pos_list and self.skill_button_pos_list[self.index]
	end

	if target == nil then
		self:Close()
		return
	end

	self.time_quest = GlobalTimerQuest:AddDelayTimer(function()
		local item = self.node_list["SkillIcon"]
		if not item then
			return
		end

		local path = {}
		self.target_pos = target.transform.position
		table.insert(path, self.target_pos)

		if self.sequence_tween then
			self.sequence_tween:Kill()
			self.sequence_tween = nil
		end

		local tween = DG.Tweening.DOTween.Sequence()
		local tween_move = item.transform:DOPath(
			path,
			timer,
			DG.Tweening.PathType.Linear,
			DG.Tweening.PathMode.TopDown2D,
			0.7,
			nil):SetEase(DG.Tweening.Ease.Linear)

		local tween_scale = item.transform:DOScale(0.8, 0.7)
		tween:Append(tween_move)
		tween:Join(tween_scale)
		tween:SetLoops(0)
		tween:OnComplete(function()
			self:CloseView()
		end)

		self.sequence_tween = tween
	end, 0)
end

function TipsGetNewSkillView:CloseView()
	self:Close()
	self.node_list["SkillIcon"]:SetActive(false)
	if self.target_icon then
		self.target_icon.gameObject:SetActive(true)
	end
	GlobalTimerQuest:CancelQuest(self.time_quest)
end

function TipsGetNewSkillView:CalTimeToHideBg()
	if self.timer_hide_quest then
	   GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
	   self.timer_hide_quest = nil
	end
	self.timer = 2
	SkillWGData.Instance:SetSkillisFunctionOpen(true)

	self.timer_hide_quest = GlobalTimerQuest:AddRunQuest(function()
		if not self:IsLoaded() then
			return
		end

		self.timer = self.timer - UnityEngine.Time.deltaTime

		if self.timer <= 0 then
			GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
			self.timer_hide_quest = nil

			local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
			if self.from_mark then
				self:DoMoveToTargetTween()
			elseif self.skill_cfg and self.skill_cfg.need_move then
				self:DoMoveToTargetTween()
			else
				if not menu_ison then
					self:DoMoveToTargetTween()
				else
					self:CloseView()
				end
			end
		end
	end, 0)
end

function TipsGetNewSkillView:DoMoveToTargetTween()
	if not self:IsLoaded() then
		return
	end
	UITween.DoViewDuangCloseTween(self.node_list.Frame, nil, function()
		self:MoveToTarget()
	end)
end

function TipsGetNewSkillView:ClickOkBtn()
	if self.ok_fun_callback then
		self.ok_fun_callback()
	end
	self.ok_fun_callback = nil
	self:Close()
end
