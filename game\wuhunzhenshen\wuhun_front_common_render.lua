---------------------------------------宝石项----------------------------------------------
WuH<PERSON>BaoShiRender = WuHunBaoShiRender or BaseClass(BaseRender)

function WuHunBaoShiRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.cell_item, BindTool.Bind(self.OnClickSelf, self))
end

function WuHunBaoShiRender:__delete()
	self:KillTween()
end

function WuHunBaoShiRender:OnClickSelf(is_on)
	if self.click_callback then
		self.click_callback(self, is_on)
	end
end

function WuHunBaoShiRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

--数据结构{empty_type 为true表示未镶嵌 show_type能装的宝石类型, level 等级}
function WuHunBaoShiRender:SetData(data)
    self.data = data

    self:SetFrontGemItemShow()
end

function WuHunBaoShiRender:OnFlush(param)
    if self.data then
        self:SetFrontGemItemShow()
    end
end

function WuHunBaoShiRender:SetFrontGemItemShow()
    if not self.data.empty_type then --已镶嵌
        self.node_list.text_gem_prop1.text.text = self.data.prop_txt1
        self.node_list.text_gem_prop2.text.text = self.data.prop_txt2

        local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id) or {}
        self.node_list.image_gem_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    local gem_star = self.data.gem_star or COMMON_GAME_ENUM.FUYI
    self.node_list.image_bg.image:LoadSprite(ResPath.GetWuHunZhenShenImage("a2_wh_gem_bg_di" .. self.data.show_type))
    self.node_list.image_typebg.image:LoadSprite(ResPath.GetWuHunZhenShenImage("a2_wh_gem_small_di" .. self.data.show_type))
    self.node_list.text_gem_type.text.text = Language.WuHunZhenShen.WuhunFrontGemNameType[self.data.show_type]
    self.node_list.text_level.text.text = self.data.level or 0
    self.node_list.node_empty:CustomSetActive(self.data.empty_type)
    self.node_list.node_equip:CustomSetActive(not self.data.empty_type)
    self.node_list.image_propbg:CustomSetActive(not self.data.hide_prop)
    self.node_list.text_canadd:CustomSetActive(not self.data.hide_prop)
    self.node_list.text_gem_prop1:CustomSetActive(not self.data.hide_prop)
    self.node_list.text_gem_prop2:CustomSetActive(not self.data.hide_prop)
    self.node_list.image_red:CustomSetActive(self.data.show_red)
    local empty_txt = gem_star == COMMON_GAME_ENUM.FUYI and Language.WuHunZhenShen.WuhunFrontBaoshiTxt4 or Language.WuHunZhenShen.WuhunFrontBaoshiTxt5
    self.node_list.text_canadd.text.text = empty_txt

	if self.data.show_red then
        self:KillTween()
		self.arrow_tweener = SetUIGreenImageCommonJump(self.node_list.image_red)
	end
end

function WuHunBaoShiRender:KillTween()
    if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

---------------------------------------宝石项----------------------------------------------



--魂阵列表项
HunZhenRender = HunZhenRender or BaseClass(BaseRender)
function HunZhenRender:OnFlush()
	self.node_list["image_red"]:SetActive(self.data.show_red)

    self.node_list["image_icon"].image:LoadSprite(ResPath.GetWuHunZhenShenImage(self.data.front_icon))
end

function HunZhenRender:OnSelectChange(is_select)
    self.node_list["image_select_bg"]:SetActive(is_select)
end





--魂石列表项
WuHunFrontGemItem = WuHunFrontGemItem or BaseClass(BaseRender)
function WuHunFrontGemItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.cell_item, BindTool.Bind(self.OnClickSelf, self))
end

function WuHunFrontGemItem:__delete()
    self.click_callback = nil
end

function WuHunFrontGemItem:OnClickSelf(is_on)
	if self.click_callback then
		self.click_callback(self, is_on)
	end
end

function WuHunFrontGemItem:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function WuHunFrontGemItem:OnFlush(param)
    if param then
        for k_1, v_1 in pairs(param) do
            if k_1 == "set_data" then
                self.data = v_1
            end
        end
    end

    if self.data then
        self:SetFrontGemItemShow()
    end
end

function WuHunFrontGemItem:SetFrontGemItemShow()
    local light_name = self.data.select_gem_index == self.data.gem_index and "a2_wh_bg_g1" or "a2_wh_bg_g2"
    self.node_list.image_bg.image:LoadSprite(ResPath.GetWuHunZhenShenImage(light_name))

    local gem_level = self.data.gem_level or 0
    local gem_star = self.data.gem_star or 0
    self.node_list.text_level.text.text = gem_level
    self.node_list.image_icon.image:LoadSprite(ResPath.GetWuHunZhenShenImage(self.data.gem_icon))

    self.node_list.node_stars:CustomSetActive(self.data.show_star)
    self.node_list.node_keyin:CustomSetActive(self.data.show_keyin)
    self.node_list.node_diezhen:CustomSetActive(self.data.show_diezhen)
    self.node_list.image_red:CustomSetActive(self.data.show_red)
    XUI.SetGraphicGrey(self.node_list.image_icon, not self.data.gem_star or self.data.gem_star == COMMON_GAME_ENUM.FUYI)

    if self.data.show_star then
        local star_res_list = GetStarImgResByStar(gem_star)
        for i = 1, 5 do
            local image_star = self.node_list["image_star" .. i]
            image_star.image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
        end
        self.node_list.node_star_parent:CustomSetActive(self.data.gem_star and self.data.gem_star > COMMON_GAME_ENUM.FUYI)

    elseif self.data.show_diezhen then
        local name = Language.WuHunZhenShen.WuhunFrontTxt4
        if self.data.gem_star and self.data.gem_star > COMMON_GAME_ENUM.FUYI then
            local cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(self.data.wuhun_id, self.data.front_index, self.data.gem_index, self.data.gem_grade)
            name = cfg.diezhen_des
        end
        self.node_list.text_grade.text.text = name

    elseif self.data.show_keyin then
        local engrave_list = WuHunFrontWGData.Instance:GetGemAllEngraveData(self.data.wuhun_id, self.data.front_index, self.data.gem_index)
        for i = 1, 6 do
            self.node_list["image_baoshi" .. i]:CustomSetActive(engrave_list[i] and engrave_list[i] ~= 0)
        end

    end
end