-- 五气朝元（龙神试炼）
DragonTrialTaskView = DragonTrialTaskView or BaseClass(SafeBaseView)

function DragonTrialTaskView:__init()
	self:AddViewResource(0, "uis/view/dragon_trial_ui_prefab", "layout_dragon_trial_task")
    self:AddViewResource(0, "uis/view/dragon_trial_ui_prefab", "layout_dragon_trial_main_view")
	self.view_cache_time = 0
    self.active_close = false
end

function DragonTrialTaskView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	self.is_out_fb = nil

	if self.reward_item_list then
        self.reward_item_list:DeleteMe()
        self.reward_item_list = nil
    end

    if self.show_top_event then
    	GlobalEventSystem:UnBind(self.show_top_event)
    	self.show_top_event = nil
    end

	if self.main_menu_icon_change then
    	GlobalEventSystem:UnBind(self.main_menu_icon_change)
    	self.main_menu_icon_change = nil
    end

    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.DragonTrialTaskView, self.get_guide_ui_event)

    self:RemoveEffectDelayTimer()
end

function DragonTrialTaskView:LoadCallBack()
    if not self.reward_item_list then
	    self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
    end

    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
    XUI.AddClickEventListener(self.node_list["yulong_btn"],BindTool.Bind(self.OnClickYuLong, self))			--御龙

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.DragonTrialTaskView, self.get_guide_ui_event)
end

function DragonTrialTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["layout_dragon_trial"] then
		self.obj = self.node_list["layout_dragon_trial"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0,0,0)
		self.obj.transform.localScale = Vector3.one
	end

	if self.is_out_fb then
        self.obj:SetActive(false)
    end
    self.is_out_fb = nil
end

function DragonTrialTaskView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
    local fun = function()
        self:OnClickYuLong()
        self.node_list["yulong_btn"]:CustomSetActive(false)
    end

    return self.node_list[ui_name], fun
end

function DragonTrialTaskView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function DragonTrialTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function DragonTrialTaskView:OnFlush()
	local scene_info = DragonTrialWGData.Instance:GetDragonTrialSceneInfo()

	if not scene_info then
		return
	end

	local level_cfg = DragonTrialWGData.Instance:GetLevelMonsterCfg(scene_info.seq)

	if self.reward_item_list and level_cfg then
        local real_reward_item = {}
        if level_cfg.fixed_level_reward ~= 0 then
            local fix_level_data = {}
            DragonTrialWGData.Instance:SetFixLevelReward(fix_level_data, level_cfg)
            table.insert(real_reward_item, fix_level_data)
        end

        for _, reward_data in pairs(level_cfg.pass_reward_item) do
            if reward_data and reward_data.item_id then
                table.insert(real_reward_item, reward_data)
            end
        end

		self.reward_item_list:SetDataList(real_reward_item)
	end

    -- 刷新怪物名称
    --[[
    local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto")
	local boss_id = level_cfg and level_cfg.boss_id

    if monster_cfg and monster_cfg.monster_list and monster_cfg.monster_list[boss_id] then
        local boss_config = monster_cfg.monster_list[boss_id]
		self.node_list.lbl_mubiao.text.text = string.format(Language.FuBen.XiuZhenLuTaskTitle, scene_info.tower_seq, ToColorStr(boss_config.name, COLOR3B.D_PURPLE))
    end
    ]]

    -- 当前关卡
    self.node_list.lbl_mubiao.text.text = level_cfg.level_des

	-- 刷新推荐战力
	local zhanli_tuijian = level_cfg and level_cfg.need_cap or 0
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
    local is_green = role_zhanli >= zhanli_tuijian	
	self.node_list.tuijian_zhanli.text.text = string.format(Language.FuBen.TuiJianZhanLi, ToColorStr(zhanli_tuijian, is_green and COLOR3B.D_GREEN or COLOR3B.D_RED))

    local scene_info = DragonTrialWGData.Instance:GetDragonTrialSceneInfo()
    self.node_list["yulong_btn"]:CustomSetActive(false)
    if DragonTrialWGData.Instance:GetCurrentPassSeq() <= 0 then
        self.node_list["yulong_btn"]:CustomSetActive(true)
    elseif scene_info.is_end ~= 1 then
        DragonTrialWGCtrl.Instance:SceneEnterCallback()
    end
end

--移除回调
function DragonTrialTaskView:RemoveEffectDelayTimer()
    if self.show_effect_timer then
        GlobalTimerQuest:CancelQuest(self.show_effect_timer)
        self.show_effect_timer = nil
    end
end

-- 点击御龙
function DragonTrialTaskView:OnClickYuLong()
    DragonTrialWGCtrl.Instance:SceneEnterCallback()
    if self.node_list["yulong_btn"] then
        self.node_list["yulong_btn"]:CustomSetActive(false)
    end
    -- 换成元神
    CultivationWGCtrl.Instance:AngerBecome()
end
