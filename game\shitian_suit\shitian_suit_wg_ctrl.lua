require("game/shitian_suit/shitian_suit_wg_data")
require("game/shitian_suit/shitian_suit_view")
require("game/shitian_suit/shitian_suit_tips")

ShiTianSuitWGCtrl = ShiTianSuitWGCtrl or BaseClass(BaseWGCtrl)
function ShiTianSuitWGCtrl:__init()
    if ShiTianSuitWGCtrl.Instance ~= nil then
		print_error("[ShiTianSuitWGCtrl] attempt to create singleton twice!")
		return
	end

    ShiTianSuitWGCtrl.Instance = self
    self.data = ShiTianSuitWGData.New()
    self.view = ShiTianSuitView.New(GuideModuleName.ShiTianSuitView)
    self.suit_tips_view = ShiTianSuitTipsView.New()
    self:RegisterAllProtocals()

    self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
end

function ShiTianSuitWGCtrl:__delete()
    ShiTianSuitWGCtrl.Instance = nil

    if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.suit_tips_view then
        self.suit_tips_view:DeleteMe()
        self.suit_tips_view = nil
    end
end

function ShiTianSuitWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(CSShiTianSuitOperate)
    self:RegisterProtocol(SCShiTianSuitInfo, "OnSCShiTianSuitInfo")
	self:RegisterProtocol(SCShiTianSuitUpdate, "OnSCShiTianSuitUpdate")
end

-- 通用请求请求操作
function ShiTianSuitWGCtrl:SendShiTianRequest(opera_type, param_1, param_2, param_3)
	--print_error("发送请求",opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShiTianSuitOperate)
	protocol.operate_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

--弑天装备信息
function ShiTianSuitWGCtrl:OnSCShiTianSuitInfo(protocol)
    self.data:SetSuitAllInfo(protocol)
    RemindManager.Instance:Fire(RemindName.ShiTianSuitActive)
end

--装备更新
function ShiTianSuitWGCtrl:OnSCShiTianSuitUpdate(protocol)
    self.data:UpdateSuitInfo(protocol)
    if self.view:IsOpen() then
		self.view:Flush()
	end

    RemindManager.Instance:Fire(RemindName.ShiTianSuitActive)
    RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
end

function ShiTianSuitWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local is_add = change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
					(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num)
	if not is_add then
		return
	end

    if self.data:IsUpStarStuffID(change_item_id) or self.data:IsShiTianChangeItem(change_item_id) then
        if self.view:IsOpen() then
            self.view:Flush()
        end

        RemindManager.Instance:Fire(RemindName.ShiTianSuitActive)
    end
end

function ShiTianSuitWGCtrl:OpenSuitTips(suit_seq)
    self.suit_tips_view:SetDataAndOpen(suit_seq)
end

function ShiTianSuitWGCtrl:OnAllStrengthenDataChange()
    if self.view:IsOpen() then
        self.view:FlushStrengthenRemind(true)
    end
end