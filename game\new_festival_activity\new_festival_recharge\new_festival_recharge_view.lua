local TextColor = {
    EnoughColor = "#355443",
    NotEnoughColor = "#E32828",
}
local recharege_time = "nfs_recharege_timer" --累充活动倒计时 类型

--给主活动UI提供刷新活动累充界面
function NewFestivalActivityView:LoadIndexCallBackRecharge()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_HDLC, NA_RECHARGE_SUM_OPERATE_TYPE.INFO)

    --加载奖励物品列表
    if not self.recharge_reward_list then
        self.recharge_reward_list = AsyncListView.New(NFRechargeItemRender, self.node_list.recharge_list)
    end

    --加载模型和动画
    if not self.recharge_show_model then
        self.recharge_show_model = OperationActRender.New(self.node_list.recharge_model_pos)
        self.recharge_show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    self:InitRechargeItemImageAndText()
    self:CreateRechargeCountDown()
    self:InitRechargeModel()
end

--给主活动面板提供移除界面信息
function NewFestivalActivityView:ReleaseRechargeActive()
    --移除计时器
    if CountDownManager.Instance:HasCountDown(recharege_time) then
        CountDownManager.Instance:RemoveCountDown(recharege_time)
    end

    --移除模型
    if self.recharge_show_model then
        self.recharge_show_model:DeleteMe()
        self.recharge_show_model = nil
    end

    --移除奖励列表
    if self.recharge_reward_list then
        self.recharge_reward_list:DeleteMe()
        self.recharge_reward_list = nil
    end
end

function NewFestivalActivityView:InitRechargeModel()
    local model_data = NewFestivalRechargeWGData.Instance:GetModelData()
    if IsEmptyTable(model_data) then
        return
    end

    -- local transform_info = model_data.transform_info
    -- RectTransform.SetAnchoredPositionXY(self.node_list.recharge_model_pos.rect, transform_info.pos_x,
    --     transform_info.pos_y, transform_info.pos_z)
    -- self.node_list.recharge_model_pos.rect.rotation = Quaternion.Euler(transform_info.rot_x, transform_info.rot_y,
    --     transform_info.rot_z)
    -- Transform.SetLocalScaleXYZ(self.node_list.recharge_model_pos.transform, transform_info.scale,
    --     transform_info.scale,
    --     transform_info.scale)

    self.recharge_show_model:SetData(model_data.display_data)
end

--初始化累充界面UI文字和图片
function NewFestivalActivityView:InitRechargeItemImageAndText()
    --ui标题  踏青累充
    local title_bundle, title_asset = ResPath.GetNewFestivalRawImages("tqlc_title")
    self.node_list.recharge_title.raw_image:LoadSprite(title_bundle, title_asset, function()
        self.node_list.recharge_title.raw_image:SetNativeSize()
    end)

    --奖励列表背景
    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("tqjrlc_bj")
    self.node_list.recharge_bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.recharge_bg.raw_image:SetNativeSize()
    end)

    --累充活动配置表
    self.recharge_config = NewFestivalActivityWGData.Instance:GetRechargeOtherCfg()
end

--创建倒计时
function NewFestivalActivityView:CreateRechargeCountDown()
    if CountDownManager.Instance:HasCountDown(recharege_time) then
        return
    end

    local time, total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_JRHD_HDLC)
    if time > 0 then
        self:RechargeUpdateCountDown(total_time - time, total_time)
        CountDownManager.Instance:AddCountDown(recharege_time,
            BindTool.Bind1(self.RechargeUpdateCountDown, self), BindTool.Bind1(self.RecharegeCompleteCallBack, self),
            nil, time, 1)
    else
        self:RecharegeCompleteCallBack()
    end
end

--减少倒计时
function NewFestivalActivityView:RechargeUpdateCountDown(elapse_time, total_time)
    if self.node_list and self.node_list.recharge_time then
        self.node_list.recharge_time.text.text = string.format(Language.NewFestivalActivityRecharge.ActEndTime,
            self.recharge_config.timer_color, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
    end
end

function NewFestivalActivityView:RecharegeCompleteCallBack()
    if self.node_list and self.node_list.recharge_time then
        self.node_list.recharge_time.text.text = Language.Common.ActivityIsEnd
    end
end

--刷新累充界面
function NewFestivalActivityView:OnFlushRecharge()
    self:FlushRechargeRewardList()
end

--刷新累充奖励层数列表
function NewFestivalActivityView:FlushRechargeRewardList()
    local reward_cfg = NewFestivalRechargeWGData.Instance:GetRecharegeRewardCfg()
    if not reward_cfg then
        return
    end
    self.recharge_reward_list:SetDataList(reward_cfg)
end

----------------NFACollectItemRender节日收集奖励列表里的item
NFRechargeItemRender = NFRechargeItemRender or BaseClass(BaseRender)
function NFRechargeItemRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    self:SetImageAndText()
    XUI.AddClickEventListener(self.node_list.rc_get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function NFRechargeItemRender:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function NFRechargeItemRender:OnFlush()
    if not self.data then
        return
    end

    self.reward_list:SetDataList(self.data.reward_item_list)
    self:ResetRechargeTextAndBtn()
end

--给领取按钮添加事件
function NFRechargeItemRender:OnClickGetBtn()
    if not self.data then
        return
    end

    local item_num = NewFestivalRechargeWGData.Instance:GetRechargeNum()
    if item_num < self.data.condition then
        TipWGCtrl.Instance:ShowSystemMsg(Language.NewFestivalActivity_Recharge.ItemNotEnough)
        return
    end
    
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_HDLC, NA_RECHARGE_SUM_OPERATE_TYPE.FETCH, self.data.seq)
end

--设置奖励item的UI
function NFRechargeItemRender:SetImageAndText()
    -- 背景
    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("tqjrlc_zdt")
    self.node_list.bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.bg.raw_image:SetNativeSize()
    end)

    --累充多少
    local top_bundle, top_asset = ResPath.GetNewFestivalActImages("a3_jrhd_tqjrlc_zdt2")
    self.node_list.top.image:LoadSprite(top_bundle, top_asset)

    --达到
    self.node_list.text.text.color = Str2C3b(TextColor.EnoughColor)
end

function NFRechargeItemRender:ResetRechargeTextAndBtn()
    if not self.data then
        return
    end
    --文字颜色判断
    local cur_num = NewFestivalRechargeWGData.Instance:GetRechargeNum()
    local rechargeTarget = self.data.condition
    local color = cur_num >= rechargeTarget and TextColor.EnoughColor or TextColor.NotEnoughColor
    self.node_list.num.text.text = string.format(Language.NewFestivalActivityRecharge.NeedNum, color, cur_num,TextColor. EnoughColor, rechargeTarget)

    local get_flag = NewFestivalRechargeWGData.Instance:GetRechargeState(self.data.seq) --是否领取了  0是未领取
    self.node_list.rc_btn_text.text.text = Language.NewFestivalActivityRecharge.GetBtnStr
    self.node_list.rc_isget_text.text.text = Language.Common.YiLingQu

    --刷新红点
    self.node_list.rc_remind:CustomSetActive(not get_flag and cur_num >= rechargeTarget)
    
    --显示隐藏领取按钮
    self.node_list.rc_get_btn:SetActive(not get_flag and cur_num >= rechargeTarget)
    self.node_list.rc_isget_bg:SetActive(get_flag)
    self.node_list.wdc_flag:SetActive(not get_flag)

    --按钮置灰
    -- XUI.SetButtonEnabled(self.node_list.rc_get_btn, cur_num >= rechargeTarget)
end
