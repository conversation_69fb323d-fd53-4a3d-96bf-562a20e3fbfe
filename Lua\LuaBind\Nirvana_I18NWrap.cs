﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_I18NWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("I18N");
		<PERSON><PERSON>Function("ListenLanguageChanged", ListenLanguageChanged);
		<PERSON><PERSON>Function("UnlistenLanguageChanged", UnlistenLanguageChanged);
		<PERSON><PERSON>unction("LoadMO", LoadMO);
		<PERSON><PERSON>unction("GetString", GetString);
		L.RegFunction("GetPluralString", GetPluralString);
		L.RegFunction("GetParticularString", GetParticularString);
		<PERSON><PERSON>RegFunction("GetParticularPluralString", GetParticularPluralString);
		<PERSON><PERSON>("Catalog", get_Catalog, set_Catalog);
		<PERSON><PERSON>("Language", get_Language, set_Language);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenLanguageChanged(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 1);
			System.Collections.Generic.LinkedListNode<System.Action> o = Nirvana.I18N.ListenLanguageChanged(arg0);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnlistenLanguageChanged(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Collections.Generic.LinkedListNode<System.Action> arg0 = (System.Collections.Generic.LinkedListNode<System.Action>)ToLua.CheckObject(L, 1, typeof(System.Collections.Generic.LinkedListNode<System.Action>));
			Nirvana.I18N.UnlistenLanguageChanged(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LoadMO(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			Nirvana.I18N.LoadMO(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetString(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes<string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string o = Nirvana.I18N.GetString(arg0);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (TypeChecker.CheckTypes<string>(L, 1) && TypeChecker.CheckParamsType<object>(L, 2, count - 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				object[] arg1 = ToLua.ToParamsObject(L, 2, count - 1);
				string o = Nirvana.I18N.GetString(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.I18N.GetString");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPluralString(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3 && TypeChecker.CheckTypes<string, string, long>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				long arg2 = LuaDLL.tolua_toint64(L, 3);
				string o = Nirvana.I18N.GetPluralString(arg0, arg1, arg2);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (TypeChecker.CheckTypes<string, string, long>(L, 1) && TypeChecker.CheckParamsType<object>(L, 4, count - 3))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				long arg2 = LuaDLL.tolua_toint64(L, 3);
				object[] arg3 = ToLua.ToParamsObject(L, 4, count - 3);
				string o = Nirvana.I18N.GetPluralString(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.I18N.GetPluralString");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetParticularString(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<string, string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				string o = Nirvana.I18N.GetParticularString(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (TypeChecker.CheckTypes<string, string>(L, 1) && TypeChecker.CheckParamsType<object>(L, 3, count - 2))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				object[] arg2 = ToLua.ToParamsObject(L, 3, count - 2);
				string o = Nirvana.I18N.GetParticularString(arg0, arg1, arg2);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.I18N.GetParticularString");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetParticularPluralString(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4 && TypeChecker.CheckTypes<string, string, string, long>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				string arg2 = ToLua.ToString(L, 3);
				long arg3 = LuaDLL.tolua_toint64(L, 4);
				string o = Nirvana.I18N.GetParticularPluralString(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (TypeChecker.CheckTypes<string, string, string, long>(L, 1) && TypeChecker.CheckParamsType<object>(L, 5, count - 4))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				string arg2 = ToLua.ToString(L, 3);
				long arg3 = LuaDLL.tolua_toint64(L, 4);
				object[] arg4 = ToLua.ToParamsObject(L, 5, count - 4);
				string o = Nirvana.I18N.GetParticularPluralString(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.I18N.GetParticularPluralString");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Catalog(IntPtr L)
	{
		try
		{
			ToLua.PushSealed(L, Nirvana.I18N.Catalog);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Language(IntPtr L)
	{
		try
		{
			ToLua.Push(L, Nirvana.I18N.Language);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Catalog(IntPtr L)
	{
		try
		{
			Nirvana.I18NCatalog arg0 = (Nirvana.I18NCatalog)ToLua.CheckObject(L, 2, typeof(Nirvana.I18NCatalog));
			Nirvana.I18N.Catalog = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Language(IntPtr L)
	{
		try
		{
			UnityEngine.SystemLanguage arg0 = (UnityEngine.SystemLanguage)ToLua.CheckObject(L, 2, typeof(UnityEngine.SystemLanguage));
			Nirvana.I18N.Language = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

