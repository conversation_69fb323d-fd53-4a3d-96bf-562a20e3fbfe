-- F-副本-抓鬼.xls
local item_table={
[1]={item_id=26471,num=1,is_bind=1},
[2]={item_id=22636,num=1,is_bind=1},
[3]={item_id=26349,num=1,is_bind=1},
[4]={item_id=26200,num=2,is_bind=1},
[5]={item_id=26470,num=1,is_bind=1},
[6]={item_id=26352,num=1,is_bind=1},
[7]={item_id=26203,num=2,is_bind=1},
[8]={item_id=26045,num=1,is_bind=1},
[9]={item_id=22575,num=1,is_bind=1},
[10]={item_id=26370,num=1,is_bind=1},
[11]={item_id=46044,num=1,is_bind=1},
[12]={item_id=26376,num=1,is_bind=1},
[13]={item_id=46039,num=1,is_bind=1},
[14]={item_id=26344,num=1,is_bind=1},
[15]={item_id=27867,num=1,is_bind=1},
[16]={item_id=27611,num=1,is_bind=1},
[17]={item_id=48071,num=1,is_bind=1},
[18]={item_id=22621,num=1,is_bind=1},
[19]={item_id=26500,num=1,is_bind=1},
[20]={item_id=26515,num=1,is_bind=1},
[21]={item_id=91457,num=1,is_bind=0},
[22]={item_id=91458,num=1,is_bind=0},
[23]={item_id=91459,num=1,is_bind=0},
[24]={item_id=91460,num=1,is_bind=0},
[25]={item_id=91461,num=1,is_bind=0},
[26]={item_id=91462,num=1,is_bind=0},
[27]={item_id=91463,num=1,is_bind=0},
[28]={item_id=91465,num=1,is_bind=0},
[29]={item_id=91464,num=1,is_bind=0},
[30]={item_id=91466,num=1,is_bind=0},
[31]={item_id=91467,num=1,is_bind=0},
[32]={item_id=27611,num=5,is_bind=1},
[33]={item_id=26346,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
event={
[1]={event_id=1,},
[2]={event_id=2,param1=2050,talk="这就是鬼王的秘宝嘛",},
[3]={event_id=3,type=1,param1=3008,talk="啊，这鲜美的血肉。",},
[4]={event_id=4,type=1,param1=3007,talk="想找到鬼王大人，得先过我这关。",},
[5]={event_id=5,type=3,param1=3006,param2=2048,talk="哼，不自量力得家伙。",}
},

event_meta_table_map={
},
event_reward={
{},
{min_times=33,max_times=74,},
{min_times=75,max_times=132,},
{min_times=133,max_times=213,},
{min_times=214,max_times=311,},
{min_times=312,max_times=460,},
{min_times=461,max_times=614,},
{min_times=615,max_times=812,},
{min_times=813,max_times=1063,},
{min_times=1064,max_times=99999,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=2,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=3,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=4,},
{event_id=5,},
{event_id=5,},
{event_id=5,},
{event_id=5,},
{event_id=5,},
{event_id=5,},
{event_id=5,},
{event_id=5,},
{event_id=5,},
{event_id=5,}
},

event_reward_meta_table_map={
[33]=3,	-- depth:1
[34]=4,	-- depth:1
[35]=5,	-- depth:1
[36]=6,	-- depth:1
[37]=7,	-- depth:1
[38]=8,	-- depth:1
[39]=9,	-- depth:1
[42]=2,	-- depth:1
[43]=33,	-- depth:2
[44]=34,	-- depth:2
[32]=42,	-- depth:2
[46]=36,	-- depth:2
[47]=37,	-- depth:2
[48]=38,	-- depth:2
[40]=10,	-- depth:1
[45]=35,	-- depth:2
[25]=45,	-- depth:3
[29]=39,	-- depth:2
[12]=32,	-- depth:3
[13]=43,	-- depth:3
[14]=44,	-- depth:3
[15]=25,	-- depth:4
[16]=46,	-- depth:3
[17]=47,	-- depth:3
[18]=48,	-- depth:3
[30]=40,	-- depth:2
[19]=29,	-- depth:3
[22]=12,	-- depth:4
[23]=13,	-- depth:4
[24]=14,	-- depth:4
[49]=19,	-- depth:4
[26]=16,	-- depth:4
[27]=17,	-- depth:4
[28]=18,	-- depth:4
[20]=30,	-- depth:3
[50]=20,	-- depth:4
},
monster={
{},
{monster_id=3008,add_nuqi=1,}
},

monster_meta_table_map={
},
baotu={
{}
},

baotu_meta_table_map={
},
baotu_reward={
{},
{min_times=33,max_times=74,},
{min_times=75,max_times=132,},
{min_times=133,max_times=213,},
{min_times=214,max_times=311,},
{min_times=312,max_times=460,},
{min_times=461,max_times=614,},
{min_times=615,max_times=812,},
{min_times=813,max_times=1063,},
{min_times=1064,max_times=99999,}
},

baotu_reward_meta_table_map={
},
nuqi={
{}
},

nuqi_meta_table_map={
},
task={
{param1=10,},
{task_id=1,task_type=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},task_des="参与击败1次鬼王分身",task_name="鬼王分身",},
{task_id=2,task_type=3,reward_item={[0]=item_table[5],[1]=item_table[2],[2]=item_table[6],[3]=item_table[7]},task_des="采集1次鬼王宝藏",task_name="鬼王宝藏",},
{task_id=3,task_type=4,reward_item={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[7]},task_des="击败1个正在捉鬼的玩家",task_name="盗宝猎人",},
{task_id=4,task_type=5,reward_item={[0]=item_table[11],[1]=item_table[2],[2]=item_table[12],[3]=item_table[4]},task_des="进行1次探索",task_name="寻龙分金",},
{task_id=5,task_type=6,reward_item={[0]=item_table[13],[1]=item_table[2],[2]=item_table[14],[3]=item_table[7]},task_des="进入鬼王空间，击败鬼王真身",task_name="鬼王真身",},
{task_id=6,task_type=7,reward_item={[0]=item_table[15],[1]=item_table[9],[2]=item_table[16],[3]=item_table[7]},task_des="封印1次鬼王墓碑",task_name="封印墓碑",},
{task_id=7,task_type=8,param1=100,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20]},task_des="鬼王之怒达到100点",task_name="鬼王之怒",}
},

task_meta_table_map={
},
prof_aoi={
{},
{prof=2,},
{prof=3,},
{prof=4,param=6000,}
},

prof_aoi_meta_table_map={
},
other_default_table={global_scene_id=651,global_scene_pos="38,109",max_refresh_event_times=10,task_need_gold=200,fb_des="<color=#FFFFFF00>锁妖塔</color>内妖魔横行，鬼王虽被镇压其中，但其魔力正在越来越强。尽快找到鬼王，封印其魂魄。",show_reward_item={[0]=item_table[21],[1]=item_table[22],[2]=item_table[23],[3]=item_table[24],[4]=item_table[25],[5]=item_table[26],[6]=item_table[27],[7]=item_table[28],[8]=item_table[29],[9]=item_table[30],[10]=item_table[31]},npcid=1011,talk_text_1="鬼王就藏在这的某个角落，少侠拿上这些阵盘，快去寻找鬼王吧！",talk_text_2="鬼王的藏身之处需要阵盘才能找到他的气息！",talk_text_3="感谢少侠！",nuqi_max=100,fb_name="封魔禁地",},

event_default_table={event_id=1,type=2,gather_id=2049,gather_time=20,param1=2047,param2=0,param3=0,talk="这就是鬼王的宝藏嘛",},

event_reward_default_table={event_id=1,min_times=0,max_times=32,reward_item={},},

monster_default_table={monster_id=3007,add_nuqi=10,},

baotu_default_table={seq=0,item_id=27867,scene_id=652,monster_id=3009,monster_pos="243,167",kick_out_time=60,fb_time_s=300,enter_pos="224,168",tuijian_zl=950000,},

baotu_reward_default_table={seq=0,min_times=0,max_times=32,reward_item={[0]=item_table[13],[1]=item_table[18],[2]=item_table[32],[3]=item_table[7]},},

nuqi_default_table={},

task_default_table={task_id=0,task_type=1,param1=1,param2=0,param3=0,param4=0,reward_item={[0]=item_table[17],[1]=item_table[2],[2]=item_table[33],[3]=item_table[4]},task_des="击败10个死灵士兵",task_name="击败亡灵",},

prof_aoi_default_table={prof=1,param=4000,}

}

