local flush_type =
{
	refresh = 1,
	refresh_all_act_cells = 2,
	select_index = 3,
	flush_cell = 4,
	flush_cell_data = 5,
}

local flush_type_list = {}
for k,v in pairs(flush_type) do
	flush_type_list[v] = k
end

AsyncListView = AsyncListView or BaseClass()

local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

function AsyncListView:__init(item_render, list_view)
	if nil == item_render or nil == list_view then
		print_error("[AsyncListView] 请指定正确的参数, item_render, list_view")
	end

	self.data_list = nil
	self.cell_list = {}
	self.list_view = list_view
	self.item_render = item_render
	self.select_callback = nil

	self.start_zero = false 							-- 是否从0开始
	self.default_select_index = 1
	self.cur_select_index = nil
	self.jump_to_select_cell_index = nil
	self.flush_param_t = {}
	self.on_new_cell_call_back = nil
	self.is_delay_flush = true
	self.is_show_item_special_title_ui = false
	self.is_use_render_click = false
end

function AsyncListView:__delete()
	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
	self.select_callback = nil
	self.refresh_callback = nil
	self.is_delay_flush = true
	self.is_show_item_special_title_ui = false
end

-- List嵌套list的时候，如果里面的List是物品，在延迟调用的情况下会闪，暂时没查到问题，先临时处理,加个标记忽略延时
function AsyncListView:SetIsDelayFlush(value)
	self.is_delay_flush = value
end

-- 数据开始下标是否0开始
function AsyncListView:SetStartZeroIndex(bool)
	self.start_zero = bool
	self.default_select_index = bool and 0 or self.default_select_index
end

-- 是否用目标的点击触发列表的点击
function AsyncListView:SetUseRenderClick(bool)
	self.is_use_render_click = bool
end

-- 设置选中回调函数
function AsyncListView:SetSelectCallBack(select_callback)
	self.select_callback = select_callback
end

-- 设置数据源(异步刷新数据 )
function AsyncListView:SetDataList(data_list)
	if nil == self.data_list then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
		list_delegate.CellRefreshDel = BindTool.Bind(self.__RefreshListViewCells, self)
	end

	self.data_list = data_list or {}
	self:__Flush(flush_type.refresh, {0, 0})
end

-- 滚动到指定位置，格子不足将额外创建
function AsyncListView:ReloadData(percent)
	self:__Flush(flush_type.refresh, {1, percent})
end

-- 只刷新已经创建了的格子（不额外创建）
function AsyncListView:RefreshActiveCellViews()
	self:__Flush(flush_type.refresh_all_act_cells, {})
end

--刷新格子
function AsyncListView:__RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]

	if not self.start_zero then
		cell_index = cell_index + 1
	end

	if not item_cell then
		item_cell = self.item_render.New(cell.gameObject)
		self.cell_list[cell] = item_cell

		local toggle = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Toggle))
		local button = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Button))

		if toggle then
			item_cell:SetToggleGroup(self.list_view.toggle_group)
			toggle:AddClickListener(BindTool.Bind(self.ListEventCallback, self, item_cell))
		elseif button then
			button:AddClickListener(BindTool.Bind(self.ListEventCallback, self, item_cell))
		end

		if self.is_use_render_click then
			item_cell:SetClickCallBack(BindTool.Bind(self.ListEventCallback, self, item_cell))
		end

		if self.on_new_cell_call_back ~= nil then
			self.on_new_cell_call_back(item_cell)
		end

		if self.is_show_item_special_title_ui then
			item_cell:IsShowSpecialTitleUI(true)
		end
	end

	local item_data = self.data_list[cell_index]
	item_cell:SetIndex(cell_index)
	item_cell:SetData(item_data)
	item_cell:SetSelectIndex(self.cur_select_index)

	if self.refresh_callback then
		self.refresh_callback(item_cell, cell_index)
	end

	self:__TrySelectIndex(cell_index)
end

-- 因为__RefreshListViewCells的刷新并不一定是在调了reload后就同步
-- 比如scroll有时是在缓动到指定位置后再调__RefreshListViewCells
function AsyncListView:__TrySelectIndex(cell_index)
	local is_select_cell_index = false
	if nil ~= self.jump_to_select_cell_index then
		is_select_cell_index = cell_index == self.jump_to_select_cell_index
	elseif nil ~= self.default_select_index then
		is_select_cell_index = cell_index == self.default_select_index
	end

	if not is_select_cell_index then
		return
	end

	local is_default = false
	if nil ~= self.jump_to_select_cell_index then
		self.cur_select_index = self.jump_to_select_cell_index
	elseif nil ~= self.default_select_index then
		self.cur_select_index = self.default_select_index
		is_default = true
	end

	self.jump_to_select_cell_index = nil
	self.default_select_index = nil
	self:__OnSelectIndex(self.cur_select_index, is_default, false)
end

-- 设置限制选中方法 需要是否限制返回值
function AsyncListView:SetLimitSelectFunc(func)
	self.limit_select_func = func
end

-- 限制选中接口（可重写）
function AsyncListView:IsLimitSelectByIndex(cell_index)
	return false
end

--list事件回调
function AsyncListView:ListEventCallback(item_cell)
	local cell_index = item_cell:GetIndex()
	if self:IsLimitSelectByIndex(cell_index) then
		return
	end

	if self.limit_select_func and self.limit_select_func(item_cell) then
		return
	end

	self.cur_select_index = cell_index
	self:__OnSelectIndex(self.cur_select_index, false, true)
end

-- 当选中某个index时
-- 参数兼容旧接口
function AsyncListView:__OnSelectIndex(cell_index, is_default, is_click)
	if self:IsLimitSelectByIndex(cell_index) then
		return
	end

	if self.limit_select_func and self.limit_select_func() then
		return
	end

	for k, v in pairs(self.cell_list) do
		v:SetSelectIndex(cell_index)
	end

	if nil ~= self.select_callback and nil ~= cell_index then
		local item = self:GetItemAt(cell_index)
		if nil ~= item then
			self.select_callback(item, cell_index, is_default, is_click)
		end
	end
end

--跳转到某个cell 新增（同步）, min_show_len：从第几个列表开始滑动
function AsyncListView:JumpToIndex(cell_index, min_show_len)
	min_show_len = min_show_len or 4
	if self.start_zero then
		min_show_len = min_show_len - 1
	end

	self.jump_to_select_cell_index = cell_index
	local cell = self:GetItemAt(cell_index)
	local cell_is_active = cell and cell.view and cell.view.gameObject.activeInHierarchy
	if (cell_index - min_show_len < 1) and cell_is_active then
		self:__Flush(flush_type.refresh, {0, 0})
	else
		if cell_index > min_show_len then
			local percent = 0
			local len = 1
			if not IsEmptyTable(self.data_list) then
				len = #self.data_list
				if self.start_zero then
					percent = (cell_index + 1) / (len + 1)
				else
					percent = cell_index / len
				end
			end
			self:__Flush(flush_type.refresh, {1, percent})
		else
			self:__Flush(flush_type.refresh, {1, 0})
		end
	end
end

function AsyncListView:JumptToPrecent(percent)
	self:__Flush(flush_type.refresh, {1, percent})
end

function AsyncListView:JumpToTop()
	self:__Flush(flush_type.refresh, {1, 0})
end

-- 获得某个索引下的item (cell复用导致index错乱 慎用！)
-- item_render 是或继承ItemCell的 【不要用】  （item_cell 会对data进行改变
function AsyncListView:GetItemAt(cell_index)
	for k, v in pairs(self.cell_list) do
		if v:GetIndex() == cell_index and v:GetData() == self.data_list[cell_index] then
			return v
		end
	end
	return nil
end

function AsyncListView:GetCellData(cell_index)
	return self.data_list[cell_index]
end

-- 获得数据源
function AsyncListView:GetDataList()
	return self.data_list
end

--获得格子数
function AsyncListView:GetListViewNumbers()
	local length = 0
	if not IsEmptyTable(self.data_list) then
		length = self.start_zero and #self.data_list + 1 or #self.data_list
	end

	return length
end

--获得当前选择的index (GetSelectItem 合到这里)
function AsyncListView:GetSelectIndex()
	if nil ~= self.jump_to_select_cell_index then
		return self.jump_to_select_cell_index
	end

	return self.cur_select_index
end

-- 当面板能看到全部item的时候才用
-- 仅用于引导那(此接口有风险，不要再用。兼容旧代码)
function AsyncListView:GetAllItems()
	local show_item_list = {}
	for k, v in pairs(self.cell_list) do
		if v.view.gameObject.activeInHierarchy then
			show_item_list[v:GetIndex()] = v
		end
	end
	return show_item_list
end

-- 获得根据index排序后的celllist, is_sub降序排列
function AsyncListView:GetSortCellList(is_sub)
	local temp_list = {}
	local seq = self.start_zero and 1 or 0
	for k,v in pairs(self.cell_list) do
		temp_list[v:GetIndex() + seq] = v
	end

	return SortTableKey(temp_list, is_sub)
end

-- 清除所有item
function AsyncListView:RemoveAllItem()
	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
end

-- 设置默认选中index
function AsyncListView:SetDefaultSelectIndex(index)
	self.default_select_index = index
end

-- 设置当前选中index，有回调
function AsyncListView:SelectIndex(index)
	if nil == self.data_list[index] then
		return
	end

	self.jump_to_select_cell_index = index
	self.cur_select_index = index
	self:__Flush(flush_type.select_index, {})
end

-- 取消选中(兼容旧代码)
function AsyncListView:CancelSelect()
	self.jump_to_select_cell_index = nil
	self.cur_select_index = nil
	self:__Flush(flush_type.select_index, {})
end

-- 刷新某个格子
function AsyncListView:FlushCell(index, key, value_t)
	self:__Flush(flush_type.flush_cell, {index, key, value_t})
end

-- 刷新某个格子数据数据（兼容旧代码，不再建议用）
function AsyncListView:FlushCellData(index, cell_data)
	self:__Flush(flush_type.flush_cell_data, {index, cell_data})
end

-- 设置回调(兼容旧代码)
function AsyncListView:SetCellSizeDel(callback)
	if not self.list_view then return end
	local list_delegate = self.list_view.list_simple_delegate
	list_delegate.CellSizeDel = callback
end

--list刷新回调(兼容旧代码)
function AsyncListView:SetRefreshCallback(refresh_callback)
	self.refresh_callback = refresh_callback
end

--获取当前选中的格子的数据(兼容旧代码)
function AsyncListView:GetCurItemData()
	return self.data_list[self:GetSelectIndex()]
end

function AsyncListView:__DoRefresh(refresh_type, percent)
	if IsNil(self.list_view.scroller) then
		return
	end

	if refresh_type == 0 then
		  -- scroller在disable时awake不会调,用ScrollRect代表awake调过。兼容老包
		if IsNil(self.list_view.scroller.ScrollRect) then
			self.list_view.scroller:ReloadData(percent or 0)
		else
			self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
		end
	elseif refresh_type == 1 then
		self.list_view.scroller:ReloadData(percent or 0)
	elseif refresh_type == 2 then
		self.list_view.scroller:RefreshActiveCellViews()
	end

	self:__CheckRefreshCall()
	-- print_error("###############__DoRefresh>>>>>>>>>>>>", refresh_type, percent, self.cur_select_index, self.jump_to_select_cell_index)
end

function AsyncListView:__CheckRefreshCall()
	local now_frame = UnityEngine.Time.frameCount
	self.last_refresh_frame = self.last_refresh_frame or now_frame
	self.refresh_times_in_frame = self.refresh_times_in_frame or 0
	if self.last_refresh_frame ~= UnityEngine.Time.frameCount then
		self.last_refresh_frame = now_frame
		self.refresh_times_in_frame = 1
	else
		self.refresh_times_in_frame = self.refresh_times_in_frame + 1
		if self.refresh_times_in_frame > 1 then
			print_error(string.format("[AsyncListView] 在1帧里执行了%s次__DoRefresh，请整理代或使用异步接口", self.refresh_times_in_frame))
		end
	end
end

function AsyncListView:__DoFlushCell(index, key, value_t)
	local cell = self:GetItemAt(index)
	if nil ~= cell then
		cell:Flush(key, value_t)
	end
end

function AsyncListView:__DoFlushCellData(index, data)
	local cell = self:GetItemAt(index)
	if nil ~= cell then
		cell:SetData(data)
	end
end

function AsyncListView:__Flush(key, value)
	self.flush_param_t[key] = value
	if self.is_delay_flush then
		TryDelayCall(self, function ()
			self:__OnFlush()
		end, 0, "flush")
	else
		self:__OnFlush()
	end
end

function AsyncListView:__OnFlush()
	for i,_ in ipairs(flush_type_list) do
		local v = self.flush_param_t[i]
		if nil ~= v then
			if i == flush_type.refresh then
				self:__DoRefresh(v[1], v[2])
				self.flush_param_t[flush_type.refresh_all_act_cells] = nil
				self.flush_param_t[flush_type.select_index] = nil
				self.flush_param_t[flush_type.flush_cell] = nil
				self.flush_param_t[flush_type.flush_cell_data] = nil
			end

			if i == flush_type.refresh_all_act_cells then
				self:__DoRefresh(2, 0)
			end

			if i == flush_type.select_index then
				self:__OnSelectIndex(self.cur_select_index, false, false)
			end

			if i == flush_type.flush_cell then
				self:__DoFlushCell(v[1], v[2], v[3])
			end

			if i == flush_type.flush_cell_data then
				self:__DoFlushCellData(v[1], v[2])
			end
		end
	end

	self.flush_param_t = {}
end

function AsyncListView:SetCreateCellCallBack(callback)
	self.on_new_cell_call_back = callback
end

-- 刷新所有Cell最后回调 scroll_obj, start_idx, end_idx
function AsyncListView:SetEndScrolledCallBack(callback)
	if self.list_view then
		self.list_view.scroller.scrollerEndScrolled = callback
	end
end

-- 单个cell发生变化后回调 cellView
function AsyncListView:SetCellChangedCallBack(callback)
	if self.list_view then
		self.list_view.scroller.cellViewVisibilityChanged = callback
	end
end

--ItemCell专用，设置特殊title ui
function AsyncListView:SetIsShowItemSpecialTitleUI(is_show_item_special_title_ui)
	self.is_show_item_special_title_ui = is_show_item_special_title_ui
end

-- 重设item大小 用于改变item大小后调用
function AsyncListView:ResetItemSize()
	self.list_view.scroller:ResetItemSize()
end


StrengthenAsyncListView = StrengthenAsyncListView or BaseClass(AsyncListView)

-- 设置数据源(异步刷新数据 )
function StrengthenAsyncListView:SetDataList(data_list)
	if nil == data_list then
		print_error("[AsyncListView]不支持data_list为nil")
		return
	end

	if nil == self.data_list then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
		list_delegate.CellRefreshDel = BindTool.Bind(self.__RefreshListViewCells, self)
	end

    local old_num = self:GetListViewNumbers()
    self.data_list = data_list
    local new_num = self:GetListViewNumbers()

    --refresh_type 0 刷新整个(当数目变化的时候使用这个) 2 刷新当前显示的(当数目没有变化的时候使用这个)
    local refresh_type = old_num ~= new_num and 0 or 2
	self:__Flush(flush_type.refresh, {refresh_type, 0})
end