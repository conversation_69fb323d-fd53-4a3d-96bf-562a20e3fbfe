-- 已整合到主View中，此脚本不会执行
CharmAttrView = CharmAttrView or BaseClass(SafeBaseView)
function CharmAttrView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/cultivation_ui/charm_prefab", "layout_charm_attr_view")
end

function CharmAttrView:LoadCallBack()
	if not self.charm_general_attr_list then
		self.charm_general_attr_list = AsyncListView.New(CharmGeneralAttrListRender, self.node_list.charm_general_attr_list)
	end
end

function CharmAttrView:ReleaseCallBack()
	if self.charm_general_attr_list then
		self.charm_general_attr_list:DeleteMe()
		self.charm_general_attr_list = nil
	end
end

function CharmAttrView:OnFlush()
	local data_list = CultivationWGData.Instance:GetCharmGeneralAttrDataList()
	local has_data = not IsEmptyTable(data_list)
	self.node_list.no_attr_tip:CustomSetActive(not has_data)
	self.node_list.charm_general_attr_list:CustomSetActive(has_data)
	
	if has_data then
		self.charm_general_attr_list:SetDataList(data_list)
	end
end

