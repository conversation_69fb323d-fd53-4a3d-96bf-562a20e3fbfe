GuildInfoListItem = GuildInfoListItem or BaseClass(BaseRender)

function GuildInfoListItem:__init()
    self.lbl_name = self.node_list.lbl_name
	self.lbl_level = self.node_list.lbl_level
	self.lbl_post = self.node_list.lbl_post
	self.lbl_cap = self.node_list.lbl_cap
    
	self.hight = self.node_list.hight

end

function GuildInfoListItem:CreateChild()
	
end

function GuildInfoListItem:OnFlush()
	if nil == self.data then
		return
	end
end