﻿using System;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine.EventSystems;
using LuaInterface;
using System.Globalization;

public class EmojiText : Text, IPointerClick<PERSON><PERSON><PERSON>, IPointerDown<PERSON>and<PERSON>, IPointerUpHandler
{
    private static readonly string no_breaking_space = "\u00A0";
    private static readonly string emojiMask = "\\[[0-9]+\\]";
    private static readonly string underLineMask = "<line>(.*?)</line>";
    private static readonly string buttonMask = "<button><([0-9]+)>(.*?)</button>";
    private static readonly string emojiStr = "<size={0}>{1}</size>";
    private static readonly string emojiReplaceStr = "&";
    private static readonly char emojiReplaceChar = '&';

    private static Dictionary<string, EmojiInfo> EmojiLookup = null;
    public static bool drawRect = false;

    [SerializeField]
    private float _workingRowWidth = 0f;
    [SerializeField]
    private float _minRowWidth = 0f;
    [SerializeField]
    private LineMode _lineMode = LineMode.Under;
    [SerializeField]
    private float _lineScale = 0.5f;
    [SerializeField]
    private float _lineOffset = 0f;
    [SerializeField]
    private float _iconScale = 1f;
    [SerializeField]
    private float _bigIconScale = 1f;
    [SerializeField]
    private Vector2 _buttonScale = Vector2.one;
    [SerializeField]
    private Vector2 _padding = Vector2.zero;
    [SerializeField]
    private bool _autoPadding = false;

    private string emojiText;
    private string originalText;
    private readonly UIVertex[] m_TempVerts = new UIVertex[4];
    private Vector3 tempVector3 = new Vector3();
    private Vector2 tempVector2 = new Vector2();
    private Dictionary<int, EmojiInfo> emojiDic = new Dictionary<int, EmojiInfo>();
    private Dictionary<int, int> lineDic = new Dictionary<int, int>();
    private Dictionary<int, int> buttonDic = new Dictionary<int, int>();
    private Dictionary<int, List<Rect>> buttonRects = new Dictionary<int, List<Rect>>();
    private List<Rect> lineRectDir = new List<Rect>();
    private Dictionary<int, Action<float, float>> callBackDir = new Dictionary<int, Action<float, float>>();
    private Dictionary<int, int> emojiDir = new Dictionary<int, int>();
    private Dictionary<int, int> lineDir = new Dictionary<int, int>();
    private Dictionary<int, int> buttonDir = new Dictionary<int, int>();

    public enum LineMode
    {
        Top, Middle, Under
    }

    public override float preferredWidth
    {
        get
        {
            float preferredWidth = cachedTextGeneratorForLayout.GetPreferredWidth(emojiText, GetGenerationSettings(rectTransform.rect.size)) / pixelsPerUnit;
            return preferredWidth;
        }
    }
    public override float preferredHeight
    {
        get
        {
            return cachedTextGeneratorForLayout.GetPreferredHeight(emojiText, GetGenerationSettings(rectTransform.rect.size)) / pixelsPerUnit;
        }
    }

    public LineMode lineMode
    {
        set
        {
            if (_lineMode != value)
            {
                _lineMode = value;
                SetVerticesDirty();
            }
        }
        get
        {
            return _lineMode;
        }
    }

    public float lineScale
    {
        set
        {
            if (_lineScale != value)
            {
                _lineScale = value;
                SetVerticesDirty();
            }
        }
        get
        {
            return _lineScale;
        }
    }

    public float lineOffset
    {
        set
        {
            if (_lineOffset != value)
            {
                _lineOffset = value;
                SetVerticesDirty();
            }
        }
        get
        {
            return _lineOffset;
        }
    }

    public float IconScale
    {
        get
        {
            return _iconScale;
        }
        set
        {
            if (_iconScale != value)
            {
                _iconScale = Mathf.Max(value, 0f);
                SetVerticesDirty();
            }
        }
    }

    public float BigIconScale
    {
        get
        {
            return _bigIconScale;
        }
        set
        {
            if (_bigIconScale != value)
            {
                _bigIconScale = Mathf.Max(value, 0f);
                SetVerticesDirty();
            }
        }
    }

    public Vector2 ButtonScale
    {
        get { return _buttonScale; }
        set { _buttonScale = value; }
    }

    public Vector2 Padding
    {
        get { return _padding; }
        set
        {
            if (value != _padding)
            {
                _padding = value;
                CalculateRect();
            }
        }
    }

    public bool AutoPadding
    {
        get { return _autoPadding; }
        set
        {
            if (value != _autoPadding)
            {
                _autoPadding = value;
                CalculateRect();
            }
        }
    }

    public static void BuildEmojiLookUp(string emojiContent)
    {
        if (null != EmojiLookup)
        {
            EmojiLookup.Clear();
        }
        else
        {
            EmojiLookup = new Dictionary<string, EmojiInfo>();
        }

        string[] lines = emojiContent.Split('\n');
        for (int i = 1; i < lines.Length; i++)
        {
            if (!string.IsNullOrEmpty(lines[i]))
            {
                string[] strs = lines[i].Split('\t');
                EmojiInfo info;
                info.x = float.Parse(strs[3], CultureInfo.InvariantCulture);
                info.y = float.Parse(strs[4], CultureInfo.InvariantCulture);
                info.size_x = float.Parse(strs[5], CultureInfo.InvariantCulture);
                info.size_y = float.Parse(strs[6], CultureInfo.InvariantCulture);
                info.isBig = int.Parse(strs[7], CultureInfo.InvariantCulture) == 1;
                //Debug.LogErrorFormat("{0}--{1}--{2}--{3}",strs[1], strs[2], strs[3], strs[4]);
                EmojiLookup.Add(strs[1], info);
            }
        }
    }

    public override string text
    {
        get
        {
            return m_Text;
        }
        set
        {
            if (String.IsNullOrEmpty(value))
            {
                m_Text = "";
                ParseText();
                CalculateRect();
                SetVerticesDirty();
            }
            else if (m_Text != value)
            {
                m_Text = value;
                ParseText();
                CalculateRect();
                SetVerticesDirty();
                SetLayoutDirty();
            }
        }
    }

    public float workingRowWidth
    {
        get { return this._workingRowWidth; }
    }

    public float minRowWidth
    {
        get { return this._minRowWidth; }
    }

    public void Clear()
    {
        originalText = string.Empty;
        m_Text = string.Empty;
        emojiText = string.Empty;
        foreach (var list in buttonRects.Values)
        {
            list.Clear();
        }

        buttonRects.Clear();
        emojiDic.Clear();
        lineDic.Clear();
        buttonDic.Clear();
        callBackDir.Clear();
        emojiDir.Clear();
        lineDir.Clear();
        buttonDir.Clear();
    }

    public void AddClickListener(Action<float, float> action, int index)
    {
        callBackDir[index] = action;
    }

    public void RemoveClickListener(int index)
    {
        if (callBackDir.ContainsKey(index))
            callBackDir.Remove(index);
    }

    [NoToLua]
    public int[] GetEmojiIndex()
    {
        int[] emojiIndexs = new int[emojiDic.Keys.Count];
        var iter = emojiDic.Keys.GetEnumerator();
        int i = 0;
        while (iter.MoveNext())
        {
            int emojiIndex = iter.Current;
            emojiIndexs[i] = emojiIndex;
            ++i;
        }
        return emojiIndexs;
    }

    protected override void Awake()
    {
        base.Awake();
        ParseText();
        CalculateRect();
    }

    protected override void OnEnable()
    {
        base.OnEnable();
#if UNITY_EDITOR
        if (null != canvas)
        {
            if (((int)canvas.additionalShaderChannels & (int)AdditionalCanvasShaderChannels.TexCoord1) != 1)
            {
                Debug.LogError("[EmojiText] Canvas additionalShaderChannels must support TexCoord1");
            }
        }
#endif
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        Clear();
    }

#if UNITY_EDITOR
    protected override void OnValidate()
    {
        base.OnValidate();
        TextAsset emojiContent = EditorResourceMgr.LoadObject("uis/emoji", "emoji.txt", typeof(TextAsset)) as TextAsset;
        if (null != emojiContent)
        {
            BuildEmojiLookUp(emojiContent.text);
        }
    }

    [NoToLua]
    public void ForceSetAllDirty()
    {
        ParseText();
        SetVerticesDirty();
        SetLayoutDirty();
        CalculateRect();
    }
#endif

    private void CalculateRect()
    {
        float paddingX = Padding.x;
        float paddingY = Padding.y;
        if (_autoPadding)
        {
            if (emojiDic.Count <= 0)
            {
                CharacterInfo characterInfo;
                font.RequestCharactersInTexture(emojiReplaceStr, fontSize, fontStyle);
                if (font.GetCharacterInfo(emojiReplaceChar, out characterInfo, fontSize, fontStyle))
                {
                    int newFontSize = Mathf.FloorToInt(fontSize * _iconScale);
                    font.RequestCharactersInTexture(emojiReplaceStr, newFontSize, fontStyle);
                    CharacterInfo newCharacterInfo;
                    if (font.GetCharacterInfo(emojiReplaceChar, out newCharacterInfo, newFontSize, fontStyle))
                    {
                        float diff = newCharacterInfo.glyphHeight - characterInfo.glyphHeight;
                        paddingY = Math.Max(paddingY, diff);
                    }
                }
            }
        }
        bool isDirty = false;
        Vector2 size = rectTransform.rect.size;
        float preferredWidth = cachedTextGeneratorForLayout.GetPreferredWidth(emojiText, GetGenerationSettings(size)) / pixelsPerUnit;
        preferredWidth = Mathf.Clamp(preferredWidth, _minRowWidth, _workingRowWidth);
        if (size.x != preferredWidth + paddingX)
        {
            size.x = preferredWidth + paddingX;
            isDirty = true;
        }

        float preferredHeight = cachedTextGeneratorForLayout.GetPreferredHeight(emojiText, GetGenerationSettings(size)) / pixelsPerUnit;
        if (size.y != preferredHeight + paddingY)
        {
            size.y = preferredHeight + paddingY;
            isDirty = true;
        }

        if (isDirty)
        {
            rectTransform.sizeDelta = size;
        }
    }

    /// <summary>
    /// 换掉富文本
    /// </summary>
    private string ReplaceRichText(string str)
    {
        str = Regex.Replace(str, @"<color=(.+?)>", "");
        str = Regex.Replace(str, @"<size=(.+?)>", "");
        str = str.Replace("</color>", "");
        str = str.Replace("</size>", "");
        str = str.Replace("<b>", "");
        str = str.Replace("</b>", "");
        str = str.Replace("<i>", "");
        str = str.Replace("</i>", "");
        str = str.Replace("\n", "");
        str = str.Replace("\t", "");
        str = str.Replace("\r", "");

        return str;
    }

    private void ParseText()
    {
        foreach (var list in buttonRects.Values)
        {
            list.Clear();
        }

        buttonRects.Clear();
        if (!supportRichText || null == EmojiLookup || originalText == m_Text)
            return;

        emojiDic.Clear();
        lineDic.Clear();
        buttonDic.Clear();
        originalText = m_Text;
        emojiText = m_Text.Replace(" ", no_breaking_space);

        MatchCollection emojiMatches;
        MatchCollection lineMatches;
        MatchCollection buttonMatches;
        
        if (_iconScale != 1f || _bigIconScale != 1f)
        {
            emojiMatches = Regex.Matches(emojiText, emojiMask);
            for (int i = 0; i < emojiMatches.Count; i++)
            {
                var match = emojiMatches[i];
                EmojiInfo info;
                if (EmojiLookup.TryGetValue(match.Value, out info))
                {
                    int size;
                    if (info.isBig)
                    {
                        size = Mathf.FloorToInt(fontSize * _bigIconScale);
                    }
                    else
                    {
                        size = Mathf.FloorToInt(fontSize * _iconScale);
                    }
                    emojiText = emojiText.Replace(match.Value, string.Format(emojiStr, size, match.Value));
                }
            }
        }

        string resEmojiText = ReplaceRichText(emojiText);
        emojiText = Regex.Replace(emojiText, emojiMask, emojiReplaceStr);
        emojiText = Regex.Replace(emojiText, "<button><([0-9]+)>", "");
        emojiText = emojiText.Replace("<line>", "");
        emojiText = emojiText.Replace("</line>", "");
        emojiText = emojiText.Replace("</button>", "");

        int nOffset = 0;
        // emoji
        emojiMatches = Regex.Matches(resEmojiText, emojiMask);
        for (int i = 0; i < emojiMatches.Count; i++)
        {
            var match = emojiMatches[i];
            if (EmojiLookup.ContainsKey(match.Value) && !emojiDir.ContainsKey(match.Index))
            {
                emojiDir.Add(match.Index, i);
            }
        }

        //下划线
        lineMatches = Regex.Matches(resEmojiText, underLineMask);
        for (int i = 0; i < lineMatches.Count; i++)
        {
            var match = lineMatches[i];
            lineDir.Add(match.Index, i);
        }

        //有点击功能的下划线
        buttonMatches = Regex.Matches(resEmojiText, buttonMask);
        for (int i = 0; i < buttonMatches.Count; i++)
        {
            var match = buttonMatches[i];
            buttonDir.Add(match.Index, i);
        }

        int length = resEmojiText.Length;
        for (int i = 0; i < length; ++i)
        {
            if (emojiDir.ContainsKey(i))
            {
                EmojiInfo info;
                var match = emojiMatches[emojiDir[i]];
                if (EmojiLookup.TryGetValue(match.Value, out info))
                {
                    emojiDic.Add(match.Index - nOffset, info);
                    nOffset += match.Length - 1;
                }

                i += match.Value.Length - 1;
            }
            else if (lineDir.ContainsKey(i))
            {
                var match = lineMatches[lineDir[i]];
                var str = match.Groups[1].Value;
                for (int j = 0; j < str.Length; ++j)
                {
                    lineDic.Add(match.Index - nOffset + j, lineDir[i]);
                }

                nOffset += match.Value.Length - str.Length;
                i += match.Value.Length - 1;
            }
            else if (buttonDir.ContainsKey(i))
            {
                var match = buttonMatches[buttonDir[i]];
                var index = int.Parse(match.Groups[1].Value);
                var str = match.Groups[2].Value;
                int offset = match.Groups[2].Index - match.Index;
                for (int j = 0; j < str.Length; ++j)
                {
                    int tempIndex = match.Groups[2].Index + j;
                    if (emojiDir.ContainsKey(tempIndex))
                    {
                        EmojiInfo info;
                        var emojiMatch = emojiMatches[emojiDir[tempIndex]];
                        if (EmojiLookup.TryGetValue(emojiMatch.Value, out info))
                        {
                            emojiDic.Add(emojiMatch.Index - nOffset - offset, info);
                            offset += emojiMatch.Length - 1;
                            str = str.Replace(emojiMatch.Value, emojiReplaceStr);
                        }
                    }
                }

                for (int j = 0; j < str.Length; ++j)
                {
                    int charIndex = match.Index - nOffset + j;
                    buttonDic.Add(charIndex, index);
                    // index大于1000则显示下划线
                    if (!emojiDic.ContainsKey(charIndex) && index >= 1000)
                        lineDic.Add(charIndex, index + lineDir.Count);
                }

                nOffset += match.Value.Length - str.Length;
                i += match.Value.Length - 1;
            }
        }
    }

    private void AddButtonRect(int buttonIndex, Vector3 topLeft, Vector3 bottomRight, int charIndex)
    {
        if (bottomRight.x - topLeft.x <= 0)
        {
            return;
        }
        float unitsPerPixel = 1 / pixelsPerUnit;
        topLeft *= unitsPerPixel;
        bottomRight *= unitsPerPixel;
        Rect rect = new Rect(topLeft.x, topLeft.y, bottomRight.x - topLeft.x, topLeft.y - bottomRight.y);
        List<Rect> list;
        if (!buttonRects.TryGetValue(buttonIndex, out list))
        {
            list = new List<Rect>();
            buttonRects.Add(buttonIndex, list);
        }

        if (list.Count <= 0)
        {
            list.Add(rect);
        }
        else
        {
            var lastRect = list[list.Count - 1];
            // 换行了
            if (!IsSameLine(charIndex, charIndex - 1))
            {
                list.Add(rect);
            }
            else
            {
                lastRect.width = rect.x - lastRect.x + rect.width;
                if (rect.height > lastRect.height)
                {
                    lastRect.height = rect.height;
                    lastRect.y = rect.y;
                }
                list[list.Count - 1] = lastRect;
            }
        }
    }

    private bool IsClickButton(Vector2 pressPosition, out int index)
    {
        index = -1;
        if (null == canvas || null == canvas.worldCamera)
            return false;

        RectTransformUtility.ScreenPointToLocalPointInRectangle(rectTransform, pressPosition, this.canvas.worldCamera, out pressPosition);
        foreach (var pairs in buttonRects)
        {
            index = pairs.Key;
            var list = buttonRects[index];
            for (int i = 0; i < list.Count; ++i)
            {
                Rect rect = list[i];

                float offsetWidth = rect.width * (ButtonScale.x - 1f);
                float offsetHeight = rect.height * (ButtonScale.y - 1f);

                float offsetX = pressPosition.x - rect.x + offsetWidth / 2f;
                float offsetY = pressPosition.y - rect.y - offsetHeight / 2f;
                if (offsetX >= 0 && offsetX <= rect.width + offsetWidth && offsetY <= 0 && -offsetY <= rect.height + offsetHeight)
                {
                    return true;
                }
            }
        }
        return false;
    }

    private void OnClick(PointerEventData eventData)
    {
        Vector2 pressPosition = eventData.pressPosition;
        int index;
        if (IsClickButton(pressPosition, out index))
        {
            ButtonClick(index, pressPosition);
        }
    }

    private void ButtonClick(int index, Vector2 pressPosition)
    {
        Action<float, float> action;
        if (callBackDir.TryGetValue(index, out action))
        {
            if (null != action)
            {
                try
                {
                    action(pressPosition.x, pressPosition.y);
                }
                catch (Exception exp)
                {
                    Debug.LogError(exp.Message);
                }
            }
        }
    }

    private void CreateLine(List<Rect> list, VertexHelper toFill, Color color)
    {
        if (list.Count <= 0)
            return;

        Rect rect = list[0];
        Vector2 anchor = rect.position;
        float totalWidth = rect.width;
        float height = rect.height;
        for (int i = 1; i < list.Count; ++i)
        {
            rect = list[i];
            totalWidth = rect.position.x - anchor.x + rect.width;
            if (rect.height > height)
            {
                height = Mathf.Max(rect.height, height);
                anchor.y = rect.y;
            }
        }

        float unitsPerPixel = 1 / pixelsPerUnit;
        CharacterInfo characterInfo;
        font.RequestCharactersInTexture("_", fontSize, fontStyle);
        if (font.GetCharacterInfo('_', out characterInfo, fontSize, fontStyle))
        {
            float offsetHeight = 0;
            if (lineMode == LineMode.Under)
            {
                offsetHeight = -height;
            }
            else if (lineMode == LineMode.Middle)
            {
                offsetHeight = -height / 2;
            }

            float glyphHeight = characterInfo.glyphHeight * _lineScale;
            float x = anchor.x;
            float y = anchor.y + offsetHeight + _lineOffset;
            float z = m_TempVerts[0].position.z;
            m_TempVerts[0].position = new Vector3(x, y, z);
            m_TempVerts[1].position = new Vector3(x + totalWidth, y, z);
            m_TempVerts[2].position = new Vector3(x + totalWidth, y - glyphHeight, z);
            m_TempVerts[3].position = new Vector3(x, y - glyphHeight, z);

            for (int j = 0; j < 4; ++j)
            {
                m_TempVerts[j].position *= unitsPerPixel;
                m_TempVerts[j].color = color;
            }

            m_TempVerts[0].uv0 = Vector2.zero;
            m_TempVerts[1].uv0 = Vector2.zero;
            m_TempVerts[2].uv0 = Vector2.zero;
            m_TempVerts[3].uv0 = Vector2.zero;

            toFill.AddUIVertexQuad(m_TempVerts);
        }
    }

    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        if (font == null)
            return;

        buttonRects.Clear();
        lineRectDir.Clear();
        m_DisableFontTextureRebuiltCallback = true;

        Vector2 extents = rectTransform.rect.size;

        var settings = GetGenerationSettings(extents);
        cachedTextGenerator.Populate(emojiText, settings);

        Rect inputRect = rectTransform.rect;

        Vector2 textAnchorPivot = GetTextAnchorPivot(alignment);
        Vector2 refPoint = Vector2.zero;
        refPoint.x = Mathf.Lerp(inputRect.xMin, inputRect.xMax, textAnchorPivot.x);
        refPoint.y = Mathf.Lerp(inputRect.yMin, inputRect.yMax, textAnchorPivot.y);

        Vector2 roundingOffset = PixelAdjustPoint(refPoint) - refPoint;

        // Apply the offset to the vertices
        List<UIVertex> verts = ListPool<UIVertex>.Get();
        cachedTextGenerator.GetVertices(verts);

        float unitsPerPixel = 1 / pixelsPerUnit;
        int vertCount = verts.Count;
        if (vertCount <= 0)
        {
            toFill.Clear();
            return;
        }

        toFill.Clear();
        if (roundingOffset != Vector2.zero)
        {
            Vector3 tempPos = Vector3.zero;
            for (int i = 0; i < vertCount; ++i)
            {
                var vert = verts[i];
                int tempVertsIndex = i & 3;
                // 当在一个四边形计算中，连续2个顶点都相同时，则认为不是一完整的字符
                // 处理<color='#ff5a00'>AA</color>这种产生多个多余顶点的情况
                if (tempVertsIndex == 0) tempPos = vert.position;
                else if (tempPos == vert.position) continue;

                m_TempVerts[tempVertsIndex] = vert;
                m_TempVerts[tempVertsIndex].position *= unitsPerPixel;
                m_TempVerts[tempVertsIndex].position.x += roundingOffset.x;
                m_TempVerts[tempVertsIndex].position.y += roundingOffset.y;
                if (tempVertsIndex == 3)
                {
                    toFill.AddUIVertexQuad(m_TempVerts);
                }
            }
        }
        else
        {
            Vector2 anchor = Vector2.zero;
            bool lineColorDirty = true;
            Color lineColor = Color.white;
            float height = 0;
            int lineIndex = 0;
            Vector3 tempPos = Vector3.zero;
            for (int i = 0; i < vertCount; ++i)
            {
                EmojiInfo info;
                int index = i / 4;
                // is emoji
                if (emojiDic.TryGetValue(index, out info))
                {
                    // 设置顶点
                    m_TempVerts[3] = verts[i];
                    m_TempVerts[2] = verts[i + 1];
                    m_TempVerts[1] = verts[i + 2];
                    m_TempVerts[0] = verts[i + 3];

                    m_TempVerts[0].position *= unitsPerPixel;
                    m_TempVerts[1].position *= unitsPerPixel;
                    m_TempVerts[2].position *= unitsPerPixel;
                    m_TempVerts[3].position *= unitsPerPixel;

                    // 设置uv
                    float pixelOffset = info.size_x / 32 / 2;

                    tempVector2.x = info.x + pixelOffset;
                    tempVector2.y = info.y + pixelOffset;
                    m_TempVerts[0].uv1 = tempVector2;

                    tempVector2.x = info.x - pixelOffset + info.size_x;
                    tempVector2.y = info.y + pixelOffset;
                    m_TempVerts[1].uv1 = tempVector2;

                    tempVector2.x = info.x - pixelOffset + info.size_x;
                    tempVector2.y = info.y - pixelOffset + info.size_y;
                    m_TempVerts[2].uv1 = tempVector2;

                    tempVector2.x = info.x + pixelOffset;
                    tempVector2.y = info.y - pixelOffset + info.size_y;
                    m_TempVerts[3].uv1 = tempVector2;

                    toFill.AddUIVertexQuad(m_TempVerts);

                    if (buttonDic.ContainsKey(index))
                    {
                        // 增加button
                        int buttonIndex = buttonDic[index];
                        AddButtonRect(buttonIndex, verts[i].position, verts[i + 2].position, index);
                    }

                    i += 3;
                }
                else
                {
                    int tempVertsIndex = i & 3;
                    // 当在一个四边形计算中，连续2个顶点都相同时，则认为不是一完整的字符)
                    // 处理<color='#ff5a00'>AA</color>这种产生多个多余顶点的情况
                    var vert = verts[i];
                    if (tempVertsIndex == 0) tempPos = vert.position;
                    else if (tempPos == vert.position) continue;
                    m_TempVerts[tempVertsIndex] = verts[i];
                    m_TempVerts[tempVertsIndex].position *= unitsPerPixel;
                    if (tempVertsIndex == 3)
                    {
                        toFill.AddUIVertexQuad(m_TempVerts);

                        if (buttonDic.ContainsKey(index))
                        {
                            // 增加button
                            int buttonIndex = buttonDic[index];
                            AddButtonRect(buttonIndex, verts[i - 3].position, verts[i - 1].position, index);
                        }

                        if (lineDic.ContainsKey(index))
                        {
                            // 增加下划线
                            lineIndex = lineDic[index];
                            float fCharHeight = (verts[i - 2].position.y - verts[i - 1].position.y);
                            float fCharWidth = verts[i - 2].position.x - verts[i - 3].position.x;
                            if (lineRectDir.Count <= 0)
                            {
                                height = fCharHeight;
                                anchor = verts[i - 3].position;
                                if (lineColorDirty)
                                {
                                    lineColor = verts[i - 3].color;
                                    lineColorDirty = false;
                                }
                            }
                            else
                            {
                                // 换行了
                                if (!IsSameLine(index, index - 1))
                                {
                                    CreateLine(lineRectDir, toFill, lineColor);
                                    lineRectDir.Clear();
                                    height = fCharHeight;
                                    anchor = verts[i - 3].position;
                                }
                                else if (fCharHeight > height)
                                {
                                    height = fCharHeight;
                                    anchor = verts[i - 3].position;
                                }
                            }
                            lineRectDir.Add(new Rect(verts[i - 3].position.x, verts[i - 3].position.y, fCharWidth, fCharHeight));

                            if (!lineDic.ContainsKey(index + 1) || lineDic[index + 1] != lineIndex)
                            {
                                CreateLine(lineRectDir, toFill, lineColor);
                                lineRectDir.Clear();
                                lineColorDirty = true;
                            }
                        }
                    }
                }
            }
        }

        ListPool<UIVertex>.Release(verts);
        m_DisableFontTextureRebuiltCallback = false;
    }

    private bool IsSameLine(int charIndexA, int charIndexB)
    {
        var lines = cachedTextGenerator.lines;
        if (lines.Count <= 1)
            return true;

        List<UIVertex> verts = ListPool<UIVertex>.Get();
        cachedTextGenerator.GetVertices(verts);
        int index_A = charIndexA * 4;
        int index_B = charIndexB * 4;

        if (index_A < 0 || index_B < 0 || index_A > verts.Count || index_B > verts.Count)
        {
            return true;
        }

        UIVertex UIVertex_A = verts[index_A];
        UIVertex UIVertex_B = verts[index_B];

        bool flagA, flagB = true;
        for (int i = 1; i < lines.Count; ++i)
        {
            UILineInfo line = lines[i];
            flagA = UIVertex_A.position.y <= line.topY && UIVertex_A.position.y > line.topY - line.height;
            flagB = UIVertex_B.position.y <= line.topY && UIVertex_B.position.y > line.topY - line.height;
            if (flagA != flagB)
            {
                return false;
            }
            else if (!flagA)
            {
                return true;
            }

        }
        return true;
    }

    [NoToLua]
    public override bool Raycast(Vector2 sp, Camera eventCamera)
    {
        bool isRaycast = base.Raycast(sp, eventCamera);
        if (isRaycast)
        {
            int index;
            return IsClickButton(sp, out index);
        }
        return false;
    }

    [NoToLua]
    public virtual void OnPointerClick(PointerEventData eventData)
    {
        if (eventData.button != PointerEventData.InputButton.Left)
            return;

        OnClick(eventData);
    }

    [NoToLua]
    public virtual void OnPointerDown(PointerEventData eventData)
    {

    }

    [NoToLua]
    public virtual void OnPointerUp(PointerEventData eventData)
    {

    }

#if UNITY_EDITOR
    private void OnGUI()
    {
        if (drawRect && null != canvas && null != canvas.worldCamera)
        {
            foreach (var list in buttonRects.Values)
            {
                for (int i = 0; i < list.Count; ++i)
                {
                    Rect rect = list[i];
                    var topLeft =  RectTransformUtility.WorldToScreenPoint(canvas.worldCamera, transform.TransformPoint(rect.position));
                    var bottomRight = RectTransformUtility.WorldToScreenPoint(canvas.worldCamera, transform.TransformPoint(rect.position + rect.size));
                    GUI.Button(new Rect(topLeft.x, Screen.height - topLeft.y, bottomRight.x - topLeft.x, bottomRight.y - topLeft.y), "");
                }
            }
        }
    }
#endif

    struct EmojiInfo
    {
        public float x;
        public float y;
        public float size_x;
        public float size_y;
        public bool isBig;
    }
}
