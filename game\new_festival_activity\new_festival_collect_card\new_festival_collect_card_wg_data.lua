NewFestivalCollectCardWGData = NewFestivalCollectCardWGData or BaseClass()

function NewFestivalCollectCardWGData:__init()
	if NewFestivalCollectCardWGData.Instance then
		error("[NewFestivalCollectCardWGData] Attempt to create singleton twice!")
		return
	end

    NewFestivalCollectCardWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.NewFestivalCollectCard, BindTool.Bind(self.GetNewJRCollectCardRemind, self))
end

function NewFestivalCollectCardWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.NewFestivalCollectCard)
    NewFestivalCollectCardWGData.Instance = nil
end

function NewFestivalCollectCardWGData:InitParam()
    self.cur_grade = -1             -- 当前档位
    self.gain_times = 0             -- 当前向别人索要的次数
    self.be_times = 0               -- 当前被赠送次数
    self.times = 0                  -- 当前主动赠送次数
    self.redemption_num = 0         -- 领取奖励次数
    self.request_list = {}          -- 别人的请求
    self.friend_list = {}           -- 朋友列表
end

function NewFestivalCollectCardWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("new_festival_activity_collect_card_auto")
    self.config_param_cfg = ListToMap(cfg.config_param, "grade")
    self.activity_param_cfg = ListToMap(cfg.activity_param, "grade")
end

function NewFestivalCollectCardWGData:GetNewJRCollectCardRemind()
    local max_redemption_num = NewFestivalCollectCardWGData.Instance:GetCurActivityOpenParamCfg().redemption_num or 0
    local item_kind_sum = self:GetItemKindSum()
    local item_list = self:GetCurGradeItemList()
    -- 可以领取道具 or 有请求
    if (self.redemption_num < max_redemption_num and item_kind_sum >= #item_list) or not IsEmptyTable(self.request_list) then
        return 1
    end

    return 0
end

-- 协议相关start
function NewFestivalCollectCardWGData:SetNewJRCollectCardSelfInfo(protocol)
    self.cur_grade = protocol.grade
    self.gain_times = protocol.gain_times
    self.be_times = protocol.be_times
    self.times = protocol.times
    self.redemption_num = protocol.redemption_num

    -- 没请求也会接收三十个数据，过滤出真正需要的信息
    self.request_list = {}
    for k, v in pairs(protocol.request_list) do
        if v.uuid > 0 then
            table.insert(self.request_list, v)
        end
    end
end

function NewFestivalCollectCardWGData:SetNewJRCollectCardFriendInfo(protocol)
    self.friend_list = protocol.friend_list
end

function NewFestivalCollectCardWGData:SetNewJRCollectCardUpdateInfo(protocol)
    for k, v in pairs(self.friend_list) do
        if v.uuid == protocol.update_uuid then
            v.name = protocol.update_name
            v.be_time = protocol.update_be_time                                         -- 今日被赠送次数
            v.flag_list[protocol.update_request_item_id] = protocol.update_flag         -- 请求道具的状态信息
        end
    end
end

function NewFestivalCollectCardWGData:GetTimes()
    return self.times
end

function NewFestivalCollectCardWGData:GetGainTimes()
    return self.gain_times
end

function NewFestivalCollectCardWGData:GetRequestList()
    return self.request_list
end

function NewFestivalCollectCardWGData:GetFriendList()
    return self.friend_list
end

function NewFestivalCollectCardWGData:GetHasRequestByItemId(item_id)
    for k, v in pairs(self.request_list) do
        if item_id == v.item_id then
            return true
        end
    end

    return false
end

function NewFestivalCollectCardWGData:GetRedemptionNum()
    return self.redemption_num
end

function NewFestivalCollectCardWGData:ResetRequestList()
    self.request_list = {}
end

-- 协议相关end

function NewFestivalCollectCardWGData:GetCurActivityCfg()
    return self.activity_param_cfg[self.cur_grade] or {}
end

function NewFestivalCollectCardWGData:GetCurActivityOpenParamCfg()
    return self.config_param_cfg[self.cur_grade] or {}
end

function NewFestivalCollectCardWGData:GetCurGradeItemList()
    if self.item_list then
        return self.item_list
    end

    local cfg = self:GetCurActivityCfg()
    local item_list = {}
    if IsEmptyTable(cfg) then
        return item_list
    end

    local item_str_list = Split(cfg.item_list, "|")
    for k, v in pairs(item_str_list) do
        table.insert(item_list, tonumber(v))
    end

    if #item_list > 0 then
        self.item_list = item_list
    end

    return item_list
end

function NewFestivalCollectCardWGData:IsNFACollectCardItem(item_id)
    local item_list = self:GetCurGradeItemList()
    for k, v in pairs(item_list) do
        if v == item_id then
            return true
        end
    end

    return false
end

function NewFestivalCollectCardWGData:GetItemKindSum()
    local item_list = self:GetCurGradeItemList()
    local kind_sum = 0
    if IsEmptyTable(item_list) then
        return kind_sum
    end

    for k, v in ipairs(item_list) do
        local item_num = ItemWGData.Instance:GetItemNumInBagById(v)
        if item_num > 0 then
            kind_sum = kind_sum + 1
        end
    end

    return kind_sum
end