TianShenHuamoSkillView = TianShenHuamoSkillView or BaseClass(SafeBaseView)

function TianShenHuamoSkillView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "TianShenHuamoSkillView"
    local bundle_name = "uis/view/tianshen_huamo_prefab"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1026, 638)})
    self:AddViewResource(0, bundle_name, "layout_tianshen_hua_mo_skill")

    self.tianshen_id = nil
    self.demon_level = nil
end

function TianShenHuamoSkillView:__delete()
end

function TianShenHuamoSkillView:OpenCallBack()
end

function TianShenHuamoSkillView:CloseCallBack()
    self.tianshen_id = nil
    self.demon_level = nil
end

function TianShenHuamoSkillView:LoadCallBack()
    self.skills = {}
    self.rumo_skill_list = nil

    self.node_list.title_view_name.text.text = Language.TianShenHuaMo.SkillCustom
    local skill_pool = TianShenHuamoWGData.Instance:GetSkillPool(self.demon_level)

	if not self.rumo_skill_list then
		self.rumo_skill_list = AsyncListView.New(TianShenHuaMoChooseSkillItem, self.node_list.item_list_root)
		self.rumo_skill_list:SetSelectCallBack(BindTool.Bind(self.FlushHuaMoSkillChoose, self))
        self.rumo_skill_list:SetRefreshCallback(BindTool.Bind(self.RefreshSkillCell, self))
	end

    if skill_pool and #skill_pool > 0 then
        self.rumo_skill_list:SetDataList(skill_pool)
    end
    self:RegisterButtonListen()
    self:FlushButtonAndTips()
end

function TianShenHuamoSkillView:ReleaseCallBack()
    self.skills = nil
    self.tianshen_id = nil
    self.demon_level = nil

	if self.rumo_skill_list then
		self.rumo_skill_list:DeleteMe()
		self.rumo_skill_list = nil
	end
end

function TianShenHuamoSkillView:SetTianshenHuaMoData(tianshen_id, demon_level)
    self.tianshen_id = tianshen_id
    self.demon_level = demon_level
end

---注册按钮方法
function TianShenHuamoSkillView:RegisterButtonListen()
    --退出按钮
    self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list["one_key_btn"].button:AddClickListener(BindTool.Bind(self.ChooseOk, self))
end

---发送选取的协议
function TianShenHuamoSkillView:ChooseOk()
    if (not self.skills) or #self.skills <= 0 or #self.skills ~= 5 then
        return 
    end

    if (not self.tianshen_id) or (not self.demon_level) then
        return 
    end

    TianShenHuamoWGCtrl:CSTianShenRuMoSkillChoose(self.tianshen_id, self.demon_level, self.skills)
    self:Close()
end


function TianShenHuamoSkillView:FlushHuaMoSkillChoose(cell, cell_index, is_default, is_click)
	-- print_error("cell_index, is_default, is_click, data.index =", cell_index, is_default, is_click, cell and cell:GetData().skill_id)
	if cell == nil or is_default then
		return
	end

	local data = cell:GetData()

    if data and data.skill_type then
        if self:CheckSelectSkill(data.skill_type) then
            self:RemoveSelectSkill(data.skill_type)
        else
            if #self.skills >= 5 then
                -- SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenHuaMo.SkillFull)
                return
            else
                self:AddSelectSkill(data.skill_type)
            end
        end
        self:SetTsSkillSelect()
    end

    self:FlushButtonAndTips()
end

function TianShenHuamoSkillView:RefreshSkillCell(cell)
	local data = cell:GetData()
    if not data then
        return
    end
    cell:RefreshStatus(self:CheckSelectSkill(data.skill_type))
end



function TianShenHuamoSkillView:SetTsSkillSelect()
	if not self.rumo_skill_list then
		return
	end
	for k,cell in pairs(self.rumo_skill_list:GetAllItems()) do
        local data = cell:GetData()
        if data and data.skill_type then
            cell:SetSelect(self:CheckSelectSkill(data.skill_type))
        end
	end
end


function TianShenHuamoSkillView:CheckSelectSkill(var_skill_type)
    if (not self.skills) or #self.skills <= 0 then
        return false
    end
    for index, skill_type in ipairs(self.skills) do
        if skill_type == var_skill_type then
            return true
        end
    end
    return false
end

function TianShenHuamoSkillView:AddSelectSkill(skill_type)
    if not self.skills then
        self.skills = {}
    end
    table.insert(self.skills, skill_type)
end

function TianShenHuamoSkillView:RemoveSelectSkill(var_skill_type)
    if (not self.skills) or #self.skills <= 0 then
        return 
    end
    for index, skill_type in ipairs(self.skills) do
        if skill_type == var_skill_type then
            table.remove(self.skills, index)
            break
        end
    end
end

function TianShenHuamoSkillView:FlushButtonAndTips()
    local length = #self.skills
    XUI.SetGraphicGrey(self.node_list.one_key_btn, not (length == 5))
end

---------------------------------------------------TianShenHuaMoChooseSkillItem--------------------------------
TianShenHuaMoChooseSkillItem = TianShenHuaMoChooseSkillItem or BaseClass(BaseRender)

function TianShenHuaMoChooseSkillItem:__init()
end

function TianShenHuaMoChooseSkillItem:__delete()
end

function TianShenHuaMoChooseSkillItem:OnFlush()
	if self.data == nil then
		return
	end
    local bundle, asset = ResPath.GetSkillIconById(self.data.skill_icon)
	self.node_list.skill_icon:SetActive(bundle ~= nil and asset ~= nil)
    self.node_list.skill_icon.image:LoadSprite(bundle, asset)
    local temp_skill_des = string.format("%d%%", 0) 
    self.node_list.skill_desc.text.text = string.format(self.data.skill_describe, temp_skill_des) 
    self.node_list.skill_name.text.text = string.format("【%s】", self.data.skill_name) 
end

function TianShenHuaMoChooseSkillItem:RefreshStatus(is_select)
    self.node_list.select:SetActive(is_select)
end

function TianShenHuaMoChooseSkillItem:SetSelect(is_select)
    self.node_list.select:SetActive(is_select)
end