-- 购买战令等级界面
LongYunBuyZhanLingLevelView = LongYunBuyZhanLingLevelView or BaseClass(SafeBaseView)

function LongYunBuyZhanLingLevelView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/longyun_zhanling_ui_prefab", "layout_longyun_buy_zhanling_level_view")
end

function LongYunBuyZhanLingLevelView:LoadCallBack()
	if not self.zhanling_level_list then
		self.zhanling_level_list = AsyncListView.New(LYZhanLingLevelCellItemRender, self.node_list.zhanling_level_list)
	end
end

function LongYunBuyZhanLingLevelView:ReleaseCallBack()
	if self.zhanling_level_list then
		self.zhanling_level_list:DeleteMe()
		self.zhanling_level_list = nil
	end
end

function LongYunBuyZhanLingLevelView:OnFlush()
	local zhanling_exp = LongYunZhanLingWGData.Instance:GetLongYunZhanLingDevote()
	local max_level_cfg = LongYunZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()

	if IsEmptyTable(max_level_cfg) then
		return
	end

	self.node_list.cur_score.text.text = string.format(Language.LongYunZhanLing.BuyZhanLingLevelCurExp, zhanling_exp)
	local target_exp = max_level_cfg.need_devote - zhanling_exp
	target_exp = target_exp > 0 and target_exp or 0
	self.node_list.need_score.text.text = string.format(Language.LongYunZhanLing.BuyZhanLingLevelToMax, target_exp)

	local level_list = LongYunZhanLingWGData.Instance:GetZhanLingBuyLevelCfg()
	self.zhanling_level_list:SetDataList(level_list)
end

---------------------------------------LYZhanLingLevelCellItemRender--------------------------------------------------
LYZhanLingLevelCellItemRender = LYZhanLingLevelCellItemRender or BaseClass(BaseRender)
function LYZhanLingLevelCellItemRender:__init()
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
	XUI.AddClickEventListener(self.node_list["cell_node_btn"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
end

function LYZhanLingLevelCellItemRender:ReleaseCallBack()
	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end
end

function LYZhanLingLevelCellItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.add_level_str.text.text = string.format(Language.LongYunZhanLing.ZhanLingBuyLevel, self.data.devote)
	local price = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
	self.node_list.now_price_text.text.text = string.format(Language.LongYunZhanLing.ZhanLingBuyLevelBtn, price)

	local cur_devote = LongYunZhanLingWGData.Instance:GetLongYunZhanLingDevote()
	local target_level = LongYunZhanLingWGData.Instance:GetZhanLingLevelByDevote(cur_devote + self.data.devote)
	local cur_zhanling_level = LongYunZhanLingWGData.Instance:GetZhanLingLevel()
	local can_up_level = target_level - cur_zhanling_level
	self.node_list.buy_desc_1.text.text = can_up_level > 0 and
		string.format(Language.LongYunZhanLing.BuyZhanLingLevelCanUpLevel, can_up_level) or ""

	local devote_return = self.data.devote_return
	self.node_list.buy_desc_2.text.text = devote_return > 0 and
		string.format(Language.LongYunZhanLing.BuyZhanLingLevelBackLingYu, devote_return) or ""
end

function LYZhanLingLevelCellItemRender:OnClickBuyLevelBtn()
	if IsEmptyTable(self.data) then
		return
	end

	local zhanling_exp = LongYunZhanLingWGData.Instance:GetLongYunZhanLingDevote()
	local max_level_cfg = LongYunZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()

	if IsEmptyTable(max_level_cfg) then
		return
	end

	local target_exp = max_level_cfg.need_devote - zhanling_exp
	target_exp = target_exp > 0 and target_exp or 0

	if self.data.devote > target_exp then
		if not self.alert then
			self.alert = Alert.New()
		end

		self.alert:ClearCheckHook()
		self.alert:SetShowCheckBox(true, "ly_zhanling_level_cell")
		self.alert:SetCheckBoxDefaultSelect(false)

		local func = function()
			RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
		end

		local str = string.format(Language.LongYunZhanLing.BuyZhanLingLevelYiChu, target_exp)

		self.alert:SetLableString(str)
		self.alert:SetOkFunc(func)
		self.alert:Open()
	else
		RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
	end
end
