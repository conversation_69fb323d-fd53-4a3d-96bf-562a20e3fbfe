-----------------投资计划----------------------

local touziplan_plan_type = 0
local touziplan_plan_grade = 0

function RechargeView:InitTouZiPlanPanel()
	self.touzi_plan_item_list = AsyncListView.New(TouZiPlanItem, self.node_list.touzi_plan_item_root)

	XUI.AddClickEventListener(self.node_list.buy_touzi_plan_btn, BindTool.Bind(self.OnClickBuyTouZiPlanBtn, self))

	self.touziplan_role_level_change = BindTool.Bind1(self.TouZiPlanRoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.touziplan_role_level_change, {"level"})

	--list的箭头显示.2025.2.12策划又不要了.
	-- self.node_list.touzi_plan_item_root.scroller.scrollerEndScrolled = BindTool.Bind(self.TouZiScrollerEndScrolled, self)

	self:InitTouZiPlanParam()
	self:InitTouZiPlanGradeList()
	self:InitTouZiPlanToggleList()
	self:ShowTouZiPlanPanel()
end

function RechargeView:DeleteTouZiPlan()
	if self.touziplan_toggle_list then
		for k,v in pairs(self.touziplan_toggle_list) do
			v:DeleteMe()
		end
		self.touziplan_toggle_list = nil
	end
	if self.touziplan_grade_list then
		for k,v in pairs(self.touziplan_grade_list) do
			v:DeleteMe()
		end
		self.touziplan_grade_list = nil
	end
	if self.touzi_plan_item_list then
		self.touzi_plan_item_list:DeleteMe()
		self.touzi_plan_item_list = nil
	end
	if self.touziplan_role_level_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.touziplan_role_level_change)
		self.touziplan_role_level_change = nil
	end
end

--list的箭头显示.
function RechargeView:TouZiScrollerEndScrolled()
	local val = self.node_list.touzi_plan_item_root.scroll_rect.horizontalNormalizedPosition
	self.node_list.touzi_plan_l_arrow:SetActive(val ~= 0 and val > 0.1)
	self.node_list.touzi_plan_r_arrow:SetActive(val ~= 0 and val < 0.9)
end

function RechargeView:InitTouZiPlanParam()
	touziplan_plan_type = 0
	touziplan_plan_grade = 1
	self.select_plan_type = nil
	self.touziplan_title_id = 0
end

function RechargeView:InitTouZiPlanToggleList()
	local res_async_loader = AllocResAsyncLoader(self, "touzi_plan_toggle_item")
	res_async_loader:Load("uis/view/recharge_ui_prefab", "touzi_plan_toggle_item", nil,
		function(new_obj)
			local toggle_root = self.node_list.touzi_plan_toggle_root.transform
			local toggle_group = self.node_list.touzi_plan_toggle_root.toggle_group
			local touziplan_toggle_list = {}
			for i=1,MAX_PLAN_COUNT do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(toggle_root, false)
				local toggle_item = TouZiPlanToggleItem.New(obj)
				toggle_item:SetIndex(i)
				toggle_item:SetToggleGroup(toggle_group)
				toggle_item:AddClickEventListener(BindTool.Bind(self.OnClickTouZiPlanToggle, self), true)
				touziplan_toggle_list[i] = toggle_item
			end
			self.touziplan_toggle_list = touziplan_toggle_list
			self:FlushTouZiPlanToggleList()
			self:SelectNormalToggleIndex()
		end)
end

function RechargeView:InitTouZiPlanGradeList()
	local grade_root = self.node_list.touzi_plan_grade_root
	local touziplan_grade_list = {}
	for i=1,MAX_PLAN_COUNT do
		local grade_item = TouZiPlanGradeItem.New(grade_root:FindObj("touzi_plan_grade_item_" .. i))
		grade_item:SetIndex(i)
		grade_item:AddClickEventListener(BindTool.Bind(self.OnClickTouZiPlanGrade, self), true)
		touziplan_grade_list[i] = grade_item
	end
	self.touziplan_grade_list = touziplan_grade_list
end

function RechargeView:OnClickTouZiPlanToggle(item)
	local data = item:GetData()
	touziplan_plan_type = data.plan_type
	touziplan_plan_grade = 3
	self:FlushTouZiPlanGradeList()
end

function RechargeView:OnClickTouZiPlanGrade(item)
	touziplan_plan_grade = item:GetIndex()
	self:FlushTouZiPlanItemList(true)
	self:FlushTouZiPlanBanner()
	self:FlushAllPriceTxt()
end

function RechargeView:OnClickBuyTouZiPlanBtn()
	RechargeWGCtrl.Instance:BuyTouZiPlan(touziplan_plan_type, touziplan_plan_grade)
end

function RechargeView:TouZiPlanRoleLevelChange()
	self:FlushTouZiPlanToggleList()
	self:FlushTouZiPlanItemList()
end

function RechargeView:ShowTouZiPlanPanel()
	self.select_plan_type = nil
	if RechargeWGData.Instance:IsShowTouZiPlanFakeRedPoint() then
		RechargeWGData.Instance:SetTouZiPlanFakeRedPoint()
		RemindManager.Instance:Fire(RemindName.Vip_TQTZ)
		RechargeWGCtrl.Instance:Flush(TabIndex.recharge_tqtz)
	end
	self:SelectNormalToggleIndex()
end

function RechargeView:FlushTouZiPlanPanel(param_t)
	if param_t then
		for k,v in pairs(param_t) do
			if v.plan_type then
				self.select_plan_type = v.plan_type
				break
			end
		end
		self:SelectNormalToggleIndex()
	end
	self:FlushTouZiPlanToggleRemind()
	self:FlushTouZiPlanGradeList()
	self:FlushTouZiPlanItemList()
	self:FlushTouZiPlanBanner()
	self:FlushAllPriceTxt()
end

function RechargeView:FlushAllPriceTxt()
	local data_list = RechargeWGData.Instance:GetTouZiPlanRewardCfgList(touziplan_plan_type)
	local all_price = 0
	for k, v in pairs(data_list) do
		all_price = all_price + tonumber(v.reward_gold_bind[3])
	end
	self.node_list.all_get_money.text.text = all_price
end

function RechargeView:FlushTouZiPlanToggleList()
	local cfg_list = RechargeWGData.Instance:GetTouZiPlanCfgList()
	if not cfg_list or not self.touziplan_toggle_list then
		return
	end
	local role_level = RoleWGData.Instance:GetAttr('level')
	local data_list = {}
	local cfg_data = nil
	for i=0,#cfg_list do
		cfg_data = cfg_list[i][1]
		if role_level >= cfg_data.min_level and role_level <= cfg_data.max_level then
			data_list[#data_list + 1] = {name = cfg_data.show_name, plan_type = cfg_data.type, max_plan_grade = #cfg_list[i]}
		end
	end
	local touziplan_toggle_list = self.touziplan_toggle_list
	for i=1,#touziplan_toggle_list do
		if data_list[i] then
			touziplan_toggle_list[i]:SetData(data_list[i])
			touziplan_toggle_list[i]:SetVisible(true)
		else
			touziplan_toggle_list[i]:SetVisible(false)
		end
	end
end

function RechargeView:SelectNormalToggleIndex()
	local index = 0
	local touziplan_toggle_list = self.touziplan_toggle_list
	if self.select_plan_type then
		index = self.select_plan_type + 1
	else
		for i=1,MAX_PLAN_COUNT do
			if RechargeWGData.Instance:GetTouZiPlanRedPoint(i - 1) then
				index = i
				break
			end
		end
		if index == 0 then
			local ser_data = nil
			for i=1,MAX_PLAN_COUNT do
				ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(i - 1)
				if ser_data and ser_data.cur_grade > 0 then
					if not RechargeWGData.Instance:TouZiPlanIsFinish(i - 1) then
						index = i
						break
					end
				end
			end
		end
	end
	index = math.max(index, 1)
	if touziplan_toggle_list and touziplan_toggle_list[index] then
		touziplan_toggle_list[index]:SetSelect(true)
	end
end

function RechargeView:FlushTouZiPlanGradeList()
	local touziplan_grade_list = self.touziplan_grade_list
	if not touziplan_grade_list then
		return
	end
	local cfg_list = RechargeWGData.Instance:GetTouZiPlanCfgList(touziplan_plan_type)
	local ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(touziplan_plan_type)
	local cur_grade = ser_data and ser_data.cur_grade or 0
	local max_grade = cfg_list and #cfg_list or 0
	local grade_index = touziplan_plan_grade
	if grade_index < 1 then
		grade_index = 1
	elseif grade_index > max_grade then
		grade_index = max_grade
	elseif grade_index < cur_grade then
		grade_index = cur_grade
	end
	for i=1,#touziplan_grade_list do
		if i <= max_grade and i >= cur_grade then
			local temp_data = {name = "", multiples = 0}
			temp_data.name = cfg_list[i].btn_res
			temp_data.multiples = cfg_list[i].show_mult or 0
			touziplan_grade_list[i]:SetData(temp_data)
		else
			touziplan_grade_list[i]:SetVisible(false)
		end
		touziplan_grade_list[i]:SetVisible(i == grade_index)
		touziplan_grade_list[i]:SetSelect(i == grade_index)
	end
	self:OnClickTouZiPlanGrade(touziplan_grade_list[grade_index])

	local bundle, asset = ResPath.GetRawImagesPNG("a3_vip_czjj_txt" .. touziplan_plan_type)
    self.node_list.zhizun_title_img.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.zhizun_title_img.raw_image:SetNativeSize()
    end)
end

function RechargeView:FlushTouZiPlanToggleRemind()
	local touziplan_toggle_list = self.touziplan_toggle_list
	if touziplan_toggle_list then
		for i=1,#touziplan_toggle_list do
			touziplan_toggle_list[i]:FlushRemind()
		end
	end
end

function RechargeView:FlushTouZiPlanItemList(need_top)
	local data_list = RechargeWGData.Instance:GetTouZiPlanRewardCfgList(touziplan_plan_type)
	local ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(touziplan_plan_type)
	if ser_data.cur_grade == touziplan_plan_grade then
		local cur_fetch_reward_flag = ser_data and ser_data.cur_fetch_reward_flag or {}
		local temp_list = {}
		for i,data in ipairs(data_list) do
			if cur_fetch_reward_flag[data.seq] == 1 then
				temp_list[i + 100] = data
			else
				temp_list[i] = data
			end
		end
		temp_list = SortTableKey(temp_list)
		self.touzi_plan_item_list:SetDataList(temp_list)
	else
		self.touzi_plan_item_list:SetDataList(data_list)
	end
	if need_top then
		self.touzi_plan_item_list:JumpToTop()
	end
end

function RechargeView:FlushTouZiPlanBanner()
	local cfg_data = RechargeWGData.Instance:GetTouZiPlanCfgList(touziplan_plan_type, touziplan_plan_grade)
	if not cfg_data then
		return
	end

	local bundle, asset = ResPath.GetNoPackPNG(cfg_data.title_res)
	self.node_list.touzi_adverts_img.image:LoadSprite(bundle, asset, function()
		self.node_list.touzi_adverts_img.image:SetNativeSize()
	end)

	local bundle, asset = ResPath.GetVipImage("a3_vip_czjj_img_" .. touziplan_plan_grade)
	self.node_list.touzi_adverts_grade_img.image:LoadSprite(bundle, asset, function()
		self.node_list.touzi_adverts_grade_img.image:SetNativeSize()
	end)

	self:FlushTouZiPlanBuyBtn(cfg_data)
end

function RechargeView:FlushTouZiPlanBuyBtn(cfg_data)
	local cfg_list = RechargeWGData.Instance:GetTouZiPlanCfgList(touziplan_plan_type)
	local ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(touziplan_plan_type)
	local cur_grade = ser_data and ser_data.cur_grade or 0
	local max_grade = cfg_list and #cfg_list or 0
	local old_cfg_data = cfg_list and cfg_list[cur_grade]
	local old_const = old_cfg_data and old_cfg_data.price or 0
	if cur_grade >= touziplan_plan_grade then
		self.node_list.buy_touzi_plan_lbl.text.text = Language.Recharge.TQTZYiTouZi
	else
		local price = cfg_data.price - old_const
		if cfg_data.money_type == 1 then
			price = RoleWGData.GetPayMoneyStr(price)
		end
		self.node_list.buy_touzi_plan_lbl.text.text = string.format(Language.Recharge.TouZiNeedNum, price)
	end

	self.node_list.buy_touzi_plan_icon:SetActive(cfg_data.money_type == 2)
	self.node_list.yi_touzi_img:SetActive(cur_grade >= touziplan_plan_grade)
	self.node_list.buy_touzi_plan_btn:SetActive(cur_grade < touziplan_plan_grade)
end

-- 有称号那一档位
function RechargeView:GetTouZiPlanHasTitleGradeCfg()
	local cfg_data_list = RechargeWGData.Instance:GetTouZiPlanCfgList(touziplan_plan_type)
	for i=1,#cfg_data_list do
		if cfg_data_list[i].active_title_id > 0 then
			return cfg_data_list[i]
		end
	end
end


------------------------------------toggle_item--------------------------------------------

TouZiPlanToggleItem = TouZiPlanToggleItem or BaseClass(BaseRender)

function TouZiPlanToggleItem:OnSelectChange(is_select)
	local view = self:GetView()
	if view then
		view.toggle.isOn = is_select
	end
end

function TouZiPlanToggleItem:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end
	self.node_list.name.text.text = data.name
	self.node_list.name_hl.text.text = data.name
	self:FlushRemind()
end

function TouZiPlanToggleItem:FlushRemind()
	local data = self:GetData()
	if not data then
		return
	end
	local is_remind = RechargeWGData.Instance:GetTouZiPlanRedPoint(data.plan_type)
	self.node_list.red_point:SetActive(is_remind)
end

--------------------------------------------------------------------------------

TouZiPlanGradeItem = TouZiPlanGradeItem or BaseClass(BaseRender)

function TouZiPlanGradeItem:OnSelectChange(is_select)
	local view = self:GetView()
	if view then
		view.toggle.isOn = is_select
	end
end

function TouZiPlanGradeItem:OnFlush()
	local data = self:GetData()

	local bundle, asset = ResPath.GetVipImage(data.name)
    self.node_list.name.image:LoadSprite(bundle, asset, function()
        self.node_list.name.image:SetNativeSize()
    end)

	self:FlushRemind()
end

function TouZiPlanGradeItem:FlushRemind()
	local index = self:GetIndex()
	local is_remind = RechargeWGData.Instance:GetTouZiPlanRedPoint(touziplan_plan_type, index)
	self.node_list.red_point:SetActive(is_remind)
end

--------------------------------------------------------------------------------

TouZiPlanItem = TouZiPlanItem or BaseClass(BaseRender)

function TouZiPlanItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TouZiPlanItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.cell_root)
	XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind(self.OnClickGetRewardBtn, self))
end

function TouZiPlanItem:OnFlush()
	local data = self:GetData()
	self.item_cell:SetData(data.reward_item[0])

	local reward_info = RechargeWGData.Instance:GetTouZiPlanRewardInfo(data.type, data.seq)
	self:FlushBtnStatus(data, reward_info)
	self:FlushRewardDesc(data, reward_info)
end

function TouZiPlanItem:FlushBtnStatus(data, reward_info)
	local can_get = false
	local is_get = false
	if reward_info.cur_grade == touziplan_plan_grade then
		is_get = reward_info.is_get
		can_get = not reward_info.is_get and RoleWGData.Instance:GetRoleLevel() >= data.need_level
	end
	self.node_list.Img_YLQ:SetActive(is_get)
	self.node_list.red_point:SetActive(can_get)

	local btn_receive_state = can_get or data.seq == 0
	local btn_str = btn_receive_state and Language.Vip.Recive or Language.Vip.NotActive
	local need_lev = data.need_level
	local cur_role_level = RoleWGData.Instance:GetRoleLevel()

	local cur_level_str = RoleWGData.GetLevelStringImg(cur_role_level)
	local need_level_str = RoleWGData.GetLevelStringImg(need_lev)

	if cur_role_level >= need_lev then
		cur_level_str = ToColorStr(cur_level_str, COLOR3B.L_GREEN)
	else
		cur_level_str = ToColorStr(cur_level_str, COLOR3B.L_RED)
	end

	self.node_list.reward_desc.text.text = cur_level_str.."/"..need_level_str
	self.node_list.text_receive.text.text = btn_str
	self.node_list.up_lv_text.text.text = string.format(Language.Recharge.TouZiUpLevel, need_level_str)
	self.node_list.btn_receive:SetActive(btn_receive_state and not is_get)
	self.node_list.Img_WJH:SetActive(not btn_receive_state and not is_get)

	local ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(touziplan_plan_type)
	local cur_grade = ser_data and ser_data.cur_grade or 0
	self.node_list.lifan_img:SetActive(data.seq == 0 and cur_grade < touziplan_plan_grade)
end

function TouZiPlanItem:FlushRewardDesc(data, reward_info)
	local now_gold = tonumber(data.reward_gold_bind[touziplan_plan_grade]) or 0
	local cur_gold = tonumber(data.reward_gold_bind[reward_info.cur_grade]) or 0
	local old_gold = tonumber(data.reward_gold_bind[reward_info.old_grade]) or 0

	local is_buchang = old_gold > 0 or (reward_info.is_get and now_gold > cur_gold)

	if now_gold > cur_gold and reward_info.is_get then
		old_gold = cur_gold
	end 
 	local is_next_grade = not is_buchang and now_gold > cur_gold and cur_gold > 0
	self.node_list.bind_gold_now_num.text.text = is_next_grade and cur_gold - old_gold or now_gold - old_gold
end

function TouZiPlanItem:OnClickGetRewardBtn()
	local ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(touziplan_plan_type)
	if ser_data and ser_data.cur_grade < touziplan_plan_grade then
		RechargeWGCtrl.Instance:BuyTouZiPlan(touziplan_plan_type, touziplan_plan_grade)
		return
	end
	local data = self:GetData()
	if data then
		ServerActivityWGCtrl.Instance:SetFetchTouZiJiHuaReward(data.type, data.seq)
	end
end