ScrollGrid = ScrollGrid or BaseClass()
function ScrollGrid:__init()
	self.grid = nil									-- 网格根节点
	self.grid_name = ""								-- 格子名称
	self.page_index = 1								-- 当前页（1~n）
	self.page_cell_count = 0						-- 一页的格子数
	self.cur_cell = nil								-- 当前选中的格子
	self.cells = {}									-- 格子列表
	self.cell_data_list = {}						-- 格子数据列表
	self.max_cell_index = 0							-- 最大索引
	self.select_callback = nil						-- 选中某个格子回调
	self.page_change_callback = nil					-- 翻页回调
	self.radio_btn = nil							-- 关联的RadioButton控件
	self.create_index = -1							-- 创建到第几个
	self.unit_count = -1 							-- 单次创建数量
	self.create_quest = nil							-- 定时器
	self.flush_index = -1 							-- 刷新到第几个
	self.flush_quest = nil							-- 刷新定时器
	self.flush_count = -1 							-- 单次刷新数量

	self.item_render = nil							-- 创建的item类型
	self.ui_config = nil
	self.pos_list = nil								-- 位置列表
	self.is_show_tips = nil							-- 是否显示tips
	self.is_center = false							-- 是否居中
	self.skin_style = nil							-- 风格
	self.create_callback = nil						-- 创建完成回调

	self.is_set_open_count = false					-- 是否设置了开启格子数
	self.max_open_index = 0							-- 已开启的最大格子索引（物品格子专用）
	self.is_change_page = false 					-- 是否正在翻页中

	self.is_multi_select = false 					-- 是否多选
	self.is_can_drag_cell = false 					-- 格子数据是否可拖动

	self.is_use_step_calc = true 					-- 默认使用分步计算
	self.use_steppool_group = 0 					-- 分步计算组

	self.is_use_page_flush = false					-- 是否使用页刷新方式


	self.cell_list = {}
	self.first_time_load = true						-- 是否第一次加载
	self.grid_render_callback = nil 				-- grid_render回调
	self.select_tab = {}
	self.start_zero = true
end

function ScrollGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}

	-- if nil ~= self.radio_btn then
	-- 	self.radio_btn:DeleteMe()
	-- 	self.radio_btn = nil
	-- end

	-- self:EndCreate()
	-- self:EndFlush()
end


-- 当面板显示全部item才用
function ScrollGrid:GetAllCell()
	return self.cell_list
end


-- 当面板显示全部item才用
function ScrollGrid:GetCell(row)
	for k,v in pairs(self.cell_list) do
		if v.view.gameObject.activeInHierarchy and v:GetRow() == row then
			return v
		end
	end
end

-- callback(cell)
function ScrollGrid:SetSelectCallBack(callback)
	self.grid_render_callback = callback
	for i, v in pairs(self.cell_list) do
		v:SetGridCallBack(self.grid_render_callback)
	end
end

function ScrollGrid:CreateCells(t)
	local row = math.ceil(t.cell_count / t.col)
	self.columns = t.col

	local function get_length_tab(length)
		local length_tab = {}

		for i = 1, length do
			local tab = {}
			table.insert(length_tab, tab)
		end
		return length_tab
	end

	self.data_list = get_length_tab(row)
	self.select_tab = __TableCopy(self.data_list)

	self.item_render = t.itemRender
	self.list_view = t.list_view

	self:SetData(self.data_list)
end

-- 设置数据源
function ScrollGrid:SetData(data_list)
	if self.first_time_load then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
		list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self)
		self.first_time_load = false
	end
	self.list_view.scroller:ReloadData(0)
end

--刷新格子
function ScrollGrid:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	cell_index = cell_index + 1
	if not item_cell then
		item_cell = self.item_render.New(cell.gameObject)
		item_cell:SetColumns(self.columns)
		item_cell:CreateItemCells()
		item_cell:SetSelectTab(self.select_tab)
		item_cell:SetGridCallBack(self.grid_render_callback)
		item_cell:SetSelectEffectCallBack(BindTool.Bind(self.SetSelectEffect, self))
		self.cell_list[cell] = item_cell

		-- cell.gameObject:GetComponent(typeof(UnityEngine.UI.Button)):AddClickListener(
		-- 	BindTool.Bind(self.ListEventCallback, self, item_cell))
	end
	item_cell:SetRows(cell_index)
	item_cell:ClearAllDatas()
	item_cell:SetIndex(cell_index)
	item_cell:SetData(self.data_list[cell_index])
end

--获得格子数
function ScrollGrid:GetListViewNumbers()
	return #self.data_list
end

-- 0 刷新跳到顶部  1 刷新调到底部  2 刷新可视格子  3 刷新和重新加载listview
function ScrollGrid:SetDataList(data, refresh)
	self.origin_data = data
	for i, v in ipairs(self.data_list) do
		self.data_list[i] = {}
	end
	for i, v in pairs(data) do
		i = self.start_zero and (i + 1) or i
		local row = math.ceil(i / self.columns)
		local col = i % self.columns
		col = col == 0 and self.columns or col
		if not self.data_list[row] then
			self.data_list[row] = {}
		end
		self.data_list[row][col] = v
	end

	if refresh == 0 then
		self.list_view.scroller:ReloadData(0)
	elseif refresh == 1 then
		self.list_view.scroller:ReloadData(1)
	elseif refresh == 2 then
		self.list_view.scroller:RefreshActiveCellViews()
	elseif refresh == 3 then
		self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
	end
end

function ScrollGrid:SetIsMultiSelect(is_multi_select)
	self.is_multi_select = is_multi_select
end

function ScrollGrid:SetSelectEffect(row, col)
	if self.is_multi_select then
		if not self.select_tab[row][col] then
			self.select_tab[row][col] = true
		else
			self.select_tab[row][col] = not self.select_tab[row][col]
		end
	else
		for i, v in ipairs(self.data_list) do
			self.select_tab[i] = {}
		end
		self.select_tab[row][col] = true
	end
	self.list_view.scroller:RefreshActiveCellViews()
end

-- 数据开始下标是否0开始
function ScrollGrid:SetStartZeroIndex(is_start_zero)
	self.start_zero = is_start_zero
end

function ScrollGrid:GetDataByIndex(index)
	return self.origin_data[index]
end

function ScrollGrid:GetMultiSelectCell()
	local select_index = {}
	for k1, v1 in pairs(self.select_tab) do
		if self.select_tab[k1] then
			for k2, v2 in pairs(v1) do
				local index = (k1 - 1) * self.columns + k2
				table.insert(select_index, index)
			end
		end
	end
	return select_index
	-- local select_cells = {}
	-- for k, v in pairs(self.cells) do
	-- 	if v:IsSelect() then
	-- 		table.insert(select_cells, v)
	-- 	end
	-- end
	-- return select_cells
end