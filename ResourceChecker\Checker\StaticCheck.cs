﻿#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Nirvana;
using System;

public abstract class StaticCheck {
    private string[] assetPaths;
    private List<CheckObject> list = new List<CheckObject>();
    protected bool hasHighLevel;

    public void StartCheck(Action callBack)
    {
        this.list.Clear();
        this.assetPaths = this.GetAssets();
        this.CheckObj(0, callBack);
    }

    private void CheckObj(int index, Action callBack)
    {
        if (index < this.assetPaths.Length)
        {
            var assetPath = this.assetPaths[index];
            Check(assetPath, objects =>
            {
                if (null != objects && objects.Length > 0)
                {
                    for (int i = 0; i < objects.Length; ++i)
                    {
                        var obj = objects[i];
                        list.Add(obj);
                        hasHighLevel = obj.warningLevel == WarningLevel.High;
                    }
                    ShowWindow();
                }

                CheckObj(index + 1, callBack);
            });
        }
        else
            callBack();
    }

    protected virtual void ShowWindow()
    {
        if (this.list.Count <= 0)
            return;
        var window = this.GetWindow();
        window.Init();
        window.List = this.list;
        window.Show();
        window.position = new Rect(new Rect(Screen.width / 2, 600, 800, 600));
        window.Focus();
        window.onClose = () =>
        {
            Scheduler.Delay(ShowWindow, 1);
        };

        window.onLostFocus = () =>
        {
            if (hasHighLevel)
            {
                window.position = new Rect(new Rect(Screen.width / 2, 600, 800, 600));
            }
        };
    }

    protected abstract string[] GetAssets();

    protected abstract void Check(string assetPath, Action<CheckObject[]> action);

    protected abstract BaseWarningWindow GetWindow();

}
#endif