FuBenMingWenOpenView = FuBenMingWenOpenView or BaseClass(SafeBaseView)

function FuBenMingWenOpenView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true, true, nil, BindTool.Bind(self.OnClickConFirm, self))
	self.ui_config = {"uis/view/common_jiesuan_prefab", "FuBenMingWenOpenView"}
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_mingwen_open")
	self.delay_close_time = 4
	self.view_layer = UiLayer.Pop
end

function FuBenMingWenOpenView:__delete()
end

function FuBenMingWenOpenView:ReleaseCallBack()
	if self.close_delay then
		GlobalTimerQuest:CancelQuest(self.close_delay)
		self.close_delay = nil
	end

	-- if self.act_star_reward_cell then
	-- 	for i= 1, 4 do
	-- 		self.act_star_reward_cell[i]:DeleteMe()
	-- 	end
	-- 	self.act_star_reward_cell = nil
	-- end
end

function FuBenMingWenOpenView:LoadCallBack(index, loaded_times)
	self.star_img_list = {}

	for i = 1 , 4 do
		self.node_list["item_pos"..i].button:AddClickListener(BindTool.Bind(self.OnClickItem, self,i))
	end
	self.node_list["btn_win_jixu"].button:AddClickListener(BindTool.Bind(self.OnClickConFirm, self))
	self.node_list.btn_container:SetActive(false)
	self.node_list.mScroll:SetActive(false)

	GlobalTimerQuest:AddDelayTimer(function()
		--self.node_list.reward_container:SetActive(true)
		self.node_list.btn_container:SetActive(true)
		self.node_list.mScroll:SetActive(true)
		UITween.AlpahShowPanel(self.node_list.btn_container.gameObject, true, 1.5)
		UITween.MoveShowPanel(self.node_list.mScroll.gameObject, Vector3(1000, 0), 0.5)
	end, 0.3)
end

function FuBenMingWenOpenView:ShowIndexCallBack(index)

end

function FuBenMingWenOpenView:OnClickItem(index)
	if self.data and self.data[index -1] then
		TipWGCtrl.Instance:OpenItem({item_id = self.data[index - 1].item_id})
	end
end

function FuBenMingWenOpenView:OnFlush()
	if IsEmptyTable(self.data) then
		self:OnClickConFirm()
		return
	end
	-- local bg_name = "a1_xiangqian_fuwendi"
	-- local open_slot = {}
	-- open_slot[3] = tonumber(self.slot_data) % 10
	-- open_slot[2] = math.floor(tonumber(self.slot_data) / 10) % 10
	-- open_slot[1] = math.floor(tonumber(self.slot_data) / 100) % 10
	for i = 0,3 do
		local index = i + 1
		if self.data[i] then
			--self.act_star_reward_cell[i+1]:SetData(self.data[i])
			local cfg = ItemWGData.Instance:GetItemConfig(self.data[i].item_id)
 			local bundle, asset = ResPath.GetItem(cfg.icon_id)
 			self.node_list["item_pos"..index].image:LoadSpriteAsync(bundle, asset,function ()
				self.node_list["item_pos"..index].image:SetNativeSize()
			end)

			self.node_list["ph_cell_" .. index]:SetActive(true)
			self.node_list["name_text"..index].text.text = ToColorStr(cfg.name ,ITEM_COLOR[cfg.color])
			self.node_list["attr_text"..index].text.text = cfg.description2
			if self.node_list["item_bg_"  .. index] then
				local bg_name = "a3_ty_wpk_" .. cfg.color
	 			local bg_bundle, bg_asset = ResPath.GetCommonImages(bg_name)
		 		self.node_list["item_bg_"  .. index].image:LoadSpriteAsync(bg_bundle, bg_asset,function ()
					self.node_list["item_bg_"  .. index].image:SetNativeSize()
				end)
 			end
		else
			self.node_list["ph_cell_" .. index]:SetActive(false)
			self.node_list["name_text"..index].text.text = ""
			self.node_list["attr_text"..index].text.text = ""
		end
	end

	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	self.node_list.text_cur_level.text.text = string.format(Language.FuBen.PassLevel, pass_level)
	local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(pass_level + 1)
	-- 还有下一层
	if cfg then
		self.node_list.next_level:SetActive(true)
		self.node_list.text_next_level.text.text = string.format(Language.FuBen.PassLevel, pass_level + 10) -- 每10层一大关
	else
		self.node_list.next_level:SetActive(false)
	end

	if nil == self.close_delay then
		self.delay_close_time = 4
		self.close_delay = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateButton, self), 1)
		self:UpdateButtonText()
	end

end

function FuBenMingWenOpenView:OnClickConFirm()
	if self.confirm_call_back then
		self.confirm_call_back()
		self.confirm_call_back = nil
	end

	self:Close()
end

function FuBenMingWenOpenView:SetShowData(data, slot_data)
	self.data = data or {}
	self.slot_data = slot_data or nil
end

function FuBenMingWenOpenView:SetConfirmCallBack(call_back)
	if call_back then
		self.confirm_call_back = call_back
	end
end

function FuBenMingWenOpenView:UpdateButton()
	self.delay_close_time = self.delay_close_time - 1
	if self.delay_close_time <= 0 then
		self:OnClickConFirm()
		if self.close_delay then
			GlobalTimerQuest:CancelQuest(self.close_delay)
			self.close_delay = nil
		end
	else
		self:UpdateButtonText()
	end
end

function FuBenMingWenOpenView:UpdateButtonText()
	self.node_list["conform_text"].text.text = Language.Common.Confirm1.."("..self.delay_close_time..")"
end
