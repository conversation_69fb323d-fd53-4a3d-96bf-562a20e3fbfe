--累充活动数据
NewFestivalRechargeWGData = NewFestivalRechargeWGData or BaseClass()
function NewFestivalRechargeWGData:__init()
    if NewFestivalRechargeWGData.Instance then
        error("[NewFestivalRechargeWGData] Attempt to create singleton twice!")
        return
    end

    NewFestivalRechargeWGData.Instance = self

    self.grade = 0
    self.recharge_sum = 0
    self.fetch_flag = {}
    self.save_model_list = {}

    self:InitCfg() --ui展示模型列表
    RemindManager.Instance:Register(RemindName.NewFestivalRecharge, BindTool.Bind(self.GetRechargeRemind, self))
end

function NewFestivalRechargeWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.NewFestivalRecharge)
    NewFestivalRechargeWGData.Instance = nil
end

function NewFestivalRechargeWGData:InitCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("na_recharge_sum_auto")              -- 获取累充活动配置表数据
    self.condition_award_cfg = ListToMap(cfg.condition_award, "grade", "seq")             -- 达成奖励条件  累充  100,1000,10000这样的
    self.model_cfg = ListToMap(cfg.model_display, "grade")                                -- 模型数据
end

---------------------------协议相关start----------------------------
function NewFestivalRechargeWGData:SetRechargeAllInfo(protocol)
    self.grade = protocol.grade
    self.recharge_sum = protocol.recharge_sum                     --累计充值数
    self.fetch_flag = bit:d2b_l2h(protocol.fetch_flag, nil, true) --领取状态
end

---------------------------协议相关end----------------------------
function NewFestivalRechargeWGData:GetRechargeRemind()
    if self:RewardRemindNow() then
        return 1
    end

    return 0
end


function NewFestivalRechargeWGData:RewardRemindNow()
    local condition_award_cfg = self.condition_award_cfg[self.grade]
    if not condition_award_cfg then
        return false
    end

    for k, v in pairs(condition_award_cfg) do
        local get_flag = self:GetRechargeState(v.seq)
        if not get_flag and self.recharge_sum >= v.condition then
            return true
        end
    end

    return false
end

-- 获取累充奖励列表
function NewFestivalRechargeWGData:GetRecharegeRewardCfg()
    return self:GetRewardList()
end

--奖励列表排序
function NewFestivalRechargeWGData:GetRewardList()
    local reward_list = {}
    local reward_cfg = self.condition_award_cfg[self.grade]
    if not reward_cfg then
        return reward_list
    end

    for k, v in pairs(reward_cfg) do
        table.insert(reward_list, v)
    end

    table.sort(reward_list, function(a, b)
        local cur_num = self:GetRechargeNum()         --当前充值数
        local a_sort = 200 + a.seq
        local a_is_get = self:GetRechargeState(a.seq) --是否已经领取
        if not a_is_get then
            local is_double = cur_num >= a.condition
            a_sort = is_double and a_sort - 100 * 2 or a_sort - 100
        end
        
        local b_sort = 200 + b.seq
        local b_is_get = self:GetRechargeState(b.seq)
        if not b_is_get then
            local is_double = cur_num >= b.condition
            b_sort = is_double and b_sort - 100 * 2 or b_sort - 100
        end

        return a_sort < b_sort
    end)

    return reward_list
end

function NewFestivalRechargeWGData:GetRechargeNum()
    return self.recharge_sum
end

--返回true是代表已领取
function NewFestivalRechargeWGData:GetRechargeState(seq)
    return ((self.fetch_flag or {})[seq] or 0) == 1
end

function NewFestivalRechargeWGData:GetModelData()
    if not self.save_model_list[self.grade] then
        local new_model_data = self:CreateModelData(self.grade)
        self.save_model_list[self.grade] = new_model_data
    end

    return self.save_model_list[self.grade]
end

function NewFestivalRechargeWGData:CreateModelData(cur_grade)
    local show_data = self.model_cfg[cur_grade]
    local new_mode_data = {}
    if IsEmptyTable(show_data) then
        return new_mode_data
    end

    local display_data = {}
    local model_show_itemid = show_data.model_show_itemid
    if model_show_itemid ~= 0 and model_show_itemid ~= "" then
        local split_list = Split(model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = model_show_itemid
        end
    end

    display_data.should_ani = true
    display_data.hide_model_block = false
    display_data.bundle_name = show_data.model_bundle_name
    display_data.asset_name = show_data.model_asset_name
    display_data.render_type = show_data.model_show_type - 1
    display_data.need_wp_tween = true

    if show_data.model_show_type == 1 and show_data.model_bundle_name and show_data.model_bundle_name ~= "" then
        display_data.need_wp_tween = false
    end

    if show_data.display_root_width and show_data.display_root_width ~= "" then
        display_data.display_root_width = show_data.display_root_width
    end

    if show_data.display_root_height and show_data.display_root_height ~= "" then
        display_data.display_root_height = show_data.display_root_height
    end

    local transform_info = {}
    local pos_x, pos_y, pos_z = 0, 0, 0
    if show_data.display_pos and show_data.display_pos ~= "" then
        local pos_list = Split(show_data.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
        pos_z = tonumber(pos_list[3]) or pos_z
    end
    transform_info.pos_x = pos_x
    transform_info.pos_y = pos_y
    transform_info.pos_z = pos_z

    if show_data.model_pos and show_data.model_pos ~= "" then
        local pos_list = string.split(show_data.model_pos, "|")
        local posx = tonumber(pos_list[1]) or 0
        local posy = tonumber(pos_list[2]) or 0
        local posz = tonumber(pos_list[3]) or 0

        display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)
    end

    local rot_x, rot_y, rot_z = 0, 0, 0
    if show_data.display_rotation and show_data.display_rotation ~= "" then
        local rot_list = Split(show_data.display_rotation, "|")
        rot_x = tonumber(rot_list[1]) or rot_x
        rot_y = tonumber(rot_list[2]) or rot_y
        rot_z = tonumber(rot_list[3]) or rot_z
    end

    transform_info.rot_x = rot_x
    transform_info.rot_y = rot_y
    transform_info.rot_z = rot_z
    display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)

    local scale = show_data.display_scale
    display_data.scale = (scale and scale ~= "" and scale > 0) and scale or 1
    display_data.model_adjust_root_local_scale = scale
    display_data.model_rt_type = ModelRTSCaleType.M

    new_mode_data = {}
    new_mode_data.display_data = display_data
    new_mode_data.transform_info = transform_info

    return new_mode_data
end
