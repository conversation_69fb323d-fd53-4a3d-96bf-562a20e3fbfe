TeamCallTogether= TeamCallTogether or BaseClass(SafeBaseView)

function TeamCallTogether:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	local assetbundle = "uis/view/new_team_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, assetbundle, "layout_team_call_together")
end

function TeamCallTogether:SetDataAndOpen(call_data)
	self.call_data = call_data
	if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function TeamCallTogether:ReleaseCallBack()
	self.call_data = nil
	self:ClearCountDown()
end

function TeamCallTogether:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_confirm"], BindTool.Bind1(self.OnCon<PERSON>rm<PERSON><PERSON><PERSON>, self))
	XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind1(self.On<PERSON><PERSON>l<PERSON><PERSON><PERSON>, self))

	self.count_down_time = 10

end

function TeamCallTogether:ShowIndexCallBack()
	self:Flush()
end

function TeamCallTogether:OnFlush()
	if not self.call_data then
		return
	end

	local map_name = Config_scenelist[self.call_data.scene_id] and Config_scenelist[self.call_data.scene_id].name or ""
	self.node_list.desc.text.text = string.format(Language.NewTeam.CallTogetherStr,map_name, self.call_data.pos_x, self.call_data.pos_y)
	self:ClearCountDown()
	if self.count_down_time and self.count_down_time > 0 then
		self.count_down = CountDown.Instance:AddCountDown(self.count_down_time, 1,BindTool.Bind(self.UpdateTime,self), BindTool.Bind(self.CompleteTime,self))
	else
		self.node_list.ok_text.text.text = self.ok_txt or ""
	end
end

function TeamCallTogether:UpdateTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if time > 0 then
		self.node_list.btn_text.text.text = string.format(Language.NewTeam.CallTogetherTime, time)
	end
end

function TeamCallTogether:CompleteTime()
	self:ClearCountDown()
	self:OnConfirmHandler()
	self:Close()
end

function TeamCallTogether:ClearCountDown()
	if CountDown.Instance:HasCountDown(self.count_down) then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function TeamCallTogether:OnCancelHandler()
	self:Close()
end

function TeamCallTogether:OnConfirmHandler()
	if not self.call_data then
		return
	end

	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
    end

    local main_role = Scene.Instance:GetMainRole()
    local scene_logic = Scene.Instance:GetSceneLogic()
    if main_role == nil or main_role:IsDeleted() then
    	return
    end

    if main_role:IsInXunYou() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
        return
    end

    local member_list = SocietyWGData.Instance:GetTeamMemberList()
    local leader_role_id
    for k, v in pairs(member_list) do
        if v.is_leader == 1 then
            leader_role_id = v.role_id
            break
        end
    end

    local sence_id = Scene.Instance:GetSceneId()
	if sence_id == self.call_data.scene_id then
        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
        GuajiWGCtrl.Instance:MoveToPos(sence_id, self.call_data.pos_x, self.call_data.pos_y, 1, nil, nil, nil, function ()
        	-- 隊長在視野範圍内
        	-- local leader_obj = Scene.Instance:GetRoleByOrginalId(leader_role_id)
        	-- if leader_obj then
        	-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        	-- end
			MainuiWGCtrl.Instance:OnClickFollowBtn()
        end, nil, CLIENT_MOVE_REASON.FOLLOW)
	else
		local map_name = Config_scenelist[self.call_data.scene_id] and Config_scenelist[self.call_data.scene_id].name or ""
    	TeamHandleClickPoint.CheckSceneIdOperate(self.call_data.scene_id, {map_name, self.call_data.scene_id, self.call_data.pos_x,
    									self.call_data.pos_y, function ()
											MainuiWGCtrl.Instance:OnClickFollowBtn()
										end})
	end
   	self:Close()
end
