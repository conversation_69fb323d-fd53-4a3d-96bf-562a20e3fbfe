require("game/operation_activity/mowu_jianglin/mowu_jianglin_wg_data")
require("game/operation_activity/mowu_jianglin/mowu_jianglin_fb_view")

MoWuJiangLinWGCtrl = MoWuJiangLinWGCtrl or BaseClass(BaseWGCtrl)
function MoWuJiangLinWGCtrl:__init()
	if MoWuJiangLinWGCtrl.Instance then
		ErrorLog("[MoWuJiangLinWGCtrl] Attemp to create a singleton twice !")
	end
	MoWuJiangLinWGCtrl.Instance = self
	self:RegisterAllProtocols()
	
	self.data = MoWuJiangLinWGData.New()
	self.mowu_jianglin_fb_view = MoWuJiangLinFBView.New()
	
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_mowu_jianglin_auto", BindTool.Bind(self.UpdataConfig, self))
end

function MoWuJiangLinWGCtrl:__delete()
	MoWuJiangLinWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.data = nil

	self.mowu_jianglin_fb_view:DeleteMe()
	self.mowu_jianglin_fb_view = nil
end

function MoWuJiangLinWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRandActMWJLInfo,'OnSCRandActMWJLInfo')
	self:RegisterProtocol(SCRAMoWuJiangLinFinishInfo,'OnSCRAMoWuJiangLinFinishInfo')
	self:RegisterProtocol(SCMoWuJiangLinDeadInfo,'OnSCMoWuJiangLinDeadInfo')
	
	self:RegisterProtocol(CSRandActMWJLInfoReq)
end

--魔物降临返回
function MoWuJiangLinWGCtrl:OnSCRandActMWJLInfo(protocol)
	self.data:SetMoWuJiangLInfo(protocol)
	RemindManager.Instance:Fire(RemindName.OperationMoWuJiangLin)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowu_jianglin)
end

-- 魔物降临结算
function MoWuJiangLinWGCtrl:OnSCRAMoWuJiangLinFinishInfo(protocol)
	self.data:SetMWJLFinishInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.MOWU_JIANLIN then
		ActivityWGCtrl.Instance:OpenActJiseSuanView(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
	end
end

function MoWuJiangLinWGCtrl:OnSCMoWuJiangLinDeadInfo(protocol)
	self.data:SetShiLianBossState(protocol.is_boss_dead)
	if self.mowu_jianglin_fb_view:IsOpen() then
		self.mowu_jianglin_fb_view:Flush("boss_state",protocol.is_boss_dead)
	end
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowu_jianglin)
	self:MWJLCountDown()
	RemindManager.Instance:Fire(RemindName.OperationMoWuJiangLin)
end

 --魔物降临请求
function MoWuJiangLinWGCtrl:RankActTSJLReq(type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActMWJLInfoReq)
	protocol.type = type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function MoWuJiangLinWGCtrl:UpdataConfig()
	self.data:UpdataConfig()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowu_jianglin)
end

function MoWuJiangLinWGCtrl:MWJLCountDown()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
	if act_info and (act_info.status == ACTIVITY_STATUS.STANDY or act_info.status == ACTIVITY_STATUS.OPEN) then
		if ActivityWGCtrl.Instance:CheckNowCountDownActType(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN) then
			return
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
		if not act_cfg then
			return
		end

		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level < act_cfg.level or role_level > act_cfg.level_max then
			return
		end

		local info_list = {}
		info_list.act_type = ACTIVITY_TYPE.CLIENT_MOWUJINGLIN
		info_list.calculate_time = 10
		info_list.timestamp = act_info.next_time
		info_list.image_name = act_info.status == ACTIVITY_STATUS.STANDY and "a1_bosslx_laix"
		info_list.is_auto_close = act_info.status == ACTIVITY_STATUS.OPEN
		ActivityWGCtrl.Instance:OpenActCountDown(info_list)
	else
		ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
	end
end

function MoWuJiangLinWGCtrl:GotoShiLian()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.MOWU_JIANLIN then
		return
	end

	local other_cfg = self.data:GetOtherCfg()
	if not other_cfg then
		return
	end

	RoleWGCtrl.Instance:SetJumpAlertCheck(other_cfg.npc_scene, function()
		GuajiWGCtrl.Instance:MoveToNpc(other_cfg.npcid, nil, other_cfg.npc_scene)
	end, true)
end

function MoWuJiangLinWGCtrl:CheckMoWuJianLinNpc(npc_id)
	local npcid = self.data:GetOtherCfg("npcid")
	if npcid ~= npc_id then
		return false
	end

	local status = self.data:GetMoWuJiangLinActivityStatus()
	if status == ACTIVITY_STATUS.STANDY then
		return true, Language.TianShenRoad.NPCTalkStr_1
	elseif status == ACTIVITY_STATUS.OPEN then
		return true, Language.OperationActivity.NPCTalkStr_2, BindTool.Bind(self.EnterMoWuJiangLinFb, self)
	end
end

function MoWuJiangLinWGCtrl:EnterMoWuJiangLinFb()
	local fb_type = 59
	FuBenWGCtrl.Instance:SendEnterFB(fb_type)
end

function MoWuJiangLinWGCtrl:OpenMWJLFbView()
	self.mowu_jianglin_fb_view:Open()
end

