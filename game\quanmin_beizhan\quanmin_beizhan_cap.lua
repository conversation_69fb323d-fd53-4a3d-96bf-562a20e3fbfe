function QuanMinBeiZhanView:InitCapView(index)
	self:CapTimeCountDown()
	XUI.AddClickEventListener(self.node_list.cap_tip, BindTool.Bind(self.OnCapBtnTipClickHnadler,self))

	local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_cap)
	self.node_list.cap_tip_label.text.text = theme_cfg.rule_tip

	if index == TabIndex.quanmin_beizhan_cap then
		self:InitRoleCap()
	elseif index == TabIndex.quanmin_beizhan_cap2 then
		self:InitServerCap()
	elseif index == TabIndex.quanmin_beizhan_cap3 then
		self:InitKanJia()
	end
end

function QuanMinBeiZhanView:ReleaseCapView()
	if self.cap_count_down and CountDownManager.Instance:HasCountDown(self.cap_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.cap_count_down)
	end

	self:ReleaseRoleCap()
	self:ReleaseRoleServerCap()
	self:ReleaseKanJia()
end

function QuanMinBeiZhanView:OnCapBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = QuanMinBeiZhanWGData.Instance:GetActivityTip(TabIndex.quanmin_beizhan_cap)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

--有效时间倒计时
function QuanMinBeiZhanView:CapTimeCountDown()
	self.cap_count_down = "cap_count_down"
	local invalid_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_cap)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.cap_time.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.cap_count_down, BindTool.Bind1(self.UpdateCapCountDown, self), BindTool.Bind1(self.OnCapComPlete, self), invalid_time, nil, 1)
	end
end

function QuanMinBeiZhanView:UpdateCapCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.cap_time.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function QuanMinBeiZhanView:OnCapComPlete()
	self.node_list.cap_time.text.text = Language.QuanMinBeiZhan.ActivityStr1
end