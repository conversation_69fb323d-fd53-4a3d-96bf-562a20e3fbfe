﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class JYCharacterShadowWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(JYCharacterShadow), typeof(CharacterShadow));
		<PERSON><PERSON>unction("Validate", Validate);
		<PERSON><PERSON>("OnDrawShadow", OnDrawShadow);
		<PERSON><PERSON>unction("Create", Create);
		L<PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("ActorRenders", get_ActorRenders, null);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Validate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			JYCharacterShadow obj = (JYCharacterShadow)ToLua.CheckObject<JYCharacterShadow>(L, 1);
			obj.Validate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrawShadow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			JYCharacterShadow obj = (JYCharacterShadow)ToLua.CheckObject<JYCharacterShadow>(L, 1);
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer> arg1 = (System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>)ToLua.CheckDelegate<System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>>(L, 3);
			obj.OnDrawShadow(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Create(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
				JYCharacterShadow o = JYCharacterShadow.Create(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2)
			{
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
				JYCharacterShadow o = JYCharacterShadow.Create(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: JYCharacterShadow.Create");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ActorRenders(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			JYCharacterShadow obj = (JYCharacterShadow)o;
			ActorRender[] ret = obj.ActorRenders;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ActorRenders on a nil value");
		}
	}
}

