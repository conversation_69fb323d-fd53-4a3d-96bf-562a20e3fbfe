ShiTuXiuLiView = ShiTuXiuLiView or BaseClass(SafeBaseView)

local MyStatusType = {
	None = 0,
	Teacher = 1,
	Student = 2,
}

local my_status = 0

function ShiTuXiuLiView:__init(view_name)
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/shituxiuli_prefab", "layout_act_bg")
	self:AddViewResource(0, "uis/view/shituxiuli_prefab", "layout_shituxiuli")
	self.toggle_index = 0
end

function ShiTuXiuLiView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitToggleList()
	self:InitListener()
	self:InitTitlePanel()
	self:InitStudentLoginItemList()
end

function ShiTuXiuLiView:ReleaseCallBack()
	if self.shitu_item then
		self.shitu_item:DeleteMe()
		self.shitu_item = nil
	end
	if self.teacher_reward_list then
		self.teacher_reward_list:DeleteMe()
		self.teacher_reward_list = nil
	end
	if self.student_apply_list then
		self.student_apply_list:DeleteMe()
		self.student_apply_list = nil
	end
	if self.student_task_list then
		self.student_task_list:DeleteMe()
		self.student_task_list = nil
	end
	if self.student_login_itemlist then
		for k,v in pairs(self.student_login_itemlist) do
			v:DeleteMe()
		end
		self.student_login_itemlist = nil
	end
	if self.role_vip_level_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_vip_level_change)
		self.role_vip_level_change = nil
	end

	CountDownManager.Instance:RemoveCountDown("shituxiuli_count_down")
	CountDownManager.Instance:RemoveCountDown("shituxiuli_baishi_cd")
	CountDownManager.Instance:RemoveCountDown("shituxiuli_shoutu_cd")
end

function ShiTuXiuLiView:OpenCallBack()
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_INFO)						-- 信息请求
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_WORLD_STUDENT_INFO)			-- 世界拜师信息
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_WORLD_SUCCESS_NODE_INFO)	-- 世界拜师成功信息
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_WORLD_TEACHER_INFO)

	if self.toggle_index ~= nil and self.toggle_index == MyStatusType.None then
		local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
		if base_info ~= nil and base_info.teacher_uid > 0 then
			self.toggle_index = MyStatusType.Student
		end
	end
end

function ShiTuXiuLiView:CloseCallBack()
	self.toggle_index = 0
	ShiTuXiuLiWGData.Instance:SetApplyStudentRemind(false)
	RemindManager.Instance:Fire(RemindName.ShiTuXiuLiMain)
end

function ShiTuXiuLiView:ShowIndexCallBack()
	
end

function ShiTuXiuLiView:SetShowDefenseIndex(index)
	self.toggle_index = index or self.toggle_index
end

function ShiTuXiuLiView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "base_info" then
			self:RefreshView()
		elseif k == "student_info" then
			self:RefreshBaiShiBtn()
			self:RefreshStudentApplyList()
		elseif k == "jiebai_info" then
			self:RefreshJieBaiRumor()
		elseif k == "task_info" then
			if self.toggle_index == MyStatusType.Student then
				self:RefreshStudentTaskList()
			end
			self:RefreshToggleRemind()
		elseif k == "new_teacher_msg" then
			self:RefreshRightPanel()
			self:RefreshRecruit()
			self:RefreshStudentApplyList()
		end
	end
end

function ShiTuXiuLiView:InitParam()
	self.toggle_list = {}
	self.student_login_itemlist = {}
	self.task_end_time = 0
end

function ShiTuXiuLiView:InitPanel()
	self.shitu_item = TeacherOrStudentItem.New(self.node_list.shitu_item_root)
	self.teacher_reward_list = AsyncListView.New(TeacherRewardItem, self.node_list.reward_list)
	self.student_task_list = AsyncListView.New(StudentTaskItem, self.node_list.task_list)
	self.student_apply_list = AsyncListView.New(StudentApplyItem, self.node_list.student_baishi_list)
end

function ShiTuXiuLiView:InitToggleList()
	local parent_root = self.node_list.toggle_list
	for i=1,2 do
		local toggle_item = ShiTuXiuLiToggleItem.New(parent_root:FindObj("toggle_" .. i))
		toggle_item:SetIndex(i)
		toggle_item:AddClickEventListener(BindTool.Bind(self.OnClickToggle, self, i), true)
		self.toggle_list[i] = toggle_item
	end
end

function ShiTuXiuLiView:InitListener()
	XUI.AddClickEventListener(self.node_list.close_btn, BindTool.Bind(self.Close, self))
	XUI.AddClickEventListener(self.node_list.tips_btn, BindTool.Bind(self.OnClickTipBtn, self))
	XUI.AddClickEventListener(self.node_list.title_click, BindTool.Bind(self.OnTitleClick, self))
	XUI.AddClickEventListener(self.node_list.baishi_btn, BindTool.Bind(self.OnClickBaiShiBtn, self))
	XUI.AddClickEventListener(self.node_list.get_title_btn, BindTool.Bind(self.OnClickGetTitleBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_recruit, BindTool.Bind(self.OnClickRecruit, self))
	self.role_vip_level_change = BindTool.Bind1(self.RoleVipLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_vip_level_change, {"vip_level"})
end

function ShiTuXiuLiView:InitTitlePanel()
	local title_id = ShiTuXiuLiWGData.Instance:GetOtherCfgList("title_show_id")
	if title_id then
		local bundle,asset = ResPath.GetTitleModel(title_id)
		self.node_list.teacher_title_obj:ChangeAsset(bundle, asset)
	end
	local vip_limit = ShiTuXiuLiWGData.Instance:GetOtherCfgList("title_card_vip_limit")
	self.node_list.get_title_desc.text.text = vip_limit and string.format(Language.ShiTuXiuLi.TitleRewardDesc, vip_limit) or ""
end

function ShiTuXiuLiView:InitStudentLoginItemList()
	local item_root = self.node_list.student_login_item_root.transform
	local item_list = {}
	for i=1,3 do
		local obj = ResMgr:Instantiate(self.node_list.student_login_item.gameObject)
		obj.transform:SetParent(item_root, false)
		obj:SetActive(true)
		item_list[i] = StudentLoginRewardItem.New(obj)
		item_list[i]:SetIndex(i)
	end
	self.student_login_itemlist = item_list
end

function ShiTuXiuLiView:OnClickTipBtn()
	local desc = ShiTuXiuLiWGData.Instance:GetOtherCfgList("tipdes")
	RuleTip.Instance:SetTitle(Language.ShiTuXiuLi.ViewTitle)
	RuleTip.Instance:SetContent(desc or "")
end

function ShiTuXiuLiView:OnTitleClick()
	local title_id = ShiTuXiuLiWGData.Instance:GetOtherCfgList("title_show_id")
	local title_cfg = TitleWGData.Instance:GetConfig(title_id)
	if title_cfg then
		TipWGCtrl.Instance:OpenItem({item_id = title_cfg.item_id})
	end
end

function ShiTuXiuLiView:OnClickBaiShiBtn()
	local can_baishi, is_baishi, tip_str = ShiTuXiuLiWGData.Instance:CanBaiShi(true)

	if tip_str then
		SysMsgWGCtrl.Instance:ErrorRemind(tip_str)
		return
	elseif is_baishi then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.IsCd)
		return 
	elseif not can_baishi then
		local student_vip_min = ShiTuXiuLiWGData.Instance:GetOtherCfgList("student_vip_min")
		local turn_str = string.format(Language.ShiTuXiuLi.ToDoStudentTip, student_vip_min, student_vip_min)
		TipWGCtrl.Instance:OpenAlertTips(turn_str, function ()
				ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "vip_level", {vip_level = student_vip_min})
			end)
		return
	end

	SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.ReqTeacherTip)
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_REQ_STUDY)
end

function ShiTuXiuLiView:OnClickRecruit()
	if my_status ~= MyStatusType.None then
		return
	end

	local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
	if not base_info then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if server_time < base_info.notify_acp_student_timestamp then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.IsCd)
		return
	end

	local teacher_vip_limit = ShiTuXiuLiWGData.Instance:GetOtherCfgList("teacher_vip_limit") or 0
	local my_vip_level = VipWGData.Instance:GetVipLevel()
	if my_vip_level < teacher_vip_limit then
		local str = string.format(Language.ShiTuXiuLi.NoEnoughVip, teacher_vip_limit)
		local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
		TipWGCtrl.Instance:OpenAlertTips(str, function() ViewManager.Instance:Open(GuideModuleName.Vip, tab_index) end)
		return
	end

	if base_info.cost_acp_student_timestam > 0 then -- 发传闻
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.AcpStudentTip)
		ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_NOTIFY_ACP_STUDENT)
	else
		local const = ShiTuXiuLiWGData.Instance:GetOtherCfgList("teaching_cost") or 0
		local str = string.format(Language.ShiTuXiuLi.CostSendMsg, const)

		local function ok_fun()
			local is_enough = RoleWGData.Instance:GetIsEnoughUseGold(const)
			if is_enough then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.FindStudentTip)
				ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_NOTIFY_ACP_STUDENT)
			else
				VipWGCtrl.Instance:OpenTipNoGold()
				-- ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
			end
		end

		TipWGCtrl.Instance:OpenAlertTips(str, ok_fun)
	end
end

function ShiTuXiuLiView:OnClickGetTitleBtn()
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_STUDENT_VIP_TITLE_REWARD)
end

function ShiTuXiuLiView:RoleVipLevelChange()
	self:RefreshCondidesc()
end

function ShiTuXiuLiView:OnClickToggle(index, is_flush)
	if index ~= self.toggle_index or is_flush then
		self.toggle_index = index

		if index == MyStatusType.Teacher then
			self:RefreshTeacherRewardList()
		elseif index == MyStatusType.Student then
			ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_WORLD_TEACHER_INFO)
			self:RefreshStudentTaskList()
		end
	end

	self.node_list.teacher_panel:SetActive(index == MyStatusType.Teacher)
	self.node_list.student_panel:SetActive(index == MyStatusType.Student)
	self:RefreshBanner()
	self:RefreshCondidesc()
	self:RefreshRightPanel()
	self:RefreshRecruit()
	self:RefreshStudentApplyList()
end

function ShiTuXiuLiView:RefreshView()
	self:RefreshMyStatus()
	self:RefreshToggle()
	self:RefreshBaiShiBtn()
	self:RefreshCountDown()
	self:RefreshRightPanel()
	self:RefreshStudentLoginRewardPanel()
	self:RefreshRecruit()
	self:RefreshStudentApplyList()
end

function ShiTuXiuLiView:RefreshToggle()
	local index = self.toggle_index or 1
	for i,toggle_item in ipairs(self.toggle_list) do
		toggle_item:FlushRemind()
		toggle_item:Flush()
		if index == 0 and toggle_item:GetIsRemind() then
			index = i
		end
	end

	index = math.max(index, 1)
	for i,toggle_item in ipairs(self.toggle_list) do
		toggle_item:SetIsOn(i == index)
	end

	self:OnClickToggle(index, true)
end

function ShiTuXiuLiView:RefreshToggleRemind()
	for _,toggle_item in ipairs(self.toggle_list) do
		toggle_item:FlushRemind()
	end
end

function ShiTuXiuLiView:RefreshMyStatus()
	local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
	if not base_info then
		return
	end
	if base_info.teacher_uid > 0 then
		my_status = MyStatusType.Student
	elseif base_info.student_uid > 0 then
		my_status = MyStatusType.Teacher
	else
		my_status = MyStatusType.None
	end
end

function ShiTuXiuLiView:RefreshBanner()
	local index = self.toggle_index
	local img_name = "a1_stzl_teacher"
	if index == MyStatusType.Student then
		img_name = my_status == MyStatusType.None and "a1_stzl_student_2" or "a1_stzl_student_3"
	else
		img_name = "a1_stzl_student_1"
	end

	local bundle,asset = ResPath.GetF2RawImagesPNG(img_name)
	self.node_list.banner_img.raw_image:LoadSprite(bundle, asset, function ()
		self.node_list.banner_img.raw_image:SetNativeSize()
	end)
	self.node_list.task_countdown:SetActive(index == MyStatusType.Student and my_status ~= MyStatusType.None)
end

-- 拜师条件 or 收徒条件 描述
function ShiTuXiuLiView:RefreshCondidesc()
	if my_status == MyStatusType.None then
		local vip_level = VipWGData.Instance:GetRoleVipLevel()
		local student_vip_max = ShiTuXiuLiWGData.Instance:GetOtherCfgList("student_vip_max")
		local student_vip_min = ShiTuXiuLiWGData.Instance:GetOtherCfgList("student_vip_min")
		local teacher_vip_limit = ShiTuXiuLiWGData.Instance:GetOtherCfgList("teacher_vip_limit")
		local desc = ""
		if vip_level >= teacher_vip_limit then
			desc = string.format(Language.ShiTuXiuLi.CanDoTeacher, vip_level)
		elseif self.toggle_index == MyStatusType.Teacher then
			desc = string.format(Language.ShiTuXiuLi.NotDoTeacher, vip_level, teacher_vip_limit)
		else
			desc = string.format(Language.ShiTuXiuLi.CanDoStudent, vip_level, student_vip_min, student_vip_max)
		end
		self.node_list.condidesc.text.text = desc
	else
		self.node_list.condidesc.text.text = ""
	end
end

function ShiTuXiuLiView:RefreshRightPanel()
	local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
	local status = base_info and base_info.student_vip_title_reward_status or 0
	self.node_list.get_title_img:SetActive(status == 2)
	self.node_list.get_title_btn:SetActive(my_status ~= MyStatusType.None and status ~= 2)
	self.node_list.get_title_btn_remind:SetActive(status == 1)
	XUI.SetButtonEnabled(self.node_list.get_title_btn, status == 1)

	local right_title = ""
	local toggle_index = self.toggle_index or 1
	if my_status == MyStatusType.None then
		right_title = Language.ShiTuXiuLi.RightStr[toggle_index]
	elseif my_status == MyStatusType.Teacher then
		right_title = Language.ShiTuXiuLi.RightStr[3]
	elseif my_status == MyStatusType.Student then
		right_title = Language.ShiTuXiuLi.RightStr[4]
	end

	if my_status ~= MyStatusType.None then
		self.node_list.no_baishi_img:SetActive(false)
	end

	self.node_list.right_top_desc.text.text = right_title
	self.node_list.student_baishi_list:SetActive(my_status == MyStatusType.None)
	-- self.node_list.teacher_title_reward:SetActive(my_status ~= MyStatusType.None)
	self.shitu_item:SetVisible(my_status ~= MyStatusType.None)
	self.shitu_item:Flush()
end

-- 徒弟登录奖励
function ShiTuXiuLiView:RefreshStudentLoginRewardPanel()
	if my_status == MyStatusType.None then
		self.node_list.student_login_reward:SetActive(false)
		return
	end
	local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
	if not base_info then
		return
	end
	local max_login_day = ShiTuXiuLiWGData.Instance:GetOtherCfgList("vip_task_login_day") or 0
	local now_login_day = base_info.study_login_day
	now_login_day = now_login_day > max_login_day and max_login_day or now_login_day
	local fill_value = {0, 0.5, 1}
	self.node_list.student_login_slider.slider.value = fill_value[now_login_day] or 0
	self.node_list.student_login_day_lbl.text.text = string.format(Language.ShiTuXiuLi.LoginDay, base_info.study_login_day, max_login_day)
	self.node_list.student_login_reward:SetActive(true)
	local item_data = {max_login_day = max_login_day, now_login_day = now_login_day, reward_falg = base_info.study_vip_fetch_reward_flag}
	if self.student_login_itemlist then
		for i,v in ipairs(self.student_login_itemlist) do
			v:SetData(item_data)
		end
	end
end

-- 拜师申请按钮
function ShiTuXiuLiView:RefreshBaiShiBtn()
	if my_status ~= MyStatusType.None then
		self.node_list.baishi_btn:SetActive(false)
		return
	end

	local can_baishi, is_baishi = ShiTuXiuLiWGData.Instance:CanBaiShi()
	self.node_list.baishi_btn_effect:SetActive(can_baishi and not is_baishi)
	
	XUI.SetGraphicGrey(self.node_list.baishi_btn, is_baishi)
	self.node_list.baishi_btn:SetActive(true)

	if can_baishi and is_baishi then
		if not CountDownManager.Instance:HasCountDown("shituxiuli_baishi_cd") then
			local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			local time_str = TimeUtil.FormatSecond2MS(base_info.notify_req_teach_timestamp - server_time)
        	self.node_list.baishi_btn_lbl.text.text = string.format(Language.ShiTuXiuLi.BtnStr2, time_str)
	        CountDownManager.Instance:AddCountDown("shituxiuli_baishi_cd",
		        function (elapse_time, total_time)
		        	local time_str = TimeUtil.FormatSecond2MS(total_time - elapse_time)
		        	self.node_list.baishi_btn_lbl.text.text = string.format(Language.ShiTuXiuLi.BtnStr2, time_str)
		        end,
		        BindTool.Bind(self.RefreshBaiShiBtn, self), base_info.notify_req_teach_timestamp, nil, 1)
        end
    else
    	CountDownManager.Instance:RemoveCountDown("shituxiuli_baishi_cd")
    	self.node_list.baishi_btn_lbl.text.text = Language.ShiTuXiuLi.BtnStr1
    end
end

-- 师傅登录奖励
function ShiTuXiuLiView:RefreshTeacherRewardList()
	local cfg_list = ShiTuXiuLiWGData.Instance:GetTeacherCfgList()
	local data_list = {}
	local flag = 0
	for k,v in pairs(cfg_list) do
		flag = ShiTuXiuLiWGData.Instance:GetTeacherLoginRewardFlag(v.index)
		if flag == 2 then
			data_list[v.login_day + 200] = v
		elseif v.login_day == 7 then -- 第七天有个大奖未领取的时候置顶显示
			data_list[v.login_day] = v
		else
			data_list[v.login_day + 100] = v
		end
	end
	data_list = SortTableKey(data_list)
	self.teacher_reward_list:SetDataList(data_list)
end

-- 徒弟任务奖励(给师傅领的,徒弟就看看或者求助一下)
function ShiTuXiuLiView:RefreshStudentTaskList()
	local cfg_list = ShiTuXiuLiWGData.Instance:GetStudentCfgList()
	local data_list = {}
	local task_info = nil
	local function sort_fun(data)
		task_info = ShiTuXiuLiWGData.Instance:GetStudentTaskInfoByTaskId(data.taskid)
		if task_info then
			if my_status == MyStatusType.Student then
				if task_info.reward_flag ~= 0 then
					return 100 + data.taskid
				end
			elseif my_status == MyStatusType.Teacher then
				if task_info.reward_flag == 0 then
					return 100 + data.taskid
				elseif task_info.reward_flag == 2 then
					return 200 + data.taskid
				end
			end
		end
		return data.taskid
	end
	for k,v in pairs(cfg_list) do
		data_list[sort_fun(v)] = v
	end
	data_list = SortTableKey(data_list)
	self.student_task_list:SetDataList(data_list)
end

-- 收徒列表
function ShiTuXiuLiView:RefreshStudentApplyList()
	if my_status == MyStatusType.None then
		local data_list = {}

		if self.toggle_index == MyStatusType.Student then
			local info_list = ShiTuXiuLiWGData.Instance:GetAllTeacherUidList()
			for i=1,#info_list do
				data_list[i] = {uid = info_list[i].uid, is_teacher = false}
			end
		elseif self.toggle_index == MyStatusType.Teacher then
			local all_student_list = ShiTuXiuLiWGData.Instance:GetAllStudentInfo()
			local info_list = all_student_list and all_student_list.student_info or {}
			for i=1,#info_list do
				data_list[i] = {uid = info_list[i].uid, is_teacher = true}
			end
		end

		-- 去掉自己
		local main_vo = GameVoManager.Instance:GetMainRoleVo()
		local main_uid = main_vo.role_id or -1
		for i=#data_list, 1, -1 do
			if data_list[i].uid == main_uid then
				table.remove(data_list, i)
			end
		end

		if data_list then
			self.student_apply_list:SetDataList(data_list)
			self.node_list.no_baishi_img:SetActive(#data_list <= 0)
		end
	end
end

function ShiTuXiuLiView:RefreshRecruit()
	if my_status == MyStatusType.None and self.toggle_index == MyStatusType.Teacher then
		self.node_list.no_jiebai_img:SetActive(false)
		self.node_list.jiebai_info_rect:SetActive(false)
		self.node_list.btn_recruit:SetActive(true)
		self.node_list.info_bg:SetActive(false)

		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
		local is_shoutu = base_info and base_info.cost_acp_student_timestam > 0 -- 给了仙玉
		local is_shoutu_cd = base_info and server_time < base_info.notify_acp_student_timestamp
		self.node_list.btn_recruit_effect:SetActive(is_shoutu and not is_shoutu_cd)
		XUI.SetGraphicGrey(self.node_list.btn_recruit, is_shoutu and is_shoutu_cd)

		if is_shoutu and is_shoutu_cd then
			if not CountDownManager.Instance:HasCountDown("shituxiuli_shoutu_cd") then
				local time_str = TimeUtil.FormatSecond2MS(base_info.notify_acp_student_timestamp - server_time)
        		self.node_list.text_recruit.text.text = string.format("%s%s", Language.ShiTuXiuLi.FindStudent, time_str)
		        CountDownManager.Instance:AddCountDown("shituxiuli_shoutu_cd",
			        function (elapse_time, total_time)
			        	local time_str = TimeUtil.FormatSecond2MS(total_time - elapse_time)
			        	self.node_list.text_recruit.text.text = string.format("%s%s", Language.ShiTuXiuLi.FindStudent, time_str)
			        end,
			        BindTool.Bind(self.RefreshRecruit, self), base_info.notify_acp_student_timestamp, nil, 1)
	        end
	    else
	    	CountDownManager.Instance:RemoveCountDown("shituxiuli_shoutu_cd")
	    	self.node_list.text_recruit.text.text = is_shoutu and Language.ShiTuXiuLi.FindStudent or Language.ShiTuXiuLi.BecomeTeacher
        end
	else
		self:RefreshJieBaiRumor()
	end
end

-- 结拜传闻
function ShiTuXiuLiView:RefreshJieBaiRumor()
	if my_status == MyStatusType.None and self.toggle_index == MyStatusType.Teacher then
		return
	end

	self.node_list.btn_recruit:SetActive(false)
	self.node_list.info_bg:SetActive(true)
	local jiebai_info = ShiTuXiuLiWGData.Instance:GetAllSuccessNodeInfo()
	local jiebai_list = jiebai_info and jiebai_info.node_info
	if not jiebai_list then
		self.node_list.no_jiebai_img:SetActive(true)
		self.node_list.jiebai_info_rect:SetActive(false)
		return
	end
	local check_str_func = CheckCharactersHaveCh
	local name_list = {}
	for i,v in ipairs(jiebai_list) do
		name_list[i] = string.format(Language.ShiTuXiuLi.JieBaiRumorDesc, v.teacher_name, v.student_name)
	end
	local desc = #name_list > 1 and table.concat(name_list, "\n") or name_list[1]
	self.node_list.jiebai_info_content.text.text = desc or ""
	self.node_list.no_jiebai_img:SetActive(false)
	self.node_list.jiebai_info_rect:SetActive(true)
end

function ShiTuXiuLiView:RefreshCountDown()
	CountDownManager.Instance:RemoveCountDown("shituxiuli_count_down")
	local close_time,task_time = ShiTuXiuLiWGData.Instance:GetActCloseTime()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	if close_time > now_time then
		self.node_list.time_text.text.text = TimeUtil.FormatSecondDHM8(close_time - now_time)
		CountDownManager.Instance:AddCountDown(
			"shituxiuli_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.RefreshCountDown, self),
			close_time,
			nil,
			1
		)
	else
		self.node_list.time_text.text.text = Language.ShiTuXiuLi.YiJieShu
		self.node_list.time_text.text.color = Str2C3b(COLOR3B.RED)
	end
	if task_time > now_time then
		self.task_end_time = task_time
		self.node_list.task_countdown_lbl.text.text = TimeUtil.FormatTimeDHMS(task_time - now_time)
		self.node_list.task_countdown_lbl.text.color = Str2C3b(COLOR3B.D_GREEN)
	else
		self.node_list.task_countdown_lbl.text.text = Language.ShiTuXiuLi.TaskEndTimeStr
		self.node_list.task_countdown_lbl.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ShiTuXiuLiView:UpdateCountDown(elapse_time, total_time)
	if self.task_end_time > 0 then
		local now_time = TimeWGCtrl.Instance:GetServerTime()
		self.node_list.task_countdown_lbl.text.text = TimeUtil.FormatTimeDHMS(self.task_end_time - now_time)
	end
	self.node_list.time_text.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

--------------------------------------------------------------------------------

ShiTuXiuLiToggleItem = ShiTuXiuLiToggleItem or BaseClass(BaseRender)

function ShiTuXiuLiToggleItem:__init()
	self.is_remind = false
end

function ShiTuXiuLiToggleItem:OnFlush()
	local index = self:GetIndex()
	local name = ""
	if my_status == MyStatusType.None then
		name = Language.ShiTuXiuLi.ToggleStr_None[index] or ""
	else
		name = Language.ShiTuXiuLi.ToggleStr[index] or ""
	end
	self.node_list.toggle_lbl.text.text = name
	self.node_list.toggle_lbl_hl.text.text = name
	self.node_list.toggle_red:SetActive(self.is_remind)
end

function ShiTuXiuLiToggleItem:SetIsOn(_bool)
	local view = self:GetView()
	if view then
		view.toggle.isOn = _bool
	end
end

function ShiTuXiuLiToggleItem:FlushRemind()
	local index = self:GetIndex()
	local is_remind = false
	if (my_status == MyStatusType.Teacher or my_status == MyStatusType.None)and index == MyStatusType.Teacher then
		is_remind = ShiTuXiuLiWGData.Instance:HasTeacherLoginReward()
	elseif index == MyStatusType.Student then
		if my_status == MyStatusType.Student then
			is_remind = ShiTuXiuLiWGData.Instance:HasStudentLoginReward()
		elseif my_status == MyStatusType.Teacher then
			is_remind = ShiTuXiuLiWGData.Instance:HasStudentTaskReward()
		end
	end
	if self.node_list.toggle_red then
		self.node_list.toggle_red:SetActive(is_remind)
	end
	self.is_remind = is_remind
end

function ShiTuXiuLiToggleItem:GetIsRemind()
	return self.is_remind
end

--------------------------------------------------------------------------------

TeacherRewardItem = TeacherRewardItem or BaseClass(BaseRender)

function TeacherRewardItem:__init()
	self.item_list = {}
	self.ex_item_num = 5
end

function TeacherRewardItem:__delete()
	for k,v in pairs(self.item_list) do
		v:DeleteMe()
	end
	self.item_list = nil
end

function TeacherRewardItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function TeacherRewardItem:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	self.node_list.desc.text.text = string.format(Language.ShiTuXiuLi.TeacherRewardDesc, data.login_day)

	local reward_flag = ShiTuXiuLiWGData.Instance:GetTeacherLoginRewardFlag(data.index)
	self.node_list.finish_img:SetActive(reward_flag == 2)
	self.node_list.noenough_img:SetActive(reward_flag == 0)
	self.node_list.get_btn:SetActive(reward_flag == 1)
	self.node_list.red_point:SetActive(reward_flag == 1)

	self:FlushRewardList(data.reward_item)
end

function TeacherRewardItem:OnClickGetBtn()
	local data = self:GetData()
	if data then
		ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_TEACHER_LOGIN_REWARD, data.index)
	end
end

function TeacherRewardItem:FlushRewardList(reward_item)
	local reward_list = SortTableKey(reward_item)
	local item_list = self.item_list
	if #reward_list > #item_list then
		local cell_parent = self.node_list.cell_root
		for i=1,#reward_list do
			item_list[i] = item_list[i] or ItemCell.New(cell_parent)
		end
		self.item_list = item_list
	end
	local item_cfg = nil
	local is_conver_num = false
	for i=1,#item_list do
		if reward_list[i] then
			---[[ 仙玉卡特殊处理数量显示成仙玉数
			is_conver_num = false
			item_cfg = ItemWGData.Instance:GetItemConfig(reward_list[i].item_id)
			if item_cfg and item_cfg.use_type == Item_Use_Type.XianYu2 then
				reward_list[i].is_conver_num = true
			end
			--]]
			item_list[i]:SetData(reward_list[i])
			item_list[i]:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
			item_list[i]:SetActive(true)
		else
			item_list[i]:SetActive(false)
		end
	end
	self.node_list.reward_rect.scroll_rect.horizontalNormalizedPosition = 0
	self.node_list.reward_rect.scroll_rect.horizontal = #reward_list >= self.ex_item_num
end

--------------------------------------------------------------------------------

StudentTaskItem = StudentTaskItem or BaseClass(TeacherRewardItem)

function StudentTaskItem:__init()
	self.item_list = {}
	self.ex_item_num = 3
end

function StudentTaskItem:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end
	self.node_list.desc.text.text = string.format(data.text, data.progress)
	self.node_list.btn_list:SetActive(my_status ~= MyStatusType.None)
	self:FlushTaskStatus()
	self:FlushRewardList(data.reward_item)
end

function StudentTaskItem:FlushTaskStatus()
	local data = self:GetData()
	local task_info = ShiTuXiuLiWGData.Instance:GetStudentTaskInfoByTaskId(data.taskid)
	if my_status == MyStatusType.None or not task_info then
		return
	end
	if task_info.progress < data.progress then
		local desc = string.format(data.text, data.progress)
		desc = string.format("%s<color=%s>(%d/%d)</color>", desc, COLOR3B.GREEN, task_info.progress, data.progress)
		self.node_list.desc.text.text = desc
	end
	if my_status == MyStatusType.Teacher then
		self:FlushTeacherBtn(task_info)
	elseif my_status == MyStatusType.Student then
		self:FlushStudentBtn(task_info, data)
	end
end

function StudentTaskItem:FlushTeacherBtn(task_info)
	local is_pass = ShiTuXiuLiWGData.Instance:StudentTaskIsPass()
	local can_get_reward = task_info.reward_flag == 1
	local is_get_reward = task_info.reward_flag == 2
	self.node_list.finish_img:SetActive(is_get_reward)
	self.node_list.noenough_img:SetActive(not can_get_reward and not is_get_reward)
	self.node_list.get_btn:SetActive(can_get_reward)
	self.node_list.red_point:SetActive(can_get_reward)
	self.node_list.get_btn_lbl.text.text = Language.ShiTuXiuLi.LingQu
	self.node_list.finish_lbl.text.text = Language.Common.YiLingQu
	self.node_list.noenough_lbl.text.text = is_pass and Language.ShiTuXiuLi.YiJieShu or Language.Common.WEIDACHENG
	XUI.SetButtonEnabled(self.node_list.get_btn, can_get_reward)
end

function StudentTaskItem:FlushStudentBtn(task_info, data)
	local is_pass = ShiTuXiuLiWGData.Instance:StudentTaskIsPass()
	local is_finish = task_info.progress >= data.progress
	local is_qiuzhu = task_info.day_req_help_flag == 1
	self.node_list.get_btn_lbl.text.text = is_qiuzhu and Language.ShiTuXiuLi.YiQiuZhu or Language.ShiTuXiuLi.QianWang
	self.node_list.finish_lbl.text.text = Language.ShiTuXiuLi.YiWanCheng
	XUI.SetButtonEnabled(self.node_list.get_btn, not is_qiuzhu)
	self.node_list.get_btn:SetActive(not is_finish and not is_pass)
	self.node_list.finish_img:SetActive(is_finish)
	self.node_list.red_point:SetActive(false)
	self.node_list.noenough_img:SetActive(not is_finish and is_pass)
	self.node_list.noenough_lbl.text.text = Language.ShiTuXiuLi.YiJieShu
end

function StudentTaskItem:OnClickGetBtn()
	local data = self:GetData()
	if not data then
		return
	end
	if my_status == MyStatusType.Teacher then
		ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_STUDY_TASK_REWARD, data.taskid)
	elseif my_status == MyStatusType.Student then
		-- ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_REQ_HELP, data.taskid)
		FunOpen.Instance:OpenViewNameByCfg(data.turn_panel_name)
	end
end

--------------------------------------------------------------------------------

StudentApplyItem = StudentApplyItem or BaseClass(BaseRender)

function StudentApplyItem:__delete()
	self.role_info = nil

	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	if self.tween_shake ~= nil then
		self.tween_shake:Kill()
		self.tween_shake = nil
	end
end

function StudentApplyItem:LoadCallBack()
	if self.node_list.head_root then
		self.role_head_cell = BaseHeadCell.New(self.node_list.head_root)
	end
	if self.node_list.shoutu_btn then
		XUI.AddClickEventListener(self.node_list.shoutu_btn, BindTool.Bind(self.OnClickShoutuBtn, self))
		XUI.AddClickEventListener(self.node_list.head_click, BindTool.Bind(self.OnClickHeadBtn, self))
		XUI.AddClickEventListener(self.node_list.btn_baishi, BindTool.Bind(self.OnClickBaiShi, self))
	end
end

function StudentApplyItem:OnFlush()
	local data = self:GetData()
	if not data or not data.uid then
		return
	end

	if data.uid > 0 then
		self.my_uid = data.uid
		ShiTuXiuLiWGData.Instance:GetRoleInfo(data.uid, BindTool.Bind(self.SetRoleInfo, self))
	end

	self.node_list.shoutu_btn:SetActive(data.is_teacher)
	self.node_list.btn_baishi:SetActive(not data.is_teacher)

	if not data.is_teacher then
		if self.tween_shake == nil then
			self.tween_shake = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.btn_baishi.transform, self.tween_shake, 1)
		end
	else
		if self.tween_shake ~= nil then
			self.tween_shake:Kill()
			self.tween_shake = nil
		end
	end
end

function StudentApplyItem:SetRoleInfo(role_info)
	-- 父类中是否加载
	if not self.has_load then
		return
	end

	if not role_info or role_info.role_id ~= self.my_uid then
		return
	end
	self.role_info = role_info
	self.node_list.name.text.text = role_info.role_name or role_info.name or ""
	self:SetHeadCell(role_info)
	self:SetVipLevel(role_info.vip_level)
end

function StudentApplyItem:SetHeadCell(role_info)
	local appearance = role_info and role_info.appearance
	self.role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.role_head_cell:SetData(role_info)
end

function StudentApplyItem:SetVipLevel(vip_level)
	self.node_list.vip_text.text.text = "V" .. vip_level
end

function StudentApplyItem:OnClickShoutuBtn()
	local role_info = self.role_info
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local teacher_vip_limit = ShiTuXiuLiWGData.Instance:GetOtherCfgList("teacher_vip_limit") or 0
	if vip_level < teacher_vip_limit then
		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.ShiTuXiuLi.ToDoTeacherTip, teacher_vip_limit, teacher_vip_limit), function ()
				if RechargeWGData.Instance:HasVipZeroBuy() then
					local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
					RechargeWGCtrl.Instance:Open(tab_index)
				else
					ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "vip_level", {vip_level = teacher_vip_limit})
				end
			end)
		return
	end
	if role_info then
		local my_role_id = RoleWGData.Instance:GetOriginUid()
		if role_info.role_id == my_role_id then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.IsMe)
			return
		end

		local name = role_info.role_name or role_info.name or ""
		local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
		if base_info ~= nil and base_info.cost_acp_student_timestam > 0 then
			TipWGCtrl.Instance:OpenAlertTips(string.format(Language.ShiTuXiuLi.BecomeTeacherTip, name), function ()
					ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_ACCEPT_STUDENT, role_info.role_id)
				end)
		else
			local const = ShiTuXiuLiWGData.Instance:GetOtherCfgList("teaching_cost") or 0
			TipWGCtrl.Instance:OpenAlertTips(string.format(Language.ShiTuXiuLi.ShouTuDesc, name, const), function ()
					ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_ACCEPT_STUDENT, role_info.role_id)
				end)
		end
	end
end

function StudentApplyItem:OnClickHeadBtn()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local data = self:GetData()
	if main_vo and data and main_vo.role_id == data.uid then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.NoWGCtrlMine)
		return
	end
	if self.role_info then
		BrowseWGCtrl.Instance:ReqRoleInfoCallBack(nil, nil, nil, self.role_info)
	end
end

function StudentApplyItem:OnClickBaiShi()
	if self.data == nil then
		return
	end

	if self.data.uid == RoleWGData.Instance:GetOriginUid() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.NoCanTeacherMe)
		return
	end

	local student_vip_min = ShiTuXiuLiWGData.Instance:GetOtherCfgList("student_vip_min") or 0
	local student_vip_max = ShiTuXiuLiWGData.Instance:GetOtherCfgList("student_vip_max") or 0
	local my_vip_level = VipWGData.Instance:GetVipLevel()

	if my_vip_level < student_vip_min then
		local str = string.format(Language.ShiTuXiuLi.BaiShiVipLimit, student_vip_min)
		TipWGCtrl.Instance:OpenAlertTips(str, function() ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "vip_level", {vip_level = student_vip_min}) end)
		return
	end

	if my_vip_level > student_vip_max then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShiTuXiuLi.NoCanBaiShi, my_vip_level))
		return
	end

	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_REQ_STUDY, self.data.uid)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTuXiuLi.IsSendReq)
end
--------------------------------------------------------------------------------

TeacherOrStudentItem = TeacherOrStudentItem or BaseClass(StudentApplyItem)

function TeacherOrStudentItem:__init(instance)
	if instance then
		local bundle,asset = "uis/view/shituxiuli_prefab", "student_apply_item"
		self:LoadAsset(bundle, asset, instance.transform)
	end
end

function TeacherOrStudentItem:OnFlush()
	if my_status == MyStatusType.None then
		return
	end
	local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
	local role_id = my_status == MyStatusType.Teacher and base_info.student_uid or base_info.teacher_uid
	if role_id > 0 then
		self.my_uid = role_id
		BrowseWGCtrl.Instance:SendQueryRoleInfoReq(role_id, BindTool.Bind(self.SetRoleInfo, self))
	end
end

--------------------------------------------------------------------------------

StudentLoginRewardItem = StudentLoginRewardItem or BaseClass(BaseRender)

function StudentLoginRewardItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetVipExpBtn, self))
end

function StudentLoginRewardItem:OnFlush()
	local index = self:GetIndex()
	local data = self:GetData()
	if not data then
		return
	end
	self.node_list.bg_last:SetActive(index == data.max_login_day)
	self.node_list.desc_lbl.text.text = (index == data.max_login_day and Language.ShiTuXiuLi.UpVipStr) or string.format(Language.ShiTuXiuLi.LoginDay2, index)

	local is_finish = data.now_login_day >= index
	local can_get_exp = false
	if index == data.max_login_day then
		can_get_exp = data.reward_falg == 1 and my_status == MyStatusType.Student
		is_finish = data.reward_falg == 2
		self:SetCanGetEffect(can_get_exp)
	end

	self.node_list.bg_block:SetActive(is_finish)
	--self.node_list.gou_img:SetActive(is_finish)
	self.node_list.get_btn:SetActive(can_get_exp)
end

function StudentLoginRewardItem:SetCanGetEffect(_bool)
	if _bool then
		local bundle,asset = ResPath.GetEffectUi("UI_vip_anniu")
		self.node_list.effect:ChangeAsset(bundle,asset)
	end
	self.node_list.effect:SetActive(_bool)
end

function StudentLoginRewardItem:OnClickGetVipExpBtn()
	if my_status == MyStatusType.Student then
		ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_STUDY_LOGIN_REWARD)
	end
end
