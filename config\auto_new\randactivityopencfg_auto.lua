-- S-随机活动开启配置.xls

return {
other={
{}
},

other_meta_table_map={
},
open_cfg={
{begin_day_idx=1,end_day_idx=9999,},
{activity_type=2281,activity_name="冲榜助力",},
{activity_type=2219,begin_day_idx=1,end_day_idx=8,activity_name="仙侣佳缘（热恋）",},
{activity_type=2251,begin_day_idx=2,end_day_idx=9999,activity_name="连续充值",},
{activity_type=2353,begin_day_idx=4,end_day_idx=6,activity_name="充值榜",},
{activity_type=2354,begin_day_idx=6,end_day_idx=8,activity_name="消费榜",},
{activity_type=2106,activity_name="摩天轮（招财猫）",},
{activity_type=2083,begin_day_idx=1,end_day_idx=3,activity_name="金光鉴福",},
{activity_type=2356,activity_name="五气朝元",},
{activity_type=2330,begin_day_idx=5,end_day_idx=6,activity_name="万象玄生",},
{activity_type=2272,activity_name="神灵庆典-限时抢购",},
{activity_type=2211,activity_name="神灵庆典-登录有礼",},
{activity_type=2212,activity_name="神灵庆典-首充送礼",},
{activity_type=2213,activity_name="神灵庆典-累计充值",},
{activity_type=2214,activity_name="神灵庆典-拼图领奖",},
{activity_type=2215,activity_name="神灵庆典-神灵排行",},
{activity_type=2216,activity_name="神灵庆典-试炼副本",},
{activity_type=2218,activity_name="神灵庆典-双倍副本",},
{activity_type=2329,activity_name="伏龙翔天",},
{activity_type=2312,activity_name="无上直购",},
{activity_type=2292,activity_name="圣兽来袭",},
{activity_type=2339,activity_name="累充豪礼",},
{activity_type=2357,activity_name="开服集字",},
{activity_type=2355,activity_name="幻兽抽奖",},
{activity_type=2336,begin_day_idx=3,end_day_idx=4,activity_name="技能直购",},
{activity_type=2361,begin_day_idx=4,end_day_idx=5,activity_name="一剑霜寒",}
},

open_cfg_meta_table_map={
[9]=1,	-- depth:1
[7]=25,	-- depth:1
[2]=3,	-- depth:1
[21]=6,	-- depth:1
[22]=5,	-- depth:1
[23]=3,	-- depth:1
[24]=4,	-- depth:1
},
base_on_day_cfg={

},

base_on_day_cfg_meta_table_map={
},
base_on_time_cfg={

},

base_on_time_cfg_meta_table_map={
},
loop_open_cfg={
{},
{activity_type=2291,loop_end_day_idx=999,loop_interval_days=5,interval_days=5,},
{activity_type=2331,loop_end_day_idx=31,}
},

loop_open_cfg_meta_table_map={
},
week_loop_open_cfg={
{},
{activity_type=2092,},
{activity_type=2095,},
{activity_type=2096,},
{activity_type=2097,},
{activity_type=2100,},
{activity_type=2101,},
{activity_type=2103,},
{activity_type=2104,},
{activity_type=2105,},
{activity_type=2107,},
{activity_type=2126,},
{activity_type=2284,},
{activity_type=2326,},
{activity_type=2330,},
{activity_type=2329,},
{activity_type=2312,},
{activity_type=2083,}
},

week_loop_open_cfg_meta_table_map={
},
combine_open_cfg={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

combine_open_cfg_meta_table_map={
},
client_open_cfg={

},

client_open_cfg_meta_table_map={
},
other_default_table={begin_day_idx=0,end_day_idx=7,allow_set_time_dayidx=8,},

open_cfg_default_table={activity_type=2208,begin_day_idx=8,begin_time=0,end_day_idx=9,end_time=0,open_type=0,activity_name="超值必买",},

base_on_day_cfg_default_table={},

base_on_time_cfg_default_table={},

loop_open_cfg_default_table={activity_type=2314,loop_begin_day_idx=0,loop_end_day_idx=6,loop_interval_days=1,interval_days=1,open_type=0,},

week_loop_open_cfg_default_table={activity_type=2091,open_day_limit=8,},

combine_open_cfg_default_table={},

client_open_cfg_default_table={}

}

