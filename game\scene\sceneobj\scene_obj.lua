SceneObj = SceneObj or BaseClass(VisibleObj)

SceneObj.WaitEnterSceneObjCount = 0

--基本场景对象
function SceneObj:__init(vo, parent_scene)
	self.obj_type = SceneObjType.Unknown
	self.shield_obj_type = ShieldObjType.SceneObj
	self.followui_class = FollowUi
	self.shadow_hide_when_self_hide = false
	self.followui_hide_when_self_hide = false

	self.vo = vo

	self.parent_scene = parent_scene

	self.logic_pos = u3d.vec2(0, 0)
	self.real_pos = u3d.vec2(0, 0)

	self.draw_obj = DrawObj.New(self, SceneObjLayer.transform)

	self.draw_obj:SetIsUseObjPool(true)

	if vo.dir and vo.dir ~= 0 then
		local angle = 90 - math.deg(vo.dir)
		self.draw_obj.root_transform.rotation = Quaternion.Euler(0, angle, 0)
	end

	-- self.draw_obj:SetIsDisableAllAttachEffects(true)
	self.draw_obj:SetSceneObj(self)
	self.draw_obj:SetLoadComplete(BindTool.Bind(self.OnModelLoaded, self))
	self.draw_obj:SetRemoveCallback(BindTool.Bind(self.OnModelRemove, self))

	self.load_priority = 0

	self.follow_ui = nil
	self.is_select = false

	self.has_set_prefab_data = false

	self.moving = false
	self.is_visible = true
	self.is_can_click = true

	self.is_enter_scene = false
	self.is_wait_enter_scene = false
	self.show_quaility = nil

	self.character_shadow = nil
end

function SceneObj:__delete()
	if self:IsDeleted() then
		return
	end

	if nil ~= self.vo and Scene and Scene.Instance and Scene.Instance:GetObj(self:GetObjId()) == self then
		print_error("[SceneObj]重大Bug!!! obj还在场景管理中，却被非法Delete!!!", self:GetType(), self:GetName())
		GameRoot.AddLuaWarning("重大Bug!!! obj还在场景管理中，却被非法Delete。马上通知主程!!!", "High")
	end

	if SceneObj.select_obj == self then
		SceneObj.select_obj = nil
	end

	if nil ~= self.follow_ui then
		self.follow_ui:DeleteMe()
		self.follow_ui = nil
	end

	if self.shadow then
		self.shadow:DeleteMe()
		self.shadow = nil
	end

	if self.gameobject_attach_handle then
		self.gameobject_attach_handle:DeleteMe()
		self.gameobject_attach_handle = nil
	end

	if nil ~= self.under_follow_ui then
		self.under_follow_ui:DeleteMe()
		self.under_follow_ui = nil
	end

	if self.character_shadow then
		self.character_shadow.enabled = false
		self.character_shadow = nil
	end

	self.parent_scene = nil
	self:DeleteDrawObj()

	if nil ~= self.vo then
		GameVoManager.Instance:DeleteVo(self.vo)
		self.vo = nil
	end

	if self.delay_time then
		GlobalTimerQuest:CancelDelayTimer(self.delay_time)
		self.delay_time = nil
	end

	self:DeleteActorFunc()
	self.has_set_prefab_data = false

	self.is_enter_scene = false
	if self.is_wait_enter_scene then
		self.is_wait_enter_scene = false
		SceneObj.WaitEnterSceneObjCount = math.max(0, SceneObj.WaitEnterSceneObjCount - 1)
	end

	self.show_quaility = nil
end

function SceneObj:DeleteActorFunc()
	if self.actor_ctrl then
		self.actor_ctrl:DeleteMe()
		self.actor_ctrl = nil
	end
	if self.actor_trigger then
		self.actor_trigger:DeleteMe()
		self.actor_trigger = nil
	end
end

function SceneObj:DeleteDrawObj()
	if nil ~= self.draw_obj then
		self.draw_obj:DeleteMe()
		self.draw_obj = nil
	end
end

function SceneObj:IsMoving()
	return self.moving
end

function SceneObj:SetIsMoving(is_move_ing)
	self.moving = is_move_ing
end

function SceneObj:IsDeleted()
	return self.draw_obj == nil or self.vo == nil
end

function SceneObj:GetRoot()
	if self.draw_obj ~= nil then
		return self.draw_obj:GetRoot()
	end

	return nil
end

function SceneObj:Init(parent_scene)
	self.parent_scene = parent_scene
	self:InitInfo()
	self:InitShow()
	self:InitEnd()
end

----------------------------------------------------
-- 继承begin
----------------------------------------------------
function SceneObj:InitInfo()
	self:SetLogicPos(self.vo.pos_x, self.vo.pos_y)

	local param_list = nil
	if self:IsBeast() then
		param_list = {
			is_force_audio = (self.vo and self.vo.owner_obj) and self.vo.owner_obj:IsMainRole(),
		}
	end

	self.actor_trigger = ActorTrigger.New(self.obj_type, param_list)
	self.actor_ctrl = ActorWGCtrl.New(self.actor_trigger)
	self:CreateShieldHandle()

	if not self:IsRole() and not self:IsFollowObj() and not self:IsPet() and not self:IsWuHun() then
		self.gameobject_attach_handle = GameObjectAttachHandle.New(self)
		self.gameobject_attach_handle:CreateShieldHandle()
	end
end

function SceneObj:InitShow()
	if self.draw_obj then
		self.draw_obj:SetName(self.vo.name)
		self.draw_obj:SetPosition(self.real_pos.x, self.real_pos.y)
	end
end

function SceneObj:InitEnd()
	self.is_inited = true

	if not self.parent_scene:IsSceneLoading() then
		if self:IsMainRole() then
			self:OnEnterScene()
			return
		end
		self.is_wait_enter_scene = true
		SceneObj.WaitEnterSceneObjCount = SceneObj.WaitEnterSceneObjCount + 1
		self.wait_enter_scene_num = math.ceil(SceneObj.WaitEnterSceneObjCount / 2)
	end
end

function SceneObj:Update(now_time, elapse_time)
	if self.is_wait_enter_scene then
		self.wait_enter_scene_num = self.wait_enter_scene_num - 1
		if self.wait_enter_scene_num <= 0 then
			SceneObj.WaitEnterSceneObjCount = math.max(0, SceneObj.WaitEnterSceneObjCount - 1)
			self.is_wait_enter_scene = false
			self:OnEnterScene()
		end
	end
end

function SceneObj:InitAppearance()
end

function SceneObj:OnEnterScene()
	self.is_enter_scene = true
	self:InitAppearance()
	if self.follow_ui then
		self.follow_ui:OnEnterScene(true)
	end

	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	if not self.is_performer then
		main_part:ListenClick(BindTool.Bind(self.OnClicked, self))
	end
	self:UpdateMaterialQuality()
end

-- 此块对性能影响大，非主程不要修改
function SceneObj:UpdateMaterialQuality()
    self.show_quaility = QualityConfig.QualityLevel
end

function SceneObj:CreateShadow()
    if self.draw_obj == nil or self.shadow_level == nil then
        return 
    end

    self.character_shadow = JYCharacterShadow.Create(self.draw_obj.root.gameObject, self.shadow_level) 
end

function SceneObj:ChangeShadowVisible(is_visible)
	if self.shadow then
		self.shadow:ForceSetVisible(is_visible)
	end
end

function SceneObj:GetShadow()
	return self.shadow
end

function SceneObj:ForceShadowVisible(is_visible)
	if self.shadow == nil then
		return
	end

	if not is_visible then
		self.shadow:ForceSetVisible(false)
	else
		self.shadow:CancelForceSetVisible()
	end
end

function SceneObj:UpdateShadowVisib()
	if self.shadow ~= nil then
		self.shadow:RefreshVisiable()
	end
end

function SceneObj:UpdateJumppointRotate()
	if not self:IsJumpPoint() then return end
	if self.vo.target_id and self.vo.target_id > 0 then
		local target_point = Scene.Instance:GetObjByTypeAndKey(SceneObjType.JumpPoint, self.vo.target_id)
		if target_point then
			self.draw_obj.root.transform:LookAt(target_point:GetRoot().transform)
		end
	end

	local jump_point_list = Scene.Instance:GetObjListByType(SceneObjType.JumpPoint)
	if jump_point_list then
		for k,v in pairs(jump_point_list) do
			if v.vo.target_id == self.vo.id then
				v:UpdateJumppointRotate()
			end
		end
	end
end

function SceneObj:IsDefenseTower()
	return false
end

function SceneObj:IsTower()
	return false
end

function SceneObj:IsCharacter()
	return false
end

function SceneObj:IsRole()
	return false
end

function SceneObj:IsGhost()
	return false
end

function SceneObj:IsEvent()
	return false
end

function SceneObj:IsMainRole()
	return false
end

function SceneObj:IsSkillShower()
	return false
end

function SceneObj:IsSpirit()
	return false
end

function SceneObj:IsGuard()
	return false
end

function SceneObj:IsMonster()
	return false
end

function SceneObj:IsBoss()
	return false
end

function SceneObj:IsNpc()
	return false
end

function SceneObj:IsSoulBoy()
	return false
end

function SceneObj:IsFollowMingJiang()
	return false
end

function SceneObj:IsPet()
	return false
end

function SceneObj:IsWuHun()
	return false
end

function SceneObj:IsBeast()
	return false
end

function SceneObj:IsTaskCallInfo()
	return false
end

function SceneObj:IsShuangShengTianShen()
	return false
end

function SceneObj:IsDeputyPet()
	return false
end

function SceneObj:IsBaby()
	return false
end

function SceneObj:IsBeauty()
	return false
end

function SceneObj:IsJumpPoint()
	return false
end

function SceneObj:IsGather()
	return false
end

function SceneObj:IsBaoJu()
	return false
end

function SceneObj:IsTruck()
	return false
end

function SceneObj:IsTrigger()
	return false
end

function SceneObj:IsEffect()
	return false
end

function SceneObj:IsBoat()
	return false
end

function SceneObj:IsFear()
	return false
end

function SceneObj:IsFollowObj()
	return false
end

function SceneObj:IsMarryObj()
	return false
end

function SceneObj:IsStatue()
	return false
end

function SceneObj:IsXiaoTianQuan()
    return false
end

function SceneObj:IsBossStone()
	return false
end

function SceneObj:IsXMZCar()
	return false
end

-- 是否传功中
function SceneObj:IsChuanGong()
	return false
end

function SceneObj:IsDuckRaceMonster()
	return false
end

function SceneObj:IsCityOwnerStatue()
	return false
end

function SceneObj:OnClick()
	if SceneObj.select_obj == self then
		return
	end

	if self:IsDeleted() then
		print_error("该对象已经被Delete掉了，什么代码还在调用，立查？？")
	end

	if SceneObj.select_obj then
		SceneObj.select_obj:CancelSelect()
		SceneObj.select_obj = nil
	end

	self.is_select = true
	SceneObj.select_obj = self

	local follow_ui = self:GetFollowUi()
	if nil ~= follow_ui and not (self:IsRole() and self:IsInvisible()) then
		follow_ui:ForceSetVisible(true)
	end
end

function SceneObj:CancelSelect()
	local old_is_selected = false
	if SceneObj.select_obj == self then
		old_is_selected = true
		SceneObj.select_obj = nil
	end
	self.is_select = false

	if nil ~= self.follow_ui and not (self:IsRole() and (self:IsInXunYou() or self:IsInvisible())) then
		self.follow_ui:CancelForceSetVisible()
	end

	if old_is_selected then
		GlobalEventSystem:Fire(ObjectEventType.CANCEL_SELECT, self)
	end
end

function SceneObj:SetFollowUIVisible(visible)
	local follow_ui = self:GetFollowUi()
	if nil ~= follow_ui
		and not (self:IsRole() and self:IsInvisible()) then
		follow_ui:VisibleChanged(visible)
	end
end

function SceneObj:CreateFollowUi(obj_type)
	if self:IsDeleted() then
		print_error("SceneObj IsDeleted")
		return
	end

	obj_type = obj_type or self.obj_type
	self.follow_ui = self.followui_class.New()
	self.follow_ui:SetOwnerObj(self)
	self.follow_ui:OnEnterScene(self.is_enter_scene)
	self.follow_ui:Create(obj_type)
	self:UpdateFollowUIRule()
	if self.draw_obj then
		self.follow_ui:SetFollowTarget(self.draw_obj.root.transform, self.draw_obj:GetName())
	end

end

function SceneObj:CreateUnderFollowUi(obj_type)
	if self:IsDeleted() then
		print_error("SceneObj IsDeleted")
		return
	end

	obj_type = obj_type or self.obj_type
	self.under_follow_ui = UnderFollowUi.New()
	self.under_follow_ui:OnEnterScene(self.is_enter_scene)
	self.under_follow_ui:Create(obj_type)
	self:UpdateFollowUIRule()
	if self.draw_obj then
		local under_point = self.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
		if under_point ~= nil then
			self.under_follow_ui:SetFollowTarget(under_point, self.draw_obj:GetName())
		else
			self.under_follow_ui:SetFollowTarget(self.draw_obj.root.transform, self.draw_obj:GetName())
		end
	end
end

function SceneObj:SetHpVisiable(value)
	if self.follow_ui then
        self.follow_ui:SetHpVisiable(value)
    end
end

--表演机器人不可选
function SceneObj:IsPerformer()
	return self.is_performer == true
end

--表演机器人不可选
function SceneObj:SetIsPerformer(value)
	self.is_performer = value
	if value then
		self:SetHpVisiable(false)
		local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
		main_part:ListenClick()
	end
end
----------------------------------------------------
-- 继承end
----------------------------------------------------

function SceneObj:GetName()
	return self.vo.name
end

function SceneObj:GetVo()
	return self.vo
end

function SceneObj:GetType()
	return self.obj_type
end

function SceneObj:GetObjId()
	return self.vo and self.vo.obj_id or -1
end

function SceneObj:GetObjKey()
	return self.vo and self.vo.obj_id or -1
end

function SceneObj:SetLogicPos(pos_x, pos_y, pos_z)
	if IS_DEBUG_BUILD then
		if self:IsMainRole() then
			if AStarFindWay ~= nil and AStarFindWay:IsBlock(pos_x, pos_y, false) then
				local scene_id = 0
				if Scene ~= nil and Scene.Instance ~= nil then
					scene_id = Scene.Instance:GetSceneId()
				end

				print_log("set logic pos is block!", scene_id, pos_x, pos_y)
			end
		end
	end

	self:SetLogicPosData(pos_x, pos_y)
	self.draw_obj:SetPosition(self.real_pos.x, self.real_pos.y, pos_z or 0)
end

function SceneObj:SetLogicPosData(pos_x, pos_y)
	self.logic_pos.x, self.logic_pos.y = pos_x, pos_y
	self.real_pos.x, self.real_pos.y = GameMapHelper.LogicToWorld(pos_x, pos_y)
end

function SceneObj:SetRealPos(pos_x, pos_y)
	self.real_pos.x, self.real_pos.y = pos_x, pos_y
	self.logic_pos.x, self.logic_pos.y = GameMapHelper.WorldToLogic(pos_x, pos_y)
end

function SceneObj:SetSpecialRealPos(pos_x,pos_y)
	self.real_pos.x, self.real_pos.y = pos_x, pos_y
	self.logic_pos.x, self.logic_pos.y = GameMapHelper.WorldToLogic(pos_x, pos_y)
	self.draw_obj:SetPosition(self.real_pos.x, self.real_pos.y)
end

function SceneObj:GetLogicPos()
	return self.logic_pos.x, self.logic_pos.y
end

function SceneObj:GetLogicPosTab()
	return self.logic_pos
end

function SceneObj:GetRealPos()
	return self.real_pos.x, self.real_pos.y
end

function SceneObj:GetRealPosOffset(target_pos)
	return target_pos
end

function SceneObj:GetLogicDir()
	local rota = self:GetRotation()
	if rota then
		local real_x, real_y = self:GetRealPos()
		local angle = math.rad(rota.eulerAngles.y)
		local read_dir_x = math.sin(angle) * 10
		local read_dir_y = math.cos(angle) * 10
		local dir_x, dir_y = GameMapHelper.WorldToLogic(real_x + read_dir_x, real_y + read_dir_y)
		local logic_x, logic_y = self:GetLogicPos()
		local vector = u3d.vec2(dir_x - logic_x, dir_y - logic_y)
		return u3d.v2Normalize(vector)
	end
	return u3d.vec2(0, 1)
end

function SceneObj:GetLogicDistance(logic_pos, is_sqrt)
	return GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, logic_pos.x, logic_pos.y, is_sqrt)
end

function SceneObj:GetLuaPosition()
	self.lua_position = Transform.GetPosition(self:GetRoot().transform, self.lua_position)
	return self.lua_position
end

function SceneObj:GetDrawObj()
	return self.draw_obj
end

function SceneObj:IsInBlock()
	return AStarFindWay:IsBlock(self.logic_pos.x, self.logic_pos.y)
end

function SceneObj:IsInSafeArea()
	return AStarFindWay:IsInSafeArea(self.logic_pos.x, self.logic_pos.y)
end

function SceneObj:IsWaterWay()
	return AStarFindWay:IsWaterWay(self.logic_pos.x, self.logic_pos.y)
end

function SceneObj:SetDirectionByXY(x, y, speed)
	if nil == self.draw_obj then
		return
	end

	local r_x, r_y = GameMapHelper.LogicToWorld(x, y)
	self.draw_obj:SetDirectionByXY(r_x, r_y, speed)
end

function SceneObj:SetRotate(x_angle, y_angle, z_angle)
	self.draw_obj:Rotate(x_angle, y_angle, z_angle)
end

function SceneObj:SetRotation(rotation)
	if not rotation then
		return
	end

	self.draw_obj:SetRotation(rotation)
end

function SceneObj:SetRotationByDir(dir)
	if not dir then
		return
	end

	local angle = 90 - math.deg(dir)
	self.draw_obj:SetRotation(Quaternion.Euler(0, angle, 0))
end

function SceneObj:GetRotation()
	return self.draw_obj:GetRotation()
end

function SceneObj:ChangeModel(part, bundle, name, callback, draw_model_type, extra_model_data, load_skin_callback)
	if not self.draw_obj or self.draw_obj:IsDeleted() then
		return
	end

	-- print_error("--ChangeModel--", SceneObjPartStr[part], name)
	--if part == SceneObjPart.Main and self:IsMainRole() then
	--	print_error("====>>>> SceneObj:ChangeModel", bundle, name, draw_model_type, extra_model_data)
	--end

	self.draw_obj:ChangeModel(part, bundle, name, callback, draw_model_type, extra_model_data, load_skin_callback)
end

function SceneObj:ResetSpecialState()
end

-- real_remove 是否真实移除
function SceneObj:RemoveModel(part, real_remove)
	if self.draw_obj then
		self.draw_obj:RemoveModel(part, real_remove)
	end
end

function SceneObj:ChangeObjWaitSyncAnimType(type, force_change)
	if self.draw_obj then
		self.draw_obj:ChangeWaitSyncAnimType(type, force_change)
	end
end

function SceneObj:GetAttr(key)
	return self.vo[key]
end

function SceneObj:SetAttr(key, value, param)
	if self:IsMainRole() and nil ~= RoleWGData.Instance then
		local old_value = self:GetAttr(key)
		self.vo[key] = value
		RoleWGData.Instance:AttrChanged(key, value, old_value)
	else
		self.vo[key] = value
	end
end

function SceneObj:SetObjId(obj_id)
	self.obj_id = obj_id
end

function SceneObj:ReloadUIName()
	if self.follow_ui ~= nil then
		self.follow_ui:SetName((self.vo and self.vo.name) or "", self)
	end
end

function SceneObj:GetFollowUi()
	if nil == self.follow_ui then
		self:CreateFollowUi()

		if nil == self.follow_ui then
			return nil
		end

		self.follow_ui:SetSpecialImage(false)
		self.follow_ui:SetGuildName("")
		self.follow_ui:SetLoverName("", self)
		self.follow_ui:SetWuXingType(0)
		self:ReloadUIName()
		self:_FlushFollowTarget()
	end

	return self.follow_ui
end

function SceneObj:GetUnderFollowUi()
	if nil == self.under_follow_ui then
		self:CreateUnderFollowUi()
		if nil == self.under_follow_ui then
			return nil
		end

		self:_FlushFollowTarget()
	end

	return self.under_follow_ui
end

function SceneObj:OnLoadSceneComplete()
	if self:IsDeleted() then
		return
	end

	self.draw_obj:SetPosition(self.real_pos.x, self.real_pos.y)

	if self.is_inited then
		self:OnEnterScene()
	end
end

function SceneObj:SetIsCanClick(is_can_click)
	self.is_can_click = is_can_click
end

function SceneObj:OnClicked(param)
	if not self.is_can_click then
		return
	end
	-- 拍照模式不响应点击操作
	if ScreenShotWGCtrl.Instance:IsOpenScreenShotView() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove() then
		return
	end

	if not self:IsDeleted() then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, self, "scene")
	end
end

function SceneObj:OnModelLoaded(part, obj, obj_class)
	if self:IsMainRole() then
		if part == SceneObjPart.Main then
			local main_part =self.draw_obj:GetPart(SceneObjPart.Main)
			main_part:ListenEvent("jump4/effect", BindTool.Bind(self.OnJump4Effect, self))
		end
	end

	if part ~= SceneObjPart.Main then
		return
	end

	self:OnMainPartLoadCallBack()

    --暂时只对Role有需求
    if self:IsRole() then
        GlobalEventSystem:Fire(ObjectEventType.OBJ_MAIN_PART_LOADED, part, obj, self)
    end

	self:_FlushFollowTarget()
end

function SceneObj:OnModelRemove(part, obj)
	if part == SceneObjPart.Main then
		self:_FlushFollowTarget()
	end
end

function SceneObj:_FlushFollowTarget()
	if self:IsDeleted() or nil == self.follow_ui then
		return
	end

	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	local point = part:GetAttachPoint(AttachPoint.UI)
	local local_ui = self.follow_ui:GetLocalUi()
	if nil == point or not self:GetVisiable() then
		local fixed_transform = self.draw_obj:GetTransfrom()
		self.follow_ui:SetFollowTarget(fixed_transform, self.draw_obj:GetName())
		if local_ui then
			if local_ui.y ~= 100 then
				self.follow_ui:SetLocalUI(0, 100, 0)
			else
				self.follow_ui:UpdateFollowPos()
			end
		else
			self.follow_ui:SetLocalUI(0, 100, 0)
		end
		self.follow_ui:SetNameTextPosition()
		self.follow_ui:SetHpBarLocalPosition(0, 10, 0)
	else
		self.follow_ui:SetFollowTarget(point, self.draw_obj:GetName())
		self.follow_ui:SetLocalUI(0, 0, 0)
		self.follow_ui:SetNameTextPosition()
		self.follow_ui:SetHpBarLocalPosition(0, 10, 0)
	end

	if self.under_follow_ui ~= nil then
		local under_point = self.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
		if nil == under_point or not self:GetVisiable() then
			local fixed_transform = self.draw_obj:GetTransfrom()
			self.under_follow_ui:SetFollowTarget(fixed_transform, self.draw_obj:GetName())
			if local_ui then
				self.under_follow_ui:UpdateFollowPos()
			else
				self.under_follow_ui:SetLocalUI(0, 100, 0)
			end
		else
			self.under_follow_ui:SetFollowTarget(under_point, self.draw_obj:GetName())
			self.under_follow_ui:SetLocalUI(0, 0, 0)
		end
	end
end

function SceneObj:OnJumpStart()

end

function SceneObj:OnJumpEnd()

end

function SceneObj:OnJump4Effect()
end

function SceneObj:GetActorWGCtrl()
	return self.actor_ctrl
end

function SceneObj:SetActorConfigPrefabData(data, dynamic_data)
	if data ~= nil then
		self.has_set_prefab_data = true
	end
	
	if self.actor_ctrl then
		self.actor_ctrl:SetPrefabData(data)
	end

	if self.actor_trigger then
		self.actor_trigger:SetPrefabData(data, nil, dynamic_data)
	end
end

function SceneObj:GetActorTrigger()
	return self.actor_trigger
end

function SceneObj:DoSpecialJump(x,y,callback)
	-- body
end

-- 血量恢复
function SceneObj:PlayAddHpEffect()

end

function SceneObj:IsInXunYou()
    return false
end

function SceneObj:VisibleChanged(visible)
	if nil ~= self.draw_obj then
		self.draw_obj:SetVisible(visible)
	end

	self:UpdateFollowUIRule()
	self:UpdateShadowRule()
	self:_FlushFollowTarget()
end

function SceneObj:UpdateFollowUIRule()
	if self.followui_hide_when_self_hide then
		local follow_ui = self:GetFollowUi()
		if nil ~= follow_ui then
			if not self:GetVisiable() then
				follow_ui:ForceSetVisible(false)
			else
				follow_ui:CancelForceSetVisible()
			end
		end
	end
end

function SceneObj:ForceSetFollowUIVisible(is_show)
	local follow_ui = self:GetFollowUi()
	if nil == follow_ui then
		return
	end

	follow_ui:ForceSetVisible(is_show)
end

function SceneObj:UpdateShadowRule()
	if self.shadow and self.shadow_hide_when_self_hide then
		if not self:GetVisiable() then
			self.shadow:ForceSetVisible(false)
		else
			self.shadow:CancelForceSetVisible()
		end
	end
end

function SceneObj:ChangeDetailLevel(level)
	local draw_obj = self:GetDrawObj()
	if draw_obj then
		return draw_obj:SetCurDetailLevel(level)
	end

	return false
end

function SceneObj:SetIsDisableAllAttachEffects(disable)
	if self.draw_obj then
		self.draw_obj:SetIsDisableAllAttachEffects(disable)
	end
end

function SceneObj:UpdateGameObjectAttachVisible()
	if self.gameobject_attach_handle then
		self.gameobject_attach_handle:RefreshVisiable()
	end
end

function SceneObj:IsVaildObj()
	local is_vaild = true

	if self.draw_obj == nil
		or self.draw_obj:GetRoot() == nil
		or self.draw_obj:GetPart(SceneObjPart.Main) == nil
		or self.draw_obj:GetPart(SceneObjPart.Main):GetObj() == nil then

		is_vaild = false
	end

	return is_vaild
end

function SceneObj:SetUnderShowInfo(data, is_force)
    self.under_info = data
    self:CheckUnderShow(is_force)
end

function SceneObj:CheckUnderShow(is_force)
    local is_show = false
    if self.under_info ~= nil then
        is_show = true
    end

    if self.under_follow_ui ~= nil then
        self.under_follow_ui:SetUnderBarVisiable(is_show)
        if is_show then
        	self.under_follow_ui:SetUnderBarShowInfo(self.under_info.show_type, self.under_info.param, self.under_info.timer_str, self.under_info.call_back)
        end
    else
        if is_show then
            local under_follow_ui = self:GetUnderFollowUi()
            if under_follow_ui ~= nil then
                under_follow_ui:SetUnderBarShowInfo(self.under_info.show_type, self.under_info.param, self.under_info.timer_str, self.under_info.call_back)
            	if is_force then
            		under_follow_ui:ForceSetVisible(true)
            	end
            end
        end
    end
end

function SceneObj:OnMainPartLoadCallBack()
	self:CheckUnderShow()
end

function SceneObj:IsWaterRipple()
	return AStarFindWay:IsWaterRipple(self.logic_pos.x, self.logic_pos.y)
end

function SceneObj:EnterWater(is_in_water)
end

function SceneObj:IsWeaponOwnAnim()
    return false
end

-- 是否需要部位动画同步
function SceneObj:GetIsNeedPartAnimSync()
	if self:IsWeaponOwnAnim() then
		return true
	end

	return false
end

function SceneObj:CrossAction(part_type, action, check_same_action, time, is_force)
	if part_type == nil or action == nil then
		return
	end

	if self:IsDeleted() then
		return
	end

	if self:IsChuanGong() then
		return
	end

	if self.draw_obj then
		self.draw_obj:CrossAction(part_type, action, check_same_action, time, is_force)
	end
end

function SceneObj:SetActionTrigger(part_type, name)
	if part_type == nil or name == nil then
		return
	end

	if self:IsDeleted() then
		return
	end

	if self.draw_obj then
		self.draw_obj:SetActionTrigger(part_type, name)
	end
end

function SceneObj:SetActionBool(part_type, name, bool)
	if part_type == nil or name == nil then
		return
	end

	if self:IsDeleted() then
		return
	end

	if self.draw_obj then
		self.draw_obj:SetActionBool(part_type, name, bool)
	end
end

function SceneObj:SetActionFloat(part_type, name, float_value)
	if part_type == nil or name == nil then
		return
	end

	if self:IsDeleted() then
		return
	end

	if self.draw_obj then
		self.draw_obj:SetActionFloat(part_type, name, float_value)
	end
end













-------------------------------------------GameObjectAttachHandle-----------------------------------------------

GameObjectAttachHandle = GameObjectAttachHandle or BaseClass(ReuseableShieldHandle)
function GameObjectAttachHandle:__init(scene_obj)
	self:Init(scene_obj)
	self.shield_obj_type = ShieldObjType.GameObjectAttach
end

function GameObjectAttachHandle:__delete()
	self:Clear()
end

function GameObjectAttachHandle:Init(scene_obj)
	self.scene_obj = scene_obj
end

function GameObjectAttachHandle:Clear()
	self.scene_obj = nil
end

function GameObjectAttachHandle:VisibleChanged(visible)
	if self.scene_obj then
		self.scene_obj:SetIsDisableAllAttachEffects(not visible)
	end
end

function GameObjectAttachHandle:GetSceneObj()
	return self.scene_obj
end
