TipSystemBeastEquipScrollView = TipSystemBeastEquipScrollView or BaseClass(SafeBaseView)

local SPEED = 100						-- 字幕滚动的速度(像素/秒)

function TipSystemBeastEquipScrollView:__init()
	self.view_layer = UiLayer.PopTop
	self.calc_active_close_ui_volume = false
	self:AddViewResource(0, "uis/view/miscpre_load_prefab", "SystemBeastAlchemyNoticeScrollView")
	self.view_name = "TipSystemBeastEquipScrollView"
	self.is_open = false
	self.str_list = {}
	self.current_index = 1
	self.total_count = 0
	self.can_do_fade = false
	self.calculate_time_quest = nil
end

function TipSystemBeastEquipScrollView:__delete()
	if nil ~= self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
		self.calculate_time_quest = nil
	end

	self.is_open = false
end

function TipSystemBeastEquipScrollView:LoadCallBack()
	self.text_trans = self.node_list["EmojiText"].rect
	self.mask_width = self.node_list["Mask"].rect.sizeDelta.x
	self.is_open = true

	XUI.AddClickEventListener(self.node_list.item_button, BindTool.Bind(self.OnClickOpenShowItem, self))                 -- 玩家操作
	XUI.AddClickEventListener(self.node_list.contact_user, BindTool.Bind(self.OnClickContactOperate, self))                 -- 玩家操作
end

function TipSystemBeastEquipScrollView:ReleaseCallBack()
	-- 清理变量和对象
	self.text_trans = nil
	self.tweener = nil
	self.equip_info = nil
	self.equip_item_id = nil
end

function TipSystemBeastEquipScrollView:OnShieldHearsay(value)
	if value then
		self:Close()
	end
end

function TipSystemBeastEquipScrollView:OpenCallBack()
	if nil == self.shield_hearsay then
		self.shield_hearsay = GlobalEventSystem:Bind(SettingEventType.CLOSE_HEARSAY, BindTool.Bind1(self.OnShieldHearsay, self))
	end

	self.tweener = nil
end

function TipSystemBeastEquipScrollView:CloseCallBack()
	if self.shield_hearsay then
		GlobalEventSystem:UnBind(self.shield_hearsay)
		self.shield_hearsay = nil
	end

	if self.tweener then
		self.tweener:Pause()
	end
end

function TipSystemBeastEquipScrollView:SetNotice(str)
	local shield_hearsay = SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_HEARSAY)
	if shield_hearsay then
		return
	end

	if not self.is_open then
		self:Open()
		self.str_list = {}
		self.current_index = 1
		self.total_count = 0
		self:AddNotice(str)
		self:Flush()
	else
		self:AddNotice(str)
	end
end

function TipSystemBeastEquipScrollView:AddNotice(str)
	self.total_count = self.total_count + 1
	self.str_list[self.total_count] = str
end

function TipSystemBeastEquipScrollView:OnFlush()
	if(self.current_index <= self.total_count) then
		local str = self.str_list[self.current_index]
		EmojiTextUtil.ParseRichText(self.node_list.EmojiText.emoji_text, str, 20)
		self.text_trans.anchoredPosition = Vector2(0, 0)

		self:SplitContentParseMark(str)
		self.calculate_time_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Calculate, self), 0.1)
	end
end

function TipSystemBeastEquipScrollView:SplitContentParseMark(str)
	local content = CommonDataManager.ParseGameName(str)
	local i, j = 0, 0
	local element_list = {}
	local last_pos = 1
	for loop_count = 1, 100 do
		i, j = string.find(content, "({.-})", j + 1)-- 匹配规则{face;20} {item;26000}
		if nil == i or nil == j then
			if last_pos <= #content then
				table.insert(element_list, {0, string.sub(content, last_pos, -1)})
			end
			break
		else
			if 1 ~= i and last_pos ~= i then
				table.insert(element_list, {0, string.sub(content, last_pos, i - 1)})
			end
			table.insert(element_list, {1, string.sub(content, i, j)})
			last_pos = j + 1
		end
	end

	local role_id, equip_item_id, equip_slot_lv, word_list
	local rule
	local equip_info = {}
	equip_info.item_id = equip_item_id
	equip_info.index = 0
	equip_info.words_list = {}

	for i2, v2 in ipairs(element_list) do
		if 0 ~= v2[1] then
			rule = string.sub(v2[2], 2, -2)
			local rule_list = Split(rule, ";")
			local mark = rule_list[1]

			if mark == "r" then		-- 获取玩家信息
				role_id = tonumber(rule_list[2]) or 0
			elseif mark == "BeastEquipShare" then	-- 获取装备信息
				local equip_str_list = string.split(rule_list[2], ",")
				equip_item_id = tonumber(equip_str_list[1]) or 1
				equip_slot_lv = tonumber(equip_str_list[2]) or 1

				if rule_list[3] ~= nil and rule_list[3] ~= "" then
					local start_index = 0
					local words_str_list = string.split(rule_list[3], ",")
					for i, v in ipairs(words_str_list) do
						equip_info.words_list[start_index] = {}
						equip_info.words_list[start_index].words_seq = tonumber(v) or 1
						equip_info.words_list[start_index].can_replace_words = tonumber(v) or 1
						start_index = start_index + 1
					end
				end
			end
		end
	end

	self.equip_info = equip_info
	self.equip_item_id = equip_item_id
	local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(equip_item_id)
	self.node_list.item_icon.image:LoadSpriteAsync(bundle, asset)

	if role_id ~= nil then
		self.role_id = role_id

		if SocietyWGData.Instance:GetIsMyFriend(role_id) then	-- 私聊
			self.node_list.contact_user_text.text.text = Language.Menu.PrivateChat
		else	-- 添加好友
			self.node_list.contact_user_text.text.text = Language.Menu.AddFriend
		end
	end
end

-- 计算滚动的时间的位置
function TipSystemBeastEquipScrollView:Calculate()
	if self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
	end
	self.calculate_time_quest = nil

	if nil == self.text_trans then
		return
	end

	local str = self.str_list[self.current_index]
	EmojiTextUtil.ParseRichText(self.node_list.EmojiText.emoji_text, str, 20)

	local width = self.text_trans.sizeDelta.x
	width = width + self.mask_width
	local duration = width / SPEED
	-- print_log(width,self.mask_width,duration,self.mask_width_cost_time)
	local tweener = self.text_trans:DOAnchorPosX(-width, duration, false)
	self.tweener = tweener
	tweener:SetEase(DG.Tweening.Ease.Linear)
	tweener:OnComplete(BindTool.Bind(self.OnMoveEnd, self))
end

function TipSystemBeastEquipScrollView:OnMoveEnd()
	self.current_index = self.current_index + 1
	if(self.current_index > self.total_count) then
		self:Close()
	else
		self:Flush()
	end
end

-- 查看当前的物品
function TipSystemBeastEquipScrollView:OnClickOpenShowItem()
	if self.equip_info == nil or self.equip_item_id == nil then
		return
	end

	local item_data = {
		item_id = self.equip_item_id, 
		equip_info = self.equip_info, 
		is_bag_equip = true, 
	}
    TipWGCtrl.Instance:OpenItem(item_data, ItemTip.BEAST_ALCHEMY_EQUIP_BAG)
end

-- 当前传闻操作
function TipSystemBeastEquipScrollView:OnClickContactOperate()
	if self.role_id == nil then
		return
	end

	if SocietyWGData.Instance:GetIsMyFriend(self.role_id) then	-- 私聊
		SocietyWGCtrl.Instance:Flush("find_role_id", {self.role_id})
	else	-- 添加好友
		if self.role_id ~= 0 and self.role_id ~= RoleWGData.Instance:GetUUid().temp_low then
			SocietyWGCtrl.Instance:IAddFriend(self.role_id)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.ApplyFriendSuccess)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NotAddSelf)
		end
	end
end
