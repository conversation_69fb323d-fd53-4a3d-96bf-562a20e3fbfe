CustomActionWindowView = CustomActionWindowView or BaseClass(SafeBaseView)

local NARMAL_POS = Vector2(0, 0)

function CustomActionWindowView:__init()
	self:SetMaskBg(false, false)
    self:ClearViewTween()
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.view_layer = UiLayer.Guide
    self.view_name = "CustomActionWindowView"
    self.self_control_rendring = true
    self:AddViewResource(0, "uis/view/custom_action_ui_prefab", "layout_custom_action_window")
end

function CustomActionWindowView:LoadCallBack()
    self.ac_sub_list = AsyncListView.New(CustomActionWindowSubRender, self.node_list.ac_sub_list)
	self.ac_sub_list:SetSelectCallBack(BindTool.Bind(self.OnClickSubHandler, self))

    if not self.ac_item_grid then
        self.ac_item_grid = AsyncBaseGrid.New()
        self.ac_item_grid:SetStartZeroIndex(false)
        self.ac_item_grid:CreateCells({
            col = 3,
            list_view = self.node_list["ac_item_grid"],
            itemRender = CustomActionWindowSmallRender,
            assetBundle = "uis/view/custom_action_ui_prefab",
            assetName = "custom_action_window_cell",
            change_cells_num = 1,
        })
        self.ac_item_grid:SetSelectCallBack(BindTool.Bind(self.OnClickSmallHandler, self))
    end
end

function CustomActionWindowView:ReleaseCallBack()
    if self.ac_sub_list then
        self.ac_sub_list:DeleteMe()
        self.ac_sub_list = nil
    end

    if self.ac_item_grid then
        self.ac_item_grid:DeleteMe()
        self.ac_item_grid = nil
    end
end

function CustomActionWindowView:OnClickSubHandler(cell)
    if not cell or not cell:GetData() then
        return
    end

    local data = cell:GetData()
    self.ac_item_grid:SetDataList(data.child_list)
end

function CustomActionWindowView:OnClickSmallHandler(cell)
    if not cell or not cell:GetData() then
        return
    end

    if ViewManager.Instance:IsOpen(GuideModuleName.ScreenShotView) then
        if ScreenShotWGData.Instance:GetIsSelect(1) == 0 then
            return
        end
    end

    local data = cell:GetData()
    if data.cfg.is_stay_tuned == 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CustomAction.StayTunedStr)
        return
    end

    local level = CustomActionData.Instance:GetRoleActionLevel(data.action_id)
    if level < 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)

        ViewManager.Instance:Open(GuideModuleName.RoleBranchView, TabIndex.custom_action, "all", {force_big_type = data.cfg.action_type, force_small_type = data.cfg.action_id})
        return
    end

    local main_role = Scene.Instance:GetMainRole()
     if main_role and CustomActionCtrl.Instance:CheckObjIsCanDoAction(main_role, true) then
        local is_riding_no_fight_mount = main_role:IsRidingNoFightMount()
        if is_riding_no_fight_mount then
            MountWGCtrl.Instance:SendMountGoonReq(0)
        end

        CustomActionCtrl.Instance:SendOperateReq(ROLEACTION_OPERA_TYPE.PERFORM, data.action_id)
     end
end

function CustomActionWindowView:SetPanelPosition(vector2)
    NARMAL_POS = vector2 or Vector2(0, 0)
end

function CustomActionWindowView:OnFlush()
    local toggle_list = CustomActionData.Instance:GetActionShowList()
    self.ac_sub_list:SetDataList(toggle_list)

    self.node_list.content.rect.anchoredPosition = NARMAL_POS
end





CustomActionWindowSubRender = CustomActionWindowSubRender or BaseClass(BaseRender)
function CustomActionWindowSubRender:OnFlush()
    if self.data == nil then
		return
	end

    self.node_list.normal_text.text.text = self.data.cfg.name
    self.node_list.select_text.text.text = self.data.cfg.name
end

function CustomActionWindowSubRender:OnSelectChange(is_select)
    self.node_list.normal_img:SetActive(not is_select)
    self.node_list.select_img:SetActive(is_select)
end




CustomActionWindowSmallRender = CustomActionWindowSmallRender or BaseClass(BaseRender)
function CustomActionWindowSmallRender:OnFlush()
	if self.data == nil then
		return
	end

    local bundle, asset = ResPath.GetCustomActionImg("a3_dzdz_icon_" .. self.data.action_id)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)

    local level = CustomActionData.Instance:GetRoleActionLevel(self.data.action_id)
    self.node_list["name"].text.text = self.data.cfg.name

    local is_stay_tuned = self.data.cfg.is_stay_tuned == 1
    self.node_list["lock"]:SetActive(not is_stay_tuned and level < 0)
    self.node_list["stay_tuned_flag"]:SetActive(is_stay_tuned)
end


