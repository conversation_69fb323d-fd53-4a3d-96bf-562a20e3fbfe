﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class OcclusionObjectWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(OcclusionObject), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Items", get_Items, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Items(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			OcclusionObject obj = (OcclusionObject)o;
			OcclusionObject.OcclusionItme[] ret = obj.Items;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Items on a nil value");
		}
	}
}

