DrawPartRender = DrawPartRender or BaseClass()

local color_white = Color.New(1, 1, 1, 1)
local vec4_zero = Vector4(0, 0, 0, 0)

function DrawPartRender:__init()
	self.partObj = nil
	self.quality_override_level = -1
	self.is_disable_effect = false
	self.is_gray = false
end

function DrawPartRender:__delete()
	-- 回对象池要恢复
	self:Reset()
	self.actor_render = nil
end

function DrawPartRender:Reset()
	-- 回对象池要恢复
	if not IsNil(self.actor_render) then
		self.actor_render:SetIsHdTexture(false)
		self.actor_render:SetIsCastShadow(false)
		self.actor_render:SetIsDisableAllAttachEffects(false)
		self.actor_render:StopAllRenderEffects()
		self.actor_render:ResetMainTexture()
		self.actor_render:SetIsLerpProbe(false)
		self.actor_render:ResetRendererRenderingLayerMask()
		self:SetIsCulled(true)
	end

	self.actor_render = nil
end

function DrawPartRender:SetActorRender(actor_render)
	self.actor_render = actor_render
end

function DrawPartRender:GetActorRender()
	return self.actor_render
end

function DrawPartRender:SetMaterialQuality(material_quality)
	-- if IsNil(self.actor_render) then
	-- 	return
	-- end

	-- self.actor_render:SetMaterialQuality(material_quality)
end

function DrawPartRender:SetIsHdTexture(is_hd_texture)
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetIsHdTexture(is_hd_texture)
end

function DrawPartRender:SetIsCastShadow(is_cast_shadow)
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetIsCastShadow(is_cast_shadow)
end

function DrawPartRender:SetRendererRenderingLayerMask(layer, isOpen)
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetRendererRenderingLayerMask(layer, isOpen)
end

-- 是否关掉AttachEffects
function DrawPartRender:SetIsDisableAllAttachEffects(is_disable_effect)
	self.is_disable_effect = is_disable_effect
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetIsDisableAllAttachEffects(is_disable_effect, self.quality_override_level)
end

-- 重载AttachEffects上的QualityControl
function DrawPartRender:SetQualityControlOverrideLevel(quality_override_level)
	self.quality_override_level = quality_override_level
	if IsNil(self.actor_render) then
		return
	end

    self.actor_render:SetIsDisableAllAttachEffects(self.is_disable_effect, quality_override_level)
end

-- 设置材质球
function DrawPartRender:SetRenderMaterial(material)
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetRenderMaterial(material)
end

-- 设置变灰
function DrawPartRender:SetIsGray(value)
	if IsNil(self.actor_render) then
		return
	end

	-- self.actor_render:SetIsGray(value)

	----[[ 换包 or 新项目再修复
	if self.is_gray == value then
		return
	end

	self.is_gray = value
	self.actor_render:SetIsGray(value)
	--]]
end

-- 设置颜色
function DrawPartRender:SetIsMultiColor(color)
	if IsNil(self.actor_render) then
		return
	end

	if nil == color then
		self.actor_render:SetIsMultiColor(false, color_white)
	else
		self.actor_render:SetIsMultiColor(true, color)
	end
end

function DrawPartRender:PlayDissolveEffect()
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:PlayDissolveEffect()
end

-- 是否需要优化动画
function DrawPartRender:IsNeedAnimatorOptimize()
	if not IsNil(self.actor_render) then
		return nil ~= self.actor_render:GetAnimator()
	end

	return false
end

function DrawPartRender:SetIsCulled(is_culled)
	if IsNil(self.actor_render) or not self:IsNeedAnimatorOptimize() then
		return
	end

	self.actor_render:SetIsCulled(is_culled)
end

function DrawPartRender:TrySetMainTexture(img)
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetMainTexture(img)
end

function DrawPartRender:TryResetMainTexture()
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:ResetMainTexture()
end

function DrawPartRender:TrySetLerpProbe(is_lerp, is_force)
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetIsLerpProbe(is_lerp, is_force or false)
end

-- 设置drawPart的物体层级
function DrawPartRender:TrySetMainGameObjectLayer(layer)
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetRendererGameObjectLayer(layer)
end

-- 还原设置物体层级
function DrawPartRender:TrySetMainDefaultGameObjectLayer()
	if IsNil(self.actor_render) then
		return
	end

	self.actor_render:SetDefaultRendererGameObjectLayer()
end
