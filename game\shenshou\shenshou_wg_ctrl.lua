require("game/shenshou/shenshou_view")
require("game/shenshou/shenshou_wg_data")
-- require("game/shenshou/shenshou_attr")
require("game/shenshou/shenshou_qianghua")
-- require("game/shenshou/shenshou_equip_bag")
require("game/shenshou/shenshou_cell")
-- require("game/shenshou/shenshou_item_render")
require("game/shenshou/shenshou_equip_tip")
-- require("game/shenshou/shenshou_skill_desc")
require("game/shenshou/shenshou_equip_contrast")
require("game/shenshou/shenshou_extrazhuzhan_tip")
require("game/shenshou/shenshou_stuff_tip")
require("game/shenshou/shenshou_overview")
require("game/shenshou/shenshou_skill_overview")
require("game/shenshou/shenshou_bag_view")
require("game/shenshou/shenshou_decompose_view")

ShenShouWGCtrl = ShenShouWGCtrl or BaseClass(BaseWGCtrl)

function ShenShouWGCtrl:__init()
	if ShenShouWGCtrl.Instance ~= nil then
		ErrorLog("[ShenShouWGCtrl] Attemp to create a singleton twice !")
	end
	ShenShouWGCtrl.Instance = self

	self.view = ShenShouView.New(GuideModuleName.ShenShou)
	self.data = ShenShouWGData.New()
	self.shenshou_bag_view = ShenShouBagView.New()
	self.shenshou_skill_overview = ShenShouSkillOverview.New()
	self.shenshou_overview = ShenShouOverview.New()
	self.shenshou_extrazhuzhan_tip = ShenShouExtraZhuZhanTip.New()
	self.shenshou_stuff_tip = ShenShouStuffTip.New()
	self.shenshou_qianghua_view = ShenShouQiangHuaView.New(GuideModuleName.ShenShouQiangHua)
	self.shenshou_equip_contrast = ShenShouContrastTip.New()
	self.shenshou_decompose_view = ShenShouDeComposeView.New()

	self.delay_notice_list = {}

	self:RegisterAllProtocols()
    Runner.Instance:AddRunObj(self, 8)

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
	RemindManager.Instance:Bind(self.remind_change, RemindName.ShenShouRemind)

    self.role_data_change = BindTool.Bind(self.AttrChangeCallBack, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

	-- self.shenshou_equip_bag_view = ShenShouEquipBagView.New(GuideModuleName.ShouhunBag)
	-- self.choose_btn_view = ChooseBtnView.New()
	-- self.choose_btn2_view = ChooseBtn2View.New()
	-- -- self.choose_qhbtn_view = ChooseQhBtnView.New()
	-- self.shenshou_skill_desc = ShenShouSkillDescView.New()
	-- --self.shenshou_strengthen_view = ShanHaiJingView.New(GuideModuleName.ShenShouStrengthenView)
    -- self:RegisterAllProtocols()
    -- self.delay_notice_list = {}
end

function ShenShouWGCtrl:__delete()

    Runner.Instance:RemoveRunObj(self)
	ShenShouWGCtrl.Instance = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	if self.remind_change then
		RemindManager.Instance:UnBind(self.remind_change)
		self.remind_change = nil
	end

	if nil ~= self.shenshou_equip_contrast then
		self.shenshou_equip_contrast:DeleteMe()
		self.shenshou_equip_contrast = nil
	end

	if nil ~= self.shenshou_extrazhuzhan_tip then
		self.shenshou_extrazhuzhan_tip:DeleteMe()
		self.shenshou_extrazhuzhan_tip = nil
	end

	if nil ~= self.shenshou_stuff_tip then
		self.shenshou_stuff_tip:DeleteMe()
		self.shenshou_stuff_tip = nil
	end

	if self.shenshou_overview then
		self.shenshou_overview:DeleteMe()
		self.shenshou_overview = nil
	end

	if self.shenshou_skill_overview then
		self.shenshou_skill_overview:DeleteMe()
		self.shenshou_skill_overview = nil
	end

	if self.shenshou_bag_view then
		self.shenshou_bag_view:DeleteMe()
		self.shenshou_bag_view = nil
	end

	if self.shenshou_decompose_view then
		self.shenshou_decompose_view:DeleteMe()
		self.shenshou_decompose_view = nil
	end

	if self.tunshi_timer then
		GlobalTimerQuest:CancelQuest(self.tunshi_timer)
		self.tunshi_timer = nil
	end
end

--------------------------------------------protocol_start------------------------------------------------
function ShenShouWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSShenshouOperaReq)
	self:RegisterProtocol(CSSHenshouReqStrength)
	self:RegisterProtocol(CSShenshouDecompos)
	self:RegisterProtocol(SCShenshouBackpackInfo, "OnShenshouBackpackInfo")
	self:RegisterProtocol(SCShenshouListInfo, "OnShenshouListInfo")
	self:RegisterProtocol(SCShenshouUpdateInfo, "OnSCShenshouUpdateInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function ShenShouWGCtrl:MainuiOpenCreate()
	self:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ALL_INFO)
end

-- 神兽分解 
function ShenShouWGCtrl:OnCSShenshouDecompos(is_double, count, msg_item)
    local protocol = ProtocolPool.Instance:GetProtocol(CSShenshouDecompos)
	protocol.is_double = is_double or 0
    protocol.count = count or 0
    protocol.msg_item = msg_item or {}
    protocol:EncodeAndSend()
end

-- 神兽操作请求
function ShenShouWGCtrl:SendShenshouOperaReq(opera_type, param_1, param_2, param_3, param_4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShenshouOperaReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol.param_4 = param_4 or 0
	protocol:EncodeAndSend()
end

-- 神兽请求强化装备
function ShenShouWGCtrl:SendSHenshouReqStrength(soul_ring_seq, equip_index, is_double_shuliandu, destroy_num, destroy_backpack_index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSHenshouReqStrength)
	protocol.soul_ring_seq = soul_ring_seq or 0
	protocol.equip_index = equip_index or 0
	protocol.is_double_shuliandu = is_double_shuliandu or 0
	protocol.destroy_num = destroy_num or 0
	protocol.destroy_backpack_index_list = destroy_backpack_index_list
	protocol:EncodeAndSend()
end

-- 神兽背包信息
function ShenShouWGCtrl:OnShenshouBackpackInfo(protocol)
	if protocol.is_full_backpack == 1 then
		self.data:SetShenShouBagInfo(protocol)
	elseif protocol.is_full_backpack == 0 then
		self.data:UpdateShenShouBagInfo(protocol)

		local put_reason = protocol.reason_type

		if not GodGetRewardWGCtrl.Instance:ViewIsOpen() then
        	if protocol.grid_num > 0 then
                if put_reason == PUT_REASON_TYPE.PUT_REASON_CSA_YANHUA_SHENGDIAN_REWARD
                or put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD then
	                local temp_tb = {}
	                temp_tb.change_item_id = protocol.grid_list[1].item_id
	                temp_tb.put_reason = put_reason
	                temp_tb.add_num = 1
	                if put_reason == PUT_REASON_TYPE.PUT_REASON_CSA_YANHUA_SHENGDIAN_REWARD then
                        temp_tb.notice_time_stamp = Status.NowTime + MergeFireworksWGData.Instance:GetDelayTime()
                    elseif put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD then--节日活动烟花盛典扭蛋机
                        temp_tb.notice_time_stamp = Status.NowTime + FestivalFireworksWGData.Instance:GetDelayTime()
	                else
	                    temp_tb.notice_time_stamp = Status.NowTime
	                end
	                table.insert(self.delay_notice_list, temp_tb)
	            else
	                local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.grid_list[1].item_id)
	                if item_cfg then
	                    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), 1))
	                end
	            end
        	end
		end
	end

	if self:GetIsAutoTunShi() then
		if not self.tunshi_timer then --自动吞噬延迟一秒  防止卡顿
			self.tunshi_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.AutoTunShiEquip, self), 1)
		end
	end

	RemindManager.Instance:Fire(RemindName.ShenShouRemind)
	ViewManager.Instance:FlushView(GuideModuleName.ShenShou)

	if self.shenshou_qianghua_view:IsOpen() then
		self.shenshou_qianghua_view:Flush()
	end

	if self.shenshou_bag_view:IsOpen() then
		self.shenshou_bag_view:Flush()
	end

	if self.shenshou_decompose_view:IsOpen() then
		self.shenshou_decompose_view:Flush()
	end

	ComposeWGCtrl.Instance:OnShenShouItemChange()
end

-- 神兽信息
function ShenShouWGCtrl:OnShenshouListInfo(protocol)
	local old_exp = self.data:GetShenShouExp()
	local new_exp = protocol.shenshou_exp

	if old_exp > 0 and new_exp > old_exp then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenShou.SoulRingEquipExp, (new_exp - old_exp)))
	end

	self.data:SetShenShouListInfo(protocol)

	RemindManager.Instance:Fire(RemindName.ShenShouRemind)
	ViewManager.Instance:FlushView(GuideModuleName.ShenShou)

	if self.shenshou_qianghua_view:IsOpen() then
		self.shenshou_qianghua_view:Flush()
	end

	if self.shenshou_bag_view:IsOpen() then
		self.shenshou_bag_view:Flush()
	end

	ComposeWGCtrl.Instance:OnShenShouItemChange()
end


function ShenShouWGCtrl:OnSCShenshouUpdateInfo(protocol)
	self.data:UpdateShenShouListInfo(protocol)

	RemindManager.Instance:Fire(RemindName.ShenShouRemind)
	ViewManager.Instance:FlushView(GuideModuleName.ShenShou)

	if self.shenshou_qianghua_view:IsOpen() then
		self.shenshou_qianghua_view:Flush()
	end

	if self.shenshou_bag_view:IsOpen() then
		self.shenshou_bag_view:Flush()
	end

	ComposeWGCtrl.Instance:OnShenShouItemChange()
end

function ShenShouWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:IsSHenShouActiveStuffItemId(change_item_id) then
			RemindManager.Instance:Fire(RemindName.ShenShouRemind)
			ViewManager.Instance:FlushView(GuideModuleName.ShenShou)
		end
	end
end

function ShenShouWGCtrl:Update(now_time, elapse_time)
	if #self.delay_notice_list > 0 then
		for i = #self.delay_notice_list, 1, -1 do
			if now_time > self.delay_notice_list[i].notice_time_stamp then
                local t = table.remove(self.delay_notice_list, i)
                local item_cfg = ItemWGData.Instance:GetItemConfig(t.change_item_id)

                if item_cfg then
                    local str = string.format(Language.SysRemind.AddItem, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), t.add_num)
                    SysMsgWGCtrl.Instance:ErrorRemind(str)
                end
			end
		end
	end
end

function ShenShouWGCtrl:RemindChangeCallBack(remind_name, num)
	if remind_name == RemindName.ShenShouRemind then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHENSHOU_BAR, num, function ()
			ViewManager.Instance:Open(GuideModuleName.ShenShou)
			return true
		end)
	end
end

function ShenShouWGCtrl:AttrChangeCallBack(key,value)
	if key == "level" then
		RemindManager.Instance:Fire(RemindName.ShenShouRemind)
		ViewManager.Instance:FlushView(GuideModuleName.ShenShou)
	end
end

--获得是否选中自动吞噬
function ShenShouWGCtrl:GetIsAutoTunShi()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	return PlayerPrefsUtil.GetInt("shenshou_auto_tunshi" .. role_id) == 1
end

--设置自动吞噬
function ShenShouWGCtrl:SetIsAutoTunShi()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local save_num = PlayerPrefsUtil.GetInt("shenshou_auto_tunshi" .. role_id)
	local value = save_num == COMMON_GAME_ENUM.ONE and COMMON_GAME_ENUM.ZERO or COMMON_GAME_ENUM.ONE
	PlayerPrefsUtil.SetInt("shenshou_auto_tunshi" .. role_id, value)
end

function ShenShouWGCtrl:AutoTunShiEquip()
	local destroy_item_list = {}
	local bag_data_list = ShenShouWGData.Instance:GetShenShouBagDataList()

	if not IsEmptyTable(bag_data_list) then
		for k,v in pairs(bag_data_list) do
			local _, item_color = ItemWGData.Instance:GetItemColor(v.item_id)
			if item_color <= GameEnum.ITEM_COLOR_RED then
				table.insert(destroy_item_list, {item_id = v.item_id, index = v.index, count = v.num})
			end
		end
	end

    if not IsEmptyTable(destroy_item_list) then
		local double_flag = 0
		ShenShouWGCtrl.Instance:OnCSShenshouDecompos(double_flag, #destroy_item_list, destroy_item_list)
    end

    if self.tunshi_timer then
		GlobalTimerQuest:CancelQuest(self.tunshi_timer)
		self.tunshi_timer = nil
	end
end
---------------------------------------------protocol_end-------------------------------------------------

--------------------------------------------view_start------------------------------------------------
function ShenShouWGCtrl:Open(tab_index, param_t)
	if self.view then
		self.view:Open()
	end
end


function ShenShouWGCtrl:OpenShenShouOverview(soul_ring_seq)
	if self.shenshou_overview then
		self.shenshou_overview:SetDataAndOpen(soul_ring_seq)
	end
end

function ShenShouWGCtrl:OpenShenShouSkillOverview(soul_ring_seq, data)
	if self.shenshou_skill_overview then
		self.shenshou_skill_overview:SetDataAndOpen(soul_ring_seq, data)
	end
end

function ShenShouWGCtrl:OpenShenShouBagView()
	if self.shenshou_bag_view then
		self.shenshou_bag_view:Open()
	end
end

function ShenShouWGCtrl:OpenShenShouQiangHua()
	if self.shenshou_qianghua_view then
		self.shenshou_qianghua_view:Open()
	end
end

function ShenShouWGCtrl:OpenShenShouEquipTip(data, fromView, param_t)
	if not data then
		return
	end
	local data_list = {}
	if fromView == ShenShouEquipTip.FROM_SHENSHOUBAG then
		local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(data.item_id)
		local view_select_soul_ring_seq = ShenShouWGData.Instance:GetViewSelectSoulRingSeq()
		local equip_info = ShenShouWGData.Instance:GetShenShouEquipInfo(view_select_soul_ring_seq, shenshou_equip_cfg.slot_index)

		if equip_info and equip_info.item_id > 0 then
			data_list[1] = {data = equip_info}
		end

		-- local view = ViewManager.Instance:GetView(GuideModuleName.ShenShou)
		-- if not view then
		-- 	return
		-- end

		-- local cur_shou_id = view.select_shou_id
		-- if self.shenshou_equip_bag_view and self.shenshou_equip_bag_view.shou_id > 0 then
		-- 	cur_shou_id =  self.shenshou_equip_bag_view.shou_id
		-- end

		-- local shenshou_list = ShenShouWGData.Instance:GetShenshouList(cur_shou_id)
		-- local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(data.item_id)
		-- if shenshou_list then
		-- 	for k, v in pairs(shenshou_list.equip_list) do
		-- 		if v.item_id > 0 and v.slot_index == shenshou_equip_cfg.slot_index then
		-- 			data_list[1] = {data = v}
		-- 			break
		-- 		end
		-- 	end
		-- end
	end
	data_list[2] = {data = data, fromView = fromView, param_t = param_t}
	self.shenshou_equip_contrast:SetData(data_list)
end

function ShenShouWGCtrl:CloseShenShouEquipTip()
	self.shenshou_equip_contrast:Close()
end

function ShenShouWGCtrl:OpenShenShouStuffTip(data, fromView)
	local show_data = {data = data, fromView = fromView}
	self.shenshou_stuff_tip:SetData(show_data)
end

function ShenShouWGCtrl:OpenExtraZhuZhanTip(data, callback)
	self.shenshou_extrazhuzhan_tip:SetData(data, callback)
end

function ShenShouWGCtrl:OpenShenShouDeComposeView()
	if self.shenshou_decompose_view and not self.shenshou_decompose_view:IsOpen() then
		self.shenshou_decompose_view:Open()
	end
end
---------------------------------------------view_end-------------------------------------------------
