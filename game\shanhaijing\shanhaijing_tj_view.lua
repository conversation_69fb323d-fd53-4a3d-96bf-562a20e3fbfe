SHJ_TJ_TYPE = {
	SJ = 1,	-- 升级（被屏蔽）
	SX = 2,	-- 升星
	SG = 3, -- 升阶
}

SHJ_TYPE = {
	Boss = 1,
	HS = 2,
	HD = 3,
	CT = 4,
	ACT = 5,
}

EF_TYPE = {
	[1] = Ui_Effect.UI_kapai_lan,
	[2] = Ui_Effect.UI_kapai_zi,
	[3] = Ui_Effect.UI_kapai_cheng,
	[4] = Ui_Effect.UI_kapai_hong,
	[5] = Ui_Effect.UI_kapai_fen,
	[6] = Ui_Effect.UI_kapai_jin,
	[7] = Ui_Effect.UI_kapai_xuancai,
}

function ShanHaiJingView:TJLoadCallBack()
	self.shj_tj_view_type = SHJ_TJ_TYPE.SX
	self.nav_bar_item_count = #self.base_data:GetTJCardTypecfg()
	self.left_btn_index = nil
	-- 升星升级激活物品
	self.isplay = false
	self.is_play_attr_eff = false
	self:CreateTuJianList()
	self:SetEnterBtnList()
	self:SetActiveAlpha(false)
	self:RestTween()
	self.AddClickEventListener(self.node_list.tj_btn_operate,BindTool.Bind(self.ClickOpera,self))
	self.AddClickEventListener(self.node_list.tj_btn_resolve,BindTool.Bind(self.ClickOpenResolveView,self))
	self.AddClickEventListener(self.node_list.tj_btn_jiban,BindTool.Bind(self.ClickOpenZuheView,self))
	self.AddClickEventListener(self.node_list.tj_btn_zonglan,BindTool.Bind(self.ClickOpenAttrInfoView,self))
	--self.AddClickEventListener(self.node_list.tj_tip, BindTool.Bind(self.OnClickTuJianTip, self))
	--self.AddClickEventListener(self.node_list.btn_back, BindTool.Bind(self.OnClickBack, self))

	self.node_list["tog_up_star"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTJTog, self, SHJ_TJ_TYPE.SX))
	self.node_list["tog_up_grade"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTJTog, self, SHJ_TJ_TYPE.SG))
	self.AddClickEventListener(self.node_list.tj_btn_upgrade, BindTool.Bind(self.OnClickTJUpGrade, self))

	if not self.upgrade_item then
		self.upgrade_item = ItemCell.New(self.node_list.upgrade_item_root)
	end
end

function ShanHaiJingView:CloseTJCallBack()
	self.tj_select_data = nil
	self.jump_tujian = false
	self.need_jump = false
	self.tj_jump_item_id = nil
	self.is_move = false
	self.show_active_tween = false
	self.tj_index = nil
end

--最外层按钮
function ShanHaiJingView:SetEnterBtnList()
	if nil == self.enter_btn_list then
		self.enter_btn_list = {}
		local num = self.node_list.btn_root.transform.childCount
		for i = 1, num do
            local cell = TJEnterRender.New(self.node_list["select_btn_" .. i]) -- .btn_root:FindObj("select_btn_" .. i)
            cell:SetSelectCallBack(BindTool.Bind1(self.OnClickSHJBarHandler, self))
            cell:SetIndex(i)
            local data_list = self.base_data:GetTJItemListInfo(i)
            if not IsEmptyTable(data_list) then
            	cell:SetData(data_list[i])
            end 

            self.enter_btn_list[i] = cell
        end
    else
    	for k,v in pairs(self.enter_btn_list) do
    		v:FlushRemind()
    	end
	end
end

-- 创建图鉴表格，左侧按钮
function ShanHaiJingView:CreateTuJianList()
	if not self.tujian_list then
		self.tujian_list = AsyncListView.New(TuJianItemRender, self.node_list.tj_handbook_list)
    	self.tujian_list:SetSelectCallBack(BindTool.Bind1(self.OnClickTJCardHandler, self))
    	self.tujian_list:SetStartZeroIndex(false)
	end

	if nil == self.bar_btn_list then
		self.bar_btn_list = AsyncListView.New(TJBarItemCellRender, self.node_list.tj_bar_list)
		self.bar_btn_list:SetSelectCallBack(BindTool.Bind1(self.OnClickAccordionSHJChild, self))
		self.bar_btn_list:SetDefaultSelectIndex(nil)
		self.bar_btn_list:SetStartZeroIndex(false)
	end
end

--最外层点击回调
function ShanHaiJingView:OnClickSHJBarHandler(index)
	if index == nil then
		return
	end
	self.is_show_tj = true

	self.select_card_type = index
	self:EnterInfoTJTween(index)
	ReDelayCall(self, function()
		local show_item_index = 1
		local cfg = self.base_data:GetTJToggleListInfo(index)
		local card_child_num = #cfg
		for i = 1, card_child_num do
			local child_flag = self.base_data:GetTJChildCardRemind(index, cfg[i].card_child_type)
			if child_flag then
				show_item_index = i
				break
			end
		end

		self:SetBarData(index)
		self.bar_btn_list:JumpToIndex(show_item_index)
	end, 0.5, "shanhaijingview_tween3")
end

-- 点击图鉴回调
function ShanHaiJingView:OnClickTJCardHandler(cell)
	self.is_move = true
	if self.tj_select_data and self.tj_select_data == (cell and cell.data) then
		local opera_info = self.base_data:GetTJOperaInfo(self.tj_select_data, true)
		local server_info = self.base_data:GetTJAllServerInfo()[self.tj_select_data.seq]
		local tj_level = server_info and server_info.level or 0
		if tj_level > 0 then
			-- 显示道具tip
			self:ShowTJStuffView(self.tj_select_data)
		elseif not IsEmptyTable(opera_info) and opera_info.has_num >= opera_info.need_num and opera_info.has_exp_num >= opera_info.need_exp_num and not self.need_jump and tj_level <= 0 then
			--右侧信息栏下拉动画
			self.show_active_tween = true
			self:ShowActiveTJTween()
			self.base_ctrl:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_ACTIVE,self.tj_select_data.seq)
			self.tj_index = cell:GetIndex()
		else
			self:ShowTJStuffView(self.tj_select_data)
		end

		self.need_jump = false
		return
	end

	self.tj_index = cell:GetIndex()
	self.tj_select_data = cell and cell.data
	if not self.tj_select_data then return end
	local opera_info = self.base_data:GetTJOperaInfo(self.tj_select_data, true)
	if IsEmptyTable(opera_info) then
		return
	end

	local server_info = self.base_data:GetTJAllServerInfo()[self.tj_select_data.seq]
	local tj_level = server_info and server_info.level or 0
	if opera_info.has_num >= opera_info.need_num and opera_info.has_exp_num >= opera_info.need_exp_num and not self.need_jump and tj_level <= 0 then
		--右侧信息栏下拉动画
		self.show_active_tween = true
		self:ShowActiveTJTween()
		self.base_ctrl:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_ACTIVE,self.tj_select_data.seq)
	end
	self.need_jump = false
	-- 刷新右边面板
	self:FlushTJRightView(self.tj_select_data, true)
end

function ShanHaiJingView:MoveToPos(is_move)
	--if self.has_active or is_move then
	local cell_width = 328
	local space = 0 --间隔
	local roll_x = space + cell_width
	local list_width = self.node_list.tj_handbook_list.rect.rect.width
	local pos_x = -1
	local padding = 20
	local final_num = self.has_active and 0 or 1
    if self.tj_index >= self.data_len - final_num then
        pos_x = self.data_len * roll_x - space - list_width + padding--(self.data_len * cell_width + (self.data_len) * space - list_width + 82) --listView宽度
    elseif self.tj_index <= 1 then
        pos_x = 0
    elseif self.tj_index > 1 then
        pos_x = roll_x * (self.tj_index - 1)
    end
    if self.data_len > 1 then
        if pos_x ~= -1 and pos_x > 0 then
            GlobalTimerQuest:AddDelayTimer(function ()
            local rect_transform = self.node_list["tj_handbook_list"].scroll_rect.content:GetComponent(typeof(UnityEngine.RectTransform))
            local list_animal = rect_transform:DOAnchorPosX(-pos_x, 0.3)
            list_animal:SetEase(DG.Tweening.Ease.OutCubic)
            end, 0)
        end
    end
	--end

	self.is_move = false
end

--左侧按钮点击回调
function ShanHaiJingView:OnClickAccordionSHJChild(item)
	if self.left_btn_index == item:GetIndex() and not self.jump_tujian then
		return
	end

	self.tj_select_data = nil
	self.left_btn_index = item:GetIndex()
	if nil ~= item and nil ~= item.data then
		self.select_card_child_type = item.data.card_child_type
		self:FlushListView(item.data.card_type, item.data.card_child_type)
		if not self.need_jump then
			self.tujian_list:JumpToIndex(1)
		end
	end
end

--设置左侧按钮数据
function ShanHaiJingView:SetBarData(card_type, card_child_type)
	local data_list = self.base_data:GetTJToggleListInfo(card_type)
	self.bar_btn_list:SetDataList(data_list)
	if not IsEmptyTable(data_list) and data_list[1].card_type_name then
		self.node_list.title.text.text = data_list[1].card_type_name
	end

	if card_child_type then
		local card_child_num = #data_list
		local show_item_index = 1
		for i = 1, card_child_num do
			if data_list[i].card_child_type == card_child_type then
				show_item_index = i
				break
			end
		end

		self.bar_btn_list:JumpToIndex(show_item_index)
	end
end

function ShanHaiJingView:OnClickBack()
	self.is_show_tj = false
	self:SetActiveAlpha(false)
	self.tj_select_data = nil
	self.left_btn_index = nil
	self.select_card_type = nil 
	self.select_card_child_type = nil
	self.jump_tujian = false
	self.tj_jump_item_id = nil
	self:RestTween()
end

function ShanHaiJingView:SetActiveAlpha(is_show)
	self.node_list.group_root:SetActive(is_show)
	self.node_list.group_root.canvas_group.alpha = is_show and 1 or 0
	self.node_list.group_root.canvas_group.blocksRaycasts = is_show
	self.node_list.enter.canvas_group.alpha = is_show and 0 or 1
	self.node_list.enter.canvas_group.blocksRaycasts = not is_show
end

function ShanHaiJingView:ShowActiveTJTween()
	UITween.FakeHideShow(self.node_list.right_root)
	UITween.FakeHideShow(self.node_list.right_boss)
	local xpos = self.node_list.right_boss.rect.anchoredPosition.x
	RectTransform.SetAnchoredPositionXY(self.node_list.right_boss.rect, xpos, 200)
	UITween.AlphaShow(GuideModuleName.ShanHaiJingView, self.node_list.right_boss, 0, 1, 0.3)
	self.node_list.right_boss.rect:DOAnchorPosY(0, 0.3)
	ReDelayCall(self, function()
		UITween.FakeToShow(self.node_list.right_root)
	end, 0.3, "shanhaijingview_tween")
end

function ShanHaiJingView:EnterInfoTJTween(index)
	self.node_list.luoshence_btn.gameObject:SetActive(false)
	self.node_list.tween_root:SetActive(true)
	local num = self.node_list.btn_root.transform.childCount
	local rect
	for i = 1, num do
		if index == i then
			rect = self.node_list.btn_root:FindObj("select_btn_" .. i).rect.anchoredPosition
		end
	end

	local data_list = self.base_data:GetTJItemListInfo(index)
	--改为变更图片
	--self.node_list.tween_title.text.text = data_list[1].card_type_name
	local bundle, name = ResPath.GetRawImagesPNG("a3_wtp_tcdi" .. index)
	self.node_list.tween_left.raw_image:LoadSprite(bundle, name)
	self.node_list.left.raw_image:LoadSprite(bundle, name)

	-- 灰色Mask
	self.node_list["tween_mask_bg"]:SetActive(true)
	UITween.AlphaShow(GuideModuleName.ShanHaiJingView, self.node_list["tween_mask_bg"], 0, 1, 0.2)
	self.node_list.tween_root.rect.anchoredPosition = rect
	local vector2 = Vector2(0, 0)
	self.node_list.tween_root.rect:DOAnchorPos(vector2, 0.2)
	UITween.AlphaShow(GuideModuleName.ShanHaiJingView, self.node_list.btn_root, 1, 0, 0.2)
	ReDelayCall(self, function()
		local target_left_pos = self.node_list.left.rect.anchoredPosition
		self.node_list.tween_left.rect:DOAnchorPos(target_left_pos, 0.5)
		self.node_list.tween_bg.rect:DOSizeDelta(Vector2(1452, 628), 0.5)
		ReDelayCall(self, function()
			self:SetActiveAlpha(true)
			self:RestTween()
		end, 0.5, "shanhaijingview_tween2")
	end, 0.2, "shanhaijingview_tween1")
end

--重置动画为初始状态
function ShanHaiJingView:RestTween()
	-- 功能开启按钮显示
	local is_fun_open = FunOpen.Instance:GetFunIsOpened(FunName.ShanHaiJingLSCView)
	self.node_list.luoshence_btn.gameObject:SetActive(is_fun_open)
	--local is_fun_open_2 = FunOpen.Instance:GetFunIsOpened(FunName.StrangeCatalogView)
	self.node_list.qiwenyilu_btn.gameObject:SetActive(false) -- 已移至双修-珍宝匣

	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView)
	self.node_list.tween_root:SetActive(false)
	self.node_list["tween_mask_bg"]:SetActive(false)
	self.node_list.tween_left.rect.anchoredPosition = Vector2(0, -9)
	--self.node_list.tween_right.rect.anchoredPosition = Vector2(46, -9)
	self.node_list.tween_bg.rect.sizeDelta = Vector2(0, self.node_list.tween_bg.rect.sizeDelta.y)
	UITween.FakeToShow(self.node_list.btn_root)
end

function ShanHaiJingView:ShowRightInfo()
	local width = self.has_active and 564 or 940
	self.node_list.tj_handbook_list.rect.sizeDelta = Vector2(width, self.node_list.tj_handbook_list.rect.sizeDelta.y)
	self.node_list.right_boss.canvas_group.alpha = self.has_active and 1 or 0
	self.node_list.right_boss.canvas_group.blocksRaycasts = self.has_active
	self.node_list.right_boss.canvas_group.interactable = self.has_active
	self.node_list.right_root.canvas_group.alpha = self.has_active and 1 or 0
	self.node_list.right_root.canvas_group.blocksRaycasts = self.has_active
	self.node_list.right_root.canvas_group.interactable = self.has_active
	ReDelayCall(self, function()
		if self.is_move then
			self:MoveToPos()
		end
	end, 0.1, "shanhaijingview_tween4")
end

function ShanHaiJingView:TJReleaseCallBack()
	if nil ~= self.enter_btn_list then
        for k,v in pairs(self.enter_btn_list) do
            v:DeleteMe()
        end
        self.enter_btn_list = nil
    end

	if nil ~= self.tujian_list then
        self.tujian_list:DeleteMe()
        self.tujian_list = nil
    end

    if nil ~= self.bar_btn_list then
        self.bar_btn_list:DeleteMe()
        self.bar_btn_list = nil
    end

    if self.arrow_tweener then
		self.arrow_tweener:Kill()
 		self.arrow_tweener = nil
   	end

	if self.upgrade_item then
		self.upgrade_item:DeleteMe()
		self.upgrade_item = nil
	end

	self.tj_select_data = nil
	self.select_card_type = nil
	self.select_card_child_type = nil
	self.nav_bar_item_count = nil
	self.isplay = nil
	self.is_play_attr_eff = nil
	if StepExcuteManager.Instance then
		StepExcuteManager.Instance:UnRegisterAllByStepKey(StepExcuteKey.TuJian)
	end
end

function ShanHaiJingView:TJFlush(param_t)
	local flush_single_flag = false
	local flush_single_item = nil
	if param_t then
		for k,v in pairs(param_t) do
			if k == "all" and v['flush_jump_tujian'] then
				self.jump_tujian = true
				local tj_defualt_index, tj_select_list_index, tj_jump_item_id = ShanHaiJingWGData.Instance:GetJumpTuJianInfo()
				self.tj_jump_item_id = tj_jump_item_id
				if tj_defualt_index ~= nil and nil ~= tj_select_list_index then
					self.is_show_tj = true
					self:SetActiveAlpha(true)
					self.select_card_type = tj_defualt_index
					self:SetBarData(tj_defualt_index, tj_select_list_index)
				end

				return
			end
			if k == "all" and v['flush_tujian_item'] then
				self:FlushTJItem(self.tj_select_data)
				if not v['flush_single_item'] then
					return
				end
			end

			if k == "all" and v['flush_single_item'] then
				flush_single_flag = true
				flush_single_item = v['flush_single_item']
				self:FlushSelectRemind()
			end

			if k == "all" and v["tujian_bag_change"] then
				if self.select_card_type and self.select_card_child_type then
					--self.is_move = false
					self:FlushListView(self.select_card_type, self.select_card_child_type)
				end
			end
		end
	end

	if not IsEmptyTable(self.tj_select_data) then
		-- 刷新右边面板
		self:FlushTJRightView(self.tj_select_data)
	end

	self:SetBarData(self.select_card_type)
	self:SetEnterBtnList()
	-- 刷新左边
	if flush_single_flag then
		self:FlushSingleTJItem(self.select_card_type, self.select_card_child_type, flush_single_item)
	end
	local total_zhandouli = self.base_data:GetTotalTJCap()
	self.node_list.tujian_zhan_dou_li.text.text = total_zhandouli
end

function ShanHaiJingView:FlushSelectRemind()
	local time = 0
	if self.show_active_tween then
		time = 0.4
		self.show_active_tween = false
	end

	AddDelayCall(self, function()
		local show_item_index = 1
		local cfg = self.base_data:GetTJToggleListInfo(self.select_card_type)
		local card_child_num = #cfg
		local child_flag = self.base_data:GetTJChildCardRemind(self.select_card_type, self.select_card_child_type)
		if not child_flag then
			for i = 1, card_child_num do
				child_flag = self.base_data:GetTJChildCardRemind(self.select_card_type, cfg[i].card_child_type)
				if child_flag then
					show_item_index = i
					self.bar_btn_list:JumpToIndex(show_item_index)
					return
				end
			end
		end

		self:FlushListView(self.select_card_type, self.select_card_child_type)
	end, time)
	
end

function ShanHaiJingView:FlushListView(card_type, card_child_type)
	local data_list = self.base_data:GetTJChildItemListInfo(card_type, card_child_type)
	if IsEmptyTable(data_list) or not data_list then
		return
	end

	self.data_len = #data_list
	local index = 1
	--local need_jump = false
	if self.jump_tujian then
		for i,v in ipairs(data_list) do
			if v.active_item_id == self.tj_jump_item_id then
				index = i
				self.need_jump = true
				break
			end
		end

		self.jump_tujian = false
	else
		local flag = false
		if not IsEmptyTable(self.tj_select_data) then
			flag = ShanHaiJingWGData.Instance:GetTJRemindByCard(self.tj_select_data, true)
		end

		if not flag then
			for i,v in ipairs(data_list) do   -- 红点判断
				flag = ShanHaiJingWGData.Instance:GetTJRemindByCard(v, true)
				if flag then
					index = i
					self.need_jump = true
					break
				end
			end
		end
	end

	if not IsEmptyTable(self.tj_select_data) then
		self:FlushTJRightView(self.tj_select_data, false)
	end

	ShanHaiJingWGData.Instance:GetCardType(self.select_card_type)
	self.tujian_list:SetDataList(data_list)
	if self.need_jump then
		self.tujian_list:JumpToIndex(index, 2)
		self.tj_index = index
		self:MoveToPos()
	end
	--self:FlushBiaoQianRemind()
end

function ShanHaiJingView:FlushBiaoQianRemind()
	StepExcuteManager.Instance:Fire(StepExcuteKey.TuJian)
end

function ShanHaiJingView:FlushSingleTJItem(card_type, card_child_type, flush_single_item)
	local data_list = self.base_data:GetTJChildItemListInfo(card_type,card_child_type)
	self.tujian_list:SetDataList(data_list)
	--self:FlushBiaoQianRemind()
end

-- 显示道具tip
function ShanHaiJingView:ShowTJStuffView(cell_data)
	local server_info = ShanHaiJingWGData.Instance:GetTJAllServerInfo()[cell_data.seq]
	-- 激活物品配置
	local stuff_item_cfg = ItemWGData.Instance:GetItemConfig(cell_data.active_item_id)
	local has_active = server_info and server_info.level > 0
	local num = ShanHaiJingWGData.Instance:GetItemNumInBagById(cell_data.active_item_id)
	if has_active or num > 0 then
		TipWGCtrl.Instance:OpenItem({item_id = stuff_item_cfg.id},ItemTip.FROM_NORMAL, nil)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_item_cfg.id})
	end
end

function ShanHaiJingView:FlushTJRightView(data, reset_viewtype, is_toggle_clisk)
	if not data then return end
	local server_info = self.base_data:GetTJAllServerInfo()[data.seq]
	self.has_active = server_info and server_info.level > 0
	self:ShowRightInfo()
	local flag = self.base_data:GetAllZuHeRemindByCardType(self.select_card_type)
	self.node_list.tj_jiban_remind:SetActive(flag == 1)
	if not self.has_active then
		return
	end
	-- 红点
	local upstar_remind = self.base_data:GetTJUpStarRemind(data)
	local upgrade_remind = self.base_data:GetTJUpGradeRemind(data)
	if not is_toggle_clisk then
		if upstar_remind ~= upgrade_remind then
			if upstar_remind then
				self.node_list["tog_up_star"].toggle.isOn = true
				self.node_list["tog_up_grade"].toggle.isOn = false
			elseif upgrade_remind then
				self.node_list["tog_up_star"].toggle.isOn = false
				self.node_list["tog_up_grade"].toggle.isOn = true
			end
		end
	end
	self.node_list["remind_up_star"]:SetActive(upstar_remind)
	self.node_list["remind_up_grade"]:SetActive(upgrade_remind)

	-- 升星配置
	local upstar_cfg = self.base_data:GetTJUpStarCfg(data.seq, server_info.star)
	self:TJActive(data, reset_viewtype, upstar_cfg ~= nil, server_info)
	self:FlushTJItem(data)
	--local skill_info_list = self.base_data:GetTJSkillInfo(data)

	self:PositionTween()
	-- 刷新卡片信息
	self:FlushRightTJCard(data)
	--self:FulshStar(server_info)
	--flag = self.base_data:GetResolveRemind(self.select_card_type)
	--self.node_list.tj_resolve_remind:SetActive(flag == 1)
end

function ShanHaiJingView:TJActive(card_data,reset_viewtype,has_upstar,server_info)
	if self.shj_tj_view_type == SHJ_TJ_TYPE.SX then
		-- 0星的时候需要显示1星的属性
		local star_info = self.base_data:GetTJUpStarAttrInfo(card_data, nil, true)
		--print_error("star_info", star_info.attr_list)
		local add_attr_list
		local star_cfg = {}
		if not star_info.is_max_star then
			local next_info = __TableCopy(server_info)
			next_info.star = next_info.star + 1
			star_cfg = self.base_data:GetTJUpStarAttrInfo(card_data, next_info)
			add_attr_list = star_cfg.attr_list
		end
		--print_error("add_attr_list", add_attr_list)
		self.node_list.tj_operate_txt.text.text = star_info.is_max_star and Language.ShanHaiJing.BtnName_4 or Language.ShanHaiJing.BtnName_8
		--XUI.SetButtonEnabled(self.node_list.tj_btn_operate, not star_info.is_max_star)
		self.node_list.tj_btn_operate:CustomSetActive(not star_info.is_max_star)
		self:FlushTJAttr(star_info.attr_list, add_attr_list, server_info.star)
		--end
		self.node_list.capacity_num.text.text = star_info.capability
	elseif self.shj_tj_view_type == SHJ_TJ_TYPE.SG then
		local up_grade_info = self.base_data:GetTJUpGradeAttrInfo(card_data)
		self:FlushTJAttr(up_grade_info.attr_list, up_grade_info.add_attr_list, server_info.star)

		local consume_item_id = up_grade_info.consume_item_id
		local had_num = self.base_data:GetItemNumInBagById(consume_item_id)
		self.upgrade_item:SetData({item_id = consume_item_id})
		self.upgrade_item:SetRightBottomTextVisible(true)
		if not up_grade_info.is_max_grade then
			local color = had_num >= up_grade_info.consume_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
			self.upgrade_item:SetRightBottomColorText(had_num .. "/" .. up_grade_info.consume_num, color)
		else
			self.upgrade_item:SetRightBottomColorText("--/--")
		end

		self.node_list.tj_btn_upgrade:CustomSetActive(not up_grade_info.is_max_grade)
	end
end

function ShanHaiJingView:PositionTween()
	if self.arrow_tweener then
		self.arrow_tweener:Kill()
 		self.arrow_tweener = nil
 		--self.node_list["layout_gray"].transform.localPosition.y = Vector2(0, -10)
   	end

	if self.has_active then
		self.node_list["tj_card_icon"].transform.localPosition = Vector2(-10, -16)
		self.arrow_tweener = self.node_list["tj_card_icon"].transform:DOLocalMoveY(-6, 1.3, true)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.Linear)
	end
end

-- 刷新右边图鉴
function ShanHaiJingView:FlushRightTJCard(data)
	-- 激活物品配置
	local stuff_item_cfg = ItemWGData.Instance:GetItemConfig(data.active_item_id)
	self.node_list.tj_card_icon.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a2_shj_tu" .. data.card_ID))
	self.node_list.tj_card_name_bg.image:LoadSprite(ResPath.GetSHJImage(
		("a3_wtp_mcdi" .. stuff_item_cfg.color)))
	self.node_list.tj_card_name.text.text = stuff_item_cfg.name
end

function ShanHaiJingView:FlushTJItem(card_data)
	if IsEmptyTable(card_data) then 
		return
	end
	local opera_info
	self.node_list.al_slider:SetActive(self.has_active)

	--图鉴是否已激活
	if not self.has_active then
		opera_info = self.base_data:GetTJOperaInfo(card_data)
		
	else
		opera_info = self.base_data:GetTJOperaInfo(card_data, self.shj_tj_view_type == SHJ_TJ_TYPE.SX)
		if not opera_info.is_max_star then
			self.node_list.al_slider.slider.value = opera_info.has_exp_num/opera_info.need_exp_num
			self.node_list.al_slider_text.text.text = opera_info.has_exp_num .. "/" .. opera_info.need_exp_num
		else
			self.node_list.al_slider.slider.value = 1
			self.node_list.al_slider_text.text.text = "max"
		end
	end

	-- 按钮红点等逻辑
	--local flag = self.base_data:GetResolveRemind(self.select_card_type)
	--self.node_list.tj_resolve_remind:SetActive(flag == 1)
	self.node_list.tj_operate_remind:SetActive(opera_info.remind)
end

-- 刷新属性
function ShanHaiJingView:FlushTJAttr(attr_list, add_attr_list, star)
	if not attr_list then return end
	local item_index = 0
	local attr_name = Language.Common.AttrNameList2
	local sort_list = AttributeMgr.SortAttribute()
	local is_per

	for k,v in pairs(sort_list) do
		is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
		if attr_list[v] > 0 then
			item_index = item_index + 1
			-- 插值部分
			if add_attr_list and add_attr_list[v] then--and add_attr_list[v] - attr_list[v] > 0 then
				local num = star + 1
				if star == 0 then
					self.node_list["attr_add_value".. item_index].text.text = (is_per and (add_attr_list[v]/100 .. "%") or add_attr_list[v])
				else
					self.node_list["attr_add_value".. item_index].text.text = (is_per and (add_attr_list[v]/100 .. "%") or add_attr_list[v])
				end
			else
				self.node_list["attr_add_value".. item_index].text.text = ''
			end
			self.node_list["attr_add_value".. item_index]:SetActive(add_attr_list ~= nil)
			self.node_list["attr_arrow".. item_index]:SetActive(add_attr_list ~= nil)
			self.node_list["attr_item_name" .. item_index].text.text = attr_name[v] --.. "：" .. ToColorStr(is_per and (attr_list[v]/100 .. '%') or attr_list[v] , "#a64a2e")
			self.node_list['attr_value' .. item_index].text.text = is_per and (attr_list[v]/100 .. '%') or attr_list[v]--ToColorStr(is_per and (attr_list[v]/100 .. '%') or attr_list[v] , "#a64a2e")
			if self.is_play_attr_eff then
				self:ShowRightAttrEffect()
			end
		end
		if item_index > 4 then break end
	end
	self.is_play_attr_eff = false

	for i=1,4 do
		self.node_list['attr_item_cell' .. i]:SetActive(item_index >= i)
	end
end

function ShanHaiJingView:ShowRightAttrEffect()
	local function set_eff_show_func(is_show)
		for i = 1, 4 do
			self.node_list["attr_eff_" .. i]:SetActive(is_show)
		end
	end
	set_eff_show_func(false)
	set_eff_show_func(true)
	ReDelayCall(self, function()
		set_eff_show_func(false)
	end, 2, "PlayAttrValueUpEffect")
end

function ShanHaiJingView:FulshStar(data)
	--local bundle_name1, asset_name1 = ResPath.GetEffectUi("UI_shengxing")
	local star_num = data.star
	local num = star_num
	if self.isplay then
		-- if star_num <= 5 and self.node_list["effect_root"..num] then
		-- 	EffectManager.Instance:PlayAtTransform(bundle_name1, asset_name1, self.node_list["effect_root"..num].transform, 3, Vector3(0, 0, 0),nil,nil)
		-- else
		-- 	num = star_num - 5
		-- 	EffectManager.Instance:PlayAtTransform(bundle_name1, asset_name1, self.node_list["effect_root"..num].transform, 3, Vector3(0, 0, 0),nil,nil)
		-- end
		self.isplay = false
	end

	if star_num <= 5 then
		for i=1,5 do
			self.node_list['tj_star' .. i]:SetActive(star_num >= i)
		end
	else
		for i=1,5 do
			self.node_list['tj_star' .. i]:SetActive(true)
		end
	end
end

-------------------------点击事件------------------------------------------
-- 点击请求操作
function ShanHaiJingView:ClickOpera()
	if IsEmptyTable(self.tj_select_data) then
		return
	end

	local opera_info = self.base_data:GetTJOperaInfo(self.tj_select_data, true)
	if IsEmptyTable(opera_info) then
		return
	end

	--该图鉴是否已激活
	if not self.has_active then
		if opera_info.has_num >= opera_info.need_num and opera_info.has_exp_num >= opera_info.need_exp_num then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
			self.isplay, self.is_play_attr_eff = true, true
			self.base_ctrl:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_ACTIVE,self.tj_select_data.seq)
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = opera_info.item_id})
		end
	else
		if opera_info.is_max_star then
			return
		end

		if opera_info.has_num >= opera_info.need_num and opera_info.has_exp_num >= opera_info.need_exp_num  then
			if opera_info.level_limit then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShanHaiJing.Btn_Tip_2,opera_info.need_role_level))
			else
				TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
				self.isplay, self.is_play_attr_eff = true, true
				self.base_ctrl:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_UPSTAR, self.tj_select_data.seq)
			end
		else
			--ViewManager.Instance:Open(GuideModuleName.SHJFenjieView)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ShanHaiJing.Tips)
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = 91128})
		end
	end
end

-- 点击打开分解面板
function ShanHaiJingView:ClickOpenResolveView()
	ViewManager.Instance:Open(GuideModuleName.SHJFenjieView)
end

-- 点击打开羁绊面板
function ShanHaiJingView:ClickOpenZuheView()
	ViewManager.Instance:Open(GuideModuleName.ShanHaiJingZuHeView)
end

-- 点击打开属性总览面板
function ShanHaiJingView:ClickOpenAttrInfoView()
	ViewManager.Instance:Open(GuideModuleName.SHJAttrView)
end

function ShanHaiJingView:OnClickTuJianTip()
	RuleTip.Instance:SetContent(Language.ShanHaiJing.TuJian_Explain,Language.ShanHaiJing.TuJian_Title)
end

-- 页签Toggle点击
function ShanHaiJingView:OnClickTJTog(sel_type, is_on)
	if is_on then
		self.shj_tj_view_type = sel_type
		self.node_list["up_star_part"]:SetActive(sel_type == SHJ_TJ_TYPE.SX)
		self.node_list["up_grade_part"]:SetActive(sel_type == SHJ_TJ_TYPE.SG)
		self:FlushTJRightView(self.tj_select_data, nil, true)
	end
end

function ShanHaiJingView:OnClickTJUpGrade()
	if IsEmptyTable(self.tj_select_data) then
		return
	end
	
	local server_info = self.base_data:GetTJAllServerInfo()
	server_info = server_info[self.tj_select_data.seq]

	local upgrade_cfg = self.base_data:GetTJUpGradeCfg(self.tj_select_data.seq)
	local consume_num = self.base_data:GetTJUpGradeConsume(upgrade_cfg.upgrade_seq, server_info.grade_level)
	local had_num = self.base_data:GetItemNumInBagById(upgrade_cfg.consume_item_id)

	if had_num >= consume_num then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengjie, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
		self.base_ctrl:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_UPGRADE, self.tj_select_data.seq)
		self.is_play_attr_eff = true
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = upgrade_cfg.consume_item_id })
	end
end
----------------------------------图鉴renderer------------------------------------
TuJianItemRender = TuJianItemRender or BaseClass(BaseRender)
function TuJianItemRender:__init()
	self.red_excute_event = StepExcuteManager.Instance:Register(StepExcuteKey.TuJian, BindTool.Bind(self.FlushRemind,self))
end

function TuJianItemRender:__delete()
	StepExcuteManager.Instance:UnRegister(StepExcuteKey.TuJian, self.red_excute_event)
end

function TuJianItemRender:OnFlush()
	if not self.data then return end
	local data = self.data
	-- self.node_list.name.text.text = data.card_name
	-- 服务器数据
	local server_info = ShanHaiJingWGData.Instance:GetTJAllServerInfo()[data.seq]
	-- 激活物品配置
	local stuff_item_cfg = ItemWGData.Instance:GetItemConfig(data.active_item_id)
	-- 是否激活
	self.has_active = server_info and server_info.level > 0
	self.node_list.layout_active:SetActive(self.has_active)
	ShanHaiJingWGCtrl.Instance:SetGraphicGrey(self.node_list.layout_gray, not self.has_active)
	--XUI.SetGraphicGrey(self.node_list.layout_gray, not self.has_active)
	--self.node_list.img_select.image:LoadSprite(ResPath.GetF2SHJImgPath("kapai_" .. stuff_item_cfg.color))
	-- 星星显隐
	-- local star_cfg = ShanHaiJingWGData.Instance:GetTJUpStarCfg(data.seq, server_info.star)
	--self.node_list.star_bg:SetActive(star_cfg ~= nil)

	if self.has_active then
		self:FlushStar(server_info)
	end

	local bundel, asset = ResPath.GetF2RawImagesPNG("a2_shj_tu" .. data.card_ID)
	self.node_list.icon.raw_image:LoadSprite(bundel, asset, function()
		self.node_list.icon.raw_image:SetNativeSize()
	end)
	self.node_list["name"].tmp.text = stuff_item_cfg.name
	self.node_list["txt_tj_grade"].tmp.text = string.format(Language.ShanHaiJing.TJGrade, server_info.grade_level)
	self.node_list["name_bg"].image:LoadSprite(ResPath.GetSHJImage(("a3_wtp_mcdi" .. stuff_item_cfg.color)))

	-- 红点
	self:FlushRemind()
end

function TuJianItemRender:FlushRemind()
	if not self.data then return end
	if not self.node_list or not self.node_list.remind then
		return
	end
	local data = self.data
	local flag = ShanHaiJingWGData.Instance:GetTJRemindByCard(data, true)
	self.node_list.remind:SetActive(flag)
end

-- 选择状态改变
function TuJianItemRender:OnSelectChange(is_select)
	self.node_list.img_select:SetActive(is_select)
end

-- 刷新评级
function TuJianItemRender:FlushPingJi(data,stuff_item_cfg)
	local quantity = data.quantity
	local pingfen_seq = (stuff_item_cfg.color - 2)*5 + quantity

	--local bundle,asset = ResPath.GetSHJImgPath("pingji_" .. pingfen_seq)
	--self.node_list.pingji.image:LoadSprite(bundle,asset,function ()
	--	self.node_list.pingji.image:SetNativeSize()
	--end)
end

function TuJianItemRender:FlushStar(data)
	local star_num = data.star
	local star_res_list = GetStarImgResByStar(star_num)
    for i=1, GameEnum.ITEM_MAX_STAR do
    	self.node_list["star" .. i]:SetActive(true)
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
    end


	-- if star_num <= 5 then
	-- 	for i=1,5 do
	-- 		self.node_list['star' .. i]:SetActive(star_num >= i)
	-- 	end
	-- else
	-- 	for i=1,5 do
	-- 		self.node_list['star' .. i]:SetActive(true)
	-- 	end
	-- end
end


------------------------------------导航小标题-----------------------------------------------
TJBarItemCellRender = TJBarItemCellRender or BaseClass(BaseRender)
function TJBarItemCellRender:__init()

end

function TJBarItemCellRender:__delete()

end

function TJBarItemCellRender:OnFlush()
	local data = self:GetData()
	if nil == data then
		return
	end

	self.node_list.normal_text.text.text = data.card_child_name
	self.node_list.select_text.text.text = data.card_child_name
	self:FlushRemind()
end

function TJBarItemCellRender:FlushRemind()
	if not self.data or not self.index then
		return
	end

	local flag = ShanHaiJingWGData.Instance:GetTJChildCardRemind(self.data.card_type, self.data.card_child_type)
	self.node_list.remind:SetActive(flag)
end

function TJBarItemCellRender:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
	self.node_list.normal:SetActive(not is_select)
end


------------------------------- TJEnterRender------------------------------
TJEnterRender = TJEnterRender or BaseClass(BaseRender)
function TJEnterRender:__delete()

end

function TJEnterRender:LoadCallBack()
	self.node_list["select_btn"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
end

function TJEnterRender:OnFlush()
	if nil == self.data then return end
	self.node_list.title.text.text = self.data.card_type_name
	self:FlushRemind()
end

function TJEnterRender:OnClickCell()
	if nil == self.data then
		return
	end

	self.select_call_back(self.index)
end

function TJEnterRender:FlushRemind()
	local flag = ShanHaiJingWGData.Instance:GetTJBigCardRemind(self.index)
	if not flag then
		flag = ShanHaiJingWGData.Instance:GetAllZuHeRemindByCardType(self.index) == 1
	end
	self.node_list.remind:SetActive(flag) 
end