FbShituSceneLogic = FbShituSceneLogic or BaseClass(CommonFbLogic)

function FbShituSceneLogic:__init()
	self.open_view = false
end

function FbShituSceneLogic:__delete()

end

function FbShituSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
end

function FbShituSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function FbShituSceneLogic:Out()
	CommonFbLogic.Out(self)
end


function FbShituSceneLogic:IsRoleEnemy(target_obj, main_role)
	if target_obj:GetType() == SceneObjType.Role then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end


-- 是否是挂机打怪的敌人
function FbShituSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

function FbShituSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
end