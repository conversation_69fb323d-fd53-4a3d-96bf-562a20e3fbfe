ThunderManaEquipBagView = ThunderManaEquipBagView or BaseClass(SafeBaseView)
local PART_INDEX = {
	[1] = {seq = 0, part = 0},
	[2] = {seq = 0, part = 1},
	[3] = {seq = 0, part = 2},
	[4] = {seq = 0, part = 3},
	[5] = {seq = 0, part = 4},
	[6] = {seq = 1, part = 0},
	[7] = {seq = 1, part = 1},
	[8] = {seq = 1, part = 2},
	[9] = {seq = 1, part = 3},
	[10] = {seq = 1, part = 4},
}

function ThunderManaEquipBagView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -28), sizeDelta = Vector2(750, 524)})
	self:AddViewResource(0, "uis/view/thunder_mana_ui_prefab", "layout_thunder_mana_equip_bag")
end


function ThunderManaEquipBagView:SetShowDataAndOpen(data)
	if data then
        self.show_data = data
		self.part_select_index = data and data.select_part_index or 0
		self:Open()
	end
end

function ThunderManaEquipBagView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ThunderMana.ThunderTitleName[2]

	if not self.bag_grid then
        self.bag_grid = AsyncBaseGrid.New()
        self.bag_grid:CreateCells({
			col = 8, 
			cell_count = 32,
            list_view = self.node_list["bag_grid"], 
            change_cells_num = 2,
            itemRender = ThunderManaEquipBagItem,
            assetBundle = "uis/view/thunder_mana_ui_prefab",
            assetName = "layout_equip_bag_item",
        })
        self.bag_grid:SetStartZeroIndex(false)  
        self.bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectEquipBagCellCallBack, self))
	end

	-- 部位选择查看列表
	if not self.part_list then
		self.part_list = AsyncListView.New(ThunderManaEquipPartSelectItemRender, self.node_list.part_list)
		self.part_list:SetSelectCallBack(BindTool.Bind(self.PartSelectCallBack, self))
		self.part_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.btn_select_part, BindTool.Bind2(self.ClickBtnSelectPart, self))
	XUI.AddClickEventListener(self.node_list.close_part_list_part, BindTool.Bind2(self.ClickClosePartList, self))
	XUI.AddClickEventListener(self.node_list.equip_btn, BindTool.Bind2(self.ClickEquip, self))
	XUI.AddClickEventListener(self.node_list.get_equip_way_btn, BindTool.Bind2(self.ClickGetEuqipWay, self))
end

function ThunderManaEquipBagView:ReleaseCallBack()
	if self.bag_grid then
        self.bag_grid:DeleteMe()
        self.bag_grid = nil
    end

	if self.part_list then
        self.part_list:DeleteMe()
        self.part_list = nil
    end

	self.part_status = nil
	self.part_select_index = nil
	self.select_equip_cell_data = nil
end

function ThunderManaEquipBagView:OnFlush(param_t)
    self.part_select_index = self.part_select_index or 0
    self.part_status = self.part_status or false

    self.part_list:SetDataList(Language.ThunderMana.EquipPartSelectNmae)
    self:FushSelectStatus()
	self:FlushEquipBagGrid()
	self:FlushBtnStr()
end

-- 刷新选中状态
function ThunderManaEquipBagView:FushSelectStatus()
    local cur_part_str = Language.ThunderMana.EquipPartSelectNmae[self.part_select_index]
    self.node_list.cur_part_text.tmp.text = cur_part_str

    self.node_list.part_arrow_down:CustomSetActive(self.part_status)
    self.node_list.part_arrow_up:CustomSetActive(not self.part_status)
    self.node_list.part_list_part:CustomSetActive(self.part_status)
	self.select_equip_cell_data = nil
end

function ThunderManaEquipBagView:FlushBtnStr()
	if not self.select_equip_cell_data then
		self.node_list.equip_btn_text.tmp.text = Language.ThunderMana.BtnNameStr[0]
		return
	end

	local cur_equip_part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_cell_data.seq, self.select_equip_cell_data.part)
	if cur_equip_part_info and cur_equip_part_info.item_id > 0 then
		self.node_list.equip_btn_text.tmp.text = Language.ThunderMana.BtnNameStr[1]
	else
		self.node_list.equip_btn_text.tmp.text = Language.ThunderMana.BtnNameStr[0]
	end
end

function ThunderManaEquipBagView:FlushEquipBagGrid()
	if not self.part_select_index then
		return
	end

	local all_bag_info = ThunderManaWGData.Instance:GetAllThunderBagInfo()
	local aim_table = {}

	local part_index_info = PART_INDEX[self.part_select_index]
	for i, v in pairs(all_bag_info) do
		if not part_index_info then
			if v.item_id > 0 then
				table.insert(aim_table, v)
			end
		else
			if part_index_info.seq == v.seq and part_index_info.part == v.part and v.item_id > 0 then
				table.insert(aim_table, v)
			end
		end
	end

	if not IsEmptyTable(aim_table) then
		table.sort(aim_table, SortTools.KeyLowerSorters("sort_star", "seq", "part", "index"))
	end

	local new_data = {}
    new_data.is_plus = true
	new_data.seq = part_index_info and part_index_info.seq or 0
    table.insert(aim_table, new_data)

	self.bag_grid:SetDataList(aim_table)
	if not IsEmptyTable(aim_table) then
		self.bag_grid:JumpToIndexAndSelect(1)
	end
end

function ThunderManaEquipBagView:SelectEquipBagCellCallBack(cell)
	if nil == cell or nil == cell:GetData() then
        return
    end

	if cell.data.is_plus then
		return
	end
	
	if self.select_equip_cell_data == cell.data then
		TipWGCtrl.Instance:OpenItem({item_id = cell.data.item_id})
		return 
	end
	
	self.select_equip_cell_data = cell.data
	self:FlushBtnStr()
end

-- 部位选择查看列表
function ThunderManaEquipBagView:PartSelectCallBack(part_item, cell_index, is_default, is_click)
	if (not part_item) or (not part_item.data) then
		return
	end

	if is_default then
		return
	end

	self.part_select_index = cell_index
    self.part_status = false
    self:FushSelectStatus()
	self:FlushEquipBagGrid()
	self:FlushBtnStr()
end

-- 部位选择按钮点击
function ThunderManaEquipBagView:ClickBtnSelectPart()
    self.part_status = true
	self:FushSelectStatus()
end

-- 关闭部位选择框
function ThunderManaEquipBagView:ClickClosePartList()
    self.part_status = false
	self:FushSelectStatus()
end

function ThunderManaEquipBagView:ClickEquip()
	if not self.select_equip_cell_data then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[1])
		return
	end

	local data = self.select_equip_cell_data
	ThunderManaWGCtrl.Instance:SendCSThunderRequest(THUNDER_OPERATE_TYPE.PUT_EQUIP, data.seq, data.part, data.index)
end

function ThunderManaEquipBagView:ClickGetEuqipWay()
	local part_index_info = PART_INDEX[self.part_select_index]
	local cur_select_seq = part_index_info and part_index_info.seq or 0 
	local other_cfg = ThunderManaWGData.Instance:GetOtherCfg()
	local jump_view = ""
	if ThunderManaWGData.ThunderType.ShadyType == cur_select_seq then
		jump_view = other_cfg.jump_view
	else
		jump_view = other_cfg.jump_view2
	end

	FunOpen.Instance:OpenViewNameByCfg(jump_view)
end
-----------------------------------------------------------------------------------
ThunderManaEquipBagItem = ThunderManaEquipBagItem or BaseClass(BaseRender)
function ThunderManaEquipBagItem:LoadCallBack()
	if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_pos)
		self.item_cell:SetIsShowTips(false)
		self.item_cell:SetClickCallBack(BindTool.Bind(self.OnClick, self))
    end

	XUI.AddClickEventListener(self.node_list["add_btn"], BindTool.Bind(self.ClickAdd, self))
end

function ThunderManaEquipBagItem:OnClick()
	if IsEmptyTable(self.data) then
		return
    end

	BaseRender.OnClick(self)
end

function ThunderManaEquipBagItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function ThunderManaEquipBagItem:OnFlush()
    if IsEmptyTable(self.data) then
		self.item_cell:ClearData()
		self.node_list.hl_img:SetActive(false)
		self.node_list.add_btn:SetActive(false)
		return
    end

	self.node_list.add_btn:SetActive(self.data.is_plus)
	if self.data.is_plus then
        self.item_cell:ClearData()
		self.node_list.hl_img:SetActive(false)

        local bundle, asset = ResPath.GetCommonImages("a2_ty_jia")
        self.item_cell:SetItemIcon(bundle, asset)
        self.item_cell:SetButtonComp(true)
        self.item_cell:SetEffectRootEnable(false)
		return
    end

	self.item_cell:SetData(self.data)

	local cur_equip_part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.data.seq, self.data.part)
	if cur_equip_part_info then
		local is_better = false
		if cur_equip_part_info.item_id <= 0 then
			is_better = true
		else
			if self.data.star > cur_equip_part_info.star then
				is_better = true
			end
		end
		
		self.item_cell:SetUpFlagIconVisible(is_better)
	end
end

function ThunderManaEquipBagItem:OnSelectChange(is_select)
	if not IsEmptyTable(self.data) then
		self.node_list.hl_img:SetActive(is_select and (not self.data.is_plus))
	end
end

function ThunderManaEquipBagItem:ClickAdd()
	if self.data and self.data.is_plus then
		local other_cfg = ThunderManaWGData.Instance:GetOtherCfg()
		local jump_view = ""
		if ThunderManaWGData.ThunderType.ShadyType == self.data.seq then
			jump_view = other_cfg.jump_view
		else
			jump_view = other_cfg.jump_view2
		end

		FunOpen.Instance:OpenViewNameByCfg(jump_view)
	end
end
-----------------------------------------------------------------------------------
ThunderManaEquipPartSelectItemRender = ThunderManaEquipPartSelectItemRender or BaseClass(BaseRender)
function ThunderManaEquipPartSelectItemRender:OnFlush()
    if not self.data then
		return
    end

    self.node_list.part_item_name.tmp.text = self.data
end