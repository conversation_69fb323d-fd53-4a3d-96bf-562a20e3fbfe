function MengLingView:InitMengLingInLayPanel()
    if not self.mengling_inlay_item then
        self.mengling_inlay_item = ItemCell.New(self.node_list.mengling_inlay_item)
        -- self.mengling_inlay_item:SetItemTipFrom(ItemTip.FROM_MENGLING_EQUIP)
    end

    if not self.mengling_inlay_attr_list then
        self.mengling_inlay_attr_list = AsyncBaseGrid.New()
        local bundle = "uis/view/function_collections_ui/mengling_prefab"
		local asset = "mengling_inlay_attr_item"

        self.mengling_inlay_attr_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.mengling_inlay_attr_list,
         assetBundle = bundle, assetName = asset, itemRender = CommonAttrRender})
		self.mengling_inlay_attr_list:SetStartZeroIndex(false)
    end

    if not self.mengling_inlay_item_list then
        self.mengling_inlay_item_list = {}

        for i = 0, 5 do
            self.mengling_inlay_item_list[i] = MengLingInlayItemRender.New(self.node_list["mengling_inlay_item" .. i])
            self.mengling_inlay_item_list[i]:SetIndex(i)
        end
    end
end

function MengLingView:DeleteMengLingInlayPanel()
    if self.mengling_inlay_item then
        self.mengling_inlay_item:DeleteMe()
        self.mengling_inlay_item = nil
    end

    if self.mengling_inlay_item_list then
        for k, v in pairs(self.mengling_inlay_item_list) do
            v:DeleteMe()
        end

        self.mengling_inlay_item_list = nil
    end

    if self.mengling_inlay_attr_list then
        self.mengling_inlay_attr_list:DeleteMe()
        self.mengling_inlay_attr_list = nil
    end
end

function MengLingView:FlushMengLingInlayPanel()
    if self:MengLingCanShowOperaPanel() then
        self.node_list.mengling_no_wearequip:CustomSetActive(true)
        self.node_list.mengling_inlay_panel_root:CustomSetActive(false)
        return
    end

    self.node_list.mengling_no_wearequip:CustomSetActive(false)
    self.node_list.mengling_inlay_panel_root:CustomSetActive(true)

    local data_info = MengLingWGData.Instance:GetMengLingEquipCellInfo(self.select_equip_suit_seq, self.select_equip_item_slot)
    local hole_list = data_info.hole_list
    self.mengling_inlay_item:SetData(data_info)

    if self.mengling_inlay_item_list then
        for i = 0, 5 do
            self.mengling_inlay_item_list[i]:SetData(hole_list[i] or {})
        end
    end

    local data_list = MengLingWGData.Instance:GetMengLingInlayAttrDataList(self.select_equip_suit_seq, self.select_equip_item_slot)
    self.mengling_inlay_attr_list:SetDataList(data_list)
end

-------------------------------------------MengLingInlayItemRender------------------------------------------
MengLingInlayItemRender = MengLingInlayItemRender or BaseClass(BaseRender)

function MengLingInlayItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.inlay_item, BindTool.Bind(self.OnClickInlayItem, self))
end

function MengLingInlayItemRender:__delete()
    self:CancelTween()
end

function MengLingInlayItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(self.data.equip_id)
    local unlock_store_num = equip_cfg and equip_cfg.hole_num or 0
    local is_unlock = self.index < unlock_store_num

    if is_unlock then
        local hole = self.data.hole
        local item_id = self.data.item_id
        local is_wear_store = item_id > 0
    
        if is_wear_store then
            local icon_id = ItemWGData.Instance:GetItemIconByItemId(item_id)
            local bundle, assert = ResPath.GetItem(icon_id)
            self.node_list.icon.image:LoadSprite(bundle, assert, function ()
                self.node_list.icon.image:SetNativeSize()
            end)
    
            local level = self.data.level
            self.node_list.name.text.text = ToColorStr(string.format(Language.Common.Level1, level), GameEnum.ITEM_COLOR_WHITE)
            local cur_level_cfg = MengLingWGData.Instance:GetMengLingInLayHoleLevelCfg(hole, level)
            local next_level_cfg = MengLingWGData.Instance:GetMengLingInLayHoleLevelCfg(hole, level + 1)
            local is_max_level = IsEmptyTable(next_level_cfg)
            local has_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
            local can_up = has_num >= cur_level_cfg.cost_item_num
            self.node_list.effect:CustomSetActive(not is_max_level and can_up)
            self:SetBaoShiRemind(not is_max_level and can_up)
        else
            local stuff_data_list = MengLingWGData.Instance:GetMengLingInlayStoreList(hole)
            local can_wear = not IsEmptyTable(stuff_data_list)
            self.node_list.effect:CustomSetActive(can_wear)
            self:SetBaoShiRemind(can_wear)
        end

        self.node_list.name_bg:CustomSetActive(is_wear_store)
        self.node_list.add:CustomSetActive(not is_wear_store)
        self.node_list.icon:CustomSetActive(is_wear_store)
    else
        self.node_list.name_bg:CustomSetActive(false)
        self.node_list.add:CustomSetActive(false)
        self:SetBaoShiRemind(false)
        self.node_list.effect:CustomSetActive(false)
        self.node_list.icon:CustomSetActive(false)
    end

    self.node_list.lock:CustomSetActive(not is_unlock)
end

function MengLingInlayItemRender:CancelTween()
    if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function MengLingInlayItemRender:SetBaoShiRemind(enable)
	self.node_list["up_flag"]:SetActive(enable)
    self:CancelTween()

	if enable then
		RectTransform.SetAnchoredPositionXY(self.node_list["up_flag"].rect, 0, 0)
		self.arrow_tweener = self.node_list["up_flag"].gameObject.transform:DOAnchorPosY(10, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function MengLingInlayItemRender:OnClickInlayItem()
    if IsEmptyTable(self.data) then
        return
    end

    local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(self.data.equip_id)
    local unlock_store_num = equip_cfg and equip_cfg.hole_num or 0
    local is_unlock = self.index < unlock_store_num

    if is_unlock then
        local item_id = self.data.item_id
        local is_wear_store = item_id > 0
        local hole = self.data.hole

        if is_wear_store then
            local level = self.data.level
            local cur_level_cfg = MengLingWGData.Instance:GetMengLingInLayHoleLevelCfg(hole, level)
            local next_level_cfg = MengLingWGData.Instance:GetMengLingInLayHoleLevelCfg(hole, level + 1)
    
            local is_max_level = IsEmptyTable(next_level_cfg)
            local cost_item_id = cur_level_cfg.cost_item_id
            local cost_item_num = cur_level_cfg.cost_item_num
            local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    
            if not is_max_level and has_num >= cost_item_num then
                MengLingWGCtrl.Instance:OpenMengLingUpStoreView(self.data)
            else
                TipWGCtrl.Instance:OpenItem({item_id = item_id})
            end
        else
            local seq = self.data.seq
            local slot = self.data.slot
            local stuff_data_list = MengLingWGData.Instance:GetMengLingInlayStoreList(hole)
            if not IsEmptyTable(stuff_data_list) then
                MengLingWGCtrl.Instance:OpenMengLingInlayStoreView(seq, slot, hole, stuff_data_list)
            else
                local hole_cfg = MengLingWGData.Instance:GetmengLingStoreHoleCfg(hole)
                TipWGCtrl.Instance:OpenItem({item_id = hole_cfg.item_id})
            end
    
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MengLing.MengLingInlayUnLockTip)
    end
end