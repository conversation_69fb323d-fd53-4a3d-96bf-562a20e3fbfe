ControlBeastsHoleTip = ControlBeastsHoleTip or BaseClass(SafeBaseView)

function ControlBeastsHoleTip:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(706, 488)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_hole_tips")
	self:SetMaskBg(true)

	self.tips_data = nil
end

function ControlBeastsHoleTip:__delete()
	self.tips_data = nil
end

function ControlBeastsHoleTip:ReleaseCallBack()
	self.tips_data = nil

	if self.condition_list then
        self.condition_list:DeleteMe()
        self.condition_list = nil
    end

	if self.buy_remind_alert ~= nil then
		self.buy_remind_alert:DeleteMe()
		self.buy_remind_alert = nil
	end
end

function ControlBeastsHoleTip:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_OK, BindTool.Bind2(self.On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind2(self.TipsOpen, self))
	-- 初始化6辅战
	if not self.condition_list then
		self.condition_list = AsyncListView.New(BeststsHoleConditionRender, self.node_list.condition_scroll)
	end
end


function ControlBeastsHoleTip:SetTipsData(tips_data)
	self.tips_data = tips_data
end

function ControlBeastsHoleTip:OnFlush()
	if not self.tips_data then
		return
	end

	self.node_list.title_view_name.text.text = self.tips_data.title_name or ""
	local cfg_data = ControlBeastsWGData.Instance:GetHoleCfgById(self.tips_data.hole_id)
	local is_battle_hole = self.tips_data.hole_id <= 2
	self.node_list.desc_01:CustomSetActive(is_battle_hole)
	self.node_list.btn_tips:CustomSetActive(not is_battle_hole)

	if cfg_data then
		local list, is_can_unlock = self:SetHoleConditionData(cfg_data)
		self.condition_list:SetDataList(list)
		self.node_list.desc_02.text.text = self:MontageHoleAttrStr(is_battle_hole, cfg_data)
		self.node_list.xianyu_icon:CustomSetActive(cfg_data.condition_type == 1 and cfg_data.condition_value ~= 0)
		local str = ""
		
		if cfg_data.condition_type == 2 then	--2-直购id
			local price = RoleWGData.GetPayMoneyStr(cfg_data.condition_value, cfg_data.rmb_type, cfg_data.rmb_seq)
			str = price
		elseif cfg_data.condition_type == 1 then	--1-灵玉
			local cost_value = cfg_data and cfg_data.condition_value or 0
			if cost_value == 0 then
				str = Language.Role.JieSuo
			else
				str = cost_value
			end
		else
			str = Language.Role.JieSuo
		end

		if not is_can_unlock then
			str = Language.Common.GoTo
		end

		self.node_list.btn_text.text.text = str
	end
end

-- 设置条件数体
function ControlBeastsHoleTip:SetHoleConditionData(cfg_data)
	local condition_table = {}
	local is_can_unlock = true

	if cfg_data.role_level and cfg_data.role_level ~= 0 then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		is_can_unlock = role_level >= cfg_data.role_level and is_can_unlock
		table.insert(condition_table, self:MontageHoleConditionStr(role_level, cfg_data.role_level or 0, Language.ContralBeasts.HoleTips3))
	end

	if cfg_data.fb_tower_level and cfg_data.fb_tower_level ~= 0 then
		local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
		is_can_unlock = pass_level >= cfg_data.fb_tower_level and is_can_unlock
		table.insert(condition_table, self:MontageHoleConditionStr(pass_level, cfg_data.fb_tower_level or 0, Language.ContralBeasts.HoleTips4))
	end

	if cfg_data.zhuanzhi and cfg_data.zhuanzhi ~= 0 then
		local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
		is_can_unlock = prof_level >= cfg_data.zhuanzhi and is_can_unlock
		table.insert(condition_table, self:MontageHoleConditionStr(prof_level, cfg_data.zhuanzhi or 0, Language.ContralBeasts.HoleTips4_1))
	end

	if cfg_data.vip_limit and cfg_data.vip_limit ~= 0 then
		local cur_vip_lv = VipWGData.Instance:GetVipLevel()
		is_can_unlock = cur_vip_lv >= cfg_data.vip_limit and is_can_unlock
		table.insert(condition_table, self:MontageHoleConditionStr(cur_vip_lv, cfg_data.vip_limit or 0, Language.ContralBeasts.HoleTips5))
	end

	return condition_table, is_can_unlock
end

-- 拼接条件文本字符串
function ControlBeastsHoleTip:MontageHoleConditionStr(cur, max, str)
	local temp_data = {}
	temp_data.is_enough = cur >= max
	local color = temp_data.is_enough and COLOR3B.D_GREEN or COLOR3B.WHITE
	local num_color = temp_data.is_enough and COLOR3B.D_GREEN or COLOR3B.RED
	local new_str = string.format(str, ToColorStr(cur, num_color), max)
	temp_data.desc = ToColorStr(new_str, color)

	return temp_data
end

-- 拼接属性文本字符串
function ControlBeastsHoleTip:MontageHoleAttrStr(is_battle_hole)
	local hole_data = ControlBeastsWGData.Instance:GetHoleDataById(self.tips_data.hole_id + 1)

	if (not hole_data) or (not hole_data.attr_types) then
		return
	end

	local per = 100
	local new_str = ""

	if is_battle_hole or hole_data.attr_types == 0 then
		new_str = string.format(Language.ContralBeasts.HoleTips7, per, new_str)
	else
		for index, attr_str in ipairs(hole_data.attr_list) do
			new_str = tostring(new_str .. EquipmentWGData.Instance:GetAttrName(attr_str, false, false))
	
			if index ~= #hole_data.attr_list then
				new_str = new_str .. "、"
			end
		end

		local hole_data = ControlBeastsWGData.Instance:GetHoleLevelCfgById(self.tips_data.hole_id, 1)
		if hole_data and hole_data.hole_addition then
			per = math.floor(hole_data.hole_addition / 100) 
		end

		new_str = string.format(Language.ContralBeasts.HoleTips7, per, new_str)
	end

	return new_str
end


function ControlBeastsHoleTip:OnClinkOkHandler()
	local cfg_data = ControlBeastsWGData.Instance:GetHoleCfgById(self.tips_data.hole_id)
	if (not cfg_data) or (not self.tips_data) then
		return
	end

	local _, is_can_unlock = self:SetHoleConditionData(cfg_data)
	if not is_can_unlock then
		if cfg_data.open_panel and cfg_data.open_panel ~= "" then
			FunOpen.Instance:OpenViewNameByCfg(cfg_data.open_panel)
			self:Close()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActCantOpen2)
		end

		return
	end

	-- local last_hole_data = ControlBeastsWGData.Instance:GetHoleDataById(self.tips_data.hole_id) --当前对应的正好是上一个孔位, 和下标差一个位置， 配置表对应的是这个id
    -- if last_hole_data and last_hole_data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.HoleTips18)	
	-- 	return
	-- end

	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold

	if nil == self.buy_remind_alert then
		self.buy_remind_alert = Alert.New(nil, nil, nil, nil, true)
		self.buy_remind_alert:SetShowCheckBox(true, "beasts_purchase")
		self.buy_remind_alert:SetCheckBoxDefaultSelect(false)
	end

	if cfg_data.condition_type == 2 then	--2-直购id
		local price = RoleWGData.GetPayMoneyStr(cfg_data.condition_value, cfg_data.rmb_type, cfg_data.rmb_seq)
		self.buy_remind_alert:SetLableString(string.format(Language.ContralBeasts.HoleTips11, price, self.tips_data.title_name))
		self.buy_remind_alert:SetOkFunc(function()
			RechargeWGCtrl.Instance:Recharge(cfg_data.condition_value, cfg_data.rmb_type, cfg_data.rmb_seq)
			self:Close()
		end)

		self.buy_remind_alert:Open()
	elseif cfg_data.condition_type == 1 then	--1-灵玉
		local cost_value = cfg_data and cfg_data.condition_value or 0
		if cost_value == 0 then
			--发送协议
			ControlBeastsWGCtrl.Instance:SendOperateTypeSlotUnlock(self.tips_data.hole_id)
			self:Close()
			return
		end

		self.buy_remind_alert:SetLableString(string.format(Language.ContralBeasts.HoleTips8, cost_value, self.tips_data.title_name))
		self.buy_remind_alert:SetOkFunc(function()
			if role_gold < cost_value then
				VipWGCtrl.Instance:OpenTipNoGold()
			else
				--发送协议
				ControlBeastsWGCtrl.Instance:SendOperateTypeSlotUnlock(self.tips_data.hole_id)
				self:Close()
			end
		end)

		self.buy_remind_alert:Open()
	else
		--发送协议
		ControlBeastsWGCtrl.Instance:SendOperateTypeSlotUnlock(self.tips_data.hole_id)
		self:Close()
	end
end

function ControlBeastsHoleTip:TipsOpen()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.ContralBeasts.HoleTips9)
	rule_tip:SetContent(Language.ContralBeasts.HoleTips10, nil, nil, nil, true)
end
