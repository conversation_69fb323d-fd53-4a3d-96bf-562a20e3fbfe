GuildJiuSheDaoFBFollow = GuildJiuSheDaoFBFollow or BaseClass(SafeBaseView)

function GuildJiuSheDaoFBFollow:__init()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "BangPaiJinDi")
	
end

function GuildJiuSheDaoFBFollow:__delete()
end

function GuildJiuSheDaoFBFollow:ReleaseCallBack()
	if self.box_cell then
		for k,v in pairs(self.box_cell) do
			v:DeleteMe()
		end
		self.box_cell = {}
	end
	if self.boss_cell then
		for k,v in pairs(self.boss_cell) do
			v:DeleteMe()
		end
		self.boss_cell = {}
	end
end

function GuildJiuSheDaoFBFollow:LoadCallBack(index, loaded_times)
	self.box_cell = {}
	self.boss_cell = {}
	local function callback(show_node)
		ResMgr:LoadGameobjSync("uis/view/guild_ui_prefab", "layout_guild_jiushedao",
				function (obj)
					obj.transform:SetParent(show_node.transform,false)
					obj.transform:GetComponent(typeof(UnityEngine.RectTransform)).anchoredPosition = Vector2(-523.5,27.5)
					-- -523.5 27.5
					obj = U3DObject(obj)
					self.data_node = BaseRender.New(obj)
					for i=1,3 do
						self.box_cell[i] = ItemCell.New(self.data_node.node_list["ph_boxcell_" .. i])
						self.box_cell[i].root_node.transform.localScale = Vector3(0.8,0.8,0.8)
					end

					for i=1,3 do
						self.boss_cell[i] = ItemCell.New(self.data_node.node_list["ph_bosscell_" .. i])
						self.boss_cell[i].root_node.transform.localScale = Vector3(0.8,0.8,0.8)
					end
					MainuiWGCtrl.Instance:SetTaskPanel(false,0,-100)
					self:Flush()
				end)
	end
	MainuiWGCtrl.Instance:GetTaskMaskRootNode(callback)
end

function GuildJiuSheDaoFBFollow:CloseCallBack()
	if self.data_node then
		local obj = self.data_node.node_list.jiushedao_root_node.gameObject
		self.data_node:DeleteMe()
		ResMgr:Destroy(obj)
		self.data_node = nil
	end
	MainuiWGCtrl.Instance:ResetTaskPanel()
end

function GuildJiuSheDaoFBFollow:OnClickLeft(btn)
	if nil ~= self.action_timer then
		GlobalTimerQuest:CancelQuest(self.action_timer)
		self.action_timer = nil
	end
end

function GuildJiuSheDaoFBFollow:ShowIndexCallBack()
	self:Flush()
end

function GuildJiuSheDaoFBFollow:OnFlush()
	if self.data_node == nil then return end
	local data = GuildWGData.Instance
	self.data_node.node_list["lbl_boss_hp"].text.text = (data:GetJiuSheDaoFBBossHpPer() .. "%")
	self.data_node.node_list["lbl_get_box"].text.text = "["..(data:GetJiuSheDaoFBHasTakenBox() .. "/" .. data:GetJiuSheDaoFBBoxNum()).."]"
	self.data_node.node_list["lbl_reborn"].text.text = (data:GetJiuSheDaoFBHasPassedCount())
	local box_reward, boss_reward = data:GetJiuSheDaoFBReward()
	for i,v in ipairs(box_reward) do
		self.box_cell[i]:SetData(v)
	end
	for i,v in ipairs(boss_reward) do
		self.boss_cell[i]:SetData(v)
	end
end