ConquestWarWGData = ConquestWarWGData or BaseClass()

function ConquestWarWGData:__init()
    if ConquestWarWGData.Instance then
		print_error("[ConquestWarWGData]:Attempt to create singleton twice!")
	end

    ConquestWarWGData.Instance = self

    local crosssetting_cfg = ConfigManager.Instance:GetAutoConfig("crosssetting_auto")
	self.conquest_war_view_display_cfg = ListToMap(crosssetting_cfg.conquest_war_view_display, "id")--征战界面显示配置

end

function ConquestWarWGData:__delete()
    ConquestWarWGData.Instance = nil
	
    self.conquest_war_view_display_cfg = nil
end

function ConquestWarWGData:GetConquestWarActivityInfoById(id)
    return self.conquest_war_view_display_cfg[id] or {}
end