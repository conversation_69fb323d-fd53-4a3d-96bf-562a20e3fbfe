DragonTrialWGData = DragonTrialWGData or BaseClass()

local activat_key = "yuanshen_activated"

function DragonTrialWGData:__init()
	if DragonTrialWGData.Instance ~= nil then
		error("[DragonTrialWGData] attempt to create singleton twice!")
		return
	end
	DragonTrialWGData.Instance = self
	self:InitConfig()

	-- 红点注册
	RemindManager.Instance:Register(RemindName.DragonTrial, BindTool.Bind(self.GetDragonTrialRed,self))
end

function DragonTrialWGData:__delete()
	DragonTrialWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.DragonTrial)
end

-- 初始化配置
function DragonTrialWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("fb_dragon_trial_auto")
	self.dragon_trial_progress_cfg = {}
	self.old_str_list = {}
	if cfg then
		self.dragon_trial_base_cfg = cfg.other[1]
        self.dragon_trial_grade_cfg = ListToMap(cfg.grade, "grade")
		self.dragon_trial_monster_cfg = ListToMap(cfg.monster, "grade", "seq")
		self.dragon_trial_progress_cfg = cfg.progress or self.dragon_trial_progress_cfg
	end
end

-- 获取基础信息表
function DragonTrialWGData:GetDragonTrialBaseCfg()
	return self.dragon_trial_base_cfg
end

-- 获取开启等级
function DragonTrialWGData:GetDragonTrialOpenLevel()
	return self.dragon_trial_base_cfg and self.dragon_trial_base_cfg.open_level or nil
end

-- 获取基础表属性
function DragonTrialWGData:GetDragonTrialBaseCfgParam(param_str)
	return self.dragon_trial_base_cfg and self.dragon_trial_base_cfg[param_str] or nil
end

-- 获取当前档次配置
function DragonTrialWGData:GetCurGradeCfg()
    local cur_grade = self:GetCurrentGrade()
	return self.dragon_trial_grade_cfg and self.dragon_trial_grade_cfg[cur_grade] or nil
end

-- 获取当前档位关卡配置列表
function DragonTrialWGData:GetLevelMonsterCfgList()
	local emtry = {}
    local cur_grade = self:GetCurrentGrade()
	return (self.dragon_trial_monster_cfg or emtry)[cur_grade] or nil
end

-- 获取关卡配置
function DragonTrialWGData:GetLevelMonsterCfg(seq)
	local cfg_list = self:GetLevelMonsterCfgList()
	if (not cfg_list) or (#cfg_list <= 0) then
		return nil
	end
	local real_index = seq > #cfg_list and #cfg_list or seq
	return cfg_list[real_index]
end

-- 获取关卡专属奖励
function DragonTrialWGData:GetLevelMonsterReward(seq)
	local cfg_list = self:GetLevelMonsterCfgList()
	if (not cfg_list) or (#cfg_list <= 0) then
		return nil
	end
	local real_index = seq > #cfg_list and #cfg_list or seq

	-- 从当前关卡往后找
	local data = {}
	for i = real_index, #cfg_list do
		local cfg = cfg_list[i]
		if cfg and cfg.fixed_level_reward ~= 0 then
			data.is_show_tag = true
			data.show_level = cfg.seq
			self:SetFixLevelReward(data, cfg)
			break
		end
	end
	
	return data
end

-- 包装专属奖励数据
function DragonTrialWGData:SetFixLevelReward(data, cfg)
	local fixed_level_reward = cfg.fixed_level_reward
	local str_list = self.old_str_list[cfg.seq]
	if self.old_fixed_level_reward ~= fixed_level_reward or not str_list then
		self.old_fixed_level_reward = fixed_level_reward
		str_list = string.split(fixed_level_reward, ":")
		self.old_str_list[cfg.seq] = str_list
	end
	data.item_id = tonumber(str_list[1]) or 0
	data.num = tonumber(str_list[2]) or 0
	data.is_bind = tonumber(str_list[3]) or 0
end

--获取当前能达到的最高层数
function DragonTrialWGData:GetCurCanQuickFinishLevel(cur_level)
	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local temp_level = cur_level
	local level_limit = 0
	local need_need_cap = 0
	local is_max = false

    local level_cfg_list = self:GetLevelMonsterCfgList()
    local max_jump_level = #level_cfg_list

    if temp_level >= max_jump_level then
        is_max = true
    else
        for i = temp_level + 1, max_jump_level do
            local cfg = self:GetLevelMonsterCfg(i)
            if cfg.need_cap > role_cap then
                need_need_cap = cfg.need_cap
                break
            end
            if role_lv < cfg.need_level then
                level_limit = cfg.need_level
                break
            end
            temp_level = i
        end
    end
	return temp_level, level_limit, need_need_cap, is_max
end

-- 获取奖励列表(超过100取10层的奖励，没超过100取每层的奖励)
function DragonTrialWGData:GetJumpLevelRewardList(seq, start_seq)
	local cfg_list = self:GetLevelMonsterCfgList()
	if (not cfg_list) or (#cfg_list <= 0) then
		return {}
	end

	local offset = seq - start_seq
	local is_ten_offset = false

	if offset >= 100 then
		is_ten_offset = true
	end

	if offset < 0 then
		return {}
	end

	local reward_list = {}

	for i = start_seq, seq do
		if cfg_list[i] then
			if is_ten_offset then
				if i % 10 == 0 then
					self:ComposeRewardList(reward_list, cfg_list[i])
				end
			else
				self:ComposeRewardList(reward_list, cfg_list[i])
			end
		end
	end

	local return_reward_list = {}
	for _, reward_data in pairs(reward_list) do
		if reward_data and reward_data.item_id then
			table.insert(return_reward_list, reward_data)
		end
	end

	return return_reward_list
end

-- 组装奖励
function DragonTrialWGData:ComposeRewardList(reward_list, cfg)
	if not reward_list then
		reward_list = {}
	end

	if cfg and cfg.pass_reward_item then
		for _, reward_data in pairs(cfg.pass_reward_item) do
			if reward_data and reward_data.item_id then
				if reward_list[reward_data.item_id] then
					reward_list[reward_data.item_id].num = reward_list[reward_data.item_id].num + reward_data.num 
				else
					reward_list[reward_data.item_id] = {}
					reward_list[reward_data.item_id].item_id = reward_data.item_id
					reward_list[reward_data.item_id].num = reward_data.num
					reward_list[reward_data.item_id].is_bind = reward_data.is_bind
				end
			end
		end
	end
end

-------------------服务器相关数据-------------------
-- 龙神试炼信息
function DragonTrialWGData:SetDragonTrialInfo(protocol)
    self.dragon_trial_info = protocol
    self.grade = protocol.grade
	self.pass_seq = protocol.pass_seq
end

-- 获取服务器信息
function DragonTrialWGData:GetDragonTrialInfo()
	return self.dragon_trial_info
end

-- 获取当前档位
function DragonTrialWGData:GetCurrentGrade()
	return self.grade or 0
end

-- 获取已通关卡数
function DragonTrialWGData:GetCurrentPassSeq()
	return self.pass_seq or 0
end

-- 设置场景数据
function DragonTrialWGData:SetDragonTrialSceneInfo(protocol)
	if self.scene_info == nil then
		self.scene_info = {}
	end

	self.scene_info = protocol
end

-- 获取场景数据
function DragonTrialWGData:GetDragonTrialSceneInfo()
	return self.scene_info
end
-------------------end-------------------

-- 龙神试炼红点
function DragonTrialWGData:GetDragonTrialRed()
    local cur_level_seq = self:GetCurrentPassSeq() + 1
    local cfg_list = self:GetLevelMonsterCfgList()
    if not cfg_list or not cfg_list[cur_level_seq] then
        return 0
    end
    local cfg_data = cfg_list[cur_level_seq]

	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
    local role_lv = RoleWGData.Instance:GetRoleLevel()

    if role_lv >= cfg_data.need_level and role_cap >= cfg_data.need_cap then
		return 1
	end

	local is_can_activate = self:IsCanActivate()
	local is_activated = CultivationWGData.Instance:IsOpenNuQi()
	if not is_activated and is_can_activate then
		return 1
	end

	return 0
end

-- 进度数
function DragonTrialWGData:GetProgressNum()
	return #self.dragon_trial_progress_cfg
end

-- 获取进度配置
function DragonTrialWGData:GetTargetLevel(progress)
	local level = 9999
	for key, value in pairs(self.dragon_trial_progress_cfg) do
		if progress == value.progress then
			level = value.pass_seq
			break
		end
	end

	return level
end

-- 检测是否解锁
function DragonTrialWGData:CheckUnlock(progress)
	local taget_level = self:GetTargetLevel(progress)
	local cur_level = self:GetCurrentPassSeq()
	return cur_level >= taget_level
end

-- 是否全部通关
function DragonTrialWGData:IsAllUnlock()
	local cfg_list = self:GetLevelMonsterCfgList()
	if (not cfg_list) or (#cfg_list <= 0) then
		return true
	end
	return self:GetCurrentPassSeq() >= #cfg_list
end

-- 是否可以激活
function DragonTrialWGData:IsCanActivate()
	local unlock_level = self.dragon_trial_base_cfg and self.dragon_trial_base_cfg.unlock_level or 0
	return unlock_level <= self:GetCurrentPassSeq()
end