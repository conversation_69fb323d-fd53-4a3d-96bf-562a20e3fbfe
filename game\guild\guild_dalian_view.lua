GuildDaLianView = GuildDaLianView or BaseClass(SafeBaseView)

function GuildDaLianView:__init()
    self.view_layer = UiLayer.Pop

    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_mengzhan_dalian")
end

function GuildDaLianView:ReleaseCallBack()
    if self.reward_suit_list then
        for k, v in pairs(self.reward_suit_list) do
            if v then
                v:DeleteMe()
            end
        end
        self.reward_suit_list = {}
    end

    if self.reward_prop_list then
        for k, v in pairs(self.reward_prop_list) do
            if v then
                v:DeleteMe()
            end
        end
        self.reward_prop_list = {}
    end

    if self.reward_exp_list then
        self.reward_exp_list:DeleteMe()
        self.reward_exp_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    CountDownManager.Instance:RemoveCountDown("guild_dalian_down_time")
end

function GuildDaLianView:CloseCallBack()
    if self.node_list.not_tip_toggle then
        local is_not_tip = self.node_list.not_tip_toggle.toggle.isOn
        if is_not_tip then
            local uuid = RoleWGData.Instance:GetUUid()
            local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "guild_dalian_flag")
            PlayerPrefsUtil.SetInt(key, 1)
        end
    end
end

function GuildDaLianView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGotoBtn, self))
    self:InitModel()
    self.reward_suit_list = {}
    self.reward_prop_list = {}
    local reward_cfg = GuildBattleRankedWGData.Instance:GetGuildWarReward()

    if not IsEmptyTable(reward_cfg) then
        local bazhu_fashion_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shizhuang_index") or 1
        local bazhu_shenbing_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shenbing_index") or 1
        local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(1, bazhu_fashion_index)
        local shenbing_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(6, bazhu_shenbing_index)
        local data2 = reward_cfg.show_reward_item
        local data2_1 = {}
        local data2_2 = {}

        if fashion_cfg then
            local prof = GameVoManager.Instance:GetMainRoleVo().prof
            local _, weapon_res_id, __ = AppearanceWGData.Instance:GetRoleResId()
            local res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
            
            local extra_role_model_data = {
                animation_name = SceneObjAnimator.Rest,
            }
            self.model_display:SetRoleResid(res_id, nil, extra_role_model_data)
            self.model_display:SetWeaponResid(weapon_res_id)
        end

        if fashion_cfg then
            local item_id = NewAppearanceWGData.Instance:GetFashionItemId(1, bazhu_fashion_index)
            local item = { item_id = item_id, num = 1, is_bind = 1, has_top = true }
            table.insert(data2_1, item)
        end

        if shenbing_cfg then
            local item_id = NewAppearanceWGData.Instance:GetFashionItemId(6, bazhu_shenbing_index)
            local item = { item_id = item_id, num = 1, is_bind = 1, has_top = true }
            table.insert(data2_1, item)
        end

        for i = 0, 1 do
            table.insert(data2_2, data2[i])
        end

        for k, v in pairs(data2_1) do
            self.reward_suit_list[k] = ItemCell.New(self.node_list.item_list_1)
            self.reward_suit_list[k]:SetData(data2_1[k])
        end

        for k, v in pairs(data2_2) do
            self.reward_prop_list[k] = ItemCell.New(self.node_list.item_list_2)
            self.reward_prop_list[k]:SetData(data2_2[k])
        end

        self.reward_exp_list = ItemCell.New(self.node_list.item_list_3)
        self.reward_exp_list:SetData(data2[2])
    end
end

function GuildDaLianView:OnFlush()
    self:RefreshView()
    self:AutoCloseView()
end

function GuildDaLianView:OnClickGotoBtn()
    self:Close()
    ViewManager.Instance:Open(GuideModuleName.GuildView, TabIndex.guild_War)
end

function GuildDaLianView:InitModel()
    if nil == self.model_display then
        self.model_display = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ph_bianshen_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.model_display:SetRenderTexUI3DModel(display_data)

        -- self.model_display:SetUI3DModel(self.node_list["ph_bianshen_display"].transform,
        --     self.node_list["ph_bianshen_display"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.model_display)
    end
end

function GuildDaLianView:RefreshView()
    self.node_list["num"].text.text = string.format(Language.Guild.ActivityDay)
end

function GuildDaLianView:AutoCloseView()
    CountDownManager.Instance:RemoveCountDown("guild_dalian_down_time")
    self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3, 30)
    CountDownManager.Instance:AddCountDown("guild_dalian_down_time",
        function(elapse_time, total_time)
            self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3,
                math.ceil(total_time - elapse_time))
        end,
        function()
            self:Close()
        end, nil, 30, 1)
end
