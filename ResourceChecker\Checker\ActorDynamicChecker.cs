﻿#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;

public class ActorDynamicChecker : DynamicChecker
{
    private static readonly string ActorDir = "actors/";
    private static readonly string ModelDir = "model/";
    //private static readonly string RoleDir = "actors/character/";
    //private static readonly string MonsterDir = "actors/momster/";
    //private static readonly string NpcDir = "actors/npc/";
    //private static readonly string MountDir = "actors/mount/";
    //private static readonly string GatherDir = "actors/gather/";
    private static HashSet<string> filterList = new HashSet<string>()
    {

    };
    private static Dictionary<UnityEngine.Object, string> refPaths = new Dictionary<UnityEngine.Object, string>();

    protected override bool NeedCheck(string bundleName)
    {
        if (bundleName.StartsWith(ActorDir) || bundleName.StartsWith(ModelDir))
        {
            return true;
        }
        return false;
    }

    protected override void Check(string assetPath, Action<CheckObject[]> callback)
    {
        List<CheckObject> list = new List<CheckObject>();
        GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
        if (null != go)
        {
            if (null != go.GetComponent<ActorRender>())
            {
                if (go.GetComponentsInChildren<ParticleSystem>().Length > 0)
                {
                    CheckObject checkObj = new CheckObject(go, "请用GameObjectAttach组件去加载ParticleSystem", WarningLevel.High);
                    list.Add(checkObj);
                }

                CheckCamera(go, list);
                CheckMultiActorRender(go, list);
                CheckShader(go, list);
            }

            CheckActorController(go, list);
            CheckActorAttachEffect(go, list);
            CheckLimitSceneEffects(go, list);
            CheckMissingReferences(go, list);
            //CheckMultiMaterials(go, list);
            //CheckSameMesh(go, list);
        }

        callback(list.ToArray());
    }

    protected override HashSet<string> GetFilterList()
    {
        return filterList;
    }

    protected override BaseWarningWindow GetWindow()
    {
        return UnityEditor.EditorWindow.GetWindowWithRect<ActorWarningWindow>(new Rect(1200, 600, 800, 600));
    }

    private static void CheckCamera(GameObject go, List<CheckObject> list)
    {
        Camera[] cameras = go.GetComponentsInChildren<Camera>(true);
        for (int i = 0; i < cameras.Length; ++i)
        {
            var camera = cameras[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的camera组件是多余的，请及时处理", camera.name), WarningLevel.Normal);
            list.Add(checkObj);
        }
    }

    private static void CheckMultiActorRender(GameObject go, List<CheckObject> list)
    {
        ActorRender[] actorRenders = go.GetComponentsInChildren<ActorRender>(true);
        if (actorRenders.Length > 1)
        {
            for (int i = 1; i < actorRenders.Length; ++i)
            {
                var actorRender = actorRenders[i];
                CheckObject checkObj = new CheckObject(go, string.Format("{0}一个预制体只能存在一个ActorRender组件，请及时处理", actorRender.name), WarningLevel.Normal);
                list.Add(checkObj);
            }
        }
    }

    private static void CheckActorController(GameObject go, List<CheckObject> list)
    {
        ActorController[] actorControllers = go.GetComponentsInChildren<ActorController>(true);
        for (int i = 0; i < actorControllers.Length; ++i)
        {
            var actorController = actorControllers[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的ActorController组件是多余的，请及时处理", actorController.name), WarningLevel.Normal);
            list.Add(checkObj);
        }
    }

    private static void CheckActorAttachEffect(GameObject go, List<CheckObject> list)
    {
        ActorAttachEffect[] actorAttachEffects = go.GetComponentsInChildren<ActorAttachEffect>(true);
        for (int i = 0; i < actorAttachEffects.Length; ++i)
        {
            var actorAttach = actorAttachEffects[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的ActorAttachEffect组件是多余的，请及时处理", actorAttach.name), WarningLevel.Normal);
            list.Add(checkObj);
        }
    }

    private static void CheckLimitSceneEffects(GameObject go, List<CheckObject> list)
    {
        LimitSceneEffects[] limitSceneEffects = go.GetComponentsInChildren<LimitSceneEffects>(true);
        for (int i = 0; i < limitSceneEffects.Length; ++i)
        {
            var limitSceneEffect = limitSceneEffects[i];
            CheckObject checkObj = new CheckObject(go, string.Format("{0}结点上的LimitSceneEffects组件是多余的，请及时处理", limitSceneEffect.name), WarningLevel.Normal);
            list.Add(checkObj);
        }
    }

    private static void CheckShader(GameObject go, List<CheckObject> list)
    {
        List<Material> materialList = new List<Material>();
        Renderer[] renderers = go.GetComponentsInChildren<Renderer>(true);
        for (int i = 0; i < renderers.Length; ++i)
        {
            var renderer = renderers[i];
            var sharedMaterials = renderer.sharedMaterials;
            materialList.AddRange(sharedMaterials);
        }

        for (int i = 0; i < materialList.Count; ++i)
        {
            var sharedMaterial = materialList[i];
            if (null == sharedMaterial)
            {
                continue;
            }
            var shader = sharedMaterial.shader;
            if (null == shader)
            {
                CheckObject checkObj = new CheckObject(go, "Shader为null", WarningLevel.Normal);
                list.Add(checkObj);
            }
            else if (shader.name == "Standard")
            {
                CheckObject checkObj = new CheckObject(go, "Shader非法，不要使用Standard", WarningLevel.Normal);
                list.Add(checkObj);
            }
        }
    }

    private void CheckMissingReferences(GameObject go, List<CheckObject> list)
    {
        var missingObjects = GetMissingComponents(go);
        for (int i = 0; i < missingObjects.Length; ++i)
        {
            var missingObj = missingObjects[i];
            CheckObject checkObj;
            if (refPaths.ContainsKey(missingObj))
                checkObj = new CheckObject(go, string.Format("{0}结点上的{1}组件引用丢失: {2}", missingObj.name, ((Component)missingObj).GetType(), refPaths[missingObj]), WarningLevel.Low);
            else
                checkObj = new CheckObject(go, string.Format("{0}结点上组件丢失", missingObj.name), WarningLevel.Low);

            list.Add(checkObj);
        }

        if (missingObjects.Length > 0)
        {
            WarningLevel level = WarningLevel.Normal;
            if (missingObjects.Length > 20)
                level = WarningLevel.High;
            CheckObject checkObj = new CheckObject(go, string.Format("预制体上一共有{0}个引用丢失", missingObjects.Length), level);
            list.Add(checkObj);
        }
    }

    private static UnityEngine.Object[] GetMissingComponents(GameObject go)
    {
        List<UnityEngine.Object> list = new List<UnityEngine.Object>();

        Queue<Transform> queue = new Queue<Transform>();
        queue.Enqueue(go.transform);
        while (queue.Count > 0)
        {
            var trans = queue.Dequeue();
            for (int i = 0; i < trans.childCount; ++i)
            {
                queue.Enqueue(trans.GetChild(i));
            }

            Component[] cps = trans.GetComponents<Component>();
            foreach (var cp in cps)
            {
                if (null == cp)
                {
                    list.Add(trans.gameObject);
                    continue;
                }
                SerializedObject so = new SerializedObject(cp);
                var iter = so.GetIterator();
                while (iter.NextVisible(true))
                {
                    if (iter.propertyType == SerializedPropertyType.ObjectReference)
                    {
                        if (iter.objectReferenceValue == null && iter.objectReferenceInstanceIDValue != 0)
                        {
                            list.Add(cp);
                            if (!refPaths.ContainsKey(cp))
                                refPaths.Add(cp, iter.propertyPath);
                            else
                                refPaths[cp] += " + " + iter.propertyPath;
                        }
                    }
                }
            }
        }

        return list.ToArray();
    }

    private void CheckMultiMaterials(GameObject go, List<CheckObject> list)
    {
        ActorRender actorRender = go.GetComponent<ActorRender>();
        if (null == actorRender) return;

        Renderer[]renderers = actorRender.GetRenderers();
        for (int i = 0; i < renderers.Length; i++)
        {
            Material[] mats = MaterialMgr.Instance.GetSharedMaterials(renderers[i]);
            int count = 0;
            for (int m = 0; m < mats.Length; m++)
            {
                if (mats[m].name != "RoleOcclusion")
                {
                    ++count;
                }
            }
            if (count > 1)
            {
                CheckObject checkObj = new CheckObject(go, string.Format("{0} {1}使用了{2}个材质球，影响渲染效率， 请改为1个", go.name, renderers[i].name, count), WarningLevel.Normal);
                list.Add(checkObj);
            }
        }
    }

    private void CheckSameMesh(GameObject go, List<CheckObject> list)
    {
        ActorRender actorRender = go.GetComponent<ActorRender>();
        if (null == actorRender) return;

        Renderer[] renderers = actorRender.GetRenderers();
        HashSet<Mesh> meshSet = new HashSet<Mesh>();
        for (int i = 0; i < renderers.Length; i++)
        {
            Mesh mesh = null;
            SkinnedMeshRenderer skinnedRender = renderers[i] as SkinnedMeshRenderer;
            if (null != skinnedRender)
            {
                mesh = skinnedRender.sharedMesh;
            }
            else
            {
                MeshFilter meshFilter = renderers[i].GetComponent<MeshFilter>();
                if (null != meshFilter) mesh = meshFilter.sharedMesh;
            }

            if (null == mesh)
            {
                CheckObject checkObj = new CheckObject(go, string.Format("{0}  Mesh为Null，为什么?? 请通知主程!", go.name), WarningLevel.High);
                list.Add(checkObj);
                return;
            }

            if (meshSet.Contains(mesh))
            {
                CheckObject checkObj = new CheckObject(go, string.Format("{0}  为什么会在一个prefab里用相同的Mesh?? 请通知主程!", go.name), WarningLevel.High);
                list.Add(checkObj);
                return;
            }

            meshSet.Add(mesh);
        }
    }
}
#endif