﻿using UnityEngine;
using System.Collections;

namespace lss
{
	[ExecuteInEditMode]
	public class Billboard : MonoBehaviour
	{
		public enum Axis { up, billboard };
		public Axis axis = Axis.up;
		
		Vector3 tmp = Vector3.forward;
		public Camera cam;
        private bool isInRenderer;

        public Vector3 GetAxis(Axis refAxis, Camera camera)
		{
           if (null == camera)
            {
                return Vector3.one;
            }

			var f = camera.transform.forward;
			switch (refAxis)
			{
				case Axis.up:
					f.y = 0.0f;
					break;
			}

			return -f.normalized;
		}
		
		void OnWillRenderObject()
		{
            if (Camera.current != null)
            {
                isInRenderer = false;
                transform.forward = GetAxis(axis, Camera.current);
			}
		}			
		void Update()
		{
            if (!isInRenderer)
            {
                if (cam != null)
                {
                    transform.forward = GetAxis(axis, cam);
                }
                else
                {
                    transform.forward = GetAxis(axis, Camera.main);
                }
            }
		}
	}
}