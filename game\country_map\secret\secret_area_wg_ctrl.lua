require("game/country_map/secret/secret_area_view")
require("game/country_map/secret/secret_area_wg_data")
require("game/country_map/secret/secret_area_task_view")
require("game/country_map/secret/secret_area_finish_view")
-- 国家秘境
SecretAreaWGCtrl = SecretAreaWGCtrl or BaseClass(BaseWGCtrl)

function SecretAreaWGCtrl:__init()
	if SecretAreaWGCtrl.Instance ~= nil then
		ErrorLog("[SecretAreaWGCtrl] attempt to create singleton twice!")
		return
	end

	SecretAreaWGCtrl.Instance = self
	self.data = SecretAreaWGData.New()
	self.secret_area_task_view = SecretAreaTaskView.New()
	self.secret_area_medicine_view = SecretAreaMedicineView.New()
	self.secret_area_end_view = SecretAreaEndView.New()

	self:RegisterAllProtocols()
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function SecretAreaWGCtrl:__delete()
	SecretAreaWGCtrl.Instance = nil
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.secret_area_task_view then
		self.secret_area_task_view:DeleteMe()
		self.secret_area_task_view = nil
	end

	if self.secret_area_medicine_view then
		self.secret_area_medicine_view:DeleteMe()
		self.secret_area_medicine_view = nil
	end

	if self.secret_area_end_view then
		self.secret_area_end_view:DeleteMe()
		self.secret_area_end_view = nil
	end
	ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
	self.activity_change_callback = nil
end

-- 活动信息改变
function SecretAreaWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA then
		self:ChangeActSatus()
		self:FlushTaskViewTime()
	end
end

function SecretAreaWGCtrl:ChangeActSatus()
	local secret_area_info = self.data:GetSecretAreaInfo()
	local secret_other_cfg = self.data:GetOtherCfg()
	if IsEmptyTable(secret_area_info) or IsEmptyTable(secret_other_cfg) then
		return 0
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA)
	if activity_info == nil then
		return
	end
	local status = activity_info.status
	local remain_times = secret_other_cfg.limit_times - secret_area_info.enter_xtmj_times
	local diy_status = ACTIVITY_STATUS.CLOSE
	if status ~= ACTIVITY_STATUS.CLOSE then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if status == ACTIVITY_STATUS.STANDY then
			if remain_times > 0 or secret_area_info.enter_xtmj_last_time >= server_time then
				diy_status = ACTIVITY_STATUS.STANDY
			end
		elseif status == ACTIVITY_STATUS.OPEN then
			if remain_times > 0 or secret_area_info.enter_xtmj_last_time >= server_time then
				diy_status = ACTIVITY_STATUS.OPEN
			end
		end
	end
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SCERET_ACTIVITY_SHOW, diy_status, activity_info.next_time, activity_info.start_time, activity_info.end_time, activity_info.open_type)
	RemindManager.Instance:Fire(RemindName.CountryMapActSecret)
	ViewManager.Instance:FlushView(GuideModuleName.CountryMapActView, TabIndex.country_map_secret_area)
end

function SecretAreaWGCtrl:FlushTaskViewTime()
	if self.secret_area_task_view:IsOpen() then
		self.secret_area_task_view:FinalCompleteTimeCallBack()
	end
end

function SecretAreaWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossXingTuMiJingOpera)

	self:RegisterProtocol(SCCrossXingTuMiJingInfo,"OnSCCrossXingTuMiJingInfo")
	self:RegisterProtocol(SCCrossXingTuMiJingSyncWarInfo,"OnSCCrossXingTuMiJingSyncWarInfo")
	self:RegisterProtocol(SCXinTuMiJingFinishInfo,"OnSCXinTuMiJingFinishInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function SecretAreaWGCtrl:OnSCCrossXingTuMiJingInfo(protocol)
	--print_error("Info星图秘境返回信息",protocol)
	self.data:SetSecretAreaInfo(protocol)
	self:ChangeActSatus()
end

--option_type类型（0：请求信息、1：进入场景、2：推出场景）
function SecretAreaWGCtrl:SendCorssSecretAreaOperate(opera_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossXingTuMiJingOpera)
	protocol.opera_type = opera_type or 0
	protocol:EncodeAndSend()
end

function SecretAreaWGCtrl:OnSCCrossXingTuMiJingSyncWarInfo(protocol)
	--print_error("Task星图秘境返回信息",protocol)
	self.data:SetSecretAreaTaskInfo(protocol)
	if self.secret_area_task_view:IsOpen() then
		self.secret_area_task_view:Flush()
	end
end

function SecretAreaWGCtrl:OnSCXinTuMiJingFinishInfo(protocol)
	--print_error("Reward星图秘境返回信息",protocol)
	self.data:SetSecretAreaFinishInfo(protocol)
	if self.secret_area_end_view:IsOpen() then
		self.secret_area_end_view:Flush()
	else
		self.secret_area_end_view:Open()
	end
end

function SecretAreaWGCtrl:MainuiOpenCreate()
	self:SendCorssSecretAreaOperate(SECRET_AREA_TYPE.XingTuMiJing_GetInfo)
end

--跨天，请求一下秘境信息
function SecretAreaWGCtrl:OnPassDay()
	GlobalTimerQuest:AddDelayTimer(function()
		self:SendCorssSecretAreaOperate(SECRET_AREA_TYPE.XingTuMiJing_GetInfo)
	end, 5)
end

function SecretAreaWGCtrl:OpenSecretAreaTaskView()
	self.secret_area_task_view:Open()
end

function SecretAreaWGCtrl:CloseSecretAreaTaskView()
	self.secret_area_task_view:Close()
end

function SecretAreaWGCtrl:GetTaskView()
	return self.secret_area_task_view
end

function SecretAreaWGCtrl:OpenSecretAreaExpMdeicineView()
	if not self.secret_area_medicine_view:IsOpen() then
		self.secret_area_medicine_view:Open()
	end
end

function SecretAreaWGCtrl:CloseSecretAreaExpMdeicineView()
	self.secret_area_medicine_view:Close()
end