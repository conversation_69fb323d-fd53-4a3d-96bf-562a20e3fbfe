require("game/new_festival_activity/new_festival_tehui_shop/new_festival_tehui_shop_view")
require("game/new_festival_activity/new_festival_tehui_shop/new_festival_tehui_shop_wg_data")

NewFestivalTehuiShopWGCtrl = NewFestivalTehuiShopWGCtrl or BaseClass(BaseWGCtrl)
function NewFestivalTehuiShopWGCtrl:__init()
	if NewFestivalTehuiShopWGCtrl.Instance then
		ErrorLog("[NewFestivalTehuiShopWGCtrl] Attemp to create a singleton twice !")
	end
	NewFestivalTehuiShopWGCtrl.Instance = self

	self.data = NewFestivalTehuiShopWGData.New()
	self:RegisterAllProtocols()
end

function NewFestivalTehuiShopWGCtrl:__delete()
	NewFestivalTehuiShopWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end
end

function NewFestivalTehuiShopWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOATuhuiShopInfo, "OnSCOATuhuiShopInfo")
end

function NewFestivalTehuiShopWGCtrl:SendTehuiShopReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.NEW_FESTIVAL_ACT_TEHUI_SHOP
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function NewFestivalTehuiShopWGCtrl:OnSCOATuhuiShopInfo(protocol)
    self.data:SetTeHuiShopInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2334)
end
