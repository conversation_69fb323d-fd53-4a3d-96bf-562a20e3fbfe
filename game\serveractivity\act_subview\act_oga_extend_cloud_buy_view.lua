function KfActivityView:LoadOGACloudBuyCallBack()
    if not self.cloud_buy_reward_gird_list then
        self.cloud_buy_reward_gird_list = AsyncBaseGrid.New()
        local tb_grid_info = {}
        tb_grid_info.col = 4
        tb_grid_info.change_cells_num = 1
        tb_grid_info.itemRender = ItemCell
        tb_grid_info.list_view = self.node_list["cloud_buy_reward_gird_list"]
        self.cloud_buy_reward_gird_list:CreateCells(tb_grid_info)
        self.cloud_buy_reward_gird_list:SetStartZeroIndex(true)
    end
    
	if not self.cb_model then
		self.cb_model = OperationActRender.New(self.node_list.cb_model)
		self.cb_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    XUI.AddClickEventListener(self.node_list["cloud_buy_btn"],BindTool.Bind(self.OnClickOGACloudBuyJump, self))
end

function KfActivityView:CloseOGACloudBuyCallBack()
end

function KfActivityView:ReleaseOGACloudBuyCallBack()
    if CountDownManager.Instance:HasCountDown("OGA_CloudBuy_CD") then
		CountDownManager.Instance:RemoveCountDown("OGA_CloudBuy_CD")
	end

    if self.cloud_buy_reward_gird_list then
        self.cloud_buy_reward_gird_list:DeleteMe()
        self.cloud_buy_reward_gird_list = nil
    end

    if self.cb_model then
        self.cb_model:DeleteMe()
        self.cb_model = nil
    end
end

function KfActivityView:FlushOGACloudBuyCallBack()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    self.cloud_buy_cfg = ServerActivityWGData.Instance:GetOGACloudBuyCfg() 
    local reward_show = self.cloud_buy_cfg.reward_show or {}
    self.cloud_buy_reward_gird_list:SetDataList(reward_show)

    self:FlushOGACloudBuyModel()

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local all_rmb_buy_cfg = ServerActivityWGData.Instance:GetOgaRmbCfgByDay(open_day)
    local tb_normal, cd_time = {}, -1
    if not IsEmptyTable(all_rmb_buy_cfg) then
        for key, value in pairs(all_rmb_buy_cfg) do
            if value.rmb_type == OGA_EXTEND_RMB_TYPE_ENUM.NORMAL then
                table.insert(tb_normal, value)
            end
        end

        local close_day, had_buy_time = -1, 0
        if not IsEmptyTable(tb_normal) then
            table.sort(tb_normal, SortTools.KeyLowerSorter("rmb_seq"))
            for key, value in pairs(tb_normal) do
                if value.close_open_day > close_day then
                    close_day = value.close_open_day
                end
            end
        end
        local diff_sec = ((close_day - open_day >= 0) and (close_day - open_day) + 1 or 0 ) * 86400
        local start_stamp = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        start_stamp = start_stamp + diff_sec
        cd_time = start_stamp - server_time
    end

    self:FlushOGACloudBuyCDPart(cd_time)
    
end

function KfActivityView:FlushOGACloudBuyModel()
    if not self.cb_model then
        return
    end

    local show_cfg = self.cloud_buy_cfg
    if IsEmptyTable(show_cfg) then return end
    local model_show_itemid = show_cfg.model_show_itemid

    local display_data = {}
	display_data.should_ani = true
	if show_cfg.model_show_itemid ~= 0 and show_cfg.model_show_itemid ~= "" then
		local split_list = string.split(show_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_cfg.model_show_itemid
		end
	end
	
	display_data.bundle_name = show_cfg["model_bundle_name"]
    display_data.asset_name = show_cfg["model_asset_name"]
    local model_show_type = tonumber(show_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

    if show_cfg.model_pos and show_cfg.model_pos ~= "" then
		local pos_list = string.split(show_cfg.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if show_cfg.rotation and show_cfg.rotation ~= "" then
		local rot_list = string.split(show_cfg.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if show_cfg.model_scale and show_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = show_cfg.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L

    self.cb_model:SetData(display_data)
end

-- 活动倒计时cd
function KfActivityView:FlushOGACloudBuyCDPart(cd_time)
    local time = cd_time
    if CountDownManager.Instance:HasCountDown("OGA_CloudBuy_CD") then
		CountDownManager.Instance:RemoveCountDown("OGA_CloudBuy_CD")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("OGA_CloudBuy_CD",
			BindTool.Bind(self.UpdateOGACloudBuyCountDown, self),
			BindTool.Bind(self.OnOGACloudBuyCDComplete, self),
			nil, time, 1)
	else
		self:OnOGACloudBuyCDComplete()
	end
end

function KfActivityView:OnOGACloudBuyCDComplete()
    self.node_list["cloud_buy_remain_time"].text.text = ""
end

function KfActivityView:UpdateOGACloudBuyCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["cloud_buy_remain_time"].text.text = time_str
end

function KfActivityView:OnClickOGACloudBuyJump()
    if self.cloud_buy_cfg then
        local open_panel = self.cloud_buy_cfg.open_panel
        if open_panel and open_panel ~= '' then
            FunOpen.Instance:OpenViewNameByCfg(open_panel)
        end
    end
end