--排行榜
ExperienceFBRankView = ExperienceFBRankView or BaseClass(SafeBaseView)

function ExperienceFBRankView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(560, 500)})
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_experience_fb_rank")
end

function ExperienceFBRankView:OpenCallBack()

end

function ExperienceFBRankView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.rank

    if not self.layout_experience_rank_list then
	    self.layout_experience_rank_list = AsyncListView.New(ExperienceRankItem, self.node_list.layout_experience_rank_list)
    end
end

function ExperienceFBRankView:CloseCallBack()

end

function ExperienceFBRankView:ReleaseCallBack()
    if self.layout_experience_rank_list then
        self.layout_experience_rank_list:DeleteMe()
        self.layout_experience_rank_list = nil
    end

	self.show_level = nil
end

function ExperienceFBRankView:SetCurrlevel(curr_level)
	self.show_level = curr_level
end


function ExperienceFBRankView:OnFlush(param_t)
	self:FlushRankList()
end

function ExperienceFBRankView:FlushRankList()
	if not self.show_level then
		return
	end


	local rank_list = ExperienceFbWGData.Instance:GetRankListByLevel(self.show_level)
	if #rank_list <= 0 then
		return
	end

	self.layout_experience_rank_list:SetDataList(rank_list)
end

-------------------------------------PWSceneHurtItemCellRender---------------------------------
ExperienceRankItem = ExperienceRankItem or BaseClass(BaseRender)

function ExperienceRankItem:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

	local rank = self.data.rank == 0 and self.index or self.data.rank
    local bundle, asset
    local is_top_three = rank <= 3

    if is_top_three then
        bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. rank)
        self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank))
    else
        bundle, asset = ResPath.GetCommonImages("a3_ty_bg1_3")
    end

    self.node_list.img_bg.image:LoadSprite(bundle, asset)
    self.node_list.img_rank:CustomSetActive(is_top_three)
	self.node_list.rank_text.text.text = rank

	if self.data.name == nil or self.data.name == "" then
		self.node_list.rank_name.text.text = Language.RoleCharmRank.noinfoname
	else
		self.node_list.rank_name.text.text = self.data.name
	end

	local valid_time = self.data.pass_time --- self.data.start_time
	if valid_time > 0 then
		self.node_list.rank_pass.text.text = TimeUtil.FormatSecondDHM4(valid_time)
	else
		self.node_list.rank_pass.text.text = Language.Common.ZanWu
	end
	
end