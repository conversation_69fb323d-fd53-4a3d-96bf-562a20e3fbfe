﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class EffectOrderGroup
{
    private static int curOrder = 0;
    private static Dictionary<string, int> groupOrder = new Dictionary<string, int>();
    private static HashSet<string> invalidDic = new HashSet<string>();

    public static void OnGameStop()
    {
        curOrder = 0;
        groupOrder.Clear();
    }

    public static void RefreshRenderOrder(GameObject effectObj)
    {
        if (null == effectObj)
        {
            return;
        }

        // 避免不必要的重复的GetComponentInChildren;
        string name = ObjectNameMgr.Instance.GetObjectName(effectObj);
        if (invalidDic.Contains(name))
        {
            return;
        }

        int startOrder = 0;
        bool isNewGroup = false;
        if (!groupOrder.TryGetValue(name, out startOrder))
        {
            isNewGroup = true;
            startOrder = curOrder;
            groupOrder.Add(name, curOrder);
        }

        var renders = ListPool<Renderer>.Get();
        effectObj.GetComponentsInChildren<Renderer>(true, renders);
        if (renders.Count > 0)
        {
            for (int i = 0; i < renders.Count; i++)
            {
                Renderer render = renders[i];
                if (null != renders[i])
                {
                    render.sortingOrder = startOrder + render.sortingOrder;
                }
            }

            if (isNewGroup)
            {
                curOrder += 10;
                if (curOrder >= 30000)
                {
                    curOrder = -30000;
                }
            }
        }
        else
        {
            invalidDic.Add(name);
        }

        ListPool<Renderer>.Release(renders);
    }
}
