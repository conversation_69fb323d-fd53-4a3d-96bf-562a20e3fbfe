CrossMarryShowRankView = CrossMarryShowRankView or BaseClass(SafeBaseView)

function CrossMarryShowRankView:__init()
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop

    self:AddViewResource(0, "uis/view/marry_rank_ui_prefab", "layout_marry_show_rank")
end

function CrossMarryShowRankView:ReleaseCallBack()
    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end
end

function CrossMarryShowRankView:OpenCallBack()
    CrossMarryRankWGCtrl.Instance:SendMarryRechargedRank(CROSS_MARRY_RANK_OPERA.RANK_INFO)
end

function CrossMarryShowRankView:LoadCallBack()
    self.rank_list = AsyncListView.New(MarryShowRankRender, self.node_list["rank_list"])
end

function CrossMarryShowRankView:OnFlush()
    self:FlushView()  
end

function CrossMarryShowRankView:FlushView()
    --获取排行榜信息
    local rank_data = CrossMarryRankWGData.Instance:GetRankData()
    --拿到配置表信息
    local last_cfg = CrossMarryRankWGData.Instance:GetLastRankCfg()
    local sort_list = CrossMarryRankWGData.Instance:GetSortRankList()
    if IsEmptyTable(rank_data) or IsEmptyTable(last_cfg) or IsEmptyTable(sort_list) then
        return
    end

    --更新累计金额与上榜条件
    local money_str = RoleWGData.GetPayMoneyStr(last_cfg.reach_value, nil, nil,"#9DF5A7")
    self.node_list["condition_text"].text.text = string.format(Language.MarryRechargeRank.ConditionText, money_str)

    local self_recharge_value = rank_data.self_recharge_value
    money_str = RoleWGData.GetPayMoneyStr(self_recharge_value, nil, nil,"#9DF5A7")
    self.node_list["recharge_text"].text.text = string.format(Language.MarryRechargeRank.MyRechargeText, money_str)

    --更新还差多少名可以提升到第几名
    local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
    local tips_str = ""
    local rank = CrossMarryRankWGData.Instance:GetCanReachRank()
    if lover_id <= 0 and rank > 0 then--未结婚够钱上榜
        self.node_list["tips_image"]:CustomSetActive(true)
        tips_str = string.format(Language.MarryRechargeRank.RanktipsText2, rank)
    else
        local need_charge, next_rank = CrossMarryRankWGData.Instance:GetNeedRechargeToNextRank()
        self.node_list["tips_image"]:CustomSetActive(next_rank > 0)
        local money_str = RoleWGData.GetPayMoneyStr(need_charge, nil, nil,"#0f9c1c")

        if lover_id <= 0 then--未结婚不够钱上榜
            tips_str = string.format(Language.MarryRechargeRank.RanktipsText3, money_str, next_rank)
        elseif next_rank > 0 then--已结婚
            tips_str = string.format(Language.MarryRechargeRank.RanktipsText1, money_str, next_rank)
        end
    end

    self.node_list["tips_text"].text.text = tips_str

    --刷新排行榜列表
    self.rank_list:SetDataList(sort_list)
end

---排名列表格子
MarryShowRankRender = MarryShowRankRender or BaseClass(BaseRender)

function MarryShowRankRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rank = self.data.rank
    local is_top_3 = rank <= 3
    self.node_list.rank_image:CustomSetActive(is_top_3)
    if is_top_3 then
        self.node_list.rank_image.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank))
        self.node_list.bg:CustomSetActive(true)
        self.node_list.bg.image:LoadSprite(ResPath.GetMarryRankBgImg("a2_xlpk_shq_yeqian" .. rank))
        self.node_list.rank_text.text.text = ""
    else
        self.node_list.bg:CustomSetActive(rank % 2 == 0)
        if rank % 2 == 0 then
            self.node_list.bg.image:LoadSprite(ResPath.GetMarryRankBgImg("a2_xlpk_shq_yeqian4"))
        end
        self.node_list.rank_text.text.text = rank
    end

    self.node_list["recharge_text"].text.text = Language.MarryRechargeRank.BaoMi

    local is_rank = self.data.is_rank 
    if not is_rank then
        for i=1, 2 do
            local name_data = Split(self.data.couple_info[i].name, "_")
            self.node_list["name_text"..i].text.text = name_data[1]
        end
    end
    self.node_list["name_root"]:CustomSetActive(not is_rank)
    self.node_list["xuweiyidai"]:CustomSetActive(is_rank)
end