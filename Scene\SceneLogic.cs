﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using Nirvana;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using System.Xml.Linq;

/// <summary>
/// The root of this scene, for logic data.
/// </summary>
[RequireComponent(typeof(SceneGridView))]
[ExecuteInEditMode]
public sealed class SceneLogic : Mono<PERSON><PERSON><PERSON><PERSON>
{
    [SerializeField]
    [Tooltip("The ID of this scene.")]
    private int sceneID;

    [SerializeField]
    [Tooltip("The name of this scene.")]
    private string sceneName;

    [SerializeField]
    [Tooltip("The level limit to enter this level.")]
    private int levelLimit;

    [SerializeField]
    private bool isTax;

    [SerializeField]
    [Tooltip("Whether this scene is forbidden player kill player.")]
    private bool isForbidPK;

    [SerializeField]
    [Tooltip("The special logic type.")]
    [EnumLabel("Special Logic Type")]
    private SpecialLogicType specialLogicType;

    [SerializeField]
    [Tooltip("The scene timeout, used for instance.")]
    private int sceneTimeout;

    [SerializeField]
    [Tooltip("Whether kick out the players when timeout.")]
    private bool isTimeoutKick;

    [SerializeField]
    [Tooltip("The scene ID of town.")]
    private int townSceneID;

    [SerializeField]
    [Tooltip("The scene x of town(出生点x).")]
    private int townSceneX;

    [SerializeField]
    [Tooltip("The scene y of town(出生点y).")]
    private int townSceneY;

    [SerializeField]
    [Tooltip("The scene range of town(出生点随机范围).")]
    private int townRange;

    [SerializeField]
    [Tooltip("Whether to skip loading.")]
    private bool skipLoading = false;

    [SerializeField]
    [Tooltip("显示天气效果")]
    private bool showWeather = false;

    [SerializeField]
    [Tooltip("否全场景广播")]
    private bool sceneBroadcast = false;


    /// <summary>
    /// The scene specify type.
    /// </summary>
    public enum SpecialLogicType
    {
        [EnumLabel("0:普通场景")]
        Common = 0,

        [EnumLabel("1:军团驻地")]
        GuildStation = 1,

        [EnumLabel("3:寻宝BOSS")]
        TreasureBoss = 3,

        [EnumLabel("4:天神3v3准备场景")]
        TianShen3v3Prepare = 4,

        [EnumLabel("5:天神3v3")]
        TianShen3v3 = 5,

        [EnumLabel("6:天下第一准备场景")]
        WorldsNO1Prepare = 6,

        [EnumLabel("7:天下第一")]
        WorldsNO1 = 7,

        [EnumLabel("9:仙盟战")]
        XianMengzhan = 9,

        [EnumLabel("10:阵营驻地")]
        CampStation = 10,

        [EnumLabel("11:婚宴副本")]
        HunYanFb = 11,

        [EnumLabel("12:全民boss")]
        NationalBoss = 12,

        [EnumLabel("14:仙盟神兽")]
        GuildMonster = 14,

        [EnumLabel("15:1v1竞技场")]
        Field1v1 = 15,

        [EnumLabel("18:情缘副本")]
        QingYuanFB = 18,

        [EnumLabel("21:极限乱斗场景(原来的一战到底)称霸三界")]
        ChaosWar = 21,

        [EnumLabel("22:组队塔防")]
        Cross_Guildbattle = 22,

        [EnumLabel("23:仙盟副本")]
        GuildMiJingFB = 23,

        [EnumLabel("27:跨服水晶")]
        Fb_Wushuang = 27,

        [EnumLabel("28:跨服修罗塔")]
        Kf_Honorhalls = 28,

        [EnumLabel("29:跨服1v1")]
        Kf_OneVOne = 29,

        [EnumLabel("30:跨服3v3")]
        Kf_PVP = 30,

        [EnumLabel("32:仙盟Boss场景")]
        GuildBoss = 32,

        [EnumLabel("33:妖兽广场 场景")]
        YaoShouPlaza = 33,

        [EnumLabel("36:组队抓鬼")]
        ZhongKui = 36,

        [EnumLabel("37:无双副本")]
        CampGaojiDuobao = 37,

        [EnumLabel("40:天仙阁")]
        TianXianGe = 40,

        [EnumLabel("45:无尽祭坛")]
        HotSpring = 45,

        [EnumLabel("46:经验本")]
        WangLingExplore = 46,

        [EnumLabel("47:师徒爬塔")]
        CrossTeamFB = 47,

        [EnumLabel("48:限时打宝副本")]
        TerritoryWar = 48,

        [EnumLabel("49:高战副本")]
        HightWarFB = 49,

        [EnumLabel("50:跨服牧场")]
        FunOpenMountFB = 50,

        [EnumLabel("51:温泉")]
        FunOpenWingFB = 51,

        [EnumLabel("52:趣味农场")]
        FunOpenJingLingFB = 52,

        [EnumLabel("53:跨服Boss--跨服首领")]
        CrossCrystal = 53,

        [EnumLabel("54:多人爬塔副本")]
        GuideFb = 54,

        [EnumLabel("63:江湖闯关副本")]
        Yongshilianjing = 63,

        [EnumLabel("64:经验闯关副本")]
        ExpPassFB = 64,

        [EnumLabel("66:vip副本")]
        VipFB = 66,

        [EnumLabel("67:天命副本")]
        DestinyFB = 67,

        [EnumLabel("68:答题")]
        AnswerFB = 68,

        [EnumLabel("69:跑酷")]
        Parkour = 69,

        [EnumLabel("70:跨服1vN比赛场景")]
        GameScene1vN = 70,

        [EnumLabel("71:跨服组队")]
        FormTeam = 71,

        [EnumLabel("72:跨服梦幻舞会")]
        DreamDance = 72,

        [EnumLabel("73:飞仙BOSS")]
        FlyImmortalBoss = 73,

        [EnumLabel("74:跨服帮派战")]
        GuideWar = 74,

        [EnumLabel("75:世界Boss--世界首领")]
        WorldBoss = 75,

        [EnumLabel("76:洞窟Boss--打宝禁地")]
        CavernBoss = 76,

        [EnumLabel("77:VipBoss--首领之家")]
        VipBoss = 77,

        [EnumLabel("78:跨服温泉")]
        KFHotSpring = 78,

        [EnumLabel("79:跨服温泉赛鸭")]
        KFAnswer = 79,

        [EnumLabel("80:转职副本")]
        ChangeFactionFB = 80,

        [EnumLabel("81:跨服钓鱼")]
        KFFish = 81,

        [EnumLabel("82:帮派答题")]
        GuideAnswer = 82,

        [EnumLabel("83:铜币副本--龙王宝藏")]
        CopperCoinFB = 83,

        [EnumLabel("84:新宠物副本--仙灵圣殿")]
        NewCrewFB = 84,

        [EnumLabel("85:任务副本-Buff怪")]
        BuffMonsterFB = 85,

        [EnumLabel("86:任务副本-石柱怪")]
        PeriSteleFB = 86,

        [EnumLabel("87:任务副本-守护仙女")]
        ProtectPeri = 87,

        [EnumLabel("88:任务副本-自爆怪")]
        BoomMonsterFB = 88,

        [EnumLabel("89:任务副本-治疗怪")]
        CureMonsterFB = 89,

        [EnumLabel("90:组队装备副本")]
        TeamEquipFB = 90,

        [EnumLabel("91:心魔副本")]
        DayBreakFB = 91,

        [EnumLabel("92:新手副本")]
        GreenHandFB = 92,

        [EnumLabel("93:个人Boss副本--个人首领")]
        PresonageBossFB = 93,

        [EnumLabel("94:远古仙殿副本")]
        PalaceFB = 94,

        [EnumLabel("95:上古Boss--上古遗迹(旧)")]
        FabBackBoss = 95,

        [EnumLabel("96:战骑副本")]
        WarRideFB = 96,

        [EnumLabel("97:帮派Boss副本")]
        GuideBossFB = 97,

        [EnumLabel("98:无尽神狱")]
        Prison = 98,

        [EnumLabel("99:夜战王城")]
        NightFightCity = 99,

        [EnumLabel("100:乱斗战场")]
        MeleeBattleField = 100,

        [EnumLabel("101:天神降临")]
        WholegodTower = 101,

        [EnumLabel("102:魔王降临")]
        XLSD = 102,

        [EnumLabel("103:魔物降临（运营活动）")]
        ZhanChangZhiHun = 103,

        [EnumLabel("104:圣印副本--跨服陨落之地")]
        YinLuoZhiDi = 104,

        [EnumLabel("105:猎鲲地带")]
        LieKunDiDai = 105,

        [EnumLabel("106:秘境BOSS--本服秘境首领")]
        MiJingBoss = 106,

        [EnumLabel("107:秘境BOSS(跨服)")]
        KFMiJingBoss = 107,

        [EnumLabel("108:引导副本")]
        GuideBoss = 108,

        [EnumLabel("109:跨服1v1准备场景")]
        KfOneVOnePrepare = 109,

        [EnumLabel("110:跨服3v3准备场景")]
        KfPVPPrepare = 110,

        [EnumLabel("111:灵魂广场")]
        LianYuDongKu = 111,

        [EnumLabel("112:首领乱斗")]
        BossLuanDou = 112,

        [EnumLabel("113:圣天城")]
        ShengTianCheng = 113,

        [EnumLabel("114:远古仙殿引导副本")]
        YuGuXianDianYinDao = 114,

        [EnumLabel("115:蓬莱仙岛")]
        BootybayFB = 115,

        [EnumLabel("116:组队蓬莱仙岛")]
        TeamBootybayFB = 116,

        [EnumLabel("117:天神副本")]
        TianShenFb = 117,

        [EnumLabel("118:隐藏场景")]
        HideScene = 118,

        [EnumLabel("119:八卦迷阵副本")]
        BaGuaMiZhenFB  = 119,

        [EnumLabel("120:蛮荒古殿")]
        ManHuangGuDianFB =120,

        [EnumLabel("121:鸿蒙神域")]
        HongMengShenYuFB = 121,

        [EnumLabel("122:跨服诛邪战场")]
        KFZhuXieZhanChang = 122,

        [EnumLabel("123:天神伪副本")]
        FakeTianShenFb = 123,

        [EnumLabel("124:伪守护圣灵")]
        FakeNewCrewFB = 124,

        [EnumLabel("125:上古遗迹")]
        ShangGuYiJi = 125,

        [EnumLabel("126:深渊魔王")]
        ShenYuanMoWang = 126,

        [EnumLabel("127:封神榜副本")]
        FengShenBang = 127,
        
        [EnumLabel("128:孤岛激战副本")]
        GuDaoFB = 128,

        [EnumLabel("129:仙盟争霸定级赛")]
        GuildInvite = 129,

        [EnumLabel("130:永夜之巅")]
        ETERNAL_NIGHT = 130,

        [EnumLabel("131:国家版图挑战场景")]
        CountryTerritory = 131,

        [EnumLabel("132:跨服国战场景")]
        NationalWar = 132,

       [EnumLabel("133:任务链活动场景")]
        CrossTaskChain = 133,

        [EnumLabel("134:任务链活动场景 护送")]
        CrossTaskChainHuSong = 134,

        [EnumLabel("135:任务链活动场景 巡逻采集")]
        CrossTaskChainXunLuoCaiJi = 135,

        [EnumLabel("136:任务链活动场景 采集")]
        CrossTaskChainCaiJi = 136,

        [EnumLabel("137:任务链活动场景 16宫格躲避")]
        CrossTaskChainAvoid = 137,

        [EnumLabel("138:任务链活动场景 运送躲避")]
        CrossTaskChainYunSongAvoid = 138,

        [EnumLabel("139:任务链活动场景 守护")]
        CrossTaskChainGuard = 139,

        [EnumLabel("140:任务链活动场景 boss")]
        CrossTaskChainBoss = 140,

        [EnumLabel("141:永夜之巅决赛圈")]
        ETERNAL_NIGHT_FINAL = 141,

        [EnumLabel("142:国家运镖")]
        ESCORT = 142,

        [EnumLabel("143:皇城战")]
        ImperialCity = 143,

        [EnumLabel("144:山海奇闻客服")]
        CrossAnecdoteServerce = 144,
        [EnumLabel("145:山海奇闻美术")]
        CrossAnecdoteArt = 145,
        [EnumLabel("146:山海奇闻程序员")]
        CrossAnecdoteProgrammer = 146,
        [EnumLabel("147:山海奇闻老板")]
        CrossAnecdoteBoss = 147,
        [EnumLabel("148:山海奇闻策划")]
        CrossAnecdotePlanner = 148,
        [EnumLabel("149:仙界魔王")]
        CrossXianJieBoss = 149,
        [EnumLabel("150:塔防")]
        TowerDefend = 150,
        [EnumLabel("151:塔防副本")]
        DefenseFb = 151,
        [EnumLabel("152:国家星图秘境")]
        KF_COUNTRY_SECRET_AREA = 152,
        [EnumLabel("153:跨服龙脉")]
        CROSS_DRAGON_VEIN = 153,
        [EnumLabel("154:跨服养龙寺")]
        SCENE_TYPE_CROSS_YANGLONG = 154,
        [EnumLabel("155:个人boss")]
        SCENE_TYPE_PERSON_SPECIAL_BOSS = 155,
        [EnumLabel("156:捉鬼副本 - 公共")]
        GHOST_FB_GLOBAL = 156,
        [EnumLabel("157:捉鬼副本 - 个人")]
        GHOST_FB_PERSON = 157,
        [EnumLabel("158:夺旗战场")]
        CROSS_FLAG_GRABBING_BATTLEFIELD = 158,
        [EnumLabel("159:国家版图")]
        CROSS_COUNTRY = 159,
        [EnumLabel("160:跨服仙侣PK")]
        CROSS_PK_LOVER = 160,
        [EnumLabel("161:跨服仙侣PK准备")]
        CROSS_PK_LOVER_READY = 161,
        [EnumLabel("162:个人麻痹boss副本")]
        PERSON_MABIBOSS_DISPLAY = 162,
        [EnumLabel("163:跨服圣天神域")]
        CROSS_DIVINE_DOMAIN = 163,
        [EnumLabel("164:跨服圣天神域膜拜")]
        CROSS_DIVINE_DOMAIN_MOBAI = 164,
        [EnumLabel("165:本服刺探")]
        LOCAL_SPY_SCENE = 165,
        [EnumLabel("166:跨服1VN准备场景")]
        SCENE_TYPE_CROSS_1VN_STANDY = 166,
        [EnumLabel("167:跨服1VN战斗场景")]
        SCENE_TYPE_CROSS_1VN = 167,
        [EnumLabel("168:大跨服真充boss")]
        CROSS_EVERYDAY_RECHARGE_BOSS = 168,
        [EnumLabel("169:武魂塔副本场景")]
        WUHUN_TOWER_FB = 169,
        [EnumLabel("170:阵地战场景")]
        CROSS_LAND_WAR = 170,
        [EnumLabel("171:历练西行副本场景")]
        SCENE_TYPE_EXP_WEST_FB = 171,
        [EnumLabel("172:幻梦秘境副本")]
        PHANTOM_DREAMLAND_FB = 172,
        [EnumLabel("173:试炼副本")]
        WORLD_TREASURE_JL_FB = 173,
        [EnumLabel("174: 阵地战副本")]
        SCENE_TYPE_LAND_WAR_FB = 174,
        [EnumLabel("175: 跨服空战")]
        SCENE_TYPE_CROSS_AIR_WAR = 175,
        [EnumLabel("176: 天梯争霸")]
        ARENA_TIANTI = 176,
        [EnumLabel("177: BOSS入侵")]
        BOSS_INVASION = 177,
		[EnumLabel("178: 百倍爆装")]
		HUNDRED_EQUIP = 178,
        [EnumLabel("179: 龙神试炼")]
        SCENE_TYPE_DRAGON_TRIALT_FB = 179,
        [EnumLabel("180: 通用组队副本1(幻兽)")]
        TEAM_COMMON_BOSS_FB_1 = 180,
        [EnumLabel("181: 通用组队副本2(女神)")]
        TEAM_COMMON_BOSS_FB_2 = 181,
        [EnumLabel("182: 通用组队副本3(武魂)")]
        TEAM_COMMON_BOSS_FB_3 = 182,
		[EnumLabel("183: 组队符文塔")]
        TEAM_COMMON_TOWER_FB_1 = 183,
		[EnumLabel("184: 组队符文塔2")]
		TEAM_COMMON_TOWER_FB_2 = 184,
		[EnumLabel("185: 七夕场景")]
		QIXI_FESTIVAL_FB_1 = 185,
	}

    /// <summary>
    /// Gets the scene ID.
    /// </summary>
    public int SceneID
    {
        get { return this.sceneID; }
    }

    private string GetBundleName(string bundleName)
    {
        var strs = bundleName.Split('_');
        string n = bundleName;
        if (strs[strs.Length - 1].StartsWith("logic") ||
            strs[strs.Length - 1].StartsWith("Logic"))
            n = bundleName.Substring(0, bundleName.Length - (strs[strs.Length - 1].Length + 1));
        return n;
    }
    /// <summary>
    /// Save this map data into lua file.
    /// </summary>
    public bool SaveSceneLua(string path, bool compress)
    {
        var gridView = this.GetComponent<SceneGridView>();
        if (gridView == null)
        {
            Debug.LogError("Can not find the SceneGridView component.");
            return false;
        }

        // 获取当前场景的名字和assetbundle.
        var scene = EditorSceneManager.GetActiveScene();
        if (null == scene || scene.name == string.Empty || scene.name == "main")
        {
            EditorUtility.DisplayDialog("生成失败", "请在对应场景下点生成，不要在main下", "确定");
            return false;
        }

        var bundleName = AssetImporter.GetAtPath(scene.path).assetBundleName;
        if (string.IsNullOrEmpty(bundleName))
        {
            EditorUtility.DisplayDialog("生成失败", "AssetBundleName没有设置", "确定");
            return false;
        }
        var assetName = Path.GetFileNameWithoutExtension(scene.path);
        if (string.IsNullOrEmpty(assetName))
        {
            EditorUtility.DisplayDialog("生成失败", "invalid assetName", "确定");
            return false;
        }

        assetName = GetBundleName(assetName);
        // 组织Lua源文件.
        string format =
@"return {{
    id = {0},
    name = ""{1}"",
    scene_type = {2},
    bundle_name = ""{3}"",
    asset_name = ""{4}"",
    width = {5},
    height = {6},
    origin_x = {7},
    origin_y = {8},
    levellimit = {9},
    is_forbid_pk = {10},
    skip_loading = {11},
    show_weather = {12},
    scene_broadcast = {13},
    scenex = {14},
    sceney = {15},
    npcs = {{{16}
    }},
    monsters = {{{17}
    }},
    doors = {{{18}
    }},
    gathers = {{{19}
    }},
    jumppoints = {{{20}
    }},
    fences = {{{21}
    }},
    effects = {{{22}
    }},
    sounds = {{{23}
    }},
    scene_way_points = {{{24}
	}},
    mask = ""{25}"",
}}";

        var npcs = this.GetSceneNPCLua();
        var monsters = this.GetSceneMonsterLua();
        var doors = this.GetSceneDoorLua();
        var gathers = this.GetSceneGatherLua();
        var jumppoint = this.GetSceneJumppointLua();
        var fences = this.GetSceneFencesLua();
        var effects = this.GetSceneEffectLua();
        var sounds = this.GetSceneSoundLua();
        var wayPoints = this.GetSceneWayPointLua();
        var mask = this.GetMapEncodedMask(gridView, compress);
        var sourceText = string.Format(
            format,
            this.sceneID,
            this.sceneName,
            (int)this.specialLogicType,
            bundleName,
            assetName,
            gridView.Row,
            gridView.Column,
            this.transform.position.x,
            this.transform.position.z,
            this.levelLimit,
            this.isForbidPK ? "1" : "0",
            this.skipLoading ? "1" : "0",
            this.showWeather ? "1" : "0",
            this.sceneBroadcast ? "1" : "0",
            this.townSceneX,
            this.townSceneY,
            npcs,
            monsters,
            doors,
            gathers,
            jumppoint,
            fences,
            effects,
            sounds,
            wayPoints,
            mask);

        var fileStream = new FileStream(path, FileMode.Create);
        var writer = new StreamWriter(fileStream);
        writer.Write(sourceText);
        writer.Close();
        fileStream.Close();

        return true;
    }

    /// <summary>
    /// Save this map data into xml file.
    /// </summary>
    public void SaveMapXML(string path)
    {
        var gridView = this.GetComponent<SceneGridView>();
        if (gridView == null)
        {
            Debug.LogError("Can not find the SceneGridView component.");
            return;
        }

        var setting = new XmlWriterSettings();

        setting.Indent = true;
        setting.IndentChars = "\t";
        setting.NewLineChars = "\n";
        setting.NewLineHandling = NewLineHandling.Replace;

        using (var writer = XmlWriter.Create(path, setting))
        {
            writer.WriteStartDocument();
            writer.WriteStartElement("scene");

            writer.WriteElementString("id", this.sceneID.ToString());
            writer.WriteElementString("width", gridView.Row.ToString());
            writer.WriteElementString("height", gridView.Column.ToString());
            writer.WriteElementString("mask", this.GetMapMaskShrinked(gridView));

            writer.WriteEndElement();
            writer.WriteEndDocument();
        }
    }

    /// <summary>
    /// Save this scene data into xml file.
    /// </summary>
    public void SaveSceneXML(string path)
    {
        var setting = new XmlWriterSettings();

        setting.Indent = true;
        setting.IndentChars = "\t";
        setting.NewLineChars = "\n";
        setting.NewLineHandling = NewLineHandling.Replace;

        using (var writer = XmlWriter.Create(path, setting))
        {
            writer.WriteStartDocument();
            writer.WriteStartElement("scene");

            writer.WriteElementString("id", this.sceneID.ToString());
            writer.WriteElementString("name", this.sceneName);
            writer.WriteElementString("mapid", this.sceneID.ToString());

            writer.WriteElementString("levellimit", this.levelLimit.ToString());
            writer.WriteElementString("istax", this.isTax ? "1" : "0");
            writer.WriteElementString("is_forbid_pk", this.isForbidPK ? "1" : "0");
            writer.WriteElementString("speciallogic_type", ((int)this.specialLogicType).ToString());
            writer.WriteElementString("scene_timeout", this.sceneTimeout.ToString());
            writer.WriteElementString("is_timeout_kick", this.isTimeoutKick ? "1" : "0");
            writer.WriteElementString("is_scene_broadcast", this.sceneBroadcast ? "1" : "0");

            writer.WriteStartElement("townpoint");
            writer.WriteElementString("sceneid", this.townSceneID.ToString());
            writer.WriteElementString("scenex", this.townSceneX.ToString());
            writer.WriteElementString("sceney", this.townSceneY.ToString());
                 writer.WriteElementString("range", this.townRange.ToString());
            writer.WriteEndElement();

            writer.WriteStartElement("triggerareas");
            writer.WriteEndElement();

            writer.WriteStartElement("triggers");
            writer.WriteEndElement();

            this.SaveNPCs(writer);
            this.SaveMonsterPoints(writer);
            this.SaveDoorsPoints(writer);
            this.SaveGatherPointsPoints(writer);

            writer.WriteEndElement();
            writer.WriteEndDocument();
        }
    }

    /// <summary>
    /// Save this scene manager into xml file.
    /// </summary>
    public void SaveSceneManagerXML(string path)
    {
        var setting = new XmlWriterSettings();

        setting.Indent = true;
        setting.IndentChars = "\t";
        setting.NewLineChars = "\n";
        setting.NewLineHandling = NewLineHandling.Replace;

        var dir = Path.GetDirectoryName(path);
        var mapDir = Path.Combine(dir, "map");
        var sceneDir = Path.Combine(dir, "scene");
        var mapFiles = Directory.GetFiles(
            mapDir, "*.xml", SearchOption.TopDirectoryOnly);
        var sceneFiles = Directory.GetFiles(
            sceneDir, "*.xml", SearchOption.TopDirectoryOnly);

        var mapNameTable = new HashSet<string>();
        foreach (var file in mapFiles)
        {
            mapNameTable.Add(Path.GetFileName(file));
        }

        var sceneNameTable = new HashSet<string>();
        foreach (var file in sceneFiles)
        {
            sceneNameTable.Add(Path.GetFileName(file));
        }

        using (var writer = XmlWriter.Create(path, setting))
        {
            writer.WriteStartDocument();
            writer.WriteStartElement("scenemanager");

            writer.WriteStartElement("maps");
            foreach (var mapFile in mapFiles)
            {
                var fileName = Path.GetFileName(mapFile);
                if (!sceneNameTable.Contains(fileName))
                {
                    Debug.LogWarningFormat(
                        "The map file: {0} is not existed in scene files.",
                        fileName);
                    continue;
                }

                var uri1 = new Uri(mapFile);
                var uri2 = new Uri(dir + "/");
                var relativePath = uri2.MakeRelativeUri(uri1).OriginalString;

                writer.WriteStartElement("map");
                writer.WriteElementString("path", relativePath);
                writer.WriteEndElement();
            }

            writer.WriteEndElement();

            writer.WriteStartElement("scenes");
            foreach (var sceneFile in sceneFiles)
            {
                var fileName = Path.GetFileName(sceneFile);
                if (!mapNameTable.Contains(fileName))
                {
                    Debug.LogWarningFormat(
                        "The scene file: {0} is not existed in map files.",
                        fileName);
                    continue;
                }

                var sceneDoc = new XmlDocument();
                sceneDoc.Load(sceneFile);

                var sceneNode = sceneDoc.SelectSingleNode("scene");

                var nameNode = sceneNode.SelectSingleNode("name");
                var sceneName = nameNode.InnerText;

                var typeNode = sceneNode.SelectSingleNode("speciallogic_type");
                var sceneType = typeNode.InnerText;

                var uri1 = new Uri(sceneFile);
                var uri2 = new Uri(dir + "/");
                var relativePath = uri2.MakeRelativeUri(uri1).OriginalString;

                writer.WriteStartElement("scene");
                writer.WriteElementString("name", sceneName);
                writer.WriteElementString("path", relativePath);
                writer.WriteElementString("scene_type", sceneType);
                writer.WriteElementString("game_index", "0");
                writer.WriteEndElement();
            }

            writer.WriteEndElement();

            writer.WriteEndElement();
            writer.WriteEndDocument();
        }
    }

    public void SaveConfigMapLua(string path, string dirpath)
    {
        var gridView = this.GetComponent<SceneGridView>();
        if (gridView == null)
        {
            Debug.LogError("Can not find the SceneGridView component.");
            return;
        }

        var dir = Path.GetDirectoryName(dirpath);
        var sceneDir = Path.Combine(dir, "scene");
        var sceneFiles = Directory.GetFiles(
            sceneDir, "*.xml", SearchOption.TopDirectoryOnly);

        var sceneNameTable = new HashSet<string>();
        foreach (var file in sceneFiles)
        {
            sceneNameTable.Add(Path.GetFileName(file));
        }

        var sceneListBuilder = new StringBuilder();
        int sceneCount = 0;
        foreach (var sceneFile in sceneFiles)
        {
            var sceneDoc = new XmlDocument();
            sceneDoc.Load(sceneFile);

            var sceneNode = sceneDoc.SelectSingleNode("scene");

            var idNode = sceneNode.SelectSingleNode("id");
            var sceneId = idNode.InnerText;

            var nameNode = sceneNode.SelectSingleNode("name");
            var sceneName = nameNode.InnerText;

            var typeNode = sceneNode.SelectSingleNode("speciallogic_type");
            var sceneType = typeNode.InnerText;

            var listLua = string.Format(
               "\n\t[{0}] = {{id = {1}, resid = {2}, sceneType = {3}, res_x = {4}, res_y = {5}, name = \"{6}\"}},",
               sceneId,
               sceneId,
               sceneId,
               sceneType,
               0,
               0,
               sceneName);
            sceneListBuilder.Append(listLua);
            ++ sceneCount;
        }

        if (sceneCount <= 300)
        {
            EditorUtility.DisplayDialog("生成失败", "场景列表极有可能错误。联系技术负责人！！！！", "确定");
            Debug.LogErrorFormat("[SaveConfigMapLua] error!!! {0}, {1}, {2}", sceneFiles.Length, sceneCount, sceneListBuilder.ToString());

            return;
        }

        var sceneListString = sceneListBuilder.ToString();

        var sourceText = string.Format("Config_scenelist = {{{0}}}", sceneListString);

        var fileStream = new FileStream(path, FileMode.Create);
        var writer = new StreamWriter(fileStream);
        writer.Write(sourceText);
        writer.Close();
        fileStream.Close();
    }

    private string GetSceneNPCLua()
    {
        var builder = new StringBuilder();
        var npcs = this.GetComponentsInChildren<SceneNPC>();
        foreach (var npc in npcs)
        {
            if (!npc.enabled) continue;
            int x;
            int y;
            this.TransformWorldToLogic(npc.transform.position, out x, out y);
            float rotationY = npc.transform.eulerAngles.y;
            string paths = "";
            if (npc.IsWalking)
            {
                foreach (var path in npc.Paths)
                {
                    paths += string.Format("{{x={0}, y={1}}},", path.x, path.y);
                }
                paths = string.Format("{{{0}}}", paths);
            }
            else
            {
                paths = "{}";
            }
            var npcLua = string.Format("\n\t\t{{id={0}, x={1}, y={2}, rotation_y = {3}, is_walking = {4}, paths = {5}}},", npc.ID, x, y, rotationY, npc.IsWalking ? 1 : 0, paths);
            builder.Append(npcLua);
        }

        return builder.ToString();
    }

    private string GetSceneMonsterLua()
    {
        var builder = new StringBuilder();
        var monsters = this.GetComponentsInChildren<SceneMonsterPoint>();
        foreach (var monster in monsters)
        {
            if (!monster.enabled) continue;
            int x;
            int y;
            this.TransformWorldToLogic(monster.transform.position, out x, out y);

            var monsterLua = string.Format("\n\t\t{{id={0}, x={1}, y={2}}},", monster.ID, x, y);
            builder.Append(monsterLua);
        }

        return builder.ToString();
    }

    private string GetSceneDoorLua()
    {
        var builder = new StringBuilder();
        var doors = this.GetComponentsInChildren<SceneDoor>();
        foreach (var door in doors)
        {
            if (!door.enabled) continue;
            int x;
            int y;
			this.TransformWorldToLogic(door.transform.position, out x, out y);

            var doorLua = string.Format(
				"\n\t\t{{id={0}, type={1}, level={2}, target_scene_id={3}, target_door_id={4}, offset={{{5}, {6}, {7}}}, rotation={{{8}, {9}, {10}}}, x={11}, y={12}, door_target_x={13}, door_target_y={14}}},",
                door.ID,
                door.DoorType,
                door.LimitLevel,
                door.TargetSceneID,
                door.TargetDoorID,
                door.Offset.x,
                door.Offset.y,
                door.Offset.z,
                door.Rotation.x,
                door.Rotation.y,
                door.Rotation.z,
                x,
                y,
				door.TargetX,
				door.TargetY);
            builder.Append(doorLua);
        }

        return builder.ToString();
    }

    private string GetSceneGatherLua()
    {
        var builder = new StringBuilder();
        var gathers = this.GetComponentsInChildren<SceneGatherPoint>();
        foreach (var gather in gathers)
        {
            if (!gather.enabled) continue;
            int x;
            int y;
            this.TransformWorldToLogic(gather.transform.position, out x, out y);
            int disappear_after_gather = gather.DisappearAfterGather;

            var doorLua = string.Format(
                "\n\t\t{{id={0}, x={1}, y={2}, disappear_after_gather={3}}},",
                gather.ID,
                x,
                y,
                disappear_after_gather);
            builder.Append(doorLua);
        }

        return builder.ToString();
    }

    private string GetSceneJumppointLua()
    {
        var builder = new StringBuilder();
        var elements = this.GetComponentsInChildren<SceneJumpPoint>();
        foreach (var element in elements)
        {
            if (!element.enabled) continue;
            int x;
            int y;
            this.TransformWorldToLogic(element.transform.position, out x, out y);

            string cgs = "";
            if (element.PlayCG)
            {
                foreach (var cg in element.CGs)
                {
                    cgs += string.Format("{{prof={0},sex={1},bundle_name=\"{2}\",asset_name=\"{3}\",position={{x={4},y={5}}},rotation={6}}},"
                        , cg.prof, cg.sex, cg.cgController.BundleName, cg.cgController.AssetName, cg.position.x, cg.position.y, cg.rotationY);
                }
                cgs = string.Format("{{{0}}}", cgs);
            }
            else
            {
                cgs = "{}";
            }

            var content = string.Format(
                "\n\t\t{{id={0}, target_id={1}, range={2}, x={3}, y={4}, jump_type={5}, air_craft_id={6}, is_show={7}, jump_speed={8}, jump_act={9},jump_tong_bu={10},jump_time={11},camera_fov={12},camera_rotation={13},offset={{{14},{15},{16}}},play_cg={17},cgs={18}}},",
                element.ID,
                element.TargetID,
                element.Range,
                x,
                y,
                element.JumpType,
                element.AirCraftId,
                element.IsShow,
                element.JumpSpeed,
                element.JumpAct,
                element.TongBu,
                element.JumpTime,
                element.JumpCameraFOV,
                element.JumpCameraRotation,
                element.Offset.x, element.Offset.y, element.Offset.z,
                element.PlayCG ? 1 : 0,
                cgs);

            builder.Append(content);
        }

        return builder.ToString();
    }

    private string GetSceneFencesLua()
    {
        var builder = new StringBuilder();
        var fences = this.GetComponentsInChildren<SceneFence>();
        foreach (var fence in fences)
        {
            if (!fence.enabled) continue;
            int x;
            int y;
            this.TransformWorldToLogic(fence.transform.position, out x, out y);

            var fenceLua = string.Format(
                "\n\t\t{{id={0}, offset={{{1}, {2}, {3}}}, rotation={{{4}, {5}, {6}}}, x={7}, y={8}}},",
                fence.ID,
                fence.Offset.x,
                fence.Offset.y,
                fence.Offset.z,
                fence.Rotation.x,
                fence.Rotation.y,
                fence.Rotation.z,
                x,
                y);
            builder.Append(fenceLua);
        }

        return builder.ToString();
    }

    private string FindAudioFileInAssets(string baseName)
    {
        List<string> audioExtensions = new List<string> { ".mp3", ".wav", ".ogg" };
        string[] assetPaths = AssetDatabase.FindAssets($"t:{typeof(AudioClip).Name} {baseName}");
        if (assetPaths.Length > 0)
        {
            string guid = assetPaths[0];
            string path = AssetDatabase.GUIDToAssetPath(guid);
            var extension = Path.GetExtension(path);

            if(audioExtensions.Contains(extension))
            {
                return extension;
            }
        }
        return "";
    }

    private string GetSceneSoundLua()
    {
        var builder = new StringBuilder();
        var sounds = this.GetComponentsInChildren<AudioSource>();
        foreach (var sound in sounds)
        {
            if (!sound.enabled) continue;
            int x;
            int y;
            this.TransformWorldToLogic(sound.transform.position, out x, out y);
            var extension = FindAudioFileInAssets(sound.clip.name);
            if (extension == "") break;
            var soundLua = string.Format(
                "\n\t\t{{clip=\"{0}\", group=\"{1}\", mute={2}, bypassE={3}, bypassLE={4}, bypassRZ={5}, playOnAwake={6}, loop={7}, priority={8}, volume={9}, pitch={10}, panStereo={11}, spatialBlend={12}, reverbZoneMix={13}, dopplerLevel={14}, spread={15}, rolloffMode={16}, minDistance={17}, maxDistance={18}, x={19}, y={20}, extension=\"{21}\"}},",
                sound.clip.name,
                sound.outputAudioMixerGroup.name,
                sound.mute ? 1 : 0,
                sound.bypassEffects ? 1 : 0,
                sound.bypassListenerEffects ? 1: 0,
                sound.bypassReverbZones ? 1 : 0,
                sound.playOnAwake ? 1 : 0,
                sound.loop ? 1 : 0,
                sound.priority,
                sound.volume,
                sound.pitch,
                sound.panStereo,
                sound.spatialBlend,
                sound.reverbZoneMix,
                sound.dopplerLevel,
                sound.spread,
                (int)sound.rolloffMode,
                sound.minDistance,
                sound.maxDistance,
                x,
                y,
                extension);
            builder.Append(soundLua);
        }

        return builder.ToString();
        return "";
    }

    private string GetSceneEffectLua()
    {
        var builder = new StringBuilder();
        var effects = this.GetComponentsInChildren<SceneEffect>();
        foreach (var effect in effects)
        {
            if (!effect.enabled) continue;
            int x;
            int y;
            this.TransformWorldToLogic(effect.transform.position, out x, out y);

            var effectLua = string.Format(
                "\n\t\t{{bundle=\"{0}\", asset=\"{1}\", offset={{{2}, {3}, {4}}}, rotation={{{5}, {6}, {7}}}, scale={{{8}, {9}, {10}}}, x={11}, y={12}}},",
                effect.AssetID.BundleName,
                effect.AssetID.AssetName,
                effect.Offset.x,
                effect.Offset.y,
                effect.Offset.z,
                effect.Rotation.x,
                effect.Rotation.y,
                effect.Rotation.z,
                effect.Scale.x,
                effect.Scale.y,
                effect.Scale.z,
                x,
                y);
            builder.Append(effectLua);
        }

        return builder.ToString();
    }

    private void LinkSceneWayPointID(Dictionary<int, HashSet<int>> dic, int id, int targetID)
    {
        if (id == targetID)
            return;

        HashSet<int> hashSet;
        if (!dic.TryGetValue(id, out hashSet))
        {
            hashSet = new HashSet<int>();
            dic.Add(id, hashSet);
        }

        if (!hashSet.Contains(targetID))
            hashSet.Add(targetID);
    }

    private string GetSceneWayPointLua()
    {
        var builder = new StringBuilder();
        var elements = this.GetComponentsInChildren<SceneWayPoint>();

        Dictionary<int, HashSet<int>> dic = new Dictionary<int, HashSet<int>>();
        foreach (var element in elements)
        {
            int id = element.ID;

            if (null == element.TargetPoints)
            {
                continue;
            }

            for (int i = 0; i < element.TargetPoints.Length; ++i)
            {
                var point = element.TargetPoints[i];
                if (null == point.sceneWayPoint)
                    continue;

                LinkSceneWayPointID(dic, id, point.sceneWayPoint.ID);
                if (point.doubleDirection)
                {
                    LinkSceneWayPointID(dic, point.sceneWayPoint.ID, id);
                }
            }
        }

        foreach (var element in elements)
        {
            int id = element.ID;
            string targets = "";
            if (!dic.ContainsKey(id))
                continue;

            foreach (var targetID in dic[id])
            {
                targets += string.Format("{0}, ", targetID);
            }
            targets = string.Format("{{{0}}}", targets);

            int x;
            int y;
            this.TransformWorldToLogic(element.transform.position, out x, out y);

            var content = string.Format(
                "\n\t\t[{0}] = {{id={0}, target_id={1},x={2}, y={3}}},",
                id,
                targets,
                x,
                y);

            builder.Append(content);
        }

        return builder.ToString();
    }


    private string GetMapEncodedMask(SceneGridView gridView, bool compress)
    {
        if (compress)
        {
            var dataStream = new MemoryStream(gridView.Column * gridView.Row);
            for (int j = 0; j < gridView.Column; ++j)
            {
                for (int i = 0; i < gridView.Row; ++i)
                {
                    var cell = gridView.GetCell(i, j) as SceneCell;
                    dataStream.WriteByte((byte)cell.Ground);
                }
            }

            var compressStream = new MemoryStream();
            var encoder = new SevenZip.Compression.LZMA.Encoder();
            encoder.SetCoderProperties(
                new SevenZip.CoderPropID[] { SevenZip.CoderPropID.EndMarker },
                new object[] { true });
            dataStream.Position = 0;
            encoder.WriteCoderProperties(compressStream);
            encoder.Code(dataStream, compressStream, -1, -1, null);
            compressStream.Flush();

            var bytes = new byte[compressStream.Length];
            compressStream.Seek(0, SeekOrigin.Begin);
            compressStream.Read(bytes, 0, bytes.Length);
            var mask = Convert.ToBase64String(bytes);
            return mask;
        }
        else
        {
            var maskBuilder = new StringBuilder();
            for (int j = 0; j < gridView.Column; ++j)
            {
                for (int i = 0; i < gridView.Row; ++i)
                {
                    var cell = gridView.GetCell(i, j) as SceneCell;
                    maskBuilder.Append((int)cell.Ground);
                }
            }

            return maskBuilder.ToString();
        }
    }

    private string GetMapMaskShrinked(SceneGridView gridView)
    {
        // 先拷贝一份int数组的Mask出来.
        var cells = new int[gridView.Column * gridView.Row];
        for (int j = 0; j < gridView.Column; ++j)
        {
            for (int i = 0; i < gridView.Row; ++i)
            {
                var cell = gridView.GetCell(i, j) as SceneCell;
                cells[(i * gridView.Column) + j] = (int)cell.Ground;
            }
        }

        if (gridView.Column > 2 && gridView.Row > 2)
        {
            // 逐列缩小
            for (int j = 1; j < gridView.Column - 1; ++j)
            {
                for (int i = 1; i < gridView.Row - 1; ++i)
                {
                    var index = (i * gridView.Column) + j;
                    var cell = cells[index];
                    if (cell == (int)SceneCell.GroundType.Block || cell == (int)SceneCell.GroundType.ObstacleWay || cell == (int)SceneCell.GroundType.ClientBlock)
                    {
                        var cellNext = ((SceneCell)gridView.GetCell(i, j + 1)).Ground;
                        var cellPrev = ((SceneCell)gridView.GetCell(i, j - 1)).Ground;
                        if (cellNext != SceneCell.GroundType.Block && cellNext != SceneCell.GroundType.ObstacleWay && cellNext != SceneCell.GroundType.ClientBlock)
                        {
                            cells[index] = (int)cellNext;
                        }
                        else if (cellPrev != SceneCell.GroundType.Block && cellPrev != SceneCell.GroundType.ObstacleWay && cellPrev != SceneCell.GroundType.ClientBlock)
                        {
                            cells[index] = (int)cellPrev;
                        }
                    }
                }
            }

            // 逐行缩小
            for (int i = 1; i < gridView.Row - 1; ++i)
            {
                for (int j = 1; j < gridView.Column - 1; ++j)
                {
                    var index = (i * gridView.Column) + j;
                    var cell = cells[index];
                    if (cell == (int)SceneCell.GroundType.Block || cell == (int)SceneCell.GroundType.ObstacleWay || cell == (int)SceneCell.GroundType.ClientBlock)
                    {
                        var cellNext = ((SceneCell)gridView.GetCell(i + 1, j)).Ground;
                        var cellPrev = ((SceneCell)gridView.GetCell(i - 1, j)).Ground;
                        if (cellNext != SceneCell.GroundType.Block && cellNext != SceneCell.GroundType.ObstacleWay && cellNext != SceneCell.GroundType.ClientBlock)
                        {
                            cells[index] = (int)cellNext;
                        }
                        else if (cellPrev != SceneCell.GroundType.Block && cellPrev != SceneCell.GroundType.ObstacleWay && cellPrev != SceneCell.GroundType.ClientBlock)
                        {
                            cells[index] = (int)cellPrev;
                        }
                    }
                }
            }
        }

        // 写入缩小后的Mask.
        var maskBuilder = new StringBuilder();
        for (int j = 0; j < gridView.Column; ++j)
        {
            for (int i = 0; i < gridView.Row; ++i)
            {
                int mask = cells[(i * gridView.Column) + j];
                maskBuilder.Append(mask);
            }
        }

        return maskBuilder.ToString();
    }

    private void SaveNPCs(XmlWriter writer)
    {
        writer.WriteStartElement("npcs");

        var npcs = this.GetComponentsInChildren<SceneNPC>();
        foreach (var npc in npcs)
        {
            if (!npc.enabled) continue;
            writer.WriteStartElement("npc");
            writer.WriteElementString("id", npc.ID.ToString());
            this.WritePosition(writer, npc.transform.position);
            writer.WriteEndElement();
        }

        writer.WriteEndElement();
    }

    private void SaveMonsterPoints(XmlWriter writer)
    {
        writer.WriteStartElement("monsterpoints");

        var monsterPoints = this.GetComponentsInChildren<SceneMonsterPoint>();
        foreach (var monsterPoint in monsterPoints)
        {
            if (!monsterPoint.enabled) continue;
            writer.WriteStartElement("point");
            writer.WriteElementString("monsterid", monsterPoint.ID.ToString());
            writer.WriteElementString("interval", monsterPoint.Interval.ToString());
            writer.WriteElementString("num", monsterPoint.Num.ToString());
            writer.WriteElementString("histroytotalnum", monsterPoint.HistroyTotalNum.ToString());
            this.WritePosition(writer, monsterPoint.transform.position);
            writer.WriteEndElement();
        }

        writer.WriteEndElement();
    }

    private void SaveDoorsPoints(XmlWriter writer)
    {
        writer.WriteStartElement("doors");

        var doors = this.GetComponentsInChildren<SceneDoor>();
        foreach (var door in doors)
        {
            if (!door.enabled) continue;
            writer.WriteStartElement("door");
            writer.WriteElementString("id", door.ID.ToString());
            writer.WriteElementString("type", door.DoorType.ToString());
            writer.WriteElementString("level", door.LimitLevel.ToString());
            writer.WriteElementString("target_scene_id", door.TargetSceneID.ToString());
            writer.WriteElementString("target_door_id", door.TargetDoorID.ToString());
            this.WritePosition(writer, door.transform.position);
            writer.WriteEndElement();
        }

        writer.WriteEndElement();
    }

    public SceneDoor[] GetSceneDoors()
    {
        var doors = this.GetComponentsInChildren<SceneDoor>();
        return doors;
    }

    public void SaveSingleDoorXMLForSever(string path, SceneDoor door)
    {
        //        <door>
        //  <id>5</id>
        //  <level>45</level>
        //  <type>0</type>
        //  <targetscene>
        //    <sceneid>104</sceneid>
        //    <x>237</x>
        //    <y>51</y>
        //  </targetscene>
        //</door>
        var setting = new XmlWriterSettings();
        setting.Indent = true;
        setting.IndentChars = "\t";
        setting.NewLineChars = "\n";
        setting.NewLineHandling = NewLineHandling.Replace;

        using (var writer = XmlWriter.Create(path, setting))
        {
            writer.WriteStartElement("door");

            writer.WriteElementString("id", door.ID.ToString());
            writer.WriteElementString("level", door.LimitLevel.ToString());
            writer.WriteElementString("type", door.DoorType.ToString());
            writer.WriteStartElement("targetscene");
            writer.WriteElementString("sceneid", door.TargetSceneID.ToString());
            if (door.NeedTargetPos)
            {
                writer.WriteElementString("x", door.TargetX.ToString());
                writer.WriteElementString("y", door.TargetY.ToString());
            }
            else
            {
                var otherDoorPos = GetOtherSceneDoorPos(door.TargetSceneID, door.TargetDoorID);
                writer.WriteElementString("x", otherDoorPos.x.ToString());
                writer.WriteElementString("y", otherDoorPos.y.ToString());
            }
            writer.WriteEndElement();
            writer.WriteEndElement();
        }
    }

    private Vector2 GetOtherSceneDoorPos(int sceneid, int doorid)
    {
        var defaultPath = Application.dataPath + "/../Config/scene/" + sceneid + ".xml";
        defaultPath = Path.GetFullPath(defaultPath);
        Vector2 result = Vector2.zero;
        XDocument document = XDocument.Load(defaultPath);
        if (document != null)
        {
            //获取到XML的根元素进行操作
            XElement root = document.Root;
            XElement ele = root.Element("doors");
            var enumerable = ele.Elements("door");
            int point = 0;
            foreach (XElement item in enumerable)
            {
                var idEle = item.Element("id");
                if (idEle != null && int.Parse(idEle.Value) == doorid)
                {
                    point = int.Parse(item.Element("x").Value);
                    result.x = point;
                    point = int.Parse(item.Element("y").Value);
                    result.y = point;
                }
            }
        }
        else
        {
            EditorUtility.DisplayDialog(sceneid + ".xml not found", "检查是否存在场景XML","确认");
        }
        return result;
    }

    private void SaveGatherPointsPoints(XmlWriter writer)
    {
        writer.WriteStartElement("gatherpoints");

        var gatherPoints = this.GetComponentsInChildren<SceneGatherPoint>();
        foreach (var gatherPoint in gatherPoints)
        {
            if (!gatherPoint.enabled) continue;
            writer.WriteStartElement("gather");
            writer.WriteElementString("id", gatherPoint.ID.ToString());
            writer.WriteElementString("index", gatherPoint.Index.ToString());
            writer.WriteElementString("create_interval", gatherPoint.Interval.ToString());
            writer.WriteElementString("gather_time", gatherPoint.GatherTime.ToString());
            writer.WriteElementString("evil_add", gatherPoint.EvilAdd.ToString());
            writer.WriteElementString("disappear_after_gather", gatherPoint.DisappearAfterGather.ToString());
            this.WritePosition(writer, gatherPoint.transform.position);
            writer.WriteEndElement();
        }

        writer.WriteEndElement();
    }

    private void WritePosition(XmlWriter writer, Vector3 position)
    {
        int x;
        int y;
        this.TransformWorldToLogic(position, out x, out y);
        writer.WriteElementString("x", x.ToString());
        writer.WriteElementString("y", y.ToString());
    }

    private void TransformWorldToLogic(Vector3 position, out int x, out int y)
    {
        var pos = this.transform.position;

        x = (int)Mathf.Floor((position.x - pos.x));
        y = (int)Mathf.Floor((position.z - pos.z));
    }

    private void Update()
    {
        // Snap to grid.
        const float GridSize = 1f;
        const float GridSizeInverse = 1.0f / GridSize;

        var x = Mathf.Floor(transform.position.x * GridSizeInverse) / GridSizeInverse;
        var y = transform.position.y;
        var z = Mathf.Floor(transform.position.z * GridSizeInverse) / GridSizeInverse;

        this.transform.position = new Vector3(x, y, z);
    }
}

#endif
