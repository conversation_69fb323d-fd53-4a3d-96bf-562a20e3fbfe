-- K-跨服BOSS来袭.xls
local item_table={
[1]={item_id=22090,num=1,is_bind=1},
[2]={item_id=26129,num=17,is_bind=1},
[3]={item_id=26368,num=2,is_bind=1},
[4]={item_id=30423,num=4,is_bind=1},
[5]={item_id=26344,num=12,is_bind=1},
[6]={item_id=26129,num=20,is_bind=1},
[7]={item_id=30447,num=7,is_bind=1},
[8]={item_id=26368,num=3,is_bind=1},
[9]={item_id=30423,num=5,is_bind=1},
[10]={item_id=26344,num=15,is_bind=1},
[11]={item_id=26129,num=22,is_bind=1},
[12]={item_id=30424,num=4,is_bind=1},
[13]={item_id=26345,num=2,is_bind=1},
[14]={item_id=44493,num=1,is_bind=1},
[15]={item_id=22090,num=2,is_bind=1},
[16]={item_id=26129,num=24,is_bind=1},
[17]={item_id=30447,num=8,is_bind=1},
[18]={item_id=30424,num=5,is_bind=1},
[19]={item_id=26345,num=3,is_bind=1},
[20]={item_id=26129,num=12,is_bind=1},
[21]={item_id=30423,num=3,is_bind=1},
[22]={item_id=26344,num=10,is_bind=1},
[23]={item_id=30447,num=9,is_bind=1},
[24]={item_id=26344,num=20,is_bind=1},
[25]={item_id=30447,num=10,is_bind=1},
[26]={item_id=26368,num=4,is_bind=1},
[27]={item_id=26129,num=13,is_bind=1},
[28]={item_id=30447,num=5,is_bind=1},
[29]={item_id=26129,num=18,is_bind=1},
[30]={item_id=30424,num=3,is_bind=1},
[31]={item_id=26129,num=23,is_bind=1},
[32]={item_id=26368,num=5,is_bind=1},
[33]={item_id=22090,num=3,is_bind=1},
[34]={item_id=26129,num=26,is_bind=1},
[35]={item_id=30425,num=3,is_bind=1},
[36]={item_id=26345,num=4,is_bind=1},
[37]={item_id=26129,num=15,is_bind=1},
[38]={item_id=30447,num=6,is_bind=1},
[39]={item_id=26344,num=24,is_bind=1},
[40]={item_id=26129,num=21,is_bind=1},
[41]={item_id=26129,num=27,is_bind=1},
[42]={item_id=30425,num=4,is_bind=1},
[43]={item_id=30447,num=11,is_bind=1},
[44]={item_id=26129,num=30,is_bind=1},
[45]={item_id=30425,num=5,is_bind=1},
[46]={item_id=30447,num=12,is_bind=1},
[47]={item_id=26368,num=6,is_bind=1},
[48]={item_id=26345,num=5,is_bind=1},
[49]={item_id=26130,num=9,is_bind=1},
[50]={item_id=26367,num=3,is_bind=1},
[51]={item_id=36420,num=240,is_bind=1},
[52]={item_id=26200,num=6,is_bind=1},
[53]={item_id=26130,num=11,is_bind=1},
[54]={item_id=36420,num=300,is_bind=1},
[55]={item_id=26130,num=8,is_bind=1},
[56]={item_id=36420,num=210,is_bind=1},
[57]={item_id=26200,num=5,is_bind=1},
[58]={item_id=26130,num=10,is_bind=1},
[59]={item_id=36420,num=370,is_bind=1},
[60]={item_id=26130,num=7,is_bind=1},
[61]={item_id=26367,num=2,is_bind=1},
[62]={item_id=36420,num=180,is_bind=1},
[63]={item_id=36420,num=340,is_bind=1},
[64]={item_id=26130,num=6,is_bind=1},
[65]={item_id=36420,num=150,is_bind=1},
[66]={item_id=36420,num=310,is_bind=1},
[67]={item_id=36420,num=120,is_bind=1},
[68]={item_id=26200,num=4,is_bind=1},
[69]={item_id=36420,num=280,is_bind=1},
[70]={item_id=26130,num=5,is_bind=1},
[71]={item_id=36420,num=100,is_bind=1},
[72]={item_id=26200,num=3,is_bind=1},
[73]={item_id=36420,num=260,is_bind=1},
[74]={item_id=30443,num=7,is_bind=1},
[75]={item_id=26367,num=4,is_bind=1},
[76]={item_id=26200,num=7,is_bind=1},
[77]={item_id=26130,num=13,is_bind=1},
[78]={item_id=30443,num=9,is_bind=1},
[79]={item_id=36420,num=350,is_bind=1},
[80]={item_id=30443,num=6,is_bind=1},
[81]={item_id=36420,num=250,is_bind=1},
[82]={item_id=26130,num=12,is_bind=1},
[83]={item_id=30443,num=8,is_bind=1},
[84]={item_id=36420,num=220,is_bind=1},
[85]={item_id=36420,num=190,is_bind=1},
[86]={item_id=30443,num=5,is_bind=1},
[87]={item_id=36420,num=160,is_bind=1},
[88]={item_id=30443,num=4,is_bind=1},
[89]={item_id=36420,num=140,is_bind=1},
[90]={item_id=36420,num=290,is_bind=1},
[91]={item_id=30443,num=10,is_bind=1},
[92]={item_id=36420,num=230,is_bind=1},
[93]={item_id=30447,num=4,is_bind=1},
[94]={item_id=36420,num=200,is_bind=1},
[95]={item_id=30447,num=3,is_bind=1},
[96]={item_id=26367,num=5,is_bind=1},
[97]={item_id=36420,num=360,is_bind=1},
[98]={item_id=26200,num=9,is_bind=1},
[99]={item_id=26130,num=16,is_bind=1},
[100]={item_id=30443,num=12,is_bind=1},
[101]={item_id=36420,num=450,is_bind=1},
[102]={item_id=36420,num=330,is_bind=1},
[103]={item_id=26200,num=8,is_bind=1},
[104]={item_id=26130,num=14,is_bind=1},
[105]={item_id=30443,num=11,is_bind=1},
[106]={item_id=36420,num=420,is_bind=1},
[107]={item_id=36420,num=390,is_bind=1},
[108]={item_id=36420,num=270,is_bind=1},
[109]={item_id=57836,num=8,is_bind=1},
[110]={item_id=36420,num=400,is_bind=1},
[111]={item_id=26200,num=10,is_bind=1},
[112]={item_id=26130,num=18,is_bind=1},
[113]={item_id=30447,num=15,is_bind=1},
[114]={item_id=30443,num=15,is_bind=1},
[115]={item_id=57836,num=10,is_bind=1},
[116]={item_id=36420,num=500,is_bind=1},
[117]={item_id=30447,num=13,is_bind=1},
[118]={item_id=57836,num=7,is_bind=1},
[119]={item_id=30447,num=16,is_bind=1},
[120]={item_id=30443,num=14,is_bind=1},
[121]={item_id=57836,num=9,is_bind=1},
[122]={item_id=36420,num=470,is_bind=1},
[123]={item_id=57836,num=6,is_bind=1},
[124]={item_id=30447,num=14,is_bind=1},
[125]={item_id=36420,num=440,is_bind=1},
[126]={item_id=36420,num=410,is_bind=1},
[127]={item_id=36420,num=380,is_bind=1},
[128]={item_id=57836,num=4,is_bind=1},
[129]={item_id=57836,num=5,is_bind=1},
[130]={item_id=26200,num=11,is_bind=1},
[131]={item_id=26200,num=14,is_bind=1},
[132]={item_id=26200,num=13,is_bind=1},
[133]={item_id=26200,num=15,is_bind=1},
[134]={item_id=26200,num=12,is_bind=1},
[135]={item_id=36420,num=320,is_bind=1},
[136]={item_id=26367,num=6,is_bind=1},
[137]={item_id=26130,num=15,is_bind=1},
[138]={item_id=30443,num=13,is_bind=1},
[139]={item_id=26200,num=16,is_bind=1},
[140]={item_id=26130,num=17,is_bind=1},
[141]={item_id=30443,num=16,is_bind=1},
[142]={item_id=26367,num=7,is_bind=1},
[143]={item_id=26368,num=7,is_bind=1},
[144]={item_id=26200,num=18,is_bind=1},
[145]={item_id=30443,num=18,is_bind=1},
[146]={item_id=36420,num=550,is_bind=1},
[147]={item_id=36420,num=520,is_bind=1},
[148]={item_id=36420,num=490,is_bind=1},
[149]={item_id=36420,num=460,is_bind=1},
[150]={item_id=36420,num=430,is_bind=1},
[151]={item_id=26367,num=8,is_bind=1},
[152]={item_id=26368,num=8,is_bind=1},
[153]={item_id=36420,num=480,is_bind=1},
[154]={item_id=26200,num=20,is_bind=1},
[155]={item_id=26130,num=20,is_bind=1},
[156]={item_id=30447,num=18,is_bind=1},
[157]={item_id=30443,num=20,is_bind=1},
[158]={item_id=36420,num=600,is_bind=1},
[159]={item_id=36420,num=570,is_bind=1},
[160]={item_id=36420,num=540,is_bind=1},
[161]={item_id=36420,num=510,is_bind=1},
[162]={item_id=27835,num=1,is_bind=1},
[163]={item_id=26358,num=5,is_bind=1},
[164]={item_id=26359,num=5,is_bind=1},
[165]={item_id=27835,num=2,is_bind=1},
[166]={item_id=26358,num=4,is_bind=1},
[167]={item_id=26359,num=4,is_bind=1},
[168]={item_id=27830,num=2,is_bind=1},
[169]={item_id=27830,num=3,is_bind=1},
[170]={item_id=26358,num=3,is_bind=1},
[171]={item_id=26359,num=3,is_bind=1},
[172]={item_id=27830,num=1,is_bind=1},
[173]={item_id=27835,num=3,is_bind=1},
[174]={item_id=26358,num=6,is_bind=1},
[175]={item_id=26359,num=6,is_bind=1},
[176]={item_id=27835,num=4,is_bind=1},
[177]={item_id=27830,num=4,is_bind=1},
[178]={item_id=27836,num=1,is_bind=1},
[179]={item_id=26358,num=7,is_bind=1},
[180]={item_id=26359,num=7,is_bind=1},
[181]={item_id=26358,num=8,is_bind=1},
[182]={item_id=26359,num=8,is_bind=1},
[183]={item_id=27836,num=2,is_bind=1},
[184]={item_id=26369,num=1,is_bind=1},
[185]={item_id=22074,num=1,is_bind=1},
[186]={item_id=39986,num=10,is_bind=1},
[187]={item_id=44494,num=1,is_bind=1},
[188]={item_id=30443,num=1,is_bind=1},
[189]={item_id=26415,num=1,is_bind=1},
[190]={item_id=26200,num=1,is_bind=1},
[191]={item_id=26148,num=1,is_bind=1},
[192]={item_id=22530,num=1,is_bind=1},
[193]={item_id=26200,num=2,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
question={
{answer1="A.炎帝",answer2="B.黄帝",},
{seq=1,question="游戏内如果强化装备，需要用什么道具？",answer1="A.强化水晶",answer2="B.淬火灰矿",},
{seq=2,question="战国时期著名军事家孙膑的老师是谁？",answer1="A.廉颇",answer2="B.鬼谷子",},
{seq=3,question="如果玩家之间结婚，需要去找哪位NPC？",answer1="A.仙缘灵君",answer2="B.财神",},
{seq=4,question="玩家镶嵌生命玉魄，可以获得什么属性？",answer1="A.攻击",right_answer=2,},
{seq=5,question="玩家镶嵌攻击玉魄，可以获得什么属性？",answer1="A.攻击",},
{seq=6,question="较高级的玉魄可以通过什么途径提升等级？",answer1="A.合成",answer2="B.拍卖",},
{seq=7,question="通过以下那个活动可以提升财神爷提取额度？",answer1="A.零元购",answer2="B.龙魂御霄",},
{seq=8,question="三国时期，蜀国的第一任丞相叫什么名字？",answer1="A.曹操",answer2="B.诸葛亮",},
{seq=9,question="玩家如果想在游戏内创建仙盟，需要消耗什么道具？",answer1="A.建盟令",answer2="B.扫荡券",},
{seq=10,question="激活玄启子，可以获得多少经验加成？",answer1="A.10%",answer2="B.30%",},
{seq=11,question="激活广虚子，可以增加玩家多少全属性伤害减免？",answer1="A.5%",answer2="B.10%",},
{seq=12,question="中国古代科举考试中，考取第一名被称为？",answer1="A.状元",answer2="B.榜眼",},
{seq=13,question="明朝时期，率领船队下西洋的叫什么名字？",answer1="A.郑和",answer2="B.张骞",},
{seq=14,question="“卧龙凤雏”来源于三国，那么卧龙先生是指？",answer1="A.孙武",},
{seq=15,question="“卧龙凤雏”来源于三国，那么凤雏先生是指？",answer2="B.庞统",},
{seq=16,question="西游记中孙悟空是用什么法器渡过火焰山的？",answer1="A.芭蕉扇",answer2="B.玉净瓶",},
{seq=17,question="“无字碑”也叫“述圣碑”，与之相关的历史人物是？",answer2="B.汉文帝",},
{seq=18,question="《诗经》中“窈窕淑女”的下一句是什么？",answer1="A.关关雎鸠",answer2="B.君子好逑",},
{seq=19,question="张无忌这个人物是出自哪部武侠小说？",answer1="A.倚天屠龙记",answer2="B.天龙八部",},
{seq=20,question="“唐太宗”是中国古代哪位皇帝的庙号？",answer2="B.李世民",right_answer=2,},
{seq=21,question="中国历史上把唐太宗统治时期称为什么？",answer1="A.开元盛世",answer2="B.贞观之治",},
{seq=22,question="下列哪个选项不属于中国四大发明？",answer1="A.铸铁术",answer2="B.指南针",},
{seq=23,question="精忠报国四个字与哪位历史人物有关？",answer1="A.孔明",answer2="B.岳飞",},
{seq=24,question="在《三国演义》中被后人尊为“武圣”的是？",answer1="A.关羽",answer2="B.周瑜",},
{seq=25,question="《西游记》中“大闹五庄观、推倒人参果树”的是谁？",answer1="A.孙悟空",answer2="B.唐僧",},
{seq=26,question="《西游记》中度过火焰山是向谁借的芭蕉扇？",answer1="A.铁扇公主",answer2="B.白骨精",},
{seq=27,question="中国历史上唯一的女皇帝的名字是？",answer2="B.蔡文姬",},
{seq=28,question="药物学百科全书《本草纲目》的作者是？",answer1="A.吴敬梓",answer2="B.李时珍",},
{seq=29,question="中国古代儒家学派的创始人名字是？",answer1="A.孔子",answer2="B.孟子",},
{seq=30,question="古时用“十二生肖“对应十二地支,其中与“辰”对应的是？",answer1="A.龙",answer2="B.虎",},
{seq=31,question="中国有十二个月，其中正月是指哪个月？",answer1="A.一月",answer2="B.五月",},
{seq=32,question="正月十五是中国哪个传统节日？",answer1="A.元宵",answer2="B.清明",},
{seq=33,question="成吉思汗名字中的“汗”是什么意思？",answer1="A.山川",answer2="B.大海",},
{seq=34,question="“西出阳关无故人”中的“阳关”在现在的哪个省区？",answer1="A.西安",answer2="B.甘肃",},
{seq=35,question="下列哪一个不是“中国古代四大美女”？",answer1="A.何仙姑",answer2="B.西施",},
{seq=36,question="刘备是中国古代哪个历史时期的人物？",answer1="A.东汉",answer2="B.西汉",},
{seq=37,question="一统六国的秦始皇，原名叫做什么？",answer1="A.刘备",answer2="B.嬴政",},
{seq=38,question="兵马俑遗址位于中国哪个城市？",answer1="A.西安",answer2="B.洛阳",},
{seq=39,question="是谁编撰了中国第一部纪传体通史《史记》？",answer1="A.司马光",answer2="B.司马迁",},
{seq=40,question="三国中，刘备曾为请诸葛亮出师而？",answer1="A.三顾茅庐",answer2="B.反客为主",},
{seq=41,question="中国农历12月30日，是什么节日？",answer1="A.清明",answer2="B.除夕",},
{seq=42,question="在游戏中，升级最需要的是什么？",answer1="A.经验",answer2="B.装备",},
{seq=43,question="秦始皇是称帝后，统一的文字叫做？",answer1="A.隶书",answer2="B.小篆",},
{seq=44,question="中国最早的文言志怪小说是哪一部？",answer1="A.山海经",answer2="B.搜神传",},
{seq=45,question="《书剑恩仇录》是金庸第几部武侠小说？",answer1="A.第1部",answer2="B.第2部",},
{seq=46,question="中国第一部兵法叫做什么名字？",answer1="A.太公兵法",answer2="B.孙子兵法",},
{seq=47,question="每年农历八月十五日是我国的什么传统节日？",answer2="B.中秋",},
{seq=48,question="中国古代传说中，捏土造人的是哪位？",answer1="A.夸父",answer2="B.女娲",},
{seq=49,question="经验祈福需要消耗的货币是？",right_answer=2,},
{seq=50,question="武则天是中国哪个朝代的人物？",answer1="A.唐",answer2="B.宋",},
{seq=51,question="林黛玉是四大名著中哪部作品中的人物？",answer1="A.红楼梦",answer2="B.水浒传",},
{seq=52,question="VIP几的玩家可以额外进入购买日月修行的次数？",answer1="A.VIP6",answer2="B.VIP3",},
{seq=53,question="《红楼梦》中，葬花的人是谁？",answer1="A.林黛玉",answer2="B.贾宝玉",},
{seq=54,question="地理上在方位的俗语“上北下南”下一句是？",answer1="A.东南西北",answer2="B.左西右东",},
{seq=55,question="下列哪个不属于书画作品中的四君子？",answer1="A.牡丹",answer2="B.梅",},
{seq=56,question="中国神话传说中开天辟地的是谁？",answer1="A.盘古",answer2="B.夸父",},
{seq=57,question="中华道教的创始人叫什么名字？",answer1="A.孔子",answer2="B.老子",},
{seq=58,question="“变脸”是中国哪种话剧的绝活？",answer1="A.川剧",answer2="B.粤剧",},
{seq=59,question="游戏中，日月修行可获得大量什么？",answer1="A.等级",answer2="B.经验",},
{seq=60,question="在游戏中天梯争霸每日结算时间为？",answer1="A.22点",answer2="B.12点",},
{seq=61,question="长恨歌中，描写的是哪位皇帝爱情故事？",answer1="A.唐玄宗",answer2="B.唐高宗",},
{seq=62,question="宝莲灯里谁的母亲被压在华山下？",answer1="A.沉香",answer2="B.岳飞",},
{seq=63,question="天梯争霸每日的基本次数是多少次？",answer1="A.8次",answer2="B.3次",},
{seq=64,question="西游记中，唐僧在哪里收了猪八戒做徒弟？",answer1="A.女儿国",answer2="B.高老庄",},
{seq=65,question="购买名望商城里的道具需要消耗什么？",answer1="A.古钱币",answer2="B.名望",},
{seq=66,question="想要合成一件红色5星装备,至少需要几件红色3星装备？",answer1="A.3件",answer2="B.1件",},
{seq=67,question="游戏中，给好友送花可以提升什么？",answer2="B.魅力值",},
{seq=68,question="游戏中，玄启子可以进阶成为什么？",answer1="A.玄启太子",answer2="B.玄启修士",},
{seq=69,question="游戏中，广虚子可以进阶成为什么？",answer1="A.影邪神君",answer2="B.影邪狂徒",},
{seq=70,question="西游记中，女儿国的国王对谁一见钟情？",answer1="A.唐僧",answer2="B.猪八戒",},
{seq=71,question="下列选项，哪个无法让角色如何快速升级？",answer1="A.换已有时装",answer2="B.提修为",},
{seq=72,question="至少需要VIP几以上的玩家才可以创建仙盟？",answer1="A.4",answer2="B.1",},
{seq=73,question="在日月修行中,玩家可以通过什么方式来提高自身的伤害？",answer1="A.鼓舞",answer2="B.刷新",},
{seq=74,question="在下列选项中，哪一个属于坐骑？",answer1="A.神灵",answer2="B.灵骑",},
{seq=75,question="下列哪个选项不会限制装备穿戴？",answer1="A.排名",answer2="B.等级",},
{seq=76,question="中国古代，晋朝皇帝的姓氏是什么？",answer1="A.唐",answer2="B.司马",},
{seq=77,question="在游戏中，如果玩家复活，会优先消耗什么物品？",answer1="A.元宝",answer2="B.装备",},
{seq=78,question="在游戏中，哪种颜色代表最高级的品质？",answer1="A.粉色",answer2="B.幻彩",},
{seq=79,question="什么颜色品质以上的装备，才可以上架市场拍卖？",answer1="A.紫",answer2="B.橙",},
{seq=80,question="在游戏中，哪个单人副本可以获得神灵进阶材料？",answer1="A.仙遗洞天",answer2="B.神灵仙岛",},
{seq=81,question="在游戏中，扩展行囊优先使用什么货币？",answer1="A.元宝",answer2="B.灵玉",},
{seq=82,question="背包空间不足时会通过什么方式把奖励发给玩家？",answer1="A.邮件",answer2="B.拍卖",},
{seq=83,question="中国历史上第一个统一的封建王朝是哪个？",answer1="A.秦朝",answer2="B.商朝",},
{seq=84,question="明朝的郑和下西洋最远到达了哪里？",answer1="A.非洲",answer2="B.美洲",},
{seq=85,question="历史上第一个完成封狼居胥的将军是谁？",answer1="A.项羽",answer2="B.霍去病",},
{seq=86,question="汉朝的开国皇帝是谁？",answer2="B.刘邦",},
{seq=87,question="东汉末年，黄巾起义的领导者是谁？",answer1="A.张角",answer2="B.董卓",},
{seq=88,question="明朝的开国皇帝是谁？",right_answer=2,},
{seq=89,question="中国古代“四大发明”不包含哪个？",answer1="A.指南针",answer2="B.放大镜",},
{seq=90,question="中国古代“四书”不包含哪个？",right_answer=2,},
{seq=91,question="中国古代“五经”不包含哪个？",answer1="A.孟子",answer2="B.春秋",},
{seq=92,question="“岁寒三友”指的是哪三种植物",answer1="A.竹兰梅",answer2="B.松竹梅",},
{seq=93,question="游戏中穿戴5件千年魂骨，魂环会变成什么颜色？",answer1="A.紫色",answer2="B.红色",},
{seq=94,question="游戏中哪个系统可以让玩家变身？",answer1="A.诸神",answer2="B.幻兽",},
{seq=95,question="游戏中想要穿戴更高阶装备，需要做什么？",answer1="A.提高修为",answer2="B.晋升突破",},
{seq=96,question="“丝绸之路”的起点在哪里？",answer1="A.长安",answer2="B.洛阳",},
{seq=97,question="明朝永乐大典是谁下令编纂的？",answer1="A.朱棣",answer2="B.朱元璋",},
{seq=98,question="唐朝哪位诗人被誉为“诗仙”？",answer1="A.杜甫",answer2="B.李白",},
{seq=99,question="明朝哪位将领抗击倭寇有功？",answer1="A.霍去病",answer2="B.戚继光",},
{seq=100,question="“唐宋八大家”之首是谁？",answer1="A.韩愈",answer2="B.王安石",}
},

question_meta_table_map={
[77]=5,	-- depth:1
[99]=5,	-- depth:1
[2]=5,	-- depth:1
[3]=5,	-- depth:1
[79]=5,	-- depth:1
[80]=5,	-- depth:1
[81]=5,	-- depth:1
[11]=5,	-- depth:1
[89]=98,	-- depth:1
[90]=5,	-- depth:1
[9]=5,	-- depth:1
[8]=5,	-- depth:1
[86]=5,	-- depth:1
[93]=5,	-- depth:1
[88]=5,	-- depth:1
[91]=92,	-- depth:1
[96]=5,	-- depth:1
[87]=86,	-- depth:2
[69]=5,	-- depth:1
[15]=9,	-- depth:2
[38]=5,	-- depth:1
[34]=5,	-- depth:1
[40]=5,	-- depth:1
[42]=5,	-- depth:1
[44]=5,	-- depth:1
[45]=5,	-- depth:1
[47]=5,	-- depth:1
[48]=42,	-- depth:2
[49]=5,	-- depth:1
[50]=82,	-- depth:1
[100]=5,	-- depth:1
[29]=5,	-- depth:1
[55]=5,	-- depth:1
[58]=5,	-- depth:1
[24]=5,	-- depth:1
[60]=5,	-- depth:1
[22]=5,	-- depth:1
[65]=5,	-- depth:1
[66]=5,	-- depth:1
[19]=5,	-- depth:1
[68]=60,	-- depth:2
[70]=5,	-- depth:1
[16]=15,	-- depth:3
[75]=5,	-- depth:1
[35]=5,	-- depth:1
},
boss={
{},
{group_index=1,},
{group_index=2,},
{group_index=3,}
},

boss_meta_table_map={
},
boss_color={
{},
{color=1,need_num=50,monster_id=43201,gather_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},max_xiuwei=7000,max_beast_exp=21000,max_coin=3500000,color_name="雷霆战龙",icon="a3_boss_lx_lt2",name_bg="a3_boss_lx_mzd2",tip_bg="a3_boss_lx_di5",show_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{color=2,need_num=120,monster_id=43202,gather_reward_item={[0]=item_table[1],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9],[5]=item_table[10]},max_xiuwei=8500,max_beast_exp=25500,max_coin=4250000,color_name="紫霄帝龙",icon="a3_boss_lx_lt3",name_bg="a3_boss_lx_mzd3",tip_bg="a3_boss_lx_di6",show_reward_item={[0]=item_table[1],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9],[5]=item_table[10]},},
{color=3,need_num=210,monster_id=43203,gather_reward_item={[0]=item_table[1],[1]=item_table[11],[2]=item_table[7],[3]=item_table[12],[4]=item_table[8],[5]=item_table[13]},max_xiuwei=9000,max_beast_exp=27000,max_coin=4500000,color_name="天怒圣龙",icon="a3_boss_lx_lt4",name_bg="a3_boss_lx_mzd4",tip_bg="a3_boss_lx_di7",show_reward_item={[0]=item_table[1],[1]=item_table[11],[2]=item_table[7],[3]=item_table[12],[4]=item_table[8],[5]=item_table[13]},},
{color=4,need_num=300,monster_id=43204,gather_reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18],[5]=item_table[8],[6]=item_table[19]},max_xiuwei=10000,max_beast_exp=30000,max_coin=5000000,color_name="苍穹神龙",icon="a3_boss_lx_lt5",name_bg="a3_boss_lx_mzd5",tip_bg="a3_boss_lx_di8",show_reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18],[5]=item_table[8],[6]=item_table[19]},},
{group_index=1,gather_reward_item={[0]=item_table[1],[1]=item_table[20],[2]=item_table[3],[3]=item_table[21],[4]=item_table[22]},show_reward_item={[0]=item_table[1],[1]=item_table[20],[2]=item_table[3],[3]=item_table[21],[4]=item_table[22]},},
{group_index=1,gather_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[7],[3]=item_table[3],[4]=item_table[4],[5]=item_table[10]},show_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[7],[3]=item_table[3],[4]=item_table[4],[5]=item_table[10]},},
{group_index=1,need_num=100,gather_reward_item={[0]=item_table[1],[1]=item_table[6],[2]=item_table[23],[3]=item_table[8],[4]=item_table[9],[5]=item_table[24]},show_reward_item={[0]=item_table[1],[1]=item_table[6],[2]=item_table[23],[3]=item_table[8],[4]=item_table[9],[5]=item_table[24]},},
{group_index=1,need_num=160,gather_reward_item={[0]=item_table[1],[1]=item_table[11],[2]=item_table[23],[3]=item_table[12],[4]=item_table[8],[5]=item_table[13]},show_reward_item={[0]=item_table[1],[1]=item_table[11],[2]=item_table[23],[3]=item_table[12],[4]=item_table[8],[5]=item_table[13]},},
{group_index=1,need_num=240,gather_reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[16],[3]=item_table[25],[4]=item_table[18],[5]=item_table[26],[6]=item_table[19]},show_reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[16],[3]=item_table[25],[4]=item_table[18],[5]=item_table[26],[6]=item_table[19]},},
{group_index=2,gather_reward_item={[0]=item_table[1],[1]=item_table[27],[2]=item_table[28],[3]=item_table[8],[4]=item_table[21],[5]=item_table[10]},show_reward_item={[0]=item_table[1],[1]=item_table[27],[2]=item_table[28],[3]=item_table[8],[4]=item_table[21],[5]=item_table[10]},},
{group_index=2,gather_reward_item={[0]=item_table[1],[1]=item_table[29],[2]=item_table[7],[3]=item_table[26],[4]=item_table[4],[5]=item_table[24]},show_reward_item={[0]=item_table[1],[1]=item_table[29],[2]=item_table[7],[3]=item_table[26],[4]=item_table[4],[5]=item_table[24]},},
{group_index=2,gather_reward_item={[0]=item_table[1],[1]=item_table[11],[2]=item_table[23],[3]=item_table[30],[4]=item_table[26],[5]=item_table[13]},show_reward_item={[0]=item_table[1],[1]=item_table[11],[2]=item_table[23],[3]=item_table[30],[4]=item_table[26],[5]=item_table[13]},},
{group_index=2,need_num=140,gather_reward_item={[0]=item_table[15],[1]=item_table[31],[2]=item_table[23],[3]=item_table[12],[4]=item_table[32],[5]=item_table[19]},show_reward_item={[0]=item_table[15],[1]=item_table[31],[2]=item_table[23],[3]=item_table[12],[4]=item_table[32],[5]=item_table[19]},},
{group_index=2,need_num=200,gather_reward_item={[0]=item_table[14],[1]=item_table[33],[2]=item_table[34],[3]=item_table[35],[4]=item_table[25],[5]=item_table[32],[6]=item_table[36]},show_reward_item={[0]=item_table[14],[1]=item_table[33],[2]=item_table[34],[3]=item_table[35],[4]=item_table[25],[5]=item_table[32],[6]=item_table[36]},},
{group_index=3,gather_reward_item={[0]=item_table[1],[1]=item_table[37],[2]=item_table[38],[3]=item_table[8],[4]=item_table[9],[5]=item_table[39]},show_reward_item={[0]=item_table[1],[1]=item_table[37],[2]=item_table[38],[3]=item_table[8],[4]=item_table[9],[5]=item_table[39]},},
{group_index=3,need_num=40,gather_reward_item={[0]=item_table[1],[1]=item_table[40],[2]=item_table[17],[3]=item_table[12],[4]=item_table[26],[5]=item_table[19]},show_reward_item={[0]=item_table[1],[1]=item_table[40],[2]=item_table[17],[3]=item_table[12],[4]=item_table[26],[5]=item_table[19]},},
{group_index=3,need_num=80,gather_reward_item={[0]=item_table[15],[1]=item_table[34],[2]=item_table[25],[3]=item_table[18],[4]=item_table[32],[5]=item_table[19]},show_reward_item={[0]=item_table[15],[1]=item_table[34],[2]=item_table[25],[3]=item_table[18],[4]=item_table[32],[5]=item_table[19]},},
{group_index=3,gather_reward_item={[0]=item_table[15],[1]=item_table[41],[2]=item_table[42],[3]=item_table[43],[4]=item_table[32],[5]=item_table[36]},show_reward_item={[0]=item_table[15],[1]=item_table[41],[2]=item_table[42],[3]=item_table[43],[4]=item_table[32],[5]=item_table[36]},},
{group_index=3,gather_reward_item={[0]=item_table[14],[1]=item_table[33],[2]=item_table[44],[3]=item_table[45],[4]=item_table[46],[5]=item_table[47],[6]=item_table[48]},show_reward_item={[0]=item_table[14],[1]=item_table[33],[2]=item_table[44],[3]=item_table[45],[4]=item_table[46],[5]=item_table[47],[6]=item_table[48]},}
},

boss_color_meta_table_map={
[18]=3,	-- depth:1
[17]=2,	-- depth:1
[15]=5,	-- depth:1
[14]=4,	-- depth:1
[10]=5,	-- depth:1
[12]=17,	-- depth:2
[19]=14,	-- depth:2
[9]=4,	-- depth:1
[8]=3,	-- depth:1
[7]=2,	-- depth:1
[13]=18,	-- depth:2
[20]=15,	-- depth:2
},
boss_skill={
{param0=3,param1=20,param2=20,param3=30,param5=30,param6=30,param7=6051,},
{skill_id=22002,type=2,param3=8,param4=6051,param5=729,tips_txt="进入泥潭，伤害、移速下降",},
{skill_id=22003,type=3,param0=1,param1=5,param2=6,param4=0,tips_txt="受到技能封印效果",},
{skill_id=22004,type=4,param4=353,tips_txt="受到雷电恐惧效果",},
{group_index=1,param0=6,param2=300,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,}
},

boss_skill_meta_table_map={
[8]=4,	-- depth:1
[12]=8,	-- depth:2
[16]=12,	-- depth:3
[6]=2,	-- depth:1
[10]=6,	-- depth:2
[14]=10,	-- depth:3
[5]=1,	-- depth:1
[7]=3,	-- depth:1
[9]=5,	-- depth:2
[11]=7,	-- depth:2
[13]=9,	-- depth:3
[15]=11,	-- depth:3
},
time_reward={
{xiuwei="19,21",beast_exp="59,61",coin="9900,10100",},
{hp_per="80,89",xiuwei="29,31",beast_exp="89,91",coin="14900,15100",},
{hp_per="70,79",beast_exp="119,121",},
{hp_per="60,69",xiuwei="49,51",beast_exp="149,151",coin="24900,25100",},
{hp_per="50,59",xiuwei="59,61",beast_exp="179,181",coin="29900,30100",},
{hp_per="0,49",},
{color=1,xiuwei="22,24",beast_exp="67,69",coin="11233,11433",},
{hp_per="80,89",xiuwei="33,35",beast_exp="101,103",coin="16900,17100",},
{hp_per="70,79",xiuwei="44,46",beast_exp="135,137",coin="22567,22767",},
{hp_per="60,69",xiuwei="56,58",beast_exp="169,171",coin="28233,28433",},
{hp_per="50,59",},
{hp_per="0,49",xiuwei="67,69",beast_exp="203,205",coin="33900,34100",},
{color=2,xiuwei="24,26",beast_exp="73,75",coin="12233,12433",},
{hp_per="80,89",xiuwei="36,38",beast_exp="110,112",coin="18400,18600",},
{hp_per="70,79",xiuwei="48,50",beast_exp="147,149",coin="24567,24767",},
{hp_per="60,69",xiuwei="61,63",beast_exp="184,186",coin="30733,30933",},
{hp_per="50,59",},
{hp_per="0,49",xiuwei="73,75",beast_exp="221,223",coin="36900,37100",},
{color=3,xiuwei="24,26",beast_exp="75,77",coin="12567,12767",},
{color=3,xiuwei="37,39",beast_exp="113,115",coin="18900,19100",},
{hp_per="70,79",xiuwei="50,52",beast_exp="151,153",coin="25233,25433",},
{color=3,xiuwei="62,64",beast_exp="189,191",coin="31567,31767",},
{hp_per="50,59",},
{hp_per="0,49",xiuwei="75,77",beast_exp="227,229",coin="37900,38100",},
{color=4,xiuwei="26,28",beast_exp="79,81",coin="13233,13433",},
{color=4,hp_per="80,89",},
{hp_per="70,79",xiuwei="52,54",beast_exp="159,161",coin="26567,26767",},
{color=4,xiuwei="66,68",beast_exp="199,201",coin="33233,33433",},
{color=4,hp_per="50,59",xiuwei="79,81",coin="39900,40100",},
{hp_per="0,49",},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,hp_per="50,59",},
{hp_per="0,49",},
{group_index=1,},
{group_index=1,},
{group_index=1,beast_exp="119,121",},
{group_index=1,beast_exp="149,151",},
{hp_per="50,59",},
{group_index=1,beast_exp="179,181",},
{group_index=1,},
{group_index=1,beast_exp="101,103",},
{group_index=1,beast_exp="135,137",},
{group_index=1,},
{group_index=1,beast_exp="203,205",},
{hp_per="0,49",},
{group_index=1,},
{group_index=1,beast_exp="110,112",},
{group_index=1,beast_exp="147,149",},
{group_index=1,beast_exp="184,186",},
{group_index=1,},
{hp_per="0,49",},
{group_index=1,beast_exp="75,77",},
{group_index=1,beast_exp="113,115",},
{group_index=1,beast_exp="151,153",},
{group_index=1,beast_exp="189,191",},
{group_index=1,beast_exp="227,229",},
{hp_per="0,49",},
{group_index=2,beast_exp="79,81",},
{group_index=2,beast_exp="119,121",},
{group_index=2,beast_exp="159,161",},
{group_index=2,beast_exp="199,201",},
{group_index=2,},
{group_index=2,hp_per="0,49",xiuwei="59,61",coin="29900,30100",},
{group_index=2,beast_exp="59,61",},
{group_index=2,beast_exp="89,91",},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{hp_per="0,49",},
{group_index=2,beast_exp="67,69",},
{group_index=2,},
{group_index=2,},
{group_index=2,beast_exp="169,171",},
{group_index=2,},
{hp_per="0,49",},
{group_index=2,beast_exp="73,75",},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{group_index=2,beast_exp="221,223",},
{hp_per="0,49",},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{hp_per="0,49",},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{hp_per="50,59",},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{hp_per="0,49",},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{hp_per="50,59",},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{hp_per="50,59",},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,},
{group_index=3,}
},

time_reward_meta_table_map={
[63]=3,	-- depth:1
[26]=3,	-- depth:1
[93]=3,	-- depth:1
[33]=63,	-- depth:2
[91]=1,	-- depth:1
[30]=29,	-- depth:1
[35]=66,	-- depth:1
[36]=35,	-- depth:2
[65]=35,	-- depth:2
[61]=1,	-- depth:1
[56]=26,	-- depth:2
[31]=61,	-- depth:2
[86]=26,	-- depth:2
[6]=5,	-- depth:1
[116]=86,	-- depth:3
[94]=4,	-- depth:1
[119]=29,	-- depth:1
[62]=2,	-- depth:1
[64]=4,	-- depth:1
[115]=25,	-- depth:1
[109]=19,	-- depth:1
[67]=7,	-- depth:1
[103]=13,	-- depth:1
[97]=7,	-- depth:1
[73]=13,	-- depth:1
[79]=19,	-- depth:1
[96]=6,	-- depth:2
[95]=96,	-- depth:3
[85]=115,	-- depth:2
[55]=25,	-- depth:1
[89]=119,	-- depth:2
[90]=89,	-- depth:3
[92]=2,	-- depth:1
[49]=79,	-- depth:2
[120]=90,	-- depth:4
[34]=64,	-- depth:2
[22]=4,	-- depth:1
[21]=19,	-- depth:1
[20]=2,	-- depth:1
[18]=13,	-- depth:1
[27]=29,	-- depth:1
[17]=18,	-- depth:2
[16]=13,	-- depth:1
[15]=13,	-- depth:1
[14]=13,	-- depth:1
[12]=7,	-- depth:1
[28]=4,	-- depth:1
[32]=62,	-- depth:2
[24]=19,	-- depth:1
[37]=67,	-- depth:2
[11]=12,	-- depth:2
[23]=24,	-- depth:2
[43]=73,	-- depth:2
[8]=7,	-- depth:1
[9]=7,	-- depth:1
[10]=7,	-- depth:1
[88]=28,	-- depth:2
[118]=88,	-- depth:3
[117]=27,	-- depth:2
[114]=24,	-- depth:2
[113]=114,	-- depth:3
[112]=22,	-- depth:2
[111]=21,	-- depth:2
[105]=15,	-- depth:2
[110]=20,	-- depth:2
[98]=8,	-- depth:2
[99]=9,	-- depth:2
[100]=10,	-- depth:2
[101]=11,	-- depth:3
[102]=101,	-- depth:4
[108]=18,	-- depth:2
[107]=108,	-- depth:3
[106]=16,	-- depth:2
[104]=14,	-- depth:2
[47]=17,	-- depth:3
[83]=23,	-- depth:3
[84]=83,	-- depth:4
[48]=47,	-- depth:4
[45]=15,	-- depth:2
[50]=20,	-- depth:2
[51]=21,	-- depth:2
[52]=22,	-- depth:2
[53]=83,	-- depth:4
[54]=53,	-- depth:5
[44]=14,	-- depth:2
[57]=27,	-- depth:2
[58]=28,	-- depth:2
[59]=119,	-- depth:2
[42]=12,	-- depth:2
[41]=42,	-- depth:3
[40]=10,	-- depth:2
[87]=117,	-- depth:3
[39]=9,	-- depth:2
[68]=8,	-- depth:2
[69]=39,	-- depth:3
[70]=40,	-- depth:3
[71]=41,	-- depth:4
[72]=71,	-- depth:5
[74]=44,	-- depth:3
[75]=45,	-- depth:3
[76]=16,	-- depth:2
[77]=47,	-- depth:4
[78]=77,	-- depth:5
[80]=50,	-- depth:3
[81]=51,	-- depth:3
[82]=52,	-- depth:3
[46]=76,	-- depth:3
[38]=68,	-- depth:3
[60]=59,	-- depth:3
},
time_reward_param={
{},
{rank=2,xiuwei_param=2000,beast_exp_param=2000,coin_param=2000,},
{rank=3,xiuwei_param=1000,beast_exp_param=1000,coin_param=1000,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=2,},
{group_index=2,},
{group_index=2,},
{group_index=3,},
{group_index=3,},
{group_index=3,}
},

time_reward_param_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[8]=5,	-- depth:2
[9]=6,	-- depth:2
[11]=8,	-- depth:3
[12]=9,	-- depth:3
},
rank_reward={
{reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[8],[3]=item_table[51],[4]=item_table[52]},rmb_reward_item={[0]=item_table[53],[1]=item_table[50],[2]=item_table[8],[3]=item_table[54],[4]=item_table[52]},reward_show_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[8],[3]=item_table[51],[4]=item_table[52]},},
{min_rank=2,max_rank=3,reward_item={[0]=item_table[55],[1]=item_table[50],[2]=item_table[8],[3]=item_table[56],[4]=item_table[57]},rmb_reward_item={[0]=item_table[58],[1]=item_table[50],[2]=item_table[8],[3]=item_table[59],[4]=item_table[57]},reward_show_item={[0]=item_table[55],[1]=item_table[50],[2]=item_table[8],[3]=item_table[56],[4]=item_table[57]},},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[3],[3]=item_table[62],[4]=item_table[57]},rmb_reward_item={[0]=item_table[49],[1]=item_table[61],[2]=item_table[3],[3]=item_table[63],[4]=item_table[57]},reward_show_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[3],[3]=item_table[62],[4]=item_table[57]},},
{min_rank=11,max_rank=20,reward_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[65],[4]=item_table[57]},rmb_reward_item={[0]=item_table[55],[1]=item_table[61],[2]=item_table[3],[3]=item_table[66],[4]=item_table[57]},reward_show_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[65],[4]=item_table[57]},},
{min_rank=21,max_rank=50,reward_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[67],[4]=item_table[68]},rmb_reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[3],[3]=item_table[69],[4]=item_table[68]},reward_show_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[67],[4]=item_table[68]},},
{min_rank=51,max_rank=9999,reward_item={[0]=item_table[70],[1]=item_table[61],[2]=item_table[3],[3]=item_table[71],[4]=item_table[72]},rmb_reward_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[73],[4]=item_table[72]},reward_show_item={[0]=item_table[70],[1]=item_table[61],[2]=item_table[3],[3]=item_table[71],[4]=item_table[72]},},
{color=1,reward_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[75],[3]=item_table[26],[4]=item_table[69],[5]=item_table[76]},rmb_reward_item={[0]=item_table[77],[1]=item_table[78],[2]=item_table[75],[3]=item_table[26],[4]=item_table[79],[5]=item_table[76]},reward_show_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[75],[3]=item_table[26],[4]=item_table[69],[5]=item_table[76]},},
{color=1,reward_item={[0]=item_table[58],[1]=item_table[80],[2]=item_table[75],[3]=item_table[26],[4]=item_table[81],[5]=item_table[52]},rmb_reward_item={[0]=item_table[82],[1]=item_table[83],[2]=item_table[75],[3]=item_table[26],[4]=item_table[59],[5]=item_table[52]},reward_show_item={[0]=item_table[58],[1]=item_table[80],[2]=item_table[75],[3]=item_table[26],[4]=item_table[81],[5]=item_table[52]},},
{color=1,reward_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[84],[5]=item_table[52]},rmb_reward_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[50],[3]=item_table[8],[4]=item_table[63],[5]=item_table[52]},reward_show_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[84],[5]=item_table[52]},},
{color=1,reward_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[85],[5]=item_table[57]},rmb_reward_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[50],[3]=item_table[8],[4]=item_table[66],[5]=item_table[57]},reward_show_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[85],[5]=item_table[57]},},
{color=1,reward_item={[0]=item_table[64],[1]=item_table[86],[2]=item_table[50],[3]=item_table[8],[4]=item_table[87],[5]=item_table[57]},rmb_reward_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[69],[5]=item_table[57]},reward_show_item={[0]=item_table[64],[1]=item_table[86],[2]=item_table[50],[3]=item_table[8],[4]=item_table[87],[5]=item_table[57]},},
{color=1,reward_item={[0]=item_table[64],[1]=item_table[88],[2]=item_table[61],[3]=item_table[3],[4]=item_table[89],[5]=item_table[68]},rmb_reward_item={[0]=item_table[60],[1]=item_table[86],[2]=item_table[61],[3]=item_table[3],[4]=item_table[73],[5]=item_table[68]},reward_show_item={[0]=item_table[64],[1]=item_table[88],[2]=item_table[61],[3]=item_table[3],[4]=item_table[89],[5]=item_table[68]},},
{color=2,},
{color=2,reward_item={[0]=item_table[58],[1]=item_table[38],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[90]},rmb_reward_item={[0]=item_table[77],[1]=item_table[7],[2]=item_table[91],[3]=item_table[75],[4]=item_table[26],[5]=item_table[59]},reward_show_item={[0]=item_table[58],[1]=item_table[38],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[90]},},
{color=2,reward_item={[0]=item_table[49],[1]=item_table[28],[2]=item_table[74],[3]=item_table[50],[4]=item_table[8],[5]=item_table[73]},rmb_reward_item={[0]=item_table[53],[1]=item_table[38],[2]=item_table[78],[3]=item_table[50],[4]=item_table[8],[5]=item_table[63]},reward_show_item={[0]=item_table[49],[1]=item_table[28],[2]=item_table[74],[3]=item_table[50],[4]=item_table[8],[5]=item_table[73]},},
{color=2,reward_item={[0]=item_table[49],[1]=item_table[28],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[92]},rmb_reward_item={[0]=item_table[53],[1]=item_table[38],[2]=item_table[83],[3]=item_table[50],[4]=item_table[8],[5]=item_table[66]},reward_show_item={[0]=item_table[49],[1]=item_table[28],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[92]},},
{color=2,reward_item={[0]=item_table[60],[1]=item_table[93],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[94]},rmb_reward_item={[0]=item_table[49],[1]=item_table[28],[2]=item_table[74],[3]=item_table[50],[4]=item_table[8],[5]=item_table[69]},reward_show_item={[0]=item_table[60],[1]=item_table[93],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[94]},},
{color=2,reward_item={[0]=item_table[64],[1]=item_table[95],[2]=item_table[86],[3]=item_table[61],[4]=item_table[3],[5]=item_table[62]},rmb_reward_item={[0]=item_table[60],[1]=item_table[93],[2]=item_table[80],[3]=item_table[61],[4]=item_table[3],[5]=item_table[73]},reward_show_item={[0]=item_table[64],[1]=item_table[95],[2]=item_table[86],[3]=item_table[61],[4]=item_table[3],[5]=item_table[62]},},
{color=3,reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[97],[6]=item_table[98]},rmb_reward_item={[0]=item_table[99],[1]=item_table[46],[2]=item_table[100],[3]=item_table[96],[4]=item_table[32],[5]=item_table[101],[6]=item_table[98]},reward_show_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[97],[6]=item_table[98]},},
{color=3,reward_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[78],[3]=item_table[96],[4]=item_table[32],[5]=item_table[102],[6]=item_table[103]},rmb_reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[105],[3]=item_table[96],[4]=item_table[32],[5]=item_table[106],[6]=item_table[103]},reward_show_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[78],[3]=item_table[96],[4]=item_table[32],[5]=item_table[102],[6]=item_table[103]},},
{color=3,reward_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[54],[6]=item_table[76]},rmb_reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[91],[3]=item_table[75],[4]=item_table[26],[5]=item_table[107],[6]=item_table[76]},reward_show_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[54],[6]=item_table[76]},},
{color=3,reward_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[74],[3]=item_table[75],[4]=item_table[26],[5]=item_table[108],[6]=item_table[76]},rmb_reward_item={[0]=item_table[82],[1]=item_table[23],[2]=item_table[78],[3]=item_table[75],[4]=item_table[26],[5]=item_table[97],[6]=item_table[76]},reward_show_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[74],[3]=item_table[75],[4]=item_table[26],[5]=item_table[108],[6]=item_table[76]},},
{color=3,reward_item={[0]=item_table[55],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[51],[6]=item_table[52]},rmb_reward_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[83],[3]=item_table[50],[4]=item_table[8],[5]=item_table[102],[6]=item_table[52]},reward_show_item={[0]=item_table[55],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[51],[6]=item_table[52]},},
{color=3,reward_item={[0]=item_table[64],[1]=item_table[28],[2]=item_table[86],[3]=item_table[50],[4]=item_table[8],[5]=item_table[84],[6]=item_table[57]},rmb_reward_item={[0]=item_table[55],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[66],[6]=item_table[57]},reward_show_item={[0]=item_table[64],[1]=item_table[28],[2]=item_table[86],[3]=item_table[50],[4]=item_table[8],[5]=item_table[84],[6]=item_table[57]},},
{color=4,reward_item={[0]=item_table[104],[1]=item_table[46],[2]=item_table[100],[3]=item_table[109],[4]=item_table[96],[5]=item_table[32],[6]=item_table[110],[7]=item_table[111]},rmb_reward_item={[0]=item_table[112],[1]=item_table[113],[2]=item_table[114],[3]=item_table[115],[4]=item_table[96],[5]=item_table[32],[6]=item_table[116],[7]=item_table[111]},reward_show_item={[0]=item_table[104],[1]=item_table[46],[2]=item_table[100],[3]=item_table[109],[4]=item_table[96],[5]=item_table[32],[6]=item_table[110],[7]=item_table[111]},},
{color=4,reward_item={[0]=item_table[77],[1]=item_table[117],[2]=item_table[105],[3]=item_table[118],[4]=item_table[96],[5]=item_table[32],[6]=item_table[59],[7]=item_table[98]},rmb_reward_item={[0]=item_table[99],[1]=item_table[119],[2]=item_table[120],[3]=item_table[121],[4]=item_table[96],[5]=item_table[32],[6]=item_table[122],[7]=item_table[98]},reward_show_item={[0]=item_table[77],[1]=item_table[117],[2]=item_table[105],[3]=item_table[118],[4]=item_table[96],[5]=item_table[32],[6]=item_table[59],[7]=item_table[98]},},
{color=4,reward_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[91],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[63],[7]=item_table[103]},rmb_reward_item={[0]=item_table[104],[1]=item_table[124],[2]=item_table[100],[3]=item_table[109],[4]=item_table[75],[5]=item_table[26],[6]=item_table[125],[7]=item_table[103]},reward_show_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[91],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[63],[7]=item_table[103]},},
{color=4,reward_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[78],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[66],[7]=item_table[103]},rmb_reward_item={[0]=item_table[104],[1]=item_table[124],[2]=item_table[105],[3]=item_table[109],[4]=item_table[75],[5]=item_table[26],[6]=item_table[126],[7]=item_table[103]},reward_show_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[78],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[66],[7]=item_table[103]},},
{color=4,reward_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[83],[3]=item_table[123],[4]=item_table[50],[5]=item_table[8],[6]=item_table[69],[7]=item_table[76]},rmb_reward_item={[0]=item_table[82],[1]=item_table[46],[2]=item_table[91],[3]=item_table[118],[4]=item_table[50],[5]=item_table[8],[6]=item_table[127],[7]=item_table[76]},reward_show_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[83],[3]=item_table[123],[4]=item_table[50],[5]=item_table[8],[6]=item_table[69],[7]=item_table[76]},},
{color=4,reward_item={[0]=item_table[60],[1]=item_table[7],[2]=item_table[80],[3]=item_table[128],[4]=item_table[50],[5]=item_table[8],[6]=item_table[73],[7]=item_table[57]},rmb_reward_item={[0]=item_table[49],[1]=item_table[23],[2]=item_table[83],[3]=item_table[129],[4]=item_table[50],[5]=item_table[8],[6]=item_table[97],[7]=item_table[57]},reward_show_item={[0]=item_table[60],[1]=item_table[7],[2]=item_table[80],[3]=item_table[128],[4]=item_table[50],[5]=item_table[8],[6]=item_table[73],[7]=item_table[57]},},
{group_index=1,reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[8],[3]=item_table[51],[4]=item_table[98]},rmb_reward_item={[0]=item_table[53],[1]=item_table[50],[2]=item_table[8],[3]=item_table[54],[4]=item_table[98]},reward_show_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[8],[3]=item_table[51],[4]=item_table[98]},},
{group_index=1,reward_item={[0]=item_table[55],[1]=item_table[50],[2]=item_table[8],[3]=item_table[56],[4]=item_table[103]},rmb_reward_item={[0]=item_table[58],[1]=item_table[50],[2]=item_table[8],[3]=item_table[59],[4]=item_table[103]},reward_show_item={[0]=item_table[55],[1]=item_table[50],[2]=item_table[8],[3]=item_table[56],[4]=item_table[103]},},
{group_index=1,reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[3],[3]=item_table[62],[4]=item_table[76]},rmb_reward_item={[0]=item_table[49],[1]=item_table[61],[2]=item_table[3],[3]=item_table[63],[4]=item_table[76]},reward_show_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[3],[3]=item_table[62],[4]=item_table[76]},},
{group_index=1,reward_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[65],[4]=item_table[76]},rmb_reward_item={[0]=item_table[55],[1]=item_table[61],[2]=item_table[3],[3]=item_table[66],[4]=item_table[76]},reward_show_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[65],[4]=item_table[76]},},
{group_index=1,reward_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[67],[4]=item_table[52]},rmb_reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[3],[3]=item_table[69],[4]=item_table[52]},reward_show_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[67],[4]=item_table[52]},},
{group_index=1,reward_item={[0]=item_table[70],[1]=item_table[61],[2]=item_table[3],[3]=item_table[71],[4]=item_table[57]},rmb_reward_item={[0]=item_table[64],[1]=item_table[61],[2]=item_table[3],[3]=item_table[73],[4]=item_table[57]},reward_show_item={[0]=item_table[70],[1]=item_table[61],[2]=item_table[3],[3]=item_table[71],[4]=item_table[57]},},
{group_index=1,reward_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[75],[3]=item_table[26],[4]=item_table[69],[5]=item_table[130]},rmb_reward_item={[0]=item_table[77],[1]=item_table[78],[2]=item_table[75],[3]=item_table[26],[4]=item_table[79],[5]=item_table[130]},reward_show_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[75],[3]=item_table[26],[4]=item_table[69],[5]=item_table[130]},},
{color=1,reward_item={[0]=item_table[58],[1]=item_table[80],[2]=item_table[75],[3]=item_table[26],[4]=item_table[81],[5]=item_table[111]},rmb_reward_item={[0]=item_table[82],[1]=item_table[83],[2]=item_table[75],[3]=item_table[26],[4]=item_table[59],[5]=item_table[111]},reward_show_item={[0]=item_table[58],[1]=item_table[80],[2]=item_table[75],[3]=item_table[26],[4]=item_table[81],[5]=item_table[111]},},
{color=1,reward_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[84],[5]=item_table[98]},rmb_reward_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[50],[3]=item_table[8],[4]=item_table[63],[5]=item_table[98]},reward_show_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[84],[5]=item_table[98]},},
{color=1,reward_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[85],[5]=item_table[103]},rmb_reward_item={[0]=item_table[58],[1]=item_table[74],[2]=item_table[50],[3]=item_table[8],[4]=item_table[66],[5]=item_table[103]},reward_show_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[85],[5]=item_table[103]},},
{color=1,reward_item={[0]=item_table[64],[1]=item_table[86],[2]=item_table[50],[3]=item_table[8],[4]=item_table[87],[5]=item_table[76]},rmb_reward_item={[0]=item_table[55],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[69],[5]=item_table[76]},reward_show_item={[0]=item_table[64],[1]=item_table[86],[2]=item_table[50],[3]=item_table[8],[4]=item_table[87],[5]=item_table[76]},},
{color=1,reward_item={[0]=item_table[64],[1]=item_table[88],[2]=item_table[61],[3]=item_table[3],[4]=item_table[89],[5]=item_table[52]},rmb_reward_item={[0]=item_table[60],[1]=item_table[86],[2]=item_table[61],[3]=item_table[3],[4]=item_table[73],[5]=item_table[52]},reward_show_item={[0]=item_table[64],[1]=item_table[88],[2]=item_table[61],[3]=item_table[3],[4]=item_table[89],[5]=item_table[52]},},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[97],[6]=item_table[131]},rmb_reward_item={[0]=item_table[99],[1]=item_table[46],[2]=item_table[100],[3]=item_table[96],[4]=item_table[32],[5]=item_table[101],[6]=item_table[131]},reward_show_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[97],[6]=item_table[131]},},
{color=3,reward_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[78],[3]=item_table[96],[4]=item_table[32],[5]=item_table[102],[6]=item_table[132]},rmb_reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[105],[3]=item_table[96],[4]=item_table[32],[5]=item_table[106],[6]=item_table[132]},reward_show_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[78],[3]=item_table[96],[4]=item_table[32],[5]=item_table[102],[6]=item_table[132]},},
{color=3,reward_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[54],[6]=item_table[130]},rmb_reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[91],[3]=item_table[75],[4]=item_table[26],[5]=item_table[107],[6]=item_table[130]},reward_show_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[54],[6]=item_table[130]},},
{color=3,reward_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[74],[3]=item_table[75],[4]=item_table[26],[5]=item_table[108],[6]=item_table[130]},rmb_reward_item={[0]=item_table[82],[1]=item_table[23],[2]=item_table[78],[3]=item_table[75],[4]=item_table[26],[5]=item_table[97],[6]=item_table[130]},reward_show_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[74],[3]=item_table[75],[4]=item_table[26],[5]=item_table[108],[6]=item_table[130]},},
{color=3,reward_item={[0]=item_table[55],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[51],[6]=item_table[98]},rmb_reward_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[83],[3]=item_table[50],[4]=item_table[8],[5]=item_table[102],[6]=item_table[98]},reward_show_item={[0]=item_table[55],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[51],[6]=item_table[98]},},
{color=3,reward_item={[0]=item_table[64],[1]=item_table[28],[2]=item_table[86],[3]=item_table[50],[4]=item_table[8],[5]=item_table[84],[6]=item_table[76]},rmb_reward_item={[0]=item_table[55],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[66],[6]=item_table[76]},reward_show_item={[0]=item_table[64],[1]=item_table[28],[2]=item_table[86],[3]=item_table[50],[4]=item_table[8],[5]=item_table[84],[6]=item_table[76]},},
{group_index=1,reward_item={[0]=item_table[104],[1]=item_table[46],[2]=item_table[100],[3]=item_table[109],[4]=item_table[96],[5]=item_table[32],[6]=item_table[110],[7]=item_table[133]},rmb_reward_item={[0]=item_table[112],[1]=item_table[113],[2]=item_table[114],[3]=item_table[115],[4]=item_table[96],[5]=item_table[32],[6]=item_table[116],[7]=item_table[133]},reward_show_item={[0]=item_table[104],[1]=item_table[46],[2]=item_table[100],[3]=item_table[109],[4]=item_table[96],[5]=item_table[32],[6]=item_table[110],[7]=item_table[133]},},
{color=4,reward_item={[0]=item_table[77],[1]=item_table[117],[2]=item_table[105],[3]=item_table[118],[4]=item_table[96],[5]=item_table[32],[6]=item_table[59],[7]=item_table[131]},rmb_reward_item={[0]=item_table[99],[1]=item_table[119],[2]=item_table[120],[3]=item_table[121],[4]=item_table[96],[5]=item_table[32],[6]=item_table[122],[7]=item_table[131]},reward_show_item={[0]=item_table[77],[1]=item_table[117],[2]=item_table[105],[3]=item_table[118],[4]=item_table[96],[5]=item_table[32],[6]=item_table[59],[7]=item_table[131]},},
{color=4,reward_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[91],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[63],[7]=item_table[134]},rmb_reward_item={[0]=item_table[104],[1]=item_table[124],[2]=item_table[100],[3]=item_table[109],[4]=item_table[75],[5]=item_table[26],[6]=item_table[125],[7]=item_table[134]},reward_show_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[91],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[63],[7]=item_table[134]},},
{color=4,reward_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[78],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[66],[7]=item_table[130]},rmb_reward_item={[0]=item_table[104],[1]=item_table[124],[2]=item_table[105],[3]=item_table[109],[4]=item_table[75],[5]=item_table[26],[6]=item_table[126],[7]=item_table[130]},reward_show_item={[0]=item_table[53],[1]=item_table[43],[2]=item_table[78],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[66],[7]=item_table[130]},},
{color=4,reward_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[83],[3]=item_table[123],[4]=item_table[50],[5]=item_table[8],[6]=item_table[69],[7]=item_table[111]},rmb_reward_item={[0]=item_table[82],[1]=item_table[46],[2]=item_table[91],[3]=item_table[118],[4]=item_table[50],[5]=item_table[8],[6]=item_table[127],[7]=item_table[111]},reward_show_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[83],[3]=item_table[123],[4]=item_table[50],[5]=item_table[8],[6]=item_table[69],[7]=item_table[111]},},
{color=4,reward_item={[0]=item_table[60],[1]=item_table[7],[2]=item_table[80],[3]=item_table[128],[4]=item_table[50],[5]=item_table[8],[6]=item_table[73],[7]=item_table[103]},rmb_reward_item={[0]=item_table[49],[1]=item_table[23],[2]=item_table[83],[3]=item_table[129],[4]=item_table[50],[5]=item_table[8],[6]=item_table[97],[7]=item_table[103]},reward_show_item={[0]=item_table[60],[1]=item_table[7],[2]=item_table[80],[3]=item_table[128],[4]=item_table[50],[5]=item_table[8],[6]=item_table[73],[7]=item_table[103]},},
{group_index=2,reward_item={[0]=item_table[58],[1]=item_table[96],[2]=item_table[32],[3]=item_table[135],[4]=item_table[134]},rmb_reward_item={[0]=item_table[82],[1]=item_table[96],[2]=item_table[32],[3]=item_table[110],[4]=item_table[134]},reward_show_item={[0]=item_table[58],[1]=item_table[96],[2]=item_table[32],[3]=item_table[135],[4]=item_table[134]},},
{group_index=2,reward_item={[0]=item_table[49],[1]=item_table[96],[2]=item_table[32],[3]=item_table[90],[4]=item_table[130]},rmb_reward_item={[0]=item_table[53],[1]=item_table[96],[2]=item_table[32],[3]=item_table[59],[4]=item_table[130]},reward_show_item={[0]=item_table[49],[1]=item_table[96],[2]=item_table[32],[3]=item_table[90],[4]=item_table[130]},},
{group_index=2,reward_item={[0]=item_table[55],[1]=item_table[75],[2]=item_table[26],[3]=item_table[73],[4]=item_table[111]},rmb_reward_item={[0]=item_table[58],[1]=item_table[75],[2]=item_table[26],[3]=item_table[63],[4]=item_table[111]},reward_show_item={[0]=item_table[55],[1]=item_table[75],[2]=item_table[26],[3]=item_table[73],[4]=item_table[111]},},
{group_index=2,reward_item={[0]=item_table[60],[1]=item_table[75],[2]=item_table[26],[3]=item_table[92],[4]=item_table[98]},rmb_reward_item={[0]=item_table[49],[1]=item_table[75],[2]=item_table[26],[3]=item_table[66],[4]=item_table[98]},reward_show_item={[0]=item_table[60],[1]=item_table[75],[2]=item_table[26],[3]=item_table[92],[4]=item_table[98]},},
{group_index=2,reward_item={[0]=item_table[64],[1]=item_table[50],[2]=item_table[8],[3]=item_table[94],[4]=item_table[103]},rmb_reward_item={[0]=item_table[55],[1]=item_table[50],[2]=item_table[8],[3]=item_table[69],[4]=item_table[103]},reward_show_item={[0]=item_table[64],[1]=item_table[50],[2]=item_table[8],[3]=item_table[94],[4]=item_table[103]},},
{group_index=2,reward_item={[0]=item_table[70],[1]=item_table[50],[2]=item_table[8],[3]=item_table[62],[4]=item_table[52]},rmb_reward_item={[0]=item_table[64],[1]=item_table[50],[2]=item_table[8],[3]=item_table[73],[4]=item_table[52]},reward_show_item={[0]=item_table[70],[1]=item_table[50],[2]=item_table[8],[3]=item_table[62],[4]=item_table[52]},},
{group_index=2,reward_item={[0]=item_table[82],[1]=item_table[105],[2]=item_table[136],[3]=item_table[47],[4]=item_table[97],[5]=item_table[131]},rmb_reward_item={[0]=item_table[137],[1]=item_table[120],[2]=item_table[136],[3]=item_table[47],[4]=item_table[101],[5]=item_table[131]},reward_show_item={[0]=item_table[82],[1]=item_table[105],[2]=item_table[136],[3]=item_table[47],[4]=item_table[97],[5]=item_table[131]},},
{color=1,reward_item={[0]=item_table[53],[1]=item_table[91],[2]=item_table[96],[3]=item_table[32],[4]=item_table[102],[5]=item_table[132]},rmb_reward_item={[0]=item_table[104],[1]=item_table[138],[2]=item_table[96],[3]=item_table[32],[4]=item_table[106],[5]=item_table[132]},reward_show_item={[0]=item_table[53],[1]=item_table[91],[2]=item_table[96],[3]=item_table[32],[4]=item_table[102],[5]=item_table[132]},},
{color=1,reward_item={[0]=item_table[58],[1]=item_table[78],[2]=item_table[96],[3]=item_table[32],[4]=item_table[54],[5]=item_table[130]},rmb_reward_item={[0]=item_table[82],[1]=item_table[105],[2]=item_table[96],[3]=item_table[32],[4]=item_table[107],[5]=item_table[130]},reward_show_item={[0]=item_table[58],[1]=item_table[78],[2]=item_table[96],[3]=item_table[32],[4]=item_table[54],[5]=item_table[130]},},
{color=1,reward_item={[0]=item_table[49],[1]=item_table[78],[2]=item_table[96],[3]=item_table[32],[4]=item_table[108],[5]=item_table[130]},rmb_reward_item={[0]=item_table[53],[1]=item_table[105],[2]=item_table[96],[3]=item_table[32],[4]=item_table[97],[5]=item_table[130]},reward_show_item={[0]=item_table[49],[1]=item_table[78],[2]=item_table[96],[3]=item_table[32],[4]=item_table[108],[5]=item_table[130]},},
{color=1,reward_item={[0]=item_table[55],[1]=item_table[74],[2]=item_table[75],[3]=item_table[26],[4]=item_table[51],[5]=item_table[98]},rmb_reward_item={[0]=item_table[58],[1]=item_table[78],[2]=item_table[75],[3]=item_table[26],[4]=item_table[102],[5]=item_table[98]},reward_show_item={[0]=item_table[55],[1]=item_table[74],[2]=item_table[75],[3]=item_table[26],[4]=item_table[51],[5]=item_table[98]},},
{color=1,reward_item={[0]=item_table[64],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[84],[5]=item_table[76]},rmb_reward_item={[0]=item_table[55],[1]=item_table[74],[2]=item_table[50],[3]=item_table[8],[4]=item_table[66],[5]=item_table[76]},reward_show_item={[0]=item_table[64],[1]=item_table[80],[2]=item_table[50],[3]=item_table[8],[4]=item_table[84],[5]=item_table[76]},},
{color=2,reward_item={[0]=item_table[104],[1]=item_table[25],[2]=item_table[138],[3]=item_table[136],[4]=item_table[47],[5]=item_table[110],[6]=item_table[139]},rmb_reward_item={[0]=item_table[140],[1]=item_table[117],[2]=item_table[141],[3]=item_table[136],[4]=item_table[47],[5]=item_table[116],[6]=item_table[139]},reward_show_item={[0]=item_table[104],[1]=item_table[25],[2]=item_table[138],[3]=item_table[136],[4]=item_table[47],[5]=item_table[110],[6]=item_table[139]},},
{group_index=2,reward_item={[0]=item_table[82],[1]=item_table[25],[2]=item_table[105],[3]=item_table[96],[4]=item_table[32],[5]=item_table[59],[6]=item_table[131]},rmb_reward_item={[0]=item_table[137],[1]=item_table[46],[2]=item_table[120],[3]=item_table[96],[4]=item_table[32],[5]=item_table[122],[6]=item_table[131]},reward_show_item={[0]=item_table[82],[1]=item_table[25],[2]=item_table[105],[3]=item_table[96],[4]=item_table[32],[5]=item_table[59],[6]=item_table[131]},},
{group_index=2,reward_item={[0]=item_table[53],[1]=item_table[17],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[63],[6]=item_table[132]},rmb_reward_item={[0]=item_table[104],[1]=item_table[25],[2]=item_table[138],[3]=item_table[96],[4]=item_table[32],[5]=item_table[125],[6]=item_table[132]},reward_show_item={[0]=item_table[53],[1]=item_table[17],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[63],[6]=item_table[132]},},
{group_index=2,reward_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[66],[6]=item_table[134]},rmb_reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[100],[3]=item_table[96],[4]=item_table[32],[5]=item_table[126],[6]=item_table[134]},reward_show_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[66],[6]=item_table[134]},},
{color=2,reward_item={[0]=item_table[49],[1]=item_table[38],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[69],[6]=item_table[111]},rmb_reward_item={[0]=item_table[53],[1]=item_table[17],[2]=item_table[91],[3]=item_table[75],[4]=item_table[26],[5]=item_table[127],[6]=item_table[111]},reward_show_item={[0]=item_table[49],[1]=item_table[38],[2]=item_table[83],[3]=item_table[75],[4]=item_table[26],[5]=item_table[69],[6]=item_table[111]},},
{group_index=2,reward_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[73],[6]=item_table[103]},rmb_reward_item={[0]=item_table[49],[1]=item_table[7],[2]=item_table[83],[3]=item_table[50],[4]=item_table[8],[5]=item_table[97],[6]=item_table[103]},reward_show_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[80],[3]=item_table[50],[4]=item_table[8],[5]=item_table[73],[6]=item_table[103]},},
{group_index=2,reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[120],[3]=item_table[142],[4]=item_table[143],[5]=item_table[125],[6]=item_table[144]},rmb_reward_item={[0]=item_table[112],[1]=item_table[124],[2]=item_table[145],[3]=item_table[142],[4]=item_table[143],[5]=item_table[146],[6]=item_table[144]},reward_show_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[120],[3]=item_table[142],[4]=item_table[143],[5]=item_table[125],[6]=item_table[144]},},
{group_index=2,reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[138],[3]=item_table[136],[4]=item_table[47],[5]=item_table[102],[6]=item_table[139]},rmb_reward_item={[0]=item_table[99],[1]=item_table[117],[2]=item_table[141],[3]=item_table[136],[4]=item_table[47],[5]=item_table[147],[6]=item_table[139]},reward_show_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[138],[3]=item_table[136],[4]=item_table[47],[5]=item_table[102],[6]=item_table[139]},},
{color=3,reward_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[105],[3]=item_table[136],[4]=item_table[47],[5]=item_table[54],[6]=item_table[131]},rmb_reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[120],[3]=item_table[136],[4]=item_table[47],[5]=item_table[148],[6]=item_table[131]},reward_show_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[105],[3]=item_table[136],[4]=item_table[47],[5]=item_table[54],[6]=item_table[131]},},
{group_index=2,reward_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[105],[3]=item_table[96],[4]=item_table[32],[5]=item_table[108],[6]=item_table[131]},rmb_reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[120],[3]=item_table[96],[4]=item_table[32],[5]=item_table[149],[6]=item_table[131]},reward_show_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[105],[3]=item_table[96],[4]=item_table[32],[5]=item_table[108],[6]=item_table[131]},},
{color=3,reward_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[51],[6]=item_table[134]},rmb_reward_item={[0]=item_table[82],[1]=item_table[23],[2]=item_table[100],[3]=item_table[96],[4]=item_table[32],[5]=item_table[150],[6]=item_table[134]},reward_show_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[91],[3]=item_table[96],[4]=item_table[32],[5]=item_table[51],[6]=item_table[134]},},
{group_index=2,reward_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[74],[3]=item_table[75],[4]=item_table[26],[5]=item_table[84],[6]=item_table[98]},rmb_reward_item={[0]=item_table[49],[1]=item_table[7],[2]=item_table[78],[3]=item_table[75],[4]=item_table[26],[5]=item_table[126],[6]=item_table[98]},reward_show_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[74],[3]=item_table[75],[4]=item_table[26],[5]=item_table[84],[6]=item_table[98]},},
{color=4,reward_item={[0]=item_table[99],[1]=item_table[124],[2]=item_table[141],[3]=item_table[109],[4]=item_table[151],[5]=item_table[152],[6]=item_table[153],[7]=item_table[154]},rmb_reward_item={[0]=item_table[155],[1]=item_table[156],[2]=item_table[157],[3]=item_table[115],[4]=item_table[151],[5]=item_table[152],[6]=item_table[158],[7]=item_table[154]},reward_show_item={[0]=item_table[99],[1]=item_table[124],[2]=item_table[141],[3]=item_table[109],[4]=item_table[151],[5]=item_table[152],[6]=item_table[153],[7]=item_table[154]},},
{group_index=2,reward_item={[0]=item_table[104],[1]=item_table[117],[2]=item_table[120],[3]=item_table[118],[4]=item_table[96],[5]=item_table[32],[6]=item_table[59],[7]=item_table[144]},rmb_reward_item={[0]=item_table[112],[1]=item_table[119],[2]=item_table[145],[3]=item_table[121],[4]=item_table[96],[5]=item_table[32],[6]=item_table[159],[7]=item_table[144]},reward_show_item={[0]=item_table[104],[1]=item_table[117],[2]=item_table[120],[3]=item_table[118],[4]=item_table[96],[5]=item_table[32],[6]=item_table[59],[7]=item_table[144]},},
{group_index=2,reward_item={[0]=item_table[77],[1]=item_table[43],[2]=item_table[138],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[63],[7]=item_table[139]},rmb_reward_item={[0]=item_table[99],[1]=item_table[124],[2]=item_table[141],[3]=item_table[109],[4]=item_table[75],[5]=item_table[26],[6]=item_table[160],[7]=item_table[139]},reward_show_item={[0]=item_table[77],[1]=item_table[43],[2]=item_table[138],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[63],[7]=item_table[139]},},
{group_index=2,reward_item={[0]=item_table[82],[1]=item_table[43],[2]=item_table[100],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[66],[7]=item_table[133]},rmb_reward_item={[0]=item_table[137],[1]=item_table[124],[2]=item_table[114],[3]=item_table[109],[4]=item_table[75],[5]=item_table[26],[6]=item_table[161],[7]=item_table[133]},reward_show_item={[0]=item_table[82],[1]=item_table[43],[2]=item_table[100],[3]=item_table[123],[4]=item_table[75],[5]=item_table[26],[6]=item_table[66],[7]=item_table[133]},},
{color=4,reward_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[91],[3]=item_table[123],[4]=item_table[50],[5]=item_table[8],[6]=item_table[69],[7]=item_table[132]},rmb_reward_item={[0]=item_table[77],[1]=item_table[46],[2]=item_table[138],[3]=item_table[118],[4]=item_table[50],[5]=item_table[8],[6]=item_table[153],[7]=item_table[132]},reward_show_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[91],[3]=item_table[123],[4]=item_table[50],[5]=item_table[8],[6]=item_table[69],[7]=item_table[132]},},
{group_index=2,reward_item={[0]=item_table[55],[1]=item_table[7],[2]=item_table[83],[3]=item_table[128],[4]=item_table[50],[5]=item_table[8],[6]=item_table[73],[7]=item_table[111]},rmb_reward_item={[0]=item_table[58],[1]=item_table[23],[2]=item_table[91],[3]=item_table[129],[4]=item_table[50],[5]=item_table[8],[6]=item_table[149],[7]=item_table[111]},reward_show_item={[0]=item_table[55],[1]=item_table[7],[2]=item_table[83],[3]=item_table[128],[4]=item_table[50],[5]=item_table[8],[6]=item_table[73],[7]=item_table[111]},},
{group_index=3,reward_item={[0]=item_table[58],[1]=item_table[162],[2]=item_table[163],[3]=item_table[164],[4]=item_table[135],[5]=item_table[134]},rmb_reward_item={[0]=item_table[82],[1]=item_table[165],[2]=item_table[163],[3]=item_table[164],[4]=item_table[110],[5]=item_table[134]},reward_show_item={[0]=item_table[58],[1]=item_table[162],[2]=item_table[163],[3]=item_table[164],[4]=item_table[135],[5]=item_table[134]},},
{group_index=3,reward_item={[0]=item_table[49],[1]=item_table[162],[2]=item_table[163],[3]=item_table[164],[4]=item_table[90],[5]=item_table[130]},rmb_reward_item={[0]=item_table[53],[1]=item_table[165],[2]=item_table[163],[3]=item_table[164],[4]=item_table[59],[5]=item_table[130]},reward_show_item={[0]=item_table[49],[1]=item_table[162],[2]=item_table[163],[3]=item_table[164],[4]=item_table[90],[5]=item_table[130]},},
{group_index=3,reward_item={[0]=item_table[55],[1]=item_table[162],[2]=item_table[166],[3]=item_table[167],[4]=item_table[73],[5]=item_table[111]},rmb_reward_item={[0]=item_table[58],[1]=item_table[165],[2]=item_table[166],[3]=item_table[167],[4]=item_table[63],[5]=item_table[111]},reward_show_item={[0]=item_table[55],[1]=item_table[162],[2]=item_table[166],[3]=item_table[167],[4]=item_table[73],[5]=item_table[111]},},
{group_index=3,reward_item={[0]=item_table[60],[1]=item_table[166],[2]=item_table[167],[3]=item_table[92],[4]=item_table[168],[5]=item_table[98]},rmb_reward_item={[0]=item_table[49],[1]=item_table[166],[2]=item_table[167],[3]=item_table[66],[4]=item_table[169],[5]=item_table[98]},reward_show_item={[0]=item_table[60],[1]=item_table[166],[2]=item_table[167],[3]=item_table[92],[4]=item_table[168],[5]=item_table[98]},},
{group_index=3,reward_item={[0]=item_table[64],[1]=item_table[170],[2]=item_table[171],[3]=item_table[94],[4]=item_table[168],[5]=item_table[103]},rmb_reward_item={[0]=item_table[55],[1]=item_table[170],[2]=item_table[171],[3]=item_table[69],[4]=item_table[169],[5]=item_table[103]},reward_show_item={[0]=item_table[64],[1]=item_table[170],[2]=item_table[171],[3]=item_table[94],[4]=item_table[168],[5]=item_table[103]},},
{group_index=3,reward_item={[0]=item_table[70],[1]=item_table[170],[2]=item_table[171],[3]=item_table[62],[4]=item_table[172],[5]=item_table[52]},rmb_reward_item={[0]=item_table[64],[1]=item_table[170],[2]=item_table[171],[3]=item_table[73],[4]=item_table[168],[5]=item_table[52]},reward_show_item={[0]=item_table[70],[1]=item_table[170],[2]=item_table[171],[3]=item_table[62],[4]=item_table[172],[5]=item_table[52]},},
{group_index=3,reward_item={[0]=item_table[82],[1]=item_table[173],[2]=item_table[174],[3]=item_table[175],[4]=item_table[97],[5]=item_table[131]},rmb_reward_item={[0]=item_table[137],[1]=item_table[176],[2]=item_table[174],[3]=item_table[175],[4]=item_table[101],[5]=item_table[131]},reward_show_item={[0]=item_table[82],[1]=item_table[173],[2]=item_table[174],[3]=item_table[175],[4]=item_table[97],[5]=item_table[131]},},
{group_index=3,reward_item={[0]=item_table[53],[1]=item_table[165],[2]=item_table[163],[3]=item_table[164],[4]=item_table[102],[5]=item_table[132]},rmb_reward_item={[0]=item_table[104],[1]=item_table[173],[2]=item_table[163],[3]=item_table[164],[4]=item_table[106],[5]=item_table[132]},reward_show_item={[0]=item_table[53],[1]=item_table[165],[2]=item_table[163],[3]=item_table[164],[4]=item_table[102],[5]=item_table[132]},},
{group_index=3,reward_item={[0]=item_table[58],[1]=item_table[162],[2]=item_table[163],[3]=item_table[164],[4]=item_table[54],[5]=item_table[130]},rmb_reward_item={[0]=item_table[82],[1]=item_table[165],[2]=item_table[163],[3]=item_table[164],[4]=item_table[107],[5]=item_table[130]},reward_show_item={[0]=item_table[58],[1]=item_table[162],[2]=item_table[163],[3]=item_table[164],[4]=item_table[54],[5]=item_table[130]},},
{group_index=3,reward_item={[0]=item_table[49],[1]=item_table[163],[2]=item_table[164],[3]=item_table[108],[4]=item_table[168],[5]=item_table[130]},rmb_reward_item={[0]=item_table[53],[1]=item_table[163],[2]=item_table[164],[3]=item_table[97],[4]=item_table[169],[5]=item_table[130]},reward_show_item={[0]=item_table[49],[1]=item_table[163],[2]=item_table[164],[3]=item_table[108],[4]=item_table[168],[5]=item_table[130]},},
{color=1,reward_item={[0]=item_table[55],[1]=item_table[166],[2]=item_table[167],[3]=item_table[51],[4]=item_table[168],[5]=item_table[98]},rmb_reward_item={[0]=item_table[58],[1]=item_table[166],[2]=item_table[167],[3]=item_table[102],[4]=item_table[169],[5]=item_table[98]},reward_show_item={[0]=item_table[55],[1]=item_table[166],[2]=item_table[167],[3]=item_table[51],[4]=item_table[168],[5]=item_table[98]},},
{color=1,reward_item={[0]=item_table[64],[1]=item_table[170],[2]=item_table[171],[3]=item_table[84],[4]=item_table[168],[5]=item_table[76]},rmb_reward_item={[0]=item_table[55],[1]=item_table[170],[2]=item_table[171],[3]=item_table[66],[4]=item_table[169],[5]=item_table[76]},reward_show_item={[0]=item_table[64],[1]=item_table[170],[2]=item_table[171],[3]=item_table[84],[4]=item_table[168],[5]=item_table[76]},},
{color=2,reward_item={[0]=item_table[104],[1]=item_table[25],[2]=item_table[173],[3]=item_table[174],[4]=item_table[175],[5]=item_table[110],[6]=item_table[139]},rmb_reward_item={[0]=item_table[140],[1]=item_table[117],[2]=item_table[176],[3]=item_table[174],[4]=item_table[175],[5]=item_table[116],[6]=item_table[139]},reward_show_item={[0]=item_table[104],[1]=item_table[25],[2]=item_table[173],[3]=item_table[174],[4]=item_table[175],[5]=item_table[110],[6]=item_table[139]},},
{group_index=3,reward_item={[0]=item_table[82],[1]=item_table[25],[2]=item_table[173],[3]=item_table[163],[4]=item_table[164],[5]=item_table[59],[6]=item_table[131]},rmb_reward_item={[0]=item_table[137],[1]=item_table[46],[2]=item_table[176],[3]=item_table[163],[4]=item_table[164],[5]=item_table[122],[6]=item_table[131]},reward_show_item={[0]=item_table[82],[1]=item_table[25],[2]=item_table[173],[3]=item_table[163],[4]=item_table[164],[5]=item_table[59],[6]=item_table[131]},},
{group_index=3,reward_item={[0]=item_table[53],[1]=item_table[17],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[63],[6]=item_table[132]},rmb_reward_item={[0]=item_table[104],[1]=item_table[25],[2]=item_table[173],[3]=item_table[163],[4]=item_table[164],[5]=item_table[125],[6]=item_table[132]},reward_show_item={[0]=item_table[53],[1]=item_table[17],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[63],[6]=item_table[132]},},
{group_index=3,reward_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[66],[6]=item_table[134]},rmb_reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[173],[3]=item_table[163],[4]=item_table[164],[5]=item_table[126],[6]=item_table[134]},reward_show_item={[0]=item_table[58],[1]=item_table[17],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[66],[6]=item_table[134]},},
{group_index=3,reward_item={[0]=item_table[49],[1]=item_table[38],[2]=item_table[166],[3]=item_table[167],[4]=item_table[69],[5]=item_table[169],[6]=item_table[111]},rmb_reward_item={[0]=item_table[53],[1]=item_table[17],[2]=item_table[166],[3]=item_table[167],[4]=item_table[127],[5]=item_table[177],[6]=item_table[111]},reward_show_item={[0]=item_table[49],[1]=item_table[38],[2]=item_table[166],[3]=item_table[167],[4]=item_table[69],[5]=item_table[169],[6]=item_table[111]},},
{group_index=3,reward_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[170],[3]=item_table[171],[4]=item_table[73],[5]=item_table[168],[6]=item_table[103]},rmb_reward_item={[0]=item_table[49],[1]=item_table[7],[2]=item_table[170],[3]=item_table[171],[4]=item_table[97],[5]=item_table[169],[6]=item_table[103]},reward_show_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[170],[3]=item_table[171],[4]=item_table[73],[5]=item_table[168],[6]=item_table[103]},},
{color=3,reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[178],[3]=item_table[179],[4]=item_table[180],[5]=item_table[125],[6]=item_table[144]},rmb_reward_item={[0]=item_table[112],[1]=item_table[124],[2]=item_table[178],[3]=item_table[179],[4]=item_table[180],[5]=item_table[146],[6]=item_table[144]},reward_show_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[178],[3]=item_table[179],[4]=item_table[180],[5]=item_table[125],[6]=item_table[144]},},
{group_index=3,reward_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[173],[3]=item_table[174],[4]=item_table[175],[5]=item_table[102],[6]=item_table[139]},rmb_reward_item={[0]=item_table[99],[1]=item_table[117],[2]=item_table[176],[3]=item_table[174],[4]=item_table[175],[5]=item_table[147],[6]=item_table[139]},reward_show_item={[0]=item_table[77],[1]=item_table[25],[2]=item_table[173],[3]=item_table[174],[4]=item_table[175],[5]=item_table[102],[6]=item_table[139]},},
{color=3,reward_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[173],[3]=item_table[174],[4]=item_table[175],[5]=item_table[54],[6]=item_table[131]},rmb_reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[176],[3]=item_table[174],[4]=item_table[175],[5]=item_table[148],[6]=item_table[131]},reward_show_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[173],[3]=item_table[174],[4]=item_table[175],[5]=item_table[54],[6]=item_table[131]},},
{group_index=3,reward_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[108],[6]=item_table[131]},rmb_reward_item={[0]=item_table[104],[1]=item_table[43],[2]=item_table[173],[3]=item_table[163],[4]=item_table[164],[5]=item_table[149],[6]=item_table[131]},reward_show_item={[0]=item_table[53],[1]=item_table[23],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[108],[6]=item_table[131]},},
{group_index=3,reward_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[51],[6]=item_table[134]},rmb_reward_item={[0]=item_table[82],[1]=item_table[23],[2]=item_table[173],[3]=item_table[163],[4]=item_table[164],[5]=item_table[150],[6]=item_table[134]},reward_show_item={[0]=item_table[58],[1]=item_table[7],[2]=item_table[165],[3]=item_table[163],[4]=item_table[164],[5]=item_table[51],[6]=item_table[134]},},
{group_index=3,reward_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[166],[3]=item_table[167],[4]=item_table[84],[5]=item_table[169],[6]=item_table[98]},rmb_reward_item={[0]=item_table[49],[1]=item_table[7],[2]=item_table[166],[3]=item_table[167],[4]=item_table[126],[5]=item_table[177],[6]=item_table[98]},reward_show_item={[0]=item_table[60],[1]=item_table[38],[2]=item_table[166],[3]=item_table[167],[4]=item_table[84],[5]=item_table[169],[6]=item_table[98]},},
{color=4,reward_item={[0]=item_table[99],[1]=item_table[124],[2]=item_table[178],[3]=item_table[109],[4]=item_table[181],[5]=item_table[182],[6]=item_table[153],[7]=item_table[154]},rmb_reward_item={[0]=item_table[155],[1]=item_table[156],[2]=item_table[183],[3]=item_table[115],[4]=item_table[181],[5]=item_table[182],[6]=item_table[158],[7]=item_table[154]},reward_show_item={[0]=item_table[99],[1]=item_table[124],[2]=item_table[178],[3]=item_table[109],[4]=item_table[181],[5]=item_table[182],[6]=item_table[153],[7]=item_table[154]},},
{group_index=3,reward_item={[0]=item_table[104],[1]=item_table[117],[2]=item_table[178],[3]=item_table[118],[4]=item_table[179],[5]=item_table[180],[6]=item_table[59],[7]=item_table[144]},rmb_reward_item={[0]=item_table[112],[1]=item_table[119],[2]=item_table[178],[3]=item_table[121],[4]=item_table[179],[5]=item_table[180],[6]=item_table[159],[7]=item_table[144]},reward_show_item={[0]=item_table[104],[1]=item_table[117],[2]=item_table[178],[3]=item_table[118],[4]=item_table[179],[5]=item_table[180],[6]=item_table[59],[7]=item_table[144]},},
{group_index=3,reward_item={[0]=item_table[77],[1]=item_table[43],[2]=item_table[173],[3]=item_table[123],[4]=item_table[174],[5]=item_table[175],[6]=item_table[63],[7]=item_table[139]},rmb_reward_item={[0]=item_table[99],[1]=item_table[124],[2]=item_table[176],[3]=item_table[109],[4]=item_table[174],[5]=item_table[175],[6]=item_table[160],[7]=item_table[139]},reward_show_item={[0]=item_table[77],[1]=item_table[43],[2]=item_table[173],[3]=item_table[123],[4]=item_table[174],[5]=item_table[175],[6]=item_table[63],[7]=item_table[139]},},
{group_index=3,reward_item={[0]=item_table[82],[1]=item_table[43],[2]=item_table[173],[3]=item_table[123],[4]=item_table[174],[5]=item_table[175],[6]=item_table[66],[7]=item_table[133]},rmb_reward_item={[0]=item_table[137],[1]=item_table[124],[2]=item_table[176],[3]=item_table[109],[4]=item_table[174],[5]=item_table[175],[6]=item_table[161],[7]=item_table[133]},reward_show_item={[0]=item_table[82],[1]=item_table[43],[2]=item_table[173],[3]=item_table[123],[4]=item_table[174],[5]=item_table[175],[6]=item_table[66],[7]=item_table[133]},},
{group_index=3,reward_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[165],[3]=item_table[123],[4]=item_table[163],[5]=item_table[164],[6]=item_table[69],[7]=item_table[132]},rmb_reward_item={[0]=item_table[77],[1]=item_table[46],[2]=item_table[173],[3]=item_table[118],[4]=item_table[163],[5]=item_table[164],[6]=item_table[153],[7]=item_table[132]},reward_show_item={[0]=item_table[58],[1]=item_table[25],[2]=item_table[165],[3]=item_table[123],[4]=item_table[163],[5]=item_table[164],[6]=item_table[69],[7]=item_table[132]},},
{group_index=3,reward_item={[0]=item_table[55],[1]=item_table[7],[2]=item_table[165],[3]=item_table[128],[4]=item_table[166],[5]=item_table[167],[6]=item_table[73],[7]=item_table[111]},rmb_reward_item={[0]=item_table[58],[1]=item_table[23],[2]=item_table[173],[3]=item_table[129],[4]=item_table[166],[5]=item_table[167],[6]=item_table[149],[7]=item_table[111]},reward_show_item={[0]=item_table[55],[1]=item_table[7],[2]=item_table[165],[3]=item_table[128],[4]=item_table[166],[5]=item_table[167],[6]=item_table[73],[7]=item_table[111]},}
},

rank_reward_meta_table_map={
[43]=13,	-- depth:1
[67]=7,	-- depth:1
[103]=91,	-- depth:1
[97]=7,	-- depth:1
[49]=19,	-- depth:1
[109]=91,	-- depth:1
[73]=61,	-- depth:1
[115]=91,	-- depth:1
[37]=7,	-- depth:1
[79]=19,	-- depth:1
[55]=25,	-- depth:1
[85]=61,	-- depth:1
[21]=3,	-- depth:1
[92]=2,	-- depth:1
[93]=3,	-- depth:1
[12]=6,	-- depth:1
[11]=5,	-- depth:1
[94]=4,	-- depth:1
[95]=5,	-- depth:1
[96]=6,	-- depth:1
[10]=4,	-- depth:1
[9]=3,	-- depth:1
[66]=6,	-- depth:1
[8]=2,	-- depth:1
[62]=2,	-- depth:1
[63]=3,	-- depth:1
[20]=2,	-- depth:1
[64]=4,	-- depth:1
[14]=2,	-- depth:1
[15]=3,	-- depth:1
[22]=4,	-- depth:1
[23]=5,	-- depth:1
[24]=6,	-- depth:1
[18]=6,	-- depth:1
[26]=2,	-- depth:1
[27]=3,	-- depth:1
[28]=4,	-- depth:1
[29]=5,	-- depth:1
[17]=5,	-- depth:1
[30]=6,	-- depth:1
[33]=3,	-- depth:1
[34]=4,	-- depth:1
[35]=5,	-- depth:1
[36]=6,	-- depth:1
[16]=4,	-- depth:1
[32]=2,	-- depth:1
[65]=5,	-- depth:1
[89]=65,	-- depth:2
[98]=8,	-- depth:2
[87]=27,	-- depth:2
[88]=28,	-- depth:2
[90]=30,	-- depth:2
[106]=16,	-- depth:2
[100]=10,	-- depth:2
[118]=28,	-- depth:2
[117]=27,	-- depth:2
[116]=26,	-- depth:2
[86]=26,	-- depth:2
[113]=23,	-- depth:2
[112]=22,	-- depth:2
[99]=9,	-- depth:2
[111]=93,	-- depth:2
[108]=18,	-- depth:2
[107]=17,	-- depth:2
[105]=15,	-- depth:2
[104]=14,	-- depth:2
[102]=96,	-- depth:2
[101]=95,	-- depth:2
[110]=20,	-- depth:2
[114]=24,	-- depth:2
[60]=36,	-- depth:2
[83]=65,	-- depth:2
[53]=35,	-- depth:2
[52]=34,	-- depth:2
[51]=33,	-- depth:2
[50]=32,	-- depth:2
[48]=18,	-- depth:2
[47]=17,	-- depth:2
[54]=36,	-- depth:2
[46]=16,	-- depth:2
[44]=14,	-- depth:2
[42]=36,	-- depth:2
[41]=35,	-- depth:2
[40]=34,	-- depth:2
[39]=33,	-- depth:2
[38]=32,	-- depth:2
[45]=15,	-- depth:2
[56]=32,	-- depth:2
[57]=33,	-- depth:2
[58]=34,	-- depth:2
[82]=22,	-- depth:2
[81]=63,	-- depth:2
[80]=20,	-- depth:2
[78]=18,	-- depth:2
[77]=65,	-- depth:2
[76]=16,	-- depth:2
[75]=15,	-- depth:2
[74]=14,	-- depth:2
[72]=66,	-- depth:2
[71]=65,	-- depth:2
[70]=64,	-- depth:2
[69]=63,	-- depth:2
[68]=62,	-- depth:2
[119]=29,	-- depth:2
[59]=35,	-- depth:2
[84]=24,	-- depth:2
[120]=30,	-- depth:2
},
rule_image={
[1]={index=1,},
[2]={index=2,image="a3_boss_lx_bj11",title="a3_boss_lx_ysz9",},
[3]={index=3,image="a3_boss_lx_bj12",title="a3_boss_lx_ysz4",},
[4]={index=4,image="a3_boss_lx_bj13",title="a3_boss_lx_ysz5",},
[5]={index=5,image="a3_boss_lx_bj14",title="a3_boss_lx_ysz6",},
[6]={index=6,image="a3_boss_lx_bj15",title="a3_boss_lx_ysz7",},
[7]={index=7,image="a3_boss_lx_bj16",title="a3_boss_lx_ysz8",},
[8]={index=8,image="a3_boss_lx_bj17",title="a3_boss_lx_ysz10",}
},

rule_image_meta_table_map={
},
privilege_display={
[1]={order=1,},
[2]={order=2,icon=21002,name="毒雾陷阵",description="真气御身，不受<color=#79fa82>减伤、定身</color>影响",},
[3]={order=3,icon=21003,name="魂锁禁阵",description="魂力破限，不受<color=#79fa82>技能封印</color>影响",},
[4]={order=4,icon=21004,name="龙鸣天罚",description="身轻如燕，不受<color=#79fa82>雷电恐惧</color>影响",},
[5]={order=5,icon=21005,name="移速增加",description="活动期间，移动速度<color=#79fa82>提高20%</color>",},
[6]={order=6,icon=21006,name="奖励提升",description="提升<color=#79fa82>伤害奖励上限、排名奖励</color>",},
[7]={order=7,icon=21007,name="自动答题",description="答题时，自动<color=#79fa82>选择正确答案</color>",},
[8]={order=8,icon=21008,name="属性加成",description="永久获得以下属性",}
},

privilege_display_meta_table_map={
},
tip_show_time={
[1]={index=1,},
[2]={index=2,start_time=200,end_time=205,},
[3]={index=3,start_time=300,end_time=305,},
[4]={index=4,start_time=400,end_time=405,},
[5]={index=5,start_time=500,end_time=505,},
[6]={index=6,start_time=600,end_time=605,},
[7]={index=7,start_time=700,end_time=705,},
[8]={index=8,start_time=800,end_time=805,},
[9]={index=9,start_time=900,end_time=905,},
[10]={index=10,start_time=1000,end_time=1005,},
[11]={index=11,start_time=1100,end_time=1105,}
},

tip_show_time_meta_table_map={
},
other_default_table={open_level=130,open_day=1,question_wait_time=90,question_time=12,question_end_show_time=3,boss_refresh_wait_time=30,boss_time=1380,boss_end_wait_time=60,rmb_type=215,rmb_seq=1,rmb_price=68,rmb_reward_item={[0]=item_table[184],[1]=item_table[25],[2]=item_table[185],[3]=item_table[19],[4]=item_table[186]},rmb_max_xiuwei_add=10000,rmb_max_beast_exp_add=30000,rmb_max_coin_add=5000000,rmb_buff_id=6050,rmb_attr="101,6857|102,171|103,86|104,57",up_color_cost_gold=20,up_color_add_num=1,up_color_reward_item={[0]=item_table[187]},up_color_reward_item_show={[0]=item_table[188],[1]=item_table[189],[2]=item_table[190],[3]=item_table[191],[4]=item_table[192]},model_show_type=1,model_bundle_name="model/boss/8049_prefab",model_asset_name=8049,model_show_itemid="",model_scale=0.5,model_rot="0|180|0",model_pos="0|-2.2|0",tip_model_show_type=1,tip_model_bundle_name="model/boss/8049_prefab",tip_model_asset_name=8049,tip_model_show_itemid="",tip_model_scale=0.08,tip_model_rot="0|180|0",tip_model_pos="0|-0.7|0",boss_show_guide_id=1,boss_end_guide_id=2,audio_clip=1101,},

question_default_table={seq=0,question="中国古代传说中，精卫是谁的女儿所化？",answer1="A.武则天",answer2="B.生命",right_answer=1,answer_type1=1,answer_type2=1,right_reward_item={[0]=item_table[193]},right_get_exp=75000,error_reward_item={[0]=item_table[190]},error_get_exp=67500,},

boss_default_table={group_index=0,scene_id=4300,born_pos="160,530",born_range=5,monster_pos="162,545",answer1_pos="134,549",answer2_pos="188,550",answer1_effect_pos="265,179,474",answer2_effect_pos="320,179,474",answer_range=15,question_num=20,answer_add_hurt_per=500,gold_guwu_add_per=1000,gold_guwu_max_times=5,gold_guwu_cost=50,coin_guwu_add_per=500,coin_guwu_max_times=5,coin_guwu_cost=50000,},

boss_color_default_table={group_index=0,color=0,need_num=0,monster_id=43200,gather_id=2064,gather_times=50,gather_time=3,duration_time=300,gather_reward_item={[0]=item_table[1],[1]=item_table[3],[2]=item_table[21],[3]=item_table[22]},max_xiuwei=5000,max_beast_exp=15000,max_coin=2500000,color_name="云影游龙",icon="a3_boss_lx_lt1",name_bg="a3_boss_lx_mzd1",tip_bg="a3_boss_lx_di4",show_reward_item={[0]=item_table[1],[1]=item_table[3],[2]=item_table[21],[3]=item_table[22]},},

boss_skill_default_table={group_index=0,skill_id=22001,type=1,param0=2,param1=3,param2=4,param3=2,param4=3,param5=0,param6=0,param7=0,param8=0,param9=0,tips_txt="已被击退，移速下降",},

time_reward_default_table={group_index=0,color=0,hp_per="90,100",xiuwei="39,41",beast_exp="239,241",coin="19900,20100",},

time_reward_param_default_table={group_index=0,rank=1,xiuwei_param=3000,beast_exp_param=3000,coin_param=3000,},

rank_reward_default_table={group_index=0,color=0,min_rank=1,max_rank=1,reward_item={[0]=item_table[53],[1]=item_table[38],[2]=item_table[78],[3]=item_table[75],[4]=item_table[26],[5]=item_table[135]},rmb_reward_item={[0]=item_table[104],[1]=item_table[17],[2]=item_table[105],[3]=item_table[75],[4]=item_table[26],[5]=item_table[110]},reward_show_item={[0]=item_table[53],[1]=item_table[38],[2]=item_table[78],[3]=item_table[75],[4]=item_table[26],[5]=item_table[135]},},

rule_image_default_table={index=1,image="a3_boss_lx_bj10",title="a3_boss_lx_ysz3",},

privilege_display_default_table={order=1,icon=21001,name="龙威天穹",description="临危不惧，屹立如山，不受<color=#79fa82>击退</color>影响",},

tip_show_time_default_table={index=1,start_time=100,end_time=105,}

}

