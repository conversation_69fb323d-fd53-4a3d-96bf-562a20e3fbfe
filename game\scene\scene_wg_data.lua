SceneWGData = SceneWGData or BaseClass()
local M = SceneWGData

local SHIELD_NUM = 10		-- 玩家数量达到设置的数量后隐藏其他玩家名字

function M:__init()
	if M.Instance then
		print_error("[SceneWGData]:Attempt to create singleton twice!")
	end
	M.Instance = self
	self.map_block_bubble_chat_limit = ListToMapList(self:GetMapBlockAutoCfg().bubble_probability, "scene_id")
	self.map_block_bubble_chat_content = ListToMapList(self:GetMapBlockAutoCfg().bubble_content, "group_id")
end

function M:__delete()
	M.Instance = nil
end

function M:TargetSelectIsTask(value)
	return value == SceneTargetSelectType.TASK
end

function M:TargetSelectIsScene(value)
	return value == SceneTargetSelectType.SCENE
end

function M:TargetSelectIsSelect(value)
	return value == SceneTargetSelectType.SELECT
end

-----------------------------------------------------------
function M:GetMapBlockAutoCfg()
	if not self.map_block_cfg_auto then
		self.map_block_cfg_auto = ConfigManager.Instance:GetAutoConfig("map_block_cfg_auto")
	end
	return self.map_block_cfg_auto
end

function M:GetMapBlockOtherCfg(scene_id)
	if not self.map_block_other_cfg then
		self.map_block_other_cfg = self:GetMapBlockAutoCfg().other
	end
	if self.map_block_other_cfg[scene_id] then
		return self.map_block_other_cfg[scene_id]
	end
end

function M:GetMapBlockBubbleCfg()
	if not self.map_block_bubble_cfg then
		self.map_block_bubble_cfg = self:GetMapBlockAutoCfg().bubble
	end
	if self.map_block_bubble_cfg[1] then
		return self.map_block_bubble_cfg[1]
	end
end

-- 获取地图阻挡配置
function M:GetMapBlockCfg(id)
	if nil == id or "" == id then
		return
	end
	local map_block_cfg = self:GetMapBlockAutoCfg()
	return map_block_cfg["actid_" .. id]
end

-- 获取地图阻挡区域内玩家群聊限制配置
function M:GetMapBlockBubbleChatLimit(scene_id)
	if self.map_block_bubble_chat_limit[scene_id] then
		return self.map_block_bubble_chat_limit[scene_id]
	end
end

-- 获取地图阻挡区域内玩家群聊限制配置
function M:GetMapBlockBubbleChatLimitGroupId(scene_id)
	local chat_limit = self:GetMapBlockBubbleChatLimit(scene_id)
	if nil == chat_limit then
		return -1
	end
	math.randomseed(os.time())
	local rand_num = math.random(1, 10000)
	local range = 0
	for k, v in pairs(chat_limit) do
		range = range + v.probability
		if rand_num <= range then
			return v.group_id
		end
	end
	return -1
end

-- 获取NPC群聊配置
function M:GetMapBlockBubbleChatContent(group_id)
	if self.map_block_bubble_chat_content[group_id] then
		return self.map_block_bubble_chat_content[group_id]
	end
end

---------------------需求-----------------------
-- 当玩家在野外挂机状态进入副本/活动场景后，从场景出来保持原来挂机状态
-- 当玩家正在主线/赏金/仙盟周环任务中途进入副本/活动场景后，从场景出来保持原来的任务状态继续做任务
------------------------------------------------

-- 缓存普通场景的挂机状态和自动做任务状态
function M:CacheCommonSceneGuajiTypeAndAutoTask()
	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId()
	if scene_type == SceneType.Common and GuajiWGCtrl.Instance:GetAutoGuajiScene()[scene_id] then
		self.common_scene_task_type = TaskGuide.Instance:CurrTaskType()
		self.common_scene_can_auto_all_task = TaskGuide.Instance:CanAutoAllTask()
		self.common_scene_guaji_type = GuajiCache.guaji_type
	end
end

-- 恢复普通场景的挂机状态和自动做任务状态
function M:ResumeCommonSceneGuajiTypeAndAutoTask()
	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId()
	if scene_type == SceneType.Common and GuajiWGCtrl.Instance:GetAutoGuajiScene()[scene_id] then
		if self.common_scene_task_type then
			TaskGuide.Instance:CurrTaskType(self.common_scene_task_type)
		end
		if self.common_scene_can_auto_all_task then
			TaskGuide.Instance:CanAutoAllTask(self.common_scene_can_auto_all_task)
		end
		if self.common_scene_guaji_type then
			GuajiWGCtrl.Instance:SetGuajiType(self.common_scene_guaji_type)
		end
		self:ClearCommonSceneGuajiTypeAndAutoTask()
	end
end

-- 清理普通场景的挂机状态和自动做任务状态的缓存
function M:ClearCommonSceneGuajiTypeAndAutoTask()
	self.common_scene_task_type = nil
	self.common_scene_can_auto_all_task = nil
	self.common_scene_guaji_type = nil
end
---------------------end------------------------