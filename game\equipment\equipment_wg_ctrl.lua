require("game/equipment/equipment_wg_data")
require("game/equipment/equipment_items")
require("game/equipment/equipment_view")
require("game/equipment/baoshi/equipment_select_baoshi_view")
require("game/equipment/baoshi/equipment_baoshi_view")
require("game/equipment/baoshi/equipment_baoshi_jinglian_view")
require("game/equipment/baoshi/equipment_shengpin_view")
require("game/equipment/baoshi/equipmeng_bsxq_overview")
require("game/equipment/baoshi/equipment_bsjl_overview")

require("game/equipment/strength/equipment_strength_view")
require("game/equipment/strength/equipment_strength_overview")
require("game/equipment/strength/equipment_strength_wg_data")

require("game/equipment/hecheng/equipment_hecheng_uplevel_bag")
require("game/equipment/hecheng/equipment_hecheng_wg_data")
require("game/equipment/equipmentsuit/equipment_suit_view")
require("game/equipment/equipmentsuit/equipment_suit_tip_view")
require("game/equipment/equipmentsuit/equipment_suit_wg_data")
require("game/equipment/baoshi/equipment_baoshi_wg_data")
require("game/equipment/equipbaptize/equip_baptize_view")
require("game/equipment/equipbaptize/equip_baptize_up_grade_view")
require("game/equipment/equipbaptize/equip_baptize_wg_data")
require("game/equipment/yinji/new_yinji_jicheng_wg_data")
require("game/shenshou/shenshou_cell")
require("game/equipment/equipbaptize/equip_baptize_additon_view")
require("game/equipment/equipbaptize/equip_baptize_overview")
require("game/equipment/baoshi/equipment_baoshi_upgrade_view")
require("game/equipment/equipmentsuit/equipment_suit_preview_view")
require("game/equipment/equipmentsuit/equipment_suit_overview")

--灵玉--
require("game/equipment/lingyu/equipment_lingyu_view")
require("game/equipment/lingyu/equipment_lingyu_wg_data")
require("game/equipment/lingyu/equipment_select_lingyu_view")
require("game/equipment/lingyu/equipment_lingyu_upgrade_view")
require("game/equipment/lingyu/equipment_lingyu_overview")

require("game/equipment/equipmentsuit/equipment_suit_zonglan_view")
require("game/equipment/yinji/equipment_mark_view")

-- 转化
require("game/equipment/transsex/equipment_transsex_view")
require("game/equipment/transsex/equipment_transsex_wg_data")
require("game/equipment/transsex/equipment_transsex_wg_ctrl")

-- 御灵
require("game/equipment/yuling/equipment_yuling_set_view")
require("game/equipment/yuling/equipment_yuling_strength_view")
require("game/equipment/yuling/equipment_yuling_wg_ctrl")
require("game/equipment/yuling/equipment_yuling_wg_data")

--铸神
require("game/equipment/zhushen/equipment_zhushen_view")
require("game/equipment/zhushen/equipment_zhushentai_view")
require("game/equipment/zhushen/equipment_zhushen_skill_tips")
require("game/equipment/zhushen/equipment_zhushen_wg_ctrl")
require("game/equipment/zhushen/equipment_zhushen_wg_data")

-- 装备
EquipmentWGCtrl = EquipmentWGCtrl or BaseClass(BaseWGCtrl)

function EquipmentWGCtrl:__init()
	if nil ~= EquipmentWGCtrl.Instance then
		ErrorLog("[EquipmentWGCtrl] attempt to create singleton twice!")
		return
	end
	EquipmentWGCtrl.Instance = self

	self.view = EquipmentView.New(GuideModuleName.Equipment)
	self.equipment_mark_view = EquipmentMarkView.New(GuideModuleName.EquipmentMarkView)
	self.data = EquipmentWGData.New()
	self.equip_yinji_data = NewYinJiJiChengWGData.New()

	self.suit_preview_view = SuitPreviewView.New()
	self.shengpin_suit_attr_view = ShengPinAttrTip.New(GuideModuleName.EquipmentShenPinAttrTip)
	self.select_baoshi_view = EquipmentSelectBaoShiView.New(GuideModuleName.EquipmentSelectBaoShi)
	self.hecheng_uplevel_bag_view = EquipmentUpLevelBagView.New(GuideModuleName.EquipmentUpLevelBagView)
	self.baptize_addition_view = BaptizeAdditonView.New(GuideModuleName.BaptizeAdditonView)

	self.equipment_suit_tip_view = EquipmentSuitTipView.New(GuideModuleName.EquipmentSuitTipView)
	self.baoshi_upgrade_panel = EquipmentBaoShiUpgradeView.New()

	self.strength_overview_view = EquipmentStrengthOverview.New()
	self.baptize_overview_view = EquipmentBaptizeOverview.New()
	self.equip_suit_overview = EquipmentSuitOverview.New()
	self.bsxq_overview = EquipmentBSXQOverview.New()
	self.lingyu_overview = EquipmentLingYuOverview.New()
	self.bsjl_overview = EquipmentBSJLOverview.New()

	self.baptize_up_grade_view = EquipBaptizeUpGradeView.New()

	self.equipment_lingyu_data = EquipmentLingYuWGData.New()
	self.select_lingyu_view = EquipmentSelectLingYuView.New(GuideModuleName.EquipmentSelectLingYu)
	self.lingyu_upgrade_view = EquipmentLingYuUpgradeView.New(GuideModuleName.EquipmentLingYuUpgradeView)
	self.equipment_suit_zonglan_view = EquipmentSuitZongLanView.New(GuideModuleName.EquipmentSuitZongLanView)
	self.take_off_alert_tips = Alert.New(nil, nil, nil, nil, true)
	self:RegisterAllProtocols()
	self:RegisterProtocel()
	self:InitImperialSpiritCtrl()
	self:InitZhuShiCtrl()
end

function EquipmentWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.shengpin_suit_attr_view then
		self.shengpin_suit_attr_view:DeleteMe()
		self.shengpin_suit_attr_view = nil
	end

	if self.suit_preview_view then
		self.suit_preview_view:DeleteMe()
		self.suit_preview_view = nil
	end

	self.take_off_alert_tips:DeleteMe()
	self.take_off_alert_tips = nil

	if nil ~= self.select_baoshi_view then
		self.select_baoshi_view:DeleteMe()
		self.select_baoshi_view = nil
	end

	if nil ~= self.hecheng_uplevel_bag_view then
		self.hecheng_uplevel_bag_view:DeleteMe()
		self.hecheng_uplevel_bag_view = nil
	end

	if nil ~= self.equipment_suit_tip_view then
		self.equipment_suit_tip_view:DeleteMe()
		self.equipment_suit_tip_view = nil
	end

	if self.baptize_addition_view then
		self.baptize_addition_view:DeleteMe()
		self.baptize_addition_view = nil
	end

	if self.baoshi_upgrade_panel then
		self.baoshi_upgrade_panel:DeleteMe()
		self.baoshi_upgrade_panel = nil
	end

	if self.select_lingyu_view then
		self.select_lingyu_view:DeleteMe()
		self.select_lingyu_view = nil
	end


	if self.lingyu_upgrade_view then
		self.lingyu_upgrade_view:DeleteMe()
		self.lingyu_upgrade_view = nil
	end

	if self.equipment_suit_zonglan_view then
		self.equipment_suit_zonglan_view:DeleteMe()
		self.equipment_suit_zonglan_view = nil
	end

	if self.equipment_lingyu_data then
		self.equipment_lingyu_data:DeleteMe()
		self.equipment_lingyu_data = nil
	end

	if self.strength_overview_view then
		self.strength_overview_view:DeleteMe()
		self.strength_overview_view = nil
	end

	if self.equip_yinji_data then
		self.equip_yinji_data:DeleteMe()
		self.equip_yinji_data = nil
	end

	if self.baptize_overview_view then
		self.baptize_overview_view:DeleteMe()
		self.baptize_overview_view = nil
	end

	if self.equip_suit_overview then
		self.equip_suit_overview:DeleteMe()
		self.equip_suit_overview = nil
	end

	if self.bsxq_overview then
		self.bsxq_overview:DeleteMe()
		self.bsxq_overview = nil
	end

	if self.lingyu_overview then
		self.lingyu_overview:DeleteMe()
		self.lingyu_overview = nil
	end

	if self.bsjl_overview then
		self.bsjl_overview:DeleteMe()
		self.bsjl_overview = nil
	end

	if self.baptize_up_grade_view then
		self.baptize_up_grade_view:DeleteMe()
		self.baptize_up_grade_view = nil
	end

	if self.role_data_change_callback then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
		self.role_data_change_callback = nil
	end

	self.first_baoshi = nil
	self:DeleteBaoShiUpGradeAlertTips()
	self:DeleteLingShiUpGradeAlertTips()
	self:DeleteImperialSpiritCtrl()
	self:DeleteZhuShiCtrl()
	EquipmentWGCtrl.Instance = nil
end

-- 注册协议
function EquipmentWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCStoneInfo, "OnStoneInfo")
	self:RegisterProtocol(SCStoneBaseInfo, "OnStoneBaseInfo")
	self:RegisterProtocol(SCStoneUpdate, "OnStoneUpdate")
	self:RegisterProtocol(SCEquipForgeLegendAck, "OnEquipForgeLegendAck")
	self:RegisterProtocol(SCEquipGridStrAllInfo, "OnSCEquipGridStrAllInfo")
	self:RegisterProtocol(SCSuitAllInfo, "OnSCSuitAllInfo")

	self:RegisterProtocol(SCEquipBaptizeAllInfo, "OnEquipBaptizeAllInfo") -- 7222 洗炼全部信息
	self:RegisterProtocol(SCEquipBaptizeOneInfo, "OnEquipBaptizeOneInfo") -- 16385 洗炼单个信息

	self:RegisterProtocol(CSEquipForgeOperate)
	self:RegisterProtocol(CSStoneOperate)
	self:RegisterProtocol(CSEquipGridStrOperaReq)
	self:RegisterProtocol(CSSuitOperaReq)
	self:RegisterProtocol(CSEquipComposeOperaReq)
	self:RegisterProtocol(CSEquipBaptizeOperaReq)
	self:RegisterProtocol(CSSpecialEquipUpGradeReq)
	self:RegisterProtocol(CSEquipComposeReq)
	self:RegisterProtocol(CSEquipBatchComposeReq)
	self:RegisterProtocol(CSStoneOneKeyInlay)

	--装备升品
	self:RegisterProtocol(CSEquipShengPinReq) 									--8450 客户端请求操作
	self:RegisterProtocol(SCEquipShengPinInfo, "OnSCEquipShengPinInfo")			--8451 装备升品信息
	self:RegisterProtocol(SCEquipShengPinLevelUp, "OnSCEquipShengPinLevelUp")	--8452 升级成功返回
	self:RegisterProtocol(SCEquipUpQualityInfo, "OnSCEquipUpQualityInfo")		--1722 装备升品星数信息

	--装备印记
	self:RegisterProtocol(CSEquipYinJiOperReq) 												-- 10457 装备印记客户端请求
	self:RegisterProtocol(SCEquipYinJiInfo, "OnSCEquipYinJiInfo")							-- 10458 装备印刻信息
	self:RegisterProtocol(SCEquipYinJiPut, "OnSCEquipYinJiPut")								-- 10459 刻印符镶嵌成功返回
	self:RegisterProtocol(SCEquipYinJiAct, "OnSCEquipYinJiAct")								-- 10460 印记激活成功返回
	self:RegisterProtocol(SCEquipYinJiChangeEquip, "OnSCEquipYinJiChangeEquip")				-- 10461 部位更换装备
	self:RegisterProtocol(SCEquipYinJiKeYinFuUpLevel, "OnSCEquipYinJiKeYinFuUpLevel")		-- 10462 刻印符升级(合成)成功

	--灵玉
	self:RegisterProtocol(CSLingYuOperate)
	self:RegisterProtocol(CSLingYuOneKeyInlay)
	self:RegisterProtocol(SCLingYuInfo,"OnSCLingYuInfo")
	self:RegisterProtocol(SCLingYuBaseInfo,"OnSCLingYuBaseInfo")
	self:RegisterProtocol(SCLingYuUpdate,"OnSCLingYuUpdate")

	self.role_data_change_callback = BindTool.Bind1(self.OnRoleDataChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"coin"})
end

function EquipmentWGCtrl:GetView()
	return self.view
end

--宝石操作
function EquipmentWGCtrl:SendStoneOperate(operate, param1, param2, param3, param4, param5)
	local protocol = ProtocolPool.Instance:GetProtocol(CSStoneOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol.param5 = param5 or 0
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:SendStoneOneKeyInlay(item_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSStoneOneKeyInlay)
	protocol.item_list = item_list or {}
	protocol:EncodeAndSend()
end


-- 宝石信息
function EquipmentWGCtrl:OnStoneInfo(protocol)
	self.data:SetStoneInfo(protocol)

	RemindManager.Instance:Fire(RemindName.Equipment_Stone_Inlay)
	RemindManager.Instance:Fire(RemindName.Equipment_Stone_Refine)
    GlobalEventSystem:Fire(OtherEventType.StrangerStarLevel)
    RemindManager.Instance:Fire(RemindName.Equipment_Stone_Active)

	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_baoshi)
		self.view:Flush(TabIndex.equipment_baoshi_jl)
	end

	if self.select_baoshi_view:IsOpen() then
		self.select_baoshi_view:Flush()
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:OnStoneBaseInfo(protocol)
	self.data:OnStoneBaseInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_Stone_Inlay)
	RemindManager.Instance:Fire(RemindName.Equipment_Stone_Refine)
    GlobalEventSystem:Fire(OtherEventType.StrangerStarLevel)
    RemindManager.Instance:Fire(RemindName.Equipment_Stone_Active)

	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_baoshi)
		self.view:Flush(TabIndex.equipment_baoshi_jl)
	end

	if self.select_baoshi_view:IsOpen() then
		self.select_baoshi_view:Flush()
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:OnStoneUpdate(protocol)
	self.data:UpdateStoneInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_Stone_Inlay)
	RemindManager.Instance:Fire(RemindName.Equipment_Stone_Refine)
    GlobalEventSystem:Fire(OtherEventType.StrangerStarLevel)
    RemindManager.Instance:Fire(RemindName.Equipment_Stone_Active)

	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_baoshi)
		self.view:Flush(TabIndex.equipment_baoshi_jl)
	end

	if self.select_baoshi_view:IsOpen() then
		self.select_baoshi_view:Flush()
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

-- 煅造操作
function EquipmentWGCtrl:SendEquipForgeOperate(operate_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipForgeOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1
	protocol.param2 = param2
	protocol.param3 = param3
	protocol.param4 = param4
	protocol:EncodeAndSend()
end


function EquipmentWGCtrl:SendEquipmentSuitReq(opera_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSuitOperaReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:OnEquipForgeLegendAck(protocol)
	local data = EquipWGData.Instance:GetGridData(protocol.equip_index)
	if data then
		FunctionGuide.Instance:OpenAutoMatic(data.item_id, protocol.equip_index, false)
	end
end

function EquipmentWGCtrl:Open(tab_index, param_t)
	if param_t and "use_legend" == param_t.to_ui_name and nil ~= param_t.to_ui_param then
		self:SendEquipForgeOperate(EQUIPMENT_OPERA_TYPE.LEGEND, param_t.to_ui_param)
		return
	end

	self.view:Open(tab_index)
end

function EquipmentWGCtrl:OpenShengPinSuitAttr(view_type)
	if self.shengpin_suit_attr_view then
		self.shengpin_suit_attr_view:SetViewType(view_type)
	end
	ViewManager.Instance:Open(GuideModuleName.EquipmentShenPinAttrTip)
end

function EquipmentWGCtrl:Flush(index, key, value)
	if self.view:IsOpen() then
		self.view:Flush(index, key, value)
	end
end

function EquipmentWGCtrl:SelectBaoShiOpen(list, equip_index, slot_index)
	self.select_baoshi_view:SetData(list, equip_index, slot_index)
	ViewManager.Instance:Open(GuideModuleName.EquipmentSelectBaoShi)
end

function EquipmentWGCtrl:SetSelectYinJiFuEquipData(equip_data)
	if self.view:IsOpen() then
		self.view:SetYinJiJiChengFuEquipCellData(equip_data)
	end
end

function EquipmentWGCtrl:HeChengUpLevelBagOpen(call_back, select_item_data)
	self.hecheng_uplevel_bag_view:SetHeChengData(call_back, select_item_data)
	ViewManager.Instance:Open(GuideModuleName.EquipmentUpLevelBagView)
end

function EquipmentWGCtrl:OpenSuitPreviewView()
	EquipmentWGData.Instance:CountSuitPartStuffNum()
	self.suit_preview_view:Open()
end

function EquipmentWGCtrl:OpenSuitTip(equip_part, content_type, ok_func)
	self.equipment_suit_tip_view:SetCurEquipPart(equip_part, content_type, ok_func)
	ViewManager.Instance:Open(GuideModuleName.EquipmentSuitTipView)
end

function EquipmentWGCtrl:HeChengUpLevelBagFlush()
	self.hecheng_uplevel_bag_view:Flush()
end

--自动进阶---------------------------------------------------------------------------------
-- 装备强化进阶
function EquipmentWGCtrl:SendEquipStrengthGrade(param_1, param_2, is_auto_uplevel, repeat_times)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipGridStrOperaReq)
	protocol.opera_type = EQUIP_GRID_STRENGTH_OPERA_TYPE.EQUIP_GRID_STRENGTH_OPERA_TYPE_STRENGTH
	protocol.param_1 = param_1
	protocol.param_2 = param_2
	protocol.is_auto_uplevel = is_auto_uplevel
	protocol.repeat_times = repeat_times or 1
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:SendExtraAttrActive(opera_type, param1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipGridStrOperaReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param1
	protocol.param_2 = param_2
	protocol.is_auto_uplevel = 0
	protocol.repeat_times = 0
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:OnEquipStrengthResult(result)
	if 0 == result then
		-- self.view:FlushEquipStrength()
		self.view:Flush(0, "OnEquipStrengthResult")
	elseif 1 == result then
		self.view:ShowStuffTips()
	end
end

--装备印记继承返回结果
function EquipmentWGCtrl:OnEquipJiChengResult(result)
	if result == 1 then
		self.equipment_mark_view:Flush("equip_jicheng_ok")
	end
end

function EquipmentWGCtrl:OnEquipBaoShiJLResult(result)
	if not self.view:IsOpen() then
		return
	end

	self.view:ShowBaoshiJLEffect(result)
end
---------------------------------------------------------------------------------------------

function EquipmentWGCtrl:OnSCEquipGridStrAllInfo(protocol)
	self.data:SetEquipGridStrAllInfo(protocol)
	self.view:Flush(TabIndex.equipment_strength)

	RoleWGCtrl.Instance:FlushEquipAttr()

    RemindManager.Instance:Fire(RemindName.Equipment_Strength)
    HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:OnSCSuitAllInfo(protocol)
	self.data:SetSuitAllInfo(protocol)
	self.view:Flush(TabIndex.equipment_suit)
	-- self.view:Flush(TabIndex.equipment_suit_zhumo)
	-- self.view:Flush(TabIndex.equipment_suit_one)
	-- self.view:Flush(TabIndex.equipment_suit_two)
	self.view:Flush(TabIndex.equipment_suit)
	self.view:Flush(TabIndex.equipment_shengpin)
	self.view:Flush(TabIndex.equipment_strength)
	RoleBagWGCtrl.Instance:Flush(nil, "guard_change")

	RemindManager.Instance:Fire(RemindName.Equipment_Suit)
	-- RemindManager.Instance:Fire(RemindName.Equipment_Suit_ZhuXian)
	-- RemindManager.Instance:Fire(RemindName.Equipment_Suit_ZhuShen)
	-- RemindManager.Instance:Fire(RemindName.Equipment_Suit_ZhuMo)
	RemindManager.Instance:Fire(RemindName.Equipment_ShengPin)
	RemindManager.Instance:Fire(RemindName.Equipment_Strength)
	GlobalEventSystem:Fire(OtherEventType.StrangerStarLevel)
    GlobalEventSystem:Fire(OtherEventType.ShengPingAvtiveLevel)
    GlobalEventSystem:Fire(OtherEventType.RoleStrangerStarLevel)
	-- RemindManager.Instance:Fire(RemindName.Equipment_Level_Active)
	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:SendEquipComposeOperaReq(opera_type, compose_id, best_attr_num, item_index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipComposeOperaReq) --7219协议
	protocol.opera_type = opera_type
	protocol.compose_id = compose_id					--目标装备Id
	protocol.best_attr_num = best_attr_num				--目标装备的星星数
	protocol.param_1 = item_index_list[1] or -1				--0:背包索引
	protocol.param_2 = item_index_list[2] or -1				--1:背包索引
	protocol.param_3 = item_index_list[3] or -1				--2:背包索引
	protocol.param_4 = item_index_list[4] or -1				--3:背包索引
	protocol.param_5 = item_index_list[5] or -1				--4:背包索引
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:SendNewEquipComposeReq(target_id, star_num, equip_list)
	-- print_error("----6852协议---", target_id, star_num, equip_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipComposeReq) --6852协议
	protocol.target_id = target_id or 0
	protocol.star_num = star_num or 0
	protocol.equip_list = equip_list or {}
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:SendNewEquipBatchComposeReq(target_id, star_num, compose_num, sign_stuff_count, equip_list)
	--print_error("----6873---", target_id, star_num, compose_num, sign_stuff_count, equip_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipBatchComposeReq) --6852协议
	protocol.target_id = target_id or 0
	protocol.star_num = star_num or 0
	protocol.compose_num = compose_num or 0
	protocol.sign_stuff_count = sign_stuff_count or 0
	protocol.equip_list = equip_list or {}
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:SuitForgeCallBack(result)
	if result == 1 and self.view:IsOpen() then
		self.view:ShowSuitForgeSuceeEffect()
	end
end

function EquipmentWGCtrl:SendEquipXianPinActiveReq(opera_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipComposeOperaReq) --7219协议
	protocol.opera_type = opera_type
	protocol.compose_id = 0
	protocol.best_attr_num = 0
	protocol.param_1 = param1 or -1
	protocol.param_2 = -1
	protocol.param_3 = -1
	protocol.param_4 = -1
	protocol.param_5 = -1
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:OnEquipBaptizeAllInfo(protocol)
	self.data:SetEquipBaptizeAllInfo(protocol)
	self.view:Flush(TabIndex.equipment_xilian)
	RemindManager.Instance:Fire(RemindName.Equipment_Baptize)
	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:OnEquipBaptizeOneInfo(protocol)
	self.data:SetEquipBaptizeOneInfo(protocol)
	self.view:Flush(TabIndex.equipment_xilian)
	RemindManager.Instance:Fire(RemindName.Equipment_Baptize)
	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:FlushBaptizeEffect(protocol)
	self.view:Flush(TabIndex.equipment_xilian, "baptize_succe")
end

function EquipmentWGCtrl:FlushBaptizeUpgradeEffect(protocol)
	if self.baptize_up_grade_view then
		local wear_data = EquipWGData.Instance:GetGridData(protocol.param1)

		if not IsEmptyTable(wear_data) then
			self.baptize_up_grade_view:SetDataAndOpen(wear_data)
		end
	end

	self.view:Flush(TabIndex.equipment_xilian, "baptize_upgrade_succe")
end

function EquipmentWGCtrl:SendEquipBaptizeOperaReq(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipBaptizeOperaReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:SendEquipUpGradeReq(grid_index, is_bag_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSpecialEquipUpGradeReq)
	protocol.grid_index = grid_index
	protocol.is_bag_index = is_bag_index
	protocol:EncodeAndSend()
end

--升品
function EquipmentWGCtrl:SendEquipShengPinOperator(req_type, equip_part)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipShengPinReq)
	protocol.req_type = req_type or EQUIP_SHENGPING_OPERATOR.EQUIP_SHENGPING_OPERA_TYPE_INFO
	protocol.equip_part = equip_part or -1
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:OnSCEquipShengPinInfo(protocol)
	self.data:SetEquipShengPinInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_ShengPin)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_shengpin)
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:OnSCEquipShengPinLevelUp(protocol)
	self.data:SetEquipShengPinInfo(protocol, protocol.equip_part)
	RemindManager.Instance:Fire(RemindName.Equipment_ShengPin)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_shengpin, "sp_success")
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:OnSCEquipUpQualityInfo(protocol)
	self.data:SetEquipShengPinStarLevelInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_ShengPin)
	HomesWGCtrl.Instance:FlushHomesView()
	-- if self.view:IsOpen() then
	-- 	self.view:Flush(TabIndex.equipment_shengpin)
	-- end
end

function EquipmentWGCtrl:FlushUpQualityEffect(result, equip_part, new_level)
	RemindManager.Instance:Fire(RemindName.Equipment_ShengPin)
	local cfg = self.data:GetNewEquipShengPinCfg(new_level, equip_part)
	local cur_level = cfg and cfg.quality_grade_star or 0
	local cur_quality = math.floor(cur_level / 10)
	local cur_star = cur_level % 10

	if self.view:IsOpen() then
		if result == 1 then
			local quality_id = 0
			local result_type = EQUIP_SHENGPING_RESULT.UP_STAR_SUCCESS
			if cur_quality > 0 and cur_star == 0 then
				result_type = EQUIP_SHENGPING_RESULT.UP_QUALITY_SUCCESS
				local last_cfg = self.data:GetNewEquipShengPinCfg(new_level - 1, equip_part)
				quality_id = last_cfg and last_cfg.star_color or 0
			end
			self.view:Flush(TabIndex.equipment_shengpin, "sp_do_effect", {result_list = {result_type = result_type, quality_id = quality_id}})
		else
			local result_type = EQUIP_SHENGPING_RESULT.UP_STAR_FAILURE
			if cur_star == 9 then
				result_type = EQUIP_SHENGPING_RESULT.UP_QUALITY_FAILURE
			end
			self.view:Flush(TabIndex.equipment_shengpin, "sp_do_effect", {result_list = {result_type = result_type, quality_id = 0}})
		end
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:SetDataAndIOpenBaptizeAddView(data)
	self.baptize_addition_view:SetDataAndOpen(data)
end

function EquipmentWGCtrl:EquipBaptizeResultCallBack(protocol)
	-- 参数1: equip_part * 100 + target_quality_id
	-- 参数2: cost_item_num * 10000 + times;
	-- 参数3: item_id 

	local times = math.floor(protocol.param2 % 10000)  -- 总次数
	local cost_item_num = math.floor(protocol.param2 / 10000) --消耗数
	local item_name = ItemWGData.Instance:GetItemName(protocol.param3)
	local cost_num = protocol.param2 % 1000 * times

	local ok_func = function()
		
	end

	-- TipWGCtrl.Instance:OpenConfirmAlertTips(string.format(Language.Equip.BaptizeOneKeyResultStr, cost_num, item_name, times))
	TipWGCtrl.Instance:OpenCheckTodayAlertTips(string.format(Language.Equip.BaptizeOneKeyResultStr, cost_item_num, item_name, times), ok_func, "EquipBaptizeResultCallBack", Language.Equip.BaptizeOneKeyResultNoRemind)
end

---------------------------------------------------宝石---------------------------------------------------
function EquipmentWGCtrl:StoneInlayCallBack(result, equip_part, slot_index)
	if result == 1 then
		if self.view:IsOpen() then
			self.view:ShowBaoshiInlayEffect(equip_part, slot_index)
		end
	end
end

function EquipmentWGCtrl:BaoShiUpGradeResult(result)
	if result == 1 then
		if self.view:IsOpen() then
			self.view:PlayerStoneUpgradeEffect()
		end
	end
end

function EquipmentWGCtrl:BaoShiUpGradeOpen(index, select_baoshi_index)
	self.baoshi_upgrade_panel:SetData(index, select_baoshi_index)
	self.baoshi_upgrade_panel:Open()
end
----------------------------------------------------------------------------------------------------------

------------------------------------------------装备印记---------------------------------------------------------------

-- 10457 装备印记客户端请求
function EquipmentWGCtrl:CSEquipYinJiOperReq(req_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipYinJiOperReq)
	protocol.req_type = req_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 10458 装备印刻信息
function EquipmentWGCtrl:OnSCEquipYinJiInfo(protocol)
	self.equip_yinji_data:SetSCEquipYinJiInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.EquipmentMarkView)
	RemindManager.Instance:Fire(RemindName.Equipment_NewYinJi)
	HomesWGCtrl.Instance:FlushHomesView()
end

-- 10459 刻印符镶嵌成
function EquipmentWGCtrl:OnSCEquipYinJiPut(protocol)
	self.equip_yinji_data:SetSCEquipYinJiPut(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.EquipmentMarkView, nil, "keyin_success")
	RemindManager.Instance:Fire(RemindName.Equipment_NewYinJi)
	RoleBagWGCtrl.Instance:Flush(TabIndex.rolebag_bag_all, "guard_change")
	HomesWGCtrl.Instance:FlushHomesView()
end

-- 10460 印记激活成功
function EquipmentWGCtrl:OnSCEquipYinJiAct(protocol)
	self.equip_yinji_data:SetSCEquipYinJiAct(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.EquipmentMarkView, nil, "yinji_active_success")
	RemindManager.Instance:Fire(RemindName.Equipment_NewYinJi)
	RoleBagWGCtrl.Instance:Flush(TabIndex.rolebag_bag_all, "guard_change")
	HomesWGCtrl.Instance:FlushHomesView()
end

-- 10461 部位更换装备
function EquipmentWGCtrl:OnSCEquipYinJiChangeEquip(protocol)
	self.equip_yinji_data:SetSCEquipYinJiChangeEquip(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.EquipmentMarkView)
	RemindManager.Instance:Fire(RemindName.Equipment_NewYinJi)
	HomesWGCtrl.Instance:FlushHomesView()
end

-- 10462 刻印符升级成功
function EquipmentWGCtrl:OnSCEquipYinJiKeYinFuUpLevel(protocol)
	self.equip_yinji_data:SetSCEquipYinJiKeYinFuUpLevel(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.EquipmentMarkView, nil, "keyinfu_uplevel_success")
	RemindManager.Instance:Fire(RemindName.Equipment_NewYinJi)
	RoleBagWGCtrl.Instance:Flush(TabIndex.rolebag_bag_all, "guard_change")
	HomesWGCtrl.Instance:FlushHomesView()
end

------------------------------------------------灵玉---------------------------------------
--灵玉操作
function EquipmentWGCtrl:SendLingYuOperate(operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLingYuOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:SendLingYuOneKeyInlay(item_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLingYuOneKeyInlay)
	protocol.item_list = item_list or {}
	protocol:EncodeAndSend()
end

--灵玉信息
function EquipmentWGCtrl:OnSCLingYuInfo(protocol)
	self.equipment_lingyu_data:SetLingYuInfo(protocol)

	RemindManager.Instance:Fire(RemindName.Equipment_LingYu_Inlay)

	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_lingyu)
	end

	if self.select_lingyu_view:IsOpen() then
		self.select_lingyu_view:FlushEquipStrengthListDataSource()
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

--灵玉信息
function EquipmentWGCtrl:OnSCLingYuBaseInfo(protocol)
	self.equipment_lingyu_data:SetLingYuBaseInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_LingYu_Inlay)
	GlobalEventSystem:Fire(OtherEventType.StrangerStarLevel)
	
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_lingyu)
	end

	if self.select_lingyu_view:IsOpen() then
		self.select_lingyu_view:FlushEquipStrengthListDataSource()
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

--灵玉信息
function EquipmentWGCtrl:OnSCLingYuUpdate(protocol)
	self.equipment_lingyu_data:SetLingYuUpdate(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_LingYu_Inlay)
	GlobalEventSystem:Fire(OtherEventType.StrangerStarLevel)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.equipment_lingyu)
	end

	if self.select_lingyu_view:IsOpen() then
		self.select_lingyu_view:FlushEquipStrengthListDataSource()
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

function EquipmentWGCtrl:SelectLingYuOpen(list, equip_index, slot_index)
	self.select_lingyu_view:SetData(list, equip_index, slot_index)
	ViewManager.Instance:Open(GuideModuleName.EquipmentSelectLingYu)
end

function EquipmentWGCtrl:LingYuUpGradeOpen(index, select_baoshi_index)
	self.lingyu_upgrade_view:SetData(index, select_baoshi_index)
	self.lingyu_upgrade_view:Open()
end

function EquipmentWGCtrl:LingYuInlayResult(result, equip_part, slot_index)
	if result == 1 then
		if self.view:IsOpen() and self.view:IsLoadedIndex(TabIndex.equipment_lingyu) then
			self.view:ShowLingYuInlayEffect(equip_part, slot_index)
		end
	end
end

function EquipmentWGCtrl:LingYuUpGradeResult(result)
	if result == 1 then
		if self.view:IsOpen() and self.view:IsLoadedIndex(TabIndex.equipment_lingyu) then
			self.view:PlayerLingYuUpgradeEffect()
		end
	end
end

------------------------------------------------灵玉END---------------------------------------
function EquipmentWGCtrl:OpenTakeOffAlertTips(text_dec, ok_fun)
	self.take_off_alert_tips:SetLableString(text_dec)
	self.take_off_alert_tips:SetOkFunc(ok_fun)
	self.take_off_alert_tips:SetCheckBoxDefaultSelect(false)

	self.take_off_alert_tips:Open()
end

function EquipmentWGCtrl:InitBaoShiUpGradeAlertTips(call_back)
	if not self.bs_cost_prop_alert then
		local alert = Alert.New(nil, nil, nil, nil, true)
		alert:SetShowCheckBox(true, "baoshi_upgrade")
		alert:SetCheckBoxDefaultSelect(false)
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
		self.bs_cost_prop_alert = alert
	end

	if not self.bs_cost_gold_alert then
		local alert = Alert.New()
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		self.bs_cost_gold_alert = alert
	end
end

function EquipmentWGCtrl:DeleteBaoShiUpGradeAlertTips()
	if self.bs_cost_prop_alert then
		self.bs_cost_prop_alert:DeleteMe()
		self.bs_cost_prop_alert = nil
	end

	if self.bs_cost_gold_alert then
		self.bs_cost_gold_alert:DeleteMe()
		self.bs_cost_gold_alert = nil
	end
end

function EquipmentWGCtrl:OpenBaoShiUpGradeAlertTips(is_gold)
	if self.bs_cost_prop_alert and not is_gold then
		self.bs_cost_prop_alert:Open()
	end

	if self.bs_cost_gold_alert and is_gold then
		self.bs_cost_gold_alert:Open()
	end
end

function EquipmentWGCtrl:SetBaoShiUpGradeAlertTips(is_gold, str)
	if self.bs_cost_prop_alert and not is_gold then
		self.bs_cost_prop_alert:SetLableString(str)
	end

	if self.bs_cost_gold_alert and is_gold then
		self.bs_cost_gold_alert:SetLableString(str)
	end
end

function EquipmentWGCtrl:InitLingShiUpGradeAlertTips(call_back)
	if not self.ls_cost_prop_alert then
		local alert = Alert.New(nil, nil, nil, nil, true)
		alert:SetShowCheckBox(true, "xingshi_upgrade")
		alert:SetCheckBoxDefaultSelect(false)
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
		self.ls_cost_prop_alert = alert
	end

	if not self.ls_cost_gold_alert then
		local alert = Alert.New()
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		self.ls_cost_gold_alert = alert
	end
end

function EquipmentWGCtrl:DeleteLingShiUpGradeAlertTips()
	if self.ls_cost_prop_alert then
		self.ls_cost_prop_alert:DeleteMe()
		self.ls_cost_prop_alert = nil
	end

	if self.ls_cost_gold_alert then
		self.ls_cost_gold_alert:DeleteMe()
		self.ls_cost_gold_alert = nil
	end
end

function EquipmentWGCtrl:OpenLingShiUpGradeAlertTips(is_gold)
	if self.ls_cost_prop_alert and not is_gold then
		self.ls_cost_prop_alert:Open()
	end

	if self.ls_cost_gold_alert and is_gold then
		self.ls_cost_gold_alert:Open()
	end
end

function EquipmentWGCtrl:SetLingShiUpGradeAlertTips(is_gold, str)
	if self.ls_cost_prop_alert and not is_gold then
		self.ls_cost_prop_alert:SetLableString(str)
	end

	if self.ls_cost_gold_alert and is_gold then
		self.ls_cost_gold_alert:SetLableString(str)
	end
end

function EquipmentWGCtrl:FlushEquipmentMarkView()
	if self.equipment_mark_view and self.equipment_mark_view:IsOpen() then
		self.equipment_mark_view:Flush()
	end
end

function EquipmentWGCtrl:OpenStrengthOverviewView()
	if self.strength_overview_view then
		self.strength_overview_view:Open()
	end
end

function EquipmentWGCtrl:CloseStrengthOverviewView()
	if self.strength_overview_view and self.strength_overview_view:IsOpen() then
		self.strength_overview_view:Close()
	end
end

function EquipmentWGCtrl:OpenSuitZongLanView(data)
	if self.equipment_suit_zonglan_view then
		self.equipment_suit_zonglan_view:SetDataAndOpen(data)
	end
end

function EquipmentWGCtrl:OpenBaptizeOverviewView()
	if self.baptize_overview_view then
		self.baptize_overview_view:Open()
	end
end

function EquipmentWGCtrl:CloseBaptizeOverviewView()
	if self.baptize_overview_view and self.baptize_overview_view:IsOpen() then
		self.baptize_overview_view:Close()
	end
end

function EquipmentWGCtrl:OpenEquipSuitOverviewView()
	if self.equip_suit_overview then
		self.equip_suit_overview:Open()
	end
end

function EquipmentWGCtrl:CloseEquipSuitOverviewView()
	if self.equip_suit_overview and self.equip_suit_overview:IsOpen() then
		self.equip_suit_overview:Close()
	end
end

function EquipmentWGCtrl:OpenBSXQOverviewView()
	if self.bsxq_overview then
		self.bsxq_overview:Open()
	end
end

function EquipmentWGCtrl:CloseBSXQOverviewView()
	if self.bsxq_overview and self.bsxq_overview:IsOpen() then
		self.bsxq_overview:Close()
	end
end

function EquipmentWGCtrl:OpenLingYuOverviewView()
	if self.lingyu_overview then
		self.lingyu_overview:Open()
	end
end

function EquipmentWGCtrl:CloseLingYuOverviewView()
	if self.lingyu_overview and self.lingyu_overview:IsOpen() then
		self.lingyu_overview:Close()
	end
end

function EquipmentWGCtrl:OpenBSJLOverviewView()
	if self.bsjl_overview then
		self.bsjl_overview:Open()
	end
end

function EquipmentWGCtrl:CloseBSJLOverviewView()
	if self.bsjl_overview and self.bsjl_overview:IsOpen() then
		self.bsjl_overview:Close()
	end
end

function EquipmentWGCtrl:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "coin" then
		ViewManager.Instance:FlushView(GuideModuleName.Equipment, TabIndex.equipment_strength)
	end
end