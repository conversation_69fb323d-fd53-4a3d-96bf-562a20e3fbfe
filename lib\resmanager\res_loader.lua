-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

local TypeUnityTexture = typeof(UnityEngine.Texture)
local TypeUnitySprite = typeof(UnityEngine.Sprite)
local TypeUnityMaterial = typeof(UnityEngine.Material)
local TypeUnityPrefab = typeof(UnityEngine.GameObject)
local TypeAudioMixer = typeof(UnityEngine.Audio.AudioMixer)
local TypeActorQingGongObject = typeof(ActorQingGongObject)
local TypeQualityConfig = typeof(QualityConfig)
local TypeTextAsset = typeof(UnityEngine.TextAsset)
local TypeShaderVariant = typeof(UnityEngine.ShaderVariantCollection)
local TypeRuntimeAnimatorController = typeof(UnityEngine.RuntimeAnimatorController)
local TypeUnityAudioClip = typeof(UnityEngine.AudioClip)
local TypeOfVolumeProfile = typeof(UnityEngine.Rendering.VolumeProfile)
local TypeTMPSpriteAsset = typeof(TMPro.TMP_SpriteAsset)


local POOL_TYPE_TBL = {
    [TypeUnityTexture] = ResPoolMgr.GetTexture,
    [TypeUnitySprite] = ResPoolMgr.GetSprite,
    [TypeUnityMaterial] = ResPoolMgr.GetMaterial,
    [TypeUnityPrefab] = ResPoolMgr.GetPrefab,
    [TypeAudioMixer] = ResPoolMgr.GetAudioMixer,
    [TypeActorQingGongObject] = ResPoolMgr.GetQingGongObj,
    [TypeQualityConfig] = ResPoolMgr.GetQualityConfig,
    [TypeTextAsset] = ResPoolMgr.GetTextAsset,
    [TypeShaderVariant] = ResPoolMgr.GetShaderVariant,
    [TypeRuntimeAnimatorController] = ResPoolMgr.GetAnimatorController,
    [TypeUnityAudioClip] = ResPoolMgr.GetAudioClip,
    [TypeOfVolumeProfile] = ResPoolMgr.GetResInType,
    [TypeTMPSpriteAsset] = ResPoolMgr.GetTMPSpriteAsset,
}

local ResLoader = BaseClass()

function ResLoader:__init()
    self.is_deleted = false
    self.cur_t = nil
    self.wait_t = nil

    self.is_async = true
    self.load_priority = ResLoadPriority.high
    self.is_loading = false
end

function ResLoader:__delete()
    if not self.__is_had_del_in_cache then
        self.__is_had_del_in_cache = true
        if nil ~= self.__loader_key and nil ~= self.__loader_owner and nil ~= self.__loader_owner.__res_loaders then
            self.__loader_owner.__res_loaders[self.__loader_key] = nil
        end
    end

    self:Destroy()

    self.is_deleted = true

    if nil ~= self.wait_t then
        ResLoader.ReleaseCBData(self.wait_t)
        self.wait_t = nil
    end
end

function ResLoader:Destroy()
    if nil ~= self.cur_t then
        if nil ~= self.cur_t[5] then
            ResPoolMgr:Release(self.cur_t[5])
        end
        ResLoader.ReleaseCBData(self.cur_t)
        self.cur_t = nil
    end
end

function ResLoader:SetIsASyncLoad(is_async)
    self.is_async = is_async
end

function ResLoader:SetLoadPriority(load_priority)
    if nil ~= load_priority then
        self.load_priority = load_priority
    end
end


function ResLoader:Load(bundle_name, asset_name, asset_type, load_callback, cbdata)
    asset_type = asset_type or TypeUnityPrefab

    if nil == bundle_name or "" == bundle_name
        or nil == asset_name or "" == asset_name then
        return
    end

    if nil == POOL_TYPE_TBL[asset_type] then
        print_error("[ResLoader] load fail, not support asset_type", bundle_name, asset_name)
        return
    end

    -- 如果是跟上次加载的资源相同则不再进行请求加载，并且若资源已存在则直接回调处理
    if nil ~= self.cur_t
        and self.cur_t[2] == bundle_name
        and self.cur_t[3] == asset_name
        and self.cur_t[4] == asset_type then
        if load_callback then
            load_callback(self.cur_t[5], cbdata)
        end
        return
    end

    if is_develop then
        if not EditorResourceMgr.IsExistsAsset(bundle_name, asset_name) then
            print_error("加载不存在资源，马上检查!!!", bundle_name, asset_name)
            return
        end
    end

    -- 如果正在加载则等待
    if self.is_loading then
        self.wait_t = ResLoader.GetCBData()
        self.wait_t[2] = bundle_name
        self.wait_t[3] = asset_name
        self.wait_t[4] = asset_type
        self.wait_t[5] = load_callback
        self.wait_t[6] = cbdata
    else
        self:Destroy()
        self:DoLoad(bundle_name, asset_name, asset_type, load_callback, cbdata)
    end
end

function ResLoader:DoLoad(bundle_name, asset_name, asset_type, load_callback, up_cbdata)
    local cbdata = ResLoader.GetCBData()
    cbdata[1] = self
    cbdata[2] = bundle_name
    cbdata[3] = asset_name
    cbdata[4] = asset_type
    cbdata[5] = load_callback
    cbdata[6] = up_cbdata

    self.is_loading = true


    if asset_type == TypeOfVolumeProfile then
        ResPoolMgr.GetResInType(
                ResPoolMgr,
                bundle_name,
                asset_name,
                ResLoader.OnLoadComplete,
                self.is_async,
                asset_type,
                cbdata)
    else
        POOL_TYPE_TBL[asset_type](
                ResPoolMgr,
                bundle_name,
                asset_name,
                ResLoader.OnLoadComplete,
                self.is_async,
                cbdata,
                self.load_priority)
    end
end

function ResLoader.OnLoadComplete(res, cbdata)
    local self = cbdata[1]
    local bundle_name = cbdata[2]
    local asset_name = cbdata[3]
    local asset_type = cbdata[4]
    local load_callback = cbdata[5]
    local up_cbdata = cbdata[6]
    ResLoader.ReleaseCBData(cbdata)

    self.is_loading = false

    -- 如果加载器已被释放则释放当前加载完成的
    if self.is_deleted then
        if nil ~= res then
            ResPoolMgr:Release(res)
        end

        return
    end

    -- 如果是有等待加载的资源则释放当前加载的
    if nil ~= self.wait_t then
        if nil ~= res then
            ResPoolMgr:Release(res)
        end

        local t = self.wait_t
        self.wait_t = nil
        self:DoLoad(t[2], t[3], t[4], t[5], t[6])
        ResLoader.ReleaseCBData(t)
        return
    end

    if nil ~= self.cur_t then
        print_error("[ResLoader] OnLoadComplete big bug", bundle_name, asset_name)
    end

    self.cur_t = ResLoader.GetCBData()
    self.cur_t[2] = bundle_name
    self.cur_t[3] = asset_name
    self.cur_t[4] = asset_type
    self.cur_t[5] = res

    if nil ~= load_callback then
        load_callback(res, up_cbdata)
    end
end

ResLoader.cbdata_list = {}
function ResLoader.GetCBData()
    local cbdata = table.remove(ResLoader.cbdata_list)
    if nil == cbdata then
        cbdata = {true, true, true, true, true, true}
    end

    return cbdata
end

function ResLoader.ReleaseCBData(cbdata)
    cbdata[1] = true
    cbdata[5] = nil
    cbdata[6] = nil
    table.insert(ResLoader.cbdata_list, cbdata)
end

return ResLoader