
YinPiaoExchangeView = YinPiaoExchangeView or BaseClass(SafeBaseView)
function YinPiaoExchangeView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "yinpiao_exchange_view")
end

function YinPiaoExchangeView:__delete()

end


function YinPiaoExchangeView:ReleaseCallBack()
	if nil ~= self.exchange_list then
		self.exchange_list:DeleteMe()
		self.exchange_list = nil
	end
	self.exchange_info_list = nil
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Bag, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
end

function YinPiaoExchangeView:ShowIndexCallBack(index)
	self:Flush()
end

function YinPiaoExchangeView:LoadCallBack(index)
	self.node_list["title_view_name"].text.text = Language.Bag.YinPiaoTitle
	self:SetSecondView(Vector2(692, 568))

	if not self.exchange_list then
		self.exchange_list = PerfectLoversListView.New(YinPiaoExchangeRender, self.node_list["exchange_list"])
	end
	XUI.AddClickEventListener(self.node_list.btn_exchange,BindTool.Bind(self.OnClickExchange,self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Bag, self.get_guide_ui_event)
end

function YinPiaoExchangeView:OnFlush(param_t, index)
	local list_cfg = RoleBagWGData.Instance:GetYinPiaoExchangeCfg()
	self.exchange_list:SetDataList(list_cfg,3)

	self.exchange_info_list = {}
	self.can_exchange = false
	local exchange_total = 0
	-- 银票总数
	for i,v in ipairs(list_cfg) do
		local list_info = {}
		list_info.num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if not self.can_exchange and list_info.num > 0 then
			self.can_exchange = true
		end
		exchange_total = exchange_total + list_info.num*v.exchange_num
		table.insert(self.exchange_info_list,list_info.num)
	end

	self.node_list.exchange_total.text.text = exchange_total

	self.node_list.remind:SetActive(exchange_total >= 200)
	XUI.SetGraphicGrey(self.node_list.btn_exchange, not self.can_exchange)

end

function YinPiaoExchangeView:OnClickExchange()
	self:Close()
	if not self.can_exchange then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Bag.Exchange_Tip_1)
		return
	end
	
	-- FightWGCtrl.Instance:DoMoneyEffect(GameEnum.NEW_MONEY_BAR.SILVER_TICKET)
	-- MainuiWGCtrl.Instance:PlayEffectByRechargeSecond()

	BagWGCtrl.Instance:CSExchangeSilverTicketReq(self.exchange_info_list)
	GlobalEventSystem:Fire(OtherEventType.GET_BAG_YUANBAO)
end

function YinPiaoExchangeView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.GoldExchangeBtn then
		return self.node_list.btn_exchange,BindTool.Bind(self.OnClickExchange,self)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

YinPiaoExchangeRender = YinPiaoExchangeRender or BaseClass(BaseRender)

function YinPiaoExchangeRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function YinPiaoExchangeRender:LoadCallBack()
	self.item = ItemCell.New(self.node_list.item_pos)
	self.item:SetNeedItemGetWay(true)
end

function YinPiaoExchangeRender:OnFlush()
	local data = self.data
	if not data then return end

	self.item:SetData({item_id = data.item_id})
	self.node_list.exchange_num.text.text = data.exchange_num
	local num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
	self.node_list.has_num.text.text = string.format(Language.Bag.Exchange_Tip_2,num)

	XUI.SetGraphicGrey(self.node_list.group, num <= 0)
	self.node_list.bg:SetActive(num <= 0)
end