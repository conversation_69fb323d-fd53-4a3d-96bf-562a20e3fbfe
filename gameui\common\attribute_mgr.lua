AttributeMgr = AttributeMgr or {}

AttributeMgr.attrview_t =
	{{"hp", "max_hp"},												    --生命
	{"gongji", "gong_ji"}, 												--攻击
	{"gong_ji", "attack"}, 												--攻击
	{"fagongji", "fa_gong_ji"},  										--攻击
	{"fangyu", "fang_yu"}, 												--防御
	{"fafangyu", "fa_fang_yu"},  										--防御
	{"pojia", "po_jia"}, 												--破甲
	{"yuansu_shanghai","yuansu_shanghai"}, 								--元素伤害
	{"yuansu_hujia","yuansu_hujia"}, 									--元素护甲
	{"shengming_qq","shengming_qq"}, 									--生命窃取
	{"fangtan","fangtan"}, 												--反弹伤害
	{"fujiashanghai", "fujia_shanghai"}, 								--真实伤害
	{"shanghai_zs", "shanghai_zs"}, 								    --真实伤害
	{"fangyu_zs","fangyu_zs"}, 											--真实防御
	{"shengming_hf","shengming_hf"}, 									--生命回复
	{"gongji_sd","gongji_sd"}, 											--攻击速度
	{"shanghai_jn","shanghai_jn"}, 										--技能伤害
	{"shengming_jc_per","shengming_jc_per"}, 							--生命加成
	{"maxhp_jiacheng","per_maxhp_jiacheng"},							--生命加成
	{"permaxhp", "per_maxhp"},											--生命百分比
	{"gongji_jc_per","gongji_jc_per"},									--攻击加成
	{"pergongji", "per_gongji"},										--攻击百分比
	{"jianren", "jian_ren"},											--坚韧
	{"jianren_per", "jianren_per"},										--坚韧百分比
	{"gedang_chuantou_per","gedang_chuantou_per"},						--格挡穿透%
	{"chuantou_per","chuantou_per"},									--格挡穿透%
	{"perpofang", "per_pofang"},										--破防率
	{"pojia_jiacheng","per_pojia_jiacheng"},							--破甲加成
	{"wuxinggongji", "wuxing_gongji"},									--五行攻击
	{"wuxingfangyu", "wuxing_fangyu"}, 									--五行防御
	{"mingzhong", "ming_zhong"}, 										--命中
	{"shanbi", "shan_bi"}, 												--闪避
	{"baoji", "bao_ji"},												--暴击
	{"per_baoji_add","per_baoji_jiacheng"},								--暴击加成
	{"perbaoji", "per_baoji"}, 											--暴击率
	{"perkangbao", "per_kangbao"}, 										--抗暴率
	{"per_pojia","per_pojia"},{"per_fangyu","per_fangyu"},				--防御%
	{"shanghai_jiacheng_per","shanghai_jiacheng_per"},					--伤害加成%
	{"shanghai_jianshao_per","shanghai_jianshao_per"},					--伤害减少%
	{"shangh_jiacheng_per","shangh_jiacheng_per"},						--伤害加成%
	{"shanghai_zengjia","per_shanghai_add"},							--伤害增加%
	{"shanghai_jianshao","per_shanghai_reduce"},						--伤害减少%
	{"gedang_per", "gedang_per"},										--格挡%
	{"permianshang", "per_mianshang"}, 									--免伤率
	{"dikangshanghai", "dikang_shanghai"},								--抵抗伤害
	{"baoshang_jiacheng","per_baoshang_jiacheng"},						--暴击伤害 加成
	{"baoji_hurt_per","baoji_hurt_per"},								--暴击伤害%
	{"baoji_hurt_cut_per","baoji_hurt_cut_per"},						--暴击伤害减少率
	{"skill_shanghai_jiacheng_per","skill_shanghai_jiacheng_per"},		--技能伤害加成%
	{"skill_shanghai_jianshao_per","skill_shanghai_jianshao_per"},		--技能伤害减少%
	{"per_exp","per_exp"},												--经验加成%
	{"pvp_shanghai_jiacheng","per_pvp_reduce_hurt"},					--pvp减伤
	{"pvp_shanghai_jianmian","per_pvp_add_hurt"},						--pvp增伤
	{"pve_shanghai_jianmian","per_pve_reduce_hurt"},					--pve减伤
	{"pve_shanghai_jiacheng","per_pve_add_hurt"}						--pve增伤
}

AttributeMgr.PROF_ATTR_RATE = {
	{max_hp = 1.1103, gong_ji = 0.89, fang_yu = 1},
	{max_hp = 0.9662, gong_ji = 1.03, fang_yu = 1},
	{max_hp = 1.0356, gong_ji = 0.98, fang_yu = 1},
	{max_hp = 0.8832, gong_ji = 1.09, fang_yu = 1}
}

-- Attribute已经被搞成一个特大表，禁止直接通过该方法调用，请使用AttributePool.AllocAttribute()接口
function AttributeMgr.__Attribute()
	return {
		max_hp = 0,							-- 最大生命
		gong_ji = 0,						-- 攻击
		fang_yu = 0,						-- 防御
		fa_fang_yu = 0,						-- 法防
		po_jia = 0,							-- 破甲
		yuansu_shanghai = 0,				-- 元素伤害
		yuansu_hujia = 0,					-- 元素护甲
		shengming_qq = 0,					-- 生命窃取
		fangtan = 0,						-- 反弹伤害
		shanghai_zs = 0,					-- 真实伤害
		fujia = 0,							-- 附加
		fangyu_zs = 0,						-- 真实防御
		shengming_hf = 0,					-- 生命回复
		gongji_sd = 0 ,        				-- 攻击速度
		shanghai_jn = 0,					-- 技能伤害
		shengming_jc_per = 0,				-- 生命加成
		gongji_jc_per = 0,					-- 攻击加成
		fangyu_jc_per = 0,					-- 防御加成
		pojia_jc_per = 0,					-- 破甲加成
		yuansu_sh_jc_per = 0,				-- 元素伤害加成
		yuansu_hj_jc_per = 0,				-- 元素护甲加成
	    zhuagnbei_sm_jc_per = 0,			-- 装备血量加成
	    zhuagnbei_gj_jc_per = 0,			-- 装备攻击加成
	    zhuagnbei_fy_jc_per = 0,			-- 装备防御加成
	    zhuagnbei_pj_jc_per = 0,			-- 装备破甲加成
		shengming_qq_jc_per = 0,			-- 生命窃取加成
		shengming_qq_per =0,				-- 生命窃取加成
	    fangtan_per = 0,					-- 反弹伤害加成
		fangtan_jc_per = 0,					-- 反弹伤害加成
		shanghai_zs_jc_per = 0,				-- 真实伤害加成
		fangyu_zs_jc_per = 0,				-- 真实防御加成
		mingzhong_per = 0,					-- 命中率
		ming_zhong = 0,						-- 命中
		shan_bi = 0,						-- 闪避
		shanbi_per = 0,						-- 闪避率
		baoji_per = 0,						-- 暴击率
		baoji_jc_per = 0,					-- 暴击加成
		kangbao_per = 0,					-- 抗暴率
		kangbao_jc_per = 0,					-- 抗暴加成
		lianji_per = 0,						-- 连击率
		lianjikang_per = 0,					-- 连击抵抗
		jichuan_per = 0,					-- 击穿率
		jichuankang_per = 0,				-- 击穿抵抗
		dikang = 0,							-- 抵抗
		zengshang_boss_per = 0,				-- 首领增伤
		jianshang_boss_per = 0,				-- 首领减伤
		zengshang_guaiwu_per = 0,			-- 怪物增伤
		jianshang_guaiwu_per = 0,			-- 怪物减伤
		zengshang_per = 0,					-- 玩家增伤
		jianshang_per = 0,					-- 玩家减伤
		zengshang_bs_per = 0,				-- 变身增伤
		jianshang_bs_per = 0,				-- 变身减伤
		shanghai_quan_jc_per = 0,			-- 全属性伤害加成
		shanghai_quan_jm_per = 0,			-- 全属性伤害减免
		shanghai_jc_per = 0,				-- 伤害加成
		per_shanghaijiacheng = 0,			-- 伤害加成
		shanghai_jm_per = 0,				-- 伤害减免
		per_shanghaijianshao = 0,			-- 伤害减免加成
		mingzhong_yc_per = 0,				-- 异常状态命中
		dikang_yc_per = 0,					-- 异常状态抵抗
		zengshang_yc_per = 0,				-- 异常状态增伤
		zhiliaoxiaoguo_per = 0,				-- 治疗效果
		gedang_per = 0,						-- 格挡率
		podang_per = 0,						-- 破档率
		gedang_ms_per = 0,					-- 格挡免伤
		gedang_ms_my_per = 0 ,       		-- 格挡免伤免疫
		baoji_shanghai_per = 0,				-- 暴击伤害
		baoji_shanghai_jm_per = 0 ,       	-- 暴击伤害减免
		lianji_shanghai_per = 0 ,        	-- 连击伤害比例
		lianji_shanghai_jm_per = 0 ,        -- 连击伤害减免
		shengming_hf_per = 0,				-- 生命回复比例
		zengshang_gx_per = 0,				-- 高血增伤
		zengshang_xr_per = 0,				-- 虚弱增伤
	    yuansu_jk_per = 0 ,        			-- 元素减抗
	    yuansu_kx_per = 0 ,        			-- 元素抗性
	    jineng_shanghai_zj_per = 0 ,        -- 技能伤害增加
	    jineng_shanghai_jm_per = 0 ,        -- 技能伤害减免
	    huo_shanghai_zj_per = 0,			-- 离火伤害增加
	    boss_zhenshang = 0,					-- BOSS真伤
	    boss_palsy_per = 0,					-- BOSS麻痹
	    boss_seckill_per = 0,				-- BOSS秒杀
	    lei_shanghai_zj_per = 0,			-- 雷罚伤害增加
	    lei_shanghai_jm_per = 0,			-- 雷罚伤害减免
		move_speed_per = 0 ,       			-- 移动速度
		shaguai_jb_diaoluo_per = 0 ,        -- 杀怪金币掉落
	    zengshang_boss_dk_per = 0 ,       	-- 首领增伤抵抗
	    jianshang_boss_dk_per = 0 ,        	-- 首领减伤抵抗
	    zengshang_guaiwu_dk_per = 0 ,       -- 怪物增伤抵抗
	    jianshang_guaiwu_dk_per = 0 ,       -- 怪物减伤抵抗
	    baoji_hurt_cut_per = 0 , 			-- 暴击减伤
	    jian_ren = 0 ,						-- 坚韧
	    fujia_shanghai = 0 ,				-- 附加伤害
	    wuxing_gongji = 0 ,					-- 五行攻击
	    wuxing_fangyu = 0 ,					-- 五行防御
	    per_baoshang_jiacheng = 0 ,			-- 暴击伤害加成
	    per_pvp_add_hurt = 0 ,				-- pvp增伤加成
	    per_pvp_reduce_hurt = 0 ,			-- pvp减伤加成
	    huixinyiji_per = 0 ,				-- 会心一击加成
	    huixinyiji_kang_per = 0 ,			-- 会心一击抵抗
	    baoji_shanghai = 0,					-- 暴击伤害固定值
	    kangbao_shanghai = 0,				-- 抗暴伤害固定值
		shengming_zb_role_jc_per = 0,		-- 基础生命加成
		gongji_zb_role_jc_per = 0,			-- 基础攻击加成
		fangyu_zb_role_jc_per = 0,			-- 基础防御加成
		pojia_zb_role_jc_per = 0,			-- 基础破甲加成
		gongji_wuqi_jc_per = 0,				-- 武器攻击加成
		pojia_wuqi_jc_per = 0,				-- 武器破甲加成
		gongji_shiping_jc_per = 0,			-- 饰品攻击加成
		kill_monster_per_exp = 0,			-- 打怪经验加成
		rare_exterior_rate_per = 0,			--珍稀外观掉落加成
		rare_equip_rate_per = 0,			--珍稀装备掉落加成

		-- 非角色属性 别瞎几把加到这里
		passive_skill_attack = 0 ,			--被动技能攻击百分百技能战力系数
	    passive_skill_depence = 0 ,			--被动技能防御百分百技能战力系数
	    passive_skill_fix_cap = 0 ,			--被动技能固定战力系数
	    zhenyan_jc_per = 0,					--本阵眼属性
		equip_qianghua_per = 0,				--装备强化加成
		tianshen_att_per = 0,				--天神全属性加成
	}
end

-- 属性排序 和上面Attribute 一一对应
local sort_attribute = {
	'max_hp',							-- 最大生命
	'gong_ji' ,							-- 攻击
	'fang_yu' ,							-- 防御
	'fa_fang_yu',						-- 法防御
	'po_jia' ,							-- 破甲
	'ming_zhong',						-- 命中
	'shan_bi',							-- 闪避
	'jian_ren',							-- 坚韧
	'baoji_shanghai',					-- 暴击伤害固定值
	'kangbao_shanghai',					-- 抗暴伤害固定值
	'yuansu_shanghai' ,					-- 元素伤害
	'yuansu_hujia' ,					-- 元素护甲
	'shengming_qq' ,					-- 生命窃取
	'fangtan' ,							-- 反弹伤害
	'shanghai_zs' ,						-- 真实伤害
	'fujia' ,							-- 附加
	'fangyu_zs' ,						-- 真实防御
	'shengming_hf' ,					-- 生命回复
	'gongji_sd' ,        				-- 攻击速度
	'shanghai_jn' ,						-- 技能伤害
	'shengming_jc_per' ,				-- 生命加成
	'gongji_jc_per' ,					-- 攻击加成
	'fangyu_jc_per' ,					-- 防御加成
	'pojia_jc_per' ,					-- 破甲加成
	'shengming_hf_per' ,				-- 生命恢复比例
	'yuansu_sh_jc_per' ,				-- 元素伤害加成
	'yuansu_hj_jc_per' ,				-- 元素护甲加成
    'zhuagnbei_sm_jc_per' ,        		-- 装备血量加成
   	'zhuagnbei_gj_jc_per' ,        		-- 装备攻击加成
    'zhuagnbei_fy_jc_per' ,        		-- 装备防御加成
    'zhuagnbei_pj_jc_per' ,        		-- 装备破甲加成
    'shengming_qq_jc_per' ,				-- 生命窃取加成
    'shengming_qq_per' ,				-- 生命窃取加成
    'fangtan_per' ,						-- 反弹伤害加成
	'fangtan_jc_per' ,					-- 反弹伤害加成
	'shanghai_zs_jc_per' ,				-- 真实伤害加成
	'fangyu_zs_jc_per' ,				-- 真实防御加成
	'mingzhong_per' ,					-- 命中率
	'shanbi_per' ,						-- 闪避率
	'baoji_per' ,						-- 暴击率
	'baoji_jc_per',						-- 暴击加成
	'kangbao_per' ,						-- 抗暴率
	'kangbao_jc_per',					-- 抗暴加成
	'lianji_per' ,						-- 连击率
	'lianjikang_per' ,					-- 连击抵抗
	'jichuan_per' ,						-- 击穿率
	'jichuankang_per' ,					-- 击穿抵抗
	'zengshang_boss_per' ,				-- 首领增伤
	'jianshang_boss_per' ,				-- 首领减伤
	'zengshang_guaiwu_per' ,			-- 怪物增伤
	'jianshang_guaiwu_per' ,			-- 怪物减伤
	'zengshang_per' ,					-- 玩家增伤
	'jianshang_per' ,					-- 玩家减伤
	'zengshang_bs_per' ,				-- 变身增伤
	'jianshang_bs_per' ,				-- 变身减伤
	'shanghai_quan_jm_per' ,			-- 全属性伤害减免
	'shanghai_quan_jc_per' ,			-- 全属性伤害加成
	'shanghai_jc_per' ,					-- 伤害加成
	'per_shanghaijiacheng' ,			-- 伤害加成
	'shanghai_jm_per' ,					-- 伤害减免
	'per_shanghaijianshao' ,			-- 伤害减免加成
	'fujia_shanghai',					-- 附加伤害
	'mingzhong_yc_per' ,				-- 异常状态命中
	'dikang_yc_per' ,					-- 异常状态抵抗
	'zengshang_yc_per' ,				-- 异常状态增伤
	'zhiliaoxiaoguo_per' ,				-- 治疗效果
	'gedang_per' ,						-- 格挡率
	'podang_per' ,						-- 破档率
	'gedang_ms_per' ,					-- 格挡免伤
	'gedang_ms_my_per' ,        		-- 格挡免伤免疫
	'baoji_shanghai_per' ,				-- 暴击伤害
	'baoji_hurt_cut_per',				-- 暴击减伤
	'per_baoshang_jiacheng',			-- 暴击伤害加成
	'baoji_shanghai_jm_per' ,        	-- 暴击伤害减免
	'lianji_shanghai_per' ,       	 	-- 连击伤害比例
	'lianji_shanghai_jm_per' ,       	-- 连击伤害减免
	'shengming_hf_per' ,					-- 生命回复比例
	'wuxing_gongji',					-- 五行攻击
	'wuxing_fangyu',					-- 五行防御
	'zengshang_gx_per' ,				-- 高血增伤
	'zengshang_xr_per' ,				-- 虚弱增伤
	'yuansu_jk_per' ,        			-- 元素减抗
    'yuansu_kx_per' ,        			-- 元素抗性
	'jineng_shanghai_zj_per' ,       	-- 技能伤害增加
    'jineng_shanghai_jm_per' ,        	-- 技能伤害减免
    'huo_shanghai_zj_per' ,        		-- 离火伤害增加
    'boss_zhenshang' ,        			-- BOSS真伤
    'boss_palsy_per' ,        			-- BOSS麻痹
    'boss_seckill_per' ,       	 		-- BOSS秒杀
   	'lei_shanghai_zj_per' ,        		-- 雷罚伤害增加
    'lei_shanghai_jm_per' ,        		-- 雷罚伤害减免
	'move_speed_per' ,       			-- 移动速度
	'shaguai_jb_diaoluo_per' ,        	-- 杀怪金币掉落
    'zengshang_boss_dk_per' ,        	-- 首领增伤抵抗
    'jianshang_boss_dk_per' ,       	-- 首领减伤抵抗
    'zengshang_guaiwu_dk_per' ,      	-- 怪物增伤抵抗
    'jianshang_guaiwu_dk_per' ,       	-- 怪物减伤抵抗
    'dikang' ,							-- 抵抗真伤
	'per_pvp_add_hurt',					-- pvp增伤加成
	'per_pvp_reduce_hurt',				-- pvp减伤加成
	'huixinyiji_per',					-- 会心一击加成
	'huixinyiji_kang_per',				-- 会心一击抵抗
    'passive_skill_attack' ,      		-- 被动技能攻击百分百技能战力系数
    'passive_skill_depence' ,       	-- 被动技能防御百分百技能战力系数
    'passive_skill_fix_cap' ,			-- 被动技能固定战力系数
    'zhenyan_jc_per',					-- 本阵眼属性
	'shengming_zb_role_jc_per',			--基础生命加成
	'gongji_zb_role_jc_per',			--基础攻击加成
	'fangyu_zb_role_jc_per',			--基础防御加成
	'pojia_zb_role_jc_per',				--基础破甲加成
	'gongji_wuqi_jc_per',				--武器攻击加成
	'pojia_wuqi_jc_per',				--武器破甲加成
	'gongji_shiping_jc_per',			--饰品攻击加成
	'kill_monster_per_exp',				--打怪经验加成
	'rare_exterior_rate_per',			-- 珍稀外观掉落加成
	'rare_equip_rate_per',				-- 珍稀装备掉落加成
	'equip_qianghua_per',				--装备强化加成
	'tianshen_att_per',					--天神全属性加成
}

-- 属性排序，外部禁止直接更改表
function AttributeMgr.SortAttribute()
	return sort_attribute
end


local attr_sort_index_list
function AttributeMgr.GetAttrSortIndexList()
	if attr_sort_index_list == nil then
		attr_sort_index_list = {}
		for k, v in ipairs(sort_attribute) do
			attr_sort_index_list[v] = k
		end
	end

	return attr_sort_index_list
end

-- 获取属性在排序表的位置
function AttributeMgr.GetSortAttributeIndex(attr_str)
	if attr_str == nil then
		return 999
	end

	local new_attr_str = AttributeMgr.GetAttributteKey(attr_str)
	local sort_index_list = AttributeMgr.GetAttrSortIndexList()
	return sort_index_list[new_attr_str] or 999
end

-- 读取一个对象的属性值
-- 属性str转换
local attr_name_tree = {
	["max_hp"] = {"maxhp", "hp", "qixue", "shengming_max","shengming"},
	["gong_ji"] = {"attack", "gongji"},
	["fang_yu"] = {"fangyu", "jian_ren", "jianren"},
	["po_jia"] = {"pojia", "chuantou", "chuan_tou"},
	["kangbao_shanghai"] = {"kang_bao"},
	["shengming_jc_per"] = {"per_maxhp", "maxhp_per"},
	["gongji_jc_per"] = {"per_gongji", "gongji_per"},
	["fangyu_jc_per"] = {"jianren_per", "fangyu_per", "per_fangyu", "per_fangyu_jiacheng"},
	["pojia_jc_per"] = {"chuantou_per", "pojia_per", "per_pojia", "per_pojia_jiacheng"},
	["shan_bi"] = {"shanbi"},
	["baoji_per"] = {"per_baoji"},
	["shanbi_per"] = {"per_shanbi"},
	["mingzhong_per"] = {"per_mingzhong"},
	["baoji_shanghai"] = {"baoji", "bao_ji"},
	["ming_zhong"] = {"mingzhong"},
	["baoji_shanghai_per"] = {"baoji_hurt_per"},
	["per_shanghaijiacheng"] = {"shanghai_jiacheng_per"},
	["per_shanghaijianshao"] = {"shanghai_jianshao_per"},
	["kangbao_per"] = {"per_kangbao"},
	["per_pvp_add_hurt"] = {"pvp_zengshang_per"},
	["per_pvp_reduce_hurt"] = {"pvp_jianshang_per"},
	["yuansu_hujia"] = {"yuansu_hj", "yuansuhujia"},
	["yuansu_shanghai"] = {"yuansu_sh", "yuansushanghai"},
	["yuansu_sh_jc_per"] = {"per_yuansu_sh"},
	["yuansu_hj_jc_per"] = {"per_yuansu_hj"},
	["fangtan_jc_per"] = {"fangtan_per"},
	["move_speed_per"] = {"move_speed"},
	["kill_monster_per_exp"] = {"add_exp_per", "add_per_exp"},
}

local attr_name_tree_r
function AttributeMgr.GetAttrNameTreeR()
	if attr_name_tree_r == nil then
		attr_name_tree_r = {}
		for i, v in pairs(attr_name_tree) do
			for _, v1 in ipairs(v) do
				attr_name_tree_r[v1] = i
			end
		end
	end
	return attr_name_tree_r
end

-- 获取定义的  正确的属性名
function AttributeMgr.GetAttributteKey(old_key)
	return AttributeMgr.GetAttrNameTreeR()[old_key] or old_key
end

-- 两个属性相加
function AttributeMgr.AddAttributeAttr(attribute1, attribute2)
	local attribute, attribute_keys = AttributePool.AllocDirtyAttribute()
	local a, b = 0, 0
	for key, _ in pairs(attribute_keys) do
		a = attribute2[key] or 0
		b = attribute1[key] or 0
		attribute[key] = a + b
	end
	return attribute
end

-- 两个属性差值(attribute2 - attribute1)
function AttributeMgr.LerpAttributeAttr(attribute1, attribute2)
	local attribute, attribute_keys = AttributePool.AllocDirtyAttribute()
	local a, b = 0, 0
	for key, _ in pairs(attribute_keys) do
		a = attribute2[key] or 0
		b = attribute1[key] or 0
		attribute[key] = a - b
	end
	return attribute
end

-- 属性乘以一个常数
function AttributeMgr.MulAttribute(attr, num)
	local value = 0
	local attribute, attribute_keys = AttributePool.AllocDirtyAttribute()
	for key,_ in pairs(attribute_keys) do
		value = attr[key] or 0
		attribute[key] = value * num
	end

	return attribute
end

-- 非万分比属性乘以一个常数(万分比属性默认常数为 1)
function AttributeMgr.MulAttributeRemovePer(attr, num)
	local value = 0
	local attribute, attribute_keys = AttributePool.AllocDirtyAttribute()
	for key,_ in pairs(attribute_keys) do
		value = attr[key] or 0
		attribute[key] = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(key) and value or value * num
	end

	return attribute
end

-- 属性乘以一个常数 但有两种常数  一个是不区分万分比 一个去除万分比(传入时不需要+1)
function AttributeMgr.MulAttributeTwo(attr, num, num_remove_per)
	local value = 0
	local last_num = num + 1
	local last_num_remove_per = num + num_remove_per + 1
	local attribute, attribute_keys = AttributePool.AllocDirtyAttribute()
	for key,_ in pairs(attribute_keys) do
		value = attr[key] or 0
		attribute[key] = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(key) and value * last_num or value * last_num_remove_per
	end

	return attribute
end

function AttributeMgr.GetAttributteByList(list, info)
	local attr_name_t = nil
	local value = 0
	if nil ~= info then
		for key, _ in pairs(list) do
			value = info[key] or 0
			attr_name_t = attr_name_tree[key]
			if attr_name_t then
				for k,v in pairs(attr_name_t) do
					value = info[v] or value
				end
			end
			list[key] = value or 0
		end
	end

	return list
end

-- 获取配置的配属性字段列表
-- cfg_map_attr_list = {[配置表配的属性字段] = 属性表的有效字段}
-- attr_map_cfg_list = {[属性表的有效字段] = 配置表配的属性字段}
function AttributeMgr.GetUsefulAttributteByClass(cfg)
	local cfg_map_attr_list, attr_map_cfg_list = {}, {}
	if IsEmptyTable(cfg) then
		return cfg_map_attr_list, attr_map_cfg_list
	end

	local attr_name
	local attribute = AttributePool.AllocDirtyAttribute()
	for key, value in pairs(cfg) do
		attr_name = AttributeMgr.GetAttributteKey(key)
		if attribute[attr_name] then
			if cfg_map_attr_list[key] == nil then
				cfg_map_attr_list[key] = attr_name
			end

			if attr_map_cfg_list[attr_name] == nil then
				attr_map_cfg_list[attr_name] = key
			end
		end
	end

	return cfg_map_attr_list, attr_map_cfg_list
end

function AttributeMgr.GetAttributteByClass(info)
	local attribute = nil
	local attribute_keys
	if nil ~= info then
		attribute, attribute_keys = AttributePool.AllocDirtyAttribute()
		local value = 0
		local attr_name_t = nil
		for _, key in pairs(attribute_keys) do
			attribute[key] = 0
			value = info[key] or 0
			attr_name_t = attr_name_tree[key]
			if attr_name_t then
				for k,v in pairs(attr_name_t) do
					value = info[v] or value

					--如果同一个属性不同属性名出现在同一个表里的话，如果valua已经有值，还会继续往下走会出现取0的情况
					if value > 0 then
						break
					end

				end
			end

			attribute[key] = value or 0
		end
	else
		attribute = AttributePool.AllocAttribute()
	end
	return attribute
end

function AttributeMgr.GetAttributteValueByClass(info)
	local _, attribute_keys = AttributePool.AllocDirtyAttribute()
	local tab = {}
	if nil ~= info then
		local value = 0
		local attr_name_t = nil
		for key, _ in pairs(attribute_keys) do
			value = info[key] or 0
			attr_name_t = attr_name_tree[key]
			if attr_name_t then
				for k,v in pairs(attr_name_t) do
					value = info[v] or value

					--如果同一个属性不同属性名出现在同一个表里的话，如果valua已经有值，还会继续往下走会出现取0的情况
					if value > 0 then
						break
					end

				end
			end

			if value > 0 then
				tab[key] = value
			end
		end
	end
	return tab
end

-- 读取一个显示属性列表
function AttributeMgr.GetAttrNameAndValueByClass(info, all_show)
	local list = {}
	local attribute = AttributeMgr.GetAttributteByClass(info)
	for k,v in pairs(attribute) do
		if all_show or v > 0 then
			local vo  = {}
			vo.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k)
			vo.value = v
			list[#list + 1] = vo
		end
	end
	return list
end

--按照属性顺序排列
function AttributeMgr.GetAttrList()
	return sort_attribute
end

function AttributeMgr.AttributeAddProfRate(attribute)
	if nil == attribute then return end
	local prof = RoleWGData.Instance:GetRoleProf()
	for k,v in pairs(attribute) do
		local prof_attr_rate = AttributeMgr.PROF_ATTR_RATE[prof] or {}
		if prof_attr_rate[k] then
			attribute[k] = math.floor(v * prof_attr_rate[k])
		end
	end
end

function AttributeMgr.AttributeFloorRounding(attribute)
	for k,v in pairs(attribute) do
		if not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k) then
			attribute[k] = math.floor(v)
		else
			if attribute[k] > 0 then
				attribute[k] = math.max(string.format("%0.3f", v), 0.0001)
			else
				attribute[k] = 0
			end
		end
	end
	return attribute
end

-- 尽量使用下面的FlushAttr 配合GetAttrShowStruct使用更佳 基本可以使属性刷新代码实现全封装
function AttributeMgr.FlushAttr(widgets, attribute, next_attribute, prefix, attr_str, next_attr_str, ignore_attr_list, attr_name_list, call_back, name_callback)
	prefix = prefix or ""
	attr_str = attr_str or "%s"
	next_attr_str = next_attr_str or "%s"
	ignore_attr_list = ignore_attr_list or {}
	local next_index = 1
	local attribute = attribute or AttributePool.AllocAttribute()
	local next_attribute = next_attribute or AttributePool.AllocAttribute()
	name_callback = name_callback or function (name) return name end

	for i,v in ipairs(AttributeMgr.SortAttribute()) do
		if not ignore_attr_list[v] then
			if next_index >= 10 then break end
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
			if attribute[v] ~=nil and attribute[v] > 0  or next_attribute[v] ~= nil and next_attribute[v] > 0 then
				if widgets["attr_" .. next_index] then
					widgets["attr_" .. next_index]:SetActive(true)
				end

				if widgets[prefix .. "lbl_type_" .. next_index] then
					widgets[prefix .. "lbl_type_" .. next_index]:SetActive(true)
					widgets[prefix .. "lbl_type_" .. next_index].text.text = name_callback(Language.Common.AttrName[v])
				end
				if widgets[prefix .. "lbl_value_" .. next_index] then
					widgets[prefix .. "lbl_value_" .. next_index]:SetActive(true)
					local text = is_per and attribute[v] / 100  .. "%" or attribute[v]
					widgets[prefix .. "lbl_value_" .. next_index].text.text = string.format(attr_str, text)
				end
				if widgets[prefix .. "lbl_add_value_" .. next_index] then
					widgets[prefix .. "lbl_add_value_" .. next_index]:SetActive(next_attribute[v] > 0)
					local text = is_per and next_attribute[v] / 100  .. "%" or next_attribute[v]
					widgets[prefix .. "lbl_add_value_" .. next_index].text.text = string.format(next_attr_str, text)
				end
				if widgets[prefix .. "img_arrow_" .. next_index] then
					widgets[prefix .. "img_arrow_" .. next_index]:SetActive(next_attribute[v] > 0)
				end
				if call_back then
					call_back(next_index, v)
				end
				next_index = next_index + 1
			end
		end
	end

	for i = next_index, 10 do 		-- 属性不会超过10条
		if widgets[prefix .. "attr_" .. i] then
			widgets[prefix .. "attr_" .. i]:SetActive(false)
		end

		if widgets[prefix .. "lbl_type_" .. i] then
			widgets[prefix .. "lbl_type_" .. i]:SetActive(false)
		end
		if widgets[prefix .. "lbl_value_" .. i] then
			widgets[prefix .. "lbl_value_" .. i]:SetActive(false)
		end
		if widgets[prefix .. "lbl_add_value_" .. i] then
			widgets[prefix .. "lbl_add_value_" .. i]:SetActive(false)
		end
		if widgets[prefix .. "img_arrow_" .. i] then
			widgets[prefix .. "img_arrow_" .. i]:SetActive(false)
		end
	end
	return next_index
end

function AttributeMgr.FlushAttrByTable(t)
	local widgets = t.widgets
	local attribute = t.attribute
	local next_attribute = t.next_attribute
	local prefix = t.prefix
	local attr_str = t.attr_str
	local next_attr_str = t.next_attr_str
	local ignore_attr_list = t.ignore_attr_list
	local call_back = t.call_back
	local name_callback = t.name_callback
	AttributeMgr.FlushAttr(widgets, attribute, next_attribute, prefix, attr_str, next_attr_str, ignore_attr_list, call_back, name_callback)
end

function AttributeMgr.PerAttrValue(attr_type, value)
	local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_type)
	value = math.floor(value)
	local text = is_per and value / 100  .. "%" or value
	return text
end

--刷新幻装，御灵的数据
function AttributeMgr.FlushFashionAttr(widgets, attribute, next_attribute, difference_prof, appoint_prof)
	local next_index = 1
	local attribute = attribute or AttributePool.AllocAttribute()
	local next_attribute = next_attribute or AttributePool.AllocAttribute()
	for i,v in ipairs(AttributeMgr.GetAttrList()) do
		if next_index >= 10 then break end
		local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
		local is_wan = AttributeMgr.AttrIsWan(v)
		if attribute[v] ~= nil and attribute[v] > 0  or next_attribute[v] ~= nil and next_attribute[v] > 0 then
			if widgets["lbl_" ..  next_index .. "_text"] then
				widgets["lbl_" ..  next_index .. "_text"].text.text = Language.Common.TipsAttrNameList[v] --.. "："  AttrNameList2
			end
			if widgets["lbl_" .. next_index .. "_val"] then
				local text
				if is_wan or is_per then
					text = attribute[v] == 0 and "0%" or string.format("%.2f", attribute[v]/100) .. "%"
				else
					text = attribute[v]
				end
				widgets["lbl_" .. next_index .. "_val"].text.text = text
			end
			if widgets["lbl_" .. next_index .. "_add"] then
				local text
				if is_wan or is_per then
					text = next_attribute[v]/100 .. "%"
				else
					text = next_attribute[v]
				end
				if next_attribute[v] > 0 then
					widgets["lbl_" .. next_index .. "_add"].text.text = text
				else
					widgets["lbl_" .. next_index .. "_add"].text.text = ""
				end
			end
			if widgets["img_" ..  next_index .. "_add"] then
				widgets["img_" ..  next_index .. "_add"]:SetActive(next_attribute[v] > 0)
			end
			next_index = next_index + 1
		end
	end

	for i = next_index, 10 do 		-- 属性不会超过10条
		if widgets["lbl_" ..  i .. "_text"] then
			widgets["lbl_" ..  i .. "_text"].text.text = ""
		end
		if widgets["lbl_" .. i .. "_val"] then
			widgets["lbl_" .. i .. "_val"].text.text = ""
		end
		if widgets["lbl_" .. i .. "_add"] then
			widgets["lbl_" .. i .. "_add"].text.text = ""
		end
		if widgets["img_" ..  i .. "_add"] then
			widgets["img_" ..  i .. "_add"]:SetActive(false)
		end
	end
end

function AttributeMgr.FlushValueAttr(widgets, attribute, difference_prof, appoint_prof)
	local next_index = 1
	local attribute = attribute or AttributePool.AllocAttribute()


	for i,v in ipairs(AttributeMgr.GetAttrList()) do
		local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
		if attribute[v] and attribute[v] > 0 then
			if widgets["lbl_type_" .. next_index] then
				widgets["lbl_type_" .. next_index]:SetActive(true)
				widgets["lbl_type_" .. next_index].text.text = Language.Common.AttrNameList[v] .. ":"
			end
			if widgets["lbl_value_" .. next_index] then
				widgets["lbl_value_" .. next_index]:SetActive(true)
				local text = is_per and attribute[v] * 100 .. "%" or attribute[v]
				widgets["lbl_value_" .. next_index].text.text = text
			end
			next_index = next_index + 1
		end
	end
end

--刷新当前属性方法
function AttributeMgr.FlushAttrView(widgets, attribute, showspd)
	if nil ~= widgets and nil ~= attribute then
		for k,v in pairs(AttributeMgr.attrview_t) do
			if widgets.transform:Find("lbl_" .. v[1] .. "_val") then
				widgets.transform:Find("lbl_" .. v[1] .. "_val"):GetComponent(typeof(UnityEngine.UI.Text)).text = attribute[v[2]]
			end
		end

		if nil ~= widgets.transform:Find("lbl_gongji_bar_val") then
			local str = attribute.gong_ji
			if attribute.min_gong_ji > 0 then
				str = attribute.min_gong_ji .. "~" .. str
			end
			widgets.transform:Find("lbl_gongji_bar_val"):GetComponent(typeof(UnityEngine.UI.Text)).text = str
		end

		if true == showspd and widgets.transform:Find("lbl_movespeed_val") then
			widgets.transform:Find("lbl_movespeed_val"):GetComponent(typeof(UnityEngine.UI.Text)).text = "+" .. attribute.speed_percent .. "%"
		end
	end
end

-- 刷新下一级属性
function AttributeMgr.FlushNextAttrView(widgets, attribute, showspd)
	if nil ~= widgets and nil ~= attribute then
		for k,v in pairs(AttributeMgr.attrview_t) do
			if widgets.transform:Find("lbl_" .. v[1] .. "_add") then
				local node = widgets.transform:Find("lbl_" .. v[1] .. "_add")
				node.gameObject:SetActive(attribute[v[2]] ~= nil and attribute[v[2]] > 0)
				if attribute[v[2]] ~= nil and attribute[v[2]] > 0 then
					node:GetComponent(typeof(UnityEngine.UI.Text)).text = attribute[v[2]]
				end
				if widgets.transform:Find("img_" .. v[1] .. "_add") then
					widgets.transform:Find("img_" .. v[1] .. "_add").gameObject:SetActive(attribute[v[2]] ~= nil and attribute[v[2]] > 0)
				end
			end
		end

		if true == showspd and widgets.transform:Find("lbl_movespeed_add") then
			widgets.transform:Find("lbl_movespeed_add").gameObject:SetActive(0 ~= attribute.move_speed)
			if 0 ~= attribute.move_speed then
				widgets.transform:Find("lbl_movespeed_add"):GetComponent(typeof(UnityEngine.UI.Text)).text = "+" .. attribute.speed_percent .. "%"
			end
		end
	end
end


--刷新当前属性方法2
function AttributeMgr.FlushAttrViewTwoWay(widgets, attribute)
	if nil == widgets and nil == attribute then
		return
	end

	for i=1,10 do
		if widgets["lbl_" .. i .. "_val"] then
			widgets["lbl_" ..  i .. "_text"].text.text = ""
			widgets["lbl_" ..  i .. "_val"].text.text = ""
		end
	end
	local index, attr_value = 1, 0
	local sort_tab = AttributeMgr.SortAttribute()
	local widgets_val, widgets_text
	for k, v in ipairs(sort_tab) do
		attr_value = attribute[v]
		if attr_value > 0 then
			widgets_val = "lbl_" .. index .. "_val"
			widgets_text = "lbl_" .. index .. "_text"
			if widgets[widgets_val] then
				attr_value = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) and attr_value / 100 .. "%" or attr_value
				widgets[widgets_text].text.text = Language.Common.AttrName[v] or ""
				widgets[widgets_val].text.text = attr_value
				index = index + 1
			end
		end
	end
end

--坐骑专用   特殊坐骑激活前显示
function AttributeMgr.FlushAttrMountView(widgets, attribute)
	if nil == widgets and nil == attribute then
		return
	end

	for i=1,10 do
		if widgets["lbl_" .. i .. "_add"] then
			widgets["lbl_" ..  i .. "_text"].text.text = ""
			widgets["lbl_" ..  i .. "_add"].text.text = ""
			widgets["lbl_" ..  i .. "_val"].text.text = ""
			widgets["img_" ..  i .. "_add"]:SetActive(false)
		end
	end

	local index = 1
	local sort_tab = AttributeMgr.SortAttribute()
	local attr_value, str
	for k, v in ipairs(sort_tab) do
		attr_value = attribute[v]
		if attr_value > 0  then
			--str = Language.Common.AttrName[v]
			str = Language.Common.TipsAttrNameList[v]
			attr_value = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) and attr_value/100 .. "%" or attr_value

			widgets["lbl_" .. index .. "_text"].text.text = str
			widgets["lbl_" .. index .. "_add"].text.text = attr_value
			widgets["img_" ..  index .. "_add"]:SetActive(true)
			widgets["lbl_" ..  index .. "_val"].text.text = 0
			index = index + 1
		end
	end
end

-- 刷新下一级属性方法2
function AttributeMgr.FlushArrowsNextAttrViewTwoWay(widgets, attribute, cur_level)
	if nil == widgets and nil == attribute then
		return
	end

	for i=1,10 do
		if widgets["lbl_" .. i .. "_add"] then
			widgets["lbl_" .. i .. "_add"].text.text = ""
			widgets["img_" .. i .. "_add"]:SetActive(false)
		end
	end

	local index, attr_value = 1, 0
	local widgets_lbl_text, widgets_lbl_val, widgets_lbl_add, widgets_img_add, str
	local sort_tab = AttributeMgr.SortAttribute()
	for k,v in ipairs(sort_tab) do
		attr_value = attribute[v]
		if attr_value > 0 then
			if cur_level and cur_level < 0 then
				str = Language.Common.AttrName[v]
				widgets_lbl_text = "lbl_" .. index .. "_text"
				widgets_lbl_val = "lbl_" .. index .. "_val"
				widgets[widgets_lbl_text].text.text = str
				widgets[widgets_lbl_val].text.text = 0
			end
			widgets_lbl_add = "lbl_" .. index .. "_add"
			widgets_img_add = "img_" .. index .. "_add"
			attr_value = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) and attr_value/100 .. "%" or attr_value

			widgets[widgets_img_add]:SetActive(true)
			widgets[widgets_lbl_add].text.text = attr_value
			index = index + 1
		end
	end
end

function AttributeMgr.GetNumberAttrCapability( value )
	value = value or {}
	local cap = 0
	for i=1,10 do
		if value["attr_type_" .. i] and FIGHT_POWER_ENUM[value["attr_type_" .. i]] then
			cap = cap + FIGHT_POWER_ENUM[value["attr_type_" .. i]] * (value["attr_value_" .. i] or 0)
		end
end
	return cap
end

-- 记录属性名称没有"per"但却是万分比的属性
local is_per_attr = {
}

-- 属性是否百分比显示
function AttributeMgr.IsPerAttr(attri_name)
	if attri_name == "move_speed_per" then
		return false
	end

	if is_per_attr[attri_name] then
		return true
	end

	local list = Split(attri_name, "_")
	if #list <= 0 then return false end
	for k,v in pairs(list) do
		if v == "per" then
			return true
		end
	end
	return false
end

-- 属性是否万分比显示
function AttributeMgr.AttrIsWan(attri_name)
	if attri_name == "yuansu_sh_jc_per" or attri_name == "per_baoshang_jiacheng" then
		return true
	end
	return false
end

local zhanli_show_role_attr = {
	"shengming_max",
	"gongji",
	"fangyu",
	"pojia",
	"yuansu_sh",
	"yuansu_hj",
	"baoji_shanghai",
	"kangbao_shanghai",
	"shengming_qq",
	"fangtan",
	"shanghai_zs",
	"fangyu_zs",
	"shengming_hf",
	"shanghai_jn",
	"shengming_jc_per",
	"gongji_jc_per",
	"fangyu_jc_per",
	"pojia_jc_per",
	"yuansu_sh_jc_per",
	"yuansu_hj_jc_per",
	"baoji_jc_per",
	"kangbao_jc_per",
	"shengming_qq_jc_per",
	"fangtan_jc_per",
	"shanghai_zs_jc_per",
	"fangyu_zs_jc_per",
	"shanbi_per",
	"mingzhong_per",
	"baoji_per",
	"kangbao_per",
	"lianji_per",
	"lianjikang_per",
	"jichuan_per",
	"jichuankang_per",
	"gedang_per",
	"podang_per",
	"baoji_shanghai_per",
	"baoji_shanghai_jm_per",
	"lianji_shanghai_per",
	"lianji_shanghai_jm_per",
	"gedang_ms_per",
	"gedang_ms_my_per",
	"jineng_shanghai_zj_per",
	"jineng_shanghai_jm_per",
	"shanghai_quan_jc_per",
	"shanghai_quan_jm_per",
	"zengshang_boss_per",
	"jianshang_boss_per",
	"zengshang_guaiwu_per",
	"jianshang_guaiwu_per",
	"zengshang_per",
	"jianshang_per",
	"zengshang_bs_per",
	"jianshang_bs_per",
	"mingzhong_yc_per",
	"dikang_yc_per",
	"zhiliaoxiaoguo_per",
	"zengshang_yc_per",
	"zengshang_gx_per",
	"zengshang_xr_per",
	"yuansu_jk_per",
	"yuansu_kx_per",
	"shanghai_jc_per",
	"shanghai_jm_per",
	"shengming_hf_per",
	"kill_monster_per_exp",
	"rare_equip_rate_per",
	"rare_exterior_rate_per",
}

local zhanli_show_cfg_attr = {
	"max_hp",
	"gong_ji",
	"fang_yu",
	"po_jia",
	"yuansu_shanghai",
	"yuansu_hujia",
	"baoji_shanghai",
	"kangbao_shanghai",
	"shengming_qq",
	"fangtan",
	"shanghai_zs",
	"fangyu_zs",
	"shengming_hf",
	"shanghai_jn",
	"shengming_jc_per",
	"gongji_jc_per",
	"fangyu_jc_per",
	"pojia_jc_per",
	"yuansu_sh_jc_per",
	"yuansu_hj_jc_per",
	"baoji_jc_per",
	"kangbao_jc_per",
	"shengming_qq_jc_per",
	"fangtan_jc_per",
	"shanghai_zs_jc_per",
	"fangyu_zs_jc_per",
	"shanbi_per",
	"mingzhong_per",
	"baoji_per",
	"kangbao_per",
	"lianji_per",
	"lianjikang_per",
	"jichuan_per",
	"jichuankang_per",
	"gedang_per",
	"podang_per",
	"baoji_shanghai_per",
	"baoji_shanghai_jm_per",
	"lianji_shanghai_per",
	"lianji_shanghai_jm_per",
	"gedang_ms_per",
	"gedang_ms_my_per",
	"jineng_shanghai_zj_per",
	"jineng_shanghai_jm_per",
	"shanghai_quan_jc_per",
	"shanghai_quan_jm_per",
	"zengshang_boss_per",
	"jianshang_boss_per",
	"zengshang_guaiwu_per",
	"jianshang_guaiwu_per",
	"zengshang_per",
	"jianshang_per",
	"zengshang_bs_per",
	"jianshang_bs_per",
	"mingzhong_yc_per",
	"dikang_yc_per",
	"zhiliaoxiaoguo_per",
	"zengshang_yc_per",
	"zengshang_gx_per",
	"zengshang_xr_per",
	"yuansu_jk_per",
	"yuansu_kx_per",
	"shanghai_jc_per",
	"shanghai_jm_per",
	"shengming_hf_per",
	"kill_monster_per_exp",
	"rare_equip_rate_per",
	"rare_exterior_rate_per",
}

-- 固定值属性
local zhanli_gd_attr_name = {
	"生命",
	"攻击",
	"防御",
	"破甲",
	"元素伤害",
	"元素护甲",
	"暴击",
	"抗暴",
	"生命窃取",
	"反弹伤害",
	"真实伤害",
	"真实防御",
}

-- 同步策划属性命名，方便策划在xls输入
local zhanli_attr_name = {
	"生命",
	"攻击",
	"防御",
	"破甲",
	"元素伤害",
	"元素护甲",
	"暴击",
	"抗暴",
	"生命窃取",
	"反弹伤害",
	"真实伤害",
	"真实防御",
	"生命回复",
	"技能伤害",
	-- "固定类技能战斗力",
	"血量百分比",
	"攻击百分比",
	"防御百分比",
	"破甲百分比",
	"元素伤害加成",
	"元素护甲加成",
	"暴击加成",
	"抗暴加成",
	"生命窃取百分比",
	"反弹伤害百分比",
	"真实伤害百分比",
	"真实防御百分比",
	"闪避率",
	"命中率",
	"暴击率",
	"抗暴率",
	"连击率",
	"连击抵抗",
	"击穿率",
	"击穿抵抗",
	"格挡率",
	"破档率",
	"暴击伤害",
	"暴击伤害减免",
	"连击伤害比例",
	"连击伤害减免",
	"格挡免伤",
	"格挡免伤免疫",
	"技能伤害增加",
	"技能伤害减免",
	"全属性伤害加成",
	"全属性伤害减免",
	"首领增伤",
	"首领减伤",
	"怪物增伤",
	"怪物减伤",
	"玩家增伤",
	"玩家减伤",
	"变身增伤",
	"变身减伤",
	"异常状态命中",
	"异常状态抵抗",
	"治疗效果",
	"异常状态增伤",
	"高血增伤",
	"虚弱增伤",
	-- "攻击类技能战斗力系数",
	-- "生存类技能战斗力系数",
	"元素减抗",
	"元素抗性",
	"伤害加成",
	"伤害减免",
	"生命回复比例",
	"杀怪经验加成",
	"基本装备掉落加成",
	"外形掉落加成",
	-- "杀怪金币掉落",
}

function AttributeMgr.PrintNeedZhanLiAttrValue(cfg_attr)
	local deng_str = "%s=%s\n"
	-- local deng_str2 = "%s = %s              %s\n"

	if not IsEmptyTable(cfg_attr) then
		local cfg_attr_str_list = {"\n"}
		-- local print_cfg_list = {"\n"}
		for k, v in ipairs(zhanli_show_cfg_attr) do
			if cfg_attr[v] and cfg_attr[v] > 0 then
				local name = zhanli_attr_name[k] or ""
				local value = AttributeMgr.PerAttrValue(v, cfg_attr[v])
				local str = string.format(deng_str, name, value)
				table.insert(cfg_attr_str_list, str)
				-- local str2 = string.format(deng_str2, name, cfg_attr[v], v)
				-- table.insert(print_cfg_list, str2)
			end
		end
		print_error("---配置属性---", table.concat(cfg_attr_str_list))
	end

	local role_vo = RoleWGData.Instance:GetRoleVo()
	if role_vo then
		local role_attr_str_list = {"\n"}
		-- local print_role_list = {"\n"}
		for k, v in ipairs(zhanli_show_role_attr) do
			if role_vo[v] and role_vo[v] > 0 then
				local name = zhanli_attr_name[k] or ""
				local value = AttributeMgr.PerAttrValue(v, role_vo[v])
				local str = string.format(deng_str, name, value)
				table.insert(role_attr_str_list, str)
				-- local str2 = string.format(deng_str2, name, role_vo[v], v)
				-- table.insert(print_role_list, str2)
			end
		end

		print_error("---玩家属性---", table.concat(role_attr_str_list))
	end
end

-- 【新】战力值
function AttributeMgr.GetCapability(attribute, skill_cfg)
	local role_vo = RoleWGData.Instance:GetRoleVo()
	if not role_vo then
		return 0
	end

	-- 打印属性
	local show_switch = RoleWGData.Instance:GetShowCapPrintSwitch()
	if show_switch then
		AttributeMgr.PrintNeedZhanLiAttrValue(attribute)
	end

	attribute = attribute or AttributePool.AllocAttribute()
	local cap_param = CommonDataManager.GetParamCfg()

	local function print_zhanli(type_str, attr_value_list)
		if IsEmptyTable(attr_value_list) then
			return
		end

		local deng_str = "%s=%s\n"
		local print_list = {"\n"}
		for k, v in ipairs(zhanli_gd_attr_name) do
			local value = attr_value_list[k] or 0
			local str = string.format(deng_str, v, value)
			table.insert(print_list, str)
		end

		print_error(type_str, table.concat(print_list))
	end

	-- 【配置固定值属性】
	local shengming_zhanli = attribute.max_hp * cap_param.shengming_zhanli_xs									-- 生命战力
	local gongji_zhanli = attribute.gong_ji * cap_param.gongji_zhanli_xs										-- 攻击战力
	local fangyu_zhanli = attribute.fang_yu * cap_param.fangyu_zhanli_xs										-- 防御战力
	local pojia_zhanli = attribute.po_jia * cap_param.pojia_zhanli_xs											-- 破甲战力
	local yuansu_shanghai_zhanli = attribute.yuansu_shanghai * cap_param.yuansu_shanghai_zhanli_xs				-- 元素伤害战力
	local yuansu_hujia_zhanli = attribute.yuansu_hujia * cap_param.yuansu_hj_zhanli_xs							-- 元素护甲战力

	local baoji_shanghai_zhanli = attribute.baoji_shanghai * cap_param.baojishanghai_zhanli_xs					-- 暴击伤害固定战力
	local kangbao_shanghai_zhanli = attribute.kangbao_shanghai * cap_param.kangbaoshanghai_zhanli_xs			-- 抗暴伤害固定战力
	local shengming_qq_zhanli = attribute.shengming_qq * cap_param.shengming_qq_zhanli_xs						-- 生命窃取战力
	local fangtan_sh_zhanli = attribute.fangtan * cap_param.shanghai_fangtan_zhanli_xs							-- 反弹伤害战力
	local shanghai_zs_zhanli = attribute.shanghai_zs * cap_param.zhenshishanghai_zhanli_xs						-- 真实伤害战力
	local fangyu_zs_zhanli = attribute.fangyu_zs * cap_param.zhenshifangyu_zhanli_xs							-- 真实防御战力
	local shengming_hf_zhanli = attribute.shengming_hf * cap_param.shengming_hf_zhanli_xs						-- 生命回复战力
	local jinengshanghai_zhanli = attribute.shanghai_jn * cap_param.shanghai_jn_zhanli_xs						-- 技能伤害战力
	local boss_zhenshang_zhanli = attribute.boss_zhenshang * cap_param.boss_zhenshang_zhanli_xs					-- boss真伤战力

	-- 固定值属性战力
	local gd_attr_zhanli = shengming_zhanli + gongji_zhanli + fangyu_zhanli
						+ pojia_zhanli + yuansu_shanghai_zhanli + yuansu_hujia_zhanli
						+ baoji_shanghai_zhanli + kangbao_shanghai_zhanli + shengming_qq_zhanli + fangtan_sh_zhanli
						+ shanghai_zs_zhanli + fangyu_zs_zhanli + shengming_hf_zhanli + jinengshanghai_zhanli
						+ boss_zhenshang_zhanli

	if show_switch then
		print_zhanli("---配置固定值战力---", {shengming_zhanli, gongji_zhanli, fangyu_zhanli,
									pojia_zhanli, yuansu_shanghai_zhanli, yuansu_hujia_zhanli,
									baoji_shanghai_zhanli, kangbao_shanghai_zhanli, shengming_qq_zhanli,
									fangtan_sh_zhanli, shanghai_zs_zhanli, fangyu_zs_zhanli})
	end

	-- 【影响战力】固定值属性 【受】 身上百分比影响战力
	local gd_shengming_add_zhanli = role_vo.shengming_jc_per * 0.0001 * shengming_zhanli								-- 生命加成战力
	local gd_gongji_add_zhanli = role_vo.gongji_jc_per * 0.0001 * gongji_zhanli											-- 攻击加成战力
	local gd_fangyu_add_zhanli = role_vo.fangyu_jc_per * 0.0001 * fangyu_zhanli											-- 防御加成战力
	local gd_pojia_add_zhanli = role_vo.pojia_jc_per * 0.0001 * pojia_zhanli											-- 破甲加成战力
	local gd_yuansu_shanghai_add_zhanli = role_vo.yuansu_sh_jc_per * 0.0001 * yuansu_shanghai_zhanli					-- 元素伤害加成战力
	local gd_yuansu_hujia_add_zhanli = role_vo.yuansu_hj_jc_per * 0.0001 * yuansu_hujia_zhanli							-- 元素护甲加成战力
	local gd_baoji_shanghai_add_zhanli = role_vo.baoji_jc_per * 0.0001 * baoji_shanghai_zhanli							-- 暴击伤害固定加成战力
	local gd_kangbao_shanghai_add_zhanli = role_vo.kangbao_jc_per * 0.0001 * kangbao_shanghai_zhanli					-- 抗暴伤害固定加成战力
	local gd_shengming_qq_add_zhanli = role_vo.shengming_qq_jc_per * 0.0001 * shengming_qq_zhanli						-- 生命窃取加成战力
	local gd_fangtan_sh_add_zhanli = role_vo.fangtan_jc_per * 0.0001 * fangtan_sh_zhanli								-- 反弹伤害加成战力
	local gd_shanghai_zs_add_zhanli = role_vo.shanghai_zs_jc_per * 0.0001 * shanghai_zs_zhanli							-- 真实伤害加成战力
	local gd_fangyu_zs_add_zhanli = role_vo.fangyu_zs_jc_per * 0.0001 * fangyu_zs_zhanli								-- 真实防御加成战力

	local gd_per_effect_zhanli = gd_shengming_add_zhanli + gd_gongji_add_zhanli + gd_fangyu_add_zhanli
							+ gd_pojia_add_zhanli + gd_yuansu_shanghai_add_zhanli + gd_yuansu_hujia_add_zhanli
							+ gd_baoji_shanghai_add_zhanli + gd_kangbao_shanghai_add_zhanli + gd_shengming_qq_add_zhanli
							+ gd_fangtan_sh_add_zhanli + gd_shanghai_zs_add_zhanli + gd_fangyu_zs_add_zhanli


	-- 玩家身上基础战力（含加成值）
	local role_base_shengming_zhanli = role_vo.shengming_max * cap_param.shengming_zhanli_xs
	local role_base_gongji_zhanli = role_vo.gongji * cap_param.gongji_zhanli_xs
	local role_base_fangyu_zhanli = role_vo.fangyu * cap_param.fangyu_zhanli_xs
	local role_base_pojia_zhanli = role_vo.pojia * cap_param.pojia_zhanli_xs
	local role_base_ys_sh_zhanli = role_vo.yuansu_sh * cap_param.yuansu_shanghai_zhanli_xs
	local role_base_ys_hj_zhanli = role_vo.yuansu_hj * cap_param.yuansu_hj_zhanli_xs
	local role_base_baoji_zhanli = role_vo.baoji_shanghai * cap_param.baojishanghai_zhanli_xs
	local role_base_kangbao_zhanli = role_vo.kangbao_shanghai * cap_param.kangbaoshanghai_zhanli_xs
	local role_base_hp_qiequ_zhanli = role_vo.shengming_qq * cap_param.shengming_qq_zhanli_xs
	local role_base_fantan_zhanli = role_vo.fangtan * cap_param.shanghai_fangtan_zhanli_xs
	local role_base_zs_sh_zhanli = role_vo.shanghai_zs * cap_param.zhenshishanghai_zhanli_xs
	local role_base_zs_fy_zhanli = role_vo.fangyu_zs * cap_param.zhenshifangyu_zhanli_xs
	
	local function calc_per_zhanli(role_value_zl, role_per_value, cfg_zhanli)
		role_value_zl = role_value_zl or 0
		role_per_value = role_per_value or 0
		cfg_zhanli = cfg_zhanli or 0
		local zhanli = (role_value_zl / (1 + role_per_value * 0.0001)) + cfg_zhanli
		return zhanli
	end

	-- 总基础值战力   (面板值/全身百分比+提升固定值) * 战力系数
	local role_gj_total_shengming_zhanli = calc_per_zhanli(role_base_shengming_zhanli, role_vo.shengming_jc_per, shengming_zhanli)
	local role_gj_total_gongji_zhanli = calc_per_zhanli(role_base_gongji_zhanli, role_vo.gongji_jc_per, gongji_zhanli)
	local role_gj_total_fangyu_zhanli = calc_per_zhanli(role_base_fangyu_zhanli, role_vo.fangyu_jc_per, fangyu_zhanli)
	local role_gj_total_pojia_zhanli = calc_per_zhanli(role_base_pojia_zhanli, role_vo.pojia_jc_per, pojia_zhanli)
	local role_gj_total_ys_sh_zhanli = calc_per_zhanli(role_base_ys_sh_zhanli, role_vo.yuansu_sh_jc_per, yuansu_shanghai_zhanli)
	local role_gj_total_ys_hj_zhanli = calc_per_zhanli(role_base_ys_hj_zhanli, role_vo.yuansu_hj_jc_per, yuansu_hujia_zhanli)
	local role_gj_total_baoji_zhanli = calc_per_zhanli(role_base_baoji_zhanli, role_vo.baoji_jc_per, baoji_shanghai_zhanli)
	local role_gj_total_kangbao_zhanli = calc_per_zhanli(role_base_kangbao_zhanli, role_vo.kangbao_jc_per, kangbao_shanghai_zhanli)
	local role_gj_total_hp_qiequ_zhanli = calc_per_zhanli(role_base_hp_qiequ_zhanli, role_vo.shengming_qq_jc_per, shengming_qq_zhanli)
	local role_gj_total_fantan_zhanli = calc_per_zhanli(role_base_fantan_zhanli, role_vo.fangtan_jc_per, fangtan_sh_zhanli)
	local role_gj_total_zs_sh_zhanli = calc_per_zhanli(role_base_zs_sh_zhanli, role_vo.shanghai_zs_jc_per, shanghai_zs_zhanli)
	local role_gj_total_zs_fy_zhanli = calc_per_zhanli(role_base_zs_fy_zhanli, role_vo.fangyu_zs_jc_per, fangyu_zs_zhanli)

	if show_switch then
		print_zhanli("---总基础值战力---", {role_gj_total_shengming_zhanli, role_gj_total_gongji_zhanli, role_gj_total_fangyu_zhanli,
									role_gj_total_pojia_zhanli, role_gj_total_ys_sh_zhanli, role_gj_total_ys_hj_zhanli,
									role_gj_total_baoji_zhanli, role_gj_total_kangbao_zhanli, role_gj_total_hp_qiequ_zhanli,
									role_gj_total_fantan_zhanli, role_gj_total_zs_sh_zhanli, role_gj_total_zs_fy_zhanli})
	end

	-- 【配置固定值百分比加成】
	local shengming_add_zhanli = attribute.shengming_jc_per * 0.0001 * role_gj_total_shengming_zhanli							-- 生命加成战力
	local gongji_add_zhanli = attribute.gongji_jc_per * 0.0001 * role_gj_total_gongji_zhanli									-- 攻击加成战力
	local fangyu_add_zhanli = attribute.fangyu_jc_per * 0.0001 * role_gj_total_fangyu_zhanli									-- 防御加成战力
	local pojia_add_zhanli = attribute.pojia_jc_per * 0.0001 * role_gj_total_pojia_zhanli										-- 破甲加成战力
	local yuansu_shanghai_add_zhanli = attribute.yuansu_sh_jc_per * 0.0001 * role_gj_total_ys_sh_zhanli							-- 元素伤害加成战力
	local yuansu_hujia_add_zhanli = attribute.yuansu_hj_jc_per * 0.0001 * role_gj_total_ys_hj_zhanli							-- 元素护甲加成战力
	local baoji_shanghai_add_zhanli = attribute.baoji_jc_per * 0.0001 * role_gj_total_baoji_zhanli								-- 暴击伤害固定加成战力
	local kangbao_shanghai_add_zhanli = attribute.kangbao_jc_per * 0.0001 * role_gj_total_kangbao_zhanli						-- 抗暴伤害固定加成战力
	local shengming_qq_add_zhanli = attribute.shengming_qq_jc_per * 0.0001 * role_gj_total_hp_qiequ_zhanli						-- 生命窃取加成战力
	local fangtan_sh_add_zhanli = attribute.fangtan_jc_per * 0.0001 * role_gj_total_fantan_zhanli								-- 反弹伤害加成战力
	local shanghai_zs_add_zhanli = attribute.shanghai_zs_jc_per * 0.0001 * role_gj_total_zs_sh_zhanli							-- 真实伤害加成战力
	local fangyu_zs_add_zhanli = attribute.fangyu_zs_jc_per * 0.0001 * role_gj_total_zs_fy_zhanli								-- 真实防御加成战力

	-- 固定值百分比加成属性战力
	local gd_per_attr_zhanli = shengming_add_zhanli + gongji_add_zhanli + fangyu_add_zhanli + pojia_add_zhanli
							+ yuansu_shanghai_add_zhanli + yuansu_hujia_add_zhanli + baoji_shanghai_add_zhanli
							+ kangbao_shanghai_add_zhanli + shengming_qq_add_zhanli + fangtan_sh_add_zhanli
							+ shanghai_zs_add_zhanli + fangyu_zs_add_zhanli


	local function final_cap_calc(base_zhanli, role_per, cfg_per)
		base_zhanli = base_zhanli or 0
		role_per = role_per or 0
		cfg_per = cfg_per or 0

		local final_cap = base_zhanli * (1 + (role_per + cfg_per) * 0.0001)
		return final_cap
	end

	-- 最终面板值战力
	local final_shengming_zhanli = final_cap_calc(role_gj_total_shengming_zhanli, role_vo.shengming_jc_per, attribute.shengming_jc_per)						-- 生命 加成后战力
	local final_gongji_zhanli = final_cap_calc(role_gj_total_gongji_zhanli, role_vo.gongji_jc_per, attribute.gongji_jc_per)									-- 攻击 加成后战力
	local final_fangyu_zhanli = final_cap_calc(role_gj_total_fangyu_zhanli, role_vo.fangyu_jc_per, attribute.fangyu_jc_per)									-- 防御 加成后战力
	local final_pojia_zhanli = final_cap_calc(role_gj_total_pojia_zhanli, role_vo.pojia_jc_per, attribute.pojia_jc_per)										-- 破甲 加成后战力
	local final_yuansu_shanghai_zhanli = final_cap_calc(role_gj_total_ys_sh_zhanli, role_vo.yuansu_sh_jc_per, attribute.yuansu_sh_jc_per)					-- 元素伤害 加成后战力
	local final_yuansu_hujia_zhanli = final_cap_calc(role_gj_total_ys_hj_zhanli, role_vo.yuansu_hj_jc_per, attribute.yuansu_hj_jc_per)						-- 元素护甲 加成后战力
	local final_baoji_shanghai_zhanli = final_cap_calc(role_gj_total_baoji_zhanli, role_vo.baoji_jc_per, attribute.baoji_jc_per)							-- 暴击伤害固定 加成后战力
	local final_kangbao_shanghai_zhanli = final_cap_calc(role_gj_total_kangbao_zhanli, role_vo.kangbao_jc_per, attribute.kangbao_jc_per)					-- 抗暴伤害固定 加成后战力
	local final_shengming_qq_zhanli = final_cap_calc(role_gj_total_hp_qiequ_zhanli, role_vo.shengming_qq_jc_per, attribute.shengming_qq_jc_per)				-- 生命窃取 加成后战力
	local final_fangtan_sh_zhanli = final_cap_calc(role_gj_total_fantan_zhanli, role_vo.fangtan_jc_per, attribute.fangtan_jc_per)							-- 反弹伤害 加成后战力
	local final_shanghai_zs_zhanli = final_cap_calc(role_gj_total_zs_sh_zhanli, role_vo.shanghai_zs_jc_per, attribute.shanghai_zs_jc_per)					-- 真实伤害 加成后战力
	local final_fangyu_zs_zhanli = final_cap_calc(role_gj_total_zs_fy_zhanli, role_vo.fangyu_zs_jc_per, attribute.fangyu_zs_jc_per)							-- 真实防御 加成后战力
	if show_switch then
		print_zhanli("---最终面板值战力---", {final_shengming_zhanli, final_gongji_zhanli, final_fangyu_zhanli,
									final_pojia_zhanli, final_yuansu_shanghai_zhanli, final_yuansu_hujia_zhanli,
									final_baoji_shanghai_zhanli, final_kangbao_shanghai_zhanli, final_shengming_qq_zhanli,
									final_fangtan_sh_zhanli, final_shanghai_zs_zhanli, final_fangyu_zs_zhanli})
	end

	-- 提升战力差(最终面板值战力 - 玩家身上基础属性战力)
	local up_diff_shengming_zhanli = final_shengming_zhanli	- role_base_shengming_zhanli						-- 生命战力
	local up_diff_gongji_zhanli = final_gongji_zhanli - role_base_gongji_zhanli									-- 攻击战力
	local up_diff_fangyu_zhanli = final_fangyu_zhanli - role_base_fangyu_zhanli									-- 防御战力
	local up_diff_pojia_zhanli = final_pojia_zhanli	- role_base_pojia_zhanli									-- 破甲战力
	local up_diff_yuansu_shanghai_zhanli = final_yuansu_shanghai_zhanli - role_base_ys_sh_zhanli				-- 元素伤害战力
	local up_diff_yuansu_hujia_zhanli = final_yuansu_hujia_zhanli - role_base_ys_hj_zhanli						-- 元素护甲战力
	local up_diff_baoji_shanghai_zhanli = final_baoji_shanghai_zhanli - role_base_baoji_zhanli					-- 暴击伤害固定战力
	local up_diff_kangbao_shanghai_zhanli = final_kangbao_shanghai_zhanli - role_base_kangbao_zhanli			-- 抗暴伤害固定战力
	local up_diff_shengming_qq_zhanli = final_shengming_qq_zhanli - role_base_hp_qiequ_zhanli					-- 生命窃取战力
	local up_diff_fangtan_sh_zhanli = final_fangtan_sh_zhanli - role_base_fantan_zhanli							-- 反弹伤害战力
	local up_diff_shanghai_zs_zhanli = final_shanghai_zs_zhanli	- role_base_zs_sh_zhanli						-- 真实伤害战力
	local up_diff_fangyu_zs_zhanli = final_fangyu_zs_zhanli	- role_base_zs_fy_zhanli							-- 真实防御战力

	if show_switch then
		print_zhanli("---提升战力差---", {up_diff_shengming_zhanli, up_diff_gongji_zhanli, up_diff_fangyu_zhanli,
									up_diff_pojia_zhanli, up_diff_yuansu_shanghai_zhanli, up_diff_yuansu_hujia_zhanli,
									up_diff_baoji_shanghai_zhanli, up_diff_kangbao_shanghai_zhanli, up_diff_shengming_qq_zhanli,
									up_diff_fangtan_sh_zhanli, up_diff_shanghai_zs_zhanli, up_diff_fangyu_zs_zhanli})
	end
	-- (基础攻击战力+基础破甲战力) * 加成值 * 系数
	local function calc_gong_no_ys_zhanli1(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (up_diff_gongji_zhanli + up_diff_pojia_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础血量战力+基础防御战力) * 加成值 * 系数
	local function calc_fang_no_ys_zhanli1(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (up_diff_shengming_zhanli + up_diff_fangyu_zhanli) * value_per * ratio
		return zhanli
	end

	-- 基础血量战力 * 加成值 * 系数
	local function calc_fang_sm_zhanli1(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = up_diff_shengming_zhanli * value_per * ratio
		return zhanli
	end

	-- (基础元素伤害战力+基础元素护甲战力) * 加成值 * 系数
	local function calc_yuansu_zhanli1(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (up_diff_yuansu_shanghai_zhanli + up_diff_yuansu_hujia_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础攻击战力+基础破甲战力+基础元素伤害战力) * 加成值 * 系数
	local function calc_gong_zhanli1(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (up_diff_gongji_zhanli + up_diff_pojia_zhanli + up_diff_yuansu_shanghai_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础血量战力+基础防御战力+基础元素护甲战力) * 加成值 * 系数
	local function calc_fang_zhanli1(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (up_diff_shengming_zhanli + up_diff_fangyu_zhanli + up_diff_yuansu_hujia_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础血量战力+基础防御战力+基础元素护甲战力+基础攻击战力+基础破甲战力+基础元素伤害战力) * 加成值 * 系数
	local function calc_all_zhanli1(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (up_diff_gongji_zhanli + up_diff_pojia_zhanli + up_diff_yuansu_shanghai_zhanli +
						up_diff_shengming_zhanli + up_diff_fangyu_zhanli + up_diff_yuansu_hujia_zhanli) * value_per * ratio
		return zhanli
	end

	-- 【影响战力】固定值属性 【对】 身上百分比影响战力
	local gd_effect_per_zhanli = calc_fang_zhanli1(role_vo.shanbi_per, cap_param.shanbi_per_zhanli_xs)			-- 闪避几率
						+ calc_gong_zhanli1(role_vo.mingzhong_per, cap_param.mingzhong_per_zhanli_xs)			-- 命中几率
						+ calc_gong_zhanli1(role_vo.baoji_per, cap_param.baoji_per_zhanli_xs)					-- 暴击几率
						+ calc_fang_zhanli1(role_vo.kangbao_per, cap_param.kangbao_per_zhanli_xs)				-- 抗暴几率
						+ calc_gong_zhanli1(role_vo.lianji_per, cap_param.lianji_per_zhanli_xs)					-- 连击几率
						+ calc_fang_zhanli1(role_vo.lianjikang_per, cap_param.lianji_dk_zhanli_xs)				-- 连击抵抗
						+ calc_gong_zhanli1(role_vo.jichuan_per, cap_param.jichuan_per_zhanli_xs)				-- 击穿几率
						+ calc_fang_zhanli1(role_vo.jichuankang_per, cap_param.jichuan_dk_zhanli_xs)			-- 击穿抵抗
						+ calc_fang_zhanli1(role_vo.gedang_per, cap_param.gedang_zhanli_xs)						-- 格挡几率
						+ calc_gong_zhanli1(role_vo.podang_per, cap_param.podang_zhanli_xs)						-- 破档几率
						+ calc_gong_zhanli1(role_vo.baoji_shanghai_per, cap_param.shanghai_baoji_zhanli_xs)		-- 暴击伤害
						+ calc_fang_zhanli1(role_vo.baoji_shanghai_jm_per, cap_param.baoshang_jm_zhanli_xs)		-- 暴击伤害减免
						+ calc_gong_zhanli1(role_vo.lianji_shanghai_per, cap_param.lianji_sh_zhanli_xs)			-- 连击伤害比例
						+ calc_fang_zhanli1(role_vo.lianji_shanghai_jm_per, cap_param.lianshang_jm_zhanli_xs)	-- 连击伤害减免
						+ calc_fang_zhanli1(role_vo.gedang_ms_per, cap_param.mianshang_gd_zhanli_xs)			-- 格挡免伤
						+ calc_gong_zhanli1(role_vo.gedang_ms_my_per, cap_param.gedang_jm_zhanli_xs)			-- 格挡免伤免疫
						+ calc_gong_zhanli1(role_vo.jineng_shanghai_zj_per, cap_param.jineng_zs_zhanli_xs)		-- 技能伤害增加
						+ calc_fang_zhanli1(role_vo.jineng_shanghai_jm_per, cap_param.jineng_jm_zhanli_xs)		-- 技能伤害减免
						+ calc_gong_zhanli1(role_vo.shanghai_quan_jc_per, cap_param.shanghai_jc_quan_zhanli_xs)	-- 全属性伤害加成
						+ calc_fang_zhanli1(role_vo.shanghai_quan_jm_per, cap_param.shanghai_jm_quan_zhanli_xs)	-- 全属性伤害减免
						+ calc_gong_zhanli1(role_vo.zengshang_boss_per, cap_param.zengshang_boss_zhanli_xs)		-- 首领增伤
						+ calc_fang_zhanli1(role_vo.jianshang_boss_per, cap_param.jianshang_boss_zhanli_xs)		-- 首领减伤
						+ calc_gong_zhanli1(role_vo.zengshang_guaiwu_per, cap_param.zengshang_guaiwu_zhanli_xs)	-- 怪物增伤
						+ calc_fang_zhanli1(role_vo.jianshang_guaiwu_per, cap_param.jianshang_guaiwu_zhanli_xs)	-- 怪物减伤
						+ calc_gong_zhanli1(role_vo.zengshang_per, cap_param.zengshang_zhanli_xs)				-- 玩家增伤
						+ calc_fang_zhanli1(role_vo.jianshang_per, cap_param.jianshang_zhanli_xs)				-- 玩家减伤
						+ calc_gong_zhanli1(role_vo.zengshang_bs_per, cap_param.zengshang_bs_zhanli_xs)			-- 变身增伤
						+ calc_fang_zhanli1(role_vo.jianshang_bs_per, cap_param.jianshang_bs_zhanli_xs)			-- 变身减伤
						+ calc_fang_zhanli1(role_vo.mingzhong_yc_per, cap_param.mingzhong_yc_zhanli_xs)			-- 异常状态命中
						+ calc_gong_zhanli1(role_vo.dikang_yc_per, cap_param.dikang_yc_zhanli_xs)				-- 异常状态抵抗
						+ calc_gong_zhanli1(role_vo.zhiliaoxiaoguo_per, cap_param.zhiliaoxiaoguo_zhanli_xs)		-- 治疗效果
						+ calc_gong_zhanli1(role_vo.zengshang_yc_per, cap_param.zengshang_yc_zhanli_xs)			-- 异常状态增伤
						+ calc_gong_zhanli1(role_vo.zengshang_gx_per, cap_param.zengshang_gaoxue_zhanli_xs)		-- 高血增伤
						+ calc_gong_zhanli1(role_vo.zengshang_xr_per, cap_param.zengshang_xuruo_zhanli_xs)		-- 虚弱增伤
						+ calc_yuansu_zhanli1(role_vo.yuansu_jk_per, cap_param.yuansu_jiankang_zhanli_xs)		-- 元素减抗
						+ calc_yuansu_zhanli1(role_vo.yuansu_kx_per, cap_param.yuansu_kangxing_zhanli_xs)		-- 元素抗性
						+ calc_gong_no_ys_zhanli1(role_vo.shanghai_jc_per, cap_param.shanghai_jc_zhanli_xs)		-- 伤害加成
						+ calc_fang_no_ys_zhanli1(role_vo.shanghai_jm_per, cap_param.shanghai_jm_zhanli_xs)		-- 伤害减免
						+ calc_fang_sm_zhanli1(role_vo.shengming_hf_per, cap_param.shengming_hf_per_zhanli_xs)	-- 生命回复比例
						+ calc_all_zhanli1(role_vo.kill_monster_per_exp, cap_param.kill_monster_per_exp_zhanli_xs)-- 杀怪经验加成
						+ calc_all_zhanli1(role_vo.rare_equip_rate_per, cap_param.rare_equip_rate_per_zhanli_xs)-- 珍稀装备掉落加成
						+ calc_all_zhanli1(role_vo.rare_exterior_rate_per, cap_param.rare_exterior_rate_per_zhanli_xs)-- 珍稀外观掉落加成


	-- (基础攻击战力+基础破甲战力) * 加成值 * 系数
	local function calc_gong_no_ys_zhanli2(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (final_gongji_zhanli + final_pojia_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础血量战力+基础防御战力) * 加成值 * 系数
	local function calc_fang_no_ys_zhanli2(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (final_shengming_zhanli + final_fangyu_zhanli) * value_per * ratio
		return zhanli
	end

	-- 基础血量战力 * 加成值 * 系数
	local function calc_fang_sm_zhanli2(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = final_shengming_zhanli * value_per * ratio
		return zhanli
	end

	-- (基础元素伤害战力+基础元素护甲战力) * 加成值 * 系数
	local function calc_yuansu_zhanli2(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (final_yuansu_shanghai_zhanli + final_yuansu_hujia_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础攻击战力+基础破甲战力+基础元素伤害战力) * 加成值 * 系数
	local function calc_gong_zhanli2(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (final_gongji_zhanli + final_pojia_zhanli + final_yuansu_shanghai_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础血量战力+基础防御战力+基础元素护甲战力) * 加成值 * 系数
	local function calc_fang_zhanli2(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (final_shengming_zhanli + final_fangyu_zhanli + final_yuansu_hujia_zhanli) * value_per * ratio
		return zhanli
	end

	-- (基础血量战力+基础防御战力+基础元素护甲战力+基础攻击战力+基础破甲战力+基础元素伤害战力) * 加成值 * 系数
	local function calc_all_zhanli2(value_per, ratio)
		value_per = value_per or 0
		ratio = ratio or 0
		local zhanli = (final_gongji_zhanli + final_pojia_zhanli + final_yuansu_shanghai_zhanli +
						final_shengming_zhanli + final_fangyu_zhanli + final_yuansu_hujia_zhanli) * value_per * ratio
		return zhanli
	end

	-- 【总攻防属性百分比加成】（公式：部分玩家6大基础属性战力 * 配置加成值 * 系数）
	local gf_per_attr_zhanli = calc_fang_zhanli2(attribute.shanbi_per, cap_param.shanbi_per_zhanli_xs)				-- 闪避几率战力
						+ calc_gong_zhanli2(attribute.mingzhong_per, cap_param.mingzhong_per_zhanli_xs)				-- 命中几率战力
						+ calc_gong_zhanli2(attribute.baoji_per, cap_param.baoji_per_zhanli_xs)						-- 暴击几率战力
						+ calc_fang_zhanli2(attribute.kangbao_per, cap_param.kangbao_per_zhanli_xs)					-- 抗暴几率战力
						+ calc_gong_zhanli2(attribute.lianji_per, cap_param.lianji_per_zhanli_xs)					-- 连击几率战力
						+ calc_fang_zhanli2(attribute.lianjikang_per, cap_param.lianji_dk_zhanli_xs)				-- 连击抵抗战力
						+ calc_gong_zhanli2(attribute.jichuan_per, cap_param.jichuan_per_zhanli_xs)					-- 击穿几率战力
						+ calc_fang_zhanli2(attribute.jichuankang_per, cap_param.jichuan_dk_zhanli_xs)				-- 击穿抵抗战力
						+ calc_fang_zhanli2(attribute.gedang_per, cap_param.gedang_zhanli_xs)						-- 格挡几率战力
						+ calc_gong_zhanli2(attribute.podang_per, cap_param.podang_zhanli_xs)						-- 破档几率战力
						+ calc_gong_zhanli2(attribute.baoji_shanghai_per, cap_param.shanghai_baoji_zhanli_xs)		-- 暴击伤害战力
						+ calc_fang_zhanli2(attribute.baoji_shanghai_jm_per, cap_param.baoshang_jm_zhanli_xs)		-- 暴击伤害减免战力
						+ calc_gong_zhanli2(attribute.lianji_shanghai_per, cap_param.lianji_sh_zhanli_xs)			-- 连击伤害比例战力
						+ calc_fang_zhanli2(attribute.lianji_shanghai_jm_per, cap_param.lianshang_jm_zhanli_xs)		-- 连击伤害减免战力
						+ calc_fang_zhanli2(attribute.gedang_ms_per, cap_param.mianshang_gd_zhanli_xs)				-- 格挡免伤战力
						+ calc_gong_zhanli2(attribute.gedang_ms_my_per, cap_param.gedang_jm_zhanli_xs)				-- 格挡免伤免疫战力
						+ calc_gong_zhanli2(attribute.jineng_shanghai_zj_per, cap_param.jineng_zs_zhanli_xs)		-- 技能伤害增加战力
						+ calc_fang_zhanli2(attribute.jineng_shanghai_jm_per, cap_param.jineng_jm_zhanli_xs)		-- 技能伤害减免战力
						+ calc_gong_zhanli2(attribute.shanghai_quan_jc_per, cap_param.shanghai_jc_quan_zhanli_xs)	-- 全属性伤害加成战力
						+ calc_fang_zhanli2(attribute.shanghai_quan_jm_per, cap_param.shanghai_jm_quan_zhanli_xs)	-- 全属性伤害减免战力
						+ calc_gong_zhanli2(attribute.zengshang_boss_per, cap_param.zengshang_boss_zhanli_xs)		-- 首领增伤战力
						+ calc_fang_zhanli2(attribute.jianshang_boss_per, cap_param.jianshang_boss_zhanli_xs)		-- 首领减伤战力
						+ calc_gong_zhanli2(attribute.zengshang_guaiwu_per, cap_param.zengshang_guaiwu_zhanli_xs)	-- 怪物增伤战力
						+ calc_fang_zhanli2(attribute.jianshang_guaiwu_per, cap_param.jianshang_guaiwu_zhanli_xs)	-- 怪物减伤战力
						+ calc_gong_zhanli2(attribute.zengshang_per, cap_param.zengshang_zhanli_xs)					-- 玩家增伤战力
						+ calc_fang_zhanli2(attribute.jianshang_per, cap_param.jianshang_zhanli_xs)					-- 玩家减伤战力
						+ calc_gong_zhanli2(attribute.zengshang_bs_per, cap_param.zengshang_bs_zhanli_xs)			-- 变身增伤战力
						+ calc_fang_zhanli2(attribute.jianshang_bs_per, cap_param.jianshang_bs_zhanli_xs)			-- 变身减伤战力
						+ calc_fang_zhanli2(attribute.mingzhong_yc_per, cap_param.mingzhong_yc_zhanli_xs)			-- 异常状态命中战力
						+ calc_gong_zhanli2(attribute.dikang_yc_per, cap_param.dikang_yc_zhanli_xs)					-- 异常状态抵抗战力
						+ calc_gong_zhanli2(attribute.zhiliaoxiaoguo_per, cap_param.zhiliaoxiaoguo_zhanli_xs)		-- 治疗效果战力
						+ calc_gong_zhanli2(attribute.zengshang_yc_per, cap_param.zengshang_yc_zhanli_xs)			-- 异常状态增伤战力
						+ calc_gong_zhanli2(attribute.zengshang_gx_per, cap_param.zengshang_gaoxue_zhanli_xs)		-- 高血增伤战力
						+ calc_gong_zhanli2(attribute.zengshang_xr_per, cap_param.zengshang_xuruo_zhanli_xs)		-- 虚弱增伤战力
						+ calc_yuansu_zhanli2(attribute.yuansu_jk_per, cap_param.yuansu_jiankang_zhanli_xs)			-- 元素减抗战力
						+ calc_yuansu_zhanli2(attribute.yuansu_kx_per, cap_param.yuansu_kangxing_zhanli_xs)			-- 元素抗性战力
						+ calc_gong_no_ys_zhanli2(attribute.shanghai_jc_per, cap_param.shanghai_jc_zhanli_xs)		-- 伤害加成战力
						+ calc_fang_no_ys_zhanli2(attribute.shanghai_jm_per, cap_param.shanghai_jm_zhanli_xs)		-- 伤害减免战力
						+ calc_fang_sm_zhanli2(attribute.shengming_hf_per, cap_param.shengming_hf_per_zhanli_xs)	-- 生命回复比例战力
						+ calc_all_zhanli2(attribute.kill_monster_per_exp, cap_param.kill_monster_per_exp_zhanli_xs)-- 杀怪经验加成战力
						+ calc_all_zhanli2(attribute.rare_equip_rate_per, cap_param.rare_equip_rate_per_zhanli_xs)-- 珍稀装备掉落加成战力
						+ calc_all_zhanli2(attribute.rare_exterior_rate_per, cap_param.rare_exterior_rate_per_zhanli_xs)-- 珍稀外观掉落加成战力

	-- 战斗技能战力
	local skill_zhanli = 0
	if not IsEmptyTable(skill_cfg) then
		local skill_xs = 0.000001
		local skill_atk_xs = skill_cfg.attack_power or 0						-- 攻击百分比技能战力
		local skill_dfc_xs = skill_cfg.defence_power or 0						-- 防御百分比技能战力
		local skill_gd_zhanli = skill_cfg.capability_inc or 0					-- 技能固定战力
		if show_switch then
			local str = "---配置技能攻防属性---\n固定类技能战斗力 = %s\n攻击类技能战斗力系数 = %s\n生存类技能战斗力系数 = %s"
			print_error(string.format(str, skill_gd_zhanli, skill_atk_xs, skill_dfc_xs))
		end

		skill_zhanli = skill_gd_zhanli
					+ calc_gong_zhanli2(skill_atk_xs, skill_xs)					-- 攻击百分比技能战力
					+ calc_fang_zhanli2(skill_dfc_xs, skill_xs)					-- 防御百分比技能战力
	end


 
	-- 1.装备基础加成-加成战力
	-- 2. 武器/饰品/所有装备加成
		-- gongji_wuqi_jc_per   	武器攻击   5 7
		-- pojia_wuqi_jc_per    	武器破甲   5 7
		-- gongji_shiping_jc_per 	饰品攻击   3 6 8 9

		-- zhuagnbei_sm_jc_per      装备生命   所有装备
		-- zhuagnbei_gj_jc_per      装备攻击
		-- zhuagnbei_fy_jc_per      装备防御
		-- zhuagnbei_pj_jc_per      装备破甲
	-- 3.装备强化加成
	---------------------------------------------装备战力加成_START------------------------------------------
	local base_equip_attr_add_per_zhanli = 0
	local need_cal_cap = false
	local all_attr_tab = AttributePool.AllocAttribute()
	local role_level_cfg = RoleWGData.GetRoleExpCfgByLv(role_vo.level)

	local add_equip_attr_info = function(attr_str, value)
		all_attr_tab[attr_str] = all_attr_tab[attr_str] + value
	end

	-- 装备属性加成
	if attribute.shengming_zb_role_jc_per > 0 or attribute.gongji_zb_role_jc_per > 0 or attribute.fangyu_zb_role_jc_per > 0 or attribute.pojia_zb_role_jc_per > 0 then
		need_cal_cap = true

		local equip_attr_cfg = EquipWGData.Instance:GetEquipBaseAttrTab()
		add_equip_attr_info("max_hp", (role_level_cfg.maxhp + equip_attr_cfg.maxhp) * attribute.shengming_zb_role_jc_per * 0.0001)
		add_equip_attr_info("gong_ji", (role_level_cfg.gongji + equip_attr_cfg.gongji) * attribute.gongji_zb_role_jc_per * 0.0001)
		add_equip_attr_info("fang_yu", (role_level_cfg.fangyu + equip_attr_cfg.fangyu) * attribute.fangyu_zb_role_jc_per * 0.0001)
		add_equip_attr_info("po_jia", (role_level_cfg.pojia + equip_attr_cfg.pojia) * attribute.pojia_zb_role_jc_per * 0.0001)

		-- all_attr_tab["max_hp"] = (role_level_cfg.maxhp + equip_attr_cfg.maxhp) * attribute.shengming_zb_role_jc_per * 0.0001
		-- all_attr_tab["gong_ji"] = (role_level_cfg.gongji + equip_attr_cfg.gongji) * attribute.gongji_zb_role_jc_per * 0.0001
		-- all_attr_tab["fang_yu"] = (role_level_cfg.fangyu + equip_attr_cfg.fangyu) * attribute.fangyu_zb_role_jc_per * 0.0001
		-- all_attr_tab["po_jia"] = (role_level_cfg.pojia + equip_attr_cfg.pojia) * attribute.pojia_zb_role_jc_per * 0.0001
	end

	-- 武器攻击/破甲
	if attribute.gongji_wuqi_jc_per > 0 or attribute.pojia_wuqi_jc_per > 0 then
		need_cal_cap = true

		local weapon_attr_cfg = EquipWGData.Instance:GetWeaponBaseAttrTab()
		add_equip_attr_info("gong_ji", (role_level_cfg.gongji + weapon_attr_cfg.gongji) * attribute.gongji_wuqi_jc_per * 0.0001)
		add_equip_attr_info("po_jia", (role_level_cfg.pojia + weapon_attr_cfg.pojia) * attribute.pojia_wuqi_jc_per * 0.0001)

		-- all_attr_tab["gong_ji"] = (role_level_cfg.gongji + weapon_attr_cfg.gongji) * attribute.gongji_wuqi_jc_per * 0.0001
		-- all_attr_tab["po_jia"] = (role_level_cfg.pojia + weapon_attr_cfg.pojia) * attribute.pojia_wuqi_jc_per * 0.0001
	end

	-- 饰品攻击
	if attribute.gongji_shiping_jc_per > 0 then
		need_cal_cap = true

		local shipin_attr_cfg = EquipWGData.Instance:GetShiPinBaseAttrTab()
		add_equip_attr_info("gong_ji", (role_level_cfg.gongji + shipin_attr_cfg.gongji) * attribute.gongji_shiping_jc_per * 0.0001)

		-- all_attr_tab["gong_ji"] = (role_level_cfg.gongji + shipin_attr_cfg.gongji) * attribute.gongji_shiping_jc_per * 0.0001
	end

	-- 装备生命/攻击/防御/破甲
	if attribute.zhuagnbei_sm_jc_per > 0 or attribute.zhuagnbei_gj_jc_per > 0 or attribute.zhuagnbei_fy_jc_per > 0 or attribute.zhuagnbei_pj_jc_per > 0 then
		need_cal_cap = true

		local equip_attr_cfg = EquipWGData.Instance:GetEquipBaseAttrTab()
		add_equip_attr_info("max_hp", (role_level_cfg.maxhp + equip_attr_cfg.maxhp) * attribute.zhuagnbei_sm_jc_per * 0.0001)
		add_equip_attr_info("gong_ji", (role_level_cfg.gongji + equip_attr_cfg.gongji) * attribute.zhuagnbei_gj_jc_per * 0.0001)
		add_equip_attr_info("fang_yu", (role_level_cfg.fangyu + equip_attr_cfg.fangyu) * attribute.zhuagnbei_fy_jc_per * 0.0001)
		add_equip_attr_info("po_jia", (role_level_cfg.pojia + equip_attr_cfg.pojia) * attribute.zhuagnbei_pj_jc_per * 0.0001)

		-- all_attr_tab["max_hp"] = (role_level_cfg.maxhp + equip_attr_cfg.maxhp) * attribute.zhuagnbei_sm_jc_per * 0.0001
		-- all_attr_tab["gong_ji"] = (role_level_cfg.gongji + equip_attr_cfg.gongji) * attribute.zhuagnbei_gj_jc_per * 0.0001
		-- all_attr_tab["fang_yu"] = (role_level_cfg.fangyu + equip_attr_cfg.fangyu) * attribute.zhuagnbei_fy_jc_per * 0.0001
		-- all_attr_tab["po_jia"] = (role_level_cfg.pojia + equip_attr_cfg.pojia) * attribute.zhuagnbei_pj_jc_per * 0.0001
	end

	if need_cal_cap then
		base_equip_attr_add_per_zhanli = AttributeMgr.GetCapability(all_attr_tab)
	end
	---------------------------------------------装备战力加成_END--------------------------------------------

	-------------------------------------
	----龙魂特有的属性战力补充（不准确）----
	-- 基础属性加成-加成战力
	-- local base_attr_per_add_zhanli = 0
	-- if attribute.shengming_zb_role_jc_per > 0 or attribute.gongji_zb_role_jc_per > 0 or attribute.fangyu_zb_role_jc_per > 0 or attribute.pojia_zb_role_jc_per > 0 then
	-- 	local role_level_cfg = RoleWGData.GetRoleExpCfgByLv(role_vo.level)
	-- 	local equip_attr_cfg = EquipWGData.Instance:GetEquipBaseAttrTab()
	-- 	local all_attr_tab = AttributePool.AllocAttribute()
	-- 	all_attr_tab["max_hp"] = (role_level_cfg.maxhp + equip_attr_cfg.maxhp) * attribute.shengming_zb_role_jc_per * 0.0001
	-- 	all_attr_tab["gong_ji"] = (role_level_cfg.gongji + equip_attr_cfg.gongji) * attribute.gongji_zb_role_jc_per * 0.0001
	-- 	all_attr_tab["fang_yu"] = (role_level_cfg.fangyu + equip_attr_cfg.fangyu) * attribute.fangyu_zb_role_jc_per * 0.0001
	-- 	all_attr_tab["po_jia"] = (role_level_cfg.pojia + equip_attr_cfg.pojia) * attribute.pojia_zb_role_jc_per * 0.0001
	-- 	base_attr_per_add_zhanli = AttributeMgr.GetCapability(all_attr_tab)

	-- 	-- base_attr_per_add_zhanli = (role_level_cfg.maxhp + equip_attr_cfg.maxhp) * cap_param.shengming_zhanli_xs * attribute.shengming_zb_role_jc_per * 0.0001 * (role_vo.shengming_jc_per * 0.0001 + 1)
	-- 	-- 						+ (role_level_cfg.gongji + equip_attr_cfg.gongji) * cap_param.gongji_zhanli_xs * attribute.gongji_zb_role_jc_per * 0.0001 * (role_vo.gongji_jc_per * 0.0001 + 1)
	-- 	-- 						+ (role_level_cfg.fangyu + equip_attr_cfg.fangyu) * cap_param.fangyu_zhanli_xs * attribute.fangyu_zb_role_jc_per * 0.0001 * (role_vo.fangyu_jc_per * 0.0001 + 1)
	-- 	-- 						+ (role_level_cfg.pojia + equip_attr_cfg.pojia) * cap_param.pojia_zhanli_xs * attribute.pojia_zb_role_jc_per * 0.0001 * (role_vo.pojia_jc_per * 0.0001 + 1)
	-- end

	-- -- 武器基础攻击破甲加成
	-- local base_weapon_per_add_zhanli = 0
	-- if attribute.gongji_wuqi_jc_per > 0 or attribute.pojia_wuqi_jc_per > 0 then
	-- 	local weapon_base_attr = EquipWGData.Instance:GetWeaponBaseAttrTab()
	-- 	base_weapon_per_add_zhanli = weapon_base_attr.gongji * cap_param.gongji_zhanli_xs * attribute.gongji_wuqi_jc_per * 0.0001
	-- 								+ weapon_base_attr.pojia * cap_param.pojia_zhanli_xs * attribute.pojia_wuqi_jc_per * 0.0001
	-- end


	-- 装备强化加成
	-- local equip_base_strengthen_zhanli = 0
	-- local equip_strengthen_attr_tab = {}
	-- if attribute.equip_qianghua_per > 0 then
	-- 	equip_strengthen_attr_tab = EquipmentWGData.Instance:GetAllEquipSetengthenAttr()
	-- 	equip_base_strengthen_zhanli = equip_strengthen_attr_tab.maxhp * cap_param.shengming_zhanli_xs * attribute.equip_qianghua_per * 0.0001 * (role_vo.shengming_jc_per * 0.0001 + 1)
	-- 							+ equip_strengthen_attr_tab.gongji * cap_param.gongji_zhanli_xs * attribute.equip_qianghua_per * 0.0001 * (role_vo.gongji_jc_per * 0.0001 + 1)
	-- 							+ equip_strengthen_attr_tab.fangyu * cap_param.fangyu_zhanli_xs * attribute.equip_qianghua_per * 0.0001 * (role_vo.fangyu_jc_per * 0.0001 + 1)
	-- 							+ equip_strengthen_attr_tab.pojia * cap_param.pojia_zhanli_xs * attribute.equip_qianghua_per * 0.0001 * (role_vo.pojia_jc_per * 0.0001 + 1)
	-- end
	-------------------------------------

	local total_zhanli = math.ceil(gd_attr_zhanli + gd_per_effect_zhanli + gd_effect_per_zhanli + gd_per_attr_zhanli + gf_per_attr_zhanli + skill_zhanli + base_equip_attr_add_per_zhanli) -- + base_attr_per_add_zhanli + equip_base_strengthen_zhanli)
	-- 处理【提升战力差部分计算精度丢失】
	total_zhanli = total_zhanli > 1 and total_zhanli or 0

	if show_switch then
		local str = "总战力:%s\n固定值属性战力:%s\n固定属性 【受】 人物百分比加成影响战力:%s\n固定属性 【对】 人物百分比加成影响战力:%s\n配置固定值的百分比加成战力:%s\n总攻防属性百分比加成:%s\n技能战力:%s\n装备战力加成:%s"
		print_error(string.format(str, total_zhanli, gd_attr_zhanli, gd_per_effect_zhanli, gd_effect_per_zhanli, gd_per_attr_zhanli, gf_per_attr_zhanli, skill_zhanli, base_equip_attr_add_per_zhanli))
	end
	
	return total_zhanli
end

function AttributeMgr.IpairsSortAttr(call_back)
	local sort_attribute = AttributeMgr.SortAttribute()
	for k, v in ipairs(sort_attribute) do
		call_back(v)
	end
end

function AttributeMgr.GetAttrShowStruct(list, obj, add_table)
	for i = 1, obj.transform.childCount do
		local child = obj:FindObj("attr" .. i)
		if child then
			list["lbl_type_" .. i] = child:FindObj("name")
			list["lbl_value_" .. i] = child:FindObj("value")
			list["img_arrow_" .. i] = child:FindObj("arrow")
			list["lbl_add_value_" .. i] = child:FindObj("next_value")
			if add_table then
				list[add_table[1] .. i] = child:FindObj(add_table[2])
			end
		end
	end
	return list
end

function AttributeMgr.DisposeAttrName(name, remove_space, remvoe_maohao)
 	if remvoe_maohao then
 		name = string.gsub(name, "：", "")
		name = string.gsub(name, ":", "")
	end

	if remove_space then
		name = DeleteStrSpace(name)
	end

	return name
end

function AttributeMgr.GetCommonAttrName(key, remove)
	local text = Language.Common.AttrName[key] or ""
	if remove then
		text = string.gsub(text, "：", "")
		text = string.gsub(text, ":", "")
	end
	return text
end
