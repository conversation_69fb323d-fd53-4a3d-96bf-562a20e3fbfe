FiveElementsQiantianluView = FiveElementsQiantianluView or BaseClass(SafeBaseView)

function FiveElementsQiantianluView:__init()
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_qiantianlu")
end

function FiveElementsQiantianluView:OpenCallBack()
    FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.QTL_INFO)
end

function FiveElementsQiantianluView:ReleaseCallBack()
    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
	end
end

function FiveElementsQiantianluView:LoadCallBack()
	if not self.task_list then
		self.task_list = AsyncListView.New(QiantianluTaskRender, self.node_list["task_list"])
	end
end

function FiveElementsQiantianluView:OnFlush()
	local data_list = FiveElementsWGData.Instance:GetQianTianDataList()
	if IsEmptyTable(data_list) then
		return
	end
	
	self.task_list:SetDataList(data_list)
end

----------------------------------------QiantianluTaskRender----------------------------------------
QiantianluTaskRender = QiantianluTaskRender or BaseClass(BaseRender)

function QiantianluTaskRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["active_btn"], BindTool.Bind(self.OnBtnClick, self))

	if not self.attr_list then
		self.attr_list = {}
		local node_num = self.node_list["attr_list"].transform.childCount
        for i = 1, node_num do
            self.attr_list[i] = CommonAttrRender.New(self.node_list["attr_list"]:FindObj("attr_" .. i))
			self.attr_list[i]:SetIndex(i)
			self.attr_list[i]:SetAttrNameNeedSpace(true)
        end
	end
end

function QiantianluTaskRender:__delete()
	if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end
end

function QiantianluTaskRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	local cfg = self.data.cfg
	local descript = Language.FiveElements.CangmingTaskStr[cfg.achievement_type]
	local text = ""
	if cfg.achievement_type == FiveElementsWGData.Qiantianlu_Type.AllActive then
		text = string.format(descript, self.data.need_num, self.data.total_num, self.data.need_num)
	elseif cfg.achievement_type == FiveElementsWGData.Qiantianlu_Type.PartLevel then
		local cangming_cfg = FiveElementsWGData.Instance:GetWaistLightCfgByType(cfg.type)
		local name = cangming_cfg and cangming_cfg.name
		text = string.format(descript, name or "", self.data.need_num, self.data.total_num, self.data.need_num)
	end

	self.node_list.des_text.text.text = text
	local bundle, asset = ResPath.GetFiveElementsImg("a2_wx_qtl_" .. cfg.icon)
	self.node_list.qtl_img.image:LoadSpriteAsync(bundle, asset, function ()
	    self.node_list.qtl_img.image:SetNativeSize()
    end)
	
	local attr_list = FiveElementsWGData.Instance:GetQiantianluAttrList(self.data.cfg.id)
	for i = 1, #self.attr_list do
		self.attr_list[i]:SetData(attr_list[i])
	end

	local is_active = self.data.active_state == 1
	XUI.SetGraphicGrey(self.node_list.icon, not is_active)
	self.node_list["active_btn"]:CustomSetActive(not is_active)
	self.node_list["yiwancheng"]:CustomSetActive(is_active)

	XUI.SetGraphicGrey(self.node_list["active_btn"], self.data.total_num < self.data.need_num)
end

function QiantianluTaskRender:OnBtnClick()
	local is_active = self.data.active_state == 1
	if self.data.total_num >= self.data.need_num and not is_active then
		FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.QTL_ACTIVE, self.data.cfg.id)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.QiantianluCantActive)
	end
end