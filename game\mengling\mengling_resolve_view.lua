-- 同步 Language.MengLing.MengLingMetingColorNameList
local index_map_color_list = {
	8,
	7,
	6,
	5,
	4,
	3,
	2,

	-- 8,  --所有品质
	-- 7,
	-- 6,
	-- 5, 	--红色
	-- 4, 	--橙色
	-- 3,	--紫色
	-- 2,
}

-- 同步 Language.Bag.NameList1
-- 11 代表目前最大阶数以下
local index_map_order_list = {15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2}

MengLingResolveView = MengLingResolveView or BaseClass(SafeBaseView)

function MengLingResolveView:__init()
	self:SetMaskBg()
    self.view_name = "MengLingResolveView"
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -26), sizeDelta = Vector2(812, 524)})
	self:AddViewResource(0, "uis/view/mengling_ui_prefab", "layout_mengling_melting_view")
end

function MengLingResolveView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.MengLing.ResolveViewName

	if not self.item_grid then
        self.item_grid = MengLingMeltingGrid.New()
        self.item_grid:SetStartZeroIndex(false)
        self.item_grid:SetIsMultiSelect(true)
        self.item_grid:CreateCells({
            col = 9,
            cell_count = 45,
            list_view = self.node_list["item_grid"],
            itemRender = MengLingMeltEquipCell,
            change_cells_num = 2,
        })
        self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

	-- 品质 颜色
	self.color_list_view = AsyncListView.New(MeltingPinZhiListRender, self.node_list["color_list"])
	self.color_list_view:SetSelectCallBack(BindTool.Bind(self.SelectColorCallBack, self))
	self.cur_select_color_index = RoleWGData.GetRolePlayerPrefsInt("ml_melting_select_pinzhi_color")
	if self.cur_select_color_index < 1 then
		self.cur_select_color_index = 2 -- 默认橙色
		RoleWGData.SetRolePlayerPrefsInt("ml_melting_select_pinzhi_color", self.cur_select_color_index)
	end

	self.color_list_view:SetDefaultSelectIndex(self.cur_select_color_index)
	self.color_list_view:SetDataList(Language.MengLing.MengLingMetingColorNameList)
	self.node_list["cur_color_text"].text.text = Language.MengLing.MengLingMetingColorNameList[self.cur_select_color_index]

	-- 阶数
	self.order_list_view = AsyncListView.New(MengLingMeltingPinJieListRender, self.node_list["order_list"])
	self.order_list_view:SetSelectCallBack(BindTool.Bind(self.SelectOrderCallBack, self))

	local order_show_list = MengLingWGData.Instance:GetMengLingResolveOrderList()
	self.cur_select_order_index = RoleWGData.GetRolePlayerPrefsInt("ml_melting_select_pinjie_num")

	if self.cur_select_order_index < 1 then
		self.cur_select_order_index = #order_show_list
		RoleWGData.SetRolePlayerPrefsInt("ml_melting_select_pinjie_num", self.cur_select_order_index)
	end

	self.order_list_view:SetDataList(order_show_list)
	self.order_list_view:SetDefaultSelectIndex(self.cur_select_order_index)
	self.node_list["cur_order_text"].text.text = Language.MengLing.MengLingMetingGradeNameList[self.cur_select_order_index] or ""

	self.is_show_color_part = true
	self:OnClickSelectColor()
	XUI.AddClickEventListener(self.node_list["btn_select_color"], BindTool.Bind(self.OnClickSelectColor, self))
	XUI.AddClickEventListener(self.node_list["close_color_list_part"], BindTool.Bind(self.OnClickSelectColor, self))

	self.is_show_order_part = true
	self:OnclickSelectOrder()
	XUI.AddClickEventListener(self.node_list["btn_select_order"], BindTool.Bind(self.OnclickSelectOrder, self))
	XUI.AddClickEventListener(self.node_list["close_order_list_part"], BindTool.Bind(self.OnclickSelectOrder, self))
	XUI.AddClickEventListener(self.node_list["btn_melting"], BindTool.Bind(self.OnClickMelting, self))
	XUI.AddClickEventListener(self.node_list.button_auto_tunshi, BindTool.Bind(self.OnTunShiToggleClick, self))
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)

	local cost_item_id = MengLingWGData.Instance:GetMengLingOtherCfg("decompose_item")
	local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(cost_item_id)
	self.node_list.target_icon.image:LoadSprite(bundle, asset, function ()
		self.node_list.target_icon.image:SetNativeSize()
	end)

	XUI.AddClickEventListener(self.node_list["target_icon"], BindTool.Bind(self.OnClickTargetIcon, self))
end

function MengLingResolveView:OnClickTargetIcon()
	local cost_item_id = MengLingWGData.Instance:GetMengLingOtherCfg("decompose_item")
	TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
end

function MengLingResolveView:CloseCallBack()
	RoleWGData.SetRolePlayerPrefsInt("ml_melting_select_pinzhi_color", self.cur_select_color_index)
	RoleWGData.SetRolePlayerPrefsInt("ml_melting_select_pinjie_num", self.cur_select_order_index)
end

function MengLingResolveView:ReleaseCallBack()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)

	if self.item_grid then
        self.item_grid:DeleteMe()
        self.item_grid = nil
    end

	if self.color_list_view then
		self.color_list_view:DeleteMe()
		self.color_list_view = nil
	end

	if self.order_list_view then
		self.order_list_view:DeleteMe()
		self.order_list_view = nil
	end

	if self.auto_tunshi_alert then
		self.auto_tunshi_alert:DeleteMe()
		self.auto_tunshi_alert = nil
	end

	self.cur_select_color_index = nil
	self.cur_select_order_index = nil
end

function MengLingResolveView:OnFlush()
    local item_list = MengLingWGData.Instance:GetMengLingBagDataList()
    self.item_grid:SetDataList(item_list)
	self:OneKeySelect()
	self:SetAutoTunShiSelect()
end

-- 选择道具回调
function MengLingResolveView:OnBagSelectItemCB(cell)
    self:CalcSelect()
end

-- 一键选择
function MengLingResolveView:OneKeySelect()
	local color = index_map_color_list[self.cur_select_color_index] or 0
	local order = index_map_order_list[self.cur_select_order_index] or 0

    self.item_grid:SetMeltingOneKeySelcet(color, order)
	self:CalcSelect()
end

-- 选择品质回调
function MengLingResolveView:SelectColorCallBack(cell)
	local data = cell and cell:GetData()
	if data == nil then
		return
	end

	local index = cell:GetIndex()
	if index == self.cur_select_color_index then
		return
	end

	self.cur_select_color_index = index
	self.node_list["cur_color_text"].text.text = Language.MengLing.MengLingMetingColorNameList[self.cur_select_color_index]
	self:OnClickSelectColor()
	self:OneKeySelect()
end

-- 展开品质筛选列表
function MengLingResolveView:OnClickSelectColor()
	self.is_show_color_part = not self.is_show_color_part
	self.node_list["color_list_part"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_down"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_up"]:SetActive(not self.is_show_color_part)
end

-- 选择阶数回调
function MengLingResolveView:SelectOrderCallBack(cell)
	local data = cell and cell:GetData()
	if data == nil then
		return
	end

	local data_index = cell.index
	if data_index == self.cur_select_order_index then
		return
	end

	self.cur_select_order_index = data_index
	self.node_list["cur_order_text"].text.text = Language.MengLing.MengLingMetingGradeNameList[self.cur_select_order_index]
	self:OnclickSelectOrder()
	self:OneKeySelect()
end

-- 展开阶数筛选列表
function MengLingResolveView:OnclickSelectOrder()
	self.is_show_order_part = not self.is_show_order_part
	self.node_list["order_list_part"]:SetActive(self.is_show_order_part)
	self.node_list["order_arrow_down"]:SetActive(self.is_show_order_part)
	self.node_list["order_arrow_up"]:SetActive(not self.is_show_order_part)
end

-- 熔炼
function MengLingResolveView:OnClickMelting()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.MengLing.MeltingError)
		return
	end

	self:OnSendMelting()
end

function MengLingResolveView:OnSendMelting()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		return
	end

	local destroy_item_list = {}
	local has_can_wear_item = false

	for k,v in pairs(select_list) do

		if not has_can_wear_item then
			local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(v.item_id)

			if not IsEmptyTable(equip_cfg) then
				local can_wear = MengLingWGData.Instance:GetMengLingItemUpFlag(equip_cfg.seq, equip_cfg.slot, equip_cfg.item_id)
			
				if can_wear then
					has_can_wear_item = true
				end
			end
		end

		table.insert(destroy_item_list, {item_id = v.item_id, bag_index = v.index, count = v.num})
	end

	local decompose_func = function()
		MengLingWGCtrl.Instance:OnCSDreamSpiritEquipDecompos(#destroy_item_list, destroy_item_list)
		self:Close()
	end

	if has_can_wear_item then
		TipWGCtrl.Instance:OpenConfirmAlertTips(Language.MengLing.MengLingDeComposeCanWearTip, decompose_func)
	else
		decompose_func()
	end
end

function MengLingResolveView:CalcSelect()
	local score = 0
	local select_list = self.item_grid:GetAllSelectCell()
	
	if not IsEmptyTable(select_list) then
		for k,v in pairs(select_list) do
			local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(v.item_id)

			if equip_cfg and equip_cfg.score then
				score = score + equip_cfg.score * v.num
			end
		end
	end

	self.node_list.melting_value.text.text = score
end

function MengLingResolveView:OnTunShiToggleClick()
	local flag = MengLingWGCtrl.Instance:GetIsAutoTunShi()

	if flag then
		MengLingWGCtrl.Instance:SetIsAutoTunShi()
		self:SetAutoTunShiSelect()
	else
		if not self.auto_tunshi_alert then
			local alert = Alert.New()
			alert:SetOkFunc(function ()
				MengLingWGCtrl.Instance:SetIsAutoTunShi()
				self:SetAutoTunShiSelect()
				MengLingWGCtrl.Instance:AutoTunShiEquip()
			end)
			alert:SetCancelString(Language.MengLing.NotResolveDesc)
			alert:SetOkString(Language.MengLing.ResolveBtnDesc)
			alert:SetLableString(Language.MengLing.AutoTunShiTxt)
			self.auto_tunshi_alert = alert
		end

		self.auto_tunshi_alert:Open()
	end
end

function MengLingResolveView:SetAutoTunShiSelect()
	local flag = MengLingWGCtrl.Instance:GetIsAutoTunShi()
	self.node_list.image_tunshi_slt:CustomSetActive(flag)
end

---------------------------------------------DeComposeGrid-----------------------------------------------
MengLingMeltingGrid = MengLingMeltingGrid or BaseClass(AsyncBaseGrid)

function MengLingMeltingGrid:SetMeltingOneKeySelcet(color_index, suit_index)
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	for i = 1, self.has_data_max_index do
		local data = self.cell_data_list[i]

		if not IsEmptyTable(data) then
			local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(data.item_id)
			local _, color = ItemWGData.Instance:GetItemColor(data.item_id)

			if not IsEmptyTable(equip_cfg) then
				-- 所有套装/单套装
				local suit_select = (suit_index == 15) or ((equip_cfg.seq + 1) < suit_index)
				local color_select = color_index == 6 or color <= color_index

				if suit_select and color_select and (self.cur_multi_select_num < ESOTERICA_DEFINE.MAX_RESLOVE) then
					self.select_tab[1][i] = true
					self.cur_multi_select_num = self.cur_multi_select_num + 1
				end
			end
		end
	end

	self:__DoRefreshSelectState()
end

function MengLingMeltingGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= ESOTERICA_DEFINE.MAX_RESLOVE then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.MengLing.ResolveLimit)
            return true
        end
    end

    return false
end

-------------------------------------------
--熔炼格子
MengLingMeltEquipCell = MengLingMeltEquipCell or BaseClass(ItemCell)
function MengLingMeltEquipCell:__init()
	self:SetIsShowTips(false)
	self:UseNewSelectEffect(true)
end

function MengLingMeltEquipCell:SetSelect(is_select)
	self:SetSelectEffect(is_select)
end

----------------CKPinZhiListRender-------------------
MeltingPinZhiListRender = MeltingPinZhiListRender or BaseClass(BaseRender)
function MeltingPinZhiListRender:OnFlush()
	self.node_list.lbl_pinzhi_name.text.text = self.data
	self.node_list.select_pinzhi_bg:SetActive(self.is_select)
	-- self.node_list.line:SetActive(not self.is_select)
end

function MeltingPinZhiListRender:OnSelectChange(is_select)
	if self.node_list.select_pinzhi_bg then
		self.node_list.select_pinzhi_bg:SetActive(is_select)
		-- self.node_list.line:SetActive(not is_select)
	end
end

----------------CKPinJieListRender-------------------
MengLingMeltingPinJieListRender = MengLingMeltingPinJieListRender or BaseClass(BaseRender)
function MengLingMeltingPinJieListRender:OnFlush()
	self.node_list.lbl_pinjie_name.text.text = self.data.name
	self.node_list.select_pinjie_bg:SetActive(self.is_select)
	-- self.node_list.line:SetActive(not self.is_select)
end

function MengLingMeltingPinJieListRender:OnSelectChange(is_select)
	if self.node_list.select_pinjie_bg then
		self.node_list.select_pinjie_bg:SetActive(is_select)
		-- self.node_list.line:SetActive(not is_select)
	end
end