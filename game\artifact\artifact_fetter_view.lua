--弃用
ArtifactFetterView = ArtifactFetterView or BaseClass(SafeBaseView)

function ArtifactFetterView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_fetter_view")
end

function ArtifactFetterView:__delete()

end

function ArtifactFetterView:SetDataAndOpen(fetter_seq)
	self.fetter_seq = fetter_seq
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function ArtifactFetterView:LoadCallBack()
		if self.fetter_attr_list == nil then
        self.fetter_attr_list = {}
        local node_num = self.node_list["fetter_attr_list"].transform.childCount
        for i = 1, node_num do
            self.fetter_attr_list[i] = CommonAddAttrRender.New(self.node_list["fetter_attr_list"]:FindObj("attr_" .. i))
        end
    end

    XUI.AddClickEventListener(self.node_list["act_btn"], BindTool.Bind(self.OnBtnAct, self))
end

function ArtifactFetterView:ReleaseCallBack()
	if self.fetter_attr_list then
        for k, v in pairs(self.fetter_attr_list) do
            v:DeleteMe()
        end
        self.fetter_attr_list = nil
    end
end

function ArtifactFetterView:CloseCallBack()
	self.fetter_seq = nil
end

function ArtifactFetterView:ShowIndexCallBack()

end

function ArtifactFetterView:OnFlush(param_t, index)
	if not self.fetter_seq then
		return
	end

	local fetter_cfg = ArtifactWGData.Instance:GetFetterCfg(self.fetter_seq)
	if not fetter_cfg then
		return
	end

	self:FlushAtrrInfo(fetter_cfg)
	self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(fetter_cfg.skill_id))
	self.node_list.skill_name.text.text = fetter_cfg.skill_name
	local need_num = 0
	local have_num = 0
	for i = 1, 2 do
		local artifact_seq = fetter_cfg["artifact_seq" .. i]
		self.node_list["condition_" .. i]:SetActive(artifact_seq ~= "")
		if artifact_seq ~= "" then
			need_num = need_num + 1
			local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(artifact_seq)
			local is_act = artifact_data.level > 0
			have_num = is_act and have_num + 1 or have_num
			--local img_name = is_act and "a1_sq_xz" or "a1_sq_wxz"
			local bundle, asset = ResPath.GetCommon("a2_ty_zs_d_1")
		    self.node_list["act_img_" .. i].image:LoadSprite(bundle, asset, function()
		    	self.node_list["act_img_" .. i].image:SetNativeSize()
		    end)

		    local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(artifact_seq)
		    if artifact_cfg then
		    	local color = is_act and COLOR3B.DEFAULT_NUM or COLOR3B. RED
		    	local name = ToColorStr(artifact_cfg.name, color)
		    	local condition_str = string.format(Language.Artifact.ActStr, name)
		    	self.node_list["act_text_" .. i].text.text = condition_str
		    end
		end
	end

	self.node_list.skill_desc.text.text = fetter_cfg.skill_desc
	self.node_list.act_title_label.text.text = string.format(Language.Artifact.ActCondition, have_num, need_num)
	local state = ArtifactWGData.Instance:GetFetterDataBySeq(self.fetter_seq)
	self.node_list.have_act:SetActive(state == 1)
	self.node_list.act_btn:SetActive(state == 0)
	XUI.SetButtonEnabled(self.node_list["act_btn"], have_num >= need_num)
	self.node_list.act_remind:SetActive(have_num >= need_num)
end

function ArtifactFetterView:FlushAtrrInfo(fetter_cfg)
	local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(fetter_cfg, nil, "attr_id", "attr_value", 1, 5)
	for k,v in pairs(self.fetter_attr_list) do
        v:SetData(attr_list[k])
    end
end

function ArtifactFetterView:OnBtnAct()
	local state = ArtifactWGData.Instance:GetFetterDataBySeq(self.fetter_seq)
	if state == 0 then
		ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.ACTIVE_FETTER, self.fetter_seq)
	end
end

-- 使用特效
function ArtifactFetterView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["effect"]})
end