-- 最强仙尊
MostVenerableView = MostVenerableView or BaseClass(SafeBaseView)

function MostVenerableView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self:SetMaskBg()
	self.default_index = TabIndex.most_venerable

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(TabIndex.most_venerable, "uis/view/most_venerable_ui_prefab", "layout_most_venerable")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function MostVenerableView:__delete()
end

function MostVenerableView:OpenCallBack()
	MostVenerableWGCtrl.Instance:RequestRankInfo()
end

function MostVenerableView:LoadCallBack()
	local bundle, assert = ResPath.GetRawImagesPNG("a3_zqxz_bg_1")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function ()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	self.node_list.title_view_name.text.text = Language.Field1v1.ZuiQiangXianZun
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		self.tabbar:Init(Language.Field1v1.ZQXZTabGroup, nil, ResPath.CommonBundleName)
	end
end

function MostVenerableView:LoadIndexCallBack(index)
	if index == TabIndex.most_venerable then
		self:InitMostVenerableView()
	end
end

function MostVenerableView:ShowIndexCallBack(index)
end

function MostVenerableView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.reward_show_list then
		self.reward_show_list:DeleteMe()
		self.reward_show_list = nil
	end

	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function MostVenerableView:OnFlush(param_t, index)
	if index == TabIndex.most_venerable  then
		local rank_list_info = MostVenerableWGData.Instance:GetRankList()
		self.rank_list:SetDataList(rank_list_info)
	
		local my_rank_info = MostVenerableWGData.Instance:GetMyRankInfo()
		if not IsEmptyTable(my_rank_info) then
			self.node_list.txt_my_rank_count.text.text = my_rank_info.rank_pos + 1;
			self.node_list.txt_my_role_name.text.text = my_rank_info.role_name;
			self.node_list.txt_my_capability.text.text = my_rank_info.capability;
		end
	end
end

function MostVenerableView:InitMostVenerableView()
	self.node_list.txt_title.text.text = Language.Field1v1.ZQXZDesTitle;
	self.node_list.txt_show_desc.text.text = Language.Field1v1.ZQXZShowDes;

	XUI.AddClickEventListener(self.node_list["btn_goto"], BindTool.Bind(self.OnClickGoToJJCBtn, self))

	local config = MostVenerableWGData.Instance:GetMostVenerableConfig()
	local show_reward = config.show_reward
	if not self.reward_show_list then
		self.reward_show_list = AsyncListView.New(ItemCell, self.node_list.reward_list_root)
		self.reward_show_list:SetStartZeroIndex(true)
		self.reward_show_list:SetDataList(show_reward)
	end

	if not self.rank_list then
		self.rank_list = AsyncListView.New(MostVenerableRankRender, self.node_list.rank_list)
	end

	-- 模型
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_root"])
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		self:FlushModel(config)
	end
end

function MostVenerableView:FlushModel(model_cfg)
	if IsEmptyTable(model_cfg) then
		return
	end

	local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
	-- display_data.model_click_func = function ()
	-- 	TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	-- end
	self.model_display:SetData(display_data)

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.model_root.rect, pos_x, pos_y)

	if model_cfg["display_scale"] then
		local scale = model_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(self.node_list.model_root.transform, scale, scale, scale)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		self.node_list.model_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function MostVenerableView:OnClickGoToJJCBtn()
	ViewManager.Instance:Open(GuideModuleName.ActJjc, TabIndex.arena_field1v1)
end

------------------------------------------------
-- 排行榜render
------------------------------------------------
MostVenerableRankRender = MostVenerableRankRender or BaseClass(BaseRender)
function MostVenerableRankRender:__init()
end

function MostVenerableRankRender:__delete()
end

function MostVenerableRankRender:OnFlush()
	if not self.data then
		return
	end
	self.node_list.txt_rank_count.text.text = self.index
	self.node_list.txt_name.text.text = self.data.rank_pos == -1 and Language.Field1v1.UpvoteRankNoRank or self.data.role_name
	self.node_list.txt_capability.text.text = self.data.capability

	local is_three = self.index <= 3
	self.node_list.txt_rank_count:SetActive(not is_three)

	if self.node_list.rawimg_bg then
		self.node_list.rawimg_bg:SetActive(is_three)
		if is_three then
			self.node_list.rawimg_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_zqxz_di_" .. self.index))
		end
	end
	if self.node_list.img_rank_bg then
		self.node_list.img_rank_bg:SetActive(is_three)
		if is_three then
			self.node_list.img_rank_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_panking_" .. self.index))
		end
	end

end