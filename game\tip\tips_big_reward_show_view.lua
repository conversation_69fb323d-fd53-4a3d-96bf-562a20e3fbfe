TipsBigRewardShowView = TipsBigRewardShowView or BaseClass(SafeBaseView)


function TipsBigRewardShowView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    self.full_screen = true
    self.view_name = "TipsBigRewardShowView"
    self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_common_big_reward")
end

function TipsBigRewardShowView:ReleaseCallBack()
	if nil ~= self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.wb_tween then
		self.wb_tween:Kill()
		self.wb_tween = nil
	end

	if self.delay_sync_anim_timer then
		self.delay_sync_anim_timer:CancelQuest()
		self.delay_sync_anim_timer = nil
	end

	self.id_list = nil
	self.cur_idx = 0
	self.can_click = true
end

function TipsBigRewardShowView:LoadCallBack()
	if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_display"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
	XUI.AddClickEventListener(self.node_list.btn_next, BindTool.Bind(self.OnClickNext, self))						--确定关闭界面
	self.cur_idx = 0
end

function TipsBigRewardShowView:CloseCallBack()
	if self.model_display then
		self.model_display:Reset()
	end

	self.id_list = nil
	self.cur_idx = 0
	self.can_click = true

	if self.wb_tween then
		self.wb_tween:Kill()
		self.wb_tween = nil
	end

	if self.delay_sync_anim_timer then
		self.delay_sync_anim_timer:CancelQuest()
		self.delay_sync_anim_timer = nil
	end

	GlobalEventSystem:Fire(OtherEventType.Common_Reward_Close3)

	if self.close_call_back then
		self.close_call_back()
		self.close_call_back = nil
	end
end

function TipsBigRewardShowView:SetDataAndOpen(id_list)
	self.id_list = id_list or {}
	if not IsEmptyTable(self.id_list) then
		self:Open()
	else
		self.id_list = {}
		self.cur_idx = 0
	end
end

function TipsBigRewardShowView:SetViewCloseCallBack(call_back)
	self.close_call_back = call_back
end

--刷新数据
function TipsBigRewardShowView:OnFlush()
	self.cur_idx = self.cur_idx + 1
	self:ResetTitleState()
	self:StartPlayTitleState()
	self:FlushDisplayModel()
end

function TipsBigRewardShowView:ResetTitleState()
	for i = 1, 4 do
		self.node_list["wb_" .. i]:SetActive(false)
	end
	self.tween_count = 0
	self.node_list.close_hint:SetActive(false)
end

function TipsBigRewardShowView:StartPlayTitleState()
	TryDelayCall(self, function ()
		self:PlayTitleState()
	end, 0, "delay_big_reward_tween_show")
end

function TipsBigRewardShowView:PlayTitleState()
	self.tween_count = self.tween_count + 1
	if self.tween_count < 5 then
		self.can_click = false
		self.node_list["wb_" .. self.tween_count]:SetActive(true)
		self.wb_tween = UITween.DoUpDownCrashTween(self.node_list["wb_" .. self.tween_count],
		{
			show_type = DG.Tweening.Ease.OutBack,
			scale_time = 0.3,
			start_scale = Vector3(5, 5, 5)
		},
		function()
			self:PlayTitleState()
		end)
	else
		self.wb_tween:Kill()
		self.wb_tween = nil
		self.can_click = true

		self.node_list.close_hint:SetActive(true)
		-- if self.delay_sync_anim_timer then
		-- 	GlobalTimerQuest:CancelQuest(self.delay_sync_anim_timer)
		-- 	self.delay_sync_anim_timer = nil
		-- end

		-- if not self.delay_sync_anim_timer then
		-- 	self.delay_sync_anim_timer = GlobalTimerQuest:AddDelayTimer(function ()
		-- 		self:OnClickNext()
		-- 	end, 2)
		-- end
	end
end

function TipsBigRewardShowView:FlushDisplayModel()
    local show_cfg = self.id_list and self.id_list[self.cur_idx] or {}
    if IsEmptyTable(show_cfg) then return end
    local model_show_itemid = show_cfg.model_show_itemid

    local display_data = {}
	display_data.should_ani = true
	if show_cfg.model_show_itemid ~= 0 and show_cfg.model_show_itemid ~= "" then
		local split_list = string.split(show_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_cfg.model_show_itemid
		end
	end
	
	display_data.bundle_name = show_cfg["model_bundle_name"]
    display_data.asset_name = show_cfg["model_asset_name"]
    local model_show_type = tonumber(show_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    self.model_display:SetData(display_data)
    local scale = 1
	if show_cfg.model_scale and show_cfg.model_scale ~= "" then
        scale = tonumber(show_cfg.model_scale)
    end
    Transform.SetLocalScaleXYZ(self.node_list["model_display"].transform, scale, scale, scale)
    local pos_x, pos_y, pos_z = 0, 0, 0
	if show_cfg.model_pos and show_cfg.model_pos ~= "" then
		local pos_list = string.split(show_cfg.model_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list["model_display"].rect, pos_x, pos_y, pos_z)

	if show_cfg.rotation and show_cfg.rotation ~= "" then
		local rotation_tab = string.split(show_cfg.rotation,"|")
		self.node_list["model_display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function TipsBigRewardShowView:OnClickNext()
	if self.can_click then
		self.model_display:Reset()
		local index = self.cur_idx + 1
		if self.delay_sync_anim_timer then
			GlobalTimerQuest:CancelQuest(self.delay_sync_anim_timer)
			self.delay_sync_anim_timer = nil
		end
		if not IsEmptyTable(self.id_list[index]) then
			self:Flush()
		else
			self:Close()
		end
	end
end