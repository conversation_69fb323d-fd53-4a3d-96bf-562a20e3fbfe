--[[
	【脚本说明】
	常用的子面板Render合集
	减少实现相同功能Render的定义
]]



--[[
	CommonAttrRender
	说明：【属性展示】    属性名  +当前属性
	例子：			   生命    +1000
	node_list = {
		attr_name,
		attr_value,
	}
	data = {
		attr_str,		-- 101 or max_hp
		attr_value,
	}
	可结合 EquipWGData.GetSortAttrListByCfg(cfg)
]]
CommonAttrRender = CommonAttrRender or BaseClass(BaseRender)
function CommonAttrRender:__init()
	self.name_need_space = false
	self.need_mao_hao = false
	self.need_per = false
	self.value_prefix = ""--"+"
	self.operate_click_call_back = nil
end

function CommonAttrRender:ReleaseCallBack()
	self.click_call_back = nil
end

function CommonAttrRender:LoadCallBack()
	if self.node_list and self.node_list.operate_btn then
		XUI.AddClickEventListener(self.node_list.operate_btn, BindTool.Bind(self.OnOperateBtnClick, self))   
	end
end

function CommonAttrRender:OnOperateBtnClick()
	if self.operate_click_call_back then
		self.operate_click_call_back(self)
	end
end

function CommonAttrRender:SetOperateBtnClickCallBack(operate_click_call_back)
	self.operate_click_call_back = operate_click_call_back
end

-- 设置属性名是否需要添加空格
function CommonAttrRender:SetAttrNameNeedSpace(bool)
	self.name_need_space = bool
end

-- 设置是否需要属性名后面添加冒号
function CommonAttrRender:SetAttrNeedMaoHao(bool)
	self.need_mao_hao = bool
end

-- 设置属性值的前缀
function CommonAttrRender:SetAttrValuePrefix(symbol)
	self.value_prefix = symbol
end

function CommonAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	local is_per = self.data.is_per or EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
	if not is_per and self.need_per then
		is_per = self.need_per
		self.need_per = false
	end
    local per_desc = is_per and "%" or ""
    local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
	local attr_name = nil ~= self.data.attr_name and self.data.attr_name or EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
	if nil == attr_name or attr_name == '' then
		attr_name = self.data.attr_name or ''
	end
    self.node_list.attr_name.text.text = attr_name
    self.node_list.attr_value.text.text = string.format("%s%s%s", self.value_prefix, value_str, per_desc)

	local special_attr_color = SPECIAL_ATTR_COLOR[self.data.attr_str]
	
	if nil ~= special_attr_color then
		-- self.node_list.attr_name.text.supportRichText = true
		-- self.node_list.attr_value.text.supportRichText = true
		self.node_list.attr_name.text.text = ToColorStr(self.node_list.attr_name.text.text, special_attr_color)
		self.node_list.attr_value.text.text = ToColorStr(self.node_list.attr_value.text.text, special_attr_color)
	end

    self.view:SetActive(true)
end

function CommonAttrRender:SetNeedPer(is_hide)
	self.need_per = is_hide
end

function CommonAttrRender:ResetName(new_name, color)
	local new_name_str = new_name;

	if color then
		new_name_str = ToColorStr(new_name, color)
	end
	self.node_list.attr_name.text.text = new_name_str
end

function CommonAttrRender:ResetAttrVlaue(attr_value_str, color)
	local new_attr_value_str = attr_value_str;

	if color then
		new_attr_value_str = ToColorStr(attr_value_str, color)
	end
	self.node_list.attr_value.text.text = new_attr_value_str
end

function CommonAttrRender:ResetAttrColor(color, is_only_value)
	if IsEmptyTable(self.data) then
		return
	end

	local name_str = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
	if not is_only_value then
		self.node_list.attr_name.text.text = color == nil and self.node_list.attr_name.text.text or ToColorStr(self.node_list.attr_name.text.text, color)
	end

	self.node_list.attr_value.text.text = color == nil and self.node_list.attr_value.text.text or ToColorStr(self.node_list.attr_value.text.text, color)
end

function CommonAttrRender:SetOperateBtnStatus(is_show)
	if self.node_list and self.node_list.operate_btn then
		self.node_list.operate_btn:CustomSetActive(is_show)
	end
end

--[[
	CommonAddAttrRender
	说明：【属性展示】    属性名  当前属性  ↑ 下级属性或加成属性
	例子：			  生命    1000		↑ 100
	node_list = {
		attr_name,
		attr_value,
		arrow,
		add_value,
		remind,        -- 红点节点
		attr_lock,     -- 锁节点  
		click_block,   -- 点击响应节点
		effect,        -- 特效节点
	}
	data = {
		attr_str,		-- 101 or max_hp
		attr_value,
		add_value,
		is_remind,           -- 显示红点
		is_lock,            -- 不显示锁
		click_func = function()     -- 点击回调
		end,
		show_effect,
	}
	可结合
	显示差值(attr_str) EquipWGData.GetSortAttrListHaveNextByCfg(cur_cfg, next_cfg)
	显示差值(attr_id)  EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, next_cfg, type_key_str, value_key_str, read_start, read_end)
	显示配置值 EquipWGData.GetSortAttrListHaveAddByCfg(cur_cfg, next_cfg)
]]
CommonAddAttrRender = CommonAddAttrRender or BaseClass(BaseRender)
function CommonAddAttrRender:__init()
    self.real_hide_next = true
    self.name_need_space = false
	self.need_mao_hao = false
	self.need_per = false
	self.is_show_difference_value = true		-- true:显示差值，false:显示next配置值
	self.arrow_tweener_need = false
	self.special_color = nil
end

function CommonAddAttrRender:__delete()
    self:KillArrowTween()
end

function CommonAddAttrRender:KillArrowTween()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end
end

-- 动作要求 箭头的锚点
-- AnchorsMin {x = 0, y = 1}
-- AnchorsMax {x = 0, y = 1}
-- Pivot {x = 0.5, y = 0.5}
function CommonAddAttrRender:LoadCallBack()
	self:KillArrowTween()
	local tween_time = 0.8
    local node = self.node_list.arrow
	if node then
        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -18)
        self.arrow_tweener = node.rect:DOAnchorPosY(-12, tween_time)
        self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end

	if self.node_list.click_block then
		XUI.AddClickEventListener(self.node_list.click_block, BindTool.Bind(self.OnClickCustomFunc, self))
	end
end
--是否需要箭头
function CommonAddAttrRender:SetAttrArrowTween(arrow_tweener_need)
	self.arrow_tweener_need = arrow_tweener_need
end

-- 设置属性名是否需要添加空格
function CommonAddAttrRender:SetAttrNameNeedSpace(bool)
	self.name_need_space = bool
end

-- 设置是否需要属性名后面添加分号
function CommonAddAttrRender:SetAttrNeedMaoHao(bool)
	self.need_mao_hao = bool
end

-- 设置特殊属性颜色
function CommonAddAttrRender:SetAttrSpecialColor(color)
	self.special_color = color
end

-- 自定义func
function CommonAddAttrRender:OnClickCustomFunc()
	if self.data and self.data.click_func then
		self.data.click_func()
	end
end

-- 有些系统属性 每级只提升部分属性 组件隐藏，实现属性位置对齐
-- 例子：
-- 属性名  当前属性  ↑ 下级属性或加成属性
-- 属性名  当前属性  ↑ 下级属性或加成属性
-- 属性名  当前属性
-- 属性名  当前属性
function CommonAddAttrRender:SetRealHideNext(is_hide)
    self.real_hide_next = is_hide
end

function CommonAddAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	local is_per = self.data.is_per or EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
	if not is_per and self.need_per then
		is_per = self.need_per
		self.need_per = false
	end

	local per_desc = is_per and "%" or ""
    local value_str = (is_per and self.data.attr_value / 100 or self.data.attr_value) or 0
	local attr_name = self.data.attr_name or EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)

	-- 临时处理，文本替换后改回
	if self.node_list.attr_name.text then
		self.node_list.attr_name.text.text = attr_name
		self.node_list.attr_value.text.text = value_str .. per_desc
	else
		self.node_list.attr_name.tmp.text = attr_name
		self.node_list.attr_value.tmp.text = value_str .. per_desc
	end


    self.node_list.arrow:SetActive(self.data.hide_arrow ~= true)
    self.node_list.add_value:SetActive(true)
    self.node_list.arrow.image.enabled = true
    if self.data.add_value and self.data.add_value > 0 then
        value_str = is_per and self.data.add_value / 100 or self.data.add_value
		-- 临时处理，文本替换后改回
		if self.node_list.add_value.text then
			self.node_list.add_value.text.text = value_str .. per_desc
		else
			self.node_list.add_value.tmp.text = value_str .. per_desc
		end
    else
        if self.real_hide_next then
            self.node_list.arrow:SetActive(false)
            self.node_list.add_value:SetActive(false)
		else
            self.node_list.arrow.image.enabled = false
			-- 临时处理，文本替换后改回
			if self.node_list.add_value.text then
				self.node_list.add_value.text.text = ""
			else
				self.node_list.add_value.tmp.text = ""
			end

        end
    end

	if self.data.add_value and self.data.add_value > 0 and self.arrow_tweener_need then
		self.node_list.arrow:SetActive(false)
		self.node_list.add_value:SetActive(true)
	end

	local special_attr_color = SPECIAL_ATTR_COLOR[self.data.attr_str]
	
	if special_attr_color == nil then
		special_attr_color = self.special_color
	end

	if nil ~= special_attr_color then
		-- 临时处理，文本替换后改回
		if self.node_list.attr_name.text then
			-- self.node_list.attr_name.text.supportRichText = true
			-- self.node_list.attr_value.text.supportRichText = true
			-- self.node_list.add_value.text.supportRichText = true
	
			self.node_list.attr_name.text.text = ToColorStr(self.node_list.attr_name.text.text, special_attr_color)
			self.node_list.attr_value.text.text = ToColorStr(self.node_list.attr_value.text.text, special_attr_color)
			self.node_list.add_value.text.text = ToColorStr(self.node_list.add_value.text.text, special_attr_color)
		else
			-- self.node_list.attr_name.text.supportRichText = true
			-- self.node_list.attr_value.text.supportRichText = true
			-- self.node_list.add_value.text.supportRichText = true
	
			self.node_list.attr_name.tmp.text = ToColorStr(self.node_list.attr_name.text.text, special_attr_color)
			self.node_list.attr_value.tmp.text = ToColorStr(self.node_list.attr_value.text.text, special_attr_color)
			self.node_list.add_value.tmp.text = ToColorStr(self.node_list.add_value.text.text, special_attr_color)
		end

	end

	-- 处理红点显示
	if self.node_list.remind then
		local show_remind = self.data.is_remind == true
		self.node_list.remind:SetActive(show_remind)
	end

	-- 处理锁显示
	if self.node_list.attr_lock then
		local show_lock = self.data.is_lock == true
		self.node_list.attr_lock:SetActive(show_lock)
	end

	-- 处理特效显示
	if self.node_list.effect then
		local is_show_effect = self.data.show_effect == true
		self.node_list.effect:SetActive(is_show_effect)
	end

	-- 处理点击响应节点显示
	local is_show_click_block = self.data.click_func ~= nil
	if self.node_list.click_block then
		self.node_list.click_block:SetActive(is_show_click_block)
	end

    self.view:SetActive(true)
end

function CommonAddAttrRender:ResetName(new_name, color)
	local new_name_str = new_name;

	if color then
		new_name_str = ToColorStr(new_name, color)
	end
	self.node_list.attr_name.text.text = new_name_str
end

function CommonAddAttrRender:ResetAddVlaue(add_value_str, color)
	local new_add_value_str = add_value_str;

	if color then
		new_add_value_str = ToColorStr(add_value_str, color)
	end
	self.node_list.add_value.text.text = new_add_value_str
end

function CommonAddAttrRender:ResetAttrVlaue(attr_value_str, color)
	local new_attr_value_str = attr_value_str;

	if color then
		new_attr_value_str = ToColorStr(attr_value_str, color)
	end
	self.node_list.attr_value.text.text = new_attr_value_str
end

function CommonAddAttrRender:ResetAddAttrVlaue(add_attr_value_str, color)
	local new_add_attr_value_str = add_attr_value_str;

	if color then
		new_add_attr_value_str = ToColorStr(add_attr_value_str, color)
	end
	self.node_list.add_value.text.text = new_add_attr_value_str
end

function CommonAddAttrRender:SetNeedPer(is_hide)
	self.need_per = is_hide
end

function CommonAddAttrRender:SetAttrValueVisible(is_visible)
	self.node_list.attr_value:CustomSetActive(is_visible)
end

-- UI_tongyonghengsao   delay_timer 延迟时间
function CommonAddAttrRender:PlayAttrValueUpEffect(delay_timer)
	if not self.node_list.attr_value_upeffect then
		return
	end

	local show_up_effect = function ()
		local parent_obj = self.node_list.attr_value_upeffect.transform.parent.gameObject
		if self.node_list.attr_value_upeffect and parent_obj.activeSelf == true then
			local particls = self:GetAttrInfoUpEffectParSys()

			if particls and particls.Length > 0 then
				for i = 0, particls.Length - 1 do
					local effect = particls[i]
	
					if effect then
						if effect.isPlaying then
							effect:Stop()
							effect:Clear()
						end

						effect:Play()
					end
				end
			end
		end
	end

	if delay_timer and delay_timer > 0 then
		ReDelayCall(self, function()
			show_up_effect()
		end, delay_timer, "PlayAttrValueUpEffect")
	else
		show_up_effect()
	end
end

function CommonAddAttrRender:GetAttrInfoUpEffectParSys()
	return self.node_list.attr_value_upeffect:GetComponentsInChildren(typeof(UnityEngine.ParticleSystem))
end

-- 设置是否锁定
function CommonAddAttrRender:SetRenderLock(is_lock)
	if self.node_list.un_lock then
		self.node_list.un_lock:CustomSetActive(not is_lock)
	end

	if self.node_list.lock then
		self.node_list.lock:CustomSetActive(is_lock)
	end
end