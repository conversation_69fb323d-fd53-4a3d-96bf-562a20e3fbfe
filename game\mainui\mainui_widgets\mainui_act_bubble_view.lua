MainuiActBubbleView = MainuiActBubbleView or BaseClass(SafeBaseView)

function MainuiActBubbleView:__init()
	-- self.view_name = GuideModuleName.MainuiActBubbleView
	
	self.mask_alpha = 0
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/main_ui_prefab", "layout_act_bubble")
	self.act_data = nil
	self.act_remind = {}
	self.function_btn_list = {}
	self.function_btn_pool = {}
	self.act_btn_list = {}
	self.act_btn_pool = {}
	self.rand_open_icon = {}						-- 开启中的版本活动（在版本活动面板中显示的）
	self.rand_open_icon[RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL] = {}	--正常随机活动
	self.rand_open_icon[RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_VERSION] = {}	--版本随机活动
	self.show_pos = nil
	self.act_lvlimit_icon_list = {}
end

function MainuiActBubbleView:__delete()
end

function MainuiActBubbleView:ReleaseCallBack()
	self.act_data = nil

	if self.function_btn_list ~= nil then
		for k,v in pairs(self.function_btn_list) do
			if v ~= nil then
				v:DeleteMe()
			end
		end
	end

	self.function_btn_list = {}

	if self.act_btn_list ~= nil then
		for k,v in pairs(self.act_btn_list) do
			if v ~= nil then
				v:DeleteMe()
			end
		end
	end

	self.act_btn_list = {}

	if self.act_btn_pool ~= nil then
		for k,v in pairs(self.act_btn_pool) do
			if v ~= nil then
				v:DeleteMe()
			end
		end
	end

	self.act_btn_pool = {}

	if self.function_btn_pool ~= nil then
		for k,v in pairs(self.function_btn_pool) do
			if v ~= nil then
				v:DeleteMe()
			end
		end
	end

	self.function_btn_pool = {}

	for k,v in pairs(self.act_remind) do
		RemindManager.Instance:UnBind(v)
	end
	self.act_remind = {}

	self.act_data = nil
end

function MainuiActBubbleView:LoadCallBack()
end

function MainuiActBubbleView:CloseCallBack()
	self:PutActBtnToPool()
	self:PutFunctionBtnToPool()
	self.act_lvlimit_icon_list = {}
	self.rand_open_icon = {}
	self.rand_open_icon[RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL] = {}
	self.rand_open_icon[RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_VERSION] = {}

	for k,v in pairs(self.act_remind) do
		RemindManager.Instance:UnBind(v)
	end
	self.act_remind = {}
end

function MainuiActBubbleView:SetData(act_data, x, y)
	if act_data == nil or x == nil or y == nil then
		return
	end

	self.act_data = act_data
	self.show_pos = {x = x, y = y}
	if not self:IsOpen() then
		self:Open()
	end
end

function MainuiActBubbleView:PutActBtnToPool()
	local index = #self.act_btn_pool + 1
	for k,v in pairs(self.act_btn_list) do
		if v ~= nil then
			v:SetActive(false)
			self.act_btn_pool[index] = v
			index = index + 1
		end
	end

	self.act_btn_list = {}
end

function MainuiActBubbleView:GetActBtn(activity_type)
	local button = nil
	if self.act_btn_list[activity_type] ~= nil then
		button = self.act_btn_list[activity_type]
	else
		if #self.act_btn_pool > 0 then
			button = table.remove(self.act_btn_pool, 1)
			self.act_btn_list[activity_type] = button
		else
			button = self:CreateActiviteBtn(self.node_list.root_rect, activity_type)
			self.act_btn_list[activity_type] = button
		end
	end

	return button
end

function MainuiActBubbleView:PutFunctionBtnToPool()
	local index = #self.function_btn_pool + 1
	for k,v in pairs(self.function_btn_list) do
		if v ~= nil then
			v:SetActive(false)
			self.function_btn_pool[index] = v
			index = index + 1
		end
	end

	self.function_btn_list = {}
end

function MainuiActBubbleView:GetFunctionBtn(function_name)
	local button = nil
	if self.function_btn_list[function_name] ~= nil then
		button = self.function_btn_list[function_name]
	else
		if #self.function_btn_pool > 0 then
			button = table.remove(self.function_btn_pool, 1)
			self.function_btn_list[function_name] = button
		else
			button = self:CreateFunctionBtn(self.node_list.root_rect, function_name)
			self.function_btn_list[function_name] = button
		end
	end

	return button
end


function MainuiActBubbleView:CreateFunctionBtn(parent, function_name)
	parent = parent or nil
	local btn = MainFunctionBtn.New()
	btn:CreateBtnAsset(parent, function_name)
	return btn
end

--创建活动按钮
function MainuiActBubbleView:CreateActiviteBtn(parent, activity_type)
	parent = parent or nil
	local act_icon
	if activity_type == ACTIVITY_TYPE.ZEROBUY then
		act_icon = ZeroBuyBtn.New()
	elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY then
		act_icon = MustBuyActivityBtn.New()
	else
		act_icon = MainActivityBtn.New()
	end
	act_icon:CreateBtnAsset(parent, activity_type)
	return act_icon
end

function MainuiActBubbleView:ActivitySetButtonVisible(activity_type, is_show)
	local button = nil

	if not is_show then
		local button = self.act_btn_list[activity_type]
		if button ~= nil then
			button:SetActive(false)
			if ActRemindList[activity_type] then
				RemindManager.Instance:UnBind(self.act_remind[activity_type])
				self.act_remind[activity_type] = nil
			end
		end
	else
		button = self:GetActBtn(activity_type)
		if button == nil then
			return
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(activity_type)
		button:SetData(activity_type)
		button:SetActCfg(act_cfg)
		button:Flush("CheckSpecialEffectBtn", {activity_type})
		button:SetActive(true)
		button:AddClickEventListener(BindTool.Bind(self.OnClickActIcon, self, activity_type))
	end

	return button
end

function MainuiActBubbleView:SetActBtn(act_type, act_cfg)
	if not act_type or not act_cfg then
		return
	end

	local act_icon = self:ActivitySetButtonVisible(act_type, true)
	if act_cfg then
		act_icon:Flush("SetSprite", {act_cfg.res_name, act_cfg.act_name_res, act_cfg.bg_res})
		act_icon:SetActCfg(act_cfg)
		if 5 < Status.NowTime and act_cfg.is_show_ani == 2 then
			act_icon:Flush("ShowFlyAni")
		else
			act_icon:SetHasFly(true)
		end
	end

	if ActRemindList[act_type] and self.act_remind[act_type] == nil then
		self.act_remind[act_type] = BindTool.Bind(self.ActBtnRemind, self, act_type)
		RemindManager.Instance:Bind(self.act_remind[act_type], ActRemindList[act_type])
	end

	act_icon:SetData(act_type)
	return act_icon
end

function MainuiActBubbleView:ActBtnRemind(act_type, remind_name, num)
	if self.act_btn_list[act_type] ~= nil then
		self.act_btn_list[act_type]:Flush("SetRedPoint", {num > 0})
	end
end

--通过活动id获取配置
function MainuiActBubbleView:GetActCfgByActType( act_type, open_type )
	local act_cfg = DailyWGData.Instance:GetActivityConfig(act_type)
	if CompetitionWGData.IsBipinType(act_type) then
		act_type = ACTIVITY_TYPE.BP_CAPABILITY_WING
		act_cfg = DailyWGData.Instance:GetActivityConfig(act_type) --比拼
	elseif nil == act_cfg or act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT or act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then
		if act_type > 2000 and act_type < 3000 or act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT then
			if ActivityWGData.Instance:GetActivityIsClose(act_type) then
				self.rand_open_icon[open_type][act_type] = nil
			else
				if self.rand_open_icon[open_type] then
					self.rand_open_icon[open_type][act_type]  = true
				else
					return
				end
			end

			if open_type == RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_VERSION then
					act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.BANBEN_ACTIVITY) --版本活动
					act_type = ACTIVITY_TYPE.BANBEN_ACTIVITY
			else
				act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.RAND_ACT) --随机活动
				act_type = ACTIVITY_TYPE.RAND_ACT
			end
		else
			return
		end
	end
	return act_cfg, act_type
end

function MainuiActBubbleView:CanCloseRandIcon(open_type)
	local icon_table = self.rand_open_icon[open_type]
	if not icon_table then
		return true
	end

	for k,v in pairs(icon_table) do
		return false
	end
	
	return true
end

function MainuiActBubbleView:CheckNotShowIcon( act_type )
	-- 活动大厅周历里面有的才更新
	--先屏蔽方便测试
	-- if BiZuoWGData.Instance:IsShieldByActType(act_type) then
	-- 	return true
	-- else
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_BATTLE_SOUL  -- 战场之魂图标用置状态控制
		or act_type == ACTIVITY_TYPE.ShopDazzleReturn --炫装白送
		or act_type == ACTIVITY_TYPE.DayDayFanLi then
		return true
	elseif act_type == ACTIVITY_TYPE.INVESTPLAN then --投资计划
		return true
	elseif act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then	--周末Boss特殊处理
		return true
	elseif act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SPECIAL_GIFT then
		return true
	-- elseif act_type == ACTIVITY_TYPE.MEIRI_LEICHONG then -- 每日充值
	-- 	return not RechargeWGData.Instance:GetIsFirstRecharge()
	elseif act_type == ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN or act_type == ACTIVITY_TYPE.SHOW_TASK_CHAIN then -- 任务链
		OperationTaskChainWGCtrl.Instance:CheckActIsOpen()
		return true
	end

	-- 判断活动提前结束条件
	if not ActivityWGData.Instance:IsActShow(act_type) then
		return true
	end
	return false
end

function MainuiActBubbleView:ActivityChangeCallBack(act_type, status, end_time, open_type)
	-- 判断这个活动是否需要通过这个方法来显示按钮     zzzz安检
	if self:CheckNotShowIcon(act_type) then
		return
	end

	--通过活动id获取配置 zzzz安检
	local act_cfg, act_type = self:GetActCfgByActType(act_type, open_type)
	if act_cfg == nil then
		return
	end

	if status == ACTIVITY_STATUS.CLOSE then
		if (act_type ~= ACTIVITY_TYPE.BP_CAPABILITY_WING and act_type ~= ACTIVITY_TYPE.RAND_ACT
			and act_type ~= ACTIVITY_TYPE.BANBEN_ACTIVITY)
			or (act_type == ACTIVITY_TYPE.RAND_ACT and self:CanCloseRandIcon(open_type))
			or (act_type == ACTIVITY_TYPE.BP_CAPABILITY_WING and self:CanCloseBipinIcon())
			or (act_type == ACTIVITY_TYPE.BANBEN_ACTIVITY and self:CanCloseRandIcon(open_type)) then
			self.act_lvlimit_icon_list[act_type] = nil
			self:ActivitySetButtonVisible(act_type, false) --移除按钮
		end
	else
		--特殊处理，让按钮消失 zzzz安检
		if self:CheckNotShowIconSpecial(act_type, act_cfg, status, end_time, open_type) then
			return
		end

		local act_icon
		if status == ACTIVITY_STATUS.STANDY then
			act_icon = self:SetActBtn(act_type, act_cfg)
			if act_icon then
				act_icon:Flush("SetEndTime", {end_time, nil, nil, nil, COLOR3B.GREEN})
				act_icon:Flush("SetActivityStateFlag", {act_type, status})
			end
		elseif status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU then
			if end_time and act_cfg.show_time == 1 then --显示倒计时
				if end_time - TimeWGCtrl.Instance:GetServerTime() <= 0 and self.act_lvlimit_icon_list[act_type] ~= nil then
					self.act_lvlimit_icon_list[act_type] = nil
				else
					act_icon = self:SetActBtn(act_type, act_cfg)
					if act_icon then
						act_icon:Flush("SetEndTime", {end_time})
						act_icon:Flush("SetActivityStateFlag", {act_type, status})
						self:SetSpecialName(status, act_icon, act_cfg)
					end
				end
			else
				act_icon = self:SetActBtn(act_type, act_cfg)
				if act_icon then
					act_icon:Flush("SetBottomContent", {""})
					act_icon:RemoveCountDown()
				end
			end
		end
		if act_icon then
			if (status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU) then
				act_icon:Flush("ShowAni", {act_cfg.is_show_ani == 1})
				act_icon:Flush("ResetLiuGuangEffect")
			end
			act_icon:AddClickEventListener(BindTool.Bind(self.OnClickActIcon, self, act_type))
		end
	end
end

--活动按钮点击事件
function MainuiActBubbleView:OnClickActIcon(act_type)
	self:Close()
	self:ClickEnterActivity(act_type)
end

function MainuiActBubbleView:ClickEnterActivity(act_type)
	local scene_type = Scene.Instance:GetSceneType()
	--跨服1v1进入预备场景
	if act_type == ACTIVITY_TYPE.KF_ONEVONE and scene_type ~= SceneType.Kf_OneVOne_Prepare then
		 Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_ONEVONE)
		return
	end
	--跨服3v3进入预备场景
	if act_type == ACTIVITY_TYPE.KF_PVP and scene_type ~= SceneType.Kf_PvP_Prepare then
		if scene_type ~= SceneType.Common then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.EnterPrepareLimit)
			return
		end
		KF3V3WGCtrl.Instance:EnterPrepareScene()
		return
	end

	-- 小鸭疾走
	if act_type == ACTIVITY_TYPE.KF_DUCK_RACE and scene_type ~= SceneType.KF_DUCK_RACE then
		-- 进入小鸭疾走活动场景
		DuckRaceWGCtrl.Instance:SendEnterScene()
	end

	if act_type == ACTIVITY_TYPE.OPERATIONACTIVITY or act_type == ACTIVITY_TYPE.OPERATIONACTIVITY_TWO then
		local open_type = OPERATION_ACTIVITY_OPEN_TYPE[act_type]
		OperationActivityWGCtrl.Instance:Open(nil, nil, open_type) --不要轻易改变传参
		return
	end

	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN  then
		local activity_status = ActivityWGData.Instance:GetActivityStatuByType(act_type)
		if activity_status.status == ACTIVITY_STATUS.STANDY then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi2)
			return
		end
	end

	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN and scene_type ~= SceneType.GuDaoJiZhan_FB then
		GuDaoFuBenWGCtrl.Instance:SendEnterGuDaoFB()
	end

	ActivityWGCtrl.Instance:OpenPopView(act_type)
end

--特殊处理，让按钮消失
function MainuiActBubbleView:CheckNotShowIconSpecial( act_type, act_cfg, status, end_time, open_type )
	--是否需要等级上限/天数处理
	if MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) then
		--print_error("隐藏活动按钮 >>>>> act_type:".. act_cfg.act_type, "status:".. status)
		self:ActivitySetButtonVisible(act_type, false)
		self.act_lvlimit_icon_list[act_type] = {act_type = act_type, status = status, end_time = end_time ,open_type = open_type}
		return true
	end
	if act_cfg.is_show_in_mainview == 0 then
		self:ActivitySetButtonVisible(act_type, false)
		return true
	end
	if act_type == ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO then
		return ServerActivityWGData.Instance:GetLingYaoMaiHuoIsBuyAllGift()
	end
	-- 小鸭疾走不显示准备状态
	if act_type == ACTIVITY_TYPE.KF_DUCK_RACE and status == ACTIVITY_STATUS.STANDY then
		return true
	end

	return false
end

--角色等级发生变化，刷新按钮
function MainuiActBubbleView:UpdateActLvLimitIcons()
	local level_limit_list = self.act_lvlimit_icon_list
	self.act_lvlimit_icon_list = {}

	for k,v in pairs(self.act_btn_list) do
		if v:CheckMainUIActIsLimit() then
			self:ActivitySetButtonVisible(v:GetData(), false)
		end
	end

	for k, v in pairs(level_limit_list) do
		self:ActivityChangeCallBack(v.act_type, v.status, v.end_time, v.open_type)
	end
end

function MainuiActBubbleView:ShowIndexCallBack()
	self.panel = self.node_list.root_rect 
	if self.show_pos ~= nil then
		self.panel.rect.anchoredPosition = Vector2(self.show_pos.x, self.show_pos.y - 80)
	else
		self.panel.rect.anchoredPosition = Vector2(0,0)
	end
end

function MainuiActBubbleView:FlushShowBtn()
	if self.act_data == nil then
		return
	end

	if self.act_data.act_list ~= nil then
		for k,v in pairs(self.act_data.act_list) do
			local act_state = ActivityWGData.Instance:GetActivityStatuByType(v.activity_type)
			if act_state ~= nil and act_state.status ~= ACTIVITY_STATUS.CLOSE then
				self:ActivityChangeCallBack(act_state.type, act_state.status, act_state.end_time, act_state.open_type)
			end
		end
	end

	if self.act_data.function_list ~= nil then
		for k,v in pairs(self.act_data.function_list) do
			local save_visib = MainuiWGCtrl.Instance:GetMergeFunctionVisib(v.mainui_key)
			if FunOpen.Instance:GetFunIsOpened(v.function_name) and save_visib ~= false then
				local button = self:GetFunctionBtn(v.function_name)
				button:SetActive(true)
				button:SetData(v)
				button:AddClickEventListener(BindTool.Bind(self.OnClickOpenActivityView, self, v))
			else
				if self.function_btn_list[v.function_name] ~= nil then
					self.function_btn_list[v.function_name]:SetActive(false)
				end
			end
		end
	end
end

function MainuiActBubbleView:OnFlush(param_t, index)
	if self.act_data == nil then
		return
	end

	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushShowBtn()
		elseif k == "act" then
			if v ~= nil then
				self:ActivityChangeCallBack(v.activity_type, v.status, v.next_time, v.open_type)
			end
		elseif k == "level" then
			if self.act_lvlimit_icon_list ~= nil then
				self:UpdateActLvLimitIcons()
			end
		elseif k == "func" then
			if v ~= nil then
				self:SetFunctionVisib(v.mainui_key, v.visible)
			end
		end
	end
end

-- 打开对面的面板
function MainuiActBubbleView:OnClickOpenActivityView(data)
	if data == nil or next(data) == nil then return end

	if data.targer_obj then
		self:SetFakeBtnHandle(data)
		return
	end

	local view_name = data.function_name
	local tab_index = data.tab_index
	if view_name == nil then
		return
	end

	if view_name == GuideModuleName.Shop then --商城屏蔽问题
		tab_index = tab_index or 10
		local select_index = math.floor(tab_index)
		if not ShopWGData.ShowShopType[select_index] then
			for k,v in pairs(ShopWGData.ShowShopType) do
				if v then
					tab_index = k *10
					break
				end
			end
		end
	end

	self:Close()
	FunOpen.Instance:OpenViewByName(view_name, tab_index)
end

function MainuiActBubbleView:SetFakeBtnHandle(data)
	-- body
	if not data or not data.targer_obj then return end

	local str = self:GetFakeBtnStr(data, true)
	if not str then return end
	SysMsgWGCtrl.Instance:ErrorRemind(str)
end

function MainuiActBubbleView:GetFakeBtnStr(data, is_click)
	-- body
	if not data or not data.targer_obj then return end
	local is_open = FunOpen.Instance:GetFunIsOpened(data.targer_obj.view_name)
	if is_open and is_click then
		print_error("请检查配置，对应功能己开启：", data.targer_obj.view_name)
		return
	end

	local fun_cfg = FunOpen.Instance:GetFunByName(data.targer_obj.view_name)
	if not fun_cfg then return end

	local str = nil
	if is_click then
		if fun_cfg.trigger_type == GuideTriggerType.ClickUi then

		elseif fun_cfg.trigger_type == GuideTriggerType.LevelUp then		--等级限制
				local limit_lv = tonumber(fun_cfg.trigger_param) or 0
				str = string.format(Language.Common.FunOpenRoleLevelLimitClick, RoleWGData.GetLevelString(limit_lv))
		elseif fun_cfg.trigger_type ~= 0 then   						--任务限制

		end
	else
		str = FunOpen.Instance:GetFunUnOpenCommonTip(fun_cfg)
	end
	return str
end

function MainuiActBubbleView:SetFunctionVisib(main_view_btn_name, visib)
	if main_view_btn_name == nil then
		return
	end

	if not self:IsOpen() then
		return
	end

	if self.act_data == nil then
		return
	end

	for k,v in pairs(self.act_data.function_list) do
		if v.mainui_key == main_view_btn_name then
			if self.function_btn_list[v.function_name] ~= nil then
				self.function_btn_list[v.function_name]:SetActive(visib)
			end
		end
	end
end

-----------------------------------------------------------------------------------
-------------------------活动按钮特殊特效---------------------------------------
MainFunctionBtn = MainFunctionBtn or BaseClass(BaseRender)
function MainFunctionBtn:__init(instance)
	self.function_name = 0
end

function MainFunctionBtn:CreateBtnAsset(parent, function_name)
	if nil == self.root_node then
		local bundle = "uis/view/main_ui_prefab"
		local asset = "BtnMainuiFunction"

		self.btn_obj = ResPoolMgr:TryGetGameObject(bundle, asset)
		self.btn_obj.transform:SetParent(parent.transform, false)
		self.btn_obj.name = "id:" .. function_name
		self:SetInstance(self.btn_obj)
	end
end

function MainFunctionBtn:__delete()
	if self.btn_obj then
		ResPoolMgr:Release(self.btn_obj)
		self.btn_obj = nil
	end
end

function MainFunctionBtn:LoadCallBack()
end

--添加按钮点击事件
function MainFunctionBtn:AddClickEventListener( callback )
	if IsEmptyTable(self.node_list) then
		self.click_callback = callback
		return
	end
	XUI.AddClickEventListener(self.node_list.BtnMainuiFunction, function()
		callback()
		self:OnClick()
	end)
end

function MainFunctionBtn:OnFlush(param_t)
	if self.data == nil or next(self.data) == nil then
		return
	end

	if self.data.function_name == GuideModuleName.SevenDay then
		self:FlushSevenDayIcon()
	else
		self:FlushNormal()
	end
end

function MainFunctionBtn:FlushNormal()
	local bg_bundle, bg_asset = ResPath.GetMainUIIcon(self.data.bg_res)
	self.node_list.img_bg.image:LoadSprite(bg_bundle, bg_asset, function()
		if self.node_list.img_bg ~= nil then
			self.node_list.img_bg.image:SetNativeSize()
		end
	 end)

	local icon_bundle, icon_asset = ResPath.GetMainUIIcon(self.data.icon_res)
	self.node_list.img_icon.image:LoadSprite(icon_bundle, icon_asset, function()
		if self.node_list.img_icon ~= nil then
			self.node_list.img_icon.image:SetNativeSize()
		end
	 end)

	local name_bundle, name_asset = ResPath.GetMainUIIcon(self.data.name_res)
	self.node_list.img_name.image:LoadSprite(name_bundle, name_asset, function()
		if self.node_list.img_name ~= nil then
			self.node_list.img_name.image:SetNativeSize()
		end
	 end)

	self.node_list.img_reward:SetActive(false)
	local has_res = false
	if self.data.remind_name ~= nil then
		has_res = RemindManager.Instance:GetRemind(self.data.remind_name) > 0
	end

	self.node_list.img_red:SetActive(has_res)
	self.node_list.root_effect:SetActive(false)
end

function MainFunctionBtn:FlushSevenDayIcon()
	--文本,图片(七天登录)
	local is_show_seven_effect = WelfareWGData.Instance:IsShowSevenServerRedPoint() == 1
	if self.node_list["img_red"] then
		self.node_list["img_red"]:SetActive(is_show_seven_effect)
	end

	local index1 = WelfareWGData.Instance:GetSevenDayDefaultIndex()
	local data_list = WelfareWGData.Instance:GetEightSevenDayCfg()
	local item_status
	local item_show_ID
	local item_name
	if data_list[index1] then
		item_status = data_list[index1].status
		item_show_ID = data_list[index1].show_ID2
		item_name = data_list[index1].show_name
	end

	self.node_list.root_effect:SetActive(item_status == SEVEN_COMPLETE_STATUS.KELINGQU)
	local icon_bundle, icon_asset = ResPath.GetF2MainUIImage(item_show_ID)
	self.node_list.img_icon.image:LoadSprite(icon_bundle, icon_asset, function()
		if self.node_list.img_icon ~= nil then
			self.node_list.img_icon.image:SetNativeSize()
		end
	 end)

	local name = ""
	if item_status == SEVEN_COMPLETE_STATUS.KELINGQU then
		name = "btn_denglu_lq"
	else
		name = item_name
	end

	local name_bundle, name_asset = ResPath.GetF2MainUIImage(name)
	self.node_list.img_name.image:LoadSprite(name_bundle, name_asset, function()
		if self.node_list.img_name ~= nil then
			self.node_list.img_name.image:SetNativeSize()
		end
	 end)

	self.node_list.img_reward:SetActive(false)
end