-- 秘境
function CountryMapActView:LoadIndexCallBackSecret()
	if not self.boss_reward_list then
		self.boss_reward_list = AsyncListView.New(ItemCell, self.node_list.boss_reward)
	end
	if not self.join_reward_list then
		self.join_reward_list = AsyncListView.New(ItemCell, self.node_list.join_reward)
	end
	self.node_list.go_secret_btn.button:AddClickListener(BindTool.Bind1(self.OnClickGoToSecret, self))

	if nil == self.boss_display_model then
		self.boss_display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["boss_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.boss_display_model:SetRenderTexUI3DModel(display_data)
		-- self.boss_display_model:SetUI3DModel(self.node_list["boss_model"].transform, self.node_list["boss_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end
end


function CountryMapActView:ShowIndexCallBackSecret()

end

function CountryMapActView:ReleaseSecret()
	if self.boss_reward_list then
		self.boss_reward_list:DeleteMe()
		self.boss_reward_list = nil
	end

	if self.join_reward_list then
		self.join_reward_list:DeleteMe()
		self.join_reward_list = nil
	end

	if self.boss_display_model then
		self.boss_display_model:DeleteMe()
		self.boss_display_model = nil
	end
end

function CountryMapActView:OnFlushSecret(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushSecretAllView()
		end
	end
end

function CountryMapActView:FlushSecretAllView()
	self:FlushSecretInfo()
	self:FlushSecretReward()
	self:FlushSecretBossModel()
end

function CountryMapActView:FlushSecretInfo()
	self.node_list.desc.text.text = Language.CountrySecret.SecretDesc
	local secret_area_info = SecretAreaWGData.Instance:GetSecretAreaInfo()
	local secret_other_cfg = SecretAreaWGData.Instance:GetOtherCfg()
	local enter_xtmj_times = secret_area_info.enter_xtmj_times or 0
	local remain_times = secret_other_cfg.limit_times - enter_xtmj_times
	self.node_list.remain_count.text.text = remain_times .."/" .. secret_other_cfg.limit_times
	self.node_list.secret_remind:SetActive(SecretAreaWGData.Instance:GetCountrySecretRemind() == 1)
end

function CountryMapActView:FlushSecretReward()
	local reward_cfg = SecretAreaWGData.Instance:GetSecretRewardCfgByWorldLv()
	if IsEmptyTable(reward_cfg) then
		return
	end
	if self.boss_reward_list ~= nil then
		self.boss_reward_list:SetDataList(reward_cfg.drop_reward_item)
	end

	if self.join_reward_list ~= nil then
		self.join_reward_list:SetDataList(reward_cfg.join_reward_item)
	end
end

function CountryMapActView:FlushSecretBossModel()
	local secret_other_cfg = SecretAreaWGData.Instance:GetOtherCfg()
	if secret_other_cfg.boss_id then
		local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[secret_other_cfg.boss_id] --获取boss模型
		self.boss_display_model:SetMainAsset(ResPath.GetMonsterModel(monster_cfg.resid))
	end
end

function CountryMapActView:OnClickGoToSecret()
	local secret_area_info = SecretAreaWGData.Instance:GetSecretAreaInfo()
	if IsEmptyTable(secret_area_info) then
		return
	end

	local secret_other_cfg = SecretAreaWGData.Instance:GetOtherCfg()
	local remain_times = secret_other_cfg.limit_times - secret_area_info.enter_xtmj_times
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if activity_info then
		if activity_info.status == ACTIVITY_STATUS.STANDY then
			if remain_times > 0 or secret_area_info.enter_xtmj_last_time >= server_time then
				SecretAreaWGCtrl.Instance:SendCorssSecretAreaOperate(SECRET_AREA_TYPE.XingTuMiJing_EnterScene)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.CountrySecret.NoCount)
			end
		elseif activity_info.status == ACTIVITY_STATUS.OPEN then
			if secret_area_info.enter_xtmj_last_time >= server_time then
				SecretAreaWGCtrl.Instance:SendCorssSecretAreaOperate(SECRET_AREA_TYPE.XingTuMiJing_EnterScene)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.CountrySecret.NoGoActivity)
			end
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CountrySecret.NoGoActivity)
	end
end