TianShuBossTip = TianShuBossTip or BaseClass(SafeBaseView)

function TianShuBossTip:__init()
	self:AddViewResource(0, "uis/view/tianshu_ui_prefab", "layout_boss_info")
end

function TianShuBossTip:__delete()

end

function TianShuBossTip:ReleaseCallBack()

end

function TianShuBossTip:LoadCallBack()
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnclickCloseView, self))
end

function TianShuBossTip:ShowIndexCallBack(index)
	self.node_list.img_boss.image:LoadSprite(ResPath.GetBossIcon("boss_" .. self.data.boss_info.small_icon))
	self.node_list.label_boss_name.text.text = self.data.boss_info.name
	self.node_list.label_level.text.text = self.data.boss_info.level
	local boss_info = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_info.id)
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(boss_info.scene_id)
	self.node_list.label_map.text.text = scene_cfg.name
end

function TianShuBossTip:OpenCallBack()
end

function TianShuBossTip:CloseCallBack()

end

function TianShuBossTip:SetData(data)
	self.data = data
	self:Open()
end
function TianShuBossTip:OnclickCloseView(data)
	self:Close()
end

function TianShuBossTip:OnFlush()
	
end

function TianShuBossTip:GoToKillBoss()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Boss)
	self:Close()
end