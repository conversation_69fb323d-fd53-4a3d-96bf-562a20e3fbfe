BossMustFallPrivilegeView = BossMustFallPrivilegeView or BaseClass(SafeBaseView)

function BossMustFallPrivilegeView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/boss_must_fall_privilege_ui_prefab", "layout_boss_must_fall_privilege_view")
end

function BossMustFallPrivilegeView:LoadCallBack()
    self.node_list.desc_ads.text.text = Language.BossMustFallPrivilege.DescAds
    self.node_list.desc_condition.text.text = Language.BossMustFallPrivilege.DescCondition

    if not self.must_fall_list then
	    self.must_fall_list = AsyncListView.New(BossMustFallPrivilegeItemRender, self.node_list["list_must_fall"])
    end

    XUI.AddClickEventListener(self.node_list["btn_flush_level"], BindTool.Bind(self.OnClickFlushLevelBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_privilege_state"], BindTool.Bind(self.OnClickPrivilegeStateBtn, self))
    XUI.AddClickEventListener(self.node_list["img_flush_level_cost"], BindTool.Bind(self.OnClickFlushLevelCostImg, self))
    XUI.AddClickEventListener(self.node_list["btn_add_time"], BindTool.Bind(self.OnClickAddTime, self))
    XUI.AddClickEventListener(self.node_list["btn_tips"], BindTool.Bind(self.OnClickTips, self))
end

function BossMustFallPrivilegeView:ReleaseCallBack()
    if self.must_fall_list then
        self.must_fall_list:DeleteMe()
	    self.must_fall_list = nil
    end

    self:ClearCountDown()
end

function BossMustFallPrivilegeView:ShowIndexCallBack()
    local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.BossMustFallPrivilegeView)

    if fun_open then
        self:SetCountDowm()
    else
        local online_time_str = BossMustFallPrivilegeWGData.Instance:GetAllFreeTimeStr()
        self.node_list.desc_timer.text.text = string.format(Language.BossMustFallPrivilege.DescOnLineTime, online_time_str, "")
    end

    self.node_list.box_btns:CustomSetActive(fun_open)
end

function BossMustFallPrivilegeView:SetCountDowm()
    local free_get_time = BossMustFallPrivilegeWGData.Instance:GetNextFrteeGetTime()

    self:ClearCountDown()
    if free_get_time > 0 then
        CountDownManager.Instance:AddCountDown("BossMustFallPrivilegeView", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnCompleteCountDown, self), free_get_time, nil, 1)
    else
        local online_time_str = BossMustFallPrivilegeWGData.Instance:GetAllFreeTimeStr()
        self.node_list.desc_timer.text.text = string.format(Language.BossMustFallPrivilege.DescOnLineTime, online_time_str, Language.BossMustFallPrivilege.HasGetFreeCountDownTimeStr)
    end
end

function BossMustFallPrivilegeView:UpdateCountDown(elapse_time, total_time)
    if self.node_list.desc_timer then
        local time = TimeUtil.FormatSecondDHM4(total_time - elapse_time)
        local online_time_str = BossMustFallPrivilegeWGData.Instance:GetAllFreeTimeStr()
        local cur_time_str = string.format(Language.BossMustFallPrivilege.FreeCountDownTimeStr, time)
        self.node_list.desc_timer.text.text = string.format(Language.BossMustFallPrivilege.DescOnLineTime, online_time_str, cur_time_str)
    end
end

function BossMustFallPrivilegeView:OnCompleteCountDown()
    self:SetCountDowm()
end

function BossMustFallPrivilegeView:ClearCountDown()
    if CountDownManager.Instance:HasCountDown("BossMustFallPrivilegeView") then
		CountDownManager.Instance:RemoveCountDown("BossMustFallPrivilegeView")
	end
end

function BossMustFallPrivilegeView:OnFlush()
    local level, cur_level_cfg = BossMustFallPrivilegeWGData.Instance:GetCurLevel()
    local bundle, asset = ResPath.GetBossMustFallPrivilegeImg("a2_vipboss_bq_" .. level)
    self.node_list.img_cur_level_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.img_cur_level_icon.image:SetNativeSize()
    end)

    local bless_value = BossMustFallPrivilegeWGData.Instance:GetCurBlessValue()
    self.node_list.bless_value.text.text = bless_value .. "/" .. cur_level_cfg.upgrade_value
    self.node_list.slider_bless_progress.slider.value = bless_value / cur_level_cfg.upgrade_value
    self.node_list.desc_flush_level_cost.text.text = cur_level_cfg.need_gold

    local level_data_list = BossMustFallPrivilegeWGData.Instance:GetLevelCfg()
    self.must_fall_list:SetDataList(level_data_list)

    local cur_time, all_time = BossMustFallPrivilegeWGData.Instance:GetPrivilegeTime()
    self.node_list.desc_cur_time.text.text = string.format(Language.BossMustFallPrivilege.TodayMustFallTime, cur_time, all_time)

    local state = BossMustFallPrivilegeWGData.Instance:GetPrivilegeState()
    self.node_list.desc_btn_privilege_state.text.text = Language.BossMustFallPrivilege.BtnDescPrivilege[state] or ""
    self.node_list.box_flush_level:CustomSetActive(state == 1)
end

function BossMustFallPrivilegeView:OnClickFlushLevelBtn()
    local is_max_level = BossMustFallPrivilegeWGData.Instance:IsMaxLevel()

    if is_max_level then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BossMustFallPrivilege.IsMaxLevel)
        return
    end

    local _, cur_level_cfg = BossMustFallPrivilegeWGData.Instance:GetCurLevel()
    local need_gold = cur_level_cfg.need_gold or 0
    local is_enough = RoleWGData.Instance:GetIsEnoughUseGold(need_gold)

    if is_enough then
        BossMustFallPrivilegeWGCtrl.Instance:SendOperateReq(VIP_BOSS_OPERA_TYPE.VIP_BOSS_OPERA_TYPE_UPGRADE_EXTRA_REWARDS_LEVEL)
    else
        VipWGCtrl.Instance:OpenTipNoGold()
    end
end

function BossMustFallPrivilegeView:OnClickPrivilegeStateBtn()
    local state = BossMustFallPrivilegeWGData.Instance:GetPrivilegeState()
    
    -- 0 关闭  1 打开
    if state == 0 then
        local remain_times = BossMustFallPrivilegeWGData.Instance:GetPrivilegeTime()

        if remain_times > 0 then
            BossMustFallPrivilegeWGCtrl.Instance:SendOperateReq(VIP_BOSS_OPERA_TYPE.VIP_BOSS_OPERA_TYPE_SET_EXTRA_REWARDS_STATE, 1)
        end
    else
        BossMustFallPrivilegeWGCtrl.Instance:SendOperateReq(VIP_BOSS_OPERA_TYPE.VIP_BOSS_OPERA_TYPE_SET_EXTRA_REWARDS_STATE, 0)
    end
end

function BossMustFallPrivilegeView:OnClickFlushLevelCostImg()
    TipWGCtrl.Instance:OpenItem({item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD})
end

function BossMustFallPrivilegeView:OnClickAddTime()
    local has_buy_time, buy_cfg = BossMustFallPrivilegeWGData.Instance:GetBuyMustFallPrivilegeCfg()
    local next_time_cfg = buy_cfg and buy_cfg[has_buy_time + 1] or {}

    if not IsEmptyTable(next_time_cfg) then
        TipWGCtrl.Instance:OpenAlertTips(string.format(Language.BossMustFallPrivilege.BuyTimeAlertFormat, next_time_cfg.need_gold, #buy_cfg - has_buy_time, #buy_cfg), function ()
            BossMustFallPrivilegeWGCtrl.Instance:SendOperateReq(VIP_BOSS_OPERA_TYPE.VIP_BOSS_OPERA_TYPE_BUY_TIMES)
        end)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BossMustFallPrivilege.IsMaxBuyTime)
    end
end

function BossMustFallPrivilegeView:OnClickTips()
    RuleTip.Instance:SetContent(Language.BossMustFallPrivilege.TipsContent, Language.BossMustFallPrivilege.TipsTitle)
end

------------------------------BossMustFallPrivilegeItemRender------------------------------
BossMustFallPrivilegeItemRender = BossMustFallPrivilegeItemRender or BaseClass(BaseRender)

function BossMustFallPrivilegeItemRender:__delete()
    if self.must_fall_reward_list then
        self.must_fall_reward_list:DeleteMe()
        self.must_fall_reward_list = nil
    end
end

function BossMustFallPrivilegeItemRender:LoadCallBack()
    if not self.must_fall_reward_list then
        self.must_fall_reward_list = AsyncListView.New(ItemCell, self.node_list["must_fall_reward_list"])
        self.must_fall_reward_list:SetStartZeroIndex(true)
    end
end

function BossMustFallPrivilegeItemRender:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetBossMustFallPrivilegeImg("a2_vipboss_bq_" .. self.data.level)
    self.node_list.icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.icon.image:SetNativeSize()
    end)

    local reward_list = BossMustFallPrivilegeWGData.Instance:GetLevelRewardCfg(self.data.level)
    self.must_fall_reward_list:SetDataList(reward_list)
end