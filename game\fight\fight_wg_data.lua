
FightWGData = FightWGData or BaseClass()

local EXP_BUFF_TYPE = {
	[1] = EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1,		--50%倍
	[2] = EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2,		--100%倍
	[3] = EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3,		--200%倍
	[4] = EFFECT_CLIENT_TYPE.ECT_ITEM_EXP4,		--250%倍
}

-- 受击者身上的buff
FightWGData.Client_Effect_Type = {
	{client_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10150, percent = 0.2, unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10150}, 	-- 回避之羽
	{client_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10151, percent = 0.2, unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10151}, 	-- 恢复之羽
	{client_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10171, percent = 0.2, unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10171}, 	-- 法器终结
	{client_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10172, percent = 0.2, unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10172}, 	-- 大巧不工
	-- {client_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10173, percent = 0.2, unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10173}, 	-- 剑如惊鸿
}

--受击者血量低于pencent，攻击者身上的buff
FightWGData.Client_Effect_Type2 = {
	{client_type = EFFECT_CLIENT_TYPE.ECT_SKILL_20228, percent = 0.2, unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_20228}, 	-- 回避之羽
}

FIGHT_EFFECT_JIANG_PO = 3102
function FightWGData:__init()
	if FightWGData.Instance then
		print_error("[FightWGData]:Attempt to create singleton twice!")
	end
	FightWGData.Instance = self

	self.main_role_effect_list = {}					-- 主角effect
	self.target_objid = COMMON_CONSTS.INVALID_OBJID	-- 目标objid
	self.others_effect_list = {}					-- 其他人effect
	self.beauty_effect_list = {}					-- 美人effect
	self.equip_level_add = 0						-- 装备越级
	self.equip_level_change_callback_list = {}
	self.exp_buff_list = {}							--经验buff

	self.be_hit_list = {}							-- 受击缓存
	self.can_send_perform_skill = true

	self.buff_effect_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("buff_desc_auto").buff_config, "buff_type", "product_id")
end

function FightWGData:__delete()
	FightWGData.Instance = nil
end

function FightWGData.CreateEffectInfo()
	return {
		effect_id = 0,
		effect_type = 0,
		product_method = 0,
		product_id = 0,
		unique_key = 0,
		param_list = {},
		client_effect_type = 0,
		merge_layer = 0,
		recv_time = 0,
		cd_time = 0,
		buff_type = 0,
	}
end

function FightWGData:Update(now_time, elapse_time)
	for k, v in pairs(self.be_hit_list) do
		if now_time >= v.max_trigger_time then
			self:DoBeHit(v, k:IsMainRole(), nil)
			self.be_hit_list[k] = nil
		end
	end
end

function FightWGData:OnEffectList(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj then
		return
	end

	local effect_list = {}
	for k, v in pairs(protocol.effect_list) do
		local effect = FightWGData.CreateEffectInfo()
		effect.effect_id = v.effect_id
		effect.effect_type = v.effect_type
		effect.product_method = v.product_method
		effect.product_id = v.product_id
		effect.param_list = v.param_list
		effect.unique_key = v.unique_key
		effect.client_effect_type = v.client_effect_type
		effect.merge_layer = v.merge_layer
		effect.buff_type = v.buff_type
		effect.recv_time = Status.NowTime
		effect.remaining_time = effect.param_list[1]    --effect 的剩余时间
		table.insert(effect_list, effect)
	end

	if obj:IsMainRole() then
		self.main_role_effect_list = effect_list
		-- self:SetAddAttr()
		GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, true)
	else
		self.target_objid = obj:GetObjId()
		self.others_effect_list[self.target_objid] = effect_list
		GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, false)
	end
end

function FightWGData:OnEffectInfo(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj then
		return
	end

	local effect = FightWGData.CreateEffectInfo()
	effect.effect_id = protocol.effect_id
	effect.effect_type = protocol.effect_type
	effect.product_method = protocol.product_method
	effect.product_id = protocol.product_id
	effect.param_list = protocol.param_list
	effect.unique_key = protocol.unique_key
	effect.client_effect_type = protocol.client_effect_type
	effect.merge_layer = protocol.merge_layer
	effect.buff_type = protocol.buff_type
	effect.recv_time = Status.NowTime
	effect.remaining_time = effect.param_list[1] and (effect.param_list[1] / 1000) or 0   --effect 的剩余时间

	if obj:IsRole() then	---产生武魂真身特效
		if effect.buff_type == BUFF_TYPE.EBT_WUHUN then
			---展示其他角色的武魂真身
			obj:BuildWuHunInstance(effect.product_id)
			if obj:IsMainRole() then
				GlobalEventSystem:Fire(OtherEventType.ROLE_WUHUNZHENSHEN_CHANGE, true, effect.product_id)
			end
		end
		if effect.client_effect_type == PRODUCT_ID_TRIGGER.PRODUCT_ID_DUJIE then
			local is_play = DujieWGData.Instance:SetSceneThunderAudio(TimeWGCtrl.Instance:GetServerTime())
			-- 避免太多音效出现
			if is_play then
				local audio_index = math.random(0,1)
				AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(audio_index == 1 and AudioUrl.Dujie_Jin or AudioUrl.Dujie_Hong, true, true))
			end
			
		end
	end

	if obj:IsMainRole() then
		self:UpdateEffect(self.main_role_effect_list, effect)
		-- self:SetAddAttr()

		if effect.effect_type == BUFF_TYPE.EBT_ELEMENT then
			local element_type = effect.param_list[3] and math.floor(effect.param_list[3] / 1000) or -1
			MainuiWGCtrl.Instance:SetBeastBuffIcon(effect.remaining_time > 0, element_type, effect.remaining_time)
		end

		if effect.remaining_time > 0 then
			MainuiWGCtrl.Instance:GetNewBuffAddToAniWaitList(effect)
		end

		GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, true)
		if effect.effect_type == FIGHT_EFFECT_TYPE.BIANSHEN then
			obj:SetAttr("bianshen_param", protocol.param_list[2])
		end

		if effect.client_effect_type == EFFECT_CLIENT_TYPE.ECT_TIANSHEN_FB_BUFF then
			GlobalEventSystem:Fire(OtherEventType.TIANSHEN_BUFF_CHANGE, true)
		elseif effect.client_effect_type == PRODUCT_ID_TRIGGER.PRODUCT_ID_DUJIE then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.WarringStr2)
		end

		if RoleWGCtrl.Instance:GetMainBuffMonitor() and UnityEngine.Debug.isDebugBuild then
			local debug_str = string.format(Language.ServerAttrSource.HasName3, effect.effect_id, effect.effect_type, effect.product_id, effect.client_effect_type)
			print_log(debug_str)
		end
	else
		self.others_effect_list[protocol.obj_id] = self.others_effect_list[protocol.obj_id] or {}
		self:UpdateEffect(self.others_effect_list[protocol.obj_id], effect)
		GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, false)
	end

	if effect.effect_type == FIGHT_EFFECT_TYPE.OTHER then
		if effect.client_effect_type == EFFECT_CLIENT_TYPE.ECT_GUILD_BATTLE_SHENGSHENGZHUFU
		or effect.client_effect_type == EFFECT_CLIENT_TYPE.ECT_GUILD_BATTLE_BOMB then
			if nil ~= obj and not obj:IsDeleted() and obj:IsRole() then
				obj:SetAttr("xmz_client_show_effect", effect.client_effect_type)
			end
		end
	end


	local buff_desc_cfg = self:GetBuffDescCfgByInfo(effect.product_id, effect.buff_type, effect.client_effect_type)
	if buff_desc_cfg and "" ~= buff_desc_cfg.sound then
		local bundle, asset = ResPath.GetSkillVoiceRes(buff_desc_cfg.sound)
		AudioManager.PlayAndForget(bundle, asset)
	end
end

function FightWGData:SetAddAttr()
	-- body
	self.main_add_attr = {}
	local param_list = nil
	local count = 2
	local index = 0
	local len = 0
	local key = 0
	local value = 0
	local percent = 0
	for k,v in pairs(self.main_role_effect_list) do
		param_list = v.param_list
		len = math.max(0, math.floor((#param_list - 1)/3)-1)
		index = 0
		for i=1,len do
			key = param_list[i+index+1]
			value = param_list[i+index+2]
			percent = param_list[i+index+3]

			if 0 < value or 0 < percent then
				if not self.main_add_attr[key] then
					self.main_add_attr[key] = {}
				end
				self.main_add_attr[key].key = key
				self.main_add_attr[key].value = value + (self.main_add_attr[key].value or 0)
				self.main_add_attr[key].percent = percent + (self.main_add_attr[key].percent or 0)
			end
			index = index+count
		end
	end
end

function FightWGData:GetAttrValue(attr_name)
	-- body
	local attr_type = RoleWGData.Instance:GetRoleAttrTypeByName(attr_name)
	if not self.main_add_attr or not attr_type or not self.main_add_attr[attr_type] then return 0 end

	local tab = self.main_add_attr[attr_type]
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
	local num = tab.value + tab.percent/10000*mainrole_vo[attr_name]
	num = math.floor(num)
	return num
end

function FightWGData:GetMainRoleEffectList()
	return self.main_role_effect_list or {}
end

function FightWGData:GetOtherRoleEffectList(obj_id)
	return self.others_effect_list[obj_id] or {}
end

function FightWGData:ClearOtherRoleEffectList(obj_id)
	self.others_effect_list[obj_id] = nil
end

function FightWGData:ClearAllOtherRoleEffectList()
	self.others_effect_list = {}
end


function FightWGData:UpdateEffect(effect_list, effect)
	for i, v in ipairs(effect_list) do
		if v.unique_key == effect.unique_key then
			effect_list[i] = effect
			return
		end
	end
	table.insert(effect_list, effect)
	table.sort(effect_list, SortTools.KeyLowerSorter("recv_time"))
end

-- 移除Effect
function FightWGData:OnEffectRemove(obj_id, effect_key, buff_type)
	local obj = Scene.Instance:GetObj(obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end

	if obj:IsRole() then	---移除武魂真身特效
		if buff_type and buff_type == BUFF_TYPE.EBT_WUHUN then
			---展示其他角色的武魂真身
			obj:RemoveWuHunInstance()
			if obj:IsMainRole() then
				GlobalEventSystem:Fire(OtherEventType.ROLE_WUHUNZHENSHEN_CHANGE, false)
			end
		end
	end

	if obj:IsMainRole() then
		for i, v in ipairs(self.main_role_effect_list) do
			if v.unique_key == effect_key then
				table.remove(self.main_role_effect_list, i)
				-- self:SetAddAttr()
				if v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_TIANSHEN_FB_BUFF then
					GlobalEventSystem:Fire(OtherEventType.TIANSHEN_BUFF_CHANGE, false)
				end
				GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, true)
				break
			end
		end
	else
		local other_effect_list = self.others_effect_list[obj_id]
		if other_effect_list ~= nil then
			for i, v in ipairs(other_effect_list) do
				if v.unique_key == effect_key then
					table.remove(other_effect_list, i)
					if v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_TIANSHEN_FB_BUFF then
						GlobalEventSystem:Fire(OtherEventType.TIANSHEN_BUFF_CHANGE, false)
					end
					GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, false)
					break
				end
			end
		end
	end
end

function FightWGData:GetExpBuff()
	local buff_list = self:GetMainRoleEffectList()
	for i, v in pairs(buff_list) do
		if v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1
		or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2
		or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3
		or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP4 then
			return v
		end
	end
end

function FightWGData:HasEffectByClientType(client_type)
	for k, v in pairs(self.main_role_effect_list) do
		if v.client_effect_type == client_type then
			return true
		end
	end

	return false
end

function FightWGData:GetEffectByClientType(client_type)
	for k, v in pairs(self.main_role_effect_list) do
		if v.client_effect_type == client_type then
			return v
		end
	end

	return nil
end

-- vip加成
function FightWGData:GetMainRoleVipEffect()
	local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
	if vip_level > 0 then
		local vip_cfg = VipWGData.Instance:GetVipBuffCfg(vip_level)
		if nil ~= vip_cfg then
			local effect = FightWGData.CreateEffectInfo()
			effect.unique_key = -1
			effect.client_effect_type = 0
			effect.cd_time = 0
			for i = 1, 10 do
				effect.param_list[i] = 0
			end
			-- effect.param_list[3] = vip_cfg.mianshang_per / 100  --vip免伤，由于是万分比，需要显示成百分比，所以除以100
			--effect.param_list[6] = vip_cfg.fangyu
			--effect.param_list[9] = vip_cfg.maxhp

			return effect
		end
	end

	return nil
end

-- 世界加成
function FightWGData:GetWroldLevelEffect()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local world_level = RankWGData.Instance:GetWordLevel()
	if role_level < COMMON_CONSTS.WORLD_LEVEL_OPEN or role_level >= world_level then
		return nil
	end
	-- local add_percent = math.min((world_level - role_level) * COMMON_CONSTS.WORLD_LEVEL_EXP_PERCENT, COMMON_CONSTS.WORLD_LEVEL_EXP_MAX_PERCENT)

	local add_percent = SkillWGData.Instance:GetExpEfficiencyInfo()
  	add_percent = add_percent and add_percent.world_extra_percent
  	add_percent = add_percent and math.ceil(add_percent * 100) or 0
  	add_percent = add_percent and add_percent * 100 or 0
  	add_percent = math.ceil(add_percent)

  	if 0 >= add_percent then
  		return nil
  	end

	local effect = FightWGData.CreateEffectInfo()
	effect.unique_key = -2
	effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_OTHER_SJJC
	effect.cd_time = 0
	for i = 1, 10 do
		effect.param_list[i] = 0
	end
	effect.param_list[2] = add_percent

	return effect
end

-- 世界boss死亡buff
function FightWGData:GetWroldBossDieEffect()
	local boss_weary = BossWGData.Instance:GetWroldBossWeary()
	local last_die_time = BossWGData.Instance:GetWroldBossWearyLastDie() + 300 - TimeWGCtrl.Instance:GetServerTime()
	if boss_weary <= 0 or BossWGData.Instance:GetWroldBossWearyLastDie() <= 0 then
		return nil
	end

	local effect = FightWGData.CreateEffectInfo()
	effect.unique_key = -2
	effect.client_effect_type = 0
	effect.cd_time = last_die_time
	for i = 1, 10 do
		effect.param_list[i] = 0
	end
	effect.param_list[2] = boss_weary < 5 and boss_weary or 5

	return effect
end

-- 获取主角Effect列表
function FightWGData:GetMainRoleShowEffect(is_gain)
	local effect_list = {}

	if is_gain == nil then
		local world_effect = self:GetWroldLevelEffect()
		if nil ~= world_effect then
			table.insert(effect_list, {type = 0, info = world_effect})
		end
	end

	local cd_time = 0
	for k, v in pairs(self.main_role_effect_list) do
		local cfg = self:GetBuffDescCfgByInfo(v.product_id, v.buff_type, v.client_effect_type)
		if cfg and (is_gain == nil or is_gain == cfg.shifouzengyi) then
			v.client_effect_type = cfg.effect_client_type
			-- if v.effect_type == FIGHT_EFFECT_TYPE.MOVESPEED then
			-- 	cd_time = v.param_list[3] or 0
			-- else
				cd_time = v.param_list[1] or 0
			-- end
			v.cd_time = math.max(cd_time / 1000 - (Status.NowTime - v.recv_time), 0)
			--VIP经验加成 策划说加成为0时，不显示
			if v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_OTHER_VIP2 or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_OTHER_VIP1 then
				local add_value = VipWGData.Instance:GetCurExpAdd()
				if add_value > 0 then
					table.insert(effect_list, {type = 1, info = v})
                end
            --世界boss复活疲劳
            elseif v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_WORLD_BOSS_ROLE_REALIVE then
                local time1 = BossWGData.Instance:GetWorldBossRoleRealiveTime()
                v.cd_time = math.max(time1 - TimeWGCtrl.Instance:GetServerTime() +  v.param_list[2], 0)
                table.insert(effect_list, {type = 1, info = v})
            --可显示多个经验药水
            elseif v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1
				or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2
				or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3 then
            	for i,exp_v in pairs(self.exp_buff_list) do
					if exp_v then
						if v.client_effect_type == exp_v.client_effect_type then
							table.insert(effect_list, {type = 1, info = v})
						else
							table.insert(effect_list, {type = 1, info = exp_v})
						end
					end
				end
			else
				table.insert(effect_list, {type = 1, info = v})
            end
		end
	end

	if is_gain == nil then
		for k,v in pairs(self.beauty_effect_list) do
			if nil ~= v then
				v.cd_time = v.param2 - TimeWGCtrl.Instance:GetServerTime()
				table.insert(effect_list, {type = 2, info = v})
			end
		end

		local has_yunshi_exp = QifuYunShiWGData.Instance:GetHasYunShiAdditionIdCfg(QifuYunShiWGData.ADDITION_TYPE.EXP_BUFF_TYPE)
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if has_yunshi_exp then
			local yunshi_exp_effect = FightWGData.CreateEffectInfo()
			yunshi_exp_effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_ITEM_EXP5
			yunshi_exp_effect.cd_time = TimeWGCtrl.Instance:NowDayTimeEnd(server_time) - server_time
			for i = 1, 10 do
				yunshi_exp_effect.param_list[i] = 0
			end
			yunshi_exp_effect.param_list[2] = has_yunshi_exp.addition_value
			table.insert(effect_list, {type = 1, info = yunshi_exp_effect})
		end

		--掉率buff
		local rare_attr_added_time = MainuiWGData.Instance:GetRouteInfo()
		local rare_cd_time = rare_attr_added_time - server_time
		if rare_cd_time > 0 then
			local rare_effect = FightWGData.CreateEffectInfo()
			rare_effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_ITEM_RARE
			rare_effect.cd_time = rare_cd_time
			for i = 1, 10 do
				rare_effect.param_list[i] = 0
			end

			table.insert(effect_list, {type = 1, info = rare_effect})
		end
    end

	return effect_list
end

-- 获取其他人Effect列表
function FightWGData:GetOtherRoleShowEffect(obj_id, is_gain)
	--is_gain = is_gain or 1
	local effect_list = {}
	local cd_time = 0
	local show
	for k, v in pairs(self.others_effect_list[obj_id] or {}) do
		local cfg = self:GetBuffDescCfgByInfo(v.product_id, v.buff_type, v.client_effect_type)
		if cfg then
			show = cfg.show == "" and 1 or cfg.show
			if show == 1 then
				v.client_effect_type = cfg.effect_client_type
				cd_time = v.param_list[1] or 0
				v.cd_time = cd_time / 1000 + v.recv_time
				table.insert(effect_list, {type = 1, info = v})
			end
		end
	end

	return effect_list
end

function FightWGData:SetExpBuffList(protocol)
	self.exp_buff_list = {}
	for k,v in pairs(protocol.exp_buff_list) do
		if v and v.exp_buff_left_time_s > 0 then
			local exp_buff_list = FightWGData.CreateEffectInfo()
			exp_buff_list.client_effect_type = EXP_BUFF_TYPE[v.exp_buff_level]
			exp_buff_list.recv_time = v.exp_buff_left_time_s
			exp_buff_list.residue_time = v.exp_buff_left_time_s
			exp_buff_list.exp_buff_level = v.exp_buff_level
			table.insert(self.exp_buff_list,exp_buff_list)
		end
	end
	table.sort(self.exp_buff_list, function(a, b) return a.exp_buff_level > b.exp_buff_level end )
	GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, true)
end

-- 获取Effect描述
function FightWGData:GetEffectDesc(effect_info)
	local data = effect_info.info
	local cfg = self:GetBuffDescCfgByType(data.client_effect_type)
	local desc = ""
	local name = ""
	if nil ~= cfg then
		name = cfg.name
		local desc_list = Split(cfg.desc, "##")
		-- "<color=#ebff7c>VIP加成：</color>
		-- ##\n攻击+<color=#ebff7c>[p_3]</color>##、生命+<color=#ebff7c>[p_6]</color>
		-- ##\n珍稀外观掉落+<color=#ebff7c>[p_9_m]%</color>
		-- ##\n珍稀装备掉落+<color=#ebff7c>[p_12_m]%</color>"
		for _, v in ipairs(desc_list) do
			local cur_desc = v
			local i, j = string.find(v, "(%[p_.-%])", 1)
			local loop = 0
			while(i ~= nil and j ~= nil and loop < 100) do
				local start_desc = string.sub(cur_desc, 0, i - 1)
				local end_desc = string.sub(cur_desc, j + 1, -1)
	            local str_arr = Split(string.sub(cur_desc, i + 1, j - 1), "_")
	            if #str_arr >= 2 then
					local index = tonumber(str_arr[2]) or 1
					local value = data.param_list[index] or 0
					if value > 0 then
						if effect_info.type == 1 and index == 1 and data.effect_type ~= FIGHT_EFFECT_TYPE.ATTR_PER then
							value = value / 1000
		                else
		                    if "w" == str_arr[3] then
		                        value = value / 100
		                    elseif "m" == str_arr[3] then
		                        value = value / 100000
		                    end
		                end
						cur_desc = start_desc .. value .. end_desc
					else
						cur_desc = ""
						break
		            end
		        else
					break
				end
				i, j = string.find(cur_desc, "(%[p_.-%])", 1)
		        loop = loop + 1
			end
			desc = desc .. cur_desc
		end
	end

	if data.residue_time and data.residue_time > 0 then
		desc = desc .. Language.Common.BufftipsText
	end

	if data.client_effect_type == EFFECT_CLIENT_TYPE.ECT_OTHER_HMCF then
		local evil = RoleWGData.Instance:GetAttr("evil") or 0
		local strevil = "(" .. string.format(Language.Tip.EvilValue, evil) ..")"
		desc = string.gsub(desc,"%($%)", strevil)
	end

	return desc, name
end


function FightWGData:GetBeHitInfo(deliverer)
	return self.be_hit_list[deliverer]
end

function FightWGData:OnHitTrigger(deliverer, target_obj)
	local info = self.be_hit_list[deliverer]
	if nil ~= info and nil ~= target_obj and not target_obj:IsDeleted() then
		self:DoBeHit(info, deliverer:IsMainRole(), target_obj:GetObjId())
		self.be_hit_list[deliverer] = nil
	end
end

function FightWGData.CreateBeHitInfo(deliverer, skill_id)
	return {
		deliverer = deliverer,
		skill_id = skill_id,
		max_trigger_time = Status.NowTime + SkillWGData.GetSkillBloodDelay(skill_id, deliverer),
		hit_info_list = {}
	}
end

function FightWGData:SaveBeHitInfo(obj_id, deliverer_id, skill_id, real_blood, blood, fighttype, nvshen_hurt, deputy_pet_deliverer, deliverer_wuxing_type)
	local deliverer = Scene.Instance:GetObj(deliverer_id)
	deliverer = deliverer or deputy_pet_deliverer

	local obj = Scene.Instance:GetObj(obj_id)
	if obj == nil or deliverer == nil then
		return
	end

	nvshen_hurt = nvshen_hurt or 0
	if nil ~= self.be_hit_list[deliverer] then
		local info = self.be_hit_list[deliverer]
		if skill_id ~= info.skill_id or Status.NowTime - info.max_trigger_time > 0.1 then
			self:DoBeHit(info, deliverer:IsMainRole(), nil)
			self.be_hit_list[deliverer] = FightWGData.CreateBeHitInfo(deliverer, skill_id)
		end
	else
		self.be_hit_list[deliverer] = FightWGData.CreateBeHitInfo(deliverer, skill_id)
	end

	table.insert(self.be_hit_list[deliverer].hit_info_list, {
		obj = obj,
		real_blood = real_blood,
		blood = blood,
		fighttype = fighttype,
		nvshen_hurt = nvshen_hurt,
		deliverer_wuxing_type = deliverer_wuxing_type,
	})
end

function FightWGData:DoBeHit(info, is_main_role, target_obj_id)
	for k, v in pairs(info.hit_info_list) do
		if v.obj ~= nil and not v.obj:IsDeleted() and v.obj:IsCharacter() then
			v.obj:DoBeHit(info.deliverer, info.skill_id, v.real_blood, v.blood, v.fighttype, nil, v.deliverer_wuxing_type)

			if v.nvshen_hurt and v.nvshen_hurt < 0 then
				v.obj:DoBeHit(info.deliverer, 0, 0, v.nvshen_hurt, v.fighttype, FIGHT_TEXT_TYPE.NVSHEN, v.deliverer_wuxing_type)
			end

			-- if not is_main_role or v.obj_id ~= target_obj_id then
				v.obj:DoBeHitShow(info.deliverer, info.skill_id, target_obj_id)
			-- end
		end
	end
end

function FightWGData:GetBuffDescCfgByType(effect_type)
	return ConfigManager.Instance:GetAutoConfig("buff_desc_auto").desc[effect_type]
end

-- (0, 10000) 或者 [20000, 无穷)  【角色技能id _拼接 子Buff类型】 or 【角色技能id】
-- (0, 10000) 主动技能id  
-- [20000, 无穷)  被动技能 和 专属技能的范围 后端定义的特殊类型(被动技能id + 20000)
-- [10000, 20000) 后端定义的特殊类型
function FightWGData:GetBuffDescCfgByInfo(skill_id, buff_type, effect_client_type)
	skill_id = skill_id or 0
	buff_type = buff_type or 0

	local buff_desc_cfg
	if (skill_id > 0 and skill_id < 10000) or skill_id >= 20000 then
		local key = string.format("%d_%d", skill_id, buff_type)
		buff_desc_cfg = self:GetBuffDescCfgByType(key)
		if not buff_desc_cfg then
			buff_desc_cfg = self:GetBuffDescCfgByType(skill_id)
		end
	end
	
	if not buff_desc_cfg then
		buff_desc_cfg = self:GetBuffDescCfgByType(effect_client_type)
	end

	return buff_desc_cfg
end





function FightWGData:GetMainRoleDrugAddExp()
	if #self.main_role_effect_list == 0 then return 0 end
	local effect_type = 0
	for k,v in pairs(self.main_role_effect_list) do
		if v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1
		or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2
		or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3
		or v.client_effect_type == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP4 then
			return v.param_list[3]
		end
	end

	return 0
end

function FightWGData:CanSendPerformSkill()
	return self.can_send_perform_skill
end

function FightWGData:SetCanSendPerformSkill(bool)
	self.can_send_perform_skill = bool
end

-- 获取buff特效配置
function FightWGData:GetBuffEffectCfg(buff_type, product_id)
	if buff_type == nil then
		return nil
	end

	if product_id then
		local cfg = CheckList(self.buff_effect_cfg, buff_type, product_id)
		if cfg then
			return cfg
		end
	end

	return CheckList(self.buff_effect_cfg, buff_type, -1)
end

function FightWGData:SetRoleReAliveType(realive_type)
	self.cur_realive_type = realive_type
end

function FightWGData:GetRoleReAliveType()
	return self.cur_realive_type
end
