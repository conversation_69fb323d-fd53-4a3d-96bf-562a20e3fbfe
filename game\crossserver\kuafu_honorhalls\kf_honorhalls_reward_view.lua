HonorhallsRewardView = HonorhallsRewardView or BaseClass(SafeBaseView)

function HonorhallsRewardView:__init()
	self.view_name = "HonorhallsRewardView"
	-- self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(704, 486)})
	self:AddViewResource(0, "uis/view/kuafu_honorhalls_ui_prefab", "honorhall_reward_view")
end

function HonorhallsRewardView:LoadCallBack()
	self:InitPanel()
end

function HonorhallsRewardView:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function HonorhallsRewardView:OnFlush()
	self:RefreshView()
end

function HonorhallsRewardView:InitPanel()
	self.item_list = AsyncListView.New(HonorhallsRewardItem, self.node_list.item_list)
	self.node_list.tips_label.text.text = Language.Honorhalls.RewardPanelTips
	self.node_list.title_view_name.text.text = Language.ViewName.rank
end

function HonorhallsRewardView:OnTieleClick(index)
	 TipWGCtrl.Instance:OpenItem({item_id = self.data[index].item_id})
end

function HonorhallsRewardView:RefreshView()
	local data_list = KuafuHonorhallWGData.Instance:GetTitleRewardInfo()
	self.item_list:SetDataList(data_list)
end

------------------------------------------------------------------------------------

HonorhallsRewardItem = HonorhallsRewardItem or BaseClass(BaseRender)

function HonorhallsRewardItem:__init()
	self.head_cell = BaseHeadCell.New(self.node_list.rank_player_head)
end

function HonorhallsRewardItem:__delete()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function HonorhallsRewardItem:OnFlush()
	local data = self:GetData()
	local index = self:GetIndex()

	local bundle, asset = "uis/view/kuafu_honorhalls_ui/images_atlas", "a3_yyhd_pmd" .. index
	self.node_list.rank_label_img.image:LoadSprite(bundle, asset, function()
		self.node_list.rank_label_img.image:SetNativeSize()
	end)

	self.node_list.rank_label.text.text = string.format(Language.Honorhalls.MyRank2, NumberToChinaNumber(index)) 
	self.node_list.rank_player_head:CustomSetActive(data.user_name ~= "")

	if data.user_name ~= "" then
		self.node_list.role_name.text.text = data.user_name
	else
		self.node_list.role_name.text.text = Language.OpenServer.XuWeiYiDai
	end

	if data.user_name ~= "" then
		local head_data = {fashion_photoframe = data.photoframe}
		head_data.role_id = data.uuid
		head_data.sex = data.sex
		head_data.prof = data.prof
		self.head_cell:SetData(head_data)
	end

	local bundle,asset = ResPath.GetTitleModel(data.title_id)
    self.node_list.title_img:ChangeAsset(bundle, asset)
end