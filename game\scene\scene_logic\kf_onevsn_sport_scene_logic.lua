
KFOneVsNSportSceneLogic = KFOneVsNSportSceneLogic or BaseClass(CrossServerSceneLogic)

function KFOneVsNSportSceneLogic:__init()

end

function KFOneVsNSportSceneLogic:__delete()

end

-- 进入场景
function KFOneVsNSportSceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	

	-- local scene_id = Scene.Instance:GetSceneId()
	-- if 7021 == scene_id then 			-- 擂台
	-- 	KFOneVsNSportWGCtrl.Instance:OpenPrepareView()
	-- 	KFOneVsNSportWGCtrl.Instance:CloseInfoView()
	-- 	GlobalTimerQuest:AddDelayTimer(function ()
	-- 		local main_role = Scene.Instance:GetMainRole()
	-- 		main_role:SetSpecialIcon("gc_" .. main_role:GetVo().special_param)
	-- 	end, 0.5)
		
	-- elseif 7020 == scene_id then 		-- 等候区
	-- 	KFOneVsNSportWGCtrl.Instance:OpenInfoView()
	-- 	KFOneVsNSportWGCtrl.Instance:ClosePrepareView()
	-- end
	

end

function KFOneVsNSportSceneLogic:Out()
	CrossServerSceneLogic.Out(self)
	-- KFOneVsNSportWGCtrl.Instance:ClosePrepareView()
	-- KFOneVsNSportWGCtrl.Instance:CloseInfoView()

	-- local main_role = Scene.Instance:GetMainRole()
	-- main_role:RemoveSpecialIcon()
	-- local mainrole = Scene.Instance:GetMainRole()
	-- if nil ~= mainrole then
	-- 	mainrole:RemoveAllBuff()
	-- end
end

-- 角色是否是敌人
function KFOneVsNSportSceneLogic:IsRoleEnemy(target_obj, main_role)
	if main_role:GetVo().special_param == target_obj:GetVo().special_param then			-- 同一边
		return false, Language.Fight.Side
	end

	return true
end

function KFOneVsNSportSceneLogic:IsEnemy(target_obj, main_role)
	if main_role:GetVo().special_param == target_obj:GetVo().special_param then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end

function KFOneVsNSportSceneLogic:OnClickHeadHandler(is_show)
	CrossServerSceneLogic.OnClickHeadHandler(self, is_show)
end

function KFOneVsNSportSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead()
		 or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

-- 获取挂机打怪的敌人
function KFOneVsNSportSceneLogic:GetGuiJiMonsterEnemy()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	return Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
end

-- 挂机打人
function KFOneVsNSportSceneLogic:GuaiJiRoleUpdate(now_time, elapse_time)
	if FightWGCtrl.Instance:HasAtkOperate() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role:CanDoAttack() then
		return
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if nil == target_obj or not target_obj:IsRole() or target_obj:IsRealDead() or not self:IsEnemy(target_obj, main_role) then
		-- self:SetGuaiJi(GUAI_JI_TYPE.NOT)
		return
	end

	local skill_index = 0
	if main_role:IsChenMo() then
		skill_index = SkillWGData.Instance:GetDefaultSkillIndex()
	else
		skill_index = SkillWGData.Instance:RandSkill()
	end
	if skill_index < 0 then
		skill_index = SkillWGData.Instance:GetDefaultSkillIndex()
	end
	if skill_index < 0 then
		self:SetGuaiJi(GUAI_JI_TYPE.NOT)
		return
	end

	if SkillWGData.Instance:IsInSkillCD(skill_index) then
		return
	end

	local target_x, target_y = target_obj:GetLogicPos()
	FightWGCtrl.Instance:DoAtkOperate(skill_index, target_x, target_y, target_obj, true)
end