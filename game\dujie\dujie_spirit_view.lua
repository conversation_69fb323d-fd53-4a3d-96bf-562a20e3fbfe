local SPIRIT_ANIM_WAIT_TIME = 1.5
local SPIRIT_ANIM_SHOW_TIME = 1.2

function DujieView:LoadSpiritCallBack()
    -- 技能列表
    if not self.spirit_type_list then
		self.spirit_type_list = AsyncListView.New(<PERSON><PERSON><PERSON><PERSON>piritTypeRender, self.node_list.spirit_type_list)
        self.spirit_type_list:SetStartZeroIndex(true)
		self.spirit_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSpiritCB, self))
        self.spirit_type_list.IsLimitSelectByIndex = function(list, cell_index)
			-- local page_data = list:GetCellData(cell_index)
            local is_open = CultivationWGData.Instance:IsOpenAngerSkill(cell_index)
            if not is_open then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.SpiritLimitStr)
                return true
            end
            return false
		end
	end

    --类别列表
	if not self.spirit_skill_list then
        self.spirit_skill_list = {}
        for i = 1, 4 do
            local cell = DuJieSpiritSkillRender.New(self.node_list.spirit_skill_list:FindObj(string.format("skill_render_0%d", i)))
            cell:SetIndex(i)
            cell:SetClickCallBack(BindTool.Bind1(self.OnSelectSpiritSkillCB, self))
            self.spirit_skill_list[i] = cell
        end
	end

    -- 基础属性
    if self.spirit_attr_list == nil then
        self.spirit_attr_list = {}
        for i = 1, 8 do
            local attr_obj = self.node_list.spirit_attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.spirit_attr_list[i] = cell
            end
        end
    end

    -- 形象点击
    if self.spirit_image_list == nil then
        self.spirit_image_list = {}
        for i = 1, 3 do
            local attr_obj = self.node_list.spirit_image_list:FindObj(string.format("spirit_image_render_%d", i))
            if attr_obj then
                local cell = DuJieSpiritImageRender.New(attr_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectSpiritImageCB, self))
                self.spirit_image_list[i] = cell
            end
        end
    end

    if not self.spirit_spend_item then
        self.spirit_spend_item = ItemCell.New(self.node_list.spirit_spend_item)
    end

    XUI.AddClickEventListener(self.node_list.btn_spirit_active, BindTool.Bind(self.OnClickSpiritActive, self))
    XUI.AddClickEventListener(self.node_list.btn_spirit_transform, BindTool.Bind(self.OnClickSpiritTransform, self))
    XUI.AddClickEventListener(self.node_list.btn_spirit_image_pre, BindTool.Bind(self.OnClickSpiritImagePre, self))
    XUI.AddClickEventListener(self.node_list.btn_spirit_mastery, BindTool.Bind(self.OnClickSpiritMastery, self))
    XUI.AddClickEventListener(self.node_list.btn_spirit_skill_show, BindTool.Bind(self.OnClickSpiritSkillShow, self))
end

function DujieView:ReleaseSpiritView()
    if self.spirit_type_list then
		self.spirit_type_list:DeleteMe()
		self.spirit_type_list = nil
	end

    if self.spirit_skill_list and #self.spirit_skill_list > 0 then
		for _, spirit_skill_cell in ipairs(self.spirit_skill_list) do
			spirit_skill_cell:DeleteMe()
			spirit_skill_cell = nil
		end

		self.spirit_skill_list = nil
	end

    if self.spirit_attr_list and #self.spirit_attr_list > 0 then
		for _, spirit_attr_cell in ipairs(self.spirit_attr_list) do
			spirit_attr_cell:DeleteMe()
			spirit_attr_cell = nil
		end

		self.spirit_attr_list = nil
	end

    if self.spirit_image_list and #self.spirit_image_list > 0 then
		for _, spirit_image_cell in ipairs(self.spirit_image_list) do
			spirit_image_cell:DeleteMe()
			spirit_image_cell = nil
		end

		self.spirit_image_list = nil
	end

    if self.spirit_spend_item then
        self.spirit_spend_item:DeleteMe()
        self.spirit_spend_item = nil
    end

    self.cur_select_type_index = nil
    self.image_show_lv = nil
    self:RemoveSpiritEffectDelayTimer()
end


function DujieView:OnSelectSpiritCB(type_item, cell_index, is_default, is_click)
    if nil == type_item or nil == type_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = type_item:GetIndex()
	-- if self.cur_select_type_index == cell_index then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
    --     return
	-- end
    self.node_list.btn_spirit_image_pre:SetActive(cell_index == 0)

	self.cur_select_type_index = cell_index
    self:SetSpiritEffectShow(cell_index, true)
    self.image_show_lv = nil
    self:FlushSpiritSkillList()
    self:FlushSpiritSkillModel()
    self:FlushSpiritMessage()
    self:ShowSelectSpiritAnim()
end

function DujieView:DuJieSpiritClose()
    self.spirit_anim_cache = nil
    self.spirit_init_anim = nil
end

-- 展示当前的动画特效
function DujieView:ShowSelectSpiritAnim()
    -- self.node_list.dujie_spirit_root:CustomSetActive(true)
    -- if not self.cur_select_type_index then
    --     return
    -- end

    -- -- if not self.spirit_anim_cache then
    -- --     self.spirit_anim_cache = {}
    -- -- end

    -- -- if self.spirit_anim_cache[self.cur_select_type_index] then
    -- --     return
    -- -- end

    -- if self.spirit_anim_cache then
    --     return
    -- end

    -- if not self.spirit_init_anim then
    --     self.spirit_init_anim = true
    --     self.node_list.dujie_spirit_root:CustomSetActive(false)
    --     self:ShowSelectSpiritAnimRoot(true)
    --     self:RemoveSpiritEffectDelayTimer()
    --     self.show_spirit_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
    --         self:ShowSelectSpiritAnim()
    --     end, SPIRIT_ANIM_WAIT_TIME)
    --     return
    -- end

    -- -- self.spirit_anim_cache[self.cur_select_type_index] = true
    -- self.spirit_anim_cache = true
    -- self.node_list.dujie_spirit_root:CustomSetActive(false)
    -- self:ShowSelectSpiritAnimRoot()

    -- self:RemoveSpiritEffectDelayTimer()
	-- self.show_spirit_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
    --     self.node_list.dujie_spirit_root:CustomSetActive(true)
    --     self:ShowSelectSpiritAnimRoot(true)
	-- end, SPIRIT_ANIM_SHOW_TIME)
end

function DujieView:ShowSelectSpiritAnimRoot(is_not_show)
    for i = 0, 2, 1 do
        if self.node_list and self.node_list[string.format("dujie_spirit_effect_%d", i)] then
            self.node_list[string.format("dujie_spirit_effect_%d", i)]:CustomSetActive((not is_not_show) and i == self.cur_select_type_index)
        end
    end
end

--移除回调
function DujieView:RemoveSpiritEffectDelayTimer()
    if self.show_spirit_effect_timer then
        GlobalTimerQuest:CancelQuest(self.show_spirit_effect_timer)
        self.show_spirit_effect_timer = nil
    end
end

function DujieView:OnSelectSpiritSkillCB(skill_item)
    if nil == skill_item or nil == skill_item.data then
		return
	end

    local skill_id = skill_item.data
    local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
    local skill_cfg = SkillWGData.Instance:GetXiuXianSkillConfig(skill_id)

    if client_skill_cfg and skill_cfg then
		local show_data = {
            icon = client_skill_cfg.icon_resource,
            top_text = skill_cfg.skill_name,
            body_text = client_skill_cfg.description,
            x = 0,
            y = -120,
            set_pos = true,
            hide_next = true,
            is_active_skill = true,
        }
        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

-- 点击形象查看
function DujieView:OnSelectSpiritImageCB(image_cell)
    if nil == image_cell or nil == image_cell.data then
		return
	end

    local title_str = Language.Cultivation.AngerSkillType2[self.cur_select_type_index] or ""
    local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, image_cell.data.image_lv)
    local image_name_str = cfg and cfg.nuqi_lv_name or ""
    local show_lv = image_cell.data.image_lv > 0 and image_cell.data.image_lv or 1
    local tips_str = string.format(Language.Cultivation.ImageItemErrorTips, title_str, show_lv, image_name_str)
    SysMsgWGCtrl.Instance:ErrorRemind(tips_str)
end

function DujieView:DuJieSpiritFlush(open_param)
    if not self.spirit_type_list then
        return 
    end
    local jump_index = nil
    local select_index = nil

    if open_param ~= nil then
        jump_index = tonumber(open_param)
    end

    if jump_index == nil then
        select_index = self.cur_select_type_index
    else
        select_index = jump_index
    end

    if select_index == nil then
        select_index = 0
    end

    local type_list = Language.Cultivation.AngerSkillType
    -- self.cur_select_type_index = nil
    self.spirit_type_list:SetDataList(type_list)

    self.spirit_type_list:JumpToIndex(select_index, 10)
end 


-- 刷新技能列表
function DujieView:FlushSpiritSkillList()
    local skill_list = CultivationWGData.Instance:GetActiveSkillListByType(self.cur_select_type_index) 
    local star_index = 1

    for i, anger_skill_cell in ipairs(self.spirit_skill_list) do
        local skill_index = star_index + i
        anger_skill_cell:SetVisible(skill_list[skill_index] ~= nil)

        if skill_list[skill_index] ~= nil then
            anger_skill_cell:SetData(skill_list[skill_index])
        end
    end
end

--刷新模型
function DujieView:FlushSpiritSkillModel()
    if not self.cur_select_type_index or not self.is_cg_loaded then
        return
    end

    local image_list, image_lv, image_index = CultivationWGData.Instance:GetProgressImage(self.cur_select_type_index)
    local max_image_count = #image_list or 0      --形象最大等级
    self.node_list.spirit_progress.slider.value = (image_index - 1) / (max_image_count - 1)

    for i, spirit_image_cell in ipairs(self.spirit_image_list) do
        spirit_image_cell:SetVisible(image_list[i] ~= nil)

        if image_list[i] ~= nil then
            spirit_image_cell:SetData(image_list[i])
        end
    end

    --刷新模型
    local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, image_lv)
    if cfg ~= nil and self.dujie_cg and self.image_show_lv ~= image_lv then
        local ys_role, is_init = self.dujie_cg:GetRoleModelByNodeName("Character_ys")
        if ys_role then
            local nuqi_client_type = self.cur_select_type_index + 1
            local main_vo = GameVoManager.Instance:GetMainRoleVo()
            local special_res_id = string.format("%s0%s", RoleWGData.GetJobModelId(main_vo.sex, 1), nuqi_client_type) 
            local bundle, asset = ResPath.GetRoleModel(special_res_id)
            local prof = main_vo.prof
            local sex = main_vo.sex
            local body_res, face_res, hair_res = RoleWGData.GetShowRoleRealmSkinPartRes(sex, prof, cfg.default_body, cfg.default_face, cfg.default_hair)

            local extra_model_data = {
                role_body_res = body_res,
                role_face_res = face_res,
                role_hair_res = hair_res,
                is_realm = true,
            }

            ys_role:ChangeModel(SceneObjPart.Main, bundle, asset, function()
                local obj = ys_role:GetRoot().gameObject

                self:FlushSpiritCGModelTrack()
                if obj then
                    obj.transform.localPosition = Vector3(0, 0, 0)
                    obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
                end
            end, DRAW_MODEL_TYPE.ROLE, extra_model_data)
        end
    end

    self.image_show_lv = image_lv
end

function DujieView:FlushSpiritCGModelTrack()
    if self.dujie_cg then
        local ys_role, is_init = self.dujie_cg:GetRoleModelByNodeName("Character_ys")
        if ys_role then
            local obj = ys_role:GetPart(SceneObjPart.Main)
            if obj and obj:GetObj() then
                local role_obj = obj:GetObj().gameObject
                local animator = role_obj:GetComponent(typeof(UnityEngine.Animator))
                self.dujie_cg:SetSpiritTrackName(self.cur_select_type_index == 1)
                if animator then
                    self.dujie_cg:AddRoleModelToActor("Character_ys_ani", animator)
                    
                end
            end
        end
    end
end

-- 刷新等级属性
function DujieView:FlushSpiritMessage()
    local bundle, asset = ResPath.GetDujieImg(string.format("a3_dz_lv_bg_%d", self.cur_select_type_index))
    self.node_list.spirit_title_bg.image:LoadSprite(bundle, asset, function()
        self.node_list.spirit_title_bg.image:SetNativeSize()
    end)
    
    local nuqi_level = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    local nuqi_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, nuqi_level)
    self.node_list.spirit_desc_txt.text.text = nuqi_upgrade_cfg and nuqi_upgrade_cfg.desc or ""
    local level_info = CultivationWGData.Instance:GetAngerLevelInfo(self.cur_select_type_index)
    local nuqi_upgrade_level = level_info and level_info.nuqi_upgrade_level or 0
    local next_upgrade_level = nuqi_upgrade_level + 1
    local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, nuqi_upgrade_level)
    local next_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, next_upgrade_level)
    local name = cfg and cfg.nuqi_lv_name or ""
    local attr_list = CultivationWGData.Instance:GetAttrListByCfg(cfg, next_cfg)
    self.node_list.spirit_spend_item:CustomSetActive(next_cfg ~= nil)
    self.node_list.btn_spirit_active:CustomSetActive(next_cfg ~= nil)
    self.node_list.spirit_level_flag:CustomSetActive(next_cfg == nil)

    if nuqi_upgrade_cfg then
        self.spirit_spend_item:SetData({ item_id = nuqi_upgrade_cfg.item_id })
        local item_num = ItemWGData.Instance:GetItemNumInBagById(nuqi_upgrade_cfg.item_id)
        local color = item_num >= nuqi_upgrade_cfg.item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list.btn_spirit_active_remind:CustomSetActive(item_num >= nuqi_upgrade_cfg.item_num and next_cfg ~= nil)
        self.spirit_spend_item:SetRightBottomTextVisible(true)
        self.spirit_spend_item:SetRightBottomColorText(item_num .. '/' .. nuqi_upgrade_cfg.item_num, color)
    end

    -- 刷新幻化状态
    local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
    local str = curr_nuqi_type == self.cur_select_type_index and Language.Common.YiHuanHua or Language.Common.HuanHua
    self.node_list.btn_spirit_transform_text.text.text = str
    local is_can_anger_buff_upgrade = CultivationWGData.Instance:BuffUplevelStuffRed(self.cur_select_type_index)

    self.node_list.btn_spirit_mastery_remind:SetActive(is_can_anger_buff_upgrade)
    -- local is_act = CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index)
    -- self.node_list.spirit_image_tips:CustomSetActive(not is_act)

    if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
        self.node_list.title_spirit_active_txt.text.text = Language.Cultivation.CostUpgradeDesc
        self.node_list.btn_spirit_active_txt.text.text = Language.Common.Up
    else
        self.node_list.title_spirit_active_txt.text.text = Language.Cultivation.CostActiveDesc
        self.node_list.btn_spirit_active_txt.text.text = Language.Common.Activate
    end

    if cfg then
        if nuqi_upgrade_level > 0 then
            self.node_list.spirit_title_txt.text.text = string.format("%s·%s", name, string.format(Language.Role.XXJie, nuqi_upgrade_level))
        else
            self.node_list.spirit_title_txt.text.text = name
        end
    end

    for index, attr_cell in ipairs(self.spirit_attr_list) do
        attr_cell:SetVisible(attr_list[index] ~= nil)

        if attr_list[index] ~= nil then
            attr_cell:SetData(attr_list[index])
        end
    end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

    for index, attr_cell in ipairs(attr_list) do
		if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
			add_tab(attr_str, attr_cell.attr_value)
		end
    end

	local cap = AttributeMgr.GetCapability(attribute)
    self.node_list.spirit_common_capability:SetActive(cap ~= 0)
    self.node_list.spirit_cap_value.text.text = cap
end
------------------------------------------------------------------------------
-- 激活
function DujieView:OnClickSpiritActive()
    if not self.cur_select_type_index then
        return
    end

    local nuqi_level = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    local max_level = CultivationWGData.Instance:GetNuqiMaxLevel(self.cur_select_type_index)

    if nuqi_level < max_level then
        local nuqi_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, nuqi_level)
        if nuqi_upgrade_cfg then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(nuqi_upgrade_cfg.item_id)
            if item_num >= nuqi_upgrade_cfg.item_num then
                CultivationWGCtrl.Instance:ActiveAngerType(self.cur_select_type_index)
            else
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = nuqi_upgrade_cfg.item_id })
            end
        end
    end
end

-- 点击幻化
function DujieView:OnClickSpiritTransform()
    if not self.cur_select_type_index then
        return
    end

    local nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
    if nuqi_type == self.cur_select_type_index then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips4)
        return
    end

    if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips3)
        CultivationWGCtrl.Instance:ChooseAngerType(self.cur_select_type_index)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips)
    end
end

-- 打开形象预览
function DujieView:OnClickSpiritImagePre()
    DujieWGCtrl.Instance:OpenDujieSpiritImageView(self.cur_select_type_index)
end

-- 点击专精预览
function DujieView:OnClickSpiritMastery()
    DujieWGCtrl.Instance:OpenDujieSpiritBuffView(self.cur_select_type_index)
end

-- 点击专精预览
function DujieView:OnClickSpiritSkillShow()
    if not self.cur_select_type_index then
        return
    end

    -- CommonSkillShowCtrl.Instance:SetSpiritSkillViewDataAndOpen(self.cur_select_type_index)

    DujieWGCtrl.Instance:OpenDujieSpiritSkillPreView(self.cur_select_type_index)
end

------------------------------------------------------------------------------
----------------------------------页签列表-----------------------
-- 技能类别
DuJieSpiritTypeRender = DuJieSpiritTypeRender or BaseClass(BaseRender)
function DuJieSpiritTypeRender:OnFlush()
	if not self.data then
		return
	end

    -- 本体可升级
    local is_red = CultivationWGData.Instance:IsCanAngerUpgrade(self.index)
    if CultivationWGData.Instance:IsActiveAnger(self.index) then
        -- buff可升级
        is_red = is_red or CultivationWGData.Instance:BuffUplevelStuffRed(self.index)
    end

    self.node_list.remind:CustomSetActive(is_red)

    local bundle, asset = ResPath.GetDujieImg(string.format("a3_dz_spirit_type_%d", self.index))
    self.node_list.normal_img.image:LoadSprite(bundle, asset, function()
        self.node_list.normal_img.image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetDujieImg(string.format("a3_dz_spirit_type_%d_hl", self.index))
    self.node_list.select_img.image:LoadSprite(bundle, asset, function()
        self.node_list.select_img.image:SetNativeSize()
    end)
end

function DuJieSpiritTypeRender:OnSelectChange(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
    self.node_list.select:CustomSetActive(is_select)
end

-- 技能
DuJieSpiritSkillRender = DuJieSpiritSkillRender or BaseClass(BaseRender)
function DuJieSpiritSkillRender:OnFlush()
	if not self.data then
		return
	end

    local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.data)
    local skill_cfg = SkillWGData.Instance:GetXiuXianSkillConfig(self.data)

    if client_skill_cfg and skill_cfg then
        self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_skill_cfg.icon_resource))
        self.node_list.skill_name.text.text = skill_cfg.skill_name
    end
end

-- 技能
DuJieSpiritImageRender = DuJieSpiritImageRender or BaseClass(BaseRender)
function DuJieSpiritImageRender:OnFlush()
	if not self.data then
		return
	end

    self.node_list.normal:CustomSetActive(self.data.is_lock)
    self.node_list.select:CustomSetActive(not self.data.is_lock)
end
