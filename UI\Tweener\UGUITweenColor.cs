﻿using UnityEngine;
using UnityEngine.UI;


[AddComponentMenu("UGUI/Tween/UGUI Tween Color")]
public class UGUITweenColor : UGUITweener 
{
	public Color from = Color.white;
	public Color to = Color.white;


	bool mCached = false;

	RawImage m_rawimage = null;
	Image m_Image = null;
	Text m_text = null;

	void Cache ()
	{
		mCached = true;
		m_rawimage = GetComponent<RawImage> ();
		m_Image = GetComponent<Image> ();
		m_text = GetComponent<Text> ();
	}

	[System.Obsolete("Use 'value' instead")]
	public Color color { get { return this.value; } set { this.value = value; } }

	public Color value
	{
		get
		{
			if (!mCached) Cache();
			if (m_rawimage != null)
				return m_rawimage.color;
			if (m_Image != null)
				return m_Image.color;
			if (m_text != null)
				return m_text.color;
			return Color.black;
		}
		set
		{
			if (!mCached) Cache();
			if (m_rawimage != null)
				m_rawimage.color = value;
			if (m_Image != null)
				m_Image.color = value;
			if (m_text != null)
				m_text.color = value;
		}
	}

	/// <summary>
	/// Tween the value.
	/// </summary>

	protected override void OnUpdate (float factor, bool isFinished) { value = Color.Lerp(from, to, factor); }

	static public UGUITweenColor Begin (GameObject go, float duration, Color color)
	{
		#if UNITY_EDITOR
		if (!Application.isPlaying) return null;
		#endif
		UGUITweenColor comp = UGUITweener.Begin<UGUITweenColor>(go, duration);
		comp.from = comp.value;
		comp.to = color;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	[ContextMenu("Set 'From' to current value")]
	public override void SetStartToCurrentValue () { from = value; }

	[ContextMenu("Set 'To' to current value")]
	public override void SetEndToCurrentValue () { to = value; }

	[ContextMenu("Assume value of 'From'")]
	void SetCurrentValueToStart () { value = from; }

	[ContextMenu("Assume value of 'To'")]
	void SetCurrentValueToEnd () { value = to; }
}
