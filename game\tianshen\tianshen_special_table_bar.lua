-----------------------------------------------------
local TOGGLE_MAX = 5
-- 新增优化列表展示
function TianShenView:FlushActivationAccordionTable()
	local data_list = TianShenWGData.Instance:GetTianShenInfoAccordionTable(TianShenView.TabIndex.Activation)
	for i = 1, TOGGLE_MAX do
		-- 如果里面的子成就都没开启  则隐藏大标签
		local show_flag = false
		local show_list = {}
		local accor_data = data_list[i]

		if accor_data then
			for k, v in pairs(accor_data) do
				local is_show = TianShenWGData.Instance:GetTianShenItemIsCanShow(v.index)
				local is_can_active = TianShenWGData.Instance:SpecialTianshenCanActive(v.index)
				if is_show or is_can_active then
					show_flag = true
					break
				end
			end
		end

		self.node_list[string.format("SelectBtn_%d", i)]:SetActive(show_flag)
		--self.node_list[string.format("List_%d", i)]:SetActive(show_flag)
	end
end

-- 创建扩展列表
function TianShenView:CreateActivationAccordion(show_type)
	if self.tianshen_accordion_list then 
		self:RefreshAccordionRed(show_type)
		return 
	end
	-- if nil == self.tianshen_cell_list then return end
	--成就目录
	local data_list = TianShenWGData.Instance:GetTianShenInfoAccordionTable(TianShenView.TabIndex.Activation)
	-- print_error("获取成就目录",accordion_tab)
	self.tianshen_accordion_list = {}
	self.tianshen_accordion_cell_list = {}
	self.jump_defult_tianshen_index = true
	for i = 1, TOGGLE_MAX do
        self.tianshen_accordion_list[i] = {}
        local parent = self.node_list[string.format("SelectBtn_%d", i)].transform
		self.tianshen_accordion_list[i].text_name = self:GetActivationNodeList(parent, "noraml/text_btn")
		self.tianshen_accordion_list[i].text_high_name = self:GetActivationNodeList(parent, "Image_hl/text_high_btn")
		self.tianshen_accordion_list[i].select_btn = self.node_list[string.format("SelectBtn_%d", i)]--按钮
		self.tianshen_accordion_list[i].select_btn.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickActivationTypeHandler, self, i))
		self.tianshen_accordion_list[i].list =  self.node_list[string.format("List_%d", i)]--列表
        self.tianshen_accordion_list[i].red_dot = self:GetActivationNodeList(parent, "img_read") --红点

		local color = i + 2
		local bundle, asset = ResPath.GetCommonButton(string.format("a3_sz_btn_%d", color))
		local normal_bg = self:GetActivationNodeList(parent, "noraml")
		self.tianshen_accordion_list[i].normal_bg = normal_bg
		if normal_bg then
			normal_bg.image:LoadSprite(bundle, asset)
		end
		
		local select_bg = self:GetActivationNodeList(parent, "Image_hl")
		self.tianshen_accordion_list[i].select_bg = select_bg
		if select_bg then
			select_bg.image:LoadSprite(bundle, asset)
		end

		local btn_str = Language.TianShen.TianShenSeriesColorStr[i]--string.format(Language.TianShen.TianShenSeriesColor[i], Language.TianShen.TianShenSeriesColorStr[i])
		--local color_str = btn_str--ToColorStr(btn_str, color)

		self.tianshen_accordion_list[i].list_item_cell = {}
		local accor_data = nil
		if nil ~= data_list[i] then
			self.tianshen_accordion_list[i].text_name.text.text = btn_str
			self.tianshen_accordion_list[i].text_high_name.text.text = btn_str
			accor_data = data_list[i]
		end
	
		self:LoadActivationCell(i, accor_data, show_type)
	end
end

function TianShenView:ReleaseActivationAccordion()
	if not self.tianshen_accordion_cell_list then return end

	for i, v in pairs(self.tianshen_accordion_cell_list) do
		for ii, vv in ipairs(v) do
			vv:DeleteMe()
		end
	end

	self.tianshen_accordion_cell_list = nil
	self.tianshen_accordion_list = nil
	self.accordion_list_loaded = nil

	self:RemoveEffectDelayTimer()
end

-- 获取节点
function TianShenView:GetActivationNodeList(parent, str)
    local obj = parent.transform:Find(str).gameObject
    local item = U3DObject(obj, obj.transform, self)
    return item
end

function TianShenView:LoadActivationCell(index, accor_data, show_type)
	if nil == accor_data then
		return
	end

	local res_async_loader = AllocResAsyncLoader(self, "drop_down_render" .. index)
	res_async_loader:Load("uis/view/tianshen_prefab", "drop_down_render", nil,
		function(new_obj)
			local item_vo = {}
			for i = 1, #accor_data do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.tianshen_accordion_list[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.tianshen_accordion_list[index].list.toggle_group
				self.tianshen_accordion_list[index].list_item_cell[i] = obj
				local item_render = TianshenActivationItem.New(obj)
				item_render:SetClickCallBack(BindTool.Bind(self.OnClickActivationItemHandler, self), true)
				item_render:SetShowType(show_type)
				item_render:SetData(accor_data[i]) --里层按钮赋值信息

				if accor_data[i].index then
					local is_show = TianShenWGData.Instance:GetTianShenItemIsCanShow(accor_data[i].index)
					local is_can_active = TianShenWGData.Instance:SpecialTianshenCanActive(accor_data[i].index) 
					item_render:SetVisible(is_show or is_can_active)
				end

				item_vo[i] = item_render
			end

			self.tianshen_accordion_cell_list[index] = item_vo
			if index == TOGGLE_MAX then
				self:RefreshAccordionRed(show_type)
				
				self.accordion_list_loaded = true
				if self.accordion_list_series and self.accordion_list_index then
					self:AccordionJumpToSelect(self.accordion_list_series, self.accordion_list_index)
					self.accordion_list_index = nil
					self.accordion_list_series = nil
				end
			end
		end
	)
end

--刷新列表
function TianShenView:RefreshAccordionData(show_type)
	local data_list = TianShenWGData.Instance:GetTianShenInfoAccordionTable(TianShenView.TabIndex.Activation)
	if nil == data_list then return end
	if nil == self.tianshen_accordion_cell_list then return end

	for i, v in pairs(self.tianshen_accordion_cell_list) do
		local data = data_list[i]
		for ii, vv in ipairs(v) do
			vv:SetShowType(show_type)
			vv:SetData(data[ii])
			local cell_data = data[ii]
			if cell_data.index then
				local is_show = TianShenWGData.Instance:GetTianShenItemIsCanShow(cell_data.index)
				local is_can_active = TianShenWGData.Instance:SpecialTianshenCanActive(cell_data.index) 
				vv:SetVisible(is_show or is_can_active)
			end
		end
	end
end

-- 刷新总红点
function TianShenView:RefreshAccordionRed(show_type)
	local data_list = TianShenWGData.Instance:GetTianShenInfoAccordionTable(TianShenView.TabIndex.Activation)
	if nil == data_list then return end
	if nil == self.tianshen_accordion_cell_list then return end

	for i, v in pairs(self.tianshen_accordion_cell_list) do
		local data = data_list[i]
		local red = false

		if show_type == TianShenView.TabIndex.Activation then
			red = TianShenWGData.Instance:SpecialTianshenSeriesCanRemind(i)
		else
			local is_open_huamo_func = FunOpen.Instance:GetFunIsOpened(FunName.TianShenHuamoView)
			local is_open_shuangsheng_func = FunOpen.Instance:GetFunIsOpened(FunName.TianShenShuangShengView)

			for _, cell_data in ipairs(data) do
				if show_type == TianShenView.TabIndex.ShenShi then
					local has_red, compose_red = TianShenWGData.Instance:CheckEquipShenShiRed(cell_data.index)
					local succinct_red = TianShenWGData.Instance:GetTianShenShenShiRefineRed(cell_data.index)
					red = has_red > 0 or compose_red == 1 or succinct_red
				
					if red then
						break
					end
				elseif show_type == TianShenView.TabIndex.TSInfo then
					-- 天神入魔
					local is_huamo_remind = TianShenHuamoWGData.Instance:GetSubRemind(cell_data.index) == 1

					--天神双生
					local is_shuangsheng_remind = TianShenShuangShengWGData.Instance:GetOneTianShenAvatarRemind(cell_data.index)
					local is_active = TianShenWGData.Instance:GetTianshenInfoStatus(cell_data.index) == 1

					red = (is_open_huamo_func and is_huamo_remind) or (is_open_shuangsheng_func and is_shuangsheng_remind and is_active)
					if red then
						break
					end
				end
			end
		end

		self.tianshen_accordion_list[i].red_dot:CustomSetActive(red)
	end
end

-- 跳转到对应的选择
function TianShenView:AccordionJumpToSelect(series, index, is_cell)
	if not self.accordion_list_loaded then
		self.accordion_list_series = series
		self.accordion_list_index = index
		return
	end

	if not self.tianshen_accordion_list then return end

	if self.tianshen_accordion_list[series] and (not is_cell) then
		self.tianshen_accordion_list[series].select_btn.toggle.isOn = true
	end

	if self.tianshen_accordion_list[series].list_item_cell and self.tianshen_accordion_list[series].list_item_cell[index] then
		local cell_toggle = self.tianshen_accordion_list[series].list_item_cell[index]:GetComponent("Toggle")
		if cell_toggle then
			if cell_toggle.isOn then
				local cell = self.tianshen_accordion_cell_list[series][index]
				self:OnClickActivationItemHandler(cell)
			else
				-- 这里延时0.1秒，父节点没激活情况下设置isOn不生效
				self:RemoveEffectDelayTimer()
				self.show_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
					if cell_toggle then
						cell_toggle.isOn = true
					end
				end, 0.1)
			end
		end
	end
end

--移除回调
function TianShenView:RemoveEffectDelayTimer()
    if self.show_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_delay_timer)
        self.show_delay_timer = nil
    end
end

-- 跳转到对应对象
function TianShenView:AccordionJumpToSelectForIndex(tianshen_index)
	local data_list = TianShenWGData.Instance:GetTianShenInfoAccordionTable(TianShenView.TabIndex.Activation)

	if tianshen_index == -1 then --  先获取当前上阵
		for _, v in pairs(data_list) do
			for _, n in pairs(v) do
				if n.battle_status >= 0 then
					tianshen_index = n.index
					break
				end
			end

			if tianshen_index ~= -1 then
				break
			end
		end
	end
	
	if tianshen_index == -1 then --  先获取当前上阵
		tianshen_index = 4
	end

	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(tianshen_index)
	local select_table = {}
	local series = 1
	local jump_index = 1

	if not tianshen_cfg then
		return 1, 1
	end

	series = tianshen_cfg.series
	for k, v in pairs(data_list) do
		if tianshen_cfg.series == k then
			select_table = v
		end
	end

	for i, v in pairs(select_table) do
		if v.index == tianshen_index then
			jump_index = i
		end
	end

	self:AccordionJumpToSelect(series, jump_index)
end

function TianShenView:OnClickActivationTypeHandler(series, isOn)
	if series == nil or not isOn then
		return
	end

	local jump_sub_index = nil
	local first_show_index = nil
	local data_list = TianShenWGData.Instance:GetTianShenInfoAccordionTable(TianShenView.TabIndex.Activation)
	local select_table = {}
	for k, v in ipairs(data_list[series]) do
		if series == v.series then
			table.insert(select_table, v)
		end
	end

	for i, v in ipairs(select_table) do
		local red = TianShenWGData.Instance:SpecialTianshenCanActive(v.index) 
			or TianShenWGData.Instance:SpecialTianshenCanUpStar(v.index) 
			or TianShenWGData.Instance:SpecialTianshenCanUpGrade(v.index)


		local is_show = TianShenWGData.Instance:GetTianShenItemIsCanShow(v.index)
		if is_show and red then
			jump_sub_index = i
			break
		elseif is_show and not first_show_index then
			first_show_index = i
		end
	end

	if not jump_sub_index then
		jump_sub_index = first_show_index or 1
	end

	self:AccordionJumpToSelect(series, jump_sub_index, true)
	if self.tianshen_accordion_cell_list and self.tianshen_accordion_cell_list[series] and self.tianshen_accordion_cell_list[series][jump_sub_index] then
		local cell = self.tianshen_accordion_cell_list[series][jump_sub_index]
		self:OnClickActivationItemHandler(cell)
	end
end

function TianShenView:OnClickActivationItemHandler(cell)
	if nil == cell or nil == cell:GetData() then
        return
    end

	local data = cell:GetData()
	TianShenWGData.Instance:InfoTsSelectIndex(data.index)
	TianShenWGData.Instance:ActivationTsSelectIndex(data.index)
	TianShenWGData.Instance:ShenShiTsSelectIndex(data.index)

	if self.show_index == TianShenView.TabIndex.TSInfo then
		self:OnClickCInfoItemRenderCalllBack(cell)
	elseif self.show_index == TianShenView.TabIndex.Activation then
		self:FlushActivationXingXiangRenderClick(cell)
	elseif self.show_index == TianShenView.TabIndex.ShenShi then
		self:FlushXingXiangRenderClick(cell)
	elseif self.show_index == TianShenView.TabIndex.LingHeUplevel then
		-- self:OnSelectTianShenCallBack(cell)
	end
end

--------------------------列表render-------------------------------------------
TianshenActivationItem = TianshenActivationItem or BaseClass(BaseRender)
function TianshenActivationItem:SetShowType(show_type)
	self.show_type = show_type
end

function TianshenActivationItem:__delete()
	self.show_type = nil
end

function TianshenActivationItem:OnFlush()
	if self.data == nil then
		return
	end

	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.index)
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.index)
	local is_activate = TianShenWGData.Instance:IsActivation(self.data.index)
	if (not tianshen_cfg) or (not tianshen_info) then
		return
	end

	self.node_list.tianshen_star:CustomSetActive(is_activate)
	self.node_list.tianshen_lv_root:CustomSetActive(is_activate)
	self.node_list.not_active:CustomSetActive(not is_activate)
	self.node_list.battle_flag:CustomSetActive(tianshen_info.zhan_index >= 0)--(self.data.battle_status ~= -1)

	local color = self.data.series + 2
	self.node_list.tianshen_name.text.text = ToColorStr(tianshen_cfg.bianshen_name, ITEM_COLOR[color])
	self.node_list.tianshen_name_hl.text.text = tianshen_cfg.bianshen_name
	self.node_list.tianshen_lv.text.text = tianshen_info.level
	local shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.data.index)

	local bundle, asset = ResPath.GetTianShenNopackImg("ts_ring_" .. self.data.index)
    self.node_list.tianshen_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.tianshen_skill_icon.image:SetNativeSize()
    end)

	local bundle, asset = ResPath.GetF2TianShenImage(string.format("a3_ts_small_yq0%d", color))
	self.node_list.img_bg.image:LoadSprite(bundle, asset)

	local star_res_list = GetStarImgResByStar(tianshen_info.star)
	for i = 1, 5 do
		self.node_list[string.format("star%d", i)].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
	end

	local red = false
	if self.show_type == TianShenView.TabIndex.Activation then
		red = TianShenWGData.Instance:SpecialTianshenCanActive(self.data.index) 
		or TianShenWGData.Instance:SpecialTianshenCanUpStar(self.data.index) 
		or TianShenWGData.Instance:SpecialTianshenCanUpGrade(self.data.index)
	elseif self.show_type == TianShenView.TabIndex.ShenShi then
		local has_red, compose_red = TianShenWGData.Instance:CheckEquipShenShiRed(self.data.index)
		local succinct_red = TianShenWGData.Instance:GetTianShenShenShiRefineRed(self.data.index)
		red = has_red > 0 or compose_red == 1 or succinct_red
	elseif self.show_type == TianShenView.TabIndex.TSInfo then
		local is_open_huamo_func = FunOpen.Instance:GetFunIsOpened(FunName.TianShenHuamoView)
		local is_open_shuangsheng_func = FunOpen.Instance:GetFunIsOpened(FunName.TianShenShuangShengView)
		-- 天神入魔
		local is_huamo_remind = TianShenHuamoWGData.Instance:GetSubRemind(self.data.index) == 1

		--天神双生
		local is_shuangsheng_remind = TianShenShuangShengWGData.Instance:GetOneTianShenAvatarRemind(self.data.index)
		local is_active = TianShenWGData.Instance:GetTianshenInfoStatus(self.data.index) == 1
		red = (is_open_huamo_func and is_huamo_remind) or (is_open_shuangsheng_func and is_shuangsheng_remind and is_active)
	end

	self.node_list.remind:CustomSetActive(red)
end


