UltimateBattleAnswerResultView = UltimateBattleAnswerResultView or BaseClass(SafeBaseView)

local TIME_DOWN = 4

function UltimateBattleAnswerResultView:__init()
	self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_talent_answer_result")
	self:SetMaskBg(true, true)
end

function UltimateBattleAnswerResultView:LoadCallBack()
end

function UltimateBattleAnswerResultView:ReleaseCallBack()
    self:CleanTimeDown()
    self.right_number = nil
end

function UltimateBattleAnswerResultView:OpenCallBack()
	
end

function UltimateBattleAnswerResultView:CloseCallBack()
    -- 打开天赋选择
    UltimateBattlefieldWGCtrl.Instance:OpenTalentSelectView()
    local show_data = {}

    -- 刷新倒计时
	local act = ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE or nil
	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)
	local end_time = act_status.end_time or 0
    show_data.end_time = end_time
    show_data.end_str = Language.UltimateBattlefield.WaitSelectTalentTime
    show_data.show_type = CROSS_1VN_TIME_DOWN_TYPE.CROSS_1VN_TIME_DOWN_TALENT
    UltimateBattlefieldWGCtrl.Instance:OpenFollowView(show_data)
end

function UltimateBattleAnswerResultView:SetData(right_number)
    self.right_number = right_number
end

function UltimateBattleAnswerResultView:OnFlush()
    if self.right_number == nil then
        return
    end

    local right_number = self.right_number or 0
    local ratio_number = 0
    local refresh_times = 0

    local cfg = UltimateBattlefieldWGData.Instance:GetTalentRefreshCfgBySeq(right_number)
    if cfg then
        ratio_number = math.random() * (cfg.max_pro - cfg.min_pro) + cfg.min_pro
        refresh_times = cfg.refresh_times or 0
    end

    local right_number_str = ToColorStr(right_number, COLOR3B.GREEN)
    local ratio_number_str = ToColorStr(string.format("%.2f%%", ratio_number), COLOR3B.GREEN)
    local refresh_times_str = ToColorStr(string.format("x%s", refresh_times), COLOR3B.GREEN)
    self.node_list.result_txt.text.text = string.format(Language.UltimateBattlefield.AnswerResultDesc, right_number_str, ratio_number_str, refresh_times_str)

    -- 触发选择天赋
    self:FlushTimeCountDownEnter()
end


-----------------活动时间倒计时-------------------
function UltimateBattleAnswerResultView:FlushTimeCountDownEnter()
    -- 这里减1秒是防止误差，这边关了界面和协议打开在同一秒
    self:FlushTimeCountDown(TIME_DOWN)
end

function UltimateBattleAnswerResultView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("ultimate_battle_answer_result_view_down") then
		CountDownManager.Instance:RemoveCountDown("ultimate_battle_answer_result_view_down")
	end
end

function UltimateBattleAnswerResultView:FlushTimeCountDown(end_time)
    self:CleanTimeDown()

    self.node_list.close_txt.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, end_time)
    CountDownManager.Instance:AddCountDown("ultimate_battle_answer_result_view_down",
    BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), nil, end_time , 1)
end

function UltimateBattleAnswerResultView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.close_txt.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, valid_time)
	end
end

function UltimateBattleAnswerResultView:OnComplete()
    self.node_list.close_txt.text.text = ""
	self:Close()
end

