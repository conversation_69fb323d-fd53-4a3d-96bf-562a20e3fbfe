﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class ApplicationBrightness
{

    /// <summary>
    /// Brightness的有效范围是0~1，-1。 若设置为-1则跟随系统亮度。本段代码没有设置有效范围检测。
    /// </summary>
    public static void SetApplicationBrightnessTo(float Brightness)
    {
        AndroidJavaObject Activity = null;
        Activity = new AndroidJavaClass("com.unity3d.player.UnityPlayer").GetStatic<AndroidJavaObject>("currentActivity");
        Activity.Call("runOnUiThread", new AndroidJavaRunnable(() =>
        {
            AndroidJavaObject Window = null, Attributes = null;
            Window = Activity.Call<AndroidJavaObject>("getWindow");
            Attributes = Window.Call<AndroidJavaObject>("getAttributes");
            Attributes.Set("screenBrightness", Brightness);
            Window.Call("setAttributes", Attributes);
        }));
    }


    /// <summary>
    /// 获取安卓机亮度
    /// </summary>
    public static float GetApplicationBrightness()
    {
        AndroidJavaObject Activity = null;
        Activity = new AndroidJavaClass("com.unity3d.player.UnityPlayer").GetStatic<AndroidJavaObject>("currentActivity");

        AndroidJavaObject Window = null, Attributes = null;
        Window = Activity.Call<AndroidJavaObject>("getWindow");
        Attributes = Window.Call<AndroidJavaObject>("getAttributes");
        return Attributes.Get<float>("screenBrightness");
    }
}
