YuanGuXianDianFBSceneLogic = YuanGuXianDianFBSceneLogic or BaseClass(CommonFbLogic)

function YuanGuXianDianFBSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
end

function YuanGuXianDianFBSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	if nil ~= self.story then
		self.story:DeleteMe()
		self.story = nil
	end
	
	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end
end

function YuanGuXianDianFBSceneLogic:Enter(old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.HIGH_TEAM_EQUIP)
	local ctrl = MainuiWGCtrl.Instance
	ctrl:AddInitCallBack(nil, function()
		FuBenWGCtrl.Instance:OpenHtePanel()
	end)
	
	local scene_id = Scene.Instance:GetSceneId()
    self.story = XinShouStorys.New(scene_id)
    if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function YuanGuXianDianFBSceneLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end

function YuanGuXianDianFBSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)
	local ctrl = MainuiWGCtrl.Instance
	ctrl:AddInitCallBack(nil, function()
		FuBenWGCtrl.Instance:CloseHtePanel()
	end)
    FuBenPanelWGCtrl.Instance:CloseStarAniView()
    FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_equip_high)
    if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end

function YuanGuXianDianFBSceneLogic:InitCallBack()
	MainuiWGCtrl.Instance:SetFirstEnterFb(true)
end

function YuanGuXianDianFBSceneLogic:OpenFbSceneCd()
end

function YuanGuXianDianFBSceneLogic:CanAutoRotation()
	return false
end

-- 获取挂机打怪的敌人
function YuanGuXianDianFBSceneLogic:GetGuajiCharacter()
	return self:SelectObjHelper(Scene.Instance:GetMonsterList())
end

function YuanGuXianDianFBSceneLogic:SelectObjHelper(obj_list)
	local target_obj = nil
	local target_distance = 100000
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return target_obj, target_distance
	end

	local m_pox_x , m_pos_y = main_role:GetLogicPos()
	local g_pos_x, g_pos_y = m_pox_x , m_pos_y
	if self.goddess_obj then
		g_pos_x, g_pos_y = self.goddess_obj:GetLogicPos()
	end
	--找到距离goddess最近的
	for _, v in pairs(obj_list) do
		if v:IsCharacter() or v:GetModel():IsVisible() then
			local can_select = Scene.Instance:IsEnemy(v, main_role)
			local target_x, target_y = v:GetLogicPos()

			if can_select then
				local distance = GameMath.GetDistance(g_pos_x, g_pos_y, target_x, target_y, false)
				if distance < target_distance then
					if v:IsInBlock() then
						if nil == target_obj then
							target_obj = v
						end
					else
						target_obj = v
						target_distance = distance
					end
				end
			end
		end
	end

	return target_obj, target_distance
end


function YuanGuXianDianFBSceneLogic:IsRoleEnemy(target_obj, main_role)
	local target_vo = target_obj:GetVo() or {}
	if target_vo.is_shadow == 1 then
		return false
	end

	return BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
end