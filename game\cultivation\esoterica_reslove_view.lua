EsotericaResloveView = EsotericaResloveView or BaseClass(SafeBaseView)
function EsotericaResloveView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1078, 590)})
	self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_esoterica_reslove_view")
end

function EsotericaResloveView:SetDataAndOpen(slot)
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function EsotericaResloveView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Esoterica.ResloveName

    if not self.item_grid then
        self.item_grid = EsotericaMeltingGrid.New()
        self.item_grid:SetStartZeroIndex(false)
        self.item_grid:SetIsMultiSelect(true)
        self.item_grid:CreateCells({
            col = 11,
            cell_count = 44,
            list_view = self.node_list["item_grid"],
            itemRender = MeltEquipCell,
            change_cells_num = 2,
        })
        self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(ESOTERICA_DEFINE.EXP_ITEM_ID)
    if item_cfg then
        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
        self.node_list["exp_icon"].image:LoadSprite(bundle, asset, function()
            self.node_list["exp_icon"].image:SetNativeSize()
        end)
    end
    
    self.color_select_list = {}
    for i = 1, 6 do
        XUI.AddClickEventListener(self.node_list["btn_color_" .. i], BindTool.Bind(self.OnSelectColor, self, i))
        self.node_list["btn_color_toggle" .. i]:SetActive(true)
        self.color_select_list[i] = true
    end

    XUI.AddClickEventListener(self.node_list["btn_reslove"], BindTool.Bind(self.OnClickReslove, self))
    XUI.AddClickEventListener(self.node_list["btn_note"], BindTool.Bind(self.OnClickNote, self))
end

function EsotericaResloveView:ReleaseCallBack()
    self.color_select_list = {}
    if self.item_grid then
        self.item_grid:DeleteMe()
        self.item_grid = nil
    end
end

function EsotericaResloveView:OnSelectColor(color)
    if color then
        local cur_state = not self.color_select_list[color]
        self.color_select_list[color] = cur_state
        self.node_list["btn_color_toggle" .. color]:SetActive(cur_state)
    end

    if self.item_grid then
        self.item_grid:SetColorSelcet(self.color_select_list)
    end

    self:FlushView()
end

function EsotericaResloveView:OnBagSelectItemCB(cell)
    self:FlushView()
end

function EsotericaResloveView:OnFlush()
    local item_list = CultivationWGData.Instance:GetEsotericaResloveBagList()
    self.item_grid:SetDataList(item_list)
    self:OnSelectColor()
end

function EsotericaResloveView:FlushView()
	local select_list = self.item_grid:GetAllSelectCell()
    local es_data = CultivationWGData.Instance
    
	local add_exp = 0
	for k,v in pairs(select_list) do
        local cfg = es_data:GetEsotericaPartItemCfg(v.item_id)
        if cfg then
            add_exp = add_exp + cfg.exp * v.num
        end
	end

    self.node_list.exp_value.text.text = add_exp
end

function EsotericaResloveView:OnClickReslove()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Esoterica.ResloveError)
		return
	end

	local reslove_list = {}
	for k,v in pairs(select_list) do
		reslove_list[#reslove_list + 1] = v
	end

    self.item_grid:CancleAllSelectCell()
	CultivationWGCtrl.Instance:OnEsotericaEquipDecompos(reslove_list)
end

function EsotericaResloveView:OnClickNote()
	RuleTip.Instance:SetTitle(Language.Esoterica.ResloveTipsTitle)
	RuleTip.Instance:SetContent(Language.Esoterica.ResloveTipsContent, nil, nil, nil, true)
end












---------------------------------------------------------
-- EsotericaMeltingGrid
---------------------------------------------------------
EsotericaMeltingGrid = EsotericaMeltingGrid or BaseClass(AsyncBaseGrid)
function EsotericaMeltingGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= ESOTERICA_DEFINE.MAX_RESLOVE then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenLingHe.ResloveLimit)
            return true
        end
    end

    return false
end

function EsotericaMeltingGrid:SetColorSelcet(select_color_list)
    if IsEmptyTable(select_color_list) then
        return
    end

    self.select_tab[1] = {}
    self.cur_multi_select_num = 0
    local data = self.cell_data_list
    for i = 1, self.has_data_max_index do
        if data[i] and select_color_list[data[i].color] then
            if self.cur_multi_select_num < ESOTERICA_DEFINE.MAX_RESLOVE then
                self.cur_multi_select_num = self.cur_multi_select_num + 1
                self.select_tab[1][i] = true
            else
                break
            end
        end
    end

    self:__DoRefreshSelectState()
end