-- 类型1：激活N套沧溟
-- 类型2：某沧溟所有部位达到N星
FiveElementsWGData.Qiantianlu_Type = {
	AllActive = 1,--1, 沧溟套装收集任务， num:套装数
	PartLevel = 2,--2, 沧溟套装星数任务， type:沧溟id star:星级要求
}

FiveElementsWGData.Qiantianlu_Attr_Num = 4

----五行沧溟拓展 乾天录
function FiveElementsWGData:InitQianTianLuData()
	local cfg = self.waistlight_cfg_auto
	if cfg then
		self.qiantianlu_cfg = ListToMap(cfg.waist_collect, "id")
	end
end

--协议数据
function FiveElementsWGData:SetQianTianLuActiveList(protocol)
	---print_error("SetQianTianLuActiveList", protocol.active_list)
	self.qiantianlu_act_list = protocol.active_list
	self:SetQianTianDataList()
end

--单个协议数据
function FiveElementsWGData:SetQianTianLuItemActive(protocol)
	--print_error("SetQianTianLuItemActive", protocol)
	if self.qiantianlu_act_list and self.qiantianlu_act_list[protocol.task_id] then
		self.qiantianlu_act_list[protocol.task_id] = 1
		self:SetQianTianDataList()
	end
end

function FiveElementsWGData:SetQianTianDataList()
	self.qiantianlu_data_list = {}
	local cfg = self.qiantianlu_cfg
	if IsEmptyTable(cfg) or IsEmptyTable(self.qiantianlu_act_list) then
		return
	end

	local info
	for id, v in pairs(cfg) do
		info = {}
		info.cfg = v
		info.total_num, info.need_num = self:GetQianTianLuTaskData(v)
		info.id = v.id
		info.active_state = self:GetQianTianLuActiveStateById(v.id)
		info.can_active_sort_key = info.total_num >= info.need_num and 0 or 1
		table.insert(self.qiantianlu_data_list, info)
	end
	
	table.sort(self.qiantianlu_data_list, SortTools.KeyLowerSorters("active_state", "can_active_sort_key", "id"))
end

--获取此乾天录是否已激活
function FiveElementsWGData:GetQianTianLuActiveStateById(id)
	--print_error(id, self.qiantianlu_act_list[id], self.qiantianlu_act_list)
	return (self.qiantianlu_act_list or {})[id] or 0
end

--获取乾天录成就配置列表
function FiveElementsWGData:GetQianTianCfgList()
	return self.qiantianlu_cfg
end

function FiveElementsWGData:GetQianTianCfgById(id)
	return (self.qiantianlu_cfg or {})[id]
end

--获取乾天录展示数据列表
function FiveElementsWGData:GetQianTianDataList()
	if IsEmptyTable(self.qiantianlu_data_list) then
		self:SetQianTianDataList()
	end

	return self.qiantianlu_data_list
end

function FiveElementsWGData:GetQianTianLuTaskData(qiantianlu_cfg)
	local total_num, need_num, cangming_level = 0, 0, 0
	local is_all_open = false
	--type == 1, 沧溟套装收集任务， num:套装数
	--type == 2, 沧溟套装星数任务， type:沧溟id star:星级要求
	if qiantianlu_cfg.achievement_type == FiveElementsWGData.Qiantianlu_Type.AllActive then
		--计算已激活沧溟套装数量
		for i = 1, #self.waist_light_type_cfg do
			local item = self.waist_light_type_cfg[i]
			cangming_level, is_all_open = self:GetWaistLightOpen(item.type)
			if self:GetWaistItemIsCanShow(item.type) and is_all_open then	
				total_num = total_num + 1
			end
		end
		need_num = qiantianlu_cfg.num--类型1：激活N套沧溟可激活

	elseif qiantianlu_cfg.achievement_type == FiveElementsWGData.Qiantianlu_Type.PartLevel then
		cangming_level, is_all_open = self:GetWaistLightOpen(qiantianlu_cfg.type)---- 这沧溟套装所有部位达到cangming_level
		total_num = cangming_level or 0
		need_num = qiantianlu_cfg.star
	end

	return total_num, need_num
end

function FiveElementsWGData:GetQiantianluAttrList(id)
	local cfg = self:GetQianTianCfgById(id)
	if IsEmptyTable(cfg) then
		return {}
	end

	local attr_list = {}
	local attr_id, attr_value, attr_str, info
	for i = 1, FiveElementsWGData.Qiantianlu_Attr_Num do
		attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
		if attr_id and attr_id > 0 then
			attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			info = {}
			info.attr_id = attr_id
			info.attr_value = attr_value
			info.attr_str = attr_str
			table.insert(attr_list, info)
		end
	end
	return attr_list
end