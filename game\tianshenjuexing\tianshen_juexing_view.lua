--天神觉醒--神兵觉醒
TianShenJuexingView = TianShenJuexingView or BaseClass(SafeBaseView)

function TianShenJuexingView:__init()
    self.view_layer = UiLayer.Normal
    self.full_screen = true
    self.is_safe_area_adapter = true

    self:SetMaskBg(true, false)
    self:AddViewResource(0, "uis/view/tianshenjuexing_ui_prefab", "layout_tianshen_juexing")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function TianShenJuexingView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("tianshen_awaken") then
		CountDownManager.Instance:RemoveCountDown("tianshen_awaken")
	end

    if self.ph_task_list then
        self.ph_task_list:DeleteMe()
        self.ph_task_list = nil
    end

    if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
    end

    -- if self.get_reward_list then
	-- 	self.get_reward_list:DeleteMe()
	-- 	self.get_reward_list = nil
	-- end
end

function TianShenJuexingView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ShenBingAwaken.TitleName

    if not self.ph_task_list then
        self.ph_task_list = AsyncListView.New(TianShenJueXingItemRenderer, self.node_list.ph_task_grid)
        self.ph_task_list:SetStartZeroIndex(false)
    end

    if self.model_display == nil then
		self.model_display = OperationActRender.New(self.node_list["model_display"])
	end

    local display_data = {}
    local item_id = TianShenJuexingWGData.Instance.right_reward_item
    if item_id ~= 0 then
    	display_data.item_id = item_id
    	display_data.should_ani = true
		display_data.render_type = 0
		self.model_display:SetData(display_data)
    end

    -- if not self.get_reward_list then
	-- 	self.get_reward_list = AsyncListView.New(ItemCell, self.node_list["ts_juexing_reward_list"])
	-- 	self.get_reward_list:SetStartZeroIndex(true)
	-- end

    self.node_list.jieyuan_right.tmp.text = Language.ShenBingAwaken.JieYuanDesc
    self.node_list.btn_active.button:AddClickListener(BindTool.Bind(self.ActiveClick, self))
    --self.node_list.btn_go_jieyuan.button:AddClickListener(BindTool.Bind(self.GoToJieYuan, self))
    self.node_list.btn_skill.button:AddClickListener(BindTool.Bind(self.ClickSkill, self))
    --self.node_list.juexing_buy_btn.button:AddClickListener(BindTool.Bind(self.ClickJuexingBuy, self))
    self:FlushSkill()
    --self:FlushReward()
end

function TianShenJuexingView:GoToJieYuan()
    FunOpen.Instance:OpenViewNameByCfg(TianShenJuexingWGData.Instance:TianshenTequan())
end

function TianShenJuexingView:ClickSkill()
    local type = 1
    local skill_id = TianShenJuexingWGData.Instance.show_skill_id
    local cfg = NewAppearanceWGData.Instance:GetBaseQiChongSkillCfg(type, skill_id, 1)
    local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(type, cfg.active_star_level)
    local limit_text = string.format(Language.NewAppearance.SkillGradeActTips2, upstar_cfg.grade_num, upstar_cfg.star_num)
    local capability = NewAppearanceWGData.Instance:GetSingleSkillCap(cfg)
    local info = NewAppearanceWGData.Instance:GetQiChongInfo(type) or {}
    local is_act = (info.star_level or 0) >= (cfg.active_star_level or 0)
    local show_data = {
        skill_box_type = SKILL_BOX_TYPE.QI_CHONG_SKILL,
        type = type,
        skill_id = skill_id,
		icon = cfg.skill_icon,
		top_text = cfg.skill_name,
		body_text = cfg.skill_describe,
		limit_text = limit_text,
        set_pos2 = true,
        is_up_operate = true,
        -- hide_level = self.data.qc_type ~= nil,
        is_lock = not is_act,
		--x = -236,
		--y = 0,
		capability = capability,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    --CommonSkillShowCtrl.Instance:SetShenBingSkillWinViewDataAndOpen(TianShenJuexingWGData.Instance.right_reward_item)
end

function TianShenJuexingView:ClickJuexingBuy()
    local other_cfg = TianShenJuexingWGData.Instance:GetOtherCfg()
    RechargeWGCtrl.Instance:Recharge(other_cfg.price, other_cfg.rmb_type, other_cfg.rmb_seq)
end

function TianShenJuexingView:ActiveClick()
    local is_show_active = TianShenJuexingWGData.Instance:GetIsCanActive()
    if is_show_active then
        local is_act = TianShenJuexingWGData.Instance:GetTianShenAwakenIsActive()
        if is_act then
            return
        end

        TianShenJuexingWGCtrl.Instance:SetIsFunopen(true)
        TianShenJuexingWGCtrl.Instance:SendAwakenOper(TianShenJuexingWGData.AWAKE_OPER_TYPE.TIANSHEN_AWAKEN)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenBingAwaken.CannotActive)
    end
end

function TianShenJuexingView:FlushSkill()
    local skill_id = TianShenJuexingWGData.Instance.show_skill_id
    local skill_cfg = NewAppearanceWGData.Instance:GetBaseQiChongSkillCfg(1, skill_id, 1)
    if not skill_cfg then
        self.node_list.btn_skill:SetActive(false)
        return
    end

    self.node_list.skill_name.tmp.text = Language.ShenBingAwaken.SkillStr1 .. skill_cfg.skill_name
    local bundle, asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
    self.node_list.skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.skill_icon.image:SetNativeSize()
    end)

    self.node_list.skill_desc.tmp.text = skill_cfg.skill_describe
    self.node_list.btn_skill:SetActive(true)
end

function TianShenJuexingView:FlushReward()
    local other_cfg = TianShenJuexingWGData.Instance:GetOtherCfg()
    local reward_data = other_cfg.reward_item
    self.get_reward_list:SetDataList(reward_data)

    local need_price = RoleWGData.GetPayMoneyStr(other_cfg.price)
    self.node_list.juexing_buy_text.text.text = string.format(Language.ShenBingAwaken.BuyBtnText, other_cfg.price * COMMON_CONSTS.RECHARGE_PAY_BILI)

    self.node_list.real_pay_txt.tmp.text = RoleWGData.GetPayMoneyStr(other_cfg.price, other_cfg.rmb_type, other_cfg.rmb_seq)
end

function TianShenJuexingView:OnFlush()
    local data_list = TianShenJuexingWGData.Instance:GetTaskListInfo()
    self.ph_task_list:SetDataList(data_list)
    for k,v in pairs(data_list) do
        local is_act = v.is_complete and v.is_get
        self.node_list["sp_" .. v.task_id]:SetActive(not is_act)
    end

    local is_show_active = TianShenJuexingWGData.Instance:GetIsCanActive()
    local is_act = TianShenJuexingWGData.Instance:GetTianShenAwakenIsActive()
    self.node_list.suipian:SetActive(not is_show_active)
    self.node_list.btn_active:SetActive(is_show_active and not is_act)

    CountDownManager.Instance:RemoveCountDown("tianshen_awaken")
    local info = TianShenJuexingWGData.Instance:GetAwakenInfo()
    if info then
        local last_time = info.close_time - TimeWGCtrl.Instance:GetServerTime()
        if last_time > 0 then
            self:FlushCountDown(info.close_time)
        else
            self:CompleteCountDownTime()
        end
    end

    self.node_list.juexing_buy_flag:SetActive(info and info.shenwu_rmb_buy_flag == 1 or false)
    --self.node_list.juexing_buy_btn:SetActive(info and info.shenwu_rmb_buy_flag == 0 or false)
end

function TianShenJuexingView:FlushCountDown(last_time)
    local srver_time = TimeWGCtrl.Instance:GetServerTime()
    self:UpdateCountDownTime(last_time - srver_time)

	CountDownManager.Instance:AddCountDown("tianshen_awaken",
        function (elapse_time, total_time)
            local last_time = math.floor(total_time - elapse_time)
            self:UpdateCountDownTime(last_time)
        end,
		BindTool.Bind(self.CompleteCountDownTime, self),
		last_time, nil, 0.5)
end

function TianShenJuexingView:UpdateCountDownTime(last_time)
	if not self.node_list["rest_time"] then
		return
	end
    
    local str_time = TimeUtil.FormatSecondDHM6(last_time)
	self.node_list["rest_time"].tmp.text = str_time
end

function TianShenJuexingView:CompleteCountDownTime()
	if not self.node_list["rest_time"] then
		return
	end

    self.node_list["rest_time"].tmp.text = Language.ShenBingAwaken.CountDown
end




---------------------------------------任务ItemRenderer-------------------------------------------
TianShenJueXingItemRenderer = TianShenJueXingItemRenderer or BaseClass(BaseRender)
function TianShenJueXingItemRenderer:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGet, self))
    XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGo, self))

    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_parent)
    end
end

function TianShenJueXingItemRenderer:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end


function TianShenJueXingItemRenderer:OnFlush()
    local data = self.data
    local param = 0
    if type(data.param1) == "string" then
        local tb = Split(data.param1, "|")
        local str_tab = {}
        local flag = bit:d2b_two(data.task_process)
        local str_boss = ""
        local red_point = "/"
        for i = 1, #tb do
            local color = (flag[i - 1] == 1 or data.is_complete) and COLOR3B.GREEN or COLOR3B.RED
            str_tab[i] = tb[i]
            local desc = string.format("{shenbingawaken_boss;%s;%s;%s}", str_tab[i], self.index, i)
            if i == 1 then
                str_boss = ToColorStr(desc, color)
            else
                str_boss = str_boss .. red_point .. ToColorStr(desc, color)
            end
        end

        local str = string.format(data.task_description, str_boss)
        EmojiTextUtil.ParseRichText(self.node_list.condition_text.emoji_text, str, 20, "#FFFDD9")

    else
        local str = data.task_description
        local cur_param = data.task_process
        param = data.param1
        if data.is_complete then
            cur_param = param
        end
        param = data.param1
        local color = cur_param >= param and COLOR3B.GREEN or COLOR3B.RED

        -- 仙修特殊处理
        if data.task_id == 6 then
            local cur_title = CultivationWGData.Instance:GetXiuweiStageTitleByState()
            local target_title = CultivationWGData.Instance:GetXiuweiStageTitleByState(param)
            EmojiTextUtil.ParseRichText(self.node_list.condition_text.emoji_text, string.format(str, ToColorStr(cur_title .. "/" .. target_title, color)), 20, "#FFFDD9")

        else
            EmojiTextUtil.ParseRichText(self.node_list.condition_text.emoji_text, string.format(str, ToColorStr(cur_param .. "/" .. param, color)), 20, "#FFFDD9")
        end
        
    end

    self.node_list.attr_text.text.text = self.data.award_attr
    self.node_list.btn_go:SetActive(not data.is_complete and not data.is_get and data.goto_module ~= "")
    self.node_list.btn_get:SetActive(data.is_complete and not data.is_get)
    self.node_list.had_get:SetActive(data.is_complete and data.is_get)

    self.item_cell:SetData(data.reward_item[0])
    -- local bundle = "uis/view/tianshenjuexing_ui/images_atlas"
    -- local asset = "sp_" .. data.task_id
    -- self.node_list.item_icon.image:LoadSprite(bundle, asset, function()
    --     self.node_list.item_icon.image:SetNativeSize()
    -- end)
end

function TianShenJueXingItemRenderer:OnClickGet()
    if self.data.is_complete and not self.data.is_get then
        TianShenJuexingWGCtrl.Instance:SendAwakenOper(TianShenJuexingWGData.AWAKE_OPER_TYPE.AWAKE_OPER_TYPE_FETCH_REWARD, self.data.task_id)
    end
end

function TianShenJueXingItemRenderer:OnClickGo()
    if self.data.is_complete then
        return
    end

    if self.data.goto_module then
        local tb = Split(self.data.goto_module, "#")
        if tb[3] then
            local layer, index = 1, 1
            if tonumber(tb[3]) and tonumber(tb[4]) then
                layer = tonumber(tb[3])
                index = tonumber(tb[4])
            end

            BossWGData.Instance:SetBossTuJianIndex(tonumber(TabIndex[tb[2]]), layer, index)
            ViewManager.Instance:Open(tb[1], tb[2])
        else
            FunOpen.Instance:OpenViewNameByCfg(self.data.goto_module)
        end
    end
end