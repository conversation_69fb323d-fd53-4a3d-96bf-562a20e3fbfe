TianShenJiangLinLogic = TianShenJiangLinLogic or BaseClass(CommonFbLogic)
function TianShenJiangLinLogic:__init()
	self.save_boss_posx = 0
	self.save_boss_posy = 0
end

function TianShenJiangLinLogic:__delete()
	self.save_boss_posx = 0
	self.save_boss_posy = 0
end

function TianShenJiangLinLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()

	local boss_id, cfg_data = TianshenRoadWGData.Instance:GetBossId()
	if cfg_data then
		self.save_boss_posx = cfg_data.boss_posx
		self.save_boss_posy = cfg_data.boss_posy
		-- local move_to_pos_call_back = function ()
		-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		-- end
		-- GuajiWGCtrl.Instance:SetMoveToPosCallBack(move_to_pos_call_back)
		local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
		if not guaji_state then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
		-- MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		-- GuajiCache.monster_id = boss_id
		-- MoveCache.param1 = boss_id
		-- local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
		-- GuajiWGCtrl.Instance:MoveToPos(cfg_data.scene_id, cfg_data.boss_posx, cfg_data.boss_posy, range, 0, 1)
	end
	
	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.TIANSHENJIANLIN, function()
		MainuiWGCtrl.Instance:SetButtonModeClick(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.TianShenRoad.JianLinStr8)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		TianshenRoadWGCtrl.Instance:OpenTSJLFbView()
		local activity_info = TianshenRoadWGData.Instance:GetTianShenJianLinInfo()
		if activity_info then
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time, false, GameEnum.FU_BEN_OUT_SCENE, true)
		end
	end)
end

function TianShenJiangLinLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)
    MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetButtonModeClick(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	FuBenPanelCountDown.Instance:CloseViewHandler()
end

function TianShenJiangLinLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function TianShenJiangLinLogic:GetGuajiPos()
	return self.save_boss_posx, self.save_boss_posy
end