-- 通天降临-携手同行

local MEMBER_MAX_COUNT = 3

function WorldTreasureView:TogetherReleaseCallBack()
    if self.together_task_list then
        self.together_task_list:DeleteMe()
        self.together_task_list = nil
    end

    if self.player_item_list then
		for k, v in pairs(self.player_item_list) do
			v:DeleteMe()
		end
		self.player_item_list = nil
	end
end

function WorldTreasureView:TogetherLoadCallBack()
    if not self.together_task_list then
        self.together_task_list = AsyncListView.New(TogetherTaskItem, self.node_list["together_task_list_view"])
    end

    if not self.player_item_list then
		self.player_item_list = {}
		for i = 1, MEMBER_MAX_COUNT do
			self.player_item_list[i] = TogetherPlayerItem.New(self.node_list["player_item_" .. i])
		end
	end

    self.node_list["to_txt_rule_tips"].text.text = Language.WorldTreasure.TogetherRuleTips

    XUI.AddClickEventListener(self.node_list["btn_goto_draw"], BindTool.Bind1(self.OnClickGoToDraw, self))
end

function WorldTreasureView:TogetherShowIndexCallBack()
    self:DoTogetherCellsAnim()
end

function WorldTreasureView:TogetherOnFlush(param_t)
    local together_task_list = WorldTreasureWGData.Instance:GetTogetherTaskDataList()
    self.together_task_list:SetDataList(together_task_list)

    local member_data_list = WorldTreasureWGData.Instance:GetTogetherMemberList()
    for i = 1, MEMBER_MAX_COUNT do
        self.player_item_list[i]:SetData(member_data_list[i])
    end
end

function WorldTreasureView:OnClickGoToDraw()
    ViewManager.Instance:Open(GuideModuleName.ControlBeastsPrizeDrawWGView, nil, "oa_act_draw_type_item")
end

function WorldTreasureView:DoTogetherCellsAnim()
	local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender
	self.node_list["together_task_list_view"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["together_task_list_view"]:SetActive(true)
        local list =  self.together_task_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyItemAnim(count)
        end
    end, tween_info.DelayDoTime, "together_task_cell_tween")
end

----------------------------
-- 携手同行-任务Item
----------------------------
TogetherTaskItem = TogetherTaskItem or BaseClass(BaseRender)

function TogetherTaskItem:LoadCallBack()
    if not self.reward_item_list then
        self.reward_item_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
        self.reward_item_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind1(self.OnClickGetRewardBtn, self))
end

function TogetherTaskItem:ReleaseCallBack()
    if self.reward_item_list then
        self.reward_item_list:DeleteMe()
        self.reward_item_list = nil
    end
end

function TogetherTaskItem:OnFlush()
    if not self.data then
        return
    end

    local consume_num = WorldTreasureWGData.Instance:GetTogetherUseItemCount()
    local target_num = tonumber(self.data.cfg.target)
    self.node_list["task_condition"].text.text = string.format(Language.WorldTreasure.TogetherTask, target_num)
    self.node_list["task_slider"].slider.value = consume_num / target_num
    local is_complete = consume_num >= target_num
    local progress_str = is_complete and Language.WorldTreasure.TogetherTaskComplete or consume_num .. "/" .. target_num
    self.node_list["task_condition_progress"].text.text = progress_str
    local fill_img_name = is_complete and "a3_ttjl_xstx_jd_di03" or "a3_ttjl_xstx_jd_di02"
    local bundle, asset = ResPath.GetWorldTreasureImg(fill_img_name)
    self.node_list["fill_imge"].image:LoadSprite(bundle, asset)

    local can_get_reward = is_complete and self.data.reward_flag == 0
    self.node_list["btn_receive"]:SetActive(can_get_reward)
    self.node_list["effect_lingqu"]:SetActive(can_get_reward)
    
    -- local item_data_list = {}
    -- for k, v in pairs(self.data.cfg.reward_item) do
    --     local data = {}
    --     data.item_id = v.item_id
    --     data.num = v.num
    --     data.is_bind = v.is_bind
    --     data.is_receive = self.data.reward_flag ~= 0
    --     table.insert(item_data_list, data)
    -- end
    self.reward_item_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(self.data.reward_flag ~= 0)
            --item_cell:SetRedPointEff(can_get_reward)
        end
    end)
    self.reward_item_list:SetDataList(self.data.cfg.reward_item)
end

function TogetherTaskItem:OnClickGetRewardBtn()
    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WALK_TOGETHER_REWARD, self.data.seq)
end

function TogetherTaskItem:PalyItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.WorldTreasureView, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "together_task_item_" .. wait_index)
end


----------------------------------
-- 携手同行-玩家头像Item
----------------------------------
TogetherPlayerItem = TogetherPlayerItem or BaseClass(BaseRender)

function TogetherPlayerItem:LoadCallBack()
	if not self.role_head then
		self.role_head = BaseHeadCell.New(self.node_list.player_node)
		self.role_head:SetBgActive(false)
	end

	if not self.role_avatar then
		self.role_avatar = RoleHeadCell.New(false)
		self.role_avatar:AddCustomMenu(Language.Menu.ShowInfo, Language.Menu.AddFriend)
	end

	XUI.AddClickEventListener(self.node_list.head_btn, BindTool.Bind(self.OnClickMainRoleHead, self))
end

function TogetherPlayerItem:ReleaseCallBack()
	if self.role_head then
		self.role_head:DeleteMe()
		self.role_head = nil
	end

	if self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end
end

function TogetherPlayerItem:OnFlush()
	if not self.data then
		return
	end

    local user_name = self.data.uid ~= 0 and self.data.user_name or ""
	self.node_list["name"].text.text = user_name

	self:SetHeadIcon()
end

function TogetherPlayerItem:SetHeadIcon()
	self.role_head:SetActive(self.data.uid ~= 0)
	--self.node_list.head_btn:SetActive(self.data.uid ~= 0)

	if self.data.uid ~= 0 then
		local data = {}
		data.role_id = self.data.uid
		data.prof = self.data.prof
		data.sex = self.data.sex
        data.fashion_photoframe = self.data.shizhuang_photoframe
		self.role_head:SetData(data)
		self.role_head:SetImgBg(true)
	end
end

function TogetherPlayerItem:OnClickMainRoleHead()
	if not self.data or self.data.uid == 0 then
        WorldTreasureWGCtrl.Instance:OpenTogetherInviteView()
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	--判断是否是自己.
	if self.data.uid == role_id then
		ViewManager.Instance:Open(GuideModuleName.RoleView, TabIndex.role_intro)
	else
		local role_info = {
			role_id =self.data.uid,
			role_name = self.data.name,
			prof = self.data.prof,
			sex = self.data.sex,
			is_online = true,
			-- server_id = self.data.usid.temp_low,
		}
		self.role_avatar:SetRoleInfo(role_info)
		self.role_avatar:OpenMenu(nil, nil, nil, MASK_BG_ALPHA_TYPE.Normal)
	end
end

function TogetherPlayerItem:OnClickInviteBtn()
    WorldTreasureWGCtrl.Instance:OpenTogetherInviteView()
end
