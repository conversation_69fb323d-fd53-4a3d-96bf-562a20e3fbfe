RoleEffectShieldHandle = RoleEffectShieldHandle or BaseClass(ReuseableShieldHandle)

function RoleEffectShieldHandle:__init(role)
	self.shield_obj_type = ShieldObjType.RoleEffect
	self.need_calculate_priortiy = true
	self:Init(role)
end

function RoleEffectShieldHandle:__delete()
	self:Clear()
end

function RoleEffectShieldHandle:Init(role)
	self.role = role
end

function RoleEffectShieldHandle:Clear()
	self.role = nil
end

function RoleEffectShieldHandle:VisibleChanged(visible)
	if self.role and not self.role:IsDeleted() then
		self.role:RoleEffectVisibleChanged(visible)
	end
end

function RoleEffectShieldHandle:CalculatePriortiy()
	if IsNil(MainCamera) or nil == self.role or self.role:IsDeleted() or not self.role:GetVisiable() then
		return SceneAppearPriority.Low
	end

	self.camera_position = Transform.GetPosition(MainCamera.transform, self.camera_position)
	local obj_pos = self.role:GetLuaPosition()
	local topos = u3dpool.v3Sub(obj_pos, self.camera_position)
	local distance = u3dpool.v3Length(topos, false)
	if distance <= 10 * 10 then
		return SceneAppearPriority.VeryHigh
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		distance = u3dpool.v2Length(u3dpool.v2Sub(main_role.logic_pos, self.role.logic_pos), false)
		if distance <= 10 * 10 then
			return SceneAppearPriority.High
		elseif distance <= 20 * 20 then
			return SceneAppearPriority.Middle
		end
	end

	return SceneAppearPriority.Low
end

----------------------------------------------SitEffectHandle------------------------------------------
SitEffectHandle = SitEffectHandle or BaseClass(ReuseableShieldHandle)
function SitEffectHandle:__init(role)
	self:Init(role)
	self.shield_obj_type = ShieldObjType.SitEffect
end

function SitEffectHandle:__delete()
	self:Clear()
end

function SitEffectHandle:Init(role)
	self.role = role
end

function SitEffectHandle:Clear()
	self.role = nil
end

function SitEffectHandle:VisibleChanged(visible)
	if self.role and not self.role:IsDeleted() then
		self.role:SetSitEffectVisible(visible)
	end
end