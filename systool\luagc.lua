LuaGC = LuaGC or {}

local next_sample_time = 0
local sample_time = 5
local smaple_mem_list = {}

local base_mem = 0
local is_sampling = false
local max_stepmul = 5000
local SysDirectory = System.IO.Directory

local need_check_lua_report = true

collectgarbage("setpause", 100)
-- 注意在第一个进度条里都不使用高频率GC，提高启动速度。
--内存会不会在前期就爆掉？？？ （现测到创角界面,lua内存200M左右)
function LuaGC:StartGC()
	if IS_CHECK_FUNTION_COST_MEM and CheckFuntionUseMem:IsCollecting() then
		return
	end

	need_check_lua_report = true
	collectgarbage("setpause", 100)
	collectgarbage("setstepmul", max_stepmul)
end

function LuaGC:StartSample()
	if IS_CHECK_FUNTION_COST_TIME then
		return
	end

	if IS_CHECK_FUNTION_COST_MEM and CheckFuntionUseMem:IsCollecting() then
		return
	end

	is_sampling = true
	collectgarbage("setpause", 100)
	collectgarbage("setstepmul", max_stepmul)
	collectgarbage("collect")
	base_mem = collectgarbage("count") / 1000
end


function LuaGC.MemStateReport(mem)
	need_check_lua_report = false

    local t = {
        res_count = 0,                  -- 资源数
        res_pool_count = 0,             -- 资源池
        gameobj_cache_count = 0,        -- 池GO
        gameobj_pool_count = 0,         -- GO池数
        gameobj_count = 0,              -- GO数
        time_quest_count = 0,           ---定时器个数
        itemlist_change_count = 0,      -- 物品监听数量1
        item_change_count = 0,          -- 物品监听数量2
        attr_listen_count = 0,          -- 属性监听数量
        event_count = 0,                -- 事件监听数量
        lua_obj_count = 0               -- lua对象数量
    }

    ResPoolMgr:GetPoolDebugInfo(t)
    ResMgr:GetDebugGameObjCount(t)
    GlobalTimerQuest:GetQuestCount(t)
    ItemWGData.Instance:GetDebugNotifyChangeCount(t)
    RoleWGData.Instance:GetDeubgListenCount(t)
    GlobalEventSystem:GetDebugEventCount(t)
    BundleCache:GetBundleCount(t)
    GetDebugLuaObjCount(t)

    local str_attr = "\n资源数量: " .. t.res_count .. "\n\n资源池: " .. t.res_pool_count .. "\n\nGO池: " .. t.gameobj_cache_count
        .. "\n\n池中GO数量: " .. t.gameobj_pool_count .. "\n\nGO数: " .. t.gameobj_count .. "\n\n定时器个数: " .. t.time_quest_count
        .. "\n\nbunlde数量: " .. t.bundle_count .. "\n\n物品监听数量: " .. t.item_change_count .. "\n\n属性监听数量: " .. t.attr_listen_count
        .. "\n\n事件监听数量: " .. t.event_count .. "\n\nlua对象数量: " .. t.lua_obj_count .. "\n\n当前lua内存：" .. (mem or 0)

    -- print_error("OnLowMemoryCheck", str_attr)

   	local date_stamp = os.date("%Y-%m-%d", os.time())
	local time_stamp = os.date("%H-%M-%S", os.time())
	local directory_path = ResUtil.GetCachePath(string.format("LuaMemLog/%s", date_stamp))
	if not SysDirectory.Exists(directory_path) then
		SysDirectory.CreateDirectory(directory_path)
	end


	local file_path = string.format("%s/%s.txt", directory_path, time_stamp)
	local file = io.open(file_path, "w")
	if nil == file then
		print_error("[ScenePreload] Write Debug Info Fail", file_path)
		return
	end

	file:write(str_attr)
	file:close()
end

function LuaGC.Update(now_time, elapse_time)
	if IS_CHECK_FUNTION_COST_TIME then
		return
	end

	if IS_CHECK_FUNTION_COST_MEM and CheckFuntionUseMem:IsCollecting() then
		return
	end

	-- IOS内存吃紧，不明白请研究ObjectTranslator
	if UNITY_IOS then
		return
	end

	if not is_sampling then
		return
	end

	if now_time < next_sample_time then
		return
	end

	next_sample_time = now_time + sample_time
	local cur_mem = collectgarbage("count")
	table.insert(smaple_mem_list, cur_mem)

	if need_check_lua_report and cur_mem ~= nil then
		local cur_mem_value = cur_mem / 1000
		if cur_mem_value >= 250 then
			LuaGC.MemStateReport(cur_mem_value)
		end
	end

	if #smaple_mem_list >= 4 then
		local mem = 0
		for i,v in ipairs(smaple_mem_list) do
			mem = mem + v
		end
		local avage_mem = mem / 1000 / #smaple_mem_list
		smaple_mem_list = {}

		if avage_mem > base_mem then
			local stepmul = 200 + (avage_mem - base_mem) / 100 * max_stepmul
			stepmul = math.min(stepmul, max_stepmul)
			collectgarbage("setstepmul", stepmul)
		end
	end
end