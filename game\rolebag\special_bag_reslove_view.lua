SpecialBagResloveView = SpecialBagResloveView or BaseClass(SafeBaseView)
function SpecialBagResloveView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
    self.view_cache_time = 0

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(930, 600)})
	self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_special_reslove_view")
end

function SpecialBagResloveView:LoadCallBack()
    self.is_on = false
    self.node_list.title_view_name.text.text = Language.Bag.ResloveTitle

    if not self.item_grid then
        self.item_grid = SpecialBagResloveGrid.New()
        self.item_grid:SetStartZeroIndex(false)
        self.item_grid:SetIsMultiSelect(true)
        self.item_grid:CreateCells({
            col = 5,
            cell_count = 45,
            list_view = self.node_list["item_grid"],
            itemRender = MeltEquipCell,
            change_cells_num = 2,
            --complement_col_item = true
        })
        self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

    if not self.fenjie_item_grid then
        self.fenjie_item_grid = AsyncBaseGrid.New()
        self.fenjie_item_grid:CreateCells({
            col = 4,
            list_view = self.node_list["fenjie_item_grid"],
            change_cells_num = 1,
        })
    end

    self.color_select_list = {}
    for i = 1, 4 do
        XUI.AddClickEventListener(self.node_list["quality_toggle_" .. i], BindTool.Bind(self.OnSelectColor, self, i))
    end
    XUI.AddClickEventListener(self.node_list["btn_reslove"], BindTool.Bind(self.OnClickReslove, self))
    XUI.AddClickEventListener(self.node_list.btn_select_color, BindTool.Bind(self.OnClickSelectColor, self))
    XUI.AddClickEventListener(self.node_list["close_color_list_part"], BindTool.Bind(self.OnClickSelectColor, self))

    self.is_first_load = true
    self.node_list["quality_toggle_1"].toggle.isOn = true
end

function SpecialBagResloveView:ReleaseCallBack()
    if self.item_grid then
        self.item_grid:DeleteMe()
        self.item_grid = nil
    end

    if self.fenjie_item_grid then
        self.fenjie_item_grid:DeleteMe()
        self.fenjie_item_grid = nil
    end

    self.info = nil
    self.is_first_load = nil
    self.grid_select_list = nil
    self.color_select_list = {}
end

function SpecialBagResloveView:SetDataAndOpen(info)
    if not info then
        return
    end

    self.info = info
    self:Open()
end

function SpecialBagResloveView:OnClickSelectColor()
    self.is_on = not self.is_on
    self.node_list.color_arrow_down:SetActive(self.is_on)
    self.node_list.color_arrow_up:SetActive(not self.is_on)
    self.node_list.color_list_part:SetActive(self.is_on) -- 打开颜色选中列表
end

function SpecialBagResloveView:OnBagSelectItemCB(cell)
    self:FlushGetItemView()
end

function SpecialBagResloveView:OnFlush()
    local bag_reslove_list
    local bag_type = self.info.bag_type
    if bag_type == KNAPSACK_TYPE.XIANJIE_EQUIP_BAG then
        bag_reslove_list = FairyLandEquipmentWGData.Instance:GetHolyEquipBagListBySlot(self.info.param_1)
    end

    self.item_grid:SetDataList(bag_reslove_list)
    self.node_list.item_grid.scroll_rect.verticalNormalizedPosition = 1
    self.item_grid:SetColorSelcet(bag_type, self.color_select_list)
    self:FlushGetItemView()
end

function SpecialBagResloveView:FlushGetItemView()
    self.grid_select_list = self.item_grid:GetAllSelectCell()
    local reslove_cfg = ConfigManager.Instance:GetAutoConfig("compose_auto").item_decompose
    local get_item_list = {}
    local item_id
    for k,v in pairs(self.grid_select_list) do
        local cfg = reslove_cfg[v.item_id]
        if cfg then
            for i,j in pairs(cfg.product_item) do
                item_id = j.item_id
                get_item_list[item_id] = get_item_list[item_id] or 0
                get_item_list[item_id] = get_item_list[item_id] + j.num
            end
        end
    end

    local show_list = {}
    for k,v in pairs(get_item_list) do
        table.insert(show_list, {item_id = k, num = v})
    end

    if not IsEmptyTable(show_list) then
        SortTools.SortAsc(show_list, "item_id")
    end

    --self.node_list["no_get_tips"]:SetActive(#show_list == 0)
    self.fenjie_item_grid:SetDataList(show_list)
end

function SpecialBagResloveView:OnSelectColor(color, is_on)
    if type(color) == "number" then
        self.color_select_list[color] = is_on
        self.node_list.cur_color_text.text.text = Language.Bag.NameList4[color]
    elseif type(color) == "table" then
        for k,v in pairs(color) do
            self.color_select_list[v] = is_on
        end
    end

    if self.is_first_load then
        self.is_first_load = nil
        return
    end

    if self.item_grid then
        self.item_grid:SetColorSelcet(self.info.bag_type, self.color_select_list)
    end

    -- 选中后的处理
    self.is_on = false
    self.node_list.color_list_part:SetActive(self.is_on)
    self.node_list.color_arrow_down:SetActive(self.is_on)
    self.node_list.color_arrow_up:SetActive(not self.is_on)

    self:FlushGetItemView()
end

function SpecialBagResloveView:OnClickReslove()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.FenJieError)
		return
	end

	local reslove_list = {}
	for k,v in pairs(select_list) do
        local data = {
            item_id = v.item_id,
            bag_index = v.index,
            num = v.num or 1,
        }
		table.insert(reslove_list, data)
	end

    self.item_grid:CancleAllSelectCell()
    RoleBagWGCtrl.Instance:ReqResloveItemList(self.info.bag_type, reslove_list)
end


---------------------------------------------------------
-- SpecialBagResloveGrid
---------------------------------------------------------
SpecialBagResloveGrid = SpecialBagResloveGrid or BaseClass(AsyncBaseGrid)
function SpecialBagResloveGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= 100 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenLingHe.ResloveLimit)
            return true
        end
    end

    return false
end

function SpecialBagResloveGrid:SetColorSelcet(bag_type, select_color_list)
    if IsEmptyTable(select_color_list) then
        return
    end

    local color
    self.select_tab[1] = {}
    self.cur_multi_select_num = 0
    local data = self.cell_data_list
    for i = 1, self.has_data_max_index do
        color = 0
        
        if data[i] then
            -- if bag_type == KNAPSACK_TYPE.XIANJIE_EQUIP_BAG then
                color = data[i].color
            -- end

            if select_color_list[color] then
                if self.cur_multi_select_num < 100 then
                    self.cur_multi_select_num = self.cur_multi_select_num + 1
                    self.select_tab[1][i] = true
                else
                    break
                end
            end
        end
    end

    self:__DoRefreshSelectState()
end

---------------------------------------------------------
-- SpecialGetItem
---------------------------------------------------------
SpecialGetItem = SpecialGetItem or BaseClass(BaseRender)
function SpecialGetItem:OnFlush()
    if self.data == nil then
        return
    end

    local icon_id = ItemWGData.Instance:GetItemIconByItemId(self.data.item_id)
    local bundle, asset = ResPath.GetItem(icon_id)
    self.node_list["can_get_icon"].image:LoadSprite(bundle, asset)
    self.node_list["can_get_num"].text.text = self.data.get_num
end