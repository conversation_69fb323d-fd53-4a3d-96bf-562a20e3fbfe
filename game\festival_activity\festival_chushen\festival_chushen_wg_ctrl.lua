require("game/festival_activity/festival_chushen/festival_chushen_wg_data")
require("game/festival_activity/festival_chushen/festival_chushen_view")
require("game/festival_activity/festival_chushen/festival_chushen_second_view")
require("game/festival_activity/festival_chushen/festival_chushen_result_view")

FestivalChuShenWGCtrl = FestivalChuShenWGCtrl or BaseClass(BaseWGCtrl)

function FestivalChuShenWGCtrl:__init()
	if FestivalChuShenWGCtrl.Instance then
		ErrorLog("[FestivalChuShenWGCtrl] Attemp to create a singleton twice !")
	end
	FestivalChuShenWGCtrl.Instance = self

	self.chushen_data = FestivalChuShenWGData.New()
	self.chushen_rank_view = FestivalChuShenSecondRankPanel.New()
	self.chushen_result_view = FestivalChuShenCookResultPanel.New()
	self.chushen_unlock_view = FestivalChuShenRewardShowPanel.New()

	self:RegisterAllProtocols()
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.ChuShenInfoReq, self))
	-- FestivalActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/FA_tiancaichushen_auto", BindTool.Bind(self.OnHotUpdate, self))
end

function FestivalChuShenWGCtrl:__delete()
	FestivalChuShenWGCtrl.Instance = nil

	if self.chushen_data then
		self.chushen_data:DeleteMe()
		self.chushen_data = nil
	end

	if self.chushen_rank_view  then
		self.chushen_rank_view:DeleteMe()
		self.chushen_rank_view = nil
	end

	if self.chushen_result_view  then
		self.chushen_result_view:DeleteMe()
		self.chushen_result_view = nil
	end

	if self.chushen_unlock_view then
		self.chushen_unlock_view:DeleteMe()
		self.chushen_unlock_view = nil
	end
end

function FestivalChuShenWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCFATianCaiChuShenLog,'OnSCFATianCaiChuShenLog')
	self:RegisterProtocol(SCFATianCaiChuShenInfo,'OnSCFATianCaiChuShenInfo')
	self:RegisterProtocol(SCFATianCaiChuShenFoodMenu,'OnSCFATianCaiChuShenFoodMenu')
	self:RegisterProtocol(SCFATianCaiChuShenCook,'OnSCFATianCaiChuShenCook')

	self:RegisterProtocol(CSFATianCaiChuShenOpera)

end

function FestivalChuShenWGCtrl:ChuShenInfoReq()
	self:CSFATianCaiChuShenOpera(FA_TIANCAICHUSHEN_OPERA_TYPE.FA_TIANCAICHUSHEN_OPERA_TYPE_1)
	self:CSFATianCaiChuShenOpera(FA_TIANCAICHUSHEN_OPERA_TYPE.FA_TIANCAICHUSHEN_OPERA_TYPE_2)
	self:CSFATianCaiChuShenOpera(FA_TIANCAICHUSHEN_OPERA_TYPE.FA_TIANCAICHUSHEN_OPERA_TYPE_3)
end

function FestivalChuShenWGCtrl:OpenChuShenSecondPanel()
	self.chushen_rank_view:Open()
end

--天才厨神信息请求
function FestivalChuShenWGCtrl:CSFATianCaiChuShenOpera(operate_typr, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFATianCaiChuShenOpera)
	protocol.operate_type = operate_typr
	protocol.param_1 = param1 or 0
	protocol.param_2 = param2 or 0
	protocol.param_3 = param3 or 0
	protocol:EncodeAndSend()
end

--厨神排行榜
function FestivalChuShenWGCtrl:OnSCFATianCaiChuShenLog(protocol)
	FestivalChuShenWGData.Instance:ChuShenRankData(protocol)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268)
	self.chushen_rank_view:Flush()
end

--厨神活动信息
function FestivalChuShenWGCtrl:OnSCFATianCaiChuShenInfo(protocol)
	FestivalChuShenWGData.Instance:ChuShenActInfoData(protocol)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268, "ChuShenAllInfo")
	self.chushen_unlock_view:Flush()
	RemindManager.Instance:Fire(RemindName.Festival_ChuShen)
end

function FestivalChuShenWGCtrl:OnSCFATianCaiChuShenFoodMenu(protocol)
	FestivalChuShenWGData.Instance:ChuShenFoodInfoData(protocol)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268)
	RemindManager.Instance:Fire(RemindName.Festival_ChuShen)
end

--烹饪结果返回
function FestivalChuShenWGCtrl:OnSCFATianCaiChuShenCook(protocol)
	FestivalChuShenWGData.Instance:ResultData(protocol)
	--FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268, "ChuShenResult")
	if protocol.cook_type and protocol.cook_type ~= TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_5 then
	    FestivalChuShenWGCtrl.Instance:OpenResultPanel(protocol)
	end
    FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268, "ChuShenResult")
	RemindManager.Instance:Fire(RemindName.Festival_ChuShen)
end

function FestivalChuShenWGCtrl:OpenResultPanel(protocol)
	self.chushen_result_view:SetDataOpen(protocol)
end

function FestivalChuShenWGCtrl:OpenRewardPanel()
	self.chushen_unlock_view:Open()
end

-- function FestivalChuShenWGCtrl:OnHotUpdate()
-- 	self.chushen_data:LFAdConfig()
-- 	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268, "ChuShenHotUpdata")
-- end