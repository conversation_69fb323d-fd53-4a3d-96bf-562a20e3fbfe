EquipTargetTipsView = EquipTargetTipsView or BaseClass(SafeBaseView)

local left_width = 196
local equip_item_width = 70

function EquipTargetTipsView:__init()
    --self.view_name = "EquipTargetTipiew"
    self:AddViewResource(0, "uis/view/rolebag_ui/equip_target_prefab", "layout_equiptarget_tip")
    self.index = 1
    self.view_cache_time = 0
end

function EquipTargetTipsView:__delete()

end

function EquipTargetTipsView:ReleaseCallBack()

    if nil ~= self.main_menu_icon_change then
		GlobalEventSystem:UnBind(self.main_menu_icon_change)
		self.main_menu_icon_change = nil
	end

    if nil ~= self.equip_obj_list then
        for k,v in pairs(self.equip_obj_list) do
            v:DeleteMe()
        end
        self.equip_obj_list = nil
    end

    self.load_callback = nil
    if CountDownManager.Instance:HasCountDown("equip_target_tips_close") then
        CountDownManager.Instance:RemoveCountDown("equip_target_tips_close")
    end

    if self.close_deley then
        GlobalTimerQuest:CancelQuest(self.close_deley)
        self.close_deley = nil
    end
    self.index = 1
end

function EquipTargetTipsView:CloseCallBack()
    TipWGCtrl.Instance:ShowNextSpecialTips()
end

function EquipTargetTipsView:LoadCallBack()
    if self.load_callback then
        self.load_callback()
        self.load_callback = nil
    end

    self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))
    self:CheckAutoClose()
    self:InitEquipList()
end

function EquipTargetTipsView:CheckAutoClose(time)
    local is_auto_close = true
    -- 仙君遗迹常驻
    local is_in, show_suit_index = EquipTargetWGData.Instance:IsVIPBossScene()
    if is_in then
        -- local cur_num,max_num = EquipTargetWGData.Instance:GetEquipStateNumBySuitIndex(show_suit_index)
        -- if cur_num < max_num then
            
        -- end
        is_auto_close = false
    end

    if is_auto_close then
        self:SetAutoClose(time or 3)
    end
end

function EquipTargetTipsView:OnFlush()
    local cfg, equip_solt, suit_index = EquipTargetWGData.Instance:GetEquipListById()

    local is_in, show_suit_index = EquipTargetWGData.Instance:IsVIPBossScene()
    if is_in and show_suit_index ~= suit_index then
        cfg, equip_solt, suit_index = EquipTargetWGData.Instance:GetEquipListByIndex(show_suit_index)
    end

    -- local info = EquipTargetWGData.Instance:GetEquipinfo(suit_index)
    self.equip_solt = equip_solt

    local max_count = EquipTargetWGData.Instance:GetEquipListCount(suit_index)
    local is_special = max_count == 6 --suit_index > 5

    for i,v in ipairs(self.equip_obj_list) do
        local is_inactive = is_special and (i == EquipTargetWGData.SuitPartNecklace or i == EquipTargetWGData.SuitPartDrop)
        self.node_list['equip_' .. i]:SetActive(not is_inactive)
        if cfg[i] then
            local item_id = tonumber(cfg[i])
            v:SetData({item_id = item_id, suit_index = suit_index, index = i})
        else
            v:SetData({})
        end
    end

    self.node_list.bg.rect.sizeDelta = Vector2(left_width + equip_item_width*max_count , 100)
end

function EquipTargetTipsView:InitEquipList()
    self.equip_obj_list = {}
    --local item
    for i=1,8 do
        self.equip_obj_list[i] = EquipTipsListRender.New(self.node_list['equip_' .. i])
        self.equip_obj_list[i]:SetIndex(i)
        --table.insert(self.equip_obj_list, item)
    end
end

function EquipTargetTipsView:SetLoadCallBack(show_func)
    self.load_callback = show_func
end

function EquipTargetTipsView:SetTipsShowData(item_id)
    self.item_id = item_id
end

function EquipTargetTipsView:SetAutoClose(time)
    if self:IsOpen() then
        if CountDownManager.Instance:HasCountDown("equip_target_tips_close") then
            CountDownManager.Instance:RemoveCountDown("equip_target_tips_close")
        end
        CountDownManager.Instance:AddCountDown("equip_target_tips_close", BindTool.Bind(self.UpdataTime, self), BindTool.Bind(self.TimeCompleteCallBack, self), nil, time, 1)
    end
end

function EquipTargetTipsView:UpdataTime(elapse_time, total_time)
end

function EquipTargetTipsView:TimeCompleteCallBack()
    self:Close()
end

function EquipTargetTipsView:GetEquipBtnNode(item_id)
    --print_log("GetEquipBtnNode++++++++++++", self.equip_solt) 
    if self.equip_solt and not IsEmptyTable(self.equip_obj_list) and self.equip_obj_list[self.equip_solt] then
        local cell = self.equip_obj_list[self.equip_solt]
        local node_obj = cell and cell.root_node
        return node_obj
    end
end

function EquipTargetTipsView:MainMenuIconChangeEvent(is_on)
    self.node_list.node_root:CustomSetActive(not is_on)
end


------------------------------------装备列表------------------------
EquipTipsListRender = EquipTipsListRender or BaseClass(BaseRender)

function EquipTipsListRender:__init()
    self.root_node = self.node_list["ph_icon"]
end

function EquipTipsListRender:__delete()
   if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.root_node = nil
end

function EquipTipsListRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.ph_icon)
    end
end

function EquipTipsListRender:OnFlush()
    local data = self:GetData()
    if not data then
        return
    end

    local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(data.suit_index, data.index) 
    self.item_cell:SetData({item_id = data.item_id, param = {star_level = star_level}})
    self.item_cell:SetGraphicGreyCualityBg(state)
    self.item_cell:SetDefaultEff(not state)
end
