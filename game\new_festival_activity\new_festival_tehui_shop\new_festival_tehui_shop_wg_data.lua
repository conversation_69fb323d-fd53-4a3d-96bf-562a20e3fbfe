NewFestivalTehuiShopWGData = NewFestivalTehuiShopWGData or BaseClass()
function NewFestivalTehuiShopWGData:__init()
	if NewFestivalTehuiShopWGData.Instance then
		ErrorLog("[NewFestivalTehuiShopWGData] Attemp to create a singleton twice !")
	end

	NewFestivalTehuiShopWGData.Instance = self

	self:InitCfg()
end

function NewFestivalTehuiShopWGData:InitCfg()
    local tehui_shop_cfg = ConfigManager.Instance:GetAutoConfig("new_festival_activity_shop_config_auto")
    self.purchase_shop_cfg = ListToMapList(tehui_shop_cfg.zhigou_shop, "grade")
    self.xianyu_shop_cfg = ListToMapList(tehui_shop_cfg.xianyu_shop, "grade")
end

function NewFestivalTehuiShopWGData:__delete()
    NewFestivalTehuiShopWGData.Instance = nil
end

function NewFestivalTehuiShopWGData:SetTeHuiShopInfo(protocol)
    self.grade = protocol.grade
    self.purchase_shop_cost_times_list = protocol.purchase_shop_cost_times_list
    self.xianyu_shop_cost_times_list = protocol.xianyu_shop_cost_times_list
end

function NewFestivalTehuiShopWGData:GetPurchaseShopcfg()
    local grade = self.grade or 1
    return self.purchase_shop_cfg[grade]
end

function NewFestivalTehuiShopWGData:GetXianYuShopcfg()
    local grade = self.grade or 1
    return self.xianyu_shop_cfg[grade]
end

function NewFestivalTehuiShopWGData:GetXianYuShopTimesBySeq(seq)
    local xianyu_shop_cost_times_list = self.xianyu_shop_cost_times_list
    if xianyu_shop_cost_times_list then
        return xianyu_shop_cost_times_list[seq]
    end
    return 0
end

function NewFestivalTehuiShopWGData:GetPurchaseShopTimesBySeq(seq)
    local purchase_shop_cost_times_list = self.purchase_shop_cost_times_list
    if purchase_shop_cost_times_list then
        return purchase_shop_cost_times_list[seq]
    end
    return 0
end

function NewFestivalTehuiShopWGData:GetPurchaseShopDataList()
    local purchase_shop_cfg = self:GetPurchaseShopcfg()
    if not IsEmptyTable(purchase_shop_cfg) then
        local purchase_shop_data = {}						-- 朋友列表
        for k, v in pairs(purchase_shop_cfg) do
            local purchase_shop_list = {}
            local sale_flag = 1
            local cost_times = self:GetPurchaseShopTimesBySeq(v.seq)
            sale_flag = v.limit_count - cost_times <= 0 and 1 or 0
            purchase_shop_list.seq = v.seq
            purchase_shop_list.grade = v.grade
            purchase_shop_list.limit_type = v.limit_type
            purchase_shop_list.limit_count = v.limit_count
            purchase_shop_list.reward_item = v.reward_item
            purchase_shop_list.rmb_type = v.rmb_type
            purchase_shop_list.rmb_seq = v.rmb_seq
            purchase_shop_list.price = v.price
            purchase_shop_list.discount = v.discount
            purchase_shop_list.sale_flag = sale_flag
            purchase_shop_data[k] = purchase_shop_list
        end
        if not IsEmptyTable(purchase_shop_data) then
            table.sort(purchase_shop_data, SortTools.KeyLowerSorter("sale_flag", "seq"))
        end
        return purchase_shop_data
    end
end

function NewFestivalTehuiShopWGData:GetXianyuShopDataList()
    local xianyu_shop_cfg = self:GetXianYuShopcfg()
    if not IsEmptyTable(xianyu_shop_cfg) then
        local xianyu_shop_data = {}						-- 朋友列表
        for k, v in pairs(xianyu_shop_cfg) do
            local xianyu_shop_list = {}
            local sale_flag = 1
            local cost_times = self:GetXianYuShopTimesBySeq(v.seq)
            sale_flag = v.limit_count - cost_times <= 0 and 1 or 0
            xianyu_shop_list.seq = v.seq
            xianyu_shop_list.grade = v.grade
            xianyu_shop_list.limit_type = v.limit_type
            xianyu_shop_list.limit_count = v.limit_count
            xianyu_shop_list.reward_item = v.reward_item
            xianyu_shop_list.free_item = v.free_item
            xianyu_shop_list.xianyu_price = v.xianyu_price
            xianyu_shop_list.discount = v.discount
            xianyu_shop_list.sale_flag = sale_flag
            xianyu_shop_data[k] = xianyu_shop_list
        end
        if not IsEmptyTable(xianyu_shop_data) then
            table.sort(xianyu_shop_data, SortTools.KeyLowerSorter("sale_flag", "seq"))
        end
        return xianyu_shop_data
    end
end