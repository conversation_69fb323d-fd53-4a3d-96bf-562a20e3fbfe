require("game/guild/guild_boss/guild_boss_wg_data")
require("game/guild/guild_boss/guild_boss_task_view")
require("game/guild/guild_boss/guild_boss_tips_view")
require("game/guild/guild_boss/guild_boss_reward_view")

-- 仙盟
GuildBossWGCtrl = GuildBossWGCtrl or BaseClass(BaseWGCtrl)

function GuildBossWGCtrl:__init()
	if GuildBossWGCtrl.Instance then
		ErrorLog("[GuildBossWGCtrl]:Attempt to create singleton twice!")
	end
	GuildBossWGCtrl.Instance = self

	self.data = GuildBossWGData.New()
	self.task_view = GuildBossTaskView.New(GuideModuleName.GuildBossTaskView)
	self.tips_view = GuildBossTipsView.New(GuideModuleName.GuildBossTipsView)
	self.guild_boss_reward = GuildBossRewardView.New(GuideModuleName.GuildBossRewardView)
	self.alert_tips = Alert.New()
	self:RegisterAllProtocols()
end

function GuildBossWGCtrl:__delete()
	if self.task_view then
		self.task_view:DeleteMe()
		self.task_view = nil
	end

	if self.tips_view then
		self.tips_view:DeleteMe()
		self.tips_view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end

	if self.guild_boss_reward then
		self.guild_boss_reward:DeleteMe()
		self.guild_boss_reward = nil
	end
	
	GuildBossWGCtrl.Instance = nil
end

-- 注册事件
function GuildBossWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCGuildBossSceneInfo, "OnGuildBossSceneInfo")
	self:RegisterProtocol(SCGuildBossStatus, "OnGuildBossStatus")
	self:RegisterProtocol(SCGuildBossHurtWarnInfo, "OnGuildBossHurtWarnInfo")

	self:RegisterProtocol(CSGuildBossShaiZi)
	self:RegisterProtocol(CSGuildBossShaiZiEnd)
    self:RegisterProtocol(SCGuildBossShaiZiInfo, "OnSCGuildBossShaiZiInfo")
    
    self:RegisterProtocol(SCGuildBossMaxDiceInfo, "OnSCGuildBossMaxDiceInfo")
    
	self:RegisterProtocol(SCGuildBossDiceRecord, "OnSCGuildBossDiceRecord")
	self:RegisterProtocol(SCGuildBossDiceRecordUpdate, "OnSCGuildBossDiceRecordUpdate")
end

-- 仙盟boss伤害警告下发
function GuildBossWGCtrl:OnGuildBossHurtWarnInfo(protocol)
	self.data:SetGuildBossHurtWarnInfo(protocol)
	self:FlushTipsView()
end

function GuildBossWGCtrl:GetBossState()
	return self.boss_state
end

function GuildBossWGCtrl:OpenTaskView()
	self.task_view:Open()
end

function GuildBossWGCtrl:GetTaskView()
	return self.task_view
end

function GuildBossWGCtrl:CloseTaskView()
	self.task_view:ResetTaskPanel()
	self.task_view:Close()
end

function GuildBossWGCtrl:OpenTipsView()
	self.tips_view:Open()
end
function GuildBossWGCtrl:FlushTipsView()
	self.tips_view:Flush()
end

function GuildBossWGCtrl:CloseTipsView()
	self.tips_view:Close()
end

-- 仙盟boss开启状态
function GuildBossWGCtrl:OnGuildBossStatus(protocol)
	local flag = GuildBossWGData.Instance:GetShowTipsFlag()
    local scene_type = Scene.Instance:GetSceneType()
    local enter_flag = GuildWGData.Instance:GetHideGuildBossRemind()
    self.data:SetGuildBossStatus(protocol)
    self.data:SetGuildBossIcon(protocol)
	if protocol.uid ~= RoleWGData.Instance:InCrossGetOriginUid() and
	self.data:IsGuildBossOpen() and protocol.finish_timestamp > 0 and not flag and not enter_flag and scene_type ~= SceneType.GuildBoss then
		self.alert_tips:SetLableString(Language.GuildBoss.GuildBossOpenTips)
		self.alert_tips:Open()
		self.alert_tips:SetOkFunc(GuildBossWGCtrl.SendGuildBossEnterReq)
		GuildBossWGData.Instance:SetShowTipsFlag(true)
	end

	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_activity)
	--ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_boss)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_Boss)--活动Boss
	-- RemindManager.Instance:Fire(RemindName.Guild_Activity)--仙盟活动
	RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面

end

-- 仙盟boss场景信息
function GuildBossWGCtrl:OnGuildBossSceneInfo(protocol)
	if self.data:GetGuildBossOutTime() == 0 and protocol.finish_timestamp > 0 then
		FuBenWGData.Instance:GetSetFBPrepareTime(protocol.next_boss_refresh_timestamp )
		FuBenPanelWGData.Instance:SetOutTimer(protocol.finish_timestamp)
		FuBenWGData.Instance:SetOutFbTime(protocol.finish_timestamp)
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.finish_timestamp)
	end

	local old_scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	if old_scene_info.dice_rounds ~= protocol.dice_rounds then
		GuildBossWGData.Instance:ClearMaxDiceData()
	end 

	self.data:SetGuildBossSceneInfo(protocol)
	self.task_view:Flush()
	if protocol.is_pass == 1  then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if protocol.finish_timestamp < protocol.kick_out_timestamp then
			if protocol.finish_timestamp > server_time then
				FuBenPanelWGCtrl.Instance:SetCountDowmTimer(protocol.finish_timestamp)
			end
		else
			if protocol.kick_out_timestamp > server_time then
				FuBenPanelWGCtrl.Instance:SetCountDowmTimer(protocol.kick_out_timestamp)
			end
		end

		if self.guild_boss_reward:IsOpen() and self.guild_boss_reward:IsLoaded() then
			self.guild_boss_reward:ShowBossShaiZi()
		end

		-- if self.task_view:IsOpen() then
		-- 	self.task_view:Flush(0, "flush_shaizi_btn_info")
		-- end
	else
		if protocol.next_boss_refresh_timestamp == 0 then
			if not IsEmptyTable(Scene.Instance:GetMonsterList()) then
				self:DoStarMove()
			end
		end
	end
end

function GuildBossWGCtrl:OpenGuildBossReward()
	ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[CHANNEL_TYPE.GUILD])
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_start_time = scene_info.dice_start_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if server_time > dice_start_time then
		if self.guild_boss_reward:IsOpen() and self.guild_boss_reward:IsLoaded() then
			self.guild_boss_reward:Flush()
		else
			MainuiWGCtrl.Instance:HideOrShowChatView(true)
			self.guild_boss_reward:Open()
		end
	end
end

-- 请求进入仙盟boss
function GuildBossWGCtrl.SendGuildBossEnterReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBossEnterReq)
	send_protocol:EncodeAndSend()
end

-- 请求开启仙盟boss
function GuildBossWGCtrl.SendGuildBossStartReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBossStartReq)
	send_protocol:EncodeAndSend()
end

function GuildBossWGCtrl:DoStarMove()
	
end

function GuildBossWGCtrl:SendCSGuildBossShaiZi(is_pass)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBossShaiZi)
	send_protocol.is_pass = is_pass or 0
	send_protocol:EncodeAndSend()
end

function GuildBossWGCtrl:SendCSGuildBossShaiZiEnd()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBossShaiZiEnd)
	send_protocol:EncodeAndSend()
end

function GuildBossWGCtrl:OnSCGuildBossShaiZiInfo(protocol)
	self.shaizi_num = protocol.shaizi_num
	self.data:SetDiceRoundShaiZiNum(protocol.shaizi_num)
	if self.guild_boss_reward:IsOpen() and self.guild_boss_reward:IsLoaded() then
		self.guild_boss_reward:FlushShaiZiNum(protocol.shaizi_num)
	end
end

function GuildBossWGCtrl:OnSCGuildBossMaxDiceInfo(protocol)
    self.data:SetMaxDiceData(protocol)
	if self.guild_boss_reward:IsOpen() and self.guild_boss_reward:IsLoaded() then
		self.guild_boss_reward:FlushMaxShaiziRole()
	end
end

function GuildBossWGCtrl:PlayShaiZiAnim(num)
	if self.guild_boss_reward:IsOpen() and self.guild_boss_reward:IsLoaded() then
		self.guild_boss_reward:PlayShaiZiAnim(num)
	end
end

function GuildBossWGCtrl:OnSCGuildBossDiceRecord(protocol)
	self.data:SetSCGuildBossDiceRecord(protocol)

	if self.guild_boss_reward:IsOpen() and self.guild_boss_reward:IsLoaded() then
		self.guild_boss_reward:Flush()
	end
end

function GuildBossWGCtrl:OnSCGuildBossDiceRecordUpdate(protocol)
	self.data:SetSCGuildBossDiceRecordUpdate(protocol)
	if self.guild_boss_reward:IsOpen() and self.guild_boss_reward:IsLoaded() then
		self.guild_boss_reward:Flush()
	end
	local other_cfg = GuildBossWGData.Instance:GetOtherCfg()
	local dice_record = protocol.dice_record
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local max_dice_rounds = scene_info.max_dice_rounds
	if max_dice_rounds > 0 and dice_record.round >= max_dice_rounds then
		self:CloseGuildBossReward()
		self.data:SetMaxDiceRecordEnd(true)
		MainuiWGCtrl.Instance:FlushView(0, "guild_boss_shaizi_state", {false})
		ReDelayCall(self, function ()
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.GuildBoss then
				TipWGCtrl.Instance:OpenAlertTips(Language.GuildBoss.CurActClose, function ()
					FuBenWGCtrl.Instance:SendLeaveFB()
				end)
			end
		end, 2, "guild_boss_delay_open_alert")
	end
end

function GuildBossWGCtrl:CloseGuildBossReward()
	if self.guild_boss_reward:IsOpen() then
		self.guild_boss_reward:Close()
	end
end

function GuildBossWGCtrl:SetShaiZiBtnActive(flag)
	if self.task_view then
		self.task_view:SetShaiZiBtnActive(flag)
	end
end

function GuildBossWGCtrl:HideShaiZiBtn(all_close)
	if self.task_view then
		self.task_view:HideShaiZiBtn(all_close)
	end
end

function GuildBossWGCtrl:ShowShaiZiTips()
	if self.task_view then
		self.task_view:ShowShaiZiTips()
	end
end

function GuildBossWGCtrl:ShowOrHideHurtRankList(is_show)
	if self.task_view and self.task_view:IsOpen() then
		self.task_view:Flush(0, "ShowOrHideHurtRankList", {state = is_show})
	end
end

function GuildBossWGCtrl:GuildActBossGoFunc()
	-- 改成自动开启了
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local is_open_act = guild_bosss_info.finish_timestamp > TimeWGCtrl.Instance:GetServerTime()
	if is_open_act then	
		GuildBossWGCtrl.SendGuildBossEnterReq()
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossTimeTips)
	end
end