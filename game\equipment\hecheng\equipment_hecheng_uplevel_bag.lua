EquipmentUpLevelBagView = EquipmentUpLevelBagView or BaseClass(SafeBaseView)

function EquipmentUpLevelBagView:__init()
	-- self.is_any_click_close = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_tips_bg_panel1", {vector2 = Vector2(0, 14), sizeDelta = Vector2(348,344)})
	self:AddViewResource(0, "uis/view/compose_ui_prefab", "layout_hecheng_baglist")
	self.click_call_back = nil
end

function EquipmentUpLevelBagView:__delete()
end

function EquipmentUpLevelBagView:CloseCallBack()
	self.click_call_back = nil
	self.select_item_data = nil
end

function EquipmentUpLevelBagView:SetHeChengData(call_back, select_item_data)
	self.click_call_back = call_back
	self.select_item_data = select_item_data
end

function EquipmentUpLevelBagView:ReleaseCallBack()
	if self.item_list_view then
		self.item_list_view:DeleteMe()
		self.item_list_view = nil
	end
end

function EquipmentUpLevelBagView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Compose.SelectEquipTitle
	self.item_list_view = AsyncListView.New(HechengBagItemRender, self.node_list["ph_hecheng_baglist"])
	self.item_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectCellItemCallBack, self))
	self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtn,self))
end

function EquipmentUpLevelBagView:ShowIndexCallBack(index)
end


function EquipmentUpLevelBagView:OnFlush()
	local data = self.select_item_data
	if data == nil then return end
	-- if data.zhizun == 1 then
	-- 	--至尊装备
	-- 	local demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
	-- 	if demand_data ~= nil then
	-- 		local equip_list = EquipmentWGData.Instance:GetHechengZhiZunEquipmentItemList(demand_data)
	-- 		self.item_list_view:SetDataList(equip_list)
	-- 	end
	-- else
	if data.level then
		local index = ComposeWGCtrl.Instance:GetShowIndex()
		if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			local demand_data = EquipmentWGData.Instance:GetSSEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
			if demand_data ~= nil then
				local equip_list = EquipmentWGData.Instance:GetSSHechengEquipmentItemList(demand_data)
				self.item_list_view:SetDataList(equip_list,3)
			end
		end


		-- local compose_data = EquipmentWGData.Instance:GetComposeByVItemId(data.item_id)
		-- local shenshou_bag_filter_list = __TableCopy(ShenShouWGData.Instance:FilterShenShouEq(compose_data.need_qualit, compose_data.need_start_num))
		-- shenshou_bag_filter_list[0] = table.remove(self.shenshou_bag_filter_list, 1)
		-- self.item_list_view:SetDataList(shenshou_bag_filter_list, 3)
	else
		--普通装备
		local demand_data = EquipmentWGData.Instance:GetEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
		if demand_data ~= nil then
		 	local equip_list = EquipmentWGData.Instance:GetHechengEquipmentItemList(demand_data, true)
		 	self.item_list_view:SetDataList(equip_list, 3)
		end
	end
end

function EquipmentUpLevelBagView:OnClickCloseBtn()
	self:Close()
end


-- click ----------------------------------------------
function EquipmentUpLevelBagView:SelectCellItemCallBack(item, cell_index, is_default, is_click)
	if item == nil then
		return
	end

	local data = __TableCopy(item:GetData())
	if data == nil or not is_click then
		return
	end
	if data.param and data.param.impression and data.param.impression > 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Compose.SelectComposeEquipError)
		return
	end

	if data.type_list ~= nil then
		data.type_index = data.type_list[1]
	end
	if self.click_call_back then
		self.click_call_back(data)
	end
	-- self.item:SetData(data)

	-- item_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(data.item_id)	-- 获取神兽装备配置
	-- if item_cfg then
	-- 	self.item:SetButtonComp(true)
	-- 	local bundle_name, asset = ResPath.GetItem(item_cfg.icon_id)
	-- 	self.item:SetItemIcon(bundle_name, asset)
	-- end
	-- ComposeWGCtrl.Instance.view:Flush({COMPOSE_TYPE.EQ_HECHENG, COMPOSE_TYPE.EQ_HECHENG_TWO, COMPOSE_TYPE.EQ_HECHENG_THREE})--, COMPOSE_TYPE.EQ_HECHENG_SHENSHOW
	self:Close()
end

----------------------------------------------------
-- HechengBagItemRender
----------------------------------------------------
HechengBagItemRender = HechengBagItemRender or BaseClass(BaseRender)
function HechengBagItemRender:__init()
end

function HechengBagItemRender:__delete()
	if self.item_tip then
		self.item_tip:DeleteMe()
		self.item_tip = nil
	end
end

function HechengBagItemRender:CreateChild(index)
	if self.item_tip then
		self.item_tip:DeleteMe()
		self.item_tip = nil
	end

	if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		self.item_tip = ShenShouEquipCell.New(self.node_list["ph_hechengbag_item_pos"])
		self.item_tip:SetItemTipFrom(ShenShouEquipTip.FROM_EQUIMENT_HECHENG)
	else
		self.item_tip = ItemCell.New(self.node_list["ph_hechengbag_item_pos"])
		self.item_tip:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
	end
	self.item_tip:SetIsShowTips(false)
end

function HechengBagItemRender:OnFlush()
	local data = self:GetData()
	if nil == data then
		return
	end
	local color = 1
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local index = ComposeWGCtrl.Instance:GetShowIndex()
	self:CreateChild(index)

	if item_cfg then
		self.item_tip:SetData(data)
		if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW and data and data.strength_level and data.strength_level > 0 then
			--策划需求，只有神兽装备才需要在已选中材料显示等级
			self.item_tip:SetRightTopImageText("+" .. data.strength_level)
		end
		color = item_cfg.color
		self.node_list["lbl_hecheng_item_name"].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[color])
		self.item_tip:SetButtonComp(false)
		self.item_tip:SetCellBgEnabled(false)
	else
		if index == COMPOSE_TYPE.LING_CHONG then
			-- print_error(data)
		elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			item_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(data.item_id)	-- 获取神兽装备配置
			-- compose_data = EquipmentWGData.Instance:GetComposeByVItemId(data.item_id)
			if not item_cfg then return end
			self.item_tip:SetData({item_id = data.item})
			local bundle_name, asset = ResPath.GetItem(item_cfg.icon_id)
			self.item_tip:SetItemIcon(bundle_name, asset)
			color = item_cfg.quality
			local bundle, asset = ResPath.GetCommonBackGround("cell_quality_bg"..color)
			self.item_tip:SetCellBg(bundle, asset)
			local compose_data = EquipmentWGData.Instance:GetComposeByVItemId(data.item_id)
			self.item_tip:SetLeftTopImg(data.star_count)--compose_data.need_start_num
			self.node_list["lbl_hecheng_item_name"].text.text = ToColorStr(item_cfg.name, EQUIP_COLOR[color])
		end
	end
end

function HechengBagItemRender:CreateSelectEffect()

end
