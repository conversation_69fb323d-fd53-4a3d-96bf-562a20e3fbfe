-----------------月卡----------------------
function YiBenWanLiView:InitMonthCardView()
	RoleWGData.Instance:SetDayFirstLoginFlag(false)
	self.month_card_type = -1
	self:CreateCardTypeList()
end

function YiBenWanLiView:DeleteMonthCard()
	if self.month_card_list then
		for i=1,2 do
			self.month_card_list[i]:DeleteMe()
		end
		self.month_card_list = nil
	end

end
function YiBenWanLiView:FlushMonthCard()--刷新是否可以领取奖励--sss
	if self.month_card_list then
		local data_list = {{card_type = 0},{card_type = 1}}
 		for i,v in ipairs(data_list) do
 			self.month_card_list[i]:SetData(v)
 		end
	end
end

function YiBenWanLiView:CreateCardTypeList()
	if not self.month_card_list then
		self.month_card_list = {}
		for i=1,2 do
			self.month_card_list[i] = MonthCardTypeItemRender.New(self.node_list['ph_card_type_item_' .. i])
			self.month_card_list[i].parent_view = self
		end
	end
end
function YiBenWanLiView:MonthCardPlayEffect()
	if self.node_list.month_effect_pos then
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_zhakai)
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectXianYuHuoDe, false, true))
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.month_effect_pos.transform,1.6,nil,nil,nil,BindTool.Bind(self.PlayEffectBySecond,self) )
	end
end


function YiBenWanLiView:PlayEffectBySecond()
	TipWGCtrl.Instance:DestroyFlyEffectByViewName(GuideModuleName.YiBenWanLiView)
	if self.money_bar then
		local end_obj = self.money_bar:GetSlicketNode(GameEnum.MONEY_BAR.GOLD)

		if end_obj then
			local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_single)
			TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Recharge, bundle_name,
	    asset_name, self.node_list.effect_pos_recharge, end_obj, DG.Tweening.Ease.OutCubic, 0.5, nil, nil, 20, 200)
		end
	end
end

function YiBenWanLiView:MonthCardPlayEffect2()
	if self.node_list.month_effect_pos then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanbao_zhakai_new")
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.month_effect_pos.transform,1.6,nil,nil,nil,BindTool.Bind(self.PlayEffectBySecond2,self) )
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectXianYuHuoDe, false, true))
	end
end


function YiBenWanLiView:PlayEffectBySecond2()
	TipWGCtrl.Instance:DestroyFlyEffectByViewName(GuideModuleName.YiBenWanLiView)
	if self.money_bar then
		local end_obj = self.money_bar:GetSlicketNode()

		if end_obj then
			TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.YiBenWanLiView, "effects2/prefab/ui/ui_yuanbao_new_prefab",
	    "UI_yuanbao_new", self.node_list.month_effect_pos, end_obj, DG.Tweening.Ease.OutCubic, 0.5, nil, nil, 20, 200)
		end
	end
end

-------MonthCardTypeItemRender
MonthCardTypeItemRender = MonthCardTypeItemRender or BaseClass(BaseRender) --周卡和月卡
function MonthCardTypeItemRender:__init()
end

function MonthCardTypeItemRender:__delete()
	if self.month_card_alert then
		self.month_card_alert:DeleteMe()
		self.month_card_alert = nil
	end
	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end
	self.parent_view = nil
end

function MonthCardTypeItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_touzi, BindTool.Bind1(self.OnClickOpenMonthCard, self))
	XUI.AddClickEventListener(self.node_list.btn_lingjiang, BindTool.Bind(self.OnClickLingJiang, self))
end

function MonthCardTypeItemRender:OnFlush()
	if not self.data then return end
	local other_cfg = RechargeWGData.Instance:GetTZJHOtherCfg()
	local card_type = self.data.card_type
	-- 刷新模型
	self:FlushDisplayModel(other_cfg['invest_card_'.. card_type ..'_model'])
	-- self.node_list.multiple_num.text.text = other_cfg['card_'.. card_type ..'_multiple']
	self.node_list.quick_get_num.text.text = other_cfg['invest_card_'.. card_type ..'_rebate']
	self.node_list.day_get_num.text.text = RechargeWGData.Instance:GetMonthCardGetAllMoney(card_type)
	self.node_list.card_des.text.text = RechargeWGData.Instance:GetMonthCardDesc(card_type)
	self.node_list.touzi_text.text.text = other_cfg['invest_card_'.. card_type ..'_price'] .. Language.Recharge.Card_Bottom_1
	self.node_list.month_lingqu_red:SetActive(true)

	self:FlushIsCanLJ(card_type)

	local succ_flag = RechargeWGData.Instance:MouthCardSuccFlag()
	if succ_flag == 1 then
		RechargeWGData.Instance:MouthCardSuccFlag(0)
		-- self.parent_view:MonthCardPlayEffect()
	end
end

function MonthCardTypeItemRender:FlushDisplayModel(res_id)
	if self.display_model then
		return
	end
	local bundle,asset = ResPath.GetItemModel( res_id )
	local ui_rotation = self.data.card_type == 1 and Vector3(0,-162,0) or Vector3(0,180,0)

	if not self.display_model then
		self.display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.display_model:SetRenderTexUI3DModel(display_data)
		-- self.display_model:SetUI3DModel(self.node_list["display"].transform,self.node_list["display"].event_trigger_listener)
	end

	self.display_model:SetMainAsset(bundle, asset)
	self.display_model:CustomDisplayPositionAndRotation( Vector3(0,0,0), ui_rotation,Vector3(450,450,450))
end


--刷新是否可以领取奖励
function MonthCardTypeItemRender:FlushIsCanLJ(card_type)

	local month_card_cfg = RechargeWGData.Instance:GetMouthCardCfg(card_type)--所有的领取信息
	-- 已领取次数
	local aleardy_day = 0
	--可领取的天数,比实际的天数少1，从0开始
	local card_day = RechargeWGData.Instance:GetMonthCardState(card_type)
	--card_day == -1 表示没有投资
	self.node_list.btn_touzi:SetActive(card_day == -1)
	self.node_list.btn_lingjiang:SetActive(card_day ~= -1)

	if card_day ~= -1 then
		for i=1,#month_card_cfg do
			-- 当天是否领取
			local flag = RechargeWGData.Instance:GetMonthCardIsGeted(card_type, month_card_cfg[i].day + 1)
			aleardy_day = flag == 1 and aleardy_day + 1 or aleardy_day
	   		if card_day == month_card_cfg[i].day or ((card_day+1) >= #month_card_cfg and i == #month_card_cfg) then
	   			if flag == 0 then
	   				self.node_list.lingqu_text.text.text = string.format(Language.Recharge.Card_Bottom_2, month_card_cfg[i].reward_silver)
	   				self.node_list.redpoint:SetActive(true)
	    			XUI.SetButtonEnabled(self.node_list.btn_lingjiang, true)
	    		else
	    			self.node_list.lingqu_text.text.text = Language.Recharge.Card_Tip_1
	 				self.node_list.month_lingqu_red:SetActive(false)
	 				self.node_list.redpoint:SetActive(false)
	 				XUI.SetButtonEnabled(self.node_list.btn_lingjiang, false)
	    		end
	    		break
			end
		end
	end

	local shengyu_day = #month_card_cfg - aleardy_day
	shengyu_day = shengyu_day < 0 and 0 or shengyu_day
	self.node_list.shengyuday.text.text = string.format(Language.Recharge.SehgnyuDay,shengyu_day)    --ToColorStr("（" .. string.format(Language.Recharge.SehgnyuDay,shengyu_day) .. "）",COLOR3B.GREEN)
	self.node_list.shengyuday:SetActive(RechargeWGData.Instance:GetMonthCardState(card_type) ~= -1)


	-- 投资按钮置灰判断
	local had_fetch = RechargeWGData.Instance:GetMonthCardLastFetchState(card_type)
	XUI.SetButtonEnabled(self.node_list.btn_touzi,not had_fetch)
	if had_fetch then
		self.node_list.touzi_text.text.text = Language.Recharge.Card_Bottom_3
		self.node_list.month_lingqu_red:SetActive(false)
	end
end


--投资按钮
function MonthCardTypeItemRender:OnClickOpenMonthCard()
	-- local card_type = self.data.card_type
	-- local card_index = RechargeWGData.Instance:GetMonthCardState(card_type)
	-- if card_index >= 0 then
	-- 	-- RechargeWGCtrl.Instance:SendBuyInvestCard(card_type)
	-- 	return
	-- end

	-- if nil == self.month_card_alert then
	-- 	self.month_card_alert = Alert.New(nil, nil, nil, nil, true)
	-- 	self.month_card_alert:SetShowCheckBox(false)
	-- end

	-- local month_card_cfg = ConfigManager.Instance:GetAutoConfig("touzijihua_auto").other
	-- if month_card_cfg[1]["invest_card_" .. card_type .. "_price"] then  --<= RoleWGData.Instance:GetRoleInfo().gold
	-- 	self.month_card_alert:Open()
	-- 	self.month_card_alert:SetLableString(string.format(Language.Recharge.MonthCardTips, month_card_cfg[1]["invest_card_" .. card_type .. "_price"]))
	-- 	self.month_card_alert:SetOkFunc(BindTool.Bind(self.OpenMonthCard, self))
	-- else
	-- 	RechargeWGCtrl.Instance:SendBuyInvestCard(card_type)
	-- end
	local other_cfg = RechargeWGData.Instance:GetTZJHOtherCfg()
	if other_cfg and self.data.card_type then
		local money = other_cfg['invest_card_'.. self.data.card_type ..'_price']
		if money then
			RechargeWGCtrl.Instance:Recharge(money, GET_GOLD_REASON.GET_GOLD_REASON_TOUZIJIHUA)
		end
	end
end

-- 请求投资
function MonthCardTypeItemRender:OpenMonthCard()
	RechargeWGCtrl.Instance:SendBuyInvestCard(self.data.card_type)
end

-- 领取银票
function MonthCardTypeItemRender:OnClickLingJiang() --领取按钮事件
	local card_type = self.data.card_type
	local month_card_cfg = RechargeWGData.Instance:GetMouthCardCfg(card_type)--所有的领取信息
	local card_day = RechargeWGData.Instance:GetMonthCardState(card_type)

	for i=1,#month_card_cfg do
		local flag = RechargeWGData.Instance:GetMonthCardIsGeted(card_type, month_card_cfg[i].day + 1)
		if flag == 0 and card_day >= month_card_cfg[i].day then
			RechargeWGCtrl.Instance:SendFetchInvestCardReward(card_type, month_card_cfg[i].day)
		end
	end
	-- self.parent_view:MonthCardPlayEffect2()
	-- AudioService.Instance:PlayRewardAudio()
end
