require("game/compose/compose_wg_data")
require("game/compose/compose_view")
require("game/compose/compose_equipment_view")
require("game/compose/compose_xianqi_view")
require("game/compose/compose_xianqi_stuff_view")
require("game/compose/compose_stuff_list_view")
require("game/compose/compose_equipment_once_view")

-- 炼炉
ComposeWGCtrl = ComposeWGCtrl or BaseClass(BaseWGCtrl)

function ComposeWGCtrl:__init()
	if ComposeWGCtrl.Instance then
		ErrorLog("[ComposeWGCtrl]:Attempt to create singleton twice!")
	end
	ComposeWGCtrl.Instance = self

	self.data = ComposeWGData.New()
	self.view = NewComposeView.New(GuideModuleName.Compose)
	self.stuff_list_view = ComposeStuffListView.New(GuideModuleName.ComposeStuffListView)
	self.compose_equip_once_view = NewComposeEquipmentOnceView.New()
	self:RegisterAllProtocls()

	self.equip_data_change = BindTool.Bind(self.OnEquipDataChange, self)
	EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_data_change)

	self.datachange_callback = BindTool.Bind(self.OnItemDataChange2, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)

	self.role_attr_data_change = BindTool.Bind1(self.RoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_attr_data_change, {"level"})
end

function ComposeWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.stuff_list_view:DeleteMe()
	self.stuff_list_view = nil

	self.compose_equip_once_view:DeleteMe()
	self.compose_equip_once_view = nil

	if self.equip_data_change then
		EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_data_change)
		self.equip_data_change = nil
	end
	if self.datachange_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
		self.datachange_callback = nil
	end

	if self.role_attr_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_attr_data_change)
		self.role_attr_data_change = nil
	end

	ComposeWGCtrl.Instance = nil
end

-- 注册合成协议
function ComposeWGCtrl:RegisterAllProtocls()
	self:RegisterProtocol(CSItemCompose)
	self:RegisterProtocol(CSXianqStuffCompose)
	self:RegisterProtocol(SCItemComposeResult,"OnItemComposeResult")
end

function ComposeWGCtrl:FlushEquipComposeViewItemChange()
	if self.view == nil or not self.view:IsOpen() then
		return
	end

	local index = self:GetShowIndex()
	if index == COMPOSE_TYPE.EQ_HECHENG or index == COMPOSE_TYPE.EQ_HECHENG_TWO
	or index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		self.view:Flush(index, "equip_compose_item_change")
	end
end

function ComposeWGCtrl:OnItemComposeResult(protocol)
	local index = self:GetShowIndex()
	self.view:FlushAddBtnText()
	self:FlushEquipComposeViewItemChange()
	if protocol.result == 1 then --成功
		if index == COMPOSE_TYPE.EQ_HECHENG or index == COMPOSE_TYPE.EQ_HECHENG_TWO
			or index == COMPOSE_TYPE.EQ_HECHENG_THREE or index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			self.view:CreateEquipSucessEffect()

		elseif index == COMPOSE_TYPE.DAOJU
			or index == COMPOSE_TYPE.TOAZHUANG
			or index == COMPOSE_TYPE.Tian_Shen or index == COMPOSE_TYPE.XIAO_GUI2
			or index == COMPOSE_TYPE.LongHun or index == COMPOSE_TYPE.SHEN_BING 
			or index == COMPOSE_TYPE.BEAST then
			self.view:ShowEffect()
			self.view:OnItemComposeResult()

			if EquipmentWGData.GetXiaoGuiCfg(protocol.target_item_id) then
				if ViewManager.Instance:IsOpen(GuideModuleName.GuardComposeView) then
					ViewManager.Instance:Close(GuideModuleName.GuardComposeView)
				end

				-- 重复恭喜获得特殊处理
				if RoleWGData.GetRolePlayerPrefsInt("guard_new" .. protocol.target_item_id) ~= 1 then
					RoleWGData.SetRolePlayerPrefsInt("guard_new" .. protocol.target_item_id, 1)
					local app_protocol = {appe_image_id = protocol.target_item_id, appe_type = ROLE_APPE_TYPE.IMP,}
					AppearanceWGCtrl.Instance:OnGetNewAppearance(app_protocol)
				end
			end
		elseif index == TabIndex.other_xianqi_stuff then
			self.view:ShowXQSCEffect()
		end



	elseif protocol.result == 0 then --失败
		self.view:CreateFailingEffect(index)
		TipWGCtrl.Instance:ShowSystemMsg(Language.Compose.ComposeLost)
	end
end

function ComposeWGCtrl:JumpToFlush(index)
	if self.view:IsOpen() then
		self.view:ChangeToIndex(index)
	end
end

-- 发送合成请求
function ComposeWGCtrl:SendComposeReq(curr_seq_id, num, compose_type, compose_id,is_auto_buy)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSItemCompose)
	send_protocol.product_seq = curr_seq_id
	send_protocol.num = num
	send_protocol.compose_type = compose_type or 0			--0普通合成， 1仙玉兑换
	send_protocol.target_compose_id = compose_id or 0
	send_protocol.is_auto_buy = is_auto_buy or 0
	send_protocol:EncodeAndSend()
end

function ComposeWGCtrl:OnItemDataChange()
	if self.view:IsOpen() then
		self.view:OnItemDataChange()
	end
end

function ComposeWGCtrl:OnItemDataChange2(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if LongHunWGData.Instance:IsLongHunCoseItem(change_item_id) then
		RemindManager.Instance:Fire(RemindName.Compose_LongHun)
	end
	if EquipmentWGData.GetXiaoGuiCfg(change_item_id) then
		RoleBagWGCtrl.Instance:FlushGuardComposeView()
	end
end

function ComposeWGCtrl:RoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" and value > old_value then
		self.data:FormatMenu()
		self.data:CreateSpecialComposeMenu()
		RemindManager.Instance:Fire(RemindName.Compose)
	end
end

function ComposeWGCtrl:Open(tab_index, param_t)
	self.view:Open(tab_index, param_t)
end

function ComposeWGCtrl:GetShowIndex()
	return self.view:GetShowIndex()
end

function ComposeWGCtrl:FlushView()

end

function ComposeWGCtrl:XianQiShengJieResult( result )
	if result == 1 then
		if self.view:IsOpen() then
			ViewManager.Instance:FlushView(GuideModuleName.Compose, TabIndex.other_compose_shengjie, nil,
			{other_compose_shengjie = true})
		end
	end
end

function ComposeWGCtrl:XianQiShengPinResult( result )
	if result == 1 then
		if self.view:IsOpen() then
			ViewManager.Instance:FlushView(GuideModuleName.Compose, TabIndex.other_compose_shengpin, nil,
			{other_compose_shengpin = true})
		end
	end
end

function ComposeWGCtrl:XianQiLianZhenResult( result )
	if result == 1 then
		if self.view:IsOpen() then
			ViewManager.Instance:FlushView(GuideModuleName.Compose, TabIndex.other_compose_zhenlian, nil,
			{other_compose_zhenlian = true})
		end
	end
end

function ComposeWGCtrl:XianQiShengXingResult( result )
	if result == 1 then
		if self.view:IsOpen() then
			ViewManager.Instance:FlushView(GuideModuleName.Compose, TabIndex.other_compose_shengxing, nil,
			{other_compose_shengxing = true})
		end
	end
end

function ComposeWGCtrl:OnEquipDataChange(change_item_id, change_item_index, change_reason)
	RemindManager.Instance:Fire(RemindName.Compose_XianQi_ShengJie)
	RemindManager.Instance:Fire(RemindName.Compose_XianQi_ShengXing)
end

function ComposeWGCtrl:LongHunComposeResult(result)
	if result > 0 then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.other_compose_longhu,"longhun_compose_success")
		end
	end
end

function ComposeWGCtrl:OpenComposeStuffListView(stuff_list, grid_item, call_back)
	self.stuff_list_view:SetDataAndOpen(stuff_list, grid_item, call_back)
end

-- 发送合成仙器材料请求
function ComposeWGCtrl:SendComposXQSeReq(product_seq, product_id, stuff_list)
	-- print_error("【----发送合成仙器材料请求-----】：", product_seq, product_id, stuff_list)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSXianqStuffCompose)
	send_protocol.product_seq = product_seq or 0
	send_protocol.product_id = product_id or 0
	send_protocol.stuff_list = stuff_list or {}

	send_protocol:EncodeAndSend()
end


function ComposeWGCtrl:OnShenShouItemChange()
	RemindManager.Instance:Fire(RemindName.Compose_ShenShou_Equip)

	if self.view:IsOpen() then
		ViewManager.Instance:FlushView(GuideModuleName.Compose, TabIndex.other_compose_eq_hecheng_shenshou, nil, {shenshou_item_change = true})
	end
end

function ComposeWGCtrl:OpenCompseEquipOnceView(item_id, compose_equip_best_attr_num)
	local data = {
		item_id = item_id,
		compose_equip_best_attr_num = compose_equip_best_attr_num,
	}

	self.compose_equip_once_view:SetShowDataAndOpen(data)
end