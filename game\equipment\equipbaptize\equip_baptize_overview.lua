EquipmentBaptizeOverview = EquipmentBaptizeOverview or BaseClass(SafeBaseView)

function EquipmentBaptizeOverview:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 14),sizeDelta = Vector2(1080, 614)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_baptize_overview")
end

function EquipmentBaptizeOverview:LoadCallBack()
    if not self.baptize_equip_body_list then
        self.baptize_equip_body_list = AsyncListView.New(BPTEquipBodyOverviewItemCellRender, self.node_list.baptize_equip_body_list)
    end

    self.node_list.title_view_name.text.text = Language.Equip.BAPtizeOverviewTitle
end

function EquipmentBaptizeOverview:ReleaseCallBack()
    if self.baptize_equip_body_list then
        self.baptize_equip_body_list:DeleteMe()
        self.baptize_equip_body_list = nil
    end
end

function EquipmentBaptizeOverview:OnFlush()
    local data_list = EquipBodyWGData.Instance:GetTotalEquipBodyDataList()
    self.baptize_equip_body_list:SetDataList(data_list)
end

-------------------------------BPTEquipBodyOverviewItemCellRender----------------------------
BPTEquipBodyOverviewItemCellRender = BPTEquipBodyOverviewItemCellRender or BaseClass(BaseRender)

function BPTEquipBodyOverviewItemCellRender:LoadCallBack()
	if not self.equip_qh_list then
	    self.equip_qh_list = {}
	    for part = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
	        self.equip_qh_list[part] = BPTEquipBodyOverviewEquipCellRender.New(self.node_list["item_" .. part])
	        self.equip_qh_list[part]:SetIndex(part)
            self.equip_qh_list[part]:SetClickCallBack(BindTool.Bind(self.OnClickBaptizeListCallBack, self))
	    end
    end

    self.default_select_part = -1
    self.equip_body_seq_cache = -1
    self.default_select_part_data = {}

    XUI.AddClickEventListener(self.node_list["btn_to_baptize"], BindTool.Bind(self.OnClickToBaptizeBtn, self)) 
    XUI.AddClickEventListener(self.node_list["btn_baptize"], BindTool.Bind(self.OnClickBaptizeBtn, self)) 
end

function BPTEquipBodyOverviewItemCellRender:__delete()
    if self.equip_qh_list then
        for k, v in pairs(self.equip_qh_list) do
            v:DeleteMe()
        end

        self.equip_qh_list = nil
    end

    self.default_select_part = nil
    self.equip_body_seq_cache = nil
    self.default_select_part_data = nil
end

function BPTEquipBodyOverviewItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.equip_body_seq_cache ~= self.data.seq then
        self.default_select_part = -1
        self.default_select_part_data = {}
    end

    self.equip_body_seq_cache = self.data.seq
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    self.node_list.baptize_cell:CustomSetActive(unlock and is_wear_equip)
    self.node_list.unlock:CustomSetActive(not unlock or not is_wear_equip)
    self.node_list.btn_baptize:CustomSetActive(unlock and is_wear_equip)
    self.node_list.name.text.text = self.data.name

    if unlock and is_wear_equip then
        local bpt_equip_data_list = EquipmentWGData.Instance:GetEquipBaptizeShowList(self.data.seq)
        for k,v in pairs(self.equip_qh_list) do
            v:SetData(bpt_equip_data_list[k])
        end

        self:SetDefaultSelect(bpt_equip_data_list)
    else
        self.node_list.desc_tip.text.text = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
    end
end

function BPTEquipBodyOverviewItemCellRender:SetDefaultSelect(equip_data_list)
    if self.default_select_part < 0 and not IsEmptyTable(equip_data_list) then
        for k, v in pairs(equip_data_list) do
            if not IsEmptyTable(v) then
                if v.item_id > 0 then
                    self:OnClickBaptizeListCallBack(self.equip_qh_list[k], true)
                    return
                end
            end
        end
    end
    
    self.node_list.desc_btn_baptize.text.text = string.format(Language.Equip.BAPtizeOverviewLevel, 0)
end

function BPTEquipBodyOverviewItemCellRender:OnClickBaptizeListCallBack(cell, is_default_select)
    if nil == cell or IsEmptyTable(cell.data) then
        return 
    end

    local equip_part = cell.index 
    local data = cell.data

    local baptize_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(data.index)
    self.node_list.desc_btn_baptize.text.text = string.format(Language.Equip.BAPtizeOverviewLevel, baptize_info.grade)

    if self.default_select_part == equip_part then
        if not is_default_select then
            TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
        end
    end

    self:SetSelectEquip(cell.index)
    self.default_select_part = equip_part
    self.default_select_part_data = data
end

function BPTEquipBodyOverviewItemCellRender:SetSelectEquip(equip_part)
    if self.equip_qh_list then
        for k, v in pairs(self.equip_qh_list) do
            v:OnSelectChange(v.index == equip_part)
        end
    end
end

function BPTEquipBodyOverviewItemCellRender:OnClickToBaptizeBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if not unlock or not is_wear_equip then
        local str = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    if unlock and is_wear_equip then
        EquipmentWGCtrl.Instance:Flush(TabIndex.equipment_xilian, "baptize_change_to_equip_body", {equip_body_seq = self.data.seq, selct_part_data = self.default_select_part_data})
        EquipmentWGCtrl.Instance:CloseBaptizeOverviewView()
    end
end

function BPTEquipBodyOverviewItemCellRender:OnClickBaptizeBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if not unlock or not is_wear_equip then
        local str = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    EquipmentWGCtrl.Instance:SetDataAndIOpenBaptizeAddView(self.default_select_part_data)
end

-------------------------------BPTEquipBodyOverviewEquipCellRender----------------------------
BPTEquipBodyOverviewEquipCellRender = BPTEquipBodyOverviewEquipCellRender or BaseClass(BaseRender)

function BPTEquipBodyOverviewEquipCellRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_node)
	self.item_cell:SetIsShowTips(false)

    self.slot_list = {}
	for i = 1, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM do
		self.slot_list[i] = self.node_list["Img_" .. i]
	end

	self.node_list.select_img:SetActive(false)
    -- self.node_list.block_click:SetActive(false)
    self.node_list.remind:SetActive(false)
	self:OnSelectChange(false)
	XUI.AddClickEventListener(self.node_list.block_click, BindTool.Bind(self.OnClick, self))
end

function BPTEquipBodyOverviewEquipCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

    self.slot_list = nil
end

function BPTEquipBodyOverviewEquipCellRender:OnSelectChange(is_select)
    if self.node_list.select_img then
        self.node_list.select_img:CustomSetActive(is_select)
    end
end

function BPTEquipBodyOverviewEquipCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.item_cell:ClearData()
		self.item_cell:SetItemIcon(ResPath.GetEquipIcon(self.index))
		self:NoDataFlush()
		return
	end

	self.item_cell:SetData(self.data)

    local baptize_part_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(self.data.index)
    if not IsEmptyTable(baptize_part_info) then
        local target_grade = EquipmentWGData.Instance:GetBaptizeTargetGrade(self.data.index)
        local str = Language.Equipment.BaptizeGradeColorName[target_grade] or ""
        -- local str = Language.Equipment.BaptizeGradeColorName[baptize_part_info.grade] or ""
        str = string.format("<size=20>%s</size>", str)
        self.item_cell:SetRightTopImageText(str)
    end

	self:OtherFlush()
end

function BPTEquipBodyOverviewEquipCellRender:OtherFlush()
	local part_is_open, need_level = EquipmentWGData.Instance:IsEquipBaptizePartOpen(self.data.index)
	self.node_list.limit_text:SetActive(not part_is_open)
	self.node_list.quality_list:SetActive(part_is_open)

	if not part_is_open then
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(need_level)
		local need_str = is_vis and Language.Common.DianFengLevelOpen or Language.Common.LevelOpen1
		local limit_desc = string.format(need_str, role_level)
		self.node_list.limit_text.text.text = limit_desc
		self.node_list.remind:SetActive(false)
		return
	end

	local slot_infos = EquipmentWGData.Instance:GetBaptizeSlotInfoByEquipBodyIndex(self.data.index)

	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		local data = slot_infos and slot_infos[i] or nil
		if data then
			local bundle, asset = ResPath.GetEquipmentIcon("a3_lq_xq" .. (data.quality))
			self.slot_list[i + 1].image:LoadSprite(bundle, asset)
			self.slot_list[i + 1]:SetActive(data.is_buy == 1)
		else
			self.slot_list[i + 1]:SetActive(false)
		end
	end
end

function BPTEquipBodyOverviewEquipCellRender:NoDataFlush()
    self:OnSelectChange(false)
    self.node_list.limit_text:SetActive(false)
	self.node_list.quality_list:SetActive(false)
	self.node_list.remind:SetActive(false)
end