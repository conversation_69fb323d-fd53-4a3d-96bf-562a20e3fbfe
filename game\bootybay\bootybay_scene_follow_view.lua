--天帝陵
BootyBaySceneFollowView = BootyBaySceneFollowView or BaseClass(SafeBaseView)

BootyBaySceneFollowView.MaxColor = 4

function BootyBaySceneFollowView:__init()
	self:AddViewResource(0, "uis/view/bootybay_ui_prefab", "layout_bootybay_follow_view")
	self.view_layer = UiLayer.MainUILow
	self.open_tween = nil
	self.close_tween = nil
    self.active_close = false
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
end

function BootyBaySceneFollowView:__delete()
	if self.stuff_cell_list then
		for k,v in pairs(self.stuff_cell_list) do
			v:DeleteMe()
		end
		self.stuff_cell_list = nil
	end

	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function BootyBaySceneFollowView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == GuideUIName.BtnBootybayGoto then
        return self.node_list["go_btn"], BindTool.Bind1(self.OnClickGoBtn, self)
    end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end


function BootyBaySceneFollowView:LoadCallBack()
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.BootyBaySceneFollowView, self.get_guide_ui_event)
	self.reward_item = ItemCell.New(self.node_list.reward_item)
	self.stuff_cell_list = {}

	for i=1, BootyBaySceneFollowView.MaxColor do
		self.stuff_cell_list[i] = ItemCell.New(self.node_list["item_cell_"..i])
		self.stuff_cell_list[i]:SetIsShowTips(false)
		self.stuff_cell_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickBaoTuCallBack,self,i))
	end

	self.node_list.go_btn.button:AddClickListener(BindTool.Bind(self.OnClickGoBtn,self))
	self.node_list.exit_btn.button:AddClickListener(BindTool.Bind(self.OnClickExitBtn,self))

	if nil == self.item_change_callback then
		self.item_change_callback = BindTool.Bind(self.ItemChangeCallBack,self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
	end

	self.node_list.finish_bg:SetActive(false)

	self.obj = self.node_list.panle_bg.gameObject
    self.out_obj = self.node_list.exit_btn.gameObject
    local is_accepted = BootyBayWGData.Instance:GetSCWabaoInfo()
    if is_accepted ~= 1 then --没接取任务才提示
        FunctionGuide.Instance:TriggerGuideByGuideName("bootybaygoto")  --因为FunctionGuide监听事件有延迟，断线重连会导致没有监听到这个界面打开，所以主动触发
    end
end

-- 切换标签调用
function BootyBaySceneFollowView:ShowIndexCallBack()
	self.obj:SetActive(true)
	self.out_obj:SetActive(true)
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
	end
end

function BootyBaySceneFollowView:InitCallBack()
    local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
    self.obj.transform:SetParent(parent.gameObject.transform, false)
    self.obj.transform.anchoredPosition = Vector3(1, -2)
    MainuiWGCtrl.Instance:SetBtnLevel(false)

	MainuiWGCtrl.Instance:AddBtnToFbRightLineGroup(self.out_obj)
	MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
	self:FlsuhStuffNum()
	self:Flush()
end

function BootyBaySceneFollowView:ReleaseCallBack()
	if self.stuff_cell_list then
		for k,v in pairs(self.stuff_cell_list) do
			v:DeleteMe()
		end
		self.stuff_cell_list = nil
	end

	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
	self.item_change_callback = nil

	if self.obj then
		ResMgr:Destroy(self.obj)
		self.obj = nil
	end

	if self.out_obj then
		ResMgr:Destroy(self.out_obj)
		self.out_obj = nil
	end
    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.BootyBaySceneFollowView, self.get_guide_ui_event)
    self.get_guide_ui_event = nil
end

function BootyBaySceneFollowView:OnClickBaoTuCallBack(index)
	local data = self.stuff_cell_list[index]:GetData()
	if not data then
		return
	end
	
	BootyBayWGCtrl.Instance:SendBaoTuWaBao(data, data.quality == BootyBaySceneFollowView.MaxColor)
end

function BootyBaySceneFollowView:OnFlush()

	local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local is_accepted = BootyBayWGData.Instance:GetSCWabaoInfo()
	local times = is_accepted == 1 and wabao_times or 10
	if is_accepted == 1 then
		if other_cfg.task_wabao_times - times <= 0 then
			self.node_list.task_panel:SetActive(false)
			self.node_list.finish_bg:SetActive(true)
		else
			self.node_list.task_panel:SetActive(true)
			self.node_list.finish_bg:SetActive(false)
			self.node_list.task_value.text.text = string.format(Language.BootyBay.TaskDesc2, times, other_cfg.task_wabao_times)
		end
	else
		self.node_list.task_panel:SetActive(true)
		self.node_list.finish_bg:SetActive(false)
		self.node_list.task_value.text.text = string.format(Language.BootyBay.TaskDesc1, is_accepted)
	end
    self.reward_item:SetData(other_cfg.renwuzhanshi)
    
    self:FlushBaotuEffect()
end

function BootyBaySceneFollowView:FlushBaotuEffect()
	local color = 0
    local use_baotu_info = BootyBayWGData.Instance:GetUseBaoTuInfo()
    if not IsEmptyTable(use_baotu_info) then
		color = use_baotu_info.quality
    end

	-- self.node_list.hong_effect:SetActive(color == 4)
	-- self.node_list.chengse_effect:SetActive(color == 3)
    -- self.node_list.zise_effect:SetActive(color == 2)
end

function BootyBaySceneFollowView:FlsuhStuffNum()
	local stuff_list = BootyBayWGData.Instance:GetBootyBayStuffList()
	for k,v in pairs(self.stuff_cell_list) do
		if stuff_list[k] then
			local stuff_num = ItemWGData.Instance:GetItemNumInBagById(stuff_list[k].baotu_item) or 0
			self.stuff_cell_list[k]:SetData({item_id = stuff_list[k].baotu_item, quality = stuff_list[k].color})
			self.stuff_cell_list[k]:SetRightBottomTextVisible(true)
			self.stuff_cell_list[k]:SetRightBottomColorText(stuff_num)
			self.node_list["item_cell_"..k]:SetActive(stuff_list[k].is_show == 1)
		else
			self.node_list["item_cell_"..k]:SetActive(false)
		end
	end
end

-- 关闭前调用
function BootyBaySceneFollowView:CloseCallBack()
	if self.obj then
		self.obj:SetActive(false)
	end

	if self.out_obj then
		self.out_obj:SetActive(false)
	end
	
end

function BootyBaySceneFollowView:ItemChangeCallBack()
	self:FlsuhStuffNum()
end

function BootyBaySceneFollowView:OnClickGoBtn()
	BootyBayWGCtrl.Instance:SendTaskWaBao()
end


function BootyBaySceneFollowView:OnClickExitBtn()
	-- GuajiWGCtrl.Instance:FlyToScene(103, true)
	BootyBayWGCtrl.Instance:SetAutoWaBaoFlag(false)
	BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_LEAVE_WABAO_SCENE)
end
