SortTools = {}

-- 升序排列函数，如果参数为空直接比较，参数不为空比较表项，优先级依次递减
function SortTools.AscFunc(...)
	local params = {...}
	local count = #params
	if 0 == count then
		return function(a, b)
			return a < b
		end
	elseif 1 == count then
		local key = params[1]
        return function(a, b)
            return a[key] < b[key]
        end
	end

	return function(a, b)
		for _, v in ipairs(params) do
			if a[v] < b[v] then
				return true
			elseif a[v] > b[v] then
				return false
			end
		end
		return false
	end
end

-- 升序排列
function SortTools.SortAsc(t, ...)
	table.sort(t, SortTools.AscFunc(...))
end

-- 降序排列函数，如果参数为空直接比较，参数不为空比较表项，优先级依次递减
function SortTools.DescFunc(...)
	local params = {...}
	local count = #params
	if 0 == count then
		return function(a, b)
			return a > b
		end
	elseif 1 == count then
		local key = params[1]
        return function(a, b)
            return a[key] > b[key]
        end
	end

	return function(a, b)
		for _, v in ipairs(params) do
			if a[v] > b[v] then
				return true
			elseif a[v] < b[v] then
				return false
			end
		end
		return false
	end
end

-- 降序排列
function SortTools.SortDesc(t, ...)
	table.sort(t, SortTools.DescFunc(...))
end

--[[
从小到大排序的算子(用于表项)
@para1 sort_key_name 需要比较的表项中的key
@para2 sort_key_name2 第一个参数相同的情况下比较此key
--]]
function SortTools.KeyLowerSorter(sort_key_name, sort_key_name2)
	return function(a, b)
		if a[sort_key_name] < b[sort_key_name] then
			return true
		elseif a[sort_key_name] == b[sort_key_name] and nil ~= sort_key_name2 then
			return a[sort_key_name2] < b[sort_key_name2]
		else
			return false
		end
	end
end

--[[
从大到小排序的算子(用于表项)
@para2 sort_key_name 需要比较的表项中的key
--]]
function SortTools.KeyUpperSorter(sort_key_name)
	return function(a, b)
		return a[sort_key_name] > b[sort_key_name]
	end
end

--[[
	从大到小排序的算子(用于表项)
--]]
function SortTools.KeyUpperSorters(...)
	local sort_keys = {...}
    local weights = {100000, 10000, 1000, 100, 10, 1}

    return function(a, b)
        local order_a, order_b = 1000000, 1000000

        if not a or not b then
            return order_a > order_b
        end

        for i = 1, #sort_keys do
            local key = sort_keys[i]
            local weight = weights[i]

            if not key then
                break
            end

            if a[key] > b[key] then
                order_a = order_a + weight
            elseif a[key] < b[key] then
                order_b = order_b + weight
            end
        end

        return order_a > order_b
    end
end

function SortTools.KeyLowerSorters(...)
    local sort_keys = {...}
    local weights = {100000, 10000, 1000, 100, 10, 1}

    return function(a, b)
        local order_a, order_b = 1000000, 1000000

        for i = 1, #sort_keys do
            local key = sort_keys[i]
            local weight = weights[i]

            if not key then
                break
            end

            if a[key] > b[key] then
                order_a = order_a + weight
            elseif a[key] < b[key] then
                order_b = order_b + weight
            end
        end

        return order_a < order_b
    end
end

--天书排序
function SortTools.KeyTianShuSorters(sort_key_name1, sort_key_name2, sort_key_name3)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] < b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] > b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a > order_b end

		if a[sort_key_name3] < b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] > b[sort_key_name3] then
			order_b = order_b + 100
		end
		return order_a > order_b
	end
end

--帮派成员排序第一个参数倒序后面正序
function SortTools.KeyGuildMemberSorters(sort_key_name1, sort_key_name2, sort_key_name3, sort_key_name4)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] < b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] > b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] > b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] < b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a > order_b end

		if a[sort_key_name3] > b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] < b[sort_key_name3] then
			order_b = order_b + 100
		end
		if nil == sort_key_name4 then  return order_a < order_b end

		if a[sort_key_name4] > b[sort_key_name4] then
			order_a = order_a + 10
		elseif a[sort_key_name4] < b[sort_key_name4] then
			order_b = order_b + 10
		end

		return order_a > order_b
	end
end

--结婚面板邀请宾客列表 升 降 升
function SortTools.KeyMarrySorters(sort_key_name1, sort_key_name2, sort_key_name3)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] < b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] > b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a > order_b end

		if a[sort_key_name3] > b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] < b[sort_key_name3] then
			order_b = order_b + 100
		end

		return order_a > order_b
	end
end

--结婚面板邀请宾客列表 升 升 降 升
function SortTools.KeyMarryTwoSorters(sort_key_name1, sort_key_name2, sort_key_name3, sort_key_name4)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] > b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] < b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a > order_b end

		if a[sort_key_name3] < b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] > b[sort_key_name3] then
			order_b = order_b + 100
		end

		if nil == sort_key_name4 then  return order_a > order_b end

		if a[sort_key_name4] > b[sort_key_name4] then
			order_a = order_a + 10
		elseif a[sort_key_name4] < b[sort_key_name4] then
			order_b = order_b + 10
		end

		-- if nil == sort_key_name5 then  return order_a > order_b end

		-- if a[sort_key_name5] > b[sort_key_name5] then
		-- 	order_a = order_a + 1
		-- elseif a[sort_key_name5] < b[sort_key_name5] then
		-- 	order_b = order_b + 1
		-- end

		return order_a > order_b
	end
end

--必做任务列表 降 升 升
function SortTools.KeyBiZuoSorters(sort_key_name1, sort_key_name2, sort_key_name3)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] < b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] > b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] > b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] < b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a > order_b end

		if a[sort_key_name3] > b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] < b[sort_key_name3] then
			order_b = order_b + 100
		end

		return order_a > order_b
	end
end