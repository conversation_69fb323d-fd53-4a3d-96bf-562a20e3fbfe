﻿using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Mirror2))]
public class Mirror2Editor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        if (GUILayout.But<PERSON>("Take Texture"))
        {
            Mirror2 mirror = target as Mirror2;
            var filePath = EditorUtility.SaveFilePanel("Save File...", Application.dataPath, "ReflectionTexture", "png");
            mirror.TakeTexture(filePath);
            AssetDatabase.Refresh();
        }
    }
}
