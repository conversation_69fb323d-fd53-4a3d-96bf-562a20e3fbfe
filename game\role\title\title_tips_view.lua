------------------------------------------------------------
--人物相关主View
------------------------------------------------------------

local ATTR_COUNT = 13
local MAX_DISPLAY_NUM = 9
TitleTipsView = TitleTipsView or BaseClass(SafeBaseView)

function TitleTipsView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "TitleTipsView"
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_attr_tip")
end

function TitleTipsView:LoadCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}
	for i=1,ATTR_COUNT do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
		if self.attr_list[i] then
			self.attr_name_list[i] = self.attr_list[i]:FindObj("attr_name")
			self.attr_value_list[i] = self.attr_list[i]:FindObj("attr_value")
		end
	end
	self:Flush()
end

function TitleTipsView:ReleaseCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}
end

function TitleTipsView:OnFlush()
	self.node_list.attr_title_name.text.text = Language.Title.TitleAttrName

	local data_id_list = TitleWGData.Instance:GetTitleIdList()   -- 激活列表
	local data_list = TitleWGData.Instance:GetAllTitleConfig()   -- 所有称号列表
	local data = AttributePool.AllocAttribute()
	local sort_list = AttributeMgr.SortAttribute()
	for i,v in ipairs(data_id_list) do --遍历ID List
		for n, m in ipairs(data_list) do --遍历所有称号列表
			if m.title_id == v then
				local attribute2 = TitleWGData.Instance:GetTitleAttrInfo(m)
				data = AttributeMgr.AddAttributeAttr(data, attribute2)
			end
		end
	end
	local attr_name = Language.Common.AttrName
	local index = 1
	for i,v in ipairs(sort_list) do
		if self.attr_list[index] then
			if data[v] ~= nil and data[v] > 0 then
				self.attr_name_list[index].text.text = attr_name[v]
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
					self.attr_value_list[index].text.text = "+" .. (data[v]/100).."%"
				else
					self.attr_value_list[index].text.text = "+" .. data[v]
				end
				index = index + 1
			end
		end
	end

    if index < MAX_DISPLAY_NUM then
        self.node_list.attr_scroll.scroll_rect.enabled = false
    else
        self.node_list.attr_scroll.scroll_rect.enabled = true
    end
	for i = 1, ATTR_COUNT do
		self.attr_list[i]:SetActive(i < index)
    end
end
