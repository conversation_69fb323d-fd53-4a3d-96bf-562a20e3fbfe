require("game/operation_activity/operation_activity_leichong_recharge/activity_leichong_recharge_wg_data")

OperationLeiChongRechargeWGCtrl = OperationLeiChongRechargeWGCtrl or BaseClass(BaseWGCtrl)
function OperationLeiChongRechargeWGCtrl:__init()
	if OperationLeiChongRechargeWGCtrl.Instance then
		ErrorLog("[OperationLeiChongRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	OperationLeiChongRechargeWGCtrl.Instance = self
	self.data = OperationLeiChongRechargeWGData.New()
	self:RegisterAllProtocols()
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate(OperationLeiChongRechargeWGData.ConfigPath, BindTool.Bind(self.OnHotUpdate, self))
end

function OperationLeiChongRechargeWGCtrl:__delete()
	OperationLeiChongRechargeWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
	if self.LCdelay_timer then
		GlobalTimerQuest:CancelQuest(self.LCdelay_timer)
		self.LCdelay_timer = nil
	end
end

function OperationLeiChongRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOALimitRecharge, "OnSCOALimitRecharge")
	self:RegisterProtocol(CSOALimitRechargeOpera)
end

function OperationLeiChongRechargeWGCtrl:OnSCOALimitRecharge(protocol)
	self.data:SetLeiChongRechargeData(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_total_recharge)
	RemindManager.Instance:Fire(RemindName.OperationTotalRecharge)
	
end

function OperationLeiChongRechargeWGCtrl:SendLeiChongRechargeReq(type, param1)

local protocol = ProtocolPool.Instance:GetProtocol(CSOALimitRechargeOpera)
	protocol.type = type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()

end

function OperationLeiChongRechargeWGCtrl:OnPassDay()
		--做个容错，跨天的时候，服务器的数据可能还没同步过来
	self.LCdelay_timer = GlobalTimerQuest:AddDelayTimer(function()
		OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_total_recharge)
		self.LCdelay_timer = nil
	end, 1)
end

--热更
function OperationLeiChongRechargeWGCtrl:OnHotUpdate()
	self.data:InitLeiChongRechargeData()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_total_recharge)
end
