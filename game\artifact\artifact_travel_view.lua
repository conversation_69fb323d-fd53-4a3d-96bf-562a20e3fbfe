---------------------------------------------
-- 双修-同游
---------------------------------------------
ArtifactTravelView = ArtifactTravelView or BaseClass(SafeBaseView)

local timer_key = "artifact_travel_count_down"

function ArtifactTravelView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self.is_safe_area_adapter = true
	
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_travel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function ArtifactTravelView:__delete()

end

function ArtifactTravelView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Artifact.TravelViewName

	self.node_list["txt_travel_reward_tips"].text.text = Language.Artifact.TravelRewardTips
	
	local bundle, asset = ResPath.GetRawImagesPNG("a3_sxhg_ty_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
		show_gold = true, show_bind_gold = true,
		show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.artifact_list then
		self.artifact_list = AsyncListView.New(ArtifactTravelCell, self.node_list["artifact_list"])
		self.artifact_list:SetSelectCallBack(BindTool.Bind(self.OnArtifactCellHandler, self))
	end

	if not self.travel_scene_list then
		self.travel_scene_list = AsyncListView.New(ArtifactSceneItem, self.node_list["scene_list"])
		self.travel_scene_list:SetSelectCallBack(BindTool.Bind(self.OnSceneSelectHandler, self))
	end

	if not self.travel_reward_list then
		self.travel_reward_list = AsyncListView.New(ArtifactTravelRewardItem, self.node_list["travel_reward_list"])
		self.travel_reward_list:SetStartZeroIndex(true)
	end

	if not self.travel_reward_item_list then
		self.travel_reward_item_list = AsyncListView.New(ItemCell, self.node_list["travel_reward_item_list"])
		self.travel_reward_item_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list["btn_travel"], BindTool.Bind1(self.OnClickTravelBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_cost_item"], BindTool.Bind1(self.OnClickTravelCostItemBtn, self))

	self.select_artifact_seq = nil
	self.select_artifact_index = nil
	self.select_scene_seq = nil

	self.is_loaded = true
	self.node_list["anim_root"].animation_player:Play("OpenAnim")
	self:DoRewardItemCellsAnim()
	self:DoSceneItemCellsAnim()
end

function ArtifactTravelView:ReleaseCallBack()
	if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

	if self.artifact_list then
		self.artifact_list:DeleteMe()
		self.artifact_list = nil
	end

	if self.travel_scene_list then
		self.travel_scene_list:DeleteMe()
		self.travel_scene_list = nil
	end

	if self.travel_reward_list then
		self.travel_reward_list:DeleteMe()
		self.travel_reward_list = nil
	end

	if self.travel_reward_item_list then
		self.travel_reward_item_list:DeleteMe()
		self.travel_reward_item_list = nil
	end

	self.select_artifact_seq = nil
	self.select_artifact_index = nil
	self.select_scene_seq = nil
	self.jump_sel_seq = nil
	self:CleanTravelCountDownTimer()
	
	self.is_loaded = nil
	ArtifactWGData.Instance:SetTravelSelectArtifactSeq(nil)
end

function ArtifactTravelView:OpenCallBack()
	if self.is_loaded then
		self.node_list["anim_root"].animation_player:Play("OpenAnim")
		self:DoSceneItemCellsAnim()
		self:DoRewardItemCellsAnim()
	end
end

function ArtifactTravelView:CloseCallBack()

end

function ArtifactTravelView:LoadIndexCallBack(index)

end

function ArtifactTravelView:ShowIndexCallBack(index)

end

function ArtifactTravelView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.sel_seq then
				self.jump_sel_seq = v.sel_seq
			end
			self:FlushArtifactList()
		elseif k == "data_flush" then
			self:FlushArtifactList(true)
			self:FlushSceneList(true)
			self:FlushTravelRewardPart()
		end
	end
end

-- 刷新列表
function ArtifactTravelView:FlushArtifactList(is_data_flush)
	local artifact_list_info = ArtifactWGData.Instance:GetArtifactCfgList()
	self.select_artifact_index = self.select_artifact_index or 1
	if self.jump_sel_seq then
		self.select_artifact_index = self.jump_sel_seq + 1
	end
	self.artifact_list:SetDataList(artifact_list_info)
	if not is_data_flush or not self.select_artifact_seq or self.jump_sel_seq then
		self.artifact_list:JumpToIndex(self.select_artifact_index, 4)
	end
	self.jump_sel_seq = nil
end

-- 刷新地图列表
function ArtifactTravelView:FlushSceneList(is_data_flush)
	if not self.select_artifact_seq then
		return
	end
	local travel_cfg_list = ArtifactWGData.Instance:GetTravelCfgList()
	self.travel_scene_list:SetDataList(travel_cfg_list)
	if not is_data_flush or not self.select_scene_seq then
		self.travel_scene_list:JumpToIndex(1)
	end
end

-- 刷新同游奖励部分
function ArtifactTravelView:FlushTravelRewardPart()
	if not self.select_artifact_seq or not self.select_scene_seq then
		return
	end

	local travel_reward_cfg_list = ArtifactWGData.Instance:GetTravelRewardCfgList(self.select_scene_seq)
	self.travel_reward_list:SetDataList(travel_reward_cfg_list)

	local travel_info = ArtifactWGData.Instance:GetArtifactTravelInfo(self.select_artifact_seq)
	if not travel_info then
		return
	end

	local btn_text_str = Language.Artifact.TravelBtnText[2]
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local can_get_reward = travel_info.scene_seq == self.select_scene_seq and travel_info.fetch_falg == 1-- travel_info.end_time <= server_time
	self.node_list["travel_reward_part"]:SetActive(can_get_reward)
	self.node_list["remind_btn_travel"]:SetActive(can_get_reward)
	-- 可领取
	if can_get_reward then
		local travel_reward_cfg = ArtifactWGData.Instance:GetTravelRewardCfg(self.select_scene_seq, travel_info.reward_seq)
		self.travel_reward_item_list:SetDataList(travel_reward_cfg.reward_item)
		btn_text_str = Language.Artifact.TravelBtnText[1]
	end
	-- 可查看
	local is_in_travel = travel_info.scene_seq == self.select_scene_seq and travel_info.fetch_falg == 0 --travel_info.end_time > server_time
	self.node_list["txt_travel_limit_time"]:SetActive(is_in_travel)
	self:CleanTravelCountDownTimer()
	if is_in_travel and travel_info.end_time > server_time then
		btn_text_str = Language.Artifact.TravelBtnText[3]
		-- 倒计时
		local time_str = string.format(Language.Artifact.LimitTravelTime, TimeUtil.FormatSecondDHM9(travel_info.end_time - server_time))
		self.node_list["txt_travel_limit_time"].text.text = time_str
		CountDownManager.Instance:AddCountDown(timer_key, 
			BindTool.Bind1(self.UpdateGradeCountDown, self), 
			BindTool.Bind1(self.CleanTravelCountDownTimer, self),  travel_info.end_time, nil, 1)
	end
	self.node_list["txt_btn_travel_text"].text.text = btn_text_str

	-- 消耗
	local show_cost = not can_get_reward and not is_in_travel
	self.node_list["txt_travel_cost"]:SetActive(show_cost)
	if show_cost then
		local travel_scene_cfg = ArtifactWGData.Instance:GetTravelSceneCfg(self.select_scene_seq)
		local item_icon = ItemWGData.Instance:GetItemIconByItemId(travel_scene_cfg.consume_item) --拥有的数量
		local item_num = ItemWGData.Instance:GetItemNumInBagById(travel_scene_cfg.consume_item)
		local bundle, asset = ResPath.GetItem(item_icon)
		self.node_list["img_cost_item"].image:LoadSpriteAsync(bundle, asset)
		local color = item_num >= travel_scene_cfg.consume_num and COLOR3B.C2 or COLOR3B.C3
		local cost_str = string.format(Language.Artifact.CostNum, ToColorStr(item_num, color), travel_scene_cfg.consume_num)
		self.node_list["txt_travel_cost"].text.text = cost_str
	end
end

function ArtifactTravelView:OnArtifactCellHandler(item, cell_index)
	if nil == item or nil == item.data then
		return
	end

	if self.select_artifact_seq and self.select_artifact_seq == item.data.seq then
		return
	end

	self.select_artifact_seq = item.data.seq
	self.select_artifact_index = cell_index
	ArtifactWGData.Instance:SetTravelSelectArtifactSeq(self.select_artifact_seq)
	self:FlushSceneList()
end

function ArtifactTravelView:OnSceneSelectHandler(item, cell_index)
	if nil == item or nil == item.data then
		return
	end

	if self.select_scene_seq ~= nil and self.select_scene_seq ~= item.data.scene_seq then
		self.node_list["anim_root"].animation_player:Play("ShowPicture")
	end

	self.select_scene_seq = item.data.scene_seq

	self.node_list["txt_scene_name"].text.text = item.data.scene_name
	local bundle, asset = ResPath.GetRawImagesPNG("a3_sxhg_ty_dcj_" .. self.select_scene_seq)
	self.node_list["img_scene_big"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["img_scene_big"].raw_image:SetNativeSize()
	end)

	self:FlushTravelRewardPart()
	self:DoRewardItemCellsAnim()
end

function ArtifactTravelView:OnClickTravelBtn()
	if not self.select_artifact_seq or not self.select_scene_seq then
		return
	end
	local travel_info = ArtifactWGData.Instance:GetArtifactTravelInfo(self.select_artifact_seq)
	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	if not travel_info or not artifact_data then
		return
	end

	if artifact_data.level <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.NoActiveTips)
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local can_get_reward = travel_info.scene_seq == self.select_scene_seq and travel_info.end_time <= server_time
	if can_get_reward then
		ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.FETCH_TRAVEL_REWARD, self.select_artifact_seq)
		return
	end

	local is_in_travel = travel_info.scene_seq == self.select_scene_seq and travel_info.end_time > server_time
	if is_in_travel then
		ViewManager.Instance:Open(GuideModuleName.Map, TabIndex.global_map)
		return
	end

	-- 是否解锁
	local is_scene_unlock = ArtifactWGData.Instance:GetIsUnlockScene(self.select_artifact_seq, self.select_scene_seq)
	if not is_scene_unlock then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.MapLocked)
		return
	end

	-- 点击同游
	if travel_info.scene_seq >= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.InTravel)
		return
	end

	local travel_scene_cfg = ArtifactWGData.Instance:GetTravelSceneCfg(self.select_scene_seq)
	local cost_item_id = travel_scene_cfg.consume_item
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
	-- 道具不足
	if item_num < travel_scene_cfg.consume_num then
		local item_name = ItemWGData.Instance:GetItemName(cost_item_id, nil, true)
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Artifact.ItemNotEnough, item_name))
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cost_item_id})
		return
	end

	ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.TRAVEL, self.select_artifact_seq, self.select_scene_seq)
end

function ArtifactTravelView:OnClickTravelCostItemBtn()
	if not self.select_scene_seq then
		return
	end
	local travel_scene_cfg = ArtifactWGData.Instance:GetTravelSceneCfg(self.select_scene_seq)
	if travel_scene_cfg then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = travel_scene_cfg.consume_item})
	end
end

function ArtifactTravelView:UpdateGradeCountDown(elapse_time, total_time)
	self.node_list["txt_travel_limit_time"].text.text = string.format(Language.Artifact.LimitTravelTime, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
end

function ArtifactTravelView:DoSceneItemCellsAnim()
	local tween_info = UITween_CONSTS.ArtifactTravel.ListCellRender
	self.node_list["scene_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["scene_list"]:SetActive(true)
        local list =  self.travel_scene_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyItemAnim(count)
        end
    end, tween_info.DelayDoTime, "travel_scene_item_tween")
end

function ArtifactTravelView:DoRewardItemCellsAnim()
	local tween_info = UITween_CONSTS.ArtifactTravel.RewardListCellRender
	self.node_list["travel_reward_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["travel_reward_list"]:SetActive(true)
        local list =  self.travel_reward_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 <= v.index then
                count = count + 1
            end
            v.item:PalyItemAnim(count)
        end
    end, tween_info.DelayDoTime, "travel_reward_item_tween")
end

function ArtifactTravelView:CleanTravelCountDownTimer()
	if CountDownManager.Instance:HasCountDown(timer_key) then
		CountDownManager.Instance:RemoveCountDown(timer_key)
	end
end

---------------------------------------
-- 左侧列表Item
---------------------------------------
ArtifactTravelCell = ArtifactTravelCell or BaseClass(BaseRender)

function ArtifactTravelCell:OnFlush()
	if not self.data then
		return
	end

	local bundle, asset = ResPath.GetArtifactImg("a3_sxhg_ty_tx" .. self.data.seq)
	self.node_list["img_icon"].image:LoadSprite(bundle, asset, function()
		self.node_list["img_icon"].image:SetNativeSize()
	end)

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)
	local is_act = cur_artifact_data.level > 0
	self.node_list["img_locked"]:SetActive(not is_act)

	local remind = ArtifactWGData.Instance:GetArtifactTravelRemind(self.data.seq)
	self.node_list["remind"]:SetActive(remind)
end

function ArtifactTravelCell:OnSelectChange(is_select)
	self.node_list["select_hl"]:SetActive(is_select)
end

---------------------------------------
-- 场景选择列表Item
---------------------------------------
ArtifactSceneItem = ArtifactSceneItem or BaseClass(BaseRender)

function ArtifactSceneItem:__init()
end

function ArtifactSceneItem:LoadCallBack()

end

function ArtifactSceneItem:ReleaseCallBack()

end

function ArtifactSceneItem:OnFlush()
	if not self.data then
		return
	end
	
	local bundle, asset = ResPath.GetArtifactImg("a3_sxhg_ty_cj" .. self.data.scene_seq)
	self.node_list["img_scene_icon"].image:LoadSprite(bundle, asset, function()
		self.node_list["img_scene_icon"].image:SetNativeSize()
	end)

	self.node_list["txt_scene_name"].text.text = self.data.scene_name

	local artifact_seq = ArtifactWGData.Instance:GetTravelSelectArtifactSeq()
	if not artifact_seq then return end

	local is_unlock = ArtifactWGData.Instance:GetIsUnlockScene(artifact_seq, self.data.scene_seq)
	self.node_list["img_locked"]:SetActive(not is_unlock)

	local remind = ArtifactWGData.Instance:GetSceneRemind(artifact_seq, self.data.scene_seq)
	self.node_list["remind"]:SetActive(remind)
end

function ArtifactSceneItem:OnSelectChange(is_select)
	self.node_list["select_hl"]:SetActive(is_select)	
end

function ArtifactSceneItem:PalyItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.ArtifactTravel.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.MoveAlphaShow(GuideModuleName.ArtifactTravelView, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "travel_scene_item_" .. wait_index)
end

---------------------------------------
-- 同游奖励列表Item
---------------------------------------
ArtifactTravelRewardItem = ArtifactTravelRewardItem or BaseClass(BaseRender)

function ArtifactTravelRewardItem:__init()
end

function ArtifactTravelRewardItem:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end
end

function ArtifactTravelRewardItem:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function ArtifactTravelRewardItem:OnFlush()
	if not self.data then
		return
	end
	self.reward_list:SetDataList(self.data.reward_item)
	self.node_list["txt_scene_reward_desc"].text.text = self.data.reward_desc
	self.node_list["txt_affection_add_value"].text.text = string.format(Language.Artifact.AddAffection, self.data.add_favor)

	local artifact_seq = ArtifactWGData.Instance:GetTravelSelectArtifactSeq() or -1
	local travel_info = ArtifactWGData.Instance:GetArtifactTravelInfo(artifact_seq)
	if not travel_info then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	--local remind = travel_info.scene_seq == self.data.scene_seq and travel_info.reward_seq == self.data.seq and travel_info.end_time <= server_time
	local remind = travel_info.scene_seq == self.data.scene_seq and travel_info.reward_seq == self.data.seq and travel_info.fetch_falg == 1
	self.node_list["remind_hl"]:SetActive(remind)
end

function ArtifactTravelRewardItem:PalyItemAnim(item_index)
	if not self.node_list["tween_root"] then return end
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.ArtifactTravel.RewardListCellRender

	if self.move_alpha_tween then
		self.move_alpha_tween:Complete()
		self.move_alpha_tween = nil
	end

	UITween.FakeHideShow(self.node_list["tween_root"])
		ReDelayCall(self, function()
		self.move_alpha_tween = UITween.MoveAlphaShow(GuideModuleName.ArtifactTravelView, self.node_list["tween_root"].gameObject, tween_info, function()
			self.move_alpha_tween = nil
		end)
	end, tween_info.NextDoDelay * wait_index, "travel_reward_item_" .. wait_index)
end