﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

using System;
using HedgehogTeam.EasyTouch;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.SceneManagement;

public sealed class UIJoystick : MonoBehaviour
{
    [SerializeField]
    [Tooltip("摇杆最大半径, 以像素为单位")]
    private float radius = 80.0f;

    [SerializeField]
    [Tooltip("可显示物体的根节点")]
    private GameObject visibleRoot = null;

    [SerializeField]
    [Tooltip("移动的摇杆显示对象")]
    private RectTransform thumb = null;

    [SerializeField]
    [Tooltip("移动的摇杆背景")]
    private RectTransform bg = null;

    [SerializeField]
    [Tooltip("摇杆模式")]
    private JoystickMode mode = JoystickMode.Fixed;

    [SerializeField]
    [Tooltip("事件触发的间隔事件")]
    private float eventInterval = 0.1f;

    [SerializeField]
    [Tooltip("是否旋转背景图片")]
    private bool rotateBG = false;

    [SerializeField]
    [Tooltip("是否自动隐藏")]
    private bool isAutoFade = false;

    [SerializeField]
    [Tooltip("显示范围 FixedDynamic有效")]
    private float dynamicRange = 380.0f;

    private Canvas canvas;
    private RectTransform thrumParent;
    private bool isTouched = false;
    private float lastEventTime = 0.0f;
    private Vector2 offset = Vector2.zero;
    private Vector2 localPos = Vector2.zero;
    private int fingerIndex = -1;

    public Action<float, float> OnDragBegin;
    public Action<float, float> OnDragUpdate;
    public Action<float, float> OnDragEnd;
    public Action<bool, int> OnIsTouched;

    /// <summary>
    /// The joystick mode.
    /// </summary>
    public enum JoystickMode
    {
        /// <summary>
        /// The fixed joystick stay in the fixed position of the screen.
        /// </summary>
        Fixed,

        /// <summary>
        /// The dynamic joystick show in any position when touched.
        /// </summary>
        Dynamic,

        /// <summary>
        /// The dynamic joystick show in some position when touched.
        /// </summary>
        LocalDynamic,
    }

    /// <summary>
    /// Validates that all required components are not null and properly initialized.
    /// </summary>
    /// <returns>True if all components are valid, false otherwise.</returns>
    private bool ValidateComponents()
    {
        // Check if this object has been destroyed
        if (this == null || gameObject == null)
        {
            return false;
        }

        if (this.canvas == null)
        {
            Debug.LogWarning("UIJoystick: Canvas is null. Attempting to reinitialize.");
            try
            {
                this.canvas = this.GetComponentInParent<Canvas>();
            }
            catch (MissingReferenceException)
            {
                return false;
            }
            if (this.canvas == null)
            {
                Debug.LogError("UIJoystick: Failed to get Canvas component.");
                return false;
            }
        }

        if (this.thumb == null)
        {
            Debug.LogWarning("UIJoystick: Thumb is null. Attempting to reinitialize.");
            try
            {
                this.thumb = (RectTransform)gameObject.transform.Find("thumb");
            }
            catch (MissingReferenceException)
            {
                return false;
            }
            if (this.thumb == null)
            {
                Debug.LogError("UIJoystick: Failed to find thumb component.");
                return false;
            }
        }

        if (this.thrumParent == null && this.thumb != null)
        {
            this.thrumParent = (RectTransform)this.thumb.parent;
            if (this.thrumParent == null)
            {
                Debug.LogError("UIJoystick: Failed to get thumb parent.");
                return false;
            }
        }

        if (this.bg == null)
        {
            Debug.LogWarning("UIJoystick: Background is null. Attempting to reinitialize.");
            try
            {
                this.bg = (RectTransform)gameObject.transform.Find("bg");
            }
            catch (MissingReferenceException)
            {
                return false;
            }
            if (this.bg == null)
            {
                Debug.LogError("UIJoystick: Failed to find bg component.");
                return false;
            }
        }

        if (this.visibleRoot == null)
        {
            Debug.LogError("UIJoystick: VisibleRoot is null.");
            return false;
        }

        return true;
    }

    /// <summary>
    /// Gets a safe camera reference, handling null cases gracefully.
    /// </summary>
    /// <returns>Camera instance or null if not available.</returns>
    private Camera GetSafeCamera()
    {
        if (this.canvas == null)
        {
            return null;
        }

        // For Screen Space - Camera and World Space canvases
        if (this.canvas.renderMode == RenderMode.ScreenSpaceCamera || 
            this.canvas.renderMode == RenderMode.WorldSpace)
        {
            return this.canvas.worldCamera;
        }

        // For Screen Space - Overlay, return null (which is expected)
        return null;
    }

    /// <summary>
    /// add joystick listener.
    /// </summary>
    public void AddDragBeginListener(Action<float, float> listener)
    {
        this.OnDragBegin += listener;
    }

    /// <summary>
    /// add joystick listener.
    /// </summary>
    public void AddDragUpdateListener(Action<float, float> listener)
    {
        this.OnDragUpdate += listener;
    }

    /// <summary>
    /// add joystick listener.
    /// </summary>
    public void AddDragEndListener(Action<float, float> listener)
    {
        this.OnDragEnd += listener;
    }

    public void AddIsTouchedListener(Action<bool, int> listener)
    {
        this.OnIsTouched += listener;
    }

    private void Awake()
    {
        this.canvas = this.GetComponentInParent<Canvas>();
        Assert.IsNotNull(this.canvas);

        if (this.thumb == null)
        {
            this.thumb = (RectTransform)gameObject.transform.Find("thumb");
        }

        Assert.IsNotNull(this.thumb);

        if (this.bg == null)
        {
            this.bg = (RectTransform)gameObject.transform.Find("bg");
        }

        Assert.IsNotNull(this.bg);

        this.thrumParent = (RectTransform)this.thumb.parent;
        Assert.IsNotNull(this.thrumParent);

        if (JoystickMode.Dynamic == this.mode)
        {
            if (this.isAutoFade)
            {
                this.visibleRoot.SetActive(false);
                // this.bg.gameObject.SetActive(false);
            }
            //localPos.x = this.visibleRoot.transform.localPosition.x;
            //localPos.y = this.visibleRoot.transform.localPosition.y;
        }

        if (this.mode == JoystickMode.Fixed || this.mode == JoystickMode.LocalDynamic)
        {
            EasyTouch.On_TouchStart += this.OnDragBeginHandler;
            EasyTouch.On_TouchDown += this.OnDragHandler;
            EasyTouch.On_TouchUp += this.OnDragEndHandler;
        }
        else
        {
            EasyTouch.On_SwipeStart += this.OnDragBeginHandler;
            EasyTouch.On_Swipe += this.OnDragHandler;
            EasyTouch.On_SwipeEnd += this.OnDragEndHandler;
        }

        EasyTouch.On_Cancel += this.OnDragCancel;
        SceneManager.activeSceneChanged += this.OnChangeScene;
    }

    //awake的时候位置是不对的，所以在start里记录位置
    private void Start()
    {
        localPos.x = this.visibleRoot.transform.localPosition.x;
        localPos.y = this.visibleRoot.transform.localPosition.y;
    }

    private void OnDestroy()
    {
        try
        {
            if (this.mode == JoystickMode.Fixed || this.mode == JoystickMode.LocalDynamic)
            {
                EasyTouch.On_TouchStart -= this.OnDragBeginHandler;
                EasyTouch.On_TouchDown -= this.OnDragHandler;
                EasyTouch.On_TouchUp -= this.OnDragEndHandler;
            }
            else
            {
                EasyTouch.On_SwipeStart -= this.OnDragBeginHandler;
                EasyTouch.On_Swipe -= this.OnDragHandler;
                EasyTouch.On_SwipeEnd -= this.OnDragEndHandler;
            }

            EasyTouch.On_Cancel -= this.OnDragCancel;
            SceneManager.activeSceneChanged -= this.OnChangeScene;
        }
        catch (System.Exception ex)
        {
            // Silently handle any exceptions during cleanup
            Debug.LogWarning($"UIJoystick: Exception during cleanup: {ex.Message}");
        }
    }

    private void Update()
    {
        if (this.isTouched)
        {
            if (this.thumb == null)
            {
                this.isTouched = false;
                return;
            }

            if (Time.time >= this.lastEventTime + this.eventInterval)
            {
                this.lastEventTime = Time.time;
                if (this.OnDragUpdate != null)
                {
                    this.OnDragUpdate(this.offset.x, this.offset.y);
                }
            }
        }
    }

    private void LateUpdate()
    {
        if (!this.isTouched)
        {
            if (this.thumb != null)
            {
                this.thumb.localPosition = Vector2.zero;
            }
            
            if (this.rotateBG && this.bg != null)
            {
                this.bg.transform.localEulerAngles = new Vector3(0, 0, 0);
            }
        }
    }

    private void OnDragBeginHandler(Gesture gesture)
    {
        if (this == null || gameObject == null)
        {
            return;
        }

        if (!ValidateComponents())
        {
            return;
        }

        if (gesture == null)
        {
            return;
        }

        if (this.isTouched && gesture.fingerIndex != this.fingerIndex)
        {
            return;
        }

        if (this.isAutoFade)
        {
            this.visibleRoot.SetActive(true);
            // this.bg.gameObject.SetActive(true);
        }

        if (this.isTouched)
        {
            if (this.OnDragEnd != null)
            {
                this.OnDragEnd(this.offset.x, this.offset.y);
            }

            this.offset = Vector2.zero;
        }
        var inRange = false;
        if (gesture.startPosition.x <= dynamicRange && gesture.startPosition.y <= dynamicRange)
        {
            inRange = true;
        }
        if (this.mode == JoystickMode.Fixed || (this.mode == JoystickMode.LocalDynamic && !inRange))
        {
            Vector2 position;
            var rect = (RectTransform)this.visibleRoot.transform;
            var safeCamera = GetSafeCamera();
            if (!RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rect, gesture.startPosition, safeCamera, out position))
            {
                return;
            }

            if (!rect.rect.Contains(position))
            {
                return;
            }
        }
        else if (this.mode == JoystickMode.Dynamic || (this.mode == JoystickMode.LocalDynamic && inRange))
        {
            Vector2 position;
            var parent = (RectTransform)this.visibleRoot.transform.parent;
            var safeCamera = GetSafeCamera();
            if (!RectTransformUtility.ScreenPointToLocalPointInRectangle(
                parent, gesture.startPosition, safeCamera, out position))
            {
                return;
            }

            this.visibleRoot.transform.localPosition = position;
        }
        this.isTouched = true;
        this.fingerIndex = gesture.fingerIndex;
        if (null != this.OnIsTouched)
        {
            this.OnIsTouched(true, this.fingerIndex);
        }

        this.lastEventTime = Time.time;

        if (this.OnDragBegin != null)
        {
            this.OnDragBegin(this.offset.x, this.offset.y);
        }
    }

    private void OnDragHandler(Gesture gesture)
    {
        if (this == null || gameObject == null)
        {
            return;
        }

        // Validate components before processing
        if (!ValidateComponents())
        {
            return;
        }

        if (gesture == null)
        {
            return;
        }

        if (!this.isTouched || gesture.fingerIndex != this.fingerIndex)
        {
            return;
        }

        Vector2 localPointerPosition;
        var safeCamera = GetSafeCamera();
        
        if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
            this.thrumParent,
            gesture.position,
            safeCamera,
            out localPointerPosition))
        {
            this.thumb.localPosition = localPointerPosition;
            this.offset.x = localPointerPosition.x;
            this.offset.y = localPointerPosition.y;
            float magnitude = this.thumb.localPosition.magnitude;
            if (magnitude > this.radius)
            {
                float x = this.thumb.localPosition.x / magnitude * this.radius;
                float y = this.thumb.localPosition.y / magnitude * this.radius;
                this.thumb.localPosition = new Vector3(x, y, 0.0f);
                this.offset.x = x;
                this.offset.y = y;
            }
            if (this.rotateBG && this.bg != null)
            {
                float angle = (float)(Math.Atan2(this.thumb.localPosition.y, this.thumb.localPosition.x) * 180 / Math.PI);
                this.bg.localEulerAngles = new Vector3(0, 0, angle + 90);
            }
        }
    }

    private void OnDragEndHandler(Gesture gesture)
    {
        if (this == null || gameObject == null)
        {
            return;
        }

        // Validate components before processing
        if (!ValidateComponents())
        {
            return;
        }

        if (gesture == null)
        {
            return;
        }

        if (!this.isTouched || gesture.fingerIndex != this.fingerIndex)
        {
            return;
        }

        if (this.OnDragEnd != null)
        {
            this.OnDragEnd(this.offset.x, this.offset.y);
        }

        this.isTouched = false;
        this.fingerIndex = -1;
        if (null != this.OnIsTouched)
        {
            this.OnIsTouched(false, -1);
        }
        this.offset = Vector2.zero;

        if (JoystickMode.Dynamic == this.mode)
        {
            if (this.isAutoFade)
            {
                this.visibleRoot.SetActive(false);
                // this.bg.gameObject.SetActive(false);
            }
            this.visibleRoot.transform.localPosition = localPos;
        }
        else if (JoystickMode.LocalDynamic == this.mode)
        {
            this.visibleRoot.transform.localPosition = localPos;
        }
    }

    private void OnDragCancel(Gesture gesture)
    {
        this.isTouched = false;
        this.fingerIndex = -1;
        if (null != this.OnIsTouched)
        {
            this.OnIsTouched(false, -1);
        }
        this.offset = Vector2.zero;

        if (JoystickMode.Dynamic == this.mode)
        {
            if (this.isAutoFade)
            {
                this.visibleRoot.SetActive(true);
                // this.bg.gameObject.SetActive(true);
            }
        }
    }

    private void OnChangeScene(Scene fron, Scene to)
    {
        if (!this.isTouched)
        {
            return;
        }

        this.isTouched = false;
        this.fingerIndex = -1;
        if (null != this.OnIsTouched)
        {
            this.OnIsTouched(false, -1);
        }
        this.offset = Vector2.zero;

        if (JoystickMode.Dynamic == this.mode)
        {
            if (this.isAutoFade)
            {
                this.visibleRoot.SetActive(false);
                // this.bg.gameObject.SetActive(false);
            }
            this.visibleRoot.transform.localPosition = localPos;
        }else if (JoystickMode.LocalDynamic == this.mode)
        {
            this.visibleRoot.transform.localPosition = localPos;
        } 
    }

    public void SetDynamicRange(float range)
    {
        this.dynamicRange = range;
    }
}
