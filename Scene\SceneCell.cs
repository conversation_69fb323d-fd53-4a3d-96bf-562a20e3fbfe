﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using Nirvana;
using UnityEngine;

#if UNITY_EDITOR

/// <summary>
/// The scene cell used by this game.
/// </summary>
[Serializable]
public sealed class SceneCell : AbstractSceneCell
{
    [SerializeField]
    [Tooltip("The ground type for this cell.")]
    private GroundType ground = GroundType.Block;

    /// <summary>
    /// The collision type of this cell.
    /// </summary>
    public enum GroundType
    {
        /// <summary>
        /// This cell is block.
        /// </summary>
        Block = 0,

        /// <summary>
        /// This cell is walkable.
        /// </summary>
        Walkable = 1,

        /// <summary>
        /// This cell is safety area.
        /// </summary>
        Safety = 2,

        /// <summary>
        /// The obstacle cell but support find way.
        /// </summary>
        ObstacleWay = 3,

        /// <summary>
        /// The cell is water.
        /// </summary>
        Water = 4,

        /// <summary>
        /// The road for path finding.
        /// </summary>
        Road = 5,

        /// <summary>
        /// 场景水区域显示人物水波纹
        /// </summary>
        WaterRipple = 6,

        /// <summary>
        /// The cell is a fishing.
        /// </summary>
        ClientBlock = 7,

        Tunnel = 8,

        Border = 9,

        HighArea = 10,
    }

    /// <summary>
    /// Gets or sets the ground type of this cell.
    /// </summary>
    public GroundType Ground
    {
        get { return this.ground; }
        set { this.ground = value; }
    }
}

#endif
