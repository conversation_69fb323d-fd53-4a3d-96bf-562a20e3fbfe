ActTreasureView = ActTreasureView or BaseClass(SafeBaseView)

function ActTreasureView:__init()
	self:SetMaskBg(false,false)
	self:LoadConfig() 
end

function ActTreasureView:__delete()
	
end

function ActTreasureView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
	self:AddViewResource(0, "uis/view/universal_treasure_ui_prefab", "layout_treasure_content")
end

function ActTreasureView:ReleaseCallBack()
	if nil ~= self.add_val_alert then
		self.add_val_alert:DeleteMe();
		self.add_val_alert = nil
	end

	if self.Treasure_gift_list then
		for k,v in pairs(self.Treasure_gift_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.Treasure_gift_list = nil
	end

	self.Treasure_act = nil
	-- self.Treasure_gift_list = {}
	self.set_treasure = nil
	self.add_val_alert = nil
	self.treas_nem = nil
	self.is_visible = false
end

function ActTreasureView:LoadCallBack()
	self.Treasure_act = nil 						--定时器
	self.set_treasure = false						--
	self.add_val_alert = nil
	self.treas_nem = 0
	self.is_visible = false
	local bundle, asset = ResPath.GetActivityTitle("ActTreasure")
	self.node_list["img_title"].image:LoadSprite(bundle, asset, function()
  		XUI.ImageSetNativeSize(self.node_list["img_title"])
	end)
	-- 幸运奖励
	self.Treasure_gift_list = {}
	for i=1,9 do
		self.Treasure_gift = ItemCell.New(self.node_list["ph_treasure_"..i])
		self.Treasure_gift_list[i] = self.Treasure_gift
	end
	self.add_val_alert = Alert.New(nil,nil,nil,nil,true)
	XUI.AddClickEventListener(self.node_list.btn_treasure, BindTool.Bind1(self.OnClickBtnWishOne, self))
	XUI.AddClickEventListener(self.node_list.btn_Automatic_treasure, BindTool.Bind1(self.OnClickBtnTreasureSelf, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.OnCloseWiew, self))
	XUI.AddClickEventListener(self.node_list.btn_rule, BindTool.Bind1(self.OnClickBtnRule, self))
	XUI.AddClickEventListener(self.node_list.item_icon, BindTool.Bind1(self.OnClickItemIcon, self))
end

function ActTreasureView:OnFlush() 
	-- local wish_info = ActwishingWGData.Instance:GetCirculationWishInfo()
	local day_cfg = ActTreasureWGData.Instance:GetTreasureData()                      --获取表中数据（许愿池）
	
	local other_cfg = ActTreasureWGData.Instance:GetTreasureOtherData()               --获取表中数据（其他）
	local national_treasure_process = ActTreasureWGData.Instance:GetTreasureProcess()
	-- 奖励物品
	for i = 1,#self.Treasure_gift_list - 1 do 
		if nil ~= self.Treasure_gift_list[i] and next(day_cfg) then
			self.Treasure_gift_list[i]:SetData(day_cfg[i].reward_item[0]) 
		end	
	end
	self.Treasure_gift_list[9]:SetData(ActTreasureWGData.Instance:GetRewardMax())
	-- --进度条
	self.node_list.Slider.slider.value = national_treasure_process / other_cfg[1].national_treasure_process_max

	self.node_list.rich_consume_percent.text.text = string.format("%d/%d", national_treasure_process or 0, other_cfg[1].national_treasure_process_max)
	-- local treasure_gold = other_cfg[1].national_treasure_gold
	-- local price_info = ShopWGData.GetItemPrice(other_cfg[1].national_treasure_gift)
	self.add_val_alert:SetLableString(string.format(Language.Treasure_btnText.Treasure_do_that,other_cfg[1].national_treasure_need_gold))
	-- self.add_val_alert:SetOkFunc(function()
	-- 		if self.treas_nem == 1 then
	-- 			ActTreasureWGCtrl.Instance:SendAllInfoReq()
	-- 			self.add_val_alert:Close()
	-- 		elseif self.treas_nem == 2 then
	-- 			self:OnClickBtnTresaure()
	-- 		end
	-- 	end)

	if ActTreasureWGData.Instance:GetTreasureIsBigReward() then
		if self.Treasure_act ~= nil then
			GlobalTimerQuest:CancelQuest(self.Treasure_act)
			self.Treasure_act = nil
		end
		self.set_treasure = false
		self.node_list.btn_auto_text.text.text = Language.Treasure_btnText.Treasure_start
	end
end

function ActTreasureView:OnClickItemIcon()
	local item_id = ActTreasureWGData.Instance:GetTreasureOtherData()[1].national_treasure_item_id
	if not item_id then return end
	TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

function ActTreasureView:OpenCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	self:ShowTreasureData()   													--请求服务器更新数据
	if CountDownManager.Instance:HasCountDown("Treasure_cat_left_time") then
		CountDownManager.Instance:RemoveCountDown("Treasure_cat_left_time")
	end
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local next_time = act_cornucopia_info.next_time - (TimeWGCtrl.Instance:GetServerTime() or 0)
		CountDownManager.Instance:AddCountDown("Treasure_cat_left_time", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), nil, next_time, 1)	
	else
		self:CompleteRollerTime()
	end
end

function ActTreasureView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list.lable_remaining_time ~= nil then
		if time > 0 then
			local format_time = TimeUtil.Format2TableDHM(time)
			local str_list = Language.Common.TimeList
			local time_str = ""
			-- if format_time.day > 0 then
			-- 	time_str = format_time.day .. str_list.d
			-- end
			if format_time.hour > 0 then
				time_str = time_str .. format_time.hour .. str_list.h
			end
			time_str = time_str .. format_time.min .. str_list.min

			self.node_list.lable_remaining_time.text.text = (time_str)
		end
	end
end

function ActTreasureView:CompleteRollerTime()
	if self.node_list.lable_remaining_time ~= nil then
		self.node_list.lable_remaining_time.text.text = ("0")
	end
end


function ActTreasureView:ShowIndexCallBack()
end

--鉴宝一次
function ActTreasureView:OnClickBtnWishOne()
	local other_cfg = ActTreasureWGData.Instance:GetTreasureOtherData()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].national_treasure_item_id)
	if  item_num <= 0 then
		self.treas_nem = 1
		self.add_val_alert:Open()
		self.add_val_alert:SetOkFunc(function ()
			ActTreasureWGCtrl.Instance:SendTreasureInfoReq()
		end)
	else
		ActTreasureWGCtrl.Instance:SendTreasureInfoReq()
	end
end

--自动鉴宝
function ActTreasureView:OnClickBtnTreasureSelf()
	-- local other_cfg = ActTreasureWGData.Instance:GetTreasureOtherData()
	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].national_treasure_item_id)
	-- if  item_num <= 0 and self.is_visible == false then
	-- 	self.treas_nem = 2
	-- 	self.add_val_alert:Open()
	-- else
		self:OnClickBtnTresaure()
	-- end
	
end 

function ActTreasureView:OnClickBtnTresaure()

	if self.set_treasure == false then
		self.is_visible = true
		self.node_list.btn_auto_text.text.text = Language.Treasure_btnText.Treasure_stop
		self:OnGlobalTimer()
	else
		if self.Treasure_act ~= nil then 
			self.set_treasure = false
			self.is_visible = false
			self.node_list.btn_auto_text.text.text = Language.Treasure_btnText.Treasure_start
			GlobalTimerQuest:CancelQuest(self.Treasure_act) 
			self.Treasure_act = nil
		end
	end
end
function ActTreasureView:OnGlobalTimer()
	if ItemWGData.Instance:GetEmptyNum() <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotBagRoom)
		return
	end

	local fun = function ()
		self.set_treasure = true
		local role_gold = RoleWGData.Instance.role_info.gold or 0  --人物信息
		local other_cfg = ActTreasureWGData.Instance:GetTreasureOtherData()
		if other_cfg == nil then return end
		local national_treasure_process = ActTreasureWGData.Instance:GetTreasureProcess() or 100000
		if self.Treasure_act ~= nil then
			GlobalTimerQuest:CancelQuest(self.Treasure_act)
			self.Treasure_act = nil
		end

		local flag = ActTreasureWGCtrl.Instance:SendTreasureInfoReq()
		
		if role_gold >= other_cfg[1].national_treasure_need_gold and not flag then
			self.Treasure_act = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.OnGlobalTimer, self), 0.5) -- 计时器
		else
			self.set_treasure = false
			self.node_list.btn_auto_text.text.text = (Language.Treasure_btnText.Treasure_start)
			GlobalTimerQuest:CancelQuest(self.Treasure_act)
			self.Treasure_act = nil
		end
	end

	local other_cfg = ActTreasureWGData.Instance:GetTreasureOtherData()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].national_treasure_item_id)
	if self.add_val_alert and self.add_val_alert:GetIsShowTips() and item_num <= 0 then  -- 弹出提示
		self.add_val_alert:Open()
		self.add_val_alert:SetOkFunc(fun)
		self.add_val_alert:SetCancelFunc(function ()
			self.node_list.btn_auto_text.text.text = (Language.Treasure_btnText.Treasure_start)
		end)
		self.add_val_alert:SetCloseFunc(function ()
			self.node_list.btn_auto_text.text.text = (Language.Treasure_btnText.Treasure_start)
		end)
	else
		fun()
	end

end

function ActTreasureView:ShowTreasureData() 			
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE,
		opera_type = RA_NATIONAL_TREASURE_TYPE.RA_NATIONAL_TREASURE_TYPE_USE_GOLD_INFO 
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ActTreasureView:OnCloseWiew()
	if self.Treasure_act ~= nil then 
		self.set_treasure = false
		self.node_list.btn_auto_text.text.text = (Language.Treasure_btnText.Treasure_start)
		GlobalTimerQuest:CancelQuest(self.Treasure_act)   -- 取消计时器
		self.Treasure_act = nil
	end
	-- self.btn1_treasure = false
	-- self.btn2_treasure = false
	self:Close()
end


function ActTreasureView:OnClickBtnRule()
	RuleTip.Instance:SetContent(Language.Treasure_btnText.Treasure_rules, Language.Treasure_btnText.UpgradePlayTitle)
end