﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class DynamicBoneWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(DynamicBone), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("InitDynamicBone", InitDynamicBone);
		<PERSON><PERSON>("SetWeight", SetWeight);
		<PERSON><PERSON>("GetWeight", GetWeight);
		<PERSON><PERSON>unction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("CurRole", get_CurRole, set_CurRole);
		<PERSON><PERSON>("m_BoneRoots", get_m_BoneRoots, set_m_BoneRoots);
		<PERSON>.<PERSON>("m_UpdateRate", get_m_UpdateRate, set_m_UpdateRate);
		<PERSON><PERSON>("m_Damping", get_m_Damping, set_m_Damping);
		<PERSON><PERSON>("m_Elasticity", get_m_Elasticity, set_m_Elasticity);
		<PERSON><PERSON>("m_Stiffness", get_m_Stiffness, set_m_Stiffness);
		<PERSON><PERSON>("m_Inert", get_m_Inert, set_m_Inert);
		L.RegVar("m_Radius", get_m_Radius, set_m_Radius);
		L.RegVar("Gravity", get_Gravity, set_Gravity);
		L.RegVar("Force", get_Force, set_Force);
		L.RegVar("m_EndLength", get_m_EndLength, set_m_EndLength);
		L.RegVar("m_EndOffset", get_m_EndOffset, set_m_EndOffset);
		L.RegVar("Colliders", get_Colliders, set_Colliders);
		L.RegVar("Exclusions", get_Exclusions, set_Exclusions);
		L.RegVar("m_FreezeAxis", get_m_FreezeAxis, set_m_FreezeAxis);
		L.RegVar("isChuangJue", get_isChuangJue, set_isChuangJue);
		L.RegVar("Damping", get_Damping, set_Damping);
		L.RegVar("DampingDistrib", get_DampingDistrib, set_DampingDistrib);
		L.RegVar("Elasticity", get_Elasticity, set_Elasticity);
		L.RegVar("ElasticityDistrib", get_ElasticityDistrib, set_ElasticityDistrib);
		L.RegVar("Stiffness", get_Stiffness, set_Stiffness);
		L.RegVar("StiffnessDistrib", get_StiffnessDistrib, set_StiffnessDistrib);
		L.RegVar("Inert", get_Inert, set_Inert);
		L.RegVar("InertDistrib", get_InertDistrib, set_InertDistrib);
		L.RegVar("Radius", get_Radius, set_Radius);
		L.RegVar("RadiusDistrib", get_RadiusDistrib, set_RadiusDistrib);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitDynamicBone(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				obj.InitDynamicBone(arg0);
				return 0;
			}
			else if (count == 3)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.InitDynamicBone(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.InitDynamicBone(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else if (count == 7)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5);
				return 0;
			}
			else if (count == 8)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				return 0;
			}
			else if (count == 9)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				return 0;
			}
			else if (count == 10)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				float arg8 = (float)LuaDLL.luaL_checknumber(L, 10);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
				return 0;
			}
			else if (count == 11)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				float arg8 = (float)LuaDLL.luaL_checknumber(L, 10);
				float arg9 = (float)LuaDLL.luaL_checknumber(L, 11);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9);
				return 0;
			}
			else if (count == 12)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				float arg8 = (float)LuaDLL.luaL_checknumber(L, 10);
				float arg9 = (float)LuaDLL.luaL_checknumber(L, 11);
				float arg10 = (float)LuaDLL.luaL_checknumber(L, 12);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10);
				return 0;
			}
			else if (count == 13)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				float arg8 = (float)LuaDLL.luaL_checknumber(L, 10);
				float arg9 = (float)LuaDLL.luaL_checknumber(L, 11);
				float arg10 = (float)LuaDLL.luaL_checknumber(L, 12);
				float arg11 = (float)LuaDLL.luaL_checknumber(L, 13);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11);
				return 0;
			}
			else if (count == 14)
			{
				DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
				RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				float arg8 = (float)LuaDLL.luaL_checknumber(L, 10);
				float arg9 = (float)LuaDLL.luaL_checknumber(L, 11);
				float arg10 = (float)LuaDLL.luaL_checknumber(L, 12);
				float arg11 = (float)LuaDLL.luaL_checknumber(L, 13);
				float arg12 = (float)LuaDLL.luaL_checknumber(L, 14);
				obj.InitDynamicBone(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DynamicBone.InitDynamicBone");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetWeight(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetWeight(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetWeight(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			DynamicBone obj = (DynamicBone)ToLua.CheckObject<DynamicBone>(L, 1);
			float o = obj.GetWeight();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CurRole(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			RoleType ret = obj.CurRole;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurRole on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_BoneRoots(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			System.Collections.Generic.List<UnityEngine.Transform> ret = obj.m_BoneRoots;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_BoneRoots on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_UpdateRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.m_UpdateRate;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_UpdateRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Damping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.m_Damping;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Damping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Elasticity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.m_Elasticity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Elasticity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Stiffness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.m_Stiffness;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Stiffness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Inert(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.m_Inert;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Inert on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.m_Radius;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Gravity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.Vector3 ret = obj.Gravity;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Gravity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Force(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.Vector3 ret = obj.Force;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Force on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_EndLength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.m_EndLength;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_EndLength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_EndOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.Vector3 ret = obj.m_EndOffset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_EndOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Colliders(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			System.Collections.Generic.List<DynamicBoneCollider> ret = obj.Colliders;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Colliders on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Exclusions(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			System.Collections.Generic.List<UnityEngine.Transform> ret = obj.Exclusions;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Exclusions on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_FreezeAxis(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			DynamicBone.FreezeAxis ret = obj.m_FreezeAxis;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_FreezeAxis on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isChuangJue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			bool ret = obj.isChuangJue;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isChuangJue on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Damping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.Damping;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Damping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DampingDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve ret = obj.DampingDistrib;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DampingDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Elasticity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.Elasticity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Elasticity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ElasticityDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve ret = obj.ElasticityDistrib;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ElasticityDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Stiffness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.Stiffness;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Stiffness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_StiffnessDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve ret = obj.StiffnessDistrib;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index StiffnessDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Inert(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.Inert;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Inert on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InertDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve ret = obj.InertDistrib;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index InertDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float ret = obj.Radius;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RadiusDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve ret = obj.RadiusDistrib;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RadiusDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CurRole(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			RoleType arg0 = (RoleType)ToLua.CheckObject(L, 2, typeof(RoleType));
			obj.CurRole = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurRole on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_BoneRoots(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			System.Collections.Generic.List<UnityEngine.Transform> arg0 = (System.Collections.Generic.List<UnityEngine.Transform>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.Transform>));
			obj.m_BoneRoots = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_BoneRoots on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_UpdateRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_UpdateRate = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_UpdateRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Damping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Damping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Damping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Elasticity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Elasticity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Elasticity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Stiffness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Stiffness = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Stiffness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Inert(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Inert = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Inert on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Radius = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Gravity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.Gravity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Gravity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Force(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.Force = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Force on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_EndLength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_EndLength = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_EndLength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_EndOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.m_EndOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_EndOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Colliders(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			System.Collections.Generic.List<DynamicBoneCollider> arg0 = (System.Collections.Generic.List<DynamicBoneCollider>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<DynamicBoneCollider>));
			obj.Colliders = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Colliders on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Exclusions(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			System.Collections.Generic.List<UnityEngine.Transform> arg0 = (System.Collections.Generic.List<UnityEngine.Transform>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.Transform>));
			obj.Exclusions = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Exclusions on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_FreezeAxis(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			DynamicBone.FreezeAxis arg0 = (DynamicBone.FreezeAxis)ToLua.CheckObject(L, 2, typeof(DynamicBone.FreezeAxis));
			obj.m_FreezeAxis = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_FreezeAxis on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isChuangJue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isChuangJue = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isChuangJue on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Damping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Damping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Damping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DampingDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve arg0 = (UnityEngine.AnimationCurve)ToLua.CheckObject<UnityEngine.AnimationCurve>(L, 2);
			obj.DampingDistrib = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DampingDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Elasticity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Elasticity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Elasticity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ElasticityDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve arg0 = (UnityEngine.AnimationCurve)ToLua.CheckObject<UnityEngine.AnimationCurve>(L, 2);
			obj.ElasticityDistrib = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ElasticityDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Stiffness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Stiffness = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Stiffness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_StiffnessDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve arg0 = (UnityEngine.AnimationCurve)ToLua.CheckObject<UnityEngine.AnimationCurve>(L, 2);
			obj.StiffnessDistrib = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index StiffnessDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Inert(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Inert = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Inert on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_InertDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve arg0 = (UnityEngine.AnimationCurve)ToLua.CheckObject<UnityEngine.AnimationCurve>(L, 2);
			obj.InertDistrib = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index InertDistrib on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Radius = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RadiusDistrib(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DynamicBone obj = (DynamicBone)o;
			UnityEngine.AnimationCurve arg0 = (UnityEngine.AnimationCurve)ToLua.CheckObject<UnityEngine.AnimationCurve>(L, 2);
			obj.RadiusDistrib = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RadiusDistrib on a nil value");
		}
	}
}

