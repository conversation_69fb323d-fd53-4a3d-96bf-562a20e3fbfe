
function SunRainbowView:LoadCallBack_Lottery()
    self:FlushEndTime()
    if not self.big_reward_list then
        self.big_reward_list = {}
        for i = 1, SunRainbowWgData.BIG_REWARD_NUM do
            self.big_reward_list[i] = BigRewardCell.New(self.node_list["big_reward_" .. i])
            self.big_reward_list[i]:SetIndex(i)
        end
    end

    if not self.small_reward_list then
        self.small_reward_list = {}
        for i = 1, SunRainbowWgData.SMALL_REWARD_NUM do
            self.small_reward_list[i] = ItemCell.New(self.node_list["small_reward_pos_" .. i])
        end
    end

    if not self.rebate_list then
        self.rebate_list = AsyncListView.New(SunRainbowLeiJiRender, self.node_list["rebate_list"])
    end

    for i = 1, 3 do
		self.node_list["draw_buy_btn_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickDraw, self, i))
        self.node_list["draw_buy_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self, i))
	end

    self.node_list["reward_display_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRewardShow, self))   -- 奖励展示
    self.node_list["probability_btn"].button:AddClickListener(BindTool.Bind(self.OnClickProbability, self))     -- 概率公示
    self.node_list["record_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRecord, self))               -- 抽奖记录
	XUI.AddClickEventListener(self.node_list["add_item"], BindTool.Bind(self.OnClickAddItem, self))
end

function SunRainbowView:ReleaseCallBack_Lottery()
    if self.big_reward_list then
		for k, v in pairs(self.big_reward_list) do
            v:DeleteMe()
        end
        self.big_reward_list = nil
	end

    if self.small_reward_list then
		for k, v in pairs(self.small_reward_list) do
            v:DeleteMe()
        end
        self.small_reward_list = nil
	end

    if self.rebate_list then
        self.rebate_list:DeleteMe()
        self.rebate_list = nil
    end

    if CountDownManager.Instance:HasCountDown("sun_end_time") then
        CountDownManager.Instance:RemoveCountDown("sun_end_time")
    end
end

function SunRainbowView:OpenCallBack()
    SunRainbowWgCtrl.Instance:SendReq(RA_GUANRICHANGHONG_OP_TYPE.INFO)
end

function SunRainbowView:OnFlush_Lottery()
    self:FlushShowReward()
    self:FlushBtnShow()
end

function SunRainbowView:FlushShowReward()
    local big_reward_cfg, small_reward_cfg = SunRainbowWgData.Instance:GetDifferentTypeCfg()
    if IsEmptyTable(big_reward_cfg) or IsEmptyTable(small_reward_cfg) then
        return
    end

    for k, v in ipairs(self.big_reward_list) do
        v:SetData(big_reward_cfg[k])
    end

    for k, v in ipairs(self.small_reward_list) do
        if small_reward_cfg[k] then
            local item_id = small_reward_cfg[k].item_id
            local num =small_reward_cfg[k].num
            v:SetData({item_id = item_id, num = num})
        else
            v:SetData(nil)
        end
    end

    local leiji_list = SunRainbowWgData.Instance:GetFanliRewardList()
    if not IsEmptyTable(leiji_list) then
        self.rebate_list:SetDataList(leiji_list)
    end
end

function SunRainbowView:FlushBtnShow()
    local cfg = SunRainbowWgData.Instance:GetConsumeCfg()
    if not cfg then
        return
    end

    local item_cfg
    local count = 0
    for i = 1, 3 do
        if cfg[i] then
            local item_id = cfg[i].draw_item.item_id
            item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
            if not IsEmptyTable(item_cfg) then
                self.node_list["draw_buy_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
            end

            local str = NumberToChinaNumber(cfg[i].onekey_draw_num)
            self.node_list["draw_buy_text_" .. i].text.text = string.format(Language.SunRainbow.BtnStr, str)
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["draw_buy_red_" .. i]:SetActive(count >= cfg[i].draw_item.num)

            self.node_list["draw_buy_num_" .. i].text.text = ToColorStr(cfg[i].draw_item.num)
        end
    end

    local cur_times = SunRainbowWgData.Instance:GetCurDrawTimes()
    self.node_list["leichou_time"].text.text = cur_times
    
    local cur_point = SunRainbowWgData.Instance:GetCurPoint()
    self.node_list["cur_score"].text.text = string.format(Language.SunRainbow.CurPoint, cur_point)

    self.node_list.next_big_reward.text.text = Language.SunRainbow.NextBigReward

	local consome_item_cfg = ItemWGData.Instance:GetItemConfig(cfg[1].draw_item.item_id)
    self.node_list["item_icon"].image:LoadSprite(ResPath.GetItem(consome_item_cfg.icon_id))

	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg[1].draw_item.item_id)
	self.node_list["item_num"].text.text = item_num
end

function SunRainbowView:OnClickDraw(draw_type)
    local cfg = SunRainbowWgData.Instance:GetConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.draw_item.item_id)

    if num >= cfg.draw_item.num then
        SunRainbowWgData.Instance:CacheOrGetDrawIndex(draw_type)
        SunRainbowWgCtrl.Instance:SendReq(RA_GUANRICHANGHONG_OP_TYPE.DRAW, cfg.onekey_draw_num, 1)
    else
        --不足够买
        SunRainbowWgCtrl.Instance:ClickUse(draw_type, function()
            self:OnClickBuy(draw_type)
        end)
    end
end

function SunRainbowView:OnClickBuy(draw_type)
    local cfg = SunRainbowWgData.Instance:GetConsumeCfg()
    local cur_cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.draw_item.item_id)
    local consume = cfg[1].consume_price * (cur_cfg.draw_item.num - num)
    --检查仙玉
    local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
    --足够购买，不足弹窗
    if enough then
        SunRainbowWgData.Instance:CacheOrGetDrawIndex(draw_type)
        SunRainbowWgCtrl.Instance:SendReq(RA_GUANRICHANGHONG_OP_TYPE.DRAW, cfg[draw_type].onekey_draw_num)
    else
        VipWGCtrl.Instance:OpenTipNoGold()
    end
end

function SunRainbowView:ShowItemTips(draw_type)
    local cfg = SunRainbowWgData.Instance:GetConsumeCfg()
    local item_id = cfg[draw_type].draw_item.item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function SunRainbowView:FlushEndTime()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUANRI_CHANGHONG)
    if CountDownManager.Instance:HasCountDown("sun_end_time") then
        CountDownManager.Instance:RemoveCountDown("sun_end_time")
    end
    if time > 0 then
        CountDownManager.Instance:AddCountDown("sun_end_time",
            BindTool.Bind(self.UpdateCountDown, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function SunRainbowView:UpdateCountDown(elapse_time, total_time)
    local time = math.ceil(total_time - elapse_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["activity_time"].text.text = string.format(Language.SunRainbow.SunActTime, time_str)
end

function SunRainbowView:OnComplete()
    self.node_list.activity_time.text.text = ""
    self:Close()
end


function SunRainbowView:OnClickRewardShow() -- 奖励展示
    SunRainbowWgCtrl.Instance:OpenSunLibraryView()
end

function SunRainbowView:OnClickProbability()
    local info = SunRainbowWgData.Instance:GetProbabilityInfo()
    TipWGCtrl.Instance:OpenTipsRewardProView(info)
end

function SunRainbowView:OnClickRecord()
    local data_list = SunRainbowWgData.Instance:GetRecordInfo()
	local tips_data_list = {}
    if not IsEmptyTable(data_list) then
		for i, v in ipairs(data_list) do
			local temp_data = {}
			temp_data.item_data = {}
            temp_data.item_data.item_id = v.item_id
            temp_data.item_data.is_bind = v.is_bind or 0
            temp_data.item_data.num = v.num or 0
			temp_data.consume_time = v.draw_time
			temp_data.role_name = v.role_name
			table.insert(tips_data_list, temp_data)
		end
    end

	TipWGCtrl.Instance:OpenTipsRewardRecordView(tips_data_list)
end

function SunRainbowView:OnClickAddItem()
	local consume_cfg = SunRainbowWgData.Instance:GetConsumeCfg()
    if not consume_cfg then
        return
    end

	local item_cfg = ItemWGData.Instance:GetItemConfig(consume_cfg[1].draw_item.item_id)
	TipWGCtrl.Instance:OpenItem({item_id = item_cfg.id})
end




----------------------BigRewardCell--------------------

BigRewardCell = BigRewardCell or BaseClass(BaseRender)

function BigRewardCell:__init()
    self.reward_item = ItemCell.New(self.node_list["item_pos"])
end

function BigRewardCell:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function BigRewardCell:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.reward_item.item_id
    local num = self.data.reward_item.num
    self.reward_item:SetData({item_id = item_id, num = num})
    
    self.node_list.text_name.text.text = ItemWGData.Instance:GetItemName(item_id)
end





----------------------SunRainbowLeiJiRender--------------------

SunRainbowLeiJiRender = SunRainbowLeiJiRender or BaseClass(BaseRender)
function SunRainbowLeiJiRender:LoadCallBack()
    self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
    if not self.show_item then
        self.show_item = ItemCell.New(self.node_list.item_pos)
    end
end

function SunRainbowLeiJiRender:ReleaseCallBack()
    if self.show_item then
        self.show_item:DeleteMe()
        self.show_item = nil
    end
end

function SunRainbowLeiJiRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data.data
    self.node_list["time_str"].text.text = data.draw_num
    if not IsEmptyTable(data.reward_item) then
        self.show_item:SetShowCualityBg(false)
        self.show_item:SetBindIconVisible(false)
        self.show_item:NeedDefaultEff(false)
        self.show_item:SetCellBgEnabled(false)
        self.show_item:SetData(data.reward_item[0])
    end

    local draw_times = SunRainbowWgData.Instance:GetCurDrawTimes()
    local is_show = data.draw_num <= draw_times and self.data.has_get ~= 1
    self.node_list["is_get"]:SetActive(self.data.has_get == 1)
    self.node_list["btn_lingqu"]:SetActive(is_show)
    self.node_list["can_get"]:SetActive(self.data.has_get == 1)

    if draw_times <= data.start_num then
        self.node_list.slider.slider.value = 0
    elseif draw_times >= data.draw_num then
        self.node_list.slider.slider.value = 1
    else
        self.node_list.slider.slider.value = (draw_times-data.start_num) / (data.draw_num - data.start_num)
    end
    
end

function SunRainbowLeiJiRender:OnClickGet()
    if not self.data then
        return
    end

    local draw_times = SunRainbowWgData.Instance:GetCurDrawTimes()
    local data = self.data.data
    if draw_times >= data.draw_num and self.data.has_get == 0 then
        SunRainbowWgCtrl.Instance:SendReq(RA_GUANRICHANGHONG_OP_TYPE.REWARD, data.index)
    end
end