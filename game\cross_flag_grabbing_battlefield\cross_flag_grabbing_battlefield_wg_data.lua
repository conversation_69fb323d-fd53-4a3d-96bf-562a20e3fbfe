CrossFlagGrabbingBattleFieldWGData = CrossFlagGrabbingBattleFieldWGData or BaseClass()

function CrossFlagGrabbingBattleFieldWGData:__init()
    if CrossFlagGrabbingBattleFieldWGData.Instance then
		print_error("[CrossFlagGrabbingBattleFieldWGData]:Attempt to create singleton twice!")
	end

    CrossFlagGrabbingBattleFieldWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("cross_flag_battle_auto")
    self.fgb_contend_cfg = ListToMap(cfg.contend, "seq")
    self.person_reward_cfg = ListToMap(cfg.person_score_reward, "seq")
    self.person_kill_reward_cfg = ListToMap(cfg.person_kill_reward, "seq")
    self.person_score_rank_reward = cfg.person_score_rank_reward

    self.camp_cfg = cfg.camp
    self.other_cfg = cfg.other[1]
    self.fgb_state = CROSS_FLAG_BATTLE_STATE_TYPE.NOMATCH

    self.room_id = 0
    self.my_camp = 0
    self.ma_camp_num = 0
    self.cur_kill_num = 0
    self.room_end_time = 0
    self.cur_fgb_score = 0
    self.match_end_time = 0
    self.team_one_score = 0
    self.team_two_score = 0
    self.room_create_time = 0
    self.kill_reward_index = -1
    self.score_reward_index = -1
    self.next_calc_contend_time = 0

    self.contend_list = {}
    self.camp_info_list = {}
    self.rank_item_list = {}
    self.my_rank_item_data = {}
    self.fgb_map_gather_info = {}
    self.camp_player_count_list = {}

    RemindManager.Instance:Register(RemindName.CrossFGB, BindTool.Bind(self.GetCrossFGBRemind, self))
end

function CrossFlagGrabbingBattleFieldWGData:__delete()
    CrossFlagGrabbingBattleFieldWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.CrossFGB)
end

function CrossFlagGrabbingBattleFieldWGData:GetCrossFGBRemind()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)
    
    if is_open then
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local room_end_time = self.room_end_time or 0

        if self:GetMyFGBState() == CROSS_FLAG_BATTLE_STATE_TYPE.MATCHSUC and room_end_time > server_time then
            return 1
        end
    end

    return 0
end

------------------------------------protocol_start-----------------------------------
function CrossFlagGrabbingBattleFieldWGData:SetFGBScoreRank(protocol)
    local count = protocol.count
    local rank_item_list = {}
    local my_rank_item_data = {}

    for i = 0, count - 1 do
        local data = protocol.rank_item_list[i]
        local uuid = data.uuid
        local role_uuid = RoleWGData.Instance:GetUUid()

        if uuid.temp_low == role_uuid.temp_low and uuid.temp_high == role_uuid.temp_high then
            my_rank_item_data = data
        end

        table.insert(rank_item_list, data)
    end

    self.rank_item_list = rank_item_list
    self.my_rank_item_data = my_rank_item_data
    table.sort(self.rank_item_list, SortTools.KeyLowerSorter("rank"))
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBScoreRankList()
    return self.rank_item_list, self.my_rank_item_data
end

function CrossFlagGrabbingBattleFieldWGData:SetFGBRoomInfo(protocol)
    self.room_id = protocol.room_id
    self.room_end_time = protocol.end_time
    self.room_create_time = protocol.create_time
    self.camp_info_list = protocol.camp_info_list

    if self.room_id > 0 and self.room_end_time == 0 then
        self.fgb_state = CROSS_FLAG_BATTLE_STATE_TYPE.MATCHING
    elseif self.room_id > 0 and self.room_end_time > 0 then
        self.fgb_state = CROSS_FLAG_BATTLE_STATE_TYPE.MATCHSUC
    else
        self.fgb_state = CROSS_FLAG_BATTLE_STATE_TYPE.NOMATCH
    end
end

function CrossFlagGrabbingBattleFieldWGData:SetFGBBaseInfo(protocol)
    self.my_camp = protocol.camp
    self.cur_fgb_score = protocol.score
	self.cur_kill_num = protocol.kill_num
    self.kill_reward_index = protocol.kill_reward_index
    self.score_reward_index = protocol.score_reward_index
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBMyCamp()
    return self.my_camp
end

function CrossFlagGrabbingBattleFieldWGData:SetFGBContendInfo(protocol)
    self.contend_list = protocol.contend_list
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBContendInfo()
    return self.contend_list
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBContendInfoBySeq(seq)
    for k, v in pairs(self.contend_list) do
        if seq == v.seq then
            return v
        end
    end
end

function CrossFlagGrabbingBattleFieldWGData:GetMyFGBState()
    return self.fgb_state
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBMatchingTime()
    local match_robot_time = (self.other_cfg or {}).match_robot_time or 0
    local match_wait_time = match_robot_time + 1
    local match_end_time = self.room_create_time + match_wait_time
    return match_end_time, match_wait_time
end

function CrossFlagGrabbingBattleFieldWGData:SetFGBSceneInfo(protocol)
    self.camp_player_count_list = protocol.camp_player_count_list
    self.ma_camp_num = self.camp_player_count_list[self.my_camp] or 0
    self.next_calc_contend_time = protocol.next_calc_contend_time
end

function CrossFlagGrabbingBattleFieldWGData:GetNextCaleContendTime()
    local calc_contend_time = (self.other_cfg or {}).calc_contend_time or 0
    return self.next_calc_contend_time, calc_contend_time
end

function CrossFlagGrabbingBattleFieldWGData:SetFGBMapGatherInfo(protocol)
    local fgb_map_gather_info = {}

    if not IsEmptyTable(protocol.gather_list) then
        for k, v in pairs(protocol.gather_list) do
            fgb_map_gather_info[v.obj_id] = v
        end
    end

    self.fgb_map_gather_info = fgb_map_gather_info
end

function CrossFlagGrabbingBattleFieldWGData:UpdateFGBMapGatherInfo(protocol)
    local obj_id = protocol.obj_id
    if obj_id and nil ~= self.fgb_map_gather_info[obj_id] then
        self.fgb_map_gather_info[obj_id] = nil
    end
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBmapGatherInfo()
    return  self.fgb_map_gather_info or {}
end
-------------------------------------protocol_end------------------------------------
function CrossFlagGrabbingBattleFieldWGData:GetFGBPersonScoreRewardList()
    return self.person_reward_cfg
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBPersonScoreRewardBySeq(seq)
    return self.person_reward_cfg[seq] or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBPersonKillRewardList()
    return self.person_kill_reward_cfg
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBPersonKillRewardBySeq(seq)
    return self.person_kill_reward_cfg[seq] or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBPersonRankRewardList()
    return self.person_score_rank_reward
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBWinRewardList()
    return (self.other_cfg or {}).win_reward_item or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBFailRewardList()
    return (self.other_cfg or {}).fail_reward_item or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBCurScore()
    return self.cur_fgb_score
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBCurKillNum()
    return self.cur_kill_num
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBOpenLevel()
    return (self.other_cfg or {}).open_level or 0
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBSceneTeamScore()
    return self.camp_info_list
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBContendList()
    return self.fgb_contend_cfg or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBContendBySeq(seq)
    return self.fgb_contend_cfg[seq] or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBTaskRewardDataList()
    local data_list = {}
    -- 积分奖励
    local score_reward_index = self.score_reward_index + 1
    local max_score_index = #self:GetFGBPersonScoreRewardList()
    score_reward_index = score_reward_index < max_score_index and score_reward_index or max_score_index
    local score_cfg = self:GetFGBPersonScoreRewardBySeq(score_reward_index)
    local score_data = {}
    score_data.reward_cfg = score_cfg.reward_item or {}
    score_data.cur_num = self.cur_fgb_score or 0
    score_data.target_num = score_cfg.need_score or 0
    score_data.is_score = true
    table.insert(data_list, score_data)

    -- 击杀奖励
    local kill_data = {}
    local kill_reward_index = self.kill_reward_index + 1
    local max_kill_index = #self:GetFGBPersonKillRewardList()
    kill_reward_index = kill_reward_index < max_kill_index and kill_reward_index or max_kill_index
    local kill_cfg = self:GetFGBPersonKillRewardBySeq(kill_reward_index)
    kill_data.reward_cfg = kill_cfg.reward_item or {}
    kill_data.cur_num = self.cur_kill_num or 0
    kill_data.target_num = kill_cfg.need_kill or 0
    kill_data.is_score = false
    table.insert(data_list, kill_data)

    return data_list
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBShowRewardList(is_score)
    return is_score and self.person_reward_cfg or self.person_kill_reward_cfg
end

-- 自动获得积分/时间 * 场景里人数  *（1+ (占领的点的比例) 0.1 +0.15 +0.1）
function CrossFlagGrabbingBattleFieldWGData:GetFGBTaskScoreAddDesc()
    local base_time = (self.other_cfg or {}).auto_get_score_time or 0
    local base_attr = (self.other_cfg or {}).auto_get_score or 0

    local my_camp = self:GetFGBMyCamp()
    local base_value = base_attr * self.ma_camp_num

    local per = 1
    for k, v in pairs(self.fgb_contend_cfg) do
        local contend_info = self:GetFGBContendInfoBySeq(v.seq)
        if not IsEmptyTable(contend_info) then
           local per_value = (contend_info.camp_value_list or {})[my_camp] or 0

           if per_value > 0 then
                per = per + v.score_added_per / 10000
           end
        end
    end

    local score_desc = string.format(Language.FlagGrabbingBattlefield.FGBTaskScoreAddDesc, math.floor(base_value * per), base_time)
    return score_desc
end

function CrossFlagGrabbingBattleFieldWGData:SetFGBExplainFocusState(state)
    local target_value = state and 1 or 0
    if state then
        self:UpdateFGBExplainDay()
    end

    PlayerPrefsUtil.SetString("fgb_jump_explain", target_value)
end

function CrossFlagGrabbingBattleFieldWGData:NeedJumpFGBExplainView()
    local need_jump_explain = PlayerPrefsUtil.GetString("fgb_jump_explain") or 0
    return need_jump_explain == 1
end

function CrossFlagGrabbingBattleFieldWGData:IsFGBExplainToday()
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local key_name = uid .. "fgb_explain"
    local explain_day = PlayerPrefsUtil.GetString(key_name) or 0
    return tonumber(explain_day) == cur_day
end

function CrossFlagGrabbingBattleFieldWGData:UpdateFGBExplainDay()
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local key_name = uid .. "fgb_explain"
    PlayerPrefsUtil.SetString(key_name, cur_day)
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBAutoFuHuoTime()
    return (self.other_cfg or {}).auto_fohuo_time or 0
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBAutoEnterTime()
    return (self.other_cfg or {}).auto_enter_time or 0
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBShowExplainTipTime()
    return (self.other_cfg or {}).explain_tip_show_time or 0
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBCampCfgByCampId(camp_id)
    return  self.camp_cfg[camp_id] or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBCampCfg()
    return  self.camp_cfg or {}
end

function CrossFlagGrabbingBattleFieldWGData:GetFGBRoomEndTime()
    return self.room_end_time or 0
end