CultivationView = CultivationView or BaseClass(SafeBaseView)
function CultivationView:__init()
	self.view_style = ViewStyle.Full
    self.default_index = TabIndex.tiandao_stone
    self.is_safe_area_adapter = true

    self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

    local view_bundle = "uis/view/cultivation_ui_prefab"
    local common_path = "uis/view/common_panel_prefab"
    local charm_bundle_name = "uis/view/cultivation_ui/charm_prefab"
    -- self:AddViewResource(TabIndex.esoterica, common_path, "layout_a3_common_panel")
    -- self:AddViewResource(0, view_bundle, "layout_base_bg")
    --self:AddViewResource(TabIndex.esoterica, view_bundle, "layout_esoterica_view")
    self:AddViewResource(TabIndex.tiandao_stone, charm_bundle_name, "layout_charm_holy_seal")
    self:AddViewResource(TabIndex.anger_skill, view_bundle, "layout_anger_skill_new_view")
    self:AddViewResource(TabIndex.equip_target, view_bundle, "layout_equiptarget")
    self:AddViewResource(TabIndex.cross_air_war, view_bundle, "layout_kf_air_war_view")
    
    -- self:AddViewResource(0, view_bundle, "VerticalTabbar")
    self:AddViewResource(0, common_path, "VerticalTabbar")
	self:AddViewResource(0, common_path, "HorizontalTabbar")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

    self.tab_sub = {}
	self.remind_tab = {
            --{RemindName.Esoterica, RemindName.EsotericaReslove},
            {RemindName.Charm_Holy_Seal},
            {RemindName.AngerSkill},
            {RemindName.EquipTarget},
            {},
		}

    local data = {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.JINGJIE}
    self:SetTabShowUIScene(0, data)
    -- self:SetTabShowUIScene(TabIndex.cultivation, data)
    -- self:SetTabShowUIScene(TabIndex.esoterica, data)
    -- self:SetTabShowUIScene(TabIndex.tiandao_stone, data)
end

function CultivationView:ReleaseCallBack()
    --self:ReleaseCallBack__Esoterica()
    self:ReleaseHolySealCallBack()
    self:ReleaseCallBack__AngerSkill()
    self:ReleaseCallBack__EquipTarget()
    self:ReleaseCallBack_CrossAirWar()

    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
end

function CultivationView:LoadCallBack()
    self:InitTabbar()
end

function CultivationView:InitTabbar()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabState, self))
		self.tabbar:Init(Language.Cultivation.TabSub1, self.tab_sub, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.CultivationView, self.tabbar)
	end
end

function CultivationView:SetTabState()
	self.tabbar:SetToggleVisible(TabIndex.equip_target, EquipTargetWGData.Instance:GetEquipTargetFuncOpen())
    self.tabbar:SetToggleVisible(TabIndex.cross_air_war, CrossAirWarWGData.Instance:GetFuncOpenStatus())
end

function CultivationView:InitMoneyBar()
    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function CultivationView:LoadIndexCallBack(index)

    -- if index == TabIndex.esoterica then
    --     self:LoadCallBack__Esoterica()
    if index == TabIndex.tiandao_stone then
        self:LoadHolySealCallBack()
    elseif index == TabIndex.anger_skill then
        self:LoadCallBack__AngerSkill()
    elseif index == TabIndex.equip_target then
       self:LoadCallBack__EquipTarget()
    elseif index == TabIndex.cross_air_war then
        self:LoadCallBack_CrossAirWar()
    end
end

function CultivationView:ShowIndexCallBack(index)
    self:ShowIndexChange(self.last_show_index)


    local effects_transform = self:GetUISceneEffectsTransform()
    if effects_transform then
        effects_transform:DOLocalMoveX(0, 0)
    end
    -- local bg_res = "a3_jj_bj_dbeijing"
    -- local bundle, asset
    -- if index == TabIndex.esoterica then
    --     self:ShowIndexCallBack__Esoterica()
    --     if self.last_show_index == TabIndex.equip_target then
    --         self:PlayEsotericaModelTween()
    --     else
    --         self:PlayEsotericaModelTween(0)
    --     end
    if index == TabIndex.tiandao_stone then
        self:ShowHolySealCallBack()
        if self.last_show_index == TabIndex.equip_target then
            self:PlaySealModelTween()
        else
            self:PlaySealModelTween(0)
        end
    elseif index == TabIndex.anger_skill then
        self:ShowIndexCallBack__AngerSkill()
    elseif index == TabIndex.equip_target then
        self:ShowIndexCallBack__EquipTarget()
        if self.last_show_index == TabIndex.tiandao_stone then
            self:PlayEquipModelTween()
        else
            self:PlayEquipModelTween(0)
        end
    elseif index == TabIndex.cross_air_war then
        self:ShowIndexCallBack_CrossAirWar()
    end
	self.last_show_index = index
    -- self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
    --     self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    -- end)
end

function CultivationView:ShowIndexChange(old_index)
	if old_index == TabIndex.tiandao_stone then
        self:StopSequence_Seal()
    --elseif old_index == TabIndex.esoterica then
        --self:StopSequence_Esoterica()
    elseif old_index == TabIndex.equip_target then
        self:StopSequence_EquipTarget()
	end
end

function CultivationView:OnFlush(param_t, index)
    -- if index == TabIndex.esoterica then
    --     self:OnFlush__Esoterica(param_t)
    if index == TabIndex.tiandao_stone then
        self:OnFlushHolySeal(param_t)
    elseif index == TabIndex.anger_skill then
        self:OnFlush__AngerSkill()
    elseif index == TabIndex.equip_target then
        self:OnFlush__EquipTarget(param_t, index)
    elseif index == TabIndex.cross_air_war then
        self:OnFlush_CrossAirWar(param_t)
    end
end


function CultivationView:OpenCallBack()
    self.is_first_equip = true
    CrossAirWarWGCtrl.Instance:SendCSCrossAirWarOperate(CROSS_AIR_WAR_OPERATE_TYPE.STATUS_INFO)
end

function CultivationView:CloseCallBack()

end

function CultivationView:GetUISceneEffectsTransform()
	local ui_scene_cfg = Scene.Instance:GetUISceneCfg(UI_SCENE_TYPE.DEFAULT)
	if not IsEmptyTable(ui_scene_cfg) then
		return Scene.Instance:GetUIEffectsTransform(ui_scene_cfg.asset_name)
	end
	return nil
end