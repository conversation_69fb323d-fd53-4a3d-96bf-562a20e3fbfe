YanYuGeEntranceView = YanYuGeEntranceView or BaseClass(SafeBaseView)

function YanYuGeEntranceView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_entrance_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function YanYuGeEntranceView:OpenCallBack()
end

function YanYuGeEntranceView:LoadCallBack()

    local bundle, asset = ResPath.GetRawImagesJPG("a3_yyg_bj3")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    XUI.AddClickEventListener(self.node_list.btn_privilege, BindTool.Bind(self.OnClickPrivilegeBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_exchange_shop, BindTool.Bind(self.OnClickExchangeShopBtn, self))
    XUI.AddClickEventListener(self.node_list.score_bg, BindTool.Bind(self.OnClickScoreBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_noble, BindTool.Bind(self.OnClickNobleBtn, self))

    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
    local item_cfg = ItemWGData.Instance:GetItemConfig(show_item)
    if item_cfg then
        self.node_list.score_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    local other_remind_list = {RemindName.YanYuGe_Privilege, RemindName.YanYuGe_Shop, RemindName.YanYuGe_Noble_Privilege}
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end

    --入口屏蔽功能不屏蔽
    --FunOpen.Instance:RegisterFunUi(FunName.YanYuGePrivilegeView, self.node_list["yytq_root"])
    FunOpen.Instance:RegisterFunUi(FunName.YanYuGeExchangeShopView, self.node_list["tzsd_root"])
    --FunOpen.Instance:RegisterFunUi(FunName.YanYuGeNobleView, self.node_list["yygz_root"])
    self.node_list["yygz_root"]:SetActive(false)
    self.node_list["yytq_root"]:SetActive(false)
end

function YanYuGeEntranceView:ReleaseCallBack()
    if self.remind_callback then
        RemindManager.Instance:UnBind(self.remind_callback)
        self.remind_callback = nil
    end
end

function YanYuGeEntranceView:ShowIndexCallBack()
    if self.node_list.view_root and self.node_list.layout_a3_common_top_panel then
        self.node_list.view_root.canvas_group.alpha = 0
        self.node_list.layout_a3_common_top_panel:CustomSetActive(false)

        ReDelayCall(self, function () 
            self.node_list.view_root.canvas_group:DoAlpha(0, 1, 0.8):SetEase(DG.Tweening.Ease.Linear):OnComplete(function ()
                self.node_list.layout_a3_common_top_panel:CustomSetActive(true)
            end)
        end, 0.6, "YanYuGeEntranceViewShow")

        local bundle, asset = ResPath.GetUIEffect("UI_yyxy_dakai")
        EffectManager.Instance:PlayAtTransform(bundle, asset, self.root_node_transform.transform, 1.6, nil, nil, nil, nil, function ()
            -- self.node_list.layout_a3_common_top_panel:CustomSetActive(true)
        end)
    end
end

function YanYuGeEntranceView:OnFlush()
    local score = YanYuGeWGData.Instance:GetCurScore()
    self.node_list.cur_score.tmp.text = score
end

function YanYuGeEntranceView:OnClickPrivilegeBtn()
    ViewManager.Instance:Open(GuideModuleName.YanYuGePrivilegeView)
end

function YanYuGeEntranceView:OnClickExchangeShopBtn()
    ViewManager.Instance:Open(GuideModuleName.YanYuGeExchangeShopView)
end

function YanYuGeEntranceView:OnClickScoreBtn()
    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")

    if show_item and show_item > 0 then
        TipWGCtrl.Instance:OpenItem({ item_id = show_item })
    end
end

function YanYuGeEntranceView:OnClickNobleBtn()
    ViewManager.Instance:Open(GuideModuleName.YanYuGeNobleView)
end

function YanYuGeEntranceView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.YanYuGe_Privilege then
        self.node_list.btn_privilege_remind:SetActive(num > 0)
    elseif remind_name == RemindName.YanYuGe_Shop then
        self.node_list.btn_exchange_shop_remind:SetActive(num > 0)
    elseif remind_name == RemindName.YanYuGe_Noble_Privilege then
        self.node_list.btn_noble_remind:SetActive(num > 0)
    end
end