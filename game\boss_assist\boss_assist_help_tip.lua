BossAssistHelpTip = BossAssistHelpTip or BaseClass(SafeBaseView)
function BossAssistHelpTip:__init()
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_assist_help_tip")
    self.view_layer = UiLayer.PopWhite
	self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
end

function BossAssistHelpTip:LoadCallBack()
    local info = Split(Language.BossAssist.BossAssistTips, "\n")
    self.node_list["txt_tip1"].text.text = info[1]
    self.node_list["txt_tip2"].text.text = info[2]

end
function BossAssistHelpTip:OpenCallBack()
    self:Flush()
end

function BossAssistHelpTip:OnFlush()
    --local assist_info = BossAssistWGData.Instance:GetAssistInfo()
    --local max_num = VipPower.Instance:GetParam(VipPowerId.reward_shengwang_day_max)
    
    local cur_num = RoleWGData.Instance.role_vo.day_assist_shengwang
    self.node_list["txt_today_num"].text.text = ToColorStr(cur_num, COLOR3B.D_GREEN) .. "/" .. BossAssistView.MAX_DAY_ASSIST_SHENGWANG
end