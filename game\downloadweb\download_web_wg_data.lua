DownLoadWebWGData = DownLoadWebWGData or BaseClass()

function DownLoadWebWGData:__init()
	if DownLoadWebWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[DownLoadWebWGData] attempt to create singleton twice!")
		return
	end

	DownLoadWebWGData.Instance = self

	self:InitCfg()
	self.agent_down_reward_status = 0
	RemindManager.Instance:Register(RemindName.DownLoadWeb, BindTool.Bind(self.ShowDownLoadWebRemind, self))
end

function DownLoadWebWGData:__delete()
	DownLoadWebWGData.Instance = nil
	self.agent_down_reward_status = nil
end

function DownLoadWebWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	self.agent_down_reward = cfg.agent_down_reward
end

function DownLoadWebWGData:SetAgentRewardInfo(protocol)
	self.agent_down_reward_status = protocol.agent_down_reward_status
end

function DownLoadWebWGData:GetAgentRewardInfo(protocol)
	return self.agent_down_reward_status
end

function DownLoadWebWGData:GetCurSpidInfo()
	return self.agent_down_reward
end

function DownLoadWebWGData:GetDownLoadBtnState()
	local btn_sate = false
	local cur_state = self:GetAgentRewardInfo()
	if GLOBAL_CONFIG.param_list.is_open_download_other_game == 1 then
		if cur_state ~= AGENT_DOWN_REWARD_STATUS.HAS_GET then
			btn_sate = true
		end
	end

	return btn_sate
end

function DownLoadWebWGData:ShowDownLoadWebRemind()
	local cur_state = self:GetAgentRewardInfo()
	if cur_state == AGENT_DOWN_REWARD_STATUS.CAN_GET then
		return 1
	end

	return 0
end