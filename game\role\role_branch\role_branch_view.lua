------------------------------------------------------------
--万象View-称号、动作、天幕背景
------------------------------------------------------------
RoleBranchView = RoleBranchView or BaseClass(SafeBaseView)
function RoleBranchView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.cheng_hao, "uis/view/chenghao_prefab", "layout_chenghao")
	self:AddViewResource(TabIndex.sky_curtain, ResPath.CommonBundleName, "layout_a3_background_common_panel")
	self:AddViewResource(TabIndex.sky_curtain, "uis/view/chenghao_prefab", "layout_role_background")
	self:AddViewResource(TabIndex.custom_action, "uis/view/custom_action_ui_prefab", "layout_custom_action")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self.default_index = TabIndex.cheng_hao
	self.remind_tab = {
		{ RemindName.PlayerTitle },
		{ RemindName.Role_Background },
		{ RemindName.CustomAction },
	}
end

function RoleBranchView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.Role.TabSub4, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.RoleBranchView, self.tabbar)

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function RoleBranchView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:ReleaseTitleCallBack()
	self:ReleaseBackgroundCallBack()
	self:ActionReleaseCallBack()
end

function RoleBranchView:LoadIndexCallBack(index)
	if index == TabIndex.cheng_hao then
		self:LoadTitleView()
	elseif index == TabIndex.sky_curtain then
		self:LoadBackgroundCallBack()
	elseif index == TabIndex.custom_action then
		self:ActionInitView()
	end
end

function RoleBranchView:OpenCallBack()

end

function RoleBranchView:ShowIndexCallBack(index)
	local title = Language.Role.BackgroundName
	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg1")
	if index == TabIndex.cheng_hao then
		self:ShowTitleIndexCallBack()
		title = Language.Role.title_name
		bundle, asset = ResPath.GetRawImagesPNG("a3_ch_bg")
	elseif index == TabIndex.sky_curtain then
		self:OpenBackgroundCallBack()
		title = Language.Role.BackgroundName
		bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg1")
	elseif index == TabIndex.custom_action then
		self:ActionShowIndexCallBack()
		title = Language.ViewName.CustomAction
		bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg1")
	end
	self.node_list.title_view_name.text.text = title

	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function RoleBranchView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.cheng_hao then
				if v.title_id then
					self:SetJumpParam(tonumber(v.title_id))
				end

				self:OnFlushTitle()
			elseif index == TabIndex.sky_curtain then
				self:OnFlushBackground(param_t, index)
			elseif index == TabIndex.custom_action then
				self.action_flush_wait_flag = true
				self:ActionSelectToggle(v.force_big_type, v.force_small_type, false)
			end
		elseif "play_uplevel_effect" == k then
			self:PlayUpLevelEffect()
		elseif k == "protocol_change" then
			self.action_flush_wait_flag = true
			self:FlushActionToggleAllData()
			self:ActionSelectToggle(nil, nil, true)
		end
	end
end

function RoleBranchView:CloseCallBack()
	self:CloseTitleCallBack()
	self:CloseBackgroundCallBack()
	self:ActionCloseCallBack()
end
