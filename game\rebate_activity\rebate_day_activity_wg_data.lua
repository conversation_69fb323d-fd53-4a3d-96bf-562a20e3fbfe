RebateDayActivityWGData = RebateDayActivityWGData or BaseClass()

RebateDayActivityWGData.OPERA_TYPE = {
    GET_REWARD = 0,
}

function RebateDayActivityWGData:__init()
    if nil ~= RebateDayActivityWGData.Instance then
        ErrorLog("[RebateDayActivityWGData]:Attempt to create singleton twice!")
    end
    RebateDayActivityWGData.Instance = self
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.RebateDayRemind, BindTool.Bind(self.GetRebateDayRed, self))
    RebateActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FANLIBAOXIAN, {[1] = MERGE_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetActIsOpen, self))
end

function RebateDayActivityWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.RebateDayRemind)

    RebateDayActivityWGData.Instance = nil
end

function RebateDayActivityWGData:InitConfig()
    local config = ConfigManager.Instance:GetAutoConfig("fanlibaoxiancfg_auto")
    self.day_activity_config = config.activity_cfg
    self.other_cfg = config.other[1]
end

function RebateDayActivityWGData:GetRewardBeishu()
    return self.day_activity_config.reward_beishu
end

function RebateDayActivityWGData:GetRebateDayRed()
    if not RebateActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FANLIBAOXIAN) then
        return 0
    end
    local box_info = self:GetBoxInfo()
    for k, v in pairs(box_info) do
        if v.can_get == 1 and v.is_get == 0 then
            return 1
        end
    end
    return 0
end

function RebateDayActivityWGData:GetActIsOpen()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local level = RebateActivityWGData.Instance:GetTabOpenLevelByAct(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FANLIBAOXIAN) --标签开启等级
    if role_lv >= self.other_cfg.limit_level and role_lv >= level then
        return true
    end
    return false
end

function RebateDayActivityWGData:SaveData(protocol)
    --print_warning("7日返利信息，当前天数",protocol.open_day,"当前消费总仙玉", protocol.total_gold_num,"各个宝箱信息", protocol.baoxiang_info)
    local baoxiang_info = self:GetBoxInfo()
    if not IsEmptyTable(baoxiang_info) then
        for k,v in pairs(baoxiang_info) do
            if v.is_get == 0 and protocol.baoxiang_info[k] == 1 then
                self.last_get_index = k
            end
        end
    end

    self.baoxiang_info = protocol.baoxiang_info
    self.cur_open_day = protocol.open_day
    self.total_gold_num = protocol.total_gold_num
end

function RebateDayActivityWGData:GetCommonInfo()
    return self.cur_open_day or 0, self.total_gold_num or 0
end

function RebateDayActivityWGData:GetBoxInfo()
    return self.baoxiang_info or {}
end

function RebateDayActivityWGData:GetLastChangeIndex()
    return self.last_get_index
end

function RebateDayActivityWGData:GetTipsContent()
    return self.other_cfg.tips
end

function RebateDayActivityWGData:SaveGoldNum(num)
    self.last_get_gold_num = num
end

function RebateDayActivityWGData:GetGoldNum()
    return self.last_get_gold_num or 0
end
