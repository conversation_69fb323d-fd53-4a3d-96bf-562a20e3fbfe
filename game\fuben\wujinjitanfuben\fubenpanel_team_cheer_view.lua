-- 鼓舞
TeamFuBenCheerView = TeamFuBenCheerView or BaseClass(SafeBaseView)

function TeamFuBenCheerView:__init()
	--self:SetModal(true)
	self.default_index = 0
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/team_exp_fb_prefab", "layout_cheer_up")
	self.flag = true           -- 鼓舞百分百后自动关闭
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
end

function TeamFuBenCheerView:__delete()
end

function TeamFuBenCheerView:ReleaseCallBack()
end

function TeamFuBenCheerView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.FuBenPanel.GuWu 		--鼓舞
	XUI.AddClickEventListener(self.node_list.btn_guwu_coin, BindTool.Bind(self.OnClickGuWu, self,0,0,1))
	XUI.AddClickEventListener(self.node_list.btn_guwu_gold, BindTool.Bind(self.OnClickGuWu, self,1,1,0))
	XUI.AddClickEventListener(self.node_list.btn_hook1, BindTool.Bind(self.OnClickHook1, self))
	XUI.AddClickEventListener(self.node_list.btn_hook2, BindTool.Bind(self.OnClickHook2, self))

	local flag1,flag2 = FuBenWGData.Instance:GetGuWuData()
	self.is_tongbi = flag1 == 1
	self.is_gold = flag2 == 1
	self.node_list.hook1:SetActive(self.is_tongbi)
	self.node_list.hook2:SetActive(self.is_gold)

	local scene_type = Scene.Instance:GetSceneType()
	local show_hook = true
	if scene_type == SceneType.Guild_Invite then
		show_hook = false
	else
		show_hook = true
	end
	self.node_list.hook_box_1:SetActive(show_hook)
	self.node_list.hook_box_2:SetActive(show_hook)
end

function TeamFuBenCheerView:OpenCallBack()
	-- body
end
function TeamFuBenCheerView:CloseCallBack()
	FuBenWGCtrl.Instance:SendSetExpFBAutoGuwu(self.is_tongbi and 1 or 0,0)
	FuBenWGCtrl.Instance:SendSetExpFBAutoGuwu(self.is_gold and 1 or 0,1)
end
function TeamFuBenCheerView:OnClickHook1()
	-- self.is_gold = index == 2
	self.is_tongbi = not self.is_tongbi
	self.node_list.hook1:SetActive(self.is_tongbi)
end
function TeamFuBenCheerView:OnClickHook2()

	self.is_gold = not self.is_gold
	self.node_list.hook2:SetActive(self.is_gold)
end

function TeamFuBenCheerView:OnClickGuWu(is_cion,count1,count2)
	local confirm_func = function ()
		FuBenWGCtrl.Instance:SendFbGuwu(is_cion,count1,count2)
	end
	if is_cion == 1 then
		local gold_guwu_cost = 0
		local cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
		local _,count_combine = FuBenWGData.Instance:SetCombineMark()
		gold_guwu_cost = cfg.gold_guwu_cost
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.Guild_Invite then
			local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
			gold_guwu_cost = other_cfg.gold_guwu_cost
		end
		local guwu_need_xianyu = gold_guwu_cost * count_combine
		local bind_gold_num = RoleWGData.Instance:GetRoleInfo().bind_gold
		local gold_num = RoleWGData.Instance:GetRoleInfo().gold
		if guwu_need_xianyu > bind_gold_num and gold_num >= guwu_need_xianyu then
			FuBenWGCtrl.Instance:OpenExpFuBenGoldGuWuTip(confirm_func)
			return
		elseif  guwu_need_xianyu > bind_gold_num and guwu_need_xianyu > gold_num then
			VipWGCtrl.Instance:OpenTipNoGold()
			return
		end
	end
	confirm_func()
end

function TeamFuBenCheerView:OnFlush(param, index)
	local is_gold = self.is_gold and 1 or 0
	local scene_type = Scene.Instance:GetSceneType()
 	local cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
 	local coin_guwu_cost = 0
 	local gold_guwu_cost = 0
 	if scene_type == SceneType.Guild_Invite then
 		local invite_cfg = GuildInviteWGData.Instance:GetOtherCfg()
 		coin_guwu_cost = invite_cfg.coin_guwu_cost
 		gold_guwu_cost = invite_cfg.gold_guwu_cost
 	else
 		coin_guwu_cost = cfg.coin_guwu_cost
 		gold_guwu_cost = cfg.gold_guwu_cost
 	end

 	local _,count_combine = FuBenWGData.Instance:SetCombineMark()
	self.node_list.lbl_coin_cost.text.text = coin_guwu_cost * count_combine --.. "/" .. Language.Common.UnitName[6]
	local guwu_need_xianyu = gold_guwu_cost * count_combine
	self.node_list.lbl_gold_cost.text.text = guwu_need_xianyu --.. "/" .. Language.Common.UnitName[6]

	local bind_gold_num = RoleWGData.Instance:GetRoleInfo().bind_gold
	local gold_num = RoleWGData.Instance:GetRoleInfo().gold
	local res_name = ""
	if bind_gold_num >= guwu_need_xianyu then
		res_name = "a3_huobi_bangyu"
	else
		res_name = "a3_huobi_xianyu"
	end
	local bundle, asset = ResPath.GetCommonIcon(res_name)
	self.node_list.lbl_gold_cost_Image.image:LoadSprite(bundle, asset, function()
		self.node_list.lbl_gold_cost_Image.image:SetNativeSize()
	end)
	
	local role_info = FuBenWGData.Instance:GetFbGuWuInfo()
	local gold_guwu_count = role_info.gold_guwu_count
	local coin_guwu_count = role_info.coin_guwu_count

	local count1,count2 = role_info.coin_guwu_count,role_info.gold_guwu_count--WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()

	local add_per1 = WuJinJiTanWGData.Instance:GetGuWuAddPer(count1)
	local add_per2 = WuJinJiTanWGData.Instance:GetGuWuAddPer(count2)
	
	if scene_type == SceneType.Guild_Invite then
		add_per1 = GuildInviteWGData.Instance:GetGuWuAddPer(count1)
		add_per2 = GuildInviteWGData.Instance:GetGuWuAddPer(count2)
	end

	local attr_name = EquipmentWGData.GetFightAttrNameByAttrId(GameEnum.FIGHT_CHARINTATTR_TYPE_SHANGHAI_JC_PER)
	self.node_list["xiaoguo_desc1"].text.text = string.format( attr_name .. "+%d%%",add_per1)
	self.node_list["xiaoguo_desc2"].text.text = string.format( attr_name .. "+%d%%",add_per2)
	self.node_list["times1"].text.text = string.format(Language.FuBenPanel.ExpCheerCount,count1)
	self.node_list["times2"].text.text = string.format(Language.FuBenPanel.ExpCheerCount,count2)
	self.node_list.btn_guwu_gold:SetActive(count2 < 5)
	self.node_list.btn_guwu_coin:SetActive(count1 < 5)
	self.node_list.coin_finish:SetActive(count1 >= 5)
	self.node_list.gold_finish:SetActive(count2 >= 5)
	-- FuBenWGCtrl.Instance:SendFbGuwu(0)
end

function TeamFuBenCheerView:ShowIndexCallBack(index)
	self:Flush()
end

function TeamFuBenCheerView:OnClickCancel()
	self:Close()
end

function TeamFuBenCheerView:OpenCallBack()
	--AudioManager.Instance:PlayOpenCloseUiEffect()
end