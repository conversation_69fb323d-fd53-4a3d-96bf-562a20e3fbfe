HolyHeavenlyDomainWarRewardView = HolyHeavenlyDomainWarRewardView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainWarRewardView:__init()
    self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_war_reward")
end

function HolyHeavenlyDomainWarRewardView:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(HHDWarRewardListItemRender, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
		self.node_list.reward_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnScrollRectValueChange, self))
	end
end

function HolyHeavenlyDomainWarRewardView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function HolyHeavenlyDomainWarRewardView:OnFlush()
    local data_list = HolyHeavenlyDomainWGData.Instance:GetScoreRewardCfg()
    self.reward_list:SetDataList(data_list)
    self:SetSliderValue(data_list)
end

function HolyHeavenlyDomainWarRewardView:OnScrollRectValueChange()
    local grade_reward_item_width = 80
	local rect = self.node_list.reward_list.scroll_rect.content
	self.node_list.slider_pro.rect.sizeDelta = Vector2(rect.rect.size.x - grade_reward_item_width, 14)
	local pos_x =  RectTransform.GetAnchoredPositionXY(self.node_list.reward_list.scroll_rect.content) + grade_reward_item_width / 2
	RectTransform.SetAnchoredPositionXY(self.node_list.slider_pro.rect, pos_x, 11)
end

function HolyHeavenlyDomainWarRewardView:SetSliderValue(data_list)
    local score = HolyHeavenlyDomainWGData.Instance:GetScore()
    local slider_value = 0
    local count = #data_list
    local per_slider_value = 1 / count

    table.sort(data_list, SortTools.KeyLowerSorter("seq"))
    local last_score = data_list[0].need_score

    for i = 0, count do
        local v = data_list[i]

        if score < v.need_score then
            if score > last_score then
                local cell_value = v.need_score - last_score
                slider_value = slider_value + per_slider_value * (score - last_score) / cell_value
            end

            break
        end

        slider_value = slider_value + per_slider_value
        last_score = v.need_score
    end

    local diff_value = data_list[0].need_score > 0 and per_slider_value or 0
    self.node_list.slider_pro.slider.value = slider_value - diff_value
end

HHDWarRewardListItemRender = HHDWarRewardListItemRender or BaseClass(BaseRender)

function HHDWarRewardListItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_root)
        self.item:SetIsShowTips(false)
        self.item:UseNewSelectEffect(true)
        self.item:SetTipClickCallBack(BindTool.Bind1(self.ClickHandler, self))
    end
end

function HHDWarRewardListItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function HHDWarRewardListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local score = HolyHeavenlyDomainWGData.Instance:GetScore()
    local is_get = HolyHeavenlyDomainWGData.Instance:GetScoreRewardFlagBySeq(self.data.seq)
    local can_get = self.data.need_score <= score and not is_get
    self.node_list.flag_can_get:CustomSetActive(can_get)

    self.item:SetFlushCallBack(function ()
		self.item:ResetSelectEffect()
		self.item:SetSelectEffect(is_get)
	end)

    self.item:SetData(self.data.reward_item[0])
    self.node_list.desc_score.text.text = self.data.need_score or 0
end

function HHDWarRewardListItemRender:ClickHandler()
    if IsEmptyTable(self.data) then
        return
    end

    local score = HolyHeavenlyDomainWGData.Instance:GetScore()
    local is_get = HolyHeavenlyDomainWGData.Instance:GetScoreRewardFlagBySeq(self.data.seq)
    local can_get = self.data.need_score <= score

    if not is_get and can_get then
        HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.FETCH_SCORE_REWARD, self.data.seq)
    else
        if #self.data.reward_item > 1 then
            local data_list =
            {
                view_type = RewardShowViewType.Normal,
                reward_item_list = self.data.reward_item
            }
            RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
        else
            TipWGCtrl.Instance:OpenItem(self.data.reward_item[0])
        end
    end
end