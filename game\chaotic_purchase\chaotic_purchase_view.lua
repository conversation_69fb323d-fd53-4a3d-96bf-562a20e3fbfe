ChaoticPurchaseView = ChaoticPurchaseView or BaseClass(SafeBaseView)

function ChaoticPurchaseView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/chaotic_purchase_ui_prefab", "layout_chaotic_purchase")
end

function ChaoticPurchaseView:__delete()
end

function ChaoticPurchaseView:ReleaseCallBack()
    if self.chaotic_purchase_page_list then
        for _, v in pairs(self.chaotic_purchase_page_list) do
            v:DeleteMe()
        end
        self.chaotic_purchase_page_list = nil
    end

    self.select_page_index = 0
end

function ChaoticPurchaseView:OpenCallBack()
    ChaoticPurchaseWGCtrl.Instance:ReqChaoticPurchaseInfo(CHAOTIC_GIFT_OPERATE_TYPE.CHAOTIC_GIFT_OPERATE_TYPE_RMB_BUY_INFO)
end

function ChaoticPurchaseView:CloseCallBack()
    ChaoticPurchaseWGData.Instance:SetRecordShowList({})
end

function ChaoticPurchaseView:LoadCallBack()
    self.select_page_index = 0

    self.chaotic_purchase_page_list = {}
    
    for i = 1, ChaoticPurchaseWGData.Max_Page_Count do
         self.chaotic_purchase_page_list[i] = ChaoticPurchasePage.New(self.node_list["item_page_" .. i])
         self.chaotic_purchase_page_list[i]:SetIndex(i)
         self.chaotic_purchase_page_list[i]:AddClickEventListener(BindTool.Bind(self.OnClickPage, self))
    end
end

function ChaoticPurchaseView:ShowIndexCallBack()
     
end

function ChaoticPurchaseView:OnFlush(param_t, index)
    local data_list = ChaoticPurchaseWGData.Instance:GetShowBuyList()
    ChaoticPurchaseWGData.Instance:SetRecordShowList(data_list)

    local page_list = self.chaotic_purchase_page_list
    for i = 1, #page_list do
        page_list[i]:SetData(data_list[i])
    end

    if self.select_page_index <= 0 then
        local select_index = 1
        for i, v in ipairs(data_list) do
            local all_buy = ChaoticPurchaseWGData.Instance:GetBuyStateBySeq(v.seq)
            if not all_buy then
                select_index = i
                break
            end
        end

        self:OnClickPage(page_list[select_index])
    end
end

function ChaoticPurchaseView:OnClickPage(page)
    -- if self.select_page_index == page:GetIndex() then
    --     return
    -- end

    self.select_page_index = page:GetIndex()
    self:FlushChaoticPurchasePage()
end

function ChaoticPurchaseView:FlushChaoticPurchasePage()
    local move_x = 0
    local conten_move_x = -90
    local spacing = 14

    local page_list = self.chaotic_purchase_page_list
    local select_index = self.select_page_index

    for i = 1, #page_list do
        page_list[i]:ShowInfoPanel(i == select_index)
        page_list[i]:DoMove(move_x)
        move_x = move_x + page_list[i]:GetWidth() + spacing
        if i < select_index then
            conten_move_x = move_x
        end
    end

    local rect = self.node_list.content_root.rect
    RectTransform.SetSizeDeltaXY(rect, move_x - spacing, 504)                       --大小

    local has_on = false
    for i = 1, #page_list do
        if page_list[i]:GetIsOn() then
            has_on = true
            break
        end
    end

    if not has_on then
        conten_move_x = 0
    elseif conten_move_x > 0 then
        conten_move_x = (conten_move_x - spacing) <= 90 and conten_move_x - spacing or 90
    end
    rect:DOAnchorPosX(-conten_move_x, 0.5)
    
end


---------
ChaoticPurchasePage = ChaoticPurchasePage or BaseClass(BaseRender)

function ChaoticPurchasePage:__init()
    self.m_width = 214
    self.m_height = 584
    self.m_ex_width = 212
    self.m_ison = false
    self.cell_list = {}
    self.reward_list = {}

    for i = 1, 2 do
        self.cell_list[i] = ItemCell.New(self.node_list["cell_" .. i])
    end

    for i = 1, 6 do
       self.reward_list[i] = ItemCell.New(self.node_list["reward_list_" .. math.ceil(i / 3)])
    end

    XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
end

function ChaoticPurchasePage:__delete()
    if self.cell_list then
        for k, v in pairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = nil
    end

    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

    if self.reward_list then
        for k, v in pairs(self.reward_list) do
            v:DeleteMe()
        end
        self.reward_list = nil
    end

end

function ChaoticPurchasePage:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local price = ""
    local already_buy_times = ChaoticPurchaseWGData.Instance:GetBuyCountBySeq(self.data.seq)
    for i = 1, 2 do
        self.node_list["name_" .. i].text.text = self.data.name
        price = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, self.data.seq)
        self.node_list["price_num_" .. i].text.text = price
        self.node_list["buy_times_" .. i].text.text = string.format(Language.ChaoticPurchase.BuyTimesStr, (self.data.buy_times - already_buy_times), self.data.buy_times)
        self.cell_list[i]:SetData(self.data.show_item)
    end

    XUI.SetButtonEnabled(self.node_list["buy_btn"], self.data.buy_times > already_buy_times)
    local data_list = {}
    for i = 0, #self.data.reward_item do
        table.insert(data_list, self.data.reward_item[i])
    end

    self.node_list.reward_list_2:SetActive(#data_list > 3)
    for i=1, #self.reward_list do
        if data_list[i] then
            self.reward_list[i]:SetData(data_list[i])
            self.reward_list[i]:SetActive(true)
            self.reward_list[i]:SetEffectRootEnable(true)
        else
            self.reward_list[i]:SetActive(false)
        end
    end
end

function ChaoticPurchasePage:ShowInfoPanel(is_show)
    self.m_ison = is_show and not self.m_ison or false 
    local move_x = self.m_ison and 0 or -self.m_ex_width
    local width = self.m_ison and 424 or 214
    if self.node_list.info_root then
        self.node_list.info_root.rect:DOAnchorPosX(move_x, 0.1)
    end

    if self.node_list.mid_bg_1 then
       self.tween = self.node_list["mid_bg_1"].rect:DOSizeDelta(Vector2(width, 504), 0.1)
    end

    if self.m_ison then
        self.node_list.close_state:SetActive(false)
        self.node_list.open_state:SetActive(true)
        self.node_list.name_bg_2:SetActive(true)
    else
        self.node_list.name_bg_2:SetActive(false)
        self.tween:OnComplete(function()
            self.node_list.close_state:SetActive(true)
            self.node_list.open_state:SetActive(false)
        end)
    end
end

function ChaoticPurchasePage:DoMove(pos_x)
    local root = self:GetView()
    root.rect:DOAnchorPosX(pos_x, 0.1)
end

function ChaoticPurchasePage:GetWidth()
    if self.m_ison then
        return self.m_width + self.m_ex_width
    else
        return self.m_width
    end
end

function ChaoticPurchasePage:GetIsOn()
    return self.m_ison
end

function ChaoticPurchasePage:OnClickBuy()
    if IsEmptyTable(self.data) then
        return
    end

    local already_buy_times = ChaoticPurchaseWGData.Instance:GetBuyCountBySeq(self.data.seq)
    if already_buy_times >= self.data.buy_times then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ChaoticPurchase.AllBuy)
        return
    end

    RechargeWGCtrl.Instance:Recharge(self.data.rmb_price, self.data.rmb_type, self.data.seq)
end