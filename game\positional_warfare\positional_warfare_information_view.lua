-- 阵地信息界面

PositionalWarfareInformationView = PositionalWarfareInformationView or BaseClass(SafeBaseView)

function PositionalWarfareInformationView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_information_view")
end

function PositionalWarfareInformationView:SetDataAndOpen(data)
	self.show_data = data

	if self.show_data ~= nil then
        PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.LAND_INFO, self.show_data.seq)
        if not self:IsOpen() then
			self:Open()
		else
			self:Flush()
		end
	end
end

function PositionalWarfareInformationView:AutoSendRequest()
    if self.show_data ~= nil then
        PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.LAND_INFO, self.show_data.seq)
    end
end

function PositionalWarfareInformationView:LoadCallBack()
    if not self.left_nav_list then
        self.left_nav_list = AsyncListView.New(PWCityInfoItemCellRender, self.node_list.left_nav_list)
        self.left_nav_list:SetStartZeroIndex(false)
        self.left_nav_list:SetSelectCallBack(BindTool.Bind(self.OnSelectLeftNavHandler, self))
    end

    if not self.drop_item_list then
        self.drop_item_list = AsyncListView.New(ItemCell, self.node_list.drop_item_list)
        self.drop_item_list:SetStartZeroIndex(true)
    end

    if not self.oner_item_list then
        self.oner_item_list = AsyncListView.New(ItemCell, self.node_list.oner_item_list)
        self.oner_item_list:SetStartZeroIndex(true)
    end

    if not self.boss_drop_item_list then
        self.boss_drop_item_list = AsyncListView.New(ItemCell, self.node_list.boss_drop_item_list)
        self.boss_drop_item_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickTipBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_tiaozhan, BindTool.Bind(self.OnClickTiaoZhanBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_person_rank, BindTool.Bind(self.OnClickPersonRankBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_defeat_rank, BindTool.Bind(self.OnClickDefeatRankBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_zhaoji, BindTool.Bind(self.OnClickZhaoJiBtn, self))

    self.select_data = {}
    self.select_index = 1
end

function PositionalWarfareInformationView:ReleaseCallBack()
    if  self.left_nav_list then
        self.left_nav_list:DeleteMe()
        self.left_nav_list = nil
    end

    if self.drop_item_list then
        self.drop_item_list:DeleteMe()
        self.drop_item_list = nil
    end

    if self.oner_item_list then
        self.oner_item_list:DeleteMe()
        self.oner_item_list = nil
    end

    if self.boss_drop_item_list then
        self.boss_drop_item_list:DeleteMe()
        self.boss_drop_item_list = nil
    end

    if CountDownManager.Instance:HasCountDown("pw_zhaoji") then
        CountDownManager.Instance:RemoveCountDown("pw_zhaoji")
    end

    self.select_data = nil
end

function PositionalWarfareInformationView:OnFlush()
    if IsEmptyTable(self.show_data) then
        return
    end

    local nav_data_list = {}
    table.insert(nav_data_list, self.show_data)

    self.node_list.title_view_name.text.text = self.show_data.land_name
    local boss_data_list = PositionalWarfareWGData.Instance:GetCurMonsterListCfg(self.show_data.seq)

    -- if not IsEmptyTable(boss_data_list) then
    --     for k, v in pairs(boss_data_list) do
    --         table.insert(nav_data_list, v)
    --     end
    -- end

    -- self.left_nav_list:SetDataList(nav_data_list)

    local target_data_list = {}
    if not IsEmptyTable(boss_data_list) then
        --根据boss类型1>2>3,从高到底排列  未击败>归属中>已击败 状态相同时，根据boss类型1>2>3,从高到底排列
        local target_data_list = TableCopy(boss_data_list)
        target_data_list = ListIndexFromZeroToOne(target_data_list)

        for k, v in pairs(target_data_list) do
            local boss_active = PositionalWarfareWGData.Instance:GetCityBossActiveState(v.land_seq, v.seq)
            local sort = 0
            local color_value = - v.monster_color * 100 

            if boss_active then
                -- local boss_info = PositionalWarfareWGData.Instance:GetMonsterCityBossInfo(v.land_seq, v.seq)
                -- local camp = boss_info and boss_info.owner_camp or -1

                -- if camp >= 0 then
                --     sort = 10000 + color_value - v.seq
                -- else
                --     sort = 1000000 + color_value - v.seq
                -- end

                sort = 10000 + color_value - v.seq
            else
                sort = color_value - v.seq
            end
            
            v.sort = sort
        end

        table.sort(target_data_list, SortTools.KeyUpperSorter("sort"))

        for i = 1, #target_data_list do
            table.insert(nav_data_list, target_data_list[i])
        end

        -- for k, v in pairs(target_data_list) do
        --     print_error(v.land_seq, v.seq, v.monster_color, v.sort)
        --     table.insert(nav_data_list, v)
        -- end
    end

    self.left_nav_list:SetDataList(nav_data_list)
    self.left_nav_list:JumpToIndex(self.select_index)

    local max_tired = PositionalWarfareWGData.Instance:GetOtherAttrValue("max_tired")
    local tired = PositionalWarfareWGData.Instance:GetTired()
    local color = tired < max_tired and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.desc_angry_value.text.text = string.format(Language.PositionalWarfare.TiredValue, color, tired, max_tired)

    self:FlushZhaoJiInfo()
end

function PositionalWarfareInformationView:OnSelectLeftNavHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return 
    end

    local data = item.data
    self.select_data = data
    self.select_index = item.index
    self:FlushMidInfo(data)
end

function PositionalWarfareInformationView:FlushMidInfo(data)
    local is_city = data.scene_id ~= nil
    self.node_list.city_infomation_panel:CustomSetActive(is_city)
    self.node_list.boss_info_panel:CustomSetActive(not is_city)

    -- local total_num = PositionalWarfareWGData.Instance:GetCityEnterTotalNum(data.seq)
    -- local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
    -- local my_camp_num = PositionalWarfareWGData.Instance:GetCityEnterCampTotalNum(data.seq, my_camp)
    -- self.node_list.desc_number_num.text.text = string.format(Language.PositionalWarfare.FigthPlayerNum, my_camp_num, total_num)

    if is_city then
        -- 城市信息
        self.node_list.desc_city_name.text.text = data.land_name
        --珍惜掉落
        self.drop_item_list:SetDataList(data.rare_drop_reward)
        -- 归属掉落
        self.oner_item_list:SetDataList(data.attribution_drop_reward)

        local city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(data.seq)
    
        local owner_camp = city_info and city_info.owner_camp or -1
        local tem_owner_camp = city_info and city_info.tem_owner_camp or -1  --临时占领

        local target_camp = -1

        local camp_name = Language.PositionalWarfare.Neutral
        if tem_owner_camp >= 0 then
            target_camp = tem_owner_camp
        elseif owner_camp >= 0 then
            target_camp = owner_camp
        end

        if target_camp >= 0 then
            local cur_camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(target_camp)
            camp_name = cur_camp_cfg.camp_name

            self.node_list.icon_city_owner.image:LoadSprite(ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. cur_camp_cfg.camp_sign))
        end

        self.node_list.icon_city_owner:CustomSetActive(target_camp >= 0)
        self.node_list.desc_city_owner_name.text.text = camp_name -- string.format(Language.PositionalWarfare.AttributionDesc, camp_name)

        local camp_score_list = city_info and city_info.camp_score_list or {}
        local camp_list = PositionalWarfareWGData.Instance:GetCurCampListCfg()

        -- 分数需要从高到低
        -- for i = 1, 4 do
        --     local camp_id = i - 1
        --     local data = camp_list[camp_id]

        --     if IsEmptyTable(data) then
        --         self.node_list["city_name" .. i]:CustomSetActive(false)
        --     else
        --         local score = camp_score_list[camp_id] or 0
        --         self.node_list["city_name" .. i].text.text = data.camp_name
        --         self.node_list["city_score" .. i].text.text = string.format(Language.PositionalWarfare.InfomationCampScore, score)
        --         self.node_list["city_name" .. i]:CustomSetActive(true)
        --     end
        -- end

        local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()

        local target_score_data = {}
        for i = 1, 4 do
            local camp_id = i - 1
            local data = camp_list[camp_id]
            
            if not IsEmptyTable(data) then
                local score = camp_score_list[camp_id] or 0
                table.insert(target_score_data, {score = score, camp_name = data.camp_name, camp_sign = data.camp_sign, is_my_camp = my_camp == data.camp})
            end
        end

        if not IsEmptyTable(target_score_data) then
            table.sort(target_score_data, SortTools.KeyUpperSorter("score")) 
        end

        for i = 1, 4 do
            local data = target_score_data[i]

            if IsEmptyTable(data) then
                self.node_list["city_name" .. i]:CustomSetActive(false)
            else
                local score = target_score_data[i].score
                local camp_name = data.camp_name
                local is_my_camp = data.is_my_camp

                self.node_list["city_name" .. i].text.text = is_my_camp and ToColorStr(camp_name, COLOR3B.C7) or camp_name

                local score_str = string.format(Language.PositionalWarfare.InfomationCampScore, score)
                self.node_list["city_score" .. i].text.text = is_my_camp and ToColorStr(score_str, COLOR3B.C7) or score_str
                self.node_list["city_name" .. i]:CustomSetActive(true)
                self.node_list["city_icon" .. i].image:LoadSprite(ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. data.camp_sign))
            end
        end

        -- 场景中本服人数/ 场景中总人数
        -- local total_num = PositionalWarfareWGData.Instance:GetCityEnterTotalNum(data.seq)
        -- self.node_list.desc_number_num.text.text = string.format(Language.PositionalWarfare.FigthNumberNum, total_num)

        local total_num = PositionalWarfareWGData.Instance:GetCityEnterTotalNum(data.seq)
        local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
        local my_camp_num = PositionalWarfareWGData.Instance:GetCityEnterCampTotalNum(data.seq, my_camp)
        self.node_list.desc_number_num.text.text = string.format(Language.PositionalWarfare.FigthPlayerNum, my_camp_num, total_num)
    else
        -- boss信息
        local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(data.monster_id)

        if not IsEmptyTable(boss_cfg) then
            self.node_list.desc_boss_title.text.text = boss_cfg.name
            self.node_list.boss_name.text.text = boss_cfg.name
            self.node_list.boss_level.text.text = string.format(Language.Common.Level1, boss_cfg.level)

            local boss_info = PositionalWarfareWGData.Instance:GetMonsterCityBossInfo(data.land_seq, data.seq)

            local bundle,asset = ResPath.GetBossIcon("wrod_boss_".. boss_cfg.small_icon)
			self.node_list.boss_head.image:LoadSprite(bundle,asset,function()
				self.node_list.boss_head.image:SetNativeSize()
			end)
        
            local hp_scale = boss_info and boss_info.hp_scale or 100
            self.node_list.boss_hp.image.fillAmount = hp_scale / 100
            local camp = boss_info and boss_info.owner_camp or -1

            if camp >= 0 then
                local cur_camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(camp)
                self.node_list.boss_state.text.text = cur_camp_cfg.camp_name
            else
                self.node_list.boss_state.text.text = Language.PositionalWarfare.Neutral
            end

            self.node_list.desc_boss_score.text.text = string.format(Language.PositionalWarfare.LandScore, data.kill_capture_score)
            self.boss_drop_item_list:SetDataList(data.rare_drop_reward)

            -- 进入的   本服人数/总人数
            -- self.node_list.desc_number_num.text.text = string.format(Language.PositionalWarfare.BossFigthNumberNum, boss_info and boss_info.camp_player_num or 0)

            local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
            local my_camp_num = (boss_info.camp_player_num_list or {})[my_camp] or 0
            self.node_list.desc_number_num.text.text = string.format(Language.PositionalWarfare.FigthPlayerNum, my_camp_num, boss_info and boss_info.camp_player_num or 0)

            self.node_list.desc_boss_score_info.text.text = string.format(Language.PositionalWarfare.LandScoreDescription, data.kill_tired)
        end
    end
end

function PositionalWarfareInformationView:FlushZhaoJiInfo()
    if CountDownManager.Instance:HasCountDown("pw_zhaoji") then
        CountDownManager.Instance:RemoveCountDown("pw_zhaoji")
    end

    local call_land_seq, call_cd_time = PositionalWarfareWGData.Instance:GetCallInfo()

    if call_land_seq == self.show_data.seq then
        local server_time = TimeWGCtrl.Instance:GetServerTime()

        if call_cd_time > server_time then
            self.node_list.desc_btn_zhaoji.text.text = string.format(Language.PositionalWarfare.DescBtnZhaoJiCD, TimeUtil.FormatSecondDHM2(math.floor(call_cd_time - server_time)))
            CountDownManager.Instance:AddCountDown("pw_zhaoji", 
            function (elapse_time, total_time)
                self.node_list.desc_btn_zhaoji.text.text = string.format(Language.PositionalWarfare.DescBtnZhaoJiCD, TimeUtil.FormatSecondDHM2(math.floor(total_time - elapse_time)))
            end,
            function ()
                self.node_list.desc_btn_zhaoji.text.text = Language.PositionalWarfare.DescBtnZhaoJi
            end, call_cd_time, nil, 1)
        else
            self.node_list.desc_btn_zhaoji.text.text = Language.PositionalWarfare.DescBtnZhaoJi
        end
    else
        self.node_list.desc_btn_zhaoji.text.text = Language.PositionalWarfare.DescBtnZhaoJi
    end
end

function PositionalWarfareInformationView:OnClickTipBtn()
    RuleTip.Instance:SetContent(Language.PositionalWarfare.InformationTipContent, Language.PositionalWarfare.InformationTipTitle)
end

function PositionalWarfareInformationView:OnClickTiaoZhanBtn()
    if not PositionalWarfareWGData.Instance:IsInFightTime() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.PositionalWarfare.EnterLandNotFightTimeTip)
        return
    end

    if IsEmptyTable(self.show_data) then
        return
    end

    -- 当前地块是否能攻击
    local can_enter = PositionalWarfareWGData.Instance:IsCanEnter(self.show_data)

    if can_enter then
        local is_city = self.select_data.scene_id ~= nil
        PositionalWarfareWGData.Instance:SetEnterSceneSelectDataCache(self.show_data.seq, is_city and -1 or self.select_data.seq)

        local scene_type = Scene.Instance:GetSceneType()
        if scene_type == SceneType.CROSS_LAND_WAR then
            local scene_id = Scene.Instance:GetSceneId()

            if scene_id == self.show_data.scene_id then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.PositionalWarfare.ReEnterSceneTip)

                if not is_city then
                    self:AutoGoToBossPos(self.select_data)
                end
            else
                PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.ENTER_LAND, self.show_data.seq)
            end

            self:Close()
            PositionalWarfareWGCtrl.Instance:ClosePWView()
        else
            CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LAND_WAR, self.show_data.seq)
        end
    else
        local group_index = PositionalWarfareWGData.Instance:GetMyGroupIndex()
        if group_index <= 0 then
            local guild_id = RoleWGData.Instance:GetAttr("guild_id")

            if guild_id <= 0 then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.PositionalWarfare.EnterLandGuildErrorTip)
                return
            end
        
            local camp_list = PositionalWarfareWGData.Instance:GetCampInfoDataList()
            local can_enter = false
        
            if not IsEmptyTable(camp_list) then
                for k, v in pairs(camp_list) do
                    if guild_id == v.guild_id then
                        can_enter = true
                        break
                    end	
                end
            end
        
            if not can_enter then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.PositionalWarfare.GuildCanNotEnterErrorTip)
                return
            end
        end

        SysMsgWGCtrl.Instance:ErrorRemind(Language.PositionalWarfare.EnterLandErrorTip)
    end
end

function PositionalWarfareInformationView:AutoGoToBossPos(select_data)
    local pw_boss_cfg = PositionalWarfareWGData.Instance:GetCurMonsterCfg(select_data.land_seq, select_data.seq)
	if IsEmptyTable(pw_boss_cfg) then
		return
	end

    local role = Scene.Instance:GetMainRole()
	if role == nil then
		return
	end

    local role_x, role_y = role:GetLogicPos()
	local pos_data = Split(pw_boss_cfg.monster_pos, ",")
	local pos_x, pos_y = pos_data[1],  pos_data[2]

	if role_x == pos_x and role_y == pos_y then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		GuajiWGCtrl:StopGuaji()
		AtkCache.target_obj = nil

		local boss_info = PositionalWarfareWGData.Instance:GetCurSceneBossInfo(select_data.seq)
		local boss_active = false

		if boss_info and boss_info.is_die == 0 then
			boss_active = true
		end

		if boss_active then
			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		else
			MoveCache.SetEndType(MoveEndType.Normal)
		end

        local scene_id = Scene.Instance:GetSceneId()
		
		MoveCache.param1 = select_data.monster_id
		GuajiCache.monster_id = select_data.monster_id
        local range = BossWGData.Instance:GetMonsterRangeByid(select_data.monster_id)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, range)
	end
end

function PositionalWarfareInformationView:OnClickPersonRankBtn()
    if IsEmptyTable(self.show_data) then
        return
    end

    PositionalWarfareWGCtrl.Instance:OpenAttributiveRewardView(self.show_data)
end

function PositionalWarfareInformationView:OnClickDefeatRankBtn()
    PositionalWarfareWGCtrl.Instance:OpenDefeatRecordView(self.select_data)
end

function PositionalWarfareInformationView:OnClickZhaoJiBtn()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.LAND_CALL, self.show_data.seq)
end

-----------------------------PWCityInfoItemCellRender-----------------------------
PWCityInfoItemCellRender = PWCityInfoItemCellRender or BaseClass(BaseRender)

function PWCityInfoItemCellRender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
end

function PWCityInfoItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_city = self.data.scene_id ~= nil
    local bg_bundle, bg_asset

    if is_city then
        local city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(self.data.seq)
    
        local owner_camp = city_info and city_info.owner_camp or -1
        local tem_owner_camp = city_info and city_info.tem_owner_camp or -1  --临时占领

        local target_camp = -1
        if tem_owner_camp >= 0 then
            target_camp = tem_owner_camp
        elseif owner_camp >= 0 then
            target_camp = owner_camp
        end

        if target_camp >= 0 then
            local cur_camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(target_camp)
            self.node_list.flag_owner.image:LoadSprite(ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. cur_camp_cfg.camp_sign))
        end

        self.node_list.flag_owner:CustomSetActive(target_camp >= 0)
        self.node_list.flag_no_owner:CustomSetActive(target_camp < 0)

        bg_bundle, bg_asset = ResPath.GetRawImagesPNG("a3_zdz_gkyq_1")
        self.node_list.area_name.text.text = self.data.land_name
    else
        bg_bundle, bg_asset = ResPath.GetRawImagesPNG("a3_zdz_gkyq_" .. self.data.bg)
        local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(self.data.monster_id)
        local boss_name_str = ""
        if boss_cfg then
            boss_name_str = boss_cfg.name .. "  " .. string.format(Language.Common.Level1, boss_cfg.level)
        end

        self.node_list.boss_name.text.text = boss_name_str

        local sign_bundle, sign_asset = ResPath.GetPositionalWarfareImg("a3_zdz_bs_" .. self.data.sign)
        self.node_list.sign.image:LoadSprite(sign_bundle, sign_asset)
        self.node_list.desc_sign.text.text = Language.PositionalWarfare.InfomationBossSignName[self.data.sign]

        self:RefreshTime()
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
    
        self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshTime,self), 0.5)

        local boss_info = PositionalWarfareWGData.Instance:GetMonsterCityBossInfo(self.data.land_seq, self.data.seq)
        local camp = boss_info and boss_info.owner_camp or -1

        if camp >= 0 then
            local cur_camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(camp)
            self.node_list.flag_owner.image:LoadSprite(ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. cur_camp_cfg.camp_sign))
        end

        self.node_list.flag_owner:CustomSetActive(camp >= 0)
        self.node_list.flag_no_owner:CustomSetActive(camp < 0)
    end

    self.node_list.bg.raw_image:LoadSprite(bg_bundle, bg_asset)
    self.node_list.boss_icon:CustomSetActive(not is_city)
    self.node_list.area_name:CustomSetActive(is_city)
    self.node_list.boss_name:CustomSetActive(not is_city)
    self.node_list.time:CustomSetActive(not is_city)
    self.node_list.sign:CustomSetActive(not is_city)
end

function PWCityInfoItemCellRender:RefreshTime()
    if not self.data then
		return
	end
    
    local boss_active = PositionalWarfareWGData.Instance:GetCityBossActiveState(self.data.land_seq, self.data.seq)
    -- local boss_active = PositionalWarfareWGData.Instance:GetBossActive(self.data.land_seq, self.data.seq)

    if boss_active then
        self.node_list.time.text.text = ToColorStr(Language.PositionalWarfare.BossActiveState, COLOR3B.GREEN)

        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
    else
        local has_next_refresh_time, time = PositionalWarfareWGData.Instance:GetBossNextRefreshTime()
        
        local time_str = ""
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        if has_next_refresh_time and time > server_time then
            time_str = ToColorStr(TimeUtil.FormatSecond(time - server_time, 3), COLOR3B.RED)
        else
            time_str = ToColorStr(Language.PositionalWarfare.BossReBirthState, COLOR3B.RED)
        end

        self.node_list.time.text.text = time_str
    end
end

function PWCityInfoItemCellRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end