local TypeUnityTexture = typeof(UnityEngine.Texture)
local TypeUnitySprite = typeof(UnityEngine.Sprite)
local TypeUnityMaterial = typeof(UnityEngine.Material)
local TypeUnityPrefab = typeof(UnityEngine.GameObject)
local TypeAudioMixer = typeof(UnityEngine.Audio.AudioMixer)
local TypeActorQingGongObject = typeof(ActorQingGongObject)
local TypeQualityConfig = typeof(QualityConfig)
local TypeTextAsset = typeof(UnityEngine.TextAsset)
local TypeShaderVariant = typeof(UnityEngine.ShaderVariantCollection)
local TypeRuntimeAnimatorController = typeof(UnityEngine.RuntimeAnimatorController)
local TypeUnityAudioClip = typeof(UnityEngine.AudioClip)
local TypeOfVolumeProfile = typeof(UnityEngine.Rendering.VolumeProfile)

local POOL_TYPE_TBL = {
    [TypeUnityTexture] = ResPoolMgr.GetTexture,
    [TypeUnitySprite] = ResPoolMgr.GetSprite,
    [TypeUnityMaterial] = ResPoolMgr.GetMaterial,
    [TypeUnityPrefab] = ResPoolMgr.GetPrefab,
    [TypeAudioMixer] = ResPoolMgr.GetAudioMixer,
    [TypeActorQingGongObject] = ResPoolMgr.GetQingGongObj,
    [TypeQualityConfig] = ResPoolMgr.GetQualityConfig,
    [TypeTextAsset] = ResPoolMgr.GetTextAsset,
    [TypeShaderVariant] = ResPoolMgr.GetShaderVariant,
    [TypeRuntimeAnimatorController] = ResPoolMgr.GetAnimatorController,
    [TypeUnityAudioClip] = ResPoolMgr.GetAudioClip,
    [TypeOfVolumeProfile] = ResPoolMgr.GetResInType,
}

local AssetEnum = {
    [1] = TypeUnityTexture,
    [2] = TypeUnitySprite,
    [3] = TypeUnityMaterial,
    [4] = TypeUnityPrefab,
    [5] = TypeAudioMixer,
    [6] = TypeActorQingGongObject,
    [7] = TypeQualityConfig,
    [8] = TypeTextAsset,
    [9] = TypeShaderVariant,  
    [10] = TypeRuntimeAnimatorController,
    [11] = TypeUnityAudioClip,
    [12] = TypeOfVolumeProfile,
}

local UseToAssetEnum = {
    [TypeUnityTexture] = 1,
    [TypeUnitySprite] = 2,
    [TypeUnityMaterial] = 3,
    [TypeUnityPrefab] = 4,
    [TypeAudioMixer] = 5,
    [TypeActorQingGongObject] = 6,
    [TypeQualityConfig] = 7,
    [TypeTextAsset] = 8,
    [TypeShaderVariant] = 9,  
    [TypeRuntimeAnimatorController] = 10,
    [TypeUnityAudioClip] = 11,
    [TypeOfVolumeProfile] = 12,
}

DynamicAssetCache = DynamicAssetCache or BaseClass()

function DynamicAssetCache:__init()
    if DynamicAssetCache.Instance then
        ErrorLog("[DynamicAssetCache] Attempt to create singleton twice!")
        return
    end
    DynamicAssetCache.Instance = self

    self.cache_asset_tab = {}
end

function DynamicAssetCache:__delete()
    for k,v in pairs(self.cache_asset_tab) do
        if v ~= nil then
            for key, value in pairs(v) do
                if value ~= nil and value.obj ~= nil then
                    ResPoolMgr:Release(value.obj)
                end
            end
        end
    end

    self.cache_asset_tab = {}
    DynamicAssetCache.Instance = nil
end

function DynamicAssetCache:LoadAsset(asset_type, bundle_name, asset_name, call_back)
    local use_type = AssetEnum[asset_type]
    if use_type == nil then
        return
    end

    if self.cache_asset_tab[asset_type] == nil then
        self.cache_asset_tab[asset_type] = {}
    end

    local key = table.concat({bundle_name, "_", asset_name})
    local data = self.cache_asset_tab[asset_type]
    if data[key] ~= nil then
        return
    end

    local up_cbdata = nil
    data[key] = {asset_type = asset_type, bundle_name = bundle_name, asset_name = asset_name, key = key, call_back = call_back, obj = nil}
    local cbdata = DynamicAssetCache.GetCBData()
    cbdata[1] = self
    cbdata[2] = bundle_name
    cbdata[3] = asset_name
    cbdata[4] = use_type
    cbdata[5] = call_back
    cbdata[6] = up_cbdata

    if use_type == TypeOfVolumeProfile then
        ResPoolMgr.GetResInType(
                ResPoolMgr,
                bundle_name,
                asset_name,
                DynamicAssetCache.OnLoadComplete,
                true,
                use_type,
                cbdata)
    else
        POOL_TYPE_TBL[use_type](
                ResPoolMgr,
                bundle_name,
                asset_name,
                DynamicAssetCache.OnLoadComplete,
                true,
                cbdata,
                ResLoadPriority.low)
    end    
end

function DynamicAssetCache:UnLoadAsset(asset_type, bundle_name, asset_name, call_back)
    if self.cache_asset_tab[asset_type] == nil then
        return
    end

    local data = self.cache_asset_tab[asset_type]
    local key = table.concat({bundle_name, "_", asset_name})
    local info = data[key]
    data[key] = nil
    if info ~= nil and info.obj ~= nil then
        ResPoolMgr:Release(info.obj)
    end
end

function DynamicAssetCache.OnLoadComplete(res, cbdata)
    local self = cbdata[1]
    local bundle_name = cbdata[2]
    local asset_name = cbdata[3]
    local asset_type = cbdata[4]
    local load_callback = cbdata[5]
    local up_cbdata = cbdata[6]
    DynamicAssetCache.ReleaseCBData(cbdata)

    local key_type = UseToAssetEnum[asset_type]
    if key_type == nil then
        ResPoolMgr:Release(res)
        return
    end

    if self.cache_asset_tab[key_type] ~= nil then
        local key = table.concat({bundle_name, "_", asset_name})
        local data = self.cache_asset_tab[key_type]
        if data ~= nil and data[key] ~= nil then
            data[key].obj = res
        else
            ResPoolMgr:Release(res)
            return           
        end
    else
        ResPoolMgr:Release(res)
        return
    end

    if load_callback ~= nil then
        load_callback()
    end
end

DynamicAssetCache.cbdata_list = {}
function DynamicAssetCache.GetCBData()
    local cbdata = table.remove(DynamicAssetCache.cbdata_list)
    if nil == cbdata then
        cbdata = {true, true, true, true, true, true}
    end

    return cbdata
end

function DynamicAssetCache.ReleaseCBData(cbdata)
    cbdata[1] = true
    cbdata[5] = nil
    cbdata[6] = nil
    table.insert(DynamicAssetCache.cbdata_list, cbdata)
end