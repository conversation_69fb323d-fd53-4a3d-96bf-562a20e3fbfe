UltimateBattlefieldRewardView = UltimateBattlefieldRewardView or BaseClass(SafeBaseView)
function UltimateBattlefieldRewardView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg()
    self.default_index = TabIndex.ultimate_battlefield_camp
    self.is_safe_area_adapter = true
    local bundle_name = "uis/view/country_map_ui/ultimate_battlefield_prefab"

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_panel")
    self:AddViewResource(TabIndex.ultimate_battlefield_camp, bundle_name, "layout_ultimate_reward_camp")
    self:AddViewResource(TabIndex.ultimate_battlefield_score, bundle_name, "layout_ultimate_reward_score")
    self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function UltimateBattlefieldRewardView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self:ReleaseScoreReward()
    self:ReleaseCampReward()
end

function UltimateBattlefieldRewardView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.UltimateBattlefield.TitleName
    
    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list,"VerticalTabbarUltimateReward")
		self.tabbar:Init(Language.UltimateBattlefield.TabGropReward, nil, nil, nil, nil)
        -- 标签选择回调绑定
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
	end
end

function UltimateBattlefieldRewardView:LoadIndexCallBack(index)
	if index == TabIndex.ultimate_battlefield_score then
		self:LoadIndexCallBackScoreReward()
	elseif index == TabIndex.ultimate_battlefield_camp then
        self:LoadIndexCallBackCamp()
	end
end

function UltimateBattlefieldRewardView:ShowIndexCallBack(index)
    if index == TabIndex.ultimate_battlefield_score then
		self:ShowIndexCallBackScoreReward()
	elseif index == TabIndex.ultimate_battlefield_camp then
        self:ShowIndexCallBackCamp()
    end
end

function UltimateBattlefieldRewardView:OnFlush(param_t, index)
    if index == TabIndex.ultimate_battlefield_score then
		self:OnFlushScoreReward()
	elseif index == TabIndex.ultimate_battlefield_camp then
        self:OnFlushCamp()
    end
end
