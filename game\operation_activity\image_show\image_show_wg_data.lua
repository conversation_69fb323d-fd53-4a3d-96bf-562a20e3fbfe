ImageShowWGData = ImageShowWGData or BaseClass()
ImageShowWGData.ConfigPath = "config/auto_new/operation_activity_show_auto"

function ImageShowWGData:__init()
	if ImageShowWGData.Instance then
		ErrorLog("[ImageShowWGData] Attemp to create a singleton twice !")
	end

	ImageShowWGData.Instance = self
	self.image_show_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_show_auto")
	self.model_show_cfg = ListToMap(self.image_show_cfg.model_show,"grade","sort_index")
	self:FlushXianShiMiaoShaCfg()
	self.item_max_cap_table = {}
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_IMAGE_SHOW, {[1] = OPERATION_EVENT_TYPE.LEVEL},
	BindTool.Bind(self.GetAvtivityIsOpen, self))

	-- RemindManager.Instance:Register(RemindName.OperationXianshiMiaosha, BindTool.Bind(self.ShowXianshiMiaoshaRemind, self))
end

function ImageShowWGData:__delete()
	ImageShowWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.OperationXianshiMiaosha)
end

function ImageShowWGData:GetAvtivityIsOpen()
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_IMAGE_SHOW)
end

function ImageShowWGData:FlushXianShiMiaoShaCfg()
	self.image_show_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_show_auto")
end

function ImageShowWGData:GetImageShowCurCfg()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_cfg = self.image_show_cfg.activity_param
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_IMAGE_SHOW)

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local pass_time = server_time - activity_status.start_time
	local one_day_time = 24 * 60 *60
	local pass_day = math.floor(pass_time / one_day_time)
	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_open_day = cur_open_day - pass_day
	local week_day = TimeUtil.FormatSecond3MYHM1(activity_status.start_time)

	for k,v in pairs(activity_cfg) do
		if v.start_server_day <= activity_open_day and v.end_server_day > activity_open_day and v.week_id == week_day then
			return v
		end
	end
	return {}
end

function ImageShowWGData:GetImageShowFaceCfg()
	local cur_day_cfg = self:GetImageShowCurCfg()
	if cur_day_cfg and not IsEmptyTable(cur_day_cfg) and self.image_show_cfg.interface then
		local cur_interface = cur_day_cfg.interface
		for k,v in pairs(self.image_show_cfg.interface) do
			if v.interface == cur_interface then
				self.image_show_interface_cfg = v
				return v
			end
		end
	end
	return {}
end

function ImageShowWGData:GetImageShowRewardData(grade)
	if self.model_show_cfg and self.model_show_cfg[grade] then
		self:CacularItemMaxCap(grade)
		return self.model_show_cfg[grade]
	end
	return {}
end

function ImageShowWGData:CacularItemMaxCap(grade)
	if self.model_show_cfg and self.model_show_cfg[grade] then
		for k,v in pairs(self.model_show_cfg[grade]) do
			if not self.item_max_cap_table[v.item_id] then
				self.item_max_cap_table[v.item_id] = ItemShowWGData.CalculateCapability(v.item_id, true)
			end
		end
	end
end

function ImageShowWGData:GetItemMaxCapById(item_id)
	return self.item_max_cap_table[item_id] or 0
end

function ImageShowWGData:GetCellImageShowFaceCfg()
	return self.image_show_interface_cfg or {}
end

function ImageShowWGData:GetCurShowItemID()
	return self.cur_is_show_item_id or 0
end

function ImageShowWGData:SetCurShowItemID(item_id)
	self.cur_is_show_item_id = item_id
end