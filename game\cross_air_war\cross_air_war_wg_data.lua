
CrossAirWarWGData = CrossAirWarWGData or BaseClass()

-- 跨服空战拍卖状态
CROSS_AIR_WAR_AUCTION_STATUS =
{
	STATUS_END 					= 0,	-- 结束
	STATUS_WAIT_START			= 1,	-- 等待开始
	STATUS_WAIT_MONSTER			= 2,	-- 等待boss
	STATUS_MONSTER				= 3,	-- boss中
	STATUS_MONSTER_END			= 4,	-- boss结束
	STATUS_GATHER				= 5,	-- 采集阶段
	STATUS_AUCTION				= 6,	-- 拍卖阶段
}

-- 跨服空战引导类型
CROSS_AIR_WAR_GUIDE_TYPE =
{
	GUIDE_TYPE_CHANGE_STAGE			= 1,	-- 切换阶段
	GUIDE_TYPE_BOSS_ENTRANCE		= 2,	-- boss出场
	GUIDE_TYPE_BOSS_SKILL			= 3,	-- boss技能
}

function CrossAirWarWGData:__init()
	if CrossAirWarWGData.Instance ~= nil then
		ErrorLog("[CrossAirWarWGData] attempt to create singleton twice!")
		return
	end
	CrossAirWarWGData.Instance = self

	local all_air_war_cfg = ConfigManager.Instance:GetAutoConfig("cross_aid_war_auto")
	if all_air_war_cfg then
		self.other_cfg = all_air_war_cfg.other[1]
		self.monster_cfg = all_air_war_cfg.monster
		self.hurt_cap_cfg = ListToMap(all_air_war_cfg.hurt_cap, "monster_seq", "index")
		self.rank_reward_cfg = ListToMapList(all_air_war_cfg.rank_reward, "monster_seq") 
		self.auction_cfg = ListToMap(all_air_war_cfg.auction, "grade", "round", "seq")
		self.auction_item_cfg = all_air_war_cfg.auction_item
		self.falling_cfg = all_air_war_cfg.falling
		self.gather_cfg = all_air_war_cfg.gather
		self.score_reward_cfg = all_air_war_cfg.score_reward
		self.scoring_cfg = ListToMapList(all_air_war_cfg.scoring, "monster_seq")
		self.scoring_list_cfg = all_air_war_cfg.scoring
		self.guide_cfg = ListToMapList(all_air_war_cfg.guide, "id")
		self.guide_find_cfg = ListToMap(all_air_war_cfg.guide, "trigger_type", "trigger_param_1")
		self.monster_cg_cfg = all_air_war_cfg.monster_cg
		self.boss_drop_show_cfg = all_air_war_cfg.boss_drop_show
		self.air_wall_cfg = ListToMap(all_air_war_cfg.stage, "stage")
	end

	self:InitDataCache()
end

function CrossAirWarWGData:__delete()
	CrossAirWarWGData.Instance = nil
end

-- 初始化一部分缓存
function CrossAirWarWGData:InitDataCache()
	-- 缓存怪物的出生点
	self.moster_build_pos = {}
	self.boss_moster_list = {}
	self.stage_tips_list = {}

	if self.monster_cfg then
		for _, cfg in pairs(self.monster_cfg) do
			if cfg and cfg.seq then
				self.moster_build_pos[cfg.seq] = {}
				local pos_str_list = Split(cfg.monster_pos, "|")

				for i, pos_str in ipairs(pos_str_list) do
					local pos_list = Split(pos_str, ",")
					local pos = {}
					pos.x = tonumber(pos_list[1]) or 0
					pos.y = tonumber(pos_list[2]) or 0
					table.insert(self.moster_build_pos[cfg.seq], pos)
				end

				if cfg.is_boss == 1 then
					table.insert(self.boss_moster_list, cfg)
				end


				self.stage_tips_list[cfg.seq] = {}
				local status_list = Split(cfg.stage_status, "|")
				local desc_list = Split(cfg.stage_dec, "|")
	
				for index, status_str in ipairs(status_list) do
					local status_index = tonumber(status_str) or 1
					self.stage_tips_list[cfg.seq][status_index]= desc_list[index]
				end
			end
		end
	end

	if self.other_cfg and self.other_cfg.box_pos then
		local pos_list = Split(self.other_cfg.box_pos, ",")
		local pos = {}
		pos.x = tonumber(pos_list[1]) or 0
		pos.y = tonumber(pos_list[2]) or 0
		self.find_box_pos = pos
	end

	if self.other_cfg and self.other_cfg.wait_air_wall then
		local air_wall_list = Split(self.other_cfg.wait_air_wall, "##")
		local data = {}
		data.x = tonumber(air_wall_list[1]) or 0
		data.y = tonumber(air_wall_list[2]) or 0
		data.w = tonumber(air_wall_list[3]) or 0
		data.h = tonumber(air_wall_list[4]) or 0
		self.wait_air_wall = data
	end

	table.sort(self.boss_moster_list, SortTools.KeyLowerSorter("seq"))
end

-------------------------------------------------------------------------------
-- 获取基础表
function CrossAirWarWGData:GetBaseCfg()
	return self.other_cfg
end

-- 获取天降宝箱寻路坐标
function CrossAirWarWGData:GetFindBoxPos()
	return self.find_box_pos
end

-- 获取等待开启的空气墙
function CrossAirWarWGData:GetWaitAirWallPos()
	return 10000, self.wait_air_wall
end

-- 获取当前怪物配置
function CrossAirWarWGData:GetMonsterCfgBySeq(monster_seq)
	local empty = {}
	return (self.monster_cfg or empty)[monster_seq]
end

-- 获取当前怪物的位置列表
function CrossAirWarWGData:GetMonsterPosBySeq(monster_seq)
	local empty = {}
	return (self.moster_build_pos or empty)[monster_seq]
end

-- 获取当前Boss怪物的列表
function CrossAirWarWGData:GetBossMonsterList()
	return self.boss_moster_list
end

-- 获取伤害战力
function CrossAirWarWGData:GetHurtCapCfgBySeqIndex(monster_seq, index)
	local empty = {}
	return ((self.hurt_cap_cfg or empty)[monster_seq] or empty)[index]
end

-- 获取当前怪物的排行奖励列表
function CrossAirWarWGData:GetRankRewardCfgBySeq(monster_seq)
	local empty = {}
	return (self.rank_reward_cfg or empty)[monster_seq]
end

-- 获取当前拍卖配置(轮次列表)
function CrossAirWarWGData:GetAuctionListByGrade(grade)
	local empty = {}
	return (self.auction_cfg or empty)[grade]
end

-- 获取当前拍卖配置（奖励列表）
function CrossAirWarWGData:GetAuctionCfgByGradeRound(grade, round)
	local empty = {}
	return ((self.auction_cfg or empty)[grade] or empty)[round]
end

-- 获取当前拍卖配置（奖励）
function CrossAirWarWGData:GetAuctionCfgByGradeRoundSeq(grade, round, seq)
	local empty = {}
	return (((self.auction_cfg or empty)[grade] or empty)[round] or empty)[seq]
end

-- 获取掉落物信息
function CrossAirWarWGData:GetFallingCfgBySeq(seq)
	local empty = {}
	return (self.falling_cfg or empty)[seq]
end

-- 获取掉落物信息
function CrossAirWarWGData:GetBoxGatherCfgBySeq(seq)
	local empty = {}
	return (self.gather_cfg or empty)[seq]
end

-- 获取积分奖励信息
function CrossAirWarWGData:GetScoreRewardCfgBySeq(seq)
	local empty = {}
	return (self.score_reward_cfg or empty)[seq]
end

-- 获取积分奖励列表
function CrossAirWarWGData:GetScoreRewardList()
	return self.score_reward_cfg
end

function CrossAirWarWGData:GetEnteranceTitle()
	return self.other_cfg.enterance_title or ''
end

function CrossAirWarWGData:GetOpenTimeDesc()
	return self.other_cfg.enter_time_desc or ''
end

function CrossAirWarWGData:GetGameplayDesc()
	return self.other_cfg.gameplay_desc or ''
end

function CrossAirWarWGData:GetShowFieldRewardData()
	return self.other_cfg.field_reward
end

function CrossAirWarWGData:GetScoreRewardCfg()
	return self.score_reward_cfg or {}
end

function CrossAirWarWGData:GetScoringListRewardCfg()
	return self.scoring_list_cfg or {}
end

function CrossAirWarWGData:GetScoringCfgBySeq(moster_seq)
	local empty = {}
	return (self.scoring_cfg or empty)[moster_seq]
end

-- 获取一个引导列表根据id
function CrossAirWarWGData:GetGuideCfgById(guide_id)
	local empty = {}
	return (self.guide_cfg or empty)[guide_id]
end

-- 获取一份配置根据触发类型和触发参数
function CrossAirWarWGData:GuideCfgByTriggerParam(trigger_type, trigger_param)
	local empty = {}
	return ((self.guide_find_cfg or empty)[trigger_type] or empty)[trigger_param]
end

-- 获取一个boss出场
function CrossAirWarWGData:GetMosterCgCfgById(boss_id)
	local empty = {}
	return (self.monster_cg_cfg or empty)[boss_id]
end

-- 获取所有的阶段的掉落奖励
function CrossAirWarWGData:GetBossDropShowCfg()
	local empty = {}
	return self.boss_drop_show_cfg
end

-- 获取空气墙信息根据阶段
function CrossAirWarWGData:GetAirWallCfgById(stage)
	local empty = {}
	return (self.air_wall_cfg or empty)[stage]
end

-- 获取空气墙信息根据阶段
function CrossAirWarWGData:GetAirWallCfg()
	return self.air_wall_cfg
end

function CrossAirWarWGData:GetAuctionItemCfgBySeq(seq)
	local empty = {}
	return (self.auction_item_cfg or empty)[seq]
end

-- 获取当前的提示
function CrossAirWarWGData:GetStageTipsBySeqStatus(seq, status)
	local empty = {}
	return ((self.stage_tips_list or empty)[seq] or empty)[status] or ""
end
------------------------- protocol start -------------------------
-- 空战基础信息
function CrossAirWarWGData:SetAirWarBaseInfo(protocol)
	self.air_war_info = protocol
end

-- 获取空战基础信息
function CrossAirWarWGData:GetAirWarBaseInfo()
	return self.air_war_info
end

-- 获取当前的怪物Seq
function CrossAirWarWGData:GetWarCurMonsterSeq(is_not_check)
	local empty = {}

	local status = self:GetAirWarSceneStatus()
	local curr_seq = (self.air_war_info or empty).monster_seq or 0
	local is_wait = false

	if is_not_check then
		return curr_seq, is_wait
	end

	if status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_MONSTER then		-- 等待boss要切换到下一个状态
		is_wait = true
	end

	return curr_seq, is_wait
end

-- 获取当前的怪物个数
function CrossAirWarWGData:GetWarCurStageMosterNum()
	local empty = {}
	return (self.air_war_info or empty).monster_num or 0
end

-- 获取当前seq开始的事件戳
function CrossAirWarWGData:GetWarCurMosterStartTime()
	local empty = {}
	return (self.air_war_info or empty).seq_start_time or 0
end

-- 空战个人信息
function CrossAirWarWGData:SetAirWarRoleBaseInfo(protocol)
	self.score 					= protocol.score
	self.score_reward_flag_list = protocol.score_reward_flag_list
	self.gather_flag_list 		= protocol.gather_flag_list
	self.auction_reward_flag 	= protocol.auction_reward_flag
	self.can_auction_reward_flag = protocol.can_auction_reward_flag
end

function CrossAirWarWGData:GetPlayerWarScore()
	return self.score or 0
end

function CrossAirWarWGData:GetPlayerScoreRewardFlagList()
	return self.score_reward_flag_list
end

function CrossAirWarWGData:GetGatherFlagList()
	return self.gather_flag_list
end

function CrossAirWarWGData:GetPlayerAuctionRewardFlag()
	return self.auction_reward_flag
end

function CrossAirWarWGData:GetPlayerCanAuctionRewardFlag()
	return self.can_auction_reward_flag
end

-- 设置状态信息
function CrossAirWarWGData:SetAirWarSceneInfo(protocol)
	self.air_war_scene_info = protocol
end
 
-- 获取当前活动的状态
function CrossAirWarWGData:GetAirWarSceneStatus()
	local empty = {}
	return (self.air_war_scene_info or empty).m_status or 0
end

-- 获取当前活动的阶段
function CrossAirWarWGData:GetAirWarSceneStage()
	local empty = {}
	return (self.air_war_scene_info or empty).stage or 0
end

-- 获取下一个阶段时间
function CrossAirWarWGData:GetAirWarSceneNextStatusTime()
	local empty = {}
	return (self.air_war_scene_info or empty).m_next_status_time or 0
end

-- 状态当前时间
function CrossAirWarWGData:GetAirWarScenePerStatusTime()
	local empty = {}
	return (self.air_war_scene_info or empty).pre_status_time or 0
end

-- 空战状态（外部获取）
function CrossAirWarWGData:SetAirWarActivityStatus(protocol)
	self.activity_stuats = protocol.m_status
end

-- 获取空战状态（外部获取）
function CrossAirWarWGData:GetAirWarActivityStatus()
	return self.activity_stuats or 0
end

-- 空战拍卖信息
function CrossAirWarWGData:SetAirWarAuctionInfo(protocol)
	self.auction_info = protocol
end

-- 获取拍卖信息(轮次)
function CrossAirWarWGData:GetMaxAirWarAuctionInfoRound()
	local empty = {}
	return #self.all_auction_info_list + 1
end

-- 获取拍卖信息(档次)
function CrossAirWarWGData:GetAirWarAuctionInfoGrade()
	local empty = {}
	return (self.auction_info or empty).auction_grade or 0
end

-- 获取拍卖信息(档次)
function CrossAirWarWGData:GetCurAirWarAuctionInfoRound()
	local empty = {}
	return (self.auction_info or empty).auction_round or 0
end

--设置当前的拍卖物信息
function CrossAirWarWGData:SetAuctionItemInfo(protocol)
	if protocol.auction_info_list then
		for i, v in ipairs(protocol.auction_info_list) do
			self:SetSingleAuctionItemInfo(i, v)
		end
	end
end

-- 设置单个拍卖物
function CrossAirWarWGData:SetSingleAuctionItemInfo(index, auction_item)
	if not self.all_auction_info_list then
		self.all_auction_info_list = {}
	end

	if not self.all_auction_info_list[auction_item.round] then
		self.all_auction_info_list[auction_item.round] = {}
	end

	local data = {}
	data.index = index
	data.auction_data = auction_item
	self.all_auction_info_list[auction_item.round][auction_item.seq] = data
end

-- 获取某个拍卖物
function CrossAirWarWGData:GetSingleAuctionItemInfo(round, seq)
	local empty = {}
	return ((self.all_auction_info_list or empty)[round] or empty)[seq]
end

------------------------- protocol end -------------------------
-- 获取拍卖物列表
function CrossAirWarWGData:GetAuctionListByRound(round)
	local list = (self.all_auction_info_list or {})[round] or {}
	local sort_list = {}

	for k, v in pairs(list) do
		if v.auction_data ~= nil then
			table.insert(sort_list, v)
		end
	end

	return sort_list
end

-- 获取数据列表
function CrossAirWarWGData:GetAllRankRewardList(type)
	if not self.rank_reward_cfg then
		return {}
	end

	local list = {}
	for _, rank_list in pairs(self.rank_reward_cfg) do
		for _, rank_data in ipairs(rank_list) do
			local data = {}
			data.cfg = rank_data
			data.show_type = type

			table.insert(list, data)
		end
	end

	return list
end

-- 根据当前的档次获取所有轮次
function CrossAirWarWGData:GetCurrAuctionMessage()
	local auction_grade = self:GetAirWarAuctionInfoGrade()
	local list = self:GetAuctionListByGrade(auction_grade)

	return auction_grade, #list + 1
end

function CrossAirWarWGData:GetFuncOpenStatus()
    local is_open = FunOpen.Instance:GetFunIsOpened(FunName.CrossAirWarView)
	return is_open
end


-- 获取一份配置根据触发类型和触发参数获取引导列表
function CrossAirWarWGData:GetGuideListCfgByTriggerParam(trigger_type, trigger_param)
	local trigger_cfg = self:GuideCfgByTriggerParam(trigger_type, trigger_param)
	if trigger_cfg then
		return self:GetGuideCfgById(trigger_cfg.id)
	end

	return nil
end

-- 获取当前拍卖配置(轮次列表)
function CrossAirWarWGData:GetAuctionBagListForGrade(is_select_cur_stage)
	local auction_grade = self:GetAirWarAuctionInfoGrade()
	local stage = self:GetAirWarSceneStage()
	local bag_list = {}

	if (not auction_grade) or (not self.all_auction_info_list) then
		return bag_list
	end

	for round, seq_list in pairs(self.all_auction_info_list) do
		for seq, data in pairs(seq_list) do
			if data.index and data.auction_data then
				local aim_data = data.auction_data
				local cfg = self:GetAuctionCfgByGradeRoundSeq(auction_grade, round, seq)

				if is_select_cur_stage then
					if cfg and cfg.show_stage == stage then
						local auction_item_cfg = self:GetAuctionItemCfgBySeq(aim_data.auction_seq)
						table.insert(bag_list, auction_item_cfg.auction_item)
					end
				else
					if cfg and cfg.show_stage < stage then
						local auction_item_cfg = self:GetAuctionItemCfgBySeq(aim_data.auction_seq)
						table.insert(bag_list, auction_item_cfg.auction_item)
					end
				end
			end
		end
	end

	return bag_list
end

-- 获取当前的额奖励列表
function CrossAirWarWGData:GetFinalBossReawardList()
	local stage = self:GetAirWarSceneStage()
	local auction_list = self:GetAuctionBagListForGrade(true)
	-- 获取掉落物展示
	local boss_drop_cfg = self:GetBossDropShowCfg()
	local boss_drop_list = {}
	for i, v in ipairs(boss_drop_cfg) do
		if v.show_stage == stage then
			boss_drop_list = v.reward_item
		end
	end

	-- 获取评分展示
	local score_reward_list = nil
	local monster_seq = self:GetWarCurMonsterSeq()
	local scoring_list = self:GetScoringCfgBySeq(monster_seq)
	local monster_cfg = self:GetMonsterCfgBySeq(monster_seq)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local seq_start_time = CrossAirWarWGData.Instance:GetWarCurMosterStartTime()
	local score_time = server_time - seq_start_time
	local scoring_reward_list = {}
	local scoring_level = -1
	local monster_num = monster_cfg and monster_cfg.monster_num or 1

	if scoring_list then
		for i, v in ipairs(scoring_list) do
			if score_time < v.time then
				scoring_reward_list = v.reward_item
				scoring_level = v.scoring_index
				break
			end
		end
	end

	local person_reward_list = {}
	for k, v in pairs(boss_drop_list) do
		if v.item_id then
			local data = {}
			data.item_id = v.item_id
			data.num = v.num * monster_num
			data.is_bind = v.is_bind
			table.insert(person_reward_list, v)
		end
	end

	for k, v in pairs(scoring_reward_list) do
		if v.item_id then
			table.insert(person_reward_list, v)
		end
	end

	return auction_list, person_reward_list, scoring_level
end

-- 获取当前状态的提示
function CrossAirWarWGData:GetNowStageTips() 
	local seq = self:GetWarCurMonsterSeq()
	local status = self:GetAirWarSceneStatus() 
	return self:GetStageTipsBySeqStatus(seq, status)
end