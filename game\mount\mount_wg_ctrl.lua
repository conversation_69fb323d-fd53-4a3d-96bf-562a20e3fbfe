MountWGCtrl = MountWGCtrl or BaseClass(BaseWGCtrl)
function MountWGCtrl:__init()
	if MountWGCtrl.Instance then
		ErrorLog("[MountWGCtrl]:Attempt to create singleton twice!")
	end

	MountWGCtrl.Instance = self
	self:RegisterAllEvents()
	self:RegisterAllProtocols()
end

function MountWGCtrl:__delete()
	MountWGCtrl.Instance = nil
end

-- 事件注册
function MountWGCtrl:RegisterAllEvents()
end

-- 协议注册
function MountWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSGoonMount)
	self:RegisterProtocol(CSMountUplevel)
	self:RegisterProtocol(CSUpgradeMount)
	self:RegisterProtocol(CSUseMountImage)
	self:RegisterProtocol(CSMountClearUplevelCD)
	self:RegisterProtocol(CSMountFlyUp)
	self:RegisterProtocol(CSMountUseFlyUpImage)
	self:RegisterProtocol(CSSetMoveMode)
	self:RegisterProtocol(CSMountUpgradeQibing)

	self:RegisterProtocol(CSMountUplevelEquip)
	self:RegisterProtocol(CSJumpProcessChange)
end

-- 所有下坐骑(普通和战斗坐骑)
function MountWGCtrl:AllDownMount()
	self:SendMountGoonReq(0)
end

-- 坐骑骑乘请求 0：下坐骑   1：上坐骑
function MountWGCtrl:SendMountGoonReq(mount_flag, force)
	local req_up = mount_flag == 1
	local main_role = Scene.Instance:GetMainRole()
	local is_riding = main_role:IsRiding()

	if req_up and is_riding and (not force) then
		return
	elseif not req_up and not is_riding and (not force) then
		return
	end

    if req_up then
		--特殊场景不能上坐骑
        local info = FuBenWGData.GetFbSceneConfig(Scene.Instance:GetSceneType())
        if info and info.pb_mount == 1 then
        	return
        end

		if main_role:IsJump()
		or main_role:IsQingGong()
		or main_role:IsInTianShenState()
		or main_role:IsMitsurugi()
		or main_role:IsXiuWeiBianShen()
		or main_role:IsXiuWeiBianShenSeq()
		or main_role:IsGundam() then
			return
		end
    end

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGoonMount)
	send_protocol.mount_flag = mount_flag
	send_protocol:EncodeAndSend()
end

function MountWGCtrl:MountAppeChange(protocol)
	local role = Scene.Instance:GetObj(protocol.obj_id)
	if nil == role then
		return
	end

	if role:IsMainRole() then
		self:CheckMountUpOrDownInCg()
	end
end

function MountWGCtrl:CheckMountUpOrDownInCg()
	local main_role = Scene.Instance:GetMainRole()
	local player_mount_flag	= main_role:GetCurRidingResId() > 0 and 1 or 0
	if CgManager.Instance:IsCgIng() then
		if 1 == player_mount_flag then
			self:SendMountGoonReq(0)
			self.mount_downed_in_cg = true
		end
	else
		if self.mount_downed_in_cg then
			self.mount_downed_in_cg = false
			self:SendMountGoonReq(1)
		end
	end
end