OpRechargeRankWGData = OpRechargeRankWGData or BaseClass()
function OpRechargeRankWGData:__init()
	if OpRechargeRankWGData.Instance then 
		ErrorLog("[OpRechargeRankWGData] Attemp to create a singleton twice !")
	end

	OpRechargeRankWGData.Instance = self
    OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_RANK_2, {[1] = OPERATION_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self))

    self:LoadConfig()
end


function OpRechargeRankWGData:__delete()
	OpRechargeRankWGData.Instance = nil
end

-- function OpRechargeRankWGData:GetRankCfg()
--     return self.recharge_rank_cfg
-- end

function OpRechargeRankWGData:LoadConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")
    self.recharge_rank_cfg = cfg.chongzhi_rank_2
end

function OpRechargeRankWGData:SetRechargeInfo(protocol)
    self.chongzhi_num = protocol.chongzhi_num
end

function OpRechargeRankWGData:GetRechargeInfo()
    return self.chongzhi_num or 0
end

function OpRechargeRankWGData:GetActCanOpen()
	--根据开启天数拿配置   拿不到配置标签隐藏
	local param_cfg = self:GetRankCfg()
	if IsEmptyTable(param_cfg) then
		return false
	end
	--活动状态是false 也隐藏
    return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_RANK_2)
end

--根据开服天数获取配置
function OpRechargeRankWGData:GetRankCfg()
	local return_cfg = {}
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_RANK_2)
	for i, v in ipairs(self.recharge_rank_cfg) do
		if v.start_day <= open_day and open_day <= v.end_day then 
			table.insert(return_cfg, v)
		end
	end

	return return_cfg
end

--没有上榜
function OpRechargeRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, rank_value = 0}
end

function OpRechargeRankWGData:ExpandRankData(rank_list, nNextIndex, nCurRank, index)
	local next_data = rank_list[nNextIndex]
	if next_data then
		local nNextRank = next_data.rank_index
		if nCurRank + 1 ~= nNextRank then
			for nSectionIndex = nCurRank + 1, nNextRank - 1 do
				local data = {}
				data.no_true_rank = true
				data.index = index
				data.rank_data = self:CreatNoRankItemData(nSectionIndex)
				table.insert(self.opReRankdata.rank_list, data)
				index = index + 1
			end
		end
	end

	return index
end

function OpRechargeRankWGData:SetOpReRankSort(protocol)
	local cfg = self:GetRankCfg()
	local rank_cfg = cfg[#cfg]
	local rank_list = {}
	local qujian_list = {}
	local is_rank = {}
	self.pro_data_list = {}
	local max_rank = rank_cfg.max_rank  --最多名次
	if #protocol.rank_list > max_rank then
		for i = 1, max_rank do
			rank_list[i] = protocol.rank_list[i]
		end
	else
		rank_list = protocol.rank_list
	end

	for i = 1, #cfg do
		qujian_list[i] = {}
	end

	for i = 1, max_rank do
		is_rank[i] = false
	end

	for k, v in pairs(rank_list) do
		local data_qujian = self:GetRankItemData(v.rank_value)
		if data_qujian ~= nil then
			local data = {
				rank_data = v,
				rank = data_qujian,
				rank_index = 0,
				sort = v.rank_value,
			}
			table.insert(qujian_list[data_qujian], data)
		end
	end

	local index = 0
	for i = 1, #cfg do
		if qujian_list[i] ~= nil then
			SortTools.SortDesc(qujian_list[i], "sort")
			for k, v in pairs(qujian_list[i]) do
				local _, rank_data = self:GetRankItemData(v.rank_data.rank_value)
				if rank_data and v.rank_index < rank_data.max_rank then
					v.rank_index = index + rank_data.min_rank
					index = index + 1
					if not is_rank[v.rank_index] then
						is_rank[v.rank_index] = true
					end
				end
				table.insert(self.pro_data_list, v)
			end
			index = 0
		end
	end

	for k, v in pairs(self.pro_data_list) do
		if is_rank[v.rank_index] and k ~= v.rank_index and v.rank_index < k then
			v.rank_index = k
		end
	end

	--print_error(self.pro_data_list)

	self.opReRankdata = {}
	self.opReRankdata.rank_list = {}
	local index2 = 1
    --local MaxValue = rank_cfg.limit_chongzhi  --最低上榜条件
	for i = 1, #self.pro_data_list do
		if i == 1 then
			if self.pro_data_list[i].rank_index ~= 1 then -- 没有第一名
				local item = {}
				item.rank_data = self:CreatNoRankItemData(1)
				item.index = index2
				item.no_true_rank = true
				index2 = index2 + 1
				table.insert(self.opReRankdata.rank_list, item)
				index2 = self:ExpandRankData(self.pro_data_list, 1, 1, index2)
			end
		end

		local item = {}
		item.index = index2
		item.rank_data = self.pro_data_list[i].rank_data
		item.rank_index = self.pro_data_list[i].rank_index
        local nCurRank = item.rank_index
		table.insert(self.opReRankdata.rank_list, item)
		index2 = index2 + 1

		index2 = self:ExpandRankData(self.pro_data_list, i + 1, nCurRank, index2)
	end

	local nMaxRank = rank_cfg.max_rank
	if index2 - 1 < nMaxRank then
		for nSectionIndex = index2, nMaxRank do
			local data = {}
			data.no_true_rank = true
			data.index = nSectionIndex
			data.rank_data = self:CreatNoRankItemData(nSectionIndex)
			table.insert(self.opReRankdata.rank_list, data)
		end
	end
end

--组合完成的排行数据
function OpRechargeRankWGData:GetRankInfo()
	if self.opReRankdata ~= nil and self.opReRankdata.rank_list ~= nil then
		return self.opReRankdata.rank_list	
	end
	return nil
end

--获取配置表排行区间数据
function OpRechargeRankWGData:GetRankItemData(rank_chongzhi)
	local index, data
	local rank_cfg = self:GetRankCfg()
	if rank_cfg ~= nil then
		for k, v in ipairs(rank_cfg) do
			if rank_chongzhi >= v.limit_chongzhi then
				return k, v
			end
		end
	end

	return nil
end

function OpRechargeRankWGData:GetMaxRankNum()
    local cfg = self:GetRankCfg()
    local rank_cfg = cfg[#cfg]
	return rank_cfg.max_rank or 0
end

--获取自己上榜信息
function OpRechargeRankWGData:GetMyRankInfo()
	local rank_list = self:GetRankInfo()
	if rank_list == nil then
		return nil
	end
    
	local my_uid = RoleWGData.Instance:GetRoleVo().origin_uid
	for k, v in pairs(rank_list) do
		if v.rank_data and v.rank_data.user_id then
			if v.rank_data.user_id == my_uid then
				return v
			end
		end
	end

	return nil
end
