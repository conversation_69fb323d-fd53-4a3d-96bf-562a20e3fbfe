MingWenWGData = MingWenWGData or BaseClass()

function MingWenWGData:__init()
	if MingWenWGData.Instance ~= nil then
		<PERSON>rrorLog("[MingWenWGData] attempt to create singleton twice!")
		return
	end
	MingWenWGData.Instance = self
	RemindManager.Instance:Register(RemindName.MingWen_HeCheng, BindTool.Bind(self.IsShowMainViewHeChengRedPoint, self))
	RemindManager.Instance:Register(RemindName.MingWen_HeCheng_In_View,BindTool.Bind(self.IsShowMingWenHeChengRedPoint, self))
	RemindManager.Instance:Register(RemindName.MingWen_XiangQian, BindTool.Bind(self.IsShowMingWenXiangQianRedPoint, self))
	RemindManager.Instance:Register(RemindName.MingWen_FenJie, BindTool.Bind(self.IsShowMingWenFenJieRedPoint, self))
	RemindManager.Instance:Register(RemindName.MingWen_DuiHuan, BindTool.Bind(self.IsShowMingWenDuiHuanRedPoint, self))
	local all_cfg = ConfigManager.Instance:GetAutoConfig("posy_auto")

	self.posy_base_attr_cfg = all_cfg.base
	self.list_map_base_cfg = ListToMap(self.posy_base_attr_cfg, "id")
	self.list_map_type_base_cfg = ListToMapList(self.posy_base_attr_cfg, "type")
	self.base_map_cfg = ListToMap(self.posy_base_attr_cfg, "type", "quality")
	self.posy_level_cfg = ListToMap(all_cfg.level,"lv")
	self.posy_special_cfg = all_cfg.special_slot
	self.posy_normal_cfg = all_cfg.slot --获取 天仙阁副本的通过等级 配置
	self.posy_normal_cfg_map = ListToMap(self.posy_normal_cfg, "slot_index")
	self.posy_normal_type_cfg_map = ListToMapList(self.posy_normal_cfg, "put_type")
	self.posy_compose_father_list = all_cfg.label
	self.posy_compose_cfg = ListToMapList(all_cfg.compose,"compose_type","sort_index")
	self.posy_compose_cfg_id = ListToMap(all_cfg.compose,"get_id")
	self.posy_limit_cfg = ListToMap(all_cfg.limit,"type")
	self.posy_limit_sort_cfg = ListToMap(all_cfg.soft,"tianxiange_fb_pass_level")
	self.posy_big_tab_cfg = ListToMap(all_cfg.tab, "tab_1")
	self.posy_small_tab_cfg = ListToMap(all_cfg.tab, "tab_1", "tab_2")
	self.posy_small_tab_list_cfg = ListToMapList(all_cfg.tab, "tab_1", "tab_2")
	self.posy_tab_data_cfg = ListToMap(all_cfg.tab, "tab_1", "tab_2", "slot_index")

	--self:GetLibraryListData()
	self.normal_slot_num = 8 --普通装备槽数
	self.all_slot_num = 10 --所有装备槽数

	self.all_grid_num = 0 --item数量
	self.max_grid_num = 400 --铭文背包容量

	self.big_cell_num = 2 --背包行格子
	self.fenjie_cell_num = 4
	self.select_posy_index = 1
	self.mingwen_xinjing = 0
	self.mingwen_minyin = 0
	self.mingwen_suipian = 0
	self.fenjie_color_select = {
		[1] = 1,
		[2] = 1,
		[3] = 0,
		[4] = 0,
	}
	self.fenjie_index_select = {}
	self.all_item_num_list = {}
	self.is_had_open_hecheng = false
	self.once_had_red = false
	self.grid_list = {}
	self.base_slot = {}
	self.effect_id = 1

end

function MingWenWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.MingWen_HeCheng)
	RemindManager.Instance:UnRegister(RemindName.MingWen_XiangQian)
	RemindManager.Instance:UnRegister(RemindName.MingWen_FenJie)
	RemindManager.Instance:UnRegister(RemindName.MingWen_DuiHuan)
	self.mingwen_exp = nil
	self.mingwen_xinjing = nil
	self.mingwen_minyin = nil
	self.mingwen_suipian = nil
	MingWenWGData.Instance = nil
end

function MingWenWGData:OnSCPosyBagInfo(protocol)
	self.all_grid_num = protocol.grid_count
	self.grid_list = protocol.grid_list
	self.all_item_num_list = {}
	local data = {}
	for k,v in pairs(self.grid_list) do
		if nil == self.all_item_num_list[v.item_id] then
			self.all_item_num_list[v.item_id] = {}
		end
		self.all_item_num_list[v.item_id][v.index] = v
	end
	self:MakePosyListSort()
end

function MingWenWGData:OnSCPosyGridInfo(protocol)
	local change_list = protocol.change_posy
	local change_count = protocol.grid_count
	if nil == self.grid_list or IsEmptyTable(self.grid_list) then
		self.grid_list = change_list
		self.all_grid_num = #self.grid_list
		self.all_item_num_list = {}
		for k,v in pairs(self.grid_list) do
			if nil == self.all_item_num_list[v.item_id] then
				self.all_item_num_list[v.item_id] = {}
			end
			self.all_item_num_list[v.item_id][v.index] = v
		end
		self:MakePosyListSort()
		return
	end
	for i=1, change_count do
		local is_new = true
		for k,v in pairs(self.grid_list) do
			if v.index == change_list[i].index then
				if change_list[i].type <= 0 then
					if nil == self.all_item_num_list[v.item_id] then
						self.all_item_num_list[v.item_id] = {}
					end
					self.all_item_num_list[v.item_id][v.index] = nil
					self.grid_list[k] = nil
					self.all_grid_num = self.all_grid_num -1
				else
					if nil == self.all_item_num_list[change_list[i].item_id] then
						self.all_item_num_list[change_list[i].item_id] = {}
                    end
                    if nil == self.all_item_num_list[v.item_id] then
						self.all_item_num_list[v.item_id] = {}
                    end
                    
					self.all_item_num_list[v.item_id][v.index] = nil
					v.item_id = change_list[i].item_id
					v.level = change_list[i].level
					v.type = change_list[i].type
					v.quality = change_list[i].quality

					

					self.all_item_num_list[v.item_id][v.index] = v

				end
				is_new = false
				break
			end
		end

		if is_new then
			if nil == self.all_item_num_list[change_list[i].item_id] then
				self.all_item_num_list[change_list[i].item_id] = {}
			end
			if change_list[i].type > 0 then
				self.all_item_num_list[change_list[i].item_id][change_list[i].index] = change_list[i]
				table.insert(self.grid_list,change_list[i])
				self.all_grid_num = self.all_grid_num + 1
			end
		end
	end
	self:MakePosyListSort()
end

function MingWenWGData:OnSCPosyBaseInfo(protocol)
	if self.mingwen_exp ~= nil then
		if protocol.mingwen_exp > self.mingwen_exp then
			local str = string.format(Language.Bag.GetItemTxt, Language.Bag.MingWenExp, protocol.mingwen_exp - self.mingwen_exp)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		elseif protocol.mingwen_exp < self.mingwen_exp then
			local str = string.format(Language.Bag.LoseItemTxt, Language.Bag.MingWenExp, self.mingwen_exp - protocol.mingwen_exp)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end

	self.mingwen_exp = protocol.mingwen_exp
	self.mingwen_xinjing = protocol.mingwen_xinjing
	self.mingwen_minyin = protocol.mingwen_minyin
	self.mingwen_suipian = protocol.mingwen_suipian
	self.base_slot = protocol.base_slot
	self.special_slot = protocol.special_slot
	self:CacularEquipType()
	GlobalEventSystem:Fire(OtherEventType.Ming_Wen_Change,"")
end

function MingWenWGData:ShowMingWenMoneyChange(protocol)
	if self.mingwen_xinjing and self.mingwen_xinjing < protocol.mingwen_xinjing then
		local add_num = protocol.mingwen_xinjing - self.mingwen_xinjing
		local cfg = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.VIRTUAL_ITEM_XINJING)
		--local str = string.format(Language.TreasureHunt.GetItem,ITEM_COLOR[cfg.color],cfg.name,add_num)
		--SysMsgWGCtrl.Instance:ErrorRemind(str)
	end
end

--返回三个数据 (当前铭文的经验值，)
function MingWenWGData:GetMingWenExpAndXinJing()
	return self.mingwen_exp or 0 ,self.mingwen_xinjing or 0,self.mingwen_minyin or 0
end

function MingWenWGData:GetMingWenSuiPian()
	return self.mingwen_suipian or 0
end

--根据铭文下标 返回数据
function MingWenWGData:GetEquipPosyDataBySlot(slot_index)
	return self.base_slot[slot_index]  --返回普通铭文数据
end

function MingWenWGData:GetEquipPosyData()
	return self.base_slot
end

function MingWenWGData:GetEquipPosyAttrByItemId(item_id)
	local base_cfg = self.list_map_base_cfg[item_id]
	if not base_cfg or base_cfg.attr_type == 0 then
		return {},{},{}
	end

	local attr_type = string.split(base_cfg.attr_type,"|")
	local attr_value = string.split(base_cfg.value,"|")
	local add_value = string.split(base_cfg.add_value,"|")
	return attr_type,attr_value,add_value
end

function MingWenWGData:GetEquipPosyAttrListByItemId(data)
	local capability = 0
	local attribute = AttributePool.AllocAttribute()
	local attr_list = {}
	data = data or {}
	local item_id = data.item_id or 0
	local attr_id_list, attr_value_list, add_value_list = self:GetEquipPosyAttrByItemId(item_id)
	if IsEmptyTable(attr_id_list) or IsEmptyTable(attr_id_list) or IsEmptyTable(attr_id_list) then
		return attr_list, capability
	end
	local now_level = data.level or 1
	local max_level = self:GetMingWenMaxLevel()
	local add_level = max_level - 1
	local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(item_id)
	if not base_cfg then
		return attr_list, capability
	end

	for k, v in ipairs(attr_id_list) do
		local attr_id = tonumber(v)
		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
		local attr_value = tonumber(attr_value_list[k]) or 0
		local add_value = tonumber(add_value_list[k]) or 0
		local max_level_value = base_cfg.is_uplevel > 0 and (add_value * add_level + attr_value) or 0
		attr_value = attr_value + add_value * (now_level-1)
		if not attr_list[attr_str] then
			attr_list[attr_str] = {}
			attr_list[attr_str].value = attr_value
			attr_list[attr_str].max_level_value = max_level_value
		else
			attr_list[attr_str].value = attr_list[attr_str].value + attr_value
			attr_list[attr_str].max_level_value = attr_list[attr_str].max_level_value + max_level_value
		end
		attribute[attr_str] = attribute[attr_str] + attr_value
	end

	capability = AttributeMgr.GetCapability(attribute)
	return attr_list, capability
end

----通过槽位和等级获取当前和下一级属性
function MingWenWGData:GetEquipPosyShowAttrBySlot(slot_index, level)
	local attr_list = {}
	level = (level and level > 1) and level or 1
	local max_level = self:GetMingWenMaxLevel()
	local equip_posy_data = self:GetEquipPosyDataBySlot(slot_index)
	if IsEmptyTable(equip_posy_data) or equip_posy_data.posy.type <= 0 then
		return attr_list
	end

	-- 天道石影响属性百分比,不影响百分比
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.MINGWEN)
	charm_rate = charm_rate/10000

	local attr_type, attr_value, add_value = self:GetEquipPosyAttrByItemId(equip_posy_data.posy.item_id)
	local data_attr_value, data_add_value = 0, 0
	if not IsEmptyTable(attr_type) then
		for k, v in ipairs(attr_type) do
			local data = {}
			data.attr_str = tonumber(v)
			data_attr_value = attr_value[k] and tonumber(attr_value[k]) or 0
			data_add_value = add_value[k] and tonumber(add_value[k]) or 0
			data.attr_value = data_attr_value + data_add_value * (level - 1)
			data.add_value = level >= max_level and 0 or data_add_value

			data.attr_value = data.attr_value * (charm_rate + 1)
			data.add_value = data.add_value * (charm_rate + 1)
			table.insert(attr_list, data)
		end
	end

	return attr_list
end

--通过道具id和等级获取当前属性
function MingWenWGData:GetEquipPosyShowAttrByItemId(item_id, level)
	local attr_list = {}
	level = (level and level > 1) and level or 1
	local max_level = self:GetMingWenMaxLevel()

	-- 天道石影响属性百分比,不影响百分比
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.MINGWEN)
	charm_rate = charm_rate/10000

	local attr_type, attr_value, add_value = self:GetEquipPosyAttrByItemId(item_id)
	local data_attr_value, data_add_value = 0, 0
	if not IsEmptyTable(attr_type) then
		for k, v in ipairs(attr_type) do
			local data = {}
			data.attr_str = tonumber(v)
			data_attr_value = attr_value[k] and tonumber(attr_value[k]) or 0
			data_add_value = add_value[k] and tonumber(add_value[k]) or 0
			data.attr_value = data_attr_value + data_add_value * (level - 1)
			data.attr_value = data.attr_value * (charm_rate + 1)
			table.insert(attr_list, data)
		end
	end

	return attr_list
end

--通过道具id获取改道具的初始属性
function MingWenWGData:GetEquipPosyBaseAttrByItemId(item_id)
	local attr_list = {}

	-- 天道石影响属性百分比,不影响百分比
	-- local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.MINGWEN)
	-- charm_rate = charm_rate/10000

	local attr_type, attr_value = self:GetEquipPosyAttrByItemId(item_id)
	if not IsEmptyTable(attr_type) then
		for k, v in ipairs(attr_type) do
			local data = {}
			data.attr_str = tonumber(v)
			data.attr_value = attr_value[k] and tonumber(attr_value[k]) or 0
			--data.attr_value = data.attr_value * (charm_rate + 1)
			table.insert(attr_list, data)
		end
	end

	return attr_list
end

--获取当前装备的所有万鬼幡的属性
function MingWenWGData:GetAllEquipPosyShowAttr()
	local attr_list = {}

	-- 天道石影响属性百分比,不影响百分比
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.MINGWEN)
	charm_rate = charm_rate/10000

	for k, v in pairs(self.base_slot) do
		if v.posy.item_id > 0 then
			local attr_type, attr_value, add_value = self:GetEquipPosyAttrByItemId(v.posy.item_id)
			local data_attr_value, data_add_value = 0, 0
			if not IsEmptyTable(attr_type) then
				for k1, v1 in ipairs(attr_type) do
					local data = {}
					local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(v1))
					local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
					data.attr_str = attr_str
					data_attr_value = attr_value[k1] and tonumber(attr_value[k1]) or 0
					data_add_value = add_value[k1] and tonumber(add_value[k1]) or 0
					data.attr_value = data_attr_value + data_add_value * (v.posy.level - 1)
					data.attr_value = data.attr_value * (charm_rate + 1)
					if attr_list[attr_sort] then
						attr_list[attr_sort].attr_value = attr_list[attr_sort].attr_value + data.attr_value
					else
						attr_list[attr_sort] = data
					end
				end
			end
		end
	end

	return SortTableKey(attr_list)
end

function MingWenWGData:MakePosyListSort()
	if self.all_grid_num >= 2 then

		local table1 = {}
		for k,v in pairs(self.grid_list) do
			table.insert(table1,v)
		end
		table.sort(table1,SortTools.KeyUpperSorters("quality","level","type"))
		self.grid_list = table1
	end
end

--计算装备槽不可穿戴属性类型（身上装备属性不能重复）特殊符文槽排除其他符文类型
function MingWenWGData:CacularEquipType()
	self.unwearalbe_table = {}

	for p = 1, self.all_slot_num do
		self.unwearalbe_table[p] = {}
	end

	for i =1 ,self.normal_slot_num do
		if self.base_slot[i].posy and self.base_slot[i].posy.type > 0 then
			local base_cfg = self.list_map_base_cfg[self.base_slot[i].posy.item_id]
			local attr_type = string.split(base_cfg.attr_type,"|")
			for t = 1,self.normal_slot_num do
				if i ~= t then
					for k,v in pairs(attr_type) do
						self.unwearalbe_table[t][v] = true
					end
				end
			end
		end
	end
end

function MingWenWGData:GetJumpTabtRedIndexByIndex(big_index)
	local small_tab = self:GetSmallTabCfgByIndex(big_index)
	for k, v in pairs(small_tab) do
		if self:GetSmallTabtRedByIndex(big_index, v.tab_2) then
			return v.tab_2
		end
	end

	return 1
end

function MingWenWGData:GetBigTabtRedByIndex(big_index)
	local small_tab = self:GetSmallTabCfgByIndex(big_index)
	for k, v in pairs(small_tab) do
		if self:GetSmallTabtRedByIndex(big_index, v.tab_2) then
			return true
		end
	end

	return false
end

function MingWenWGData:GetSmallTabtRedByIndex(big_index, small_index)
	local tab_data = self:GetTabDataCfgByIndex(big_index, small_index)
	for k, v in pairs(tab_data) do
		if self:GetIsSlotRedByIndex(v.slot_index) then
			return true
		end
	end

	return false
end

--获取槽位是否红点
function MingWenWGData:GetIsSlotRedByIndex(slot_index)
	if self:GetSlotIndexIsOpen(slot_index) then
		local is_red = 0
		local is_red2 = 0
		is_red2 = MingWenWGData.Instance:CanBasePosyUpGrade(slot_index)
		is_red = self:GetSlotIsHasBetterByIndex(slot_index)

	 	return is_red == 1 or is_red2 == 1
	end

	return false
end

--该槽位是否有更好的魂印可以穿戴
function MingWenWGData:GetSlotIsHasBetterByIndex(slot_index)
	if not self.grid_list or IsEmptyTable(self.grid_list) then
		return 0
	end

	local type = self.posy_normal_cfg_map[slot_index].put_type
	local equip_data = self:GetEquipPosyDataBySlot(slot_index)
	for k,v in pairs(self.grid_list) do
		if v.type == type then
			if self:GetPosyIsLimite(v.item_id) then

			elseif equip_data.posy.type <= 0 or equip_data.posy.quality < v.quality then
				return 1
			end
		end
	end

	return 0
end

function MingWenWGData:GetItemIsBetterBySlotIndex(slot_index, item_id)
	if self:GetPosyIsLimite(item_id) then
		return false
	end

	local equip_data = self:GetEquipPosyDataBySlot(slot_index)
	local base_cfg = self:GetMingWenBaseCfgByID(item_id)
	if equip_data.posy.type <= 0 or base_cfg.quality > equip_data.posy.quality then
		return true
	end

	return false
end

function MingWenWGData:GetItemIsBetter(item_id, type, quality)
	if self:GetPosyIsLimite(item_id) then
		return false
	end

	if not type or not quality then
		local base_cfg = self:GetMingWenBaseCfgByID(item_id)
		type = base_cfg.type
		quality = base_cfg.quality
	end

	local cfg_list = self.posy_normal_type_cfg_map[type]
	if cfg_list then
		for k, v in pairs(cfg_list) do
			local equip_data = self:GetEquipPosyDataBySlot(v.slot_index)

			if equip_data.posy.type <= 0 or quality > equip_data.posy.quality then
				return true
			end
		end
	end

	return false
end

function MingWenWGData:IsActive(item_id)
	local base_cfg = self:GetMingWenBaseCfgByID(item_id)
	if base_cfg then
		local cfg_list = self.posy_normal_type_cfg_map[base_cfg.type]
		if cfg_list then
			for k, v in pairs(cfg_list) do
				local data = self:GetEquipPosyDataBySlot(v.slot_index)
				if data.posy.item_id > 0 and data.posy.quality >= base_cfg.quality then
					return true
				end
			end

		end
	end
	return false
end

function MingWenWGData:GetBaseBagPosyData(slot_index, is_all)
	local data_list = {}
	if not self.grid_list or IsEmptyTable(self.grid_list) then
		return data_list
	end

	if is_all then
		return data_list
	else
		local type = self.posy_normal_cfg_map[slot_index].put_type
		for k,v in pairs(self.grid_list) do
			if v.type == type then
				table.insert(data_list, v)
			end
		end
		table.sort(data_list, SortTools.KeyUpperSorters("quality"))
		return data_list
	end
end

----特殊槽位slot_index，posy_type能否装备
function MingWenWGData:ChackIsSpecialAndWearable(slot_index,posy_type)
	for k,v in pairs(self.posy_special_cfg) do
		if slot_index == v.slot_index and  v.put_type == posy_type then
			return true
		end
	end
	return false
end

function MingWenWGData:GetAllPosyItem()
	return self.grid_list or {}
end

function MingWenWGData:GetPosyBagListData(index, is_show_all)
	index = index or 0
	local data = {}
	if is_show_all then
		data = self:GetAllPosyItem()
	else
		data = self:GetBaseBagPosyData(index)
	end
	local list_data = {}
	local num = 1
	for k,v in pairs(data) do
		local a = math.ceil(num / self.big_cell_num)
		local b = num % self.big_cell_num
		if nil == list_data[a] then
			list_data[a] = {}
		end
		if b == 0 then
			b = self.big_cell_num
		end
		list_data[a][b] = v
		num = num + 1
	end
	return list_data
end

function MingWenWGData:GetFenJieListData1()
	local data = self:GetAllPosyItem()
	local list_data = {}
	for k,v in pairs(data) do
		local base_cfg = self:GetMingWenBaseCfgByID(v.item_id)
		if base_cfg and base_cfg.cost_type >= 0 then
			table.insert(list_data, v)
		end
	end

	return list_data
end

function MingWenWGData:GetSelectPosyIndex()
	return self.select_posy_index or 0
end

function MingWenWGData:SetSelectPosyIndex(index)
	self.select_posy_index = index
end

function MingWenWGData:GetComposeFatherList()
	local father_type_list = {}
	local num = 0
	for k,v in pairs(self.posy_compose_father_list) do
		father_type_list[v.compose_type] = v
		num = num + 1
	end
	return father_type_list, num
end

--得到符文槽是否开启
function MingWenWGData:GetSlotIndexIsOpen(slot_index)
	local pass_level = self:GetPassLevel() --得到当前修真路通关的层数
	--如果传进来的下标 大于 普通装备槽数 8  就停止执行返回true，0 
	-- if slot_index > self.normal_slot_num then
	-- 	return true,0
	-- end

	for k,v in pairs(self.posy_normal_cfg) do
		if v.slot_index == slot_index then
			return pass_level >= v.tianxiange_fb_pass_level , v.tianxiange_fb_pass_level
		end
	end
end

function MingWenWGData:GetNextFBOpenLevel()
	local pass_level = self:GetPassLevel() --得到当前修真路通关的层数
	for k,v in pairs(self.posy_normal_cfg_map) do  --遍历配置表里 修真路所有层
		if v.tianxiange_fb_pass_level > pass_level then  --如果大于当前通关层数
			return v.tianxiange_fb_pass_level  --就返回该层数
		end
	end
	return -1
end

-- 获得属性名字
function MingWenWGData:GetAttributeNameListByType(attr_type_list)
	local name_list = {}
	local is_pre = {}
	if not IsEmptyTable(attr_type_list) then
		for k,v in pairs(attr_type_list) do
			local attr_type = tonumber(v)
			name_list[k] = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_type, true)
			is_pre[k] = EquipmentWGData.Instance:GetAttrIsPerByAttrId(attr_type)
		end
	end
	return name_list,is_pre
end

function MingWenWGData:InitFenJieSelectColor(is_red_check)
	for i =1,4 do
		local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
		local index = role_id + i
		if PlayerPrefsUtil.HasKey("posy_fenjie_color"..index) then
			self.fenjie_color_select[i] = PlayerPrefsUtil.GetInt("posy_fenjie_color"..index)
		else
			PlayerPrefsUtil.SetInt("posy_fenjie_color"..index,self.fenjie_color_select[i])
		end
	end
	if is_red_check then
		return self:FenJieRedCheck()
	else
		self.fenjie_index_select = {}
		self:FenJieSelectColorItem()
	end
end

function MingWenWGData:CheckFenJieColorSelect(color)
	if self.fenjie_color_select then
	 	return self.fenjie_color_select[color]
	 end

	return 0
end

function MingWenWGData:SetFenJieColorSelect(color,value) -- 1,0
	self.fenjie_color_select[color] = value
	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local index = role_id + color
	PlayerPrefsUtil.SetInt("posy_fenjie_color"..index,value)
	RemindManager.Instance:Fire(RemindName.MingWen_FenJie)
end

function MingWenWGData:FenJieSelectColorItem(is_red_check)
	for k,v in pairs(self.grid_list) do
		if v and v.type > 0 then
			self.fenjie_index_select[v.index] = {}
			self.fenjie_index_select[v.index].index = v.index
			self.fenjie_index_select[v.index].fenjie_data = v
			self.fenjie_index_select[v.index].quality = v.quality
			local cost_type = self.list_map_base_cfg[v.item_id].cost_type or 0
			self.fenjie_index_select[v.index].cost_type = cost_type
			self.fenjie_index_select[v.index].item_id = v.item_id
			local is_hecheng = self:CheckMingWenIsFromHeCheng(v.item_id)
			if is_hecheng then
				local compose_data = self.posy_compose_cfg_id[v.item_id]
				self.fenjie_index_select[v.index].get_jingyan = self.posy_level_cfg[v.level]["get_jingyan_"..cost_type] - self.posy_level_cfg[1]["get_jingyan_"..cost_type]
				self.fenjie_index_select[v.index].get_xinjing = compose_data and compose_data.xin_jing or 0
				-- 消耗道具数量
				self.fenjie_index_select[v.index].get_stuff_num = compose_data and compose_data.stuff_num_vec or 0
				-- 消耗魂印(云篆)数量
				local temp_posy_num = tonumber(compose_data.posy_num_vec)
				self.fenjie_index_select[v.index].get_posy_num_vec = compose_data and temp_posy_num or 0
			else
				self.fenjie_index_select[v.index].get_jingyan = self.posy_level_cfg[v.level]["get_jingyan_"..cost_type]
				self.fenjie_index_select[v.index].get_xinjing = self.posy_level_cfg[v.level]["get_xinjing_"..cost_type]
				self.fenjie_index_select[v.index].get_mingyin = self.posy_level_cfg[v.level]["get_mingyin_"..cost_type]
			end
			self.fenjie_index_select[v.index].type = v.type
			if self.fenjie_color_select[v.quality] and self.fenjie_color_select[v.quality] == 1 and v.type ~= 2 and v.type ~=3 or v.type == 1 then --铭文精华1固定选中
				self.fenjie_index_select[v.index].select = 1
			else
				self.fenjie_index_select[v.index].select = 0
			end
		end
	end
end

function MingWenWGData:FenJieRedCheck()
	if not self.grid_list or IsEmptyTable(self.grid_list) then return 0 end
	for k,v in pairs(self.grid_list) do
		if v and v.type > 0 then
			if self.fenjie_color_select[v.quality] and self.fenjie_color_select[v.quality] == 1 and v.type ~= 2 and v.type ~=3 or v.type == 1 then --铭文精华1固定选中
				return 1
			end
		end
	end
	return 0
end

function MingWenWGData:FenJieFlushColorData(color, value, index, data) -- value = color_select
	if not self.fenjie_index_select then 
		return 
	end
	for k,v in pairs(self.fenjie_index_select) do
		if v.quality == color and v.type ~= 2 and v.type ~=3 then
			v.select = value --改变状态
		end
	end
end

function MingWenWGData:GetFenJieExpAndXinJing()
	if not self.fenjie_index_select then return 0,0 end
	local exp = 0
	local xing_jing = 0
	for k,v in pairs(self.fenjie_index_select) do
		if v.select == 1 then
			exp = exp + v.get_jingyan
			xing_jing = xing_jing + v.get_xinjing
		end
	end
	return exp,xing_jing
end

function MingWenWGData:FenJieSelectByIndex(index, value)
	if nil == self.fenjie_index_select or not self.fenjie_index_select[index] then return end
	self.fenjie_index_select[index].select = value
end

function MingWenWGData:CheckIndexIsSelect(index, data)
	if nil == self.fenjie_index_select or not self.fenjie_index_select[index] then return false end
	return self.fenjie_index_select[index].select == 1
end

function MingWenWGData:GetAllFenJieSelectItem()
	return self.fenjie_index_select or {}
end

function MingWenWGData:GetComPoseCellList(compose_type)
	return self.posy_compose_cfg[compose_type] or {}
end

function MingWenWGData:GetPosyIsLimite(item_id)
	local base_cfg = self.list_map_base_cfg[item_id]
	if not base_cfg then
		return false , 0
	end

	local limit_cfg = self.posy_limit_cfg[base_cfg.type]
	if not limit_cfg then
		return false , 0
	end

	local pass_level = self:GetPassLevel()
	return limit_cfg.tianxiange_fb_pass_level > pass_level,limit_cfg.tianxiange_fb_pass_level
end

function MingWenWGData:GetPosyLimitCfgByType(type)
	return self.posy_limit_cfg[type] or {}
end

function MingWenWGData:GetPosyLimitCfg()
	return self.posy_limit_cfg or {}
end

--获取总览界面总数据
function MingWenWGData:GetPosyLibraryAllData()
	local data_list = {}
	for k, v in pairs(self.posy_limit_cfg) do
		local data = {}
		local base_cfg = self.base_map_cfg[v.type][5]
		if base_cfg and v.view_show_order >= 0 then
			data.item_id = base_cfg.id
			data.type = v.type
			data.quality = 5
			data.view_show_order = v.view_show_order
			data.tianxiange_fb_pass_level = v.tianxiange_fb_pass_level
			table.insert(data_list, data)
		end
	end

	table.sort(data_list, SortTools.KeyLowerSorter("view_show_order"))
	return data_list
end

function MingWenWGData:GetMingWenBaseCfgByTypeAndQuality(type, quality)
	local base_cfg = self.base_map_cfg[type][quality]
	return base_cfg
end

function MingWenWGData:GetMingWenBaseCfgByID(item_id)
	return self.list_map_base_cfg[item_id]
end

function MingWenWGData:GetMingWenBagItemByID(item_id)
	return self.all_item_num_list[item_id] or {}
end

function MingWenWGData:GetMingWenBagItemNum(item_id)
	if self.all_item_num_list and self.all_item_num_list[item_id] then
		local num = 0
		for k,v in pairs(self.all_item_num_list[item_id]) do
			num = num + 1
		end
		return num
	else
		return 0
	end
end

function MingWenWGData:GetMingWenLvCfgByLv(level)
	return self.posy_level_cfg[level] or {}
end

function MingWenWGData:GetMingWenMaxLevel()
	return self.posy_level_cfg and #self.posy_level_cfg or 0
end

function MingWenWGData:GetMingWenFenJieGet(data)
	local e_base_cfg = self:GetMingWenBaseCfgByID(data.item_id)
	if not e_base_cfg then
		return 0, 0
	end

	local cost_type =  e_base_cfg.cost_type
	local level_cfg = self:GetMingWenLvCfgByLv(data.level)
	local one_level_cfg = self:GetMingWenLvCfgByLv(1)
	local get_jingyan = level_cfg["get_jingyan_"..cost_type] or 0
	local get_exp = level_cfg["get_mingyin_"..cost_type] or 0
	get_jingyan = get_jingyan - one_level_cfg["get_jingyan_"..cost_type]
	get_exp = get_exp - one_level_cfg["get_mingyin_"..cost_type]
	return get_jingyan, get_exp
end

function MingWenWGData:CacularComposeLevel(cost_type,exp,minyin)
	local level = 1
	for k,v in pairs(self.posy_level_cfg) do
		if not v["cost_jingyan_"..cost_type] or not v["cost_mingyin_"..cost_type] then
			return level
		end

		if v["cost_jingyan_"..cost_type] == 0 then
			return 1
		end

		if v["cost_jingyan_"..cost_type] <= exp and v["cost_mingyin_"..cost_type] <= minyin then
			exp = exp - v["cost_jingyan_"..cost_type]
			minyin = minyin - v["cost_mingyin_"..cost_type]
			level = k + 1
		else
			return level
		end
	end
	return level
end

function MingWenWGData:GetLibraryListData()
	if self.posy_library_list then
		return self.posy_library_list
	end
	self.posy_library_list = {}
	local type_list = {}
	local layer_list = {}
	for k,v in pairs(self.posy_limit_cfg) do
		if self.posy_limit_sort_cfg[v.tianxiange_fb_pass_level] then
			local index = self.posy_limit_sort_cfg[v.tianxiange_fb_pass_level].soft
			if nil == type_list[index] then
				type_list[index] = {}
				layer_list[index] = v.tianxiange_fb_pass_level
			end
			type_list[index][k] = true
		end
	end


	local data = {}
	local data_count = 0
	for k,v in pairs(type_list) do
		data_count = data_count + 1
		data[data_count] = {level = layer_list[k]}
		local chack_list = self:GetSameLevelLockPosy(v)
		local num = 1
		for t,q in pairs(chack_list) do
			if num == 7 then
				num = 1
			end
			if num == 1 then
				data_count = data_count + 1
				data[data_count] = {}
			end
			data[data_count][num] = q
			num = num + 1
		end
	end
	self.posy_library_list = data
	return self.posy_library_list
end

function MingWenWGData:GetSameLevelLockPosy(list)
	local chack_list = {}
	for k,v in pairs(self.posy_base_attr_cfg) do
		if list[v.type] and v.quality >= 2 then
			chack_list[#chack_list + 1] = v
		end
	end
	return chack_list
end

function MingWenWGData:GetMingWenItemCanHeCheng(item_id)
	if nil == item_id then
		return 0
	end
	local is_limite,condition = MingWenWGData.Instance:GetPosyIsLimite(item_id)

	if is_limite then
		return 0
	end
	local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(item_id)
	local compose_data = self.posy_compose_cfg_id[item_id]
	if not base_cfg or IsEmptyTable(compose_data) then
		return 0
	else
		local final_is_enough = true
		if compose_data.stuff_id_vec ~= 0 then
			local have_num = ItemWGData.Instance:GetItemNumInBagById(tonumber(compose_data.stuff_id_vec))
			final_is_enough = have_num >= compose_data.stuff_num_vec
		elseif compose_data.xin_jing > 0 then
			final_is_enough = self.mingwen_xinjing >= compose_data.xin_jing
		end
		-- if not final_is_enough then
		-- 	return 0
		-- end
		local posy_id_list = string.split(compose_data.posy_id_vec,"|")
		local posy_num_list = string.split(compose_data.posy_num_vec,"|")
		local need_double = false
		local posy_id_vec = {}
		local posy_num_vec = {}

		for k,v in pairs(posy_id_list) do
			posy_id_vec[k] = tonumber(v)
		end

		for t,q in pairs(posy_num_list) do
			posy_num_vec[t] = tonumber(q)
		end

		if posy_id_vec[2] then
			need_double = true
		end
		local equip_posy = {}

		local slot_data = MingWenWGData.Instance:GetEquipPosyData()
		if compose_data.is_cost_inlay == 1 then
			for k, v in pairs(slot_data) do
				local solt_data = v
				if not IsEmptyTable(solt_data) then
					if posy_id_vec[1] == solt_data.posy.item_id then
						equip_posy[1] = true
					elseif posy_id_vec[2] and posy_id_vec[2] == solt_data.posy.item_id then
						equip_posy[2] = true
					end
				end
			end
		end

		for i =1 ,2 do
			if i == 1 or need_double then
				local num = MingWenWGData.Instance:GetMingWenBagItemNum(posy_id_vec[i])
				if equip_posy[i] then
					num = num +1
				end
				local is_enough1 = num >= posy_num_vec[i]
				if not is_enough1 then
					final_is_enough = false
				end
			end
		end

		-- if self.list_map_type_base_cfg and self.list_map_type_base_cfg[base_cfg.type] then
		-- 	for k,v in pairs(self.list_map_type_base_cfg[base_cfg.type]) do
		-- 		if  v.quality >= base_cfg.quality and MingWenWGData.Instance:GetMingWenBagItemNum(v.id) > 0 then
		-- 			return 0
		-- 		end
		-- 	end
		-- end

		-- if compose_data.is_cost_inlay == 1 then
		-- 	for k, v in pairs(slot_data) do
		-- 		local solt_data = v
		-- 		if not IsEmptyTable(solt_data) then
		-- 			if solt_data.posy.type == base_cfg.type and solt_data.posy.quality >= base_cfg.quality then
		-- 				return 0
		-- 			end
		-- 		end
		-- 	end
		-- end

		if final_is_enough then
			return 1
		end

		return 0
	end
end

function MingWenWGData:SetIsOpenHeCheng()
	if self.once_had_red then
		self.is_had_open_hecheng = true
	end
end

function MingWenWGData:GetPassLevel()
	local pase_level = FuBenPanelWGData.Instance:GetPassLevel()
	if pase_level < 0 then
		pase_level = 0
	end
	return pase_level or 0
end

function MingWenWGData:GetCurToggleNum()
	local pase_level = self:GetPassLevel()
	local big_tab_cfg = self:GetBigTabCfg()
	local small_tab_cfg = self:GetSmallTabCfg()
	local num = 0

	if small_tab_cfg then
		for i = 1, #big_tab_cfg do
			if pase_level >= small_tab_cfg[i][1].tower_pass_show then
				num = num + 1
			end
		end
	end

	return num
end

--通过槽位索引获取是否是特殊槽位
function MingWenWGData:GetIsSpecialBySlotIndex(index)
	return self:GetIsSpecialByType(self.posy_normal_cfg_map[index].put_type)
end

--通过类型获取是否是特殊卡牌
function MingWenWGData:GetIsSpecialByType(type)
	local cfg = self:GetPosyLimitCfgByType(type)
	return cfg and cfg.is_special or 0
end

function MingWenWGData:GetPosySlotCfgBySlotIndex(index)
	return self.posy_normal_cfg_map[index] or {}
end

function MingWenWGData:GetSmallToggleData(index)
	local pase_level = self:GetPassLevel()
	local small_tab_cfg = self:GetSmallTabCfg()
	local data_list = {}

	if small_tab_cfg then
		for k, v in pairs(small_tab_cfg[index]) do
			if pase_level >= v.tower_pass_show then
				table.insert(data_list, v)
			end
		end
	end

	return data_list
end

function MingWenWGData:GetBigTabCfg()
	return self.posy_big_tab_cfg
end

function MingWenWGData:GetSmallTabCfg()
	return self.posy_small_tab_cfg
end

function MingWenWGData:GetTabDataCfg()
	return self.posy_tab_data_cfg
end

function MingWenWGData:GetBigTabCfgByIndex(big_index)
	return self.posy_big_tab_cfg[big_index] or {}
end

function MingWenWGData:GetSmallTabCfgByIndex(big_index)
	return self.posy_small_tab_cfg[big_index] or {}
end

function MingWenWGData:GetSmallTabListCfgByIndex(big_index, small_index)
	return (self.posy_small_tab_list_cfg[big_index] or {})[small_index] or {}
end

function MingWenWGData:GetMingWenItemCellDataByItemId(item_id)
	local data = {}
	local cfg = self:GetMingWenBaseCfgByID(item_id)
	if cfg then
		data.item_id = item_id
		data.type = cfg.type
		data.quality = cfg.quality
	end

	return data
end

function MingWenWGData:GetTabDataCfgByIndex(big_index, small_index)
	return (self.posy_tab_data_cfg[big_index] or {})[small_index] or {}
end

function MingWenWGData:IsShowMainViewHeChengRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.MingWenView) then
		return 0
	end

	if self.is_had_open_hecheng then
		return 0
	end

	local is_red = self:IsShowMingWenHeChengRedPoint()
	if is_red == 1 then
		self.once_had_red = true
	end
	return is_red
end

function MingWenWGData:IsShowMingWenHeChengRedPoint() --合成红点
	for k,v in pairs(self.posy_compose_cfg_id) do
		if 1 == self:GetMingWenItemCanHeCheng(v.get_id) then

			return 1
		end
	end
	return 0
end

function MingWenWGData:IsShowMingWenXiangQianRedPoint() --镶嵌红点
	if not FunOpen.Instance:GetFunIsOpened(FunName.MingWenView) then
		return 0
	end

	for k, v in pairs(self.base_slot) do
		if self:GetIsSlotRedByIndex(k) then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MingWen_XianQian, 1, function()
				ViewManager.Instance:Open(GuideModuleName.MingWenView,TabIndex.ming_wen_xiang_qian)
			end)
			return 1
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MingWen_XianQian, 0, function()
		ViewManager.Instance:Open(GuideModuleName.MingWenView,TabIndex.ming_wen_xiang_qian)
	end)
	return 0
end

function MingWenWGData:IsShowMingWenFenJieRedPoint() --分解红点
	--策划：变强按钮，铭文的分解和提升不要同时出现，优先提示提升，没有提升的情况下，才出现分解提示 红点也一样处理
	if not FunOpen.Instance:GetFunIsOpened(FunName.MingWenView) then
		return 0
	end

	local is_fenjie_red = 0
	local is_xiangqian_red = self:IsShowMingWenXiangQianRedPoint()
	if is_xiangqian_red and is_xiangqian_red <= 0 then
		is_fenjie_red = self:InitFenJieSelectColor(true)
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MING_WEN_FJ, is_fenjie_red, function ()
		ViewManager.Instance:Open(GuideModuleName.MingWenView,TabIndex.ming_wen_fen_jie)
	end)
	return is_fenjie_red or 0
end

function MingWenWGData:IsShowMingWenDuiHuanRedPoint() -- 兑换红点
	return 0
end

function MingWenWGData:GetMingWenBagNum()
	return self.all_grid_num,self.max_grid_num
end

function MingWenWGData:GetAllFightPower()
	return 0
end

function MingWenWGData:SetDoingFenJie(value)
	self.is_doing_fenjie = value
	if value then
		self.effect_id = self.effect_id + 1
	end
end

function MingWenWGData:CheckIsDoingFenjie()
	return self.is_doing_fenjie or false,self.effect_id
end


function MingWenWGData:ShowGetWayPosyIndex(slot_index)
	local data = {}
	if slot_index == 9 then
		data.item_id = 30015
	elseif slot_index == 10 then
		data.item_id = 30005
	end
	return data
end

function MingWenWGData:CanBasePosyUpGrade(slot_index)
	local posy_data = self:GetEquipPosyDataBySlot(slot_index)
	if IsEmptyTable(posy_data) or posy_data.posy.type <= 0 then
		return 0
	end

	local base_cfg = self:GetMingWenBaseCfgByID(posy_data.posy.item_id)
	if not base_cfg or base_cfg.is_uplevel == 0 then return 0 end

	local level_cfg = self:GetMingWenLvCfgByLv(posy_data.posy.level)
	local next_level = MingWenWGData.Instance:GetMingWenLvCfgByLv(posy_data.posy.level + 1)
	if IsEmptyTable(next_level) then
		return 0
	end
	local need_jingyan = level_cfg["cost_jingyan_"..base_cfg.cost_type]
	local need_mingyin = level_cfg["cost_mingyin_"..base_cfg.cost_type]
	local need_xinjing = level_cfg["cost_xinjing_"..base_cfg.cost_type]

	local have_exp,have_xinjing,have_minying = self:GetMingWenExpAndXinJing()
	if have_exp >= need_jingyan and have_xinjing >= need_xinjing and have_minying >= need_mingyin then
		return 1
	end
	return 0
end

function MingWenWGData:GetDuiHuanList()
	if not self.xunbao_mingwen_list then
		self.xunbao_mingwen_list = TreasureHuntWGData.Instance:GetConvertShopCfgByType(4)
	end
	local index1 = 0
	local index2 = 0
	local open_list = {}
	local close_list = {}
	for k,v in pairs(self.xunbao_mingwen_list) do
		local is_limite,need_level = MingWenWGData.Instance:GetPosyIsLimite(v.itemid)
		if is_limite then
			index2 = index2 + 1
			close_list[index2] = v
		else
			index1 = index1 + 1
			open_list[index1] = v
		end
	end

	for k,v in pairs(close_list) do
		index1 = index1 + 1
		open_list[index1] = v
	end
	return open_list
end

function MingWenWGData:CheckMingWenIsFromHeCheng(item_id)
	if self.posy_compose_cfg_id and self.posy_compose_cfg_id[item_id] then
		return true
	else
		return false
	end
end

-- 通过id去拿能分解得到的东西
function MingWenWGData:GetFenJieReward(item_id)
	return (self.posy_compose_cfg_id or {})[item_id] or {}
end

-- 一份内存，重复操作
function MingWenWGData:FlushFenJieReward(item_id, data, list)
	local reward_id_cfg = self:GetFenJieReward(item_id)
	local is_hecheng = self:CheckMingWenIsFromHeCheng(item_id)

	if IsEmptyTable(reward_id_cfg) then
		local reward_lv_cfg = self:GetMingWenLvCfgByLv(data.level)
		if not IsEmptyTable(reward_lv_cfg) then
			local exp = self:GetFenJieExp(data.index)
			if exp <= 0 then
				local mingyin = self:GetFenJieMingYin(data.index)
				if mingyin > 0 then
					list = self:GetFenJieList(list, COMMON_CONSTS.VIRTUAL_ITEM_MINYIN, mingyin) -- 分解只得经验的铭文
				end
			else
				list = self:GetFenJieList(list, COMMON_CONSTS.VIRTUAL_ITEM_MINGWEN, exp) -- 分解只得经验的铭文
			end
		end
		return list
	end

	local posy_hy_vec_list = nil
	if type(reward_id_cfg.posy_id_vec) == "string" then
		posy_hy_vec_list = string.split(reward_id_cfg.posy_id_vec,"|")  -- 消耗的魂印
	else
		posy_hy_vec_list = {}
		table.insert(posy_hy_vec_list, reward_id_cfg.posy_id_vec)
	end

	local posy_hy_num_list = nil
	if type(reward_id_cfg.posy_id_vec) == "string" then
		posy_hy_num_list = string.split(reward_id_cfg.posy_num_vec,"|")  -- 消耗魂印的数量
	else
		posy_hy_num_list = {}
		table.insert(posy_hy_num_list, reward_id_cfg.posy_num_vec)
	end

	if (not posy_hy_vec_list) or (not posy_hy_num_list) then
		return nil
	end

	if is_hecheng then
		for k, v in pairs(posy_hy_vec_list) do 
			local temp_item_id = tonumber(v) or 0
			local temp_num = tonumber(posy_hy_num_list[k]) or 0
			list = self:GetFenJieList(list, temp_item_id, temp_num)
		end

		list = self:GetFenJieList(list, reward_id_cfg.stuff_id_vec, reward_id_cfg.stuff_num_vec)
		if reward_id_cfg.xin_jing > 0 then
			list = self:GetFenJieList(list, COMMON_CONSTS.VIRTUAL_ITEM_XINJING, reward_id_cfg.xin_jing)
		end

		local exp = self:GetFenJieExp(data.index)
		if exp <= 0 then
			local mingyin = self:GetFenJieMingYin(data.index)
			if mingyin and mingyin > 0 then
				list = self:GetFenJieList(list, COMMON_CONSTS.VIRTUAL_ITEM_MINYIN, mingyin)
			end
		else
			list = self:GetFenJieList(list, COMMON_CONSTS.VIRTUAL_ITEM_MINGWEN, exp)
		end
	end

	return list
end

function MingWenWGData:GetFenJieList(list, item_id, var_num)
	if (not list) then
		list = {}
	end

	if item_id == 0 then
		return list
	end

	if list[item_id] then
		list[item_id].num = list[item_id].num + var_num -- 数量变化
	else
		list[item_id] = {}
		list[item_id] = {item_id = item_id, num = var_num, id_bind = 1}
	end

	return list
end

function MingWenWGData:GetFenJieExp(index)
	return (self.fenjie_index_select or {})[index].get_jingyan
end

function MingWenWGData:GetFenJieMingYin(index)
	return (self.fenjie_index_select or {})[index].get_mingyin
end