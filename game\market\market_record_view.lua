function MarketView:RecordLoadCallBack()
	self.record_list_view = AsyncListView.New(MarketRecord, self.node_list["record_list"])
end

function MarketView:RecordReleaseCallBack()
	if nil ~= self.record_list_view then
		self.record_list_view:DeleteMe()
		self.record_list_view = nil
	end
end

function MarketView:RecordShowIndexCallBack()
	MarketWGCtrl.Instance:SendCSAuctionLog()
end

function MarketView:RecordOnFlush(param_list)
	local record_list_info = MarketWGData.Instance:GetRecordInfoList()
	self.record_list_view:SetDataList(record_list_info)
	self.node_list["record_empty_tips"]:SetActive(IsEmptyTable(record_list_info))
end

---------------------市场记录------------------
MarketRecord = MarketRecord or BaseClass(BaseRender)
function MarketRecord:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function MarketRecord:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function MarketRecord:OnFlush()
	if not self.data then
		return
	end

	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	local item_data = {}
	item_data.item_id = self.data.item_data.item_id
	item_data.is_bind = self.data.item_data.is_bind
	local item_count = (other_cfg.gold_bind_id == self.data.item_data.item_id) and (self.data.item_data.num * other_cfg.min_shelves_gond_bind_num) or self.data.item_data.num
	item_data.num = item_count
	item_data.star_level = ((self.data.item_data or {}).param or {}).star_level or 0
	self.item_cell:SetData(item_data)
	
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)

	-- 物品名称
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_data.item_id)

	-- 等级
	self.node_list["item_desc"].text.text = string.format(Language.Common.Level, item_cfg.limit_level)

	-- 交易时间
	self.node_list["exchange_time"].text.text = TimeUtil.FormatYMDHMS(self.data.trade_time)

	-- 记录类型
	self.node_list["exchange_type"].text.text = Language.Market.RecordType[self.data.op_type]

	-- 税收
	self.node_list["tax"].text.text = self.data.tax

	-- 收支
	local price 
	local color 
	if self.data.price > 0 then
		price = "+"..self.data.price
		color = COLOR3B.GREEN
	else
		price = self.data.price
		color = COLOR3B.RED
	end
	self.node_list["revenue"].text.text = price
		
	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_data.item_id)
	local auction_price_type = cfg.auction_price_type
	local icon_name = AUCTION_PRICE_TYPE_ICON[auction_price_type]
	if icon_name then
		local bundel, asset = ResPath.GetCommonIcon(icon_name)
		for i = 1, 2 do
			self.node_list["money_type_icon_" .. i].image:LoadSprite(bundel, asset, function ()
				self.node_list["money_type_icon_" .. i].image:SetNativeSize()
			end)
		end
	end
end