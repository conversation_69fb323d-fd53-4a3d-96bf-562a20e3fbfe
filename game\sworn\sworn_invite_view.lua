SwornInviteView = SwornInviteView or BaseClass(SafeBaseView)

function SwornInviteView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(1102, 590)})
	self:AddViewResource(0, "uis/view/sworn_ui_prefab", "sworn_invite_view")
	self:SetMaskBg()
	self.select_index = 0
	self.data_list = {}
end

function SwornInviteView:ReleaseCallBack()
	if self.invite_list then
		self.invite_list:DeleteMe()
		self.invite_list = nil
	end
end

function SwornInviteView:OpenCallBack()
	SocietyWGCtrl.Instance:SendFriendInfoReq()

	if 0 ~= RoleWGData.Instance.role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
	end
end

function SwornInviteView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Sworn.TitleInvite
	self.node_list["layout_blank_tip"]:SetActive(false)

	if not self.invite_list then
		self.invite_list = AsyncListView.New(SwornInviteListItem, self.node_list["ph_invite_list"])
	end

	for i=1,2 do
		self.node_list["ph_btn_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickInviteSelect, self, i))
	end

	self.node_list.btn_invite_all.button:AddClickListener(BindTool.Bind(self.OnClickInviteAll,self))
end

function SwornInviteView:ShowIndexCallBack()
	self.node_list["ph_btn_1"].toggle.isOn = true
	self:OnClickInviteSelect(1)
end

function SwornInviteView:OnClickInviteSelect(index)
	if index == self.select_index then
		 return
	end

	if 1 == index then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif 2 == index then
		if 0 ~= RoleWGData.Instance.role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
		end
    end

	self.select_index = index
	self:Flush()
end

function SwornInviteView:OnClickInviteAll()
	local is_invite = false

	for k,v in pairs(self.data_list) do
		if SwornWGData.Instance:GetCacheCDByRoleid(v.user_id) <= 0 then
			SwornWGData.Instance:AddCacheCDList(v.user_id)
			SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE, v.user_id)
			is_invite = true
		end
	end

	if not is_invite then 
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
	end
end

function SwornInviteView:OnFlush()
    for i = 1, 2 do
		self.node_list["HightLight" .. i]:SetActive(i == self.select_index)
	end

	local level_limit = SwornWGData.Instance:GetSwornLevelLimit()
	local data_list = {}

	if 1 == self.select_index then
		local temp_list = __TableCopy(NewTeamWGData.Instance:GetFriendList())

		for i, v in pairs(temp_list) do
			if v.is_online == 1 and v.level >= level_limit and v.jieyi_id <= 0 then
				table.insert(data_list, v)
			end
		end
	elseif 2 == self.select_index then
		local m_list = GuildDataConst.GUILD_MEMBER_LIST
		local my_uid = RoleWGData.Instance:InCrossGetOriginUid()

		for i = 1, m_list.count do
			local item = m_list.list[i]

			if item.uid ~= my_uid and 1 == item.is_online and item.level >= level_limit and item.jieyi_id <= 0 then
				local datasource = {
					user_id = item.uid,
					gamename = item.role_name, 
					level = item.level, 
					sex = item.sex,
					prof = item.prof, 
					team_index = item.team_index, 
					post = item.post,  
					is_online = item.is_online,
					join_time = item.join_time,
					capability = item.capability, 
					vip_level = item.vip_level, 
					team_type = TEAM_INVITE_TYPE.GUILD, 
					relation_flag = item.relation_flag, 
					fashion_photoframe = item.photframe,
					shield_vip_flag = item.shield_vip_flag}

				table.insert(data_list, datasource)
			end
		end
	end
	
	self.data_list = data_list

	if nil ~= self.data_list and nil ~= self.invite_list then
		self.invite_list:SetDataList(self.data_list)
	end

	if #self.data_list == 0 then
		self.node_list["layout_blank_tip"]:SetActive(true)
		self.node_list["btn_invite_all"]:SetActive(false)
		self.node_list["invite_bg"]:SetActive(false)


		if self.select_index == 1 then
			self.node_list["lbl_tips"].text.text = (Language.Sworn.Friends)
		elseif self.select_index == 2 then
			if 0 ~= RoleWGData.Instance.role_vo.guild_id then
				self.node_list["lbl_tips"].text.text = (Language.Sworn.FairyUnion)
			else
				self.node_list["lbl_tips"].text.text = (Language.Common.PleaseJoinGuild)
			end
		end
	else
		self.node_list["btn_invite_all"]:SetActive(true)
		self.node_list["invite_bg"]:SetActive(true)
		self.node_list["layout_blank_tip"]:SetActive(false)
	end
end

function SwornInviteView:FlushTextInvite()
    if not IsEmptyTable(self.invite_list.cell_list) then
        for k, v in pairs(self.invite_list.cell_list) do
            v:FlushTextInvite()
        end
    end
end

------------------itemRender-----------------
SwornInviteListItem = SwornInviteListItem or BaseClass(BaseRender)

function SwornInviteListItem:__init()
	self.is_myself = false
	XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind1(self.OnClickInvite, self))

	if not self.head_cell then
		self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
	end
end

function SwornInviteListItem:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function SwornInviteListItem:OnFlush()
	if nil == self.data then
		return
	end

	self.node_list.lbl_role_name.text.text = self.data.gamename
	self.node_list.power_right.text.text = self.data.capability

	if self.data.vip_level and self.data.vip_level > 0 then
		local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
		self.node_list["vip_level"].text.text = is_hide_vip and "V" or "V" .. self.data.vip_level
	else
		self.node_list["vip_level"].text.text = ""
	end

	if self.head_cell then
		local data = {}
		data.role_id = self.data.user_id
		data.prof = self.data.prof
		data.sex = self.data.sex
		data.fashion_photoframe = self.data.shizhuang_photoframe or self.data.fashion_photoframe
		self.node_list["power_icon"].image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(data.prof, data.sex)))
		self.head_cell:SetData(data)
	end

	self:FlushTextInvite()
end

function SwornInviteListItem:FlushTextInvite()
    if SwornWGData.Instance:GetCacheCDByRoleid(self.data.user_id) > 0 then
        self.node_list["text_invite"].text.text = SwornWGData.Instance:GetCacheCDByRoleid(self.data.user_id)
        XUI.SetButtonEnabled(self.node_list["btn_invite"], false)
        return
    end

	self.node_list["text_invite"].text.text = Language.Sworn.SwornInviteBtn
    XUI.SetButtonEnabled(self.node_list["btn_invite"], true)
end

function SwornInviteListItem:OnClickInvite()
	if nil == self.data then
		return
	end

	SwornWGData.Instance:AddCacheCDList(self.data.user_id)
	SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE, self.data.user_id)
	-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Sworn.InviteSucessTip)
end
