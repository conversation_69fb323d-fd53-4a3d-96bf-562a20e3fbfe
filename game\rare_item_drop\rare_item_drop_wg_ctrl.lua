require("game/rare_item_drop/rare_item_drop_wg_data")
require("game/rare_item_drop/rare_item_drop_view")
require("game/rare_item_drop/rare_item_drop_test_view")

RareItemDropWGCtrl = RareItemDropWGCtrl or BaseClass(BaseWGCtrl)

function RareItemDropWGCtrl:__init()
	if RareItemDropWGCtrl.Instance then
		error("[RareItemDropWGCtrl]:Attempt to create singleton twice!")
		return
	end
	RareItemDropWGCtrl.Instance = self

	self.data = RareItemDropWGData.New()
	self.view = RareItemDropView.New()
	self.test_view = RareItemDropTestView.New()

	self.pick_drop_item_time = 5 	-- 5s内拾取掉落物后弹珍惜展示
	self.end_show_call_back_list = {}
end

function RareItemDropWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	CountDownManager.Instance:RemoveCountDown("rare_item_count_down")

	RareItemDropWGCtrl.Instance = nil
end

function RareItemDropWGCtrl:CheckPickItem(item_id, item_num, star_level, item_index)
	if not self:CanShowRareItem(true) then
		return
	end

	local item_data = {item_id = item_id, star_level = star_level, rare_item_type = 0, item_index = item_index}
	local is_rare_item,rare_item_type = self:IsRareItem(item_data)
	--[[ 测试打印
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		print_error("珍稀掉落打印:", "item_id:", item_id, item_cfg.name, "星级:", star_level, "是否珍稀:", is_rare_item, "类型:", rare_item_type)
	--]]
	if not is_rare_item then
		return
	end

	item_data.rare_item_type = rare_item_type

	if rare_item_type == RareItemType.Equip then
		self.data:SetBagRareEquipItem(nil, item_data)
	else
		self.data:AddBagRareItem(item_id, item_data, item_num)
	end

	self:CheckPickAllFallItem()
end

-- 检测掉落物
function RareItemDropWGCtrl:CheckPickAllFallItem()
	if not self:CheckFinishPick() and not CountDownManager.Instance:HasCountDown("rare_item_count_down") then
		CountDownManager.Instance:AddCountDown(
				"rare_item_count_down",
				BindTool.Bind(self.CheckFinishPick, self),
				BindTool.Bind(self.ShowRareItemView, self),
				nil,
				self.pick_drop_item_time, -- 限定时间内不管有没有捡完都开始展示
				1
			)
	end
end

-- 每秒检测下捡完了没有
function RareItemDropWGCtrl:CheckFinishPick()
	local is_all_pick = true
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
	if not IsEmptyTable(fall_item_list) then
		for _,v in pairs(fall_item_list) do
			if not v:IsPicked() then
				is_all_pick = false
				break
			end
		end
	end

	if is_all_pick then
		self:ShowRareItemView()
	end

	return is_all_pick
end

-- 是否拾取获得的
function RareItemDropWGCtrl:CheckIsPickItem(item_id)
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
	if not IsEmptyTable(fall_item_list) then
		for _,v in pairs(fall_item_list) do
			if v.vo.item_id == item_id then
				return true
			end
		end
	end
	return false
end

-- 展示珍稀物品掉落
function RareItemDropWGCtrl:ShowRareItemView()
	if self.data:HasRareItem() then
		ReDelayCall(self, function()
			self.view:Open()
		end, 1, "RareItemDropViewOpen")
	else
		RareItemDropWGData.Instance:CleanCahceItemList()
		self:EndShowRareItem()
	end
	CountDownManager.Instance:RemoveCountDown("rare_item_count_down")
end

-- 是否在展示珍稀物品掉落
function RareItemDropWGCtrl:IsShowRareItemView()
	return self.data:HasRareItem() or self.view:IsOpen()
end

-- 能否展示珍稀物品掉落
function RareItemDropWGCtrl:CanShowRareItem(is_pass_fall)
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if scene_cfg and scene_cfg.bestdrop_item_show ~= 1 then
		return false
	end

	local role_level = RoleWGData.Instance:GetAttr("level")
	local min_level,max_level = self.data:GetLimitLevel()
	if role_level < min_level or role_level > max_level then
		return false
	end

	if not is_pass_fall then
		local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
		if IsEmptyTable(fall_item_list) then
			return false
		end
	end

	return true
end

-- 没走掉落直接下发奖励的(特殊处理，不用考虑物品叠加)
function RareItemDropWGCtrl:CheckRewardList(reward_list)
	if not self:CanShowRareItem(true) then
		self:EndShowRareItem()
		return
	end

	local is_rare_item = false
	local rare_item_type = 0
	for k,v in pairs(reward_list) do
		is_rare_item,rare_item_type = self:IsRareItem(v)
		if is_rare_item then
			local temp_data = {item_id = v.item_id, param = v.param, rare_item_type = rare_item_type}
			self.data:SetBagRareItem(k, temp_data)
		end
	end

	self:ShowRareItemView()
end

-- 判断是否珍惜物品
function RareItemDropWGCtrl:IsRareItem(item_data)
	local is_rare_item = false
	local rare_item_type = 0

	is_rare_item,rare_item_type = self:IsBagRareItem(item_data)

	return is_rare_item,rare_item_type
end

-- 判断是否珍稀背包的物品
function RareItemDropWGCtrl:IsBagRareItem(item_data)
	if not item_data or not item_data.item_id then
		return false, 0
	end

	local item_cfg,item_type = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if not item_cfg then
		return false, 0
	end

	-- 材料不显示
	local show_id = ItemWGData.Instance:GetComposeProductid(item_data.item_id)
	if show_id ~= item_data.item_id then
		return false, 0
	end

	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		if  item_cfg.sub_type >= GameEnum.EQUIP_TYPE_JINGLING then
			return false, 0
		end

		local min_color, max_color = self.data:GetLimitColor()
		if item_cfg.color >= min_color and item_cfg.color <= max_color then
			local star_level = item_data.star_level or CheckList(item_data, "param", "star_level")
			local min_star, max_star = self.data:GetLimitStar()
			if star_level and star_level >= min_star and star_level <= max_star then
				return true, RareItemType.Equip
			end
		end
	elseif item_cfg.is_display_role and item_cfg.is_display_role > 0 then
		return true, RareItemType.Fashion
	end

	local special_cfg = self.data:GetSpecialRareItemCfg(item_data.item_id)
	if special_cfg then
		return true, RareItemType.Special
	end

	return false, 0
end

-- 掉落珍稀物品展示完后回调
function RareItemDropWGCtrl:AddEndShowRareItemCallBack(call_back)
	if not call_back then
		return
	end

	if not self:CanShowRareItem() then
		call_back()
		return
	end

	self:AddEndCallBack(call_back)
	self:CheckPickAllFallItem()
end

function RareItemDropWGCtrl:AddEndCallBack(call_back)
	if call_back then
		for _,v in pairs(self.end_show_call_back_list) do
			if v == call_back then
				return
			end
		end
		table.insert(self.end_show_call_back_list, call_back)
	end
end

-- 珍稀物品展示完后回调
function RareItemDropWGCtrl:EndShowRareItem()
	local call_back_list = self.end_show_call_back_list
	for i=1,#call_back_list do
		local func = call_back_list[i]
		func()
	end
	self.end_show_call_back_list = {}
end

---[[ 测试
function RareItemDropWGCtrl:TestRareItemShow()
	self.test_view:Open()
end
--]]