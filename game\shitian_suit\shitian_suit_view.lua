ShiTianSuitView = ShiTianSuitView or BaseClass(SafeBaseView)
function ShiTianSuitView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	self:SetMaskBg(false)

	self:AddViewResource(0, "uis/view/shitian_suit_ui_prefab", "shitian_suit_view")
end

function ShiTianSuitView:LoadCallBack()
    self.node_list.view_name_txt.text.text = Language.ShiTianSuit.ViewName

    if self.st_equip_list == nil then
        self.st_equip_list = {}
        local node_num = self.node_list["st_equip_list"].transform.childCount
        for i = 0, node_num - 1 do
            self.st_equip_list[i] = STEquipRender.New(self.node_list["st_equip_list"]:FindObj("equip_" .. i))
            self.st_equip_list[i]:SetIndex(i)
            self.st_equip_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectSTSlotCallBack, self))
        end
    end

    if self.st_nor_attr_list == nil then
        self.st_nor_attr_list = {}
        local node_num = self.node_list["st_nor_attr_list"].transform.childCount
        for i = 1, node_num do
            self.st_nor_attr_list[i] = CommonAddAttrRender.New(self.node_list["st_nor_attr_list"]:FindObj("attr_" .. i))
        end
    end

    if self.st_xianpin_attr_list == nil then
        self.st_xianpin_attr_list = {}
        local node_num = self.node_list["st_xianpin_attr_list"].transform.childCount
        for i = 1, node_num do
            self.st_xianpin_attr_list[i] = CommonAddAttrRender.New(self.node_list["st_xianpin_attr_list"]:FindObj("attr_" .. i))
        end
    end

    if self.up_cell_pos == nil then
        self.up_cell_pos = ItemCell.New(self.node_list["up_cell_pos"])
    end

    if self.sp_lock_cell_pos_1 == nil then
        self.sp_lock_cell_pos_1 = ItemCell.New(self.node_list["sp_lock_cell_pos_1"])
    end

    if self.lock_item_pos == nil then
        self.lock_item_pos = ItemCell.New(self.node_list["lock_item_pos"])
    end

    if self.sp_lock_cell_pos_2 == nil then
        self.sp_lock_cell_pos_2 = ItemCell.New(self.node_list["sp_lock_cell_pos_2"])
    end

    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_root"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.suit_type_list then
        local res_async_loader = AllocResAsyncLoader(self, "shitiansuit_type_render")
        res_async_loader:Load("uis/view/shitian_suit_ui_prefab", "suit_type_cell", nil,
        function(new_obj)
            if IsNil(new_obj) then
                return
            end

            local show_info = ShiTianSuitWGData.Instance:GetSuitTypeCfg()
            local item_parent = self.node_list.suit_type_list.transform
            local item_list = {}
            for i = 0, #show_info do
                local obj = ResMgr:Instantiate(new_obj)
                obj.transform:SetParent(item_parent, false)
                item_list[i] = STSuitTypeRender.New(obj)
                item_list[i]:SetToggleGroup(self.node_list.suit_type_list.toggle_group)
                item_list[i]:SetData(show_info[i])
                item_list[i]:SetIndex(i)
                item_list[i]:SetClickCallBack(BindTool.Bind(self.OnSuitTypeHandler, self))
            end

            self.suit_type_list = item_list

            if self:IsOpen() then
                self:Flush()
            end
        end)
    end

    self.node_list["btn_right_up"].button:AddClickListener(BindTool.Bind(self.OnClickNorLockUp, self)) --普通解锁/升星
    self.node_list["btn_right_lock"].button:AddClickListener(BindTool.Bind(self.OnClickSpUnLock, self)) --特殊装备解锁
    self.node_list["st_btn_act"].button:AddClickListener(BindTool.Bind(self.OnClickSTact, self))
    self.node_list["suit_look"].button:AddClickListener(BindTool.Bind(self.OpenSuitTipsView, self))
    self.node_list["goto_strengthen_btn"].button:AddClickListener(BindTool.Bind(self.OpenSuitStrengthenView, self))

    self.select_suit_type = nil
    self.select_hole_data = nil
    self.select_hole_index = nil
    self.show_reward_seq = nil
end

function ShiTianSuitView:ReleaseCallBack()

    if self.suit_type_list then
        for k, v in pairs(self.suit_type_list) do
            v:DeleteMe()
        end
		self.suit_type_list = nil
	end

    if self.st_equip_list then
        for k, v in pairs(self.st_equip_list) do
            v:DeleteMe()
        end
        self.st_equip_list = nil
    end

    if self.st_nor_attr_list then
        for k, v in pairs(self.st_nor_attr_list) do
            v:DeleteMe()
        end
        self.st_nor_attr_list = nil
    end

    if self.st_xianpin_attr_list then
        for k, v in pairs(self.st_xianpin_attr_list) do
            v:DeleteMe()
        end
        self.st_xianpin_attr_list = nil
    end

    if self.up_cell_pos then
        self.up_cell_pos:DeleteMe()
        self.up_cell_pos = nil
    end

    if self.sp_lock_cell_pos_1 then
        self.sp_lock_cell_pos_1:DeleteMe()
        self.sp_lock_cell_pos_1 = nil
    end

    if self.lock_item_pos then
        self.lock_item_pos:DeleteMe()
        self.lock_item_pos = nil
    end

    if self.sp_lock_cell_pos_2 then
        self.sp_lock_cell_pos_2:DeleteMe()
        self.sp_lock_cell_pos_2 = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    self.select_suit_type = nil
    self.to_ui_param = nil
    self.sel_index = nil
    self.select_hole_data = nil
    self.select_hole_index = nil
    self.show_reward_seq = nil
end

function ShiTianSuitView:ShowIndexCallBack()
    self:OpenViewAnim()
end

function ShiTianSuitView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
            self.to_ui_param = v.to_ui_param
            self.sel_index = v.sel_index
            self:FlushJumpHoleView()
        end
    end
end

--装备升星/普通装备解锁
function ShiTianSuitView:OnClickNorLockUp()
    if not self.select_hole_index then
        return
    end

    local cell_data = self.st_equip_list[self.select_hole_index]:GetData()
    if not cell_data then
        return 
    end

    local hole_cfg = ShiTianSuitWGData.Instance:GetShiTianHole(cell_data.suit, cell_data.hole)
    if hole_cfg == nil then
        return
    end

    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(cell_data.suit, cell_data.hole)
    local stuff_item_id, stuff_item_num
    if not is_act then
        stuff_item_id = hole_cfg.cost_item_id1
        stuff_item_num = hole_cfg.cost_item_num1
    else
        local star_cfg = ShiTianSuitWGData.Instance:GetEquipStarCfg(cell_data.suit, cell_data.hole, cell_data.star_level)
        if star_cfg then
            stuff_item_id = star_cfg.cost_item_id
            stuff_item_num = star_cfg.cost_item_num
        end   
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_item_id)
    if has_num < stuff_item_num then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_item_id})
        return
    end

    if not is_act and hole_cfg.is_normal_part > 0 then
        ShiTianSuitWGCtrl.Instance:SendShiTianRequest(SHITIAN_SUIT_OPERATE_TYPE.UNLOCK, cell_data.suit, cell_data.hole)
    else
        ShiTianSuitWGCtrl.Instance:SendShiTianRequest(SHITIAN_SUIT_OPERATE_TYPE.STAR_UP, cell_data.suit, cell_data.hole)
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, 
                                pos = Vector2(0, 0), parent_node = self.node_list["effect_star_root"]})
    end
end

--特殊装备解锁
function ShiTianSuitView:OnClickSpUnLock()
    if not self.select_hole_index then
        return
    end

    local cell_data = self.st_equip_list[self.select_hole_index]:GetData()
    if not cell_data then
        return 
    end
    
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(cell_data.suit, cell_data.hole)
    if is_act then
        return
    end

    local is_lock = ShiTianSuitWGData.Instance:GetSpHoleIsLock(cell_data.suit, cell_data.hole)
    if not is_lock then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ShiTianSuit.LockError)
        return
    end

    ShiTianSuitWGCtrl.Instance:SendShiTianRequest(SHITIAN_SUIT_OPERATE_TYPE.UNLOCK, cell_data.suit, cell_data.hole)
end

--激活时装
function ShiTianSuitView:OnClickSTact()
    if not self.select_suit_type then
		return
	end

    local reward_is_act = ShiTianSuitWGData.Instance:GetRewardIsAct(self.select_suit_type)
    if reward_is_act then
        ShiTianSuitWGCtrl.Instance:SendShiTianRequest(SHITIAN_SUIT_OPERATE_TYPE.REWARD, self.select_suit_type)
    end
end

--打开进度tips
function ShiTianSuitView:OpenSuitTipsView()
    if not self.select_suit_type then
		return
	end

    ShiTianSuitWGCtrl.Instance:OpenSuitTips(self.select_suit_type)
end

function ShiTianSuitView:OpenViewAnim()
    local tween_info = UITween_CONSTS.ShiTianSuit
    self.node_list["left_root"].canvas_group.alpha = 0
    RectTransform.SetAnchoredPositionXY(self.node_list["rigtht_ani"].rect, 500, 0)

    self.node_list["left_root"].canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
	self.node_list["rigtht_ani"].rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
end

--打开套装养成view
function ShiTianSuitView:OpenSuitStrengthenView()
    if not self.select_suit_type then
		return
	end

    ShiTianSuitStrengthenWGCtrl.Instance:OpenStrengthenView(self.select_suit_type)
end

function ShiTianSuitView:FlushJumpHoleView()
    local show_info = ShiTianSuitWGData.Instance:GetSuitTypeCfg()
    if IsEmptyTable(show_info) or IsEmptyTable(self.suit_type_list) then
        return
    end

    for i = 0, #show_info do
        self.suit_type_list[i]:SetData(show_info[i])
    end

    if self.to_ui_param then
        local to_index = tonumber(self.to_ui_param)
        local suit_type_cell = self.suit_type_list[to_index]
        if suit_type_cell then
            suit_type_cell:SetToggleIsOn(true)
        end
        self.to_ui_param = nil
    elseif not self.select_suit_type then
        local jump_index = 0
        for i = 0, #show_info do
            local is_red = ShiTianSuitWGData.Instance:GetSuitRemind(show_info[i].suit_seq)
            if is_red then
                jump_index = i
                break
            end
        end



        local suit_type_cell = self.suit_type_list[jump_index]
        suit_type_cell:SetToggleIsOn(true)
    end

    self:FlushLeftView()
    self:FlushRightView()
    self:FlushShiTianModel()
end

function ShiTianSuitView:FlushLeftView(flush_jump)
    if not self.select_suit_type then
		return
	end

    local suit_list = ShiTianSuitWGData.Instance:GetSuitHoleList(self.select_suit_type)
    if not suit_list then
        return
    end

    for k, v in pairs(self.st_equip_list) do
        v:SetData(suit_list[k])
    end

    if flush_jump or self.sel_index ~= nil then
        local click_cell_index = 0
		if self.sel_index then
            click_cell_index = tonumber(self.sel_index)
            self.sel_index = nil
        else
            for i = 0, #suit_list do
                local is_red = ShiTianSuitWGData.Instance:GetHoleUpStarRemind(suit_list[i].suit, suit_list[i].hole)
                if is_red then
                    click_cell_index = i
                    break
                end
            end
        end
        
        click_cell_index = click_cell_index <= #self.st_equip_list and click_cell_index or 0
        self.st_equip_list[click_cell_index]:OnClick()
	end

    local reward_list = ShiTianSuitWGData.Instance:GetSuitRewardList(self.select_suit_type)
    if reward_list then
        local nor_act_num = ShiTianSuitWGData.Instance:GetSuitActNum(self.select_suit_type)
        local suit_name = Language.ShiTianSuit.SuitName[self.select_suit_type]
        self.node_list["reward_act_desc"].text.text = string.format(Language.ShiTianSuit.RewardActDesc, suit_name,
                                            reward_list.image_name, nor_act_num, reward_list.need_normal_part_count)
    end

    local reward_is_act = ShiTianSuitWGData.Instance:GetRewardIsAct(self.select_suit_type)
    self.node_list["btn_act_remind"]:SetActive(reward_is_act)
    XUI.SetButtonEnabled(self.node_list["st_btn_act"], reward_is_act)

    local info = ShiTianSuitWGData.Instance:GetSuitPartInfo(self.select_suit_type)
    local reward_seq = info and info.reward_seq or 0
    local cur_reawrd_cfg = ShiTianSuitWGData.Instance:GetSuitRewradCfg(self.select_suit_type)
    local is_get_all_reward = reward_seq >= #cur_reawrd_cfg
    self.node_list["st_btn_act"]:SetActive(not is_get_all_reward)
    self.node_list["reward_act_desc"]:SetActive(not is_get_all_reward)
    self.node_list["reward_act_des_bg"]:SetActive(not is_get_all_reward)
    self:FlushStrengthenRemind()
end

function ShiTianSuitView:FlushRightView()
    if not self.select_hole_index then
        return
    end

    local cell_data = self.st_equip_list[self.select_hole_index]:GetData()
    if not cell_data then
        return 
    end

    local hole_cfg = ShiTianSuitWGData.Instance:GetShiTianHole(cell_data.suit, cell_data.hole)
    if hole_cfg == nil then
        return
    end

    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(cell_data.suit, cell_data.hole)
    self.node_list["lock_panel"]:SetActive((not is_act) and hole_cfg.is_normal_part < 1)
    self.node_list["up_panel"]:SetActive(is_act or hole_cfg.is_normal_part > 0)
    
    if is_act or hole_cfg.is_normal_part > 0 then
        local nor_attr_list, xp_attr_list = ShiTianSuitWGData.Instance:GetShiTianEquipStarAttrList(cell_data.item_id, cell_data.star_level)
        if nor_attr_list and xp_attr_list then
            for k,v in pairs(self.st_nor_attr_list) do
                v:SetData(nor_attr_list[k])
            end

            for k,v in pairs(self.st_xianpin_attr_list) do
                v:SetData(xp_attr_list[k])
            end
        end

        local is_can_uplevel = false
        local stuff_item_id, stuff_item_num
        if not is_act then
            stuff_item_id = hole_cfg.cost_item_id1
            stuff_item_num = hole_cfg.cost_item_num1
        else
            local star_cfg = ShiTianSuitWGData.Instance:GetEquipStarCfg(cell_data.suit, cell_data.hole, cell_data.star_level)
            if star_cfg then
                stuff_item_id = star_cfg.cost_item_id
                stuff_item_num = star_cfg.cost_item_num
            end
        end

        self.up_cell_pos:SetData({item_id = stuff_item_id})
        local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_item_id)
        is_can_uplevel = has_num >= stuff_item_num
        local color = (not is_can_uplevel) and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
        local str = has_num .. "/" .. stuff_item_num
        self.up_cell_pos:SetRightBottomColorText(str, color)
        self.up_cell_pos:SetRightBottomTextVisible(true)

        self.node_list["btn_right_text"].text.text = is_act and Language.ShiTianSuit.BtnDesc2 or Language.ShiTianSuit.BtnDesc1
        local title_name = string.format("a2_lhtz_epname_%s_%s", cell_data.suit, cell_data.hole)
        local name_bundle, name_asset = ResPath.GetF2RawImagesPNG(title_name)
        if name_bundle and name_asset then
            self.node_list["title_name"].raw_image:LoadSpriteAsync(name_bundle, name_asset, function ()
                self.node_list["title_name"].raw_image:SetNativeSize()
            end)
        end

        self.node_list["btn_right_remind"]:SetActive(is_can_uplevel)
    elseif not is_act and hole_cfg.is_normal_part < 1 then
        local nor_act_num, sp_act_num = ShiTianSuitWGData.Instance:GetSuitActNum(cell_data.suit)
        local sp_act_color = self:ColorToStrbyActNum(sp_act_num, 0)
        local sp_act_str = ToColorStr(sp_act_num, sp_act_color)
        self.node_list["lock_task_desc"].text.text = string.format(Language.ShiTianSuit.LockTaskDesc, sp_act_str)


        local task1_color = self:ColorToStrbyActNum(nor_act_num, hole_cfg.need_normal_part_count)
        local task1_str = ToColorStr(nor_act_num .. "/" .. hole_cfg.need_normal_part_count, task1_color)
        local suit_name = Language.ShiTianSuit.SuitName[cell_data.suit]
        self.node_list["task_desc_1"].text.text = string.format(Language.ShiTianSuit.TaskDesc1, task1_str, suit_name)
        self.node_list.task_state_1.text.text = nor_act_num >= hole_cfg.need_normal_part_count and Language.ShiTianSuit.TaskState1 or Language.ShiTianSuit.TaskState2

        local task2_has_num = ItemWGData.Instance:GetItemNumInBagById(hole_cfg.cost_item_id1)
        local task2_item_name = ""
        local task2_item_cfg = ItemWGData.Instance:GetItemConfig(hole_cfg.cost_item_id1)
        if task2_item_cfg then
            task2_item_name = task2_item_cfg.name
        end
        local task2_color = self:ColorToStrbyActNum(task2_has_num, hole_cfg.cost_item_num1)
        local task2_str = ToColorStr(task2_has_num .. "/" .. hole_cfg.cost_item_num1, task2_color)
        self.node_list["task_desc_2"].text.text = string.format(Language.ShiTianSuit.TaskDesc2, task2_str, task2_item_name)
        self.node_list.task_state_2.text.text = task2_has_num >= hole_cfg.cost_item_num1 and Language.ShiTianSuit.TaskState1 or Language.ShiTianSuit.TaskState2


        local task3_has_num = ItemWGData.Instance:GetItemNumInBagById(hole_cfg.cost_item_id2)
        local task3_item_name = ""
        local task3_item_cfg = ItemWGData.Instance:GetItemConfig(hole_cfg.cost_item_id2)
        if task3_item_cfg then
            task3_item_name = task3_item_cfg.name
        end

        local task3_color = self:ColorToStrbyActNum(task3_has_num, hole_cfg.cost_item_num2)
        local task3_str = ToColorStr(task3_has_num .. "/" .. hole_cfg.cost_item_num2, task3_color)
        self.node_list["task_desc_3"].text.text = string.format(Language.ShiTianSuit.TaskDesc3, task3_str, task3_item_name)
        self.node_list.task_state_3.text.text = task3_has_num >= hole_cfg.cost_item_num2 and Language.ShiTianSuit.TaskState1 or Language.ShiTianSuit.TaskState2


        self.sp_lock_cell_pos_1:SetData({item_id = hole_cfg.cost_item_id1})
        self.sp_lock_cell_pos_1:SetRightBottomColorText(task2_str, task2_color)
        self.sp_lock_cell_pos_1:SetRightBottomTextVisible(true)

        self.sp_lock_cell_pos_2:SetData({item_id = hole_cfg.cost_item_id2})
        self.sp_lock_cell_pos_2:SetRightBottomColorText(task3_str, task3_color)
        self.sp_lock_cell_pos_2:SetRightBottomTextVisible(true)

        local lock_item_name = ""
        local lock_item_cfg = ItemWGData.Instance:GetItemConfig(hole_cfg.icon_id)
        if lock_item_cfg then
            lock_item_name = lock_item_cfg.name
        end

        self.lock_item_pos:SetData({item_id = hole_cfg.icon_id})
        self.node_list["lock_item_name"].text.text = lock_item_name

        local is_lock = ShiTianSuitWGData.Instance:GetSpHoleIsLock(cell_data.suit, cell_data.hole)
        self.node_list["right_lock_remind"]:SetActive(is_lock)
    end

    local max_star = ShiTianSuitWGData.Instance:GetMaxStar(cell_data.suit, cell_data.hole)
    local is_max = cell_data.star_level >= max_star
    self.node_list["is_max"]:SetActive(is_max)
    self.node_list["btn_right_up"]:SetActive(not is_max)
end

function ShiTianSuitView:FlushShiTianModel()
    if not self.select_suit_type then
		return
	end

    local info = ShiTianSuitWGData.Instance:GetSuitPartInfo(self.select_suit_type)
    if info and self.show_reward_seq == info.reward_seq then
        return
    end

    self.show_reward_seq = info.reward_seq
    local model_item_list = ShiTianSuitWGData.Instance:GetRewardModelItemList(self.select_suit_type)
    local is_get_seq = info and info.reward_seq or 0
    self.node_list["model_root"].transform.rotation = Quaternion.Euler(0, 0, 0)
    if model_item_list then
        local display_data = {}
        display_data.model_item_id_list = model_item_list
        display_data.render_type = 0

        if is_get_seq >= 2 then
            display_data.position = Vector3(0, -800, 0)
            self.node_list["model_root"].transform.rotation = Quaternion.Euler(0, -60, 0)
        else
            display_data.position = Vector3(0, 0, 0)
        end

        display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]
        self.model_display:SetData(display_data)
    end

    local model_scale = is_get_seq >= 2 and 0.25 or 1
    RectTransform.SetLocalScale(self.node_list["model_root"].transform, model_scale)
end

function ShiTianSuitView:ColorToStrbyActNum(my_num, cfg_num)
    if my_num == nil or cfg_num == nil then
        return COLOR3B.D_RED
    end

    local color
    if cfg_num == 0 then
        color = my_num > cfg_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    else
        color = my_num >= cfg_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    end

    return color
end

function ShiTianSuitView:OnSuitTypeHandler(item)
	if nil == item or nil == item.data then
		return
	end

	local cell_index = item:GetIndex()
    if self.select_suit_type and self.select_suit_type == cell_index then
		return
	end

	self.select_suit_type = cell_index
    self.show_reward_seq = nil
    self:FlushLeftView(true)
    self:FlushShiTianModel()
end

function ShiTianSuitView:OnSelectSTSlotCallBack(cell)
    if nil == cell or nil == cell.data then
		return
	end

    local data = cell:GetData()
    if self.select_hole_index == data.hole and self.select_hole_data == data then
        local is_act = self.select_hole_data.star_level > 0
        TipWGCtrl.Instance:OpenItem({item_id = self.select_hole_data.item_id}, is_act and ItemTip.FROM_SHITIAN_EQUIP or ItemTip.FROM_NORMAL)
        return
    end

    self.select_hole_data = data
    self.select_hole_index = data.hole
    for k, v in pairs(self.st_equip_list) do
        v:FlushSelectHL(k == data.hole)
    end

    self:FlushRightView()
end

function ShiTianSuitView:FlushStrengthenRemind(is_update)
    if not self.select_suit_type or not self.node_list then
        return
    end

    local all_strengthen_remind = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitRemind(self.select_suit_type)
    self.node_list.strengthen_btn_remind:SetActive(all_strengthen_remind)

    if not is_update then
        return
    end

    if self.suit_type_list then
        for k, v in pairs(self.suit_type_list) do
            v:FlushRemind()
        end
    end
end

-----------------------------STSuitTypeRender-------
STSuitTypeRender = STSuitTypeRender or BaseClass(BaseRender)

function STSuitTypeRender:OnFlush()
	if not self.data then
		return
	end

    self.node_list.nor_name_text.text.text = self.data.description_top
	self.node_list.hl_name_text.text.text = self.data.description_top
    self:FlushRemind()
end

function STSuitTypeRender:FlushRemind()
    if not self.data then
        return
    end

	local remind = ShiTianSuitWGData.Instance:GetSuitRemind(self.data.suit_seq)
    local all_strengthen_remind = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitRemind(self.data.suit_seq)
	self.node_list.remind:SetActive(remind or all_strengthen_remind)
end

function STSuitTypeRender:SetClickCallBack(callback)
	self.click_callback = callback
	if self.need_click_listen ~= false and self.view then
		self:AddClickEventListener(self.click_callback, true)
	end
end

function STSuitTypeRender:SetToggleIsOn(isOn)
	if self.view.toggle then
		self.view.toggle.isOn = isOn
	end
end

-----------------------------STEquipRender-------
STEquipRender = STEquipRender or BaseClass(BaseRender)
function STEquipRender:__init()
    self.equip_cell = ItemCell.New(self.node_list["cell_pos"])
	XUI.AddClickEventListener(self.node_list["btn_cell_click"], BindTool.Bind(BindTool.Bind(self.OnClick, self)))
end

function STEquipRender:__delete()
    if self.equip_cell then
        self.equip_cell:DeleteMe()
        self.equip_cell = nil
    end

    self.item_click_callback = nil
end

function STEquipRender:SetCellClickCallBack(call_back)
    self.item_click_callback = call_back
end

function STEquipRender:OnClick()
    if self.item_click_callback then
        self.item_click_callback(self)
    end
end


function STEquipRender:OnFlush()
    if self.data == nil then
        return
    end

    local cfg = self.data
    local hole_cfg = ShiTianSuitWGData.Instance:GetShiTianHole(cfg.suit, cfg.hole)
    self.equip_cell:SetData({item_id = cfg.item_id})
    self.equip_cell:SetLeftTopImg(cfg.star_level)

    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(cfg.suit, cfg.hole)

    if not is_act then
        self.equip_cell:SetGraphicGreyCualityBg(true)
        self.equip_cell:SetDefaultEff(false)
    else
        self.equip_cell:SetGraphicGreyCualityBg(false)
    end

    if hole_cfg and hole_cfg.is_normal_part < 1 then
        self.node_list["is_lock"]:SetActive(not is_act)
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id)
    local item_name = ""
    if item_cfg then
        item_name = item_cfg.name
    end
    self.node_list["get_way_str"].text.text = item_name

    local is_can_upstar = ShiTianSuitWGData.Instance:GetHoleUpStarRemind(cfg.suit, cfg.hole)
    self.node_list["remind"]:SetActive(is_can_upstar)
end

function STEquipRender:FlushSelectHL(is_select)
    self.node_list["select_hl"]:SetActive(is_select)
end