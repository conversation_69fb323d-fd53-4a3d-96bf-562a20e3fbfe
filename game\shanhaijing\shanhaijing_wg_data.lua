ShanHaiJingWGData = ShanHaiJingWGData or BaseClass()

function ShanHaiJingWGData:__init()
	if ShanHaiJingWGData.Instance then
		error("[ShanHaiJingWGData] Attempt to create singleton twice!")
		return
	end
	ShanHaiJingWGData.Instance = self
	self.all_card_auto = ConfigManager.Instance:GetAutoConfig("bosscardcfg_auto")
	-- 宠物配置
	self.pet_card_cfg = ListToMapList(self.all_card_auto.petcard, "series")
	-- 天神配置
	self.tianshen_card_cfg = ListToMapList(self.all_card_auto.tianshencard, "series")
	--天机谱配置
	self.tianjipu_card_cfg = ListToMapList(self.all_card_auto.tianjipu_card, "chapter_id","shenqi_id")
	-- 属性显示配置
	self.sl_card_attr_cfg = ListToMap(self.all_card_auto.card_show_attr_cfg, "card_type")

	-- 图鉴配置
	self.tujian_auto = ConfigManager.Instance:GetAutoConfig("tujian_auto")
	-- 图鉴显示配置
	self.active_tujian_auto = ListToMapList(self.tujian_auto.active, "card_type")
	-- 图鉴分解显示配置
	self.resolve_tujian_auto = ListToMap(self.tujian_auto.active, "active_item_id")
	-- 图鉴子类型显示配置
	self.active_child_tujian_auto = ListToMapList(self.tujian_auto.active, "card_type", "card_child_type")
	-- 图鉴升级配置
	self.uplevel_tujian_auto = ListToMap(self.tujian_auto.uplevel, "seq", "level")
	-- 图鉴升级配置
	self.uplevel_tujian_cfg = self.tujian_auto.uplevel
	-- 图鉴升星配置
	self.upstar_tujian_auto = ListToMap(self.tujian_auto.upstar, "seq", "star")
	-- 图鉴羁绊配置
	self.group_tujian_auto = self.tujian_auto.group
	self.group_tujian_auto_group_type = ListToMapList(self.tujian_auto.group, "card_type", "group_type")
	self.group_tujian_auto_card_type = ListToMapList(self.tujian_auto.group, "card_type")
	-- 图鉴技能配置
	self.skill_tujian_auto = ListToMapList(self.tujian_auto.skill, "tujian_seq")
	-- 图鉴组合显示配置
	self.single_byseq_tujian_auto = ListToMap(self.tujian_auto.active, "seq")

	-- 图鉴升阶配置
	self.up_grade_cfg = ListToMap(self.tujian_auto.up_grade, "seq")
    -- 图鉴升阶百分比衰减数据
    self.fix_fail_count_cfg = ListToMapList(self.tujian_auto.fix_fail_count, "fix_fail_index")
    self.per_fail_count_cfg = ListToMapList(self.tujian_auto.per_fail_count, "per_fail_index")
    self.up_grade_consume = ListToMapList(self.tujian_auto.up_grade_consume, "upgrade_seq")
	-- 配置缓存
    self.fix_fail_count_cache = self:CalAttrCfgPack(self.fix_fail_count_cfg, "attr_per")
    self.per_fail_count_cache = self:CalAttrCfgPack(self.per_fail_count_cfg, "attr_per")
    self.up_grade_consume_cache = self:CalAttrCfgPack(self.up_grade_consume, "consume_num")

	self.tujian_server_info = {}
	self.use_level_attr_key = {}
	self.use_star_attr_key = {}
	self.use_base_attr_key = {}
	self.use_group_attr_key = {}
	local level_attr_cfg = self.uplevel_tujian_auto[0][0]
	local star_attr_cfg = self.upstar_tujian_auto[0][0]
	local base_attr_cfg = self.single_byseq_tujian_auto[0]
	local group_attr_cfg = self.group_tujian_auto[1]
	local attr_key_data = AttributePool.AllocAttribute()
	self.use_level_attr_key = AttributeMgr.GetUsefulAttributteByClass(level_attr_cfg)
	self.use_star_attr_key = AttributeMgr.GetUsefulAttributteByClass(star_attr_cfg)
	self.use_base_attr_key = AttributeMgr.GetUsefulAttributteByClass(base_attr_cfg)
	self.use_group_attr_key = AttributeMgr.GetUsefulAttributteByClass(group_attr_cfg)

	local remindmanager_instance = RemindManager.Instance
	--remindmanager_instance:Register(RemindName.ShanHaiJing_SL_CW, BindTool.Bind(self.GetSLRemind,self,SHJ_SL_TYPE.CW))
	--remindmanager_instance:Register(RemindName.ShanHaiJing_SL_TS, BindTool.Bind(self.GetSLRemind,self,SHJ_SL_TYPE.TS))
	remindmanager_instance:Register(RemindName.ShanHaiJing_TJ, BindTool.Bind(self.GetTJAllCardRemind,self,SHJ_SL_TYPE.TS))
	self.show_tips = true
	self.select_stone_flag = false
	self:RegisterRemindInBag(RemindName.ShanHaiJing_TJ)
end

function ShanHaiJingWGData:__delete()
	ShanHaiJingWGData.Instance = nil
	local remindmanager_instance = RemindManager.Instance
	--[[remindmanager_instance:UnRegister(RemindName.ShanHaiJing_SL_CW)
	remindmanager_instance:UnRegister(RemindName.ShanHaiJing_SL_TS)--]]
	remindmanager_instance:UnRegister(RemindName.ShanHaiJing_TJ)
end

-- 注册背包红点通知
function ShanHaiJingWGData:RegisterRemindInBag(remind_name)
	local map = {}
	local pairs = pairs
	for k,v in pairs(self.tujian_auto.active) do
		if v["active_item_id"] > 0 then
			map[v["active_item_id"]] = true
		end
	end

	for k,v in pairs(self.tujian_auto.uplevel) do
		if v["consume_item_id"] > 0 then
			map[v["consume_item_id"]] = true
		end
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

-- 获取收录配置
function ShanHaiJingWGData:GetSLCfgByType(card_type)
	if card_type == SHJ_SL_TYPE.CW then
		return self.pet_card_cfg
	elseif card_type == SHJ_SL_TYPE.TS then
		return self.tianshen_card_cfg
	end
end

---------------------------收录----------------------------------
-- 获取收录属性显示配置
function ShanHaiJingWGData:GetSLAttrCfgByType(card_type)
	return self.sl_card_attr_cfg[card_type]
end

-- 设置收录图鉴数据
function ShanHaiJingWGData:SetSLServerData(protocol)
	if not self.sl_server_data then
		self.sl_server_data = {}
	end
	local card_can_active_flag = {}
	local bit_list = {}

 	for i,v in ipairs(protocol.card_can_active_flag) do
 		bit_list = bit:d2b(v, bit_list)
 		for ii=25,32,1 do
 			table.insert(card_can_active_flag,bit_list[ii])
 		end
 	end

	local card_has_active_flag = {}
 	for i,v in ipairs(protocol.card_has_active_flag) do
 		bit_list = bit:d2b(v, bit_list)
 		for ii=25,32,1 do
 			table.insert(card_has_active_flag,bit_list[ii])
 		end
 	end

	self.sl_server_data[protocol.card_type] =
	{card_can_active_flag = card_can_active_flag,
		card_has_active_flag = card_has_active_flag}

end

-- 获取收录图鉴数据
function ShanHaiJingWGData:GetSLServerData(card_type)
	return self.sl_server_data and self.sl_server_data[card_type] or nil
end

function ShanHaiJingWGData:GetTJConsume()
	return self.uplevel_tujian_cfg[1].consume_item_id
end

-- 获取收录总属性
function ShanHaiJingWGData:GetSLViewInfo(card_type)
	-- 图鉴服务器数据
	local card_server_list = self:GetSLServerData(card_type)
	if not card_server_list then return end
	-- 图鉴激活标识
	local card_has_active_list = card_server_list.card_has_active_flag
	-- 该类型所有图鉴配置
	local card_cfg_list = self:GetSLCfgByType(card_type)
	local capability = 0
	local attr_list = AttributePool.AllocAttribute()

	local total_num = 0
	local active_num = 0

	for i,v in ipairs(card_cfg_list) do
		for i1,v1 in ipairs(v) do
			if card_has_active_list[v1.card_seq+1] == 1 then
				active_num = active_num + 1
				-- 计算属性
				local temp_attr_list = AttributeMgr.GetAttributteByClass(v1)
				attr_list = AttributeMgr.AddAttributeAttr(temp_attr_list,attr_list)
			end
			total_num = total_num + 1
		end
	end
	capability = AttributeMgr.GetCapability(attr_list)

	local view_info = {
		capability = capability,
		attr_list = attr_list,
		total_num = total_num,
		active_num = active_num,
	}

	return view_info
end

-- 获取收录单个item显示数据
function ShanHaiJingWGData:GetSLItemViewInfo(card_type,card_seq,data)
    -- 图鉴服务器数据
	local card_server_list = self:GetSLServerData(card_type)
	if not card_server_list then return end
	-- 图鉴激活标识
	local card_has_active_list = card_server_list.card_has_active_flag
	-- 图鉴可激活标识
	local card_can_active_list = card_server_list.card_can_active_flag
	local view_info = {}
	view_info.remind = card_can_active_list[card_seq+1] == 1 and card_has_active_list[card_seq+1] == 0
	view_info.has_active = card_has_active_list[card_seq+1] == 1

	local attr_name = Language.Common.TipsAttrNameList
	local attr_list = AttributeMgr.GetAttributteByClass(data)
	local sort_list = AttributeMgr.SortAttribute()

	local attr_txt_1 = ""
	local attr_txt_2 = ""
	local index = 1
	for k,v in ipairs(sort_list) do
		if attr_list[v] ~= 0 then
			if index < 3 then
				attr_txt_1 = attr_txt_1 .. attr_name[v] .. "+" .. attr_list[v] .. "\n"
			else
				attr_txt_2 = attr_txt_2 .. attr_name[v] .. "+" .. attr_list[v] .. "\n"
			end
			index = index + 1
		end
	end
	view_info.attr_txt_1 = attr_txt_1
	view_info.attr_txt_2 = attr_txt_2

	return view_info
end

-- 收录红点
function ShanHaiJingWGData:GetSLRemind(card_type,only_flag)

	local card_cfg_list = card_type == SHJ_SL_TYPE.CW and self.all_card_auto.petcard or self.all_card_auto.tianshencard
	local view_info
	for k,v in pairs(card_cfg_list) do
		view_info = self:GetSLItemViewInfo(card_type,v.card_seq,v)
		if view_info.remind then
			if not only_flag then
				local tab_name = "shj_tianshen"
				if card_type == SHJ_SL_TYPE.CW then
					tab_name = "shj_chongwu"
				end
				local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(tab_name)
				if is_open then
					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TUJIAN_SL,1,function ()
						local tab_index = card_type == SHJ_SL_TYPE.CW and TabIndex.shj_chongwu or TabIndex.shj_tianshen
						FunOpen.Instance:OpenViewByName(GuideModuleName.ShanHaiJingView,tab_index)
						return true
					end)
				else
					return 0
				end
			end
			return 1
		end
	end
	if not only_flag then
		local other_flag = self:GetSLRemind(card_type == SHJ_SL_TYPE.CW and SHJ_SL_TYPE.TS or SHJ_SL_TYPE.CW,true)
		if other_flag == 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TUJIAN_SL,0)
		end
	end
	return 0
end

--------------------------图鉴----------------------------------
--获取图鉴大类型
function  ShanHaiJingWGData:GetTJCardTypecfg()
	return self.active_tujian_auto
end

-- 获取图鉴技能配置
function ShanHaiJingWGData:GetTJSkillCfg(seq)
	return self.skill_tujian_auto[seq]
end

-- 获取图鉴升星配置（沒有配置代表这个图鉴不能升星）
function ShanHaiJingWGData:GetTJUpStarCfg(seq,star)
	return self.upstar_tujian_auto[seq] and self.upstar_tujian_auto[seq][star] or nil
end

-- 获取图鉴升级配置
function ShanHaiJingWGData:GetTJUpLevelCfg(seq,level)
	return self.uplevel_tujian_auto[seq] and self.uplevel_tujian_auto[seq][level] or nil
end

-- 获取图鉴显示配置
function ShanHaiJingWGData:GetTJToggleListInfo(card_type)
	local data_list = {}
	if not card_type then return data_list end
	for i,v in ipairs(self.active_tujian_auto[card_type]) do
		if nil == data_list[v.card_child_type] and self:IsCardChildTypeHasData(card_type, v.card_child_type) then
			data_list[v.card_child_type] = v
		end
	end

	local list = {}
	for k,v in pairs(data_list) do
		table.insert(list, v)
	end
	return list
end

-- 获取图鉴分解显示配置
function ShanHaiJingWGData:GetTJResolveShowCfg(item_id)
	if item_id == nil then
		return
	end
	return self.resolve_tujian_auto[item_id]
end

-- 获取图鉴核心
function ShanHaiJingWGData:GetTJKernel(item_id)
	local stuff_item_cfg = self:GetTJResolveShowCfg(item_id)
	if stuff_item_cfg then
		local item_cfg = ItemWGData.Instance:GetItemConfig(stuff_item_cfg.active_item_id)
		return stuff_item_cfg.is_kernel == 1 and item_cfg.color or 0
	end
	return 0
end

function ShanHaiJingWGData:IsCardChildTypeHasData(card_type, card_child_type)
	if card_type and card_child_type then
		return #self:GetTJChildItemListInfo(card_type, card_child_type) > 0
	else
		return false
	end
end

-- 获取图鉴类型显示配置
function ShanHaiJingWGData:GetTJItemListInfo(card_type)
	local tujian_cfg = {}
	local tujian_cfg2 = {}
	local tujian_info = self.active_tujian_auto[card_type]
	local role_vo = RoleWGData.Instance.role_vo
	if not IsEmptyTable(tujian_info) then
		for k,v in ipairs(tujian_info) do
			if v.is_show == 1 then
				if role_vo.level >= v.show_level then
					--[[local tujian_cfg1 = {}
					tujian_cfg1.cfg = v
					tujian_cfg1.index = v.color*100 + v.seq--]]
					table.insert(tujian_cfg, v)
				end
			end
		end
		SortTools.SortAsc(tujian_cfg, "order")
	end

	--[[for k,v in ipairs(tujian_cfg) do
		table.insert(tujian_cfg2, v.cfg)
	end--]]
	return tujian_cfg or {}
end

-- 获取图鉴子类型显示配置
function ShanHaiJingWGData:GetTJChildItemListInfo(card_type,card_child_type)
	local tujian_cfg = {}
	local role_vo = RoleWGData.Instance.role_vo
	local data_list = self.active_child_tujian_auto[card_type] and self.active_child_tujian_auto[card_type][card_child_type] or {}
	if not IsEmptyTable(data_list) then
		for k,v in ipairs(data_list) do
	 		if v.is_show == 1 then
	 			if role_vo.level >= v.show_level then
	 				table.insert(tujian_cfg, v)
				end
			end
		end
		SortTools.SortAsc(tujian_cfg, "order")
	end
	return tujian_cfg
end

-- 设置所有图鉴服务器数据
function ShanHaiJingWGData:SetTJAllServerInfo(protocol)
	self.group_active_flag = {}
	local bit_list = {}
 	for i,v in ipairs(protocol.group_active_flag) do
 		bit_list = bit:d2b(v, bit_list)
 		for ii=32,25,-1 do
 			table.insert(self.group_active_flag, bit_list[ii])
 		end
 	end

 	self.exp = protocol.exp
	self.tujian_server_info = protocol.tujian_list
end

-- 获取所有图鉴服务器数据
function ShanHaiJingWGData:GetTJAllServerInfo()
	return self.tujian_server_info
end

function ShanHaiJingWGData:GetTJIsActBySeq(seq)
	local info = self:GetTJAllServerInfo()
	if info[seq] and info[seq].level > 0 then
		return true
	end

	return false
end

-- 设置单个图鉴服务器数据
function ShanHaiJingWGData:SetTJSingleServerInfo(protocol)
	local tujian_server_info = self:GetTJAllServerInfo()
	tujian_server_info[protocol.tujian_seq] = protocol.tujian_item --__TableCopy(protocol.tujian_item, 0)
end

-- 设置外部跳转参数
function ShanHaiJingWGData:SetJumpTuJianInfo(item_id)
	local tujiancfg = self:GetTJResolveShowCfg(item_id)
	if tujiancfg then
		self.jump_big_page = tujiancfg.card_type
		self.jump_small_page = tujiancfg.card_child_type
		self.jump_item_id = item_id
	end

	-- if ViewManager.Instance:IsOpenByIndex(GuideModuleName.ShanHaiJingView, TabIndex.shj_tujian) then
	-- 		ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView,TabIndex.shj_tujian,
 --   				nil,{flush_jump_tujian = true})
	-- 	return
	-- end
end

function ShanHaiJingWGData:GetJumpTuJianInfo()
	local value1,value2,value3 = self.jump_big_page, self.jump_small_page, self.jump_item_id
	self.jump_big_page,self.jump_small_page,self.jump_item_id = nil,nil,nil
	return value1,value2,value3
end

--------------------------------------
-- 图鉴新增升阶

-- 图鉴升阶固定属性衰减配置
function ShanHaiJingWGData:GetTJUpGradeFixFailCfg(index, grade)
    return (self.fix_fail_count_cache[index] or {})[grade]
end

-- 图鉴升阶百分比属性衰减配置
function ShanHaiJingWGData:GetTJUpGradePerFailCfg(index, grade)
    return (self.per_fail_count_cache[index] or {})[grade]
end

-- 获取图鉴升阶消耗
function ShanHaiJingWGData:GetTJUpGradeConsume(index, grade)
    return (self.up_grade_consume_cache[index] or {})[grade]
end

-- 获取图鉴升阶单条配置
function ShanHaiJingWGData:GetTJUpGradeCfg(seq)
	return self.up_grade_cfg[seq]
end

-- 图鉴阶级属性计算
function ShanHaiJingWGData:GetTJUpGradeAttr(seq, grade)
	local upgrade_cfg = self:GetTJUpGradeCfg(seq)
	local active_cfg = self:GetTJCfgBySeq(seq)
	local attr_data = {}
	if grade > upgrade_cfg.max_grade_level then
		return attr_data
	end
	local base_attr_list = AttributeMgr.GetAttributteByClass(active_cfg) --基础属性读激活配置
	for i = 1, grade do
		local fix_fail_value = self:GetTJUpGradeFixFailCfg(upgrade_cfg.fix_fail_index, i) --固定值属性衰减万分比
		local per_fail_value = self:GetTJUpGradePerFailCfg(upgrade_cfg.per_fail_index, i) --百分比属性衰减万分比
		for k, v in pairs(base_attr_list) do
			if v > 0 then
				local target_value = 0
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
				if is_per then
					target_value = math.floor(v * per_fail_value / 10000)
				else
					target_value = math.floor(v * fix_fail_value / 10000)
				end
				if not attr_data[k] then
					attr_data[k] = 0
				end
				attr_data[k] = attr_data[k] + target_value
			end
		end
	end

	return attr_data
end

-- 获取升阶属性信息
function ShanHaiJingWGData:GetTJUpGradeAttrInfo(card_data)
	local server_info = self:GetTJAllServerInfo()
	server_info = server_info[card_data.seq]
	-- 获取阶级属性
	local upgrade_cfg = self:GetTJUpGradeCfg(card_data.seq)
	local is_max_grade = server_info.grade_level >= upgrade_cfg.max_grade_level
	local attr_list = self:GetTJUpGradeAttr(card_data.seq, server_info.grade_level)
	local add_grade_attr_list
	if not is_max_grade then
		local next_grade_attr_list = self:GetTJUpGradeAttr(card_data.seq, server_info.grade_level + 1)
		add_grade_attr_list = AttributeMgr.LerpAttributeAttr(attr_list, next_grade_attr_list)
	end
	-- 合并升星属性
	local upstar_info_list = self:GetTJUpStarAttrInfo(card_data, nil, false)
	attr_list = AttributeMgr.AddAttributeAttr(attr_list, upstar_info_list.attr_list)
	
	local grade_capability = AttributeMgr.GetCapability(uplevel_attr_list)
	local consume_num = self:GetTJUpGradeConsume(upgrade_cfg.upgrade_seq, server_info.grade_level)
	local info_list = {
		is_max_grade = server_info.grade_level >= upgrade_cfg.max_grade_level,
		capability = upstar_info_list.capability + grade_capability,
		attr_list = attr_list,
		add_attr_list = add_grade_attr_list,
		consume_item_id = upgrade_cfg.consume_item_id,
		consume_num = consume_num,
	}
	return info_list
end

-- 获取该图鉴升星属性
function ShanHaiJingWGData:GetTJUpStarAttrInfo(card_data, server_info, had_up_grade)
	local is_present = false
	local attr_list = AttributePool.AllocAttribute()
	local attr_list1 = AttributePool.AllocAttribute()
	local capability = 0
	if not server_info then
		is_present = true
		server_info = self:GetTJAllServerInfo()
		server_info = server_info[card_data.seq]
	end

	if not server_info then return { attr_list = attr_list, capability = capability, add_attr_per = 0 } end
	local star_cfg = self:GetTJUpStarCfg(card_data.seq, server_info.star)
	if not star_cfg then return { attr_list = attr_list, capability = capability, add_attr_per = 0 } end

	local add_attr_per = star_cfg.add_attr_per / 10000
	attr_list = AttributeMgr.GetAttributteByClass(star_cfg)

	if not is_present then
		local star_cfg1 = self:GetTJUpStarCfg(card_data.seq, server_info.star - 1)
		if not star_cfg1 then
			return { attr_list = attr_list ,capability = capability, add_attr_per = 0 }
		end
		attr_list1 = AttributeMgr.GetAttributteByClass(star_cfg1)
		attr_list = AttributeMgr.LerpAttributeAttr(attr_list1, attr_list)
	end
	-- 天道石影响属性百分比,不影响百分比 升星属性
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.TUJIAN)
	charm_rate = charm_rate / 10000
	---
	-- 策划需求真怪 有个影响百分比的有个不影响百分比的
	attr_list = AttributeMgr.MulAttributeTwo(attr_list, add_attr_per, charm_rate)
	if is_present then
		local info_list = self:GetTJActiveAttrInfo(card_data, server_info)
		attr_list = AttributeMgr.AddAttributeAttr(attr_list, info_list.attr_list)
		if had_up_grade then -- 合并升阶属性
			local grade_attr_list = self:GetTJUpGradeAttr(card_data.seq, server_info.grade_level)
			attr_list = AttributeMgr.AddAttributeAttr(attr_list, grade_attr_list)
		end
	end

	capability = AttributeMgr.GetCapability(attr_list)
	return {attr_list = attr_list,
		capability = capability,
		add_attr_per = add_attr_per,
		is_max_star = self.upstar_tujian_auto[card_data.seq] and #self.upstar_tujian_auto[card_data.seq] == server_info.star
	}
end

-- 获取该图鉴激活属性
function ShanHaiJingWGData:GetTJActiveAttrInfo(card_data, server_info)
	local attr_list = AttributePool.AllocAttribute()
	local capability = 0
	if not server_info then
		server_info = self:GetTJAllServerInfo()
		server_info = server_info[card_data.seq]
	end
	if not server_info then
		return {attr_list = attr_list, capability = capability, add_attr_per = 0}
	end

	local base_cfg = self.single_byseq_tujian_auto[card_data.seq]
	local star_cfg = self:GetTJUpStarCfg(card_data.seq, server_info.star)
	local add_attr_per = star_cfg.add_attr_per/10000

	-- 天道石影响属性百分比,不影响百分比 激活属性
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.TUJIAN)
	charm_rate = charm_rate / 10000
	---
	attr_list = AttributeMgr.GetAttributteByClass(base_cfg)
	attr_list = AttributeMgr.MulAttributeTwo(attr_list, add_attr_per, charm_rate)
	capability = AttributeMgr.GetCapability(attr_list)
	return {attr_list = attr_list,
		capability = capability,
	}
end

function ShanHaiJingWGData:GetTotalTJCap()
	local base_attr, star_attr, group_attr, upgrade_attr = self:GetAttrInfoShow()
	local capability = AttributeMgr.GetCapability(base_attr)
	capability = capability + AttributeMgr.GetCapability(star_attr)
	capability = capability + AttributeMgr.GetCapability(group_attr)
	capability = capability + AttributeMgr.GetCapability(upgrade_attr)
	return capability
end

-- 获取该图鉴升级属性[弃用]
function ShanHaiJingWGData:GetTJUpLevelAttrInfo(card_data,server_info)
	-- 升星属性信息
	local info_list = self:GetTJUpStarAttrInfo(card_data,server_info)
	-- 基础信息
	local base_attr_list = AttributeMgr.GetAttributteByClass(card_data)
	-- 升级
	if not server_info then
		server_info = self:GetTJAllServerInfo()
		server_info = server_info[card_data.seq]
	end

	local tj_level = server_info and server_info.level or 0
	local uplevel_cfg = self:GetTJUpLevelCfg(card_data.seq, tj_level)
	local uplevel_attr_list = AttributeMgr.GetAttributteByClass(uplevel_cfg)
	-- 是否最大等级
	info_list.is_max_level = #self.uplevel_tujian_auto[card_data.seq] == tj_level

	uplevel_attr_list = AttributeMgr.AddAttributeAttr(base_attr_list,uplevel_attr_list)
	uplevel_attr_list = AttributeMgr.MulAttribute(uplevel_attr_list,info_list.add_attr_per + 1)
	local capability = AttributeMgr.GetCapability(uplevel_attr_list)

	info_list.capability = info_list.capability + capability
	info_list.attr_list = uplevel_attr_list  -- AttributeMgr.AddAttributeAttr(info_list.attr_list,uplevel_attr_list)

	return info_list
end

--获取该图鉴技能信息
function ShanHaiJingWGData:GetTJSkillInfo(card_data)
	local skill_cfg = self:GetTJSkillCfg(card_data.seq)
	local skill_info_list = {}
	if not skill_cfg then return skill_info_list end

	local server_info = self:GetTJAllServerInfo()
	server_info = server_info[card_data.seq]

	if not server_info then
		return skill_info_list
	end

	for i,v in ipairs(skill_cfg) do
		skill_info_list[i] = {}
		skill_info_list[i].skill_cfg = v
		skill_info_list[i].has_active = server_info.star >= v.active_level
	end

	return skill_info_list
end

-- 获取该图鉴操作物品信息
function ShanHaiJingWGData:GetTJOperaInfo(card_data, is_sx)
	local server_info = self:GetTJAllServerInfo()
	if IsEmptyTable(server_info) then return {} end
	server_info = server_info[card_data.seq]
	local has_active = server_info.level > 0
	local opera_info = {}
	local has_num
	local need_num
	local need_exp_num = 0
	if not has_active then
		has_num = self:GetItemNumInBagById(card_data.active_item_id)
		need_num = 1
		need_exp_num = 0
		opera_info.item_id = card_data.active_item_id
		opera_info.remind = has_num >= need_num
	else
		-- 是否升星
		if is_sx then
			local star_cfg = self:GetTJUpStarCfg(card_data.seq,server_info.star)
			if not star_cfg then
				opera_info.remind = false
			else
				has_num = self:GetItemNumInBagById(star_cfg.consume_item_id)
				need_num = star_cfg.consume_item_num
				need_exp_num = star_cfg.need_exp
				opera_info.item_id = star_cfg.consume_item_id
				-- 满级判断
				opera_info.is_max_star = self.upstar_tujian_auto[card_data.seq] and #self.upstar_tujian_auto[card_data.seq] == server_info.star
				opera_info.remind = has_num >= need_num and not opera_info.is_max_star and self.exp >= need_exp_num
			end
		else
			local level_cfg = self:GetTJUpLevelCfg(card_data.seq,server_info.level)
			has_num = self:GetItemNumInBagById(level_cfg.consume_item_id)
			need_num = level_cfg.consume_item_num
			opera_info.item_id = level_cfg.consume_item_id
			local role_level = RoleWGData.Instance:GetAttr("level")
			opera_info.level_limit = level_cfg.need_role_level > role_level
			opera_info.need_role_level = level_cfg.need_role_level
			opera_info.is_max_level = #self.uplevel_tujian_auto[card_data.seq] == server_info.level
			opera_info.remind = has_num >= need_num and not opera_info.level_limit and not opera_info.is_max_level
		end
	end
	
	opera_info.need_exp_num = need_exp_num
	opera_info.has_exp_num = self.exp
	opera_info.has_num = has_num
	opera_info.need_num = need_num
	return opera_info
end

-- 获取图鉴名字
function ShanHaiJingWGData:GetTuJianNameBySeq(seq)
	local tujiancfg = self:GetTJCfgBySeq(seq)
	if not tujiancfg then return "" end
	local item_cfg = ItemWGData.Instance:GetItemConfig(tujiancfg.active_item_id)
	local name = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
	return name
end

-- 设置背包信息
function ShanHaiJingWGData:SetBagInfo(protocol)
	self.bag_list = protocol.bag_list
end

-- 背包信息改变
function ShanHaiJingWGData:SetChangeBagInfo(protocol)
	if not self.bag_list then return end
	local data = protocol.change_info

	local old_data = self.bag_list[data.index]
	self.bag_list[data.index] = data
	local cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local is_tujian = self:GetTJResolveShowCfg(data.item_id) ~= nil
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("ShanHaiJingView")
	if cfg then
		if not old_data or old_data.num == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, ToColorStr(cfg.name, GET_TIP_ITEM_COLOR[cfg.color]), data.num))
			if is_tujian and is_open then
				FunctionGuide.Instance:OpenKeyUseViewHasCallback(data.item_id, data.index, function ()
				end)
			end
		else
			if data.num - old_data.num > 0 then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, ToColorStr(cfg.name, GET_TIP_ITEM_COLOR[cfg.color]), data.num - old_data.num))
			end
		end
	end
end

-- 获取背包信息
function ShanHaiJingWGData:GetBagInfo()
	return self.bag_list or {}
end

-- 获取有序的背包信息(剔除掉升级材料)
function ShanHaiJingWGData:GetOrderBagInfo()
	local bag_list = self:GetBagInfo()
	local data_list = {}
	for k,v in pairs(bag_list) do
		if v.item_id ~= 0 then
			local itemcfg = self:GetTJResolveShowCfg(v.item_id)
			if itemcfg then
				table.insert(data_list,v)
			end
		end
	end

	-- SortTools.SortDesc(data_list,"is_special","color")
	return data_list
end

-- 获取背包中物品数量
function ShanHaiJingWGData:GetItemNumInBagById(item_id)
	local bag_list = self:GetBagInfo()
	local num = 0
	for k,v in pairs(bag_list) do
		if v.item_id == item_id then
			num = v.num + num
		end
	end
	return num
end

function ShanHaiJingWGData:CalAttrCfgPack(target_data_list, target_attr_name)
    local data_list = {}

    for k, v in pairs(target_data_list) do
        data_list[k] = data_list[k] or {}

        for index, data in pairs(v) do
            for i = data.min_grade, data.max_grade do
                data_list[k][i] = data[target_attr_name]
            end
        end
    end

    return data_list
end

--[[
-- 图鉴红点[废弃]
function ShanHaiJingWGData:GetTJRemindByCard(card_data, is_sx)
	local opera_info = self:GetTJOperaInfo(card_data, is_sx)
	if opera_info.remind then
		return true
	else
		opera_info = self:GetTJOperaInfo(card_data,true)
		return opera_info.remind and true or false
	end
end
]]

-- 图鉴红点新(不包含升级，已被屏蔽)
function ShanHaiJingWGData:GetTJRemindByCard(card_data)
	local server_info = self:GetTJAllServerInfo()
	if IsEmptyTable(server_info) then return false end
	server_info = server_info[card_data.seq]

	-- 激活红点
	local has_active = server_info.level > 0
	if not has_active then
		local has_num = self:GetItemNumInBagById(card_data.active_item_id)
		return has_num >= 1
	end

	-- 升星红点
	local star_cfg = self:GetTJUpStarCfg(card_data.seq, server_info.star)
	local is_max_star = self.upstar_tujian_auto[card_data.seq] and #self.upstar_tujian_auto[card_data.seq] == server_info.star
	if not is_max_star and star_cfg then
		local has_num = self:GetItemNumInBagById(star_cfg.consume_item_id)
		if has_num >= star_cfg.consume_item_num and self.exp >= star_cfg.need_exp then
			return true
		end
	end

	-- 升阶红点
	local upgrade_cfg = self:GetTJUpGradeCfg(card_data.seq)
	local is_max_grade = server_info.grade_level >= upgrade_cfg.max_grade_level
	if not is_max_grade then
		local has_num = self:GetItemNumInBagById(upgrade_cfg.consume_item_id)
		local consume_num = self:GetTJUpGradeConsume(upgrade_cfg.upgrade_seq, server_info.grade_level)
		if has_num >= consume_num then
			return true
		end
	end

	return false
end

-- 升星红点
function ShanHaiJingWGData:GetTJUpStarRemind(card_data)
	local server_info = self:GetTJAllServerInfo()
	if IsEmptyTable(server_info) then return false end
	server_info = server_info[card_data.seq]

	local has_active = server_info.level > 0
	if not has_active then
		return false
	end

	local star_cfg = self:GetTJUpStarCfg(card_data.seq, server_info.star)
	local is_max_star = self.upstar_tujian_auto[card_data.seq] and #self.upstar_tujian_auto[card_data.seq] == server_info.star
	if not is_max_star and star_cfg then
		local has_num = self:GetItemNumInBagById(star_cfg.consume_item_id)
		if has_num >= star_cfg.consume_item_num and self.exp >= star_cfg.need_exp then
			return true
		end
	end
	
	return false
end

-- 升阶红点
function ShanHaiJingWGData:GetTJUpGradeRemind(card_data)
	local server_info = self:GetTJAllServerInfo()
	if IsEmptyTable(server_info) then return false end
	server_info = server_info[card_data.seq]

	local has_active = server_info.level > 0
	if not has_active then
		return false
	end

	-- 升阶红点
	local upgrade_cfg = self:GetTJUpGradeCfg(card_data.seq)
	local is_max_grade = server_info.grade_level >= upgrade_cfg.max_grade_level
	if not is_max_grade then
		local has_num = self:GetItemNumInBagById(upgrade_cfg.consume_item_id)
		local consume_num = self:GetTJUpGradeConsume(upgrade_cfg.upgrade_seq, server_info.grade_level)
		if has_num >= consume_num then
			return true
		end
	end
	
	return false
end

-- 图鉴红点
function ShanHaiJingWGData:GetTJAllCardRemind()
	local fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.ShanHaiJingView)
	if not fun_is_open then
		return 0
	end

	local fun = function ()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TUJIAN_UP,1,function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.ShanHaiJingView)
			return true
		end)
	end

	local role_vo = RoleWGData.Instance.role_vo
	-- 图鉴红点
	for k,v in pairs(self.single_byseq_tujian_auto) do
		if v.is_show == 1 and role_vo.level >= v.show_level then
			if self:GetTJRemindByCard(v, true) then
				fun()
				return 1
			end
		end
	end
	-- 羁绊红点
	if self:GetAllZuHeRemind() == 1 then
		fun()
		return 1
	end
	-- 分解红点
	-- if self:GetResolveRemind() == 1 then
	-- 	fun()
	-- 	return 1
	-- end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TUJIAN_UP,0)
	return 0
end

-- 图鉴子页签红点
function ShanHaiJingWGData:GetTJChildCardRemind(card_type,card_child_type)
	local card_list = self:GetTJChildItemListInfo(card_type,card_child_type)
	for k,v in pairs(card_list) do
		local flag = self:GetTJRemindByCard(v, true)
		if flag then
			return true
		end
	end
	return false
end

-- 图鉴大页签红点
function ShanHaiJingWGData:GetTJBigCardRemind(card_type)
	local tujian_cfg = {}
	local role_vo = RoleWGData.Instance.role_vo
	for k,v in ipairs(self.active_tujian_auto[card_type]) do
		if v.is_show == 1 then
			if role_vo.level >= v.show_level then
				table.insert(tujian_cfg, v)
			end
		end
	end

	for k,v in pairs(tujian_cfg) do
		local flag = self:GetTJRemindByCard(v, true)
		if flag then
			return true
		end
	end

	-- 分解红点
	-- if self:GetResolveRemind(card_type) == 1 then
	-- 	return true
	-- end

	return false
end
------------------------------组合-----------------------------------
-- 获取图鉴信息
function ShanHaiJingWGData:GetTJCfgBySeq(seq)
	return self.single_byseq_tujian_auto[seq]
end

-- 设置图鉴组合服务器数据
function ShanHaiJingWGData:SetTJGroupActiveServerInfo(protocol)
	self.group_active_flag = {}
	local bit_list
 	for i,v in ipairs(protocol.group_active_flag) do
 		bit_list = bit:d2b(v)
 		for ii=32,25,-1 do
 			table.insert(self.group_active_flag,bit_list[ii])
 		end
 	end

	 if self.exp and protocol.exp and protocol.exp > self.exp then
		local num = protocol.exp - self.exp
		local str = string.format(Language.ShenShou.TuJianExpStr, num)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

 	self.exp = protocol.exp
end

function ShanHaiJingWGData:GetGroupTujianCfg(card_type)
	return self.group_tujian_auto_group_type[card_type] or {}
end

-- 获取羁绊显示列表
function ShanHaiJingWGData:GetZuHeInfoList(group_type, select_card_type)
	local data_list = {}
	-- 服务器组合激活标识
	local group_active_flag = self.group_active_flag
	if not group_active_flag then return data_list end
	-- 图鉴激活标识
	local tujian_active_flag = self:GetTJAllServerInfo()
	if IsEmptyTable(tujian_active_flag) then return data_list end
	local type_list = self:GetGroupTujianCfg(select_card_type)[group_type] or {}
	local group_type_list = nil == group_type and self.group_tujian_auto or type_list
	for i,v in ipairs(group_type_list) do
		local info = {}
		info.cfg_list = v
		info.seq = v.seq
		info.sort_id = v.sort_id
		local list_info_data = Split(v.tujian_seq_list,"|", true)
		info.active_num = 0

		for i1,v1 in ipairs(list_info_data) do
			if tujian_active_flag[tonumber(v1)].level > 0 then
				info.active_num = info.active_num + 1
			end
		end

		info.has_active = group_active_flag[v.seq + 1]
		if info.has_active == 1 then
			info.active_num = 0
		end
		-- 可激活标识
		info.can_active = info.has_active == 0 and info.active_num == #list_info_data or false

		if info.can_active then
			info.sort_active = 0
		else
			if info.has_active == 1 then
				info.sort_active = 1
			else
				info.sort_active = 2
			end
		end

		info.list_info_data = list_info_data
		table.insert(data_list,info)
	end
	-- 需要做一下排序 --升序 小在前
	SortTools.SortAsc(data_list,"sort_active","sort_id")--"active_num","has_active",'')
	return data_list
end

-- 获取羁绊名字
function ShanHaiJingWGData:GetZuHeNameBySeq(seq)
	for k,v in pairs(self.group_tujian_auto) do
		if v.seq == seq then
			return v.group_name, v
		end
	end
	return "", nil
end

-- 获取图鉴对应的羁绊配置
function ShanHaiJingWGData:GetZuHeSeqListBySeq(seq)
	for k,v in pairs(self.group_tujian_auto) do
		local list_info_data = Split(v.tujian_seq_list,"|", true)
		for _, v_seq in pairs(list_info_data) do
			if seq == tonumber(v_seq) then
				return list_info_data
			end
		end
	end
	return nil
end

-- 羁绊红点
function ShanHaiJingWGData:GetZuHeRemindBySeq(seq, data_list)
	local data_list = data_list or self:GetZuHeInfoList()
	for i,v in ipairs(data_list) do
		if v.cfg_list.seq == seq and v.can_active then
			return true
		end
	end
	return false
end

-- 羁绊红点
function ShanHaiJingWGData:GetAllZuHeRemind()
	local data_list = self:GetZuHeInfoList()
	for i,v in ipairs(self.group_tujian_auto) do
		if self:GetZuHeRemindBySeq(v.seq, data_list) then
			return 1
		end
	end
	return 0
end

function ShanHaiJingWGData:GetAllZuHeRemindByGroupType(group_type, select_card_type)
	local data_list = self:GetZuHeInfoList()
	local group = self.group_tujian_auto_group_type[select_card_type][group_type]
	if group then
		for k,v in pairs(group) do
			if self:GetZuHeRemindBySeq(v.seq, data_list) then
				return 1
			end
		end
	end
	return 0
end

function ShanHaiJingWGData:GetAllZuHeRemindByCardType(select_card_type)
	local data_list = self:GetZuHeInfoList()
	local group = self.group_tujian_auto_card_type[select_card_type]
	
	if group then
		for i,v in ipairs(group) do
			if self:GetZuHeRemindBySeq(v.seq, data_list) then
				return 1
			end
		end
	end

	return 0
end

-- 标记当前选中的图鉴
function ShanHaiJingWGData:SignCurSelectTuJian(seq)
	self.select_cur_tujian = seq
end

function ShanHaiJingWGData:GetCurSelectTuJian()
	return self.select_cur_tujian
end

function ShanHaiJingWGData:ResetCurSelectTuJian()
	self.select_cur_tujian = nil
end
--------------------------分解--------------------------------------
-- 获取分解面板显示配置
function ShanHaiJingWGData:GetResolveInfoList(card_type)
	local data_list = {}
	-- 背包信息
	local bag_info_list = self:GetOrderBagInfo()
	-- 激活标识
	local tujian_active_flag = self:GetTJAllServerInfo()
	for k,v in pairs(bag_info_list) do
		local tujiancfg = self:GetTJResolveShowCfg(v.item_id)
		local itemcfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		local server_info = tujian_active_flag[tujiancfg.seq]
		v.max_star = server_info and server_info.star >= 10
		local star_cfg = self:GetTJUpStarCfg(tujiancfg.seq,0)
		v.color = itemcfg.color
		v.tujiancfg = tujiancfg
		v.remind = v.max_star or true or false
		-- 激活判断  红粉满星判断
		if server_info and server_info.level > 0  then--and ((itemcfg.color >= 5 and server_info.star >= 10) or itemcfg.color < 5) then
			if card_type and tujiancfg.card_type == card_type then
				table.insert(data_list, v)
			elseif not card_type then
				table.insert(data_list, v)
			end
		end
	end

	return data_list
end

function ShanHaiJingWGData:GetCardType(tj_card_type)
	if tj_card_type == nil then
		return self.tj_card_type
	end

	self.tj_card_type = tj_card_type
end

-- 获取当前颜色对应的图鉴
function ShanHaiJingWGData:GetFenJieGridByColor(color,data_list)
	local select_list = {}
	for i,v in ipairs(data_list) do
		if v.color == color then
			table.insert(select_list,i)
		end
	end
	return select_list
end

-- 分解红点
function ShanHaiJingWGData:GetResolveRemind(select_card_type)
	local data_list = self:GetResolveInfoList(select_card_type)
	for k,v in pairs(data_list) do
		if v.remind then
			return 1
		end
	end
	return 0
end

------------------------------属性总览---------------------------------------------
function ShanHaiJingWGData:GetAttrInfoShow()
	local tujian_server_info = self:GetTJAllServerInfo()
	local level_cfg
	local star_cfg
	local base_cfg
	local star_add_per = 0
	local star_attr_list = AttributePool.AllocAttribute()
	local level_attr_list = AttributePool.AllocAttribute()
	local base_attr_list = AttributePool.AllocAttribute()
	local jc_attr_list = AttributePool.AllocAttribute()
	local sx_attr_list = AttributePool.AllocAttribute()
	local jb_attr_list = AttributePool.AllocAttribute()
	local sj_attr_list = AttributePool.AllocAttribute() --升阶
	local a, b, c = 0, 0, 0
	local base_attr_list = AttributeMgr.GetAttributteByClass(active_cfg) --基础属性读激活配置
	for k,v in pairs(tujian_server_info) do
		level_cfg = self:GetTJUpLevelCfg(k,v.level)
		star_cfg = self:GetTJUpStarCfg(k,v.star)
		active_cfg = self:GetTJCfgBySeq(seq)
		base_cfg = self.tujian_auto.active[k+1]

		star_add_per = star_cfg and (star_cfg.add_attr_per/10000) or 0
		if level_cfg and v.level > 0 then
			for k1, v1 in pairs(self.use_level_attr_key) do
				if level_cfg and level_cfg[k1] ~= nil and level_cfg[k1] ~= 0 then
					level_attr_list[v1] = level_attr_list[v1] + level_cfg[k1]
				end
			end

			for k1, v1 in pairs(self.use_star_attr_key) do
				if star_cfg and star_cfg[k1] ~= nil and star_cfg[k1] ~= 0 then
					star_attr_list[v1] = star_attr_list[v1] + star_cfg[k1]
				end
			end

			for k1, v1 in pairs(self.use_base_attr_key) do
				if base_cfg and base_cfg[k1] ~= nil and base_cfg[k1] ~= 0 then
					base_attr_list[v1] = base_attr_list[v1] + base_cfg[k1]
				end
			end

			if v.grade_level > 0 then
				local upgrade_attr = self:CalcTJUpGradeAttr(k, v.grade_level, base_cfg)
				for k1, v1 in pairs(self.use_base_attr_key) do
					if base_cfg and base_cfg[k1] ~= nil and base_cfg[k1] ~= 0 then
						sj_attr_list[v1] = sj_attr_list[v1] + upgrade_attr[k1]
					end
				end
			end
		end
	end

	-- 天道石影响属性百分比 升星属性
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.TUJIAN)
	charm_rate = charm_rate / 10000
	--- 

	for key, _ in pairs(jc_attr_list) do
		a = level_attr_list[key] or 0
		b = base_attr_list[key] or 0
		c = star_attr_list[key] or 0
		if sx_attr_list[key] and jc_attr_list[key] then
			if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(key) then
				sx_attr_list[key] = (c + sx_attr_list[key]) * (star_add_per + 1)
				jc_attr_list[key] = (a + b + jc_attr_list[key]) * (star_add_per + 1)
			else
				sx_attr_list[key] = (c + sx_attr_list[key]) * (star_add_per + charm_rate + 1)
				jc_attr_list[key] = (a + b + jc_attr_list[key]) * (star_add_per + charm_rate + 1)
			end
		end
	end

	-- 羁绊部分
	local jb_info_list = self:GetZuHeInfoList()
	local jb_cfg
	local special_add_attr = 0
	for k,v in pairs(jb_info_list) do
		if v.has_active == 1 then
			for k1, v1 in pairs(self.use_group_attr_key) do
				if v.cfg_list[k1] ~= nil and v.cfg_list[k1] ~= 0 then
					jb_attr_list[v1] = jb_attr_list[v1] + v.cfg_list[k1]
				end
			end

			-- jb_cfg = AttributeMgr.GetAttributteByClass(v.cfg_list)
			-- jb_attr_list = AttributeMgr.AddAttributeAttr(jb_cfg,jb_attr_list)
			if v.cfg_list.attr_added_modulus then
				special_add_attr = special_add_attr + v.cfg_list.attr_added_modulus
			end
		end
	end
	return jc_attr_list, sx_attr_list, jb_attr_list, sj_attr_list, special_add_attr / 100
end

-- 升阶属性，用于计算总属性
function ShanHaiJingWGData:CalcTJUpGradeAttr(seq, grade, base_attr)
	local upgrade_cfg = self:GetTJUpGradeCfg(seq)
	local attr_data = {}
	for i = 1, grade do
		local fix_fail_value = self:GetTJUpGradeFixFailCfg(upgrade_cfg.fix_fail_index, i) --固定值属性衰减万分比
		local per_fail_value = self:GetTJUpGradePerFailCfg(upgrade_cfg.per_fail_index, i) --百分比属性衰减万分比
		
		local base_value
		for k, v in pairs(self.use_base_attr_key) do
			base_value = base_attr[k]
			if base_value > 0 then
				local target_value = 0
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
				if is_per then
					target_value = math.floor(base_value * per_fail_value / 10000)
				else
					target_value = math.floor(base_value * fix_fail_value / 10000)
				end
				if not attr_data[k] then
					attr_data[k] = 0
				end
				attr_data[k] = attr_data[k] + target_value
			end
		end
	end
	return attr_data
end

function ShanHaiJingWGData:SetTJTips(is_show)
	self.show_tips = is_show
end

function ShanHaiJingWGData:GetTJTips()
	return self.show_tips
end

function ShanHaiJingWGData:SetSSQHStoneMatSetlect(is_select)
	self.select_stone_flag = is_select
end

function ShanHaiJingWGData:GetSSQHStoneMatSetlect()
	return self.select_stone_flag
end
