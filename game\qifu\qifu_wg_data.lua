QiFuWGData = QiFuWGData or BaseClass()

function QiFuWGData:__init()
	if QiFuWGData.Instance then
		error("[QiFuWGData] Attempt to create singleton twice!")
		return
	end
	QiFuWGData.Instance = self
	self.welfare_qifu_data = {}
	self.is_can = false
	self.from_tip_view = false
	self.expbuy_cfg = ConfigManager.Instance:GetAutoConfig("expbuy_cfg_auto")
	self.exp_cfg = ListToMap(self.expbuy_cfg.exp_cfg,"og_open_day","buy_time")
	RemindManager.Instance:Register(RemindName.WelfareQiFu, BindTool.Bind(self.IsShowWelfareQiFuRedPoint, self))--祈福
	RemindManager.Instance:Register(RemindName.QiFuGetEnergy, BindTool.Bind(self.IsShowGetEnergyRedPoint, self))--获取仙气
	RemindManager.Instance:Register(RemindName.HongMengWuDao, BindTool.Bind(self.IsShowGetHongMengRedPoint, self))--鸿蒙悟道
end

function QiFuWGData:__delete()
	QiFuWGData.Instance = nil
	self.is_can = false
	self.from_tip_view = false
	RemindManager.Instance:UnRegister(RemindName.WelfareQiFu)--祈福
	RemindManager.Instance:UnRegister(RemindName.QiFuGetEnergy)
	RemindManager.Instance:UnRegister(RemindName.HongMengWuDao)
end

function QiFuWGData:SetWelfareQiFuData(gold_buycoin_times, gold_buyyuanli_times, free_buycoin_time,qifu_reward_mutiple)
--	print_error("gold_buycoin_times",gold_buycoin_times, gold_buyyuanli_times, free_buycoin_time,qifu_reward_mutiple)
	self.welfare_qifu_data.gold_buycoin_times = gold_buycoin_times
	self.welfare_qifu_data.gold_buyyuanli_times = gold_buyyuanli_times
	self.welfare_qifu_data.free_buycoin_time = free_buycoin_time
	self.welfare_qifu_data.qifu_reward_mutiple = qifu_reward_mutiple
	RemindManager.Instance:Fire(RemindName.WelfareQiFu)--祈福
	--RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮
end

function QiFuWGData:GetGoldBuyyuanliTimes()
	return self.welfare_qifu_data.gold_buyyuanli_times or 0
end

function QiFuWGData:GetFreeTime()
	return self.welfare_qifu_data.free_buycoin_time
end

--祈福
function QiFuWGData:IsShowWelfareQiFuRedPoint()
	local data_list = QiFuWGData.Instance:GetWelfareQiFuDataList()
	local server_time = math.floor(TimeWGCtrl.Instance:GetServerTime())

	if data_list then
		for k,v in pairs(data_list) do
			if k == 1 then
				if v.free_buycoin_time and v.free_buycoin_time <= server_time + 2 then
					return 1
				end
			end
		end
	end
	return 0
end

--获取仙力
function QiFuWGData:IsShowGetEnergyRedPoint()
	return BossWGData.Instance:JudgeCanGetBossXianli() and 1 or 0
end

--鸿蒙悟道
function QiFuWGData:IsShowGetHongMengRedPoint()
	local isOpen = self:GetBtnIsShow()  --功能是否开启
	local exp_buy_info = self:GetExpBuyInfo()
	local can_buy_info = self:GetCanBuyInfo()
	local level = GameVoManager.Instance:GetMainRoleVo().level --人物等级

	local sheng_num = 0
	if can_buy_info then
		sheng_num = can_buy_info.buy_limit_count - exp_buy_info.buy_count
	end

	if isOpen and level < exp_buy_info.buy_limit_level and sheng_num > 0 then
		return 1
	end

	return 0
end

function QiFuWGData:GetAllCount()
	local gold_buycoin_times = self.welfare_qifu_data.gold_buycoin_times or 0
	local gold_buyyuanli_times = self.welfare_qifu_data.gold_buyyuanli_times or 0
	local coin_num_data = VipPower.Instance:GetParam(VipPowerId.pray_coin_times)
	local gold_num_data = VipPower.Instance:GetParam(VipPowerId.pray_exp_times)
	local num1 = coin_num_data - gold_buycoin_times
	local num2 = gold_num_data - gold_buyyuanli_times
	return num1 + num2,num1,num2
end

function QiFuWGData:GetWelfareQiFuDataList()
	local data_list = {}
	--print_error( self.welfare_qifu_data.gold_buycoin_times)
	data_list[1] = {
		free_buycoin_time = self.welfare_qifu_data.free_buycoin_time or 0,
		gold_buy_times = self.welfare_qifu_data.gold_buycoin_times or 0,

		qifu_reward_mutiple = self.welfare_qifu_data.qifu_reward_mutiple or {},
	}
	data_list[2] = {
		free_buycoin_time = 0,
		gold_buy_times = self.welfare_qifu_data.gold_buyyuanli_times or 0,
		qifu_reward_mutiple = self.welfare_qifu_data.qifu_reward_mutiple or {},
	}
	return data_list
end

function QiFuWGData:GetWelfareQiFuData()
	return self.welfare_qifu_data.qifu_reward_mutiple or {}
end

function QiFuWGData:GetWelfareQiFuBuyCost(num)
	local level_config = ConfigManager.Instance:GetAutoConfig("vip_auto")
	if #level_config.qifu_cost < num then
		num = num - 1
	end
	for k,v in ipairs(level_config.qifu_cost) do
		if v.min_buy_times <= num and num <= v.max_buy_times then
			return v
		end
	end
end

function QiFuWGData:GetWelfareQiFuExpReward()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local level_config = ConfigManager.Instance:GetAutoConfig("vip_auto")
	local config = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto")
	if level_config.qifu_reward[level] then
		local kill_monster_exp = config.level_reward_list[level].kill_monster_exp
		local num = level_config.qifu_reward[level].exp_copies * kill_monster_exp
		local exp_per = RoleWGData.Instance:GetRoleExpCorrection()
		num = num * exp_per
		return num, #level_config.qifu_reward
	end
	return 0, #level_config.qifu_reward
end

function QiFuWGData:GetWelfareQiFuCoinReward()
	local level_config = ConfigManager.Instance:GetAutoConfig("vip_auto")
	return level_config.other[1].qifu_silver_num
end

function QiFuWGData:RemindTip()
	local server_time = math.floor(TimeWGCtrl.Instance:GetServerTime())
	return server_time > self.welfare_qifu_data.free_buycoin_time and 1 or 0
end

function QiFuWGData:RemainQifuTime()
	local list = self:GetWelfareQiFuDataList()
	local num_data = VipPower.Instance:GetParam(VipPowerId.pray_exp_times)
	local num = num_data - list[2].gold_buy_times
	return num
end

function QiFuWGData:SetOpenFromExpTipView(is_from)
	self.from_tip_view = is_from
end

function QiFuWGData:IsOpenFromExpTipView()
	return self.from_tip_view
end
--今日是否提醒过
function QiFuWGData:RemindToday(remind_name)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	--local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local remind_day = PlayerPrefsUtil.GetString(remind_name)
	return tonumber(remind_day) == cur_day
end
--本地保存今天提醒状态（用于remind局部单日提醒）
function QiFuWGData:SetTodayDoFlag(remind_name)
	--local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id

	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	PlayerPrefsUtil.SetString(remind_name, cur_day)
end

function QiFuWGData:SetExpBuyInfo(protocol)
	self.exp_buy_info = {}
	self.exp_buy_info.end_timestame = protocol.end_timestame
	self.exp_buy_info.buy_limit_level = protocol.buy_limit_level
	self.exp_buy_info.buy_count = protocol.buy_count
end

function QiFuWGData:SetBtnEffect(is_can)
	self.is_can = is_can
end

function QiFuWGData:GetBtnEffect()
	return self.is_can
end

function QiFuWGData:GetExpBuyInfo()
	return self.exp_buy_info
end

function QiFuWGData:GetCanBuyInfo()
	local open_cfg = self.expbuy_cfg.open_cfg
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or -1
	local num = #open_cfg
	for i=num,1,-1 do
		if open_cfg[i].og_open_day <= open_day then
			return open_cfg[i]
		end
	end
end

function QiFuWGData:GetGanWuPrice()
	local exp_buy_info = self:GetExpBuyInfo()
	local price_cfg = self.expbuy_cfg.price_cfg
	if nil == exp_buy_info then
		return 0
	end
	local buy_count = exp_buy_info.buy_count
	local fg_num = #price_cfg
	for i=fg_num,1,-1 do
		if buy_count >= price_cfg[i].buy_time then
			return price_cfg[i].price
		end
	end
	return 0
end


function QiFuWGData:GetGanWuExP()
	local exp_buy_info = self:GetExpBuyInfo()
	local price_cfg = self.expbuy_cfg.price_cfg
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or -1
	if nil == exp_buy_info then
		return 0
	end
	local open_cfg = self.expbuy_cfg.open_cfg
	local limint_num = 0
	for k,v in pairs(open_cfg) do
		if open_day == v.og_open_day then
			limint_num = v.buy_limit_count
		end
	end
	local buy_count = exp_buy_info.buy_count + 1
	buy_count = buy_count > limint_num and limint_num or buy_count
	if self.exp_cfg[open_day] and self.exp_cfg[open_day][buy_count] then
		return self.exp_cfg[open_day][buy_count].exp
	end
	return 0
	-- local level = GameVoManager.Instance:GetMainRoleVo().level
	-- local config = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list
	-- local exp_cfg = self.expbuy_cfg.exp_cfg
	-- local dobule = 0
	-- local exp = 0
	-- for k,v in ipairs(config) do
	--  	if  level == v.level then
	--  		exp = v.kill_monster_exp
	--  		break
	--  	end
	-- end
	-- for k,v in pairs(exp_cfg) do
	-- 	if level >= v.limit_down_level and level <= v.limit_up_level then
	-- 		dobule = v.exp_copies
	-- 		break
	-- 	end
	-- end
	-- return exp*dobule
end

function QiFuWGData:GetBtnIsShow()
	local exp_buy_info = self:GetExpBuyInfo()
	local is_open = FunOpen.Instance:GetFunIsOpened("qifu_hmwd")
	if nil == exp_buy_info then
		return false
	end

	if exp_buy_info.end_timestame > TimeWGCtrl.Instance:GetServerTime() and is_open then
		return true
	end
	return false
end

function QiFuWGData:GetClickRemind()
	return self.is_click_hongmeng or false
end

function QiFuWGData:SetClickRemind()
	self.is_click_hongmeng = true
end