CrossTeamWGData = CrossTeamWGData or BaseClass()

function CrossTeamWGData:__init()
	if CrossTeamWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[CrossTeamWGData] attempt to create singleton twice!")
		return
	end
    CrossTeamWGData.Instance = self
    self.team_apply_list = {}
    self.team_invite_list = {}
    self.quick_invite_team_list = {}
    self.hebing_list = {}
    self.my_team_info = {}
end

function CrossTeamWGData:__delete()
    CrossTeamWGData.Instance = nil
    self.team_apply_list = {}
    self.team_invite_list = {}
    self.hebing_list = {}
    self.quick_invite_team_list = {}
    if CountDownManager.Instance:HasCountDown("cross_team_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("cross_team_invite_cd_time")
    end
end


function CrossTeamWGData:SaveTeamList(protocol)
    self.team_list = protocol.team_list
    for k, v in pairs(self.team_list) do
        table.sort(v.member_list, SortTools.KeyUpperSorter("is_leader"))
    end
end

function CrossTeamWGData:GetTeamList()
    if not self.team_list then
        self.team_list = {}
    end
    return self.team_list
end

--获得筛选等级的队伍列表
function CrossTeamWGData:GetTeamListForView()
    local team_list = self:GetTeamList()
    local cur_min, cur_max = self:GetPtTeamLimitLevel()
    local view_team_list = {}
    local team_min, team_max
    local my_level = RoleWGData.Instance:GetRoleLevel()
    for k, v in pairs(team_list) do
        team_min = v.min_level_limit
        team_max = v.max_level_limit
        --只显示队伍人数小于3的队伍, 和等級筛选下的队伍
        if v.cur_member_num < 3 and (my_level >= team_min and my_level <= team_max) and (my_level >= cur_min and my_level <= cur_max)
        and (cur_min <= team_min and cur_max >= team_max) then
            table.insert(view_team_list, v)
        end
    end
    return view_team_list
end

function CrossTeamWGData:AddToTeamList(protocol)
    local team_list = self:GetTeamList()
    team_list[#team_list+1] = protocol.add_item
end

function CrossTeamWGData:RemoveToTeamList(protocol)
    local team_list = self:GetTeamList()
    for k, v in pairs(team_list) do
        if v.index == protocol.index then
            table.remove(team_list, k)
        end
    end
end

function CrossTeamWGData:ClearTeamList()
    self.team_list = {}
end

function CrossTeamWGData:SaveInviteUserInfo(protocol)
    local temp = {}
    temp.inviter_uuid = protocol.inviter_uuid
    temp.inviter_usid = protocol.inviter_usid
    temp.req_role_name = protocol.inviter_name
    temp.req_role_camp = protocol.inviter_camp
    temp.req_role_prof = protocol.inviter_prof
    temp.req_role_sex = protocol.inviter_sex
    temp.inviter_vip_level = protocol.inviter_vip_level
    temp.inviter_level = protocol.inviter_level
    temp.avatar_key_big = protocol.avatar_key_big
	temp.avatar_key_small = protocol.avatar_key_small
    temp.team_min_level = protocol.inviter_team_min_level
    temp.team_max_level = protocol.inviter_team_max_level
    temp.inviter_relation_flag = protocol.inviter_relation_flag
    self:RemoveTeamInviteReq(temp.inviter_uuid)
    while(#self.team_invite_list >= 20)
	do
		local team_invite = table.remove(self.team_invite_list, 1)
		CrossTeamWGCtrl.Instance:SendInviteUserRet(team_invite.inviter_uuid, 1)
		CrossTeamWGCtrl.Instance:DeleteReq(team_invite.inviter_uuid)
	end
	table.insert(self.team_invite_list, temp)
end
--邀请
function CrossTeamWGData:RemoveTeamInviteReq(uuid)
	for k,v in pairs(self.team_invite_list) do
		if v.inviter_uuid == uuid then
			table.remove(self.team_invite_list, k)
			break
		end
	end
end

function CrossTeamWGData:TeamInviteReqClear()
	self.team_invite_list = {}
end

function CrossTeamWGData:GetInviteList()
	return self.team_invite_list
end

function CrossTeamWGData:GetInviteListSize()
	return #self.team_invite_list
end

function CrossTeamWGData:SaveReqJoinUserInfo(protocol)
    local temp = {}
    temp.req_uuid = protocol.req_uuid
    temp.req_usid = protocol.req_usid
    temp.req_role_name = protocol.req_role_name
    temp.req_role_camp = protocol.req_role_camp
    temp.req_role_prof = protocol.req_role_prof
    temp.req_role_sex = protocol.req_role_sex
    temp.req_role_vip_level = protocol.req_role_vip_level
    temp.req_role_level = protocol.req_role_level
    temp.req_avatar_timestamp = protocol.req_avatar_timestamp
    temp.req_role_photoframe = protocol.req_role_photoframe
    temp.req_role_capability = protocol.req_role_capability
    temp.req_role_relation_flag = protocol.req_role_relation_flag
    self:RemoveTeamJoinReq(temp.req_uuid)
	table.insert(self.team_apply_list, temp)
end

function CrossTeamWGData:TeamJoinReqClear()
	self.team_apply_list = {}
end

--获取申请列表
function CrossTeamWGData:GetReqTeamList()
	return self.team_apply_list
end

--获取申请数量
function CrossTeamWGData:GetReqTeamListSize()
	return #self.team_apply_list
end

--移除一条申请
function CrossTeamWGData:RemoveTeamJoinReq(uuid)
	for k,v in pairs(self.team_apply_list) do
		if v.req_uuid == uuid then
			table.remove(self.team_apply_list, k)
			break
		end
	end
	--RemindManager.Instance:Fire(RemindName.NewTeam_MyTeam)
end

function CrossTeamWGData:SaveMyTeamInfo(protocol)
    self.my_team_info = protocol.team
    local temp = {}
    local my_uuid = RoleWGData.Instance:GetUUid()
    for k, v in pairs(self.my_team_info.member_list) do
        if v.uuid.temp_low > 0 then
            table.insert(temp, v)
        end
        if v.uuid == my_uuid and v.is_leader == 0 then
            self:RestHeBing()
        end
    end
    table.sort(temp, SortTools.KeyUpperSorter("is_leader"))
    self.my_team_info.member_list = temp
end

-- 申请入队是否需要验证，0否，1是
function CrossTeamWGData:GetTeamMustCheck()
	return self.my_team_info.must_check or 1
end

function CrossTeamWGData:GetTeamMemberList()
    local member_list = {}
    local msg_info = self.my_team_info and self.my_team_info.member_list or {}
	for i,v in pairs(msg_info) do
		table.insert(member_list, v)
	end
	return member_list
end

--获取当前队伍的
function CrossTeamWGData:GetTeamLimitLevel()
    if IsEmptyTable(self.my_team_info) then
        return 1, COMMON_CONSTS.MaxRoleLevel
    end
	return self.my_team_info.min_level_limit,  self.my_team_info.max_level_limit
end

--设置全部队伍筛选
function CrossTeamWGData:SetPtTeamLimitLevel(min_level, max_level)
	self.pt_min_level_limit = min_level
	self.pt_max_level_limit = max_level
end

function CrossTeamWGData:GetPtTeamLimitLevel()
    if self.pt_min_level_limit then
        return self.pt_min_level_limit,  self.pt_max_level_limit
    end
    return 1, COMMON_CONSTS.MaxRoleLevel
end

--获取默认的
function CrossTeamWGData:GetDefTeamLimitLevel()
    return 1, COMMON_CONSTS.MaxRoleLevel
end

function CrossTeamWGData:GetTeamMemberListView()
	local member_list = self:GetTeamMemberList()
	local role_vo = RoleWGData.Instance.role_vo
	-- if 0 == #member_list then
    --     member_list[1] = {name = role_vo.name, level = role_vo.level, uuid = role_vo.uuid, prof = role_vo.prof, 
    --     is_match = false, is_leader = 0, vip_level = role_vo.vip_level}
	-- end

	local sort_team = function (a, b)
		local order_a = 100000
		local order_b = 100000
		if a.is_leader == 1 then
			order_a = order_a + 100000
		elseif b.is_leader == 1 then
			order_b = order_b + 100000
		end

		if a.uuid == RoleWGData.Instance:GetUUid() then
			order_a = order_a + 1000
		elseif b.uuid == RoleWGData.Instance:GetUUid() then
			order_b = order_b + 1000
		end

		return order_a > order_b
	end

	table.sort(member_list, sort_team)

	local change_index = {1,2,3}
	return change_index, member_list
end

function CrossTeamWGData:GetIsInTeam()
    local flag = IsEmptyTable(self.my_team_info)
    return flag and 0 or 1
end

function CrossTeamWGData:GetTeamMemberCount()
    local is_in_team = self:GetIsInTeam()
    if is_in_team == 0 then
        return 0
    end
    return self.my_team_info and self.my_team_info.cur_member_num or 0
end

function CrossTeamWGData:GetTargetIsTeamMember(uid)
	local my_uid = RoleWGData.Instance:GetUUid()
	local list = self:GetTeamMemberList()
	for i, v in ipairs(list) do
		if my_uid ~= v.uuid and uid == v.uuid then
			return true
		end
	end
	return false
end

function CrossTeamWGData:GetTeamIndex()
    return self.my_team_info and self.my_team_info.index or nil
end

function CrossTeamWGData:GetIsTeamLeader()
    local is_in_team = self:GetIsInTeam()
    if is_in_team == 0 then
        return 0
    end
    if not IsEmptyTable(self.my_team_info) and not IsEmptyTable(self.my_team_info.member_list)  then
        local uuid = RoleWGData.Instance:GetUUid()
        for k, v in pairs(self.my_team_info.member_list) do
            if v.uuid == uuid then
                return v.is_leader
            end
        end
    end
    return 0
end

function CrossTeamWGData:ClearTeamData()
	self.my_team_info = {}
end

function CrossTeamWGData:SetNearRoleList(protocol)
    local role_list = __TableCopy(protocol.role_list)
	local member_list = self:GetTeamMemberList()
	self.near_role_list = {}
	local my_uuid = RoleWGData.Instance:GetUUid()
	if IsEmptyTable(member_list) then
		for k,v in pairs(protocol.role_list) do
			if v.uuid == my_uuid then
				table.remove(role_list,k)
				break
			end
		end
		self.near_role_list = role_list
		return
	end

	for k = #protocol.role_list,1,-1 do
		for i,j in pairs(member_list) do
			if j.uuid == protocol.role_list[k].uuid then
				table.remove(role_list,k)
			end
		end
	end

	self.near_role_list = role_list
end

function CrossTeamWGData:GetNearRoleList()
    local team_list = self:GetTeamList()
    local cur_team_count = self:GetTeamMemberCount()
    local near_role_list = __TableCopy(self.near_role_list or {}) --不去影响协议数据
    if not IsEmptyTable(team_list) then
        for k, v in pairs(team_list) do
            if v.cur_member_num + cur_team_count <= 3 then
                for k1, v1 in pairs(v.member_list) do
                    if v1.is_leader == 1 then
                        local stu = {}
                        stu.uuid = v1.uuid
                        stu.usid = v1.usid
                        stu.name = v1.name
                        stu.avatar = v1.avatar
                        stu.sex = v1.sex
                        stu.prof = v1.prof
                        stu.camp = v1.camp
                        stu.capability = v1.capability
                        stu.level = v1.level
                        stu.shizhuang_photoframe = v1.shizhuang_photoframe
                        stu.team_index = v.index
                        stu.vip_level = v1.vip_level
                        stu.orig_uid = v1.uuid.temp_low
                        stu.zhandui_id = -1 --没用
                        stu.relation_flag = 0 --其他地方赋值
                        near_role_list[#near_role_list + 1] = stu
                    end
                end
            end
        end
    end
    local my_uuid = RoleWGData.Instance:GetUUid()
    for k,v in pairs(near_role_list) do
        if v.uuid == my_uuid then
            table.remove(near_role_list, k)
            break
        end
    end

    for i = #near_role_list, 0, -1 do
        while(i+1<#near_role_list and near_role_list[i] and near_role_list[i+1] and near_role_list[i].uuid == near_role_list[i+1].uuid) do
            near_role_list[i].team_index = -1
            table.remove(near_role_list, i + 1)
        end
    end
    local list = __TableCopy(SocietyWGData.Instance:GetFriendList2())
    for k,v in pairs(list) do
        for i,j in pairs(near_role_list) do
            if v.user_id == j.uuid.temp_low then
				j.relation_flag = v.relation_flag
			end
		end
    end
    
    local m_list = GuildDataConst.GUILD_MEMBER_LIST
    for i = 1, m_list.count do
        local item = m_list.list[i]
        local uuid = MsgAdapter.ReadUUIDByValue(item.uid, my_uuid.temp_high)
        for i, j in pairs(near_role_list) do
            if uuid == j.uuid then
                j.relation_flag = item.relation_flag
            end
        end
    end

    return near_role_list
end


function CrossTeamWGData:GetNearFriendList()
    local near_list = self:GetNearRoleList()
    if IsEmptyTable(near_list) then
        return {}
    end
	local list = __TableCopy(SocietyWGData.Instance:GetFriendList2())
    local member_list = self:GetTeamMemberList()
    local my_uuid = RoleWGData.Instance:GetUUid()
    for k,v in pairs(list) do
        v.uuid = MsgAdapter.ReadUUIDByValue(v.user_id, my_uuid.temp_high)
        v.name = v.gamename
		for i,j in ipairs(member_list) do
            if v.user_id == j.uuid.temp_low then
				table.remove(list,k)
			end
		end
    end
    local temp_list = {}
    for k, v in pairs(list) do
        for k1, v1 in pairs(near_list) do
            if v.uuid == v1.uuid then
                v.usid = v1.usid
                v.team_index = v1.team_index
                table.insert(temp_list, v)
            end
        end
    end
	return temp_list
end

function CrossTeamWGData:GetNearGuildList()
    local near_list = self:GetNearRoleList()
    if IsEmptyTable(near_list) then
        return {}
    end
    local m_list = GuildDataConst.GUILD_MEMBER_LIST
    local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
    local my_uuid = RoleWGData.Instance:GetUUid()
    local data_list = {}
    for i = 1, m_list.count do
        local item = m_list.list[i]
        local uuid = MsgAdapter.ReadUUIDByValue(item.uid, my_uuid.temp_high)
        if item.uid ~= my_uid and 1 == item.is_online then 
            local datasource = {uuid = uuid, user_id = item.uid, name = item.role_name, level = item.level, sex = item.sex,
                        prof = item.prof, team_index = item.team_index, post = item.post,  is_online = item.is_online,
                        join_time = item.join_time,capability = item.capability, vip_level = item.vip_level
                        , relation_flag = item.relation_flag, fashion_photoframe = item.photframe, shield_vip_flag = item.shield_vip_flag}
                table.insert(data_list, datasource)
        end
    end

    local temp_list = {}
    for k, v in pairs(data_list) do
        for k1, v1 in pairs(near_list) do
            if v.uuid == v1.uuid then
                v.usid = v1.usid
                v.team_index = v1.team_index
                v.relation_flag = v1.relation_flag
                table.insert(temp_list, v)
            end
        end
    end
	return temp_list
end

function CrossTeamWGData:ClearNearRoleList()
    self.near_role_list = {} 
end

--只能在同个场景邀请
function CrossTeamWGData:GetSceneName()
    local scene_id = Scene.Instance:GetSceneId()
    local map_name = Config_scenelist[scene_id] and Config_scenelist[scene_id].name or Language.XianJieBoss.XianJieBossName
    return map_name
end

------------快速组队相关---------

function CrossTeamWGData:GetQuickInviteFromUserTransmit(team_info)
    -- 2022.5.27 拒绝所有人组队邀请（原 拒绝陌生人）
	if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.REFUSE_STRANGER_TEAM) then
		-- local is_friend = SocietyWGData.Instance:CheckIsFriend(protocol.inviter)
		-- if not is_friend then
			return
		-- end
	end

	if self:GetIsInTeam() == 1 or #self.quick_invite_team_list > 20 then
		return
	end

	local info = CrossTeamWGData.GetQuickInviteTeamInfo(team_info)
	table.insert(self.quick_invite_team_list, info)
	CrossTeamWGCtrl.Instance:OpenQuickInviteTeam()
end

function CrossTeamWGData:OnSCTeamInviteChannelChat(protocol)
	-- 2022.5.27 拒绝所有人组队邀请（原 拒绝陌生人）
	if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.REFUSE_STRANGER_TEAM) then
		-- local is_friend = SocietyWGData.Instance:CheckIsFriend(protocol.inviter)
		-- if not is_friend then
			return
		-- end
	end

	if self:GetIsInTeam() == 1 or #self.quick_invite_team_list > 20 then
		return
    end

    local leader_info = protocol.leader_info
    local data = {}
    data.inviter_uuid = leader_info.uuid
    data.inviter_name = leader_info.name
    data.inviter_usid = leader_info.usid
    table.insert(self.quick_invite_team_list, CrossTeamWGData.GetQuickInviteTeamInfo(data))
    CrossTeamWGCtrl.Instance:OpenQuickInviteTeam()
end

function CrossTeamWGData.GetQuickInviteTeamInfo(msg_info)
	local t = {}
	t.inviter = msg_info and msg_info.inviter_uuid or nil
    t.inviter_name = msg_info and msg_info.inviter_name or ""
    t.inviter_usid = msg_info and msg_info.inviter_usid or nil
	return t
end

function CrossTeamWGData:GetQuickInviteTeamTopTeamInfo()
	return not IsEmptyTable(self.quick_invite_team_list) and table.remove(self.quick_invite_team_list, 1) or nil
end

function CrossTeamWGData:ClearQuickInviteTeamList()
	self.quick_invite_team_list = {}
end

-- 缓存邀请人员的cd列表
function CrossTeamWGData:AddCacheCDList(uuid, time)
    if not self.cache_invite_cd_list then
        self.cache_invite_cd_list = {}
    end
    if CountDownManager.Instance:HasCountDown("cross_team_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("cross_team_invite_cd_time")
    end
    CountDownManager.Instance:AddCountDown("cross_team_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CheckInviteTime, self), 40 + TimeWGCtrl.Instance:GetServerTime())
    time = time or 30
    local is_exist = false
    for k, v in pairs(self.cache_invite_cd_list) do
        if v.uuid == uuid then
            is_exist = true
            break
        end
    end
    if not is_exist then
        local temp = {}
        temp.time = time
        temp.uuid = uuid
        self.cache_invite_cd_list[#self.cache_invite_cd_list + 1] = temp
    end
    CrossTeamWGCtrl.Instance:FlushTextInvite()
end

function CrossTeamWGData:GetCacheCDByRoleid(uuid)
    if not self.cache_invite_cd_list then
        return 0
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        if v.uuid == uuid then
            return v.time
        end
    end 
    return 0
end

function CrossTeamWGData:CheckInviteTime()
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("cross_team_invite_cd_time")
    else
        CountDownManager.Instance:AddCountDown("cross_team_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
        BindTool.Bind(self.CheckInviteTime,self), 40 + TimeWGCtrl.Instance:GetServerTime())
    end
end

-- 缓存邀请人员的cd列表
function CrossTeamWGData:UpdateInviteTime(elapse_time, total_time)
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("cross_team_invite_cd_time")
        return
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        local time = self.cache_invite_cd_list[k].time
        self.cache_invite_cd_list[k].time = time - 1
        if self.cache_invite_cd_list[k].time <= 0 then
            self.cache_invite_cd_list[k] = nil
            table.remove(self.cache_invite_cd_list, k)
        end
    end
    CrossTeamWGCtrl.Instance:FlushTextInvite()
end


----合并
--获取合并列表
function CrossTeamWGData:GetHeBingList()
	return self.hebing_list
end

--获取合并数量
function CrossTeamWGData:GetHeBingCount()
	return #self.hebing_list
end

--添加合并消息
function CrossTeamWGData:AddHeBingInfo(protocol)
	local hebing_item = {}
    hebing_item.uuid = protocol.uuid
    hebing_item.usid = protocol.usid
	hebing_item.req_role_name = protocol.name
	hebing_item.req_role_vip_level = protocol.vip_level
	hebing_item.req_role_level = protocol.level
	hebing_item.req_role_sex = protocol.sex
	hebing_item.req_role_prof = protocol.prof
	hebing_item.req_role_photoframe = protocol.shizhuang_photoframe
	hebing_item.avatar_key_big = protocol.avatar_key_big
	hebing_item.avatar_key_small = protocol.avatar_key_small
	hebing_item.req_role_capability = protocol.capability
	hebing_item.relation_flag = protocol.relation_flag

	self:RemoveHeBingItem(hebing_item.uuid)
	table.insert(self.hebing_list, hebing_item)
	if #self.hebing_list > COMMON_CONSTS.TEAM_HEBING_COUNT then
		table.remove(self.hebing_list, 1)
	end
end

--移除合并消息
function CrossTeamWGData:RemoveHeBingItem(uuid)
	local remove_success = false
	for i, v in ipairs(self.hebing_list) do
		if v.uuid == uuid then
			table.remove(self.hebing_list, i)
			remove_success = true
			break
		end
	end

	return remove_success
end

--清空合并消息
function CrossTeamWGData:ClearHeBing()
	self.hebing_list = {}
end

function CrossTeamWGData:RestHeBing()
	self:ClearHeBing()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)
	NewTeamWGCtrl.Instance:CloseHeBingView()
end

--是否是同一个大跨服的玩家（在跨服场景中调用）
function CrossTeamWGData:GetIsCanDoCrossTeamACT(area_index)
    if not Scene.Instance:GetIsOpenCrossViewByScene() then
        return false
    end
    local main_vo = GameVoManager.Instance:GetMainRoleVo()
    if area_index ~= - 1 and main_vo.area_index ~= -1 then
        return main_vo.area_index == area_index
    else
        return false
    end
end

-- 设置喊话CD结束时间
function CrossTeamWGData:SetRecruitCdEndTime()
	local cd_time = 20
	self.recruit_cd_end_time = Status.NowTime + cd_time
end

-- 获取喊话CD结束时间
function CrossTeamWGData:GetRecruitCdEndTime()
	return self.recruit_cd_end_time or Status.NowTime
end

function CrossTeamWGData:GetRecruitCdTime()
    local end_time = self:GetRecruitCdEndTime()
    if (end_time - Status.NowTime) > 0 then
        return math.ceil(end_time - Status.NowTime)
    else
        return math.floor(end_time - Status.NowTime)
    end
end