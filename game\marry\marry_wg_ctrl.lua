require("game/marry/marry_view")
require("game/marry/marry_propose_view")
require("game/marry/marry_proposeback_view")
require("game/marry/marry_wg_data")
require("game/marry/marry_jiehun")
require("game/marry/marry_hunjie")
require("game/marry/marry_baoxia")
require("game/marry/marry_fb")
require("game/marry/marry_baby")
require("game/marry/marry_dianchun")
require("game/marry/marry_friend_list")
require("game/marry/marry_yuyue_view/marry_explain")
require("game/marry/marry_yuyue_view/marry_xunyuan_fabu")
require("game/marry/marry_yuyue_view/marry_tiqin")
require("game/marry/marry_yuyue_view/marry_yuyue")
require("game/marry/marry_yuyue_view/marry_invite")
require("game/marry/marry_yuyue_view/marry_hunyan_task")
require("game/marry/marry_yuyue_view/marry_intimacy")
require("game/marry/marry_yuyue_view/marry_fb_task")
require("game/marry/marry_yuyue_view/marry_fb_choose")
require("game/marry/marry_yuyue_view/marry_title")
require("game/marry/marry_nearbysingle_view")
require("game/marry/marry_xunyou_view")
require("game/marry/marry_baitang_view")
require("game/marry/marry_yuyue_view/marry_fingerprint_view")
require("game/marry/marry_profess_wall_view")
require("game/marry/marry_blessing_view")
require("game/marry/marry_notice_view")
require("game/marry/marry_yuyue_view/marry_paiqi_view")
require("game/marry/marry_yuyue_view/marry_beiyao_view")
require("game/marry/marry_fbbuy_tips_view")
require("game/marry/hunjie_skill_tip_view")
require("game/marry/xunyou_open_hint_view")
require("game/marry/marry_common_alert")
require("game/marry/marry_view_will_get")
require("game/marry/marry_yuyue_view/marry_discount")
require("game/marry/marry_flowers_view")
require("game/marry/marry_baoxia_view")

MarryWGCtrl = MarryWGCtrl or BaseClass(BaseWGCtrl)

local LIAO_YI_LIAO_TYPE = {
	LIAO_YI_LIAO_TYPE_INTERACTION = 1,		--撩对方
	LIAO_YI_LIAO_TYPE_CHANGE_ROLE = 2,		--切换玩家
	LIAO_YI_LIAO_TYPE_REFRESH_LIST = 3,		--刷新匹配玩家
}

function MarryWGCtrl:__init()
	if MarryWGCtrl.Instance ~= nil then
		ErrorLog("[MarryWGCtrl] Attemp to create a singleton twice !")
	end
	self:SetMoveXuyou(false)
	MarryWGCtrl.Instance = self
	self.view = MarryView.New(GuideModuleName.Marry)
	self.data = MarryWGData.New()
	self.qiuhun = MarryProposeView.New(GuideModuleName.MarryPropose)
	self.qiuhun_back = MarryProposeBackView.New(GuideModuleName.MarryProposeBack)
	self.friend_view = MarryFriendView.New(GuideModuleName.MarryFriend)
	self.nearbysingleview = NearbySingleView.New(GuideModuleName.MarryNearbySingle)
	self.will_get_new_view = WaWillGetNew.New(GuideModuleName.MarryGetNew)
	-------------------------------------------
	self.explain_view = MarryExplainView.New(GuideModuleName.MarryExplain)
	--self.xunyuan_view = MarryXunYuanView.New(GuideModuleName.MarryXunYuan)
	self.xunyuan_fabu_view = MarryXunYuanFaBuView.New(GuideModuleName.MarryXunYuanFaBu)
	self.tiqin_view = MarryTiQinView.New(GuideModuleName.MarryTiQin)
	self.discount_view = MarryDiscountView.New(GuideModuleName.MarryDiscountView)
	self.marry_yuyue = MarryYuYueView.New(GuideModuleName.MarryYuYue)
	self.marry_paiqi = MarryPaiQiView.New(GuideModuleName.MarryPaiQi)
	self.marry_beiyao = MarryBeiYaoView.New(GuideModuleName.MarryBeiYao)

	self.invite_view = MarryInviteView.New(GuideModuleName.MarryInvite)
	self.hunyan_task = MarryHunYanTask.New(GuideModuleName.MarryHunYanTask)
	self.marry_intimacy = MarryIntimacyView.New(GuideModuleName.MarryIntimacy)     --亲密度小界面
	self.fb_task = MarryFbTaskView.New(GuideModuleName.MarryFbTaskView)
	self.fb_choose = MarryFbChooseView.New(GuideModuleName.MarryFbChoose)
	self.marry_baitang_view = MarryBaiTangView.New(GuideModuleName.MarryBaiTangView) --是否同意拜堂
	---------------------------------------------------------------------
	self.marry_title_view = MarryTitleView.New(GuideModuleName.MarryTitleView)
	self.skill_tip_view = HunJieSkillTipView.New()
	---------------------------------------------------------------------
	self.marry_xunyou_open_view = XunYouOpenHintView.New(GuideModuleName.XunYouOpenHintView) --巡游开始提示针对其他人除新郎新娘
	self.marry_xunyou_view = MarryXunYouView.New(GuideModuleName.Marry_XunYouView) --巡游
	self.marry_fingerprint_view = MarryFingerPrintView.New(GuideModuleName.MarryFingerPrintView) 	--按手印

	self.marry_blessing_view = MarryBlessingView.New(GuideModuleName.MarryBlessingView) --祝福界面
	self.marry_notice_view = MarryNoticeView.New(GuideModuleName.MarryNotice) --结婚预告
	self.marry_fbbuy_view = MarryFbBuyTipsView.New() --副本购买次数

	self.is_self_buy_dianchun_num = false
	self.dianchun_view_close = true
	self.cur_buy_dianchun_num = -1

	self.marry_flag = true
	self.first_enter_game = true

	self.is_can_open_xunyou_hint = true

	self.marry_baoxia_view = MarryBaoXiaView.New(GuideModuleName.MarryBaoXiaView)

	self:RegisterAllProtocols()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.DayChange, self))
	self.activity_call_back = BindTool.Bind(self.ActivityChangeCallback, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_call_back)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		self.data:IsShowMainUIChatIcon(true)
	end)

	self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function MarryWGCtrl:__delete()
	-- self:SendCoupleDianChunCloseReq()
	MarryWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.qiuhun then
		self.qiuhun:DeleteMe()
		self.qiuhun = nil
	end

	if self.qiuhun_back then
		self.qiuhun_back:DeleteMe()
		self.qiuhun_back = nil
	end

	if self.explain_view then
		self.explain_view:DeleteMe()
		self.explain_view = nil
	end

	if self.tiqin_view then
		self.tiqin_view:DeleteMe()
		self.tiqin_view = nil
	end

	if self.discount_view then
		self.discount_view:DeleteMe()
		self.discount_view = nil
	end

	if self.marry_yuyue then
		self.marry_yuyue:DeleteMe()
		self.marry_yuyue = nil
	end

	if self.invite_view then
		self.invite_view:DeleteMe()
		self.invite_view = nil
	end

	if self.hunyan_task then
		self.hunyan_task:DeleteMe()
		self.hunyan_task = nil
	end

	if self.marry_intimacy then
		self.marry_intimacy:DeleteMe()
		self.marry_intimacy = nil
	end

	if self.fb_task then
		self.fb_task:DeleteMe()
		self.fb_task = nil
	end

	if self.fb_choose then
		self.fb_choose:DeleteMe()
		self.fb_choose = nil
	end

	if self.marry_title_view then
		self.marry_title_view:DeleteMe()
		self.marry_title_view = nil
	end

	if self.alert_baoxia then
		self.alert_baoxia:DeleteMe()
		self.alert_baoxia = nil
	end

	if self.friend_view then
		self.friend_view:DeleteMe()
		self.friend_view = nil
	end

	if self.nearbysingleview then
		self.nearbysingleview:DeleteMe()
		self.nearbysingleview = nil
	end

	if self.will_get_new_view then
		self.will_get_new_view:DeleteMe()
		self.will_get_new_view = nil
	end

	if nil ~= self.dianchun_invite_alert then
		self.dianchun_invite_alert:DeleteMe()
		self.dianchun_invite_alert = nil
	end

	if nil ~= self.dianchun_cd_end_alert then
		self.dianchun_cd_end_alert:DeleteMe()
		self.dianchun_cd_end_alert = nil
	end

	if self.marry_xunyou_view then
		self.marry_xunyou_view:DeleteMe()
		self.marry_xunyou_view = nil
	end

	if self.marry_xunyou_open_view then
		self.marry_xunyou_open_view:DeleteMe()
		self.marry_xunyou_open_view = nil
	end

	if self.marry_baitang_view then
		self.marry_baitang_view:DeleteMe()
		self.marry_baitang_view = nil
	end

	if self.marry_fingerprint_view then
		self.marry_fingerprint_view:DeleteMe()
		self.marry_fingerprint_view = nil
	end

	if self.global_yuelao_event then
		GlobalEventSystem.UnBind(self.global_yuelao_event)
		self.global_yuelao_event = nil
	end

	if self.marry_blessing_view then
		self.marry_blessing_view:DeleteMe()
		self.marry_blessing_view = nil
	end

	if self.goto_yuelao_alert then
		self.goto_yuelao_alert:DeleteMe()
		self.goto_yuelao_alert = nil
	end

	if 	self.marry_notice_view then
		self.marry_notice_view:DeleteMe()
		self.marry_notice_view = nil
	end

	if self.marry_paiqi then
		self.marry_paiqi:DeleteMe()
		self.marry_paiqi = nil
	end

	if self.marry_beiyao then
		self.marry_beiyao:DeleteMe()
		self.marry_beiyao = nil
	end

	if self.marry_fbbuy_view then
		self.marry_fbbuy_view:DeleteMe()
		self.marry_fbbuy_view = nil
    end

	if self.skill_tip_view then
		self.skill_tip_view:DeleteMe()
		self.skill_tip_view = nil
	end

	if self.xunyou_hint_alert then
		self.xunyou_hint_alert:DeleteMe()
		self.xunyou_hint_alert = nil
	end

	if self.qiuhun_hint_aleart then
		self.qiuhun_hint_aleart:DeleteMe()
		self.qiuhun_hint_aleart = nil
    end

    if self.xunyou_remind_alert then
		self.xunyou_remind_alert:DeleteMe()
		self.xunyou_remind_alert = nil
    end

    if self.xunyou_remind_alert2 then
		self.xunyou_remind_alert2:DeleteMe()
		self.xunyou_remind_alert2 = nil
	end	

	if self.marry_baoxia_view then
		self.marry_baoxia_view:DeleteMe()
		self.marry_baoxia_view = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	self.item_remind_id = nil
	self.marry_flag = true
	GlobalTimerQuest:CancelQuest(self.dianchun_cd_timer_quest)
	self:DelXunYouRoleRule()
	self:EndTimeCallBack()
    if CountDownManager.Instance:HasCountDown("xunyou_steady") then
        CountDownManager.Instance:RemoveCountDown("xunyou_steady")
    end

    if CountDownManager.Instance:HasCountDown("xunyou_alert_timer") then
        CountDownManager.Instance:RemoveCountDown("xunyou_alert_timer")
    end
	self.is_can_open_xunyou_hint = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.activity_call_back then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_call_back)
		self.activity_call_back = nil
	end
end

function MarryWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local item_cfg = MarryWGData.Instance:GetSendFlowersItemList()
	for k, v in pairs(item_cfg) do
		if change_item_id == v.item_id then
			-- if self.view:IsOpen() and self.view:GetShowIndex() == MARRY_TAB_TYPE.ZH then
			-- 	self.view:Flush(MARRY_TAB_TYPE.ZH, "flower_item_num_change")
			-- end
			if FlowerWGCtrl.Instance.send_flower_view:IsOpen() and FlowerWGCtrl.Instance.send_flower_view:GetShowIndex() == TabIndex.flower_upgrade then
				FlowerWGCtrl.Instance.send_flower_view:Flush(TabIndex.flower_upgrade, "flower_item_num_change")
			end
		end
	end
end

function MarryWGCtrl:SetXunYouRoleRule()
	if nil == self.set_rule then
		self.set_rule = SimpleRule.New(ShieldObjType.MainRole, ShieldRuleWeight.Max, function()
			return MarryWGData.Instance:GetOwnIsXunyou()
		end)
		self.set_rule:Register()
	end

	if nil == self.set_follow_rule then
		self.set_follow_rule = SimpleRule.New(ShieldObjType.MainRoleFollowUI, ShieldRuleWeight.Max, function()
			return MarryWGData.Instance:GetOwnIsXunyou()
		end)
		self.set_follow_rule:Register()
	end

	if nil == self.set_shadow_rule then
		self.set_shadow_rule = SimpleRule.New(ShieldObjType.MainRoleShadow, ShieldRuleWeight.Max, function()
			return MarryWGData.Instance:GetOwnIsXunyou()
		end)
		self.set_shadow_rule:Register()
	end

	if nil == self.set_title_rule then
		self.set_title_rule = SimpleRule.New(ShieldObjType.MainRoleFollowTitle, ShieldRuleWeight.Max, function()
			return MarryWGData.Instance:GetOwnIsXunyou()
		end)
		self.set_title_rule:Register()
	end
	

end

function MarryWGCtrl:DelXunYouRoleRule()
	if self.set_rule then
		self.set_rule:DeleteMe()
		self.set_rule = nil
	end

	if self.set_follow_rule then
		self.set_follow_rule:DeleteMe()
		self.set_follow_rule =nil
	end

	if self.set_shadow_rule then
		self.set_shadow_rule:DeleteMe()
		self.set_shadow_rule = nil
	end

	if self.set_title_rule then
		self.set_title_rule:DeleteMe()
		self.set_title_rule = nil
	end
	
end


function MarryWGCtrl:Open(index, param_t)
	self.view:Open(index,param_t)

	if param_t ~= nil and param_t.sub_view_name == SubViewName.Explain then
		self:OpenExplainView()
	end

	if param_t ~= nil and param_t.sub_view_name == SubViewName.TiQin then
		self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_LOVER_INFO_REQ)
		self:OpenTiQinView()
	end

	self.SendQingyuanReqEquipInfo()
	self:SendQingyuanReqInfo()
end

function MarryWGCtrl:CloseSelf()
	self.view:Close()
end

function MarryWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCMarryReqRoute, "OnMarryReqRoute")
	self:RegisterProtocol(SCMarrySpecialEffect, "OnMarrySpecialEffect")
	self:RegisterProtocol(SCQingyuanEquipInfo, "OnQingyuanEquipInfo")
	self:RegisterProtocol(SCQingyuanInfo, "OnQingyuanInfo")
	self:RegisterProtocol(SCQingyuanFBInfo, "OnQingyuanFBInfo")
	self:RegisterProtocol(SCRoleMarryInfoChange, "OnRoleMarryInfoChange")
	self:RegisterProtocol(SCQingyuanMateValueSend, "OnQingyuanMateValueSend")
	self:RegisterProtocol(SCMarriageSeekingInfo, "OnMarriageSeekingInfo")
	self:RegisterProtocol(SCQingyuanCardUpdate, "OnQingyuanCardUpdate")
	self:RegisterProtocol(SCQingyuanCoupleHaloInfo, "OnQingyuanCoupleHaloInfo")
	self:RegisterProtocol(SCQingyuanCardLevelList, "OnQingyuanCardLevelList")
	self:RegisterProtocol(SCWeddingScheduleInfo, "OnSCWeddingScheduleInfo")--排期
	self:RegisterProtocol(SCWeddingInviteAckInfo, "OnSCWeddingInviteAckInfo")--主动被邀请
    self:RegisterProtocol(SCMarryRewardLimitInfo, "OnSCMarryRewardLimitInfo")--主动被邀请
    self:RegisterProtocol(SCQingYuanSingleInfo, "OnSCQingYuanSingleInfo")--单身角色信息
    self:RegisterProtocol(SCFlowerScoreUpgradeLevelInfo, "OnSCFlowerScoreUpgradeLevelInfo")--赠花信息
	self:RegisterProtocol(SCRASendFlowerToplistInfo, "OnSCRASendFlowerToplistInfo")--赠花信息
	

	self:RegisterProtocol(CSMarryReq)
	self:RegisterProtocol(CSMarryRet)
	self:RegisterProtocol(CSQingyuanTakeOffEquip)
	self:RegisterProtocol(CSQingyuanUpLevel)
	self:RegisterProtocol(CSQingyuanReqEquipInfo)
	self:RegisterProtocol(CSQingyuanReqInfo)
	self:RegisterProtocol(CSQingyuanBuyJoinTimes)
	self:RegisterProtocol(CSQingyuanMateValueQuery)
	self:RegisterProtocol(CSQingyuanDivorceReqCS)
	self:RegisterProtocol(CSMarriageSeekingInfo)
	self:RegisterProtocol(CSQingyuanCardUpgradeReq)
	self:RegisterProtocol(CSWeddingInviteGustList)

	self:RegisterProtocol(CSQingYuanFBChooseInfo)         --选择心有灵犀

	self:RegisterProtocol(CSUpgradeFlowerScoreLevel)         -- 赠花

	-----------------------手印--------------------------
	self:RegisterProtocol(SCIsAcceptMarry, "OnAcceptMarry")
	self:RegisterProtocol(SCMarryRetInfo, "OnMarryRetInfo")         --选择心有灵犀

	-----------------------预约--------------------------
	self:RegisterProtocol(SCQingYuanAllInfo, "OnQingYuanAllInfo")
	self:RegisterProtocol(SCHunYanCurWeddingAllInfo, "OnHunYanCurWeddingAllInfo")
	self:RegisterProtocol(SCWeddingApplicantInfo, "OnWeddingApplicantInfo")
	self:RegisterProtocol(SCWeddingRoleInfo, "OnWeddingRoleInfo")
	self:RegisterProtocol(CSQingYuanOperaReq)

	self:RegisterProtocol(SCQingYuanWeddingAllInfo, "OnQingYuanWeddingAllInfo")
	-----------------------------------------------------

	------------------征婚----------------------
	self:RegisterProtocol(SCMarrySeekingReq, "OnMarrySeekingReq")
	self:RegisterProtocol(CSMarrySeekingReq)

	------------------宝匣----------------------
	self:RegisterProtocol(SCLoveboxFetchReward, "OnLoveboxFetchReward")
	self:RegisterProtocol(CSLoveboxBuyReq)
	self:RegisterProtocol(CSLoveboxFetchReward)

	------------------宝宝----------------------
	self:RegisterProtocol(SCBabyAllInfo, "OnBabyAllInfo")
	self:RegisterProtocol(SCBabyInfo, "OnBabyInfo")
	self:RegisterProtocol(CSBabyOperaReq)
	------------------宝宝----------------------

	------------------点唇----------------------
	self:RegisterProtocol(CSInviteLoverReq)
	self:RegisterProtocol(CSDianChunReq)
	self:RegisterProtocol(CSCoupleDianChunGetRewardReq)
	self:RegisterProtocol(CSCoupleDianChunCloseReq)
	self:RegisterProtocol(SCInviteLoverInfo, "OnInviteLoverInfo")
	self:RegisterProtocol(SCCoupleDianChunInfo, "OnCoupleDianChunInfo")
	self:RegisterProtocol(SCCoupleDianChunGetRewardInfo, "OnCoupleDianChunGetRewardInfo")
	self:RegisterProtocol(SCCoupleDianChunCloseInfo, "OnCoupleDianChunCloseInfo")


	------------------印记----------------------
	--self:RegisterProtocol(SCLoveStampImgInfo, "OnLoveStampImgInfo")
	------------------印记----------------------
	self:RegisterProtocol(CSQingyuanCoupleHaloOperaReq)

	---------------------表白墙-----------------------
	self:RegisterProtocol(CSProfessWallReq)
	self:RegisterProtocol(SCProfessLevelInfo, "OnProfessLevelInfo")

	---------------------祝福-----------------------
	self:RegisterProtocol(CSMarryWish) --8496协议祝福新人
	self:RegisterProtocol(SCMarryNotic, "OnSCMarryNotic") --8495协议结婚通知好友和盟友

	---------------------结婚预告--------------------------------
	self:RegisterProtocol(SCQingYuanNoticeChange, "OnSCQingYuanNoticeChange")
	self:RegisterProtocol(CSQingYuanNoticeFetchReward)

	---------------------情缘聊一聊---------------------
	self:RegisterProtocol(CSLiaoYiLiaoOper)
	self:RegisterProtocol(SCLiaoYiLiaoObjInfo, "OnSCLiaoYiLiaoObjInfo")
	self:RegisterProtocol(SCLiaoYiLiaoInteraction, "OnSCLiaoYiLiaoInteraction")
	self:RegisterProtocol(SCLiaoYiLiaoRoleStatusChange, "OnSCLiaoYiLiaoRoleStatusChange")

	----------------------------推荐情缘-----------------------
	self:RegisterProtocol(CSGetRandomSingleRoleList)
	self:RegisterProtocol(SCRandomSingleRoleList, "OnSCRandomSingleRoleList")

	----------------------------寻缘-----------------------
	self:RegisterProtocol(CSQingYuanZhengHunReq)
	self:RegisterProtocol(SCQingYuanZhengHunInfo, "OnSCQingYuanZhengHunInfo")
end

function MarryWGCtrl:OnSCLiaoYiLiaoObjInfo(protocol)
	self.data:OnSCLiaoYiLiaoObjInfo(protocol)
	if protocol.role_id > 0 then
		AvatarManager.Instance:SetAvatarKey(protocol.role_id, protocol.avatar_key_big, protocol.avatar_key_small)
	end
end

function MarryWGCtrl:OnSCLiaoYiLiaoInteraction(protocol)
	--print_error("~~~~~~~ MarryWGCtrl.OnSCLiaoYiLiaoInteraction", protocol)
	self.data:OnSCLiaoYiLiaoInteraction(protocol)
end


function MarryWGCtrl:OnSCLiaoYiLiaoRoleStatusChange(protocol)
	--print_error("~~~~~~~ MarryWGCtrl.OnSCLiaoYiLiaoRoleStatusChange", protocol)
	self.data:OnSCLiaoYiLiaoRoleStatusChange(protocol)
end

function MarryWGCtrl:RemindMarryXunyouTime(end_time)
    if CountDownManager.Instance:HasCountDown("xunyou_steady") then
        CountDownManager.Instance:RemoveCountDown("xunyou_steady")
    end

    if end_time - 5 > TimeWGCtrl.Instance:GetServerTime() then
        CountDownManager.Instance:AddCountDown("xunyou_steady",
        nil, BindTool.Bind1(self.CompleteTime, self), end_time - 5, nil, 1)
    elseif end_time > TimeWGCtrl.Instance:GetServerTime() then
        self.inter = end_time - TimeWGCtrl.Instance:GetServerTime()
        self:CompleteTime()
    end
end

function MarryWGCtrl:CompleteTime()
    if Scene.Instance:GetSceneType() ~= SceneType.Common then
        return
    end
    if not self.xunyou_remind_alert then
        self.xunyou_remind_alert = MarryAlert.New()
    end
    if CountDownManager.Instance:HasCountDown("xunyou_alert_timer") then
        CountDownManager.Instance:RemoveCountDown("xunyou_alert_timer")
    end
    self.xunyou_remind_alert:UseOne()
    local cur_total_time = self.inter and (self.inter - 1) or 4
    self.xunyou_remind_alert:SetLableString(Language.Marry.XunyouReadyDes)
    self.xunyou_remind_alert:SetOkString(string.format(Language.Marry.XunyouDaojishi, cur_total_time))
    self.xunyou_remind_alert:Open()
    CountDownManager.Instance:AddCountDown("xunyou_alert_timer", function(elapse_time, total_time) 
        self.xunyou_remind_alert:SetOkString(string.format(Language.Marry.XunyouDaojishi, math.floor(total_time - elapse_time)))
    end,
    function()
        self.inter = nil
        self.xunyou_remind_alert:Close()
    end, nil, cur_total_time + 1, 1)
end


function MarryWGCtrl:ActivityChangeCallback(activity_type, status, next_status_switch_time, open_type,param_1)
	if activity_type ~= ACTIVITY_TYPE.HUNYAN then
		return
    end

	WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_WEDDING_INFO)
	if status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU then
		local flag = 2
		self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_OBJ_POS, flag)
		if self.first_flat then
			ViewManager.Instance:CloseAll()
			self.first_flat = false
        end
        if Scene.Instance:GetSceneType() == SceneType.Common then
        else
            self:ShowXunyouAlert()
        end
    elseif status == ACTIVITY_STATUS.STANDY then --准备巡游
        if param_1 == 2 then --表示有巡游的婚宴
            if Scene.Instance:GetSceneType() == SceneType.Common then
                self:RemindMarryXunyouTime(next_status_switch_time)
            end
        end
	elseif status == ACTIVITY_STATUS.CLOSE then
		ViewManager.Instance:Close(GuideModuleName.Marry_DeMandView)
	else
		self.first_flat = true
		self:CloseXunYouView()
		self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_ROLE_INFO)
	end

	local hunyan_info = self.data:GetCurWeddingInfo()
	if status == ACTIVITY_STATUS.OPEN and not IsEmptyTable(hunyan_info) then
		local vo = Scene.Instance:GetObjByUId(hunyan_info.role_id)
		local lover_vo = Scene.Instance:GetObjByUId(hunyan_info.lover_role_id)
		if vo then
			vo:SetMarryFlag(0)
		end
		if lover_vo then
			lover_vo:SetMarryFlag(0)
		end
	end
end

function MarryWGCtrl:DayChange()
    MarryWGData.Instance:DayChange()
	if self.marry_yuyue:IsOpen() then
		self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_FLAG)
	end
end

function MarryWGCtrl:OpenNearbySingleView()
	ViewManager.Instance:Open(GuideModuleName.MarryNearbySingle)
	-- self.nearbysingleview:Open()
end
-- 请求选择心有灵犀
function MarryWGCtrl:SendCSQingYuanFBChooseInfo(choose_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingYuanFBChooseInfo)
	protocol.choose_index = choose_index or 0
	protocol:EncodeAndSend()
end

function MarryWGCtrl:CheckMarryRemind(remind_id)
	return 0
end

-- 仙侣加入队伍
function MarryWGCtrl:LoverJoinTeam()
	self:Flush(MARRY_TAB_TYPE.FB, "team_change")
end

function MarryWGCtrl:LeaveTeam()
	self:Flush(MARRY_TAB_TYPE.FB, "team_out")
end

-- 角色结婚信息改变
function MarryWGCtrl:OnRoleMarryInfoChange(protocol)
	local obj_id = protocol.obj_id
	if obj_id == Scene.Instance.main_role:GetObjId() then-- 如果是自己
		local obj = Scene.Instance.main_role
		if obj then
			-- 策划需求，玩家婚姻状态变更时需要检查并更新变强按钮“仙侣赠送”状态
			-- if protocol.lover_uid ~= RoleWGData.Instance:GetLoverRoleId() then
			-- 	local has_gift, has_flower, _, flower_item_id = ItemWGData.Instance:CheckHasScoietyGiftItem()
			-- 	if has_gift or has_flower then
			-- 		MarryWGData.Instance:SetSocietyGiftOpened(false)
			-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, 1, function()
			-- 			if has_gift then
			-- 				--ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
			-- 			else
			-- 				local t = flower_item_id ~= 0 and {item_id = flower_item_id} or nil
			-- 				FlowerWGCtrl.Instance:Open(t)
			-- 			end
			-- 			MarryWGData.Instance:SetSocietyGiftOpened(true)
			-- 			return SocietyWGData.Instance:GetIsLoverOnline() or not MarryWGData.Instance:GetIsSocietyGiftOpened()
			-- 		end)
			-- 	else
			-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, 0)
			-- 	end
			-- end

			RoleWGData.Instance:SetAttr("lover_uid", protocol.lover_uid)
			RoleWGData.Instance:SetAttr("lover_name", protocol.lover_name)
			RoleWGData.Instance:SetAttr("last_marry_time", protocol.last_marry_time)
		end
	else
		for k,v in pairs(Scene.Instance.obj_list) do
			if v:GetObjId() == obj_id then
				v:GetVo().lover_name = protocol.lover_name or ""
			end
		end
	end
	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if obj then
		obj:SetAttr("lover_uid", protocol.lover_uid)
	end
	self:Flush(MARRY_TAB_TYPE.HJ)
	self:Flush(MARRY_TAB_TYPE.JH)

	ViewManager.Instance:FlushView(GuideModuleName.MarryExplain)
end

function MarryWGCtrl:CloseFbChoose()
    if self.fb_choose:IsOpen() then
        self.fb_choose:Close()
    end
end

-- 情缘副本场景信息下发
function MarryWGCtrl:OnQingyuanFBInfo(protocol)
	self.data:SetQyFbSceneInfo(protocol)
	if self.fb_task:IsOpen() then
		self.fb_task:Flush()
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic and scene_logic.SetStartTimeCutDown then
		scene_logic:SetStartTimeCutDown()
	end
	--print_error( "choose_end_timestamp", protocol.choose_end_timestamp , protocol.is_pass, " protocol.is_couple", protocol.is_couple,  protocol.is_choose_finish)
	if protocol.choose_end_timestamp > 0 and protocol.is_pass == 1 and protocol.is_couple == 1 and protocol.is_choose_finish == 0 then --打赢了，还没选完
		self:OpenChooseView()
	elseif (protocol.is_couple == 1 and protocol.is_pass == 0 and protocol.is_choose_finish == 1) --失败了， 选完了
			or(protocol.is_couple == 0 and protocol.is_pass == 0 and protocol.is_finish == 1 ) then --失败不需要选定	
		FuBenWGCtrl.Instance:OpenLose(SceneType.QingYuanFB)
	elseif protocol.is_couple == 1 and protocol.is_pass == 0 and protocol.is_choose_finish == 0 then	--失败了，还没选完
		return
	elseif (protocol.is_couple == 1  and protocol.is_pass == 1 and protocol.is_choose_finish == 1) --打赢了，选完了
		or (protocol.is_couple == 0  and protocol.is_pass == 1) then --打赢了，不需要选定
		if self.fb_choose:IsOpen() then
			self.fb_choose:Close()
		end

		local other_cfg = self.data:GetMarryOtherCfg()
		local fb_data_list = protocol.drop_item_list
		-- local fb_tongguan_list = other_cfg.pass_fb_item
		local fb_data = {}
		local is_eyetoeye = 0 
		if 0 == protocol.is_helper then
			if protocol.is_same == 1 and 1 == protocol.is_couple then
				--fb_data_list = other_cfg.lovers_fb_item1 --s双倍
				is_eyetoeye = 1
			else
				--fb_data_list = other_cfg.lovers_fb_item2 --单倍
			end
			--数据处理
			local index = 1
			for k,v in pairs(fb_data_list) do
				fb_data[index] = {}
				fb_data[index].item_id = v.item_id
				fb_data[index].num = v.num
				fb_data[index].is_bind = v.is_bind
				fb_data[index].is_eyetoeye = is_eyetoeye
				index = index + 1
			end
			-- for k,v in pairs(fb_tongguan_list) do
			-- 	fb_data[index] = {}
			-- 	fb_data[index].item_id = v.item_id
			-- 	fb_data[index].num = v.num
			-- 	fb_data[index].is_bind = v.is_bind
			-- 	fb_data[index].is_eyetoeye = 0
			-- 	index = index + 1
			-- end
		end
		local is_show_win = true
		if 1 == protocol.is_helper and protocol.assist_time <= 0 then
			is_show_win = false
		end
		if is_show_win then
			--self.data:SetCommonWinData(SceneType.QingYuanFB, fb_data, protocol.get_exp)
			--self:SetCommonWinData()
			FuBenWGCtrl.Instance:OpenWin(SceneType.QingYuanFB, fb_data, protocol.get_exp, 0, 0, 10)
		else
			--没有助战次数也要打开界面
			FuBenWGCtrl.Instance:OpenWin(SceneType.QingYuanFB, {}, 0, 0, 0, 10)
		end
	end
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamBaoMingEnterView)
end

function MarryWGCtrl:SetCommonWinData()
	local data = self.data:GetCommonWinData()
	local scene_type = Scene.Instance:GetSceneType()

	if IsEmptyTable(data) then
		FuBenWGCtrl.Instance:OpenLose(SceneType.QingYuanFB)
	else
		FuBenWGCtrl.Instance:OpenWin(data.type, data.data, data.exp, 0, 0, 10)
	end
end

-- 情缘副本基本信息下发
function MarryWGCtrl:OnQingyuanInfo(protocol)
	self.data:SetQyFbInfo(protocol)
	self:Flush(MARRY_TAB_TYPE.HJ)
	self:Flush(MARRY_TAB_TYPE.JH)
	self:Flush(MARRY_TAB_TYPE.FB)

	RemindManager.Instance:Fire(RemindName.Marry_Copy)
	HomesWGCtrl.Instance:FlushHomesView()
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamBaoMingEnterView)
	NewTeamWGCtrl.Instance:FLushPrePareTimes()
end

-- 伴侣情缘值下发
function MarryWGCtrl:OnQingyuanMateValueSend(protocol)
	self.data:SetMateQyValue(protocol.mate_qingyuan_value)
end

--千里寻姻缘
function MarryWGCtrl:OnMarriageSeekingInfo(protocol)
	self.data:SetMarriageSeekingInfo(protocol)
end

-- 情缘卡牌等级变更通知   协议下发监听
function MarryWGCtrl:OnQingyuanCardUpdate(protocol)
	self.data:SetQingyuanCardUpdate(protocol)
	self:Flush({MARRY_TAB_TYPE.QYSD})
	-- self:FlushHolyView()
end


-- 情缘卡牌等级列表下发监听  协议下发监听
function MarryWGCtrl:OnQingyuanCardLevelList(protocol)
	self.data:SetQingyuanCardLevelList(protocol)
	self:Flush(MARRY_TAB_TYPE.QYSD)
	-- self:FlushHolyView()
end

-- 征婚信息下发
function MarryWGCtrl:OnMarrySeekingReq(protocol)
	self.data:SetMarrySeekingReq(protocol)
end

-- 宝匣领取信息下发
function MarryWGCtrl:OnLoveboxFetchReward(protocol)
	self.data:SetLoveboxFetchReward(protocol)
	self:Flush(MARRY_TAB_TYPE.JH)

	if self.marry_baoxia_view then
		self.marry_baoxia_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.Marry_BaoXia)
	RemindManager.Instance:Fire(RemindName.Marry_GetMarry)
	HomesWGCtrl.Instance:FlushHomesView()
	self.data:IsShowLongHunWay()
	if protocol.accu_day > 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.LingQuMyRewardHint,protocol.accu_day))
	end
	if protocol.lover_accu_day > 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.LingQuMyLoverRewardHint,protocol.lover_accu_day))
	end
end

-- 请求情缘副本基本信息
function MarryWGCtrl:SendQingyuanReqInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanReqInfo)
	protocol:EncodeAndSend()
end

-- 购买情缘副本进入次数
function MarryWGCtrl:SendQingyuanBuyJoinTimes()
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanBuyJoinTimes)
	protocol:EncodeAndSend()
end

-- 请求伴侣情缘值查询
function MarryWGCtrl.SendQingyuanMateValueQuery()
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanMateValueQuery)
	protocol:EncodeAndSend()
end

-- 请求离婚协议
function MarryWGCtrl.SendCSQingyuanDivorceReqCS()
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanDivorceReqCS)
	protocol:EncodeAndSend()
end

function MarryWGCtrl:SendLoveboxBuyReq(opera_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLoveboxBuyReq)
	protocol.opera_type = opera_type or 0
	protocol:EncodeAndSend()
end


function MarryWGCtrl:SendLoveboxFetchReward(fetch_reward_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLoveboxFetchReward)
	protocol.fetch_reward_type = fetch_reward_type or 0
	protocol:EncodeAndSend()
end

function MarryWGCtrl:SendMarrySeekingReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSMarrySeekingReq)
	protocol:EncodeAndSend()
end

MarriageSeekingType = {
	MARRIAGE_SEEKING_TYPE_INFO = 1, --请求信息
	MARRIAGE_SEEKING_TYPE_INSERT = 2, --发布征婚
}
--千里寻缘，请求信息以及发布征婚
function MarryWGCtrl.SendMarriageSeekingInfo(type, marriage_seeking_notice)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMarriageSeekingInfo)
	protocol.type = type
	protocol.marriage_seeking_notice = marriage_seeking_notice or ""
	protocol:EncodeAndSend()
	Log("SendMarriageSeekingInfo-->>", marriage_seeking_notice)
end

-- 请求升级情缘卡牌
function MarryWGCtrl:SendCSQingyuanCardUpgradeReq(card_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanCardUpgradeReq)
	protocol.card_id = card_id
	protocol:EncodeAndSend()
end

function MarryWGCtrl:OpenProposeView(lover_id, str, marry_type)
	str = str or ""
	if lover_id and lover_id ~= 0 then
		self.qiuhun:SetData(lover_id, str, marry_type)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryNoLoverTips)
		return
	end
	ViewManager.Instance:Open(GuideModuleName.MarryPropose)
	-- self.qiuhun:Open()
end

function MarryWGCtrl:OpenProposeBackView()
	if self.qiuhun_back:IsOpen() then
		self.qiuhun_back:Flush()
	else
		ViewManager.Instance:Open(GuideModuleName.MarryProposeBack)
	end
end

-- 求婚信息转发给对方
function MarryWGCtrl:OnMarryReqRoute(protocol)
	MarryWGData.ProposeInfo.marry_type = protocol.marry_type or 0
	MarryWGData.ProposeInfo.req_uid = protocol.req_uid or 0
    MarryWGData.ProposeInfo.GameName = protocol.GameName or ""
    MarryWGData.ProposeInfo.shizhuang_photoframe = protocol.shizhuang_photoframe or 0
    MarryWGData.ProposeInfo.prof = protocol.prof or ""
    MarryWGData.ProposeInfo.sex = protocol.sex or ""
    MarryWGData.Instance:SetCurLoverName(protocol.GameName)

	MarryWGData.ProposeInfo.lover_marry_type_flag = bit:d2b(protocol.lover_marry_type_flag) or {}
	MarryWGData.ProposeInfo.marry_reward_limit_item_id = protocol.marry_reward_limit_item_id or {}
	self:OpenProposeBackView()
end

-- 结婚特效
function MarryWGCtrl:OnMarrySpecialEffect(protocol)
	local is_all_flower_effect = false
	local duration = 7
	local asset_name = nil

	if 0 == protocol.marry_type then      	--99   双方 纯色
		asset_name = Ui_Effect.UI_songhuabian_homeigu

	elseif 1 == protocol.marry_type then      --520 全服 纯色
		asset_name = Ui_Effect.UI_songhuaxinxing_hong

	elseif 2 == protocol.marry_type then		--999 全服 双色
		is_all_flower_effect = true
		asset_name = Ui_Effect.UI_songhua999
	end

	if asset_name and not SettingWGData.Instance:GetSettingData(SETTING_TYPE.FLOWER_EFFECT) then
		local bundle, asset = ResPath.GetEffectUi(asset_name)
		FlowerWGCtrl.Instance:PlayerEffect(bundle, asset, duration)
		-- if is_all_flower_effect then
		-- 	bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_songhuaxinxing_hong)
		-- 	FlowerWGCtrl.Instance:PlayerEffect(bundle, asset, duration)
		-- 	bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_songhuabian_homeigu)
		-- 	FlowerWGCtrl.Instance:PlayerEffect(bundle, asset, duration)
		-- end
	end

	--print_error("播放结婚音效")
end

-- 情缘装备信息
function MarryWGCtrl:OnQingyuanEquipInfo(protocol)
	self.data:SetRingInfo(protocol)
	self:Flush(MARRY_TAB_TYPE.HJ)

	RemindManager.Instance:Fire(RemindName.Marry_GetMarry)
	RemindManager.Instance:Fire(RemindName.Marry_Equip)
	HomesWGCtrl.Instance:FlushHomesView()
	self.data:IsShowLongHunWay()
end

-- 请求结婚
function MarryWGCtrl:SendMarryReq(marry_operator, marry_type, target_uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMarryReq)
	protocol.ope_type = marry_operator
	protocol.marry_type = marry_type or 0
	protocol.target_uid = target_uid or 0
	protocol:EncodeAndSend()
end

-- 求婚回复
function MarryWGCtrl:SendMarryRet(is_accept)
	if nil == is_accept then return end
	local protocol = ProtocolPool.Instance:GetProtocol(CSMarryRet)
	protocol.marry_type =  MarryWGData.ProposeInfo.marry_type
	protocol.req_uid = MarryWGData.ProposeInfo.req_uid
	protocol.is_accept = is_accept
	protocol:EncodeAndSend()
end

-- 情缘装备进阶
function MarryWGCtrl:SendQingyuanUpQuality(slot)
	-- if nil == slot then return end
	-- local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanUpQuality)
	-- protocol.slot = slot
	-- protocol:EncodeAndSend()
end

-- 情缘装备加经验
function MarryWGCtrl:SendQingyuanUpLevel(item_id, slot)
	-- if nil == item_id then return end

	-- local equip_cfg = self.data:GetEquipCfgById(item_id)
	-- if (nil == slot or -1 == slot) and equip_cfg then
	-- 	slot = equip_cfg.slot_idx
	--  end

	-- local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanUpLevel)
	-- protocol.slot =  slot
	-- protocol.stuff_id = item_id
	-- protocol:EncodeAndSend()
end

-- 情缘装备信息请求
function MarryWGCtrl.SendQingyuanReqEquipInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanReqEquipInfo)
	protocol:EncodeAndSend()
end

function MarryWGCtrl:MainuiOpenCreate()
	self.SendQingyuanReqEquipInfo()
	self:SendQingyuanReqInfo()
	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.MarryNotice)
	self.first_enter_game = false
	if is_open then
		local cur_states = self.data:GetCurNoticeStates()
		if cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_END or cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_INVAILD then
			MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnMarryNotice", false)
		end
	end
	self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_ROLE_INFO)
	self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.HUNYAN_GET_APPLICANT_INFO, 0, 0)
	self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_GET_YUYUE_INFO) 
	self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_REWARD_LIMIT_INFO)
	-- 请求印记
	-- self:SendLoveStampUpStarReq(LoveStampOperaType.LOVE_STAMP_OPERA_TYPE_ALL_INFO)

	-- WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_APPLICANT_INFO, 0, 0)
	--BrowseWGCtrl.Instance:BrowRoelInfo(RoleWGData.Instance.role_vo.role_id, BindTool.Bind1(self.ChangeLover, self))
end

function MarryWGCtrl:ChangeLover(protocol)
	self.data:SetQiyuanCardInfo(protocol)
	self:Flush({MARRY_TAB_TYPE.QYSD})
end

----------------by bzw, modify please
--升级情缘装备
function MarryWGCtrl:SendUpLevelEquip(item_id, equip_index, is_auto_buy, is_auto_up, repeat_times)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanUpLevel)
	protocol.stuff_id = item_id
	protocol.equip_index = equip_index
	protocol.is_autobuy = is_auto_buy
	protocol.repeat_times = repeat_times or 1
	protocol:EncodeAndSend()
end

function MarryWGCtrl:SendTakeOffEquip(equip_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanTakeOffEquip)
	protocol.equip_index = equip_index
	protocol:EncodeAndSend()
end

--protocol.result --作废
function MarryWGCtrl:OnRingResult(result)
	-- print_error("protocol.result", result)
	-- if 0 == result then
	-- 	if self.view.is_auto_up then
	-- 		self.view.is_auto_up = false
	-- 		self:Flush(MARRY_TAB_TYPE.HJ)
	-- 	end
	-- elseif 1 == result and self.view.is_auto_up then
	-- 	self.view:AutoMarryGradeUpOnce()
	-- end
end

------------------宝宝协议----------------------------------------------------
-- 协议请求
function MarryWGCtrl:SendBabyOperaReq(opera_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBabyOperaReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

function MarryWGCtrl:OpenFriendView()
	ViewManager.Instance:Open(GuideModuleName.MarryFriend)
	-- self.friend_view:Open()
end

function MarryWGCtrl:SetIntimacyData(intimacy, name)
	self.marry_intimacy:SetIntimacyView(intimacy, name)
end

function MarryWGCtrl:OpenFbTaskView()
	ViewManager.Instance:Open(GuideModuleName.MarryFbTaskView)
	-- self.fb_task:Open()
end

function MarryWGCtrl:CloseFbTaskView()
	self.fb_task:Close()
end

function MarryWGCtrl:OpenChooseView()
	ViewManager.Instance:Open(GuideModuleName.MarryFbChoose)
	-- self.fb_choose:Open()
end

function MarryWGCtrl:OnBabyAllInfo(protocol)
	self.data:SetBabyTypeList(protocol)
	self:FlushBabyFromProtocol()
	self:Flush(MARRY_TAB_TYPE.JH)
	RemindManager.Instance:Fire(RemindName.Marry_Baby)
	RemindManager.Instance:Fire(RemindName.Marry_GetMarry) --结婚
	HomesWGCtrl.Instance:FlushHomesView()
	self.data:IsShowLongHunWay()
end

function MarryWGCtrl:FlushBabyFromProtocol()
    self:Flush(MARRY_TAB_TYPE.BB,"is_from_protocol")
end


function MarryWGCtrl:OnBabyInfo(protocol)
	self.data:SetOneBabyInfo(protocol)
	self:FlushBabyFromProtocol()
	RemindManager.Instance:Fire(RemindName.Marry_Baby)
	HomesWGCtrl.Instance:FlushHomesView()
	self.data:IsShowLongHunWay()
end

-- 宝宝进阶结果返回 --已作废
function MarryWGCtrl:OnBabyUpgradeResult(result, index)

end

------------------宝宝协议----------------------------------------------------

------------------点唇协议----------------------------------------------------
-- 邀请伴侣
function MarryWGCtrl:SendInviteLoverReq(type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSInviteLoverReq)
	protocol.type = type
	protocol:EncodeAndSend()
end

-- 点唇请求
function MarryWGCtrl:SendDianChunReq(type, param)
	if 0 == type and 0 == param then
		self.is_self_buy_dianchun_num = true
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSDianChunReq)
	protocol.type = type
	protocol.param = param or 0
	protocol:EncodeAndSend()
end

-- 领取奖励请求
function MarryWGCtrl:SendCoupleDianChunGetRewardReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCoupleDianChunGetRewardReq)
	protocol:EncodeAndSend()
end

-- 夫妻点唇关闭请求
function MarryWGCtrl:SendCoupleDianChunCloseReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCoupleDianChunCloseReq)
	protocol:EncodeAndSend()
end

-- 邀请确认
function MarryWGCtrl:OnInviteLoverInfo(protocol)
	if 0 == protocol.type then
		if nil == self.dianchun_invite_alert then
			self.dianchun_invite_alert = Alert.New(string.format(Language.Marry.DianChunAlert, RoleWGData.Instance.role_vo.lover_name), BindTool.Bind(self.OnClickInviteOK, self), BindTool.Bind(self.SendInviteLoverReq, self, 3), BindTool.Bind(self.SendInviteLoverReq, self, 3))
			self.dianchun_invite_alert:SetAutoCloseTime(5)
		end
		self.dianchun_invite_alert:Open()
	elseif 2 == protocol.type then
		--self.data:SetFereDianChunStatus(1)
		--self:Flush(MARRY_TAB_TYPE.DC, "dianchun_fere_status")
	elseif 3 == protocol.type then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.CancelTips)
	end
end

function MarryWGCtrl:OnClickInviteOK()
	self:SendInviteLoverReq(2)
	FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_dianchun")
end

function MarryWGCtrl:OnDianChunCDEndHandler()
	if not self.view:IsOpen() then
		if nil == self.dianchun_cd_end_alert then
			self.dianchun_cd_end_alert = Alert.New(Language.Marry.DianChunCDEndTips, BindTool.Bind(self.OnClickCDEndOK, self))
			self.dianchun_cd_end_alert:SetOkString(Language.Marry.XianZaiJiuQu)
			self.dianchun_cd_end_alert:SetCancelString(Language.Marry.ShaoHouJiuQu)
			self.dianchun_cd_end_alert:SetAutoCloseTime(10)
		end
		self.dianchun_cd_end_alert:Open()
	end
end

function MarryWGCtrl:OnClickCDEndOK()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_dianchun")
end

-- 夫妻点唇信息
function MarryWGCtrl:OnCoupleDianChunInfo(protocol)
	GlobalTimerQuest:CancelQuest(self.dianchun_cd_timer_quest)
	local cd_remain_time = protocol.couple_dianchun_cd_time_stamp - TimeWGCtrl.Instance:GetServerTime()
	if cd_remain_time > 0 then
		self.dianchun_cd_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind2(self.OnDianChunCDEndHandler, self, true), cd_remain_time)
	end

	self.data:SetCoupleDianChunInfo(protocol)
	if not self.view:IsOpen() then
		return
	end
	self:Flush(MARRY_TAB_TYPE.DC)
	--self:Flush(MARRY_TAB_TYPE.DC, "dianchun_fere_status")
	RemindManager.Instance:Fire(RemindName.Marry_DianChun)
	HomesWGCtrl.Instance:FlushHomesView()
	local function fun()
		if 0 ~= protocol.self_dice_data[1] and self.on_click_dian_chun then
			self.on_click_dian_chun = false
			self:Flush(MARRY_TAB_TYPE.DC, "flush_self_dice_data") --旋转色子
		end
		if 0 ~= protocol.qingyuan_dice_data[1] and protocol.qingyuan_dianchun_flage > 0 then
			self:Flush(MARRY_TAB_TYPE.DC, "flush_fere_dice_data")
		end
	end

	if self.dianchun_view_close then
		if 0 ~= protocol.self_dice_data[1] then
			self:Flush(MARRY_TAB_TYPE.DC, "flush_self_dice_data") --旋转色子
		end
		if 0 ~= protocol.qingyuan_dice_data[1] then
			self:Flush(MARRY_TAB_TYPE.DC, "flush_fere_dice_data")
		end
		self.dianchun_view_close = false
	else
		fun()
	end

	-- if -1 ~= self.cur_buy_dianchun_num and self.cur_buy_dianchun_num ~= protocol.buy_couple_dianchun_count and protocol.reason == 1 then
	-- 	if protocol.role_id and protocol.role_id == RoleWGData.Instance.role_vo.role_id then
	-- 		ChatWGCtrl.Instance:SendSingleChat(RoleWGData.Instance.role_vo.lover_uid, string.format(Language.Marry.FereBuyNumTips, RoleWGData.Instance.role_vo.lover_name), CHAT_CONTENT_TYPE.TEXT)
	-- 	end
	-- end

	self.is_self_buy_dianchun_num = false
	self.cur_buy_dianchun_num = protocol.buy_couple_dianchun_count
end

function MarryWGCtrl:SetCloseDianChunViewFlag() --关闭点唇界面
	self.dianchun_view_close = true
end

function MarryWGCtrl:OnClickDianChun()
	self.on_click_dian_chun = true
end

-- 领取奖励信息
function MarryWGCtrl:OnCoupleDianChunGetRewardInfo(protocol)
	self.data:SetCoupleDianChunRewardInfo(protocol)
	self:Flush(MARRY_TAB_TYPE.DC)
	RemindManager.Instance:Fire(RemindName.Marry_DianChun)
	HomesWGCtrl.Instance:FlushHomesView()
	-- print_error("点唇领取奖励后特效如果触发双倍时的特效")
	if 2 == protocol.double_reward then
		if self.view:IsOpen() then
			-- 没看 直抄放烟火
			--TODO 播放烟火

		end
	end
end

-- 夫妻点唇关闭信息
function MarryWGCtrl:OnCoupleDianChunCloseInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Marry_DianChun)
	HomesWGCtrl.Instance:FlushHomesView()
	self.data:SetFereDianChunStatus(0)
	--self:Flush(MARRY_TAB_TYPE.DC, "dianchun_fere_status")
end
------------------点唇协议----------------------------------------------------

------------------寻缘协议----------------------------------------------------
-- 寻缘操作请求
function MarryWGCtrl:SendXunYuanOperateReq(operate_type, param1, param2, msg_length, content)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingYuanZhengHunReq)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 2
	protocol.param2 = param2 or 0
	protocol.msg_length = msg_length or 0
	protocol.content = content or ""
	protocol:EncodeAndSend()
end

function MarryWGCtrl:OnSCQingYuanZhengHunInfo(protocol)
	self.data:SetAllXunYuanInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ProfessWallView)
end
------------------寻缘协议----------------------------------------------------

---------------------夫妻特效---------------
-- function MarryWGCtrl:OpenCoupleHaloView()
-- 	self.couple_halo_view:Open()
-- end

function MarryWGCtrl:OnQingyuanCoupleHaloInfo(protocol)
	self.data:SetQingyuanCoupleHaloInfo(protocol)
	self:Flush(math.floor(TabIndex.marry_couple_halo), "couple_halo")
end

--请求夫妻光环特效
function MarryWGCtrl.SendQingyuanCoupleHaloOperaReq(param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingyuanCoupleHaloOperaReq)
	protocol.req_type = param.req_type or QINGYUAN_COUPLE_HALO_REQ_TYPE.QINGYUAN_COUPLE_REQ_TYPE_INFO
	protocol.param_1 = param.param_1 or 0
	protocol.param_2 = param.param_2 or 0
	protocol.param_3 = param.param_3 or 0
	protocol:EncodeAndSend()
end


function MarryWGCtrl:Flush(tab_index,key,value)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.Marry, tab_index) then
		self.view:Flush(tab_index,key,value)
	end
end

----------------------------------------------

------------------印记----------------------
--function MarryWGCtrl:OnLoveStampSendAllInfo(protocol)
--	self.view.is_upgrading = false
--	self.data:SetLoveMarkAllInfo(protocol)
--	self:Flush(MARRY_TAB_TYPE.YJ)
--	self.view.is_first_open = false
--
--end

---- 附近的人有印记改变的发的广播
--function MarryWGCtrl:OnLoveStampImgInfo(protocol)
--	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
--	local mainrole = Scene.Instance:GetMainRole()
--	if protocol.obj_id == mainrole.obj_id then
--		mainrole:SetAttr("love_stamp_img_id", protocol.img_id)
--		self:Flush({MARRY_TAB_TYPE.XZYJ})
--	else
--		local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
--		if role then
--			role:SetAttr("love_stamp_img_id", protocol.img_id)
--		end
--	end
--end

function MarryWGCtrl:OnLoveMarkUpGradeResult(result)
	-- self.view:SetUpLoveMarkButtonEnabled(true)
	-- if 0 == result then

	-- elseif 1 == result and self.view:IsOpen() then
	-- 	self:Flush({MARRY_TAB_TYPE.YJ})
	-- end
end

-- function MarryWGCtrl:SendLoveStampUpStarReq(opera_type, param1)
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSLoveStampUpStarReq)
-- 	protocol.type = opera_type or 0
-- 	protocol.param1 = param1 or 0
-- 	protocol:EncodeAndSend()
-- end


------------------(新)结婚----------------------
function MarryWGCtrl:OpenExplainView()
	self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_GET_ROLE_INFO)  -- 玩家信息
	ViewManager.Instance:Open(GuideModuleName.MarryExplain)
end

function MarryWGCtrl:OpenXunYuanView()
	ViewManager.Instance:Open(GuideModuleName.ProfessWallView, TabIndex.profess_wall_xunyuan)
end

function MarryWGCtrl:OpenXunYuanFaBuView()
	ViewManager.Instance:Open(GuideModuleName.MarryXunYuanFaBu)
end

function MarryWGCtrl:SetXunYuanFaBuInfo()
	self.xunyuan_fabu_view:SetXunYuanFaBuInfo()
end

function MarryWGCtrl:SetXunYuanModifyInfo(zhenghun_info)
	self.xunyuan_fabu_view:SetXunYuanModifyInfo(zhenghun_info)
end

function MarryWGCtrl:OpenTiQinView(role_id, role_name, prof, sex)
	-- 征婚者id
	role_id = role_id or 0
	role_name = role_name or ""
	self.tiqin_view:SetSelectRoleId(role_id, role_name, prof, sex)
	ViewManager.Instance:Open(GuideModuleName.MarryTiQin)
end

function MarryWGCtrl:FlushFriendQinMi()
	if self.tiqin_view:IsOpen() then
		self.tiqin_view:Flush()
	end
end

function MarryWGCtrl:OpenYuYueView()
	ViewManager.Instance:Open(GuideModuleName.MarryYuYue)
	-- self.marry_yuyue:Open()
end

function MarryWGCtrl:OpenInviteView(seq)
	if self.invite_view then
		self.invite_view:SetMarryDataInfo(seq)
	end
	-- ViewManager.Instance:Open(GuideModuleName.MarryInvite)
	-- self.invite_view:Open()
end

function MarryWGCtrl:FlushInviteView()
	if self.invite_view:IsOpen() then
		self.invite_view:Flush()
	end
end

function MarryWGCtrl:OpenHunYanTask()
	ViewManager.Instance:Open(GuideModuleName.MarryHunYanTask)
	-- self.hunyan_task:Open()
end

function MarryWGCtrl:CloseHunYanTask()
	self.hunyan_task:Close()
end
function MarryWGCtrl:FlushHunYanTask()
	if self.hunyan_task and self.hunyan_task:IsOpen() then
		self.hunyan_task:Flush()
	end
end

function MarryWGCtrl:OpenMarryTitleView()
	ViewManager.Instance:Open(GuideModuleName.MarryTitleView)
	-- self.marry_title_view:Open()
end

function MarryWGCtrl:OnQingYuanAllInfo(protocol)
	local marry_yuyue_list = {}
	marry_yuyue_list.info_type = protocol.info_type
	marry_yuyue_list.param_ch1 = protocol.param_ch1
	marry_yuyue_list.param_ch2 = protocol.param_ch2
	marry_yuyue_list.param_ch3 = protocol.param_ch3
	marry_yuyue_list.param_ch4 = protocol.param_ch4
	marry_yuyue_list.param2 = protocol.param2
	marry_yuyue_list.role_name = protocol.role_name
	local info_type = marry_yuyue_list.info_type

	if info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_ROLE_INFO then  -- 玩家信息
		self.data:SetYuYueRoleInfo(protocol)
		self.data:SetCurRoleIsTiQin(protocol.param2)
		if (marry_yuyue_list.param_ch1 > 0 and marry_yuyue_list.param_ch2 > 0) or
		   (protocol.param_ch5 > 0 and protocol.param_ch6 > 0) then
			-- self:OpenYuYueView()
		end
		if protocol.param_ch3 == 2 then
			self.marry_yuyue:Flush()
		end

		if self.invite_view:IsOpen() then
			self.invite_view:Flush()
		end

		if self.explain_view:IsOpen() then
			self.explain_view:Flush()
        end
        if self.view:IsOpen() then --刷新预约红点
			self.view:Flush()
        end
        RemindManager.Instance:Fire(RemindName.Marry_GetMarry)
        HomesWGCtrl.Instance:FlushHomesView()
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_GET_BLESSING then

	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_BAITANG_RET then
		if self.marry_flag then
			local lover_name = RoleWGData.Instance.role_vo.lover_name
			if protocol.param_ch1 == 1 then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.BaiTangTips5, lover_name))
			else
				local str = ""
				str = string.format(Language.Marry.BaiTangTips4, lover_name)
				self.marry_baitang_view:SetLableString(str)
				self.marry_baitang_view:SetOkFunc(function()
					WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNUAN_OPERA_TYPE_BAITANG_RET, 1)
				end)

				self.marry_baitang_view:SetCancelFunc(function()
					WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNUAN_OPERA_TYPE_BAITANG_RET, 0)
					self.marry_flag = true
				end)
				ViewManager.Instance:Open(GuideModuleName.MarryBaiTangView)
				self.marry_flag = false
			end
		end
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_WEDDING_YUYUE_FLAG then
		self.data:SetYuYueListInfo(protocol)
		if self.marry_yuyue:IsOpen() then
			self.marry_yuyue:Flush()
        end
        RemindManager.Instance:Fire(RemindName.Marry_GetMarry)
        HomesWGCtrl.Instance:FlushHomesView()
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_YUYUE_RET then
		self.view:OpenYuYueTips(protocol.param_ch1)
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_YUYUE_POPUP then
		MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_FLAG)
		if self.tiqin_view:IsOpen() then
			self.tiqin_view:Close()
		end

	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB_RET then
		self:OpenMarryFbBuyView("lover_buy")
		-- local callback = function()
		-- 	if nil == self.alert_buy_count then
		-- 		self.alert_buy_count = Alert.New(nil, nil, nil, nil, false)
		-- 	end
		-- 	local other_cfg = self.data:GetMarryOtherCfg()
		-- 	local str = string.format(Language.Marry.FbBuyCountTips2, other_cfg.fb_buy_times_gold_cost)
		-- 	self.alert_buy_count:SetLableString(str)
		-- 	self.alert_buy_count:SetOkFunc(function()
		-- 	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB)
		-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BUYFBCOUNT, 0)
		-- 	end)
		-- 	self.alert_buy_count:Open()
		-- 	self.alert_buy_count:SetCancelFunc(function()
 	-- 				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BUYFBCOUNT, 0)
		-- 			end)
		-- 	self.alert_buy_count:SetCloseFunc(function()
 	-- 				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BUYFBCOUNT, 0)
		-- 			end)
		-- end
		-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BUYFBCOUNT, 1, callback)
		-- callback()

	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_YUYUE_SUCC then
		self:OpenInviteView(protocol.param2)
		if self.marry_yuyue:IsOpen() then
			self.marry_yuyue:Close()
		end
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_LOVER_INFO then
		self.data:SetLoverInfo(protocol)
		if self.tiqin_view:IsOpen() then
			self.tiqin_view:Flush()
		end
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_LOVER_TITLE_INFO then
		self.data:SetMarryTitleInfo(protocol.param2)
		if self.marry_title_view:IsOpen() then
			self.marry_title_view:Flush()
		end

		RemindManager.Instance:Fire(RemindName.Marry_GetMarry)
		HomesWGCtrl.Instance:FlushHomesView()
		self:Flush(MARRY_TAB_TYPE.JH)
		self.data:IsShowLongHunWay()

	-- elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_REQ_LOVER_BUY_LOVE_BOX then
	-- 	--请求仙侣购买宝匣
	-- 	local lover_name = RoleWGData.Instance.role_vo.lover_name
	-- 	if nil == self.alert_baoxia then
	-- 		self.alert_baoxia = Alert.New(nil, nil, nil, nil, false)
	-- 	end
	-- 	local str = ""
	-- 		str = string.format(Language.Marry.MarryRequestBaoXia, lover_name)
	-- 		self.alert_baoxia:SetLableString(str)
	-- 		self.alert_baoxia:SetOkFunc(function()
	-- 		MarryWGCtrl.Instance:SendLoveboxBuyReq()
	-- 		end)
	-- 	self.alert_baoxia:Open()
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_WEDDING_BEGIN_NOTICE then
		WeddingWGCtrl.Instance:WeddingDeMandViewOpen(true)
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_XUNYOU_INFO then
		if not self.data:IsMarryUser() then
			return
		end
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)

		if nil == activity_info then
			return
		end

		local main_obj = Scene.Instance:GetMainRole()
		local lover_obj = Scene.Instance:GetObjByUId(main_obj.vo.lover_uid)
		if activity_info.status ~= HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU or protocol.is_trans_to_xunyou_scence ~= 1 then
			ScreenShotWGCtrl.Instance:Resume()
			--巡游结束（开始整个功能引导）
			FunctionGuide.Instance:SetOpenFunGuide(true)
			self:DelXunYouRoleRule()
			self:CloseXunYouView()
			main_obj:SetMarryFlag(0)
			if lover_obj then
				lover_obj:SetMarryFlag(0)
			end
			return
		end
		self.data:XunYouInfo(protocol) --红包次数/鲜花次数等
		local scene = Scene.Instance:GetSceneType()
		local fun_calk = function ()
			local main_obj = Scene.Instance:GetMainRole()
			local lover_obj = Scene.Instance:GetObjByUId(main_obj.vo.lover_uid)
			if activity_info.status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU then
				--巡游开始（关闭整个功能引导）
				FunctionGuide.Instance:SetOpenFunGuide(false)
				self:SetXunYouRoleRule()
				if self.marry_xunyou_view:IsOpen() then
					self.marry_xunyou_view:Flush()
				else
					self:OpenXunYouView()
					ScreenShotWGCtrl.Instance:Resume() -- 关掉拍照
					-- ViewManager.Instance:Open(GuideModuleName.Marry_DeMandView)
				end
				main_obj:SetMarryFlag(1)
				if lover_obj then
					lover_obj:SetMarryFlag(1)
				end
			end
			if self.scene_all_load then
				GlobalEventSystem:UnBind(self.scene_all_load)
				self.scene_all_load = nil
			end
		end
        if SceneType.Common ~= scene then
			self.scene_all_load = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(fun_calk))
		else
			fun_calk()
		end
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_XUNYOU_OBJ_POS then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
		self.data:SetXunYouPos(protocol)
		if self.is_move_xuyou then
			self:SetMoveXuyou(false)
			local marry_info = self.data:GetXunYouPos()
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type ~= SceneType.Common then
				-- print_error("不在普通场景中")
			end
			if marry_info then
				TaskGuide.Instance:CanAutoAllTask(false)
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
				GuajiWGCtrl.Instance:StopGuaji()
				MoveCache.SetEndType(MoveEndType.Normal)
			    if marry_info.scene == Scene.Instance:GetSceneId() and GuajiWGCtrl.CheckRange( marry_info.x,  marry_info.y, 40) then --太近不给跳,直接移动
					GuajiWGCtrl.Instance:FlyToScenePos(marry_info.scene_id, marry_info.x, marry_info.y, false)
			    else
			    	TaskWGCtrl.Instance:JumpFly(marry_info.scene_id, marry_info.x, marry_info.y, true)
			    end
			end
		end
		if activity_info.status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU and self.data:IsMarryUser() then
			self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_ROLE_INFO) --获取巡游玩家信息
		end
	elseif info_type == QINGYUAN_INFO_TYPE.QINGYUAN_INFO_TYPE_ENTER_WEDDING then
		WeddingWGData.Instance:SetMarryBaiTangStartTime(protocol.param7)
	end
	-- print_error('SG>>>>','protocol.goto_xunyou_flag',protocol.goto_xunyou_flag,"info_type",info_type," is_trans_to_xunyou_scence", protocol.is_trans_to_xunyou_scence)
	self:WantXunYouHint(protocol.goto_xunyou_flag)
end

function MarryWGCtrl:ShowXunyouAlert()
    if not self.xunyou_remind_alert2 then
        self.xunyou_remind_alert2 = MarryAlert.New()
    end
    if CountDownManager.Instance:HasCountDown("xunyou_steady") then
        CountDownManager.Instance:RemoveCountDown("xunyou_steady")
    end
    if CountDownManager.Instance:HasCountDown("xunyou_alert_timer") then
        CountDownManager.Instance:RemoveCountDown("xunyou_alert_timer")
    end
    self.xunyou_remind_alert2:UseOne()
    local cur_total_time = 3
    self.xunyou_remind_alert2:SetLableString(Language.Marry.XunyouReadyDes2)
    self.xunyou_remind_alert2:SetOkString(string.format(Language.Marry.XunyouGoTo, cur_total_time))
    self.xunyou_remind_alert2:Open()
    local func = function()
        local xunyou_cfg = MarryWGData.Instance:GetXunYouSceneIdAndPos()
        GuajiWGCtrl.Instance:MoveToPos(xunyou_cfg.scene_id, xunyou_cfg.x, xunyou_cfg.y)
    end
    self.xunyou_remind_alert2:SetOkFunc(func)
    CountDownManager.Instance:AddCountDown("xunyou_alert_timer", function(elapse_time, total_time) 
        self.xunyou_remind_alert2:SetOkString(string.format(Language.Marry.XunyouGoTo, math.floor(total_time - elapse_time)))
    end,
    function()
        self.inter = nil
        self.xunyou_remind_alert2:Close()
    end, nil, cur_total_time + 1, 1)
end

function MarryWGCtrl:SendQingYuanOperaReq(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingYuanOperaReq)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function MarryWGCtrl:OnQingYuanWeddingAllInfo(protocol)
	self.data:SetInviteGuests(protocol)
	if self.invite_view:IsOpen() then
		self.invite_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.Marry_PaiQi)
	HomesWGCtrl.Instance:FlushHomesView()
	self:FlushMarryPaiQiView()
end

function MarryWGCtrl:OnWeddingApplicantInfo(protocol)
	self.data:SetWeddingApplicantInfo(protocol)
	if self.invite_view:IsOpen() then
		self.invite_view:Flush()
	end

    WeddingWGCtrl.Instance:WeddingSendGiftViewFlush()

	WeddingWGCtrl.Instance:FlushWeddingDeMandView()

	if #protocol.applicant_list >= 1 then
		local seq = protocol.seq
		local callback = function()
			self:OpenInviteView(seq)
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE, 1, callback)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE, 0)
	end

	RemindManager.Instance:Fire(RemindName.HUNYAN)
	RemindManager.Instance:Fire(RemindName.Marry_PaiQi)
	HomesWGCtrl.Instance:FlushHomesView()
	self:FlushMarryPaiQiView()
end

function MarryWGCtrl:OnHunYanCurWeddingAllInfo(protocol)
    self.data:SetCurWeddingInfo(protocol)
    AvatarManager.Instance:SetAvatarKey(protocol.role_id, protocol.role_avatar_key_big, protocol.role_avatar_key_small)
	AvatarManager.Instance:SetAvatarKey(protocol.lover_role_id, protocol.lover_avatar_key_big, protocol.lover_avatar_key_small)
	WeddingWGCtrl.Instance:FlushSendToCouple()
    WeddingWGCtrl.Instance:FlushWeddindSendGiftProgress()
    WeddingWGCtrl.Instance:FlushWeddingDeMandView()
    WeddingWGCtrl.Instance:WeddingSendGiftViewFlush()
	self:OpenXunYouOpenHintView()

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	if activity_info and activity_info.status and activity_info.status == ACTIVITY_STATUS.XUNYOU then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.Common then
			local obj_list = Scene.Instance:GetObjListByType(SceneObjType.MarryObj)
			if obj_list ~= nil then
				for k,v in pairs(obj_list) do
					if v ~= nil and not v:IsDeleted() then
						v:InitNameInfo()
					end
				end
			end
		end
	end
end

function MarryWGCtrl:OnWeddingRoleInfo(protocol)
	local old_has_gather = self.data:GetHasGatherRedBag()

	self.data:SetWeddingRoleInFo(protocol)
	if protocol.is_baitang == 1 then
		-- if not CgManager.Instance:IsCgIng() then
		-- 	CgManager.Instance:Play(BaseCg.New("cg/f2_cg_jiehun_prefab", "F2_CG_jiehun"), function()
		-- 		local scene_logic = Scene.Instance:GetSceneLogic()
		-- 		if nil ~= scene_logic and scene_logic:GetSceneType() == SceneType.HunYanFb then
		-- 			if scene_logic.InitGatherFlag then
		-- 				scene_logic:InitGatherFlag()
		-- 			end
		-- 		end
		-- 	end)
		-- end
		local scene_logic = Scene.Instance:GetSceneLogic()
		if nil ~= scene_logic and scene_logic:GetSceneType() == SceneType.HunYanFb then
			if scene_logic.InitGatherFlag then
				scene_logic:InitGatherFlag()
			end
		end

		local main_role = Scene.Instance:GetMainRole()
		if main_role:GetIsGatherState() then
			Scene.Instance:SendStopGather()
		end
	end

	for k,v in pairs(protocol.hunyan_food_id_list) do
		self:ChangeGatherModle(v)
	end

	if self.hunyan_task:IsOpen() then
		self.hunyan_task:Flush()
	end
    WeddingWGCtrl.Instance:FlushWeddindSendGiftProgress()
    
    WeddingWGCtrl.Instance:WeddingSendGiftViewFlush()

	if protocol.is_baitang == 2 then --已结束
		WeddingWGData.Instance:DelMarryDanMuInfo(true)
	end

	local qingyuancfg = self.data:GetQingyuanCfg()
	local red_max = qingyuancfg.wedding_liveness[1].every_turn_gather_limit

	if red_max and old_has_gather ~= protocol.has_gather_red_bag and protocol.has_gather_red_bag > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.TangGuoGatnHint3, red_max - protocol.has_gather_red_bag))
	end
end

function MarryWGCtrl:OpenXunYouView()
	ViewManager.Instance:Open(GuideModuleName.Marry_XunYouView)
end

function MarryWGCtrl:CloseXunYouView()
	ViewManager.Instance:Close(GuideModuleName.Marry_XunYouView)
end

--设置是否传送去巡游队伍
function MarryWGCtrl:SetMoveXuyou(bool)
	self.is_move_xuyou = bool
end

--按手印
--结婚操作回馈
function MarryWGCtrl:OnMarryRetInfo(protocol)
	if protocol.ret_type == MARRY_RET_TYPE.MARRY_PRESS_FINGER then
		--代表对方摁了
		self.marry_fingerprint_view:Flush(0, "finish")
	elseif protocol.ret_type == MARRY_RET_TYPE.MARRY_CANCEL then
		--对方拒绝了
		self.marry_fingerprint_view:Close()
	end
	if protocol.ret_val == 2 then --两人都摁了
		self.marry_fingerprint_view:Flush(0, "bothfinish")
	end
end

--返回对方是否同意结婚请求    1:同意, 0:不同意
function MarryWGCtrl:OnAcceptMarry(protocol)
	if protocol.accept_flag == 1 then
		if self.explain_view:IsOpen() then
			self.explain_view:Close()
		end
		--self.marry_fingerprint_view:Open()
		--策划要求干掉戴戒指界面，直接打开预约界面，无预约结婚次数不打开预约面板
		local role_msg_info = self.data:GetYuYueRoleInfo()
        local can_yuyue = role_msg_info.marry_count > 0
		if can_yuyue and not self.marry_yuyue:IsOpen() then
			self.marry_yuyue:Open()
		end
	else
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.DisAgreeMarryDes)
	end
end

-- 改变已采集婚宴的模型
function MarryWGCtrl:ChangeGatherModle(gather_obj_id)
	local gather_obj = Scene.Instance:GetObjectByObjId(gather_obj_id)
	if gather_obj ~= nil then
		local res_id = MarryGatherId or 0			--更换酒席模型id
		gather_obj:ChangeModel(SceneObjPart.Main, ResPath.GetGatherModel(res_id))
	end
end

--前往月老
function MarryWGCtrl:OpenSelectLoverView(btn_index,callback)
	local function ok_func()
		--巡游中直接拦截掉
		if self.data:GetOwnIsXunyou() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
			return
		end

		-- 护送中不可操作
		if YunbiaoWGData.Instance:GetIsHuShong() then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
			return
		end

		if TaskWGCtrl.Instance:IsFly() then
			return
		end	

		GuajiWGCtrl.Instance:StopGuaji()
		TaskGuide.Instance:CanAutoAllTask(false)
		ViewManager.Instance:CloseAll()

		local npc_cfg = self.data:GetYueLaoCfg()
		if npc_cfg then
			local npc_id = MarryWGData.Instance:GetYueLaoNpcId()
			local range = TaskWGData.Instance:GetNPCRange(npc_id)
			GuajiWGCtrl.Instance:MoveToPos(npc_cfg.scene_id, npc_cfg.x, npc_cfg.y, range)
			MoveCache.SetEndType(MoveEndType.ClickNpc)
			MoveCache.param1 = npc_id
		end
	end

	if not self.goto_yuelao_alert then
		self.goto_yuelao_alert = Alert.New()
	end

	if btn_index == 1 then
		self.goto_yuelao_alert:SetLableString(Language.Marry.GoToYueLaoContent)
	elseif btn_index == 2 then
		self.goto_yuelao_alert:SetLableString(Language.Marry.GoToYueLaoContent2)
	end
    self.goto_yuelao_alert:SetLableRectWidth(450)
	self.goto_yuelao_alert:SetOkFunc(ok_func)
	self.goto_yuelao_alert:Open()
end

--------------------------------- 表白墙 ---------------------------------
---------表白墙通用请求----------
function MarryWGCtrl:SendProfessWallReq(opertype, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSProfessWallReq)
	protocol.opertype = opertype
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

--表白等级信息
function MarryWGCtrl:OnProfessLevelInfo(protocol)
	local info,is_show = self.data:GetProfessLevelInfo()
	if is_show then
		if protocol.add_exp > 0 then
			local str = string.format(Language.ProfessWall.ProfessExpTis,protocol.add_exp)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
--			print_error(str)
		end
		if info.my_grade ~= protocol.my_grade then
			local str = string.format(Language.ProfessWall.ProfessLevelTips,protocol.my_grade)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
			--print_error(str)
		end
	end
	self.data:SetProfessLevelInfo(protocol)
	if self.view:IsOpen() then
		self:Flush(MARRY_TAB_TYPE.PW)
    end
    RemindManager.Instance:Fire(RemindName.Marry_Propose)
    HomesWGCtrl.Instance:FlushHomesView()
    --ProfessWallWGCtrl.Instance:FlushChooseViewByInfoChange()
end


--------------------------------- 祝福 ---------------------------------
function MarryWGCtrl:OnSCMarryNotic( protocol )
	self.data:SaveBlessingInfo(protocol)
	AvatarManager.Instance:SetAvatarKey(protocol.uid1, protocol.avatar_key_big1, protocol.avatar_key_small1)
	AvatarManager.Instance:SetAvatarKey(protocol.uid2, protocol.avatar_key_big2, protocol.avatar_key_small2)

	-- 本次登录不再提示
	local cur_status = MarryWGData.Instance:GetCurLoginBlessingNoTips()
	if cur_status then
		return
	end

	--请求帮派数据
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
	ViewManager.Instance:Open(GuideModuleName.MarryBlessingView)
end

-- 祝贺新人
function MarryWGCtrl:SendMarryWish(uid, wedding_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMarryWish)
	protocol.uid = uid or 0
	protocol.wedding_index = wedding_index or 0
	protocol:EncodeAndSend()
end

--is_close 是否主动关闭
function MarryWGCtrl:OpenMarryNoticeView(is_close)
	if 	not self.marry_notice_view:IsOpen() then
		self.marry_notice_view:IsCanCloseView(is_close)
		self.marry_notice_view:Open()
	end
end

function MarryWGCtrl:CloseMarryNoticeView()
	if 	self.marry_notice_view:IsOpen() then
		self.marry_notice_view:Close()
	end
end

function MarryWGCtrl:FlushMarryNoticeView()
	if 	self.marry_notice_view:IsOpen() then
		self.marry_notice_view:Flush()
	end
end

--情缘预告相关信息
function MarryWGCtrl:OnSCQingYuanNoticeChange(protocol)
	self.data:SetQingYuanNoticeChange(protocol)
	if protocol.cur_states ~= QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY
		or protocol.fetch_reward_flag > 0 then
		self:CloseMarryNoticeView()
	else
		self:FlushMarryNoticeView()
	end

	MainuiWGCtrl.Instance:FlushView(0,"set_marry_notice",{protocol.cur_states})
	RemindManager.Instance:Fire(RemindName.MarryNotice)
	HomesWGCtrl.Instance:FlushHomesView()
end

--情缘领奖
function MarryWGCtrl:SendQingYuanNoticeFetchReward(cur_states, reserve_sh)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQingYuanNoticeFetchReward)
	protocol.cur_states = cur_states or 0
	protocol.reserve_sh = reserve_sh or 0
	protocol:EncodeAndSend()
end 
--发送宾客邀请
function MarryWGCtrl:SendYaoQingBingKeReq(seq, num, data_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWeddingInviteGustList)
	protocol.num = num or 0
	protocol.seq = seq or 0
	protocol.data_list = data_list or {}
	protocol:EncodeAndSend()
end 
--婚宴排期数据
function MarryWGCtrl:OnSCWeddingScheduleInfo(protocol)
	if 	protocol.info_type == 0 then --可以存储数据
		self.data:SetWeddingScheduleInfo(protocol)
		self:FlushMarryPaiQiView()
	elseif protocol.info_type == 1 then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.MARRY_PAIQI, ACTIVITY_STATUS.OPEN)
	elseif protocol.info_type == 2 then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.MARRY_PAIQI, ACTIVITY_STATUS.CLOSE)
		if self.marry_paiqi:IsOpen() then
			self.marry_paiqi:Close()
		end	
	end
	RemindManager.Instance:Fire(RemindName.Marry_PaiQi)
	HomesWGCtrl.Instance:FlushHomesView()
	CalendarWGData.Instance:ClearCalendarActivityCfg()
	MainuiWGCtrl.Instance:FlushView(0, "FlushCalendar")
end
--刷新婚宴排期
function MarryWGCtrl:FlushMarryPaiQiView()
	if self.marry_paiqi:IsOpen() then
		self.marry_paiqi:Flush()
	end
end

--刷新婚宴排期
function MarryWGCtrl:FlushMarryBeYaoView()
	if self.marry_beiyao:IsOpen() then
		self.marry_beiyao:Flush()
	end
end

--主动邀请自己的婚宴信息
function MarryWGCtrl:OnSCWeddingInviteAckInfo(protocol)
	self.data:SetWeddingInviteAckInfo(protocol)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MARRY_BEYAO,1,function()
		ViewManager.Instance:Open(GuideModuleName.MarryBeiYao)
	end)
end

--打开副本购买弹窗提示
function MarryWGCtrl:OpenMarryFbBuyView(from)
	self.marry_fbbuy_view:SetFbBuyDate(from)
end

--单身角色信息
function MarryWGCtrl:OnSCQingYuanSingleInfo(protocol)
	self.data:SetSingleList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MarryNearbySingle)
end

--打开副本购买弹窗提示
function MarryWGCtrl:OnSCMarryRewardLimitInfo(protocol)
	self.data:SetMarryRewardLimitInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MarryTiQin)
end

function MarryWGCtrl:OpenSkillTip(data)
	if self.skill_tip_view then
		self.skill_tip_view:SetSkillData(data)
		self.skill_tip_view:Open()
	end
end

--巡游提示
function MarryWGCtrl:WantXunYouHint(goto_xunyou_flag)
	if 0 == goto_xunyou_flag then
		return
	end
	if not self.xunyou_hint_alert then
		self.xunyou_hint_alert = Alert.New()
	end
	self.xunyou_hint_alert:SetLableString(Language.Marry.XunYouOpenHint)
	self.xunyou_hint_alert:SetOkFunc(function()
		if not MarryWGData.Instance:GetOwnIsXunyou() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.XunYouEntHint)
			return
		end
		self:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_INFO_TYPE_SEND_XUNYOU_FLAG)
	end)
	self.xunyou_hint_alert:Open()
end

--巡游提示
function MarryWGCtrl:GotoYueLao(user_id)
	if nil == user_id then
		return
	end

	local is_friend = SocietyWGData.Instance:CheckIsFriend(user_id)
	if is_friend then
		self:OpenSelectLoverView(1)
		return
	end

	if not self.qiuhun_hint_aleart then
		self.qiuhun_hint_aleart = Alert.New()
	end
	
	self.qiuhun_hint_aleart:SetLableString(Language.Marry.QinHunOpenHint)
	self.qiuhun_hint_aleart:SetOkFunc(function()
		SocietyWGCtrl.Instance:AddFriend(user_id, 0)
	end)
	self.qiuhun_hint_aleart:Open()
end

--这个界面的信息因为会受到一条主动请求协议的控制，所以请求一次就会条件满足就会触发所以需要一个标识来控制(心酸)
function MarryWGCtrl:OpenXunYouOpenHintView()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	if nil == activity_info then
        return
	end
    local is_open = FunOpen.Instance:GetFunIsOpened(FunName.Marry)
    if not is_open then
        return
    end

	local is_zhuren = self.data:IsMarryUser()
	if is_zhuren then
		--如果数据没拿到之前打开了要关闭
		self.marry_xunyou_open_view:Close()
		return
	end

	if not self.marry_xunyou_open_view then
		if activity_info.activity_info.status ~= HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU then
			self.is_can_open_xunyou_hint = true
		elseif activity_info.activity_info.status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU then
			if self.marry_xunyou_open_view:IsOpen() then
				self.marry_xunyou_open_view:Flush()
			end
		end
        return
	end

	if activity_info.status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU then
		if self.is_can_open_xunyou_hint then
			-- 如果是巡游需要主动请求协议获取信息
			WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_WEDDING_INFO)

			local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
			if marry_info and not IsEmptyTable(marry_info) then
				self.marry_xunyou_open_view:Open()
				self.is_can_open_xunyou_hint = false
			end
		end
	else
		self.is_can_open_xunyou_hint = true
		self.marry_xunyou_open_view:Close()
    end	
end

function MarryWGCtrl:SetXunYouMainUiState(value)
	local main_role = Scene.Instance:GetMainRole()
	if value and main_role ~= nil then
		if not main_role:IsInXunYou() then
			return
		end
	end

	-- 容错，避免无法退出该模式
	if not self.data:IsMarryUser() then
		value = false
	end

	MainuiWGCtrl.Instance:FlushView(0, "xunyou_mian_ui_state", {value})
end

function MarryWGCtrl:OnClickQiuHunHandler()
	if CountDownManager.Instance:HasCountDown("close_tiqin_countdown") and self.qiuhun_end_time and self.qiuhun_end_time > TimeWGCtrl.Instance:GetServerTime() then
		local time = self.qiuhun_end_time - TimeWGCtrl.Instance:GetServerTime()
		local format_time = TimeUtil.Format2TableDHMS(time)
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.MarryTiQinCd, format_time.s + 1))
		return true
	end

	self.qiuhun_end_time = TimeWGCtrl.Instance:GetServerTime() + 10
	CountDownManager.Instance:AddCountDown("close_tiqin_countdown", nil, BindTool.Bind1(self.EndTimeCallBack, self), self.qiuhun_end_time, nil, 1)
	return false
end

function MarryWGCtrl:EndTimeCallBack()
	if CountDownManager.Instance:HasCountDown("close_tiqin_countdown") then
		CountDownManager.Instance:RemoveCountDown("close_tiqin_countdown")
		self.qiuhun_end_time = nil
	end
end

function MarryWGCtrl:OpenGetNewView()
	if self.will_get_new_view and not self.will_get_new_view:IsOpen() then
		self.will_get_new_view:Open()
	end
end

function MarryWGCtrl:ReqLYLRole(role_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLiaoYiLiaoOper)
	protocol.oper_type = LIAO_YI_LIAO_TYPE.LIAO_YI_LIAO_TYPE_INTERACTION
	protocol.param1 = role_id or 0
	protocol:EncodeAndSend()
end

function MarryWGCtrl:ReqChangeLYLRole(is_refresh)
	local open_fun_cfg = is_refresh and FunOpen.Instance:GetFunByName("society_friend") or nil
	local protocol = ProtocolPool.Instance:GetProtocol(CSLiaoYiLiaoOper)
	protocol.oper_type = is_refresh and LIAO_YI_LIAO_TYPE.LIAO_YI_LIAO_TYPE_REFRESH_LIST or LIAO_YI_LIAO_TYPE.LIAO_YI_LIAO_TYPE_CHANGE_ROLE
	protocol.param1 = is_refresh and (open_fun_cfg and open_fun_cfg.trigger_param) or 0
	protocol:EncodeAndSend()
	--print_error("请求刷新推荐撩人数据 ReqChangeLYLRole is_refresh, oper_type, param1 =", is_refresh, protocol.oper_type, protocol.param1)
end

-- 请求赠花界面信息
function MarryWGCtrl:SendCSUpgradeFlowerScoreLevel()
	local protocol = ProtocolPool.Instance:GetProtocol(CSUpgradeFlowerScoreLevel)
	protocol:EncodeAndSend()
end

function MarryWGCtrl:OnSCFlowerScoreUpgradeLevelInfo(protocol)
	local key = nil
	if protocol.flower_score_upgrade_level ~= self.data:GetFlowerScoreUpgradeLevel() then
		key = "send_flower_level_change"
	elseif protocol.send_flower_num ~= self.data:GetSendFlowerNum() then
		key = "send_flower_num_change"
	end

	self.data:SetSendFlowersUpgradeData(protocol)
	-- if self.view:IsOpen() and self.view:GetShowIndex() == MARRY_TAB_TYPE.ZH then
	-- 	self.view:Flush(MARRY_TAB_TYPE.ZH, key)
	-- end

	if FlowerWGCtrl.Instance.send_flower_view:IsOpen() and FlowerWGCtrl.Instance.send_flower_view:GetShowIndex() == TabIndex.flower_upgrade then
		FlowerWGCtrl.Instance.send_flower_view:Flush(TabIndex.flower_upgrade, key)
		FlowerWGCtrl.Instance.send_flower_view:UpGradeSuccess()
	end

	HomesWGCtrl.Instance:FlushHomesView()
	RemindManager.Instance:Fire(RemindName.Marry_Flowers)
end

function MarryWGCtrl:OnSCRASendFlowerToplistInfo(protocol)
	RoleCharmNoticeWGData.Instance:SetCharmRankActivityInfo(protocol)
	RemindManager.Instance:Fire(RemindName.RoleCharmRank)
	ViewManager.Instance:FlushView(GuideModuleName.RoleCharmNoticeView)
	RoleCharmNoticWGCtrl.Instance:OperateRankInfoByView()
end

function MarryWGCtrl:OnSCRandomSingleRoleList(protocol)
	self.data:SetRandomSingleRoleList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MarryTiQin)
end

function MarryWGCtrl:SendGetRandomSingleRoleList()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetRandomSingleRoleList)
	protocol:EncodeAndSend()
end

function MarryWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
		RemindManager.Instance:Fire(RemindName.MarryNotice)
	end

	if self.data:GetCurNoticeRewardFlag() > 0 then
		if self.role_data_change then
			RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
			self.role_data_change = nil
		end
	end
end