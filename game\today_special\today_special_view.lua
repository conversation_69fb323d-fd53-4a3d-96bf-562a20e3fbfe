TodaySpecialView = TodaySpecialView or BaseClass(SafeBaseView)

function TodaySpecialView:__init()
    self.select_item_index = -1
	self.select_item_data = nil
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/today_special_ui_prefab", "layout_today_special")
end

function TodaySpecialView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
    XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list["once_buy_btn"], BindTool.Bind(self.OnClickBuyAllShop, self))

    if self.item_seq_list == nil then
        self.item_seq_list = {}
        local node_num = self.node_list["th_item_root"].transform.childCount
        for i = 1, node_num do
            self.item_seq_list[i] = TodaySpecialRender.New(self.node_list["th_item_root"]:FindObj("item_pos_" .. i))
            self.item_seq_list[i]:SetIndex(i)
            self.item_seq_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectItemCallBack, self))
        end
    end

    if self.item_reward_list == nil then
        self.item_reward_list = {}
        for i = 0, 5 do
            self.item_reward_list[i] = ItemCell.New(self.node_list["reward_pos_" .. i])
        end
    end

    if self.over_reward_list == nil then
        self.over_reward_list = {}
        for i = 0, 3 do
            self.over_reward_list[i] = ItemCell.New(self.node_list["over_reward_pos_" .. i])
        end
    end
end

function TodaySpecialView:ReleaseCallBack()
    self.select_item_index = -1
	self.select_item_data = nil

    if self.item_seq_list then
        for k, v in pairs(self.item_seq_list) do
            v:DeleteMe()
        end
        self.item_seq_list = nil
    end

    if self.item_reward_list then
        for k, v in pairs(self.item_reward_list) do
            v:DeleteMe()
        end
        self.item_reward_list = nil
    end

    if self.over_reward_list then
        for k, v in pairs(self.over_reward_list) do
            v:DeleteMe()
        end
        self.over_reward_list = nil
    end

    if CountDownManager.Instance:HasCountDown("act_limit_buy2_time") then
        CountDownManager.Instance:RemoveCountDown("act_limit_buy2_time")
    end

end

function TodaySpecialView:OpenCallBack()
    TodaySpecialWGCtrl.Instance:ReqActivityLimitBuy2Info(OA_LIMIT_RMB_BUY2_OPERATE_TYPE.INFO)
end

function TodaySpecialView:OnClickBuy()
    if self.select_item_data == nil then
        return
    end

    local already_buy_times = TodaySpecialWGData.Instance:GetBuyCountBySeq(self.select_item_data.seq)
    if already_buy_times >= self.select_item_data.buy_times then
        TipWGCtrl.Instance:ShowSystemMsg(Language.TodaySpecial.AllBuy)
        return
    end

    RechargeWGCtrl.Instance:Recharge(self.select_item_data.rmb_price, self.select_item_data.rmb_type, self.select_item_data.rmb_seq)
end

function TodaySpecialView:OnClickBuyAllShop()
    local cur_allbuy_list = TodaySpecialWGData.Instance:GetCurDayOnceBuyList()
    if not IsEmptyTable(cur_allbuy_list) then
        RechargeWGCtrl.Instance:Recharge(cur_allbuy_list.rmb_price, cur_allbuy_list.rmb_type, cur_allbuy_list.rmb_seq)
    end
end

function TodaySpecialView:OnSelectItemCallBack(cell)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    local index = cell:GetIndex()
    if index == self.select_item_index and self.select_item_data == data then
        return
    end
    self.select_item_index = index

    for k,v in pairs(self.item_seq_list) do
        v:FlushSelectHL(k == index)
    end

    self:FlushRewardList()
end

function TodaySpecialView:ShowIndexCallBack()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.TodaySpecialView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY2)
end

function TodaySpecialView:OnFlush(param_t)
    local seq_reward_list = TodaySpecialWGData.Instance:GetCurDayBuyList()
    local cur_overreward_list = TodaySpecialWGData.Instance:GetCurDayOnceBuyList()

    if IsEmptyTable(cur_overreward_list) then
        return
    end

    if seq_reward_list then
        for k, v in pairs(self.item_seq_list) do
            v:SetData(seq_reward_list[k])
        end
    end

    for k, v in pairs(self.over_reward_list) do
        v:SetData(cur_overreward_list.reward_item[k])
    end

    self.node_list.all_shop_price.text.text = string.format(Language.TodaySpecial.AllBuyBtnText, cur_overreward_list.rmb_price)

    local jump_seq = TodaySpecialWGData.Instance:GetBuyJumpSeq()
    local cell = self.item_seq_list[jump_seq]
    if cell then
        self:OnSelectItemCallBack(cell)
    end

    local is_can_all_buy = TodaySpecialWGData.Instance:GetIsCanAllBuy()
    XUI.SetButtonEnabled(self.node_list["once_buy_btn"], is_can_all_buy)
    self:FlushRewardList()
    self:FlushTimeCount()
end

function TodaySpecialView:FlushRewardList()
    local cell = self.item_seq_list[self.select_item_index]
    if cell and cell.data ~= nil then
        self.select_item_data = cell.data
    else
        self.select_item_data = nil
        return
    end

    for k, v in pairs(self.item_reward_list) do
        if self.select_item_data.reward_item and self.select_item_data.reward_item[k] then
            v:SetData(self.select_item_data.reward_item[k])
        end
    end

    local is_buy = TodaySpecialWGData.Instance:GetBuyStateBySeq(self.select_item_index)
    self.node_list["is_buy_times"]:SetActive(is_buy)
    self.node_list["buy_btn"]:SetActive(not is_buy)
    self.node_list.shop_price_text.text.text = string.format(Language.Common.MoneyTypes[0],self.select_item_data.rmb_price)
end


function TodaySpecialView:FlushTimeCount()
    --local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY)
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("act_limit_buy2_time") then
            CountDownManager.Instance:RemoveCountDown("act_limit_buy2_time")
        end

        CountDownManager.Instance:AddCountDown("act_limit_buy2_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function TodaySpecialView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["act_time"].text.text = time_str
end

function TodaySpecialView:OnComplete()
    self.node_list.act_time.text.text = ""
end


TodaySpecialRender = TodaySpecialRender or BaseClass(BaseRender)
function TodaySpecialRender:__init()
	XUI.AddClickEventListener(self.view, BindTool.Bind(BindTool.Bind(self.OnClickCellBtn, self)))
    self.show_item = ItemCell.New(self.node_list["item_pos"])
end

function TodaySpecialRender:__delete()
    self.item_click_callback = nil

    if self.show_item then
        self.show_item:DeleteMe()
        self.show_item = nil
    end
end

function TodaySpecialRender:SetCellClickCallBack(call_back)
    self.item_click_callback = call_back
end

function TodaySpecialRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["item_name"].text.text = self.data.name
    self.show_item:SetData(self.data.show_item)

    local price = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, self.data.rmb_seq)
    local already_buy_times = TodaySpecialWGData.Instance:GetBuyCountBySeq(self.data.seq)
    self.node_list["price_txt"].text.text = price
    self.node_list["buy_times"].text.text = string.format(Language.TodaySpecial.BuyTimesStr, (self.data.buy_times - already_buy_times), self.data.buy_times)

    local kuang_bundel, kuang_asset = ResPath.GetCommon("a2_ty_wpk_bk_" .. self.data.show_icon_ground)
    self.node_list["item_kuang"].image:LoadSprite(kuang_bundel, kuang_asset, function ()
        self.node_list["item_kuang"].image:SetNativeSize()
    end)

    local is_buy = TodaySpecialWGData.Instance:GetBuyStateBySeq(self.data.seq + 1)
    self.node_list["item_price"]:SetActive(not is_buy)
    self.node_list["is_buy"]:SetActive(is_buy)
end

function TodaySpecialRender:OnClickCellBtn()
    if self.item_click_callback then
        self.item_click_callback(self)
    end
end

function TodaySpecialRender:FlushSelectHL(is_select)
    self.node_list["hl_img"]:SetActive(is_select)
    self.node_list["item_mask"]:SetActive(not is_select)
end