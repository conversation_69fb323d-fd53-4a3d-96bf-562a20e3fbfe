require("game/common/ui_scene")
require("game/common/ui_tween")
require("game/common/base_view_loader")
require("game/common/base_view_render")
require("game/common/base_view_effect")

local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()
local view_helper = require("game/common/base_view_helper")
local def_view_name_index = 1000
local UnityGameObject = UnityEngine.GameObject
local ONLY_VIEW = nil

MASK_BG_ALPHA_TYPE = {
	HALF = 128 / 255,
	Normal = 153 / 255,
	Tips = 210 / 255,
	Zero = 0,
	One = 1,
}

UILoadState = {
    Loading = 1,
    Loaded = 2,
}

CloseMode = {
	CloseVisible = 1,			-- 隐藏
	CloseDestroy = 2,			-- 延时销毁
}

REFERENCE_RESOLUTION_TYPE = {
	HD   = 1,   -- 1334 * 768
	FHD  = 2,   -- 1920 * 1080
}

UiLayer = {
	SceneName = 0,				-- 场景名字
	FloatText = 1,				-- 飘字
	MainUILow = 2,				-- 主界面(低)
	MainUI = 3,					-- 主界面
	MainUIHigh = 4,				-- 主界面(高)
	SkillFloatWord = 5,			-- 技能飘字图片层
	Normal = 6,					-- 普通界面
	Pop = 7,					-- 弹出框
	PopWhite = 8,				-- 透明弹出框
	PopTop = 9,                 -- 弹出框(高)
	Guide = 10,					-- 引导层
	SceneLoading = 11,			-- 场景加载层
	SceneLoadingPop = 12,		-- 场景加载层上的弹出层
	Disconnect = 13,			-- 断线面板弹出层
	Standby = 14,				-- 待机遮罩
	GM = 15,
	MaxLayer = 16,
}

ViewStyle = {
	Window = 0,					-- 窗口，不关闭场景摄像机
	Half = 1,					-- 半屏，关闭场景摄像机，主动设置（self.is_need_depth）背景模糊显示
	Full = 2,					-- 全屏，关闭场景摄像机
}

ViewCacheTime = {
	LEAST = 5,
	NORMAL = 60,
	MOST = 3000,
}

UI_SCENE_TYPE = {
	DEFAULT = 0,
}

UI_SCENE_CONFIG_INDEX = {
	JUESE_INFO = 0,
	JUESE_HUANJUE = 1,
	BAG = 2,
	BOSS_ZI = 3,
	BOSS_HUANG = 4,
	JINGJIE = 5,
	WAIGUAN = 6,
	WAIGUAN_JINGJIE = 7,
	CHANGE_BG_TEST = 8,
	TIANSHEN_INFO = 9,
	BEASTS_INFO_BLUE = 10,			-- 幻兽展示(蓝色)
	BEASTS_INFO_RED = 11,			-- 幻兽展示(红色)
	BEASTS_INFO_GOLD = 12,			-- 幻兽展示(金色)
	MARRY_BODY_INFO = 13,
	MARRY_BODY_INFO2 = 14,
	GuiXuDream_1 = 15,                --掌握天权
	BEASTS_BASE_INFO = 16,			-- 幻兽详情信息
	WAIGUAN_DYE = 17,				-- 外观染色
	CG = 18,						-- CG专用
	LINGYU = 19,                    -- 领域
	XIUWEI = 20,                    -- 修为
	FACE_DIY = 21,					-- 捏脸
	WARDROBE = 22,					-- 衣橱
	SoulRing = 23,                  -- 魂环
	DressingDiy = 24,               -- 易容
	HOLY_BEAST = 25,				-- 创世圣兽
	TOTAL_RECHARGE_GIFT = 26,		-- 累充豪礼
	PRI_COL_ZZTQ = 27,              -- 特权至尊特权
	PRI_COL_ZJTQ = 28,              -- 特权终极特权
	GLORY_CRYSTAL = 29,             -- 天裳仙衣
	OTHER_JUESE_INFO = 30,			-- 其他角色信息
	TWIN_DIRECT_PURCHASE = 31,		-- 精品直购
	PRI_COL_SQCY = 32,              -- 特权异火特权
	ThunderMana_Halo = 33,          -- 光环
	PIERRE_DIRECT_PURCHASE = 34,    -- 开服秒杀
	FETTERS_COLLECTION = 35,        -- 羁绊收集
	DUDI_FUSHI = 36,        	-- 毒敌覆世
	ONESWORD_FROSTBITE = 37,        -- 一剑霜寒
}

-- 当下面的界面打开时，新界面打开/(副屏按钮点击) 将其关闭
OTHER_VIEW_OPEN_CLOSE_THESE_VIEW_LIST = {
	[GuideModuleName.CustomActionWindowView] = true,
}

-- NonsupportScreenShot = false
-- NonsupportChangeCamera = false
SafeBaseView = SafeBaseView or BaseClass()

local TypeCamera = typeof(UnityEngine.Camera)
local TypeResolutionImageAdapter = typeof(ResolutionImageAdapter)
local CameraClearFlags = UnityEngine.CameraClearFlags
local BaseViewParentTemplate = UnityGameObject.Find("GameRoot/BaseView")
local FHDBaseViewParentTemplate = UnityGameObject.Find("GameRoot/FHDBaseView")

if BaseViewParentTemplate then
	BaseViewParentTemplate:SetActive(false)
end

if FHDBaseViewParentTemplate then
	FHDBaseViewParentTemplate:SetActive(false)
end

UICameraGameObject = GameObject.Find("GameRoot/UICamera")
UICamera = UICameraGameObject:GetComponent(TypeCamera)
UIURPCameraComp = UICameraGameObject:GetComponent(typeof(URPCamera))

UILayer = GameObject.Find("GameRoot/UILayer").transform
SafeBaseView.MASK_A = 0.7
SafeBaseView.ScreenShotCount = 0
SafeBaseView.IsOpenScreenShot = false
SafeBaseView.UICameraToBaseCount = 0

SafeBaseView.OpenViewOrderTime = 0
SafeBaseView.open_view_list = {}
SafeBaseView.OpenViewCount = 0 -- Normal层级界面打开数，检测是否全部关闭

function SafeBaseView:__init(view_name)
	if nil == view_name or "" == view_name then
		view_name = "safe_view_name_" .. def_view_name_index
		def_view_name_index = def_view_name_index + 1
	end

	self.view_name = view_name
	self.view_layer = UiLayer.Normal
	self.view_cache_time = ViewCacheTime.LEAST						-- 界面缓存时间
	self.show_index = -1											-- 当前显示的标签
	self.default_index = 0											-- 默认显示的标签
	self.remind_tab = nil 											-- 每个标签对应的remindname
	self.view_style = ViewStyle.Window								-- 界面类型
	self.full_screen = false										-- 是否是全屏界面
	self.is_need_depth = false										-- 是否需要开启景深
	self.is_use_objpool = false										-- 是否使用对象池（慎用）
	self.is_async_load = true 										-- 是否是异步加载 (默认全异步)
	self.mask_alpha = MASK_BG_ALPHA_TYPE.Normal 					-- MaskBg透明度
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.HD        -- 分辨率

	self.is_open = false											-- 是否已打开
	self.is_view_loaded = false										-- 是否已加载(index = 0)
	self.is_view_release = false									-- 是否已释放
	self.is_safe_area_adapter = false								-- 是否刘海屏适配
	self.active_close = true										-- 是否可以主动关闭(用于关闭所有界面操作)
	self.is_maskbg_click = true										-- 蒙板是否可点击(能点击的情况下无法穿透点击场景)
	self.flush_param_t = {}											-- 界面刷新参数
	self.opened_index_t = {}										-- 对应标签是否被打开过,关闭后清理
	self.blocks_raycasts = true
	self.is_show_loading_action = false
	self.self_control_rendring = false
	self.open_source_view = nil										-- 打开界面来源（主界面按钮）
	self.open_source_act = nil										-- 打开界面来源（主界面活动按钮）
	self.is_open_source_open = false								-- 打开界面是否使用打开界面来源播放打开界面动效

	self.is_use_perspective = true									-- UI摄像机切为透视
	self.tab_sub = nil 												-- (兼容旧baseView)
	self.order = 10 												-- (兼容旧baseView)有些系统由多个baseview组成，区间（1-10），越大越上面
	self.sort_order = 0												-- (兼容旧baseView)
	self.open_time = 0 												-- (兼容旧baseView)同一层级的系统，记录打开时间
	self.open_tween = nil--UITween.ShowFadeScale							-- (兼容旧baseView)
	self.close_tween = nil--UITween.HideFadeUp							-- (兼容旧baseView)
	self.can_do_fade = true 										-- (兼容旧baseView)

	self.root_node = nil 											-- (兼容旧baseView)
	self.root_node_transform =  nil 								-- (兼容旧baseView)
	self.canvas = nil 												-- (兼容旧baseView)
	self.mask_bg = nil 												-- (兼容旧baseView)
	self.hide_mask = false 											-- (兼容旧baseView)

	self.ui_scene_change_config_index = -1                                 -- UI场景的参数index(不是UI_SCENE_CONFIG_INDEX,是UI场景显示表里的参数设置的index)

	self.node_list = {}
	self.ui_scene_show_tabs = {}
	self.view_loader = BaseViewLoader.New()
	self.view_effect = BaseViewEffect.New()
	self.view_render = BaseViewRender.New()
	self.view_render:SetViewInStance(self)

	ViewManager.Instance:RegisterView(self, view_name)
end

function SafeBaseView:__delete()
	ViewManager.Instance:UnRegisterView(self.view_name)
	self:Release()
	self.ui_scene_show_tabs = {}
	self.view_loader:DeleteMe()
	self.view_effect:DeleteMe()
	self.view_render:DeleteMe()
end

function SafeBaseView:Release()
	if self.__gameobj_loaders then
		ReleaseGameobjLoaders(self)
	end

	if self.__res_loaders then
		ReleaseResLoaders(self)
	end

	if self.__delay_call_map then
		CancleAllDelayCall(self)
	end

	self.view_loader:Clear()
	self.view_effect:Clear()
	self.view_render:Clear()


	if self.is_view_loaded then
		self.is_view_loaded = false
		Trycall(self.ReleaseCallBack, self)
	end

	self.view_render:DestroyAllNode()

	self.show_index = -1
	self.flush_param_t = {}
	self.is_open = false
	self.root_node = nil
	self.root_node_transform = nil
	self.canvas = nil
	self.mask_bg = nil
	self.resolution_image_adapter = nil
	self.node_list = {}
	self.ui_scene_change_config_index = -1

	if FunOpen.Instance then
		FunOpen.Instance:UnRegsiterTabFunUi(self.view_name)
	end

	if self.change_scene then
		GlobalEventSystem:UnBind(self.change_scene)
		self.change_scene = nil
	end

	if is_develop or GLOBAL_DELETE_ME_CHECK_SWITCH then
		develop_mode:OnReleaseView(self)
	end

	self.is_view_release = true
end

function SafeBaseView:ClearViewTween()
	self.open_tween = nil
	self.close_tween = nil
end

-- 蛋疼的旧代码，这里只是做接口显示
function SafeBaseView:SetViewName(str)
	self.view_render:SetTextViewNameShow(self.view_name, str)
end

-- transform_cfg {vector2, sizeDelta}
function SafeBaseView:AddViewResource(index, bundle_name, asset_name, transform_cfg)
	self.view_loader:AddViewResource(index, bundle_name, asset_name, transform_cfg)
end

function SafeBaseView:GetViewResourceGameObj(bundle_name, asset_name)
	return self.view_loader:GetViewResourceGameObj(bundle_name, asset_name)
end

-- 需要展示ui场景的标签		data = {type = UI_SCENE_TYPE.DEFAULT}
-- data 为{}时不主动设置显示，只隐藏主场景
function SafeBaseView:SetTabShowUIScene(index, data)
	self.ui_scene_show_tabs[index] = data or {}
end

function SafeBaseView:IsOpen()
	return self.is_open
end

function SafeBaseView:IsLoaded()
	return self.is_view_loaded
end

function SafeBaseView:IsLoadedIndex(index)
	return self.view_loader:IsLoadedIndex(index)
end

function SafeBaseView:DoOperationsOnViewOpen()
	-- 当新界面打开时，一些界面会被关闭
	for k,v in pairs(OTHER_VIEW_OPEN_CLOSE_THESE_VIEW_LIST) do
		if ViewManager.Instance:IsOpen(k) and k ~= self.view_name then
			ViewManager.Instance:Close(k)
		end
	end
end

function SafeBaseView:Open(index)
	-- 等待主界面加载完 和 拍摄UI景深摄像机加载完 再打开界面
	if (MainuiWGCtrl.Instance and not MainuiWGCtrl.Instance:HadSnapShotBackground()) or IsNil(MainCameraSnapshot) then
		if self.is_need_depth or self.view_style == ViewStyle.Half then
			ViewManager.Instance:SetWaitScreenShotView(self, index)
			return
		end
	end

	if ONLY_VIEW ~= nil and not ViewManager.Instance:IsCanOpenWaitOnlyView(ONLY_VIEW, self.view_name) then
		ViewManager.Instance:SetWaitOnlyViewOpenView(self, index)
		return
	end

	-- if self.view_name == GuideModuleName.ReconnectView then
	-- 	return
	-- end

	local show_index = index
	if nil == show_index then
		show_index = self:CalcShowIndex()
	end

	-- 防止切场景期间，打开含UI场景，导致出现异常
	if Scene.Instance:IsSceneLoadingStateEnter() and (self.ui_scene_show_tabs[0] ~= nil or self.ui_scene_show_tabs[show_index]) then
		ViewManager.Instance:SetWaitSceneLoadedView(self, show_index)
		return
	end

	self:DoOperationsOnViewOpen()

	if self.is_open then
		-- 如果View在Open状态又被调了Open，认为需要弹到最上层
		SafeBaseView.OpenViewOrderTime = SafeBaseView.OpenViewOrderTime + 1
		self.open_time = SafeBaseView.OpenViewOrderTime
		SafeBaseView.AddToViewList(self)
		ViewManager.Instance:AddOpenView(self)
		ViewManager.Instance:UpdateSceneCameraPP()
		ViewManager.Instance:CtrlSnapShotBackgroundShow(self)
		self:ChangeToIndex(show_index, true)
		return
	end

	self:ChangeOpenCount(1)
	self.is_open = true
	self.is_view_release = false
	SafeBaseView.OpenViewOrderTime = SafeBaseView.OpenViewOrderTime + 1
	self.open_time = SafeBaseView.OpenViewOrderTime

	local is_big_size_view = self.view_style ~= ViewStyle.Window
	if self.is_async_load then -- 根据大小界面判定是否异步(大界面同步，小界面异步)
		if is_big_size_view then
			self.view_loader:SetLoadPriority(ResLoadPriority.faster_async_high)
		else
			self.view_loader:SetLoadPriority(ResLoadPriority.faster_async_mid)
		end
	end

	self.view_loader:SetIsAsyncLoad(self.is_async_load)
	self.view_loader:SetIsUseObjPool(self.is_use_objpool)
	self.view_render:SetViewName(self.view_name)
	self.view_render:SetReferenceResolution(self.reference_resolution)
	self.view_render:SetIsSafeAreaAdapter(self.is_safe_area_adapter)

	local root_node, root_canvas, gameobj_root_transform, node_list = self.view_render:TryCreateRooNode()
	self.canvas = root_canvas 		-- 为了兼容旧的baseView
	self.root_node = root_node		-- 为了兼容旧的baseView
	self.root_node_transform = gameobj_root_transform -- 为了兼容旧的baseView
	self.node_list = node_list

	gameobj_root_transform:GetComponent(typeof(UnityEngine.CanvasGroup)).blocksRaycasts = self.blocks_raycasts
	self.view_effect:SetGameObjRootTransform(gameobj_root_transform)
	self.view_loader:SetGameObjRootTransform(gameobj_root_transform)
	self.view_render:SetRootNodeActive(true)
	self.view_render:TryShowMaskBg(self.mask_alpha)
	self.mask_bg = self.view_render:GetMaskBg()
	self.view_render:SetUiRoleModelStageIsActive(true)

	if (is_big_size_view) and not self.view_loader:IsLoadedIndex(show_index) then
		self.is_show_loading_action = true
		self.view_render:TryAddLoadErrorTimer()
	end

	CancleAllDelayCall(self)
	SafeBaseView.AddToViewList(self)
	
	-- 半屏界面强制需要背景模糊, 全屏界面因为遮挡不需要
	if self.view_style == ViewStyle.Half then
		self.is_need_depth = true
	elseif self.view_style == ViewStyle.Full then
		self.is_need_depth = false
	end

	if ViewManager and ViewManager.Instance then
		ViewManager.Instance:AddOpenView(self)
		-- 更新Scene摄像机后处理开关
		ViewManager.Instance:UpdateSceneCameraPP()
		ViewManager.Instance:CtrlSnapShotBackgroundShow(self)
	end

	if is_big_size_view then
		self:ClearViewTween()
	end
	
	local root, tween_close, tween_open = self:CheckOpenSourceNeedClose()
	local open_tween = self.is_open_source_open and tween_open or self.open_tween
	self.view_effect:SetIsPlayTween(self.can_do_fade, open_tween, tween_close or self.close_tween, root)
	self.view_effect:TryPlayTweenOpen()

	if is_big_size_view or self.is_need_depth then
		self.view_effect:TrySetUICameraRenderTypeToBase()
	end

	self:OpenCallBack()
	if VIEW_CLICK_REPORT_MAP[self.view_name] then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, self.view_name, show_index, BURIED_EVENT_PARAM.openView)
	end

	self:ChangeToIndex(show_index)
	self:SetResolutionImageAdapterActive(true)

	if self.view_loader:IsLoadedIndex(index) then
		GlobalEventSystem:Fire(OtherEventType.VIEW_OPEN, self, index)
	end
	
	if not self.change_scene then
		self.change_scene = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind1(self.OnStartLoadScene, self))
	end

	if self.view_layer == UiLayer.Normal and not IgnoreAudioUrlView[self.view_name] then
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(ViewAudioUrl[self.view_name] or AudioUrl.DefaultOpen, nil, true))
	end
end

function SafeBaseView:Close()
	if not self.is_open then
		return
	end

	self.is_show_loading_action = false
	self.is_open = false
	self:ChangeOpenCount(-1)
	SafeBaseView.RemoveFromOpenViewList(self)
	if ViewManager and ViewManager.Instance then
		ViewManager.Instance:RemoveOpenView(self)
	end

	self.view_render:TryHideMaskBg()
	self.view_render:SetUiRoleModelStageIsActive(false)
	self.view_render:ClearLoadErrorTimer()
	self.view_render:TryHideLoadAction()
	self.view_render:TryHideFixCloseBtn()
	self:SetResolutionImageAdapterActive(false)
	if VIEW_CLICK_REPORT_MAP[self.view_name] then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, self.view_name, self.show_index, BURIED_EVENT_PARAM.closeView)
	end
	self.show_index = -1
	self.opened_index_t = {}

	if is_develop or GLOBAL_DELETE_ME_CHECK_SWITCH then
		develop_mode:OnCloseView(self)
	end

	if ViewManager and ViewManager.Instance then
		-- 更新Scene摄像机后处理开关
		ViewManager.Instance:UpdateSceneCameraPP()
		ViewManager.Instance:AddUISceneShowList(self, false)
		ViewManager.Instance:CtrlSnapShotBackgroundShow(self)
		ViewManager.Instance:OpenWaitOnlyViewOpenView()
	end
	
	self:CloseCallBack()
	GlobalEventSystem:Fire(OtherEventType.VIEW_CLOSE, self)

	if self.view_style ~= ViewStyle.Window or self.is_need_depth then
		self.view_effect:TryResetUICameraRenderType()
	end
	
	self.view_effect:TryPlayTweenClose(function ()
		self.view_render:SetRootNodeActive(false)
		if self.view_cache_time then
			ReDelayCall(self, function()
				self:Release()
			end, self.view_cache_time, "base_view_release")
		end
	end)

	if self.view_layer == UiLayer.Normal and not IgnoreAudioUrlView[self.view_name] then
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.DefaultClose, nil, true))
	end
end

function SafeBaseView:ChangeToIndex(index, is_same_flush_ui_scene)
	if nil == index then
		print_error("[SafeBaseView] ChangeToIndex error!, 请指定index", self.view_name)
		return
	end

	if not self.is_open then
		-- print_error("[SafeBaseView] ChangeToIndex error!, 当前面板没有处于打开状态，禁止调用")
		return
	end

	-- 指定的index相同时则只刷新index
	if self.show_index == index then
		self:__TryFlushIndex(index)

		if is_same_flush_ui_scene then	-- 打开界面且切换相同的界面时需要尝试刷新一下UI场景显示模型
			self:CheckUISceneShow(index)
		end
		return
	end
	if self.show_index ~= -1 then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick,self.view_name, self.show_index, BURIED_EVENT_PARAM.closeView)
	end

	self.show_index = index
	-- 切换页签重置ui_scene_change_config_index
	self.ui_scene_change_config_index = -1

	if self.view_loader:IsLoadedIndex(index) then
		self:CheckUISceneShow(index)
		self.view_render:CtrlRoleModelActiveByIndex(index)
		self:__RefreshIndex(index)

	else
		-- 每个不是0的index都先加载0
		if 0 ~= index and not self.view_loader:IsLoadedIndex(0) then
			-- 先加载index=0，完成后再加载目标index
			self:__LoadIndexSerially(0, function()
				self:__LoadIndexSerially(index, nil)
			end)
		else
			-- 直接加载目标index
			self:__LoadIndexSerially(index, nil)
		end
	end
end

-- 串行加载指定index，确保严格的加载顺序
function SafeBaseView:__LoadIndexSerially(index, completion_callback)
	self.view_loader:LoadAssets({index}, function()
		self:__LoadIndex(index, completion_callback)
	end)
end

function SafeBaseView:__LoadIndex(index, completion_callback)
	self.view_loader:Load(index, function (index, gamobjs)
		if self.is_view_release then
			return
		end

		if 0 == index then
			self.is_view_loaded = true
		end
		
		-- 存在界面已关闭，资源才异步加载出来的情况（一些状态不可设置）
		if self.is_open then
			self:CheckUISceneShow(index)
			self.view_render:CtrlRoleModelActiveByIndex(index)
		end

		-- 存在虽关闭，但是界面未释放，缓存期间再次打开的情况（必要逻辑需执行）
		self.view_render:AddRenderGameObjs(index, gamobjs)
		if 0 == index then
			self.is_show_loading_action = false
			self.view_render:ClearLoadErrorTimer()
			self.view_render:TryHideLoadAction()
			self.view_render:TryHideFixCloseBtn()

			self.view_render:TryHandleDefaultCloseBtn(self.node_list, BindTool.Bind1(self.OnClickCloseWindow, self))
			self.view_render:TryHandleDefaultRuleTipsBtn(self.node_list, BindTool.Bind1(self.OnClickRuleTips, self))
			self.view_render:SetTextViewNameShow(self.view_name, nil)

			if is_develop then
				develop_mode:OnLuaCall("load_callback_start")
			end

			self:LoadCallBack()

			if is_develop then
				develop_mode:OnLuaCall("load_callback_end")
			end
		end

		self:LoadIndexCallBack(index)

		if self.is_open then			
			GlobalEventSystem:Fire(OtherEventType.VIEW_OPEN, self, index)
			self:__RefreshIndex(index)
			
			if completion_callback then
				completion_callback()
			end
		end
	end)
end

function SafeBaseView:CtrlRoleModelActiveByOtherView(is_show)
	if self.view_render then
		self.view_render:CtrlRoleModelActiveByIndex(self.show_index, is_show)
	end
end

function SafeBaseView:RegisterDefaultClose()
	self.view_render:TryHandleDefaultCloseBtn(self.node_list, BindTool.Bind1(self.OnClickCloseWindow, self))
end

function SafeBaseView:__RefreshIndex(index)
	self.view_render:RefreshGameObjActive(index)
	self.view_render:RefreshTabbar(self.tabbar, index, self.show_index == index)
	if index == self.show_index then
		if nil == self.opened_index_t[index] then
			self.opened_index_t[index] = true
			self:OpenIndexCallBack(index)
		end

		if self.is_show_loading_action then
			self.is_show_loading_action = false
			self.view_render:ClearLoadErrorTimer()
			self.view_render:TryHideLoadAction()
			self.view_render:TryHideFixCloseBtn()
		end

		self:ShowIndexCallBack(index)
		local report_data = VIEW_CLICK_REPORT_MAP[self.view_name]
		if report_data ~= nil then
			if type(report_data) == "number" and report_data > 0 then
				ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, self.view_name, report_data)
			elseif type(report_data) == "table" and (report_data["all"] or report_data[index]) then
				ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewTabClick, self.view_name, index)
				
				ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick,self.view_name, index, BURIED_EVENT_PARAM.openView)
			end
		end
	end

	view_helper:TryInitViewTween(self.view_name, index, self.node_list)
	self.view_render:TryRefreshDefaultRuleTipsBtn(self.node_list, self.view_name, index)
	self:__MergeFlushParam(index, "all", {"all"}, false)
	self:__TryFlushIndex(index)
end

function SafeBaseView:Flush(index, key, value, is_override_value)
	if nil ~= index and type(index) == "table" then
		print_error("[SafeBaseView]Flush请指定正确的index", index)
		return
	end

	-- 当外部传了nil过来意味着外部希望传参数进行刷新，具体刷新哪个index，外部并不关心。因此处理成如下
	if nil == index then
		if self.show_index == -1 then
			index = 0
		else
			index = self.show_index
		end
	end

	self:__MergeFlushParam(index, key, value, is_override_value)

	if self:IsOpen() then
		TryDelayCall(self, function ()
			if index == self.show_index then
				self:__TryFlushIndex(self.show_index)
			end

		end, 0, "base_view_flush_" .. index)
	end
end

function SafeBaseView:__MergeFlushParam(index, key, value, is_override_value)
	index = index or self.show_index
	key = key or "all"
	value = value or {"all"}

	if nil == is_override_value then
		is_override_value = true
	end

	self.flush_param_t[index] = self.flush_param_t[index] or {}
	for k1, v1 in pairs(value) do
		self.flush_param_t[index][key] = self.flush_param_t[index][key] or {}
		if is_override_value then
			self.flush_param_t[index][key][k1] = v1
		else
			if nil == self.flush_param_t[index][key][k1] then
				self.flush_param_t[index][key][k1] = v1
			end
		end
	end
end

function SafeBaseView:__TryFlushIndex(index)
	if nil ~= self.flush_param_t[index] and self.view_loader:IsLoadedIndex(index) then
		local param_list = self.flush_param_t[index]
		self.flush_param_t[index] = nil
		self:OnFlush(param_list, index)
	end
end

-- 兼容旧baseview
function SafeBaseView:GetRootNode()
	return self.root_node
end

function SafeBaseView:GetLoadedIndexGameObjList(index)
	return self.view_loader:GetLoadedIndexGameObjList(index)
end

function SafeBaseView:GetShowIndex()
	return self.show_index
end

function SafeBaseView:GetLayer()
	return self.view_layer
end

function SafeBaseView:SetRendering(value)
	self.view_render:SetRendering(value)
end

-- 兼容旧baseView
function SafeBaseView:SetActive(value)
	self.view_render:SetRendering(value)
end

function SafeBaseView:IsRendering()
	return self.view_render:IsRendering()
end

function SafeBaseView:CanActiveClose()
	return self.active_close
end

--[[
	【is_maskbg_button_click】是否有蒙版点击回调，结合is_maskbg_click使用
		true:	执行回调 callback or Close。
		false：	不执行
	【is_maskbg_click】蒙板是否可点击
		true：	可点击, 无法穿透。
		false:	不可点击, 穿透下层
]]
function SafeBaseView:SetMaskBg(is_maskbg_button_click, is_maskbg_click, is_vague, callback)
	if nil == is_maskbg_click then
		is_maskbg_click = self.is_maskbg_click
	end
	self.view_render:SetMaskBg(is_maskbg_button_click, is_maskbg_click, callback, BindTool.Bind1(self.Close, self))
end

function SafeBaseView:SetMaskBgAlpha(pre_value)
	pre_value = pre_value or MASK_BG_ALPHA_TYPE.Normal
	self.view_render:SetMaskBgAlpha(pre_value)
	self.mask_alpha = pre_value
end

-- 当前view_index 与 tab_index 不同则隐藏role_model
-- @tab_index：TabIndex.XXX、{TabIndex.XXX1, TabIndex.XXX2}
function SafeBaseView:AddUiRoleModel(role_model, tab_index)
	self.view_render:AddUiRoleModel(role_model, tab_index)
end

function SafeBaseView:SetSecondView(vec2, size_node, anchored_pos)
	self.view_render:SetSecondView(vec2, size_node, anchored_pos)
end

function SafeBaseView.GetBaseViewParentTemplate(reference_resolution_type)
	if reference_resolution_type == REFERENCE_RESOLUTION_TYPE.FHD then
		return FHDBaseViewParentTemplate
	else
		return BaseViewParentTemplate
	end
end

----[[ UI摄像机 RenderType 为 Base
function SafeBaseView.SetUICameraRenderTypeToBase()
	SafeBaseView.UICameraToBaseCount = SafeBaseView.UICameraToBaseCount + 1
	SafeBaseView.UpdateFollowCameraEnabled()
end

function SafeBaseView.ResetUICameraRenderType()
	SafeBaseView.UICameraToBaseCount = SafeBaseView.UICameraToBaseCount - 1
	SafeBaseView.UpdateFollowCameraEnabled()
end

function SafeBaseView.UpdateFollowCameraEnabled()
	SafeBaseView.UICameraToBaseCount = SafeBaseView.UICameraToBaseCount < 0 and 0 or SafeBaseView.UICameraToBaseCount
	Scene.Instance:CtrlFollowCameraEnabled(SafeBaseView.UICameraToBaseCount == 0)
end
--]]

----[[ UI背景模糊
function SafeBaseView.UpdateScreenShotStatus(is_open)
	if is_open ~= SafeBaseView.IsOpenScreenShot then
		SafeBaseView.IsOpenScreenShot = is_open
		Scene.Instance:ChangeBGBlurCamera()
		MainCameraSnapshot.enabled = is_open
	end
end

-- 兼容旧baseView
function SafeBaseView.UpdateScreenShot()
	-- if NonsupportScreenShot then
	-- 	return
	-- end

	SafeBaseView.UpdateScreenShotStatus(SafeBaseView.ScreenShotCount > 0)
end

-- 兼容旧baseView
function SafeBaseView.ForceCloseScreenShot()
	SafeBaseView.UpdateScreenShotStatus(false)
end

function SafeBaseView.OpenScreenShot()
	-- if NonsupportScreenShot then
	-- 	return
	-- end

	SafeBaseView.ScreenShotCount = SafeBaseView.ScreenShotCount + 1
	SafeBaseView.UpdateScreenShotStatus(true)
end

function SafeBaseView.CloseScreenShot()
	SafeBaseView.ScreenShotCount = SafeBaseView.ScreenShotCount - 1
	if SafeBaseView.ScreenShotCount <= 0 then
		SafeBaseView.UpdateScreenShotStatus(false)
		if SafeBaseView.ScreenShotCount < 0 then
			SafeBaseView.ScreenShotCount = 0
			print_error("[SafeBaseView] screen_shot_count is less 0!!!")
		end
	end
end
--]]

function SafeBaseView.SetAllUICameraEnable(enabled)
	UICamera.enabled = enabled
end

function SafeBaseView.SetUICameraClearFlag(flag)
	flag = flag or CameraClearFlags.Depth
	UICamera.clearFlags = flag
end

-------------------------------------------------------------------
-- 处理隐藏所有面板的管理 begin
-------------------------------------------------------------------
function SafeBaseView.RemoveFromOpenViewList(view)
	for k,v in pairs(SafeBaseView.open_view_list) do
		if v == view then
			table.remove(SafeBaseView.open_view_list, k)
			break
		end
	end

	SafeBaseView.UpdateOpenViewActive()
end

function SafeBaseView.AddToViewList(view)
	local is_open = false
	for k,v in pairs(SafeBaseView.open_view_list) do
		if v == view then
			is_open = true
			break
		end
	end

	if not is_open then
		table.insert(SafeBaseView.open_view_list, view)
	end

	SafeBaseView.UpdateLocalZOrder(view)
	SafeBaseView.UpdateOpenViewActive()

	if ONLY_VIEW and view.view_name ~= ONLY_VIEW and not SafeBaseView.NotCloseViewByOnlyView(view.view_name) then
		view:SetRootNodeActive(false)
	end
end

local SORT_INTERVAL = 100
OverrideOrderGroupMgr.Instance:SetGroupCanvasOrderInterval(SORT_INTERVAL)
function SafeBaseView.UpdateLocalZOrder(cur_view)
	if not cur_view.root_node then
		return
	end

	local view, view_canvas
	local list = {}
	for k,v in pairs(SafeBaseView.open_view_list) do
		table.insert(list, v)
	end

	local function list_sort(a, b)
		if a.view_layer == b.view_layer then
			if a.order ~= b.order then
				return a.order < b.order
			end

			if a.open_time ~= b.open_time then
				return a.open_time < b.open_time
			end

			return a.sort_order < b.sort_order
		end

		return a.view_layer < b.view_layer
	end

	table.sort(list, list_sort)
	local len = #list
	local index_t = {}
	local k
	for i,v in ipairs(list) do
		if v.root_node and v.view_layer == cur_view.view_layer then
			if v.canvas then
				k = index_t[v.view_layer] or 1
				v.sort_order = v.view_layer * 1000 + (k - 1) * SORT_INTERVAL
				v.canvas.sortingOrder = v.sort_order
				index_t[v.view_layer] = k + 1
			end
		end
	end

	SafeBaseView.open_view_list = list
end

function SafeBaseView.UpdateOpenViewActive()
	local _view, has_mask, inactive_views = nil, false, {}
	for i = #SafeBaseView.open_view_list, 1, -1 do
		_view = SafeBaseView.open_view_list[i]
		if _view then
			if nil ~= _view.SetRootPointActive then
				_view:SetRootPointActive(true)
			end
			--2020/5/5 14:09 策划要把遮罩加上
			-- -- 拍照时关闭所有遮罩
			if _view.view_name == GuideModuleName.ScreenShotView then
				has_mask = true
			end

			--只显示最上层遮罩
			if _view.mask_bg and _view.view_layer ~= UiLayer.PopWhite-- and not _view.full_screen
				and _view.view_name ~= GuideModuleName.NormalGuideView then
				_view.mask_bg:SetActive(not has_mask and not _view.hide_mask and _view:IsRendering())
				has_mask = true
			end
		end
	end
end

function SafeBaseView:SetRootPointActive(show)
	if self.root_node_transform then
		self.root_node_transform.gameObject:SetActive(show)
	end

	if self.mask_bg and not self.hide_mask then
		self.mask_bg:SetActive(self:IsRendering())
	end
end

---- 兼容旧baseView 设置唯一显示的view
function SafeBaseView.SetOnlyView(view_name)
	ONLY_VIEW = view_name
	for k,v in pairs(SafeBaseView.open_view_list) do
		if ONLY_VIEW and v.view_name ~= ONLY_VIEW and not SafeBaseView.NotCloseViewByOnlyView(v.view_name) then
			v:SetRootNodeActive(false)
		else
			v:SetRootNodeActive(true)
		end
	end

	ViewManager.Instance:CheckViewRendering()
end

local not_close_view_list = {
	[GuideModuleName.TipsEneterCommonSceneView] = true,
	[GuideModuleName.ItemGoodsTipView] = true,
	[GuideModuleName.TipsSystemView] = true,
	[GuideModuleName.FirstRechargeTips] = true,
	[GuideModuleName.SecondRechargeTips] = true,
	[GuideModuleName.VipFourBuyTips] = true,
	[GuideModuleName.CameraBubbleView] = true,
	[GuideModuleName.SceneLoading] = true,
	[GuideModuleName.ReconnectView] = true,
	[GuideModuleName.MainUIView] = true,
	[GuideModuleName.ItemTip] = true,
}

-- 设置唯一view时,不关闭的界面
function SafeBaseView.NotCloseViewByOnlyView(view_name)
	return not_close_view_list[view_name]
end

function SafeBaseView:CheckUISceneShow(index)
	local data = self.ui_scene_show_tabs[index] or self.ui_scene_show_tabs[0]
	if not data then
		local is_big_size_view = self.view_style ~= ViewStyle.Window
		if is_big_size_view or self.is_need_depth then
			ViewManager.Instance:AddUISceneShowList(self, false, nil)
		end
	else
		ViewManager.Instance:AddUISceneShowList(self, true, data)
	end
end

-- 场景发生改变了
function SafeBaseView:OnStartLoadScene()
	if self:IsOpen() and self.view_render:CheckRoleModelActiveInIndex() then
		self:Close()
	end
end

-- 检测特殊关闭方式，需要增加关闭动效，界面缩小到对应打开按钮位置
function SafeBaseView:CheckOpenSourceNeedClose()
	if (not self.open_source_view) and (not self.open_source_act) then
		return
	end

	local view = nil
	if self.open_source_view then
		view = MainuiWGCtrl.Instance:GetOneSundryNode(self.open_source_view)
	end

	if view == nil then
		if self.open_source_act then
			for act_id, _ in pairs(self.open_source_act) do
				local btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(act_id)
				view = btn and btn:GetView()

				if view ~= nil then
					break
				end
			end
		end
	end

	if view == nil then
		return view, nil
	end

	return view, UITween.HideFadeUpToOpenPos, UITween.ShowFadeUpToOpenPos
end

function SafeBaseView:AddActToOpenSource(act_id)
	if not self.open_source_act then
		self.open_source_act = {}
	end

	self.open_source_act[act_id] = true
end

-- 设置适配屏幕组件激活
function SafeBaseView:SetResolutionImageAdapterActive(is_enabled)
	if (not self.node_list) or (not self.node_list.panel_bg) then
		return
	end

	if not self.resolution_image_adapter then
		self.resolution_image_adapter = self.node_list.panel_bg:GetComponent(TypeResolutionImageAdapter)
	end

	if self.resolution_image_adapter == nil or IsNil(self.resolution_image_adapter) then
		return
	end

	self.resolution_image_adapter.enabled = is_enabled
end

-------------------------------------------------

----------------------------------- 检测界面是否全部关闭 -------------------------------------
function SafeBaseView:ChangeOpenCount(offset)
	if self.view_layer == UiLayer.Normal then
		SafeBaseView.OpenViewCount = SafeBaseView.OpenViewCount + offset
	end
end

function SafeBaseView.CheckIsAllClose()
	return SafeBaseView.OpenViewCount <= 0
end
----------------------------------- 检测界面是否全部关闭end -------------------------------------

-- 计算要显示的index（可重写）
function SafeBaseView:CalcShowIndex()
	return view_helper:CalcShowIndex(self.default_index, self.remind_tab, self.tab_sub, self.view_name)
end

-- 加载完指定的index后调用
function SafeBaseView:LoadCallBack()
end

-- 打开后调用（注意此打开是在加载之前调用）
function SafeBaseView:OpenCallBack()
end

function SafeBaseView:LoadIndexCallBack(index)
end

-- 打开标签调用（注意此打开是在加载之后调用）
function SafeBaseView:OpenIndexCallBack(index)
end

-- 切换标签调用
function SafeBaseView:ShowIndexCallBack(index)
end

-- 关闭前调用
function SafeBaseView:CloseCallBack()
end

-- 销毁前调用
function SafeBaseView:ReleaseCallBack()
end

-- 刷新
function SafeBaseView:OnFlush(param_list, index)
end

-- 兼容旧baseView
function SafeBaseView:SetRootNodeActive(value)
	self.view_render:SetRootNodeActive(value)
end

-- 兼容旧baseView(可重写)
function SafeBaseView:OnClickCloseWindow()
	self:Close()
end

-- 拓展规则按钮
function SafeBaseView:OnClickRuleTips()
	view_helper:TryShowRuleTips(self.view_name, self.show_index, self.node_list)
end

-- 兼容旧baseView
function SafeBaseView:GetGuideUiCallBack(ui_name, ui_param)
	return self.view_render:GetGuideUiCallBack(ui_name, BindTool.Bind1(self.OnClickCloseWindow, self), self:IsOpen())
end

-- 兼容旧baseView
function SafeBaseView:ShowIndex(index)
	self:ChangeToIndex(index)
end

function SafeBaseView:LoadSprite(bundle_name, asset_name, callback, cbdata)
	LoadSprite(self, bundle_name, asset_name, callback, cbdata)
end

function SafeBaseView:LoadSpriteAsync(bundle_name, asset_name, callback, cbdata)
	LoadSpriteAsync(self, bundle_name, asset_name, callback, cbdata)
end

function SafeBaseView:LoadRawImage(arg0, arg1, arg2)
	LoadRawImage(self, arg0, arg1, arg2)
end

function SafeBaseView:GetViewName()
	return self.view_name or ""
end