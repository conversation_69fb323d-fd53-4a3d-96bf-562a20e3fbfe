
BossAssistRewardView = BossAssistRewardView or BaseClass(SafeBaseView)
function BossAssistRewardView:__init()
	self.is_use_mask = true
	self.default_index = 1

	self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_assist_reward")
end

function BossAssistRewardView:__delete()
end

function BossAssistRewardView:ReleaseCallBack()
	if self.reward_f then
		self.reward_f:DeleteMe()
		self.reward_f = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function BossAssistRewardView:LoadCallBack(index, loaded_times)
	if loaded_times <= 1 then
		self.reward_f = ItemCell.New(self.node_list.ph_reward_f)
		local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_assist_auto").other[1]
		self.reward_f:SetData(other_cfg.assist_thank_reward_item)
		XUI.AddClickEventListener(self.node_list.btn_reward, BindTool.Bind(self.OnClickReward, self))
	end

	self.head_cell = BaseHeadCell.New(self.node_list["img_cell"])
end

function BossAssistRewardView:ShowIndexCallBack(index)
	self:Flush(index)
end

function BossAssistRewardView:OpenCallBack()

end

function BossAssistRewardView:CloseCallBack()

end

function BossAssistRewardView:OnClickReward()
	if self.has_reward then
		local reward_info = BossAssistWGData.Instance:GetGuildAssisThankLattertInfo()
		BossAssistWGCtrl.SendGuildAssistGetThankReward(reward_info.role_id, reward_info.target_boss_scene_id, reward_info.target_boss_id)
	end
	self:Close()
end

function BossAssistRewardView:OnFlush(param_t, index)
	local reward_info = BossAssistWGData.Instance:GetGuildAssisThankLattertInfo()
	self.node_list.thank_txt.text.text = Language.BossAssist.AssistThankDec[reward_info.select_statment] or ""
	self.node_list.role_name.text.text = reward_info.role_name

	local is_df, level = RoleWGData.Instance:GetDianFengLevel(reward_info.role_level)
	self.node_list["img_feixian"]:SetActive(is_df)
	self.node_list.level.text.text = level

	self.head_cell:SetData(reward_info)

	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	local times_max =  ConfigManager.Instance:GetAutoConfig("guild_assist_auto").other[1].thank_other_reward_times_max
	self.has_reward = assist_info.day_assist_reward_times < times_max

	self.reward_f:MakeGray(not self.has_reward)
	self.node_list.yilinwan:SetActive(not self.has_reward)
	self.node_list.btn_reward_text.text.text = self.has_reward and Language.BossAssist.RewardBtnTxt[1] or Language.BossAssist.RewardBtnTxt[2]
end