SupremeFieldsWuxing = SupremeFieldsWuxing or BaseClass(SafeBaseView)

function SupremeFieldsWuxing:__init()
	self.view_name = "SupremeFieldsWuxing"

	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/supreme_fields_ui_prefab", "layout_supreme_fields_wuxing")
end

function SupremeFieldsWuxing:ReleaseCallBack()
	if nil ~= self.attr_obj_list then
		for k, v in pairs(self.attr_obj_list) do
			v:DeleteMe()
		end
		self.attr_obj_list = nil
	end

	if nil ~= self.late_attr_obj_list then
		for k, v in pairs(self.late_attr_obj_list) do
			v:DeleteMe()
		end
		self.late_attr_obj_list = nil
	end
end

function SupremeFieldsWuxing:LoadCallBack()
	self.type_index = 0
	if nil == self.attr_obj_list then
		self.attr_obj_list = {}
		local attr_num = self.node_list.wuxing_attr_list.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAddAttrRender.New(self.node_list["wuxing_attr_" .. i])
			cell:SetAttrNameNeedSpace(true)
			cell:SetIndex(i)
			self.attr_obj_list[i] = cell
		end
	end

	if nil == self.late_attr_obj_list then
		self.late_attr_obj_list = {}
		local attr_num = self.node_list.wuxing_down_attr_list.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAddAttrRender.New(self.node_list["wuxing_down_attr_" .. i])
			cell:SetAttrNameNeedSpace(true)
			cell:SetIndex(i)
			self.late_attr_obj_list[i] = cell
		end
	end

	XUI.AddClickEventListener(self.node_list["attr_activate_btn"], BindTool.Bind(self.OnBtnActivateAttr, self)) --激活属性加成.
end

function SupremeFieldsWuxing:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v.type_index then
				self.type_index = v.type_index
			end
		end
	end

	local wuxing_star_list = SupremeFieldsWGData.Instance:GetAllStarAttrInfo()
	if IsEmptyTable(wuxing_star_list) or not wuxing_star_list[self.type_index] then
		return
	end

	local lv = wuxing_star_list[self.type_index]
	local is_open = wuxing_star_list[self.type_index] > 0

	self:FlushSupremeFieldsWuxingView(lv, is_open)
end

function SupremeFieldsWuxing:FlushSupremeFieldsWuxingView(lv, is_open)
	local cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(self.type_index)
	if not cfg then
		return
	end

	local field_lv, filed_is_open = SupremeFieldsWGData.Instance:TestFootLightOpen(self.type_index)
	self.node_list.attr_activate_text.text.text = lv >= 10 and Language.SupremeFields.WuXing_Attr_Max or Language.SupremeFields.WuXing_Attr_Activate

	local is_max_lv = lv >= 10
	self.node_list.panel_1:SetActive(is_open)
	self.node_list.wuxing_noactive_panel:SetActive(not is_open)

	self.node_list.attr_activate:SetActive(is_open and lv >= 10)
	self.node_list.attr_activate_btn:SetActive(lv < 10)
	self.node_list.attr_activate_btn_text.text.text = string.format(filed_is_open and Language.SupremeFields.WuXing_Activate_Btn or Language.SupremeFields.WuXing_Activate_Btn2)
	XUI.SetButtonEnabled(self.node_list.attr_activate_btn, filed_is_open)

	local sum, is_max = SupremeFieldsWGData.Instance:GetSlotNextNeedSum(self.type_index, lv == 0 and 1 or lv)

	self.node_list.wuxing_field_name_text.text.text = string.format(Language.SupremeFields.JieShu2, lv == 0 and 1 or lv, cfg.name)
	
	self.node_list.wuxing_field_need_text_1.text.text = string.format(
		lv >= 10 and Language.SupremeFields.WuXing_Field_Text or
		is_max and Language.SupremeFields.WuXing_Field_Need_Max_Text or Language.SupremeFields
		.WuXing_Field_Need_Text, lv == 0 and 1 or lv, sum)

	local is_red = SupremeFieldsWGData.Instance:GetWuXingLateAttrIsCanActivate(self.type_index)
	self.node_list.remind:SetActive(not is_open and is_red)

	self:SetFieldIcon(self.node_list.wuxing_field_icon)
	self:SetSkillInfoText(self.node_list.wuxing_skill_text, is_open, lv)
	self:SetWuXingAttr(self.attr_obj_list, is_open and lv or 1)

	
	self.node_list.panel_2:SetActive(not is_max_lv)
	self.node_list.wuxing_max_panel:SetActive(is_max_lv)
	self.node_list.wuxing_max_text.text.text = Language.SupremeFields.WuXingNoDataText[2]
	if not is_max_lv then
		local sum, is_max = SupremeFieldsWGData.Instance:GetSlotNextNeedSum(self.type_index, lv + 1)

		self.node_list.wuxing_field_need_text_2.text.text = string.format(is_max and 
			Language.SupremeFields.WuXing_Field_Need_Max_Text or Language.SupremeFields.WuXing_Field_Need_Text, lv + 1, sum)

		self.node_list.attr_activate_btn_text.text.text = string.format(is_max and 
			Language.SupremeFields.WuXing_Activate_Btn or Language.SupremeFields.WuXing_Activate_Btn2)
		
		self.node_list.wuxing_field_name_text2.text.text = string.format(Language.SupremeFields.JieShu2, lv + 1, cfg.name)
		XUI.SetButtonEnabled(self.node_list.attr_activate_btn, is_max)
		self.node_list.remind:SetActive(is_max)

		self:SetFieldIcon(self.node_list.wuxing_field_icon2)
		self:SetSkillInfoText(self.node_list.wuxing_down_skill_text, is_open, lv + 1)
		self:SetWuXingAttr(self.late_attr_obj_list, lv + 1)
	end

	--[[
	--设置下一阶段.
	if lv >= 10 or is_open == false then
		self.node_list.panel_2:SetActive(false)
	else
		local sum, is_max = SupremeFieldsWGData.Instance:GetSlotNextNeedSum(self.type_index, lv + 1)

		self.node_list.wuxing_field_need_text_2.text.text = string.format(
			is_max and Language.SupremeFields.WuXing_Field_Need_Max_Text or Language.SupremeFields
			.WuXing_Field_Need_Text, lv + 1, sum)

		self.node_list.attr_activate_btn_text.text.text = string.format(is_max and
			Language.SupremeFields.WuXing_Activate_Btn or Language.SupremeFields.WuXing_Activate_Btn2)

		XUI.SetButtonEnabled(self.node_list.attr_activate_btn, is_max)
		self.node_list.remind:SetActive(is_max)

		self:SetFieldIcon(self.node_list.wuxing_field_icon2)
		self:SetSkillInfoText(self.node_list.wuxing_down_skill_text, is_open, lv + 1)
		self:SetWuXingAttr(self.late_attr_obj_list, lv + 1)
	end
	]]
end

function SupremeFieldsWuxing:SetFieldIcon(obj)
	local bundle, asset = ResPath.GetSupremeFieldsIcon(self.type_index)
	obj.image:LoadSpriteAsync(bundle, asset)
end

function SupremeFieldsWuxing:SetSkillInfoText(obj, is_open, lv)
	local skill_index = is_open and lv or 1
	local skill_id = SupremeFieldsWGData.Instance:GetSkillIDList(self.type_index, skill_index)[1] or 0
	local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(skill_id)
	obj.text.text = skill_cfg.skill_up_txt
end

function SupremeFieldsWuxing:SetWuXingAttr(obj, lv)
	local attr_list = SupremeFieldsWGData.Instance:GetWuXingStarAttrList(self.type_index, lv)
	if not IsEmptyTable(attr_list) then
		for k, v in ipairs(obj) do
			v:SetData(attr_list[k])
		end
	end
end

function SupremeFieldsWuxing:OnBtnActivateAttr()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
	SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.ACTIVATE_STAR_ATTR, self.type_index)
end
