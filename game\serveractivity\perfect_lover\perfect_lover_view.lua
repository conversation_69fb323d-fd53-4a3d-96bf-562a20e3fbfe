PerfectLoverView = PerfectLoverView or BaseClass(SafeBaseView)

function PerfectLoverView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/prefactloverui_prefab", "layout_open_server_lovers")
end

function PerfectLoverView:__delete()
end

function PerfectLoverView:ReleaseCallBack()
	if self.open_server_competition_list ~= nil then 
		self.open_server_competition_list:DeleteMe()
		self.open_server_competition_list = nil
	end

	if CountDownManager.Instance:HasCountDown("collect_bless_time") then
		CountDownManager.Instance:RemoveCountDown("collect_bless_time")
	end
end

function PerfectLoverView:LoadCallBack()
	self:CreateListView()
end

function PerfectLoverView:CreateListView()
	self.open_server_competition_list = PerfectLoversListView.New(PerfectLoversItemRender,self.node_list.ph_list)   

	XUI.AddClickEventListener(self.node_list.btn_marry_1, BindTool.Bind1(self.OnClick<PERSON>arry, self))
	XUI.AddClickEventListener(self.node_list.btn_schedule, BindTool.Bind1(self.OnClickSchedule, self))
	XUI.AddClickEventListener(self.node_list.btn_lover_title, BindTool.Bind1(self.OnClickTitle, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind1(self.Close, self))

end

function PerfectLoverView:OpenCallBack()
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_PERFECTLOVER,
		opera_type = 0,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)

	if CountDownManager.Instance:HasCountDown("collect_bless_time") then
		CountDownManager.Instance:RemoveCountDown("collect_bless_time")
	end
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_PERFECTLOVER) or {}
	if activity_info.status == ACTIVITY_STATUS.OPEN then
		local next_time = activity_info.next_time or 0
		CountDownManager.Instance:AddCountDown("collect_bless_time", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)
	else
		self:CompleteRollerTime()
	end
end

function PerfectLoverView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list.lbl_activity_time ~= nil then
		-- if time > 0 then
			local format_time = TimeUtil.FormatSecond(time)
			-- local str_list = Language.Common.TimeList
			-- local time_str = ""
			-- if format_time.day > 0 then
				-- time_str = format_time.day .. str_list.d
			-- end
			-- if format_time.hour > 0 then
				-- time_str = time_str .. format_time.hour .. str_list.h
			-- end
			-- time_str = time_str .. format_time.min .. str_list.min

			self.node_list.lbl_activity_time.text.text = (format_time)
		-- end
	end
end

function PerfectLoverView:CompleteRollerTime()
	if self.node_list.lbl_activity_time ~= nil then
		self.node_list.lbl_activity_time.text.text = ("0")
	end
end

function PerfectLoverView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("collect_bless_time") then
		CountDownManager.Instance:RemoveCountDown("collect_bless_time")
	end
end

function PerfectLoverView:OnFlush()
	local reward_id = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().perfect_lover[1].reward_item.item_id
	-- self.reward_title:SetTitleId(ItemWGData.Instance:GetItemConfig(reward_id).param1)
	local attr_cfg = TitleWGData.Instance.GetTitleConfig(ItemWGData.Instance:GetItemConfig(reward_id).param1)
	local attr = AttributeMgr.GetAttributteByClass(attr_cfg)
	local capability = AttributeMgr.GetCapability(attr)
	self.node_list.lbl_zhandoiuli.text.text =capability

	local opengame_info = PerfectLoverWGData.Instance:GetPerfectLoverNameList()
	local data_list = __TableCopy(opengame_info)

	for i=1,10 do
		if nil == data_list[i] then
			data_list[i] = {}
		end
	end
	if next(data_list) == nil then return end
	self.open_server_competition_list:SetDataList(data_list,3)

	local marry_flag = PerfectLoverWGData.Instance:GetPerfectLoverInfo()
	local count = bit:d2b1n(marry_flag)
	local str = "(%d/%d)"
	self.node_list.lbl_schedule.text.text =  ToColorStr(string.format(str, count, 3),count >= 3 and COLOR3B.GREEN or COLOR3B.RED)
end

-- function PerfectLoverView:OnClickMarry()
-- 	FunOpen.Instance:OpenViewNameByCfg("marry##sub=tiqin")
-- end

function PerfectLoverView:OnClickMarry()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_LOVER_INFO_REQ)
	MarryWGCtrl.Instance:OpenTiQinView()
end

function PerfectLoverView:OnClickSchedule()
	ServerActivityWGCtrl.Instance:SetRecordFlag(PerfectLoverWGData.Instance:GetPerfectLoverInfo())
	ServerActivityWGCtrl.Instance:OpenMarryScheduleView()
end

function PerfectLoverView:OnClickTitle()
	local item_data = CommonStruct.ItemDataWrapper()
	local reward_id = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().perfect_lover[1].reward_item.item_id
	item_data.item_id = reward_id
	TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROME_BROWSE_ROLE)
end

----------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------
PerfectLoversItemRender = PerfectLoversItemRender or BaseClass(BaseRender)
function PerfectLoversItemRender:__init()
	
end

function PerfectLoversItemRender:__delete()
	
end

function PerfectLoversItemRender:OnFlush()
	if not self.data then
		return
	end 
	self.node_list.lbl_no_name:SetActive(self.data[1] == "")
	self.node_list.heart:SetActive(self.data[1] ~= "")
	self.node_list.lbl_left_name.text.text = (self.data[1] or "")
	self.node_list.lbl_right_name.text.text = (self.data[2] or "")
	self.node_list.lbl_rank.text.text = (self.index)
	self:FlushBg()
	self:SetHighLight(self:IsSelectIndex())
end

function PerfectLoversItemRender:SetHighLight(bool)
	if bool ~= nil then 
		self.node_list.high_light.image.enabled = bool
	end
end

function PerfectLoversItemRender:FlushBg()
	if self.index <= 3 then 
		self.node_list.img_star:SetActive(true)
		self.node_list.img_star.image:LoadSprite(ResPath.GetRankImage("rank_num_" .. self.index))
		self.node_list.img_bg.image:LoadSprite(ResPath.GetPerfectLover("rank_" .. self.index))   
		self.node_list.lbl_rank:SetActive(false)
	else
		self.node_list.lbl_rank:SetActive(true)
		self.node_list.img_star:SetActive(false)
		self.node_list.img_bg.image:LoadSprite(ResPath.GetPerfectLover("bg_3"))   
	end

end

PerfectLoversListView = PerfectLoversListView or BaseClass(AsyncListView)
--刷新格子
function PerfectLoversListView:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	cell_index = cell_index + 1
	if not item_cell then
		local toggle = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Toggle))
		local button = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Button))
		item_cell = self.item_render.New(cell.gameObject)
		self.cell_list[cell] = item_cell
		if toggle then
			item_cell:SetToggleGroup(self.list_view.toggle_group)
			toggle:AddClickListener(BindTool.Bind(self.ListEventCallback, self, item_cell))
		elseif button then
			button:AddClickListener(
				BindTool.Bind(self.ListEventCallback, self, item_cell))
		end
	end
	local item_data = self.data_list[cell_index]
	item_cell:SetIndex(cell_index)
	item_cell:SetSelectIndex(self.select_index)
	item_cell:SetData(item_data)

	if self.default_index and self.default_index == cell_index then
		self:ListEventCallback(item_cell)
		self.default_index = nil
	end
end
