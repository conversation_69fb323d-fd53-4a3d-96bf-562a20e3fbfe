FieldPvPAimView = FieldPvPAimView or BaseClass(SafeBaseView)

function FieldPvPAimView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_match_aim")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function FieldPvPAimView:LoadCallBack()
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list.title_view_name.text.text = Language.Field1v1.TitleRankAim
	self.ranking_reward_list = AsyncListView.New(RankingRewardItemRender,self.node_list.ph_ranking_reward_list)
	self.has_load_callback = true
	if self.need_flush then
		self:Flush(self.need_flush)
		self.need_flush = nil
	end		
end

function FieldPvPAimView:ReleaseCallBack()
	if self.ranking_reward_list then
		self.ranking_reward_list:DeleteMe()
		self.ranking_reward_list = nil
	end
	self.has_load_callback = nil
	self.need_flush = nil
end

function FieldPvPAimView:OnFlush( param )
	if not self.has_load_callback then
		self.need_flush = param
		return
	end
	local tb = "table"
	if type(param) ~= tb then
		return
	end
	for k,v in pairs(param) do
		if type(v) == tb then
			for m,n in pairs(v) do
				self:FlushRankMatchAimPanel(n)
			end
		end
	end
end

function FieldPvPAimView:FlushRankMatchAimPanel(index)
	if index == KFPVP_TYPE.ONE then
		local kf_info = KuafuOnevoneWGData.Instance:Get1V1Info()

		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local time_table = os.date("*t", server_time)
		local month_day_count = os.date("%d",os.time({year=time_table.year,month=time_table.month + 1,day=0}))
		local count_shengyu = month_day_count - time_table.day + 1
		self.node_list.lbl_desc_2.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc2,time_table.month,month_day_count,count_shengyu)

		-- local month_day_count = os.date("%d",os.time({year=os.date("%Y"),month=os.date("%m"),day=0}))
		-- local month = os.date("%m",os.time())
		-- local day = os.date("%d",os.time())
		-- local name1 = {}
		-- for i=1,#month do
		-- 	table.insert(name1,string.sub(month,i,i))
		-- end
		-- local shengyu_day = month_day_count - time_table.day + 1 -- tonumber(month_day_count) - tonumber(day) + 1
		-- if tonumber(name1[1]) <= 0 then
		-- 	self.node_list.lbl_desc_2.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc2,tonumber(name1[2])+1,shengyu_day)
		-- else
		-- 	if tonumber(name1[2]) >= 2 then
		-- 		self.node_list.lbl_desc_2.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc2,1,shengyu_day)
		-- 	else
		-- 		self.node_list.lbl_desc_2.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc2,tonumber(month) + 1,shengyu_day)
		-- 	end
		-- end
		
		local desc_reward =  kf_info.cross_lvl_total_join_times > 20 and  20 or  kf_info.cross_lvl_total_join_times
		local color_desc = kf_info.cross_lvl_total_join_times >= 20 and COLOR3B.D_GREEN or COLOR3B.RED 

		self.node_list.lbl_desc_3.text.text = string.format(Language.Ranking_Text.lbl_Desc,color_desc,desc_reward)
		self.node_list.rich_desc_1.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc1)

		local ranking_data = KuafuOnevoneWGData.Instance:GetdwRankReward(kf_info.cur_season)
		self.ranking_reward_list:SetDataList(ranking_data)
		self.ranking_reward_list:JumpToTop()

	elseif index == KFPVP_TYPE.MORE then  --3v3
		local kf_info = KuafuPVPWGData.Instance:GetActivityInfo()
		self.node_list.lbl_desc_2.text.text = (Language.Ranking_Text.Rank_End) --赛季截至时间
		local desc_reward =  kf_info.challenge_total_match_count > 20 and  20 or  kf_info.challenge_total_match_count
		local color_desc = kf_info.challenge_total_match_count >= 20 and COLOR3B.D_GREEN or COLOR3B.RED 

		self.node_list.lbl_desc_3.text.text = string.format(Language.Ranking_Text.lbl_Desc,color_desc,desc_reward)
		self.node_list.rich_desc_1.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc1)

		local ranking_data = KuafuPVPWGData.Instance:GetdwRankReward(kf_info.cur_season)
		self.ranking_reward_list:SetDataList(ranking_data)
		self.ranking_reward_list:JumpToTop()
	end
end

----------------------ItemRender------------------------------
RankingRewardItemRender = RankingRewardItemRender or BaseClass(BaseRender)

function RankingRewardItemRender:__init()

end

function RankingRewardItemRender:__delete()
	-- for i = 1, 5 do
	-- 	if self.cell_list[i] then
	-- 		self.cell_list[i]:DeleteMe()
	-- 		self.cell_list[i] = nil
	-- 	end
	-- end
	if self.cell_list then
		self.cell_list:DeleteMe()
		self.cell_list = nil
	end
end

function RankingRewardItemRender:LoadCallBack()
	self.cell_list = AsyncListView.New(RankingRewardItemCell, self.node_list.list)
	-- for i = 0, 5 do
	-- 	self.cell_list[i] = ItemCell.New(self.node_list["ph_ranking_reward_" .. i])
	-- 	self.cell_list[i]:SetCoinNumShow(true)
	-- end
end

function RankingRewardItemRender:OnFlush()
	-- local asset_name,bundle_name = nil, nil
	-- if self.data.grade < 4 then
	-- 	asset_name,bundle_name = ResPath.GetKf1V1("flag_" .. self.data.grade)
	-- else
	-- 	asset_name,bundle_name = ResPath.GetKf1V1("flag_4")
	-- end
	-- self.node_list.flag.image:LoadSprite(asset_name,bundle_name,function ()
	-- 	self.node_list.flag.image:SetNativeSize()
	-- end)	
	self.node_list.img_match_aim.text.text = self.data.name
	local item_data = TableCopy(self.data.reward_item)
	table.insert(item_data,1,item_data[0])

	self.cell_list:SetDataList(item_data)
	-- for i = 0, 5 do
	-- 	if i <= #item_data then
	-- 		if i == 0 then
	-- 			item_data[i].jjc_equip_type = self.data.name
	-- 		end
	-- 		self.cell_list[i]:SetData(item_data[i])
	-- 		self.cell_list[i]:SetActive(true)
	-- 	else
	-- 		self.cell_list[i]:SetActive(false)
	-- 	end
	-- end
end

function RankingRewardItemRender:OnSelectChange(is_select)
	self.node_list.highlight:SetActive(is_select)
end

------------------------------------------------------------------------------
RankingRewardItemCell = RankingRewardItemCell or BaseClass(ItemCell)

function RankingRewardItemCell:__init()
	self.is_show_coin_num = true
end