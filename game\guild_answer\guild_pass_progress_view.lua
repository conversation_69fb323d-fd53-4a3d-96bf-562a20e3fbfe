GuildPassProgressView = GuildPassProgressView or BaseClass(SafeBaseView)
local pro_val = 0.9
function GuildPassProgressView:__init()
    self:AddViewResource(0, "uis/view/guild_answer_ui_prefab", "layout_pass_progess")
end

function GuildPassProgressView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("guild_pass_progress") then
        CountDownManager.Instance:RemoveCountDown("guild_pass_progress")
    end
end

function GuildPassProgressView:ShowIndexCallBack()
    local time = GuildAnswerWGData.Instance:GetPassRemainTime()
    CountDownManager.Instance:AddCountDown("guild_pass_progress", BindTool.Bind(self.UpdateView, self),
        BindTool.Bind(self.CompleteView, self), nil, time, 0.02)
end

function GuildPassProgressView:UpdateView(elapse_time, total_time)
    local num = string.format("%.2f", elapse_time/total_time)
    self.node_list["slider"].slider.value = num
    -- local amount1 = num / pro_val
    -- if amount1 > pro_val then
    --     self.node_list["slider"].slider.value = (num - pro_val) / (1 - pro_val)
    -- end

    -- self.node_list["txt_pro"].text.text = string.format(Language.GuildAnswer.PassPro, num*100)
    self.node_list["txt_pro"].text.text = num*100 .. "%"
end

function GuildPassProgressView:CompleteView()
    self:Close()
end