DungeonQualitySceneLogic = DungeonQualitySceneLogic or BaseClass(CommonFbLogic)

function DungeonQualitySceneLogic:__init()

end

function DungeonQualitySceneLogic:__delete()

end

function DungeonQualitySceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
	FuBenWGCtrl.Instance:OpenTaskFollow()
	FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function DungeonQualitySceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
end


function DungeonQualitySceneLogic:DoMoveOperate()
	--自动寻路去传送阵
	local cfg = Scene.Instance.scene_config.doors[1]
	if cfg ~= nil then
		Scene.Instance.main_role:DoMoveOperate(cfg.x, cfg.y, 0, nil, true)
	end
end

function DungeonQualitySceneLogic:SetGuaiJiDoors()
	local monster_list = Scene.Instance:GetMonsterList()
	if #monster_list == 0 then
		if Scene.Instance.scene_config.doors[1] ~= nil then
			local cfg = Scene.Instance.scene_config.doors[1]
			Scene.Instance.main_role:DoMoveOperate(cfg.x, cfg.y, 0, nil, true)
		end
	end
end