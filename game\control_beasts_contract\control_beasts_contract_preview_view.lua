ControlBeastsContractPreviewView = ControlBeastsContractPreviewView or BaseClass(SafeBaseView)

function ControlBeastsContractPreviewView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/control_beasts_contract_ui_prefab", "control_beasts_contract_preview")
end

function ControlBeastsContractPreviewView:__delete()
	
end

function ControlBeastsContractPreviewView:ReleaseCallBack()
	if self.preview_type_list then
		self.preview_type_list:DeleteMe()
		self.preview_type_list = nil
	end

	if self.preview_grid then
		self.preview_grid:DeleteMe()
		self.preview_grid = nil
	end

	self.curr_type_index = nil
end

function ControlBeastsContractPreviewView:LoadCallBack()
	if not self.preview_grid then
        self.preview_grid = AsyncBaseGrid.New()
        self.preview_grid:CreateCells({
										col = 5, 
										cell_count = 60, 
										list_view = self.node_list.preview_grid, 
										itemRender = BeastPreviewGridItem,
										change_cells_num = 1,
										assetBundle = "uis/view/control_beasts_contract_ui_prefab",
										assetName = "preview_grid_Item",
		})
		self.preview_grid:SetStartZeroIndex(false)
	end

	-- 初始化已孵化列表
	if not self.preview_type_list then
		self.preview_type_list = AsyncListView.New(BeastPreviewTypeItemRender, self.node_list.preview_type_list)
		self.preview_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectPreviewTypeCB, self))
	end
end

function ControlBeastsContractPreviewView:OnSelectPreviewTypeCB(type_item, cell_index, is_default, is_click)
    if nil == type_item or nil == type_item.data then
		return
	end

	--拿到当前选中的格子下标
	local item_index = type_item:GetIndex()
	if self.curr_type_index == item_index then
        return
	end

	self.curr_type_index = item_index
	self.preview_type_list:JumpToIndex(self.curr_type_index, 3)
	self:FlushPreviewGrid()
end

function ControlBeastsContractPreviewView:FlushPreviewGrid()
	if not self.curr_type_index then
		return
	end

	local list, _ = ControlBeastsContractWGData.Instance:GetBeastRewardCfgByType(self.curr_type_index, true)
	self.preview_grid:SetDataList(list)
end

function ControlBeastsContractPreviewView:OnFlush(param_t)
	local type_list = ControlBeastsContractWGData.Instance:GetDeedLevelCfg()
	self.preview_type_list:SetDataList(type_list)

	if self.curr_type_index == nil then
		self.curr_type_index = ControlBeastsContractWGData.Instance:GetBeastDrawDeedLevel()
	end

	self.preview_type_list:JumpToIndex(self.curr_type_index, 3)
	self:FlushPreviewGrid()
end

---------------------------------------------------------------------------------------
BeastPreviewGridItem = BeastPreviewGridItem or BaseClass(BaseRender)

function BeastPreviewGridItem:LoadCallBack()
    if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_pos)
	end
end

function BeastPreviewGridItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BeastPreviewGridItem:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData({item_id = self.data.item_id, is_beast = false})---这里需要其他的东西在加，看策划需求
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local item_color = item_cfg and item_cfg.color or 0
	local item_name = item_cfg and item_cfg.name or ""
	self.node_list.item_name.text.text = ToColorStr(item_name, ITEM_COLOR[item_color])
end

----------------------------------------------------------------------------
BeastPreviewTypeItemRender = BeastPreviewTypeItemRender or BaseClass(BaseRender)

function BeastPreviewTypeItemRender:OnFlush()
	if not self.data then
		return
	end

	local str = string.format(Language.ContralBeasts.ContrastCondition12, self.index)
	self.node_list.normal_text.text.text = str
	self.node_list.select_text.text.text = str
end

function BeastPreviewTypeItemRender:OnSelectChange(is_select)
	self.node_list.normal:CustomSetActive(not is_select)
	self.node_list.select:CustomSetActive(is_select)
end