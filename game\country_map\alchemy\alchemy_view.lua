-- 炼丹
AlchemyView = AlchemyView or BaseClass(SafeBaseView)

function AlchemyView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/country_map_ui/alchemy_prefab", "layout_alchemy") 		-- 炼丹房
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
	self.is_safe_area_adapter = true
	self.view_style = ViewStyle.Full
end


function AlchemyView:LoadCallBack()
	if not self.alchemy_cell_list then
		self.alchemy_cell_list = {}
		for i = 0, AlchemyWGData.MAX_ALCHEMY_CELL_COUNT - 1 do
			self.alchemy_cell_list[i] = AlchemyCellItem.New(self.node_list["alchemy_cell_" .. i])
			self.alchemy_cell_list[i]:SetIndex(i)
		end
	end

	if not self.alchemy_furnace_list then
		self.alchemy_furnace_list = {}
		for i = 0, AlchemyWGData.MAX_FURNACE_COUNT - 1 do
			self.alchemy_furnace_list[i] = AlchemyFurnaceCell.New(self.node_list["furnace_" .. i])
			self.alchemy_furnace_list[i]:SetIndex(i)
		end
	end

	self.node_list.title_view_name.text.text = Language.CountryAlchemy.ViewName
	XUI.AddClickEventListener(self.node_list["normal_array"], BindTool.Bind(self.OpenNormalArrayView, self))
	XUI.AddClickEventListener(self.node_list["btn_dandao"], BindTool.Bind(self.OpenDandaoView, self))
	AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.INFO)
end

function AlchemyView:ShowIndexCallBack()

end

function AlchemyView:ReleaseCallBack()
	if self.alchemy_cell_list then
		for k, v in pairs(self.alchemy_cell_list) do
			v:DeleteMe()
		end
		self.alchemy_cell_list = nil
	end

	if self.alchemy_furnace_list then
		for k, v in pairs(self.alchemy_furnace_list) do
			v:DeleteMe()
		end
		self.alchemy_furnace_list = nil
	end

	self:CancelAlchemyTween()
end

function AlchemyView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushAlchemyAllView()
		elseif k == "flush_alchemy_normal" then
			self:FlushNormalArrayTween()
		end
	end
end

function AlchemyView:FlushAlchemyAllView()
	self:FlushAlchemyCellPanel()
	self:FlushAlchemyNormalArrayPanel()
	self:FlushAlchemyFurnacePanel()
	self.node_list.normal_effct:SetActive(false)
end

function AlchemyView:FlushNormalArrayTween()
	local cur_level = AlchemyWGData.Instance:GetNormalArrayLevel()
	local max_level = AlchemyWGData.Instance:GetNormalArrayCfgMaxLevel()
	local cur_level_cfg = AlchemyWGData.Instance:GetNormalArrayCfgByLevel(cur_level)
	self.node_list.normal_array_name.text.text = cur_level_cfg.name
	self.node_list.normal_effct:SetActive(false)

	self:CancelAlchemyTween()
	local show_tweener = DG.Tweening.DOTween.Sequence()
	local canvas_group = self.node_list["normal_array_bg"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local tween_alpah_1 = canvas_group:DoAlpha(1, 0.5, 0.15)
	local tween_alpah_2 = canvas_group:DoAlpha(0.5, 1, 0.15)
	local tween_alpah_3 = canvas_group:DoAlpha(1, 0.4, 0.15)
	local tween_alpah_4 = canvas_group:DoAlpha(0.4, 1, 0.15):OnComplete(function()
		self.node_list["normal_array_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_fz_" .. cur_level))
		self.node_list.normal_effct:SetActive(true)
	end)

	show_tweener:Append(tween_alpah_1)
	show_tweener:Append(tween_alpah_2)
	show_tweener:Append(tween_alpah_3)
	show_tweener:Append(tween_alpah_4)
	show_tweener:Append(self.node_list["normal_array_bg"].rect:DOScale(Vector3(1.5, 1.2, 1), 0.2))
	show_tweener:Append(self.node_list["normal_array_bg"].rect:DOScale(Vector3(1, 1, 1), 0.2))

	self.alchemy_tweener = show_tweener
end

function AlchemyView:CancelAlchemyTween()
    if self.alchemy_tweener then
        self.alchemy_tweener:Kill()
        self.alchemy_tweener = nil
    end
end

function AlchemyView:FlushAlchemyCellPanel()
	local danzao_list_data = AlchemyWGData.Instance:GetDanzaoAllInfo()
	for k, v in pairs(self.alchemy_cell_list) do
		v:SetData(danzao_list_data[k])
	end
end

function AlchemyView:FlushAlchemyNormalArrayPanel()
	local cur_level = AlchemyWGData.Instance:GetNormalArrayLevel()
	local max_level = AlchemyWGData.Instance:GetNormalArrayCfgMaxLevel()

	self.node_list["normal_array_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_fz_" .. cur_level))
	local cfg_level = cur_level == 0 and 1 or cur_level -- 0级没配置
	local cur_level_cfg = AlchemyWGData.Instance:GetNormalArrayCfgByLevel(cfg_level)
	self.node_list.normal_array_name.text.text = cur_level_cfg.name
end

function AlchemyView:FlushAlchemyFurnacePanel()
	for k, v in pairs(self.alchemy_furnace_list) do
		--v:SetData(danzao_list_data[k])
		local data = {}
		local level = AlchemyWGData.Instance:GetFurnaceLevelBySeq(k)
		data.level = level
		data.seq = k
		v:SetData(data)
	end
end

function AlchemyView:OpenNormalArrayView()
	ViewManager.Instance:Open(GuideModuleName.AlchemyNormalArrayView)
end

function AlchemyView:OpenDandaoView()
	ViewManager.Instance:Open(GuideModuleName.RoleView, TabIndex.role_refining)
end

function AlchemyView:PlayExpediteEffect(seq)
	if self.alchemy_cell_list and self.alchemy_cell_list[seq] then
		self.alchemy_cell_list[seq]:PlayEffect()
	end
end

------------------------ 丹灶item ------------------------
AlchemyCellItem = AlchemyCellItem or BaseClass(BaseRender)
function AlchemyCellItem:__init()

end

function AlchemyCellItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["lock_btn"], BindTool.Bind(self.OnClickLockBtn, self))
	XUI.AddClickEventListener(self.node_list["add_alchemy_btn"], BindTool.Bind(self.OnClickSowBtn, self)) 	
	XUI.AddClickEventListener(self.node_list["lingqu_btn"], BindTool.Bind(self.OnClickLingQu, self))
	XUI.AddClickEventListener(self.node_list["product_time_part"], BindTool.Bind(self.OnClickSpeedUp, self))
	self.product_item = ItemCell.New(self.node_list["product_item"])
	self.count_down_timer = ""
end

function AlchemyCellItem:__delete()
	if self.product_item then
		self.product_item:DeleteMe()
		self.product_item = nil
	end

	self:CancelTimer()
end

function AlchemyCellItem:HasCompose()
	return self.data ~= nil and self.data.is_unlock > 0 and self.data.compos_seq >= 0
end

function AlchemyCellItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.count_down_timer = "grow_finals_time" .. self.index
	local danzao_cfg = AlchemyWGData.Instance:GetDanzaoInfobySeq(self.data.seq)
	if IsEmptyTable(danzao_cfg) then
		return
	end

	self.node_list.effect_root:SetActive(false)
	if self.old_data == nil then
		self.old_data = self.data
	else
		if self.old_data.is_unlock == 0 and self.data.is_unlock > 0 then
			self.node_list.effect_root:SetActive(true)
		end
		self.old_data = self.data
	end

    self.node_list.cell_name.text.text = danzao_cfg.name
    self.node_list.lock_btn:SetActive(not (self.data.is_unlock > 0))
    self.node_list.add_alchemy_btn:SetActive(self.data.is_unlock > 0 and self.data.compos_seq < 0)
    self.node_list.product_part:SetActive(self.data.is_unlock > 0 and self.data.compos_seq >= 0)

	if self.data.is_unlock > 0 then -- 已开启
		self.node_list.buy_cout_str.text.text = ""
	else  --未开启
		local text_dec = ""
	    if danzao_cfg.condition_type == 1 then --等级类型
	        text_dec = string.format(Language.CountryAlchemy.DanzaoBuyStr1, danzao_cfg.condition_value)
	  	elseif danzao_cfg.condition_type == 2 then --元宝类型
	        text_dec = string.format(Language.CountryAlchemy.DanzaoBuyStr2, danzao_cfg.condition_value)
	 	elseif danzao_cfg.condition_type == 3 then  --灵玉类型
	 		text_dec = string.format(Language.CountryAlchemy.DanzaoBuyStr3, danzao_cfg.condition_value)
	 	elseif danzao_cfg.condition_type == 4 then  --直购类型
	 		local price = RoleWGData.GetPayMoneyStr(danzao_cfg.condition_value, danzao_cfg.rmb_type, danzao_cfg.rmb_seq)
	 		text_dec = string.format(Language.CountryAlchemy.DanzaoBuyStr4, price)
	  	end
	  	self.node_list.buy_cout_str.text.text = text_dec
	end

	self:FlushGrowTime()
end

function AlchemyCellItem:CancelTimer()
	if CountDownManager.Instance:HasCountDown(self.count_down_timer) then
		CountDownManager.Instance:RemoveCountDown(self.count_down_timer)
	end
end

function AlchemyCellItem:FlushGrowTime()
	if self:HasCompose() then  -- 正在练丹
		local compos_cfg = AlchemyWGData.Instance:GetComposeCfgSeq(self.data.compos_seq)
		local product_item_id = compos_cfg.product_item.item_id
		self.product_item:SetData({item_id = product_item_id})

		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local cfg_need_time = compos_cfg.need_time
		local reduce_time = AlchemyWGData.Instance:GetReduceTime()
		local need_time = cfg_need_time * (1 - reduce_time)
		local compose_end_time = self.data.start_compos_time + need_time
		local grow_time = compose_end_time - server_time

		self.node_list.product_progress_slider.slider.value = math.min((server_time - self.data.start_compos_time)  / need_time, 1)
		self.node_list.progress_text.text.text = TimeUtil.FormatSecond(compose_end_time - server_time)
		self:CancelTimer()
		if grow_time > 0 then
			CountDownManager.Instance:AddCountDown(self.count_down_timer, BindTool.Bind(self.FinalUpdateTimeCallBack, self), BindTool.Bind(self.FinalCompleteTimeCallBack, self), nil, grow_time, 1)
		else
			self:FinalCompleteTimeCallBack()
		end
	else
		self.product_item:SetData(nil)
		self.node_list.product_time_part:SetActive(true)
		self.node_list.lingqu_btn:SetActive(false)
		self.node_list.progress_text.text.text = ""
		self.node_list.product_progress_slider.slider.value = 0

	end
end

function AlchemyCellItem:FinalUpdateTimeCallBack(now_time, total_time)
	self.node_list.product_time_part:SetActive(true)
	self.node_list.lingqu_btn:SetActive(false)

	local compos_cfg = AlchemyWGData.Instance:GetComposeCfgSeq(self.data.compos_seq)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local cfg_need_time = compos_cfg.need_time
	local reduce_time = AlchemyWGData.Instance:GetReduceTime()
	local need_time = cfg_need_time * (1 - reduce_time)
	self.node_list.product_progress_slider.slider.value = math.min((server_time - self.data.start_compos_time)  / need_time, 1)
	self.node_list.progress_text.text.text = TimeUtil.FormatSecond(total_time - now_time)
end

function AlchemyCellItem:FinalCompleteTimeCallBack()
	self.node_list.product_time_part:SetActive(false)
	self.node_list.lingqu_btn:SetActive(true)
end

function AlchemyCellItem:OnClickLockBtn()
	if IsEmptyTable(self.data) then
		return
	end

	local danzao_cfg = AlchemyWGData.Instance:GetDanzaoInfobySeq(self.data.seq)
	if IsEmptyTable(danzao_cfg) then
		return
	end

	local text_dec = ""
	local is_enough_lv
    local is_enough_money

 	if danzao_cfg.condition_type == 1 then --等级类型
        text_dec = string.format(Language.CountryAlchemy.BuyTips1, danzao_cfg.condition_value)
        is_enough_lv = RoleWGData.Instance.role_vo.level >= danzao_cfg.condition_value
  	elseif danzao_cfg.condition_type == 2 then --元宝类型
        text_dec = string.format(Language.CountryAlchemy.BuyTips2, danzao_cfg.condition_value)
        is_enough_money = RoleWGData.Instance:GetIsEnoughAllGold(danzao_cfg.condition_value)
 	elseif danzao_cfg.condition_type == 3 then  --灵玉类型
 		text_dec = string.format(Language.CountryAlchemy.BuyTips3, danzao_cfg.condition_value)
 		is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(danzao_cfg.condition_value)
 	elseif danzao_cfg.condition_type == 4 then  --直购类型
 		local price = RoleWGData.GetPayMoneyStr(danzao_cfg.condition_value, danzao_cfg.rmb_type, danzao_cfg.rmb_seq)
 		text_dec = string.format(Language.CountryAlchemy.BuyTips4, price)
  	end

    local ok_func = function ()
        if danzao_cfg.condition_type == 1 then
 			if is_enough_lv then
				AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.DANZAO_UNLOCK, danzao_cfg.seq)
            else
            	SysMsgWGCtrl.Instance:ErrorRemind(Language.CountryAlchemy.NoEnoughlv)
            end
        elseif danzao_cfg.condition_type == 2 or danzao_cfg.condition_type == 3 then
            if is_enough_money then
            	AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.DANZAO_UNLOCK, danzao_cfg.seq)
            else
                VipWGCtrl.Instance:OpenTipNoGold()
            end
        elseif danzao_cfg.condition_type == 4 then
        	RechargeWGCtrl.Instance:Recharge(danzao_cfg.condition_value, danzao_cfg.rmb_type, danzao_cfg.rmb_seq)
        end
    end

    TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
end

function AlchemyCellItem:OnClickSowBtn()
	if self.data and self.data.is_unlock > 0 then
		ViewManager.Instance:Open(GuideModuleName.AlchemyComposeView, nil, "all", {field_seq = self.data.seq})
	end
end

function AlchemyCellItem:OnClickLingQu()
	if self:HasCompose() then  -- 正在练丹
		local compos_cfg = AlchemyWGData.Instance:GetComposeCfgSeq(self.data.compos_seq)
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local need_time = compos_cfg.need_time
		local reduce_time = AlchemyWGData.Instance:GetReduceTime()
		local compose_end_time = self.data.start_compos_time + (need_time * (1 - reduce_time))
		local grow_time = compose_end_time - server_time
		if grow_time <= 0 then
			AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.COMPOS_FINISIH, self.data.seq)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.CountryAlchemy.StuffNotEnough)
		end
	end
end

function AlchemyCellItem:OnClickSpeedUp()
	if self.data and self:HasCompose() then
		ViewManager.Instance:Open(GuideModuleName.AlchemySpeedUpView, nil, "all", {speed_seq = self.data.seq})
	end
end

function AlchemyCellItem:PlayEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_zhuanpanka_huitan_cheng)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["ex_effect_root"].transform)
end

------------------------------丹炉AlchemyFurnaceCell---------------------
AlchemyFurnaceCell = AlchemyFurnaceCell or BaseClass(BaseRender)

function AlchemyFurnaceCell:__init()
	XUI.AddClickEventListener(self.node_list["root"], BindTool.Bind(self.OnClickUpFurnace, self))
end

function AlchemyFurnaceCell:__delete()

end

function AlchemyFurnaceCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local furnace_cfg
	if self.data.level == 0 then
		furnace_cfg = AlchemyWGData.Instance:GetFurnaceCfgBySeqAndLevel(self.data.seq, self.data.level + 1)
		self.node_list.reduce_time.text.text = string.format(Language.CountryAlchemy.DanSpeedTime, 0)
	else
		furnace_cfg = AlchemyWGData.Instance:GetFurnaceCfgBySeqAndLevel(self.data.seq, self.data.level)
		self.node_list.reduce_time.text.text = string.format(Language.CountryAlchemy.DanSpeedTime, furnace_cfg.reduce_time_scale / 100)
	end
	self.node_list.name.text.text = furnace_cfg.name
	self.node_list.furnace_lv.text.text = self.data.level == 0 and "" or self.data.level
	self.node_list["furnace_img"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_" .. self.data.level))
end

function AlchemyFurnaceCell:OnClickUpFurnace()
	if IsEmptyTable(self.data) then
		return
	end

	ViewManager.Instance:Open(GuideModuleName.AlchemyFurnaceView, nil, "all", {furnace_seq = self.data.seq})
end