﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Profiling_ProfilerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Profiling.Profiler), typeof(System.Object));
		<PERSON><PERSON>RegFunction("SetAreaEnabled", SetAreaEnabled);
		<PERSON><PERSON>RegFunction("GetAreaEnabled", GetAreaEnabled);
		<PERSON><PERSON>RegFunction("AddFramesFromFile", AddFramesFromFile);
		L.RegFunction("BeginThreadProfiling", BeginThreadProfiling);
		L.RegFunction("EndThreadProfiling", EndThreadProfiling);
		L<PERSON>RegFunction("BeginSample", BeginSample);
		<PERSON><PERSON>Function("EndSample", EndSample);
		L.RegFunction("GetRuntimeMemorySizeLong", GetRuntimeMemorySizeLong);
		<PERSON><PERSON>RegFunction("GetMonoHeapSizeLong", GetMonoHeapSizeLong);
		<PERSON><PERSON>Function("GetMonoUsedSizeLong", GetMonoUsedSizeLong);
		<PERSON><PERSON>RegFunction("SetTempAllocatorRequestedSize", SetTempAllocatorRequestedSize);
		L.RegFunction("GetTempAllocatorSize", GetTempAllocatorSize);
		L.RegFunction("GetTotalAllocatedMemoryLong", GetTotalAllocatedMemoryLong);
		L.RegFunction("GetTotalUnusedReservedMemoryLong", GetTotalUnusedReservedMemoryLong);
		L.RegFunction("GetTotalReservedMemoryLong", GetTotalReservedMemoryLong);
		L.RegFunction("GetTotalFragmentationInfo", GetTotalFragmentationInfo);
		L.RegFunction("GetAllocatedMemoryForGraphicsDriver", GetAllocatedMemoryForGraphicsDriver);
		L.RegFunction("EmitFrameMetaData", EmitFrameMetaData);
		L.RegFunction("EmitSessionMetaData", EmitSessionMetaData);
		L.RegFunction("SetCategoryEnabled", SetCategoryEnabled);
		L.RegFunction("IsCategoryEnabled", IsCategoryEnabled);
		L.RegFunction("GetCategoriesCount", GetCategoriesCount);
		L.RegFunction("GetAllCategories", GetAllCategories);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("supported", get_supported, null);
		L.RegVar("logFile", get_logFile, set_logFile);
		L.RegVar("enableBinaryLog", get_enableBinaryLog, set_enableBinaryLog);
		L.RegVar("maxUsedMemory", get_maxUsedMemory, set_maxUsedMemory);
		L.RegVar("enabled", get_enabled, set_enabled);
		L.RegVar("enableAllocationCallstacks", get_enableAllocationCallstacks, set_enableAllocationCallstacks);
		L.RegVar("areaCount", get_areaCount, null);
		L.RegVar("usedHeapSizeLong", get_usedHeapSizeLong, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetAreaEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Profiling.ProfilerArea arg0 = (UnityEngine.Profiling.ProfilerArea)ToLua.CheckObject(L, 1, typeof(UnityEngine.Profiling.ProfilerArea));
			bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Profiling.Profiler.SetAreaEnabled(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAreaEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Profiling.ProfilerArea arg0 = (UnityEngine.Profiling.ProfilerArea)ToLua.CheckObject(L, 1, typeof(UnityEngine.Profiling.ProfilerArea));
			bool o = UnityEngine.Profiling.Profiler.GetAreaEnabled(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddFramesFromFile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			UnityEngine.Profiling.Profiler.AddFramesFromFile(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BeginThreadProfiling(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			UnityEngine.Profiling.Profiler.BeginThreadProfiling(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EndThreadProfiling(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			UnityEngine.Profiling.Profiler.EndThreadProfiling();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BeginSample(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				UnityEngine.Profiling.Profiler.BeginSample(arg0);
				return 0;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 2);
				UnityEngine.Profiling.Profiler.BeginSample(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Profiling.Profiler.BeginSample");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EndSample(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			UnityEngine.Profiling.Profiler.EndSample();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRuntimeMemorySizeLong(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 1);
			long o = UnityEngine.Profiling.Profiler.GetRuntimeMemorySizeLong(arg0);
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMonoHeapSizeLong(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			long o = UnityEngine.Profiling.Profiler.GetMonoHeapSizeLong();
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMonoUsedSizeLong(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			long o = UnityEngine.Profiling.Profiler.GetMonoUsedSizeLong();
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTempAllocatorRequestedSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			uint arg0 = (uint)LuaDLL.luaL_checknumber(L, 1);
			bool o = UnityEngine.Profiling.Profiler.SetTempAllocatorRequestedSize(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTempAllocatorSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			uint o = UnityEngine.Profiling.Profiler.GetTempAllocatorSize();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTotalAllocatedMemoryLong(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			long o = UnityEngine.Profiling.Profiler.GetTotalAllocatedMemoryLong();
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTotalUnusedReservedMemoryLong(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			long o = UnityEngine.Profiling.Profiler.GetTotalUnusedReservedMemoryLong();
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTotalReservedMemoryLong(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			long o = UnityEngine.Profiling.Profiler.GetTotalReservedMemoryLong();
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTotalFragmentationInfo(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Unity.Collections.NativeArray<int> arg0 = StackTraits<Unity.Collections.NativeArray<int>>.Check(L, 1);
			long o = UnityEngine.Profiling.Profiler.GetTotalFragmentationInfo(arg0);
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAllocatedMemoryForGraphicsDriver(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			long o = UnityEngine.Profiling.Profiler.GetAllocatedMemoryForGraphicsDriver();
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EmitFrameMetaData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			System.Guid arg0 = StackTraits<System.Guid>.Check(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			System.Array arg2 = (System.Array)ToLua.CheckObject<System.Array>(L, 3);
			UnityEngine.Profiling.Profiler.EmitFrameMetaData(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EmitSessionMetaData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			System.Guid arg0 = StackTraits<System.Guid>.Check(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			System.Array arg2 = (System.Array)ToLua.CheckObject<System.Array>(L, 3);
			UnityEngine.Profiling.Profiler.EmitSessionMetaData(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCategoryEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Unity.Profiling.ProfilerCategory arg0 = StackTraits<Unity.Profiling.ProfilerCategory>.Check(L, 1);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Profiling.Profiler.SetCategoryEnabled(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsCategoryEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Unity.Profiling.ProfilerCategory arg0 = StackTraits<Unity.Profiling.ProfilerCategory>.Check(L, 1);
			bool o = UnityEngine.Profiling.Profiler.IsCategoryEnabled(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCategoriesCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			uint o = UnityEngine.Profiling.Profiler.GetCategoriesCount();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAllCategories(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes<Unity.Profiling.ProfilerCategory[]>(L, 1))
			{
				Unity.Profiling.ProfilerCategory[] arg0 = ToLua.ToStructArray<Unity.Profiling.ProfilerCategory>(L, 1);
				UnityEngine.Profiling.Profiler.GetAllCategories(arg0);
				return 0;
			}
			else if (count == 1 && TypeChecker.CheckTypes<Unity.Collections.NativeArray<Unity.Profiling.ProfilerCategory>>(L, 1))
			{
				Unity.Collections.NativeArray<Unity.Profiling.ProfilerCategory> arg0 = StackTraits<Unity.Collections.NativeArray<Unity.Profiling.ProfilerCategory>>.To(L, 1);
				UnityEngine.Profiling.Profiler.GetAllCategories(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Profiling.Profiler.GetAllCategories");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supported(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Profiling.Profiler.supported);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_logFile(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Profiling.Profiler.logFile);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableBinaryLog(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Profiling.Profiler.enableBinaryLog);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxUsedMemory(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Profiling.Profiler.maxUsedMemory);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enabled(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Profiling.Profiler.enabled);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableAllocationCallstacks(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Profiling.Profiler.enableAllocationCallstacks);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_areaCount(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Profiling.Profiler.areaCount);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_usedHeapSizeLong(IntPtr L)
	{
		try
		{
			LuaDLL.tolua_pushint64(L, UnityEngine.Profiling.Profiler.usedHeapSizeLong);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_logFile(IntPtr L)
	{
		try
		{
			string arg0 = ToLua.CheckString(L, 2);
			UnityEngine.Profiling.Profiler.logFile = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableBinaryLog(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Profiling.Profiler.enableBinaryLog = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxUsedMemory(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Profiling.Profiler.maxUsedMemory = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enabled(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Profiling.Profiler.enabled = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableAllocationCallstacks(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Profiling.Profiler.enableAllocationCallstacks = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

