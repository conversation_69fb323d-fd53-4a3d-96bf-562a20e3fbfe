require("game/tianshenjuexing/tianshen_juexing_wg_data")
require("game/tianshenjuexing/tianshen_juexing_view")

TianShenJuexingWGCtrl = TianShenJuexingWGCtrl or BaseClass(BaseWGCtrl)

function TianShenJuexingWGCtrl:__init()
	if TianShenJuexingWGCtrl.Instance then
		ErrorLog("[TianShenJuexingWGCtrl] attempt to create singleton twice!")
		return
	end
	TianShenJuexingWGCtrl.Instance = self

	self.view = TianShenJuexingView.New(GuideModuleName.TianShenJuexing)
    self.data = TianShenJuexingWGData.New()
	self:RegisterAllProtocols()
    self.is_funopen = false
    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
end

function TianShenJuexingWGCtrl:__delete()
	TianShenJuexingWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
    end

    if self.open_fun_change then
        GlobalEventSystem:UnBind(self.open_fun_change)
        self.open_fun_change = nil
    end
end

function TianShenJuexingWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCTianShenAwakenInfo, "OnSCTianShenAwakenInfo")
    self:RegisterProtocol(CSTianShenAwakeOper)
end

function TianShenJuexingWGCtrl:OnSCTianShenAwakenInfo(protocol)
    self.data:SetAwakenInfo(protocol)

    if self.data:GetShenWuFuncIsOpen() then
        FunOpen.Instance:ForceOpenFunByName(FunName.NewAppearanceUpgradeLingChong)
        RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_LingChong)
        self.is_funopen = false
        self.view:Close()
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHENBINGAWAKEN_ACTIVITY, ACTIVITY_STATUS.CLOSE)
    end

    self.view:Flush()
    MainuiWGCtrl.Instance:FlushView(0, "mainui_xxym_tip")
    if self.data:GetShenWuFuncIsOpen() then
        NewAppearanceWGCtrl.Instance:FlushView(nil, "check_tabbar")
    end

    RemindManager.Instance:Fire(RemindName.TianShenAwaken)
end

function TianShenJuexingWGCtrl:SetIsFunopen(active)
    self.is_funopen = active
end

function TianShenJuexingWGCtrl:SendAwakenOper(opear_type,task_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenAwakeOper)
    protocol.opear_type = opear_type
    protocol.task_type = task_type or 0
	protocol:EncodeAndSend()
end

function TianShenJuexingWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.TianShenJuexing then 
        local is_open = FunOpen.Instance:GetFunIsOpened(FunName.TianShenJuexing)
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHENBINGAWAKEN_ACTIVITY, state)
        MainuiWGCtrl.Instance:FlushView(0, "mainui_xxym_tip")
    end
end