require("game/rolebag/rolebag_wg_data")
require("game/rolebag/rolebag_view")
require("game/rolebag/rolebag_bag")
require("game/rolebag/rolebag_storge")
require("game/rolebag/rolebag_auto_sell")
require("game/rolebag/rolebag_melting_enter_view")
require("game/rolebag/rolebag_melting")
require("game/rolebag/role_bag_clean")
require("game/rolebag/rolebag_stuff_bag")
require("game/rolebag/rolebag_gurad_show_view")
require("game/rolebag/guard_compose_view")
require("game/rolebag/rolebag_yinpiao_exchange")
require("game/rolebag/special_bag_reslove_view")
require("game/rolebag/longzhu/rolebag_longzhu")


-- 背包系统
RoleBagWGCtrl = RoleBagWGCtrl or BaseClass(BaseWGCtrl)
function RoleBagWGCtrl:__init()
	if RoleBagWGCtrl.Instance then
		error("[RoleBagWGCtrl]:Attempt to create singleton twice!")
	end
	RoleBagWGCtrl.Instance = self

	self.data = RoleBagWGData.New()
	self.view = RoleBagView.New(GuideModuleName.Bag)

	self.auto_sell_view = RoleBagAutoSell.New()
	self.equip_melting_enter = RoleBagMeltingEnterView.New(GuideModuleName.RoleBagMeltingEnterView)
	self.equip_melting = RoleBagMeltingView.New(GuideModuleName.RoleBagViewMeltingView) 		--一键装备熔炼
	self.rolebag_clean = BagCleanView.New()
	self.gurad_show_view = GuradShowVeiw.New(GuideModuleName.GuradShwoView)
	self.guard_compose_view = GuardComposeView.New(GuideModuleName.GuardComposeView)
	self.yinpiao_exchange_view = YinPiaoExchangeView.New(GuideModuleName.YinPiaoExchangeView)
	self.special_bag_reslove_view = SpecialBagResloveView.New()

	self.item_data_event = BindTool.Bind(self.BagDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)

	self:RegisterAllProtocals()
end

function RoleBagWGCtrl:FlushModel()
	if self.view and self.view:IsOpen() then
		self.view:Flush(nil,"flush_rolebag_model")
		--self.view:FlushModel()
	end
end

function RoleBagWGCtrl:__delete()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	self.item_data_event = nil

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.yinpiao_exchange_view then
		self.yinpiao_exchange_view:DeleteMe()
		self.yinpiao_exchange_view = nil
	end

	if nil ~= self.auto_sell_view then
		self.auto_sell_view:DeleteMe()
		self.auto_sell_view = nil
	end

	if self.equip_melting_enter then
		self.equip_melting_enter:DeleteMe()
		self.equip_melting_enter = nil
	end

	if self.equip_melting then
		self.equip_melting:DeleteMe()
		self.equip_melting = nil
	end

	if self.rolebag_clean then
		self.rolebag_clean:DeleteMe()
		self.rolebag_clean = nil
	end

	if self.role_bag_clean_flag then
		GlobalTimerQuest:CancelQuest(self.role_bag_clean_flag)
		self.role_bag_clean_flag = nil
	end

	if self.clean_fall_cache then
    	GlobalTimerQuest:CancelQuest(self.clean_fall_cache)
    	self.clean_fall_cache = nil
    end

    if self.gurad_show_view then
    	self.gurad_show_view:DeleteMe()
    	self.gurad_show_view = nil
    end

    if self.guard_compose_view then
    	self.guard_compose_view:DeleteMe()
    	self.guard_compose_view = nil
    end

	self.special_bag_reslove_view:DeleteMe()
	self.special_bag_reslove_view = nil

	if self.tunshi_timer then
		GlobalTimerQuest:CancelQuest(self.tunshi_timer)
		self.tunshi_timer = nil
	end
	
	RoleBagWGCtrl.Instance = nil
end

function RoleBagWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCShenluInfo, "OnShenluInfo")

	self:RegisterProtocol(CSShenluRonglian)

	-- 分解
	self:RegisterProtocol(CSItemDecompose)
	-- 虚拟背包道具分解
	self:RegisterProtocol(CSItemDecomposeReq)


	---------熔炼------------
	self:RegisterProtocol(CSEquipReqMelt)								--1787  熔炼装备
	self:RegisterProtocol(CSEquipReqMeltInfo) 		                    --1786  请求熔炼信息
	self:RegisterProtocol(SCEquipReqMeltInfo,"OnSCEquipReqMeltInfo") 	----1720 下发熔炼信息 返回1786的请求
	self:RegisterProtocol(SCEquipReqMelt,"OnSCEquipReqMelt") 	        --1721  熔炼操作返回 返回1787的请求

	self:RegisterProtocol(SCKnapsackGridNotEnough, "OnSCKnapsackGridNotEnough") 		--16717 背包 - 空间不足

	
end

function RoleBagWGCtrl:MainuiOpenCreate()

end

function RoleBagWGCtrl:Open(tab_index, param_t)
	self.view:Open(tab_index, param_t)

end

function RoleBagWGCtrl:CloseView()
	if self.view and self.view:IsOpen() then
		self.view:Close()
	end
end

function RoleBagWGCtrl:Flush(index, key, value)
	if self.view and self.view:IsOpen() then
		self.view:Flush(index, key, value)
	end
end

function RoleBagWGCtrl:OnShenluInfo(protocol)
	self.data:SetShenluInfo(protocol)
	--self.view:Flush(0, "forge_succeed")
	-- -- Remind.Instance:DoRemind(RemindId.rolebag_ronglian)
end

--打开一键熔炼按钮
function RoleBagWGCtrl:OpenMeltView()
	if self.equip_melting_enter then
		self.equip_melting_enter:Open()
	end
end

function RoleBagWGCtrl:SendShenluRonglian(ronglian_equip_index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShenluRonglian)
	protocol.equip_index_count = #ronglian_equip_index_list
	protocol.ronglian_equip_index_list = ronglian_equip_index_list
	protocol:EncodeAndSend()
end

function RoleBagWGCtrl:SendItemDecompose(index, num)
	local protocol = ProtocolPool.Instance:GetProtocol(CSItemDecompose)
	protocol.index = index
	protocol.num = num
	protocol:EncodeAndSend()
end

function RoleBagWGCtrl:Slotholeindex(index)
	self.data:SetZBSlotholeIndex(index)
end

function RoleBagWGCtrl:OpenAutoSellView()
	self.auto_sell_view:Open()
end

function RoleBagWGCtrl:OpenRoleBagClean(item_objid_list)
	local scene_type = Scene.Instance:GetSceneType()
	if Auto_Pick_SceneType[scene_type] then
		Scene.ScenePickItem(item_objid_list)
		return
	end
	local obj = Scene.Instance:GetObj(item_objid_list[1])
	local vo = obj:GetVo()
	if vo then
	    if self.fall_item_ori_id ~= vo.monster_id then
	        self.fall_item_ori_id = vo.monster_id
	        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotBagRoom)
	    end

	    if self.clean_fall_cache then
	    	GlobalTimerQuest:CancelQuest(self.clean_fall_cache)
	    	self.clean_fall_cache = nil
	    end

	    self.clean_fall_cache = GlobalTimerQuest:AddDelayTimer(function()
			self.fall_item_ori_id = nil
		end,60)
	end
end

function RoleBagWGCtrl:OpenRoleBagCleanTips(bag_type)
	if not self.rolebag_clean:IsOpen() then
		self.rolebag_clean:SetDataAndOpen(bag_type)
	end
	-- if RoleBagWGData.Instance:GetBagCleanFlag() then
	-- 	RoleBagWGData.Instance:SetRoleBagCleanFlag(false)

	-- end

	-- self.role_bag_clean_flag = GlobalTimerQuest:AddDelayTimer(function()
	-- 	RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
	-- end,60)
end

function RoleBagWGCtrl:CheckAutoRongLian()
	local auto_ronglian = SettingWGData.Instance:GetGuajiDataTable(SETTING_TYPE.GUAJI_RONGLIAN)-- 自动熔炼装备
	if not auto_ronglian then
		return
	end

	local data_list = RoleBagWGData.Instance:GetBagEquipAutoMeltList()
	local ronglian_list = {}
	for _,data in pairs(data_list) do
		ronglian_list[#ronglian_list + 1] = {melt_index = data.index}
	end

	if #ronglian_list > 0 then
		self:OnReqEquipMelt(#ronglian_list, ronglian_list, 1)
	end

	return #ronglian_list > 0
end

-------------------------------------------
-- 熔炼
function RoleBagWGCtrl:OnSCEquipReqMelt(protocol)
	if protocol.melt_ret == 1 then
		if self.equip_melting_enter:IsOpen() then
			self.equip_melting_enter:CacheSuccessMeltingNum(protocol.melt_count)
		end

		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Bag.MeltingError1, protocol.melt_count))
	else
		if protocol.melt_is_auto == 0 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Bag.MeltingError2) --熔炼装备失败
		end
	end
end

function RoleBagWGCtrl:OnSCEquipReqMeltInfo(protocol)
	self.data:SetEquipMeltInfo(protocol)

	if self.equip_melting and self.equip_melting:IsOpen() then
		self.equip_melting:Flush()
	end

	if self.equip_melting_enter:IsOpen() then
		self.equip_melting_enter:Flush()
	end
end

function RoleBagWGCtrl:OnFluhEquipMeltView()
	if self.equip_melting and self.equip_melting:IsOpen() then
		self.equip_melting:Flush()
	end
end

--请求熔炼装备信息：熔炼等级
function RoleBagWGCtrl:OnReqEquipMeltInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipReqMeltInfo)
	protocol:EncodeAndSend()
end

--请求熔炼装备
function RoleBagWGCtrl:OnReqEquipMelt(melt_num,melt_list, is_auto)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipReqMelt)
	protocol.melt_num = melt_num
	protocol.melt_backpack_index_list = melt_list
	protocol.is_auto = is_auto or 0
	protocol:EncodeAndSend()
end

function RoleBagWGCtrl:OpenGuradShowView()
	if not self.gurad_show_view:IsOpen() then
		self.gurad_show_view:Open()
	end
end

function RoleBagWGCtrl:OpenGuardComposeView(producd_id)
	self.guard_compose_view:SetData(producd_id)
	ViewManager.Instance:Open(GuideModuleName.GuardComposeView)
end

function RoleBagWGCtrl:FlushGuardComposeView()
	self.guard_compose_view:Flush()
end

function RoleBagWGCtrl:OpenDisposeListUpdate(list)
	Runner.Instance:AddRunObj(self)
	self.update_list = list
end

function RoleBagWGCtrl:Update()
	for i=1,10 do
		local cfg = table.remove(self.update_list, 1)
		if cfg then
			local item_id = ItemWGData.Instance:GetItemIdByIndex(cfg.index)
			if item_id > 0 and item_id == cfg.item_id then
				FunctionGuide.Instance:OnItemDataChange(cfg.item_id, cfg.index, GameEnum.DATALIST_CHANGE_REASON_CHECK
						, PUT_REASON_TYPE.PUT_REASON_ChECK_TIP)
			end
		else
			Runner.Instance:RemoveRunObj(self)
			break
		end
	end
end

-- 龙玺背包状态刷新
function RoleBagWGCtrl:FlushLongXiState()
	if self.view and self.view:IsOpen() then
		self.view:Flush(nil,"longxi")
	end
end

--请求分解道具 by 背包类型
function RoleBagWGCtrl:ReqResloveItemList(bag_type, reslove_list)
	-- print_error("-----请求分解道具-----", bag_type, reslove_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSItemDecomposeReq)
	protocol.bag_type = bag_type or 0
	protocol.reslove_list = reslove_list or {}
	protocol:EncodeAndSend()
end

-- 特殊背包分解
-- info = {bag_type, param_1, param_2}
function RoleBagWGCtrl:OpenSpecialBagResloveView(info)
	self.special_bag_reslove_view:SetDataAndOpen(info)
end

function RoleBagWGCtrl:FlushSpecialBagResloveView(bag_type)
	if self.special_bag_reslove_view and self.special_bag_reslove_view:IsOpen() then
		local info = self.special_bag_reslove_view.info
		if info and info.bag_type == bag_type then
			self.special_bag_reslove_view:Flush()
		end
	end
end

function RoleBagWGCtrl:OnEISuitUpLevelResult(result, sit_index)
	if 0 == result then
		self.view:StopLevelOperator()
	elseif 1 == result  then
        if self.view:IsAutoUpLevel() then
            self.view:AutoUpLevelUpOnce()
        end

        self.view:PlayEiUseEffect(UIEffectName.s_shengji)
	end
end

--获得是否选中自动吞噬
function RoleBagWGCtrl:GetIsAutoTunShi()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	return PlayerPrefsUtil.GetInt("role_auto_tunshi" .. role_id) == 1
end

--设置自动吞噬
function RoleBagWGCtrl:SetIsAutoTunShi()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local save_num = PlayerPrefsUtil.GetInt("role_auto_tunshi" .. role_id)
	local value = save_num == COMMON_GAME_ENUM.ONE and COMMON_GAME_ENUM.ZERO or COMMON_GAME_ENUM.ONE
	PlayerPrefsUtil.SetInt("role_auto_tunshi" .. role_id, value)
end

--设置蓝紫装备的自动吞噬
function RoleBagWGCtrl:AutoTunShiEquip()
	local destroy_item_list = {}
	local bag_item_list = ItemWGData.Instance:GetBagItemDataList()
	for k, v in pairs(bag_item_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and item_cfg.color <= GameEnum.ITEM_COLOR_PURPLE and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			if not EquipWGData.Instance:GetIsBetterEquip(v) then
				destroy_item_list[#destroy_item_list + 1] = {melt_index = v.index}
			end
		end
	end

	if #destroy_item_list > 0 then
		self:OnReqEquipMelt(#destroy_item_list, destroy_item_list)
	end

	self.tunshi_timer = nil
end

--背包物品发生改变监听
function RoleBagWGCtrl:BagDataChange(item_id, index, reason, put_reason, old_num, new_num)
    local is_add = reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	                (reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num)

	if not is_add then
		return
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return
	end

	if not (big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT or item_cfg.recycltype == 2) then
		return
	end

	if self:GetIsAutoTunShi() and item_cfg.color <= GameEnum.ITEM_COLOR_PURPLE then
		if not self.tunshi_timer then --自动吞噬延迟一秒  防止卡顿
			self.tunshi_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.AutoTunShiEquip, self), 1)
		end
	end
end

function RoleBagWGCtrl:OnSCKnapsackGridNotEnough(protocol)--协议16717
	self:OpenRoleBagCleanTips(protocol.bag_type)
 end