FightSoulBoneHeightTips = FightSoulBoneHeightTips or BaseClass(SafeBaseView)
function FightSoulBoneHeightTips:__init()
	self.view_layer = UiLayer.Pop
    self.view_name = "FightSoulBoneHeightTips"
	self:SetMaskBg(true)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(886, 482)})
    self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_fight_soul_bone_height_tips")
end

function FightSoulBoneHeightTips:__delete()

end

function FightSoulBoneHeightTips:ReleaseCallBack()
    if nil ~= self.cur_item then
        self.cur_item:DeleteMe()
        self.cur_item = nil
    end

    if nil ~= self.next_item then
        self.next_item:DeleteMe()
        self.next_item = nil
    end

    self.select_data = nil
    self.sure_callback = nil
end

function FightSoulBoneHeightTips:LoadCallBack()
    if nil == self.cur_item then
        self.cur_item = ItemCell.New(self.node_list.cur_item)
    end

    if nil == self.next_item then
        self.next_item = ItemCell.New(self.node_list.target_item)
    end

    XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind(self.ClickOkBtn, self))
end

function FightSoulBoneHeightTips:SetDataAndOpen(select_data, sure_callback, cur_show_data, next_show_data)
    self.select_data = select_data
    self.sure_callback = sure_callback
	self.cur_show_data = cur_show_data
	self.next_show_data = next_show_data

    self:Open()
end

function FightSoulBoneHeightTips:OnFlush()
	local cur_show_data, next_show_data
	if self.select_data ~= nil then
		cur_show_data, next_show_data = FightSoulWGData.Instance:GetBoneComposeShowItemData(self.select_data)
	elseif self.cur_show_data and self.next_show_data then
		cur_show_data = self.cur_show_data
		next_show_data = self.next_show_data
	end

	if IsEmptyTable(cur_show_data) or IsEmptyTable(next_show_data) then
		self:ClickOkBtn()
    	return
    end

	self.cur_item:SetData(cur_show_data)
	self.next_item:SetData(next_show_data)

	local cur_item_name = ItemWGData.Instance:GetItemName(cur_show_data.item_id, nil, true)
	local next_item_name = ItemWGData.Instance:GetItemName(next_show_data.item_id, nil, true)
	self.node_list.tran_desc.text.text = string.format(Language.FightSoul.ComposeHeightDesc, cur_item_name, next_item_name)
end

function FightSoulBoneHeightTips:ClickOkBtn()
    if self.sure_callback then
        self.sure_callback()
    end

    self:Close()
end
