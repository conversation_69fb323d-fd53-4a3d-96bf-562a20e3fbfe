MapWGData = MapWGData or BaseClass()

-- 世界地图
MapWGData.WORLDCFG = {
	[1] = 1001,		-- 新手村					
	[2] = 1002,		-- 鬼界					
	[3] = 1003,		-- 主城					
	[4] = 1004,		-- 雪山					
	[5] = 1005,		-- 登神派					
	[6] = "vip_boss",		-- 原始深林-打开 魔王-魔界纵横界面			
	[7] = "world_boss",		-- 熔浆地区-打开 魔王-猎魔深渊界面		
	-- [8] = 1005,							
	-- [9] = 1008,			
	-- [10] = 1010				
}



function MapWGData:__init()
	if MapWGData.Instance then
		print_error("[MapWGData] Attempt to create singleton twice!")
		return
	end
	MapWGData.Instance = self

	self.map_config = {}

	self.info_table = {}
	self.icon_table = {}

	self.m_map_info_cfg = ConfigManager.Instance:GetAutoConfig("guaji_pos_auto").map_info
	self.m_gather_list_cfg = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list
	self.bottom_color = ListToMap(ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto").bottom_color, "scene_id")
	
	-- 熔浆地区 世界boss 猎魔深渊 
	self.world_boss_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("worldboss_auto").world_boss_boss_info, "scene_id")
	-- 原始森林 vipBoss 魔界纵横
	self.vip_boss_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("worldboss_auto").vipBoss_boss_info, "scene_id")

	
	self.npc_cfg_list = {} 			--npc
	self.door_cfg_list = {}			--传送门
	self.monster_cfg_list = {}		--怪物
	self.last_map_id = -1
end

function MapWGData:__delete()
	MapWGData.Instance = nil
	self.last_map_id = -1
end

function MapWGData:GetBossCfg(scene_id)
	if self.world_boss_cfg[scene_id] then
		return self.world_boss_cfg[scene_id], "world_boss"
	end
	if self.vip_boss_cfg[scene_id] then
		return self.vip_boss_cfg[scene_id], "vip_boss"
	end
end

function MapWGData:GetMapBottomColor(scene_id)
	if not self.bottom_color[scene_id] then
		return 1
	end
	return self.bottom_color[scene_id].bottom_par
end

function MapWGData:GetMapConfig(map_id)
	if not self.map_config[map_id] then
		if type(map_id) == "number" then
			self.map_config[map_id] = ConfigManager.Instance:GetSceneConfig(map_id)
			if not self.map_config[map_id] then
				print_error("Can't find scene_" .. map_id .. "config")
				return
			end
		else
			return
		end
	end
	return self.map_config[map_id]
end

function MapWGData:GetInfoByType(info_type)
	if not info_type then
		return self.info_table
	else
		local temp_table = {}
		local count = 0
		for _, v in pairs(self.info_table) do
			if (v.obj_type == info_type) then
				count = count + 1
				temp_table[count] = v
			end
		end
		return temp_table, count
	end
end

function MapWGData:GetInfoByIndex(index)
	if not index then
		return self.info_table
	else
		return self.info_table[index]
	end
end

function MapWGData:SetInfo(list)
	self:ClearInfo()
	self.info_table = list
	local count = 0
	for _, v in pairs(self.info_table) do
		count = count + 1
	end
end

function MapWGData:ClearInfo()
	if self.info_table then
		for _, v in pairs(self.info_table) do
			ResMgr:Destroy(v.obj)
		end
		self.info_table = {}
	end
end

function MapWGData:SetIcon(list)
	self.icon_table = list
end

function MapWGData:ClearIcon()
	if self.icon_table then
		for _, v in pairs(self.icon_table) do
			ResMgr:Destroy(v.obj)
		end
		self.icon_table = {}
	end
end

function MapWGData:GetNpcIcon()
	local temp_table = {}
	local count = 0
	for _, v in pairs(self.icon_table) do
		if v.npc_id then
			count = count + 1
			temp_table[count] = v
		end
	end
	return temp_table, count
end

function MapWGData:GetMonster(monster_id)
	local monster_info = ConfigManager.Instance:GetAutoConfig("guaji_pos_auto").map_info
	for k,v in pairs(monster_info) do
		if monster_id == v.monster_id then
			return v
		end
	end
	return nil
end

function MapWGData:GetGatherById( gather_id )
	return self.m_gather_list_cfg[gather_id]
end

function MapWGData:SetSceneLineInfo(protocol)
	self.map_max_line_num = protocol.max_line
	self.role_cur_line = protocol.cur_line
end

function MapWGData:GetMapMaxLineNum()
	return self.map_max_line_num or 0
end

function MapWGData:GetMapCurLine()
	return self.role_cur_line
end

-- (当前地图怪物按钮排序)
function MapWGData:GetSceneMonsterSort(list)
	if list then
		local new_list = nil
		local last_monster_id = 0
		for _,v in pairs(list) do
			if last_monster_id ~= v.id then
				last_monster_id = v.id
				local boss_cfg = BossWGData.Instance:GetMonsterInfo(v.id)
				if boss_cfg then
					local info = {}
					info.id = v.id or 0
					info.x = v.x or 0
					info.y = v.y or 0
					info.name = boss_cfg.name or ""
					info.level = boss_cfg.level or 0
					if new_list == nil then
						new_list = {}
					end
					table.insert(new_list, info)
				end
			end
		end
		if new_list then
			SortTools.SortAsc(new_list, "level")
		end
		return new_list
	end
	return nil
end

--npc
function MapWGData:GetNpcCfgList()
	local scene_id = Scene.Instance:GetSceneId()
	-- if self.last_map_id ~= scene_id then
		self.npc_cfg_list = {}
		local config = self:GetMapConfig(scene_id)
		if not config then
			return nil
		end
		local count = 0

		local npc_list = {}
		local bool = false
		local npc_cfg = nil
		for k,v in pairs(config.npcs) do
			bool = TaskWGData.Instance:GetNPCHideCfg(v.id)
			if not bool then
				npc_cfg = TaskWGData.Instance:GetNPCConfig(v.id)
				bool = npc_cfg and 1 == tonumber(npc_cfg.cansee)
			end
	        if not bool then
	        	npc_list[#npc_list + 1] = v
	        end
		end

		for k,v in pairs(npc_list) do
			local name = ""
			npc_cfg = TaskWGData.Instance:GetNPCConfig(v.id)
			if npc_cfg and npc_cfg.show_name and npc_cfg.show_name ~= "" then
				name = npc_cfg.show_name
			end
			local info = {
				x = v.x,
				y = v.y,
				id = v.id,
				obj_type = SceneObjType.Npc,
				scene_id = scene_id,
				name = name,
				level = 0
			}
			self.npc_cfg_list[k] = info
			count = count + 1
		end
	-- end
	self.last_map_id = scene_id
	return self.npc_cfg_list, count
end

--传送门
function MapWGData:GetDoorCfgList()
	local scene_id = Scene.Instance:GetSceneId()
	-- if self.last_map_id ~= scene_id then
		self.door_cfg_list = {}
		local config = self:GetMapConfig(scene_id)
		if not config then
			return nil
		end
		local count = 0
		for k,v in ipairs(config.doors) do
			local scene_config = self:GetMapConfig(v.target_scene_id)
			local name = ""
			local level = 0
			if scene_config then
				name = scene_config.name
				level = scene_config.levellimit
			end
			local info = {
				x = v.x,
				y = v.y,
				id = v.id,
				obj_type = SceneObjType.Door,
				scene_id = scene_id,
				name = name,
				level = level
			}
			self.door_cfg_list[k] = info
			count = count + 1
		end
	-- end
	self.last_map_id = scene_id
	return self.door_cfg_list, count
end

function MapWGData:GetRegionCfgList()
	self.region_cfg_list = {}
	local count = 0
	local scene_id = Scene.Instance:GetSceneId()

	self.last_map_id = scene_id
	return self.region_cfg_list, count
end

--怪物
function MapWGData:GetMonsterCfgList()
	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()
	local config = self:GetMapConfig(scene_id)
	if not config then
		return nil, 0
	end
	self.last_map_id = scene_id
	local scene_logic = Scene.Instance:GetSceneLogic()

	local monster_cfg_list, count = scene_logic:GetFbSceneMonsterCfg(config.monsters)
	if nil ~= monster_cfg_list then
		return monster_cfg_list, count
	else	
		return self:GetNormalSceneMonsterCfg(config.monsters)
	end
	return nil, 0
end

--BOSS
function MapWGData:GetBossCfgList()
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic then
		return scene_logic:GetFbSceneMonsterBossCfg()
	end
	return nil
end

function MapWGData:GetNormalSceneMonsterCfg( monsters )
	self.monster_cfg_list = {}

	local count = 0
	local monsters_list = self:GetSceneMonsterSort(monsters)
	local scene_id = Scene.Instance:GetSceneId()
	if monsters_list then
		for k,v in pairs(monsters_list) do
			local info = {
				x = v.x,
				y = v.y,
				id = v.id,
				obj_type = SceneObjType.Monster,
				scene_id = scene_id,
				name = v.name,
				level = v.level
			}
			self.monster_cfg_list[k] = info
			count = count + 1
		end
	end
	return self.monster_cfg_list, count
end

--采集物
function MapWGData:GetGatherCfgList()
	local scene_id = Scene.Instance:GetSceneId()
	-- if self.last_map_id ~= scene_id then
		self.gather_cfg_list = {}
		local config = self:GetMapConfig(scene_id)
		if not config then
			return nil
		end
		local count = 0
		local bool = false
		local gather_cfg
		local name = ""
		for k,v in pairs(config.gathers) do
			bool = TaskWGData.Instance:GetGatherHideCfg(v.id)
			gather_cfg = self:GetGatherById(v.id)
		    if not bool and gather_cfg and tonumber(gather_cfg.cansee) ~= 1 then
				name = gather_cfg.show_name
				local info = {
					x = v.x,
					y = v.y,
					id = v.id,
					obj_type = SceneObjType.Door,
					scene_id = scene_id,
					name = name,
					level = 0
				}
				self.gather_cfg_list[v.id] = info
			end
		end
	-- end
	self.last_map_id = scene_id
	return self.gather_cfg_list, GetTableLen(self.gather_cfg_list)
end

function MapWGData:GetSetCurSelectInfo( info )
	if info then
		self.cur_select_monster_info = info
		return
	end
	return self.cur_select_monster_info
end

function MapWGData:GetSpecialIconInfo()
	local result = {}
	local cur_scene = Scene.Instance:GetSceneId()
	-- 夺旗战场小图标
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
		local camp_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfg()
		if not IsEmptyTable(camp_cfg) then
			for k, v in pairs(camp_cfg) do
				local data = {}
				local pos_list = string.split(v.born_pos, ",")
				local target_pos_x = pos_list[1]
				local target_pos_y = pos_list[2]
				data.x = target_pos_x
				data.y = target_pos_y
				data.icon_bundle = "uis/view/country_map_ui/flag_grabing_battlefield_ui/images_atlas"
				data.icon_name = "a2_dt_fh"
				data.call_back = function() GuajiWGCtrl.Instance:MoveToPos(cur_scene, target_pos_x, target_pos_y) end
				data.bottom_str = string.format(Language.FlagGrabbingBattlefield.FGBBornAreaName, v.camp_name)
				data.bottom_str_outline_color = "#9B1914"
				table.insert(result, data)
			end
		end

		local contend_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendList()
		if not IsEmptyTable(contend_cfg) then
			for k, v in pairs(contend_cfg) do
				local data = {}
				local pos_list = string.split(v.pos, ",")
				local target_pos_x = pos_list[1]
				local target_pos_y = pos_list[2]
				data.x = target_pos_x
				data.y = target_pos_y
				data.icon_bundle = "uis/view/country_map_ui/flag_grabing_battlefield_ui/images_atlas"
				data.icon_name = "a2_dt_zd"
				data.call_back = function() GuajiWGCtrl.Instance:MoveToPos(cur_scene, target_pos_x, target_pos_y) end
				data.bottom_str = v.contend_name
				data.bottom_str_outline_color = "#9B1914"
				table.insert(result, data)
			end
		end
	end

	return result
end

function MapWGData:GetGatherIconInfo()
	local result = {}
	local scene_type = Scene.Instance:GetSceneType()
	local cur_scene = Scene.Instance:GetSceneId()

	-- 夺旗战场动态宝箱小图标
	if scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
		local gather_info = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBmapGatherInfo()

		if not IsEmptyTable(gather_info) then
			for k, v in pairs(gather_info) do
				local data = {}
				data.x = v.pos_x
				data.y = v.pos_y
				data.icon_bundle = "uis/view/country_map_ui/flag_grabing_battlefield_ui/images_atlas"
				data.icon_name = "a2_dt_bx"
				data.call_back = function() GuajiWGCtrl.Instance:MoveToPos(cur_scene, v.pos_x, v.pos_y) end
				data.str = ""
				data.str_outline_color = "#9B1914"
				table.insert(result, data)
			end
		end
	end

	return result
end