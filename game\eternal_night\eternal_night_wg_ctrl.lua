require("game/eternal_night/eternal_night_wg_data")
require("game/eternal_night/eternal_night_equip_view")
require("game/eternal_night/eternal_night_rank_view")
require("game/eternal_night/eternal_night_result_view")
require("game/eternal_night/eternal_night_reward_view")
require("game/eternal_night/eternal_night_task_view")
require("game/eternal_night/eternal_night_getitem_show")
require("game/eternal_night/eternal_night_map_view")
require("game/eternal_night/eternal_night_eliminate_rank_view")

-- 永夜之巅
EternalNightWGCtrl = EternalNightWGCtrl or BaseClass(BaseWGCtrl)

function EternalNightWGCtrl:__init()
	if nil ~= EternalNightWGCtrl.Instance then
		ErrorLog("[EternalNightWGCtrl] attempt to create singleton twice!")
		return
	end
	EternalNightWGCtrl.Instance = self

	self.eternal_night_equip_view = EternalNightEquipView.New(GuideModuleName.EternalNightEquipView)
	self.eternal_night_rank_view = EternalNightRankView.New(GuideModuleName.EternalNightRankView)
	self.eternal_night_eliminate_rank_view = EternalNightEliminateRankView.New(GuideModuleName.EternalNightEliminateRankView)
	self.eternal_night_result_view = EternalNightResultView.New(GuideModuleName.EternalNightResultView)
	self.eternal_night_reward_view = EternalNightRewardView.New(GuideModuleName.EternalNightRewardView)
	self.eternal_night_task_view = EternalNightTaskView.New(GuideModuleName.EternalNightTaskView)
	self.eternal_night_map = EternalNightMapView.New(GuideModuleName.EternalNightMapView)
	self.eternal_night_getitem_show = EternalNightGetItemShow.New()

	self.eternal_night_data = EternalNightWGData.New()

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	-- self.scene_change_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))

	self:RegisterAllProtocols()
end

function EternalNightWGCtrl:__delete()
	if self.eternal_night_equip_view then
		self.eternal_night_equip_view:DeleteMe()
		self.eternal_night_equip_view = nil
	end
	
	if self.eternal_night_rank_view then
		self.eternal_night_rank_view:DeleteMe()
		self.eternal_night_rank_view = nil
	end
	
	if self.eternal_night_eliminate_rank_view then
		self.eternal_night_eliminate_rank_view:DeleteMe()
		self.eternal_night_eliminate_rank_view = nil
	end
	
	if self.eternal_night_result_view then
		self.eternal_night_result_view:DeleteMe()
		self.eternal_night_result_view = nil
	end
	
	if self.eternal_night_reward_view then
		self.eternal_night_reward_view:DeleteMe()
		self.eternal_night_reward_view = nil
	end
	
	if self.eternal_night_task_view then
		self.eternal_night_task_view:DeleteMe()
		self.eternal_night_task_view = nil
	end
	
	if self.eternal_night_data then
		self.eternal_night_data:DeleteMe()
		self.eternal_night_data = nil
	end
	
	if self.eternal_night_getitem_show then
		self.eternal_night_getitem_show:DeleteMe()
		self.eternal_night_getitem_show = nil
	end
	
	if self.eternal_night_map then
		self.eternal_night_map:DeleteMe()
		self.eternal_night_map = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	-- GlobalEventSystem:UnBind(self.scene_change_complete_event)

	EternalNightWGCtrl.Instance = nil

end

function EternalNightWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCEternalNightInfo, "OnSCEternalNightInfo")
	self:RegisterProtocol(SCEternalNightPlayerInfo, "OnSCEternalNightPlayerInfo")
	self:RegisterProtocol(SCEternalNightEquipUpdate, "OnSCEternalNightEquipUpdate")
	self:RegisterProtocol(SCEternalNightLiveNum, "OnSCEternalNightLiveNum")
	self:RegisterProtocol(SCEternalNightSelfInfo, "OnSCEternalNightSelfInfo")
	self:RegisterProtocol(SCEternalNightMonsterInfo, "OnSCEternalNightMonsterInfo")
	self:RegisterProtocol(SCEternalNightPickFallingItem, "OnSCEternalNightPickFallingItem")
	self:RegisterProtocol(SCEternalNightKillInfo, "OnSCEternalNightKillInfo")
	self:RegisterProtocol(SCEternalNightEnterSubRoomInfo, "OnSCEternalNightEnterSubRoomInfo")
	self:RegisterProtocol(SCEternalNightRandomMonsterInfo, "OnSCEternalNightRandomMonsterInfo")
	self:RegisterProtocol(SCEternalNightGetPlayerPos, "OnSCEternalNightGetPlayerPos")
	self:RegisterProtocol(CSEternalNightEnterSubRoom)
	self:RegisterProtocol(CSEternalNightGetPlayerPos)
	self:RegisterProtocol(SCEternalNightSceneInfo, "OnSCEternalNightSceneInfo")
end

function EternalNightWGCtrl:SendCSEternalNightEnter(act_type)
	if not ActivityWGData.Instance:GetActivityIsOpen(act_type) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.EternalNight.NotOpenTips)
		return
	end
	
	ActivityWGCtrl.Instance:SendCSDoubleSideFBIsFinishReq(act_type, function(protocol)
		if protocol.is_activity_fb_finish == 1 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.EternalNight.MissActDesc)
		else
			self.eternal_night_data:SetCurEnterActType(act_type)
			self:SendEnterCSEternalNight()
		end
	end)
end

function EternalNightWGCtrl:SendEnterCSEternalNight()
	local enter_cross = 0
	local act_type = self.eternal_night_data:GetCurEnterActType()
	if act_type then
		enter_cross = act_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF and 1
	end
	local is_finish = ActivityWGData.Instance:GetActDoubleSideFBIsFinish(act_type)
	if is_finish then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ActDoubleSideFBTips)
		return
	end

	CrossServerWGCtrl.SendCrossStartReq(act_type)

end

function EternalNightWGCtrl:SendCSEternalNightEnterSubRoom(sub_room_key)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEternalNightEnterSubRoom)
	protocol.sub_room_key = sub_room_key or 0
	protocol:EncodeAndSend()
end

function EternalNightWGCtrl:OnSCEternalNightInfo(protocol)
	self.eternal_night_data:SetSCEternalNightInfo(protocol)
	local kick_out_role_time = protocol.kick_out_role_time
	if kick_out_role_time > 0 then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
		if self.eternal_night_result_view then
			self.eternal_night_result_view:Open()
		end
	end
	
	if self.eternal_night_task_view and self.eternal_night_task_view:IsOpen() and self.eternal_night_task_view:IsLoaded() then
		self.eternal_night_task_view:FlushMapBtn()
		self.eternal_night_task_view:FlushTimeCount()
	end

	if self.eternal_night_map and self.eternal_night_map:IsOpen() then
		self.eternal_night_map:Flush()
	end
end

function EternalNightWGCtrl:OnSCEternalNightPlayerInfo(protocol)
	local old_info = self.eternal_night_data:GetPlayerInfoBySelfId()
	local old_score = old_info and old_info.score or 0
	self.eternal_night_data:SetSCEternalNightPlayerInfo(protocol)
	local new_info = self.eternal_night_data:GetPlayerInfoBySelfId()
	local new_score = new_info and new_info.score or 0
	local lerp_score = new_score - old_score
	if lerp_score > 0 then
		local str = string.format(Language.EternalNight.ScoreStr2,lerp_score)
		local main_role = Scene.Instance:GetMainRole()
		local bottom_point = main_role.draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
		local bundle_name = "uis/view/floatingtext_ui_prefab"
		local asset_name = "EternalNightScoreText"
		FightText.Instance:ShowText(bundle_name, asset_name, str, bottom_point,true)
	end
	if self.eternal_night_task_view:IsOpen() then
		self.eternal_night_task_view:Flush()
	end

	if self.eternal_night_result_view:IsOpen() then
		self.eternal_night_result_view:Flush()
	end

	for k,v in pairs(protocol.player_list) do
		local role_obj = Scene.Instance:GetRoleByUUID(v.uuid)
		if role_obj then
			if role_obj then
				local obj_vo = role_obj:GetVo()
				EternalNightWGData.Instance:SetEquipShiZhuang(obj_vo.appearance,v.equip_list,v.uuid)
				role_obj:UpdateAppearanceInEternalNight()
				role_obj:SetAttr("eternal_night_title")
			end
			
		end
	end
end

function EternalNightWGCtrl:OnSCEternalNightEquipUpdate(protocol)
	self.eternal_night_data:SetSCEternalNightEquipUpdate(protocol)
	local uuid = protocol.uuid
	local role_obj = Scene.Instance:GetRoleByUUID(uuid)
	if role_obj then
		if role_obj then
			local obj_vo = role_obj:GetVo()
			EternalNightWGData.Instance:SetEquipShiZhuang(obj_vo.appearance,protocol.equip_list,uuid)
			role_obj:SetAttr("eternal_night_title")
			role_obj:UpdateAppearanceInEternalNight()
		end
		
	end
end

function EternalNightWGCtrl:OnSCEternalNightLiveNum(protocol)
	self.eternal_night_data:SetSCEternalNightLiveNum(protocol)
	if self.eternal_night_task_view:IsOpen() then
		self.eternal_night_task_view:Flush()
	end
end

function EternalNightWGCtrl:OnSCEternalNightSelfInfo(protocol)
	self.eternal_night_data:SetSCEternalNightSelfInfo(protocol)
	if self.eternal_night_task_view:IsOpen() then
		self.eternal_night_task_view:Flush()
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local main_role_vo = main_role:GetVo()
		local uuid = RoleWGData.Instance:GetUUid()
		EternalNightWGData.Instance:SetRoleOriginAppearance(main_role_vo.appearance)
		EternalNightWGData.Instance:SetEquipShiZhuang(main_role_vo.appearance, protocol.equip_list,uuid)
		main_role:UpdateAppearanceInEternalNight()
		main_role:SetAttr("eternal_night_title")
	end
end

function EternalNightWGCtrl:GetTaskView()
	return self.eternal_night_task_view
end

function EternalNightWGCtrl:OnSCEternalNightMonsterInfo(protocol)

end


function EternalNightWGCtrl:GetEquipBtnNode(item_id)
	if self.eternal_night_equip_view and self.eternal_night_equip_view:IsOpen() then
		local node = self.eternal_night_equip_view:GetEquipBtnNode(item_id)
		return node
	end
end


function EternalNightWGCtrl:OnPickItemEvent()
	if not self.eternal_night_equip_view:IsOpen() then
		self.eternal_night_equip_view:Open()
	end
end

--捡起物品动画播放完了，回调事件
function EternalNightWGCtrl:PickItemAnimComplete()
	-- local old_info = EternalNightWGData.Instance:GetOldSelfInfo()
	-- local old_value = old_info.equip_attr_info and old_info.equip_attr_info.equip_capability or 0
	EternalNightWGData.Instance:SetNewSelfInfo()
	local new_info = EternalNightWGData.Instance:GetOldSelfInfo()
	local new_value = new_info.equip_attr_info and new_info.equip_attr_info.equip_capability or 0
	if self.eternal_night_equip_view and self.eternal_night_equip_view:IsOpen() then
		self.eternal_night_equip_view:Flush()
		self.eternal_night_equip_view:SetAutoClose()
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ETERNAL_NIGHT then
		local main_cap = RoleWGData.Instance:GetOriginCapability()
		RoleWGData.Instance:SetAttr("capability", main_cap + new_value)
	end
end

--从地上捡起物品 
local pick_falling_count = 1
function EternalNightWGCtrl:OnSCEternalNightPickFallingItem(protocol)
	local show_func	= function ()
		local data = EternalNightWGData.Instance:GetEquipCfgById(protocol.id)
		if data and next(data) then
			local item_data = {}
			item_data.item_id = protocol.id
			item_data.change_num = 1
			item_data.anim_complete_fun = BindTool.Bind(self.PickItemAnimComplete,self)
			pick_falling_count = pick_falling_count + 1
			if pick_falling_count > 50 then
				pick_falling_count = 1
			end
			EternalNightWGCtrl.Instance:ShowGetItem(item_data)

			local bundle, asset = ResPath.GetFallItemEffect(data.color)
			local async_loader = AllocAsyncLoader(self, "EternalNightPickFalling" .. pick_falling_count)
			local mainrole = Scene.Instance:GetMainRole()
			async_loader:SetIsUseObjPool(true)
			async_loader:SetParent(mainrole.draw_obj.root_transform)
			async_loader:Load(bundle, asset, function(obj)
				if IsNil(obj) then
					return
				end
				obj:SetActive(true)
				if mainrole and mainrole.draw_obj then
					local x, y = GameMapHelper.LogicToWorld(protocol.pos_x, protocol.pos_y)
					local role_pos = mainrole.draw_obj.root_transform.position
					obj.transform.position = Vector3(x, role_pos.y, y)
					obj.transform:DOLocalMove(Vector3(0, 2, 0), 1)
				end
				GlobalTimerQuest:AddDelayTimer(function()
					async_loader:Destroy()
				end, 1)
			end)
		end
	end

	if self.eternal_night_equip_view then
		if self.eternal_night_equip_view:IsOpen() then
			show_func()
		else
			self.eternal_night_equip_view:SetLoadCallBack(show_func)
		end
	end
	self:OnPickItemEvent()
end

function EternalNightWGCtrl:ShowGetItem(item_data)
	local item_cfg = EternalNightWGData.Instance:GetEquipCfgById(item_data.item_id)
	if item_cfg then
		self.eternal_night_getitem_show:Show(item_data)
	end
end

function EternalNightWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if (activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT or activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF) 
		and status == ACTIVITY_STATUS.CLOSE then
		EternalNightWGData.Instance:ClearOldSelfInfo()
	end
end

--连杀传闻
function EternalNightWGCtrl:OnSCEternalNightKillInfo(protocol)
	local kill_player = protocol.kill_player
	local dead_player = protocol.dead_player
	local kill_head_key = kill_player.uuid.temp_low
	local dead_head_key = dead_player.uuid.temp_low
	AvatarManager.Instance:SetAvatarKey(kill_head_key, kill_player.avatar_key_big, kill_player.avatar_key_small)
	AvatarManager.Instance:SetAvatarKey(dead_head_key, dead_player.avatar_key_big, dead_player.avatar_key_small)
	local t = {}
	t.killer_uid = kill_head_key
	t.killer_name = kill_player.name
	t.killer_sex = kill_player.sex
	t.killer_prof = kill_player.prof
	t.killer_is_red_side = 0
	t.target_uid = dead_head_key
	t.target_name = dead_player.name
	t.target_sex = dead_player.sex
	t.target_prof = kill_player.prof
	t.target_is_red_side = 1
	t.param = protocol.combo_kill_num
	-- t.kill_msg = Language.ZCResult.ShutDown
	TipWGCtrl.Instance:AddZCRuneMsgCenter(t)
	local role_uuid = RoleWGData.Instance:GetUUid()
	if role_uuid == dead_player.uuid then
		EternalNightWGData.Instance:SetNewSelfInfo()
		EternalNightWGData.Instance:SetDropEquipData(protocol.drop_equip_id,kill_player.name)
		if self.eternal_night_equip_view and self.eternal_night_equip_view:IsOpen() then
			self.eternal_night_equip_view:Flush()
		end
	end
end

function EternalNightWGCtrl:OnSCEternalNightEnterSubRoomInfo(protocol)
	self.eternal_night_data:SetSCEternalNightEnterSubRoomInfo(protocol)
end

function EternalNightWGCtrl:OnSCEternalNightRandomMonsterInfo(protocol)
	self.eternal_night_data:SetSCEternalNightRandomMonsterInfo(protocol)
	if self.eternal_night_task_view:IsOpen() then
		self.eternal_night_task_view:Flush()
	end
end

function EternalNightWGCtrl:SendCSEternalNightGetPlayerPos(uuid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEternalNightGetPlayerPos)
	protocol.uuid = uuid
	protocol:EncodeAndSend()
end

function EternalNightWGCtrl:OnSCEternalNightGetPlayerPos(protocol)
	local uuid = protocol.uuid
	local save_uuid = EternalNightWGData.Instance:GetGoByRoleUUid()
	local pos_x = protocol.pos_x
	local pos_y = protocol.pos_y
	local scene_id = protocol.scene_id
	local cur_scene_id = Scene.Instance:GetSceneId()
	local goto_fun = function ()
		local scene_type = Scene.Instance:GetSceneType()
		if (scene_type == SceneType.ETERNAL_NIGHT_FINAL or scene_type == SceneType.ETERNAL_NIGHT) 
			and save_uuid and save_uuid == uuid then
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
				local role_obj = Scene.Instance:GetRoleByUUID(save_uuid)
				if role_obj then
			        GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, role_obj, SceneTargetSelectType.SCENE)
	    		else
		    		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		    	end
		    end)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y,0,nil,nil,nil,nil,nil,nil,true)
		end
	end

	if cur_scene_id == scene_id then
		goto_fun()
	else
		local room_info = EternalNightWGData.Instance:GetSceneRoomInfo(scene_id)
		if room_info then
			local room_key = room_info.sub_room_key
			EternalNightWGCtrl.Instance:SendCSEternalNightEnterSubRoom(room_key)
			EternalNightWGData.Instance:SetGoToBossFun(goto_fun)
		end
	end
	
end

function EternalNightWGCtrl:OnSCEternalNightSceneInfo(protocol)
	self.eternal_night_data:SetEternalNightSceneInfo(protocol)

	if self.eternal_night_task_view and self.eternal_night_task_view:IsOpen() then
		self.eternal_night_task_view:Flush()
	end
end