SwornRechargeWGData = SwornRechargeWGData or BaseClass()
function SwornRechargeWGData:__init()
	if SwornRechargeWGData.Instance then
		print_error("[SwornRechargeWGData] Attempt to create singleton twice!")
		return
	end

	SwornRechargeWGData.Instance = self

	self:InitParam()
end

function SwornRechargeWGData:__delete()
	SwornRechargeWGData.Instance = nil
end

function SwornRechargeWGData:InitParam()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_jieyi_chongzhi_rank_auto")
	self.act_reward_cfg = ListToMapList(cfg.reward, "grade")
	--自己的队伍.
	self.self_jieyi_team = {}
	--自己的队伍充值数.
	self.my_rank_value = 0
	--自己的队伍排名.
	self.my_rank = 0
	--当前档次.
	self.grade = 0
	--所有的队伍.
	self.rank_list = {}
end

function SwornRechargeWGData:GetActRewardCfg()
	return self.act_reward_cfg[self.grade] or {}
end

function SwornRechargeWGData:SetSwornRechargeInfo(protocol)
	self.self_jieyi_team = self:SortTeamRank(protocol.self_jieyi_team)

	self.my_rank_value = protocol.self_rank_value

	self.my_rank = protocol.self_rank

	self.grade = protocol.grade

	local cfg = self:GetActRewardCfg()
	local rank_cfg = cfg[#cfg]
	local max_rank = rank_cfg.max_rank
	local rank_item_list = protocol.rank_item_list
	if rank_item_list then
		for i = 1, max_rank do
			local rank_item = {}
			if rank_item_list[i] then
				--队伍信息.
				rank_item.team_data = self:SortTeamRank(rank_item_list[i].team_data)

				--结义充值数.
				rank_item.rank_value = rank_item_list[i].rank_value
				--排名.
				rank_item.rank = rank_item_list[i].rank
			end

			self.rank_list[i] = rank_item
		end
	end
end

--自己的队伍.
function SwornRechargeWGData:GetSelfTeam()
	return self.self_jieyi_team
end

--自己的队伍充值数.
function SwornRechargeWGData:GetMyRankValue()
	return self.my_rank_value
end

--自己的队伍排名.
function SwornRechargeWGData:GetMyRank()
	return self.my_rank
end

--所有的队伍.
function SwornRechargeWGData:GetAllRank()
	return self.rank_list
end

--排序队伍.
function SwornRechargeWGData:SortTeamRank(data_list)
	if IsEmptyTable(data_list) then
		return {}
	end

	table.sort(data_list, function(a, b)
		if a.level <= 0 then
			return false
		end

		if a.chongzhi_num == b.chongzhi_num then
			if a.level > b.level then
				return true
			end
		end

		return a.chongzhi_num > b.chongzhi_num
	end)

	return data_list
end

--计算 超越排名 需要的金额、排名.
--再累充x元，可提升至前y名.
function SwornRechargeWGData:GetNeedMoneyAndNextRank()
	local reward_cfg = self:GetActRewardCfg()

	if IsEmptyTable(reward_cfg) then
		return
	end

	--计算 还可以提升排名的奖励档次.
	--如果 next_grade == 0，则达到了第一档，没有下一档了.
	local next_grade = #reward_cfg

	for i = 1, #reward_cfg do
		if self.my_rank_value >= reward_cfg[i].reach_value then
			next_grade = i - 1
			break
		end
	end

	local need_money = 0
	local next_rank = 0

	if next_grade <= 0 then
		return next_grade, need_money, next_rank
	end

	need_money = reward_cfg[next_grade].reach_value - self.my_rank_value
	next_rank = reward_cfg[next_grade].max_rank

	return next_grade, need_money, next_rank
end
