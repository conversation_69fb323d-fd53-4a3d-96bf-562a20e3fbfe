ControlBeastsSkillShowView = ControlBeastsSkillShowView or BaseClass(SafeBaseView)

function ControlBeastsSkillShowView:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)
    self.view_layer = UiLayer.SkillFloatWord
    
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_skill_show_view")
end

function ControlBeastsSkillShowView:ReleaseCallBack()
    self:CleanDelayTimer()
end

function ControlBeastsSkillShowView:SetBeastsId(beasts_id)
    self.beasts_id = beasts_id
	local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.beasts_id)

    if cfg and cfg.skill_id <= 0 then
		return
	end
	
	self:Open()
end

function ControlBeastsSkillShowView:OnFlush()
	if nil == self.beasts_id then
		return
	end

	local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.beasts_id)

    if cfg then
		if cfg.skill_id <= 0 then
			return
		end

        local skill_id = cfg.skill_id
		local real_beast_element = cfg.beast_element

		if real_beast_element == 4 then
			real_beast_element = 5
		elseif real_beast_element == 5 then
			real_beast_element = 4
		end

		-- local node = self.node_list[string.format("beasts_skill_name_txt_%d", real_beast_element)] 
		-- self.node_list.beasts_skill_name_txt_1:CustomSetActive(real_beast_element == 1)
		-- self.node_list.beasts_skill_name_txt_2:CustomSetActive(real_beast_element == 2)
		-- self.node_list.beasts_skill_name_txt_3:CustomSetActive(real_beast_element == 3)
		-- self.node_list.beasts_skill_name_txt_4:CustomSetActive(real_beast_element == 4)
		-- self.node_list.beasts_skill_name_txt_5:CustomSetActive(real_beast_element == 5)


		-- 加载纹理图
		local raw_image_str = string.format("a3_hs_skill_type_%d", real_beast_element)
		self.node_list.raw_img_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG(raw_image_str))

		local beast_raw_image_str = cfg.chang_head or ""
		self.node_list.beast_raw_image.raw_image:LoadSprite(ResPath.GetNoPackPNG(beast_raw_image_str))

		local beast_skill_image = string.format("a3_hs_skill_name_%d", cfg.chang_head)
		self.node_list.beast_skill_name_image.raw_image:LoadSprite(ResPath.GetRawImagesPNG(beast_skill_image))

        --去技能数据类查
        --local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(skill_id)
		--local skill_name = beast_cfg and beast_cfg.skill_name or ""

		-- if node then
		-- 	node.text.text = skill_name
		-- end
    end

	self:CleanDelayTimer()
	self.hied_tianshen_root_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 1.5)
end

function ControlBeastsSkillShowView:CleanDelayTimer()
	if self.hied_tianshen_root_timer then
		GlobalTimerQuest:CancelQuest(self.hied_tianshen_root_timer)
		self.hied_tianshen_root_timer = nil
	end
end



