﻿using System;
using UnityEngine;

namespace Nirvana
{
	/// <summary>
	/// Android 网络状态回调桥接。
	/// 通过 JNI（<see cref="AndroidJavaClass"/> / <see cref="AndroidJavaProxy"/>）与
	/// Java 侧的 `com.winunet.and.InternetState` 和回调接口
	/// `com.winunet.and.InternetStateReceiverListener` 建立连接，
	/// 将网络状态变化以 C# 委托方式分发到上层。
	/// </summary>
	public static class NetWorkState
	{
		/// <summary>
		/// 设置网络状态监听回调。
		/// 若传入回调不为 null，会立即回调一次当前缓存状态（<see cref="curState"/> 与 <see cref="curMsg"/>），
		/// 然后初始化并注册 Java 侧监听器。
		/// </summary>
		/// <param name="callback">回调：参数1为状态码，参数2为描述信息。</param>
		public static void SetNetWorkStateListener(Action<int, string> callback = null)
		{
			bool flag = callback != null;
			if (flag)
			{
				callback(NetWorkState.curState, NetWorkState.curMsg);
			}
			NetWorkState.netWorkStateReceiverListener.CompleteDelegate = callback;
			NetWorkState.InitNetWorkState();
		}

		/// <summary>
		/// 初始化 Java 侧网络状态类并注册监听器。
		/// </summary>
		private static void InitNetWorkState()
		{
			bool flag = NetWorkState.netWorkStateClass == null;
			if (flag)
			{
				// Java 端网络状态管理类
				NetWorkState.netWorkStateClass = new AndroidJavaClass("com.winunet.and.InternetState");
			}
			// 注册监听（Java: InternetState.setListener(InternetStateReceiverListener listener)）
			NetWorkState.netWorkStateClass.CallStatic("setListener", new object[] { NetWorkState.netWorkStateReceiverListener });
		}

		private static int curState = 0; // 当前网络状态码（由 Java 端定义）

		private static string curMsg = string.Empty; // 当前网络状态描述

		private static AndroidJavaClass netWorkStateClass; // Java 类 com.winunet.and.InternetState

		private static NetWorkState.NetWorkStateReceiverListener netWorkStateReceiverListener = new NetWorkState.NetWorkStateReceiverListener(); // 回调代理实例（单例复用）

		/// <summary>
		/// Java 回调代理，映射接口 com.winunet.and.InternetStateReceiverListener。
		/// 通过 onComplete(int state, String msg) 接受网络状态变化并转发到 C#。
		/// </summary>
		private class NetWorkStateReceiverListener : AndroidJavaProxy
		{
			/// <summary>
			/// 构造代理并声明所实现的 Java 接口全名。
			/// </summary>
			public NetWorkStateReceiverListener()
				: base("com.winunet.and.InternetStateReceiverListener")
			{
			}

			/// <summary>
			/// C# 侧完成回调（供上层业务设置）。
			/// </summary>
			public Action<int, string> CompleteDelegate { get; set; }

			/// <summary>
			/// Java 侧回调入口。由 InternetStateReceiverListener.onComplete 调用。
			/// 更新本地缓存并通过 <see cref="SdkScheduler.PostTask(Action)"/> 切回主线程再执行上层回调。
			/// </summary>
			private void onComplete(int state, string msg)
			{
				NetWorkState.curState = state;
				NetWorkState.curMsg = msg;
				// 回到主线程再通知上层，确保与 Unity 主线程上下文一致
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.CompleteDelegate != null;
					if (flag)
					{
						this.CompleteDelegate(state, msg);
					}
				});
			}
		}
	}
}
