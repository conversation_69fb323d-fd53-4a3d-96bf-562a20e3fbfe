FallItem = FallItem or BaseClass(SceneObj)

FallItemHideType = {
 	None = 0,
 	ToRole = 1,
 	ToBag = 2
}

function FallItem:__init(vo)
	self.obj_type = SceneObjType.FallItem
	self.followui_class = FallItemFollow
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.FallItem
	self.need_calculate_priortiy = true

	self.is_picked = false
	self.picked_invalid_time = 0
	self.show_tips_time = 0

	-- 是否延时创建
	self.is_delay_create = vo.is_create == 1
	self.create_time = Status.NowTime
	self.effect_bundle = nil
	self.effect_name = nil
end

function FallItem:__delete()
    self.show_tips_time = 0
	self:ShowSpecialEffect()
	self:RemoveDelayTime()
	self:RemoveFallTime()
	if nil ~= self.item_effect then
		self.item_effect:DeleteMe()
		self.item_effect = nil
	end
end

function FallItem:ShowSpecialEffect()
	if not self.vo or not self.vo.is_client_fall then
		return
	end

	if self.vo.hide_type == FallItemHideType.ToRole then
		local main_role = Scene.Instance:GetMainRole()
		main_role:DoSpecialEffect(nil, self, self.vo.obj_id)
	elseif self.vo.hide_type == FallItemHideType.ToBag then
		local item_data = {}
		item_data.item_id = self.vo.item_id
		item_data.change_num = 1
		TipWGCtrl.Instance:ShowGetItem(item_data)
	end
end

function FallItem:InitInfo()
	SceneObj.InitInfo(self)
	self.cfg, self.item_type = self:GetItemCfg()
	if self.cfg == nil and self.vo.is_buff_falling ~= 1 then
		print_error("InitInfo has a nil cfg",self.vo.item_id)
	end

	if nil ~= self.cfg then
		self.vo.name = self.cfg.name
		if self.cfg then
			if self.follow_effect == nil  then
				self.follow_effect = AllocAsyncLoader(self, "follow_effect")
				self.follow_effect:SetParent(self.draw_obj:GetRoot().transform)
				self.follow_effect:SetIsUseObjPool(true)
			end
			local bundle_name, prefab_name = ResPath.GetFallItemEffect(self.cfg.color)
			self.follow_effect:Load(bundle_name, prefab_name)
		end
		if self.follow_effect then
			self.follow_effect:SetActive(self.cfg ~= nil)
		end
	end
end

function FallItem:InitAppearance()
	-- 延迟创建
	if self.is_delay_create then
		self:RemoveDelayTime()
		self.delay_time = GlobalTimerQuest:AddDelayTimer(function() self.is_delay_create = false self:InitAppearance() end, 1)
		return
	end

    if self.vo.monster_id and self.vo.obj_id then
        --之前通过monster_id,但是现在boss召唤可能召唤出相同的monster_id
        local monster = Scene.Instance:GetObj(self.vo.obj_id) --Scene.Instance:GetMonsterByMonsterId(vo.monster_id)
        local monster1 = Scene.Instance:GetMonsterByMonsterId(self.vo.monster_id)

		if monster and monster1 then
			self.monster_pos = monster1:GetRoot().transform.position
		end
	end

	local model_bundle, model_asset = ResPath.GetFallItemModel(5103001) -- 宝箱模型
	local drop_model = Split(self.cfg and self.cfg.drop_model or "", "#")

	if #drop_model > 1 then
		model_bundle, model_asset = drop_model[1], drop_model[2]
	elseif drop_model[1] and drop_model[1] ~= "" then
		model_bundle, model_asset = ResPath.GetFallItemModel(tonumber(drop_model[1]))
	end

	local follow_ui = self:GetFollowUi()
	self.effect_bundle = nil
	self.effect_name = nil

	self.pos_y = 0
	local sb_eff_bundle, sb_eff_asset = ItemWGData.Instance:GetSpecialBeamAsset(self.cfg and self.cfg.id)

	if self.is_client_fall and self.vo.hide_type ~= FallItemHideType.ToRole and self.cfg then
		-- model_bundle, model_asset = ResPath.GetFallItemModel(5109, true)
		follow_ui:SetName(ToColorStr(self.vo.client_name, ITEM_COLOR[self.cfg.color]), self)
	elseif self.cfg and self.cfg.sub_type and self.item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and not EquipWGData.IsJLType(self.cfg.sub_type) then
		self.effect_name = ResPath.GetFallItemZhuEffectName( self.cfg.color ) 
		-- model_bundle, model_asset = ResPath.GetForgeEquipModelForSubType(self.cfg.sub_type) --装备模型
		if model_bundle then
			self.pos_y = 0.7
		else
			model_bundle, model_asset = ResPath.GetFallItemModel(5103001) -- 宝箱模型
			self.effect_name = ResPath.GetFallItemZhuEffectName( self.cfg.color )
		end
	-- 特殊道具光柱
	elseif self.cfg and sb_eff_bundle and sb_eff_asset then
		self.effect_bundle = sb_eff_bundle
		self.effect_name = sb_eff_asset
	elseif self.vo.is_buff_falling == 1 and self.cfg == nil then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.YEZHANWANGCHENGFUBEN or scene_type == SceneType.CROSS_ULTIMATE_BATTLE then
			local other_cfg = KuafuYeZhanWangChengWGData.Instance:GetTipsShowInfo()
			if self.vo.buff_appearance == NIGHT_FIGHT_FALL_BUFF_TYPE.ADD_HP then
				self.effect_name = other_cfg.hp_effect
			elseif self.vo.buff_appearance == NIGHT_FIGHT_FALL_BUFF_TYPE.ADD_GONGJI then
				self.effect_name = other_cfg.gj_effect
			elseif self.vo.buff_appearance == NIGHT_FIGHT_FALL_BUFF_TYPE.ADD_FANGYU then
				self.effect_name = other_cfg.fy_effect
			elseif self.vo.buff_appearance == NIGHT_FIGHT_FALL_BUFF_TYPE.ADD_WUDI then
                self.effect_name = other_cfg.wd_effect
			elseif self.vo.buff_appearance == NIGHT_FIGHT_FALL_BUFF_TYPE.ADD_TIAN_XUAN then
				self.effect_name = other_cfg.tx_effect
			end
			self.effect_name = self.effect_name or TEMP_BUFF_NAME[self.vo.buff_appearance%10]
		elseif scene_type == SceneType.CROSS_AIR_WAR  then
			local buff_cfg = CrossAirWarWGData.Instance:GetFallingCfgBySeq(self.vo.buff_appearance)
			if buff_cfg then
				self.effect_name = buff_cfg.effect_seq
			end
		elseif scene_type == SceneType.ETERNAL_NIGHT_FINAL then
			local buff_cfg = EternalNightWGData.Instance:GetSceneBuffTypeCfg(self.vo.buff_appearance)
			if buff_cfg then
				self.effect_name = buff_cfg.effect_name
			end
		end

		model_bundle, model_asset = nil, nil
		if self.item_effect == nil  then
			self.item_effect = AllocAsyncLoader(self, "Buff_Effect") --self.draw_obj:GetRoot().transform, self.pos_y or 0
			self.item_effect:SetParent(self.draw_obj:GetRoot().transform)
			self.item_effect:SetIsInQueueLoad(true)
			self.item_effect:SetIsUseObjPool(true)
		end
		self.item_effect:Load(ResPath.GetEnvironmentBanyunEffect(self.effect_name))
		self.item_effect:SetActive(self.effect_name ~= nil)
		self:ShowFallAni()
	elseif self.vo.is_buff_falling ~= 1 then
		local scene_type = Scene.Instance:GetSceneType()
		--在永夜之巅装备处理
		if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
			local id = self.vo.coin
		    local equip_cfg = EternalNightWGData.Instance:GetEquipCfgById(id)
		    if equip_cfg then
		    	local drop_model = Split(equip_cfg and equip_cfg.drop_model or "", "#")
				if #drop_model > 1 then
					model_bundle, model_asset = drop_model[1], drop_model[2]
				end
		    	self.effect_name = ResPath.GetFallItemZhuEffectName(equip_cfg.color)
	    	end
	    else
	    	if self.cfg and self.cfg.color then
				self.effect_name = ResPath.GetFallItemZhuEffectName(self.cfg.color)
			end
	    end
	end

	self:ChangeModel(SceneObjPart.Main, model_bundle, model_asset)--self.cfg.drop_icon
	if self.cfg and self.vo.hide_type ~= FallItemHideType.ToRole then
		local scene_type = Scene.Instance:GetSceneType()
		--在永夜之巅装备处理
		if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
			local id = self.vo.coin
		    local equip_cfg = EternalNightWGData.Instance:GetEquipCfgById(id)
		    if equip_cfg then
		        follow_ui:SetName(ToColorStr(equip_cfg.name, FALL_ITEM_COLOR[equip_cfg.color]), self)	
            end
        elseif scene_type == SceneType.XianJie_Boss then
            if self.vo.item_id == COMMON_CONSTS.XiamVirtualItemId then
                local id = self.vo.coin
                local cfg, item_type = ItemWGData.Instance:GetItemConfig(id)
                if cfg == nil then
                    print_error("取不到物品配置 id = ",id)
                else
                    follow_ui:SetName(ToColorStr(cfg.name, FALL_ITEM_COLOR[cfg.color]), self)
                end
            else
                follow_ui:SetName(ToColorStr(self.cfg.name, FALL_ITEM_COLOR[self.cfg.color]), self)	
            end
		else
			follow_ui:SetName(ToColorStr(self.cfg.name, FALL_ITEM_COLOR[self.cfg.color]), self)	
		end
	end
end

function FallItem:GetItemCfg()
	local item_id = self.vo.item_id
	local scene_logic = Scene.Instance:GetSceneLogic()
	if self.vo.item_id == 0 and self.vo.is_buff_falling == 1 and scene_logic and scene_logic.GetFallItemItemId then
		item_id = scene_logic:GetFallItemItemId(self.vo) or item_id
	end

	local cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	return cfg, item_type
end

function FallItem:ShowFallAni()
	if not self.vo then
		return
	end

	-- 防止重新进入视野后，又飘动画
	if self.vo.drop_time + 3 < TimeWGCtrl.Instance:GetServerTime() then
		return
	end

	if self.monster_pos and self.vo.create_time + 3 > Status.NowTime then
		local pos = self:GetRoot().transform.position
		self:GetRoot().transform.position = self.monster_pos
		self:GetRoot().transform:DOJump(pos, 8, 1, 1.6):SetEase(DG.Tweening.Ease.OutBack)
		self.monster_pos = nil

		self:RemoveFallTime()
		self.action_delay_time = GlobalTimerQuest:AddDelayTimer(function ()
			if not self.draw_obj or not self.draw_obj:GetRoot() or not self.cfg then
				return
			end

			local bundle_name, prefab_name = ResPath.GetFallItemEffectSecond(self.cfg.color)
			EffectManager.Instance:PlayAtTransform(bundle_name, prefab_name, self.draw_obj:GetRoot().transform)
		end, 1.2)
	end
end

function FallItem:RemoveFallTime()
	if self.action_delay_time then
		GlobalTimerQuest:CancelQuest(self.action_delay_time)
		self.action_delay_time = nil
	end
end

function FallItem:RemoveDelayTime()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
		self.delay_time = nil
	end
end

function FallItem:Update(now_time, elapse_time)
	SceneObj.Update(self, now_time, elapse_time)
	if self.picked_invalid_time > 0 and now_time >= self.picked_invalid_time then
		self.picked_invalid_time = 0
		self.is_picked = false
	end
end

function FallItem:IsCoin()
	return self.vo.coin > 0
end

-- 否是虚拟物品
function FallItem:IsVirtualItem()
	if self.item_type == GameEnum.ITEM_BIGTYPE_VIRTUAL then
		return true
	end
	return false
end

--是否是装备	只限制角色装备
function FallItem:IsEquip()
	if self.cfg == nil then return false end
	local is_equip = false
	local sub_type = self.cfg.sub_type or 0
	local part = ItemWGData.GetEquipTypeName(sub_type)
	is_equip = part ~= -1
	return is_equip
end

function FallItem:GetAutoPickupMaxDis()
	-- 如果在障碍区，则直接捡起
	if self:IsInBlock() then
		return 0
	else
		return Scene.Instance:GetSceneLogic():GetPickItemMaxDic(self.vo.item_id)
	end
end

function FallItem:PlayPick()
	-- 播放拾取特效
	local position = self:GetRoot().transform.position
	ResMgr:LoadGameobjSync("effects2/prefab/misc/drop_weapon_prefab", "drop_weapon", function(obj)
		obj.transform.position = position

		local follow = obj:GetComponent(typeof(FollowTarget))
		local main_role = Scene.Instance:GetMainRole()
		if follow ~= nil and
			main_role ~= nil and
			main_role.draw_obj ~= nil and
			not main_role:IsDeleted() and
			main_role:GetRoot().gameObject ~= nil then
			local hurt_point = main_role.draw_obj:GetAttachPoint(AttachPoint.Hurt)
			follow:Follow(hurt_point, function()
				ResMgr:Destroy(obj)
			end)
		else
			ResMgr:Destroy(obj)
		end
		if self.vo.is_buff_falling == 1 then
			GlobalTimerQuest:AddDelayTimer(function() self:CheckShowEff() end, 0.5)
			-- if Language.TowerDefend.FallItemDec[self.vo.buff_appearan] then
			-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.TowerDefend.FallItemDec[self.vo.buff_appearan])
			-- end
		end
	end)
end

function FallItem:RecordIsPicked()
	self.is_picked = true
	self.picked_invalid_time = Status.NowTime + 1.5
end

function FallItem:IsPicked()
	return self.is_picked
end

function FallItem:OnClick()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.XianJie_Boss and self.vo.item_id == COMMON_CONSTS.XiamVirtualItemId  then
        local id = self.vo.coin
        local is_can_pick = XianJieBossWGData.Instance:GetCanPickItemById(id)
        if not is_can_pick then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.XianJieBoss.PickEquipTips)
            return
        end
    end
    SceneObj.OnClick(self)
end

--判断该装备在可拾取范围内是否可以捡
function FallItem:IsCanPickItem()
	local scene_type = Scene.Instance:GetSceneType()
	--在永夜之巅判断该装备是否可以捡
	if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
		local id = self.vo.coin
	    local equip_cfg = EternalNightWGData.Instance:GetEquipCfgById(id)
	    if equip_cfg and equip_cfg.equip_part then
	        local self_equip = EternalNightWGData.Instance:GetSelfEquipInfo(equip_cfg.equip_part)
	        if self_equip then
	        	if equip_cfg.zhanli > self_equip.zhanli then
	        		return true
	        	else
	        		local now_time = TimeWGCtrl.Instance:GetServerTime()
        			if now_time >= self.show_tips_time then
        				SysMsgWGCtrl.Instance:ErrorRemind(Language.EternalNight.PickEquipTips)
        				self.show_tips_time = now_time + 5
        			end
	        		return false
	        	end
	        end
        end
    elseif scene_type == SceneType.XianJie_Boss and self.vo.item_id == COMMON_CONSTS.XiamVirtualItemId then
        local id = self.vo.coin
        local is_can_pick = XianJieBossWGData.Instance:GetCanPickItemById(id)
        if is_can_pick then
            return is_can_pick
        else
            local now_time = TimeWGCtrl.Instance:GetServerTime()
            if now_time >= self.show_tips_time then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.XianJieBoss.PickEquipTips)
                self.show_tips_time = now_time + 30
            end
            return false
        end
	end
	return true
end

function FallItem:CheckShowEff()
	if self.vo.buff_appearan == BUFF_FALLING_APPEARAN_TYPE.NSTF_BUFF_1 then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("towerdefend_auto").level_scene_cfg[1]
		local life_tower_monster_id = other_cfg.life_tower_monster_id
		local monster_list = Scene.Instance:GetMonsterList()
		for k,v in pairs(monster_list) do
			if v:GetMonsterId() >= life_tower_monster_id then
				local res = "BUFF_meirenzhufu"
				local pos = v.draw_obj:GetRoot().transform.position
				if pos then
					local bundle_name, prefab_name = ResPath.GetEnvironmentBanyunEffect(res)
					EffectManager.Instance:PlayControlEffect(self, bundle_name, prefab_name, Vector3(pos.x, pos.y + 3, pos.z))
				end
			end
		end
	elseif self.vo.buff_appearan == BUFF_FALLING_APPEARAN_TYPE.NSTF_BUFF_2 then
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			main_role:AddEffect("BUFF_meirenzhiqiang", 3)
		end
	elseif self.vo.buff_appearan == BUFF_FALLING_APPEARAN_TYPE.NSTF_BUFF_3 then
		local pos = Scene.Instance:GetMainRole().draw_obj:GetPart(SceneObjPart.Main):GetAttachPoint(2)
		if pos then
			local bundle_name, prefab_name = ResPath.GetEffect("BUFF_meirenzhinu")
			EffectManager.Instance:PlayControlEffect(self, bundle_name, prefab_name, pos.transform.position)
		end
	end
end

function FallItem:OnModelLoaded(part, obj)
	SceneObj.OnModelLoaded(self, part, obj)
	if part ~= SceneObjPart.Main then
		return
	end

	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	if main_part == nil then
		return
	end

	local cfg, item_type = self:GetItemCfg()
	local drop_model_scale = cfg and cfg.drop_model_scale
	if drop_model_scale and drop_model_scale ~= "" then
		main_part:SetPartScale(drop_model_scale, drop_model_scale, drop_model_scale)
	else
		main_part:SetPartScale(1, 1, 1)
	end

	if self.item_effect == nil  then
		self.item_effect = AllocAsyncLoader(self, "item_effect")
		self.item_effect:SetParent(self.draw_obj:GetRoot().transform)
		self.item_effect:SetIsInQueueLoad(true)
		self.item_effect:SetIsUseObjPool(true)
	end

	if self.effect_name then
		local bundle, asset
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.XianMengzhan then
			bundle, asset = ResPath.GetEnvironmentBanyunEffect(self.effect_name)
		elseif scene_type == SceneType.YEZHANWANGCHENGFUBEN then
			if self.vo.buff_appearance == NIGHT_FIGHT_FALL_BUFF_TYPE.ADD_WUDI then
				bundle, asset = ResPath.GetEnvironmentBanyunEffect(self.effect_name)
			else
				bundle, asset = ResPath.GetFallItemZhuEffect(self.effect_name)
			end
			
		elseif scene_type == SceneType.ETERNAL_NIGHT_FINAL then
			if self.vo.is_buff_falling == 1 and self.cfg == nil then
				bundle, asset = ResPath.GetEnvironmentBanyunEffect(self.effect_name)
			else
				bundle, asset = ResPath.GetFallItemZhuEffect(self.effect_name)
			end
		elseif self.effect_bundle ~= nil then
			bundle = self.effect_bundle
			asset = self.effect_name
		else
			bundle, asset = ResPath.GetFallItemZhuEffect(self.effect_name)
		end

		if bundle ~= nil and asset ~= nil then
			self.item_effect:Load(bundle, asset)
		end
	end

	self.item_effect:SetActive(self.effect_name ~= nil)
	self:ShowFallAni()
end

function FallItem:OnModelRemove(part, obj)
	SceneObj.OnModelRemove(self, part, obj)
	if part ~= SceneObjPart.Main then
		return
	end

	if self.item_effect then
		self.item_effect:DeleteMe()
		self.item_effect = nil
	end

	if self.follow_effect then
		self.follow_effect:DeleteMe()
		self.follow_effect = nil
	end
end

function FallItem:CreateFollowUi()
	SceneObj.CreateFollowUi(self)
	self.follow_ui:SetLocalUI(0, 40, 0)
end

function FallItem:CalculatePriortiy()
	if SceneObj.select_obj == self then
		return SceneAppearPriority.High
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		if u3d.v2Length(u3d.v2Sub(main_role.logic_pos, self.logic_pos), false) <= 15 * 15 then
			return SceneAppearPriority.High
		else
			return SceneAppearPriority.Middle
		end
	end

	return SceneAppearPriority.Middle
end