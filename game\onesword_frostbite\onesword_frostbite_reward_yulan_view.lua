OneSwordFrostbiteRewardYulanView = OneSwordFrostbiteRewardYulanView or BaseClass(SafeBaseView)

local ITEM_HEIGHT = 219
local SPACE = 23
local ITEM_NUM = 10

function OneSwordFrostbiteRewardYulanView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/onesword_frostbite_ui_prefab", "layout_onesword_frostbite_yulan_view")
end

function OneSwordFrostbiteRewardYulanView:LoadCallBack()
    self.is_first_open = true
    self.is_first_show = false

    self.reward_yulan_list = {}
    for i = 1, ITEM_NUM do
        self.reward_yulan_list[i] = OneSwordFrostbiteYulanRender.New(self.node_list.content:FindObj("onesword_frostbite_yulan_item" .. i))
    end
end

function OneSwordFrostbiteRewardYulanView:ReleaseCallBack()
    if self.reward_yulan_list then
		for k, v in pairs(self.reward_yulan_list) do
            v:DeleteMe()
		end

		self.reward_yulan_list = nil
	end
end

function OneSwordFrostbiteRewardYulanView:ShowIndexCallBack()
    self.is_first_show = true
end

function OneSwordFrostbiteRewardYulanView:OnFlush()
    local info = OneSwordFrostbiteWGData.Instance:GetRewardYulanData()
    for i = 1, ITEM_NUM do
        if i <= #info then
            self.node_list.content:FindObj("onesword_frostbite_yulan_item" .. i):SetActive(true)
            self.reward_yulan_list[i]:SetData(info[i])
        else
            self.node_list.content:FindObj("onesword_frostbite_yulan_item" .. i):SetActive(false)
        end
    end

    if self.is_first_open then
        self.is_first_open = false
        self:PlaySlide(0.5)
    elseif self.is_first_show then
        self.is_first_show = false
        self:PlaySlide(0)
    end
end

function OneSwordFrostbiteRewardYulanView:PlaySlide(delay_time)
    ReDelayCall(self, function()
        local cur_round = OneSwordFrostbiteWGData.Instance:GetCurRound()
        local target_height = (ITEM_HEIGHT + SPACE) * (cur_round - 1)
        local node = self.node_list.content
        local list_height = self.node_list.reward_yulan_list.rect.rect.height
        local container_height = node.rect.rect.height
        local max_height = container_height - list_height
        if max_height > 0 then
            target_height = target_height <= max_height and target_height or max_height
        end

        local start_pos_y = node.rect.anchoredPosition.y

        UITween.CleanAllMoveToShowPanel(self.view_name)
	    UITween.MoveToShowPanel(self.view_name, node, Vector2(0, start_pos_y), Vector2(0, target_height), 0.1, DG.Tweening.Ease.Linear)
	end, delay_time, "OneSwordFrostbiteRewardYulanView")
end

OneSwordFrostbiteYulanRender = OneSwordFrostbiteYulanRender or BaseClass(BaseRender)
function OneSwordFrostbiteYulanRender:LoadCallBack()
    if nil == self.item_list then
		self.item_list = AsyncBaseGrid.New()
		self.item_list:CreateCells({col = 6, change_cells_num = 1, list_view = self.node_list["item_list"]})
        self.item_list:SetStartZeroIndex(false)
	end
end

function OneSwordFrostbiteYulanRender:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function OneSwordFrostbiteYulanRender:OnFlush()
    if not self.data then return end

    local cur_round = OneSwordFrostbiteWGData.Instance:GetCurRound()
    self.node_list["cur_round_img"]:SetActive(self.data.round == cur_round)
    self.item_list:SetDataList(self.data.reward_list)
    self.node_list.round_desc.text.text = string.format(Language.OneSwordFrostbite.RewardYulanDesc, self.data.round)
end