TweenWGData = TweenWGData or BaseClass()

TweenType = {
	Alpha = 1,
	Height = 2,
	Position = 3,
	Rotation = 4,
	Scale = 5,
	Width = 6,
	SizeDelta = 7,
}

TweenLuaMethod = {
	Linear = 0,
	EaseIn = 1,
	EaseOut = 2,
	EaseInOut = 3,
	BounceIn = 4,
	BounceOut = 5,
}


function TweenWGData:__init()
	if TweenWGData.Instance then
		error("[TweenWGData] Attempt to create singleton twice!")
		return
	end
	TweenWGData.Instance = self

	self.view_tween_auto = ConfigManager.Instance:GetAutoConfig("view_tween_cfg_auto")
	self.tween_module_cfg = ListToMap(self.view_tween_auto.guide_module_node, "guide_module_name", "sub_index")
	self.tween_tween_cfg = ListToMap(self.view_tween_auto.tween, "tween_id")

end

function TweenWGData:__delete()
	TweenWGData.Instance = nil
end

-- 获取对应的配置
function TweenWGData:GetModuleNodeCfg(view_name, sub_index)
	return ((self.tween_module_cfg or {})[view_name] or {})[sub_index]
end


-- 获取对应动画配置
function TweenWGData:GetTweenCfg(tween_id, node)
	local cfg = self.tween_tween_cfg[tween_id]

	if not cfg then
		return nil
	end

	local aim = {}
	aim.tween_type = cfg.tween_type
	aim.form = cfg.form
	aim.to = cfg.to
	aim.tween_time = cfg.tween_time
	aim.initinte_time = cfg.initinte_time
	aim.tween_method = cfg.tween_method

	if cfg.tween_type == TweenType.Position then
		aim.form = self:GetTweenVector3(cfg.form, node.transform.anchoredPosition)
		aim.to = self:GetTweenVector3(cfg.to, node.transform.anchoredPosition)
	elseif cfg.tween_type == TweenType.Rotation then
		aim.form = self:GetTweenVector3(cfg.form, node.transform.localEulerAngles)
		aim.to = self:GetTweenVector3(cfg.to, node.transform.localEulerAngles)
	elseif cfg.tween_type == TweenType.Scale then
		aim.form = self:GetTweenVector3(cfg.form, node.transform.localScale)
		aim.to = self:GetTweenVector3(cfg.to, node.transform.localScale)
	elseif cfg.tween_type == TweenType.SizeDelta then
		aim.form = self:GetTweenVector2(cfg.form, node.rect.sizeDelta)
		aim.to = self:GetTweenVector2(cfg.to, node.rect.sizeDelta)
	end

	return aim
end

-- 获取一份三维向量
function TweenWGData:GetTweenVector3(ve3_str, original)
	local list = Split(ve3_str, "|")
	local x = tonumber(list[1]) or 0 
	local y = tonumber(list[2]) or 0 
	local z = tonumber(list[3]) or 0 

	local real_x = (x == 0) and original.x or x
	local real_y = (y == 0) and original.y or y
	local real_z = (z == 0) and original.z or z

	return Vector3(real_x, real_y, real_z)
end

-- 获取一份二维向量
function TweenWGData:GetTweenVector2(ve2_str, original)
	local list = Split(ve2_str, "|")
	local x = tonumber(list[1]) or 0 
	local y = tonumber(list[2]) or 0 

	local real_x = (x == 0) and original.x or x
	local real_y = (y == 0) and original.y or y

	return Vector2(real_x, real_y)
end