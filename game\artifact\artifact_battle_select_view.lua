ArtifactBattleSelectView = ArtifactBattleSelectView or BaseClass(SafeBaseView)

function ArtifactBattleSelectView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(-2, -20), sizeDelta = Vector2(814, 578)})
	self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_battle_select")
	self.view_layer = UiLayer.Pop
end

function ArtifactBattleSelectView:__delete()

end

function ArtifactBattleSelectView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.Artifact.BattleSelect
	if not self.battle_select_list then
		self.battle_select_list = AsyncListView.New(ArtifactBattleSelectItem, self.node_list.artifact_select_list)
	end

	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ArtifactSelectView, self.get_guide_ui_event)
end

function ArtifactBattleSelectView:CloseCallBack()

end

function ArtifactBattleSelectView:ReleaseCallBack()
	if self.battle_select_list then
		self.battle_select_list:DeleteMe()
		self.battle_select_list = nil
	end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ArtifactSelectView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function ArtifactBattleSelectView:ShowIndexCallBack()

end

function ArtifactBattleSelectView:OnFlush()
	local battle_list = ArtifactWGData.Instance:GetArtifactBattleList()
	if self.battle_select_list then
		self.battle_select_list:SetDataList(battle_list)
	end
	self.node_list["common_no_data_panel"]:SetActive(IsEmptyTable(battle_list))
end

function ArtifactBattleSelectView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	if ui_name == "btn_apply_guide" then
		local fun = function()
			local battle_list = ArtifactWGData.Instance:GetArtifactBattleList()
			if battle_list and battle_list[1] then
				local data = battle_list[1]
	
				ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.APPLY, data.seq, 0)
				self:Close()
			end
		end

		return self.node_list[ui_name], fun
	end

	return self.node_list[ui_name]
end

--------------------------------
-- 出战选择ItemRender
--------------------------------
ArtifactBattleSelectItem = ArtifactBattleSelectItem or BaseClass(BaseRender)

function ArtifactBattleSelectItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_apply"], BindTool.Bind(self.OnClickApplyBtn, self))
end

function ArtifactBattleSelectItem:ReleaseCallBack()

end

function ArtifactBattleSelectItem:OnFlush()
	if not self.data then
		return
	end
	local data = self.data
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(data.seq)
	if cur_artifact_data then
		local artifact_name = ArtifactWGData.Instance:GetArtifactAwakeName(data.seq, cur_artifact_data.awake_level)
		self.node_list["txt_artifact_name"].text.text = artifact_name

		local bundle, asset = ResPath.GetArtifactImg("a3_sx_czdx" .. data.seq)
		self.node_list["img_head"].image:LoadSprite(bundle, asset, function()
			self.node_list["img_head"].image:SetNativeSize()
		end)
		bundle, asset = ResPath.GetArtifactImg("a3_sx_mzt_icon" .. self.data.battle_type)
		self.node_list["icon_type"].image:LoadSprite(bundle, asset)

		local star_res_list = GetTwenTyStarImgResByStar(cur_artifact_data.star_level)
		for i = 1, GameEnum.ITEM_MAX_STAR do
			self.node_list["star_" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
		end

		local skill_cfg = ArtifactWGData.Instance:GetArtifactSkillCfg(data.seq, cur_artifact_data.star_level)
		if skill_cfg then
			local skill_bundle, skill_asset = ResPath.GetSkillIconById(skill_cfg.skill_id)
			self.node_list["skill_icon"].image:LoadSprite(skill_bundle, skill_asset)
			self.node_list["txt_skill_des"].text.text = skill_cfg.skill_desc
		end
	end
end

function ArtifactBattleSelectItem:OnClickApplyBtn()
	local battle_pos_seq = ArtifactWGData.Instance:GetSetSelectedPosSeq()
	if not battle_pos_seq then
		return
	end
	local cur_artifact_type = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.data.seq).battle_type

	local battle_info_list =  ArtifactWGData.Instance:GetArtifactBattleInfo()
	local cur_battle_info = battle_info_list[battle_pos_seq + 1]
	local replace_artifact_type = -1
	if cur_battle_info.seq >= 0 then
		replace_artifact_type = ArtifactWGData.Instance:GetArtifactCfgBySeq(cur_battle_info.seq).battle_type
	end
	
	local battle_count_list = ArtifactWGData.Instance:GetBattleTypeCountList()
	if replace_artifact_type ~= cur_artifact_type and battle_count_list[cur_artifact_type] and battle_count_list[cur_artifact_type] >= 1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.MaxBattleType)
		return
	end

	ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.APPLY, self.data.seq, battle_pos_seq)
	ArtifactWGCtrl.Instance:CloseBattleSelectView()
end