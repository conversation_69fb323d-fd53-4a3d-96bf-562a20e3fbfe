NovicePopDialogView = NovicePopDialogView or BaseClass(SafeBaseView)

function NovicePopDialogView:__init()
	self.view_name = GuideModuleName.NovicePopDialogView
	-- self.open_tween = self.OpenTween
	-- self.close_tween = self.CloseTween
	self.active_close = false
	self:AddViewResource(0, "uis/view/novice_pop_dialog_prefab", "layout_novice_pop_dialog")
end

function NovicePopDialogView:__delete()
end

function NovicePopDialogView:OpenCallBack()

end

function NovicePopDialogView:CloseCallBack()
	GlobalTimerQuest:CancelQuest(self.hole_delay_timer)
	self.hole_delay_timer = nil
	if FunctionGuide.Instance:GetIsGuide() then
		FunctionGuide.Instance:StartNextStep()
	end
end

function NovicePopDialogView:LoadCallBack()
	self.cur_step = 1
	self.display = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.PS_M,
		can_drag = false,
	}
	
	self.display:SetRenderTexUI3DModel(display_data)
	-- self.display:SetUI3DModel(self.node_list["display"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
end

function NovicePopDialogView:ReleaseCallBack()
	if self.display then
		self.display:DeleteMe()
		self.display = nil
	end

	self.data = nil

	GlobalTimerQuest:CancelQuest(self.hole_delay_timer)
	self.hole_delay_timer = nil
end


function NovicePopDialogView:ShowIndexCallBack(index)
end

function NovicePopDialogView:OnFlush(param_list, index)
	for k,v in pairs(param_list) do
		if k == "all" then
			self.data = v.data
		end
	end
	if not self.data then
		self:Close()
		return
	end
	NovicePopDialogWGData.Instance:SetTriggeredList(self.data[1].id)

	self.cur_step = 1
	self:ShowStep()
end

function NovicePopDialogView:ShowStep()
	if not self.data then
		self:Close()
		return
	end

	local step_data = self.data[self.cur_step]
	if not step_data then
		self:Close()
		return
	end

	if self.hole_delay_timer then
		return
	end

	if not self.node_list["txt_content"] then
		return
	end

	self.node_list["txt_content"].text.text = step_data.word
	self.node_list["txt_name"].text.text = step_data.name
	self.display:SetMainAsset(ResPath.GetNpcModel(step_data.model))
	-- 音频
	local audio_id = step_data.talk_audio
	if audio_id and audio_id ~= "" then
		-- self.old_audio_id = audio_id
		local bundle, asset = ResPath.GetNpcTalkVoiceResByResName(audio_id)
		TalkCache.PlayGuideTalkAudio(bundle, asset)
	end

	if step_data.center_pos and step_data.center_pos ~= "" then
		local center_pos = Split(step_data.center_pos, '|')
		local pos = self.node_list.content.transform.anchoredPosition
		pos.x = tonumber(center_pos[1]) or 0
		pos.y = tonumber(center_pos[2]) or 0
		self.node_list.content.transform.anchoredPosition = pos
	end

	if step_data.display_pos and step_data.display_pos ~= "" then
		local display_pos = Split(step_data.display_pos, '|')
		local pos = self.node_list.display.transform.anchoredPosition
		pos.x = tonumber(display_pos[1]) or 0
		pos.y = tonumber(display_pos[2]) or 0
		self.node_list.display.transform.anchoredPosition = pos
	end

	if step_data.model_pos and step_data.model_pos ~= "" then
		local pos = Split(step_data.model_pos, '|')
		self.display:SetRTAdjustmentRootLocalPosition(pos[1], pos[2], pos[3])
	end

	if step_data.model_euler and step_data.model_euler ~= "" then
		self.display:SetRTAdjustmentRootLocalRotation(0, step_data.model_euler, 0)
	end

	if step_data.model_scale and step_data.model_scale ~= "" then
		self.display:SetRTAdjustmentRootLocalScale(step_data.model_scale)
	end

	self.hole_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		self.cur_step = self.cur_step + 1
		self.hole_delay_timer = nil
		self:ShowStep()
	end, step_data.hole_time)
end

function NovicePopDialogView:OpenTween()
	-- self.root_node_transform.transform.anchoredPosition = Vector2(-1500, self.root_node_transform.transform.anchoredPosition.y)
	-- local tween = self.root_node_transform.transform:DOAnchorPosX(0, 0.5)
	-- return tween

	self.root_node_transform.transform.anchoredPosition = Vector2(self.root_node_transform.transform.anchoredPosition.x, -500)
	local tween = self.root_node_transform.transform:DOAnchorPosY(0, 0.5)
	return tween
end

function NovicePopDialogView:CloseTween()
	local tween = self.root_node_transform.transform:DOAnchorPosY(-500, 0.5)
	return tween
end

function NovicePopDialogView:SetRendering(value)
	SafeBaseView.SetRendering(self, value)
	if value then
		-- 避免动画播到一半，界面被隐藏，导致坐标错误。这里恢复到0
		self.root_node_transform.transform.anchoredPosition = Vector2(0, 0)
	end
end