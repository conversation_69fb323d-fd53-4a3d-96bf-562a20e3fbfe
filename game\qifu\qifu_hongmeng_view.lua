local Tween_Time = {
	[1] = 1,
	[2] = 1.5,
	[3] = 2,
	[4] = 2.5,
}

function QiFuView:InitHongMengView()
	local exp_buy_info = QiFuWGData.Instance:GetExpBuyInfo()
	if exp_buy_info then
		local end_time = exp_buy_info.end_timestame
		if end_time > TimeWGCtrl.Instance:GetServerTime() then
			self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), end_time)
			CountDownManager.Instance:AddCountDown("hongmeng_end_countdown", BindTool.Bind1(self.UpdataEndTime, self), BindTool.Bind1(self.EndTimeCallBack, self), end_time, nil, 1)
		else
			self.node_list["shengyu_bg"]:SetActive(false)
		end
	end
	self.node_list["btn_ganwu"].button:AddClickListener(BindTool.Bind1(self.OnClickGanWu, self))
	self.node_list["btn_hongmeng_rule"].button:AddClickListener(BindTool.Bind1(self.OnClickHongMengRule, self))
	QiFuWGData.Instance:SetClickRemind()

	self.role_data_change_callback =  BindTool.Bind1(self.OnRoleDataChangeHongMeng, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"level"})

	-- if nil == self.ball_tween then
	-- 	self.ball_tween = {}
	-- 	for i = 1, 9 do
	-- 		local tween_root = self.node_list["exp_ball" .. i].rect
	-- 		self.ball_tween[i] = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 10, Tween_Time[math.ceil(i / 4)])
	-- 		self.ball_tween[i]:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	-- 	end
	-- end
end

function QiFuView:OnRoleDataChangeHongMeng(attr_name, value, old_value)
	if attr_name == "level" then
		self:Flush(TabIndex.qifu_hmwd)
	end
end

function QiFuView:UpdataEndTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	local str = ""
	if format_time.day > 0 then
		str = string.format(Language.QiFu.EndTimeShow[1],format_time.day,format_time.hour,format_time.min,format_time.s)
	elseif format_time.hour > 0 then
		str = string.format(Language.QiFu.EndTimeShow[2],format_time.hour,format_time.min,format_time.s)
	elseif format_time.min > 0 then
		str = string.format(Language.QiFu.EndTimeShow[3],format_time.min,format_time.s)
	elseif format_time.s > 0 then
		str = string.format(Language.QiFu.EndTimeShow[4],format_time.s)
	end
	self.node_list["shengyu_bg"]:SetActive(not str == "")
	self.node_list["shengyu_time"].text.text = str
end

function QiFuView:EndTimeCallBack()
	self:Close()
end


function QiFuView:DelHongMengView()
	if self.pm_list then
		self.pm_list:DeleteMe()
		self.pm_list = nil
	end

	if self.alert_all_tips3 then
		self.alert_all_tips3:DeleteMe()
		self.alert_all_tips3 = nil
	end

	-- if self.ball_tween then
	-- 	for k, v in pairs(self.ball_tween)do
	-- 		v:Kill()
	-- 	end
	-- 	self.ball_tween = nil
	-- end

	if CountDownManager.Instance:HasCountDown("hongmeng_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("hongmeng_end_countdown")
	end
	if self.role_data_change_callback then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	end
end

function QiFuView:CreateAlert()
	if not self.alert_all_tips3 then
		self.alert_all_tips3 = Alert.New()
	end

end


function QiFuView:CreatPmList()
	local data_list = RankWGData.Instance:GetRankData(RankKind.Person,PersonRankType.Level)
	local can_buy_info = QiFuWGData.Instance:GetCanBuyInfo()
	local data_info = {}
	if not self.pm_list then
		self.pm_list = AsyncListView.New(QiFuHongMengPmItem, self.node_list["ph_pm_list"])
	end
	if self.pm_list and data_list then
		for k,v in pairs(data_list) do
			-- if v.rank_index >= can_buy_info.rank_region_min and v.rank_index <= can_buy_info.rank_region_max then
			-- 	table.insert( data_info, v )
			-- end
			if v.rank_index <= 10 then
				table.insert( data_info, v )
			end

		end
		self.pm_list:SetDataList(data_info,0)
	end
end

function QiFuView:FlushHongMengView()
	self:CreatPmList()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local exp_buy_info = QiFuWGData.Instance:GetExpBuyInfo()
	local can_buy_info = QiFuWGData.Instance:GetCanBuyInfo()
	if exp_buy_info and can_buy_info then
		local sheng_num = can_buy_info.buy_limit_count - exp_buy_info.buy_count

		if nil == self.shengyu_num then
			self.shengyu_num = sheng_num
		else
			if self.shengyu_num == sheng_num then

			elseif self.shengyu_num > sheng_num then
				--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_shuijingqiu_shanguang)
 		 		--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effects.transform)
 		 		-- local exp_view = MainuiWGCtrl.Instance:GetView():GetExpView()
				-- if exp_view and exp_view.exp_effect then
				-- 	local end_pos = exp_view.exp_effect
				-- 	local bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_jinyanqiu_guangqiu)
    			-- 	TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Welfare, bundle, asset, self.node_list.end_pos, end_pos, DG.Tweening.Ease.OutCubic, 1, nil, nil, nil, 200)
    			-- end
	   		end

			self.shengyu_num = sheng_num
		end

		local is_dianfeng, world_level = RoleWGData.Instance:GetDianFengLevel(exp_buy_info.buy_limit_level)
		local color = sheng_num > 0 and "#9DF5A7" or "#FF9797"
		local level_color = vo.level <= exp_buy_info.buy_limit_level and "#9DF5A7" or "#FF9797"
		sheng_num = ToColorStr(sheng_num,color)
		self.node_list["shengyu_num"].text.text = string.format(Language.QiFu.GanWuShengYuNun, sheng_num, can_buy_info.buy_limit_count)

		if is_dianfeng then 
			self.node_list.ganwu_limint.text.text = string.format(Language.Role.DianFeng, world_level)
		else
			self.node_list.ganwu_limint.text.text = world_level .. Language.Common.Ji
		end
		self.node_list["my_level"].text.text = ToColorStr(vo.level, level_color)

		local price = QiFuWGData.Instance:GetGanWuPrice()
		self.node_list["btnganwu_txt"].text.text = Language.QiFu.GanWu2
		self.node_list["btnnum_txt"].text.text = price
		local get_exp = QiFuWGData.Instance:GetGanWuExP()
		self.node_list["cur_get_exp"].text.text = string.format(Language.QiFu.GanWuGetExp, CommonDataManager.ConverExpByThousand(get_exp, nil, false))

		local vo = GameVoManager.Instance:GetMainRoleVo()
		local max_level = RoleWGData.GetRoleMaxLevel()
		local new_vo_exp = vo.exp + get_exp
		local role_level = RoleWGData.Instance:GetRoleLevel() or 0
		local exp_cfg = RoleWGData.Instance.GetRoleExpCfgByLv(role_level) or {}
		local max_exp = exp_cfg.exp or 0
		--self.node_list.effects:SetActive(false)
		if role_level >= max_level then
			self.node_list["get_exp"]:SetActive(false)
			self.node_list["up_level"]:SetActive(false)
		else
			if new_vo_exp >= max_exp then
				local enough_exp = new_vo_exp - max_exp
				local add_level = 1
				for i=1,50 do
					local cfg = RoleWGData.Instance.GetRoleExpCfgByLv(vo.level + add_level)

					if cfg and cfg.exp and enough_exp > cfg.exp then
						enough_exp = enough_exp - cfg.exp
						add_level = add_level + 1
					else
						break
					end
				end
				if vo.level + add_level >= max_level then
					add_level = max_level - role_level
				end
				self.node_list["up_level"].text.text = string.format(Language.QiFu.HongMengLvAdd, add_level)
				self.node_list["get_exp"]:SetActive(false)
				self.node_list["up_level"]:SetActive(true)
			else
				self.node_list["get_exp"]:SetActive(true)
				self.node_list["up_level"]:SetActive(false)
				max_exp = max_exp > 0 and max_exp or 1
				local per_exp = math.floor((100 * new_vo_exp) / max_exp)
				self.node_list["get_exp"].text.text = string.format(Language.QiFu.HongMengExpAdd, per_exp) .. "%"
			end
		end
	end

	local red_show = QiFuWGData.Instance:IsShowGetHongMengRedPoint()
	self.node_list["red_point"]:SetActive(red_show > 0)
end

function QiFuView:OnClickHongMengRule()
	local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.QiFu.HongMengTitle)
    role_tip:SetContent(Language.QiFu.HongMengDesc)
end

function QiFuView:OnClickGanWu()
	local price = QiFuWGData.Instance:GetGanWuPrice()
	local exp_buy_info = QiFuWGData.Instance:GetExpBuyInfo()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local can_buy_info = QiFuWGData.Instance:GetCanBuyInfo()
	if nil == exp_buy_info or nil == can_buy_info then
		return
	end
	local sheng_num = can_buy_info.buy_limit_count - exp_buy_info.buy_count
	if level >= exp_buy_info.buy_limit_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.QiFu.Hint1)
		return
	elseif sheng_num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.QiFu.Hint2)
		return
	end
	local is_cun = QiFuWGCtrl.Instance:GetWelfareGanWuCheckBox()
	if not is_cun then
		self:SendGanWu()
		return
	end

	self.alert_all_tips3:SetOkFunc(BindTool.Bind(self.SendGanWu, self))
	self.alert_all_tips3:SetLableString(string.format(Language.Welfare.WelfareRender14, price))
	self.alert_all_tips3:SetShowCheckBox(true)
	self.alert_all_tips3:Open()
end

function QiFuView:SendGanWu()
	local check_box = self.alert_all_tips3:SetCheckBoxState()
	if check_box then
		QiFuWGCtrl.Instance:SetWelfareGanWuCheckBox(not check_box)
	end

	QiFuWGCtrl.Instance:SendExpBuyReqBuy()
end

QiFuHongMengPmItem = QiFuHongMengPmItem or BaseClass(BaseRender)
function QiFuHongMengPmItem:__init()

end

function QiFuHongMengPmItem:LoadCallBack()

end

function QiFuHongMengPmItem:__delete()

end

function QiFuHongMengPmItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local is_top_three = self.index <= 3
	self.node_list.top_three_bg:SetActive(is_top_three)
	self.node_list.bg:SetActive(not is_top_three)
	if self.index <= 3 then
		self.node_list["top_three_bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a2_qf_jy_pm_" .. self.index))
		self.node_list["img_pm"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
	end

	local is_dianfeng, world_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)

	if is_dianfeng then
		self.node_list.lbl_level.text.text = string.format(Language.QiFu.GanWuDFLevel, world_level)
	else
		self.node_list.lbl_level.text.text = world_level
	end

	self.node_list["lbl_name"].text.text = self.data.user_name

	if self.data.rank_index then
		self.node_list["img_pm"]:SetActive(self.data.rank_index <= 3)
		self.node_list["lbl_pm"]:SetActive(self.data.rank_index > 3)
		self.node_list["lbl_pm"].text.text = self.data.rank_index
	end
end






