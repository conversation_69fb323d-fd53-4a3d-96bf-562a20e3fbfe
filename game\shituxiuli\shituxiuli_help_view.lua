ShiTuXiuLiHelpView = ShiTuXiuLiHelpView or BaseClass(SafeBaseView)

function ShiTuXiuLiHelpView:__init(view_name)
	self.view_name = "ShiTuXiuLiHelpView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/shituxiuli_prefab", "help_list_view")
	-- self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	-- self:SetMaskBg(true, true)
end

function ShiTuXiuLiHelpView:LoadCallBack()
	self:InitPanel()
	self:InitListener()
end

function ShiTuXiuLiHelpView:ReleaseCallBack()
	if self.help_list then
		self.help_list:DeleteMe()
		self.help_list = nil
	end
end

function ShiTuXiuLiHelpView:OnFlush(param_t)
	self:RefreshView()
end

function ShiTuXiuLiHelpView:InitPanel()
	self.help_list = AsyncListView.New(ShiTuXiuLiHelpItem, self.node_list.ph_btn_list)
end

function ShiTuXiuLiHelpView:InitListener()
	XUI.AddClickEventListener(self.node_list.close_btn, BindTool.Bind(self.Close, self))
end

function ShiTuXiuLiHelpView:RefreshView()
	local help_info = ShiTuXiuLiWGData.Instance:GetStudentHelpInfo()
	local help_info_list = help_info and help_info.student_help_info
	if help_info_list then
		local data_list = {}
		for i=1,#help_info_list do
			if help_info_list[i].teacher_is_help == 0 then
				data_list[#data_list + 1] = help_info_list[i]
			end
		end
		self.help_list:SetDataList(data_list)
	end
end

-----------------------------------------------------------------------------------------------------

ShiTuXiuLiHelpItem = ShiTuXiuLiHelpItem or BaseClass(BaseRender)

function ShiTuXiuLiHelpItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.root_img, BindTool.Bind(self.OnClickHelpItem, self))
end

function ShiTuXiuLiHelpItem:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end
	local student_cfg = ShiTuXiuLiWGData.Instance:GetStudentCfgByTaskid(data.task_id)
	if student_cfg then
		self.node_list.help_desc.text.text = string.format(student_cfg.text, student_cfg.progress)
	end
end

function ShiTuXiuLiHelpItem:OnClickHelpItem()
	local data = self:GetData()
	if not data then
		return
	end
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_ACCEPT_HELP, data.task_id)
end