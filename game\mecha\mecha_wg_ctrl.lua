require("game/mecha/mecha_view")
require("game/mecha/mecha_wg_data")
require("game/mecha/mecha_equip_view")
require("game/mecha/mecha_fighter_plane_view")
require("game/mecha/mecha_to_fight_view")
require("game/mecha/mecha_weapon_view")
require("game/mecha/mecha_wing_view")
require("game/mecha/mecha_armament_view")
require("game/mecha/mecha_select_view")

MechaWGCtrl = MechaWGCtrl or BaseClass(BaseWGCtrl)

function MechaWGCtrl:__init()
	if MechaWGCtrl.Instance ~= nil then
		print_error("[MechaWGCtrl] attempt to create singleton twice!")
		return
	end
	MechaWGCtrl.Instance = self

	self.view = MechaView.New(GuideModuleName.MechaView)
	self.mecha_select_view = MechaSelecttView.New()
	self.mecha_armament_view = MechaArmamentView.New()
	self.data = MechaWGData.New()

	self:RegisterProtocol(CSMechanClientReq)
	self:RegisterProtocol(SCMechanAllInfo, "OnSCMechanAllInfo")
	self:RegisterProtocol(SCMechanPutonInfo, "OnSCMechanPutonInfo")
	self:RegisterProtocol(SCMechanPartStarInfo, "OnSCMechanPartStarInfo")
	self:RegisterProtocol(SCMechanMainFightInfo, "OnSCMechanMainFightInfo")
	self:RegisterProtocol(SCMechanHelpFightOpenInfo, "OnSCMechanHelpFightOpenInfo")
	self:RegisterProtocol(SCMechanHelpFightBattleInfo, "OnSCMechanHelpFightBattleInfo")
	self:RegisterProtocol(SCMechanChooseWingSkillInfo, "OnSCMechanChooseWingSkillInfo")
	self:RegisterProtocol(SCMechanBianshenInfo, "OnSCMechanBianshenInfo")

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level", "special_appearance"})
end

function MechaWGCtrl:__delete()
	if self.view ~= nil then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.mecha_select_view ~= nil then
		self.mecha_select_view:DeleteMe()
		self.mecha_select_view = nil
	end

	if self.mecha_armament_view ~= nil then
		self.mecha_armament_view:DeleteMe()
		self.mecha_armament_view = nil
	end

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	MechaWGCtrl.Instance = nil
end

--------------------------------------------------------view_opa--------------------------------------------------------------
function MechaWGCtrl:OpenMechaSelectView(fight_pos_data)
	if self.mecha_select_view then
		self.mecha_select_view:SetFightPosData(fight_pos_data)
		self.mecha_select_view:Open()
	end
end

function MechaWGCtrl:FlushMechaSelectView()
	if self.mecha_select_view then
		self.mecha_select_view:Flush()
	end
end

function MechaWGCtrl:CloseMechaSelectView()
	if self.mecha_select_view and self.mecha_select_view:IsOpen() then
		self.mecha_select_view:Close()
	end
end

function MechaWGCtrl:OpenMechaArmamentView(mecha_seq)
	if self.mecha_armament_view then
		self.mecha_armament_view:SetSelectMechaSeq(mecha_seq)
		self.mecha_armament_view:Open()
	end
end

function MechaWGCtrl:FlushMechaArmamentView()
	if self.mecha_armament_view then
		self.mecha_armament_view:Flush()
	end
end

---------------------------------------------------------protocol----------------------------------------------------------------
function MechaWGCtrl:SendRequest(operate_type, param1, param2, param3, param_list)
	-- print_error("请求", operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMechanClientReq)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param_list = param_list or {}
	protocol:EncodeAndSend()
end

function MechaWGCtrl:OnSCMechanAllInfo(protocol)
	-- print_error("------------------OnSCMechanAllInfo------------------", protocol)
	self.data:SetMechanAllInfo(protocol)
    RemindManager.Instance:Fire(RemindName.MechaFighterPlane)
    RemindManager.Instance:Fire(RemindName.MechaWing)
    RemindManager.Instance:Fire(RemindName.MechaWeapon)
    RemindManager.Instance:Fire(RemindName.MechaToFight)
    RemindManager.Instance:Fire(RemindName.MechaEquip)
	ViewManager.Instance:FlushView(GuideModuleName.MechaView)

	MainuiWGCtrl.Instance:ChangeMechaBsBtnState()
end

function MechaWGCtrl:OnSCMechanPutonInfo(protocol)
	-- print_error("------------------OnSCMechanPutonInfo------------------", protocol)
	self.data:SetMechanPutonInfo(protocol)
	RemindManager.Instance:Fire(RemindName.MechaToFight)
	MechaWGCtrl.Instance:FlushMechaArmamentView()
	ViewManager.Instance:FlushView(GuideModuleName.MechaView)
end

function MechaWGCtrl:OnSCMechanPartStarInfo(protocol)
	-- print_error("------------------OnSCDiyChuanWenAllInfo------------------", protocol)
	self.data:SetMechanPartStarInfo(protocol)
    RemindManager.Instance:Fire(RemindName.MechaFighterPlane)
	RemindManager.Instance:Fire(RemindName.MechaWing)
	RemindManager.Instance:Fire(RemindName.MechaWeapon)
	RemindManager.Instance:Fire(RemindName.MechaToFight)
	ViewManager.Instance:FlushView(GuideModuleName.MechaView)
end

function MechaWGCtrl:OnSCMechanMainFightInfo(protocol)
	-- print_error("------------------OnSCMechanMainFightInfo------------------", protocol)
	local old_bianshen_seq = self.data:GetBianShenSeq()
	local new_sbianshen_seq = protocol.bianshen_seq

	if new_sbianshen_seq ~= old_bianshen_seq and new_sbianshen_seq >= 0 then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
	end

	self.data:SetMechanMainFightInfo(protocol)
	self:CloseMechaSelectView()

	RemindManager.Instance:Fire(RemindName.MechaToFight)
	ViewManager.Instance:FlushView(GuideModuleName.MechaView)

	MainuiWGCtrl.Instance:ChangeMechaBsBtnState()
end

function MechaWGCtrl:OnSCMechanHelpFightOpenInfo(protocol)
	-- print_error("------------------OnSCMechanHelpFightOpenInfo------------------", protocol)
	self.data:SetMechanHelpFightOpenInfo(protocol)
	RemindManager.Instance:Fire(RemindName.MechaToFight)
	ViewManager.Instance:FlushView(GuideModuleName.MechaView)
end

function MechaWGCtrl:OnSCMechanHelpFightBattleInfo(protocol)
	-- print_error("------------------OnSCMechanHelpFightBattleInfo------------------", protocol)
	local seq = protocol.seq
	local old_help_battle_seq = self.data:GetHelpFightBattleSeq(seq)
	local new_help_battle_seq = protocol.mechan_seq

	if old_help_battle_seq ~= new_help_battle_seq and new_help_battle_seq >= 0 then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_zhuzhan, is_success = true, pos = Vector2(0, 0)})
	end

	self.data:SetMechanHelpFightBattleInfo(protocol)
	self:CloseMechaSelectView()
	RemindManager.Instance:Fire(RemindName.MechaToFight)
	ViewManager.Instance:FlushView(GuideModuleName.MechaView)
end

function MechaWGCtrl:OnSCMechanChooseWingSkillInfo(protocol)
	-- print_error("------------------OnSCMechanChooseWingSkillInfo------------------", protocol)
	self.data:SetMechanChooseWingSkillInfo(protocol)
end

function MechaWGCtrl:OnSCMechanBianshenInfo(protocol)
	-- print_error("------------------OnSCMechanBianshenInfo------------------", protocol)
	self.data:SetMechanBianshenInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MechaView)
	MainuiWGCtrl.Instance:ChangeMechaBsBtnState()
end

---------------------------------------------------------item_change--------------------------------------------------------------
function MechaWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:IsMechaActivePartItem(change_item_id) then
			FunOpen.Instance:DoOpenFun(FunOpen.Instance:GetFunByName(FunName.MechaView), true)
		end

		if self.data:IsMFPCostItem(change_item_id) then
			self.data:CalFighterPlaneRemind()
			RemindManager.Instance:Fire(RemindName.MechaFighterPlane)
			ViewManager.Instance:FlushView(GuideModuleName.MechaView, TabIndex.mecha_fighter_plane)
		elseif self.data:IsMWCostItem(change_item_id) then
			self.data:CalWingRemind()
			RemindManager.Instance:Fire(RemindName.MechaWing)
			ViewManager.Instance:FlushView(GuideModuleName.MechaView, TabIndex.mecha_wing)
		elseif self.data:IsMWPCostItem(change_item_id) then
			self.data:CalWeaponRemind()
			RemindManager.Instance:Fire(RemindName.MechaWeapon)
			ViewManager.Instance:FlushView(GuideModuleName.MechaView, TabIndex.mecha_weapon)
		elseif self.data:IsMTFCostItem(change_item_id) then
			if self.data:NeedCheckFightState() then
				self.data:CalToFightRemind()
				RemindManager.Instance:Fire(RemindName.MechaToFight)
				ViewManager.Instance:FlushView(GuideModuleName.MechaView, TabIndex.mecha_to_fight)
			end
		end
	end
end

function MechaWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
		if self.data:NeedCheckFightState() then
			self.data:CalToFightRemind()
			RemindManager.Instance:Fire(RemindName.MechaToFight)
			ViewManager.Instance:FlushView(GuideModuleName.MechaView, TabIndex.mecha_to_fight)
		end
	elseif attr_name == "special_appearance" then
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			local is_gundam = main_role:IsGundam()

			if is_gundam then
				self:OnBSGundam()
			end
		end
	end
end

function MechaWGCtrl:OnBSGundam()
	local cur_bianshen_mecha_seq = self.data:GetBianShenSeq()

	if cur_bianshen_mecha_seq >= 0 then
		local cur_mecha_cfg = self.data:GetMechaCfgBySeq(cur_bianshen_mecha_seq)

		if not IsEmptyTable(cur_mecha_cfg) then
			MainuiWGCtrl.Instance:ShowMechaScreenEffect()
			PlaySkillFloatWordWGCtrl.Instance:OpenFloatWordViewByType(cur_mecha_cfg.float_word_type)
		end
	end
end


--[[
模型资源编号 = 机甲编号 * 100000 + 部位 * 1000 + 【类型】 编号


【部位】
躯干 0
左臂 1
右臂 2
左腿 3
右腿 4
左翼 5
右翼 6
武器 7


【类型】 可自定义成
剑 100 + 编号
枪 200 + 编号


机甲1编号1躯干1 = 100001
机甲1编号1左臂1 = 101001
机甲1编号1右臂1 = 102001
机甲1编号1左腿1 = 103001
机甲1编号1右腿1 = 104001
机甲1编号1左翼1 = 105001
机甲1编号1右翼1 = 106001
机甲1编号1武器1(剑) = 107101
机甲1编号1武器1(枪) = 107201
]]