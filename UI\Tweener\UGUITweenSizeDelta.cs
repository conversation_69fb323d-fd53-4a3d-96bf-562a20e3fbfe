﻿using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UGUI/Tween/UGUI Tween SizeDelta")]
[RequireComponent(typeof(RectTransform))]
public class UGUITweenSizeDelta : UGUITweener 
{
	public Vector2 from = Vector2.zero;
	public Vector2 to = Vector2.zero;

	bool mCached = false;
	RectTransform mTrans;

	void Cache ()
	{
		mCached = true;
		mTrans = GetComponent<RectTransform>();
	}

	public Vector2 value
	{
		get
		{
			if (!mCached) Cache();
			if (mTrans != null) return mTrans.sizeDelta;
			return  Vector2.zero;
		}
		set
		{
			if (!mCached) Cache();
			if (mTrans != null) mTrans.sizeDelta = value;
		}
	}

	protected override void OnUpdate (float factor, bool isFinished) { value = from * (1f - factor) + to * factor; }

	static public UGUITweenSizeDelta Begin (GameObject go, float duration, Vector2 vec2)
	{
		#if UNITY_EDITOR
		if (!Application.isPlaying) return null;
		#endif
		UGUITweenSizeDelta comp = UGUITweener.Begin<UGUITweenSizeDelta>(go, duration);
		comp.from = comp.value;
		comp.to = vec2;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	[ContextMenu("Set 'From' to current value")]
	public override void SetStartToCurrentValue () { from = value; }

	[ContextMenu("Set 'To' to current value")]
	public override void SetEndToCurrentValue () { to = value; }

	[ContextMenu("Assume value of 'From'")]
	void SetCurrentValueToStart () { value = from; }

	[ContextMenu("Assume value of 'To'")]
	void SetCurrentValueToEnd () { value = to; }
}
