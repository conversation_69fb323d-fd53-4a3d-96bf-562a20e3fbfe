-----------------------------------------
--赏金任务-守护仙女
-----------------------------------------
FuBenBountyFairyLogic = FuBenBountyFairyLogic or BaseClass(CommonFbLogic)

function FuBenBountyFairyLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.show_word_img = true
end

function FuBenBountyFairyLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function FuBenBountyFairyLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	FuBenPanelWGCtrl.Instance:OpenBountyTaskView()
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	
end

function FuBenBountyFairyLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	local monster_id = 0
	local monster_list = Scene.Instance:GetMonsterList()
	for k, v in pairs(monster_list) do
		if v:GetVo().monster_id == 11262 and self.show_word_img then
			monster_id = v:GetVo().monster_id
			FuBenPanelWGCtrl.Instance:VistWordImg(monster_id)
			self.show_word_img = false
			break
		end
	end
	BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
end

-- 获取挂机打怪的敌人
function FuBenBountyFairyLogic:GetGuiJiMonsterEnemy()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = 3600
	return self:SelectObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
end

function FuBenBountyFairyLogic:SelectObjHelper(obj_list, x, y, distance_limit, select_type)
	local target_obj = nil
	local target_distance = distance_limit

	for _, v in pairs(obj_list) do
		if v:IsCharacter() or v:GetModel():IsVisible() then 
			local can_select = Scene.Instance:IsEnemy(v, self.main_role)
			if Scene.Instance:GetIsInAppearLimitList(v) then --限制显示的不让选择
				can_select = false
			end

			if can_select then
				local target_x, target_y = v:GetLogicPos()
				local distance = GameMath.GetDistance(x, y, target_x, target_y, false)
				local distance_mpos = GameMath.GetDistance(71, 23, target_x, target_y, false)
				local distance_pos = GameMath.GetDistance(71, 23, 43, 32, false)
				if distance < target_distance and distance_mpos < distance_pos then
					if v:IsInBlock() then
						if nil == target_obj then
							target_obj = v
						end
					else
						target_obj = v
						target_distance = distance
					end
				end
			end
		end
	end

	if target_obj == nil then
		self:MoveToPos(60, 28)
	end

	return target_obj, target_distance
end

function FuBenBountyFairyLogic:Out()
	CommonFbLogic.Out(self)
	self.show_word_img = true
	FuBenPanelWGCtrl.Instance:VistWordImg(0)	
	FuBenPanelWGCtrl.Instance:CloseBountyTaskView()
    GuajiWGCtrl.Instance:StopGuaji()

end