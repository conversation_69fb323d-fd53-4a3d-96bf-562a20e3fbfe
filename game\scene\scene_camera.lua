Scene = Scene or BaseClass(BaseWGCtrl)
local ABS = math.abs
local GameRootTransform = GameObject.Find("GameRoot").transform
local UniversalAdditionalCameraData = typeof(UnityEngine.Rendering.Universal.UniversalAdditionalCameraData)
DownAngleOfCamera = 180
local TargetGroupMaxDistance = 25

-- ==================================================================================
-- ================================= 摄像机 =========================================
-- ==================================================================================
function Scene:InitSceneCameraLua()
    self.boss_camera = BossCamera.New()

    self.urp_ui_camera_data = nil
    self.urp_scene_camera_data = nil
    self.ctrl_follow_camera_enabled = nil
    self.camera_target_group_list = {}
    self.camera_is_follow_target_group = false
    self.last_move_cache_end_type = nil
    self.camera_xunlu_state = -1
    self.real_camera_enter_battle = nil
    self.camera_enter_battle = nil
    self:BindGlobalEvent(SettingEventType.MAIN_CAMERA_MODE_CHANGE, BindTool.Bind1(self.UpdateCameraMode, self))
    self:BindGlobalEvent(SettingEventType.MAIN_CAMERA_SETTING_CHANGE, BindTool.Bind1(self.UpdateCameraSetting, self))


    self:BindGlobalEvent(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjectCreateToCamera, self))
    self:BindGlobalEvent(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjectDeadToCamera, self))
    self:BindGlobalEvent(ObjectEventType.OBJ_DELETE, BindTool.Bind(self.OnObjDeleteToCamera, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_CREATE, BindTool.Bind(self.OnMainRoleCreateToCamera, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind(self.OnMainRoleDeadToCamera, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_REALIVE, BindTool.Bind(self.OnMainRoleRealiveToCamera, self))
    self:BindGlobalEvent(GlobalTableValChange.GUAJI_CACHE, BindTool.Bind(self.OnGuaJiCacheChangeToCamera, self))
    self:BindGlobalEvent(GlobalTableValChange.ATK_CACHE, BindTool.Bind(self.OnAtkCacheChangeToCamera, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRoleChangePosToCamera, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, BindTool.Bind(self.OnMainRoleAutoXunLuToCamera, self))
    self:BindGlobalEvent(ObjectEventType.EXIT_FIGHT, BindTool.Bind(self.OnMainRoleExitFightToCamera, self))
    self:BindGlobalEvent(ObjectEventType.ENTER_FIGHT, BindTool.Bind(self.OnMainRoleEnterFightToCamera, self))
end

function Scene:DeleteScenceCameraLua()
    self.boss_camera:DeleteMe()

    if self.camera_follow_obj then
        ResPoolMgr:Release(self.camera_follow_obj)
        self.camera_follow_obj = nil
    end

    MainCameraFollow = nil
    MainCameraSnapshot = nil
    MainCamera = nil
    UICamera = nil
    CAMERA_TYPE = -1
    self.ctrl_follow_camera_enabled = nil
    self.urp_ui_camera_data = nil
    self.urp_scene_camera_data = nil
    self.camera_target_group_list = {}
    self.camera_xunlu_state = -1
    self.last_move_cache_end_type = nil
    self.real_camera_enter_battle = nil
    self.camera_enter_battle = nil
end

-- ==================================================================================
-- ================================= 旧 =========================================
-- ==================================================================================
function Scene:OnMainLoadEndToCamera(scene_id)
    --创建场景摄像机
    self:CreateCameraFollow()
end

function Scene:OnLoadEndToCamera(scene_id, is_same)
    -- 转场景时手动调用一次UpdateDistances
    if not IsNil(self.camera_follow_obj) then
        local cameraculling_distance = self.camera_follow_obj:GetComponentInChildren(typeof(CameraCullingDistance))
        if not IsNil(cameraculling_distance) then
            cameraculling_distance:UpdateDistances()
        end
    end

    self:SetCurFixedCamera(scene_id)
end

function Scene:OnLoadSceneMainCompleteToCamera(scene_id, scene_type, is_simulation)
    MainCamera.enabled = self.ctrl_follow_camera_enabled == nil or self.ctrl_follow_camera_enabled

    self:UpdateCameraMode()
    DownAngleOfCamera = 180 + MainCamera.transform.eulerAngles.y
    self:CheckCameraAngel()

    if not is_simulation then
        self:RecoverCamera(true)
    end

    self:AddMainRoleToTargetGroup()
end

--创建场景摄像机
function Scene:CreateCameraFollow()
    if nil == MainCameraFollow then
        self.camera_follow_obj = ResPoolMgr:TryGetGameObject("scenes_prefab", "CameraFollow")
        self.camera_follow_obj.transform:SetParent(GameRootTransform, false)
        MainCameraFollow = self.camera_follow_obj:GetComponent(typeof(CameraFollow))
        MainCameraSnapshot = self.camera_follow_obj:GetComponentInChildren(typeof(UIGlassBlurCtrl))
        self.urp_scene_camera_data = self.camera_follow_obj.transform:Find("Camera").gameObject:GetComponent(UniversalAdditionalCameraData)
        self.camera_default_setting = {}
        self.camera_default_setting.OriginAngle = MainCameraFollow.OriginAngle
        self.camera_default_setting.Distance = MainCameraFollow.Distance
        MainuiWGCtrl.Instance:InitDistanceChangeCallback()
        ViewManager.Instance:OpenWaitScreenShotView()
    end

    local camera_focal_point = GameObject.New("CamerafocalPoint")
    MainCameraFollow:CreateFocalPoint(camera_focal_point)
    MainCamera = self.camera_follow_obj:GetComponentInChildren(typeof(UnityEngine.Camera), true)

    self.yy_wave_effect = nil
    local ui_camera_node = GameObject.Find("GameRoot/UICamera")
    if not IsNil(ui_camera_node) then
        self.yy_wave_effect = ui_camera_node:GetComponent(typeof(Game.GameObjectAttach))
        if not self.urp_ui_camera_data then
            self.urp_ui_camera_data = ui_camera_node.gameObject:GetComponent(UniversalAdditionalCameraData)
        end

        UICamera = ui_camera_node:GetComponent(typeof(UnityEngine.Camera))
    end

    -- 设置HUD场景摄象机
    if not IsNil(MainCamera) then
        HUDManager.Instance:SetHUDSceneCamera(MainCamera)
    end

    if not IsNil(UICamera) then
        HUDManager.Instance:SetHUDMainCamera(UICamera)
    end

    self.boss_camera:InitBossFollowCamera()
end

-- 设置跟随摄像机
function Scene:CtrlFollowCameraEnabled(is_show)
    if self.ctrl_follow_camera_enabled == is_show then
        return
    end

    self.ctrl_follow_camera_enabled = is_show
    if not IsNil(MainCamera) then
        MainCamera.enabled = is_show
    end
end

function Scene:UpdateCameraMode(param)
    local guide_flag_list = SettingWGData.Instance:GetSettingDataListByKey(HOT_KEY.CAMERA_KEY_FLAG)
    local flag = guide_flag_list.item_id
    self:SetCameraMode(flag)
end

function Scene:SetCameraMode(value, is_from_btn)
    value = value or 0
    if CAMERA_TYPE == value then
        return
    end
    if nil == MainCameraFollow or nil == MainCamera then
        return
    end

    CAMERA_TYPE = value

    if is_from_btn then
        self:CheckCameraByModeChange()
    end

    self:UpdateCameraSetting(is_from_btn)

    Scheduler.Delay(function()
        self.main_role:UpdateCameraFollowTarget(false)
    end)
end

--默认摄像机距离和角度
local DefaultDistance = 7
local DefaultAnble = Vector2(14, 0)
local cacha_camera_y
-- 巡游锁定视角距离
local XunYouFixDis = 18
function Scene:UpdateCameraSetting(is_from_btn)
    if IsNil(MainCameraFollow) then
        return
    end

    if CAMERA_TYPE == CameraType.Free then
        MainCameraFollow.AllowRotation = true
        MainCameraFollow.AllowXRotation = true
        MainCameraFollow.AllowYRotation = true
        local rotation_x = SettingWGData.Instance:GetSettingDataListByKey(HOT_KEY.CAMERA_ROTATION_X).item_id or 0
        local rotation_y = SettingWGData.Instance:GetSettingDataListByKey(HOT_KEY.CAMERA_ROTATION_Y).item_id or 0
        local distance = SettingWGData.Instance:GetSettingDataListByKey(HOT_KEY.CAMERA_DISTANCE).item_id or 0
        if self.camera_default_setting then
            rotation_x = rotation_x == 0 and self.camera_default_setting.OriginAngle.x or rotation_x
            rotation_y = rotation_y == 0 and self.camera_default_setting.OriginAngle.y or rotation_y
            distance = distance == 0 and self.camera_default_setting.Distance or distance
        end

        -- 容错玩家在巡游时调整过摄像机，结果巡游中掉线，导致距离无法恢复
        local main_role = self:GetMainRole()
        if main_role ~= nil and not main_role:IsInXunYou() and distance >= XunYouFixDis then
            distance = self.camera_default_setting and self.camera_default_setting.Distance or distance
        end

        if main_role:IsInXunYou() and is_from_btn then
            distance = XunYouFixDis
            local angle = MainCameraFollow.transform.localEulerAngles
            rotation_x = angle.x
            rotation_y = angle.y
        end

        if rotation_x > 180 then    --大于180时就应该是负值了
            rotation_x = rotation_x - 360
        end
        if not is_from_btn then
            self:ChangeCamera(ADJUST_CAMERA_TYPE.NORMAL, rotation_x, rotation_y, distance, nil)
        end
    elseif CAMERA_TYPE == CameraType.Fixed then
        MainCameraFollow.AllowRotation = true
        MainCameraFollow.AllowXRotation = true
        MainCameraFollow.AllowYRotation = false

        local rotation_y
        if cacha_camera_y then
            rotation_y = MainCamera.transform.parent.transform.localEulerAngles.y
        else
            rotation_y = SettingWGData.Instance:GetSettingDataListByKey(HOT_KEY.CAMERA_ROTATION_Y).item_id or 0
        end

        rotation_y = rotation_y == 0 and DefaultAnble.y or rotation_y

        if rotation_y == 0 then  ---这里获取一下配置相关的rotation_y
            local fuben_data = FuBenWGData.Instance:GetFbCamearCfg(self:GetSceneId())
            if fuben_data then
                rotation_y = fuben_data.rotation_y
            end
        end

        cacha_camera_y = rotation_y
        local fix_dis = Scene.Instance:GetFixCameraDis(DefaultDistance)

        local camera_type = ADJUST_CAMERA_TYPE.NORMAL
        if is_from_btn then
            camera_type = ADJUST_CAMERA_TYPE.CLICK_FIX
        end

        local main_role = self:GetMainRole()
        if main_role ~= nil and not self.is_npc_talk_lock then
            if main_role:IsInXunYou() then
                camera_type = ADJUST_CAMERA_TYPE.XUNYOU
            end
        end
        
        self:ChangeCamera(camera_type, DefaultAnble.x, rotation_y, fix_dis, nil)
    end
end

function Scene:CheckCameraAngel()
    if self.old_scene_id ~= -1 and self:GetSceneType() == SceneType.Common and CAMERA_TYPE == CameraType.Fixed and self.camera_cache == nil then
        self:ChangeToDefCameraAngel()
    end
end

function Scene:ChangeToDefCameraAngel()
   local y = MainCamera.transform.parent.transform.localEulerAngles.y
    self:ChangeCamera(ADJUST_CAMERA_TYPE.NORMAL, DefaultAnble.x, y, DefaultDistance, nil)
end

local MaxZoom = 10
local LeastZoom = 2
local LeastDistance = 3
function Scene:UpdateCameraDistance()
    if IsNil(MainCameraFollow) or RoleWGData.Instance:IsHoldAngle() then
        return
    end
    if MainCameraFollow.Distance <= LeastDistance and not self.is_npc_talk_lock then
        MainuiWGData.IsSetCameraZoom = false
        MainCameraFollow.ZoomSmoothing = LeastZoom
        self:SetCameraDis(Scene.Instance:GetFixCameraDis(DefaultDistance))

        GlobalTimerQuest:AddDelayTimer(function ()
            if IsNil(MainCameraFollow) then
                return
            end
            MainCameraFollow.ZoomSmoothing = MaxZoom
            MainuiWGData.IsSetCameraZoom = true
        end,8)
    end
end

-- 进引导副本需要写死视角
function Scene:SetGuideFixedCamera(rotation_x, rotation_y, reset, is_scene_pos)
    if IsNil(MainCameraFollow) then
        return
    end

    local scene_id = self:GetSceneId()
    local param_t = {scene_id = scene_id}
    local camera_type = ADJUST_CAMERA_TYPE.NORMAL
    if reset ~= nil and reset == 1 then
        camera_type = ADJUST_CAMERA_TYPE.CAN_CHANGE
    else
        if is_scene_pos then
            camera_type = ADJUST_CAMERA_TYPE.SCENE_POS
        end
    end

    Scene.Instance:ChangeCamera(camera_type, rotation_x, rotation_y, DefaultDistance, param_t)
end

-- 战斗摄像机角度调整
function Scene:FlushFightCamera(stop_x, stop_y, dis)
    -- NPC对话锁定时不允许调整摄像机
    if IsNil(MainCameraFollow) or self.is_npc_talk_lock then
        return
    end

    if stop_x and stop_y then
        MainCameraFollow:ForceTransitionToAngle(Vector2(stop_x, stop_y), false, 0)
    end
    
    -- 设置摄像机距离
    if dis then
        self:SetCameraDis(self:GetFixCameraDis(dis))
    end
end

function Scene:GetFixCameraDis(value)
    local fix_dis = value or DefaultDistance
    if CAMERA_TYPE ~= CameraType.Fixed then
        return fix_dis
    end

    local main_role = self:GetMainRole()
    if main_role ~= nil and not self.is_npc_talk_lock then
        if main_role:IsInXunYou() then
            fix_dis = XunYouFixDis
        end
    end

    return fix_dis
end

function Scene:SetCurFixedCamera(scene_id)
    if self.main_role then
        local pos_x, pos_y = self.main_role:GetLogicPos()
        local camear_cfg = FuBenWGData.Instance:GetOrdCamearCfg(scene_id, pos_x, pos_y)
        if camear_cfg then
            local rotation_x = CAMERA_TYPE == CameraType.Fixed and DefaultAnble.x or camear_cfg.rotation_x
            self:SetGuideFixedCamera(rotation_x, camear_cfg.rotation_y, camear_cfg.reset, true)
        end
        --self.setcamera_scene_id = scene_id
    end
end

function Scene:TrySetCameraBehind()
    if IsNil(MainCameraFollow) then
        return
    end

    local main_role = self:GetMainRole()
    if main_role == nil or main_role:IsDeleted() then
        return
    end

    local draw_obj = main_role:GetDrawObj()
    if draw_obj ~= nil then
        local r_rotate = draw_obj:GetRotation()
        local param_t = {is_ro_immediately = true}
        local pos_y = r_rotate.eulerAngles.y
        local pos_x = MainCameraFollow.transform.localEulerAngles.x
        if pos_y > 180 then
            pos_y = pos_y - 360
        end

		self:ChangeCamera(ADJUST_CAMERA_TYPE.NORMAL, pos_x, pos_y, nil, param_t)
    end
end

function Scene:GetCameraAutoTime()
    local time = 0
    local main_role = self:GetMainRole()

    if main_role ~= nil and main_role:IsInXunYou() then
        time = 5
    end

    return time
end

local cache_recover = nil
function Scene:CheckCanOperaCamera()
    cache_recover = nil

    if self.camera_cache ~= nil and self.camera_cache.camera_type ~= nil then
        local camera_type = self.camera_cache.camera_type
        if camera_type == ADJUST_CAMERA_TYPE.NO_CAN_CHANGE then
            return false, camera_type == ADJUST_CAMERA_TYPE.NO_CAN_CHANGE
        end
    end

    if self.camera_cache ~= nil and self.camera_cache.camera_type ~= nil and (self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.NORMAL
        or self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.FUNBEN
        or self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.CLICK_FIX
        or self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.SCENE_POS) then
        self:ClearCameraCache()
    end

    return true
end

function Scene:CheckAreaCamera()
    if self.camera_cache == nil or self.camera_cache.param_t == nil or self.camera_cache.camera_type == nil or self.camera_cache.camera_type ~= ADJUST_CAMERA_TYPE.AREA then
        return
    end

    local data = self.camera_cache.param_t
    if data.x ~= nil and data.y ~= nil and data.w ~= nil and data.h ~= nil and data.scene_id ~= nil then
        self:RecoverCamera()
    end
end

function Scene:GetIsCanChangeCameraMode()
    if self.camera_cache ~= nil and self.camera_cache.camera_type ~= nil and
        (self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.NO_CAN_CHANGE and CAMERA_TYPE == CameraType.Fixed) then
        return false
    end

    return true
end

-- 玩家处于任务相机和区域相机，手动切换到3D，清空缓存
function Scene:CheckCameraByModeChange()
    if CAMERA_TYPE == CameraType.Free and self.camera_cache ~= nil and self.camera_cache.camera_type ~= nil
        and (self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.TASK or self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.AREA) then
        self:ClearCameraCache()
    end
end


function Scene:CheckTaskCamera(data)
    if data == nil or data.task_id == nil then
        return
    end

    if self.camera_cache == nil or self.camera_cache.camera_type == nil or self.camera_cache.camera_type ~= ADJUST_CAMERA_TYPE.TASK then
        return
    end

    local task_cfg = TaskWGData.Instance:GetTaskConfig(data.task_id)
    local task_state = TaskWGData.Instance:GetTaskStatus(data.task_id)
    local task_is_completed = TaskWGData.Instance:GetTaskIsCompleted(data.task_id)
    if task_cfg and task_cfg.camera and task_cfg.camera ~= "" then
        local camera_t = Split(task_cfg.camera, "|")
        if camera_t ~= nil then
            for k,v in pairs(camera_t) do
                local tab = Split(v, "##")
                -- 任务表的camera 参数只有一个代表需要恢复
                if tab ~= nil and #tab == 1 then
                    local real_state = TaskWGData.Instance:GetTaskCameraType(tonumber(tab[1]))
                    if task_state == GameEnum.TASK_STATUS_NONE then
                        if task_is_completed then
                            self.camera_cache.reset_camera = true
                        end
                    else
                        if task_state == real_state then
                            self.camera_cache.reset_camera = true
                        end
                    end
                end
            end
        end
    end

    if self.camera_cache.reset_camera then
        self:RecoverCamera()
    end
end

function Scene:CheckTaskCameraByLogic()
    if self.camera_cache ~= nil and self.camera_cache.camera_type ~= nil and self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.TASK then
        local param_t = self.camera_cache.param_t
        if param_t == nil or type(param_t) ~= "table" then
            return
        end

        self:CheckTaskCamera({task_id = param_t.task_id})
        if self.camera_cache ~= nil and not self.camera_cache.reset_camera then
            local next_task = TaskWGData.Instance:GetNextZhuTaskConfigById(param_t.task_id)
            self:CheckTaskCamera({task_id = next_task.task_id})
        end
    end
end

function Scene:ClearCameraCache()
    self.camera_cache = nil
end

-- 抖动主摄像机(时间 强度(float/v3) 幅度 随机摇摆度)
function Scene:ShakeMainCamera(duration, strength, vibrato, randomness)
    if not IsNil(MainCamera) then
        MainCamera:DOShakePosition(duration, strength, vibrato, randomness)
    end
end

--[[调试打印用枚举
local ADJUST_CAMERA_TYPE_STR = {
    [ADJUST_CAMERA_TYPE.SCENE_POS] = "SCENE_POS",
    [ADJUST_CAMERA_TYPE.TASK] = "任务",
    [ADJUST_CAMERA_TYPE.NO_CAN_CHANGE] = "NO_CAN_CHANGE",
    [ADJUST_CAMERA_TYPE.FUNBEN] = "FUNBEN",
    [ADJUST_CAMERA_TYPE.XUNYOU] = "巡游",
    [ADJUST_CAMERA_TYPE.CLICK_FIX] = "玩家点击",
    [ADJUST_CAMERA_TYPE.AREA] = "区域",
    [ADJUST_CAMERA_TYPE.CAN_CHANGE] = "CAN_CHANGE",
    [ADJUST_CAMERA_TYPE.NORMAL] = "NORMAL",
    [ADJUST_CAMERA_TYPE.SKILL] = "技能",
    [ADJUST_CAMERA_TYPE.UP_MOUNT] = "坐骑",
}
--]]
-- ADJUST_CAMERA_TYPE.SCENE_POS 用于拍照功能
-- ADJUST_CAMERA_TYPE.TASK 用于任务触发的摄像机调整，玩家可以整，任务状态改变后恢复，优先级最高
-- ADJUST_CAMERA_TYPE.NO_CAN_CHANGE 用于切换场景时调整摄像机，玩家在场景内不可以调整，并且会切换到锁定，离开场景后恢复进入场景前的参数
-- ADJUST_CAMERA_TYPE.AREA 用于在某些区域时触发，离开区域后恢复
-- ADJUST_CAMERA_TYPE.CAN_CHANGE 用于切换场景时调整摄像机，玩家在场景内可以调整，离开场景后恢复进入场景前的参数
-- ADJUST_CAMERA_TYPE.NORMAL 用于平时直接设置摄像机，不需要恢复，优先级最低

-- @param camera_type 摄像机调整类型
-- @param angle_x X轴角度
-- @param angle_y Y轴角度  
-- @param dis 摄像机距离
-- @param param_t 参数表，支持的字段：
--   - is_only_cache: 仅做数据缓存而不改变摄像机状态，后续可通过RecoverCamera恢复
--   - is_immediately: 立即同步摄像机状态
--   - is_ro_immediately: 立即同步摄像机旋转
--   - scene_id: 场景ID
--   - is_need_cache_recover_data: 是否需要缓存恢复数据
-- 使用示例：
-- Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.TASK, 30, 45, 10, {is_only_cache = true})
function Scene:ChangeCamera(camera_type, angle_x, angle_y, dis, param_t)
    if IsNil(MainCameraFollow) then
        return
    end

    -- print_error("Scene.ChangeCamera", camera_type, ADJUST_CAMERA_TYPE_STR[camera_type], angle_x, angle_y, dis, self.camera_cache and self.camera_cache.camera_type)
    --if camera_type == ADJUST_CAMERA_TYPE.TASK then
    --    print_error("执行任务类型摄像机设定", angle_x, angle_y)
    --end
    if self.camera_cache ~= nil and self.camera_cache.camera_type ~= nil and self.camera_cache.camera_type < camera_type then
        -- print_error("---因为摄像机类型优先级拦截---", camera_type, self.camera_cache.camera_type)
        return
    end

    -- print_error("---ChangeCamera---", ADJUST_CAMERA_TYPE_STR[camera_type], camera_type, angle_x, angle_y, dis, param_t)
    local old_cache_info = cache_recover
    cache_recover = nil
    if old_cache_info ~= nil and old_cache_info.clear_time ~= nil and old_cache_info.clear_time < Status.NowTime then
        old_cache_info = nil
    end

    -- 区域的is_need_cache_recover_data默认值是0，默认不缓存
    local is_area_not_need_cache_recover_data = camera_type == ADJUST_CAMERA_TYPE.AREA and (param_t and param_t.is_need_cache_recover_data == 0)
    -- 副本的是后增加的条件 非0 都视为缓存
    local is_can_change_not_need_cache_recover_data = camera_type == ADJUST_CAMERA_TYPE.CAN_CHANGE and (param_t and param_t.is_need_cache_recover_data == 0)

    -- 缓存恢复数据
    if camera_type ~= ADJUST_CAMERA_TYPE.NORMAL
        and camera_type ~= ADJUST_CAMERA_TYPE.XUNYOU
        and camera_type ~= ADJUST_CAMERA_TYPE.CLICK_FIX
        and camera_type ~= ADJUST_CAMERA_TYPE.SCENE_POS
        and camera_type ~= ADJUST_CAMERA_TYPE.SKILL
        and camera_type ~= ADJUST_CAMERA_TYPE.UP_MOUNT
        and (not is_area_not_need_cache_recover_data)
        and (not is_can_change_not_need_cache_recover_data) then
        
        local old_origin_x = nil
        local old_origin_y = nil
        local old_camera_mode = CAMERA_TYPE
        local old_dis
        if self.camera_cache ~= nil then
            if self.camera_cache.param_t ~= nil and self.camera_cache.param_t.scene_id ~= nil then
                if param_t ~= nil and param_t.scene_id ~= nil and param_t.scene_id == self.camera_cache.param_t.scene_id then
                    old_origin_x = self.camera_cache.origin_x
                    old_origin_y = self.camera_cache.origin_y
                    old_camera_mode = self.camera_cache.camera_mode
                end
            elseif self.camera_cache.camera_type == ADJUST_CAMERA_TYPE.AREA and camera_type ~= ADJUST_CAMERA_TYPE.AREA then
                old_origin_x = self.camera_cache.origin_x
                old_origin_y = self.camera_cache.origin_y
                old_camera_mode = self.camera_cache.camera_mode
            end
        end

        self.camera_cache = {}
        self.camera_cache.camera_type = camera_type
        self.camera_cache.angle_x = angle_x
        self.camera_cache.angle_y = angle_y
        self.camera_cache.dis = old_dis or MainCameraFollow.Distance

        self.camera_cache.param_t = param_t
        self.camera_cache.reset_camera = false

        local now_x = MainCameraFollow.transform.localEulerAngles.x
        local now_y = MainCameraFollow.transform.localEulerAngles.y

        -- 处理在恢复过程中，又进入了摄像机设置逻辑
        if old_cache_info ~= nil and (camera_type == ADJUST_CAMERA_TYPE.TASK
        or camera_type == ADJUST_CAMERA_TYPE.NO_CAN_CHANGE
        or camera_type == ADJUST_CAMERA_TYPE.FUNBEN
        or camera_type == ADJUST_CAMERA_TYPE.CAN_CHANGE) then
            
            old_origin_x = old_cache_info.angle_x or old_origin_x
            old_origin_y = old_cache_info.angle_y or old_origin_y
            old_camera_mode = old_cache_info.camera_mode or old_camera_mode
            self.camera_cache.dis = old_cache_info.dis or MainCameraFollow.Distance
        end

        if camera_type == ADJUST_CAMERA_TYPE.AREA then
            if self.camera_cache ~= nil then
                if self.camera_cache.origin_x then
                    now_x = self.camera_cache.origin_x
                end

                if self.camera_cache.origin_y then
                    now_y = self.camera_cache.origin_y
                end
            end
        end

        self.camera_cache.origin_x = old_origin_x or now_x
        self.camera_cache.origin_y = old_origin_y or now_y

        self.camera_cache.camera_mode = old_camera_mode or CAMERA_TYPE

    elseif camera_type == ADJUST_CAMERA_TYPE.SKILL or camera_type == ADJUST_CAMERA_TYPE.UP_MOUNT then
        -- 只记录距离，不记录角度缓存
        if self.camera_cache == nil then
            self.camera_cache = {}
            self.camera_cache.camera_type = camera_type
            self.camera_cache.dis = MainCameraFollow.Distance
            self.camera_cache.camera_mode = CAMERA_TYPE
        end
    end

    if camera_type == ADJUST_CAMERA_TYPE.NO_CAN_CHANGE then
        if CAMERA_TYPE ~= CameraType.Fixed then
            MainuiWGCtrl.Instance:ChangeCameraMode()
        end
    end

    -- 如果只需要缓存数据而不改变摄像机状态，则在此处返回
    if param_t ~= nil and param_t.is_only_cache then
        return
    end

    if (angle_x ~= nil and angle_x ~= "") or (angle_y ~= nil and angle_y ~= "") then
        -- 不改变摄像机角度
        if camera_type == ADJUST_CAMERA_TYPE.SKILL or camera_type == ADJUST_CAMERA_TYPE.UP_MOUNT then
            -- 跳过角度设置
        else
            if angle_x ~= nil and angle_x ~= "" then
                angle_x = angle_x
            else
                angle_x = MainCameraFollow.transform.localEulerAngles.x
            end

            if angle_y ~= nil and angle_y ~= "" then
                angle_y = angle_y
            else
                angle_y = MainCameraFollow.transform.localEulerAngles.y
            end

            if camera_type == ADJUST_CAMERA_TYPE.AREA or camera_type == ADJUST_CAMERA_TYPE.TASK then
                self:FlushFightCamera(angle_x, angle_y, dis)
                return
            elseif camera_type == ADJUST_CAMERA_TYPE.SCENE_POS and angle_x and angle_y then
                -- SCENE_POS类型立即设置角度，不使用平滑过渡
                MainCameraFollow:ForceTransitionToAngle(Vector2(angle_x, angle_y), true, 0)
                MainCameraFollow:SyncImmediate()
            else
                self:SetCameraAngle(Vector2(angle_x, angle_y))
                if param_t ~= nil and param_t.is_immediately then
                    MainCameraFollow:SyncImmediate()
                end

                if param_t ~= nil and param_t.is_ro_immediately then
                    MainCameraFollow:SyncRotation()
                end
            end
        end
    end

    if dis ~= nil and dis ~= "" then
        self:SetCameraDis(dis)
    end
end

function Scene:RecoverCamera(is_scene_change, is_need_dis_tween)
    if self.camera_cache == nil then
        return
    end

    local camera_type = self.camera_cache.camera_type
    -- print_error("---RecoverCamera---", is_scene_change, camera_type, self.camera_cache)
    if is_scene_change and camera_type == ADJUST_CAMERA_TYPE.SKILL then
        return
    end

    local scene_id = self:GetSceneId()
    local is_reset = false
    local is_need = false
    local angle_x
    local angle_y
    local dis
    local is_smooth = false
    local need_change_mode = false
    local camera_mode

    if camera_type == ADJUST_CAMERA_TYPE.TASK then
        if self.camera_cache.param_t == nil or type(self.camera_cache.param_t) ~= "table" then
            is_reset = true
        else
            local param_t = self.camera_cache.param_t
            if self.camera_cache.reset_camera then
                if CAMERA_TYPE ~= CameraType.Free then
                    if math.abs(self.camera_cache.origin_x - MainCameraFollow.transform.localEulerAngles.x) > 0.5
                    or math.abs(self.camera_cache.dis - MainCameraFollow.Distance) > 0.5 then
                        is_need = true
                    end
                end

                is_reset = true
            end

            if not param_t.not_reset then
                is_reset = true
            end

            if is_need then
                angle_x = self.camera_cache.origin_x
                angle_y = self.camera_cache.origin_y
                dis = DefaultDistance
                is_smooth = true
                if self.camera_cache.camera_mode ~= nil and self.camera_cache.camera_mode ~= CAMERA_TYPE then
                    need_change_mode = true
                    camera_mode = self.camera_cache.camera_mode
                end
            end
        end
    elseif camera_type == ADJUST_CAMERA_TYPE.NO_CAN_CHANGE then
        if self.camera_cache.camera_mode ~= nil and self.camera_cache.camera_mode ~= CAMERA_TYPE then
            need_change_mode = true
            camera_mode = self.camera_cache.camera_mode
        end

        angle_x = self.camera_cache.origin_x
        angle_y = self.camera_cache.origin_y
        dis = self.camera_cache.dis
        is_reset = true

    elseif camera_type == ADJUST_CAMERA_TYPE.CAN_CHANGE or camera_type == ADJUST_CAMERA_TYPE.FUNBEN then
        if self.camera_cache.param_t == nil or type(self.camera_cache.param_t) ~= "table" then
            is_reset = true
        else
            if self.camera_cache.param_t.scene_id ~= nil then
                if self.camera_cache.param_t.scene_id ~= scene_id then
                    is_need = true

                    if self.camera_cache.param_t.is_recover == nil or self.camera_cache.param_t.is_recover == 1 then
                        angle_x = self.camera_cache.origin_x
                        angle_y = self.camera_cache.origin_y
                        dis = self.camera_cache.dis
                        if camera_type == ADJUST_CAMERA_TYPE.FUNBEN then
                            dis = DefaultDistance
                        end

                        if self.camera_cache.camera_mode ~= nil and self.camera_cache.camera_mode ~= CAMERA_TYPE then
                            need_change_mode = true
                            camera_mode = self.camera_cache.camera_mode
                        end
                    end
                end
            else
                is_reset = true
            end
        end

    elseif camera_type == ADJUST_CAMERA_TYPE.NORMAL then
        is_reset = true
    elseif camera_type == ADJUST_CAMERA_TYPE.AREA then
        if self.camera_cache.param_t == nil or type(self.camera_cache.param_t) ~= "table" then
            is_reset = true
        else
            local data = self.camera_cache.param_t
            local main_role = self:GetMainRole()
            if data.x ~= nil and data.y ~= nil and data.w ~= nil and data.h ~= nil and data.scene_id ~= nil then
                if main_role ~= nil then
                     local role_x, role_y = main_role:GetLogicPos()
                    if not GameMath.IsInRect(role_x, role_y, data.x, data.y, data.w, data.h) or scene_id ~= data.scene_id then
                        is_reset = true

                        if not data.not_reset then
                            if CAMERA_TYPE ~= CameraType.Free then
                                if math.abs(self.camera_cache.origin_x - MainCameraFollow.transform.localEulerAngles.x) > 0.5
                                or math.abs(self.camera_cache.dis - MainCameraFollow.Distance) > 0.5 then
                                    is_need = true
                                end
                            end
                        end

                        if scene_id ~= data.scene_id then
                            data.is_smooth = false
                        end
                    end
                end
            else
                is_reset = true
            end

            if is_need then
                angle_x = self.camera_cache.origin_x
                angle_y = self.camera_cache.origin_y
                dis = self.camera_cache.dis
                is_smooth = data.is_smooth or false

                if self.camera_cache.camera_mode ~= nil and self.camera_cache.camera_mode ~= CAMERA_TYPE then
                    need_change_mode = true
                    camera_mode = self.camera_cache.camera_mode
                end
            end
        end
    elseif camera_type == ADJUST_CAMERA_TYPE.SKILL or camera_type == ADJUST_CAMERA_TYPE.UP_MOUNT then
        if self.camera_cache.camera_mode ~= nil and self.camera_cache.camera_mode ~= CAMERA_TYPE then
            need_change_mode = true
            camera_mode = self.camera_cache.camera_mode
        end

        -- 只恢复距离，不恢复角度
        dis = self.camera_cache.dis
        is_reset = true
    end

    if is_reset or is_need then
        self:ClearCameraCache()
        -- 恢复摄像机将战斗恢复状态去除，以免触发退战逻辑
        self.real_camera_enter_battle = false
        self.camera_enter_battle = false
        if not IsNil(MainCameraFollow) then
            MainCameraFollow:ClearAllTransitionState()
        end
    end

    if need_change_mode then
        MainuiWGCtrl.Instance:ChangeCameraMode()
    end

    -- print_error("---RecoverCamera---", angle_x, angle_y, dis, camera_mode)
    if (angle_x ~= nil and angle_x ~= "") and (angle_y ~= nil and angle_y ~= "") then
        -- 不改变摄像机角度
        if camera_type == ADJUST_CAMERA_TYPE.SKILL or camera_type == ADJUST_CAMERA_TYPE.UP_MOUNT then
            -- 跳过角度恢复
        else
            if CAMERA_TYPE == CameraType.Fixed then
                angle_x = DefaultAnble.x
            end

            if is_smooth then
                local is_only_x = false
                if (camera_type == ADJUST_CAMERA_TYPE.AREA or camera_type == ADJUST_CAMERA_TYPE.TASK) and CAMERA_TYPE == CameraType.Fixed then
                    is_only_x = true
                end

                self:FlushFightCamera(angle_x, angle_y, dis, is_only_x)
            else
                self:SetCameraAngle(Vector2(angle_x, angle_y))

                if cache_recover == nil then
                    cache_recover = {}
                end

                cache_recover.angle_x = angle_x
                cache_recover.angle_y = angle_y
            end
        end
    end

    if dis ~= nil and dis ~= "" then
        if is_need_dis_tween then
            MainCameraFollow:DOCameraDistanceTweenTo(dis, 0.6)
        else
            self:SetCameraDis(dis)
        end

        if cache_recover == nil then
            cache_recover = {}
        end

        cache_recover.dis = dis
    end

    if camera_mode ~= nil then
        -- if CAMERA_TYPE ~= camera_mode then
        --     MainuiWGCtrl.Instance:ChangeCameraMode()
        -- end

        if cache_recover == nil then
            cache_recover = {}
        end

        cache_recover.camera_mode = camera_mode
    end

    if cache_recover ~= nil then
        cache_recover.clear_time = Status.NowTime + 1
    end
end

function Scene:SetCameraAngle(vector2)
    if not IsNil(MainCameraFollow) then
        -- print_error("---改变角度---", vector2)
        MainCameraFollow:ChangeAngle(vector2)
    end
end

function Scene:SetCameraDis(dis)
    if dis ~= nil and not IsNil(MainCameraFollow) then
        -- print_error("---改变距离---", dis)
        MainCameraFollow.Distance = dis
    end
end

function Scene:GetCameraDis()
    if  not IsNil(MainCameraFollow) then
        return MainCameraFollow.Distance
    end

    return 0
end






-- ==================================================================================
-- ================================= 新 =========================================
-- ==================================================================================
local main_role_key = -10086
-- obj创建
function Scene:OnObjectCreateToCamera(obj)
    self:CheckIsAddCameraTargetGroup(obj, true)
end

-- obj死亡
function Scene:OnObjectDeadToCamera(obj)
    self:CheckIsAddCameraTargetGroup(obj, false)
end

-- obj删除
function Scene:OnObjDeleteToCamera(obj)
    self:CheckIsAddCameraTargetGroup(obj, false)
end

-- 主角创建
function Scene:OnMainRoleCreateToCamera()
    self:AddMainRoleToTargetGroup()
end

-- 主角死亡
function Scene:OnMainRoleDeadToCamera()
end

-- 主角删除
function Scene:OnMainRoleDeteleToCamera()
    self:UpdateTargetGroup(main_role_key, false)
end

-- 主角重生
function Scene:OnMainRoleRealiveToCamera()
    local main_role = Scene.Instance:GetMainRole()
    if not main_role then
        return
    end

    local role_x, role_y = main_role:GetLogicPos()
    self:CheckTargetGroupObjDis(role_x, role_y)
end

-- 寻路状态变化
-- manual_operate 手动操作
function Scene:OnMainRoleAutoXunLuToCamera(status, manual_operate)
    if IsNil(MainCameraFollow) then
        return
    end

    local is_immediately_adjust = false
    local old_end_type = self.last_move_cache_end_type
    -- 寻路暂停且为非手操
    if not manual_operate and status == XunLuStatus.None then
        if old_end_type == MoveEndType.NpcTask or old_end_type == MoveEndType.ClickNpc
        or old_end_type == MoveEndType.Gather or old_end_type == MoveEndType.GatherById then
        -- or old_end_type == MoveEndType.PickAroundItem then
            is_immediately_adjust = true
        end
    end

    -- print_error("----尝试变化----", status, XunLuStatusStr[status], old_end_type, MoveCache.end_type, is_immediately_adjust)
    self.last_move_cache_end_type = MoveCache.end_type
    if self.camera_xunlu_state == status then
        return
    end

    self.camera_xunlu_state = status
    -- print_error("----寻路状态变化----", status, XunLuStatusStr[status])

    if status == XunLuStatus.XunLu or status == XunLuStatus.AutoWaBao then
        if self.camera_enter_battle then
            self:OnMainRoleExitFightToCamera(true)
        end

        MainCameraFollow:SetAutoPathfindingState(true)
    else
        MainCameraFollow:SetAutoPathfindingState(false)
        if is_immediately_adjust then
            MainCameraFollow:AdjustCameraToBehindTarget()
        end

        if status == XunLuStatus.AutoFight and not self.camera_enter_battle and self.real_camera_enter_battle then
            self:OnMainRoleEnterFightToCamera()
        end
    end
end

-- 进入战斗
function Scene:OnMainRoleEnterFightToCamera()
    if IsNil(MainCameraFollow) or (self.camera_enter_battle and self.real_camera_enter_battle) then
        return
    end

    self.real_camera_enter_battle = true
    self.camera_enter_battle = true

    local default_distance = 14
    -- 修为变身特殊处理
    local main_role = Scene.Instance:GetMainRole()
    if main_role and main_role:IsXiuWeiBianShen() then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type ~= SceneType.CROSS_AIR_WAR then
            default_distance = 9
        end
    end

    MainCameraFollow:SetEnterBattleState(true, default_distance)
end

-- 特殊处理(修为变身)
function Scene:UpdateMainRoleEnterFightCameraDis()
    if not self.camera_enter_battle then
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role then
        return
    end

    local change_distance = 14
    if main_role:IsXiuWeiBianShen() then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type ~= SceneType.CROSS_AIR_WAR then
            change_distance = 9
        end
    end

    if change_distance then
        MainCameraFollow:UpdateEnterBattleDistance(change_distance, true, 0.5)
    end
end

-- 退出战斗
-- is_interrupt 被打断
function Scene:OnMainRoleExitFightToCamera(is_interrupt)
    if IsNil(MainCameraFollow) or (self.camera_enter_battle == false and self.real_camera_enter_battle == false) then
        return
    end

    -- 一些场景不需镜头脱战
    local scene_cfg = self:GetCurFbSceneCfg()
    if scene_cfg and scene_cfg.is_battle_camera_reset == 0 then
        return
    end

    if not is_interrupt then
        self.real_camera_enter_battle = false
    end
    
    self.camera_enter_battle = false
    MainCameraFollow:SetEnterBattleState(false)
end

function Scene:OnSceneTypeChangeToCommon()
    if self.real_camera_enter_battle then
        self:OnMainRoleExitFightToCamera()
    end
end

-- 更新目标组的里的对象距离是否适合跟随
function Scene:CheckTargetGroupObjDis(role_x, role_y)
    for k,v in pairs(self.camera_target_group_list) do
        local obj = self:GetObjectByObjId(k)
        if obj ~= nil then
            local obj_x, obj_y = obj:GetLogicPos()
            if k ~= main_role_key then
                if (obj:IsDeleted()
                    or not AStarFindWay:IsInRange(role_x, role_y, obj_x, obj_y, TargetGroupMaxDistance)) then
                    self:UpdateTargetGroup(k, false)
                end
            end
        end
    end
end

-- GuaJiCache数据改变
function Scene:OnGuaJiCacheChangeToCamera(key, oldValue, value)
    if not value then return end

    if key == "target_obj_id" then
        if value ~= COMMON_CONSTS.INVALID_OBJID then
            local obj = self:GetObjectByObjId(value)
            self:CheckIsAddCameraTargetGroup(obj, true)
        end

    elseif key == "target_obj" then
        self:CheckIsAddCameraTargetGroup(value, true)
    -- elseif key == "monster_id" then
    --     local obj = self:GetMonstObjByMonstID(value)
    --     self:CheckIsAddCameraTargetGroup(obj, true)
    end
end

-- AtkCache数据改变
function Scene:OnAtkCacheChangeToCamera(key, oldValue, value)
    if not value then return end

    if key == "target_obj_id" then
        if value ~= COMMON_CONSTS.INVALID_OBJID then
            local obj = self:GetObjectByObjId(value)
            self:CheckIsAddCameraTargetGroup(obj, true)
        end

    elseif key == "target_obj" then
        self:CheckIsAddCameraTargetGroup(value, true)
    end
end

-- 主角位置变化
function Scene:OnMainRoleChangePosToCamera(x, y)
    if AtkCache.target_obj then
        self:CheckIsAddCameraTargetGroup(AtkCache.target_obj, true)
        return
    end

    if AtkCache.target_obj_id then
        local obj = self:GetObjectByObjId(AtkCache.target_obj_id)
        self:CheckIsAddCameraTargetGroup(obj, true)
        return
    end

    if GuajiCache.target_obj then
        self:CheckIsAddCameraTargetGroup(GuajiCache.target_obj, true)
        return
    end

    if GuajiCache.target_obj_id then
        local obj = self:GetObjectByObjId(GuajiCache.target_obj_id)
        self:CheckIsAddCameraTargetGroup(obj, true)
        return
    end

    if (MoveCache.end_type == MoveEndType.FightByMonsterId or MoveCache.end_type == MoveEndType.Fight)
    and GuajiCache.monster_id then
        local obj = self:GetMonstObjByMonstID(GuajiCache.monster_id)
        self:CheckIsAddCameraTargetGroup(obj, true)
        return
    end
end

-- obj更新目标组检查
function Scene:CheckIsAddCameraTargetGroup(obj, is_add)
    if not obj then
        return
    end

    local obj_type = obj:GetType()
    if obj_type == SceneObjType.MainRole then
        return
    end

    local is_boss = obj:IsBoss()
    if obj_type ~= SceneObjType.Monster or not is_boss then
        return
    end

    local obj_id = obj:GetObjId()
    if is_add then
        if obj:IsDeleted() then
            return
        end

        -- 已经有就不添加
        if self.camera_target_group_list[obj_id] then
            return
        end
    
        local main_role = Scene.Instance:GetMainRole()
        if not main_role then
            return
        end

        local role_x, role_y = main_role:GetLogicPos()
        local obj_x, obj_y = obj:GetLogicPos()
        local obj_monster_id = obj.vo.monster_id or 0

        -- local cfg = BossWGData.Instance:GetMonsterCfgByid(obj_monster_id)
        -- print_error("----cfg----", obj_id, obj_monster_id, cfg and cfg.name)
        -- 超过最大范围的不加
        if not AStarFindWay:IsInRange(role_x, role_y, obj_x, obj_y, TargetGroupMaxDistance) then
            return
        end

        if AtkCache.target_obj == obj or AtkCache.target_obj_id == obj_id then
            self:AddMonsterToTargetGroup(obj, is_boss)
            return
        end

        if GuajiCache.target_obj == obj or GuajiCache.target_obj_id == obj_id then
            self:AddMonsterToTargetGroup(obj, is_boss)
            return
        end

        if (MoveCache.end_type == MoveEndType.FightByMonsterId or MoveCache.end_type == MoveEndType.Fight)
        and obj_monster_id == GuajiCache.monster_id then
            self:AddMonsterToTargetGroup(obj, is_boss)
            return
        end
    else
        self:UpdateTargetGroup(obj_id, false)
    end
end

-- 添加主角到目标组
function Scene:AddMainRoleToTargetGroup()
    local main_role = self:GetMainRole()
    if not main_role then return end

    local draw_obj = main_role:GetDrawObj()
    if not draw_obj then return end

    local focal_point_trans = draw_obj:GetLookAtPointTrans()
    if not focal_point_trans then
        local x, y, z = main_role:GetLookAtPointPos()
        focal_point_trans = draw_obj:GetLookAtPoint(x, y, z, true)
    end

    self:UpdateTargetGroup(main_role_key, true, focal_point_trans, 2, 1)
end

-- 添加怪物到目标组
function Scene:AddMonsterToTargetGroup(obj, is_boss)
    if not obj then return end

    local obj_id = obj:GetObjId()
    local obj_monster_id = obj.vo.monster_id or 0

    local draw_obj = obj:GetDrawObj()
    if not draw_obj then return end

    local focal_point_trans = draw_obj:GetLookAtPointTrans()
    if not focal_point_trans then
        focal_point_trans = draw_obj:GetLookAtPoint(0, 6, 0, true)
    end

    self:UpdateTargetGroup(obj_id, true, focal_point_trans, 1, 1, is_boss)
end

-- 更新目标组
function Scene:UpdateTargetGroup(obj_id_key, is_add, focal_point_trans, weight, radius, is_boss)
    local is_group_data_change = false
    local had_old_data = self.camera_target_group_list[obj_id_key]
    if is_add then
        if not had_old_data then
            is_group_data_change = true
        elseif had_old_data.trans ~= focal_point_trans then
            self:UpdateObjToTargetGroup(had_old_data.trans, false)
            is_group_data_change = true
        elseif had_old_data.weight ~= weight or had_old_data.radius ~= radius then
            is_group_data_change = true
        end

        if is_group_data_change then
            self.camera_target_group_list[obj_id_key] = {trans = focal_point_trans, is_add = false, weight = weight, radius = radius, is_boss = is_boss}
        end
    else
        if had_old_data ~= nil then
            self:UpdateObjToTargetGroup(had_old_data.trans, false)
            is_group_data_change = true
        end

        self.camera_target_group_list[obj_id_key] = nil
    end

    if is_group_data_change then
        local cur_target_num = 0
        local boss_num = 0
        for k,v in pairs(self.camera_target_group_list) do
            cur_target_num = cur_target_num + 1
            if v.is_boss then
                boss_num = boss_num + 1
            end
        end

        local is_follow_target_group = cur_target_num > 1
        local is_open_follow_boss = boss_num > 0
        -- 更新数据
        if is_follow_target_group then
            for k,v in pairs(self.camera_target_group_list) do
                if not v.is_add then
                    self.camera_target_group_list[k].is_add = true
                    self:UpdateObjToTargetGroup(v.trans, true, v.weight, v.radius)
                end
            end
        end

        -- 【战斗镜头】开启
        if self.camera_is_follow_target_group ~= is_open_follow_boss then
            self.camera_is_follow_target_group = is_open_follow_boss
            if is_open_follow_boss then
                if not IsNil(MainCameraFollow) then
                    MainCameraFollow:EnableTargetGroupMode()
                end
            else
                if not IsNil(MainCameraFollow) then
                    MainCameraFollow:DisableTargetGroupMode()
                end
            end
        end
    end
end

function Scene:UpdateObjToTargetGroup(focal_point_trans, is_add, weight, radius)
    if IsNil(MainCameraFollow) or IsNil(focal_point_trans) then
        return
    end

    if is_add then
        weight = weight or 1
        radius = radius or 1
        MainCameraFollow:AddTarget(focal_point_trans, weight, radius)
    else
        MainCameraFollow:RemoveTarget(focal_point_trans)
    end
end
