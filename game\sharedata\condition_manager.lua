-- 通用条件管理
ConditionManager = ConditionManager or BaseClass()

local Condition = "Condition"
local language = nil
ConditionManager.Check_Type = {
	None = 0,
	Type1 = 1, -- and
	Type2 = 2, -- or
	Type101 = 101, -- 人物等级限制
	Type102 = 102, -- 转职等级限制
	Type103 = 103, -- VIP 等级限制
	Type104 = 104, -- 仙盟等级限制
	Type105 = 105, -- 世界等级限制
	Type106 = 106, -- 开服天数限制
	Type107 = 107, -- 季卡特权限制
	Type108 = 108, -- 至尊玉玺特权限制
	Type109 = 109, -- 九重劫塔层数限制
}
function ConditionManager:__init()
	if ConditionManager.Instance ~= nil then
		ErrorLog("[ConditionManager] attempt to create singleton twice!")
		return
	end
	ConditionManager.Instance = self
end

function ConditionManager:__delete()
	ConditionManager.Instance = nil

end

-- 检测数据 check_data
-- 是否需要返回提示 back_reason，这是Language的索引
function ConditionManager:CheckSingle(check_data, back_reason)
	local func = self[Condition..check_data.condition_type]
	if not func then
		print_error("This function does not exist:::", Condition .. check_data.condition_type)
		return
	end

	local flag, str = func(check_data, back_reason)
	return flag, str
end

-- 检测数据 check_data_list
-- 检测类型 check_type 1:and 2:or
-- 是否需要返回提示 back_reason，这是Language的索引
function ConditionManager:CheckList(check_data_list, check_type, back_reason)
	-- body
	local func = nil
	local back_list = {}
	local back_flag = true
	for k,v in pairs(check_data_list) do
		func = self[Condition..v.condition_type]
		if not func then
			print_error("This function does not exist:::", Condition..v.condition_type)
			return
		end

		local flag, str = func(v, back_reason)
		back_list[k] = {}
		back_list[k].flag = flag
		if check_type == ConditionManager.Check_Type.Type1 then
			if not flag then
				back_flag = false
				if back_reason then
					back_list[k].str = str
				end
				break
			end
		elseif check_type == ConditionManager.Check_Type.Type2 then
			if not flag then
				back_flag = false
				if back_reason then
					back_list[k].str = str
				end
			else
				back_flag = true
				break
			end
		end
	end

	if back_flag then
		return true
	end

	for k,v in pairs(back_list) do
		if check_type == ConditionManager.Check_Type.Type1 then
			if not v.flag then
				return v.flag, v.str
			end
		elseif check_type == ConditionManager.Check_Type.Type2 then
			if not v.flag then
				return v.flag, v.str
			end
		end		
	end
	
	return false, ""
end

-- 检测条件列表
function ConditionManager:CheckConditionList(condition_list)
	-- body
	local func = nil
	local back_list = {}
	for k,v in pairs(condition_list) do
		func = self[Condition..v.condition_type]
		if not func then
			print_error("This function does not exist:::", Condition .. v.condition_type)
			return
		end

		local flag, str = func(v)
		back_list[v.condition_type] = flag
	end

	return back_list
end

-- 玩家等级区间
function ConditionManager.Condition101(check_data, back_reason)
	-- body
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if check_data.param2 <= 0 then
		if check_data.param1 <= main_role_vo.level then
			return true
		end
	else
		if check_data.param1 <= main_role_vo.level and main_role_vo.level <= check_data.param2 then
			return true
		end
	end

	local str = nil
	if back_reason then
		language = Language["ConditionDes" .. back_reason]
		if language then
			local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(check_data.param1)
			if is_vis then
				str = string.format(language[check_data.condition_type], role_level)
				str = Language.Common.DianFeng .. str
			else	
				str = string.format(language[check_data.condition_type], check_data.param1) 
			end
		end
	end
	return false, str
end

-- 玩家转职区间
function ConditionManager.Condition102(check_data, back_reason)
	local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	if check_data.param2 <= 0 then
		if check_data.param1 <= role_zhuan then
			return true
		end
	else
		if check_data.param1 <= role_zhuan and role_zhuan <= check_data.param2 then
			return true
		end
	end

	local str = nil
	if back_reason then
		language = Language["ConditionDes" .. back_reason]
		if language then
			str = string.format(language[check_data.condition_type], check_data.param1) 
		end
	end
	return false, str
end

-- 玩家VIP区间
function ConditionManager.Condition103(check_data, back_reason)
	-- body
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if check_data.param2 <= 0 then
		if check_data.param1 <= main_role_vo.vip_level then
			return true
		end
	else
		if check_data.param1 <= main_role_vo.vip_level and main_role_vo.vip_level <= check_data.param2 then
			return true
		end
	end

	local str = nil
	if back_reason then
		language = Language["ConditionDes" .. back_reason]
		if language then
			str = string.format(language[check_data.condition_type], check_data.param1) 
		end
	end
	return false, str
end

-- 仙盟等级区间
function ConditionManager.Condition104(check_data, back_reason)
	-- body
	local level = GuildDataConst.GUILDVO.guild_level
	if check_data.param2 <= 0 then
		if check_data.param1 <= level then
			return true
		end
	else
		if check_data.param1 <= level and level <= check_data.param2 then
			return true
		end
	end

	local str = nil
	if back_reason then
		language = Language["ConditionDes" .. back_reason]
		if language then
			str = string.format(language[check_data.condition_type], check_data.param1) 
		end
	end
	return false, str
end

-- 世界等级区间
function ConditionManager.Condition105(check_data, back_reason)
	-- body
	local word_level = RankWGData.Instance:GetWordLevel()
	if check_data.param2 <= 0 then
		if check_data.param1 <= word_level then
			return true
		end
	else
		if check_data.param1 <= word_level and word_level <= check_data.param2 then
			return true
		end
	end

	local str = nil
	if back_reason then
		language = Language["ConditionDes" .. back_reason]
		if language then
			str = string.format(language[check_data.condition_type], check_data.param1) 
		end
	end
	return false, str
end

-- 开服天数区间
function ConditionManager.Condition106(check_data, back_reason)
	-- body
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if check_data.param2 <= 0 then
		if check_data.param1 <= open_day then
			return true
		end
	else
		if check_data.param1 <= open_day and open_day <= check_data.param2 then
			return true
		end
	end

	local str = nil
	if back_reason then
		language = Language["ConditionDes" .. back_reason]
		if language then
			str = string.format(language[check_data.condition_type], check_data.param1) 
		end
	end
	return false, str
end

-- 季卡特权区间
function ConditionManager.Condition107(check_data, back_reason)
	local active = RechargeWGData.Instance:IsHasWeekCard()
	local str = nil
	if active then
		return true
	else
		return true, Language.Shop.PrivilegeBuyDes1
	end
end

-- 至尊玉玺特权区间
function ConditionManager.Condition108(check_data, back_reason)
	local super_dragon_seal_data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
    local active = super_dragon_seal_data.is_active ~= 0
	local str = nil
	if active then
		return true
	else
		return true, Language.Shop.PrivilegeBuyDes2
	end
end

-- 九重劫塔区间
function ConditionManager.Condition109(check_data, back_reason)
	local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
	local need_level = check_data.param1

	if cur_level >= need_level then
		return true
	end

	return false, string.format(Language.Shop.FuBenWelkinLimitDes1, Language.FuBenPanel.RuleTitle[11], need_level)
end

-- 玩家是否加入仙盟
-- function ConditionManager.Condition2()
-- 	-- body
-- 	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
-- 	return 0 < main_role_vo.guild_id
-- end