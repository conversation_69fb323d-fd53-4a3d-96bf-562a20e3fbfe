﻿using UnityEngine;


public class RotateThis : MonoBehaviour
{
    public float rotationSpeedX = 90;
    public float rotationSpeedY = 0;
    public float rotationSpeedZ = 0;
    Vector3 rotationVec = Vector3.zero;
    void Start()
    {
        rotationVec = new Vector3(rotationSpeedX, rotationSpeedY, rotationSpeedZ);
    }


    void Update()
    {
        transform.Rotate(rotationVec * Time.deltaTime);
    }
}


