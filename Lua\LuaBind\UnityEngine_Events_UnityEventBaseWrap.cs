﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Events_UnityEventBaseWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Events.UnityEventBase), typeof(System.Object));
		<PERSON><PERSON>RegFunction("GetPersistentEventCount", GetPersistentEventCount);
		<PERSON><PERSON>unction("GetPersistentTarget", GetPersistentTarget);
		<PERSON><PERSON>unction("GetPersistentMethodName", GetPersistentMethodName);
		<PERSON><PERSON>RegFunction("SetPersistentListenerState", SetPersistentListenerState);
		<PERSON><PERSON>RegFunction("GetPersistentListenerState", GetPersistentListenerState);
		<PERSON><PERSON>RegFunction("RemoveAllListeners", RemoveAllListeners);
		<PERSON><PERSON>RegFunction("ToString", ToString);
		<PERSON><PERSON>RegFunction("GetValidMethodInfo", GetValidMethodInfo);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPersistentEventCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Events.UnityEventBase obj = (UnityEngine.Events.UnityEventBase)ToLua.CheckObject<UnityEngine.Events.UnityEventBase>(L, 1);
			int o = obj.GetPersistentEventCount();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPersistentTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Events.UnityEventBase obj = (UnityEngine.Events.UnityEventBase)ToLua.CheckObject<UnityEngine.Events.UnityEventBase>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Object o = obj.GetPersistentTarget(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPersistentMethodName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Events.UnityEventBase obj = (UnityEngine.Events.UnityEventBase)ToLua.CheckObject<UnityEngine.Events.UnityEventBase>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			string o = obj.GetPersistentMethodName(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPersistentListenerState(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Events.UnityEventBase obj = (UnityEngine.Events.UnityEventBase)ToLua.CheckObject<UnityEngine.Events.UnityEventBase>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Events.UnityEventCallState arg1 = (UnityEngine.Events.UnityEventCallState)ToLua.CheckObject(L, 3, typeof(UnityEngine.Events.UnityEventCallState));
			obj.SetPersistentListenerState(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPersistentListenerState(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Events.UnityEventBase obj = (UnityEngine.Events.UnityEventBase)ToLua.CheckObject<UnityEngine.Events.UnityEventBase>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Events.UnityEventCallState o = obj.GetPersistentListenerState(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveAllListeners(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Events.UnityEventBase obj = (UnityEngine.Events.UnityEventBase)ToLua.CheckObject<UnityEngine.Events.UnityEventBase>(L, 1);
			obj.RemoveAllListeners();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Events.UnityEventBase obj = (UnityEngine.Events.UnityEventBase)ToLua.CheckObject<UnityEngine.Events.UnityEventBase>(L, 1);
			string o = obj.ToString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetValidMethodInfo(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3 && TypeChecker.CheckTypes<System.Type, string, System.Type[]>(L, 1))
			{
				System.Type arg0 = (System.Type)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				System.Type[] arg2 = ToLua.ToObjectArray<System.Type>(L, 3);
				System.Reflection.MethodInfo o = UnityEngine.Events.UnityEventBase.GetValidMethodInfo(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<object, string, System.Type[]>(L, 1))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				System.Type[] arg2 = ToLua.ToObjectArray<System.Type>(L, 3);
				System.Reflection.MethodInfo o = UnityEngine.Events.UnityEventBase.GetValidMethodInfo(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Events.UnityEventBase.GetValidMethodInfo");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

