return {
	["other"]={
		{free_join_times=2,first_wave_s=10,team_life_tower_monster_id=31100,auto_item_count=5,auto_item_id=22000,auto_fb_empty_grid=2,life_tower_monster_id=1100,team_birth_pos_y=67,team_enter_sceneid=7004,team_birth_pos_x=104,},},
	["pass_level_config_default_table"]={},
	["wave_list"]={
		{monster_id_1=31101,wave=0,monster_id_0=31101,level=0,wave_level=32,monster_id_2=31101,},
		{monster_id_1=31102,monster_id_0=31102,level=0,wave_level=32,monster_id_2=31102,},
		{monster_id_1=31103,wave=2,monster_id_0=31103,level=0,wave_level=32,monster_id_2=31103,},
		{monster_id_1=31104,wave=3,monster_id_0=31104,level=0,wave_level=32,monster_id_2=31104,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31105,wave=4,monster_count_0=1,monster_id_0=31105,level=0,wave_level=32,monster_id_2=31105,},
		{monster_id_1=31106,wave=5,monster_id_0=31106,level=0,wave_level=32,monster_id_2=31106,},
		{monster_id_1=31107,wave=6,monster_id_0=31107,level=0,wave_level=32,monster_id_2=31107,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31108,wave=7,monster_count_0=2,monster_id_0=31108,level=0,wave_level=32,monster_id_2=31108,},
		{monster_id_1=31109,wave=8,monster_id_0=31109,level=0,wave_level=32,monster_id_2=31109,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31110,wave=9,monster_count_0=0,level=0,wave_level=32,},
		{monster_id_1=31111,wave=0,monster_id_0=31111,wave_level=40,monster_id_2=31111,},
		{monster_id_1=31112,monster_id_0=31112,wave_level=40,monster_id_2=31112,},
		{monster_id_1=31113,wave=2,monster_id_0=31113,wave_level=40,monster_id_2=31113,},
		{monster_id_1=31114,wave=3,monster_id_0=31114,wave_level=40,monster_id_2=31114,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31115,wave=4,monster_count_0=1,monster_id_0=31115,wave_level=40,monster_id_2=31115,},
		{monster_id_1=31116,wave=5,monster_id_0=31116,wave_level=40,monster_id_2=31116,},
		{monster_id_1=31117,wave=6,monster_id_0=31117,wave_level=40,monster_id_2=31117,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31118,wave=7,monster_count_0=2,monster_id_0=31118,wave_level=40,monster_id_2=31118,},
		{monster_id_1=31119,wave=8,monster_id_0=31119,wave_level=40,monster_id_2=31119,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31120,wave=9,monster_count_0=0,wave_level=40,},
		{monster_id_1=31121,wave=0,monster_id_0=31121,level=2,monster_id_2=31121,},
		{monster_id_1=31122,monster_id_0=31122,level=2,monster_id_2=31122,},
		{monster_id_1=31123,wave=2,monster_id_0=31123,level=2,monster_id_2=31123,},
		{monster_id_1=31124,wave=3,monster_id_0=31124,level=2,monster_id_2=31124,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31125,wave=4,monster_count_0=1,monster_id_0=31125,level=2,monster_id_2=31125,},
		{monster_id_1=31126,wave=5,monster_id_0=31126,level=2,monster_id_2=31126,},
		{monster_id_1=31127,wave=6,monster_id_0=31127,level=2,monster_id_2=31127,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31128,wave=7,monster_count_0=2,monster_id_0=31128,level=2,monster_id_2=31128,},
		{monster_id_1=31129,wave=8,monster_id_0=31129,level=2,monster_id_2=31129,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31130,wave=9,monster_count_0=0,level=2,},
		{monster_id_1=31131,wave=0,monster_id_0=31131,level=3,wave_level=60,monster_id_2=31131,},
		{monster_id_1=31132,monster_id_0=31132,level=3,wave_level=60,monster_id_2=31132,},
		{monster_id_1=31133,wave=2,monster_id_0=31133,level=3,wave_level=60,monster_id_2=31133,},
		{monster_id_1=31134,wave=3,monster_id_0=31134,level=3,wave_level=60,monster_id_2=31134,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31135,wave=4,monster_count_0=1,monster_id_0=31135,level=3,wave_level=60,monster_id_2=31135,},
		{monster_id_1=31136,wave=5,monster_id_0=31136,level=3,wave_level=60,monster_id_2=31136,},
		{monster_id_1=31137,wave=6,monster_id_0=31137,level=3,wave_level=60,monster_id_2=31137,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31138,wave=7,monster_count_0=2,monster_id_0=31138,level=3,wave_level=60,monster_id_2=31138,},
		{monster_id_1=31139,wave=8,monster_id_0=31139,level=3,wave_level=60,monster_id_2=31139,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31140,wave=9,monster_count_0=0,level=3,wave_level=60,},
		{monster_id_1=31141,wave=0,monster_id_0=31141,level=4,wave_level=70,monster_id_2=31141,},
		{monster_id_1=31142,monster_id_0=31142,level=4,wave_level=70,monster_id_2=31142,},
		{monster_id_1=31143,wave=2,monster_id_0=31143,level=4,wave_level=70,monster_id_2=31143,},
		{monster_id_1=31144,wave=3,monster_id_0=31144,level=4,wave_level=70,monster_id_2=31144,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31145,wave=4,monster_count_0=1,monster_id_0=31145,level=4,wave_level=70,monster_id_2=31145,},
		{monster_id_1=31146,wave=5,monster_id_0=31146,level=4,wave_level=70,monster_id_2=31146,},
		{monster_id_1=31147,wave=6,monster_id_0=31147,level=4,wave_level=70,monster_id_2=31147,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31148,wave=7,monster_count_0=2,monster_id_0=31148,level=4,wave_level=70,monster_id_2=31148,},
		{monster_id_1=31149,wave=8,monster_id_0=31149,level=4,wave_level=70,monster_id_2=31149,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31150,wave=9,monster_count_0=0,level=4,wave_level=70,},
		{monster_id_1=31151,wave=0,monster_id_0=31151,level=5,wave_level=80,monster_id_2=31151,},
		{monster_id_1=31152,monster_id_0=31152,level=5,wave_level=80,monster_id_2=31152,},
		{monster_id_1=31153,wave=2,monster_id_0=31153,level=5,wave_level=80,monster_id_2=31153,},
		{monster_id_1=31154,wave=3,monster_id_0=31154,level=5,wave_level=80,monster_id_2=31154,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31155,wave=4,monster_count_0=1,monster_id_0=31155,level=5,wave_level=80,monster_id_2=31155,},
		{monster_id_1=31156,wave=5,monster_id_0=31156,level=5,wave_level=80,monster_id_2=31156,},
		{monster_id_1=31157,wave=6,monster_id_0=31157,level=5,wave_level=80,monster_id_2=31157,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31158,wave=7,monster_count_0=2,monster_id_0=31158,level=5,wave_level=80,monster_id_2=31158,},
		{monster_id_1=31159,wave=8,monster_id_0=31159,level=5,wave_level=80,monster_id_2=31159,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31160,wave=9,monster_count_0=0,level=5,wave_level=80,},
		{wave=0,monster_id_0=31161,level=6,wave_level=90,monster_id_2=31161,},
		{monster_id_1=31162,monster_id_0=31162,level=6,wave_level=90,monster_id_2=31162,},
		{monster_id_1=31163,wave=2,monster_id_0=31163,level=6,wave_level=90,monster_id_2=31163,},
		{monster_id_1=31164,wave=3,monster_id_0=31164,level=6,wave_level=90,monster_id_2=31164,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31165,wave=4,monster_count_0=1,monster_id_0=31165,level=6,wave_level=90,monster_id_2=31165,},
		{monster_id_1=31166,wave=5,monster_id_0=31166,level=6,wave_level=90,monster_id_2=31166,},
		{monster_id_1=31167,wave=6,monster_id_0=31167,level=6,wave_level=90,monster_id_2=31167,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31168,wave=7,monster_count_0=2,monster_id_0=31168,level=6,wave_level=90,monster_id_2=31168,},
		{monster_id_1=31169,wave=8,monster_id_0=31169,level=6,wave_level=90,monster_id_2=31169,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31170,wave=9,monster_count_0=0,level=6,wave_level=90,},
		{wave=0,monster_id_0=31161,level=7,wave_level=95,monster_id_2=31161,},
		{monster_id_1=31162,monster_id_0=31162,level=7,wave_level=95,monster_id_2=31162,},
		{monster_id_1=31163,wave=2,monster_id_0=31163,level=7,wave_level=95,monster_id_2=31163,},
		{monster_id_1=31164,wave=3,monster_id_0=31164,level=7,wave_level=95,monster_id_2=31164,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31165,wave=4,monster_count_0=1,monster_id_0=31165,level=7,wave_level=95,monster_id_2=31165,},
		{monster_id_1=31166,wave=5,monster_id_0=31166,level=7,wave_level=95,monster_id_2=31166,},
		{monster_id_1=31167,wave=6,monster_id_0=31167,level=7,wave_level=95,monster_id_2=31167,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31168,wave=7,monster_count_0=2,monster_id_0=31168,level=7,wave_level=95,monster_id_2=31168,},
		{monster_id_1=31169,wave=8,monster_id_0=31169,level=7,wave_level=95,monster_id_2=31169,},
		{monster_count_1=1,monster_count_2=0,next_wave_s=99999,monster_id_1=31170,wave=9,monster_count_0=0,level=7,wave_level=95,},},
	["skill_cfg"]={
		{param_c=1,attr_type=0,cd_s=3,skill_index=0,distance=16,param_a=100,perform_type=0,hurt_percent=0,param_b=5,},
		{param_c=1,attr_type=0,cd_s=3,distance=16,perform_type=0,hurt_percent=0,param_b=1,},
		{cd_s=1,skill_id=4,skill_index=2,distance=1,param_a=100,perform_type=0,},
		{cd_s=5,skill_id=5,skill_index=3,},
		{skill_id=6,skill_index=4,param_a=400,},
		{param_c=5,cd_s=15,skill_id=7,skill_index=5,param_a=120,},
		{attr_type=2,cd_s=1,skill_index=6,distance=1,param_a=200,perform_type=0,param_b=200,},
		{param_c=2,attr_type=2,cd_s=2,skill_id=2,skill_index=7,distance=18,param_a=0,perform_type=2,hurt_percent=50,},
		{param_c=5,attr_type=2,skill_id=3,skill_index=8,param_a=10,},
		{attr_type=2,cd_s=180,skill_id=1,skill_index=9,distance=1,param_a=5,perform_type=2,},
		{attr_type=3,cd_s=15,skill_id=8,skill_index=10,},
		{param_c=200,attr_type=3,skill_id=9,skill_index=11,distance=30,param_a=0,perform_type=2,},
		{param_c=10,attr_type=3,cd_s=180,skill_id=10,skill_index=12,distance=30,param_a=100,perform_type=2,},
		{attr_type=3,cd_s=15,skill_id=11,skill_index=13,param_a=5,},},
	["path_list"]={
		{pos_x_1=35,pos_y_2=45,pos_x_2=39,path_idx=0,path_type=0,},
		{pos_x_1=83,pos_y_2=46,pos_x_2=59,path_type=0,},
		{pos_x_1=91,pos_y_2=32,pos_y_1=26,pos_x_2=61,path_idx=2,path_type=0,},
		{pos_x_3=111,pos_y_2=79,pos_y_1=88,path_idx=0,pos_x_4=111,pos_y_3=66,pos_y_4=66,},
		{pos_x_3=111,pos_x_1=47,pos_y_1=40,pos_x_4=111,pos_y_3=66,pos_y_4=66,},
		{pos_x_3=111,pos_x_1=157,pos_y_2=42,pos_y_1=23,pos_x_2=143,path_idx=2,pos_x_4=111,pos_y_3=66,pos_y_4=66,},},
	["skill_cfg_default_table"]={param_c=0,attr_type=1,distance=14,cd_s=10,skill_id=0,skill_index=1,fix_hurt=0,param_a=300,perform_type=1,hurt_percent=100,param_b=0,energy=50,cd_factor=0,},
	["path_list_default_table"]={pos_x_3=36,pos_x_1=58,pos_y_2=50,pos_y_1=67,pos_x_2=73,path_idx=1,pos_x_4=34,pos_y_3=28,path_type=1,pos_y_4=25,},
	["buy_cost"]={
		{},
		{buy_times=2,},
		{buy_times=3,},
		{buy_times=4,},
		{buy_times=5,},},
	["wave_list_default_table"]={monster_count_1=4,monster_count_2=3,next_wave_s=30,monster_id_1=31161,wave=1,monster_count_0=3,monster_id_0=0,level=1,wave_level=50,monster_id_2=0,},
	["level_scene_cfg_default_table"]={birth_pos_y=31,scene_id=3001,fb_name="诸神黄昏",birth_pos_x=41,level=1,need_level=50,show_reward=26026,},
	["buy_cost_default_table"]={buy_times=1,gold_cost=50,},
	["skill_list_default_table"]={attr_type=1,tipType="",skill_index=1,releaseEffectPath="",readEffectPath="",icon=1913,hurt_percent=0,lauchType=5,hitType=0,is_extra=0,duration="",distance=16,cd_s=20,titleRes="",skillType=3,skill_name="风咒",fix_hurt=0,hitEffectPath="",desc="治疗技能，治疗血量随攻击力和技能等级变化<br/><font color='#1eff01'>升级技能可提升技能治疗量</font>",targetType=0,processEffectPath="",cd_factor=0,},
	["skill_list"]={
		{cd_s=2,icon=1901,skill_name="火神之力",lauchType=0,distance=10,desc="朱雀被动技能，提高自身攻击力<br/><font color='#1eff01'>升级技能可以提高加成百分比</font>",skillType=5,},
		{cd_s=1,skill_index=2,icon=1902,skill_name="烈炎术",lauchType=1,desc="朱雀单体技能，对目标造成大量伤害<br/><font color='#1eff01'>升级技能可以提高伤害</font>",skillType=1,hurt_percent=120,hitEffectPath=10500,hitType=1,processEffectPath=10500,},
		{cd_s=6,skill_index=3,releaseEffectPath=10501,icon=1903,skill_name="天火术",lauchType=2,desc="朱雀群体攻击，对多个目标造成伤害<br/><font color='#1eff01'>升级技能可以提高伤害</font>",hurt_percent=50,hitEffectPath=10501,hitType=2,},
		{cd_s=15,skill_index=4,icon=1904,skill_name="炽热红莲",lauchType=2,desc="群体攻击，在地面释放一个固定伤害的持续阵法 <font color='#1eff01'>升级技能可以提高伤害</font>",hitType=2,},
		{attr_type=2,cd_s=2,skill_index=5,icon=1905,skill_name="水神之盾",lauchType=0,distance=10,desc="被动技能，提高自身防御力和仇恨<br/><font color='#1eff01'>升级技能可提升技能增加防御和仇恨</font>",skillType=5,},
		{attr_type=2,cd_s=2,skill_index=6,icon=1906,skill_name="水神之血",lauchType=0,distance=10,desc="被动技能，提高自身血量上限<br/><font color='#1eff01'>升级技能可提升技能增加血量</font>",skillType=5,},
		{attr_type=2,skill_index=7,releaseEffectPath=10502,icon=1907,skill_name="冰封咆哮",desc="群体攻击，对自身周围目标造成伤害并嘲讽，并且减少受到伤害<font color='#1eff01'>升级技能可提升伤害减免</font>",hurt_percent=50,hitType=2,},
		{attr_type=2,cd_s=1,skill_index=8,releaseEffectPath=10503,icon=1908,skill_name="寒冰杀",desc="群体攻击，对多个目标造成伤害并降低其攻<br/>击力  <font color='#1eff01'>升级技能可降低敌方更多攻击力</font>",hurt_percent=50,hitEffectPath=10503,hitType=2,},
		{attr_type=3,cd_s=1,skill_index=9,icon=1909,lauchType=1,desc="单体攻击，附加固定伤害<br/><font color='#1eff01'>升级技能可以提高伤害</font>",skillType=1,hurt_percent=100,hitEffectPath=10504,hitType=1,processEffectPath=10504,},
		{attr_type=3,cd_s=15,skill_index=10,icon=1910,skill_name="风神治疗",targetType=1,skillType=4,hitEffectPath=10505,},
		{attr_type=3,skill_index=11,icon=1911,skill_name="风神祝福",targetType=1,desc="辅助技能，提高目标的攻防值<br/><font color='#1eff01'>升级技能可提升技能攻防效果</font>",skillType=4,hitEffectPath=10506,},
		{attr_type=3,cd_s=30,skill_index=12,releaseEffectPath=10507,icon=1912,skill_name="风之禁锢",lauchType=2,desc="群体攻击，对多个目标造成伤害并定身5秒<br/><font color='#1eff01'>升级技能可降低技能冷却</font>",hurt_percent=50,hitEffectPath=10507,hitType=2,cd_factor=1,},
		{cd_s=60,skill_index=13,releaseEffectPath=10501,is_extra=1,skill_name="雷霆之怒",lauchType=2,desc="群体攻击，对多个目标造成大量伤害<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",hurt_percent=300,fix_hurt=2000,hitEffectPath=10501,hitType=2,},
		{skill_index=14,releaseEffectPath=10501,is_extra=1,icon=1914,skill_name="燃烧",lauchType=2,desc="群体攻击，对多个目标造成伤害并附带燃烧效果<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",hurt_percent=40,hitEffectPath=10501,hitType=2,},
		{attr_type=2,cd_s=180,skill_index=15,is_extra=1,icon=1915,skill_name="神佑复生",lauchType=0,distance=10,desc="被动技能，死亡后立即复活，血量剩余50%<br/>触发冷却180秒<font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=5,},
		{attr_type=2,cd_s=60,skill_index=16,is_extra=1,icon=1916,skill_name="绝对防御",targetType=1,distance=10,desc="辅助技能，使用后自身获得5秒绝对防御<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=4,hitEffectPath=10503,},
		{attr_type=3,skill_index=17,is_extra=1,icon=1917,skill_name="回春术",targetType=1,desc="辅助技能，给队友附加持续治疗状态<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=4,hitEffectPath=10505,},
		{attr_type=3,cd_s=40,skill_index=18,releaseEffectPath=10507,is_extra=1,icon=1918,skill_name="群体诅咒",lauchType=2,desc="群体攻击，使多个目标降低10%攻防持续10秒<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",hurt_percent=50,hitEffectPath=10507,hitType=2,},
		{attr_type=0,cd_s=60,skill_index=19,is_extra=1,icon=1919,skill_name="雷霆震慑",lauchType=1,desc="单体攻击，使目标眩晕5秒<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=2,hurt_percent=100,hitEffectPath=10504,hitType=1,processEffectPath=10504,},
		{attr_type=0,cd_s=30,skill_index=20,is_extra=1,icon=1920,skill_name="熔岩护盾",targetType=1,desc="辅助技能，给队友或自己附加伤害反弹状态<br/><font color='#1eff01'>删除后下次拾取技能不会获得该技能</font>",skillType=4,hitEffectPath=10505,},},
	["pass_level_config"]={
		{},
		{},
		{},
		{},
		{},},
	["team_wave_list"]={
		{wave=0,monster_id_0=31101,wave_level=32,monster_id_2=31101,},
		{monster_id_1=31102,monster_id_0=31102,wave_level=32,monster_id_2=31102,},
		{monster_id_1=31103,wave=2,monster_id_0=31103,wave_level=32,monster_id_2=31103,},
		{monster_id_1=31104,wave=3,monster_id_0=31104,wave_level=32,monster_id_2=31104,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31105,wave=4,monster_count_0=1,monster_id_0=31105,wave_level=32,monster_id_2=31105,},
		{monster_id_1=31106,wave=5,monster_id_0=31106,wave_level=32,monster_id_2=31106,},
		{monster_id_1=31107,wave=6,monster_id_0=31107,wave_level=32,monster_id_2=31107,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31108,wave=7,monster_count_0=2,monster_id_0=31108,wave_level=32,monster_id_2=31108,},
		{monster_id_1=31109,wave=8,monster_id_0=31109,wave_level=32,monster_id_2=31109,},
		{monster_count_1=1,monster_count_2=0,monster_id_1=31110,wave=9,monster_count_0=0,wave_level=32,},
		{monster_id_1=31111,wave=10,monster_id_0=31111,monster_id_2=31111,},
		{monster_id_1=31112,wave=11,monster_id_0=31112,monster_id_2=31112,},
		{monster_id_1=31113,wave=12,monster_id_0=31113,monster_id_2=31113,},
		{monster_id_1=31114,wave=13,monster_id_0=31114,monster_id_2=31114,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31115,wave=14,monster_count_0=1,monster_id_0=31115,monster_id_2=31115,},
		{monster_id_1=31116,wave=15,monster_id_0=31116,monster_id_2=31116,},
		{monster_id_1=31117,wave=16,monster_id_0=31117,monster_id_2=31117,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31118,wave=17,monster_count_0=2,monster_id_0=31118,monster_id_2=31118,},
		{monster_id_1=31119,wave=18,monster_id_0=31119,monster_id_2=31119,},
		{monster_count_1=1,monster_count_2=0,monster_id_1=31120,wave=19,monster_count_0=0,},
		{monster_id_1=31121,wave=20,monster_id_0=31121,monster_id_2=31121,},
		{monster_id_1=31122,wave=21,monster_id_0=31122,monster_id_2=31122,},
		{monster_id_1=31123,wave=22,monster_id_0=31123,monster_id_2=31123,},
		{monster_id_1=31124,wave=23,monster_id_0=31124,monster_id_2=31124,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31125,wave=24,monster_count_0=1,monster_id_0=31125,monster_id_2=31125,},
		{monster_id_1=31126,wave=25,monster_id_0=31126,monster_id_2=31126,},
		{monster_id_1=31127,wave=26,monster_id_0=31127,monster_id_2=31127,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31128,wave=27,monster_count_0=2,monster_id_0=31128,monster_id_2=31128,},
		{monster_id_1=31129,wave=28,monster_id_0=31129,monster_id_2=31129,},
		{monster_count_1=1,monster_count_2=0,monster_id_1=31130,wave=29,monster_count_0=0,},
		{monster_id_1=31131,wave=30,monster_id_0=31131,monster_id_2=31131,},
		{monster_id_1=31132,wave=31,monster_id_0=31132,monster_id_2=31132,},
		{monster_id_1=31133,wave=32,monster_id_0=31133,monster_id_2=31133,},
		{monster_id_1=31134,wave=33,monster_id_0=31134,monster_id_2=31134,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31135,wave=34,monster_count_0=1,monster_id_0=31135,monster_id_2=31135,},
		{monster_id_1=31136,wave=35,monster_id_0=31136,monster_id_2=31136,},
		{monster_id_1=31137,wave=36,monster_id_0=31137,monster_id_2=31137,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31138,wave=37,monster_count_0=2,monster_id_0=31138,monster_id_2=31138,},
		{monster_id_1=31139,wave=38,monster_id_0=31139,monster_id_2=31139,},
		{monster_count_1=1,monster_count_2=0,monster_id_1=31140,wave=39,monster_count_0=0,},
		{monster_id_1=31141,wave=40,monster_id_0=31141,monster_id_2=31141,},
		{monster_id_1=31142,wave=41,monster_id_0=31142,monster_id_2=31142,},
		{monster_id_1=31143,wave=42,monster_id_0=31143,monster_id_2=31143,},
		{monster_id_1=31144,wave=43,monster_id_0=31144,monster_id_2=31144,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31145,wave=44,monster_count_0=1,monster_id_0=31145,monster_id_2=31145,},
		{monster_id_1=31146,wave=45,monster_id_0=31146,monster_id_2=31146,},
		{monster_id_1=31147,wave=46,monster_id_0=31147,monster_id_2=31147,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31148,wave=47,monster_count_0=2,monster_id_0=31148,monster_id_2=31148,},
		{monster_id_1=31149,wave=48,monster_id_0=31149,monster_id_2=31149,},
		{monster_count_1=1,monster_count_2=0,monster_id_1=31150,wave=49,monster_count_0=0,},
		{monster_id_1=31151,wave=50,monster_id_0=31151,monster_id_2=31151,},
		{monster_id_1=31152,wave=51,monster_id_0=31152,monster_id_2=31152,},
		{monster_id_1=31153,wave=52,monster_id_0=31153,monster_id_2=31153,},
		{monster_id_1=31154,wave=53,monster_id_0=31154,monster_id_2=31154,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31155,wave=54,monster_count_0=1,monster_id_0=31155,monster_id_2=31155,},
		{monster_id_1=31156,wave=55,monster_id_0=31156,monster_id_2=31156,},
		{monster_id_1=31157,wave=56,monster_id_0=31157,monster_id_2=31157,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31158,wave=57,monster_count_0=2,monster_id_0=31158,monster_id_2=31158,},
		{monster_id_1=31159,wave=58,monster_id_0=31159,monster_id_2=31159,},
		{monster_count_1=1,monster_count_2=0,monster_id_1=31160,wave=59,monster_count_0=0,},
		{monster_id_1=31161,wave=60,monster_id_0=31161,monster_id_2=31161,},
		{monster_id_1=31162,wave=61,monster_id_0=31162,monster_id_2=31162,},
		{monster_id_1=31163,wave=62,monster_id_0=31163,monster_id_2=31163,},
		{monster_id_1=31164,wave=63,monster_id_0=31164,monster_id_2=31164,},
		{monster_count_1=1,monster_count_2=1,monster_id_1=31165,wave=64,monster_count_0=1,monster_id_0=31165,monster_id_2=31165,},
		{monster_id_1=31166,wave=65,monster_id_0=31166,monster_id_2=31166,},
		{monster_id_1=31167,wave=66,monster_id_0=31167,monster_id_2=31167,},
		{monster_count_1=1,monster_count_2=2,monster_id_1=31168,wave=67,monster_count_0=2,monster_id_0=31168,monster_id_2=31168,},
		{monster_id_1=31169,wave=68,monster_id_0=31169,monster_id_2=31169,},
		{monster_count_1=1,monster_count_2=0,monster_id_1=31170,wave=69,monster_count_0=0,},},
	["level_scene_cfg"]={
		{fb_name="神魔秘境",level=0,need_level=32,show_reward=26020,},
		{scene_id=3002,fb_name="神魔之井",need_level=40,show_reward=26021,},
		{scene_id=3003,fb_name="妖兽巢穴",level=2,show_reward=26022,},
		{scene_id=3004,fb_name="兽潮来袭",level=3,need_level=60,show_reward=26023,},
		{scene_id=3005,fb_name="魔神入侵",level=4,need_level=70,show_reward=26024,},
		{scene_id=3006,fb_name="末日灾变",level=5,need_level=80,show_reward=26025,},
		{scene_id=3007,fb_name="仙域守护",level=6,need_level=90,},
		{scene_id=3008,level=7,need_level=95,show_reward=26027,},},
	["team_wave_list_default_table"]={monster_count_1=4,monster_count_2=3,next_wave_s=60,desc="",monster_id_1=31101,wave=1,monster_count_0=3,monster_id_0=0,wave_level="",monster_id_2=0,kill_score=1,},
}
