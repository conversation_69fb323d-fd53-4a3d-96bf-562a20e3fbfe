--动态运营活动 连续充值
function OperationActivityView:InitContinue<PERSON><PERSON><PERSON>hi()
    self.ctn_day_button_list = {}
    self.ctn_active_button_list = {} --显示的天数按钮标记列表
    self.ctn_active_card_list = {}
    self.ctn_card_cell_list = {}
end

function OperationActivityView:ReleaseContinueChongZhi()
    if self.ctn_day_recharge_list then
        self.ctn_day_recharge_list:DeleteMe()
        self.ctn_day_recharge_list = nil
    end

    for i, v in ipairs(self.ctn_card_cell_list) do
        v:DeleteMe()
    end
    self.ctn_card_cell_list = {}
    self.ctn_data_list = {}

    for i, v in ipairs(self.ctn_day_button_list) do
        v:DeleteMe()
    end
    self.ctn_day_button_list = {}
    self.ctn_active_button_list = {}
    self.ctn_active_card_list = {}
end

function OperationActivityView:LoadIndexCallBackContinue<PERSON><PERSON><PERSON>hi()
    local button_data_list = OperationCtnRechargeWGData.Instance:GetDayButtonDataList()
    --天数按钮
    for i = 1, 7 do
        if button_data_list[i] then
            self.ctn_day_button_list[i] = OperationCtnRechargeDayButtonRender.New(self.node_list["ctn_toggle" .. i])
            self.ctn_day_button_list[i]:SetActive(true)
            self.ctn_day_button_list[i]:SetParentView(self)
            self.ctn_day_button_list[i]:SetMyIndex(i)
            self.ctn_day_button_list[i]:SetClickEvent(BindTool.Bind(self.CtnOnClickDayButton, self))
            self.ctn_day_button_list[i]:SetData(button_data_list[i])
            self.ctn_active_button_list[i] = true
        else
            self.node_list["ctn_toggle" .. i]:SetActive(false)
        end
    end

    self.ctn_data_list = OperationCtnRechargeWGData.Instance:GetTotalRehargeCfgList()

    for i = 1, 3 do
        if self.ctn_data_list[i] then
            self.ctn_card_cell_list[i] = OperationContinueChongZhiPageRender.New(self.node_list["ctn_total_recharge_item" .. i])
            self.ctn_card_cell_list[i]:SetActive(true)
            self.ctn_card_cell_list[i]:SetData(self.ctn_data_list[i])
            self.ctn_active_card_list[i] = true
        else
            self.node_list["ctn_total_recharge_item" .. i]:SetActive(false)
        end
    end

    --下滑列表
    self.ctn_day_recharge_list = AsyncListView.New(OperationContinueChongZhiRewardRender, self.node_list.ctn_day_recharge_list)
    self.ctn_day_recharge_list:SetCreateCellCallBack(BindTool.Bind(self.OnCreateCellContinueChongZhi, self))

    local param_cfg = OperationCtnRechargeWGData.Instance:GetParamCfgByServerOpenDay()
    if not IsEmptyTable(param_cfg) and param_cfg.grade then
        local bundle, asset = ResPath.GetOperationActCtnRecharge(param_cfg.slogan)
        self.node_list.ctn_tip_title.image:LoadSprite(bundle, asset, function()
            XUI.ImageSetNativeSize(self.node_list.ctn_tip_title)
        end)

        if param_cfg.ctn_big_bg then
            local bundle, asset = ResPath.GetF2RawImagesPNG(param_cfg.ctn_big_bg)
            self.node_list.ctn_big_bg.raw_image:LoadSprite(bundle, asset, function()
                self.node_list.ctn_big_bg.raw_image:SetNativeSize()
            end)
        end
    end
end

function OperationActivityView:OnCreateCellContinueChongZhi(cell)
    cell:SetParentScrollRect(self.node_list.ctn_day_recharge_list.scroll_rect)
end

function OperationActivityView:ShowIndexCallBackContinueChongZhi()
    self:RefreshDayIndex()
    self.ctn_data_list = OperationCtnRechargeWGData.Instance:GetTotalRehargeCfgList()
end

function OperationActivityView:OnFlushContinueChongZhi(param_t)
    local data_list = {}
    local is_set_rule = false
    local param_cfg = OperationCtnRechargeWGData.Instance:GetParamCfgByServerOpenDay()
    if IsEmptyTable(param_cfg) or not param_cfg.grade then
        data_list = {}
        --没读到配置就读language
		self:SetRuleInfo(Language.OpertionAcitvity.CtnRuleContent, Language.OpertionAcitvity.CtnRuleTitle)
        self:SetOutsideRuleTips(Language.OpertionAcitvity.CtnRuleOutSideDefaultContent)
        is_set_rule = true
    else
        data_list = OperationCtnRechargeWGData.Instance:GetDayRehargeCfgList(self.ctn_select_day_index, param_cfg.grade)
    end
    self.ctn_day_recharge_list:SetDataList(data_list)

    self:FlushCtnCards()
    self:FlushCtnDayButtons()

    if not is_set_rule then
        self:SetRuleInfo(param_cfg.tips_within, param_cfg.tips_title)
        self:SetOutsideRuleTips(param_cfg.tips_external)
    end
end

function OperationActivityView:CtnOnClickDayButton(item, index)
    self.ctn_select_day_index = index
	--self:FlushCtnDayButtons()
    self:Flush(TabIndex.operation_act_ctn_recharge)
end

--刷新天数按钮
function OperationActivityView:FlushCtnDayButtons()
    for i,v in ipairs(self.ctn_day_button_list) do
        if self.ctn_active_button_list[i] then
            v:Flush()
        end
    end
end

--刷新卡片
function OperationActivityView:FlushCtnCards()
    for i, v in ipairs(self.ctn_card_cell_list) do
         if self.ctn_active_card_list[i] then
            v:Flush()
        end
    end
end

function OperationActivityView:RefreshDayIndex()
    self.ctn_select_day_index = OperationCtnRechargeWGData.Instance:GetCtnRechargeOpenDay()
end