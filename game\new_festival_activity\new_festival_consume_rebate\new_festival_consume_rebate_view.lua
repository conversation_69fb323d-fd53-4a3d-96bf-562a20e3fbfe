-- 新节日活动_消费返利
local consume_rebate_timer_key = "consume_rebate_timer"

function NewFestivalActivityView:LoadIndexCallBackConsumeRebate()
    NewFestivalConsumeRebateWGCtrl.Instance:SendConsumeRebateReq(OA_NEW_FESTIVAL_CONSUME_REBATE_INFO.INFO)
    self:SetConsumeRebateImg()
    self:CreateConsumeRebateCountDown()
end

function NewFestivalActivityView:ReleaseCallBackConsumeRebate()
    if CountDownManager.Instance:HasCountDown(consume_rebate_timer_key) then
        CountDownManager.Instance:RemoveCountDown(consume_rebate_timer_key)
    end
end

function NewFestivalActivityView:SetConsumeRebateImg()
    local consume_rebate_bg_bundle, consume_rebate_bg_asset = ResPath.GetNewFestivalRawImages("xffl_bg")
    self.node_list["consume_rebate_bg"].raw_image:LoadSprite(consume_rebate_bg_bundle, consume_rebate_bg_asset, function ()
        self.node_list["consume_rebate_bg"].raw_image:SetNativeSize()
     end)

    local consume_rebate_title_bg_bundle, consume_rebate_title_bg_asset = ResPath.GetNewFestivalRawImages("xffl_title")
    self.node_list["consume_rebate_title_bg"].raw_image:LoadSprite(consume_rebate_title_bg_bundle, consume_rebate_title_bg_asset, function ()
        self.node_list["consume_rebate_title_bg"].raw_image:SetNativeSize()
    end)

    local consume_rebate_second_title_bg_bundle, consume_rebate_second_title_bg_asset = ResPath.GetNewFestivalRawImages("xffl_second_title")
    self.node_list["consume_rebate_second_title_bg"].raw_image:LoadSprite(consume_rebate_second_title_bg_bundle, consume_rebate_second_title_bg_asset, function ()
        self.node_list["consume_rebate_second_title_bg"].raw_image:SetNativeSize()
    end)

    local consume_rebate_box_bg_bundle, consume_rebate_box_bg_asset = ResPath.GetNewFestivalRawImages("xffl_box_bg")
    self.node_list["consume_rebate_box_bg"].raw_image:LoadSprite(consume_rebate_box_bg_bundle, consume_rebate_box_bg_asset, function ()
        self.node_list["consume_rebate_box_bg"].raw_image:SetNativeSize()
    end)

    local consume_rebate_box_img_bundle, consume_rebate_box_img_asset = ResPath.GetNewFestivalRawImages("xffl_box")
    self.node_list["consume_rebate_box_img"].raw_image:LoadSprite(consume_rebate_box_img_bundle, consume_rebate_box_img_asset, function ()
        self.node_list["consume_rebate_box_img"].raw_image:SetNativeSize()
    end)

    local consume_rebate_text_bg_bundle, consume_rebate_text_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xffl_txt_bg")
    self.node_list["consume_rebate_text_bg"].image:LoadSprite(consume_rebate_text_bg_bundle, consume_rebate_text_bg_asset)

    local consume_rebate_fgx_bundle, consume_rebate_fgx_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xffl_fgx")
    self.node_list["consume_rebate_fgx"].image:LoadSprite(consume_rebate_fgx_bundle, consume_rebate_fgx_asset)

    local timer_bg_bundle, timer_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dl_di2")
    self.node_list.consume_rebate_act_bg.image:LoadSprite(timer_bg_bundle, timer_bg_asset, function ()
        self.node_list.consume_rebate_act_bg.image:SetNativeSize()
    end)

    local shop_text_cfg = NewFestivalActivityWGData.Instance:GetConsumeRebateTextCfg()
    self.node_list["consume_rebate_text1"].text.color = Str2C3b(shop_text_cfg.consume_rebate_text_color)
    self.node_list["consume_rebate_act_des"].text.color = Str2C3b(shop_text_cfg.text_des_color)
    self.node_list["consume_rebate_text2"].text.color = Str2C3b(shop_text_cfg.text_color_1)
    self.node_list["consume_rebate_num"].text.color = Str2C3b(shop_text_cfg.rebate_num_text_color)
    self.node_list["consume_rebate_act_times"].text.color = Str2C3b(shop_text_cfg.timer_color)
    self.node_list.consume_rebate_act_des.text.text = Language.NewFestivalActivity.ConsumeRebateDes
end

function NewFestivalActivityView:OnFlushConsumeRebate()
    local rebate_num = NewFestivalConsumeRebateWGData.Instance:GetConsumeRebateNum()
    self.node_list.consume_rebate_num.text.text = rebate_num
end

function NewFestivalActivityView:CreateConsumeRebateCountDown()
    if CountDownManager.Instance:HasCountDown(consume_rebate_timer_key) then
        return
    end

    local time, total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_FESTIVAL_ACT_CONSUME_REBATE)
    if time > 0 then
        self:ConsumeRebateUpdateCountDown(total_time - time, total_time)
        CountDownManager.Instance:AddCountDown(consume_rebate_timer_key, BindTool.Bind1(self.ConsumeRebateUpdateCountDown, self), BindTool.Bind1(self.ConsumeRebateCompleteCallBack, self), nil, time, 1)
    else
        self:ConsumeRebateCompleteCallBack()
    end
end

function NewFestivalActivityView:ConsumeRebateUpdateCountDown(elapse_time, total_time)
    if self.node_list and self.node_list.consume_rebate_act_times then
        local shop_text_cfg = NewFestivalActivityWGData.Instance:GetConsumeRebateTextCfg()
        local time_part_color = shop_text_cfg and shop_text_cfg.time_part_color or COLOR3B.D_GREEN
        self.node_list.consume_rebate_act_times.text.text = string.format(Language.NewFestivalActivity.ActTime, time_part_color, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
    end
end

function NewFestivalActivityView:ConsumeRebateCompleteCallBack()
    if self.node_list and self.node_list.consume_rebate_act_times then
        self.node_list.consume_rebate_act_times.text.text = Language.Common.ActivityIsEnd
    end
end