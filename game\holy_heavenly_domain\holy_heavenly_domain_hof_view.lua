function HolyHeavenlyDomainView:LoadIndexCallBackRank()
    self.rank_show_modle_cache = -1

    if not self.all_server_rank_list then
        self.all_server_rank_list = AsyncListView.New(HHDRankAllServerListRender, self.node_list.all_server_rank_list)
    end

    if not self.all_server_reward_list then
        self.all_server_reward_list = AsyncListView.New(ItemCell, self.node_list.all_server_my_rank_reward)
        self.all_server_reward_list:SetStartZeroIndex(true)
    end

    if not self.total_server_rank_list then
        self.total_server_rank_list = AsyncListView.New(HHDRankTotalServerListRender, self.node_list.total_server_rank_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_rank_go_worship, BindTool.Bind(self.OnClickRankGoWorShip, self))
    XUI.AddClickEventListener(self.node_list.btn_domain_hof_tip, BindTool.Bind(self.OnClickDomainHofTip, self))
end

function HolyHeavenlyDomainView:ShowIndexCallBackRank()
    self:FlushRankCountDownTomer()
	HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.PLAYER_RANK, CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK)
    HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.SERVER_RANK)
end

function HolyHeavenlyDomainView:ChangeIndexCallBackRank()
    self:CancheDemainRankCountDown()
end

function HolyHeavenlyDomainView:CancheDemainRankCountDown()
    if CountDownManager.Instance:HasCountDown("holy_heavenly_domain_rank") then
		CountDownManager.Instance:RemoveCountDown("holy_heavenly_domain_rank")
	end
end

function HolyHeavenlyDomainView:FlushRankCountDownTomer()
    self.node_list.desc_rank_count_down_time.text.text = ""
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local time = HolyHeavenlyDomainWGData.Instance:GetActTimeEnd()
    
    if time > server_time then
        self:CancheDemainRankCountDown()

        self.node_list.rank_count_down_tip:CustomSetActive(true)
        CountDownManager.Instance:AddCountDown("holy_heavenly_domain_rank",
		    function (now_time, total_time)
			    if self.node_list.desc_rank_count_down_time then
				    local time = math.ceil(total_time - now_time)
				    self.node_list.desc_rank_count_down_time.text.text = string.format(Language.HolyHeavenlyDomain.SeasonEndTime, COLOR3B.GREEN, TimeUtil.FormatSecondDHM7(time))
			    end
		    end,
		    function ()
			    if self.node_list.desc_rank_count_down_time then
				    self.node_list.desc_rank_count_down_time.text.text = ""
                    self.node_list.rank_count_down_tip:CustomSetActive(false)
			    end
		    end,
        time, nil, 1)
    end
end

function HolyHeavenlyDomainView:ReleaseCallBackRank()
    self.rank_show_modle_cache = nil

    if self.rank_show_model then
        self.rank_show_model:DeleteMe()
        self.rank_show_model = nil
    end

    if self.all_server_rank_list then
        self.all_server_rank_list:DeleteMe()
        self.all_server_rank_list = nil
    end

    if self.total_server_rank_list then
        self.total_server_rank_list:DeleteMe()
        self.total_server_rank_list = nil
    end

    if self.all_server_reward_list then
        self.all_server_reward_list:DeleteMe()
        self.all_server_reward_list = nil
    end
    
    self:CancheDemainRankCountDown()
end

function HolyHeavenlyDomainView:OnFlushRank(param_t)
    local all_server_data_list, total_player_score_rank_list, my_server_data, total_my_data = HolyHeavenlyDomainWGData.Instance:GetHofRankDataList()
    self.all_server_rank_list:SetDataList(all_server_data_list)
    self.total_server_rank_list:SetDataList(total_player_score_rank_list)
    self.node_list.all_server_rank_no_data:CustomSetActive(IsEmptyTable(all_server_data_list))
    self.node_list.total_server_rank_no_data:CustomSetActive(IsEmptyTable(total_player_score_rank_list))

    self:FlushMyServerRankInfo(my_server_data)
    self:FlushTotalMyServerRankInfo(total_my_data)
    self:FlushRankModel(total_player_score_rank_list[1])

    local can_enter_mobai_scene = HolyHeavenlyDomainWGData.Instance:CanEnterMoBaiScene()
    self.node_list.btn_rank_go_worship:CustomSetActive(can_enter_mobai_scene)
end

function HolyHeavenlyDomainView:FlushMyServerRankInfo(my_server_data)
    local reward_data_list = {}
    local all_desc_name = "--"
    local all_desc_score = 0

    if IsEmptyTable(my_server_data) then
        self.node_list.all_img_rank:CustomSetActive(false)
        self.node_list.all_desc_rank.text.text = Language.HolyHeavenlyDomain.NotInTheList
        self.node_list.all_desc_score.text.text = ""
    else
        local rank = my_server_data.rank
        local is_top_three = rank <= 3
        local rank_str = is_top_three and "" or rank
        self.node_list.all_desc_rank.text.text = rank_str

        if is_top_three then
            local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. rank)
            self.node_list.all_img_rank.image:LoadSprite(bundle, asset, function ()
                self.node_list.all_img_rank.image:SetNativeSize()
            end)
        end

        self.node_list.all_img_rank:CustomSetActive(is_top_three)

        all_desc_score = my_server_data.score
        local camp_seq = my_server_data.camp_seq
        local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(camp_seq)

        if not IsEmptyTable(camp_cfg) then
            all_desc_name = camp_cfg.camp_name
        end

        local data_list = HolyHeavenlyDomainWGData.Instance:GetHofServerScoreRankCfgByRank(rank)
        reward_data_list = data_list and data_list.reward_item or {}
    end
    
    self.node_list.all_desc_score.text.text = all_desc_score
    self.node_list.all_desc_name.text.text = all_desc_name
    self.all_server_reward_list:SetDataList(reward_data_list)
end

function HolyHeavenlyDomainView:FlushTotalMyServerRankInfo(total_my_data)
    local uuid = RoleWGData.Instance:GetUUid()

    if IsEmptyTable(total_my_data) then
        self.node_list.total_img_rank:CustomSetActive(false)
        self.node_list.total_desc_rank.text.text = Language.HolyHeavenlyDomain.NotInTheList
        self.node_list.total_desc_score.text.text = "--"
        self.node_list.total_reward:CustomSetActive(false)
        self.node_list.total_desc_name.text.text = RoleWGData.Instance:GetAttr("name")
    else
        self.node_list.total_desc_score.text.text = total_my_data.rank_score
        local rank = total_my_data.rank
        local person_rank_reward_cfg = HolyHeavenlyDomainWGData.Instance:GetHofPersonScoreRankCfgByRank(rank)
        local title_id = person_rank_reward_cfg and person_rank_reward_cfg.show_title or 0

        local is_top_three = rank <= 3
        local rank_str = is_top_three and "" or rank
        self.node_list.total_desc_rank.text.text = rank_str

        if is_top_three then
            local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. rank)
            self.node_list.total_img_rank.image:LoadSprite(bundle, asset, function ()
                self.node_list.total_img_rank.image:SetNativeSize()
            end)
        end
        self.node_list.total_img_rank:CustomSetActive(is_top_three)

        self.node_list.total_desc_name.text.text = total_my_data.name

        if title_id > 0 then
            self.node_list.total_reward:ChangeAsset(ResPath.GetTitleModel(title_id))
            self.node_list.total_reward:CustomSetActive(true)
            RectTransform.SetLocalScale(self.node_list.total_reward.rect, 0.5)
        else
            self.node_list.total_reward:CustomSetActive(false)
        end
    end

    local camp = HolyHeavenlyDomainWGData.Instance:GetMyCampSeq()
    local target_camp = camp > 0 and camp or 16
    local bundle, asset = ResPath.GetHolyHeavenlyDomainCountryImg(target_camp, 1)
    self.node_list.total_img_team.image:LoadSprite(bundle, asset, function ()
        self.node_list.total_img_team.image:SetNativeSize()
    end)
    
    local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(target_camp)
    local camp_name = camp_cfg and camp_cfg.camp_name or ""
    self.node_list.total_team_name.text.text = camp_name
end

function HolyHeavenlyDomainView:FlushRankModel(data)
    local user_id = RoleWGData.Instance:InCrossGetOriginUid()
    local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true}
    local role_id = ((data or {}).uuid or {}).temp_low or -1
    local plat_type = ((data or {}).uuid or {}).temp_high or -1
    local has_role_data = role_id > 0

    self.node_list["flag_rank_no_data"]:CustomSetActive(not has_role_data)
    self.node_list["flag_rank_has_data"]:CustomSetActive(has_role_data)
    self.node_list["model_display_rank"]:CustomSetActive(has_role_data)

    if not has_role_data then
        self.node_list.desc_role_name.text.text = Language.HolyHeavenlyDomain.XuWeiYiDai
    end

    local set_model_data = function (role_vo)
        if self.rank_show_model then
            self.rank_show_model:SetModelResInfo(role_vo, ignore_table, function()
                -- self.rank_show_model:PlayRoleShowAction()
            end)

            self.node_list.desc_role_name.text.text = role_vo.role_name
            self.node_list.desc_rank_cap_value.text.text = role_vo.capability or 0
            self.rank_show_model:FixToOrthographic(self.root_node_transform)
        end
    end

    if self.rank_show_modle_cache ~= role_id then
        self.rank_show_modle_cache = role_id

        if not self.rank_show_model then
            local node = self.node_list["model_display_rank"]
            self.rank_show_model = RoleModel.New()
            local display_data = {
                parent_node = node,
                camera_type = MODEL_CAMERA_TYPE.BASE,
                -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                rt_scale_type = ModelRTSCaleType.M,
                can_drag = true,
            }
            
            self.rank_show_model:SetRenderTexUI3DModel(display_data)
            -- self.rank_show_model:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
        else
            self.rank_show_model:ClearModel()
        end

        if self.rank_show_model then
            if user_id == role_id then
                local role_vo = RoleWGData.Instance:GetRoleVo()
                set_model_data(role_vo)
            else
                BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
                    set_model_data(protocol)
                end, plat_type, true)
            end
        end
    else
        if self.rank_show_model then
            self.rank_show_model:PlayLastAction()
        end
    end
end

function HolyHeavenlyDomainView:OnClickRankGoWorShip()
    CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_DIVINE_DOMAIN)
end

function HolyHeavenlyDomainView:OnClickDomainHofTip()
    RuleTip.Instance:SetContent(Language.HolyHeavenlyDomain.HOFTipContent, Language.HolyHeavenlyDomain.HOFTipTitle)
end

------------------------------------HHDRankAllServerListRender--------------------------------------
HHDRankAllServerListRender = HHDRankAllServerListRender or BaseClass(BaseRender)

function HHDRankAllServerListRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function HHDRankAllServerListRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function HHDRankAllServerListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rank = self.data.rank
    local is_top_three = rank <= 3
    local rank_str = is_top_three and "" or rank
    local bg_bundle, bg_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_rank_bg")

    if is_top_three then
        local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. rank)
        self.node_list.img_rank.image:LoadSprite(bundle, asset, function ()
            self.node_list.img_rank.image:SetNativeSize()
        end)

        bg_bundle, bg_asset = ResPath.GetCommonImages("a2_pm_di_"..rank)
    end

    self.node_list.tween_root.image:LoadSprite(bg_bundle, bg_asset)

    self.node_list.img_rank:CustomSetActive(is_top_three)

    local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(self.data.camp_seq)
    local camp_name = camp_cfg and camp_cfg.camp_name or ""
    self.node_list.desc_name.text.text = camp_name
    self.node_list.desc_score.text.text = self.data.score
    self.node_list.desc_rank.text.text = rank_str

    local reward_data_list = HolyHeavenlyDomainWGData.Instance:GetHofServerScoreRankCfgByRank(rank)
    local item_list = reward_data_list and reward_data_list.reward_item or {}
    self.reward_list:SetDataList(item_list)
end

------------------------------------HHDRankTotalServerListRender--------------------------------------
HHDRankTotalServerListRender = HHDRankTotalServerListRender or BaseClass(BaseRender)

function HHDRankTotalServerListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rank = self.data.rank
    local is_top_three = rank <= 3
    local rank_str = is_top_three and "" or rank
    local bg_bundle, bg_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_rank_bg")

    if is_top_three then
        local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. rank)
        self.node_list.img_rank.image:LoadSprite(bundle, asset, function ()
            self.node_list.img_rank.image:SetNativeSize()
        end)

        bg_bundle, bg_asset = ResPath.GetCommonImages("a2_pm_di_"..rank)
    end

    self.node_list.tween_root.image:LoadSprite(bg_bundle, bg_asset)

    self.node_list.img_rank:CustomSetActive(is_top_three)
    self.node_list.desc_rank.text.text = rank_str
    self.node_list.desc_name.text.text = self.data.name

    -- local plat, server = LLStrToInt(self.data.usid)
    local camp_id = HolyHeavenlyDomainWGData.Instance:GetCountryIdByServerUsid(self.data.usid)
    local bundle, asset = ResPath.GetHolyHeavenlyDomainCountryImg(camp_id, 1)
    self.node_list.img_team.image:LoadSprite(bundle, asset, function ()
        self.node_list.img_team.image:SetNativeSize()
    end)

    local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(camp_id)
    self.node_list.desc_team_name.text.text = camp_cfg and camp_cfg.camp_name or ""

    self.node_list.desc_score.text.text = self.data.rank_score

    local person_rank_reward_cfg = HolyHeavenlyDomainWGData.Instance:GetHofPersonScoreRankCfgByRank(rank)
    local title_id = person_rank_reward_cfg and person_rank_reward_cfg.show_title or 0

    if title_id > 0 then
    	self.node_list.reward:ChangeAsset(ResPath.GetTitleModel(title_id))
        self.node_list.reward:CustomSetActive(true)
        RectTransform.SetLocalScale(self.node_list.reward.rect, 0.5)
    else
        self.node_list.reward:CustomSetActive(false)
    end
end