ScreenShotModelData = ScreenShotModelData or BaseClass()
-- 时装需要展示的
local fashion_sex_prof_list = {
	{sex = 1, prof = 3, local_path = "Actors/Character/RoleMan/Body/"},
	{sex = 0, prof = 3, local_path = "Actors/Character/RoleWoman/Body/"},
}

local test_fashion_sex_prof_list = {
	{local_path = "Actors/Character/RoleMan/1101001/"},
	{local_path = "Actors/Character/RoleMan/1103001/"},
    {local_path = "Actors/Character/RoleWoman/3101001/"},
    {local_path = "Actors/Character/RoleWoman/3102001/"},
    {local_path = "Actors/Character/RoleWoman/3103001/"},
}

-- 神兵需要展示的
local shenbing_sex_prof_list = {
	{sex = 1, prof = 1, local_path = "Model/Weapon/9001/"},
	{sex = 0, prof = 2, local_path = "Model/Weapon/9002/"},
	{sex = 1, prof = 3, local_path = "Model/Weapon/9003/"},
}

function ScreenShotModelData:__init()
    if ScreenShotModelData.Instance then
		error("[ScreenShotModelData] Attempt to create singleton twice!")
		return
	end
    ScreenShotModelData.Instance = self
end

function ScreenShotModelData:__delete()
    ScreenShotModelData.Instance = nil
end

function ScreenShotModelData:GetModelShowList(type)
    if type == ScreenShotModelView.MODEL_TYPE.ALL then
        local list = {}
        for i = 2, ScreenShotModelView.MODEL_TYPE.MAX do
            local data_list = self:GetModelList(i)

            for j = 1, #data_list do
                table.insert(list, data_list[j])
            end
        end

        return list
    else
        return self:GetModelList(type)
    end
end

function ScreenShotModelData:GetDirectories(path, data, list)
    list = list or {}
    if IS_LOCLA_WINDOWS_DEBUG_EXE then
        path = string.format("../../Assets/Game/%s", path)
    else
        path = string.format("Assets/Game/%s", path)
    end

    -- 目录下是文件，非文件夹的
    -- if data.is_halo then
    --     local files = System.IO.Directory.GetFiles(path, "*.prefab")
    --     for i = 0, files.Length - 1 do
    --         local file = files[i]
    --         local name = System.IO.Path.GetFileName(file)
    --         local res_id = string.gsub(name,".prefab", "")
    --         local bundle, asset = nil, nil
    --         if data.is_halo then
    --             bundle = string.format("effects/prefab/halo/%s_prefab", string.lower(res_id))
    --             asset = res_id
    --         end
            
    --         local show_data = {
    --             res_id = res_id,
    --             is_show_role = data.is_show_role,
    --             is_weapon = data.is_weapon,
    --             is_fabao = data.is_fabao,
    --             is_tianshen = data.is_tianshen,
    --             is_mount = data.is_mount,
    --             is_kun = data.is_kun,
    --             prof = data.prof,
    --             sex = data.sex,
    --             type_name = data.type_name,
    --             folder_name = data.folder_name,
    --             respath_func = data.respath_func,
    --             pos_y = data.pos_y,
    --             rot_y = data.rot_y,
    --             scale = data.scale,
    --             bundle = bundle,
    --             asset = asset,
    --         }

    --         table.insert(list, show_data)
    --     end

    --     return list
    -- end


    local directories = System.IO.Directory.GetDirectories(path)
    for i = 0, directories.Length - 1 do
        local dir = directories[i]
        local name = System.IO.Path.GetFileName(dir)
        if not string.find(name, "Shared") then
            local res_id = name
            if data.is_pure_number then
                res_id = tonumber(name)
            end
    
            if res_id then
                local is_show = true
                -- 剔除
                -- 角色排除前三个基础套
                if data.is_show_role and res_id % 1000 <= 3 then
                    is_show = false
                elseif data.is_weapon then
                    local num = math.floor((res_id % 100000) / 10000)
                    is_show = num == data.sex
                elseif data.is_mount then
                    local num = math.floor(res_id / 1000)
                    is_show = num < 8
                elseif data.is_kun then
                    local num = math.floor(res_id / 1000)
                    is_show = num >= 8
                elseif data.is_halo then
                    local num = res_id:match("%d+")
                    res_id = num
                end
    
                if data.splice then
                    res_id = tonumber(res_id .. data.splice)
                end
    
                local tianshen_index = nil
                if data.is_tianshen then
                    tianshen_index = TianShenWGData.Instance:GetWeaponIndexByAppeImageId(res_id)
                end
    
                if is_show then
                    local show_data = {
                        res_id = res_id,
                        is_show_role = data.is_show_role,
                        is_weapon = data.is_weapon,
                        is_fabao = data.is_fabao,
                        is_tianshen = data.is_tianshen,
                        is_mount = data.is_mount,
                        is_kun = data.is_kun,
                        prof = data.prof,
                        sex = data.sex,
                        type_name = data.type_name,
                        folder_name = data.folder_name,
                        respath_func = data.respath_func,
                        tianshen_index = tianshen_index,
                        pos_y = data.pos_y,
                        rot_y = data.rot_y,
                        scale = data.scale,
                    }
        
                    table.insert(list, show_data)
                end
            end
        end
    end

    return list
end

function ScreenShotModelData:GetModelList(type)
    local list = {}

    local fashion_cfg = NewAppearanceWGData.Instance.fashion_map_cfg
    if not fashion_cfg then return list end
    if type == ScreenShotModelView.MODEL_TYPE.FASHION then
        for k,v in ipairs(fashion_sex_prof_list) do
            local data = {
                is_show_role = true,
                prof = v.prof,
                sex = v.sex,
                type_name = "时装",
                folder_name = "Fashion",
                is_pure_number = true,
                rot_y = 180,
                pos_y = -2.5,
            }
    
            list = self:GetDirectories(v.local_path, data, list)
        end

    elseif type == ScreenShotModelView.MODEL_TYPE.SHENBING then
        for k,v in ipairs(shenbing_sex_prof_list) do
            local data = {
                is_weapon = true,
                prof = v.prof,
                sex = v.sex,
                type_name = "武器",
                folder_name = "Weapon",
                respath_func = ResPath.GetWeaponModelRes,
                splice = "01",
                is_pure_number = true,
            }
            list = self:GetDirectories(v.local_path, data, list)
        end

    elseif type == ScreenShotModelView.MODEL_TYPE.WING then
        local data = {
            type_name = "翅膀",
            folder_name = "Wing",
            respath_func = ResPath.GetWingModel,
        }
        list = self:GetDirectories("Model/Wings/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.FABAO then
        local data = {
            is_fabao = true,
            type_name = "法宝",
            folder_name = "Fabao",
            respath_func = ResPath.GetFaBaoModel,
        }
        list = self:GetDirectories("Model/Fabao/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.HALO then
        local data = {
            is_halo = true,
            type_name = "光环",
            folder_name = "Halo",
            respath_func = ResPath.GetHaloModel,
        }

        list = self:GetDirectories("Model/Halo/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.JIANZHEN then
        local data = {
            type_name = "剑阵",
            folder_name = "JianZhen",
            respath_func = ResPath.GetJianZhenModel,
        }
        list = self:GetDirectories("Model/Beishi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.TIANSHEN then
        local data = {
            is_tianshen = true,
            type_name = "天神",
            folder_name = "Tianshen",
            is_pure_number = true,
        }
        list = self:GetDirectories("Model/Tianshen/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.BOSS then
        local data = {
            type_name = "Boss",
            folder_name = "Boss",
            respath_func = ResPath.GetMonsterModel,
            pos_y = -2.5,
        }
        list = self:GetDirectories("Model/Boss/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.NPC then
        local data = {
            type_name = "NPC",
            folder_name = "NPC",
            respath_func = ResPath.GetNpcModel,
            pos_y = -2.5,
        }
        list = self:GetDirectories("Model/NPC/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.MOUNT then
        local data = {
            is_mount = true,
            type_name = "坐骑",
            folder_name = "Zuoqi",
            respath_func = ResPath.GetMountModel,
            pos_y = -2.5,
            is_pure_number = true,
        }
        list = self:GetDirectories("Model/Zuoqi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.LINGCHONG then
        local data = {
            type_name = "灵宠",
            folder_name = "Chongwu",
            respath_func = ResPath.GetPetModel,
        }
        list = self:GetDirectories("Model/Chongwu/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.HUAKUN then
        local data = {
            is_kun = true,
            type_name = "鲲",
            folder_name = "kun",
            respath_func = ResPath.GetMountModel,
            pos_y = -2.5,
            is_pure_number = true,
        }
        list = self:GetDirectories("Model/Zuoqi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.BABY then
        local data = {
            type_name = "仙娃",
            folder_name = "Child",
            respath_func = ResPath.GetHaiZiModel,
            is_pure_number = true,
        }
        list = self:GetDirectories("Model/Child/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.IMP then
        local data = {
            type_name = "守护",
            folder_name = "Shouhu",
            respath_func = ResPath.GetGuardModel,
            is_pure_number = true,
        }
        list = self:GetDirectories("Model/Shouhu/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.WUHUN then
        local data = {
            type_name = "武魂",
            folder_name = "WuHun",
            respath_func = ResPath.GetWuHunModel,
        }
        list = self:GetDirectories("Model/WuHun/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.YUSHOU then
        local data = {
            type_name = "御兽",
            folder_name = "Yushou",
            respath_func = ResPath.GetBeastsModel,
        }
        list = self:GetDirectories("Model/Yushou/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.GATHER then
        local data = {
            type_name = "采集物",
            folder_name = "Gather",
            respath_func = ResPath.GetGatherModel,
            pos_y = -2,
            scale = 1.5,
        }
        list = self:GetDirectories("Model/Gather/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.BEISHI then
        local data = {
            type_name = "背饰",
            folder_name = "Beishi",
            respath_func = ResPath.GetJianZhenModel,
            is_pure_number = true,
        }
        list = self:GetDirectories("Model/Beishi/", data, list)
    end

    return list
end

--=========================================================
--=========================================================
--=========================================================
--=========================================================
--=========================================================
--=========================================================
--=========================================================
--=========================================================
--=========================================================
--=========================================================
function ScreenShotModelData:GetModelTestShowList(type)
    if type == ScreenShotModelView.MODEL_TYPE.ALL then
        local list = {}
        for i = 2, ScreenShotModelView.MODEL_TYPE.MAX do
            local data_list = self:GetModelTestList(i)

            for j = 1, #data_list do
                table.insert(list, data_list[j])
            end
        end

        return list
    else
        return self:GetModelTestList(type)
    end
end

function ScreenShotModelData:GetModelTestList(type)
    local list = {}

    local fashion_cfg = NewAppearanceWGData.Instance.fashion_map_cfg
    if not fashion_cfg then return list end
    if type == ScreenShotModelView.MODEL_TYPE.FASHION then
        for k,v in ipairs(test_fashion_sex_prof_list) do
            local data = {
                is_show_role = true,
                folder_name = "character",
                is_pure_number = true,
            }

            list = self:GetTestDirectories(v.local_path, data, list)
        end

    elseif type == ScreenShotModelView.MODEL_TYPE.SHENBING then
        for k,v in ipairs(shenbing_sex_prof_list) do
            local data = {
                is_weapon = true,
                folder_name = "Weapon",
                splice = "01",
                is_pure_number = true,
            }
            list = self:GetTestDirectories(v.local_path, data, list)
        end

    elseif type == ScreenShotModelView.MODEL_TYPE.WING then
        local data = {
            folder_name = "Wings",
        }
        list = self:GetTestDirectories("Model/Wings/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.FABAO then
        local data = {
            is_fabao = true,
            folder_name = "Fabao",
        }
        list = self:GetTestDirectories("Model/Fabao/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.HALO then
        local data = {
            is_halo = true,
            folder_name = "Halo",
        }

        list = self:GetTestDirectories("Model/Halo/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.JIANZHEN then
        local data = {
            folder_name = "JianZhen",
        }
        list = self:GetTestDirectories("Model/Beishi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.TIANSHEN then
        local data = {
            is_tianshen = true,
            folder_name = "Tianshen",
            is_pure_number = true,
        }
        list = self:GetTestDirectories("Model/Tianshen/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.TIANSHEN_WUQI then
        local data = {
            folder_name = "Tianshenwuqi",
            is_pure_number = true,
        }
        list = self:GetTestDirectories("Model/Tianshenwuqi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.BOSS then
        local data = {
            folder_name = "Boss",
        }
        list = self:GetTestDirectories("Model/Boss/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.NPC then
        local data = {
            folder_name = "NPC",
        }
        list = self:GetTestDirectories("Model/NPC/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.MOUNT then
        local data = {
            is_mount = true,
            folder_name = "Zuoqi",
            is_pure_number = true,
        }
        list = self:GetTestDirectories("Model/Zuoqi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.LINGCHONG then
        local data = {
            folder_name = "Chongwu",
        }
        list = self:GetTestDirectories("Model/Chongwu/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.HUAKUN then
        local data = {
            is_kun = true,
            folder_name = "Zuoqi",
            is_pure_number = true,
        }
        list = self:GetTestDirectories("Model/Zuoqi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.BABY then
        local data = {
            folder_name = "Child",
            is_pure_number = true,
        }
        list = self:GetTestDirectories("Model/Child/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.IMP then
        local data = {
            folder_name = "Shouhu",
            is_pure_number = true,
        }
        list = self:GetTestDirectories("Model/Shouhu/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.WUHUN then
        local data = {
            folder_name = "WuHun",
        }
        list = self:GetTestDirectories("Model/WuHun/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.YUSHOU then
        local data = {
            folder_name = "Yushou",
        }
        list = self:GetTestDirectories("Model/Yushou/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.GATHER then
        local data = {
            folder_name = "Gather",
        }
        list = self:GetTestDirectories("Model/Gather/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.TIANHUN_SHENQI then
        local data = {
            folder_name = "Tianhunshenqi",
        }
        list = self:GetTestDirectories("Model/Tianhunshenqi/", data, list)

    elseif type == ScreenShotModelView.MODEL_TYPE.UI_OTHER then
        local data = {
            folder_name = "UIother",
        }
        list = self:GetTestDirectories("Model/UIother/", data, list)
    elseif type == ScreenShotModelView.MODEL_TYPE.BEISHI then
        local data = {
            folder_name = "Beishi",
        }
        list = self:GetTestDirectories("Model/Beishi/", data, list)
    elseif type == ScreenShotModelView.MODEL_TYPE.EQUIPDROP then
        local data = {
            folder_name = "EquipDrop",
        }
        list = self:GetTestDirectories("Model/EquipDrop/", data, list)
    elseif type == ScreenShotModelView.MODEL_TYPE.FOOTLIGHT then
        local data = {
            folder_name = "FootLight",
        }
        list = self:GetTestDirectories("Model/FootLight/", data, list)
    end

    return list
end

function ScreenShotModelData:GetTestDirectories(path, data, list)
    list = list or {}
    if IS_LOCLA_WINDOWS_DEBUG_EXE then
        path = string.format("../../Assets/Game/%s", path)
    else
        path = string.format("Assets/Game/%s", path)
    end

    -- 目录下是文件，非文件夹的
    if data.is_show_role then
        local files = System.IO.Directory.GetFiles(path, "*.prefab")
        for i = 0, files.Length - 1 do
            local file = files[i]
            local name = System.IO.Path.GetFileName(file)
            local res_id = string.gsub(name,".prefab", "")
            local bundle = string.lower(string.format("model/%s/%s_prefab", data.folder_name, res_id))
            if data.splice then
                res_id = res_id .. data.splice
            end
            local asset = res_id
            local show_data = {
                bundle = bundle,
                asset = asset,
            }

            table.insert(list, show_data)
        end

        return list
    end

    local directories = System.IO.Directory.GetDirectories(path)
    for i = 0, directories.Length - 1 do
        local dir = directories[i]
        local name = System.IO.Path.GetFileName(dir)
        if not string.find(name, "Shared") then
            local res_id = name
            if data.is_pure_number then
                res_id = tonumber(name)
            end

            if res_id then
                -- 剔除
                local is_show = true
                if data.is_mount then
                    local num = math.floor(res_id / 1000)
                    is_show = num < 8
                elseif data.is_kun then
                    local num = math.floor(res_id / 1000)
                    is_show = num >= 8
                end

                if is_show then
                    local bundle = string.lower(string.format("model/%s/%s_prefab", data.folder_name, res_id))
                    if data.splice then
                        res_id = tonumber(res_id .. data.splice)
                    end
                    local asset = res_id

                    local show_data = {
                        bundle = bundle,
                        asset = asset,
                    }
        
                    table.insert(list, show_data)
                end
            end
        end
    end

    return list
end
