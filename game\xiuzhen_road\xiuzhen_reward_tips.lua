XiuZhenRewardTips = XiuZhenRewardTips or BaseClass(SafeBaseView)
-- -40 22
function XiuZhenRewardTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
	-- 					{vector2 = Vector2(0, -30), sizeDelta = Vector2(814, 520)})
	self:AddViewResource(0, "uis/view/xiuzhen_road_ui_prefab", "layout_reward")
end

function XiuZhenRewardTips:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function XiuZhenRewardTips:LoadCallBack()
	self.node_list["close_btn"].button:AddClickListener(BindTool.Bind(self.OnCloseView, self))
	--self.node_list["title_view_name"].text.text = Language.XiuZhenRoad.ViewName
	if nil == self.reward_list_view then
		self.reward_list_view = AsyncListView.New(XiuZhenReWardItemRender, self.node_list["ph_item_list"])
	end
end

function XiuZhenRewardTips:OnFlush()
	if self.reward_list_view then
		local leiji_data = __TableCopy(XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiInfo())
		local cur_total_progress =  XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiRewardProgress()
		--数据处理
		local data_list = {}
		for k,v in pairs(leiji_data) do
			local list = {}
			list["old_info"] = v
			local reward_flag = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiRewardFlag(v.progress)
			if 	1 == reward_flag then
				list["reward_states"] = XIUZHENZHILU_TASK_STATES.YLQ
			elseif cur_total_progress >= v.condition and 0 == reward_flag then
				list["reward_states"] = XIUZHENZHILU_TASK_STATES.KLQ
			else
				list["reward_states"] = XIUZHENZHILU_TASK_STATES.WDC
			end
			list["seq"] = v.progress
			table.insert(data_list,list)
		end
		table.sort(data_list,SortTools.KeyLowerSorter("reward_states","seq"))
		self.reward_list_view:SetDataList(data_list, 0)
		self.node_list["star_num"].text.text = string.format(Language.XiuZhenRoad.TipsDesc, cur_total_progress)
	end
end

function XiuZhenRewardTips:OnCloseView()
	XiuZhenRoadWGCtrl.Instance:CloseXiuZhenRewardTips()
end


--修真累积奖励
XiuZhenReWardItemRender = XiuZhenReWardItemRender or BaseClass(BaseRender)
function XiuZhenReWardItemRender:__delete()
	if self.rewarditem_cells then
		for k,v in pairs(self.rewarditem_cells) do
			v:DeleteMe()
		end
		self.rewarditem_cells = nil
	end
end

function XiuZhenReWardItemRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	if nil == self.rewarditem_cells then
		self.rewarditem_cells = {}
		for i = 0, 4 do
			self.rewarditem_cells[i] = ItemCell.New(self.node_list["reward_list"])
			--self.rewarditem_cells[i]:SetInstanceParent(self.node_list["ph_show_"..i])
		end	
	end
end

function XiuZhenReWardItemRender:OnFlush()
	if not self.data then return end
	local reward_states = self.data.reward_states
	local cell_count = 0
	for i = 0, 4 do
		if self.data.old_info.reward_item [i] then
			self.rewarditem_cells[i]:SetActive(true)
			self.rewarditem_cells[i]:SetData(self.data.old_info.reward_item[i])
			cell_count = cell_count + 1
		else
			self.rewarditem_cells[i]:SetActive(false)
		end	
	end
	self.node_list["lbl_lscs"].text.text = string.format(Language.XiuZhenRoad.TipItemsDesc, self.data.old_info.condition)
	self.node_list["btn_lingqu"]:SetActive(reward_states == XIUZHENZHILU_TASK_STATES.KLQ)
	self.node_list["image_ylq"]:SetActive(reward_states == XIUZHENZHILU_TASK_STATES.YLQ)
	--XUI.SetButtonEnabled(self.node_list["image_ylq"], false)
	self.node_list["text_wdc"]:SetActive(reward_states == XIUZHENZHILU_TASK_STATES.WDC)
	self.node_list.scroll_rect.scroll_rect.enabled = cell_count > 2
	Transform.SetLocalPosition(self.node_list.scroll_rect.transform, cell_count > 2 and Vector3(-2, 15 ,0) or Vector3(12, 15 ,0))
end

function XiuZhenReWardItemRender:OnClickLingQu()
	XiuZhenRoadWGCtrl.Instance:SendXiuZhenZhiLuReq(XIUZHENZHILU_OPEN_TYPE.XIUZHENZHILU_OPEN_TYPE_PROGRESS_REWARD,self.data.old_info.progress)
end
