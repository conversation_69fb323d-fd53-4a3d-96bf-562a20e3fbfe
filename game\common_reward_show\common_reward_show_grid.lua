local flush_type =
{
	refresh = 1,
	refresh_all_act_cells = 2,
	update_one_cell = 3,
	update_select_state = 4,
}

local flush_type_list = {}
for k, v in pairs(flush_type) do
	flush_type_list[v] = k
end

local RewardShowGridCalaInfo =
{
	--Normal
	[1] =
	{
		group_tilte_high = 32,			-- 标题 高度
		child_cell_high = 74,			-- cell 高度
		child_cell_spacing = 16,		-- cell 之间间距
		group_spaicing = 16,			-- group 之间间距
	},
	--Normal_Probability
	[2] =
	{
		group_tilte_high = 32,
		child_cell_high = 106,
		child_cell_spacing = 16,
		group_spaicing = 16,
	},
	--Title
	[3] =
	{
		group_tilte_high = 32,
		child_cell_high = 74,
		child_cell_spacing = 16,
		group_spaicing = 16,
	},
	--Title_Probability
	[4] =
	{
		group_tilte_high = 32,
		child_cell_high = 106,
		child_cell_spacing = 16,
		group_spaicing = 16,
	},
	--Normal_Center
	[5] =
	{
		group_tilte_high = 32,
		child_cell_high = 74,
		child_cell_spacing = 10,
		group_spaicing = 10,
	},
	--Title_Probability_2
	[6] =
	{
		group_tilte_high = 32,
		child_cell_high = 106,
		child_cell_spacing = 16,
		group_spaicing = 8,
	},
}

RewardShowGrid = RewardShowGrid or BaseClass()
function RewardShowGrid:__init()
	self.item_render = nil   -- 创建的item类型
	self.data_list = {}      -- 格子数据列表
	self.create_callback = nil -- 创建完成回调

	self.first_time_load = true -- 是否第一次加载

	self.cell_list = {}
	self.select_tab = { [1] = {} } -- 选择项保存表
	self.asset_bundle = nil
	self.asset_name = nil
	self.has_data_max_index = 0 --最大有数据的格子索引
	self.flush_param_t = {}
end

function RewardShowGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
end

-- 设置数据源
function RewardShowGrid:SetDataList(data)
	self.data_list = data
	self:__Flush(flush_type.refresh, { 0, 0 })
end

-- 创建网格 {t.asset_bundle, t.asset_name, t.list_view, t.columns}
--- t.itemRender 可为nil，默认为ItemCell
-- asset_bundle 和 asset_name 创建自己的预制物（不传默认创建ItemCell）
function RewardShowGrid:CreateCells(t)
	if t.assetBundle and t.assetName then
		self.asset_bundle = t.assetBundle -- 路径
		self.asset_name = t.assetName -- 预制体名字
	end

	self.content_type = t.content_type
	self.color_type = t.color_type

	self.item_render = t.itemRender or ItemCell -- render方法
	self.list_view = t.list_view             -- 需生成的东西的父节点
	self.columns = t.columns or 1            -- 多少列

	if self.first_time_load then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self) -- 需要创建多少个group
		list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self) -- 刷新格子
		list_delegate.CellSizeDel = BindTool.Bind(self.GetGroupCellHigh, self)
		self.first_time_load = false
	end

	if nil ~= self.create_callback then
		self.create_callback()
	end
end

-- 设置创建完成回调函数(在执行CreateCells之前设置)
function RewardShowGrid:SetCreateCallback(create_callback)
	self.create_callback = create_callback
end

--获得格子数
function RewardShowGrid:GetListViewNumbers()
	return #self.data_list
end

-- title_reward_item_data = --包含标题与奖励列表的数据.
-- {
-- 		{
-- 			title_text, --标题.
-- 			reward_item_list --奖励列表.
-- 		},
-- 		{
-- 			title_text,
-- 			reward_item_list
-- 		},
-- }
--刷新格子
function RewardShowGrid:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	if not item_cell then
		item_cell = RewardShowItemRender.New(cell.gameObject, "grid_cell_top_" .. self.color_type)
		item_cell:SetAssetBundle(self.asset_bundle, self.asset_name)
		item_cell:SetItemRender(self.item_render)
		self.cell_list[cell] = item_cell
	end

	local new_index = cell_index + 1
	item_cell:SetIndex(new_index)
	item_cell:SetData(self.data_list[new_index])
end

-- 单个group的高度
function RewardShowGrid:GetGroupCellHigh(cell_index)
	local data = self.data_list[cell_index + 1].reward_item_list
	if not data then
		return 0
	end

	local rows = math.ceil(#data / self.columns)
	local calc_info = RewardShowGridCalaInfo[self.content_type]
	return calc_info.group_tilte_high + rows * calc_info.child_cell_high + calc_info.child_cell_spacing * (rows - 1)
end

function RewardShowGrid:JumptToPrecent(percent)
	self:__Flush(flush_type.refresh, { 1, percent })
end

function RewardShowGrid:__DoRefresh(refresh_type, percent)
	if IsNil(self.list_view.scroller) then
		return
	end

	if self.list_view.scroller.isActiveAndEnabled then
		if refresh_type == 0 then
			self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
		elseif refresh_type == 1 then
			self.list_view.scroller:ReloadData(percent)
		elseif refresh_type == 2 then
			self.list_view.scroller:RefreshActiveCellViews()
		end
	end
end

function RewardShowGrid:__DoUpdateOneCell(index, data)
	local cell = self:GetCell(index)
	if cell then
		cell:SetData(data)
	end
end

function RewardShowGrid:__DoRefreshSelectState()
	for k, v in pairs(self.cell_list) do
		v:RefreshSelectState()
	end
end

function RewardShowGrid:__Flush(key, value)
	self.flush_param_t[key] = value
	TryDelayCall(self, function()
		self:__OnFlush()
	end, 0, "flush")
end

function RewardShowGrid:__OnFlush()
	for i, _ in ipairs(flush_type_list) do
		local v = self.flush_param_t[i]
		if nil ~= v then
			if i == flush_type.refresh then
				self:__DoRefresh(v[1], v[2])
				self.flush_param_t[flush_type.refresh_all_act_cells] = nil
				self.flush_param_t[flush_type.update_one_cell] = nil
				self.flush_param_t[flush_type.update_select_state] = nil
			end

			if i == flush_type.refresh_all_act_cells then
				self:__DoRefresh(2, 0)
			end

			if i == flush_type.update_one_cell then
				self:__DoUpdateOneCell(v[1], v[2])
			end

			if i == flush_type.update_select_state then
				self:__DoRefreshSelectState()
			end
		end
	end

	self.flush_param_t = {}
end
