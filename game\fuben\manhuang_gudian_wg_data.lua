---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by 123.
--- DateTime: 2019/10/9 20:55
---
ManHuangGuDianWGData = ManHuangGuDianWGData or BaseClass()
function ManHuangGuDianWGData:__init()
    ManHuangGuDianWGData.Instance = self

	self.manhuang_enter_times = 0
	self.manhuang_buy_times = 0

    --初始化蛮荒古殿配置
	self:InitManHuangGuDianCfg()
	--蛮荒古殿首次领取标记
	self.manhuang_first_reward_flag_list = {}
end
function ManHuangGuDianWGData:__delete()
	self.manhuang_first_reward_flag_list = nil
	self.base_info = nil
	self.rank_info = nil
	self.manhuang_other_cfg = nil
	self.manhuang_reward_cfg = nil
	ManHuangGuDianWGData.Instance = nil
end

function ManHuangGuDianWGData:InitManHuangGuDianCfg()
	local all_cfg = ConfigManager.Instance:GetAutoConfig("manhuanggudian_cfg_auto")
	--蛮荒古殿其他配置
	self.manhuang_other_cfg = all_cfg.other[1]
	--蛮荒古殿奖励配置
	self.manhuang_reward_cfg = all_cfg.reward
	--购买次数配置
	self.buy_times_cfg = ListToMap(all_cfg.buy_times, "buy_times")
	local count = 0
	for i, v in pairs(all_cfg.buy_times) do
		count = count + 1
	end
	self.max_buy_times = count

	--缓存有奖励的列表
	self.cache_reward_list = {}
	--总共可领取奖励的个数
	self.cache_reward_count = 0
	--最后可领取的波数
	self.final_wave = 1

	--初始化奖励展示
	self:InitRewardDataList()
	self:InitSceneRewardDataList()
end
function ManHuangGuDianWGData:InitSceneRewardDataList()
	local data_list = {}
	for i = 1, #self.manhuang_reward_cfg do
		local data = {}
		data.wave = self.manhuang_reward_cfg[i].wave
		data.item_list = {}
		local reward = self.manhuang_reward_cfg[i].client_show_reward
		for i = 0, 10 do
			if reward[i] then
				table.insert(data.item_list, reward[i])
			else
				break
			end
		end
		table.insert(data_list, data)
	end
	self.scene_reward_data_list = data_list
end

function ManHuangGuDianWGData:GetSceneRewardDataList()
	return self.scene_reward_data_list
end


function ManHuangGuDianWGData:InitRewardDataList()
	local data_list = {}
	for i = 1, #self.manhuang_reward_cfg do
		local reward = self.manhuang_reward_cfg[i].reward_item
		if reward[0] then
			local data = {}
			data.wave = self.manhuang_reward_cfg[i].wave
			data.item_list = {}
			--data.item_list
			for j = 0, 10 do
				if reward[j] then
					table.insert(data.item_list, reward[j])
				else
					break
				end
			end

			self.cache_reward_count = self.cache_reward_count + 1
			self.cache_reward_list[data.wave] = self.cache_reward_count
			self.final_wave = data.wave
			table.insert(data_list, data)
		end
	end
	self.reward_data_list = data_list
end

function ManHuangGuDianWGData:GetRewardDataList()
	return self.reward_data_list
end

function ManHuangGuDianWGData:GetCfgByWave(wave)
	return self.manhuang_reward_cfg[wave]
end


--设置蛮荒古殿每天已进入次数
function ManHuangGuDianWGData:SetManHuangEnterTimes(times)
	self.manhuang_enter_times = times
end
--获取蛮荒古殿每天已进入次数
function ManHuangGuDianWGData:GetManHuangEnterTimes()
	return self.manhuang_enter_times
end
--设置蛮荒古殿每天已购买次数
function ManHuangGuDianWGData:SetManHuangBuyTimes(times)
	self.manhuang_buy_times = times
end
--获取蛮荒古殿每天已购买次数
function ManHuangGuDianWGData:GetManHuangBuyTimes()
	return self.manhuang_buy_times
end
--设置蛮荒古殿每天已协助次数
function ManHuangGuDianWGData:SetManHuangXieZhuTimes(times)
	self.manhuang_xiezhu_times = times
end
--获取蛮荒古殿每天已协助次数
function ManHuangGuDianWGData:GetManHuangXieZhuTimes()
	return self.manhuang_xiezhu_times
end


--获取蛮荒古殿每天进入总次数
function ManHuangGuDianWGData:GetManHuangTotalEnterTimes()
	return self.manhuang_other_cfg.everyday_times
end

function ManHuangGuDianWGData:GetTotalTimes()
	local enter_times = self:GetManHuangEnterTimes()
	local buy_times = self:GetManHuangBuyTimes()
	local day_total_times = self:GetManHuangTotalEnterTimes()
	return day_total_times + buy_times
end
function ManHuangGuDianWGData:GetRemainTimes()
	local enter_times = self:GetManHuangEnterTimes()
	local buy_times = self:GetManHuangBuyTimes()
	local day_total_times = self:GetManHuangTotalEnterTimes()
	return day_total_times + buy_times - enter_times
end

--获取蛮荒古殿每天协助奖励总次数
function ManHuangGuDianWGData:GetManHuangTotalXieZhuTimes()
	return self.manhuang_other_cfg.help_times
end
--获取蛮荒古殿协助奖励荣誉
function ManHuangGuDianWGData:GetManHuangHelpHonor()
	return self.manhuang_other_cfg.help_honor
end
--获取蛮荒古殿波数奖励
function ManHuangGuDianWGData:GetManHuangItemReward()
	if self.manhuang_show_reward_item then
		return self.manhuang_show_reward_item
	end
	local reward_cfg = self.manhuang_other_cfg.show_reward_item
	local data_list = {}
	for i = 0, 10 do
		if reward_cfg[i] then
			reward_cfg[i].show_duobei = true
			reward_cfg[i].task_type = RATSDUOBEI_TASK.MANHUANGUDIAN
			table.insert(data_list, reward_cfg[i])
		else
			break
		end
	end
	if not self.manhuang_show_reward_item then
		self.manhuang_show_reward_item = data_list
	end
	return data_list
end
--获取蛮荒古殿购买次数消耗元宝
function ManHuangGuDianWGData:GetManHuangBuyComsumeGold()
	return self.manhuang_other_cfg.buy_times_need_gold
end


--设置蛮荒古殿基本信息
function ManHuangGuDianWGData:SetManHuangBaseInfo(protocol)
	self.base_info = {}
	self.base_info.manhuang_max_wave = protocol.max_wave
	self.base_info.manhuang_pass_time = protocol.max_wave_pass_time
	self.base_info.manhuang_first_reward_flag_list = {}
	--protocol.first_reward_flag_list
	local bit_list = {}
 	for i,v in ipairs(protocol.first_reward_flag_list) do
 		bit_list = bit:d2b(v, bit_list)
 		for ii=32,25,-1 do
 			table.insert(self.base_info.manhuang_first_reward_flag_list, bit_list[ii])
 		end
 	end

	--第一个标记无用，第一个标记是第0波，不会用到
	table.remove(self.base_info.manhuang_first_reward_flag_list, 1)
end
--获取蛮荒古殿基本信息
function ManHuangGuDianWGData:GetManHuangBaseInfo()
	return self.base_info
end
--获取蛮荒古殿当前通关波数
function ManHuangGuDianWGData:GetManHuangPassMaxWave()
	if not self.base_info then
		return 0
	end
	return self.base_info.manhuang_max_wave
end
--获取蛮荒古殿当前通关时间
function ManHuangGuDianWGData:GetManHuangPassTime()
	if not self.base_info then
		return 0
	end
	return self.base_info.manhuang_pass_time
end
--获取蛮荒古殿首通领取标记
function ManHuangGuDianWGData:GetManHuangFirstRewardFlagList()
	return self.base_info.manhuang_first_reward_flag_list
end

function ManHuangGuDianWGData:GetJumpIndex()
	local jump_index = 1
	local base_info = self:GetManHuangBaseInfo()
    if not base_info then return jump_index end
    local record_index = 0
    local has_can_fetch = false
	local has_find_next = false
    local flag_list = base_info.manhuang_first_reward_flag_list
	for i, v in ipairs(flag_list) do
		--找到第一个可领取的
		if i <= base_info.manhuang_max_wave and v == MANHUANGGUDIAN_FLAG.NOT_FETCH and self.cache_reward_list[i] then
			jump_index = self.cache_reward_list[i]
			has_can_fetch = true
			break
		end

		--没有可领取，并且还没达到最大通关波数
		--如果没有可领取档位了，则定位到即将达到的那1档
		if i > base_info.manhuang_max_wave and v == MANHUANGGUDIAN_FLAG.NOT_FETCH and self.cache_reward_list[i] then
			jump_index = self.cache_reward_list[i]
			has_find_next = true
			break
		end

		--记录以领取的数量
		if v == MANHUANGGUDIAN_FLAG.FETCH and self.cache_reward_list[i] then
			record_index = record_index + 1
		end
    end

    if not has_can_fetch and not has_find_next then
        if record_index == self.cache_reward_count then
            --全部领完
            jump_index = self.final_wave
        end
    end
	jump_index =  MathClamp(jump_index, 1, #self.manhuang_reward_cfg)
	return jump_index
end


function ManHuangGuDianWGData:GetIsShowRedPoint()
	local base_info = self:GetManHuangBaseInfo()
    if not base_info then return false end

	if self:GetRemainTimes() > 0 then
		return true
	end

	return self:GetIsCanFetch()
end

function ManHuangGuDianWGData:GetIsCanFetch()
	local base_info = self:GetManHuangBaseInfo()
    if not base_info then return false end
	local flag_list = self:GetManHuangFirstRewardFlagList()
    for i, v in ipairs(flag_list) do
        if i <= base_info.manhuang_max_wave and v == MANHUANGGUDIAN_FLAG.NOT_FETCH and self.cache_reward_list[i] then
            return true
        end
    end
	return false
end



--设置蛮荒古殿排行信息
function ManHuangGuDianWGData:SetManHuangRankInfo(protocol)
	self.rank_info = {}
	self.rank_info.self_rank = protocol.self_rank
	self.rank_info.rank_count = protocol.rank_count
	self.rank_info.rank_list = protocol.rank_list
end

--获取蛮荒古殿排行信息
function ManHuangGuDianWGData:GetManHuangRankInfo()
	return self.rank_info
end

--获取蛮荒古殿显示列表
function ManHuangGuDianWGData:GetManHuangRankShowList()
    local show_num = 11      -- 为显示列表最小显示数量
    local show_table = {}

    for i = 1, show_num do
        show_table[i] = {}
    end

    if self.rank_info then
        local rank_list = self.rank_info.rank_list
        if self.rank_info.rank_count > show_num then
            return rank_list
        else
            for i = 1, show_num do
                show_table[i] = rank_list[i] or {}
            end
        end
    end

    return show_table,self.rank_info.rank_count
end

--获取蛮荒古殿当前通关时间
function ManHuangGuDianWGData:GetManHuangRankCount()
	if not self.rank_info then
		return 0
	end
	return self.rank_info.rank_count
end
--获取蛮荒古殿排行信息
function ManHuangGuDianWGData:GetManHuangRankList()
	if not self.rank_info then
		return {}
	end
	return self.rank_info.rank_list
end

--设置蛮荒古殿排行信息
function ManHuangGuDianWGData:SetManHuangFBSceneInfo(protocol)
	self.scene_info = {}
	self.scene_info.is_finish = protocol.is_finish						--是否结束
	self.scene_info.is_pass = protocol.is_pass							--是否通关
	self.scene_info.curr_wave_index = protocol.curr_wave_index					--当前波索引
	self.scene_info.is_helper = protocol.is_helper							--是否助战
	self.scene_info.prepare_end_timestamp = protocol.prepare_end_timestamp			--副本准备状态结束时间戳
	self.scene_info.finish_timestamp = protocol.finish_timestamp					--副本结束时间戳
	self.scene_info.next_wave_refresh_timestamp = protocol.next_wave_refresh_timestamp		--刷怪时间戳
	self.scene_info.kick_out_timestamp = protocol.kick_out_timestamp					--延迟踢出时间戳
	self.scene_info.pass_time_s = protocol.pass_time_s							--进入副本到目前经过的时间
	self.scene_info.kill_monster_num = protocol.kill_monster_num 					--当前波已被击杀的数量
	self.scene_info.cur_wave_monster_num = protocol.cur_wave_monster_num 				--这一波总的数量
	self.scene_info.reward_item_count = protocol.reward_item_count 					--奖励物品数量
	self.scene_info.reward_item_list = protocol.reward_item_list
end
function ManHuangGuDianWGData:GetSceneInfo()
	return self.scene_info
end

function ManHuangGuDianWGData:GetBuyTimesComsumeGold(will_buy_times)
	if self.buy_times_cfg[will_buy_times] then
		return self.buy_times_cfg[will_buy_times].price
	end
	if will_buy_times > self.max_buy_times then
		return self.buy_times_cfg[self.max_buy_times].price
	end
	return self.buy_times_cfg[1].price
end

function ManHuangGuDianWGData:GetManHuangGuDianOtherCfg()
	return self.manhuang_other_cfg or {}
end