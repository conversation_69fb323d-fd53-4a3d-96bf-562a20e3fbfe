function QuanMinBeiZhanView:InitServerCap()
	local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_cap)
	self.node_list.cap_server_word1.image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath(theme_cfg.word3))
	self.node_list.cap_server_word1.image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath(theme_cfg.word4))

	local theme_other = QuanMinBeiZhanWGData.Instance:GetActivityThemeOtherCfg()
	self.node_list.cap_server_buff_icon.image:LoadSprite(ResPath.GetBuff(theme_other[1].effect_type))
	self.node_list.cap_server_buff_name.text.text = theme_other[1].buff_name

	self.is_servercap_init = true
	self:FluahServerCap()
	self.node_list.cap_server_cap.text.text = string.format(Language.QuanMinBeiZhan.CapStr2 ,QuanMinBeiZhanWGData.Instance:GetServerCap())
end

function QuanMinBeiZhanView:ReleaseRoleServerCap()
	if self.server_cap_reward_list then
		self.server_cap_reward_list:DeleteMe()
		self.server_cap_reward_list = nil
	end
	self.is_servercap_init = nil
end

function QuanMinBeiZhanView:FluahServerCap()
	if self.is_servercap_init == nil then
		return
	end

	local list = QuanMinBeiZhanWGData.Instance:GetServerCapList()
	if list ~= nil then
		if not self.server_cap_reward_list then
			self.server_cap_reward_list = AsyncListView.New(BZServerCapItemRender, self.node_list.cap_server_reward_list)
		end
		self.server_cap_reward_list:SetDataList(list)
	end

	self:FlushBuff()
end

function QuanMinBeiZhanView:FlushBuff()
	local buff_cfg = QuanMinBeiZhanWGData.Instance:GetBuffCfg()
	local buff_level = QuanMinBeiZhanWGData.Instance:GetBuffLevel()
	local show_level = buff_level

	self.node_list.cap_server_preview:SetActive(false)
	if buff_level == 0 then
		show_level = 1
		self.node_list.cap_server_target_cap.text.text = string.format(Language.QuanMinBeiZhan.CapStr3, buff_cfg[1].zhanli_need)
	else
		local buff_max_level = #buff_cfg
		local is_full = buff_level == buff_max_level
		if is_full then
			show_level = buff_max_level
			self.node_list.cap_server_target_cap.text.text = Language.QuanMinBeiZhan.CapStr5
		else
			self.node_list.cap_server_preview:SetActive(true)
			self.node_list.cap_server_target_cap.text.text = string.format(Language.QuanMinBeiZhan.CapStr4, buff_cfg[show_level +1].zhanli_need)

			self.node_list.cap_server_cur_tg_label.text.text = string.format("%s%%",  math.floor(buff_cfg[show_level].add_param1 / 100))
			self.node_list.cap_server_next_tg_label.text.text = string.format("%s%%",  math.floor(buff_cfg[show_level+1].add_param1 / 100))
			self.node_list.cap_server_cur_jm_label.text.text = string.format("%s%%",  math.floor(buff_cfg[show_level].add_param_2 / 100))
			self.node_list.cap_server_next_jm_label.text.text = string.format("%s%%",  math.floor(buff_cfg[show_level+1].add_param_2 / 100))
		end
	end
	self.node_list.cap_server_buff_desc.text.text = string.format(buff_cfg[show_level].buff_desc, math.floor(buff_cfg[show_level].add_param1 / 100), math.floor(buff_cfg[show_level].add_param_2 / 100)) 
end

-----------------------------------------------
BZServerCapItemRender = BZServerCapItemRender or BaseClass(BaseRender)
function BZServerCapItemRender:__init()
	self.max_item_count = 4
end

function BZServerCapItemRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnLingQuBtnClick, self))
end

function BZServerCapItemRender:__delete()
	if self.cell_list ~= nil then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
			v = nil
		end
	end
end

function BZServerCapItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function BZServerCapItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.btn_wdc:SetActive(self.data.state == ActivityRewardState.BKL)
	self.node_list.btn_lingqu:SetActive(self.data.state == ActivityRewardState.KLQ)
	self.node_list.yilingqu:SetActive(self.data.state == ActivityRewardState.YLQ)

	local server_cap = QuanMinBeiZhanWGData.Instance:GetServerCap()
	if server_cap >= self.data.cfg.zhanli_need then
		self.node_list.name.text.text = string.format(Language.QuanMinBeiZhan.CapStr10, self.data.cfg.zhanli_need)
	else
		self.node_list.name.text.text = string.format(Language.QuanMinBeiZhan.CapStr13, self.data.cfg.zhanli_need)
	end

	local list = {}
	for k,v in pairs(self.data.cfg.reward_item) do
		table.insert(list, v)
	end

	if self.cell_list == nil then
		self.cell_list = {}
		for i = 1, self.max_item_count do
			self.cell_list[i] = QuanMinCommonItemRender.New(self.node_list["ph_cell_"..i].gameObject)
		end
	end
	
	local len = #list
	for i = 1, self.max_item_count do
		if i <= len then
			self.node_list["ph_cell_"..i]:SetActive(true)
			self.cell_list[i]:SetItemData(list[i])
		else
			self.node_list["ph_cell_"..i]:SetActive(false)
		end
	end
end

function BZServerCapItemRender:OnLingQuBtnClick()
	if self.data.state == ActivityRewardState.KLQ then
		QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_ZHANLIBIPIN, ZHANLIBIPIN_OP_TYPE.TYPE_SERVER_REWARD, self.data.cfg.reward_id)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.QuanMinBeiZhan.CapStr6)
	end
end
