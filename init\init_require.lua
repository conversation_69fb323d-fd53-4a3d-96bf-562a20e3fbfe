local InitRequire = {
	ctrl_state = CTRL_STATE.START,
	require_list = {},
	require_count = 0,
	require_index = 0,
}
function InitRequire:Start()
	-- 获取基础的require列表.
	self.require_list = require("game/common/require_list")

	-- 创建渠道匹配器, 如果这个列表里面没有则使用默认的.
	local agentTable = {
		["pev"] = "agent/dev/agent_adapter",
		["dev"] = "agent/dev/agent_adapter",
		["its"] = "agent/dev/agent_adapter",
		["dem"] = "agent/dev/agent_adapter",
		["iem"] = "agent/dev/agent_adapter"
	}

	local agentPath = agentTable[CHANNEL_AGENT_ID]
	if agentPath ~= nil then
		table.insert(self.require_list, agentPath)
	elseif UNITY_EDITOR then
		table.insert(self.require_list, "agent/dev/agent_adapter")
	else
		table.insert(self.require_list, "agent/agent_adapter")
	end

	self.require_count = #self.require_list
	InitWGCtrl:SetText("正在读取资源(此过程不消耗流量)，请您耐心等待")
	-- ReportManager:Step(Report.STEP_REQUIRE_START)

	local flag = UnityEngine.PlayerPrefs.GetInt("START_CHECK_FUNTION_COST_MEM", 0)
	if 1 == flag then
		IS_CHECK_FUNTION_COST_MEM = true
	end

	if IS_CHECK_FUNTION_COST_MEM then
		CheckFuntionUseMem:StartCollectMem()
	end

	flag = UnityEngine.PlayerPrefs.GetInt("COLLECT_GRAPHIC_MEM", 0)
	if 1 == flag then
		GameRoot.SetIsCollectGraphicMem(true)
	end

	GameRoot.SetIsLowMemColllect(true)
	GameRoot.SetIsChangeSceneColllect(true, 5)
end

function InitRequire:Update(now_time, elapse_time)
	if self.ctrl_state == CTRL_STATE.UPDATE then
		local end_index = self.require_index + 12
		for i = self.require_index + 1, end_index do
			self.require_index = i
			if nil == self.require_list[i] then
				-- ReportManager:Step(Report.STEP_REQUIRE_END)
				self.ctrl_state = CTRL_STATE.STOP
				InitWGCtrl:SetPercent(0.6, function()
					InitWGCtrl:OnCompleteRequire()
				end)
				return
			else
				local path = self.require_list[self.require_index]
				if string.match(path, "^config/auto_new/.*") then
					CheckLuaConfig(path, require(path))
				else
					require(path)
				end
			end
		end
		InitWGCtrl:SetPercent(self.require_index / self.require_count * 0.6)
	elseif self.ctrl_state == CTRL_STATE.START then
		self.ctrl_state = CTRL_STATE.UPDATE
		self:Start()
	elseif self.ctrl_state == CTRL_STATE.STOP then
		self.ctrl_state = CTRL_STATE.NONE
		self:Stop()
		PopCtrl(self)
	end
end

function InitRequire:Stop()
	GameRoot.Instance:PruneLuaBundles()
end

return InitRequire