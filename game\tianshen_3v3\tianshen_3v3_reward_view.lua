TianShen3v3RewardView = TianShen3v3RewardView or BaseClass(SafeBaseView)

function TianShen3v3RewardView:__init()
    self:SetMaskBg()
	self:LoadConfig()
    self.default_index = TabIndex.tianshen_3v3_grade_reward
end

function TianShen3v3RewardView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_reward")
end

function TianShen3v3RewardView:ReleaseCallBack()
    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end
	if self.duanwei_reward_list then
		self.duanwei_reward_list:DeleteMe()
		self.duanwei_reward_list = nil
	end
end

function TianShen3v3RewardView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.TianShen3v3.ViewName_SeasonReward
	self:SetSecondView(nil, self.node_list["size"])
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.TianShen3v3.RewardTabGroup, nil, "uis/view/tianshen_3v3_ui_prefab")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:SetInstanceParent(self.node_list.TabbarContainer)
		self.tabbar:SetLocalPosition(0,0,0)
	end
	self.rank_reward_list = AsyncListView.New(TianShen3v3RankRewardItem, self.node_list["rank_reward_list"])
	self.duanwei_reward_list = AsyncListView.New(TianShen3v3StageRewardItem, self.node_list["duanwei_reward_list"])
end

function TianShen3v3RewardView:ShowIndexCallBack(index)
	self:Flush(index)
end

function TianShen3v3RewardView:OnFlush()
	local index = self:GetShowIndex()
	local data_list = {}
	local need_joint_count = 0
	if index == TabIndex.tianshen_3v3_rank_reward then
		self.node_list["rank_reward_list"]:SetActive(true)
		self.node_list["duanwei_reward_list"]:SetActive(false)
		data_list = TianShen3v3WGData.Instance:GetRankRewardCfg()
		self.rank_reward_list:SetDataList(data_list)

		 -- 领取奖励所需参加次数
		local need_join_count = TianShen3v3WGData.Instance:GetOtherCfg().enter_list_need_join_times
		local join_time = TianShen3v3WGData.Instance:GetJoinTimes()
		local color = join_time > 0 and COLOR3B.RED or COLOR3B.GREEN
		local str = ToColorStr(join_time .. "/" .. need_join_count, color)
		self.node_list["rank_condition"].text.text = string.format(Language.TianShen3v3.RankNeedJoinCondition, str)
		self.node_list["rank_condition"]:SetActive(true)
	elseif index == TabIndex.tianshen_3v3_grade_reward then
		self.node_list["duanwei_reward_list"]:SetActive(true)
		self.node_list["rank_reward_list"]:SetActive(false)
		local cfg_list = TianShen3v3WGData.Instance:GetGradeRewardCfg()
		for i = #cfg_list, 1, -1 do
			table.insert(data_list, cfg_list[i])
		end
		self.duanwei_reward_list:SetDataList(data_list)
		self.node_list["rank_condition"]:SetActive(false)

		-- local cur_reward_cfg = KF3V3WGData.Instance:GetCurDuanWeiReward()
		-- if cur_reward_cfg then
		-- 	need_joint_count = cur_reward_cfg.need_match_times 				-- 领取奖励所需参加次数
		-- end
	end

	-- --已参赛text
	-- local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
	-- local joint_count = role_info.season_match_times
	-- local change_str = string.format(Language.KuafuPVP.RewardchangCount, joint_count, need_joint_count)
	-- change_str = ToColorStr(change_str, joint_count >= need_joint_count and COLOR3B.GREEN or COLOR3B.RED)
	-- self.node_list.join_count.text.text = string.format(Language.KuafuPVP.Rewardchang, change_str)

	-- --结算时间text
	-- if index == TabIndex.tianshen_3v3_rank_reward then
	-- 	self.node_list.season_rank_reward_time.text.text = string.format(Language.KuafuPVP.SeasonRankRewardTimeTip, TimeUtil.FormatYMDH(role_info.season_rank_reward_time))
	-- elseif index == TabIndex.tianshen_3v3_grade_reward then
	-- 	self.node_list.season_rank_reward_time.text.text = string.format(Language.KuafuPVP.SeasonDuanWeiRewardTimeTip, TimeUtil.FormatYMDH(role_info.season_rank_reward_time))
	-- end

end


------------------------------- 排名奖励item ----------------------------------------
TianShen3v3RankRewardItem = TianShen3v3RankRewardItem or BaseClass(BaseRender)
function TianShen3v3RankRewardItem:__init()
	self.item_list = AsyncListView.New(TianShen3v3RewardItemCell, self.node_list.item_list)
end

function TianShen3v3RankRewardItem:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function TianShen3v3RankRewardItem:OnFlush()
	--奖励物品
	local data_list = {}
	for i = 0, #self.data.reward_item do
		table.insert(data_list, self.data.reward_item[i])
	end
	self.item_list:SetDataList(data_list)

	-- 背景
	self.node_list.bg:SetActive(self.index % 2 == 1)

	-- 排名
	self.node_list["rank_text"]:SetActive(false)
	self.node_list["rank_img"]:SetActive(false)
	if self.data.lower_rank == self.data.upper_rank then
		local rank = self.data.lower_rank
		if rank <= 3 then
			self.node_list["rank_img"]:SetActive(true)
			self.node_list["rank_img"].image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. rank))
		else
			self.node_list["rank_text"]:SetActive(true)
			self.node_list["rank_text"].text.text = rank
		end
	else
		self.node_list["rank_text"]:SetActive(true)
		self.node_list["rank_text"].text.text = string.format(Language.TianShen3v3.RankRnage, self.data.lower_rank, self.data.upper_rank)
	end

	--参与场次要求
	-- self.node_list["join_count"].text.text = string.format(Language.TianShen3v3.JoinCount, self.data.need_match_times)
end

------------------------------- 段位奖励item ----------------------------------------
TianShen3v3StageRewardItem = TianShen3v3StageRewardItem or BaseClass(BaseRender)
function TianShen3v3StageRewardItem:__init()
	self.item_list = AsyncListView.New(TianShen3v3RewardItemCell, self.node_list.item_list)
end

function TianShen3v3StageRewardItem:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function TianShen3v3StageRewardItem:OnFlush()
	--奖励物品
	local data_list = {}
	for i = 0, #self.data.reward_item do
		table.insert(data_list, self.data.reward_item[i])
	end
	self.item_list:SetDataList(data_list, 0)

	-- 背景
	self.node_list["bg"]:SetActive(self.index % 2 == 1)

	-- 段位名称
	self.node_list["duanwei_text"].text.text = self.data.duanwei_name
	ChangeToQualityText(self.node_list["duanwei_text"], RankGradeEnum[self.data.grade])

	-- 段位图标
	self.node_list["duanwei_img"].image:LoadSprite(ResPath.GetTianShen3v3Img("duanwei_icon" .. self.data.grade))

	--参与场次要求
	self.node_list["join_count"].text.text = string.format(Language.TianShen3v3.JoinCount, self.data.need_match_times)
end

------------------------------- 单个物品格子 ----------------------------------------
TianShen3v3RewardItemCell = TianShen3v3RewardItemCell or BaseClass(BaseRender)
function TianShen3v3RewardItemCell:__init()
	self.cell = ItemCell.New(self.node_list.pos)
end

function TianShen3v3RewardItemCell:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function TianShen3v3RewardItemCell:OnFlush()
	self.cell:SetData(self.data)
end