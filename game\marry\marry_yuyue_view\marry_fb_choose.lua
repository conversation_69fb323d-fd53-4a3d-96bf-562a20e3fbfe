MarryFbChooseView = MarryFbChooseView or BaseClass(SafeBaseView)

function MarryFbChooseView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg()
    self.view_layer = UiLayer.Pop
    self.active_close = false --断线重连会被CloseAll 关闭
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_fb_choose")
end

function MarryFbChooseView:__delete()

end

function MarryFbChooseView:ReleaseCallBack()
	self.choose_index = 0
end

function MarryFbChooseView:LoadCallBack()
	for i = 1, 3 do
		self.node_list["btn_choose_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickChooseHandler, self, i))
	end
	self.node_list["lbl_yes"]:SetActive(false)
end

function MarryFbChooseView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("choose_fb_timer") then
		CountDownManager.Instance:RemoveCountDown("choose_fb_timer")
	end
end

function MarryFbChooseView:ShowIndexCallBack()
	self.choose_index = 0
	local scene_info = MarryWGData.Instance:GetQyFbSceneInfo()
	if scene_info then
		local time = scene_info.choose_end_timestamp - TimeWGCtrl.Instance:GetServerTime()
		if time > 0 then
			self:UpdateChooseCloseTime(TimeWGCtrl.Instance:GetServerTime(), scene_info.choose_end_timestamp)
			CountDownManager.Instance:AddCountDown("choose_fb_timer", BindTool.Bind1(self.UpdateChooseCloseTime, self), BindTool.Bind1(self.CompleteChooseTime, self), scene_info.choose_end_timestamp, nil, 0.1)
		end
	end
	for i = 1, 3 do
		self.node_list["img_choose_flag_" .. i]:SetActive(false)
	end
end

function MarryFbChooseView:OnFlush()
    local scene_info = MarryWGData.Instance:GetQyFbSceneInfo()
    local marry_cfg = MarryWGData.Instance:GetMarryOtherCfg()
    if scene_info then     
        for i = 1, 3 do
            if self.node_list["img_flag_" .. i] then
                --self.node_list["img_flag_" .. i].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("jh_chahua" .. scene_info.rand_num_list[i - 1]))
                self.node_list["name_text_" .. i].text.text = marry_cfg["linxi_name"..scene_info.rand_num_list[i - 1]]
                self.node_list["hl_text_" .. i].text.text = marry_cfg["linxi_name"..scene_info.rand_num_list[i - 1]]
            end
        end
	end
end

function MarryFbChooseView:UpdateChooseCloseTime(elapse_time, total_time)
	local time = 0
	local scene_info = MarryWGData.Instance:GetQyFbSceneInfo()
	if scene_info then
		time = scene_info.choose_end_timestamp - TimeWGCtrl.Instance:GetServerTime()
	end
	if time > 0 then
		local end_time = TimeUtil.Format2TableDHMS(time)
		self.node_list["lbl_choose_endtime"].text.text = (string.format(Language.Marry.MarryChooseTime, end_time.s))
	end
end

function MarryFbChooseView:CompleteChooseTime()
	if self.choose_index == 0 then
		MarryWGCtrl.Instance:SendCSQingYuanFBChooseInfo(self.choose_index)
	end

	self:Close()
end

function MarryFbChooseView:OnClickChooseHandler(index)
	local scene_info = MarryWGData.Instance:GetQyFbSceneInfo()
	self.choose_index = index

	for i = 1, 3 do
		self.node_list["btn_choose_" .. i].button.interactable = i == index
	end
	self.node_list["img_choose_flag_" .. self.choose_index]:SetActive(true)
	self.node_list["btn_choose_" .. self.choose_index]:SetActive(false)
	if scene_info then
		MarryWGCtrl.Instance:SendCSQingYuanFBChooseInfo(scene_info.rand_num_list[self.choose_index - 1])
		self.node_list["lbl_yes"]:SetActive(true)
	end
end