require("game/shennutianzhu/shennutianzhu_view")
require("game/shennutianzhu/shennutianzhu_wg_data")


ShenNuTianZhuCtrl = ShenNuTianZhuCtrl or BaseClass(BaseWGCtrl)

function ShenNuTianZhuCtrl:__init()
    if ShenNuTianZhuCtrl.Instance then
		error("[ShenNuTianZhuCtrl]:Attempt to create singleton twice!")
	end

    ShenNuTianZhuCtrl.Instance = self

    self.view = ShenNuTianZhuView.New(GuideModuleName.ShenNuTianZhuView)
    self.data = ShenNuTianZhuData.New()

    self:RegisterAllProtocols()
end

function ShenNuTianZhuCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    ShenNuTianZhuCtrl.Instance = nil
end

function ShenNuTianZhuCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOAShenNuTianZhuInfo, "OnSCOAShenNuTianZhuInfo")
end

function ShenNuTianZhuCtrl:OnSCOAShenNuTianZhuInfo(protocol)
	self.data:SetSntzBuyInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end
end