SiXiangCallWGData = SiXiangCallWGData or BaseClass()

MAX_DAOHUN_YUGAO_COUNT = 12
MAX_PERSON_DRAW_LOG = 100

local TenSummonTypeList = {
	OP_YSZH_DRAW_TYPE.MULTI,				-- 普通十连道具(需要九个 受四象类型限制)
	OP_YSZH_DRAW_TYPE.USE_MULTI_ITEM,		-- 特殊十连道具(1个即可 受四象类型限制)
	OP_YSZH_DRAW_TYPE.NOR_MULTI,			-- 通用十连道具(需要九个 不受四象类型限制)
	OP_YSZH_DRAW_TYPE.NOR_USE_MULTI_ITEM,	-- 通用特殊十连道具(1个即可 不受四象类型限制)
}

function SiXiangCallWGData:__init()
	if SiXiangCallWGData.Instance then
		error("[SiXiangCallWGData] Attempt to create singleton twice!")
		return
	end
	SiXiangCallWGData.Instance = self

	self:InitParam()
	self:InitSiXiangCfg()
    self:RegisterRemind()
end

function SiXiangCallWGData:__delete()
    self:UnRegisterRemind()
    SiXiangCallWGData.Instance = nil
end

function SiXiangCallWGData:InitParam()
	self.xianqi_best_reward_level = 4	-- 仙器大奖等级
	self.sx_summon_type = 0
	self.sixiang_result_list = {}
	self.sixiang_server_log = {}
	self.sixiang_person_log = {}
	self.sixiang_danmu_row = {}
	self.sx_summon_show_list = {}
	self.sx_shop_money_num = 0
	self.sx_danmu_index = 1
	self.cache_summon_info = 0
	self.cache_summon_item_data = {
		level = 0,
		star = 0,
		grade = 0,
		is_bind = 1,
		color = 4,
		item_id = 0,
		index = 0,
		fight_soul_type = 1,
	}
	self.cache_reward_pond_list = {}
	self.xianqi_server_log = {}
	self.xianqi_person_log = {}
	self.xianqi_result_list = {}

	--仙器真炼新增
	self.cache_xqzl_item_data = {
		level = 0,
		star = 0,
		grade = 0,
		is_bind = 1,
		color = 4,
		item_id = 0,
		index = 0,
		fight_soul_type = 1,
	}
	self.cache_xianqi_info = {}
end

function SiXiangCallWGData:InitSiXiangCfg()
	local sixiang_cfg = ConfigManager.Instance:GetAutoConfig("sixiang_system_cfg_auto")
	if sixiang_cfg then
		self.sixiang_other_cfg = sixiang_cfg.other and sixiang_cfg.other[1]
		self.sixiang_bone_cfg = ListToMap(sixiang_cfg.hungu_star, "hungu_id")
	end
	self.sixiang_cfg = sixiang_cfg

	local sixiang_summon_cfg = ConfigManager.Instance:GetAutoConfig("yuanshengzhaohuan_auto")
	if sixiang_summon_cfg then
		self.sixiang_summon_other_cfg = sixiang_summon_cfg.other and sixiang_summon_cfg.other[1]
		self.sixiang_reward_pond = ListToMapList(sixiang_summon_cfg.reward_pond_show, "sixiang_type")
		self.item_random_desc = ListToMapList(sixiang_summon_cfg.item_random_desc, "sixiang_type")
	end
	self.sixiang_summon_cfg = sixiang_summon_cfg

	--仙器真炼配置
	local xianqi_zhenlian_cfg = ConfigManager.Instance:GetAutoConfig("shenjibailian_auto")
	self.xqzl_other_cfg = xianqi_zhenlian_cfg.other[1]
	self.machine_special_cfg = ListToMap(xianqi_zhenlian_cfg.special_multi_lotto, "special_multi_lotto_group", "special_single_lotto_id")
	self.machine_tedian_cfg = ConfigManager.Instance:GetAutoConfig("shenjibailian_tedian_auto")
	self.machine_tedian_special_cfg = ListToMap(self.machine_tedian_cfg.special_multi_lotto, "cycle", "special_multi_lotto_group", "special_single_lotto_id")
	self.machine_tedian_reward_cfg = ListToMap(self.machine_tedian_cfg.reward, "cycle", "reward_id")
	self.xianqi_zhenlian_cfg = xianqi_zhenlian_cfg
end

function SiXiangCallWGData:RegisterRemind()
	RemindManager.Instance:Register(RemindName.SiXiangCall_SX, BindTool.Bind(self.SiXiangCallRemind, self))	-- 主界面召唤红点
	RemindManager.Instance:Register(RemindName.SiXiangCall_XQZL, BindTool.Bind(self.IsShowMachineRedPoint, self)) -- 仙器真炼
	RemindManager.Instance:Register(RemindName.SiXiangCall_SXSM, BindTool.Bind(self.SiXiangSummonRemind, self))	-- 四象召唤

	self:RegisterMachineRemindInBag(RemindName.SiXiangCall_XQZL)
	self:RegisterSXSMRemindInBag(RemindName.SiXiangCall_SXSM)
end

function SiXiangCallWGData:UnRegisterRemind()
	RemindManager.Instance:UnRegister(RemindName.SiXiangCall_SX)
	RemindManager.Instance:UnRegister(RemindName.SiXiangCall_XQZL)
	RemindManager.Instance:UnRegister(RemindName.SiXiangCall_SXSM)
end

-- 百炼召唤主界面红点
function SiXiangCallWGData:SiXiangCallRemind()
	return 0
end

-- 打乱奖励列表顺序
function SiXiangCallWGData:RandList(list)
	local new_list = {}
	local rand = 0
	for i = 1, #list do
		rand = GameMath.Rand(1, #list)
		table.insert(new_list, table.remove(list, rand))
	end
	return new_list
end

-------------------------     四象召唤      --------------------------------------

-- 四象其它配置
function SiXiangCallWGData:GetSiXiangOtherCfg(key)
	if self.sixiang_other_cfg and key then
		return self.sixiang_other_cfg[key]
	end
end

-- 四象召唤其它配置
function SiXiangCallWGData:GetSiXiangSummonOtherCfg(key)
	if self.sixiang_summon_other_cfg and key then
		return self.sixiang_summon_other_cfg[key]
	end
end

-- 四象界面配置
function SiXiangCallWGData:GetSummonInterfaceCfg()
	local summon_type = self:GetSiXiangSummonType()
	local cfg_list = self.sixiang_summon_cfg.summon_interface
	if cfg_list then
		return cfg_list[summon_type]
	end
end

-- 魂骨配置
function SiXiangCallWGData:GetHunGuCfg(item_id)
	if self.sixiang_bone_cfg then
		return self.sixiang_bone_cfg[item_id]
	end
end

-- 奖池展示
function SiXiangCallWGData:GetRewardPondShowCfg()
	if self.sixiang_reward_pond then
		local summon_type = self:GetSiXiangSummonType()
		return self.sixiang_reward_pond[summon_type]
	end
end

-- 四象物品配置(魂晶和魂骨)
function SiXiangCallWGData:GetSiXiangItemCfg(item_id)
	local hunjing_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(item_id)
	if hunjing_cfg then
		return hunjing_cfg, FIGHT_SOUL_ITEM_TYPE.SOUL
	end
	local hungu_cfg = SiXiangCallWGData.Instance:GetHunGuCfg(item_id)
	if hungu_cfg then
		return hungu_cfg, FIGHT_SOUL_ITEM_TYPE.BONE
	end
end

-- 四象召唤兑换物品列表
function SiXiangCallWGData:GetExchangeDataList()
	if self.sixiang_summon_cfg then
		return self.sixiang_summon_cfg.exchange or {}
	end
end

-- 四象召唤展示道具
function SiXiangCallWGData:GetSiXiangSummonItemList()
	local summon_type = self:GetSiXiangSummonType()
	if not self.sx_summon_show_list[summon_type] then
		local interfacecfg = self:GetSummonInterfaceCfg()
		local show_str = interfacecfg.item_list
	    local item_id_list = Split(show_str or "", ",")
	    local item_list = {}
	    for i=1,#item_id_list do
	    	item_list[i] = {item_data = {item_id = tonumber(item_id_list[i]), star = 5,}}
	    end
	    self.sx_summon_show_list[summon_type] = item_list
	end
	return self.sx_summon_show_list[summon_type]
end

-- 四象召唤类型 1青龙 2白虎 3朱雀 4玄武
function SiXiangCallWGData:GetSiXiangSummonType()
	if self.sx_summon_type <= 0 then
		local summon_type = RoleWGData.GetRolePlayerPrefsInt("sx_summon_type")
		self.sx_summon_type = summon_type
		if not summon_type or summon_type <= 0 then
			summon_type = 1
			self:SetSiXiangSummonType(summon_type)
		end
	end
	return self.sx_summon_type
end

function SiXiangCallWGData:SetSiXiangSummonType(summon_type)
	self.sx_summon_type = summon_type or 0
	RoleWGData.SetRolePlayerPrefsInt("sx_summon_type", summon_type)
	GlobalEventSystem:Fire(MainUIEventType.SIXIANG_ICON_TIYE_CHANGE)
end

-- 四象召唤消耗道具
function SiXiangCallWGData:GetSiXiangSummonConst(draw_type)
	local summon_type = self:GetSiXiangSummonType()
	local const_list = self.sixiang_summon_cfg and self.sixiang_summon_cfg.new_consume
	local const_item = nil
	if const_list and const_list[summon_type] then
		if draw_type == OP_YSZH_DRAW_TYPE.SINGLE then
			const_item = const_list[summon_type].single_lotto_consume
		elseif draw_type == OP_YSZH_DRAW_TYPE.MULTI then
			const_item = const_list[summon_type].multi_lotto_consume
		elseif draw_type == OP_YSZH_DRAW_TYPE.USE_MULTI_ITEM then
			const_item = const_list[summon_type].multi_lotto_spconsume
		elseif draw_type == OP_YSZH_DRAW_TYPE.NOR_SINGLE then
			const_item = const_list[summon_type].single_public_consume
		elseif draw_type == OP_YSZH_DRAW_TYPE.NOR_MULTI then
			const_item = const_list[summon_type].multi_public_consume
		elseif draw_type == OP_YSZH_DRAW_TYPE.NOR_USE_MULTI_ITEM then
			const_item = const_list[summon_type].multi_public_spconsume
		end
		if const_item then
			const_item.draw_type = draw_type
		end
	end
	return const_item
end

-- 四象召唤点击弹tip的数据格式
function SiXiangCallWGData:GetSiXiangSummonItemData()
	return self.cache_summon_item_data
end

function SiXiangCallWGData:GetSummonItemDataByItemId(item_id)
	local sixiang_cfg = SiXiangCallWGData.Instance:GetSiXiangItemCfg(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local item_data = SiXiangCallWGData.Instance:GetSiXiangSummonItemData()
	if sixiang_cfg and item_cfg then
		item_data.item_id = item_id
		item_data.star = sixiang_cfg.base_star or 0
		item_data.color = item_cfg.color or 1
		item_data.fight_soul_type = sixiang_cfg.sixiang_type or 1
		item_data.is_bind = item_cfg.isbind or 1
	end
    return item_data
end

---[[ 四象召唤后端数据
function SiXiangCallWGData:SetSiXiangSummonInfo(protocol)
    self.sixiang_server_log = protocol.server_draw_log
    self.sixiang_person_log = protocol.person_draw_log
	self.sixiang_shop_money_num = protocol.convert_shop_money_num
end

function SiXiangCallWGData:SetSiXiangShopMoneyNum(protocol)
	self.sixiang_shop_money_num = protocol.convert_shop_money_num
end

function SiXiangCallWGData:AddSiXiangServerLog(protocol)
	local sever_log = self.sixiang_server_log
	for i,v in ipairs(protocol.server_draw_log) do
		sever_log[#sever_log + 1] = v
	end
	self.sixiang_server_log = sever_log
end

--四象召唤对应日志信息
function SiXiangCallWGData:GetSiXiangCallRecordData(log_type)
	local log_list = {}
	if log_type == SiXiangRecordType.SELF then
		log_list = self:GetSiXiangSummonPersonLog()
	elseif log_type == SiXiangRecordType.ALL then
		log_list = self:GetSiXiangSummonServerLog()
	end
	return log_list
end

--仙器百炼日志
function SiXiangCallWGData:GetShenJiTianCiRecordData(log_type)
	local log_list = {}
	if log_type == SiXiangRecordType.SELF then
		log_list = self.xianqi_person_log
	elseif log_type == SiXiangRecordType.ALL then
		log_list = self.xianqi_server_log
	end
	return log_list
end

-- 全服召唤日志
function SiXiangCallWGData:GetSiXiangSummonServerLog()
	return self.sixiang_server_log
end

-- 个人召唤日志
function SiXiangCallWGData:GetSiXiangSummonPersonLog()
	return self.sixiang_person_log
end

-- 四象兑换货币
function SiXiangCallWGData:GetSiXiangShopMoneyNum()
	return self.sixiang_shop_money_num or 0
end
--]]

---[[ 四象召唤获得
function SiXiangCallWGData:SetSiXiangSummonReward(protocol)
	self.reward_cycle = protocol.cycle
    self.sixiang_draw_type = protocol.draw_type
    self.sixiang_draw_count = protocol.reward_count
    self.sixiang_result_list = protocol.reward_info
end

function SiXiangCallWGData:GetSiXiangSummonReward()
	return self.sixiang_result_list
end

function SiXiangCallWGData:SetCacheSummonInfo(summon_info)
	self.cache_summon_info = summon_info
end

function SiXiangCallWGData:GetCacheSummonType()
	return self.cache_summon_info
end
--]]

---[[ 四象召唤弹幕
function SiXiangCallWGData:GetSiXiangDanmu()
	local index = self.sx_danmu_index or 1
	if index > #self.sixiang_server_log then
		index = 1
	end
	self.sx_danmu_index = index + 1
	return self.sixiang_server_log[index]
end
--]]

-- 四象召唤红点
function SiXiangCallWGData:SiXiangSummonRemind()
	if self:SiXiangCanTenSummon() then
		return 1
	end
	return 0
end

-- 是否能十连召唤
function SiXiangCallWGData:SiXiangCanTenSummon()
	local item_list = self:GetAllTenConstItem()
	local has_num = 0
	for i=1,#item_list do
		has_num = ItemWGData.Instance:GetItemNumInBagById(item_list[i].item_id)
		if has_num >= item_list[i].num then
			return true
		end
	end
	return false
end

-- 所有可以十连的道具
function SiXiangCallWGData:GetAllTenConstItem()
	local item_list = {}
	local summon_multi_item = nil
	for i=1,#TenSummonTypeList do
		summon_multi_item = self:GetSiXiangSummonConst(TenSummonTypeList[i])
		if summon_multi_item then
			item_list[#item_list + 1] = summon_multi_item
		end
	end
	return item_list
end

-- 是否十连召唤
function SiXiangCallWGData:IsTenSummon(summon_type)
	for i=1,#TenSummonTypeList do
		if TenSummonTypeList[i] == summon_type then
			return true
		end
	end
	return false
end

function SiXiangCallWGData:RegisterSummonRemindInBag()
	local item_list = self:GetAllTenConstItem()
	local item_id_list = {}
	for i=1,#item_list do
		item_id_list[i] = item_list[i].item_id
	end
	BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.SiXiangCall_SXSM, item_id_list, nil)
end

function SiXiangCallWGData:GetGaiLvInfo(sixiang_type)
	return self.item_random_desc[sixiang_type] or {}
end

---------------------------------------------------------------------------------

--[[ 四象激活
function SiXiangCallWGData:GetNoticeDataList()
	return self.sixiang_cfg and self.sixiang_cfg.yugao
end

function SiXiangCallWGData:SetSiXiangYuGaoInfo(protocol)
	self.yugao_flag_list = protocol.yugao_flag_list
end

function SiXiangCallWGData:GetSiXiangYuGaoInfo()
	return self.yugao_flag_list
end

function SiXiangCallWGData:GetSiXiangIsActivate()
	local cfg_list = self:GetNoticeDataList()
	if cfg_list and self.yugao_flag_list then
		for i=1,#cfg_list do
			if self:GetSiXiangYuGaoRewardFlag(cfg_list[i].reward_index) ~= DAOHUN_YUGAO_REWARD_TYPE.HAD_FETCH then
				return false
			end
		end
		return true
	end
	return false
end

function SiXiangCallWGData:GetSiXiangYuGaoRewardFlag(index)
	if index and self.yugao_flag_list then
		return self.yugao_flag_list[index + 1]
	end
	return DAOHUN_YUGAO_REWARD_TYPE.INVALID
end

function SiXiangCallWGData:IsGetAllYuGaoReward()
	local cfg_list = self:GetNoticeDataList()
	if cfg_list then
		for i=1,#cfg_list do
			if self:GetSiXiangYuGaoRewardFlag(cfg_list[i].reward_index) ~= DAOHUN_YUGAO_REWARD_TYPE.HAD_FETCH then
				return false
			end
		end
		return true
	end
	return false
end

function SiXiangCallWGData:SiXiangYugaoRemind()
	local flag_list = self.yugao_flag_list
	if flag_list then
		for i=1,#flag_list do
			if flag_list[i] == DAOHUN_YUGAO_REWARD_TYPE.CAN_FETCH then
				return 1
			end
		end
	end
	return 0
end
--]]


-----------------------------------------------仙器真炼(F1神机百炼) start----------------------------------------------\
--NewFun新方法 start
function SiXiangCallWGData:GetXQZLOtherCfg()
	return self.xqzl_other_cfg
end

function SiXiangCallWGData:GetXQZLShowItemList()
	local show_list = {}
	local other_cfg = SiXiangCallWGData.Instance:GetXQZLOtherCfg()
    if not other_cfg then
        return show_list
    end
    local item_id_list = Split(other_cfg.show or "", ",")
    for i = 1, #item_id_list do
        show_list[i] = {item_id = tonumber(item_id_list[i])}
    end
    return show_list
end

function SiXiangCallWGData:GetXQZLShowItemTag()
	local other_cfg = SiXiangCallWGData.Instance:GetXQZLOtherCfg()
	local item_label_list = Split(other_cfg.label or "", ",")
    return item_label_list
end

function SiXiangCallWGData:GetXQZLShowItemSecTag()
	local other_cfg = SiXiangCallWGData.Instance:GetXQZLOtherCfg()
	local item_label = Split(other_cfg.labels or "", ",")
    return item_label
end

function SiXiangCallWGData:GetXQZLOtherCfgByKey(key)
	if self.xqzl_other_cfg and key then
		return self.xqzl_other_cfg[key]
	end
end

function SiXiangCallWGData:GetXianQiZhenLianItemData()
	return self.cache_xqzl_item_data
end

function SiXiangCallWGData:GetXQZLExchangeDataList()
	if self.xianqi_zhenlian_cfg then
		return self.xianqi_zhenlian_cfg.exchange or {}
	end
end

-- 仙器是否有大奖
function SiXiangCallWGData:XianQiHasBigReward()
	local reward_list = self:GetMachineDrawReward()
	if reward_list then
		local item_cfg = nil
		local best_level = self.xianqi_best_reward_level
		for i=1,#reward_list do
			item_cfg = ItemWGData.Instance:GetItemConfig(reward_list[i].item_id)
			if item_cfg and item_cfg.color >= best_level then
				return true
			end
		end
	end
	return false
end

function SiXiangCallWGData:SetCacheXianQiInfo(xianqi_info)
	self.cache_xianqi_info = xianqi_info
end

function SiXiangCallWGData:GetCacheXianQiType()
	return self.cache_xianqi_info
end

--NewFun新方法 end

function SiXiangCallWGData:SetMachineInfo(protocol)
    local has_first_single_draw = protocol.has_first_single_draw--是否首次单抽（备用）
    local has_first_multi_draw = protocol.has_first_multi_draw--是否首次n抽（备用）
    self.xianqi_server_log = protocol.server_draw_log
    self.xianqi_person_log = protocol.person_draw_log
	self.xianqi_convert_shop_money_num  = protocol.convert_shop_money_num
end

function SiXiangCallWGData:SetMachineResult(protocol)
	self.ma_cycle = protocol.cycle
    self.machine_draw_type = protocol.draw_type
    self.machine_draw_count = protocol.reward_count

	if protocol.draw_type == MACHINE_REWARD_TYPE.FIRST_N or
			protocol.draw_type == MACHINE_REWARD_TYPE.USE_ITEM then
		self.xianqi_result_list = self:RandList(protocol.reward_info)
	else
		self.xianqi_result_list = protocol.reward_info
	end
end

function SiXiangCallWGData:AddMachineLog(protocol)
    for i, v in ipairs(protocol.server_draw_log) do
        table.insert(self.xianqi_server_log, 1, v)
    end
end

function SiXiangCallWGData:SetXianQiShopMoneyChange(convert_shop_money_num)
	self.xianqi_convert_shop_money_num = convert_shop_money_num
end

function SiXiangCallWGData:GetXianQitShopMoneyChange()
	return self.xianqi_convert_shop_money_num or 0
end

function SiXiangCallWGData:GetMachineDrawReward()
    local cfg
    local reward_list = {}
    local list = self.xianqi_result_list or {}
	local is_old = self.ma_cycle == 0

    if self.machine_draw_type == MACHINE_REWARD_TYPE.FIRST_N then
		if is_old then
			cfg = self.xianqi_zhenlian_cfg.first_multi_lotto
		else
			cfg = self.machine_tedian_cfg.first_multi_lotto
		end
        for i, v in ipairs(list) do--因为这个首次n抽仅出现一次，所以不做处理
            for m, n in pairs(cfg) do
                if (is_old or self.ma_cycle == n.cycle) and n.first_multi_lotto_group == v.reward_pool
						and n.first_single_lotto_id == v.reward_id then
                    table.insert(reward_list, n)
                end
            end
        end
	elseif self.machine_draw_type == MACHINE_REWARD_TYPE.USE_ITEM then
		if is_old then
			for i, v in ipairs(list) do
				if self.machine_special_cfg[v.reward_pool] then
					table.insert(reward_list, self.machine_special_cfg[v.reward_pool][v.reward_id])
				end
			end
		else
			for i, v in ipairs(list) do
				if self.machine_tedian_special_cfg[self.ma_cycle] and self.machine_tedian_special_cfg[self.ma_cycle][v.reward_pool] then
					table.insert(reward_list, self.machine_tedian_special_cfg[self.ma_cycle][v.reward_pool][v.reward_id])
				end
			end
		end
	elseif self.machine_draw_type == MACHINE_REWARD_TYPE.FIRST_SINGLE then
		if is_old then
			cfg = self.xianqi_zhenlian_cfg.first_single_lotto
		else
			cfg = self.machine_tedian_cfg.first_single_lotto
		end
		for i, v in ipairs(list) do--首抽1次同上
			for m, n in pairs(cfg) do
				if (is_old or self.ma_cycle == n.cycle) and n.first_single_lotto_reward == v.reward_id then
					table.insert(reward_list, n)
				end
			end
		end
    else
		if is_old then
			cfg = self.xianqi_zhenlian_cfg.reward
			for i, v in ipairs(list) do
				table.insert(reward_list, cfg[v.reward_id])
			end
		else
			cfg = self.machine_tedian_reward_cfg
			for i, v in ipairs(list) do
				table.insert(reward_list, cfg[self.ma_cycle][v.reward_id])
			end
		end
    end
    return reward_list
end

function SiXiangCallWGData:GetMachineDrawType()
	return self.machine_draw_type
end

function SiXiangCallWGData:GetMachineDrawCount()
	return self.machine_draw_count
end

function SiXiangCallWGData:GetMachineOtherCfg()
	return self.xianqi_zhenlian_cfg.other[1]
end

function SiXiangCallWGData:GetMachineServerLog()
	return self.xianqi_server_log
end

function SiXiangCallWGData:GetMachinePersonLog()
	return self.xianqi_person_log
end

function SiXiangCallWGData:GetMachineServerLogByIndex(index)
	return self.xianqi_server_log[index]
end

function SiXiangCallWGData:GetMachineDanmu()
	self.danmu_index = self.danmu_index or 1
	if self.danmu_index > #self.xianqi_server_log then
		self.danmu_index = 1
	end
	local log = self:GetMachineServerLogByIndex(self.danmu_index)
	self.danmu_index = self.danmu_index + 1
	return log
end

function SiXiangCallWGData:GetCanDrawMachine()
    local cfg = self:GetMachineOtherCfg()
	local num = ItemWGData.Instance:GetItemNumInBagById(cfg.multi_lotto_consume_special)
	if num > 0 then
		return 1
	end

	local multi_lotto = cfg.multi_lotto_consume
    num = ItemWGData.Instance:GetItemNumInBagById(multi_lotto.item_id)
	if num >= multi_lotto.num then
		return 1
	end

	if cfg.single_lotto_consume.item_id ~= cfg.multi_lotto_consume.item_id then
		num = ItemWGData.Instance:GetItemNumInBagById(cfg.multi_lotto_consume.item_id)
		return num >= cfg.multi_lotto_consume.num and 1 or 0
	end

	return 0
end

function SiXiangCallWGData:GetMaAlertStr(item_id, need_num, has_num)
	local cfg = SiXiangCallWGData.Instance:GetMachineOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    local limit_shop_can_buy_num = ShopWGData.Instance:GetMaxBuyNum(cfg.shop_seq)
    local str = Language.SiXiangCall.XQZLAlertTip
    local dif_num = need_num - has_num
    local limit_price, price, sum
    if limit_shop_can_buy_num >= dif_num then
        local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(cfg.shop_seq)
        limit_price = dif_num * shop_cfg.price
        str = string.format(str, limit_price, name)
        str = str .. "\n" .. string.format(Language.SiXiangCall.XQZLLimitStr, shop_cfg.price, dif_num)
		sum = limit_price
    elseif limit_shop_can_buy_num == 0 then
        price = cfg.complement_num * dif_num
        str = string.format(str, price, name)
        str = str .. "\n" .. string.format(Language.SiXiangCall.XQZLNorStr, cfg.complement_num, dif_num)
		sum = price
    else
        local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(cfg.shop_seq)
        local limit_num = limit_shop_can_buy_num
        local nor_num = need_num - has_num - limit_shop_can_buy_num
        limit_price = limit_num * shop_cfg.price
        price = cfg.complement_num * nor_num

        str = string.format(str, limit_price + price, name)
        str = str .. "\n" .. string.format(Language.SiXiangCall.XQZLLimitStr, shop_cfg.price, limit_num)
        str = str .. "\n" .. string.format(Language.SiXiangCall.XQZLNorStr, cfg.complement_num, nor_num)
		sum = limit_price + price
    end

    return str, sum
end

function SiXiangCallWGData:RegisterMachineRemindInBag(remind_name)
	local list = self:GetMachineRemindItemList()
	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, list, nil)
end

function SiXiangCallWGData:RegisterSXSMRemindInBag(remind_name)
	local list = {}
	local item_list = self:GetAllTenConstItem()
	local has_num = 0
	for i=1,#item_list do
		table.insert(list,item_list[i].item_id)
	end
	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, list, nil)
end

function SiXiangCallWGData:GetMachineRemindItemList()
	local cfg = self:GetMachineOtherCfg()
	local list = {}
	table.insert(list, cfg.single_lotto_consume.item_id)
	if cfg.multi_lotto_consume.item_id ~= cfg.single_lotto_consume.item_id then
		table.insert(list, cfg.multi_lotto_consume.item_id)
	end
	table.insert(list, cfg.multi_lotto_consume_special)
	table.insert(list, cfg.lotto_exchange.item_id)
	return list
end

--仙器真炼红点判断
function SiXiangCallWGData:IsShowMachineRedPoint()
    if self:GetCanDrawMachine() == 1 then
        return 1
    end

	-- if self:GetShenjiEquipRed() == 1 then
	-- 	return 1
	-- end

	-- if XianQiTeDianWGData.Instance:GetXianQITeDianShiLianRemind() == 1 then
	-- 	return 1
	-- end

	-- if XianQiTeDianWGData.Instance:GetXianQITeDianZhenXuanRemind() == 1 then
	-- 	return 1
	-- end

	-- if ShenJiNoticeWGData.Instance:GetSale1Remind() == 1 then
	-- 	return 1
	-- end

    return 0
end

function SiXiangCallWGData:GetShenjiEquipRed()
	if FunOpen.Instance:GetFunIsOpened("ShenJi") then
		if (HiddenWeaponWGData.Instance.remind_manager:IsWeaponTypeRed(1)) then
			return 1
		end
		if (HiddenWeaponWGData.Instance.remind_manager:IsWeaponTypeRed(2)) then
			return 1
		end
	end
	return 0
end
-----------------------------------------------仙器真炼(F1神机百炼) end----------------------------------------------