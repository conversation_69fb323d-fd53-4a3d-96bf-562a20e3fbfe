-- 五行沧溟
function FiveElementsView:InitFiveElementCangMingView()
	XUI.AddClickEventListener(self.node_list.cangming_skill_img, BindTool.Bind(self.OnClickSkillIcon ,self))
	XUI.AddClickEventListener(self.node_list.cangming_skill_btn, BindTool.Bind(self.OnClickOpenSkillView ,self))
	XUI.AddClickEventListener(self.node_list.cangming_active_btn, BindTool.Bind(self.OnActOrUpgradeClick, self))--孔位激活升级
	XUI.AddClickEventListener(self.node_list["cangming_btn_use"], BindTool.Bind(self.OnBtnHHClick, self))--幻化
	XUI.AddClickEventListener(self.node_list.cangming_btn_qtl, BindTool.Bind(self.OnClickQtlBtn, self))--乾天录
	XUI.AddClickEventListener(self.node_list.cangming_btn_wd, BindTool.Bind(self.OnClickWdBtn, self))--道魂
	
	if not self.waist_type_list then
		self.waist_type_list = AsyncListView.New(CangMingWaistRender, self.node_list.waist_type_list)
    	self.waist_type_list:SetSelectCallBack(BindTool.Bind1(self.OnClickWaistkType, self))
	end

	if not self.slot_type_cell_list then
		self.slot_type_cell_list = {}
		for i = 1, 5 do
			self.slot_type_cell_list[i] = CangMingSlotRender.New(self.node_list["waist_slot_" .. i])
			self.slot_type_cell_list[i]:SetIndex(i)
			self.slot_type_cell_list[i]:SetOnlyClickCallBack(BindTool.Bind(self.SlotSelectCellBack, self))
		end
	end

	if not self.model_display then
        self.model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.model_display:SetRenderTexUI3DModel(display_data)

        -- self.model_display:SetUI3DModel(self.node_list["display"].transform, self.node_list.EventTriggerListener.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.model_display)
	end

	if self.cangming_comsume_cell == nil then
		self.cangming_comsume_cell = ItemCell.New(self.node_list.cangming_comsume_pos)
		self.cangming_comsume_cell:SetNeedItemGetWay(true)
	end

	if self.cangming_attr_list == nil then
        self.cangming_attr_list = {}
        local node_num = self.node_list["cangming_attr_list"].transform.childCount
        for i = 1, node_num do
            self.cangming_attr_list[i] = CommonAddAttrRender.New(self.node_list["cangming_attr_list"]:FindObj("attr_" .. i))
			self.cangming_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

	self.select_waist_data = nil
	self.select_slot_data = nil
	self.jump_type = nil
end

function FiveElementsView:ShowFiveElementCangMingView()
	self.node_list["title_view_name"].text.text = Language.FiveElements.TabName[4]
	self:ChangeFiveElementsViewBg("a2_wx_bg1")
	self:PlayCangMingAnim()
end

function FiveElementsView:CangMingViewReleaseCallBack()
	if nil ~= self.waist_type_list then
        self.waist_type_list:DeleteMe()
        self.waist_type_list = nil
    end

	if self.slot_type_cell_list then
		for k, v in pairs(self.slot_type_cell_list) do
			v:DeleteMe()
		end
		self.slot_type_cell_list = nil
	end

	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

	if self.cangming_comsume_cell then
		self.cangming_comsume_cell:DeleteMe()
		self.cangming_comsume_cell = nil
	end

	if self.cangming_attr_list then
        for k, v in pairs(self.cangming_attr_list) do
            v:DeleteMe()
        end
        self.cangming_attr_list = nil
    end

	self.select_waist_data = nil
	self.select_slot_data = nil
	self.select_solt_index = nil
	self.jump_type = nil
end

function FiveElementsView:OnFlushCangMing(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.jump_type then
				self.jump_type = v.jump_type
			end
		end
	end

	self:OnFlushTypeList()
	self:OnFlushSlot(true)
	self:FlushCangMingInfoPanel()
	self:FlushCangMingConsume()
	self:OnFlushCangMingAttrView()
end

function FiveElementsView:OnFlushTypeList()
	local data_list = FiveElementsWGData.Instance:GetWaistLightList()
	if IsEmptyTable(data_list) then
		return
	end

 	self.waist_type_list:SetDataList(data_list)
	if not self.select_waist_data then
		local selcet_index = 1
		if self.jump_type then
			selcet_index = self.jump_type
			self.jump_type = nil
		else
			for i, v in ipairs(data_list) do
				local is_red = FiveElementsWGData.Instance:GetCangMingTypeRemind(v.type)
				if is_red then
					selcet_index = i
					break
				end
			end
		end

		local jump_index = selcet_index > #data_list and 1 or selcet_index
		self.waist_type_list:JumpToIndex(jump_index)
	else
		if self.jump_type then
			local jump_index = self.jump_type > #data_list and 1 or self.jump_type
			self.waist_type_list:JumpToIndex(jump_index)
			self.jump_type = nil
		end
	end
end

function FiveElementsView:OnClickWaistkType(item)
	if nil == item or nil == item.data or self.select_waist_data == item.data then
		return
	end

	local data = item.data
	self.select_waist_data = data
	self:OnFlushSlot(true)

	self:FlushCangMingModel()
	self:FlushCangMingInfoPanel()
end

function FiveElementsView:OnFlushSlot(flush_jump)
	if not self.select_waist_data then
		return
	end

	local data_list = FiveElementsWGData.Instance:GetWaistLightSlotCfg(self.select_waist_data.type)
	if not data_list then
		return
	end

	for i, v in ipairs(self.slot_type_cell_list) do
    	if data_list[i] then
        	v:SetData(data_list[i])
        end
    end

	local select_index = -1
	if self.select_solt_index ~= nil then--已经选中且还能激活或升星的情况下不跳到其他的slot
		local is_red = FiveElementsWGData.Instance:GetCangMingSlotRemind(self.select_waist_data.type, self.select_solt_index)
		select_index = is_red and self.select_solt_index or -1
	end
	
	if flush_jump and select_index < 0 then
		local click_cell_index = 1
		for i, v in ipairs(data_list) do
			local is_red = FiveElementsWGData.Instance:GetCangMingSlotRemind(v.type, v.slot)
			if is_red then
				click_cell_index = i
				break
			end
		end

		self.slot_type_cell_list[click_cell_index]:OnClick()
	end
end

function FiveElementsView:SlotSelectCellBack(item)
	if nil == item or nil == item.data then
		return
	end

	local data = item.data
	if self.select_slot_data == data then 
		TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
		return 
	end

	self.select_slot_data = data
	self.select_solt_index = item.index
	for i,v in ipairs(self.slot_type_cell_list) do
		v:SetHighLight(v.data == data)
	end

	self:FlushCangMingConsume()
	self:OnFlushCangMingAttrView()
end

function FiveElementsView:FlushCangMingInfoPanel()
	if not self.select_waist_data then
		return
	end

	self.node_list.cangming_name.text.text = self.select_waist_data.name
	local waist_level, is_open = FiveElementsWGData.Instance:GetWaistLightOpen(self.select_waist_data.type)
	self.node_list.cangming_level.text.text = string.format(Language.FiveElements.JieStr, waist_level)
	local skill_level = waist_level == 0 and 1 or waist_level
	local skill_cfg = SkillWGData.Instance:GetHaloSkillById(self.select_waist_data.skill_id, skill_level)
	if skill_cfg then
		self.node_list.cangming_skill_name.text.text = skill_cfg.skill_name
		local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_cfg.skill_id, skill_level)
		if clien_skill_cfg then
			local s_bundle, s_asset = ResPath.GetSkillIconById(clien_skill_cfg.icon_resource)
			self.node_list.cangming_skill_img.image:LoadSprite(s_bundle, s_asset)
		end
	end

	local is_use = FiveElementsWGData.Instance:GetWaistLightUse(self.select_waist_data.type)
	local bt_text = is_use and  Language.FiveElements.BtnCZTxt or Language.FiveElements.BtnHHTxt
	self.node_list["use_text"].text.text = bt_text
	self.node_list.cangming_btn_use.image = ResPath.GetFiveElementsImg("a2_ty_anniu_di_16")


	self.node_list.cangming_btn_use:SetActive(is_open)

	local cap = FiveElementsWGData.Instance:GetWaistLightCap(self.select_waist_data.type)
	self.node_list.cangming_cap_value.text.text = cap
end

function FiveElementsView:FlushCangMingConsume()
	if not self.select_slot_data then
		return
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.select_slot_data.item_id)
	local str = item_num .. "/" .. self.select_slot_data.cost_item_num
	local color = item_num >= self.select_slot_data.cost_item_num and COLOR3B.GREEN or COLOR3B.RED
	self.cangming_comsume_cell:SetData({item_id = self.select_slot_data.item_id})
	self.cangming_comsume_cell:SetRightBottomColorText(ToColorStr(str, color))
	self.cangming_comsume_cell:SetRightBottomTextVisible(true)
end

function FiveElementsView:OnFlushCangMingAttrView()
	if not self.select_slot_data then
		return
	end

	local attr_list = FiveElementsWGData.Instance:GetWaistLightSlotAttrList(self.select_slot_data.type, self.select_slot_data.slot)
	local info = FiveElementsWGData.Instance:GetWaistLightSlotInfo(self.select_slot_data.type, self.select_slot_data.slot)

	local need_show_effect = false
	if info and nil ~= self.cm_slot_cache and nil ~= self.cm_type_cache and nil ~= self.cm_level_cache then
		if (self.cm_slot_cache == self.select_slot_data.slot) and (self.cm_type_cache == self.select_slot_data.type) and (info.level - self.cm_level_cache == 1) then
			need_show_effect = true
		end
	end
	self.cm_slot_cache = self.select_slot_data.slot
	self.cm_type_cache = self.select_slot_data.type


	for k, v in pairs(self.cangming_attr_list) do
        v:SetData(attr_list[k])

		if need_show_effect then
			v:PlayAttrValueUpEffect()
		end
    end

	if info then
		self.node_list.cangming_btn_text.text.text = info.level > 0 and Language.FiveElements.StarBtnStr or Language.FiveElements.ActiveBtnStr
		self.node_list.cangming_is_max:SetActive(self.select_slot_data.max_lv <= info.level)
		self.node_list.cangming_active_btn:SetActive(self.select_slot_data.max_lv > info.level)
		self.node_list.cangming_comsume_pos:SetActive(self.select_slot_data.max_lv > info.level)
		local is_red = FiveElementsWGData.Instance:GetCangMingSlotRemind(self.select_slot_data.type, self.select_slot_data.slot)
		self.node_list.cangming_btn_remind:SetActive(is_red)

		self.cm_level_cache = info.level
	end
end

function FiveElementsView:FlushCangMingModel()
	if not self.select_waist_data then
		return
	end

	self.model_display:RemoveAllModel()
    local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.model_display:SetModelResInfo(role_vo, nil, function()
		self.model_display:PlayRoleShowAction()
	end)

	local pos_x, pos_y = 0, 0
	if self.select_waist_data.display_pos and self.select_waist_data.display_pos ~= "" then
		local pos_list = string.split(self.select_waist_data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.display.rect, pos_x, pos_y)

	if self.select_waist_data.rotation and self.select_waist_data.rotation ~= "" then
		local rotation_tab = string.split(self.select_waist_data.rotation,"|")
		self.node_list["display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end

	if self.select_waist_data.display_scale ~= "" then
		local scale = self.select_waist_data.display_scale
		RectTransform.SetLocalScale(self.node_list["display"].transform, scale)
	end
	
	self.model_display:SetSkillHaloResid(self.select_waist_data.type)
	self.model_display:FixToOrthographic(self.root_node_transform)
end

function FiveElementsView:PlayCangMingAnim()
	local tween_info = UITween_CONSTS.FiveElements
	RectTransform.SetAnchoredPositionXY(self.node_list.cangming_right_panel.rect, 600, 0)
	local right_tween = self.node_list.cangming_right_panel.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
end

function FiveElementsView:OnClickOpenSkillView()
	FiveElementsWGCtrl.Instance:OpenCangMingSkillView()
end

function FiveElementsView:OnClickSkillIcon()
	if not self.select_waist_data then
		return
	end

	local data = FiveElementsWGData.Instance:GetWaistLightSkillInfo(self.select_waist_data.skill_id)
	CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)
end

function FiveElementsView:OnActOrUpgradeClick()
	if not self.select_slot_data then
		return
	end

	local info = FiveElementsWGData.Instance:GetWaistLightSlotInfo(self.select_slot_data.type, self.select_slot_data.slot)
	if not info then 
		return
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(self.select_slot_data.item_id)
	if num < self.select_slot_data.cost_item_num then
		TipWGCtrl.Instance:OpenItem({item_id = self.select_slot_data.item_id})
        return
    end

    local level = info.level
    local max_level = self.select_slot_data.max_lv
    if level >= max_level then
        return
    end

	if level > 0 then
		--五行孔位升级操作
		FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.STONE_LEL, self.select_slot_data.type, self.select_slot_data.slot)
	else
		--五行孔位激活操作
		local bag_index = ItemWGData.Instance:GetItemIndex(self.select_slot_data.item_id)
		FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.USE_STONE, self.select_slot_data.type, self.select_slot_data.slot, bag_index)
	end
end

function FiveElementsView:OnBtnHHClick()
	if not self.select_waist_data then
		return
	end

	local is_use = FiveElementsWGData.Instance:GetWaistLightUse(self.select_waist_data.type)
	if is_use then
		FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.CANCEL_WAIST, self.select_waist_data.type)
	else
		FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.USE_WAIST, self.select_waist_data.type)
	end
end

function FiveElementsView:OnClickQtlBtn()
	FiveElementsWGCtrl.Instance:OpenQiantianluView()
end

function FiveElementsView:OnClickWdBtn()
	FiveElementsWGCtrl.Instance:OpenDaohunView()
end

------------------------------------五行光环列表------------------------
CangMingWaistRender = CangMingWaistRender or BaseClass(BaseRender)

function CangMingWaistRender:__init()

end

function CangMingWaistRender:__delete()
	XUI.SetGraphicGrey(self.node_list.icon, false)
end

function CangMingWaistRender:LoadCallBack()

end

function CangMingWaistRender:OnFlush()
	if nil == self.data then
		return
	end

	self.node_list.name.text.text = self.data.name
	local bundle, asset = ResPath.GetFiveElementsImg("a1_wx_type_" .. self.data.type)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
	    self.node_list.icon.image:SetNativeSize()
    end)

	local level, isopen = FiveElementsWGData.Instance:GetWaistLightOpen(self.data.type)
	XUI.SetGraphicGrey(self.node_list.icon, not isopen)
	local is_use = FiveElementsWGData.Instance:GetWaistLightUse(self.data.type)
	self.node_list.put_on:SetActive(is_use)

	local is_red  = FiveElementsWGData.Instance:GetCangMingTypeRemind(self.data.type)
	self.node_list.remind:SetActive(is_red)
end

function CangMingWaistRender:OnSelectChange(is_select)
	self.node_list.bg_hl:SetActive(is_select)
end

------------------------------------孔位item列表------------------------
CangMingSlotRender = CangMingSlotRender or BaseClass(BaseRender)

function CangMingSlotRender:LoadCallBack()
	if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list.cell_pos)
		self.item_cell:SetIsShowTips(false)
	end

	self.star_list = {}
	local star_num = self.node_list.stars.transform.childCount
    for i = 1, star_num do
        self.star_list[i] = self.node_list["star" .. i]
    end

	XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClick, self))
	self.click_callback = nil

	self:SetHighLight(false)
end

function CangMingSlotRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.click_callback = nil
	self.star_list = nil
end

function CangMingSlotRender:OnFlush()
	if nil == self.data then 
		return 
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if nil ~= item_cfg then
		self.item_cell:SetData({item_id = self.data.item_id})
	end

	local info = FiveElementsWGData.Instance:GetWaistLightSlotInfo(self.data.type, self.data.slot)
	self.item_cell:MakeGray(not (info and info.level > 0))
	local is_red = FiveElementsWGData.Instance:GetCangMingSlotRemind(self.data.type, self.data.slot)
	self.node_list.remind:SetActive(is_red)
	self:FlushStar(info)
end

function CangMingSlotRender:FlushStar(info)
	if not info then
		return
	end
		
    local star_res_list = GetStarImgResByStar(info.level)
    for k,v in pairs(self.star_list) do
        v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
    end
end

function CangMingSlotRender:SetOnlyClickCallBack(callback)
    self.click_callback = callback
end

function CangMingSlotRender:OnClick()
    if nil ~= self.click_callback then
		self.click_callback(self)
	end
end

function CangMingSlotRender:SetHighLight(enabled)
	self.node_list.select_hl:SetActive(enabled)
end