CangMingChaseView = CangMingChaseView or BaseClass(SafeBaseView)

function CangMingChaseView:__init()
	self.view_name = "CangMingChaseView"
	self.view_style = ViewStyle.Half
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/cangmingbuy_ui_prefab", "layout_cangming_buy_activity")
end

function CangMingChaseView:__delete()

end

function CangMingChaseView:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CANGMING_BUY, 1)
end

function CangMingChaseView:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.reward_list:SetStartZeroIndex(true)

	XUI.AddClickEventListener(self.node_list["btn_sell"], BindTool.Bind(self.OnBtnSellBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_open_skill_view"], BindTool.Bind(self.OnBtnOpenSkillViewBtn, self))
	self:FlushTimeCount()
end

function CangMingChaseView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("cang_ming_time") then
		CountDownManager.Instance:RemoveCountDown("cang_ming_time")
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

--战力显示
function CangMingChaseView:FlushCapStr()
	local cur_shop_cfg = CangMingChaseWGData.Instance:GetCurShopCfg()
	if IsEmptyTable(cur_shop_cfg) and (not self.cap_display_flush) then
		return
	end

	local capability = 0
	local item_data = SortDataByItemColor(cur_shop_cfg.reward_item)
	for k, v in pairs(item_data) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			capability = capability + ItemShowWGData.CalculateCapability(v.item_id)
		end
	end

	self.node_list.cap_value.text.text = capability
	self.cap_display_flush = false
end

function CangMingChaseView:OnFlush()
	local cur_shop_cfg = CangMingChaseWGData.Instance:GetCurShopCfg()
	if IsEmptyTable(cur_shop_cfg) then
		return
	end

	local price = RoleWGData.GetPayMoneyStr(cur_shop_cfg.price, cur_shop_cfg.rmb_type, cur_shop_cfg.rmb_seq)
	local is_buy_rmb = CangMingChaseWGData.Instance:GetShopIsBuyFlag()
	self.reward_list:SetDataList(cur_shop_cfg.reward_item)
	self.node_list.btn_text.text.text = price
	XUI.SetButtonEnabled(self.node_list["btn_sell"], not is_buy_rmb)

	self:FlushSkillImg(cur_shop_cfg)
end

function CangMingChaseView:FlushSkillImg(cur_shop_cfg)
	local pos_x, pos_y = 0, 0
	local pos_list = string.split(cur_shop_cfg.img_pos, "|")
	pos_x = tonumber(pos_list[1]) or pos_x
	pos_y = tonumber(pos_list[2]) or pos_y
	RectTransform.SetAnchoredPositionXY(self.node_list.esoterica_img.rect, pos_x, pos_y)

	local skill_type = cur_shop_cfg.skill_type
	local skill_img_bundle, skill_img_asset = ResPath.GetRawImagesPNG("a3_xf_rwlh" .. skill_type)
	self.node_list.esoterica_img.raw_image:LoadSprite(skill_img_bundle, skill_img_asset, function()
		self.node_list.esoterica_img.raw_image:SetNativeSize()
	end)

	local scale = cur_shop_cfg.img_scale
	RectTransform.SetLocalScaleXYZ(self.node_list.esoterica_img.transform, scale, scale, scale)

	local skill_name_bundle, skill_name_asset = ResPath.GetXianFaImg("a3_xfzg_ysz" .. skill_type)
	self.node_list.skill_name.image:LoadSprite(skill_name_bundle, skill_name_asset, function()
		self.node_list.skill_name.image:SetNativeSize()
	end)

	local effect_bundle, effect_asset = ResPath.GetA2Effect(cur_shop_cfg.ui_effect_asset)
	self.node_list.esoterica_effect:ChangeAsset(effect_bundle, effect_asset)

	self.node_list.skill_desc.text.text = cur_shop_cfg.skill_desc
end

function CangMingChaseView:FlushTimeCount()
	if CountDownManager.Instance:HasCountDown("cang_ming_time") then
		CountDownManager.Instance:RemoveCountDown("cang_ming_time")
	end

	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CANGMING_BUY)
	if time > 0 then
		CountDownManager.Instance:AddCountDown("cang_ming_time",
			BindTool.Bind(self.FinalUpdateTimeCallBack, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function CangMingChaseView:FinalUpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM9(time)
	self.node_list["time_str"].text.text = string.format(Language.CangMingBuy.ActivityTime, time_str)
end

function CangMingChaseView:OnComplete()
	self.node_list.time_str.text.text = "00:00"
end

-- function CangMingChaseView:OnBtnfreeBtn()
-- 	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
-- 	.RAND_ACTIVITY_TYPE_OA_CANGMING_BUY)
-- 	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
-- 		local _, is_buy_free = CangMingChaseWGData.Instance:GetShopIsBuyFlag()
-- 		if is_buy_free then
-- 			TipWGCtrl.Instance:ShowSystemMsg(Language.CangMingBuy.AllFreeShopBuy)
-- 			return
-- 		end

-- 		FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.BUY_FREE,
-- 			CangMingChaseWGData.Instance:GetCurFreeShopID())
-- 	end
-- end

function CangMingChaseView:OnBtnSellBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
	.RAND_ACTIVITY_TYPE_OA_CANGMING_BUY)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_rmb = CangMingChaseWGData.Instance:GetShopIsBuyFlag()
		if is_buy_rmb then
			TipWGCtrl.Instance:ShowSystemMsg(Language.CangMingBuy.AllShopBuy)
			return
		end

		local cur_shop_cfg = CangMingChaseWGData.Instance:GetCurShopCfg()
		if not IsEmptyTable(cur_shop_cfg) then
			RechargeWGCtrl.Instance:Recharge(cur_shop_cfg.price, cur_shop_cfg.rmb_type, cur_shop_cfg.rmb_seq)
			return
		end
	end
end

function CangMingChaseView:OnBtnOpenSkillViewBtn()
	local cur_shop_cfg = CangMingChaseWGData.Instance:GetCurShopCfg()
	CultivationWGCtrl.Instance:OpenEsotericaSkillView({slot = cur_shop_cfg.esoterica_seq})
end

function CangMingChaseView:OnBtnOpenView()
	local cur_shop_cfg = CangMingChaseWGData.Instance:GetCurShopCfg()
	if IsEmptyTable(cur_shop_cfg) then
		return
	end

	ViewManager.Instance:Open(GuideModuleName.FiveElementsView, TabIndex.five_elements_cangming, "all",
		{ jump_type = cur_shop_cfg.waist_type })
	self:Close()
end

function CangMingChaseView:OnBtnShowSkill()
	local cur_shop_cfg = CangMingChaseWGData.Instance:GetCurShopCfg()
	if not cur_shop_cfg then
		return
	end

	local cfg = FiveElementsWGData.Instance:GetWaistLightCfgByType(cur_shop_cfg.waist_type)
	if not cfg then
		return
	end

	local data = {
		level = 1,
		skill_id = cfg.skill_id,
		skill_halo_id = cfg.type,
	}
	CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)
end

------------------- CangMingRewardRender -------------------
CangMingRewardRender = CangMingRewardRender or BaseClass(BaseRender)
function CangMingRewardRender:LoadCallBack()
	if not self.reward_item then
		self.reward_item = ItemCell.New(self.node_list.reward_item)
	end
end

function CangMingRewardRender:ReleaseCallBack()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function CangMingRewardRender:OnFlush()
	if not self.data then
		return
	end

	self.reward_item:SetData(self.data)

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.item_name.text.text = item_cfg.name
end