require("game/chat/chat_wg_data")
require("game/chat/chat_view")
-- require("game/chat/chat_record_view")
-- require("game/chat/chat_private")
require("game/chat/chat_protocol")
require("game/chat/chat_transmit")
require("game/chat/chat_filter")
-- require("game/chat/chat_msg_input")
-- require("game/chat/blacklist_view")

require("game/chat/chat_window1")
require("game/chat/chat_setting")
require("game/chat/chat_location")
require("game/chat/handle_click_point")
require("game/chat/team_handle_click_point")
require("game/chat/chat_paiyipai")
require("game/chat/chat_vip_limit_tip_view")


ChatWGCtrl = ChatWGCtrl or BaseClass(BaseWGCtrl)

function ChatWGCtrl:__init()
	if ChatWGCtrl.Instance then
		ErrorLog("[ChatWGCtrl]:Attempt to create singleton twice!")
	end
	ChatWGCtrl.Instance = self

	self.chatFilter = ChatFilter.New()
	--self.society_view = SocietyView.New()  --好友聊天
	self.data = ChatWGData.New()
	self.chat_window = NewChatWindow.New(GuideModuleName.ChatView)
	--self.record_mgr = ChatRecordMgr.New()
	self.view = ChatView.New()
	self.face_view = ChatFaceView.New()				-- 表情
	-- self.big_face_view = ChatBigFaceView.New()		-- 大表情
	-- self.word_face_view = ChatWordFaceView.New()
	-- self.item_view = ChatItemView.New()				-- 物品
	self.setting_view = ChatSetting.New()				-- 设置
	self.location_view = ChatLocation.New()			-- 选择城市
	self.city_select_menu = CitySelectMenu.New() 		-- 城市列表面板


	-- self.blacklist_view = BlacklistView.New()
	-- self.add_blacklist_view = AddBlacklistView.New()

	self.transmit_pop_view = ChatTransmitPopView.New()

	self:RegisterAllProtocols()

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

	self:CreateItemList()
	self.auto_play_voice_list = {}			--自动播放语音队列


	self.interval = 0.5							--添加消息间隔
	self.chuanwen_interval = 2 					--传闻消息间隔
	self.guild_system_interval = 1 				--仙盟系统消息间隔
	self.next_send_chuanwen_time = 0 				--下次传闻刷新时间
	self.next_send_world_time = 0 				--下次世界刷新时间
	self.next_send_guild_system_time = 0 				--下次仙盟系统信息刷新时间

	self.world_time_quest = nil				--世界聊天计时器
	self.chuanwen_time_quest = nil			--系统聊天计时器
	self.guild_system_time_quest = nil			--仙盟系统聊天计时器

	self.client_hearsay_time_quest = {}  	--K传闻
	self.zhandui_log_msg = {} 				--战队日志消息

	self.chat_measuring_list = {}
	self.calc_init = false
	self.is_init_msg_heig = false

	-- self:BindGlobalEvent(OtherEventType.ZhanDui_Add_Log, BindTool.Bind(self.OnZhanDuiLogChange, self))
	-- self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.OnEnterChangeScene, self))
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.InitCalcHeigCell, self))

	--场景变更
	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	--所处服变更
	self:BindGlobalEvent(SceneEventType.ENTER_OTHER_SERVER_COMPLETE, BindTool.Bind(self.OnEnterOtherServer, self))
end
function ChatWGCtrl:flushwindow( ... )
	-- self.chat_window:Flush()
end

function ChatWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	self.location_view:DeleteMe()
	self.location_view = nil

	self.city_select_menu:DeleteMe()
	self.city_select_menu = nil

	self.setting_view:DeleteMe()
	self.setting_view = nil

	self.face_view:DeleteMe()
	self.face_view = nil

	-- self.blacklist_view:DeleteMe()
	-- self.blacklist_view = nil

	-- if self.add_blacklist_view then
	-- 	self.add_blacklist_view:DeleteMe()
	-- 	self.add_blacklist_view = nil
	-- end

	self.chat_window:DeleteMe()
	self.chat_window = nil

	self.transmit_pop_view:DeleteMe()
	self.transmit_pop_view = nil

	self.data:DeleteMe()
	self.data = nil
	self.chatFilter:DeleteMe()

	if self.record_mgr then
		self.record_mgr:DeleteMe()
		self.record_mgr = nil
	end

	self:ClearWorldTimeQuest()
	self:ClearChuanWenTimeQuest()
	self:ClearGuildSystemTimeQuest()

	ChatWGCtrl.Instance = nil

	for k,v in pairs(self.client_hearsay_time_quest) do
		GlobalTimerQuest:CancelQuest(v)
	end
	self.client_hearsay_time_quest = {}
	self.suiji_gonggao_num = nil

	--ClickPointHandle = nil

	self.is_receive_complete = nil

	if self.chat_vip_limit_tip_view then
		self.chat_vip_limit_tip_view:DeleteMe()
		self.chat_vip_limit_tip_view = nil
	end

	if self.chat_measuring_list ~= nil then
		for k,v in pairs(self.chat_measuring_list) do
			v:DeleteMe()
		end
		self.chat_measuring_list = nil
	end

	self:RemoveInitMsgTimer()
	self.wait_init_msg_list = {}
	self.is_init_msg_heig = false
	self.calc_init = false
end


function ChatWGCtrl:GetChatWindow()
	return self.chat_window
end

function ChatWGCtrl:SetStartPlayVoiceState(state)
	self.start_play_voice = state
end

function ChatWGCtrl:ClearPlayVoiceList()
	self.auto_play_voice_list = {}
end

--开始自动播放语音
function ChatWGCtrl:StartAutoPlayVoice()
	if self.start_play_voice then
		return
	end

	self.start_play_voice = true
	if not next(self.auto_play_voice_list) then
		self.start_play_voice = false
		return
	end

	local function paly_call_back()
		if not next(self.auto_play_voice_list) then
			self.start_play_voice = false
			return
		end

		local max_count = #self.auto_play_voice_list

		local channel, data
		for i = max_count, 1, -1 do
			data = self.auto_play_voice_list[i]
			channel = ChatWGData.Instance:GetChannel(data.channel_type)
			if not channel or not channel.is_auto_voice then
				table.remove(self.auto_play_voice_list, i)
			end
		end

		if not next(self.auto_play_voice_list) then
			self.start_play_voice = false
			return
		end

		local new_voice_path = self.auto_play_voice_list[1].path
		local new_voice_content_type = self.auto_play_voice_list[1].content_type
		table.remove(self.auto_play_voice_list, 1)
		GlobalTimerQuest:AddDelayTimer(function()
			if new_voice_content_type == CHAT_CONTENT_TYPE.AUDIO then
				ChatRecordMgr.Instance:PlayVoice(new_voice_path, nil, paly_call_back)
			elseif new_voice_content_type == CHAT_CONTENT_TYPE.FEES_AUDIO then
				local content_t = Split(new_voice_path, "_")
				if #content_t == 3 then
					AudioService.Instance:PlayFeesAudio(content_t[1], paly_call_back)
				end
			end
		end, 0)
	end

	paly_call_back()
end

function ChatWGCtrl:CheckToPlayVoice(msg_info)
	--判断是否自动播放语音
	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	if (msg_info.content_type == CHAT_CONTENT_TYPE.AUDIO or msg_info.content_type == CHAT_CONTENT_TYPE.FEES_AUDIO) and msg_info.from_uid ~= role_id then
		local data = {}
		local function add_data()
			data.from_uid = msg_info.from_uid
			data.msg_id = msg_info.msg_id
			data.channel_type = msg_info.channel_type
			data.content_type = msg_info.content_type
			data.path = msg_info.content
		end
		local channel = ChatWGData.Instance:GetChannel(msg_info.channel_type)
		if channel and channel.is_auto_voice then
			add_data()
		end

		if next(data) then
			table.insert(self.auto_play_voice_list, data)
			self:StartAutoPlayVoice()
		end
	end
end

-- 添加私聊
function ChatWGCtrl:AddPrivateRequset(role_name, callback, role_id, role_info_data)
	if role_name == "" or nil == role_name then
		return
	end

	-- -- 必须是好友才能私聊
	-- if nil == SocietyWGData.Instance:FindFriend(to_uid) then		-- 拒绝陌生私聊
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.IsFindFriend)
	-- 	return
	-- end

	-- 判断等级是否足够
	if GameVoManager.Instance:GetMainRoleVo().level < COMMON_CONSTS.PRIVATE_CHAT_LEVEL_LIMIT then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.PRIVATE_CHAT_LEVEL_LIMIT))
		return
	end

	-- if nil ~= role_id then
	-- 	local index = ChatWGData.Instance:GetPrivateIndex(role_id)
	-- 	if index > 0 then							-- 私聊已添加
	-- 		-- self.view:OpenPrivate(index)
	-- 		--self.chat_window:OpenPrivate(index)
	-- 		return
	-- 	end
	-- end
    if IS_ON_CROSSSERVER then
        if IsEmptyTable(role_info_data) then
            return
        end
		if role_info_data.level < COMMON_CONSTS.CHAT_LEVEL_LIMIT then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PrivateLevelDeficient)
			return
		end
		local user_info = {}
		user_info.role_id = role_info_data.user_id
		user_info.role_name = role_info_data.username or role_info_data.gamename or role_info_data.role_name
		user_info.sex = role_info_data.sex
		user_info.camp = role_info_data.camp
		user_info.prof = role_info_data.prof
		user_info.avatar_key_small = role_info_data.avatar_key_small
		user_info.level = role_info_data.level
		self:AddPrivateResponse(user_info)

	else
		SocietyWGCtrl.Instance:GetUserInfoByName(9998, role_name, function (flag, user_info)
			if 0 == flag or nil == user_info then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NotExist)
				return
			end

			if user_info.level < COMMON_CONSTS.CHAT_LEVEL_LIMIT then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PrivateLevelDeficient)
				return
			end

			self:AddPrivateResponse(user_info)

			if nil ~= callback then
				callback()
			end
		end)
	end
end

--获取聊天cell格子
function ChatWGCtrl:GetChatMeasuring(delegate)
		return self.chat_window:GetChatMeasuring(delegate)
	-- if self.chat_window:IsOpen() then
	-- 	return self.chat_window:GetChatMeasuring(delegate)
	-- end
end

function ChatWGCtrl:AddPrivateResponse(user_info)
	if nil == user_info then
		return
	end

	if not self:IsOwns(user_info.role_id) then
		-- if nil == self.data:GetPrivateObjByRoleId(user_info.role_id) then
			--SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.CreatePrivateSucess)
		-- end
		local private_obj = ChatWGData.CreatePrivateObj()
		private_obj.role_id = user_info.role_id
		private_obj.username = user_info.role_name or user_info.username or user_info.gamename
		private_obj.sex = user_info.sex
		private_obj.camp = user_info.camp
		private_obj.prof = user_info.prof
		private_obj.avatar_key_small = user_info.avatar_key_small
		private_obj.level = user_info.level
		self.data:AddPrivateObj(private_obj.role_id, private_obj)
		-- self.view:OpenPrivate(ChatWGData.Instance:GetPrivateIndex(user_info.role_id))
		--self.chat_window:OpenPrivate(ChatWGData.Instance:GetPrivateIndex(user_info.role_id))
		--self.chat_window:UpdatePrivateView(true)
		--帮派聊天需要直接打开面板
		local guild_send_id = GuildWGData.Instance:GetSendID()
		if guild_send_id ~= nil then
			SocietyWGCtrl.Instance:OpenFriendChatView()
		end

		Log("不能添加自己")
	end
end

function ChatWGCtrl:IsOwns(from_uid)
	return from_uid == GameVoManager.Instance:GetMainRoleVo().role_id
end

-- function ChatWGCtrl:Open(tab_index, param_t)
-- 	self.view:Open(tab_index)
-- 	self.chat_window:Open(tab_index)

-- 	-- if param_t ~= nil and param_t.sub_view_name == SubViewName.SmallLabaSend then --大喇叭
-- 	-- 	self.view:Close()
-- 	-- 	self.view:OpenLabaSendView(1)
-- 	-- end

-- 	-- if param_t ~= nil and param_t.sub_view_name == SubViewName.BigLabaSend then --小喇叭
-- 	-- 	self.view:Close()
-- 	-- 	self.view:OpenLabaSendView(0)
-- 	-- end
-- end

function ChatWGCtrl:OpenChatWindow(index, is_invite_click)
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) and not is_invite_click then
		index = ChatTabIndex[CHANNEL_TYPE.GUILD]
	end
	-- self.chat_window:SetCurrSendChannel(index)
	if not self.chat_window:IsOpen() then
		self.chat_window:Open(index)
	end
	-- GlobalEventSystem:Fire(OtherEventType.CHAT_HANDLE)
end

function ChatWGCtrl:ShowAnswerContent(bo)
	if self.chat_window:IsOpen() then
		if self.chat_window.dati then
			self.chat_window.dati:SetActive(bo)
			-- self.chat_window:SetGuildListSizeDelta(bo)
		end
	end
	if not bo then
		GuildAnswerWGCtrl.Instance:CloseRankView()
	end
end

function ChatWGCtrl:Close()
	self.chat_window:Close()
end

function ChatWGCtrl:CloseChatWindow()
	self.chat_window:CloseChatView()
end

function ChatWGCtrl:OpenChatWindowMore(index)
	if self.chat_window:IsOpen() then
		self.chat_window:OperateActive(index)
	end
end

function ChatWGCtrl:OpenTransmitPopView()
	local is_open, str = ChatWGData.Instance:GetLaBaIsOpen()
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end

	if ChatWGData.Instance:GetIsInSilent() then --静默状态
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.IsInSlient)
		return
	end

	self.transmit_pop_view:Open()
end

function ChatWGCtrl:OpenFace()
	self.face_view:Open()
end

function ChatWGCtrl:CloseFace()
	self.face_view:Close()
end

function ChatWGCtrl:IsOpenHornPopView()
	return self.transmit_pop_view:GetHornInput()
end

function ChatWGCtrl:IsOpenTransmitPopView()
	return self.chat_window:IsOpen()
end

function ChatWGCtrl:ClaerChannelContent(index)
	-- self.view:ClaerChannelContent(index)
	self.chat_window:ClaerChannelContent(index)
end

function ChatWGCtrl:OpenBlacklistView()
	-- self.blacklist_view:Open()
end

function ChatWGCtrl:OpenAddBlacklistView()
	-- if self.add_blacklist_view then
	-- 	self.add_blacklist_view:Open()
	-- end
end

-- 添加一条系统消息
function ChatWGCtrl:AddSystemMsg(content, time)
	if self.data:IsPingBiChannel(CHANNEL_TYPE.SYSTEM) then
		return
	end
	time = time or TimeWGCtrl.Instance:GetServerTime()
	local msg_info = ChatWGData.CreateMsgInfo()
	msg_info.channel_type = CHANNEL_TYPE.SYSTEM
	msg_info.content = content
	msg_info.send_time_str = time--TimeUtil.FormatTable2HMS(os.date("*t", time))

	self:AddChannelMsg(msg_info, true)
end

-- 添加一条传闻消息
function ChatWGCtrl:AddRumorMsg(content, time, chat_type, fix_show_main, is_special)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.next_send_chuanwen_time < server_time then
		self.next_send_chuanwen_time = server_time + self.chuanwen_interval
	end

	if self.data:IsPingBiChannel(CHANNEL_TYPE.CHUAN_WEN) then
		return
	end

	local shield_hearsay = SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_HEARSAY)
	if shield_hearsay then
		return
	end
	time = time or TimeWGCtrl.Instance:GetServerTime()
	local msg_info = ChatWGData.CreateMsgInfo()
	local channel_type = CHANNEL_TYPE.CHUAN_WEN
	if is_special then
		channel_type = chat_type or CHANNEL_TYPE.CHUAN_WEN
	end
	msg_info.channel_type = channel_type
	msg_info.content = content
	msg_info.send_time_str = time--TimeUtil.FormatTable2HMS(os.date("*t", time))
	msg_info.msg_reason = (not is_special and fix_show_main) and CHAT_MSG_RESSON.NORMAL or CHAT_MSG_RESSON.GUILD_TIPS
	msg_info.fix_show_main = fix_show_main or false

	local function AddMsgInfo(new_msg_info)
		self:AddChannelMsg(new_msg_info, true)
		self:TryCalcH(new_msg_info)
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, new_msg_info)
	end

	local temp_chuanwen_list = self.data:GetTempChuanWenList()
	if next(temp_chuanwen_list) or (self.next_send_chuanwen_time - server_time > 0 and self.next_send_chuanwen_time - server_time < self.chuanwen_interval) then
		self.data:AddTempChuanWenList(msg_info)
		if self.chuanwen_time_quest then
			return
		end
		self.chuanwen_time_quest = GlobalTimerQuest:AddRunQuest(function()
			local new_server_time = TimeWGCtrl.Instance:GetServerTime()
			if self.next_send_chuanwen_time > new_server_time then
				return
			end
			if not next(temp_chuanwen_list) then
				self:ClearChuanWenTimeQuest()
				return
			end
			local new_msg_info = temp_chuanwen_list[1]
			AddMsgInfo(new_msg_info)
			--移除表头
			self.data:RemoveTempChuanWenList(1)
			--重新记录下次发送时间
			self.next_send_chuanwen_time = new_server_time + self.chuanwen_interval
		end, 0.1)
	else
		AddMsgInfo(msg_info)
	end
end

--添加一条仙盟系统信息
function ChatWGCtrl:AddGuildSystemMsg(content, time)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.next_send_guild_system_time < server_time then
		self.next_send_guild_system_time = server_time + self.guild_system_interval
	end

	time = time or TimeWGCtrl.Instance:GetServerTime()
	local msg_info = ChatWGData.CreateMsgInfo()
	msg_info.channel_type = CHANNEL_TYPE.GUILD
	msg_info.content = content
	msg_info.send_time_str = time--TimeUtil.FormatTable2HMS(os.date("*t", time))
	msg_info.msg_reason = CHAT_MSG_RESSON.GUILD_TIPS

	local function AddMsgInfo(new_msg_info)
		local temp_guild_system_list = self.data:GetGuildSystemList()
		for i=1,#temp_guild_system_list do
			local need_refresh = i == #temp_guild_system_list
			self:AddChannelMsg(temp_guild_system_list[i], need_refresh)
			self:TryCalcH(new_msg_info)
		end
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, temp_guild_system_list[#temp_guild_system_list])
		ChatWGData.Instance:ClearGuildSystemList()

		-- self:AddChannelMsg(msg_info,true)
		-- GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE, msg_info)
	end

	local temp_guild_system_list = self.data:GetGuildSystemList()
	if next(temp_guild_system_list) or (self.next_send_guild_system_time - server_time > 0 and self.next_send_guild_system_time - server_time <= self.guild_system_interval) then
		self.data:AddTempGuildSystemList(msg_info)
		if self.guild_system_time_quest then
			return
		end
		self.guild_system_time_quest = GlobalTimerQuest:AddRunQuest(function()
			local new_server_time = TimeWGCtrl.Instance:GetServerTime()
			if self.next_send_guild_system_time > new_server_time then
				return
			end
			if not next(temp_guild_system_list) then
				self:ClearGuildSystemTimeQuest()
				return
			end
			-- local new_msg_info = temp_guild_system_list[1]
			-- AddMsgInfo(new_msg_info)
			AddMsgInfo()
			--移除表头
			-- self.data:RemoveGuildSystemList(1)
			--重新记录下次发送时间
			self.next_send_guild_system_time = new_server_time + self.guild_system_interval
		end, 0.1)
	else
		AddMsgInfo(msg_info)
	end
end

function ChatWGCtrl:RefreshChannel(type, is_guild_answer, force_flush)
	if self.chat_window:IsOpen() then
		self.chat_window:RefreshChannel(type, is_guild_answer, force_flush)
	end
end

function ChatWGCtrl:OpenPrivate(index)
	-- self.view:OpenPrivate(index)
	self.chat_window:OpenPrivate(index)
end

function ChatWGCtrl:IsPrivateOpen()
	-- return self.view:IsPrivateOpen()
	return self.chat_window:GetCurWin()
end

-- function ChatWGCtrl:IsTransmitOpen()
-- 	-- return self.view:IsTransmitOpen()
-- 	return self.chat_window:IsTransmitOpen()
-- end

function ChatWGCtrl:OpenSettings()
	self.setting_view:Open()
end

function ChatWGCtrl:CloseSetting()
	self.setting_view:Close()
end

function ChatWGCtrl:OpenLocation()
	self.location_view:Open()
end

function ChatWGCtrl:CloseLocation()
	self.location_view:Close()
end

--根据城市名获取省份名
function ChatWGCtrl:GetProvinceNameByCityName(check_city_name)
	local city_list
	for p_index, v in pairs(Config.Location) do
		city_list = v.city
		for c_index, city_name in pairs(city_list) do
			if city_name == check_city_name then
				return v.province
			end
		end
	end

	return ""
end

function ChatWGCtrl:OpenCitySceleMenu(data_list, default_select_index, select_callback, pos_flag)
	self.city_select_menu:SetMenuData(data_list, default_select_index, select_callback, pos_flag)
	self.city_select_menu:Open()
end

function ChatWGCtrl:GetTransmitInputEdit()
	return self.chat_window:GetEditText()
end
function ChatWGCtrl:GetIsOpenGuild()
	return self.chat_window:IsOpen() and self.chat_window:GetCurrSendChannel() == CHANNEL_TYPE.GUILD
end

function ChatWGCtrl:AddTransmitInputEdit(text)
	return self.chat_window:AddEditText(text)
end


function ChatWGCtrl:GetHornInputEdit()
	return self.transmit_pop_view:GetEditText()
end

function ChatWGCtrl:AddHornInputEdit(text)
	return self.transmit_pop_view:AddEditText(text)
end


-- function ChatWGCtrl:GetMainRolePos()
-- 	-- self.view:GetMainRolePos()
-- 	self.chat_window:GetMainRolePos()
-- end

function ChatWGCtrl:GetEditTextByCurPanel()
	if self:IsOpenTransmitPopView() then
		return self:GetTransmitInputEdit()
	end
end

function ChatWGCtrl:SendChatText(content,not_report)
	self.chat_window:SendText(content,not_report)
end

function ChatWGCtrl:SendPrivateChatMsg(to_uid, text, content_type, is_immite, is_format, not_report)
	if ChatWGData.Instance:IsPHPVipLimitChat() then
		return
	end

	is_immite = is_immite == nil and true or is_immite
	is_format = is_format == nil and true or is_format

	-- -- 必须是好友才能私聊
	-- if nil == SocietyWGData.Instance:FindFriend(to_uid) then		-- 拒绝陌生私聊
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.IsFindFriend)
	-- 	return
	-- end

	local len = string.len(text)
	if len <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end

	if len >= COMMON_CONSTS.MAX_CHAT_MSG_LEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		return
	end

	if ChatWGData.ExamineEditText(text, 0) == false then return end


	-- 聊天内容检测
	if is_format then
		text = ChatWGData.Instance:FormattingMsg(text, content_type)
	end

	if content_type ~= CHAT_CONTENT_TYPE.AUDIO and content_type ~= CHAT_CONTENT_TYPE.FEES_AUDIO then
		text = ChatFilter.Instance:Filter(text)
	end

	self:SendSingleChat(to_uid, text, content_type, not_report)

	if is_immite then
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = RoleWGData.Instance:InCrossGetOriginUid()
		msg_info.from_cross_uuid = RoleWGData.Instance:InCrossGetOriginUid()
		msg_info.username = RoleWGData.Instance:GetRoleVo().role_name
		msg_info.sex = main_role_vo.sex
		msg_info.camp = main_role_vo.camp
		msg_info.prof = main_role_vo.prof
		msg_info.authority_type = main_role_vo.authority_type
		msg_info.avatar_key_small = main_role_vo.avatar_key_small
		msg_info.level = main_role_vo.level
		msg_info.vip_level = main_role_vo.vip_level
		msg_info.channel_type = CHANNEL_TYPE.PRIVATE
		msg_info.content = text
		msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
		msg_info.rank = SocietyWGCtrl.Instance:GetChatRank()
		msg_info.content_type = content_type
		msg_info.city_name = main_role_vo.city_name
		-- msg_info.channel_window_bubble_type = ChatWinData.Instance:GetCurrChooseBubble() or 0
		msg_info.channel_window_bubble_type = 0

		local vo = GameVoManager.Instance:GetMainRoleVo()
		msg_info.fashion_bubble = vo.appearance.fashion_bubble
		msg_info.fashion_photoframe = vo.appearance.fashion_photoframe
		msg_info.rank = vo.capability_rank
		-- print_error("添加信息自己",msg_info.from_uid,msg_info.from_cross_uuid,msg_info.fashion_bubble,msg_info.fashion_photoframe)

		self.data:AddPrivateMsg(to_uid, msg_info)
		-- if self:IsPrivateOpen() == CHANNEL_TYPE.PRIVATE then
			-- self.view:UpdatePrivateView()
			--self.chat_window:UpdatePrivateView(true)
		-- end
	end
end

function ChatWGCtrl:GetRandomContent()
	local config = ConfigManager.Instance:GetAutoConfig("chat_stranger_auto").chat
	local max_count = #config
	local random_num = math.random(1,max_count)
	local content = Language.Chat.DefaultRandomContent
	if config[random_num] then
		content = config[random_num].chat_item or Language.Chat.DefaultRandomContent
	end
	return content
end

function ChatWGCtrl:MainuiOpenCreate()
	-- self.main_role_id = role_vo.role_id
	local content = Language.Common.Warner2
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self:AddSystemMsg(content, server_time)
	self:AddRumorMsg(content, server_time, nil, true)
	self:CreateClientRichTex()
end

function ChatWGCtrl:HandleVoiceStart()
	self.chat_window:HandleVoiceStart()
end

function ChatWGCtrl:HandleVoiceStop(is_cancel_voice)
	self.chat_window:HandleVoiceStop(is_cancel_voice)
end

function ChatWGCtrl:SetCurrSendChannel(channel)
	self.chat_window:SetCurrSendChannel(channel)
end

function ChatWGCtrl:GetCurrSendChannel()
	return self.chat_window:GetCurrSendChannel()
end

function ChatWGCtrl:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node, plat_type, server_id, plat_name)
	self.chat_window:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node, plat_type, server_id, plat_name)
end

function ChatWGCtrl:CreateItemList()
	self.get_item_list = {}
end

function ChatWGCtrl:AddItem(item_id)
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		--item_cfg = Equip
		return
	end

	table.insert(self.get_item_list, item_id)
end

--客户端传闻列表（数字为秒数）
local ClientHearsayList = {
	-- [CHAT_LINK_TYPE.WO_CHONGZHI] = 30,
	[CHAT_LINK_TYPE.SUIJI_GONGGAO] = 600,
}

function ChatWGCtrl:StopClientHearsayTimeQuest(key)
	for k, _ in pairs(ClientHearsayList) do
		if key == nil or key == k then
			local time_quest = self.client_hearsay_time_quest[k]
			if time_quest then
				GlobalTimerQuest:CancelQuest(time_quest)
				time_quest = nil
			end
		end
	end
end

-- 假传闻（审核服不显示）
function ChatWGCtrl:CreateClientRichTex()
	-- if not IS_AUDIT_VERSION then
	self:StopClientHearsayTimeQuest()
	for k, v in pairs(ClientHearsayList) do
		self:CreateOneClientRichText(k, v)
	end
	-- end
end

function ChatWGCtrl:CreateOneClientRichText(k, time)
	if k == CHAT_LINK_TYPE.WO_CHONGZHI and TimeWGCtrl.Instance:GetCurOpenServerDay() > 7 then
		return
	end

	if k == CHAT_LINK_TYPE.SUIJI_GONGGAO then
		self:SuiJiGongGaoDealt(k,time)
		return
	end

	self.client_hearsay_time_quest[k] = GlobalTimerQuest:AddRunQuest(function()
		if k == CHAT_LINK_TYPE.WO_CHONGZHI and TimeWGCtrl.Instance:GetCurOpenServerDay() > 7 then
			self:StopClientHearsayTimeQuest(k)
			return
		end

		-- 随即名
		local rand_num = math.floor(math.random(1, 200))
		local rand_name = CommonDataManager.GetRandomName(rand_num)
        --local rand_name = RobertMgr.Instance:GetRandomName(rand_num % 2)
		-- 随机传闻
		local rich_text = Language.ClientRichText[k]
        local content = ""

		content = string.format(rich_text, rand_name, k)
		TipWGCtrl.Instance:ShowSystemNotice(content)
		self:AddRumorMsg(content, TimeWGCtrl.Instance:GetServerTime(), nil, true)
	end, time)
end

function ChatWGCtrl:CheckIsShowAnswer()
	return self.chat_window:CheckIsShowAnswer()
end

--随机公告单独处理
function ChatWGCtrl:SuiJiGongGaoDealt(k,time)
	self.client_hearsay_time_quest[k] = GlobalTimerQuest:AddRunQuest(function()
		if IS_ON_CROSSSERVER then
			return
		end

		local max_num = #Language.ClientRichText[k]
		if self.suiji_gonggao_num == nil then
			self.suiji_gonggao_num = 1
		else
			self.suiji_gonggao_num = (self.suiji_gonggao_num + 1) > max_num and 1 or (self.suiji_gonggao_num + 1)
        end

        if TimeWGCtrl.Instance:GetCurOpenServerDay() > 7 and self.suiji_gonggao_num == 12 then --0元购 开服7天后不显示
            self.suiji_gonggao_num = (self.suiji_gonggao_num + 1) > max_num and 1 or (self.suiji_gonggao_num + 1)
        end
		-- local rand_num = math.floor(math.random(1, max_num))
		local rich_text = Language.ClientRichText[k][self.suiji_gonggao_num]
		-- local rand_namenum = math.floor(math.random(1, 200))
		-- local rand_name = CommonDataManager.GetRandomName(rand_namenum)
		-- local content = ""
		-- content = string.format(rich_text, rand_name)
		TipWGCtrl.Instance:ShowSystemNotice(rich_text)
		self:AddRumorMsg(rich_text, TimeWGCtrl.Instance:GetServerTime(), nil, true)
	end, time)
end

function ChatWGCtrl:GetChannel()
	return  self.chat_window:GetChannel()
end

--[[--策划需求:屏蔽战队频道
function ChatWGCtrl:OnZhanDuiLogChange(content, timestamp)
	local msg_info = ChatWGData.CreateMsgInfo()
	--msg_info.channel_type = CHANNEL_TYPE.SYSTEM
	msg_info.from_uid = 0
	msg_info.username = ""
	msg_info.sex = 0
	msg_info.camp = 0
	msg_info.prof = 0
	msg_info.authority_type = 0
	msg_info.tuhaojin_color = 0
	msg_info.level = 0
	msg_info.msg_reason = CHAT_MSG_RESSON.GUILD_TIPS
	msg_info.vip_level = 0
	msg_info.channel_type = CHANNEL_TYPE.ZHANDUI3V3
	msg_info.content = content
	msg_info.send_time_str = timestamp

	if not self.is_receive_complete then
		table.insert(self.zhandui_log_msg, msg_info)
		--print_error("德玛>>>>>>> 返回*****************************************************")
		return
	end

	self:AddChannelMsg(msg_info, true)
end
--]]

--添加货币增加系统消息
function ChatWGCtrl:AddMoneyChangeMsg(money_type, change_num)
	if not money_type or not change_num then
		return
	end
	local item_id = self:GetItemIdByMoneyType(money_type)
	local str = string.format(Language.Chat.GetItemMsg, item_id, change_num)
	self:AddSystemMsg(str)
end

function ChatWGCtrl:GetItemIdByMoneyType(money_type)
	local item_id = 0
	if money_type == MoneyType.XianYu then
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
	elseif money_type == MoneyType.BangYu then
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL
	elseif money_type == MoneyType.YuanBao then
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO
	elseif money_type == MoneyType.ShengWang then
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR
	elseif money_type == MoneyType.JinBi then
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_COIN
	elseif money_type == MoneyType.ZhanLing then
		item_id = COMMON_CONSTS.ITEM_ZHAN_LING
	elseif money_type == MoneyType.ZhanHun then
		item_id = COMMON_CONSTS.ITEM_ZHAN_HUN
	elseif money_type == MoneyType.GuiZuJiYi then
		item_id = COMMON_CONSTS.ITEM_GUIZU_JIYI
	elseif money_type == MoneyType.ZhenQi then
		item_id = COMMON_CONSTS.ITEM_ZHEN_QI
	elseif money_type == MoneyType.GuaXiangJingHua then
		item_id = COMMON_CONSTS.ITEM_GUAXIANG_JINGHUA
	elseif money_type == MoneyType.GuildGongXian then
		item_id = COMMON_CONSTS.ITEM_GUILD_GONGXIAN
	elseif money_type == MoneyType.GuildJianSheDu then
		item_id = COMMON_CONSTS.ITEM_GUILD_JIANSHEDU
	elseif money_type == MoneyType.HongMengJingHua then
		item_id = COMMON_CONSTS.ITEM_HONGMENG_JINGHUA
	elseif money_type == MoneyType.Chivalrous then
		item_id = COMMON_CONSTS.ITEM_HONGMENG_JINGHUA
	else
		print_error("找不到货币类型", money_type)
	end
	return item_id
end
--[[
function ChatWGCtrl:OnEnterChangeScene()
	--不在跨服场景清空跨服聊天数据
	if not IS_ON_CROSSSERVER then
		self.data:RemoveGetChannel(CHANNEL_TYPE.CROSS)
		self.data:RemoveMainUIChannel(CHANNEL_TYPE.CROSS)
		--清掉主界面的聊天消息
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
	end
end
--]]
--添加一条频道消息
function ChatWGCtrl:AddChannelMsg(msg_info, need_refresh)
	if not self:CanAddMsg(msg_info) then
		return
	end
	self.data:AddChannelMsg(msg_info)
	if need_refresh then
		self:RefreshChannel(msg_info.channel_type)
		GlobalEventSystem:Fire(OtherEventType.CHAT_CHANGE, msg_info)
	end
end

--TODO
function ChatWGCtrl:CanAddMsg(msg_info)
	return true
end

function ChatWGCtrl:SendMainRolePos(channel)
    if Scene.Instance:GetSceneId() == 4600 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.SceneLimitPos)
        return
    end
	local main_role = Scene.Instance.main_role
	if nil ~= main_role then
		local x, y = main_role:GetLogicPos()
		local main_role_vo = main_role.vo
		local plat_name = main_role_vo.cur_plat_name
		local plat_type = main_role_vo.plat_type
		local server_id = main_role_vo.current_server_id
		local pos_msg = string.format(Language.Chat.PosFormat, Scene.Instance:GetSceneName(), x, y, plat_name, server_id)

		ChatWGData.Instance:InsertPointTab((channel and channel == CHANNEL_TYPE.CROSS) and 1 or 0)
		-- 聊天内容检测
		local message = ChatWGData.Instance:FormattingMsg(pos_msg, CHAT_CONTENT_TYPE.TEXT)
		if "" == message then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
			return
		end

		ChatWGCtrl.Instance:SendChannelChat(channel or CHANNEL_TYPE.WORLD, message, CHAT_CONTENT_TYPE.TEXT,nil,nil,true)
	end
end

--打开聊天Vip等级限制提示面板
function ChatWGCtrl:OpenChatVipLimitTipView()
	local up_level = VipWGData.Instance:GetVIPZeroBuyCfg("arrive_level") or 0
	if up_level > 0 then
		if not self.chat_vip_limit_tip_view then
			self.chat_vip_limit_tip_view = ChatVipLimitTipView.New()
		end
		if not self.chat_vip_limit_tip_view:IsOpen() then
			self.chat_vip_limit_tip_view:Open()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VIP6Error)
	end
end

function ChatWGCtrl:FlushChatViewCheckAiteTimer()
	if self.chat_window:IsOpen() then
		self.chat_window:FlushCheckAiteTimerState()
	end
end

local Chat_Type_Cell_Tab = {
	[CHANNEL_TYPE.SYSTEM] = "ChatSysCell",
	[CHANNEL_TYPE.CHUAN_WEN] = "ChatSysCell",
	[CHANNEL_TYPE.WORLD] = "ChatCell",
	[CHANNEL_TYPE.GUILD] = "ChatCell",
	[CHANNEL_TYPE.TEAM] = "ChatCell",
	[CHANNEL_TYPE.ZUDUI] = "ChatCell",
	[CHANNEL_TYPE.CROSS] = "ChatCell",
	-- [CHANNEL_TYPE.ZHANDUI3V3] = "ChatCell",
	[CHANNEL_TYPE.SCENE] = "ChatCell",
	[CHANNEL_TYPE.PRIVATE] = "ChatCell",

}

function ChatWGCtrl:InitCalcHeigCell()
	if self.calc_init then
		return
	end

	local bundle = "uis/view/chat_ui_prefab"
	local async_loader = AllocAsyncLoader(self, "render_calc_h_chat")
	async_loader:SetParent(UILayer)
	self.calc_init = true

	async_loader:Load(bundle, "ChatSysCell", function (obj)
		if not self.calc_init then
			if obj ~= nil then
				async_loader:Destroy()
			end

			return
		end

		obj.transform.localPosition = Vector3(9999, 9999, 0)
		self.chat_measuring_list["ChatSysCell"] = ChatCell.New(obj.gameObject)
		self.chat_measuring_list["ChatSysCell"]:SetIsCalchighCell(true)
		self:TryStartInitChatMsg()
	end)

	async_loader = AllocAsyncLoader(self, "render_calc_h_sys")
	async_loader:SetParent(UILayer)
	async_loader:Load(bundle, "ChatCell", function (obj)
		if not self.calc_init then
			if obj ~= nil then
				async_loader:Destroy()
			end

			return
		end

		obj.transform.localPosition = Vector3(9999, 9999, 0)
		self.chat_measuring_list["ChatCell"] = ChatCell.New(obj.gameObject)
		self.chat_measuring_list["ChatCell"]:SetIsCalchighCell(true)
		self:TryStartInitChatMsg()
	end)
end

function ChatWGCtrl:TryStartInitChatMsg()
	if self.chat_measuring_list["ChatSysCell"] == nil or self.chat_measuring_list["ChatCell"] == nil then
		return
	end

	if self.is_init_msg_heig then
		return
	end

	self.is_init_msg_heig = true
	self:CheckIsNeedTimer()
end

function ChatWGCtrl:TryInitChatMsgH()
	self.wait_init_msg_list = {}
	local index = 1
	local sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.SYSTEM)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index+ 1
	end

	local sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.CHUAN_WEN)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end

	sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.WORLD)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end

	sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.GUILD)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end

	sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.TEAM)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end

	sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.ZUDUI)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end

	sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.CROSS)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end

	--[[--策划需求:屏蔽战队频道
	sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.ZHANDUI3V3)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end
	--]]

	sys = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.PRIVATE)
	for k,v in pairs(sys.msg_list) do
		self.wait_init_msg_list[index] = v
		index = index + 1
	end

	self:TryStartInitChatMsg()
end

function ChatWGCtrl:CheckIsNeedTimer()
	if self.time_state_timer ~= nil then
		return
	end

	if not self.time_state_timer then
		self.time_state_timer = GlobalTimerQuest:AddRunQuest(function()
			self:TryCalcHStep()
		end, 0.5)
	end
end

function ChatWGCtrl:TryCalcHStep()
	if self.wait_init_msg_list == nil then
		return
	end

	if #self.wait_init_msg_list <= 0 then
		self:RemoveInitMsgTimer()
		return
	end

	for i = 1, 20 do
		if #self.wait_init_msg_list > 0 then
			local data = table.remove(self.wait_init_msg_list, 1)
			self:TryCalcH(data)
		end
	end
end

function ChatWGCtrl:TryCalcH(data)
	if data == nil or data.channel_type == nil then
		return
	end

	local height = ChatWGData.Instance:GetChannelItemHeight(data.channel_type, data.msg_id)
	if height ~= nil and height > 0 then
		return
	end

	local key = Chat_Type_Cell_Tab[data.channel_type]
	if key ~= nil and self.chat_measuring_list[key] ~= nil then
		self.chat_measuring_list[key]:SetData(data)
		height = self.chat_measuring_list[key]:GetContentHeight() --计算自身的高度
		ChatWGData.Instance:SetChannelItemHeight(data.channel_type, data.msg_id, height)
	end
end

function ChatWGCtrl:RemoveInitMsgTimer()
	if self.time_state_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.time_state_timer)
	end

	self.time_state_timer = nil
end

function ChatWGCtrl:FinishCalcH()
	self:RemoveInitMsgTimer()
	self.wait_init_msg_list = {}
	self.is_init_msg_heig = true
	self.calc_init = true
end

function ChatWGCtrl:OnSceneChangeComplete(old_scene_type, new_scene_type)
	self:CheckClearSceneChannelMag()
end

function ChatWGCtrl:OnEnterOtherServer()
	self:CheckClearSceneChannelMag()
end

--检测清除附近(场景)频道聊天信息
function ChatWGCtrl:CheckClearSceneChannelMag()
	local old_key = self.data:GetCacheSceneChannelKey()
	local new_key = self.data:GetCurSceneChannelKey()
	-- print_error("FFF===== old_key, new_key", old_key, new_key)
	if old_key ~= new_key then
		self.data:SetCacheSceneChannelKey(new_key)
		--清除@列表信息
		self:ClearAiTeInfoListByChannel(CHANNEL_TYPE.SCENE)
		self.data:ClearAiteInfoByChannel(CHANNEL_TYPE.SCENE)
		self.data:RemoveGetChannel(CHANNEL_TYPE.SCENE)

		--刷新聊天面板
		self:RefreshChannel()

		--主界面信息清空
		self.data:RemoveMainUIChannel(CHANNEL_TYPE.SCENE)
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)--刷新

		self.data:ResetSceneChatBubbleInfo()
	end
end

--设置显示场景中,玩家聊天信息
function ChatWGCtrl:ShowSceneRoleChatBubble(role_id, content)
	local show_bubble_role = self:CheckRoleInMyView(role_id)
	if show_bubble_role ~= nil then--该玩家在视野范围内,显示聊天气泡框
		-- print_error("FFFF===== 该玩家在视野范围内,显示聊天气泡框")
		if show_bubble_role:IsBubbleVisible() then--气泡还在展示
			-- print_error("FFFF===== 该玩家气泡还在展示", role_id)
			-- show_bubble_role:HideBubble()
			return
		end
		self.data:ChangeSceneChatBubbleShowNum(1)
		self.data:DelSceneChatBubbleFirstInfo()
		local bubble_cb = function()
			self.data:ChangeSceneChatBubbleShowNum(-1)
			self:TryShowNextSceneRoleChatBubble()
		end
		show_bubble_role:ChangeBubble(content, SCENE_CHAT_BUBBLE_TIME, bubble_cb)
	else--缓存信息中,该玩家已经脱离我的视野范围
		self.data:DelSceneChatBubbleFirstInfo()
		self:TryShowNextSceneRoleChatBubble()
	end
end

function ChatWGCtrl:TryShowNextSceneRoleChatBubble()
	local cur_show_num = self.data:GetSceneChatBubbleShowNum()
	-- print_error("FFFF====== 当前场景显示气泡数量:", cur_show_num)
	if cur_show_num < SCENE_CHAT_BUBBLE_MAX_NUM then--小于限制显示数量,直接显示
		local show_info = self.data:GetSceneChatBubbleInfo()
		-- print_error("FFFF====== show_info", show_info)
		if show_info and show_info[1] then
			self:ShowSceneRoleChatBubble(show_info[1].role_id, show_info[1].content)
		end
	end
end

function ChatWGCtrl:CheckRoleInMyView(role_id)
	-- print_error("FFF=====111 role_id", role_id)
	local my_role_id = RoleWGData.Instance:GetOriginUid()
	local check_role = nil
	if role_id == my_role_id then--玩家本身
		check_role = Scene.Instance:GetMainRole()
	else
		local role_list = Scene.Instance:GetRoleList()
		if IsEmptyTable(role_list) then
			return nil
		end

		check_role = role_list[role_id]
		if not check_role then
			for k, v in pairs(role_list) do
				if v.vo and v.vo.origin_uid == role_id then
					check_role = v
					break
				end
			end
		end
	end
	return check_role
end
