require("game/browse/browse_view")
require("game/browse/browse_attr")
require("game/browse/browse_wg_data")
require("game/browse/browse_head_view")
require("game/browse/browse_xian_qi_item")

-- 查看角色信息
BrowseWGCtrl = BrowseWGCtrl or BaseClass(BaseWGCtrl)

function BrowseWGCtrl:__init()
	if BrowseWGCtrl.Instance ~= nil then
		ErrorLog("[BrowseWGCtrl] Attemp to create a singleton twice !")
	end
	BrowseWGCtrl.Instance = self

	self.browse_data = BrowseWGData.New()
	self.browse_view = BrowseView.New()
	self.browse_head_view = BrowseHeadView.New()

	self.open_role_id = 0

	self.request_callback_list = {}

	self:RegisterAllProtocols()
end

function BrowseWGCtrl:__delete()
	self.browse_view:DeleteMe()
	self.browse_view = nil

	self.browse_data:DeleteMe()
	self.browse_data = nil

	if self.browse_head_view then
		self.browse_head_view:DeleteMe()
		self.browse_head_view = nil
	end


	self.request_callback_list = {}

	BrowseWGCtrl.Instance = nil
	self.has_set_meili = nil
end

function BrowseWGCtrl:GetBrowseView()
	return self.browse_view
end

function BrowseWGCtrl:HeadOpen()
	self.browse_head_view:Open()
end

function BrowseWGCtrl:GetBrowseData()
	return self.browse_data
end

function BrowseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSEvaluateRole)
	self:RegisterProtocol(CSQueryRoleInfo)
	self:RegisterProtocol(CSCrossQueryRoleInfo)
	self:RegisterProtocol(CSGlobalQueryRoleInfo)

	self:RegisterProtocol(SCGetRoleBaseInfoAck, "OnGetRoleBaseInfoAck")
	self:RegisterProtocol(SCAllCharmChange, "OnAllCharmChange")

	self:RegisterProtocol(SCQueryNofityWhoSeeYou, "OnSCQueryNofityWhoSeeYou")

	self:RegisterProtocol(CSSingleQuery) --圣装查看
	self:RegisterProtocol(SCSingleQueryXianjieOneEquip, "OnSCSingleQueryXianjieOneEquip")
	self:RegisterProtocol(SCSingleQueryXianjieAllEquip, "OnSCSingleQueryXianjieAllEquip")
end

--is_who_see_you 0/1 查看其他玩家信息时，其他玩家需要系统弹出错误码：XXX正在偷偷打量你
function BrowseWGCtrl:OpenWithUid(uid, browse_index, is_cross, plat_type,is_who_see_you)
	if uid < 0 then
		ErrorLog("BrowseWGCtrl:OpenWithUid role_id ==", uid)
		return
	end

	self.open_role_id = uid
	if IS_ON_CROSSSERVER or is_cross then
		if not UserVo.IsCrossServer(uid) then
			local plat_type1 = plat_type or RoleWGData.Instance.role_vo.plat_type
			self:SendGlobalQueryRoleInfo(plat_type1, uid, uid, nil, is_who_see_you)
		else
			local plat_type1 = plat_type or RoleWGData.Instance.role_vo.plat_type
			self:SendCrossQueryRoleInfo(plat_type1, uid, BindTool.Bind(self.OpenByCrossQuery, self, self.open_role_id), is_who_see_you)
		end
	else
		self:SendQueryRoleInfoReq(uid, nil, is_who_see_you)
	end
end

function BrowseWGCtrl:OpenByCrossQuery(target_uid, protocol)
	if target_uid and self.open_role_id == target_uid then
		self.open_role_id = protocol.role_id
	end
end

-- 给赞
function BrowseWGCtrl:SendEvaluateRoleReq(target_uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEvaluateRole)
	protocol.uid = target_uid
	protocol.rank_type = 0
	protocol:EncodeAndSend()
end

-- 请求角色信息
function BrowseWGCtrl:SendQueryRoleInfoReq(target_uid,callback,is_who_see_you)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQueryRoleInfo)
	protocol.target_uid = target_uid
	protocol.is_who_see_you = is_who_see_you or 0
	protocol:EncodeAndSend()

	if callback then
		for k, v in pairs(self.request_callback_list) do
			if v.callback == callback then
				return
			end
		end
		table.insert(self.request_callback_list, {["role_id"] = target_uid, ["callback"] = callback,})
	end
end

-- 请求跨服角色信息 target_uid为跨服id,返回的数据中的role_id为原服id
local corss_query_t = {}
function BrowseWGCtrl:SendCrossQueryRoleInfo(plat_type, target_uid,callback,is_who_see_you)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossQueryRoleInfo)
	protocol.plat_type = plat_type
	protocol.target_uid = target_uid
	protocol.is_who_see_you = is_who_see_you or 0
	protocol:EncodeAndSend()
	if callback then
		for k, v in pairs(self.request_callback_list) do
			if v.callback == callback then
				return
			end
		end
		corss_query_t[callback] = true
		table.insert(self.request_callback_list, {["role_id"] = target_uid, ["callback"] = callback,})
	end

end

-- 跨服组查询玩家信息
function BrowseWGCtrl:SendGlobalQueryRoleInfo(plat_type, target_uid,open_role_id,callback,is_who_see_you)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGlobalQueryRoleInfo)
	protocol.plat_type = plat_type
	protocol.target_uid = target_uid
	protocol.is_who_see_you = is_who_see_you or 0
	protocol:EncodeAndSend()
	if callback then
		for k, v in pairs(self.request_callback_list) do
			if v.callback == callback then
				return
			end
		end
		table.insert(self.request_callback_list, {["role_id"] = target_uid, ["callback"] = callback,})
	end
	self.open_role_id = open_role_id or self.open_role_id
end

-- 角色信息返回
function BrowseWGCtrl:OnGetRoleBaseInfoAck(protocol)
	-- 头像
	AvatarManager.Instance:SetAvatarKey(protocol.role_id, protocol.avatar_key_big, protocol.avatar_key_small)
	AvatarManager.Instance:SetAvatarFrameKey(protocol.role_id, protocol.appearance.fashion_photoframe)

	self:RoleInfoReqCallBack(protocol)
	self:RoleInfoReqTemp(protocol)
	RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
	if self.open_role_id ~= protocol.role_id and 0 ~= protocol.role_id then
		return
	end
	self.open_role_id = 0

	local lover_info = protocol.lover_info
	if 0 ~= lover_info.lover_uid then
		AvatarManager.Instance:SetAvatarKey(lover_info.lover_uid, lover_info.lover_avatar_key_big, lover_info.lover_avatar_key_small)
	end
	self.last_check_uuid = nil
	self.last_check_slot = nil
	self.browse_data:SetRoleInfo(protocol)
	self.browse_view:Open()
	self.browse_view:Flush()
	--self.browse_view:Open()
end

function BrowseWGCtrl:BrowseRobotInfo(data)
    self.browse_data:SetRoleInfo(data)
	self.browse_view:Open()
	self.browse_view:Flush()
end

-- 魅力值改变
function BrowseWGCtrl:OnAllCharmChange(protocol)
	if RoleWGData.Instance:InCrossGetOriginUid() == protocol.uid then
		local origin_all_charm = RoleWGData.Instance:GetRoleVo()
		if self.has_set_meili then
			if origin_all_charm.all_charm ~=  protocol.all_charm then
				local str = string.format(Language.ProfessWall.ProfessMeiLiZhiTis, protocol.all_charm - origin_all_charm.all_charm)
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end
		end
		RoleWGData.Instance:SetAttr("all_charm", protocol.all_charm)
		self.has_set_meili = true
	end
	if self.browse_data:GetRoleId() == protocol.uid then
		self.browse_data:SetAllCharm(protocol.all_charm)
		if self.browse_view:IsOpen() then
			self.browse_view:Flush(0, "all_charm")
			--self.browse_view:Flush()
		end
	end
end

-- 其它模块请求角色信息
function BrowseWGCtrl:BrowRoelInfo(role_id, callback, plat_type, is_cross)
	if not role_id or role_id < 0 or nil == callback then
		return
	end

	if IS_ON_CROSSSERVER or is_cross then
		if not UserVo.IsCrossServer(role_id) then
			self:SendGlobalQueryRoleInfo(plat_type or RoleWGData.Instance.role_vo.plat_type, role_id, nil, callback)
		else
			self:SendCrossQueryRoleInfo(plat_type or RoleWGData.Instance.role_vo.plat_type, role_id, callback)
		end
		return
	else
		self:SendQueryRoleInfoReq(role_id, callback)
	end

	for k, v in pairs(self.request_callback_list) do
		if v.callback == callback then
			return
		end
	end

	table.insert(self.request_callback_list, {["role_id"] = role_id, ["callback"] = callback,})
end

function BrowseWGCtrl:AllReqRoleInfo(uid, plat_type, callback)
	if uid < 0 then
		ErrorLog("BrowseWGCtrl:OpenWithUid role_id ==", uid)
		return
	end

	if IS_ON_CROSSSERVER then
		if not UserVo.IsCrossServer(uid) then
			local plat_type1 = plat_type or RoleWGData.Instance.role_vo.plat_type
			self:SendGlobalQueryRoleInfo(plat_type1, uid, nil, callback)
		else
			local plat_type1 = plat_type or RoleWGData.Instance.role_vo.plat_type
			--print_error(plat_type1,uid)
			self:SendCrossQueryRoleInfo(plat_type1, uid,callback)
		end
	else
		self:BrowRoelInfo(uid, callback)
	end
end

function BrowseWGCtrl:RoleInfoReqCallBack(protocol)
	local count = #self.request_callback_list
	if count > 0 then
		local has_call_back = false
		local info = nil
		for i = count, 1, -1 do
			info = self.request_callback_list[i]
			if info.role_id == protocol.role_id then
				info.callback(protocol)
				table.remove(self.request_callback_list, i)
				has_call_back = true
			end
		end
		if not has_call_back then
			info = self.request_callback_list[1]
			if info and corss_query_t[info.callback] then
				corss_query_t[info.callback] = nil
				info.callback(protocol)
				table.remove(self.request_callback_list, 1)
			end
		end
	end
end
--多次刷新会导致仙盟模型出错，所以单独传递
function BrowseWGCtrl:RoleInfoReqTemp(protocol)
	GuildWGData.Instance:SetRoleBaseInfoAck(protocol)
end

---[[ F2
local RoleHeadDefItemsKey = {
	ShowInfo = 1,
	AddFriend = 2,
	GiveFlower = 3,
	Profess = 4,
}
-- 默认菜单项
local RoleHeadDefItems = {
	Language.Menu.ShowInfo,
	Language.Menu.AddFriend,
	Language.Menu.GiveFlower,
	--Language.Menu.Profess,
}

local anchored_osition_cache = Vector2(0, 0)
function BrowseWGCtrl:ShowOtherRoleInfo(role_id, anchored_osition, is_cross, plat_type, show_menu, callback)
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_vo.role_id == role_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.OtherErrors1)
		return
	elseif COMMON_CONSTS.ROBERT_ROLE_ID == role_id then
		return
	end
	anchored_osition_cache = anchored_osition
	if is_cross then
		self:SendCrossQueryRoleInfo(plat_type, role_id, BindTool.Bind(self.ReqRoleInfoCallBack, self, is_cross, show_menu, callback))
	else
		self:SendQueryRoleInfoReq(role_id, BindTool.Bind(self.ReqRoleInfoCallBack, self, is_cross, show_menu, callback))
	end
end

function BrowseWGCtrl:ReqRoleInfoCallBack(is_cross, show_menu, callback, protocol)
	local pos = anchored_osition_cache or Vector2(0, 0)
	UiInstanceMgr.Instance:OpenCustomMenu(show_menu or RoleHeadDefItems, pos, callback or BindTool.Bind(self.OnClickMenuButton, self, is_cross), protocol, nil, nil, nil, protocol)
	anchored_osition_cache = nil
end

function BrowseWGCtrl:OnClickMenuButton(is_cross, index, sender, param)
	if index == RoleHeadDefItemsKey.ShowInfo then
		self.browse_data:SetRoleInfo(param)
		self.browse_view:Open()
		self.browse_view:Flush()
	elseif index == RoleHeadDefItemsKey.AddFriend then
		if not is_cross then
			SocietyWGCtrl.Instance:IAddFriend(param.role_id)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
		end
	elseif index == RoleHeadDefItemsKey.GiveFlower then
		if not is_cross then
			FlowerWGCtrl.Instance:OpenSendFlowerView(param.role_id, param.role_name, param.sex, param.prof)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
		end
	elseif index == RoleHeadDefItemsKey.Profess then
		if not is_cross then
			local is_friend = SocietyWGData.Instance:CheckIsFriend(param.role_id)
			if is_friend then
				-- ProfessWallWGData.Instance:SetDefaultInfo(param.role_id,nil)
				-- ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
			else
				local data = {}
				data.role_id = param.role_id
				data.role_name = param.role_name
	            data.sex = param.sex
	            data.prof = param.prof
				SocietyWGCtrl.Instance:OpenAddTipsPanel(data)
			end
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
		end
	end
end

function BrowseWGCtrl:TestWhoSeeYou(role_id)
	local protocol = {
		role_name = "名字很长11",
		role_id = role_id,
		plat_type = 2,
	}
	-- BrowseWGCtrl.Instance:TestWhoSeeYou(131274)
	self:OnSCQueryNofityWhoSeeYou(protocol)
end

--被其他玩家查看信息时，自己在线的情况下，系统弹出错误码：XXX正在偷偷打量你
function BrowseWGCtrl:OnSCQueryNofityWhoSeeYou(protocol)
	local role_name = protocol.role_name
	local role_id = protocol.role_id
    local plat_type = protocol.plat_type
    local merge_server_id = protocol.merge_server_id
	--{who_see_you;rold_id(%d) role_name(%s) plat_type(%d),merge_server_id(%d)}  {go_to_see;rold_id(%d) plat_type(%d),merge_server_id(%d)}
	local str = string.format(Language.Browse.WhoSeeYou,role_name)
	local chuanwen_str = string.format(Language.Browse.WhoSeeYouChuanWen,role_id,role_name,plat_type,merge_server_id, role_id, plat_type, merge_server_id)
	SysMsgWGCtrl.Instance:ErrorRemind(str)
	if chuanwen_str ~= "" then
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.vip_level = 0
		msg_info.msg_reason = CHAT_MSG_RESSON.NORMAL
		msg_info.channel_type = CHANNEL_TYPE.CHUAN_WEN
		msg_info.content = chuanwen_str
		msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
		msg_info.is_add_team = false
		msg_info.title_text = Language.ChannelColor2[10]
		msg_info.fix_show_main = true
		ChatWGCtrl.Instance:AddChannelMsg(msg_info, true)
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
	end
end

--]]

-- 给赞
function BrowseWGCtrl:SendCSSingleQueryReq(plat_type,target_uid,viewer_uid,reason_type,slot)
	if not target_uid then return end
	-- target_uid = tonumber(target_uid)
	-- viewer_uid = RoleWGData.Instance:GetUUid()
	-- viewer_uid = TwoUIntToLL(viewer_uid.temp_high, viewer_uid.temp_low)
	reason_type = reason_type or 0
	if reason_type == 0 then
		if not slot then
			return
		end
	end
	slot = slot or 0
	if reason_type == 0 then
		if not self.last_check_uuid or self.last_check_uuid ~= target_uid then
			self.last_check_uuid = target_uid
			self.last_check_slot = slot
		elseif self.last_check_uuid == target_uid and self.last_check_slot == slot then
			return
		end
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSSingleQuery)
	protocol.target_uid = target_uid
	protocol.plat_type = plat_type
	protocol.reason_type = reason_type-- 0 单个 p0 slot 1 所有
	protocol.param_0 = slot
	protocol:EncodeAndSend()
end



function BrowseWGCtrl:OnSCSingleQueryXianjieOneEquip(protocol)
	self.browse_data:OnSCSingleQueryXianjieOneEquip(protocol)
	if self.browse_view:IsOpen() then
		self.browse_view:Flush()
	end
end



function BrowseWGCtrl:OnSCSingleQueryXianjieAllEquip(protocol)
	self.browse_data:OnSCSingleQueryXianjieAllEquip(protocol)
	if self.browse_view:IsOpen() then
		self.browse_view:Flush()
	end
end

function BrowseWGCtrl:GetOpenRoleID()
	return self.open_role_id or 0
end

function BrowseWGCtrl:CheckCurOpenXianJieEquipBySlotIndex(plat_type,role_id,slot_index)
	if not role_id or role_id == 0 then
		print_error("role_id is illegal")
		return
	end
	self:SendCSSingleQueryReq(plat_type,role_id,nil,0,slot_index)
end

function BrowseWGCtrl:CheckCurOpenAllXianJieEquip(plat_type, role_id)
	if not role_id or role_id == 0 then
		print_error("role_id is illegal")
		return
	end

	self:SendCSSingleQueryReq(plat_type, role_id, nil, 1)
end