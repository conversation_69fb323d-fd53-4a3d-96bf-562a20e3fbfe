TripleRechargeWGData = TripleRechargeWGData or BaseClass()

function TripleRechargeWGData:__init()
	if TripleRechargeWGData.Instance then
		error("[TripleRechargeWGData] Attempt to create singleton twice!")
		return
	end

    TripleRechargeWGData.Instance = self

    self:InitParam()
    self:InitConfig()
end

function TripleRechargeWGData:__delete()
    TripleRechargeWGData.Instance = nil
end

function TripleRechargeWGData:InitParam()
    self.grade = -1
    self.buy_flag = -1
end

function TripleRechargeWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_triple_recharge_auto")
    self.recharge_cfg = ListToMap(cfg.recharge, "grade", "seq")
end

function TripleRechargeWGData:SetAllInfo(protocol)
    self.grade = protocol.grade
    self.buy_flag = protocol.buy_flag
end

function TripleRechargeWGData:GetCurGrade()
    return self.grade
end

function TripleRechargeWGData:GetCurGradeCfg()
    return self.recharge_cfg[self.grade] or {}
end

function TripleRechargeWGData:GetCurGradeCfgBySeq(seq)
    return (self.recharge_cfg[self.grade] or {})[seq]
end

function TripleRechargeWGData:GetCurGradeIsNeedClose()
    return self.buy_flag > -1 and true or false
end