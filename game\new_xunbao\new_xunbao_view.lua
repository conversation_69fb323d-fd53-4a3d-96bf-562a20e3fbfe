
NewXunbaoView = NewXunbaoView or BaseClass(SafeBaseView)

NewXunbaoView.RawBg = {
	[TabIndex.fuwen_xunbao] = "bg_bugua",
	[TabIndex.equipxunbao_xunbao] = "bg_xiuzhen_road_raw",
	[TabIndex.xuanshi_xunbao] = "bg_xiuzhen_road_raw",
}

function NewXunbaoView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:AddViewResource(0, "uis/view/zhuangbeixunbao_ui_prefab", "layout_xunbao_panel")
	self:AddViewResource(0, "uis/view/zhuangbeixunbao_ui_prefab", "VerticalTabbar")
	self:AddViewResource(TabIndex.fuwen_xunbao, "uis/view/zhuangbeixunbao_ui_prefab", "layout_wengua")
	self:AddViewResource(TabIndex.equipxunbao_xunbao, "uis/view/zhuangbeixunbao_ui_prefab", "layout_qizhenyibao")
	self:AddViewResource(TabIndex.xuanshi_xunbao, "uis/view/zhuangbeixunbao_ui_prefab", "layout_qizhenyibao")
	self.datachange_callback = BindTool.Bind1(self.OnItemDataChange, self)
end

function NewXunbaoView:__delete()

end

function NewXunbaoView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:DeleteWenGua()
    self:ReleaseTaoBaoLong()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
end

function NewXunbaoView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	-- 刷新红点
	RemindManager.Instance:Fire(RemindName.XunBao_XunBao)
	RemindManager.Instance:Fire(RemindName.XunBao_XuanShi)
	--RemindManager.Instance:Fire(RemindName.XunBao_FuWen)
end

function NewXunbaoView:LoadCallBack()
	local remind_tab = {
		{RemindName.XunBao_XunBao},
		{RemindName.XunBao_XuanShi},
		--{RemindName.XunBao_FuWen},
	}

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.Xunbao.ViewName,nil,"uis/view/zhuangbeixunbao_ui_prefab", nil ,remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end

	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.XunBao, self.tabbar)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)
end

function NewXunbaoView:LoadIndexCallBack(index)
	if index == TabIndex.fuwen_xunbao then
		self:InitWenGua()
	elseif self:IsSameXunbao(index) then
		self:InitLongXunBao()
	end
end

function NewXunbaoView:CloseCallBack()
	self:CloseTaobaoCallBack()
end

function NewXunbaoView:ShowIndexCallBack(index)
	if index == TabIndex.fuwen_xunbao then
		self:ShowIndexWenGua()
	elseif self:IsSameXunbao(index) then
		self:ShowIndexLongXunBao(index)
	end
	
	-- if NewXunbaoView.RawBg[index] then
	-- 	self.node_list["raw_xunbao_bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesJPG(NewXunbaoView.RawBg[index]))
	-- end
end

function NewXunbaoView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.fuwen_xunbao then
				self:OnFlushWenGua()
			elseif self:IsSameXunbao(index) then
				self:OnFlushLongXunBao()
            end
		elseif self:IsSameXunbao(index) then
            self:OnFlushLongShow(k,v)
		elseif index == TabIndex.fuwen_xunbao then
            self:OnFlushWenGuaShow(k, v)
		end
	end
end

function NewXunbaoView:IsSameXunbao(index)
	if FunOpen.Instance:GetFunIsOpenedByTabName("equipxunbao_xunbao") then
		return index == TabIndex.equipxunbao_xunbao or index == TabIndex.xuanshi_xunbao
	else
		return index == TabIndex.equipxunbao_xunbao or index == TabIndex.xuanshi_xunbao
	end
end
