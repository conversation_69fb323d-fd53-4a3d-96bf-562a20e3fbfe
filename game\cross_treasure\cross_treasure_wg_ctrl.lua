require("game/cross_treasure/cross_treasure_wg_data")
require("game/cross_treasure/cross_treasure_view")
require("game/cross_treasure/cross_treasure_reward_view")
require("game/cross_treasure/cross_treasure_result_win")
require("game/cross_treasure/cross_treasure_result_loser")


CrossTreasureWGCtrl = CrossTreasureWGCtrl or BaseClass(BaseWGCtrl)

function CrossTreasureWGCtrl:__init()
	if CrossTreasureWGCtrl.Instance then
        ErrorLog("[CrossTreasureWGCtrl]:Attempt to create singleton twice!")
        return
	end
	CrossTreasureWGCtrl.Instance = self
	self.data = CrossTreasureWGData.New()
	self.view = CrossTreasureView.New(GuideModuleName.CrossTreasureView)
	self.reward_view = CrossTreasureRewardView.New()
	self.result_win = CrossTreasureResultWinView.New()
	self.result_loser = CrossTreasureResultLoserView.New()
    self:RegisterAllProtocals()

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
end

function CrossTreasureWGCtrl:__delete()
    CrossTreasureWGCtrl.Instance = nil
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

	if self.view then
		self.view:DeleteMe()
        self.view = nil
	end

	if self.reward_view then
		self.reward_view:DeleteMe()
        self.reward_view = nil
	end

	if self.result_win then
		self.result_win:DeleteMe()
        self.result_win = nil
	end

	if self.result_loser then
		self.result_loser:DeleteMe()
        self.result_loser = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
	self.item_data_change_callback = nil
end

function CrossTreasureWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(CSCrossTreasureOperate)
    self:RegisterProtocol(SCCrossTreasureInfo, "OnSCCrossTreasureInfo") 				--灵珠信息
	self:RegisterProtocol(SCCrossTreasureRoleInfo, "OnSCCrossTreasureRoleInfo")   		--灵珠信息(个人)
	self:RegisterProtocol(SCCrossTreasureBaseInfo, "OnSCCrossTreasureBaseInfo")   		--跨服藏宝基础信息
	self:RegisterProtocol(SCCrossTreasureBeastInfo, "OnSCCrossTreasureBeastInfo")   	--跨服藏宝幻兽信息
end

-- -- 获取自身的跨服藏宝信息
-- function CrossTreasureWGCtrl:MainuiOpenCreateCallBack()
-- 	local plat_type = RoleWGData.Instance:GetCurPlatType()
-- 	local server_id = RoleWGData.Instance:GetCurServerId()
-- 	CrossTreasureWGCtrl.Instance:SendTreasureOperaTypeInfo(plat_type, server_id)
-- end

--物品变化(这里需要更新红点)
function CrossTreasureWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE or change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		if self.data:GetIsTreasureItemChange(change_item_id) then
			-- 刷新主界面任务信息
			MainuiWGCtrl.Instance:FlushView(0, "CrossCrossTreasureTask")
		end
	end
end

-- 跨服藏宝操作
function CrossTreasureWGCtrl:SendTreasureOperaReq(operate_type, param1, param2, param3)
    -- print_error(">>>SendTreasureOperaReq>>>>>>>>",operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTreasureOperate)
  	protocol.operate_type = operate_type or 0
  	protocol.param1 = param1 or 0
  	protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
  	protocol:EncodeAndSend()
end

-- 获取跨服藏宝信息信息			param1:server_id  param2:seq  param3:level	
function CrossTreasureWGCtrl:SendTreasureOperaTypeInfo(server_id, seq)
    -- print_error(">>>SendTreasureOperaTypeInfo >>>>>>>>", server_id, seq)
	self:SendTreasureOperaReq(CROSS_TREASURE_TYPE.CROSS_TREASURE_OPERATE_TYPE_INFO, server_id, seq)
end

-- 跨服藏宝使用物品
function CrossTreasureWGCtrl:SendTreasureOperaTypeUse(seq)
    -- print_error(">>>SendTreasureOperaTypeUse >>>>>>>>", seq)
	self:SendTreasureOperaReq(CROSS_TREASURE_TYPE.CROSS_TREASURE_OPERATE_TYPE_USE, seq)
end

-- 获取跨服藏宝信息
function CrossTreasureWGCtrl:SendTreasureRoleInfo()
    -- print_error(">>>SendTreasureOperaTypeInfo >>>>>>>>")
	self:SendTreasureOperaReq(CROSS_TREASURE_TYPE.CROSS_TREASURE_OPERATE_TYPE_ROLE_INFO)
end

-- 获取跨服藏宝幻兽基础信息
function CrossTreasureWGCtrl:SendTreasureBaseInfo()
    -- print_error(">>>SendTreasureOperaTypeInfo >>>>>>>>")
	self:SendTreasureOperaReq(CROSS_TREASURE_TYPE.CROSS_TREASURE_OPERATE_TYPE_BASE_INFO)
end

-- 获取跨服藏宝服务器幻兽信息
function CrossTreasureWGCtrl:SendTreasureBeastInfo(server_id)
    -- print_error(">>>SendTreasureOperaTypeInfo >>>>>>>>")
	self:SendTreasureOperaReq(CROSS_TREASURE_TYPE.CROSS_TREASURE_OPERATE_TYPE_BEAST_INFO, server_id)
end

-----------------------------------------------------
-- 跨服藏宝 灵珠信息
function CrossTreasureWGCtrl:OnSCCrossTreasureInfo(protocol)
	-- print_error(">>>OnSCCrossTreasureRoleInfo 跨服藏宝 灵珠信息)>>>>>>>>",protocol)
    self.data:SetTreasureInfo(protocol)

	GlobalTimerQuest:AddDelayTimer(function ()
		self:FlushView()
	end, 0.2)
end

-- 跨服藏宝 个人灵珠信息
function CrossTreasureWGCtrl:OnSCCrossTreasureRoleInfo(protocol)
    -- print_error(">>>OnSCCrossTreasureRoleInfo 跨服藏宝 个人灵珠信息)>>>>>>>>",protocol)
	self.data:SetTreasureRoleInfo(protocol)
	self:FlushView()
	-- 刷新主界面任务信息
	RemindManager.Instance:Fire(RemindName.CrossTreasureLinzu)
	MainuiWGCtrl.Instance:FlushView(0, "CrossCrossTreasureTask")
end

-- 跨服藏宝 基础信息
function CrossTreasureWGCtrl:OnSCCrossTreasureBaseInfo(protocol)
    -- print_error(">>>OnSCCrossTreasureRoleInfo 跨服藏宝 基础信息)>>>>>>>>",protocol)
	self.data:SetTreasureBaseInfo(protocol)
	self:FlushView()
	-- 刷新主界面任务信息
	RemindManager.Instance:Fire(RemindName.CrossTreasureLinzu)
	RemindManager.Instance:Fire(RemindName.CrossTreasureBeast)
	MainuiWGCtrl.Instance:FlushView(0, "CrossCrossTreasureTask")
end

-- 跨服藏宝 幻兽信息
function CrossTreasureWGCtrl:OnSCCrossTreasureBeastInfo(protocol)
    -- print_error(">>>OnSCCrossTreasureRoleInfo 跨服藏宝 幻兽信息)>>>>>>>>",protocol)
	self.data:SetTreasureBeastInfo(protocol)
	GlobalTimerQuest:AddDelayTimer(function ()
		self:FlushView()
	end, 0.2)
end

-- 跨服藏宝 幻兽信息
-- OP_CROSS_TREASURE_BEAST_GATHER_RESULT = 155,	--// 跨服幻兽捕捉结果 result(1 succ, 0 fail) param1=pool_id * 1000 + seq param2=is_special param3=is_rampage
-- OP_CROSS_TREASURE_GATHER_RESULT = 156,		--// 跨服藏宝采集结果 result(1 succ, 0 fail) param1=index param1=seq
function CrossTreasureWGCtrl:OnCrossTreasureBeastOperareCallBack(operate_type, protocol)
	if MODULE_OPERATE_TYPE.OP_CROSS_TREASURE_BEAST_GATHER_RESULT == operate_type then
		local data = {}
		data.pool_id = math.floor(protocol.param1 / 1000)
		data.seq = protocol.param1 % 1000

		if protocol.result == 1 then
			self:OpenResultShowWinShow(data)
		else
			self:OpenResultShowLoserShow(data)
		end
	elseif MODULE_OPERATE_TYPE.OP_CROSS_TREASURE_GATHER_RESULT == operate_type then
		local cfg = CrossTreasureWGData.Instance:GetTreasureCfgBySeq(protocol.param1)
		if cfg and cfg.type ~= 1 then		-- 玩家自身看到的区分炸弹，其他玩家
			-- 采集到炸弹了
			local main_role = Scene.Instance:GetMainRole()
			if not main_role then
				return
			end
			main_role:PlayGatherBombEffect()
			return
		end
	end
end
-----------------------------------------------------
function CrossTreasureWGCtrl:FlushView(index, key, param_t)
	if self.view:IsOpen() then
		self.view:Flush(index, key, param_t)
	end
end

--跨服藏宝 打开界面
function CrossTreasureWGCtrl:OpenTreasureView()
	if not self.view:IsOpen() then
		self.view:Open()
	end
end

--跨服藏宝 打开界面
function CrossTreasureWGCtrl:FlushTreasureView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

--跨服藏宝 关闭界面
function CrossTreasureWGCtrl:CloseTreasureView()
	if self.view:IsOpen() then
		self.view:Close()
	end
end

-- 打开奖励预览界面
function CrossTreasureWGCtrl:OpenRewardPreview()
	if self.reward_view then
		self.reward_view:Open()
	end
end

-- 打开捕捉成功奖励界面
function CrossTreasureWGCtrl:OpenResultShowWinShow(data)
	self.result_win:SetShowData(data)
	if self.result_win then
		self.result_win:Open()
	end
end

-- 打开捕捉失败奖励界面
function CrossTreasureWGCtrl:OpenResultShowLoserShow(data)
	self.result_loser:SetShowData(data)
	if self.result_loser then
		self.result_loser:Open()
	end
end

-- 种植灵珠假动作
function CrossTreasureWGCtrl:OperateLingZhuPlace(time, var_default_describe)
	GatherBar.Instance:SetGatherTime(time, nil, nil) 															--界面采集动画时长
	GatherBar.Instance:SetOtherDefaultDescribe(var_default_describe)
	GatherBar.Instance:Open()
end

-- 关闭种植灵珠假动作
function CrossTreasureWGCtrl:CloseLingZhuPlace()
	GatherBar.Instance:Close()
end