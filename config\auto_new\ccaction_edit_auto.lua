return {
    action_list={
        xianhe_1={
            "1#DelayTime(1)",
            "2#FadeIn(0.5)",
            "3#MoveBy(3,600,-100)",
            "4#FadeOut(0.5)",
            "5#Place(dp1,dp2)",
            "201#Spawn(2,3)",
            "301#Sequence(1,201,4,5)",
            "501#RepeatForever(301)"
        },
        xianhe_2={
            "1#DelayTime(1)",
            "2#FadeIn(0.5)",
            "3#MoveBy(2,430,-120)",
            "4#FadeOut(0.5)",
            "5#Place(dp1,dp2)",
            "201#Spawn(2,3)",
            "301#Sequence(1,201,4,5)",
            "501#RepeatForever(301)"
        },
        star_flash1={
            "1#DelayTime(2)",
            "2#FadeIn(1)",
            "3#FadeOut(1)",
            "4#RotateBy(1,60)",
            "5#RotateBy(1,60)",
            "6#ScaleTo(1,1.2,1.2)",
            "7#ScaleTo(1,0.1,0.1)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(1,8,9)",
            "11#RepeatForever(10)"
        },
        star_flash2={
            "1#DelayTime(2)",
            "2#FadeIn(1)",
            "3#FadeOut(1)",
            "4#RotateBy(1,45)",
            "5#RotateBy(1,45)",
            "6#ScaleTo(1,1.2,1.2)",
            "7#ScaleTo(1,0.1,0.1)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(8,9,1)",
            "11#RepeatForever(10)"
        },
        star_flash3={
            "1#DelayTime(1.5)",
            "2#FadeIn(0.5)",
            "3#FadeOut(1)",
            "4#RotateBy(0.5,20)",
            "5#RotateBy(1,40)",
            "6#ScaleTo(0.5,1,1)",
            "7#ScaleTo(1,0.1,0.1)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(1,8,9)",
            "11#RepeatForever(10)"
        },
        star_flash4={
            "1#DelayTime(1.5)",
            "2#FadeIn(1)",
            "3#FadeOut(0.5)",
            "4#RotateBy(1,70)",
            "5#RotateBy(0.5,35)",
            "6#ScaleTo(1,1.2,1.2)",
            "7#ScaleTo(0.5,0.3,0.3)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(1,8,9)",
            "11#RepeatForever(10)"
        },
        star_flash5={
            "1#DelayTime(1.5)",
            "2#FadeIn(0.5)",
            "3#FadeOut(1)",
            "4#RotateBy(0.5,25)",
            "5#RotateBy(1,50)",
            "6#ScaleTo(0.5,1,1)",
            "7#ScaleTo(1,0.1,0.1)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(8,9,1)",
            "11#RepeatForever(10)"
        },
        star_flash6={
            "1#DelayTime(1.5)",
            "2#FadeIn(1)",
            "3#FadeOut(0.5)",
            "4#RotateBy(1,80)",
            "5#RotateBy(0.5,40)",
            "6#ScaleTo(1,1.2,1.2)",
            "7#ScaleTo(0.5,0.3,0.3)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(8,9,1)",
            "11#RepeatForever(10)"
        },
        star_flash7={
            "1#DelayTime(1)",
            "2#FadeIn(0.5)",
            "3#FadeOut(0.5)",
            "4#RotateBy(0.5,45)",
            "5#RotateBy(0.5,45)",
            "6#ScaleTo(0.5,1,1)",
            "7#ScaleTo(0.5,0.3,0.3)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(1,8,9)",
            "11#RepeatForever(10)"
        },
        star_flash8={
            "1#DelayTime(1)",
            "2#FadeIn(0.5)",
            "3#FadeOut(0.5)",
            "4#RotateBy(0.5,55)",
            "5#RotateBy(0.5,55)",
            "6#ScaleTo(0.5,1,1)",
            "7#ScaleTo(0.5,0.3,0.3)",
            "8#Spawn(2,4,6)",
            "9#Spawn(3,5,7)",
            "10#Sequence(8,9,1)",
            "11#RepeatForever(10)"
        },
        xianjian_attack={
            "1#MoveTo(0.05,0,40)",
            "2#ScaleTo(0.05,1.8)",
            "3#RotateBy(0.1,dp1)",
            "4#ScaleTo(0.05,1)",
            "5#MoveTo(0.2,dp2,dp3)",
            "51#RotateBy(0.3,-1080)",
            "6#RotateTo(0.03,dp4)",
            "7#MoveTo(0.2,0,0)",
            "101#EaseSineIn(1)",
            "102#EaseSineIn(3)",
            "103#EaseBackOut(5)",
            "201#Spawn(1,2)",
            "202#Spawn(4,5)",
            "203#Sequence(6,7)",
            "204#RotateTo(0.2,0)",
            "205#Spawn(4,204)",
            "501#Sequence(201,102,5,51,203,205)"
        },
        xianjian_move={
            "1#MoveTo(1,dp1,dp3)",
            "2#DelayTime(0.5)",
            "3#MoveTo(1,dp2,dp3)",
            "4#DelayTime(5)",
            "301#Sequence(1,2,3,4)",
            "501#RepeatForever(301)"
        },
        dati_yinzhang={
            "1#ScaleTo(0,1.3,1.3)",
            "2#ScaleTo(0.15,1.5,1.5)",
            "3#ScaleTo(0.3,0.9,0.9)",
            "4#ScaleTo(0.05,1,1)",
            "101#EaseExponentialOut(3)",
            "301#Sequence(1,2,101,4)"
        },
        dog_runout={
            "1#MoveBy(0.8,120,150)",
            "2#FlipX(true)",
            "3#MoveBy(1.6,-240,-300)",
            "4#MoveBy(0.8,120,150)",
            "5#MoveBy(1,-120,-150)",
            "6#MoveBy(1.2,300,-144)",
            "7#MoveBy(0.3,75,-54)",
            "8#FadeOut(0.3)",
            "9#FlipX(false)",
            "201#Spawn(7,8)",
            "301#Sequence(1,2,3,9,4)",
            "302#Sequence(6,201)",
            "501#Sequence(301,302)"
        },
        dog_move={
            "1#MoveBy(1,120,150)",
            "2#FlipX(true)",
            "3#MoveBy(2,-240,-300)",
            "4#MoveBy(1,120,150)",
            "5#MoveBy(1,-120,-150)",
            "6#FlipX(false)",
            "301#Sequence(1,2,3,6,4)",
            "501#RepeatForever(301)"
        },
        monster_hited_move_ondie_1={
            "_radian=atan2(dp4-dp2,dp3-dp1)",
            "_dis=400",
            "1#MoveBy(0.5,cos(_radian))*_dis,sin(_radian)*_dis)",
            "101#EaseSineOut(1)",
            "2#DelayTime(0.5)",
            "3#FadeOut(0.5)",
            "102#Sequence(2,3)",
            "501#Spawn(101,102)"
        },
        monster_hited_move_ondie_2={
            "_radian=atan2(dp4-dp2,dp3-dp1)",
            "_dis=400",
            "1#MoveBy(0.7,cos(_radian))*_dis,sin(_radian)*_dis)",
            "101#EaseSineOut(1)",
            "2#DelayTime(0.7)",
            "3#FadeOut(0.5)",
            "102#Sequence(2,3)",
            "501#Spawn(101,102)"
        },
        monster_hited_ondie_effect_2={
            "_radian=atan2(dp4-dp2,dp3-dp1)",
            "_rate=abs(cos(_radian))",
            "1#MoveTo(0.18,0,_rate*180)",
            "2#MoveTo(0.18,0,0)",
            "3#MoveTo(0.12,0,_rate*40)",
            "4#MoveTo(0.12,0,0)",
            "101#EaseSineOut(1)",
            "102#EaseSineIn(2)",
            "103#EaseSineOut(3)",
            "104#EaseSineIn(4)",
            "501#Sequence(101,102,103,104)"
        },
        monster_hited_move_ondie_3={
            "_radian=atan2(dp4-dp2,dp3-dp1)",
            "_dis=400",
            "1#MoveBy(0.7,cos(_radian))*_dis,sin(_radian)*_dis)",
            "101#EaseSineOut(1)",
            "2#DelayTime(0.7)",
            "3#FadeOut(0.5)",
            "102#Sequence(2,3)",
            "501#Spawn(101,102)"
        },
        monster_hited_ondie_effect_3={
            "1#RotateBy(0.12,-360)",
            "2#RotateBy(0.13,-360)",
            "3#RotateBy(0.14,-360)",
            "4#RotateBy(0.15,-360)",
            "5#RotateBy(0.16,-360)",
            "101#Sequence(1,2,3,4,5)",
            "102#ScaleTo(0.7,0,0)",
            "501#Spawn(101,102)"
        },
        xianhe_login_move={
            "1#DelayTime(1)",
            "2#FadeIn(0.5)",
            "3#MoveBy(17,dp3,0)",
            "4#FadeOut(0.5)",
            "5#Place(dp1,dp2)",
            "201#Spawn(2,3)",
            "301#Sequence(1,201,4,5)",
            "501#RepeatForever(301)"
        },
        nu_skill_head_pic={
            "1#MoveTo(0.2,dp1,dp2)",
            "2#FadeIn(0.2)",
            "501#Spawn(1,2)"
        },
        nu_skill_head_pic_bg={
            "0#Hide()",
            "1#ScaleTo(0.05,0.1)",
            "2#Place(dp1,dp2)",
            "3#DelayTime(0.15)",
            "4#Show()",
            "5#FadeIn(0.2)",
            "6#ScaleTo(0.2,1.3)",
            "7#Spawn(4,5,6)",
            "501#Sequence(0,1,2,3,7)"
        },
        nu_skill_shine={
            "0#Hide()",
            "1#DelayTime(0.8)",
            "2#Show()",
            "501#Sequence(0,1,2)"
        },
        nu_mount_skill_1={
            "1#MoveBy(5,2700,-1500)"
        },
        nu_mount_skill_2={
            "1#MoveBy(4.8,2700,-1500)"
        },
        nu_mount_skill_3={
            "1#MoveBy(5.2,2700,-1500)"
        }
    }
}