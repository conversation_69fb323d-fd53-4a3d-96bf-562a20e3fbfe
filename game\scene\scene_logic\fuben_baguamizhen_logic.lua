BaGuaMiZhenSceneLogic = BaGuaMiZhenSceneLogic or BaseClass(CommonFbLogic)

function BaGuaMiZhenSceneLogic:__init()

end

function BaGuaMiZhenSceneLogic:__delete()

end

function BaGuaMiZhenSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	local ctrl = MainuiWGCtrl.Instance
    ctrl:AddInitCallBack(nil, function()
        ctrl:SetTaskContents(false)
        ctrl:SetOtherContents(true)
        ctrl:SetFBNameState(true, Scene.Instance:GetSceneName())
        ctrl:SetTeamBtnState(false)

        --进入打开task界面
		BaGuaMiZhenWGCtrl.Instance:OpenTaskView()
    end)
    if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function BaGuaMiZhenSceneLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end

function BaGuaMiZhenSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	MainuiWGCtrl.Instance:SetTaskContents(true)
    -- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)
    MainuiWGCtrl.Instance:SetTeamBtnState(true)

    MainuiWGCtrl.Instance:SetOtherContents(false)
	BaGuaMiZhenWGCtrl.Instance:CloseTaskView()
	FuBenWGData.Instance:ClearBaGuaMiZhenFBStatus()
    FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_bagua)
    if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end