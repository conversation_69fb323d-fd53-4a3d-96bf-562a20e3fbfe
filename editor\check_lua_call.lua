local CheckLuaCall = {}
local SysFile = System.IO.File
local gameing_time = 0
local is_gameing = nil
local require_cfg_list = {}
local deep_copy_t = {}
local table_copy_t = {}
local load_asset_t1 = {}
local load_asset_t2 = {}
local load_asset_t3 = {}
local for_times_dic = {}
local for_table_lens = {}
local for_call_warns = {}
local list_to_map_dic = {}
local list_to_map_list_dic = {}
IsStopCheckForCall = false

function CheckLuaCall:OnLuaCall(event, ...)
	local params = {...}

	if event == "require_config" then
		self:OnRequireCofing(params[1])
	elseif event == "list_to_map" then
		self:OnListToMap(params[1], params[2])
	elseif event == "list_to_map_list" then
		self:OnListToMapList(params[1], params[2])
	elseif event == "table_copy" then
		-- self:OnTableCopy()
	elseif event == "deep_copy" then
		-- self:OnDeepCopy()
	elseif event == "load_sprite" then
		self:OnLoadSprite(params[1], params[2], params[3], params[4])
	elseif event == "call_obsolete" then
		self:OnCallObsoleteFun(params[1])
	elseif event == "call_invalid_param" then
		self:OnCallInvalidParam(params[1])
	elseif event == "view_load_callback" then
		self:OnViewLoadCallback(params[1])
	elseif event == "pairs" then
		self:OnCallFor(params[1])
	end
end

function CheckLuaCall:Update(now_time, elapse_time)
	if self:IsGameing() then
		gameing_time = gameing_time + elapse_time
		self:CheckRequireConfig()
		self:CheckDeepCopy()
		self:CheckTableCopy()
		self:CheckLoadAsset()
		self:CheckCallFor()
	end
end

function CheckLuaCall:IsGameing()
	if is_gameing then
		return true
	end

	if ViewManager and ViewManager.Instance and ViewManager.Instance:IsOpen(ViewName.Main) then
		is_gameing = true
		return true
	end

	return false
end

function CheckLuaCall:OnRequireCofing(path)
	if self:IsGameing() and gameing_time >= 5 then
		table.insert(require_cfg_list, path)
	end
end

function CheckLuaCall:CheckRequireConfig()
	if #require_cfg_list >= 10 then
		print_error(string.format("在一帧同时同步加载%s个配置，极易低端在手机上造成卡顿，请联系主程进行优化\n", #require_cfg_list), require_cfg_list)
	end

	for k,v in pairs(require_cfg_list) do
		local path = string.format("%s/Game/Lua/%s.lua", UnityEngine.Application.dataPath, v)
		local file_size = UtilU3d.GetFileInfoLength(path)
		if file_size >= 100 then
			print_error(string.format("加载的配置文件过大，极易在低端手机上造成卡顿, 请放在rquire_list里.%s, %skb", path, file_size))
		end
	end

	require_cfg_list = {}
end

function CheckLuaCall:OnListToMap(list, key_list)
	if self:IsGameing() and gameing_time >= 5 then
		print_error("禁止在游戏中使用ListToMapList接口，该接口开销较大，有部分人总是烂用该接口而导致性能问题，因此限制只能在初始化中使用。马上修改！！!")
	end

	local key = ""
	for k,v in ipairs(key_list) do
		key = key .. v
	end
	list_to_map_dic[list] = list_to_map_dic[list] or {}
	list_to_map_dic[list][key] = list_to_map_dic[list][key] or {}

	local tbl = list_to_map_dic[list][key]
	table.insert(tbl, debug.getinfo(5, "Sln"))

	if #tbl > 1 then
		local str = "多处地方对同一个配置表做ListToMap的操作，请检查："
		for k,v in pairs(tbl) do
			str = str .. string.format("\n[%s:%s]", v.source, v.currentline)
		end
		UnityEngine.Debug.LogError(str)
	end
end

function CheckLuaCall:OnListToMapList(list, key_list)
	if self:IsGameing() and gameing_time >= 5 then
		print_error("禁止在游戏中使用ListToMapList接口，该接口开销较大，有部分人总是烂用该接口而导致性能问题，因此只能在初始化中使用。马上修改！！!")
	end

	local key = ""
	for k,v in ipairs(key_list) do
		key = key .. v
	end
	list_to_map_list_dic[list] = list_to_map_list_dic[list] or {}
	list_to_map_list_dic[list][key] = list_to_map_list_dic[list][key] or {}

	local tbl = list_to_map_list_dic[list][key]
	table.insert(tbl, debug.getinfo(5, "Sln"))

	if #tbl > 1 then
		local str = "多处地方对同一个配置表做ListToMapList的操作，请检查："
		for k,v in pairs(tbl) do
			str = str .. string.format("\n[%s:%s]", v.source, v.currentline)
		end
		UnityEngine.Debug.LogError(str)
	end
end

function CheckLuaCall:OnDeepCopy()
	if self:IsGameing() and gameing_time >= 5 then
		local traceback = debug.traceback()
		deep_copy_t[traceback] = deep_copy_t[traceback] or {}
		table.insert(deep_copy_t[traceback], GlobalUnityTime)
	end
end

function CheckLuaCall:CheckDeepCopy()
	self:ChecSameTrackbackCall(deep_copy_t, "DeepCopy", 1, 10)
end

function CheckLuaCall:OnTableCopy()
	if self:IsGameing() and gameing_time >= 5 then
		local traceback = debug.traceback()
		table_copy_t[traceback] = table_copy_t[traceback] or {}
		table.insert(table_copy_t[traceback], GlobalUnityTime)
	end
end

function CheckLuaCall:CheckTableCopy()
	self:ChecSameTrackbackCall(table_copy_t, "TableCopy", 1, 10)
end

function CheckLuaCall:OnLoadSprite(is_async, image, bundle_name, asset_name)
	if self:IsGameing() and gameing_time >= 5 and image.gameObject then
		-- if not is_async and AssetBundleMgr:IsLowLoad() then
		-- 	print_error("你在小界面使用了同步接口，请改用LoadSpriteAsync", bundle_name, asset_name)
		-- end

		-- local canvas = image.gameObject:GetComponentInParent(typeof(UnityEngine.Canvas))
		-- if nil ~= canvas and not canvas.gameObject.activeInHierarchy then
		-- 	print_error("Canvas已经被隐藏，也在做刷新操作,请检查代码")
		-- end

		-- local traceback = debug.traceback()
		-- local key = string.format("%s %s %s", image.gameObject:GetInstanceID(), bundle_name, asset_name)
		-- load_asset_t1[key] = load_asset_t1[key] or {}
		-- table.insert(load_asset_t1[key], {traceback = traceback, call_time = GlobalUnityTime})
		-- load_asset_t2[key] = load_asset_t2[key] or {}
		-- table.insert(load_asset_t2[key], {traceback = traceback, call_time = GlobalUnityTime})
	end
end

function CheckLuaCall:CheckLoadAsset()
	self:ChecDiffTrackbackCall(load_asset_t1, "Load", 1, 10, true)
	self:ChecDiffTrackbackCall(load_asset_t2, "Load", 0, 3, false)
end

function CheckLuaCall:OnCallObsoleteFun(log)
	print_error(log)
	GameRoot.AddLuaWarning("【废弃接口】调用了废弃接口，使用不当将导致性能问题，马上修改 (查看控制台日志)", "Normal")
end

function CheckLuaCall:OnCallInvalidParam(log)
	print_error(log)
	GameRoot.AddLuaWarning("【参数无效】参数无效，将有可能导致性能问题，马上修改 (查看控制台日志)")
end

function CheckLuaCall:OnViewLoadCallback(base_view)
	if not base_view.full_screen and not base_view.is_big_view and
		base_view.view_name ~= ViewName.Main then
		local transforms = base_view.root_node.transform:GetComponentsInChildren(typeof(UnityEngine.RectTransform))
		for i = 0, transforms.Length - 1 do
			local width = transforms[i].rect.width
			local height = transforms[i].rect.height
			if width < 1334 and height < 768 and width >= 1000 and height >= 650 then
				print_error(string.format("view_name = %s  width = %s, height = %s", base_view.view_name, width, height))
				GameRoot.AddLuaWarning("【Lua性能问题】大面板请标记full_screen或者is_big_view为true，这将有效提升界面性能。不懂问主程 (查看控制台日志)", "Normal")
				break
			end
		end
	end
end

-- 检查同样的地方调用（堆栈相同）
function CheckLuaCall:ChecSameTrackbackCall(map, fun_name, check_time, max_call_times)
	local del_list = {}
	for traceback, call_list in pairs(map) do
		if #call_list >= 2 then
			local last_call = call_list[#call_list]
			local first_call = call_list[1]
			if last_call - first_call >= check_time then
				table.insert(del_list, traceback)
				if #call_list >= max_call_times then
					GameRoot.AddLuaWarning("【Lua性能问题】代码质量差导致严重性能问题，马上修改！！(查看控制台日志)", "High")
					print_error(string.format("(可被重构的接口)%s在%s秒内被调用了%s次。TableCopy接口开销较大，你什么逻辑非得拷贝？？导致卡顿，代码质量差。不懂修改找主程！！！\n%s", fun_name, check_time, #call_list, traceback))
				end
			end
		end
	end

	for k,v in pairs(del_list) do
		map[v] = nil
	end
end

-- 检查一个方法被不同地方调用（堆栈不相同）
function CheckLuaCall:ChecDiffTrackbackCall(map, fun_name, check_time, max_call_times, is_merge_trackback)
	local del_list = {}
	for key, call_list in pairs(map) do
		if #call_list >= 2 then
			local last_call = call_list[#call_list].call_time
			local first_call = call_list[1].call_time
			if last_call - first_call >= check_time then
				table.insert(del_list, key)
				if #call_list >= max_call_times then
					-- 相同的trackback合并统计
					local traceback = ""
					if is_merge_trackback then
						local traceback_count = 0
						local traceback_set = {}
						local trackback_list = {}
						local trackback_call_times = {}
						for i, v in ipairs(call_list) do
							if nil == traceback_set[v.traceback] then
								traceback_set[v.traceback] = true
								table.insert(trackback_list, v.traceback)
							end
							trackback_call_times[v.traceback] = (trackback_call_times[v.traceback] or 0) + 1
						end

						-- 连续trackback
						for i,v in ipairs(trackback_list) do
							traceback = traceback .. string.format("次数:%s  %s\n", trackback_call_times[v], v)
						end
					else
						-- 连续trackback
						for i, v in ipairs(call_list) do
							traceback = traceback .. string.format("时间:%s  %s\n", v.call_time, v.traceback)
						end
					end

					GameRoot.AddLuaWarning("【Lua性能问题】代码质量差导致严重性能问题，马上修改！！(查看控制台日志)", "Normal")
					print_error(string.format("%s在%s秒内被调用了%s次。说明你的界面在无故多次刷新，请去整理代码, 去掉无用的刷新界面调用（可考虑改调用Flush刷新）！！！导致卡顿，代码质量差。不懂修改找主程！！！\n%s", fun_name, last_call - first_call, #call_list, traceback))
				end
			end
		elseif #call_list == 1 then
			if GlobalUnityTime - call_list[1].call_time >= check_time then
				table.insert(del_list, key)
			end
		end
	end

	for k,v in pairs(del_list) do
		map[v] = nil
	end
end

local scene_obj_list = {}
function CheckLuaCall:OnCreateObj(obj)
	-- local class_type = obj._class_type
	-- while nil ~= class_type do
	-- 	local class_name = GetClassName(class_type)
	-- 	if class_name == "SceneObj" then
	-- 		local info = debug.getinfo(5, "Sl")
	-- 		scene_obj_list[obj] = info.source
	-- 		break
	-- 	end
	-- 	class_type = class_type.super
	-- end
end

function CheckLuaCall:OnDeleteObj(obj)
	-- if nil == scene_obj_list[obj] then
	-- 	return
	-- end

	-- local info = debug.getinfo(5, "Sln")
	-- if info.source ~= scene_obj_list[obj] then
	-- 	print_error("尝试非法删除SceneObj!!!")
	-- end

	-- scene_obj_list[obj] = nil
end

function CheckLuaCall:OnCallFor(t)
	if nil == t or not self:IsGameing() or gameing_time < 5 or IsGameStop or IsStopCheckForCall or t == _G then
		return
	end

	if nil == for_times_dic[t] then
		for_times_dic[t] = 0
		for_table_lens[t] = GetTableLen(t)
	end
	for_times_dic[t] = for_times_dic[t] + 1
	if for_times_dic[t] >= 2 and for_table_lens[t] >= 100 then
		if nil == for_call_warns[t] then
			for_call_warns[t] = {trackback = debug.traceback()}
		end
		for_call_warns[t].times = for_times_dic[t]
	end
end

function CheckLuaCall:CheckCallFor()
	for k,v in pairs(for_call_warns) do
		if v.times >= 2 and v.times * for_table_lens[k] >= 20000 then
			print_error(string.format("写的代码在一帧里调用了%s次pairs，循环总次数达%s，请整理代码! \n %s", v.times, v.times * for_table_lens[k], v.trackback))
		end
	end

	for_call_warns = {}
	for_times_dic = {}
	for_table_lens = {}
end


return CheckLuaCall