require("game/wuhunzhenshen/wuhun_wg_data")
require("game/wuhunzhenshen/wuhun_wg_tower_data")
require("game/wuhunzhenshen/wuhun_front_wg_data")
require("game/wuhunzhenshen/wuhun_view")
require("game/wuhunzhenshen/wuhun_details_view")
require("game/wuhunzhenshen/wuhun_cultivate_view")
require("game/wuhunzhenshen/wuhun_tower_view")
require("game/wuhunzhenshen/wuhun_tower_task_view")
require("game/wuhunzhenshen/wuhun_tower_jiesuan_view")
require("game/wuhunzhenshen/wuhun_front_view")
require("game/wuhunzhenshen/wuhun_gem_front_view")
require("game/wuhunzhenshen/wuhun_front_common_render")
require("game/wuhunzhenshen/wuhun_select_baoshi_view")
require("game/wuhunzhenshen/wuhun_front_baoshi_upgrade")
require("game/wuhunzhenshen/wuhun_front_property_tip")
require("game/wuhunzhenshen/wuhun_material_buy_view")
require("game/wuhunzhenshen/wuhun_prerogative_view")
require("game/wuhunzhenshen/wuhun_prerogative_library_view")
require("game/wuhunzhenshen/wuhun_prerogative_gailv_view")
require("game/wuhunzhenshen/wuhun_lianhun_view")

--武魂技能和武魂之力
require("game/wuhunzhenshen/wuhunzhili_view")
require("game/wuhunzhenshen/wuhun_skill_view")

--武魂技能特效界面
require("game/wuhunzhenshen/wuhun_skill_effect_view")

-- 武魂真身
WuHunWGCtrl = WuHunWGCtrl or BaseClass(BaseWGCtrl)

function WuHunWGCtrl:__init()
	if WuHunWGCtrl.Instance ~= nil then
		ErrorLog("[WuHunWGCtrl] attempt to create singleton twice!")
		return
	end
	WuHunWGCtrl.Instance = self
	self.wuhun_view = WuHunView.New(GuideModuleName.WuHunView)
	self.wuhun_data = WuHunWGData.New()
	self.wuhun_wg_tower_data = WuHunTowerWGData.New()
	self.wuhun_front_wg_data = WuHunFrontWGData.New()
	self.wuhun_cultivate_view = WuHunCultivateView.New(GuideModuleName.WuHunCultivateView)
	self.wuhun_skill_view = WuHunSkillView.New(GuideModuleName.WuHunSkillView)
	self.wuhun_zhanli_view = WuHunZhiLiView.New()
	self.wuhun_skill_effect_view = WuHunSkillEffectView.New()
	self.wuhun_tower_task_view = WuHunTowerTaskView.New()
	self.wuhun_tower_jiesuan_view = WuHunTowerJieSuanView.New()
	self.wuhun_lianhun_view = WuHunLianHunView.New()
	
	self.skill_show_wuhun_effect_view = WuHunSkillEffectView.New()
	self.skill_show_wuhun_effect_view.view_layer = UiLayer.Normal
	self.skill_show_wuhun_effect_view.self_control_rendring = true

	self.wuhun_gem_front_view = WuHunGemFrontView.New(GuideModuleName.WuHunGemFrontView)
	self.select_baoshi_view = WuHunSelectBaoShiView.New()
	self.front_baoshi_upgrade_view = WuHunBaoShiUpgradeView.New()
	self.wuhun_front_property_tip = WuHunFrontPropertyTip.New()

	self.wuhun_material_buy_view = WuHunMaterialBuyView.New(GuideModuleName.WuHunMaterialBuyView)
	self.wuhun_prerogative_view = WuHunPrerogativeView.New(GuideModuleName.WuHunPrerogativeView)
	self.wuhun_prerogative_library_view = WuHunPrerogativeLibrary.New()
	self.wuhun_prerogative_probability_view = WuHunPrerogativeProbabilityView.New()

	self:RegisterAllProtocols()

	self.scene_wuhun_event = GlobalEventSystem:Bind(OtherEventType.ROLE_WUHUNZHENSHEN_CHANGE, BindTool.Bind(self.OnRoleWuHunZhenShenChange, self))
	self.scene_wuhun_hit = GlobalEventSystem:Bind(OtherEventType.ROLE_WUHUNZHENSHEN_HIT_NUMBER, BindTool.Bind(self.OnRoleWuHunZhenShenHit, self))

	self.role_data_change_callback = BindTool.Bind1(self.RoleDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"capability", "level"})

	self.item_data_event = BindTool.Bind1(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function WuHunWGCtrl:__delete()
	self.wuhun_view:DeleteMe()
	self.wuhun_view = nil

	self.wuhun_data:DeleteMe()
	self.wuhun_data = nil

	self.wuhun_wg_tower_data:DeleteMe()
	self.wuhun_wg_tower_data = nil

	self.wuhun_front_wg_data:DeleteMe()
	self.wuhun_front_wg_data = nil

	self.wuhun_gem_front_view:DeleteMe()
	self.wuhun_gem_front_view = nil

	self.select_baoshi_view:DeleteMe()
	self.select_baoshi_view = nil

	self.front_baoshi_upgrade_view:DeleteMe()
	self.front_baoshi_upgrade_view = nil

	self.wuhun_front_property_tip:DeleteMe()
	self.wuhun_front_property_tip = nil

	if self.wuhun_skill_view then
		self.wuhun_skill_view:DeleteMe()
		self.wuhun_skill_view = nil
	end

	if self.wuhun_zhanli_view then
		self.wuhun_zhanli_view:DeleteMe()
		self.wuhun_zhanli_view = nil
	end

	if self.wuhun_cultivate_view then
		self.wuhun_cultivate_view:DeleteMe()
		self.wuhun_cultivate_view = nil
	end

	if self.wuhun_skill_effect_view then
		self.wuhun_skill_effect_view:DeleteMe()
		self.wuhun_skill_effect_view = nil
	end

	if self.wuhun_tower_task_view then
		self.wuhun_tower_task_view:DeleteMe()
		self.wuhun_tower_task_view = nil
	end

	if self.wuhun_tower_jiesuan_view then
		self.wuhun_tower_jiesuan_view:DeleteMe()
		self.wuhun_tower_jiesuan_view = nil
	end

	self.skill_show_wuhun_effect_view:DeleteMe()
	self.skill_show_wuhun_effect_view = nil

	if self.wuhun_material_buy_view then
		self.wuhun_material_buy_view:DeleteMe()
		self.wuhun_material_buy_view = nil
	end

	if self.wuhun_prerogative_library_view then
		self.wuhun_prerogative_library_view:DeleteMe()
		self.wuhun_prerogative_library_view = nil
	end

	if self.wuhun_prerogative_probability_view then
		self.wuhun_prerogative_probability_view:DeleteMe()
		self.wuhun_prerogative_probability_view = nil
	end

	if self.wuhun_lianhun_view then
		self.wuhun_lianhun_view:DeleteMe()
		self.wuhun_lianhun_view = nil
	end

    if self.scene_wuhun_event then
        GlobalEventSystem:UnBind(self.scene_wuhun_event)
        self.scene_wuhun_event = nil
    end

	if self.scene_wuhun_hit then
        GlobalEventSystem:UnBind(self.scene_wuhun_hit)
        self.scene_wuhun_hit = nil
    end

	self:DeleteBaoShiUpGradeAlertTips()

	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	WuHunWGCtrl.Instance = nil
end

function WuHunWGCtrl:RegisterAllProtocols()
	-- 仙灵武魂
	self:RegisterProtocol(CSRoleWuHunReq)
	self:RegisterProtocol(SCRoleSingleWuHunInfo, "SCRoleSingleWuHunInfo")
	self:RegisterProtocol(SCRoleWuHunInfoResp, "SCRoleWuHunInfoResp")
	self:RegisterProtocol(SCRoleWuHunUseSkillResp, "SCRoleWuHunUseSkillResp")
	self:RegisterProtocol(SCRoleWuHunAuraId, "SCRoleWuHunAuraId")

	-- 武魂塔
	self:RegisterProtocol(CSWuHunTowerOperate)
	self:RegisterProtocol(SCWuHunTowerItemInfo, "SCWuHunTowerItemInfo")
	self:RegisterProtocol(SCWuHunTowerItemUpdate, "SCWuHunTowerItemUpdate")
	self:RegisterProtocol(SCWuHunTowerRankInfo, "SCWuHunTowerRankInfo")
	self:RegisterProtocol(SCWuHunTowerSceneInfo, "SCWuHunTowerSceneInfo")

	--魂阵
	self:RegisterProtocol(CSHunZhenOperate)
	self:RegisterProtocol(SCHunZhenAllInfo, "SCHunZhenAllInfo")
	self:RegisterProtocol(SCHunZhenSoulFrontInfo, "SCHunZhenSoulFrontInfo")
	self:RegisterProtocol(SCHunZhenSoulStoneInfo, "SCHunZhenSoulStoneInfo")
	self:RegisterProtocol(SCHunZhenEngraveInfo, "SCHunZhenEngraveInfo")
	self:RegisterProtocol(SCHunZhenSoulFrontUse, "SCHunZhenSoulFrontUse")
	self:RegisterProtocol(SCWuHunBag, "SCWuHunBag")
	self:RegisterProtocol(SCWuHunBagGrid, "SCWuHunBagGrid")

	--武魂材料直购.
	self:RegisterProtocol(SCRoleWuHunRmbBuyInfo, "SCRoleWuHunRmbBuyInfo")

	--武魂特权.
	self:RegisterProtocol(SCWuHunRightUpdate, "SCWuHunRightUpdate")
	self:RegisterProtocol(SCWuHunDraw2Result, "SCWuHunDraw2Result")
end

--物品改变刷新界面背包
function WuHunWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local is_need = self.wuhun_data:ChceckItemChangeRefresh(change_item_id)

	if is_need then
		self.wuhun_data:RefreshWuHunRedActiveState()
		if self.wuhun_cultivate_view:IsOpen() then
			self.wuhun_cultivate_view:ItemChangeFlush()
		end
		RemindManager.Instance:Fire(RemindName.WuHunDetails)

		if self.wuhun_lianhun_view:IsOpen() then
			self.wuhun_lianhun_view:ItemChangeFlush()
		end
	end
end

function WuHunWGCtrl:IsOpenTianShenWuHun()
	return self.tianshen_view:IsOpen() and self.tianshen_view.show_index == TabIndex.wuhun_details
end

function WuHunWGCtrl:SendRoleWuHunOperate(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleWuHunReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.reserve1 = 0
	protocol:EncodeAndSend()
end

function WuHunWGCtrl:SCRoleSingleWuHunInfo(protocol)
	local is_active = self.wuhun_data:RefreshSingleData(protocol)
	RemindManager.Instance:Fire(RemindName.WuHunDetails)
	RemindManager.Instance:Fire(RemindName.WuHunFront)

	if protocol.opera_type == ROLE_OPERA_TYPE.OPER_TYPE_BREAK then
		ViewManager.Instance:FlushView(GuideModuleName.WuHunCultivateView, nil, "Tupo")
		local main_role = Scene.Instance:GetMainRole()
		if main_role and protocol.wuhun then
			main_role:SetAttr("wuhun_zhenshen", protocol.wuhun.wuhun_id, protocol.wuhun.break_times)
		end
	else
		ViewManager.Instance:FlushView(GuideModuleName.WuHunView, TabIndex.wuhun_details)
		ViewManager.Instance:FlushView(GuideModuleName.WuHunCultivateView)
	end

	if self.wuhun_skill_view:IsOpen() then
		self.wuhun_skill_view:Flush()
	end

	if self.wuhun_lianhun_view:IsOpen() then
		self.wuhun_lianhun_view:Flush()
	end

	GlobalEventSystem:Fire(SkillEventType.FLUSH_SKILL_LIST)

	if is_active then
		local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(protocol.wuhun.wuhun_id)

		if active_cfg then
			local data = {appe_image_id = active_cfg.appe_image_id, appe_type = ROLE_APPE_TYPE.WU_HUN_ZHEN_SHEN, index_param = protocol.wuhun.wuhun_id}
			AppearanceWGCtrl.Instance:OnGetNewAppearance(data)
		end
	end
end

function WuHunWGCtrl:SCRoleWuHunInfoResp(protocol)
	if protocol then
		self.wuhun_data:initServerData(protocol)
		RemindManager.Instance:Fire(RemindName.WuHunDetails)
		ViewManager.Instance:FlushView(GuideModuleName.WuHunView, TabIndex.wuhun_details)
		ViewManager.Instance:FlushView(GuideModuleName.WuHunCultivateView)

		if self.wuhun_skill_view:IsOpen() then
			self.wuhun_skill_view:Flush()
		end

		if self.wuhun_lianhun_view:IsOpen() then
			self.wuhun_lianhun_view:Flush()
		end

		GlobalEventSystem:Fire(SkillEventType.FLUSH_SKILL_LIST)
	end
end

function WuHunWGCtrl:SCRoleWuHunUseSkillResp(protocol)
	-- print_error("WuHunWGCtrl:SCRoleWuHunUseSkillResp", protocol)
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local wuhun_skill_data = SkillWGData.Instance:GetSkillInfoByIndex(protocol.wuhun_skill_index)
		local main_role_x, main_role_y = main_role:GetLogicPos()

		FightWGCtrl.SendPerformSkillReq(
			protocol.wuhun_skill_index,
			1,
			main_role_x,
			main_role_y,
			protocol.wuhun_target_id,
			false,
			main_role_x,
			main_role_y)
	end
end

function WuHunWGCtrl:SCRoleWuHunAuraId(protocol)
	if protocol and protocol.wuhun_id then
		self.wuhun_data:UpdateWunId(protocol)
		local single_data = WuHunWGData.Instance:GetWuHunSingleData(protocol.wuhun_id)
		local break_times = single_data and single_data.break_times or 0
		local main_role = Scene.Instance:GetMainRole()
		
		if main_role then
			main_role:SetAttr("wuhun_zhenshen", protocol.wuhun_id, break_times)
		end

		ViewManager.Instance:FlushView(GuideModuleName.WuHunCultivateView,  nil, "HuanHua")
	end
end

function WuHunWGCtrl:OpenWuHunCultivateView()
	if not self.wuhun_cultivate_view:IsOpen() then
			self.wuhun_cultivate_view:Open()
	else
		self.wuhun_cultivate_view:Flush()
	end
end

function WuHunWGCtrl:OpenWuHunSkillView()
	if not self.wuhun_skill_view:IsOpen() then
		self.wuhun_skill_view:Open()
	end
end

--刷新炼魂.
function WuHunWGCtrl:OpenWuHunLianHunView(data, is_open)
	if not self.wuhun_lianhun_view:IsOpen() then
		if is_open then
			self.wuhun_lianhun_view:Open()
			self.wuhun_lianhun_view:Flush(nil, "LianHun", data)
		end
	else
		self.wuhun_lianhun_view:Flush(nil, "LianHun", data)
	end
end

function WuHunWGCtrl:OpenWuHunZhanLiView()
	if not self.wuhun_zhanli_view:IsOpen() then
		self.wuhun_zhanli_view:Open()
	end
end

function WuHunWGCtrl:OnRoleWuHunZhenShenChange(is_trigger, wuhun_skill_id)
	if is_trigger then
		local wuhun_data = WuHunWGData.Instance:GetWuHunSkillActiveCfg(wuhun_skill_id)
		if wuhun_data then
			self:OpenEffetView(wuhun_data.wuhun_id)
			Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.SKILL, nil, nil, wuhun_data.set_role_camera or 12)
		end
	else
		Scene.Instance:RecoverCamera()
		if self.wuhun_skill_effect_view:IsOpen() then
			self.wuhun_skill_effect_view:TriggerClose()
		end
	end
end

function WuHunWGCtrl:OnRoleWuHunZhenShenHit()
	if self.wuhun_skill_effect_view:IsOpen() then
		self.wuhun_skill_effect_view:NotifyHit()
	end
end

function WuHunWGCtrl:OpenEffetView(wuhun_id)
	self.wuhun_skill_effect_view:AddEffetData(wuhun_id)
	if not self.wuhun_skill_effect_view:IsOpen() then
		self.wuhun_skill_effect_view:Open()
	end
end

function WuHunWGCtrl:OpenSkillShowEffetView(wuhun_id)
	self.skill_show_wuhun_effect_view:SetNoAddMainUINode(1)
	self.skill_show_wuhun_effect_view:AddEffetData(wuhun_id)
	self.skill_show_wuhun_effect_view:Open()
end

function WuHunWGCtrl:CloseSkillShowEffetView()
	if self.skill_show_wuhun_effect_view:IsOpen() then
		self.skill_show_wuhun_effect_view:TriggerClose()
	end
end

-----------------------------------------武魂塔-------------------------------------
function WuHunWGCtrl:RoleDataChangeCallback(attr_name, value, old_value)
	if attr_name == "capability" then				--战斗力
		RemindManager.Instance:Fire(RemindName.WuHunTower)
	elseif attr_name == "level" then
		RemindManager.Instance:Fire(RemindName.WuHunTower)
	end
end

-- 武魂塔操作请求
function WuHunWGCtrl:SendRoleWuHunTowerOperate(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWuHunTowerOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	-- print_error("挑战副本", protocol)
	protocol:EncodeAndSend()
end

-- 武魂塔目标信息
function WuHunWGCtrl:SCWuHunTowerItemInfo(protocol)
	-- print_error("武魂塔目标信息", protocol)
	self.wuhun_wg_tower_data:SetTowerItemInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WuHunView, TabIndex.wuhun_tower)
	RemindManager.Instance:Fire(RemindName.WuHunTower)
end

-- 武魂塔目标信息更新
function WuHunWGCtrl:SCWuHunTowerItemUpdate(protocol)
	-- print_error("武魂塔目标信息更新", protocol)
	self.wuhun_wg_tower_data:UpdateTowerItemInfo(protocol.tower_type, protocol.tower_item)
	ViewManager.Instance:FlushView(GuideModuleName.WuHunView, TabIndex.wuhun_tower)
	RemindManager.Instance:Fire(RemindName.WuHunTower)
end

-- 武魂塔排行榜信息
function WuHunWGCtrl:SCWuHunTowerRankInfo(protocol)
	-- print_error("武魂塔排行榜信息", protocol)
	self.wuhun_wg_tower_data:SetTowerRankInfo(protocol)
	self:FlushWuHunTowerRank()
end

-- 武魂塔场景信息
function WuHunWGCtrl:SCWuHunTowerSceneInfo(protocol)
	-- print_error("武魂塔场景信息", protocol)
	self.wuhun_wg_tower_data:SetTowerSceneInfo(protocol)
	self:FlushWuHunTowerTaskView()
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.fb_end_time)

	if protocol then
		self:CheckWuHunTowerResult(protocol.is_end, protocol.is_pass, protocol.tower_type, protocol.tower_seq)
	end
end

-- 武魂塔场景结算(一键直达不走这个副本)
function WuHunWGCtrl:CheckWuHunTowerResult(is_end, is_pass, tower_type, tower_seq)
	-- print_error("武魂塔场景结算(一键直达不走这个副本)", is_end, is_pass, tower_type, tower_seq)
	if is_end == 1 then
		if is_pass == 1 then	--- 挑战胜利
			self:OpenWuHunTowerJieSuanView(true, true, tower_type, tower_seq)
		else					--- 挑战失败
			self:OpenWuHunTowerJieSuanView(false, true, tower_type, tower_seq)
		end
	end
end

-- 武魂塔场景结算(一键直达)
function WuHunWGCtrl:QuickFinishWuHunTower(result, tower_start_seq, tower_seq, tower_type)
	-- print_error("武魂塔场景结算(一键直达)", result, tower_start_seq, tower_seq, tower_type)
	if result == 1 then	--- 挑战胜利
		self:OpenWuHunTowerJieSuanView(true, false, tower_type, tower_seq, tower_start_seq)
	end
end

-- 武魂塔刷新排行榜
function WuHunWGCtrl:FlushWuHunTowerRank()
	if self.wuhun_view:IsOpen() then
		self.wuhun_view:FlushWuHunTowerRank()
	end
end

-- 挑战副本
function WuHunWGCtrl:ClickTowerGoTo()
	if self.wuhun_view:IsOpen() then
		self.wuhun_view:ClickTowerGoTo()
	end
end


-- 打开武魂塔副本场景奖励面板
function WuHunWGCtrl:OpenWuHunTowerTaskView()
	if not self.wuhun_tower_task_view:IsOpen() then
		self.wuhun_tower_task_view:Open()
	end
end

-- 刷新武魂塔副本场景奖励面板
function WuHunWGCtrl:FlushWuHunTowerTaskView()
	if self.wuhun_tower_task_view:IsOpen() then
		self.wuhun_tower_task_view:Flush()
	end
end

-- 打开武魂塔副本场景奖励面板
function WuHunWGCtrl:CloseWuHunTowerTaskView()
	if self.wuhun_tower_task_view:IsOpen() then
		self.wuhun_tower_task_view:Close()
	end
end

-- 打开武魂塔副本结算界面
-- param: 是否表示是单个通关
function WuHunWGCtrl:OpenWuHunTowerJieSuanView(is_win, is_once, tower_type, tower_seq, tower_start_seq)
	local show_data = {}
	show_data.is_win = is_win
	show_data.is_once = is_once
	show_data.tower_type = tower_type
	show_data.tower_seq = tower_seq
	show_data.tower_start_seq = tower_start_seq

	self.wuhun_tower_jiesuan_view:SetShowData(show_data)

	if not self.wuhun_tower_jiesuan_view:IsOpen() then
		self.wuhun_tower_jiesuan_view:Open()
	else
		self.wuhun_tower_jiesuan_view:Flush()
	end
end









-----------------------------------------------------魂阵相关 开始--------------------------------------------------
--请求魂阵相关的信息
function WuHunWGCtrl:SendWuHunFrontOperate(opera_type, param1, param2, param3, param4, param5)
	local protocol = ProtocolPool.Instance:GetProtocol(CSHunZhenOperate)
	protocol.operate_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol.param5 = param5 or {}
	protocol:EncodeAndSend()
end

--下发所有的魂阵信息
function WuHunWGCtrl:SCHunZhenAllInfo(protocol)
	self.wuhun_front_wg_data:SetWuHunFrontData(protocol)

	RemindManager.Instance:Fire(RemindName.WuHunFront)
end

function WuHunWGCtrl:SCHunZhenSoulFrontInfo(protocol)
	self.wuhun_front_wg_data:UpdateFrontPropertyData(protocol)

	self:ReflushWuHunView()

	--如果属性界面打开了就刷新
	if self.wuhun_front_property_tip:IsOpen() then
		self.wuhun_front_property_tip:Flush()
	end

	RemindManager.Instance:Fire(RemindName.WuHunFront)
end

function WuHunWGCtrl:SCHunZhenSoulStoneInfo(protocol)
	self.wuhun_front_wg_data:UpdateFrontGemData(protocol)

	--自动强化继续
	if self.wuhun_view:IsOpen() and self.wuhun_view:GetIsAutoStrenge() then
		self.wuhun_view:FrontStrengeTimerFunc()
	end

	if self.wuhun_gem_front_view:IsOpen() and self.wuhun_gem_front_view:GetIsAutoGrade() then
		self.wuhun_gem_front_view:FrontGradeTimerFunc()
	end

	self:ReflushWuHunView()

	RemindManager.Instance:Fire(RemindName.WuHunFront)
end

function WuHunWGCtrl:SCHunZhenEngraveInfo(protocol)
	self.wuhun_front_wg_data:UpdateFrontGemEngraveItem(protocol)

	self:ReflushWuHunView()

	RemindManager.Instance:Fire(RemindName.WuHunFront)
end

function WuHunWGCtrl:SCHunZhenSoulFrontUse(protocol)
	self.wuhun_front_wg_data:UpdateHuanHuaFrontIndex(protocol)

	self:ReflushWuHunView()

	RemindManager.Instance:Fire(RemindName.WuHunFront)
end

--刷新打开的武魂界面
function WuHunWGCtrl:ReflushWuHunView()
	if self.wuhun_view:IsOpen() then
		self.wuhun_view:Flush(TabIndex.wuhun_front)
	end

	if self.wuhun_gem_front_view:IsOpen() then
		self.wuhun_gem_front_view:Flush()
	end
end

-- 武魂魂阵升阶回复
function WuHunWGCtrl:FrontGemGradeResult(result)
	if result == 1 then	--- 挑战胜利
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuhunFrontDieZhenTxt1)
	else --失败这里要刷新下界面
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuhunFrontDieZhenTxt2)

		if self.wuhun_gem_front_view:IsOpen() and self.wuhun_gem_front_view:GetIsAutoGrade() then
			self.wuhun_gem_front_view:FrontGradeTimerFunc()
		end
		--RemindManager.Instance:Fire(RemindName.WuHunFront)
	end
end

--刷新属性界面
function WuHunWGCtrl:SetFrontPropertyTipShow(data)
	self.wuhun_front_property_tip:SetData(data)

	if not self.wuhun_front_property_tip:IsOpen() then
		self.wuhun_front_property_tip:Open()
	end
end

--打开武魂宝石界面需要传选中的武魂ID 魂阵ID 魂石ID
function WuHunWGCtrl:OpenFrontGemShow(gem_data)
	local param = {}
	gem_data = gem_data or {}
	param.cur_front_wuhun_index = gem_data.cur_front_wuhun_index or 1
	param.cur_hunzhen_index = gem_data.cur_hunzhen_index or 1
	param.cur_front_gem_index = gem_data.cur_front_gem_index or 1

	if self.wuhun_gem_front_view:IsOpen() then
		self.wuhun_gem_front_view:Flush(nil, "set_data", param)
	else
		ViewManager.Instance:Open(GuideModuleName.WuHunGemFrontView, nil, "set_data", param)
	end
end

--打开宝石选择界面
function WuHunWGCtrl:OpenBaoShiSelectView(param)
	self.select_baoshi_view:SetData(param)
	self.select_baoshi_view:Open()
end

--打开宝石升级界面
function WuHunWGCtrl:OpenBaoShiUpgradeView(param)
	self.front_baoshi_upgrade_view:SetData(param)

	self.front_baoshi_upgrade_view:Open()
end

function WuHunWGCtrl:InitBaoShiUpGradeAlertTips(call_back)
	if not self.bs_cost_prop_alert then
		local alert = Alert.New(nil, nil, nil, nil, true)
		alert:SetShowCheckBox(true, "wuhun_baoshi_upgrade")
		alert:SetCheckBoxDefaultSelect(false)
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
		self.bs_cost_prop_alert = alert
	end

	if not self.bs_cost_gold_alert then
		local alert = Alert.New()
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		self.bs_cost_gold_alert = alert
	end
end

function WuHunWGCtrl:DeleteBaoShiUpGradeAlertTips()
	if self.bs_cost_prop_alert then
		self.bs_cost_prop_alert:DeleteMe()
		self.bs_cost_prop_alert = nil
	end

	if self.bs_cost_gold_alert then
		self.bs_cost_gold_alert:DeleteMe()
		self.bs_cost_gold_alert = nil
	end
end

function WuHunWGCtrl:OpenBaoShiUpGradeAlertTips(is_gold)
	if self.bs_cost_prop_alert and not is_gold then
		self.bs_cost_prop_alert:Open()
	end

	if self.bs_cost_gold_alert and is_gold then
		self.bs_cost_gold_alert:Open()
	end
end

function WuHunWGCtrl:SetBaoShiUpGradeAlertTips(is_gold, str)
	if self.bs_cost_prop_alert and not is_gold then
		self.bs_cost_prop_alert:SetLableString(str)
	end

	if self.bs_cost_gold_alert and is_gold then
		self.bs_cost_gold_alert:SetLableString(str)
	end
end

---------------------------------------武魂背包相关  --------------------------

--设置所有的背包数据
function WuHunWGCtrl:SCWuHunBag(protocol)
	self.wuhun_front_wg_data:SetAllWuHunBagData(protocol)
end

--更新武魂背包的数据
function WuHunWGCtrl:SCWuHunBagGrid(protocol)
	self.wuhun_front_wg_data:UpdateWuHunBagData(protocol)
	RemindManager.Instance:Fire(RemindName.WuHunFront)

	self:ReflushWuHunView()
end

function WuHunWGCtrl:OnWuHunItemDataChange(change_item_id, change_item_index, change_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then

		local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, new_num - old_num))

			self:WuHunKeyUseView(change_item_id, change_item_index)
		end
	end
end

-- 快速使用
function WuHunWGCtrl:WuHunKeyUseView(item_id, item_index)
	local active_cfg = self.wuhun_front_wg_data:GetIsFrontGemByItemId(item_id)
	if active_cfg and self.wuhun_data:GetWuHunIsActive(active_cfg.wuhun_id) then
		local change_data = self.wuhun_front_wg_data:GetWuHunBagItem(item_index)
		FunctionGuide.Instance:OpenWuHunKeyUseView(change_data)
	end
end

--跳转到武魂魂阵界面
function WuHunWGCtrl:JumpWUHunFrontView(wuhun_id, front_index, gem_index)
	local param_t = {wuhun_id = wuhun_id, front_index = front_index, gem_index = gem_index}
	if not self.wuhun_view:IsOpen() then
		ViewManager.Instance:Open(GuideModuleName.WuHunView, TabIndex.wuhun_front, "jump", param_t)
	else
		self.wuhun_view:Flush(TabIndex.wuhun_front, "jump", param_t)
	end
end
-----------------------------------------------------魂阵相关 结束--------------------------------------------------

---------------------------------------------武魂材料直购 开始---------------------------------------------
function WuHunWGCtrl:SCRoleWuHunRmbBuyInfo(protocol)
	self.wuhun_data:SetWuhunMaterialBuyInfo(protocol)

	if self.wuhun_view:IsOpen() and self.wuhun_material_buy_view:IsOpen() then
		self.wuhun_view:OnClickBtnOpenWuHunMaterialBuyView()
		self.wuhun_material_buy_view:SetWuHunMaterialBuyCountDown()
	end
end
---------------------------------------------武魂材料直购 结束---------------------------------------------

---------------------------------------------武魂特权 开始---------------------------------------------
function WuHunWGCtrl:SCWuHunRightUpdate(protocol)
	self.wuhun_data:SetWuhunPrerogativeUpdate(protocol)

	if self.wuhun_prerogative_view:IsOpen() then
		self.wuhun_prerogative_view:Flush(nil, "SetPanel1")
	end

	RemindManager.Instance:Fire(RemindName.WuHunDetails)
	if self.wuhun_view:IsOpen() then
		self.wuhun_view:SetWuHunPrerogativeBtn()
	end
end

function WuHunWGCtrl:SCWuHunDraw2Result(protocol)
	self.wuhun_data:SetWuhunPrerogativeDraw(protocol)

	if self.wuhun_prerogative_view:IsOpen() then
		self.wuhun_prerogative_view:Flush(nil, "SetPanel2")
	end

	RemindManager.Instance:Fire(RemindName.WuHunDetails)
	if self.wuhun_view:IsOpen() then
		self.wuhun_view:SetWuHunPrerogativeBtn()
	end

	local ok_func = function()
		--数量检测
		local item_id = WuHunWGData.Instance:GetWuhunPrerogativeDrawCostItem()
		local num = ItemWGData.Instance:GetItemNumInBagById(item_id)

		local cfg = WuHunWGData.Instance:GetWuhunPrerogativeDrawModeCfg()
		cfg = cfg[protocol.mode]
		--不足弹窗
		if num < cfg.cost_item_num then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
		else
			--使用
			self:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_DRAW, protocol.mode)
		end
	end

	local other_info = {}
	other_info.again_text = string.format(Language.WuHunPrerogative.DrawBtnStr, NumberToChinaNumber(protocol.count))
	TipWGCtrl.Instance:ShowGetCommonReward(protocol.result_item_list, ok_func, other_info, false)
end

function WuHunWGCtrl:OpenWuHunPrerogativeLibrayView()
	self.wuhun_prerogative_library_view:Open()
end

function WuHunWGCtrl:OpenWuHunPrerogativeProbabilityView()
	self.wuhun_prerogative_probability_view:Open()
end

---------------------------------------------武魂特权 结束---------------------------------------------