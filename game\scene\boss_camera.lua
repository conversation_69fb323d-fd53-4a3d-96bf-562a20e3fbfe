BossCamera = BossCamera or BaseClass()
local TypeUINameTable = typeof(UINameTable)
local XiuWeiAnimTime = 1.8

function BossCamera:__init()
	if BossCamera.Instance then
		print_error("[BossCamera] Attempt to create singleton twice!")
		return
	end
	BossCamera.Instance = self

    --- 新增boss视角摄象机
    self.show_boss_camera_obj = nil
    self.show_boss_follow_camera = nil
    self.show_boss_camera = nil
    self.show_boss_timer = nil
    self.is_show_boss_time = false

    self.boss_name_ui_loader = nil
    self.show_boss_ui_canvas = nil
end

function BossCamera:__delete()
    --- 新增boss视角摄象机
    if self.show_boss_camera_obj then
        ResPoolMgr:Release(self.show_boss_camera_obj)
        self.show_boss_camera_obj = nil
    end

    self.show_boss_follow_camera = nil
    self.show_boss_camera = nil
    self.is_show_boss_time = false

    if self.show_boss_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.show_boss_timer)
		self.show_boss_timer = nil
	end

    BossCamera.Instance = nil
    self.show_boss_ui_canvas = nil
    self:TryDestroyUILoader()
end

function BossCamera:TryDestroyUILoader()
    if self.boss_name_ui_loader ~= nil then
        self.boss_name_ui_loader:Destroy()
    end

    self.boss_name_ui_loader = nil
end


function BossCamera:InitBossFollowCamera()
    if nil == self.show_boss_follow_camera then
        self.show_boss_camera_obj = ResPoolMgr:TryGetGameObject("scenes_prefab", "CameraBossFollow")
        local GameRootTransform = GameObject.Find("GameRoot").transform
        self.show_boss_ui_canvas = self.show_boss_camera_obj.transform:Find("UI_Canvas")
        self.show_boss_camera_obj.transform:SetParent(GameRootTransform, false)
        self.show_boss_follow_camera = self.show_boss_camera_obj:GetComponent(typeof(CameraFollow))
        self.show_boss_camera = self.show_boss_camera_obj:GetComponentInChildren(typeof(UnityEngine.Camera), true)
        self.show_boss_camera_anim = self.show_boss_camera_obj:GetComponentInChildren(typeof(UnityEngine.Animator), true)

        self.show_boss_camera_obj:SetActive(false)
        if not IsNil(self.show_boss_follow_camera) then
            self.show_boss_follow_camera.Distance = 7.2
        end
    else
        local boss_camera_focal_point = GameObject.New("BossCamerafocalPoint")
        self.show_boss_follow_camera:CreateFocalPoint(boss_camera_focal_point)
    end
end

function BossCamera:SetBossFollowCameraTarget(target_obj, target_point)
    if target_obj ~= nil and (not IsNil(self.show_boss_follow_camera)) then
        self.show_boss_follow_camera.Target = target_point
        self.show_boss_follow_camera.targetOffset = Vector3(0, -0.1, 0)
        self.show_boss_follow_camera:SyncImmediate()
        -- SceneObj:GetRotation()
        local trans = target_obj.draw_obj:GetTransfrom()
        local angle = trans.localEulerAngles
        -- local r_x, r_y = GameMapHelper.LogicToWorld(logic_pos.x, logic_pos.y)
        self.show_boss_follow_camera:ChangeAngle(Vector2(19, angle.y))

        if not IsNil(self.show_boss_camera) then
            self.show_boss_camera.transform.localPosition = Vector3(0, 0, -2)
        end
    end
end

-- 展示境界界面镜头
function BossCamera:BossFollowXiuWeiBianShenCaneraShow()
    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil then
        return
    end

    local target_point = main_role:GetRoot() and main_role:GetRoot().transform or nil
    local x, y, z = main_role:GetLookAtPointPos()
    local point = main_role.draw_obj:GetLookAtPoint(x, y, z, false)
    local target_point = point or target_point

    if not IsNil(self.show_boss_camera) then
        self.show_boss_camera.transform.localPosition = Vector3(0, 0, -2)
    end

    if not IsNil(self.show_boss_camera_anim) then
        self.show_boss_camera_anim.enabled = true
    end

    self:SetBossFollowCameraTarget(main_role, target_point)
    self:BossShow(true)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	-- 执行boss动作
    self.is_show_boss_time = true
    
	if self.show_boss_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.show_boss_timer)
		self.show_boss_timer = nil
	end

	self.show_boss_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self.is_show_boss_time = false
		self:BossShow(false)
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end, XiuWeiAnimTime)
end

-- 展示boss镜头
function BossCamera:BossFollowCaneraShow(monster_obj, cg_time, operate_param_t, camera_offset_t, bundle, assets, interlude_name_img, is_not_close_all)
    if operate_param_t == nil or camera_offset_t == nil then
        return
    end

    if not is_not_close_all then
        ViewManager.Instance:CloseAll()
    end

    local angle_x = tonumber(operate_param_t[1])
    local angle_y = tonumber(operate_param_t[2])
    local dis = tonumber(operate_param_t[3])
    local offset_x = tonumber(camera_offset_t[1])
    local offset_y = tonumber(camera_offset_t[2])

    if angle_x == nil or angle_y == nil or dis == nil or offset_x == nil or offset_y == nil then
        return
    end
    
    if not IsNil(self.show_boss_camera_anim) then
        self.show_boss_camera_anim.enabled = false
    end

    if not IsNil(self.show_boss_camera) then
        self.show_boss_camera.transform.localPosition = Vector3(0, 0, -2)
    end

    if not IsNil(self.show_boss_follow_camera) and monster_obj and monster_obj:GetRoot() then
        self.show_boss_follow_camera.Target = monster_obj:GetRoot().transform
        self.show_boss_follow_camera.targetOffset = Vector3(offset_x, offset_y, 0)
        self.show_boss_follow_camera.Distance = Scene.Instance:GetFixCameraDis(dis)
        self.show_boss_follow_camera:ChangeAngle(Vector2(angle_x, angle_y))
        self.show_boss_follow_camera:SyncImmediate()
        self.show_boss_follow_camera:SyncRotation()
    end

    self:TryDestroyUILoader()
    self.boss_name_ui_loader = AllocAsyncLoader(self, "show_boss_ui")
    self.boss_name_ui_loader:SetParent(self.show_boss_ui_canvas.transform, false)
    self.boss_name_ui_loader:Load(bundle, assets, function(obj)
        if obj then
            local name_table = obj:GetComponent(TypeUINameTable)
            local node_list = U3DNodeList(name_table)
            local name_str = string.format("boss_name_0%d", interlude_name_img)
            if node_list and node_list[name_str] then
                node_list[name_str]:SetActive(true)
            end
        end
    end)


    self:BossShow(true)
	-- 执行boss动作
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
    self.is_show_boss_time = true

	if self.show_boss_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.show_boss_timer)
		self.show_boss_timer = nil
	end

	self.show_boss_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self.is_show_boss_time = false
		self:BossShow(false)
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

        local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
        if not fuben_data or not next(fuben_data) then return end
        local new_fb_cfg = FuBenWGData.Instance:GetNewPlayerFbCfg(fuben_data.fb_type)
        if new_fb_cfg == nil or new_fb_cfg.temp_xiuwei_index ~= -1 then
            return
        end
	end, cg_time)
end

function BossCamera:BossFollowCaneraShowStatus()
    return self.is_show_boss_time
end

function BossCamera:BossShow(is_forward)
    Scene.Instance:CtrlFollowCameraEnabled(not is_forward)

    if UICamera ~= nil  then
        UICamera.enabled = not is_forward
    end

    if not is_forward then
        self:TryDestroyUILoader()
    end

	if self.show_boss_camera_obj ~= nil then
		self.show_boss_camera_obj:SetActive(is_forward)
	end

    -- -- 触发展示时关掉UI
    -- if is_forward then
    --     ViewManager.Instance:CloseAll()
    -- end
 
	local UIRoot = GameObject.Find("GameRoot/UILayer").transform
	local canvas_group = nil

	if not IsNil(UIRoot) then
		canvas_group = UIRoot:GetComponent(typeof(UnityEngine.CanvasGroup))
	end

	if not IsNil(canvas_group) then
		canvas_group.blocksRaycasts = not is_forward
	end
end

