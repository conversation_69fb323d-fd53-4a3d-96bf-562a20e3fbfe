Story = Story or BaseClass()

function Story:__init(step_list_cfg, story_view, story_id)
	self.step_list_cfg = step_list_cfg
	self.step = 0
	self.is_playing = true
	self.story_id = story_id
	self.clock_end_time = 0
	self.story_view = story_view
	self.do_delay_list = {}
	self.effect_obj_map = {}
	self.code_if_then_list = {}
	self.block_t = {}
	self.last_check_monster_hp_time = 0

	-- 某个机器人强制一段时间干某件事(主要用于主角)
	self.force_t = {
		f_robert_id = 0,
		f_time_interval = 0,
		f_do_type = "",
		f_do_param_t = {},
		f_do_next_time = 0,
	}

	Runner.Instance:AddRunObj(self, 5)

	self.stop_gather_event = GlobalEventSystem:Bind(ObjectEventType.STOP_GATHER, BindTool.Bind(self.OnStopGather, self))
	self.attack_robert_event = GlobalEventSystem:Bind(OtherEventType.ROBERT_ATTACK_ROBERT, BindTool.Bind(self.OnRobertAttackRobert, self))
	self.robert_die_event = GlobalEventSystem:Bind(OtherEventType.ROBERT_DIE, BindTool.Bind(self.OnRobertDie, self))
	self.load_scene_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnLoadSceneComplete, self))
	self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind1(self.OnCloseLoadingView, self))
	self.camera_handle_change = GlobalEventSystem:Bind(MainUIEventType.CAMERA_HANDLE_CHANGE, BindTool.Bind(self.CameraHandleChange, self))
end

function Story:__delete()
	if nil ~= CgManager.Instance then
		CgManager.Instance:Stop()
	end

	GlobalEventSystem:UnBind(self.stop_gather_event)
	GlobalEventSystem:UnBind(self.attack_robert_event)
	GlobalEventSystem:UnBind(self.robert_die_event)
	GlobalEventSystem:UnBind(self.load_scene_complete_event)
	GlobalEventSystem:UnBind(self.close_loading_view_event)
	GlobalEventSystem:UnBind(self.camera_handle_change)

	for _, v in ipairs(self.do_delay_list) do
		GlobalTimerQuest:CancelQuest(v)
	end
	self.do_delay_list = {}

	for k,v in pairs(self.effect_obj_map) do
		v:Destroy()
		v:DeleteMe()
	end
	self.effect_obj_map = {}

	-- 很重要，如果剧情里的场景跟进入时的场景一样，是不会重复加载场景的，导致block残留
	for k, _ in pairs(self.block_t) do
		local i = math.floor(k / 100000)
		local j = k % 100000
		AStarFindWay:RevertBlockInfo(i, j)
	end
	self.block_t = {}
	self.is_playing = false
	if self.fly_icon_timer then
		GlobalTimerQuest:CancelQuest(self.fly_icon_timer)
		self.fly_icon_timer = nil
	end

	self.last_check_monster_hp_time = 0

	Runner.Instance:RemoveRunObj(self)
end

function Story:Update(now_time, elapse_time)
	self:CheckMoveInArea()
	self:CheckMoveInAreaRepeat()
	self:CheckRoleLevel()
	self:CheckTaskReceived()
	self:CheckTaskCanCommit()
	self:CheckClockEnd(now_time)
	self:CheckForceDo(now_time)
	self:CheckDelayDo(now_time)
	self:CheckMonsterHp(now_time)
end

-- 通过设置来触发
function Story:SetTrigger(trigger, trigger_param)
	if not self.is_playing then
		return
	end

	trigger_param = trigger_param or ""

	local check_count = 0
	while check_count < 100 do
		check_count = check_count + 1
		local step_cfg = self.step_list_cfg[self.step + 1]
		if nil == step_cfg or step_cfg.trigger ~= trigger or step_cfg.trigger_param ~= trigger_param then
			break
		end

		self.step = self.step + 1
		self:DoOperate(step_cfg.operate, Split(step_cfg.operate_param, "##"))
	end

	if nil ~= self.step_list_cfg[self.step] and nil ~= self.story_view then
		self.story_view:ShowStepDesc(self.step_list_cfg[self.step].desc, BindTool.Bind(self.DoOperate, self))
	end

	self:CheckCodeIfThen(trigger, trigger_param)
end

-- 加载场景完成
function Story:OnLoadSceneComplete()
	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.ENTER_SCENE ~= step_cfg.trigger then
		return
	end

	local new_scene_type = Scene.Instance:GetSceneType()
	if Scene.Instance:IsFirstEnterScene() and SceneType.Common ~= new_scene_type then
		return
	end

	local param_t = Split(step_cfg.trigger_param, "##")
	if #param_t > 0 and Scene.Instance:GetSceneId() ~= tonumber(param_t[1]) then
		return
	end

	for i, v in ipairs(param_t) do
		if param_t[i] == "role_level" and tonumber(param_t[i + 1]) ~= RoleWGData.Instance:GetRoleVo().level then
			return
		end

		if param_t[i] == "task_id" and not TaskWGData.Instance:GetTaskIsAccepted(tonumber(param_t[i + 1])) then
			return
		end
	end
	self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
end

-- loading进度条关闭
function Story:OnCloseLoadingView()
	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.CLOSE_LOADING_VIEW ~= step_cfg.trigger then
		return
	end

	local new_scene_type = Scene.Instance:GetSceneType()
	if Scene.Instance:IsFirstEnterScene() and SceneType.Common ~= new_scene_type then
		return
	end

	local param_t = Split(step_cfg.trigger_param, "##")
	if #param_t > 0 and Scene.Instance:GetSceneId() ~= tonumber(param_t[1]) then
		return
	end

	for i, v in ipairs(param_t) do
		if param_t[i] == "role_level" and tonumber(param_t[i + 1]) ~= RoleWGData.Instance:GetRoleVo().level then
			return
		end

		if param_t[i] == "task_id" and not TaskWGData.Instance:GetTaskIsAccepted(tonumber(param_t[i + 1])) then
			return
		end
	end
	self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
end



-- 手动改变相机
function Story:CameraHandleChange()
	if CAMERA_TYPE == CameraType.Fixed then
		if self.fightstate_camera_param then
			self.fightstate_camera_param.y = nil
		end
	else
		if self.fightstate_camera_param then
			self.fightstate_camera_param.x = nil
			self.fightstate_camera_param.y = nil
		end
	end
end

-- 检查移动到某个区域触发
function Story:CheckMoveInAreaRepeat()
	if Scene.Instance then
		Scene.Instance:CheckAreaCamera()
	end

	local step_cfg = self.step_list_cfg[1]
	if nil == step_cfg or S_STEP_TRIGGER.MOVE_INTO_AREA ~= step_cfg.trigger or step_cfg.can_repeat ~= 1 then
		return
	end

	if not Scene.Instance or not Scene.Instance:GetMainRole() then
		return
	end
	local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()

	if self.trigger_inter_area then
		if not GameMath.IsInRect(role_x, role_y, self.trigger_inter_area.x, self.trigger_inter_area.y, self.trigger_inter_area.w, self.trigger_inter_area.h) then
			self.trigger_inter_area = nil
			self.step = 0
		end
	else
		if nil == self.trigger_area then
			self.trigger_area = {}
			local param_t = Split(step_cfg.trigger_param, "##")
			self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h = tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]), tonumber(param_t[4])
			if self:IsPlay(param_t[5], tonumber(param_t[6])) then
				self.trigger_area = nil
				return
			end
		end

		if GameMath.IsInRect(role_x, role_y, self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h) then
			if step_cfg.scene_id == 198 then		-- 特殊场景需要变身后才触发镜头
				local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
				if main_role_vo and main_role_vo.special_appearance == 0 then
					return
				end
			end

			self.trigger_inter_area = {}
			self.trigger_inter_area.x, self.trigger_inter_area.y, self.trigger_inter_area.w, self.trigger_inter_area.h = self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h
			self.trigger_area = nil
			self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
		end
	end
end

-- 检查移动到某个区域触发
function Story:CheckMoveInArea()
	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.MOVE_INTO_AREA ~= step_cfg.trigger or step_cfg.can_repeat == 1 then
		return
	end

	if not Scene.Instance then
		return
	end

	local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
	if nil == self.trigger_area then
		self.trigger_area = {}
		local param_t = Split(step_cfg.trigger_param, "##")
		self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h = tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]), tonumber(param_t[4])
		if self:IsPlay(param_t[5], tonumber(param_t[6])) then
			self.trigger_area = nil
			return
		end
	end

	if GameMath.IsInRect(role_x, role_y, self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h) then
		self.trigger_area = nil
		self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
	end
end

--设置cg是否播放
function Story:IsPlay(param1, param2)
	if param1 == "role_level" then
		return tonumber(param2) ~= RoleWGData.Instance:GetRoleVo().level
	elseif param1 == "task_id" then
		return not TaskWGData.Instance:GetTaskIsAccepted(tonumber(param2))
	end
	return false
end

-- 检查角色等级
function Story:CheckRoleLevel()
	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.ROLE_LEVEL ~= step_cfg.trigger then
		return
	end

	if step_cfg.trigger_param == RoleWGData.Instance:GetRoleVo().level then
		self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
	end
end

-- 检查任务是否接受
local RECEIVED_TASK_TRIGGER_RECORD = {}
function Story:CheckTaskReceived()
	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.RECEIVED_TASK ~= step_cfg.trigger
		or (Scene.Instance:GetSceneType() == 0 and RECEIVED_TASK_TRIGGER_RECORD[step_cfg.scene_id * 1000 + step_cfg.story_id + step_cfg.trigger_param]) then
		return
	end

	if nil == self.trigger_task_id then
		local param_t = Split(step_cfg.trigger_param, "##")
		self.trigger_task_id = tonumber(param_t[1])
	end

	if TaskWGData.Instance:GetTaskIsAccepted(self.trigger_task_id) then
		self.trigger_task_id = nil
		self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
		RECEIVED_TASK_TRIGGER_RECORD[step_cfg.scene_id * 1000 + step_cfg.story_id + step_cfg.trigger_param] = 1
	end
end

--任务可提交触发
function Story:CheckTaskCanCommit()
	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.TASK_CAN_COMMIT ~= step_cfg.trigger
		or (Scene.Instance:GetSceneType() == 0 and RECEIVED_TASK_TRIGGER_RECORD[step_cfg.scene_id * 1000 + step_cfg.story_id + step_cfg.trigger_param]) then
		return
	end

	if nil == self.trigger_task_id then
		local param_t = Split(step_cfg.trigger_param, "##")
		self.trigger_task_id = tonumber(param_t[1])
	end

	if TaskWGData.Instance:GetTaskIsCanCommint(self.trigger_task_id) then
		self.trigger_task_id = nil
		self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
		RECEIVED_TASK_TRIGGER_RECORD[step_cfg.scene_id * 1000 + step_cfg.story_id + step_cfg.trigger_param] = 1
	end
end

-- 检查时钟时否结束
function Story:CheckClockEnd(now_time)
	if self.clock_end_time > 0 and now_time >= self.clock_end_time then
		self.clock_end_time = 0
		self:SetTrigger(S_STEP_TRIGGER.CLOCK_END)
	end
end

-- 检测强制主角干某事
function Story:CheckForceDo(now_time)
	if now_time < self.force_t.f_do_next_time or "" == self.force_t.f_do_type then
		return
	end

	local force_robert =  RobertManager.Instance:GetRobertByRobertId(self.force_t.f_robert_id)
	if nil == force_robert then
		return
	end

	if S_FORCE_DO_TYPE.F_MOVE_TO == self.force_t.f_do_type then
		local p1x, p1y = force_robert:GetLogicPos()
		local p2x, p2y = tonumber(self.force_t.f_do_param_t[1]), tonumber(self.force_t.f_do_param_t[2])

		if force_robert:IsStand() and GameMath.GetDistance(p1x, p1y, p2x, p2y, false) > 1 then
			RobertManager.Instance:RobertMoveTo(self.force_t.f_robert_id, p2x, p2y)
			self.force_t.f_do_next_time = now_time + self.force_t.f_time_interval
		end
	end

	if S_FORCE_DO_TYPE.F_ATTACK_TARGET == self.force_t.f_do_type then
		for _, v in ipairs(self.force_t.f_do_param_t) do
			local target_robert = RobertManager.Instance:GetRobertByRobertId(tonumber(v))
			if RobertManager.Instance:IsEnemy(force_robert, target_robert)  then
				self.force_t.f_do_next_time = now_time + self.force_t.f_time_interval
				RobertManager.Instance:RobertAtkTarget(self.force_t.f_robert_id, tonumber(v))
				break
			end
		end
	end

	if S_FORCE_DO_TYPE.F_GATHER == self.force_t.f_do_type then
		if force_robert:IsStand() then
			self.force_t.f_do_next_time = now_time + self.force_t.f_time_interval
			RobertManager.Instance:RobertStartGather(self.force_t.f_robert_id, tonumber(self.force_t.f_do_param_t[1]))
		end
	end
end

function Story:CheckDelayDo(now_time)
	for i = #self.do_delay_list, 1, -1 do
		if now_time >= self.do_delay_list[i].do_time then
			local t = table.remove(self.do_delay_list, i)
			self:DoOperate(t.operate, t.param_t)
		end
	end
end

-- 检查怪物血量
function Story:CheckMonsterHp(now_time)
	-- 0.2秒检查一次
	if now_time - self.last_check_monster_hp_time < 0.2 then
		return
	end

	self.last_check_monster_hp_time = now_time

	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.MONSTER_HP_CHANGE ~= step_cfg.trigger then
		return
	end
	local param_list = Split(step_cfg.trigger_param, "##")
	local monster = Scene.Instance:GetMonsterByMonsterId(tonumber(param_list[1]))
	if monster then
		monster:NotifyDieEvent("CheckMonsterHp", function()
			self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
		end)

		local vo = monster:GetVo()
		if vo.hp / vo.max_hp <= tonumber(param_list[2]) and not monster:IsDead() then
			self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
			monster:UnNotifyDieEvent("CheckMonsterHp")
		end
	end
end

function Story:CheckCodeIfThen(trigger, trigger_param)
	if #self.code_if_then_list <= 0 then
		return
	end

	local del_list = {}
	for i, v in ipairs(self.code_if_then_list) do
		if v.condition == trigger or
			v.condition == trigger .. "##" .. trigger_param then
			for _, execute in ipairs(v.execute_list) do
				local t = Split(execute, "##")
				self:DoOperate(table.remove(t, 1), t)

				table.insert(del_list, i)
			end
		end
	end

	for i = #del_list, 1, -1 do
		table.remove(self.code_if_then_list, del_list[i])
	end
end

-- 采集结束(服务器返回)
function Story:OnStopGather(reason, role_obj_id)
	-- if role_obj_id ~= Scene.Instance:GetMainRole():GetObjId() or 1 ~= reason then
	-- 	return
	-- end

	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.GATHER_END ~= step_cfg.trigger then
		return
	end

	if step_cfg.trigger_param ~= role_obj_id then
		return
	end

	-- 采集结束
	self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
end

-- 机器人打机器人触发
function Story:OnRobertAttackRobert(attacker_robert_id, target_robert_id)
	local step_cfg = self.step_list_cfg[self.step + 1]
	if nil == step_cfg or S_STEP_TRIGGER.BE_ATTACKED ~= step_cfg.trigger and S_STEP_TRIGGER.ROBERT_HP_CHANGE ~= step_cfg.trigger then
		return
	end

	local param_t = Split(step_cfg.trigger_param, "##")
	if target_robert_id == tonumber(param_t[1]) then
		if S_STEP_TRIGGER.BE_ATTACKED == step_cfg.trigger then
			self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
		else
			local robert = RobertManager.Instance:GetRobertByRobertId(target_robert_id)
			if robert and robert:GetObj():GetVo().hp / robert:GetObj():GetVo().max_hp <= tonumber(param_t[2]) then
				self:SetTrigger(step_cfg.trigger, step_cfg.trigger_param)
			end
		end
	end
end

-- 机器人死亡触发
function Story:OnRobertDie(robert_id)
	self:SetTrigger(S_STEP_TRIGGER.ROBERT_DIE, robert_id)
end

function Story:DoOperate(operate, param_t)
	if S_STEP_OPERATE.CG_START == operate then
		self:OpPlayCg(param_t[1], param_t[2])
	elseif S_STEP_OPERATE.S_STEP_START == operate then
		self:OpStepStartToServer(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.INTERACTIVE_START == operate then
		self:OpInteractiveStart(param_t[1], param_t[2])

	elseif S_STEP_OPERATE.SET_ROLE_POS == operate then
		self:OpSetRolePos(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.RANDOM_SET_ROLE_POS == operate then
		self:OpRandomSetRolePos(tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]))

	elseif S_STEP_OPERATE.CHANGE_SCENE == operate then
		self:OpChangeScene(tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]))

	elseif S_STEP_OPERATE.DIALOG_START == operate then
		self:OpDialog(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.EXIT_FB == operate then
		self:OpExitFb()

	elseif S_STEP_OPERATE.GIRL_SAY == operate then
		self:OpGirlSay(param_t[1], tonumber(param_t[2]))

	elseif S_STEP_OPERATE.CREATE_ROBERT == operate then
		self:OpCreatRobert(param_t)

	elseif S_STEP_OPERATE.DEL_ROBERT == operate then
		self:OpDelRobert(param_t)

	elseif S_STEP_OPERATE.ROBERT_SAY == operate then
		self:OpRobertSay(tonumber(param_t[1]), param_t[2], tonumber(param_t[3]))

	elseif S_STEP_OPERATE.ROBERT_MOVE == operate then
		self:OpRobertMove(tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]))

	elseif S_STEP_OPERATE.ROBERT_ATK_TARGET == operate then
		self:OpRobertAtk(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.FIGHT_START == operate then
		self:OpFightStart(param_t)

	elseif S_STEP_OPERATE.CHANGE_APPEARANCE == operate then
		self:OpChangeAppearance(tonumber(param_t[1]), param_t[2], tonumber(param_t[3]))

	elseif S_STEP_OPERATE.CHANGE_TITLE == operate then
		self:OpChangeTitle(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.CHANGE_MOVE_SPEED == operate then
		self:OpChangeMoveSpeed(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.CHANGE_HUSONG_COLOR == operate then
		self:OpChangeHusongColor(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.CHANGE_MAXHP == operate then
		self:OpChangeMaxHp(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.CHANGE_AOE_RANGE == operate then
		self:OpChangeAoeRange(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.CLOCK_START == operate then
		self:OpClockStart(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.S_DROP == operate then
		self:OpServerDrop(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.S_FB_PASS_SUCC == operate then
		self:OpServerFbPassSucc()

	elseif S_STEP_OPERATE.FORCE_DO == operate then
		local f_do_param_t = {}
		for i = 4, #param_t do
			table.insert(f_do_param_t, param_t[i])
		end
		self:OpForceDo(tonumber(param_t[1]), tonumber(param_t[2]), param_t[3], f_do_param_t)

	elseif S_STEP_OPERATE.STOP_FORCE_DO == operate then
		self:OpForceDo(tonumber(param_t[1]), 0, "", {})

	elseif S_STEP_OPERATE.START_GATHER == operate then
		self:OpStartGather(tonumber(param_t[1]), tonumber(param_t[2]))
		
	elseif S_STEP_OPERATE.CREATE_AIR_RECT_WALL == operate then
		self:OpCreateAirWall(tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]), tonumber(param_t[4]))

	elseif S_STEP_OPERATE.DEL_AIR_RECT_WALL == operate then
		self:OpDelAllWall(tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]), tonumber(param_t[4]))

	elseif S_STEP_OPERATE.SET_SCENE_ELEMENT_ACT == operate then
		self:OpSetSceneElementAct(param_t[1], tonumber(param_t[2]))

	elseif S_STEP_OPERATE.SHOW_COUNTDOWN_TIME == operate then
		self:OpShowCountDownTime(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.SHOW_MESSAGE == operate then
		self:OpShowMessage(param_t[1], tonumber(param_t[2]))

	elseif S_STEP_OPERATE.CREATE_GATHER == operate then
		self:OpCreateGather(param_t[1], param_t[2], param_t[3], param_t[4])

	elseif S_STEP_OPERATE.CREATE_NPC == operate then
		self:OpCreateNpc(tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]), tonumber(param_t[4]))

	elseif S_STEP_OPERATE.DEL_NPC == operate then
		self:OpDelNpc(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.PLAY_AUDIO == operate then
		self:OpPlayAudio(param_t[1], param_t[2])

	elseif S_STEP_OPERATE.PLAY_EFFECT == operate then
		self:OpPlayEffect(param_t[1], param_t[2], tonumber(param_t[3]), tonumber(param_t[4]), tonumber(param_t[5]), tonumber(param_t[6]))

	elseif S_STEP_OPERATE.PLAY_AROUND_EFFECT == operate then
		self:OpPlayAroundEffect(param_t[1], param_t[2], tonumber(param_t[3]), tonumber(param_t[4]), tonumber(param_t[5]), tonumber(param_t[6]))

	elseif S_STEP_OPERATE.CREATE_EFFECT_OBJ == operate then
		self:OpCreateEffectObj(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.DEL_EFFECT_OBJ == operate then
		self:OpDelEffectObj(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.AUTO_TASK == operate then
		self:OpAutoTask()

	elseif S_STEP_OPERATE.BREAK_TASK == operate then
		self:OpBreakTask()

	elseif S_STEP_OPERATE.AUTO_GUAJI == operate then
		self:OpAutoGuaji()

	elseif S_STEP_OPERATE.MOVE_ARROW_TO == operate then
		self:OpMoveArrowTo(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.FUN_GUIDE == operate then
		self:OpFunGuide(param_t[1])

	elseif S_STEP_OPERATE.DEL_MOVE_ARROW == operate then
		self:OpDelMoveArrow()

	elseif S_STEP_OPERATE.DELAY_DO == operate then
		self:OpDelayDo(param_t)

	elseif S_STEP_OPERATE.CODE_IF_THEN == operate then
		self:OpCodeIfThen(param_t)

	elseif S_STEP_OPERATE.DEL_CODE == operate then
		self:OpDelCode()

	elseif S_STEP_OPERATE.ROBERT_ROTATE_TO == operate then
		self:OpRobertRotateTo(tonumber(param_t[1]), tonumber(param_t[2]))

	elseif S_STEP_OPERATE.DO_NEXT_WAVE == operate then
		self:OpInteractiveStart(operate,param_t[1])

	elseif S_STEP_OPERATE.SET_ROLE_CAMERA == operate then
		self:ChangeCamera(tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]))

	elseif S_STEP_OPERATE.ICON_FLY == operate then
		self:IconFly(param_t[1], param_t[2])

	elseif S_STEP_OPERATE.GUIDE_START == operate then
		self:GuideStart(tonumber(param_t[1]))

	elseif S_STEP_OPERATE.FIGHT_PAUSE == operate then
		self:SetRobotePause(true)

	elseif S_STEP_OPERATE.FIGHT_CONTINUE == operate then
		self:SetRobotePause(false)

	elseif S_STEP_OPERATE.ERROR_REMIND == operate then
		self:SetErrorRemind(param_t[1])

	elseif S_STEP_OPERATE.TEAM_ADD == operate then
		self:AddTeammate(param_t)

	elseif S_STEP_OPERATE.TEAM_LEAVE == operate then
		self:LeaveTeam()

	elseif S_STEP_OPERATE.GAME_INTERLUDE == operate then	-- 过场
		self:GameInterlude(param_t[1])
	
	elseif S_STEP_OPERATE.OP_SCENE_OBJ_HIDDEN == operate then
		local hide_group = tonumber(param_t[1])
		if hide_group >= 0 then
			Scene.Instance:TrySetSceneStaticObjActive(hide_group, false)
			if Scene.Instance.scene_static_obj_hide_cache ~= nil then
				Scene.Instance.scene_static_obj_hide_cache[ScenePreload.main_scene_asset_name] = nil
			end
		end

	elseif S_STEP_OPERATE.OP_SCENE_OBJ_SHOW == operate then
		local hide_group = tonumber(param_t[1])
		if hide_group >= 0 then
			Scene.Instance:TrySetSceneStaticObjActive(hide_group, true)
		end

	end
end

-- 播放cg
function Story:OpPlayCg(bundle_name, asset_name)
	local end_callback = function()
		self:SetTrigger(S_STEP_TRIGGER.CG_END)
	end
	local start_callback = function()
		self:SetTrigger(S_STEP_TRIGGER.CG_START)
	end

	--- 只要以后增加CG跳过，则跳过就是完成副本
	local skip_callback = function ()
		CgManager.Instance:OnPlayEnd()
	end

	if IsLowMemSystem then
		GlobalTimerQuest:AddDelayTimer(function ()
			start_callback()
			end_callback()
		end, 0)
	else
		CgManager.Instance:Play(BaseCg.New(bundle_name, asset_name), end_callback, start_callback, nil, skip_callback)
	end
end

-- 请求服务开始阶段，待服务器返回阶段结束
function Story:OpStepStartToServer(step)
	-- local cmd = ProtocolPool.Instance:GetProtocol(CSFunOpenStoryStep)
	-- cmd.step = step
	-- cmd:EncodeAndSend()

	-- local msg_type = ProtocolPool.Instance:Register(SCFunOpenStoryStepEnd)
	-- if msg_type > 0 then
	-- 	GameNet.Instance:RegisterMsgOperate(msg_type, function ()
	-- 		ProtocolPool.Instance:UnRegister(SCFunOpenStoryStepEnd, msg_type)
	-- 		GameNet.Instance:UnRegisterMsgOperate(msg_type)

	-- 		self:SetTrigger(S_STEP_TRIGGER.S_STEP_END)
	-- 	end)
	-- end
end

-- 请求人机交互
function Story:OpInteractiveStart(interacive_type, interacive_param1)

	local function interactive_end(param)
		self:SetTrigger(S_STEP_TRIGGER.INTERACTIVE_END, param)
	end

	if S_INTERACTIVE_TYPE.FETCH_HUSONG_REWARD == interacive_type then
		self.story_view:ShowHusongRewardView(interactive_end)
	end

	if S_INTERACTIVE_TYPE.WING_OPEN_DOOR == interacive_type then
		self.story_view:ShowOpenDoor(interactive_end)
	end

	if S_INTERACTIVE_TYPE.DISTRIBUTE_RED_PACKET == interacive_type then
		self.story_view:ShowRedPacket(interactive_end)
	end

	if S_INTERACTIVE_TYPE.SHOW_VICTORY == interacive_type then
		self.story_view:ShowVictoryView(interactive_end, tonumber(interacive_param1))
	end

	if S_INTERACTIVE_TYPE.SHOW_HELP == interacive_type then
		self.story_view:ShowHelp(interactive_end, tonumber(interacive_param1))
	end

	if S_INTERACTIVE_TYPE.SHOW_ATTACK == interacive_type then
		self.story_view:ShowAttack(interactive_end, tonumber(interacive_param1))
	end

	if S_INTERACTIVE_TYPE.SHOW_ATTACK_BACK == interacive_type then
		self.story_view:ShowAttackBack(interactive_end, tonumber(interacive_param1))
	end
	if S_STEP_OPERATE.DO_NEXT_WAVE == interacive_type then
		self.story_view:ShowLevelTips(interacive_param1)
		--print_error("DO_NEXT_WAVE",interacive_param1)
	end
end

-- 设置相机位置
function Story:ChangeCamera(pos_x, pos_y, dis)
	-- 自由模式下不调整摄像机
	if CAMERA_TYPE == CameraType.Free then
		return
	end

	if IsNil(MainCameraFollow) then
		return
	end

	self.fightstate_camera_param = {}
	if self.step_list_cfg == nil or self.step_list_cfg[1] == nil then
		return
	end

	local step_cfg = self.step_list_cfg[1]
	if step_cfg.trigger_param ~= nil and step_cfg.trigger_param ~= "" then
		local area_param = Split(step_cfg.trigger_param, "##")
		if area_param then
			local scene_id = Scene.Instance:GetSceneId()
			local param_t = {scene_id = scene_id, is_smooth = true, x = tonumber(area_param[1]), y = tonumber(area_param[2]), w = tonumber(area_param[3]), h = tonumber(area_param[4]), is_need_cache_recover_data = tonumber(area_param[5] or 0)}
			Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.AREA, pos_x, pos_y, dis, param_t)
		end
	end
end

-- 图标飞行
function Story:IconFly(view_name, icon_name)
	local view = ViewManager.Instance:GetView(view_name)
	if view and view.node_list[icon_name] and view.mask_bg_node then
		local node = view.node_list[icon_name]
		node:SetActive(true)
		local bg_pos = view.mask_bg_node.transform.position
		local tar_local_pos = node.transform.localPosition
		node.transform.position = bg_pos
		if self.fly_icon_timer  then
			return
		end

		self.fly_icon_timer = GlobalTimerQuest:AddDelayTimer(function ()
			if not IsNil(node.gameObject) then
				node.transform:DOLocalMove(tar_local_pos, 1)
			end
			self.fly_icon_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self.fly_icon_timer = nil
				node.transform.localPosition = tar_local_pos
				self:SetTrigger(S_STEP_TRIGGER.ICON_FLY_END)
			end, 1.2)
		end, 0.5)
	end
end

-- 开始引导
function Story:GuideStart(guide_id)
	FunctionGuide.Instance:TriggerGuideById(guide_id, function ()
		self:SetTrigger(S_STEP_TRIGGER.GUIDE_END)
	end)
end

-- 设置机器人暂停
function Story:SetRobotePause(is_pause)
	RobertManager.Instance:SetPause(is_pause)
end

-- 错误码提示（中间弹出提示）
function Story:SetErrorRemind(dec)
	SysMsgWGCtrl.Instance:ErrorRemind(dec)
end

-- 添加队友
function Story:AddTeammate(param_t)
	local protocol = SCTeamInfo.New()
	protocol.team_index = 0
	protocol.team_leader_index = 0
	protocol.assign_mode = 0
	protocol.must_check = 0
	protocol.member_can_invite = 0
	protocol.limit_capability = 0
	protocol.limit_level = 0
	protocol.max_level = 0
	protocol.member_count = #param_t + 1
	protocol.team_type = 0
	protocol.teamfb_mode = 0
	protocol.team_member_list = {}
	protocol.team_member_list[1] = self:TeamMemberInfoRead(tonumber(0))
	for i=2,protocol.member_count do
		protocol.team_member_list[i] = self:TeamMemberInfoRead(tonumber(param_t[i - 1]))
	end
	SocietyWGCtrl.Instance:OnTeamInfo(protocol)
end

-- 离开队伍
function Story:LeaveTeam()
	local protocol = SCOutOfTeam.New()
	protocol.reason = OUT_OF_TEAM_REASON.OFT_DISMISS
	protocol.user_id = RoleWGData.Instance:InCrossGetOriginUid()
	protocol.user_name = RoleWGData.Instance:GetRoleVo().role_name
	SocietyWGCtrl.Instance:OnOutOfTeam(protocol)
end

-- 设置角色位置
function Story:TeamMemberInfoRead(robert_id)
	local robert_cfg = RobertManager.Instance:GetRobertCfg(robert_id) or {}
	local robert = RobertManager.Instance:GetRobertByRobertId(robert_id)
	local stu = {}
	stu.role_id = robert and robert:GetRoleId() or 0
	stu.name = robert_id == 0 and RoleWGData.Instance:GetRoleVo().role_name or robert_cfg.name
	stu.scene_index = 0
	stu.scene_id = Scene.Instance:GetSceneId()
	stu.is_online = 1
	stu.is_hang = 0
	stu.prof = robert_cfg.prof
	stu.camp = 0
	stu.sex = robert_cfg.sex
	stu.team_tower_defend_join_times = 0

	stu.level = RoleWGData.Instance:GetRoleVo().level
	stu.vip_level = 0
	stu.fbroom_read = 0

	stu.capability = 0
	stu.avatar_key_big = 0
	stu.avatar_key_small = 0
    stu.shizhuang_photoframe = 0
    stu.jingjie_level = 0
    stu.fame = 0
	stu.plat_type = 0
	stu.orgin_role_id = 0
	return stu
end

-- 设置角色位置
function Story:OpSetRolePos(pos_x, pos_y)
	-- 因为低内存手机会跳过CG，部分CG会重置主角位置，体验会很奇怪
	if IsLowMemSystem then
		return
	end
	local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
	Scene.SendSyncJump(Scene.Instance:GetSceneId(), pos_x, pos_y, scene_key)

	Scene.Instance:GetMainRole():SetLogicPos(pos_x, pos_y)
	if not IsNil(MainCameraFollow) then
		MainCameraFollow:SyncImmediate()
	end
end

-- 随机设置角色位置
function Story:OpRandomSetRolePos(pos_x, pos_y, range)
	-- 因为低内存手机会跳过CG，部分CG会重置主角位置，体验会很奇怪
	if IsLowMemSystem then
		return
	end

	pos_x = pos_x + math.random(-range, range)
	pos_y = pos_y + math.random(-range, range)

	if SceneType.Common == Scene.Instance:GetSceneType() then
		local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
		Scene.SendSyncJump(Scene.Instance:GetSceneId(), pos_x, pos_y, scene_key)
	end

	Scene.Instance:GetMainRole():SetLogicPos(pos_x, pos_y)
	if not IsNil(MainCameraFollow) then
		MainCameraFollow:SyncImmediate()
	end
end

-- 改变场景(普通场景)
function Story:OpChangeScene(scene_id, x, y)
	if not x or not y or x == "" or y == "" then
		local scene_logic = Scene.Instance:GetSceneLogic()
		x, y = scene_logic:GetTargetScenePos(scene_id)
	end

	if x and y then
		Scene.Instance:OpenSceneLoading(scene_id)
	    TaskWGCtrl.Instance:SendFlyByShoe(scene_id, x, y, -1, false)
	end
end

-- 对话
function Story:OpDialog(npc_id)
	local dialog_view = ViewManager.Instance:GetView(GuideModuleName.TaskDialog)
	if nil ~= dialog_view then
		dialog_view:SetStoryNpcId(npc_id, function ()
			self:SetTrigger(S_STEP_TRIGGER.DIALOG_END)
		end)

		dialog_view:Open()		
	end
end

-- 退出副本
function Story:OpExitFb()
	--FuBenWGCtrl.Instance:SendExitFBReq()
	FuBenWGCtrl.Instance:SendLeaveFB()
end

-- 美女说话
function Story:OpGirlSay(content, time)
	self.story_view:OnGirlSay(content, time or 5)
end

-- 创建机器人
function Story:OpCreatRobert(robert_id_list)
	for _, v in ipairs(robert_id_list) do
		RobertManager.Instance:CreateRobert(tonumber(v))
	end
end

-- 移除机器人
function Story:OpDelRobert(robert_id_list)
	for _, v in ipairs(robert_id_list) do
		RobertManager.Instance:DelRobertByRobertId(tonumber(v))
	end
end

-- 机器人说话
function Story:OpRobertSay(robert_id, content, time)
	RobertManager.Instance:RobertSay(robert_id, content, time or 3)
end

-- 机器人移动
function Story:OpRobertMove(robert_id, pos_x, pos_y)
	RobertManager.Instance:RobertMoveTo(robert_id, pos_x, pos_y)
end

-- 机器人对另外一个机器人发起攻击
function Story:OpRobertAtk(robert_id, target_robert_id)
	RobertManager.Instance:RobertAtkTarget(robert_id, target_robert_id)
end

-- 战斗开始
function Story:OpFightStart(end_condition)
	local die_robert_list = {}
	for _, v in pairs(end_condition) do
		table.insert(die_robert_list, tonumber(v))
	end
	-- print_error("-- 战斗开始", end_condition)
	RobertManager.Instance:StartFight(die_robert_list, function (fight_id_inc)
		self:SetTrigger(S_STEP_TRIGGER.FIGHT_END, fight_id_inc)
	end)
end

-- 改变机器人外观
function Story:OpChangeAppearance(robert_id, appearance_type, appearance_value)
	RobertManager.Instance:RobertChangeAppearance(robert_id, appearance_type, appearance_value)
end

-- 改变机器人称号
function Story:OpChangeTitle(robert_id, title_id)
	RobertManager.Instance:RobertChangeTitle(robert_id, title_id)
end

-- 机器人改变速度
function Story:OpChangeMoveSpeed(robert_id, move_speed)
	RobertManager.Instance:RobertChangeAttrValue(robert_id, "move_speed", move_speed)
end

-- 机器人更改护送颜色
function Story:OpChangeHusongColor(robert_id, husong_color)
	RobertManager.Instance:RobertChangeAttrValue(robert_id, "husong_taskid", 1)
	RobertManager.Instance:RobertChangeAttrValue(robert_id, "husong_color", husong_color)
end

-- 机器人更改最大血量
function Story:OpChangeMaxHp(robert_id, max_hp)
	RobertManager.Instance:RobertChangeAttrValue(robert_id, "max_hp", max_hp)
	RobertManager.Instance:RobertChangeAttrValue(robert_id, "hp", max_hp)
end

-- 机器人更改攻击力
function Story:OpChangeGongji(robert_id, min_gongji, max_gongji)
	RobertManager.Instance:RobertChangeGongJi(robert_id, min_gongji, max_gongji)
end

-- 机器人更改Aoe范围
function Story:OpChangeAoeRange(robert_id, aoe_range)
	RobertManager.Instance:RobertChangeAoeRange(robert_id, aoe_range)
end

-- 开启时钟
function Story:OpClockStart(time)
	self.clock_end_time = Status.NowTime + time
end

-- 服务器掉落(掉落物品由服务器决定，服务器在掉落后会认为完成了该剧情)
function Story:OpServerDrop(pos_x, pos_y)
	local pos = self.story_view:GetLastRobertPos()
	if pos and pos.x and pos.x then
		FuBenWGCtrl.Instance:SendYuanGuFBReq(FB_HIGH_REQ_TYPE.FB_HIGH_REQ_TYPE_GUID_FINISH,pos.x,pos.x)
	else
		FuBenWGCtrl.Instance:SendYuanGuFBReq(FB_HIGH_REQ_TYPE.FB_HIGH_REQ_TYPE_GUID_FINISH,pos_x,pos_y)
	end

	-- local cmd = ProtocolPool.Instance:GetProtocol(CSFbGuideFinish)
	-- cmd.pos_x = pos_x
	-- cmd.pos_y = pos_y
	-- cmd:EncodeAndSend()
end

-- 通知服务器副本通关成功
function Story:OpServerFbPassSucc()
	local cs_pass_speciak_req = ProtocolPool.Instance:GetProtocol(CSClientPassSpeciakInfo)
	cs_pass_speciak_req.scene_id = Scene.Instance:GetSceneId()
	cs_pass_speciak_req.scene_type = Scene.Instance:GetSceneType()
	cs_pass_speciak_req:EncodeAndSend()
end

-- 强制机器人定期做某件事
function Story:OpForceDo(f_robert_id, f_time_interval, f_do_type, f_do_param_t)
	-- print("============OpForceDo,", f_robert_id, f_time_interval, f_do_type, f_do_param_t[1], f_do_param_t[2])
	self.force_t.f_robert_id = f_robert_id
	self.force_t.f_time_interval = f_time_interval
	self.force_t.f_do_type = f_do_type
	self.force_t.f_do_param_t = f_do_param_t
	self.force_t.f_do_next_time = Status.NowTime
end

-- 机器人操作采集
function Story:OpStartGather(robert_id, gather_id)
	RobertManager.Instance:RobertStartGather(robert_id, gather_id)
end

-- 生成空气墙
function Story:OpCreateAirWall(x, y, w, h)
	for i = x, x + w - 1 do
		for j = y, y + h - 1 do
			self.block_t[i * 100000 + j] = true
			AStarFindWay:SetBlockInfo(i, j)
		end
	end
end

-- 移除空气墙
function Story:OpDelAllWall(x, y, w, h)
	for i = x, x + w - 1 do
		for j = y, y + h - 1 do
			self.block_t[i * 100000 + j] = nil
			AStarFindWay:RevertBlockInfo(i, j)
		end
	end
end

-- 设置场景元素是否激活
function Story:OpSetSceneElementAct(element_path, is_act)
	local t = Split(element_path, "/")
	if #t <= 1 then
		return
	end

	local obj_name = t[#t]
	local parent_path = string.gsub(element_path, "/" .. obj_name, "")
	local parent = GameObject.Find(parent_path)
	if nil ~= parent then
		local obj = parent.transform:Find(obj_name)
		if nil ~= obj then
			obj.gameObject:SetActive(1 == is_act)
		end
	end
end

-- 显示倒计时
function Story:OpShowCountDownTime(time)
	local fuben_icon_view = FuBenWGCtrl.Instance:GetFuBenIconView()
	if nil ~= fuben_icon_view and fuben_icon_view:IsOpen() then
		fuben_icon_view:SetCountDownByTotalTime(time)
	end
end

-- 显示传闻
function Story:OpShowMessage(content, time)
	self.story_view:ShowMessage(content, time)
end

-- 创建采集物
function Story:OpCreateGather(gather_id, gather_time, pos_x, pos_y)
	-- local cmd = ProtocolPool.Instance:GetProtocol(CSFbGuideCreateGather)
	-- cmd.gather_id = gather_id
	-- cmd.gather_time = gather_time
	-- cmd.pos_x = pos_x
	-- cmd.pos_y = pos_y

	-- cmd:EncodeAndSend()
end

-- 创建NPC
function Story:OpCreateNpc(npc_id, x, y, rotation_y)
	local vo = NpcVo.New()
	vo.npc_id = npc_id
	vo.pos_x = x
	vo.pos_y = y
	vo.rotation_y = rotation_y
	Scene.Instance:CreateNpc(vo)
end

-- 移除NPC
function Story:OpDelNpc(npc_id)
	Scene.Instance:DeleteObjByTypeAndKey(SceneObjType.Npc, npc_id)
end

-- 播放音效
function Story:OpPlayAudio(bundle_name, asset_name)
	AudioManager.PlayAndForget(bundle_name, asset_name)
end

-- 播放特效。在机器人偏移位置处播
function Story:OpPlayEffect(bundle_name, asset_name, robert_id, offest_x, offest_y, offest_z)
	local robert = RobertManager.Instance:GetRobertByRobertId(robert_id)
	if nil == robert then
		return
	end

	local position = Vector3(offest_x, offest_y, offest_z) + robert:GetObj():GetRoot().transform.position
	EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, position)
end

-- 播放区域特效,在机器人中心的所在区域
function Story:OpPlayAroundEffect(bundle_name, asset_name, robert_id, width, height, count)
	local robert = RobertManager.Instance:GetRobertByRobertId(robert_id)
	if nil == robert then
		return
	end

	local pos = robert:GetObj():GetRoot().transform.position
	local min_x, max_x = pos.x - width / 2,  pos.x + width / 2
	local min_y, max_y = pos.y, pos.y
	local min_z, max_z = pos.z - height / 2, pos.z + height / 2

	for i = 1, count do
		local position = Vector3(GameMath.Rand(min_x, max_x), GameMath.Rand(min_y, max_y), GameMath.Rand(min_z, max_z))
		local delay_time = 0.3 * math.ceil(i / 4)
		GlobalTimerQuest:AddDelayTimer(function()
			EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, position)
			end, delay_time)
	end
end

-- 创建特效对象
function Story:OpCreateEffectObj(effect_id)
	if nil ~= self.effect_obj_map[effect_id] then
		return
	end

	local effect_cfg = ConfigManager.Instance:GetAutoConfig("story_auto")["effect"][effect_id]
	if nil == effect_cfg then
		return
	end

	local effect_obj = self.effect_obj_map[effect_id] or AllocAsyncLoader(self, "effect_loader")
	effect_obj:SetParent(SceneObjLayer.transform)
	effect_obj:SetLocalPosition(Vector3(effect_cfg.pos_x, effect_cfg.pos_y, effect_cfg.pos_z))
	effect_obj:SetIsUseObjPool(true)
	effect_obj:SetLocalScale(Vector3(effect_cfg.scale_x, effect_cfg.scale_y, effect_cfg.scale_z))
	effect_obj:SetLocalRotation(Quaternion.Euler(effect_cfg.rotate_x, effect_cfg.rotate_y, effect_cfg.rotate_z))
	effect_obj:Load(effect_cfg.bundle_name, effect_cfg.asset_name)

	if effect_cfg.is_wangqi_visable ~= nil and effect_cfg.is_wangqi_visable == 1 then
		effect_obj:SetIsWangQiVisable(true)
	end
	
	self.effect_obj_map[effect_id] = effect_obj
end

function Story:OpDelEffectObj(effect_id)
	local effect_obj = self.effect_obj_map[effect_id]
	if nil ~= effect_obj then
		effect_obj:Destroy()
		effect_obj:DeleteMe()
		self.effect_obj_map[effect_id] = nil
	end
end

-- 自动任务
function Story:OpAutoTask()
	TaskGuide.Instance:CanAutoAllTask(true)
end

-- 停止任务
function Story:OpBreakTask()
	TaskGuide.Instance:CanAutoAllTask(false)
end

-- 自动挂机
function Story:OpAutoGuaji()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

-- 移动箭头指向点
function Story:OpMoveArrowTo(x, y)
	local wx, wy = GameMapHelper.LogicToWorld(x, y)
	Scene.Instance:ActGuideArrowTo(wx, wy)
end

-- 功能引导
function Story:OpFunGuide(guide_name)
	FunctionGuide.Instance:TriggerGuideByGuideName(guide_name)
end

-- 移除移动箭头
function Story:OpDelMoveArrow()
	Scene.Instance:DelGuideArrow()
end

-- 延迟做某件事
function Story:OpDelayDo(param_t)
	local time = tonumber(table.remove(param_t, 1))
	local operate = table.remove(param_t, 1)

	local t = {}
	t.do_time = time + Status.NowTime
	t.operate = operate
	t.param_t = param_t

	table.insert(self.do_delay_list, t)
end

-- 执行条件语句
function Story:OpCodeIfThen(param_t)
	local code_str = ""
	for i,v in ipairs(param_t) do
		if "" == code_str then
			code_str = v
		else
			code_str = code_str .. "##" .. v
		end
	end

	local code_ary = Split(code_str, " then ")

	local code = {}
	code.condition = string.gsub(code_ary[1], "if ", "")
	code.execute_list = Split(code_ary[2], ";")
	table.insert(self.code_if_then_list, code)
end

-- 删除编码
function Story:OpDelCode()
	self.code_if_then_list = {}
end

function Story:OpRobertRotateTo(robert_id, angle)
	RobertManager.Instance:RobertRotateTo(robert_id, angle)
end

-- 游戏过场
function Story:GameInterlude(interlude_id)
	InterludePopDialogWGCtrl.Instance:OpenInterludePopDialogView(interlude_id)
end


