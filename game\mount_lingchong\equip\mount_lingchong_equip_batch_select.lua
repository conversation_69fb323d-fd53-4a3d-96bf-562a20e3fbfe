--骑宠装备升品批量选择界面
MountLingChongEquipBatchSelect = MountLingChongEquipBatchSelect or BaseClass(SafeBaseView)

function MountLingChongEquipBatchSelect:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 488)})
	self:AddViewResource(0, "uis/view/qichong_equip_ui_prefab", "layout_batch_shengpin")
    self.view_name = "MountLingChongEquipBatchSelect"
    self:SetMaskBg(true)
end

function MountLingChongEquipBatchSelect:ReleaseCallBack()
	if nil ~= self.batch_item_grid then
		self.batch_item_grid:DeleteMe()
    end
    self.batch_item_grid = nil
    self.item_id = nil
end

function MountLingChongEquipBatchSelect:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.MountPetEquip.BatchSelect
    
    self.batch_item_grid = BatchGrid.New(self)
    self.batch_item_grid:SetStartZeroIndex(false)
    self.batch_item_grid:SetIsShowTips(false)
    self.batch_item_grid:SetNoSelectState(false)
    self.batch_item_grid:SetIsMultiSelect(true)                       --  ,change_cells_num = 2
    self.batch_item_grid:CreateCells({col = 8 ,change_cells_num = 2, cell_count = 32,
    list_view = self.node_list["ph_item_grid"], itemRender = BatchSelectRender})
    self.batch_item_grid:SetSelectCallBack(BindTool.Bind(self.SelectShenShouBagCellCallBack, self))

    self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSure, self))
    self.node_list["btn_one_key"].button:AddClickListener(BindTool.Bind(self.OnClickOneKey, self))
end

function MountLingChongEquipBatchSelect:SelectShenShouBagCellCallBack(cell)
	self:FlushAddNum()
end

function MountLingChongEquipBatchSelect:SetData(item_id, is_selected)
    self.cur_grid_index = nil
    self.item_id = item_id
    -- self.is_selected = is_selected
    -- if is_selected then
    --     local list = MountLingChongEquipWGData.Instance:GetShengpinSelectList()
    --     local grid_list = {}
    --     for k, v in pairs(list) do
    --         if v > -1 then
    --             grid_list[#grid_list+1] = v
    --         end
    --     end
    -- end 
    --MountLingChongEquipWGData.Instance:SaveShengpinSelectList({})
    if self:IsLoaded() then
        self:Flush()
    end
end

function MountLingChongEquipBatchSelect:FlushDataList()
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(self.item_id)
    local equip_cfg, show_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(self.item_id)
    if IsEmptyTable(target_cfg) then
        return
    end
    self.cur_grid_index = nil
    local compose_str = target_cfg.consume_equip_2
    local str_list = Split(compose_str, "|")
    local item_list = {}
    local bag_list = MountLingChongEquipWGData.Instance:GetBagItemDataList(show_type)

    local selected_list = {}
    local consume_grid_index_list = MountLingChongEquipWGData.Instance:GetShengpinSelectList()
    if not IsEmptyTable(consume_grid_index_list) then
        for k, v in pairs(consume_grid_index_list) do
            if v > -1 then
                selected_list[v] = true
            end
        end
    end

    local need_must = true
    if target_cfg.consume_num_1 == 0 then
        need_must = false
        self.cur_grid_index = -1
    end
    local index = 0
    if not IsEmptyTable(bag_list) then
        for k, v in pairs(bag_list) do
            for k1, v1 in pairs(str_list) do
                if v.item_id == self.item_id and need_must and self.cur_grid_index == nil then --去掉一个必定需要的
                    self.cur_grid_index = v.grid_index
                elseif v.item_id == tonumber(v1) then
                    index = index + 1
                    local data = {}
                    data.item_id = v.item_id
                    data.show_type = show_type
                    data.cur_grid_index = v.grid_index
                    if selected_list[v.grid_index] then
                        self.batch_item_grid.select_tab[1][index] = true --选中之前选择的
                    end
                    item_list[#item_list+1] = data
                end
            end
        end
    end
    local data = {}
    data.is_plus = true
    data.item_id = 1
    data.show_type = show_type
    item_list[#item_list+1] = data
    if #item_list <= 1 then
        self.batch_item_grid:CancleAllSelectCell()
    end
    self.batch_item_grid:SetDataList(item_list)
    self.node_list["ph_item_grid"].scroll_rect.verticalNormalizedPosition = 1
end

function MountLingChongEquipBatchSelect:GetNeedNum()
    return self.need_num
end

function MountLingChongEquipBatchSelect:OnClickSure()
    if not self.item_id then
        return
    end
    local data_list = self.batch_item_grid:GetAllSelectCell()
    local index = 1
    local consume_grid_index_list = {}
    for k, v in pairs(data_list) do
        consume_grid_index_list[index] = v.cur_grid_index
        index = index + 1
    end
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(self.item_id)
    if target_cfg.consume_equip_1 > 0 then
        consume_grid_index_list[#consume_grid_index_list+1] = self.cur_grid_index or -1 --把必定需要的放进去
    end
    MountLingChongEquipWGData.Instance:SaveShengpinSelectList(consume_grid_index_list)
    SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.SelectComplete)
    self:Close()
end

function MountLingChongEquipBatchSelect:OnClickOneKey()
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(self.item_id)
    self.need_num = target_cfg.consume_num_2
    local item_list = self.batch_item_grid:GetDataList()
    local cur_count = #item_list - 1 --去掉显示加号的数据
    if cur_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.StuffNotEnough1)
        return
    end
    if self.need_num > cur_count then
        --数量不够
        self.batch_item_grid:SetMultiSelectEffect(cur_count)
    else
        self.batch_item_grid:SetMultiSelectEffect(self.need_num)
    end
    self:FlushAddNum()
end

function MountLingChongEquipBatchSelect:FlushAddNum()
    local equip_cfg, show_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(self.item_id)
    if not equip_cfg then
        return
    end

    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(self.item_id)
    self.need_num = target_cfg.consume_num_2
    local type_str = Language.MountPetEquip.NameStrTable[show_type]
    local color_str = Language.MountPetEquip.ColorStr[equip_cfg.quality]
    self.node_list.tip_text.text.text = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[equip_cfg.quality], 
    self.need_num, color_str..type_str)

    local selected_cells = self.batch_item_grid:GetAllSelectCell()
    self.cur_select_num = #selected_cells
    local color = self.cur_select_num ==  self.need_num and COLOR3B.DEFAULT_NUM or COLOR3B.RED
    self.node_list.select_num_text.text.text = string.format(Language.MountPetEquip.HadSelect, color, self.cur_select_num, self.need_num)
end

function MountLingChongEquipBatchSelect:CloseCallBack()
    if self.batch_item_grid then
        self.batch_item_grid:CancleAllSelectCell()
    end
    local equip_cfg, show_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(self.item_id)
    local view_name = GuideModuleName.LingChongEquipView
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
        view_name = GuideModuleName.MountEquipView
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        view_name = GuideModuleName.HuaKunEquipView
    end
    ViewManager.Instance:FlushView(view_name, nil, "flush_select")
end


function MountLingChongEquipBatchSelect:OnFlush()
    if not self.item_id then
        return
    end
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(self.item_id)
    if IsEmptyTable(target_cfg) then
        return
    end
    self:FlushDataList()
    self:FlushAddNum()
end

BatchSelectRender = BatchSelectRender or BaseClass(ItemCell)
function BatchSelectRender:__init()
	self:UseNewSelectEffect(true)
end

function BatchSelectRender:OnClick()
    self:UseNewSelectEffect(true)
    if self.data then
        if self.data.is_plus then
            local pos = self:GetPos()
            MountLingchongEquipWGCtrl.Instance:OpenGetWayView(self.data.show_type, pos)
        else
            ItemCell.OnClick(self)
        end
    end
end

function BatchSelectRender:GetPos()
    if nil ==  self.view then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect = main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x + 220, y)
end


function BatchSelectRender:OnFlush()
    if IsEmptyTable(self.data) then
        self:Reset()
		return
    end
    self:SetIgnoreDataToSelect(self.data.is_plus)
    ItemCell.OnFlush(self)
    if self.data.is_plus then
        self:Reset()
        local bundle, asset = ResPath.GetCommonImages("a2_ty_jia")
        self:SetItemIcon(bundle, asset)
        self:SetButtonComp(true)
        self:SetEffectRootEnable(false)
        ItemCell.SetSelectEffect(self, false)
    end
end

function BatchSelectRender:SetSelect(is_select, item_call_back)
	if is_select and IsEmptyTable(self.data) then
		return 
    end
    self:UseNewSelectEffect(true)
	ItemCell.SetSelectEffect(self, is_select)	
end



BatchGrid = BatchGrid or BaseClass(ShenShouGrid)

function BatchGrid:__init(parent)
    self.parent_view = parent
    AsyncBaseGrid.__init(self)
end


function BatchGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
    self.cell_list = {}
    self.parent_view = nil
end

-- 选择某个格子回调
function BatchGrid:SelectCellHandler(cell)
    local select_num = #self:GetAllSelectCell()
	self.cur_index = cell:GetIndex()
    local cell_index = self.cur_index
    if self.is_multi_select then
        if IsEmptyTable(cell:GetData()) or cell:GetData().item_id == 0 then
            return
        end
        if not self.select_tab[1][cell_index] then
            if select_num == self.parent_view:GetNeedNum() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.HadSelectEnough)
                return
            end
			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
			end
			self.select_tab[1][cell_index] = true
		elseif self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = nil
		else
			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
            end
            if select_num == self.parent_view:GetNeedNum() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.HadSelectEnough)
                return
            end
			self.select_tab[1][cell_index] = true
		end
    else
		for k, v in pairs(self.select_tab[1]) do
			if cell_index == k then
				return
			end
		end

		self.select_tab[1] = {}
		self.select_tab[1][cell_index] = true
	end

	if nil ~= self.select_callback then
		self.select_callback(cell)
	end

	-- self.list_view.scroller:RefreshActiveCellViews()
	self:RefreshSelectCellState()
end

function BatchGrid:SetMultiSelectEffect(num)
	self.select_tab[1] = {}
	for i=1,num do
		self.select_tab[1][i] = true
	end
	self:RefreshSelectCellState()
	-- self.list_view.scroller:RefreshActiveCellViews()
end