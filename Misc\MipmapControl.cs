﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class MipmapControl : MonoBehaviour
{
	[SerializeField]
	private float mipMapBias = -1.0f;
	
	[SerializeField]
	private Texture[] textures;

	public MipmapControl()
	{

	}

	private void OnEnable()
	{
		foreach (var texture in this.textures)
		{
			texture.mipMapBias = this.mipMapBias;
		}
	}

	private void OnDisable()
	{
		foreach (var texture in this.textures)
		{
			texture.mipMapBias = 0.0f;
		}
	}

#if UNITY_EDITOR
	private void OnValidate()
	{
		if (this.isActiveAndEnabled)
		{
			this.OnEnable();
		}
	}
#endif
}
