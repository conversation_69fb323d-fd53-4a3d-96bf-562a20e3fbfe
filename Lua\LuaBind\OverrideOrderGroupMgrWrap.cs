﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class OverrideOrderGroupMgrWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(OverrideOrderGroupMgr), typeof(Nirvana.Singleton<OverrideOrderGroupMgr>));
		<PERSON><PERSON>unction("OnGameStartup", OnGameStartup);
		<PERSON><PERSON>unction("OnGameStop", OnGameStop);
		<PERSON><PERSON>unction("SetGroupCanvasOrderInterval", SetGroupCanvasOrderInterval);
		<PERSON><PERSON>RegFunction("AddToGroup", AddToGroup);
		<PERSON><PERSON>Function("RemoveFromGroup", RemoveFromGroup);
		<PERSON><PERSON>unction("SetGroupCanvasDirty", SetGroupCanvasDirty);
		<PERSON><PERSON>RegFunction("Update", Update);
		<PERSON>.RegFunction("New", _CreateOverrideOrderGroupMgr);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("GroupCanvasOrderInterval", get_GroupCanvasOrderInterval, null);
		<PERSON><PERSON>Class();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateOverrideOrderGroupMgr(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				OverrideOrderGroupMgr obj = new OverrideOrderGroupMgr();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: OverrideOrderGroupMgr.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStartup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)ToLua.CheckObject(L, 1, typeof(OverrideOrderGroupMgr));
			obj.OnGameStartup();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)ToLua.CheckObject(L, 1, typeof(OverrideOrderGroupMgr));
			obj.OnGameStop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGroupCanvasOrderInterval(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)ToLua.CheckObject(L, 1, typeof(OverrideOrderGroupMgr));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetGroupCanvasOrderInterval(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddToGroup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)ToLua.CheckObject(L, 1, typeof(OverrideOrderGroupMgr));
			IOverrideOrder arg0 = (IOverrideOrder)ToLua.CheckObject<IOverrideOrder>(L, 2);
			UnityEngine.Canvas o = obj.AddToGroup(arg0);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveFromGroup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)ToLua.CheckObject(L, 1, typeof(OverrideOrderGroupMgr));
			UnityEngine.Canvas arg0 = (UnityEngine.Canvas)ToLua.CheckObject(L, 2, typeof(UnityEngine.Canvas));
			IOverrideOrder arg1 = (IOverrideOrder)ToLua.CheckObject<IOverrideOrder>(L, 3);
			obj.RemoveFromGroup(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGroupCanvasDirty(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)ToLua.CheckObject(L, 1, typeof(OverrideOrderGroupMgr));
			UnityEngine.Canvas arg0 = (UnityEngine.Canvas)ToLua.CheckObject(L, 2, typeof(UnityEngine.Canvas));
			obj.SetGroupCanvasDirty(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)ToLua.CheckObject(L, 1, typeof(OverrideOrderGroupMgr));
			obj.Update();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_GroupCanvasOrderInterval(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			OverrideOrderGroupMgr obj = (OverrideOrderGroupMgr)o;
			int ret = obj.GroupCanvasOrderInterval;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index GroupCanvasOrderInterval on a nil value");
		}
	}
}

