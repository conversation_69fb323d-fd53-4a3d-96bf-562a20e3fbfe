MasterWGData = MasterWGData or BaseClass()

function MasterWGData:__init()
	if MasterWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[MasterWGData] Attemp to create a singleton twice !")
	end
	MasterWGData.Instance = self
	self.shitu_info = {
		shifu_id = 0,
		shifu_camp = 0,
		shifu_role_name = "",
		shitu_exp = 0,
		shitu_level = 0,
		total_degree = 0,
		degree_fetch_flag = {},
		tudi_list ={}
	}
	self.shitu_datalist = {}

	self.shitu_pata_info = {}
	self.shitu_equip_datalist = {}
	self.shitu_wash_part_list = {}

	self.kill_boss_seq = 0
end

function MasterWGData:__delete()
	MasterWGData.Instance = nil
end

-- 检查是否能够拜师
function MasterWGData:CheckCanBaishi(user_info, ignore_lv)
	if nil == user_info then
		return false, Language.Master.MasterNoMasterTips
	end
	local shifu_name = user_info.gamename or ""

	local qy_cfg_other = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").other[1] or {}
	local lv_lim = qy_cfg_other.role_level_limit or 0
	local role_lv = RoleWGData.Instance.role_vo.level or 0
	local master_lv = ignore_lv and lv_lim or user_info.level or 0
	if role_lv < lv_lim or master_lv < lv_lim then
		local lan = role_lv < lv_lim and Language.Master.MasterMyLevelLimit or Language.Master.MasterLevelLimit
		local dec = string.format(lan, RoleWGData.GetLevelString(lv_lim))		-- 等级因素
		return false, dec
	end

	local role_shifu = self.shitu_info.shifu_id or 0
	if role_shifu ~= 0 then
		return false, Language.Master.MasterOnlyLimit 							 -- 有师傅了因素
	end

	for k,v in pairs(self.shitu_info.tudi_list) do
		local role_tudi = v.shifu_id or 0
		if role_tudi == user_info.user_id then
			return false, Language.Master.MasterOnlyLimit2 							 -- 徒弟因素
		end
	end

	local role_id =  RoleWGData.Instance.role_vo.role_id or 0
	if role_id == user_info.user_id then
		return false, Language.Master.MasterSelfLimit 							 -- 拜自己为师因素
	end

	return true, shifu_name
end

-- 通过戒指id获取拜师类型
function MasterWGData:GetBaishiTypeById(item_id,num)
	if item_id then
		for k,v in pairs(ConfigManager.Instance:GetAutoConfig("shituconfig_auto").baishi_gift) do
			if v and v.reward_item
				and v.reward_item.item_id == item_id and  v.reward_item.num  == num then
				return v.type
			end
		end
	end
	return 0
end

-- 根据拜师类型获取拜师配置
function MasterWGData:GetOneBaishiCfgByType(type)
	if nil == type then return end
	for _,v in pairs(ConfigManager.Instance:GetAutoConfig("shituconfig_auto").baishi_gift) do
		if v and v.type == type then
			return v
		end
	end
	return nil
end

function MasterWGData:GetShituLevelExp(level)
	for _,v in pairs(ConfigManager.Instance:GetAutoConfig("shituconfig_auto").shitu_uplevel) do
		if v and v.shitu_level == level then
			return v.max_exp
		end
	end
	return 1
end

function MasterWGData:GetMaxShituLevel()
	return #ConfigManager.Instance:GetAutoConfig("shituconfig_auto").shitu_uplevel - 1
end

-- 师徒信息
function MasterWGData:SetShituInfo(info)
-- print_error(info)
	self.shitu_info.shifu_id = info.shifu_id
	self.shitu_info.shifu_camp = info.shifu_camp
	self.shitu_info.shifu_role_name = info.shifu_role_name
	self.shitu_info.shixiongdi_id = info.shixiongdi_id
	self.shitu_info.shixiongdi_camp = info.shixiongdi_camp
	self.shitu_info.shixiongdi_name = info.shixiongdi_name
	self.shitu_info.shifu_is_online = info.shifu_is_online
	self.shitu_info.shixiongdi_is_online = info.shixiongdi_is_online
	self.shitu_info.shitu_exp = info.shitu_exp
	self.shitu_info.shitu_level = info.shitu_level
	self.shitu_info.degree_fetch_flag = info.degree_fetch_flag
	self.shitu_info.tudi_list = info.tudi_list

	self.shitu_info.total_degree = 0
	self.shitu_info.gf_chibang_flag = false

	self.shitu_datalist = {}
	self.shitu_datalist[0] = {}
	local data = self.shitu_datalist[0]
	data.is_master = true
	data.role_id = info.shifu_id
	data.camp = info.shifu_camp
	data.role_name = info.shifu_role_name
	for i = 1, 2 do
		data = {}
		local tudi_info = info.tudi_list[i] or {}
		data.is_master = false
		data.role_id = tudi_info.role_id or 0
		data.camp = tudi_info.camp or 0
		data.cap_num = tudi_info.cap_num or 0
		data.avater_timestamp = tudi_info.avater_timestamp or 0
		data.prof = tudi_info.prof
		data.sex = tudi_info.sex
		data.level = tudi_info.level or 0
		data.tudi_is_online = tudi_info.tudi_is_online or 0
		data.is_show_chibang = false
		data.role_name = tudi_info.role_name or ""
		table.insert(self.shitu_datalist, data)
		self.shitu_info.total_degree = self.shitu_info.total_degree + (tudi_info.degree or 0)
	end
	--print_error("++++++++++",data)
end

function MasterWGData:GetShituInfo()
	return self.shitu_info
end

function MasterWGData:GetShituLevel()
	return self.shitu_info.shitu_level
end

function MasterWGData:IsMaster(user_id)
	return self.shitu_info.shifu_id == user_id
end

function MasterWGData:IsApprentice(user_id)
	for k,v in pairs(self.shitu_info.tudi_list) do
		if v.role_id == user_id then
			return true
		end
	end
	return false
end

function MasterWGData:GetShituDataList()
	return self.shitu_datalist
end

function MasterWGData:GetShituFriendList()
	local data = SocietyWGData.Instance:GetFriendList()
	local list = {}
	for i,v in ipairs(data) do
		if self:IsMaster(v.user_id) or self:IsApprentice(v.user_id) then
			table.insert(list, v)
		end
	end
	return list
end

function MasterWGData:HasShituInTeam()
	local team_cfg = SocietyWGData.Instance:GetTeamMemberList() or {}
	for k,v in pairs(team_cfg) do
		if self:IsMaster(v.role_id) or self:IsApprentice(v.role_id) then
			return true
		end
	end
	return false
end

-- 师徒爬塔信息
function MasterWGData:SetShituPataInfo(info)
	self.shitu_pata_info = info
end

function MasterWGData:GetShituPataInfo()
	return self.shitu_pata_info
end

function MasterWGData:SetShituPataSceneInfo(info)
	self.shitu_pata_scene_info = info
end

function MasterWGData:GetShituPataSceneInfo(info)
	return self.shitu_pata_scene_info
end

function MasterWGData:GetShituPataPassFetchLevel()
	return self.shitu_pata_info.fetch_level or 0
end
function MasterWGData:GetShituPataPassLevel()
	if nil == self.shitu_pata_info.level or self.shitu_pata_info.level < 0
		or self.shitu_pata_info.level > #ConfigManager.Instance:GetAutoConfig("shitupataconfig_auto").level_cfg then
		return -1
	end
	return self.shitu_pata_info.level
end

function MasterWGData:GetShituPateRewards(level)
	local pata_reward_cfg = ConfigManager.Instance:GetAutoConfig("shitupataconfig_auto").day_reward
	for k,v in pairs(pata_reward_cfg) do
		if v.level == level then
			return v
		end
	end
	return nil
end

function MasterWGData:GetCurTitleName(level)
	local cur_level = level or self.shitu_pata_info.level or 0
	if nil == ConfigManager.Instance:GetAutoConfig("shitupataconfig_auto").level_cfg[cur_level] then return "" end
	return ConfigManager.Instance:GetAutoConfig("shitupataconfig_auto").level_cfg[cur_level].title_name
end

-- 根据等级获取配置信息
function MasterWGData:GetShituPataCfgByLevel(level)
	return ConfigManager.Instance:GetAutoConfig("shitupataconfig_auto").level_cfg[level]
end
-- 获取最高等级
function MasterWGData:GetMaxLevel()
	local level = 0
	local cfg = ConfigManager.Instance:GetAutoConfig("shitupataconfig_auto").level_cfg
	for k,v in pairs(cfg) do
		if level <= v.level then
			level = v.level
		end
	end
	return level

end

-- 师徒装备信息
function MasterWGData:SetShituEquipAllInfo(info)
	self.shitu_equip_datalist = info.equip_list
end

-- 师徒装备改变信息
function MasterWGData:SethituEquipChangeInfo(info)
	if info.item_data.item_id > 0 then
		self.shitu_equip_datalist[info.equip_index] = info.item_data
	else
		self.shitu_equip_datalist[info.equip_index] = nil
	end
end

function MasterWGData:GetShituEquipAllInfo()
	return self.shitu_equip_datalist
end

function MasterWGData:GetShituEquipInfo(index)
	return self.shitu_equip_datalist[index]
end

--背包数据
function MasterWGData:InitShituBagItemDataList()
	self.bag_item_data_list = {}
	local bag_item_index_count = 0
	local role_bag_data_list = ItemWGData.Instance:GetBagItemDataList()
	for k, v in pairs(role_bag_data_list) do
		local item_config = ItemWGData.Instance:GetItemConfig(v.item_id)
		if nil ~= item_config and MasterWGData.IsShituEquip(item_config.sub_type) then
			self.bag_item_data_list[bag_item_index_count] = v
			bag_item_index_count = bag_item_index_count + 1
		end
	end
end

--背包数据
function MasterWGData:GetShituBagItemDataList()
	if nil == self.bag_item_data_list then
		self:InitShituBagItemDataList()
	end
	return self.bag_item_data_list
end

function MasterWGData.IsShituEquip(sub_type)
	if sub_type == GameEnum.E_TYPE_SHITU_TOUKUI or
		sub_type == GameEnum.E_TYPE_SHITU_YIFU or
		sub_type == GameEnum.E_TYPE_SHITU_HUTUI or
		sub_type == GameEnum.E_TYPE_SHITU_XIEZI or
		sub_type == GameEnum.E_TYPE_SHITU_HUSHOU or
		sub_type == GameEnum.E_TYPE_SHITU_XIANGLIAN or
		sub_type == GameEnum.E_TYPE_SHITU_WUQI or
		sub_type == GameEnum.E_TYPE_SHITU_JIEZHI then
		return true
	end
	return false
end

-- 师徒洗练信息
function MasterWGData:SetShituWashInfo(info)
	self.shitu_wash_part_list = info.part_list
	self:SetPartScore()
end

function MasterWGData:SetPartScore()
	for k,v in pairs(self.shitu_wash_part_list) do
		if v.attr_item_list then
			local attribute_1 = AttributePool.AllocAttribute()
			local attribute_2 = AttributePool.AllocAttribute()
			for k,v in pairs(v.attr_item_list) do
				local attr_name = EquipmentWGData:GetAttrStrByAttrId(v.attr_type)
				if attribute_1[attr_name] then
					attribute_1[attr_name] = attribute_1[attr_name] + v.attr_value
				end

				attr_name = EquipmentWGData:GetAttrStrByAttrId(v.wash_attr_type)
				if attribute_2[attr_name] then
					attribute_2[attr_name] = attribute_2[attr_name] + v.wash_attr_value
				end
			end

			v.capability = AttributeMgr.GetCapability(attribute_1)
			v.capability_wash = AttributeMgr.GetCapability(attribute_2)
		end
	end
end

function MasterWGData:GetShituWashInfo()
	return self.shitu_wash_part_list
end

-- 获取部位洗炼评分
function MasterWGData:GetIndexXilianCap(index)
	local score_t = {0, 0}
	local part_t = self.shitu_wash_part_list[index]
	if part_t and part_t.capability then
		score_t[1] = part_t.capability
	end
	if part_t and part_t.capability_wash then
		score_t[2] = part_t.capability_wash
	end
	return score_t[1], score_t[2]
end

function MasterWGData:HasWashRare(index)
	if self.shitu_wash_part_list[index] then
		for k,v in pairs(self.shitu_wash_part_list[index].attr_item_list) do
			local attr_type = EquipmentWGData.Instance:GetAttrStrByAttrId(v.wash_attr_type)
			local star = self:GetWashStar(attr_type, v.wash_attr_value)
			if star > 9 and (v.attr_type ~= v.wash_attr_type or v.wash_attr_value > v.attr_value) then
				return true
			end
		end
		return false
	end
	return false
end

function MasterWGData:IsWashNeedSave(index)
	if self.shitu_wash_part_list[index] then
		for k,v in pairs(self.shitu_wash_part_list[index].attr_item_list) do
			if v.wash_attr_type > 0 then
				 return true
			end
		end
		return false
	end
	return false
end

function MasterWGData:GetWashStar(attr_name, value)
	local cfg = ConfigManager.Instance:GetAutoConfig("shituwashconfig_auto").star
	for k,v in pairs(cfg) do
		for i = 1, 5 do
			if v["attr_name" .. i] == attr_name and v["attr_min" .. i] <= value and v["attr_max" .. i] >= value then
				return v.star_level
			end
		end
	end
	return 0
end

function MasterWGData:GetAttrListByIndex(cellindex)
	for k,v in pairs(self.shitu_wash_part_list) do
		if cellindex == k then
			return v.attr_item_list
		end
	end
	return nil
end

function MasterWGData.GetActiveMax()
	local degree_cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").degree
	local max_cfg = degree_cfg[#degree_cfg]
	return max_cfg and max_cfg.degree_limit or 1
end
function MasterWGData:GetDisplayItem(index)
	local degree_cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").degree
	for k,v in pairs(degree_cfg) do
		if v.degree_limit == index then
			return v
		end
	end
end

-- 某类型是否领取
function MasterWGData:GetRewardFlagByType(type)
	return self.shitu_info.degree_fetch_flag[33 - type]
end

-- 设置当前查询boss数量
function MasterWGData:SetRecordCurLayerBossInfo(protocol)
	local count = 0
	for k,v in pairs(protocol.boss_list) do
		if 0 == v.next_refresh_time then
			count = count + 1
		end
	end
	self.cur_boss_num = count
end

-- 获取当前记录的boss数量
function MasterWGData:GetRecordCurLayerBossNum()
	return self.cur_boss_num or 0
end

-- 根据当前层师徒boss配置
function MasterWGData:GetShituBossCfgByLayer(cur_layer)
	local nor_boss_cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").shitu_boss
	for k,v in pairs(nor_boss_cfg) do
		if v.cave_layer == cur_layer then
			return v
		end
	end
end
-- 获取师徒boss配置
function MasterWGData:GetShituBossCfg()
	local nor_boss_cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").shitu_boss
	local boss_list = {}
	for i,v in ipairs(nor_boss_cfg) do
		boss_list[v.cave_layer - 1] = v
	end
	return boss_list
end

-- 五行神将击杀信息
function MasterWGData:SetWuXingShenJiangInfo(protocol)
	self.kill_boss_seq = protocol.kill_boss_seq
end

-- 获取五行神将击杀信息
function MasterWGData:GetWuXingShenJiangInfo()
	return self.kill_boss_seq
end

-- 获取五行神将击杀信息
function MasterWGData:GetWuXingShenJiangCfg()
	local boss_cfg = __TableCopy(ConfigManager.Instance:GetAutoConfig("wuxingshenjiangconfig_auto").wxsj_boss)
	boss_cfg[0] = table.remove(boss_cfg, 1)
	return boss_cfg
end

function MasterWGData:RemindReward()
	local num = 0
	local has_exp = RoleWGData.Instance.role_vo.shitu_exp
	local shitu_exp = self:GetShituInfo().shitu_exp
	local shitu_level = self:GetShituInfo().shitu_level
	local item_data_list = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").shitu_uplevel
	if shitu_level >= #item_data_list - 1 then
		return 0
	end
	for k,v in pairs(item_data_list) do
		if has_exp + shitu_exp >= v.max_exp and shitu_level == v.shitu_level then
			num = num + 1
		end
	end
	return num
end

function MasterWGData:RemindGongFeng()
	local num = 0
	local tudi_list = self:GetShituInfo().tudi_list
	local item_data_list = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").degree
	for k1,v1 in pairs(item_data_list) do
		for k2,v2 in pairs(tudi_list) do
			if v2.degree >= v1.degree_limit and self:GetRewardFlagByType(k1) == 0 then
				num = num + 1
			end
		end
	end
	return num
end

--师徒等级属性
function MasterWGData:GetLevelCfgByLevel(level)
	local master_work_auto = ConfigManager.Instance:GetAutoConfig("shituconfig_auto") or {}

	local cfg = master_work_auto.shitu_uplevel or {}
	local level_cfg = {}
	for k, v in ipairs(cfg) do
		if v.shitu_level == level then
			level_cfg = v
			break
		end
	end
	return level_cfg
end

function MasterWGData:GetMasterRemindList()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").other[1]
	return other_cfg.item_id
end

function MasterWGData:GetShouTuLevel()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("shituconfig_auto").other[1]
	return other_cfg.shoutu_level_limit
end
