LuckyGiftBagLocalSuperDoubleView = LuckyGiftBagLocalSuperDoubleView or BaseClass(SafeBaseView)
function LuckyGiftBagLocalSuperDoubleView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "lucky_gift_bag_superdouble_view")
end

function LuckyGiftBagLocalSuperDoubleView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_bug, BindTool.Bind(self.OnSuperDoubleBtnClick, self))
end

function LuckyGiftBagLocalSuperDoubleView:OnFlush()
	local grade, current_data, end_grade_data, current_lucky_value_per = LuckyGiftBagLocalWgData.Instance:GetCurrentLuckyDoubleGradeAndCfg()
	local no_grade_buy = IsEmptyTable(current_data)
	-- XUI.SetButtonEnabled(self.node_list.btn_bug, not no_grade_buy)
	self.node_list.btn_bug:SetActive(not no_grade_buy)
	local show_data = no_grade_buy and end_grade_data or current_data
	self.node_list.lucky_value_add.text.text = string.format(Language.LuckyGiftBag.LuckyValueAdd1, current_lucky_value_per)

	if no_grade_buy then 
		self.node_list.bug_desc.text.text = Language.LuckyGiftBag.LuckyMaxPer
	else
		local coust_num = show_data.type == 1 and show_data.need_cost or RoleWGData.GetPayMoneyStr(show_data.need_cost, show_data.rmb_type, show_data.rmb_seq)
		local desc_type = show_data.type == 1 and Language.LuckyGiftBag.GoldBugDesc or Language.LuckyGiftBag.RMBBugDesc
		self.node_list.bug_desc.text.text = string.format(desc_type, coust_num, show_data.lucky_per)
		local is_rmb = show_data.type == 2
		self.node_list.btn_text:SetActive(is_rmb)
		self.node_list.btn_gold_text:SetActive(not is_rmb)
		self.node_list.btn_text.text.text = coust_num
		self.node_list.btn_gold_text.text.text = coust_num
	end
end

function LuckyGiftBagLocalSuperDoubleView:OnSuperDoubleBtnClick()
	local grade, current_data = LuckyGiftBagLocalWgData.Instance:GetCurrentLuckyDoubleGradeAndCfg()
	if IsEmptyTable(current_data) then
		return
	end

	if current_data.type == 1 then
		LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_LUCKY_DOUBLE_GRADE)
	else
		RechargeWGCtrl.Instance:Recharge(current_data.need_cost, current_data.rmb_type, current_data.rmb_seq)
	end
end