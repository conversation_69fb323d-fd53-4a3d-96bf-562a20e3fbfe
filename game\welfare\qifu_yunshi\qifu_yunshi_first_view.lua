QifuYunShiFirstView = QifuYunShiFirstView or BaseClass(SafeBaseView)

function QifuYunShiFirstView:__init()
    self:SetMaskBg(false,true)
    self:AddViewResource(0, "uis/view/welfare_ui_prefab", "layout_yunshi_first_view")
end

function QifuYunShiFirstView:__delete()
end

function QifuYunShiFirstView:ReleaseCallBack()
	if self.yunshi_list_view then
		self.yunshi_list_view:DeleteMe()
		self.yunshi_list_view = nil
	end

	if CountDownManager.Instance:HasCountDown("yunshi_auto_close_timer") then
		CountDownManager.Instance:RemoveCountDown("yunshi_auto_close_timer")
	end

	if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end

	self.ske_graphic_chouqian = nil

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	if self.beat_yunshi_alert then
		self.beat_yunshi_alert:DeleteMe()
		self.beat_yunshi_alert = nil
	end

	if self.yunshi_alert then
		self.yunshi_alert:DeleteMe()
		self.yunshi_alert = nil
	end

end

function QifuYunShiFirstView:CloseCallBack()
	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	if self.chouqian_sequence then
		self.chouqian_sequence:Kill()
		self.chouqian_sequence = nil
	end

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.paly_anim_end = false
end

function QifuYunShiFirstView:LoadCallBack()
	self.yunshi_list_view = AsyncListView.New(YunShiListItem,self.node_list["yunshi_list_view"])
	self.paly_anim_end = true

	XUI.AddClickEventListener(self.node_list["chouqian_btn"], BindTool.Bind(self.ClickChouQianBtn,self))
	XUI.AddClickEventListener(self.node_list["chouqian_bg_btn"], BindTool.Bind(self.ClickChouQianBtn,self))
	XUI.AddClickEventListener(self.node_list["change_yunshi_btn"], BindTool.Bind(self.ClickChangeYunShiBtn,self))
	XUI.AddClickEventListener(self.node_list["ignore_anim_toggle"], BindTool.Bind(self.ClickIgnoreAnimToggle,self))
	XUI.AddClickEventListener(self.node_list["item_toggle"], BindTool.Bind(self.ClickItemToggle,self))
	XUI.AddClickEventListener(self.node_list["item_icon_btn"], BindTool.Bind(self.ClickItemIcon,self))
	XUI.AddClickEventListener(self.node_list["check_yunshi_btn"], BindTool.Bind(self.ClickCheckYunShiBtn,self))

	-- XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind(self.ClickCloseBtn,self))
	self.node_list["close_block"]:SetActive(false)
	self.node_list["yunshi_content"]:SetActive(false)
	self.node_list["chouqian_bg_btn"]:SetActive(true)

    self.arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAniCallBack, self))

    if not self.ske_graphic_chouqian then
        self.ske_graphic_chouqian = self.node_list["chouqian_img_anim"].gameObject:GetComponent("SkeletonGraphic")
    end

    if self.ske_graphic_chouqian and self.ske_graphic_chouqian.AnimationState then
    	self.ske_graphic_chouqian.AnimationState:SetAnimation(0, "stand", false)
    end

    local is_ignoire_anim = self.node_list["ignore_anim_toggle"].toggle.isOn
	QifuYunShiWGData.Instance:SetIgnoireAnimFlag(is_ignoire_anim)

	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local refresh_times = fortune_info.refresh_times > 0 and fortune_info.refresh_times or 1
	refresh_times = refresh_times + 1
	local max_timer = QifuYunShiWGData.Instance:GetMaxRefreshTimer()
	refresh_times = refresh_times >= max_timer and max_timer or refresh_times
	local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(refresh_times)
	if not IsEmptyTable(refresh_cfg) then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(refresh_cfg.cost_item_id)
		self.node_list["item_toggle"].toggle.isOn = has_num >= refresh_cfg.cost_item_num
	end
	self.item_toggle_ison = self.node_list["item_toggle"].toggle.isOn

end

function QifuYunShiFirstView:ShowIndexCallBack()
	self.node_list["yunshi_root"].transform.anchoredPosition = Vector2(0,0)
	self.node_list["yunshi_root"].transform.localScale = Vector3(1,1,1)
	self.node_list["chouqian_img_anim"].transform.localScale = Vector3(1,1,1)
	self.node_list["close_block"]:SetActive(false)
	self.node_list["yunshi_content"]:SetActive(false)
	self.node_list["chouqian_bg_btn"]:SetActive(true)
	self.node_list["chouqian_img_anim"]:SetActive(true)
	self.node_list["chouqian_btn"]:SetActive(true)
end

local YunShiColor = {
	[1] = "#0098d1",
	[2] = "#8000bc",
	[3] = "#bc5400",
	[4] = "#f10c14",
}
function QifuYunShiFirstView:OnFlush()
	-- local has_fortune = QifuYunShiWGData.Instance:GetToDayHasFortune()
	-- self.node_list["chouqian_root"]:SetActive(not has_fortune)
	-- self.node_list["yunshi_content"]:SetActive(has_fortune)
	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local fortune_type = fortune_info.fortune_type
	local add_list = QifuYunShiWGData.Instance:GetCurMyFortuneAdditionCfg()
	self.yunshi_list_view:SetDataList(add_list)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_str = TimeUtil.FormarDaXieTimeYMD(server_time)
	self.node_list["yunshi_timer"].text.text = time_str
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
	if not IsEmptyTable(fortune_cfg) then
		self.node_list["yunshi_name"].image:LoadSprite(ResPath.GetF2WelfareImages("yunshi_img"..fortune_type))
		self.node_list["yunshi_name"].image:SetNativeSize()
		self.node_list["qian_yunshi_name"].text.text = ToColorStr(fortune_cfg.name,YunShiColor[fortune_type])
		self.node_list["best_img"]:SetActive(fortune_cfg.is_best == 1 and self.paly_anim_end)

		local refresh_times = fortune_info.refresh_times > 0 and fortune_info.refresh_times or 1
		refresh_times = refresh_times + 1
		local max_timer = QifuYunShiWGData.Instance:GetMaxRefreshTimer()
		refresh_times = refresh_times >= max_timer and max_timer or refresh_times
		local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(refresh_times)
		if not IsEmptyTable(refresh_cfg) then
			local item_cfg = ItemWGData.Instance:GetItemConfig(refresh_cfg.cost_item_id)
			if not IsEmptyTable(item_cfg) then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
				local num_color = has_num >= refresh_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
				local need_num = refresh_cfg.cost_item_num
				local num_str = string.format("%s/%s",has_num,need_num)
				self.node_list["item_num_text"].text.text = ToColorStr(num_str,num_color)
				-- self.node_list["item_text_line"].text.text = ToColorStr(Language.YunShi.ItemTextLine,ITEM_COLOR[item_cfg.color])
				self.node_list["item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
			end
		end
	end
end

-- function QifuYunShiFirstView:FlushHasYunShi()
-- 	-- self.node_list["ignore_anim_toggle"].transform.anchoredPosition = u3dpool.vec3(200, -290, 0)
-- 	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
-- 	local fortune_type = fortune_info.fortune_type
-- 	local add_list = QifuYunShiWGData.Instance:GetCurMyFortuneAdditionCfg()
-- 	self.yunshi_list_view:SetDataList(add_list)
-- 	local server_time = TimeWGCtrl.Instance:GetServerTime()
-- 	local time_str = TimeUtil.FormarDaXieTimeYMD(server_time)
-- 	self.node_list["yunshi_timer"].text.text = time_str
-- 	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
-- 	if not IsEmptyTable(fortune_cfg) then
-- 		self.node_list["yunshi_name"].image:LoadSprite(ResPath.GetF2WelfareImages("yunshi_img"..fortune_type))
-- 		self.node_list["yunshi_name"].image:SetNativeSize()
-- 		self.node_list["qian_yunshi_name"].text.text = ToColorStr(fortune_cfg.name or "",YunShiColor[fortune_type])
-- 		self.node_list["best_img"]:SetActive(fortune_cfg.is_best == 1)
-- 		-- self.node_list["item_num_text"]:SetActive(fortune_cfg.is_best ~= 1)
-- 		-- self.node_list["change_yunshi_btn"]:SetActive(fortune_cfg.is_best ~= 1)
-- 		-- self.node_list["ignore_anim_toggle"]:SetActive(fortune_cfg.is_best ~= 1)
-- 		local refresh_times = fortune_info.refresh_times > 0 and fortune_info.refresh_times or 1
-- 		refresh_times = refresh_times + 1
-- 		local max_timer = QifuYunShiWGData.Instance:GetMaxRefreshTimer()
-- 		refresh_times = refresh_times >= max_timer and max_timer or refresh_times
-- 		local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(refresh_times)
-- 		if not IsEmptyTable(refresh_cfg) then
-- 			local item_cfg = ItemWGData.Instance:GetItemConfig(refresh_cfg.cost_item_id)
-- 			if not IsEmptyTable(item_cfg) then
-- 				local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
-- 				local num_color = has_num >= refresh_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
-- 				local need_num = refresh_cfg.cost_item_num
-- 				local num_str = string.format("%s/%s",has_num,need_num)
-- 				self.node_list["item_num_text"].text.text = ToColorStr(num_str,num_color)
-- 				-- self.node_list["item_text_line"].text.text = ToColorStr(Language.YunShi.ItemTextLine,ITEM_COLOR[item_cfg.color])
-- 				self.node_list["item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
-- 			end
-- 		end
-- 	end

-- end

function QifuYunShiFirstView:ClickChouQianBtn()
	local send_fun = function ()
		QiFuWGCtrl.Instance:SendCSFortuneOperate(FORTUNE_OPERATE_TYPE.FORTUNE_OPERATE_TYPE_REFRESH_TYPE,0)
	end
	QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
	QifuYunShiWGData.Instance:SetFirstSendChouQianFlag(true)
	send_fun()
	-- self:PlayChouQianAnim(send_fun)
end

function QifuYunShiFirstView:ChouQianAnim()
    self.node_list["chouqian_img_anim"]:SetActive(true)
	self.node_list["yunshi_content"]:SetActive(false)

	local tween_scale = self.node_list["chouqian_img_anim"].transform:DOScale(1.3, 0.5)

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:Append(tween_scale)
    self.sequence:SetEase(DG.Tweening.Ease.Linear)
	self.sequence:OnComplete(function ()
		if self.ske_graphic_chouqian and self.ske_graphic_chouqian.AnimationState then
	    	self.ske_graphic_chouqian.AnimationState:SetAnimation(0, "action", true)
	    end
		TryDelayCall(self, function ()
			self:YunShiAnim()
		end, 1, "chouqian_img_anim_delay")
	end)
end

function QifuYunShiFirstView:YunShiAnim()
	self.node_list["close_block"]:SetActive(true)
	self.node_list["chouqian_img_anim"]:SetActive(false)
	self.node_list["auto_close_text"]:SetActive(false)
	self.node_list["yunshi_content"]:SetActive(true)
	if self.ske_graphic_chouqian and self.ske_graphic_chouqian.AnimationState then
    	self.ske_graphic_chouqian.AnimationState:SetAnimation(0, "stand", false)
    end
    self.paly_anim_end = false
	local qian_obj = self.node_list["qian_img"].gameObject
	local qian_obj_x, qian_obj_y = qian_obj.transform.anchoredPosition.x, qian_obj.transform.anchoredPosition.y
	local qian_obj_start_rotation = u3dpool.vec3(0, 0, 10)
	qian_obj.transform.localRotation = u3dpool.vec3(0, 0, 0)
	qian_obj.transform.localScale = u3dpool.vec3(0, 0, 0)
	qian_obj.transform.anchoredPosition = u3dpool.vec3(0, -12, 0)

	local yunshi_anim_obj = self.node_list["yunshi_anim_root"].gameObject
	local yunshi_anim_obj_x, yunshi_anim_obj_y = yunshi_anim_obj.transform.anchoredPosition.x, yunshi_anim_obj.transform.anchoredPosition.y
	yunshi_anim_obj.transform.anchoredPosition = u3dpool.vec3(0, 740, 0)

	local best_img_obj = self.node_list["best_img"].gameObject
	best_img_obj:SetActive(false)	
	local yunshi_name_obj = self.node_list["yunshi_name"].gameObject
	yunshi_name_obj:SetActive(false)	

	local qian_tween1 = qian_obj.transform:DOScale(Vector3(1, 1, 1), 0.25)
	local qian_tween2 = qian_obj.transform:DOAnchorPos(u3dpool.vec3(qian_obj_x, qian_obj_y, 0), 0.25)
	local qian_tween3 = qian_obj.transform:DOLocalRotate(u3dpool.vec3(0, 0, 10), 0.25)
	local yunshi_anim_tween1 = yunshi_anim_obj.transform:DOAnchorPos(u3dpool.vec3(yunshi_anim_obj_x, yunshi_anim_obj_y, 0), 0.25)

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	self.anim_tween = DG.Tweening.DOTween.Sequence()
	self.anim_tween:Append(qian_tween1)
	self.anim_tween:Append(qian_tween2)
	self.anim_tween:Append(qian_tween3)
	self.anim_tween:Append(yunshi_anim_tween1)
	self.anim_tween:SetEase(DG.Tweening.Ease.Linear)
	self.anim_tween:OnComplete(function ()
		local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
		local fortune_type = fortune_info.fortune_type
		local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
		yunshi_name_obj:SetActive(true)
		self.paly_anim_end = true
		UITween.ScaleShowPanel(yunshi_name_obj,u3dpool.vec3(5, 5, 5))
		if not IsEmptyTable(fortune_cfg) and fortune_cfg.is_best == 1 then
			best_img_obj:SetActive(true)
			UITween.ScaleShowPanel(best_img_obj,u3dpool.vec3(5, 5, 5))
			
		end
		
		self.node_list["chouqian_bg_btn"]:SetActive(false)
		local bundle, asset = ResPath.GetEffectUi("UI_yunshi_sg_001")
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["yunshi_anim_effect"].transform,1)
		TryDelayCall(self,function ()
			self.node_list["close_block"]:SetActive(false)
		end,1,"show_close_block")
		-- self:StartAutoClose()
	end)
end

function QifuYunShiFirstView:PlayAnim()
	QifuYunShiWGData.Instance:SetFirstSendChouQianFlag(false)
	self.node_list["close_block"]:SetActive(true)
	self.node_list["chouqian_btn"]:SetActive(false)
	self:ChouQianAnim()
end

function QifuYunShiFirstView:StartAutoClose()
	self.node_list["auto_close_text"]:SetActive(true)
	if CountDownManager.Instance:HasCountDown("yunshi_auto_close_timer") then
		CountDownManager.Instance:RemoveCountDown("yunshi_auto_close_timer")
	end
	
	CountDownManager.Instance:AddCountDown("yunshi_auto_close_timer", BindTool.Bind1(self.UpdateRefreshTime, self), BindTool.Bind1(self.CompleteCallBack, self), nil, 10,1)
end

function QifuYunShiFirstView:UpdateRefreshTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	self.node_list["auto_close_text"].text.text = string.format(Language.YunShi.AutoCloseDes,time)
end

function QifuYunShiFirstView:CompleteCallBack()
	self:Close()
end

function QifuYunShiFirstView:Close()
	local is_on_s_btn = MainuiWGCtrl.Instance:GetShrinkButtonToggleIsOn()
	if is_on_s_btn == false then
		MainuiWGCtrl.Instance:PlayTopButtonTween(true, nil, true)
		self.need_close_anim = true
		return
	end
	if self:IsLoaded() then
		local root_obj = self.node_list["yunshi_root"]
		local start_pos = root_obj.transform.anchoredPosition
		local qifu_btn = MainuiWGCtrl.Instance:GetBtnQiFuView()
		if qifu_btn then
			local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
		    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, qifu_btn.rect.position)
		    local parent_node_rect = root_obj.transform
		    local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
		    local end_pos = Vector2(local_bullet_start_pos_tbl.x,local_bullet_start_pos_tbl.y)
			UITween.MoveToScaleAndShowPanel(root_obj,start_pos,end_pos,0,nil,nil,function ()
				SafeBaseView.Close(self)
			end)
		else
			SafeBaseView.Close(self)
		end
	else
		SafeBaseView.Close(self)
	end
end

function QifuYunShiFirstView:TopPanelAniCallBack(is_on)
	if is_on and self.need_close_anim then
		local anim_fun = function ()
			local root_obj = self.node_list["yunshi_root"]
			local start_pos = root_obj.transform.anchoredPosition
			local qifu_btn = MainuiWGCtrl.Instance:GetBtnQiFuView()
			local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
		    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, qifu_btn.rect.position)
		    local parent_node_rect = root_obj.transform
		    local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
		    local end_pos = Vector2(local_bullet_start_pos_tbl.x,local_bullet_start_pos_tbl.y)
			UITween.MoveToScaleAndShowPanel(root_obj,start_pos,end_pos,0,nil,nil,function ()
				SafeBaseView.Close(self)
			end)
		end
		TryDelayCall(self,anim_fun,0.45,"yunshi_first_delay_close")
		self.need_close_anim = false
	end
end

function QifuYunShiFirstView:ClickChangeYunShiBtn()
	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local refresh_times = fortune_info.refresh_times > 0 and fortune_info.refresh_times or 1
	refresh_times = refresh_times + 1
	local max_timer = QifuYunShiWGData.Instance:GetMaxRefreshTimer()
	refresh_times = refresh_times >= max_timer and max_timer or refresh_times
	local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(refresh_times)
	local fortune_type = fortune_info.fortune_type
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
	-- if fortune_cfg and fortune_cfg.is_best == 1 then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.YunShi.IsBestTips)
	-- 	return
	-- end
	if not IsEmptyTable(refresh_cfg) then
		if self.item_toggle_ison then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
			local need_num = refresh_cfg.cost_item_num or 0
			if has_num >= need_num then
				local ok_fun = function ()
					QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
					QiFuWGCtrl.Instance:SendCSFortuneOperate(FORTUNE_OPERATE_TYPE.FORTUNE_OPERATE_TYPE_REFRESH_TYPE,0)
				end

				if fortune_cfg.is_best == 1 then
					if not self.beat_yunshi_alert then
			            self.beat_yunshi_alert = Alert.New()
			        end
			        self.beat_yunshi_alert:SetShowCheckBox(true, "beat_yunshi_alert")
			        self.beat_yunshi_alert:SetOkFunc(ok_fun)
			        self.beat_yunshi_alert:SetLableString(Language.YunShi.IsBestAlert)
			        self.beat_yunshi_alert:SetCheckBoxDefaultSelect(false)
			        self.beat_yunshi_alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
			        self.beat_yunshi_alert:Open()
				else
					ok_fun()	
				end
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.YunShi.ItemNotEnough)
			end
		else
			local str = string.format(Language.YunShi.UseGoldChangeYunShi,refresh_cfg.cost_gold)
			local ok_fun = function ()
				local enough_gold = RoleWGData.Instance:GetIsEnoughUseGold(refresh_cfg.cost_gold)
				if not enough_gold then
					VipWGCtrl.Instance:OpenTipNoGold()
					return
				end
				local send_fun = function ()
					QiFuWGCtrl.Instance:SendCSFortuneOperate(FORTUNE_OPERATE_TYPE.FORTUNE_OPERATE_TYPE_REFRESH_TYPE,1)
				end
				QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
				send_fun()
				-- self:PlayChouQianAnim(send_fun)
			end

			if not self.yunshi_alert then
	            self.yunshi_alert = Alert.New()
	        end
	        self.yunshi_alert:SetShowCheckBox(true, "qifu_yunshi_alert")
	        self.yunshi_alert:SetOkFunc(ok_fun)
	        self.yunshi_alert:SetLableString(str)
	        self.yunshi_alert:SetCheckBoxDefaultSelect(false)
	        self.yunshi_alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
	        self.yunshi_alert:Open()
		end
	end
end


function QifuYunShiFirstView:ClickIgnoreAnimToggle(ison)
	QifuYunShiWGData.Instance:SetIgnoireAnimFlag(ison)
end


function QifuYunShiFirstView:ClickItemToggle(ison)
	self.item_toggle_ison = ison
end

function QifuYunShiFirstView:ClickItemIcon()
	local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(1)
	-- TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil)
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = refresh_cfg.cost_item_id})
end

function QifuYunShiFirstView:ClickCheckYunShiBtn()
	RuleTip.Instance:SetContent(Language.YunShi.YunShiAfterTips, Language.YunShi.YunShiTips, nil, nil, true)
end

-- function QifuYunShiFirstView:CloseAnim()
-- 	local is_on_s_btn = MainuiWGCtrl.Instance:GetShrinkButtonToggleIsOn()
-- 	if is_on_s_btn == false then
-- 		MainuiWGCtrl.Instance:PlayTopButtonTween(true, nil, true)
-- 		self.need_close_anim = true
-- 		return
-- 	end
-- 	local root_obj = self.node_list["yunshi_root"]
-- 	local start_pos = root_obj.transform.anchoredPosition
-- 	local qifu_btn = MainuiWGCtrl.Instance:GetBtnQiFuView()
-- 	local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
--     local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, qifu_btn.rect.position)
--     local parent_node_rect = root_obj.transform
--     local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
--     local end_pos = Vector2(local_bullet_start_pos_tbl.x,local_bullet_start_pos_tbl.y)
-- 	UITween.MoveToScaleAndShowPanel(root_obj,start_pos,end_pos,0,nil,nil,function ()
-- 		SafeBaseView.Close(self)
-- 	end)
-- end

-- function QifuYunShiFirstView:ClickCloseBtn()
-- 	self:CloseAnim()
-- end