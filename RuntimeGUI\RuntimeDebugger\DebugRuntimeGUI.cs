﻿using System.Collections.Generic;
using UnityEngine;

public partial class DebugRuntimeGUI
{
    internal static Rect DefaultIconRect = new Rect(0f, 0f, 120f, 70f);
    internal static Rect DefaultWindowRect = new Rect(0f, 0f, 1334, 768);
    internal static readonly float DefaultWindowScale = 1f;
    private Rect m_IconRect = DefaultIconRect;
    private Rect m_WindowRect = DefaultWindowRect;
    private Rect m_DragRect = new Rect(0f, 0f, float.MaxValue, 25f);
    private float m_WindowScale = DefaultWindowScale;
    
    private bool m_ShowFullWindow = false;
    
    //IconRect限制范围
    private float m_MinPosX = 0f;
    private float m_MinPosY = 0f;
    private float m_MaxPosX = 0f;
	private float m_MaxPosY = 0f;
	
	private readonly string LogTips = "查看日志";
    private readonly string ErrorTips = "有报错！";
    
    private DebugWindowTabbar m_DebugWindowRoot = new DebugWindowTabbar();
    private ConsoleRuntimeWindow m_LogWindow = new ConsoleRuntimeWindow();
    private SettingWindow m_SettingWindow;
    private LuaConsoleGUI m_luaConsoleGUI;
   
    public DebugRuntimeGUI()
    {
	    DefaultIconRect = new Rect(0f, 0f, 120f, 70f);
	    DefaultWindowRect = new Rect(0f, 0f, Screen.width, Screen.height);
    }
	
    public void Init()
    {
	    m_DebugWindowRoot.Init();
	    m_SettingWindow = new SettingWindow();
	    m_luaConsoleGUI = new LuaConsoleGUI();
	    
	    m_MaxPosX = Screen.width - DefaultIconRect.width;
	    m_MaxPosY = Screen.height - DefaultIconRect.height;

	    if (RuntimeGUIMgr.Instance.IsGUIOpening())
	    {
		    RegisterWindow("日志", m_LogWindow);
		    m_LogWindow.SetErrorListener(SetIconPosToScreenCenter);
		    
            RegisterWindow("其他/Lua控制台", m_luaConsoleGUI);
            RegisterWindow("其他/设置", m_SettingWindow);	
	    }
    }

    public void Destroy()
    {
	    m_DebugWindowRoot.Destroy();
	    m_SettingWindow = null;
	    m_luaConsoleGUI = null;
    }

    public void Update()
    {
	    m_DebugWindowRoot.Update();
    }
    
	public void OnGUI()
	{
		Matrix4x4 tempMatrix = GUI.matrix;
        GUI.matrix = Matrix4x4.Scale(new Vector3(m_WindowScale, m_WindowScale, 1f));
        if (m_ShowFullWindow)
        {	
	        m_WindowRect = GUILayout.Window(100, m_WindowRect, DrawWindow, "调试窗口");
        }
        else
        {	
	        m_IconRect = GUILayout.Window(100, m_IconRect, DrawDebuggerWindowIcon, "调试窗口");
        }

        
        RuntimeGUIMgr.Instance.GetGUIBlock().ShowRect(m_WindowRect, 1);
        GUI.matrix = tempMatrix;
	}
	
	public void RegisterWindow(string name, IDebugWindow window)
	{
		m_DebugWindowRoot.RegisterWindow(name, window);
		window.Init();
	}
	
	private void DrawWindow(int windowId)
	{
		DrawWindowHandle(m_DebugWindowRoot);
	}

	private void DrawWindowHandle(DebugWindowTabbar window)
	{
		var nameArray = window.GetDebuggerWindowNames();
        List<string> names = new List<string>();
        for (int i = 0; i < nameArray.Length; i++)
        {
        	names.Add(nameArray[i]);
        }

        if (window == m_DebugWindowRoot)
        {
	        names.Add("关闭");
        }

        int toolbarIndex = GUILayout.Toolbar(window.SelectIndex, names.ToArray(), GUILayout.Height(40f), GUILayout.MaxWidth(Screen.width));
        if (toolbarIndex >= nameArray.Length)
        {
        	m_ShowFullWindow = false;
        	return;
        }
        
        if (window.SelectWindow == null)
        {
        	return;
        }
        
        //点击不同的按钮调用一次
        if (window.SelectIndex != toolbarIndex)
        {
	        window.SelectWindow.OnLeave();
	        window.SelectIndex = toolbarIndex;
	        window.SelectWindow.OnEnter();
        }
        
        DebugWindowTabbar subTabbar = window.SelectWindow as DebugWindowTabbar;
        if (subTabbar != null)
        {
	        DrawWindowHandle(subTabbar);
        }
        
        window.SelectWindow.OnDraw();
	}
	
	private void DrawDebuggerWindowIcon(int windowId)
    {
        GUI.DragWindow(m_DragRect);
        FixDragIconPos();
        GUILayout.Space(5);
        Color32 color = Color.white;
        if (m_LogWindow == null)
        {
            return;
        }
        m_LogWindow.RefreshCount();
        
        if (m_LogWindow.ErrorCount > 0)
        {
            color = m_LogWindow.GetLogStringColor(LogType.Error);
        }
        else if (m_LogWindow.WarningCount > 0)
        {
            color = m_LogWindow.GetLogStringColor(LogType.Warning);
        }
        else
        {
            color = m_LogWindow.GetLogStringColor(LogType.Log);
        }

        string str = m_LogWindow.ErrorCount > 0 ? ErrorTips : LogTips; 
        string title = string.Format("<color=#{0}{1}{2}{3}><b><size=18>{4}</size></b></color>", color.r.ToString("x2"), color.g.ToString("x2"), color.b.ToString("x2"), color.a.ToString("x2"), str);
        if (GUILayout.Button(string.Format("<size=18>{0}</size>", title), GUILayout.Width(120f), GUILayout.Height(40f)))
        {
            m_ShowFullWindow = true;
        }
    }
	
	private void FixDragIconPos()
    {
	    float x = Mathf.Clamp(m_IconRect.x, m_MinPosX, m_MaxPosX);
	    float y = Mathf.Clamp(m_IconRect.y, m_MinPosY, m_MaxPosY);

        m_IconRect.position = new Vector2(x, y);
    }
	
	private void SetIconPosToScreenCenter()
	{
		m_IconRect.position = new Vector2((Screen.width - m_IconRect.width) * 0.5f, (Screen.height - m_IconRect.height) * 0.5f);
	}

	public float WindowScale
	{
		get { return m_WindowScale; }
		set { m_WindowScale = value; }
	}
}
