----------------------------------------------------
-- 登录有礼
----------------------------------------------------
ActPictureRewardView = ActPictureRewardView or BaseClass(SafeBaseView)

local CAN_REWARD_LEVEL = 80

function ActPictureRewardView:__init(act_id)
	self:AddViewResource(0, "uis/view/act_subview_ui_prefab", "layout_login_view")
	self.act_id = act_id
	self.open_tween = nil
	self.close_tween = nil
end

function ActPictureRewardView:__delete()

end

function ActPictureRewardView:ReleaseCallBack()
	if self.reward_list ~= nil then 
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	self.has_load_callback = nil
	self.need_refresh = nil
end

function ActPictureRewardView:LoadCallBack()
	self.reward_list = AsyncListView.New(PictureItemRender,self.node_list["ph_login_gift_list"])
	self.has_load_callback = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ActPictureRewardView:RefreshView(param_list)
	if not self.has_load_callback then
		self.need_refresh = true
		return
	end
	self:RefreshTopDesc()
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_LOGIN_GIFT)
	local reward_cfg = __TableCopy(ServerActivityWGData.Instance:GetActivityItemDataList(ServerActClientId.RAND_LOGIN_GIFT))
	for i,v in ipairs(reward_cfg) do
		if info.can_reward_flag[32 - (i - 1)] == 1 then
			v.is_lingqu = 1
		else
			v.is_lingqu = 0
		end
	end

	local sort_func = function (a, b)
		local order_a = 100000
		local order_b = 100000
		if a.is_lingqu > b.is_lingqu then
			order_a = order_a + 10000
		elseif a.is_lingqu < b.is_lingqu then
			order_b = order_b + 10000
		end

		if a.day_index > b.day_index then
			order_a = order_a + 100
		elseif a.day_index < b.day_index then
			order_b = order_b + 100
		end
		return order_a < order_b
	end
	table.sort(reward_cfg, sort_func)
	self.reward_list:SetDataList(reward_cfg,3)
end

function ActPictureRewardView:UpdateCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
	local tip_str = self.act_status.status == ACTIVITY_STATUS.CLOSE and Language.Activity.ActClosing or Language.Activity.ActPreparing
	self.node_list.wb_next_flush_time.text.text = (tip_str..TimeUtil.FormatSecond2HMS(last_time))
end

function ActPictureRewardView:CompleteCountDownTime(is_auto_fuhuo)
	self:RefreshView()
end

function ActPictureRewardView:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)

	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 1)
		end
		self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text = (open_act_cfg.top_desc)
	end
end

function ActPictureRewardView:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function ActPictureRewardView:CloseCallBack()
	self.open_tween = nil
end

-------------------------------------------------------------------
------------------         itemRender           -------------------
-------------------------------------------------------------------
PictureItemRender = PictureItemRender or BaseClass(BaseRender)
function PictureItemRender:__init()
	
end

function PictureItemRender:__delete()

end

function PictureItemRender:ReleaseCallBack()
	for i,v in ipairs(self.item_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end
	self.cell_cache = {}
	self.item_list = {}
end

function PictureItemRender:LoadCallBack()
	self.item_list = {}
	for i=1,4 do
		self.item_list[i] = ItemCell.New(self.node_list["ph_cell_" .. i])
	end
	XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind1(self.OnClickLingQu, self))
	self.cell_cache = {1,1,1,1}
end

function PictureItemRender:OnFlush()
	if nil == self.data then return end
	local item_data = self.data.reward_item
	local cell_cache2 = {false,false,false,false}
	for i=1,4 do
		if nil == item_data[i - 1] then
			-- self.item_list[i]:SetActive(false)
			cell_cache2[i] = false
		else
			self.item_list[i]:SetData(item_data[i - 1])
			-- self.item_list[i]:SetActive(true)
			cell_cache2[i] = true
		end
	end

	for k,v in pairs(cell_cache2) do
		if self.cell_cache[k] ~= cell_cache2[k] then
			self.cell_cache[k] = cell_cache2[k]
			self.item_list[k]:SetActive(cell_cache2[k])
		end
	end

	local flag = true
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_LOGIN_GIFT)
	if 1 == info.reward_flag[32 - (self.data.day_index)] then
		self.node_list.btn_receive:SetActive(true)
		self.node_list.img_has:SetActive(false)
		self.node_list.img_no:SetActive(false)
		-- flag = false
	else
		self.node_list.btn_receive:SetActive(false)
		self.node_list.img_has:SetActive(false)
		self.node_list.img_no:SetActive(true)
		-- flag = true
	end

	if info.can_reward_flag[32 - (self.data.day_index)] == 1 then
		self.node_list.btn_receive:SetActive(false)
		self.node_list.img_has:SetActive(true)
		self.node_list.img_no:SetActive(false)
		-- flag = false
	end

	-- XUI.SetButtonEnabled(self.node_list.btn_receive,not flag)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_LOGIN_GIFT)
	local star_str = ""
	if act_info then
		star_str = TimeUtil.FormatSecond2MY(act_info.start_time + (self.data.day_index * 3600 * 24))
	end

	self.node_list.lbl_desc.text.text = (star_str .. Language.Achieve.KeLingQu2)
end

function PictureItemRender:OnClickLingQu()
	--80级
	local level = RoleWGData.Instance.role_vo.level
	if level < CAN_REWARD_LEVEL then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.NoAchieve)
		return
	end

	local param_t ={}
	param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_LOGIN_GIFT,
		opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_FETCH_REWARD,
		param_1 = self.data.day_index
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	AudioService.Instance:PlayRewardAudio()
end

--override
function PictureItemRender:CreateSelectEffect()
	
end

