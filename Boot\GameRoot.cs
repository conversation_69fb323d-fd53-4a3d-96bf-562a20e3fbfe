﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

using System;
using LuaInterface;
using Nirvana;
using UnityEngine;
using UnityEngine.SceneManagement;
using System.IO;
using System.Text;
using System.Collections.Generic;

/// <summary>
/// Used to start the game.
/// </summary>
public sealed class GameRoot : MonoBehaviour 
{
    [SerializeField]
    private GameObject loadingPrefab;

    private LuaState luaState;
    private LuaB<PERSON>le<PERSON>oader luaLoader;

    private LuaFunction luaUpdate;
    private LuaFunction luaStop;
    private LuaFunction luaFocus;
    private LuaFunction luaPause;
    private LuaFunction luaExecuteGm;
    private LuaFunction luaCollectgarbage;
    private LuaFunction luaCheckMemoryLeak;
    private LuaFunction luaGetMainRoleLevel;
    private LuaFunction luaGetDebugTrackback;
    private LuaFunction luaConsoleGm;
    private LuaFunction luaGetAllResources;
    private LuaFunction luaGetAllGameObject;
    private LuaFunction luaGetResTraceBack;
    private LuaFunction luaGetAssetBundleLeak;

    private LuaFunction luaPlayAudio;

    private LuaLooper looper = null;
    private static bool isEnterGamePlay = false;
    private static bool isCollectGraphicMem = false;
    private static bool isShowGraphicWindow = false;
    private static bool isLowMemColllect = false;
    private static bool isChangeSceneColllect = false;
    private static int maxSceneLogCount = 0;
    private bool is_delay_time = false;
    private int delay_time = 0;
    private bool startFixRequest = false;
    private int countDown = 0;
    private static bool hasSendFixRequest = false;

    public static string GetQueryUrl
    {
        //国服内网Dev
        get {
            return "http://192.168.0.133/a3/cn/v1/client/init.php";
        }
    }

    /// <summary>
    /// Gets the singleton instance.
    /// </summary>
    public static GameRoot Instance { get; private set; }

    /// <summary>
    /// The stop event for lua.
    /// </summary>
    [NoToLua]
    public event Action StopEvent;

    [NoToLua]
    public event Action UpdateEvent;

    /// <summary>
    /// Query签名加密Key
    /// </summary>
    public const string GLOBAL_URL_SIGN_KEY = "fba0de406ea352eb8ff760c0587611d3";

    /// <summary>
    /// Query解密Key
    /// </summary>
    public static string QueryURLBase64DecodeKey = "dd0091cb686e627ece36dfb513cfb2c9";

    /// <summary>
    /// The lua state.
    /// </summary>
    [NoToLua]
    public LuaState LuaState
    {
        get
        {
            return luaState;
        }
    }

    public static bool IsUseAndroid64()
    {
#if UNITY_ANDROID
        return true;
#else
        return false;
#endif
    }

    /// <summary>
    /// Clear old lua vm, restart the game.
    /// </summary>
    public void Restart()
    {
        // Stop the lua engine.
        if (this.luaState != null)
        {
            if (this.luaStop != null)
            {
                this.luaStop.Call();
            }

            if (this.StopEvent != null)
            {
                this.StopEvent();
            }

            var oldState = this.luaState;
            this.luaState = null;
            Scheduler.Delay(
                () =>
                {
                    oldState.Dispose();
                    // Destroy self.
                    GameObject.Destroy(this.gameObject);
                    // Reload start scene.
                    SceneManager.LoadScene(0);
                }
            );
            this.luaLoader = null;
        }

        // Kill all dotween.
        DG.Tweening.DOTween.KillAll(false);
        OverrideOrderGroupMgr.Instance.OnGameStop();
        MaterialMgr.Instance.OnGameStop();
        CameraCullObjMgr.Instance.OnGameStop();
        FontTextureReBuild.Instance.OnGameStop();
        EffectOrderGroup.OnGameStop();
        GPUInstancingOrderGroup.OnGameStop();
        RichTextGroup.ClearPool();
        CameraDepthMgr.Instance.OnGameStop();
        DownloadBufferMgr.OnGameStop();

        AssetBundle.UnloadAllAssetBundles(true);
        Resources.UnloadUnusedAssets();
        GC.Collect();

        if (this.looper != null)
        {
            GameObject.Destroy(this.looper);
        }
        //         // Destroy self.
        //         GameObject.Destroy(this.gameObject);
        //
        //         // Reload start scene.
        //         SceneManager.LoadScene(0);
    }

    /// <summary>
    /// Prune the lua bundles.
    /// </summary>
    public void PruneLuaBundles()
    {
        luaLoader.PruneLuaBundles();
    }

    /// <summary>
    /// Set the user ID for bugly
    /// </summary>
    public void SetBuglyUserID(string userID)
    {
        BuglyAgent.SetUserId(userID);
    }

    /// <summary>
    /// Set the scene ID for bugly
    /// </summary>
    public void SetBuglySceneID(int sceneID)
    {
        BuglyAgent.SetScene(sceneID);
    }

    public void UpdateLogEnable(bool isForceClose = false)
    {
#if !UNITY_EDITOR && UNITY_STANDALONE
        Debug.unityLogger.logEnabled = true;
        return;
#endif

        if (isForceClose)
        {
            Debug.unityLogger.logEnabled = false;
            return;
        }

        if (!ChannelAgent.GetOutlog())
        {
            Debug.unityLogger.filterLogType = LogType.Error;
            return;
        }

        Debug.unityLogger.filterLogType = LogType.Log;
    }

    /// <summary>
    /// 限制屏幕分辨率的尺寸.
    /// </summary>
    public void LimitScreenResolution(int limit)
    {
        var isFullscreen = UnityEngine.Application.platform != UnityEngine.RuntimePlatform.WindowsPlayer;
        if (Screen.width > Screen.height)
        {
            if (Screen.height > limit)
            {
                var radio = (float)Screen.width / Screen.height;
                Screen.SetResolution((int)(limit * radio), limit, isFullscreen);
            }
        }
        else
        {
            if (Screen.width > limit)
            {
                var radio = (float)Screen.width / Screen.height;
                Screen.SetResolution(limit, (int)(limit * radio), isFullscreen);
            }
        }

    }

    [MonoPInvokeCallback(typeof(LuaCSFunction))]
    private static int LuaOpen_Socket_Core(IntPtr l)
    {
        return LuaDLL.luaopen_socket_core(l);
    }

    [MonoPInvokeCallback(typeof(LuaCSFunction))]
    private static int LuaOpen_Mime_Core(IntPtr l)
    {
        return LuaDLL.luaopen_mime_core(l);
    }

    private void Awake()
    {
        Debugger.Log("game root awake");
        
        this.UpdateLogEnable();

        // 创建Loading界面.
        var loading = GameObject.Find("Loading");
        if (loading == null)
        {
            loading = GameObject.Instantiate(this.loadingPrefab);
            loading.name = loading.name.Replace("(Clone)", string.Empty);
            GameObject.DontDestroyOnLoad(loading);
        }

        // 初始化bugly
        this.InitBuglySDK();

        // 永久存在的单件.
        Instance = this;
        GameObject.DontDestroyOnLoad(this.gameObject);

        // 初始化日志工具.
        LogSystem.AddAppender(new LogUnity());

        // 监听低内存更新
        Application.lowMemory -= this.OnLowMemory;
        Application.lowMemory += this.OnLowMemory;

        // 监听下载事件
#if UNITY_EDITOR
        AssetManager.DownloadStartEvent +=
            url => Debug.Log("##DownloadStart: " + url);
        AssetManager.DownloadFinishEvent +=
            url => Debug.Log("##DownloadFinish: " + url);
#endif

        // 检查并设置分辨率.
#if UNITY_EDITOR || !UNITY_ANDROID && !UNITY_IOS
        this.LimitScreenResolution(1080);
#endif
    }

    private void InitBuglySDK()
    {
#if _DEBUG
        // 开启SDK的日志打印，发布版本请务必关闭
        BuglyAgent.ConfigDebugMode(true);
#endif

        // 配置标识参数, 必须在InitWithAppId之前调用.
        BuglyAgent.ConfigDefault(
            ChannelAgent.GetChannelID(),
            Application.version,
            string.Empty,
            0);

#if UNITY_IOS
        BuglyAgent.InitWithAppId("5f4a5e788c");
#elif UNITY_ANDROID
        BuglyAgent.InitWithAppId("50dde67be7");
#endif

        // 如果你确认已在对应的iOS工程或Android工程中初始化SDK，那么在脚本中只
        // 需启动C#异常捕获上报功能即可
        BuglyAgent.EnableExceptionHandler();
    }

    private void OnDestroy()
    {
        Instance = null;
    }

    private void Start()
    {
        if (RuntimeGUIMgr.Instance.IsGUIOpening() || RuntimeGUIMgr.Instance.IsAndroidGM())
        {
            Screen.fullScreen = false;
            RuntimeGUIMgr.Instance.Init();

            if (!isEnterGamePlay)
            {
                RuntimeGUIMgr.Instance.ShowLoginWindow(OnGamePlay);
            }
            else
            {
                OnGamePlay();
            }
            return;
        }

        RuntimeGUIMgr.Instance.DelAllCache();
        OnGamePlay();
#if !UNITY_EDITOR
        // 检查包体版本，版本不同时需要清理缓存
        CheckPackageVersion();
#endif
    }

    private void OnGamePlay()
    {
        isEnterGamePlay = true;
        UnityEngine.UI.Graphic.defaultGraphicMaterial.shader = Shader.Find("UI/UIZTestOff");
        OverrideOrderGroupMgr.Instance.OnGameStartup();
#if UNITY_EDITOR
        //if (!UnityEngine.PlayerPrefs.HasKey("a3_develop_mode") || UnityEngine.PlayerPrefs.GetInt("a3_develop_mode") == 0)
        //    UnityEngine.PlayerPrefs.SetInt("a3_develop_mode", 1);  // 编辑器
        DeveloperHotupdate.CacheAllFileModifyTime();
#endif

#if UNITY_IOS
        UnzipAssetBundle unzip = new UnzipAssetBundle();
        unzip.Start(()=>
        {
            SendRequestBeforeLuaBundleSetup();
        });
		return;
#endif

        this.StartGame();
    }

    private void StartGame()
    {
        // 初始化资源管理器.
        Debugger.Log("gameroot start game");

        this.startFixRequest = true;
        this.countDown = 30;

#if UNITY_EDITOR || UNITY_STANDALONE
        RuntimeGUIMgr.Instance.TryOpenLuaProfiler();
#endif

        OverrideOrderGroupMgr.Instance.OnGameStartup();
        EncryptMgr.InitEncryptKey();
        MaterialMgr.Instance.OnGameStartup();

        // 构造Lua脚本加载器.
        this.luaLoader = new LuaBundleLoader();
        this.luaLoader.LoadAliasResPathMap();

        // 初始化Lua虚拟机.
        this.luaState = new LuaState();

        this.luaState.OpenLibs(LuaDLL.luaopen_struct);
#if UNITY_STANDALONE_OSX || UNITY_EDITOR_OSX
        luaState.OpenLibs(LuaDLL.luaopen_bit);
#endif
        this.OpenLuaSocket();
        this.OpenCJson();
        LuaLog.OpenLibs(this.luaState);

        this.luaState.LuaPushFunction(AddLuaBundle);
        this.luaState.LuaSetGlobal("AddLuaBundle");

        this.luaState.LuaSetTop(0);
        LuaBinder.Bind(this.luaState);
        DelegateFactory.Init();
        LuaCoroutine.Register(this.luaState, this);

        looper = this.gameObject.AddComponent<LuaLooper>();
        looper.luaState = this.luaState;

#if UNITY_EDITOR
        SetGlobalBoolean("UNITY_EDITOR", true);
        var simulateAssetBundle = (UnityEditor.EditorPrefs.GetInt("SimulateAssetBundlesMode", 1) == 1);
        SetGlobalBoolean("GAME_ASSETBUNDLE", !simulateAssetBundle);
#else
        SetGlobalBoolean("GAME_ASSETBUNDLE", true);
#endif

#if UNITY_EDITOR_WIN
        SetGlobalBoolean("UNITY_EDITOR_WIN", true);
#endif

#if UNITY_STANDALONE_WIN
        SetGlobalBoolean("UNITY_STANDALONE_WIN", true);
#endif

#if UNITY_IOS
        SetGlobalBoolean("UNITY_IOS", true);
#endif

#if UNITY_ANDROID
        SetGlobalBoolean("UNITY_ANDROID", true);
#endif

#if UNITY_STANDALONE
        SetGlobalBoolean("UNITY_STANDALONE", true);
#endif
      
#if DEVELOPMENT_BUILD
        SetGlobalBoolean("DEVELOPMENT_BUILD", true);
#endif

        this.luaLoader.SetupLuaLoader(this.luaState);
        this.luaState.Start();

        try
        {
            Debugger.Log("lib/startup lua");
            // 执行启动文件.
            this.luaState.DoFile("lib/startup.lua");

            // 获取Update函数
            this.luaUpdate = this.luaState.GetFunction("GameUpdate");
            this.luaStop = this.luaState.GetFunction("GameStop");
            this.luaFocus = this.luaState.GetFunction("GameFocus");
            this.luaPause = this.luaState.GetFunction("GamePause");
            this.luaExecuteGm = this.luaState.GetFunction("ExecuteGm");
            this.luaCheckMemoryLeak = this.LuaState.GetFunction("CheckMemoryLeak");
            this.luaCollectgarbage = this.luaState.GetFunction("Collectgarbage");
            this.luaGetMainRoleLevel = this.LuaState.GetFunction("GetMainRoleLevel");
            this.luaGetDebugTrackback = this.luaState.GetFunction("GetDebugTrackback");
            //this.luaConsoleGm = this.luaState.GetFunction("SendInfoToLuaConsole");
            this.luaGetAllResources = this.luaState.GetFunction("GetAllResources");
            this.luaGetAllGameObject = this.luaState.GetFunction("GetAllGameObject");
            this.luaGetResTraceBack = this.luaState.GetFunction("GetResLoaderTraceBack");
            this.luaGetAssetBundleLeak = this.luaState.GetFunction("GetAssetBundleLeak");

            var eventDispatcher = EventDispatcher.Instance;

            eventDispatcher.EnableGameObjAttachFunc = this.luaState.GetFunction("EnableGameObjAttachEvent");
            eventDispatcher.DisableGameObjAttachFunc = this.luaState.GetFunction("DisableGameObjAttachEvent");
            eventDispatcher.DestroyGameObjAttachFunc = this.luaState.GetFunction("DestroyGameObjAttachEvent");

            eventDispatcher.EnableLoadRawImageFunc = this.luaState.GetFunction("EnableLoadRawImageEvent");
            eventDispatcher.DisableLoadRawImageFunc = this.luaState.GetFunction("DisableLoadRawImageEvent");
            eventDispatcher.DestroyLoadRawImageFunc = this.luaState.GetFunction("DestroyLoadRawImageEvent");
            eventDispatcher.UIMouseClickEffectFunc = this.luaState.GetFunction("UIMouseClickEffectEvent");
            eventDispatcher.ProjectileSingleEffectFunc = this.luaState.GetFunction("ProjectileSingleEffectEvent");

            this.luaPlayAudio = this.luaState.GetFunction("PlayAudio");
            ClickSound.OnClick = LuaPlayAudio;

        }
        catch (LuaException exp)
        {
            Debug.LogError(exp.Message);
        }

#if UNITY_EDITOR
        if (1 == UnityEngine.PlayerPrefs.GetInt("a3_develop_mode"))
        {
            CheckController.Start(luaState);
        }
#endif
    }

    void SendRequestBeforeLuaBundleSetup()
    {
        string initUrl;
#if UNITY_EDITOR
        initUrl = GetQueryUrl;
#else
        initUrl = ChannelAgent.GetInitUrl();
#endif
        string queryUrl = string.Empty;
        if (!string.IsNullOrEmpty(initUrl))
        {
            string[] urls = initUrl.Split(',');
            queryUrl = urls[urls.Length - 1];
        }

        string plat_id = ChannelAgent.GetChannelID();
        string pkg_version = Application.version;
        string device = DeviceTool.GetDeviceID();
        string sign = string.Format("device={0}&pkg={1}&plat_id={2}&{3}", device, pkg_version, plat_id, GLOBAL_URL_SIGN_KEY);
        string md5Sign = MD52.GetMD5(sign);

        string url = string.Format("{0}?plat_id={1}&pkg={2}&device={3}&sign={4}", queryUrl, plat_id, pkg_version, device, md5Sign);
        Debug.LogFormat("SendRequest before LuaBundleLoader, url: {0}", url);
        bool isConnectInternet = true;
#if UNITY_EDITOR
        isConnectInternet = true;
#else
        isConnectInternet = Application.internetReachability != NetworkReachability.NotReachable;
#endif
        if (!isConnectInternet)
        {
            //无网络，延迟请求
            is_delay_time = true;
        }
        else
        {
            UtilU3d.RequestGet(url, (bool succ, string data) =>
            {
                Debug.LogFormat("succ: {0}, data: {1}", succ, data);
                if (!succ)
                {
                    return;
                }

                if (string.IsNullOrEmpty(data))
                {
                    return;
                }

                QueryData queryData;
                //内网包不需要解密
                if (!GetFixQueryUrl().Contains(GetQueryUrl))
                {
                    ResponseStrData responseData = ResponseStrData.DecodeJson(data);
                    string decodeStr = DecodeJsonData(responseData.data, QueryURLBase64DecodeKey);
                    queryData = QueryData.DecodeJson(decodeStr);
                }
                else
                {
                    ResponseData responseData = ResponseData.DecodeJson(data);
                    queryData = responseData.data;
                }

                if (null == queryData.param_list)
                {
                    Debug.LogErrorFormat("[Error]SendRequest initUrl Error, param_list: {0}, res_encrypt_type: {1}", null == queryData.param_list, queryData.param_list.res_encrypt_type);
                    return;
                }

                Debug.LogFormat("res_encrypt_type: {0}, res_encrypt_key: {1}, res_base64_value: {2} ", queryData.param_list.res_encrypt_type, queryData.param_list.res_encrypt_key, queryData.param_list.res_base64_value);
                if (queryData.param_list.res_encrypt_type == 2)
                {
                    EncryptMgr.SetEncryptKey(queryData.param_list.res_encrypt_key);
                    EncryptMgr.IsBase64EncryptCacheAsset(true);
                    EncryptMgr.SetBase64EncryptKey(queryData.param_list.res_base64_value);
                    EncryptMgr.SaveCacheEncryptKeyFile();
                    ConfoundMgr.RunRubbishCS();
                }

                this.StartGame();
            });
        }
    }

    void UpdateDelayTime()
    {
        if (delay_time > 30)
        {
            is_delay_time = false;
            delay_time = 0;
            SendRequestBeforeLuaBundleSetup();
        }
    }

    private void Update()
    {
        if (this.startFixRequest)
        {
            // 修复逻辑的启动稍晚于游戏主逻辑，目的是让主逻辑的query先请求
            --this.countDown;
            if (this.countDown <= 0)
            {
                this.startFixRequest = false;
                try
                {
                    SendFixRequest();
                }
                catch(Exception ex)
                {
                    Debug.LogError(ex.ToString());
                }
            }
        }

        if (this.luaState != null)
        {
            this.luaState.Collect();
            //this.luaUpdate.Call();
        }

        if (is_delay_time)
        {
            delay_time += 1;
            UpdateDelayTime();
        }

        FontTextureReBuild.Instance.Update();
        MaterialMgr.Instance.Update();
        RuntimeGUIMgr.Instance.Update();
        DownloadBufferMgr.Update();

        if (isCollectGraphicMem)
            TextureWindow.Update();

#if UNITY_EDITOR
        if (null != this.UpdateEvent)
        {
            this.UpdateEvent();
        }
#endif
    }

    private void OnApplicationFocus(bool hasFocus)
    {
        if (this.luaState != null)
        {
            this.luaFocus.Call(hasFocus);
        }
    }

    private void OnApplicationPause(bool pauseStatus)
    {
        if (this.luaState != null)
        {
            this.luaPause.Call(pauseStatus);
        }
    }

    private void SetGlobalBoolean(string key, bool value)
    {
        this.luaState.LuaPushBoolean(value);
        this.luaState.LuaSetGlobal(key);
    }

    private static bool checkedVersion = false;
    private static void CheckPackageVersion()
    {
        if (checkedVersion)
            return;

        checkedVersion = true;

        if (string.IsNullOrEmpty(Application.version))
        {
            Debug.LogErrorFormat("Application Version is null {0} {1}", Application.productName, Application.installerName);
            return;
        }

        string version = PlayerPrefs.GetString("GameRootPackageVersion");
        if (string.IsNullOrEmpty(version) || version != Application.version)
        {
            bool needRestart = false;
            bool succ = true;
            try
            {
                needRestart = DelLuaCache();
            }
            catch (Exception e)
            {
                Debug.LogErrorFormat("[CheckPackageVersion]删除lua缓存失败 Message:{0} OldVersion:{1} NewVersion:{2} ProductName:{3}", e.Message, version, Application.version, Application.productName);
                succ = false;
            }

            if (succ)
            {
                PlayerPrefs.SetString("GameRootPackageVersion", Application.version);

                if (needRestart)
                {
                    Debug.LogErrorFormat("[CheckPackageVersion]换包了删除缓存重启 OldVersion:{0} NewVersion:{1} ProductName:{2}", version, Application.version, Application.productName);
                    if (null != GameRoot.Instance)
                        GameRoot.Instance.Restart();
                }
            }
        }
    }

    private static bool DelLuaCache()
    {
        string path = Path.Combine(Application.persistentDataPath, "BundleCache/LuaAssetBundle/LuaAssetBundle.lua");

        if (EncryptMgr.IsEncryptAsset())
        {
            path = Path.Combine(Application.persistentDataPath, EncryptMgr.GetEncryptPath("BundleCache/LuaAssetBundle/LuaAssetBundle.lua"));
        }

        if (File.Exists(path))
        {
            try
            {
                File.Delete(path);
            }
            catch (Exception e)
            {
                throw (e);
            }

            return true;
        }

        return false;
    }

    [Serializable]
    // 用接收加密数据
    public class ResponseStrData
    {
        public int code;
        public string msg;
        public string info;
        public string data;

        public static ResponseStrData DecodeJson(string json)
        {
            return JsonUtility.FromJson<ResponseStrData>(json);
        }
    }

    [Serializable]
    // 用接收明码数据
    public class ResponseData
    {
        public int code;
        public string msg;
        public string info;
        public QueryData data;

        public static ResponseData DecodeJson(string json)
        {
            return JsonUtility.FromJson<ResponseData>(json);
        }
    }

    [Serializable]
    public class QueryData
    {
        [Serializable]
        public class SwitchList
        {
            public bool open_fix = false;
        }

        [Serializable]
        public class ParamList
        {
            public string cdn_url = string.Empty;
            public string cdn_url2 = string.Empty;
            public SwitchList switch_list;
            public int res_encrypt_type;
            public string res_encrypt_key = string.Empty;
            public string res_base64_value = string.Empty;
        }

        public ParamList param_list;

        public static QueryData DecodeJson(string json)
        {
            return JsonUtility.FromJson<QueryData>(json);
        }

        public static bool IsEmpty(QueryData data)
        {
            return null == data || null == data.param_list || !data.param_list.switch_list.open_fix || string.IsNullOrEmpty(data.param_list.cdn_url);
        }
    }

    // 热更新的修复模块（注意别影响ios审核服）
    private static void SendFixRequest()
    {
#if !UNITY_EDITOR
        if (hasSendFixRequest)
            return;

        hasSendFixRequest = true;
        string url = GetFixQueryUrl();
        UtilU3d.RequestGet(url, OnFixRequestCallback);
#endif
    }

    public static string GetFixQueryUrl()
    {
        string initUrl = ChannelAgent.GetInitUrl();
        string queryUrl;
        if (!string.IsNullOrEmpty(initUrl))
        {
            //sdk可能会返回多个url，如果是审核状态，最后一条url为审核服url
            //为了不影响ios审核，固定取最后一条，当审核通过以后，sdk会把审核服url去掉的。
            string[] urls = initUrl.Split(',');
            queryUrl = urls[urls.Length - 1];
        }
        else
        {
            queryUrl = GetQueryUrl;
        }

        if (UnityEngine.Debug.isDebugBuild)
        {
            queryUrl = GetQueryUrl;
        }

        string plat_id = ChannelAgent.GetChannelID();
        string pkg_version = Application.version;
        string device = DeviceTool.GetDeviceID();
        string sign = string.Format("device={0}&pkg={1}&plat_id{2}&{3}", device, pkg_version, plat_id, GLOBAL_URL_SIGN_KEY);
        string md5Sign = MD52.GetMD5(sign);

        string url = string.Format("{0}?plat_id={1}&pkg={2}&device={3}&sign={4}", queryUrl, plat_id, pkg_version, device, md5Sign);
        return url;
    }

    private static void OnFixRequestCallback(bool succ, string data)
    {
        if (!succ)
        {
            return;
        }

        if (string.IsNullOrEmpty(data))
        {
            return;
        }

        QueryData queryData;
        //内网包不需要解密
        if (!GetFixQueryUrl().Contains(GetQueryUrl))
        {
            ResponseStrData responseData = ResponseStrData.DecodeJson(data);
            string decodeStr = DecodeJsonData(responseData.data, QueryURLBase64DecodeKey);
            queryData = QueryData.DecodeJson(decodeStr);
        }
        else
        {
            ResponseData responseData = ResponseData.DecodeJson(data);
            queryData = responseData.data;
        }

        if (QueryData.IsEmpty(queryData))
        {
            return;
        }

        // 从update_url与update_url2随机选一个
        string cdn_url = queryData.param_list.cdn_url;
        if (!string.IsNullOrEmpty(queryData.param_list.cdn_url2))
        {
            var rd = new System.Random();
            int index = rd.Next();
            if (index % 2 == 0)
                cdn_url = queryData.param_list.cdn_url2;
        }

        string fixUrl = string.Format("{0}/fixcode.lua", cdn_url);
        UtilU3d.RequestGet(fixUrl, (bool b, string s) => { OnFixCodeCallback(b, s, data); });
    }

    public static string DecodeJsonData(string data, string key)
    {
        byte[] key_b = Encoding.Default.GetBytes(key);
        int key_l = key_b.Length;
        byte[] data_deb64_b = Convert.FromBase64String(data);
        for (int i = 0; i < data_deb64_b.Length; i++)
        {
            data_deb64_b[i] = (byte)(data_deb64_b[i] ^ key_b[i % key_l]);
        }
        return Encoding.ASCII.GetString(data_deb64_b);
    }

    private static void OnFixCodeCallback(bool succ, string code, string queryData)
    {
        if (!succ)
        {
            return;
        }

        if (string.IsNullOrEmpty(code))
        {
            return;
        }

        if (null != GameRoot.Instance && null != GameRoot.Instance.LuaState)
        {
            try
            {
                GameRoot.Instance.luaState.LuaPushString(queryData);
                GameRoot.Instance.luaState.LuaSetGlobal("QueryData");
                GameRoot.Instance.luaState.DoString(code);
            }
            catch (Exception e)
            {
                Debug.LogErrorFormat("FixCode Error: {0}", e);
            }
        }
    }

#if UNITY_EDITOR
    private void OnApplicationQuit()
    {
        if (this.luaState != null)
        {
            if (this.luaStop != null)
            {
                this.luaStop.Call();
            }

            if (this.StopEvent != null)
            {
                this.StopEvent();
            }

            this.luaState.Dispose();
            this.luaState = null;
        }

        if (RuntimeGUIMgr.Instance.IsGUIOpening() || RuntimeGUIMgr.Instance.IsAndroidGM())
        {
            RuntimeGUIMgr.Instance.OnGameStop();
            RuntimeGUIMgr.Instance.OnApplicationQuit();
        }
    }
#endif

    [MonoPInvokeCallback(typeof(LuaCSFunction))]
    // private int AddLuaBundle(IntPtr l)
    private static int AddLuaBundle(IntPtr l)
    {
        int len;
        string luaFile = LuaDLL.luaL_checklstring(l, 1, out len);
        string bundleName = LuaDLL.luaL_checklstring(l, 2, out len);

        GameRoot.Instance.luaLoader.AddLuaBundle(luaFile, bundleName);

        return 0;
    }

    public static bool IsLuaFileExist(String path)
    {
        return GameRoot.Instance.luaLoader.IsLuaFileExist(path);
    }

    public static string GetAliasResPath(String path)
    {
        return GameRoot.Instance.luaLoader.GetAliasResPath(path);
    }

    private void LuaPlayAudio(string bundleName, string assetName)
    {
        this.luaPlayAudio.Call(bundleName, assetName);
    }

    public static void AddLuaWarning(string note, string level = "Low")
    {
#if UNITY_EDITOR
        CheckController.AddLuaWarning(note, level);
#endif
    }

    private void OpenLuaSocket()
    {
        LuaConst.openLuaSocket = true;
        this.luaState.BeginPreLoad();
        this.luaState.RegFunction("socket.core", LuaOpen_Socket_Core);
        this.luaState.RegFunction("mime.core", LuaOpen_Mime_Core);
        this.luaState.EndPreLoad();
    }

    private void OpenCJson()
    {
        this.luaState.LuaGetField(LuaIndexes.LUA_REGISTRYINDEX, "_LOADED");
        this.luaState.OpenLibs(LuaDLL.luaopen_cjson);
        this.luaState.LuaSetField(-2, "cjson");

        this.luaState.OpenLibs(LuaDLL.luaopen_cjson_safe);
        this.luaState.LuaSetField(-2, "cjson.safe");
    }

    private float lowMemoryTimer = 0;
    private void OnLowMemory()
    {
        Collectgarbage("collect");

        if (this.luaState != null)
        {
            this.luaState.Collect();
        }

#if !UNITY_EDITOR
        GC.Collect();
#endif

        // 防止频繁触发
        if (!(RuntimeGUIMgr.Instance.IsGUIOpening() && Debug.isDebugBuild) && isLowMemColllect && lowMemoryTimer + 60 <= Time.realtimeSinceStartup)
        {
            lowMemoryTimer = Time.realtimeSinceStartup;
            CollectMemoryInfo("LowMemory", true);
            PrintSceneMemoryLog(true, true);
        }
    }

    private static string CollectMemoryInfo(bool forceCollect)
    {
        string memoryInfo = TextureWindow.GetGraphicMemoryInfo(forceCollect);
        string luaMem = "0";
        if (null != GameRoot.Instance)
        {
            luaMem = string.Format("{0}MB", (GameRoot.Instance.Collectgarbage("count") / 1024).ToString("0.0"));
        }

        StringBuilder builder = new StringBuilder();
        builder.AppendFormat("游戏运行时间：{0}\n", Time.realtimeSinceStartup);
        var scene = SceneManager.GetActiveScene();
        if (null != scene)
        {
            builder.AppendFormat("当前Scene：{0}\t已进入场景时间:{1}\n", scene.name, Time.timeSinceLevelLoad);
        }
        builder.Append(memoryInfo);
        builder.Append("\n");
        builder.AppendFormat("Lua内存：{0}\n", luaMem);

        string totalReserver = (UnityEngine.Profiling.Profiler.GetTotalReservedMemoryLong() / 1024 / 1024).ToString("0.0");
        string totalAllocated = (UnityEngine.Profiling.Profiler.GetTotalAllocatedMemoryLong() / 1024 / 1024).ToString("0.0");
        string mono = (UnityEngine.Profiling.Profiler.GetMonoUsedSizeLong() / 1024 / 1024).ToString("0.0");
        string monoReserver = (UnityEngine.Profiling.Profiler.GetMonoHeapSizeLong() / 1024 / 1024).ToString("0.0");
        builder.AppendFormat("AllReserved:{0}MB Allocated:{1}MB Mono:{2}MB MonoReserved:{3}MB\n", totalReserver, totalAllocated, mono, monoReserver);

        string memInfo = builder.ToString();

        return memInfo;
    }

    public static void CollectMemoryInfo(string fileName, bool reportBugly)
    {
        if (isCollectGraphicMem)
        {
            DoCollectMemoryInfo(fileName, reportBugly);
        }
        else
        {
            isCollectGraphicMem = true;
            Scheduler.Delay(() => 
            {
                isCollectGraphicMem = false;
                DoCollectMemoryInfo(fileName, reportBugly);
            }, 1f);
        }
    }

    private static void DoCollectMemoryInfo(string fileName, bool reportBugly)
    {
        string memInfo = CollectMemoryInfo(false);
        //上报bugly
        if (reportBugly)
            Debug.LogError(memInfo);

        string directoryPath = Path.Combine(Application.persistentDataPath, "MemoryInfo");
        if (!Directory.Exists(directoryPath))
        {
            try
            {
                Directory.CreateDirectory(directoryPath);
            }
            catch (Exception e)
            {
                Debug.LogErrorFormat("[CollectMemoryInfo] Fail {0}", e.Message);
            }
        }

        System.DateTime now = System.DateTime.Now;
        string filePath = Path.Combine(directoryPath, string.Format("{0}_{1}.txt", fileName, now.ToLongTimeString().Replace(':', '_')));
        try
        {
            File.WriteAllText(filePath, memInfo);
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("[CollectMemoryInfo] Fail {0}", e.Message);
        }
    }

    [NoToLua]
    public void ExecuteGm(string gm)
    {
        if (null != this.luaExecuteGm)
        {
            this.luaExecuteGm.Call(gm);
        }
    }

    [NoToLua]
    public double Collectgarbage(string param)
    {
        if (null != this.luaCollectgarbage)
        {
            return this.luaCollectgarbage.Invoke<string, double>(param);
        }
        return 0;
    }

    [NoToLua]
    public int[] GetAllResources()
    {
        if (null != this.luaGetAllResources)
        {
            return this.luaGetAllResources.Invoke<int[]>();
        }

        return new int[0];
    }

    [NoToLua]
    public int[] GetAllGameObject()
    {
        if (null != this.luaGetAllGameObject)
        {
            return this.luaGetAllGameObject.Invoke<int[]>();
        }

        return new int[0];
    }

    [NoToLua]
    public string GetResTraceBack(int instanceID)
    {
        if (null != this.luaGetResTraceBack)
        {
            return this.luaGetResTraceBack.Invoke<int, string>(instanceID);
        }

        return string.Empty;
    }

    [NoToLua]
    public string[] GetAssetBundleLeak()
    {
        if (null != this.luaGetAssetBundleLeak)
        {
            return this.luaGetAssetBundleLeak.Invoke<string[]>();
        }

        return null;
    }

    public static void SetIsCollectGraphicMem(bool value)
    {
        isCollectGraphicMem = value;
        isShowGraphicWindow = value;
    }

    public static void SetIsLowMemColllect(bool value)
    {
        isLowMemColllect = value;
    }

    private static Queue<string> sceneLogQueue;
    public static void SetIsChangeSceneColllect(bool value, int maxCount)
    {
        isChangeSceneColllect = value;
        maxSceneLogCount = maxCount;
        SceneManager.activeSceneChanged -= OnLevelChanged;

        if (isChangeSceneColllect)
        {
            SceneManager.activeSceneChanged += OnLevelChanged;
            if (null == sceneLogQueue)
            {
                sceneLogQueue = new Queue<string>();
            }
            else
            {
                sceneLogQueue.Clear();
            }
        }
    }

    public static void PrintSceneMemoryLog(bool reportBugly, bool writeLocal)
    {
        if (null != sceneLogQueue)
        {
            StringBuilder builder = new StringBuilder();
            foreach (var log in sceneLogQueue)
            {
                builder.AppendFormat("{0}\n", log);
            }
            string memInfo = builder.ToString();

            if (reportBugly)
            {
                Debug.LogErrorFormat("ChangeSceneMemoryLog:{0}", memInfo);
            }

            if (writeLocal)
            {
                string directoryPath = Path.Combine(Application.persistentDataPath, "ChangeSceneMemoryInfo");
                if (!Directory.Exists(directoryPath))
                {
                    try
                    {
                        Directory.CreateDirectory(directoryPath);
                    }
                    catch (Exception e)
                    {
                        Debug.LogErrorFormat("[PrintSceneMemoryLog] Fail {0}", e.Message);
                    }
                }

                System.DateTime now = System.DateTime.Now;
                string filePath = Path.Combine(directoryPath, string.Format("SceneMemoryLog_{0}.txt", now.ToLongTimeString().Replace(':', '_')));
                try
                {
                    File.WriteAllText(filePath, memInfo);
                }
                catch (Exception e)
                {
                    Debug.LogErrorFormat("[PrintSceneMemoryLog] Fail {0}", e.Message);
                }
            }
        }
    }

    private static void OnLevelChanged(Scene oldScene, Scene newScene)
    {
        string oldSceneName = string.Empty;
        if (null != oldScene)
        {
            oldSceneName = oldScene.name;
        }

        string newSceneName = string.Empty;
        if (null != newScene)
        {
            newSceneName = newScene.name;

            if (newSceneName == "empytscene")
                return;
        }

        string info = CollectMemoryInfo(true);
        string sceneInfo = string.Format("{0} => {1}\n{2}", oldSceneName, newSceneName, info);
        sceneLogQueue.Enqueue(sceneInfo);
        if (sceneLogQueue.Count > maxSceneLogCount)
        {
            sceneLogQueue.Dequeue();
        }
    }

    public FPSSampler GetFPSSampler()
    {
        return gameObject.GetOrAddComponent<FPSSampler>();
    }

    public static double GetEditorTimeStamp()
    {
#if UNITY_EDITOR
        return UnityEditor.EditorApplication.timeSinceStartup;
#else
        return 0f;
#endif
    }

    public string GetDebugTrackback()
    {
        if (null != this.luaGetDebugTrackback)
        {
            return string.Format("{0}   {1}", Time.realtimeSinceStartup, this.luaGetDebugTrackback.Invoke<string>());
        }
        return string.Empty;
    }

    //这个接口用来热更指定配置表，慎用！！
    public void OverrideLuaBundle(string fileName, string bundleName)
    {
        luaLoader.OverrideLuaBundle(fileName, bundleName);
    }

    [NoToLua]
    public void LuaConsoleGm(string sz)
    {
        if (null != this.luaConsoleGm)
        {
            this.luaConsoleGm.Call(sz);
        }
    }

    [NoToLua]
    public int GetMainRoleLevel()
    {
        int level = 0;
        if (null != this.luaGetMainRoleLevel)
        {
            level = this.luaGetMainRoleLevel.Invoke<int>();
        }
        return level;
    }

    private void OnGUI()
    {
        if (isShowGraphicWindow)
            TextureWindow.OnGUI();

#if UNITY_EDITOR
        if (timer > Time.realtimeSinceStartup)
        {
            Rect area = new Rect(Screen.width / 2 - 100, Screen.height / 2 - 50, 200, 100);
            GUI.Box(area, "");
            GUI.Label(area, notice, MessageStyle.NoticeStyle);
        }
#endif

#if UNITY_STANDALONE
        RuntimeGUIMgr.Instance.OnGUI();
#else
        if (RuntimeGUIMgr.Instance.IsAndroidGM())
        {
            RuntimeGUIMgr.Instance.OnGUI();
        }
#endif

        if (DownloadBufferMgr.IsDebug)
        {
            DownloadBufferMgr.OnGUI();
        }
    }

#if UNITY_EDITOR
    [NoToLua]
    public void LuaCheckMemoryLeak()
    {
        if (null != this.luaCheckMemoryLeak)
        {
            this.luaCheckMemoryLeak.Call();
        }
    }

    private static string notice;
    private static float timer;
    [NoToLua]
    public static void ShowMessage(string str, float time)
    {
        notice = str;
        timer = Time.realtimeSinceStartup + time;
    }

    [NoToLua]
    public static class MessageStyle
    {
        public static GUIStyle NoticeStyle { get; private set; }
        static MessageStyle()
        {
            NoticeStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 40,
                alignment = TextAnchor.MiddleCenter,
                padding = new RectOffset(0, 0, 0, 0),
            };
        }
    }
#endif
}
