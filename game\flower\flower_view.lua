FlowerView = FlowerView or BaseClass(SafeBaseView)

local SHOW_TABBAR_POS = {{x = 0, y = 0}, {x = -14, y = -92}}

function FlowerView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(TabIndex.flower_upgrade, "uis/view/sendflower_ui_prefab", "layout_flower_upgrade")
    self:AddViewResource(TabIndex.flower_send, "uis/view/sendflower_ui_prefab", "layout_flower")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
    self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbarLight")

    self.is_checkvisible_onopen = true
    self.is_show_panel = false
    self.default_index = TabIndex.flower_upgrade

	self.remind_tab = {
		{ RemindName.Marry_Flowers },
        { RemindName.Marry_Send_Flowers},
	}
end

function FlowerView:__delete()
end

function FlowerView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end
    self:ReleaseFlowerSendPanel()
    self:DeleteFlowerUpgradeView()
    -- self.select_friend_callback = nil
    -- self.cell = nil
    -- self.select_data = nil
end

function FlowerView:LoadCallBack()
	self:InitMoneyBar()
	self.node_list.title_view_name.text.text = Language.Flower.TitleName

    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbarLight")
        self.tabbar:SetVerTabbarCellName("VerticalTabbarLightCell")
        self.tabbar:Init(Language.Flower.NameTable, nil, nil, nil, self.remind_tab)
        self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Flower, self.tabbar)
    end
end

function FlowerView:LoadIndexCallBack(index)
    if index == TabIndex.flower_send then
        self:InitFlowerSendPanel()
    elseif index == TabIndex.flower_upgrade then
        self:InitFlowerUpgradeView()
    end
end

function FlowerView:ShowIndexCallBack(index)
    local bg_name = "a3_zh_bj_1"
    if index == TabIndex.flower_send then
        bg_name = "a3_zh_bj_2"
    elseif index == TabIndex.flower_upgrade then
        bg_name = "a3_zh_bj_1"
        self.node_list.up_grade_success_effect:SetActive(false)
    end

	local bundle, asset = ResPath.GetRawImagesJPG(bg_name)
	XUI.SetNodeImage(self.node_list.RawImage_tongyong, bundle, asset)
end

function FlowerView:OpenCallBack()
    self.is_show_panel = false
end

function FlowerView:CloseCallBack()
    -- self.is_select = false
    -- self:SetFriendListShow(true)
end

function FlowerView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if "all" == k then
            if index == TabIndex.flower_upgrade then
                self:FlushFlowersView(param_t)
            elseif index == TabIndex.flower_send then
                self:FlushFlowerSendPanel(param_t)
            end
        elseif "send_flower_level_change" == k or "send_flower_num_change" == k then
			self:FlushFlowersView(k)
        end
    end
end

function FlowerView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")

		local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

---FriendListItemRender		好友列表
FriendListItemRender = FriendListItemRender or BaseClass(BaseRender)
function FriendListItemRender:__init()
end

function FriendListItemRender:__delete()

end

function FriendListItemRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list["lbl_friend_name"].text.text = self.data.gamename
end

-- SendFlowerItemRender		赠花列表
SendFlowerItemRender = SendFlowerItemRender or BaseClass(BaseRender)
-- 应该没什么用
local VER_POS = {
    [1] = {
        [1] = Vector2(-248, -113),
        [2] = Vector2(264, 146.2)
    },
    [2] = {
        [1] = Vector2(-60, -113),
        [2] = Vector2(212, 146.2)
    },
    [3] = {
        [1] = Vector2(124, -113),
        [2] = Vector2(41, 146.2)
    },
    [4] = {
        [1] = Vector2(309, -113),
        [2] = Vector2(-26, 146.2)
    }
}
function SendFlowerItemRender:__init()

end

function SendFlowerItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function SendFlowerItemRender:OnFlush()
    if nil == self.data then
        return
    end
    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    self.node_list["icon_bg"]:SetActive(item_num == 0)
    local cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
    local price = 0
    if cfg then
        price = cfg.price
    else
        print_error("取不到商城配置, seq =", self.data.seq)
    end
    self.node_list["lbl_price"].text.text = price
    self.node_list["cur_all_num"].text.text = string.format(Language.Flower.CurItemNum, item_num)
    local icon_name = "a2_zsxh_di" .. self.data.bottom_color
    self.node_list["img9_bg"].image:LoadSprite(ResPath.GetF2SendFlowerImagePath(icon_name))
    -- self.node_list["title_bg"].image:LoadSprite(ResPath.GetF2SendFlowerImagePath("namedi_"..self.data.bottom_color))
    if nil == self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["ph_cell"])
        self.item_cell:SetData({
            item_id = self.data.item_id
        })
    end
    local name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list["lbl_title_name"].text.text = name
    self.item_num = item_num > 999 and 999 or item_num
    -- self.node_list["cur_num"].text.text = self.item_num
    self:SetViewInfoShow()
end

function SendFlowerItemRender:OnSelectChange(is_on)
    if self.node_list["hight"] then
        self.node_list["hight"]:SetActive(is_on)
        self.node_list["hook"]:SetActive(is_on)
    end
end

function SendFlowerItemRender:SetViewInfoShow()
    local num = self.item_num > 0 and self.item_num or 1
    local meli = self.data.add_charm * num
    self.node_list["lbl_meli"].text.text = string.format(Language.Flower.AddMeLi, meli)
    local qinmidu = self.data.add_qinmi * num
    self.node_list["lbl_qimidu"].text.text = string.format(Language.Flower.AddQinMiDu, qinmidu)
end

function SendFlowerItemRender:GetSendFlowerInfo()
    local data = {}
    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    if item_num > 0 then
        local grid_index = ItemWGData.Instance:GetItemIndex(self.data.item_id)
        data.grid_index = grid_index
        data.item_num = self.item_num
        data.seq = self.data.seq
        data.need_buy = 0
        data.item_id = self.data.item_id
    else
        data.grid_index = -1
        data.item_num = 1
        data.seq = self.data.seq
        data.need_buy = 1
        data.item_id = self.data.item_id
    end
    return data
end

------------------------------------------------------------------------------------------