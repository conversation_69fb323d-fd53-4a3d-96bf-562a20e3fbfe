-----------------------------------
-- 百亿补贴
-----------------------------------
BillionSubsidyReceiveDCEffectView = BillionSubsidyReceiveDCEffectView or BaseClass(SafeBaseView)

function BillionSubsidyReceiveDCEffectView:__init()
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Window
	self.is_safe_area_adapter = true
	self.view_cache_time = 1
	self:SetMaskBg(false, true)

	self:AddViewResource(0, "uis/view/billion_subsidy_ui_prefab", "layout_billion_subsidy_receive_dc_effect")
end

function BillionSubsidyReceiveDCEffectView:LoadCallBack()

end

function BillionSubsidyReceiveDCEffectView:ReleaseCallBack()

end

function BillionSubsidyReceiveDCEffectView:SetDataAndOpen(callback)
	self.callback = callback
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function BillionSubsidyReceiveDCEffectView:CloseCallBack()
	self.callback = nil
end

function BillionSubsidyReceiveDCEffectView:OnFlush(param_t, index)
    self:PlayReceiveDCEffect(self.callback)
end

function BillionSubsidyReceiveDCEffectView:PlayReceiveDCEffect(callback)
    BillionSubsidyWGData.Instance:SetShowVipBlankTipsFlag(false)
    BillionSubsidyWGCtrl.Instance:SetVipBlankTipsShow(false)
    local bundle_name, asset_name = ResPath.GetEffectUi("UI_bybt_zhekoujuan")
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos1"].transform, 1.6, nil)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos2"].transform, 1.6, nil)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos3"].transform, 1.6, nil, nil, nil, nil, function ()
        self:Close()
        BillionSubsidyWGData.Instance:SetShowVipBlankTipsFlag(true)
        if callback then
            callback()
        end
    end)
end