﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_UI_ScrollRect_ScrollRectEventWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.UI.ScrollRect.ScrollRectEvent), typeof(UnityEngine.Events.UnityEvent<UnityEngine.Vector2>));
		<PERSON><PERSON>unction("New", _CreateUnityEngine_UI_ScrollRect_ScrollRectEvent);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_UI_ScrollRect_ScrollRectEvent(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.UI.ScrollRect.ScrollRectEvent obj = new UnityEngine.UI.ScrollRect.ScrollRectEvent();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.UI.ScrollRect.ScrollRectEvent.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

