require("game/operation_activity/operation_activity_first_recharge/activity_first_recharge_wg_data")

OperationFirstRechargeWGCtrl = OperationFirstRechargeWGCtrl or BaseClass(BaseWGCtrl)
function OperationFirstRechargeWGCtrl:__init()
	if OperationFirstRechargeWGCtrl.Instance then
		ErrorLog("[OperationFirstRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	OperationFirstRechargeWGCtrl.Instance = self
	self.data = OperationFirstRechargeWGData.New()
	self:RegisterAllProtocols()
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate(OperationFirstRechargeWGData.ConfigPath, BindTool.Bind(self.OnHotUpdate, self))
end

function OperationFirstRechargeWGCtrl:__delete()
	OperationFirstRechargeWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
	if self.FRdelay_timer then
		GlobalTimerQuest:CancelQuest(self.FRdelay_timer)
		self.FRdelay_timer = nil
	end
end

function OperationFirstRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOADayFirstRecharge, "OnSCOADayShouchong")
	self:RegisterProtocol(CSOADayFirstRechargeOpera)
end

function OperationFirstRechargeWGCtrl:OnSCOADayShouchong(protocol)
	self.data:SetFirstRechargeData(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_first_recharge)
	RemindManager.Instance:Fire(RemindName.OperationFirstRecharge)
end

function OperationFirstRechargeWGCtrl:SendDayShouChongReq(type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOADayFirstRechargeOpera)
	protocol.type = type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function OperationFirstRechargeWGCtrl:OnPassDay()
	--做个容错，跨天的时候，服务器的数据可能还没同步过来
	self.FRdelay_timer = GlobalTimerQuest:AddDelayTimer(function()
		OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_first_recharge)
		self.FRdelay_timer = nil
	end, 1)
	
end

--热更
function OperationFirstRechargeWGCtrl:OnHotUpdate()
	self.data:IntFirstRechargeData()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_first_recharge)
end