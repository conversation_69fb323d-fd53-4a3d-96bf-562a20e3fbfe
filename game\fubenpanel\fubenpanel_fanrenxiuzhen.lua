FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)
--百炼成神 (原凡人修真，天线阁， 读天线阁的配置)

local PAGE_DATA_COUNT = 10 --每页显示的个数
local IMAGE_COUNT = 12 	   --总图片个数

function FuBenPanelView:InitFanRenXiuZhen()
	self.cur_chapter = FuBenPanelWGData.Instance:GetChapter()
	self.max_chapter = FuBenPanelWGData.Instance:GetMaxChapterCount()
	-- self.is_first_enter_view = true
	self.item_render_list = {}

	XUI.AddClickEventListener(self.node_list.btn_rank, BindTool.Bind(self.OnClickXiuZhenRank, self))
	XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.OnClickGoToXiuZhen, self))
	XUI.AddClickEventListener(self.node_list.btn_fanrenxiuzhen_reward, BindTool.Bind(self.OnClickBtnFanrenxiuzhenReward, self))
	XUI.AddClickEventListener(self.node_list.btn_reward_pre, BindTool.Bind(self.OnClickBtnFanrenxiuzhenRewardPreview, self))
	
	--所有章节数据
	self.xiuxian_data_list = FuBenPanelWGData.Instance:GetFanRenXiuZhenCfgDataList()
	--章节格子列表
	self.chapter_cell_list = {}

	if self.fanrenxiuzhen_model_display == nil then
        self.fanrenxiuzhen_model_display = OperationActRender.New(self.node_list["fanrenxiuzhen_model_pos"])
    end

	--self.fanrenxiuzhen_first_pass_reward_list = AsyncListView.New(FanRenXiuZhenFirstPassRender, self.node_list["first_pass_item_list"])
	-- self.fanrenxiuzhen_first_pass_reward_list = {}
	-- for i =1,3 do
	-- 	self.fanrenxiuzhen_first_pass_reward_list[i] = {}
	-- 	self.fanrenxiuzhen_first_pass_reward_list[i].cell_pos = self.node_list["fanrenxiuzhen_first_pass_item"..i]
	-- 	self.fanrenxiuzhen_first_pass_reward_list[i].item_cell = FanRenXiuZhenFirstPassRender.New(self.node_list["fanrenxiuzhen_first_pass_item"..i])
	-- end

	--ListView
	local list_delegate = self.node_list["chapter_list"].list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.XiuZhenGetNumberOfCells, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.XiuZhenRefreshCell, self)
	--页面改变事件
	self.page_event = BindTool.Bind(self.OnJumpPageCallBack, self)
	local page_scroll = self.node_list["chapter_list"].list_page_scroll
	page_scroll.JumpToPageEvent = page_scroll.JumpToPageEvent + self.page_event
	page_scroll:SetPageCount(self:XiuZhenGetNumberOfCells())
	-- page_scroll:JumpToPageImmidate(self:XiuZhenGetNumberOfCells())
	-- self.node_list["chapter_list"].scroller:RefreshAndReloadActiveCellViews(true)
	self.node_list["chapter_list"].scroll_rect.enabled = false
	self.node_list["chapter_list"].scroller:RefreshActiveCellViews()
	self:OnJumpPageCallBack()

	self.node_list.desc_fanrenxiuzhen_ads.text.text = Language.FanRenXiuZhen.FanRenXiuZhenAds
	local other_cfg = FuBenPanelWGData.Instance:GetXiuZhenLuOtherCfg()
	self.node_list.desc_fanrenxiuzhen_fb.text.text = other_cfg and other_cfg.fb_des or ""
end

function FuBenPanelView:GetCurChapterPage()
	return self.cur_chapter
end

function FuBenPanelView:DoTXGFBTweenStart()
	self.tgx_start_tween = true
end

function FuBenPanelView:PreParePlayChapterCellListAnim()
	if self.node_list["chapter_list"] then
		local obj_transform = UITween.CanvasGroup(self.node_list["chapter_list"])
		obj_transform.alpha = 0
	end
end

function FuBenPanelView:PlayChapterCellListAnim()
	local obj_transform = UITween.CanvasGroup(self.node_list["chapter_list"])
	obj_transform.alpha = 1
	if self.chapter_cell_list then
		for k,v in pairs(self.chapter_cell_list) do
			if v:GetActive() then
				v:PositionAnimShow()
			end
		end
	end
end

function FuBenPanelView:DeleteFanRenXiuZhen()

	if self.fanrenxiuzhen_model_display then
		self.fanrenxiuzhen_model_display:DeleteMe()
		self.fanrenxiuzhen_model_display = nil
	end

	if self.drop_cell_list then
		for k,v in pairs(self.drop_cell_list) do
			v:DeleteMe()
		end
		self.drop_cell_list = nil
	end

	if self.chapter_cell_list then
		for k,v in pairs(self.chapter_cell_list) do
			v:DeleteMe()
		end
		self.chapter_cell_list = nil
	end

	if self.node_list["chapter_list"] then
		local page_scroll = self.node_list["chapter_list"].list_page_scroll
		if not IsNil(page_scroll) then
			page_scroll.JumpToPageEvent = page_scroll.JumpToPageEvent - self.page_event
		end
	end

	self.cur_chapter = 1
	self.page_event = nil
	self.xiuxian_data_list = nil
	self.is_first_enter_view = nil
	self.tgx_start_tween = nil

	-- if self.fanrenxiuzhen_first_pass_reward_list then
	-- 	for i =1 ,3 do
	-- 		self.fanrenxiuzhen_first_pass_reward_list[i].item_cell:DeleteMe()
	-- 	end
	-- 	self.fanrenxiuzhen_first_pass_reward_list = nil
	-- end
end

function FuBenPanelView:XiuZhenGetNumberOfCells()
	if self.xiuxian_data_list == nil then
		return 0
	end
	return 1--math.ceil(#self.xiuxian_data_list / PAGE_DATA_COUNT)
end

function FuBenPanelView:XiuZhenRefreshCell(cell, cell_index)
	local chapter_cell = self.chapter_cell_list[cell]
	local cache_index = cell_index
	cache_index = cache_index + 1
	if chapter_cell == nil then
		chapter_cell = FanRenChapterGroupRender.New(cell.gameObject)
		chapter_cell:SetIndex(cache_index)
		self.chapter_cell_list[cell] = chapter_cell
	end

	chapter_cell:SetData(self.xiuxian_data_list)
end

function FuBenPanelView:XiuZhenGetGuideBoxAndCallBack()
	local index = FuBenPanelWGData.Instance:GetFirstCanFetchBox()--self.first_can_fatch_chapter  获取第一个可以领的宝箱
	local cell
	for i, v in pairs(self.chapter_cell_list) do
		if v:GetIndex() == index then
			cell = v
		end
	end
	if cell then
		return cell:GetCell3()
	end
	return nil, nil
end

--页面跳转回调
function FuBenPanelView:OnJumpPageCallBack()
	if not self:IsLoadedIndex(TabIndex.fubenpanel_welkin) then return end
	-- self.node_list["chapter_list"].scroll_rect.enabled = true
	--local now_page = self.node_list["chapter_list"].list_page_scroll:GetNowPage() + 1
	--self.cur_chapter = now_page
	self:FlushOther()
	--刷新关卡首通格子
	-- self:FlushFirstPassCell()
	--刷新以显示格子
	self.node_list["chapter_list"].scroller:RefreshActiveCellViews()
end

--找到当前页两边是否有红点
function FuBenPanelView:CheckRemind(now_page, is_left)
	return false
end

function FuBenPanelView:OnFlushFanRenXiuZhenPanel(param_t)
	if not self:IsOpen() then return end

	if not param_t then
		self:XZNormalFlushAll() --正常刷新
	 	return
	end

	for k,v in pairs(param_t) do
		if k == "all" then
			self:XZNormalFlushAll()
		elseif k == "xz_fetch_result" then
			if v['xz_fetch_result'] then --领取宝箱刷新
				self:XZFlushFetchResult()
			end
		end
	end
end

--领取单个宝箱刷新
function FuBenPanelView:XZFlushFetchResult()
	self.node_list["chapter_list"].scroller:RefreshActiveCellViews()
	self:XZFlushRewardRemind()
	-- self:FlushFirstPassCell()
end

--刷新阶段奖励红点
function FuBenPanelView:XZFlushRewardRemind()
	self.node_list["fanrenxiuzhen_reward_remind"]:CustomSetActive(FuBenPanelWGData.Instance:CheckLevelReward())
end

-- 刷新阶段奖励显示
function FuBenPanelView:XZFlushLevelReawrd()
	local count = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdCount()
	local is_red , seq = FuBenPanelWGData.Instance:CheckLevelReward()
	local max_level = FuBenPanelWGData.Instance:GetFanrenxiuzhenMaxLevel()

	self.node_list["fanrenxiuzhen_reward_remind"]:CustomSetActive(is_red)
	local is_open = FunOpen.Instance:GetFunIsOpenedByMouduleName(GuideModuleName.FanRenXiuZhenRewardView)
	self.node_list["btn_fanrenxiuzhen_reward"]:CustomSetActive(seq ~= count and is_open)

	if seq ~= count then
		local cfg = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdDataBySeq(seq)
		self:XZFlushModel(cfg)

		self.node_list.text_reward_pre.text.text = string.format(Language.FanRenXiuZhen.RewardLevelStr2, cfg.level)
	end

	
end

function FuBenPanelView:XZFlushModel(show_model_data)
	local old_data = self.fanrenxiuzhen_model_display:GetData()
    if old_data and old_data.item_id == show_model_data.model_show_itemid then
        return
    end

    local data = {}
    data.item_id = show_model_data.model_show_itemid
    data.render_type = show_model_data.model_show_type - 1
    data.hide_model_block = true
	data.skip_rest_action = true
	data.can_drag = false
    -- if show_model_data.display_pos ~= nil and show_model_data.display_pos ~= "" then
    --     local pos = Split(show_model_data.display_pos, "|")
	-- 	RectTransform.SetAnchoredPositionXY(self.node_list.fanrenxiuzhen_model_pos.rect, pos[1], pos[2])
    --     -- data.position = Vector3(tonumber(pos[1]), tonumber(pos[2]), tonumber(pos[3]))
    -- end

    if show_model_data.display_scale ~= nil and show_model_data.display_scale ~= "" then
        data.model_adjust_root_local_scale = show_model_data.display_scale
    end

    if show_model_data.display_rotation ~= nil and show_model_data.display_rotation ~= "" then
        local rota = Split(show_model_data.display_rotation, "|")
        data.role_rotation = Vector3(tonumber(rota[1]), tonumber(rota[2]), tonumber(rota[3]))
    end

    self.fanrenxiuzhen_model_display:SetData(data)
end


--刷新全部
function FuBenPanelView:XZNormalFlushAll()
	self:FlushDropCell()
	-- self:FlushFirstPassCell()
	self:FlushCapability()
	self:FlushHistory()
	self:FlushOther()
	self:XZFlushLevelReawrd()

	local page = self:XiuZhenGetNumberOfCells()
	if page > 0 then
		--所有章节数据
		self.xiuxian_data_list = FuBenPanelWGData.Instance:GetFanRenXiuZhenCfgDataList()
		local page_count = self:XiuZhenGetNumberOfCells()
		self.node_list["chapter_list"].list_page_scroll:SetPageCount(page_count)
		self.node_list["chapter_list"].scroller:ReloadData(1)
		--引导的时候强行跳到第一章
		if FunctionGuide.Instance:GetIsGuide() then
			page_count = 1
			self:HandleJumpPageImmidate(page_count)
		end
	end

	local rank = FuBenPanelWGData.Instance:GetXiuZhenRank()
	if rank > 0 then
		self.node_list["my_rank"].text.text = string.format(Language.FanRenXiuZhen.MyRank,rank)
	else
		self.node_list["my_rank"].text.text = Language.FanRenXiuZhen.NoRank
	end
end


function FuBenPanelView:FlushHistory()
	local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
	local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(cur_level + 1)
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    if cfg and cfg.open_level > role_lv then
        local str = RoleWGData.GetLevelString(cfg.open_level)
        self.node_list.role_lv_limit.text.text = string.format(Language.FanRenXiuZhen.NotOpen, str)
		self.node_list.capability_group:SetActive(false)

    else
        self.node_list.role_lv_limit.text.text = ""
		self.node_list.capability_group:SetActive(true)
    end
	if IsEmptyTable(cfg) then
		self.node_list["tuijian_capability"].text.text = string.format(Language.FanRenXiuZhen.TuiJianCapability_Dark, "--")
		self.node_list.desc_zhangjie_name.text.text = Language.FanRenXiuZhen.FBLevelTitleName

	else

		local capability = cfg.capability
		--local color = GameVoManager.Instance:GetMainRoleVo().capability >= capability and COLOR3B.GREEN or COLOR3B.RED
		self.node_list["tuijian_capability"].text.text = string.format(Language.FanRenXiuZhen.TuiJianCapability_Dark, capability)
		self.node_list.desc_zhangjie_name.text.text = string.format(Language.FanRenXiuZhen.LevelStr, cfg.level)
	end

	local is_red , seq = FuBenPanelWGData.Instance:CheckLevelReward()
	local level_reward_cfg = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdDataBySeq(seq)
	self.node_list.btn_reward_pre:SetActive(is_red or not IsEmptyTable(level_reward_cfg))
end

--刷新概率掉落格子
function FuBenPanelView:FlushDropCell()
	local data_list = FuBenPanelWGData.Instance:GetFanRenXiuZhenRewardItemList(true)
	local cell_num = #data_list
	if not self.drop_cell_list then
		self.drop_cell_list = {}
		for i=1,#data_list do
			self.drop_cell_list[i] = ItemCell.New(self.node_list["drop_item_pos"..i])
			self.node_list["drop_item_pos"..i]:SetActive(true)
		end
	end

	for i=1,#data_list do
		if self.drop_cell_list[i] then
			self.drop_cell_list[i]:SetData(data_list[i])--{item_id = data_list[i].item_id, is_bind = data_list[i].is_bind, num = data_list[i].num}
		end
	end

	if not self.tgx_start_tween then
		return
	end

	self.tgx_start_tween = false
	GlobalTimerQuest:AddDelayTimer(function ()
		local pos_x = 45 * cell_num -5
		self.node_list.drop_item_content.rect.localPosition = u3dpool.vec2(pos_x,0)
	end,0)
end

--刷新首通格子
-- function FuBenPanelView:FlushFirstPassCell()
-- 	local data_list = FuBenPanelWGData.Instance:GetChapterRewardItemList(self.cur_chapter)
-- 	local have_reward = false
-- 	for i =1,3 do
-- 		if data_list[i] and data_list[i].item_id ~= 0 then
-- 			self.fanrenxiuzhen_first_pass_reward_list[i].item_cell:SetData(data_list[i])
-- 			self.fanrenxiuzhen_first_pass_reward_list[i].cell_pos:SetActive(true)
-- 			have_reward = true
-- 		else
-- 			self.fanrenxiuzhen_first_pass_reward_list[i].cell_pos:SetActive(false)
-- 		end

-- 	end
-- end

function FuBenPanelView:OnClickBtnFanrenxiuzhenReward()
	FuBenPanelWGCtrl.Instance:OpenFanRenXiuZhenRewardView()
end

function FuBenPanelView:OnClickBtnFanrenxiuzhenRewardPreview()
	local is_red , seq = FuBenPanelWGData.Instance:CheckLevelReward()
	local index = FuBenPanelWGData.Instance:GetFanrenxiuzhenRewardGroupSeq(seq)
	FuBenPanelWGCtrl.Instance:OpenFanRenXiuZhenRewardPreview(index)
end



--刷新我的战斗力
function FuBenPanelView:FlushCapability()
	if self.node_list["my_capability"] and self.node_list["chapter_list"] then
		local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
		local cur_capability = GameVoManager.Instance:GetMainRoleVo().capability
		local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(cur_level + 1)
		if IsEmptyTable(cfg) or cur_capability >= cfg.capability then
			self.node_list["my_capability"].text.text = Language.FanRenXiuZhen.MyCapability .. ToColorStr(cur_capability,IS_CAN_COLOR.ENOUGH)
		else
			self.node_list["my_capability"].text.text = Language.FanRenXiuZhen.MyCapability .. ToColorStr(cur_capability,IS_CAN_COLOR.NOT_ENOUGH)
		end
		-- self.node_list["chapter_list"].scroller:RefreshAndReloadActiveCellViews(true)
		self.node_list["chapter_list"].scroller:RefreshActiveCellViews()
	end
end

--刷新Ohter
function FuBenPanelView:FlushOther()
	-- local cur_level_list = FuBenPanelWGData.Instance:GetChapterRewardItemList(self.cur_chapter)
	local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
	local max_level = FuBenPanelWGData.Instance:GetCfgMaxLevel()
	-- self.node_list.reward_text1.text.text = string.format(Language.FuBenPanel.TongGuanNum,cur_level + 1)..Language.FuBenPanel.JiangLi
	-- self.node_list.reward_text2.text.text = string.format(Language.FuBenPanel.TongGuanNum,cur_level_list[1].level)
	if cur_level == max_level then
		self.node_list.reward_bg:SetActive(false)
		self.node_list.reward_bg1:SetActive(false)
		self.node_list.btn_goto:SetActive(false)
		self.node_list.yitonguan:SetActive(true)
	end
end


--点击排行(试炼榜)
function FuBenPanelView:OnClickXiuZhenRank()
	ViewManager.Instance:Open(GuideModuleName.Rank, TabIndex.paihangbang, "select_rank_type", {rank_type = PersonRankType.TianXianGeRank})
end

--点击挑战 
function FuBenPanelView:OnClickGoToXiuZhen(is_guide)
	local level = FuBenPanelWGData.Instance:GetPassLevel()
	local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
	if level >= max_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FbWushuang.MaxLevelTips)
		return
	end

    local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(level + 1)
    if IsEmptyTable(cfg) then return end
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    if cfg.open_level > role_lv then
        local str = RoleWGData.GetLevelString(cfg.open_level)
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FanRenXiuZhen.NotOpen, str))
        return
    end

	if GameVoManager.Instance:GetMainRoleVo().capability < cfg.capability then
		local data = {}
		data.level = level
		data.capability = cfg.capability
		FuBenPanelWGCtrl.Instance:OpenChallengeTip(data)
	else
		local function callback()
			self:RealEnterFanRenXiuZhen(is_guide)
		end
		OperateFrequency.Operate(callback, EnumOperateFrequency.EnterFB_FanRenXiuZhen, 3)
	end
end

function FuBenPanelView:RealEnterFanRenXiuZhen(is_guide)
	FuBenWGData.Instance:SetTXGEnter(true)
	FuBenWGCtrl.Instance:SendEnterWelkinFb(is_guide)
end

--点击左
function FuBenPanelView:OnClickLeft()
	if self.click_cd then
		return
	end

	local now_page = self.cur_chapter
	if now_page <= 1 then
		return
	end
	now_page = now_page - 1
	self:HandleJumpPage(now_page)
end

--点击右
function FuBenPanelView:OnClickRight()
	if self.click_cd then
		return
	end

	local now_page = self.cur_chapter
	if now_page >= self.max_chapter then
		return
	end
	now_page = now_page + 1
	local chapter = FuBenPanelWGData.Instance:GetChapter()
	if now_page > chapter then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FanRenXiuZhen.ClickRightTip)
		return
	end

	self:HandleJumpPage(now_page)
end

function FuBenPanelView:HandleJumpPage(now_page)
	if not self.node_list["chapter_list"].scroller.isActiveAndEnabled then
		return
	end
	-- self.node_list["chapter_list"].scroll_rect.enabled = false
	self.cur_chapter = now_page
	self.node_list["chapter_list"].list_page_scroll:JumpToPage(now_page - 1 )

	self.click_cd = true
	GlobalTimerQuest:AddDelayTimer(function()
		self.click_cd = false
	end, 0.3)
end

function FuBenPanelView:HandleJumpPageImmidate(now_page)
	if not self.node_list["chapter_list"] or not self.node_list["chapter_list"].list_page_scroll
		or not self.node_list["chapter_list"].list_page_scroll.isActiveAndEnabled
		or not self.node_list["chapter_list"].list_page_scroll.JumpToPageImmidate then
		return
	end
	-- self.node_list["chapter_list"].scroll_rect.enabled = false
	local init_flag = false
	for k,v in pairs(self.chapter_cell_list) do
		init_flag = true
		break
	end
	if not init_flag then
		GlobalTimerQuest:AddDelayTimer(function ()
			self:HandleJumpPageImmidate(now_page)
		end,0)
		return
	end
	self.cur_chapter = now_page
	self.node_list["chapter_list"].list_page_scroll:JumpToPageImmidate(now_page - 1 )
end

-- 这个函数屏蔽了 有空可以把相关的删掉
function FuBenPanelView:OnClickFanRenXiuZhenRewardBtn()
	local data_list = FuBenPanelWGData.Instance:GetChapterRewardItemList(self.cur_chapter)
	
	if IsEmptyTable(data_list) then
		return 
	end

	local have_reward = false
	local reward_data_list = {}

	for k, v in pairs(data_list) do
		if v.item_id and v.item_id > 0 then
			have_reward = true
			table.insert(reward_data_list, {item_id = v.item_id, is_bind = v.is_bind, num = v.num, open_slot = v.open_slot, index = v.index})
		end
	end

	if have_reward then
		local cur_level_list = FuBenPanelWGData.Instance:GetChapterRewardItemList(self.cur_chapter)
		local level = cur_level_list[1].level
		local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(level)
		local title_str = ""

		if not IsEmptyTable(cfg) then
			title_str = string.format(Language.FuBenPanel.TongGuanRewardShowDesc1, cfg.title_name)
		else
			title_str = string.format(Language.FuBenPanel.TongGuanRewardShowDesc, level)
		end

		FuBenPanelWGCtrl.Instance:OpenFanRenXiuZhenRewardShowTip({title = title_str, reward_data_list = reward_data_list})
	end
end



----------------------------------------------------------------------------

FanRenChapterGroupRender = FanRenChapterGroupRender or BaseClass(BaseRender)
function FanRenChapterGroupRender:__init()
	self.cell_list = {}
	self.data = nil
	for i = 1, PAGE_DATA_COUNT do
		self.cell_list[i] = FanRenXiuZhenRender.New(self.node_list["level_render_" .. i])
	end
end
function FanRenChapterGroupRender:__delete()
	for k,v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
	self.data = nil
end
function FanRenChapterGroupRender:SetData(data)
	self.data = data
	if #self.cell_list < PAGE_DATA_COUNT then return end
	if not self.data then
	 	return
	end
	--背景
	local image_id = tonumber(self.data[1].chapter)
	image_id = image_id % IMAGE_COUNT
	image_id = image_id == 0 and IMAGE_COUNT or image_id
	--local bundle,asset = ResPath.GetF2RawImagesPNG("fb_xiuzhen"..image_id)
	--self.node_list["big_bg"].raw_image:LoadSprite(bundle, asset)
	for k,v in pairs(self.cell_list) do
		v:SetData(data[k])
		v:SetActive(data[k] ~= nil)
	end
	-- self:PositionAnimShow()

	local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
	local pos_flag = cur_level % 10 >=6 and 1 or 0
	self.node_list.scroll_rect.scroll_rect.horizontalNormalizedPosition = pos_flag
end

-- local index_group = {1, 2, 3, 7, 8}
-- local index_group2 = {4, 5, 6, 9, 10}

function FanRenChapterGroupRender:PositionAnimShow()
	-- local tween_time = 0.6

	-- for key, value in pairs(index_group) do
	-- 	UITween.MoveShowPanel(self.node_list["level_render_" .. value],Vector3(0, 62),tween_time)
	-- end
	-- for key, value in pairs(index_group2) do
	-- 	UITween.MoveShowPanel(self.node_list["level_render_" .. value],Vector3(0, 62),tween_time)
	-- end
end

function FanRenChapterGroupRender:GetCell3()
	local _,sub_level_index = FuBenPanelWGData.Instance:GetFirstCanFetchBox()
	if self.cell_list and self.cell_list[sub_level_index] then
		local node = self.cell_list[sub_level_index]:GetBaoXiangNode()
		local callback = self.cell_list[sub_level_index]:GetBaoXiangClickCallBack()
		return node, callback
	end
	return nil, nil
end

-----------------------FanRenXiuZhenRender-------------------------------

FanRenXiuZhenRender = FanRenXiuZhenRender or BaseClass(BaseRender)
function FanRenXiuZhenRender:__init()

end

function FanRenXiuZhenRender:__delete()
	self.load_finish = false
end


function FanRenXiuZhenRender:GetBaoXiangNode()
	return self.node_list and self.node_list["baoxiang"]
end

function FanRenXiuZhenRender:GetBaoXiangClickCallBack()
	return BindTool.Bind(self.OnClickBaoXiang, self)
end

function FanRenXiuZhenRender:OnFlush()
	if self.data == nil then
		return
	end

    local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
    local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
	local is_cur = (cur_level + 1) == self.data.level

	-- local name_str = is_cur and ToColorStr(monster_cfg.name, "#FFF8BB") or monster_cfg.name
    self.node_list.name.text.text = monster_cfg.name
	self.node_list.hl_name.text.text = ToColorStr(monster_cfg.name, "#FFF8BB")

	-- self.node_list.is_pass:SetActive((cur_level + 1) > self.data.level)  -- 通过标记
	-- self.node_list.text_pass:SetActive((cur_level + 1) > self.data.level)
	-- self.node_list["effect"]:SetActive(is_cur)

	self.node_list.bg:CustomSetActive(not is_cur)
	self.node_list.bg_hl:CustomSetActive(is_cur)

	-- self.node_list.text_cur_level:SetActive(is_cur)
	-- self.node_list.text_cur_level.text.text = string.format(Language.FanRenXiuZhen.History, cur_level + 1)
	-- self.node_list.img_mask:SetActive((cur_level + 1) < self.data.level)
	XUI.SetGraphicGrey(self.node_list.level_render_root, (cur_level + 1) < self.data.level)
end




-----------------------------凡人修真宝箱弹框-----------------------------------
FanRenXiuZhenBoxTips = FanRenXiuZhenBoxTips or BaseClass(SafeBaseView)
function FanRenXiuZhenBoxTips:__init()
	self:SetMaskBg(false, true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_xiuzhen_box_tip")
end
function FanRenXiuZhenBoxTips:__delete()

end

function FanRenXiuZhenBoxTips:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FanRenXiuZhen.ChallageTipsTitle
	self:SetSecondView(u3dpool.vec2(782, 420))
	-- self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
 --    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
 --    self.node_list["title_view_name"].text.text = Language.FanRenXiuZhen.ChallageTipTitle
	XUI.AddClickEventListener(self.node_list["btn_sure"], BindTool.Bind(self.OnClickSure, self))
	XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind(self.OnClickSure, self))
	--self.item_list = {}
	--for i=1,3 do
	--	self.item_list[i] = ItemCell.New(self.node_list["cell_pos"..i])
	--end

	self.item_list = AsyncListView.New(FanRenXiuZhenBoxRender, self.node_list["cell_root"])
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.FuBenPanel, self.get_guide_ui_event)
end

function FanRenXiuZhenBoxTips:ReleaseCallBack()
	self.data = nil
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	self.click_cd = nil
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.FuBenPanel, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
end
function FanRenXiuZhenBoxTips:SetData(data)
	self.data = data
	self:Open()
	self:Flush()
end

function FanRenXiuZhenBoxTips:OnFlush()
	if not self.data then return end
	local chapter = FuBenPanelWGCtrl.Instance:GetCurChapterPage()
	local data_list = FuBenPanelWGData.Instance:GetBoxRewardItemList(chapter, self.data.level)
	local list = {}
	for i, v in ipairs(data_list) do
		local data = {}
		data.cfg = v
		data.has_fetch = self.data.has_fetch
		table.insert(list, data)
	end
	self.item_list:SetDataList(list)

	self.node_list["btn_cancel"]:SetActive(not self.data.can_fetch)
	self.node_list["btn_sure"]:SetActive(self.data.can_fetch)

	local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(self.data.level)
	local layer = FuBenPanelWGData.Instance:GetXiuXianLayer(self.data.level)
	self.node_list["content"].text.text = string.format(Language.FanRenXiuZhen.FirstPassLevel, cfg and cfg.title_name or "", layer)
end

function FanRenXiuZhenBoxTips:OnClickSure()
	self:Close()
	if self.data.can_fetch then
		--请求领取
		FuBenPanelWGCtrl.Instance:SendLevelFetchReq(self.data.level)
	end
end

function FanRenXiuZhenBoxTips:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.FRXZRewardPanel then
		return self.node_list["cell_root"]
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end



FanRenXiuZhenBoxRender = FanRenXiuZhenBoxRender or BaseClass(BaseRender)
function FanRenXiuZhenBoxRender:__init()
	self.cell = ItemCell.New(self.node_list["pos"])
end
function FanRenXiuZhenBoxRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end
function FanRenXiuZhenBoxRender:OnFlush()
	self.cell:SetData(self.data.cfg)
	self.node_list.fetch_flag:SetActive(self.data.has_fetch)
end

-----------------------------凡人修真挑战弹框-----------------------------------
FanRenXiuZhenChallengeTips = FanRenXiuZhenChallengeTips or BaseClass(SafeBaseView)
function FanRenXiuZhenChallengeTips:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_xiuzhen_tip")
end

function FanRenXiuZhenChallengeTips:__delete()
end

function FanRenXiuZhenChallengeTips:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FanRenXiuZhen.ChallageTipTitle
	XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind(self.OnClickCancel, self))
	XUI.AddClickEventListener(self.node_list["btn_sure"], BindTool.Bind(self.OnClickSure, self))
end

function FanRenXiuZhenChallengeTips:ReleaseCallBack()
	self.data = nil
end

function FanRenXiuZhenChallengeTips:SetData(data)
	self.data = data
	self:Open()
	self:Flush()
end

function FanRenXiuZhenChallengeTips:OnFlush()
	if not self.data then return end
	if self.node_list["content"] then
		self.node_list["content"].text.text = string.format(Language.FanRenXiuZhen.ChallageTip, self.data.capability)
	end
end

function FanRenXiuZhenChallengeTips:OnClickCancel()
    FuBenWGCtrl.Instance:SendLeaveFB()
	self:Close()
end

--确认挑战
function FanRenXiuZhenChallengeTips:OnClickSure()
	self:Close()
	if self.data.enter_func ~= nil then
		self.data.enter_func()
		self.data.enter_func = nil
		return
	end
	FuBenWGData.Instance:SetTXGEnter(true)
	FuBenWGCtrl.Instance:SendEnterWelkinFb()
end



--------------------------章节奖励格子-----------------------------------
ChapterRewardItem = ChapterRewardItem or BaseClass(ItemCell)
function ChapterRewardItem:__init()

end
function ChapterRewardItem:OnClick()
	local chapter = FuBenPanelWGCtrl.Instance:GetCurChapterPage()
	local can_fetch = FuBenPanelWGData.Instance:GetChapterCanFetch(chapter)
	if can_fetch then
		FuBenPanelWGCtrl.Instance:SendChapterFetchReq(chapter)
	else
		ItemCell.OnClick(self)
	end
end

FanRenXiuZhenFirstPassRender = FanRenXiuZhenFirstPassRender or BaseClass(BaseRender)
function FanRenXiuZhenFirstPassRender:__init()
	--self.base_cell = ItemCell.New(self.node_list["pos"])
    XUI.AddClickEventListener(self.node_list["effect"], BindTool.Bind(self.OnClickFetch, self))
end

function FanRenXiuZhenFirstPassRender:__delete()
	--if self.base_cell then
	--	self.base_cell:DeleteMe()
	--	self.base_cell = nil
	--end
end

function FanRenXiuZhenFirstPassRender:OnFlush()
	if not self.data or self.data.item_id == 0 then return end
	--self.base_cell:SetData(self.data)
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
 	local bundle, asset = ResPath.GetItem(cfg.icon_id)
 	self.node_list["pos"].image:LoadSpriteAsync(bundle, asset,function ()
		-- self.node_list["pos"].image:SetNativeSize()
	end)

	local bg_name = "a2_zw_ptdi"
	local node_root = self.node_list["bg"]
	self.node_list["bg"]:CustomSetActive(false)
	self.node_list["item_bg"]:CustomSetActive(false)
	local open_slot = {}
	open_slot[3] = tonumber(self.data.open_slot) % 10
	open_slot[2] = math.floor(tonumber(self.data.open_slot) / 10) % 10
	open_slot[1] = math.floor(tonumber(self.data.open_slot) / 100) % 10
 	if open_slot[self.data.index] == 1 then
 		self.node_list["pass_text1"].text.text = Language.MingWenView.NewMingSolt
 	elseif open_slot[self.data.index] == 3 then
 		bg_name = "a3_ty_wpk_" .. cfg.color
		self.node_list["pass_text1"].text.text = Language.MingWenView.NewTuJianType
		node_root = self.node_list["item_bg"]
		node_root:CustomSetActive(true)
 	elseif open_slot[self.data.index] == 2 then
 		self.node_list["pass_text1"].text.text = Language.MingWenView.NewMingType
		node_root:CustomSetActive(true)
 	end

 	if node_root then
 		local bg_bundle, bg_asset = ResPath.GetCommonImages(bg_name)
		node_root.image:LoadSpriteAsync(bg_bundle, bg_asset,function ()
			--self.node_list["bg"].image:SetNativeSize()
		end)
 	end
end

function FanRenXiuZhenFirstPassRender:OnClickFetch()
	if IsEmptyTable(self.data) then
			return
	end

	local show_data = {item_id = self.data.item_id, is_bind = self.data.is_bind, num = self.data.num, level = 1}
	TipWGCtrl.Instance:OpenItem(show_data)
	--local chapter = FuBenPanelWGCtrl.Instance:GetCurChapterPage()
	--local can_fetch = FuBenPanelWGData.Instance:GetChapterCanFetch(chapter)
	--if can_fetch then
	--	FuBenPanelWGCtrl.Instance:SendChapterFetchReq(chapter)
	--end
end

------------------------------- FanRenXiuZhenRewardShowTip ----------------------------------
FanRenXiuZhenRewardShowTip = FanRenXiuZhenRewardShowTip or BaseClass(SafeBaseView)

function FanRenXiuZhenRewardShowTip:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fanrenxiuzhen_reward_show_tip")
end

function FanRenXiuZhenRewardShowTip:SetDataAndOpen(data)
	if IsEmptyTable(data) then
		return
	end
	
	self.show_data = data
	if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function FanRenXiuZhenRewardShowTip:LoadCallBack()
	if not self.chapter_list then
		self.chapter_list = AsyncListView.New(FanRenXiuZhenRewardItemCellRender, self.node_list.chapter_list)
	end

	self.node_list.title_view_name.text.text = Language.FuBenPanel.TongGuanRewardShowTipTitle
	XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind(self.Close, self))
end

function FanRenXiuZhenRewardShowTip:ReleaseCallBack()
	if self.chapter_list then
		self.chapter_list:DeleteMe()
		self.chapter_list = nil
	end
end

function FanRenXiuZhenRewardShowTip:OnFlush()
	if IsEmptyTable(self.show_data) then
		return
	end
	
	self.node_list.desc_reward_content.text.text = self.show_data.title
	self.chapter_list:SetDataList(self.show_data.reward_data_list)
end

FanRenXiuZhenRewardItemCellRender = FanRenXiuZhenRewardItemCellRender or BaseClass(BaseRender)

function FanRenXiuZhenRewardItemCellRender:LoadCallBack()
	if not self.reward_item then
		self.reward_item = ItemCell.New(self.node_list.cell_pos)
	end
end

function FanRenXiuZhenRewardItemCellRender:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function FanRenXiuZhenRewardItemCellRender:OnFlush()
	if not self.data then
		return
	end

	self.reward_item:SetData(self.data)
	
	local open_slot = {}
	open_slot[3] = tonumber(self.data.open_slot) % 10
	open_slot[2] = math.floor(tonumber(self.data.open_slot) / 10) % 10
	open_slot[1] = math.floor(tonumber(self.data.open_slot) / 100) % 10
 	if open_slot[self.data.index] == 1 then
 		self.node_list["text"].text.text = Language.MingWenView.NewMingSolt
 	elseif open_slot[self.data.index] == 3 then
		self.node_list["text"].text.text = Language.MingWenView.NewTuJianType
 	elseif open_slot[self.data.index] == 2 then
 		self.node_list["text"].text.text = Language.MingWenView.NewMingType
	else
		self.node_list["text"].text.text = ""
 	end
end