WeddingSceneLogic = WeddingSceneLogic or BaseClass(CommonFbLogic)

function WeddingSceneLogic:__init()
	CommonFbLogic.__init(self)
	self.is_show = false
end

function WeddingSceneLogic:__delete()
	self.before_act_mode = nil
end

function WeddingSceneLogic:Enter(old_scene_type, new_scene_type)
	--print_error("进入副本")
	local callback = function ()
		local mcl = MainuiWGCtrl.Instance
		mcl:SetSkillShowState(false)
		mcl:SetButtonModeClick(false)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil, callback)
	self.is_show = false
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self:InitGatherFlag()
	local wedding_ctrl = WeddingWGCtrl.Instance
	wedding_ctrl:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_WEDDING_INFO)
	wedding_ctrl:OnShow(self.is_show)
	wedding_ctrl:WeddingSendGiftViewOpen()
	wedding_ctrl:WeddingSendGiftProgressViewOpen()
	wedding_ctrl:RemoveAllInviteTip()
	wedding_ctrl:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_WEDDING_ROLE_INFO)
	wedding_ctrl:WeddingDeMandViewClose()
	MarryWGCtrl.Instance:OpenHunYanTask()
	MainuiWGCtrl.Instance:SetFBNameState(true, Language.Marry.WeddingTitle)
	MainuiWGCtrl.Instance:SetTeamBtnState(false)
	MainuiWGCtrl.Instance:SetTaskContents(false)
    MainuiWGCtrl.Instance:SetOtherContents(true)
	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)

	if nil == self.guaji_hy_global_event then
		self.onguaji_event = BindTool.Bind(self.OnGuaJiStatyChange, self)
		self.guaji_hy_global_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, self.onguaji_event)
	end
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	if activity_status then
		FuBenWGCtrl.Instance:SetOutFbTime(activity_status.next_time)
	end
	-- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	-- local main_role = Scene.Instance:GetMainRole()
    -- self.before_act_mode = main_role:GetVo().attack_mode

    -- CgManager.Instance:PreloadCacheCg("cg/f2_cg_jiehun_prefab", "F2_CG_jiehun")
end

function WeddingSceneLogic:Out()
	local callback = function ()
		local mcl = MainuiWGCtrl.Instance
		mcl:SetSkillShowState(true)
		mcl:SetButtonModeClick(true)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil, callback)
	self:InitGatherFlag()
	ViewManager.Instance:CloseAll()
	CommonFbLogic.Out(self)
	WeddingWGCtrl.Instance:RemoveInvalidHunyanInfo()
	WeddingWGCtrl.Instance:WeddingAllViewClose()
	MarryWGCtrl.Instance:CloseHunYanTask()
	local main_uictrl = MainuiWGCtrl.Instance
	main_uictrl:SetFBNameState(false)
	main_uictrl:SetTeamBtnState(true)
    main_uictrl:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	if self.hunyan_ui then
		self.hunyan_ui:setVisible(false)
	end

	if CgManager.Instance:IsCgIng() then
		CgManager.Instance:Stop()
	end
    CgManager.Instance:DelCacheCgs()
	-- if self.before_act_mode ~= nil then
	-- 	main_uictrl:SendSetAttackMode(self.before_act_mode)
	-- end
	if self.guaji_hy_global_event then
		GlobalEventSystem:UnBind(self.guaji_hy_global_event)
		self.guaji_hy_global_event = nil
	end
	MarryWGData.Instance:RemberGatnId()
	FuBenPanelCountDown.Instance:CloseViewHandler()
end

function WeddingSceneLogic:InitGatherFlag()
	self.is_not_goto_gather = true
	self.is_not_in_gather = true
end

function WeddingSceneLogic:OnGuaJiStatyChange( guaji_type )
	if guaji_type == GuajiType.None then
		self.is_not_goto_gather = true
		self.cur_gather_obj = nil
		local main_role = Scene.Instance:GetMainRole()
		if nil == main_role then
			return
		end
		main_role:StopMove()
	end
end

function WeddingSceneLogic:CreateButton()
	if nil == self.hunyan_ui then
		local zodaer =  WeddingWGCtrl.Instance.send_gift_view and WeddingWGCtrl.Instance.send_gift_view:GetLocalZOrder() or 0
		HandleRenderUnit:AddUi(self.hunyan_ui, zodaer + 1,zodaer + 1)
	end
end

function WeddingSceneLogic:OnClickHeadHandler(is_show)
	CommonFbLogic.OnClickHeadHandler(self, is_show)
end

function WeddingSceneLogic:GetRoleNameBoardText(role_vo)
	local t = {}
	t.color = role_vo.special_param == 1 and COLOR3B.WHITE or COLOR3B.WHITE --F540F1
	t.text = role_vo.name
	return t
end

-- 获取角色仙盟名
function WeddingSceneLogic:GetGuildNameBoardText(role_vo)
	local t = {}
	local index = 1
	local guild_name = role_vo.guild_name or ""
	local guild_post = role_vo.guild_post

	if "" == guild_name then return t end
	guild_name = "【" .. guild_name .. "】"
	local authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[guild_post]
	local post_name = authority and authority.post or ""

	t[index] = {}
	t[index].color = role_vo.special_param == 1 and COLOR3B.WHITE or COLOR3B.WHITE --F540F1
	t[index].text = guild_name .. COMMON_CONSTS.POINT
	index = index + 1

	t[index] = {}
	t[index].color = role_vo.special_param == 1 and COLOR3B.WHITE or COLOR3B.WHITE --F540F1
	t[index].text = post_name

	return t
end

local elaspe_t = 0

function WeddingSceneLogic:Update( now_time, elapse_time)
	BaseSceneLogic.Update(self,now_time,elapse_time)
	if elaspe_t + 0.5 > now_time then
		return
	end

	elaspe_t = now_time
	local scene_ins = Scene.Instance
	if GuajiCache.guaji_type == GuajiType.Auto then
		local main_role = scene_ins:GetMainRole()
		if nil == main_role then
			return
		end
		if main_role:GetIsGatherState() then
			self.is_not_goto_gather = true
			self.is_not_in_gather = false
		else
			self.is_not_in_gather = true
		end
		local cur_gather_id = MarryWGData.Instance:GetRemberGatnId()
		if nil == cur_gather_id then
			local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo() or {}
			if weeding_info and not IsEmptyTable(weeding_info) then
				local red_id, red_max, jiuxi_id, jiuxi_max, today_jiuxi_num = MarryWGData.Instance:GetWeedingSceneCfg()
				if red_id and red_max and jiuxi_id and jiuxi_max and today_jiuxi_num then
					local can_gather_jiuxi = weeding_info.has_gather_num < tonumber(jiuxi_max) --是否可以继续采集酒席
					local can_gather_red_bag = weeding_info.has_gather_red_bag < red_max 		--是否可以继续采集喜酒/糖果
					if can_gather_jiuxi then
						MarryWGData.Instance:RemberGatnId(jiuxi_id)
					elseif can_gather_red_bag then
						MarryWGData.Instance:RemberGatnId(red_id)
					end
				end
			end
		end
		if self.is_not_in_gather and self.is_not_goto_gather then
			local obj = self:GetGuajiGather()
			if nil == obj then
				return
			end
			self.is_not_goto_gather = false

			GuajiWGCtrl.Instance:MoveToPos(scene_ins:GetSceneId(), obj.vo.pos_x, obj.vo.pos_y, self:MoveToGatherRange(), nil, nil, nil, function()
				self.is_not_goto_gather = true
			    if nil ~= obj and not obj:IsDeleted() then
			        obj:OnClick()
			        GuajiWGCtrl.Instance:CheckCanGather(obj)
			    end
				-- MarryWGData.Instance:RemberGatnId(nil)
			end)

		end
	end
end

function WeddingSceneLogic:GetGuajiGather()
	local cur_gather_id = MarryWGData.Instance:GetRemberGatnId()
	if nil == cur_gather_id then
		return nil,nil
	end
	local obj_list = Scene.Instance:GetGatherList()
	if not obj_list or IsEmptyTable(obj_list) then
		return nil, nil
	end
	local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo() or {}
	if IsEmptyTable(weeding_info) then
		return nil, nil
	end
	local red_id, red_max, jiuxi_id, jiuxi_max, today_jiuxi_num = MarryWGData.Instance:GetWeedingSceneCfg()
	local main_role = Scene.Instance:GetMainRole()
    local main_role_x, main_role_y = main_role:GetLogicPos()
    local target_distance = 10000
    local target_x, target_y, distance = 0, 0, 0

	local can_gather_jiuxi = weeding_info.has_gather_num < tonumber(jiuxi_max) --是否可以继续采集酒席
	local can_gather_red_bag = weeding_info.has_gather_red_bag < red_max 		--是否可以继续采集喜酒/糖果
	if weeding_info.today_food_count ~= nil and weeding_info.today_food_count > 0 and weeding_info.today_food_count >= today_jiuxi_num then
		can_gather_jiuxi = false
	end
	if not can_gather_jiuxi and not can_gather_red_bag then
		return nil, nil
	end

	local target_obj = nil
	for k, v in pairs(obj_list) do
		local obj_id = v:GetObjId()
		local vo = v:GetVo()
        target_x, target_y = v:GetLogicPos()
        distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
        if distance < target_distance then
			if can_gather_jiuxi and vo.gather_id == jiuxi_id and vo.gather_id == cur_gather_id then
				if not MarryWGData.Instance:GetIsHasGather(obj_id) then
					target_obj = v
					target_distance = distance
				end
			elseif can_gather_red_bag and vo.gather_id == red_id and vo.gather_id == cur_gather_id  then
				target_obj = v
				target_distance = distance
			end
        end
	end
	return target_obj
end

function WeddingSceneLogic:IsRoleEnemy()
	return false
end

function WeddingSceneLogic:MoveToGatherRange()
	return 3.5
end