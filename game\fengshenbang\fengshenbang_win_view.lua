FengShenBangWinView = FengShenBangWinView or BaseClass(SafeBaseView)

function FengShenBangWinView:__init(view_name)
	self.view_style = ViewStyle.Half
	self.view_name = "FengShenBangWinView"
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/fengshenbang_prefab", "layout_fengshenbang_win")
end

function FengShenBangWinView:LoadCallBack()
	self:InitPanel()
	self:InitListener()
end

function FengShenBangWinView:ReleaseCallBack()
	CountDownManager.Instance:RemoveCountDown("fengshenbang_winview")

	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function FengShenBangWinView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "title_info" then
			self:SetTitleInfo(v)
		elseif k == "kill_info" then
			self:SetKillInfo(v)
		end
	end
end

function FengShenBangWinView:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown("fengshenbang_winview")
	if Scene.Instance:GetSceneType() ~= SceneType.Common then
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function FengShenBangWinView:InitPanel()
	CountDownManager.Instance:RemoveCountDown("fengshenbang_winview")
	CountDownManager.Instance:AddCountDown("fengshenbang_winview", 
		BindTool.Bind1(self.UpdateCountDownTime, self), 
		BindTool.Bind1(self.CompleteCountDownTime, self), 
		nil, 10, 1)
end

function FengShenBangWinView:InitListener()
	self.node_list.sure_btn.button:AddClickListener(BindTool.Bind(self.Close, self))
end

function FengShenBangWinView:SetTitleInfo(title_info)
	if self:CheckIsShowItemReward() then
		return
	end

	if title_info and title_info.title_id then
		local bundle,asset = ResPath.GetTitleModel(title_info.title_id)
		self.node_list.title_display:ChangeAsset(bundle, asset)
		self.node_list.no_title_label.text.text = ""
	else
		self.node_list.no_title_label.text.text = Language.Honorhalls.NoTitleLabel
	end
end

function FengShenBangWinView:CheckIsShowItemReward()
	local tip_show_des = ""
	local is_show_item = false
	if FengShenBangWGData.Instance:GetJoinRewardShowState() then
		local other_cfg = FengShenBangWGData.Instance:GetActCfgList("other")
		local need_time = other_cfg.join_num or 5
		local scene_id = FengShenBangWGData.Instance:GetFSBSceneId()
		local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgDataBySceneID(scene_id)
		if scene_cfg and scene_cfg.reward_item then
			local temp_data = {}
			for k, v in pairs(scene_cfg.reward_item) do
				table.insert(temp_data, v)
			end
			if not self.reward_item_list then
				self.reward_item_list = AsyncListView.New(ItemCell, self.node_list["reward_item_list"])
			end
			self.reward_item_list:SetDataList(temp_data)
		end
		
		tip_show_des = string.format(Language.FengShenBang.JoinJieSuanDes, Language.Common.UpNum[need_time])
		is_show_item = true
	end
	self.node_list["join_reward_lbl"].text.text = tip_show_des
	self.node_list["reward_item_list"]:SetActive(is_show_item)

	return is_show_item
end

function FengShenBangWinView:SetKillInfo(kill_info)
	if kill_info and kill_info.boss_id then
		local monster_cfg = BossWGData.Instance:GetMonsterInfo(kill_info.boss_id)
		local name = monster_cfg and monster_cfg.name or ""
		self.node_list.kill_info_label.text.text = string.format(Language.FengShenBang.KillBossDesc, name)
	end
end

function FengShenBangWinView:UpdateCountDownTime(elapse_time, total_time)
	self.node_list.auto_close_lbl.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, total_time - elapse_time)
end

function FengShenBangWinView:CompleteCountDownTime()
	self.node_list.auto_close_lbl.text.text = ""
	self:Close()
end