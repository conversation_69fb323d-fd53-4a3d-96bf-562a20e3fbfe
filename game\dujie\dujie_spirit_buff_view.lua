DujieSpiritBuffView = DujieSpiritBuffView or BaseClass(SafeBaseView)

--进阶形象展示预览
function DujieSpiritBuffView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_spirit_buff_view")
end

function DujieSpiritBuffView:SetCurSelectIndex(select_type)
	self.cur_select_type_index = select_type
end

function DujieSpiritBuffView:ReleaseCallBack()
	self.cur_select_type_index = nil

    if self.anger_type_attr_list and #self.anger_type_attr_list > 0 then
		for _, attr_cell in ipairs(self.anger_type_attr_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end

		self.anger_type_attr_list = nil
	end

	if self.anger_buff_attr_list and #self.anger_buff_attr_list > 0 then
		for _, attr_cell in ipairs(self.anger_buff_attr_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end

		self.anger_buff_attr_list = nil
	end

	if self.cell_list then
		for k,v in ipairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
    end

    if self.view_close_tween then
        self.view_close_tween:Kill()
        self.view_close_tween = nil
    end

	self:ClearSpiritBuffSliderTween()
end

function DujieSpiritBuffView:LoadCallBack()
    -- 基础属性
    if self.anger_type_attr_list == nil then
        self.anger_type_attr_list = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.anger_type_attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.anger_type_attr_list[i] = cell
            end
        end
    end

	-- 基础属性
	if self.anger_buff_attr_list == nil then
		self.anger_buff_attr_list = {}
		for i = 1, 6 do
			local attr_obj = self.node_list.anger_buff_attr_list:FindObj(string.format("attr_%d", i))
			if attr_obj then
				local cell = CommonAddAttrRender.New(attr_obj)
				cell:SetIndex(i)
				cell:SetAttrNameNeedSpace(true)
				self.anger_buff_attr_list[i] = cell
			end
		end
	end

	self.cell_list = {}
	XUI.AddClickEventListener(self.node_list.btn_buff_upgrade, BindTool.Bind(self.OnClickBuffUpgrade, self))
    self.close_tween = nil
end

function DujieSpiritBuffView:Close()
    local MOVE_TIME = 0.4
    self.view_close_tween = self.node_list.layout_spirit_buff_right.transform:DOAnchorPosX(642, MOVE_TIME)
    self.view_close_tween:OnComplete(function ()
        SafeBaseView.Close(self)
    end)
end

function DujieSpiritBuffView:OnFlush()
	if not self.cur_select_type_index then
		return
	end

    -- 获取怒气本体属性
    local anger_level = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    local cur_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, anger_level)
    local next_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, anger_level + 1)
    local upgrade_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_upgrade_cfg, next_upgrade_cfg, nil, nil, 1, 4)
    for index, attr_cell in ipairs(self.anger_type_attr_list) do
        attr_cell:SetVisible(upgrade_attr_list[index] ~= nil)

        if upgrade_attr_list[index] ~= nil then
            attr_cell:SetData(upgrade_attr_list[index])
        end
    end

    -- 根据buff等级获取buff属性
    local buff_level = CultivationWGData.Instance:GetAngerBuffLevel(self.cur_select_type_index)
	local buff_exp = CultivationWGData.Instance:GetAngerBuffExp(self.cur_select_type_index)
    local cur_buff_cfg = CultivationWGData.Instance:GetAngerBuffCfg(self.cur_select_type_index, buff_level)
    local next_buff_cfg = CultivationWGData.Instance:GetAngerBuffCfg(self.cur_select_type_index ,buff_level + 1)
	local is_max = next_buff_cfg == nil
    local buff_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_buff_cfg, next_buff_cfg, nil, nil, 10, 12)
    for index, attr_cell in ipairs(self.anger_buff_attr_list) do
        attr_cell:SetVisible(buff_attr_list[index] ~= nil)

        if buff_attr_list[index] ~= nil then
            attr_cell:SetData(buff_attr_list[index])
        end
    end

	self.node_list.img_max_buff:CustomSetActive(next_buff_cfg == nil)
	self.node_list.buff_up_layer_root:CustomSetActive(next_buff_cfg ~= nil)
    local is_can_anger_buff_upgrade = CultivationWGData.Instance:BuffUplevelStuffRed(self.cur_select_type_index)
    self.node_list.buff_upgrade_remind:CustomSetActive(is_can_anger_buff_upgrade)
	local list = CultivationWGData.Instance:GetBuffUplevelStuffList()

	for i, v in ipairs(list) do
		if not self.cell_list[i] then
			self.cell_list[i] = ItemCell.New(self.node_list["buff_item"])
		end

		self.cell_list[i]:SetData(v)
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		self.cell_list[i]:SetRightBottomTextVisible(true)
		self.cell_list[i]:SetRightBottomColorText(item_num)
	end

    local uplevel_exp_val = buff_exp
    local need_exp_val = cur_buff_cfg.need_exp

    if is_max then
        if self.is_spirit_auto_uplevel then
            self:PlaySpiritBuffSliderTween(0, uplevel_exp_val, need_exp_val)
        else
            self.node_list["exp_progress"].slider.value = 1
        end

        self.node_list["exp_txt"].text.text = "-/-"
    else
        if self.is_spirit_auto_uplevel then
            local add_level = buff_level - self.buff_old_level
            self:PlaySpiritBuffSliderTween(add_level, uplevel_exp_val, need_exp_val)
        else
            self.node_list["exp_txt"].text.text = uplevel_exp_val .. "/" .. need_exp_val
            self.node_list["exp_progress"].slider.value = uplevel_exp_val / need_exp_val
            self.node_list["btn_buff_upgrade_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[5]
        end
    end

    if not self.is_spirit_auto_uplevel then
        self:SpiritBuffLevelChangeFlush(buff_level, false)
    end

    -- 星级
    local show_star_num = 0

    if buff_level ~= 0 then
        show_star_num = buff_level % 10 == 0 and 10 or buff_level % 10
    end

    for i = 1, 10 do
        local res = (show_star_num >= i or is_max) and "a3_ty_xx_zc" or "a3_ty_xx_zh"
        self.node_list["spirit_buff_star_" .. i].image:LoadSprite(ResPath.GetCommonImages(res))
    end
end

-- 升级
function DujieSpiritBuffView:StartSpiritAutoUpLevel(no_tips)
	if not self.cur_select_type_index then
		return
	end

    local buff_level = CultivationWGData.Instance:GetAngerBuffLevel(self.cur_select_type_index)
	local next_buff_level = buff_level + 1
	local next_buff_cfg = CultivationWGData.Instance:GetAngerBuffCfg(self.cur_select_type_index, next_buff_level)

    if not next_buff_cfg then
        self:StopSpiritAutoUpLevel()
        return
    end

    -- 升阶材料
    local stuff_list = CultivationWGData.Instance:GetBuffUplevelStuffList()
    local stuff_id, had_stuff = 0, false
    for i, v in ipairs(stuff_list) do
        if stuff_id == 0 then
            stuff_id = v.item_id
        end
        
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if item_num > 0 then
            stuff_id = v.item_id
			had_stuff = true
            break
		end
	end

    if not had_stuff then
        if not no_tips then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_id})
        end

        self:StopSpiritAutoUpLevel()
        return
    end

    self.buff_old_level = buff_level
    self.is_spirit_auto_uplevel = true
    self.node_list["btn_buff_upgrade_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[3]
	CultivationWGCtrl.Instance:UpgradeAngerBuff(self.cur_select_type_index)
end

-- 停止
function DujieSpiritBuffView:StopSpiritAutoUpLevel()
    self.is_spirit_auto_uplevel = nil
    self.node_list["btn_buff_upgrade_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[5]
end

-- 去掉进度动画
function DujieSpiritBuffView:ClearSpiritBuffSliderTween()
    if self.buff_lv_slider_tween then
        self.buff_lv_slider_tween:Kill()
        self.buff_lv_slider_tween = nil
    end
end

-- 进度动画
function DujieSpiritBuffView:PlaySpiritBuffSliderTween(add_level, exp_val, need_exp_val)
    if add_level == 0 and exp_val == need_exp_val then
        self:ClearSpiritBuffSliderTween()
        local slider = self.node_list["exp_progress"].slider
        local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.1))
        self.buff_lv_slider_tween = slider:DOValue(1, time)
        self.buff_lv_slider_tween:OnComplete(function ()
            self:PlayUpLevelEffect()
		end)

        self:StopSpiritAutoUpLevel()
        return
    end

    local buff_level = CultivationWGData.Instance:GetAngerBuffLevel(self.cur_select_type_index)
	local buff_exp = CultivationWGData.Instance:GetAngerBuffExp(self.cur_select_type_index)

	self.slider_tween_func = function(progress)
		self:ClearSpiritBuffSliderTween()
		local before_uplevel_cfg = CultivationWGData.Instance:GetAngerBuffCfg(self.cur_select_type_index, self.buff_old_level)

        if not before_uplevel_cfg then
            return
        end

		if progress <= 0 then
            if self.is_spirit_auto_uplevel then
                if self.buff_old_level ~= buff_level then
                    self.buff_old_level = buff_level
                    self:SpiritBuffLevelChangeFlush(buff_level, true)
                end

                self:StartSpiritAutoUpLevel(true)
            end

            return
        end

        local is_up_one_level = false
        local slider = self.node_list["exp_progress"].slider

		if progress > 1 then
			local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.1))
			self.buff_lv_slider_tween = slider:DOValue(1, time)
            is_up_one_level = true
            self.node_list["exp_txt"].text.text = before_uplevel_cfg.need_exp .. "/" .. before_uplevel_cfg.need_exp
		else
			local time = tonumber(string.format("%.2f", (progress - slider.value) * 0.1))
			self.buff_lv_slider_tween = slider:DOValue(progress, time)
            self.node_list["exp_txt"].text.text = buff_exp .. "/" .. before_uplevel_cfg.need_exp
		end

        progress = progress - 1
        self.buff_lv_slider_tween:OnComplete(function ()
			if progress >= 0 then
				slider.value = 0
                if is_up_one_level then
                    self.buff_old_level = self.buff_old_level + 1
                    self:SpiritBuffLevelChangeFlush(self.buff_old_level, true)
                    self:PlayUpLevelEffect()
                end
			end
			
			self.slider_tween_func(progress)
		end)
    end

    local total_progress = add_level + exp_val / need_exp_val
    self.slider_tween_func(total_progress)
end

function DujieSpiritBuffView:SpiritBuffLevelChangeFlush(level, need_check_show_effect)
    local heavy_value = math.floor(level / 10)
    local lv_value = level % 10

    local str = ""
    if heavy_value > 0 then
        str = string.format("%s%s", string.format(Language.Common.Heavy, heavy_value), string.format(Language.NewAppearance.LevelText, lv_value))
    else
        str = string.format(Language.NewAppearance.LevelText, lv_value)
    end

	self.node_list.lv_txt.text.text = str
    local need_show_up_effect = false

    if need_check_show_effect then
        if nil ~= self.select_spirit_type_cache and nil ~= self.select_spirit_type_level_cache then
            if self.select_spirit_type_cache == self.cur_select_type_index and (level - self.select_spirit_type_level_cache == 1) then
                need_show_up_effect = true
            end
        end
    end

    self.select_spirit_type_cache = self.cur_select_type_index
    self.select_spirit_type_level_cache = level
end

-- 升级特效
function DujieSpiritBuffView:PlayUpLevelEffect()
    TipWGCtrl.Instance:ShowEffect({
        effect_type = UIEffectName.s_shengji,
        is_success = true,
        pos = Vector2(0, 0),
        parent_node = self.node_list["effect_pos"]
    })
end

------------------------------------------------
-- 升级buff
function  DujieSpiritBuffView:OnClickBuffUpgrade()
    if not self.cur_select_type_index then
        return
    end

    if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
		if not self.is_spirit_auto_uplevel then
			self:StartSpiritAutoUpLevel()
		else
			self:StopSpiritAutoUpLevel()
		end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips)
    end
end

-- 怒气buff升级
function DujieSpiritBuffView:AngerBuffUpgrade()
    if not CultivationWGData.Instance:IsMaxAngerBuff(self.cur_select_type_index) then
        local is_rich, item_id = CultivationWGData.Instance:BuffUplevelStuffRed(self.cur_select_type_index)
        if is_rich then
            CultivationWGCtrl.Instance:UpgradeAngerBuff(self.cur_select_type_index)
        else
            if item_id ~= 0 then
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
            end
        end
    end
end
