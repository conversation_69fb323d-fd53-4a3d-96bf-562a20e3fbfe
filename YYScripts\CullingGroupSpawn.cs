﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class CullingGroupSpawn : MonoBehaviour {

    [SerializeField] Transform[] positions;
    [SerializeField] string sceneName;  // 要加载的场景名称
    private CullingGroup cullingGroup = null;

    public List<MeshRenderer> listMesh;// = new List<MeshRenderer>();
    public List<GameObject> listObjParent;// = new List<GameObject>();
    //
    //public List<int> listParentNo;


    Dictionary<GameObject, List<MeshRenderer>> dicGoListMesh = new Dictionary<GameObject, List<MeshRenderer>>();

    //状态
    Dictionary<GameObject, bool> dicGoStatus = new Dictionary<GameObject, bool>();
    //Dictionary<GameObject, List<bool>> dicGoListStatus = new Dictionary<GameObject, List<bool>>();
    Dictionary<MeshRenderer, bool> dicMeshListStatus = new Dictionary<MeshRenderer, bool>();



    public void OnAwake(List<MeshRenderer> listMesh, List<GameObject> listObjParent)
    {
        this.listMesh = listMesh;
        this.listObjParent = listObjParent;

        dicGoListMesh.Clear();
        for (int i = 0; i < listMesh.Count; i++)
        {
            if (!dicGoListMesh.ContainsKey(listObjParent[i]))
            {
                dicGoListMesh.Add(listObjParent[i], null);
            }

            if (dicGoListMesh[listObjParent[i]] == null)
            {
                dicGoListMesh[listObjParent[i]] = new List<MeshRenderer>();
            }
            dicGoListMesh[listObjParent[i]].Add(listMesh[i]);
        }
        // status Init

        for (int i = 0; i < listMesh.Count; i++)
        {
            //if(dicGoStatus)
            if (!dicGoStatus.ContainsKey(listObjParent[i]))
            {
                dicGoStatus.Add(listObjParent[i], false);
            }
            //dicGoStatus[listObjParent[i]] = false;
            //dicGoListStatus[listObjParent[i]][i] = false;
            if (!dicMeshListStatus.ContainsKey(listMesh[i]))
            {
                dicMeshListStatus.Add(listMesh[i], false);
            }
        }

    }

    private void ResetStatus()
    {
        for (int i = 0; i < listMesh.Count; i++)
        {
            //if(dicGoStatus)
            dicGoStatus[listObjParent[i]] = false;
            dicMeshListStatus[listMesh[i]] = false;
        }
    }


    private bool bInit = false;

    private void Update()
    {
        OnStart();
    }

    public void OnStart()
    {
        if (bInit)
            return;
        if (listMesh == null || listMesh.Count <= 0)
            return;
        if (Camera.main == null)
            return;

        positions = new Transform[listMesh.Count];
        for (int i = 0; i < listMesh.Count; i++)
        {
            positions[i] = listMesh[i].gameObject.transform;
        }

        // cullingGroup初始化
        cullingGroup = new CullingGroup();
        cullingGroup.targetCamera = Camera.main;

        Debug.Log(cullingGroup.targetCamera.name);
        // 注册 更新可见性状态时 的回调
        cullingGroup.onStateChanged += OnStateChange;

        // 注册坐标
        BoundingSphere[] bounds = new BoundingSphere[positions.Length];
        for (int i = 0; i < bounds.Length; i++)
        {
            bounds[i].position = positions[i].position;
            bounds[i].radius = positions[i].localScale.magnitude;
        }
        cullingGroup.SetBoundingSpheres(bounds);
        cullingGroup.SetBoundingSphereCount(positions.Length);

        bInit = true;
    }

    //Dictionary<GameObject, List<MeshRenderer>> oldGoListMesh = new Dictionary<GameObject, List<MeshRenderer>>();

   

    void OnStateChange(CullingGroupEvent ev)
    {
        ResetStatus();
        for (int i = 0; i < positions.Length; i++)
        {
            if (cullingGroup.IsVisible(i) == true)
            {
                dicGoStatus[listObjParent[i]] = true;
                dicMeshListStatus[listMesh[i]] = true;
            }
        }
        ////去除  旧的 有 新的没有 
        foreach(var go in dicGoStatus)
        {
            if (go.Value == false)
            {
                go.Key.SetActive(false);
            }
            else
            {
                go.Key.SetActive(true);
                go.Key.GetComponent<GInstancing>().Init(dicGoListMesh[go.Key],dicMeshListStatus);
            }
        }
        
    }
    void OnDestroy()
    {
        cullingGroup.Dispose();
        cullingGroup = null;
    }

    void OnDrawGizmos()
    {
#if UNITY_EDITOR
        UnityEditor.Handles.color = Color.red;
        //foreach (var pos in positions)
        //{
           
        //    UnityEditor.Handles.CircleHandleCap(
        //      0, pos.position,
        //      UnityEditor.SceneView.currentDrawingSceneView.camera.transform.rotation,
        //      pos.localScale.magnitude);
        //}
#endif
    }
}
