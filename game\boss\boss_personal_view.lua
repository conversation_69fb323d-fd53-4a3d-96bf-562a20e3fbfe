BossView.PerReqType = {
	ALLINFO = 0,
}

function BossView:DeletePerBossView()
    if self.will_cell_list ~= nil then
        self.will_cell_list:DeleteMe()
        self.will_cell_list = nil
	end

	if self.per_boss_cell_list then
		self.per_boss_cell_list:DeleteMe()
		self.per_boss_cell_list = nil
	end
end

function BossView:InitPerBossView()
    self:CreatePerRareFallItem()
    --self.node_list["btn_zhenjia_per"]:SetActive(false)
    self.node_list["btn_zhenjia_per"].button:AddClickListener(BindTool.Bind1(self.OnPerentZhenjia, self))
end

function BossView:CreatePerRareFallItem()
	if not self.will_cell_list then
		self.will_cell_list = AsyncBaseGrid.New()
		local t = {}
		t.col = 3
		t.change_cells_num = 1
		t.itemRender = BossRewardCell
		t.list_view = self.node_list["per_mustdrop_items"]
		self.will_cell_list:CreateCells(t)
		self.will_cell_list:SetStartZeroIndex(false)
	end
end

function BossView:OnFlushPerBossView()
	local buy_count = BossWGData.Instance:GetPersonBossBuyCount()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = VipPower.Instance:GetPowerCfg(VipPowerId.vat_person_boss_extra_buy_times) or {}
	local can_buy = vip_buy_cfg["param_" .. role_vip] or 0
	local left_enter_num = BossWGData.Instance:GetPersonalBossEnterInfo()
    self.node_list.effect_zhenjia:SetActive(left_enter_num == 0 and can_buy > buy_count)
    self.node_list["Txt_goto_kill"].text.text = Language.Boss.PersonGoKill
end

function BossView:GetDefalutPerBossIndex(default_index, list_data)
	default_index = #list_data
	local role_level = RoleWGData.Instance:GetRoleLevel()
	for i, v in ipairs(list_data) do
		if role_level == v.need_level then
			default_index = v.index
			break
		elseif v.need_level > role_level then
			default_index = v.index - 1
			break
		end
	end
	return (default_index == 0 and 1 or default_index)
end

function BossView:OnFlushPerBossInfo()
	self:refreshPerRareFall()
	--需求要 新 boss才显示，又要都显示
	--self.node_list["per_tip"]:SetActive(BossWGData.Instance:IsPersonBossFirstKill(self.list_index))
end

function BossView:refreshPerRareFall()
    local boss_info = self.select_item_data
	if IsEmptyTable(boss_info) then
		return
	end
	
	self:GetBiDiaoCell(boss_info)
	self:FlushPerBossCellGrid(boss_info)

	local left_enter_num, max_enter_num = BossWGData.Instance:GetPersonalBossEnterInfo()
	local color = left_enter_num > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
	local num_str = ToColorStr((left_enter_num .. "/" .. max_enter_num), color)
	local temp_str = Language.Boss.LeftPerBossEnterTip
	local str = string.format(temp_str, num_str)
	self.node_list["rich_perboss_leftnum"].text.text = str
	self.node_list["per_kill_txt_tip"].text.text = Language.Boss.PerKillTxtTip
end

function BossView:GetBiDiaoCell(boss_info)
	if not self.will_cell_list then
		self:CreatePerRareFallItem()
    end

    local data = {}
    for k, v in pairs(boss_info.reward_item) do
        data[k + 1] = v
        data[k + 1].cell_scale = 0.9
    end
    self.will_cell_list:SetDataList(data)
end

function BossView:FlushPerBossCellGrid(boss_info)
	self:GetPerBossCellGrid()
	local item_list
	local list = {}
	if not boss_info.is_item_list then
		item_list = Split(boss_info.drop_item_list, "|")
	else
		item_list = boss_info.drop_item_list
		local zero_t = item_list[0]
		zero_t.cell_scale = 0.9
		table.insert(list, zero_t)
	end

	local data
	for i, v in ipairs(item_list) do
		if boss_info.is_item_list then
			data = v
			data.cell_scale = 0.9
		else
			data = {item_id = tonumber(v), cell_scale = 0.9}
		end
		table.insert(list, data)
	end

	self.per_boss_cell_list:SetDataList(list,3)
end

function BossView:GetPerBossCellGrid()
	if not self.per_boss_cell_list then
		self.per_boss_cell_list = AsyncBaseGrid.New()
		local t = {}
		t.col = 3
		t.change_cells_num = 1
		t.itemRender = BossRewardCell
		t.list_view = self.node_list["per_item_list"]
		self.per_boss_cell_list:CreateCells(t)
		self.per_boss_cell_list:SetStartZeroIndex(false)
	end
end

function BossView:OnPerentZhenjia()
    BossWGCtrl.Instance:OpenBossVipTimesView(VipPowerId.vat_person_boss_extra_buy_times)
    --FuBenPanelWGCtrl.Instance:OpenBuyView(VipPowerId.vat_person_boss_extra_buy_times)
end

-- 前往击杀
function BossView:GoToKillPerBoss()
	-- if Scene.Instance:GetSceneType() == SceneType.VIP_BOSS or Scene.Instance:GetSceneType() == SceneType.DABAO_BOSS -- 其他副本场景
	-- 		or Scene.Instance:GetSceneType() == SceneType.SG_BOSS then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutFubenTip)
	-- 	return
	-- end
	--local person_data = BossWGData.Instance:GetPersonalBossList()
	local boss_data = self.select_item_data

    if boss_data == nil then
        return
    end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < boss_data.need_level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelUnLock2, RoleWGData.GetLevelString(boss_data.need_level)))
		return
	end

	local left_enter_num, max_enter_num = BossWGData.Instance:GetPersonalBossEnterInfo()
    local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level

	if vip_level < BossWGData.Instance:GetEnterPersonCfgbyIndex() then
        local data = {}
        data.tip = string.format(Language.Boss.PersonBossVipNoEnohgh,  BossWGData.Instance:GetEnterPersonCfgbyIndex())
        data.click_ok = function ()      
            ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
        end
        BossWGCtrl.Instance:OpenVipNotEnoughView(data)
		return
	end

	if left_enter_num < 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		return
    end
    local ok_func = function()
        BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex(), boss_data.layer, boss_data.boss_id)
        FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_PERSON_BOSS, boss_data.layer)
        BossWGData.Instance:SetBossEnterFlag(true)
        BossWGData.Instance:SetEnterPersonBossData(boss_data.boss_id)
    end
    BossWGCtrl.Instance:OpenPersonCosumeView(ok_func)
end