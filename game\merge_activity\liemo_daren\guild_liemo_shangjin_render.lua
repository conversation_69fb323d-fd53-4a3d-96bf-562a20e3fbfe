--猎魔达人_仙盟赏金格子
GuildLieMoShangJinRender = GuildLieMoShangJinRender or BaseClass(BaseRender)

function GuildLieMoShangJinRender:LoadCallBack()
    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    self.node_list.join_guild_tip.button:AddClickListener(BindTool.Bind(self.JoinGuild, self))
    self.item_list:SetIsDelayFlush(false)
end

function GuildLieMoShangJinRender:__delete()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function GuildLieMoShangJinRender:JoinGuild()
    GuildWGCtrl.Instance:Open(TabIndex.guild_info)
end

function GuildLieMoShangJinRender:OnFlush()
    if not self.data then
        return
    end

    --奖励格子列表
    self.item_list:SetDataList(self.data.item_list)

    --描述
    local info = LieMoDaRenWGData.Instance:GetLieMoInfo()
    local cur_score = info.guild_score
    local has_complete = cur_score >= self.data.score
    local score_str = ToColorStr(string.format("%s", self.data.score), has_complete and COLOR3B.C2 or COLOR3B.C3)
    local cur_score_str = ToColorStr(cur_score, COLOR3B.C3)
    self.node_list.desc.text.text = string.format(Language.LieMoDaRen.GuildShangJinItemDesc, score_str, cur_score_str)

    --是否有仙盟
    local has_guild = GameVoManager.Instance:GetMainRoleVo().guild_id > 0
    self.node_list.join_guild_tip:SetActive(not has_guild)
    self.node_list.img_state:SetActive(has_guild)

    --按钮
    if has_guild then
        local guild_is_reward = info.guild_is_reward == 1
        local bundle_name, asset_name = ResPath.GetCommonImages((has_complete or not guild_is_reward) and "a3_ty_tlabel_02" or "a3_ty_tlabel_01")
        self.node_list.img_state.image:LoadSprite(bundle_name, asset_name)
        local str = has_complete and Language.LieMoDaRen.BtnText_IsEnd or Language.LieMoDaRen.BtnText_IsNot
        self.node_list.img_state_text.text.text = guild_is_reward and str or Language.LieMoDaRen.BtnText_WaitComplete
    end
end