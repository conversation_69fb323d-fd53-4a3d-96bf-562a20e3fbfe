
function TianshenRoadView:InitJiangLinView()
	XUI.AddClickEventListener(self.node_list.jl_btn_shilian, BindTool.Bind(self.OnBtnShiLian<PERSON><PERSON><PERSON><PERSON><PERSON>,self))

	local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.tianshenroad_jianglin)
	if theme_cfg ~= nil then
		self.node_list.jl_tip_label.text.text = theme_cfg.rule_desc
	end

	if not self.tsjl_boss_model then
		self.tsjl_boss_model = OperationActRender.New(self.node_list["tsjl_boss_model"])
		self.tsjl_boss_model:SetModelType(MODEL_CAMERA_TYPE.BASE)
	end

	self.jl_sl_reward_list = AsyncListView.New(ItemCell, self.node_list["jl_sl_reward_list"])
	self.jl_yj_reward_list = AsyncListView.New(Item<PERSON>ell, self.node_list["jl_yi_reward_list"])

	self:JLTimeCountDown()
	self:FlushJiangLinView()

	self.node_list.ts_title_sl.text.text = Language.TianShenRoad.TianShenXianLingMustFull
	self.node_list.ts_title_yj.text.text = Language.TianShenRoad.TianShenXianLingProbabilityFull
end

function TianshenRoadView:ReleaseJiangLinView()
	if self.jl_sl_reward_list then
		self.jl_sl_reward_list:DeleteMe()
		self.jl_sl_reward_list = nil
	end

	if self.jl_yj_reward_list then
		self.jl_yj_reward_list:DeleteMe()
		self.jl_yj_reward_list = nil
	end

	if self.tsjl_boss_model then
		self.tsjl_boss_model:DeleteMe()
		self.tsjl_boss_model = nil
	end
	
	CountDownManager.Instance:RemoveCountDown("tianshenroad_jianglin_count_down")
	CountDownManager.Instance:RemoveCountDown("TS_common_count_douwn")
end

function TianshenRoadView:TRJLShowIndexCallBack()
	self:DoTRJLAnim()
end

function TianshenRoadView:FlushJiangLinView()
	local is_open = TianshenRoadWGData.Instance:IsInTSJLActivity()

	self.node_list["jl_btn_name"].text.text = Language.TianShenRoad.GoShiLian
	self.node_list.jl_btn_redpoint:SetActive(is_open)
	XUI.SetButtonEnabled(self.node_list.jl_btn_shilian, is_open)

	self:InitJiangLinRewardList()
	self:FlushJiangLinModel()
end

function TianshenRoadView:InitJiangLinRewardList()
	local reward_cfg = TianshenRoadWGData.Instance:GetJiangLinReward()
	if reward_cfg and reward_cfg.reward_show then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(reward_cfg.reward_show)
		self.jl_yj_reward_list:SetDataList(data_list)
	end

	local boss_drop_cfg = TianshenRoadWGData.Instance:GetJiangLinBossDropCfg()
	if boss_drop_cfg and boss_drop_cfg[1] then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(boss_drop_cfg[1].random_item)
		self.jl_sl_reward_list:SetDataList(data_list)
	end
end

function TianshenRoadView:FlushJiangLinModel()
	local init_cfg = TianshenRoadWGData.Instance:GetActModelCfgById(ACTIVITY_TYPE.GOD_JIANGLIN)
	if not init_cfg then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if init_cfg.model_show_itemid ~= 0 and init_cfg.model_show_itemid ~= "" then
		local split_list = string.split(init_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = init_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = init_cfg["model_bundle_name"]
	display_data.asset_name = init_cfg["model_asset_name"]
	local model_show_type = tonumber(init_cfg["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1

	self.tsjl_boss_model:SetData(display_data)

	if init_cfg.model_scale and init_cfg.model_scale ~= "" then
		local scale = init_cfg.model_scale
		Transform.SetLocalScaleXYZ(self.node_list["tsjl_boss_model"].transform, scale, scale, scale)
	end

	local pos_x, pos_y = 0, 0
	if init_cfg.model_pos and init_cfg.model_pos ~= "" then
		local pos_list = string.split(init_cfg.model_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.tsjl_boss_model.rect, pos_x, pos_y)

	if init_cfg.model_rot and init_cfg.model_rot ~= "" then
		local rotation_tab = string.split(init_cfg.model_rot, "|")
		self.node_list["tsjl_boss_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
			rotation_tab[3])
	end
end

function TianshenRoadView:OnBtnShiLianClickHnadler()
	local boss_id = TianshenRoadWGData.Instance:GetBossId()
	if boss_id and boss_id > 0 then
		TianshenRoadWGCtrl.Instance:GotoShiLian()
		self:Close()
	end
end 

--有效时间倒计时
function TianshenRoadView:JLTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_jianglin_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_jianglin)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.jl_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("tianshenroad_jianglin_count_down", BindTool.Bind1(self.UpdateJLCountDown, self), BindTool.Bind1(self.JLTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.jl_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.jl_time_label.text.color = Str2C3b(COLOR3B.RED)
	end
end

function TianshenRoadView:UpdateJLCountDown(elapse_time, total_time)
	self.node_list.jl_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

function TianshenRoadView:DoTRJLAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["ts_jianglin_root"])
    UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["ts_jianglin_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end
