ChangeHeadView = ChangeHeadView or BaseClass(SafeBaseView)

function ChangeHeadView:__init()
	self:SetMaskBg(true, true)
	self.open_tween = nil
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_change_head")
end

function ChangeHeadView:__delete()

end

function ChangeHeadView:ReleaseCallBack()
	if self.act_stuff_cell then
		self.act_stuff_cell:DeleteMe()
		self.act_stuff_cell = nil
	end

	if self.first_reward_cell then
		self.first_reward_cell:DeleteMe()
		self.first_reward_cell = nil
	end

	if self.head_list_view then
		self.head_list_view:DeleteMe()
		self.head_list_view = nil
	end

	self.attr_list = nil
	self.role_id = nil
	self.uploading_list = nil
	self.select_head_index = -1
	self.uploading_count = nil
	self.cur_show_toggle_head = nil
end

function ChangeHeadView:LoadCallBack()
	self.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	self.uploading_list = {}			--上传的头像数据
	self.select_head_index = -1
	self.uploading_count = 0
	self.cur_show_toggle_head = false

	if self.act_stuff_cell == nil then
		self.act_stuff_cell = ItemCell.New(self.node_list.act_stuff_cell)
	end

	if self.first_reward_cell == nil then
		self.first_reward_cell = ItemCell.New(self.node_list.first_reward_node)
		local other_cfg = RoleWGData.Instance:GetHeadOtherCfg()
		local reward_item = other_cfg.reward_item and other_cfg.reward_item[0]
		self.first_reward_cell:SetData(reward_item)
	end

	if not self.head_list_view then
		self.head_list_view = AsyncBaseGrid.New()
		self.head_list_view:CreateCells({col = 3, change_cells_num = 1,
										   assetName = "head_list_cell",
										   assetBundle = "uis/view/role_ui_prefab",
										   itemRender = HeadIconRender,
										   list_view = self.node_list.head_list
										  })
		self.head_list_view:SetSelectCallBack(BindTool.Bind(self.OnClickHeadIcon, self))
		self.head_list_view:SetStartZeroIndex(false)
	end

	self.attr_list = {}
	local attr_num = self.node_list.attr_list.transform.childCount
	for i = 1, attr_num do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr" .. i)
	end

	self:FlushUseDIYRemind(false)
	XUI.AddClickEventListener(self.node_list.btn_active, BindTool.Bind(self.OnClickActive, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
	XUI.AddClickEventListener(self.node_list.btn_photo, BindTool.Bind(self.OnClickSelectPhoto, self))
	XUI.AddClickEventListener(self.node_list.btn_reset, BindTool.Bind(self.OnClickReset, self))
	XUI.AddClickEventListener(self.node_list.btn_photograph, BindTool.Bind(self.OnClickPhotograph, self))
    XUI.AddClickEventListener(self.node_list.btn_set, BindTool.Bind(self.OnClickSet, self))
    XUI.AddClickEventListener(self.node_list.custom_icon, BindTool.Bind(self.OnClickCustomBtn, self))
    self.node_list.custom_icon.raw_image.raycastTarget = true

	--self.node_list.cap_part:SetActive(false)
end

function ChangeHeadView:ShowIndexCallBack()
	-- self.first_select_index = RoleWGData.Instance:GetCurHeadIndex()
	self:DoViewAnimation()
end

function ChangeHeadView:OpenCallBack()
	-- local head_index = RoleWGData.Instance:GetCurUseHead() --是否为自定义头像
	-- self.is_show = head_index > GameEnum.CUSTOM_HEAD_ICON
end

function ChangeHeadView:CloseCallBack()
	self.select_head_index = -1
	-- if self.head_list_view then
	-- 	self.head_list_view:CancelSelect()
	-- end
end

function ChangeHeadView:DoViewAnimation()
	local tween_info = UITween_CONSTS.ChangeHead
	-- local tween_root_1 = self.node_list.middle
	local tween_root_2 = self.node_list.content_part

	UITween.CleanAllTween(GuideModuleName.ChangeHeadView)
	-- UITween.FakeHideShow(tween_root_1)
	UITween.FakeHideShow(tween_root_2)
	-- RectTransform.SetAnchoredPositionXY(self.node_list.list_bg.rect, -494, 2)
	-- RectTransform.SetAnchoredPositionXY(self.node_list.right_di.rect, 500, 0)

	-- ReDelayCall(self, function()
	-- 	UITween.AlphaShow(GuideModuleName.ChangeHeadView, tween_root_1, 0, 1, tween_info.ListMoveTime, tween_info.AlphaShowType)
	-- 	self.node_list.list_bg.rect:DOAnchorPos(Vector2(-277, 2), tween_info.ListMoveTime)
	-- 	self.node_list.right_di.rect:DOAnchorPos(Vector2(262, 0), tween_info.ListMoveTime)
	-- end, tween_info.BgMoveTime, "change_head_tween_1")

	ReDelayCall(self, function()
		UITween.AlphaShow(GuideModuleName.ChangeHeadView, tween_root_2, 0, 1, tween_info.AlphaTime, tween_info.AlphaShowType)
	end, tween_info.DelayAlphaShowTime, "change_head_tween_2")
end

-- 点击回调
function ChangeHeadView:OnClickHeadIcon(cell, cell_index, is_default, is_click)
	if not cell or not cell.data or not cell.data.cfg then
		return
	end
	if is_click and self.select_head_index == cell:GetIndex() then
		return
	end

	self.select_head_index = cell:GetIndex()
	self.cur_show_toggle_head = true
	self.icon_path_big = nil
	self.icon_path_small = nil
	self:FlushUseDIYRemind(false)

	local prof = RoleWGData.Instance:GetRoleProf()
	local resouce = cell.data.cfg["resouce" .. prof]
	self:ShowSystemHead(resouce)

	self:FlushPanel()
end

function ChangeHeadView:OnClickCustomBtn()
    if self.icon_path_big and self.icon_path_big ~= "" then
        RoleWGCtrl.Instance:OpenHeadBigViewByPath(self.icon_path_big)
    else
    	 local index = AvatarManager.Instance:GetAvatarKey(self.role_id, true)
    	 if index > GameEnum.CUSTOM_HEAD_ICON then
    	 	local data = {}
    	 	data.role_id = self.role_id
			RoleWGCtrl.Instance:OpenHeadBigView(data)
    	 end
    end
end

function ChangeHeadView:OnFlush()
	if self.head_list_view then
		local data = RoleWGData.Instance:GetAllHeadSortCfg()
		self.head_list_view:SetDataList(data)
		self:FlushPanel(data)
	end
end

function ChangeHeadView:FlushPanel(data_list)
	local cur_select_index = self.select_head_index == -1 and RoleWGData.Instance:GetCurHeadIndex() or self.select_head_index
	local role_data = RoleWGData.Instance
	-- 首次
	local limit = RoleWGData.Instance:GetLimitCustomAvatar()
	local first_flag = role_data:IsFirstChangeDiyHead()
	local is_click = role_data:IsClickDiyHeadBtn()
	self.node_list.frist_use_part:SetActive(first_flag and (not limit))
	self.node_list.frist_bg:SetActive(first_flag and (not limit))
	self.node_list.btn_photo_red:SetActive(not is_click and first_flag and (not limit))
	self.node_list.btn_photograph_red:SetActive(not is_click and first_flag and (not limit))

	self.node_list.btn_photo:SetActive(not limit)
	self.node_list.btn_photograph:SetActive(not limit)
	self.node_list.btn_reset:SetActive(not limit)

	if self.select_head_index == -1 and cur_select_index == 1 then
		self:OnClickReset()
		self.node_list.btn_set:SetActive(true)
		self.node_list.attr_bg:SetActive(true)
		self.node_list.attr_list:SetActive(false)
		self.node_list.no_act_part:SetActive(false)
		self.node_list.cap_part:SetActive(false)
		self.head_list_view:SetSelectCellIndex(1)
		return
	end
	local head_cfg = data_list and data_list[cur_select_index] or RoleWGData.Instance:GetAllHeadSortCfg()[cur_select_index]
	if self.select_head_index == -1 then
		if head_cfg then
			local prof = RoleWGData.Instance:GetRoleProf()
			self:ShowSystemHead(head_cfg.cfg["resouce" .. prof], cur_select_index)
		end
		self.head_list_view:SetSelectCellIndex(cur_select_index)
	end

	-- 属性
	local no_data_flag = true
	local attr_list = role_data:GetHeadAttrList(head_cfg.cfg.index)
	local attr_data, is_per, per_desc, name_str, value_str
	for k,v in pairs(self.attr_list) do
		attr_data = attr_list[k]
		if attr_data then
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_data.attr_str)
			per_desc = is_per and "%" or ""
			name_str = Language.Common.AttrNameList2[attr_data.attr_str]
			value_str = is_per and attr_data.attr_value / 100 or attr_data.attr_value
			value_str = value_str .. per_desc
			v.text.text = string.format("%s       <color=#ffffff>%s</color>", name_str, value_str)
			v:SetActive(true)
			no_data_flag = false
		else
			v:SetActive(false)
		end
	end

	self.node_list.attr_list:SetActive(not no_data_flag)
	self.node_list.attr_bg:SetActive(no_data_flag)

	-- 战力
	local cap = 0
	if head_cfg then
		local temp_attr = AttributeMgr.GetAttributteByClass(head_cfg.cfg)
		cap = AttributeMgr.GetCapability(temp_attr)
	end
	self.node_list.cap_value.text.text = cap

	-- 消耗
	local status = role_data:GetHeadStatusByIndex(head_cfg.cfg.index)
	if (status == GOODS_STATE_TYPE.UNACT or status == GOODS_STATE_TYPE.CAN_ACT) and head_cfg then
		self.node_list.no_act_part:SetActive(true)
		self.act_stuff_cell:SetData({item_id = head_cfg.cfg.active_stuff_id})
		local num = ItemWGData.Instance:GetItemNumInBagById(head_cfg.cfg.active_stuff_id)
		local is_enough = num >= head_cfg.cfg.count
		local color = is_enough and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		self.act_stuff_cell:SetRightBottomColorText(ToColorStr(num .. "/" .. head_cfg.cfg.count, color))
		self.act_stuff_cell:SetRightBottomTextVisible(true)

		local prof_enough = true
		if head_cfg.cfg.zhuanzhi_revolution > 0 then
			local prof_level = role_data:GetZhuanZhiNumber()
			prof_enough = prof_level >= head_cfg.cfg.zhuanzhi_revolution
		end

		self.node_list.btn_set:SetActive(false)
		-- 激活红点
		self.node_list.btn_active_red:SetActive(role_data:CanActiveHeadByIndex(head_cfg.cfg.index))
	else
		self.node_list.btn_set:SetActive(true)
		self.node_list.no_act_part:SetActive(false)
	end
end

function ChangeHeadView:FlushUseDIYRemind(remind)
	if self.node_list.btn_set_red then
		self.node_list.btn_set_red:SetActive(remind)
	end
end

-- 激活
function ChangeHeadView:OnClickActive()
	local role_data = RoleWGData.Instance
	local cur_select_index = self.select_head_index == -1 and RoleWGData.Instance:GetCurHeadIndex() or self.select_head_index

	local head_cfg = role_data:GetAllHeadSortCfg()[cur_select_index]
	local status = role_data:GetHeadStatusByIndex(head_cfg.cfg.index)
	if status == GOODS_STATE_TYPE.USING or status == GOODS_STATE_TYPE.NORMAL then
		return
	end

	local head_cfg = role_data:GetHeadCfgByIndex(head_cfg.cfg.index)
	if head_cfg == nil then
		return
	end

	if head_cfg.zhuanzhi_revolution > 0 then
		local prof_level = role_data:GetZhuanZhiNumber()
		if prof_level < head_cfg.zhuanzhi_revolution then
			return
		end
	end

	if status ~= GOODS_STATE_TYPE.CAN_ACT then
		local item_cfg = ItemWGData.Instance:GetItemConfig(head_cfg.active_stuff_id)
		if item_cfg then
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.NoEnoughTips, item_name))
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = head_cfg.active_stuff_id})
		end
		return
	end

	local bag_index = ItemWGData.Instance:GetItemIndex(head_cfg.active_stuff_id)
	BagWGCtrl.Instance:SendUseItem(bag_index, head_cfg.count)
end

-- 相册选择
function ChangeHeadView:OnClickSelectPhoto()
	local is_click = RoleWGData.Instance:IsClickDiyHeadBtn()
	if not is_click then
		RoleWGCtrl.Instance:SendHeadAgreeProtocolReq(1)
	end

	local is_agree = RoleWGData.Instance:IsAgreeHeadProtocol()
	if is_agree then
		ImagePicker.PickFromPhoto(256, 32, function(pickPath, scaledPath)
			self:SelectIconCallback(pickPath, scaledPath)
		end)
	else
		local call_back = BindTool.Bind(self.OnClickSelectPhoto, self)
		RoleWGCtrl.Instance:OpenHeadProtocolTips(call_back)
		return
	end
end

-- 相机拍照
function ChangeHeadView:OnClickPhotograph()
	local is_click = RoleWGData.Instance:IsClickDiyHeadBtn()
	if not is_click then
		RoleWGCtrl.Instance:SendHeadAgreeProtocolReq(1)
	end

	local is_agree = RoleWGData.Instance:IsAgreeHeadProtocol()
	if is_agree then
		ImagePicker.PickFromCamera(256, 32, function(pickPath, scaledPath)
			self:SelectIconCallback(pickPath, scaledPath)
		end)
	else
		local call_back = BindTool.Bind(self.OnClickPhotograph, self)
		RoleWGCtrl.Instance:OpenHeadProtocolTips(call_back)
		return
	end
end

--选择图片回调
function ChangeHeadView:SelectIconCallback(pickPath, scaledPath)
	if pickPath == nil or scaledPath == nil or pickPath == "" then
		print_log("选择路径为空  pickPath", pickPath, "scaledPath", scaledPath)
		return
	end

	self.icon_path_big = pickPath
	self.icon_path_small = scaledPath

	self.cur_show_toggle_head = false
	self:ShowCustomHead(pickPath)
	local limit = RoleWGData.Instance:GetLimitCustomAvatar()
	self:FlushUseDIYRemind(not limit)
end

-- 重置头像（显示当前头像）
function ChangeHeadView:OnClickReset()
	self.cur_show_toggle_head = false
	self.icon_path_big = nil
	self.icon_path_small = nil
	self:FlushUseDIYRemind(false)
	local role_sex, role_prof = RoleWGData.Instance:GetRoleSexProf()
    XUI.UpdateRoleHead(self.node_list.default_icon, self.node_list.custom_icon, self.role_id, role_sex, role_prof)
end

-- 设置头像
function ChangeHeadView:OnClickSet()
	-- 使用自定义
	if self.icon_path_big ~= nil and self.icon_path_small ~= nil then
		local limit = RoleWGData.Instance:GetLimitCustomAvatar()
		if limit then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.LimitHeadSet)
			return
		else
			self:UploadBackground()
		end
	elseif self.cur_show_toggle_head then
		-- 使用系统的
		local head_cfg = RoleWGData.Instance:GetAllHeadSortCfg()[self.select_head_index]
		if not head_cfg or not head_cfg.cfg then
			return
		end
		local status = RoleWGData.Instance:GetHeadStatusByIndex(head_cfg.cfg.index)
		if status == GOODS_STATE_TYPE.UNACT or status == GOODS_STATE_TYPE.CAN_ACT then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.HeadNoAct)
			return
		end

		local old_index = RoleWGData.Instance:GetCurUseHead()
		if old_index ~= head_cfg.cfg.index then
			RoleWGCtrl.Instance:SendUseAttrHead(head_cfg.cfg.index)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.HeadSetSuccess)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.HeadHadSet)
		end
	elseif not self.cur_show_toggle_head then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.HeadHadSet)
	end
end

--上传到后台
function ChangeHeadView:UploadBackground()
	if self.icon_path_big == "" or self.icon_path_small == "" then
		print_log("自定义数据 - 无")
		return
	end

	local upload_callback = BindTool.Bind(self.UploadCallback, self)
	local url_big = AvatarManager.GetFileUrl(self.role_id, true)
	self.uploading_list[url_big] = {url = url_big, path = self.icon_path_big, callback = upload_callback}
	if not HttpClient:Upload(url_big, self.icon_path_big, upload_callback) then
		self:CancelUpload()
		SysMsgWGCtrl.Instance:ErrorRemind("上传失败")
		print_log("上传失败", url_big, self.icon_path_big)
		return
	end

	-- local url_small = AvatarManager.GetFileUrl(self.role_id, false)
	-- self.uploading_list[url_small] = {url = url_small, path = self.icon_path_small, callback = upload_callback}
	-- if not HttpClient:Upload(url_small, self.icon_path_small, upload_callback) then
	-- 	self:CancelUpload()
	-- 	SysMsgWGCtrl.Instance:ErrorRemind("上传失败")
	-- 	print_log("上传失败", url_small, self.icon_path_small)
	-- 	return
	-- end
end

-- 上传头像回调
function ChangeHeadView:UploadCallback(url, path, is_succ, data)
	if GLOBAL_CONFIG.local_package_info.config.agent_id == "dev" then
		print_error("is_succ", is_succ, "self.uploading_count:", self.uploading_count)
	end
	if self.uploading_count == nil then
		self:FlushUseDIYRemind(false)
		return
	end

	self.uploading_count = self.uploading_count - 1
	if not is_succ then
		self:CancelUpload()
		SysMsgWGCtrl.Instance:ErrorRemind("上传失败")
		print_log(" 上传失败 UploadCallback ")
		return
	end

	if self.uploading_count <= 0 then
		if GLOBAL_CONFIG.local_package_info.config.agent_id == "dev" then
			print_error("succ")
		end
		local custom_info = RoleWGData.Instance:GetCustomHeadInfo()
		local icon_key_big = self.icon_path_big and AvatarManager.getFileKey(self.icon_path_big) or custom_info.avatar_key_big
		local icon_key_small = self.icon_path_small and AvatarManager.getFileKey(self.icon_path_small) or custom_info.avatar_key_small
		self:FlushUseDIYRemind(false)
		RoleWGCtrl.Instance.SendSetAvatarTimeStamp(icon_key_big, icon_key_small)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.HeadSetSuccess)
	end
end

-- 取消上传
function ChangeHeadView:CancelUpload()
	if self.uploading_list == nil then
		return
	end

	for k, v in pairs(self.uploading_list) do
		HttpClient:CancelUpload(v.url, v.callback)
	end

	self.uploading_count = 0
	self.uploading_list = {}
end

-- 设置自定义头像
function ChangeHeadView:ShowCustomHead(pickPath)
	self.node_list.custom_icon.raw_image:LoadURLSprite(pickPath, function()
		self.node_list.custom_icon:SetActive(true)
		self.node_list.default_icon:SetActive(false)
		self.node_list.cap_part:SetActive(false)
		self.node_list.frist_bg:SetActive(false)
	end)
end

-- 设置系统头像
function ChangeHeadView:ShowSystemHead(resouce, cur_select_index)
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local bundle, asset = ResPath.GetNewHeadBigIcon(role_sex, resouce)
	self.node_list.default_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.default_icon:SetActive(true)
		self.node_list.custom_icon:SetActive(false)
		if cur_select_index then
			self.node_list.cap_part:SetActive(cur_select_index > 1)
		else
			self.node_list.cap_part:SetActive(self.select_head_index > 1)
		end
	end)
end

---------------------------- HeadIconRender ----------------------------

HeadIconRender = HeadIconRender or BaseClass(BaseRender)
function HeadIconRender:__init()
end

function HeadIconRender:__delete()
end

function HeadIconRender:LoadCallBack()
end

function HeadIconRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cfg = self.data.cfg
	-- print_error("self.data.cfg =", self:GetIndex(), self.data.cfg)
	local status = self.data.status
    local role_sex = RoleWGData.Instance:GetRoleSex()
    local prof = RoleWGData.Instance:GetRoleProf()
	local resouce = cfg["resouce" .. prof]

	self.node_list.default_icon.image:LoadSprite(ResPath.GetNewHeadBigIcon(role_sex, resouce))
	self.node_list.custom_icon:SetActive(false)
	self.node_list.default_icon.image:SetNativeSize()

	self.node_list.Text.text.text = cfg.name
	local is_active = status == GOODS_STATE_TYPE.USING or status == GOODS_STATE_TYPE.NORMAL
	self.node_list.lock_state:SetActive(not is_active)
	self.node_list.flag_img:SetActive(status == GOODS_STATE_TYPE.USING)
	self.node_list.img_red:SetActive(status == GOODS_STATE_TYPE.CAN_ACT)

	if cfg.zhuanzhi_revolution and cfg.zhuanzhi_revolution > 0 then
		self.node_list.bottom_bg:SetActive(true)
		self.node_list.bottom_text.text.text = Language.Common.ZhuanZhiShu[cfg.zhuanzhi_revolution]
	else
		self.node_list.bottom_bg:SetActive(false)
		self.node_list.bottom_text.text.text = ""
	end
end

function HeadIconRender:OnSelectChange(is_select)
	self.node_list.img_hl:SetActive(is_select)
end
