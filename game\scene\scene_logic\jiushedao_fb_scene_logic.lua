-----------------------------------------
--仙盟禁地
-----------------------------------------
JiuSheDaoSceneLogic = JiuSheDaoSceneLogic or BaseClass(CommonFbLogic)

REFER_DISTANCE = 13.45 --速度为1时完整的跳跃距离

function JiuSheDaoSceneLogic:__init()
    self.curr_jump = nil
    self.is_jumping = false
    self.curr_jump_vo = nil
    self.next_jump_vo = nil
    self.jump_end_call_back = nil
    self.role_move_call_back = nil
    self.jump_point_x = nil
    self.jump_point_y = nil
end

function JiuSheDaoSceneLogic:__delete()
    self.curr_jump = nil
    self.jump_end_call_back = nil
    self.role_move_call_back = nil
end

function JiuSheDaoSceneLogic:Enter(old_scene_type, new_scene_type)
    ViewManager.Instance:CloseAll()
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    GuildWGCtrl.Instance:OpenGuildJiuSheDaoFBFollowView()

    self:ClickHelper()

    self.main_role_relive_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_REALIVE, BindTool.Bind1(self.OnMainRoleRelive, self))
end

function JiuSheDaoSceneLogic:OnMainRoleRelive()
    --self.curr_jump = nil
    Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
end

function JiuSheDaoSceneLogic:Out()
    GuildWGCtrl.Instance:CloseGuildJiuSheDaoFBFollowView()
    --UiInstanceMgr.Instance:HideFBLeaveCountDown()
    FuBenPanelCountDown.Instance:CloseViewHandler()
    MainuiWGCtrl.Instance:ResetTaskPanel()
    CommonFbLogic.Out(self)
    if nil ~= self.clickHandle and nil ~= ClickManager.Instance then
        ClickManager.Instance:UnlistenClickGround(self.clickHandle)
        self.clickHandle = nil
    end

    self.curr_jump = nil
    GlobalEventSystem:UnBind(self.main_role_relive_event)
    self.main_role_relive_event = nil
end

function JiuSheDaoSceneLogic:OpenFbSceneCd()
    local data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.JIUSHEDAO)
    local out_time = data and data.next_time or 0
    if out_time > TimeWGCtrl.Instance:GetServerTime() then
        MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time)
    end
end

function JiuSheDaoSceneLogic:FindJumpPointForSingle(x, y)
    -- print_error("======",x,y)
    local jump_obj_list = Scene.Instance:GetObjListByType(SceneObjType.JumpPoint)
    for k, v in pairs(jump_obj_list) do
        if v ~= nil and v.vo ~= nil then
            local range = v.vo.range
            -- if v.vo.x == x and v.vo.y == y then
            --     return v
            -- end
            local point_distance = GameMath.GetDistance(x, y, v.vo.pos_x, v.vo.pos_y, true)
            if point_distance <= range then
                return v
            end
        end
    end
    return nil
end

function JiuSheDaoSceneLogic:FindJumpPointForSingleByIndex(target_id)
    local jump_obj_list = Scene.Instance:GetObjListByType(SceneObjType.JumpPoint)
    for k, v in pairs(jump_obj_list) do
        if v ~= nil and v.vo ~= nil then
            local range = v.vo.range
            if v.vo.target_id == target_id then
                return v
            end
        end
    end
    return nil
end

function JiuSheDaoSceneLogic:ClickHelper()
    if nil ~= ClickManager.Instance then
        if nil ~= self.clickHandle and nil ~= ClickManager.Instance then
            ClickManager.Instance:UnlistenClickGround(self.clickHandle)
            self.clickHandle = nil
        end

        self.clickHandle = ClickManager.Instance:ListenClickGround(BindTool.Bind1(self.ClickScene, self))
    end
end

function JiuSheDaoSceneLogic:ClickScene(hit)
    if self.is_jumping then
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil or main_role:IsDeleted() or main_role:IsRealDead() then
        return
    end
    local x, y = GameMapHelper.WorldToLogic(hit.point.x, hit.point.z)
    --print_error("===========",x,y)

    --if GuildWGData.Instance:GetJiuSheDaoFBJumpArea(x,y)==false then print_error("不能跳到当前距离") return end
    local is_click_jump_point, point_x, point_y = self:CheckCanJump(x, y, main_role)
    -- print_error("GetJiuSheDaoFBJumpArea", is_click_jump_point, point_x, point_y)
    if is_click_jump_point == false then
        main_role:DoMoveByClick(x, y, 0, function ()--进入BOSS地图注释
            GuajiWGCtrl.Instance:ClearAllOperate()
        end)
        return
    end

    if point_x == 0 then
        return
    end
    self:DoJump(point_x,point_y)
end

function JiuSheDaoSceneLogic:DoJump(x, y)
	local main_role = Scene.Instance:GetMainRole()
    if main_role == nil or main_role:IsDeleted() or main_role:IsRealDead() then
        return
    end
    if self.jump_end_call_back == nil then
        self.jump_end_call_back = BindTool.Bind1(self.JumpEndCallBack, self)
    end
    GuajiWGCtrl.Instance:ClearAllOperate()
    self.is_jumping = true
    GuildWGCtrl.Instance:SendJiuSheDaoFBHandler(JIUSHEDAOFB_REQUESET_REASON.REQUESET_REASON_JUMP, nil, x, y)

    local role_x,role_y = main_role:GetLogicPos()
    local distance = GameMath.GetDistance(role_x, role_y, x, y, true)
    main_role:SetSpecialJumpSpeed(distance)
    main_role:DoSpecialJump(x, y, self.jump_end_call_back)
end

function JiuSheDaoSceneLogic:CheckCanJump(click_x, click_y, main_role)
    local rx, ry = main_role:GetLogicPos()
    local gd = GuildWGData.Instance
    local is_click_jump_point, point_x, point_y = gd:GetJiuSheDaoFBJumpArea(click_x, click_y)
    if is_click_jump_point then

        local is_stand_on_zhuang = gd:GetJiuSheDaoFBJumpArea(rx, ry)
        if is_stand_on_zhuang then
            return true, point_x, point_y
        else
            local distance = GameMath.GetDistance(point_x, point_y, rx, ry, true)
            --print_error("测试distance==========",distance)
            if distance < 15 then
                return true, point_x, point_y
            else
                self.jump_point_x = point_x
                self.jump_point_y = point_y
                -- print_error("CheckCanJump",point_x,point_y)
                local near_pos_x, near_pos_y = self:CalcStartJumpPoint(point_x, point_y, main_role)
                --print_error("CheckCanJump",point_x,point_y)
                local gjc = GuajiWGCtrl.Instance
                if self.role_move_call_back == nil then
                    self.role_move_call_back = BindTool.Bind(self.RoleMoveCallBack, self)
                end
                --print_error("near pos ",GameMapHelper.LogicToWorld(near_pos_x, near_pos_y))
                gjc:SetMoveToPosCallBack(self.role_move_call_back)
                gjc:MoveToPos(Scene.Instance:GetSceneId(), near_pos_x, near_pos_y, nil)
                return true, 0, 0
            end
        end

    end
    if gd:GetJiuSheDaoFBJumpArea(rx, ry) then
        local is_on_pingtai, idx = gd:GetIsInJiuSheDaoFBIsPlatformArea(click_x, click_y)
        if is_on_pingtai then
            if not AStarFindWay:IsBlock(click_x, click_y) then
                return true, click_x, click_y
            else
                local cfg = GuildWGData.Instance:GetCenterPointByIndex(idx)
                return true,cfg.x,cfg.y
            end
        end
    end
    return false, 0, 0
end

function JiuSheDaoSceneLogic:RoleMoveCallBack()
    -- print_error("RoleMoveCallBack.........")
    self:DoJump(self.jump_point_x,self.jump_point_y)
end


function JiuSheDaoSceneLogic:JumpEndCallBack()
    --print_error("JumpEndCallBack")
    GuildWGData.Instance:GetJiuSheDaoFBRoleState()
    --self.curr_jump = nil
    self.is_jumping = false
end

function JiuSheDaoSceneLogic:CalcStartJumpPoint(click_x, click_y, main_role)
    local role_real_x, role_real_y = main_role:GetRealPos()
    local click_real_x, click_real_y = GameMapHelper.LogicToWorld(click_x, click_y)
    local move_dir = self:GetMoveDir(click_real_x, click_real_y, role_real_x, role_real_y)
    local error_pos_x, error_pos_y = self:FindIllicitPos(move_dir, 0.5, click_real_x, click_real_y)
    -- print_error("error x", GameMapHelper.LogicToWorld(error_pos_x, error_pos_y))
    click_real_x, click_real_y = GameMapHelper.LogicToWorld(error_pos_x, error_pos_y)

    local distance = GameMath.GetDistance(click_real_x, click_real_y, role_real_x, role_real_y, true)
    -- print_error("distance", distance)
    return self:FindLegitimatePos(move_dir, 0.5, click_real_x, click_real_y)
end

function JiuSheDaoSceneLogic:FindLegitimatePos(move_dir, distance, origin_x, origin_y)
    local logic_x, logic_y = nil, nil
    local target_pos = u3d.v2Mul(move_dir, distance)
    target_pos.x = target_pos.x + origin_x
    target_pos.y = target_pos.y + origin_y
    logic_x, logic_y = GameMapHelper.WorldToLogic(target_pos.x, target_pos.y)
    if AStarFindWay:IsBlock(logic_x, logic_y) then
        return self:FindLegitimatePos(move_dir, distance + 0.5, origin_x, origin_y)
    else
        return logic_x, logic_y
    end
end

function JiuSheDaoSceneLogic:FindIllicitPos(move_dir, distance, origin_x, origin_y)
    local logic_x, logic_y = nil, nil
    local target_pos = u3d.v2Mul(move_dir, distance)
    target_pos.x = target_pos.x + origin_x
    target_pos.y = target_pos.y + origin_y
    logic_x, logic_y = GameMapHelper.WorldToLogic(target_pos.x, target_pos.y)
    if AStarFindWay:IsBlock(logic_x, logic_y) then
        return logic_x, logic_y
    else
        return self:FindIllicitPos(move_dir, distance + 0.5, origin_x, origin_y)
    end
end

function JiuSheDaoSceneLogic:GetMoveDir(click_real_x, click_real_y, target_real_x, target_real_y)
    local delta_pos = u3d.v2Sub(u3d.vec2(target_real_x, target_real_y), u3d.vec2(click_real_x, click_real_y))
    return u3d.v2Normalize(delta_pos)
end
