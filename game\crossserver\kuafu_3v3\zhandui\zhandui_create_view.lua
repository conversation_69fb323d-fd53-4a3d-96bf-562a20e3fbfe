--创建战队弹窗
ZhanDuiCreateView = ZhanDuiCreateView or BaseClass(SafeBaseView)

function ZhanDuiCreateView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(598, 420)})
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_create")
end

function ZhanDuiCreateView:ReleaseCallBack()
    self.last_edit_time = nil
end

function ZhanDuiCreateView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.KuafuPVP.ViewName_CreateZhanDui
	--self:SetSecondView(nil, self.node_list["size"])
    XUI.AddClickEventListener(self.node_list.btn_create, BindTool.Bind(self.OnClickCreate, self))
    self.node_list.name_inputfield.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnEditValueChange, self))
    self.node_list.name_inputfield.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnEndEdit, self))
end

function ZhanDuiCreateView:OnEditValueChange(str)
    local len, table = CheckStringLen(str, COMMON_CONSTS.MAX_ZHAN_DUI_NAME_LEN)
    
    if not len then
        if table then
            local str = ""
            for i = 1, #table do
                str = str .. table[i]
            end
            self.node_list.name_inputfield.input_field.text = str
        end
        if self.last_edit_time and self.last_edit_time > Status.NowTime then
            return
        end
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.CreateMaxFontCount)
        self.last_edit_time = Status.NowTime + 0.5
    end
end

function ZhanDuiCreateView:OnEndEdit(str)
end

function ZhanDuiCreateView:OnClickCreate()
    local str = self.node_list.name_inputfield.input_field.text
    local len, table = CheckStringLen(str, COMMON_CONSTS.MAX_ZHAN_DUI_NAME_LEN)
    if string.len(str) <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.GreateNilFontCount)
        return
    end
    if not len then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.CreateMaxFontCount)
        return
    end
    if ChatFilter.Instance:IsIllegal(str, true) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
		return
	end
    --TODO send msg
    ZhanDuiWGCtrl.Instance:ReqCreateZhanDui(str)
     RoleWGData.SetRolePlayerPrefsInt("is_creat_zhandui", 1)
end