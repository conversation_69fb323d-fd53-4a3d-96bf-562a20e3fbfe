﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Game_LoadRawImageWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Game.LoadRawImage), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("SetTexture", SetTexture);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("BundleName", get_BundleName, set_BundleName);
		<PERSON><PERSON>("AssetName", get_AssetName, set_AssetName);
		<PERSON><PERSON>("AutoFitNativeSize", get_AutoFitNativeSize, set_AutoFitNativeSize);
		<PERSON><PERSON>("AutoUpdateAspectRatio", get_AutoUpdateAspectRatio, set_AutoUpdateAspectRatio);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTexture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Game.LoadRawImage obj = (Game.LoadRawImage)ToLua.CheckObject<Game.LoadRawImage>(L, 1);
			UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 2, typeof(UnityEngine.Texture2D));
			obj.SetTexture(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BundleName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			string ret = obj.BundleName;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BundleName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AssetName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			string ret = obj.AssetName;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AssetName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoFitNativeSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			bool ret = obj.AutoFitNativeSize;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoFitNativeSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoUpdateAspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			bool ret = obj.AutoUpdateAspectRatio;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoUpdateAspectRatio on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_BundleName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.BundleName = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BundleName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AssetName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.AssetName = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AssetName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoFitNativeSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoFitNativeSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoFitNativeSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoUpdateAspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.LoadRawImage obj = (Game.LoadRawImage)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoUpdateAspectRatio = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoUpdateAspectRatio on a nil value");
		}
	}
}

