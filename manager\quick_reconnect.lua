--[[
-- 废弃的
--]]
local QuickReconnect = {}
local quick_login_callback = nil
IS_MSG_ENCRYPT = false

function QuickReconnect:Reconnect()
	self:CacheData()
	GameRestart()
end

function QuickReconnect:CacheData()
	local user_vo = GameVoManager.Instance:GetUserVo()
	if user_vo.is_in_game_server then
		UtilU3d.CacheData("is_quick_reconnect_ing", true)
		UtilU3d.CacheData("prve_role_id", user_vo.origin_role_id)
		UtilU3d.CacheData("prve_server_id", user_vo.real_server_id)
		UtilU3d.CacheData("prve_plat_server_id", user_vo.plat_server_id)
		UtilU3d.CacheData("plat_server_name", user_vo.plat_server_name or "")
		UtilU3d.CacheData("prve_account_user_id", user_vo.account_user_id)
		UtilU3d.CacheData("prve_login_ip", GameNet.Instance:GetLoginServerIp())
		UtilU3d.CacheData("prve_login_port", GameNet.Instance:GetLoginServerPort())
	end
end

function QuickReconnect:IsQuickLogin()
	local is_quick = UtilU3d.GetCacheData("is_quick_reconnect_ing")
	if is_quick then
		-- 快速重连的时候也要检测这个服能不能上
		local prve_server_id = UtilU3d.GetCacheData("prve_server_id")
		if prve_server_id ~= nil and prve_server_id ~= 0 then
			if LoginWGData ~= nil and LoginWGData.Instance ~= nil and not LoginWGData.Instance:IsCanLoginServer(prve_server_id) then
				is_quick = false
				UtilU3d.CacheData("select_role_state", 0)
				UtilU3d.CacheData("is_quick_reconnect_ing", false)
			end
		end
	end

	return is_quick
end

function QuickReconnect:TryQuickLogin(callback)
	if not UtilU3d.GetCacheData("is_quick_reconnect_ing") then
		print_error("[QuickReconnect] big bug， 当前不是在断线重连状态")
		return
	end

	UtilU3d.CacheData("is_quick_reconnect_ing", false)
	self.prve_role_id = UtilU3d.GetCacheData("prve_role_id")
	self.prve_server_id = UtilU3d.GetCacheData("prve_server_id")
	self.prve_plat_server_id = UtilU3d.GetCacheData("prve_plat_server_id")
	self.prve_account_user_id = UtilU3d.GetCacheData("prve_account_user_id")
	self.prve_login_ip = UtilU3d.GetCacheData("prve_login_ip")
	self.prve_login_port = UtilU3d.GetCacheData("prve_login_port")
	self.plat_server_name = UtilU3d.GetCacheData("plat_server_name")

	quick_login_callback = callback
	LoginWGCtrl.Instance:UnRegisterLoginEvents()		-- 去掉loginctrl的监听
	self:ConnectServer()							-- 连接服务器
end

function QuickReconnect:GetReloadFiles(reload_files)
	local file_list = Split(reload_files or "", "|")
	return file_list
end

function QuickReconnect:ReLoadFiles(reload_files)
	for _, v in pairs(reload_files) do
		if "" ~= v then
			print("hotupdate:", v)
			_G.package.loaded[v] = nil
			require(v)
		end
	end
end

function QuickReconnect:PreCheckReloadFiles(reload_files)
	for _, v in pairs(reload_files) do
		assert(loadfile(string.gsub(v, "%.", "/")))
	end

	return true
end

function QuickReconnect:ConnectServer()
	self:RegisterProtocols()
	self:ReqConnectLoginServer()
end

function QuickReconnect:RegisterProtocols()
	local list = {
		{SCLoginAck, "OnLoginAck"},
		{SCRoleListAck, "OnRoleListAck"},
		{SCAccountKeyError, "OnAccountKeyError"},
		{SCMergeRoleListAck, "OnMergeRoleListAck"},
		{SCUserEnterGSAck, "OnUserEnterGSAck"},
		{SCDisconnectNotice, "OnDisconnectNotice"},
	}

	for _, v in ipairs(list) do
		local msg_type = ProtocolPool.Instance:Register(v[1], true)
		GameNet.Instance:RegisterMsgOperate(msg_type, BindTool.Bind1(self[v[2]], self))
	end
end

function QuickReconnect:OnAccountKeyError(protocol)
	if protocol.result == ACCOUNT_KEY_ERROR.MD5 then
		TipWGCtrl.Instance:ShowSystemMsg("MD5 Error !!!")
	end
end

function QuickReconnect:ReqConnectLoginServer()
	local user_vo = GameVoManager.Instance:GetUserVo()
	user_vo.plat_server_id = self.prve_server_id
	user_vo.account_user_id = self.prve_account_user_id
	user_vo.plat_server_name = self.plat_server_name or ""
	GameNet.Instance:SetLoginServerInfo(self.prve_login_ip, self.prve_login_port)

	print("[QuickReconnect] start async connect login server", user_vo.plat_server_id, user_vo.account_user_id, self.prve_login_ip, self.prve_login_port)
	GameNet.Instance:AsyncConnectLoginServer(5)
	GlobalEventSystem:Bind(LoginEventType.LOGIN_SERVER_CONNECTED, BindTool.Bind(self.OnConnectLoginServer, self))
	GlobalEventSystem:Bind(LoginEventType.LOGIN_SERVER_DISCONNECTED, BindTool.Bind(self.OnDisconnectLoginServer, self))
end

function QuickReconnect:OnDisconnectLoginServer(custom_disconnect_reason, custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
	print("[QuickReconnect] login server disconnect")
	if self.login_server_heartbeat_timer then
		GlobalTimerQuest:CancelQuest(self.login_server_heartbeat_timer)
		self.login_server_heartbeat_timer = nil
	end

	-- 非手动断线，显示断线提示
	if real_disconnect_reason ~= GameNet.DisconnectReason.Manual then
		TipWGCtrl.Instance:ShowDisconnected(custom_disconnect_notice_type)
	end
end

function QuickReconnect:OnConnectLoginServer(is_suc)
	print("[QuickReconnect] connect login server success")
	if is_suc then
		-- ReportManager:Step(Report.STEP_LOGIN_SERVER_CONNECTED)
		if IS_MSG_ENCRYPT then
			GameNet.Instance:GetGameServerNet():ClearEncryptKey()
		end

		self:ReqLogin()
	else
		-- print_error("[QuickReconnect] OnConnectLoginServer fail")
		-- ReportManager:Step(Report.STEP_LOGIN_SERVER_CONNECTED_FAILED)
		TipWGCtrl.Instance:ShowDisconnected(DISCONNECT_NOTICE_TYPE.CONNECT_LOGIN_SERVER_ERROR)
		if quick_login_callback ~= nil then
			quick_login_callback(false)
		end
	end
end

function QuickReconnect:ReqLogin()
	print("[QuickReconnect] ReqLogin")

	local user_vo = GameVoManager.Instance:GetUserVo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSLoginReq)
	protocol.rand_1 = math.floor(math.random(1000000, ********))
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.rand_2 = math.floor(math.random(1000000, ********))
	protocol.plat_name = user_vo.account_user_id
	protocol.plat_server_id = user_vo.plat_server_id
	protocol.access_token = user_vo.access_token
	
	print("[QuickReconnect] ReqLogin send", protocol.account_user_id, protocol.plat_server_id)
	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())

	if not self.login_server_heartbeat_timer then
		self.login_server_heartbeat_timer = GlobalTimerQuest:AddRunQuest(function()
				local cmd = ProtocolPool.Instance:GetProtocol(CSLHeartBeat)
				cmd:EncodeAndSend(GameNet.Instance:GetLoginNet())
			end, 10)
	end
end

function QuickReconnect:OnRoleListAck(protocol)
	print("[QuickReconnect] OnRoleListAck", protocol.result)

	if nil ~= LoginWGData.Instance then
		LoginWGData.Instance:DeleteMe()
	end

	self.login_data = LoginWGData.New()
	self.login_data:SetRoleListAck(protocol)
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
	local user_vo = GameVoManager.Instance:GetUserVo()

	if 0 == protocol.result and protocol.count > 0 then
		local role_id = self.prve_role_id
		print("[QuickReconnect] OnRoleListAck succ", protocol.count, self.prve_role_id)
		for _, v in pairs(protocol.role_list) do
			if self.prve_role_id == v.role_id then
				user_vo:SetNowRole(v.role_id)
				mainrole_vo.name = v.role_name
				self.SendRoleReq()
				break
			end
		end

	elseif -6 == protocol.result then -- 没有角色
		if 0 == #protocol.role_list then
			CacheManager.Instance:ClearAllCache()
		end
		print_error("[QuickReconnect] Big Bug, OnRoleListAck 没有角色!!!")
		TipWGCtrl.Instance:ShowSystemMsg("重连失败,没有找到角色")
	end
end

function QuickReconnect:OnMergeRoleListAck(protocol)
	print("[QuickReconnect] OnMergeRoleListAck", protocol.count)
	if nil ~= self.login_data then
		self.login_data:DeleteMe()
	end
	self.login_data = LoginWGData.New()
	self.login_data:SetRoleListAck(protocol)
	if protocol.count == 0 then
		print_error("[QuickReconnect] Big Bug, OnMergeRoleListAck 没有角色!!!")
		TipWGCtrl.Instance:ShowSystemMsg("重连失败,没有找到角色")
	else
		local user_vo = GameVoManager.Instance:GetUserVo()
		user_vo:ClearRoleList()
		for i = 1, protocol.count do
			user_vo:AddRole(
				protocol.role_list[i].role_id,
				protocol.role_list[i].role_name,
				protocol.role_list[i].avatar,
				protocol.role_list[i].sex,
				protocol.role_list[i].prof,
				protocol.role_list[i].country,
				protocol.role_list[i].level,
				protocol.role_list[i].create_time,
				protocol.role_list[i].online_time,
				protocol.role_list[i].last_logout_time,
				protocol.role_list[i].capability,
				protocol.role_list[i].avatar_key_big,
				protocol.role_list[i].avatar_key_small
				)
		end

		for _, v in pairs(protocol.role_list) do
			if self.prve_role_id == v.role_id then
				user_vo:SetNowRole(v.role_id)
				local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
				mainrole_vo.name = v.role_name
				self.SendRoleReq()
				break
			end
		end
	end
end

function QuickReconnect.SendRoleReq()
	print("[QuickReconnect] SendRoleReq")
	local user_vo = GameVoManager.Instance:GetUserVo()
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()

	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleReq)
	protocol.rand_1 = math.floor(math.random(1, ********))
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.rand_2 = math.floor(math.random(1000000, ********))
	protocol.role_id = mainrole_vo.role_id
	protocol.plat_name = user_vo.account_user_id
	protocol.plat_server_id = user_vo.plat_server_id
	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
end

function QuickReconnect:OnLoginAck(protocol)
	TimeWGCtrl.Instance:SetServerTime(protocol.server_time, protocol.pi_time)

	local error_code = protocol.result
	if 0 ~= error_code then
		TipWGCtrl.Instance:ShowSystemMsg(string.format("登陆认证失败: %d.", error_code))

		if LoginAckResult.LOGIN_LOGIN_FORBID == error_code then
			local str = Language.Login.CountBeBand
			local ok_func = function ()
				GlobalEventSystem:Fire(LoginEventType.LOGOUT)
			end
			TipWGCtrl.Instance:OpenConfirmAlertTips(str, ok_func)
		else
			local disconnect_notice_type = LOGIN_ACK_RESULT_TO_DISCONNECT_NOTICE_TYPE[error_code]
										or DISCONNECT_NOTICE_TYPE.CONNECT_LOGIN_SERVER_ERROR
			TipWGCtrl.Instance:ShowDisconnected(disconnect_notice_type)
		end

		if quick_login_callback ~= nil then
			quick_login_callback(false)
		end
		return
	end

	print("[QuickReconnect] OnLoginAck hostname:" .. protocol.gs_hostname .. "  prot:" .. protocol.gs_port)
	GameNet.Instance:SetGameServerInfo(protocol.gs_hostname, protocol.gs_port)
	local user_vo = GameVoManager.Instance:GetUserVo()
	user_vo:SetNowRole(protocol.role_id)
	user_vo.login_time = protocol.time
	user_vo.session_key = protocol.key
	user_vo.anti_wallow = protocol.anti_wallow
	user_vo.scene_id = protocol.scene_id
	user_vo.last_scene_id = protocol.last_scene_id
	GameNet.Instance:DisconnectLoginServer()

	self:ReqConnectGameServer()
end

function QuickReconnect:ReqConnectGameServer()
	GameNet.Instance:AsyncConnectGameServer(5)

	GlobalEventSystem:Bind(LoginEventType.GAME_SERVER_CONNECTED, function(is_succ)
		print("[QuickReconnect] connect game server success")
		TipWGCtrl.Instance:CloseDisconnected()

		if is_succ then
			-- ReportManager:Step(Report.STEP_CONNECT_GAME_SERVER)
			if IS_MSG_ENCRYPT then
				GameNet.Instance:GetGameServerNet():TrySendEncryptKeyToServer()
			end

			self:SendUserEnterGSReq()
		else
			-- ReportManager:Step(Report.STEP_CONNECT_GAME_SERVER_FAILED)
			TipWGCtrl.Instance:ShowSystemMsg("登陆游戏服务器失败")
			TipWGCtrl.Instance:ShowDisconnected(DISCONNECT_NOTICE_TYPE.CONNECT_GAME_SERVER_ERROR)
			if quick_login_callback ~= nil then
				quick_login_callback(false)
			end
		end
	end)

	GlobalEventSystem:Bind(LoginEventType.GAME_SERVER_DISCONNECTED, function(custom_disconnect_reason, custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
		print("[QuickReconnect] game server disconnect")
		if real_disconnect_reason == GameNet.DisconnectReason.Manual then
			return
		end

		-- ReportManager:Step(Report.STEP_DISCONNECT_GAME_SERVER)
		if TipWGCtrl.Instance ~= nil then
			TipWGCtrl.Instance:ShowDisconnected(custom_disconnect_notice_type)
		end
	end)
end

function QuickReconnect.SendUserEnterGSReq()
	local user_vo = GameVoManager.Instance:GetUserVo()
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()

	local protocol = ProtocolPool.Instance:GetProtocol(CSUserEnterGSReq)
	protocol.scene_id = user_vo.scene_id
	protocol.scene_key = 0
	protocol.last_scene_id = user_vo.last_scene_id
	protocol.role_id = mainrole_vo.role_id
	protocol.role_name = mainrole_vo.role_name
	protocol.time = user_vo.login_time
	protocol.is_login = 1
	protocol.server_id = mainrole_vo.server_id
	protocol.key = user_vo.session_key
	protocol.plat_name = user_vo.account_user_id
	protocol.is_micro_pc = 0
	protocol.plat_spid = tostring(GLOBAL_CONFIG.local_package_info.config.agent_id)
	protocol:EncodeAndSend(GameNet.Instance:GetGameServerNet())
	if GlobalLocalRoleId == nil then
		GlobalLocalRoleId = protocol.role_id
	end

	print("[QuickReconnect] SendUserEnterGSReq name=" .. mainrole_vo.role_name.."server_id="..mainrole_vo.server_id)
end

function QuickReconnect:OnUserEnterGSAck(protocol)
	print("[QuickReconnect] OnUserEnterGSAck result=" .. protocol.result)
	if 0 == protocol.result then
		GlobalEventSystem:Fire(LoginEventType.ENTER_GAME_SERVER_SUCC)
		self.enter_gs_count = 0
	elseif -1 == protocol.result then
		self.enter_gs_count = (self.enter_gs_count or 0) + 1
		if self.enter_gs_count >= 5 then
			self.enter_gs_count = 0
		else
			self.enter_gs_timer = GlobalTimerQuest:AddDelayTimer(
				function() self.SendUserEnterGSReq() end, 0.1)
		end
	elseif -3 == protocol.result then
		-- 玩家在跨服，请求重连
		-- ReportManager:Step(Report.STEP_ENTER_GS_ACK_FAILED)
		self.enter_gs_count = (self.enter_gs_count or 0) + 1
		if self.enter_gs_count >= 5 then
			self.enter_gs_count = 0
		else
			self.enter_gs_timer = GlobalTimerQuest:AddDelayTimer(
				function() self.SendUserEnterGSReq() end, 3)
		end
	else
		self.enter_gs_count = 0
		print_log("LoginWGCtrl:OnUserEnterGSAck", protocol.result)
	end

	if 0 == protocol.result and not IS_ON_CROSSSERVER then
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local data_list = self.login_data:GetRoleListAck()
		local index = nil
		for k,v in pairs(data_list.role_list) do
			if mainrole_vo.role_id == v.role_id then
				index = k
			end
		end
		index = index or #data_list.role_list + 1
		PlayerPrefsUtil.SetString("SAVE_UP_LOGIN", index)

		--开启冒险的时候保存用户名，之前是在AgentView:OnLoginClick()储存的但村的过早
		local uservo = GameVoManager.Instance:GetUserVo()
		uservo.is_in_game_server = true
		PlayerPrefsUtil.SetString("login_account_name", uservo.account_user_id)
		PlayerPrefsUtil.SetInt("login_account_uid", mainrole_vo.role_id)
		if quick_login_callback ~= nil then
			quick_login_callback(true)
		end
	end
end

function QuickReconnect:OnDisconnectNotice(protocol)
	GameNet.Instance.custom_disconnect_notice_type = protocol.reason
end

return QuickReconnect