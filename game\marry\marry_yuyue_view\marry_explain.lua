MarryExplainView = MarryExplainView or BaseClass(SafeBaseView)

function MarryExplainView:__init()
	self.view_style = ViewStyle.Window
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_explain")

	self.marry_role_info = {}
end

function MarryExplainView:__delete()

end

function MarryExplainView:ReleaseCallBack()
	if self.alert_attr_window then
		self.alert_attr_window:DeleteMe()
		self.alert_attr_window = nil
	end	
end

function MarryExplainView:LoadCallBack()
	-- XUI.AddClickEventListener(self.node_list["btn_marry_flow"], BindTool.Bind(self.OnClickMarryFlow, self)) 	--结婚流程
	-- XUI.AddClickEventListener(self.node_list["btn_intimacy_tips"], BindTool.Bind(self.OnClickIntimacyTips, self)) --亲密度说明
	XUI.AddClickEventListener(self.node_list["layout_xunyuan"], BindTool.Bind1(self.OnClickMarryXunYuan, self))
	XUI.AddClickEventListener(self.node_list["layout_qiuhun"], BindTool.Bind1(self.OnClickMarryQiuHun, self))
	XUI.AddClickEventListener(self.node_list["layout_yuyue"], BindTool.Bind1(self.OnClickMarryYuYue, self))
	XUI.AddClickEventListener(self.node_list["layout_hunyan"], BindTool.Bind1(self.OnClickInviteGuests, self))
	-- XUI.AddClickEventListener(self.node_list["layout_gohunyan"], BindTool.Bind1(self.OnClickGoHunYan, self), true) --参加婚宴 修改为离婚功能
	XUI.AddClickEventListener(self.node_list["layout_divorce"], BindTool.Bind1(self.OnDivorceClick, self))
end

function MarryExplainView:ShowIndexCallBack()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_LOVER_INFO_REQ)
    MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_FLAG)     -- 预约所有状态
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_GET_YUYUE_INFO)    -- 获取预约列表信息
end

function MarryExplainView:OnFlush()
    self.node_list["red_yuyue"]:SetActive(MarryWGData.Instance:GetMarryYuyueRemind() == 1)
end

function MarryExplainView:OnClickMarryXunYuan()
	MarryWGCtrl.Instance:OpenXunYuanView()
	self:Close()
end

function MarryExplainView:OnClickMarryQiuHun()
	MarryWGCtrl.Instance:OpenTiQinView()
	self:Close()
end

function MarryExplainView:OnClickMarryYuYue()
	self.marry_role_info = MarryWGData.Instance:GetYuYueRoleInfo()
	
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	if lover_id > 0 or self.marry_role_info.marry_state == 1 then
		MarryWGCtrl.Instance:OpenYuYueView()
		self:Close()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YuYueTips2)
	end
end

function MarryExplainView:OnDivorceClick()
	if RoleWGData.Instance.role_vo.lover_uid <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Marry.DivorceError)
		return
	end

	local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	if other_cfg ~= nil then
		local divorce_coin_cost = other_cfg.divorce_coin_cost or 0
		local divorce_qingyuan_dec = other_cfg.divorce_qingyuan_dec or 0
		local divorce_intimacy_dec = other_cfg.divorce_intimacy_dec or 0
		local divorce_name = RoleWGData.Instance.role_vo.lover_name or ""
		if divorce_coin_cost > 10000 then
			divorce_coin_cost = math.floor(divorce_coin_cost / 10000) .. Language.Common.Wan
		end

		-- 离婚弹窗二次确认。
		if nil == self.alert_attr_window then
			self.alert_attr_window = Alert.New()
		end
		self.alert_attr_window:SetLableString(string.format(Language.Marry.DivorceConfirm,divorce_name))
		self.alert_attr_window:SetOkFunc(
			function()
				MarryWGCtrl.Instance.SendCSQingyuanDivorceReqCS()
			end)
		self.alert_attr_window:Open()
	end
end

function MarryExplainView:OnClickInviteGuests()
	local sequence = MarryWGData.Instance:GetCurWeddingSequence()
	if sequence ~= MARRY_WEDDING_TYPE.NONE then
		MarryWGCtrl.Instance:OpenInviteView()
		self:Close()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YuYueTips3)
	end
end

function MarryExplainView:OnClickGoHunYan()
	self.marry_role_info = MarryWGData.Instance:GetYuYueRoleInfo()
	if self.marry_role_info.marry_state > 2 then
		local id = WeddingWGData.Instance:GetScenceId()
		WeddingWGCtrl.Instance:SendJoinHunyan(id)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YuYueTips4)
	end
end

function MarryExplainView:OnClickMarryFlow()
	MarryView.OpenTips(Language.Marry.MarryFlowTips, Language.Marry.MarryFlowTitle)
end

function MarryExplainView:OnClickIntimacyTips()
	MarryView.OpenTips(Language.Marry.IntimacyTips, Language.Marry.IntimacyTitle)
end

