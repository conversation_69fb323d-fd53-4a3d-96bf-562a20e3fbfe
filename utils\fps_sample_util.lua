------------------------------------------------------
--帧频采样工具,在处于低频时将对发警告级别
--<AUTHOR>
------------------------------------------------------
CC_DIRECTOR_STATS_INTERVAL = 0.1

FpsSampleUtil = FpsSampleUtil or BaseClass()
function FpsSampleUtil:__init()
	if FpsSampleUtil.Instance ~= nil then
		Error("FpsSampleUtil has been created.")
	end
	FpsSampleUtil.Instance = self

	self.frames = 0									-- 帧数
	self.accum_dt = 0								-- 时间
	self.frame_rate = 0								-- 帧率

	self.is_open_fps_sample = true 					-- 是否开启帧频采样功能(通过此值开启或关闭采样功能)
	self.sample_invalid = false 					-- 采样是否有效
	self.low_fps_warn_callback = nil
	self.cancle_low_fps_warn_callback = nil
	self.is_in_low_warning = false 					-- 是否在低频警告中

	self.fps_sample_max_rate = 0.2 					-- 达到n值时将发出低频警告
	self.fps_sample_value = 25 						-- 低于n帧认为是低频
	self.fps_smaple_time = 10 						-- 采样总时间
	self.fps_check_time_gap = 0.2 					-- 采样间隔时间
	
	self.fps_start_sample_time = 0 					-- 开始采样时间点
	self.fps_sample_total_times = 0					-- 采样总次数
	self.fps_sample_low_rate_times = 0				-- 采样到的低频次数
	self.fps_prve_sample_time = 0 					-- 上次采样检查时间	

	self.prve_warn_time = 0							-- 上次发出警告时间
	self.cancle_warn_time = 10 	       				-- 发出警告后n久时间取消警告

	Runner.Instance:AddRunObj(self, 1)
end

function FpsSampleUtil:__delete()
	FpsSampleUtil.Instance = nil
	Runner.Instance:RemoveRunObj(self)
end

function FpsSampleUtil:SetLowFpsWarnCallback(low_fps_warn_callback)
	self.low_fps_warn_callback = low_fps_warn_callback
end

function FpsSampleUtil:SetCancleLowFpsWarnCallback(cancle_low_fps_warn_callback)
	self.cancle_low_fps_warn_callback = cancle_low_fps_warn_callback
end

function FpsSampleUtil:Update(now_time, elapse_time)
	self:CalcFps()

	if self.is_open_fps_sample and self.sample_invalid then
		self:FpsSample(now_time, elapse_time)
	end
end

--计算帧频
function FpsSampleUtil:CalcFps()
	self.frames = self.frames + 1
	self.accum_dt = self.accum_dt + Status.ElapseTime
	if self.accum_dt > CC_DIRECTOR_STATS_INTERVAL then
		self.frame_rate = self.frames / self.accum_dt
		self.frames = 0
		self.accum_dt = 0
	end
end

--获得当前帧频
function FpsSampleUtil:GetFps()
	return self.frame_rate
end

--设置帧频采样开放
function FpsSampleUtil:SetFpsSampleInvalid(invalid)
	self.sample_invalid = invalid
	if not self.sample_invalid then
		self:StopFpsSample()
	end
end

function FpsSampleUtil:FpsSample(now_time, elapse_time)
	local fps = self.frame_rate
	if fps == 0 or fps > self.fps_sample_value and self.fps_start_sample_time == 0 then
		if self.is_in_low_warning and now_time - self.prve_warn_time > self.cancle_warn_time then
			self:CanCleLowFpsWarn()
		end
		return
	end

	if self.fps_start_sample_time == 0 then
		self.fps_start_sample_time = now_time
	end

	if now_time - self.fps_prve_sample_time >= self.fps_check_time_gap then
		self.fps_prve_sample_time = now_time
		self.fps_sample_total_times = self.fps_sample_total_times + 1
		if fps <= self.fps_sample_value then
			self.fps_sample_low_rate_times = self.fps_sample_low_rate_times + 1 
		end
	end

	if now_time - self.fps_start_sample_time >= self.fps_smaple_time then --采样时间结束
		local low_rate = self.fps_sample_low_rate_times / self.fps_sample_total_times
		if low_rate >= self.fps_sample_max_rate then
			self:LowFpsWarn(low_rate, now_time)
		
		elseif self.is_in_low_warning and now_time - self.prve_warn_time >= self.cancle_warn_time then
			self:CanCleLowFpsWarn()
		end

		self:StopFpsSample()
	end
end

function FpsSampleUtil:StopFpsSample()
	self.fps_start_sample_time = 0
	self.fps_prve_sample_time = 0
	self.fps_sample_low_rate_times = 0
	self.fps_sample_total_times = 0
end

--低频警告
function FpsSampleUtil:LowFpsWarn(low_rate, now_time)
	self.is_in_low_warning = true
	self.prve_warn_time = now_time

	if self.low_fps_warn_callback ~= nil then
		self.low_fps_warn_callback(low_rate)
	end
end

--取消低频警告
function FpsSampleUtil:CanCleLowFpsWarn(low_rate)
	self.is_in_low_warning = false
	if self.cancle_low_fps_warn_callback then
		self.cancle_low_fps_warn_callback(low_rate)
	end
end

