ManHuangGuDianSceneLogic = ManHuangGuDianSceneLogic or BaseClass(CommonFbLogic)

function ManHuangGuDianSceneLogic:__init()
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
end

function ManHuangGuDianSceneLogic:__delete()

end

function ManHuangGuDianSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	local ctrl = MainuiWGCtrl.Instance
	ctrl:AddInitCallBack(nil, function()
		FuBenPanelWGCtrl.Instance:OpenManHuangLogicView()
	end)
	MainuiWGCtrl.Instance:SetFirstEnterFb(true)
end

function ManHuangGuDianSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)
	local ctrl = MainuiWGCtrl.Instance
	ctrl:AddInitCallBack(nil, function()
		FuBenPanelWGCtrl.Instance:CloseManHuangLogicView()
		UiInstanceMgr.Instance:ColseFBStartDown()
	end)
end

function ManHuangGuDianSceneLogic:InitCallBack()
	MainuiWGCtrl.Instance:SetFirstEnterFb(true)
end

function ManHuangGuDianSceneLogic:IsRoleEnemy(target_obj, main_role)
	local target_vo = target_obj:GetVo() or {}
	if target_vo.is_shadow == 1 then
		return false
	end

	return BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
end