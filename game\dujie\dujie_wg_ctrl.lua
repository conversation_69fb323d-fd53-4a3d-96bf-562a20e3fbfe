require("game/dujie/dujie_wg_data")
require("game/dujie/dujie_main_view")
require("game/dujie/dujie_view")
require("game/dujie/dujie_attr_view")
require("game/dujie/dujie_result_fail_view")
require("game/dujie/dujie_result_success_view")
require("game/dujie/dujie_spirit_view")
require("game/dujie/dujie_spirit_image_view")
require("game/dujie/dujie_spirit_buff_view")
require("game/dujie/dujie_spirit_skill_pre_view")
require("game/dujie/dujie_operate_view")
require("game/dujie/dujie_invite_view")
require("game/dujie/dujie_receive_invite_view")
--灵根
require("game/dujie/dujie_body/dujie_body_wg_ctrl")
require("game/dujie/dujie_body/dujie_body_wg_data")
require("game/dujie/dujie_body/dujie_body_view")
require("game/dujie/dujie_body/body_talent_active_result_view")
require("game/dujie/dujie_body/body_talent_show_num_view")
require("game/dujie/dujie_body/body_talent_skill_view")
require("game/dujie/dujie_body/body_reset_tips_view")
require("game/dujie/dujie_body/body_talent_skill_tips_view")



DujieWGCtrl = DujieWGCtrl or BaseClass(BaseWGCtrl)
function DujieWGCtrl:__init()
	if DujieWGCtrl.Instance then
		print_error("[DujieWGCtrl]:Attempt to create singleton twice!")
	end
	DujieWGCtrl.Instance = self

	self.scene_effect_loadr = nil

	self.view = DujieView.New(GuideModuleName.DujieView)
	self.data = DujieWGData.New()
	self.attr_view = DujieAttrView.New()
	self.result_fail_view = DujieResultFailView.New(GuideModuleName.DujieResultFailView)
	self.result_success_view = DujieResultSuccessView.New(GuideModuleName.DujieResultSuccessView)
	self.dujie_spirit_image_view = DujieSpiritImageView.New()
	self.dujie_spirit_buff_view = DujieSpiritBuffView.New()
	self.dujie_spirit_skill_pre_view = DujieSpiritSkillPreView.New()
	self.dujie_operate_view = DujieOperateView.New(GuideModuleName.DujieOperateView)
	self.dujie_invite_view = DujieInviteView.New(GuideModuleName.DujieInviteView)
	self.dikoe_receive_view = DujieReceiveInviteView.New(GuideModuleName.DujieReceiveView)
	
	-- self.esoterica_part_view = EsotericaPartView.New(GuideModuleName.EsotericaPartView)
	-- self.esoterica_reslove_view = EsotericaResloveView.New()
	-- self.privilege_view = CultivationPrivilegeView.New(GuideModuleName.CultivationPrivilegeView)
	self:InitBody()
	self:RegisterProtocol(CSOrdealOperate)
	self:RegisterProtocol(SCOrdealBaseInfo, "OnSCOrdealBaseInfo")
	self:RegisterProtocol(SCOrdealInfo, "OnSCOrdealInfo")
	self:RegisterProtocol(SCOrdealViewInvite, "OnSCOrdealViewInvite")
	self:RegisterProtocol(SCOrdealingInfo, "OnSCOrdealingInfo")

	
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.RolePosChange, self))

	self.tips_time = 0
end

function DujieWGCtrl:__delete()

	self:DeleteSceneEffect()
	self:CleanEndDujieTimer()

	DujieWGCtrl.Instance = nil

	self:DeleteBody()

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.attr_view then
		self.attr_view:DeleteMe()
		self.attr_view = nil
	end

	if self.result_fail_view then
		self.result_fail_view:DeleteMe()
		self.result_fail_view = nil
	end

	
	if self.result_success_view then
		self.result_success_view:DeleteMe()
		self.result_success_view = nil
	end

	if self.dujie_spirit_image_view then
		self.dujie_spirit_image_view:DeleteMe()
		self.dujie_spirit_image_view = nil
	end

	
	if self.dujie_spirit_buff_view then
		self.dujie_spirit_buff_view:DeleteMe()
		self.dujie_spirit_buff_view = nil
	end

	
	if self.dujie_spirit_skill_pre_view then
		self.dujie_spirit_skill_pre_view:DeleteMe()
		self.dujie_spirit_skill_pre_view = nil
	end

	if self.dujie_operate_view then
		self.dujie_operate_view:DeleteMe()
		self.dujie_operate_view = nil
	end

	if self.dujie_invite_view then
		self.dujie_invite_view:DeleteMe()
		self.dujie_invite_view = nil
	end

	if self.dikoe_receive_view then
		self.dikoe_receive_view:DeleteMe()
		self.dikoe_receive_view = nil
	end
	
	
	self:RemoveWaitZhuanShengDelayTimer()
end

function DujieWGCtrl:CleanEndDujieTimer()
	if self.end_dujie_timer then
		GlobalTimerQuest:CancelQuest(self.end_dujie_timer)
		self.end_dujie_timer = nil
	end
end

-----------------发送协议，9248------------------------------
function DujieWGCtrl:SendOrdealOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOrdealOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end


-- 渡劫基础信息
function DujieWGCtrl:GetDujieBaseInfo()
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_BASE_INFO)
end

-- 开始渡劫
function DujieWGCtrl:DujieStart()
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_DO_ORDEAL)
end

-- 取消渡劫
function DujieWGCtrl:DujieCannel()
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_CANNEL_ORDEAL)
end

-- 使用技能
function DujieWGCtrl:UseDujieSkill(skill_id)
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_USE_SKILL,skill_id)
end



------------------------------------------------------------

-----------------接收协议------------------------------
function DujieWGCtrl:OnSCOrdealInfo(protocol)
	local old_data = self.data:GetOldOrdealInfo()
	self.data:SetOrdealInfo(protocol)

	if old_data.ordeal_start_time == 0 and protocol.ordeal_start_time ~= 0 then
		self.data:SetIsInDujie(true)
		ViewManager.Instance:CloseAll()
		-- if self.view and self.view:IsOpen() then
			-- self.view:Flush(0,"start_dujie")


			-- if self.data:GetDujieLevel() > 0 then
				Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_DUJIE)
			-- end
			-- self.view:Close()
			self.dujie_operate_view:Flush(0,"start_dujie")

			-- ViewManager.Instance:Open(GuideModuleName.DujieOperateView)
			-- self:OpenDujieOperateView()
			
		-- end
	elseif old_data.ordeal_start_time ~= 0 and protocol.ordeal_start_time == 0 then

		if protocol.vitality < old_data.vitality or protocol.strength < old_data.strength then
			local harm_num = (old_data.vitality - protocol.vitality) + (old_data.strength - protocol.strength)
			self.dujie_operate_view:Flush(0,"flush_dujie_ing",{harm_num = harm_num})
			self.dujie_operate_view:Flush(0,"show_thunder")
		else
			self.dujie_operate_view:Flush(0,"flush_dujie_ing")
		end

		self.end_dujie_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.data:SetIsInDujie(false)
			Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
			self.dujie_operate_view:Close()
			
		end, 1)

	elseif protocol.ordeal_start_time ~= 0 then
		
		if old_data.next_thunder_index~= 0 and old_data.next_thunder_index ~= protocol.next_thunder_index then
			local harm_num = (old_data.vitality - protocol.vitality) + (old_data.strength - protocol.strength)
			self.dujie_operate_view:Flush(0,"flush_dujie_ing",{harm_num = harm_num})
			self.dujie_operate_view:Flush(0,"show_thunder")

		else
			self.dujie_operate_view:Flush(0,"flush_dujie_ing")
		end
	end

	if protocol.skill_active_time > 0 and  protocol.skill_seq ~= -1 then

		self.dujie_operate_view:Flush(0,"show_skill")
	end

end

function DujieWGCtrl:OnSCOrdealBaseInfo(protocol)
	self.data:SetOrdealBaseInfo(protocol)
	-- self.view:Flush(0, "all", {slot = slot})
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.Dujieing)
	RemindManager.Instance:Fire(RemindName.Dujie_Body)
	ViewManager.Instance:FlushView(GuideModuleName.ZhuanSheng)
end

function DujieWGCtrl:OnSCOrdealViewInvite(protocol)
	if self.data:IsInDujieArea() then
		return
	end

	local is_tips = false
	-- 判断创景是否需要弹出
	local info = FuBenWGData.GetFbSceneConfig(Scene.Instance:GetSceneType())
	if info.is_show_dujie_invite and info.is_show_dujie_invite == 1 then 
		-- 判断今日是否需要弹出
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if PlayerPrefsUtil.GetInt("dujie_receive_tips") ~= open_day then
			is_tips = true
		end
	end
	if is_tips then
		self:OpenDujieReceivreView(protocol)
	else
		DujieWGData.Instance:AddDujieInvite(protocol.invite_uid,protocol.invite_name)
	end

end

-- 
function DujieWGCtrl:OnSCOrdealingInfo(protocol)
	self.data:SetOrderCount(protocol.ordeal_num)
	self.data:SetOrderRangeCount(protocol.ordeal_range_num - 1)
	

	self:ShowSceneEffect()

	if not self.data:IsInDujie() then
		-- print_error("渡劫人数:",protocol.ordeal_num)
		--print_error("渡劫范围人数:",protocol.ordeal_range_num - 1)
		if self.dujie_operate_view and self.dujie_operate_view:IsOpen() then
			self.dujie_operate_view:Flush(0, "flush_watch")
		end	
	end
end



------------------------------------------------------------

------------------刷新View-----------------------------------



------------------------------------------------------------

-----------------打开view------------------------------
function DujieWGCtrl:OpenAttrView()
	if self.attr_view then
		self.attr_view:Open()
	end
end

function DujieWGCtrl:OpenDujieView()
	if self.view then
		DujieWGCtrl.Instance:SetDujieViewInitState(DUJIE_VIEW_STATE.DUJIE)
		self.view:Open(0, "open_dujie")
		self.view:Flush(0, "open_dujie")
	end
end

function DujieWGCtrl:SetDujieViewInitState(view_state)
	self.view:SetInitViewState(view_state)
end

function DujieWGCtrl:OpenDujieFailView()
	if self.result_fail_view then
		self.result_fail_view:Open()
	end
end

function DujieWGCtrl:OpenDujieSuccessView()
	if self.result_success_view then
		self.result_success_view:Open()
	end
end

function DujieWGCtrl:CloseDujieSuccessView()
	if self.result_success_view and self.result_success_view:IsOpen() then
		self.result_success_view:Close()
	end
end

-- 打开形象展示界面
function DujieWGCtrl:OpenDujieSpiritImageView(nuqi_type)
	self.dujie_spirit_image_view:SetCurSelectIndex(nuqi_type)

	if not self.dujie_spirit_image_view:IsOpen() then
		self.dujie_spirit_image_view:Open()
	else
		self.dujie_spirit_image_view:Flush()
	end
end

-- 打开元神专精界面
function DujieWGCtrl:OpenDujieSpiritBuffView(nuqi_type)
	self.dujie_spirit_buff_view:SetCurSelectIndex(nuqi_type)

	if not self.dujie_spirit_buff_view:IsOpen() then
		self.dujie_spirit_buff_view:Open()
	else
		self.dujie_spirit_buff_view:Flush()
	end
end

-- 刷新元神专精界面
function DujieWGCtrl:FlushDujieSpiritBuffView()
	if self.dujie_spirit_buff_view:IsOpen() then
		self.dujie_spirit_buff_view:Flush()
	end
end

-- 打开元神技能预览界面
function DujieWGCtrl:OpenDujieSpiritSkillPreView(nuqi_type)
	self.dujie_spirit_skill_pre_view:SetCurSelectIndex(nuqi_type)

	if not self.dujie_spirit_skill_pre_view:IsOpen() then
		self.dujie_spirit_skill_pre_view:Open()
	else
		self.dujie_spirit_skill_pre_view:Flush()
	end
end

function DujieWGCtrl:OpenDujieOperateView()
	self.view:Close()
	if not self.dujie_operate_view:IsOpen() then
		self.dujie_operate_view:Open()
	else
		self.dujie_operate_view:Flush()
	end
end

function DujieWGCtrl:OpenDujieInviteView()
	if not self.dujie_invite_view:IsOpen() then
		self.dujie_invite_view:Open()
	end
end

function DujieWGCtrl:OpenDujieReceivreView(data)

	if self.dikoe_receive_view then
		self.dikoe_receive_view:SetData(data)
		self.dikoe_receive_view:Open()
	end
end

function DujieWGCtrl:RolePosChange()
    self:UpdateDujieState()
end

function DujieWGCtrl:UpdateDujieState()
	-- 判断是否需要打开渡劫界面
	if self.data:GetIsNeedOpenDujie() then
		if self.data:IsInDujieArea() then
			self.dujie_operate_view:Open()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.Arrived)
			-- self.view:Flush(0 , "open_dujie")
		end
	else
		local server_time = TimeWGCtrl.Instance:GetServerTime() 
		local level_limit = self.data:GetOtherCfg("thunder_hurt_level_limit")
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level >= level_limit and self.data:IsInDujieArea() and server_time - self.tips_time > 60 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.WarringStr)
			self.tips_time = server_time
		end
	end
end

-- 转职任务完成操作
function DujieWGCtrl:OperateZhuanShenTaskStatusChange()
	if self.view:IsOpen() then
		self:RemoveWaitZhuanShengDelayTimer()
		self.show_zhuansheng_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.view:Close()
			self:CloseDujieSuccessView()
			ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
			self:RemoveWaitZhuanShengDelayTimer()
		end, 2)
	else
		ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
	end
end

--移除回调
function DujieWGCtrl:RemoveWaitZhuanShengDelayTimer()
    if self.show_zhuansheng_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_zhuansheng_delay_timer)
        self.show_zhuansheng_delay_timer = nil
    end
end

--------------------- 场景特效----------------------
function DujieWGCtrl:ShowSceneEffect()
    if DujieWGData.Instance:IsShowSceneEffect() then
        self:SetSceneEffect(true)
    else
        self:DeleteSceneEffect()
    end
end

function DujieWGCtrl:SetSceneEffect(is_show)
    if self.scene_effect_loadr == nil then
        local key = "scene_effect_dujie"
        self.scene_effect_loadr = AllocAsyncLoader(self, key)
    end
    local bundle = "effects/prefab/environment/common/eff_dujie_leiyun_prefab"
    local asset = "eff_dujie_leiyun"
    local is_change = false
    if is_show then
        if self.effect_bundle == nil and self.effect_asset == nil then
            self.effect_bundle = bundle
            self.effect_asset = asset
            is_change = true

        end
    end

    self.scene_effect_loadr:SetActive(is_show)
    if is_change then
        self.scene_effect_loadr:SetIsUseObjPool(true)
        self.scene_effect_loadr:SetParent(G_EffectLayer)
        self.scene_effect_loadr:Load(bundle, asset, function(obj)
            if obj == nil then
                return
            end
			
            local _, pos_x, pos_y = self.data:GetDujieAreaInfo()
            local wx, wy = GameMapHelper.LogicToWorld(pos_x, pos_y)
            local move_obj = obj:GetOrAddComponent(typeof(MoveableObject))
            move_obj:SetPosition(Vector3(wx, -30, wy))
            move_obj.gameObject.name = asset
        end)
    end
end


function DujieWGCtrl:DeleteSceneEffect()
    if self.scene_effect_loadr ~= nil then
        self.scene_effect_loadr:DeleteMe()
        self.scene_effect_loadr = nil
        self.effect_bundle = nil
        self.effect_asset = nil
    end
end

-------------------

function DujieWGCtrl:GotoDujieArea()
	local scene_id = DujieWGData.Instance:GetDujieAreaInfo()
	local dujie_pos = DujieWGData.Instance:GetRandomDujiePos()
	if dujie_pos then
		self:FlyToPos(scene_id, dujie_pos.x, dujie_pos.y)
	end
end

function DujieWGCtrl:FlyToPos(scene_id, x, y)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:ClearGuaJiInfo()
	end

    local main_role = Scene.Instance:GetMainRole()
    if main_role:IsInXunYou() then
        return
    end
	TaskWGCtrl.Instance:JumpFly(scene_id, x, y)
end


----------------------------音效---------------------------------------

function DujieWGCtrl:RePlayMusic()
    local sceneLogic = Scene.Instance:GetSceneLogic()
    sceneLogic:PlayBGM()
end

function DujieWGCtrl:StopBgMusic()
	AudioService.Instance:StopBgm()
end