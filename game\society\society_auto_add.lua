SocietyAutoAddView = SocietyAutoAddView or BaseClass(SafeBaseView)

function SocietyAutoAddView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	local assetbundle = "uis/view/society_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(694, 480)})
	self:AddViewResource(0, assetbundle, "layout_addfriendList")
	self.reflush_cd = 3
end

function SocietyAutoAddView:__delete()

end

function SocietyAutoAddView:ReleaseCallBack()
	self.m_data_mgr = nil
	if nil ~= self.auto_friend_list then
		self.auto_friend_list:DeleteMe()
		self.auto_friend_list = nil
	end

	self.list_auto_add = nil
	if nil ~= self.update_list_cd then
		GlobalTimerQuest:CancelQuest(self.update_list_cd)
		self.update_list_cd = nil
	end

	self.input_add_friend = nil
end

function SocietyAutoAddView:LoadCallBack()
	--首次进入请求一下协议
	SocietyWGCtrl.Instance:SendGetRandomRoleList()

	--self:SetSecondView(nil, self.node_list["size"])
	self.node_list["title_view_name"].text.text = Language.Society.ViewNameAddFirend
	self.input_add_friend = self.node_list["inp_addfriend_name"]:GetComponent(typeof(TMPro.TMP_InputField))
	self.m_data_mgr = SocietyWGData.Instance
	self.list_auto_add = self.node_list["list_auto_addlist"]
	self:CreateAutoFriendList()
	self.node_list["btn_yijianaddfriend"].button:AddClickListener(BindTool.Bind1(self.AutoAddFriendHandler, self))
	self.node_list["btn_updatelist"].button:AddClickListener(BindTool.Bind1(self.UpdateListHandler, self))
	self.node_list["btn_addfd_ok"].button:AddClickListener(BindTool.Bind1(self.OnAddFriend, self))
	self:Flush()
end

function SocietyAutoAddView:AutoAddFriendHandler()
	local data = SocietyWGData.Instance:GetAddfriendList()
	local sengadd = false
	for k,v in pairs(data) do 
		if v then
			SocietyWGCtrl.Instance:AddFriend(k,1)
			sengadd = true
		end
	end

	if sengadd then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.ApplyFriendSuccess)
	elseif #data == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.SelectAddFriendItemTips)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoAddFriendItemTips)
	end
end

function SocietyAutoAddView:UpdateListHandler()
	if self.update_list_cd then
		--SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.UpdateFriendItemTipsTooMore)
		return
	end
	SocietyWGCtrl.Instance:SendGetRandomRoleList()

	self.reflush_cd = 3--self:GetCdTime()
	self.node_list.reflush_text.text.text = Language.Society.NewTuijian.."（"..self.reflush_cd.."）"
	XUI.SetGraphicGrey(self.node_list.btn_updatelist, true)
	self.update_list_cd = GlobalTimerQuest:AddRunQuest(function()
			self.reflush_cd = self.reflush_cd - 1
			self.node_list.reflush_text.text.text = Language.Society.NewTuijian.."（"..self.reflush_cd.."）"
			if self.reflush_cd <= 0 and self.update_list_cd then
				self.node_list.reflush_text.text.text = Language.Society.NewTuijian
				XUI.SetGraphicGrey(self.node_list.btn_updatelist, false)
				GlobalTimerQuest:CancelQuest(self.update_list_cd)
				self.update_list_cd = nil
			end
		end, 1)
end

function SocietyAutoAddView:GetCdTime()
	local end_time = SocietyWGData.Instance:GetChannelCdEndTime() or Status.NowTime
	return math.ceil(end_time - Status.NowTime)
end


function SocietyAutoAddView:CreateAutoFriendList()
	self.auto_friend_list = AsyncListView.New(SocietyAutoAddFriendRender, self.list_auto_add)
end

function SocietyAutoAddView:OnFlush()
	-- 不会显示小于自己50级的人 不用浪费时间找原因了┗|｀O′|┛ 不是bug 配置的 
	local data = self.m_data_mgr:GetAutoaddfriendList()
	if nil ~= data and nil ~= self.auto_friend_list then
		self.auto_friend_list:SetDataList(data, 0)
	end
	--self.node_list["bg"]:SetActive(nil ~= data and #data > 0)
	self.node_list["bg_no"]:SetActive(nil == data or #data == 0)

end

function SocietyAutoAddView:GetGuideKeyAddFriend()
	if self.node_t_list.btn_yijianaddfriend then
		return self.node_t_list.btn_yijianaddfriend.node, BindTool.Bind1(self.AutoAddFriendHandler, self)
	end
end

function SocietyAutoAddView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.CloseBtn then
		return self.node_t_list.btn_close_window.node, BindTool.Bind1(self.OnCloseHandler, self)
	
	elseif ui_name == GuideUIName.KeyAddFriend then
		return self:GetGuideKeyAddFriend()
	end

	return nil, nil
end

function SocietyAutoAddView:OnAddFriend()
	local game_name = self.input_add_friend.text
	self.input_add_friend.text = ""
	local msg_ctrl = SysMsgWGCtrl.Instance
	--print_error(game_name)
	if nil ~= game_name and "" ~= game_name then
		if SocietyWGData.Instance:FindFriendByName(game_name) then
			if nil ~= Language.Society["AlreadyYouFriend"] then
				msg_ctrl:ErrorRemind(Language.Society["AlreadyYouFriend"])
			end
			return
		end

		SocietyWGCtrl.Instance:GetUserInfoByName(2151,game_name,function (flag, user_info)
			--找不到角色的时候
			if 0 == flag or nil == user_info then
				if nil ~= Language.Society["UserNotExist"] then
					msg_ctrl:ErrorRemind(Language.Society["UserNotExist"])
				end
				return
			end

			--添加的人为自己的时候
			if user_info.role_id == GameVoManager.Instance:GetMainRoleVo().role_id then
				if nil ~= Language.Society["NotAddSelf"] then
					msg_ctrl:ErrorRemind(Language.Society["NotAddSelf"])
				end
				return
			end	
			--黑名单
			if ChatWGData.Instance:InBlacklist(user_info.role_id, nil) then
				msg_ctrl:ErrorRemind(Language.Society["AlreadyBlackFriend"])
				return
			end
			SocietyWGCtrl.Instance:IAddFriend(user_info.role_id)
			local data = self.m_data_mgr:GetAutoaddfriendList()
			if nil ~= data and nil ~= self.auto_friend_list then
				self.auto_friend_list:SetDataList(data, 0)
			end
		end)
	else
		msg_ctrl:ErrorRemind(Language.Society["PleaseInputGameNme"])
	end

end
