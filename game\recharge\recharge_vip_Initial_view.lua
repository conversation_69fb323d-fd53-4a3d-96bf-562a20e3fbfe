VipInitialView = VipInitialView or BaseClass(SafeBaseView)

function VipInitialView:__init()
	self.view_layer = UiLayer.Normal
	self.mask_alpha = MASK_BG_ALPHA_TYPE.Normal
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_vip_initial")
end

function VipInitialView:ReleaseCallBack()

	if self.vip_initial_reward_list then
		self.vip_initial_reward_list:DeleteMe()
		self.vip_initial_reward_list = nil
	end

	self.vip_bg_tween = nil
	self:CancelCloseQuest()
end

function VipInitialView:OpenCallBack()
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.HuanYinYe, nil, true))
end

function VipInitialView:LoadCallBack()
	self.vip_initial_reward_list = AsyncListView.New(ItemCell, self.node_list.vip_initial_reward_list)
	self.vip_initial_reward_list:SetStartZeroIndex(true)
	XUI.AddClickEventListener(self.node_list["initial_vip_btn"], BindTool.Bind(self.OnClickVipInitial, self))

	self.vip_bg_tween = self.node_list.vip_bg:GetComponent(typeof(UGUITweenAlpha))

	self:CancelCloseQuest()
	self.close_timer = GlobalTimerQuest:AddTimesTimer(function ()
		self:OnClickVipInitial()
	end, 5, 1)
end

function VipInitialView:CancelCloseQuest()
	if self.close_timer then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
end

function VipInitialView:CloseCallBack()
	self:CancelCloseQuest()
	TaskGuide.Instance:CanAutoAllTask(true)
end

function VipInitialView:OnFlush(param_t)
	local initial_vip_reward_list = VipWGData.Instance:GetVipOtherInfo("initial_vip_reward_item")
	self.vip_initial_reward_list:SetDataList(initial_vip_reward_list)
end

function VipInitialView:OnClickVipInitial()
	VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_FETCH_INITIAL_VIP)
	self.vip_bg_tween:PlayReverse()
	ReDelayCall(self, function()
		self:Close()
	end, 0.6, "VipInitial")
end
