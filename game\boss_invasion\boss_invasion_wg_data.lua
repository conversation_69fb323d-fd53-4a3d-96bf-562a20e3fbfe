BOSSInvasionWGData = BOSSInvasionWGData or BaseClass()

function BOSSInvasionWGData:__init()
	if BOSSInvasionWGData.Instance then
		print_error("[BOSSInvasionWGData] Attempt to create singleton twice!")
		return
	end

	BOSSInvasionWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("cross_boss_strike_auto")
	self.rank_reward_cfg = ListToMapList(cfg.rank_reward, "group_index", "color")
	self.rule_image_cfg = cfg.rule_image
	self.privilege_display_cfg = cfg.privilege_display
	self.boss_color_cfg = ListToMap(cfg.boss_color, "group_index", "color")
	self.other_cfg = cfg.other[1]
	self.question_cfg = ListToMap(cfg.question, "seq")
	self.boss_cfg = ListToMap(cfg.boss, "group_index")
	self.tip_show_time_cfg = cfg.tip_show_time
	self.boss_skill_cfg = ListToMap(cfg.boss_skill, "group_index", "skill_id")

	self.group_index = 0                 -- 总group_index
	self.boss_color = 0					 -- boss颜色
	self.boss_num = 0                    -- boss总数量

	self.status = CROSS_BOSS_STRIKE_STATUS.STATUS_END     -- 状态
	self.next_status_time = 0   -- 切换到下一个状态时间 未来时间  切回end 是 0
	self.question_num = 0   -- 当前第几题
	self.question_seq = 0   --题得seq

	self.get_exp = 0    -- 经验 只有答题产出  差值算展示
	self.get_xiuwei = 0 -- 修为
	self.get_beast_exp = 0  -- 幻兽禁言
	self.get_coin = 0   --获得的同比
	self.right_answer_times = 0  -- 正确次数答题
	self.gather_flag = 0 -- 0未采集过
	self.can_gather = 0  -- 是否还有可采集 有最大采集限制
	self.has_gather_times = 0  -- 采集物已被采集次数
	self.coin_guwu_times = 0--金币已经鼓舞次数
	self.gold_guwu_times = 0-- 灵玉已经鼓舞次数

	self.rmb_buy_flag = 0  -- 特权标记 0 未购买

	self.hurt_top_uuid = {}     -- 同仙盟  第一名玩家uuid    group_index  是0 表示仙盟  > 0 服  role_id = uuid.temp_low
	self.hurt_top_name = ""
	-- self.hurt_top_param = 0     -- 同仙盟 仙盟id               服 服id

	self.hurt_top_usid = {}
	self.hurt_top_guild_id = 0

	self.auto_guwu_flag = {} -- 按位 自动鼓舞标记

	self.my_hurt_rank_id = -1
	self.my_hurt_damage = 0
	self.hurt_rank_list = {}
	self.hurt_rank_info_count = 0
	self.max_hurt = 1

	self:InitDataCache()
	RemindManager.Instance:Register(RemindName.BOSSInvasion, BindTool.Bind(self.GetBOSSInvasionRemind, self))
end

function BOSSInvasionWGData:__delete()
	BOSSInvasionWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.BOSSInvasion)
end

-------------------------------------INIT_START------------------------------------
function BOSSInvasionWGData:InitDataCache()

end
-------------------------------------INIT_END--------------------------------------

-------------------------------------REMING_START----------------------------------
function BOSSInvasionWGData:GetBOSSInvasionRemind()
	
	return 0
end
-------------------------------------REMING_END------------------------------------

-------------------------------------PROTOCOL_START--------------------------------
function BOSSInvasionWGData:SetCrossBossStrikeBaseInfo(protocol)
	self.status = protocol.status
	self.group_index = protocol.group_index   -- 组
	self.boss_color = protocol.boss_color     -- boss quality
	self.boss_num = protocol.boss_num         -- boss全服击杀数量
end

function BOSSInvasionWGData:SetCrossBossStrikeSceneInfo(protocol)
	self.status = protocol.status     -- 状态
	self.next_status_time = protocol.next_status_time   -- 切换到下一个状态时间 未来时间  切回end 是 0
	self.question_num = protocol.question_num   -- 当前第几题
	self.question_seq = protocol.question_seq   --题得seq

	self.hurt_top_uuid = protocol.hurt_top_uuid-- 同仙盟  第一名玩家uuid    group_index  是0 表示仙盟  > 0 服  role_id = uuid.temp_low
	self.hurt_top_name = protocol.hurt_top_name
	-- self.hurt_top_param = protocol.hurt_top_param -- 同仙盟 仙盟id               服 服id
	self.hurt_top_usid = protocol.hurt_top_usid
	self.hurt_top_guild_id = protocol.hurt_top_guild_id
end

function BOSSInvasionWGData:SetCrossBossStrikeRoleInfo(protocol)
	self.get_exp = protocol.get_exp    -- 经验 只有答题产出  差值算展示
	self.get_xiuwei = protocol.get_xiuwei -- 修为
	self.get_beast_exp = protocol.get_beast_exp  -- 幻兽禁言
	self.get_coin = protocol.get_coin   --获得的同比
	self.right_answer_times = protocol.right_answer_times  -- 正确次数答题
	self.gather_flag = protocol.gather_flag -- 0未采集过
	self.can_gather = protocol.can_gather
	self.coin_guwu_times = protocol.coin_guwu_times--金币已经鼓舞次数
	self.gold_guwu_times = protocol.gold_guwu_times-- 灵玉已经鼓舞次数
end

function BOSSInvasionWGData:SetCrossBossStrikeRoleBaseInfo(protocol)
	self.rmb_buy_flag = protocol.rmb_buy_flag
	self.auto_guwu_flag = bit:d2b_l2h(protocol.auto_guwu_flag, nil, true)  -- 按位 自动鼓舞标记
end

function BOSSInvasionWGData:SetCrossBossStrikeHurtRankInfo(protocol)
	self.hurt_rank_info_count = protocol.info_count
	self.hurt_rank_list = protocol.hurt_info
	local my_uuid = RoleWGData.Instance:GetUUid()
	local max_hurt = 1

	for k, v in pairs(self.hurt_rank_list) do
		if v.uuid == my_uuid then
			self.my_hurt_rank_id = v.rank
			self.my_hurt_damage = v.hurt
		end

		if v.hurt > max_hurt then
			max_hurt = v.hurt
		end
	end

	self.max_hurt = max_hurt

	-- self.my_hurt_rank_id = protocol.role_rank
	-- self.hurt_rank_list = protocol.hurt_info
	-- self.hurt_rank_info_count = protocol.info_count
	-- self.my_hurt_damage = protocol.role_hurt
end

function BOSSInvasionWGData:GetMaxHurtValue()
	return self.max_hurt
end

function BOSSInvasionWGData:SetHasGatherTimes(has_gather_times)
	self.has_gather_times = has_gather_times
end

function BOSSInvasionWGData:IsGetPrivilege()
	return self.rmb_buy_flag == 1
end

function BOSSInvasionWGData:GetCurBossNum()
	return self.boss_num
end

function BOSSInvasionWGData:GetGroupIndex()
	return self.group_index
end

function BOSSInvasionWGData:GetCurActStatus()
	return self.status
end

function BOSSInvasionWGData:GetNextStatusTime()
	return self.next_status_time
end

function BOSSInvasionWGData:GetCurQuestionNum()
	return self.question_num
end

function BOSSInvasionWGData:GetCurQuestionCfg()
	return self:GetQuestionCfgByQuestionSeq(self.question_seq)
end

function BOSSInvasionWGData:GetCurExp()
	return self.get_exp
end

function BOSSInvasionWGData:GetCurRightAnswerTimes()
	return self.right_answer_times
end

function BOSSInvasionWGData:GetBeastExp()
	return self.get_beast_exp
end

function BOSSInvasionWGData:GetCurXiuWei()
	return self.get_xiuwei
end

function BOSSInvasionWGData:GetCurCoin()
	return self.get_coin
end

function BOSSInvasionWGData:GetCurGuwuTime()
	return self.coin_guwu_times, self.gold_guwu_times
end

function BOSSInvasionWGData:GetAutoGuwuFlag(guwu_type)
	return self.auto_guwu_flag[guwu_type] == 1
end

function BOSSInvasionWGData:IsGatherFlag()
	return self.gather_flag == 1
end

function BOSSInvasionWGData:IsHasHurtRankList()
	return self.hurt_rank_info_count > 0
end

function BOSSInvasionWGData:GetHurtRankList()
	return self.hurt_rank_list
end

function BOSSInvasionWGData:GetMyHurtIdAndDamage()
	return self.my_hurt_rank_id, self.my_hurt_damage
end

function BOSSInvasionWGData:GetWinnerInfo()
	return self.hurt_top_uuid, self.hurt_top_usid, self.hurt_top_guild_id
end

function BOSSInvasionWGData:GetCanGatherFlag()
	return self.can_gather == 1
end

function BOSSInvasionWGData:GetHasGatherTime()
	return self.has_gather_times
end

function BOSSInvasionWGData:GetHurtTopName()
	return self.hurt_top_name
end
-------------------------------------PROTOCOL_END----------------------------------

-------------------------------------CFG_GET_START---------------------------------
function BOSSInvasionWGData:GetOtherCfgDataByAttrName(name)
    return self.other_cfg[name]
end

function BOSSInvasionWGData:GetOtherCfg()
    return self.other_cfg
end

function BOSSInvasionWGData:GetRuleImageCfg()
	return self.rule_image_cfg
end

function BOSSInvasionWGData:GetPrivilegeDisplayCfg()
	return self.privilege_display_cfg
end

function BOSSInvasionWGData:GetBossRankRewardCfg()
	return (self.rank_reward_cfg[self.group_index] or {})[self.boss_color]
end

function BOSSInvasionWGData:GetCurBossColorListCfg()
	return self.boss_color_cfg[self.group_index]
end

function BOSSInvasionWGData:GetNextBossColorListCfg()
	local next_color = self.boss_color + 1
	return (self.boss_color_cfg[self.group_index] or {})[next_color] or self:GetBossMaxQualityCfg()
end

function BOSSInvasionWGData:GetCurBossColorCfg()
	return (self.boss_color_cfg[self.group_index] or {})[self.boss_color]
end

function BOSSInvasionWGData:GetBossColorCfg(group_index, boss_color)
	return (self.boss_color_cfg[group_index] or {})[boss_color]
end

function BOSSInvasionWGData:GetQuestionCfgByQuestionSeq(seq)
	return self.question_cfg[seq]
end

function BOSSInvasionWGData:GetCurBossCfg()
	return self.boss_cfg[self:GetGroupIndex()]
end

function BOSSInvasionWGData:GetTipShowTimeCfg()
	return self.tip_show_time_cfg
end

function BOSSInvasionWGData:GetCurSkillCfgBySkillId(skill_id)
	return (self.boss_skill_cfg[self.group_index] or {})[skill_id]
end
-------------------------------------CFG_GET_END-----------------------------------

-------------------------------------CAL_START-------------------------------------
function BOSSInvasionWGData:GetBossRewardMyRankStr()
	local my_rank = self:GetMyHurtIdAndDamage()
	local rank_str = my_rank > 0 and my_rank or Language.BOSSInvasion.RankRewardNoMyRank
	return string.format(Language.BOSSInvasion.RankRewardMyRankStr, rank_str)
end

function BOSSInvasionWGData:CalBossQualitySLidervalue()
	local slider_value = 0
	local total_boss_num = self:GetCurBossNum()
	local last_value = 0
	local color_quality_list = self:GetCurBossColorListCfg()
	local per_value = 1 / (#color_quality_list) -- 0开始

	for i = 0, #color_quality_list do
		local data = color_quality_list[i]

		if total_boss_num == data.need_num then
			slider_value = per_value * i
			break
		elseif total_boss_num < data.need_num then
			slider_value = slider_value + per_value * ((total_boss_num- last_value) / (data.need_num - last_value))
			break
		else
			slider_value = per_value * i
			last_value = data.need_num
		end
	end

	return slider_value
end

function BOSSInvasionWGData:IsBossMaxQuality()
	local max_quality_cfg = self:GetBossMaxQualityCfg()
	return self.boss_color >= max_quality_cfg.color
end

function BOSSInvasionWGData:GetBossMaxQualityCfg()
	local cur_quality_list = self:GetCurBossColorListCfg()
	return cur_quality_list[#cur_quality_list]
end

function BOSSInvasionWGData:GetCanAddBossQualityCount()
	local max_quality_cfg = self:GetBossMaxQualityCfg()
	local per_add_num = self:GetOtherCfgDataByAttrName("up_color_add_num")
	local count = max_quality_cfg.need_num - self.boss_num
	return count > 0 and math.ceil(count / per_add_num) or 0
end

function BOSSInvasionWGData:GetBossRankRewardCfgByRankId(rank_id)
	local rank_reward_data_list = self:GetBossRankRewardCfg()

	for k, v in pairs(rank_reward_data_list) do
		if rank_id >= v.min_rank and rank_id <= v.max_rank then
			return v
		end
	end
end

function BOSSInvasionWGData:IsCrossServer()
	return self:GetGroupIndex() > 0
end

function BOSSInvasionWGData:IsBossInvasionEnd()
	return self:GetCurActStatus() == CROSS_BOSS_STRIKE_STATUS.STATUS_END
end

function BOSSInvasionWGData:GetGoldAddPer(gold_guwu_time)
	local boss_cfg = self:GetCurBossCfg()
	return boss_cfg.gold_guwu_add_per / 100 * gold_guwu_time
end

function BOSSInvasionWGData:GetCoinAddPer(gold_guwu_time)
	local boss_cfg = self:GetCurBossCfg()
	return boss_cfg.coin_guwu_add_per / 100 * gold_guwu_time
end

function BOSSInvasionWGData:CanGetBossEndReward()
	local hurt_top_uuid, hurt_top_usid_tab, hurt_top_guild_id = self:GetWinnerInfo()

	local my_uuid = RoleWGData.Instance:GetUUid()
	if my_uuid == hurt_top_uuid then
		return true
	end

	if self:IsCrossServer() then
		local server_id = RoleWGData.Instance:GetOriginServerId()
		local my_plat_type = RoleWGData.Instance:GetPlatType()
		return (server_id == hurt_top_usid_tab.temp_low and my_plat_type == hurt_top_usid_tab.temp_high)
	else
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		return (guild_id > 0 and guild_id == hurt_top_guild_id)
	end
end
-------------------------------------CAL_END---------------------------------------