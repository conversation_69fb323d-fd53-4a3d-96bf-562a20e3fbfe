-- 积分奖励界面
PositionalWarfareScoreRewardView = PositionalWarfareScoreRewardView or BaseClass(SafeBaseView)

local grade_reward_item_width = 80

function PositionalWarfareScoreRewardView:__init()
    self.open_tween = nil
	self.close_tween = nil
    self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_score_reward_view")
end

function PositionalWarfareScoreRewardView:LoadCallBack()
    if not self.reward_list then
		self.reward_list = AsyncListView.New(PWScoreRewardItemCellRender, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(false)
		self.node_list.reward_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnScrollRectValueChange, self))
	end

    self.unlock_pos_x = 0

    XUI.AddClickEventListener(self.node_list.unlock_btn, BindTool.Bind(self.OnClickUNlockBtn, self))
end

function PositionalWarfareScoreRewardView:ShowIndexCallBack()
    self.node_list.alpha_root.canvas_group.alpha = 0
    self.node_list.tween_root.rect.sizeDelta = Vector2(20, 128)

    if self.show_tween then
        self.show_tween:Kill()
        self.show_tween = nil
    end

    self.show_tween = self.node_list.tween_root.rect:DOSizeDelta(Vector2(792, 128), 0.3)
    self.show_tween:OnComplete(function ()
        self.node_list.alpha_root.canvas_group.alpha = 1
    end)
end

function PositionalWarfareScoreRewardView:Close()
    if self.node_list and self.node_list.alpha_root then
        self.node_list.alpha_root.canvas_group.alpha = 0
        self.hide_tween = self.node_list.tween_root.rect:DOSizeDelta(Vector2(20, 128), 0.3)
    
        self.hide_tween:OnComplete(function ()
            SafeBaseView.Close(self)
        end)
    else
        SafeBaseView.Close(self)
    end
end

function PositionalWarfareScoreRewardView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.show_tween then
        self.show_tween:Kill()
        self.show_tween = nil
    end

    if self.hide_tween then
        self.hide_tween:Kill()
        self.hide_tween = nil
    end

    self.unlock_pos_x = nil
end

function PositionalWarfareScoreRewardView:OnFlush()
    local data_list = PositionalWarfareWGData.Instance:GetPersonScoreRewardCfg()
    self.reward_list:SetDataList(data_list)
    self:SetSliderValue(data_list)
    self.node_list.unlock_btn:CustomSetActive(not PositionalWarfareWGData.Instance:GetPeosonScoreRewardUnLockFlag())

    local score = PositionalWarfareWGData.Instance:GetDevote()
    self.node_list.desc_week_score.text.text = score
end

function PositionalWarfareScoreRewardView:OnScrollRectValueChange()
	local rect = self.node_list.reward_list.scroll_rect.content
	self.node_list.slider_pro.rect.sizeDelta = Vector2(rect.rect.size.x - grade_reward_item_width, 14)
	local pos_x =  RectTransform.GetAnchoredPositionXY(self.node_list.reward_list.scroll_rect.content) + grade_reward_item_width / 2
	RectTransform.SetAnchoredPositionXY(self.node_list.slider_pro.rect, pos_x, -23)
    RectTransform.SetAnchoredPositionXY(self.node_list.unlock_btn.rect, self.unlock_pos_x + pos_x, -23)
end

function PositionalWarfareScoreRewardView:SetSliderValue(data_list)
    local score = PositionalWarfareWGData.Instance:GetDevote()
    local slider_value = 0
    local count = #data_list
    local per_slider_value = 1 / (count - 1)

    table.sort(data_list, SortTools.KeyLowerSorter("seq"))
    local last_score = data_list[1].need_devote

    for i = 1, count do
        local v = data_list[i]

        if score == v.need_devote then
            slider_value = (i - 1) * per_slider_value
            break
        elseif score < v.need_devote then
            local cell_value = v.need_devote - last_score
            slider_value = (i - 2) * per_slider_value + per_slider_value * (score - last_score) / cell_value
            break
        end

        last_score = v.need_devote

        slider_value = (i - 1) * per_slider_value
    end

    self.node_list.slider_pro.slider.value = slider_value

    local index = 0
    for i = 1, #data_list do
        if data_list[i].is_added == 1 then
            break
        end

        index = index + 1
    end

    local pos_x = 0
    if index > 1 then
        local haf_width = grade_reward_item_width / 2
        pos_x = grade_reward_item_width * (3 * index / 2 - 3 / 4)
    end

    self.unlock_pos_x = pos_x
end

function PositionalWarfareScoreRewardView:OnClickUNlockBtn()
    PositionalWarfareWGCtrl.Instance:OpenPlayUnlockView()
end

-----------------------------------------PWScoreRewardItemCellRender----------------------------------------
PWScoreRewardItemCellRender = PWScoreRewardItemCellRender or BaseClass(BaseRender)

function PWScoreRewardItemCellRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_root)
        self.item:SetIsShowTips(false)
        self.item:UseNewSelectEffect(true)
        self.item:SetTipClickCallBack(BindTool.Bind1(self.ClickHandler, self))
    end
end

function PWScoreRewardItemCellRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function PWScoreRewardItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local score = PositionalWarfareWGData.Instance:GetDevote()
    local is_get = PositionalWarfareWGData.Instance:IsDevoteRewardIsGet(self.data.seq)
    local unlock_flag = PositionalWarfareWGData.Instance:GetPeosonScoreRewardUnLockFlag()
    local score_enough = self.data.need_devote <= score
    local devote_reward_flag = PositionalWarfareWGData.Instance:GetDevoteRewardFlag()
    local sort_get = (self.data.seq - devote_reward_flag) == 1

    local can_get
    if self.data.is_added == 1 then
        can_get = unlock_flag and score_enough and sort_get
    else
        can_get = score_enough and sort_get
    end

    self.node_list.flag_is_get:CustomSetActive(is_get)
    self.node_list.flag_can_get:CustomSetActive(not is_get and can_get)

    self.item:SetFlushCallBack(function ()
		self.item:ResetSelectEffect()
		self.item:SetSelectEffect(is_get)
	end)

    self.item:SetData(self.data.item)
    self.node_list.desc_score.text.text = self.data.need_devote or 0
end

function PWScoreRewardItemCellRender:ClickHandler()
    if IsEmptyTable(self.data) then
        return
    end

    local score = PositionalWarfareWGData.Instance:GetDevote()
    local is_get = PositionalWarfareWGData.Instance:IsDevoteRewardIsGet(self.data.seq)
    local unlock_flag = PositionalWarfareWGData.Instance:GetPeosonScoreRewardUnLockFlag()
    local score_enough = self.data.need_devote <= score
    local devote_reward_flag = PositionalWarfareWGData.Instance:GetDevoteRewardFlag()
    local sort_get = (self.data.seq - devote_reward_flag) == 1
    local can_get
    if self.data.is_added == 1 then
        can_get = unlock_flag and score_enough and sort_get
    else
        can_get = score_enough and sort_get
    end

    if not is_get and can_get then
        PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.FETCH_DEVOTE_REWARD)
    else
        TipWGCtrl.Instance:OpenItem(self.data.item)
        -- if #self.data.item > 1 then
        --     RewardShowViewWGCtrl.Instance:SetRewardShowData(self.data.reward_item)
        -- else
        --     TipWGCtrl.Instance:OpenItem(self.data.reward_item[0])
        -- end
    end
end