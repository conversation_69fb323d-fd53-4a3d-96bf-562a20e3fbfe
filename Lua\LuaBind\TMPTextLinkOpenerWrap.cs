﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TMPTextLinkOpenerWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(TMPTextLinkOpener), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("AddClickListener", AddClickListener);
		<PERSON><PERSON>Function("ClearSubscription", ClearSubscription);
		<PERSON><PERSON>RegFunction("OnPointerClick", OnPointerClick);
		<PERSON><PERSON>RegFunction("Clear", Clear);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddClickListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			TMPTextLinkOpener obj = (TMPTextLinkOpener)ToLua.CheckObject<TMPTextLinkOpener>(L, 1);
			System.Action<float,float> arg0 = (System.Action<float,float>)ToLua.CheckDelegate<System.Action<float,float>>(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			obj.AddClickListener(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearSubscription(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPTextLinkOpener obj = (TMPTextLinkOpener)ToLua.CheckObject<TMPTextLinkOpener>(L, 1);
			obj.ClearSubscription();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerClick(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPTextLinkOpener obj = (TMPTextLinkOpener)ToLua.CheckObject<TMPTextLinkOpener>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerClick(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Clear(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPTextLinkOpener obj = (TMPTextLinkOpener)ToLua.CheckObject<TMPTextLinkOpener>(L, 1);
			obj.Clear();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

