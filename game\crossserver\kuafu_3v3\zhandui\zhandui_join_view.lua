--- 加入战队界面
ZhanDuiJoinView = ZhanDuiJoinView or BaseClass(SafeBaseView)

function ZhanDuiJoinView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(902, 568)})
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_join")
    self.sort_condition = ZHANDUI_LIST_SORT_CONDITION.UP_SOCRE
end

function ZhanDuiJoinView:ReleaseCallBack()
    if self.zhan_dui_list_event then
        GlobalEventSystem:UnBind(self.zhan_dui_list_event)
        self.zhan_dui_list_event = nil
    end
    if self.zhan_dui_applied_list_event then
        GlobalEventSystem:UnBind(self.zhan_dui_applied_list_event)
        self.zhan_dui_applied_list_event = nil
    end
    if self.zhandui_list then
        self.zhandui_list:DeleteMe()
        self.zhandui_list = nil
    end
    if FunctionGuide.Instance then
        FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ActJjc, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
    end
end

function ZhanDuiJoinView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.KuafuPVP.ViewName_JoinZhanDui
    XUI.AddClickEventListener(self.node_list.btn_create, BindTool.Bind(self.OnClickCreate, self))                   --创建战队
    XUI.AddClickEventListener(self.node_list.btn_change_sort_type, BindTool.Bind(self.OnClickChangeSortType, self)) --改变排序条件按钮
    XUI.AddClickEventListener(self.node_list.btn_auto_apply, BindTool.Bind(self.OnClickAutoApply, self))            --一键申请
    self.zhandui_list = AsyncListView.New(ZhanDuiJoinRender, self.node_list.zhandui_list)
    self.sort_condition = ZHANDUI_LIST_SORT_CONDITION.UP_SOCRE
    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ActJjc, self.get_guide_ui_event)
    KF3V3WGData.Instance:SetZhanDuiViewIsOpened()
end

function ZhanDuiJoinView:ShowIndexCallBack()
    self.sort_condition = ZHANDUI_LIST_SORT_CONDITION.UP_SOCRE
    if not self.zhan_dui_list_event then
        self.zhan_dui_list_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_List_Receive, BindTool.Bind(self.OnZhanDuiListReceive, self))
    end
    if not self.zhan_dui_applied_list_event then
        self.zhan_dui_applied_list_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Applied_List_Receive, BindTool.Bind(self.OnZhanDuiAppliedListReceive, self))
    end
    ZhanDuiWGCtrl.Instance:ReqZhanDuiInfoList()
    self:Flush()
end

function ZhanDuiJoinView:OnFlush()
    self:FlushList()
end

function ZhanDuiJoinView:OnZhanDuiListReceive()
    self:FlushList()
end

function ZhanDuiJoinView:OnZhanDuiAppliedListReceive()
    self:FlushList()
end

function ZhanDuiJoinView:FlushList()
    local data_list = ZhanDuiWGData.Instance:GetZhanDuiNotFullInfoList(self.sort_condition)
    local is_empty_table = IsEmptyTable(data_list)
    self.zhandui_list:SetDataList(data_list, 3)

    self.node_list.layout_blank_tip2:SetActive(is_empty_table)
    self.node_list.btn_auto_apply:SetActive(not is_empty_table)
    local bundle, asset = nil, nil
    if self.sort_condition == ZHANDUI_LIST_SORT_CONDITION.UP_SOCRE then
        --bundle, asset = ResPath.GetCommonOthers("arrow_18")
        self.node_list["change_sort_img"].rect.localScale = Vector3(1,1,1)
    else
        self.node_list["change_sort_img"].rect.localScale = Vector3(-1,1,1)
        --bundle, asset = ResPath.GetCommonOthers("arrow_19")
    end
    --self.node_list.change_sort_img.image:LoadSprite(bundle, asset)
end

--创建战队
function ZhanDuiJoinView:OnClickCreate()
    ZhanDuiWGCtrl.Instance:OpenCreateView()
end

--一键申请
function ZhanDuiJoinView:OnClickAutoApply()
    local list = ZhanDuiWGData.Instance:GetZhanDuiNotFullInfoList(self.sort_condition)
    if nil == list or #list <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NoZhanDuiCanApply)
		return
	end
	local is_top_ten_zhandui_applyed = true
    local zhandui_id_list = ZhanDuiWGData.Instance:CreateZhanduiAutoApplyIdList()
    for i,v in ipairs(list) do
        if i <= 10 then
            zhandui_id_list[i] = v.zhandui_id
            if not ZhanDuiWGData.Instance:GetIsAppliedZhanDui(v.zhandui_id) then
            	is_top_ten_zhandui_applyed = false
            end
        else
            break
        end
    end

    if is_top_ten_zhandui_applyed then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.AlreadyApplyWaitToCallback)
		return
    end

    ZhanDuiWGCtrl.Instance:SendAutoApplyZhanDui(zhandui_id_list)
end

--改变排序类型
function ZhanDuiJoinView:OnClickChangeSortType()
    self.sort_condition = self.sort_condition == ZHANDUI_LIST_SORT_CONDITION.UP_SOCRE and ZHANDUI_LIST_SORT_CONDITION.UP_CAPABILITY or ZHANDUI_LIST_SORT_CONDITION.UP_SOCRE
    self:FlushList()
end

function ZhanDuiJoinView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == GuideUIName.Tab and self.tabbar then
        local tab_index = math.floor(TabIndex[ui_param])
        if tab_index == self:GetShowIndex() then
            return NextGuideStepFlag
        else
            local ui = self.tabbar:GetToggleByIndex(tab_index)
            return ui, BindTool.Bind(self.ChangeToIndex, self, tab_index)
        end
    elseif ui_name == GuideUIName.KF3V3ZhanDuiBtn then
        --  Field1v1WGData.Instance:SetGuideFlag(true)
        return self.node_list["bottom_container"]
    end
    return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end


ZhanDuiJoinRender = ZhanDuiJoinRender or BaseClass(BaseRender)
function ZhanDuiJoinRender:__init()
    XUI.AddClickEventListener(self.node_list.btn_handle, BindTool.Bind(self.OnClickApply, self))
end

function ZhanDuiJoinRender:OnFlush()
    if not self.data or IsEmptyTable(self.data) then
        return
    end
    --战队名
    self.node_list.zhandui_name.text.text = self.data.name
    --战队段位
    local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.score)
    ZhanDuiWGCtrl.SetZhanDuiDuanWeiImage(self.node_list.duanwei_img, grade_cfg)
    --self.node_list.zhandui_dan_grading.image:LoadSprite(ResPath.GetNoPackPNG("a2_duanweiname"..grade_cfg.grade))
    --self.node_list.zhandui_dan_grading.image:SetNativeSize()
	self.node_list.zhandui_tier_name.text.text = grade_cfg.tier_name
    --成员个数
    self.node_list.zhandui_member_count.text.text = self.data.member_count
    --队长名
    self.node_list.leader_name.text.text = self.data.captain_name
    --战队战斗力
    self.node_list.zhandui_capability.text.text = self.data.capability
    --处理申请状态
    local is_applied = ZhanDuiWGData.Instance:GetIsAppliedZhanDui(self.data.zhandui_id)
    self.node_list.img_applying_text:SetActive(is_applied)
    self.node_list.btn_handle:SetActive(not is_applied)
    --self.node_list.bg:SetActive(self.index % 2 ~= 0)
    --TODO 战队图片

end

function ZhanDuiJoinRender:OnClickApply()
    ZhanDuiWGCtrl.Instance:SendApplyToJoinZhanDui(self.data.zhandui_id)
end