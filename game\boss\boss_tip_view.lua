BossDescTip = BossDescTip or BaseClass(SafeBaseView)
function BossDescTip:__init()
	if BossDescTip.Instance then
		ErrorLog("[BossDescTip] Attemp to create a singleton twice !")
	end
	self.is_modal = true
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_tip")

	BossDescTip.Instance = self
	self.content = ""
	self.title_name_txt = nil
end

function BossDescTip:__delete()
	BossDescTip.Instance = nil
end

function BossDescTip:LoadCallBack()
	
end

--设置内容
function BossDescTip:SetContent(content, title_name)
	self.content = content
	self.title_name_txt = title_name
	self:Open()
end

function BossDescTip:ShowIndexCallBack()
	self:Flush()
end

function BossDescTip:CloseCallBack()
	self.content = ""
	self.title_name_txt = nil
end

function BossDescTip:OnFlush()
	self.node_list["rich_tip_desc"].text.text = ToColorStr(self.content, COLOR3B.BLACK)

	self.node_list["rich_tip_title"].text.text = ToColorStr(self.title_name_txt, COLOR3B.BLACK)

end
