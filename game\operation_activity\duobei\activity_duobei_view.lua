function OperationActivityView:LoadIndexCallBackDuoBei()
	self:FlushDBView()
	self:DBTimeCountDown()
	--XUI.AddClickEventListener(self.node_list.db_btn_tip, BindTool.Bind(self.OnDBBtnTipClickHnadler,self))
	self:DTYYLoadDuoBeiView()
end
--设置界面的所有显示信息包括底图(只需要赋值一次的)
function OperationActivityView:DTYYLoadDuoBeiView()
	local other_cfg = OperationActDuoBeiWGData.Instance:GetActivityOtherCfg()
	if nil == other_cfg or IsEmptyTable(other_cfg) then
		return
	end
	self.node_list.duobei_title_desc.text.text = other_cfg.rule_tip

	local db_xuanchuantu_name = OperationActivityWGData.Instance:GetCurTypeImgName("a2_zx_ggdt7")
	local bundle, asset = ResPath.GetRawImagesPNG(db_xuanchuantu_name)
	self.node_list["db_xuanchuantu"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["db_xuanchuantu"].raw_image:SetNativeSize()
    end)

	local title_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a2_zx_ggdt")
	local bundle2, asset2 = ResPath.GetRawImagesPNG(title_bg_name)
	self.node_list["bg_title"].raw_image:LoadSprite(bundle2, asset2, function()
        self.node_list["bg_title"].raw_image:SetNativeSize()
    end)

	-- local asset_name3,bundle_name3 = ResPath.GetF2RawImagesPNG(other_cfg.word)
	-- 			self.node_list["db_word"].raw_image:LoadSprite(asset_name3,bundle_name3,function ()
	-- 			self.node_list["db_word"].raw_image:SetNativeSize()
	-- 		end)
end


function OperationActivityView:ReleaseDBView()
	if self.db_reward_list then
		self.db_reward_list:DeleteMe()
		self.db_reward_list = nil
	end

	if self.cell_list ~= nil then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end

	if self.duoibei_count_down and CountDownManager.Instance:HasCountDown(self.duoibei_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.duoibei_count_down)
	end
end

function OperationActivityView:FlushDBView()
	self:FlushDBReward()
	local other_cfg = OperationActDuoBeiWGData.Instance:GetActivityOtherCfg()
	self:SetRuleInfo(other_cfg.rule_desc, Language.OperationActivity.ActiveTipTitle)
	self:SetOutsideRuleTips(other_cfg.rule_tip)
end

function OperationActivityView:FlushDBReward()
	local cfg = OperationActDuoBeiWGData.Instance:GetDuoBeiInfo()
	if cfg ~= nil then
		-- if #cfg < 4 then
		-- 	self.node_list.db_list:SetActive(false)
		-- 	self.node_list.db_grid:SetActive(true)
		-- 	if self.cell_list == nil then
		-- 		self.cell_list = {}
		-- 		for i=1,3 do
		-- 			self.cell_list[i] = OpeActDuoBeiItemRender.New(self.node_list["db_grid_item"..i].gameObject)
		-- 		end
		-- 	end

		-- 	local len = #cfg
		-- 	for i = 1, 3 do
		-- 		if i <= len then
		-- 			self.node_list["db_grid_item"..i]:SetActive(true)
		-- 			self.cell_list[i]:SetItemData(cfg[i])
		-- 		else
		-- 			self.node_list["db_grid_item"..i]:SetActive(false)
		-- 		end
		-- 	end
		-- else
		-- 	self.node_list.db_list:SetActive(true)
		-- 	self.node_list.db_grid:SetActive(false)
		-- 	if not self.db_reward_list then
		-- 		self.db_reward_list = AsyncListView.New(OpeActDuoBeiItemRender, self.node_list.db_list)
		-- 	end
		-- 	self.db_reward_list:SetDataList(cfg)
		-- end

		if not self.db_reward_list then
			self.db_reward_list = AsyncListView.New(OpeActDuoBeiItemRender, self.node_list.db_list)
		end
		self.db_reward_list:SetDataList(cfg)
	end
end

-- function OperationActivityView:OnDBBtnTipClickHnadler()
	
-- end

--有效时间倒计时
function OperationActivityView:DBTimeCountDown()
	self.duoibei_count_down = "duoibei_count_down"
	local invalid_time = OperationActDuoBeiWGData.Instance:GetActivityInValidTime()
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.duoibei_count_down, BindTool.Bind1(self.UpdateDBCountDown, self), BindTool.Bind1(self.OnDBComplete, self), invalid_time, nil, 1)
	end
end

function OperationActivityView:UpdateDBCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function OperationActivityView:OnDBComplete()
	self.node_list.db_time_label.text.text = ""
end

-------------------------------------

OpeActDuoBeiItemRender = OpeActDuoBeiItemRender or BaseClass(BaseRender)
function OpeActDuoBeiItemRender:__init()
	self.db_render_info_cfg = {}
	
end
--设置界面的所有显示信息包括底图(只需要赋值一次的)
function OpeActDuoBeiItemRender:LoadDuoBeiRenderView()
	local other_cfg = OperationActDuoBeiWGData.Instance:GetActivityOtherCfg()
	if nil == other_cfg or IsEmptyTable(other_cfg) then
		return
	end
	-- local asset_name1,bundle_name1 = ResPath.GetF2RawImagesPNG(other_cfg.duobei_item)
	-- 		self.node_list["duobei_item"].raw_image:LoadSprite(asset_name1,bundle_name1,function ()
	-- 		self.node_list["duobei_item"].raw_image:SetNativeSize()
	-- 	end)
	-- local asset_name1,bundle_name1 = ResPath.GetF2RawImagesPNG(other_cfg.item_bg)
	-- 		self.node_list["item_bg"].raw_image:LoadSprite(asset_name1,bundle_name1,function ()
	-- 		self.node_list["item_bg"].raw_image:SetNativeSize()
	-- 	end)
	-- local asset_name1,bundle_name1 = ResPath.GetF2RawImagesPNG(other_cfg.icon_bg)
	-- 		self.node_list["icon_bg"].raw_image:LoadSprite(asset_name1,bundle_name1,function ()
	-- 		self.node_list["icon_bg"].raw_image:SetNativeSize()
	-- 	end)

end
function OpeActDuoBeiItemRender:LoadCallBack()
	--self.db_reward_item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function OpeActDuoBeiItemRender:__delete()
	-- if self.db_reward_item_list then
	-- 	self.db_reward_item_list:DeleteMe()
	-- 	self.db_reward_item_list = nil
	-- end
end

function OpeActDuoBeiItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function OpeActDuoBeiItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	--self:LoadDuoBeiRenderView()
	-- local bundle, asset = ResPath.GetCommonIcon(data.cfg.icon)
	-- self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
	-- 	self.node_list.icon.image:SetNativeSize()
	-- end)

	local beishu = CommonDataManager.GetDaXie(data.cfg.reward_mult)
	beishu = beishu == Language.TianShenRoad.twoconversion[1] and Language.TianShenRoad.twoconversion[2] or beishu
	self.node_list.beishu.text.text = string.format(Language.TianShenRoad.beishudown,beishu)
	
	self.node_list.name.text.text = data.cfg.wanfa_name

	-- local list = {}
	-- if IsEmptyTable(data.cfg.reward_item) then
	-- 	list = TianshenRoadWGData.Instance:GetDuoBeiRewardListByTaskType(data.cfg.task_type)
	-- else
	-- 	list = SortTableKey(data.cfg.reward_item)
	-- end
	
	--self.db_reward_item_list:SetDataList(list)
end

function OpeActDuoBeiItemRender:OnBtnClickDuoBei()
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")  
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end
