---------------------------------------------
-- 双修-好感度
---------------------------------------------
ArtifactView = ArtifactView or BaseClass(SafeBaseView)

function ArtifactView:AffectionReleaseCallBack()
	if self.affection_gift_grid then
		self.affection_gift_grid:DeleteMe()
		self.affection_gift_grid = nil
	end

	if self.affection_reward_list then
		self.affection_reward_list:DeleteMe()
		self.affection_reward_list = nil
	end


	self.selected_gift_item = nil
	self.send_amount = nil

	self.need_flush_amount = nil
	self.is_affection_loaded = nil

	-- RemindManager.Instance:UnBind(self.remind_callback)
end

function ArtifactView:AffectionCloseCallBack()
end

function ArtifactView:AffectionLoadCallBack()
	if not self.affection_reward_list then
		self.affection_reward_list = AsyncListView.New(Item<PERSON>ell, self.node_list["affection_reward_list"])
		self.affection_reward_list:SetStartZeroIndex(true)
	end

	if not self.affection_gift_grid then
		self.affection_gift_grid = AsyncBaseGrid.New()
		self.affection_gift_grid:SetStartZeroIndex(false)
		self.affection_gift_grid:CreateCells({
			col = 4,
			assetName = "affection_gift_item",
			assetBundle = "uis/view/artifact_ui_prefab",
			list_view = self.node_list["affection_gift_grid"],
			itemRender = AffectionGiftCell,
			change_cells_num = 1,
		})
		self.affection_gift_grid:SetSelectCallBack(BindTool.Bind(self.OnAffectionGiftSelectCB, self))
	end

	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind1(self.OnClickPlus, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind1(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["amount_btn"], BindTool.Bind1(self.OnClickSetAmount, self))
	XUI.AddClickEventListener(self.node_list["btn_send_gift"], BindTool.Bind1(self.OnClickSendGiftBtn, self))			--赠送
	XUI.AddClickEventListener(self.node_list["btn_add_send"], BindTool.Bind1(self.OnClickAddSendCountBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_get_affection_reward"], BindTool.Bind1(self.OnClickGetRewardBtn, self))

	XUI.AddClickEventListener(self.node_list["btn_open_send_gift"], BindTool.Bind1(self.OnClickShowSendGiftBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_treasure_box"], BindTool.Bind1(self.OnClickOpenQWYLView, self))
	XUI.AddClickEventListener(self.node_list["btn_travel"], BindTool.Bind1(self.OnClickOpenTravelBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_reward_preview"], BindTool.Bind1(self.OnClickRewardPreviewBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_close_send_gift"], BindTool.Bind1(self.OnClickCloseSendGiftBtn, self))

	self.is_affection_loaded = true
	self:PlayAffectionShowAnim()
	self.send_amount = 1

    -- -- 界面监听其他功能的红点变化
    -- local other_remind_list = {RemindName.ArtifactTravel}
    -- self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    -- for k,v in pairs(other_remind_list) do
    --     RemindManager.Instance:Bind(self.remind_callback, v)
    -- end
end

function ArtifactView:AffectionShowIndexCallBack()
	self.node_list["btns_root"]:SetActive(true)
	self.node_list["send_gift_part"]:SetActive(false)
	self.node_list["effect_send_gift"]:SetActive(false)
	self:PlayAffectionShowAnim()
end

function ArtifactView:AffectionOnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.sel_seq then
				self.jump_seq = v.sel_seq
			end
			self:FlushAffectionRightPart()
			self:FlushSendGiftPart()
		elseif k == "protocol_flush" then
			self:FlushAffectionRightPart()
			self:FlushSendGiftPart(true)
		elseif k == "flush_remind" then
			self:FlushAffectionRightPart()
			self:FlushSendGiftPart(true)
		elseif k == "show_send_gift" then
			self:FlushAffectionRightPart()
			self:FlushSendGiftPart(true)
			self:OnClickShowSendGiftBtn()
		end
	end
end

function ArtifactView:FlushAffectionRightPart()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	if not cur_artifact_data then
		return
	end
	self:FlushRemind()
	local affection_level_cfg = ArtifactWGData.Instance:GetAffectionLevelCfg(self.select_artifact_seq, cur_artifact_data.favor_level)
	local affection_next_level_cfg = ArtifactWGData.Instance:GetAffectionLevelCfg(self.select_artifact_seq, cur_artifact_data.favor_level + 1)
	if not affection_level_cfg then
		return
	end

	local is_max_affection_level = affection_next_level_cfg == nil
	self.node_list["txt_level_name"].text.text = affection_level_cfg.favor_level_name
	self.node_list["txt_affections_level"].text.text = cur_artifact_data.favor_level
	local value_str = "MAX"
	local slider_value = 1
	if not is_max_affection_level then
		value_str = ToColorStr(cur_artifact_data.favor_value, COLOR3B.C8) .. "/" ..affection_next_level_cfg.need_favor
		slider_value = (cur_artifact_data.favor_value - affection_level_cfg.need_favor) / (affection_next_level_cfg.need_favor - affection_level_cfg.need_favor)
	end
	self.node_list["txt_affection_value"].text.text = value_str
	self.node_list["affection_slider"].slider.value = slider_value

	-- 奖励部分
	local can_get_reward_level = 0
	for i, v in ipairs(cur_artifact_data.favor_reward_flag) do
		if v == 0 and i <= cur_artifact_data.favor_level then
			can_get_reward_level = i
		end
	end
	local show_reward_list = affection_level_cfg.reward_item
	self.node_list["affection_reward_remind"]:SetActive(can_get_reward_level > 0)
	if can_get_reward_level > 0 then
		local cfg = ArtifactWGData.Instance:GetAffectionLevelCfg(self.select_artifact_seq, can_get_reward_level)
		show_reward_list = cfg and cfg.reward_item
	elseif affection_next_level_cfg then
		show_reward_list = affection_next_level_cfg.reward_item
	end

	local cur_level_reward_falg = ArtifactWGData.Instance:GetAffectionLevelRewardFlag(self.select_artifact_seq, cur_artifact_data.favor_level)
	self.affection_reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(is_max_affection_level and cur_level_reward_falg == 1)
        end
    end)
	self.affection_reward_list:SetDataList(show_reward_list)
end

function ArtifactView:FlushRemind()
	if not self.select_artifact_seq then
		return
	end

	-- 同游按钮红点
	local travel_remind = ArtifactWGData.Instance:GetArtifactTravelRemind(self.select_artifact_seq)
	self.node_list["travel_remind"]:SetActive(travel_remind)

	-- 珍宝匣红点
	local treasure_remind = StrangeCatalogWGData.Instance:GetBigTypeRemind(self.select_artifact_seq)
	self.node_list["treasure_box_remind"]:SetActive(treasure_remind)
end

function ArtifactView:OnArtifactAffectionCellHandler(item, cell_index)
	self:FlushAffectionRightPart()
	self:FlushSendGiftPart()
end

function ArtifactView:OnClickShowSendGiftBtn()
	self.node_list["btns_root"]:SetActive(false)
	self.node_list["send_gift_part"]:SetActive(true)
	self.node_list["affection_anim_root"].animation_player:Play("SendGiftPart")
end

function ArtifactView:OnClickCloseSendGiftBtn()
	self.node_list["btns_root"]:SetActive(true)
	self.node_list["send_gift_part"]:SetActive(false)
	self.node_list["affection_anim_root"].animation_player:Play("RightButtons")
end

-- 领取好感奖励
function ArtifactView:OnClickGetRewardBtn()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	if not cur_artifact_data then
		return
	end
	local can_get_reward_level = 0
	for i, v in ipairs(cur_artifact_data.favor_reward_flag) do
		if v == 0 and i <= cur_artifact_data.favor_level then
			can_get_reward_level = i
			break
		end
	end
	if can_get_reward_level == 0 then
		ArtifactWGCtrl.Instance:OpenAffectionLevelView({seq = self.select_artifact_seq})
		return
	end
	ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.ONE_KEY_FETCH_FAVOR_REWARD, self.select_artifact_seq)
end

-- 打开好感奖励预览
function ArtifactView:OnClickRewardPreviewBtn()
	if not self.select_artifact_seq then
		return
	end
	ArtifactWGCtrl.Instance:OpenAffectionLevelView({seq = self.select_artifact_seq})
end

--打开奇闻异录界面
function ArtifactView:OnClickOpenQWYLView()
	FunOpen.Instance:OpenViewByName(GuideModuleName.StrangeCatalogView, nil, {sel_seq = self.select_artifact_seq})
end

--打开同游界面
function ArtifactView:OnClickOpenTravelBtn()
	FunOpen.Instance:OpenViewByName(GuideModuleName.ArtifactTravelView, nil, {sel_seq = self.select_artifact_seq})
end

-- 赠礼部分
function ArtifactView:FlushSendGiftPart(is_data_flush)
	local gift_item_list = ArtifactWGData.Instance:GetGiftItemList(self.select_artifact_seq)
	self.affection_gift_grid:SetDataList(gift_item_list)
	self.need_flush_amount = is_data_flush
	-- 如果是刷新就需要重新选中原来的item
	if is_data_flush then
		local jump_item_id = self.selected_gift_item and self.selected_gift_item.item_id or 0
		local jump_index = 1
		if jump_item_id > 0 then
			local num = ItemWGData.Instance:GetItemNumInBagById(jump_item_id)
			if num > 0 then
				for i, v in ipairs(gift_item_list) do
					if v.item_id == jump_item_id then
						jump_index = i
						break
					end
				end
			end
		end
		self.affection_gift_grid:JumpToIndexAndSelect(jump_index)
	else
		self.affection_gift_grid:JumpToIndexAndSelect(1)
	end
	local send_count, max_count = ArtifactWGData.Instance:GetGetSendGiftCount()
	local color = send_count <= 0 and  COLOR3B.C10 or COLOR3B.C8
	self.node_list["txt_send_count"].text.text = string.format(Language.Artifact.SendGiftCount, ToColorStr(send_count, color), max_count)
end

function ArtifactView:OnClickSendGiftBtn()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	if not cur_artifact_data or not self.selected_gift_item then
		return
	end

	if cur_artifact_data.level <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.NoActive)
		return
	end

	local had_num = self.selected_gift_item.num
	if had_num <= 0 or self.send_amount > had_num then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.NotEnoughStuff)
		return
	end
	
	local send_count, max_count = ArtifactWGData.Instance:GetGetSendGiftCount()
	if send_count <= 0 or self.send_amount > send_count then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.SendGiftCountNotEnough)
		return
	end

	local max_level_cfg = ArtifactWGData.Instance:GetMaxAffectionLevelCfg(self.select_artifact_seq)
	if cur_artifact_data.favor_level >= max_level_cfg.favor_level then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.AffectionMaxLevel)
		return
	end

	local add_per = self.selected_gift_item.is_favor and (self.selected_gift_item.exclusive_add + 10000) / 10000 or 1
	local add_value = math.floor(add_per * self.selected_gift_item.base_favor * self.send_amount)
	if cur_artifact_data.favor_value + add_value > max_level_cfg.need_favor then
		TipWGCtrl.Instance:OpenAlertTips(Language.Artifact.AffectionOver, function()
			ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.SEND_GIFT, self.select_artifact_seq, self.selected_gift_item.item_id, self.send_amount)
			self.node_list["effect_send_gift"]:SetActive(false)
			self.node_list["effect_send_gift"]:SetActive(true)
		end)
	else
		ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.SEND_GIFT, self.select_artifact_seq, self.selected_gift_item.item_id, self.send_amount)
		self.node_list["effect_send_gift"]:SetActive(false)
		self.node_list["effect_send_gift"]:SetActive(true)
	end
end

function ArtifactView:OnClickAddSendCountBtn()
	local send_count, max_count = ArtifactWGData.Instance:GetGetSendGiftCount()
	if send_count >= max_count then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.SendGiftCountMax)
		return
	end

	local path = ArtifactWGData.Instance:GetAffectionGiftJumpPath()
	FunOpen.Instance:OpenViewNameByCfg(path)
end

function ArtifactView:OnAffectionGiftSelectCB(cell)
	if not cell or not cell.data then
		return
	end
	self.selected_gift_item = cell.data

	local send_count = ArtifactWGData.Instance:GetGetSendGiftCount()
	local send_amount = math.min(self.selected_gift_item.num, send_count)
	send_amount = math.max(send_amount, 1)
	if not self.need_flush_amount or not self.send_amount or self.send_amount > send_amount then
		self.send_amount = send_amount
	end
	self.need_flush_amount = false

	self:FlushAmount()
	self:FlushAmountBtnStatus()
end

function ArtifactView:OnClickPlus()
	local max_num = math.max(self.selected_gift_item.num, 1)
	self:ChangeAmount(math.min(self.send_amount + 1, max_num))
end

function ArtifactView:OnClickMinus()
	self:ChangeAmount(math.max(self.send_amount - 1, 1))
end

function ArtifactView:ChangeAmount(new_amount)
	self.send_amount = new_amount
	self:FlushAmount()
	self:FlushAmountBtnStatus()
end

function ArtifactView:FlushAmount()
	self.node_list["txt_send_amount"].text.text = self.send_amount
end

function ArtifactView:FlushAmountBtnStatus()
	-- local send_count = ArtifactWGData.Instance:GetGetSendGiftCount()
	-- local max_num = math.max(math.min(self.selected_gift_item.num, send_count), 1)
	local max_num = math.max(self.selected_gift_item.num, 1)
	XUI.SetGraphicGrey(self.node_list["btn_plus"], self.send_amount >= max_num)
	XUI.SetGraphicGrey(self.node_list["btn_minus"], self.send_amount <= 1)
end

-- 点击设置数量
function ArtifactView:OnClickSetAmount()
	local function callback(input_num)
		self:ChangeAmount(input_num)
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.send_amount)
	local max_num = math.max(self.selected_gift_item.num, 1)
	num_keypad:SetMaxValue(max_num)
	num_keypad:SetMinValue(1)
	num_keypad:SetOkCallBack(callback)
end

function ArtifactView:PlayAffectionShowAnim()
	if self.is_affection_loaded then
		self.node_list["affection_anim_root"].animation_player:Play("OpenAnim")
	end
end

--[[
-- 红点变化回调
function ArtifactView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.ArtifactTravel then
        self.node_list["travel_remind"]:SetActive(num > 0)
    end
end
]]

--[[
---------------------------------------
-- 左侧列表Item ArtifactAffectionCell
---------------------------------------
ArtifactAffectionCell = ArtifactAffectionCell or BaseClass(BaseRender)
function ArtifactAffectionCell:__init()
	self.is_selected = nil
end

function ArtifactAffectionCell:LoadCallBack()

end

function ArtifactAffectionCell:ReleaseCallBack()
	self.is_selected = nil
	self:ClearTweener()
end

function ArtifactAffectionCell:OnFlush()
	if not self.data then
		return
	end

	local remind = ArtifactWGData.Instance:GetArtifactAffectionSingleRemind(self.data.seq)
	self.node_list.remind:SetActive(remind)

	local bundle, asset = ResPath.GetRawImagesPNG(self.data.icon_name)
	self.node_list["img_icon"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["img_icon"].raw_image:SetNativeSize()
	end)

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)
	local is_act = cur_artifact_data.level > 0

	self.node_list["img_affection"]:SetActive(is_act)
	self.node_list["img_locked"]:SetActive(not is_act)
	self.node_list["txt_affection_level"].text.text = cur_artifact_data.favor_level
end

function ArtifactAffectionCell:OnSelectChange(is_select)
	if not self.data then
		return
	end
	self.node_list["select_hl"]:SetActive(is_select)
	self.node_list["select_normal"]:SetActive(not is_select)
	-- self.node_list["icon_type"]:SetActive(is_select)
	-- self.node_list["type_bg"]:SetActive(is_select)

	if self.is_selected ~= is_select then
		self.target_height = is_select and ARTIFACT_RENDER_HEIGHT_EXPAND or ARTIFACT_RENDER_HEIGHT_SHRINK
		self:SetItemScale()
		self.is_selected = is_select
	end
end

function ArtifactAffectionCell:SetItemScale()
	self:ClearTweener()
	local rect = self.node_list["rect_size"].rect
	self.size_change_tweener = rect:DOSizeDelta(Vector2(292, self.target_height), 0.4)
	self.size_change_tweener:OnUpdate(function()
		self.view.layout_element.minHeight = rect.sizeDelta.y
	end)
end

function ArtifactAffectionCell:ClearTweener()
    if self.size_change_tweener then
		self.size_change_tweener:Kill()
		self.size_change_timer = nil
    end
end
]]

---------------------------------------
-- 好感道具Item
---------------------------------------
AffectionGiftCell = AffectionGiftCell or BaseClass(BaseRender)
function AffectionGiftCell:__init()
end

function AffectionGiftCell:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_cell_root"])
		self.item_cell:SetIsShowTips(false)
		self.item_cell:SetClickCallBack(BindTool.Bind(self.OnClick, self))
	end

	XUI.AddClickEventListener(self.node_list["btn_show_item"], BindTool.Bind1(self.OnClickShowItemBtn, self))
end

function AffectionGiftCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function AffectionGiftCell:OnFlush()
	if not self.data then
		return
	end
	self.item_cell:SetData(self.data)
	self.item_cell:SetRightBottomTextVisible(false)
	self.node_list["favort_tag"]:SetActive(self.data.is_favor)
	self.node_list["mask"]:SetActive(self.data.num <= 0)
	local str = self.data.num > 0 and self.data.num or ToColorStr(Language.Artifact.NotHave, COLOR3B.C10)
	self.node_list["txt_bottom_text"].text.text = str
end

function AffectionGiftCell:OnSelectChange(is_select)
	self.node_list["img_selected"]:SetActive(is_select)
	self.node_list["btn_show_item"]:SetActive(is_select)
end

function AffectionGiftCell:OnClickShowItemBtn()
	if not self.data then
		return
	end
	TipWGCtrl.Instance:OpenItem(self.data)
end