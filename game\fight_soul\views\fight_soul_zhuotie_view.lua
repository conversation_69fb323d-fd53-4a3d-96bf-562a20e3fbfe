FightSoulZhuoTieView = FightSoulZhuoTieView or BaseClass(SafeBaseView)

function FightSoulZhuoTieView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(1, 1), sizeDelta = Vector2(886, 552)})
    self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_fight_soul_zhuotie")
end

function FightSoulZhuoTieView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.FightSoul.ZhuoTieTitleName

    if not self.zhuotie_list then
        self.zhuotie_list = AsyncListView.New(ZhuoTieItemRender, self.node_list.zhuotie_list)
        self.zhuotie_list:SetStartZeroIndex(true)
    end
end

function FightSoulZhuoTieView:ReleaseCallBack()
    if self.zhuotie_list then
        self.zhuotie_list:DeleteMe()
        self.zhuotie_list = nil
    end
end

function FightSoulZhuoTieView:OnFlush()
    local data_list = FightSoulWGData.Instance:GetZhuoTieCfg()
    local has_data = not IsEmptyTable(data_list)
    self.node_list.nodata:SetActive(not has_data)

    if has_data then
        self.zhuotie_list:SetDataList(data_list)
    end
end

ZhuoTieItemRender = ZhuoTieItemRender or BaseClass(BaseRender)
function ZhuoTieItemRender:LoadCallBack()
    if not self.cost_item then
        self.cost_item = ItemCell.New(self.node_list.cost_item)
    end

    XUI.AddClickEventListener(self.node_list.btn_use, BindTool.Bind(self.ClickBtnUse, self))
end

function ZhuoTieItemRender:__delete()
    if self.cost_item then
        self.cost_item:DeleteMe()
        self.cost_item = nil
    end
end

function ZhuoTieItemRender:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.cost_item_id
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    self.cost_item:SetFlushCallBack(function()
        local right_text = ToColorStr(item_num, (item_num > 0) and COLOR3B.D_GREEN or COLOR3B.D_RED)
        self.cost_item:SetRightBottomColorText(right_text)
        self.cost_item:SetRightBottomTextVisible(true)
    end)

    self.cost_item:SetData({item_id = item_id})
    self.node_list.cost_item_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    local attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.data, "attr_id", "attr_value", "attr_name", "attr_value", 1, 5, false)

    for i = 1, 4 do
        local data = attr_data[i]
        local has_data = not IsEmptyTable(data)
        self.node_list["name" .. i].text.text = has_data and data.attr_name or ""
        self.node_list["cur_value" .. i].text.text = has_data and data.attr_value or ""
    end

    local level = FightSoulWGData.Instance:GetZhuoTieLevelBySeq(self.data.seq)
    local is_max_level = level >= self.data.max_level
    self.node_list.btn_use:CustomSetActive(not is_max_level)
    self.node_list.max_level:CustomSetActive(is_max_level)
    XUI.SetGraphicGrey(self.node_list.btn_use, item_num <= 0)

    local per = math.floor(level / self.data.stage_level)
    local divisor = (per + 1) * self.data.stage_level
    divisor = divisor > self.data.max_level and self.data.max_level or divisor

    self.node_list.progress.slider.value = level / divisor
    self.node_list.pro_text.text.text = level .. "/" .. divisor
end

function ZhuoTieItemRender:ClickBtnUse()
    if not self.data then
        return
    end

    local item_id = self.data.cost_item_id
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    local level = FightSoulWGData.Instance:GetZhuoTieLevelBySeq(self.data.seq)
    local is_max_level = level >= self.data.max_level
    
    if not is_max_level then
        if item_num > 0 then
            local use_max_num = self.data.max_level - level
            local cost_num = item_num > use_max_num and use_max_num or item_num
            FightSoulWGCtrl.Instance:SendFightSoulCuiZhuoOperate(SIXIANG_QUENCH_OPERATE_TYPE.CUT_IRON_UPLEVEL, self.data.seq, cost_num)
        else
            TipWGCtrl.Instance:OpenItem({item_id = item_id})
        end
    end
end