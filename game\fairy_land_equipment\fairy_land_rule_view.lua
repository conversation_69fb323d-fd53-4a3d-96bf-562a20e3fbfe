FairyLandRuleView = FairyLandRuleView or BaseClass(SafeBaseView)

local RULE_POS_LIST = {
    [0] = {x = 3, y = 0},
    {x = -354, y = 193},
    {x = 355, y = 196},
    {x = -350, y = -166},
    {x = 355, y = -165},
}

local PAGE_NUM = 5

function FairyLandRuleView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.is_safe_area_adapter = true

	local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, bundle, "layout_fairy_land_rule_view")
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")

    local data = {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.LINGYU}
	self:SetTabShowUIScene(0, data)

    self.plane_index = -1
end

function FairyLandRuleView:LoadCallBack()
    self.cur_select_index = -1
    self.jump_to_page = -1
    self.is_prepare_active = false
    self.prepare_active_page = -1

    if not self.rule_list then
        self.rule_list = {}

        for i = 0, PAGE_NUM - 1 do
            local cell_obj = self.node_list["rule_" .. i]
            if cell_obj then
                local cell = FairyLandRuleItemRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectRule, self))
                cell:SetIndex(i)
                self.rule_list[i] = cell
            end
        end
    end

    if not self.attr_list then
		self.attr_list = {}
		local parent_node = self.node_list["rule_attr_list"]
		local attr_num = parent_node.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAttrRender.New(parent_node:FindObj("attr_" .. i))
			cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
			self.attr_list[i] = cell
		end
	end

    XUI.AddClickEventListener(self.node_list.btn_open_equipment_view, BindTool.Bind(self.OnClickOpenEquipmentViewBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_all_attr, BindTool.Bind2(self.OnClickOpenAllAttrPanel, self))
    XUI.AddClickEventListener(self.node_list.btn_book_get, BindTool.Bind(self.ClickGetBook, self))
    XUI.AddClickEventListener(self.node_list.btn_restore, BindTool.Bind(self.OnClickRestoreBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_act_god_body, BindTool.Bind(self.ClickActGodBody, self))
    XUI.AddClickEventListener(self.node_list.btn_restore2, BindTool.Bind(self.OnClickRestoreBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.CloseView, self))
    XUI.AddClickEventListener(self.node_list.btn_check, BindTool.Bind(self.OnClickCheckBtn, self))

    self.node_list.block:SetActive(false)
    self.node_list.btn_restore:SetActive(false)
end

function FairyLandRuleView:ReleaseCallBack()
    if self.rule_list and #self.rule_list > 0 then
        for i, cell in ipairs(self.rule_list) do
            cell:DeleteMe()
        end

        self.rule_list = nil
    end

    if self.attr_list then
		for k, v in pairs(self.attr_list) do
			v:DeleteMe()
		end
		self.attr_list = nil
	end

    self:CancelLandRuleTween()
    self:CancelLandRuleRestoreTween()
end

function FairyLandRuleView:__delete()

end

function FairyLandRuleView:OpenCallBack()

end

function FairyLandRuleView:ShowIndexCallBack()

end

function FairyLandRuleView:CloseCallBack()

end

function FairyLandRuleView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if k == "open_rule" then
            if v.to_slot then -- 跳书页
                self.plane_index = tonumber(v.to_slot)
                if v.to_ui_param then
                    self.jump_to_page = tonumber(v.to_ui_param)
                end
            end
        end
    end

    local body_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(self.plane_index)
    self.node_list.title_view_name.text.text = body_cfg.body_name
    -- if self.plane_index < 0 then
    --     return
    -- end

    local data_list = FairyLandEquipmentWGData.Instance:GetGodBookAllCfg()
    for i = 0, PAGE_NUM - 1 do
        self.rule_list[i]:SetPanelIndex(self.plane_index)
        self.rule_list[i]:SetData(data_list[i])
    end

    -- 战力
    self.node_list.cap_value.text.text = FairyLandEquipmentWGData.Instance:GetGBAllActAttrCap(self.plane_index)

    local jieling_remind = FairyLandEquipmentWGData.Instance:GetJieLingRemind()
    self.node_list.btn_open_equipment_remind:SetActive(jieling_remind)

    if self.cur_select_index >= 0 then
        self:FlushRightPanel(self.cur_select_index)
    end

    local max_show_num = FairyLandEquipmentWGData.Instance:GetGodBodyMaxShowNum1()
    self.node_list.btn_open_equipment_view:SetActive(max_show_num >= 0)

    self.node_list.jieling_condition.text.text = string.format(Language.FairyLandEquipment.JielingConditionDesc, body_cfg.jieling_name)

    if self.jump_to_page >= 0 then
        if self.cur_select_index < 0 then
            self:OnSelectRule(self.rule_list[self.jump_to_page])
        end
        self.jump_to_page = -1
    end
end

function FairyLandRuleView:FlushRightPanel(rule_index)
    local act_page_remind = FairyLandEquipmentWGData.Instance:GetPageActRemind(self.plane_index, rule_index)
    self.node_list["btn_act_god_body_remind"]:SetActive(act_page_remind)

    -- 属性
    local attr_list = FairyLandEquipmentWGData.Instance:GetGBAllActAttrList(self.plane_index, rule_index)
    for k, v in pairs(self.attr_list) do
        v:SetData(attr_list[k])
    end

    local book_cfg = FairyLandEquipmentWGData.Instance:GetGodBookCfg(rule_index)
    local bundle, asset = ResPath.GetRawImagesPNG(book_cfg.title_img)
    self.node_list.rule_title_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["rule_title_img"].raw_image:SetNativeSize()
    end)

    local act_num = FairyLandEquipmentWGData.Instance:GetPageChipGatherNum(self.plane_index, rule_index)
    local total_num = FairyLandEquipmentWGData.Instance:GetActPageNeedNum()
    self.node_list.progress_text.text.text = string.format(Language.FairyLandEquipment.RuleActProgressDesc2, act_num, total_num)

    local page_state = FairyLandEquipmentWGData.Instance:GetPageActState(self.plane_index, rule_index)
    local is_act = page_state == GOODS_STATE_TYPE.NORMAL
    self.node_list.progress_text:SetActive(not is_act)
    self.node_list.btn_act_god_body:SetActive(not is_act)
    self.node_list.active_flag:SetActive(is_act)
end

-- 激活书页或神体
function FairyLandRuleView:ClickActGodBody()
    local data = FairyLandEquipmentWGData.Instance:GetGodBodyData(self.plane_index)
    if data == nil then
        return
    end

    local slot = self.plane_index
    local page = self.cur_select_index
    local is_act = data:GetIsAct()

    -- 激活按钮
    local act_body_remind = FairyLandEquipmentWGData.Instance:GetGodBodyActRemind(slot)
    local act_page_remind = FairyLandEquipmentWGData.Instance:GetPageActRemind(slot, page)
    if act_page_remind and not act_body_remind then
        self.is_prepare_active = true
        self.prepare_active_page = page
        self:OnClickRestoreBtn()
        --FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_PAGE, slot, page)
    else
        if is_act then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.AlreadyActBodyDesc)
            return
        end

        local page_state = FairyLandEquipmentWGData.Instance:GetPageActState(slot, page)

        if (page_state == GOODS_STATE_TYPE.UNACT or page_state == GOODS_STATE_TYPE.READY_ACT) and not act_page_remind then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.NotActPageDesc)
            return
        end

        if page_state == GOODS_STATE_TYPE.NORMAL then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.AlreadyActPageDesc)
            return
        end

        if not act_body_remind then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.NotActBodyDesc)
            return
        end

        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_BODY, slot)
    end
end

function FairyLandRuleView:SetPlaneIndex(index)
    self.plane_index = index
end

function FairyLandRuleView:OnSelectRule(cell)
    if (not cell) or (not cell.data) then
		return
	end

    local data = cell.data
    if self.cur_select_index == cell.index then
        return
    end

    self.cur_select_index = cell.index
    self:FlushRightPanel(self.cur_select_index)
    self:PlayAnim()
end

function FairyLandRuleView:PlayAnim()
    self:SetPanelActive(false)
    self.node_list.block:SetActive(true)
    self.rule_list[self.cur_select_index]:PlayAnim()
    self:PlayCellAnim(self.rule_list[self.cur_select_index].view)
end

function FairyLandRuleView:OnClickRestoreBtn()
    self.node_list.block:SetActive(true)
    self.node_list.right_panel:SetActive(false)
    --self.node_list.chip_list:SetActive(false)
    self.rule_list[self.cur_select_index]:PlayRestoreAnim()
    self:PlayCellRestoreAnim(self.rule_list[self.cur_select_index].view)
end

function FairyLandRuleView:CloseView()
    if self.cur_select_index and self.cur_select_index >= 0 then
        self:OnClickRestoreBtn()
    else
        self:Close()
    end
end

function FairyLandRuleView:OnClickCheckBtn()
    local body_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(self.plane_index)
    local item_id = body_cfg.show_item_id
    TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

function FairyLandRuleView:RestoreView()
    if self.cur_select_index and self.cur_select_index >= 0 then
        self:OnClickRestoreBtn()
    end
end

function FairyLandRuleView:PlayCellAnim(node)
    local x = node.rect.anchoredPosition.x
    local y = node.rect.anchoredPosition.y
    UITween.CleanAllMoveToShowPanel("FairyLandRuleItemRender")
    local tween_move = UITween.MoveToShowPanel("FairyLandRuleItemRender", node, Vector2(x, y), Vector2(-263, -22), 0.3, DG.Tweening.Ease.Linear)
    local tween_scale = self.rule_list[self.cur_select_index].node_list.effect.transform:DOScale(Vector3(0.95, 0.95, 0.95), 0.3)
    tween_scale:SetEase(DG.Tweening.Ease.Linear)

    self:CancelLandRuleTween()
    local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_move)
	tween:Join(tween_scale)
	tween:OnComplete(function ()
        self.node_list.block:SetActive(false)
        self.node_list.right_panel:SetActive(true)
        self.rule_list[self.cur_select_index]:PlayAnimFinish()
    end)

    self.land_rule_cell_tween = tween
end

function FairyLandRuleView:CancelLandRuleTween()
    if self.land_rule_cell_tween then
        self.land_rule_cell_tween:Kill()
        self.land_rule_cell_tween = nil
    end
end

function FairyLandRuleView:PlayCellRestoreAnim(node)
    local x = node.rect.anchoredPosition.x
    local y = node.rect.anchoredPosition.y
    local target_x = RULE_POS_LIST[self.cur_select_index].x
    local target_y = RULE_POS_LIST[self.cur_select_index].y

    UITween.CleanAllMoveToShowPanel("FairyLandRuleItemRender")
    local scale = self.cur_select_index == 0 and 0.55 or 0.42

    local tween_move = UITween.MoveToShowPanel("FairyLandRuleItemRender", node, Vector2(x, y), Vector2(target_x, target_y), 0.3, DG.Tweening.Ease.Linear)
    local tween_scale = self.rule_list[self.cur_select_index].node_list.effect.transform:DOScale(Vector3(scale, scale, scale), 0.3)
    tween_scale:SetEase(DG.Tweening.Ease.Linear)

    self:CancelLandRuleRestoreTween()
    local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_move)
	tween:Join(tween_scale)
	tween:OnComplete(function ()
        self.node_list.block:SetActive(false)
        self:SetPanelActive(true)
        self.rule_list[self.cur_select_index]:PlayRestoreAnimFinish()
        self.cur_select_index = -1
        if self.is_prepare_active then
            self.is_prepare_active = false
            self.node_list.block:SetActive(true)
            self.rule_list[self.prepare_active_page]:PlayActiveEffect(function ()
                self.node_list.block:SetActive(false)
            end)
        end
    end)

    self.land_rule_cell_restore_tween = tween
end

function FairyLandRuleView:CancelLandRuleRestoreTween()
    if self.land_rule_cell_restore_tween then
        self.land_rule_cell_restore_tween:Kill()
        self.land_rule_cell_restore_tween = nil
    end
end

function FairyLandRuleView:SetPanelActive(bool)
    for i = 0, PAGE_NUM - 1 do
        if i ~= self.cur_select_index then
            self.rule_list[i]:SetVisible(bool)
        end
    end

    self.node_list.over:SetActive(bool)
    self.node_list.btn_restore:SetActive(not bool)
end

function FairyLandRuleView:OnClickOpenEquipmentViewBtn()
    FairyLandEquipmentWGCtrl.Instance:OpenEquipmentView()
end

-- 总属性
function FairyLandRuleView:OnClickOpenAllAttrPanel()
    -- 属性
    local attr_list = FairyLandEquipmentWGData.Instance:GetGBAllActAttrList(self.plane_index)

	local tips_data = {
		title_text = Language.FairyLandEquipment.AllAttrDesc,
		attr_data = attr_list,
	}

    if IsEmptyTable(tips_data.attr_data) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.JingMai.AttrTip)
        return
    end
	TipWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

function FairyLandRuleView:ClickGetBook()
    self:RestoreView()
    ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.XianJieBoss)
end

----------------------------------item-----------------------
FairyLandRuleItemRender = FairyLandRuleItemRender or BaseClass(BaseRender)
function FairyLandRuleItemRender:__init()
    self.plane_index = -1
    self.is_select = false
end

function FairyLandRuleItemRender:LoadCallBack()
    self.page_chip_list = {}
    local list_node = self.node_list["chip_list"]
    local chip_num = list_node.transform.childCount
    for i = 0, chip_num - 1 do
        self.page_chip_list[i] = GBPageChipRender.New(list_node:FindObj("chip_" .. i))
        self.page_chip_list[i]:SetIndex(i)
    end
end

function FairyLandRuleItemRender:ReleaseCallBack()
    if self.page_chip_list then
        for k, v in pairs(self.page_chip_list) do
            v:DeleteMe()
        end
        self.page_chip_list = nil
    end
end

function FairyLandRuleItemRender:OnFlush()
    if not self.data then return end

    self.node_list.normal_name.text.text = self.data.page_name
    self.node_list.select_name.text.text = self.data.page_name
    local data = FairyLandEquipmentWGData.Instance:GetGodBodyData(self.plane_index)
    if data == nil then
        return
    end

    local page = self.data.page
    local need_data = {slot = self.plane_index, page = page}
    if self.page_chip_list ~= nil then
        for k, v in pairs(self.page_chip_list) do
            v:SetData(need_data)
        end
    end

    local act_num = FairyLandEquipmentWGData.Instance:GetPageChipGatherNum(self.plane_index, self.data.page)
    local is_act = FairyLandEquipmentWGData.Instance:GetPageIsAct(self.plane_index, self.data.page)
    local is_collect = act_num > 0 and not is_act
    self.node_list.is_collect_text:SetActive(is_collect)

    XUI.SetGraphicGrey(self.node_list.name_bg, not is_act and not self.is_select)   --处理选中状态不管有没有激活都不置灰
    local bundle, asset = ResPath.GetEffectUi(self.data.effect)
	self.node_list.effect:ChangeAsset(bundle, asset)
    local effect_name = (not is_act and not self.is_select) and "UI_yuanshen_fazhe_huise" or self.data.effect
    self:FlushEffect(effect_name)

    self.node_list.remind:SetActive(FairyLandEquipmentWGData.Instance:GetPageRemind(self.plane_index, self.data.page))

    bundle, asset = ResPath.GetFairyLandEquipImages(self.data.name_bg)
    self.node_list.name_bg.image:LoadSprite(bundle, asset, function ()
        self.node_list["name_bg"].image:SetNativeSize()
    end)

    self.node_list.select_name_bg.image:LoadSprite(bundle, asset, function ()
        self.node_list["select_name_bg"].image:SetNativeSize()
    end)
end

function FairyLandRuleItemRender:SetPanelIndex(plane_index)
    self.plane_index = plane_index
end

function FairyLandRuleItemRender:PlayAnim()
    self.is_select = true
    self.node_list.normal_content:SetActive(false)
end

function FairyLandRuleItemRender:PlayAnimFinish()
    XUI.SetGraphicGrey(self.node_list.name_bg, false)
    local effect_name = self.data.effect
    self:FlushEffect(effect_name)
    self.node_list.select_content:SetActive(true)
end

function FairyLandRuleItemRender:PlayRestoreAnim()
    self.is_select = false
    self.node_list.select_content:SetActive(false)
    local is_act = FairyLandEquipmentWGData.Instance:GetPageIsAct(self.plane_index, self.data.page)
    XUI.SetGraphicGrey(self.node_list.name_bg, not is_act)
    local effect_name = not is_act and "UI_yuanshen_fazhe_huise" or self.data.effect
    self:FlushEffect(effect_name)
end

function FairyLandRuleItemRender:PlayRestoreAnimFinish()
    self.node_list.normal_content:SetActive(true)
end

function FairyLandRuleItemRender:FlushEffect(effect_name)
    local bundle, asset = ResPath.GetEffectUi(effect_name)
	self.node_list.effect:ChangeAsset(bundle, asset)
end

function FairyLandRuleItemRender:PlayActiveEffect(callback)
    local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanshen_fazhe_jiesuo")
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["active_effect_pos"].transform, 0.7, nil, nil, nil, nil, function ()
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_PAGE, self.plane_index, self.data.page)
        if callback then
            callback()
        end
    end)
end