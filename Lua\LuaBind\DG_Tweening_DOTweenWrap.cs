﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class DG_Tweening_DOTweenWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(DG.Tweening.DOTween), typeof(System.Object));
		<PERSON><PERSON>RegFunction("Init", Init);
		<PERSON><PERSON>Function("SetTweensCapacity", SetTweensCapacity);
		<PERSON><PERSON>unction("Clear", Clear);
		<PERSON><PERSON>unction("ClearCachedTweens", ClearCachedTweens);
		L.RegFunction("Validate", Validate);
		<PERSON>.RegFunction("ManualUpdate", ManualUpdate);
		<PERSON><PERSON>Function("To", To);
		<PERSON><PERSON>RegFunction("ToAxis", ToAxis);
		<PERSON><PERSON>unction("ToAlpha", ToAlpha);
		<PERSON><PERSON>RegFunction("Punch", Punch);
		<PERSON>.RegFunction("Shake", Shake);
		<PERSON><PERSON>RegFunction("ToArray", ToArray);
		<PERSON><PERSON>RegFunction("Sequence", Sequence);
		<PERSON><PERSON>RegFunction("CompleteAll", CompleteAll);
		<PERSON><PERSON>RegFunction("Complete", Complete);
		<PERSON><PERSON>RegFunction("FlipAll", FlipAll);
		<PERSON><PERSON>Function("Flip", Flip);
		L.RegFunction("GotoAll", GotoAll);
		L.RegFunction("Goto", Goto);
		L.RegFunction("KillAll", KillAll);
		L.RegFunction("Kill", Kill);
		L.RegFunction("PauseAll", PauseAll);
		L.RegFunction("Pause", Pause);
		L.RegFunction("PlayAll", PlayAll);
		L.RegFunction("Play", Play);
		L.RegFunction("PlayBackwardsAll", PlayBackwardsAll);
		L.RegFunction("PlayBackwards", PlayBackwards);
		L.RegFunction("PlayForwardAll", PlayForwardAll);
		L.RegFunction("PlayForward", PlayForward);
		L.RegFunction("RestartAll", RestartAll);
		L.RegFunction("Restart", Restart);
		L.RegFunction("RewindAll", RewindAll);
		L.RegFunction("Rewind", Rewind);
		L.RegFunction("SmoothRewindAll", SmoothRewindAll);
		L.RegFunction("SmoothRewind", SmoothRewind);
		L.RegFunction("TogglePauseAll", TogglePauseAll);
		L.RegFunction("TogglePause", TogglePause);
		L.RegFunction("IsTweening", IsTweening);
		L.RegFunction("TotalActiveTweens", TotalActiveTweens);
		L.RegFunction("TotalActiveTweeners", TotalActiveTweeners);
		L.RegFunction("TotalActiveSequences", TotalActiveSequences);
		L.RegFunction("TotalPlayingTweens", TotalPlayingTweens);
		L.RegFunction("TotalTweensById", TotalTweensById);
		L.RegFunction("PlayingTweens", PlayingTweens);
		L.RegFunction("PausedTweens", PausedTweens);
		L.RegFunction("TweensById", TweensById);
		L.RegFunction("TweensByTarget", TweensByTarget);
		L.RegFunction("New", _CreateDG_Tweening_DOTween);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("Version", get_Version, null);
		L.RegVar("useSafeMode", get_useSafeMode, set_useSafeMode);
		L.RegVar("safeModeLogBehaviour", get_safeModeLogBehaviour, set_safeModeLogBehaviour);
		L.RegVar("nestedTweenFailureBehaviour", get_nestedTweenFailureBehaviour, set_nestedTweenFailureBehaviour);
		L.RegVar("showUnityEditorReport", get_showUnityEditorReport, set_showUnityEditorReport);
		L.RegVar("timeScale", get_timeScale, set_timeScale);
		L.RegVar("unscaledTimeScale", get_unscaledTimeScale, set_unscaledTimeScale);
		L.RegVar("useSmoothDeltaTime", get_useSmoothDeltaTime, set_useSmoothDeltaTime);
		L.RegVar("maxSmoothUnscaledTime", get_maxSmoothUnscaledTime, set_maxSmoothUnscaledTime);
		L.RegVar("onWillLog", get_onWillLog, set_onWillLog);
		L.RegVar("drawGizmos", get_drawGizmos, set_drawGizmos);
		L.RegVar("debugMode", get_debugMode, set_debugMode);
		L.RegVar("defaultUpdateType", get_defaultUpdateType, set_defaultUpdateType);
		L.RegVar("defaultTimeScaleIndependent", get_defaultTimeScaleIndependent, set_defaultTimeScaleIndependent);
		L.RegVar("defaultAutoPlay", get_defaultAutoPlay, set_defaultAutoPlay);
		L.RegVar("defaultAutoKill", get_defaultAutoKill, set_defaultAutoKill);
		L.RegVar("defaultLoopType", get_defaultLoopType, set_defaultLoopType);
		L.RegVar("defaultRecyclable", get_defaultRecyclable, set_defaultRecyclable);
		L.RegVar("defaultEaseType", get_defaultEaseType, set_defaultEaseType);
		L.RegVar("defaultEaseOvershootOrAmplitude", get_defaultEaseOvershootOrAmplitude, set_defaultEaseOvershootOrAmplitude);
		L.RegVar("defaultEasePeriod", get_defaultEasePeriod, set_defaultEasePeriod);
		L.RegVar("instance", get_instance, set_instance);
		L.RegVar("logBehaviour", get_logBehaviour, set_logBehaviour);
		L.RegVar("debugStoreTargetId", get_debugStoreTargetId, set_debugStoreTargetId);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateDG_Tweening_DOTween(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				DG.Tweening.DOTween obj = new DG.Tweening.DOTween();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: DG.Tweening.DOTween.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Init(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				DG.Tweening.IDOTweenInit o = DG.Tweening.DOTween.Init();
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 1)
			{
				System.Nullable<bool> arg0 = ToLua.CheckNullable<bool>(L, 1);
				DG.Tweening.IDOTweenInit o = DG.Tweening.DOTween.Init(arg0);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2)
			{
				System.Nullable<bool> arg0 = ToLua.CheckNullable<bool>(L, 1);
				System.Nullable<bool> arg1 = ToLua.CheckNullable<bool>(L, 2);
				DG.Tweening.IDOTweenInit o = DG.Tweening.DOTween.Init(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 3)
			{
				System.Nullable<bool> arg0 = ToLua.CheckNullable<bool>(L, 1);
				System.Nullable<bool> arg1 = ToLua.CheckNullable<bool>(L, 2);
				System.Nullable<DG.Tweening.LogBehaviour> arg2 = ToLua.CheckNullable<DG.Tweening.LogBehaviour>(L, 3);
				DG.Tweening.IDOTweenInit o = DG.Tweening.DOTween.Init(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Init");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTweensCapacity(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			DG.Tweening.DOTween.SetTweensCapacity(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Clear(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				DG.Tweening.DOTween.Clear();
				return 0;
			}
			else if (count == 1)
			{
				bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
				DG.Tweening.DOTween.Clear(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Clear");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearCachedTweens(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			DG.Tweening.DOTween.ClearCachedTweens();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Validate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.Validate();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ManualUpdate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
			DG.Tweening.DOTween.ManualUpdate(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int To(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOSetter<float>, float, float, float>(L, 1))
			{
				DG.Tweening.Core.DOSetter<float> arg0 = (DG.Tweening.Core.DOSetter<float>)ToLua.ToObject(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Tweener o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<float>, DG.Tweening.Core.DOSetter<float>, float, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<float> arg0 = (DG.Tweening.Core.DOGetter<float>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<float> arg1 = (DG.Tweening.Core.DOSetter<float>)ToLua.ToObject(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<float,float,DG.Tweening.Plugins.Options.FloatOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<double>, DG.Tweening.Core.DOSetter<double>, double, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<double> arg0 = (DG.Tweening.Core.DOGetter<double>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<double> arg1 = (DG.Tweening.Core.DOSetter<double>)ToLua.ToObject(L, 2);
				double arg2 = (double)LuaDLL.lua_tonumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<double,double,DG.Tweening.Plugins.Options.NoOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<int>, DG.Tweening.Core.DOSetter<int>, int, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<int> arg0 = (DG.Tweening.Core.DOGetter<int>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<int> arg1 = (DG.Tweening.Core.DOSetter<int>)ToLua.ToObject(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<int,int,DG.Tweening.Plugins.Options.NoOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<uint>, DG.Tweening.Core.DOSetter<uint>, uint, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<uint> arg0 = (DG.Tweening.Core.DOGetter<uint>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<uint> arg1 = (DG.Tweening.Core.DOSetter<uint>)ToLua.ToObject(L, 2);
				uint arg2 = (uint)LuaDLL.lua_tonumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<uint,uint,DG.Tweening.Plugins.Options.UintOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<long>, DG.Tweening.Core.DOSetter<long>, long, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<long> arg0 = (DG.Tweening.Core.DOGetter<long>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<long> arg1 = (DG.Tweening.Core.DOSetter<long>)ToLua.ToObject(L, 2);
				long arg2 = LuaDLL.tolua_toint64(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<long,long,DG.Tweening.Plugins.Options.NoOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<ulong>, DG.Tweening.Core.DOSetter<ulong>, ulong, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<ulong> arg0 = (DG.Tweening.Core.DOGetter<ulong>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<ulong> arg1 = (DG.Tweening.Core.DOSetter<ulong>)ToLua.ToObject(L, 2);
				ulong arg2 = LuaDLL.tolua_touint64(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<ulong,ulong,DG.Tweening.Plugins.Options.NoOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<string>, DG.Tweening.Core.DOSetter<string>, string, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<string> arg0 = (DG.Tweening.Core.DOGetter<string>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<string> arg1 = (DG.Tweening.Core.DOSetter<string>)ToLua.ToObject(L, 2);
				string arg2 = ToLua.ToString(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<string,string,DG.Tweening.Plugins.Options.StringOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<UnityEngine.Vector2>, DG.Tweening.Core.DOSetter<UnityEngine.Vector2>, UnityEngine.Vector2, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector2> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector2>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector2> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector2>)ToLua.ToObject(L, 2);
				UnityEngine.Vector2 arg2 = ToLua.ToVector2(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>, DG.Tweening.Core.DOSetter<UnityEngine.Vector3>, UnityEngine.Vector3, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.ToObject(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<UnityEngine.Vector4>, DG.Tweening.Core.DOSetter<UnityEngine.Vector4>, UnityEngine.Vector4, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector4> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector4>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector4> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector4>)ToLua.ToObject(L, 2);
				UnityEngine.Vector4 arg2 = ToLua.ToVector4(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<UnityEngine.Quaternion>, DG.Tweening.Core.DOSetter<UnityEngine.Quaternion>, UnityEngine.Vector3, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Quaternion> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Quaternion>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Quaternion> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Quaternion>)ToLua.ToObject(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<UnityEngine.Color>, DG.Tweening.Core.DOSetter<UnityEngine.Color>, UnityEngine.Color, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Color> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Color>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Color> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Color>)ToLua.ToObject(L, 2);
				UnityEngine.Color arg2 = ToLua.ToColor(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<UnityEngine.Rect>, DG.Tweening.Core.DOSetter<UnityEngine.Rect>, UnityEngine.Rect, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Rect> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Rect>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Rect> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Rect>)ToLua.ToObject(L, 2);
				UnityEngine.Rect arg2 = StackTraits<UnityEngine.Rect>.To(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions> o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<DG.Tweening.Core.DOGetter<UnityEngine.RectOffset>, DG.Tweening.Core.DOSetter<UnityEngine.RectOffset>, UnityEngine.RectOffset, float>(L, 1))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.RectOffset> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.RectOffset>)ToLua.ToObject(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.RectOffset> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.RectOffset>)ToLua.ToObject(L, 2);
				UnityEngine.RectOffset arg2 = (UnityEngine.RectOffset)ToLua.ToObject(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Tweener o = DG.Tweening.DOTween.To(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.To");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToAxis(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> o = DG.Tweening.DOTween.ToAxis(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 5)
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				DG.Tweening.AxisConstraint arg4 = (DG.Tweening.AxisConstraint)ToLua.CheckObject(L, 5, typeof(DG.Tweening.AxisConstraint));
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> o = DG.Tweening.DOTween.ToAxis(arg0, arg1, arg2, arg3, arg4);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.ToAxis");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToAlpha(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			DG.Tweening.Core.DOGetter<UnityEngine.Color> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Color>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Color>>(L, 1);
			DG.Tweening.Core.DOSetter<UnityEngine.Color> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Color>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Color>>(L, 2);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
			DG.Tweening.Core.TweenerCore<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> o = DG.Tweening.DOTween.ToAlpha(arg0, arg1, arg2, arg3);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Punch(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Punch(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 5)
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Punch(arg0, arg1, arg2, arg3, arg4);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 6)
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Punch(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Punch");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Shake(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<float>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<float, int>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Vector3, int>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<float, int, float>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Vector3, int, float>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 7 && TypeChecker.CheckTypes<float, int, float, bool>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				bool arg6 = LuaDLL.lua_toboolean(L, 7);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 7 && TypeChecker.CheckTypes<UnityEngine.Vector3, int, float, bool>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				bool arg6 = LuaDLL.lua_toboolean(L, 7);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 8 && TypeChecker.CheckTypes<float, int, float, bool, bool>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				bool arg6 = LuaDLL.lua_toboolean(L, 7);
				bool arg7 = LuaDLL.lua_toboolean(L, 8);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 8 && TypeChecker.CheckTypes<UnityEngine.Vector3, int, float, bool, DG.Tweening.ShakeRandomnessMode>(L, 4))
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				bool arg6 = LuaDLL.lua_toboolean(L, 7);
				DG.Tweening.ShakeRandomnessMode arg7 = (DG.Tweening.ShakeRandomnessMode)ToLua.ToObject(L, 8);
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 9)
			{
				DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
				DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				bool arg6 = LuaDLL.luaL_checkboolean(L, 7);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 8);
				DG.Tweening.ShakeRandomnessMode arg8 = (DG.Tweening.ShakeRandomnessMode)ToLua.CheckObject(L, 9, typeof(DG.Tweening.ShakeRandomnessMode));
				DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.Shake(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Shake");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToArray(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			DG.Tweening.Core.DOGetter<UnityEngine.Vector3> arg0 = (DG.Tweening.Core.DOGetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>(L, 1);
			DG.Tweening.Core.DOSetter<UnityEngine.Vector3> arg1 = (DG.Tweening.Core.DOSetter<UnityEngine.Vector3>)ToLua.CheckDelegate<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>(L, 2);
			UnityEngine.Vector3[] arg2 = ToLua.CheckStructArray<UnityEngine.Vector3>(L, 3);
			float[] arg3 = ToLua.CheckNumberArray<float>(L, 4);
			DG.Tweening.Core.TweenerCore<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> o = DG.Tweening.DOTween.ToArray(arg0, arg1, arg2, arg3);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Sequence(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				DG.Tweening.Sequence o = DG.Tweening.DOTween.Sequence();
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				DG.Tweening.Sequence o = DG.Tweening.DOTween.Sequence(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Sequence");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CompleteAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				int o = DG.Tweening.DOTween.CompleteAll();
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 1)
			{
				bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
				int o = DG.Tweening.DOTween.CompleteAll(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.CompleteAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Complete(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.Complete(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				int o = DG.Tweening.DOTween.Complete(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Complete");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FlipAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.FlipAll();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Flip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			object arg0 = ToLua.ToVarObject(L, 1);
			int o = DG.Tweening.DOTween.Flip(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GotoAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
				int o = DG.Tweening.DOTween.GotoAll(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				int o = DG.Tweening.DOTween.GotoAll(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.GotoAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Goto(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				int o = DG.Tweening.DOTween.Goto(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 3)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				int o = DG.Tweening.DOTween.Goto(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Goto");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int KillAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				int o = DG.Tweening.DOTween.KillAll();
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<bool>(L, 1))
			{
				bool arg0 = LuaDLL.lua_toboolean(L, 1);
				int o = DG.Tweening.DOTween.KillAll(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (TypeChecker.CheckTypes<bool>(L, 1) && TypeChecker.CheckParamsType<object>(L, 2, count - 1))
			{
				bool arg0 = LuaDLL.lua_toboolean(L, 1);
				object[] arg1 = ToLua.ToParamsObject(L, 2, count - 1);
				int o = DG.Tweening.DOTween.KillAll(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.KillAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Kill(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.Kill(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<bool>(L, 2))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.lua_toboolean(L, 2);
				int o = DG.Tweening.DOTween.Kill(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<object>(L, 2))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				int o = DG.Tweening.DOTween.Kill(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 3)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				int o = DG.Tweening.DOTween.Kill(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Kill");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PauseAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.PauseAll();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Pause(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			object arg0 = ToLua.ToVarObject(L, 1);
			int o = DG.Tweening.DOTween.Pause(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.PlayAll();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.Play(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				int o = DG.Tweening.DOTween.Play(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Play");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayBackwardsAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.PlayBackwardsAll();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayBackwards(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.PlayBackwards(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				int o = DG.Tweening.DOTween.PlayBackwards(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.PlayBackwards");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayForwardAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.PlayForwardAll();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayForward(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.PlayForward(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				int o = DG.Tweening.DOTween.PlayForward(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.PlayForward");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RestartAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				int o = DG.Tweening.DOTween.RestartAll();
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 1)
			{
				bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
				int o = DG.Tweening.DOTween.RestartAll(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.RestartAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Restart(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.Restart(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<bool>(L, 2))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.lua_toboolean(L, 2);
				int o = DG.Tweening.DOTween.Restart(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<object>(L, 2))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				int o = DG.Tweening.DOTween.Restart(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<bool, float>(L, 2))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.lua_toboolean(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int o = DG.Tweening.DOTween.Restart(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<object, bool>(L, 2))
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				bool arg2 = LuaDLL.lua_toboolean(L, 3);
				int o = DG.Tweening.DOTween.Restart(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 4)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				object arg1 = ToLua.ToVarObject(L, 2);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				int o = DG.Tweening.DOTween.Restart(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Restart");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RewindAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				int o = DG.Tweening.DOTween.RewindAll();
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 1)
			{
				bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
				int o = DG.Tweening.DOTween.RewindAll(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.RewindAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Rewind(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.Rewind(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				int o = DG.Tweening.DOTween.Rewind(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.Rewind");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SmoothRewindAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.SmoothRewindAll();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SmoothRewind(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			object arg0 = ToLua.ToVarObject(L, 1);
			int o = DG.Tweening.DOTween.SmoothRewind(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TogglePauseAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.TogglePauseAll();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TogglePause(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			object arg0 = ToLua.ToVarObject(L, 1);
			int o = DG.Tweening.DOTween.TogglePause(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsTweening(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool o = DG.Tweening.DOTween.IsTweening(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				bool o = DG.Tweening.DOTween.IsTweening(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.IsTweening");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TotalActiveTweens(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.TotalActiveTweens();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TotalActiveTweeners(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.TotalActiveTweeners();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TotalActiveSequences(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.TotalActiveSequences();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TotalPlayingTweens(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = DG.Tweening.DOTween.TotalPlayingTweens();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TotalTweensById(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				int o = DG.Tweening.DOTween.TotalTweensById(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				int o = DG.Tweening.DOTween.TotalTweensById(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.TotalTweensById");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayingTweens(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.PlayingTweens();
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 1)
			{
				System.Collections.Generic.List<DG.Tweening.Tween> arg0 = (System.Collections.Generic.List<DG.Tweening.Tween>)ToLua.CheckObject(L, 1, typeof(System.Collections.Generic.List<DG.Tweening.Tween>));
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.PlayingTweens(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.PlayingTweens");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PausedTweens(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.PausedTweens();
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 1)
			{
				System.Collections.Generic.List<DG.Tweening.Tween> arg0 = (System.Collections.Generic.List<DG.Tweening.Tween>)ToLua.CheckObject(L, 1, typeof(System.Collections.Generic.List<DG.Tweening.Tween>));
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.PausedTweens(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.PausedTweens");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TweensById(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.TweensById(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.TweensById(arg0, arg1);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 3)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				System.Collections.Generic.List<DG.Tweening.Tween> arg2 = (System.Collections.Generic.List<DG.Tweening.Tween>)ToLua.CheckObject(L, 3, typeof(System.Collections.Generic.List<DG.Tweening.Tween>));
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.TweensById(arg0, arg1, arg2);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.TweensById");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TweensByTarget(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.TweensByTarget(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 2)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.TweensByTarget(arg0, arg1);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 3)
			{
				object arg0 = ToLua.ToVarObject(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				System.Collections.Generic.List<DG.Tweening.Tween> arg2 = (System.Collections.Generic.List<DG.Tweening.Tween>)ToLua.CheckObject(L, 3, typeof(System.Collections.Generic.List<DG.Tweening.Tween>));
				System.Collections.Generic.List<DG.Tweening.Tween> o = DG.Tweening.DOTween.TweensByTarget(arg0, arg1, arg2);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DG.Tweening.DOTween.TweensByTarget");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Version(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, DG.Tweening.DOTween.Version);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useSafeMode(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.useSafeMode);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_safeModeLogBehaviour(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.safeModeLogBehaviour);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_nestedTweenFailureBehaviour(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.nestedTweenFailureBehaviour);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_showUnityEditorReport(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.showUnityEditorReport);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timeScale(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, DG.Tweening.DOTween.timeScale);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_unscaledTimeScale(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, DG.Tweening.DOTween.unscaledTimeScale);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useSmoothDeltaTime(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.useSmoothDeltaTime);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxSmoothUnscaledTime(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, DG.Tweening.DOTween.maxSmoothUnscaledTime);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onWillLog(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.onWillLog);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_drawGizmos(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.drawGizmos);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_debugMode(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.debugMode);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultUpdateType(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.defaultUpdateType);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTimeScaleIndependent(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.defaultTimeScaleIndependent);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultAutoPlay(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.defaultAutoPlay);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultAutoKill(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.defaultAutoKill);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultLoopType(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.defaultLoopType);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultRecyclable(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.defaultRecyclable);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultEaseType(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.defaultEaseType);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultEaseOvershootOrAmplitude(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, DG.Tweening.DOTween.defaultEaseOvershootOrAmplitude);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultEasePeriod(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, DG.Tweening.DOTween.defaultEasePeriod);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_instance(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_logBehaviour(IntPtr L)
	{
		try
		{
			ToLua.Push(L, DG.Tweening.DOTween.logBehaviour);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_debugStoreTargetId(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DG.Tweening.DOTween.debugStoreTargetId);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useSafeMode(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.useSafeMode = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_safeModeLogBehaviour(IntPtr L)
	{
		try
		{
			DG.Tweening.Core.Enums.SafeModeLogBehaviour arg0 = (DG.Tweening.Core.Enums.SafeModeLogBehaviour)ToLua.CheckObject(L, 2, typeof(DG.Tweening.Core.Enums.SafeModeLogBehaviour));
			DG.Tweening.DOTween.safeModeLogBehaviour = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_nestedTweenFailureBehaviour(IntPtr L)
	{
		try
		{
			DG.Tweening.Core.Enums.NestedTweenFailureBehaviour arg0 = (DG.Tweening.Core.Enums.NestedTweenFailureBehaviour)ToLua.CheckObject(L, 2, typeof(DG.Tweening.Core.Enums.NestedTweenFailureBehaviour));
			DG.Tweening.DOTween.nestedTweenFailureBehaviour = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_showUnityEditorReport(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.showUnityEditorReport = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_timeScale(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			DG.Tweening.DOTween.timeScale = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_unscaledTimeScale(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			DG.Tweening.DOTween.unscaledTimeScale = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useSmoothDeltaTime(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.useSmoothDeltaTime = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxSmoothUnscaledTime(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			DG.Tweening.DOTween.maxSmoothUnscaledTime = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onWillLog(IntPtr L)
	{
		try
		{
			System.Func<UnityEngine.LogType,object,bool> arg0 = (System.Func<UnityEngine.LogType,object,bool>)ToLua.CheckDelegate<System.Func<UnityEngine.LogType,object,bool>>(L, 2);
			DG.Tweening.DOTween.onWillLog = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_drawGizmos(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.drawGizmos = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_debugMode(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.debugMode = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultUpdateType(IntPtr L)
	{
		try
		{
			DG.Tweening.UpdateType arg0 = (DG.Tweening.UpdateType)ToLua.CheckObject(L, 2, typeof(DG.Tweening.UpdateType));
			DG.Tweening.DOTween.defaultUpdateType = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultTimeScaleIndependent(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.defaultTimeScaleIndependent = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultAutoPlay(IntPtr L)
	{
		try
		{
			DG.Tweening.AutoPlay arg0 = (DG.Tweening.AutoPlay)ToLua.CheckObject(L, 2, typeof(DG.Tweening.AutoPlay));
			DG.Tweening.DOTween.defaultAutoPlay = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultAutoKill(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.defaultAutoKill = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultLoopType(IntPtr L)
	{
		try
		{
			DG.Tweening.LoopType arg0 = (DG.Tweening.LoopType)ToLua.CheckObject(L, 2, typeof(DG.Tweening.LoopType));
			DG.Tweening.DOTween.defaultLoopType = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultRecyclable(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.defaultRecyclable = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultEaseType(IntPtr L)
	{
		try
		{
			DG.Tweening.Ease arg0 = (DG.Tweening.Ease)ToLua.CheckObject(L, 2, typeof(DG.Tweening.Ease));
			DG.Tweening.DOTween.defaultEaseType = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultEaseOvershootOrAmplitude(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			DG.Tweening.DOTween.defaultEaseOvershootOrAmplitude = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultEasePeriod(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			DG.Tweening.DOTween.defaultEasePeriod = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_instance(IntPtr L)
	{
		try
		{
			DG.Tweening.Core.DOTweenComponent arg0 = (DG.Tweening.Core.DOTweenComponent)ToLua.CheckObject<DG.Tweening.Core.DOTweenComponent>(L, 2);
			DG.Tweening.DOTween.instance = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_logBehaviour(IntPtr L)
	{
		try
		{
			DG.Tweening.LogBehaviour arg0 = (DG.Tweening.LogBehaviour)ToLua.CheckObject(L, 2, typeof(DG.Tweening.LogBehaviour));
			DG.Tweening.DOTween.logBehaviour = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_debugStoreTargetId(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DG.Tweening.DOTween.debugStoreTargetId = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

