------------------------ 战魂列表item ------------------------
FightSoulItem = FightSoulItem or BaseClass(BaseRender)
function FightSoulItem:__init()
    self.star_list = {}
    self.old_lock_state = nil
    self.is_doing_uplock_tween = false
    local star_num = self.node_list.t_star_part.transform.childCount
    for i = 1, star_num do
        self.star_list[i] = self.node_list["star_" .. i]
    end
    self.node_list.unlock_effect:SetActive(false)
end

function FightSoulItem:LoadCallBack()
    self:CleanTween()
	local tween_time = 0.8
    local node = self.node_list["arrow"]
	if node then
        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -15)
        self.arrow_tweener = node.rect:DOAnchorPosY(-7, tween_time)
        self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end

    self.view.button:AddClickListener(BindTool.Bind(self.ListEventCallback, self))
end

function FightSoulItem:CleanTween()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end
end

function FightSoulItem:__delete()
    self.fight_soul_slot = nil
    self.star_list = nil
    self.old_lock_state = nil
    self.is_doing_uplock_tween = nil
    self:CleanTween()

    if CountDownManager.Instance:HasCountDown("DoUnLockTween" .. self.index) then
        CountDownManager.Instance:RemoveCountDown("DoUnLockTween" .. self.index)
    end
end

function FightSoulItem:InitState()

end

function FightSoulItem:ListEventCallback()
    if self:IsLimitSelectByIndex() then
		return
	end

    if self.select_call_back then
        self.select_call_back(self, true)
    end
end

function FightSoulItem:IsLimitSelectByIndex()
    if self.data == nil then
        return
    end

    local slot = self.data.sixiang_slot_index
    local cfg = FightSoulWGData.Instance:GetFightslotCfgBySlot(slot)
    if IsEmptyTable(cfg) then
        return false
    end

    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = cfg.role_level_limit
    local is_unlock = FightSoulWGData.Instance:GetSlotLockFlag(slot)
    if role_level < need_level then
        TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.FightSoul.WearLimit, RoleWGData.GetLevelString2(need_level)))
        return true
    elseif not is_unlock and cfg.stuff_id > 0 and cfg.stuff_num > 0 then
        FightSoulWGCtrl.Instance:OpenFightSoulUnlockTips(slot)
        return true
    end

	return false
end

function FightSoulItem:OnFlush()
	if IsEmptyTable(self.data) then
        self:InitState()
        self.fight_soul_slot = nil
		return
	end

    local fs_data = FightSoulWGData.Instance
	self.fight_soul_slot = fs_data:GetFightSoulSlot(self.data.sixiang_slot_index)
    if IsEmptyTable(self.fight_soul_slot) then
        self:InitState()
        return
    end

    local slot_index = self.fight_soul_slot:GetSlotIndex()
    local is_lock = self.fight_soul_slot:IsLock()
    local is_wear = self.fight_soul_slot:GetIsWear()
    local level = self.fight_soul_slot:GetLevel()
    self.node_list.fight_tag:SetActive(self.fight_soul_slot:GetOutFightFlag())

    local slot_data = FightSoulWGData.Instance:GetFightSoulSlot(slot_index)
    self.node_list.level.text.text = is_wear and string.format(Language.FightSoul.SlotInfo, slot_data:GetName(), level) or ""
    self:SetStar(self.fight_soul_slot:GetStar())
    if is_lock then
        local slot_cfg = FightSoulWGData.Instance:GetFightslotCfgBySlot(slot_index)
        if slot_cfg then
            local role_level = GameVoManager.Instance:GetMainRoleVo().level
            local need_level = slot_cfg.role_level_limit
            if role_level < need_level then
                self.node_list.no_wear_text.text.text = string.format(Language.FightSoul.SlotNotDataStr[1], RoleWGData.GetLevelString2(need_level))
            elseif slot_cfg.stuff_id > 0 and slot_cfg.stuff_num > 0 then
                self.node_list.no_wear_text.text.text = Language.FightSoul.SlotNotDataStr[2]
            end
        end
    elseif not is_wear then
        self.node_list.no_wear_text.text.text = Language.FightSoul.SlotNotDataStr[3]
    end

    self.node_list.no_wear_text:SetActive(not is_wear or is_lock)
    if self.old_lock_state and not is_lock and not self.is_doing_uplock_tween then
        self.is_doing_uplock_tween = true
        -- self:DoUnLockTween()
    end
    self.old_lock_state = is_lock

    if is_wear then
        local bundle, asset = ResPath.GetRawImagesPNG("a2_ymgy_js_k" .. self.fight_soul_slot:GetIconId())
        self.node_list.icon.raw_image:LoadSprite(bundle, asset, function()
            self.node_list.icon.raw_image:SetNativeSize()
            self.node_list.icon:SetActive(true)
        end)
    else
        self.node_list.icon:SetActive(false)
    end

    local bg_bundle, bg_asset = ResPath.GetRawImagesPNG("a2_ymgy_di_" .. self.fight_soul_slot:GetColor())
    self.node_list.color_bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.color_bg.raw_image:SetNativeSize()
    end)

    local better_remind = false
    if not is_lock then
        better_remind = fs_data:GetFightSoulCanWearByType(slot_index)
    end
    self.node_list["arrow"]:SetActive(better_remind)

    local need_check_remind = not is_lock
    if not better_remind and need_check_remind then
        local remind = false 
        local show_index = FightSoulWGCtrl.Instance:GetViewShowIndex()
        if show_index == TabIndex.fight_soul_train then
            remind = fs_data:GetSingleSlotRemind(slot_index, true)
        elseif show_index == TabIndex.fight_soul_bone then
            remind = fs_data:GetBoneSingleSlotRemind(slot_index, true)
        elseif show_index == TabIndex.fight_soul_cuizhuo then
            remind = fs_data:GetCuiZhuoSingleSlotRemind(slot_index, true)
        end
        self.node_list.remind:SetActive(remind)
    else
        self.node_list.remind:SetActive(false)
    end
end

function FightSoulItem:SetStar(num)
    num = num or 0
    local star_res_list = GetStarImgResByStar(num)
    local bundle, asset = "", ""
    for i = 1, GameEnum.ITEM_MAX_STAR do
        if self.star_list[i] then
            if i <= num then
                self.star_list[i]:SetActive(true)
                bundle, asset = ResPath.GetCommonImages(star_res_list[i])
                self.star_list[i].image:LoadSprite(bundle, asset)
            else
                self.star_list[i]:SetActive(false)
            end
        end
    end
end

function FightSoulItem:OnSelectChange(is_select)
	self.node_list.select_bg:SetActive(is_select)
end

function FightSoulItem:PalyItemAnimator(item_index)
    local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.SiXiangSys.SlotTween

	UITween.FakeHideShow(self.node_list["content"])
	ReDelayCall(self, function()
		if self.node_list and self.node_list["content"] then
			UITween.MoveAlphaShow(GuideModuleName.FightSoulView, self.node_list["content"], tween_info)
		end

	end, tween_info.NextDoDelay * wait_index, "FightSoulItem" .. wait_index)
end

function FightSoulItem:DoUnLockTween()
    if not self.is_doing_uplock_tween then
        return
    end

    self.node_list.lock:SetActive(true)
    self.node_list.no_wear_text:SetActive(false)
    self.node_list.remind.image.enabled = false
    self.node_list.arrow.image.enabled = false

    self.node_list.unlock_effect:SetActive(false)
    self.node_list.unlock_effect:SetActive(true)
    local total_time = 1.3
    local lock_miss_time = 0.4
    local interval = 0.1
    local miss_lock

    CountDownManager.Instance:AddCountDown("DoUnLockTween" .. self.index,
        function(elapse_time, total_time)
            if elapse_time >= lock_miss_time and not miss_lock then
                self.node_list.lock:SetActive(false)
				miss_lock = true
            end
        end,

        function()
            self.node_list.no_wear_text:SetActive(true)
            self.node_list.remind.image.enabled = true
            self.node_list.arrow.image.enabled = true
            self.is_doing_uplock_tween = false
            self.node_list.unlock_effect:SetActive(false)
        end,
    nil, total_time, interval)
end

-- 属性
-------------------------FightSoulAttrRender---------------------------------------
FightSoulAttrRender = FightSoulAttrRender or BaseClass(BaseRender)
function FightSoulAttrRender:__init()
    self.real_hide_next = true
end

function FightSoulAttrRender:__delete()
    if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function FightSoulAttrRender:LoadCallBack()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end

	local tween_time = 0.8
    local node = self.node_list["arrow"]
	if node then
        self.arrow_tweener = node.rect:DOAnchorPosY(-7, tween_time)
        self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function FightSoulAttrRender:SetRealHideNext(is_hide)
    self.real_hide_next = is_hide
end

function FightSoulAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(self.data.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
    self.node_list.name.text.text = Language.Common.TipsAttrNameList[self.data.attr_str]
    self.node_list.cur_value.text.text = value_str .. per_desc

    self.node_list.arrow:SetActive(true)
    self.node_list.next_value:SetActive(true)
    self.node_list.arrow.image.enabled = true
    if self.data.add_value > 0 then
        value_str = is_per and self.data.add_value / 100 or self.data.add_value
        self.node_list.next_value.text.text = value_str .. per_desc
    else
        if self.real_hide_next then
            self.node_list.arrow:SetActive(false)
            self.node_list.next_value:SetActive(false)
        else
            self.node_list.arrow.image.enabled = false
            self.node_list.next_value.text.text = ""
        end
    end

    self.view:SetActive(true)
end

--------------------------------------------------------------------------------
FightSoulDropRender = FightSoulDropRender or BaseClass(BaseRender)
function FightSoulDropRender:__init()
end

function FightSoulDropRender:__delete()
    self.star_list = nil
end

function FightSoulDropRender:LoadCallBack()
    self.star_list = {}
    for i = 1, GameEnum.ITEM_MAX_STAR do
        self.star_list[i] = self.node_list["star_" .. i]
    end
end

function FightSoulDropRender:OnFlush()
    if self.data == nil or self.data.item_data == nil then
        self.node_list.level.text.text = ""
        self.node_list.star_part:SetActive(false)
        return
    end

    local data = self.data.item_data
    local b_bundle, b_asset = ResPath.GetF2RawImagesPNG("a2_ymgy_di_" .. data.color)
    self.node_list.color_bg.raw_image:LoadSprite(b_bundle, b_asset)

    local bundle, asset = ResPath.GetF2RawImagesPNG("a2_ymgy_js_k" .. data.fight_soul_type)
    self.node_list.icon.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.icon.raw_image:SetNativeSize()
    end)

    self:SetStar(data.star)
    local level = data.level or 0
    self.node_list.level.text.text = string.format("<color=#F5EE9D>LV.%s</color>", data.level)
end

function FightSoulDropRender:SetStar(num)
    num = num or 0
    self.node_list.star_part:SetActive(true)
    local star_res_list = GetStarImgResByStar(num)
    local bundle, asset = "", ""
    for i = 1, GameEnum.ITEM_MAX_STAR do
        if self.star_list[i] then
            if i <= num then
                self.star_list[i]:SetActive(true)
                bundle, asset = ResPath.GetCommonImages(star_res_list[i])
                self.star_list[i].image:LoadSprite(bundle, asset)
            else
                self.star_list[i]:SetActive(false)
            end
        end
    end
end

---------------------------------------------------------------------------------
SelectFightSoulRender = SelectFightSoulRender or BaseClass(FightSoulDropRender)
function SelectFightSoulRender:OnFlush()
    FightSoulDropRender.OnFlush(self)
    if self.data == nil or self.data.item_data == nil then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
    if item_cfg ~= nil then
        self.node_list.level.text.text = string.format(Language.FightSoul.SlotInfo, item_cfg.name, self.data.item_data.level)
    end
    self.node_list.cap_value.text.text = self.data.capability
end

---------------------------------------------------------------------------------
--------------------- FightSoulBagItem ------------------------------------------
---------------------------------------------------------------------------------
FightSoulBagItem = FightSoulBagItem or BaseClass(FightSoulDropRender)
function FightSoulBagItem:LoadCallBack()
    FightSoulDropRender.LoadCallBack(self)
    XUI.AddClickEventListener(self.node_list.item_node, BindTool.Bind(self.OnClick, self))
end

-- 点击回调
function FightSoulBagItem:OnClick()
    if self.data == nil or self.data.item_data == nil then
        return
    end

	if nil ~= self.click_callback then
		self.click_callback(self)
	end
end

function FightSoulBagItem:OnFlush()
    FightSoulDropRender.OnFlush(self)
    self:FlushCheckState()
    if self.data == nil or self.data.item_data == nil then
        self.node_list.icon.raw_image.enabled = false
        self.node_list.fight_flag.image.enabled = false
        self.node_list.fight_text.text.enabled = false
        self.node_list.item_remind.image.enabled = false
        self.node_list.select_effect.image.enabled = false
        local b_bundle, b_asset = ResPath.GetF2RawImagesPNG("a2_ymgy_di_0")
        self.node_list.color_bg.raw_image:LoadSprite(b_bundle, b_asset)
        return
    end

    local is_wear, slot_index = FightSoulWGData.Instance:GetBagIndexIsWear(self.data.item_data.index)
    local item_id = self.data.item_data.item_id
    local youming_id = ItemWGData.Instance:GetEquipmentCfgByID(item_id)
    self.node_list.fight_flag.image.enabled = is_wear
    self.node_list.fight_text.text.enabled = is_wear
    self.node_list.item_remind.image.enabled = FightSoulWGData.Instance:GetFightSoulComposeRemind(self.data.item_data)
    if youming_id ~= nil then
        self.node_list.level.text.text = string.format(Language.FightSoul.SlotInfo, youming_id.name, self.data.item_data.level)
    end
end

function FightSoulBagItem:OnSelectChange(is_select)
    if is_select == nil or self.data == nil or self.data.item_data == nil then
        return
    end

    self.node_list.select_effect.image.enabled = is_select
end

function FightSoulBagItem:FlushCheckState()
    if not self.node_list.check_effect then
        return
    end

    if self.data == nil or self.data.item_data == nil then
        self.node_list.check_effect:SetActive(false)
    else
        local show_check = FightSoulWGData.CheckCurStuffIsSelect(self.data.select_state)
        self.node_list.check_effect:SetActive(show_check)
    end
end

---------------------------------------------------------------------------------
--------------------- FightSoulComposeStuffItem ---------------------------------
---------------------------------------------------------------------------------
FightSoulComposeStuffItem = FightSoulComposeStuffItem or BaseClass(BaseRender)

function FightSoulComposeStuffItem:LoadCallBack()
    if nil == self.stuff_item then
        self.stuff_item = ItemCell.New(self.node_list.item_pos)
        self.stuff_item:SetUseButton(false)
        self.stuff_item:SetIsShowTips(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))
end

function FightSoulComposeStuffItem:__delete()
    if nil ~= self.stuff_item then
        self.stuff_item:DeleteMe()
        self.stuff_item = nil
    end
end

function FightSoulComposeStuffItem:OnFlush()
    if self.data == nil then
        self.stuff_item:ClearData()
        self.stuff_item:SetItemIcon(ResPath.GetCommonImages("a2_sjdt_suotou"))
        self.node_list.add_part:SetActive(false)
        self.node_list.need_text.text.text = ""
        self.view:SetActive(false)
        return
    end

    self.view:SetActive(true)
    if self.data.item_id > 0 then
        self.stuff_item:SetData({item_id = self.data.item_id, is_bind = 0, num = 0, level = 0})
    else
        self.stuff_item:ClearData()
        local bundle, asset = ResPath.GetCommonImages(string.format("a3_ty_wpk_%d", self.data.color))
        self.stuff_item:SetQualityIcon(bundle, asset)
        self.stuff_item:SetQualityIconVisible(true)
        self.stuff_item:SetLeftTopImg(self.data.star)
        bundle, asset = ResPath.GetItem(400015)
        self.stuff_item:SetItemIcon(bundle, asset)
    end

    local enough_stuff = self.data.had_num >= self.data.need_num
    local text_color = enough_stuff and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.need_text.text.text = string.format(Language.FightSoul.StuffNumDesc,
                                        text_color, self.data.had_num, self.data.need_num)
    self.node_list.add_part:SetActive(not enough_stuff)
end

---------------------------------------------------------------------------------
--------------------- FightSoulStuffItem ------------------------------------------
---------------------------------------------------------------------------------
FightSoulStuffItem = FightSoulStuffItem or BaseClass(FightSoulDropRender)
function FightSoulStuffItem:LoadCallBack()
    FightSoulDropRender.LoadCallBack(self)
    self.node_list.select_effect.image.enabled = false
    self.node_list.fight_flag:SetActive(false)
    self.node_list.item_remind.image.enabled = false
end

function FightSoulStuffItem:__delete()

end

function FightSoulStuffItem:OnFlush()
    FightSoulDropRender.OnFlush(self)

    if self.data == nil then
        return
    end

    local show_check = FightSoulWGData.CheckCurStuffIsSelect(self.data.select_state)
    self.node_list.check_effect:SetActive(show_check)
end

---------------------------------------------------------------------------------
------------------------ FSBoneEquipBaseItem ------------------------------------------
---------------------------------------------------------------------------------
FSBoneEquipBaseItem = FSBoneEquipBaseItem or BaseClass(BaseRender)
function FSBoneEquipBaseItem:LoadCallBack()
    self.is_showtip = false
    self.star_list = {}
    for i = 1, GameEnum.ITEM_MAX_STAR do
        self.star_list[i] = self.node_list["star_" .. i]
    end

    self.view.button:AddClickListener(BindTool.Bind(self.OnClick, self))
end

function FSBoneEquipBaseItem:__delete()
    self.star_list = nil
end

function FSBoneEquipBaseItem:OnFlush()
    if self.data == nil then
        return
    end

    local data = self.data.bone_data or self.data.item_data
    if IsEmptyTable(data) then
        self:SetStar(0)
        self:SetSuit(0)
        local b_bundle, b_asset = ResPath.GetFightSoulImg("a2_hbzb_bg_0")
        self.node_list.color_bg.image:LoadSprite(b_bundle, b_asset)
        self.node_list.icon.image.enabled = false
        self.node_list.level.text.text = ""
    else
        local param_star = data.param and data.param.star_level
        local show_star = data.star or param_star or 0
        self:SetStar(show_star)

        local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(data.item_id)
        self:SetSuit(suit_type)
        
        local color = data.color
        if not color then
            local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
            color = item_cfg and item_cfg.color or 0
        end
        local b_bundle, b_asset = ResPath.GetFightSoulImg("a2_hbzb_bg_" .. color)
        self.node_list.color_bg.image:LoadSprite(b_bundle, b_asset)
    
        local bundle, asset = ResPath.GetNoPackPNG("a2_hbzb_" .. fight_soul_type * 10 + bone_part)
        self.node_list.icon.image:LoadSprite(bundle, asset, function()
            self.node_list.icon.image:SetNativeSize()
        end)
    end
end

function FSBoneEquipBaseItem:SetStar(num)
    num = num or 0
    local star_res_list = GetStarImgResByStar(num)
    local bundle, asset = "", ""
    for i = 1, GameEnum.ITEM_MAX_STAR do
        if self.star_list[i] then
            if i <= num then
                self.star_list[i]:SetActive(true)
                bundle, asset = ResPath.GetCommonImages(star_res_list[i])
                self.star_list[i].image:LoadSprite(bundle, asset)
            else
                self.star_list[i]:SetActive(false)
            end
        end
    end
end

function FSBoneEquipBaseItem:SetSuit(suit_type)
	if suit_type == nil or suit_type <= 0 then
		self.node_list.suit_iocn:SetActive(false)
		return
	end

	local bundle, asset = ResPath.GetFightSoulImg("a2_hzpz_h_" .. suit_type)
    if self.node_list.suit_text then
        self.node_list.suit_text.text.text = Language.FightSoul.SuitStr[suit_type]
    end
	self.node_list.suit_iocn.image:LoadSpriteAsync(bundle, asset)
	self.node_list.suit_iocn:SetActive(true)
end

function FSBoneEquipBaseItem:SetIsShowTips(flag)
	self.is_showtip = flag
end

-- 点击格子
function FSBoneEquipBaseItem:OnClick()
	if self.is_showtip and self.data then
        local data = self.data.bone_data or self.data.item_data
		TipWGCtrl.Instance:OpenItem(data)
	end

	BaseRender.OnClick(self)
end


---------------------------------------------------------------------------------
------------------------ FSBoneEquipItem ------------------------------------------
---------------------------------------------------------------------------------
FSBoneEquipItem = FSBoneEquipItem or BaseClass(FSBoneEquipBaseItem)
function FSBoneEquipItem:SetData(data, fs_type)
    self.data = data
    self.fs_type = fs_type
    self:Flush()
end

function FSBoneEquipItem:OnFlush()
    FSBoneEquipBaseItem.OnFlush(self)
    if self.data == nil then
        return
    end

    local strength_level = self.data.slot_level or 0
    if strength_level > 0 then
        self.node_list.level_bg:SetActive(true)
        self.node_list.star_part.transform.localPosition = Vector3(10,-91,0)
    else
        self.node_list.level_bg:SetActive(false)
        self.node_list.star_part.transform.localPosition = Vector3(0,-91,0)
    end

    self.node_list.level.text.text = strength_level > 0 and strength_level or ""
    self.node_list.img_add.image.enabled = IsEmptyTable(self.data.bone_data)
    self.node_list.remind.image.enabled = FightSoulWGData.Instance:GetPartHaveBetterBone(self.fs_type, self.data.bone_part)
end


---------------------------------------------------------------------------------
------------------------ FSBoneSuitBtnItem ------------------------------------------
---------------------------------------------------------------------------------
FSBoneSuitBtnItem = FSBoneSuitBtnItem or BaseClass(BaseRender)
function FSBoneSuitBtnItem:__init()
    self.old_suit_type = nil
    self.old_color = nil
    self.old_same_num = nil
    self.old_fs_type = nil
    self.is_doing_act_tween = false
end

function FSBoneSuitBtnItem:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.content, BindTool.Bind(self.OnClick, self))
end

function FSBoneSuitBtnItem:__delete()
    self.old_suit_type = nil
    self.old_color = nil
    self.old_same_num = nil
    self.old_fs_type = nil
    self.is_doing_act_tween = nil
	self.fs_type = nil
end

function FSBoneSuitBtnItem:SetData(data, fs_type)
    self.data = data
    self.fs_type = fs_type
    self:Flush()
end

function FSBoneSuitBtnItem:OnFlush()
    if IsEmptyTable(self.data) or self.data.suit_type < 0 or
        self.data.color <= 0 or self.data.same_num <= 0 then
        UITween.CleanAlphaShow("FSBoneSuitBtnItem" .. self.index)
        UITween.FakeToShow(self.view)
        self.old_fs_type = self.fs_type
        self.view:SetActive(false)
        return
    end

    if self.old_fs_type and self.old_suit_type and self.old_color and self.old_same_num then
        if (self.old_suit_type ~= self.data.suit_type or self.old_color ~= self.data.color
        or self.old_same_num ~= self.data.same_num) and self.fs_type == self.old_fs_type then
            self:DoTween()
        end
    end

    self.old_fs_type = self.fs_type
    self.old_suit_type = self.data.suit_type
    self.old_color = self.data.color
    self.old_same_num = self.data.same_num

    local bg_res = FightSoulWGData.Instance:GetBoneSuitFlagBgRes(self.data.suit_type)
    local bundle, asset = ResPath.GetFightSoulImg("suit_color_" .. bg_res)
    self.node_list.suit_icon.image:LoadSprite(bundle, asset)
    local suit_name = FightSoulWGData.Instance:GetBoneSuitName(self.data.suit_type, self.fs_type)
    self.node_list.suit_name.text.text = suit_name
    self.node_list.suit_num.text.text = self.data.same_num
    self.view:SetActive(true)
end

function FSBoneSuitBtnItem:DoTween()
    UITween.CleanAlphaShow("FSBoneSuitBtnItem" .. self.index)

    UITween.FakeHideShow(self.view)
    local wait_index = self.index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_time = 0.8

    ReDelayCall(self, function()
        self:ShowEffect()
        UITween.AlphaShow("FSBoneSuitBtnItem" .. self.index, self.view, 0, 1, tween_time)
    end, tween_time * wait_index, "FSBoneSuitBtnItem" .. wait_index)
end

function FSBoneSuitBtnItem:ShowEffect()
    if self.node_list.effect_node then
        local bundle_name, asset_name = ResPath.GetUIEffect("UI_sixiang_jihuo")
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect_node.transform,
                            nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
    end
end

---------------------------------------------------------------------------------
------------------------ FSSuitSkillDescItem ------------------------------------------
---------------------------------------------------------------------------------
FSSuitSkillDescItem = FSSuitSkillDescItem or BaseClass(BaseRender)
function FSSuitSkillDescItem:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    --local res = self.data.is_act and "sx_gai_jingsezhu" or "sx_gai_huisezhu"
    if self.data.is_act then
        local bundle1, asset1 = ResPath.GetFightSoulImg("a2_hzpz_h_" .. self.index)
        self.node_list.act_icon.image:LoadSprite(bundle1, asset1, function()
            self.node_list.act_icon.image:SetNativeSize()
        end)
    else
        local bundle2, asset2 = ResPath.GetFightSoulImg("a2_ymgy_bq_hh")
        self.node_list.act_icon.image:LoadSprite(bundle2, asset2, function()
            self.node_list.act_icon.image:SetNativeSize()
        end)
    end
    
    local need_desc = ""
    if not self.data.is_act then
        local grade_str = Language.FightSoul.SuitShortStr[self.index]
        need_desc = string.format(Language.FightSoul.AutoSkillActNeedDesc, grade_str)
        need_desc = ToColorStr(need_desc, COLOR3B.GRAY)
    end
    
    self.node_list.suit_act_desc.text.text = need_desc
    self.node_list.suit_skill_desc.text.text = self.data.desc
    self.node_list.act_text.text.text = Language.FightSoul.SuitStr[self.index]
    self.view:SetActive(true)
end

---------------------------------------------------------------------------------
------------------------ FSSuitActItem ------------------------------------------
---------------------------------------------------------------------------------
FSSuitActItem = FSSuitActItem or BaseClass(BaseRender)
function FSSuitActItem:__init()
    for i = 1, 3 do
        XUI.AddClickEventListener(self.node_list["skill" .. i], BindTool.Bind(self.OnClickSkill, self, i))
    end
end

function FSSuitActItem:OnClickSkill(index)
    if IsEmptyTable(self.data) then
        return
    end

    FightSoulWGCtrl.Instance:OpenPassiveSkillTips(self.data.fs_type, self.data.suit_type, self.data.color, self.data.same_num, index, true)
end

function FSSuitActItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

    local color = self.data.color
    local same_num = self.data.same_num
    local suit_type = self.data.suit_type
    local bundle, asset = ResPath.GetFightSoulImg("a2_hzpz_h_" .. suit_type)
    self.node_list.suit_icon.image:LoadSprite(bundle, asset)
    self.node_list.suit_text.text.text = Language.FightSoul.SuitStr[suit_type]

    local title_name_str = string.format(Language.FightSoul.suitTitleDesc, same_num, Language.FightSoul.PzXiaoGuo[color])
    self.node_list.title_name.text.text = ToColorStr(title_name_str, ITEM_COLOR[color])

    local skill_list = self.data.skill_list
    local is_show = false
    for i = 1, 3 do
        local skill = self.node_list["skill" .. i]
        local skill_icon = self.node_list["skill_icon" .. i]
        --local skill_no_act_falg = self.node_list["skill_is_act" .. i]
        local skill_data = skill_list[i]

        if skill_data then
            is_show = skill_data.is_show
            if is_show then
                local s_bundle, s_asset = ResPath.GetSkillIconById(skill_data.skill_icon)
                skill_icon.image:LoadSprite(s_bundle, s_asset, function()
                    skill_icon.image:SetNativeSize()
                end)

                --skill_no_act_falg.image.enabled = not skill_data.is_act
                XUI.SetGraphicGrey(skill_icon, not skill_data.is_act)
            end
        else
            is_show = false
        end

        skill:SetActive(is_show)
    end
end


---------------------------------------------------------------------------------
------------------------ FSBoneSelectItem ------------------------------------------
---------------------------------------------------------------------------------
-- 装备 item，选中时高亮
FSBoneSelectItem = FSBoneSelectItem or BaseClass(FSBoneEquipBaseItem)
function FSBoneSelectItem:LoadCallBack()
    FSBoneEquipBaseItem.LoadCallBack(self)
    self.node_list.check_effect:SetActive(false)
    self.node_list.select_effect.image.enabled = false
    self.node_list.item_remind.image.enabled = false
end

function FSBoneSelectItem:OnSelectChange(is_select)
    if is_select == nil or self.data == nil then
        return
    end

    self.node_list.check_effect:SetActive(is_select)
end


---------------------------------------------------------------------------------
--------------------- FSBoneStrengthAttrRender ----------------------------------
---------------------------------------------------------------------------------
FSBoneStrengthAttrRender = FSBoneStrengthAttrRender or BaseClass(BaseRender)

function FSBoneStrengthAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(self.data.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value

    self.node_list.name.text.text = Language.Common.TipsAttrNameList[self.data.attr_str]
    self.node_list.cur_value.text.text = value_str .. per_desc
    if self.data.attr_next_value > 0 then
        value_str = is_per and self.data.attr_next_value / 100 or self.data.attr_next_value
        self.node_list.next_value.text.text = value_str .. per_desc
        self.node_list.arrow:SetActive(true)
        self.node_list.next_value:SetActive(true)
    else
        self.node_list.arrow:SetActive(false)
        self.node_list.next_value:SetActive(false)
    end

    self.view:SetActive(true)
end

---------------------------------------------------------------------------------
--------------------- FightSoulBoneBagItem ------------------------------------------
---------------------------------------------------------------------------------
FightSoulBoneBagItem = FightSoulBoneBagItem or BaseClass(BaseRender)

function FightSoulBoneBagItem:LoadCallBack()
    if nil == self.fs_item then
        self.fs_item = ItemCell.New(self.node_list.item_pos)
        self.fs_item:SetUseButton(false)
        self.fs_item:SetIsShowTips(false)
    end

    XUI.AddClickEventListener(self.node_list.item_node, BindTool.Bind(self.OnClick, self))
end

function FightSoulBoneBagItem:__delete()
    if nil ~= self.fs_item then
        self.fs_item:DeleteMe()
        self.fs_item = nil
    end
end

-- 点击回调
function FightSoulBoneBagItem:OnClick()
    if self.data == nil or self.data.item_data == nil then
        return
    end

	if nil ~= self.click_callback then
		self.click_callback(self)
	end
end

function FightSoulBoneBagItem:ClickItem()
    self:OnClick()
end

function FightSoulBoneBagItem:OnFlush()
    self:FlushCheckState()
    if self.data == nil or self.data.item_data == nil then
        self.fs_item:ClearData()
        self.node_list.fight_flag:SetActive(false)
        self.node_list.item_remind.image.enabled = false
        self.node_list.select_effect.image.enabled = false
        return
    end

    self.fs_item:SetData(self.data.item_data)
    self.fs_item:SetEffectRootEnable(false)
    local is_wear = FightSoulWGData.Instance:GetBoneBagIndexIsWear(self.data.item_data.bag_index)
    self.node_list.fight_flag:SetActive(is_wear)
    self.node_list.item_remind.image.enabled = FightSoulWGData.Instance:GetSingleBoneComposeRemind(self.data.item_data)
end

function FightSoulBoneBagItem:OnSelectChange(is_select)
    if self.data == nil or self.data.item_data == nil then
        return
    end

    self.node_list.select_effect.image.enabled = is_select
end

function FightSoulBoneBagItem:FlushCheckState()
    if not self.node_list.check_effect then
        return
    end

    if self.data == nil or self.data.item_data == nil then
        self.node_list.check_effect:SetActive(false)
    else
        local show_check = FightSoulWGData.CheckCurStuffIsSelect(self.data.select_state)
        self.node_list.check_effect:SetActive(show_check)
    end
end

---------------------------------------------------------------------------------
--------------------- FightSoulBoneComposeStuffItem ---------------------------------
---------------------------------------------------------------------------------
FightSoulBoneComposeStuffItem = FightSoulBoneComposeStuffItem or BaseClass(BaseRender)

function FightSoulBoneComposeStuffItem:LoadCallBack()
    if nil == self.stuff_item then
        self.stuff_item = ItemCell.New(self.node_list.item_pos)
        self.stuff_item:SetUseButton(false)
        self.stuff_item:SetIsShowTips(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))
end

function FightSoulBoneComposeStuffItem:__delete()
    if nil ~= self.stuff_item then
        self.stuff_item:DeleteMe()
        self.stuff_item = nil
    end
end

function FightSoulBoneComposeStuffItem:OnFlush()
    if self.data == nil then
        self.stuff_item:ClearData()
        self.stuff_item:SetItemIcon(ResPath.GetCommonImages("a2_sjdt_suotou"))
        self.node_list.add_part:SetActive(false)
        self.node_list.need_text.text.text = ""
        self.view:SetActive(false)
        return
    end

    self.view:SetActive(true)
    if self.data.item_id > 0 then
        self.stuff_item:SetData({item_id = self.data.item_id, is_bind = 0, num = 0, star = self.data.star})
    else
        self.stuff_item:ClearData()
        local bundle, asset = ResPath.GetCommonImages(string.format("a3_ty_wpk_%d", self.data.color))
        self.stuff_item:SetQualityIcon(bundle, asset)
        self.stuff_item:SetQualityIconVisible(true)
        self.stuff_item:SetLeftTopImg(self.data.star)
        if self.data.icon < 100 then
            bundle, asset = ResPath.GetItem(400015)
        else
            local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.icon)
            if item_cfg then
                bundle, asset = ResPath.GetItem(item_cfg.icon_id)
            end
        end
        self.stuff_item:SetSiXiangSuit(1, self.data.show_suit_type)
        self.stuff_item:SetItemIcon(bundle, asset)
    end

    local enough_stuff = self.data.had_num >= self.data.need_num
    local text_color = enough_stuff and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.need_text.text.text = string.format(Language.FightSoul.StuffNumDesc,
                                        text_color, self.data.had_num, self.data.need_num)
    self.node_list.add_part:SetActive(not enough_stuff)
end

---------------------------------------------------------------------------------
--------------------- FightSoulBoneStuffItem ------------------------------------------
---------------------------------------------------------------------------------
FightSoulBoneStuffItem = FightSoulBoneStuffItem or BaseClass(FSBoneEquipBaseItem)
function FightSoulBoneStuffItem:LoadCallBack()
    FSBoneEquipBaseItem.LoadCallBack(self)
    self.node_list.select_effect.image.enabled = false
    self.node_list.item_remind.image.enabled = false
end

function FightSoulBoneStuffItem:OnFlush()
    FSBoneEquipBaseItem.OnFlush(self)

    if self.data == nil then
        return
    end

    local show_check = FightSoulWGData.CheckCurStuffIsSelect(self.data.select_state)
    self.node_list.check_effect:SetActive(show_check)
end
