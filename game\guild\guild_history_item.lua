GuildHistoryItem = GuildHistoryItem or BaseClass(BaseRender)

function GuildHistoryItem:__init()
		self.guild_data = GuildWGData.Instance
		self.rich_text = nil
		self.lbl_event_time1 = self.node_list.lbl_history_time1
		self.lbl_event_time2 = self.node_list.lbl_history_time2
		self.lbl_history_role_name = self.node_list.lbl_history_role_name
end

function GuildHistoryItem:__delete()
	self.guild_data = nil
end

function GuildHistoryItem:CreateSelectEffect()
end

function GuildHistoryItem:OnFlush()
	if nil == self.data then
		return
	end

	-- local asset_name = self.index % 2 == 0 and "a2_zudui_lbdi" or "a2_ty_xxd_5"
	-- self.node_list["img_bg"].image:LoadSprite(ResPath.GetCommonImages(asset_name))

	local event_type = GuildDataConst.GUILD_EVENT_TYPE
	local text = ""		-- 空串会导致富文本报错
	local key = "Event_Type_" .. self.data.event_type
	
	if self.data.event_type == event_type.ADD_EXP then
		key = "Event_Type_" .. self.data.event_type .."_" .. self.data.param0
		text = self:FormatLanguage(key, 2, self.data.owner, self.data.param1)
	elseif self.data.event_type == event_type.STORAGE_OPERATE then
		local itemname = ItemWGData.Instance:GetItemName(self.data.param1)
		key = "Event_Type_" .. self.data.event_type .."_" .. self.data.param0
		if self.data.param0 == 3 then
			text = self:FormatLanguage(key, 1 , itemname)
		else
			text = self:FormatLanguage(key, 2, self.data.owner, itemname)
		end
	elseif self.data.event_type == event_type.MEMBER_APPOINT then
		local post_authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[self.data.owner_post]
		if nil ~= post_authority then
			text = self:FormatLanguage(key, 2, self.data.owner , post_authority.post)
		end
	elseif self.data.event_type == event_type.TRANSFER_TUANZHANG then
		text = self:FormatLanguage(key, 2, self.data.owner , self.data.sparam0)
	elseif self.data.event_type == event_type.GUILD_EVENT_TYPE_RENAME then
		text = self:FormatLanguage(key, 2, self.data.owner , self.data.sparam0)
	else
		text = self:FormatLanguage(key, 1, self.data.owner)
	end
	self.lbl_history_role_name.text.text = text
	-- local time_text1 = ""
	-- local time_text2 = ""
	local history_params = os.date("*t", self.data.time)
	-- local now_params = TimeWGCtrl.Instance:GetServerTimeFormat()
	-- if now_params.day > history_params.day or now_params.month > history_params.month then
	-- time_text1 = string.format(Language.Common.XXYXXMXXD,history_params.year, history_params.month, history_params.day)
	-- else
	-- time_text2 = string.format(Language.Common.XXHXXM, history_params.hour, history_params.min)
	-- end
	self.lbl_event_time1.text.text = string.format(Language.Common.XXYXXMXXD,history_params.year, history_params.month, history_params.day)
	self.lbl_event_time2.text.text = string.format(Language.Common.XXHXXM, history_params.hour, history_params.min)
end

-- 格式化语言文字
function GuildHistoryItem:FormatLanguage(key, count, str1, str2, str3)
	local result = ""	
	if nil == key  then
		return result
	end
	if 1 == count then
		if nil ~= str1 and nil ~= Language.Guild[key] then
			result = string.format(Language.Guild[key], str1)
		end
	elseif 2 == count then
		if nil ~= str1 and nil ~= str2 and nil ~= Language.Guild[key] then
			result = string.format(Language.Guild[key], str1, str2)
		end
	end

	return result
end