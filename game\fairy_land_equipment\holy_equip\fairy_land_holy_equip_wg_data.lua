local this = FairyLandEquipmentWGData
local empty_table = {}
function this:InitHolyEquipData()
	local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
	self.holy_equip_item_cfg = main_cfg.gb_equip
	self.holy_equip_item_map_cfg = self:InitAllHolyEquipSplitData()
	self.gb_equip_virtual_cfg = ListToMap(main_cfg.gb_equip_virtual,"slot","part")
	self.gb_equip_virtual_cfg_by_itemid = ListToMap(main_cfg.gb_equip_virtual, "item_id")
end

-- 通过 神体槽位 部位 品质 划分配置
function this:InitAllHolyEquipSplitData()
	if IsEmptyTable(self.holy_equip_item_cfg) then
		return {}
	end

	local cache_list = {}
	for k, v in pairs(self.holy_equip_item_cfg) do
		cache_list[v.slot] = cache_list[v.slot] or {}
		cache_list[v.slot][v.part] = cache_list[v.slot][v.part] or {}
		cache_list[v.slot][v.part][v.color] = v
	end

	return cache_list
end

function this:InitHolyEquipParam()
	self.holy_equip_bag_list = {}
	self.cache_he_color_list = {}

	self.total_level_list = {}
	self.total_star_list = {}
	self.he_level_list = {}
	self.he_grade_list = {}
	self.he_rand_attr_list = {}
	self.he_wear_list = {}
end

function this.DefaultHEItemData()
	return {item_id = 0, index = -1, slot = -1, part = -1, name = "",
			color = 0, star = 0, num = 0, is_bind = 0, is_wear = false}
end

function this:HolyEquipItemData(item_id)
	local data = this.DefaultHEItemData()
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg then
		data.item_id = item_cfg.id
		data.color = item_cfg.color
		data.name = item_cfg.name
	end

	local he_item_cfg = self:GetHolyEquipItemCfg(item_id)
	if he_item_cfg then
		data.slot = he_item_cfg.slot
		data.part = he_item_cfg.part
	end

	return data
end

-- 判断装备是否特殊类型
function this:GetIsSpecialType(part)
	if XIANJIE_EQUIP_TYPE.TEJIE == part or XIANJIE_EQUIP_TYPE.JINNANG == part
	or XIANJIE_EQUIP_TYPE.YUPEI == part or XIANJIE_EQUIP_TYPE.XIANYIN == part then
		return true
	end

	return false
end

-- 判断是否是圣装
function this:IsHolyEquipBySubType(sub_type)
	return sub_type == GameEnum.E_TYPE_XIANJIE_EQUI
end

-- 判断是否是圣装
function this:IsHolyEquipByItemId(item_id)
	return self:GetHolyEquipItemCfg(item_id) ~= nil
end

-- 获取圣装配置（by item_id）
function this:GetHolyEquipItemCfg(item_id)
	return self.holy_equip_item_cfg[item_id]
end

-- 获取圣装配置（by slot, part, color）
function this:GetHolyEquipItemMapCfg(slot, part, color)
	return ((self.holy_equip_item_map_cfg[slot] or empty_table)[part] or empty_table)[color]
end

function this:GetGBEquipVirtualCfg(slot, part)
	return (self.gb_equip_virtual_cfg[slot] or empty_table)[part]
end

function this:GetGBEquipVirtualCfgByItemId(item_id)
	return self.gb_equip_virtual_cfg_by_itemid and self.gb_equip_virtual_cfg_by_itemid[item_id]
end

-- 圣装背包信息
function this:SetHolyEquipBagInfo(protocol)
	self.holy_equip_bag_list = protocol.bag_grid_list
	self:CacheAllHolyEquipColorList()
	if not IsEmptyTable(self.he_wear_list) then
		self:HolyEquipBagKeyUse()
		self:SortEvolveEquipBagList()
	end
end

-- 单个圣装背包信息变化
function this:SetHolyEquipBagChangeInfo(protocol)
	local new_data = protocol.change_info
	local index = new_data.index
	local item_id = new_data.item_id
	local old_data = self:GetHolyEquipBagItem(index)
	local old_num = old_data and old_data.num or 0
	local new_num = new_data.num

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	if (old_data == nil or old_data.item_id == nil or old_data.item_id <= 0) and new_num > old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_ADD
		self:CacheHolyEquipColorData(change_reason, new_data, new_num - old_num)
	elseif old_data and old_data.item_id > 0 and new_num < old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_REMOVE
		self:CacheHolyEquipColorData(change_reason, new_data, old_num - new_num)
	else
		self:CacheHolyEquipColorData(change_reason, new_data, new_num - old_num)
	end

	self.holy_equip_bag_list[index] = new_data
	self:SortEvolveSingleEquipBag(change_reason, new_data, old_data)
	FairyLandEquipmentWGCtrl.Instance:OnHolyEquipItemDataChange(new_data, index, change_reason, old_num, new_num)
end

-- 获取圣装背包列表
function this:GetHolyEquipBagList()
	return self.holy_equip_bag_list
end

function this:GetHolyEquipBagListBySlot(slot)
	local bag_list = {}
	for i = 0, #self.holy_equip_bag_list do
		local data = self.holy_equip_bag_list[i]
		if data and data.slot == slot then
			table.insert(bag_list, data)
		end
	end

	return bag_list
end

function this:GetHolyEquipBagItem(index)
	return self.holy_equip_bag_list[index]
end

-- 缓存圣装品质列表
function this:CacheAllHolyEquipColorList()
	self.cache_he_color_list = {}
	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	local bag_list = self:GetHolyEquipBagList()
	for k,v in pairs(bag_list) do
		self:CacheHolyEquipColorData(change_reason, v, v.num)
	end
end

-- 改变缓存数据
function this:CacheHolyEquipColorData(change_reason, data, change_num)
	if data == nil or change_num == 0 then
		return
	end

	local slot = data.slot
	local part = data.part
	local color = data.color
	self.cache_he_color_list[slot] = self.cache_he_color_list[slot] or {}
	self.cache_he_color_list[slot][part] = self.cache_he_color_list[slot][part] or {}
	local num = self.cache_he_color_list[slot][part][color] or 0
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
		num = num + change_num
	elseif change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
		num = num + change_num
	elseif change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		num = num - change_num
		num = num > 0 and num or 0
	end

	self.cache_he_color_list[slot][part][color] = num
end

function this:GetHolyEquipColorCache(slot, part, color)
	return ((self.cache_he_color_list[slot] or empty_table)[part] or empty_table)[color] or 0
end

-- 所有神体圣装穿戴信息
function this:SetHolyEquipWearInfo(protocol)
	self.total_level_list = protocol.total_level_list
	self.total_star_list = protocol.total_star_list
	self.he_level_list = protocol.he_level_list
	self.he_grade_list = protocol.he_grade_list
	self.he_rand_attr_list = protocol.he_rand_attr_list

	local old_wear_list = self.he_wear_list
	self.he_wear_list = protocol.he_wear_list

	if IsEmptyTable(old_wear_list) then
		self:HolyEquipBagKeyUse()
		self:SortEvolveEquipBagList()
	end
end

-- 单个神体圣装穿戴信息变化
function this:SetHolyEquipWearChangeInfo(protocol)
	local slot = protocol.slot_index
	local part = protocol.part_index
	local grid_info = protocol.grid_info
	local equip_info = (self.he_wear_list[slot] or empty_table)[part]
	if equip_info then
		grid_info.star = self:GetHEEvolveStarNum(slot, part)
		self.he_wear_list[slot][part] = grid_info
		FairyLandEquipmentWGData.Instance:SetEvolveNeedReCaleEquip()
	end

end

function this:GetHolyEquipAttrList()
	local he_rand_attr_list = {}
	local fle_data = FairyLandEquipmentWGData.Instance
	local slot_num = fle_data:GetGodBodyMaxNum()
	local part_num = GOD_BODY_ENUM.MAX_HOLY_EQUIP_PART - 1

	for slot = 0, slot_num do
		he_rand_attr_list[slot] = {}

		for part = 0, part_num do
			he_rand_attr_list[slot][part] = {}
			local grade = self:GetPartGrade(slot, part)
			local cfg = self:GetGBEquipUpgradeListCfg(slot, part)
			for k, v in pairs(cfg) do
				if grade >= v.grade then
					table.insert(he_rand_attr_list[slot][part], v.attr_index)
				end
			end
		end
	end

	return he_rand_attr_list
end

-- 获取圣装穿戴列表
function this:GetHolyEquipWearList(slot)
	return self.he_wear_list[slot]
end

-- 获取圣装穿戴部位信息
function this:GetHolyEquipWearPartInfo(slot, part)
	return (self.he_wear_list[slot] or empty_table)[part]
end

-- 获取圣装部位是否穿戴
function this:GetHolyEquipPartIsWear(slot, part)
	local data = self:GetHolyEquipWearPartInfo(slot, part)
	if data and data.item_id and data.item_id > 0 then
		return true
	end

	return false
end

--圣装成长线变化
function this:SetHolyEquipChengZhangInfoChange(protocol)
	local reason_type = protocol.reason_type
	local slot = protocol.slot_index
	local part = protocol.part_index
	if reason_type == XIANJIE_CHANGE_REASON.UPLEVEL or reason_type == XIANJIE_CHANGE_REASON.ONE_KEY_LEVEL then--装备升级
		if self.he_level_list[slot] then
			self.he_level_list[slot][part] = protocol.level
		end
	elseif reason_type == XIANJIE_CHANGE_REASON.UPGRADE then					--装备进阶
		if self.he_grade_list[slot] then
			self.he_grade_list[slot][part] = protocol.grade
		end

		if self.he_rand_attr_list[slot] then
			self.he_rand_attr_list[slot][part] = self:GetHolyEquipAttrListBySlotAndPart(slot, part)
		end
		local equip_info = (self.he_wear_list[slot] or empty_table)[part]
		if equip_info then
			equip_info.star = self:GetHEEvolveStarNum(slot, part)
			--equip_info.star = protocol.grade
			self.he_wear_list[slot][part]= equip_info
		end
	elseif reason_type == XIANJIE_CHANGE_REASON.TAOTAL_LEVEL then				--装备总等级激活
		self.total_level_list[slot] = protocol.strengthen_total_level
	elseif reason_type == XIANJIE_CHANGE_REASON.TAOTAL_STAR then				--装备总星级激活
		self.total_star_list[slot] = protocol.star_total_level
	end
end

function this:GetHolyEquipAttrListBySlotAndPart(slot, part)
	local he_rand_attr_list = {}
	local grade = self:GetPartGrade(slot, part)
	local cfg = self:GetGBEquipUpgradeListCfg(slot, part)
	for k, v in pairs(cfg) do
		if grade >= v.grade then
			table.insert(he_rand_attr_list, v.attr_index)
		end
	end

	return he_rand_attr_list
end

-- 获取强化总等级
function this:GetTotalStrengthenLevel(slot)
	return self.total_level_list[slot]
end

-- 获取全身装备星级总等级
function this:GetTotalStarLevel(slot)
	return self.total_star_list[slot]
end

-- 获取强化等级
function this:GetPartStrengthenLevel(slot, part)
	return (self.he_level_list[slot] or empty_table)[part] or 0
end

-- 获取强化等级列表
function this:GetPartStrengthenLevelList(slot)
	return self.he_level_list[slot] or empty_table
end

-- 获取进化阶数
function this:GetPartGrade(slot, part)
	return (self.he_grade_list[slot] or empty_table)[part] or 0
end

-- 获取进化随机属性
function this:GetPartRandAttr(slot, part)
	return (self.he_rand_attr_list[slot] or empty_table)[part] or empty_table
end

-- 获取进化随机属性
function this:GetSlotRandAttrList(slot)
	return self.he_rand_attr_list[slot] or empty_table
end

-- 获取进化属性中星级属性数量
function this:GetHEEvolveStarNum(slot, part)
	local rand_attr_list = self:GetPartRandAttr(slot, part)
	return self:GetHEEvolveStarNumByAttr(rand_attr_list)
end

-- 获取进化属性中星级属性数量
function this:GetHEEvolveStarNumByAttr(rand_attr)
	local star_num = 0
	if not IsEmptyTable(rand_attr) then
		for k,v in pairs(rand_attr) do
			local attr_cfg = self:GetGBEquipAttrListCfg(v)
			if attr_cfg and attr_cfg.is_star == 1 then
				star_num = star_num + 1
			end
		end
	end

	return star_num
end

function this:HolyEquipBagKeyUse()
	for k,v in pairs(self.holy_equip_bag_list) do
		FairyLandEquipmentWGCtrl.Instance:AddKeyUse(v)
	end
end

-- 目标装备是否比身上的好
function this:HolyEquipIsBetterWear(data)
	if data == nil then
		return false
	end

	local item_data = self:HolyEquipItemData(data.item_id)
	local slot = item_data.slot
	local part = item_data.part
	local color = item_data.color
	-- 神体未激活
	local gb_data = self:GetGodBodyData(slot)
	if gb_data == nil or (gb_data and not gb_data:GetIsAct()) then
		return false
	end

	-- 未装备
	local wear_data = self:GetHolyEquipWearPartInfo(slot, part)
	if IsEmptyTable(wear_data) or wear_data.item_id <= 0 then
		return true
	end

	-- 品质比较
	if color > wear_data.color then
		return true
	end

	return false
end

-- 单套圣装红点
function this:GetSlotHolyEquipRemind(slot)
	local gb_data = self:GetGodBodyData(slot)
	if gb_data == nil or not gb_data:GetIsAct() then
		return false
	end

	local end_color = 0
	local max_color = GameEnum.ITEM_COLOR_COLOR_FUL
	for part = 0, XIANJIE_EQUIP_TYPE.XIANYIN do
		local wear_data = self:GetHolyEquipWearPartInfo(slot, part)
		if wear_data == nil
		or (wear_data and (wear_data.item_id == nil or wear_data.item_id <= 0)) then
			end_color = 1
		else
			end_color = wear_data.color + 1
		end

		for color = max_color, end_color, -1 do
			if self:GetHolyEquipColorCache(slot, part, color) > 0 then
				return true
			end
		end
	end

	return false
end

-- 圣装红点
function this:GetHolyEquipRemind()
	local gb_list = self:GetGodBodyList()
	for slot = 0, #gb_list do
		if self:GetSlotHolyEquipRemind(slot) then
			self:ToBeStrengthen(1, MAINUI_TIP_TYPE.FLE_HOLYEQUIP_WEAR,
								TabIndex.fairy_land_eq_holy_equip, "all",
								{to_ui_name = slot})
			return 1
		end
	end

	self:ToBeStrengthen(0, MAINUI_TIP_TYPE.FLE_HOLYEQUIP_WEAR)
	return 0
end

function this:GetSinglePartEquipCapability(slot, part)
	local capability = 0
	local wear_data = self:GetHolyEquipWearPartInfo(slot, part)
	if IsEmptyTable(wear_data) or wear_data.item_id <= 0 then
		return capability
	end

	local attr_str
	local item_id = wear_data.item_id
	local base_attribute = AttributePool.AllocAttribute()
	-- 装备基础属性
	local base_attr = self:GetHolyEquipItemCfg(item_id)
	if base_attr ~= nil then
		for attr, value in pairs(base_attr) do
            attr_str = AttributeMgr.GetAttributteKey(attr)
            if base_attribute[attr_str] ~= nil and value > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + value
            end
        end
	end

	-- 装备强化属性
	local strengthen_level = self:GetPartStrengthenLevel(slot, part)
	local strengthen_cfg = self:GetFLEStrengthenCfgBySlotPartLv(slot, part, strengthen_level)
	if strengthen_cfg ~= nil then
		for attr, value in pairs(strengthen_cfg) do
            attr_str = AttributeMgr.GetAttributteKey(attr)
            if base_attribute[attr_str] ~= nil and value > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + value
            end
        end
	end

	-- 强化大师属性
	local total_strengthen_cfg = self:GetFLEStrengthenTotalCfg(slot)
	if total_strengthen_cfg ~= nil then
		for attr, value in pairs(total_strengthen_cfg) do
            attr_str = AttributeMgr.GetAttributteKey(attr)
            if base_attribute[attr_str] ~= nil and value > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + value
            end
        end
	end

	-- 装备星级属性
	local rand_attr = self:GetGBEquipAttrGroupAttrList(slot,part)
	for k,v in pairs(rand_attr) do
		local type_str = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_cfg.attr_type)
		local value = v.attr_cfg.attr_value
		attr_str = AttributeMgr.GetAttributteKey(type_str)
		if base_attribute[attr_str] ~= nil and value > 0 and v.is_act == 1 then
			base_attribute[attr_str] = base_attribute[attr_str] + value
		end
	end

	-- 星级大师属性
	local total_star_cfg = self:GetEvolveLevelTotalCfg(slot)
	if total_star_cfg ~= nil then
		for attr, value in pairs(total_star_cfg) do
			attr_str = AttributeMgr.GetAttributteKey(attr)
			if base_attribute[attr_str] ~= nil and value > 0 then
				base_attribute[attr_str] = base_attribute[attr_str] + value
			end
		end
	end

	capability = AttributeMgr.GetCapability(base_attribute)
	return capability
end

function this:GetSlotEquipCapability(slot)
	local capability = 0
	local gb_data = self:GetGodBodyData(slot)
	if gb_data == nil or not gb_data:GetIsAct() then
		return capability
	end

	for part = 0, XIANJIE_EQUIP_TYPE.XIANYIN do
		capability = capability + self:GetSinglePartEquipCapability(slot, part)
	end

	return capability
end

--显示圣装的品质路径 橙 红 粉 金 幻彩 固定5个
function this:GetHolyEquipTipsShowColorData(slot, part, color)
	local equip_list = {}
	if color < GameEnum.ITEM_COLOR_ORANGE then
		return equip_list
	end

	for i = GameEnum.ITEM_COLOR_ORANGE, GameEnum.ITEM_COLOR_COLOR_FUL do
		local is_cur = color == i
		local cfg = self:GetHolyEquipItemMapCfg(slot, part, color)
		if cfg then
			equip_list[#equip_list + 1] = {color = i, is_cur = is_cur, item_id = cfg.id}
		end
	end

    return equip_list
end

function this:GetHolyEquipGetWayList()
	local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
	local other_cfg = main_cfg.other[1]
	return other_cfg.get_way_list or ""
end

function this:GetSlotHadWearEquip(slot)
	local wear_lsit = self:GetHolyEquipWearList(slot)
	if IsEmptyTable(wear_lsit) then
		return false
	end

	for part,data in pairs(wear_lsit) do
		if data.item_id and data.item_id > 0 then
			return true
		end
	end

	return false
end

function this:GetEquipGridEffectByPartColor(part, color)
	local name_t = nil
	if FairyLandEquipmentWGData.Instance:GetIsSpecialType(part) then--特殊部位 8 9 10 11
		name_t = XJEQ_GRID_SPC_EFFECT

	elseif part == XIANJIE_EQUIP_TYPE.WUQI or part == XIANJIE_EQUIP_TYPE.FUWU
		or part == XIANJIE_EQUIP_TYPE.YAODAI or part == XIANJIE_EQUIP_TYPE.JIANJIA then --0 1 4 5
		name_t = XJEQ_GRID_EFFECT_0

	elseif part == XIANJIE_EQUIP_TYPE.TOUKUI or part == XIANJIE_EQUIP_TYPE.XIEZI then--3 7
		name_t = XJEQ_GRID_EFFECT_1

	elseif part == XIANJIE_EQUIP_TYPE.YIFU or part == XIANJIE_EQUIP_TYPE.KUZI then--2 6
		name_t = XJEQ_GRID_EFFECT_2

	end

	if name_t then
		return ResPath.GetUIEffect(name_t[color])
	end
end
