require("game/cross_marry_rank/cross_marry_rank_view")
require("game/cross_marry_rank/cross_marry_rank_wg_data")
require("game/cross_marry_rank/cross_marry_show_rank_view")

CrossMarryRankWGCtrl = CrossMarryRankWGCtrl or BaseClass(BaseWGCtrl)
function CrossMarryRankWGCtrl:__init()
    if CrossMarryRankWGCtrl.Instance then
		ErrorLog("[CrossMarryRankWGCtrl]:Attempt to create singleton twice!")
	end

    -- 单例
    CrossMarryRankWGCtrl.Instance = self

    -- 创建data
    self.data = CrossMarryRankWGData.New()
    self.view = CrossMarryRankView.New(GuideModuleName.CrossMarryRankView)
    self.show_rank_view = CrossMarryShowRankView.New()

    -- 注册协议
    self:RegisterAllProtocols()
end

function CrossMarryRankWGCtrl:__delete()
    CrossMarryRankWGCtrl.Instance = nil

    if self.data ~= nil then
        self.data:DeleteMe()
        self.data = nil
    end
    
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.show_rank_view then
        self.show_rank_view:DeleteMe()
        self.show_rank_view = nil
    end
end

function CrossMarryRankWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCCrossCoupleChongZhiRankInfo, "OnSCCrossCoupleChongZhiRankInfo")
end

function CrossMarryRankWGCtrl:OnSCCrossCoupleChongZhiRankInfo(protocol)
    -- print_error("OnSCCrossCoupleChongZhiRankInfo", protocol)
    self.data:SetRankData(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end
    
    if self.show_rank_view:IsOpen() then
        self.show_rank_view:Flush()
    end
end

--打开排名面板
function CrossMarryRankWGCtrl:OpenShowRankView()
    self.show_rank_view:Open()
end

--发送获取排名
function CrossMarryRankWGCtrl:SendMarryRechargedRank(opera_type, param_1,param_2, param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_MARRY_RANK
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0
	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end