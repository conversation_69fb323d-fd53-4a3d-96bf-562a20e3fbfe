GuildInviteWGData = GuildInviteWGData or BaseClass()

function GuildInviteWGData:__init()
	if GuildInviteWGData.Instance then
		ErrorLog("[GuildInviteWGData]:Attempt to create singleton twice!")
	end
	GuildInviteWGData.Instance = self

	self.guild_info_list = {}

	local cfg = ConfigManager.Instance:GetAutoConfig("guild_dingji_auto")
	self.other_cfg = cfg.other[1]
	self.join_reward_cfg = ListToMap(cfg.join_reward,"index")
end

function GuildInviteWGData:__delete()
	GuildInviteWGData.Instance = nil
end

function GuildInviteWGData:GetDefaultReward()
	local cfg = self.join_reward_cfg[1]
	local data = {}
	if cfg then
		for i=0,#cfg.reward_item do
			table.insert(data,cfg.reward_item[i])
		end
	end
	return data
end

function GuildInviteWGData:GetTempleReward()
	local cfg = self.other_cfg.temple_reward
	local data = {}
	if cfg then
		for i=0,#cfg do
			table.insert(data,cfg[i])
		end
	end
	return data
end

function GuildInviteWGData:GetJoinReward(index)
	local cfg = self.join_reward_cfg[index]
	local data = {}
	if cfg then
		for i=0,#cfg.reward_item do
			table.insert(data,cfg.reward_item[i])
		end
	end
	return data
end

function GuildInviteWGData:GetOtherCfg()
	return self.other_cfg
end


function GuildInviteWGData:SetSCGuildDingJiHurtRankInfo(protocol)
	self.boss_fresh_timestamp = protocol.boss_fresh_timestamp
	self.guild_info_list = protocol.guild_info_list
	self.self_guild_hurt = protocol.self_guild_hurt
end

function GuildInviteWGData:GetBossFreshTimestamp()
	return self.boss_fresh_timestamp
end

function GuildInviteWGData:GetGuildInfoList()
	return self.guild_info_list
end

function GuildInviteWGData:GetSelfGuildHurt()
	return self.self_guild_hurt or 0
end

function GuildInviteWGData:GetGuildMaxHurt()
	local max_info = self.guild_info_list[1]
	local max_hurt = max_info and max_info.hurt_value or 1
	return max_hurt
end

function GuildInviteWGData:GetInviteResultList()
	local data_list = {}
	for i=1,5 do
		local data = {}
		for k=1,4 do
			local index = (i - 1) * 4 + k
			if self.guild_info_list[index] then
				data["name"..k] = self.guild_info_list[index].guild_name
			end
		end
		table.insert(data_list,data)
	end
	return data_list
end

--获取该活动的开启的开服天数
function GuildInviteWGData:GetInviteOpenServerDay()
	return self.other_cfg.foreshow_day
end

function GuildInviteWGData:GetGuWuAddPer(count)
	local add_per = self.other_cfg.guwu_add_per / 100 * count
	return add_per
end

function GuildInviteWGData:GetActivityLimitLevel()
	return self.other_cfg.level_limit ,self.other_cfg.foreshow_day
end

function GuildInviteWGData:GetGuildInviteOpenTime()
	local round_1 = self.other_cfg.open_time
	local time_tab = Split(self.other_cfg.open_time,"|")
	return tonumber(time_tab[1]),tonumber(time_tab[2])
end