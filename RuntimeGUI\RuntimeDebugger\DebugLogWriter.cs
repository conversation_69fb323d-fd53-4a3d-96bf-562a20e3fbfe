﻿using System;
using System.Diagnostics;
using System.IO;
using UnityEngine;

public class DebugLogWriter
{	
	private string m_FilePath;
	private const string LOG_FILE_NAME = "debug_log.txt"; 
	private bool m_IsAppend = false;
	
	public DebugLogWriter()
	{
		m_FilePath = Path.Combine(Application.persistentDataPath, LOG_FILE_NAME);
	}
	
	public void Write(string str)
	{
		using (StreamWriter writer = new StreamWriter(m_FilePath, m_IsAppend))
		{
			writer.Write(str);
		}
		if (!m_IsAppend)
		{
			m_IsAppend = true;
		}
	}

	public void OpenFile()
	{
		if (File.Exists(m_FilePath))
		{
			Process.Start(m_FilePath);
		}
	}
	
	public void OpenFileFolder()
	{
		Process.Start(Application.persistentDataPath);
	}
	
	

	public void CopyFile()
	{
		if (File.Exists(m_FilePath))
		{
			string folderPath = Application.persistentDataPath + "/ErrorLog";
			if (!Directory.Exists(folderPath))
			{
				Directory.CreateDirectory(folderPath);
			}
			string targetPath = Path.Combine(folderPath, GetCopyFileName());
			File.Copy(m_FilePath, targetPath);
		}
	}

	private string GetCopyFileName()
	{
		return "ErrorLog_" + DateTime.Now.ToString("yyyyMMdd_hhmmss") + ".txt";
	}

#if UNITY_EDITOR
	[UnityEditor.MenuItem("自定义工具/打开日志文件夹 %e", false, 0)]
    static void OpenLogFolder()
    {
	    Process.Start(Application.persistentDataPath + "/ErrorLog");
    }
#endif
}
