﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TMPro_TMP_SettingsWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(TMPro.TMP_Settings), typeof(UnityEngine.ScriptableObject));
		<PERSON><PERSON>Function("LoadDefaultSettings", LoadDefaultSettings);
		<PERSON><PERSON>unction("GetSettings", GetSettings);
		<PERSON><PERSON>unction("GetFontAsset", GetFontAsset);
		L.RegFunction("GetSpriteAsset", GetSpriteAsset);
		<PERSON>.RegFunction("GetStyleSheet", GetStyleSheet);
		<PERSON><PERSON>Function("LoadLinebreakingRules", LoadLinebreakingRules);
		<PERSON><PERSON>Function("New", _CreateTMPro_TMP_Settings);
		<PERSON>.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("version", get_version, null);
		<PERSON><PERSON>("enableWordWrapping", get_enableWordWrapping, null);
		<PERSON><PERSON>("enableKerning", get_enableKerning, null);
		L.RegVar("enableExtraPadding", get_enableExtraPadding, null);
		L.RegVar("enableTintAllSprites", get_enableTintAllSprites, null);
		L.RegVar("enableParseEscapeCharacters", get_enableParseEscapeCharacters, null);
		L.RegVar("enableRaycastTarget", get_enableRaycastTarget, null);
		L.RegVar("getFontFeaturesAtRuntime", get_getFontFeaturesAtRuntime, null);
		L.RegVar("missingGlyphCharacter", get_missingGlyphCharacter, set_missingGlyphCharacter);
		L.RegVar("warningsDisabled", get_warningsDisabled, null);
		L.RegVar("defaultFontAsset", get_defaultFontAsset, null);
		L.RegVar("defaultFontAssetPath", get_defaultFontAssetPath, null);
		L.RegVar("defaultFontSize", get_defaultFontSize, null);
		L.RegVar("defaultTextAutoSizingMinRatio", get_defaultTextAutoSizingMinRatio, null);
		L.RegVar("defaultTextAutoSizingMaxRatio", get_defaultTextAutoSizingMaxRatio, null);
		L.RegVar("defaultTextMeshProTextContainerSize", get_defaultTextMeshProTextContainerSize, null);
		L.RegVar("defaultTextMeshProUITextContainerSize", get_defaultTextMeshProUITextContainerSize, null);
		L.RegVar("autoSizeTextContainer", get_autoSizeTextContainer, null);
		L.RegVar("isTextObjectScaleStatic", get_isTextObjectScaleStatic, set_isTextObjectScaleStatic);
		L.RegVar("fallbackFontAssets", get_fallbackFontAssets, null);
		L.RegVar("matchMaterialPreset", get_matchMaterialPreset, null);
		L.RegVar("defaultSpriteAsset", get_defaultSpriteAsset, set_defaultSpriteAsset);
		L.RegVar("defaultSpriteAssetPath", get_defaultSpriteAssetPath, null);
		L.RegVar("enableEmojiSupport", get_enableEmojiSupport, set_enableEmojiSupport);
		L.RegVar("missingCharacterSpriteUnicode", get_missingCharacterSpriteUnicode, set_missingCharacterSpriteUnicode);
		L.RegVar("defaultColorGradientPresetsPath", get_defaultColorGradientPresetsPath, null);
		L.RegVar("defaultStyleSheet", get_defaultStyleSheet, null);
		L.RegVar("styleSheetsResourcePath", get_styleSheetsResourcePath, null);
		L.RegVar("leadingCharacters", get_leadingCharacters, null);
		L.RegVar("followingCharacters", get_followingCharacters, null);
		L.RegVar("linebreakingRules", get_linebreakingRules, null);
		L.RegVar("useModernHangulLineBreakingRules", get_useModernHangulLineBreakingRules, set_useModernHangulLineBreakingRules);
		L.RegVar("instance", get_instance, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateTMPro_TMP_Settings(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				TMPro.TMP_Settings obj = new TMPro.TMP_Settings();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: TMPro.TMP_Settings.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LoadDefaultSettings(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			TMPro.TMP_Settings o = TMPro.TMP_Settings.LoadDefaultSettings();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSettings(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			TMPro.TMP_Settings o = TMPro.TMP_Settings.GetSettings();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFontAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			TMPro.TMP_FontAsset o = TMPro.TMP_Settings.GetFontAsset();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSpriteAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			TMPro.TMP_SpriteAsset o = TMPro.TMP_Settings.GetSpriteAsset();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStyleSheet(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			TMPro.TMP_StyleSheet o = TMPro.TMP_Settings.GetStyleSheet();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LoadLinebreakingRules(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			TMPro.TMP_Settings.LoadLinebreakingRules();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_version(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, TMPro.TMP_Settings.version);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableWordWrapping(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.enableWordWrapping);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableKerning(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.enableKerning);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableExtraPadding(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.enableExtraPadding);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableTintAllSprites(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.enableTintAllSprites);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableParseEscapeCharacters(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.enableParseEscapeCharacters);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableRaycastTarget(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.enableRaycastTarget);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_getFontFeaturesAtRuntime(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.getFontFeaturesAtRuntime);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_missingGlyphCharacter(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, TMPro.TMP_Settings.missingGlyphCharacter);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_warningsDisabled(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.warningsDisabled);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultFontAsset(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.defaultFontAsset);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultFontAssetPath(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, TMPro.TMP_Settings.defaultFontAssetPath);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultFontSize(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, TMPro.TMP_Settings.defaultFontSize);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTextAutoSizingMinRatio(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, TMPro.TMP_Settings.defaultTextAutoSizingMinRatio);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTextAutoSizingMaxRatio(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, TMPro.TMP_Settings.defaultTextAutoSizingMaxRatio);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTextMeshProTextContainerSize(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.defaultTextMeshProTextContainerSize);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTextMeshProUITextContainerSize(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.defaultTextMeshProUITextContainerSize);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoSizeTextContainer(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.autoSizeTextContainer);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isTextObjectScaleStatic(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.isTextObjectScaleStatic);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fallbackFontAssets(IntPtr L)
	{
		try
		{
			ToLua.PushSealed(L, TMPro.TMP_Settings.fallbackFontAssets);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_matchMaterialPreset(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.matchMaterialPreset);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSpriteAsset(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.defaultSpriteAsset);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSpriteAssetPath(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, TMPro.TMP_Settings.defaultSpriteAssetPath);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableEmojiSupport(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.enableEmojiSupport);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_missingCharacterSpriteUnicode(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, TMPro.TMP_Settings.missingCharacterSpriteUnicode);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultColorGradientPresetsPath(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, TMPro.TMP_Settings.defaultColorGradientPresetsPath);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultStyleSheet(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.defaultStyleSheet);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_styleSheetsResourcePath(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, TMPro.TMP_Settings.styleSheetsResourcePath);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_leadingCharacters(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.leadingCharacters);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_followingCharacters(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.followingCharacters);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_linebreakingRules(IntPtr L)
	{
		try
		{
			ToLua.PushObject(L, TMPro.TMP_Settings.linebreakingRules);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useModernHangulLineBreakingRules(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, TMPro.TMP_Settings.useModernHangulLineBreakingRules);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_instance(IntPtr L)
	{
		try
		{
			ToLua.Push(L, TMPro.TMP_Settings.instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_missingGlyphCharacter(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			TMPro.TMP_Settings.missingGlyphCharacter = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isTextObjectScaleStatic(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			TMPro.TMP_Settings.isTextObjectScaleStatic = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultSpriteAsset(IntPtr L)
	{
		try
		{
			TMPro.TMP_SpriteAsset arg0 = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 2);
			TMPro.TMP_Settings.defaultSpriteAsset = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableEmojiSupport(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			TMPro.TMP_Settings.enableEmojiSupport = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_missingCharacterSpriteUnicode(IntPtr L)
	{
		try
		{
			uint arg0 = (uint)LuaDLL.luaL_checknumber(L, 2);
			TMPro.TMP_Settings.missingCharacterSpriteUnicode = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useModernHangulLineBreakingRules(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			TMPro.TMP_Settings.useModernHangulLineBreakingRules = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

