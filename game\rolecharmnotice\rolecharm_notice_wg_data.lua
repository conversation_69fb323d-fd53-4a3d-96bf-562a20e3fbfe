RoleCharmNoticeWGData = RoleCharmNoticeWGData or BaseClass()
function RoleCharmNoticeWGData:__init()
    if RoleCharmNoticeWGData.Instance then
        Error<PERSON><PERSON>("[KuafuPVPWGData] attempt to create singleton twice!")
        return
    end
    RoleCharmNoticeWGData.Instance =self

    local cfg = ConfigManager.Instance:GetAutoConfig("send_flower_toplist_config_auto")
    self.charm_rank_raward_cfg = ListToMapList(cfg.send_flower_toplist_reward, "reward_id")
	self.base_cfg = cfg.send_flower_toplist_base
    self.man_charm_rank_list = {}
    self.woman_charm_rank_list = {}

	self.male_reward_item_list = {}
	self.female_reward_item_list = {}
	RemindManager.Instance:Register(RemindName.RoleCharmRank, BindTool.Bind(self.IsShowRoleCharmRedPoint, self))
end

function RoleCharmNoticeWGData:__delete()
    RoleCharmNoticeWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.RoleCharmRank)
end

--设置魅力男榜信息
function RoleCharmNoticeWGData:SetCharmRankManInfo(rank_list)
	self.man_charm_rank_list = rank_list
end

--设置魅力女榜信息
function RoleCharmNoticeWGData:SetCharmRankFeManInfo(rank_list)
	self.woman_charm_rank_list = rank_list
end

--设置活动信息
function RoleCharmNoticeWGData:SetCharmRankActivityInfo(protocol)
	self.profess_score = protocol.profess_score						-- 魅力值
	self.daily_reward_flag = protocol.daily_reward_flag				-- 每日奖励标记 0: 未领取 1: 已领取
end

--每日奖励标记
function RoleCharmNoticeWGData:GetDailyGiftRewardFlag()
	return self.daily_reward_flag or 1
end

function RoleCharmNoticeWGData:GetRankList(rank_type)
	if rank_type == PROFESS_RANK_TYPE.SEND_FLOWER_MALE then
		return self.man_charm_rank_list
	else
		return self.woman_charm_rank_list
	end
end

function RoleCharmNoticeWGData:GetRewardID()
	local start_day = ActivityWGData.Instance:GetActivityOpenInServerDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK)
	local reward_id = 0
	for k, v in pairs(self.base_cfg) do
		if start_day >= v.min_open_day and start_day <= v.max_open_day then
			reward_id = v.reward_id
			break
		end
	end
	return reward_id
end

--获取奖励配置
function RoleCharmNoticeWGData:GetCharmRankRewardCfg(rank_type)
	local reward_list = {}
	if rank_type == nil then
		return reward_list
	end

	local activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK
	if not ActivityWGData.Instance:GetActivityIsOpen(activity_type) then
		self.male_reward_item_list = {}
		self.female_reward_item_list = {}
		return reward_list
	end

	reward_list = rank_type == PROFESS_RANK_TYPE.SEND_FLOWER_MALE and self.male_reward_item_list or self.female_reward_item_list
	if IsEmptyTable(reward_list) then
		local start_day = ActivityWGData.Instance:GetActivityOpenInServerDay(activity_type)
		local reward_id = 0
		for k, v in pairs(self.base_cfg) do
			if start_day >= v.min_open_day and start_day <= v.max_open_day then
				reward_id = v.reward_id
				break
			end
		end

		local cfg = self.charm_rank_raward_cfg[reward_id]
		if cfg then
			local reward_key = rank_type == PROFESS_RANK_TYPE.SEND_FLOWER_MALE and "man_reward" or "woman_reward"
			local min_rank = cfg[1].min_rank or 1
			local max_rank = cfg[#cfg].max_rank or 0
			for i = min_rank, max_rank do
				for k,v in ipairs(cfg) do
					if v.min_rank <= i and i <= v.max_rank then
						table.insert(reward_list, v[reward_key])
					end
				end
			end
		end
	end

	return reward_list
end

--跨天
function RoleCharmNoticeWGData:OnDayChange()
	self.male_reward_item_list = {}
	self.female_reward_item_list = {}
end

--获取上榜条件
function RoleCharmNoticeWGData:GetCharmRankTarget(rank)
	local reward_id = self:GetRewardID()
	local cfg = self.charm_rank_raward_cfg[reward_id] or {}
	for i, v in ipairs(cfg) do
		if v.min_rank <= rank and rank <= v.max_rank then
			return v.reach_value
		end
	end
	return 0
end

--获取排行榜信息
function RoleCharmNoticeWGData:GetHadDataRankList(rank_type)
    local rank_list = {}
	if rank_type == nil then
		return rank_list
	end

	rank_list = self:GetRankList(rank_type)
	local sex = rank_type == PROFESS_RANK_TYPE.SEND_FLOWER_MALE and 1 or 0
	local reward_list = self:GetCharmRankRewardCfg(rank_type)
	for i = #rank_list + 1, #reward_list do
		local data = {
			sex = sex,
		}

		table.insert(rank_list, data)
	end

	return rank_list
end

--获取自己性别对应排行榜信息
function RoleCharmNoticeWGData:GetMyCharmRankList()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local rank_type = role_sex == GameEnum.MALE and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local rank_list = self:GetRankList(rank_type)
	return rank_list
end

--获取自己的排名
function RoleCharmNoticeWGData:GetSelfRankNum()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local rank_type = role_sex == GameEnum.MALE and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local rank_list = self:GetRankList(rank_type)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	for i,v in ipairs(rank_list) do
		if v.user_id == role_id then
			return i 
		end
	end

	return 0
end

--获取上升排行的差值
function RoleCharmNoticeWGData:GetUpRankCharmCondition()
	local my_rank_data = self:GetSelfRankCharmData()
	if my_rank_data.self_rank == 1 then -- 已是第一
		return 0
	end

	local rank_list = self:GetMyCharmRankList()
	local reward_id = self:GetRewardID()
	local cfg = self.charm_rank_raward_cfg[reward_id] or {}

	local target_rank = my_rank_data.self_rank == 0 and cfg[#cfg].max_rank or my_rank_data.self_rank - 1
	local target_charm = (rank_list[target_rank] or {}).rank_value
	if not target_charm or target_charm == 0 then
		for i, v in ipairs(cfg) do
			if target_rank >= v.min_rank and target_rank <= v.max_rank then
				target_charm = v.reach_value
				break
			end
		end
		target_charm = target_charm or cfg[#cfg].reach_value --兼容
	else
		target_charm = target_charm + 1 -- 高1才能顶下去
	end
	return target_charm - my_rank_data.self_value
end

--[[
--获取自己的魅力值[废弃]
function RoleCharmNoticeWGData:GetSelfRankCharmNum()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local rank_type = role_sex == GameEnum.MALE and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local rank_list = self:GetRankList(rank_type)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	for i,v in ipairs(rank_list) do
		if v.user_id == role_id then
			return v.rank_value 
		end
	end

	return 0
end
]]

--获取自己的魅力值
function RoleCharmNoticeWGData:GetSelfRankCharmData()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local rank_type = role_sex == GameEnum.MALE and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local self_data = RankWGData.Instance:GetSelfValueData(RankKind.Person, rank_type)
	return self_data
end

--主界面显示气泡
function RoleCharmNoticeWGData:GetMainUiIsShowCharmRank()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowCharmRank")
	if not PlayerPrefsUtil.HasKey(key) then -- 默认显示
		--开服冲榜的气泡，玩家等级200级后默认勾选，如果玩家手动点掉了那就不显示，200级前默认不勾选
		local level = RoleWGData.Instance:GetRoleLevel()
		if level >= 200 then
			PlayerPrefsUtil.SetInt(key, 1)
			return true
		else
			PlayerPrefsUtil.SetInt(key, 0)
			return false
		end
		
	end
	local flag = PlayerPrefsUtil.GetInt(key)
	return flag == 1
end

--设置主界面气泡显示
function RoleCharmNoticeWGData:SetMainUiIsShowCharmRank(flag)
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowCharmRank")
	PlayerPrefsUtil.SetInt(key, flag)
	RoleCharmNoticWGCtrl.Instance:RuningRequesFlushCharmRankInfo()
end

function RoleCharmNoticeWGData:IsShowRoleCharmRedPoint()
	if self:IsShowNeedCharmRedPoint() == 1 then
		return 1
	end

	-- 每日礼包
	if self:GetDailyGiftRewardFlag() == 0 then
		return 1
	end

	return 0
end

function RoleCharmNoticeWGData:IsShowNeedCharmRedPoint()
	local slower_data = MarryWGData.Instance:GetSendFlowerCfg()
	if slower_data then
		for k,v in pairs(slower_data) do
			local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
			if item_num > 0 then
				return 1
			end
		end
	end
	return 0
end