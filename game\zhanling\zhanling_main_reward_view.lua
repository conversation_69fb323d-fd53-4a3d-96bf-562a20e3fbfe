function ZhanLingView:InitMianRewardPanel()
    self.old_scorll_index = -1
    self.old_gd_show_level = 0
    self.old_model_show_itemid = 0
    self.level_show_list, self.max_show_level = ZhanLingWGData.Instance:GetRewardShowLevelCfg()
    XUI.AddClickEventListener(self.node_list["btn_m_buy_exp"], BindTool.Bind(self.OnClickBuyExpBtn, self))
    --XUI.AddClickEventListener(self.node_list["btn_normal_zhanli"], BindTool.Bind(self.OnClickNormalZhanling, self))
    --XUI.AddClickEventListener(self.node_list["btn_high_zhanli"], BindTool.Bind(self.OnClickHighZhanling, self))
    XUI.AddClickEventListener(self.node_list["btn_m_buy_zhanli"], BindTool.Bind(self.OnClickBuyHigh<PERSON><PERSON><PERSON>, self))
    XUI.AddClickEventListener(self.node_list["btn_m_get_all_reward"], BindTool.Bind(self.OnClickGetAllReward, self))

    self.m_reward_list = AsyncListView.New(ZhanLingRewardRender, self.node_list["m_reward_list"])
    self.node_list["m_reward_list"].scroller.scrollerEndScrolled = BindTool.Bind(self.OnScrollValueChanged, self)

    self.gd_show_reward_item = ZhanLingRewardRender.New(self.node_list["gd_reward_cell"])
    self.gd_show_reward_item:SetIsGuDingReward(true)

    if not self.m_model_display then
        local item_bundle = "uis/view/zhanling_ui_prefab"
        local item_asset = "zhanling_display_item"
        self.m_model_display = OperationActRender.New(self.node_list.model_display, item_bundle, item_asset)
        local offset_setting = MODEL_OFFSET_TYPE.NORMALIZE
        self.m_model_display:SetModelType(MODEL_CAMERA_TYPE.TIPS, offset_setting)
    end

    -- local get_today_remind = ZhanLingWGData.Instance:GetZhanLingTodayRemind("reward")
    -- if get_today_remind then
    --     ZhanLingWGData.Instance:SetZhanLingTodayRemind("reward")
    --     RemindManager.Instance:Fire(RemindName.ZhanLing_Reward)
    -- end
end

function ZhanLingView:ReleaseMianRewardPanel()
    if self.m_reward_list then
        self.m_reward_list:DeleteMe()
        self.m_reward_list = nil
    end

    if self.gd_show_reward_item then
        self.gd_show_reward_item:DeleteMe()
        self.gd_show_reward_item = nil
    end

    if self.m_model_display then
        self.m_model_display:DeleteMe()
        self.m_model_display = nil
    end

    if self.zhanling_up_level_tween then
        self.zhanling_up_level_tween:Kill()
        self.zhanling_up_level_tween = nil
    end

    self.level_show_list = nil
    self.max_show_level = nil
    self.old_scorll_index = nil
    self.old_gd_show_level = nil
    self.old_model_show_itemid = nil
    self.old_cycle_title_img = nil
end

function ZhanLingView:SetJumpRewardflag(bool)
    self.jump_reward_flag = bool
end

function ZhanLingView:FlushMianRewardPanel()
    local zhanling_info = ZhanLingWGData.Instance:GetZhanLingInfo()
    if IsEmptyTable(zhanling_info) then
        return
    end

    local need_act_value = ZhanLingWGData.Instance:GetUpNextLevelNeedActValue()

    self.node_list.tips_label.text.text = Language.ZhanLing.ActValueDesc
    -- 升级特效
    if ZhanLingWGData.Instance:GetZhanLingOldLevel() ~= zhanling_info.level then
        -- local node_root = {
        --     num_root = self.node_list.m_level_num,
        --     effect_kuoda = self.node_list.level_effect2,
        --     effect_saoguang = self.node_list.level_effect3,
        --     effect_boom = self.node_list.level_effect4,
        -- }
        --self:PlayUpLevelTween(node_root)
        ZhanLingWGData.Instance:SetZhanLingOldLevel()
    else
        local new_str = zhanling_info.level >= 100 and string.format(Language.ZhanLing.LevelStr2, zhanling_info.level) or zhanling_info.level -- 优化显示
        self.node_list.m_level_num.text.text = string.format(Language.ZhanLing.LevelStr2, new_str)
    end

    self.node_list.m_pregress_label.text.text = string.format(Language.ZhanLing.ActValueDesc2, zhanling_info.act_value, need_act_value)
    self.node_list.m_pregress_slider.slider.value = zhanling_info.act_value / need_act_value


    -- 战令
    self:SetHighLockState(not zhanling_info.act_high)

    -- 奖励列表
    self:FlushRewardList()
    -- 固定显示奖励
    self:FlushGuDingShowReward(self.old_gd_show_level)

    -- 按钮
    local remind_get = ZhanLingWGData.Instance:GetHaveRewardCanGet()
    self.node_list.btn_m_buy_zhanli:SetActive(not zhanling_info.act_high)
    --self.node_list.m_btn_effect:SetActive(remind_get) --按钮红点提示特效
    self.node_list.get_all_red_point:SetActive(remind_get)
    self.node_list.btn_m_get_all_reward:SetActive(remind_get)
    --XUI.SetButtonEnabled(self.node_list.btn_m_get_all_reward, remind_get)

    -- 模型名称图片
    local act_view_show_cfg = ZhanLingWGData.Instance:GetActViewShowModelCfg()
    if act_view_show_cfg and act_view_show_cfg.cycle_title_img then
        if self.old_cycle_title_img ~= act_view_show_cfg.cycle_title_img then
            local bundle, asset = ResPath.GetRawImagesPNG(act_view_show_cfg.cycle_title_img)
            self.node_list.m_model_desc.raw_image:LoadSprite(bundle, asset) --.. model_cfg_id
            self.old_cycle_title_img = act_view_show_cfg.cycle_title_img
        end
    end

    if self.m_model_display then
		self.m_model_display:ActModelPlayLastAction()
	end
end

function ZhanLingView:SetHighLockState(bool)
    local do_tween = self.old_high_state ~= nil and self.old_high_state ~= bool
	if do_tween then
        ReDelayCall(self, function()
            self.node_list.m_high_lock:SetActive(false)
        end, 0.9, "zhan_ling_lock")
    else
        self.node_list.m_high_lock:SetActive(bool)
    end
    self.old_high_state = bool
end

-- function ZhanLingView:PlayUpLevelTween(node_root)
--     if self.zhanling_up_level_tween then
--         return
--     end

--     local old_level = ZhanLingWGData.Instance:GetZhanLingOldLevel()
--     local new_level = ZhanLingWGData.Instance:GetZhanLingInfo().level
--     local num_canvas_group = node_root.num_root.canvas_group

--     local tween_sequence = DG.Tweening.DOTween.Sequence()
--     local tween_alpha_zero = num_canvas_group:DoAlpha(1, 0, 0.2)
--     local tween_alpha_one = num_canvas_group:DoAlpha(0, 1, 0.2)

--     tween_sequence:AppendCallback(function ()
--         local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhanling_02")
--         EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, node_root.effect_saoguang.transform, 1)
--     end)

--     tween_sequence:InsertCallback(0.5, function ()
--         local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhanling_01")
--         EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, node_root.effect_kuoda.transform, 1)
--     end)

--     tween_sequence:AppendInterval(0.5)

--     tween_sequence:Append(tween_alpha_zero)

--     tween_sequence:AppendCallback(function ()
--         node_root.effect_boom:SetActive(true)
--         local new_str = new_level >= 100 and string.format(Language.ZhanLing.LevelStr2, new_level) or new_level -- 优化显示
--         node_root.num_root.text.text = string.format(Language.ZhanLing.LevelStr2, new_str)
--     end)

--     tween_sequence:Append(tween_alpha_one)

--     tween_sequence:AppendInterval(0.3)

--     tween_sequence:OnComplete(function ()
--         node_root.effect_boom:SetActive(false)
--         self.zhanling_up_level_tween:Kill()
--         self.zhanling_up_level_tween = nil
--     end)

--     self.zhanling_up_level_tween = tween_sequence
-- end

function ZhanLingView:FlushRewardList()
    local reward_list = ZhanLingWGData.Instance:GetRewardShowRemindInfo()

    if self.jump_reward_flag then
        self.m_reward_list:SetDataList(reward_list)
        self.reward_list_jump_index = ZhanLingWGData.Instance:GetRewardAutoJumpIndex()
        if self.reward_list_jump_index > 5 then
            self.m_reward_list:JumpToIndex(self.reward_list_jump_index)
        end
        --self:JumpToRealIndex()
        self.jump_reward_flag = nil
    else
        self.m_reward_list:SetDataList(reward_list)
    end
end

function ZhanLingView:JumpToRealIndex()
    if self.m_reward_list then
        local percent = 0
        if self.m_reward_list.list_view and self.m_reward_list.list_view.scroller
            and self.reward_list_jump_index > 5 then
            local cell_height = 76
            local cell_num = self.m_reward_list:GetListViewNumbers()
            local total_length = cell_num * cell_height
            local view_show_height = self.node_list["m_reward_list"].rect.sizeDelta.y
            local scroll_size = total_length - view_show_height
            local jump_position = (self.reward_list_jump_index - 1) * cell_height
            percent = jump_position / scroll_size
        end

        self.m_reward_list:JumptToPrecent(percent)
    end
end

function ZhanLingView:FlushGuDingShowReward(level)
    if not level or level <= 0 then
        return
    end

    local reward
    local show_level, last_level = ZhanLingWGData.Instance:GetNextShowLevel(level)
    --local model_show_level = show_level

    local max_level = ZhanLingWGData.Instance:GetCurRewardMaxLevel()
    if show_level > max_level then
        --model_show_level = last_level
        reward = ZhanLingWGData.Instance:GetSpecialRewardShowRemindInfo()
    else
        reward = ZhanLingWGData.Instance:GetRewardShowInfoByLevel(show_level)--show_level
    end

    if self.old_gd_show_level ~= show_level then
        self.gd_show_reward_item:SetData(reward)
        self.old_gd_show_level = show_level
    end

    -- 模型显示的文本状态
    local final_reward_level = ZhanLingWGData.Instance:GetCurRewardMaxLevel()
    local level_cfg = ZhanLingWGData.Instance:GetRewardShowInfoByLevel(final_reward_level)

    if self.m_model_display and level_cfg then
        if self.old_model_show_itemid ~= level_cfg.model_item_id then
            self.old_model_show_itemid = level_cfg.model_item_id

            local data = {}
            data.should_ani = true
            data.render_type = level_cfg.guding_mark - 1
            data.item_id = level_cfg.model_item_id
            data.model_click_func = function ()
                TipWGCtrl.Instance:OpenItem({item_id = level_cfg.model_item_id})
            end
            data.model_rt_type = ModelRTSCaleType.XS
            self.m_model_display:SetData(data)
            -- 模型战力
            local cap_value = ItemShowWGData.CalculateCapability(level_cfg.model_item_id, true)
            self.node_list.m_model_cap_value.text.text = cap_value
        end

        -- local model_name = ItemWGData.Instance:GetItemName(level_cfg.model_item_id, nil, false)
        -- self.node_list.m_model_name.text.text = model_name

        -- if level_cfg.high_reward_state == REWARD_STATE_TYPE.FINISH then
        --     self.node_list.m_model_desc.text.text = ToColorStr(Language.ZhanLing.IsGetDesc, "#74ff6d")
        -- else
        --     local model_cfg = ZhanLingWGData.Instance:GetActViewShowModelCfg()
        --     local art_desc = model_cfg and model_cfg.art_fonts or ""
        --     self.node_list.m_model_desc.text.text = art_desc
        -- end
    end
end

function ZhanLingView:FlushRestTimeM(time_str)
    if self.node_list.m_reset_time then
        self.node_list.m_reset_time.text.text = time_str
    end
end

-- 获取滑动列表最右的item的index
function ZhanLingView:GetScorllRightItemIndex()
    if self.node_list["m_reward_list"] then
        return self.node_list["m_reward_list"].scroller.EndCellViewIndex + 1
    end

    return 1
end

-- 监听滑动列表改变
function ZhanLingView:OnScrollValueChanged(scroll_obj, start_idx, end_idx)
    local now_index  = end_idx + 1
    if self.old_scorll_index == now_index then
        return
    end
    self.old_scorll_index = now_index
    if self.reward_list_jump_index then
        if now_index >= self.reward_list_jump_index then
            self.old_gd_show_level = now_index
            self:FlushGuDingShowReward(now_index)
            self.reward_list_jump_index = nil
        end
    else
        now_index = now_index > self.max_show_level and self.max_show_level or now_index
        self:FlushGuDingShowReward(now_index)
    end
end

-- 点击白银战令
function ZhanLingView:OnClickNormalZhanling()
    local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
    local show_text = other_cfg and other_cfg.zhanling_tips_1 or ""
    ZhanLingWGCtrl.Instance:SetDescTipsData({anchoredPosition = Vector2(545,-230), show_text = show_text})
end

-- 点击黄金战令
function ZhanLingView:OnClickHighZhanling()
    local act_high = ZhanLingWGData.Instance:GetIsActHighZhanLing()
    if act_high then
        local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
        local show_text = other_cfg and other_cfg.zhanling_tips_2 or ""
        ZhanLingWGCtrl.Instance:SetDescTipsData({anchoredPosition = Vector2(545,-360), show_text = show_text})
    else
        self:OnClickBuyHighZhanling()
    end
end

-- 点击解锁战令
function ZhanLingView:OnClickBuyHighZhanling()
    ZhanLingWGCtrl.Instance:OpenActZhanlingView()
end

-- 一键领取
function ZhanLingView:OnClickGetAllReward()
    ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.GetAllReward)
end
