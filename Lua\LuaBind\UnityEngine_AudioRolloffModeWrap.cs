﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_AudioRolloffModeWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>gin<PERSON>num(typeof(UnityEngine.AudioRolloffMode));
		<PERSON><PERSON>("Logarithmic", get_Logarithmic, null);
		<PERSON><PERSON>("Linear", get_Linear, null);
		<PERSON><PERSON>("Custom", get_Custom, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.End<PERSON>num();
		TypeTraits<UnityEngine.AudioRolloffMode>.Check = CheckType;
		StackTraits<UnityEngine.AudioRolloffMode>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.AudioRolloffMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.AudioRolloffMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Logarithmic(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.AudioRolloffMode.Logarithmic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Linear(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.AudioRolloffMode.Linear);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Custom(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.AudioRolloffMode.Custom);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.AudioRolloffMode o = (UnityEngine.AudioRolloffMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

