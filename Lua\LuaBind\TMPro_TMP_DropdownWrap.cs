﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TMPro_TMP_DropdownWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(TMPro.TMP_Dropdown), typeof(UnityEngine.UI.Selectable));
		<PERSON><PERSON>RegFunction("SetValueWithoutNotify", SetValueWithoutNotify);
		<PERSON><PERSON>unction("RefreshShownValue", RefreshShownValue);
		<PERSON><PERSON>Function("AddOptions", AddOptions);
		<PERSON><PERSON>RegFunction("ClearOptions", ClearOptions);
		<PERSON><PERSON>RegFunction("OnPointerClick", OnPointerClick);
		<PERSON><PERSON>RegFunction("OnSubmit", OnSubmit);
		<PERSON><PERSON>unction("OnCancel", OnCancel);
		<PERSON><PERSON>Function("Show", Show);
		<PERSON>.RegFunction("Hide", Hide);
		<PERSON>.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("template", get_template, set_template);
		<PERSON><PERSON>Reg<PERSON>("captionText", get_captionText, set_captionText);
		L.RegVar("captionImage", get_captionImage, set_captionImage);
		L.RegVar("placeholder", get_placeholder, set_placeholder);
		L.RegVar("itemText", get_itemText, set_itemText);
		L.RegVar("itemImage", get_itemImage, set_itemImage);
		L.RegVar("options", get_options, set_options);
		L.RegVar("onValueChanged", get_onValueChanged, set_onValueChanged);
		L.RegVar("alphaFadeSpeed", get_alphaFadeSpeed, set_alphaFadeSpeed);
		L.RegVar("value", get_value, set_value);
		L.RegVar("IsExpanded", get_IsExpanded, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetValueWithoutNotify(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetValueWithoutNotify(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshShownValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			obj.RefreshShownValue();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddOptions(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<System.Collections.Generic.List<TMPro.TMP_Dropdown.OptionData>>(L, 2))
			{
				TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
				System.Collections.Generic.List<TMPro.TMP_Dropdown.OptionData> arg0 = (System.Collections.Generic.List<TMPro.TMP_Dropdown.OptionData>)ToLua.ToObject(L, 2);
				obj.AddOptions(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Collections.Generic.List<string>>(L, 2))
			{
				TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
				System.Collections.Generic.List<string> arg0 = (System.Collections.Generic.List<string>)ToLua.ToObject(L, 2);
				obj.AddOptions(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Collections.Generic.List<UnityEngine.Sprite>>(L, 2))
			{
				TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
				System.Collections.Generic.List<UnityEngine.Sprite> arg0 = (System.Collections.Generic.List<UnityEngine.Sprite>)ToLua.ToObject(L, 2);
				obj.AddOptions(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Dropdown.AddOptions");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearOptions(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			obj.ClearOptions();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerClick(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerClick(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnSubmit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnSubmit(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnCancel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnCancel(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Show(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			obj.Show();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Hide(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)ToLua.CheckObject<TMPro.TMP_Dropdown>(L, 1);
			obj.Hide();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_template(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.RectTransform ret = obj.template;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index template on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_captionText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			TMPro.TMP_Text ret = obj.captionText;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index captionText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_captionImage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.UI.Image ret = obj.captionImage;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index captionImage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_placeholder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.UI.Graphic ret = obj.placeholder;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index placeholder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_itemText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			TMPro.TMP_Text ret = obj.itemText;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index itemText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_itemImage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.UI.Image ret = obj.itemImage;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index itemImage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_options(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			System.Collections.Generic.List<TMPro.TMP_Dropdown.OptionData> ret = obj.options;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index options on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onValueChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			TMPro.TMP_Dropdown.DropdownEvent ret = obj.onValueChanged;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onValueChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_alphaFadeSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			float ret = obj.alphaFadeSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alphaFadeSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			int ret = obj.value;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index value on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsExpanded(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			bool ret = obj.IsExpanded;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsExpanded on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_template(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.template = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index template on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_captionText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			TMPro.TMP_Text arg0 = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 2);
			obj.captionText = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index captionText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_captionImage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.UI.Image arg0 = (UnityEngine.UI.Image)ToLua.CheckObject<UnityEngine.UI.Image>(L, 2);
			obj.captionImage = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index captionImage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_placeholder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.UI.Graphic arg0 = (UnityEngine.UI.Graphic)ToLua.CheckObject<UnityEngine.UI.Graphic>(L, 2);
			obj.placeholder = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index placeholder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_itemText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			TMPro.TMP_Text arg0 = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 2);
			obj.itemText = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index itemText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_itemImage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			UnityEngine.UI.Image arg0 = (UnityEngine.UI.Image)ToLua.CheckObject<UnityEngine.UI.Image>(L, 2);
			obj.itemImage = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index itemImage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_options(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			System.Collections.Generic.List<TMPro.TMP_Dropdown.OptionData> arg0 = (System.Collections.Generic.List<TMPro.TMP_Dropdown.OptionData>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<TMPro.TMP_Dropdown.OptionData>));
			obj.options = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index options on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onValueChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			TMPro.TMP_Dropdown.DropdownEvent arg0 = (TMPro.TMP_Dropdown.DropdownEvent)ToLua.CheckObject<TMPro.TMP_Dropdown.DropdownEvent>(L, 2);
			obj.onValueChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onValueChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_alphaFadeSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.alphaFadeSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alphaFadeSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Dropdown obj = (TMPro.TMP_Dropdown)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.value = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index value on a nil value");
		}
	}
}

