MarryBlessingView = MarryBlessingView or BaseClass(SafeBaseView)

function MarryBlessingView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_blessing_view")
	
	self.cur_data_times = -1 --当前显示的是第几对情侣
end

function MarryBlessingView:CloseCallBack()
	self:ClearTime()
	MarryWGData.Instance:ClearBlessingInfo()
end

function MarryBlessingView:ReleaseCallBack()
	-- if self.head_frame_cell then
	-- 	self.head_frame_cell:DeleteMe()
	-- 	self.head_frame_cell = nil
    -- end

    -- if self.lover_head_frame_cell then
	-- 	self.lover_head_frame_cell:DeleteMe()
	-- 	self.lover_head_frame_cell = nil
	-- end
end

function MarryBlessingView:LoadCallBack()
	self.cur_data_times = -1 --当前显示的是第几对情侣
	XUI.AddClickEventListener(self.node_list["btn_send_blessing"], BindTool.Bind(self.OnSendBlessingClick, self))
	XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.OnCloseClick, self))
	XUI.AddClickEventListener(self.node_list["btn_not_tips"], BindTool.Bind(self.OnCloseNoTips, self))

	local cur_status = MarryWGData.Instance:GetCurLoginBlessingNoTips()
	self.node_list["btn_not_tips_hook"]:CustomSetActive(cur_status)
end

function MarryBlessingView:ShowIndexCallBack()
	self:FlushBlessingViewIndex()
end

function MarryBlessingView:FlushBlessingViewIndex()
	local data = MarryWGData.Instance:GetBlessingInfo()

	for k,v in pairs(data) do
		if v.server_marry_times and self.cur_data_times ~= k then
			self.cur_data_times = k
			break
		end
	end

	if self.cur_data_times == -1 then
		self:Close()
	else
		self:FlushBlessingView()
	end
end

function MarryBlessingView:FlushBlessingView()
	local data = MarryWGData.Instance:GetBlessingInfo(self.cur_data_times)
	if data and data.server_marry_times then
		self:UpdateHeadIcon(data)
		self:UpdateContent(data)
	else
		self:Close()
		return
	end

	local closetime = MarryWGData.Instance:GetOtherProfessCfg().blessing_count_down --关闭的时间
	if nil ~= closetime and closetime > 0 then
		self:ClearTime()
		self.close_time = GlobalTimerQuest:AddDelayTimer(function ()
			self:OnSendBlessingClick()
		end, closetime)
	end
end

function MarryBlessingView:ClearTime()
	if self.close_time then
		GlobalTimerQuest:CancelQuest(self.close_time)
		self.close_time = nil
	end
end

--刷新头像
function MarryBlessingView:UpdateHeadIcon(data)
	self.node_list["lbl_headname_left"].text.text = data.name1
    self.node_list["lbl_headname_right"].text.text = data.name2

    -- if not self.head_frame_cell then
    --     self.head_frame_cell = BaseHeadCell.New(self.node_list["left_head_cell"])
    -- end
    --local data1 = {role_id = data.uid1, sex = data.sex1, prof = data.prof1}
    --self.head_frame_cell:SetData(data1)

    -- if not self.lover_head_frame_cell then
    --     self.lover_head_frame_cell = BaseHeadCell.New(self.node_list["right_head_cell"])
    -- end
    -- local data2 = {role_id = data.uid2, sex = data.sex2, prof = data.prof2}
    -- self.lover_head_frame_cell:SetData(data2)
end


function MarryBlessingView:UpdateContent( data )
	self.node_list["lbl_info"].text.text = string.format(Language.Marry.MarryBlessingInfo,data.name1, data.name2, data.server_marry_times)
end

--发送祝福
function MarryBlessingView:OnSendBlessingClick()
	self:SendBlessingSeq()
	self:OnCloseClick()
end

function MarryBlessingView:OnCloseClick()
	local cur_status = MarryWGData.Instance:GetCurLoginBlessingNoTips()
	if cur_status then
		self:Close()
	else
		MarryWGData.Instance:SetBlessingInfo(self.cur_data_times, nil)
		self.cur_data_times = -1
		self:FlushBlessingViewIndex()
	end
end

function MarryBlessingView:SendBlessingSeq()
	local data = MarryWGData.Instance:GetBlessingInfo(self.cur_data_times)
	if data == nil or self.cur_data_times == -1 then
		return
	end
	local boo1 = SocietyWGData.Instance:CheckIsFriend(data.uid1)
	local boo2 = SocietyWGData.Instance:CheckIsFriend(data.uid2)
	local send_uid = 0
	if boo1 then
		send_uid = data.uid1
	elseif boo2 then
		send_uid = data.uid2
	end

	--判断盟友
	if send_uid == 0 then
		local m_list = GuildDataConst.GUILD_MEMBER_LIST
		for i = 1, m_list.count do
			local item = m_list.list[i]
			if item.uid == data.uid1 then
				send_uid = data.uid1
				break
			elseif item.uid == data.uid2 then
				send_uid = data.uid2
				break
			end
		end
	end
	MarryWGCtrl.Instance:SendMarryWish(send_uid, data.server_marry_times)
end

function MarryBlessingView:OnCloseNoTips()
	local cur_status = MarryWGData.Instance:GetCurLoginBlessingNoTips()
	MarryWGData.Instance:SetCurLoginBlessingNoTips(not cur_status)
	self.node_list["btn_not_tips_hook"]:CustomSetActive(not cur_status)
end
