--Boss乱斗
ActBossFightSceneView = ActBossFightSceneView or BaseClass(SafeBaseView)

function ActBossFightSceneView:__init()
	-- self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/boss_fight_ui_prefab", "content")
	self.view_cache_time = 0
end

function ActBossFightSceneView:__delete()
	
end

function ActBossFightSceneView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	local CDM = CountDownManager.Instance
	if CDM:HasCountDown("boss_fight_flush") then
		CDM:RemoveCountDown("boss_fight_flush")
	end
end

function ActBossFightSceneView:LoadCallBack()
	local function callback(show_node)
		ResMgr:LoadGameobjSync("uis/view/boss_fight_ui_prefab", "boss_fight_scene_view",
				function (obj)
					if self:IsO<PERSON>() then
						obj.transform:SetParent(show_node.transform,false)
						obj = U3DObject(obj)
						self.data_node = FuBenTFTaskDataList.New(obj)
						MainuiWGCtrl.Instance:SetTaskPanel(false)
						self:LoadMainCallBack()
					else
						self:Close()
					end
				end)
	end
	MainuiWGCtrl.Instance:GetTaskMaskRootNode(callback)
	local info = ServerActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.KF_BOSS_FIGHT)
	if info.status == ACTIVITY_STATUS.OPEN then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(info.next_time)
	end
end

function ActBossFightSceneView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.data_node then
		local obj = self.data_node.node_list.boss_fight_root.gameObject
		self.data_node:DeleteMe()
		ResMgr:Destroy(obj)
		self.data_node = nil
	end
end

function ActBossFightSceneView:LoadMainCallBack()
	self:CreateRankList()
	self.data_node.node_list["btn_rank"].button:AddClickListener(BindTool.Bind(self.OnClickSwitch, self))
	self.data_node.node_list["btn_desc"].button:AddClickListener(BindTool.Bind(self.OnClickOpenRewardPanel, self))
	self:Flush()
end	

function ActBossFightSceneView:OnClickSwitch()
	self.switch_panel = not self.switch_panel
	self.data_node.node_list["info_panel"]:SetActive(not self.switch_panel)
	self.data_node.node_list["rank_panel"]:SetActive(self.switch_panel)
	self.data_node.node_list["btn_rank_txt"].text.text = self.switch_panel and Language.ActBossFight.ActInfo or Language.ActBossFight.RankInfo
end

function ActBossFightSceneView:OnClickOpenRewardPanel()
	ViewManager.Instance:Open(GuideModuleName.BossFightRewardView)
end

function ActBossFightSceneView:CreateRankList()
	self.rank_list = AsyncListView.New(ActBossFightSceneRankRender,self.data_node.node_list["rank_list"])
	self:FlushRankList()
end

function ActBossFightSceneView:OnFlush(param)
	self:FlushRankList()
	self:FlushSelfInfo()
	self:FlushSceneInfo()
	self:CheckTime()
end

function ActBossFightSceneView:FlushSelfInfo()
	local info = ServerActivityWGData.Instance:GetBossFightUserInfo()
	if info == nil then return end
	self.data_node.node_list["txt_score"].text.text = Language.ActBossFight.TitleScore .. ToColorStr(info.score,COLOR3B.GREEN)
	self.data_node.node_list["my_txt_name"].text.text = GameVoManager.Instance:GetMainRoleVo().name
	self.data_node.node_list["my_txt_score"].text.text = info.score -- ToColorStr(info.score,COLOR3B.GREEN)
	local rank = ServerActivityWGData.Instance:GetBossFightUserRank()
	if rank == nil then return end
	self.data_node.node_list["my_txt_rank"].text.text = rank
	self.data_node.node_list["txt_rank"].text.text = Language.ActBossFight.TitleRank .. ToColorStr(rank,COLOR3B.GREEN)
end

function ActBossFightSceneView:FlushSceneInfo()
	local info = ServerActivityWGData.Instance:GetBossFightSceneInfo()
	if info == nil then
		return
	end
	self.data_node.node_list["txt_cur_wave"].text.text = Language.ActBossFight.TitleWave .. ToColorStr(info.cur_wave,COLOR3B.GREEN)
	self.data_node.node_list["txt_boss_num"].text.text = Language.ActBossFight.TitleBoss .. ToColorStr(info.boss_count,COLOR3B.GREEN)
	self.data_node.node_list["txt_monster_num"].text.text = Language.ActBossFight.TitleMonster .. ToColorStr(info.monster_count,COLOR3B.GREEN)
end

function ActBossFightSceneView:FlushRankList()
	local list = ServerActivityWGData.Instance:GetBossFightRankInfo()
	self.rank_list:SetDataList(list)
end

function ActBossFightSceneView:CheckTime()
	local flush_time = ServerActivityWGData.Instance:GetBossFightFlushTime()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local last_time = flush_time - ServerActivityWGData.Instance:GetBossFightCountLastTime()
	if now_time > flush_time and now_time < last_time then
		self.node_list["img_word_tip"]:SetActive(false)
		self.node_list["txt_time"].text.text = ""
		return
	end
	local CDM = CountDownManager.Instance
	if CDM:HasCountDown("boss_fight_flush") then
		return
	end
	self.node_list["img_word_tip"]:SetActive(true)
	CDM:AddCountDown("boss_fight_flush", BindTool.Bind(self.UpdateCountDown,self),
		BindTool.Bind(self.CompleteCountDown, self), flush_time, nil, 0.3)
end

function ActBossFightSceneView:UpdateCountDown(elapse_time, total_time)
	local time = GameMath.Round(total_time - elapse_time)
	self.node_list["txt_time"].text.text = time
	self.node_list["img_word_tip"]:SetActive(true)
end

function ActBossFightSceneView:CompleteCountDown()
	self.node_list["img_word_tip"]:SetActive(false)
	self.node_list["txt_time"].text.text = ""
end

---------------------------------------------------------------------------------------------
ActBossFightSceneRankRender = ActBossFightSceneRankRender or BaseClass(BaseRender)
function ActBossFightSceneRankRender:__init()

end

function ActBossFightSceneRankRender:__delete()

end

function ActBossFightSceneRankRender:LoadCallBack()

end

function ActBossFightSceneRankRender:OnFlush()
	self.node_list["my_txt_rank"].text.text = self.index
	self.node_list["my_txt_name"].text.text = self.data.name
	self.node_list["my_txt_score"].text.text = self.data.score
end

function ActBossFightSceneRankRender:__init()

end