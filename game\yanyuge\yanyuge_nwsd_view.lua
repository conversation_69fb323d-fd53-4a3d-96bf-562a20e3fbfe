function YanYuGeExchangeShopView:NWSDLoadCallBack()
    if not self.nwsd_grid_list then
		self.nwsd_grid_list = AsyncBaseGrid.New()
		self.nwsd_grid_list:CreateCells({col = 4,change_cells_num = 1, list_view = self.node_list["nwsd_grid_list"],
		assetBundle = "uis/view/yanyuge_ui_prefab", assetName = "nwsd_grid_list_cell",  itemRender = NWSDGridItemCellRender})
		self.nwsd_grid_list:SetStartZeroIndex(false)
	end
end

function YanYuGeExchangeShopView:NWSDShowIndexCallBack()

end

function YanYuGeExchangeShopView:NWSDReleaseCallBack()
    if self.nwsd_grid_list then
        self.nwsd_grid_list:DeleteMe()
        self.nwsd_grid_list = nil
    end
end

function YanYuGeExchangeShopView:NWSDOnFlush(param_t)
    local shop_info = YanYuGeWGData.Instance:GetNWSDGridDataList(true)
    self.nwsd_grid_list:SetDataList(shop_info)
end

------------------NWSDGridItemCellRender-------------
NWSDGridItemCellRender = NWSDGridItemCellRender or BaseClass(BaseRender)
function NWSDGridItemCellRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
    end

    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuy, self))
end

function NWSDGridItemCellRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function NWSDGridItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local reward_cfg = self.data.cfg.reward_item[0]
    if reward_cfg then
        self.item_cell:SetData(reward_cfg)
    end

    local item_name = ItemWGData.Instance:GetItemName(reward_cfg.item_id)
    self.node_list["desc_name"].tmp.text = item_name

    local discount = self.data.cfg.discount
    if discount and discount ~= "" then
        self.node_list.desc_discount.tmp.text = discount
        self.node_list.discount_bg:CustomSetActive(true)
    else
        self.node_list.discount_bg:CustomSetActive(false)
    end

    self.node_list["desc_btn_buy"].tmp.text = string.format(Language.YanYuGe.ScoreStr, self.data.cfg.consume_score)

    local refresh_type = self.data.cfg.refresh_type or 0
    -- self.node_list.desc_buy_limit:SetActive(refresh_type ~= 0)
    local score = YanYuGeWGData.Instance:GetCurScore()

    if refresh_type ~= 0 then
        local remain_times = self.data.cfg.buy_limit - self.data.buy_times
        local color = remain_times > 0 and COLOR3B.L_GREEN or COLOR3B.L_RED

        self.node_list["desc_buy_limit"].tmp.text = string.format(Language.YanYuGe.NWSDRefreshType[refresh_type], color, remain_times, self.data.cfg.buy_limit)
        self.node_list["flag_sell_out"]:SetActive(remain_times <= 0)
        self.node_list["btn_buy"]:CustomSetActive(remain_times > 0)

        -- self.node_list.btn_buy_remind:CustomSetActive(remain_times > 0 and score >= self.data.cfg.consume_score)
    else
        self.node_list["desc_buy_limit"].tmp.text = Language.YanYuGe.NWSDRefreshType[refresh_type]
        self.node_list["flag_sell_out"]:SetActive(false)
        self.node_list["btn_buy"]:CustomSetActive(true)
        -- self.node_list.btn_buy_remind:CustomSetActive(score >= self.data.cfg.consume_score)
    end
end

function NWSDGridItemCellRender:OnClickBuy()
    if IsEmptyTable(self.data) then
        return
    end

    local refresh_type = self.data.cfg.refresh_type or 0
    local consume_score = self.data.cfg.consume_score or 0
    local cur_score = YanYuGeWGData.Instance:GetCurScore()
    if cur_score < consume_score then
        TipWGCtrl.Instance:ShowSystemMsg(Language.YanYuGe.NoEnoughScore)
        RechargeWGCtrl.Instance:RemindRechargeByCangJinShangPuScoreNoEnough((consume_score - cur_score) / 10)
        return
    end

    if refresh_type ~= 0 then
        local remain_times = self.data.cfg.buy_limit - self.data.buy_times
        if remain_times <= 0 then
            TipWGCtrl.Instance:ShowSystemMsg(Language.YanYuGe.NWSDMaxExchangeNum)
            return
        end
    end

    YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_LIMIT_SHOP, self.data.cfg.seq, 1)
end