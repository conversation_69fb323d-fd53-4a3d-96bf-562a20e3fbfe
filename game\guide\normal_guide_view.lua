NormalGuideView = NormalGuideView or BaseClass(SafeBaseView)
local MASK_TYPE = {
	CIRCLE = 1,
	RECT = 2,
}

local AafeAreaAdapter = 200		-- 刘海屏偏移

function NormalGuideView:__init()
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "NormalGuideView")
	self.view_layer = UiLayer.Guide
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:SetMaskBg()

	self.click_obj = nil
	self.open_tween = nil
	self.close_tween = nil
end

-- 设置按钮位置
function NormalGuideView:SetBtnObj(obj)
	if obj and self.click_obj ~= obj then
		self.click_obj = obj
		self.click_rect = self.click_obj:GetComponent(typeof(UnityEngine.RectTransform))
	end
end

-- 设置当前步骤配置
function NormalGuideView:SetStepCfg(cfg)
	self.step_cfg = cfg
end

-- 设置当前引导配置
function NormalGuideView:SetGuideCfg(cfg)
	self.guide_cfg = cfg
end

-- 设置第一次打开
function NormalGuideView:SetIsFrist(state)
	self.frist_open = state
end

-- 设置点击回调
function NormalGuideView:SetClickCallBack(callback, is_trigger_action)
	self.click_call_back = callback
	self.click_trigger_action = is_trigger_action
end

function NormalGuideView:SetArrow(arrow)
	self.auto_arrow = arrow
end

function NormalGuideView:OpenCallBack()
	-- if self.step_cfg ~= nil then
	-- 	if self.step_cfg.module_name ~= nil and self.step_cfg.module_name ~= "" then
	-- 		ViewManager.Instance:CloseNormalViewExceptViewName(self.step_cfg.module_name)
	-- 	end
	-- end
end

function NormalGuideView:CheckIsCanOpenView(view_name)
	local next_step_cfg = FunctionGuide.Instance:GetNextStepGuide()
	if next_step_cfg ~= nil and next_step_cfg.module_name ~= GuideModuleName.MainUIView and view_name ~= next_step_cfg.module_name then
		return false
	end

	return true
end

function NormalGuideView:CloseCallBack()
	self.click_obj = nil
	self.click_rect = nil
	self.click_call_back = nil
	self.is_end_ani = nil
	self.change_value = nil
	self.target_width = nil
	self.target_height = nil
	self.obj_height = nil
	self.obj_width = nil
	self.obj_pos_x = nil
	self.obj_pos_y = nil
	self.is_show_mask = nil
	self.has_model_do_action = nil
	self.step_cfg = nil
	self.guide_cfg = nil
	self.click_trigger_action = nil

	self:CancelDelayClickStrongBtnTimer()
	self:CancelDelayShowStrongMarkTimer()
	self:RemoveDescTimer()
	self:RemoveMaskTweenTimer()
end

function NormalGuideView:LoadCallBack()
	--获取变量
	-- self.girl_image = self:FindVariable("GirlImage")					--美女图片
	self.node_list.Block:SetActive(true)

	--获取组件
	self.kuang = self.node_list["Kuang"]									--指引框
	self.other_kuang = self.node_list["OtherKuang"]						--指引框(提示用)
	self.strong_mask_1 = self.node_list["StrongMask"]						-- 强引导黑幕1（圆形）
	self.strong_mask_2 = self.node_list["StrongMask2"]						-- 强引导黑幕2（长方形）
	self.strong_mask_3 = self.node_list["StrongMask3"]						-- 强引导黑幕3（shader）

	self.img_material = self.strong_mask_3.raw_image.material

	self.left_strong_guide = self.node_list["LeftStrongGuide"]			--左强指引
	self.right_strong_guide = self.node_list["RightStrongGuide"]			--右强指引
	self.top_strong_guide = self.node_list["TopStrongGuide"]				--上强指引
	self.bottom_strong_guide = self.node_list["BottomStrongGuide"]		--下强指引
	self.left = self.node_list["Left"]
	self.right = self.node_list["Right"]
	self.top = self.node_list["Top"]
	self.bottom = self.node_list["Bottom"]
	self.strong_guide = self.node_list["StrongGuide"]
	self.girl_arrow = self.node_list["GirlArrow"]							--美女强指引箭头
	self.week_block = self.node_list["WeekBlock"]							--弱指引遮罩

	self.node_list.WeekBlock.button:AddClickListener(BindTool.Bind(self.WeekBlockClick, self))
	self.node_list.WeekBtn.button:AddClickListener(BindTool.Bind(self.StrongBlockClick, self))
	self.left.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.right.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.top.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.bottom.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.node_list.StrongBlock.button:AddClickListener(BindTool.Bind(self.StrongBlockClick, self))

	self.begin_left_bottom = Vector2(-UnityEngine.Screen.width / 2 , -UnityEngine.Screen.height/2)
	self.begin_right_top = Vector2(UnityEngine.Screen.width / 2 , UnityEngine.Screen.height/2)
end

function NormalGuideView:ReleaseCallBack()
	-- 清理变量和对象
	-- self.girl_image = nil
	self.kuang = nil
	self.other_kuang = nil
	self.strong_mask_1 = nil
	self.strong_mask_2 = nil
	self.strong_mask_3 = nil
	self.img_material = nil
	self.left_strong_guide = nil
	self.right_strong_guide = nil
	self.top_strong_guide = nil
	self.bottom_strong_guide = nil
	self.cur_strong_guide = nil
	self.left = nil
	self.right = nil
	self.top = nil
	self.bottom = nil
	self.strong_guide = nil
	self.girl_arrow = nil
	self.week_block = nil
	
	if self.anim_model then
		self.anim_model:DeleteMe()
		self.anim_model = nil
	end
end

function NormalGuideView:OnFlush()
	self:FlushNow()
end

-- 刷新界面
function NormalGuideView:FlushNow()
	self:FlsuhCurrViewStatus()

	-- 界面动画修改
	self:CancelDelayShowStrongMarkTimer()
	self.delay_show_strong_mask = GlobalTimerQuest:AddDelayTimer(function()
		if self.step_cfg ~= nil then
			if self.step_cfg.is_modal == 1 then	-- 是否为强引导
				self:FlushStrong()
			else
				self:FlushWeak()
			end
		end
	end, 0.5)
end

function NormalGuideView:FlsuhCurrViewStatus()
	self.node_list.Block:CustomSetActive(false)
	self.img_material:SetVector("_Origin",Vector4(10000,10000, 0, 0))
	if self.step_cfg and next(self.step_cfg) then
		local audio_id = self.step_cfg.offset_x
		if audio_id and audio_id ~= "" and audio_id ~= self.old_audio_id then
			self.old_audio_id = audio_id
			local bundle, asset = ResPath.GetVoiceRes(audio_id)
			TalkCache.PlayGuideTalkAudio(bundle, asset)
		end

		if self.step_cfg.param1 and self.step_cfg.param1 ~= "" then
			local param_t = Split(self.step_cfg.param1, ";")
			self.node_list.eff:ChangeAsset(param_t[2], param_t[1])
			if param_t[3] and param_t[4] then
				self.node_list.eff.transform:SetLocalScale(param_t[3], param_t[4], 1)
			end
		else
			self.node_list.eff:ChangeAsset(ResPath.GetUIEffect("UI_yindao_dianji"))
		end

		self.node_list.GuideTextArrow:CustomSetActive(self.click_rect ~= nil)
		self.node_list.StrongBlock:CustomSetActive(self.step_cfg.is_modal == 1)
		self.week_block:CustomSetActive(self.step_cfg.is_modal ~= 1)
		self.strong_mask_1:CustomSetActive(false)
		self.strong_mask_2:CustomSetActive(false)
		self.left_strong_guide:CustomSetActive(false)
		self.right_strong_guide:CustomSetActive(false)
		self.top_strong_guide:CustomSetActive(false)
		self.bottom_strong_guide:CustomSetActive(false)
		self.left:CustomSetActive(self.click_rect ~= nil)
		self.right:CustomSetActive(self.click_rect ~= nil)
		self.top:CustomSetActive(self.click_rect ~= nil)
		self.bottom:CustomSetActive(self.click_rect ~= nil)
		self.kuang:CustomSetActive(false)
		self.node_list.eff:CustomSetActive(false)

		if self.click_rect ~= nil then
			if self.mask_bg  then
				self.mask_bg.image.color = Color.New(0, 0, 0, 0)
			end
		else
			if self.mask_bg  then
				self.mask_bg.image.color = Color.New(0, 0, 0, SafeBaseView.MASK_A)
			end
		end

		-- 美女指引逻辑，暂时没用到
		self.node_list.GirlArrow:CustomSetActive(FunctionGuide.IsGuideStepType(self.step_cfg.step_type))	--美女指引
		local offset = Split(self.step_cfg.offset_y, "##")
		local off_x, off_y = offset[1] or 0, offset[2] or 0
		local off_x2, off_y2 = offset[3] or 0, offset[4] or 0
		if self.auto_arrow == nil then
			self.node_list.GirlArrow.transform.localPosition = Vector2(off_x, off_y)
		else
			if self.auto_arrow == 0 and offset[3] and offset[4] then
				self.node_list.GirlArrow.transform.localPosition = Vector2(off_x2, off_y2)
			else
				self.node_list.GirlArrow.transform.localPosition = Vector2(off_x, off_y)
			end
		end
	end
end


-- 重置强引导
function NormalGuideView:ReSetStrongGuide()
	local rect = self.root_node:GetComponent(typeof(UnityEngine.RectTransform))
	local width = rect.rect.width
	local height = rect.rect.height

	self.left.rect.sizeDelta = Vector2(0, self.left.rect.sizeDelta.y)
	self.right.rect.sizeDelta = Vector2(0, self.right.rect.sizeDelta.y)
	self.bottom.rect.sizeDelta = Vector2(self.bottom.rect.sizeDelta.x, 0)
	self.top.rect.sizeDelta = Vector2(self.top.rect.sizeDelta.x, 0)
end

-- 更新位置
function NormalGuideView:UpdatePos()
	if (not self.click_rect) or IsNil(self.click_rect) or (not self.step_cfg) then
		return 0, 0
	end

	--获取指引按钮的屏幕坐标
	local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.click_rect.position)

	--转换屏幕坐标为本地坐标
	local rect = self.root_node:GetComponent(typeof(UnityEngine.RectTransform))
	local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos_tbl, UICamera, Vector2(0, 0))

	local click_real_rect = self.click_rect.rect
	local pivot = self.click_rect.pivot
	local btn_height = click_real_rect.height
	local btn_width = click_real_rect.width
	self.obj_pos_x = local_pos_tbl.x + (0.5 - pivot.x) * btn_width
	self.obj_pos_y = local_pos_tbl.y + (0.5 - pivot.y) * btn_height

	return self.obj_pos_x, self.obj_pos_y
end

-- 刷新强引导
function NormalGuideView:FlushStrong()
	if (not self.click_rect) or IsNil(self.click_rect) or (not self.step_cfg) then
		return
	end

	self:ReSetStrongGuide()
	self.node_list.eff:CustomSetActive(self.step_cfg.is_finger_effect ~= 2 and self.step_cfg.need_rect_effect == 1)
	self.kuang:CustomSetActive(self.step_cfg.is_finger_effect == 2)
	
	local pos_x, pos_y = self:UpdatePos()
	local strong_guide, guide_x, guide_y = self:GetStrongNodePos(pos_x, pos_y)
	--拿到按钮的区域大小
	local click_real_rect = self.click_rect.rect
	local btn_height = click_real_rect.height
	local btn_width = click_real_rect.width
	--记录指引按钮的大小
	self.obj_height = btn_height
	self.obj_width = btn_width
	-- 引导的界面是FHD的，如果引导界面为HD的需要把控件宽高缩放一下计算
	local step_view = ViewManager.Instance:GetView(self.step_cfg.module_name)
	if step_view then
		if step_view.reference_resolution == REFERENCE_RESOLUTION_TYPE.HD then
			self.obj_width = self.obj_width / 0.7
			self.obj_height = self.obj_height / 0.7
		end
	end

	self.obj_width_half = self.obj_width / 2
	self.obj_height_half = self.obj_height / 2
	self.obj_pos_x = pos_x
	self.obj_pos_y = pos_y

	if self.obj_width <= 40 then
		self.obj_width = 40
	end

	if self.obj_height <= 40 then
		self.obj_height = 40
	end

	if strong_guide then
		strong_guide:CustomSetActive(true)
		local panel = strong_guide.transform:FindHard("Panel")
		if panel then
			panel.gameObject:SetActive(self.step_cfg.step_type == GuideStepType.Arrow)
		end

		local des = strong_guide.transform:FindHard("Panel/Des")
		if FunctionGuide.IsGuideStepType(self.step_cfg.step_type) then	-- 设置美女指引
			des = self.girl_arrow.transform:FindHard("Panel/Des")
			self:SetGirlPanel()
		end

		local normal_rect = strong_guide.rect
		normal_rect.localPosition = Vector2(guide_x, guide_y)
		self:SetDec(des)
	end

	self.node_list.eff.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_1.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_2.rect.localPosition = Vector2(pos_x, pos_y)
	self.cur_strong_guide = strong_guide
	--设置框
	self.kuang.rect.localPosition = Vector2(pos_x, pos_y)
	self.kuang.rect.sizeDelta = Vector2(self.obj_width, self.obj_height)
	-- 获取当前全屏UI的宽度和高度
	local height = self.node_list.StrongBlock.rect.rect.height
	local width = self.node_list.StrongBlock.rect.rect.width
	--计算出右手坐标系的位置，原点为左下角
	if pos_x > 0 then
		pos_x = pos_x + width / 2
	else
		pos_x = width / 2 - math.abs(pos_x)
	end

	if pos_y > 0 then
		pos_y = pos_y + height / 2
	else
		pos_y = height / 2 - math.abs(pos_y)
	end

	-- 左右设置宽度即可
	local left_width = pos_x - self.obj_width / 2 
	local right_width = width - (pos_x + self.obj_width / 2) 
	local bottom_height = pos_y - self.obj_height / 2
	local top_height = height - (pos_y + self.obj_height / 2)
	self.left.rect.sizeDelta = Vector2(AafeAreaAdapter + left_width, self.left.rect.sizeDelta.y)
	self.right.rect.sizeDelta = Vector2(AafeAreaAdapter + right_width, self.right.rect.sizeDelta.y)
	self.bottom.rect.sizeDelta = Vector2(self.bottom.rect.sizeDelta.x, bottom_height)
	self.top.rect.sizeDelta = Vector2(self.top.rect.sizeDelta.x, top_height)

	-- 五秒之后自动点击
	if self.guide_cfg and self.guide_cfg.auto_next_step == 1 then
		self:AddDelayClickStrongBtnTimer()
	end

	self:ShowStrongMask(false)
end

-- 动画结束后再更新一下位置
function NormalGuideView:UpdateFinalPosScale()
	if (not self.click_rect) or IsNil(self.click_rect) or (not self.step_cfg) then
		return
	end

	local pos_x, pos_y = self:UpdatePos()
	local strong_guide, guide_x, guide_y = self:GetStrongNodePos(pos_x, pos_y)
	--拿到按钮的区域大小
	local click_real_rect = self.click_rect.rect
	local btn_height = click_real_rect.height
	local btn_width = click_real_rect.width
	--记录指引按钮的大小
	self.obj_height = btn_height
	self.obj_width = btn_width
	-- 引导的界面是FHD的，如果引导界面为HD的需要把控件宽高缩放一下计算
	local step_view = ViewManager.Instance:GetView(self.step_cfg.module_name)
	if step_view then
		if step_view.reference_resolution == REFERENCE_RESOLUTION_TYPE.HD then
			self.obj_width = self.obj_width / 0.7
			self.obj_height = self.obj_height / 0.7
		end
	end

	self.obj_width_half = self.obj_width / 2
	self.obj_height_half = self.obj_height / 2
	self.obj_pos_x = pos_x
	self.obj_pos_y = pos_y

	if self.obj_width <= 40 then
		self.obj_width = 40
	end

	if self.obj_height <= 40 then
		self.obj_height = 40
	end

	self.node_list.eff.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_1.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_2.rect.localPosition = Vector2(pos_x, pos_y)
	--设置框
	self.kuang.rect.localPosition = Vector2(pos_x, pos_y)
	self.kuang.rect.sizeDelta = Vector2(self.obj_width, self.obj_height)

	-- 获取当前全屏UI的宽度和高度
	local height = self.node_list.StrongBlock.rect.rect.height
	local width = self.node_list.StrongBlock.rect.rect.width
	--计算出右手坐标系的位置，原点为左下角
	if pos_x > 0 then
		pos_x = pos_x + width / 2
	else
		pos_x = width / 2 - math.abs(pos_x)
	end

	if pos_y > 0 then
		pos_y = pos_y + height / 2
	else
		pos_y = height / 2 - math.abs(pos_y)
	end

	-- 左右设置宽度即可
	local left_width = pos_x - self.obj_width / 2 
	local right_width = width - (pos_x + self.obj_width / 2) 
	local bottom_height = pos_y - self.obj_height / 2
	local top_height = height - (pos_y + self.obj_height / 2)
	self.left.rect.sizeDelta = Vector2(AafeAreaAdapter + left_width, self.left.rect.sizeDelta.y)
	self.right.rect.sizeDelta = Vector2(AafeAreaAdapter + right_width, self.right.rect.sizeDelta.y)
	self.bottom.rect.sizeDelta = Vector2(self.bottom.rect.sizeDelta.x, bottom_height)
	self.top.rect.sizeDelta = Vector2(self.top.rect.sizeDelta.x, top_height)
end

-- 获取强引导的节点和位置
function NormalGuideView:GetStrongNodePos(pos_x, pos_y)
	local strong_guide, guide_x, guide_y
	--设置强指引的位置
	local arrow_dir = self.step_cfg.arrow_dir
	guide_x = pos_x
	guide_y = pos_y

	if arrow_dir == "left" then
		strong_guide = self.left_strong_guide
	elseif arrow_dir == "right" then
		strong_guide = self.right_strong_guide
	elseif arrow_dir == "up" then
		strong_guide = self.top_strong_guide
	elseif arrow_dir == "down" then
		strong_guide = self.bottom_strong_guide
	end

	return strong_guide, guide_x, guide_y
end

-- 设置指引米哦啊书
function NormalGuideView:SetDec(des)
	if nil == des then
		return 
	end

	if (not self.step_cfg) or (not self.step_cfg.arrow_tip) or (not self.step_cfg.arrow_tip == "") then
		return
	end

	local arrow_tip = self.step_cfg.arrow_tip or ""
	local arrow_tip_t = Split(arrow_tip, "##")
	des.gameObject:SetActive(arrow_tip_t[1] ~= nil and arrow_tip_t[1] ~= "")
	local show_text = des.transform:Find("Text")

	if arrow_tip ~= "" and show_text then
		show_text = U3DObject(show_text.gameObject)
		if #arrow_tip_t == 2 then
			self:RemoveDescTimer()
			local time = tonumber(arrow_tip_t[2])
			show_text.text.text = string.format(arrow_tip_t[1], time)
			self.dec_timer =  GlobalTimerQuest:AddRunQuest(function ()
				time = time - 1
				show_text.text.text = string.format(arrow_tip_t[1], time)
				if time <= 0 then
					self:Close() 					-- 引导一段时间，玩家不点击则消失
					self:DoNext()
				end
			end, 1)
		else
			show_text.text.text = arrow_tip_t[1]
		end
	end
end

function NormalGuideView:SetGirlPanel()
	local text_w = 308 --234
	self.node_list.girl:CustomSetActive(self.step_cfg.step_type == GuideStepType.GirlGuide)
	self.node_list.sprite:CustomSetActive(FunctionGuide.IsSpriteGuideType(self.step_cfg.step_type))
	if FunctionGuide.IsSpriteGuideType(self.step_cfg.step_type) and not self.anim_model then
		self.anim_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["sprite"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}
		
		self.anim_model:SetRenderTexUI3DModel(display_data)
		self.anim_model:SetMainAsset(ResPath.GetNpcModel(6021002))
	end
	if not self.has_model_do_action and FunctionGuide.IsSpriteGuideType(self.step_cfg.step_type) and self.anim_model then
		self.has_model_do_action = true
		if self.step_cfg.step_type == GuideStepType.SpriteAnger then
			self.anim_model:SetTrigger("attack1")
		elseif self.step_cfg.step_type == GuideStepType.SpriteClap then
			self.anim_model:SetTrigger("attack2")
		elseif self.step_cfg.step_type == GuideStepType.SpriteExpect then
			self.anim_model:SetTrigger("attack3")
		end
	end
	if self.step_cfg.step_type == GuideStepType.TextGuide then
 		text_w = 308
 	end
 	self.node_list.gril_panel.rect.sizeDelta = Vector2(text_w, 125)
end

function NormalGuideView:FlushWeak()
	if (not self.click_rect) or IsNil(self.click_rect) or (not self.step_cfg) then
		return 0, 0
	end

	self:ReSetStrongGuide()
	self.node_list.eff:CustomSetActive(self.step_cfg.is_finger_effect ~= 2 and self.step_cfg.need_rect_effect == 1)
	self.kuang:CustomSetActive(self.step_cfg.is_finger_effect == 2)
	local pos_x, pos_y = self:UpdatePos()
	local strong_guide, guide_x, guide_y = self:GetStrongNodePos(pos_x, pos_y)
	--计算高亮框的位置
	local height = self.left.rect.rect.height
	local width = self.left.rect.rect.width
	--拿到按钮的区域大小
	local click_real_rect = self.click_rect.rect
	local btn_height = click_real_rect.height
	local btn_width = click_real_rect.width
	--记录指引按钮的大小
	self.obj_height = btn_height
	self.obj_width = btn_width
	-- 引导的界面是FHD的，如果引导界面为HD的需要把控件宽高缩放一下计算
	local step_view = ViewManager.Instance:GetView(self.step_cfg.module_name)
	if step_view then
		if step_view.reference_resolution == REFERENCE_RESOLUTION_TYPE.HD then
			self.obj_width = self.obj_width / 0.7
			self.obj_height = self.obj_height / 0.7
		end
	end

	self.obj_width_half = self.obj_width / 2
	self.obj_height_half = self.obj_height / 2
	self.obj_pos_x = pos_x
	self.obj_pos_y = pos_y

	if self.obj_width <= 40 then
		self.obj_width = 40
	end

	if self.obj_height <= 40 then
		self.obj_height = 40
	end

	if strong_guide then
		strong_guide:CustomSetActive(true)
		local panel = strong_guide.transform:FindHard("Panel")
		if panel then
			panel.gameObject:SetActive(self.step_cfg.step_type == GuideStepType.Arrow)
		end

		local des = strong_guide.transform:FindHard("Panel/Des")
		if FunctionGuide.IsGuideStepType(self.step_cfg.step_type) then	-- 设置美女指引
			des = self.girl_arrow.transform:FindHard("Panel/Des")
			self:SetGirlPanel()
		end

		local normal_rect = strong_guide.rect
		normal_rect.localPosition = Vector2(guide_x, guide_y)
		self:SetDec(des)
	end

	self.node_list.eff.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_1.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_2.rect.localPosition = Vector2(pos_x, pos_y)
	self.cur_strong_guide = strong_guide

	--设置框
	self.kuang.rect.localPosition = Vector2(pos_x, pos_y)
	self.kuang.rect.sizeDelta = Vector2(self.obj_width + 10, self.obj_height + 10)

	--设置框
	self.node_list.WeekBtn.rect.localPosition = Vector2(pos_x, pos_y)
	self.node_list.WeekBtn.rect.sizeDelta = Vector2(self.obj_width + 10, self.obj_height + 10)

	-- 五秒之后自动点击
	if self.step_cfg.is_auto == 1 then
		self:AddDelayClickStrongBtnTimer()
	end
end

-- 展示强引导动画
function NormalGuideView:ShowStrongMask(is_not_show_ani)
	if self.is_show_mask then
		return
	end
	local step_view = ViewManager.Instance:GetView(self.step_cfg.module_name)
	if not step_view then
		return false
	end

	local strong_mask_type = self.step_cfg.mask_type
	self.is_show_mask = true
	self:RemoveMaskTweenTimer()

	self.strong_mask_3:SetActive(true)
	if strong_mask_type == MASK_TYPE.CIRCLE then
		local target_value = 70
		-- 固定大小
		local time = 0.5
		local finish_fun = function()
			self:UpdatePos()
			self.is_show_mask = false
			local cur_value = target_value

			local pos_x_right = self.obj_pos_x >= 0 and self.obj_pos_x or 0
			local pos_y_right = self.obj_pos_y >= 0 and self.obj_pos_y or 0
			local pos_x_left = self.obj_pos_x < 0 and self.obj_pos_x or 0
			local pos_y_left = self.obj_pos_y < 0 and self.obj_pos_y or 0

			self.img_material:SetVector("_Origin",Vector4(pos_x_right * 2, pos_y_right *2, pos_x_left*2, pos_y_left*2))
			self.img_material:SetFloat("_RoundRadius",cur_value)
			self:UpdateFinalPosScale()
			self:RemoveMaskTweenTimer()
		end

		if is_not_show_ani then
			finish_fun()
		else
			self:RemoveMaskTweenTimer()
			self.mask_tween_timer = GlobalTimerQuest:AddRunQuest(function()
				time = time - UnityEngine.Time.deltaTime
				if time <= 0 then	
					finish_fun()
				else
					-- 一直更新是因为有的控件有动画，坐标会变。
					-- 而且还有可能 引导聚焦结束 控件动画还没完成，所以聚焦结束后还要继续设置最新的坐标
					self:UpdatePos()
					local cur_value = target_value * (time * 30 + 1) 
		
					local pos_x_right = self.obj_pos_x >= 0 and self.obj_pos_x or 0
					local pos_y_right = self.obj_pos_y >= 0 and self.obj_pos_y or 0
					local pos_x_left = self.obj_pos_x < 0 and self.obj_pos_x or 0
					local pos_y_left = self.obj_pos_y < 0 and self.obj_pos_y or 0
		
					self.img_material:SetVector("_Origin",Vector4(pos_x_right * 2, pos_y_right *2, pos_x_left*2, pos_y_left*2))
					self.img_material:SetFloat("_RoundRadius",cur_value)
				end
			end, 0)
		end
	elseif strong_mask_type == MASK_TYPE.RECT then
		local time = 0.5
		local finish_fun = function()
			self:UpdatePos()
			self.is_show_mask = false

			if self.obj_width_half == nil or self.obj_height_half == nil or self.obj_pos_x == nil or self.obj_pos_y == nil then
				return
			end

			local cur_value_width =  self.obj_width_half
			local cur_value_height = self.obj_height_half
			local pos_x_left = self.obj_pos_x - cur_value_width
			local pos_y_left = self.obj_pos_y - cur_value_height
			local pos_x_right = self.obj_pos_x + cur_value_width
			local pos_y_right = self.obj_pos_y + cur_value_height

			-- 设置左下角到右上角
			self.img_material:SetVector("_Origin", Vector4(pos_x_left , pos_y_left , pos_x_right, pos_y_right))
			-- 矩形 圆角默认为10
			self.img_material:SetFloat("_RoundRadius",10)
			self:UpdateFinalPosScale()
			self:RemoveMaskTweenTimer()
		end

		if is_not_show_ani then
			finish_fun()
		else
			self:RemoveMaskTweenTimer()
			self.mask_tween_timer = GlobalTimerQuest:AddRunQuest(function()
				time = time - UnityEngine.Time.deltaTime
				if time <= 0 then
					finish_fun()
				else
					if self.obj_width_half == nil or self.obj_height_half == nil or self.obj_pos_x == nil or self.obj_pos_y == nil then
						return
					end

					-- 一直更新是因为有的控件有动画，坐标会变。
					-- 而且还有可能 引导聚焦结束 控件动画还没完成，所以聚焦结束后还要继续设置最新的坐标
					self:UpdatePos()
					local half_width_value = self.obj_width_half
					local half_height_value = self.obj_height_half
					local cur_value_width = half_width_value * (time * 30 + 1) 
					local cur_value_height = half_height_value * (time * 30 + 1) 
	
					local pos_x_left = self.obj_pos_x - cur_value_width
					local pos_y_left = self.obj_pos_y - cur_value_height
					local pos_x_right = self.obj_pos_x + cur_value_width
					local pos_y_right = self.obj_pos_y + cur_value_height
					
					self.img_material:SetVector("_Origin",Vector4(pos_x_left , pos_y_left , pos_x_right, pos_y_right))
					-- 矩形 圆角默认为10
					self.img_material:SetFloat("_RoundRadius",10)
				end
			end, 0)
		end
	end
end

-- 配置N秒后之后自动点击(默认5秒)
function NormalGuideView:AddDelayClickStrongBtnTimer()
	if not self.delay_click_strong_timer and self.step_cfg then
		local auto_time = self.step_cfg and self.step_cfg.auto_time or 5

		if self.cur_strong_guide then
			local img_line = nil
			local time_line = self.cur_strong_guide.gameObject.Find("time_line")
			if time_line ~= nil then
				img_line = time_line:GetComponent(typeof(UnityEngine.UI.Image))
			end
			self.delay_click_strong_timer = CountDown.Instance:AddCountDown(auto_time, 0.02,
				-- 回调方法
				function(elapse_time, total_time)
					if img_line == nil then
						local time_line = self.cur_strong_guide.gameObject.Find("time_line")
						if time_line then
							img_line = time_line:GetComponent(typeof(UnityEngine.UI.Image))
						end
					end
					if img_line then
						img_line.fillAmount = elapse_time/total_time
					end
				end,
				-- 倒计时完成回调方法
				function()
					self:StrongBlockClick()
				end
			)
		end
	end
end

-- 延迟显示强引导黑幕
function NormalGuideView:AddDelayShowStrongMarkTimer()
	if self.delay_show_strong_mask then
		return
	end
	self.delay_show_strong_mask = GlobalTimerQuest:AddDelayTimer(function()
		self:ShowStrongMask()
		self.delay_show_strong_mask = nil
	end, self.guide_cfg.black_time)
end

-- 进行下一步
function NormalGuideView:DoNext()
	FunctionGuide.Instance:StartNextStep()
end

-- 取消延迟点击强引导按钮的计时器
function NormalGuideView:CancelDelayClickStrongBtnTimer()
	if self.delay_click_strong_timer and CountDown.Instance:HasCountDown(self.delay_click_strong_timer) then
        CountDown.Instance:RemoveCountDown(self.delay_click_strong_timer)
    end
	self.delay_click_strong_timer = nil
end

-- 引导倒计时指引
function NormalGuideView:RemoveDescTimer()
	if self.dec_timer then
		GlobalTimerQuest:CancelQuest(self.dec_timer)
		self.dec_timer = nil
	end
end

-- 黑幕动画
function NormalGuideView:RemoveMaskTweenTimer()
	if self.mask_tween_timer then
		GlobalTimerQuest:CancelQuest(self.mask_tween_timer)
		self.mask_tween_timer = nil
	end
end

-- -- 主界面引导需要等待一会
-- function NormalGuideView:RemoveWaitMainViewTimer()
-- 	if self.wait_main_timer then
-- 		GlobalTimerQuest:CancelQuest(self.wait_main_timer)
-- 		self.wait_main_timer = nil
-- 	end
-- end

function NormalGuideView:ClearOldAudioId()
	self.old_audio_id = 0
end

function NormalGuideView:CancelDelayShowStrongMarkTimer()
	if self.delay_show_strong_mask then
		GlobalTimerQuest:CancelQuest(self.delay_show_strong_mask)
		self.delay_show_strong_mask = nil
	end
end

-----------------------------------------------------------------
-----------------------------------------------------------------
-- 强引导点击按钮其他位置
function NormalGuideView:OtherClick()
	--是否点击任意地方关闭界面
	local is_click_another_close = self.step_cfg.is_rect_effect
	if is_click_another_close == 1 then
		self:StrongBlockClick()
		return
	else
		self:ShowStrongMask()
	end
end

-- 强引导点击按钮
function NormalGuideView:StrongBlockClick()
	self:CancelDelayClickStrongBtnTimer()

	-- 这里是旧逻辑，做个兼容 
	if self.click_trigger_action == nil or self.click_trigger_action ~= 0 then
		if self.click_call_back then
			self.click_call_back()
		else
			-- 走此流程不需要再view中写额外的GetGuideUiCallBack去获取UI节点和回调
			-- BaseViewRender:GetGuideUiCallBack 这里最后会拿到UI节点
			-- 如果没有获取到单独的点击回调，就直接调用UI自己绑定好的点击回调
			if not IsNil(self.click_obj.gameObject) then
				if not IsNil(self.click_obj.button) then
					self.click_obj.button.onClick:Invoke()
				elseif not IsNil(self.click_obj.toggle) then
					if not self.click_obj.toggle.isOn then
						self.click_obj.toggle.isOn = true
					else
						self.click_obj.toggle.onValueChanged:Invoke(true)
					end
				end
			end
		end
	end

	self.click_call_back = nil
	self.node_list.Block:SetActive(true)
	self:Close()
	self:DoNext()
end

-- 弱引导点击按钮
function NormalGuideView:WeekBlockClick()
	self:Close()
	self:DoNext()
end