--二次确认界面，带倒计时
ActivitySecondConfirmationView = ActivitySecondConfirmationView or BaseClass(SafeBaseView)

function ActivitySecondConfirmationView:__init()
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/dialog_ui_prefab", "second_confirmation_view")
end

function ActivitySecondConfirmationView:__delete()
end

function ActivitySecondConfirmationView:CloseCallBack()
	self:ClearCountDown()
end

function ActivitySecondConfirmationView:ReleaseCallBack()

	if nil ~= self.day_count_change_event then
		GlobalEventSystem:UnBind(self.day_count_change_event)
		self.day_count_change_event = nil
	end

	self.content_txt = nil
	self.ok_func = nil
	self.close_func = nil
	self.ok_txt = nil
	self.count_down_time = nil
	self:ClearCountDown()
end

function ActivitySecondConfirmationView:ShowIndexCallBack()
	self:Flush()
end

function ActivitySecondConfirmationView:SetData(content_txt,ok_func,close_func,ok_txt,count_down_time)
	self.content_txt = content_txt
	self.ok_func = ok_func
	self.close_func = close_func
	self.ok_txt = ok_txt
	self.count_down_time = count_down_time
	self:Open()
end

function ActivitySecondConfirmationView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Common.AlertTitile
	-- self:SetSecondView(nil, self.node_list["size"])
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
   -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	self.node_list.btn_cancel.button:AddClickListener(BindTool.Bind(self.OnClickCancel,self))
	self.node_list.btn_OK.button:AddClickListener(BindTool.Bind(self.OnClickOK,self))
	self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.OnClickClose,self))

    self.day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DayCounterChange, self))
end

function ActivitySecondConfirmationView:DayCounterChange(day_counter_id)
	local times = YunbiaoWGData.Instance:GetHusongRemainTimes()
	if times <= 0 then
		self:Close()
	end
end

function ActivitySecondConfirmationView:ClearCountDown()
	if CountDown.Instance:HasCountDown(self.count_down) then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function ActivitySecondConfirmationView:OnFlush()
	self.node_list.rich_dialog.text.text = self.content_txt or ""
	self:ClearCountDown()
	if self.count_down_time and self.count_down_time > 0 then
		self.count_down = CountDown.Instance:AddCountDown(self.count_down_time,1,BindTool.Bind(self.UpdateTime,self),BindTool.Bind(self.CompleteTime,self))
	else
		self.node_list.ok_text.text.text = self.ok_txt or ""
	end
end

function ActivitySecondConfirmationView:UpdateTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if time > 0 then
		self.node_list.ok_text.text.text = string.format("%s(%s)",self.ok_txt or "",time)
	end
end

function ActivitySecondConfirmationView:CompleteTime()
	self:ClearCountDown()
	self:OnClickOK()
end

function ActivitySecondConfirmationView:OnClickCancel()
	if self.close_func then
		self.close_func()
	else
		self:Close()
	end
end

function ActivitySecondConfirmationView:OnClickOK()
	if self.ok_func then
		self.ok_func()
	end
	self:Close()
end

function ActivitySecondConfirmationView:OnClickClose()
	self:Close()
end