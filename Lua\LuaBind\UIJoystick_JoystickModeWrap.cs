﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UIJoystick_JoystickModeWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>gin<PERSON>num(typeof(UIJoystick.JoystickMode));
		<PERSON><PERSON>("Fixed", get_Fixed, null);
		<PERSON><PERSON>("Dynamic", get_Dynamic, null);
		<PERSON><PERSON>("LocalDynamic", get_LocalDynamic, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UIJoystick.JoystickMode>.Check = CheckType;
		StackTraits<UIJoystick.JoystickMode>.Push = Push;
	}

	static void Push(IntPtr L, UIJoystick.JoystickMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UIJoystick.JoystickMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Fixed(IntPtr L)
	{
		ToLua.Push(L, UIJoystick.JoystickMode.Fixed);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Dynamic(IntPtr L)
	{
		ToLua.Push(L, UIJoystick.JoystickMode.Dynamic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LocalDynamic(IntPtr L)
	{
		ToLua.Push(L, UIJoystick.JoystickMode.LocalDynamic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UIJoystick.JoystickMode o = (UIJoystick.JoystickMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

