require("game/mount_lingchong/equip/mount_lingchong_equip_view")
require("game/mount_lingchong/equip/mount_lingchong_equip_bag")
require("game/mount_lingchong/equip/mount_lingchong_equip_strength")
require("game/mount_lingchong/equip/mount_lingchong_equip_shengpin")
require("game/mount_lingchong/equip/mount_lingchong_equip_star")
require("game/mount_lingchong/equip/mount_lingchong_equip_suit")
require("game/mount_lingchong/equip/mount_lingchong_equip_wg_data")
require("game/mount_lingchong/equip/mount_lingchong_equip_quick_sp")
require("game/mount_lingchong/equip/mount_lingchong_equip_batch_select")
require("game/mount_lingchong/equip/mount_lingchong_equip_get_way")
require("game/mount_lingchong/equip/mount_lingchong_equip_resolve")

MountLingchongEquipWGCtrl = MountLingchongEquipWGCtrl or BaseClass(BaseWGCtrl)

function MountLingchongEquipWGCtrl:__init()
	if MountLingchongEquipWGCtrl.Instance then
		ErrorLog("[MountLingchongEquipWGCtrl] attempt to create singleton twice!")
		return
	end

	MountLingchongEquipWGCtrl.Instance = self

    self.lingchong_equip_view = MountLingChongEquipView.New(GuideModuleName.LingChongEquipView)
    self.mount_equip_view = MountLingChongEquipView.New(GuideModuleName.MountEquipView)
    self.huakun_equip_view = MountLingChongEquipView.New(GuideModuleName.HuaKunEquipView)

    self.data = MountLingChongEquipWGData.New()

    self.mount_quick_shengpin_view = MountLingChongEquipQuickSp.New(GuideModuleName.MountEquipQuickSpView)
    self.lingchong_quick_shengpin_view = MountLingChongEquipQuickSp.New(GuideModuleName.LingChongQuickSpView)
    self.huakun_quick_shengpin_view = MountLingChongEquipQuickSp.New(GuideModuleName.HuaKunEquipQuickSpView)
    self.lingchong_batch_select_view = MountLingChongEquipBatchSelect.New()

    self.equip_get_way_view = MountLingChongEquipGetWay.New()
    self:RegisterAllProtocols()

   -- 功能开启监听
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

    --self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_CREATE, BindTool.Bind1(self.SendBaseEquipInfo, self))
end

function MountLingchongEquipWGCtrl:__delete()
	MountLingchongEquipWGCtrl.Instance = nil
	if self.mount_quick_shengpin_view then
		self.mount_quick_shengpin_view:DeleteMe()
		self.mount_quick_shengpin_view = nil
    end
    if self.lingchong_batch_select_view then
		self.lingchong_batch_select_view:DeleteMe()
		self.lingchong_batch_select_view = nil
    end
    if self.lingchong_quick_shengpin_view then
		self.lingchong_quick_shengpin_view:DeleteMe()
		self.lingchong_quick_shengpin_view = nil
    end

    if self.huakun_quick_shengpin_view then
        self.huakun_quick_shengpin_view:DeleteMe()
        self.huakun_quick_shengpin_view = nil
    end
    
    if self.mount_equip_view then
		self.mount_equip_view:DeleteMe()
		self.mount_equip_view = nil
    end
    
    if self.lingchong_equip_view then
		self.lingchong_equip_view:DeleteMe()
		self.lingchong_equip_view = nil
	end
    if self.equip_get_way_view then
		self.equip_get_way_view:DeleteMe()
		self.equip_get_way_view = nil
    end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
    end

    if self.huakun_equip_view then
        self.huakun_equip_view:DeleteMe()
        self.huakun_equip_view = nil
    end

    GlobalEventSystem:UnBind(self.open_fun_change)
end

function MountLingchongEquipWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSMountPetEquipOper)--4590
    self:RegisterProtocol(CSMountPetEquipComposeReq)--4556
    self:RegisterProtocol(CSMountPetEquipNingPinOper)--4558
    
    self:RegisterProtocol(SCMountPetEquipSinglePartInfo,"OnSCMountPetEquipSinglePartInfo")--4591
    self:RegisterProtocol(SCMountPetEquipBagInfo, "OnSCMountPetEquipBagInfo")--4592

    self:RegisterProtocol(SCMountPetEquipAllPartInfo, "OnSCMountPetEquipAllPartInfo")--4593
    self:RegisterProtocol(SCMountPetEquipAddItemToBag, "OnSCMountPetEquipAddItemToBag")--4594
    self:RegisterProtocol(SCMountPetEquipSuitInfo, "OnSCMountPetEquipSuitInfo")--4595
    self:RegisterProtocol(SCMountPetEquipComposeSuc, "OnSCMountPetEquipComposeSuc")--4557
    self:RegisterProtocol(SCMountPetEquipNingPinInfo, "OnSCMountPetEquipNingPinInfo")--4559
end

function MountLingchongEquipWGCtrl:OnSCMountPetEquipComposeSuc(protocol)
    --SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.UpSuccess)
    MountLingChongEquipWGData.Instance:SaveShengpinSelectList({})
    local view_quick = self:GetQuickShengPinViewByEquipType(protocol.type)
    local view = self:GetViewByEquipType(protocol.type)
    if view_quick:IsOpen() and view_quick:GetIsClickSure() then
        view_quick:PlayShengPinSuccess()
    elseif view:IsOpen() then
        view:PlayShengPinSuccess()
        view:FlushQualityStuffItem()
    end
end

function MountLingchongEquipWGCtrl:OnSCMountPetEquipAddItemToBag(protocol)
    local put_reason = protocol.reason_type
    local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.item_id)
    if item_cfg then
        local name_str = ToColorStr(item_cfg.name,  ITEM_COLOR[item_cfg.color])
        local str = string.format(Language.SysRemind.AddItem, name_str, protocol.num)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
    end
end

function MountLingchongEquipWGCtrl:SendMountPetEquipReq(show_type, oper_type, param1, param2)
    --print_error("发送请求",show_type, oper_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMountPetEquipOper)
    protocol.type = show_type or 0
    protocol.oper_type = oper_type or 0
    protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function MountLingchongEquipWGCtrl:SendMountPetEquipShengPinReq(show_type, compose_equip_id, puton_equip_part_index, consume_grid_index_list)
    --print_error("发送升品请求", show_type, compose_equip_id, puton_equip_part_index, consume_grid_index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMountPetEquipComposeReq)
    protocol.type = show_type or 0
    protocol.compose_equip_id = compose_equip_id or 0
    protocol.puton_equip_part_index = puton_equip_part_index or 0
    protocol.consume_grid_index_list = consume_grid_index_list
	protocol:EncodeAndSend()
end

function MountLingchongEquipWGCtrl:SendMountPetEquipNingPinReq(show_type, select_equip_part_index, resolve_grid_index_list)
    --print_error("发送凝品请求", show_type, puton_equip_part_index, resolve_grid_index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMountPetEquipNingPinOper)
    protocol.type = show_type or 0
    protocol.select_equip_part_index = select_equip_part_index or 0
    protocol.resolve_grid_index_list = resolve_grid_index_list
	protocol:EncodeAndSend()
end

function MountLingchongEquipWGCtrl:OnSCMountPetEquipAllPartInfo(protocol)
    self.data:SaveEquipListInfo(protocol)
    --print_error("OnSCMountPetEquipAllPartInfo",protocol)
    local view = self:GetViewByEquipType(protocol.type)
    view:Flush()
    view:SetMaskBtnsActive()
    if protocol.ret_flag == MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_ONE_KEY_STRENGTH then --一键强化成功
        view:PlayEquipStrengthEffect()
    end
    ViewManager.Instance:FlushView(GuideModuleName.MountLingChongView, nil, "mount_pet_equip")
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Bag)
    RemindManager.Instance:Fire(RemindName.MountEquip_Bag)
    RemindManager.Instance:Fire(RemindName.MountEquip_Shengpin)
    RemindManager.Instance:Fire(RemindName.MountEquip_Resolve)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Resolve)
    RemindManager.Instance:Fire(RemindName.MountEquip_Resolve)
    RemindManager.Instance:Fire(RemindName.MountEquip_Star)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Star)
    RemindManager.Instance:Fire(RemindName.MountEquip_Strength)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Strength)
    RemindManager.Instance:Fire(RemindName.MountEquip_Suit)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Suit)

    --化鲲装备红点刷新
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Bag)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Strength)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Shengpin)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Star)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Suit)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Resolve)

    MountLingChongEquipWGData.Instance:FlushMainInvateTip(protocol.type)
end

function MountLingchongEquipWGCtrl:OnSCMountPetEquipBagInfo(protocol)
    self.data:SaveEquipBagInfo(protocol)
    --print_error("OnSCMountPetEquipBagInfo",protocol)
    local view = self:GetViewByEquipType(protocol.type)
    view:Flush()

    local quick_sp_view = self:GetQuickShengPinViewByEquipType(protocol.type)
    if quick_sp_view:IsOpen() then
        quick_sp_view:Flush(0, "from_protocol")
    end
    if self.lingchong_batch_select_view:IsOpen() then
        self.lingchong_batch_select_view:Flush()
    end
    ViewManager.Instance:FlushView(GuideModuleName.MountLingChongView, nil, "mount_pet_equip")
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Bag)
    RemindManager.Instance:Fire(RemindName.MountEquip_Bag)
    RemindManager.Instance:Fire(RemindName.MountEquip_Shengpin)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Shengpin)

    RemindManager.Instance:Fire(RemindName.MountEquip_Resolve)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Resolve)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Resolve)
    --化鲲装备红点刷新
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Bag)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Shengpin)

    MountLingChongEquipWGData.Instance:FlushMainInvateTip(protocol.type)
end

function MountLingchongEquipWGCtrl:OnSCMountPetEquipSinglePartInfo(protocol)
    self.data:SaveEquipInfo(protocol)
    --print_error("OnSCMountPetEquipSinglePartInfo",protocol)
    local view = self:GetViewByEquipType(protocol.type)
    view:Flush()
    view:SetMaskBtnsActive()
    if protocol.ret_flag == 3 and view:IsAutoStrength() then --成功强化
        view:AutoStrengthOnce()
    end
    ViewManager.Instance:FlushView(GuideModuleName.MountLingChongView, nil, "mount_pet_equip")
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Bag)
    RemindManager.Instance:Fire(RemindName.MountEquip_Bag)
    RemindManager.Instance:Fire(RemindName.MountEquip_Shengpin)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Shengpin)
    RemindManager.Instance:Fire(RemindName.MountEquip_Star)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Star)
    RemindManager.Instance:Fire(RemindName.MountEquip_Strength)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Strength)
    RemindManager.Instance:Fire(RemindName.MountEquip_Suit)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Suit)

    --化鲲装备红点刷新
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Bag)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Strength)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Shengpin)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Star)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Suit)

    MountLingChongEquipWGData.Instance:FlushMainInvateTip(protocol.type)
end

function MountLingchongEquipWGCtrl:OnSCMountPetEquipSuitInfo(protocol)
    --print_error("OnSCMountPetEquipSuitInfo",protocol)
    self.data:SaveEquipSuitInfo(protocol)
    local view = self:GetViewByEquipType(protocol.type)
    view:Flush()
    RemindManager.Instance:Fire(RemindName.MountEquip_Suit)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Suit)
    ViewManager.Instance:FlushView(GuideModuleName.MountLingChongView, nil, "mount_pet_equip")

    --化鲲装备红点刷新
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Suit)

    MountLingChongEquipWGData.Instance:FlushMainInvateTip(protocol.type)
end

function MountLingchongEquipWGCtrl:OnSCMountPetEquipNingPinInfo(protocol)
    --print_error("OnSCMountPetEquipNingPinInfo",protocol)
    self.data:SaveEquipResolveInfo(protocol)
    local view = self:GetViewByEquipType(protocol.type)
    view:Flush()
    RemindManager.Instance:Fire(RemindName.MountEquip_Resolve)
    MountLingChongEquipWGData.Instance:FlushMainInvateTip(protocol.type)
    RemindManager.Instance:Fire(RemindName.LingChongEquip_Resolve)
    RemindManager.Instance:Fire(RemindName.MountEquip_Resolve)
    RemindManager.Instance:Fire(RemindName.HuaKunEquip_Resolve)
end

function MountLingchongEquipWGCtrl:OpenQuickSpView(show_type)
    local quick_view = self:GetQuickShengPinViewByEquipType(show_type)
    if quick_view then
        quick_view:Open()
    end
end

function MountLingchongEquipWGCtrl:OpenBatchShengPin(item_id)
    self.lingchong_batch_select_view:SetData(item_id)
    self.lingchong_batch_select_view:Open()
end

function MountLingchongEquipWGCtrl:OpenGetWayView(show_type, pos)
    self.equip_get_way_view:SetData(show_type, pos)
    self.equip_get_way_view:Open()
end

function MountLingchongEquipWGCtrl:OpenBeastGetWayView(pos)
    self.equip_get_way_view:SetBeastData(pos)
    self.equip_get_way_view:Open()
end

function MountLingchongEquipWGCtrl:CloseGetWayViewAndBatchView()
    self.lingchong_batch_select_view:Close()
    self.equip_get_way_view:Close()
end

function MountLingchongEquipWGCtrl:SendBaseEquipInfo()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local other_cfg = self.data:GetOtherCfg()
    if role_lv >= other_cfg.mount_opne_level then
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(MOUNT_PET_EQUIP_TYPE.MOUNT, MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_SEND_ALL_INFO) --请求所有信息
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(MOUNT_PET_EQUIP_TYPE.MOUNT, MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_SUIT_INFO) --请求套装信息
    end
   
    if role_lv >= other_cfg.pet_open_level then
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(MOUNT_PET_EQUIP_TYPE.PET, MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_SEND_ALL_INFO) --请求所有信息
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(MOUNT_PET_EQUIP_TYPE.PET, MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_SUIT_INFO) --请求套装信息
    end

    if role_lv >= other_cfg.kun_open_level then
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(MOUNT_PET_EQUIP_TYPE.HUAKUN, MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_SEND_ALL_INFO) --请求所有信息
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(MOUNT_PET_EQUIP_TYPE.HUAKUN, MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_SUIT_INFO) --请求套装信息
    end
end

--功能开启，请求一下信息
function MountLingchongEquipWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
	if self.data:GetIsFun(fun_name) and is_open then
		self:SendBaseEquipInfo()
	end
end

function MountLingchongEquipWGCtrl:GetViewByEquipType(e_type)
    local view = self.lingchong_equip_view--默认返回灵宠装备界面
    if e_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
        view = self.mount_equip_view
    elseif e_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        view = self.huakun_equip_view
    end
    return view
end

function MountLingchongEquipWGCtrl:GetQuickShengPinViewByEquipType(e_type)
    local view = self.lingchong_quick_shengpin_view--默认返回灵宠快速升品界面
    if e_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
        view = self.mount_quick_shengpin_view
    elseif e_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        view = self.huakun_quick_shengpin_view
    end
    return view
end


-- MOUNT_PET_EQUIP_OPERA_TYPE = {
--     MOUNT_PET_EQUIP_OPER_TYPE_PUT_ON = 1,					-- 穿戴装备
-- 	MOUNT_PET_EQUIP_OPER_TYPE_UP_STAR = 2,					-- 升星
-- 	MOUNT_PET_EQUIP_OPER_TYPE_STRENGTHEN = 3,				-- 强化
-- 	MOUNT_PET_EQUIP_OPER_TYPE_SEND_SINGLE_PART_INFO = 4,	-- 单个部位信息。
-- }