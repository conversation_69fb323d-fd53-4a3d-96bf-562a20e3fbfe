BootyBayWGData = BootyBayWGData or BaseClass()
function BootyBayWGData:__init()
	if BootyBayWGData.Instance then
		print_error("[BootyBayWGData] Attempt to create singleton twice!")
		return
	end
	BootyBayWGData.Instance = self
	local bootybay_cfg = ConfigManager.Instance:GetAutoConfig("wabaoconfig_auto")
	self.bootybay_other_cfg = bootybay_cfg.other[1]

	self.bootybay_wabao_pos_cfg = bootybay_cfg.wabao_pos[1]
	self.bootybay_stuff_cfg = bootybay_cfg.wabao_color
	self.bootybay_event_cfg = bootybay_cfg.wabao_event
	self.treasure_item_list = {}
	self.use_baotu_info = {}
	self.bootybay_team_info = {}
	self.wabao_type = 0
	self.boss_count = 0
	self.remain_task_wabao_times = 0
	self.has_accept_wabao_task = 0
	self.person_kill_boss_num = 0
	self.team_kill_boss_num = 0
	self.kill_normal_monster_num = 0
end

function BootyBayWGData:__delete()
	BootyBayWGData.Instance = nil
end

function BootyBayWGData:GetOtherConfig()
	return self.bootybay_other_cfg
end

function BootyBayWGData:GetRewardList()
	return self.bootybay_other_cfg.reward_item
end

function BootyBayWGData:GetRewaredListData()
	if not self.rewared_gailv_data then
		local all_rewared = self.bootybay_other_cfg.reward_item
		local gailv_rewared = self.bootybay_other_cfg.gailv
		local gailv_table = Split(gailv_rewared,"|")
		self.rewared_gailv_data = {}
		for k,v in pairs(all_rewared) do
			self.rewared_gailv_data[k+1] = {}
			self.rewared_gailv_data[k+1].item_id = v.item_id
			self.rewared_gailv_data[k+1].num = v.num
			self.rewared_gailv_data[k+1].is_bind = v.is_bind
			self.rewared_gailv_data[k+1].is_three_must_drop = false
			for t,q in pairs(gailv_table) do
				if tonumber(v.item_id) == tonumber(q) then
					self.rewared_gailv_data[k+1].is_three_must_drop = true
					break
				end
			end
		end
	end
	return self.rewared_gailv_data
end

function BootyBayWGData:GetBootyBaySceneId()
	return self.bootybay_wabao_pos_cfg.scene_id
end

function BootyBayWGData:GetBootyBayStuffList()
	local stuff_list = self.bootybay_stuff_cfg
	SortTools.SortDesc(stuff_list,"color")
	return stuff_list
end

function BootyBayWGData:SetWaBaoPosInfo(protocol)
	self.treasure_item_list = {}
	self.treasure_item_list = protocol.treasure_item_list
end

function BootyBayWGData:GetWaBaoPosInfo()
	return self.treasure_item_list
end

function BootyBayWGData:GetWaBaoEventById(event_id)
	local bootybay_event_cfg = self.bootybay_event_cfg

	for k,v in pairs(bootybay_event_cfg) do
		if v.event_id == event_id then
			return v
		end
	end
end

function BootyBayWGData:SetSCWabaoInfo(protocol)
	self.has_accept_wabao_task = protocol.has_accept_wabao_task
	self.enter_teamfb_times = protocol.enter_teamfb_times
end

function BootyBayWGData:GetEnterTeamFBTimes()
	return self.enter_teamfb_times
end

function BootyBayWGData:GetSCWabaoInfo()
	return self.has_accept_wabao_task
end

function BootyBayWGData:GetIsAcceptWaBaoTask()
	return self.has_accept_wabao_task == 1
end

--获取最近一次手动使用的宝图信息
function BootyBayWGData:SetUseBaoTuInfo(info)
	self.use_baotu_info = info
end

function BootyBayWGData:ClearUseBaoTuInfo()
	self.use_baotu_info = {}
end


function BootyBayWGData:GetUseBaoTuInfo()
	return self.use_baotu_info
end

function BootyBayWGData:SetWaBaoType(type)
	self.wabao_type = type
end

function BootyBayWGData:SetDailyWaBaoRemainCount(times)
	self.remain_task_wabao_times = times
end

function BootyBayWGData:GettDailyWaBaoRemainCount()
	return self.remain_task_wabao_times
end

function BootyBayWGData:GetBaoTuCfgById(item_id)
	local cfg = self.bootybay_stuff_cfg
	for k,v in pairs(cfg) do
		if v.baotu_item == item_id then
			return v
		end
	end
end

function BootyBayWGData:GetIsShowAgainBtn()
    local wabao_type = self.wabao_type
    if wabao_type == WABAO_TYPE.WABAO_TYPE_BAOTU then
        local item_data = self:GetContinueDataCfg()
        if item_data then
            --local use_baotu_info = self:GetUseBaoTuInfo()
            local num = ItemWGData.Instance:GetItemNumInBagById(item_data.baotu_item)
            if num > 0 and Scene.Instance:GetSceneType() == SceneType.BOOTYBAY_FB then 
                return true
            end
        end
    end
    return false
end


function BootyBayWGData:GetContinueDataCfg()
    local data_list = self:GetBootyBayStuffList()
    local cc_need_color = self.bootybay_other_cfg.cc_need_color  --橙色的才有再来一次
    for k, v in pairs(data_list) do
        if v.color == cc_need_color then
            return v
        end
    end
end

function BootyBayWGData:SetPersonFBInfo(protocol)
	self.person_kill_boss_num = protocol.kill_boss_num or 0
end

function BootyBayWGData:SetTeamFBInfo(protocol)
	self.team_kill_boss_num = protocol.kill_boss_num or 0
	self.kill_normal_monster_num = protocol.kill_normal_monster_num or 0

end

function BootyBayWGData:GetPersonKillBossNum()
	return self.person_kill_boss_num
end

function BootyBayWGData:GetTeamKillBossNum()
	return self.team_kill_boss_num
end

function BootyBayWGData:GetTeamCurMonsterCount()
	return self.kill_normal_monster_num
end

function BootyBayWGData:SetBootybayTeamInfo(protocol)
	self.bootybay_team_info.notify_reason = protocol.notify_reason
	self.bootybay_team_info.wabao_uid = protocol.wabao_uid
	self.bootybay_team_info.wabao_role_name =  protocol.wabao_role_name
	self.bootybay_team_info.cound_down_timestamp  =  protocol.cound_down_timestamp
	self.bootybay_team_info.confirm_item_list =  protocol.confirm_item_list

end

function BootyBayWGData:GetBootybayTeamInfo()
	return self.bootybay_team_info
end

function BootyBayWGData:SetNowWabaoInfo(data)
	self.now_wabao_info = data
end

function BootyBayWGData:GetNowWabaoInfo()
	return self.now_wabao_info or {}
end

function BootyBayWGData:SetTempWaBaoInfo(data)
	self.wabao_temp_info = data
end

function BootyBayWGData:GetTempWaBaoInfo()
	return self.wabao_temp_info
end
