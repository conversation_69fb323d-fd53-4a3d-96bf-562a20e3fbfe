EquipmentSuitTipView = EquipmentSuitTipView or BaseClass(SafeBaseView)

function EquipmentSuitTipView:__init()
    self:SetMaskBg()
    self:LoadConfig()
end

function EquipmentSuitTipView:__delete()
end

function EquipmentSuitTipView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/equipment_suit_ui_prefab", "layout_tz_ronglian_tip")
end

function EquipmentSuitTipView:ReleaseCallBack()
    self.equip_part = nil
end

function EquipmentSuitTipView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Equip.TiShi
    self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClickCanel, self))
    self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSure, self))

    self:Flush()
end

function EquipmentSuitTipView:OnFlush()
    self.node_list["content"].text.text = Language.Equip.EquipSuitTip
end

function EquipmentSuitTipView:OnClickCanel()
    self:Close()
end

function EquipmentSuitTipView:OnClickSure()
    if not self.equip_part then
        return
    end
    if self.ok_func then
        self.ok_func()
    end
    
    self:Close()
end

function EquipmentSuitTipView:SetCurEquipPart(equip_part, content_type, ok_func, equip_index)
    self.equip_part = equip_part
    self.content_type = content_type
    self.ok_func = ok_func
    self.equip_index = equip_index
end