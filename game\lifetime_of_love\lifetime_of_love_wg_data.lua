LifeTimeOfLoveWGData = LifeTimeOfLoveWGData or BaseClass()

function LifeTimeOfLoveWGData:__init()
	if LifeTimeOfLoveWGData.Instance then
		error("[LifeTimeOfLoveWGData] Attempt to create singleton twice!")
		return
	end

    LifeTimeOfLoveWGData.Instance = self
    self:InitParam()
    self:InitConfig()
    RemindManager.Instance:Register(RemindName.LifeTimeOfLove, BindTool.Bind(self.GetRemind, self))
end

function LifeTimeOfLoveWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.LifeTimeOfLove)
    LifeTimeOfLoveWGData.Instance = nil
end

function LifeTimeOfLoveWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("a_lifelong_love_cfg_auto")
    self.lifelong_love_task_list = ListToMapList(cfg.task, "grade", "reward_type")
    self.lifelong_love_task = ListToMap(cfg.task, "seq")
    self.lifelong_love_task_show = ListToMap(cfg.task_show, "grade", "task_type")
end

function LifeTimeOfLoveWGData:InitParam()
    self.grade = -1
    self.big_reward_flag = {}
    self.task_progress = {}
    self.task_reward_count = {}
end

function LifeTimeOfLoveWGData:SetAllInfo(protocol)
    self.grade = protocol.grade
    self.big_reward_flag = bit:d2b_l2h(protocol.big_reward_flag, nil, true)
    self.task_progress = protocol.task_progress
    self.task_reward_count = protocol.task_reward_count
end

function LifeTimeOfLoveWGData:GetRemind()
    local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_A_LIFELONG_LOVE_TASK)
    if not act_open then
        return 0
    end

    local task_show = self:GetTaskShowCfg()
    for k, v in pairs(task_show) do
        if self:GetTypeRemind(v.task_type) then
            return 1
        end
    end

    return 0
end

function LifeTimeOfLoveWGData:GetTaskListByType(reward_type)
    if self.lifelong_love_task_list[self.grade] then
        return self.lifelong_love_task_list[self.grade][reward_type] or {}
    end

    return {}
end

function LifeTimeOfLoveWGData:GetTaskShowCfg()
    return self.lifelong_love_task_show[self.grade] or {}
end

-- 单个任务能否领取
function LifeTimeOfLoveWGData:GetTaskCanGetBySeq(seq)
    local get_num = 0
    local task_cfg = self.lifelong_love_task[seq]
    local limit_count = task_cfg and task_cfg.get_limit or 0

    local cur_reward_count = self:GetTaskRewardCountBySeq(seq)
    local cur_progress = self.task_progress[seq] or 0
    cur_progress = math.min(cur_progress, limit_count)
    get_num = cur_progress - cur_reward_count

    return get_num > 0, get_num
end

-- 任务奖励领取次数
function LifeTimeOfLoveWGData:GetTaskRewardCountBySeq(seq)
    return self.task_reward_count[seq] or 0
end

-- 大奖能否领取
function LifeTimeOfLoveWGData:GetTaskBigRewardCanGetByType(reward_type)
    if IsEmptyTable(self.task_reward_count) then
        return false
    end

    local cfg = self:GetTaskListByType(reward_type)
    if IsEmptyTable(cfg) then
        return false
    end

    for k, v in pairs(cfg) do
        if self:GetTaskRewardCountBySeq(v.seq) == 0 then
            return false
        end
    end

    if self:GetBigRewardFlag(reward_type) then
        return false
    end

    return true
end

-- 大奖领取没
function LifeTimeOfLoveWGData:GetBigRewardFlag(reward_type)
    if IsEmptyTable(self.big_reward_flag) then
        return false
    end

    return self.big_reward_flag[reward_type] > 0
end

-- 单个页签里有无可领物品
function LifeTimeOfLoveWGData:GetTypeRemind(reward_type)
    if IsEmptyTable(self.task_reward_count) or IsEmptyTable(self.task_progress) then
        return false
    end

    if self:GetTaskBigRewardCanGetByType(reward_type) then
        return true
    end

    local cfg = self:GetTaskListByType(reward_type)
    if cfg then
        for k, v in pairs(cfg) do
            if self:GetTaskCanGetBySeq(v.seq) then
                return true
            end
        end
    end

    return false
end