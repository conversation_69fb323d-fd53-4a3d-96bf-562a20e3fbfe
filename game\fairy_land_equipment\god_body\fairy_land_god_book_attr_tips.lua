FairyLandGodBookAttrTips = FairyLandGodBookAttrTips or BaseClass(SafeBaseView)
function FairyLandGodBookAttrTips:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/fairy_land_equipment_ui_prefab", "layout_god_book_attr_tip")
end

function FairyLandGodBookAttrTips:__delete()

end

function FairyLandGodBookAttrTips:ReleaseCallBack()
	if self.attr_list then
		for k, v in pairs(self.attr_list) do
			if v.loaded_flag then
				v.cell:DeleteMe()
			end
		end
		self.attr_list = nil
	end

    self.slot = nil
	self.attr_list_data = nil
end

function FairyLandGodBookAttrTips:LoadCallBack()
    self.attr_list = {}
end

function FairyLandGodBookAttrTips:SetDataAndOpen(slot)
    self.slot = slot
	self.attr_list_data = FairyLandEquipmentWGData.Instance:GetGBAllActAttrList(self.slot)
	if IsEmptyTable(self.attr_list_data) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.AttrTipsLimit)
	else
		self:Open()
	end
end

function FairyLandGodBookAttrTips:OnFlush()
    if IsEmptyTable(self.attr_list_data) then
        return
    end

    local show_list = {}
    local cell_path = "uis/view/fairy_land_equipment_ui_prefab"
	local cell_res = "god_book_attr_cell"
	for i, v in ipairs(self.attr_list_data) do
        show_list[i] = true
		if self.attr_list[i] and self.attr_list[i].loaded_flag then
			self.attr_list[i].cell:SetData(v)
		else
            self.attr_list[i] = {}
            self.attr_list[i].loaded_flag = false
			local async_loader = AllocAsyncLoader(self, "gb_attr_cell" .. i)
			async_loader:SetParent(self.node_list["attr_list"].transform)
			async_loader:Load(cell_path, cell_res, function(obj)
				local cell = GBTipsAttrRender.New(obj)
				cell:SetIndex(i)
				cell:SetData(v)
				self.attr_list[i].cell = cell
                self.attr_list[i].loaded_flag = true
			end)
		end
	end

    for k, v in pairs(self.attr_list) do
        if v.loaded_flag then
            v.cell:SetActive(show_list[k])
        end
    end
end
