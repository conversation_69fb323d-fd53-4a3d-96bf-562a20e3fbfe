EquipmentSelectBaoShiView = EquipmentSelectBaoShiView or BaseClass(SafeBaseView)

function EquipmentSelectBaoShiView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function EquipmentSelectBaoShiView:LoadConfig()
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_select_baoshi")
end

function EquipmentSelectBaoShiView:__delete()

end

function EquipmentSelectBaoShiView:ReleaseCallBack()
	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end
	self.select_equip_list = nil
end

function EquipmentSelectBaoShiView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Equip.SelectBaoShiViewTitle1  --选择镶嵌宝石 文字
	self:CreateEquipBaoShiListView()
end

function EquipmentSelectBaoShiView:ShowIndexCallBack(index)

end

function EquipmentSelectBaoShiView:OpenCallBack()

end

function EquipmentSelectBaoShiView:CloseCallBack()

end

function EquipmentSelectBaoShiView:SetData(list, equip_index, slot_index)
	self.select_equip_list = list
	self.select_equip_index = equip_index
	self.select_slot_index = slot_index
end

function EquipmentSelectBaoShiView:OnFlush()
	self:FlushEquipStrengthListDataSource()
end

-- 创建列表
function EquipmentSelectBaoShiView:CreateEquipBaoShiListView()
	self.stone_list_view = AsyncListView.New(SelectBaoShiItemRender, self.node_list["ph_select_baoshi_list_view"])
	self.stone_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectEquipStrengthItemHandler, self))
end

--设置移除按钮是否显示
function EquipmentSelectBaoShiView:SetRemoveBtnState(boo)
	XUI.SetButtonEnabled(self.node_list.btn_remove, boo)
end

-- 刷列表数据源
function EquipmentSelectBaoShiView:FlushEquipStrengthListDataSource()
	local slot_stone_itemid = EquipmentWGData.Instance:GetBaoShiItemIdBySelectIndex(self.select_equip_index, self.select_slot_index)
	if slot_stone_itemid and slot_stone_itemid > 0 then
		self.node_list.title_view_name.text.text = Language.Equip.SelectBaoShiViewTitle2
	else
		self.node_list.title_view_name.text.text = Language.Equip.SelectBaoShiViewTitle1
	end

	if nil ~= self.stone_list_view then
		self.stone_list_view:SetDataList(self.select_equip_list)
		self.stone_list_view:CancelSelect()
	end
end

-- 选择列表项回调
function EquipmentSelectBaoShiView:OnSelectEquipStrengthItemHandler(item, cell_index, is_default, is_click)
	if not is_click then
		return
	end

	if nil == item or nil == item.data then
		return
	end
	-- local data_instance = EquipmentWGData.Instance

	-- -- 可升级
	-- elseif item.data.to_up_level then
	-- 	local price = data_instance:GetBaoShiUpgradePrice(self.select_equip_index, self.select_slot_index)
	-- 	if price <= 0 then
	-- 		EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.UP, self.select_equip_index, self.select_slot_index)
	-- 	else
	-- 		EquipmentWGCtrl.Instance:BaoShiUpGradeOpen(self.select_equip_index, self.select_slot_index)
	-- 	end
	-- else
	-- 	EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.INLAY, self.select_equip_index, self.select_slot_index, item.data.index)
	-- end
	EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.INLAY, self.select_equip_index, self.select_slot_index, item.data.index)
	self:Close()
end

-----------------------------------------------------------------------------
SelectBaoShiItemRender = SelectBaoShiItemRender or BaseClass(BaseRender)
function SelectBaoShiItemRender:__init()
	self:CreateChild()

	if not self.arrow_tweener then
		self.arrow = self.node_list["img_remind"]
		self.arrow_tweener = self.arrow.gameObject.transform:DOAnchorPosY(0, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function SelectBaoShiItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
	self.node_list["lbl_name"] = nil
end

function SelectBaoShiItemRender:CreateChild()
	self.item_cell = ItemCell.New(self.node_list["ph_item"])

	--XUI.AddClickEventListener(self.view, BindTool.Bind1(self.OnClick, self))
	self:SetRemind(false)
end


function SelectBaoShiItemRender:OnFlush()
	if nil == self.data then
		return
	end

	if self.item_cell then
		self.item_cell:SetData(self.data)
		self.item_cell:SetItemTipFrom(ItemTip.FROM_BAOSHI)
	end

	local data_instance = EquipmentWGData.Instance
	local name_str, attr_str = data_instance:GetBaoShiNatrue(self.data.item_id)
	self.node_list["lbl_name"].text.text = name_str
	self.node_list["lbl_attr"].text.text = attr_str
	self:SetRemind(true)
end

--红点
function SelectBaoShiItemRender:SetRemind(enable)
	self.node_list.img_remind:SetActive(enable)
end
