CalendarWGData = CalendarWGData or BaseClass()

CalendarWGData.OPEN_TYPE = {
	PREPARATION = 0, 				-- 准备中
	OPENNING = 1, 					-- 开启中
	TODAY_OPEN = 2, 				-- 今天将要开启
	TOMORROW_OPEN = 3, 				-- 明天开启
	THIS_WEEK_OPEN = 4, 			-- 本周开启
	NEXT_WEEK_OPEN = 5, 			-- 下周开启
	OTHER = 6, 						-- XX天后开启
}

function CalendarWGData:__init()
	if CalendarWGData.Instance ~= nil then
		ErrorLog("[CalendarWGData] attempt to create singleton twice!")
		return
	end
	CalendarWGData.Instance = self

	self.activity_hall_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("dailywork_auto").activity_hall, "server_open_day", "level_part", "act_seq")

	RemindManager.Instance:Register(RemindName.Calendar, BindTool.Bind(self.GetCalendarRemind, self))

	self.click_close_act = {}

	self.opened = false 
end

function CalendarWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.Calendar)
	CalendarWGData.Instance = nil
	self.click_close_act = nil
end

function CalendarWGData:GetHallCfg(open_serverday)
	local cur_open_day = open_serverday or TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	
	-- 开服天数段
	local cur_open_server_day_stage = math.huge
	for server_open_day, v in pairs(self.activity_hall_cfg) do
		if server_open_day >= cur_open_day and server_open_day < cur_open_server_day_stage then
			cur_open_server_day_stage = server_open_day
		end
	end

	if not self.activity_hall_cfg[cur_open_server_day_stage] then
		return nil
	end

	-- 等级段
	local cur_level_stage = math.huge
	for level_stage, v in pairs(self.activity_hall_cfg[cur_open_server_day_stage]) do
		if level_stage >= role_level and level_stage < cur_level_stage then
			cur_level_stage = level_stage
		end
	end

	return self.activity_hall_cfg[cur_open_server_day_stage][cur_level_stage]
end

function CalendarWGData:GetHallCfgByActType(act_type, open_serverday)
	local act_list = self:GetHallCfg(open_serverday)
	if act_list then
		for _,v in pairs(act_list) do
			if v.act_type == act_type then
				return v
			end
		end
	end
	return nil
end

-- 判断活动是否在所给的开服天数里开启
function CalendarWGData:ActIsOpen(open_server_day, act_seq)
	local role_level = GameVoManager.Instance:GetMainRoleVo().level

	-- 开服天数段
	local cur_open_server_day_stage = math.huge
	for cfg_open_server_day, v in pairs(self.activity_hall_cfg) do
		if cfg_open_server_day >= open_server_day and cfg_open_server_day < cur_open_server_day_stage then
			cur_open_server_day_stage = cfg_open_server_day
		end
	end

	-- 等级段
	local cur_level_stage = math.huge
	local cross_process_cfg = self.activity_hall_cfg[cur_open_server_day_stage]
	if cross_process_cfg then
		for level_stage, v in pairs(cross_process_cfg) do
			if level_stage >= role_level and level_stage < cur_level_stage then
				cur_level_stage = level_stage
			end
		end
	else
		self:CfgError(cur_open_server_day_stage, cur_level_stage, act_seq)
		return false
	end

	local is_open = false
	local cfg = nil
	local level_cfg = self.activity_hall_cfg[cur_open_server_day_stage][cur_level_stage]
	if level_cfg then
		cfg = level_cfg[act_seq]
		if cfg then
			-- 开服第几天强制开启
			if cfg.openserverday_limit ~= 0 and open_server_day <= 3 then
				is_open = cfg.openserverday_limit == open_server_day
			elseif cfg.openserverday_limit ~= 0 then
				is_open = cfg.openserverday_limit <= open_server_day
			else
				local weekday = self:GetWeekday(open_server_day)
		        local open_days = Split(cfg.open_day, ":")
		        for _, open_weekday in ipairs(open_days) do
		        	if weekday == tonumber(open_weekday) then
		        		is_open = true
		        		break
		        	end
		        end
			end
		else
			self:CfgError(cur_open_server_day_stage, cur_level_stage, act_seq)
			return false
		end
	else
		self:CfgError(cur_open_server_day_stage, cur_level_stage, act_seq)
		return false
	end
	return is_open, cfg
end

function CalendarWGData:CfgError(cur_open_server_day_stage, cur_level_stage, act_seq)
	print_error("没有读到 每日必做-活动大厅 的配置，开服天数：", cur_open_server_day_stage, "角色等级阶段：", cur_level_stage, " act_seq:", act_seq)
end

-- 返回true表示处于开服前三天并且活动开启时间在三天之后,则不预告活动
function CalendarWGData:CheckThreeServerDay(act_open_server_day)
	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return  cur_open_day <= 3 and act_open_server_day > 3 
end

-- 清理缓存
function CalendarWGData:ClearCalendarActivityCfg()
	self.calendar_activity_cfg = nil
end

-- 私有：构建活动数据（不含婚宴），返回nil表示本活动本次不展示
function CalendarWGData:_buildCalendarEntry(cfg, server_time, cur_open_day, now_weekday)
    local data = { cfg = cfg, open_type = CalendarWGData.OPEN_TYPE.OTHER }

    -- 今天是否开启（含准备中/进行中/未开始）
    local is_open_today, real_cfg = self:ActIsOpen(cur_open_day, cfg.act_seq)
    if is_open_today and real_cfg then
        local act_time_open_stamp = self:ChangeToStamp(real_cfg.open_time)
        local act_time_close_stamp = self:ChangeToStamp(real_cfg.close_time)
        local act_time_prepare_stamp = act_time_open_stamp - real_cfg.prepare_time

        if server_time > act_time_prepare_stamp and server_time < act_time_open_stamp then
            data.str = Language.Calendar.Str0
            data.sort_key = 0
            data.open_type = CalendarWGData.OPEN_TYPE.PREPARATION
			data.act_time_open_stamp = act_time_open_stamp
            return data
        elseif server_time >= act_time_open_stamp and server_time < act_time_close_stamp then
            data.str = Language.Calendar.Str1
            data.sort_key = act_time_close_stamp - act_time_open_stamp
            data.is_openning = true
            data.open_type = CalendarWGData.OPEN_TYPE.OPENNING
			data.act_time_open_stamp = act_time_open_stamp
            return data
        elseif server_time < act_time_open_stamp then
            data.str = string.format(Language.Calendar.Str2, cfg.open_time)
            data.sort_key = act_time_open_stamp
            data.open_type = CalendarWGData.OPEN_TYPE.TODAY_OPEN
			data.act_time_open_stamp = act_time_open_stamp
            return data
        end
    end

    -- 明天是否开启
    if self:ActIsOpen(cur_open_day + 1, cfg.act_seq) then
        if not self:CheckThreeServerDay(cur_open_day + 1) then
            data.str = Language.Calendar.Str3
            data.sort_key = self:ChangeToStamp(cfg.open_time, 1)
            data.open_type = CalendarWGData.OPEN_TYPE.TOMORROW_OPEN
            return data
        end
    end

    -- 本周是否开启
    for i = 2, 7 - now_weekday do
        if self:ActIsOpen(cur_open_day + i, cfg.act_seq) then
            if not self:CheckThreeServerDay(cur_open_day + i) then
                data.str = string.format(Language.Calendar.Str4, Language.Common.WeekDay[now_weekday + i])
                data.sort_key = self:ChangeToStamp(cfg.open_time, i)
                data.open_type = CalendarWGData.OPEN_TYPE.THIS_WEEK_OPEN
                return data
            end
        end
    end

    -- 下周是否开启
    for i = 1, 7 do
        local delta = i + (7 - now_weekday)
        if self:ActIsOpen(cur_open_day + delta, cfg.act_seq) then
            if not self:CheckThreeServerDay(cur_open_day + delta) then
                data.str = Language.Calendar.Str5
                data.sort_key = self:ChangeToStamp(cfg.open_time, delta)
                data.open_type = CalendarWGData.OPEN_TYPE.NEXT_WEEK_OPEN
                return data
            end
        end
    end

    -- 一个月内，几天后开启（只预告到30天）
    for i = 14 - now_weekday, 30 do
        if self:ActIsOpen(cur_open_day + i, cfg.act_seq) then
            if not self:CheckThreeServerDay(cur_open_day + i) then
                data.str = string.format(Language.Calendar.Str6, i)
                data.sort_key = self:ChangeToStamp(cfg.open_time, i)
                data.open_type = CalendarWGData.OPEN_TYPE.OTHER
                return data
            end
        end
    end

    return nil
end

function CalendarWGData:GetCalendarActivityCfg()
    if self.calendar_activity_cfg then
        return self.calendar_activity_cfg 
    end
    self.calendar_activity_cfg = {}

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local kf_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
    local now_weekday = tonumber(os.date("%w", server_time))
    if now_weekday == 0 then
        now_weekday = 7
    end

    local hall_cfg = self:GetHallCfg()
    if not IsEmptyTable(hall_cfg) then
        for _, cfg in pairs(hall_cfg) do
            if role_level >= cfg.level and cfg.cross_gs_level and cfg.cross_gs_level <= kf_world_level then
                local entry = self:_buildCalendarEntry(cfg, server_time, cur_open_day, now_weekday)
                if entry and entry.str then
                    table.insert(self.calendar_activity_cfg, entry)
                end
            end
        end
    end

    if FunOpen.Instance:GetFunIsOpenedByTabName(FunName.Marry) then
        -- 婚宴排期
        local schedule_list = MarryWGData.Instance:GetWeddingScheduleInfo()
        for _, v in ipairs(schedule_list) do
            local data = {}
            data.is_hunyan = true
            data.cfg = {
                name = Language.Calendar.Hunyan,
                act_seq = 10000 + v.seq,
                hunyan_seq = v.seq,
                res_head = 10000,
                res_name = 10000,
                smallview_des = Language.Calendar.HunyanDesc,
                zjm_zp_btn_name = "a3_zjm_icon_marry_party",
            }
            local _, begin_time, end_time = MarryWGData.Instance:GetShowYuYueTime(v.seq)

            if begin_time ~= "" and end_time ~= "" then
                local begin_time_stamp = self:ChangeToStamp(begin_time)
                local end_time_stamp = self:ChangeToStamp(end_time)

                if server_time >= begin_time_stamp and server_time < end_time_stamp then
                    data.str = Language.Calendar.Str1
                    data.sort_key = 0
                    data.is_openning = true
                    data.open_type = CalendarWGData.OPEN_TYPE.OPENNING
                elseif server_time < begin_time_stamp then
                    data.str = string.format(Language.Calendar.Str2, begin_time)
                    data.sort_key = begin_time_stamp
                    data.open_type = CalendarWGData.OPEN_TYPE.TODAY_OPEN
                end

                if data.str then
                    table.insert(self.calendar_activity_cfg, data)
                end
            end
        end
    end

    table.sort(self.calendar_activity_cfg, SortTools.KeyLowerSorter("sort_key"))
    return self.calendar_activity_cfg
end

-- 检查所给开服天数是否符合活动的开服天数限制（返回true表示活动符合可以开启）
function CalendarWGData:CheckOpenserverDayLimit(cfg, openserverday)
	openserverday = openserverday or TimeWGCtrl.Instance:GetCurOpenServerDay()

	-- 开启天数判断
	if cfg.openserverday_limit ~= nil and cfg.openserverday_limit ~= "" then
		if openserverday < cfg.openserverday_limit then
			return false
		end 
	end

	-- 关闭天数判断
	if cfg.openserverday_end_limit ~= nil and cfg.openserverday_end_limit ~= "" then
		if openserverday >= cfg.openserverday_end_limit then
			return false
		end 
	end

	return true
end

-- 获得根据开服天数强行开启的时间是星期几
function CalendarWGData:GetForceOpenWeekday(cfg)	
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local now_weekday = tonumber(os.date("%w", server_time)) 	-- 今天星期几
	if now_weekday == 0 then
		now_weekday = 7
	end
	local openserverday = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- 到了openserverday_limit配置的天数强行开启活动
	if cfg.openserverday_limit - openserverday >= 0 then
		local force_open_weekday = (now_weekday + cfg.openserverday_limit - openserverday) % 7
		if force_open_weekday == 0 then
			force_open_weekday = 7
		end
		return force_open_weekday
	else
		return -1
	end
end

-- 获得对应开服天数是星期几
function CalendarWGData:GetWeekday(open_server_day)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	local now_weekday = tonumber(os.date("%w", server_time)) 	-- 今天星期几
	if now_weekday == 0 then
		now_weekday = 7
	end

	local result_weekday = (now_weekday + open_server_day - cur_open_day) % 7 
	if result_weekday == 0 then
		result_weekday = 7
	end

	return result_weekday
end

-- 获得所有开启中或今天将要开启的活动
function CalendarWGData:GetCalendarActivityNoticeCfg()
	local cfg = self:GetCalendarActivityCfg()
	local result = {}
	for i,v in ipairs(cfg) do
		if v.open_type == CalendarWGData.OPEN_TYPE.OPENNING or v.open_type == CalendarWGData.OPEN_TYPE.TODAY_OPEN then
			table.insert(result, v)
		end
	end
	return result
end

-- 获得所有正在开启的活动
function CalendarWGData:GetCalendarOpenningActivityCfg()
	local cfg = self:GetCalendarActivityCfg()
	local result = {}
	for i,v in ipairs(cfg) do
		if v.open_type == CalendarWGData.OPEN_TYPE.OPENNING then
			table.insert(result, v)
		end
	end
	return result
end

-- 获得所有未被玩家手动点击关闭的正在开启的活动
function CalendarWGData:GetUnClickOpenningActivityCfg()
	local cfg = self:GetCalendarOpenningActivityCfg()
	local result = {}
	for i,v in ipairs(cfg) do
		if not self.click_close_act[v.cfg.act_seq] and not v.is_hunyan then
			table.insert(result, v)
		end
	end
	return result
end

-- 记录点击了关闭活动提醒面板的活动
function CalendarWGData:SetClickCloseAct(act_seq)
	self.click_close_act[act_seq] = true
end


function CalendarWGData:ChangeToStamp(time_str, add_day)
	add_day = add_day or 0
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_tb = Split(time_str, ":")
	local tab = os.date("*t", server_time)
	tab.day = tab.day + add_day
	tab.hour = tonumber(time_tb[1])
	tab.min = tonumber(time_tb[2])
	tab.sec = 0
	local stamp = os.time(tab) or 0
	return stamp
end

-- 红点
function CalendarWGData:GetCalendarRemind()
	if self.opened then
		return 0
	end

	local cfg = self:GetCalendarActivityCfg()
	local result = 0
	for i,v in ipairs(cfg) do
		if v.open_type == CalendarWGData.OPEN_TYPE.TODAY_OPEN then
			result = result + 1
		end
	end
	return result
end

function CalendarWGData:SetOpened(bool)
	self.opened = bool
end
