KFBossGatherView = KFBossGatherView or BaseClass(SafeBaseView)
function KFBossGatherView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(602, 426)})
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_kf_boss_gather_panel")
end


function KFBossGatherView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.Boss.ShuiJingCaiJiViewName
	for i = 1,3 do
		self.node_list["btn_"..i].button:AddClickListener(BindTool.Bind(self.OnClickGather,self,i))
	end
	XUI.AddClickEventListener(self.node_list["xiezhu_tip_btn"], BindTool.Bind(self.OnClickXieZhuTipBtn, self))
end

function KFBossGatherView:ReleaseCallBack()
	self:CleanRefreshTime()
	self.cur_layer = nil
end

-- 协助
function KFBossGatherView:OnClickXieZhuTipBtn()
   BossXiezhuWGCtrl.Instance:ShowOrHideXieZhuRule()
end

function KFBossGatherView:MainMenuIconChangeEvent(isOn)
	if isOn then
		if self.node_list["root_bg"] then
			self.node_list["root_bg"].canvas_group:DoAlpha(1, 0, 0.8)
			self.node_list["root_bg"].canvas_group.blocksRaycasts = false
		end
	else
		if self.node_list["root_bg"] then
			self.node_list["root_bg"].canvas_group.blocksRaycasts = true
			self.node_list["root_bg"].canvas_group:DoAlpha(0, 1, 0.8)
		end
	end	
end

function KFBossGatherView:OnFlush()
	local scene_id = Scene.Instance:GetSceneId()
	local cur_gather_info = BossWGData.Instance:GetKFNewGatherSceneCfgBySceneId(scene_id)
	if not cur_gather_info then return end
	self.cur_layer = cur_gather_info.layer_index
	local cur_layer_info = BossWGData.Instance:GetNewGatherLayerCfg(cur_gather_info.layer_index)
	local gather_times_tab = BossWGData.Instance:GetNewKFBossGatherInfo()

	local have_time = BossWGData.Instance:GetTreasureGatherTimes()
	local max_time = BossWGData.Instance:GetTreasureGatherMaxTimes()
	local color = have_time > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
	self.node_list["title_text"].text.text = string.format(Language.Boss.ShuiJingCaiJi,color,have_time,max_time)
	for i =1,3 do
		if cur_layer_info[i-1] and cur_layer_info[i-1].gather_id then
			local gather_cfg = Scene.Instance:GetGatherCfgByGatherId(cur_layer_info[i-1].gather_id) 
			local left_times = 1 - (gather_times_tab[i] or 0)
			local color = left_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
			self.node_list["btn_text_"..i].text.text = string.format(Language.Boss.GatherTimeWorld,color,left_times)
		end
	end

	local xiezhu_count = BossXiezhuWGData.Instance:GetAssistCount()
    local xiezhu_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST)
    local color = xiezhu_count - xiezhu_num > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
    self.node_list.xiezhu_cout.text.text =   string.format(Language.Boss.XiezhuCout, color, xiezhu_count - xiezhu_num, xiezhu_count)   
	
	self:RefreshRemainTime()
	if self.refresh_event == nil then
		self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 1)
	end
end

function KFBossGatherView:RefreshRemainTime()
	local gather_info = BossWGData.Instance:GetKFGatherInfo(self.cur_layer)
	if IsEmptyTable(gather_info) then
		return
	end
	
	local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(gather_info.boss_id)
	if boss_server_info == nil then
		boss_server_info = BossWGData.Instance:GetCrossBossById(gather_info.boss_id)
	end

	self.node_list["txt_name"].text.text = gather_info.boss_name .. " "

	local time = 0
	if boss_server_info ~= nil then
		time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
	end

	local state = time > 1
	if state then
		self.node_list["lbl_time"].text.text = TimeUtil.FormatSecond(time) .. Language.Boss.LiveTime
		self.node_list["lbl_state"].text.text = ""
	else
		self.node_list["lbl_time"].text.text = ""
		self.node_list["lbl_state"].text.text = Language.Boss.YiShuaxin
		self:CleanRefreshTime()
	end
end

function KFBossGatherView:CleanRefreshTime()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
end

function KFBossGatherView:OnClickGather(index)
	local gather_times_tab = BossWGData.Instance:GetNewKFBossGatherInfo()
	local left_times = 1 - (gather_times_tab[index] or 0)
	if left_times > 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.GatherTips)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.NoGatherTime)
	end
end