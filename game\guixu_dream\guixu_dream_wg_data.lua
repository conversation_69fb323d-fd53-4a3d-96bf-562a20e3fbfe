GuiXuDreamWGData = GuiXuDreamWGData or BaseClass()

function GuiXuDreamWGData:__init()
	if GuiXuDreamWGData.Instance ~= nil then
		ErrorLog("[GuiXuDreamWGData] attempt to create singleton twice!")
		return
	end

	GuiXuDreamWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("wardrobe_cfg_auto")
	self.suit_skill_map_cfg = ListToMap(cfg.wardrobeskill, "seq")
	RemindManager.Instance:Register(RemindName.GuiXuDreamView, BindTool.Bind(self.GetTotalRemindAndSuit, self))
end

function GuiXuDreamWGData:__delete()
	GuiXuDreamWGData.Instance = nil
end

function GuiXuDreamWGData:GetSkillBySeq(seq)
	return self.suit_skill_map_cfg[seq] or {}
end

-- 展示列表
function GuiXuDreamWGData:UpdateSuitShowList()
	self.item_show_list = {}

	local all_list = WardrobeWGData.Instance:GetSuitAllList()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if all_list then
		for suit, suit_data in pairs(all_list) do
			local is_show = WardrobeWGData.Instance:IsShowSuitList(suit)
			if server_day >= suit_data.open_day or is_show then
				if suit_data.theme_type == WARDROBE_THEME_TYPE.GUIXUDREAM then
					self.item_show_list[#self.item_show_list + 1] = suit_data
				end
			end
		end
	end

	if not IsEmptyTable(self.item_show_list) then
		table.sort(self.item_show_list, SortTools.KeyLowerSorter("sort_index"))
	end
end

function GuiXuDreamWGData:GetSuitShowList()
	-- if not self.item_show_list or #self.item_show_list <= 0 then
	-- 	self:UpdateSuitShowList()
	-- end
	self:UpdateSuitShowList()
	return self.item_show_list
end

function GuiXuDreamWGData:GetSuitShowSplitList(change_type, cur_select_index)
	cur_select_index = (cur_select_index and cur_select_index > -1) and cur_select_index or 1
	if (not change_type) or (not self.item_show_list) then
		return nil, (cur_select_index or 1), false
	end

	local return_table = {}
	local jump_index
	local is_red = false

	for _, item_data in ipairs(self.item_show_list) do
		if item_data and item_data.suit_type == change_type then
			table.insert(return_table, item_data)
		end
	end

	for index, item_data in ipairs(return_table) do
		if item_data and item_data.can_act then
			if index == cur_select_index then
				jump_index = index
				is_red = true
				break
			elseif not jump_index then
				jump_index = index
				is_red = true
			end
		end
	end

	return return_table, (jump_index or cur_select_index), is_red
end

function GuiXuDreamWGData:GetTotalRemindAndSuit()
	local cfg = self:GetSuitShowList()
	if IsEmptyTable(cfg) then
		return 0, 1, 1
	end

	if not FunOpen.Instance:GetFunIsOpened(FunName.GuiXuDreamView) then
		return 0, 1, 1
	end

	local length = #cfg
	for i = 1, length do
		if cfg[i].can_act then
			return 1, cfg[i].suit, cfg[i].suit_type
		end
	end

	return 0, 1, 1
end

function GuiXuDreamWGData:GetGuiXuDreamSuitInfoBySuit(suit)
	self:GetSuitShowList()

	local show_suit_info = {}
	for i, v in ipairs(self.item_show_list) do
		if v.suit == suit then
			show_suit_info = v
			break
		end
	end

	return show_suit_info
end


function GuiXuDreamWGData:IsGuiXuDreamSkillAct(suit)
	local info = {}
	local num = 0
	for i, v in ipairs(self.item_show_list) do
		if v.suit == suit then
			info = v.part_list
			local len = #info
			for k1,v1 in pairs(info) do
				if v1.state ~= REWARD_STATE_TYPE.UNDONE then
					num = num + 1
				end
			end

			return num >= len
		end
	end

	return false
end