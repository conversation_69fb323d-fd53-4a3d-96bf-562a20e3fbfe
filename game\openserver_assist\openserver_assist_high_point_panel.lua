function OpenServerAssistView:InitHighPoint()
    self.hp_task_list = AsyncListView.New(HighPointTaskRender, self.node_list["hp_task_list"])
    self.hp_reward_list = AsyncListView.New(HighPointRwardRender, self.node_list["hp_reward_list"])

    self:InitHighPointActInfo()
end

function OpenServerAssistView:ReleaseHighPoint()
    if self.hp_task_list then
        self.hp_task_list:DeleteMe()
        self.hp_task_list = nil
    end

    if self.hp_reward_list then
        self.hp_reward_list:DeleteMe()
        self.hp_reward_list = nil
    end

    if CountDownManager.Instance:HasCountDown("high_point_act_time") then
        CountDownManager.Instance:RemoveCountDown("high_point_act_time")
    end
end

function OpenServerAssistView:InitHighPointActInfo()
    local all_cfg = OpenServerAssistWGData.Instance:GetHighPointAllCfg()
    local other_cfg = all_cfg and all_cfg.other and all_cfg.other[1]

    if other_cfg then
        self.node_list.hp_act_desc.text.text = other_cfg.palay_desc or ""
    end

    local act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT
    local res_time = ActivityWGData.Instance:GetActivityResidueTime(act_type)
    self.node_list.hp_act_time.text.text = TimeUtil.FormatSecondDHM2(res_time)
    if res_time > 0 then
        CountDownManager.Instance:AddCountDown("high_point_act_time", BindTool.Bind(self.UpdateHighPointCountDown, self),
                                                nil, nil, res_time, 1)
    end

    -- 当日提醒红点
    local get_today_remind = OpenServerAssistWGData.Instance:GetActivityTodayRemind(act_type)
    if get_today_remind == 0 then
        OpenServerAssistWGData.Instance:SetActivityTodayRemind(act_type)
        RemindManager.Instance:Fire(RemindName.OSA_High_Point)
    end
end

function OpenServerAssistView:UpdateHighPointCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 and self.node_list.hp_act_time then
        self.node_list.hp_act_time.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function OpenServerAssistView:FlushHighPointPanel()
    local cur_hp = OpenServerAssistWGData.Instance:GetHighPointCount()
    self.node_list.hp_cur_value.text.text = string.format(Language.Activity.HighPointCurValue, cur_hp)
    self:FlushHPRewardList()
    self:FlushHPTaskList()
end

function OpenServerAssistView:FlushHPRewardList()
    local reward_list = OpenServerAssistWGData.Instance:GetHighPointRewardList()
    self.hp_reward_list:SetDataList(reward_list)
end

function OpenServerAssistView:FlushHPTaskList()
    local task_list = OpenServerAssistWGData.Instance:GetHighPointTaskList()
    self.hp_task_list:SetDataList(task_list)
end

------------------------  HighPointTaskRender -----------------------------------
HighPointTaskRender = HighPointTaskRender or BaseClass(BaseRender)
function HighPointTaskRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_jump_view"], BindTool.Bind(self.OnClickJumpView, self))
end

function HighPointTaskRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local task_cfg = self.data.task_cfg
    local task_desc = string.format(task_cfg.task_desc, CommonDataManager.GetDaXie(task_cfg.param))
    self.node_list.task_desc.text.text = string.format(Language.Activity.HighPointTaskDesc, task_desc, task_cfg.once_haidian_count)
    self.node_list.task_num.text.text =  self.data.finish_count .. "/" .. task_cfg.times_limit
    self.node_list.task_icon.image:LoadSpriteAsync(ResPath.GetF2MainUIImage("act_" .. task_cfg.task_icon))
    self.node_list.is_finish:SetActive(self.data.is_finish)
    self.node_list.btn_jump_view:SetActive(not self.data.is_finish)
end

function HighPointTaskRender:OnClickJumpView()
    if IsEmptyTable(self.data) then
        return
    end

    local task_cfg = self.data.task_cfg
    if task_cfg.task_type == 9 and task_cfg.jump_view == "husong" then --护送美人
		if NewTeamWGData.Instance:GetIsMatching() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
			return
		end

		if YunbiaoWGData.Instance:GetIsHuShong() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
        	return
		end

		TaskGuide.Instance:CanAutoAllTask(false)
		ViewManager.Instance:Close(GuideModuleName.OpenServerAssistView)
		ActIvityHallWGCtrl.Instance:DoHuSong()
		return
    else
        if task_cfg.activity_id > 0 then
            local is_open = ActivityWGData.Instance:GetActivityIsOpen(task_cfg.activity_id)
            if not is_open then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
        end

        FunOpen.Instance:OpenViewNameByCfg(task_cfg.jump_view)
    end
end

function HighPointTaskRender:PalyHighPointTaskItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "high_point_task_item_" .. wait_index)
end
---------------------------------------------------------------------------------



------------------------  HighPointRwardRender ----------------------------------
HighPointRwardRender = HighPointRwardRender or BaseClass(BaseRender)
function HighPointRwardRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_to_get"], BindTool.Bind(self.OnClickGetReward, self))
end

function HighPointRwardRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
    end
end

function HighPointRwardRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function HighPointRwardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.need_desc.text.text = string.format(Language.Activity.HighPointNeedDesc, self.data.need_hp)
    self.node_list.is_get:SetActive(self.data.get_state == REWARD_STATE_TYPE.FINISH)
    self.node_list.is_undone:SetActive(self.data.get_state == REWARD_STATE_TYPE.UNDONE)
    self.node_list.btn_to_get:SetActive(self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.btn_to_get_remind:SetActive(self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH)
    self.reward_list:SetDataList(self.data.reward_list)
end

function HighPointRwardRender:OnClickGetReward()
    if IsEmptyTable(self.data) then
        return
    end

    OpenServerAssistWGCtrl.Instance:SendRandActivityRequest(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT,
                                    LOGIN_LUXURY_GIFT_OP.FETCH_REWARD, self.data.reward_grade)
end

function HighPointRwardRender:PalyHighPointRwardItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.MoveAlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "high_point_reward_item_" .. wait_index)
end
---------------------------------------------------------------------------------
