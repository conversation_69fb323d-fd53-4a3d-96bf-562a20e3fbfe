local NOW_FASHION_SHOW_PART_TYPE = {
	[SHIZHUANG_TYPE.BODY] = 1001,
	[SHIZHUANG_TYPE.SHENBING] = 1002,
	[SHIZHUANG_TYPE.WING] = 1003,
}

local NOW_JEWELRY_SHOW_PART_TYPE = {
	[SHIZHUANG_TYPE.MASK] = 1001,
	[SHIZHUANG_TYPE.BELT] = 1002,
	[SHIZHUANG_TYPE.FABAO] = 1003,
}

local NOW_EFFECT_SHOW_PART_TYPE = {
	-- [SHIZHUANG_TYPE.FOOT] = true,
	-- [SHIZHUANG_TYPE.SHOUHUAN] = true,
}

WardrobeDressingUpView = WardrobeDressingUpView or BaseClass(SafeBaseView)
function WardrobeDressingUpView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

    local common_bundle = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/wardrobe_new_ui_prefab"
	self:AddViewResource(0, bundle_name, "layout_wardrobe_dressing_up")
	self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.WAIGUAN})
end

function WardrobeDressingUpView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Wardrobe.ViewDressingUpName

    if not self.left_scheme_list then
		self.left_scheme_list = AsyncListView.New(DressingSchemeRender, self.node_list.left_scheme_list)
		self.left_scheme_list:SetSelectCallBack(BindTool.Bind(self.SelectSchemeCallBack, self))
		self.left_scheme_list:SetStartZeroIndex(true)
	end

	if not self.fashion_part_item_list then
		self.fashion_part_item_list = AsyncListView.New(WardrobePartShowRender, self.node_list.fashion_part_item_list)
	end

	if not self.jewelry_part_item_list then
		self.jewelry_part_item_list = AsyncListView.New(WardrobePartShowRender, self.node_list.jewelry_part_item_list)
	end

	-- if not self.effect_part_item_list then
	-- 	self.effect_part_item_list = AsyncListView.New(WardrobePartShowRender, self.node_list.effect_part_item_list)
	-- end

	if nil == self.role_model then
		self.role_model = CommonUserModelRender.New(self.node_list.ph_display)
		self.role_model:AddUiRoleModel(self)
	end

	XUI.AddClickEventListener(self.node_list.btn_wardrobe_dressing_use, BindTool.Bind(self.OnClickDressingUse, self))               -- 使用
	XUI.AddClickEventListener(self.node_list.btn_wardrobe_dressing_save, BindTool.Bind(self.OnClickDressingSave, self))           	-- 保存
end

function WardrobeDressingUpView:ReleaseCallBack()
	if self.left_scheme_list then
		self.left_scheme_list:DeleteMe()
		self.left_scheme_list = nil
	end

	if self.fashion_part_item_list then
		self.fashion_part_item_list:DeleteMe()
		self.fashion_part_item_list = nil
	end

	if self.jewelry_part_item_list then
		self.jewelry_part_item_list:DeleteMe()
		self.jewelry_part_item_list = nil
	end

	-- if self.effect_part_item_list then
	-- 	self.effect_part_item_list:DeleteMe()
	-- 	self.effect_part_item_list = nil
	-- end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.show_data = nil
	self.cur_scheme_index = nil
	self.cur_scheme_data = nil
	self.user_fashion_model_data = nil
end

-- 关闭前调用
function WardrobeDressingUpView:CloseCallBack()
	WardrobeWGCtrl.Instance:UpdateUISceneShowState()
end

-- 设置当前预览的缓存数据
function WardrobeDressingUpView:SetNowCacheData(cache_data)
	self.show_data = cache_data
end

-- 方案切换
function WardrobeDressingUpView:SelectSchemeCallBack(cell, cell_index, is_default, is_click)
	if cell == nil or cell.data == nil then
		return
	end

    if self.cur_scheme_index == cell_index then
        return
    end

    self.cur_scheme_index = cell_index
    self.cur_scheme_data = cell.data
    self:FlushSchemeShowMessage()
end

-- 刷新
function WardrobeDressingUpView:OnFlush()
	local list = WardrobeWGData.Instance:GetShiZhuangProjectList()
	list[0] = self.show_data
	self.left_scheme_list:SetDataList(list)

	local jump_index = self.cur_scheme_index or 0
	self.cur_scheme_index = nil
    self.left_scheme_list:JumpToIndex(jump_index, 5)
end

-- 刷新方案信息
function WardrobeDressingUpView:FlushSchemeShowMessage()
	local fashion_part_list, jewelry_part_list, effect_part_list = self:SplitPartItemList()
	self.fashion_part_item_list:SetDataList(fashion_part_list)
	self.jewelry_part_item_list:SetDataList(jewelry_part_list)
	-- self.effect_part_item_list:SetDataList(effect_part_list)
	self:FlushNowCacheModel()

	self.node_list.btn_wardrobe_dressing_use:CustomSetActive(self.cur_scheme_index ~= 0)
	self.node_list.btn_wardrobe_dressing_save:CustomSetActive(self.cur_scheme_index ~= 0)
end

-- 拆分数据
function WardrobeDressingUpView:SplitPartItemList()
	if not self.cur_scheme_data then
		return
	end

	local fashion_part_list = {}
	local jewelry_part_list = {}
	local effect_part_list = {}

	for i, v in ipairs(self.cur_scheme_data) do
		local data = {}
		data.show_item_id = v.stuff_id or 0
		data.used_index = v.used_index
		data.part_type = v.part_type
		data.state = REWARD_STATE_TYPE.UNDONE

		if v.used_index ~= 0  then
			local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(v.part_type, v.used_index)
			data.state = is_act and REWARD_STATE_TYPE.FINISH or REWARD_STATE_TYPE.UNDONE
		end

		-- if NOW_EFFECT_SHOW_PART_TYPE[v.part_type] then
		-- 	data.sort_num = NOW_EFFECT_SHOW_PART_TYPE[v.part_type]
		-- 	table.insert(effect_part_list, data)
		-- else
		if NOW_FASHION_SHOW_PART_TYPE[v.part_type] then
			data.sort_num = NOW_FASHION_SHOW_PART_TYPE[v.part_type]
			table.insert(fashion_part_list, data)
		elseif NOW_JEWELRY_SHOW_PART_TYPE[v.part_type] then
			data.sort_num = NOW_JEWELRY_SHOW_PART_TYPE[v.part_type]
			table.insert(jewelry_part_list, data)
		end
	end

	table.sort(fashion_part_list, SortTools.KeyLowerSorter("sort_num"))
	table.sort(jewelry_part_list, SortTools.KeyLowerSorter("sort_num"))
	return fashion_part_list, jewelry_part_list, effect_part_list
end

-- 刷新模型
function WardrobeDressingUpView:FlushNowCacheModel()
	if not self.cur_scheme_data then
		return
	end

	self.user_fashion_model_data = {}
	local role_res_id = AppearanceWGData.Instance:GetRoleResId()

	for i, v in ipairs(self.cur_scheme_data) do
		local part_type = v.part_type
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(v.part_type, v.used_index)
		local res_id = fashion_cfg and fashion_cfg.resouce or 0

		if part_type == SHIZHUANG_TYPE.BODY then      -- 脸饰
			if res_id == 0 then
				res_id = role_res_id
			end

			self.user_fashion_model_data.body_res_id = res_id
		elseif part_type == SHIZHUANG_TYPE.MASK then      -- 脸饰
			self.user_fashion_model_data.mask_id = res_id
		elseif part_type == SHIZHUANG_TYPE.BELT then  -- 腰饰
			self.user_fashion_model_data.belt_id = res_id
		elseif part_type == SHIZHUANG_TYPE.WEIBA then -- 尾巴
			self.user_fashion_model_data.tail_id = res_id
		elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
			self.user_fashion_model_data.shou_huan_id = res_id
		elseif part_type == SHIZHUANG_TYPE.HALO then  -- 光环
			self.user_fashion_model_data.halo_id = res_id
		elseif part_type == SHIZHUANG_TYPE.WING then  -- 羽翼
			self.user_fashion_model_data.wing_id = res_id
		elseif part_type == SHIZHUANG_TYPE.FABAO then -- 法宝
			self.user_fashion_model_data.fabao_id = res_id
		elseif part_type == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
			self.user_fashion_model_data.jianzhen_id = res_id
		elseif part_type == SHIZHUANG_TYPE.SHENBING then -- 武器
			self.user_fashion_model_data.weapon_id = res_id
		elseif part_type == SHIZHUANG_TYPE.FOOT then  -- 足迹
			self.user_fashion_model_data.foot_effect_id = res_id
		end
	end

	self.user_fashion_model_data.foot_effect_id = 0
	self.user_fashion_model_data.cur_anim = SceneObjAnimator.UiIdle
	self.user_fashion_model_data.model_rt_type = ModelRTSCaleType.M
	self.user_fashion_model_data.is_ui_scene = true
	self.role_model:SetData(self.user_fashion_model_data)
	self.role_model:SetUSAdjustmentNodeLocalScale(1)
	self.role_model:SetUSAdjustmentNodeLocalPosition(Vector3(-1, 0, 0))
end
------------------------------------------------------------------------------------------
-- 使用方案    
function WardrobeDressingUpView:OnClickDressingUse()
	if not self.cur_scheme_data then
		return
	end

	WardrobeWGCtrl.Instance:SetNowCacheUseList(self.cur_scheme_data)
	self:Close()
end 

-- 保存方案 		
function WardrobeDressingUpView:OnClickDressingSave()
	if (not self.cur_scheme_data) or (not self.show_data) then
		return
	end

	if (not self.cur_scheme_index) or self.cur_scheme_index == 0 then
		return
	end
 
	local part_list = {}
	for i, v in ipairs(self.show_data) do
		if v.used_index > 0 then
			local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(v.part_type, v.used_index)
			part_list[v.part_type] = v.used_index

			if not is_act then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Wardrobe.DressingSaveError)
				return
			end
		end
	end

	WardrobeWGCtrl.Instance:SendShiZhuangProjectRequest(self.cur_scheme_index - 1, part_list)
end
----------------------------------方案item-----------------------
DressingSchemeRender = DressingSchemeRender or BaseClass(BaseRender)
function DressingSchemeRender:OnFlush()
    if not self.data then
        return
    end

    local name_str = ""
    if self.index == 0 then
		name_str = Language.Wardrobe.NowShowDressing
    else
        name_str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(self.index))
    end

    self.node_list.normal_txt.text.text = name_str
    self.node_list.select_txt.text.text = name_str
end

function DressingSchemeRender:OnSelectChange(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
    self.node_list.select:CustomSetActive(is_select)
end