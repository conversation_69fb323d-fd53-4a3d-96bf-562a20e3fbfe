local empty_table = {}
function CultivationWGData:Esoterica__init()
	self.esoterica_list = {}
	self.esoterica_bag_list = {}
	self.cache_esoterica_color_list = {}
	self.esoterica_item_id_num = {}
	self.esoterica_skill_list = {}
	self.esoterica_skill_show_list = {}

	self.esoterica_exe = -1

	local cfg = ConfigManager.Instance:GetAutoConfig("esoterica_cfg_auto")
	self.esoterica_cfg = cfg.esoterica
	self.esoterica_level_cfg = ListToMap(cfg.esoterica_level, "seq", "level")
	self.esoterica_part_cfg = ListToMap(cfg.part, "seq", "part")
	self.esoterica_part_level_cfg = ListToMap(cfg.part_level, "seq", "part", "level")
	self.esoterica_equip_cfg = cfg.equip
	self.esoterica_skill_level_cfg = ListToMap(cfg.skill_level, "seq", "level")
	self:CacheEsotericaResloveList()

	self.esoterica_skill_cfg = {}
	for k,v in pairs(self.esoterica_cfg) do
		self.esoterica_skill_cfg[v.skill_id] = v
	end

	RemindManager.Instance:Register(RemindName.Esoterica, BindTool.Bind(self.GetEsotericaAllRemind, self))
	RemindManager.Instance:Register(RemindName.EsotericaReslove, BindTool.Bind(self.GetEsotericaResloveRemind, self))
end

function CultivationWGData:Esoterica__delete()
	RemindManager.Instance:UnRegister(RemindName.Esoterica)
	RemindManager.Instance:UnRegister(RemindName.EsotericaReslove)
end

--============================ 配置 ==================================
-- 秘笈
function CultivationWGData:GetEsotericaCfg(slot)
	return self.esoterica_cfg[slot]
end

-- 秘笈 by skill_id
function CultivationWGData:GetEsotericaCfgBySkillId(skill_id)
	return self.esoterica_skill_cfg[skill_id]
end

-- 秘笈等级
function CultivationWGData:GetEsotericaLevelCfg(slot, level)
	return (self.esoterica_level_cfg[slot] or empty_table)[level]
end

-- 秘笈最大等级
function CultivationWGData:GetEsotericaMaxLevel(slot)
	return #self.esoterica_level_cfg[slot]
end

function CultivationWGData:GetEsotericaLineShow(slot,part)
	local line_show=self.esoterica_cfg[slot].line_show
	local tab = Split(line_show,"|")
	return Split(tab[part],",")
end

-- 部位
function CultivationWGData:GetEsotericaPartCfg(slot, part)
	return (self.esoterica_part_cfg[slot] or empty_table)[part]
end

-- 部位等级
function CultivationWGData:GetEsotericaPartLevelCfg(slot, part, level)
	return ((self.esoterica_part_level_cfg[slot] or empty_table)[part] or empty_table)[level]
end

-- 装备
function CultivationWGData:GetEsotericaPartItemCfg(item_id)
	return self.esoterica_equip_cfg[item_id]
end

function CultivationWGData:GetIsEsotericaPartItem(item_id)
	return self.esoterica_equip_cfg[item_id] ~= nil
end

function CultivationWGData:GetEsotericaIsActive(item_id)
	local equip_cfg = self.esoterica_equip_cfg[item_id]
	if not equip_cfg then
		return false
	end

	local slot_info = self:GetEsotericaSlotInfo(equip_cfg.seq)
	local is_active = false
	if slot_info then
		is_active = slot_info.is_active
	end

	return is_active
end

-- 技能等级
function CultivationWGData:GetEsotericaSkillLevelList(slot)
	return self.esoterica_skill_level_cfg[slot] or empty_table
end

-- 技能等级
function CultivationWGData:GetEsotericaSkillLevelCfg(slot, level)
	return (self.esoterica_skill_level_cfg[slot] or empty_table)[level]
end

-- 缓存分解信息
function CultivationWGData:CacheEsotericaResloveList()
	-- 经验道具
	if self.es_reslove_item_list == nil then
		self.es_reslove_item_list = {}
		for k,v in pairs(self.esoterica_equip_cfg) do
			if v.seq == -1 or v.part == -1 then
				self.es_reslove_item_list[v.item_id] = true
			end
		end
	end

	-- 装备升级 在某等级前需要的道具
	if self.es_slot_part_stuff_list == nil then
		self.es_slot_part_stuff_list = {}
		local slot, part, level, stuff_id, cache_level = -1, -1, -1, 0, -1
		local cfg = ConfigManager.Instance:GetAutoConfig("esoterica_cfg_auto")
		for k,v in pairs(cfg.part_level) do
			slot = v.seq
			part = v.part
			level = v.level
			stuff_id = v.cost_item_id
			if not self.es_slot_part_stuff_list[slot] then
				self.es_slot_part_stuff_list[slot] = {}
			end
		
			if not self.es_slot_part_stuff_list[slot][part] then
				self.es_slot_part_stuff_list[slot][part] = {}
			end

			if not self.es_slot_part_stuff_list[slot][part][stuff_id] then
				self.es_slot_part_stuff_list[slot][part][stuff_id] = level
			end

			cache_level = self.es_slot_part_stuff_list[slot][part][stuff_id]
			if level > cache_level then
				self.es_slot_part_stuff_list[slot][part][stuff_id] = level
			end
		end
	end
end

-- 是否经验道具
function CultivationWGData:GetEsotericaIsExpItemid(item_id)
	return self.es_reslove_item_list[item_id] ~= nil
end

-- 获取部位升级需要的材料
function CultivationWGData:GetEsotericaPartUplevelListData(slot, part)
	return (self.es_slot_part_stuff_list[slot] or empty_table)[part] or empty_table
end







--============================== 协议 ================================
-- 槽位信息
function CultivationWGData:SetEsotericaSlotInfo(protocol)
	self.esoterica_list = protocol.esoterica_list
end

-- 槽位更新
function CultivationWGData:UpdateEsotericaSlotInfo(protocol)
	self.esoterica_list[protocol.slot] = protocol.esoterica_data
end

-- 获取槽位信息
function CultivationWGData:GetEsotericaSlotInfo(slot)
	return self.esoterica_list[slot]
end

function CultivationWGData:GetEsotericaSlotLevel(slot)
	return (self.esoterica_list[slot] or empty_table).level or 0
end

function CultivationWGData:GetEsotericaSlotInfoList()
	return self.esoterica_list
end

function CultivationWGData:GetEsotericaSlotPartInfoList(slot)
	return (self.esoterica_list[slot] or empty_table).part_list or empty_table
end

function CultivationWGData:GetEsotericaSlotPartInfo(slot, part)
	return ((self.esoterica_list[slot] or empty_table).part_list or empty_table)[part]
end






-- 基础信息
function CultivationWGData:SetEsotericaBaseInfo(protocol)
	self.esoterica_exe = protocol.esoterica_exe
end

function CultivationWGData:GetEsotericaEXP()
	return self.esoterica_exe
end





-- 背包信息
function CultivationWGData:SetEsotericaBagInfo(protocol)
	self.esoterica_bag_list = protocol.grid_list
	self:CacheAllEsotericaColorList()
	-- print_error("---背包列表--", self.esoterica_bag_list)
end

-- 格子信息
function CultivationWGData:GetEsotericaBagItem(index)
	return self.esoterica_bag_list[index]
end

function CultivationWGData:GetEsotericaBagList()
	return self.esoterica_bag_list
end

function CultivationWGData:UpdateEsotericaBagGrid(protocol)
	local new_data = protocol.grid_info
	local index = new_data.index
	local item_id = new_data.item_id
	local old_data = self:GetEsotericaBagItem(index)
	local old_num = old_data and old_data.num or 0
	local new_num = new_data.num

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	if (old_data == nil or old_data.item_id == nil or old_data.item_id <= 0) and new_num > old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_ADD
		self:CacheEsotericaColorData(change_reason, new_data, new_num - old_num)
	elseif old_data and old_data.item_id > 0 and new_num < old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_REMOVE
		if new_data.item_id <= 0 then
			item_id = old_data.item_id
			new_data = nil
			self:CacheEsotericaColorData(change_reason, old_data, old_num - new_num)
		else
			self:CacheEsotericaColorData(change_reason, new_data, old_num - new_num)
		end
	else
		self:CacheEsotericaColorData(change_reason, new_data, new_num - old_num)
	end

	self.esoterica_bag_list[index] = new_data
	-- print_error("---背包列表--", self.esoterica_bag_list)
	CultivationWGCtrl.Instance:OnEsotericaItemDataChange(item_id, index, change_reason, old_num, new_num)
end

-- 缓存品质列表
function CultivationWGData:CacheAllEsotericaColorList()
	self.cache_esoterica_color_list = {}
	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	local bag_list = self:GetEsotericaBagList()
	for k,v in pairs(bag_list) do
		self:CacheEsotericaColorData(change_reason, v, v.num)
	end
end

-- 改变缓存数据
function CultivationWGData:CacheEsotericaColorData(change_reason, data, change_num)
	if data == nil or change_num == 0 then
		return
	end

	local item_id = data.item_id
	local slot = data.slot
	local part = data.part
	local color = data.color

	-- 计数
    local item_num = self.esoterica_item_id_num[item_id] or 0
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
        item_num = item_num - change_num
        item_num = item_num > 0 and item_num or 0
    else
        item_num = item_num + change_num
	end

    self.esoterica_item_id_num[item_id] = item_num


	-- 材料不统计
	if data.is_stuff then
		return
	end

	if not self.cache_esoterica_color_list[slot] then
		self.cache_esoterica_color_list[slot] = {}
	end

	if not self.cache_esoterica_color_list[slot][part] then
		self.cache_esoterica_color_list[slot][part] = {}
	end

	local num = self.cache_esoterica_color_list[slot][part][color] or 0
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
		num = num + change_num
	elseif change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
		num = num + change_num
	elseif change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		num = num - change_num
		num = num > 0 and num or 0
	end

	self.cache_esoterica_color_list[slot][part][color] = num
end

function CultivationWGData:GetEsotericaColorCache(slot, part, color)
	return ((self.cache_esoterica_color_list[slot] or empty_table)[part] or empty_table)[color] or 0
end

-- 获取 道具数量
function CultivationWGData:GetEsotericaItemNum(item_id)
    return self.esoterica_item_id_num[item_id] or 0
end

-- 主界面技能列表
function CultivationWGData:SetEsotericaSkillInfo(protocol)
	self.esoterica_skill_list = protocol.esoterica_skill_list
	SortTools.SortAsc(self.esoterica_skill_list, "start_time")
	-- print_error("---技能列表---", self.esoterica_skill_list)
	self:ChangeEsotericaShowSkill()

	-- print_error("---技能展示列表---", self:GetEsotericaShowSkill())
end

-- 主界面技能列表 更新
function CultivationWGData:UpdateEsotericaSkillInfo(protocol)
	local pro_slot = protocol.slot
	local data = protocol.esoterica_skill_data
	local cur_server_time = TimeWGCtrl.Instance:GetServerTime()
	local is_remove = data.skill_id <= 0 or cur_server_time >= data.end_time

	local index
	for k,v in ipairs(self.esoterica_skill_list) do
		if v.slot == pro_slot then
			index = k
			break
		end
	end

	if index then
		if not is_remove then
			self.esoterica_skill_list[index] = data
		else
			table.remove(self.esoterica_skill_list, index)
		end
		
	elseif not index and not is_remove then
		table.insert(self.esoterica_skill_list, data)
	end

	SortTools.SortAsc(self.esoterica_skill_list, "start_time")
	-- print_error("---技能列表---", self.esoterica_skill_list)
	self:ChangeEsotericaShowSkill()
	-- print_error("---技能展示列表---", self:GetEsotericaShowSkill())
end




--==============================  ================================
function CultivationWGData:InitEsotericaActNeed()
	local esoterica_equip_map_cfg = ListToMapByDisorder(self.esoterica_equip_cfg, "seq", "part", "part_level") or {}

	self.esoterica_slot_min_color_list = {}
	self.esoterica_part_actid_list = {}
	local item_id, color = 0, 0
	for slot = 0, ESOTERICA_DEFINE.MAX_NUM - 1 do
		if not self:GetEsotericaCfg(slot) then
			break
		end

		color = 0
		self.esoterica_part_actid_list[slot] = {}
		for part = 0, ESOTERICA_DEFINE.PART_NUM - 1 do
			local cfg = ((esoterica_equip_map_cfg[slot] or {})[part] or {})[1]
			item_id = cfg and cfg.item_id or 0
			self.esoterica_part_actid_list[slot][part] = item_id

			local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
			if item_cfg and (color == 0 or color < item_cfg.color) then
				color = item_cfg.color
			end
		end

		self.esoterica_slot_min_color_list[slot] = color
	end
end

-- 获取槽位最低品质色
function CultivationWGData:GetEsotericaSlotMinColor(slot)
	if not self.esoterica_slot_min_color_list then
		self:InitEsotericaActNeed()
	end
	
	return self.esoterica_slot_min_color_list[slot] or 1
end

-- 当前品质色
function CultivationWGData:GetEsotericaSlotRealColor(slot)
	local slot_info = self:GetEsotericaSlotInfo(slot)
	if not slot_info then
		return 0
	end

	local color = 0
	for k,v in pairs(slot_info.part_list) do
		if color == 0 or v.color < color then
			color = v.color
		end
	end

	return color
end

-- 获取槽位展示品质色
function CultivationWGData:GetEsotericaSlotCurShowColor(slot)
	local slot_info = self:GetEsotericaSlotInfo(slot)
	if not slot_info then
		return 1
	end

	if not slot_info.is_active then
		return self:GetEsotericaSlotMinColor(slot)
	end

	local color = self:GetEsotericaSlotRealColor(slot)
	return color == 0 and 1 or color
end


-- 获取部位最低激活道具
function CultivationWGData:GetEsotericaPartMinActItemid(slot, part)
	if not self.esoterica_part_actid_list then
		self:InitEsotericaActNeed()
	end

	return (self.esoterica_part_actid_list[slot] or {})[part] or 0
end

-- 秘笈 展示列表
function CultivationWGData:GetEsotericaShowList()
	local list = {}

	for slot = 0, #self.esoterica_cfg do
		local cfg = self.esoterica_cfg[slot]
		local slot_info = CultivationWGData.Instance:GetEsotericaSlotInfo(slot)
		local data = {
			slot = cfg.seq,
			name = cfg.name,
			index = cfg.jingjie_level_limit,
			img_id = cfg.img_id,
			skill_img = cfg.skill_img,
			img_pos = cfg.img_pos,
			img_scale = cfg.img_scale,
			ui_effect_asset = cfg.ui_effect_asset,
			skill_label = cfg.skill_label,
			level = self:GetEsotericaSlotLevel(slot),
			color = self:GetEsotericaSlotCurShowColor(slot),
			is_remind = self:GetEsotericaSlotRemind(slot),
			is_active = slot_info.is_active,
			is_active_sort = slot_info.is_active and 10 or 100
		}

		table.insert(list, data)

	end
	table.sort(list, SortTools.KeyLowerSorters("is_active_sort", "index", "slot"))

	return list
end

-- 获取 槽位技能等级
function CultivationWGData:GetEsotericaSlotSkillLevel(slot)
	local skill_list = self:GetEsotericaSkillLevelList(slot)
	if not skill_list then
		return 0
	end

	local slot_info = self:GetEsotericaSlotInfo(slot)
	if not slot_info or not slot_info.is_active then
		return 0
	end

	local level = 0
	for i = 0, ESOTERICA_DEFINE.PART_NUM - 1 do
		if level == 0 or slot_info.part_list[i].level < level then
			level = slot_info.part_list[i].level
		end
	end
	-- for k,v in pairs(slot_info.part_list) do
	-- 	if level == 0 or v.level < level then
	-- 		print_error(v.level)
	-- 		level = v.level
	-- 	end
	-- end

	for i = #skill_list, 1, -1 do
		local cfg = skill_list[i]
		if level >= cfg.need_part_level then
			return cfg.level
		end
	end

	return 0
end

-- 获取 槽位激活部位的数量
function CultivationWGData:GetEsotericaSlotActPartNum(slot)
	local slot_info = self:GetEsotericaSlotInfo(slot)
	if not slot_info then
		return 0
	end

	local num = 0
	for k,v in pairs(slot_info.part_list) do
		if v.item_id > 0 then
			num = num + 1
		end
	end

	return num
end

-- 属性
function CultivationWGData:GetEsotericaUpLevelShowAttr(slot, cur_level)
    local cur_cfg = self:GetEsotericaLevelCfg(slot, cur_level)
    local next_cfg = self:GetEsotericaLevelCfg(slot, cur_level + 1)
	local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, next_cfg, "attr_id", "attr_value", 1, 5)
    return attr_list
end

-- 是否有更好的装备
function CultivationWGData:GetEsotericaSlotPartHadBetter(slot, part)
	local is_had_better = false
	local bag_index = -1
	local part_info = self:GetEsotericaSlotPartInfo(slot, part)
	if not part_info then
		return is_had_better, bag_index
	end

	local better_color = 0
	local max_color = GameEnum.ITEM_COLOR_COLOR_FUL
	local end_color = part_info.color + 1
	for color = max_color, end_color, -1 do
		if self:GetEsotericaColorCache(slot, part, color) > 0 then
			is_had_better = true
			better_color = color
			break
		end
	end

	if is_had_better then
		for k,v in pairs(self:GetEsotericaBagList()) do
			if v.slot == slot and v.part == part and v.color == better_color then
				bag_index = v.index
				break
			end
		end

		if bag_index == -1 then
			print_error("---【秘笈】数据统计有误---")
		end
	end

	return is_had_better, bag_index
end


-- 分解列表
function CultivationWGData:GetEsotericaResloveBagList()
	local reslove_list = {}
	local can_reslove_item_list = {}
	for slot = 0, ESOTERICA_DEFINE.MAX_NUM - 1 do
		if not self:GetEsotericaCfg(slot) then
			break
		end

		local part_list = self:GetEsotericaSlotPartInfoList(slot)
		for k,v in pairs(part_list) do
			local stuff_list = self:GetEsotericaPartUplevelListData(slot, v.part)
			for stuff_id, max_level in pairs(stuff_list) do
				if v.level >= max_level then
					can_reslove_item_list[stuff_id] = true
				end
			end
		end
	end

	for k,v in pairs(self:GetEsotericaBagList()) do
		if self:GetEsotericaIsExpItemid(v.item_id) or can_reslove_item_list[v.item_id] then
			table.insert(reslove_list, v)
		end
	end

	SortTools.SortAsc(reslove_list, "slot", "part")
	return reslove_list
end

-- 槽位战力计算
function CultivationWGData:GetEsotericaSlotCapability(slot)
	local capability = 0
	local base_attribute = AttributePool.AllocAttribute()

	local em_data = EquipmentWGData.Instance
	local type_key_str = "attr_id"
	local value_key_str = "attr_value"
	local attr_id, attr_value, attr_str = 0, 0, ""
	local function AddAttr(cfg)
		if not cfg then return end
		for i = 1, 5 do
			attr_id = cfg and cfg[type_key_str .. i] or 0
			attr_value = cfg and cfg[value_key_str .. i] or 0
			if attr_id > 0 and attr_value > 0 then
				attr_str = em_data:GetAttrStrByAttrId(attr_id)
				if base_attribute[attr_str] ~= nil then
					base_attribute[attr_str] = base_attribute[attr_str] + attr_value
				end
			end
		end
	end

	-- 部位
	local part_list = self:GetEsotericaSlotPartInfoList(slot)
	for k,v in pairs(part_list) do
		if v.item_id > 0 then
			local cfg = self:GetEsotericaPartLevelCfg(slot, v.part, v.level)
			AddAttr(cfg)
		end
	end

	-- 秘笈升级
	local slot_info = self:GetEsotericaSlotInfo(slot)
	if slot_info and slot_info.is_active then
		local cfg = self:GetEsotericaLevelCfg(slot, slot_info.level)
		AddAttr(cfg)
	end

	-- 技能
	local temp_skill_cfg
	local slot_cfg = self:GetEsotericaCfg(slot)
	if slot_cfg then
	local cur_skill_level = self:GetEsotericaSlotSkillLevel(slot)
		local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(slot_cfg.skill_id, cur_skill_level)
		if not IsEmptyTable(skill_cfg) then
			temp_skill_cfg = {}
			temp_skill_cfg.attack_power = skill_cfg.attack_power
			temp_skill_cfg.defence_power = skill_cfg.defence_power
			temp_skill_cfg.capability_inc = skill_cfg.capability_inc
		end
	end
	capability = AttributeMgr.GetCapability(base_attribute, temp_skill_cfg)
	return capability
end

-- 部位战力计算
function CultivationWGData:GetEsotericaPartCapability(slot, part)
	local capability = 0

	local part_info = self:GetEsotericaSlotPartInfo(slot, part)
	if not part_info then
		return capability
	end

	local base_attribute = AttributePool.AllocAttribute()
	local em_data = EquipmentWGData.Instance
	local type_key_str = "attr_id"
	local value_key_str = "attr_value"
	local attr_id, attr_value, attr_str = 0, 0, ""
	local function AddAttr(cfg)
		if not cfg then return end
		for i = 1, 5 do
			attr_id = cfg and cfg[type_key_str .. i] or 0
			attr_value = cfg and cfg[value_key_str .. i] or 0
			if attr_id > 0 and attr_value > 0 then
				attr_str = em_data:GetAttrStrByAttrId(attr_id)
				if base_attribute[attr_str] ~= nil then
					base_attribute[attr_str] = base_attribute[attr_str] + attr_value
				end
			end
		end
	end

	local cfg = self:GetEsotericaPartLevelCfg(slot, part, part_info.level)
	AddAttr(cfg)

	capability = AttributeMgr.GetCapability(base_attribute)
	return capability
end

-- 是否比当前部位更好的
function CultivationWGData:EsotericaIsBetterWearByGirdData(data)
	if data == nil then
		return false
	end

	local part_info = self:GetEsotericaSlotPartInfo(data.slot, data.part)
	if not part_info then
		return false
	end

	-- 有品质更好的
	if data.color > part_info.color then
		return true
	end

	if part_info.item_id <= 0 then
		return false
	end

	local next_part_level_cfg = self:GetEsotericaPartLevelCfg(data.slot, data.part, part_info.level + 1)
	if not next_part_level_cfg then
		return false
	end

	local cur_part_level_cfg = self:GetEsotericaPartLevelCfg(data.slot, data.part, part_info.level)
	if not cur_part_level_cfg or cur_part_level_cfg.cost_item_id ~= data.item_id then
		return false
	end

	local had_num = self:GetEsotericaItemNum(cur_part_level_cfg.cost_item_id)
	return had_num >= cur_part_level_cfg.cost_item_num
end







-- 槽位 部位红点
function CultivationWGData:GetEsotericaSlotPartRemind(slot, part)
	local slot_info = self:GetEsotericaSlotInfo(slot)
	if not slot_info or not slot_info.is_active then
		return false
	end

	local part_info = self:GetEsotericaSlotPartInfo(slot, part)
	if not part_info then
		return false
	end

	-- 有品质更好的
	local max_color = GameEnum.ITEM_COLOR_COLOR_FUL
	local end_color = part_info.color + 1
	for color = max_color, end_color, -1 do
		if self:GetEsotericaColorCache(slot, part, color) > 0 then
			return true
		end
	end

	-- 道具升级
	if part_info.item_id <= 0 then
		return false
	end

	local cur_part_level_cfg = self:GetEsotericaPartLevelCfg(slot, part, part_info.level)
	local next_part_level_cfg = self:GetEsotericaPartLevelCfg(slot, part, part_info.level + 1)
	if not cur_part_level_cfg or not next_part_level_cfg then
		return false
	end

	local had_num = self:GetEsotericaItemNum(cur_part_level_cfg.cost_item_id)
	return had_num >= cur_part_level_cfg.cost_item_num
end

-- 槽位 激活红点
function CultivationWGData:GetEsotericaSlotActRemind(slot)
	local slot_info = self:GetEsotericaSlotInfo(slot)
	if not slot_info or slot_info.is_active then
		return false
	end

	local slot_cfg = self:GetEsotericaCfg(slot)
	if not slot_cfg then
		return false
	end

	local item_id = CultivationWGData.Instance:GetEsotericaPartMinActItemid(slot, 0)
	local had_num = CultivationWGData.Instance:GetEsotericaItemNum(item_id)
	if had_num < 1 then
		return false
	end

	-- for i = 0, ESOTERICA_DEFINE.PART_NUM - 1 do
	-- 	if slot_info.part_list[i].item_id <= 0 then
	-- 		return false
	-- 	end
	-- end
	-- for k,v in pairs(slot_info.part_list) do
	-- 	if v.item_id <= 0 then
	-- 		print_error("111")
	-- 		return false
	-- 	end
	-- end

	-- 境界满足
	-- local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
	-- return cur_stage >= slot_cfg.jingjie_level_limit
	return true
end

-- 槽位 升级红点
function CultivationWGData:GetEsotericaSlotUplevelRemind(slot)
	local slot_info = self:GetEsotericaSlotInfo(slot)
	if not slot_info or not slot_info.is_active then
		return false
	end

	local cur_level_cfg = self:GetEsotericaLevelCfg(slot, slot_info.level)
	local next_level_cfg = self:GetEsotericaLevelCfg(slot, slot_info.level + 1)
	if not cur_level_cfg or not next_level_cfg then
		return false
	end

	local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
	if cur_stage < cur_level_cfg.jingjie_level_limit then
		return false
	end

	local cur_exp = self:GetEsotericaEXP()
	return cur_exp >= cur_level_cfg.need_exp
end

-- 槽位红点
function CultivationWGData:GetEsotericaSlotRemind(slot)
	-- 部位升级
	for part = 0, ESOTERICA_DEFINE.PART_NUM - 1 do
		if self:GetEsotericaSlotPartRemind(slot, part) then
			return true
		end
	end

	-- 槽位激活
	if self:GetEsotericaSlotActRemind(slot) then
		return true
	end

	-- 槽位升级
	if self:GetEsotericaSlotUplevelRemind(slot) then
		return true
	end

	return false
end

-- 仙法总红点
function CultivationWGData:GetEsotericaAllRemind()
	local esoterica_remind = self:GetEsotericaRemind()
	local esoterica_reslove_remind = self:GetEsotericaResloveRemind()
	if esoterica_remind == 1 or esoterica_reslove_remind == 1 then
		return 1
	else
		return 0
	end
end

-- 红点
function CultivationWGData:GetEsotericaRemind()
	for slot = 0, ESOTERICA_DEFINE.MAX_NUM - 1 do
		if not self:GetEsotericaCfg(slot) then
			self:SetEsotericaInvateTip(0)
			return 0
		end

		if self:GetEsotericaSlotRemind(slot) then
			self:SetEsotericaInvateTip(1)
			return 1
		end
	end

	self:SetEsotericaInvateTip(0)
	return 0
end

function CultivationWGData:SetEsotericaInvateTip(index)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ESOTERICA, index, function()
		ViewManager.Instance:Open(GuideModuleName.EsotericaDetailView)
	end)
end

-- 分解红点
function CultivationWGData:GetEsotericaResloveRemind()
	for item_id, v in pairs(self.es_reslove_item_list) do
		if self:GetEsotericaItemNum(item_id) > 0 then
			return 1
		end
	end

	for slot = 0, ESOTERICA_DEFINE.MAX_NUM - 1 do
		if not self:GetEsotericaCfg(slot) then
			break
		end

		local part_list = self:GetEsotericaSlotPartInfoList(slot)
		for k,v in pairs(part_list) do
			local stuff_list = self:GetEsotericaPartUplevelListData(slot, v.part)
			for stuff_id, max_level in pairs(stuff_list) do
				if v.level >= max_level and self:GetEsotericaItemNum(stuff_id) > 0 then
					return 1
				end
			end
		end
	end

	return 0
end



-- 主界面技能展示列表
--[[
	后端只发对应秘笈的技能是否下发，需要前端对技能列表进行排序
	已排序的技能需要保留原本的位置
	大于展示的数（6）的技能不显示，当其他技能消失再加入展示列表
]]
function CultivationWGData:ChangeEsotericaShowSkill()
	self.esoterica_remove_index_list = {}
	local cur_server_time = TimeWGCtrl.Instance:GetServerTime()

	local remove_table = {}
	local is_remove = true
	for k,v in pairs(self.esoterica_skill_show_list) do
		is_remove = true
		for i,j in ipairs(self.esoterica_skill_list) do
			if v.slot == j.slot then
				is_remove = false
				break
			end
		end

		if not is_remove and cur_server_time >= v.end_time then
			is_remove = true
		end

		if is_remove then
			table.insert(remove_table, k)
		end
	end

	for k,v in pairs(remove_table) do
		table.insert(self.esoterica_remove_index_list, v)
		self.esoterica_skill_show_list[v] = nil
	end

	local recover_index = nil
	for k,v in ipairs(self.esoterica_skill_list) do
		recover_index = nil
		for i,j in pairs(self.esoterica_skill_show_list) do
			if v.slot == j.slot then
				recover_index = i
				break
			end
		end

		if not recover_index then
			for i = 1, ESOTERICA_DEFINE.MAINUI_SHOW_SKILL_NUM do
				if not self.esoterica_skill_show_list[i] then
					self.esoterica_skill_show_list[i] = v
					break
				end
			end
		else
			self.esoterica_skill_show_list[recover_index] = v
		end
	end
end

-- 
function CultivationWGData:GetEsotericaShowSkill()
	return self.esoterica_skill_show_list
end

function CultivationWGData:GetEsotericaRemoveIndexList()
	return self.esoterica_remove_index_list
end

function CultivationWGData:ChangeEsotericaRemoveIndexList(index)
	if self.esoterica_remove_index_list and #self.esoterica_remove_index_list >= index then
		table.remove(self.esoterica_remove_index_list, index)
	end
end
