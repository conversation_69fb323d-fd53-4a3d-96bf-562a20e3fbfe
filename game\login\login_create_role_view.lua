-- 【职业改动修改】
-- 创角环形列表
local create_character_sex_prof_list = {
	{sex = 0, prof = 2},
	{sex = 1, prof = 1},
	{sex = 0, prof = 1},
	{sex = 1, prof = 3},
	{sex = 0, prof = 3},
	-- {sex = 1, prof = 4},
	-- {sex = 0, prof = 4},
}

local cg_bundle = "cg/a3_cg_chuangjue_prefab"
-- 【职业改动修改】
local cg_info_list = {
	[0] = {
		[1] = {
			-- bundle = "cg/a3_cg_chuangjue_prefab",
			asset = "CG_ChuangJue_NvJian",
			-- role_parent = "Character",
			-- role_node = "3101001_hm",
			camera_parent = "GameObject/Camera/Camera001",
			chuchang_stage = "GameObject/stage_chuangjue",
		},

		[2] = {
			-- bundle = "cg/a3_cg_chuangjue_prefab",
			asset = "CG_ChuangJue_NvCiKe",
			-- role_parent = "Character",
			-- role_node = "1102001_hm",
			camera_parent = "GameObject/Camera/Camera001",
			chuchang_stage = "GameObject/stage_chuangjue",
		},

		[3] = {
			-- bundle = "cg/a3_cg_chuangjue_prefab",
			asset = "CG_ChuangJue_NvDaoshi",
			-- role_parent = "Character",
			-- role_node = "3103001_hm",
			camera_parent = "GameObject/Camera/Camera001",
			chuchang_stage = "GameObject/stage_chuangjue",
		},

		-- [4] = {
		-- 	bundle = "cg/a3_cg_chuangjue_prefab",
		-- 	asset = "CG_ChuangJue_TianShi",
		-- 	role_parent = "stage/zhujue_tianshi",
		-- 	role_node = "3104001",
		-- 	camera_parent = "stage/xuanjue@daoshi_camera",
		-- 	chuchang_stage = "stage",
		-- },
	},

	[1] = {
		[1] = {
			-- bundle = "cg/a3_cg_chuangjue_prefab",
			asset = "CG_ChuangJue_NanJian",
			-- role_parent = "Character",
			-- role_node = "1101001_hm",
			camera_parent = "GameObject/Camera/Camera001",
			chuchang_stage = "GameObject/stage_chuangjue",
		},

		[3] = {
			-- bundle = "cg/a3_cg_chuangjue_prefab",
			asset = "CG_ChuangJue_NanDaoShi",
			-- role_parent = "Character",
			-- role_node = "1103001_hm",
			camera_parent = "GameObject/Camera/Camera001",
			chuchang_stage = "GameObject/stage_chuangjue",
		},

		-- [4] = {
		-- 	bundle = "cg/a3_cg_chuangjue_prefab",
		-- 	asset = "CG_ChuangJue_DaoShi",
		-- 	role_parent = "stage/zhujue_daoshi",
		-- 	role_node = "1104001",
		-- 	camera_parent = "stage/xuanjue@daoshi_camera",
		-- 	chuchang_stage = "stage",
		-- },
	},
}

function LoginView:Init__CreateRole()
	self.cg_instance_list = {}
	self.cg_urpcamera_component = nil
end


function LoginView:ReleaseCallBack__CreateRole()
	self:ClearCGHander()
	-- self:DeleteCreateCharacterCamberedList()
	self:DeleteCreateCharacterList()
	self.prof_desc_eff = {}
	self.role_rotate_area = nil
	self.cg_urpcamera_component = nil
	self.scene_waters_node = nil
	self.scene_models_node = nil
	self.scene_hightfog_node = nil
	self.cg_chuchang = nil

	self:RemoveShowEffectDelayTimer()
end

function LoginView:LoadCallBack__CreateRole()
	-- self:InitCreateCharacterCamberedList()
	self:InitCreateCharacterList()

	self.prof_desc_eff = {}
	for k,v in pairs(create_character_sex_prof_list) do
		self.prof_desc_eff[v.prof] = self.prof_desc_eff[v.prof] or {}
		self.prof_desc_eff[v.prof][v.sex] = self.prof_desc_eff[v.prof][v.sex] or {}
		self.prof_desc_eff[v.prof][v.sex] = self.node_list["ProfDescEff_" .. v.prof .. "_" .. v.sex]
	end

	-- 创建角色旋转区域
	self.role_rotate_area = self.node_list["role_rotate_area"]
	local event_trigger = self.role_rotate_area:GetComponent(typeof(EventTriggerListener))
	event_trigger:AddDragListener(BindTool.Bind(self.OnCreateoleModelDrag, self))

	self.node_list["return_btn"].button:AddClickListener(BindTool.Bind(self.OnCreateReturnClick, self))
	self.node_list["goto_diy_Btn"].button:AddClickListener(BindTool.Bind(self.OnGoToDIYBtn, self))
	self.node_list["cg_skip_btn"].button:AddClickListener(BindTool.Bind(self.OnCGSkipBtn, self))
end



--[[
-- 滑动弧形列表 旧逻辑暂时保留
function LoginView:DeleteCreateCharacterCamberedList()
	if self.create_character_cambered_list then
		self.create_character_cambered_list:DeleteMe()
		self.create_character_cambered_list = nil
	end
end

function LoginView:InitCreateCharacterCamberedList()
	self.cccl_drag_select_index = -1
	self.cccl_btn_select_index = -1
	self.cccl_viewport_drag_count = 3

	local cambered_list_data = {
		item_render = CreateCharacterCamberedRander,
		asset_bundle = "uis/view/login_ui_prefab",
		asset_name = "layout_create_character_render",

		scroll_list = self.node_list.character_list,
		center_x = -480, center_y = -10,
		radius_x = 370, radius_y = 228,
		angle_delta = Mathf.PI / 9,
		origin_rotation = -Mathf.PI / 2,
		is_drag_horizontal = false,
		viewport_count = self.cccl_viewport_drag_count,
		speed = -0.3,
		arg_speed = 0.2,
		--limit_drag_over_angle = 0.5,

		click_item_cb = BindTool.Bind(self.OnClickCCCLBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragCCCLToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragCCCLToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragCCCLEndCallBack, self),
	}

	self.create_character_cambered_list = CamberedList.New(cambered_list_data)
	self.create_character_cambered_list:CreateCellList(#create_character_sex_prof_list)
	self.create_character_cambered_list.angle_rotate = 0
	local btn_item_list = self.create_character_cambered_list:GetRenderList()
	-- self.create_character_cambered_list:StartMove(Mathf.PI / 4)		-- 增加一个滑动进场
	for k, item_cell in ipairs(btn_item_list) do
		local item_data = create_character_sex_prof_list[k]
		item_cell:SetData(item_data)
	end
end

function LoginView:OnClickCCCLBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.cccl_btn_select_index == select_index) then
		return
	end

	self.cccl_btn_select_index = select_index
	self:OnCCCLSelectedBtnChange(function ()
		self:OnClickSelectProf(select_data.sex, select_data.prof)
	end, true)
end

-- 拖拽到下一个
function LoginView:OnDragCCCLToNextCallBack()
	local max_index = #create_character_sex_prof_list
	self.cccl_drag_select_index = self.cccl_drag_select_index < self.cccl_viewport_drag_count and self.cccl_viewport_drag_count or self.cccl_drag_select_index
	self.cccl_drag_select_index = self.cccl_drag_select_index + 1
	self.cccl_drag_select_index = self.cccl_drag_select_index > max_index and max_index or self.cccl_drag_select_index
end

function LoginView:OnDragCCCLToLastCallBack()
	self.cccl_drag_select_index = self.cccl_drag_select_index - 1
	self.cccl_drag_select_index = self.cccl_drag_select_index < self.cccl_viewport_drag_count and self.cccl_viewport_drag_count or self.cccl_drag_select_index
end

function LoginView:OnDragCCCLEndCallBack()
	self:OnCCCLSelectedBtnChange(nil, nil, self.cccl_drag_select_index)
end

function LoginView:OnCCCLSelectedBtnChange(callback, is_click, drag_index)
	if self.create_character_cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.cccl_btn_select_index or 1
	-- 不做滑动选中直接调用回调
	--self.create_character_cambered_list:ScrollToIndex(to_index, callback, is_click)
	if callback then
		callback()
	end

	if is_click then
		local item_list = self.create_character_cambered_list:GetRenderList()
		for k, item_cell in ipairs(item_list) do
			item_cell:SetSelectedHL(to_index)
		end
	end
end

function LoginView:CamberedListScrollEnter()
	if self.create_character_cambered_list == null then
		return
	end

	self.create_character_cambered_list.angle_rotate = 0
	self.create_character_cambered_list.target_angle = -Mathf.PI / 3.8 + (-Mathf.PI / 2)
	self.create_character_cambered_list:StartMove(Mathf.PI / 3.8 + Mathf.PI / 2)
end
]]--


function LoginView:DeleteCreateCharacterList()
	if self.create_character_list then
		for k,v in pairs(self.create_character_list) do
			v:DeleteMe()
		end

		self.create_character_list = nil
	end
end

function LoginView:InitCreateCharacterList()
	if not self.create_character_list then
		self.create_character_list = {}
		local node_num = self.node_list.character_list.transform.childCount
		for i = 1, node_num do
			self.create_character_list[i] = CreateCharacterCamberedRander.New(self.node_list.character_list:FindObj("node" .. i))
			self.create_character_list[i]:SetIndex(i)
			self.create_character_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickCreateCharacterRenderCB, self))
		end
	end

	for k,v in pairs(self.create_character_list) do
		v:SetData(create_character_sex_prof_list[k])
	end
end

function LoginView:OnClickCreateCharacterRenderCB(item_cell)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if item_cell.is_shield then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Login.OpenRoleTips)
		return
	end

	if self.cccl_btn_select_index == select_index then
		return
	end

	for k, cell in pairs(self.create_character_list) do
		cell:SetSelectedHL(select_index)
	end

	self.cccl_btn_select_index = select_index
	self:OnClickSelectProf(select_data.sex, select_data.prof)
end

function LoginView:SetLowProf(low_prof)
	self.low_prof = low_prof
end

function LoginView:SetLowSex(low_sex)
	self.low_sex = low_sex
end

function LoginView:SetSelectProf(select_prof)
	self.select_prof = select_prof
end

function LoginView:SetSelectSex(select_sex)
	self.select_sex = select_sex
end

function LoginView:OnClickSelectProf(sex, prof)
	if self.select_prof == prof and self.select_sex == sex then
		return
	end

	if not IsNil(self.scene_bloom) then
		self.scene_bloom.enabled = false
	end

	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.createRoleCG, sex, prof)
	self:OnProfAndSexToggleChange(prof, sex)
end

function LoginView:OnProfAndSexToggleChange(prof, sex)
	self:SetSelectProf(prof)
	self:SetSelectSex(sex)
	self:ChangeRight(prof)
	self:DestroyDrawObj()
	self:OnCGChange()
end

function LoginView:OnChangeToCreate(show_sex, show_prof)
	if not self:IsOpen() then
		return
	end

	local save_last_server = UtilU3d.GetCacheData("no_enter_scene_server")
	if save_last_server ~= nil and save_last_server ~= 0 then
		UtilU3d.CacheData("no_enter_scene_server", nil)
		InitWGCtrl:HideLoading()
	end

	PlayerPrefsUtil.SetString("agree_abide_protocol", "1")

	self.view_show_status = LoginViewStatus.CreateRole
	self:ChangeViewShowStatus()

	local prof = show_prof or self.low_prof or GameEnum.ROLE_PROF_1
	local sex = show_sex or self.low_sex or GameEnum.MALE
	local select_index = 1
	for k,v in ipairs(create_character_sex_prof_list) do
		if sex == v.sex and prof == v.prof then
			select_index = k
			break
		end
	end

	local item_cell = self.create_character_list and self.create_character_list[select_index]
	if item_cell then
		self:OnClickCreateCharacterRenderCB(item_cell)
	end

	-- if self.create_character_cambered_list then
	-- 	local btn_item_list = self.create_character_cambered_list:GetRenderList()
	-- 	local item_cell = btn_item_list[select_index]
	-- 	self:OnClickCCCLBtn(item_cell)
	-- end
end

-----------------------------------------------------------------------------------
----------------------------------   CG  ------------------------------------------
-----------------------------------------------------------------------------------
-- CG预加载
function LoginView:PreLoadCG(callback)
	--解决创角CG加载时卡顿太久问题，CG预制体放在场景Main/CGPos下
	-- local cgpos_node = UnityEngine.GameObject.Find("CGPos")
	for k,prof_list in pairs(cg_info_list) do
		for _,v in pairs(prof_list) do
			local cg_asset = v.asset
			-- local cg_bundle = v.bundle
			local cg_key = cg_bundle .. cg_asset
			local cg_obj = GameObject.Find(string.format("Main/CGPos/%s", cg_asset))
			if nil == cg_obj or IsNil(cg_obj.gameObject) then
				ResMgr:LoadGameobjAsync(cg_bundle, cg_asset, function(cg_obj)
					if cg_obj then
						cg_obj:SetActive(false)
						self.cg_instance_list[cg_key] = cg_obj
					end
				end, cgpos_node)
				print_error(string.format("CG资源未放到Main/CGPos下   资源为：%s ", cg_asset))
			else
				cg_obj:SetActive(false)
				self.cg_instance_list[cg_key] = cg_obj
			end
		end
	end

	if callback then
		callback()
	end
end

function LoginView:ClearCGInstanceList()
	if self.cg_instance_list then
		for k,v in pairs(self.cg_instance_list) do
			ResMgr:Destroy(v, ResPoolReleasePolicy.DestroyQuick)
		end
	end
	self.cg_instance_list = {}
end

function LoginView:HideAllCGInstance()
	for k, v in pairs(self.cg_instance_list) do
		v.gameObject:SetActive(false)
	end
end

function LoginView:ClearCgObj()
	if self.current_cg_obj then
		ResMgr:Destroy(self.current_cg_obj)
		self.current_cg_obj = nil
	end
end

function LoginView:ClearCGHander()
	if self.cg_handler ~= nil then
		GlobalTimerQuest:CancelQuest(self.cg_handler)
		self.cg_handler = nil
	end
end

function LoginView:OnCGChange()
	self.is_in_chuchang_anim = false
	self.is_init_role_model = false
	self.last_select_role_item_index = 0
	self.change_role_btn_click_timestamp = 0
	GlobalTimerQuest:CancelQuest(self.chuchang_enter_anim_timer)
	GlobalTimerQuest:CancelQuest(self.chuchang_out_anim_timer)

	self.cg_chuchang = nil
	self.cg_to_idle = nil
	self.cg_zhujue = nil
	local bundle = chuangjue_bundle
	local asset = chuangjue_asset

	self:ClearCGHander()

	self.role_rotate_area:SetActive(false)
		-- CG 切换
	local change_scene_func = function (bundle, asset, cg_key, cg_obj)
		self:ChangeScene(bundle, asset, function()
			if self.cg_instance_list == nil or cg_key == nil  then
				return
			end

			if not cg_obj and not self.cg_instance_list[cg_key] then
				return
			end

			local cg_instance = self.cg_instance_list[cg_key]
			if not cg_instance then
				cg_instance = cg_obj
			else
				cg_obj = cg_instance
			end

			if IsNil(cg_instance) then
				return
			end

			local key = bundle .. asset
			local center = nil
			if self.scene_cache[key] then
				local objs = self.scene_cache[key].roots
				for i = 0, objs.Length - 1 do
					if objs[i] and objs[i].gameObject.name == "Main" then
						center = objs[i].gameObject.transform:Find("CGPos")
					end
				end
			end

			if not IsNil(self.current_cg_obj) then
				self.current_cg_obj:SetActive(false)
			end

			if not IsNil(cg_obj) then
				cg_obj:SetActive(true)
			end
			self.current_cg_obj = cg_obj

			cg_instance.transform:SetParent(center.transform)
			cg_instance.transform.localPosition = Vector3.zero
			cg_instance.transform.localRotation = Quaternion.identity

			if self.cg_chuchang then
				self.cg_chuchang:Stop()
				self.cg_chuchang = nil
			end

			self.cg_ctrl = cg_instance:GetComponent(typeof(CGController))
			if self.cg_ctrl then
				self.cg_ctrl:SetPlayEndCallback(BindTool.Bind(self.CGPlayEndCallBack, self))
			end
			
			local cg_info = cg_info_list[self.select_sex] and cg_info_list[self.select_sex][self.select_prof]
			local chuchang_stage = cg_info and cg_info.chuchang_stage or "stage"
			local chuchang = cg_instance.transform:Find(chuchang_stage)
			if chuchang then
				self.cg_chuchang = chuchang:GetComponent(typeof(UnityEngine.Playables.PlayableDirector))
				self:CGPlayChangeUIShow(false)
				self:StopLoginMusic()
				self.cg_chuchang:Play()

				self:RemoveShowEffectDelayTimer()
				self.show_effect_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
					self:RemoveShowEffectDelayTimer()
					self:CGPlayChangeUIShow(true)
				end, 0.2)
			end

			-- self:ClearCGHander()
			-- if self.cg_chuchang then
				-- self.cg_handler = GlobalTimerQuest:AddDelayTimer(
				-- 	function()
				-- 		if not IsNil(self.cg_chuchang) then
				-- 			self:CGPlayEndCallBack()
				-- 		end
				-- 	end,
				-- self.cg_chuchang.duration)
			-- end
		end)
	end

	local cg_info = cg_info_list[self.select_sex] and cg_info_list[self.select_sex][self.select_prof]
	local cg_asset = cg_info and cg_info.asset
	-- local cg_bundle = cg_info and cg_info.bundle
	-- 加载场景
	local cg_key = cg_bundle .. cg_asset
	if not self.cg_instance_list[cg_key] then
		-- ResMgr:LoadGameobjSync(cg_bundle, cg_asset, function(cg_obj)
		-- 	if not cg_obj then
		-- 		return
		-- 	end

		-- 	self.cg_instance_list[cg_key] = cg_obj
		-- 	change_scene_func(bundle, asset, cg_key, cg_obj)
		-- end)
		print_error(string.format("CG资源未放到Main/CGPos下   资源为：%s ", cg_asset))
	else
		change_scene_func(bundle, asset, cg_key)
	end
end

--移除回调
function LoginView:RemoveShowEffectDelayTimer()
    if self.show_effect_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_effect_delay_timer)
        self.show_effect_delay_timer = nil
    end
end

function LoginView:CGPlayChangeUIShow(is_show)
	self.node_list["return_btn"]:SetActive(is_show)
	self.node_list["prof_desc"]:SetActive(is_show)
	self.node_list["create_role_left"]:SetActive(is_show)
	self.node_list["create_role_right"]:SetActive(is_show)
	-- self.node_list["cg_skip_btn"]:SetActive(not is_show)

	-- if is_show then
	-- 	self:CamberedListScrollEnter()
	-- end
end

function LoginView:CGPlayEndCallBack()
	if not self:IsLoaded() then
		return
	end

	self:PlayLoginMusic()
	self:CGPlayChangeUIShow(true)
	-- ReportManager:Step(Report.STEP_CREATE_ROLE_CG_END)
	-- self.role_rotate_area:SetActive(true)
	-- GlobalTimerQuest:AddDelayTimer(function ()
		-- if not IsNil(self.dynamic_bone) then
		-- 	self.dynamic_bone.enabled = true
		-- end
	-- end, 0)

	-- local animator = not IsNil(self.cg_zhujue_model) and self.cg_zhujue_model:GetComponent(typeof(UnityEngine.Animator))
	-- if animator then
	-- 	animator:CrossFadeInFixedTime("cg_idle", 0.01)
	-- end

	self:ClearCGHander()
end

function LoginView:OnCGSkipBtn()
	self:ClearCGHander()
	self:StopCgAudioSource()
	
	if self.cg_ctrl then
		self.cg_ctrl:SkipToEnd()
	end
end

function LoginView:StopCgAudioSource()
	if nil ~= self.current_cg_obj then
		local audio_list = self.current_cg_obj:GetComponentsInChildren(typeof(UnityEngine.AudioSource))

		for i = 0, audio_list.Length - 1 do
			local audio = audio_list[i]

			if audio then
				audio:Stop()
				-- audio.enabled = false
			end
		end
	end
end


-- 角色被拖转动事件
function LoginView:OnCreateoleModelDrag(data)
	if self.cg_zhujue and self.cg_zhujue_rotate_cache then
		local cache = self.cg_zhujue_rotate_cache
		self.cg_zhujue_rotate_cache = {x = cache.x, y = - data.delta.x * 0.25 + cache.y, z = cache.z}
		self.cg_zhujue.transform.localRotation = Quaternion.Euler(cache.x, cache.y, cache.z)
	end
end

function LoginView:ResetCreateBeforeParam()
	if not IsNil(self.scene_bloom) then
		self.scene_bloom.enabled = false
	end

	self:ClearCGHander()
	self:HideAllCGInstance()

	self:DestroyDrawObj()
	self.select_sex = -1
	self.select_prof = -1
	self.cccl_drag_select_index = -1
	self.cccl_btn_select_index = -1

	self:ChangeToSceneCamera()
	self:PlayLoginMusic()
end


-- 返回按钮
function LoginView:OnCreateReturnClick()
	if self.select_role_item_click_time and (Status.NowTime - self.select_role_item_click_time < 1) then
		return
	end

	if not IsNil(self.scene_bloom) then
		self.scene_bloom.enabled = false
	end

	self:ClearCGHander()
	self:HideAllCGInstance()

	self:DestroyDrawObj()
	self.select_sex = -1
	self.select_prof = -1
	self.cccl_drag_select_index = -1
	self.cccl_btn_select_index = -1

	local role_list_ack_info = LoginWGData.Instance:GetRoleListAck()
	if self.view_show_status == LoginViewStatus.CreateRole and role_list_ack_info.count > 0 then
		self:OnChangeToSelectRole()
		self:PlayLoginMusic()
	else
		LoginWGData.Instance:SetCurrSelectRoleId(-1)
		GameNet.Instance:ResetLoginServer()

		self.view_show_status = LoginViewStatus.HomePage
		self:ChangeViewShowStatus()
		self:SetCurServerInfo()

		self.last_select_role_item_index = 0
		self.change_role_btn_click_timestamp = 0
	end
end

-- 职业描述
function LoginView:ChangeRight(prof, sex)
	if not self:IsLoaded() then
		return
	end

	sex = sex or self.select_sex
	local img_asset, img_name = ResPath.GetProfRawImagesPNG(sex, prof)
	self.node_list["prof_name"].raw_image:LoadSprite(img_asset, img_name, function()
		self.node_list["prof_name"].raw_image:SetNativeSize()
	end)

	local bundle, asset = RoleWGData.Instance:GetJobProfEffect(sex, prof)
    if bundle and asset then
        self.node_list["prof_name"]:ChangeAsset(bundle, asset)
    end

	self.node_list.prof_title_text.text.text = RoleWGData.Instance:GetJobName1(sex, prof)
	local desc = RoleWGData.Instance:GetJobProfDesc(sex, prof)
	self.node_list.desc_name_1.text.text = desc and desc[1] or ""
	self.node_list.desc_name_2.text.text = desc and desc[2] or ""
end

function LoginView:ChangeToSceneCamera()
	if not IsNil(self.scene_camera) then
		self.scene_camera.enabled = true
	end
end

function LoginView:CloseSceneCamera()
	if not IsNil(self.scene_camera) then
		self.scene_camera.enabled = false
	end
end

function LoginView:OnGoToDIYBtn()
	LoginWGCtrl.Instance:ChangeLoginViewShow(false)
	self:DestroyDrawObj()
	self:ClearCGHander()
	self:HideAllCGInstance()

	self:ChangeToSceneCamera()
	self:SetSceneWatersNodeShow(true)
	
	--LoginWGCtrl.Instance:OpenCreateDIYView({sex = self.select_sex, prof = self.select_prof})

	self:PlayLoginMusic()

	local data = {}
	data.operate_type = ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL
	data.sex = self.select_sex
	data.prof = self.select_prof
	data.country = 1
	--RoleDiyAppearanceWGCtrl.Instance:SetViewDataAndOpen(data)
	LoginWGCtrl.Instance:OpenShowRoleAppranceView(data)
end

function LoginView:OnDIYToCreate()
	self:StopLoginMusic()
	self:CloseSceneCamera()
	self:SetSceneWatersNodeShow(false)
	self:OnCGChange()
end

-- 美术设计缺陷，播CG时需屏蔽水面
function LoginView:SetSceneWatersNodeShow(is_show)
	if self.scene_waters_show_state == is_show then
		return
	end

	self.scene_waters_show_state = is_show
	if not self.scene_waters_node then
		self.scene_waters_node = UnityEngine.GameObject.Find("Waters")
	end

	if not self.scene_models_node then
		self.scene_models_node = UnityEngine.GameObject.Find("Models")
	end

	if not self.scene_waters_node then
		print_error("注意！！！ 场景中没有Waters节点")
	else
		self.scene_waters_node:SetActive(is_show)
	end

	if not self.scene_models_node then
		print_error("注意！！！ 场景中没有Models节点")
	else
		self.scene_models_node:SetActive(is_show)
	end
end

------------------------------------- 环形列表格子
CreateCharacterCamberedRander = CreateCharacterCamberedRander or BaseClass(BaseRender)
function CreateCharacterCamberedRander:__init(instance)
	local bundle = "uis/view/login_ui_prefab"
	local asset = "layout_create_character_render"
	self:LoadAsset(bundle, asset, instance.transform)

	self.click_callback = nil
end

function CreateCharacterCamberedRander:__delete()
	self.click_callback = nil
end

function CreateCharacterCamberedRander:LoadCallBack()
	self.is_shield = false
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

-- 设置点击回调
function CreateCharacterCamberedRander:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function CreateCharacterCamberedRander:OnClick()
	if self.click_callback then
		self.click_callback(self)
	end
end

function CreateCharacterCamberedRander:OnFlush()
	if not self.data then
		return
	end

	self.is_shield = RoleWGData.Instance:GetIsShieldSexAndProf(self.data.sex, self.data.prof)
	self.node_list.lock:SetActive(self.is_shield)
	-- local cfg = RoleWGData.Instance:GetJobConfig(self.data.sex, self.data.prof)
	-- self.node_list.name.text.text = cfg and cfg.name1 or ""
	local bundle, asset = ResPath.GetProfIcon(self.data.sex, self.data.prof % 10)
	self.node_list.nor_icon.image:LoadSprite(bundle, asset, function ()
		self.node_list.nor_icon.image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetProfIcon(self.data.sex, self.data.prof % 10, true)
	self.node_list.select_icon.image:LoadSprite(bundle, asset, function ()
		self.node_list.select_icon.image:SetNativeSize()
	end)
end

function CreateCharacterCamberedRander:SetSelectedHL(index)
	local is_select = self.index == index

	self.node_list.nor_node:SetActive(not is_select)
	self.node_list.select_node:SetActive(is_select)
end