-- Y-运营活动-一线牵.xls
local item_table={
[1]={item_id=48077,num=1,is_bind=1},
[2]={item_id=22013,num=1,is_bind=1},
[3]={item_id=48078,num=1,is_bind=1},
[4]={item_id=48071,num=3,is_bind=1},
[5]={item_id=44183,num=1,is_bind=1},
[6]={item_id=26444,num=1,is_bind=1},
[7]={item_id=22753,num=1,is_bind=1},
[8]={item_id=44184,num=1,is_bind=1},
[9]={item_id=26450,num=1,is_bind=1},
[10]={item_id=44185,num=1,is_bind=1},
[11]={item_id=26455,num=1,is_bind=1},
[12]={item_id=26191,num=1,is_bind=1},
[13]={item_id=26460,num=1,is_bind=1},
[14]={item_id=48145,num=1,is_bind=1},
[15]={item_id=26464,num=1,is_bind=1},
[16]={item_id=26377,num=2,is_bind=1},
[17]={item_id=44182,num=1,is_bind=1},
[18]={item_id=26377,num=5,is_bind=1},
[19]={item_id=26377,num=10,is_bind=1},
[20]={item_id=22061,num=1,is_bind=1},
[21]={item_id=26459,num=1,is_bind=1},
[22]={item_id=26380,num=1,is_bind=1},
[23]={item_id=26180,num=3,is_bind=1},
[24]={item_id=26180,num=6,is_bind=1},
[25]={item_id=26180,num=8,is_bind=1},
[26]={item_id=26180,num=12,is_bind=1},
[27]={item_id=26180,num=14,is_bind=1},
[28]={item_id=26180,num=26,is_bind=1},
[29]={item_id=26180,num=4,is_bind=1},
[30]={item_id=26180,num=7,is_bind=1},
[31]={item_id=26180,num=13,is_bind=1},
[32]={item_id=26180,num=9,is_bind=1},
[33]={item_id=26180,num=18,is_bind=1},
[34]={item_id=26180,num=27,is_bind=1},
[35]={item_id=26180,num=16,is_bind=1},
[36]={item_id=26180,num=20,is_bind=1},
[37]={item_id=26180,num=40,is_bind=1},
[38]={item_id=26180,num=60,is_bind=1},
[39]={item_id=26180,num=24,is_bind=1},
[40]={item_id=26180,num=36,is_bind=1},
[41]={item_id=26437,num=1,is_bind=1},
[42]={item_id=26180,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=8,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
reward_pool={
{},
{seq=1,item=item_table[1],},
{seq=2,item=item_table[2],},
{seq=3,item=item_table[3],},
{seq=4,item=item_table[4],},
{seq=5,item=item_table[5],},
{seq=6,item=item_table[6],},
{seq=7,item=item_table[7],},
{seq=8,item=item_table[8],},
{seq=9,item=item_table[9],},
{seq=10,item=item_table[10],},
{seq=11,item=item_table[11],},
{seq=12,item=item_table[12],},
{seq=13,item=item_table[13],},
{seq=14,item=item_table[14],},
{seq=15,item=item_table[15],},
{grade=2,},
{grade=2,item=item_table[16],},
{grade=2,item=item_table[17],},
{grade=2,item=item_table[18],},
{grade=2,item=item_table[5],},
{grade=2,item=item_table[6],},
{grade=2,item=item_table[19],},
{grade=2,item=item_table[20],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,item=item_table[21],},
{grade=2,},
{grade=2,item=item_table[22],},
{grade=2,}
},

reward_pool_meta_table_map={
[30]=14,	-- depth:1
[29]=13,	-- depth:1
[28]=12,	-- depth:1
[27]=11,	-- depth:1
[26]=10,	-- depth:1
[25]=9,	-- depth:1
[21]=5,	-- depth:1
[23]=7,	-- depth:1
[22]=6,	-- depth:1
[20]=4,	-- depth:1
[19]=3,	-- depth:1
[18]=2,	-- depth:1
[31]=15,	-- depth:1
[24]=8,	-- depth:1
[32]=16,	-- depth:1
},
special_reward={
{},
{seq=1,condition="4|5|6|7",},
{seq=2,condition="8|9|10|11",},
{seq=3,condition="12|13|14|15",},
{seq=4,condition="0|4|8|12",},
{seq=5,condition="1|5|9|13",},
{seq=6,condition="2|6|10|14",},
{seq=7,condition="3|7|11|15",},
{seq=8,is_rare=1,condition="0|1|2|3|4|5|6|7",item=item_table[15],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

special_reward_meta_table_map={
[17]=8,	-- depth:1
[11]=2,	-- depth:1
[12]=3,	-- depth:1
[13]=4,	-- depth:1
[14]=5,	-- depth:1
[15]=6,	-- depth:1
[16]=7,	-- depth:1
[18]=9,	-- depth:1
},
consume={
{},
{times=2,cost_item_num=3,},
{times=3,cost_item_num=6,},
{times=4,cost_item_num=10,},
{times=5,cost_item_num=15,},
{times=6,cost_item_num=20,},
{times=7,cost_item_num=25,},
{times=8,cost_item_num=30,},
{times=9,cost_item_num=38,},
{times=10,cost_item_num=46,},
{times=11,cost_item_num=55,},
{times=12,cost_item_num=65,},
{times=13,cost_item_num=80,},
{times=14,cost_item_num=95,},
{times=15,cost_item_num=110,},
{times=16,cost_item_num=125,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

consume_meta_table_map={
[30]=14,	-- depth:1
[29]=13,	-- depth:1
[28]=12,	-- depth:1
[27]=11,	-- depth:1
[26]=10,	-- depth:1
[25]=9,	-- depth:1
[21]=5,	-- depth:1
[23]=7,	-- depth:1
[22]=6,	-- depth:1
[20]=4,	-- depth:1
[19]=3,	-- depth:1
[18]=2,	-- depth:1
[31]=15,	-- depth:1
[24]=8,	-- depth:1
[32]=16,	-- depth:1
},
task={
{task_type=1,param1=30,des="获得活跃点",target=30,open_panel="bizuo#bizuo_bizuo",},
{task_id=1,task_type=1,des="获得活跃点",open_panel="bizuo#bizuo_bizuo",},
{task_id=2,param1=90,target=90,},
{task_id=3,param1=120,target=120,},
{task_id=4,param1=160,target=160,},
{task_id=5,param1=200,target=200,},
{task_id=6,task_type=2,param1=1,item_list={[0]=item_table[23]},des="充值任意金额",target=1,open_panel="vip#recharge_cz",},
{task_id=7,task_type=3,param1=1,des="每日登录",target=1,open_panel="",},
{task_id=8,param1=500,target=500,},
{task_id=9,param1=1000,target=1000,},
{task_id=10,param1=1500,target=1500,},
{task_id=11,param1=2000,target=2000,},
{task_id=12,param1=2500,target=2500,},
{task_id=13,param1=3000,target=3000,},
{task_id=14,param1=3500,target=3500,},
{task_id=15,param1=4000,target=4000,},
{task_id=16,param1=4500,target=4500,},
{task_id=17,param1=5000,target=5000,},
{task_id=18,param1=300,item_list={[0]=item_table[24]},target=300,},
{task_id=19,task_type=5,param1=680,item_list={[0]=item_table[25]},des="累计充值灵玉",target=680,open_panel="vip#recharge_cz",},
{task_id=20,param1=1280,item_list={[0]=item_table[26]},target=1280,},
{task_id=21,param1=1980,item_list={[0]=item_table[27]},target=1980,},
{task_id=22,param1=3280,item_list={[0]=item_table[28]},target=3280,},
{task_id=23,task_type=6,param1=300,item_list={[0]=item_table[23]},des="累计消费灵玉",target=300,open_panel="shop#Tab_Shop30",},
{task_id=24,param1=680,item_list={[0]=item_table[29]},target=680,},
{task_id=25,param1=1280,item_list={[0]=item_table[24]},target=1280,},
{task_id=26,param1=1980,item_list={[0]=item_table[30]},target=1980,},
{task_id=27,param1=3280,item_list={[0]=item_table[31]},target=3280,},
{task_id=28,param1=10,item_list={[0]=item_table[29]},target=10,},
{task_id=29,task_type=7,param2=1,item_list={[0]=item_table[30]},des="天地至宝寻宝",open_panel="TreasureHunt#treasurehunt_equip",},
{task_id=30,param1=100,item_list={[0]=item_table[32]},target=100,},
{task_id=31,param1=200,item_list={[0]=item_table[33]},target=200,},
{task_id=32,param1=350,item_list={[0]=item_table[34]},target=350,},
{task_id=33,param1=10,item_list={[0]=item_table[25]},target=10,},
{task_id=34,task_type=7,param2=2,item_list={[0]=item_table[35]},des="沧溟珍宝寻宝",open_panel="TreasureHunt#treasurehunt_dianfeng",},
{task_id=35,param1=100,item_list={[0]=item_table[36]},target=100,},
{task_id=36,param1=200,item_list={[0]=item_table[37]},target=200,},
{task_id=37,param1=350,item_list={[0]=item_table[38]},target=350,},
{task_id=38,param1=10,item_list={[0]=item_table[26]},target=10,},
{task_id=39,param1=20,target=20,},
{task_id=40,param1=40,target=40,},
{task_id=41,task_type=8,param1=60,item_list={[0]=item_table[39]},des="战纹秘宝寻宝",target=60,open_panel="TreasureHunt#treasurehunt_fuwen",},
{task_id=42,param1=90,item_list={[0]=item_table[40]},target=90,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

task_meta_table_map={
[58]=15,	-- depth:1
[57]=14,	-- depth:1
[61]=18,	-- depth:1
[56]=13,	-- depth:1
[55]=12,	-- depth:1
[54]=11,	-- depth:1
[53]=10,	-- depth:1
[52]=9,	-- depth:1
[60]=17,	-- depth:1
[59]=16,	-- depth:1
[45]=2,	-- depth:1
[44]=1,	-- depth:1
[6]=1,	-- depth:1
[5]=1,	-- depth:1
[4]=1,	-- depth:1
[3]=1,	-- depth:1
[73]=30,	-- depth:1
[46]=3,	-- depth:2
[49]=6,	-- depth:2
[48]=5,	-- depth:2
[51]=8,	-- depth:1
[78]=35,	-- depth:1
[47]=4,	-- depth:2
[41]=42,	-- depth:1
[43]=42,	-- depth:1
[39]=42,	-- depth:1
[21]=20,	-- depth:1
[22]=20,	-- depth:1
[23]=20,	-- depth:1
[25]=24,	-- depth:1
[26]=24,	-- depth:1
[27]=24,	-- depth:1
[28]=24,	-- depth:1
[19]=20,	-- depth:1
[40]=39,	-- depth:2
[67]=24,	-- depth:1
[65]=22,	-- depth:2
[69]=26,	-- depth:2
[70]=27,	-- depth:2
[71]=28,	-- depth:2
[82]=39,	-- depth:2
[83]=40,	-- depth:3
[84]=41,	-- depth:2
[66]=23,	-- depth:2
[68]=25,	-- depth:2
[64]=21,	-- depth:2
[62]=19,	-- depth:2
[85]=42,	-- depth:1
[38]=35,	-- depth:1
[37]=35,	-- depth:1
[36]=35,	-- depth:1
[63]=20,	-- depth:1
[34]=35,	-- depth:1
[86]=43,	-- depth:2
[33]=30,	-- depth:1
[32]=30,	-- depth:1
[50]=7,	-- depth:1
[31]=30,	-- depth:1
[29]=30,	-- depth:1
[74]=31,	-- depth:2
[75]=32,	-- depth:2
[77]=34,	-- depth:2
[79]=36,	-- depth:2
[80]=37,	-- depth:2
[81]=38,	-- depth:2
[72]=29,	-- depth:2
[76]=33,	-- depth:2
},
other_default_table={cost_item_id=26180,cost_gold=80,},

open_day_default_table={start_day=1,end_day=7,grade=1,},

reward_pool_default_table={grade=1,seq=0,item=item_table[41],},

special_reward_default_table={grade=1,seq=0,is_rare=0,condition="0|1|2|3",item=item_table[12],},

consume_default_table={grade=1,times=1,cost_item_num=1,},

task_default_table={grade=1,task_id=0,task_type=4,param1=50,param2=0,item_list={[0]=item_table[42]},des="仙图达到名望",target=50,open_panel="counrty_map_task_view",}

}

