CrossConsumeRankWGData = CrossConsumeRankWGData or BaseClass()

--消费排行榜的枚举
CROSS_CONSUME_RANK_OPERATE_TYPE = {
    RANK_INFO = 1
}

function CrossConsumeRankWGData:__init()
    if CrossConsumeRankWGData.Instance then
        error("[CrossConsumeRankWGData] Attempt to create singleton twice!")
		return
    end

    CrossConsumeRankWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("cross_consume_rank_auto")
    self.rank_reward_cfg = ListToMapList(cfg.rank_reward, "grade")

    self.grade = 0
    self.self_rank = 0
    
end

function CrossConsumeRankWGData:__delete()
    CrossConsumeRankWGData.Instance = nil
end

--协议信息
function CrossConsumeRankWGData:SetConRankInfo(protocol)
    self.grade = protocol.grade    --当前轮次
    self.self_rank_value = protocol.self_rank_value     --累计充值数
    self.rank_item_list = protocol.rank_item_list or {}       --上榜信息
    self.self_rank = protocol.self_rank                 --自己的排行信息
	self:SetCrossConsumeRankInfo()
end

--返回当前自己的排行信息
function CrossConsumeRankWGData:GetMyRankData()
    return self.self_rank
end

--返回当前轮次
function CrossConsumeRankWGData:GetConsumeGrade()
    return self.grade
end

--消费榜奖励配置
function CrossConsumeRankWGData:GetConRankListCfg()
    return self.rank_reward_cfg or {}
end

--返回当前自己的累计消费数
function CrossConsumeRankWGData:GetSelfRankValue()
    return self.self_rank_value or 0
end

--通过当前轮次获取配置信息
function CrossConsumeRankWGData:GetConsumeRankCfg(grade)
    return self.rank_reward_cfg[grade] or {}
end

--没有上榜
function CrossConsumeRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, rank_value = 0}
end

function CrossConsumeRankWGData:ExpandRankData(rank_list, nNextIndex, nCurRank, index)
	local next_data = rank_list[nNextIndex]
	if next_data then
		local nNextRank = next_data.rank
		if nCurRank + 1 ~= nNextRank then -- 说明断了 要补一下
			for nSectionIndex = nCurRank + 1, nNextRank - 1 do
				local data = {}
				data.no_true_rank = true
				data.index = index
				data.rank_data = self:CreatNoRankItemData(nSectionIndex)
				table.insert(self.consumeRankdata_rank_list, data)
				index = index + 1
			end
		end
	end
	return index
end

function CrossConsumeRankWGData:SetCrossConsumeRankInfo()
	self.consumeRankdata_rank_list = {}

	local index = 1
	local cfg = self:GetConsumeRankCfg(self.grade)
	--self.cur_sex = protocol.sex_type
	--self.my_charm = protocol.my_charm
	for i = 1, #self.rank_item_list do
		if i == 1 then
			if self.rank_item_list[i].rank ~= 1 then
				local item = {}
				item.rank_data = self:CreatNoRankItemData(1)
				item.index = index
				item.no_true_rank = true
				index = index + 1
				table.insert(self.consumeRankdata_rank_list, item)
				index = self:ExpandRankData(self.rank_item_list, 1, 1, index)
			end
		end

		local item = {}
		item.index = index
		item.rank_data = self.rank_item_list[i]

		local nCurRank = item.rank_data.rank
		table.insert(self.consumeRankdata_rank_list, item)
		index = index + 1

		index = self:ExpandRankData(self.rank_item_list, i + 1, nCurRank, index)
	end

	local rank_cfg = cfg[#cfg]
	local nMaxRank = rank_cfg.max_rank
	if index - 1 < nMaxRank then
		for nSectionIndex = index, nMaxRank do
			local data = {}
			data.no_true_rank = true
			data.index = nSectionIndex
			data.rank_data = self:CreatNoRankItemData(nSectionIndex)
			table.insert(self.consumeRankdata_rank_list, data)
		end
	end
end

--返回排行榜的数据
function CrossConsumeRankWGData:GetRankInfo()
	return  self.consumeRankdata_rank_list or {}
end

--获取到对应区间的配置表信息
function CrossConsumeRankWGData:GetRankItemData(rank_data)
	local rank_cfg = CrossConsumeRankWGData.Instance:GetConsumeRankCfg(self.grade)
	if rank_cfg ~= nil and rank_data ~= nil then
		for _,v in ipairs(rank_cfg) do
			if rank_data.rank >= v.min_rank and rank_data.rank <= v.max_rank then
				return v
			end
		end
	end

	return nil
end

--获取对应排行榜的金额信息
function CrossConsumeRankWGData:GetMoneyNum(rank)
    --获取到排行榜信息
    local consume_rank_list = self:GetRankInfo()
    for k, v in ipairs(consume_rank_list) do
        if rank == v.rank_data.rank then
            return v.rank_data.rank_value or 0
        end
    end

    return 0
end

--获取榜一的信息
function CrossConsumeRankWGData:GetTopRankInfo()
	local data_list = {}
	local rank_cfg = self:GetRankInfo()
	if rank_cfg == nil then
		return data_list
	end

	if rank_cfg[1] == nil then
		return data_list
	end

	table.insert(data_list, rank_cfg[1])
	return data_list
end


