--合服活动子标签
MergeTabButton = MergeTabButton or BaseClass(BaseRender)
function MergeTabButton:LoadCallBack()

end
function MergeTabButton:__delete()

end
function MergeTabButton:OnFlush(param_t)
    for k, v in pairs(param_t or {"all"}) do
        if k == "FlushSelectState" then
            self:_FlushSelect(v[1] == self.sub_index)
        end
    end
end

function MergeTabButton:SetSubIndex(index)
    self.sub_index = index
end

function MergeTabButton:_FlushSelect(is_select)
    self.node_list.img_hl:SetActive(is_select)
    self.node_list.img_normal:SetActive(not is_select)
    self.node_list.text_hl:SetActive(is_select)
    self.node_list.text_normal:SetActive(not is_select)
end