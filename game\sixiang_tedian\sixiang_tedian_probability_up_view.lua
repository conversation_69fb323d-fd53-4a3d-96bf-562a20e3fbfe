SiXiangTeDianProUpView = SiXiangTeDianProUpView or BaseClass(SafeBaseView)

function SiXiangTeDianProUpView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(896, 540)})
    self:AddViewResource(0, "uis/view/sixiang_call_prefab", "layout_probability_view")
	self:SetMaskBg(true, true)
end

function SiXiangTeDianProUpView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitTeDianUpList()
end

function SiXiangTeDianProUpView:ReleaseCallBack()
	if self.hunjing_item_list then
		for k,v in pairs(self.hunjing_item_list) do
			v:DeleteMe()
		end
	end
	self.hunjing_item_list = nil

	if self.hungu_item_list then
		for k,v in pairs(self.hungu_item_list) do
			v:DeleteMe()
		end
	end
	self.hungu_item_list = nil
end

function SiXiangTeDianProUpView:OnFlush(param_t)
	self:RefreshView()
end

function SiXiangTeDianProUpView:InitParam()
	self.save_now_cycle = -1
end

function SiXiangTeDianProUpView:InitPanel()
	self.node_list.title_view_name.text.text = Language.SiXiangCall.ViewName_ProbabilityUpView
end

function SiXiangTeDianProUpView:RefreshView()
	self:InitTeDianUpList()
end

function SiXiangTeDianProUpView:InitTeDianUpList()
	local cycle = SiXiangTeDianWGData.Instance:GetCycle()
	if self.save_now_cycle ~= cycle then
		self.save_now_cycle = cycle
		self:InitHunJingList()
		self:InitHunGuList()
	end
end

function SiXiangTeDianProUpView:InitHunJingList()
	local hunjing_cfg_list = SiXiangTeDianWGData.Instance:GetProUpCfgList(FIGHT_SOUL_ITEM_TYPE.SOUL)
	local hunjing_item_list = {}
	local parent_obj = self.node_list.item_list_1

	for i = #hunjing_item_list + 1, #hunjing_cfg_list do
		hunjing_item_list[i] = SiXiangTeDianProUpItem.New()
		hunjing_item_list[i]:DoLoad(parent_obj)
	end

	for i=1,#hunjing_item_list do
		hunjing_item_list[i]:SetData(hunjing_cfg_list[i])
	end

	parent_obj:SetActive(not IsEmptyTable(hunjing_cfg_list))

	self.hunjing_item_list = hunjing_item_list
end

function SiXiangTeDianProUpView:InitHunGuList()
	local hungu_cfg_list = SiXiangTeDianWGData.Instance:GetProUpCfgList(FIGHT_SOUL_ITEM_TYPE.BONE)
	local hungu_item_list = {}
	local parent_obj = self.node_list.item_list_2

	for i = #hungu_item_list + 1, #hungu_cfg_list do
		hungu_item_list[i] = SiXiangTeDianProUpItem.New()
		hungu_item_list[i]:DoLoad(parent_obj)
	end

	for i=1,#hungu_item_list do
		hungu_item_list[i]:SetData(hungu_cfg_list[i])
	end

	parent_obj:SetActive(not IsEmptyTable(hungu_cfg_list))

	self.hungu_item_list = hungu_item_list
end

----------------------------------------------------------------

SiXiangTeDianProUpItem = SiXiangTeDianProUpItem or BaseClass(SiXiangSummonItem)

function SiXiangTeDianProUpItem:DoLoad(parent)
	self:LoadAsset("uis/view/sixiang_call_prefab", "sixiang_proup_render", parent.transform)
end

function SiXiangTeDianProUpItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)

	self:SetItemID(data.item_id)
	self:SetItemStar(data.param0)
	self:FlushItemBg()
    self:FlushItemIcon()
    self:FlushItemStar()
    self:FlushItemName()
end