ZhuZaiEndRewardView = ZhuZaiEndRewardView or BaseClass(SafeBaseView)

function ZhuZaiEndRewardView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/zhuzaishendian_ui_prefab", "layout_zhongjie")
end

function ZhuZaiEndRewardView:__delete()

end
function ZhuZaiEndRewardView:ReleaseCallBack()
	if nil ~= self.zjreward_cells then
		self.zjreward_cells:DeleteMe()
		self.zjreward_cells = nil
	end
end

function ZhuZaiEndRewardView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.anchoredPosition = Vector2(-3.71,-24.71)
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(676.96,487.6)
	self.node_list.title_view_name.text.text = Language.ZhuZaiShenDian.ViewNameZhongJie

	self:InitZhuZaiEndRewardView()
end

function ZhuZaiEndRewardView:InitZhuZaiEndRewardView()
	self.zjreward_cells = ItemCell.New()
	self.zjreward_cells:SetInstanceParent(self.node_list["ph_zjreward"])
	self.node_list.img_sign:SetActive(false)
	local WinStreak = ZhuZaiShenDianWGData.Instance:GetLianShen()
	if WinStreak < 2 then
		self.zjreward_cells:SetActive(false)
		self.node_list.img_sign:SetActive(true)
	end
	self.node_list.rich_zhongjie.text.text = Language.ZhuZaiShenDian.ZhonJieDesc
	self.node_list.lbl_guwu.text.text = Language.ZhuZaiShenDian.GuWuDesc
	self.node_list.btn_min_close.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtnHandler, self))
	local WinStreak = ZhuZaiShenDianWGData.Instance:GetLianShen()
	local buff = ""
	if WinStreak > 1 then  
		local zjreward = ZhuZaiShenDianWGData.Instance:GetZhongJiecfg(WinStreak)
		if zjreward.challenger_add_gongji_per then 
			buff = Language.ZhuZaiShenDian.Buff..(zjreward.challenger_add_gongji_per / 100) ..Language.ZhuZaiShenDian.Fuhao
			self.node_list.lbl_zhongjie_buff.text.text = buff
		end
	end
end

function ZhuZaiEndRewardView:OnNorBossTips()

end

function ZhuZaiEndRewardView:OnClickExamine()

end

function ZhuZaiEndRewardView:GoToJoinAct()
	
end

function ZhuZaiEndRewardView:OnFlush(param_t, index)
	if self.zjreward_cells then
		local WinStreak = ZhuZaiShenDianWGData.Instance:GetLianShen()
		if WinStreak > 1 then  
			local zjreward = ZhuZaiShenDianWGData.Instance:GetZhongJieReward()
			if zjreward then 
				self.zjreward_cells:SetData(zjreward[0])
			end
		end
	end
end
function ZhuZaiEndRewardView:OnClickCloseBtnHandler()
	self:Close()
end