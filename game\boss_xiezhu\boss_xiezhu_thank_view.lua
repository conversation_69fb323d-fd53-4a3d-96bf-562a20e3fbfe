
BossXiezhuThanksView = BossXiezhuThanksView or BaseClass(SafeBaseView)
function BossXiezhuThanksView:__init()
	self:AddViewResource(0, "uis/view/boss_xiezhu_ui_prefab", "boss_xiezhu_thank")
	self:SetMaskBg()
end

function BossXiezhuThanksView:__delete()
end

function BossXiezhuThanksView:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function BossXiezhuThanksView:LoadCallBack(index, loaded_times)
	self.item_list = AsyncListView.New(ItemCell,self.node_list["item_list"])
	XUI.AddClickEventListener(self.node_list["btn_reward"], BindTool.Bind(self.ClickBtnReward,self))
	local reward_data = BossXiezhuWGData.Instance:GetThankReward()
	self.item_list:SetDataList(reward_data)
	self.node_list["thank_txt"].text.text = Language.BossXiezhu.ThankDesc
end

function BossXiezhuThanksView:OnFlush()
	
end

function BossXiezhuThanksView:ClickBtnReward()
	self:Close()
end
