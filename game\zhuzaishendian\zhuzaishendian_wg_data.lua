ZhuZaiShenDianWGData = ZhuZaiShenDianWGData or BaseClass()

function ZhuZaiShenDianWGData:__init()
	if ZhuZaiShenDianWGData.Instance then
		error("[ZhuZaiShenDianWGData] Attempt to create singleton twice!")
		return
	end
	ZhuZaiShenDianWGData.Instance = self
    self.other_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").other[1]
    RemindManager.Instance:Register(RemindName.ZhuZaiShenDian, BindTool.Bind(self.IsShowShenDianRedPoint, self))	  -- 主宰神殿
end

function ZhuZaiShenDianWGData:__delete()
	ZhuZaiShenDianWGData.Instance = nil
end

-- function ZhuZaiShenDianWGData:GetYaoShouCfg()
-- 	local mrrewrad_cfg = self.other_cfg.winner_day_reward
-- 	return mrrewrad_cfg
-- end

function ZhuZaiShenDianWGData:GetYaoShouCfgLength()
	local rewrad_Distribution = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").keep_win_reward
	return rewrad_Distribution
end

-- function ZhuZaiShenDianWGData:GetBangZhuRewardCfg()
-- 	local bzrewrad_cfg = self.other_cfg.bazhu_reward
-- 	return bzrewrad_cfg
-- end

function ZhuZaiShenDianWGData:SetGuildBattleRankInfo(protocol)
	self.xmz_rank_list = protocol.xmz_rank_list
	--print_error("ZhuZaiShenDianWGData",protocol.keep_win_times,protocol.xmz_rank_list)
	self.xmz_winner_bazhu_role_id = protocol.xmz_winner_bazhu_role_id
	self.keep_win_times = protocol.keep_win_times or 0
	self.allocate_list = protocol.allocate_list
end

function ZhuZaiShenDianWGData:GetLianShen()
	return self.keep_win_times or 0
end

function ZhuZaiShenDianWGData:SetRawardcfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local next_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").win_reward_show
	for k,v in pairs(next_cfg) do
		if type(v.day_max) == type(open_day) and type(v.day_min) == type(open_day) then
			if v.day_max > open_day  and  v.day_min <= open_day then
				return v.seq
			end
		end
	end
end

function ZhuZaiShenDianWGData:GetRawardLianShen()
	local next_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").win_reward_show
	local t_config = self:SetRawardcfg()
	if t_config == nil  then
		t_config = next_cfg[#next_cfg].seq + 1
	end

	local rewrad_config = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").keep_win_reward
	local world_level = RankWGData.Instance:GetWordLevel()
	for k,v in ipairs(rewrad_config) do
		if v.seq == t_config and world_level <= v.level_max and world_level >= v.world_level then
			return v
		end
	end
end

function ZhuZaiShenDianWGData:GetTotalReWard()
	local t_config = self:GetRawardLianShen()
	local config = {}
	if not t_config then return config end
	for i = 2, 100 do
		if i % 3 == 0 then
			local tab = {}
			table.insert(tab,t_config.keepwin_item)
			table.insert(tab,t_config.keepwin_extra_item)
			table.insert(config,tab)
		else
			table.insert(config,t_config.keepwin_item)
		end
	end
	return config
end
function ZhuZaiShenDianWGData:GetBaseLianShengCfg()
	--local cfg =  ConfigManager.Instance:GetAutoConfig("guild_battle_auto").keep_win_reward_new
	local offse =  ConfigManager.Instance:GetAutoConfig("guild_battle_auto").keep_win_reward_new
	local open_day =  TimeWGCtrl.Instance:GetCurOpenServerDay()
	local rand_t = {}
	local day = nil
	for k,v in pairs(offse) do
		if nil ~= v.opengame_day then
			if v and (nil == day or v.opengame_day == day) and open_day  <= v.opengame_day then
				--return v
				day = v.opengame_day
				table.insert(rand_t, v)
			end
		end
	end
	return rand_t or {}
end
function ZhuZaiShenDianWGData:GetGuildId()
	if self.xmz_rank_list then
		return self.xmz_rank_list[1] or {}
	end
end

function ZhuZaiShenDianWGData:GetZhongJieReward()
	local config = self:GetRawardLianShen()
	return config.shutdown_item or {}
end

function ZhuZaiShenDianWGData:GetZhongJiecfg(num)
	local lianshen = num
	local buff =  ConfigManager.Instance:GetAutoConfig("guild_battle_auto").keep_win_buff
	if lianshen > #buff then
		lianshen = #buff+ 1
	end

	for k,v in pairs(buff) do
		if v.keep_win_times == lianshen then
			return v
		end
	end
	return 0
end

function ZhuZaiShenDianWGData:SetRoleBaseInfoAck( protocol )
	if not protocol then
		--print_error("查询不到玩家信息")
		self.role_base_info_ack = nil
		return
	end
	
	self.role_base_info_ack = {}
	self.role_base_info_ack.appearance = protocol.appearance
	-- if fashion_cfg_list and protocol.appearance then
		-- local v = self:GetFahionBody(fashion_cfg_list,1, protocol.appearance.fashion_body)
		-- if v then
	-- self.role_base_info_ack.appearance.fashion_body = protocol.appearance.fashion_body
		-- end
	-- end
	self.role_base_info_ack.role_id = protocol.role_id
	self.role_base_info_ack.role_name = protocol.role_name
	self.role_base_info_ack.prof = protocol.prof
	self.role_base_info_ack.sex = protocol.sex
end
function ZhuZaiShenDianWGData:GetFahionBody(fashion_cfg_list,part_type,index)
	for k, v in pairs(fashion_cfg_list) do
		if v.part_type == part_type and index == v.index then
			return v
		end
	end
	return nil
end
function ZhuZaiShenDianWGData:GetRoleBaseInfoAck()
	if not self.role_base_info_ack then
		--print_error("没有相关角色信息")
		self.role_base_info_ack = nil
		return
	end
	return self.role_base_info_ack
end


--盟主uid

function ZhuZaiShenDianWGData:GetMengZhuUid()
	return self.xmz_winner_bazhu_role_id
end

function ZhuZaiShenDianWGData:SetCityOwnerInfo(info)
	if not info then
		self.cityowner_info = nil
	else
		self.cityowner_info = {}
		self.cityowner_info.role_name = info.role_name
		self.cityowner_info.appearance = info.appearance
		self.cityowner_info.sex = info.sex
		self.cityowner_info.prof = info.prof
	end
end

function ZhuZaiShenDianWGData:GetCityOwnerInfo()
	return self.cityowner_info
end

function ZhuZaiShenDianWGData:SetCityOwnerLover(info)
	if not info then
		self.cityowner_lover = nil
	else
		self.cityowner_lover = {}
		self.cityowner_lover.role_name = info.role_name
		self.cityowner_lover.appearance = info.appearance
		self.cityowner_lover.sex = info.sex
		self.cityowner_lover.prof = info.prof
	end
end

function ZhuZaiShenDianWGData:GetCityOwnerLover()
	return self.cityowner_lover
end

--领取标记
function ZhuZaiShenDianWGData:GetFecthFlag()
	return self.allocate_list
end

function ZhuZaiShenDianWGData:GetIsActive(data)
	local is_active = false
	if not data or not next(data) then return end

	local allocate_list = self:GetFecthFlag()

	for k,v in pairs(data) do
		if (v.num - (allocate_list[k] or 0)) > 0 then
			is_active = true
			break
		end
	end

	return is_active
end

function ZhuZaiShenDianWGData:IsCanGet()
	-- local data = self:GetTotalReWard()
	-- local temp_data = {}
	-- for k,v in ipairs(data) do
	-- 	local times = k + 1
	-- 	if self.keep_win_times == times then
	-- 		if v and #v > 1 then
	-- 			for k_1,v_1 in pairs(v) do
	-- 				table.insert(temp_data,k_1 - 1,v_1[0])
	-- 			end
	-- 		else
	-- 			temp_data = v
	-- 		end
	-- 		break
	-- 	end
	-- end
	-- local is_active = self:GetIsActive(temp_data)
	return false
end

--每日领取次数
function ZhuZaiShenDianWGData:SetReceiveFlag(num)
	self.receive_flag = num
end

function ZhuZaiShenDianWGData:GetReceiveFlag()
	return nil == self.receive_flag and 1 or self.receive_flag
end

function ZhuZaiShenDianWGData:GetRewardNum()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local zhuzai_guild = self:GetGuildId()
	local guild_fight_state = GuildWGData.Instance:GetGuildBattleFightState()
	if guild_fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_FIRST_FIGHT_ON or 
		guild_fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_FIRST_FIGHT_END or
		guild_fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_SECOND_FIGHT_ON then
		return 0 
	end

	if nil == zhuzai_guild or self.keep_win_times == nil then
		return 0
	end
	local uid = ZhuZaiShenDianWGData.Instance:GetMengZhuUid()
	if guild_id > 0 and guild_id == zhuzai_guild[1].guild_id and uid > 0 then
		local guild_post = RoleWGData.Instance.role_vo.guild_post
		local is_can_appoit = (self.keep_win_times > 1 and GUILD_POST.TUANGZHANG == guild_post and self:IsCanGet()) --and 0 == self.allocate_list
		if 0 == self.receive_flag or is_can_appoit then
			return 1
		end
	end

	local receive_sign = self:GetReceiveFlag()
	if receive_sign then
		if receive_sign == 0 and guild_id == zhuzai_guild[1].guild_id and zhuzai_guild[1].guild_id > 0 and uid > 0 then
			return 1
	 	end
	end

	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local bangzhuuid = self:GetMengZhuUid()
	local is_active = self:GetFecthFlag()
	local winning_streak = self:GetLianShen()

	if is_active == 0 and guild_id == zhuzai_guild[1].guild_id and role_id == bangzhuuid and winning_streak > 1 and uid > 0 then
	 	return 1
	end

	return 0
end

function ZhuZaiShenDianWGData:GetRecordTimes()
	return self.other_cfg.divide_times or 0
end

function ZhuZaiShenDianWGData:SetAllocationRecordInfo(protocol)
	self.record_list = protocol.record_list
end

function ZhuZaiShenDianWGData:SetGuildBattleWinFirstRankFightResult(protocol)
	self.guildbattle_result = protocol
end

function ZhuZaiShenDianWGData:GetGuildBattleWinFirstRankFightResult()
	return self.guildbattle_result or {}
end
function ZhuZaiShenDianWGData:GetAllocationRecordInfo()
	return self.record_list or {}
end

function ZhuZaiShenDianWGData:IsShowShenDianRedPoint()
	local uid = self:GetMengZhuUid()
	if self:GetRewardNum() == 1 and uid > 0 then
		return 1
	end
	return 0
end