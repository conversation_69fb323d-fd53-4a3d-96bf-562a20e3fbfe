TianshenRoadView = TianshenRoadView or BaseClass(SafeBaseView)

function TianshenRoadView:__init()
	self.view_name = GuideModuleName.TianShenRoadPanel
	self.view_style = ViewStyle.Half
	self:SetMaskBg(false)
	self.default_index = TabIndex.tianshenroad_login

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	local assetbundle = "uis/view/tianshenroad_ui_prefab"
	-- self:AddViewResource(0, assetbundle, "layout_tianshen_road_panel")
	self:AddViewResource(TabIndex.tianshenroad_login, assetbundle, "layout_login")
	--self:AddViewResource(TabIndex.tianshenroad_login, assetbundle, "layout_duobei")
	
	self:AddViewResource(TabIndex.tianshenroad_first_recharge, assetbundle, "layout_first_recharge")
	self:AddViewResource(TabIndex.tianshenroad_total_recharge, assetbundle, "layout_total_recharge")
	self:AddViewResource(TabIndex.tianshenroad_jianglin, assetbundle, "layout_tianshen_jianlin")
	self:AddViewResource(TabIndex.tianshenroad_mowang, assetbundle, "layout_mowan")
	self:AddViewResource(TabIndex.tianshenroad_chongbang, assetbundle, "layout_chongbang")
	
	self:AddViewResource(0, assetbundle, "VerticalTabbar_Activity")
	self:AddViewResource(TabIndex.tianshenroad_shenqi, assetbundle, "layout_tianshen_shenqi")
	--限时秒杀
	self:AddViewResource(TabIndex.tianshenroad_xianshi_miaosha, assetbundle, "tianshenroad_miaosha_view")

end

function TianshenRoadView:ReleaseCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.get_guide_ui_event then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TianShenRoadPanel, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	self:ReleaseLoginView()
	self:ReleaseShouChongView()
	self:ReleaseRechargeView()
	self:ReleaseJiangLinView()
	self:ReleaseShenQiView()
	self:ReleaseMWView()
	self:ReleaseCBView()
	self:ReleaseDBView()
	self:DeleteHeFuMiaoSha()
end

function TianshenRoadView:CloseCallBack()
	self.is_play_left_btn_anim = nil
end

function TianshenRoadView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShenRoad.Title

	self.is_play_left_btn_anim = nil
	if not self.tabbar then
		self:SetTabIndex()
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:Init(self.tab_name_list, nil, ResPath.CommonBundleName, nil, self.remind_name_list)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self:SetTabSate()
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
			show_cangjin_score = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TianShenRoadPanel, self.get_guide_ui_event)
end

function TianshenRoadView:LoadIndexCallBack(index)
	if index == TabIndex.tianshenroad_login then
		self:InitLoginRewarView()
		--self:InitDBView()
	elseif index == TabIndex.tianshenroad_first_recharge then
		self:InitShouChongView()
	elseif index == TabIndex.tianshenroad_total_recharge then
		self:InitLeiChongView()
	elseif index == TabIndex.tianshenroad_shenqi then
		self:InitShenQiView()
	elseif index == TabIndex.tianshenroad_chongbang then
		self:InitCBView()
	elseif index == TabIndex.tianshenroad_jianglin then
		self:InitJiangLinView()
	elseif index == TabIndex.tianshenroad_mowang then
		self:InitMWView()
	elseif index == TabIndex.tianshenroad_xianshi_miaosha then --限时秒杀
		self:LoadIndexCallBackHeFuMiaoSha()
	-- elseif index == TabIndex.tianshenroad_duobei then
	-- 	self:InitDBView()
	end
end

function TianshenRoadView:ShowIndexCallBack(index)
	UITween.CleanAllTween(GuideModuleName.TianShenRoadPanel)
	if not self.is_play_left_btn_anim then
		self.is_play_left_btn_anim = true
		self:PlayTRVOpenAnim()
	end

	local bg_name = "a3_ptlj_dt_1"
	if index == TabIndex.tianshenroad_login then   -- 天神主题-登陆有礼
		bg_name = "a3_zsqd_dlyl_bg1"
		self:LoginShowIndexCallBack()
		--self:TRDBShowIndexCallBack()
	elseif index == TabIndex.tianshenroad_mowang then -- 天神主题-魔王有礼
		bg_name = "a3_zsqd_slfb_bg"
		self:ShowMWView()
	elseif index == TabIndex.tianshenroad_first_recharge then
		bg_name = "a3_zsqd_scsl_bg"
		self:ShowShouChongView()
	elseif index == TabIndex.tianshenroad_total_recharge then
		bg_name = "a3_ptlj_lc_di"
		self:ShowLeiChongView()
	elseif index == TabIndex.tianshenroad_shenqi then       -- 天神主题-我要神器
		bg_name = "a3_ptlj_dt_1"
		self:TRSQShowIndexCallBack()
	elseif index == TabIndex.tianshenroad_chongbang then    -- 天神主题-天神冲榜
		bg_name = "a3_zsqd_ljcc_dt_1"
		self:TRCBShowIndexCallBack()
	elseif index == TabIndex.tianshenroad_jianglin then     -- 天神主题-天神降临
		bg_name = "a3_zsqd_slfb_bg"
		self:TRJLShowIndexCallBack()
	-- elseif index == TabIndex.tianshenroad_duobei then       -- 天神主题-多陪奖励
	-- 	self:TRDBShowIndexCallBack()
	elseif index == TabIndex.tianshenroad_xianshi_miaosha then --限时秒杀
		bg_name = "a3_xsms_bg"
		self:ShowIndexCallHeFuMiaoSha()
		TianShenRoadMiaoshaWGData.Instance:CheckHasMiaoShaTabClickRemind()
	end

	local bundle, assert = ResPath.GetF2RawImagesPNG(bg_name)
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function TianshenRoadView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "VerticalTabbarCell_Activity" and self.tabbar then
		return self.tabbar:GetToggleByIndex(ui_param)
	elseif ui_name == GuideUIName.BtnShowTask then
		return self.node_list[ui_name], BindTool.Bind(self.ShowTask, self)
	elseif ui_name == GuideUIName.Jigsaw_1 then
		if self.bx_list and self.bx_list[1] then
			return self.bx_list[1].node_list.drag_evt, BindTool.Bind(self.ShowGuide, self, true)
		end
	elseif ui_name == GuideUIName.Jigsaw_2 then
		if self.bx_list and self.bx_list[2] then
			return self.bx_list[2].node_list.drag_evt
		end
	elseif ui_name == GuideUIName.JigsawAwardItem then
		if self.award_list and self.award_list[1] then
			return self.award_list[1].node_list.award
		end
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function TianshenRoadView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.tianshenroad_xianshi_miaosha then --限时秒杀
				self:FlushHeFuMiaoSha()
			end
		elseif "login_reward" == k then
			local day_index = v[1] or 0
			self:ShowLoginReward(day_index)
		elseif "login_view" == k then
			self:FlushLoginView()
		elseif "jianglin_view" == k then
			self:FlushJiangLinView()
		elseif "shenqi_view" == k then
			self:FlushSQView()
		elseif "mowang_view" == k then
			self:FlushMWView()
		elseif "chongbang_view" == k then
			self:FlushCBView()
		-- elseif "duobei_view" == k then
		-- 	self:FlushDBView()
		elseif "first_recharge_view" == k then
			self:FlushShouChongView()
		elseif "total_recharge_view" == k then
			self:FlushLeiChongView()
		end
	end
end

function TianshenRoadView:SetTabIndex()
	local tb = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshen_theme_desc
	local remind_data_map = TianshenRoadWGData.Instance:GetRemindNameMap()
	--table.sort(tb, SortTools.KeyLowerSorter("rank_id"))
	self.tab_name_list = {}
	self.sub_tab_name_list = {}
	self.remind_name_list = {}

	for k, v in ipairs(tb) do
		self.tab_name_list[v.rank_id] = v.active_name
		self.remind_name_list[v.rank_id] = {}
		table.insert(self.remind_name_list[v.rank_id], remind_data_map[v.real_id])
	end
end

function TianshenRoadView:SetTabSate()
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_login,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_login))
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_shenqi,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_shenqi))
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_chongbang,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_chongbang))
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_jianglin,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_jianglin))
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_mowang,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_mowang))
	-- self.tabbar:SetToggleVisible(TabIndex.tianshenroad_duobei,
	-- 	TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_duobei))
	--限时秒杀
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_xianshi_miaosha,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_xianshi_miaosha))
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_first_recharge,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_first_recharge))
	self.tabbar:SetToggleVisible(TabIndex.tianshenroad_total_recharge,
		TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_total_recharge))
end

function TianshenRoadView:PlayTRVOpenAnim()
	if self.node_list["tianshen_road_title_img"] then
		UITween.DoUpDownCrashTween(self.node_list["tianshen_road_title_img"])
	end

	local tween_info = UITween_CONSTS.TianshenRoadView
	UITween.FakeHideShow(self.node_list["VerticalTabbar_Mask"])

	UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["VerticalTabbar_Mask"], 0, tween_info.ToAlpha,
		tween_info.AlphaTime, tween_info.AlphaShowType)
end
