-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local ResUtil = require "lib/resmanager/res_util"
local FileDownloader = require "lib/resmanager/file_downloader"

local M = ResUtil.create_class()

function M:_init()
	self.v_file_downloaders = {}
end

function M:CreateFileDownloader(url, update_callback, complete_callback, cache_path, bundle_name, bundle_hash, check_hash)
	local file_downloader = FileDownloader:new(url, update_callback, complete_callback, cache_path, bundle_name, bundle_hash, check_hash)
	self.v_file_downloaders[file_downloader] = true
end

function M:Update()
	for v, _ in pairs(self.v_file_downloaders) do
		if not v:Update() then
			self.v_file_downloaders[v] = nil
		end
	end
end

return M
