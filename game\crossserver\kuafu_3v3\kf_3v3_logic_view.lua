--3v3场景信息界面
KF3V3LogicView = KF3V3LogicView or BaseClass(SafeBaseView)
function KF3V3LogicView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "left_player_info")
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "top_pk_info")
    self.is_safe_area_adapter = true
end

function KF3V3LogicView:__delete()
end

function KF3V3LogicView:ReleaseCallBack()
	if self.player_list then
		self.player_list:DeleteMe()
		self.player_list = nil
	end

	if self.pk_info_list then
		for key, value in pairs(self.pk_info_list) do
			value:DeleteMe()
			value = nil
		end
		self.pk_info_list = nil
	end

	if self.scene_info_change_event then
		GlobalEventSystem:UnBind(self.scene_info_change_event)
		self.scene_info_change_event = nil
	end
	if nil ~= self.main_menu_icon_change then
		GlobalEventSystem:UnBind(self.main_menu_icon_change)
		self.main_menu_icon_change = nil
	end
	if CountDownManager.Instance:HasCountDown("kf_3v3_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("kf_3v3_time_countdown")
	end
end

function KF3V3LogicView:LoadCallBack()
	self.player_list = AsyncListView.New(KF3V3LeftPlayerInfoRender, self.node_list["player_list"])

	if not self.pk_info_list then
		self.pk_info_list = {}
		local count = self.node_list.pk_info_list.transform.childCount
		for i = 1, count do
			self.pk_info_list[i] = KF3V3TopInfoRender.New(self.node_list.pk_info_list:FindObj("head_info_" .. i))
			self.pk_info_list[i]:SetIndex(i)
		end
	end

	self.scene_info_change_event = GlobalEventSystem:Bind(OtherEventType.KF3V3SceneInfoChange, BindTool.Bind(self.OnSceneInfoChange, self))
	self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))
	self:SetHideUiIsOn(true)
end

function KF3V3LogicView:ShowIndexCallBack()
	self:Flush()
end

function KF3V3LogicView:OnFlush()
	local scene_info = KF3V3WGData.Instance:GetSceneInfo()
	if not scene_info then
		return
	end
	local other_info = KF3V3WGData.Instance:GetSceneOtherInfo()
	if not other_info then
		return
	end
	--左侧玩家列表
	local data_list, remain_count_list = KF3V3WGData.Instance:GetScenePlayerList()
	self.player_list:SetDataList(data_list)

	--顶部列表.
	local info_list = KF3V3WGData.Instance:GetMatchInfo()
	if not info_list or #info_list < 0 then
		return
	end

	for key, value in pairs(self.pk_info_list) do
		if info_list[key] then
			value:SetIndex(key)
			value:SetData(info_list[key])
		end
	end

	--顶部剩余人数
	self.node_list.red_remain_num.text.text = remain_count_list[EnumKF3V3SideType.RED]
	self.node_list.blue_remain_num.text.text = remain_count_list[EnumKF3V3SideType.BLUE]


	--顶部时间
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if server_time < other_info.next_state_time then
		if other_info.pk_state == EnumKF3V3PKStateType.Fight then
			if CountDownManager.Instance:HasCountDown("kf_3v3_time_countdown") then
				CountDownManager.Instance:RemoveCountDown("kf_3v3_time_countdown")
			end

			local time = math.floor(other_info.next_state_time - TimeWGCtrl.Instance:GetServerTime())

			self:UpdateTime(0, time)
			CountDownManager.Instance:AddCountDown("kf_3v3_time_countdown", BindTool.Bind(self.UpdateTime, self), BindTool.Bind(self.CompleteTime, self), nil, time, 1)
		end
	end
end

function KF3V3LogicView:OnSceneInfoChange()
	self:Flush()
end

function KF3V3LogicView:UpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	self.node_list.remain_time.text.text = TimeUtil.MSTime(temp_seconds)
end

function KF3V3LogicView:CompleteTime(elapse_time, total_time)
	self.node_list.remain_time.text.text = ""
end

local SetUiIsOn = false
function KF3V3LogicView:SetHideUiIsOn(isOn)
	if not self:IsLoadedIndex(0) then
		return
	end
	if SetUiIsOn == isOn then
		return
	end
	SetUiIsOn = isOn
	if self.node_list.left_player_info_root then
		self.node_list.left_player_info_root.transform:DOLocalMoveX(isOn and -500 or 22, 0.5)
	end
	if self.node_list.top_pk_info_root then
		self.node_list.top_pk_info_root.transform:DOLocalMoveY(isOn and 500 or 0, 0.5)
	end
end

function KF3V3LogicView:MainMenuIconChangeEvent(isOn)
	if self.node_list.left_player_info_root then
		self.node_list.left_player_info_root.transform:DOLocalMoveX(isOn and -500 or 22, 0.5)
	end
end


--左侧玩家信息
KF3V3LeftPlayerInfoRender = KF3V3LeftPlayerInfoRender or BaseClass(BaseRender)
function KF3V3LeftPlayerInfoRender:__init()
	XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind1(self.OnClick, self))
end

function KF3V3LeftPlayerInfoRender:OnFlush()
	local is_dead = self.data.cur_hp <= 0
	--local name_color = (is_dead or self.data.is_offline == 1) and "#929192" or "#dcfff6"
	local name_color = "#ffffff"

    local name = ""
	if self.data.name and self.data.name ~= "" then
		local name_list = Split(self.data.name, "_")
        if name_list[2] then          
            name = ToColorStr(string.format(Language.KuafuPVP.ServerName_1, name_list[2],name_list[1]), name_color)
		else
			local main_vo = RoleWGData.Instance:GetRoleVo()
            local server_str = "S" .. main_vo.merge_server_id
			name = ToColorStr(string.format(Language.KuafuPVP.ServerName_1, server_str,name_list[1]), name_color)
		end
	end
	self.node_list.name.text.text = name

	local max_hp = self.data.max_hp
	if max_hp == 0 then
		max_hp = 1
	end
	self.node_list.slider_blood.slider.value = self.data.cur_hp / max_hp
	self.node_list.Fill.image:LoadSprite(ResPath.GetCommon(self.data.side == EnumKF3V3SideType.RED and "a3_ty_jd_red" or "a3_ty_jd_blue"))
	self.node_list.death_img:SetActive(is_dead and self.data.is_offline ~= 1)
	self.node_list.offline_img:SetActive(self.data.is_offline == 1)
end

function KF3V3LeftPlayerInfoRender:OnClick()
	if self.data then
		if KF3V3WGData.Instance:GetMySide() ~= self.data.side then
			local role = Scene.Instance:GetRoleByRoleId(self.data.uid)
			if not role then
				return
			end
			if role:IsRole() then
	            MoveCache.SetEndType(MoveEndType.AttackTarget)
	            GuajiCache.target_obj = role
	            GuajiWGCtrl.Instance:MoveToObj(role, COMMON_CONSTS.GUAJI_MAX_RANGE)
	            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		end
	end
end

---------------------------------------顶部玩家信息----------------------------------
KF3V3TopInfoRender = KF3V3TopInfoRender or BaseClass(BaseRender)
function KF3V3TopInfoRender:__init()
	self.head_cell = BaseHeadCell.New(self.node_list.head_root)
end

function KF3V3TopInfoRender:__delete()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function KF3V3TopInfoRender:OnFlush()
	local data_list, remain_count_list = KF3V3WGData.Instance:GetScenePlayerList()
	if not data_list or not data_list[self.index] then
		return
	end

	local cur_list = data_list[self.index]

	local is_dead = cur_list.cur_hp <= 0

	local name = ToColorStr(self.data.name, is_dead and COLOR3B.C10 or "#f9f8f9")
	self.node_list.name.text.text = name

	self.node_list.dead:SetActive(is_dead and self.data.is_offline ~= 1)

	if self.data.uid ~= 0 then
		local data = {}
		local uid = self.data.uid
		--高位表示平台类型 平台类型为0表示是机器人
		if self.data.uuid and self.data.uuid.temp_high == 0 then
			uid = 0
		end
		data.role_id = uid
		data.prof = self.data.prof
		data.sex = self.data.sex
		data.fashion_photoframe = self.data.photoframe
		self.head_cell:SetBgActive(false)
		self.head_cell:SetData(data)
	end
	self.head_cell:SetGray(is_dead)

	local max_hp = cur_list.max_hp
	if max_hp == 0 then
		max_hp = 1
	end
	self.node_list.hp.image.fillAmount = cur_list.cur_hp / max_hp
	self.node_list.hp.image:LoadSprite(ResPath.GetF2Field1v1(cur_list.side == EnumKF3V3SideType.RED and "a3_jjc_htxkt" or "a3_jjc_ltxkt"))
end