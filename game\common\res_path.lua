ResPath = ResPath or {}
local _sformat = string.format

---------------------------------------------------------
-- 3D项目用到的预制体路径(换UI的时候顺便把views更换)
---------------------------------------------------------
function ResPath.GetBossUI(str)
    return "uis/view/boss_ui/images_atlas", str
end

function ResPath.GetWidgets(res_name)
    return "uis/view/miscpre_load_prefab", res_name
end

function ResPath.GetMingWenPrefab(res_name)
    return "uis/view/ming_wen_ui_prefab", res_name
end

---------------------------------------------------------
-- 3D项目用到的模型路径
---------------------------------------------------------
function ResPath.GetRoleXunyouModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return "model/character/xunyou_prefab", tostring(res_id)
end

function ResPath.GetRoleModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/character/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetRoleModelMaterial(res_id, color, low)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end
    color = color > 0 and _sformat("%02d", color) or ""
    local name = low and "_Material_low" or "_Material"
    return _sformat("model/character/%s_prefab", res_id), res_id .. color .. name
end

function ResPath.GetItemModel( res_id )
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/item/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetTriggerModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/trigger/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetFallItemModel(res_id, bool)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end
    if bool then
        return _sformat("model/forge/%s_prefab", res_id), tostring(res_id)
    else
        return _sformat("model/forge/%s_prefab", res_id), tostring(res_id)
    end
end

function ResPath.GetGatherModel(res_id)
    if res_id == nil or res_id == 0 then 
        return nil, nil
    end

    return _sformat("model/gather/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetGuardModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/shouhu/%s_prefab", res_id), tostring(res_id)
end

--[[获取灵童模型资源路径]]
function ResPath.GetSoulBoyModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/goddess/%s_prefab", res_id), tostring(res_id)
end

--[[获取上古神灵模型资源路径]]
function ResPath.GetShangGuJinModel( res_id )
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/shanggushenling/%s_prefab", res_id), tostring(res_id)
end

--[[获取灵童翅膀模型资源路径]]
function ResPath.GetSoulBoyWingModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/GoddessWing/%s_prefab", res_id), tostring(res_id)
end

--[[获取灵童武器模型资源路径 低模]]
function ResPath.GetSoulBoyWeaponModel(res_id)
    if res_id == nil or res_id == 0 or res_id == 1 then
        return nil, nil
    end

    return _sformat("actors/goddessweapon/%s_prefab", res_id), tostring(res_id)
end

--[[UI幻装 高模]]
function ResPath.GetSoulBoyWeaponModel2(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/goddessweapon/%s_prefab", res_id), tostring(res_id) .. "_H"
end

-- 翅膀
function ResPath.GetWingModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/wings/%s_prefab", res_id), tostring(res_id)
end

--剑阵模型
function ResPath.GetJianZhenModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/beishi/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetPifengModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/pifeng/%s_prefab", res_id), tostring(res_id)
end

--- 武魂真身模型
function ResPath.GetWuHunModel(res_id)
    if res_id == nil or res_id == "" or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/wuhun/%s_prefab", res_id), tostring(res_id)
end

--- 武魂真身技能贴图
function ResPath.GetWuHunSkillTexture(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("uis/rawimages/%s", res_id), tostring(res_id).. ".png"
end

--- 武魂真身模型
function ResPath.GetWuHunModel2(res_id, res_lv)
    if res_id == nil or res_id == "" or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/wuhun/%s_prefab", res_id), tostring(res_lv)
end

--装备模型
function ResPath.GetForgeEquipModelForSubType(sub_type)
    if sub_type == nil or sub_type == 0 then
        return nil, nil
    end

    local res_str = ""
    if sub_type == GameEnum.EQUIP_TYPE_TOUKUI then
        res_str = "0001"
    elseif sub_type == GameEnum.EQUIP_TYPE_YIFU then
        res_str = "0002"
    elseif sub_type == GameEnum.EQUIP_TYPE_KUZI then
        res_str = "0003"
    elseif sub_type == GameEnum.EQUIP_TYPE_XIANLIAN then
        res_str = "0005"
    elseif sub_type == GameEnum.EQUIP_TYPE_XIEZI then
        res_str = "0004"
    elseif sub_type == GameEnum.EQUIP_TYPE_WUQI then
        res_str = "0007"
    elseif sub_type == GameEnum.EQUIP_TYPE_XIANZHUI then
        res_str = "0006"
    elseif sub_type == GameEnum.EQUIP_TYPE_XIANFU then
        res_str = "0006"
    elseif sub_type == GameEnum.EQUIP_TYPE_XIANJIE then
        res_str = "0008"
    elseif sub_type == GameEnum.EQUIP_TYPE_XIANZHUO then
        res_str = "0003"
    elseif sub_type == GameEnum.EQUIP_TYPE_HUFU then
        res_str = "0009"
    elseif sub_type == GameEnum.EQUIP_TYPE_MIANJIA then
        res_str = "0001"
    elseif sub_type == GameEnum.EQUIP_TYPE_XIANGLIAN_1 then
        res_str = "0006"
    elseif sub_type == GameEnum.EQUIP_TYPE_DIAOZHUI then
        res_str = "0006"
    end

   if res_str == "" then
        return nil, nil
   end

    return _sformat("model/forge/%s_prefab", res_str), res_str
end

--- 角色技能视频
function ResPath.GetRoleSkillVideoPath(role_id, res_id)
    if role_id == nil or role_id == "" or role_id == 0 or res_id == nil or res_id == "" or res_id == 0 then
        return nil, nil
    end

    return _sformat("uis/videos/roleskill/%s", role_id), tostring(res_id)
end

--- 元神技能视频
function ResPath.GetDujieSpiritSkillVideoPath(sex, res_id)
    if res_id == nil or res_id == "" or res_id == 0 then
        return nil, nil
    end

    return _sformat("uis/videos/angerskill/%s", sex), tostring(res_id)
end

--- 技能突破技能视频
function ResPath.GetSkillBreakPurchaseSkillVideoPath(role_id, res_id)
    if role_id == nil or role_id == "" or role_id == 0 or res_id == nil or res_id == "" or res_id == 0 then
        return nil, nil
    end

    return _sformat("uis/videos/skillbreak/%s", role_id), tostring(res_id)
end

--通用技能
function ResPath.GetCommonSpiritSkillVideoPath(res_id)
    if res_id == nil or res_id == "" or res_id == 0 then
        return nil, nil
    end

    return "uis/videos/commonskill", tostring(res_id)
end
--===================================
--幻装模型
--===================================
--法宝
function ResPath.GetFaBaoModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/fabao/%s_prefab", res_id), tostring(res_id)
end

--五术之山
function ResPath.GetWSASModel(name)
    return "model/wushuzhishan_prefab", name 
end

--特殊界面
function ResPath.GetOtherUIModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/uiother/%s_prefab", res_id), tostring(res_id)
end

--特殊界面
function ResPath.GetNormalOtherModel(bundle_id, res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/other/%s_prefab", bundle_id), tostring(res_id)
end

function ResPath.GetOtherUIModelByName(res_name)
    if res_name == nil or res_name == "" then
        return nil, nil
    end
    
    return "actors/uiother/3_prefab", res_name
end

--脸
function ResPath.GetMaskModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/lianshi/%s_prefab", res_id), tostring(res_id)
end

--腰--4001 4001001
function ResPath.GetBeltModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/yaoshi/%s_prefab", res_id), tostring(res_id)
end

--尾21010
function ResPath.GetWeibaModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/weiba/%s_prefab", res_id), tostring(res_id)
end


-- 圣器模型
function ResPath.GetRelicModel(res_id)
    return _sformat("model/shengqi/%s_prefab", res_id), res_id
end

-- 仙魔神器模型
function ResPath.GetArtifactModel(res_id)
    return _sformat("model/xianmoshenqi/%s_prefab", res_id), res_id
end

-- 命器模型
function ResPath.GetMingQiModel(res_id)
    return _sformat("model/mingqi/%s_prefab", res_id), res_id
end

-- 双修spine
function ResPath.GetShuangXiuShowUI(res_id)
    return "uis/view/artifact_ui/spineanimations/sx_spine_" .. res_id .. "_prefab", "sx_spine_" .. res_id
end

-- 双修TipSpine
function ResPath.GetShuangXiuTipUI(res_id)
    return "uis/view/artifact_ui/spineanimations/sx_spine_" .. res_id .. "_prefab", "sx_spine_tip_" .. res_id
end

-- 双修出战spine
function ResPath.GetShuangXiuCZUI(res_id)
    return "uis/view/artifact_ui/spineanimations/sx_spine_" .. res_id .. "_prefab", "sx_spine_cz_" .. res_id
end

-- 高达
local gundam_part_str = {
    [0] = "body", "arm", "arm", "leg", "leg", "wing", "wing", "weapon",
}
function ResPath.GetGundamPartModel(gundam_seq, part, part_res_id)
    gundam_seq = gundam_seq or 0
    part = part or 0
    part_res_id = part_res_id or 0
    local bundle, asset
    local part_str = gundam_part_str[part]
    if not part_str then
        return bundle, asset
    end

    local parent_id = 0
    if part == MECHA_PART_TYPE.WEAPON then
        parent_id = gundam_seq * 100000 + (part_res_id % 100)
        asset = gundam_seq * 100000 + part * 1000 + part_res_id
        bundle = _sformat("model/jijia/%s/%d/%d_prefab", part_str, parent_id, asset)
    else
        parent_id = gundam_seq * 100000 + part_res_id
        asset = parent_id + part * 1000
        bundle = _sformat("model/jijia/%s/%d_prefab", part_str, parent_id)
    end

    return bundle, asset
end

--灵犀芒图模型
function ResPath.GetLingXiMangTuModel(res_id)
    return _sformat("actors/other/lingximangtu/%s_prefab", res_id), res_id
end

-- 珍惜物品装备模型
function ResPath.GetRareEquipItemModel(res_id)
    return _sformat("model/equipdrop/%s_prefab", res_id), res_id
end
---------------------------------------------------------
-- 声音资源路径
---------------------------------------------------------
function ResPath.GetBGMResPath(res_id)
    return "audios/musics/bgm" .. res_id, "BGM" .. res_id
end

function ResPath.GetVoiceRes(res_id)
    return "audios/sfxs/voice/" .. res_id, tostring(res_id)
end

function ResPath.GetUisVoiceRes(res_id)
    return "audios/sfxs/uis", res_id
end

function ResPath.UiseRes(res_id)
    return "audios/sfxs/uis", tostring(res_id) .. ".asset"
end

function ResPath.GetTianShenVoice(res_id)
    local res_id_lower = string.lower(res_id)
    return "audios/sfxs/tianshen/" .. res_id_lower, res_id_lower
end

---------------------------------------------------------
-- 特效资源路径
---------------------------------------------------------
function ResPath.GetEffect(res_id)
    if res_id == nil then
        return
    end
	
    return string.lower(_sformat("effects/prefab/ui/%s_prefab", res_id)), res_id
end

function ResPath.GetUIEffect(res_id)
    return string.lower(_sformat("effects/prefab/ui/%s_prefab", res_id)), res_id
end

function ResPath.GeBufftEffect(res_id)
    if res_id == nil then
        return
    end

    return string.lower(_sformat("effects/prefab/model/buff/%s_prefab", res_id)), res_id
end

function ResPath.GetEffectUi(res_id)
    if res_id == nil then
        return
    end

    return string.lower(_sformat("effects/prefab/ui/%s_prefab", res_id)), res_id
end

function ResPath.GetWuPinKuangEffectUi(res_id)
    if res_id == nil then
        return
    end

    return string.lower(_sformat("effects/prefab/ui/wupinkuang/%s_prefab", res_id)), res_id
end

function ResPath.GetA2Effect(index)
    if index == nil or index == "" then
        return
    end

    return string.lower(_sformat("effects/prefab/ui/%s_prefab", index)), index
end

function ResPath.GetBuffEffect(ab, res)
   if ab == nil or res == nil or res == "" or ab == "" then
        return
   end

   return ab, tostring(res)
end

function ResPath.GetEnvironmentCommonEffect(res)
   if res == nil or res == "" then
        return
   end

   return string.lower(_sformat("effects/prefab/environment/common/%s_prefab", res)), res
end

function ResPath.GetEnvironmentBanyunEffect(res)
    if res == nil or res == "" then
         return
    end
 
    return string.lower(_sformat("effects/prefab/environment/banyun/%s_prefab", res)), res
 end

 function ResPath.GetEnvironmentBuffEffect(res)
    if res == nil or res == "" then
        return
    end

    return string.lower(_sformat("effects/prefab/environment/buff/%s_prefab", res)), res
 end

 function ResPath.GetEnvironmentMiscEffect(res)
    if res == nil or res == "" then
         return
    end
 
    return string.lower(_sformat("effects/prefab/environment/Misc/%s_prefab", res)), res
 end
 

--足迹
function ResPath.GetFootEffect(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("effects/prefab/model/foot/foot_%s_prefab", res_id), "Foot_" .. res_id
end

--足迹 UI
function ResPath.GetUIFootEffect(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    local asset_name = "Foot_" .. res_id .. "_loop"
    return _sformat("effects/prefab/model/foot/foot_%s_loop_prefab", res_id), asset_name
end

--光环
function ResPath.GetHaloModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/halo/halo_%s_prefab", res_id),  "Halo_" .. res_id
end

-- 技能光环
function ResPath.GetSkillHaloModel(res_id, is_skill)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    local asset = is_skill and "skill_halo_skill_" or "skill_halo_"
    local asset_name = asset .. res_id
    return _sformat("model/skillhalo/%s_prefab", asset_name), asset_name
end

--手21002-02
function ResPath.GetShouhuanModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/shouhuan/shouhuan_%s_prefab", res_id) , "shouhuan_" .. res_id
end

--场景称号模型
function ResPath.GetTitleModel(title_id)
    if title_id == nil or title_id <= 0 then
        return nil, nil
    end

    local asset_name = "title_" .. title_id
    return _sformat("effects/prefab/model/title/%s_prefab", asset_name), asset_name
end

-- 头像框装饰
function ResPath.GetPhotoFrameAdorn(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return "effects/prefab/xiangkuang_prefab", "head_adorn_" .. res_id
end

--掉落柱子特效
local fall_item_zhu_eff = {
    [1] = "eff_guangzhu_lv",
    [2] = "eff_guangzhu_lan",
    [3] = "eff_guangzhu_zi",
    [4] = "eff_guangzhu_cheng",
    [5] = "eff_guangzhu_hong",
    [6] = "eff_guangzhu_fen",
    [7] = "eff_guangzhu_jin",
    [8] = "eff_guangzhu_cai",
}

--掉落特效
local fall_item_eff = {
    [1] = "eff_diaoluo_lv",
    [2] = "eff_diaoluo_lan",
    [3] = "eff_diaoluo_zi",
    [4] = "eff_diaoluo_cheng",
    [5] = "eff_diaoluo_hong",
    [6] = "eff_diaoluo_fen",
}

function ResPath.GetFallItemEffectName( color )
    return fall_item_eff[color] or fall_item_eff[1]
end

function ResPath.GetFallItemZhuEffectName( color )
    return fall_item_zhu_eff[color] or fall_item_zhu_eff[1]
end

function ResPath.GetFallItemEffect( color )
    local name = fall_item_eff[color] or fall_item_eff[1]
    return _sformat("effects/prefab/environment/common/%s_prefab", name), name
end

function ResPath.GetFallItemZhuEffect(name)
    return _sformat("effects/prefab/environment/gather/%s_prefab", name), name
end

--掉落特效二段
local fall_item_eff_sec = {
    [1] = "eff_diaoluo_zadi_lv",
    [2] = "eff_diaoluo_zadi_lan",
    [3] = "eff_diaoluo_zadi_zi",
    [4] = "eff_diaoluo_zadi_cheng",
    [5] = "eff_diaoluo_zadi_hong",
    [6] = "eff_diaoluo_zadi_fen",
}
function ResPath.GetFallItemEffectSecond(color)
    local name = fall_item_eff_sec[color] or fall_item_eff_sec[1]
    return _sformat("effects/prefab/environment/common/%s_prefab", name), name
end

--婚宴烟花特效
function ResPath.GetEnvironmentEffect( name )
    return "effects/prefab/environment/tongyong_prefab", name
end

function ResPath.GetRoleJumpEff(prof)
    if prof == nil or prof < 1 or prof > 4 then
        return nil, nil
    end
    local name = ""
    if prof == 1 then
        name = "eff_yujian_jump"
    elseif prof == 2 then
        name = "eff_yujian_jump"
    elseif prof == 3 then
        name = "eff_yujian_jump"
    elseif prof == 4 then
        name = "eff_yujian_jump"
    end

return _sformat("effects/prefab/environment/common/%s_prefab", string.lower(name)), name
end

function ResPath.GetFourJumpMount(prof)
    if prof == nil or prof < 1 or prof > 4 then
        return nil, nil
    end
    local name = ""
    if prof == 1 then
        name = "xianhe"
    elseif prof == 2 then
        name = "xianhe"
    elseif prof == 3 then
        name = "zhihe"
    elseif prof == 4 then
        name = "zhihe"
    end

    return "effects/prefab/mount/" .. string.lower(name) .. "_prefab", name
end

function ResPath.GetMiscEffect(name)
    return "effects/prefab/misc/" .. string.lower(name) .. "_prefab", name
end

function ResPath.GetTuTenEffect( str )
    local lower_str = string.lower(str)

    return "effects/prefab/ui/" .. lower_str .. "_prefab", lower_str
end

-- 技能法阵
function ResPath.GetSkillFaZhenModel(res_id)
    if res_id == nil then
        return nil, nil
    end

    return "model/footlight/footlight_".. res_id ..  "_prefab", "FootLight_" .. res_id
end

function ResPath.GetEnvironmentZhanLingQuYuEffect(name)
    return _sformat("effects/prefab/environment/zhanchang/%s_prefab", name), name
end

-- 怒气变身
function ResPath.GetAransformationEffect(name, index)
    local type_str = "ren"
    if index == 2 then
        type_str = "xian"
    elseif index == 1 then
        type_str = "mo"
    end

    return _sformat("effects/prefab/model/nuqi/%s/%s_prefab", type_str, name), name
end

-- 怒气变身(UI)
function ResPath.GetUIAransformationEffect(name, index)
    local type_str = "ren"
    if index == 2 then
        type_str = "xian"
    elseif index == 1 then
        type_str = "mo"
    end

    local str = _sformat("UI_%s_%s", type_str, name)
    return ResPath.GetEffect(str)
end

-- 怒气变身
function ResPath.GetAransformationUIEffectName(index)
    local type_str = "ren"
    if index == 2 then
        type_str = "xian"
    elseif index == 1 then
        type_str = "mo"
    end

    return _sformat("UI_nuqi_%s_jihuo", type_str)
end

-- 魂环
function ResPath.GeHunHuanEffect(res_id)
    if res_id == nil then
        return
    end

    return string.lower(_sformat("effects/prefab/model/hunhuan/%s_prefab", res_id)), res_id
end

local level_show_effect = {
    [0] = "UI_pingfen_A",
    [1] = "UI_pingfen_S",
    [2] = "UI_pingfen_SS",
    [3] = "UI_pingfen_SSS",
}

--评分特效.
function ResPath.GetStarLevelUIEffect(star_num)
    local name = level_show_effect[star_num] or level_show_effect[0]
    return string.lower(_sformat("effects/prefab/ui/%s_prefab", name)), name
end
---------------------------------------------------------
-- UI
---------------------------------------------------------
function ResPath.GetBossHp(index)
    return ResPath.GetMainUIIcon("a3_boss_pro_" .. index)
end

ITEM_INTERVAL = 1000
ITEM_DICHOTOMY = 500

function ResPath.GetItem(icon_id)
    icon_id = tonumber(icon_id)
    if not icon_id then
        return nil, nil
    end
    local bundle_id = math.floor(icon_id / ITEM_INTERVAL)
    local atlas_id = icon_id % ITEM_INTERVAL > ITEM_DICHOTOMY and 2 or 1
    return string.format("uis/icons/item_%d_%d_atlas", bundle_id, atlas_id), icon_id
    -- local bundle_id = math.floor(icon_id / 1000)
    -- if bundle_id == 27
    --     or bundle_id == 26
    --     or bundle_id == 23
    --     or bundle_id == 22 then

    --     bundle_id = math.floor(icon_id / 100)
    --     return "uis/icons/item_" .. bundle_id .. "_atlas", icon_id
    -- else
    --     return "uis/icons/item_" .. bundle_id * 10 .. "_atlas", icon_id
    -- end
end

function ResPath.GetShenjiRecord(index)
    return "uis/rawimages/a2_sj_display_" .. index, "a2_sj_display_".. index .. ".png"
end

function ResPath.GetFuBenPanel(name)
    return "uis/icons/fubenpanel_atlas", name
end

function ResPath.GetMainlblIcon(color_name)
    return "uis/view/main/images_atlas", "lbl_bg_" .. color_name
end

function ResPath.GetCommon(name)
    return "uis/images/common_atlas", name
end

--新通用加载路径方法  button&toggle_atlas
function ResPath.GetCommonButtonToggle_atlas(name)
    return "uis/images/button&toggle_atlas", name
end

function ResPath.MainUIActivityButton(name)
    return "uis/view/main_ui/activitybuttons_prefab", name
end

--新通用加载路径方法  BackGround
function ResPath.GetCommonBackGround(name)
    return "uis/imagesf2/common_atlas", name
end

--新通用加载路径方法  others
function ResPath.GetCommonOthers(name)
    return "uis/images/others_atlas", name
end

--新的加载套装的路径方法
function ResPath.GetAppearanceImg(name)
    return "uis/uires/res/x1ui/appearance_atlas", name
end

function ResPath.GetPrivilegedGuidanceImg(name)
    return "uis/view/privileged_guidance_ui/image_atlas", name
end

--上古神卷图片加载--sgfl
function ResPath.GetTianshuResPath(name)
    return "uis/uires/res/x1ui/tianshu_atlas", name
end

function ResPath.GetAnswer( name )
    return "uis/uires/res/x1ui/answer_atlas", name
end

function ResPath.GetRankImage(name)
    return "uis/images/others_atlas", name
end

function ResPath.GetRoleTitle(id)
   local index = math.floor(tonumber(id)/1000) * 1000
   return _sformat('uis/icons/title/%d_atlas',index) ,'Title_' .. id
end

function ResPath.GetMarryResPath(name)
    return "uis/uires/res/x1ui/marry_atlas", name
end

function ResPath.GetMarryResPath2(name)
    return "uis/uires/res/x1ui/marry_atlas", name
end

function ResPath.GetKf1V1(name)
    return "uis/uires/res/x1ui/arena_atlas", name
end

function ResPath.GetSkillIcon(icon_id)
    return "uis/imagesf2/icon/skill_atlas", icon_id
end

function ResPath.GetF2RechargeIcon(name)
    return "uis/view/rechargereward_ui/images_atlas", name
end

function ResPath.GetTalentSkill(name)
    return "uis/icons/talent_skills_atlas", name
end

function ResPath.GetProfIcon(sex, prof, is_hl)
    local hl = ""
    if is_hl then
        hl = "_hl"
    end

    local prof_str = string.format("role_prof_%s%s%s", sex, prof, hl)
    return "uis/icons/role_prof_atlas", prof_str
end

function ResPath.GetNewTeamImg(name)
    return "uis/view/new_team_ui/images_atlas", name
end

function ResPath.GetTitle(name)
    local atlas = math.floor(tonumber(name) / 1000) * 1000
    return "uis/icons/title/" .. atlas .. "_atlas", "Title_" .. name
end

function ResPath.GetBubble(index)
    return "uis/icons/qipao_atlas", "qipao_" .. index
end

function ResPath.GetPhotoFrame(index)
    return "uis/icons/xiangkuang_atlas", "xiangkuang_" .. index
end

function ResPath.GetWelfareModel(model_id)
    return "actors/welfare/" ..model_id.. "_prefab", model_id
end

function ResPath.GetRoleIconBig(res_id)
    return "uis/icons/portrait_atlas", res_id
end

--大图资源获取
function ResPath.GetBigPainting(name)
    return "uis/uires/res/x1ui/painting_atlas", name
end

function ResPath.GetShenShouIcon(name)
    return "uis/uires/res/x1ui/shenshou_atlas", name
end

function ResPath.GetBiZuo(name)
    return "uis/uires/res/x1ui/bizuo_atlas", name
end

function ResPath.GetTransFer(name)
    --return "uis/uires/res/x1ui/tansfer_atlas", name
    return "uis/view/tansfer_ui/images_atlas", name
end

function ResPath.GetActIvityHall(name)
    return "uis/uires/res/x1ui/activity_hall_atlas", name
end

function ResPath.GetBossIcon(name)
    return "uis/icons/boss_atlas", name
end

function ResPath.GetBossAssitImg(name)
    return "uis/view/boss_assist_ui/images_atlas", name
end

function ResPath.GetGoddessModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("actors/goddess/%s_prefab", res_id), tostring(res_id)
end

--获取宝宝模型
function ResPath.GetHaiZiModel(res_id)
    if res_id == nil or 0 > res_id then
        return nil, nil
    end

    return _sformat("model/child/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetNpcDialog(res_id)
    return "uis/uires/res/x1ui/npcdialog_atlas", res_id
end

--boss怒气值图标
function ResPath.GetBossAngryIcon()
    return "uis/view/mainui/images_atlas", "a3_zjm_ztbnmq"
end

--boss积分值图标
function ResPath.GetBossScoreIcon()
    return "uis/view/mainui/images_atlas", "a3_zjm_ztbnq2"
end

--仙界boss仙魂值图标
function ResPath.GetBossXianjieTiredIcon()
    return "uis/view/mainui/images_atlas", "a3_zjm_ztbnmq"
end

--师徒界面  加载师徒的角色
function ResPath.GetShituRoleModel(prof, res_id)
    if prof == 1 then
        return "model/character/120100" .. (res_id + 1) .. "_prefab", "120100" .. (res_id + 1)
    elseif prof == 3 then
        return "model/character/120300" .. (res_id + 1) .. "_prefab", "120300" .. (res_id + 1)
    end
end

--师徒界面翅膀
function ResPath.GetShituWuQiModel(prof, res_id)
    if prof == 1 then
        return "model/weapon/9001001_prefab", "900100101"
    elseif prof == 3 then
        return "model/weapon/9001001_prefab", "900100101"
    end
end

--交互界面头像资源
function ResPath.GetRoleHeadIconSociety (prof)
    local profession = prof % 10
    if profession == 1 then
        return "uis/icons/portrait_atlas", "11"
    elseif profession == 2 then
        return "uis/icons/portrait_atlas", "20" --暂定
    elseif profession == 3 then
        return "uis/icons/portrait_atlas", "20"
    elseif profession == 4 then
        return "uis/icons/portrait_atlas", "11" --暂定
    end
end

--获取鱼的模型资源
function ResPath.GetFishModelRes(res_id)
    --uis/view/fishpond_ui/fishanimatepic/fish/fish_01_prefab
    return "uis/view/fishpond_ui/fishanimatepic/fish/" ..string.lower(tostring(res_id)) .. "_prefab", tostring(res_id)
end

--1v1
function ResPath.GetKf1V1Old(name)
    return "uis/uires/res/x1ui/arena_atlas", name
end

--获取3V3图片
function ResPath.GetKf3V3Old(name)
    return "uis/uires/res/x1ui/arena/temp_fold_atlas", name
end

function ResPath.GetPerfectLover(name)
    return "uis/uires/res/x1ui/prefactloverui_atlas", name
end

function ResPath.GetHuanlezadan(name)
    return "uis/uires/res/x1ui/happyegg_atlas", name
end

--获取活动名字标题
function ResPath.GetActivityTitle(name)
    return "uis/uires/res/x1ui/activitycommon_atlas", name
end

function ResPath.GetOpenServerName(name)
    return "uis/uires/res/x1ui/openserveractivity_atlas", name
end

function ResPath.GetBossQuilitySpriteName(index)
    return "uis/rawimages/quality_card_bg_" ..index, "quality_card_bg_" ..index.. ".png"
end

function ResPath.GetBossIconName(index)
    return "uis/rawimages/icon_" ..index, "icon_" ..index.. ".png"
end

function ResPath.GetOpenserver(name)
    return _sformat("res/x1ui/openserver/%s.png", name)
end

function ResPath.GetResNormalFace(res_id)
    return "uis/icons/normalface_prefab", tostring(res_id)
end

function ResPath.GetResBigFace(res_id)
    return "uis/icons/bigface_prefab", tostring(res_id)
end

-- 转职技能类型图标
function ResPath.GetCareerTypeImage(career_type)
    local icon = "fire_flag"
    if career_type == CAREER_SERIOUS_TYPE.FIRE then
        icon = "fire_flag"
    elseif career_type == CAREER_SERIOUS_TYPE.ICE then
        icon = "snow_flag"
    elseif career_type == CAREER_SERIOUS_TYPE.THUNDER then
        icon = "electric_flag"
    end

    return "uis/images/icon_atlas", icon
end

function ResPath.GetRushRecharge( name )
    return "uis/uires/res/x1ui/rush_recharge_atlas", name
end

function ResPath.GetEquipIcon(id, item_type)
    if item_type ==EquipIcon.ShenShou then
        id = id + 20
        if id == 21 then
            id = 0
        end
        if id == 22 then
            id = 43
        end
        if id == 23 then
            id = 8
        end
        if id == 24 then
            id = 6
        end
    elseif item_type ==EquipIcon.Mount then
        id = id + 30
    elseif item_type ==EquipIcon.LingChong then
        id = id + 40
    elseif item_type == EquipIcon.RoleBag then
        if id == 5 then
            id = 13
        end
    end

    return "uis/images/icon_atlas", "a3_zb_" .. id
end

function ResPath.GetRoleHeadIconPVP (prof)
    local profession = prof % 10
    if profession == 1 then
        return "uis/uires/res/x1ui/arena_atlas", "11"
    elseif profession == 2 then
        return "uis/uires/res/x1ui/arena_atlas", "20" --暂定
    elseif profession == 3 then
        return "uis/uires/res/x1ui/arena_atlas", "20"
    elseif profession == 4 then
        return "uis/uires/res/x1ui/arena_atlas", "11" --暂定
    end
end

-- 获取时装模型
function ResPath.GetFashionModelId(prof, index)
    if not prof or not index then
        return 0
    end

    prof = prof % 10
    local id = 1200000 + prof * 1000 + index
    return id
end

-- 获取猎鲲倒计时图片
function ResPath.GetFishModelId(index)
    if index == nil then return end

    return "uis/uires/res/x1ui/liekunui_atlas",'countdown_' .. index
end

function ResPath.GetOpenserverActiveIcon(name)
    return "uis/uires/res/x1ui/act_subview_atlas", name
end

function ResPath.GetNewHeadIcon(sex, id)
    return "uis/icons/toxiang_atlas","toxiang_" ..sex.. "_" .. id
end

function ResPath.GetNewHeadBigIcon(sex, id)
    if sex == nil or (tonumber(sex) ~= GameEnum.MALE and tonumber(sex) ~= GameEnum.FEMALE) then
        print_error("sex = ",sex, "加载头像数据有问题")
        sex = GameEnum.MALE
    end

    return "uis/icons/toxiang_big_atlas","toxiang_" ..sex.. "_" .. id
end

function ResPath.GetRechargeReward(index)
    if index == DailyTotalRechargeView.BtnState.btn_name_ylq then
        return "uis/uires/res/x1ui/rechargereward_atlas","btn_has_receive"
    elseif index == DailyTotalRechargeView.BtnState.btn_name_lqjl then
        return "uis/images/others_atlas","btn_world_lingqu"
    elseif index == DailyTotalRechargeView.BtnState.btn_name_cdxq then
        return "uis/uires/res/x1ui/rechargereward_atlas","btn_text"
    end
end

function ResPath.GetBuff(client_effect_type)
    local cfg = FightWGData.Instance:GetBuffDescCfgByType(client_effect_type)
    if cfg ~= nil then
        return "uis/icons/buff_atlas", "buff_" .. cfg.icon
    end

    return ""
end

function ResPath.GetEnterSceneTipIcon(name)
    return "uis/view/entercommonscenetip/images_atlas", name
end

function ResPath.FieldOneVOne(name)
    return "uis/uires/res/x1ui/arena_atlas", name
end

function ResPath.ChatBubbleBg(res_id)
    return "uis/chatres/bubbleres/bubble" .. res_id.. "_prefab","ChatRightBubble" .. res_id
end

function ResPath.ChatBigBubbleBig(res_id)
    return "uis/chatres/bubbleres/bubble" .. res_id.. "_prefab","ChatRightBubbleBig" .. res_id
end

function ResPath.BackgroundItem(res_id)
    return "uis/backgrounds/" .. res_id.. "_prefab","BackgroundShow"
end

function ResPath.BackgroundShow(res_id)
    return "uis/backgrounds/" .. res_id.. "_prefab","BackgroundShow"
end

function ResPath.GetNpcTalkVoiceRes(res_id, subsection, prof)
    if not res_id or not subsection then
        return nil, nil
    end

    local name = res_id .. "_" .. subsection .. "_0"
    if prof then
        name = res_id .. "_" .. subsection .. "_" .. prof
    end
    -- print_error(res_id, subsection, prof , name)
    return "audios/sfxs/npctalk", tostring(name)
end

function ResPath.GetNpcTalkVoiceResByResName(res)
    local str_list = Split(res, "_")
    return "audios/sfxs/npctalk", res
end

function ResPath.GetOtherVoiceRes(name)
    return "audios/sfxs/other", name
end

function ResPath.GetSkillVoiceRes(name)
    return "audios/sfxs/skill", name
end

function ResPath.GetSkillVoiceResByFolder(folder_name, asset_name)
    return _sformat("audios/sfxs/skill/%s", folder_name), asset_name
end

function ResPath.GetActDiscountIcon(name, is_hl)
    if is_hl then
        return "uis/uires/res/x1ui/act_discount_atlas", name.. "_hl"
    else
        return "uis/uires/res/x1ui/act_discount_atlas", name
    end
end

function ResPath.GetQilinBiModel( res_id, sex ,is_view_show)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end
    local res_num1 = sex == 1 and 0 or 100000
    local res_num2 = is_view_show and 1 or 0
    res_id = res_id + res_num1 + res_num2
    if sex == 1 then
        return "actors/arm/man/" .. math.floor(res_id / 1000) .. "_prefab", res_id
    else
        return "actors/arm/woman/" .. math.floor(res_id / 1000) .. "_prefab", res_id
    end
end

function ResPath.GetXunBaoCellRender()
    return "uis/view/zhuangbeixunbao_ui_prefab", "xunbao_long_cell"
end

function ResPath.GetXunBaoImg(name)
    return "uis/uires/res/x1ui/xunbao_atlas", name
end

function ResPath.GetAchievement(name)
    return "uis/uires/res/x1ui/achievement_atlas", name
end

function ResPath.GetKF3V3(name)
    return "uis/uires/res/x1ui/kf_pvp_atlas", name
end

function ResPath.GetNpcIcon(npc_icon_id)
    return "uis/view/miscpre_load/floating_atlas", npc_icon_id
end

-- 幼鲲模型
function ResPath.GetKunEggModel(res_id)
    if res_id == nil then
        return nil, nil
    end

    return _sformat("actors/kunegg/%s_prefab", res_id), res_id
end

-- 结婚锁图片
function ResPath.GetJieHunSuoImg(name)
    return "uis/view/marry_ui/images/suo_images_atlas", name
end

-- 骑宠装备图片加载
function ResPath.GetMountPetEquipPath(name)
    return "uis/view/qichong_equip_ui/images_atlas", name
end

function ResPath.GetXunyouImg(name)
    return "uis/view/wedding_ui/images_atlas", name
end

-- 山海经图片加载
function ResPath.GetSHJImgPath(name)
    return "uis/uires/res/x1ui/shj_atlas", name
end

-- F2山海经图片加载
function ResPath.GetF2SHJImgPath(name)
    return "uis/view/shj_ui/images_atlas", name
end
--神技抽奖展示图片
function ResPath.GetSHENJIImgPath(icon_id)
    return "uis/view/shenji_tianci_ui/images_atlas", icon_id
end

-- 宠物模型
function ResPath.GetPetModel(res_id)
    if res_id == nil then
        return nil, nil
    end

    return _sformat("model/chongwu/%s_prefab", res_id), tostring(res_id)
end

-- 一元购
function ResPath.GetYiYuanHaoLiImg(name)
    return "uis/view/yiyuanhaoli_ui/images_atlas", name
end

-- 其他模型
function ResPath.GetOtherModel(res_id)
    if res_id == nil then
        return nil, nil
    end

    return _sformat("actors/other/%s_prefab", res_id), tostring(res_id)
end

--送花
function ResPath.GetF2SendFlowerImagePath(name)
    return "uis/view/sendflower_ui/images_atlas", name
end

function ResPath.GetTianShenShenQiFashionPath(name)
    return "model/mingjiangweaponfacade/" .. name .. "_prefab", name
end

function ResPath.YiBenWanLiImagePath(name)
    return "uis/uires/res/x1ui/yibenwanli_ui_atlas", name
end

function ResPath.YiBenWanLiIconPath(name)
    return "uis/view/yibenwanli_ui/images_atlas", name
end

function ResPath.GetLoadingPath(name)
    return "uis/view/miscpre_load/images_atlas", name
end

--========================= F2 新路径   ========================================
ResPath.CommonBundleName = "uis/view/common_panel_prefab"

function ResPath.GetF2CommonImages(name)
    return "uis/imagesf2/common_atlas", name
end

function ResPath.GetF2BackgroundImages(name)
    return "uis/imagesf2/background_atlas", name
end

function ResPath.GetF2CommonButtonToggle(name)
    return "uis/imagesf2/button&toggle_atlas", name
end

function ResPath.GetF2CommonIcon(name)
    return "uis/imagesf2/icon_atlas", name
end

function ResPath.GetVipIcon(name)
    return "uis/imagesf2/icon/vip_atlas", name
end

function ResPath.GetF2RawImagesJPG(name)
    return "uis/rawimages/" .. name, name .. ".jpg"
end

function ResPath.GetF2RawImagesPNG(name)
     return "uis/rawimages/" .. name, name .. ".png"
end

-- 日常
function ResPath.GetDailyTypeIcon(name)
    return "uis/view/bizuo_ui/images_atlas", name
end

-- 功能开启
function ResPath.GetFunTrailerImages(name)
    return "uis/view/funtrailer/images_atlas", name
end

-- 装备目标
function ResPath.GetRoleBagImg(name)
    return "uis/view/rolebag_ui/images_atlas", name
end

-- 竞技
function ResPath.GetF2Field1v1(name)
    return "uis/view/field1v1_ui/images_atlas", name
end

function ResPath.GetF2Field1v1Rank(id)
    return "uis/view/field1v1_ui/images_atlas", "rank_" .. id
end

-- tips
function ResPath.GetF2TipPrefab(name)
    return "uis/view/itemtip_ui_prefab", name
end

function ResPath.GetF2TipImages(name)
   return "uis/view/itemtip_ui/images_atlas", name
end

function ResPath.GetF2Society(name)
    return "uis/view/society_ui/images_atlas", name
end

function ResPath.GetZhanLingImg(name)
    return "uis/view/zhanling_ui/images_atlas", name
end

--境界
function ResPath.GetF2JingJie(level)
    return "uis/imagesf2/icon/jingjie_atlas", "img_jingjielevel" .. level
end

--十五日巡礼
function ResPath.GetF2WelfareImages(name)
    return "uis/view/welfare_ui/images_atlas", name
end

function ResPath.GetF2FunTrailerImages(name)
    return "uis/view/funtrailer/icons_atlas", name
end

function ResPath.GetF2TianShenImage(name)
    return "uis/view/tianshen/images_atlas", name
end

function ResPath.GetTianShenNopackImg(name)
    return ResPath.GetNoPackPNG(name)
end

function ResPath.GetF2TianShenIcon(name)
    return "uis/view/tianshen/icons_atlas", name
end

function ResPath.GetF2TianShenSkillShowImage(name)
    return "uis/view/tianshenskillshow_ui/images_atlas", name
end

function ResPath.GetTianShenLingHeImage(name)
    return "uis/view/tianshen_linghe_ui/images_atlas", name
end

function ResPath.GetDiceRawImage(name)
    return "uis/rawimages/" .. name, name .. ".png"
end

function ResPath.GetActTipsFlag(type)
    return "uis/view/activity_tips_ui/images/a3_zjm_hdtc_" .. type ..".png"
end

-- 主界面
function ResPath.GetF2MainUIImage( name )
    return "uis/view/mainui/images_atlas", name
end
--------------------- 模型资源获取接口 ---------------------

function ResPath.GetWeaponModelRes(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/weapon/%s_prefab", math.floor(res_id / 100)), tostring(res_id)
end

--神兵
function ResPath.GetShenBingModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/weapon/%s_prefab", math.floor(res_id / 100)), tostring(res_id)
end

--灵弓
function ResPath.GetLingGongModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/weapon/%s_prefab", math.floor(res_id / 100)), tostring(res_id)
end

function ResPath.GetBodyModel(res_id)
    return _sformat("model/character/body/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetFaceModel(res_id)
    return _sformat("model/character/face/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetHairModel(res_id)
    return _sformat("model/character/hair/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetBodyHighModel(res_id)
    return _sformat("model/character/body/%s_hm_prefab", res_id), tostring(res_id)
end

function ResPath.GetFaceHighModel(res_id)
    return _sformat("model/character/face/%s_hm_prefab", res_id), tostring(res_id)
end

function ResPath.GetHairHighModel(res_id)
    return _sformat("model/character/hair/%s_hm_prefab", res_id), tostring(res_id)
end

function ResPath.GetRealmBodyModel(res_id)
    return _sformat("model/character/realm/body/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetRealmFaceModel(res_id)
    return _sformat("model/character/realm/face/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetRealmHairModel(res_id)
    return _sformat("model/character/realm/hair/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetBodyDyeMaterialAsset(res_id, material_res_id)
    return _sformat("model/character/body/%s_dye", res_id), tostring(material_res_id)
end

function ResPath.GetFaceDyeMaterialAsset(res_id, material_res_id)
    return _sformat("model/character/face/%s_dye", res_id), tostring(material_res_id)
end

function ResPath.GetHairDyeMaterialAsset(res_id, material_res_id)
    return _sformat("model/character/hair/%s_dye", res_id), tostring(material_res_id)
end
---------------------------------------------------------------

function ResPath.GetTeamExpRes(name)
    return "uis/view/team_exp_fb/images_atlas", name
end

function ResPath.GetF2ShopUi(name)
    return "uis/view/shop_ui/images_atlas", name
end

function ResPath.GetKFMSUi(name)
    return "uis/view/pierre_direct_purchase_ui/images_atlas", name
end

function ResPath.GetTeamHead(name)
    return "uis/view/team_equip_fb/images_atlas", name
end

function ResPath.GetXiaoHaoIcon(item_id)
    if item_id == 65535 then
        return ResPath.GetCommonIcon("a3_huobi_tongqian")
    elseif item_id == 65533 then
        return ResPath.GetCommonIcon("a3_huobi_bangyu")
    elseif item_id == 65534 then
        return ResPath.GetCommonIcon("a3_huobi_xianyu")
    end

    return ResPath.GetF2CommonIcon("a3_huobi_xianyu")
end

-- 1:仙玉
-- 2:绑玉
-- 3:贡玉
-- 4:声望
-- 5:铜币
function ResPath.GetF2MoneyIcon(money_type)
    local icon = ""
    if money_type == 1 then
        icon = "i_xiaohao_xianyu"
    elseif money_type == 2 then
        icon = "i_xiaohao_bangyu"
    elseif money_type == 3 then
        icon = "i_xiaohao_yuanbao"
    elseif money_type == 4 then
        icon = "i_xiaohao_honor"
    elseif money_type == 5 then
        icon = "i_xiaohao_tongqian"
    end

    return icon
end

function ResPath.GetF2ZhuXieIcon(name)
    return "uis/view/zhuxie_ui/images_atlas", name
end

function ResPath.GetF2HotSpringIcon(name)
    return "uis/view/hot_spring_ui/images_atlas", name
end

function ResPath.GetLongHunImg(name)
    return "uis/view/long_hun_ui/images_atlas", name
end

function ResPath.GetXianLingGuZhenImg(name)
    return "uis/view/xianling_guzhen/images_atlas", name
end

function ResPath.GetLoginUiImg(name)
    return "uis/view/login_ui/images_atlas", name
end

function ResPath.GetCreateRoleIcon(name)
    return "uis/view/login_ui/images/part_icon_atlas", name
end

function ResPath.GetZhouYiYunChengImg(name)
    return "uis/view/zhouyi_yuncheng/images_atlas", name
end

function ResPath.GetF2ZCResultImg(name)
    return "uis/view/zc_result_ui/images_atlas", name
end

function ResPath.GetTaskDiceImg(name)
    return "uis/view/task_dice/images_atlas", name
end

function ResPath.GetTaskImg(name)
    return "uis/view/task/images_atlas", name
end

function ResPath.GetHotSpringBodyModel(res_id)
    return _sformat("model/character/body/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetLimitTimeGiftIcon(res_name)
    return "uis/view/limit_time_gift/images_atlas", res_name
end

function ResPath.GetHuSongIcon(res_name)
    return "uis/view/yunbiao_ui/images_atlas", res_name
end

function ResPath.GetF2TianShenRank(rank)
    rank = rank + 1
    rank = rank > 8 and 8 or rank
    local res_name = "ts_rank" .. rank
    return "uis/view/tianshen/images_atlas", res_name
end

function ResPath.GetKaiFuChongBangUi(res_name)
    return "uis/view/open_server_activity_ui/images_atlas", res_name
end

function ResPath.GetActivityUi(res_name)
    return "uis/view/activity_ui/images_atlas", res_name
end

-- 全民备战
function ResPath.GetQuanMinBeiZhanImagePath(name)
    return "uis/view/quanmin_beizhan_ui/images_atlas", name
end

-- 仙器解封
function ResPath.GetXianQiJieFengImagePath(name)
    return "uis/view/act_xianqi_jiefeng_ui/images_atlas", name
end

function ResPath.GetCountryMapPath(name)
    return "uis/view/country_map_ui/images_atlas", name
end

function ResPath.GetTSActivityImg(name)
    return "uis/view/ts_activity_ui/images_atlas", name
end

function ResPath.GetCountryMapTaskTypePath(name)
    return "uis/view/country_map_ui/shengxiantask_ui/images_atlas", name
end

function ResPath.GetTianShenRoadImg(name)
    return "uis/view/tianshenroad_ui/images_atlas", name
end

--动态运营活动
function ResPath.GetOperationActivityImagePath(name)
    return "uis/view/operation_activity_ui/images_atlas", name
end

--连续充值
function ResPath.GetOperationActCtnRecharge(name)
    return "uis/view/operation_ctn_recharge/images_atlas", name
end

-- 狂欢
function ResPath.GetOperationHappyKhImagePath(name)
    return "uis/view/operation_happy_kuanghuan/images_atlas", name
end

--天才厨神
function ResPath.GetChuShenImg(name)
    return "uis/view/operation_tiancai_chushen/images_atlas", name
end

--兔女郎
function ResPath.GetOATurnTablePath(name)
    return "uis/view/operation_turntable/images_atlas", name
end

-- 兑换商店
function ResPath.GetOperationExchangeShop(name)
    return "uis/view/operation_exchange_shop/images_atlas", name
end

-- 种花浇水
function ResPath.GetOperationWateringFlower(name)
    return "uis/view/operation_watering_flowers/images_atlas", name
end

--风筝夺宝
function ResPath.GetOperationFZduoBaoImagePath(name)
    return"uis/uires/res/x1ui/operation_activity_fengzheng_atlas", name
end

-- 限时秒杀
function ResPath.GetXianShiMiaoShaoPictrue(name)
    return "uis/view/operation_xianshi_miaosha/images_atlas", name
end

--动态运营活动捐献
function ResPath.GetOperationJuanXianImagePath(name)
    return "uis/view/operation_juanxian/images_atlas", name
end

function ResPath.GetOperationActivityLongGuPath(name)
    return "uis/view/operation_activity_longgu_prefab", name
end

function ResPath.GetOperationTaskChainF2(name)
    return "uis/view/operation_task_chain_ui/images_atlas", name
end

----合服活动-------------
--合服招财喵喵
function ResPath.GetMergeMiaoMiaoImg(name)
    return "uis/view/merge_activity_ui/zhaocai_miaomiao/images_atlas", name
end

function ResPath.GetMergeImg(name)
    return "uis/view/merge_activity_ui/images_atlas", name
end

----合服活动 end-------------
function ResPath.GetOperationTaskChainPrefabF2(name)
    return "uis/view/operation_task_chain_ui_prefab", name
end

function ResPath.GetEternalNightUI(res_name)
    return "uis/view/eternal_night_ui/images_atlas", res_name
end

function ResPath.GetTianShenBaGuaModel(res_name, name2)
    return "actors/bagua/" .. res_name.. "_prefab", res_name..name2
end

function ResPath.GetTianShenCloseUpSpine(tianshen_appid)
    return "uis/view/tianshen/spineanimations/tscc_" .. tianshen_appid .. "_prefab", "spine_prefab_" .. tianshen_appid
end

function ResPath.GetTianShen3v3Spine(tianshen_appid)
    return "uis/view/tianshen/spineanimations/tscc_" .. tianshen_appid .. "_prefab", "ts3v3_spine_" .. tianshen_appid
end

function ResPath.GetWorldTreasureSpine(id)
    return "uis/view/world_treasure_ui/spineanimations/a3_hs_" .. id .. "_prefab", "a3_hs_" .. id .. "_spine"
end

function ResPath.GetTsDuoBaoIcon(name)
    return "uis/view/ts_duobao_ui/images_atlas", name
end

function ResPath.GetSnapshotFrameImg(name)
    return "uis/view/screen_shot_ui/images_atlas", name
end

function ResPath.GetOABottomImg(name)
    return "uis/view/operation_activity_bottom_ui/images_atlas", name
end

function ResPath.GetFightSoulImg(name)
    return "uis/view/fight_soul_ui/images_atlas", name
end

function ResPath.GetFightSoulShowUI(res_id)
    return "uis/view/fight_soul_ui/spineanimations/ymgy_spine_" .. res_id .. "_prefab", "ymgy_spine_" .. res_id
end

function ResPath.GetShanhaiBossUI(name)
    return "uis/view/shanhai_boss_ui/images_atlas", name
end

--极品装备掉落
function ResPath.GetBestEquipDropImg(name)
    return "uis/view/drop_equip_show_ui/images_atlas", name
end

-- 小鸭疾走
function ResPath.GetDuckRaceImg(name)
    return "uis/view/duck_race_ui/images_atlas", name
end

function ResPath.GetFloatingImage(name)
    return "uis/view/miscpre_load/floating_atlas", name
end

function ResPath.GetChatUiImg(name)
    return "uis/view/chat_ui/images_atlas", name
end

function ResPath.GetNpcHead(name)
    return "uis/icons/npc_atlas", name
end

--神机模型
function ResPath.GetShenJiModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return "actors/shenji/" .. res_id .. "_prefab", res_id
end

-- 封神榜
function ResPath.GetFengShenBangIcon(name)
    return "uis/view/fengshenbang/images_atlas", name
end

function ResPath.GetCountryMapNotice(name)
    return "uis/view/country_map_ui/country_notice_ui/images_atlas", name
end

function ResPath.GetShenJiGridColor(color)
    return "uis/imagesf2/common_atlas", "color_bg_big_grid_" .. color
end

function ResPath.GetShenJiAwakenBg(lv)
    if lv > 3 then
        lv = 3
    end

    return "uis/imagesf2/common_atlas", "awaken_bg_" .. lv
end

function ResPath.GetShenJiImage(name)
    return "uis/view/shenji_anqiruanjia_ui/images_atlas", name
end

function ResPath.GetLongZhuImage(name)
    return "uis/view/long_zhu_ui/images_atlas", name
end

-- 返利活动UI资源
function ResPath.GetRebateActImage(name)
    return "uis/view/rebate_activity_ui/images_atlas", name
end

-- 珍惜掉落展示
function ResPath.GetRareItemImage(name)
    return "uis/view/rare_item_drop_ui/images_atlas", name
end

--节日活动的图片读取路径 -----------------------------------

function ResPath.GetFestivalRawImages(name)
    local fa_path = FestivalActivityWGData.GetResPath()
    local img_name = "a2_" .. fa_path .. "_" .. name
    return "uis/rawimages/" .. img_name, img_name .. ".png"
end

function ResPath.GetFestivalActImages(name)
    local path = FestivalActivityWGData.GetResPath()
    return "uis/view/festival_activity_ui/" .. path .. "_atlas", name
end

function ResPath.GetFestivalImages(name)
    local path = FestivalActivityWGData.GetResPath()
    return "uis/view/festival_activity_ui/" .. path .. "/common_atlas", name
end

function ResPath.GetFestivalExchageImages(name)
    local path = FestivalActivityWGData.GetResPath()
    return "uis/view/festival_activity_ui/" .. path .. "/exchageimages_atlas", name
end

function ResPath.GetFestivalMowangYouliUIImages(name)
    local path = FestivalActivityWGData.GetResPath()
    return "uis/view/festival_activity_ui/" .. path .. "/mowang_youliimages_atlas", name
end

function ResPath.GetFestivalMiaoshaUiImages(name)
    local path = FestivalActivityWGData.GetResPath()
    return "uis/view/festival_activity_ui/" .. path .. "/miaosha_uiimages_atlas", name
end

function ResPath.GetFestivalSpRankImages(name)
    local path = FestivalActivityWGData.GetResPath()
    return "uis/view/festival_activity_ui/" .. path .. "/sp_rankimages_atlas", name
end

function ResPath.GetFestivalChuShenUiImages(name)
    local path = FestivalActivityWGData.GetResPath()
    return "uis/view/festival_activity_ui/" .. path .. "/chushen_ui_atlas", name
end

function ResPath.GetFairyLandEquipImages(res)
    return "uis/view/fairy_land_equipment_ui/images_atlas", res
end

function ResPath.GetFairyLandBrowseEquipImages(res)
    return "uis/view/browse_ui/images_atlas", res
end

--仙界装备--神体模型
function ResPath.GetFairyLandGodBodyModel(slot, sex)
    local bundle_name = sex == GameEnum.MALE and "xianjiezhuangbei_nanmo_prefab" or "xianjiezhuangbei_nvmo_prefab"
    local asset_name = sex == GameEnum.MALE and "xianjie_nan_" or "xianjie_nv_"
    slot = slot + 1--神体序号从0开始,模型资源从1开始
    if slot < 10 then
        asset_name = asset_name .. "0" .. slot
    else
        asset_name = asset_name .. slot
    end

    return "model/character/" .. bundle_name, asset_name
end


--节日活动的图片读取路径 END -----------------------------------
function ResPath.GetTianShen3v3Img(res_id)
    return "uis/view/tianshen_3v3_ui/images_atlas", res_id
end

function ResPath.GetWorldsNO1Img(res_id)
    return "uis/view/worlds_no1_ui/images_atlas", res_id
end

function ResPath.GetXianyuTrunTableImg(name)
    return "uis/view/xianyu_trun_table/images_atlas", name
end

function ResPath.GetLingZhiImg(name)
    return "uis/view/lingzhi/images_atlas", name
end

function ResPath.GetLoverPkImg(name)
    return "uis/view/cross_lover/images_atlas", name
end



--===========================================================
--===========================================================
--===========================================================
--==================【a1路径】=================================
--===========================================================
--===========================================================
function ResPath.GetImages(name)
    return "uis/images_atlas", name
end

function ResPath.GetCommonImages(name)
    return "uis/images/common_atlas", name
end

function ResPath.GetCommonButton(name)
    return "uis/images/button_atlas", name
end

function ResPath.GetCommonIcon(name)
    return "uis/images/icon_atlas", name
end

function ResPath.GetCommonPanel(name)
    return "uis/images/commonpanel_atlas", name
end

function ResPath.GetRawImagesJPG(name)
    return "uis/rawimages/" .. name, name .. ".jpg"
end

function ResPath.GetRawImagesPNG(name)
    return "uis/rawimages/" .. name, name .. ".png"
end

function ResPath.GetProfRawImagesPNG(sex, prof)
    local str = string.format("a3_crole_prof_%s%s", sex, prof)
    return "uis/rawimages/" .. str, str .. ".png"
end

function ResPath.GetFunctionIcon(name)
    return "uis/images/icon/functionicon_atlas", name
end

-- 1:仙玉
-- 2:绑玉
-- 3:贡玉
-- 4:声望
-- 5:铜币
function ResPath.GetMoneyIcon(money_type)
    local icon = ""
    if money_type == 1 then
        icon = "a3_huobi_xianyu"
    elseif money_type == 2 then
        icon = "a3_huobi_bangyu"
    elseif money_type == 3 then
        icon = "a2_huobi_gongyu"  --美术出图后要替换旧图标
    elseif money_type == 4 then
        icon = "a3_huobi_honor"   --美术出图后要替换旧图标
    elseif money_type == 5 then
        icon = "a3_huobi_tongqian"
    end

    return icon
end

-- 获取技能图标
function ResPath.GetSkillIconById(icon_id)
    local temp_id = icon_id
    icon_id = tonumber(icon_id)
    if not icon_id then
        print_error("---传入技能icon_id错误---", temp_id)
        return nil, nil
    end

    -- 主界面技能图标  Id大于10000
    -- 功能技能图标  Id小于10000
    return _sformat("uis/skillicon/%s", icon_id), _sformat("%s.png", icon_id)
end

-- 系统
function ResPath.GetMainUIIcon(name)
    return "uis/view/mainui/images_atlas", name
end

function ResPath.GetRoleUIImage(name)
    return "uis/view/role_ui/images_atlas", name
end

function ResPath.GetEquipmentIcon(name)
    return "uis/view/equipment_ui/images_atlas", name
end

function ResPath.GetEquipmentSuit(name)
    return "uis/view/equipment_suit_ui/images_atlas", name
end

function ResPath.GetChatType(name)
   return "uis/view/mainui/images_atlas", "a3_zj_bg_" .. name
end

function ResPath.GetSupremeFields(name)
   return "uis/view/supreme_fields_ui/images_atlas", name
end

function ResPath.GetSupremeFieldsTxt(type)
   return "uis/rawimages/a2_dzly_" .. type, "a2_dzly_".. type .. ".png"
end

function ResPath.GetSupremeFieldsSkillTxt(type)
    return "uis/view/supreme_fields_ui/images_atlas/a2_wszj_js_" .. type, "a2_wszj_js_".. type .. ".png"
end

function ResPath.GetSupremeFieldsActTxt(type)
   return "uis/rawimages/a2_wszj_ysz_act_" .. type, "a2_wszj_ysz_act_".. type .. ".png"
end

function ResPath.GetSupremeFieldsIcon(type)
   return "uis/view/supreme_fields_ui/images_atlas", "a1_dzly_icon_" .. type
end

function ResPath.GetGuildSystemImage(name)
    return "uis/view/guild_ui/images_atlas", name
end

function ResPath.GetFuBenImage(name)
    return "uis/view/fubenpanel/images_atlas", name
end

function ResPath.GetKaiFuHuuoDongPanel(name)
    return "uis/view/open_server_activity_ui/images_atlas", name
end

function ResPath.GetMingWenImage(name)
    return "uis/view/ming_wen_ui/images_atlas", name
end

function ResPath.GetCangMingBuyActTxt(type)
    return "uis/rawimages/a2_waist_light_act_" .. type, "a2_waist_light_act_".. type .. ".png"
 end

 function ResPath.GetCangMingBuyBgImage(type)
    return "uis/rawimages/a2_wszj_dt" .. type, "a2_wszj_dt".. type .. ".png"
 end

-- 新形象
function ResPath.GetNewAppearanceImage(name)
    return "uis/view/new_appearance_ui/images_atlas", name
end

--山海经
function ResPath.GetSHJImage(name)
    return "uis/view/shj_ui/images_atlas", name
end

-- 贵族
function ResPath.GetVipImage(name)
    return "uis/view/recharge_ui/images_atlas", name
end

--神兽
function ResPath.GetShenShouImages(name)
    return "uis/view/shenshou_ui/images_atlas", name
end

--寻宝
function ResPath.GetTreasurehuntIcon(name)
    return "uis/view/treasurehunt_ui/images_atlas", name
end

-- 结婚图片
function ResPath.GetJieHunImg(name)
    return "uis/view/marry_ui/images_atlas", name
end

-- 修仙试炼
function ResPath.GetXiuXianShiLianViewImg(name)
    return "uis/view/xiuxianshilian_ui/images_atlas", name
end

--跨服PVP战队
function ResPath.GetZhanDuiItemIcon(name)
    return "uis/view/zhandui_ui/images_atlas", name
end

-- 祈福
function ResPath.GetQiFuImages(name)
    return "uis/view/qifu_ui/images_atlas", name
end

-- 仙盟战
function ResPath.GetXMZNewImg(name)
    return "uis/view/guild_battle_new_ui/images_atlas", name
end

--装备印记
function ResPath.GetEquipmentMarkImages(name)
    return "uis/view/equipment_mark_ui/images_atlas", name
end

--魂兵
function ResPath.GetSiXiangCallImg(name)
    return "uis/view/sixiang_call/images_atlas", name
end

function ResPath.GetRebateActivityImg(name)
    return "uis/view/rebate_gift_ui/images_atlas", name
end

--炼丹
function ResPath.GetAlchemyImages(name)
    return "uis/view/country_map_ui/alchemy/image_atlas", name
end

-- 五行
function ResPath.GetFiveElementsImg(name)
    return "uis/view/five_elements_ui/images_atlas", name
end

-- 限购
function ResPath.GetLimitBuyImg(name)
    return "uis/view/chaotic_purchase_ui/images_atlas", name
end

-- 限购2
function ResPath.GetLimitBuy2Img(name)
    return "uis/view/today_special_ui/images_atlas", name
end

-- 仙灵直购
function ResPath.GetTianShenPurchaseImg(name)
    return "uis/view/tianshen_purchase_ui/images_atlas", name
end

-- 仙灵直购背景
function ResPath.GetTianShenPurchaseBgImg(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("uis/rawimages/%s", res_id), tostring(res_id).. ".png"
end

-- 鸿蒙神藏
function ResPath.GetHmGodImg(name)
    return "uis/view/hm_god_ui/images_atlas", name
end

-- 归墟梦魇
function ResPath.GetGuiXuDreamImg(name)
    return "uis/view/guixudream_ui/images_atlas", name
end

-- 技能定制
function ResPath.GetCustomizedImg(name)
    return "uis/view/customized_suit_ui/images_atlas", name
end

-- DIY抽奖
function ResPath.GetDIYDrawImg(name)
    return "uis/view/diy_draw_ui/images_atlas", name
end

-- 圣器/暗器
function ResPath.GetHolyDarkImg(name)
    return "uis/view/holy_dark_ui/images_atlas", name
end

-- 双修(仙魔神器)
function ResPath.GetArtifactImg(name)
    return "uis/view/artifact_ui/images_atlas", name
end

--弑天套装
function ResPath.GetShiTianSuitImg(name)
    return "uis/view/shitian_suit_ui/images_atlas", name
end

function ResPath.GetShiTianSuitIcon(name)
    return "uis/view/shitian_suit_ui/icons_atlas", name
end

--称号
function ResPath.GetChengHaoImg(name)
    return "uis/view/chenghao/images_atlas", name
end

--龙神殿
function ResPath.GetDragonTempleImg(name)
    return "uis/view/dragon_temple_ui/images_atlas", name
end

function ResPath.GetMultiFunctionImg(name)
    return "uis/view/multi_function_ui/images_atlas", name
end

-- boss必爆
function ResPath.GetBossMustFallPrivilegeImg(name)
    return "uis/view/boss_must_fall_privilege_ui/images_atlas", name
end

--唯我独尊
function ResPath.GetWeiWoDuZunImg(name)
    return "uis/view/weiwoduzun_ui/images_atlas", name
end

--新战斗坐骑
function ResPath.GetNewFightMountImg(name)
    return "uis/view/new_fight_mount_ui/images_atlas", name
end

function ResPath.GetHolyHeavenlyDomainImg(name)
    return "uis/view/holy_heavenly_domain_ui/images_atlas", name
end

-- country_id 从0开始 16 灰色   type 从1开始 
function ResPath.GetHolyHeavenlyDomainCountryImg(country_id, type)
    country_id = country_id and country_id >= 0 and country_id or 16
    type = type or 1
    return "uis/view/holy_heavenly_domain_ui/country_atlas", "a2_stsy_cs_" .. type.. "_" .. country_id
end

function ResPath.GetCustomizedRumorsImg(name)
    return "uis/view/customized_rumors_ui/images_atlas", name
end

function ResPath.GetCustomizedRumorsWidgets(res_name)
    return "uis/view/customized_rumors_ui_prefab", res_name
end

function ResPath.GetMechaImg(name)
    return "uis/view/mecha_ui/images_atlas", name
end

-- 模型(鲲模型)
function ResPath.GetMountModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/zuoqi/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetMonsterModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/boss/%s_prefab", res_id), tostring(res_id)
end

-- 天兵
function ResPath.GetDivineSoldierModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/tianhunshenqi/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetNpcModel(res_id)
    res_id = tonumber(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/npc/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetNpcWeaponModel(res_id)
    res_id = tonumber(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/npc/%s_prefab", res_id), tostring(res_id)
end

function ResPath.GetTianShenShenQiPath(name)
    return _sformat("model/tianshenwuqi/%s_prefab", name), name
end

function ResPath.GetBianShenModel(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/tianshen/%s_prefab", res_id), tostring(res_id)
end

-- 双生神灵
function ResPath.GetShuangShengModel( res_id )
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/ssshenling/%s_prefab", res_id), tostring(res_id)
end

-- 双生神灵
function ResPath.GetShuangShengModelUI( res_id )
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("model/ssshenling/%s_prefab", res_id), res_id
end

-- 普通法阵（已废弃）
function ResPath.GetFaZhenModel(res_id)
    return "", ""
end

function ResPath.GetRiderSwordCarrier(sex)
    if sex == GameEnum.FEMALE then
        return "model/yujian/woman/900103701_prefab", "900103701"
    else
        return "model/yujian/man/900106501_prefab", "900106501"
    end
end

function ResPath.GetWuHunZhenShenImage(name)
    return "uis/view/wuhunzhenshen/images_atlas", name
end

function ResPath.GetWaterModel()
    return "model/water_prefab", "UI_Water"
end

function ResPath.GetSyatemForcePNG(name)
    return "uis/view/system_force_ui/images_atlas", name
end

-- 御兽天下背景
function ResPath.GetControlBeastsBgImg(res_id)
    if res_id == nil or res_id == 0 then
        return nil, nil
    end

    return _sformat("uis/rawimages/%s", res_id), tostring(res_id).. ".png"
end

function ResPath.GetSkillShowImg(res_str)
    return "uis/view/skill_show_ui/images_atlas", res_str
end

function ResPath.GetCrossFGBPathImg(name)
    return "uis/view/country_map_ui/flag_grabing_battlefield_ui/images_atlas", name
end

--驭兽图标
function ResPath.GetControlBeastsImg(name)
    return "uis/view/control_beasts_ui/images_atlas", name
end

--驭兽模型
function ResPath.GetBeastsModel(res_id)
    return _sformat("model/yushou/%s_prefab", res_id), res_id
end

--驭兽抽奖spine
function ResPath.GetBeastsSpine(res_id)
    return _sformat("uis/view/control_beasts_ui/spineanimations/spine_%s_prefab", res_id), "spine_" .. res_id
end

function ResPath.GetCangJinShopNopackImg(name)
    return "uis/view/cangjin_shop/images/nopack_atlas", name
end

function ResPath.GetTwinDirectPurchaseImage(name)
    return "uis/view/twin_direct_purchase_ui/images_atlas", name
end

function ResPath.GetCultivationStageIcon(stage)
    return "uis/images/icon/cultivationstage_atlas", "a3_zjm_xwjd" .. stage
end

function ResPath.GetCustomActionImg(name)
    return "uis/view/custom_action_ui/images_atlas", name
end

function ResPath.GetCultivationImg(name)
    return "uis/view/cultivation_ui/images_atlas", name
end

function ResPath.GetDujieImg(name)
    return "uis/view/dujie_ui/images_atlas", name
end

function ResPath.GetDujieBodyImg(name)
    return "uis/view/dujie_body_ui/images_atlas", name
end

function ResPath.GetMapImg(name)
    return "uis/view/map_ui/images_atlas", name
end

function ResPath.GetCountryUltimateImg(name)
    return "uis/view/country_map_ui/ultimate_battlefield/images_atlas", name
end

function ResPath.GetMarryRankBgImg(name)
    return "uis/view/marry_rank_ui/images_atlas", name
end

function ResPath.GetSwornImg(name)
    return "uis/view/sworn_ui/images_atlas", name
end

function ResPath.GetJHSYImg(name)
    return "uis/view/jinghuashuiyue_ui/images_atlas", name
end

function ResPath.GetMysteryBoxImg(name)
    return "uis/view/mystery_box_ui/images_atlas", name
end

-- 奇闻异物
function ResPath.GetStrangeCatalogImg(name)
    return "uis/view/strange_catalog_ui/images_atlas", name
end

function ResPath.GetSwornRechargeImg(name)
    return "uis/view/sworn_recharge/images_atlas", name
end

function ResPath.GetFortuneCatImg(name)
    return "uis/view/fortunecat_ui/images_atlas", name
end

function ResPath.GetNoPackPNG(name)
    if not name then
        return nil, nil
    end

    return _sformat("uis/nopack/%s", name), _sformat("%s.png", name)
end

function ResPath.GetNoPackJPG(name)
    if not name then
        return nil, nil
    end

    return _sformat("uis/nopack/%s", name), _sformat("%s.jpg", name)
end

--合成.
function ResPath.GetComposeImg(name)
    return "uis/view/compose_ui/images_atlas", name
end

-- 修仙缘梦
function ResPath.GetXiuZhenRoadImg(name)
    return "uis/view/xiuzhen_road_ui/images_atlas", name
end

-- 阵地战
function ResPath.GetPositionalWarfareImg(name)
    return "uis/view/positional_warfare_ui/images_atlas", name
end

-- 首充
function ResPath.GetFirstRechargeImg(name)
    return "uis/view/rechargereward_ui/images_atlas", name
end

--委托任务|历练
function ResPath.GetOfflinerestImages(name)
    return "uis/view/offlinerest_ui/images_atlas", name
end

-- HUD预制体
function ResPath.GetHUDAssetManager()
    return "uis/hudprogramme/prefabs_prefab", "hud_asset_manager"
end

function ResPath.GetUIModelDisplay()
	return "uis/view/modeldisplay_prefab", "UIModelDisplay"
end

-- 历练
function ResPath.GetOfflinerestImg(name)
    return "uis/view/offlinerest_ui/images_atlas", name
end

-- 成就
function ResPath.GetAchievementImg(name)
    return "uis/view/achievement_ui/images_atlas", name
end

-- 梦灵
function ResPath.GetMengLingImg(name)
    return "uis/view/mengling_ui/images_atlas", name
end

-- 贯日长虹 消费抽奖
function ResPath.GetSunRainbowImg(name)
    return "uis/view/sun_rainbow_ui/images_atlas", name
end

-- 打地鼠
function ResPath.GetHitHamsterImg(name)
    return "uis/view/hit_hamster/images_atlas", name
end

--龙云战令.
function ResPath.GetLongYunZhanLingImg(name)
    return "uis/view/longyun_zhanling_ui/images_atlas", name
end

--商城.
function ResPath.GetMarketImg(name)
    return "uis/view/market_ui/images_atlas", name
end

--拍卖提示弹窗.
function ResPath.GetAuctionTipsImg(name)
    return "uis/view/auction_tips_ui/images_atlas", name
end

function ResPath.GetEquipBodyIcon(name)
    return "uis/images/icon/equipbody_atlas", name
end

--开服投资
function ResPath.GetOpenServerInvestImg(name)
    return "uis/view/openserver_invest_ui/images_atlas", name
end

--须弥直购
function ResPath.GetGodPurchaseImg(name)
    return "uis/view/god_purchase_ui/images_atlas", name
end

--招财进宝
function ResPath.GetGodOfWealthImg(name)
    return "uis/view/god_of_wealth_ui/images_atlas", name
end

--时装兑换商店.
function ResPath.GetFashionExchangeShopImg(name)
    return "uis/view/fashion_exchange_shop_ui/images_atlas", name
end

-- 天财地宝
function ResPath.GetWorldTreasureImg(name)
    return "uis/view/world_treasure_ui/images_atlas", name
end

function ResPath.GetWorldTreasureRawImages(name)
    local fa_path = WorldTreasureWGData.Instance:GetResPath()
    local img_name = "a3_" .. fa_path .. "_" .. name
    return "uis/rawimages/" .. img_name, img_name .. ".png"
end

-- 累充豪礼
function ResPath.GetTotalRechargeGiftImg(name)
    return "uis/view/total_recharge_gift_ui/image_atlas", name
end

-- 获取累充豪礼资源
function ResPath.GetCumulateRechargeImage(name)
    if not name then
        return nil, nil
    end
    
    return _sformat("uis/view/recharge_volume_ui/images_atlas"), name
end

--新节日活动
function ResPath.GetNewFestivalRawImages(name)
    local fa_path = NewFestivalActivityWGData.Instance:GetResPath()
    local img_name = "a3_nfa_" .. fa_path .. "_" .. name
    return "uis/rawimages/" .. img_name, img_name .. ".png"
end

function ResPath.GetNewFestivalActImages(name)
    local path = NewFestivalActivityWGData.Instance:GetResPath()
    return "uis/view/new_festival_activity_ui/" .. path .. "_atlas", name
end

-- 阵地战预热活动
function ResPath.GetTianShiGodownHillImg(name)
    return "uis/view/tianshi_godownhill_ui/images_atlas", name
end

-- boss入侵
function ResPath.GetBossInvasionImg(name)
    return "uis/view/boss_invasion_ui/images_atlas", name
end

-- 百亿补贴
function ResPath.GetBillionSubsidyImg(name)
    return "uis/view/billion_subsidy_ui/images_atlas", name
end

-- 龙玺
function ResPath.GetLongXiImg(name)
    return "uis/view/long_xi_ui/images_atlas", name
end

-- 捏脸
function ResPath.GetDiyAppearanceImg(name)
    return "uis/view/role_diy_appearance_ui/images_atlas", name
end

function ResPath.GetFaceDecalImg(face_decal_id, sex)
    local cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalByIdCfg(face_decal_id)
    if not cfg then
        return nil, nil
    end

    local asset_name = sex == 1 and cfg.nan_asset_name or cfg.nv_asset_name
    return "actors/face_decal_textures", asset_name .. ".tga"
end

function ResPath.GetPupilTypeImg(pupil_type)
    local cfg = RoleDiyAppearanceWGData.Instance:GetPupilTypeByIdCfg(pupil_type)
    if not cfg then
        return nil, nil
    end

    return "actors/pupil_textures", cfg.asset_name .. ".tga"
end

-- 本服魅力榜
function ResPath.GetActCharmRankImg(name)
    return "uis/view/rolecharmnotice_ui/images_atlas", name
end

--天裳仙衣兑换商店.
function ResPath.GetGloryCrystalExchangeShopImg(name)
    return "uis/view/glory_crystal_ui/images_atlas", name
end

--修为
function ResPath.GetXiuWeiImg(name)
    return "uis/view/xiuwei_ui/images_atlas", name
end

-- 烟雨阁
function ResPath.GetYanYuGeImg(name)
    return "uis/view/yanyuge_ui/images_atlas", name
end

--衣橱
function ResPath.GetWardrobeImg(name)
    return "uis/view/wardrobe_new_ui/images_atlas", name
end

-- 获取男角色时装icon
function ResPath.GetManRoleWardrobeIcon(item_id, prof)
    return "uis/icons/wardrobe/roleman_atlas", item_id
end

-- 获取女角色时装icon
function ResPath.GetWomanRoleWardrobeIcon(item_id, prof)
    return "uis/icons/wardrobe/rolewoman_atlas", item_id
end

-- 获取通用时装icon
function ResPath.GetCommonWardrobeIcon(item_id, prof)
    return "uis/icons/wardrobe/common_atlas", item_id
end

-- 获取武器时装icon(区分职业)
function ResPath.GetCommonWardrobeWeaponIcon(item_id, prof)
    return _sformat("uis/icons/wardrobe/weapon/prof%d_atlas", prof), item_id
end

--通用奖励预览.
function ResPath.GetRewardShowTipPrefab(name)
    return "uis/view/common_reward_show_ui_prefab", name
end

--百倍爆装
function ResPath.GetHundredEquipImg(name)
    return "uis/view/hundred_equip_ui/images_atlas", name
end

function ResPath.GetExpGuideImg(name)
    return "uis/view/exp_guide_ui/images_atlas", name
end

function ResPath.GetPrivilegeCollectionImg(name)
    return "uis/view/privilege_collection_ui/images_atlas", name
end


--终身特惠.
function ResPath.GetLifeIndulgenceImg(name)
    return "uis/view/life_indulgence/images_atlas", name
end

-- 仙法直购
function ResPath.GetXianFaImg(name)
    return "uis/view/cangmingbuy_ui/images_atlas", name
end

-- 霸服指引
function ResPath.GetGameAssistantImg(name)
    return "uis/view/game_assistant/images_atlas", name
end

-- 跨服藏宝
function ResPath.GetGameCrossTreasureImg(name)
    return "uis/view/cross_treasure_ui/images_atlas", name
end

function ResPath.GetHuanHuaFetterImg(name)
    return "uis/view/huanhua_fetter_ui/images_atlas", name
end

--守护升级-小鬼.
function ResPath.GetGuardComposeImg(name)
    return "uis/view/gurad_invalid_time/images_atlas", name
end

--圣兽来袭
function ResPath.GetXuYuanFleshPoolImg(name)
    return "uis/view/xuyuan_freshpool_ui/images_atlas", name
end