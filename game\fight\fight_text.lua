FightText = FightText or BaseClass()

local TypeCamera = typeof(UnityEngine.Camera)
local TypeRect = typeof(UnityEngine.RectTransform)
local TypeText = typeof(TMPro.TextMeshProUGUI)
local TypeAnimator = typeof(UnityEngine.Animator)

local need_hide_view = {
	GuideModuleName.DujieOperateView
}

function FightText:__init()
	if FightText.Instance then
		--print_error("[FightText]:Attempt to create singleton twice!")
	end
	FightText.Instance = self

	self.is_active = true
    local obj = ResMgr:Instantiate(SafeBaseView.GetBaseViewParentTemplate())
	if obj then
		obj.name = "FightCanvas"
		obj:SetActive(true)
	
		self.canvas = obj:GetComponent(typeof(UnityEngine.Canvas))
		self.canvas.overrideSorting = true
		self.canvas.worldCamera = UICamera
		self.canvas.sortingOrder = UiLayer.FloatText
		self.canvas_transform = obj.transform
	
		obj:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster)).enabled = false

		self.canvas_transform:SetParent(UILayer.transform, false)
		self.canvas_transform:SetLocalScale(1, 1, 1)
		local rect = self.canvas_transform:GetComponent(TypeRect)

		rect.anchorMax = u3dpool.vec2(1, 1)
		rect.anchorMin = u3dpool.vec2(0, 0)
		rect.anchoredPosition3D = u3dpool.vec3(0, 0, 0)
		rect.sizeDelta = u3dpool.vec2(0, 0)
	end

	self.max_text_count = 25
	self.current_text_count = 0
	self.show_text_loader_t = {}
	self.wait_event_t = {}
	self.attach_position_list = {}
end

function FightText:__delete()
	FightText.Instance = nil
	if self.attr_add_timer then
		GlobalTimerQuest:CancelQuest(self.attr_add_timer)
		self.attr_add_timer = nil
	end
end

function FightText:GetCanvas()
	return self.canvas
end


function FightText:SetActive(value)
	if value and self:IsNeedHideView() then
		value = false
	end
	FollowUi.SetActive(value)
	if self.is_active == value then return end
	self.is_active = value
	if self.canvas_transform then
		self.canvas_transform.gameObject:SetActive(value)
		if value then
			self.canvas_transform:SetParent(UILayer.transform, false)
			self.canvas_transform:SetLocalScale(1, 1, 1)
			local rect = self.canvas_transform:GetComponent(TypeRect)
			rect.anchorMax = u3dpool.vec2(1, 1)
			rect.anchorMin = u3dpool.vec2(0, 0)
			rect.anchoredPosition3D = u3dpool.vec3(0, 0, 0)
			rect.sizeDelta = u3dpool.vec2(0, 0)
		end
	end
	if value then
		self:RemoveAll()
	end
end

--任务特效
function FightText:TaskStateChangeText(task_state, parent)
	if not self.is_active or not self.canvas then
		return
	end

	local attach = Scene.Instance:GetMainRole().draw_obj:GetAttachPoint(AttachPoint.UI)
	if nil == attach and nil == parent then return end
	local attach_position = attach.position
	local task_change_text_loader = AllocAsyncLoader(self, "TaskStateChangeTextEffect")
	task_change_text_loader:SetIsUseObjPool(true)
	task_change_text_loader:SetObjAliveTime(5)
	task_change_text_loader:SetParent(parent or self.canvas_transform)
	local bundle, asset = ResPath.GetEffectUi(task_state)
	task_change_text_loader:Load(bundle, asset, function(obj)
		if not obj or IsNil(MainCamera) then
			return
		end

		obj:SetActive(true)
		if not parent then
			obj.transform.localPosition = u3dpool.vec3(0, 158, 0)
		end
	end)
end

local function ShowTextLoadCallBack(obj, cbdata)
	local self = cbdata[1]
	local text = cbdata[2]
	local text_count = cbdata[3]
	local is_main_role = cbdata[4]
	local attach_position = cbdata[5]
	local special_param = cbdata[6]
	local text_path = cbdata[7]
	local param_path = cbdata[8]
	FightText.ReleaseCBData(cbdata)

	if not obj or IsNil(MainCamera) then
		return
	end

	if text ~= nil then
        local text_obj = obj.transform:Find(text_path)
        local text_component
        if not IsNil(text_obj) then
            text_component = text_obj:GetComponent(TypeText)
            if text_component then
                text_component.text = text
            end
        end

		if special_param ~= nil and param_path ~= nil then
			text_obj = obj.transform:Find(param_path)
			if not IsNil(text_obj) then
				text_component = text_obj:GetComponent(TypeText)
				if text_component then
					text_component.text = special_param
				end
			end
		end
	end

	local cb = self.wait_event_t[text_count]
	if nil == cb then
		cb = function ()
			local show_text_loader = self.show_text_loader_t[text_count]
			if show_text_loader then
				local go = show_text_loader:GetGameObj()
				if not IsNil(go) then
					local animator = go:GetComponent(TypeAnimator)
					animator.cullingMode = AnimatorCullingMode.CullCompletely
				end
				show_text_loader:Destroy()
			end
		end
		self.wait_event_t[text_count] = cb
	end

	local animator = obj:GetComponent(TypeAnimator)
	animator.cullingMode = AnimatorCullingMode.AlwaysAnimate
	animator:WaitEvent("exit", cb)

	Vector3.SetTemporary(attach_position)
	attach_position = UIFollowTarget.CalculateScreenPosition(attach_position, MainCamera, self.canvas, obj.transform.parent)
	if not is_main_role then
		attach_position.y = attach_position.y + math.random(-1, 4)
		attach_position.x = attach_position.x + math.random(-4, 4)
	end

	obj.transform.position = attach_position
end

function FightText:ShowText(bundle, asset, text, attach_point, is_main_role, special_param, text_path, param_path, alive_time)
	if text == 0 or text == "0" then
		return
	end

	if not self.is_active then
		return
	end
	if nil == self.canvas then
		return
	end

	alive_time = alive_time or 5
	self.current_text_count = self.current_text_count + 1
	if self.current_text_count > 25 then
		self.current_text_count = 0
	end

	local attach_position = self.attach_position_list[self.current_text_count]
	if nil == attach_position then
		attach_position = Vector3(0, 0, 0)
		self.attach_position_list[self.current_text_count] = attach_position
	end
	attach_position = Transform.GetPosition(attach_point, attach_position)

	local show_text_loader = AllocAsyncLoader(self, "FightText_UpLevel_Effect".. self.current_text_count)
	self.show_text_loader_t[self.current_text_count] = show_text_loader
	show_text_loader:SetIsUseObjPool(true, ResPoolReleasePolicy.Culling)
	show_text_loader:SetObjAliveTime(alive_time)
	show_text_loader:SetParent(self.canvas_transform)

	local cbdata = FightText.GetCBData()
	cbdata[1] = self
	cbdata[2] = text
	cbdata[3] = self.current_text_count
	cbdata[4] = is_main_role
	cbdata[5] = attach_position
	cbdata[6] = special_param
	cbdata[7] = text_path or "Text"
	cbdata[8] = param_path or ""
	show_text_loader:Load(bundle, asset, ShowTextLoadCallBack, cbdata)
end

function FightText:RemoveAll()
	for k,v in pairs(self.show_text_loader_t) do
		v:Destroy()
	end
	self.show_text_loader_t = {}
	self.current_text_count = 0
end

local floatingtext_bundle = "uis/view/floatingtext_ui_prefab"
function FightText:ShowHurt(text, pos, attach_point, text_type, is_main_role,deliverer_wuxing_type,my_wuxing_type)
	if 0 == text then return end
	local add_str = pos.is_top and "1" or ""
	text_type = text_type or FIGHT_TEXT_TYPE.NORMAL
	if text_type == FIGHT_TEXT_TYPE.NORMAL then
		if pos.is_left then
			if deliverer_wuxing_type and deliverer_wuxing_type == WUXING_HURT_TYPE.WUXING_JIN then
				my_wuxing_type = my_wuxing_type or 0
				local text_wuxing_type = Language.TianShen.WuXingHurtText[deliverer_wuxing_type][my_wuxing_type]
				if text_wuxing_type ~= "" then
					text = text_wuxing_type..text
					self:ShowText(floatingtext_bundle,"HurtJinLeft",text,attach_point,is_main_role)
				else
					self:ShowText(floatingtext_bundle,"HurtJinLeft1",text,attach_point,is_main_role)
				end
			elseif deliverer_wuxing_type and deliverer_wuxing_type == WUXING_HURT_TYPE.WUXING_MU then
				my_wuxing_type = my_wuxing_type or 0
				local text_wuxing_type = Language.TianShen.WuXingHurtText[deliverer_wuxing_type][my_wuxing_type]
				if text_wuxing_type ~= "" then
					text = text_wuxing_type..text
					self:ShowText(floatingtext_bundle,"HurtMuLeft",text,attach_point,is_main_role)
				else
					self:ShowText(floatingtext_bundle,"HurtMuLeft1",text,attach_point,is_main_role)
				end
			elseif deliverer_wuxing_type and deliverer_wuxing_type == WUXING_HURT_TYPE.WUXING_SHUI then
				my_wuxing_type = my_wuxing_type or 0
				local text_wuxing_type = Language.TianShen.WuXingHurtText[deliverer_wuxing_type][my_wuxing_type]
				if text_wuxing_type ~= "" then
					text = text_wuxing_type..text
					self:ShowText(floatingtext_bundle,"HurtShuiLeft",text,attach_point,is_main_role)
				else
					self:ShowText(floatingtext_bundle,"HurtShuiLeft1",text,attach_point,is_main_role)
				end
			elseif deliverer_wuxing_type and deliverer_wuxing_type == WUXING_HURT_TYPE.WUXING_HUO then
				my_wuxing_type = my_wuxing_type or 0
				local text_wuxing_type = Language.TianShen.WuXingHurtText[deliverer_wuxing_type][my_wuxing_type]
				if text_wuxing_type ~= "" then
					text = text_wuxing_type..text
					self:ShowText(floatingtext_bundle,"HurtHuoLeft",text,attach_point,is_main_role)
				else
					self:ShowText(floatingtext_bundle,"HurtHuoLeft1",text,attach_point,is_main_role)
				end
			elseif deliverer_wuxing_type and deliverer_wuxing_type == WUXING_HURT_TYPE.WUXING_TU then
				my_wuxing_type = my_wuxing_type or 0
				local text_wuxing_type = Language.TianShen.WuXingHurtText[deliverer_wuxing_type][my_wuxing_type]
				if text_wuxing_type ~= "" then
					text = text_wuxing_type..text
					self:ShowText(floatingtext_bundle,"HurtTuLeft",text,attach_point,is_main_role)
				else
					self:ShowText(floatingtext_bundle,"HurtTuLeft1",text,attach_point,is_main_role)
				end
			else
				self:ShowText(floatingtext_bundle, "HurtLeft1", text, attach_point, is_main_role)
			end
		else
			self:ShowText(floatingtext_bundle, "HurtRight1", text, attach_point, is_main_role)
		end
	elseif text_type == FIGHT_TEXT_TYPE.BAOJU then
		if pos.is_left then
			self:ShowText(floatingtext_bundle, "BeHurtBaoJuLeft" .. add_str, "if" .. text, attach_point, is_main_role)
		else
			self:ShowText(floatingtext_bundle, "BeHurtBaoJuLeft" .. add_str, "if" .. text, attach_point, is_main_role)
		end
	elseif text_type == FIGHT_TEXT_TYPE.NVSHEN then
		if pos.is_left then
			self:ShowText(floatingtext_bundle, "HurtLeftNvShen" .. add_str, "ic" .. text, attach_point, is_main_role)
		else
			self:ShowText(floatingtext_bundle, "HurtRightNvShen" .. add_str, "ic" .. text, attach_point, is_main_role)
		end
	end
end

function FightText:ShowGeneralHurt(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	local add_str = pos.is_top and "1" or ""
	text_type = text_type or FIGHT_TEXT_TYPE.GREATE_SOLDIER
	if text_type == FIGHT_TEXT_TYPE.GREATE_SOLDIER then
		-- self:ShowText(floatingtext_bundle, "HurtGeneral", text, attach_point)
		if pos.is_left then
			self:ShowText(floatingtext_bundle, "HurtLeftNvShen" .. add_str, "ic" .. text, attach_point, is_main_role)
		else
			self:ShowText(floatingtext_bundle, "HurtRightNvShen" .. add_str, "ic" .. text, attach_point, is_main_role)
		end
	end
end

function FightText:ShowCritical(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	local add_str = pos.is_top and "1" or ""
	text_type = text_type or FIGHT_TEXT_TYPE.NORMAL
	if text_type == FIGHT_TEXT_TYPE.NORMAL then
		if pos.is_left then
			self:ShowText(floatingtext_bundle, "CriticalLeft", "b" .. text, attach_point, is_main_role)
		else
			self:ShowText(floatingtext_bundle, "CriticalRight", "b" .. text, attach_point, is_main_role)
		end
	end
end

function FightText:ShowLianji(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "LianjiLeft", "l" .. text, attach_point, is_main_role)
end

function FightText:ShowJiChuan(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "JiChuanLeft", "j" .. text, attach_point, is_main_role)
end

function FightText:ShowMineHuiXinHurt(text, pos, attach_point, is_main_role)
	if 0 == text then return end
	local add_str = pos and pos.is_top and "1" or ""
		self:ShowText(floatingtext_bundle, "BeMineHuiXinLeft", "h" .. math.abs(tonumber(text)), attach_point, is_main_role)
end

function FightText:ShowHuiXinHurt(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	local add_str = pos and pos.is_top and "1" or ""
		self:ShowText(floatingtext_bundle, "BeHuiXinLeft", "h" .. text, attach_point, is_main_role)
end

function FightText:ShowGeDangHurt(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	local add_str = pos and pos.is_top and "1" or ""
		self:ShowText(floatingtext_bundle, "BeGeDangLeft", "g" .. text, attach_point, is_main_role)
end

function FightText:ShowGeDangRight(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "GeDang", "g" .. text, attach_point, is_main_role)
end

function FightText:ShowBeHurt(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	local add_str = pos and pos.is_top and "1" or ""
	-- if pos.is_left then
		self:ShowText(floatingtext_bundle, "BeHurtLeft", text, attach_point, is_main_role)
	-- else
	-- 	self:ShowText(floatingtext_bundle, "BeHurtRight" .. add_str, text, attach_point)
	-- end
end

function FightText:ShowBaoJuBeHurt(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then return end
	local add_str = pos and pos.is_top and "1" or ""
	-- if pos.is_left then
		self:ShowText(floatingtext_bundle, "BeHurtBaoJuLeft", "if" .. text, attach_point, is_main_role)
	-- else
	-- 	self:ShowText(floatingtext_bundle, "BeHurtRight" .. add_str, text, attach_point)
	-- end
end


function FightText:ShowBeCritical(text, pos, attach_point, is_main_role)
	if 0 == text then return end
	-- local add_str = pos and  pos.is_top and "1" or ""
	-- if pos.is_left then
		self:ShowText(floatingtext_bundle, "BeHurtLeft", "b" .. text, attach_point, is_main_role)
	-- else
	-- 	self:ShowText(floatingtext_bundle, "BeHurtRight" .. add_str, text, attach_point)
	-- end
end

function FightText:ShowDodge(pos, attach_point, is_main_role, is_main_role, is_main_role)
	local add_str = pos and  pos.is_top and "1" or ""
	-- if pos.is_left then
	if is_main_role then
		self:ShowText(floatingtext_bundle, "BeDodge", nil, attach_point, is_main_role) 	--未命中
	else
		self:ShowText(floatingtext_bundle, "Dodge", nil, attach_point, is_main_role)	--闪避
	end
end

function FightText:ShowRecover(text, attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "Recover", "+" .. text, attach_point, is_main_role)
end

-- 免疫
function FightText:ShowMianYi(attach_point, is_main_role)
	self:ShowText(floatingtext_bundle, "Immune", nil, attach_point, is_main_role)
end

-- 免疫
function FightText:ShowMianYiRight(attach_point, is_main_role)
	self:ShowText(floatingtext_bundle, "immuneright", nil, attach_point, is_main_role)
end

-- 反伤
function FightText:ShowFanShang(text,attach_point, is_main_role)
	if 0 == text then return end

	self:ShowText(floatingtext_bundle, "HurtLeft1", text, attach_point, is_main_role)
end

-- 流血
function FightText:ShowLiuXue(text,attach_point, is_main_role)
	if 0 == text then return end
	text = "il" .. math.abs(text)
	self:ShowText(floatingtext_bundle, "BloodLeft", text, attach_point, is_main_role)
end

-- 流血右
function FightText:ShowLiuXueRight(text, attach_point, is_main_role)
	if 0 == text then return end
	text = "il" .. math.abs(text)
	self:ShowText(floatingtext_bundle, "BloodRight", text, attach_point, is_main_role)
end

-- 中毒
function FightText:ShowZhongDu(text,attach_point, is_main_role)
	if 0 == text then return end
	text = "i" .. math.abs(text)
	self:ShowText(floatingtext_bundle, "PoisonLeft", text, attach_point, is_main_role)
end

-- 中毒 右
function FightText:ShowZhongDuRight(text,attach_point, is_main_role)
	if 0 == text then return end
	text = "i" .. math.abs(text)
	self:ShowText(floatingtext_bundle, "PoisonRight", text, attach_point, is_main_role)
end

-- 灼烧
function FightText:ShowZhuoShao(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "Fire", "i" .. text, attach_point, is_main_role)
end

-- 灼烧 右
function FightText:ShowZhuoShaoRight(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "FireRight", "i" .. text, attach_point, is_main_role)
end

function FightText:OnFightSpecialFloat(receiver_pos, receive_type, float_value, is_main_role)
	-- if 0 ~= float_value then
		self:ShowText(floatingtext_bundle, "HutAbsorb", nil, receiver_pos, is_main_role)
	-- end
end

-- 多倍伤害
function FightText:ShowMoreCritical(text, attach_point, is_main_role, fight_type)
	if 0 == text then return end

	local param = 1
	if fight_type ~= nil then
		if fight_type == FIGHT_TYPE.FIGHT_TYPE_CRIT_2 then
			param = 2
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_CRIT_3 then
			param = 3
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_CRIT_4 then
			param = 4
		end
	end

	local sp_text = "x" .. param
	self:ShowText(floatingtext_bundle, "MoreCriticalLeft", text, attach_point, is_main_role, sp_text, "Root/Text", "Root/GameObject/SpecialText")
end

-- 多倍伤害 右
function FightText:ShowMoreCriticalRight(text,attach_point, is_main_role, fight_type)
	if 0 == text then return end

	local param = 1
	if fight_type ~= nil then
		if fight_type == FIGHT_TYPE.FIGHT_TYPE_CRIT_2 then
			param = 2
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_CRIT_3 then
			param = 3
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_CRIT_4 then
			param = 4
		end
	end

	local sp_text = "x" .. param
	self:ShowText(floatingtext_bundle, "MoreCriticalRight", text, attach_point, is_main_role, sp_text, "Text", "GameObject/SpecialText")
end

-- 斩杀
function FightText:Showkill(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "KillLeft", text, attach_point, is_main_role, nil, "Root/Text")
end

-- 斩杀 右
function FightText:ShowkillRight(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "KillRight", text, attach_point, is_main_role, nil, "Root/Text")
end

-- 齐天大圣击飞
function FightText:ShowKnock(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "KnockLeft", text, attach_point, is_main_role, nil, "Root/Text")
end

-- 齐天大圣击飞 右
function FightText:ShowKnockRight(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "KnockRight", text, attach_point, is_main_role)
end

-- 多倍回复
function FightText:ShowMoreRecover(text, attach_point, is_main_role, fight_type)
	if 0 == text then return end

	local param = 1
	if fight_type ~= nil then
		if fight_type == FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_2 then
			param = 2
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_3 then
			param = 3
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_4 then
			param = 4
		end
	end

	local sp_text = "x" .. param
	self:ShowText(floatingtext_bundle, "MoreRecoverlLeft", text, attach_point, is_main_role, sp_text, "Root/Text", "Root/GameObject/SpecialText")
end

-- 多倍回复 右
function FightText:ShowMoreRecoverRight(text,attach_point, is_main_role, fight_type)
	if 0 == text then return end

	local param = 1
	if fight_type ~= nil then
		if fight_type == FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_2 then
			param = 2
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_3 then
			param = 3
		elseif fight_type == FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_4 then
			param = 4
		end
	end

	local sp_text = "x" .. param
	self:ShowText(floatingtext_bundle, "MoreRecoverRight", text, attach_point, is_main_role, sp_text, "Text", "GameObject/SpecialText")
end

function FightText:OnFightSpecialFloat(receiver_pos, receive_type, float_value, is_main_role)
	-- if 0 ~= float_value then
		self:ShowText(floatingtext_bundle, "HutAbsorb", nil, receiver_pos, is_main_role)
	-- end
end

function FightText:OnFightSpecialFloat(receiver_pos, receive_type, float_value, is_main_role)
	-- if 0 ~= float_value then
		self:ShowText(floatingtext_bundle, "HutAbsorb", nil, receiver_pos, is_main_role)
	-- end
end

-- 仙镯伤害
function FightText:ShowEquipXianZhouHurt(text, pos, attach_point, text_type, is_main_role)
	if 0 == text then
		return
	end

	self:ShowText(floatingtext_bundle, "BeHurtXianZhouRight", "z" .. text, attach_point, is_main_role)
end

-- 仙戒回血
function FightText:ShowEquipXianJieHurt(text, attach_point, is_main_role)
	if 0 == text then
		return
	end

	text = "j" .. math.abs(text)
	self:ShowText(floatingtext_bundle, "PoisonRight", text, attach_point, is_main_role)
end

-- 四象技能
function FightText:ShowSiXiangSkillHurt(type, text, attach_point, is_main_role)
	if 0 == text then
		return
	end

	local res
	local icon_str = ""
	if type == FIGHT_TYPE.FIGHT_TYPE_SIXIANG_1 then
		res = "SiXiangHurt1Right"
		icon_str = "i"
	elseif type == FIGHT_TYPE.FIGHT_TYPE_SIXIANG_2 then
		res = "SiXiangHurt2Right"
		icon_str = "i"
	elseif type == FIGHT_TYPE.FIGHT_TYPE_SIXIANG_3 then
		res = "SiXiangHurt3Right"
		icon_str = "i"
	elseif type == FIGHT_TYPE.FIGHT_TYPE_SIXIANG_4 then
		res = "SiXiangHurt4Right"
		icon_str = "i"
	end

	text = icon_str .. text
	self:ShowText(floatingtext_bundle, res, text, attach_point, is_main_role)
end

-- 暗器技能伤害飘字
function FightText:ShowShenJiSkillHurt(type, text, attach_point, is_main_role)
	if 0 == text then
		return
	end

	local res
	local icon_str = ""
	if type == FIGHT_TYPE.FIGHT_TYPE_ANQI_WEAPON then
		res = "ShenJiHurt1Right"
		icon_str = "g"
	elseif type == FIGHT_TYPE.FIGHT_TYPE_ANQI_ARMOR then
		res = "ShenJiHurt2Right"
		icon_str = "g"
	end

	text = icon_str .. text
	self:ShowText(floatingtext_bundle, res, text, attach_point, is_main_role)
end

-- 免死
function FightText:ShowMianYiDead(attach_point, is_main_role)
	self:ShowText(floatingtext_bundle, "ImmuneDead", nil, attach_point, is_main_role)
end

-- 免死
function FightText:ShowMianYiDeadRight(attach_point, is_main_role)
	self:ShowText(floatingtext_bundle, "ImmuneDeadRight", nil, attach_point, is_main_role)
end

-- 恶魔猎手背刺 左
function FightText:ShowBlackstab(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "BlackstabLeft", text, attach_point, is_main_role, nil, "Root/Text")
end

-- 恶魔猎手背刺 右
function FightText:ShowBlackstabRight(text, attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "BlackstabRight", text, attach_point, is_main_role)
end

-- 恶魔猎手破隐 左
function FightText:ShowInvisible(text, attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "InvisibleLeft", text, attach_point, is_main_role, nil, "Root/Text")
end

-- 恶魔猎手破隐 右
function FightText:ShowInvisibleRight(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "InvisibleRight", text, attach_point, is_main_role)
end

-- 熊猫大侠反弹 左
function FightText:ShowDrunkRebund(text, attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "DrunkReboundLeft", text, attach_point, is_main_role, nil, "Root/Text")
end

-- 熊猫大侠反弹 右
function FightText:ShowDrunkRebundRight(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "DrunkReboundRight", text, attach_point, is_main_role)
end

-- 熊猫大侠燃烧 左
function FightText:ShowDrunkFire(text, attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "DrunkFrieLeft", text, attach_point, is_main_role, nil, "Root/Text")
end

-- 熊猫大侠燃烧 右
function FightText:ShowDrunkFireRight(text,attach_point, is_main_role)
	if 0 == text then return end
	self:ShowText(floatingtext_bundle, "DrunkFrieRight", text, attach_point, is_main_role)
end

-- 龙珠技能
function FightText:ShowLongZhuSkillHurt(text, attach_point, is_main_role)
	if 0 == text then return end
	text = "l" .. text
	self:ShowText(floatingtext_bundle, "LongZhuHurtRight", text, attach_point, is_main_role)
end

-- 器魂法宝技能
function FightText:ShowQiHunFaBaoSkillHurt(text, attach_point, is_main_role)
	if 0 == text then return end
	text = "f" .. text
	self:ShowText(floatingtext_bundle, "QiHunFaBaoHurtRight", text, attach_point, is_main_role)
end


-- 器魂神兵技能
function FightText:ShowQiHunShenBingSkillHurt(text, attach_point, is_main_role)
	if 0 == text then return end
	text = "j" .. text
	self:ShowText(floatingtext_bundle, "QiHunShenBingHurtRight", text, attach_point, is_main_role)
end

-- 器魂羽翼抵挡
function FightText:ShowQiHunWingDiDangHurt(text, attach_point, is_main_role)
	text = "yd"
	self:ShowText(floatingtext_bundle, "PoisonRight", text, attach_point, is_main_role)
end

-- 器魂剑灵抵挡
function FightText:ShowQiHunJianLingDiDangHurt(text, attach_point, is_main_role)
	text = "jd"
	self:ShowText(floatingtext_bundle, "FireRight", text, attach_point, is_main_role)
end

-- 器魂羽翼回血
function FightText:ShowQiHunWingHuiXue(text, attach_point, is_main_role)
	if 0 == text then return end
	text = "y+" .. text
	self:ShowText(floatingtext_bundle, "PoisonRight", text, attach_point, is_main_role)
end

-- 器魂剑灵回血
function FightText:ShowQiHunJianLingHuiXue(text, attach_point, is_main_role)
	if 0 == text then return end
	text = "q+" .. text
	self:ShowText(floatingtext_bundle, "PoisonRight", text, attach_point, is_main_role)
end

-- Boss真伤
function FightText:ShowBossZhenShang(value, attach_point, is_main_role)
	local text = "z" .. value
	self:ShowText(floatingtext_bundle, "BossZhenShang", text, attach_point, is_main_role)
end

-- Boss秒杀
function FightText:ShowBossSecKill(attach_point, is_main_role)
	self:ShowText(floatingtext_bundle, "BossSecKill", nil, attach_point, is_main_role, nil, nil, nil, 1.5)
end

-- 武魂真伤
function FightText:WuHunZhenShang(value, attach_point, is_main_role)
	local text = "w" .. value
	self:ShowText(floatingtext_bundle, "WuHunZhenShang", text, attach_point, is_main_role)
end

-- 驭兽伤害
function FightText:ShowBeastHurt(value, attach_point, is_main_role)
	local text = "y" .. value
	self:ShowText(floatingtext_bundle, "BeastHurt", text, attach_point, is_main_role)
end

-- 驭兽真龙伤害
function FightText:ShowBeastDragonHurt(value, attach_point, is_main_role)
	local text = "y" .. value
	self:ShowText(floatingtext_bundle, "BeastDragonHurt", text, attach_point, is_main_role, nil, "Text")
end

-- 双生神灵伤害
function FightText:ShowShuangShengHurt(value, attach_point, is_main_role)
	local text = "ds" .. value
	self:ShowText(floatingtext_bundle, "ShuangShengHurt", text, attach_point, is_main_role)
end

-- 永世套麒麟伤害
function FightText:ShowQiLingHurt(value, attach_point, is_main_role)
	local text = "dq" .. value
	self:ShowText(floatingtext_bundle, "QiLingHurt", text, attach_point, is_main_role)
end

-- 永世套创世伤害
function FightText:ShowLongShenLvHurt(value, attach_point, is_main_role)
	local text = "dl" .. value
	self:ShowText(floatingtext_bundle, "LongShenLvHurt", text, attach_point, is_main_role)
end

-- 龙神伤害
function FightText:ShowLongShenHuangHurt(value, attach_point, is_main_role)
	local text = "dl" .. value
	self:ShowText(floatingtext_bundle, "LongShenHuangHurt", text, attach_point, is_main_role)
end

-- 境界压制伤害
function FightText:ShowJingJieHurt(value, attach_point, is_main_role)
	local text = "dy" .. value
	self:ShowText(floatingtext_bundle, "JinJieHurt", text, attach_point, is_main_role)
end

-- 机甲伤害
function FightText:ShowJiJiaHurt(value, attach_point, is_main_role)
	local text = "dj" .. value
	self:ShowText(floatingtext_bundle, "JiJiaHurt", text, attach_point, is_main_role)
end

-- 天神伤害
function FightText:ShowTianShenHurt(value, attach_point, is_main_role)
	local text = "j" .. value
	self:ShowText(floatingtext_bundle, "BeHuiXinLeft", text, attach_point, is_main_role)
end

-- 五行天道伤害
function FightText:ShowWuXingActiveHurt(value, attach_point, is_main_role)
	local text = "z" .. value
	self:ShowText(floatingtext_bundle, "WuXingActiveHurt", text, attach_point, is_main_role)
end

-- 天神合击伤害
function FightText:ShowTianShenHeJiHurt(value, attach_point, is_main_role)
	local text = "y" .. value
	self:ShowText(floatingtext_bundle, "PoisonRight", text, attach_point, is_main_role)
end

-- 秘籍伤害
function FightText:ShowEsotericaHurt(value, attach_point, is_main_role)
	local text = "i" .. value
	self:ShowText(floatingtext_bundle, "WuXingActiveHurt", text, attach_point, is_main_role)
end

-- 无上之境伤害
function FightText:ShowWuShangZhiJingHurt(value, attach_point, is_main_role)
	local text = "q" .. value
	self:ShowText(floatingtext_bundle, "WuHunZhenShang", text, attach_point, is_main_role)
end

-- 五行沧溟伤害
function FightText:ShowWaistLightHurt(value, attach_point, is_main_role)
	local text = "w" .. value
	self:ShowText(floatingtext_bundle, "WaistLightHurt", text, attach_point, is_main_role)
end

FightText.cbdata_list = {}
function FightText.GetCBData()
    local cbdata = table.remove(FightText.cbdata_list)
    if nil == cbdata then
        cbdata = {true, true, true, true, true, true, true, true}
    end

    return cbdata
end

function FightText.ReleaseCBData(cbdata)
    table.insert(FightText.cbdata_list, cbdata)
end

-- 是否需要隐藏FightText
function FightText:IsNeedHideView()
	for k, v in pairs(need_hide_view) do
		if ViewManager.Instance:IsOpen(v) then
			return true
		end
	end
	return false
end
