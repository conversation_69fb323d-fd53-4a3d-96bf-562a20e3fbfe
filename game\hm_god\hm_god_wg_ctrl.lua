require("game/hm_god/hm_god_wg_data")
require("game/hm_god/hm_god_view")
require("game/hm_god/hm_god_item")
require("game/hm_god/hm_god_skill_view")

-- 【鸿蒙神藏】
HmGodWGCtrl = HmGodWGCtrl or BaseClass(BaseWGCtrl)

function HmGodWGCtrl:__init()
	if nil ~= HmGodWGCtrl.Instance then
		Error<PERSON><PERSON>("[HmGodWGCtrl] attempt to create singleton twice!")
		return
	end
	HmGodWGCtrl.Instance = self

    self.data = HmGodWGData.New()
	self.view = HmGodView.New(GuideModuleName.HmGodView)
	self.hm_god_skill_view = HmGodSkillView.New()
end

function HmGodWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

    self.hm_god_skill_view:DeleteMe()
    self.hm_god_skill_view = nil

    HmGodWGCtrl.Instance = nil
end

function HmGodWGCtrl:OnPartActiveResult(result, suit, part)
	if result == 1 then
		if self.view:IsOpen() then
			self.view:DoActiveEffect(suit, part)
		end
	end
end

function HmGodWGCtrl:OpenHmGodSkillView(suit_seq)
	self.hm_god_skill_view:SetDataAndOpen(suit_seq)
end