require("game/activity_fold_fun/activity_fold_fun_view")
require("game/activity_fold_fun/activity_fold_fun_wg_data")

ActicityFoldFunWGCtrl = ActicityFoldFunWGCtrl or BaseClass(BaseWGCtrl)

function ActicityFoldFunWGCtrl:__init()
	if ActicityFoldFunWGCtrl.Instance then
		ErrorLog("[ActicityFoldFunWGCtrl] attempt to create singleton twice!")
		return
	end

	ActicityFoldFunWGCtrl.Instance = self
	self.data = ActicityFoldFunWGData.New()
    self.view = ActicityFoldFunView.New(GuideModuleName.ActicityFoldFunView)

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	-- 角色屬性改變
	self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeComplete, self))
	self.listen_remind = {}
end

function ActicityFoldFunWGCtrl:__delete()
	ActicityFoldFunWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	for k,v in pairs(self.listen_remind) do
		RemindManager.Instance:UnBind(v)
	end
	self.listen_remind = {}
end

-- 活动信息改变
function ActicityFoldFunWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(activity_type)
	if act_cfg and act_cfg.parent_act_type and act_cfg.parent_act_type ~= "" and act_cfg.parent_act_type > 0 then
		self:CheckActTypeState(act_cfg.parent_act_type, activity_type)
		ViewManager.Instance:FlushView(GuideModuleName.ActicityFoldFunView)
		if ActRemindList[act_cfg.parent_act_type] then
			RemindManager.Instance:Fire(ActRemindList[act_cfg.parent_act_type])
		end
	end
end

--人物属性监听
function ActicityFoldFunWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
		local limit_list = ActicityFoldFunWGData.Instance:GetActTypeLimit()
		for k, v in pairs(limit_list) do
			self:ActivityChangeCallBack(v.act_type)
		end
	end
end

--跨天
function ActicityFoldFunWGCtrl:OnPassDay()
	local limit_list = ActicityFoldFunWGData.Instance:GetActTypeLimit()
	for k, v in pairs(limit_list) do
		self:ActivityChangeCallBack(v.act_type)
	end
end

function ActicityFoldFunWGCtrl:OnSceneChangeComplete(old_scene_type, new_scene_type)
	ViewManager.Instance:Close(GuideModuleName.ActicityFoldFunView)
end

function ActicityFoldFunWGCtrl:CheckActTypeState(parent_act_type, child_act_type)
	local fold_act_record_list = ActicityFoldFunWGData.Instance:GetFoldParentActRecord(parent_act_type)
	local pairs_list = {}
	if fold_act_record_list == nil or fold_act_record_list[child_act_type] == nil then
		local add_data = {}
		add_data.act_type = child_act_type
		table.insert(pairs_list, add_data)
	end
	
	if not IsEmptyTable(fold_act_record_list) then
		for k, v in pairs(fold_act_record_list) do
			table.insert(pairs_list, v)
		end
	end

	local act_state = ACTIVITY_STATUS.CLOSE
	for k, v in pairs(pairs_list) do
		while(true) do
			-- 判断这个活动是否需要通过这个方法来显示按钮
			if self:CheckNotShowIcon(v.act_type) then
				break
			end

			local child_act_info = ActivityWGData.Instance:GetActivityStatuByType(v.act_type)
			if child_act_info == nil then
				ActicityFoldFunWGData.Instance:RemoveActTypeLimit(child_act_type)
				ActicityFoldFunWGData.Instance:RemoveFoldParentActRecord(parent_act_type, child_act_type)
				self:SetUnBindRemind(child_act_type)
				break
			end

			--通过活动id获取配置
			local child_act_cfg
			child_act_cfg, child_act_type = self:GetActCfgByActType(v.act_type, child_act_info.open_type)
			if child_act_cfg == nil then
				break
			end

			if child_act_info.status == ACTIVITY_STATUS.CLOSE then
				ActicityFoldFunWGData.Instance:RemoveActTypeLimit(child_act_type)
				ActicityFoldFunWGData.Instance:RemoveFoldParentActRecord(parent_act_type, child_act_type)
				self:SetUnBindRemind(child_act_type)
			else
				--特殊处理，让按钮消失
				if self:CheckNotShowIconSpecial(child_act_type, child_act_cfg, child_act_info.status, child_act_info.end_time, child_act_info.open_type) then
					ActicityFoldFunWGData.Instance:RemoveFoldParentActRecord(parent_act_type, child_act_type)
					self:SetUnBindRemind(child_act_type)
					break
				end

				ActicityFoldFunWGData.Instance:RemoveActTypeLimit(child_act_type)
				ActicityFoldFunWGData.Instance:SetFoldParentActRecord(parent_act_type, child_act_type)
				self:SetBindRemind(child_act_type)
				act_state = ACTIVITY_STATUS.OPEN
			end

			break
		end
	end

	-- ActivityWGData.Instance:SetActivityStatus(parent_act_type, act_state)
	local new_act_record_list = ActicityFoldFunWGData.Instance:GetFoldParentActRecord(parent_act_type)
	if GetTableLen(new_act_record_list) == 1 then
		local act_type = next(new_act_record_list)
		ActivityWGData.Instance:SetActivityStatus(parent_act_type, ACTIVITY_STATUS.CLOSE)
		-- MainuiWGCtrl.Instance:ActivitySetButtonVisible(act_type, true)

		local active_data = ActivityWGData.Instance:GetActivityStatuByType(act_type)
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
		MainuiWGCtrl.Instance:AddTempActIcon(act_type, active_data.end_time, act_cfg)
	else
		ActivityWGData.Instance:SetActivityStatus(parent_act_type, act_state)
		for k_1, v_1 in pairs(new_act_record_list or {}) do
			MainuiWGCtrl.Instance:ActivitySetButtonVisible(k_1, false)
		end
	end
end

function ActicityFoldFunWGCtrl:CheckNotShowIcon(act_type)
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_BATTLE_SOUL  -- 战场之魂图标用置状态控制
		or act_type == ACTIVITY_TYPE.ShopDazzleReturn 		-- 炫装白送
		or act_type == ACTIVITY_TYPE.DayDayFanLi then
		return true
	elseif act_type == ACTIVITY_TYPE.INVESTPLAN then 		-- 投资计划
		return true
	elseif act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then	-- 周末Boss特殊处理
		return true
	elseif act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SPECIAL_GIFT then
		return true
	-- elseif act_type == ACTIVITY_TYPE.MEIRI_LEICHONG then 	-- 每日充值
	-- 	return not RechargeWGData.Instance:GetIsFirstRecharge()
	elseif act_type == ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN or act_type == ACTIVITY_TYPE.SHOW_TASK_CHAIN then -- 任务链
		OperationTaskChainWGCtrl.Instance:CheckActIsOpen()
		return true
	end

	-- 判断活动提前结束条件
	if not ActivityWGData.Instance:IsActShow(act_type) then
		return true
	end

	return false
end

-- 特殊处理，让按钮消失
function ActicityFoldFunWGCtrl:CheckNotShowIconSpecial(act_type, act_cfg, status, end_time, open_type)
	if act_cfg.is_show_in_mainview == 0 then
		return true
	end

	--是否需要等级上限/天数处理
	if MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) then
		ActicityFoldFunWGData.Instance:SetActTypeLimit(act_type)
		return true
	end

	-- 灵妖卖货
	if act_type == ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO then
		return ServerActivityWGData.Instance:GetLingYaoMaiHuoIsBuyAllGift()
	end

	-- 小鸭疾走不显示准备状态
	if act_type == ACTIVITY_TYPE.KF_DUCK_RACE and status == ACTIVITY_STATUS.STANDY then
		return true
	end

	return false
end


function ActicityFoldFunWGCtrl:GetActCfgByActType(act_type, open_type)
	local act_cfg = DailyWGData.Instance:GetActivityConfig(act_type)
	if CompetitionWGData.IsBipinType(act_type) then
		act_type = ACTIVITY_TYPE.BP_CAPABILITY_WING
		act_cfg = DailyWGData.Instance:GetActivityConfig(act_type) --比拼
	elseif nil == act_cfg or act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT or act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then
		if act_type > 2000 and act_type < 3000 or act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT then
			if ActivityWGData.Instance:GetActivityIsClose(act_type) then
				if self.rand_open_icon[open_type] and self.rand_open_icon[open_type][act_type] then
					self.rand_open_icon[open_type][act_type] = nil
				end
			else
				if self.rand_open_icon[open_type] then
					self.rand_open_icon[open_type][act_type]  = true
				else
					return nil, nil
				end
			end

			if open_type == RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_VERSION then
					act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.BANBEN_ACTIVITY) --版本活动
					act_type = ACTIVITY_TYPE.BANBEN_ACTIVITY
			else
				act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.RAND_ACT) --随机活动
				act_type = ACTIVITY_TYPE.RAND_ACT
			end
		else
			return nil, nil
		end
	end

	return act_cfg, act_type
end

function ActicityFoldFunWGCtrl:SetBindRemind(act_type)
	if ActRemindList[act_type] and self.listen_remind[act_type] == nil then
		self.listen_remind[act_type] = BindTool.Bind(self.RemindChangeCallBack, self, act_type)
		RemindManager.Instance:Bind(self.listen_remind[act_type], ActRemindList[act_type])
	end
end

function ActicityFoldFunWGCtrl:SetUnBindRemind(act_type)
	if ActRemindList[act_type] then
		RemindManager.Instance:UnBind(self.listen_remind[act_type])
		self.listen_remind[act_type] = nil
	end
end

--红点监听
function ActicityFoldFunWGCtrl:RemindChangeCallBack(act_type, remind_name, num)
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
	if act_cfg and act_cfg.parent_act_type and act_cfg.parent_act_type ~= "" and act_cfg.parent_act_type > 0 then
		ViewManager.Instance:FlushView(GuideModuleName.ActicityFoldFunView)
		if ActRemindList[act_cfg.parent_act_type] then
			RemindManager.Instance:Fire(ActRemindList[act_cfg.parent_act_type])
		end
	end
end