function HolyDarkWeaponView:InitHolyDarkEquipSkillView()
    self.up_skill_need_equip = ItemCell.New(self.node_list["up_skill_need_equip_pos"])

    XUI.AddClickEventListener(self.node_list["up_skill_need_equip_add"], BindTool.Bind(self.ClickUpNeedEquipAdd, self))
    XUI.AddClickEventListener(self.node_list["up_skill_btn"], BindTool.Bind(self.ClickSkillUpBtn, self))
end

function HolyDarkWeaponView:DeleteHolyDarkEquipSkillView()
    if self.up_skill_need_equip then
        self.up_skill_need_equip:DeleteMe()
        self.up_skill_need_equip = nil
    end
end

function HolyDarkWeaponView:FlushHolyDarkEquipSkillView()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(relic_seq)
        if relic_cfg and relic_cfg.skill_id then
            self.node_list.equip_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(relic_cfg.skill_id))
        end

        local skill_level, skill_exp = HolyDarkWeaponWGData.Instance:GetRelicSkillLevel(relic_seq)
        self.node_list.equip_skill_level.text.text = "LV." .. skill_level
        self.node_list.equip_skill_level_1.text.text = string.format(Language.HolyDarkWeapon.EquipJi, skill_level) 
        local cur_skill_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSkillLevelCfg(relic_seq, skill_level)
        local next_skill_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSkillLevelCfg(relic_seq, skill_level + 1)
        if relic_cfg.type == HolyDarkWeaponWGData.Weapon_type.HolyType then
            self.node_list.equip_skill_desc.text.text = string.format(Language.HolyDarkWeapon.EquipSkillDesc, cur_skill_level_cfg and cur_skill_level_cfg.equip_attr_per * 0.01 or 0) 
        else 
             self.node_list.equip_skill_desc.text.text = string.format(Language.HolyDarkWeapon.EquipSkillDesc1, cur_skill_level_cfg and cur_skill_level_cfg.equip_attr_per * 0.01 or 0)
        end
        local is_max = IsEmptyTable(next_skill_level_cfg)
        self.node_list.skill_level_max:SetActive(is_max)
        self.node_list.no_max_panel:SetActive(not is_max)
        self.node_list.equip_skill_level_up:SetActive(not is_max)

        if not is_max then
            local select_list, add_per, count = HolyDarkWeaponWGData.Instance:GetSkillSelectBagParam()
            self.node_list.equip_skill_slider.slider.value = skill_exp / cur_skill_level_cfg.need_exp
            self.node_list.equip_skill_add_slider.slider.value = (skill_exp + add_per) / cur_skill_level_cfg.need_exp
            self.node_list.equip_skill_exp_num.text.text = (skill_exp + add_per) .. "/" .. cur_skill_level_cfg.need_exp 
            local bundle, asset = ResPath.GetFairyLandEquipImages("a1_zzlz_tbdi")
            self.up_skill_need_equip:SetCellBg(bundle, asset)
            if select_list and select_list[1] then
                local info = select_list[1]
                if info then
                    self.up_skill_need_equip:SetData(info)
                    self.up_skill_need_equip:SetRightBottomTextVisible(true, true)
                    self.up_skill_need_equip:SetRightBottomColorText("x" .. count)
                end
            else
                self.up_skill_need_equip:ClearData()
            end

            local skill_equip_bag = HolyDarkWeaponWGData.Instance:GetUpSkillEquipBag(relic_seq)
            local is_max_decomse = select_list and (#select_list >= HolyDarkWeaponWGData.DecompsEquip) or false
            self.node_list.up_skill_add_remind:SetActive(#skill_equip_bag > 0 and (not is_max_decomse))
            self.node_list.up_skill_need_equip_add_img:SetActive(not is_max_decomse)
            self.node_list.equip_skill_level_up.text.text = ""

            local will_add_exp = skill_exp + add_per
            local cur_level_max_exp = cur_skill_level_cfg.need_exp
            local total_add_lev = 0
            if will_add_exp > cur_level_max_exp then
                while(true) do
                    if will_add_exp - cur_level_max_exp > 0 then
                        will_add_exp = will_add_exp - cur_level_max_exp
                        total_add_lev = total_add_lev + 1
                        local cfg = HolyDarkWeaponWGData.Instance:GetRelicSkillLevelCfg(relic_seq, skill_level + total_add_lev)
                        if not cfg then
                            break
                        end

                        cur_level_max_exp = cfg.need_exp
                    else
                        break
                    end
                end
            end

            if total_add_lev > 0 then
                self.node_list.equip_skill_level_up.text.text = string.format(Language.HolyDarkWeapon.SkillLevelUp, total_add_lev + skill_level)
            end
        end
    end
end



function HolyDarkWeaponView:ClickUpNeedEquipAdd()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        HolyDarkWeaponWGCtrl.Instance:OpenSkillPopEquipBagView(relic_seq)
    end
end

function HolyDarkWeaponView:ClickSkillUpBtn()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        local select_list, add_per, count = HolyDarkWeaponWGData.Instance:GetSkillSelectBagParam()
        local bag_list = {}
        if not IsEmptyTable(select_list) then
            for i, v in ipairs(select_list) do
                bag_list[i] = {}
                bag_list[i].item_id = v.item_id
                bag_list[i].index = v.index
                bag_list[i].num = v.num
            end
        end

        if IsEmptyTable(bag_list) then
            TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.NeedAddEquip)
            return
        end

        local send_fun = function ()
            HolyDarkWeaponWGCtrl.Instance:SendCSRelicDecompsEquip(relic_seq, #bag_list, bag_list)
        end

        send_fun()
    end
end

function HolyDarkWeaponView:PlaySkillUpEffect()
    if self.node_list["skill_succ_pos"] then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0),
                                parent_node = self.node_list["skill_succ_pos"]})
    end
end