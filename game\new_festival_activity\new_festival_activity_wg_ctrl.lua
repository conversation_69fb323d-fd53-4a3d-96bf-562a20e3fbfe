require("game/new_festival_activity/new_festival_activity_wg_view")
require("game/new_festival_activity/new_festival_activity_wg_data")
require("game/new_festival_activity/new_festival_collect_item/new_festival_collect_item_view")
require("game/new_festival_activity/new_festival_collect_card/new_festival_collect_card_view")

require("game/new_festival_activity/new_festival_prayer/new_festival_prayer_view")
require("game/new_festival_activity/new_festival_boss_drop/new_festival_boss_drop_view")
require("game/new_festival_activity/new_festival_recharge/new_festival_recharge_view")

NewFestivalActivityWGCtrl = NewFestivalActivityWGCtrl or BaseClass(BaseWGCtrl)
function NewFestivalActivityWGCtrl:__init()
    if NewFestivalActivityWGCtrl.Instance then
		ErrorLog("[NewFestivalActivityWGCtrl] attempt to create singleton twice!")
		return
	end

	NewFestivalActivityWGCtrl.Instance = self

    self.data = NewFestivalActivityWGData.New()
    self.view = NewFestivalActivityView.New(GuideModuleName.NewFestivalActivityView)

    self.item_change_callback = BindTool.Bind(self.OnNotifyDataChangeCallBack,self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)

    self.act_change_callback = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change_callback)
end

function NewFestivalActivityWGCtrl:__delete()
	NewFestivalActivityWGCtrl.Instance = nil
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.item_change_callback then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
        self.item_change_callback = nil
    end

    if self.act_change_callback then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change_callback)
        self.act_change_callback = nil
    end
end


function NewFestivalActivityWGCtrl:OnNotifyDataChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if NewFestivalCollectCardWGData.Instance:IsNFACollectCardItem(change_item_id) then
        -- 消耗道具领取奖励并没有协议下发，所以也需要监听道具的减少
        ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2350, "all", {"item_add"})
        RemindManager.Instance:Fire(RemindName.NewFestivalCollectCard)
    end
end

function NewFestivalActivityWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.NEW_JRHD_JFSC and status == ACTIVITY_STATUS.CLOSE then
        ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2334)
        RemindManager.Instance:Fire(RemindName.NewFestivalCollectItem)
    elseif activity_type == ACTIVITY_TYPE.NEW_JRHD_JFHD and status == ACTIVITY_STATUS.CLOSE then
        NewFestivalCollectCardWGData.Instance:ResetRequestList()
        ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2350)
        RemindManager.Instance:Fire(RemindName.NewFestivalCollectCard)
    end
end