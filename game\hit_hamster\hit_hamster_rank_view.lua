--排行榜
HitHamsterRankView = HitHamsterRankView or BaseClass(SafeBaseView)

function HitHamsterRankView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 578)})
	self:AddViewResource(0, "uis/view/hit_hamster_prefab", "hit_hamster_reward_rank")
end

function HitHamsterRankView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.rank

    if not self.hit_hamster_reward_list then
	    self.hit_hamster_reward_list = AsyncListView.New(HamsterRewardRankItem, self.node_list.hit_hamster_reward_list)
    end

	if not self.my_hit_hamster_reward then
        self.my_hit_hamster_reward = HamsterRewardRankItem.New(self.node_list.my_hit_hamster_reward)
    end
end

function HitHamsterRankView:ReleaseCallBack()
    if self.hit_hamster_reward_list then
        self.hit_hamster_reward_list:DeleteMe()
        self.hit_hamster_reward_list = nil
    end

	if self.my_hit_hamster_reward then
        self.my_hit_hamster_reward:DeleteMe()
        self.my_hit_hamster_reward = nil
    end
end


function HitHamsterRankView:OnFlush(param_t)
	self:FlushRankList()
end

function HitHamsterRankView:FlushRankList()
    local rank_list = HitHamsterWGData.Instance:GetWhackMoleRankList()
    if rank_list and self.hit_hamster_reward_list then
        self.hit_hamster_reward_list:SetDataList(rank_list)
    end

    local my_rank_data = HitHamsterWGData.Instance:GetMyWhackMoleRank()
    self.my_hit_hamster_reward:SetData(my_rank_data)
end

-------------------------------------PWSceneHurtItemCellRender---------------------------------
HamsterRewardRankItem = HamsterRewardRankItem or BaseClass(BaseRender)

function HamsterRewardRankItem:LoadCallBack()
    if not self.item_list and self.node_list.reward_item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
		self.item_list:SetStartZeroIndex(true)
    end
end

function HamsterRewardRankItem:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function HamsterRewardRankItem:OnFlush()
	local rank = self.data.rank or self.index
    local bundle, asset
    local is_top_three = rank <= 3
	self.node_list.img_rank:CustomSetActive(is_top_three)

    if is_top_three then
        bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. rank)
        self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank))
    else
        bundle, asset = ResPath.GetCommonImages("a3_ty_list_4")
    end
    self.node_list.img_bg.image:LoadSprite(bundle, asset)

    if rank >= 999 then
        self.node_list.rank_text.text.text = Language.FuBenPanel.ManHuangNotRankStr
    else
        self.node_list.rank_text.text.text = rank
    end

    if IsEmptyTable(self.data) then
        self.node_list.rank_name.text.text = Language.Common.XuWeiYiDai
        self.node_list.rank_score.text.text = tostring(0)
    else
        self.node_list.rank_name.text.text = self.data.name
        self.node_list.rank_score.text.text = self.data.score
    end

	local rank_data = HitHamsterWGData.Instance:GetRankRewardByRank(rank)
	if self.node_list.reward_item_list and rank_data and rank_data.reward_item then
        self.item_list:SetDataList(rank_data.reward_item)
    end
end