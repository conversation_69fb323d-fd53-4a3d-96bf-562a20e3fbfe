require("game/holy_beast_call/holy_beast_call_wg_data")
require("game/holy_beast_call/holy_beast_call_view")
require("game/holy_beast_call/holy_beast_call_probability")

HolyBeastCallWGCtrl = HolyBeastCallWGCtrl or BaseClass(BaseWGCtrl)

function HolyBeastCallWGCtrl:__init()
	if HolyBeastCallWGCtrl.Instance then
		print_error("[HolyBeastCallWGCtrl]:Attempt to create singleton twice!")
	end

	HolyBeastCallWGCtrl.Instance = self
    self.data = HolyBeastCallWGData.New()
    self.view = HolyBeastCallView.New(GuideModuleName.HolyBeastCallView)
	self.probability_view = HolyBeastCallProbabilityView.New()
	self.last_draw_type = -1

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function HolyBeastCallWGCtrl:__delete()
    self:UnRegisterAllEvents()

    if self.data then
        self.data:DeleteMe()
	    self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
	    self.view = nil
    end

	if self.probability_view then
        self.probability_view:DeleteMe()
	    self.probability_view = nil
    end

	self.last_draw_type = nil
    HolyBeastCallWGCtrl.Instance = nil
end

function HolyBeastCallWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAShengShouDrawInfo, "OnSCOAShengShouDrawInfo")
	self:RegisterProtocol(SCOAShengShouDrawResult, "OnSCOAShengShouDrawResult")
end

function HolyBeastCallWGCtrl:RegisterAllEvents()
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function HolyBeastCallWGCtrl:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function HolyBeastCallWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        if self.data:IsHolyBeastCallItem(change_item_id) then
			RemindManager.Instance:Fire(RemindName.HolyBeastCall)

			if self.view and self.view:IsOpen() then
				self.view:Flush()
			end
		end
	end
end

function HolyBeastCallWGCtrl:SendHolyBeastCallReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HOLY_BEAST_CALL
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function HolyBeastCallWGCtrl:OpenProbabilityView()
	if self.probability_view and not self.probability_view:IsOpen() then
		self.probability_view:Open()
	end
end

function HolyBeastCallWGCtrl:OnSCOAShengShouDrawInfo(protocol)
	self.data:SetShengShouDrawInfo(protocol)
	RemindManager.Instance:Fire(RemindName.HolyBeastCall)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end

function HolyBeastCallWGCtrl:OnSCOAShengShouDrawResult(protocol)
	if protocol.count > 0 and not IsEmptyTable(protocol.result_item_list) then
		local item_cost_cfg = HolyBeastCallWGData.Instance:GetItemCostCfg()
		local cur_data = item_cost_cfg[self.last_draw_type] or {}
	
		if not IsEmptyTable(cur_data) then
			local cost_item_id = cur_data.cost_item_id
			local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
			local draw_time = cur_data.draw_times
			local cost_item_num = cur_data.cost_item_num
	
			local ok_func = function ()
				local function draw_func()
					HolyBeastCallWGCtrl.Instance:SendHolyBeastCallReq(OA_SHENGSHOU_DRAW_OPERATE_TYPE.DRAW, draw_time)
				end
		
				if cost_item_num <= item_num then
					draw_func()
				else
					local once_cost_gold = cur_data.cost_gold_num / draw_time
					local once_cost_item = cost_item_num / draw_time
					local need_num = cost_item_num - item_num
					local need_gole = (need_num / once_cost_item) * once_cost_gold
					local item_name = item_cfg and item_cfg.name or ""
					local utem_color = ITEM_COLOR[item_cfg and item_cfg.color or 0] or GameEnum.ITEM_COLOR_WHITE
					local desc = string.format(Language.HolyBeastCall.DrawAlertDesc, utem_color, item_name,  COLOR3B.GREEN, need_gole, COLOR3B.GREEN, need_num, COLOR3B.GREEN, draw_time)
					TipWGCtrl.Instance:OpenCheckTodayAlertTips(desc, function ()
						draw_func()
					end, "HolyBeastCallDraw", Language.Chat.DontTipToday)
				end
			end
	
			local draw_str = string.format(Language.HolyBeastCall.DrawTimeDesc, cur_data.draw_times)
			TipWGCtrl.Instance:ShowGetDrawResult(protocol.result_item_list, cost_item_id, cost_item_num, draw_str, function ()
				ok_func()
			end)
		else
			if protocol.count > 0 and not IsEmptyTable(protocol.result_item_list) then
				TipWGCtrl.Instance:ShowGetReward(nil, protocol.result_item_list)
			end
		end
	end
end

function HolyBeastCallWGCtrl:SetLastDrawType(draw_type)
	self.last_draw_type = draw_type
end