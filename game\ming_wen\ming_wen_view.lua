require("game/ming_wen/ming_wen_xiang_qian")
require("game/ming_wen/ming_wen_he_cheng")
require("game/ming_wen/ming_wen_fen_jie")
require("game/ming_wen/ming_wen_dui_huan")

MingWenView = MingWenView or BaseClass(SafeBaseView)

function MingWenView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:LoadConfig()
	self.default_index = TabIndex.ming_wen_xiang_qian
	self:SetMaskBg(false, true)
	self.remind_tab = {
		{RemindName.MingWen_XiangQian},
		{RemindName.MingWen_FenJie},
		{RemindName.MingWen_DuiHuan},
		{RemindName.MingWen_HeCheng_In_View},
	}
end

function MingWenView:__delete()

	self:DeleteFenJie()
	self:DeleteDuiHuan()
	self:DeleteHeCheng()
	self:DeleteXiangQian()
end

function MingWenView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:ReleaseFenJie()
	self:ReleaseDuiHuan()
	self:ReleaseHeCheng()
	self:ReleaseXiangQian()
end

-- 加载配置
function MingWenView:LoadConfig()
	local bundle_name = "uis/view/ming_wen_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.ming_wen_xiang_qian,bundle_name,"layout_mingwen_xiangqian")
	self:AddViewResource(TabIndex.ming_wen_fen_jie,bundle_name,"layout_mingwen_fenjie")
	self:AddViewResource(TabIndex.ming_wen_dui_huan,bundle_name,"layout_mingwen_duihuan")
	self:AddViewResource(TabIndex.ming_wen_he_cheng,bundle_name,"layout_mingwen_hecheng")
	self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
	self:AddViewResource(TabIndex.ming_wen_xiang_qian,bundle_name,"layout_mingwen_of_entrance")
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")
end

function MingWenView:LoadCallBack(index, loaded_times)
	if not self.tabbar then
  		self.tabbar = Tabbar.New(self.node_list)
  		self.tabbar:Init(Language.MingWenView.TabGrop, nil,nil,nil,self.remind_tab)
  		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.first_open = true
  	end

  	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_mingwen_exp = true,
			show_mingwen_minyin = true, show_mingwen_xinjing = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
	--批量注册标签功能
  	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.MingWenView, self.tabbar)	
	self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.MingWenView]
end

function MingWenView:LoadIndexCallBack(index)
	if index == TabIndex.ming_wen_xiang_qian then
		self:InitXiangQian()
	elseif index == TabIndex.ming_wen_fen_jie then
		self:InitFenJie()
	elseif index == TabIndex.ming_wen_dui_huan then
		self:InitDuiHuan()
	elseif index == TabIndex.ming_wen_he_cheng then
		self:InitHeCheng()
	end
end

function MingWenView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg4")

	if index == TabIndex.ming_wen_xiang_qian then
		bundle, asset = ResPath.GetRawImagesPNG("a3_wgf_bj")
		--self:InitXiangQian()
		--self:FlushXiangQian()
	elseif index == TabIndex.ming_wen_fen_jie then
		--self:InitFenJie()
		self:FlushFenJie("all")
	elseif index == TabIndex.ming_wen_dui_huan then
		--self:InitDuiHuan()
	elseif index == TabIndex.ming_wen_he_cheng then
		--self:InitHeCheng()
		--self:FlushHeCheng()
	end
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function MingWenView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.ming_wen_xiang_qian then
				self:FlushXiangQian()
			elseif index == TabIndex.ming_wen_fen_jie then
				self:FlushFenJie()
			elseif index == TabIndex.ming_wen_dui_huan then
				self:FlushDuiHuan()
			elseif index == TabIndex.ming_wen_he_cheng then
				self:FlushHeCheng()
			end
		elseif k == "ming_wen_fen_jie" then
			self:FlushFenJie()
		elseif k == "ming_wen_xiang_qian" then
			self:FlushXiangQian()
		elseif k == "ming_wen_dui_huan" then
			self:FlushDuiHuan()
		elseif k == "ming_wen_he_cheng" then
			self:FlushHeCheng()
		elseif k == "compose_success" then
			self:HeChengSuccess()
		elseif k == "upgrade_success" then
			self:UpGradeSuccess()
		end
	end
end

function MingWenView:FLushCurIndex()
	if not self:IsLoadedIndex(self.show_index) then
		return
	end

	if self.show_index == TabIndex.ming_wen_xiang_qian then
		self:FlushXiangQian()
	elseif self.show_index == TabIndex.ming_wen_fen_jie then
		self:FlushFenJie("all")
	elseif self.show_index == TabIndex.ming_wen_dui_huan then
		self:FlushDuiHuan()
	elseif self.show_index == TabIndex.ming_wen_he_cheng then
		self:FlushHeCheng()
	end
end