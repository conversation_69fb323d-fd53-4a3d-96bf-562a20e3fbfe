FeixianBossSceneLogic = FeixianBossSceneLogic or BaseClass(CommonFbLogic)

function FeixianBossSceneLogic:__init()
	self.open_view = false
end

function FeixianBossSceneLogic:__delete()

end

function FeixianBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenWGCtrl.Instance:OpenTaskFollow()
	-- XuiBaseView.CloseAllView()

end

function FeixianBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	-- FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function FeixianBossSceneLogic:Out()
	CommonFbLogic.Out(self)
	-- UiInstanceMgr.Instance:CloseRewardAction()
	-- UiInstanceMgr.Instance:OpenLeaveSceneCountDown(20, true)
	-- UiInstanceMgr.Instance:CloseSceneCountDown()

	FuBenWGCtrl.Instance:CloseTaskFollow()
end
