HongHuangGoodCoremonyWGData = HongHuangGoodCoremonyWGData or BaseClass()
function HongHuangGoodCoremonyWGData:__init()
    if nil ~= HongHuangGoodCoremonyWGData.Instance then
        ErrorLog("[HongHuangGoodCoremonyWGData]:Attempt to create singleton twice!")
    end
    HongHuangGoodCoremonyWGData.Instance = self

	self.chaotic_gift_config = ConfigManager.Instance:GetAutoConfig("chaotic_gift_auto")
    self.gifts_cfg = ListToMapList(self.chaotic_gift_config.gifts, "grade")
    self.gifts_seq_cfg = ListToMap(self.chaotic_gift_config.gifts, "seq")

    self.gift_buy_flag = {}
    self.current_show_grade = 1
    RemindManager.Instance:Register(RemindName.HongHuangGoodCoremony, BindTool.Bind(self.GetHongHuangGoodCoremonyRemind, self))
end

function HongHuangGoodCoremonyWGData:__delete()
    HongHuangGoodCoremonyWGData.Instance = nil
	self.chaotic_gift_config= nil
    self.gift_buy_flag = nil
    self.current_show_grade = nil
    RemindManager.Instance:UnRegister(RemindName.HongHuangGoodCoremony)
end

function HongHuangGoodCoremonyWGData:GetHongHuangGoodCoremonyRemind()
    return 0
end

function HongHuangGoodCoremonyWGData:SetChaoticGifInfo(protocol)
	local bit_list = {}
	local index = 0
 	for i,v in ipairs(protocol.gift_buy_flag) do
 		bit_list = bit:d2b_l2h(v, bit_list)
 		for j = 1, 8 do
 			self.gift_buy_flag[index] = bit_list[j]
 			index = index + 1
 		end
 	end
end

function HongHuangGoodCoremonyWGData:GetChaoticGifInfo(seq)
    return self.gift_buy_flag[seq] == 1
end

function HongHuangGoodCoremonyWGData:GetCurrentShowSeq()
    local grade_num = #self.gifts_cfg
    for i = self.current_show_grade, grade_num do
        for k, v in pairs(self.gifts_cfg[i]) do
            if not self:GetChaoticGifInfo(v.seq) then
                self.current_show_grade = v.grade
                return self.current_show_grade
            end
        end
    end
    
    return self.gifts_cfg[grade_num][1].grade
end

function HongHuangGoodCoremonyWGData:GetChaoticGifDataList(current_grade)
    local current_grade = current_grade or self:GetCurrentShowSeq()
    local all_grade = #self.gifts_cfg
    if current_grade > all_grade then
        current_grade = all_grade
    end
    return self.gifts_cfg[current_grade]
end

function HongHuangGoodCoremonyWGData:GetIsAllBuy()
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay() -- 新增需求（增加天数判断）
    local all_buy = true
    for k, v in pairs(self.gifts_seq_cfg) do
        if self.gift_buy_flag[v.seq] == 0 and server_day >= v.day then
            all_buy = false
            break
        end 
    end

    return all_buy
end