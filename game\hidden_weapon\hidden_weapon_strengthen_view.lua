-- 暗器软甲强化
HiddenWeaponStrengthenView = HiddenWeaponStrengthenView or BaseClass()

local ETabType = {
    Soul = 1,
    Inscription = 2,
    Awaken = 3
}

function HiddenWeaponStrengthenView:__init(parent_view)
    self.parent_view = parent_view
    self.node_list = self.parent_view.node_list
    self.zl_once_lv_add_exp_sum = 0
    -- self.qh_skill_node = {}
end

function HiddenWeaponStrengthenView:__delete()
    CancleAllDelayCall(self)
    self.is_init_loaded = nil
    self.list_toggle_filter = nil
    self.parent_view = nil
    self.node_list = nil
    self:CancelAllQuest()

    self.is_auto_zhuling = false
    self.type_tab_arr = nil
    self:SoulDestory()
    self:InscriptionDestory()
    self:AwakenDestory()
    if self.wupin_item then
        self.wupin_item:DeleteMe()
        self.wupin_item = nil
    end

    if self.qianghua_model_display then
        self.qianghua_model_display:DeleteMe()
        self.qianghua_model_display = nil
    end
end

function HiddenWeaponStrengthenView:CancelAllQuest()
    if self.quest_request_upgrade then
        GlobalTimerQuest:CancelQuest(self.quest_request_upgrade)
        self.quest_request_upgrade = nil
    end

    if self.zl_once_lv_add_exp_sum > 0 then
        self.is_request_upgreade = true
        HiddenWeaponRequest:ReqUpgrade(self.parent_view:GetWeaponType(), self.zl_once_lv_add_exp_sum)
    end
    self.zl_once_lv_add_exp_sum = 0
end

function HiddenWeaponStrengthenView:OnClickDown()
    self:CancelAllQuest()
    if self:IsAutoUpZhuLing() then
        self:SetAutoUpZhuLingButtonEnabled(false)
        return
    end
    local data = self:GetCurData()
    local weapon_type = self.parent_view:GetWeaponType()

    if not data.equip then
        return
    end

    local pro_data = data.protocol_equip_item
    self.zl_has_exp = pro_data.exp_value
    if self.zl_has_exp == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiEquip.NO_ENOUGH_EXP_TIS[weapon_type])
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = HW_CONST_PARAM.EXP_ICON_IDS[weapon_type]})
        return
    end
    self:SetAutoUpZhuLingButtonEnabled(true)
    self.zl_progress_lv = pro_data.level

    local node_type = data.equip.node_type
    local next_lv_add_exp = pro_data.next_lv_add_exp
    local cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, pro_data.level + 1)
    self.zl_next_lv_last_exp = cfg.need_exp - next_lv_add_exp
    self.zl_cur_add_exp_sum = 0
    self.zl_once_lv_add_exp_sum = 0

    self.up_speed_time = Status.NowTime + HW_CONST_PARAM.AddSpeedTime
    self.start_speed = HW_CONST_PARAM.ZLProgressMinV
    self.quest_request_upgrade = GlobalTimerQuest:InvokeRepeating(BindTool.Bind1(self.UpdateMidProgress2, self), 0, 0.1, 99999999)
end

function HiddenWeaponStrengthenView:RefreshMidProgress()
    self.is_request_upgreade = false
    local data = self:GetCurData()
    if self.quest_request_upgrade then
        if data.equip then
            local cur_lv = data.protocol_equip_item.level
            local pro = cur_lv % 10
        end
        return
    end

    local weapon_type = self.parent_view:GetWeaponType()
    if HiddenWeaponWGData.Instance:IsCanUpGrade(weapon_type) then
        local canup_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, data.equip.node_type, data.protocol_equip_item.level) or {}
        self.node_list.zhuling_pro.slider.value = 1
        self.node_list.zl_exp_txt.text.text = canup_cfg.need_exp .. "/" .. canup_cfg.need_exp
        return
    end

    if data.equip then
        local node_type = data.equip.node_type
        local protocol_equip_item = data.protocol_equip_item
        local cur_lv = protocol_equip_item.level
        local next_lv_add_exp = protocol_equip_item.next_lv_add_exp
        local cur_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, cur_lv) or {}
        local next_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, cur_lv + 1) or {}
        local exp_pro = 0
        if not next_cfg then
            self.node_list.zhuling_pro.slider.value = 1
            self.node_list.zl_exp_txt.text.text = next_lv_add_exp .. "/" .. cur_cfg.need_exp
        else
             self.node_list.zhuling_pro.slider.value = next_lv_add_exp / next_cfg.need_exp
             self.node_list.zl_exp_txt.text.text = next_lv_add_exp .. "/" .. next_cfg.need_exp
        end
        local eff_num = cur_lv % 10
        local max_lv = HiddenWeaponWGData.Instance:GetZLAttrMaxLv(weapon_type, node_type)
        if cur_lv >= max_lv then
            eff_num = 10
        end
    else
        self.node_list.zhuling_pro.slider.value = 0
        self.node_list.zl_exp_txt.text.text = 0
    end
end


function HiddenWeaponStrengthenView:CalProgressValSum(weapon_type, node_type, cur_lv, next_lv_add_exp)
    local cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, cur_lv + 1) or {}
    if not cfg then
        return 360
    end
    local need_exp = cfg.need_exp or 0
    local pro = cur_lv % 10

    local pro_val_sum = 0

    --先计算当前这一圈已经升过的级的进度
    if pro == 1 then
        pro_val_sum = 33
    elseif pro > 1 then
        pro_val_sum = pro_val_sum + 33
        for i = 2, pro do
            pro_val_sum = pro_val_sum + 36
        end
    end

    --计算下一级的进度
    if pro == 0 then
        pro_val_sum = pro_val_sum + ((next_lv_add_exp or 0) / need_exp) * 33
    else
        pro_val_sum = pro_val_sum + ((next_lv_add_exp or 0) / need_exp) * 36
    end

    return pro_val_sum
end

function HiddenWeaponStrengthenView:AddProgressData(val)
    self.zl_next_lv_last_exp = self.zl_next_lv_last_exp - val
    self.zl_once_lv_add_exp_sum = self.zl_once_lv_add_exp_sum + val
    self.zl_cur_add_exp_sum = self.zl_cur_add_exp_sum + val
end

function HiddenWeaponStrengthenView:UpdateMidProgress2()
    local data = self:GetCurData()
    local equip = data.equip
    if not equip then
        self:CancelAllQuest()
        return
    end

    if Scene.Instance:IsChangeSceneIng() then
        self:CancelAllQuest()
        return
    end


    local node_type = equip.node_type
    local weapon_type = self.parent_view:GetWeaponType()
    local cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, self.zl_progress_lv + 1)
    if cfg == nil then
        self:CancelAllQuest()
        return
    end
    
    if Status.NowTime >= self.up_speed_time and self.start_speed < HW_CONST_PARAM.ZLProgressMaxV then
        self.up_speed_time = Status.NowTime + HW_CONST_PARAM.AddSpeedTime 
        local new_speed = self.start_speed + HW_CONST_PARAM.ZLProgressAddV
        if new_speed < HW_CONST_PARAM.ZLProgressMaxV then
            self.start_speed = new_speed
        else
            self.start_speed = HW_CONST_PARAM.ZLProgressMaxV
        end
    end

    local add_set = self.start_speed

    local last_exp = self.zl_has_exp - self.zl_once_lv_add_exp_sum
    local is_no_exp = false
    if last_exp < add_set then
        add_set = last_exp
        is_no_exp = true
    end

    local add_val = add_set
    local is_up = false
    local up_level = 0
    if add_val >= self.zl_next_lv_last_exp then
        up_level = 1

        -- print_log("==000===", add_set, self.zl_next_lv_last_exp)
        last_exp = last_exp - self.zl_next_lv_last_exp
        self:AddProgressData(self.zl_next_lv_last_exp)

        local tmp_add_last = add_set - self.zl_next_lv_last_exp
        local cur_lv = self.zl_progress_lv + up_level
        local next_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, cur_lv + 1)
        if not next_cfg then --满级了
            self:CancelAllQuest()
            return
        end

        self.zl_next_lv_last_exp = next_cfg.need_exp 
        -- print_log("==aaa===", tmp_add_last, self.zl_next_lv_last_exp)
        while tmp_add_last >= self.zl_next_lv_last_exp do
            tmp_add_last = tmp_add_last - self.zl_next_lv_last_exp
            last_exp = last_exp - self.zl_next_lv_last_exp
            self:AddProgressData(self.zl_next_lv_last_exp)

            up_level = up_level + 1
            cur_lv = cur_lv + 1
            next_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, cur_lv + 1)

            if not next_cfg then --满级了
                self:CancelAllQuest()
                return
            end

            self.zl_next_lv_last_exp = next_cfg.need_exp 
            if self.zl_next_lv_last_exp == 0 then -- 避免死循环
                print_error("zl_next_lv_last_exp 为0")
                return
            end
            -- print_log("==bbb===", tmp_add_last, self.zl_next_lv_last_exp)
        end

        add_val = tmp_add_last
        --升级
        is_up = true
    end
    -- print_log("=====11====", self.zl_next_lv_last_exp, add_val)
    self:AddProgressData(add_val)
    last_exp = last_exp - add_val


    if is_no_exp then --经验不足
        self:CancelAllQuest()
        self:SetAutoUpZhuLingButtonEnabled(false)
        return
    end
    if is_up then
        -- print_error("升级", up_level)
        self.zl_progress_lv = self.zl_progress_lv + up_level
        self.is_request_upgreade = true

        HiddenWeaponRequest:ReqUpgrade(weapon_type, self.zl_once_lv_add_exp_sum)

        self.zl_has_exp = self.zl_has_exp - self.zl_once_lv_add_exp_sum
        self.zl_once_lv_add_exp_sum = 0

        local max_lv = HiddenWeaponWGData.Instance:GetZLAttrMaxLv(weapon_type, node_type)
        if self.zl_progress_lv >= max_lv then
            self:CancelQuest()
            return
        end
    end
    local cur_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, self.zl_progress_lv) or {}
    local next_cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, self.zl_progress_lv + 1) or {}
    local exp_pro = 0
    local last_need = next_cfg.need_exp - self.zl_next_lv_last_exp
    if not next_cfg then
        self.node_list.zhuling_pro.slider.value = 1
        self.node_list.zl_exp_txt.text.text = last_need .. "/" .. cur_cfg.need_exp
    else
         self.node_list.zhuling_pro.slider.value = last_need / next_cfg.need_exp
         self.node_list.zl_exp_txt.text.text = last_need .. "/" .. next_cfg.need_exp
    end
    if last_exp < 0 then --避免显示错误 
        last_exp = 0
    end

    self.wupin_item:SetRightBottomText(last_exp)
    -- print_log(add_val, self.zl_next_lv_last_exp, self.zl_progress_lv, self.cur_progress)
end

function HiddenWeaponStrengthenView:LoadTab()
    local tab_node = self.node_list["type_btn_title"]
    self.type_tab_arr = {}

    for i = 1, 3 do
        local nor_ui = tab_node:FindObj(string.format("detail_toggle_%s/btn_nor", i))
        local sel_ui = tab_node:FindObj(string.format("detail_toggle_%s/btn_selected", i))
        self.type_tab_arr[i] = {
            nor_ui = nor_ui,
            sel_ui = sel_ui
        }

        nor_ui:SetActive(true)
        XUI.AddClickEventListener(nor_ui, BindTool.Bind2(self.OnClickTab, self, i))
        XUI.AddClickEventListener(sel_ui, BindTool.Bind2(self.OnClickTab, self, i))
    end
end

function HiddenWeaponStrengthenView:LoadIndexCallBack()
    if self.is_init_loaded == true then
        return
    end
    self.is_init_loaded = true
    if not self.wupin_item then
        self.wupin_item = ItemCell.New(self.node_list["wupin_item"])
    end
    self:LoadTab()
    self:SoulLoadUI()
    self:InscriptionLoadUI()
    self:AwakenLoadUI()
    self:LoadModel()
end

function HiddenWeaponStrengthenView:ShowIndexCallBack()
    if not self.is_init_loaded then
        return
    end

    self.qh_model_id = -1
    self:UpdateMidInfo()
    self:RefreshMidProgress()

    --默认选中第一个标签
    --self:OnClickTab(self.cur_select_tab or ETabType.Soul)
    self:RefreshRemind()
    self:JumpToRed()
end

function HiddenWeaponStrengthenView:JumpToRed()
    -- 注灵红点/进阶红点
    local weapon_type = self.parent_view:GetWeaponType()
    if weapon_type == 1 then
        if
            RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL1) > 0 or
                RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_JJ1) > 0
         then
            self:OnClickTab(ETabType.Soul)
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_KM1) > 0 then
            self:OnClickTab(ETabType.Inscription)
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_AWAKE1) > 0 then
            self:OnClickTab(ETabType.Awaken)
        else
            self:OnClickTab(ETabType.Soul)
        end
    elseif weapon_type == 2 then
        if
            RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL2) > 0 or
                RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_JJ2) > 0
         then
            self:OnClickTab(ETabType.Soul)
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_KM2) > 0 then
            self:OnClickTab(ETabType.Inscription)
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_AWAKE2) > 0 then
            self:OnClickTab(ETabType.Awaken)
        else
            self:OnClickTab(ETabType.Soul)
        end
    else
        self:OnClickTab(ETabType.Soul)
    end
end

function HiddenWeaponStrengthenView:GetCurData()
    if self.parent_view == nil then
        self:CancelAllQuest()
        return nil
    end
    local weapon_type = self.parent_view:GetWeaponType()
    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)
    return data
end

function HiddenWeaponStrengthenView:UpdateMidInfo()
    if self.node_list["mid_node"] and not self.node_list["mid_node"].gameObject.activeSelf then
        self.node_list["mid_node"]:SetActive(true)
    end

    local weapon_type = self.parent_view:GetWeaponType()
    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)

    local equip = data.equip
    if equip then
        self.node_list["qh_txt_capability"].text.text = data.protocol_equip_item.capability
        self.node_list["txt_equip_name"].text.text = equip.name
        self.node_list["zl_lv_txt"].text.text = "LV." .. data.protocol_equip_item.level
        -- 策划要求暂时屏蔽
        -- self:UpdateQhSkillUI(data)
        self:UpdateQhAwakenSkill(data)
        self.node_list.qh_special_skill_wu:SetActive(false)
        self.node_list.qh_star_area:SetActive(true)
        self.node_list.qh_skill_content:SetActive(true)

        local model_bundle, model_asset = ResPath.GetArtifactModel(equip.origin_data.base_model)
        if self.qh_model_id ~= equip.origin_data.base_model then
            self.qh_model_id = equip.origin_data.base_model
            self.qianghua_model_display:SetMainAsset(model_bundle, model_asset)
            self.node_list.qianghua_model_display:CustomSetActive(true)
        end
    else
        self.node_list["zl_lv_txt"].text.text = "LV." .. 0
        self.node_list["empty_img"]:SetActive(true)
        self.node_list["qh_txt_capability"].text.text = 0
        self.node_list["km_node"]:SetActive(false)
        self.node_list["zl_node"]:SetActive(false)
        self.node_list["txt_equip_name"].text.text = Language.ShenJiEquip.NO_EQUIP_TIPS[weapon_type]
        self.node_list["no_tips_qh"].text.text = Language.ShenJiEquip.NO_EQUIP_TIPS[weapon_type]

        -- for k, v in pairs(self.qh_skill_node) do
        --     v:SetData(nil)
        -- end
        self.node_list.qh_special_skill_wu:SetActive(true)
        self.node_list.qh_star_area:SetActive(false)
        self.node_list.qh_skill_content:SetActive(false)
        self.node_list.qianghua_model_display:CustomSetActive(false)
    end
end

function HiddenWeaponStrengthenView:UpdateQhSkillUI(equip_vo)
    for k, v in pairs(self.qh_skill_node) do
        v:SetData(nil)
        self.node_list["hw_qianghua_skill" .. k]:CustomSetActive(false)
    end

    --主动
    local equip = equip_vo.equip
    local color = equip.base_color
    local star = equip.base_star
    local active_skill_list = string.split(equip.active_skill_list, "|")

    local index = 1
    for k, v in pairs(active_skill_list) do
        local skill_ui = self.qh_skill_node[index]
        local skill_id = tonumber(active_skill_list[k])

        local cfg, is_active, max_cfg =
            HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.ACTIVE_SKILL_TYPE, skill_id, color, star)
        if cfg then
            skill_ui:SetData(
                {
                    cfg = cfg,
                    is_active = is_active,
                    skill_type = HW_CONST_PARAM.ACTIVE_SKILL_TYPE,
                    max_cfg = max_cfg,
                    is_bi_sha = true
                }
            )

            self.node_list["hw_qianghua_skill" .. index]:CustomSetActive(true)
            index = index + 1
        end
    end

    -- 专属
    local special_skill_list = string.split(equip.special_skill_list, "|")

    for i = 1, 2 do
        local skill_ui = self.qh_skill_node[index]
        local skill_id = tonumber(special_skill_list[i])

        local cfg, is_active, max_cfg =
            HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.SPECIAL_SKILL_TYPE, skill_id, color, star)
        if cfg then
            self.node_list["hw_qianghua_skill" .. index]:CustomSetActive(true)
            skill_ui:SetData(
                {cfg = cfg, is_active = is_active, skill_type = HW_CONST_PARAM.SPECIAL_SKILL_TYPE, max_cfg = max_cfg}
            )
            index = index + 1
        end
    end

    --被动
    index = 5
    local passive_skill_list = string.split(equip.passive_skill_list, "|")
    local num = #passive_skill_list

    for i = 1, 3 do
        local skill_ui = self.qh_skill_node[index]
        local skill_id = tonumber(passive_skill_list[i])

        local cfg, is_active, max_cfg =
            HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.PASSIVE_SKILL_TYPE, skill_id, color, star)
        if cfg then
            self.node_list["hw_qianghua_skill" .. index]:CustomSetActive(true)
            skill_ui:SetData(
                {cfg = cfg, is_active = is_active, skill_type = HW_CONST_PARAM.PASSIVE_SKILL_TYPE, max_cfg = max_cfg}
            )
            index = index + 1
        end
    end
end

function HiddenWeaponStrengthenView:UpdateQhAwakenSkill(equip_vo)
    local weapon_type = self.parent_view:GetWeaponType()
    local data_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(weapon_type)
    local max_cfg = HiddenWeaponWGData.Instance:GetMaxAwakenCfgData(weapon_type)
    local bundle, asset = ResPath.GetShenJiImage("a2_sjxt_" .. equip_vo.equip.special_skill_icon)
    self.node_list.qh_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.qh_skill_icon.image:SetNativeSize()
    end)

    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)
    local pro_equip_item = data.protocol_equip_item
    local special_effect_level = pro_equip_item.special_effect_level or 0

    self.qh_special_skill_node:SetData({big_type = weapon_type, special_effect_level = special_effect_level})
end

function HiddenWeaponStrengthenView:SetQhStar()
    local data = self:GetCurData()
    local pro_equip_item = data.protocol_equip_item
    local special_effect_level = pro_equip_item.special_effect_level or 0
    local bundle, asset = ResPath.GetCommonImages("a2_sj_xx")
    local bundle1, asset1 = ResPath.GetCommonImages("a2_sj_hx")
    for i = 1, 5 do
        if i <= special_effect_level then
            self.node_list["qianghua_star_".. i].image:LoadSprite(bundle, asset, function()
            self.node_list["qianghua_star_".. i].image:SetNativeSize()
            end)
        else
            self.node_list["qianghua_star_".. i].image:LoadSprite(bundle1, asset1, function()
            self.node_list["qianghua_star_".. i].image:SetNativeSize()
            end)
        end
    end
end

function HiddenWeaponStrengthenView:RefreshTab(i)
    for k, v in pairs(self.type_tab_arr) do
        v.sel_ui:SetActive(false)
    end
    self.type_tab_arr[i].sel_ui:SetActive(true)
end

function HiddenWeaponStrengthenView:OnClickTab(index)
    if index ~= ETabType.Inscription and self.keming_handler then
        self.keming_handler:ClearAutoFlag()
    end
    self:RefreshTab(index)
    self.node_list["zl_node"]:SetActive(false)
    self.node_list["km_node"]:SetActive(false)
    self.node_list["awaken_node"]:SetActive(false)
    self.node_list["empty_img"]:SetActive(false)
    self.cur_select_tab = index
    if index == ETabType.Soul then
        self.node_list["zl_node"]:SetActive(true)
        self:UpdateMidInfo()
        self:SoulRefresh()
    elseif index == ETabType.Inscription then
        self.node_list["km_node"]:SetActive(true)
        self:UpdateMidInfo()
        self.keming_handler:Refresh()
        self:CancelAllQuest()
        self:SetAutoUpZhuLingButtonEnabled(false)
    elseif index == ETabType.Awaken then
        self.node_list["awaken_node"]:SetActive(true)
        self:AwakenRefresh()
        self:CancelAllQuest()
        self:SetAutoUpZhuLingButtonEnabled(false)
    end

    self:SetQhStar()
end

function HiddenWeaponStrengthenView:FlushMaterialNum(change_item_id)
    local material_map = HiddenWeaponWGData.Instance:GetMaterialMap()
    if material_map and material_map[change_item_id] then
        self:Refresh("grid")
    end
end

function HiddenWeaponStrengthenView:Refresh(pro_type)
    if pro_type == "grid" then
        self:OnClickTab(self.cur_select_tab)

        if self.cur_select_tab == ETabType.Soul or self.cur_select_tab == ETabType.Inscription then
            self:UpdateMidInfo()
            self:RefreshMidProgress()
        end
    end
    self:RefreshRemind()
end

function HiddenWeaponStrengthenView:RefreshRemind()
    -- 注灵红点/进阶红点
    local weapon_type = self.parent_view:GetWeaponType()
    if weapon_type == 1 then
        local z1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL1)
        local j1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_JJ1)
        self.node_list["remind_zl"]:SetActive(z1 > 0 or j1 > 0)
        self.node_list["remind_zl_sub"]:SetActive(z1 > 0)
        self.node_list["remind_jj_sub"]:SetActive(j1 > 0)
    elseif weapon_type == 2 then
        local z1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL2)
        local j1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_JJ2)
        self.node_list["remind_zl"]:SetActive(z1 > 0 or j1 > 0)
        self.node_list["remind_zl_sub"]:SetActive(z1 > 0)
        self.node_list["remind_jj_sub"]:SetActive(j1 > 0)
    else
        self.node_list["remind_zl"]:SetActive(false)
        self.node_list["remind_zl_sub"]:SetActive(false)
        self.node_list["remind_jj_sub"]:SetActive(false)
    end
    -- 刻铭红点
    local k1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_KM1)
    local k2 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_KM2)
    if (k1 > 0 and weapon_type == 1) or (k2 > 0 and weapon_type == 2) then
        self.node_list["remind_km"]:SetActive(true)
        self.node_list["remind_km_sub"]:SetActive(true)
        self.node_list["remind_auto_km"]:SetActive(true)
    else
        self.node_list["remind_km"]:SetActive(false)
        self.node_list["remind_km_sub"]:SetActive(false)
        self.node_list["remind_auto_km"]:SetActive(false)
    end
    -- 觉醒红点
    local a1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_AWAKE1)
    local a2 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_AWAKE2)
    if (a1 > 0 and weapon_type == 1) or (a2 > 0 and weapon_type == 2) then
        self.node_list["remind_awake"]:SetActive(true)
        self.node_list["remind_awake_sub"]:SetActive(true)
    else
        self.node_list["remind_awake"]:SetActive(false)
        self.node_list["remind_awake_sub"]:SetActive(false)
    end
end

function HiddenWeaponStrengthenView:CloseCallBack()
    self.qh_model_id = -1
    self:CancelAllQuest()
    self:SetAutoUpZhuLingButtonEnabled(false)

    if self.keming_handler then
        self.keming_handler:ClearAutoFlag()
    end

    if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
    end
end

------------
function HiddenWeaponStrengthenView:LoadModel()
    if not self.qianghua_model_display then
        self.qianghua_model_display = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["qianghua_model_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = true,
        }
        
        self.qianghua_model_display:SetRenderTexUI3DModel(display_data)
        -- self.qianghua_model_display:SetUI3DModel(self.node_list["qianghua_model_display"].transform, self.node_list["qianghua_model_display"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end
end

-------------------------
--注灵
-------------------------
function HiddenWeaponStrengthenView:SoulLoadUI()
    -- 加号按钮
    -- self.upgrade_zl_button = LongClickButton.New(self.node_list["btn_zhuling"].event_trigger_listener)
    -- --点击抬起
    -- self.upgrade_zl_button:AddPointerClickListener(BindTool.Bind(self.CancelAllQuest, self))
    -- --长按抬起
    -- self.upgrade_zl_button:AddLongClickUpListener(BindTool.Bind(self.CancelAllQuest, self))
    -- --按下监听
    -- self.upgrade_zl_button:AddPointDownListener(BindTool.Bind(self.OnClickDown, self))
    XUI.AddClickEventListener(self.node_list["btn_zhuling"], BindTool.Bind(self.OnClickDown, self))

    XUI.AddClickEventListener(
        self.node_list["btn_jinjie"],
        BindTool.Bind1(
            self.SoulJJOnClick,
            self
        )
    )

    self.is_auto_zhuling = false

    local zl_attr_area = self.node_list["zl_attr_area_group"]
    self.zl_attr_arr = {}

    for i = 1, 3 do
        local attr_ui = zl_attr_area:FindObj("attr_" .. i)
        local cur_attr_txt = attr_ui:FindObj("cur_attr")
        local next_attr_txt = attr_ui:FindObj("next_attr")

        self.zl_attr_arr[i] = {
            attr_ui = attr_ui,
            cur_attr_txt = cur_attr_txt,
            next_attr_txt = next_attr_txt
        }
    end

    local jj_attr_area = self.node_list["jj_attr_area"]
    self.jj_attr_arr = {}
    for i = 1, 3 do
        local attr_ui = jj_attr_area:FindObj("attr_" .. i)
        local cur_attr_txt = attr_ui:FindObj("cur_attr")
        local next_attr_txt = attr_ui:FindObj("next_attr")

        self.jj_attr_arr[i] = {
            attr_ui = attr_ui,
            cur_attr_txt = cur_attr_txt,
            next_attr_txt = next_attr_txt
        }

    end

    self.jj_cost_item = ItemCell.New(self.node_list["cost_item_node"])
    -- --技能
    -- for index = 1, 7 do
    --     self.qh_skill_node[index] = HWDetailSkillRender.New(self.node_list["hw_qianghua_skill" .. index])
    -- end
    -- 专属技能
    self.qh_special_skill_node = HWDetailSpecialSkillRender.New(self.node_list["qh_skill_special"])
end

function HiddenWeaponStrengthenView:IsAutoUpZhuLing()
    return self.is_auto_zhuling
end

function HiddenWeaponStrengthenView:SetAutoUpZhuLingButtonEnabled(enabled)
    if not self.is_init_loaded then
        return
    end

    self.is_auto_zhuling = enabled
    self.node_list["zhuling_text"].text.text = self:IsAutoUpZhuLing() and Language.ShenJiEquip.ZhuLingBtnStr[1] or Language.ShenJiEquip.ZhuLingBtnStr[2]
end

function HiddenWeaponStrengthenView:SoulJJOnClick()
    if not self.zl_is_can_enough_jj then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.zl_jj_cost_item_id})
        return
    end

    HiddenWeaponRequest:ReqUpgrade2(self.parent_view:GetWeaponType())
end

function HiddenWeaponStrengthenView:ShowZLCostItemTip()
    local weapon_type = self.parent_view:GetWeaponType()
    local item_id = HW_CONST_PARAM.EXP_ICON_IDS[weapon_type]
    -- local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)

    local data = {
        item_id = item_id
    }
    TipWGCtrl.Instance:OpenItem(data, ItemTip.FROM_NORMAL, nil)
end

function HiddenWeaponStrengthenView:SoulRefresh()
    local weapon_type = self.parent_view:GetWeaponType()
    local data = self:GetCurData()
    local equip = data.equip
    local nengliang = HW_CONST_PARAM.EXP_ICON_IDS[weapon_type]
    self.wupin_item:SetData({item_id = nengliang})
    self.wupin_item:SetRightBottomTextVisible(true)
    self.node_list["zl_area"]:SetActive(false)
    self.node_list["jj_area"]:SetActive(false)


    if equip then
        local is_can_upgrade = HiddenWeaponWGData.Instance:IsCanUpGrade(weapon_type)
        if not is_can_upgrade then
            self:SoulUpdateZLRightInfo()
        else
            self:SetAutoUpZhuLingButtonEnabled(false)
            self:SoulUpdateJJRightInfo()
        end

        if not self.old_is_can_upgrade then
            self.old_is_can_upgrade = is_can_upgrade
        end
        self.old_is_can_upgrade = is_can_upgrade
    end
end

function HiddenWeaponStrengthenView:SoulUpdateZLRightInfo()
    self.node_list["zl_area"]:SetActive(true)
    self.node_list["zl_no_max"]:SetActive(true)
    self.node_list["zl_max_img"]:SetActive(false)

    local data_ins = HiddenWeaponWGData.Instance
    local weapon_type = self.parent_view:GetWeaponType()
    local data = data_ins:GetSCShenJiEquipGridByType(weapon_type)
    local equip = data.equip
    local protocol_equip_item = data.protocol_equip_item
    if equip then
        local cur_lv = protocol_equip_item.level
        local next_lv = cur_lv + 1
        local grade = protocol_equip_item.grade
        local node_type = equip.node_type
        local max_lv = data_ins:GetZLAttrMaxLv(weapon_type, node_type)
        local cur_attr = data_ins:GetZLAttrCfg(weapon_type, node_type, cur_lv)
        local is_max = (cur_lv >= max_lv)

        local jj_attr = data_ins:GetJJAttrCfg(weapon_type, node_type, grade)
        local attr_sum = AttributeMgr.AddAttributeAttr(jj_attr, cur_attr)


        local next_attr = {}
        if not is_max then
            next_attr = data_ins:GetZLAttrCfg(weapon_type, node_type, next_lv)
        end

        local sort_list = AttributeMgr.SortAttribute()
        local attr_name = Language.Common.AttrNameList2
        local cur_attr_sort = {}
        for k, v in pairs(sort_list) do
            local val = cur_attr[v]
            if val and val > 0 then
                if cur_lv == 0 then
                    table.insert(cur_attr_sort, {name = attr_name[v], attr_type = v, attr_val = 0})
                else
                    table.insert(cur_attr_sort, {name = attr_name[v], attr_type = v, attr_val = val})
                end
            end
        end

        for i=1,#self.zl_attr_arr do
            local ui_data = self.zl_attr_arr[i]
            ui_data.attr_ui:SetActive(not IsEmptyTable(cur_attr_sort[i]))
            if cur_attr_sort[i] then
                local attr = cur_attr_sort[i]
                local next_val = next_attr[attr.attr_type] or 0
                local ui_data = self.zl_attr_arr[i]
                if attr.attr_val > 0 then
                    ui_data.cur_attr_txt.text.text = string.format("%s %s", attr.name, ToColorStr(cur_attr[attr.attr_type], COLOR3B.WHITE))
                else
                    ui_data.cur_attr_txt.text.text = string.format("%s %s", attr.name, ToColorStr(attr.attr_val, COLOR3B.WHITE))
                end

                if next_val == 0 then
                    ui_data.next_attr_txt:SetActive(false)
                else
                    ui_data.next_attr_txt:SetActive(not is_max)
                    ui_data.next_attr_txt.text.text = (next_val - attr.attr_val)
                end
            end
        end

        -- for k, v in pairs(cur_attr_sort) do
        --     local next_val = next_attr[v.attr_type] or 0
        --     local ui_data = self.zl_attr_arr[k]

        --     if v.attr_val > 0 then
        --         ui_data.cur_attr_txt.text.text = string.format("%s: %s", v.name, ToColorStr(cur_attr[v.attr_type],"#e4d3b8"))
        --     else
        --         ui_data.cur_attr_txt.text.text = string.format("%s: %s", v.name, v.attr_val)
        --     end

        --     if next_val == 0 then
        --         ui_data.next_attr_txt:SetActive(false)
        --     else
        --         ui_data.next_attr_txt:SetActive(not is_max)
        --         ui_data.next_attr_txt.text.text = (next_val - v.attr_val)
        --     end
        -- end

        local has_exp = protocol_equip_item.exp_value
        self.wupin_item:SetRightBottomText(has_exp)
        self.node_list["zhuling_text"].text.text = self:IsAutoUpZhuLing() and Language.ShenJiEquip.ZhuLingBtnStr[1] or Language.ShenJiEquip.ZhuLingBtnStr[2]
        self.node_list["zl_no_enough_exp_txt"]:SetActive(false)

        self.node_list["zl_no_enough_exp_txt"].text.text = Language.ShenJiEquip.NO_ENOUGH_EXP_TIS[weapon_type]

        if is_max then
            self.node_list["zl_no_max"]:SetActive(false)
            self.node_list["zl_max_img"]:SetActive(true)
        end
    else
    end
end

function HiddenWeaponStrengthenView:SoulUpdateJJRightInfo()
    self.node_list["jj_area"]:SetActive(true)
    self:CancelAllQuest()

    local data_ins = HiddenWeaponWGData.Instance
    local weapon_type = self.parent_view:GetWeaponType()
    local data = data_ins:GetSCShenJiEquipGridByType(weapon_type)
    local equip = data.equip
    local protocol_equip_item = data.protocol_equip_item
    if equip then
        local cur_grade = protocol_equip_item.grade
        local next_grade = cur_grade + 1
        local node_type = equip.node_type

        local cur_attr = data_ins:GetJJAttrCfg(weapon_type, node_type, cur_grade)
        local next_attr = data_ins:GetJJAttrCfg(weapon_type, node_type, next_grade)


        local cur_lv = protocol_equip_item.level
        local zl_attr = data_ins:GetZLAttrCfg(weapon_type, node_type, cur_lv)
        local attr_sum = AttributeMgr.AddAttributeAttr(zl_attr, cur_attr)
        -- local attr_sum = ps

        --最大阶数不会到这里来  所以这里不用判断最大值

        local sort_list = AttributeMgr.SortAttribute()
        local attr_name = Language.Common.AttrNameList2
        local cur_attr_sort = {}
        for k, v in pairs(sort_list) do
            local val = cur_attr[v]
            if val and val > 0 then
                if cur_grade == 0 then
                    val = 0
                end
                table.insert(cur_attr_sort, {name = attr_name[v], attr_type = v, attr_val = val})
            end
        end

        for i=1,#self.jj_attr_arr do
            local ui_data = self.jj_attr_arr[i]
            ui_data.attr_ui:SetActive(not IsEmptyTable(cur_attr_sort[i]))
            if cur_attr_sort[i] then
                local attr = cur_attr_sort[i]
                local next_val = next_attr[attr.attr_type] or 0

                if attr.attr_val > 0 then
                    ui_data.cur_attr_txt.text.text = string.format("%s %s", attr.name, ToColorStr(cur_attr[attr.attr_type], COLOR3B.WHITE))
                else
                    ui_data.cur_attr_txt.text.text = string.format("%s %s", attr.name, ToColorStr(attr.attr_val, COLOR3B.WHITE))
                end

                if next_val == 0 then
                    ui_data.next_attr_txt:SetActive(false)
                else
                    ui_data.next_attr_txt:SetActive(true)
                    ui_data.next_attr_txt.text.text = (next_val - attr.attr_val)
                end
            end
        end

        -- for k, v in pairs(cur_attr_sort) do
        --     local next_val = next_attr[v.attr_type] or 0
        --     local ui_data = self.jj_attr_arr[k]

        --     if v.attr_val > 0 then
        --         ui_data.cur_attr_txt.text.text = string.format("%s: %s", v.name, cur_attr[v.attr_type])
        --     else
        --         ui_data.cur_attr_txt.text.text = string.format("%s: %s", v.name, v.attr_val)
        --     end


        --     if next_val == 0 then
        --         ui_data.next_attr_txt:SetActive(false)
        --     else
        --         ui_data.next_attr_txt:SetActive(true)
        --         ui_data.next_attr_txt.text.text = (next_val - v.attr_val)
        --     end
        -- end

        local next_cfg = data_ins:GetUpgradeCfg(weapon_type, node_type, next_grade)
        local consume_item_id = next_cfg["consume_item"]
        local consume_item_num = next_cfg["consume_num"]
        local has_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)

        local item_data = {
            item_id = consume_item_id
        }
        self.jj_cost_item:SetData(item_data)
        self.jj_cost_item:SetRightBottomTextVisible(true)
        local color = has_num >= consume_item_num and "#72eba9" or "#FF5252"
        self.jj_cost_item:SetRightBottomText(
            "<color=" .. color .. ">" .. has_num .. "/" .. consume_item_num .. "</color>"
        )

        self.zl_is_can_enough_jj = has_num >= consume_item_num
        self.zl_jj_cost_item_id = consume_item_id
    else
    end
end

function HiddenWeaponStrengthenView:SoulDestory()
    if self.jj_cost_item then
        self.jj_cost_item:DeleteMe()
        self.jj_cost_item = nil
    end

    self:CancelAllQuest()
    -- if self.qh_skill_node then
    --     for index, value in ipairs(self.qh_skill_node) do
    --         if value and value.DeleteMe then
    --             value:DeleteMe()
    --         end
    --     end
    -- end
    -- self.qh_skill_node = nil

    if self.qh_special_skill_node then
        self.qh_special_skill_node:DeleteMe()
        self.qh_special_skill_node = nil
    end
end

-------------------------
--刻铭
-------------------------
function HiddenWeaponStrengthenView:InscriptionLoadUI()
    self.keming_handler = HiddenWeaponKemingHandler.New(self.parent_view)
    self.keming_handler:InscriptionLoadUI()
end

function HiddenWeaponStrengthenView:InscriptionDestory()
    if self.keming_handler and self.keming_handler.DeleteMe then
        self.keming_handler:DeleteMe()
    end
end

-------------------------
--觉醒
-------------------------
function HiddenWeaponStrengthenView:AwakenLoadUI()
    self.awaken_btn = self.node_list["awaken_btn"]
    XUI.AddClickEventListener(
        self.awaken_btn,
        BindTool.Bind1(
            self.AwakenOnClick,
            self
        )
    )

    XUI.AddClickEventListener(self.node_list["cur_awake_skill_icon"],BindTool.Bind(self.ClickAwakenSelIcon,self, 1))
    XUI.AddClickEventListener(self.node_list["next_awake_skill_icon"],BindTool.Bind(self.ClickAwakenSelIcon,self, 2))

    self.awaken_cost_item = ItemCell.New(self.node_list["awaken_cost_item_node"])
    self.awaken_cost_item:SetNeedItemGetWay(true)
end

function HiddenWeaponStrengthenView:AwakenOnClick()
    local weapon_type = self.parent_view:GetWeaponType()
    local weapon_type_data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)
    if IsEmptyTable(weapon_type_data.equip) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiEquip.NO_EQUIP_TIPS[weapon_type])
        return
    end

    if not self.awaken_has_enough_cost then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenJiEquip.AWAKEN_NO_ENOUGH_TIP, self.awaken_cost_item_cfg.name))
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.awaken_cost_item_cfg.id})
        return
    end

    HiddenWeaponRequest:ReqAwake(self.parent_view:GetWeaponType())
end


function HiddenWeaponStrengthenView:ClickAwakenSelIcon(icon_type)
    local weapon_type = self.parent_view:GetWeaponType()
    local data = self:GetCurData()
    local pro_equip_item = data.protocol_equip_item or {}
    local special_effect_level = pro_equip_item.special_effect_level or 0
    if icon_type == 2 then
        special_effect_level = special_effect_level + 1
    end
    local data = {big_type = weapon_type, special_effect_level = special_effect_level}
    HiddenWeaponWGCtrl.Instance:ShowDetailSkillView(data)
end

function HiddenWeaponStrengthenView:AwakenRefresh()
    local weapon_type = self.parent_view:GetWeaponType()
    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)
    local equip = data.equip
    if equip then 
        self.node_list["empty_img"]:SetActive(false)
        local data_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(weapon_type)
        local data = self:GetCurData()
        local pro_equip_item = data.protocol_equip_item or {}

        local data_list = {}
        for k, v in ipairs(data_cfg) do
            if k > 1 then
                table.insert(data_list, {cfg = v, weapon_type = weapon_type, pre_cfg = data_cfg[k - 1]})
            end
        end

        local special_effect_level = pro_equip_item.special_effect_level or 0
        local num = #data_list
        local is_max = (special_effect_level >= num)
        local jump = special_effect_level + 1
        if is_max then
            jump = num
        end
        self:AwakenUpdateMidUI()
        self:AwakenUpdateAttr()
    else
        self.node_list["empty_img"]:SetActive(true)
        self.node_list["awaken_node"]:SetActive(false)
    end
end

function HiddenWeaponStrengthenView:AwakenUpdateAttr()
    local weapon_type = self.parent_view:GetWeaponType()
    local data_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(weapon_type)
    local data = self:GetCurData()

    local pro_equip_item = data.protocol_equip_item or {}
    local special_effect_level = pro_equip_item and pro_equip_item.special_effect_level or 0
    local is_max = (special_effect_level >= (#data_cfg - 1))
    local cur_level_cfg = data_cfg[special_effect_level + 1] -- 配置表1开始  等级0开始
    local next_level_cfg = {}
    if not is_max then
        next_level_cfg = data_cfg[special_effect_level + 2]
    end

    local cur_attr_list = AttributeMgr.GetAttributteByClass(cur_level_cfg)
    local next_attr_list = AttributeMgr.GetAttributteByClass(next_level_cfg)
    local attr_sort = {}
    local attr_name = Language.Common.AttrNameList2
    local sort_list = AttributeMgr.SortAttribute()
     for k,v in pairs(sort_list) do
        local cur_val = cur_attr_list[v]
        local next_val = next_attr_list[v]
        local attr_value = cur_val
        local add_value = 0
        if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
            attr_value = (cur_val / 100) .. '%'
        end
        if next_val and next_val > 0 then
            add_value = next_val - cur_val
        end
        if cur_val > 0 or next_val > 0 then
            table.insert(attr_sort, {name = attr_name[v], attr_type = v, attr_val = attr_value, add_value = add_value})
        end
    end
    local count = 0
    for i,v in ipairs(attr_sort) do
        self.node_list["juexing_base_attr_" .. i].text.text = v.name .. " " .. ToColorStr(v.attr_val, COLOR3B.WHITE)
        self.node_list["juexing_add_attr_" .. i]:SetActive(v.add_value > 0)
        if v.add_value > 0 then
            local add_atrr = v.add_value
            if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_type) then
                add_atrr = (v.add_value / 100) .. '%'
            end
            self.node_list["juexing_add_attr_" .. i].text.text = add_atrr
        end
        count = count + 1
    end
    for i = 1, 6 do
       self.node_list["juexing_attr_" .. i]:SetActive(i <= count)
    end
end

function HiddenWeaponStrengthenView:AwakenUpdateMidUI()
    local weapon_type = self.parent_view:GetWeaponType()
    local data_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(weapon_type)
    local data = self:GetCurData()
    local pro_equip_item = data.protocol_equip_item
    local special_effect_level = pro_equip_item.special_effect_level or 0
    local is_max = (special_effect_level >= (#data_cfg - 1))
    if data and data.equip then
        self.node_list.cur_awake_star_area:SetActive(true)
        local bundle2, asset2 = ResPath.GetShenJiImage("a2_sjxt_" .. data.equip.special_skill_icon)
        self.node_list.cur_awake_skill_icon.image:LoadSprite(bundle2, asset2, function()
            self.node_list.cur_awake_skill_icon.image:SetNativeSize()
        end)
        local bundle, asset = ResPath.GetCommonImages("a2_sj_xx")
        local bundle1, asset1 = ResPath.GetCommonImages("a2_sj_hx")
        for i = 1, 5 do
            -- self.node_list["cur_awake_star_".. i]:SetActive(i <= special_effect_level)
            if i <= special_effect_level then
                self.node_list["cur_awake_star_".. i].image:LoadSprite(bundle, asset, function()
                self.node_list["cur_awake_star_".. i].image:SetNativeSize()
            end)
            else
                self.node_list["cur_awake_star_".. i].image:LoadSprite(bundle1, asset1, function()
                    self.node_list["cur_awake_star_".. i].image:SetNativeSize()
                end)
            end
        end

        if not is_max then -- 没有满级
            self.node_list.next_skill_icon_bg:SetActive(true)
            self.node_list.next_juexing_desc:SetActive(true)
            self.node_list.juexing_desc:SetActive(false)
            self.node_list.next_awake_skill_icon.image:LoadSprite(bundle2, asset2, function()
                self.node_list.next_awake_skill_icon.image:SetNativeSize()
            end)
            for i = 1, 5 do
                if i <= special_effect_level + 1 then
                    self.node_list["next_awake_star_".. i].image:LoadSprite(bundle, asset, function()
                    self.node_list["next_awake_star_".. i].image:SetNativeSize()
                end)
                else
                    self.node_list["next_awake_star_".. i].image:LoadSprite(bundle1, asset1, function()
                        self.node_list["next_awake_star_".. i].image:SetNativeSize()
                    end)
                end
            end
            self.node_list["awaken_mid_desc"].text.text = data_cfg[special_effect_level + 2].skill_des_dark
            self.node_list.Text_title:SetActive(true)
        else
            self.node_list.Text_title:SetActive(false)
            self.node_list.next_skill_icon_bg:SetActive(false)
            self.node_list.next_juexing_desc:SetActive(false)
            self.node_list.juexing_desc:SetActive(true)
        end
        for i = 1, 5 do
            self.node_list["juexing_text_".. i].text.text = data_cfg[i + 1].skill_des_dark
        end
    else
        self.node_list.cur_awake_star_area:SetActive(false)
        local bundle, asset = ResPath.GetCommon("a3_ty_skill_bg")
        self.node_list.cur_awake_skill_icon.image:LoadSprite(bundle, asset, function()
            self.node_list.cur_awake_skill_icon.image:SetNativeSize()
        end)
        self.node_list.next_skill_icon_bg:SetActive(false)
        self.node_list.next_juexing_desc:SetActive(false)
        self.node_list.juexing_desc:SetActive(true)
    end
    self.node_list["awaken_max"]:SetActive(is_max)
    self.node_list["awaken_cost_item_node"]:SetActive(not is_max)
    self.node_list["cost_item_bg"]:SetActive(not is_max)
    self.awaken_btn:SetActive(not is_max)
    if is_max then
        return
    end

    local next_cfg = data_cfg[special_effect_level + 2]
    local consume_item_id = next_cfg["consume_item"]
    local consume_item_num = next_cfg["consume_num"]
    local has_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)

    local item_data = {
        item_id = consume_item_id
    }

    self.awaken_cost_item_cfg = ItemWGData.Instance:GetItemConfig(consume_item_id)

    self.awaken_cost_item:SetData(item_data)
    self.awaken_has_enough_cost = has_num >= consume_item_num
    local color = self.awaken_has_enough_cost and "#28E53A" or "#FF6275"
    self.awaken_cost_item:SetRightBottomTextVisible(true)
    self.awaken_cost_item:SetRightBottomText(
        "<color=" .. color .. ">" .. has_num .. "/" .. consume_item_num .. "</color>"
    )
end

function HiddenWeaponStrengthenView:AwakenOnSelect(item)
end

function HiddenWeaponStrengthenView:AwakenDestory()
    self.is_init_awaken_list = false
    if self.awaken_cost_item then
        self.awaken_cost_item:DeleteMe()
        self.awaken_cost_item = nil
    end
end