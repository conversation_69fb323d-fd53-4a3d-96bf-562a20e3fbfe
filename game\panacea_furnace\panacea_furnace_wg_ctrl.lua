require("game/panacea_furnace/panacea_furnace_view")
require("game/panacea_furnace/panacea_furnace_wg_data")
require("game/panacea_furnace/panacea_furnace_reward_view")

PanaceaFurnaceWGCtrl = PanaceaFurnaceWGCtrl or BaseClass(BaseWGCtrl)
function PanaceaFurnaceWGCtrl:__init()
	if PanaceaFurnaceWGCtrl.Instance then
		error("[PanaceaFurnaceWGCtrl]:Attempt to create singleton twice!")
	end

    PanaceaFurnaceWGCtrl.Instance = self

    self.data = PanaceaFurnaceWGData.New()
    self.view = PanaceaFurnaceView.New(GuideModuleName.PanaceaFurnaceView)
    self.reward_view = PanaceaFurnaceRewardView.New()

    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)

    self:RegisterAllProtocols()
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function PanaceaFurnaceWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    self.reward_view:DeleteMe()
	self.reward_view = nil

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
    end

    PanaceaFurnaceWGCtrl.Instance = nil
end

function PanaceaFurnaceWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOAGloryCrystalInfo2, "OnSCOAGloryCrystalInfo")
    self:RegisterProtocol(SCOAGloryCrystalDrawResult2, "OnSCOAGloryCrystalDrawResult")
end

function PanaceaFurnaceWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local other_cfg = self.data:GetOtherCfg()
    if (change_item_id == other_cfg.cost_item_id or self.data:GetIsStuffItem(change_item_id)) and old_num < new_num then
        if self.view:IsOpen() then
            self.view:OnItemDataChange()
        end

        RemindManager.Instance:Fire(RemindName.PanaceaFurnace)
    end
end

function PanaceaFurnaceWGCtrl:ReqPanaceaFurnaceInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_PANACEA_FURNACE
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function PanaceaFurnaceWGCtrl:OnSCOAGloryCrystalInfo(protocol)
    local old_reward_state = self.data:GetAllRewardState()
	local reward_list = {}
	local reward_cfg = self.data:GetAllTimesRewardCfg()
	if not IsEmptyTable(old_reward_state) then
		for k, v in pairs(protocol.times_reward_flag) do
			if old_reward_state[k] ~= v and old_reward_state[k] == 0 and reward_cfg[k] then
				table.insert(reward_list, reward_cfg[k].item)
			end
		end
	end

	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end

	self.data:SetAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.PanaceaFurnaceView)
	RemindManager.Instance:Fire(RemindName.PanaceaFurnace)
end

function PanaceaFurnaceWGCtrl:OnSCOAGloryCrystalDrawResult(protocol)
	self.data:SetResultData(protocol)
	-- ViewManager.Instance:FlushView(GuideModuleName.PanaceaFurnaceView, nil, "play_ani")
	-- local delay_time = self.data:GetDelayTime()
    -- GlobalTimerQuest:AddDelayTimer(function ()
    local other_cfg = self.data:GetOtherCfg()
    local reward_list = PanaceaFurnaceWGData.Instance:GetResultRewardList()
    local other_info = {}
    other_info.stuff_id = other_cfg.cost_item_id
    other_info.times = protocol.count
    other_info.spend = other_cfg.cost_gold
    other_info.again_text = Language.PanaceaFurnace.BtnText[protocol.mode]

    TipWGCtrl.Instance:ShowGetValueReward(reward_list, function()
        self:ReqPanaceaFurnaceInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.DRAW, protocol.mode)
    end, other_info, false)
    -- end, delay_time)
end

function PanaceaFurnaceWGCtrl:MainuiOpenCreate()
	self:ReqPanaceaFurnaceInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.INFO)
end

--使用道具并弹窗
function PanaceaFurnaceWGCtrl:ClickUse(mode_type, form_reward_view)
    --数量检测
   local other_cfg = self.data:GetOtherCfg()
   local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
   local mode_cfg = self.data:GetModeCfgByMode(mode_type)

   if not mode_cfg then
       return
   end

   local tips_data = {}
   tips_data.item_id = other_cfg.cost_item_id
   tips_data.price = other_cfg.cost_gold
   tips_data.draw_count = mode_cfg.cost_item_num
   tips_data.has_checkbox = true
   tips_data.checkbox_str = string.format("panacea_furnace_%d", mode_type)
   TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, function()
        self:ReqPanaceaFurnaceInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.DRAW, mode_type)
   end, nil)
end