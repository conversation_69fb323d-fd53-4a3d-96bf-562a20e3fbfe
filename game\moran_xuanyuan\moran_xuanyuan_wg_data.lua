MoRanXuanYuanWGData = MoRanXuanYuanWGData or BaseClass()

function MoRanXuanYuanWGData:__init()
	if MoRanXuanYuanWGData.Instance then
		ErrorLog("[MoRanXuanYuanWGData] Attemp to create a singleton twice !")
	end
	MoRanXuanYuanWGData.Instance = self

	self:InitCfg()

	self.grade = 0
    self.cur_draw_times = 0
    self.fetch_flag = 0
    self.record_list = {}

    RemindManager.Instance:Register(RemindName.MoRanXuanYuan, BindTool.Bind(self.ShowRed, self))
end

function MoRanXuanYuanWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.MoRanXuanYuan)
	MoRanXuanYuanWGData.Instance = nil
end

function MoRanXuanYuanWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_yanhua_shengdian5_auto")
	self.param_cfg = ListToMap(cfg.config_param, "grade")
	self.grade_cfg = cfg.grade
	self.consume_cfg = ListToMapList(cfg.consume, "consume")
    self.reward_cfg = ListToMap(cfg.reward, "reward", "reward_id")
    self.rebate_cfg = ListToMap(cfg.rebate, "rebate", "index")
	self.role_sp_guarantee_cfg = ListToMapList(cfg.role_sp_guarantee, "reward")
    self.item_random_desc_cfg = ListToMapList(cfg.item_random_desc, "grade")
end

function MoRanXuanYuanWGData:SetInfo(protocol)
    self.grade = protocol.grade
    self.cur_draw_times = protocol.person_draw_count
    self.fetch_flag = protocol.leiji_reward_fetch_flag
    self.is_skip_comic = protocol.is_skip_comic         --跳过动画？
    self.sp_guarantee_x = protocol.sp_guarantee_x       --特殊保底次数？ 弃用
    self.sp_guarantee_n = protocol.sp_guarantee_n       --特殊保底轮数？
    self.sp_enter_num = protocol.sp_enter_num           --进入保底库次数？

    self.gather_small_count = protocol.gather_small_count
    self.gather_big_count = protocol.gather_big_count
end

--日志协议
function MoRanXuanYuanWGData:SetRecord(record_list)
    self.record_list = record_list
    self.new_record_time = self.record_list[1] and self.record_list[1].draw_time or 0
end


function MoRanXuanYuanWGData:GetRecordInfo()
    local list = {}
    if not IsEmptyTable(self.record_list) then
        for k, v in pairs(self.record_list) do
            list[k] = v
        end
    end
    
    if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyUpperSorter("draw_time"))
    end
    --获取日志
    return list
end

function MoRanXuanYuanWGData:GetCurDrawTimes()
    return self.cur_draw_times
end

function MoRanXuanYuanWGData:GetFanliHasGet(index)
    return bit:_and(self.fetch_flag or 0, bit:_lshift(1, index - 1)) ~= 0
end

function MoRanXuanYuanWGData:GetGradeCfg()
    return self.grade_cfg[self.grade] or self.grade_cfg[0] -- 没有配置 就取默认配置
end

function MoRanXuanYuanWGData:GetConsumeCfg()
    local cfg = self:GetGradeCfg()
    return self.consume_cfg[cfg.consume]
end

function MoRanXuanYuanWGData:GetRewardById(reward_pool_id, reward_id)
    return self.reward_cfg[reward_pool_id or 1][reward_id]
end

function MoRanXuanYuanWGData:GetItemDataChangeList()
    if not self.item_data_change_list then
        self.item_data_change_list = {}
        local cfg = self:GetConsumeCfg()
        for i, v in pairs(cfg) do
            table.insert(self.item_data_change_list, v.yanhua_item.item_id)
        end
    end
    return self.item_data_change_list
end

function MoRanXuanYuanWGData:GetShowAllrewardList()
    local show_list = {}
    local grade_cfg = self:GetGradeCfg()

    local reward_pool = grade_cfg and grade_cfg.reward_unbind or 1
    local reward_cfg = self.reward_cfg[reward_pool]  --做展示用的奖励配置
    for i, v in ipairs(reward_cfg) do
        table.insert(show_list, v.reward_item)
    end

    return show_list
end

function MoRanXuanYuanWGData:GetFanliRewardList()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local rebate_cfg = self.rebate_cfg[rebate]
    local draw_time = self:GetCurDrawTimes()
    local list = {}
    for i = 1, #rebate_cfg do
        local temp = {}
        if draw_time >= rebate_cfg[i].lotto_num then
            if not self:GetFanliHasGet(rebate_cfg[i].index) then
                temp.index = rebate_cfg[i].index
            else
                temp.index = 1000 + rebate_cfg[i].index
            end
        else
            temp.index = 100 + rebate_cfg[i].index
        end

        temp.data = rebate_cfg[i]
        temp.has_get = self:GetFanliHasGet(rebate_cfg[i].index) and 1 or 0
        table.insert(list, temp)
    end
    
    if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyLowerSorter("index"))
    end

    return list
end

function MoRanXuanYuanWGData:CacheOrGetDrawIndex(btn_index)
    if btn_index then
        self.draw_btn_index = btn_index
    end

    return self.draw_btn_index
end

--获取奖励对应的配置
function MoRanXuanYuanWGData:CalDrawRewardList(protocol)
    local data_list = {}
    if not protocol or not protocol.count or protocol.count <= 0 then
        return data_list
    end

    for i,v in ipairs(protocol.reward_list) do
        local cfg = self:GetRewardById(v.reward_pool_id, v.reward_id)
        if cfg then
            local temp = cfg.reward_item
            table.insert(data_list, temp)
        else
            print_error("错误数据 请检查奖励配置 reward_pool_id,reward_id: ", v.reward_pool_id, v.reward_id)
        end
    end

    return data_list
end

--已经抽到的奖励
function MoRanXuanYuanWGData:SaveDrawInfo(protocol)
    self.had_draw_list = protocol.reward_info
end

function MoRanXuanYuanWGData:ShowRed()
    --背包有道具
    if self:GetEnoughItem() then
        return 1
    end

    --返利
    if self:GetFanliRed() then
        return 1
    end

    return 0
end

function MoRanXuanYuanWGData:GetEnoughItem()
    local item_list = self:GetItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

function MoRanXuanYuanWGData:GetFanliRed()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local draw_time = self:GetCurDrawTimes()
    for i, v in ipairs(self.rebate_cfg[rebate]) do
        if draw_time >= v.lotto_num then
            if not self:GetFanliHasGet(v.index) then
                return true
            end
        else
            return false
        end
    end
end

function MoRanXuanYuanWGData:GetRandomGaiLvinfo()
    return self.item_random_desc_cfg[self.grade]
end
