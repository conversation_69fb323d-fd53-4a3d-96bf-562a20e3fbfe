EquipmentWGData = EquipmentWGData or BaseClass()

-------------------------------------------------INIT_START-------------------------------------------------
function EquipmentWGData:InitBaptizeData()
	self.baptize_lock_flag = {}
	self.baptize_info_list = {}

	self.today_free_baptize_num = 0    -- 今日剩余免费洗炼次数
	self.baptize_list = {}             -- 所有部位洗炼信息      序号 装备 equip_index 0 - 374
	self.baptize_add_act_remind_list = {} -- 用于阶数加成红点判断

	local baptize_cfg = ConfigManager.Instance:GetAutoConfig("equip_baptize_cfg_auto")

	self.other_cfg = baptize_cfg.other
	self.new_baptize_weight = baptize_cfg.new_baptize_weight
	self.new_baptize_slot_open = ListToMap(baptize_cfg.new_slot_open, "equip_seq", "slot_id") -- 洗炼槽位开启
	self.new_baptize_part_open = ListToMap(baptize_cfg.new_part_open, "equip_seq", "equip_part") -- 洗炼部位开启
	self.new_baptize_consume = ListToMap(baptize_cfg.new_baptize_consume, "lock_num") -- 洗炼消耗
	self.new_baptize_attr = ListToMapList(baptize_cfg.new_baptize_attr, "part") -- 洗炼加成表（阶级）
	self.new_baptize_word_grade = ListToMap(baptize_cfg.word_grade, "attr_id", "grade") -- 词条阶级表
	self.new_baptize_up_grade = ListToMap(baptize_cfg.up_grade, "grade")  --进阶表
end
--------------------------------------------------INIT_END-------------------------------------------------

-------------------------------------------------PROTOCOL_START--------------------------------------------
function EquipmentWGData:SetEquipBaptizeAllInfo(protocol)
	self.today_free_baptize_num = protocol.today_free_baptize_num
	self.baptize_list = protocol.baptize_list
	for i = 0, GameEnum.MAX_ROLE_EQUIP_NUM - 1 do
		self:SetBaptizePartSlotListParam(i, self.baptize_list[i])
	end

	-- if IsEmptyTable(self.baptize_add_act_remind_list) then
	-- 	self:GetBaptizeAddRemind()
	-- end
end

function EquipmentWGData:SetEquipBaptizeOneInfo(protocol)
	self.today_free_baptize_num = protocol.today_free_baptize_num
	local baptize_part = protocol.baptize_part
	self:SetBaptizePartSlotListParam(protocol.equip_part, baptize_part)
	self.baptize_list[protocol.equip_part] = baptize_part
end

-- 设置部位的洗炼槽参数
function EquipmentWGData:SetBaptizePartSlotListParam(equip_body_index, baptize_part)
	if IsEmptyTable(baptize_part) then
		return baptize_part
	end
	
	local is_first
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local baptize_level_limit = EquipBodyWGData.Instance:GetBaptizeLevelLimit(equip_body_seq)
	local is_max_grade = baptize_level_limit < baptize_part.grade
	local target_grade = is_max_grade and baptize_level_limit or baptize_part.grade
	
	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		local slot_info = baptize_part.slot_info[i]
		slot_info.slot_id = i
		slot_info.quality = self:CalcEquipBaptizeQuality(slot_info.quality_value)
		local min_value, max_value = self:GetEquipBaptizeAttrRange(slot_info.word_type, target_grade)
		slot_info.min_value, slot_info.max_value = min_value, max_value

		local progress = slot_info.quality_value * 0.0001
		slot_info.is_max_grade = is_max_grade

		slot_info.progress = progress
		slot_info.attr_value = math.ceil(min_value + (max_value - min_value) * progress)
		slot_info.is_first_buy = false
		if not is_first and slot_info.is_buy ~= 1 then
			slot_info.is_first_buy = true
			is_first = true
		end
	end

	return baptize_part
end

-- equip_index 人物装备 0 - 374
function EquipmentWGData:GetBaptizeInfoByEquipBodyIndex(equip_body_index)
	return self.baptize_list[equip_body_index] or {}
end

function EquipmentWGData:GetBaptizeSlotInfoByEquipBodyIndex(equip_body_index)
	return (self.baptize_list[equip_body_index] or {}).slot_info or {}
end

function EquipmentWGData:GetBaptizeSlotInfoByIndexAndSlotId(equip_index, slot_id)
	return ((self.baptize_list[equip_index] or {}).slot_info or {})[slot_id] or {}
end

function EquipmentWGData:GetEquipBodyBaptizePartInfo(equip_body_seq, equip_part)
	return (self.baptize_list[equip_body_seq] or {})[equip_part] or {}
end

function EquipmentWGData:GetEquipBodyBaptizePartSlotInfo(equip_body_seq, equip_part)
	return ((self.baptize_list[equip_body_seq] or {})[equip_part] or {}).slot_info or {}
end

-- 获取今日剩余免费次数
function EquipmentWGData:GetEquipBaptizeFreeTimes()
	return self.today_free_baptize_num, self.other_cfg[1].free_baptize_count
end

function EquipmentWGData:GetEquipBaptizeOtherAttrInfo(attr_name)
	return (self.other_cfg[1] or {})[attr_name]
end
-------------------------------------------------PROTOCOL_END---------------------------------------------

-------------------------------------------------REMIND_START--------------------------------------------
-- 洗练提醒
function EquipmentWGData:GetEquipBaptizeRemindNum()
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()

	if not IsEmptyTable(total_equip_body_data_list) then
		for k, v in pairs(total_equip_body_data_list) do
			if self:GetEquipBodyBaptizeRemind(v.seq) > 0 then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_XiLian, 1, function ()
					FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_xilian)
				end)

				return 1
			end
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_XiLian, 0)
	return 0
end

-- 洗炼肉身红点
function EquipmentWGData:GetEquipBodyBaptizeRemind(equip_body_seq)
	if not self:IsEquipBaptizeUnlockLevelLimit(equip_body_seq) then
		return 0
	end

	local equip_data_list = self:GetEquipBaptizeShowList(equip_body_seq)

	if not IsEmptyTable(equip_data_list) then
		for equip_part, u in pairs(equip_data_list) do
			if self:GetEquipBodyBaptizeEquipRemind(u.index) then
				return 1
			end
		end
	end

	return 0
end

-- 洗炼肉身 单个装备红点
function EquipmentWGData:GetEquipBodyBaptizeEquipRemind(equip_body_index)
	local part_is_open = self:IsEquipBaptizePartOpen(equip_body_index)

	if not part_is_open then
		return false
	end

	local baptize_part_info = self:GetBaptizeInfoByEquipBodyIndex(equip_body_index)
	if IsEmptyTable(baptize_part_info) then
		return false
	end

	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	-- 有可免费开启槽位
	for k, v in pairs(self:GetNewBaptizeSlotOpenDataList(equip_body_seq)) do
		if v.consume_gold <= 0 then
			if baptize_part_info.slot_info[v.slot_id].is_buy ~= 1 then
				return true
			end
		end
	end

	-- 洗炼红点
	local baptize_remind = self:GetEquipBaptizeRemind(equip_body_index)
	if baptize_remind then
		return true
	end

	-- -- 洗炼阶数加成红点
	-- local grade_addition_remind = self:GetEquipBaptizeAddRemind(equip_part)
	-- if grade_addition_remind then
	-- 	return true
	-- end

	return false
end

-- 洗炼条件是否满足   
function EquipmentWGData:GetEquipBaptizeRemind(equip_body_index)
	local baptize_part_info = self:GetBaptizeInfoByEquipBodyIndex(equip_body_index)
	if IsEmptyTable(baptize_part_info) then
		return false
	end

	-- 最大洗炼等级
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local baptize_level_limit = EquipBodyWGData.Instance:GetBaptizeLevelLimit(equip_body_seq)
	if baptize_level_limit < baptize_part_info.grade then
		return false
	end

	local equip_data = EquipWGData.Instance:GetGridData(equip_body_index)
	if IsEmptyTable(equip_data) then
		return false 
	end

	-- 品质无法晋升不提示
	local _, item_color = ItemWGData.Instance:GetItemColor(equip_data.item_id)
	local upgrade_cfg = self.new_baptize_up_grade[baptize_part_info.grade]

	if IsEmptyTable(upgrade_cfg) then
		return false
	end

	if item_color < upgrade_cfg.next_need_quality then
		return false
	end

	-- 有已解锁的洗炼属性未达到红色，且道具足够或有免费次数
	local is_enough = self:GetEquipBaptizeItemIsEnough(equip_body_index)
	local has_free = self:GetEquipBaptizeFreeTimes() > 0

	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		local slot_info = baptize_part_info.slot_info[i]
		local quality = self:CalcEquipBaptizeQuality(slot_info.quality_value)
		if slot_info.is_buy == 1 and quality < GameEnum.ITEM_COLOR_RED and (has_free or is_enough) then
			return true
		end
	end

	-- 可晋升
	return self:GetEquipBaptizeCanUpgrade(equip_body_index)
end

-- 洗炼是否达到进阶条件  equip_part = 0 - 375
function EquipmentWGData:GetEquipBaptizeCanUpgrade(equip_body_index)
	local baptize_part_info = self:GetBaptizeInfoByEquipBodyIndex(equip_body_index)
	if IsEmptyTable(baptize_part_info) then
		return false
	end

	local upgrade_cfg = self.new_baptize_up_grade[baptize_part_info.grade]

	if IsEmptyTable(upgrade_cfg) then
		return false
	end

	return baptize_part_info.total_score >= upgrade_cfg.next_need_score
end

-- -- 洗炼阶数加成红点
-- function EquipmentWGData:GetEquipBaptizeAddRemind(equip_body_seq, equip_part)
-- 	if IsEmptyTable(self.baptize_add_act_remind_list) then return false end
-- 	local old_num = self.baptize_add_act_remind_list[equip_part]
-- 	if not old_num then return false end
	
-- 	local add_list = self:GetEquipBaptizeAttrAddList(equip_part)
-- 	if IsEmptyTable(add_list) then
-- 		return false
-- 	end
-- 	-- 是否有新的加成被激活
-- 	local act_num = 0
-- 	for i, v in ipairs(add_list) do
-- 		if v.is_act then
-- 			act_num = act_num + 1
-- 		end
-- 	end
	
-- 	return act_num > old_num
-- end

-- -- 部位红点
-- function EquipmentWGData:GetEquipBaptizeSingleRemind(equip_part)
-- 	equip_part = equip_part or 0
-- 	if equip_part >= GameEnum.EQUIP_INDEX_HUNJIE then return false end

-- 	local part_is_open = self:IsEquipBaptizePartOpen(equip_part)
-- 	if not part_is_open then return false end

-- 	local baptize_part = self:GetEquipBaptizePartInfo(equip_part)
-- 	if IsEmptyTable(baptize_part) then return false end

-- 	-- 第一个洗炼槽未解锁
-- 	local slot_remind = baptize_part.slot_info[0].is_buy ~= 1
-- 	if slot_remind then
-- 		return true
-- 	end

-- 	-- 按钮红点
-- 	local btn_remind = self:GetEquipBaptizeBtnRemind(equip_part)
-- 	if btn_remind then
-- 		return true
-- 	end

-- 	-- 洗炼阶数加成红点
-- 	local grade_addition_remind = self:GetEquipBaptizeAddRemind(equip_part)
-- 	if grade_addition_remind then
-- 		return true
-- 	end

-- 	return false
-- end

-- -- 洗炼按钮红点
-- function EquipmentWGData:GetEquipBaptizeBtnRemind(equip_part)
-- 	local baptize_part = self:GetEquipBaptizePartInfo(equip_part)
-- 	if IsEmptyTable(baptize_part) then return false end

-- 	local equip_data = EquipWGData.Instance:GetGridData(equip_part)
-- 	if IsEmptyTable(equip_data) then return false end

-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
-- 	local need_order = self:GetEquipBaptizeNeedOrder()
-- 	-- 洗炼装备阶级
-- 	if item_cfg.order < need_order then
-- 		return false
-- 	end

-- 	-- 有已解锁的洗炼属性未达到红色，且道具足够或有免费次数
-- 	local is_enough = self:GetEquipBaptizeItemIsEnough(equip_part)
-- 	local has_free = self:GetEquipBaptizeFreeTimes() > 0
-- 	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
-- 		local slot_info = baptize_part.slot_info[i]
-- 		local quality = self:CalcEquipBaptizeQuality(slot_info.quality_value)
-- 		if slot_info.is_buy == 1 and quality < GameEnum.ITEM_COLOR_RED and (has_free or is_enough) then
-- 			return true
-- 		end
-- 	end

-- 	-- 可晋升
-- 	return self:GetEquipBaptizeCanUpgrade(equip_part)
-- end
-------------------------------------------------REMIND_END--------------------------------------------

-------------------------------------------------CFG_GET_START-----------------------------------------
function EquipmentWGData:GetEquipBaptizePartOpenLevel(equip_seq, equip_part)
	return ((self.new_baptize_part_open[equip_seq] or {})[equip_part] or {}).role_level or 0
end

function EquipmentWGData:GetNewBaptizeSlotOpenDataList(equip_body_seq)
	return self.new_baptize_slot_open[equip_body_seq] or {}
end

-- 根据洗炼加成获得洗炼属性品质
function EquipmentWGData:CalcEquipBaptizeQuality(quality_value)
	quality_value = quality_value or 0
	
	for k, v in ipairs(self.new_baptize_weight) do
		if quality_value >= v.min_value and quality_value <= v.max_value then
			return v.quality_id
		end
	end

	return 0
end

-- 获取洗炼槽位解锁花费
function EquipmentWGData:GetEquipBaptizeSlotActiveCost(equip_body_seq, slot_id)
	return ((self.new_baptize_slot_open[equip_body_seq] or {})[slot_id] or {}).consume_gold or 0
end
--------------------------------------------------CFG_GET_END------------------------------------------

-------------------------------------------------CAL_START---------------------------------------------
-- 肉身装备 数据list
function EquipmentWGData:GetEquipBaptizeShowList(equip_body_seq)
	local data_list = EquipWGData.Instance:GetDataList()		--身上穿戴的装备列表
	local equip_data = {}

	if IsEmptyTable(data_list) then
		return equip_data
	end

	local index_offset = equip_body_seq * 15
	for index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		local data = data_list[index + index_offset]

		if not IsEmptyTable(data) then
			equip_data[index] = data
		end
	end

	return equip_data
end

-- 洗炼部位是否开启
function EquipmentWGData:IsEquipBaptizePartOpen(equip_body_index)
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	if not self:IsEquipBaptizeUnlockLevelLimit(equip_body_seq) then
		return false
	end
	
	local role_level = RoleWGData.Instance:GetAttr('level')
	local need_level = self:GetEquipBaptizePartOpenLevel(equip_body_seq, equip_part)
	return role_level >= need_level, need_level
end

-- 洗炼道具是否足够
function EquipmentWGData:GetEquipBaptizeItemIsEnough(equip_body_index)
	local cost_cfg = self:GetEquipBaptizeCostCfg(equip_body_index)
	local need_num = cost_cfg.consume_item_num
	local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.consume_item_id)
	return have_num >= need_num, have_num, need_num
end

-- 获取洗炼消耗
function EquipmentWGData:GetEquipBaptizeCostCfg(equip_body_index)
	local lock_num = self:GetEquipBaptizeAttrLockNum(equip_body_index)
	return self.new_baptize_consume[lock_num]
end

-- 获取当前部位上锁数
function EquipmentWGData:GetEquipBaptizeAttrLockNum(equip_body_index)
	local num = 0
	local slot_list = self:GetBaptizeSlotInfoByEquipBodyIndex(equip_body_index)

	if IsEmptyTable(slot_list) then
		return num 
	end

	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		if slot_list[i].is_lock == 1 then
			num = num + 1
		end
	end

	return num
end

-- 获取洗炼进阶条件
function EquipmentWGData:GetEquipBaptizeUpgradeCondition(equip_body_index)
	local baptize_part = self:GetBaptizeInfoByEquipBodyIndex(equip_body_index)
	if IsEmptyTable(baptize_part) then
		return
	end

	local upgrade_cfg = self.new_baptize_up_grade[baptize_part.grade]
	if IsEmptyTable(upgrade_cfg) then
		return
	end
	
	return upgrade_cfg.next_need_score, upgrade_cfg.next_need_quality
end

function EquipmentWGData:SetIsUseGuaranteeItem(is_use)
	self.baptize_is_use_guarantee = is_use
end

function EquipmentWGData:GetIsUseGuaranteeItem()
	return self.baptize_is_use_guarantee or false
end

-- 红橙品质属性未上锁
function EquipmentWGData:GetEquipBaptizeBatterNoLock(equip_body_index)
	local orange_no_lock = false
	local red_no_lock = false
	local slot_list = self:GetBaptizeSlotInfoByEquipBodyIndex(equip_body_index)
	if not slot_list then
		return orange_no_lock, red_no_lock
	end

	for k = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		local is_lock = slot_list[k].is_lock == 1
		local quality = slot_list[k].quality
		if not is_lock and quality == GameEnum.ITEM_COLOR_ORANGE then
			orange_no_lock = true
		elseif not is_lock and quality == GameEnum.ITEM_COLOR_RED then
			red_no_lock = true
		end
	end

	return orange_no_lock, red_no_lock
end

function EquipmentWGData:IsEquipBaptizeUnlockLevelLimit(equip_body_seq)
	local equip_baptize_level_limit = EquipBodyWGData.Instance:GetEquipBaptizeLevelLimit(equip_body_seq)
	return RoleWGData.Instance:GetAttr('level') >= equip_baptize_level_limit, equip_baptize_level_limit
end
--------------------------------------------------CAL_END----------------------------------------------

-- 初始化阶级加成红点数据
function EquipmentWGData:GetBaptizeAddRemind()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "EquipmentGradeRemind")
	local str = PlayerPrefsUtil.GetString(key)
	local str_tab = Split(str, "|")
	local table = {}
	for i = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		table[i] = str_tab[i + 1] and tonumber(str_tab[i + 1]) or 0
	end
	self.baptize_add_act_remind_list = table
end

-- 保存阶级加成红点数据
function EquipmentWGData:SaveBaptizeAddRemind()
	local str, splitStr = "", ''
	for i = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		splitStr = i ~= GameEnum.EQUIP_INDEX_XIANZHUO and "|" or ''
		str = str .. (self.baptize_add_act_remind_list[i] or 0) .. splitStr
	end
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "EquipmentGradeRemind")
	PlayerPrefsUtil.SetString(key, str)
end

-- 获取洗炼需要装备阶级
function EquipmentWGData:GetEquipBaptizeNeedOrder()
	return self.other_cfg[1].need_grade
end

-- 获得洗炼属性范围
function EquipmentWGData:GetEquipBaptizeAttrRange(attr_id, grade)
	local cfg = (self.new_baptize_word_grade[attr_id] or {})[grade]
	if cfg then
		return cfg.min_value, cfg.max_value 
	end
	return 0, 0
end

-- 当前装备部位洗炼数据
function EquipmentWGData:GetEquipBaptizePartInfo(equip_part)
	return self.baptize_list[equip_part] or {}
end

-- 洗炼属性上锁
function EquipmentWGData:GetEquipBaptizeLockState(equip_part, slot_id)
	slot_id = slot_id or 0
	return ((self:GetEquipBaptizePartInfo(equip_part).slot_info or {})[slot_id] or {}).is_lock == 1
end



-- 获取部位已解锁的属性槽位数量
function EquipmentWGData:GetEquipBaptizeSlotBuyNum(equip_part)
	local num = 0
	local slot_list = self:GetEquipBaptizePartInfo(equip_part).slot_info
	if not slot_list then return num end
	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		if slot_list[i].is_buy == 1 then
			num = num + 1
		end
	end
	return num
end

-- -- 洗炼装备列表
-- function EquipmentWGData:GetEquipBaptizeShowList(need_jump, view_index)
-- 	local data_list = EquipWGData.Instance:GetDataList()		--身上穿戴的装备列表
-- 	local equip_data = {}
-- 	local jump_equip_data = {}
-- 	if not data_list then
-- 		return equip_data
-- 	end
	
-- 	for index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
-- 		local data = data_list[index]
-- 		if data ~= nil then
-- 			data.sort_index = EquipmentWGData.GetSortIndex(index)
-- 			if need_jump then
-- 				table.insert(jump_equip_data, data)
-- 			end
-- 		end

-- 		equip_data[index] = data
-- 	end
	
-- 	if not need_jump then
-- 		return equip_data, view_index
-- 	end

-- 	if equip_data[view_index] then
-- 		local remind = self:GetEquipBaptizeSingleRemind(view_index)
-- 		if remind then
-- 			return equip_data, view_index
-- 		end
-- 	end

-- 	if not IsEmptyTable(jump_equip_data) then
-- 		table.sort(jump_equip_data, SortTools.KeyLowerSorter("sort_index"))
-- 	end

-- 	for k, v in ipairs(jump_equip_data) do
-- 		local remind = self:GetEquipBaptizeSingleRemind(v.index)
-- 		if remind then
-- 			return equip_data, v.index
-- 		end

-- 		if view_index == nil then
-- 			view_index = v.index
-- 		end
-- 	end

-- 	return equip_data, view_index
-- end


-- 保底洗炼道具是否足够
function EquipmentWGData:GetEquipBaptizeGuaranteeIsEnough(equip_part)
	local cost_cfg = self:GetEquipBaptizeCostCfg(equip_part)
	local need_num = cost_cfg.guarantee_item_num
	local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.guarantee_item_id)
	return have_num >= need_num, have_num, need_num
end

-- 当前洗炼属性加成列表
function EquipmentWGData:GetEquipBaptizeAttrAddList(equip_body_index)
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)

	local add_cfg = self.new_baptize_attr[equip_part]
	if IsEmptyTable(add_cfg) then
		return {}
	end

	local baptize_level_limit = EquipBodyWGData.Instance:GetBaptizeLevelLimit(equip_body_seq)
	local attr_list = {}

	local cfg_list = __TableCopy(add_cfg)
	local grade = self:GetBaptizeInfoByEquipBodyIndex(equip_body_index).grade

	for k, v in pairs(cfg_list) do
		if v.highest_grade <= baptize_level_limit then
			v.cur_grade = grade
			v.is_act = grade >= v.highest_grade
			table.insert(attr_list, v)
		end
	end

	-- local attr_list = __TableCopy(add_cfg)
	-- local grade = self:GetBaptizeInfoByEquipBodyIndex(equip_body_index).grade
	-- for k, v in pairs(attr_list) do
	-- 	v.cur_grade = grade
	-- 	v.is_act = grade >= v.highest_grade
	-- end
	return attr_list
end

-- 当前装备洗炼加成进度
function EquipmentWGData:GetEquipBaptizeAttrAddProgress(equip_body_index)
	local add_list = self:GetEquipBaptizeAttrAddList(equip_body_index)
	local progress = 0
	local desc = ""
	local need_desc = ""
	if IsEmptyTable(add_list) then
		return progress, desc, need_desc
	end

	local show_add_id
	for i = #add_list, 1, -1 do
		show_add_id = i
		if add_list[i].is_act or (add_list[i - 1] and add_list[i - 1].is_act) then
			break
		end
	end

	local add_info = add_list[show_add_id]
	progress = add_info.cur_grade / add_info.highest_grade
	local attr_str = ""
	local type = add_info.attr_type1
	local value = add_info.attr_val1
	if value and value > 0 then
		local name = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
		name = DeleteStrSpace(name)
		local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
		value = is_per and (value * 0.01 .. "%") or value
		attr_str = name .. "+" .. value
	end

	local need_desc = ""
	if add_info.is_act then
		need_desc = ToColorStr(Language.Equip.BaptizeOpen, COLOR3B.D_GREEN)
	else
		need_desc = string.format(Language.Equipment.BaptizeActDesc, add_info.cur_grade, add_info.highest_grade)
	end
	desc = attr_str

	return progress, desc, need_desc
end


-- 打开界面后记录
function EquipmentWGData:SetEquipBaptizeAddRemind(equip_part)
	local add_list = self:GetEquipBaptizeAttrAddList(equip_part)
	if IsEmptyTable(add_list) then return end
	
	local act_num = 0
	for i, v in ipairs(add_list) do
		if v.is_act then
			act_num = act_num + 1
		end
	end
	self.baptize_add_act_remind_list[equip_part] = act_num
end

-- 装备洗炼后 > 最大级别的晋升是属性拉满，不是升到下一阶级 后端的grade + 1 仅用于最大级别判断
function EquipmentWGData:GetBaptizeTargetGrade(equip_body_index)
	local target_grade = 0

	local baptize_part_info = self:GetBaptizeInfoByEquipBodyIndex(equip_body_index)
	if not baptize_part_info then
		return target_grade
	end

	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local baptize_level_limit = EquipBodyWGData.Instance:GetBaptizeLevelLimit(equip_body_seq)
	local is_max_grade = baptize_level_limit < baptize_part_info.grade
	target_grade = is_max_grade and baptize_level_limit or baptize_part_info.grade

	return target_grade
end


------------------------------------------------------------------ 分割线 ------------------------------------------------------------------

-- 根据槽位获得洗炼属性品质[废弃]
function EquipmentWGData:GetEquipBaptizeQuality(equip_part, attr_idx)
	local addition = self:GetEquipBaptizeAttrAddition(equip_part, attr_idx)
	local quility = self:GetEquipBaptizeQualityByAddition(addition)
	return quility
end

-- 当前装备洗炼属性显示数据[废弃]
function EquipmentWGData:GetEquipBaptizeAttrList(item_data)
	if IsEmptyTable(item_data) then
		return {}
	end

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if item_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return {}
	end

	local legend_attr_list = {}
	local equip_index = item_data.index
	local big_type = EquipWGData.GetEquipBigType(item_cfg.sub_type)

	if item_data.param and item_data.param.xianpin_type_list then
		local xianpin_list = item_data.param.xianpin_type_list
		local star_level = item_data.param.star_level or 0
		local mix_add, max_add = self:GetEquipBaptizeAttrRange()
		local show_value, min_value, max_value = 0, 0, 0

		local is_first_locked
		for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
			local data = {}
			local per_str = ""
			local addition = self:GetEquipBaptizeAttrAddition(equip_index, i)
			show_value, min_value, max_value = 0, 0, 0

			if xianpin_list[i + 1] then
				local attr_id = tonumber(xianpin_list[i + 1])
				local cfg_data = self:GetEquipRandAttrByType(item_cfg.color, big_type, attr_id)
				if cfg_data then
					local need_star = self:GetEquipBaptizeSlotActiveNeed(i)
					local attr_value = cfg_data["attr_val_" .. item_cfg.order] or 0
					local is_per = cfg_data.is_per == 1
					local per_desc = is_per and "%" or ""
					data.is_act = star_level >= need_star
					data.attr_id = attr_id
					min_value = attr_value
					max_value = attr_value * (max_add * 0.0001)
					data.mix_value = min_value .. per_desc
					data.max_value = max_value .. per_desc
					-- 策划需求：先算加成后值 取整 再算万分比属性 保留两位小数
					local to_ceil = math.ceil(attr_value * (addition * 0.0001))
					to_ceil = is_per and to_ceil * 0.01 or to_ceil
					per_str = is_per and "%" or per_str
					show_value = math.floor(to_ceil * 100)
					show_value = show_value * 0.01
					data.attr_desc = cfg_data.desc
					data.attr_value = show_value .. per_str
					data.sort_index = cfg_data.Attributes_sorting or 999
				end
			end

			local quality = self:GetEquipBaptizeQuality(equip_index, i)
			data.real_index = i
			data.sort_index = data.sort_index or (999 + i)
			data.attr_id = data.attr_id or 0
			data.is_act = data.is_act or false
			data.is_first_locked = false
			if not data.is_act and not is_first_locked then
				is_first_locked = true
				data.is_first_locked = true
			end
			data.quality = quality
			data.attr_desc = data.attr_desc or ""
			data.attr_value = data.attr_value or ""
			data.mix_value = data.mix_value or 0
			data.max_value = data.max_value or 0
			data.progress = (max_value - min_value) > 0 and (show_value - min_value) / (max_value - min_value) or 0
			--data.progress = (addition - mix_add) / (max_add - mix_add)
			data.progress = data.progress > 1 and 1 or data.progress
			data.progress = data.progress < 0 and 0 or data.progress
			data.attr_lock = self:GetEquipBaptizeLockState(equip_index, i)

			data.act_need = self:GetEquipBaptizeSlotActiveNeed(i)
			legend_attr_list[i + 1] = data
		end
	end

	if not IsEmptyTable(legend_attr_list) then
		table.sort(legend_attr_list, SortTools.KeyLowerSorter("sort_index"))
	end

	return legend_attr_list
end

-- 洗炼属性加成[废弃]
function EquipmentWGData:GetEquipBaptizeAttrAddition(equip_part, attr_idx)
	local addition = (self.baptize_info_list[equip_part] or {})[attr_idx] or 0
	addition = addition == 0 and 10000 or addition
	return addition
end

-- 根据洗炼加成获得洗炼属性品质[废弃]
function EquipmentWGData:GetEquipBaptizeQualityByAddition(addition)
	return 0
end

-- 洗炼属性加成列表[废弃][a3洗炼不再加成仙品属性]
function EquipmentWGData:GetEquipBaptizeAttrAdditionList(equip_part)
	local attr_add_list = {}
	for k = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		attr_add_list[k] = self:GetEquipBaptizeAttrAddition(equip_part, k)
	end

	return attr_add_list
end

-- 获取洗炼需要星级[废弃]
function EquipmentWGData:GetEquipBaptizeSlotActiveNeed(slot_id)
	return (self.new_baptize_slot_open[slot_id] or {}).need_star_level or 0
end

-- 记录当前优先消耗, 刷新红点用[废弃]
function EquipmentWGData:SetFirstCostRecord(equip_part, is_use)
	self.baptize_first_cost_equip_part = equip_part
	self.baptize_first_cost_state = is_use
end

-- 获取当前优先消耗[废弃]
function EquipmentWGData:GetFirstCostRecord()
	return self.baptize_first_cost_equip_part or -1, self.baptize_first_cost_state or false
end

--获取洗练系统所有装备的评分和
function EquipmentWGData:GetAllBaptizePingFen()
	local pingfen = 0

	return pingfen
end
