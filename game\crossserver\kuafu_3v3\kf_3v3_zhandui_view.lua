KF3V3ZhanDuiView = KF3V3ZhanDuiView or BaseClass(SafeBaseView)

local LogSpace = 23 --日志间距

-- 战队信息界面
function KF3V3ZhanDuiView:__init()
    self:SetMaskBg()
    self.is_safe_area_adapter = true
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
        { vector2 = Vector2(-6, -15), sizeDelta = Vector2(1078, 608) })
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "zhandui_info_render") --战队信息
end

function KF3V3ZhanDuiView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_zhandui_quit, BindTool.Bind1(self.OnClickZhanDuiQuit, self))
    XUI.AddClickEventListener(self.node_list.btn_zhandui_invite, BindTool.Bind1(self.OnClickZhanDuiInvite, self))
    XUI.AddClickEventListener(self.node_list.btn_zhandui_apply, BindTool.Bind1(self.OnClickZhanDuiApply, self))
    XUI.AddClickEventListener(self.node_list.btn_chang_name, BindTool.Bind1(self.OnClickZhanDuiChangeName, self))
    XUI.AddClickEventListener(self.node_list.btn_chang_notice, BindTool.Bind1(self.OnClickZhanDuiChangeNotice, self))
    XUI.AddClickEventListener(self.node_list.zhanling_img, BindTool.Bind1(self.OnClickZhanLing, self))
    XUI.AddClickEventListener(self.node_list.btn_zhandui_speak, BindTool.Bind1(self.OnClickZhanDuiSpeak, self))

    self.log_list = AsyncListView.New(ZhanDuiLogRender, self.node_list.log_list)
    self.log_list:SetCellSizeDel(BindTool.Bind(self.GetLogCellSizeDel, self))
    self.member_list = AsyncListView.New(ZhanDuiMemberInfoRender, self.node_list.member_list)

    self.zhan_dui_log_change_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Add_Log,
        BindTool.Bind(self.OnZhanDuiLogChange, self))  --日志改变
    self.zhan_dui_info_change_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Info_Change,
        BindTool.Bind(self.OnZhanDuiInfoChange, self)) --战队信息改变
    self.new_apply_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_New_Apply,
        BindTool.Bind(self.OnNewApplyCallBack, self))  --申请消息改变
    self.apply_view_close_event = GlobalEventSystem:Bind(OtherEventType.VIEW_CLOSE,
        BindTool.Bind(self.OnViewCloseHandler, self))

    self.world_talk_update_event = GlobalEventSystem:Bind(ZhanDuiWorldTalk.UPDATE_WORLD_TALK,
        BindTool.Bind(self.UpdateWordTalk, self))
    self.world_talk_complete_event = GlobalEventSystem:Bind(ZhanDuiWorldTalk.COMPLETE_WORLD_TALK,
        BindTool.Bind(self.ComleteWoldTalk, self)) --申请界面关闭
end

function KF3V3ZhanDuiView:ReleaseCallBack()
    if self.zhan_dui_log_change_event then
        GlobalEventSystem:UnBind(self.zhan_dui_log_change_event)
        self.zhan_dui_log_change_event = nil
    end
    if self.zhan_dui_info_change_event then
        GlobalEventSystem:UnBind(self.zhan_dui_info_change_event)
        self.zhan_dui_info_change_event = nil
    end
    if self.new_apply_event then
        GlobalEventSystem:UnBind(self.new_apply_event)
        self.new_apply_event = nil
    end
    if self.apply_view_close_event then
        GlobalEventSystem:UnBind(self.apply_view_close_event)
        self.apply_view_close_event = nil
    end
    if self.world_talk_complete_event then
        GlobalEventSystem:UnBind(self.world_talk_complete_event)
        self.world_talk_complete_event = nil
    end

    if self.world_talk_update_event then
        GlobalEventSystem:UnBind(self.world_talk_update_event)
        self.world_talk_update_event = nil
    end

    if self.log_list then
        self.log_list:DeleteMe()
        self.log_list = nil
    end
    if self.member_list then
        self.member_list:DeleteMe()
        self.member_list = nil
    end
    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
        self.role_head_cell = nil
    end
    if self.quit_alert then
        self.quit_alert:DeleteMe()
        self.quit_alert = nil
    end

    self.has_change_btn_gray = nil
end

function KF3V3ZhanDuiView:ClickCloseZhanDui()
    self:Open3V3Info()
end

function KF3V3ZhanDuiView:ShowIndexZhanDui()
    self.log_data_list = ZhanDuiWGData.Instance:GetLogList()
    self:Flush()
end

function KF3V3ZhanDuiView:OnFlush()
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    if not zhan_dui_info or IsEmptyTable(zhan_dui_info) then return end
    --战队名字text
    self:FlushZhanDuiName()
    --战队排名text
    --TODO
    --战队段位text
    self:FlushZhanDuiDuanWei()
    --公告text
    self:FlushZhanDuiNotice()
    --日志列表
    self:FlushLog()
    --成员列表
    self:FlushMemberList()
    --按钮显示隐藏逻辑
    self:FlushZhanDuiBtn()
    --刷新申请红点
    self:FlushApplyRemind()
    --战令
    self:FlushZhanZhanLing()
end

function KF3V3ZhanDuiView:GetLogCellSizeDel(data_index)
    local data = self.log_data_list[data_index + 1]
    --尝试去找缓存高度
    local record_height = ZhanDuiWGData.Instance:GetRecordLogHeight(data.log_id)
    if record_height then
        return record_height + LogSpace
    end
    self.node_list.log_content_template.text.text = data.content
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.log_content_template.rect)
    local hight = math.ceil(self.node_list.log_content_template.rect.rect.height)
    --缓存高度，下次直接取，会很快
    ZhanDuiWGData.Instance:RecordLogHeight(data.log_id, hight)
    return hight and hight + LogSpace or 48
end

function KF3V3ZhanDuiView:OnZhanDuiLogChange()
    self:FlushLog()
end

function KF3V3ZhanDuiView:OnZhanDuiInfoChange(notify_reason)
    if notify_reason ~= NotifyZhanduiInfoReason.LeaveZhandui then
        self:FlushMemberList()
        self:FlushZhanZhanLing()
    end
    if notify_reason == NotifyZhanduiInfoReason.ModifyName then
        self:FlushZhanDuiName()
    elseif notify_reason == NotifyZhanduiInfoReason.ModifyNotice then
        self:FlushZhanDuiNotice()
    elseif notify_reason == NotifyZhanduiInfoReason.ChangeCaptain then
        self:FlushZhanDuiBtn()
    elseif notify_reason == NotifyZhanduiInfoReason.ChangeLingpaiName then
        self:FlushZhanZhanLing()
    end
end

function KF3V3ZhanDuiView:OnNewApplyCallBack()
    self:FlushApplyRemind()
end

function KF3V3ZhanDuiView:OnViewCloseHandler(view)
    if view.view_name == GuideModuleName.ZhanDuiApplyView then
        self:FlushApplyRemind()
    end
end

--刷新申请红点
function KF3V3ZhanDuiView:FlushApplyRemind()
    self.node_list.apply_red_point:SetActive(ZhanDuiWGData.Instance:GetApplyCount() > 0)
end

--刷新日志
function KF3V3ZhanDuiView:FlushLog()
    self.log_data_list = ZhanDuiWGData.Instance:GetLogList()
    self.log_list:SetDataList(self.log_data_list)
end

--刷新成员
function KF3V3ZhanDuiView:FlushMemberList()
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    self.member_list:SetDataList(zhan_dui_info.member_list)
end

--刷新战队名
function KF3V3ZhanDuiView:FlushZhanDuiName()
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    self.node_list.zhandui_name.text.text = zhan_dui_info.name
end

--刷新公告
function KF3V3ZhanDuiView:FlushZhanDuiNotice()
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    self.node_list.gonggao_content.text.text = zhan_dui_info.notice
end

--刷新战队段位
function KF3V3ZhanDuiView:FlushZhanDuiDuanWei()
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local grade_cfg, next_score, is_max = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhan_dui_info.score)
    self.node_list.duanwei_right.text.text = grade_cfg.tier_name
end

--刷新按钮
function KF3V3ZhanDuiView:FlushZhanDuiBtn()
    local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
    self.node_list.btn_zhandui_quit:SetActive(is_in_zhandui)
    self.node_list.btn_zhandui_invite:SetActive(is_in_zhandui)
    local is_zhandui_captain = ZhanDuiWGData.Instance:GetIsZhanDuiCaptain()
    self.node_list.btn_zhandui_apply:SetActive(is_zhandui_captain)
    self.node_list.btn_zhandui_speak:SetActive(ZhanDuiWGData.Instance:GetZhanDuiMemberCount() <
        COMMON_CONSTS.ZHAN_DUI_MAX_MEMBER_COUNT)
end

--刷新战队战令
function KF3V3ZhanDuiView:FlushZhanZhanLing()
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local grade_cfg, next_score, is_max = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhan_dui_info.score)
    self.node_list.zhanling_text.text.text = zhan_dui_info.zhandui_lingpai_name
    self.node_list.zhanling_img.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. grade_cfg.grade))
    if ZhanDuiWGData.Instance:GetIsZhanDuiCaptain() then
        local is_creat = RoleWGData.GetRolePlayerPrefsInt("is_creat_zhandui")
        self.node_list.redpoint_img:SetActive(not is_creat or is_creat == 1)
    else
        self.node_list.redpoint_img:SetActive(false)
    end
end

--点击退出战队
function KF3V3ZhanDuiView:OnClickZhanDuiQuit()
    if KF3V3WGData.Instance:GetIsMatching() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MatchingCanNotDoThis2)
        return
    end
    local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
    if not is_in_zhandui then
        return
    end
    if not self.quit_alert then
        self.quit_alert = Alert.New()
    end
    self.quit_alert:SetLableString(Language.ZhanDui.QuitTeamAlertStr)
    self.quit_alert:SetCancelFunc(function()
    end)
    self.quit_alert:SetOkFunc(function()
        ZhanDuiWGCtrl.Instance:SendLeaveZhanDui()
        ZhanDuiWGCtrl.Instance:CloseZhanDuiView()
    end)
    self.quit_alert:Open()
end

--点击邀请入队
function KF3V3ZhanDuiView:OnClickZhanDuiInvite()
    if ZhanDuiWGData.Instance:GetZhanDuiMemberCount() >= COMMON_CONSTS.ZHAN_DUI_MAX_MEMBER_COUNT then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.CurMaxMember)
        return
    end
    ZhanDuiWGCtrl.Instance:OpenInviteView()
end

--点击申请列表
function KF3V3ZhanDuiView:OnClickZhanDuiApply()
    ZhanDuiWGCtrl.Instance:OpenApplyView()
end

--点击修改名字
function KF3V3ZhanDuiView:OnClickZhanDuiChangeName()
    if not ZhanDuiWGData.Instance:GetIsZhanDuiCaptain() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotIsZhanduiCaptainCantDo)
        return
    end
    ZhanDuiWGCtrl.Instance:OpenChangeNameView()
end

--点击修改公告
function KF3V3ZhanDuiView:OnClickZhanDuiChangeNotice()
    if not ZhanDuiWGData.Instance:GetIsZhanDuiCaptain() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotIsZhanduiCaptainCantDo)
        return
    end
    ZhanDuiWGCtrl.Instance:OpenChangeNoticeView()
end

--点击战令
function KF3V3ZhanDuiView:OnClickZhanLing()
    ZhanDuiWGCtrl.Instance:OpenZhanLingView()
    if ZhanDuiWGData.Instance:GetIsZhanDuiCaptain() then
        --判断红点是否开启
        self.node_list.redpoint_img:SetActive(false)
        RoleWGData.SetRolePlayerPrefsInt("is_creat_zhandui", 0)
    end
end

--点击喊话
function KF3V3ZhanDuiView:OnClickZhanDuiSpeak()
    ZhanDuiWGCtrl.Instance:OpenTalkView()
end

function KF3V3ZhanDuiView:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node, plat_type, server_id,
                                             plat_name)
    if self.role_head_cell == nil then
        self.role_head_cell = RoleHeadCell.New(false)
    end
    NewTeamWGCtrl.Instance:QueryTeamInfo(role_id, function(protocol)
        if self:IsOpen() and self:IsLoaded() then
            local role_info = {
                role_id = role_id,
                role_name = role_name,
                prof = prof,
                sex = sex,
                is_online = is_online,
                team_index = protocol.team_index,
                team_type = TEAM_INVITE_TYPE.CHAT,
                plat_type = plat_type,
            }
            if IS_ON_CROSSSERVER then
                local main_role = Scene.Instance:GetMainRole()
                local main_role_vo = main_role.vo
                role_info.plat_name = plat_name or main_role_vo.plat_name
                role_info.server_id = server_id or main_role_vo.merge_server_id
                self.role_head_cell:SetRoleInfo(role_info)
            else
                role_info.plat_name = plat_name
                role_info.server_id = server_id
                self.role_head_cell:SetRoleInfo(role_info)
            end
            self.role_head_cell:OpenMenu(node)
        end
    end)
end

function KF3V3ZhanDuiView:UpdateWordTalk(time)
    if self.node_list.zhandui_speak_text then
        self.node_list.zhandui_speak_text.text.text = string.format(Language.NewTeam.WorldTalk5, time)
    end

    if not self.has_change_btn_gray then
        self.has_change_btn_gray = true
        XUI.SetGraphicGrey(self.node_list.btn_zhandui_speak, true)
        --self.node_list.btn_zhandui_speak.image:LoadSprite(ResPath.GetF2CommonIcon("zd_hh"))
    end
end

function KF3V3ZhanDuiView:ComleteWoldTalk()
    if CountDownManager.Instance:HasCountDown("zhandui_world_talk") then
        CountDownManager.Instance:RemoveCountDown("zhandui_world_talk")
    end
    if self.node_list.zhandui_speak_text then
        self.node_list.zhandui_speak_text.text.text = "招募"
    end
    self.has_change_btn_gray = nil
    XUI.SetGraphicGrey(self.node_list.btn_zhandui_speak, false)
    --self.node_list.btn_zhandui_speak.image:LoadSprite(ResPath.GetF2CommonIcon("zd_hanhua"))
end

--------------------renders----------------------
--日志
ZhanDuiLogRender = ZhanDuiLogRender or BaseClass(BaseRender)
function ZhanDuiLogRender:OnFlush()
    local color = "#A7D2EBFF"
    self.node_list.time.text.text = TimeUtil.FormatYMDHM(self.data.timestamp)
    EmojiTextUtil.ParseRichText(self.node_list.content.emoji_text, self.data.content, 20, color, nil, nil,
        RICH_CONTENT_TYPE.ParseZhanDuiRoleColor, nil)
end

--成员
ZhanDuiMemberInfoRender = ZhanDuiMemberInfoRender or BaseClass(BaseRender)
function ZhanDuiMemberInfoRender:__init()
    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClickCell, self))
end

function ZhanDuiMemberInfoRender:OnFlush()
    self.node_list.name.text.text = self.data.name
    self.node_list.vip_level:SetActive(self.data.vip_level >= 1)
    local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
    self.node_list.vip_level.text.text = is_hide_vip and "V" or "V" .. self.data.vip_level

    local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
    self.node_list.dianfen_img:SetActive(is_vis)
    if is_vis then
        self.node_list.level.text.text = level
    else
        self.node_list.level.text.text = string.format(Language.Common.LevelNormal, level)
    end
    local score = self.data.cross3v3_score or 0
    self.node_list["score"].text.text = score
    self.node_list.capability.text.text = self.data.capability
    self.node_list.season.text.text = string.format(Language.ZhanDui.SeasonChangCi, self.data.season_win_times,
        self.data.season_match_time)
    -- local is_danshu = self.index % 2 ~= 0
    -- self.node_list.bg:SetActive(is_danshu)
    if self.data.last_cross3v3_pk_time == 0 then
        self.node_list.last.text.text = Language.KuafuPVP.Wu
    else
        local second = math.floor(TimeWGCtrl.Instance:GetServerTime() - self.data.last_cross3v3_pk_time)
        --local time_tab = os.date("*t", self.data.last_cross3v3_pk_time)
        --print_error("上次PK时间", self.data.last_cross3v3_pk_time, second)
        if second >= 86400 * 30 then
            self.node_list.last.text.text = Language.KuafuPVP.OnMonthAgo
        elseif second >= 86400 then
            self.node_list.last.text.text = string.format(Language.KuafuPVP.DayAgoFormat, math.floor(second / 86400))
        elseif second < 86400 and second >= 3600 then
            self.node_list.last.text.text = string.format(Language.KuafuPVP.HourAgoFormat, math.floor(second / 3600))
        elseif second < 3600 and second >= 60 then
            self.node_list.last.text.text = string.format(Language.KuafuPVP.MinAgoFormat, math.floor(second / 60))
        elseif second < 60 and second >= 1 then
            self.node_list.last.text.text = string.format(Language.KuafuPVP.SecAgoFormat, second)
        end
    end
end

function ZhanDuiMemberInfoRender:OnClickCell()
    if self.data.uid == RoleWGData.Instance:InCrossGetOriginUid() then
        return
    end
    local items, callback_param = self:GetItems()
    local role_data = {}
    role_data.role_id = self.data.uid
    UiInstanceMgr.Instance:OpenCustomMenu(items, self:GetPos(self.node_list.btn_click),
        BindTool.Bind1(self.OnClickMenuCallback, self), callback_param, nil, nil, nil, role_data)
end

function ZhanDuiMemberInfoRender:GetItems()
    local items = { Language.KuafuPVP.MenuBrouse }
    local callback_param = {}
    local index = 1
    if nil == SocietyWGData.Instance:FindFriend(self.data.uid) and not UserVo.IsCrossServer(self.data.uid) then
        table.insert(items, Language.KuafuPVP.MenuAddFriend)
        callback_param.has_add_friend = true
        index = index + 1
        callback_param.add_friend_index = index
    end

    --组队邀请


    if ZhanDuiWGData.Instance:GetIsZhanDuiCaptain() then
        table.insert(items, Language.KuafuPVP.MenuTurnLeader)
        table.insert(items, Language.KuafuPVP.MenuQuitTeam)
        callback_param.is_leader = true
        index = index + 1
        callback_param.turn_leader_index = index
        index = index + 1
        callback_param.quit_team_index = index
    end
    return items, callback_param
end

function ZhanDuiMemberInfoRender:OnClickMenuCallback(index, sender, callback_param)
    if 1 == index then                                                                     --查看信息
        BrowseWGCtrl.Instance:OpenWithUid(self.data.uid)
    elseif callback_param.has_add_friend and callback_param.add_friend_index == index then --加为好友
        SocietyWGCtrl.Instance:IAddFriend(self.data.uid)
    elseif callback_param.is_leader and callback_param.turn_leader_index == index then     --转移队长
        ZhanDuiWGCtrl.Instance:SendZhanDuiChangeCaptain(self.data.uid)
    elseif callback_param.is_leader and callback_param.quit_team_index == index then       --请离队伍
        ZhanDuiWGCtrl.Instance:SendKickMember(self.data.uid)
    elseif callback_param.invite_team_index == index then
        BrowseWGCtrl.Instance:AllReqRoleInfo(self.data.uid, nil, function(protocol)
            if protocol.is_online == 1 or protocol.is_online == 3 then
                if 0 == SocietyWGData.Instance:GetIsInTeam() then
                    local team_type = 0
                    local fb_mode = 1
                    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType() --获取服务器最高世界等级
                    local min_level, max_level = COMMON_CONSTS.NoalGoalLimitMinLevel, top_user_level
                    NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
                end
                --NewTeamWGCtrl.Instance:SendCreateTeam()
                NewTeamWGCtrl.Instance:Open()
                if self.is_inteam_index > 0 then
                    SocietyWGCtrl.Instance:SendReqJoinTeam(self.is_inteam_index)
                else
                    SocietyWGCtrl.Instance:ITeamInvite(protocol.role_id, self.team_type)
                end
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
            end
        end)
    end
end

function ZhanDuiMemberInfoRender:GetPos(node)
    if nil == node then return nil end
    local parent_rect = self.view.gameObject.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
    local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect,
        screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x, y)
end

------------------------------------------
