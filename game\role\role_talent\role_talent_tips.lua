RoleTalentTips = RoleTalentTips or BaseClass(SafeBaseView)

function RoleTalentTips:__init()
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_talent_tips")
	self:SetMaskBg(true)
end

function RoleTalentTips:__delete()

end

function RoleTalentTips:LoadCallBack()
	self.node_list.subtract_btn.button:AddClickListener(BindTool.Bind(self.OnClickSubtractBtn,self))
	self.node_list.add_btn.button:AddClickListener(BindTool.Bind(self.OnClickAddBtn,self))
	self.node_list.confirm_btn.button:AddClickListener(BindTool.Bind(self.OnClickConfirmBtn,self))
end

-- 切换标签调用
function RoleTalentTips:ShowIndexCallBack()
	-- if self.data.talent_id < 300 then
	-- 	self.node_list.obj.transform.localPosition = Vector3(375, 318.15, 0)
	-- else
		self.node_list.obj.transform.localPosition = Vector3(self.data.tips_x, self.data.tips_y, 0)
	-- end
	self:Flush()
end

function RoleTalentTips:SetData(data)
	self.data = data
	if self.data then
		self.temp_level = self.data.level
	end
end

function RoleTalentTips:ReleaseCallBack()

end

function RoleTalentTips:OnFlush()
	if not self.data then return end
	local talent_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(self.data.talent_id, (self.temp_level == 0 and self.data.max_level or self.temp_level))
	local color
	self.node_list.skill_icon.image:LoadSprite(ResPath.GetTalentSkill(self.data.icon))
	self.node_list.skill_name.text.text = self.data.name
	self.node_list.base_skill_desc.text.text = talent_cfg.desc
	if self.temp_level < self.data.max_level then
		local level = self.temp_level == 0 and 1 or (self.temp_level + 1)
		self.node_list.other_text_1.text.text = self.temp_level == 0 and Language.RoleTalent.TalentSkillTop[3] or Language.RoleTalent.TalentSkillTop[1]
		local next_talent_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(self.data.talent_id, level)
		self.node_list.next_skill_desc.text.text = next_talent_cfg.desc
		self.node_list.next_attr:SetActive(true)
		color = COLOR3B.WHITE
	else
		color = COLOR3B.YELLOW
		self.node_list.next_attr:SetActive(false)
	end
	self.node_list.skill_level.text.text = ToColorStr(self.temp_level .."/".. self.data.max_level, color)
	local need_count = RoleWGData.Instance:GetNeedTalentCount(self.data.talent_id, self.data.level, self.temp_level - self.data.level)
	local is_condition, limit_desc = RoleWGData.Instance:GetTalentIsSatisfyPreconditions(self.data)
	local talent_point = RoleWGData.Instance:GetRoleTalentPoint()
	local color = talent_point >= need_count and COLOR3B.D_GREEN or COLOR3B.RED
	self.node_list.need_num_text.text.text = ToColorStr(need_count,color).."/"..talent_point --string.format(Language.RoleTalent.UpLevelNeedCount, ToColorStr(need_count,color))
	self.node_list.bottom_obj:SetActive(not (self.data.level >= self.data.max_level) and is_condition)
	self.node_list.limit_desc:SetActive(not is_condition and self.data.level < self.data.max_level)
	self.node_list.limit_desc_text.text.text = limit_desc
	XUI.SetButtonEnabled(self.node_list.subtract_btn, self.temp_level > self.data.level)
	XUI.SetButtonEnabled(self.node_list.add_btn,self.temp_level < self.data.max_level )--and need_count <= talent_point
	XUI.SetButtonEnabled(self.node_list.confirm_btn, need_count <= talent_point)

	local single_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(self.data.talent_id,1) or {}
	local single_need_count = single_cfg.xiaohao or 0
	self.node_list.desc.text.text = string.format(Language.RoleTalent.UpLevelNeedCount2, single_need_count)
end

-- 关闭前调用
function RoleTalentTips:CloseCallBack()
	-- override
end

function RoleTalentTips:OnClickSubtractBtn()
	if not self.data then
		return
	end

	local level = self.data.level
	if self.temp_level <= level then
		return
	end

	self.temp_level = self.temp_level - 1
	self:Flush()
end

function RoleTalentTips:OnClickAddBtn()
	if not self.data then
		return
	end

	local need_count = RoleWGData.Instance:GetNeedTalentCount(self.data.talent_id, self.data.level, self.temp_level - self.data.level)
	local talent_point = RoleWGData.Instance:GetRoleTalentPoint()
	if need_count >= talent_point then
		TipWGCtrl.Instance:ShowSystemMsg(Language.RoleTalent.ErrorTip2)
		return
	end
	if self.temp_level >= self.data.max_level then
		return
	end

	self.temp_level = self.temp_level + 1

	-- if self.data then
	-- 	local talent_point = RoleWGData.Instance:GetRoleTalentPoint()
	-- 	local need_count = RoleWGData.Instance:GetNeedTalentCount(self.data.talent_id, self.data.level, self.temp_level - self.data.level)
	-- 	if need_count > talent_point then
	-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.RoleTalent.TalentNotEmougth)
	-- 		return
	-- 	end
	-- end
	self:Flush()
end

function RoleTalentTips:OnClickConfirmBtn()
	if not self.data then
		return
	end

	RoleWGCtrl.Instance:SendRoleTelentOperate(ROLE_TALENT_OPERATE_TYPE.ROLE_TALENT_OPERATE_TYPE_UPLEVEL, self.data.talent_id, self.temp_level)
	self:Close()
end

function RoleTalentTips:FlushData()
	local data = RoleWGData.Instance:SetRoleTalentLevelByid(self.data.talent_type, self.data.talent_id)
	if not data then
		self:Close()
	else
		self.data = data
		self.temp_level = self.data.level == 0 and 1 or self.data.level
		self:Flush()
	end
end