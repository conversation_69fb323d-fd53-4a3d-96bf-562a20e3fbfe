return {
	["smashed_egg_rank_reward_default_table"]={
		reward_item={[0]={item_id=22771,num=1,is_bind=1,},},row_rank=1,type=2,high_rank=1,},
	["happy_smashed_egg_reward_default_table"]={opengame_day=9999,index=1,
		reward_item={item_id=26345,num=1,is_bind=1,},},
	["charm_val_cfg"]={
		{},
		{send_add_charm_val=9,be_send_add_charm_val=5,send_flower_num=9,flower_id=26121,},
		{send_add_charm_val=99,be_send_add_charm_val=50,send_flower_num=99,flower_id=26122,},
		{send_add_charm_val=1314,be_send_add_charm_val=657,send_flower_num=999,flower_id=26123,},},
	["smashed_egg_rank_reward"]={
		{},
		{reward_item={[0]={item_id=22771,num=1,is_bind=1,},},row_rank=2,high_rank=2,},
		{reward_item={[0]={item_id=22771,num=1,is_bind=1,},},row_rank=3,high_rank=3,},},
	["charm_val_cfg_default_table"]={send_add_charm_val=1,be_send_add_charm_val=1,send_flower_num=1,flower_id=26120,},
	["consume_rank_show"]={
		{join_reward_item={item_id=29028,num=1,is_bind=1,},},
		{person_reward_item={item_id=29039,num=1,is_bind=1,},need_total_consume=40000,show_ranking=2,},
		{person_reward_item={item_id=29040,num=1,is_bind=1,},join_reward_item={item_id=29030,num=1,is_bind=1,},need_total_consume=30000,show_ranking=3,},
		{person_reward_item={item_id=29041,num=1,is_bind=1,},join_reward_item={item_id=29031,num=1,is_bind=1,},need_total_consume=20000,show_ranking="4-5",},
		{person_reward_item={item_id=29042,num=1,is_bind=1,},join_reward_item={item_id=29032,num=1,is_bind=1,},need_total_consume=18000,show_ranking="6-10",},},
	["consume_rank_show_default_table"]={
		person_reward_item={item_id=29038,num=1,is_bind=1,},
		join_reward_item={item_id=29029,num=1,is_bind=1,},need_total_consume=50000,show_ranking=1,},
	["flower_rank_reward"]={
		{female_reward_item={[0]={item_id=29332,num=1,is_bind=1,},},male_reward_item={[0]={item_id=29325,num=1,is_bind=1,},},},
		{female_reward_item={[0]={item_id=29333,num=1,is_bind=1,},},row_rank=2,high_rank=2,},
		{female_reward_item={[0]={item_id=29334,num=1,is_bind=1,},},row_rank=3,male_reward_item={[0]={item_id=29327,num=1,is_bind=1,},},high_rank=3,},
		{female_reward_item={[0]={item_id=29335,num=1,is_bind=1,},},row_rank=5,male_reward_item={[0]={item_id=29328,num=1,is_bind=1,},},high_rank=4,},
		{female_reward_item={[0]={item_id=29336,num=1,is_bind=1,},},row_rank=10,male_reward_item={[0]={item_id=29329,num=1,is_bind=1,},},high_rank=6,},
		{row_rank=20,male_reward_item={[0]={item_id=29330,num=1,is_bind=1,},},high_rank=11,},
		{female_reward_item={[0]={item_id=29338,num=1,is_bind=1,},},row_rank=100,male_reward_item={[0]={item_id=29331,num=1,is_bind=1,},},high_rank=21,},},
	["consume_rank_default_table"]={
		join_reward_item={item_id=29028,num=1,is_bind=1,},need_total_consume=18000,
		person_reward_item={item_id=29039,num=1,is_bind=1,},need_person_consume=0,rank=1,},
	["chongzhi_rank_show_default_table"]={need_total_chongzhi=50000,
		join_reward_item={item_id=29025,num=1,is_bind=1,},
		person_reward_item={item_id=29033,num=1,is_bind=1,},show_ranking=1,},
	["smashed_egg_times_reward"]={
		{reward_item={[0]={item_id=26345,num=1,is_bind=1,},},seq=0,times=10,},
		{reward_item={[0]={item_id=26366,num=1,is_bind=1,},[1]={item_id=26427,num=1,is_bind=1,},},},
		{seq=2,times=30,},
		{reward_item={[0]={item_id=27465,num=1,is_bind=1,},[2]={item_id=26365,num=2,is_bind=1,},[1]={item_id=26366,num=1,is_bind=1,},},seq=3,times=160,},
		{reward_item={[0]={item_id=26235,num=1,is_bind=1,},[2]={item_id=26360,num=5,is_bind=1,},[1]={item_id=26357,num=2,is_bind=1,},},seq=4,times=500,},},
	["chongzhi_rank_show"]={
		{join_reward_item={item_id=29023,num=1,is_bind=1,},},
		{need_total_chongzhi=40000,join_reward_item={item_id=29024,num=1,is_bind=1,},person_reward_item={item_id=29034,num=1,is_bind=1,},show_ranking=2,},
		{need_total_chongzhi=30000,person_reward_item={item_id=29035,num=1,is_bind=1,},show_ranking=3,},
		{need_total_chongzhi=20000,join_reward_item={item_id=29026,num=1,is_bind=1,},person_reward_item={item_id=29036,num=1,is_bind=1,},show_ranking="4-5",},
		{need_total_chongzhi=18000,join_reward_item={item_id=29027,num=1,is_bind=1,},person_reward_item={item_id=29037,num=1,is_bind=1,},show_ranking="6-10",},},
	["dingji_wedding"]={
		{wedding_reward_item={[0]={item_id=22000,num=1,is_bind=1,},},},},
	["flower_rank_reward_default_table"]={
		female_reward_item={[0]={item_id=29337,num=1,is_bind=1,},},row_rank=1,
		male_reward_item={[0]={item_id=29326,num=1,is_bind=1,},},high_rank=1,},
	["consume_rank"]={
		{need_total_consume=50000,person_reward_item={item_id=29038,num=1,is_bind=1,},},
		{join_reward_item={item_id=29029,num=1,is_bind=1,},need_total_consume=40000,rank=2,},
		{join_reward_item={item_id=29030,num=1,is_bind=1,},need_total_consume=30000,person_reward_item={item_id=29040,num=1,is_bind=1,},rank=3,},
		{join_reward_item={item_id=29031,num=1,is_bind=1,},need_total_consume=20000,person_reward_item={item_id=29041,num=1,is_bind=1,},rank=4,},
		{join_reward_item={item_id=29031,num=1,is_bind=1,},need_total_consume=20000,person_reward_item={item_id=29041,num=1,is_bind=1,},rank=5,},
		{join_reward_item={item_id=29032,num=1,is_bind=1,},person_reward_item={item_id=29042,num=1,is_bind=1,},rank=6,},
		{join_reward_item={item_id=29032,num=1,is_bind=1,},person_reward_item={item_id=29042,num=1,is_bind=1,},rank=7,},
		{join_reward_item={item_id=29032,num=1,is_bind=1,},person_reward_item={item_id=29042,num=1,is_bind=1,},rank=8,},
		{join_reward_item={item_id=29032,num=1,is_bind=1,},person_reward_item={item_id=29042,num=1,is_bind=1,},rank=9,},
		{join_reward_item={item_id=29032,num=1,is_bind=1,},person_reward_item={item_id=29042,num=1,is_bind=1,},rank=10,},},
	["other"]={
		{smashed_egg_end_flush_need_s=2,chongzhi_rank_need_min_num=10000,flower_rank_min_charm_val=1314,happy_smashed_egg_need_gold_once=30,consume_rank_need_min_num=10000,},},
	["activity_info"]={
		{name="跨服充值排行",type=4000,},
		{name="跨服消费排行",type=4002,},},
	["happy_smashed_egg_reward"]={
		{index=0,reward_item={item_id=26350,num=5,is_bind=1,},},
		{reward_item={item_id=28090,num=1,is_bind=1,},},
		{index=2,reward_item={item_id=28057,num=1,is_bind=1,},},
		{index=3,reward_item={item_id=26347,num=5,is_bind=1,},},
		{index=4,},
		{index=5,reward_item={item_id=26341,num=1,is_bind=1,},},
		{index=6,reward_item={item_id=27464,num=1,is_bind=1,},},
		{index=7,reward_item={item_id=22576,num=1,is_bind=1,},},
		{index=8,reward_item={item_id=26367,num=1,is_bind=1,},},
		{index=9,reward_item={item_id=26368,num=1,is_bind=1,},},},
	["chongzhi_rank_default_table"]={need_person_chongzhi=0,
		join_reward_item={item_id=29027,num=1,is_bind=1,},need_total_chongzhi=18000,
		person_reward_item={item_id=29035,num=1,is_bind=1,},rank=1,},
	["chongzhi_rank"]={
		{join_reward_item={item_id=29023,num=1,is_bind=1,},need_total_chongzhi=50000,person_reward_item={item_id=29033,num=1,is_bind=1,},},
		{join_reward_item={item_id=29024,num=1,is_bind=1,},need_total_chongzhi=40000,person_reward_item={item_id=29034,num=1,is_bind=1,},rank=2,},
		{join_reward_item={item_id=29025,num=1,is_bind=1,},need_total_chongzhi=30000,rank=3,},
		{join_reward_item={item_id=29026,num=1,is_bind=1,},need_total_chongzhi=20000,person_reward_item={item_id=29036,num=1,is_bind=1,},rank=4,},
		{join_reward_item={item_id=29026,num=1,is_bind=1,},need_total_chongzhi=20000,person_reward_item={item_id=29036,num=1,is_bind=1,},rank=5,},
		{person_reward_item={item_id=29037,num=1,is_bind=1,},rank=6,},
		{join_reward_item={item_id=29027,num=1,is_bind=1,},person_reward_item={item_id=29037,num=1,is_bind=1,},rank=7,},
		{join_reward_item={item_id=29027,num=1,is_bind=1,},person_reward_item={item_id=29037,num=1,is_bind=1,},rank=8,},
		{join_reward_item={item_id=29027,num=1,is_bind=1,},person_reward_item={item_id=29037,num=1,is_bind=1,},rank=9,},
		{join_reward_item={item_id=29027,num=1,is_bind=1,},person_reward_item={item_id=29037,num=1,is_bind=1,},rank=10,},},
	["smashed_egg_times_reward_default_table"]={opengame_day=99999,
		reward_item={[0]={item_id=22764,num=1,is_bind=1,},[2]={item_id=26368,num=1,is_bind=1,},[1]={item_id=22013,num=1,is_bind=1,},},seq=1,times=20,},
}
