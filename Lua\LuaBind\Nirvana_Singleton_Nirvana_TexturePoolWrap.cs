﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_Singleton_Nirvana_TexturePoolWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.Singleton<Nirvana.TexturePool>), typeof(System.Object), "Singleton_Nirvana_TexturePool");
		<PERSON><PERSON>unction("New", _CreateNirvana_Singleton_Nirvana_TexturePool);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.Reg<PERSON>ar("Instance", get_Instance, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_Singleton_Nirvana_TexturePool(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.Singleton<Nirvana.TexturePool> obj = new Nirvana.Singleton<Nirvana.TexturePool>();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.Singleton<Nirvana.TexturePool>.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.PushSealed(L, Nirvana.Singleton<Nirvana.TexturePool>.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

