BodyResetTipsView = BodyResetTipsView or BaseClass(SafeBaseView)
function BodyResetTipsView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(598, 420)})
    self:AddViewResource(0, "uis/view/dujie_body_ui_prefab", "layout_body_reset_tips_view")
end

function BodyResetTipsView:LoadCallBack()
	

    XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind(self.OnClickBtnCancel, self))
	XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind(self.OnClickBtnOK, self))

    if not self.cost_item_cell then
		self.cost_item_cell = ItemCell.New(self.node_list.cell_node)
        -- self.reward_item_list:SetStartZeroIndex(true)
	end


end

function BodyResetTipsView:ReleaseCallBack()

    if self.cost_item_cell then
		self.cost_item_cell:DeleteMe()
		self.cost_item_cell = nil
	end
end

function BodyResetTipsView:CloseCallBack()
    self.is_reach = nil
end

function BodyResetTipsView:OnFlush()
    if not self.data then
        return 
    end

    self.node_list["title_view_name"].text.text = self.data.title_view_name
    self.node_list["text_tips"].tmp.text = self.data.desc


    self.cost_item_cell:SetData(self.data.cost_item)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item.item_id)
    local color = item_num >= self.data.cost_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
    local up_level_str = item_num .. "/" .. self.data.cost_item.num

    self.cost_item_cell:SetRightBottomColorText(ToColorStr(up_level_str, color))
    self.cost_item_cell:SetRightBottomTextVisible(true)

    self.is_reach = item_num >= self.data.cost_item.num
end



function BodyResetTipsView:SetData(data)
    self.data = data
end

function BodyResetTipsView:OnClickBtnCancel()
    self:Close()
end


function BodyResetTipsView:OnClickBtnOK()
    if not self.is_reach then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)

    else
        DujieWGCtrl.Instance:TalentReset(self.data.bodu_seq)
    end
    
    self:Close()
end

