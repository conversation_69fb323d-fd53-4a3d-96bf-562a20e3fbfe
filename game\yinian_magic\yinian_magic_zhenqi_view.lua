local LEVEL_DELT_TIME = 0.5 --0.5s升级一次

YinianMagicZhenQitView = YinianMagicZhenQitView or BaseClass(SafeBaseView)
function YinianMagicZhenQitView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/yinianmagic_prefab", "layout_magic_bg")
	self:AddViewResource(0, "uis/view/yinianmagic_prefab", "laout_magic_zhenqi_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function YinianMagicZhenQitView:LoadCallBack()
    if self.attr_list == nil then
        self.attr_list = {}
        local node_num = self.node_list["zhenqi_attr_list"].transform.childCount
        for i = 1, node_num do
            self.attr_list[i] = CommonAddAttrRender.New(self.node_list["zhenqi_attr_list"]:FindObj("attr_" .. i))
        end
    end

	self.skill_item_list = {}
    for i = 1, 4 do
        self.skill_item_list[i] = YinianMagicSkillRender.New(self.node_list["zhenqi_skill_" .. i])
        self.skill_item_list[i]:SetIndex(i)
    end

	if not self.model_display then
        self.model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.model_display:SetRenderTexUI3DModel(display_data)
        -- self.model_display:SetUI3DModel(self.node_list["display"].transform, self.node_list.EventTriggerListener.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.model_display)
	end

	XUI.AddClickEventListener(self.node_list["up_grade_btn"], BindTool.Bind(self.AutoUpGrade, self)) --自动升级
	XUI.AddClickEventListener(self.node_list["ug_btn_left"], BindTool.Bind(self.OnClickUGModelShow, self, -1))
    XUI.AddClickEventListener(self.node_list["ug_btn_right"], BindTool.Bind(self.OnClickUGModelShow, self, 1))
	XUI.AddClickEventListener(self.node_list["use_btn"], BindTool.Bind(self.OnClickUse, self))
	XUI.AddClickEventListener(self.node_list["reset_btn"], BindTool.Bind(self.OnClickRsest, self))
	self.node_list.title_view_name.text.text = Language.YinianMagicView.RiYueXiuLianMagicTile
	
	self.node_list.mo_skelet:SetActive(false)
	self.node_list.s_skelet:SetActive(false)

	self.is_auto_grade = false
	self.cur_show_model_type = nil
end

function YinianMagicZhenQitView:ReleaseCallBack()
    if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end

	if self.skill_item_list then
        for k,v in pairs(self.skill_item_list) do
            v:DeleteMe()
        end
        self.skill_item_list = nil
    end

	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

	self.is_auto_grade = false
	self.cur_show_model_type = nil
	self.no_jump_grade = nil
end

function YinianMagicZhenQitView:CloseCallBack()
	if self:IsAutoUpGrade() then
		self:StopGradeOperator()
	end
end

function YinianMagicZhenQitView:OnFlush()
	self:FlushRightInfoPanel()
	local flush_model = false
	local cur_grade = YinianMagicWGData.Instance:GetCurGrade()
	local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
	local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade)
	if IsEmptyTable(cur_cfg) then
		return
	end
	local model_type = cur_cfg.modle_type
	if self.cur_show_model_type ~= model_type and not self.no_jump_grade then
		flush_model = true
		self.cur_show_model_type = model_type
	end

	self:FlushModel(flush_model)

	self.node_list["big_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ynsm_js_bg_" .. select_type))
	self.node_list["yuan_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ynsm_js_yuan_" .. select_type))

	local effct_str = select_type == 1 and "UI_ryxh_r4_yuan" or "UI_ryxh_y4_yuan"
    local asset, bundle = ResPath.GetA2Effect(effct_str)
    self.node_list.bg_effect:ChangeAsset(asset, bundle)
end

function YinianMagicZhenQitView:FlushRightInfoPanel()
	local cur_grade = YinianMagicWGData.Instance:GetCurGrade()
	local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
	self.node_list.zhenqi_level.text.text = string.format(Language.YinianMagicView.Jie, cur_grade)
	self:FlushAtrrInfo(select_type, cur_grade)

	-- 技能
	local skill_list = YinianMagicWGData.Instance:GetSkillList(select_type)
	for k,v in pairs(skill_list) do
		if self.skill_item_list[k] then
			self.skill_item_list[k]:SetData(v)
		end
	end

	local cur_bless = YinianMagicWGData.Instance:GetCurBless()
	local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade)
	local next_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade + 1)
	local need_bless = cur_cfg and cur_cfg.need_bless or 0
	local color = cur_bless >= need_bless and COLOR3B.D_GREEN or COLOR3B.D_RED
	local up_level_str = cur_bless .. "/" .. need_bless
	self.node_list.bless_need_comsume.text.text = ToColorStr(up_level_str, color)
	self.node_list.xiulianzhi_bar_ctrl.slider.value = cur_bless / need_bless

	local cap = YinianMagicWGData.Instance:GetCapability()
	self.node_list.cap_value.text.text = cap
	self.node_list.up_grade_red:SetActive(not IsEmptyTable(next_cfg) and cur_bless >= need_bless)

	self.node_list.can_up_jie:SetActive(not IsEmptyTable(next_cfg))
	self.node_list.max_jie:SetActive(IsEmptyTable(next_cfg))
end

function YinianMagicZhenQitView:FlushModel(flush_model)
	if self.cur_show_model_type == nil then
		return
	end

	local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
	local cur_grade = YinianMagicWGData.Instance:GetCurGrade()
	local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade)
	local show_model_type_cfg = YinianMagicWGData.Instance:GetModelTypeCfg(select_type,  self.cur_show_model_type)
	local appe_image_id = show_model_type_cfg.appe_image_id

	local max_model_type = YinianMagicWGData.Instance:GetMaxModelType(select_type)
	self.node_list["ug_btn_left"]:CustomSetActive(self.cur_show_model_type > 1)
    self.node_list["ug_btn_right"]:CustomSetActive(self.cur_show_model_type < cur_cfg.modle_type + 1 and self.cur_show_model_type < max_model_type)
	self.node_list["ug_no_act_flag"]:CustomSetActive(self.cur_show_model_type > cur_cfg.modle_type)
	self.node_list.no_act_str.text.text = string.format(Language.YinianMagicView.JieActive, show_model_type_cfg.min_grade)
	local use_garde = YinianMagicWGData.Instance:GetCurUseGrade()

	local cur_use_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, use_garde)
	local cur_use_model_type = cur_use_cfg and cur_use_cfg.modle_type or 0
	self.node_list["use_btn"]:CustomSetActive(self.cur_show_model_type ~= cur_use_model_type and self.cur_show_model_type <= cur_cfg.modle_type)
	self.node_list["reset_btn"]:CustomSetActive(self.cur_show_model_type == cur_use_model_type and self.cur_show_model_type <= cur_cfg.modle_type)

	if self.no_jump_grade then
        self.no_jump_grade = nil
        return
    end

	local special_status_table = {ignore_god_or_demon = true}
	if flush_model then
		self.model_display:RemoveAllModel()
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		self.model_display:SetModelResInfo(role_vo, special_status_table, function()
			self.model_display:PlayRoleAction()
		end)
		self.model_display:FixToOrthographic(self.root_node_transform)
	end

	self.model_display:SetGodOrDemonResid(appe_image_id)
end

-- 使用
function YinianMagicZhenQitView:OnClickUse()
    if not self.cur_show_model_type then
        return
    end

	local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
	local show_model_type_cfg = YinianMagicWGData.Instance:GetModelTypeCfg(select_type,  self.cur_show_model_type)
	if not show_model_type_cfg then
		return
	end

	YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.USE_GRADE, show_model_type_cfg.min_grade or 1)
	self.no_jump_grade = true
	self:PlayUseEffect()
end

function YinianMagicZhenQitView:OnClickRsest()
    if not self.cur_show_model_type then
        return
    end

	YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.USE_GRADE, -1)
end

-- 方向键点击
function YinianMagicZhenQitView:OnClickUGModelShow(dir)
    if not self.cur_show_model_type then
        return
    end

	local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
    local max_model_type = YinianMagicWGData.Instance:GetMaxModelType(select_type)
    local model_type = self.cur_show_model_type + dir
    model_type = math.min(model_type, max_model_type)
    model_type = math.max(1, model_type)
    if model_type ~= self.cur_show_model_type then
        self.cur_show_model_type = model_type
        self:FlushModel(true)
    end
end

function YinianMagicZhenQitView:FlushAtrrInfo(select_type, cur_grade)
	local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade)
	local next_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade + 1)
	if IsEmptyTable(cur_cfg) then
		return
	end

	local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, next_cfg, "attr_id", "attr_value", 1, 5)
	for k,v in pairs(self.attr_list) do
        v:SetData(attr_list[k])
    end
end

function YinianMagicZhenQitView:OnBtnUpGrade(one_key, is_auto)
	local cur_grade = YinianMagicWGData.Instance:GetCurGrade()
	local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
	local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade)
	local next_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, cur_grade + 1)

	if cur_cfg and next_cfg then
		local cur_bless = YinianMagicWGData.Instance:GetCurBless()
		if cur_bless >= cur_cfg.need_bless then
			YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.UP_GRADE)
			if is_auto then
				self:SetGradeButtonEnabled(true)
			end
		else
			self:StopGradeOperator()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.YinianMagicView.BlessNotEnough)
		end
	else
		self:StopGradeOperator()
	end
end

function YinianMagicZhenQitView:AutoUpGrade()
	--正在自动升阶则取消强化
	if self:IsAutoUpGrade() then 
		self:StopGradeOperator()
		return
	end

	self:OnBtnUpGrade(1, true)
end

--取消自动升阶倒计时
function YinianMagicZhenQitView:CancelAutoGradeTimer( )
	if nil ~= self.auto_grade_timer_quest then
		GlobalTimerQuest:CancelQuest(self.auto_grade_timer_quest)
		self.auto_grade_timer_quest = nil
	end
end

-- 自动升阶操作
function YinianMagicZhenQitView:AutoUpGradeUpOnce()
	self:CancelAutoGradeTimer()

	if self:IsAutoUpGrade() then
		self.auto_grade_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnBtnUpGrade, self, 1, true), LEVEL_DELT_TIME)
	end
end

function YinianMagicZhenQitView:IsAutoUpGrade()
	return self.is_auto_grade
end

function YinianMagicZhenQitView:StopGradeOperator()
	self:SetGradeButtonEnabled(false)
end

-- 设置强化按钮是否可用
function YinianMagicZhenQitView:SetGradeButtonEnabled(enabled)
	self.is_auto_grade = enabled
	self:SetAutoGradeBtnNameStr(not enabled and Language.YinianMagicView.AutoUpGrade or Language.YinianMagicView.UpGradeBtnStop)
	if false == enabled then
		self:CancelAutoGradeTimer()
	end
end

function YinianMagicZhenQitView:SetAutoGradeBtnNameStr(strength_btn_str)
	if self.node_list["btn_text"] then
		self.node_list["btn_text"].text.text = strength_btn_str
	end
end

function YinianMagicZhenQitView:PlayEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["effect_pos"]})
end

function YinianMagicZhenQitView:PlayUseEffect()
	local bundle_name, asset_name = ResPath.GetEffect(Ui_Effect.UI_huanhua)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos"].transform, nil, Vector2(0, 0), Quaternion.Euler(0, 0, 0))
end

--------------------技能YinianMagicSkillRender-----------
YinianMagicSkillRender = YinianMagicSkillRender or BaseClass(BaseRender)
function YinianMagicSkillRender:__init()
	self:AddClickEventListener(BindTool.Bind(self.OnClickSkill, self))
end

function YinianMagicSkillRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

	local bundel, asset = ResPath.GetSkillIconById(self.data.skill_icon)
    self.node_list["icon"].image:LoadSpriteAsync(bundel, asset, function ()
		self.node_list["icon"].image:SetNativeSize()
	end)

	local cur_grade = YinianMagicWGData.Instance:GetCurGrade()
	XUI.SetGraphicGrey(self.node_list["icon"], self.data.need_grade > cur_grade)
end

function YinianMagicSkillRender:OnClickSkill()
    if IsEmptyTable(self.data) then
        return
    end

	local cur_grade = YinianMagicWGData.Instance:GetCurGrade()
	local is_act = cur_grade >= self.data.need_grade
	local cur_skill_desc = self.data.skill_describe or ""
	local limit_text
	local skill_level
	if not is_act then
		limit_text = string.format(Language.YinianMagicView.SkillActNeed, self.data.need_grade)
	end

	skill_level = is_act and ToColorStr(Language.YinianMagicView.SkillActive, COLOR3B.D_GREEN) or ToColorStr(Language.YinianMagicView.HasNotActive, COLOR3B.D_RED)
	local capability = YinianMagicWGData.Instance:GetSkillCapability(self.data.type, self.data.skill_id)
	local show_data = {
		icon = self.data.skill_icon,
		top_text = self.data.skill_name,					-- 技能名
		top_text_color = COLOR3B.D_GREEN,
		skill_level = skill_level,
		body_text = cur_skill_desc,							-- 当前等级技能描述
		limit_text = limit_text,
		capability = capability,
		x = 0,
		y = 0,
		set_pos2 = true,
		show_level = true,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end