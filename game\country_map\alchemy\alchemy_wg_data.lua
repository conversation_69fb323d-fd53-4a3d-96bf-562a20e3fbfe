AlchemyWGData = AlchemyWGData or BaseClass()
AlchemyWGData.MAX_ALCHEMY_CELL_COUNT = 6 -- 丹灶最大数量
AlchemyWGData.MAX_FURNACE_COUNT = 3 -- 丹灶最大数量


function AlchemyWGData:__init()
	if AlchemyWGData.Instance then
		error("[AlchemyWGData] Attempt to create singleton twice!")
		return
	end

	AlchemyWGData.Instance = self

    -- 【配置】
    local cfg = ConfigManager.Instance:GetAutoConfig("alchemy_cfg_auto")
    --self.other_cfg = cfg.other[1]

    --丹药
    self.pellet_info_cfg = cfg.pellet
    self.pellet_cfg = ListToMap(cfg.pellet, "seq", "type")
    self.pellet_type_cfg = ListToMapList(cfg.pellet, "big_type")
    self.pellet_cfg_id = ListToMap(cfg.pellet, "cost_item_id")

    --炼丹
    self.danzao_cfg = ListToMap(cfg.danzao, "seq")
    self.furnace_cfg = ListToMap(cfg.furnace, "seq", "level")
    self.normal_array_cfg = ListToMap(cfg.normal_array, "level")
    self.compose_cfg = ListToMapList(cfg.compos, "compose_type")
    self.compose_seq_cfg = ListToMap(cfg.compos, "seq")
    self.compose_stuff_cfg = ListToMap(cfg.stuff, "stuff_id")
    self.compos_show_rare_cfg = ListToMap(cfg.compos_show_rare, "compose_type")
    self.compos_expedite_cfg = cfg.compos_expedite
    self.compos_expedite_item_cfg = ListToMap(cfg.compos_expedite, "item_id")
    -- 初始化
    self.pellet_info_list = {}
    self.furnace_level_flag_list = {}
    self.normal_array_level_flag = {}
    self.danzao_list = {}
    -- 【红点】
    RemindManager.Instance:Register(RemindName.CountryMapActAlchemy, BindTool.Bind(self.ShowCountryAlchemyRemind, self))
    RemindManager.Instance:Register(RemindName.Role_LianDan, BindTool.Bind(self.GetPelletUpRemind, self)) --丹药
end

function AlchemyWGData:__delete()
     -- 红点
    RemindManager.Instance:UnRegister(RemindName.CountryMapActAlchemy)
	RemindManager.Instance:UnRegister(RemindName.Role_LianDan)

    AlchemyWGData.Instance = nil
end

------------------协议数据------------------------
--炼丹全部数据
function AlchemyWGData:SetAllAlchemyInfo(protocol)
    self.pellet_info_list = protocol.pellet_info_list
    self.furnace_level_flag_list = protocol.furnace_level_flag_list
    self.normal_array_level_flag = protocol.normal_array_level_flag
    self.danzao_list = protocol.danzao_list
end

-- 单个丹药信息改变
function AlchemyWGData:SetSinglePelletInfo(protocol)
    local seq = protocol.seq
    local level = protocol.level
    if self.pellet_info_list[seq] then
        self.pellet_info_list[seq] = level
    end
end

-- 单个炼丹炉改变
function AlchemyWGData:SetSingleFurnaceInfo(protocol)
    local seq = protocol.seq
    local level_flag = protocol.level_flag
    if self.furnace_level_flag_list[seq] then
        self.furnace_level_flag_list[seq] = level_flag
    end
end

-- 单个法阵改变
function AlchemyWGData:SetSingleNormalArrayInfo(protocol)
    self.normal_array_level_flag = protocol.normal_array_level_flag
end

-- 单个丹灶改变
function AlchemyWGData:SetSingleDanzaoInfo(protocol)
    local seq = protocol.seq
    if self.danzao_list[seq] then
        self.danzao_list[seq] = protocol.change_data
    end
end

--丹药配置
function AlchemyWGData:GetPelletCfgGroup()
    return self.pellet_type_cfg
end

--丹药操作[旧]
function AlchemyWGData:GetPelletCfg(index)
    local cfg_list = {}
    if index == 1 then
        for i = 6, 0, -1 do
            table.insert(cfg_list, self:GetPelletLevelCfg(i))
        end
    else
        for i = 13, 7, -1 do
            table.insert(cfg_list, self:GetPelletLevelCfg(i))
        end
    end
    return cfg_list
end

--丹药阶段配置
function AlchemyWGData:GetPelletLevelCfg(type)
    local data_list = {}
    for k, v in pairs(self.pellet_cfg) do
        if v[type] then
            local v_level = self:GetPelletItemLevel(v[type].seq)
            local data = {
                cost_item_id = v[type].cost_item_id,
                type = v[type].type,
                attr_id1 = v[type].attr_id1,
                attr_id2 = v[type].attr_id2,
                attr_id3 = v[type].attr_id3,
                attr_id4 = v[type].attr_id4,
                attr_id5 = v[type].attr_id5,
                attr_value1 = v[type].attr_value1,
                attr_value2 = v[type].attr_value2,
                attr_value3 = v[type].attr_value3,
                attr_value4 = v[type].attr_value4,
                attr_value5 = v[type].attr_value5,
                level_limit = v[type].level_limit,
                seq = v[type].seq,
                level = v_level,
            }
            table.insert(data_list, data)
        end
    end
    return data_list
end

--丹药单个升级信息
function AlchemyWGData:GetPelletItemLevel(seq)
    return self.pellet_info_list[seq] or 0
end

--丹药配置信息
function AlchemyWGData:GetPelletInfo(item_id)
    for k, v in pairs(self.pellet_info_cfg) do
        if item_id ~= nil and item_id == v.cost_item_id then
            return v
        end
    end

    return nil
end

--获得丹药属性
function AlchemyWGData:GetPelletUpLevelCfg(item)
    local attr_list = {}
    if item == nil then
        return attr_list
    end

    local em_data = EquipmentWGData.Instance
    local max_attr_num = 5
    local attr_id, attr_value = 0, 0
    for i = 1, max_attr_num do
        attr_id = item["attr_id" .. i]
        attr_value = item["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            --print_error(attr_str, attr_value)
            local level = self:GetPelletItemLevel(item.seq)
            local data = {}
		    data.attr_str = attr_str
		    data.attr_value = attr_value * level
            if level < item.level_limit then
                data.add_value = attr_value
            else
                data.add_value = 0
            end
		    data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
		    table.insert(attr_list, data)
        end
    end

    return attr_list
end

-- 总战力计算
function AlchemyWGData:GetPelletTotalCapability()
    local attr_list = {}
    local attr_id, attr_value = 0, 0
    for i, v in ipairs(self.pellet_info_cfg) do
        local level = self:GetPelletItemLevel(v.seq)
        if level > 0 then
            for j = 1, 5 do
                attr_id = v["attr_id" .. j]
                attr_value = v["attr_value" .. j]
                if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                    local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
                    if attr_list[attr_str] ~= nil then
                        attr_list[attr_str] = attr_list[attr_str] + level * attr_value
                    else
                        attr_list[attr_str] = level * attr_value
                    end
                end
            end
        end
    end
    local attribute = AttributePool.AllocAttribute()
    for k, v in pairs(attr_list) do
       attribute[k] = attribute[k] + v
    end
    return AttributeMgr.GetCapability(attribute)
end

--丹药红点
function AlchemyWGData:GetPelletCellRemind(item)
    if item == nil then
        return false
    end
    
    local level = self:GetPelletItemLevel(item.seq)
    local has_num = ItemWGData.Instance:GetItemNumInBagById(item.cost_item_id)
    if has_num < 1 or level >= item.level_limit then
        return false
    end

    return true
end

function AlchemyWGData:GetPelletUpRemind()
    local is_remind = 0
    local danzao_info = self:GetDanzaoAllInfo()
    for k, v in pairs(self.pellet_info_cfg) do
        local is_up = self:GetPelletCellRemind(v)
        if is_up then
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REFINING_DAN, 1, function()
				ViewManager.Instance:Open(GuideModuleName.RoleView,TabIndex.role_refining)
			end)
            return 1
        end
    end
    for y, q in pairs(danzao_info) do
        if q.is_unlock ~= 0 and q.compos_seq >= 0 then
            local grow_time = self:GetGrowTimeComposeBySeq(q.compos_seq, q.start_compos_time)
            if grow_time <= 0 then
                is_remind = 1
                return is_remind
            end
        end
    end

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REFINING_DAN, 0, function()
		ViewManager.Instance:Open(GuideModuleName.RoleView,TabIndex.role_refining)
	end)
    return 0
end

--某个大类是否红点
function AlchemyWGData:GetPelletUpBigTypeRemind(big_type)
    local cur_cfg = self.pellet_type_cfg[big_type]
    if not cur_cfg then
        return false
    end
    for i, v in ipairs(cur_cfg) do
        local is_up = self:GetPelletCellRemind(v)
        if is_up then
            return true
        end
    end
    return false
end

--[废弃]
function AlchemyWGData:GetPelletUpRemindByBigType()
    local remind_1 = 0
    local remind_2 = 0
    for k, v in pairs(self.pellet_info_cfg) do
        local is_up = self:GetPelletCellRemind(v)
        if is_up then
            if v.big_type == 1 then
                remind_1 = 1
            else
                remind_2 = 1
            end
        end
    end

    return remind_1, remind_2
end

---获取第一个有红点的丹药
---@return integer big_type 大类型
---@return integer index 位置index
function AlchemyWGData:GetJumpPelletRemindIndex()
    for big_type, cfg_list in ipairs(self.pellet_type_cfg) do
        for index, cfg in ipairs(cfg_list) do
            local is_up = self:GetPelletCellRemind(cfg)
            if is_up then
                return big_type, index
            end
        end
    end
    return 0, 0
end

-- 获取丹药的大类型和index
function AlchemyWGData:GetPelletTypeIndexByItemId(item_id)
    for big_type, cfg_list in ipairs(self.pellet_type_cfg) do
        for index, cfg in ipairs(cfg_list) do
            if cfg.cost_item_id == item_id then
                return big_type, index
            end
        end
    end
    return 1, 1
end

-- function AlchemyWGData:GetJumpPelletRemindIndex(item_id, index)
--     local group_index = 1
--     local item_index = 1
--     local flag = 1 
--     if self:GetPelletInfo(item_id) then
--         flag = self:GetPelletInfo(item_id).big_type
--     elseif index then
--         flag = index
--     else
--         local remind_1, remind_2 = self:GetPelletUpRemindByBigType()
--         if remind_1 == 1 then
--             flag = 1
--         else
--             if remind_2 == 1 then
--                 flag = 2
--             end
--         end
--     end
--     local cfg = self:GetPelletCfg(flag)
--     if cfg == nil then
--         return group_index, item_index
--     end
--     for k, v in pairs(cfg) do
--         for k1, v1 in pairs(v) do
--             if item_id then
--                 if tonumber(item_id) == v1.cost_item_id then
--                     group_index = k
--                     item_index = k1
--                     return group_index, item_index
--                 end
--             else
--                 local is_up = self:GetPelletCellRemind(v1)
--                 if is_up then
--                     group_index = k
--                     item_index = k1
--                     return group_index, item_index
--                 end
--             end
        
--         end
--     end
--     return group_index, item_index
-- end
--炼丹操作
function AlchemyWGData:GetDanzaoAllInfo()
    return self.danzao_list
end

function AlchemyWGData:GetDanzaoTimeInfo(seq)
    return self.danzao_list[seq] or {}
end

function AlchemyWGData:GetDanzaoInfobySeq(seq)
    return self.danzao_cfg[seq] or {}
end

-- 根据配方类型返回合成列表
function AlchemyWGData:GetComposeCfgByType(compose_type)
    return self.compose_cfg[compose_type] or {}
end

function AlchemyWGData:GetComposeCfgSeq(seq)
    return self.compose_seq_cfg[seq] or {}
end

function AlchemyWGData:GetShowComposeCfgByType(compose_type)
    return self.compos_show_rare_cfg[compose_type] or {}
end

function AlchemyWGData:GetAllShowComposeCfg()
    return self.compos_show_rare_cfg
end

function AlchemyWGData:GetExpediteCfg()
    return self.compos_expedite_cfg
end

function AlchemyWGData:GetStuffCfg(stuff_id)
    return self.compose_stuff_cfg[stuff_id] ~= nil
end

function AlchemyWGData:GetExpediteItemCfg(item_id)
    return self.compos_expedite_item_cfg[item_id] ~= nil
end

-- 获取丹药是否可激活
function AlchemyWGData:GetEIItemIdCanAct(cost_item_id)
    if not IsEmptyTable(self.pellet_cfg_id) then
        return cost_item_id ~= nil
    end
end

function AlchemyWGData:SetCurDanzaoSeq(seq)
    self.cur_danzao_seq = seq
end

function AlchemyWGData:GetCurDanzaoSeq(seq)
   return self.cur_danzao_seq or 0
end

-- 获取炼丹减免时间
function AlchemyWGData:GetReduceTime()
    local reduce_time = 0
    local level = 0
    local cfg
    for i = 0, AlchemyWGData.MAX_FURNACE_COUNT - 1 do
        level = self:GetFurnaceLevelBySeq(i)
        if level ~= 0 then
            cfg = self:GetFurnaceCfgBySeqAndLevel(i, level) 
            reduce_time = reduce_time + (cfg.reduce_time_scale / 10000)
        end
    end

    return reduce_time
end

--获得法阵等级
function AlchemyWGData:GetNormalArrayLevel()
    local level = 0
    for k, v in ipairs(self.normal_array_level_flag) do
        if v == 1 then
            level = k
        else
            break
        end
    end

    return level
end

-- 根据等级取法阵配置
function AlchemyWGData:GetNormalArrayCfgByLevel(level)
    return self.normal_array_cfg[level] or {}
end

-- 获得法阵最高等级
function AlchemyWGData:GetNormalArrayCfgMaxLevel()
    return #self.normal_array_cfg
end

-- 根据seq取丹炉等级
function AlchemyWGData:GetFurnaceLevelBySeq(seq)
    local level = 0
    if self.furnace_level_flag_list[seq] then
        for k, v in ipairs(self.furnace_level_flag_list[seq]) do
            if v == 1 then
                level = k
            else
                break
            end
        end
    end

    return level
end

-- 根据seq和等级取丹炉配置
function AlchemyWGData:GetFurnaceCfgBySeqAndLevel(seq, level)
    return self.furnace_cfg[seq] and (self.furnace_cfg[seq][level] or {}) or {}
end

-- 获得丹炉最高等级
function AlchemyWGData:GetFurnaceCfgMaxLevel(seq)
    return self.furnace_cfg[seq] and #self.furnace_cfg[seq] or 0
end

-- 获取当前丹炉属性
function AlchemyWGData:GetCurFurnaceAttrList(seq, level)
    local attr_list = {}
    local level_is_zero = level == 0 -- 是否是0级
    local attr_show_level = level_is_zero and level + 1 or level  -- 0级拿1级的属性展示下
    local cfg = self:GetFurnaceCfgBySeqAndLevel(seq, attr_show_level)
    if not cfg then
        return attr_list
    end

    local em_data = EquipmentWGData.Instance
    local attr_id, attr_value = 0, 0
    local max_attr_num = 4
    for i = 1, max_attr_num do
        attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
                attr_str = attr_str,
                attr_value = level_is_zero and 0 or attr_value,
                add_value = 0,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }

            table.insert(attr_list, data)
        end
    end

    if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

    return attr_list
end

-- 战力计算
function AlchemyWGData:GetFurnaceCapability(seq, level)
    local attr_list = self:GetCurFurnaceAttrList(seq, level) -- 属性
    local attribute = AttributePool.AllocAttribute()
    for i, v in ipairs(attr_list) do
       attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
    end

    return AttributeMgr.GetCapability(attribute)
end

--获取合成剩余时间
function AlchemyWGData:GetGrowTimeComposeBySeq(seq, start_compos_time)
    local grow_time = 0
    local compos_cfg = self:GetComposeCfgSeq(seq)
    if IsEmptyTable(compos_cfg) then
        print_error("拿不到配置合成seq=",seq)
        return grow_time
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local cfg_need_time = compos_cfg.need_time
    local reduce_time = AlchemyWGData.Instance:GetReduceTime()
    local need_time = cfg_need_time * (1 - reduce_time)
    local compose_end_time = start_compos_time + need_time
    grow_time = compose_end_time - server_time
    return grow_time
end

-----------------------红点-----------------
function AlchemyWGData:ShowCountryAlchemyRemind()
    local is_remind = 0
    local danzao_info = self:GetDanzaoAllInfo()
    for k, v in pairs(danzao_info) do
        if v.is_unlock ~= 0 and v.compos_seq >= 0 then
            local grow_time = self:GetGrowTimeComposeBySeq(v.compos_seq, v.start_compos_time)
            if grow_time <= 0 then
                is_remind = 1
                break
            end
        end
    end

    return is_remind
end