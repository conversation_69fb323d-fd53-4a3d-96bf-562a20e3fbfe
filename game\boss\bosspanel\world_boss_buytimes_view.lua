
WorldBossBuyTimesView = WorldBossBuyTimesView or BaseClass(DayCountChangeView)

function WorldBossBuyTimesView:__init()
    self:SetMaskBg()
    self.view_name = "WorldBossBuyTimesView"
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_vip_times_1")
    self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function WorldBossBuyTimesView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
    if self.vip_change_event then
        GlobalEventSystem:UnBind(self.vip_change_event)
        self.vip_change_event = nil
    end
    if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function WorldBossBuyTimesView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
    local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(SceneType.WorldBoss)
    if item_id ==  cfg.consume_item then
        self:FlushItemNum()
    end
end

function WorldBossBuyTimesView:LoadCallBack()
    DayCountChangeView.LoadCallBack(self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.node_list["title_view_name"].text.text = Language.ViewName.CiShuZengJia

	self.item_cell = ItemCell.New(self.node_list.item_cell)

    --Vip信息改变
    self.vip_change_event = GlobalEventSystem:Bind(OtherEventType.VIP_INFO_CHANGE, BindTool.Bind(self.OnVipInfoChange, self))
    XUI.AddClickEventListener(self.node_list["btn_item_add"], BindTool.Bind(self.OnClickItemAdd, self))
    XUI.AddClickEventListener(self.node_list["btn_buy_count"], BindTool.Bind(self.OnClickBuyCount, self))
	XUI.AddClickEventListener(self.node_list["btn_xufei"], BindTool.Bind(self.OnClickXuFei, self))

	self.node_list.btn_item_add:CustomSetActive(true)
	self.node_list.btn_cancel:CustomSetActive(false)
	self.node_list.text_buy.tmp.text = Language.Boss.Buy
	
end

--fb_type 副本类型
function WorldBossBuyTimesView:SetData(vip_type)
    self.data = {}
	self.data.vip_type = vip_type
	self.scene_type = SceneType.WorldBoss
	if vip_type == VIP_LEVEL_AUTH_TYPE.MAMHUANG_ENTER_TIMES then
		self.scene_type = SceneType.KF_BOSS
	end

    self:Open()
end

function WorldBossBuyTimesView:OnVipInfoChange()
    self:Flush()
end

function WorldBossBuyTimesView:FlushItemNum()
    local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(self.scene_type)
    local item_id = cfg.consume_item
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    local str =  ToColorStr(item_num, item_num > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED)
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    self.node_list.item_num_des.tmp.text = string.format(Language.Boss.WorldBossTimesAddItem, ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]), str)

	self.item_cell:SetData({item_id = item_id})
end

function WorldBossBuyTimesView:OnFlush()
    self:FlushItemNum()
    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local vip_buy_cfg = VipWGData.Instance:GetVipSpecPermissions(self.data.vip_type)
	local now_count = VipPower.Instance:GetParam(self.data.vip_type)
	local buy_times = BossWGData.Instance:GetWorldBossBuyTimes()
	if self.scene_type == SceneType.KF_BOSS then
		buy_times = BossWGData.Instance:GetCrossBossBuyTime()
	end

	local next_vip, next_count = VipPower.Instance:GetNextBigParam(self.data.vip_type ,now_count)
	local remain_count = vip_buy_cfg["param_" .. role_vip] - buy_times
	local color = remain_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
   	local des = string.format(Language.Boss.WorldBossBuyTips, color, now_count - buy_times, now_count)
	local comsume_gold = self:GetNeedGold(buy_times + 1)
	if comsume_gold then
        local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
        local cost_text = Language.Common.GoldText

        if bind_gold_num >= comsume_gold then
            cost_text = Language.Common.BindGoldText
        end 
		local comsume_str = string.format(Language.Boss.WorldBossBuyNum, cost_text, comsume_gold)
		if self.scene_type == SceneType.KF_BOSS then
			comsume_str = string.format(Language.Boss.ManHuangBossBuyNum, cost_text, comsume_gold)
		end
		self.node_list["rich_buy_desc"].tmp.text = comsume_str
	end
	self.node_list["rich_buy_des_1"].tmp.text = des
	local max_vip = VipWGData.Instance:GetMaxVIPLevel()
	if role_vip < max_vip and vip_buy_cfg["param_" .. role_vip + 1] then
		if vip_buy_cfg["param_" .. role_vip + 1] > vip_buy_cfg["param_" .. role_vip] then
			des = string.format(Language.Boss.WoldBossBuyDes, vip_buy_cfg["param_" .. role_vip + 1])
		else
			des = string.format(Language.Boss.WoldBossBuyDes, next_count)
		end
	else
		des = Language.Boss.WorldBuyLimit
	end
	if role_vip >= next_vip then
		des = Language.Boss.WorldBuyLimit
	end
	self.node_list["rich_buy_des_2"].tmp.text = des
	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].tmp.text = "V"..role_vip
		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..next_vip
			self.node_list["layout_next_vip"]:SetActive(true)
		else
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..role_vip
			self.node_list["layout_next_vip"]:SetActive(false)
		end
	end
	local is_vip = VipWGData.Instance:IsVip()
	self.node_list.btn_xufei:SetActive(not is_vip)
	--self.node_list.btn_item_add:SetActive(is_vip)
	self.node_list.btn_buy_count:SetActive(is_vip)
end

function WorldBossBuyTimesView:OnClickItemAdd()
	local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(self.scene_type)
    if cfg ~= nil then
        local item_id = cfg.consume_item
        local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
        if item_num <= 0 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
        else
            FuBenWGCtrl.Instance:SendDecBossTired(cfg.dec_tired_type)
            --BossWGCtrl.Instance:OpenQuickUseView(cfg)
        end
    end
end

function WorldBossBuyTimesView:OnClickXuFei()
	-- VipWGCtrl.Instance:OpenVipRenewView()
	ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_vip)
end

function WorldBossBuyTimesView:OnClickBuyCount()
	local max_vip_level = VipWGData.Instance:GetMaxVIPLevel()
	local vip_buy_cfg = VipWGData.Instance:GetVipSpecPermissions(self.data.vip_type)
    local now_count = VipPower.Instance:GetParam(self.data.vip_type)
	local buy_times = BossWGData.Instance:GetWorldBossBuyTimes()
	--local next_vip, next_count = VipPower.Instance:GetNextBigParam(self.data.vip_type ,now_count)

	if self.scene_type == SceneType.KF_BOSS then
		buy_times = BossWGData.Instance:GetCrossBossBuyTime()
	end

	local times = now_count - buy_times
	local is_max_count = vip_buy_cfg["param_" .. max_vip_level] == buy_times

	if 0 >= times then
		if is_max_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.BuyMaxCount)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.VIPTooLow)
		end
	else
		self:SendBuy()
	end
end

--发送购买协议
function WorldBossBuyTimesView:SendBuy()
	if self.scene_type == SceneType.KF_BOSS then
		BossWGCtrl.Instance:SendCrossBossReq(BossView.KfReqType.CROSS_BOSS_BUY_COUNT,1)
	else
		BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.WORLD_BOSS_BUY_VIP_ENTER_TIMES)
	end
end

--购买需要消耗
function WorldBossBuyTimesView:GetNeedGold(buy_times)
	if self.scene_type == SceneType.KF_BOSS then
		return BossWGData.Instance:GetCrossBossVipBuyCost()
	end
	local need_gold = BossWGData.Instance:GetWorldBossBuyConsumeByTimes(buy_times)
	return need_gold
end