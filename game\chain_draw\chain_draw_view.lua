ChainDrawView = ChainDrawView or BaseClass(SafeBaseView)

function ChainDrawView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/chain_draw_ui_prefab", "layout_chain_draw")
end

function ChainDrawView:__delete()
end

function ChainDrawView:ReleaseCallBack()
    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if CountDownManager.Instance:HasCountDown("chain_draw_time") then
        CountDownManager.Instance:RemoveCountDown("chain_draw_time")
    end

    if self.alert ~= nil then
        self.alert:DeleteMe()
        self.alert = nil
    end

    if self.once_alert ~= nil then
        self.once_alert:DeleteMe()
        self.once_alert = nil
    end

    if self.reward_list then
        for k, v in pairs(self.reward_list) do
            v:DeleteMe()
        end
        self.reward_list = nil
    end

    if self.spe_reward_list then
        for k, v in pairs(self.spe_reward_list) do
            v:DeleteMe()
        end
        self.spe_reward_list = nil
    end
end

function ChainDrawView:OpenCallBack()
    ChainDrawWGCtrl.Instance:ReqChainDrawInfo(OA_CHAIN_DRAW_OPERTE_TYPE.INFO)
end

function ChainDrawView:LoadCallBack()
    if nil == self.task_list then
        self.task_list = AsyncListView.New(ChainDrawTaskCell, self.node_list.task_list)
    end

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar_pos"].transform)
    end

    if self.reward_list == nil then
        self.reward_list = {}
        for i = 0, ChainDrawWGData.MAX_REWARD_COUNT - 1 do
            self.reward_list[i] = ChainDrawRewardCell.New(self.node_list["reward_group"]:FindObj("reward_item_" .. i))
            self.reward_list[i]:SetIndex(i)
        end
    end

    if self.spe_reward_list == nil then
        self.spe_reward_list = {}
        for i = 0, ChainDrawWGData.MAX_SPE_REWARD_COUNT - 1 do
            self.spe_reward_list[i] = ChainDrawSpeRewardCell.New(self.node_list["special_reward_group"]:FindObj("special_reward_" .. i))
            self.spe_reward_list[i]:SetIndex(i)
        end
    end

    self.node_list["tips_btn"].button:AddClickListener(BindTool.Bind(self.OnClickTip, self))
    XUI.AddClickEventListener(self.node_list["flop_btn"], BindTool.Bind(self.OnClickStepBtn, self))
    XUI.AddClickEventListener(self.node_list["once_flop_btn"], BindTool.Bind(self.OnClickOnceStepBtn, self)) --一键
    self.node_list["need_cost_icon"].button:AddClickListener(BindTool.Bind(self.ShowCostItemTips, self))
end

function ChainDrawView:ShowIndexCallBack()
    self:FlushTimeCount()
end

function ChainDrawView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
        if k == "all" then
            self:FlushAllView()
        elseif k == "flush_task" then
            self:FlushRightView()
        elseif k == "flush_cost" then
            self:FlushCostView()
        end
    end
end

function ChainDrawView:FlushAllView()
    self:FlushRightView()
    self:FlushMidView()
    self:FlushCostView()
end

function ChainDrawView:FlushMidView()
    local reward_cfg = ChainDrawWGData.Instance:GetAllRewardCfg()
    for i, v in pairs(self.reward_list) do
        v:SetData(reward_cfg[i])
    end

    local other_cfg = ChainDrawWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if item_cfg then
        self.node_list["need_cost_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id)) 
    end

    local spe_reward_cfg = ChainDrawWGData.Instance:GetAllSpeRewardCfg()
    for i, v in pairs(self.spe_reward_list) do
        v:SetData(spe_reward_cfg[i])
    end
end

function ChainDrawView:FlushCostView()
    local cur_time = ChainDrawWGData.Instance:GetCurTime()
    local other_cfg = ChainDrawWGData.Instance:GetOtherCfg()
    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = ChainDrawWGData.Instance:GetTimeCostNum(cur_time + 1)
    local str = item_num .. "/" .. cost_num
    local color = item_num >= cost_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.need_cost_item_num.text.text = ToColorStr(str, color)
    local is_remind = ChainDrawWGData.Instance:ShowTimeBtnRemind()
    self.node_list.time_remind:SetActive(is_remind)
end

function ChainDrawView:FlushRightView()
    local all_task_data = ChainDrawWGData.Instance:GetAllTaskList()
    self.task_list:SetDataList(all_task_data)
end

function ChainDrawView:FlushTimeCount()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHAIN_DRAW)
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("chain_draw_time") then
            CountDownManager.Instance:RemoveCountDown("chain_draw_time")
        end

        CountDownManager.Instance:AddCountDown("chain_draw_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function ChainDrawView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["act_time"].text.text = string.format(Language.ChainDrawView.ActivityTime, time_str) 
end

function ChainDrawView:OnComplete()
    self.node_list.act_time.text.text = ""
end

function ChainDrawView:OnClickTip()
    RuleTip.Instance:SetContent(Language.ChainDrawView.RuleContent, Language.ChainDrawView.RuleTitle)
end

function ChainDrawView:OnClickStepBtn()
    local cur_time = ChainDrawWGData.Instance:GetCurTime()
    if cur_time >= ChainDrawWGData.MAX_REWARD_COUNT then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ChainDrawView.AllBuy)
        return
    end

    local other_cfg = ChainDrawWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if not item_cfg then
        print_error("=====物品id不存在===", other_cfg.cost_item_id)
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = ChainDrawWGData.Instance:GetTimeCostNum(cur_time + 1)
    if item_num >= cost_num then
        ChainDrawWGCtrl.Instance:ReqChainDrawInfo(OA_CHAIN_DRAW_OPERTE_TYPE.DO_DRAW, 0)
    else
        if not self.alert then
            self.alert = Alert.New()
        end

        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "chain_draw")
        self.alert:SetCheckBoxDefaultSelect(false)
        local name = ""
        if item_cfg ~= nil then
            name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        end

        local cost = other_cfg.cost_gold * (cost_num - item_num)
        local str = string.format(Language.ChainDrawView.CostStr, name, cost)
        self.alert:SetLableString(str)
        local ok_func = function ()
            local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(cost)
            if have_enough then
                ChainDrawWGCtrl.Instance:ReqChainDrawInfo(OA_CHAIN_DRAW_OPERTE_TYPE.DO_DRAW, 0)
            else
                UiInstanceMgr.Instance:ShowChongZhiView()
            end
        end

        self.alert:SetOkFunc(ok_func)
        self.alert:Open()
    end 
end

function ChainDrawView:OnClickOnceStepBtn()
    local cur_time = ChainDrawWGData.Instance:GetCurTime()
    if cur_time >= ChainDrawWGData.MAX_REWARD_COUNT then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ChainDrawView.AllBuy)
        return
    end

    local other_cfg = ChainDrawWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if not item_cfg then
        print_error("=====物品id不存在===", other_cfg.cost_item_id)
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = 0
    for i = cur_time + 1, ChainDrawWGData.MAX_REWARD_COUNT do
        cost_num = cost_num + ChainDrawWGData.Instance:GetTimeCostNum(i)
    end

    if item_num >= cost_num then
        ChainDrawWGCtrl.Instance:ReqChainDrawInfo(OA_CHAIN_DRAW_OPERTE_TYPE.DO_DRAW, 1)
    else
        if not self.once_alert then
            self.once_alert = Alert.New()
        end

        self.once_alert:ClearCheckHook()
        self.once_alert:SetShowCheckBox(true, "once_chain_draw")
        self.once_alert:SetCheckBoxDefaultSelect(false)
        local name = ""
        if item_cfg ~= nil then
            name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        end

        local cost = other_cfg.cost_gold * (cost_num - item_num)
        local str = string.format(Language.FlopDraw.CostStr, name, cost)
        self.once_alert:SetLableString(str)
        local ok_func = function ()
            local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(cost)
            if have_enough then
                ChainDrawWGCtrl.Instance:ReqChainDrawInfo(OA_CHAIN_DRAW_OPERTE_TYPE.DO_DRAW, 1)
            else
                UiInstanceMgr.Instance:ShowChongZhiView()
            end
        end

        self.once_alert:SetOkFunc(ok_func)
        self.once_alert:Open()
    end 
end

function ChainDrawView:ShowCostItemTips()
    local other_cfg = ChainDrawWGData.Instance:GetOtherCfg()
    local item_id = other_cfg.cost_item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id}) 
end
--------------------------------任务格子----------------
ChainDrawTaskCell = ChainDrawTaskCell or BaseClass(BaseRender)

function ChainDrawTaskCell:__init()
    self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetTaskReward,self))
end

function ChainDrawTaskCell:__delete()
end

function ChainDrawTaskCell:OnFlush()
    if not self.data then
        return
    end

    self.node_list.go_btn:SetActive(self.data.status == REWARD_STATE_TYPE.UNDONE)
    self.node_list.get_btn:SetActive(self.data.status == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.have_get:SetActive(self.data.status == REWARD_STATE_TYPE.FINISH)

    self.node_list.target_text:SetActive(self.data.task_type ~= 2 and self.data.task_type ~= 3)
    local str = self.data.des
    local num = self.data.item_list[0].num
    self.node_list.desc_text.text.text = string.format(Language.ChainDrawView.Desc, str, num)
    local color = self.data.progress_num >= self.data.target and COLOR3B.GREEN or COLOR3B.RED
    local count_str = ToColorStr(self.data.progress_num .. "/" .. self.data.target, color) 
    self.node_list.target_text.text.text = string.format(Language.ChainDrawView.TaskProgress, count_str)

    local item_id = self.data.item_list[0].item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg then
        self.node_list["icon_img"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end
end

function ChainDrawTaskCell:OnClickGo()
    if self.data.open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
    end
end

function ChainDrawTaskCell:OnClickGetTaskReward()
    ChainDrawWGCtrl.Instance:ReqChainDrawInfo(OA_CHAIN_DRAW_OPERTE_TYPE.TASK_REWARD, self.data.task_id)
end

-------------------普通奖励格子-------------------
ChainDrawRewardCell = ChainDrawRewardCell or BaseClass(BaseRender)

function ChainDrawRewardCell:__init()
    self.reward_item = ItemCell.New(self.node_list["cell_pos"])
end

function ChainDrawRewardCell:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function ChainDrawRewardCell:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.item.item_id
    self.reward_item:SetData({item_id = item_id})
    self.reward_item:SetRightBottomColorText(self.data.item.num)
    self.reward_item:SetRightBottomTextVisible(true)
    local state = ChainDrawWGData.Instance:GetRewardflag(self.data.seq)
    self.node_list.already_get:SetActive(state)
end

-------------------特殊奖励格子-------------------
ChainDrawSpeRewardCell = ChainDrawSpeRewardCell or BaseClass(BaseRender)

function ChainDrawSpeRewardCell:__init()
    self.reward_item = ItemCell.New(self.node_list["reward_item_pos"])
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function ChainDrawSpeRewardCell:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function ChainDrawSpeRewardCell:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.item.item_id
    self.reward_item:SetData({item_id = item_id})
    self.reward_item:SetRightBottomColorText(self.data.item.num)
    self.reward_item:SetRightBottomTextVisible(true)

    self.node_list.is_rare:SetActive(self.data.is_rare == 1)
    local state = ChainDrawWGData.Instance:GetSpeRewardflag(self.data.seq)
    self.node_list.already_get:SetActive(state)
    local is_can_get = ChainDrawWGData.Instance:IsCanGetSpeReward(self.data.seq)
    self.node_list.canget_img:SetActive(is_can_get)
    self.node_list.get_btn:SetActive(is_can_get)
end

function ChainDrawSpeRewardCell:OnClickGetReward()
    if not self.data then
        return
    end

    local is_get = ChainDrawWGData.Instance:GetSpeRewardflag(self.data.seq) 
    if is_get then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ChainDrawView.IsGetReward)
        return
    end

    ChainDrawWGCtrl.Instance:ReqChainDrawInfo(OA_CHAIN_DRAW_OPERTE_TYPE.DRAW_REWARD, self.data.seq)
end