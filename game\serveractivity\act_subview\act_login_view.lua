--不知道有什么用的脚本
----------------------------------------------------
-- 登录有礼
----------------------------------------------------
ActLoginView = ActLoginView or BaseClass(SafeBaseView)

function ActLoginView:__init(act_id)
	self:AddViewResource(0, "uis/view/act_subview_ui_prefab", "layout_login_view")
	self.act_id = act_id
	self.list_is_jum_top = true
end

function ActLoginView:__delete()

end

function ActLoginView:ReleaseCallBack()
	if self.reward_list ~= nil then 
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	self.list_is_jum_top = true
end

function ActLoginView:LoadCallBack()
	self.reward_list = AsyncListView.New(ActLoginItemRender,self.node_list["ph_login_gift_list"])
end

function ActLoginView:RefreshView(param_list)
	self:RefreshTopDesc()

	local info = ActCombineData.Instance:GetActLoginGiftData(self.act_id)
	if info == nil then return end
	local item_data_list = {}
	local key = 0
	for k = 0, #info.login_gift_cfg do
		if info.login_gift_cfg[k] then
			key = k + 1
			item_data_list[key] = __TableCopy(info.login_gift_cfg[k])
			item_data_list[key].reward_flag = info.reward_flag[31 - item_data_list[key].seq] == 1
			item_data_list[key].now_day = info.login_days
			item_data_list[key].reward_type = info.reward_type
			item_data_list[key].item_func = info.item_func
			item_data_list[key].min_level = info.min_level
		end
	end

	self.reward_list:SetDataList(item_data_list)
	if self.list_is_jum_top then
		self.reward_list:JumpToTop()
		self.list_is_jum_top = false
	end
end

function ActLoginView:CompleteCountDownTime(is_auto_fuhuo)
	self:RefreshView()
end

function ActLoginView:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)

	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time)
		end
		self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text = (open_act_cfg.top_desc)
	end
end

-------------------------------------------------------------------
------------------         itemRender           -------------------
-------------------------------------------------------------------
ActLoginItemRender = ActLoginItemRender or BaseClass(BaseRender)
function ActLoginItemRender:__init()
	
end

function ActLoginItemRender:__delete()
	for i,v in ipairs(self.item_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end

	self.item_list = {}
end

function ActLoginItemRender:LoadCallBack()
	self.item_list = {}
	for i=1,4 do
		self.item_list[i] = ItemCell.New(self.node_list["ph_cell_" .. i])
	end
	XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind1(self.OnClickLingQu, self))
end

function ActLoginItemRender:OnFlush()
	if nil == self.data then return end
	local item_data = self.data.reward_item
	for i=1,4 do
		if nil == item_data[i - 1] then
			self.item_list[i]:SetActive(false)
		else
			self.item_list[i]:SetData(item_data[i - 1])
			self.item_list[i]:SetActive(true)
		end
	end

	if self.data.reward_flag then
		self.node_list.btn_receive:SetActive(false)
		self.node_list.img_has:SetActive(true)
		self.node_list.img_no:SetActive(false)
	elseif self.data.now_day == self.data.combine_days then
		self.node_list.btn_receive:SetActive(true)
		self.node_list.img_has:SetActive(false)
		self.node_list.img_no:SetActive(false)
	else
		self.node_list.btn_receive:SetActive(false)
		self.node_list.img_has:SetActive(false)
		self.node_list.img_no:SetActive(true)
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	XUI.SetButtonEnabled(self.node_list.btn_receive, role_level >= self.data.min_level)

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(self.data.reward_type)
	local star_str = ""
	if act_info ~= nil and act_info.start_time ~= nil then
		star_str = TimeUtil.FormatSecond2MY(act_info.start_time + ((self.data.combine_days - 1) * 3600 * 24))
	end

	self.node_list.lbl_desc.text.text = (star_str .. Language.Achieve.KeLingQu)
end

function ActLoginItemRender:OnClickLingQu()
	if self.data ~= nil and self.data.item_func then
		self.data.item_func()
	end
end

--override
function ActLoginItemRender:CreateSelectEffect()
	
end
