-- T-天机谱.xls
local item_table={
[1]={item_id=27938,num=1,is_bind=1},
[2]={item_id=36541,num=1,is_bind=1},
[3]={item_id=36425,num=300,is_bind=1},
[4]={item_id=27700,num=3,is_bind=1},
[5]={item_id=27703,num=1,is_bind=1},
[6]={item_id=26344,num=3,is_bind=1},
[7]={item_id=26200,num=3,is_bind=1},
[8]={item_id=26203,num=2,is_bind=1},
[9]={item_id=26346,num=3,is_bind=1},
[10]={item_id=26349,num=3,is_bind=1},
[11]={item_id=27611,num=3,is_bind=1},
[12]={item_id=26121,num=1,is_bind=1},
[13]={item_id=26148,num=3,is_bind=1},
[14]={item_id=26149,num=3,is_bind=1},
[15]={item_id=27829,num=3,is_bind=1},
[16]={item_id=26914,num=10,is_bind=1},
[17]={item_id=26606,num=1,is_bind=1},
[18]={item_id=26501,num=1,is_bind=1},
[19]={item_id=32300,num=1,is_bind=1},
[20]={item_id=22537,num=1,is_bind=1},
[21]={item_id=48048,num=1,is_bind=1},
[22]={item_id=26419,num=1,is_bind=1},
[23]={item_id=27931,num=1,is_bind=1},
[24]={item_id=36431,num=100,is_bind=1},
[25]={item_id=28055,num=1,is_bind=1},
[26]={item_id=26415,num=5,is_bind=1},
[27]={item_id=29209,num=1,is_bind=1},
[28]={item_id=26370,num=3,is_bind=1},
[29]={item_id=36421,num=30,is_bind=1},
[30]={item_id=26162,num=1,is_bind=1},
[31]={item_id=29481,num=1,is_bind=1},
[32]={item_id=28089,num=2,is_bind=1},
[33]={item_id=27900,num=200,is_bind=1},
[34]={item_id=27903,num=1,is_bind=1},
[35]={item_id=27923,num=1,is_bind=1},
[36]={item_id=26368,num=1,is_bind=1},
[37]={item_id=26359,num=1,is_bind=1},
[38]={item_id=29476,num=3,is_bind=1},
[39]={item_id=36425,num=400,is_bind=1},
[40]={item_id=27820,num=1,is_bind=1},
[41]={item_id=36425,num=500,is_bind=1},
[42]={item_id=27656,num=3,is_bind=1},
[43]={item_id=27656,num=5,is_bind=1},
[44]={item_id=43002,num=1,is_bind=1},
[45]={item_id=26356,num=1,is_bind=1},
[46]={item_id=28084,num=1,is_bind=1},
[47]={item_id=22529,num=200,is_bind=1},
[48]={item_id=28086,num=1,is_bind=1},
[49]={item_id=27861,num=1,is_bind=1},
}

return {
condition={
{param2=40,param3=2,desc="总功能开启条件",},
{condition_id=1001,desc="章节1开启条件",},
{condition_id=1002,desc="章节2开启条件",},
{condition_id=1003,desc="章节3开启条件",},
{condition_id=1004,desc="章节4开启条件",},
{condition_id=1011,desc="第1章法器1开启",},
{condition_id=1012,condition_type=101,},
{condition_id=1013,desc="第1章法器3开启",},
{condition_id=1021,desc="第2章法器1开启",},
{condition_id=1022,condition_type=103,param1=2,desc="第2章法器2开启",},
{condition_id=1023,condition_type=103,param1=3,desc="第2章法器3开启",},
{condition_id=1031,condition_type=103,param1=4,desc="第3章法器1开启",},
{condition_id=1032,condition_type=103,param1=5,desc="第3章法器2开启",},
{condition_id=1041,condition_type=103,param1=6,desc="第4章法器1开启",},
{condition_id=1042,condition_type=103,param1=7,},
{condition_id=1100,param2=70,param3=2,desc="完成任务：支援青衣师妹（<per>0/1</per>）",},
{condition_id=1101,param2=140,param3=2,desc="完成任务：在商城购买宠物蛋（<per>0/1</per>）",},
{condition_id=1102,condition_type=121,param1=3,desc="拥有1只 <per>0/3</per> 级的宠物",},
{condition_id=1103,param2=155,param3=2,desc="完成任务：探明前方妖气来源（<per>0/1</per>）",},
{condition_id=1104,condition_type=121,param1=5,desc="拥有1只 <per>0/5</per> 级宠物",},
{condition_id=1105,param2=310,param3=2,desc="完成任务：击败烈焰穷奇（<per>0/1</per>）",},
{condition_id=1106,condition_type=120,param2=10,desc=" <per>0/3</per> 个技能提升到10级 ",},
{condition_id=1107,param2=315,param3=2,desc="完成任务：挑战魔王烈火狻猊（<per>0/1</per>）",},
{condition_id=1108,condition_type=151,param1=5,desc="装备强化总等级达到 <per>0/5</per> 级",},
{condition_id=1109,condition_type=126,param2=1,desc="将坐骑进阶到一阶2星（<per>0/1</per>）",},
{condition_id=1110,condition_type=101,param1=35,desc="角色等级达到 <per>0/35</per> 级",},
{condition_id=1111,param2=770,param3=2,desc="完成任务：购买御仙之翼（<per>0/1</per>）",},
{condition_id=1200,condition_type=127,param1=2,desc="羽翼等级提升到 <per>0/2</per> 级",},
{condition_id=1201,param2=811,param3=2,desc="完成任务：购买猪小戒（<per>0/1</per>）",},
{condition_id=1202,param2=815,param3=2,desc="完成任务：挑战魔王上古邪魔（<per>0/1</per>）",},
{condition_id=1203,param2=819,param3=2,desc="完成任务：购买定风珠（<per>0/1</per>）",},
{condition_id=1204,condition_type=124,param1=2,desc="法器等级提升至 <per>0/2</per> 级",},
{condition_id=1205,param2=20,desc=" <per>0/4</per> 个技能全部达到20级",},
{condition_id=1206,param2=823,param3=2,desc="完成任务：解救蜀山圣女（<per>0/1</per>）",},
{condition_id=1207,param2=825,param3=2,desc="完成任务：收集暗影残魂（<per>0/1</per>）",},
{condition_id=1208,condition_type=149,param1=2,desc="神灵等级提升到 <per>0/2</per> 级",},
{condition_id=1209,condition_type=146,param2=63,desc="击杀心魔牢狱天音琴妖 <per>0/1</per> 次 ",},
{condition_id=1210,param2=860,param3=2,desc="完成任务：探索金蟾宝库（<per>0/1</per>）",},
{condition_id=1211,condition_type=101,param1=65,desc="角色等级达到 <per>0/65</per> 级",},
{condition_id=1300,param2=30,desc=" <per>0/4</per> 个技能全部达到30级",},
{condition_id=1301,condition_type=146,param2=64,desc="击杀心魔牢狱洪荒赤魔 <per>0/1</per> 次",},
{condition_id=1302,param2=1080,param3=2,desc="完成任务：营救圣灵仙子（<per>0/1</per>）",},
{condition_id=1303,param2=1160,param3=2,desc="完成任务：挑战伏魔战场（<per>0/1</per>）",},
{condition_id=1304,condition_type=146,param2=65,desc="击杀心魔牢狱通臂神猿 <per>0/1</per> 次",},
{condition_id=1305,param2=1310,param3=2,desc="完成任务：送喜帖至御影门(<per>0/1</per>)",},
{condition_id=1306,param2=1410,param3=2,desc="完成任务：解决白廷的烦恼（<per>0/1</per>）",},
{condition_id=1307,param2=9,desc="穿戴 <per>0/1</per> 件紫色以上的仙镯 ",},
{condition_id=1308,param2=1450,param3=2,desc="完成任务：帮助老僧解决心魔（<per>0/1</per>）",},
{condition_id=1309,condition_type=104,desc="完成角色 <per>0/1</per> 转",},
{condition_id=1310,param2=1550,param3=2,desc="完成任务：购买神兵墨玉（<per>0/1</per>）",},
{condition_id=1311,condition_type=101,param1=170,desc="角色等级达到 <per>0/170</per> 级",},
{condition_id=2100,condition_type=132,param1=2,desc="上阵 <per>0/2</per> 只宠物",},
{condition_id=2101,param1=7,param3=1,desc="穿戴 <per>0/7</per> 件五阶1星以上红装",},
{condition_id=2102,condition_type=145,param2=5,param3=3,param4=5,desc="穿戴 <per>0/1</per> 件五阶3星以上红装",},
{condition_id=2103,condition_type=106,desc="次日登陆 <per>0/1</per> 次",},
{condition_id=2104,param4=2,desc="上阵 <per>0/2</per> 只紫色以上宠物",},
{condition_id=2105,param2=15,desc="上阵 <per>0/2</per> 只15级以上宠物",},
{condition_id=2106,condition_type=132,param3=3,desc="上阵 <per>0/1</per> 只3阶以上宠物",},
{condition_id=2107,condition_type=101,param1=180,desc="角色等级达到 <per>0/180</per> 级",},
{condition_id=2108,condition_type=101,param1=190,desc="开启奇门-八卦",},
{condition_id=2109,condition_type=139,param3=2,desc="镶嵌 <per>0/3</per> 个蓝色以上八卦印",},
{condition_id=2110,condition_type=139,param2=5,desc="镶嵌 <per>0/1</per> 个5级以上八卦印",},
{condition_id=2111,condition_type=139,param3=3,desc="镶嵌 <per>0/1</per> 个紫色以上八卦印",},
{condition_id=2200,desc="开启装备洗炼",},
{condition_id=2201,condition_type=150,param1=10,desc="进行 <per>0/10</per> 次装备洗炼",},
{condition_id=2202,condition_type=118,param2=3,desc="拥有 <per>0/2</per> 条紫色以上洗炼属性",},
{condition_id=2203,condition_type=101,param1=220,desc="角色等级达到 <per>0/220</per> 级",},
{condition_id=2204,param2=1960,param3=2,desc="完成任务：购买天绝剑阵（<per>0/1</per>）",},
{condition_id=2205,condition_type=149,param1=10,desc="神灵等级提升到 <per>0/10</per> 级",},
{condition_id=2206,condition_type=136,param2=2,desc="拥有 <per>0/1</per> 张紫色以上图鉴",},
{condition_id=2207,condition_type=135,param2=2,desc="激活 <per>0/1</per> 套紫色以上图鉴羁绊",},
{condition_id=2208,condition_type=151,param1=70,desc="装备强化总等级到 <per>0/70</per> 级",},
{condition_id=2209,condition_type=152,param1=10,desc="仙器强化总等级到 <per>0/10</per> 级",},
{condition_id=2210,param2=80,desc=" <per>0/4</per> 个技能全部达到80级",},
{condition_id=2211,param1=4,desc="穿戴 <per>0/4</per> 件五阶3星以上红装",},
{condition_id=2300,condition_type=129,param1=30,desc="修仙达到元婴期（<per>0/30</per>）",},
{condition_id=2301,condition_type=104,param1=2,desc="完成角色 <per>0/2</per> 转",},
{condition_id=2302,condition_type=148,desc="进入 <per>0/1</per> 次天道苍茫",},
{condition_id=2303,condition_type=112,param2=0,desc="炼器 <per>0/1</per> 件5阶以上诛魔装备",},
{condition_id=2304,condition_type=101,param1=250,desc="角色等级达到 <per>0/250</per> 级",},
{condition_id=2305,condition_type=105,param2=27923,desc="获得 <per>0/1</per> 个幼鲲元神·紫",},
{condition_id=2306,condition_type=143,param2=3,desc="孵化 <per>0/1</per> 只幼鲲元神·紫",},
{condition_id=2307,param1=3,desc="上阵 <per>0/3</per> 只紫色以上的宠物",},
{condition_id=2308,condition_type=121,param1=20,desc="拥有1只 <per>0/20</per> 级宠物",},
{condition_id=2309,condition_type=126,param2=3,desc="坐骑等级进阶到三阶以上（<per>0/1</per>）",},
{condition_id=2310,condition_type=126,param2=4,desc="坐骑等级进阶到四阶以上（<per>0/1</per>）",},
{condition_id=2311,condition_type=101,param1=260,desc="角色等级达到 <per>0/260</per> 级",},
{condition_id=3100,condition_type=124,param1=30,desc="法器等级提升至 <per>0/30</per> 级",},
{condition_id=3101,condition_type=124,param1=40,desc="法器等级提升至 <per>0/40</per> 级",},
{condition_id=3102,condition_type=117,param1=10000,desc="商城累计消耗 <per>0/10000</per> 元宝",},
{condition_id=3103,param1=4,desc="拥有 <per>0/4</per> 条紫色以上洗炼属性",},
{condition_id=3104,condition_type=151,param1=100,desc="装备强化总等级到 <per>0/100</per> 级",},
{condition_id=3105,condition_type=130,param1=3,desc="进行 <per>0/3</per> 次装备升品",},
{condition_id=3106,condition_type=117,param1=15000,desc="商城累计消耗 <per>0/15000</per> 元宝",},
{condition_id=3107,condition_type=133,param1=2,desc="出战 <per>0/2</per> 位神灵",},
{condition_id=3108,condition_type=149,param1=20,desc="神灵等级提升到 <per>0/20</per> 级",},
{condition_id=3109,condition_type=149,param1=30,desc="神灵等级提升到 <per>0/30</per> 级",},
{condition_id=3110,condition_type=117,param1=20000,desc="商城累计消耗 <per>0/20000</per> 元宝",},
{condition_id=3111,condition_type=134,param2=0,desc="激活上古神灵-蚩尤（<per>0/1</per>）",},
{condition_id=3200,condition_type=101,param1=280,desc="角色等级达到 <per>0/280</per> 级",},
{condition_id=3201,param2=100,desc=" <per>0/4</per> 个技能全部达到100级",},
{condition_id=3202,param1=2,desc="炼器 <per>0/2</per> 件5阶以上诛魔装备",},
{condition_id=3203,condition_type=129,param1=40,desc="修仙达到渡劫期（<per>0/40</per>） ",},
{condition_id=3204,condition_type=137,param2=1,desc="激活 <per>0/1</per> 只蓝色以上的神兽",},
{condition_id=3205,condition_type=138,param2=5,desc=" <per>0/1</per> 件神兽装备强化至5级",},
{condition_id=3206,condition_type=137,desc="激活 <per>0/2</per> 只紫色以上的神兽",},
{condition_id=3207,param1=10,desc="蚩尤全身神格总阶数达到 <per>0/10</per> 阶",},
{condition_id=3208,condition_type=127,param1=40,desc="羽翼等级提升到 <per>0/40</per> 级",},
{condition_id=3209,condition_type=127,param1=50,desc="羽翼等级提升到 <per>0/50</per> 级",},
{condition_id=3210,condition_type=119,param2=1,desc="激活 <per>0/1</per> 个1阶以上羽翼外观",},
{condition_id=3211,condition_type=101,param1=300,desc="角色等级达到 <per>0/300</per> 级",},
{condition_id=4100,condition_type=151,param1=150,desc="装备强化总等级到 <per>0/150</per> 级",},
{condition_id=4101,condition_type=152,param1=40,desc="仙器强化总等级到 <per>0/40</per> 级",},
{condition_id=4102,condition_type=111,param1=15,desc="玉魄镶嵌总等级达到 <per>0/15</per> 级",},
{condition_id=4103,param1=3,desc="炼器 <per>0/3</per> 件5阶以上诛魔装备",},
{condition_id=4104,param1=6,desc="拥有 <per>0/6</per> 条紫色以上洗炼属性",},
{condition_id=4105,condition_type=131,param2=6,desc="对项链进行 <per>0/1</per> 次洗炼",},
{condition_id=4106,condition_type=111,param1=20,desc="玉魄镶嵌总等级达到 <per>0/20</per> 级",},
{condition_id=4107,condition_type=137,param2=3,desc="激活 <per>0/1</per> 只橙色以上的神兽",},
{condition_id=4108,condition_type=136,param2=3,desc="拥有 <per>0/1</per> 张橙色品质图鉴",},
{condition_id=4109,param1=2,desc="激活 <per>0/2</per> 套紫色以上图鉴羁绊",},
{condition_id=4110,condition_type=111,param1=25,desc="玉魄镶嵌总等级达到 <per>0/25</per> 级",},
{condition_id=4111,condition_type=101,param1=315,desc="角色等级达到 <per>0/315</per> 级",},
{condition_id=4200,param1=20,desc="蚩尤全身神格总阶数达到 <per>0/20</per> 阶",},
{condition_id=4201,condition_type=120,param2=110,desc=" <per>0/4</per> 个技能全部达到110级",},
{condition_id=4202,condition_type=152,param1=60,desc="仙器强化总等级到 <per>0/60</per> 级",},
{condition_id=4203,condition_type=129,param1=50,desc="修仙达到化神期（<per>0/50</per>）",},
{condition_id=4204,condition_type=147,param1=10,desc="进行 <per>0/10</per> 次挖宝",},
{condition_id=4205,condition_type=117,param1=40000,desc="商城累计消耗 <per>0/40000</per> 元宝",},
{condition_id=4206,condition_type=128,param2=0,desc="蚩尤全身神格总阶数达到 <per>0/30</per> 阶",},
{condition_id=4207,condition_type=144,param2=8,desc="拥有 <per>0/1</per> 件紫色以上戒指",},
{condition_id=4208,condition_type=142,param2=3,desc="获得 <per>0/1</per> 个紫色龙魂",},
{condition_id=4209,condition_type=141,param2=3,desc="镶嵌 <per>0/3</per> 个紫色龙魂",},
{condition_id=4210,param1=40,desc="蚩尤全身神格总阶数达到 <per>0/40</per> 阶",},
{condition_id=4211,param1=4,desc="炼器 <per>0/4</per> 件5阶以上诛魔装备",}
},

condition_meta_table_map={
[2]=7,	-- depth:1
[3]=7,	-- depth:1
[6]=7,	-- depth:1
[8]=7,	-- depth:1
[9]=7,	-- depth:1
[64]=67,	-- depth:1
[5]=14,	-- depth:1
[4]=12,	-- depth:1
[130]=97,	-- depth:1
[66]=52,	-- depth:1
[121]=71,	-- depth:1
[134]=130,	-- depth:2
[125]=12,	-- depth:1
[61]=11,	-- depth:1
[124]=130,	-- depth:2
[133]=11,	-- depth:1
[57]=52,	-- depth:1
[131]=58,	-- depth:1
[56]=52,	-- depth:1
[135]=79,	-- depth:1
[106]=121,	-- depth:2
[40]=125,	-- depth:2
[116]=66,	-- depth:2
[115]=79,	-- depth:1
[91]=66,	-- depth:2
[22]=11,	-- depth:1
[25]=42,	-- depth:1
[107]=130,	-- depth:2
[74]=125,	-- depth:2
[47]=131,	-- depth:2
[83]=56,	-- depth:2
[102]=79,	-- depth:1
[101]=125,	-- depth:2
[33]=125,	-- depth:2
[75]=54,	-- depth:1
[53]=54,	-- depth:1
},
other={
{}
},

other_meta_table_map={
},
chapter={
{},
{chapter_id=2,shenqi_id_list="21|22|23",pre_chapter=1,},
{chapter_id=3,shenqi_id_list="31|32",condition_id=1003,},
{chapter_id=4,shenqi_id_list="41|42",condition_id=1004,}
},

chapter_meta_table_map={
},
shenqi={
{reward_skill_id_list=11,open_condition_desc="默认开启",},
{shenqi_id=12,begin_node_id=1200,end_node_id=1212,pre_shenqi=11,open_xinmo_fb=1,shenqi_name="渡心镜",cutoutIimage=2,model_id=2,model_size=250,model_pos="0.01|-1|0",},
{shenqi_id=13,begin_node_id=1300,end_node_id=1312,pre_shenqi=12,reward_skill_id_list=13,shenqi_name="天王宝塔",cutoutIimage=3,model_id=3,model_size=300,model_pos="0|-0.64|0",model_rotation="0|10|0",detail_model_rotation="0|-10|0",},
{shenqi_id=21,begin_node_id=2100,end_node_id=2112,pre_shenqi=13,reward_skill_id_list=21,shenqi_name="龙神印",cutoutIimage=4,model_id=4,model_pos="0.03|-1.1|0",model_rotation="0|-188|0",detail_model_rotation="0|-188|0",},
{shenqi_id=22,begin_node_id=2200,end_node_id=2212,condition_id=1022,reward_skill_id_list=22,shenqi_name="玉净瓶",cutoutIimage=5,model_id=5,open_condition_desc="第2天开启",model_pos="0|-1.13|0",},
{shenqi_id=23,begin_node_id=2300,end_node_id=2312,condition_id=1023,reward_item={[0]=item_table[1]},shenqi_name="沧海遗音",cutoutIimage=6,model_id=6,model_size=350,open_condition_desc="第3天开启",model_pos="-0.04|-0.43|0",detail_model_rotation="0|-20|0",},
{shenqi_id=31,begin_node_id=3100,end_node_id=3112,condition_id=1031,reward_skill_id_list=23,shenqi_name="女娲璧",cutoutIimage=7,model_id=7,model_size=120,open_condition_desc="第4天开启",model_pos="0|-1.52|0",},
{shenqi_id=32,begin_node_id=3200,end_node_id=3212,condition_id=1032,reward_skill_id_list=31,shenqi_name="续命莲盏",cutoutIimage=8,model_id=8,model_size=160,open_condition_desc="第5天开启",model_pos="0.05|-1.1|0",},
{shenqi_id=41,begin_node_id=4100,end_node_id=4112,condition_id=1041,reward_skill_id_list=41,shenqi_name="鹤留",cutoutIimage=9,model_id=9,model_size=400,open_condition_desc="第6天开启",model_pos="0|-0.56|0",},
{shenqi_id=42,begin_node_id=4200,end_node_id=4212,condition_id=1042,reward_skill_id_list=42,shenqi_name="黄泉",cutoutIimage=10,model_id=10,open_condition_desc="第7天开启",model_pos="0.05|-1.23|0",model_rotation="0|-180|0",detail_model_rotation="0|-180|0",}
},

shenqi_meta_table_map={
},
node={
[1100]={node_id=1100,reward_item={[0]=item_table[2]},is_need_remind=0,next_node_id=1111,goto_type=1,},
[1101]={node_id=1101,pre_node=1100,condition_id=1101,reward_item={[0]=item_table[3]},is_need_remind=0,next_node_id=1111,light_desc="完成任务：在商城购买宠物蛋（<per>0/1</per>）",goto_type=1,},
[1102]={node_id=1102,pre_node=1101,condition_id=1102,reward_item={[0]=item_table[4]},is_need_remind=0,next_node_id=1111,light_desc="拥有1只 <per>0/3</per> 级的宠物",view_path="PetView",},
[1103]={node_id=1103,pre_node=1102,condition_id=1103,reward_skill_id=111,light_desc="完成任务：探明前方妖气来源（<per>0/1</per>）",},
[1104]={node_id=1104,pre_node=1103,condition_id=1104,light_desc="拥有1只 <per>0/5</per> 级宠物",},
[1105]={node_id=1105,pre_node=1104,condition_id=1105,reward_item={[0]=item_table[5]},is_need_remind=0,next_node_id=1111,light_desc="完成任务：击败烈焰穷奇（<per>0/1</per>）",goto_type=1,},
[1106]={node_id=1106,pre_node=1105,condition_id=1106,reward_item={[0]=item_table[2]},is_need_remind=0,next_node_id=1111,light_desc=" <per>0/3</per> 个技能提升到10级 ",view_path="skill_view",},
[1107]={node_id=1107,pre_node=1106,condition_id=1107,reward_skill_id=112,next_node_id=1111,light_desc="完成任务：挑战魔王烈火狻猊（<per>0/1</per>）",},
[1108]={node_id=1108,pre_node=1107,condition_id=1108,reward_item={[0]=item_table[6]},is_need_remind=0,next_node_id=1111,light_desc="装备强化总等级达到 <per>0/5</per> 级",view_path="equipment#equipment_strength",},
[1109]={node_id=1109,pre_node=1108,condition_id=1109,reward_item={[0]=item_table[6]},is_need_remind=0,next_node_id=1111,light_desc="将坐骑进阶到一阶2星（<per>0/1</per>）",view_path="mount_lingchong",},
[1110]={node_id=1110,pre_node=1109,condition_id=1110,reward_item={[0]=item_table[7]},is_need_remind=0,next_node_id=1111,light_desc="角色等级达到 <per>0/35</per> 级",goto_type=1,},
[1111]={node_id=1111,pre_node=1110,condition_id=1111,reward_skill_id=113,node_type=1,is_need_remind=1,light_desc="完成任务：购买御仙之翼（<per>0/1</per>）",goto_type=1,},
[1200]={node_id=1200,condition_id=1200,reward_item={[0]=item_table[8]},is_need_remind=0,next_node_id=1211,light_desc="羽翼等级提升到 <per>0/2</per> 级",view_path="role_view#role_wing",},
[1201]={node_id=1201,pre_node=1200,condition_id=1201,reward_item={[0]=item_table[9]},is_need_remind=0,next_node_id=1211,light_desc="完成任务：购买猪小戒（<per>0/1</per>）",goto_type=1,},
[1202]={node_id=1202,pre_node=1201,condition_id=1202,light_desc="完成任务：挑战魔王上古邪魔（<per>0/1</per>）",},
[1203]={node_id=1203,pre_node=1202,condition_id=1203,reward_skill_id=121,is_need_remind=0,next_node_id=1211,light_desc="完成任务：购买定风珠（<per>0/1</per>）",},
[1204]={node_id=1204,pre_node=1203,condition_id=1204,reward_item={[0]=item_table[2]},is_need_remind=0,next_node_id=1211,light_desc="法器等级提升至 <per>0/2</per> 级",view_path="role_view#role_fabao",},
[1205]={node_id=1205,pre_node=1204,condition_id=1205,reward_item={[0]=item_table[10]},is_need_remind=0,next_node_id=1211,light_desc=" <per>0/4</per> 个技能全部达到20级",view_path="skill_view",},
[1206]={node_id=1206,pre_node=1205,condition_id=1206,reward_item={[0]=item_table[10]},is_need_remind=0,next_node_id=1211,light_desc="完成任务：解救蜀山圣女（<per>0/1</per>）",goto_type=1,},
[1207]={node_id=1207,pre_node=1206,condition_id=1207,reward_skill_id=122,light_desc="完成任务：收集暗影残魂（<per>0/1</per>）",},
[1208]={node_id=1208,pre_node=1207,condition_id=1208,reward_item={[0]=item_table[11]},is_need_remind=0,next_node_id=1211,light_desc="神灵等级提升到 <per>0/2</per> 级",view_path="TianShenView#tianshen_upgrade",},
[1209]={node_id=1209,pre_node=1208,condition_id=1209,reward_item={[0]=item_table[11]},is_need_remind=0,next_node_id=1211,light_desc="击杀心魔牢狱天音琴妖 <per>0/1</per> 次 ",view_path="boss|20|0|63",},
[1210]={node_id=1210,pre_node=1209,condition_id=1210,reward_item={[0]=item_table[3]},is_need_remind=0,next_node_id=1211,light_desc="完成任务：探索金蟾宝库（<per>0/1</per>）",goto_type=1,},
[1211]={node_id=1211,pre_node=1210,condition_id=1211,reward_skill_id=123,node_type=1,is_need_remind=1,light_desc="角色等级达到 <per>0/65</per> 级",goto_type=1,},
[1300]={node_id=1300,condition_id=1300,reward_item={[0]=item_table[12]},light_desc=" <per>0/4</per> 个技能全部达到30级",view_path="skill_view",},
[1301]={node_id=1301,pre_node=1300,condition_id=1301,reward_item={[0]=item_table[13]},light_desc="击杀心魔牢狱洪荒赤魔 <per>0/1</per> 次",view_path="boss|20|0|64",},
[1302]={node_id=1302,pre_node=1301,condition_id=1302,reward_item={[0]=item_table[14]},light_desc="完成任务：营救圣灵仙子（<per>0/1</per>）",goto_type=1,},
[1303]={node_id=1303,pre_node=1302,condition_id=1303,reward_skill_id=131,node_type=1,light_desc="完成任务：挑战伏魔战场（<per>0/1</per>）",goto_type=1,},
[1304]={node_id=1304,pre_node=1303,condition_id=1304,reward_item={[0]=item_table[15]},light_desc="击杀心魔牢狱通臂神猿 <per>0/1</per> 次",view_path="boss|20|0|65",},
[1305]={node_id=1305,pre_node=1304,condition_id=1305,reward_item={[0]=item_table[16]},light_desc="完成任务：送喜帖至御影门(<per>0/1</per>)",goto_type=1,},
[1306]={node_id=1306,pre_node=1305,condition_id=1306,light_desc="完成任务：解决白廷的烦恼（<per>0/1</per>）",},
[1307]={node_id=1307,pre_node=1306,condition_id=1307,reward_skill_id=132,node_type=1,light_desc="穿戴 <per>0/1</per> 件紫色以上的仙镯 ",view_path="bag_view#rolebag_bag_all",},
[1308]={node_id=1308,pre_node=1307,condition_id=1308,reward_item={[0]=item_table[17]},light_desc="完成任务：帮助老僧解决心魔（<per>0/1</per>）",goto_type=1,},
[1309]={node_id=1309,pre_node=1308,condition_id=1309,reward_item={[0]=item_table[18]},light_desc="完成角色 <per>0/1</per> 转",view_path="transfer",},
[1310]={node_id=1310,pre_node=1309,condition_id=1310,reward_item={[0]=item_table[19]},light_desc="完成任务：绝世神兵（<per>0/1</per>）",goto_type=1,},
[1311]={node_id=1311,pre_node=1310,condition_id=1311,reward_skill_id=133,node_type=1,light_desc="角色等级达到 <per>0/170</per> 级",goto_type=1,},
[2100]={node_id=2100,condition_id=2100,reward_item={[0]=item_table[20]},light_desc="上阵 <per>0/2</per> 只宠物",view_path="PetView#pet_fight",},
[2101]={node_id=2101,pre_node=2100,condition_id=2101,reward_item={[0]=item_table[21]},light_desc="穿戴 <per>0/7</per> 件五阶1星以上红装",view_path="bag_view#rolebag_bag_all",},
[2102]={node_id=2102,pre_node=2101,condition_id=2102,reward_item={[0]=item_table[22]},light_desc="穿戴 <per>0/1</per> 件五阶3星以上红装",view_path="bag_view#rolebag_bag_all",},
[2103]={node_id=2103,pre_node=2102,condition_id=2103,reward_skill_id=211,node_type=1,light_desc="次日登陆 <per>0/1</per> 次",goto_type=1,},
[2104]={node_id=2104,pre_node=2103,condition_id=2104,reward_item={[0]=item_table[4]},light_desc="上阵 <per>0/2</per> 只紫色以上宠物",view_path="PetView#pet_fight",},
[2105]={node_id=2105,pre_node=2104,condition_id=2105,light_desc="上阵 <per>0/2</per> 只15级以上宠物",},
[2106]={node_id=2106,pre_node=2105,condition_id=2106,reward_item={[0]=item_table[5]},light_desc="上阵 <per>0/1</per> 只3阶以上宠物",view_path="PetView#pet_garde_view",},
[2107]={node_id=2107,pre_node=2106,condition_id=2107,reward_skill_id=212,node_type=1,light_desc="角色等级达到 <per>0/180</per> 级",goto_type=1,},
[2108]={node_id=2108,pre_node=2107,condition_id=2108,reward_item={[0]=item_table[23]},light_desc="开启奇门-八卦",goto_type=1,},
[2109]={node_id=2109,pre_node=2108,condition_id=2109,reward_item={[0]=item_table[24]},light_desc="镶嵌 <per>0/3</per> 个蓝色以上八卦印",view_path="fuwen#fuwen_xiangqian",},
[2110]={node_id=2110,pre_node=2109,condition_id=2110,reward_item={[0]=item_table[25]},light_desc="镶嵌 <per>0/1</per> 个5级以上八卦印",view_path="fuwen#fuwen_xiangqian",},
[2111]={node_id=2111,pre_node=2110,condition_id=2111,reward_skill_id=213,node_type=1,light_desc="镶嵌 <per>0/1</per> 个紫色以上八卦印",view_path="fuwen#fuwen_xiangqian",},
[2200]={node_id=2200,condition_id=2200,reward_item={[0]=item_table[2]},light_desc="开启装备洗炼",},
[2201]={node_id=2201,pre_node=2200,condition_id=2201,reward_item={[0]=item_table[26]},light_desc="进行 <per>0/10</per> 次装备洗炼",view_path="equipment#equipment_xilian",},
[2202]={node_id=2202,pre_node=2201,condition_id=2202,reward_item={[0]=item_table[27]},light_desc="拥有 <per>0/2</per> 条紫色以上洗炼属性",view_path="equipment#equipment_xilian",},
[2203]={node_id=2203,pre_node=2202,condition_id=2203,reward_skill_id=221,node_type=1,light_desc="角色等级达到 <per>0/220</per> 级",goto_type=1,},
[2204]={node_id=2204,pre_node=2203,condition_id=2204,reward_item={[0]=item_table[28]},light_desc="完成任务：购买天绝剑阵（<per>0/1</per>）",goto_type=1,},
[2205]={node_id=2205,pre_node=2204,condition_id=2205,reward_item={[0]=item_table[28]},light_desc="神灵等级提升到 <per>0/10</per> 级",view_path="TianShenView#tianshen_upgrade",},
[2206]={node_id=2206,pre_node=2205,condition_id=2206,reward_item={[0]=item_table[29]},light_desc="拥有 <per>0/1</per> 张紫色以上图鉴",view_path="ShanHaiJingView",},
[2207]={node_id=2207,pre_node=2206,condition_id=2207,reward_skill_id=222,node_type=1,light_desc="激活 <per>0/1</per> 套紫色以上图鉴羁绊",view_path="ShanHaiJingView",},
[2208]={node_id=2208,pre_node=2207,condition_id=2208,light_desc="装备强化总等级到 <per>0/70</per> 级",},
[2209]={node_id=2209,pre_node=2208,condition_id=2209,reward_item={[0]=item_table[8]},light_desc="仙器强化总等级到 <per>0/10</per> 级",view_path="equipment#equipment_strength",},
[2210]={node_id=2210,pre_node=2209,condition_id=2210,reward_item={[0]=item_table[29]},light_desc=" <per>0/4</per> 个技能全部达到80级",view_path="skill_view",},
[2211]={node_id=2211,pre_node=2210,condition_id=2211,reward_skill_id=223,node_type=1,light_desc="穿戴 <per>0/4</per> 件五阶3星以上红装",view_path="bag_view#rolebag_bag_all",},
[2300]={node_id=2300,condition_id=2300,reward_item={[0]=item_table[30]},light_desc="修仙达到元婴期（<per>0/30</per>）",view_path="jingjie",},
[2301]={node_id=2301,pre_node=2300,condition_id=2301,reward_item={[0]=item_table[31]},light_desc="完成角色 <per>0/2</per> 转",view_path="transfer#transfer_2",},
[2302]={node_id=2302,pre_node=2301,condition_id=2302,reward_item={[0]=item_table[32]},light_desc="进入 <per>0/1</per> 次天道苍茫",view_path="boss#boss_dabao",},
[2303]={node_id=2303,pre_node=2302,condition_id=2303,reward_skill_id=231,node_type=1,light_desc="炼器 <per>0/1</per> 件5阶以上基础诛魔装备",view_path="equipment#equipment_suit",},
[2304]={node_id=2304,pre_node=2303,condition_id=2304,reward_item={[0]=item_table[33]},light_desc="角色等级达到 <per>0/250</per> 级",goto_type=1,},
[2305]={node_id=2305,pre_node=2304,condition_id=2305,reward_item={[0]=item_table[34]},light_desc="获得 <per>0/1</per> 个幼鲲元神·紫",view_path=27923,goto_type=3,},
[2306]={node_id=2306,pre_node=2305,condition_id=2306,reward_item={[0]=item_table[35]},light_desc="孵化 <per>0/1</per> 只幼鲲元神·紫",view_path="mount_lingchong#mount_lingchong_duhua",},
[2307]={node_id=2307,pre_node=2306,condition_id=2307,reward_skill_id=232,node_type=1,light_desc="上阵 <per>0/3</per> 只紫色以上的宠物",view_path="PetView#pet_fight",},
[2308]={node_id=2308,pre_node=2307,condition_id=2308,reward_item={[0]=item_table[4]},light_desc="拥有1只 <per>0/20</per> 级宠物",view_path="PetView",},
[2309]={node_id=2309,pre_node=2308,condition_id=2309,reward_item={[0]=item_table[6]},light_desc="坐骑等级进阶到三阶以上（<per>0/1</per>）",view_path="mount_lingchong",},
[2310]={node_id=2310,pre_node=2309,condition_id=2310,reward_item={[0]=item_table[36]},light_desc="坐骑等级进阶到四阶以上（<per>0/1</per>）",view_path="mount_lingchong",},
[2311]={node_id=2311,pre_node=2310,condition_id=2311,reward_skill_id=233,node_type=1,light_desc="角色等级达到 <per>0/260</per> 级",goto_type=1,},
[3100]={node_id=3100,condition_id=3100,reward_item={[0]=item_table[10]},light_desc="法器等级提升至 <per>0/30</per> 级",view_path="role_view#role_fabao",},
[3101]={node_id=3101,pre_node=3100,condition_id=3101,reward_item={[0]=item_table[37]},light_desc="法器等级提升至 <per>0/40</per> 级",view_path="role_view#role_fabao",},
[3102]={node_id=3102,pre_node=3101,condition_id=3102,reward_item={[0]=item_table[3]},light_desc="商城累计消耗 <per>0/10000</per> 元宝",view_path="shop#Tab_Shop43",},
[3103]={node_id=3103,pre_node=3102,condition_id=3103,reward_skill_id=311,node_type=1,light_desc="拥有 <per>0/4</per> 条紫色以上洗炼属性",view_path="equipment#equipment_xilian",},
[3104]={node_id=3104,pre_node=3103,condition_id=3104,reward_item={[0]=item_table[2]},light_desc="装备强化总等级到 <per>0/100</per> 级",view_path="equipment#equipment_strength",},
[3105]={node_id=3105,pre_node=3104,condition_id=3105,reward_item={[0]=item_table[38]},light_desc="进行 <per>0/3</per> 次装备升品",view_path="equipment#equipment_shengpin",},
[3106]={node_id=3106,pre_node=3105,condition_id=3106,reward_item={[0]=item_table[39]},light_desc="商城累计消耗 <per>0/15000</per> 元宝",view_path="shop#Tab_Shop43",},
[3107]={node_id=3107,pre_node=3106,condition_id=3107,reward_skill_id=312,node_type=1,light_desc="出战 <per>0/2</per> 位神灵",view_path="TianShenView#TianShenActivationView",},
[3108]={node_id=3108,pre_node=3107,condition_id=3108,reward_item={[0]=item_table[11]},light_desc="神灵等级提升到 <per>0/20</per> 级",view_path="TianShenView#tianshen_upgrade",},
[3109]={node_id=3109,pre_node=3108,condition_id=3109,reward_item={[0]=item_table[40]},light_desc="神灵等级提升到 <per>0/30</per> 级",view_path="TianShenView#tianshen_upgrade",},
[3110]={node_id=3110,pre_node=3109,condition_id=3110,reward_item={[0]=item_table[41]},light_desc="商城累计消耗 <per>0/20000</per> 元宝",view_path="shop#Tab_Shop43",},
[3111]={node_id=3111,pre_node=3110,condition_id=3111,reward_skill_id=313,node_type=1,light_desc="激活上古神灵-蚩尤（<per>0/1</per>）",view_path="ShangGuJingLingView",},
[3200]={node_id=3200,condition_id=3200,reward_item={[0]=item_table[30]},light_desc="角色等级达到 <per>0/280</per> 级",goto_type=1,},
[3201]={node_id=3201,pre_node=3200,condition_id=3201,reward_item={[0]=item_table[20]},light_desc=" <per>0/4</per> 个技能全部达到100级",view_path="skill_view",},
[3202]={node_id=3202,pre_node=3201,condition_id=3202,reward_item={[0]=item_table[32]},light_desc="炼器 <per>0/2</per> 件5阶以上基础诛魔装备",view_path="equipment#equipment_suit",},
[3203]={node_id=3203,pre_node=3202,condition_id=3203,reward_skill_id=321,node_type=1,light_desc="修仙达到渡劫期（<per>0/40</per>） ",view_path="jingjie",},
[3204]={node_id=3204,pre_node=3203,condition_id=3204,reward_item={[0]=item_table[42]},light_desc="激活 <per>0/1</per> 只蓝色以上的神兽",view_path="shenshou#shenshou_shenshou",},
[3205]={node_id=3205,pre_node=3204,condition_id=3205,reward_item={[0]=item_table[43]},light_desc=" <per>0/1</per> 件神兽装备强化至5级",view_path="ShenShouStrengthenView",},
[3206]={node_id=3206,pre_node=3205,condition_id=3206,reward_item={[0]=item_table[44]},light_desc="激活 <per>0/2</per> 只紫色以上的神兽",view_path="shenshou#shenshou_shenshou",},
[3207]={node_id=3207,pre_node=3206,condition_id=3207,reward_skill_id=322,node_type=1,light_desc="蚩尤全身神格总阶数达到 <per>0/10</per> 阶",view_path="ShangGuJingLingView",},
[3208]={node_id=3208,pre_node=3207,condition_id=3208,reward_item={[0]=item_table[9]},light_desc="羽翼等级提升到 <per>0/40</per> 级",view_path="role_view#role_wing",},
[3209]={node_id=3209,pre_node=3208,condition_id=3209,light_desc="羽翼等级提升到 <per>0/50</per> 级",},
[3210]={node_id=3210,pre_node=3209,condition_id=3210,reward_item={[0]=item_table[45]},light_desc="激活 <per>0/1</per> 个1阶以上羽翼外观",view_path="appearance_yuling#appearance_image_wing",},
[3211]={node_id=3211,pre_node=3210,condition_id=3211,reward_skill_id=323,node_type=1,light_desc="角色等级达到 <per>0/300</per> 级",goto_type=1,},
[4100]={node_id=4100,condition_id=4100,reward_item={[0]=item_table[7]},light_desc="装备强化总等级到 <per>0/150</per> 级",view_path="equipment#equipment_strength",},
[4101]={node_id=4101,pre_node=4100,condition_id=4101,light_desc="仙器强化总等级到 <per>0/40</per> 级",},
[4102]={node_id=4102,pre_node=4101,condition_id=4102,reward_item={[0]=item_table[17]},light_desc="玉魄镶嵌总等级达到 <per>0/15</per> 级",view_path="equipment#equipment_baoshi",},
[4103]={node_id=4103,pre_node=4102,condition_id=4103,reward_skill_id=411,node_type=1,light_desc="炼器 <per>0/3</per> 件5阶以上基础诛魔装备",view_path="equipment#equipment_suit",},
[4104]={node_id=4104,pre_node=4103,condition_id=4104,light_desc="拥有 <per>0/6</per> 条紫色以上洗炼属性",},
[4105]={node_id=4105,pre_node=4104,condition_id=4105,light_desc="对项链进行 <per>0/1</per> 次洗炼",},
[4106]={node_id=4106,pre_node=4105,condition_id=4106,reward_item={[0]=item_table[18]},light_desc="玉魄镶嵌总等级达到 <per>0/20</per> 级",view_path="equipment#equipment_baoshi",},
[4107]={node_id=4107,pre_node=4106,condition_id=4107,reward_skill_id=412,node_type=1,light_desc="激活 <per>0/1</per> 只橙色以上的神兽",view_path="shenshou#shenshou_shenshou",},
[4108]={node_id=4108,pre_node=4107,condition_id=4108,light_desc="拥有 <per>0/1</per> 张橙色品质图鉴",},
[4109]={node_id=4109,pre_node=4108,condition_id=4109,reward_item={[0]=item_table[15]},light_desc="激活 <per>0/2</per> 套紫色以上图鉴羁绊",view_path="ShanHaiJingView",},
[4110]={node_id=4110,pre_node=4109,condition_id=4110,reward_item={[0]=item_table[46]},light_desc="玉魄镶嵌总等级达到 <per>0/25</per> 级",view_path="equipment#equipment_baoshi",},
[4111]={node_id=4111,pre_node=4110,condition_id=4111,reward_skill_id=413,node_type=1,light_desc="角色等级达到 <per>0/315</per> 级",goto_type=1,},
[4200]={node_id=4200,condition_id=4200,reward_item={[0]=item_table[22]},light_desc="蚩尤全身神格总阶数达到 <per>0/20</per> 阶",view_path="ShangGuJingLingView",},
[4201]={node_id=4201,pre_node=4200,condition_id=4201,reward_item={[0]=item_table[2]},light_desc=" <per>0/4</per> 个技能全部达到110级",view_path="ShangGuJingLingView",},
[4202]={node_id=4202,pre_node=4201,condition_id=4202,reward_item={[0]=item_table[20]},light_desc="仙器强化总等级到 <per>0/60</per> 级",view_path="equipment#equipment_strength",},
[4203]={node_id=4203,pre_node=4202,condition_id=4203,reward_skill_id=421,node_type=1,light_desc="修仙达到化神期（<per>0/50</per>）",view_path="jingjie",},
[4204]={node_id=4204,pre_node=4203,condition_id=4204,reward_item={[0]=item_table[47]},light_desc="进行 <per>0/10</per> 次挖宝",view_path="fubenpanel#fubenpanel_bootybay",},
[4205]={node_id=4205,pre_node=4204,condition_id=4205,light_desc="商城累计消耗 <per>0/40000</per> 元宝",},
[4206]={node_id=4206,pre_node=4205,condition_id=4206,light_desc="蚩尤全身神格总阶数达到 <per>0/30</per> 阶",},
[4207]={node_id=4207,pre_node=4206,condition_id=4207,reward_skill_id=422,node_type=1,light_desc="拥有 <per>0/1</per> 件紫色以上戒指",view_path="bag_view#rolebag_bag_all",},
[4208]={node_id=4208,pre_node=4207,condition_id=4208,reward_item={[0]=item_table[48]},light_desc="获得 <per>0/1</per> 个紫色龙魂",view_path=22492,goto_type=3,},
[4209]={node_id=4209,pre_node=4208,condition_id=4209,reward_item={[0]=item_table[49]},light_desc="镶嵌 <per>0/3</per> 个紫色龙魂",view_path="fuwen#long_hun_xiangqian",},
[4210]={node_id=4210,pre_node=4209,condition_id=4210,light_desc="蚩尤全身神格总阶数达到 <per>0/40</per> 阶",},
[4211]={node_id=4211,pre_node=4210,condition_id=4211,reward_skill_id=423,node_type=1,light_desc="炼器 <per>0/4</per> 件5阶以上基础诛魔装备",view_path="equipment#equipment_suit",}
},

node_meta_table_map={
[2200]=3200,	-- depth:1
[2208]=4100,	-- depth:1
[4210]=4200,	-- depth:1
[2105]=2308,	-- depth:1
[4104]=2201,	-- depth:1
[4206]=4200,	-- depth:1
[4205]=3110,	-- depth:1
[4108]=4109,	-- depth:1
[4105]=2202,	-- depth:1
[4101]=2209,	-- depth:1
[3209]=3208,	-- depth:1
[1306]=2200,	-- depth:2
[1104]=1102,	-- depth:1
[1202]=1201,	-- depth:1
[1203]=1211,	-- depth:1
[1107]=1203,	-- depth:2
[1207]=1203,	-- depth:2
[1103]=1107,	-- depth:3
},
passive_skill={
[11]={skill_seq=11,param1="gongji",param3="fangyu",param4=250,desc="攻击 <color=#009621>+250</color>\n防御 <color=#009621>+250</color>",desc_2="攻击 <color=#fffc00>+250</color>\n防御 <color=#fffc00>+250</color>",},
[13]={skill_seq=13,skill_type=3,param1=1000,param2=0,skill_name="<color=#bb01c3>宝塔借力</color>",skill_index=21,desc="角色杀怪经验<color=#009621>+10%</color>",desc_2="角色杀怪经验<color=#fffc00>+10%</color>",},
[21]={skill_seq=21,param1="zengshang_boss_per",param2=500,param3=1,skill_name="<color=#bb01c3>神龙刻印</color>",skill_index=22,desc="对BOSS造成伤害提升<color=#009621>5%</color>",desc_2="对BOSS造成伤害提升<color=#fffc00>5%</color>",},
[22]={skill_seq=22,skill_type=2,param1="baoji_per",param3=2,skill_name="<color=#bb01c3>玉瓶凛凛</color>",skill_index=23,desc="暴击率<color=#009621>+2.5%</color>",desc_2="暴击率<color=#fffc00>+2.5%</color>",},
[23]={skill_seq=23,param1="lianji_per",param3=4,skill_name="<color=#bb01c3>神璧无瑕</color>",skill_index=24,desc="连击率<color=#009621>+2.5%</color>",desc_2="连击率<color=#fffc00>+2.5%</color>",},
[31]={skill_seq=31,skill_type=2,param1="jianshang_per",param3=3,skill_name="<color=#bb01c3>莲盏护体</color>",skill_index=25,desc="玩家减伤<color=#009621>+5%</color>",desc_2="玩家减伤<color=#fffc00>+5%</color>",},
[41]={skill_seq=41,skill_type=2,param1="jichuan_per",param3=5,skill_name="<color=#bb01c3>白鹤留影</color>",skill_index=26,desc="击穿率<color=#009621>+2.5%</color>",desc_2="击穿率<color=#fffc00>+2.5%</color>",},
[42]={skill_seq=42,percent_val=2500,skill_type=4,param1=100,param2=0,attack_power=10000,defence_power=10000,skill_name="<color=#bb01c3>黄泉引路</color>",skill_index=27,desc="组队时，<color=#009621>25%</color>几率对敌对玩家造成伤害提升<color=#009621>10%</color>",desc_2="组队时，<color=#fffc00>25%</color>几率对敌对玩家造成伤害提升<color=#fffc00>10%</color>",},
[111]={skill_seq=111,param1="fangyu",param2=200,skill_name="<color=#bb01c3>结魄灯芯</color>",skill_index=28,desc="防御<color=#009621>+200</color>",},
[112]={skill_seq=112,param1="pojia",param2=200,skill_name="<color=#bb01c3>结魄虚影</color>",skill_index=29,desc="破甲<color=#009621>+200</color>",},
[113]={skill_seq=113,param1="gongji",param2=200,skill_name="<color=#bb01c3>结魄魔光</color>",skill_index=30,desc="攻击<color=#009621>+200</color>",},
[121]={skill_seq=121,param2=5000,skill_name="<color=#bb01c3>渡心镜面</color>",skill_index=31,desc="生命<color=#009621>+5000</color>",},
[122]={skill_seq=122,param1="yuansu_hj",param2=200,skill_name="<color=#bb01c3>渡心虚妄</color>",skill_index=32,desc="天道护甲<color=#009621>+200</color>",},
[123]={skill_seq=123,param1="yuansu_sh",param2=200,skill_name="<color=#bb01c3>渡心复像</color>",skill_index=33,desc="天道伤害<color=#009621>+200</color>",},
[131]={skill_seq=131,param1="fangyu",skill_name="<color=#bb01c3>天王塔灵</color>",skill_index=34,desc="防御<color=#009621>+250</color>",},
[132]={skill_seq=132,param1="pojia",skill_name="<color=#bb01c3>天王困身</color>",skill_index=35,desc="破甲<color=#009621>+250</color>",},
[133]={skill_seq=133,param1="gongji",skill_name="<color=#bb01c3>天王囚神</color>",skill_index=36,desc="攻击<color=#009621>+250</color>",},
[211]={skill_seq=211,param2=6250,skill_name="<color=#bb01c3>龙神印刻</color>",skill_index=37,desc="生命<color=#009621>+6250</color>",},
[212]={skill_seq=212,param1="yuansu_hj",skill_name="<color=#bb01c3>龙神威严</color>",skill_index=38,desc="天道护甲<color=#009621>+250</color>",},
[213]={skill_seq=213,param1="yuansu_sh",skill_name="<color=#bb01c3>龙神压顶</color>",skill_index=39,desc="天道伤害<color=#009621>+250</color>",},
[221]={skill_seq=221,skill_name="<color=#bb01c3>玉净瓶心</color>",skill_index=40,},
[222]={skill_seq=222,param1="fangyu",param2=150,param3="pojia",param4=150,skill_name="<color=#bb01c3>玉净圣水</color>",skill_index=41,desc="防御<color=#009621>+150</color>,破甲<color=#009621>+150</color>",},
[223]={skill_seq=223,param1="yuansu_hj",param2=150,param3="yuansu_sh",param4=150,skill_name="<color=#bb01c3>玉净灌江</color>",skill_index=42,desc="天道护甲<color=#009621>+150</color>,天道伤害<color=#009621>+150</color>",},
[231]={skill_seq=231,param2=3750,param3="gongji",param4=150,skill_name="<color=#bb01c3>沧海琴玄</color>",skill_index=43,},
[232]={skill_seq=232,param1="fangyu",param2=150,param3="gongji",param4=150,skill_name="<color=#bb01c3>沧海音波</color>",skill_index=44,desc="防御<color=#009621>+150</color>,攻击<color=#009621>+150</color>",},
[233]={skill_seq=233,param2=3750,param3="yuansu_sh",param4=150,skill_name="<color=#bb01c3>沧海音杀</color>",skill_index=45,desc="生命<color=#009621>+3750</color>,天道伤害<color=#009621>+150</color>",},
[311]={skill_seq=311,skill_name="<color=#bb01c3>女娲璧环</color>",skill_index=46,},
[312]={skill_seq=312,param1="fangyu",param2=175,param3="pojia",param4=175,skill_name="<color=#bb01c3>女娲福泽</color>",skill_index=47,desc="防御<color=#009621>+175</color>,破甲<color=#009621>+175</color>",},
[313]={skill_seq=313,param1="yuansu_hj",param2=175,param3="yuansu_sh",param4=175,skill_name="<color=#bb01c3>女娲怒气</color>",skill_index=48,desc="天道护甲<color=#009621>+175</color>,天道伤害<color=#009621>+175</color>",},
[321]={skill_seq=321,param2=4375,param3="gongji",param4=175,skill_name="<color=#bb01c3>续命香油</color>",skill_index=49,desc="生命<color=#009621>+4375</color>,攻击<color=#009621>+175</color>",},
[322]={skill_seq=322,param1="fangyu",param2=175,param3="gongji",param4=175,skill_name="<color=#bb01c3>续命驱邪</color>",skill_index=50,desc="防御<color=#009621>+175</color>,攻击<color=#009621>+175</color>",},
[323]={skill_seq=323,param2=4375,param3="yuansu_sh",param4=175,skill_name="<color=#bb01c3>续命明火</color>",skill_index=51,desc="生命<color=#009621>+4375</color>,天道伤害<color=#009621>+175</color>",},
[411]={skill_seq=411,param2=3333,param4=133,param5="shanghai_zs",param6=89,skill_name="<color=#bb01c3>鹤留遗影</color>",skill_index=52,desc="生命<color=#009621>+3333</color>,攻击<color=#009621>+133</color>\n真实伤害<color=#009621>+89</color>",},
[412]={skill_seq=412,param3="pojia",param5="fangyu_zs",param6=89,skill_name="<color=#bb01c3>鹤留乘风</color>",skill_index=53,desc="防御<color=#009621>+133</color>,破甲<color=#009621>+133</color>\n真实防御<color=#009621>+89</color>",},
[413]={skill_seq=413,param1="yuansu_hj",param2=133,param3="yuansu_sh",skill_name="<color=#bb01c3>鹤留破邪</color>",skill_index=54,desc="天道护甲<color=#009621>+133</color>,天道伤害<color=#009621>+133</color>\n真实伤害<color=#009621>+89</color>",},
[421]={skill_seq=421,param5="fangyu_zs",skill_name="<color=#bb01c3>黄泉剑刃</color>",skill_index=55,desc="生命<color=#009621>+3333</color>,攻击<color=#009621>+133</color>\n真实防御<color=#009621>+89</color>",},
[422]={skill_seq=422,param2=133,param4=133,param5="shengming_qq",param6=44,skill_name="<color=#bb01c3>黄泉斩鬼</color>",skill_index=56,desc="防御<color=#009621>+133</color>,攻击<color=#009621>+133</color>\n生命窃取<color=#009621>+44</color>",},
[423]={skill_seq=423,param3="yuansu_sh",param5="fangtan",param6=44,skill_name="<color=#bb01c3>黄泉灭魂</color>",skill_index=57,desc="生命<color=#009621>+3333</color>,天道伤害<color=#009621>+133</color>\n反弹伤害<color=#009621>+44</color>",}
},

passive_skill_meta_table_map={
[221]=231,	-- depth:1
[311]=321,	-- depth:1
[21]=41,	-- depth:1
[23]=21,	-- depth:2
[411]=321,	-- depth:1
[421]=411,	-- depth:2
[423]=411,	-- depth:2
[422]=232,	-- depth:1
[412]=422,	-- depth:2
[413]=411,	-- depth:2
},
condition_default_table={condition_id=1000,condition_type=102,param1=1,param2="",param3="",param4="",desc="第1章法器2开启",},

other_default_table={tianjipu_condition_id=1000,},

chapter_default_table={chapter_id=1,shenqi_id_list="11|12|13",pre_chapter="",condition_id="",},

shenqi_default_table={shenqi_id=11,begin_node_id=1100,end_node_id=1112,pre_shenqi="",condition_id="",reward_item={},reward_skill_id_list="",open_xinmo_fb="",shenqi_name="结魄灯",cutoutIimage=1,model_id=1,model_size=180,open_condition_desc="修复前置法器",model_pos="0|-1|0",model_rotation="0|0|0",detail_model_rotation="0|0|0",},

node_default_table={node_id=1100,pre_node="",condition_id=1100,reward_item={},reward_skill_id="",node_type=2,is_need_remind=2,next_node_id=0,light_desc="完成任务：支援青衣师妹（<per>0/1</per>）",view_path=0,goto_type=2,},

passive_skill_default_table={skill_seq=11,cd_time_s=0,percent_val=10000,skill_type=1,param1="shengming_max",param2=250,param3=0,param4=0,param5=0,param6=0,attack_power=0,defence_power=0,capability_inc=0,skill_name="<color=#bb01c3>结魄固魂</color>",skill_index=20,desc="生命<color=#009621>+3750</color>,攻击<color=#009621>+150</color>",desc_2="",}

}

