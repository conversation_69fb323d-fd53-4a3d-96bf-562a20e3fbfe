JumpPoint = JumpPoint or BaseClass(SceneObj)

function JumpPoint:__init(vo)
	self.obj_type = SceneObjType.JumpPoint
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.JumpPoint
	self.is_available = true
end

function JumpPoint:InitAppearance()
	if self.vo.target_id ~= 0 and self.vo.is_show == 1 then
		if self.vo.offset then
			local offset = self.vo.offset
			self.draw_obj:SetOffset(Vector3(offset[1], offset[2], offset[3]))
		end
		local bundle_name, prefab_name = ResPath.GetEnvironmentCommonEffect("eff_tiaoyuedian")
		self:ChangeModel(SceneObjPart.Main, bundle_name, prefab_name)
	else
		self:ChangeModel(SceneObjPart.Main, nil, nil)
	end
end

function JumpPoint:CanShowJump(value)
	-- body
	self.vo.is_show = value
	self:InitAppearance()
end

function JumpPoint:__delete()
	self.is_available = true
end

function JumpPoint:GetObjKey()
	return self.vo.id
end

function JumpPoint:IsJumpPoint()
	return true
end

function JumpPoint:GetFollowUi()
	return nil
end

function JumpPoint:GetIsAvailable()
	return self.is_available
end

function JumpPoint:SetIsAvailable(is_available)
	self.is_available = is_available
end