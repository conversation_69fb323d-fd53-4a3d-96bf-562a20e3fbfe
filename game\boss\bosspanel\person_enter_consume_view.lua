--专属魔王进入消耗物品界面
PersonEnterCosumeView = PersonEnterCosumeView or BaseClass(SafeBaseView)

function PersonEnterCosumeView:__init()
	self.is_modal = true
	self.is_any_click_close = true
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
    self.view_name = "PersonEnterCosumeView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(612, 364)})
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_person_enter_boss")
    self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function PersonEnterCosumeView:ReleaseCallBack()
	if nil ~=  self.comsun_cell then
		self.comsun_cell:DeleteMe()
		self.comsun_cell = nil
	end

    if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end
    
	self.tiky_item_id = nil
	self.enter_comsun = nil
	self.map_tip = nil
	self.consum_tip = nil
	self.ok_func = nil
end


function PersonEnterCosumeView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
    local cfg = BossWGData.Instance:GetPersonBossOtherCfg()
    if cfg and cfg.enter_item_id then
        if item_id == cfg.enter_item_id then
            self:Flush()
        end
    end
end

function PersonEnterCosumeView:LoadCallBack()
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.node_list.title_view_name.text.text = Language.Common.AlertTitile

	self.node_list["btn_enter_dabao"].button:AddClickListener(BindTool.Bind1(self.OnBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_cancle, BindTool.Bind1(self.Close, self))
	if nil ==  self.comsun_cell then
		self.comsun_cell = ItemCell.New(self.node_list["ph_cell"])
		self.comsun_cell:SetNeedItemGetWay(true)
	end
end

function PersonEnterCosumeView:SetData(ok_func)
    self.ok_func = ok_func
	self:Open()
end

function PersonEnterCosumeView:OnFlush()
    local cfg = BossWGData.Instance:GetPersonBossOtherCfg()
    if cfg and cfg.enter_item_id then
        self.tiky_item_id = cfg.enter_item_id
        self.enter_comsun = cfg.enter_item_num
    else
        print_error("The PersonBossCfg about enter_item_id is nil")
        return
    end

	local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(self.tiky_item_id)
	self.comsun_cell:SetData({item_id = self.tiky_item_id})
	local color = has_tiky_num >= self.enter_comsun and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.comsun_cell:SetRightBottomColorText(has_tiky_num.."/"..self.enter_comsun, color)
	self.comsun_cell:SetRightBottomTextVisible(true)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.tiky_item_id)
    if item_cfg == nil then
        print_error("取不到物品配置 item_id =",self.tiky_item_id)
        return 
    end

    local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
    local cost_text = Language.Common.GoldText
	local name = item_cfg.name
	local color_1 = ITEM_COLOR[item_cfg.color]
	color_1 = color_1 or COLOR3B.GREEN
	name = ToColorStr(name, color_1)
	local tips = Split(Language.Boss.EnterPersonBoss, "\n")

    if bind_gold_num >= self.enter_comsun * cfg.enter_gold then
        cost_text = Language.Common.BindGoldText
    end 

	self.node_list["text_dabao_comsun"].tmp.text = tips[1]
    self.node_list["consume_num"].tmp.text = string.format(tips[2], name, self.enter_comsun)
    
    self.node_list["lbl_consum_money"]:SetActive(has_tiky_num < self.enter_comsun)
	self.node_list["lbl_consum_money"].tmp.text = string.format(Language.Boss.EnterBossConsum, cost_text, self.enter_comsun * cfg.enter_gold)
end

function PersonEnterCosumeView:OnBtnClick()
    local cfg = BossWGData.Instance:GetPersonBossOtherCfg()
    if cfg and cfg.enter_item_id then
        if self.ok_func then
            self:Close()
            self.ok_func()
        end
    else
        print_error("The PersonBossCfg about enter_item_id is nil")
    end
end
