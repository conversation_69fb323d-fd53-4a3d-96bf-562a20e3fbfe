BrowseWGData = BrowseWGData or BaseClass()

function BrowseWGData:__init()
	if BrowseWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[BrowseWGData] Attemp to create a singleton twice !")
	end
	BrowseWGData.Instance = self
	self.shengzhuang_table = {}
	self.role_info = {}
end

function BrowseWGData:__delete()
	BrowseWGData.Instance = nil
end

function BrowseWGData:SetRoleInfo(protocol)
	if protocol then
		self.role_info = protocol
		self.suit_order_list = nil
		self.equip_dz_suit_data = nil
		self.equip_dz_suit_open_data = nil
	end
	self:ClearOldCacheShengZhuang()
end

function BrowseWGData:GetRoleInfo()
	return self.role_info
end

function BrowseWGData:GetRoleProf()
	return self.role_info.prof
end

function BrowseWGData:GetRoleId()
	return self.role_info.role_id or 0
end

-- 设置魅力值
function BrowseWGData:SetAllCharm(all_charm)
	self.role_info.all_charm = all_charm
end

-- 小鬼
function BrowseWGData:GetXiaoGuiInfoList()
	local xiaogui_data = {}
	xiaogui_data[1] = self.role_info.xiaogui_data1
	xiaogui_data[2] = self.role_info.xiaogui_data2
	return xiaogui_data
	--return self.role_info.xiaogui_data
end
function BrowseWGData:GetXiaoGuiUseInfoList()
	local xiaogui_use_data = {}
	xiaogui_use_data[1] = self.role_info.used_imp_type_1
	xiaogui_use_data[2] = self.role_info.used_imp_type_2
	return xiaogui_use_data
end

-- 获取装备列表
function BrowseWGData:GetEquipInfoList()
	local equip_info_list = {}
	if nil ~= self.role_info.equipment_info  then --and nil ~= self.role_info.equipforge_data then
		for i, v in pairs(self.role_info.equipment_info) do
			local index = i
			local item_data = CommonStruct.ItemDataWrapper()
			item_data.item_id = v.equip_id
			item_data.num = 1
			item_data.is_bind = 0
			item_data.has_param = 1
			item_data.invalid_time = 0
			item_data.gold_price = 0
			item_data.index = index
			item_data.frombody = true
			item_data.quality_level = self.role_info.shengpin_info[i]

			item_data.param.strengthen_level = v.strengthen_level or 0					-- 强化等级
			item_data.param.star_level = v.star_level									-- 升星等级
			item_data.param.impression = v.impression or 0								-- 升星等级
			item_data.param.xianpin_type_list = v.xianpin_type_list or {}				-- 仙品属性
			item_data.param.baoshi_t = self.role_info.stone_infos and self.role_info.stone_infos[index] or {}			-- 宝石 list
			item_data.param.lingyu_t = self.role_info.lingyu_infos and self.role_info.lingyu_infos[index] or {}			-- 灵玉 list
			item_data.param.stone_baptize_level = self.role_info.stone_refine_level[index] or 0							-- 精炼等级
			item_data.param.baptize_list = v.baptize_list or {}							-- 洗练属性

			item_data.param.suit_index = v.suit_index or 0								-- 套装激活 id
			item_data.param.suit_open_num = v.suit_count or 0							-- 套装激活件数

			equip_info_list[index] = item_data
		end

		-- {
		-- 	index = index,
		-- 	item_id = v.equip_id,
		-- 	is_bind = 0,
		-- 	num = 0,
		-- 	invalid_time = 0,

		-- 	has_param = 1,

		-- 	-- 在此强调此装备是查看信息里面的数据
		-- 	is_browse = true,
		-- 	frombody = true,

		-- 	param = {
				-- strengthen_level = self.role_info.equipforge_data[index].strengthen_level or 0,					-- 神铸等级
				-- shen_level = self.role_info.equipforge_data[index].shenzhu_level or 0,							-- 品质等级
				-- quality = self.role_info.equipforge_data[index].quality or 0,									-- 强化等级
				-- gongji_refine_count = self.role_info.equipforge_data[index].gongji_refine_count or 0,			-- 大攻击精炼次数
				-- min_gongji_refine_count = self.role_info.equipforge_data[index].min_gongji_refine_count or 0,	-- 小攻击精炼次数
				-- fangyu_refine_count = self.role_info.equipforge_data[index].fangyu_refine_count or 0,			-- 防御精炼次数
				-- maxhp_refine_count = self.role_info.equipforge_data[index].maxhp_refine_count or 0,				-- 气血精炼次数
				-- strengthen_lucky = self.role_info.equipforge_data[index].strengthen_luck or 0,

				-- fuling_level = v.fuling_level,
				-- star_level = v.star_level,
				-- has_lucky = v.has_lucky,
				-- xianpin_type_list = v.xianpin_type_list,
		-- 	},
		-- }
		-- end
	end
	return equip_info_list
end

-- 【后端处理计算量大，前端同步装备套装逻辑】获取装备套装激活数据，用于显示查看玩家套装信息
-- 2024/10/28 装备肉身扩展 已不存在套装概念 只存在一个套装  查看的玩家只展示一套装备
function BrowseWGData:GetSameOrderCountList()
	if self.equip_dz_suit_data and self.equip_dz_suit_open_data then
		return self.equip_dz_suit_data, self.equip_dz_suit_open_data
	end

	local equip_dz_suit_data = {}
	local equip_dz_suit_open_data = {}
	local equipment_info = (self.role_info or {}).equipment_info or {}
	if not IsEmptyTable(equipment_info) then
		for i = 0, GameEnum.SUIT_ROLE_INDEX_MAX do
			local data = equipment_info[i]

			if not IsEmptyTable(data) and data.suit_count then
				-- local item_id = data.equip_id
				-- local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
				local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(i)

				if data.suit_count > 0 then
					equip_dz_suit_data[equip_type] = equip_dz_suit_data[equip_type] or 0
					equip_dz_suit_data[equip_type] = equip_dz_suit_data[equip_type] + 1
				end

				equip_dz_suit_open_data[i] = data.suit_count
			end
		end
	end

	self.equip_dz_suit_data = equip_dz_suit_data
	self.equip_dz_suit_open_data = equip_dz_suit_open_data

	return self.equip_dz_suit_data, self.equip_dz_suit_open_data

	-- if self.suit_order_list then
	-- 	return self.suit_order_list
	-- end

	-- self.suit_order_list = {}
	-- local cur_suit_list = {}      -- 装备的套装index
	-- local equip_order_list = {}   -- 装备的阶数
	-- -- 统计
	-- if nil ~= self.role_info.equipment_info then
	-- 	for i = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
	-- 		local data = self.role_info.equipment_info[i]
	-- 		local order = 0
	-- 		local suit_index = -1
	-- 		if data then
	-- 			local item_id = data.equip_id
	-- 			local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	-- 			local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(i)
	-- 			suit_index = data.suit_index or -1
	-- 			order = item_cfg and item_cfg.order or 0
	-- 			if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
	-- 				order = suit_index > -1 and order or 0
	-- 			end
	-- 		end

	-- 		equip_order_list[i] = order
	-- 		cur_suit_list[i] = suit_index
	-- 	end
	-- end

	-- if IsEmptyTable(cur_suit_list) or IsEmptyTable(equip_order_list) then
	-- 	return self.suit_order_list
	-- end

	-- -- 初始化
	-- for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
	-- 	self.suit_order_list[suit] = {}
	-- 	for equip_type = 0, GameEnum.EQUIP_BIG_TYPE_XIANQI do
	-- 		self.suit_order_list[suit][equip_type] = {}
	-- 		for order = 0, COMMON_CONSTS.EQUIP_MAX_ORDER do
	-- 			self.suit_order_list[suit][equip_type][order] = {count = 0, act_suit_list = {}}    -- 结构
	-- 		end
	-- 	end
	-- end

	-- -- 阶数倒序
	-- local order_list = {}
	-- for k, v in pairs(equip_order_list) do
	-- 	local cur_suit = cur_suit_list[k]
	-- 	local data = {part = k, order = v, cur_suit = cur_suit}
	-- 	table.insert(order_list, data)
	-- end
	-- table.sort(order_list, SortTools.KeyUpperSorter("order"))

	-- -- 已锻造的阶数计数
	-- for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
	-- 	for part = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
	-- 		local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(part)
	-- 		local cur_order = equip_order_list[part]
	-- 		local cur_suit = cur_suit_list[part]
	-- 		if suit <= cur_suit then
	-- 			if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
	-- 				local count = self.suit_order_list[suit][equip_type][cur_order].count
	-- 				self.suit_order_list[suit][equip_type][cur_order].count = count + 1
	-- 			end
	-- 		end

	-- 		-- 仙器不考虑阶数限制
	-- 		if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI and suit == cur_suit and cur_order > 0 then
	-- 			for i = suit, 0, -1 do
	-- 				for k, v in pairs(self.suit_order_list[i][equip_type]) do
	-- 					v.count = v.count + 1
	-- 				end
	-- 			end
	-- 		end
	-- 	end
	-- end

	-- -- 普通装备  高阶向低阶兼容
	-- for i = #order_list, 1, -1 do
	-- 	local part = order_list[i].part
	-- 	local cur_order = order_list[i].order
	-- 	local cur_suit = order_list[i].cur_suit
	-- 	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(part)
	-- 	if cur_order > 0 then
	-- 		if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
	-- 			for order = cur_order - 1, 1, -1 do
	-- 				for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
	-- 					local count = self.suit_order_list[suit][equip_type][order].count
	-- 					if suit <= cur_suit and count > 0 then
	-- 						self.suit_order_list[suit][equip_type][order].count = count + 1
	-- 					end
	-- 				end
	-- 			end
	-- 		end
	-- 	end
	-- end

	-- -- -- 普通激活 套件由高往低激活，只激活一种
	-- local equip_type = GameEnum.EQUIP_BIG_TYPE_NORMAL
	-- local cache_normal_act = {}
	-- local suit_stone_attr = EquipmentWGData.Instance:GetSuitStoneAttrCfg()
	-- for suit = GameEnum.SUIT_TYPE_MAX - 1, 0, -1  do
	-- 	local limit_cfg = EquipmentWGData.Instance:GetEquipmenSuitActivation(suit)
	-- 	for order = COMMON_CONSTS.EQUIP_MAX_ORDER, 0, -1 do
	-- 		local cur_count = 0
	-- 		if order >= limit_cfg.min_order then
	-- 			cur_count = self.suit_order_list[suit][equip_type][order].count
	-- 		end

	-- 		for i = cur_count, 0, -1 do
	-- 			local suit_cfg = suit_stone_attr and suit_stone_attr[suit]
	-- 							and suit_stone_attr[suit][order]
	-- 							and suit_stone_attr[suit][order][i]
	-- 			if suit_cfg and not cache_normal_act[i] then
	-- 				self.suit_order_list[suit][equip_type][order].act_suit_list[i] = true
	-- 				cache_normal_act[i] = true
	-- 			end
	-- 		end
	-- 	end
	-- end

	-- -- 仙器激活 不考虑阶数限制  套件由高往低激活，只激活一种
	-- local cache_xianqi_act = {}
	-- equip_type = GameEnum.EQUIP_BIG_TYPE_XIANQI
	-- -- local limit_cfg = self:GetEquipmenSuitActivation(0)
	-- for suit = GameEnum.SUIT_TYPE_MAX - 1, 0, -1  do
	-- 	for k, v in pairs(self.suit_order_list[suit][equip_type]) do
	-- 		local cur_count = v.count
	-- 		for i = cur_count, 0, -1 do
	-- 			local suit_cfg = EquipmentWGData.Instance:GetSuitXianQiAttrCfg(suit, i)
	-- 			if suit_cfg and ((not cache_xianqi_act[i]) or (cache_xianqi_act[i][suit])) then
	-- 				v.act_suit_list[i] = true
	-- 				if not cache_xianqi_act[i] then
	-- 					cache_xianqi_act[i] = {}
	-- 				end
	-- 				cache_xianqi_act[i][suit] = true
	-- 			end
	-- 		end
	-- 	end
	-- end

	-- return self.suit_order_list
end

function BrowseWGData:GetDZSuitDataInfo(equip_type)
	local equip_dz_suit_data = self:GetSameOrderCountList()
	return equip_dz_suit_data[equip_type] or 0
end

function BrowseWGData:GetEquipmenSuitOpenFlag(equip_part)
	local _, equip_dz_suit_open_data = self:GetSameOrderCountList()
	return equip_dz_suit_open_data[equip_part] or 0
end

-- 弃用
-- function BrowseWGData:GetSuitOrderListByData(suit, equip_type, order)
-- 	local suit_order_list = self:GetSameOrderCountList()
-- 	return suit_order_list and suit_order_list[suit]
-- 			and suit_order_list[suit][equip_type]
-- 			and suit_order_list[suit][equip_type][order]
-- end

function BrowseWGData:GetEquipmenSuitOpenNum(equip_data)
	if nil == equip_data then
		return 0
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
	if nil == item_cfg then
		return 0
	end

	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)

	return self:GetDZSuitDataInfo(equip_type)

	-- local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	-- local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_index)
	-- local suit_index = equip_data.param and equip_data.param.suit_index or -1
	-- local info = self:GetSuitOrderListByData(suit_index, equip_type, item_cfg.order)
	-- return info and info.count or 0
end
---------------------------- 套装 end --------------------------------------

-- 获取宝石数据
function BrowseWGData:GetEquipForgeGridListById(index)
	if self.role_info.equipforge_data then
		return self.role_info.equipforge_data[index]
	end
end

-- 获取宝石数据
function BrowseWGData:GetStoneInfoListByIndex(index)
	if self.role_info.stone_infos then
		return self.role_info.stone_infos[index]
	end
end

-- 获取灵玉数据
function BrowseWGData:GetLingYuInfoListByIndex(index)
	if self.role_info.lingyu_infos then
		return self.role_info.lingyu_infos[index]
	end
end

function BrowseWGData:OnSCSingleQueryXianjieOneEquip(protocol) --一套返回
	if not self.shengzhuang_table then
		self.shengzhuang_table = {}
	end

	if not self.shengzhuang_table[protocol.target_uid] then
		self.shengzhuang_table[protocol.target_uid] = {}
	end

	if not self.shengzhuang_table[protocol.target_uid][protocol.slot_index] then
		self.shengzhuang_table[protocol.target_uid][protocol.slot_index] = {}
	end

	self.shengzhuang_table[protocol.target_uid][protocol.slot_index] = protocol.slot_info
end

function BrowseWGData:OnSCSingleQueryXianjieAllEquip(protocol) --所有套返回
	if not self.shengzhuang_table then
		self.shengzhuang_table = {}
	end

	if not self.shengzhuang_table[protocol.target_uid] then
		self.shengzhuang_table[protocol.target_uid] = {}
	end

	for k,v in pairs(protocol.slot_info_list) do
		self.shengzhuang_table[protocol.target_uid][k] = v
	end
end

function BrowseWGData:GetPlayerXianJieEquipByUUID(plat_type,uid,class) --转数class 从0开始
	if not self.shengzhuang_table then
		self.shengzhuang_table = {}
	end
	local key = uid

	if not self.shengzhuang_table[key] then
		self.shengzhuang_table[key] = {}
	end

	if not self.shengzhuang_table[key][class] then
		BrowseWGCtrl.Instance:CheckCurOpenXianJieEquipBySlotIndex(plat_type,uid,class)
		return {}
	else
		return self.shengzhuang_table[key][class]
	end
end

function BrowseWGData:ClearOldCacheShengZhuang()
	self.shengzhuang_table = {}
end