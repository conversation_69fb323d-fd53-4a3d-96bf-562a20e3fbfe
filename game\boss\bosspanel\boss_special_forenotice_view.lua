--------------------------------------------------
-- boss刷新提醒,目前只适用于深渊boss
--------------------------------------------------
BossSpecialForenoticeView = BossSpecialForenoticeView or BaseClass(SafeBaseView)
local FLAG_NAME = {
    [1] = "a3_zjm_bq",
    [2] = "a3_zjm_bq1",
}

function BossSpecialForenoticeView:__init()
	self.is_modal = true

	self.view_layer = UiLayer.Pop
	self.view_name = "BossSpecialForenoticeView"
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_forenotice2")
end

function BossSpecialForenoticeView:__delete()
end

function BossSpecialForenoticeView:ReleaseCallBack()
	self.boss_info = {}

	--[[
	if self.reward_show_list then
		self.reward_show_list:DeleteMe()
		self.reward_show_list = nil
	end
	]]
end

function BossSpecialForenoticeView:LoadCallBack()
	self.node_list["btn_go_kill"].button:AddClickListener(BindTool.Bind1(self.ClickHandler, self))

	--[[ 策划要求隐藏极品掉落
	if not self.reward_show_list then
		self.reward_show_list = AsyncListView.New(ItemCell, self.node_list.reward_show_list)
	end
	]]
end

function BossSpecialForenoticeView:ShowIndexCallBack()
	self:Flush()
end

function BossSpecialForenoticeView:OnFlush()
	local cfg = BossWGData.Instance:GetMonsterInfo(self.boss_info.boss_id)
	if cfg then
		if self.node_list["boss_name_txt"] then
			self.node_list["boss_name_txt"].text.text = cfg.name
		end

		if self.node_list["img_boss_head"] then
			local bundle,asset = ResPath.GetBossIcon("wrod_boss_".. cfg.small_icon)
			self.node_list["img_boss_head"].image:LoadSprite(bundle,asset,function()
				self.node_list["img_boss_head"].image:SetNativeSize()
			end)
		end
	end

	local boss_info = BossWGData.Instance:GetBossAllInfoByBossId(self.boss_info.boss_id)
	if boss_info == nil then return end

	self:FlushRewardShowDataList(boss_info)

	if Config_scenelist[boss_info.scene_id] then
		self.node_list["lbl_title"].text.text = Config_scenelist[boss_info.scene_id].name
    end

    if TreasureBossWGData.Instance:IsTreasureBoss(self.boss_info.boss_id) then
    	local jie_shu = TreasureBossWGData.Instance:GetTreasureBossClass(self.boss_info.boss_id)
        self.node_list.jieshu_text.text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(jie_shu))
    elseif XianJieBossWGData.Instance:GetBossCfgById(self.boss_info.boss_id) then
        local cfg = XianJieBossWGData.Instance:GetBossCfgById(self.boss_info.boss_id)
        local slor_str = NumberToChinaNumber(cfg.slot_limit + 1)
        local page_str2 = XianJieBossWGData.Instance:GetPageStr(cfg.slot_page_limit, true)
        self.node_list.jieshu_text.text.text = string.format(Language.XianJieBoss.SlotSNoPoint, slor_str, page_str2)
    else
    	self.node_list.jieshu_text.text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(cfg.boss_jieshu))
    end

    self:FlushBossImgBG()
end

function BossSpecialForenoticeView:FlushBossImgBG()
    if self.boss_info then
        local bg_type = 1 --1 白色 2 紫色 3 金色
        local flag_type = 1 -- 1和平 2 危险
		local scene_type = BossWGData.SceneType[self.boss_info.boss_type]
        if scene_type == SceneType.WorldBoss
		or scene_type == SceneType.VIP_BOSS
		or scene_type == SceneType.DABAO_BOSS then
            bg_type = 1
        	flag_type = 1
        elseif scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS
		or scene_type == SceneType.Shenyuan_boss then
            bg_type = 3
            flag_type = 2
        elseif scene_type == SceneType.KF_BOSS
		or scene_type == SceneType.XianJie_Boss then
            bg_type = 2
            flag_type = 2
        end

		--美术指出了一张底，策划xzp让只用原图
        local flag_name = FLAG_NAME[flag_type]
        local bundle1, asset1 = ResPath.GetBossUI(flag_name)
        self.node_list.flag_img.image:LoadSprite(bundle1, asset1) --左上角flag
		self.node_list.boss_status_text.text.text = Language.BossFlagStatus[flag_type]

        local bundle2, asset2 = ResPath.GetBossUI("a3_zjm_bst_" .. self.boss_info.boss_type)
        self.node_list.name_flag_img.image:LoadSprite(bundle2, asset2)
    end
end

function BossSpecialForenoticeView:SetShowData(data)
	self.boss_info = data
	local boss_info = BossWGData.Instance:GetBossAllInfoByBossId(self.boss_info.boss_id)
    if boss_info == nil then
        print_error("取不到boss配置 boss_id =", self.boss_info.boss_id, "boss_type", self.boss_info.boss_type)
		self:Close()
        return
    end
	self.boss_info.big_icon = boss_info.big_icon
	self.boss_info.boss_level = boss_info.boss_level
	self.boss_info.boss_name = boss_info.boss_name
	-- self.boss_info.boss_type =
	self:Flush()
end

function BossSpecialForenoticeView:JudgeNeedCloseView(boss_id)
    if self:IsOpen() and not IsEmptyTable(self.boss_info) and self.boss_info.boss_id == boss_id then
        self:Close()
    end
end

function BossSpecialForenoticeView:FlushRewardShowDataList(boss_info)
	local data_list = {}
	local zero_index = false

	local scene_type = BossWGData.SceneType[self.boss_info.boss_type]
	if scene_type == SceneType.WorldBoss or scene_type == SceneType.DABAO_BOSS or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.KF_BOSS then
		if boss_info and boss_info.drop_item_list then
			zero_index = true
			data_list = boss_info.drop_item_list
		end
	elseif scene_type == SceneType.SG_BOSS then

	elseif scene_type == SceneType.MJ_BOSS then

	elseif scene_type == SceneType.TIANSHEN_JIANLIN or scene_type == SceneType.WORLD_TREASURE_JIANLIN then

	elseif scene_type == SceneType.Shenyuan_boss then
		if boss_info and boss_info.drop_item_list1 then
			local list = {}
			local data
			for i, v in pairs(boss_info.drop_item_list1) do
				local str = Split(v, ":")
				data = {
					item_id = tonumber(str[1]),
					num = tonumber(str[2]),
					is_bind = tonumber(str[3]),
				}
				table.insert(list, data)
			end

			data_list = list
		end
	elseif scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
		local treasure_boss_info = TreasureBossWGData.Instance:GetBossDropShowData(self.boss_info.boss_id)
		data_list = treasure_boss_info and treasure_boss_info.drop_item_list or {}
	end

	--[[ 策划要求隐藏极品掉落
	local has_drop_item = not IsEmptyTable(data_list)

	if has_drop_item then
		self.reward_show_list:SetStartZeroIndex(zero_index)
		self.reward_show_list:SetDataList(data_list)
	
		-- self.node_list.head_root.transform.localPosition = Vector3(-58, 57, 0)
		-- self.node_list.flag_img.transform.localPosition = Vector3(-58, 27, 0)
		-- self.node_list.jieshu_text.transform.localPosition = Vector3(26, 70, 0)
		-- self.node_list.center_name.transform.localPosition = Vector3(0, 57, 0)
		-- self.node_list.name_flag_img.transform.localPosition = Vector3(-105, 11, 0)
		-- self.node_list.boss_name_txt.transform.localPosition = Vector3(39, -12, 0)
	-- else
	-- 	self.node_list.head_root.transform.localPosition = Vector3(0, 57, 0)
	-- 	self.node_list.flag_img.transform.localPosition = Vector3(0, 27, 0)
	-- 	self.node_list.jieshu_text.transform.localPosition = Vector3(-22, -10, 0)
	-- 	self.node_list.center_name.transform.localPosition = Vector3(0, -23, 0)
	-- 	self.node_list.name_flag_img.transform.localPosition = Vector3(-90, 0, 0)
	-- 	self.node_list.boss_name_txt.transform.localPosition = Vector3(-9, -11, 0)
	end

	self.node_list.flag_drop:CustomSetActive(has_drop_item)	
	]]
end

function BossSpecialForenoticeView:ClickHandler()
	if not ViewManager.Instance:IsOpenByIndex(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss) then
		ViewManager.Instance:Open(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
	end

	self:Close()
end

function BossSpecialForenoticeView:IsSameScene(scene_id)
	if scene_id == Scene.Instance:GetSceneId() then
		if Scene.Instance:GetSceneType() == SceneType.HONG_MENG_SHEN_YU then
			local scene_index = BossWGData.Instance:GetCurHMSYSceneIndex()
			return self.boss_info.scene_index ~= nil and self.boss_info.scene_index == scene_index
		else
			return true
		end
	end

	return false
end