--------------------------------------------------
--充值控制器
--<AUTHOR>
--------------------------------------------------
require("game/recharge/recharge_wg_data")
require("game/recharge/recharge_all_view")
require("game/recharge/recharge_view")
require("game/recharge/recharge_view_week_buy")
require("game/recharge/recharge_vip_panel")
require("game/recharge/recharge_zero_buy_panel")
require("game/recharge/recharge_storehouse_panel")
require("game/recharge/recharge_month_card_panel")
require("game/recharge/recharge_touziplan_panel")
require("game/recharge/monthcard_provolege_view")
require("game/recharge/recharge_week_card_panel")
require("game/recharge/discount_coupon_view")
require("game/recharge/recharge_reserve_card_panel")
require("game/recharge/zftq_reward_preview")
require("game/recharge/recharge_king_vip_view")
require("game/recharge/recharge_king_vip_tips")
require("game/recharge/recharge_vip_all_privilege_tips")
require("game/recharge/recharge_leichong_view")
require("game/recharge/recharge_xiaofei_view")

RechargeWGCtrl = RechargeWGCtrl or BaseClass(BaseWGCtrl)

function RechargeWGCtrl:__init()
	if RechargeWGCtrl.Instance then
		ErrorLog("[RechargeWGCtrl] Attemp to create a singleton twice !")
	end
	RechargeWGCtrl.Instance = self

	self.recharge_data = RechargeWGData.New()
	self.recharge_all_view = RechargeView.New(GuideModuleName.Vip)
	self.replace_coin_recharge_time = 0 -- 代币充值时间 解决连点问题
	self.is_first_init = true
	self.is_first_recharge_pro = true
	self.monthcard_provolege_view = MonthCardProvolegeView.New(GuideModuleName.MonthCardProvolegeView)
	self.discount_coupon_view = DiscountCouponView.New()
	self.preview_view = ZFTQRewardPreviewView.New()
	self.king_vip_tips = RechargeKingVipTips.New()
	self.vip_all_privilege_tips = RechargeVipAllPrivilegeTips.New()

	-- 玩家充值信息
	self:RegisterProtocol(SCChongZhiInfo, "OnSCChongZhiInfo")
	-- 玩家真实充值金额
	self:RegisterProtocol(SCRealChongZhiRmbInfo, "OnRealChongZhiRmbInfo")
	-- 玩家今日真实充值金额 (单位:元)
	self:RegisterProtocol(SCTodayRealChongZhiRmbInfo, "OnTodayRealChongZhiRmbInfo")

	---------------月卡------------------
	self:RegisterProtocol(SCInvestCardInfo, "OnInvestCardInfo")
	self:RegisterProtocol(CSFetchInvestCardReward)
	self:RegisterProtocol(CSBuyInvestCard)

	----------------每周必买------------------------------------
	self:RegisterProtocol(CSPeriodOperaReq)
	--self:RegisterProtocol(SCPeriodAllInfo, "OnSCPeriodAllInfo")
	self:RegisterAllEvents()

	----------------零元购-------------------------------------
	self:RegisterProtocol(SCVipZeroBuyInfo, "OnSCVipZeroBuyInfo")

	-- 使用虚拟现金
	self:RegisterProtocol(CSVirtualRechargeReq)
	self:RegisterProtocol(SCVirtualGoldOtherInfo, "OnVirtualGoldOtherInfo")

	-------------------------周卡-------------------------------
	self:RegisterProtocol(SCWeekCardInfo, "OnSCWeekCardInfo")
	self:RegisterProtocol(CSWeekCardOperate)

	--------------------------攒福特权--------------------------
	self:RegisterProtocol(SCZanfutequanAllInfo, "OnSCZanfutequanAllInfo")
	self:RegisterProtocol(CSZanfutequanClientReq)

	--------------------------王者特权--------------------------
	self:RegisterProtocol(SCKingPrivilegeInfo, "OnSCKingPrivilegeInfo")
	self:RegisterProtocol(CSKingPrivilegeInfoReq)

	---------------------------充值劵操作-----------------------------
	self:RegisterProtocol(CSVirualGold2Req)

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function RechargeWGCtrl:__delete()
	RechargeWGCtrl.Instance = nil

	self:ClearZFTQOnlineTimeQuest()
	self:CleanUpdateRmbDiscountUsedCDTimer()
	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.recharge_data ~= nil then
		self.recharge_data:DeleteMe()
		self.recharge_data = nil
	end
	if self.recharge_all_view ~= nil then
		self.recharge_all_view:DeleteMe()
		self.recharge_all_view = nil
	end

	if self.monthcard_provolege_view ~= nil then
		self.monthcard_provolege_view:DeleteMe()
		self.monthcard_provolege_view = nil
	end


	if self.goods_buy_tips then
		self.goods_buy_tips:DeleteMe()
		self.goods_buy_tips = nil
	end

	if self.pay_replace_coin_tips then
		self.pay_replace_coin_tips:DeleteMe()
		self.pay_replace_coin_tips = nil
	end

	if self.pay_virtual_gold_tips then
		self.pay_virtual_gold_tips:DeleteMe()
		self.pay_virtual_gold_tips = nil
	end

	if self.pay_cash_point_tips then
		self.pay_cash_point_tips:DeleteMe()
		self.pay_cash_point_tips = nil
	end

	self.discount_coupon_view:DeleteMe()
	self.discount_coupon_view = nil

	if self.preview_view then
		self.preview_view:DeleteMe()
		self.preview_view = nil
	end

	if self.king_vip_tips then
		self.king_vip_tips:DeleteMe()
		self.king_vip_tips = nil
	end

	if self.vip_all_privilege_tips then
		self.vip_all_privilege_tips:DeleteMe()
		self.vip_all_privilege_tips = nil
	end
end

-- 注册事件
function RechargeWGCtrl:RegisterAllEvents()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function RechargeWGCtrl:MainuiOpenCreate()
	self:CSPeriodOperaReq(PERIOD_OP_TYPE.PERIOD_OP_TYPE_ALL_INFO, 0)
end

function RechargeWGCtrl:Open(tab_index)
	self.recharge_all_view:Open(tab_index)
end

function RechargeWGCtrl:Flush(...)
	if self.recharge_all_view:IsOpen() then
		self.recharge_all_view:Flush(...)
	end
end

function RechargeWGCtrl:OpenMonthCardProvolege()
	if not self.monthcard_provolege_view:IsOpen() then
		self.monthcard_provolege_view:Open()
	else
		self.monthcard_provolege_view:Flush()
	end
end

--设置每日累充活动状态
function RechargeWGCtrl:SetDailyRechargeStatus()
	if FunOpen.Instance:GetFunIsOpened(FunName.DailyRecharge) then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.MEIRI_LEICHONG, ACTIVITY_STATUS.OPEN)
	else
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.MEIRI_LEICHONG, ACTIVITY_STATUS.CLOSE)
	end
end

--充值信息返回
function RechargeWGCtrl:OnSCChongZhiInfo(protocol)
	-- 累充改变
	local history_recharge = self.recharge_data:GetHistoryRecharge()
	if not self.is_first_recharge_pro and protocol.history_recharge > history_recharge then
		GlobalEventSystem:Fire(AuditEvent.RECHARGE_CHANGE, protocol.history_recharge)
	else
		self.is_first_recharge_pro = false
	end

	self.recharge_data:SetRechargeData(protocol)
	VipWGData.Instance:SetVipRechargeData(protocol)
	RechargeRewardWGData.Instance:SetRechargeFetchInfo(protocol)
	ServerActivityWGData.Instance:SetTotalChongZhiData(protocol)
	RechargeRewardWGCtrl.Instance:FLush()
	RechargeRewardWGCtrl.Instance:RefreshDailyRechargeView()
	ServerActivityWGCtrl.Instance:FlushRushRecharge()
	RoleWGCtrl.Instance:FlushBianQiangButton(true)
	self:SetDailyRechargeStatus()
	-- RemindManager.Instance:Fire(RemindName.DailyRecharge)
	VipTyWGCtrl.Instance:CheckIsFirstRechargeCloseTips()
	self:Flush(TabIndex.recharge_cz)
	MainuiWGCtrl.Instance:FlushView(0, "flush_ser_rec_tip")
	LingYuActiveWGCtrl.Instance:FlushLingYuView()
end

-- 真实充值
function RechargeWGCtrl:OnRealChongZhiRmbInfo(protocol)
	self.recharge_data:SetRealChongZhiRmbInfo(protocol)
	GlobalEventSystem:Fire(ROLE_REAL_RECHARGE_NUM_CHANGE.REAL_RECHARGE)
end

-- 今日真实充值
function RechargeWGCtrl:OnTodayRealChongZhiRmbInfo(protocol)
	local old_today_real_recharge_num = self.recharge_data:GetToDayRealChongZhiRmb()
	local new_today_real_recharge_num = protocol.today_real_chongzhi_rmb
	self.recharge_data:SetTodayRealChongZhiRmbInfo(protocol)

	local real_recharge_diff = new_today_real_recharge_num - old_today_real_recharge_num
	if real_recharge_diff > 0 and new_today_real_recharge_num > 0 then
		GlobalEventSystem:Fire(ROLE_REAL_RECHARGE_NUM_CHANGE.TODAY_REAL_RECHARGE_CHANGE, new_today_real_recharge_num,
			real_recharge_diff)
	end
end

-- 折扣券支付
function RechargeWGCtrl:TryUseDiscountPay(product_id, money, desc, recharge_type, gold_flag, discount_ticket_index, custom_param)
	-- local discount_list, can_use_num = self.recharge_data:GetRmbDiscountList(money)
	-- if can_use_num > 0 then
	-- 	local data = {
	-- 		discount_list = discount_list,
	-- 		product_id = product_id,
	-- 		money = money,
	-- 		desc = desc,
	-- 		recharge_type = recharge_type,
	-- 		gold_flag = gold_flag,
	-- 	}
	-- 	self.discount_coupon_view:SetDataAndOpen(data)
	-- else
	-- 	if AgentAdapter and AgentAdapter.Instance then
	-- 		AgentAdapter.Instance:Pay(product_id, money, desc, recharge_type, gold_flag)
	-- 	end
	-- end

	if AgentAdapter and AgentAdapter.Instance then
		AgentAdapter.Instance:Pay(product_id, money, desc, recharge_type, gold_flag, discount_ticket_index, custom_param)
	end
end

-- 琉璃不足，提醒玩家充值获得琉璃
function RechargeWGCtrl:RemindRechargeByCangJinShangPuScoreNoEnough(money)
	local desc = "灵玉"
	local pay_real_money_to_chongzhi_fun = function()
		local cz_map_cfg = self.recharge_data:GetRechargeMapCfg(money, 0, money)
		local product_id = money
		local trans_money = money
		if cz_map_cfg then
			local pay_type = RoleWGData.GetPayMoneyType()
			if pay_type == PAY_MONEY_TYPES.DOLLAR then
				trans_money = cz_map_cfg.USD
			end

			if cz_map_cfg.product_name and cz_map_cfg.product_name ~= "" then
				desc = cz_map_cfg.product_name
			end

			product_id = cz_map_cfg.product_id
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.MapErrorTip)
			product_id = "cz_" .. money
		end

		-- 折扣券
		self:TryUseDiscountPay(tostring(product_id), trans_money, desc, 0, money)
	end

	if self.goods_buy_tips then
		self.goods_buy_tips:DeleteMe()
		self.goods_buy_tips = nil
	end

	self.goods_buy_tips = Alert.New()

	-- if not self.goods_buy_tips then
	-- 	self.goods_buy_tips = Alert.New()
	-- end

	local cz_map_cfg = RechargeWGData.Instance:GetRechargeMapBestCfg(money, 0, money)
	money = cz_map_cfg and cz_map_cfg.RMB or money
	local money_desc = tostring(money)
	if cz_map_cfg then
		if cz_map_cfg.product_name and cz_map_cfg.product_name ~= "" then
			desc = cz_map_cfg.product_name
		end

		money_desc = RoleWGData.GetPayMoneyStr(money, 0, money)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.MapErrorTip)
		return
	end

	local cur_score = YanYuGeWGData.Instance:GetCurScore()
	self.goods_buy_tips:SetLableString(string.format(Language.Recharge.BuyGoodsTipsStr2, money_desc, desc, money * 10, cur_score))
	self.goods_buy_tips:SetOkFunc(pay_real_money_to_chongzhi_fun)
	self.goods_buy_tips:SetCancelFunc(nil)
	self.goods_buy_tips:SetOkString(Language.Recharge.BuyGoodsTipsBtn1)
	self.goods_buy_tips:SetCancelString(Language.Common.BtnCancel)
	self.goods_buy_tips:Open()
end

--充值
-- discount_ticket_index：服务器的券包列表索引
function RechargeWGCtrl:Recharge(orginal_money, recharge_type, gold_flag, discount_ticket_index, custom_param, other_info)
	if not RechargeWGData.IsOpenRecharge then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.NotOpenCloseBeta)
		return
	end

	local desc = Language.Common.Gold
	orginal_money = orginal_money or 0
	if orginal_money <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.ReceiveFailTip)
		return
	end

	recharge_type = recharge_type or 0
	gold_flag = gold_flag or 0
	discount_ticket_index = discount_ticket_index or -1
	custom_param = custom_param or -1
	other_info = other_info or {}

	local map_cfg = self.recharge_data:GetRechargeMapCfg(orginal_money, recharge_type, gold_flag)
	-- 百亿补贴购物车特殊处理
	if recharge_type ~= GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY then
		if not map_cfg then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.MapErrorTip)
			return
		end
	else
		desc = Language.BillionSubsidy.ShopCart
	end


	local discounted_price = orginal_money

	-- 满减券
	if discount_ticket_index >= 0 then
		local discount_ticket_data = BillionSubsidyWGData.Instance:GetSingleDiscountTicketDataBySeq(discount_ticket_index)
		if discount_ticket_data then
			if orginal_money < discount_ticket_data.quota_limit then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.ErrorDiscountTicketPay)
				return
			end

			discounted_price = orginal_money - discount_ticket_data.reduce_quota
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.ErrorDiscountTicketCfg)
			return
		end
	end

	-- 次数满减额度
	if recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TB_DEZG_RMB_BUY then
		local reduce_value = BillionSubsidyWGData.Instance:GetDEZGShopCurReduceValue()
		discounted_price = discounted_price - reduce_value
	end

	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.pay, recharge_type, gold_flag, discounted_price)

	-- 琉璃购买
	local is_cangjinshangpu_score = ((map_cfg or {}).is_cangjinshangpu_score or 0) == 1
	if is_cangjinshangpu_score then
		local need_score = map_cfg.RMB
		local cur_score = YanYuGeWGData.Instance:GetCurScore()

		if need_score > cur_score then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YanYuGe.NoEnoughScore)
			self:RemindRechargeByCangJinShangPuScoreNoEnough(need_score - cur_score)
		else
			TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Recharge.PayRechargeScoreStr, need_score, cur_score),
				function()
					YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.SCORE_BUY, map_cfg.rmb_type, map_cfg.rmb_seq, map_cfg.RMB)
				end
			)
		end

		-- if not ViewManager.Instance:IsOpenByIndex(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_privilege) then
		-- 	ViewManager.Instance:Open(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_privilege, "jump_to_privilege", map_cfg)
		-- else
		-- 	local need_score = map_cfg.RMB
		-- 	local cur_score = YanYuGeWGData.Instance:GetCurScore()

		-- 	if need_score > cur_score then
		-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.YanYuGe.NoEnoughScore)
		-- 		self:RemindRechargeByCangJinShangPuScoreNoEnough(need_score - cur_score)
		-- 	else
		-- 		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Recharge.PayRechargeScoreStr, need_score, cur_score),
		-- 			function()
		-- 				YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.SCORE_BUY, map_cfg.rmb_type, map_cfg.rmb_seq, map_cfg.RMB)
		-- 			end
		-- 		)
		-- 	end
		-- end

		return
	end

	local money_desc = tostring(discounted_price)
	if map_cfg then
		if map_cfg.product_name and map_cfg.product_name ~= "" then
			desc = map_cfg.product_name
		end
	end
	money_desc = RoleWGData.GetPayMoneyStr(discounted_price, recharge_type, gold_flag)

	local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume")
	local cash_point_num = RoleWGData.Instance:GetAttr("cash_point")
	local replace_coin_num = RechargeWGData.Instance:GetReplaceCoinNum()

	local is_recharge = recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_CHONGZHI
	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local day_limit = server_day > other_cfg.virtual_gold_1_openday_limit
	local is_tongpiao = recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TONGPIAO
	local not_can_use_tongpiao = map_cfg and map_cfg.can_use_virtual_gold_1 or 0
	local is_tongpiao = is_tongpiao or (not_can_use_tongpiao == 0)

	-- 百亿补贴购物车没有配置 直接设置可以通票购买
	if recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY then
		is_tongpiao = false
	end
	-- 代币（充值 or 直购）、 现金点（充值 or 直购）、 充值券(充值)
	-- 优先级：充值券 --> 现金点 --> 代币（代金券） --> 现金支付
	--local can_use_recharge_volume = is_recharge and recharge_volume_num >= discounted_price
	--充值券在充值券界面进行使用，在充值界面无法使用
	local can_use_recharge_volume = false
	local can_use_cash_point = not is_tongpiao and day_limit and cash_point_num > 0
	-- local can_use_replace_coin = replace_coin_num >= discounted_price

	-- 现金支付
	local pay_real_money_fun = function()
		-- TipWGCtrl.Instance:ShowSystemMsg("调支付！！！")
		local product_id = discounted_price
		local trans_money = discounted_price
		if map_cfg then
			local pay_type = RoleWGData.GetPayMoneyType()
			if pay_type == PAY_MONEY_TYPES.DOLLAR then
				trans_money = map_cfg.USD
			end

			product_id = map_cfg.product_id
		else
			product_id = "cz_" .. discounted_price
		end

		-- 折扣券
		self:TryUseDiscountPay(tostring(product_id), trans_money, desc, recharge_type, gold_flag, discount_ticket_index, custom_param)
	end

	-- 代币（代金券）
	local pay_replace_coin_fun = function()
		if replace_coin_num > 0 then
			if not self.pay_replace_coin_tips then
				self.pay_replace_coin_tips = Alert.New()
			end

			
			self.pay_replace_coin_tips:SetOkString(Language.Recharge.VirtualGoldBuyBtnStr[VIRTUAL_GOLD_TYPE.REPLACE_COIN_ITEM])
			self.pay_replace_coin_tips:SetCancelString(Language.Recharge.VirtualGoldBuyBtnStr2)

			local is_enough = replace_coin_num >= discounted_price
			self.pay_replace_coin_tips:SetLableString(string.format(Language.Recharge.PayReplaceCoinStr, discounted_price,
				replace_coin_num))
			self.pay_replace_coin_tips:SetOkFunc(function()
				if recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY and not BillionSubsidyWGData.Instance:IsShopCartLock() then
					TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.NotLockTime)
					return
				end
				if is_enough then
					self:SendUsedReplaceCoin(discounted_price, recharge_type, gold_flag, VIRTUAL_GOLD_TYPE.REPLACE_COIN_ITEM, discount_ticket_index, custom_param)
				else
					TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.NoEnoughReplaceCoinStr)
				end
			end)

			if recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY then
				self.pay_replace_coin_tips:SetCancelFunc(function ()
					BillionSubsidyWGCtrl.Instance:ShopCartLock(0)
				end)
				
				self.pay_replace_coin_tips:SetCloseFunc(function ()
					BillionSubsidyWGCtrl.Instance:ShopCartLock(0)
				end)
			end
			-- self.pay_replace_coin_tips:SetCancelFunc(pay_real_money_fun)
			self.pay_replace_coin_tips:Open()
		else
			-- pay_real_money_fun()
		end
	end

	-- 现金点
	local function pay_by_cash_point()
		if not self.pay_cash_point_tips then
			self.pay_cash_point_tips = Alert.New()
		end

		self.pay_cash_point_tips:SetOkString(Language.Recharge.VirtualGoldBuyBtnStr[VIRTUAL_GOLD_TYPE.CASH_POINT])
		self.pay_cash_point_tips:SetCancelString(Language.Recharge.VirtualGoldBuyBtnStr2)
		self.pay_cash_point_tips:SetLableString(string.format(Language.Recharge.PayCashPointStr, discounted_price, cash_point_num))
		self.pay_cash_point_tips:SetOkFunc(function()
			self:SendUsedReplaceCoin(discounted_price, recharge_type, gold_flag, VIRTUAL_GOLD_TYPE.CASH_POINT, discount_ticket_index, custom_param)
			if cash_point_num >= discounted_price then
				self:SendUsedReplaceCoin(discounted_price, recharge_type, gold_flag, VIRTUAL_GOLD_TYPE.CASH_POINT, discount_ticket_index, custom_param)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.NoEnoughReplaceCashStr)
				ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz, "all" ,{ to_ui_param = RECHARGE_PANEL_TYPE.CashPoint })
			end
		end)

		self.pay_cash_point_tips:SetCancelFunc(pay_replace_coin_fun)
		self.pay_cash_point_tips:Open()
	end

	-- 充值券
	local function pay_by_recharge_volume()
		if not self.pay_virtual_gold_tips then
			self.pay_virtual_gold_tips = Alert.New()
		end

		self.pay_virtual_gold_tips:SetOkString(Language.Recharge.VirtualGoldBuyBtnStr[VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME])
		self.pay_virtual_gold_tips:SetCancelString(Language.Recharge.VirtualGoldBuyBtnStr2)
		self.pay_virtual_gold_tips:SetLableString(string.format(Language.Recharge.PayRechargeVolumeStr, discounted_price,
			recharge_volume_num))
		self.pay_virtual_gold_tips:SetOkFunc(function()
			local virtual_type = VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME
			local use_num = RechargeWGData.Instance:GetVirtualGoldUseNum(virtual_type)
			local max_num = RechargeWGData.Instance:GetAllVirtualGoldMaxUseNum(virtual_type)
			--local time_limit_num = RechargeWGData.Instance:GetVirtualGoldLimitTimeLimited(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)

			if use_num + discounted_price > max_num then
				TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Recharge.VirtualGoldUseLimit[virtual_type], discounted_price))
			else
				self:SendUsedReplaceCoin(discounted_price, recharge_type, gold_flag, virtual_type)
			end
		end)

		-- 现金点
		if can_use_cash_point then
			self.pay_virtual_gold_tips:SetCancelFunc(pay_by_cash_point)
		-- 代币（代金券）
		else
			self.pay_virtual_gold_tips:SetCancelFunc(pay_replace_coin_fun)
		end

		self.pay_virtual_gold_tips:Open()
	end

	local is_cangjinshangpu_package = not IsEmptyTable(other_info)

	if self.goods_buy_tips then
		self.goods_buy_tips:DeleteMe()
		self.goods_buy_tips = nil
	end

	-- 商品购买提示
	if not self.goods_buy_tips then
		if is_cangjinshangpu_package then
			self.goods_buy_tips = PrivilegeCollectionRechargePackageAlert.New()
		else
			self.goods_buy_tips = Alert.New()
		end
	end

	-- self.goods_buy_tips:SetLableString(string.format(Language.Recharge.BuyGoodsTipsStr, money_desc, desc, discounted_price))
	self.goods_buy_tips:SetLableString(string.format(Language.Recharge.BuyGoodsTipsStr3, money_desc, desc))
	if recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY then
		self.goods_buy_tips:SetLableString(string.format(Language.Recharge.BuyGoodsTipsStr4, money_desc))
		
		self.goods_buy_tips:SetCloseFunc(function ()
			BillionSubsidyWGCtrl.Instance:ShopCartLock(0)
		end)
	else
		if is_cangjinshangpu_package then
			self.goods_buy_tips:SetLableString(string.format(Language.Recharge.BuyGoodsTipsStr5, money_desc, desc))
		else
			self.goods_buy_tips:SetLableString(string.format(Language.Recharge.BuyGoodsTipsStr3, money_desc, desc))
		end
	end
	self.goods_buy_tips:SetOkFunc(pay_real_money_fun)

	if can_use_recharge_volume or can_use_cash_point or replace_coin_num > 0 then
		self.goods_buy_tips:SetCancelFunc(function()
			if can_use_recharge_volume then
				pay_by_recharge_volume()
			elseif can_use_cash_point then
				pay_by_cash_point()
			elseif replace_coin_num > 0 then
				pay_replace_coin_fun()
			else
				if recharge_type == GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY then
					BillionSubsidyWGCtrl.Instance:ShopCartLock(0)
				end
				TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.NoEnoughReplaceCoinStr)
			end
		end)

		self.goods_buy_tips:SetCancelString(Language.Recharge.BuyGoodsTipsBtn2)
		self.goods_buy_tips:SetCancleBtnState(true)
	else
		self.goods_buy_tips:SetCancleBtnState(false)
	end


	self.goods_buy_tips:SetOkString(Language.Recharge.BuyGoodsTipsBtn1)

	if not IsEmptyTable(other_info) then
		self.goods_buy_tips:SetOtherInfo(other_info)
	end

	self.goods_buy_tips:Open()
end

---[[ F2月卡
function RechargeWGCtrl:OnInvestCardInfo(protocol)
	self.recharge_data:SetInvestCardInfo(protocol)
	self:Flush(TabIndex.recharge_month_card)
	self:FlushMonthCardProvolegeView()
	FuBenWGCtrl.Instance:FlushCombinePanel()
	FuBenWGCtrl.Instance:FlushSaoDangPanel()
	ArenaTiantiWGCtrl.Instance:FlushView()
	RemindManager.Instance:Fire(RemindName.Vip_VTZ)
	RemindManager.Instance:Fire(RemindName.Vip_Month)
	MainuiWGCtrl.Instance:FlushView(0, "free_fuhuo_info")
	PrivilegedGuidanceWGCtrl.Instance:FlushGuidanceView()
	ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_privilege)
	RemindManager.Instance:Fire(RemindName.PrivilegedGuidance)
	--由于策划要求月卡特权主界面按钮放在活动按钮列表中，配置了一个对应的活动用于开关按钮，需要手动激活
	if self.is_first_init then
		self.is_first_init = false
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.MONTH_CARD_PROVOLEGE, ACTIVITY_STATUS.OPEN)
		end)
	end
end

function RechargeWGCtrl:SendBuyInvestCard(card_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBuyInvestCard)
	protocol.card_type = card_type or 0
	protocol:EncodeAndSend()
end

function RechargeWGCtrl:SendFetchInvestCardReward(card_type, day) --发送领取
	local protocol = ProtocolPool.Instance:GetProtocol(CSFetchInvestCardReward)
	protocol.card_type = card_type or 0
	protocol.day = day or 0
	protocol:EncodeAndSend()
end

function RechargeWGCtrl:BuyInvestCard(card_type)
	local card_cfg = RechargeWGData.Instance:GetTZCardCfg(card_type)
	if not card_cfg then
		return
	end
	local can_active = RechargeWGData.Instance:CanActiveTZCard(card_type)
	if not can_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.NotTZCardTips)
		return
	end
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	if role_vip_level < card_cfg.invest_need_vip_level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Recharge.NotVipLvelTips, card_cfg.invest_need_vip_level))
		return
	end
	local gold = RoleWGData.Instance:GetAttr('gold')
	if gold < card_cfg.invest_need_gold then
		VipWGCtrl.Instance:OpenTipNoGold()
		return
	end
	if card_cfg.invest_need_gold >= COMMON_CONSTS.AlertConst then
		TipWGCtrl.Instance:OpenAlertTips(
		string.format(Language.Recharge.AlertTips, card_cfg.invest_need_gold, Language.Recharge.InvestCardName
		[card_type]), function()
			RechargeWGCtrl.Instance:SendBuyInvestCard(card_type)
		end)
	else
		RechargeWGCtrl.Instance:SendBuyInvestCard(card_type)
	end
end

--]]

--每周必买操作请求
function RechargeWGCtrl:CSPeriodOperaReq(opera_type, param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPeriodOperaReq)
	protocol.op_type = opera_type or 0
	protocol.param = param or 0
	protocol:EncodeAndSend()
end

-- --每周必买返回
-- function RechargeWGCtrl:OnSCPeriodAllInfo(protocol)
-- 	self.recharge_data:SetWeekBuyInfo(protocol)
-- 	if self.recharge_all_view:IsOpen() then
-- 		self.recharge_all_view:Flush(TabIndex.new_recharge_weekbuy)
-- 	end
-- 	--RemindManager.Instance:Fire(RemindName.Week_Buy)
-- end

function RechargeWGCtrl:OnClickGetInvestPlanTwo()
	self.recharge_all_view:OnClickGetInvestPlanTwo()
end

function RechargeWGCtrl:GetPanelShowIndex()
	if self.recharge_all_view:IsOpen() then
		local index = self.recharge_all_view:GetShowIndex()
		return index == TabIndex.recharge_cz
	end
end

-- F2 特权投资传闻跳转
function RechargeWGCtrl:TouZiPlanTurn(plan_type)
	local cfg_data = RechargeWGData.Instance:GetTouZiPlanCfgList(plan_type, 1)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if not cfg_data or role_level < cfg_data.min_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.TQTZLevelLow)
		return
	end
	if RechargeWGData.Instance:TouZiPlanIsFinish(plan_type, true) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.TQTZGetAllReward)
		return
	end
	self.recharge_all_view:Open(TabIndex.recharge_tqtz)
	self.recharge_all_view:Flush(TabIndex.recharge_tqtz, nil, { plan_type = plan_type })
end

function RechargeWGCtrl:BuyTouZiPlan(plan_type, plan_grade)
	local cfg_data = RechargeWGData.Instance:GetTouZiPlanCfgList(plan_type, plan_grade)
	if not cfg_data then
		return
	end
	local ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(plan_type)
	local cur_grade = ser_data and ser_data.cur_grade or 0
	local old_cfg_data = RechargeWGData.Instance:GetTouZiPlanCfgList(plan_type, cur_grade)
	local old_const = old_cfg_data and old_cfg_data.price or 0
	local now_const = cfg_data.price - old_const
	local tip_str = old_const > 0 and Language.Recharge.TZPlanZhuiJiaTip or Language.Common.CommonAlertFormat3
	if cfg_data.money_type == 1 then
		now_const = RoleWGData.GetPayMoneyStr(now_const)
		tip_str = Language.Common.CommonAlertFormat4
	end
	TipWGCtrl.Instance:OpenAlertTips(string.format(tip_str, now_const, cfg_data.show_name), function()
		if cfg_data.money_type == 1 then
			RechargeWGCtrl.Instance:Recharge(cfg_data.price, cfg_data.rmb_type, cfg_data.rmb_seq)
		elseif cfg_data.money_type == 2 then
			ServerActivityWGCtrl.Instance:SendTouzijihuaActive(plan_type, plan_grade)
		end
	end)
end

-- F2 VIP零元购
function RechargeWGCtrl:OnSCVipZeroBuyInfo(protocol)
	self.recharge_data:SetVipZeroBuyInfo(protocol)
	self:Flush(TabIndex.recharge_zerobuy)
	RemindManager.Instance:Fire(RemindName.Vip_ZeroBuy)
	MainuiWGCtrl.Instance:FlushView(0, "vip_zero_buy")
end

function RechargeWGCtrl:FlushMonthCardProvolegeView()
	if self.monthcard_provolege_view:IsOpen() then
		self.monthcard_provolege_view:Flush()
	end
end

function RechargeWGCtrl:SendUsedReplaceCoin(money_num, money_type, money_flag, virtual_gold_type, discount_ticket_index, custom_param)
	local now_time = Status.NowTime
	if now_time < self.replace_coin_recharge_time + 1 then
		return
	end

	self.replace_coin_recharge_time = now_time
	local protocol = ProtocolPool.Instance:GetProtocol(CSVirtualRechargeReq)
	protocol.money_num = money_num or 0
	protocol.money_type = money_type or 0
	protocol.money_flag = money_flag or 0
	protocol.virtual_gold_type = virtual_gold_type or 0
	protocol.discount_ticket_index = discount_ticket_index or -1
	protocol.custom_param = custom_param or -1
	protocol:EncodeAndSend()
end

function RechargeWGCtrl:OnVirtualGoldOtherInfo(protocol)
	self.recharge_data:SetVirtualGoldDailyInfo(protocol)
	RechargeVolumeWGData.Instance:SetCumulateData(protocol)
	RechargeVolumeWGCtrl.Instance:FlushRechargeVolume()
	MainuiWGCtrl.Instance:FlushRechargeVolume()

	-- RemindManager.Instance:Fire(RemindName.PrivilegeCollection_NSTQ)
	-- ViewManager.Instance:FlushView(GuideModuleName.PrivilegeCollectionView)
end

----------------------------周卡-----------------------------
function RechargeWGCtrl:OnSCWeekCardInfo(protocol)
	self.recharge_data:SetWeekCardInfo(protocol)
	if self.recharge_all_view:IsOpen() then
		self.recharge_all_view:Flush(TabIndex.recharge_week_card)
	end

	RemindManager.Instance:Fire(RemindName.Vip_Week)
	ViewManager.Instance:FlushView(GuideModuleName.PrivilegeShop)
	PrivilegedGuidanceWGCtrl.Instance:FlushGuidanceView()
	ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_privilege)
	RemindManager.Instance:Fire(RemindName.PrivilegedGuidance)
end

function RechargeWGCtrl:SendWeekCardOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWeekCardOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- ====================================== 充值抵扣券 ==================================================================
function RechargeWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if not self.recharge_data:GetRmbDiscountCfg(change_item_id) then
		return
	end

	-- 物品数量减少
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE or
		(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num < old_num) then
		self.recharge_data:RemoveRmbDiscountUsedCD(change_item_id)
		self:ChangeRmbDiscountCD()
	end
end

function RechargeWGCtrl:AddRmbDiscountUsedCD(item_id)
	local time = TimeWGCtrl.Instance:GetServerTime() + 15
	self.recharge_data:AddRmbDiscountUsedCD(item_id, time)
	self:ChangeRmbDiscountCD()
end

function RechargeWGCtrl:CleanUpdateRmbDiscountUsedCDTimer()
	if self.rmb_discount_used_cd_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.rmb_discount_used_cd_timer)
	end

	self.rmb_discount_used_cd_timer = nil
end

function RechargeWGCtrl:ChangeRmbDiscountCD()
	local min_time
	local list = self.recharge_data:GetRmbDiscountUsedCDList()
	for k, v in pairs(list) do
		for i, time in pairs(v) do
			if not min_time or time < min_time then
				min_time = time
			end
		end
	end

	if self.discount_coupon_view:IsOpen() then
		self.discount_coupon_view:Flush()
	end

	self:CleanUpdateRmbDiscountUsedCDTimer()
	if not min_time then
		return
	end

	local rest_time = min_time - TimeWGCtrl.Instance:GetServerTime()
	if rest_time > 0 then
		self.rmb_discount_used_cd_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.UpdateRmbDiscountCD, self),
			rest_time)
	else
		self.recharge_data:UpdateRmbDiscountCD()
	end
end

function RechargeWGCtrl:UpdateRmbDiscountCD()
	self.recharge_data:UpdateRmbDiscountCD()
	self:ChangeRmbDiscountCD()
end

-- ====================================== 充值折扣券 end ================================================================

--------------------------------------攒福特权--------------------------------------
function RechargeWGCtrl:CSZanfutequanClientReq(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSZanfutequanClientReq)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function RechargeWGCtrl:OnSCZanfutequanAllInfo(protocol)
	self.recharge_data:SetReserveCardInfo(protocol)
	if self.recharge_all_view:IsOpen() then
		self.recharge_all_view:Flush(TabIndex.recharge_reserve_card)
	end

	RemindManager.Instance:Fire(RemindName.Reserve_Zftq)
	-- 更新计时器
	self:UpdateZFTQOnlineTimeQuest()
end

function RechargeWGCtrl:ClearZFTQOnlineTimeQuest()
	if self.zftq_online_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.zftq_online_timer)
	end
	self.zftq_online_timer = nil
end

function RechargeWGCtrl:UpdateZFTQOnlineTimeQuest()
	self:ClearZFTQOnlineTimeQuest()

	local time = self.recharge_data:GetZFOnlineRewardNextTime()
	if time > 0 then
		self.zftq_online_timer = GlobalTimerQuest:AddDelayTimer(function()
			RechargeWGCtrl.Instance:CSZanfutequanClientReq(ZANFUTEQUAN_OPERA_TYPE.INFO)
		end, time + 1)
	end
end

function RechargeWGCtrl:OpenPreviewView()
	self.preview_view:Open()
end

-- ====================================== 王者特权 ==================================================================
function RechargeWGCtrl:OpenKingVipTips()
	self.king_vip_tips:Open()
end

function RechargeWGCtrl:GetKingVipTipsPageIdx()
	return self.king_vip_tips:GetPageIdx()
end

function RechargeWGCtrl:OnSCKingPrivilegeInfo(protocol)
	self.recharge_data:SetKingVipInfo(protocol)
	if self.recharge_all_view:IsOpen() then
		self.recharge_all_view:Flush(TabIndex.recharge_king_vip)
	end

	NewAppearanceWGCtrl.Instance:FlushView(nil)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)

	RemindManager.Instance:Fire(RemindName.Vip_King)
end

function RechargeWGCtrl:CSKingPrivilegeInfoReq(operate_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSKingPrivilegeInfoReq)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function RechargeWGCtrl:OnCSKingPrivilegeInfoReq()
	self:CSKingPrivilegeInfoReq()
end

--获取王者特权激活状态.
function RechargeWGCtrl:GetKingVipIsActive()
	local end_time = self.recharge_data:GetKingVipEndTime()
	if end_time <= 0 then
		return false
	end

	local server_day = TimeWGCtrl.Instance:GetServerTime()
	return end_time >= server_day
end

-- ====================================== 王者特权 end ==============================================================

-- ====================================== 贵族特权总览提示界面 ==================================================================
function RechargeWGCtrl:OpenAllPrivilegeTips()
	self.vip_all_privilege_tips:Open()
end

function RechargeWGCtrl:GetAllPrivilegeTipsPageIdx()
	return self.vip_all_privilege_tips:GetPageIdx()
end
-- ====================================== 贵族特权总览提示界面数据 end ==============================================================

--------------------------充值劵操作--------------------------------
function RechargeWGCtrl:OnSendVirualGold2Req(operate_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSVirualGold2Req)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end