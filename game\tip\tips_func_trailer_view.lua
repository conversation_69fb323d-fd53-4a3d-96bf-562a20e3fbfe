TipsFuncTrailerView = TipsFuncTrailerView or BaseClass(SafeBaseView)

local scroll_change_value = 0
local ACTIVE_WIDTH = 523 --激活后的实际宽度
local TIME = 0.4
local LEFT_PADDING = 0
--local SINGLE_WIDTH = 130 -- 单个移动距离
local MAX_TYPE = 3 --开启类型最多为3
-- 开启类型 主线等级：1，巅峰等级：2，开服天数：3
local OPEN_TYPE = {
	LEVEL = 1,
	DF_LEVEL = 2,
	DAY = 3,
}

function TipsFuncTrailerView:__init()
	self.view_name = "TipsFuncTrailerView"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/funtrailer_prefab", "func_trailer_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg()
	self.is_load = false
	self.is_play_tween_done = false
	self.select_index = 0
	--self.old_select_index = 0
	self.can_get_reward = false
	self.function_is_open = false
	self.model_view = nil
	self.head_cell = nil
	self.data_list_length = 0
	self.load_func_trailer_list_complete = nil
	self.load_func_trailer_list_complete2 = true
	self.old_len = 0
end

function TipsFuncTrailerView:__delete()
	self.select_index = 0
	--self.old_select_index = 0
	self.data_list_length = 0
end

function TipsFuncTrailerView:LoadCallBack()
	self.is_load = true
	self.page_list_container = nil
	self.cur_data = nil
	self:InitPagePanel()
	self.node_list.title_view_name.text.text = Language.Guide.GuideName
	self.node_list.title_view_name.text.color = Str2C3b("#FFF8BB")

	local bundle, asset = ResPath.GetCommonButton("a3_ty_hretutn")
	self.node_list.btn_close_window_img.image:LoadSprite(bundle, asset, function()
		self.node_list.btn_close_window_img.image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetRawImagesPNG("a3_gnkq_bg")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

end

function TipsFuncTrailerView:ReleaseCallBack()
	self.is_load = false
	self.is_play_tween_done = false
	self.open_tween_sequence = nil
	self.page_list_container = nil
	self.cur_data = nil
	self.load_func_trailer_list_complete = nil
	self.load_func_trailer_list_complete2 = true
	self.old_len = 0

	if self.func_trailer_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.func_trailer_timer)
        self.func_trailer_timer = nil
    end

	if self.model_view then
		self.model_view:DeleteMe()
		self.model_view = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

	if self.yoyo_tween then
		self.yoyo_tween:Kill()
		self.yoyo_tween = nil
	end

	if self.display_tween then
		self.display_tween:Kill()
		self.display_tween = nil
	end

	if self.func_trailer_list then
		for k, v in pairs(self.func_trailer_list) do
            v:DeleteMe()
		end

		self.func_trailer_list = nil
	end
end

function TipsFuncTrailerView:ShowIndexCallBack()
	--self:PlayOpenTween()
end

function TipsFuncTrailerView:CloseCallBack()
	if self.open_tween_sequence then
		self.open_tween_sequence:Kill(true)
		self.open_tween_sequence = nil
	end

	self.is_play_tween_done = false
end

function TipsFuncTrailerView:OnFlush()
	if self.load_func_trailer_list_complete then
		self:UpdateFuncTrailerList()
		if self.load_func_trailer_list_complete2 then
			self:FirstSelect()
			self:FlushComponentView()
			self:FlushClickTrailerList()
			self:FlushPrice()
		end
	end
end

function TipsFuncTrailerView:InitPagePanel()
	local cfg_data_list = OpenFunWGData.Instance:GetNoticeList() --功能预告所有数据
	self.data_list_length = #cfg_data_list

	self:FirstSelect()
	self:CreateFuncTrailerList(cfg_data_list)
end

function TipsFuncTrailerView:CreateFuncTrailerList(cfg_data_list)
	self.load_func_trailer_list_complete = false
    self.func_trailer_list = {}
	self.old_len = #cfg_data_list

    local res_async_loader = AllocResAsyncLoader(self, "cell_trailer")
	res_async_loader:Load("uis/view/funtrailer_prefab", "cell_trailer", nil,
		function(new_obj)
			for i, v in pairs(cfg_data_list) do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["Content"].transform, false)
				local item_render = TrailerRender.New(obj)
                item_render:SetParent(self)
				item_render:SetIndex(i)
				item_render:SetData(v)
				item_render:SetClickCallBack(BindTool.Bind(self.OnClickTrailerBtn, self))
				self.func_trailer_list[i] = item_render
				if i == #cfg_data_list then
					self.load_func_trailer_list_complete = true
				end
			end

			if self.load_func_trailer_list_complete then
				self:Flush()
			end
		end)
end

function TipsFuncTrailerView:UpdateFuncTrailerList()
	local cfg_data_list = OpenFunWGData.Instance:GetNoticeList() --功能预告所有数据
	local old_len = self.old_len
	self.old_len = #cfg_data_list

	for i = 1, old_len do
		self.func_trailer_list[i]:SetData(cfg_data_list[i])
	end

	if old_len < #cfg_data_list then
		self.load_func_trailer_list_complete2 = false
		local res_async_loader = AllocResAsyncLoader(self, "cell_trailer")
		res_async_loader:Load("uis/view/funtrailer_prefab", "cell_trailer", nil,
			function(new_obj)
				for i = old_len + 1 , #cfg_data_list do
					local obj = ResMgr:Instantiate(new_obj)
					local obj_transform = obj.transform
					obj_transform:SetParent(self.node_list["Content"].transform, false)
					local item_render = TrailerRender.New(obj)
					item_render:SetParent(self)
					item_render:SetIndex(i)
					item_render:SetData(cfg_data_list[i])
					item_render:SetClickCallBack(BindTool.Bind(self.OnClickTrailerBtn, self))
					self.func_trailer_list[i] = item_render
					if i == #cfg_data_list then
						self.load_func_trailer_list_complete2 = true
					end
				end

				if self.load_func_trailer_list_complete2 then
					self:Flush()
				end
			end)
	end
end

--切换面板刷新
function TipsFuncTrailerView:MainViewSwitch(index)
	local cfg_data_list = OpenFunWGData.Instance:GetNoticeList()
	self.cur_data = cfg_data_list[index] --获取当前红点跳转的下标的数据

	self:FlushComponentView()
	self:FlushPrice()
end

function TipsFuncTrailerView:FlushClickTrailerList()
	self:OnClickTrailerBtn(self.func_trailer_list[self.select_index], true)
end

function TipsFuncTrailerView:FlushComponentView()
	local is_show, is_open, is_get = OpenFunWGData.Instance:GetNoticeIsCanShow(self.cur_data.id)
	local can_get_reward = is_open and not is_get

	self.can_get_reward = can_get_reward
	self.function_is_open = is_open

	self.node_list["raw_img"]:SetActive(self.cur_data.is_model == 0)     --2D图显示
	self.node_list["img"]:SetActive(self.cur_data.is_model == 0)         --2D图显示
	self.node_list["Display"]:SetActive(self.cur_data.is_model == 1)     --模型显示
	self.node_list["head_cell_root"]:SetActive(true)                     --self.cur_data.is_model == 2)  --头像框显示
	self.node_list["spine_root"]:SetActive(self.cur_data.is_model == 3)  --spine动画显示

	self.is_open_task_label = true
	if not self.function_is_open then
		local role_level = RoleWGData.Instance:GetAttr("level")
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local kf_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
		--多少级开启功能描述
		if role_level < self.cur_data.open_level then
			self.node_list["label_task_desc"].text.text = self.cur_data.open_dec
		elseif kf_world_level < self.cur_data.open_cross_world_level then
			self.node_list["label_task_desc"].text.text = self.cur_data.open_world_level
		elseif open_day < self.cur_data.open_server_day then
			self.node_list["label_task_desc"].text.text = self.cur_data.open_day_dec
		else
			self.node_list["label_task_desc"].text.text = ""
			self.is_open_task_label = false
		end
	else
		self.node_list["label_task_desc"].text.text = ""
		self.is_open_task_label = false
	end

	self.node_list["label_task"]:SetActive(self.is_open_task_label)
end

function TipsFuncTrailerView:FlushPrice()
	if not self.cur_data then
		return
	end

	self:StopTween()                 --停止动画

	if 0 == self.cur_data.is_model then --设置2D图
		local is_raw = self.cur_data.is_rawimage == 1
		self.node_list["raw_img"]:SetActive(is_raw)
		self.node_list["img"]:SetActive(not is_raw)
		local path = is_raw and ResPath.GetF2RawImagesPNG or ResPath.GetF2FunTrailerImages
		local bundle, asset = path(self.cur_data.icon_view)
		local node_image = is_raw and self.node_list["raw_img"].raw_image or self.node_list["img"].image
		node_image:LoadSprite(bundle, asset, function()
			node_image:SetNativeSize()
		end)

		self:PalyYoyoTween()             --2D图动画
	elseif 1 == self.cur_data.is_model then --设置模型
		self:SetModelInfo()
	elseif 2 == self.cur_data.is_model then
		self:SetHeadInfo() --设置头像
	elseif 3 == self.cur_data.is_model then
		self:SetSpineInfo()
	end

	self:CheckDisplayNodeScale()
end

function TipsFuncTrailerView:PlayOpenTween()
	self.node_list["ui_writedepth"]:SetActive(true)
	-- self.node_list["middle_panel"].scroll_rect.enabled = false
	local left_panel = self.node_list["left_panel"]
	local middle_root = self.node_list["middle_root"]
	local middle_mask = self.node_list["middle_mask"]

	left_panel.transform.anchoredPosition = Vector2(0, 25)
	middle_mask.transform.anchoredPosition = Vector2(516, 20)
	middle_root.transform.anchoredPosition = Vector2(-1000, 0)

	local sequence = DG.Tweening.DOTween.Sequence()
	local tween_1 = left_panel.transform:DOAnchorPosX(-514, 0.5)
	local tween_2 = middle_mask.transform:DOAnchorPosX(98, 0.5)
	local tween_3 = middle_root.transform:DOAnchorPosX(16, 0.5)
	sequence:AppendInterval(0.3)
	sequence:Append(tween_1)
	sequence:Join(tween_2)
	sequence:Join(tween_3)

	sequence:OnComplete(function()
		self.is_play_tween_done = true
		self.node_list.ui_writedepth:SetActive(false)
		self.node_list.label_task:SetActive(self.is_open_task_label and self.is_play_tween_done)
		self.open_tween_sequence:Kill()
		self.open_tween_sequence = nil
	end)
	sequence:SetEase(DG.Tweening.Ease.Linear)

	self.open_tween_sequence = sequence
end

function TipsFuncTrailerView:FirstSelect()
	local cfg_data_list = OpenFunWGData.Instance:GetNoticeList()
	if IsEmptyTable(cfg_data_list) then
		return
	end
	local select_index = 1
	local is_show, is_open, is_get = nil, nil, nil
	for i, data in ipairs(cfg_data_list) do
		select_index = i
		is_show, is_open, is_get = OpenFunWGData.Instance:GetNoticeIsCanShow(data.id)
		if not is_open then
			select_index = math.max(select_index - 1, 0)
			break
		elseif is_open and not is_get then
			break
		end
	end

	if select_index == 0 then
		select_index = 1
	end

	--self.old_select_index = self.select_index
	self.select_index = select_index
	self.cur_data = cfg_data_list[self.select_index] --获取当前红点跳转的下标的数据
end

function TipsFuncTrailerView:StopTween()
	if self.yoyo_tween then
		self.yoyo_tween:Pause()
	end
	if self.display_tween then
		self.display_tween:Rewind()
	end
end

function TipsFuncTrailerView:CheckDisplayNodeScale()
	-- local ui_scale = 1

	-- if self.cur_data.inside_scale and self.cur_data.inside_scale ~= "" then
	-- 	ui_scale = self.cur_data.inside_scale
	-- end

	--Transform.SetLocalScaleXYZ(self.node_list.display_node.transform, ui_scale, ui_scale, ui_scale)
	local pos_x, pos_y = 0, 0
	if self.cur_data.inside_pos and self.cur_data.inside_pos ~= "" then
		local pos_list = string.split(self.cur_data.inside_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.display_node.rect, pos_x, pos_y)

	if self.model_view then
		if self.cur_data.inside_rotation and self.cur_data.inside_rotation ~= "" then
			local rot_list = string.split(self.cur_data.inside_rotation, "|")
			local rot_x = tonumber(rot_list[1]) or 0
			local rot_y = tonumber(rot_list[2]) or 0
			local rot_z = tonumber(rot_list[3]) or 0

			self.model_view:SetRTAdjustmentRootLocalRotation(rot_x, rot_y, rot_z)
		end

		local ui_scale = 1
		if self.cur_data.inside_scale and self.cur_data.inside_scale ~= "" then
			ui_scale = self.cur_data.inside_scale
		end

		self.model_view:SetRTAdjustmentRootLocalScale(ui_scale)
	end

	-- if self.cur_data.inside_rotation and self.cur_data.inside_rotation ~= "" then
	-- 	local rotation_tab = string.split(self.cur_data.inside_rotation, "|")
	-- 	self.node_list.display_node.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	-- end
end

function TipsFuncTrailerView:PalyCellTrailerTween(posx)
	local content_x = self.node_list["Content"].rect.anchoredPosition.x

	-- if self.old_select_index ~= self.select_index then
	-- 	local num = self.select_index - self.old_select_index
	-- 	self.old_select_index = self.select_index
	-- 	num = num > 0 and num - 1 or num
	-- 	local change_pos = SINGLE_WIDTH * num
	-- 	print_error(num)
	-- 	UITween.CleanAllMoveToShowPanel(self.view_name)
	-- 	UITween.MoveToShowPanel(self.view_name, self.node_list["Content"], Vector2(content_x, 0), Vector2(content_x - change_pos, 0), TIME, DG.Tweening.Ease.Linear)
	-- else
	local value1 = ACTIVE_WIDTH + posx - (self.node_list["trailer_list"].rect.rect.width + math.abs(content_x))
	local value2 = ACTIVE_WIDTH - (ACTIVE_WIDTH + posx - math.abs(content_x)) + LEFT_PADDING
	if value1 > 0 then
		UITween.CleanAllMoveToShowPanel(self.view_name)
		UITween.MoveToShowPanel(self.view_name, self.node_list["Content"], Vector2(content_x, 0), Vector2(content_x - value1, 0), TIME, DG.Tweening.Ease.Linear)
	elseif value2 > 0 then
		UITween.CleanAllMoveToShowPanel(self.view_name)
		UITween.MoveToShowPanel(self.view_name, self.node_list["Content"], Vector2(content_x, 0), Vector2(content_x + value2, 0), TIME, DG.Tweening.Ease.Linear)
	end
	--end
end

function TipsFuncTrailerView:PalyYoyoTween()
	if self.yoyo_tween then
		self.yoyo_tween:Restart()
		return
	end
	local tween_root = self.node_list.tween_root.rect
	self.yoyo_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 20, 1)
	self.yoyo_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	self.yoyo_tween:SetEase(DG.Tweening.Ease.Linear)
end

function TipsFuncTrailerView:PlayDisplayTween()
	if self.display_tween then
		self.display_tween:Restart()
		return
	end
	local display_rect = self.node_list["Display"].rect
	self.display_tween = display_rect:DOAnchorPosY(display_rect.anchoredPosition.y + 20, 1)
	self.display_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	self.display_tween:SetEase(DG.Tweening.Ease.Linear)
end

function TipsFuncTrailerView:SetModelInfo()
	if not self.cur_data then
		return
	end

	if not self.model_view then
		self.model_view = RoleModel.New()
		local display_root = self.node_list["Display"]

		local display_data = {
			parent_node = display_root,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}

		self.model_view:SetRenderTexUI3DModel(display_data)
		-- self.model_view:SetUI3DModel(display_root.transform, display_root.event_trigger_listener, 1, false,
		-- 	MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	-- else
	-- 	self.model_view:ClearModel()
	-- 	self.model_view:RemoveActionDelayTime()
	end

	local res_id = nil
	local bundle, asset = nil, nil
	local res_type = self.cur_data.res_type
	local res_show = self.cur_data.res_show

	if res_type == FunResType.SHEN_WU then
		local weapon_res_id = RoleWGData.GetFashionWeaponId(nil, nil, res_show)
		bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
		self:PlayDisplayTween()
	elseif res_type == FunResType.SHI_ZHUANG then
		res_id = AppearanceWGData.GetFashionBodyResIdByResViewId(res_show)
		bundle, asset = ResPath.GetRoleModel(res_id)
	elseif res_type == FunResType.FOOT then
		bundle, asset = ResPath.GetUIFootEffect(res_show)
	elseif res_type == FunResType.HALO then
		bundle, asset = ResPath.GetSoulBoyModel(res_show)
	elseif res_type == FunResType.WING then
		bundle, asset = ResPath.GetWingModel(res_show)
	elseif res_type == FunResType.MOUNT then
		bundle, asset = ResPath.GetMountModel(res_show)
	elseif res_type == FunResType.LING_CHONG then
		bundle, asset = ResPath.GetPetModel(res_show)
	elseif res_type == FunResType.LING_YI then
		bundle, asset = ResPath.GetSoulBoyModel(res_show)
	elseif res_type == FunResType.LING_GONG then
		bundle, asset = ResPath.GetSoulBoyWeaponModel2(res_show)
	elseif res_type == FunResType.FA_BAO then
		bundle, asset = ResPath.GetFaBaoModel(res_show)
	elseif res_type == FunResType.MONSTER then
		bundle, asset = ResPath.GetMonsterModel(res_show)
	elseif res_type == FunResType.ITEM_MODEL then
		bundle, asset = ResPath.GetItemModel(res_show)
	elseif res_type == FunResType.NPC then
		bundle, asset = ResPath.GetNpcModel(res_show)
	elseif res_type == FunResType.MARRY_MODEL then
		local prof = RoleWGData.Instance.role_vo.prof
		bundle, asset = ResPath.GetRoleModel(ResPath.GetFashionModelId(prof, res_show))
	elseif res_type == FunResType.QILINGBI then      --麒麟臂
		bundle, asset = ResPath.GetQilinBiModel(res_show + 1, RoleWGData.Instance.role_vo.sex, true)
	elseif res_type == FunResType.SHENSHOU then      --神兽
		bundle, asset = ResPath.GetMonsterModel(res_show)
	elseif res_type == FunResType.TIANSHEN_SHENQI then --天神神器
		bundle, asset = ResPath.GetTianShenShenQiPath(res_show)
	elseif res_type == FunResType.HUA_KUN then       --化鲲
		bundle, asset = ResPath.GetMountModel(res_show)
	elseif res_type == FunResType.JIAN_LING then     --剑灵
		bundle, asset = ResPath.GetJianZhenModel(res_show)
	elseif res_type == FunResType.SG_SHENLING then   --上古神灵
		bundle, asset = ResPath.GetShangGuJinModel(res_show)
	elseif res_type == FunResType.TIAN_SHEN then     --天神
		bundle, asset = ResPath.GetBianShenModel(res_show)
	elseif res_type == FunResType.ZHUAN_ZHI_ONE then --转职一
		bundle, asset = ResPath.GetPetModel(res_show)
	elseif res_type == FunResType.ZHUAN_ZHI_TWO then --转职二
		bundle, asset = ResPath.GetPetModel(res_show)
	elseif res_type == FunResType.GOD_BODY then      --神体
		bundle, asset = ResPath.GetFairyLandGodBodyModel(res_show, RoleWGData.Instance:GetRoleSex())
	elseif res_type == FunResType.WU_HUN_ZHEN_SHEN then -- 武魂真身
		bundle, asset = ResPath.GetWuHunModel(res_show)
	elseif res_type == FunResType.BEAST then         -- 御兽
		bundle, asset = ResPath.GetBeastsModel(res_show)
	elseif res_type == FunResType.GUNDAM then        -- 机甲
		if res_show >= 0 then
			local part_list = {}
			local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(res_show)
			for k, v in pairs(base_part) do
				local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
				part_list[part_cfg.part] = part_cfg.res_id
			end

			local mecha_part_cfg = MechaWGData.Instance:GetPartCfgBySeq(res_show)
			if not IsEmptyTable(mecha_part_cfg) then
				part_list[mecha_part_cfg.part] = mecha_part_cfg.res_id
			end

			local part_info = {
				gundam_seq = res_show,
				gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
				gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
				gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
				gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
				gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
				gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
				gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
				gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
			}

			self.model_view:SetGundamModel(part_info)
		end

		return
	elseif res_type == FunResType.SHOU_HU then         -- 守护
		bundle, asset = ResPath.GetGuardModel(res_show)
	end

	if res_id then
		local extra_role_model_data = {
			no_need_do_anim = true,
		}
		self.model_view:SetRoleResid(res_id, nil, extra_role_model_data)
	else
		self.model_view:SetMainAsset(bundle, asset)
	end
end

function TipsFuncTrailerView:SetHeadInfo()
	if not self.cur_data then
		return
	end
	if not self.head_cell then
		self.head_cell = BaseHeadCell.New(self.node_list["head_cell_root"])
		self.head_cell:SetImgBg(true)
	end
	local item_id = self.cur_data.res_show
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg then
		local photoframe_id = item_cfg.param2
		local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(SHIZHUANG_TYPE.PHOTOFRAME, photoframe_id)
		photoframe_id = cfg and cfg.resouce or photoframe_id
		local data = { fashion_photoframe = photoframe_id }
		self.head_cell:SetData(data)
		self.head_cell:SetBgActive(false)
	else
		self.node_list["head_cell_root"]:SetActive(false)
	end
end

function TipsFuncTrailerView:SetSpineInfo()
	local bundle, asset
	if self.cur_data.res_type == FunResType.SI_XIANG then
		bundle, asset = ResPath.GetFightSoulShowUI(self.cur_data.res_show)
	end

	self.node_list["spine_root"]:ChangeAsset(bundle, asset, false)
end

function TipsFuncTrailerView:OnClickTrailerBtn(cell, is_not_click)
	if not cell then
		return
	end

	local data = cell:GetData()

	local is_show = cell.is_click_cur
	local cell_index = cell:GetIndex()

	if is_not_click and is_show then
		return
	end

	for k, v in ipairs(self.func_trailer_list) do
		if cell_index ~= k or (is_show and cell_index == k) then
			v:SetHideShow()
		end
	end

	if not is_show then
		cell:DoShowTween()
	end

	if IsEmptyTable(data) or cell:GetIndex() == self.select_index then
		return
	end

	self.select_index = cell:GetIndex()
	--self.old_select_index = self.select_index
	self:MainViewSwitch(self.select_index)
end

function TipsFuncTrailerView:OnClickTurnBtn()
	TipWGCtrl.Instance:CloseFunTrailerTip()
end

function TipsFuncTrailerView:OnClickGetBtn()
	if self.can_get_reward then
		OpenFunWGCtrl.Instance:SendAdvanceNoitceOperate(ADVANCE_NOTICE_OPERATE_TYPE.ADVANCE_NOTICE_FETCH_REWARD,
			self.cur_data.id)
	elseif self.cur_data then
		if string.find(self.cur_data.open_panel_name, "new_appearance_upgrade_shenbing") then
			if TianShenJuexingWGData.Instance:GetTianShenAwakenIsEnd() or TianShenJuexingWGData.Instance:GetTianShenAwakenIsActive() == 1 then
				FunOpen.Instance:OpenViewNameByCfg(self.cur_data.open_panel_name)
			else
				FunOpen.Instance:OpenViewNameByCfg(GuideModuleName.TianShenJuexing)
			end
		else
			FunOpen.Instance:OpenViewNameByCfg(self.cur_data.open_panel_name)
		end
	end
end

----------------------------TrailerRender--------------------------
TrailerRender = TrailerRender or BaseClass(BaseRender)

function TrailerRender:LoadCallBack()
	self.reward_cell = ItemCell.New(self.node_list["price_cell"])
	XUI.AddClickEventListener(self.node_list.bg_content_btn, BindTool.Bind(self.OnClickTitle, self))
	XUI.AddClickEventListener(self.node_list.bg_content_hl_btn, BindTool.Bind(self.OnClickTitle, self))
	XUI.AddClickEventListener(self.node_list.get_reward, BindTool.Bind(self.OnClickGetBtn, self)) --领取
	XUI.AddClickEventListener(self.node_list.goto_func, BindTool.Bind(self.OnClickGoTo, self)) --前往功能
	XUI.AddClickEventListener(self.node_list.goto_upgrade, BindTool.Bind(self.OnClickTurnBtn, self)) --去升级

	self.is_click_cur = false
end

function TrailerRender:ReleaseCallBack()
	self.parent = nil
	self.is_load = false
	self.is_play_tween_done = false
	self.open_tween_sequence = nil
	self.page_list_container = nil

	self.reward_cell:DeleteMe()
	self.reward_cell = nil
end

function TrailerRender:SetClickCallBack(callback)
	self.click_callback = callback
end

function TrailerRender:OnClickTitle()
	if self.click_callback then
		self.click_callback(self, false)
	end
end

function TrailerRender:SetParent(parent)
	self.parent = parent
end

function TrailerRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	-- local bundle, asset = ResPath.GetFunTrailerImages("a3_gnkq_type" .. self.data.id)
	-- self.node_list["title_img"].image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["title_img"].image:SetNativeSize()
	-- end)

	local fun_name = self.data.fun_name
	self.node_list.name_text1.text.text = fun_name
	self.node_list.name_text2.text.text = self.data.open_dec
	self.node_list.title.text.text = "【" .. fun_name .. "】"

	local is_show, is_open, is_get = OpenFunWGData.Instance:GetNoticeIsCanShow(self.data.id)
	local can_get_reward = is_open and not is_get
	self.node_list.red_point1:SetActive(can_get_reward)

	self.reward_cell:SetData(self.data.reward_item[0])

	self.node_list["function_desc"].text.text = ("\u{3000}\u{3000}" .. self.data.fun_dec) or "" --功能描述
	self.node_list["get_remind"]:SetActive(can_get_reward)                   --领取的红点
	self.node_list["get_reward_img"]:SetActive(is_get)                   --已领取
	self.node_list["get_reward"]:SetActive(false)                      --领取按钮
	self.node_list["goto_upgrade"]:SetActive(not is_open)                --去升级 (蓝色按钮)
	self.node_list["goto_func"]:SetActive(false)

	if not is_open then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level < self.data.open_level then
			self.node_list["turn_btn_label"].text.text = Language.Guide.GotoUpLevel --去升级
		else
			self.node_list["turn_btn_label"].text.text = Language.Guide.GotoFinishTask --去任务
		end
	else
		if can_get_reward then
			self.node_list["get_reward"]:SetActive(true)
			self.node_list["goto_func"]:SetActive(false)
			--self.node_list["get_btn_label"].text.text = Language.Guide.GetbtnStr --领取
		else
			--self.node_list["get_btn_label"].text.text = Language.Guide.GotoView --前往功能
			if self.data.open_panel_name and self.data.open_panel_name ~= "" then
				self.node_list["goto_func"]:SetActive(true)
			else
				self.node_list["goto_func"]:SetActive(false)
			end

			self.node_list["get_reward"]:SetActive(false)
		end
	end
end

function TrailerRender:OnClickTurnBtn()
	TipWGCtrl.Instance:CloseFunTrailerTip()
end

function TrailerRender:OnClickGetBtn()
	local is_show, is_open, is_get = OpenFunWGData.Instance:GetNoticeIsCanShow(self.data.id)
	local can_get_reward = is_open and not is_get
	if can_get_reward then
		OpenFunWGCtrl.Instance:SendAdvanceNoitceOperate(ADVANCE_NOTICE_OPERATE_TYPE.ADVANCE_NOTICE_FETCH_REWARD,
			self.data.id)
	end
end

function TrailerRender:OnClickGoTo()
	local is_show, is_open, is_get = OpenFunWGData.Instance:GetNoticeIsCanShow(self.data.id)
	local can_get_reward = is_open and not is_get
	if not can_get_reward and self.data then
		if string.find(self.data.open_panel_name, "new_appearance_upgrade_shenbing") then
			if TianShenJuexingWGData.Instance:GetTianShenAwakenIsEnd() or TianShenJuexingWGData.Instance:GetTianShenAwakenIsActive() == 1 then
				FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel_name)
			else
				FunOpen.Instance:OpenViewNameByCfg(GuideModuleName.TianShenJuexing)
			end
		else
			FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel_name)
		end
	end
end

function TrailerRender:SetHideShow()
	self.node_list.bg_content_hl:CustomSetActive(false)

	if self.is_click_cur then
        self.is_click_cur = false
		self.node_list.content_root.rect:DOSizeDelta(Vector2(0, 552), 0.1):OnComplete(function()
			if not self.is_click_cur then
				self.node_list.content_root:SetActive(false)
			end
		end)
		--self.node_list.bg_content:SetActive(true)
		self.node_list.interval:SetActive(true)
	end
end

function TrailerRender:DoShowTween()
	self.is_click_cur = true
	self.node_list.content_root:SetActive(true)
	--self.node_list.bg_content:SetActive(false)
	self.node_list.interval:SetActive(false)
	self.node_list.bg_content_hl:SetActive(true)
	RectTransform.SetSizeDeltaXY(self.node_list.content_root.rect, 0, 552)
	self.node_list.content_root.rect:DOSizeDelta(Vector2(364, 552), 0.4)

	if self.func_trailer_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.func_trailer_timer)
		self.func_trailer_timer = nil
	end

	self.func_trailer_timer = GlobalTimerQuest:AddDelayTimer(function()
		local posx = self.node_list.cell_trailer.rect.anchoredPosition.x
		self.parent:PalyCellTrailerTween(posx)
	end, 0.2)
end