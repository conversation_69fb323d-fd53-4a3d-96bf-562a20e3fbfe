----------------------------------------------------
-- 祝福面板TIPS窗口
-- 界面UI放在itemtip.fla里面的。需要修改可去那改再导出
----------------------------------------------------
BlessTip = BlessTip or BaseClass(SafeBaseView)
function BlessTip:__init()
	if BlessTip.Instance then
		ErrorLog("[BlessTip] Attemp to create a singleton twice !")
	end
	self:SetMaskBg(true)
	self.ui_config = {"uis/view/itemtip_ui_prefab", "ItemtipUi"}
	self.config_tab = {
		{"layout_bless_tip", {0}}
	}
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_bless_tip")

	self.is_update_time = false

	BlessTip.Instance = self
end

function BlessTip:__delete()
	BlessTip.Instance = nil
end

function BlessTip:ReleaseCallBack()
	-- body
end

function BlessTip:LoadCallBack()
	self.node_list["lbl_bless_purpose"].text.text = Language.Tip.BlessPurposeDesc
	self.node_list["lbl_bless_clear_time"].text.text = Language.Tip.BlessClearTimeDesc

	self.node_list["btn_operate"].button:addClickEventListener(BindTool.Bind1(self.OnClickHandle, self))
end

--设置数据
function BlessTip:SetData(procee_val, procee_val_limit, next_clear_bless_time)
	self.percent = 0
	self.next_clear_bless_time = next_clear_bless_time or 0
	self.procee_val = procee_val or 0
	self.procee_val_limit = procee_val_limit or 0
	self.is_update_time = true
	if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function BlessTip:OpenCallBack()
	self:Flush()
end

function BlessTip:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("bless_time_update") then
		CountDownManager.Instance:RemoveCountDown("bless_time_update")
	end
end

function BlessTip:OnFlush()
	self.node_list["lbl_process_val"].text.text = self.procee_val .. "/" .. self.procee_val_limit
	self.percent = self.procee_val / self.procee_val_limit * 100
	self.node_list["prog9_process"]:setPercent(self.percent)
	if true == self.is_update_time then
		self.is_update_time = false
		self:UpdateDaojishi()
	end
end

function BlessTip:UpdateDaojishi()
	if CountDownManager.Instance:HasCountDown("bless_time_update") then
		CountDownManager.Instance:RemoveCountDown("bless_time_update")
	end
	if nil == self.next_clear_bless_time then return end
	
	self:UpdataTime(TimeWGCtrl.Instance:GetServerTime(), self.next_clear_bless_time)
	CountDownManager.Instance:AddCountDown("bless_time_update", BindTool.Bind1(self.UpdataTime, self), BindTool.Bind1(self.CompleteTime, self), self.next_clear_bless_time, nil, 1)
end

function BlessTip:UpdataTime(elapse_time, total_time)
	local time_str = TimeUtil.FormatSecond(total_time - elapse_time)
	self.node_list["lbl_remain_time"].text.text = time_str
end

function BlessTip:CompleteTime()
	-- body
end

function BlessTip:OnCloseHandler()
	self:Close()

	RoleWGCtrl.Instance:Close()
end

function BlessTip:OnClickHandle()
	self:Close()
end