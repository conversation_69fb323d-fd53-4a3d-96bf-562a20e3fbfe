function CangJinExchangeView:LoadIndexCallBackShop()
    if not self.shop_grid_list then
		self.shop_grid_list = AsyncBaseGrid.New()
		self.shop_grid_list:CreateCells({col = 4,change_cells_num = 1, list_view = self.node_list["shop_grid_list"],
		assetBundle = "uis/view/cangjin_shop_prefab", assetName = "shop_item",  itemRender = CangJinExchangeShopCell})
		self.shop_grid_list:SetStartZeroIndex(false)
	end

    XUI.AddClickEventListener(self.node_list["refresh_shop_btn"], BindTool.Bind(self.OnClickFlushShop, self))
    XUI.AddClickEventListener(self.node_list["btn_preview"], BindTool.Bind(self.OnClickPreview, self))
end

function CangJinExchangeView:ShowIndexCallBackShop()

end

function CangJinExchangeView:ReleaseCallBackShop()
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.shop_grid_list then
        self.shop_grid_list:DeleteMe()
        self.shop_grid_list = nil
    end
end

function CangJinExchangeView:OnFlushShop()
    local shop_info = CangJinShopWGData.Instance:GetShowLimitShopInfo()
    if self.shop_grid_list then
		self.shop_grid_list:SetDataList(shop_info)
	end

    local refresh_cfg = CangJinShopWGData.Instance:GetLimitShopRefreshCfg()
    self.node_list["flush_gold"].text.text = refresh_cfg.consume_count or 0

    local limit_shop_refresh_count = CangJinShopWGData.Instance:GetLimitShopRefreshCount()
    local other_cfg = CangJinShopWGData.Instance:GetOtherCfg()
    local need_count = other_cfg.sp_reward_need_num - limit_shop_refresh_count
    self.node_list.shop_lucky_reward_text.text.text = string.format(Language.CangJinShopView.ShopNeedCount, need_count)
end


function CangJinExchangeView:OnClickFlushShop()
    local refresh_cfg = CangJinShopWGData.Instance:GetLimitShopRefreshCfg()
    local flush_money = refresh_cfg.consume_count or 0
    local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(flush_money)
    local str = string.format(Language.CangJinShopView.ShopFlushConfirm, flush_money)
    local ok_func = function ()
        if have_enough then
            CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.REFRESH_LIMIT_SHOP)
        else
            UiInstanceMgr.Instance:ShowChongZhiView()
        end
    end

    TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, "cangjin_exchange_shop", nil)
end

function CangJinExchangeView:OnClickPreview()
    ViewManager.Instance:Open(GuideModuleName.CangJinExchangeShopPreview)
end

------------------CangJinExchangeShopCell-------------
CangJinExchangeShopCell = CangJinExchangeShopCell or BaseClass(BaseRender)
function CangJinExchangeShopCell:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
    end

    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuy, self))
end

function CangJinExchangeShopCell:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function CangJinExchangeShopCell:OnFlush()
    if self.data == nil then
        return
    end

    local reward_cfg = self.data.cfg.reward_item[0]
    if reward_cfg then
        self.item_cell:SetData(reward_cfg)
    end

    local item_name = ItemWGData.Instance:GetItemName(reward_cfg.item_id)
    self.node_list["item_name"].text.text = item_name

    local refresh_type = self.data.cfg.refresh_type or 0
    self.node_list.limit_bg:SetActive(refresh_type ~= 0)
    if refresh_type ~= 0 then
        local remain_times = self.data.cfg.buy_limit - self.data.buy_times
        self.node_list["limit_times"].text.text = string.format(Language.CangJinShopView.RefreshType[refresh_type], remain_times)
        self.node_list["is_buy_sold"]:SetActive(remain_times <= 0)
        XUI.SetButtonEnabled(self.node_list["btn_buy"], remain_times > 0)
    else
        self.node_list["is_buy_sold"]:SetActive(false)
        XUI.SetButtonEnabled(self.node_list["btn_buy"], true)
    end

    local discount = self.data.cfg.discount
    if discount and discount ~= "" then
        self.node_list.discount_text.text.text = discount
        self.node_list.discount:CustomSetActive(true)
    else
        self.node_list.discount:CustomSetActive(false)
    end

    self.node_list["score_text"].text.text = string.format(Language.CangJinShopView.JiFen, self.data.cfg.consume_score)

    local is_grand_prize = self.data.cfg.is_grand_prize
    self.node_list.bg_hl:CustomSetActive(is_grand_prize and is_grand_prize == 1)
end

function CangJinExchangeShopCell:OnClickBuy()
    if self.data == nil then
        return
    end

    local refresh_type = self.data.cfg.refresh_type or 0
    local consume_score = self.data.cfg.consume_score or 0
    local cur_score = CangJinShopWGData.Instance:GetCurScore()
    if cur_score < consume_score then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.NoEnoughScore)
        RechargeWGCtrl.Instance:RemindRechargeByCangJinShangPuScoreNoEnough(consume_score - cur_score)
        return
    end

    if refresh_type ~= 0 then
        local remain_times = self.data.cfg.buy_limit - self.data.buy_times
        if remain_times <= 0 then
            TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.MaxExchangeNum)
            return
        end
    end

    CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.BUY_LIMIT_SHOP, self.data.cfg.seq, 1)
end