FortuneCatWGData = FortuneCatWGData or BaseClass()


FortuneCatWGData.OA_FORTUNECAT_OPER =
{
	OA_FORTUNECAT_TYPE_DRAW = 1,			    -- 抽奖
    OA_FORTUNECAT_TYPE_SEND_PERSON_INFO = 2,	-- 个人信息
    OA_FORTUNECAT_TYPE_SEND_SERVER_INFO = 3,    -- 全服信息
}
function FortuneCatWGData:__init()
	if FortuneCatWGData.Instance ~= nil then
		ErrorLog("[FortuneCatWGData] attempt to create singleton twice!")
		return
	end
    FortuneCatWGData.Instance = self
    
    local fortune_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_fortune_cat_auto")
    self.fortune_cat_recharge_cfg = fortune_cfg.recharge_cfg
    self.fortune_cat_consume_cfg = fortune_cfg.consume_cfg
    self.fortune_cat_other = fortune_cfg.other[1]
    self.fortune_cat_reward_cfg = fortune_cfg.reward_grade_cfg
    self.fortune_cat_data = {
        person_draw_count = 0,
        server_draw_count = 0,
        person_remain_times = 0,
        world_record_list = {},
        person_record_list = {}
    }
    self:CountTotalTarget()
    RemindManager.Instance:Register(RemindName.FortuneCatRemind, BindTool.Bind(self.GetFortuneCatRemind, self))
    self.role_level_change = GlobalEventSystem:Bind(OtherEventType.ROLE_LEVEL_UP,BindTool.Bind(self.FireRemind, self))
	self.cur_index = 0
end

function FortuneCatWGData:__delete()
    FortuneCatWGData.Instance = nil
    -- if self.load_complete then
	-- 	GlobalEventSystem:UnBind(self.load_complete)
	-- 	self.load_complete = nil
    -- end
    if self.role_level_change then
		GlobalEventSystem:UnBind(self.role_level_change)
		self.role_level_change = nil
	end
    RemindManager.Instance:UnRegister(RemindName.FortuneCatRemind)
end

function FortuneCatWGData:FireRemind()
    RemindManager.Instance:Fire(RemindName.FortuneCatRemind)
end

function FortuneCatWGData:GetFortuneCatRemind()
    local act_is_open = self:GetFortuneCatIsOpen()
	if not act_is_open then
		return 0
    end
    if self.fortune_cat_data.person_remain_times > 0 and self:GetWorldRemainTimes() > 0 then
        return 1
    end
    return 0
end

function FortuneCatWGData:GetRewardCfg()
    return self.fortune_cat_reward_cfg
end

function FortuneCatWGData:SetFortuneCatRecordData(protocol)
    self.fortune_cat_data.world_record_list = protocol.world_record_list
    self.fortune_cat_data.person_record_list = protocol.person_record_list
end

function FortuneCatWGData:SetFortuneCatPersonData(protocol)
    self.fortune_cat_data.person_draw_count = protocol.person_draw_count
    self.fortune_cat_data.server_draw_count = protocol.server_draw_count
    self.fortune_cat_data.next_draw_recharge_num = protocol.next_draw_recharge_num
    self.fortune_cat_data.person_remain_times = protocol.person_remain_times
    self.fortune_cat_data.recharge_value = protocol.recharge_value
end

function FortuneCatWGData:SetFortuneCatRewardInfo(protocol)
    self.consume_gold_type = protocol.consume_gold_type			-- 当前抽的类型
	self.get_num = protocol.get_num								-- 抽中的数量
	self.cur_index = protocol.cur_index 						-- 当前抽中的索引
end

function FortuneCatWGData:GetFortuneCatCurIndex()
    return self.cur_index
end

function FortuneCatWGData:GetFortuneCatNextRechargeNum()
    if self:GetCurRemainTimes() + self:GetCurTimes() == 0 and self.fortune_cat_data.next_draw_recharge_num == 0 then
        return self:GetCurRechargeValue(0)
    end
    return self.fortune_cat_data.next_draw_recharge_num or 0
end

function FortuneCatWGData:GetWorldRemainTimes()
    local total = self.fortune_cat_other.server_draw_count
    local remain = total - self.fortune_cat_data.server_draw_count
    return  remain > 0 and remain or 0
end

--1个人2全服
function FortuneCatWGData:GetRecordListByType(index)
    local data_list = {}
    if index == 1 then
        data_list = self.fortune_cat_data.person_record_list
    else
        data_list = self.fortune_cat_data.world_record_list
    end
    return data_list
end

function FortuneCatWGData:GetCurTimes()
    return self.fortune_cat_data.person_draw_count
end

function FortuneCatWGData:GetCurRemainTimes()
    return self.fortune_cat_data.person_remain_times
end

function FortuneCatWGData:GetFortuneCatRechargeValue()
    local index = self.fortune_cat_data.person_draw_count + self.fortune_cat_data.person_remain_times
    local total_target = 0
    if self.target_list[index] then
        total_target = self.target_list[index].total_target
    end
    return self.fortune_cat_data.recharge_value + total_target
end

function FortuneCatWGData:SetSkipAniFlag()
    if self.skip_to_ani == 1 then
        self.skip_to_ani = 0
    else
        self.skip_to_ani = 1
    end
    PlayerPrefsUtil.SetInt("skip_to_ani"..RoleWGData.Instance:GetRoleInfo().role_id, self.skip_to_ani)
end

function FortuneCatWGData:GetSkipAniFlag()
    local flag = PlayerPrefsUtil.GetInt("skip_to_ani"..RoleWGData.Instance:GetRoleInfo().role_id)
    if flag then
        self.skip_to_ani = flag
    else
        self.skip_to_ani = 0
    end
	return self.skip_to_ani == 1
end

function FortuneCatWGData:GetCurRechargeValue(cur_times)
    cur_times = cur_times + 1
    if not IsEmptyTable(self.fortune_cat_recharge_cfg) then
        for k, v in pairs(self.fortune_cat_recharge_cfg) do
            if v.min_count <= cur_times and v.max_count >= cur_times then
                return v.recharge_value
            end
        end
        return self.fortune_cat_recharge_cfg[#self.fortune_cat_recharge_cfg].recharge_value
    end
    return 0
end

-- 计算累加充值金额
function FortuneCatWGData:CountTotalTarget()
    local temp_list = {}
    for k, v in pairs(self.fortune_cat_recharge_cfg) do
        temp_list[v.min_count] = v.recharge_value
    end

    self.target_list = {}
    local total_target = 0
    for i, v in ipairs(temp_list) do
        total_target = total_target + v
        local temp_data = {}
        temp_data.min_count = i
        temp_data.total_target = total_target
        self.target_list[i] = temp_data
    end
end

function FortuneCatWGData:GetTargetDataList()
    if IsEmptyTable(self.target_list) then
        self:CountTotalTarget()
    end
    return self.target_list

end

function FortuneCatWGData:GetCurConsumeValue(cur_times)
    cur_times = cur_times + 1
    if not IsEmptyTable(self.fortune_cat_consume_cfg) then
        for k, v in pairs(self.fortune_cat_consume_cfg) do
            if v.min_count <= cur_times and v.max_count >= cur_times then
                return v.consume_value, v.money_type
            end
        end

        local last_data = self.fortune_cat_consume_cfg[#self.fortune_cat_consume_cfg]
        return last_data.consume_value, last_data.money_type
    end

    return 0, 1
end

function FortuneCatWGData:JudgeIsNotOpenByLv()
    local lv = RoleWGData.Instance:GetRoleLevel()
    local limit_lv = self.fortune_cat_other.open_level or 0
    return lv < limit_lv, limit_lv
end

function FortuneCatWGData:GetFortuneCatIsOpen()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT) then
		return false
	end
	return not self:JudgeIsNotOpenByLv()
end

function FortuneCatWGData:GeOtherCfg()
    return self.fortune_cat_other
end

function FortuneCatWGData:GeOtherCfgByKey(key)
    return self.fortune_cat_other[key]
end