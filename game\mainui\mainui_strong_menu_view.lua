MianuiStrongMenuView = MianuiStrongMenuView or BaseClass(SafeBaseView)

local DEFAULT_POS = { 260, 8 }

function MianuiStrongMenuView:__init()
	self.view_layer = UiLayer.Pop
	--self:SetMaskBg(true, true)
	--self.mask_alpha = 0
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:AddViewResource(0, "uis/view/activity_ui_prefab", "layout_actbtn_list2")
end

function MianuiStrongMenuView:LoadCallBack()
	-- self.bianqiang_list = {}

	if not self.bianqiang_item_list then
		self.bianqiang_item_list = AsyncListView.New(OpenServerMenuItem, self.node_list.bianqiang_item_list)
		self.bianqiang_item_list:SetDefaultSelectIndex(nil)
		self.bianqiang_item_list:SetSelectCallBack(BindTool.Bind(self.OnClickOpen, self))
	end

	XUI.AddClickEventListener(self.node_list.btn_close_mask, BindTool.Bind1(self.Close, self))
end

function MianuiStrongMenuView:ReleaseCallBack()
	if self.bianqiang_item_list then
		self.bianqiang_item_list:DeleteMe()
		self.bianqiang_item_list = nil
	end

	-- if self.bianqiang_list then
	-- 	for i,v in pairs(self.bianqiang_list) do
	-- 		v:DeleteMe()
	-- 	end
	-- end
	-- self.bianqiang_list = {}

	if self.bottom_obj then
		self.bottom_obj:DeleteMe()
		self.bottom_obj = nil
	end

	self.open_data = nil
end

function MianuiStrongMenuView:OpenCallBack()
	MainuiWGCtrl.Instance:CleanBianQiangBubbleTipsTimer()
	MainuiWGCtrl.Instance:ShowBianQiangBubbleTips(false)
end

function MianuiStrongMenuView:CloseCallBack()
	MainuiWGCtrl.Instance:UpdateBianQiangBubbleTips()
end

function MianuiStrongMenuView:SetDataAndOpen(data)
	self.open_data = data

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function MianuiStrongMenuView:OnFlush()
	if not IsEmptyTable(self.open_data) then
		self:SetPosition(self.open_data)
		self.open_data = nil
	else
		-- self.node_list.root.rect.anchoredPosition = Vector2(DEFAULT_POS[1], DEFAULT_POS[2])
	end

	local data_list = MainuiWGData.Instance:GetBianQiangListCache()

	if not data_list then return end
	local _tmp = {}
	for i,v in ipairs(data_list) do
		if v and v.repetition_num > 0 and v.callback ~= nil then
			table.insert(_tmp, v)
		end
	end

	self.bianqiang_item_list:SetDataList(_tmp)

	-- for i,v in pairs(self.bianqiang_list) do
	-- 	v:SetActive(false)
	-- end

	-- local normal_bianqiang_amount = 0
	-- for i,v in ipairs(_tmp) do
	-- 	if not self.bianqiang_list[i] then
	-- 		local go = ResMgr:Instantiate(self.node_list["btn_item_prefab"].gameObject)
	-- 		go:SetActive(true)
	-- 		go.transform:SetParent(self.node_list["bianqiang_list"].transform, false)
	-- 		self.bianqiang_list[i] = OpenServerMenuItem.New(go)
	-- 		self.bianqiang_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickOpen, self))
	-- 	end
	-- 	self.bianqiang_list[i]:SetActive(true)
	-- 	self.bianqiang_list[i]:SetData(v)
	-- 	normal_bianqiang_amount = normal_bianqiang_amount + 1
	-- end

	self:FlushBottomBtn()
end

-- 刷新底部按钮
-- 特殊需求：当没有其他变强的时候底部按钮不要显示
function MianuiStrongMenuView:FlushBottomBtn()
	local data = MainuiWGData.Instance:GetTopPriorityBianQiangBottomBtnInfo()
	if self.node_list["bottom_item"] then
		self.node_list["bottom_item"]:SetActive(data ~= nil and RechargeWGData.IsOpenRecharge)
	end
	if data then 
		if not self.bottom_obj then
			self.bottom_obj = BianQiangBottomMenuItem.New(self.node_list["bottom_item"])
		end
		self.bottom_obj:SetData(data)
	end
end

function MianuiStrongMenuView:OnClickOpen(item)
	if nil == item.data then
		return
	end
	if item.data.callback then
		local flag = item.data.callback(item.data.callback_param or nil)
		if not flag then
			MainuiWGCtrl.Instance:RemoveTipIconByIconObj(item.data.tip_type, true)
		end
	end
	self:Close()
end

-- 由于打开动画，实际偏移值会略有差距
function MianuiStrongMenuView:SetPosition(param)
	if param and param.s_pos then
		local offset_x = param.offset_x or 0
		local offset_y = param.offset_y or 0
		local rect = self.node_list.root.rect
		local p_rect = self.node_list.root.transform.parent:GetComponent(typeof(UnityEngine.RectTransform))
		local _, local_pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(p_rect, param.s_pos, UICamera, Vector2(0, 0))
		local posx = local_pos.x + offset_x
		local posy = rect.localPosition.y + offset_y
		rect.localPosition = Vector2(posx, posy)
	end
end

----------------------------------------------OpenServerMenuItem----------------------------------------------
OpenServerMenuItem = OpenServerMenuItem or BaseClass(BaseRender)
function OpenServerMenuItem:OnFlush()
	if self.data then
		local is_special = self.data.tip_type == MAINUI_TIP_TYPE.SHOU_CHONG_TE_HUI
		if not is_special then
			self.node_list["lbl_caozuo_name"].text.text = Language.MainUIIcon[self.data.tip_type]
		else
			self.node_list["lbl_caozuo_name"].text.text = ""
		end
	end
end

------------------------------------------- 首充特惠 -------------------------------------------------
BianQiangBottomMenuItem = BianQiangBottomMenuItem or BaseClass(BaseRender)
function BianQiangBottomMenuItem:OnFlush()
	if self.data then
		self.node_list["btn_bg"].image:LoadSprite(ResPath.GetActivityUi(self.data.btn_res))
		-- self.node_list["btn_title"].text.text = self.data.title_desc
		self.node_list["desc"].text.text = self.data.desc
		self:SetClickCallBack(BindTool.Bind(self.OnClick, self))
	end
end

function BianQiangBottomMenuItem:OnClick(item)
	if self.data then
		if self.data.open_param == GuideModuleName.LingZhiZhiGouTips then
			local lingzhi_type = nil
			if self.data.id == BIANQIANG_BOTTOM_ID.LINGZHI_WING then
				lingzhi_type = LINGZHI_SKILL_TYPE.WING
			elseif self.data.id == BIANQIANG_BOTTOM_ID.LINGZHI_FABAO then
				lingzhi_type = LINGZHI_SKILL_TYPE.FABAO
			elseif self.data.id == BIANQIANG_BOTTOM_ID.LINGZHI_JIANZHEN then
				lingzhi_type = LINGZHI_SKILL_TYPE.JIANZHEN
			elseif self.data.id == BIANQIANG_BOTTOM_ID.LINGZHI_SHENBING then
				lingzhi_type = LINGZHI_SKILL_TYPE.SHENBING
			end

			if lingzhi_type then
				local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
				if server_info and not IsEmptyTable(server_info) then
					LingZhiWGCtrl.Instance:OpenLingZhiZhiGouTip(server_info)
				end
			end
		else
			FunOpen.Instance:OpenViewNameByCfg(self.data.open_param)
		end
	end

	MainuiWGCtrl.Instance:CloseStrongMenu()
end