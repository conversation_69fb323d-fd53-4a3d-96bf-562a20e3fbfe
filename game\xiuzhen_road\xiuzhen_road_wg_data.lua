XiuZhenRoadWGData = XiuZhenRoadWGData or BaseClass()
function XiuZhenRoadWGData:__init()
	if XiuZhenRoadWGData.Instance then
		ErrorLog("[XiuZhenRoadWGData] Attemp to create a singleton twice !")
	end
	XiuZhenRoadWGData.Instance = self
	self.xiuzhen_cfg = ConfigManager.Instance:GetAutoConfig("xiuzhen_auto")
	self.task_cfg = ListToMap(self.xiuzhen_cfg.task, "open_day", "task_seq")
	self.task_tab_type_cfg = ListToMapList(self.xiuzhen_cfg.task, "open_day", "tab_type")
	RemindManager.Instance:Register(RemindName.XiuZhenRoad, BindTool.Bind(self.IsShowXiuZhenRoadRedPoint, self))

	self.gift_info_list = {}
	self.login_timestamp = nil
	self.first_login_red = true    --每次登录游戏的红点
end

function XiuZhenRoadWGData:__delete()
	XiuZhenRoadWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.XiuZhenRoad)
	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end
end

--获取当前点击页签的数据信息
function XiuZhenRoadWGData:GetCurViewNeedInfo(open_day,tab_type)
	local daily_cfg = self.xiuzhen_cfg.daily_cfg
	local max_task = daily_cfg[open_day].task_max_num
	local cur_need_info = {}
	for i=1,max_task do
		local task_info = {}
		local task_states = 1 --默认未达成
		local old_info = self.task_cfg[open_day][i]
		if old_info.tab_type == tab_type then
			local cur_task_info = self.task_list[old_info.seq]
			if cur_task_info then
				if old_info.task_type == 90 then
					local is_finish = cur_task_info.task_progress > 0 and cur_task_info.task_progress <= old_info.param_1
					if is_finish and 1 == cur_task_info.fetch_reward_flag then
						task_states = XIUZHENZHILU_TASK_STATES.YLQ
					elseif is_finish and 0 == cur_task_info.fetch_reward_flag then
						task_states = XIUZHENZHILU_TASK_STATES.KLQ
					else
						task_states = XIUZHENZHILU_TASK_STATES.WDC
					end
				else
					if cur_task_info.task_progress >= old_info.param_1 and 1 == cur_task_info.fetch_reward_flag then
						task_states = XIUZHENZHILU_TASK_STATES.YLQ
					elseif cur_task_info.task_progress >= old_info.param_1 and 0 == cur_task_info.fetch_reward_flag then
						task_states = XIUZHENZHILU_TASK_STATES.KLQ
					else
						task_states = XIUZHENZHILU_TASK_STATES.WDC
					end
				end
			end
			task_info["old_info"] = old_info
			task_info["task_states"] = task_states
			task_info["task_progress"] = cur_task_info.task_progress
			task_info["task_seq"] = old_info.task_seq
			table.insert(cur_need_info, task_info)
		end
	end
	table.sort(cur_need_info,SortTools.KeyLowerSorter("task_states", "task_seq"))
	return cur_need_info
end

function XiuZhenRoadWGData:GetJumpIndex(open_day)
	local all_info = self:GetCueViewAllTaskInfo(open_day)
	local jump_index = 1
	for i = 1, 3 do
		for k, v in pairs(all_info) do
			if v.old_info.tab_type == i and v.task_states ~= XIUZHENZHILU_TASK_STATES.YLQ then
				jump_index = i
				return jump_index
			end
		end
	end

	return jump_index
end

--策划新增 每次登录游戏 灯笼都会显示红点 去提醒玩家购买礼包
--大圣归来三种礼包购买的红点判断（一到七天的全部都买了 每次登录才不显示灯笼红点）
function XiuZhenRoadWGData:GetRMBBuyRed()
	local cur_day = self:GetActivityOpenDay()
	local daily_cfg = self:GetXiuZhenZhiLuBtnInfo()
	local index_one, index_two, index_three = 1, 2, 3
	for i = 1, cur_day do
		if daily_cfg[i] then
			local limit1 = daily_cfg[i].limit_time_list[index_one]
			local limit2 = daily_cfg[i].limit_time_list[index_two]
			local limit3 = daily_cfg[i].limit_time_list[index_three]
			local buy_count1 = XiuZhenRoadWGData.Instance:GetDayGiftInfo(i,index_one)
			local buy_count2 = XiuZhenRoadWGData.Instance:GetDayGiftInfo(i,index_two)
			local buy_count3 = XiuZhenRoadWGData.Instance:GetDayGiftInfo(i,index_three)
			if buy_count1.buy_count >= tonumber(limit1) and buy_count2.buy_count >= tonumber(limit2) and buy_count3.buy_count >= tonumber(limit3) and i == cur_day then
				return 0
			end
		end
	end

	return 1
end

function XiuZhenRoadWGData:GetCueViewAllTaskInfo(open_day)
	local daily_cfg = self.xiuzhen_cfg.daily_cfg
	local max_task = daily_cfg[open_day].task_max_num
	local cur_need_info = {}
	for i=1,max_task do
		local task_info = {}
		local task_states = 1 --默认未达成
		local old_info = self.task_cfg[open_day][i]
		local cur_task_info = self.task_list[old_info.seq]
		if cur_task_info then
			if cur_task_info.task_progress >= old_info.param_1 and 1 == cur_task_info.fetch_reward_flag then
				task_states = XIUZHENZHILU_TASK_STATES.YLQ
			elseif cur_task_info.task_progress >= old_info.param_1 and 0 == cur_task_info.fetch_reward_flag then
				task_states = XIUZHENZHILU_TASK_STATES.KLQ
			else
				task_states = XIUZHENZHILU_TASK_STATES.WDC
			end
		end
		task_info["old_info"] = old_info
		task_info["task_states"] = task_states
		task_info["task_progress"] = cur_task_info.task_progress
		task_info["task_seq"] = old_info.task_seq
		table.insert(cur_need_info,task_info)
	end
	table.sort(cur_need_info,SortTools.KeyLowerSorter("task_states","task_seq"))
	return cur_need_info
end

function XiuZhenRoadWGData:GetCurNeedInfoProgress(open_day)
	local progress = 0
	local complse_num = 0
	local daily_cfg = self.xiuzhen_cfg.daily_cfg
	local max_task = daily_cfg[open_day].task_max_num
	local cur_need_info = {}
	for i=1,max_task do
		local old_info = self.task_cfg[open_day][i]
		local cur_task_info = old_info and self.task_list[old_info.seq]
		if cur_task_info then
			if cur_task_info.task_progress >= old_info.param_1 and 1 == cur_task_info.fetch_reward_flag then
				complse_num = complse_num + 1
			end
		end
	end
	local max_num = max_task
	max_num = max_num > 0 and max_num or 1
	return complse_num,max_num
end

--全部信息
function XiuZhenRoadWGData:SetXiuZhenZhiLuAllInfo(protocol)
	self.cur_total_progress = protocol.cur_total_progress --当前总进度（最终奖励进度）
	self.progress_reward_flag = bit:d2b(protocol.progress_reward_flag) --累积礼包的奖励表示（4位储存）
	self.gift_pack_buy_flag = protocol.gift_pack_buy_flag --礼包购买标记(7位)
	self.start_timestap = protocol.start_timestap --活动开始时间戳
	self.cur_compose_count = protocol.cur_compose_count --当前合成碎片数量
	self.final_reward_fech_flag = protocol.final_reward_fech_flag --最终奖励领取标记
	self.dayinfo = protocol.dayinfo
	self.task_list = protocol.task_list

	self.login_timestamp = protocol.login_timestamp

	self:AddItemChanegeEvent()
end

--单个信息
function XiuZhenRoadWGData:SetXiuZhenZhiLuInfo(protocol)
	--当天属性值综合变化
	if self.dayinfo and protocol.task_day > 0 and self.dayinfo[protocol.task_day] then
		for k=1,XIUZHENZHILU_OTHER.MAX_NATURE_NUM do
			self.dayinfo[protocol.task_day].active_attr_value[k] = protocol.active_attr_value[k]
		end
	end
	--单个任务信息变化
	if self.task_list and self.task_list[protocol.seq] then
		self.task_list[protocol.seq].task_progress = protocol.task_progress
		self.task_list[protocol.seq].fetch_reward_flag = protocol.fetch_reward_flag
	end
	--合成碎片数量改变
	self.cur_compose_count = protocol.cur_compose_count
	--任务总进度变化（累积进度）
	self.cur_total_progress = protocol.cur_total_progress
end
--礼包信息
function XiuZhenRoadWGData:SetXiuZhenZhiLuGiftInfo(protocol)
	--购买次数变化
	if self.dayinfo and protocol.task_day > 0 and self.dayinfo[protocol.task_day] then
		self.dayinfo[protocol.task_day].gift_pack_buy_time = protocol.gift_pack_buy_time
	end

	--合成碎片数量改变
	self.cur_compose_count = protocol.cur_compose_count
	 --最终奖励领取标记
	self.final_reward_fech_flag = protocol.final_reward_fech_flag
	--累积礼包标记变化
	self.progress_reward_flag = bit:d2b(protocol.progress_reward_flag)
end

function XiuZhenRoadWGData:SetSCXiuZhenZhiLuGiftInfo(protocol)
	self.gift_info_list = protocol.gift_info_list
	self.cur_total_progress = protocol.cur_total_progress
end

function XiuZhenRoadWGData:GetDayGiftInfo(open_day,tab_type)
	return self.gift_info_list[open_day] and self.gift_info_list[open_day][tab_type]
end

--获取活动开启的第某天
function XiuZhenRoadWGData:GetActivityOpenDay()
	-- self.start_timestap = TimeWGCtrl.Instance:GetServerTime() -86400*2
	if nil == self.start_timestap then
		return 0
	end
	local time =  TimeWGCtrl.Instance:GetServerTime() - self.start_timestap
	return math.ceil(time/86400)
end

--获取模型展示标签
function XiuZhenRoadWGData:GetModelShowTabIndex()
	local cur_day = self:GetActivityOpenDay()
	local model_index = 1
	if cur_day > 6 then
		model_index = 3
	elseif cur_day > 4 then
		model_index = 2
	end

	return model_index
end

--获取修真之路的页签信息
function XiuZhenRoadWGData:GetXiuZhenZhiLuBtnInfo()
	--[[local btn_data = self.xiuzhen_cfg.daily_cfg
	local temp_list = {}
	local key = #btn_data + 1
	for i=1, #btn_data do
		temp_list[i] = btn_data[key - i]
	end--]]
	if IsEmptyTable(self.xiuzhen_btn_cfg) then
		self.xiuzhen_btn_cfg = {}
		for k,v in pairs(self.xiuzhen_cfg.daily_cfg) do
			self.xiuzhen_btn_cfg[v.open_day] = {}
			self.xiuzhen_btn_cfg[v.open_day].cfg = v
			self.xiuzhen_btn_cfg[v.open_day].top_names_list = Split(v.top_names, "|")
			self.xiuzhen_btn_cfg[v.open_day].init_price_list = Split(v.init_price, "|")
			self.xiuzhen_btn_cfg[v.open_day].price_list = Split(v.price, "|")
			self.xiuzhen_btn_cfg[v.open_day].limit_time_list = Split(v.limit_time, "|")
			self.xiuzhen_btn_cfg[v.open_day].gift_star_list = Split(v.gift_star, "|")
			self.xiuzhen_btn_cfg[v.open_day].glod_type_list = Split(v.glod_type, "|")
			self.xiuzhen_btn_cfg[v.open_day].new_show_item_list = Split(v.new_show_item, "|")
		end
	end
	return self.xiuzhen_btn_cfg
end

--获取修真之路的累积物品信息
function XiuZhenRoadWGData:GetXiuZhenZhiLuLeiJiInfo()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	return pro_reward
end

--获取修真之路的累积物品信息(6个为一组)
function XiuZhenRoadWGData:GetXiuZhenZhiLuFourLeiJiInfo()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()
	local data = {}
	if nil == pro_reward and nil == cur_total_progress then
		return {}
	end
	-- 4个阶段
	local max_num = #pro_reward
	local for_num = math.ceil(max_num / 6)
	local index = 0
	local del = 0
	for i=1,for_num do
		index = i * 7
		if index > max_num then
			del = 7
			index = max_num
			break
		elseif pro_reward[index] and pro_reward[index].condition <= cur_total_progress then
			del = 6
			if self:GetCurDataIsCanLingQu(index, del) then
				break
			end
		elseif pro_reward[index] and pro_reward[index].condition > cur_total_progress then
			del = 6
			break
		end
	end
	local is_can_lingqu = false
	--从小到大排序生成
	for i = index - del, index do
		if pro_reward[i] then
			table.insert(data,pro_reward[i])
			if not is_can_lingqu then
				local flag = self:GetXiuZhenZhiLuLeiJiRewardFlag(pro_reward[i].progress)
				is_can_lingqu = (0 == flag and pro_reward[i].condition <= cur_total_progress)
			end
		end
	end

	--从大到小排序生成
	-- for i = index , index - del , -1 do
	-- 	if pro_reward[i] and i < max_num then
	-- 		table.insert(data,pro_reward[i])
	-- 		if not is_can_lingqu then
	-- 			local flag = self:GetXiuZhenZhiLuLeiJiRewardFlag(pro_reward[i].progress)
	-- 			is_can_lingqu = (0 == flag and pro_reward[i].condition <= cur_total_progress)
	-- 		end
	-- 	end
	-- end
	return data, is_can_lingqu
end

--获取修真之路未到最大阶段奖励的进度
function XiuZhenRoadWGData:GetXiuZhenZhiLuNoMaxRewardProgress()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()

	if nil == pro_reward and nil == cur_total_progress then
		return {}
	end

	local max_num = #pro_reward
	local for_num = math.ceil(max_num / 4)
	local reward_progress = false
	local index = (for_num - 1) * 4
	if pro_reward[index] and pro_reward[index].condition <= cur_total_progress then
		reward_progress =  true
	else
		reward_progress = false
	end

	-- for i = 1,for_num do
	-- 	index = i * 6
	-- 	if pro_reward[index] and pro_reward[index].condition <= cur_total_progress then
	-- 		reward_progress =  true
	-- 	else
	-- 		reward_progress = false
	-- 	end
	-- end
	return reward_progress
end

--大圣归来进度条
function XiuZhenRoadWGData:GetCurProgress(leiji_data, intimacy_value)  --亲密度进度条
	local cur_progress = 0
	local cur_intimacy_value = tonumber(intimacy_value)
	if next(leiji_data) == nil or cur_intimacy_value == nil then
		return cur_progress
	end
	local max_progress = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuNoMaxRewardProgress()
	local slider_piecewise = {0.12, 0.24, 0.37, 0.5, 0.63, 0.75, 0.88, 1}	--对应的进度条值

	for k, v in pairs(slider_piecewise) do
		local seq = k - 1
		local length = #slider_piecewise
		local cur_need = leiji_data[seq] and leiji_data[seq].condition or 0
		local next_need = leiji_data[seq + 1] and leiji_data[seq + 1].condition or leiji_data[#leiji_data].condition
		local cur_value = slider_piecewise[seq] and slider_piecewise[seq] or 0
		local next_value = slider_piecewise[seq + 1] and slider_piecewise[seq + 1] or slider_piecewise[length]
		if max_progress then --如果是最后一轮奖励 next_nedd 直接拿奖励最大值   leiji_data的数据长度只有6  用来判断前几轮用的
			local max_total_progress = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuMaxCondition()
			cur_need = leiji_data[#leiji_data].condition
			next_need = max_total_progress
			cur_value = slider_piecewise[#slider_piecewise - 1]
			next_value = slider_piecewise[#slider_piecewise]
		end
		if cur_intimacy_value > cur_need and cur_intimacy_value <= next_need then
			cur_progress = (cur_intimacy_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif cur_intimacy_value > leiji_data[#leiji_data].condition then
			cur_progress = slider_piecewise[length]
			break
		end
	end
	return cur_progress
end

--上面进度条
function XiuZhenRoadWGData:GetpProgressTopInfo()
	local max_progress = 1
	local cur_progress = 0
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	if not IsEmptyTable(pro_reward) then
		max_progress = pro_reward[#pro_reward].condition - pro_reward[#pro_reward - 1].condition
		if cur_total_progress > pro_reward[#pro_reward - 1].condition then
			cur_progress = cur_total_progress - pro_reward[#pro_reward - 1].condition
		end
	end
	return max_progress, cur_progress
end

--中间进度条
function XiuZhenRoadWGData:GetNewProgressInfo()
	local max_progress = 1
	local cur_progress = 1
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	if not IsEmptyTable(pro_reward) then
		for i, v in ipairs(pro_reward) do
			if i > 2 and cur_total_progress < v.condition then
				max_progress = pro_reward[i].condition - pro_reward[i - 1].condition
				cur_progress = cur_total_progress - pro_reward[i - 1].condition
				if cur_total_progress >= pro_reward[#pro_reward - 1].condition then
					max_progress = 1
					cur_progress = 1
				end
				break
			end
		end
	end
	return max_progress, cur_progress
end

--最下面一段进度条
function XiuZhenRoadWGData:GetLowProgressInfo()
	local max_progress = 1
	local cur_progress = 0
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	if not IsEmptyTable(pro_reward) then
		max_progress = pro_reward[2].condition
		cur_progress = cur_total_progress
		if cur_total_progress >= pro_reward[2].condition then
			cur_progress = pro_reward[2].condition
		end
	end
	return max_progress, cur_progress
end

function XiuZhenRoadWGData:GetXiuZhenZhiLuFourLeiJiInfo1()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()--已获得星数
	if nil == pro_reward and nil == cur_total_progress then
		return {}
	end
	local data = {}

	if cur_total_progress < pro_reward[3].condition then
		for i = 3, 1, -1 do
			table.insert(data, pro_reward[i])
		end
	else
		if cur_total_progress > pro_reward[#pro_reward - 2].condition then
			cur_total_progress = pro_reward[#pro_reward - 2].condition
		end

		for i = #pro_reward, 1, -1 do
			if cur_total_progress >= pro_reward[i].condition and #data < 3 then
				table.insert(data, pro_reward[i + 1])
			end
		end
	end

	--累计奖励判断
	local is_can_lingqu = false
	for k,v in pairs(pro_reward) do
		if cur_total_progress >= v.condition then
			local flag = self:GetXiuZhenZhiLuLeiJiRewardFlag(v.progress)
			if flag == 0 then
				is_can_lingqu = true
				break
			end
		end
	end
	return data, is_can_lingqu
end

function XiuZhenRoadWGData:GetRedFlag(progress)
	local pro_reward = self:GetXiuZhenZhiLuLeiJiInfo()
	for i = progress+1, 1, -1 do
		local flag = self:GetXiuZhenZhiLuLeiJiRewardFlag(pro_reward[i].progress)
		if 0 == flag then
			return 0
		end
	end
end

--判断当前的数据中是否存在没有领取的
function XiuZhenRoadWGData:GetCurDataIsCanLingQu(index, del)
	local pro_reward = self.xiuzhen_cfg.pro_reward
	if nil == pro_reward then
		return false
	end

	for i = index - del, index do
		if pro_reward[i] then
			local flag = self:GetXiuZhenZhiLuLeiJiRewardFlag(pro_reward[i].progress)
			if 0 == flag then
				return true
			end
		end
	end

	return false
end

--获取修真之路的阶段进度信息(默认1因为0不能被除)
function XiuZhenRoadWGData:GetpProgressInfo()
	local leiji_data = self:GetXiuZhenZhiLuFourLeiJiInfo()
	local pro_reward = self.xiuzhen_cfg.pro_reward
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()
	local max_progress = 1
	local cur_progress = 1
	local condition = 0
	if nil == leiji_data and nil == pro_reward or nil == cur_total_progress then
		return 1,1
	end
	if leiji_data[1] then
		local progress = leiji_data[1].progress - 1
		for k,v in pairs(pro_reward) do
			if v.progress == progress then
				condition = v.condition
				break
			end
		end
		max_progress = leiji_data[#leiji_data].condition - condition
	end
	cur_progress = cur_total_progress - condition
	return max_progress,cur_progress
end

--获取修真之路累积奖励的最大条件
function XiuZhenRoadWGData:GetXiuZhenZhiLuMaxCondition()
	local index = #self.xiuzhen_cfg.pro_reward
	return self.xiuzhen_cfg.pro_reward[index].condition
end
--获取修真之路的当天信息加成属性和礼包购买等
function XiuZhenRoadWGData:GetXiuZhenZhiLuCurDayInfo(open_day)
	--给一个假数据防止报错
	local jia_info = {active_attr_value ={[1] = 0,[2] = 0} , gift_pack_buy_time = 0}
	if nil == self.dayinfo then
		return jia_info
	end
	if nil == self.dayinfo[open_day] then
		return jia_info
	end
	return self.dayinfo[open_day]
end

--获取修真之路的累积奖励总进度
function XiuZhenRoadWGData:GetXiuZhenZhiLuLeiJiRewardProgress()
	return self.cur_total_progress or 0
end

--测试用
function XiuZhenRoadWGData:SetCurTotalProgress(num)

	self.cur_total_progress = num
end

--获取修真之路的累积的碎片数量
function XiuZhenRoadWGData:GetXiuZhenZhiLuLeiJiComposeNum()
	local item_id = self:GetRewardItemInfo()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	return item_num or 0
end

--现在走合成协议所以这边监听不了这个字段策划说不要这个标识了，但防止出现意外默认为0
--获取修真之路最终奖励（宠物）领取标识
function XiuZhenRoadWGData:GetXiuZhenZhiLuPetFlag()
	-- return self.final_reward_fech_flag or 0
	return 0
end

--获取修真之路的其他信息
function XiuZhenRoadWGData:GetXiuZhenZhiLuOtherCfg()
	return self.xiuzhen_cfg.other[1]
end

--获取修真之路的累积物品领取信息
function XiuZhenRoadWGData:GetXiuZhenZhiLuLeiJiRewardFlag(progress)
	if self.progress_reward_flag and self.progress_reward_flag[32 - progress] then
		return self.progress_reward_flag[32 - progress]
	end
	return 0
end
--获取当前页签是否需要展示红点
function XiuZhenRoadWGData:GetSelectBtnRedShow(open_day)
	local cur_day = self:GetActivityOpenDay()
	if open_day > cur_day then
		return false
	end
	local daily_cfg = self.xiuzhen_cfg.daily_cfg
	local max_task = daily_cfg[open_day].task_max_num

	local all_nature_value = XiuZhenRoadWGData.Instance:GetCurAllNatureSum(open_day)
	local day_info = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuCurDayInfo(open_day)
	local complse_num,max_num = XiuZhenRoadWGData.Instance:GetCurNeedInfoProgress(open_day)
	local progress = complse_num / max_num
	progress = string.format("%.2f", progress)
	progress = tonumber(progress)

	local active_attr_value = day_info.active_attr_value
	if progress >= 1 and day_info.is_active_attr == 0 then
		return true
	end

	for i=1,max_task do
		local old_info = self.task_cfg[open_day][i]
		if old_info then
			local cur_task_info = self.task_list[old_info.seq]
			if cur_task_info then
				if cur_task_info.task_progress >= old_info.param_1 and 0 == cur_task_info.fetch_reward_flag then
					return true
				end
			end
		end
	end
	return false
end

function XiuZhenRoadWGData:GetSelectTabTypeBtnRedShow(open_day,tab_type)
	local cfg_list = self.task_tab_type_cfg[open_day] and self.task_tab_type_cfg[open_day][tab_type]
	if IsEmptyTable(cfg_list) then return false end
	local cur_day = self:GetActivityOpenDay()
	if open_day > cur_day then
		return false
	end
	local daily_cfg = self.xiuzhen_cfg.daily_cfg

	local all_nature_value = XiuZhenRoadWGData.Instance:GetCurAllNatureSum(open_day)
	local day_info = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuCurDayInfo(open_day)
	local active_attr_value = day_info.active_attr_value
	local progress = active_attr_value[1]/all_nature_value
	if progress >= 1 and day_info.is_active_attr == 0 then
		return true
	end
	for k,v in pairs(cfg_list) do
		local old_info = self.task_cfg[open_day][v.task_seq]
		if old_info then
			local cur_task_info = self.task_list[old_info.seq]
			if cur_task_info then
				if cur_task_info.task_progress >= old_info.param_1 and 0 == cur_task_info.fetch_reward_flag then
					return true
				end
			end
		end
	end
	return false
end


--获取当前页签是否有未完成的任务
function XiuZhenRoadWGData:GetSelectBtnNoFinish(open_day)
	local cur_day = self:GetActivityOpenDay()
	if open_day > cur_day then
		return false
	end
	local daily_cfg = self.xiuzhen_cfg.daily_cfg
	local max_task = daily_cfg[open_day].task_max_num
	for i=1,max_task do
		local old_info = self.task_cfg[open_day][i]
		if old_info then
			local cur_task_info = self.task_list[old_info.seq]
			if cur_task_info then
				if cur_task_info.task_progress < old_info.param_1 and 0 == cur_task_info.fetch_reward_flag then
					return true
				end
			end
		end
	end
	return false
end

--获取当前选中天数完成任务应加属性之和
function XiuZhenRoadWGData:GetCurAllNatureSum(open_day)
	local daily_cfg = self.xiuzhen_cfg.daily_cfg
	local max_task = daily_cfg[open_day].task_max_num
	local value = 0
	for i=1,max_task do
		local old_info = self.task_cfg[open_day][i]
		if old_info and old_info.attr_value1 then
			value = value + old_info.attr_value1
		end
	end
	value = value > 0 and value or 1
	return value
end

--活动剩余时间获取
function XiuZhenRoadWGData:GetActivityEndTime()
	local other_cfg = self.xiuzhen_cfg.other[1]
	local start_time = self.start_timestap or 0
	local end_time = start_time + other_cfg.open_day*86400
	if start_time == 0 then
		end_time = 0
	end
	return end_time
end

--当前天数任务是否全部完成
function XiuZhenRoadWGData:GetCurDayTaskIsFinish(open_day)
	local daily_cfg = self.xiuzhen_cfg.daily_cfg
	local max_task = (daily_cfg[open_day] or {}).task_max_num or 0
	for i=1,max_task do
		local old_info = self.task_cfg[open_day][i]
		local cur_task_info = self.task_list[old_info.seq]
		if cur_task_info then
			if 0 == cur_task_info.fetch_reward_flag then
				return false
			end
		end
	end
	-- local dayinfo = self:GetXiuZhenZhiLuCurDayInfo(open_day)
	-- if dayinfo.gift_pack_buy_time < daily_cfg[open_day].limit_time then
	-- 	return false
	-- end
	return true
end

--获取当前已开启功能中有红点的部分
function XiuZhenRoadWGData:GetCurOpenCanSelect()
	local index = 1
	local cur_open_day = self:GetActivityOpenDay()
	cur_open_day = cur_open_day > XIUZHENZHILU_OTHER.MAX_OPEN_DAY and XIUZHENZHILU_OTHER.MAX_OPEN_DAY or cur_open_day
	if cur_open_day <=0 then
		return index
	end
	--是否有红点
	for i=1,cur_open_day do
		local is_red = self:GetSelectBtnRedShow(i)
		if is_red then
			return i
		end
	end
	--是否未完成
	for i=1,cur_open_day do
		local is_red = self:GetSelectBtnNoFinish(i)
		if is_red then
			return i
		end
	end
	return index
end


--红点
function XiuZhenRoadWGData:IsShowXiuZhenRoadRedPoint()
	local function remove_tip_func()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XiuZhenRoad, 0)
	end

	local function invate_tip_func()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XiuZhenRoad, 1, function ()
			ViewManager.Instance:Open(GuideModuleName.XiuZhenRoadView)
		end)
	end

	if not self:XiuZhenRoadIsOpen() then
		remove_tip_func()
		return 0
	end
	local cur_open_day = self:GetActivityOpenDay()
	cur_open_day = cur_open_day > XIUZHENZHILU_OTHER.MAX_OPEN_DAY and XIUZHENZHILU_OTHER.MAX_OPEN_DAY or cur_open_day
	if cur_open_day <= 0 then
		remove_tip_func()
		return 0
	end
	for i=1,cur_open_day do
		if self:GetSelectBtnRedShow(i) then
			invate_tip_func()
			return 1
		end
	end
	--累计奖励判断
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()
	local pro_reward = self:GetXiuZhenZhiLuLeiJiInfo()
	for k,v in pairs(pro_reward) do
		if cur_total_progress >= v.condition then
			local flag = self:GetXiuZhenZhiLuLeiJiRewardFlag(v.progress)
			if 0 == flag then
				invate_tip_func()
				return 1
			end
		end
	end

	local buy_red_point = self:GetRMBBuyRed()
	if self:GetFirstLoginRed() and buy_red_point == 1 then
		invate_tip_func()
		return 1
	end

	--最终奖励参数
	-- local item_id,consume_num = self:GetRewardItemInfo()
	-- local composenum = self:GetXiuZhenZhiLuLeiJiComposeNum()
	-- local final_reward_fech_flag = self:GetXiuZhenZhiLuPetFlag()
	-- if composenum >= consume_num and 0 == final_reward_fech_flag then
	-- 	return 1
	-- end

	remove_tip_func()
	return 0
end

--红点
function XiuZhenRoadWGData:IsShowXiuZhenRoadSkillRedPoint()
	local cur_open_day = self:GetActivityOpenDay()
	cur_open_day = cur_open_day > XIUZHENZHILU_OTHER.MAX_OPEN_DAY and XIUZHENZHILU_OTHER.MAX_OPEN_DAY or cur_open_day
	if cur_open_day <= 0 then
		return 0
	end
	for i=1,cur_open_day do
		if self:GetSelectBtnRedShow(i) then
			return 1
		end
	end
	return 0
end

function XiuZhenRoadWGData:GetFirstLoginRed()
	return self.first_login_red
end

function XiuZhenRoadWGData:SetFirstLoginRed(flag)
	self.first_login_red = flag
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	RemindManager.Instance:Fire(RemindName.XiuZhenRoad)
end

function XiuZhenRoadWGData:ShowXiuZhenRoadRedInfo()
	local cur_open_day = self:GetActivityOpenDay()
	cur_open_day = cur_open_day > XIUZHENZHILU_OTHER.MAX_OPEN_DAY and XIUZHENZHILU_OTHER.MAX_OPEN_DAY or cur_open_day
	if cur_open_day > 0 then
		for i=1,cur_open_day do
			if self:GetSelectBtnRedShow(i) then
				print_error("大圣归来红点信息", "天数", i)
			end
		end
	end

	--累计奖励判断
	local cur_total_progress = self:GetXiuZhenZhiLuLeiJiRewardProgress()
	local pro_reward = self:GetXiuZhenZhiLuLeiJiInfo()
	for k,v in pairs(pro_reward) do
		if cur_total_progress >= v.condition then
			local flag = self:GetXiuZhenZhiLuLeiJiRewardFlag(v.progress)
			if 0 == flag then
				print_error("大圣归来红点信息", "累计奖励判断",cur_total_progress)
				print_error(pro_reward)
				print_error(self.progress_reward_flag)
			end
		end
	end
	--最终奖励参数
	local item_id,consume_num = self:GetRewardItemInfo()
	local composenum = self:GetXiuZhenZhiLuLeiJiComposeNum()
	local final_reward_fech_flag = self:GetXiuZhenZhiLuPetFlag()
	if composenum >= consume_num and 0 == final_reward_fech_flag then
		print_error("大圣归来红点信息", "最终奖励参数",final_reward_fech_flag,consume_num)
	end
end

function XiuZhenRoadWGData:XiuZhenRoadIsOpen()
	local other_cfg = self.xiuzhen_cfg.other[1]
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local end_time = self:GetActivityEndTime()
    local fun_is_open = FunOpen.Instance:GetFunIsOpened("XiuZhenRoadView")
    local is_red = self:IsShowXiuZhenRoadSkillRedPoint()
    local flag_show = true
    if is_red ~= 1 then
    	flag_show = TimeWGCtrl.Instance:GetServerTime() < end_time
    end
	if role_level >= other_cfg.open_level  and fun_is_open  and flag_show then
		return true
	end
	return false
end

--添加一个背包物品变化的通知
function XiuZhenRoadWGData:AddItemChanegeEvent()
	local final_reward_fech_flag = self:GetXiuZhenZhiLuPetFlag()
	if not self:XiuZhenRoadIsOpen() or 1 == final_reward_fech_flag then
		return
	end

	if nil == self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end
end

function XiuZhenRoadWGData:ItemDataChangeCallback(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local item_id = self:GetRewardItemInfo()
	if item_id == change_item_id then
		XiuZhenRoadWGCtrl.Instance:Flush()
		RemindManager.Instance:Fire(RemindName.XiuZhenRoad)
	end
end

function XiuZhenRoadWGData:GetRewardItemInfo()
    local consume_id = self.xiuzhen_cfg.other[1].consume_id or 0
    local cfg = ComposeWGData.Instance:GetComposeCfgByItemId(consume_id)
	local consume_num = 0
	local product_id = 0 --被合成对象
	local producd_seq = 0
	if cfg then
		consume_id = cfg.stuff_id_1
		consume_num = cfg.stuff_count_1
		product_id = cfg.product_id
		producd_seq = cfg.producd_seq
	end
	return consume_id, consume_num, product_id, producd_seq
end

function XiuZhenRoadWGData:GetXiuZhenSkillIsActive(open_day)
	local day_info = self:GetXiuZhenZhiLuCurDayInfo(open_day)
	local flag = day_info.is_active_attr or 0
	return flag == 1
end

function XiuZhenRoadWGData:GetSkillAttrCap(data)
	local cap = 0
	if IsEmptyTable(data) then
		return cap
	end

	local attr_list = {}
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end

		if attr_list[attr_str] then
			attr_list[attr_str] = attr_list[attr_str] + value
		else
			attr_list[attr_str] = value
		end
	end

	for i = 1, 2 do
		local attr_value = data["param" .. i] or 0
		local attr_str = data["attr_type" .. i] or ""
		if attr_value > 0 and attr_str ~= "" then
			attr_str = AttributeMgr.GetAttributteKey(attr_str)
			if data.open_day == 3 then
				local role_level = RoleWGData.Instance:GetRoleLevel()
				add_tab(attr_str, attr_value * role_level)
			else
				add_tab(attr_str, attr_value)
			end
		end
	end

	local attribute = AttributeMgr.GetAttributteByClass(attr_list)
	cap = AttributeMgr.GetCapability(attribute)

	return cap
end

function XiuZhenRoadWGData:SetCanGiftPanelLoop(flag)
	self.can_gift_panel_loop = flag
end

function XiuZhenRoadWGData:GetCanGiftPanelLoop()
	return self.can_gift_panel_loop
end

function XiuZhenRoadWGData:GetPrintLog()
	local tab_str = "XiuZhenRoadWGData:GetPrintLog\n"
	local other_cfg = self.xiuzhen_cfg.other[1]
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local end_time = self:GetActivityEndTime()
    local fun_is_open = FunOpen.Instance:GetFunIsOpened("XiuZhenRoadView")
    tab_str = tab_str .. "活动结束时间戳:"..tostring(end_time).."\n"
    tab_str = tab_str .. "活动开启时间戳:"..tostring(self.start_timestap or 0).."\n"
    tab_str = tab_str .. "大圣归来是否开启:"..tostring(TimeWGCtrl.Instance:GetServerTime() < end_time).."\n"
    tab_str = tab_str .. "大圣归来是否开启等级:"..tostring(role_level) .. "  other_cfg.open_level:" .. other_cfg.open_level .."\n"
    tab_str = tab_str .. "fun_is_open:"..tostring(fun_is_open) .. "\n"
    local act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.XIUZHEN_ROAD)
    tab_str = tab_str .. "act_btn:"..tostring(act_btn ~= nil) .. "\n"
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.XIUZHEN_ROAD)
    if not IsEmptyTable(act_info) then
    	for k,v in pairs(act_info) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
    end
	return tab_str
end

function XiuZhenRoadWGData:GetShowMainUIInfo()
	local xiuzhen_info = self:GetXiuZhenZhiLuBtnInfo()
	local cur_day = self:GetActivityOpenDay()
	local open_day = cur_day > XIUZHENZHILU_OTHER.MAX_OPEN_DAY and XIUZHENZHILU_OTHER.MAX_OPEN_DAY or cur_day
	for i = 1, open_day do
		if not self:GetCurDayTaskIsFinish(i) or i == open_day then
			local complse_num, max_num = self:GetCurNeedInfoProgress(i)
			return xiuzhen_info[i].cfg.button_name, complse_num, max_num
		end
	end

	return "", 0, 0
end

function XiuZhenRoadWGData:GetMiJiShowItemInfo()
	local miji_seq = self.xiuzhen_cfg.other[1].miji_seq
	return CultivationWGData.Instance:GetEsotericaCfg(miji_seq)
end

function XiuZhenRoadWGData:GetMainViewXXYMACTTipData()
	local cur_day = self:GetActivityOpenDay()
	local open_day = cur_day > XIUZHENZHILU_OTHER.MAX_OPEN_DAY and XIUZHENZHILU_OTHER.MAX_OPEN_DAY or cur_day
	local daily_cfg = self.xiuzhen_cfg.daily_cfg

	local complse_num, max_num = self:GetCurNeedInfoProgress(open_day)

	local default_data

	for i = 1, open_day do
		local max_task = (daily_cfg[i] or {}).task_max_num or 0

		for j = 1, max_task do
			local old_info = self.task_cfg[i][j]

			if old_info then
				local cur_task_info = self.task_list[old_info.seq]

				if cur_task_info then
					if cur_task_info.task_progress < old_info.param_1 and 0 == cur_task_info.fetch_reward_flag then
						return old_info, complse_num, max_num
					end
				end

				default_data = old_info
			end
		end
	end

	return default_data, complse_num, max_num
end

function XiuZhenRoadWGData:GetXiuZhenRoadLoginTimeStamp()
	return self.login_timestamp
end

function XiuZhenRoadWGData:SetTaskTimerIdx(timer_idx)
	self.save_timer_idx = timer_idx or 1
end

function XiuZhenRoadWGData:GetTaskTimerIdx()
	return self.save_timer_idx
end